import com.alibaba.fastjson.JSONObject;
import java.util.ArrayList;
import java.util.List;

public class TestConversion {
    public static void main(String[] args) {
        // 测试字符串列表转对象
        List<String> inputList = new ArrayList<>();
        inputList.add("苹果");
        inputList.add("香蕉");
        
        System.out.println("输入列表: " + inputList);
        
        List<JSONObject> resultList = new ArrayList<>();
        
        for (int i = 0; i < inputList.size(); i++) {
            String item = inputList.get(i);
            System.out.println("处理第 " + i + " 项: '" + item + "'");
            
            if (item != null && !item.trim().isEmpty()) {
                JSONObject obj = new JSONObject();
                String trimmedItem = item.trim();
                obj.put("output", trimmedItem);
                resultList.add(obj);
                System.out.println("创建对象: output='" + trimmedItem + "', 对象内容: " + obj.toJSONString());
            }
        }
        
        // 构建结果
        JSONObject result = new JSONObject();
        result.put("objects", resultList);
        result.put("count", resultList.size());
        
        System.out.println("最终结果: " + result.toJSONString());
    }
}
