/// <reference types="node" />
import TOSBase from '../base';
import { Acl, DataTransferStatus } from '../../interface';
import { IRateLimiter } from '../../universal/rate-limiter';
import { StorageClassType } from '../../TosExportEnum';
export interface AppendObjectInput {
    bucket?: string;
    key: string;
    offset: number;
    body?: File | Blob | Buffer | NodeJS.ReadableStream;
    preHashCrc64ecma?: string;
    /**
     * unit: bit/s
     * server side traffic limit
     **/
    trafficLimit?: number;
    /**
     * only works for nodejs environment
     */
    rateLimiter?: IRateLimiter;
    contentLength?: number;
    cacheControl?: string;
    contentDisposition?: string;
    contentEncoding?: string;
    contentLanguage?: string;
    contentType?: string;
    expires?: Date;
    acl?: Acl;
    grantFullControl?: string;
    grantRead?: string;
    grantReadAcp?: string;
    grantWriteAcp?: string;
    meta?: Record<string, string>;
    websiteRedirectLocation?: string;
    storageClass?: StorageClassType;
    dataTransferStatusChange?: (status: DataTransferStatus) => void;
    /**
     * the simple progress feature
     * percent is [0, 1].
     *
     * since appendObject is stateless, so if `appendObject` fail and you retry it,
     * `percent` will start from 0 again rather than from the previous value.
     * if you need `percent` start from the previous value, you can use `uploadFile` instead.
     */
    progress?: (percent: number) => void;
    headers?: {
        [key: string]: string | undefined;
        'Cache-Control'?: string;
        'x-tos-acl'?: Acl;
        'x-tos-grant-full-control'?: string;
        'x-tos-grant-read'?: string;
        'x-tos-grant-read-acp'?: string;
        'x-tos-grant-write-acp'?: string;
        'x-tos-website-redirect-location'?: string;
        'x-tos-storage-class'?: string;
    };
}
export interface AppendObjectOutput {
    nextAppendOffset: number;
    hashCrc64ecma: string;
    'x-tos-version-id'?: string;
    'x-tos-hash-crc64ecma'?: string;
    'x-tos-next-append-offset'?: string;
}
export declare function appendObject(this: TOSBase, input: AppendObjectInput | string): Promise<import("../base").TosResponse<AppendObjectOutput>>;
export default appendObject;
