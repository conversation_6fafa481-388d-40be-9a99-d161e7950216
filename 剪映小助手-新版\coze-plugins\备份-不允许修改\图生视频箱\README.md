# 剪映小助手_图生视频箱

> 扣子平台插件 - 图生视频箱

## 📖 插件简介

剪映小助手_图生视频箱是专为视频创作者设计的AI视频生成工具集合，集成豆包图生视频插件的能力，提供从图片到视频的完整生成流程。

## 🛠️ 工具列表

本插件包含以下工具，每个工具都有对应的JSON定义文件：

### 核心工具
- **创建视频生成任务** - 调用豆包图生视频插件创建视频生成任务
- **查询视频生成任务** - 查询视频生成任务状态和结果

## 📁 文件结构

```
图生视频箱/
├── README.md                    # 插件说明文档
├── zj_create_video_task.json    # 创建视频生成任务工具定义
└── zj_query_video_task.json     # 查询视频生成任务工具定义
```

## 🔧 使用说明

1. 将JSON文件导入到扣子平台
2. 配置API Token（从 https://www.aigcview.com/usercenter 获取）
3. 在工作流中调用相应工具
4. 获取视频生成结果和下载链接

## 🔑 API配置

- **API Token**: 需要从 https://www.aigcview.com/usercenter 获取
- **Base URL**: https://www.coze.cn
- **插件ID**: 7504578949696061492
- **费用说明**: 插件比较贵1块一次，请谨慎使用

## 📝 开发规范

- 每个工具都有独立的JSON定义文件
- 遵循扣子平台的工具定义规范
- 输入输出参数清晰明确
- 提供详细的工具描述和使用说明
- 统一使用 `zj_` 前缀命名参数
- 操作ID使用标准命名（不带后缀）

## 🎯 功能特点

- **图生视频**: 支持从图片生成视频内容
- **异步处理**: 支持异步和同步两种处理模式
- **状态跟踪**: 实时查询任务进度和状态
- **错误处理**: 完善的错误处理和提示机制
- **标准化**: 遵循OpenAPI 3.0.0规范
- **多模型支持**: 支持多种豆包视频生成模型

## 🤖 支持的模型

- **doubao-seedance-1-0-lite-i2v-250428**: 轻量级图生视频模型（推荐）
- **doubao-seedance-1-0-pro-250528**: 专业级图生视频模型（高质量，不支持尾帧和720p分辨率）

## 📋 参数说明

### 创建视频生成任务参数
- **apiKey** (必填): API令牌，从 https://www.aigcview.com/usercenter 获取
- **prompt** (必填): 视频生成提示词
- **image_url** (必填): 首帧图片链接（图片宽度需大于300px）
- **model** (可选): 视频生成模型ID，默认为 doubao-seedance-1-0-lite-i2v-250428
- **resolution** (可选): 视频分辨率，可选值：480p、720p、1080p，默认480p
- **duration** (可选): 视频时长，可选值：5秒或10秒，默认5秒
- **asyn** (可选): 是否同步返回内容，默认false
- **camerafixed** (可选): 是否固定摄像头，默认false
- **end_image_url** (可选): 尾帧图片链接（pro模型不支持）

### 查询视频生成任务参数
- **task_id** (必填): 视频生成任务ID
- **wait_cnt** (可选): 等待次数，默认1
