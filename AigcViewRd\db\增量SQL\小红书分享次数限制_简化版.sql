-- =============================================
-- 小红书分享次数限制功能数据库脚本（简化版）
-- 创建时间：2025-06-27
-- 版本：V1.0
-- 说明：为aicg_user_api_usage表添加分享次数限制相关字段
-- =============================================

-- 添加分享次数相关字段（如果字段已存在会报错，可以忽略）
ALTER TABLE aicg_user_api_usage ADD COLUMN share_attempts INT DEFAULT 0 COMMENT '分享尝试次数';
ALTER TABLE aicg_user_api_usage ADD COLUMN max_share_attempts INT DEFAULT 3 COMMENT '最大分享尝试次数';
ALTER TABLE aicg_user_api_usage ADD COLUMN last_attempt_time DATETIME COMMENT '最后尝试时间';
ALTER TABLE aicg_user_api_usage ADD COLUMN share_status TINYINT(1) DEFAULT 0 COMMENT '分享状态：0-未分享，1-已分享';
ALTER TABLE aicg_user_api_usage ADD COLUMN share_time DATETIME COMMENT '分享时间';
ALTER TABLE aicg_user_api_usage ADD COLUMN share_platform VARCHAR(50) COMMENT '分享平台';
ALTER TABLE aicg_user_api_usage ADD COLUMN page_id VARCHAR(100) COMMENT '页面ID';

-- 创建相关索引
-- 🔥 创建唯一索引，确保同一页面同一插件只有一条记录
CREATE UNIQUE INDEX uk_page_id_plugin ON aicg_user_api_usage(page_id, plugin_key);
CREATE INDEX idx_share_status ON aicg_user_api_usage(share_status);
CREATE INDEX idx_last_attempt_time ON aicg_user_api_usage(last_attempt_time);

-- 查看表结构确认
DESCRIBE aicg_user_api_usage;
