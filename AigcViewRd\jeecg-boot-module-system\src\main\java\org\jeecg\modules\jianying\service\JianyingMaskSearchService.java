package org.jeecg.modules.jianying.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.constant.CacheConstant;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.PostConstruct;

/**
 * 剪映蒙版搜索服务
 * 完全按照稳定版动画查询的模式实现：使用@Cacheable注解，Redis缓存7天
 *
 * <AUTHOR>
 * @date 2025-01-23
 */
@Slf4j
@Service
public class JianyingMaskSearchService {

    private static final String MASK_API_URL = "https://lv-api-sinfonlinec.ulikecam.com/artist/v1/effect/get_resources_by_category_id";

    @Autowired
    private RestTemplate restTemplate;

    /**
     * 蒙版信息类
     */
    public static class MaskInfo {
        private String resourceId;
        private String effectId;
        private String name;
        private String resourceType;
        private String title;

        public MaskInfo() {}

        public MaskInfo(String resourceId, String effectId, String name, String resourceType, String title) {
            this.resourceId = resourceId;
            this.effectId = effectId;
            this.name = name;
            this.resourceType = resourceType;
            this.title = title;
        }

        // Getters and Setters
        public String getResourceId() { return resourceId; }
        public void setResourceId(String resourceId) { this.resourceId = resourceId; }
        public String getEffectId() { return effectId; }
        public void setEffectId(String effectId) { this.effectId = effectId; }
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
        public String getResourceType() { return resourceType; }
        public void setResourceType(String resourceType) { this.resourceType = resourceType; }
        public String getTitle() { return title; }
        public void setTitle(String title) { this.title = title; }
    }

    /**
     * 初始化服务
     */
    @PostConstruct
    public void init() {
        log.info("剪映蒙版搜索服务初始化完成");
    }

    /**
     * 根据蒙版名称查找蒙版信息
     * 使用@Cacheable注解，完全按照稳定版动画查询的模式：Redis缓存7天
     *
     * @param maskName 蒙版名称（如"圆形"、"矩形"等）
     * @return 蒙版信息，如果未找到返回null
     */
    @Cacheable(value = CacheConstant.JIANYING_MASKS_CACHE, key = "#maskName")
    public MaskInfo findMaskByName(String maskName) {
        if (maskName == null || maskName.trim().isEmpty()) {
            log.warn("蒙版名称为空，无法查找");
            return null;
        }

        String cleanName = maskName.trim();
        log.info("缓存未命中，调用剪映API获取蒙版数据: {}", cleanName);

        // 调用剪映官方API
        try {
            MaskInfo maskInfo = callMaskAPI(cleanName);
            if (maskInfo != null) {
                log.info("蒙版API调用成功: {} -> resource_id: {}, effect_id: {}",
                        cleanName, maskInfo.getResourceId(), maskInfo.getEffectId());
                return maskInfo;
            } else {
                log.warn("蒙版API未找到: {}", cleanName);
                return null;
            }
        } catch (Exception e) {
            log.error("蒙版API调用失败: {}", cleanName, e);
            return null;
        }
    }

    /**
     * 调用剪映官方蒙版API
     */
    private MaskInfo callMaskAPI(String maskName) {
        try {
            // 构建请求URL（完全按照头儿提供的参数）
            String url = MASK_API_URL +
                "?effect_sdk_version=16.4.0" +
                "&channel=jianyingpro_baidusem" +
                "&version_name=5.9.0" +
                "&device_platform=windows" +
                "&device_type=x86_64";

            // 构建请求体（完全按照头儿提供的参数）
            JSONObject requestBody = new JSONObject();
            requestBody.put("app_id", 3704);
            requestBody.put("category_id", -1);
            requestBody.put("category_key", "all");
            requestBody.put("count", 50);
            requestBody.put("panel", "videomask");
            requestBody.put("panel_source", "loki");

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            HttpEntity<String> entity = new HttpEntity<>(requestBody.toJSONString(), headers);

            // 发送请求
            ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.POST, entity, String.class);

            if (response.getStatusCode().is2xxSuccessful()) {
                String responseBody = response.getBody();
                return parseMaskResponse(responseBody, maskName);
            } else {
                log.error("蒙版API请求失败，状态码: {}", response.getStatusCode());
                return null;
            }

        } catch (Exception e) {
            log.error("调用蒙版API失败: {}", maskName, e);
            return null;
        }
    }

    /**
     * 解析蒙版API响应
     */
    private MaskInfo parseMaskResponse(String responseBody, String searchMaskName) {
        try {
            JSONObject response = JSONObject.parseObject(responseBody);

            if (response == null) {
                log.warn("蒙版API响应为空: {}", searchMaskName);
                return null;
            }

            String ret = response.getString("ret");
            if (!"0".equals(ret)) {
                log.error("蒙版API返回错误: ret={}, errmsg={}", ret, response.getString("errmsg"));
                return null;
            }

            JSONObject data = response.getJSONObject("data");
            if (data == null) {
                log.warn("蒙版API响应data为空: {}", searchMaskName);
                return null;
            }

            JSONArray effectItemList = data.getJSONArray("effect_item_list");
            if (effectItemList == null || effectItemList.isEmpty()) {
                log.warn("蒙版API响应effect_item_list为空: {}", searchMaskName);
                return null;
            }

            // 查找匹配的蒙版
            for (int i = 0; i < effectItemList.size(); i++) {
                JSONObject item = effectItemList.getJSONObject(i);
                JSONObject commonAttr = item.getJSONObject("common_attr");

                if (commonAttr != null) {
                    String title = commonAttr.getString("title");
                    if (searchMaskName.equals(title)) {
                        // 找到匹配的蒙版
                        String resourceId = commonAttr.getString("id");
                        String effectId = commonAttr.getString("effect_id");

                        // 解析resource_type
                        String resourceType = "circle"; // 默认值
                        String extra = commonAttr.getString("extra");
                        if (extra != null && !extra.isEmpty()) {
                            try {
                                JSONObject extraObj = JSONObject.parseObject(extra);
                                String type = extraObj.getString("resource_type");
                                if (type != null && !type.isEmpty()) {
                                    resourceType = type;
                                }
                            } catch (Exception e) {
                                log.warn("解析extra失败，使用默认resource_type: {}", extra, e);
                            }
                        }

                        return new MaskInfo(resourceId, effectId, searchMaskName, resourceType, title);
                    }
                }
            }

            log.warn("蒙版API响应中未找到匹配的蒙版: {}", searchMaskName);
            return null;

        } catch (Exception e) {
            log.error("解析蒙版API响应失败: {}", searchMaskName, e);
            return null;
        }
    }
}