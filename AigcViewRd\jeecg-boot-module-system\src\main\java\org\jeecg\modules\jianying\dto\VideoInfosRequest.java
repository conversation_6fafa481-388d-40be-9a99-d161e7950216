package org.jeecg.modules.jianying.dto;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 视频数据生成器请求参数
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class VideoInfosRequest extends BaseJianyingRequest {
    
    @ApiModelProperty(value = "视频列表（必填）", required = true,
                     example = "[\"https://example.com/video1.mp4\", \"https://example.com/video2.mp4\"]")
    @NotEmpty(message = "video_urls不能为空")
    @JsonProperty("video_urls")
    private List<String> zjVideoUrls;

    @ApiModelProperty(value = "时间节点，只接收结构：[{\"start\":0,\"end\":4612}]（必填）", required = true,
                     example = "[{\"start\": 0, \"end\": 4612000}]")
    @NotEmpty(message = "timelines不能为空")
    @JsonProperty("timelines")
    private List<JSONObject> zjTimelines;

    @ApiModelProperty(value = "视频蒙版，可填写值：圆形，矩形，爱心，星形", example = "圆形")
    @JsonProperty("mask")
    private String zjMask;

    @ApiModelProperty(value = "视频高度", example = "1080")
    @JsonProperty("height")
    private Integer zjHeight;

    @ApiModelProperty(value = "转场", example = "淡入淡出")
    @JsonProperty("transition")
    private String zjTransition;

    @ApiModelProperty(value = "转场的时长", example = "1000000")
    @JsonProperty("transition_duration")
    private Integer zjTransitionDuration;

    @ApiModelProperty(value = "音量大小，0-10，默认1", example = "1.0")
    @JsonProperty("volume")
    private Double zjVolume;

    @ApiModelProperty(value = "视频宽度", example = "1920")
    @JsonProperty("width")
    private Integer zjWidth;
    
    @Override
    public String getSummary() {
        return "VideoInfosRequest{" +
               "accessKey=" + (getAccessKey() != null && getAccessKey().length() > 10 ?
                              getAccessKey().substring(0, 10) + "***" : "null") +
               ", videoUrlsCount=" + (zjVideoUrls != null ? zjVideoUrls.size() : 0) +
               ", timelinesCount=" + (zjTimelines != null ? zjTimelines.size() : 0) +
               ", mask=" + zjMask +
               ", height=" + zjHeight +
               ", width=" + zjWidth +
               ", transition=" + zjTransition +
               ", transitionDuration=" + zjTransitionDuration +
               ", volume=" + zjVolume +
               "}";
    }
}
