package org.jeecg.modules.system.util;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.system.entity.SysRole;
import org.jeecg.modules.system.entity.SysUserRole;
import org.jeecg.modules.system.service.ISysRoleService;
import org.jeecg.modules.system.service.ISysUserRoleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Collections;
import java.util.stream.Collectors;

/**
 * @Description: 角色检查工具类
 * @Author: jeecg-boot
 * @Date: 2025-06-21
 * @Version: V1.0
 */
@Slf4j
@Component
public class RoleChecker {
    
    @Autowired
    private ISysUserRoleService sysUserRoleService;
    
    @Autowired
    private ISysRoleService sysRoleService;
    
    /**
     * 检查用户是否是admin角色（基于role_code）
     * @param userId 用户ID
     * @return true-是admin角色，false-不是admin角色
     */
    public boolean checkUserIsAdmin(String userId) {
        try {
            log.info("=== 开始检查用户admin角色 ===");
            log.info("用户ID：{}", userId);

            // 1. 获取用户的所有角色
            List<SysUserRole> userRoles = sysUserRoleService.list(
                new QueryWrapper<SysUserRole>().eq("user_id", userId)
            );

            log.info("用户 {} 的角色数量：{}", userId, userRoles.size());

            if (userRoles.isEmpty()) {
                log.warn("用户 {} 没有分配任何角色", userId);
                return false;
            }

            // 2. 检查是否有admin角色
            for (SysUserRole userRole : userRoles) {
                SysRole role = sysRoleService.getById(userRole.getRoleId());
                if (role != null) {
                    log.info("用户 {} 具有角色：{} (role_code: {})", userId, role.getRoleName(), role.getRoleCode());
                    if ("admin".equals(role.getRoleCode())) {
                        log.info("*** 用户 {} 具有admin角色 ***", userId);
                        return true;
                    }
                } else {
                    log.warn("角色ID {} 对应的角色不存在", userRole.getRoleId());
                }
            }

            log.info("用户 {} 不具有admin角色", userId);
            return false;
            
        } catch (Exception e) {
            log.error("检查用户admin角色失败，用户ID：{}，错误：{}", userId, e.getMessage(), e);
            return false;
        }
    }
    
    /**
     * 获取用户角色代码列表
     * @param userId 用户ID
     * @return 角色代码列表
     */
    public List<String> getUserRoleCodes(String userId) {
        try {
            List<SysUserRole> userRoles = sysUserRoleService.list(
                new QueryWrapper<SysUserRole>().eq("user_id", userId)
            );
            
            return userRoles.stream()
                .map(userRole -> {
                    SysRole role = sysRoleService.getById(userRole.getRoleId());
                    return role != null ? role.getRoleCode() : null;
                })
                .filter(roleCode -> roleCode != null)
                .collect(Collectors.toList());
                
        } catch (Exception e) {
            log.error("获取用户角色代码失败，用户ID：{}，错误：{}", userId, e.getMessage(), e);
            return Collections.emptyList();
        }
    }
}
