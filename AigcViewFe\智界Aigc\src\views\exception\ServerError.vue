<template>
  <div class="server-error-container">
    <!-- 动态网格背景 -->
    <div class="grid-background"></div>

    <!-- 浮动装饰元素 -->
    <div class="floating-elements">
      <div class="element element-1">
        <div class="element-core"></div>
      </div>
      <div class="element element-2">
        <div class="element-core"></div>
      </div>
      <div class="element element-3">
        <div class="element-core"></div>
      </div>
      <div class="element element-4">
        <div class="element-core"></div>
      </div>
    </div>

    <!-- 科技感扫描线 -->
    <div class="scan-lines">
      <div class="scan-line scan-line-1"></div>
      <div class="scan-line scan-line-2"></div>
      <div class="scan-line scan-line-3"></div>
    </div>

    <!-- 数据流动效果 -->
    <div class="data-streams">
      <div class="stream stream-1"></div>
      <div class="stream stream-2"></div>
      <div class="stream stream-3"></div>
    </div>

    <!-- 主要内容卡片 -->
    <div class="error-card card-primary">
      <!-- 错误图标 -->
      <div class="error-icon">
        <div class="server-icon">
          <div class="server-body">
            <div class="server-light red"></div>
            <div class="server-light yellow"></div>
            <div class="server-light green"></div>
            <div class="server-screen">
              <div class="screen-line"></div>
              <div class="screen-line"></div>
              <div class="screen-line"></div>
            </div>
          </div>
          <div class="server-base"></div>
          <div class="server-glow"></div>
        </div>
        <div class="error-symbol">⚠</div>
        <div class="error-particles">
          <div class="particle particle-1"></div>
          <div class="particle particle-2"></div>
          <div class="particle particle-3"></div>
          <div class="particle particle-4"></div>
        </div>
      </div>

      <!-- 错误信息 -->
      <div class="error-info">
        <h1 class="error-title">服务器连接异常</h1>
        <p class="error-subtitle">Server Connection Failed</p>

        <div class="error-details">
          <p class="main-message">
            抱歉，智界AIGC服务暂时无法访问
          </p>
          <p class="sub-message">
            我们的技术团队正在努力修复此问题，请稍后再试
          </p>
        </div>

        <!-- 联系信息卡片 -->
        <div class="contact-card card-secondary">
          <h3 class="contact-title">需要帮助？请联系我们：</h3>
          <div class="contact-methods">
            <div class="contact-item">
              <a-icon type="phone" class="contact-icon" />
              <span>技术支持热线：************</span>
            </div>
            <div class="contact-item">
              <a-icon type="mail" class="contact-icon" />
              <span>邮箱：<EMAIL></span>
            </div>
            <div class="contact-item">
              <a-icon type="wechat" class="contact-icon" />
              <span>微信客服：ZhiJie_AIGC</span>
            </div>
            <div class="contact-item">
              <a-icon type="qq" class="contact-icon" />
              <span>QQ群：123456789</span>
            </div>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="action-buttons">
          <button class="btn-primary" @click="retryConnection" :disabled="retrying">
            <a-icon type="reload" :spin="retrying" />
            {{ retrying ? '重新连接中...' : '重新连接' }}
          </button>
          <button class="btn-secondary" @click="goHome">
            <a-icon type="home" />
            返回首页
          </button>
          <button class="btn-secondary" @click="contactSupport">
            <a-icon type="customer-service" />
            联系客服
          </button>
        </div>

        <!-- 错误代码 -->
        <div class="error-code">
          <p>错误代码：{{ errorCode }}</p>
          <p>时间：{{ errorTime }}</p>
        </div>
      </div>
    </div>

    <!-- 底部信息 -->
    <div class="footer-info">
      <p>© 2025 <span class="brand-text">智界AIGC</span> - 专业的AI内容生成平台</p>
      <p>如问题持续存在，请保存错误代码并联系技术支持</p>
    </div>
  </div>
</template>

<script>
import { gsap } from 'gsap'
import { TextPlugin } from 'gsap/TextPlugin'
import { MorphSVGPlugin } from 'gsap/MorphSVGPlugin'

// 注册GSAP插件
gsap.registerPlugin(TextPlugin, MorphSVGPlugin)

export default {
  name: 'ServerError',
  data() {
    return {
      retrying: false,
      errorCode: 'SRV_CONNECTION_REFUSED_001',
      errorTime: new Date().toLocaleString('zh-CN'),
      animationTimeline: null
    }
  },
  mounted() {
    // 延迟初始化动画，确保DOM完全加载
    this.$nextTick(() => {
      setTimeout(() => {
        this.initAnimations()
      }, 100)
    })

    // 备用方案：如果GSAP加载失败，确保元素可见
    setTimeout(() => {
      const selectors = [
        '.contact-card',
        '.error-card',
        '.action-buttons',
        '.action-buttons .btn-primary',
        '.action-buttons .btn-secondary',
        '.error-title',
        '.error-subtitle',
        '.error-details'
      ]

      selectors.forEach(selector => {
        const elements = document.querySelectorAll(selector)
        elements.forEach(el => {
          if (el) {
            const computedStyle = getComputedStyle(el)
            if (computedStyle.opacity === '0' || computedStyle.visibility === 'hidden') {
              console.warn('⚠️ 检测到元素不可见，强制显示:', selector, el)
              el.style.opacity = '1'
              el.style.visibility = 'visible'
              el.style.transform = 'none'
              el.style.display = el.tagName === 'BUTTON' ? 'inline-block' : 'block'
            }
          }
        })
      })

      console.log('🔍 备用检测完成')
    }, 1500)
  },

  beforeDestroy() {
    if (this.animationTimeline) {
      this.animationTimeline.kill()
    }
  },

  methods: {
    // 初始化GSAP动画
    initAnimations() {
      console.log('🎬 开始初始化GSAP动画')

      // 检查元素是否存在
      const elements = ['.error-card', '.error-icon', '.error-title', '.error-subtitle', '.error-details', '.contact-card', '.action-buttons', '.error-code']
      elements.forEach(selector => {
        const el = document.querySelector(selector)
        console.log(`🔍 检查元素 ${selector}:`, el ? '存在' : '不存在')
      })

      // 立即设置所有元素为可见状态，防止闪烁
      gsap.set(['.error-card', '.error-icon', '.error-title', '.error-subtitle', '.error-details', '.contact-card', '.action-buttons', '.action-buttons button', '.error-code'], {
        opacity: 1,
        transform: 'none',
        clearProps: 'all'
      })

      console.log('✅ 所有元素已设置为可见状态')

      // 创建主时间轴
      this.animationTimeline = gsap.timeline({
        onComplete: () => {
          console.log('✅ GSAP动画完成')
          // 动画完成后再次确保所有元素可见
          gsap.set(['.contact-card', '.action-buttons', '.action-buttons button'], {
            opacity: 1,
            clearProps: 'transform'
          })
        }
      })

      // 简化的页面入场动画 - 分别执行，避免时间轴问题

      // 1. 主卡片动画
      gsap.from('.error-card', {
        duration: 1,
        scale: 0.8,
        opacity: 0,
        y: 50,
        ease: 'back.out(1.7)',
        onComplete: () => {
          console.log('✅ error-card 动画完成')
          // 确保卡片内容可见
          gsap.set('.error-card *', { opacity: 1 })
        }
      })

      // 2. 错误图标动画
      gsap.from('.error-icon', {
        duration: 0.8,
        scale: 0,
        rotation: 180,
        ease: 'elastic.out(1, 0.5)',
        delay: 0.3,
        onComplete: () => console.log('✅ error-icon 动画完成')
      })

      // 3. 文字内容动画
      gsap.from('.error-title', {
        duration: 0.8,
        y: 30,
        opacity: 0,
        ease: 'power2.out',
        delay: 0.5,
        onComplete: () => console.log('✅ error-title 动画完成')
      })

      gsap.from('.error-subtitle', {
        duration: 0.6,
        y: 20,
        opacity: 0,
        ease: 'power2.out',
        delay: 0.7,
        onComplete: () => console.log('✅ error-subtitle 动画完成')
      })

      gsap.from('.error-details', {
        duration: 0.8,
        y: 30,
        opacity: 0,
        ease: 'power2.out',
        delay: 0.9,
        onComplete: () => console.log('✅ error-details 动画完成')
      })

      // 4. 联系卡片动画
      gsap.from('.contact-card', {
        duration: 0.8,
        y: 40,
        opacity: 0,
        ease: 'power2.out',
        delay: 1.1,
        onComplete: () => {
          console.log('✅ contact-card 动画完成')
          // 强制确保联系卡片可见
          const contactCard = document.querySelector('.contact-card')
          if (contactCard) {
            contactCard.style.opacity = '1'
            contactCard.style.visibility = 'visible'
          }
        }
      })

      // 5. 按钮动画
      gsap.from('.btn-primary', {
        duration: 0.6,
        y: 30,
        opacity: 0,
        ease: 'back.out(1.7)',
        delay: 1.3,
        onComplete: () => {
          console.log('✅ btn-primary 动画完成')
          const btn = document.querySelector('.btn-primary')
          if (btn) {
            btn.style.opacity = '1'
            btn.style.visibility = 'visible'
          }
        }
      })

      gsap.from('.btn-secondary', {
        duration: 0.6,
        y: 30,
        opacity: 0,
        ease: 'back.out(1.7)',
        delay: 1.4,
        stagger: 0.1,
        onComplete: () => {
          console.log('✅ btn-secondary 动画完成')
          const btns = document.querySelectorAll('.btn-secondary')
          btns.forEach(btn => {
            btn.style.opacity = '1'
            btn.style.visibility = 'visible'
          })
        }
      })

      // 6. 错误代码动画
      gsap.from('.error-code', {
        duration: 0.6,
        opacity: 0,
        ease: 'power2.out',
        delay: 1.6,
        onComplete: () => console.log('✅ error-code 动画完成')
      })

      // 服务器指示灯闪烁动画
      this.initServerLightAnimation()

      // 浮动元素动画
      this.initFloatingAnimation()

      // 网格背景动画
      this.initGridAnimation()

      // 错误符号脉冲动画
      this.initErrorSymbolAnimation()
    },

    // 服务器指示灯动画
    initServerLightAnimation() {
      gsap.to('.server-light.red', {
        opacity: 0.3,
        duration: 0.8,
        repeat: -1,
        yoyo: true,
        ease: 'power2.inOut'
      })

      gsap.to('.server-light.yellow', {
        opacity: 0.6,
        duration: 1.2,
        repeat: -1,
        yoyo: true,
        ease: 'power2.inOut',
        delay: 0.3
      })

      gsap.to('.server-light.green', {
        opacity: 0.2,
        duration: 2,
        repeat: -1,
        yoyo: true,
        ease: 'power2.inOut',
        delay: 0.6
      })
    },

    // 浮动元素动画
    initFloatingAnimation() {
      gsap.to('.element-1', {
        y: -20,
        rotation: 360,
        duration: 8,
        repeat: -1,
        yoyo: true,
        ease: 'sine.inOut'
      })

      gsap.to('.element-2', {
        y: -30,
        rotation: -360,
        duration: 12,
        repeat: -1,
        yoyo: true,
        ease: 'sine.inOut',
        delay: 2
      })

      gsap.to('.element-3', {
        y: -15,
        rotation: 180,
        duration: 6,
        repeat: -1,
        yoyo: true,
        ease: 'sine.inOut',
        delay: 4
      })

      gsap.to('.element-4', {
        y: -25,
        rotation: -180,
        duration: 10,
        repeat: -1,
        yoyo: true,
        ease: 'sine.inOut',
        delay: 1
      })
    },

    // 网格背景动画增强
    initGridAnimation() {
      gsap.to('.grid-background', {
        backgroundPosition: '60px 60px',
        duration: 20,
        repeat: -1,
        ease: 'none'
      })
    },

    // 错误符号脉冲动画
    initErrorSymbolAnimation() {
      gsap.to('.error-symbol', {
        scale: 1.2,
        duration: 1.5,
        repeat: -1,
        yoyo: true,
        ease: 'power2.inOut'
      })

      // 错误粒子动画
      gsap.to('.particle', {
        y: -60,
        opacity: 0,
        duration: 4,
        repeat: -1,
        stagger: 1,
        ease: 'power2.out'
      })

      // 服务器光晕动画
      gsap.to('.server-glow', {
        scale: 1.1,
        opacity: 0.8,
        duration: 2,
        repeat: -1,
        yoyo: true,
        ease: 'sine.inOut'
      })

      // 扫描线动画
      gsap.to('.scan-line', {
        x: '100vw',
        duration: 4,
        repeat: -1,
        stagger: 1.5,
        ease: 'none'
      })

      // 数据流动画
      gsap.to('.stream', {
        y: '100vh',
        duration: 3,
        repeat: -1,
        stagger: 1,
        ease: 'none'
      })

      // 浮动元素核心发光
      gsap.to('.element-core', {
        scale: 1.5,
        opacity: 0.8,
        duration: 2,
        repeat: -1,
        yoyo: true,
        stagger: 0.5,
        ease: 'sine.inOut'
      })
    },

    // 重新连接 - 直接返回首页
    async retryConnection() {
      this.retrying = true

      // 添加重连动画
      gsap.to('.btn-primary', {
        scale: 0.95,
        duration: 0.1,
        yoyo: true,
        repeat: 1
      })

      try {
        // 等待1秒显示重连效果
        await new Promise(resolve => setTimeout(resolve, 1000))

        // 直接跳转到首页，如果服务器还有问题会再次跳转到异常页面
        this.$router.push('/home')
      } catch (error) {
        this.$message.error('跳转失败，请稍后再试')
      } finally {
        this.retrying = false
      }
    },

    // 返回首页
    goHome() {
      // 添加按钮点击动画
      gsap.to('.btn-secondary', {
        scale: 0.95,
        duration: 0.1,
        yoyo: true,
        repeat: 1,
        onComplete: () => {
          this.$router.push('/home')
        }
      })
    },

    // 联系客服
    contactSupport() {
      // 添加按钮点击动画
      gsap.to('.btn-secondary', {
        scale: 0.95,
        duration: 0.1,
        yoyo: true,
        repeat: 1,
        onComplete: () => {
          // 提示暂未接入客服
          this.$message.info('客服系统暂未接入，请稍后再试或通过其他方式联系我们')
        }
      })
    }
  }
}
</script>

<style lang="less" scoped>
// 智界AIGC设计规范 - 服务器异常页面
.server-error-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
  font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
  padding: 2rem;
}

// 动态网格背景 - 按照开发规范
.grid-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    linear-gradient(rgba(59, 130, 246, 0.05) 1px, transparent 1px),
    linear-gradient(90deg, rgba(59, 130, 246, 0.05) 1px, transparent 1px);
  background-size: 60px 60px;
  animation: grid-move 30s linear infinite;
  z-index: 1;
}

@keyframes grid-move {
  0% { transform: translate(0, 0); }
  100% { transform: translate(60px, 60px); }
}

// 浮动装饰元素 - 科技感增强
.floating-elements {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;

  .element {
    position: absolute;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(139, 92, 246, 0.1) 100%);
    border: 1px solid rgba(59, 130, 246, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;

    .element-core {
      width: 30%;
      height: 30%;
      background: radial-gradient(circle, rgba(59, 130, 246, 0.8) 0%, rgba(139, 92, 246, 0.4) 100%);
      border-radius: 50%;
      box-shadow: 0 0 20px rgba(59, 130, 246, 0.5);
    }

    &.element-1 {
      width: 80px;
      height: 80px;
      top: 20%;
      left: 10%;
    }

    &.element-2 {
      width: 120px;
      height: 120px;
      top: 60%;
      right: 15%;
    }

    &.element-3 {
      width: 60px;
      height: 60px;
      bottom: 30%;
      left: 20%;
    }

    &.element-4 {
      width: 100px;
      height: 100px;
      top: 10%;
      right: 30%;
    }
  }
}

// 科技感扫描线
.scan-lines {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  pointer-events: none;

  .scan-line {
    position: absolute;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, transparent 0%, rgba(59, 130, 246, 0.8) 50%, transparent 100%);
    animation: scan-move 4s linear infinite;

    &.scan-line-1 {
      top: 20%;
      animation-delay: 0s;
    }

    &.scan-line-2 {
      top: 50%;
      animation-delay: 1.5s;
    }

    &.scan-line-3 {
      top: 80%;
      animation-delay: 3s;
    }
  }
}

@keyframes scan-move {
  0% {
    transform: translateX(-100%);
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    transform: translateX(100%);
    opacity: 0;
  }
}

// 数据流动效果
.data-streams {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  pointer-events: none;

  .stream {
    position: absolute;
    width: 2px;
    height: 100px;
    background: linear-gradient(180deg, transparent 0%, rgba(59, 130, 246, 0.6) 50%, transparent 100%);
    animation: stream-flow 3s linear infinite;

    &.stream-1 {
      left: 15%;
      animation-delay: 0s;
    }

    &.stream-2 {
      left: 50%;
      animation-delay: 1s;
    }

    &.stream-3 {
      right: 20%;
      animation-delay: 2s;
    }
  }
}

@keyframes stream-flow {
  0% {
    transform: translateY(-100px);
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    transform: translateY(100vh);
    opacity: 0;
  }
}

// 主要内容卡片 - 按照开发规范
.error-card {
  max-width: 700px;
  width: 90%;
  z-index: 2;
  position: relative;
  text-align: center;
}

// 卡片样式 - 按照开发规范
.card-primary {
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid rgba(59, 130, 246, 0.1);
  border-radius: 24px;
  padding: 3rem;
  backdrop-filter: blur(20px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.card-secondary {
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(59, 130, 246, 0.1);
  border-radius: 16px;
  padding: 1.5rem;
  backdrop-filter: blur(10px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  margin: 2rem 0;
  opacity: 1; /* 确保初始可见 */
}

// 强制可见样式 - 防止GSAP问题
.contact-card,
.action-buttons,
.action-buttons .btn-primary,
.action-buttons .btn-secondary,
.error-card,
.error-info {
  opacity: 1 !important; /* 强制可见，防止GSAP问题 */
  visibility: visible !important;
}

// 确保按钮容器可见
.action-buttons {
  display: flex !important;
  opacity: 1 !important;

  .btn-primary,
  .btn-secondary {
    opacity: 1 !important;
    display: inline-block !important;
  }
}

// 错误图标 - 科技感增强
.error-icon {
  position: relative;
  margin-bottom: 2rem;

  .server-icon {
    display: inline-block;
    position: relative;

    .server-body {
      width: 100px;
      height: 80px;
      background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
      border: 2px solid rgba(59, 130, 246, 0.3);
      border-radius: 12px;
      margin: 0 auto 10px;
      position: relative;
      box-shadow:
        0 0 30px rgba(59, 130, 246, 0.3),
        inset 0 2px 4px rgba(255, 255, 255, 0.1);

      .server-light {
        position: absolute;
        width: 10px;
        height: 10px;
        border-radius: 50%;
        top: 20px;

        &.red {
          left: 20px;
          background: radial-gradient(circle, #ef4444 0%, #dc2626 100%);
          box-shadow:
            0 0 15px #ef4444,
            0 0 30px rgba(239, 68, 68, 0.5);
        }

        &.yellow {
          left: 40px;
          background: radial-gradient(circle, #f59e0b 0%, #d97706 100%);
          box-shadow:
            0 0 15px #f59e0b,
            0 0 30px rgba(245, 158, 11, 0.5);
        }

        &.green {
          left: 60px;
          background: radial-gradient(circle, #10b981 0%, #059669 100%);
          box-shadow:
            0 0 15px #10b981,
            0 0 30px rgba(16, 185, 129, 0.5);
        }
      }

      .server-screen {
        position: absolute;
        bottom: 15px;
        left: 15px;
        right: 15px;
        height: 25px;
        background: #0f172a;
        border-radius: 4px;
        border: 1px solid rgba(59, 130, 246, 0.2);
        display: flex;
        flex-direction: column;
        justify-content: space-around;
        padding: 3px;

        .screen-line {
          height: 2px;
          background: linear-gradient(90deg, transparent 0%, rgba(59, 130, 246, 0.8) 50%, transparent 100%);
          border-radius: 1px;
          animation: data-flow 2s linear infinite;

          &:nth-child(2) {
            animation-delay: 0.5s;
          }

          &:nth-child(3) {
            animation-delay: 1s;
          }
        }
      }
    }

    .server-base {
      width: 110px;
      height: 20px;
      background: linear-gradient(135deg, #334155 0%, #1e293b 100%);
      border: 1px solid rgba(59, 130, 246, 0.2);
      border-radius: 0 0 12px 12px;
      margin: 0 auto;
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    }

    .server-glow {
      position: absolute;
      top: -10px;
      left: -10px;
      right: -10px;
      bottom: -10px;
      background: radial-gradient(circle, rgba(59, 130, 246, 0.1) 0%, transparent 70%);
      border-radius: 20px;
      animation: glow-pulse 3s ease-in-out infinite;
    }
  }

  .error-symbol {
    position: absolute;
    top: -15px;
    right: -15px;
    font-size: 3rem;
    color: #ef4444;
    text-shadow: 0 0 20px rgba(239, 68, 68, 0.8);
    z-index: 2;
  }

  .error-particles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;

    .particle {
      position: absolute;
      width: 4px;
      height: 4px;
      background: radial-gradient(circle, rgba(59, 130, 246, 0.8) 0%, transparent 100%);
      border-radius: 50%;
      animation: particle-float 4s linear infinite;

      &.particle-1 {
        top: 20%;
        left: 10%;
        animation-delay: 0s;
      }

      &.particle-2 {
        top: 30%;
        right: 15%;
        animation-delay: 1s;
      }

      &.particle-3 {
        bottom: 25%;
        left: 20%;
        animation-delay: 2s;
      }

      &.particle-4 {
        bottom: 35%;
        right: 25%;
        animation-delay: 3s;
      }
    }
  }
}

// 科技感动画关键帧
@keyframes data-flow {
  0% {
    transform: translateX(-100%);
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    transform: translateX(100%);
    opacity: 0;
  }
}

@keyframes glow-pulse {
  0%, 100% {
    opacity: 0.5;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}

@keyframes particle-float {
  0% {
    transform: translateY(0) scale(0);
    opacity: 0;
  }
  50% {
    opacity: 1;
    transform: translateY(-30px) scale(1);
  }
  100% {
    transform: translateY(-60px) scale(0);
    opacity: 0;
  }
}

// 错误信息 - 按照开发规范
.error-info {
  .error-title {
    font-size: 2.5rem;
    font-weight: 700;
    background: linear-gradient(135deg, #1e293b 0%, #3b82f6 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 1rem;
  }

  .error-subtitle {
    font-size: 1rem;
    color: #6b7280;
    margin-bottom: 2rem;
    font-weight: 400;
  }

  .error-details {
    margin-bottom: 2.5rem;

    .main-message {
      font-size: 1.25rem;
      color: #374151;
      margin-bottom: 1rem;
      font-weight: 600;
    }

    .sub-message {
      font-size: 1rem;
      color: #6b7280;
      line-height: 1.6;
    }
  }
}

// 联系信息卡片 - 按照开发规范
.contact-card {
  text-align: left;

  .contact-title {
    font-size: 1.125rem;
    color: #374151;
    margin-bottom: 1.5rem;
    font-weight: 600;
  }

  .contact-methods {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
    }
  }

  .contact-item {
    display: flex;
    align-items: center;
    font-size: 0.875rem;
    color: #374151;
    padding: 0.75rem;
    border-radius: 8px;
    background: rgba(59, 130, 246, 0.05);
    border: 1px solid rgba(59, 130, 246, 0.1);
    transition: all 0.3s ease;

    &:hover {
      background: rgba(59, 130, 246, 0.1);
      border-color: rgba(59, 130, 246, 0.2);
      transform: translateY(-1px);
    }

    .contact-icon {
      margin-right: 0.75rem;
      color: #3b82f6;
      font-size: 1rem;
    }
  }
}

// 操作按钮 - 按照开发规范
.action-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin: 2rem 0;
  flex-wrap: wrap;
}

// 主要按钮 - 按照开发规范
.btn-primary {
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  border: none;
  border-radius: 12px;
  color: white;
  font-weight: 600;
  padding: 1rem 2rem;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  cursor: pointer;
  font-size: 1rem;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
  }

  &:hover::before {
    left: 100%;
  }

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 32px rgba(59, 130, 246, 0.4);
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
  }

  .anticon {
    margin-right: 0.5rem;
  }
}

// 次要按钮 - 按照开发规范
.btn-secondary {
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(59, 130, 246, 0.2);
  border-radius: 12px;
  color: #6b7280;
  font-weight: 500;
  padding: 0.75rem 1.5rem;
  transition: all 0.3s ease;
  cursor: pointer;
  font-size: 1rem;

  &:hover {
    background: rgba(255, 255, 255, 0.95);
    border-color: rgba(59, 130, 246, 0.3);
    color: #374151;
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  }

  .anticon {
    margin-right: 0.5rem;
  }
}

// 错误代码 - 按照开发规范
.error-code {
  font-size: 0.75rem;
  color: #9ca3af;
  border-top: 1px solid rgba(59, 130, 246, 0.1);
  padding-top: 1.5rem;
  margin-top: 2rem;

  p {
    margin: 0.25rem 0;
  }
}

// 底部信息 - 按照开发规范
.footer-info {
  position: absolute;
  bottom: 2rem;
  text-align: center;
  color: #6b7280;
  font-size: 0.875rem;
  z-index: 2;

  p {
    margin: 0.5rem 0;
  }

  .brand-text {
    font-weight: 700;
    background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
}

// 响应式设计 - 按照开发规范
@media (max-width: 768px) {
  .server-error-container {
    padding: 1rem;
  }

  .error-card {
    padding: 2rem 1.5rem;
    border-radius: 16px;
  }

  .error-info .error-title {
    font-size: 2rem;
  }

  .action-buttons {
    flex-direction: column;
    align-items: center;
    gap: 0.75rem;

    .btn-primary,
    .btn-secondary {
      width: 100%;
      max-width: 280px;
    }
  }

  .contact-methods {
    grid-template-columns: 1fr !important;
  }

  .footer-info {
    position: relative;
    bottom: auto;
    margin-top: 2rem;
  }
}
</style>
