package org.jeecg.modules.jianyingpro.service.internal;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.PostConstruct;
import java.nio.charset.StandardCharsets;

/**
 * 超级剪映小助手 - Coze平台API服务
 * 复制自CozeApiService，保持所有原有业务逻辑不变
 * 
 * <AUTHOR>
 * @since 2025-01-01
 */
@Slf4j
@Service
public class JianyingProCozeApiService {

    @Value("${coze.api.base-url:https://api.coze.cn}")
    private String cozeBaseUrl;

    @Value("${coze.api.token:}")
    private String cozeApiToken;

    // 🆕 CDN配置
    @Value("${volcengine.tos.cdn.enabled:false}")
    private Boolean cdnEnabled;

    @Value("${volcengine.tos.cdn.domain:}")
    private String cdnDomain;

    private final RestTemplate restTemplate = new RestTemplate();

    @Autowired
    private JianyingProTosService tosService;

    @Autowired
    private JianyingProDataboxService jianyingDataboxService;

    /**
     * 初始化RestTemplate，设置UTF-8编码
     */
    @PostConstruct
    public void initRestTemplate() {
        // 设置UTF-8编码的StringHttpMessageConverter
        restTemplate.getMessageConverters()
                .stream()
                .filter(converter -> converter instanceof org.springframework.http.converter.StringHttpMessageConverter)
                .forEach(converter -> ((org.springframework.http.converter.StringHttpMessageConverter) converter).setDefaultCharset(StandardCharsets.UTF_8));

        // 设置更长的超时时间，避免TOS访问超时
        restTemplate.getRequestFactory();
        log.info("超级剪映小助手 - RestTemplate初始化完成，支持TOS文件下载");
    }

    /**
     * 调用Coze平台快速创建素材轨道API（本地处理）
     * 简化版本：直接返回成功结果，实际功能由JianyingProAssistantService处理
     */
    public JSONObject callEasyCreateMaterial(String videoUrl, String text, Double textTransformX, Double textTransformY,
                                           Integer fontSize, String imgUrl, String textColor,
                                           String audioUrl, String draftUrl) {
        try {
            log.info("本地处理快速创建素材轨道请求 - audioUrl: {}, draftUrl: {}", audioUrl, draftUrl);

            // 参数验证
            if (audioUrl == null || audioUrl.trim().isEmpty()) {
                throw new RuntimeException("音频链接不能为空");
            }
            if (draftUrl == null || draftUrl.trim().isEmpty()) {
                throw new RuntimeException("草稿地址不能为空");
            }

            // 简化实现：直接返回成功结果
            // 实际的素材添加逻辑由JianyingProAssistantService中的其他方法处理

            // 计算添加的素材数量
            int materialCount = 1; // 音频必有
            if (videoUrl != null && !videoUrl.trim().isEmpty()) materialCount++;
            if (imgUrl != null && !imgUrl.trim().isEmpty()) materialCount++;
            if (text != null && !text.trim().isEmpty()) materialCount++;

            // 返回结果（匹配竞争对手格式）
            JSONObject result = new JSONObject();
            result.put("message", "不知道导入的请看：https://www.aigcview.com/JianYingDraft?draft=" + draftUrl);
            result.put("draft_url", draftUrl);
            result.put("material_count", materialCount);

            log.info("快速创建素材轨道请求处理完成 - 素材数量: {}", materialCount);
            return result;

        } catch (Exception e) {
            log.error("快速创建素材轨道失败", e);
            throw new RuntimeException("快速创建素材轨道失败", e);
        }
    }



    /**
     * 下载音频文件并上传到TOS（使用统一文件夹ID，供add_audios接口使用）
     * @return String[] {fileName, downloadUrl}
     */
    public String[] downloadAndUploadAudioWithUnifiedFolder(String audioUrl, String unifiedFolderId) {
        return downloadAndUploadAudio(audioUrl, unifiedFolderId);
    }

    /**
     * 下载视频文件并上传到TOS（使用统一文件夹ID，供add_videos接口使用）
     * @return String[] {fileName, downloadUrl}
     */
    public String[] downloadAndUploadVideoWithUnifiedFolder(String videoUrl, String unifiedFolderId) {
        return downloadAndUploadVideo(videoUrl, unifiedFolderId);
    }

    /**
     * 下载视频文件并上传到TOS
     */
    private String[] downloadAndUploadVideo(String videoUrl, String folderId) {
        try {
            log.info("开始下载视频文件: {}", videoUrl);

            // 下载视频文件
            byte[] videoData = downloadFile(videoUrl);

            // 生成视频文件名
            String videoFileName = java.util.UUID.randomUUID().toString() + ".mp4";

            // 上传到TOS（使用现有的uploadAudioFile方法，它可以上传任何文件）
            String videoPath = "/jianying-assistant/drafts/" +
                java.time.LocalDate.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyy/MM/dd")) +
                "/" + folderId + "/" + videoFileName;

            tosService.uploadAudioFile(videoPath, videoData);

            // 返回系统内部路径，不暴露TOS签名URL
            String internalPath = videoPath;

            log.info("视频文件上传成功，内部路径: {}", internalPath);
            return new String[]{videoFileName, internalPath};

        } catch (Exception e) {
            log.error("视频文件处理失败: {}", videoUrl, e);
            throw new RuntimeException("视频文件处理失败: " + e.getMessage(), e);
        }
    }

    /**
     * 下载音频文件并上传到TOS（带回退机制）
     * @return String[] {fileName, downloadUrl}
     */
    public String[] downloadAndUploadAudioWithFallback(String audioUrl, String unifiedFolderId) {
        long startTime = System.currentTimeMillis();

        log.info("开始容错下载音频文件: {}", audioUrl);

        // 重试机制：最多3次
        for (int attempt = 1; attempt <= 3; attempt++) {
            try {
                log.info("尝试下载音频文件 (第{}次): {}", attempt, audioUrl);

                // 调用原有的下载方法
                String[] result = downloadAndUploadAudio(audioUrl, unifiedFolderId);
                long downloadTime = System.currentTimeMillis() - startTime;

                log.info("音频文件下载成功: {}, 耗时: {}ms", audioUrl, downloadTime);
                return result;

            } catch (Exception e) {
                log.warn("音频文件下载失败 (第{}次): {}, 错误: {}", attempt, audioUrl, e.getMessage());

                // 如果是最后一次重试，使用占位符
                if (attempt == 3) {
                    log.info("使用静音占位符替代失败的音频文件: {}", audioUrl);
                    return createSilentAudioPlaceholder(unifiedFolderId, e.getMessage());
                }

                // 指数退避等待
                try {
                    Thread.sleep(attempt * 1000);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }
        }

        // 兜底处理
        log.error("音频文件下载重试失败，使用占位符: {}", audioUrl);
        return createSilentAudioPlaceholder(unifiedFolderId, "下载重试失败");
    }

    /**
     * 创建静音音频占位符
     */
    private String[] createSilentAudioPlaceholder(String folderId, String errorMessage) {
        try {
            log.info("创建静音音频占位符，错误信息: {}", errorMessage);

            // 生成占位符文件名
            String placeholderFileName = "silent_placeholder_" + java.util.UUID.randomUUID().toString() + ".mp3";

            // 创建最小的MP3文件
            byte[] silentAudioData = createEmptyAudioFile();

            // 构建TOS路径
            String placeholderPath = "/jianying-assistant/drafts/" +
                                   java.time.LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyy/MM/dd/")) +
                                   "0E685133-18CE-45ED-8CB8-2904A212EC80/" + folderId + "/" + placeholderFileName;

            // 上传占位符文件
            tosService.uploadAudioFile(placeholderPath, silentAudioData);

            // 生成下载URL
            String placeholderDownloadUrl = tosService.generateSystemFileUrl(placeholderPath);

            log.info("静音占位符创建成功: {}", placeholderDownloadUrl);
            return new String[]{placeholderFileName, placeholderDownloadUrl};

        } catch (Exception e) {
            log.error("创建静音占位符失败", e);
            // 返回一个基本的占位符信息
            String fallbackFileName = "error_placeholder.mp3";
            String fallbackUrl = "placeholder://error";
            return new String[]{fallbackFileName, fallbackUrl};
        }
    }

    /**
     * 下载草稿JSON文件
     */
    public JSONObject downloadDraftJson(String draftUrl) {
        try {
            log.info("开始下载草稿JSON: {}", draftUrl);

            // 委托给现有的方法
            return downloadAndParseDraft(draftUrl);

        } catch (Exception e) {
            log.error("下载草稿JSON失败: {}", draftUrl, e);
            throw new RuntimeException("下载草稿JSON失败: " + e.getMessage(), e);
        }
    }

    /**
     * 下载图片文件并上传到TOS
     * @return String[] {fileName, downloadUrl}
     */
    public String[] downloadAndUploadImage(String imageUrl, String unifiedFolderId) {
        try {
            log.info("开始下载图片文件: {}", imageUrl);

            // 下载图片文件
            byte[] imageData = downloadFile(imageUrl);

            // 生成图片文件名（根据URL判断扩展名）
            String extension = ".png"; // 默认png
            if (imageUrl.toLowerCase().contains(".jpg") || imageUrl.toLowerCase().contains(".jpeg")) {
                extension = ".jpg";
            } else if (imageUrl.toLowerCase().contains(".gif")) {
                extension = ".gif";
            } else if (imageUrl.toLowerCase().contains(".webp")) {
                extension = ".webp";
            }

            String imageFileName = java.util.UUID.randomUUID().toString() + extension;

            // 上传到TOS
            String imagePath = "/jianying-assistant/drafts/" +
                java.time.LocalDate.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyy/MM/dd")) +
                "/" + unifiedFolderId + "/" + imageFileName;

            tosService.uploadAudioFile(imagePath, imageData);

            // 生成下载URL（返回系统URL，避免内网TOS签名失败）
            String imageDownloadUrl = tosService.generateSystemFileUrl(imagePath);

            log.info("图片文件上传成功: {}", imageDownloadUrl);
            return new String[]{imageFileName, imageDownloadUrl};

        } catch (Exception e) {
            log.error("下载并上传图片文件失败: {}", imageUrl, e);
            throw new RuntimeException("下载并上传图片文件失败: " + e.getMessage(), e);
        }
    }

    /**
     * 下载并上传音频文件（核心方法）
     */
    private String[] downloadAndUploadAudio(String audioUrl, String folderId) {
        try {
            log.info("开始下载音频文件: {}", audioUrl);

            // 1. 尝试下载音频文件到临时位置
            byte[] audioData;
            try {
                audioData = downloadAudioFile(audioUrl);
                log.info("音频文件下载成功: {}", audioUrl);
            } catch (Exception e) {
                log.warn("音频文件下载失败，创建空音频文件: {}, 错误: {}", audioUrl, e.getMessage());
                // 创建一个空的音频文件（最小的MP3文件）
                audioData = createEmptyAudioFile();
            }

            // 2. 生成音频文件名（使用UUID确保唯一性）
            String audioFileName = java.util.UUID.randomUUID().toString() + ".mp3";

            // 3. 构建TOS中的音频文件路径（与剪映path格式匹配）
            String audioFilePath = "/jianying-assistant/drafts/" +
                                 java.time.LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyy/MM/dd/")) +
                                 "0E685133-18CE-45ED-8CB8-2904A212EC80/" + folderId + "/" + audioFileName;

            // 4. 上传音频文件到TOS
            tosService.uploadAudioFile(audioFilePath, audioData);

            // 5. 生成音频文件的下载URL（返回系统URL，避免内网TOS签名失败）
            String audioDownloadUrl = tosService.generateSystemFileUrl(audioFilePath);

            log.info("音频文件处理完成: {}, 下载URL: {}", audioFilePath, audioDownloadUrl);
            return new String[]{audioFileName, audioDownloadUrl};

        } catch (Exception e) {
            log.error("音频文件处理失败: {}", audioUrl, e);
            throw new RuntimeException("音频文件处理失败: " + e.getMessage(), e);
        }
    }

    /**
     * 下载音频文件
     */
    private byte[] downloadAudioFile(String audioUrl) {
        return downloadFile(audioUrl);
    }

    /**
     * 创建空音频文件（最小的MP3文件）
     */
    private byte[] createEmptyAudioFile() {
        // 返回一个最小的MP3文件头
        return new byte[]{
            (byte)0xFF, (byte)0xFB, (byte)0x90, (byte)0x00,
            (byte)0x00, (byte)0x00, (byte)0x00, (byte)0x00,
            (byte)0x00, (byte)0x00, (byte)0x00, (byte)0x00
        };
    }

    /**
     * 通用文件下载方法
     */
    private byte[] downloadFile(String fileUrl) {
        try {
            log.info("开始下载文件: {}", fileUrl);

            org.springframework.web.client.RestTemplate restTemplate = new org.springframework.web.client.RestTemplate();
            org.springframework.http.ResponseEntity<byte[]> response = restTemplate.getForEntity(fileUrl, byte[].class);

            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                log.info("文件下载成功，大小: {} bytes", response.getBody().length);
                return response.getBody();
            } else {
                throw new RuntimeException("文件下载失败，HTTP状态码: " + response.getStatusCode());
            }

        } catch (Exception e) {
            log.error("文件下载失败: {}", fileUrl, e);
            throw new RuntimeException("文件下载失败: " + e.getMessage(), e);
        }
    }

    /**
     * 通用Coze API调用方法
     */
    public JSONObject callCozeApi(String endpoint, JSONObject parameters) {
        try {
            String url = cozeBaseUrl + endpoint;
            
            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(new MediaType("application", "json", StandardCharsets.UTF_8));
            headers.setAcceptCharset(java.util.Collections.singletonList(StandardCharsets.UTF_8));
            if (cozeApiToken != null && !cozeApiToken.isEmpty()) {
                headers.setBearerAuth(cozeApiToken);
            }
            
            HttpEntity<String> entity = new HttpEntity<>(parameters.toJSONString(), headers);
            
            // 发送请求
            ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.POST, entity, String.class);
            
            if (response.getStatusCode().is2xxSuccessful()) {
                String responseBody = response.getBody();
                if (responseBody != null && !responseBody.isEmpty()) {
                    return JSONObject.parseObject(responseBody);
                } else {
                    log.warn("Coze API响应为空: {}", endpoint);
                    return new JSONObject();
                }
            } else {
                log.error("Coze API调用失败: {} - {}", endpoint, response.getStatusCode());
                return null;
            }
            
        } catch (Exception e) {
            log.error("Coze API调用异常: {} - {}", endpoint, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 🔒 下载并解析草稿JSON文件（剪映草稿文件优先内网TOS）
     */
    public JSONObject downloadAndParseDraft(String draftUrl) {
        try {
            log.info("开始下载草稿JSON: {}", draftUrl);

            // 🔒 剪映草稿文件必须使用内网TOS（免费流量）
            if (draftUrl.contains("jianying-assistant/drafts/")) {
                log.info("检测到剪映草稿文件，使用内网TOS下载: {}", draftUrl);

                String objectKey;
                if (draftUrl.contains("aigcview.cn") && draftUrl.contains("/sys/common/")) {
                    // 系统URL：提取文件路径
                    objectKey = extractFilePathFromUrl(draftUrl);
                } else {
                    // TOS URL：提取对象键
                    objectKey = extractObjectKeyFromUrl(draftUrl);
                }

                // 直接使用内网TOS SDK下载（不尝试CDN）
                return downloadDraftViaTosSDK(objectKey);
            }

            // 🌐 其他文件：使用HTTP下载
            log.info("使用HTTP下载非剪映文件: {}", draftUrl);
            return downloadDraftViaHttp(draftUrl);

        } catch (Exception e) {
            log.error("下载草稿文件失败: {}", draftUrl, e);
            throw new RuntimeException("下载草稿文件失败: " + e.getMessage(), e);
        }
    }

    /**
     * 通过内网TOS SDK下载草稿文件
     */
    private JSONObject downloadDraftViaTosSDK(String objectKey) {
        try {
            String jsonContent = tosService.downloadDraftFile(objectKey);
            return JSONObject.parseObject(jsonContent);
        } catch (Exception e) {
            log.error("TOS SDK下载草稿失败: {}", objectKey, e);
            throw new RuntimeException("TOS SDK下载草稿失败: " + e.getMessage(), e);
        }
    }

    /**
     * 通过HTTP下载草稿文件
     */
    private JSONObject downloadDraftViaHttp(String draftUrl) {
        try {
            ResponseEntity<String> response = restTemplate.getForEntity(draftUrl, String.class);
            if (response.getStatusCode().is2xxSuccessful()) {
                String jsonContent = response.getBody();
                if (jsonContent != null && !jsonContent.isEmpty()) {
                    return JSONObject.parseObject(jsonContent);
                } else {
                    throw new RuntimeException("草稿文件内容为空");
                }
            } else {
                throw new RuntimeException("HTTP下载失败，状态码: " + response.getStatusCode());
            }
        } catch (Exception e) {
            log.error("HTTP下载草稿失败: {}", draftUrl, e);
            throw new RuntimeException("HTTP下载草稿失败: " + e.getMessage(), e);
        }
    }

    /**
     * 从系统URL中提取文件路径
     */
    private String extractFilePathFromUrl(String url) {
        try {
            // 系统URL格式：https://aigcview.cn/jeecg-boot/sys/common/jianying-file/jianying-assistant/drafts/...
            String prefix = "/sys/common/jianying-file/";
            int index = url.indexOf(prefix);
            if (index != -1) {
                return url.substring(index + prefix.length());
            } else {
                throw new RuntimeException("无法从系统URL中提取文件路径: " + url);
            }
        } catch (Exception e) {
            log.error("提取系统URL文件路径失败: {}", url, e);
            throw new RuntimeException("提取系统URL文件路径失败: " + e.getMessage(), e);
        }
    }

    /**
     * 从TOS URL中提取对象键
     */
    private String extractObjectKeyFromUrl(String url) {
        try {
            // TOS URL格式：https://tos-cn-guangzhou.volces.com/bucket/jianying-assistant/drafts/...
            String[] parts = url.split("/");
            if (parts.length >= 5) {
                // 从第4个部分开始是对象键（跳过协议、域名、桶名）
                StringBuilder objectKey = new StringBuilder();
                for (int i = 4; i < parts.length; i++) {
                    if (i > 4) objectKey.append("/");
                    objectKey.append(parts[i]);
                }
                return objectKey.toString();
            } else {
                throw new RuntimeException("无法从TOS URL中提取对象键: " + url);
            }
        } catch (Exception e) {
            log.error("提取TOS URL对象键失败: {}", url, e);
            throw new RuntimeException("提取TOS URL对象键失败: " + e.getMessage(), e);
        }
    }

    /**
     * 覆盖保存草稿文件到原地址
     */
    public void overwriteDraftFile(String draftUrl, JSONObject updatedDraft) {
        try {
            log.info("开始覆盖保存草稿文件: {}", draftUrl);

            // 🔒 剪映草稿文件必须使用内网TOS（免费流量）
            if (draftUrl.contains("jianying-assistant/drafts/")) {
                String objectKey;
                if (draftUrl.contains("aigcview.cn") && draftUrl.contains("/sys/common/")) {
                    // 系统URL：提取文件路径
                    objectKey = extractFilePathFromUrl(draftUrl);
                } else {
                    // TOS URL：提取对象键
                    objectKey = extractObjectKeyFromUrl(draftUrl);
                }

                // 使用TOS服务上传（覆盖原文件）
                String jsonString = com.alibaba.fastjson.JSON.toJSONString(updatedDraft,
                    com.alibaba.fastjson.serializer.SerializerFeature.WriteMapNullValue);
                
                tosService.uploadDraftFileWithKey(jsonString, objectKey);
                log.info("草稿文件覆盖保存成功: {}", objectKey);
            } else {
                throw new RuntimeException("不支持覆盖保存非剪映草稿文件: " + draftUrl);
            }

        } catch (Exception e) {
            log.error("覆盖保存草稿文件失败: {}", draftUrl, e);
            throw new RuntimeException("覆盖保存草稿文件失败: " + e.getMessage(), e);
        }
    }

    /**
     * 提取或创建统一的文件夹ID
     */
    public String extractOrCreateUnifiedFolderId(JSONObject draft) {
        try {
            // 第1步：尝试从现有音频素材中提取文件夹ID
            JSONObject materials = draft.getJSONObject("materials");
            if (materials != null) {
                com.alibaba.fastjson.JSONArray audios = materials.getJSONArray("audios");
                if (audios != null && audios.size() > 0) {
                    JSONObject firstAudio = audios.getJSONObject(0);
                    String path = firstAudio.getString("path");
                    if (path != null && path.contains("##_draftpath_placeholder_")) {
                        // 提取文件夹ID：##_draftpath_placeholder_xxx_##\folderId\filename
                        String[] parts = path.split("\\\\");
                        if (parts.length >= 2) {
                            String folderId = parts[1]; // 第二部分是文件夹ID
                            log.info("从现有音频素材中提取文件夹ID: {}", folderId);
                            return folderId;
                        }
                    }
                }

                // 第2步：尝试从现有视频素材中提取文件夹ID
                com.alibaba.fastjson.JSONArray videos = materials.getJSONArray("videos");
                if (videos != null && videos.size() > 0) {
                    JSONObject firstVideo = videos.getJSONObject(0);
                    String path = firstVideo.getString("path");
                    if (path != null && path.contains("##_draftpath_placeholder_")) {
                        String[] parts = path.split("\\\\");
                        if (parts.length >= 2) {
                            String folderId = parts[1];
                            log.info("从现有视频素材中提取文件夹ID: {}", folderId);
                            return folderId;
                        }
                    }
                }
            }

            // 第3步：如果没有现有素材，创建新的文件夹ID
            String newFolderId = java.util.UUID.randomUUID().toString();
            log.info("创建新的统一文件夹ID: {}", newFolderId);
            return newFolderId;

        } catch (Exception e) {
            log.warn("提取文件夹ID失败，创建新的: {}", e.getMessage());
            return java.util.UUID.randomUUID().toString();
        }
    }
}
