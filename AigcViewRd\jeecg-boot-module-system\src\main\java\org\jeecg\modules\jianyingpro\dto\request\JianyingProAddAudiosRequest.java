package org.jeecg.modules.jianyingpro.dto.request;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 一体化音频添加请求
 * 合并 audio_infos + add_audios 的参数
 * 
 * <AUTHOR>
 * @since 2025-01-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class JianyingProAddAudiosRequest extends BaseJianyingProRequest {
    
    // ========== 核心参数 ==========
    @ApiModelProperty(value = "草稿地址，使用create_draft输出的draft_url即可（必填）", required = true,
                     example = "https://example.com/draft/123")
    @NotBlank(message = "draft_url不能为空")
    @JsonProperty("draft_url")
    private String draftUrl;

    @ApiModelProperty(value = "音频列表（必填）", required = true,
                     example = "[\"https://example.com/audio1.mp3\", \"https://example.com/audio2.mp3\"]")
    @NotEmpty(message = "mp3_urls不能为空")
    @JsonProperty("mp3_urls")
    private List<String> mp3Urls;

    @ApiModelProperty(value = "时间线（必填）", required = true,
                     example = "[{\"start\": 0, \"end\": 4612000}]")
    @NotEmpty(message = "timelines不能为空")
    @JsonProperty("timelines")
    private List<JSONObject> timelines;

    // ========== 来自audio_infos的可选参数 ==========
    @ApiModelProperty(value = "特效音，eg：教堂，默认无", example = "教堂")
    @JsonProperty("audio_effect")
    private String audioEffect;

    @ApiModelProperty(value = "音量大小，0-10，默认1", example = "1.0")
    @JsonProperty("volume")
    private Double volume;
    
    @Override
    public String getSummary() {
        return "JianyingProAddAudiosRequest{" +
               "draftUrl=" + (draftUrl != null && draftUrl.length() > 50 ?
                            draftUrl.substring(0, 50) + "***" : draftUrl) +
               ", mp3UrlsCount=" + (mp3Urls != null ? mp3Urls.size() : 0) +
               ", timelinesCount=" + (timelines != null ? timelines.size() : 0) +
               ", audioEffect=" + audioEffect +
               ", volume=" + volume +
               "}";
    }
    
    @Override
    public void validate() {
        super.validate();
        
        // mp3_urls是必填的（复制自稳定版audio_infos要求）
        if (mp3Urls == null || mp3Urls.isEmpty()) {
            throw new IllegalArgumentException(
                "参数不完整：mp3_urls不能为空"
            );
        }

        // timelines是必填的（复制自稳定版audio_infos要求）
        if (timelines == null || timelines.isEmpty()) {
            throw new IllegalArgumentException(
                "参数不完整：timelines不能为空"
            );
        }
        
        // 验证mp3_urls中不能有空值
        for (String url : mp3Urls) {
            if (url == null || url.trim().isEmpty()) {
                throw new IllegalArgumentException(
                    "mp3_urls 中不能包含空的URL"
                );
            }
        }
        
        // 验证volume范围
        if (volume != null && (volume < 0 || volume > 10)) {
            throw new IllegalArgumentException(
                "volume 参数值必须在 0-10 之间，当前值: " + volume
            );
        }
    }
}
