package org.jeecg.modules.jianyingpro.service;

import com.alibaba.fastjson.JSONObject;
import org.jeecg.modules.jianyingpro.dto.request.*;

/**
 * 超级剪映小助手服务接口
 * 提供8个一体化操作接口，整合原有功能
 * 
 * <AUTHOR>
 * @since 2025-01-01
 */
public interface JianyingProService {
    
    /**
     * 一体化音频添加
     * 合并 audio_infos + add_audios 功能
     * 
     * @param request 音频添加请求
     * @return 操作结果
     */
    JSONObject addAudios(JianyingProAddAudiosRequest request);
    
    /**
     * 一体化视频添加
     * 合并 video_infos + add_videos 功能
     * 
     * @param request 视频添加请求
     * @return 操作结果
     */
    JSONObject addVideos(JianyingProAddVideosRequest request);
    
    /**
     * 一体化图片添加
     * 合并 imgs_infos + add_images 功能
     * 
     * @param request 图片添加请求
     * @return 操作结果
     */
    JSONObject addImages(JianyingProAddImagesRequest request);
    
    /**
     * 一体化字幕添加
     * 合并 caption_infos + add_captions 功能
     * 
     * @param request 字幕添加请求
     * @return 操作结果
     */
    JSONObject addCaptions(JianyingProAddCaptionsRequest request);
    
    /**
     * 一体化特效添加
     * 合并 effect_infos + add_effects 功能
     * 
     * @param request 特效添加请求
     * @return 操作结果
     */
    JSONObject addEffects(JianyingProAddEffectsRequest request);
    
    /**
     * 一体化关键帧添加
     * 合并 keyframes_infos + add_keyframes 功能
     * 
     * @param request 关键帧添加请求
     * @return 操作结果
     */
    JSONObject addKeyframes(JianyingProAddKeyframesRequest request);
    
    /**
     * 智能时间线生成
     * 合并 timelines + audio_timelines 功能，支持智能模式识别
     * 
     * @param request 时间线生成请求
     * @return 操作结果
     */
    JSONObject generateTimelines(JianyingProTimelinesRequest request);
    
    /**
     * 智能数据转换
     * 支持同时输入多种数据类型，系统自动进行所有可能的转换
     *
     * @param request 数据转换请求
     * @return 操作结果
     */
    JSONObject dataConversion(JianyingProDataConversionRequest request);
}
