<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.demo.userrecord.mapper.AicgUserRecordMapper">

    <!-- 查询用户交易记录 -->
    <select id="getUserTransactions" parameterType="String" resultType="org.jeecg.modules.demo.userrecord.entity.AicgUserRecord">
        SELECT * FROM aicg_user_transaction
        WHERE user_id = #{userId}
        ORDER BY transaction_time DESC
    </select>

    <!-- 查询用户交易统计 -->
    <select id="getUserTransactionStats" parameterType="String" resultType="java.util.Map">
        SELECT
            SUM(CASE WHEN transaction_type = 1 THEN amount ELSE 0 END) as total_consumption,
            SUM(CASE WHEN transaction_type = 2 THEN amount ELSE 0 END) as total_recharge,
            COUNT(CASE WHEN transaction_type = 1 THEN 1 ELSE NULL END) as consumption_count,
            COUNT(CASE WHEN transaction_type = 2 THEN 1 ELSE NULL END) as recharge_count,
            MAX(transaction_time) as last_transaction_time
        FROM aicg_user_transaction
        WHERE user_id = #{userId}
    </select>

    <!-- 查询指定时间范围内的交易记录 -->
    <select id="getTransactionsByDateRange" resultType="org.jeecg.modules.demo.userrecord.entity.AicgUserRecord">
        SELECT * FROM aicg_user_transaction
        WHERE user_id = #{userId}
        AND transaction_time BETWEEN #{startDate} AND #{endDate}
        ORDER BY transaction_time DESC
    </select>

    <!-- 查询交易类型统计 -->
    <select id="getTransactionTypeStats" resultType="java.util.Map">
        SELECT
            transaction_type,
            COUNT(*) as count,
            SUM(amount) as total_amount,
            AVG(amount) as avg_amount,
            MAX(amount) as max_amount,
            MIN(amount) as min_amount
        FROM aicg_user_transaction
        WHERE user_id = #{userId}
        GROUP BY transaction_type
    </select>

</mapper>