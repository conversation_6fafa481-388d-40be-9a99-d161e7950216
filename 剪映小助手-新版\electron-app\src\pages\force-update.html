<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>必须更新 - 智界剪映小助手</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            overflow: hidden;
        }

        .container {
            display: flex;
            flex-direction: column;
            height: 100vh;
            padding: 30px;
            text-align: center;
        }

        .header {
            margin-bottom: 30px;
        }

        .icon {
            width: 80px;
            height: 80px;
            margin: 0 auto 20px;
            background: #ff6b6b;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 40px;
            color: white;
            box-shadow: 0 10px 30px rgba(255, 107, 107, 0.3);
        }

        .title {
            font-size: 28px;
            font-weight: 600;
            color: white;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .subtitle {
            font-size: 16px;
            color: rgba(255, 255, 255, 0.9);
            line-height: 1.5;
        }

        .content {
            flex: 1;
            background: white;
            border-radius: 20px;
            padding: 30px;
            margin: 20px 0;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
            display: flex;
            flex-direction: column;
        }

        .version-info {
            margin-bottom: 25px;
        }

        .version-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .version-row:last-child {
            border-bottom: none;
        }

        .version-label {
            font-weight: 500;
            color: #666;
        }

        .version-value {
            font-weight: 600;
            color: #333;
        }

        .current-version {
            color: #ff6b6b;
        }

        .latest-version {
            color: #4ecdc4;
        }

        .update-content {
            flex: 1;
            text-align: left;
        }

        .update-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }

        .update-title::before {
            content: "📋";
            margin-right: 8px;
        }

        .update-text {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 20px;
            line-height: 1.6;
            color: #555;
            border-left: 4px solid #4ecdc4;
            max-height: 150px;
            overflow-y: auto;
        }

        .actions {
            margin-top: 25px;
            display: flex;
            gap: 15px;
            justify-content: center;
        }

        .btn {
            padding: 12px 30px;
            border: none;
            border-radius: 25px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            min-width: 120px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(78, 205, 196, 0.3);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(78, 205, 196, 0.4);
        }

        .btn-secondary {
            background: #f8f9fa;
            color: #666;
            border: 2px solid #e9ecef;
        }

        .btn-secondary:hover {
            background: #e9ecef;
            border-color: #dee2e6;
        }

        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            color: #856404;
            display: flex;
            align-items: center;
        }

        .warning::before {
            content: "⚠️";
            margin-right: 10px;
            font-size: 18px;
        }

        .loading {
            display: none;
            align-items: center;
            justify-content: center;
            margin-top: 20px;
        }

        .spinner {
            width: 20px;
            height: 20px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #4ecdc4;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* 🚀 更新弹窗内的下载进度样式 */
        .update-download-progress {
            margin-top: 20px;
            padding: 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
        }

        .update-download-progress.hidden {
            display: none;
        }

        .update-download-progress h4 {
            margin: 0 0 15px 0;
            color: white;
            font-size: 18px;
            font-weight: 600;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .progress-info {
            display: flex;
            flex-direction: column;
            gap: 8px;
            margin-bottom: 15px;
        }

        .progress-info > div {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .progress-label {
            font-weight: 500;
            color: rgba(255, 255, 255, 0.8);
            font-size: 14px;
        }

        .progress-value {
            color: white;
            font-size: 14px;
            font-weight: 600;
        }

        .progress-bar-container {
            margin: 15px 0;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .progress-bar {
            flex: 1;
            height: 10px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 5px;
            overflow: hidden;
            position: relative;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4ecdc4 0%, #44a08d 100%);
            border-radius: 5px;
            transition: width 0.3s ease;
            position: relative;
        }

        .progress-fill::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, transparent 0%, rgba(255,255,255,0.3) 50%, transparent 100%);
            animation: shimmer 2s infinite;
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        .progress-percent {
            font-size: 14px;
            font-weight: 700;
            color: white;
            min-width: 45px;
            text-align: right;
        }

        .progress-details {
            display: flex;
            justify-content: space-between;
            gap: 20px;
            margin-top: 10px;
        }

        .progress-details > div {
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .progress-speed .progress-value {
            color: #4ecdc4;
            font-weight: 700;
        }

        .progress-size .progress-value {
            color: #a8e6cf;
            font-weight: 700;
        }
    </style>
</head>
<body>
    <div class="container fade-in">
        <div class="header">
            <div class="icon">🚀</div>
            <h1 class="title">发现重要更新</h1>
            <p class="subtitle">为了您的使用安全和体验，请立即更新到最新版本</p>
        </div>

        <div class="content">
            <div class="warning">
                此更新包含重要的安全修复和功能改进，必须更新后才能继续使用
            </div>

            <div class="version-info">
                <div class="version-row">
                    <span class="version-label">当前版本</span>
                    <span class="version-value current-version" id="currentVersion">-</span>
                </div>
                <div class="version-row">
                    <span class="version-label">最新版本</span>
                    <span class="version-value latest-version" id="latestVersion">-</span>
                </div>
                <div class="version-row">
                    <span class="version-label">发布日期</span>
                    <span class="version-value" id="releaseDate">-</span>
                </div>
            </div>

            <div class="update-content">
                <div class="update-title">更新内容</div>
                <div class="update-text" id="updateContent">
                    正在加载更新信息...
                </div>
            </div>

            <!-- 🚀 下载进度区域 -->
            <div id="update-download-progress" class="update-download-progress hidden">
                <h4>正在下载更新...</h4>
                <div class="progress-info">
                    <div class="progress-file">
                        <span class="progress-label">文件：</span>
                        <span id="update-progress-filename" class="progress-value">剪映小助手-v1.0.1.exe</span>
                    </div>
                    <div class="progress-status">
                        <span class="progress-label">状态：</span>
                        <span id="update-progress-status" class="progress-value">准备下载...</span>
                    </div>
                </div>

                <div class="progress-bar-container">
                    <div class="progress-bar">
                        <div id="update-progress-fill" class="progress-fill" style="width: 0%"></div>
                    </div>
                    <div class="progress-text">
                        <span id="update-progress-percent" class="progress-percent">0%</span>
                    </div>
                </div>

                <div class="progress-details">
                    <div class="progress-speed">
                        <span class="progress-label">速度：</span>
                        <span id="update-progress-speed" class="progress-value">0 KB/s</span>
                    </div>
                    <div class="progress-size">
                        <span class="progress-label">大小：</span>
                        <span id="update-progress-size" class="progress-value">0 MB / 0 MB</span>
                    </div>
                </div>
            </div>

            <div class="actions">
                <button class="btn btn-primary" id="updateBtn">
                    立即更新
                </button>
                <button class="btn btn-secondary" id="exitBtn">
                    退出应用
                </button>
            </div>

            <div class="loading" id="loading">
                <div class="spinner"></div>
                <span>正在打开下载页面...</span>
            </div>
        </div>
    </div>

    <script>
        const { ipcRenderer } = require('electron');

        let versionInfo = null;

        // 接收版本信息
        ipcRenderer.on('version-info', (event, data) => {
            versionInfo = data;
            updateUI(data);
        });

        // 更新界面
        function updateUI(data) {
            document.getElementById('currentVersion').textContent = data.currentVersion || '-';
            document.getElementById('latestVersion').textContent = data.latestVersion || '-';
            document.getElementById('releaseDate').textContent = formatDate(data.releaseDate) || '-';
            document.getElementById('updateContent').textContent = data.updateContent || '暂无更新说明';
        }

        // 格式化日期
        function formatDate(dateString) {
            if (!dateString) return '-';
            try {
                const date = new Date(dateString);
                return date.toLocaleDateString('zh-CN', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit'
                });
            } catch (error) {
                return dateString;
            }
        }

        // 立即更新按钮
        document.getElementById('updateBtn').addEventListener('click', async () => {
            if (versionInfo && versionInfo.downloadUrl) {
                // 🚀 显示弹窗内下载进度
                showInModalProgress();
                setupProgressListeners();

                try {
                    // 🚀 启动应用内下载
                    console.log('🚀 开始应用内下载更新文件:', versionInfo.downloadUrl);
                    const result = await ipcRenderer.invoke('download-update-file', {
                        downloadUrl: versionInfo.downloadUrl,
                        version: versionInfo.versionNumber,
                        fileName: generateFileName(versionInfo.versionNumber)
                    });

                    // 🔧 [FIX] 移除重复处理 - 下载完成由事件监听器处理
                    if (!result.success) {
                        // 下载失败，回退到浏览器下载
                        console.error('应用内下载失败，回退到浏览器下载:', result.error);
                        hideInModalProgress();
                        alert('应用内下载失败，将使用浏览器下载');
                        ipcRenderer.send('version-check-user-choice', 'update', versionInfo);
                    }
                } catch (error) {
                    console.error('下载更新文件失败:', error);
                    hideInModalProgress();
                    alert('下载失败，将使用浏览器下载');
                    ipcRenderer.send('version-check-user-choice', 'update', versionInfo);
                }
            } else {
                alert('下载链接不可用，请联系技术支持');
            }
        });

        // 退出应用按钮
        document.getElementById('exitBtn').addEventListener('click', () => {
            ipcRenderer.send('version-check-user-choice', 'exit', versionInfo);
        });

        // 🚀 在弹窗内显示下载进度
        function showInModalProgress() {
            const updateContent = document.querySelector('.update-content');
            const progressArea = document.getElementById('update-download-progress');
            const actions = document.querySelector('.actions');
            const loading = document.getElementById('loading');

            if (updateContent) updateContent.style.display = 'none';
            if (progressArea) progressArea.classList.remove('hidden');
            if (actions) actions.style.display = 'none';
            if (loading) loading.style.display = 'none';

            console.log('🚀 弹窗内下载进度区域已显示');
        }

        // 🚀 隐藏弹窗内下载进度
        function hideInModalProgress() {
            const updateContent = document.querySelector('.update-content');
            const progressArea = document.getElementById('update-download-progress');
            const actions = document.querySelector('.actions');

            if (updateContent) updateContent.style.display = 'block';
            if (progressArea) progressArea.classList.add('hidden');
            if (actions) actions.style.display = 'flex';

            console.log('🚀 弹窗内下载进度区域已隐藏');
        }

        // 🚀 设置下载进度监听器
        function setupProgressListeners() {
            // 监听下载进度事件
            ipcRenderer.on('download-progress', (event, data) => {
                updateInModalProgress(data);
            });

            // 监听下载完成事件
            ipcRenderer.on('download-complete', (event, data) => {
                handleDownloadComplete(data);
            });

            // 监听下载错误事件
            ipcRenderer.on('download-error', (event, error) => {
                handleDownloadError(error);
            });

            console.log('🚀 下载进度监听器已设置');
        }

        // 🚀 更新弹窗内的下载进度
        function updateInModalProgress(data) {
            console.log('🚀 更新弹窗内进度:', data);

            // 更新文件名
            const filenameEl = document.getElementById('update-progress-filename');
            if (filenameEl && data.fileName) {
                filenameEl.textContent = data.fileName;
            }

            // 更新状态
            const statusEl = document.getElementById('update-progress-status');
            if (statusEl) {
                if (data.status === 'downloading') {
                    statusEl.textContent = '正在下载...';
                } else if (data.status === 'completed') {
                    statusEl.textContent = '下载完成';
                } else {
                    statusEl.textContent = data.status || '下载中...';
                }
            }

            // 更新进度条
            const progressFill = document.getElementById('update-progress-fill');
            const progressPercent = document.getElementById('update-progress-percent');
            if (progressFill && progressPercent && data.progress !== undefined) {
                const percent = Math.round(data.progress);
                progressFill.style.width = `${percent}%`;
                progressPercent.textContent = `${percent}%`;
            }

            // 更新下载速度
            const speedEl = document.getElementById('update-progress-speed');
            if (speedEl && data.speed) {
                speedEl.textContent = data.speed;
            }

            // 更新文件大小
            const sizeEl = document.getElementById('update-progress-size');
            if (sizeEl && data.downloaded && data.total) {
                sizeEl.textContent = `${data.downloaded} / ${data.total}`;
            }
        }

        // 🚀 处理下载完成
        function handleDownloadComplete(data) {
            console.log('🚀 下载完成:', data);

            // 🔧 [FIX] 防重复处理机制
            if (window.downloadCompleted) {
                console.log('🔧 下载已完成，跳过重复处理');
                return;
            }
            window.downloadCompleted = true;

            // 更新状态为完成
            updateInModalProgress({
                status: 'completed',
                progress: 100
            });

            // 显示准备安装状态
            setTimeout(() => {
                updateInModalProgress({
                    status: '准备安装...'
                });
            }, 800);

            // 🔧 [FIX] 跳过安装提示，直接安装
            setTimeout(async () => {
                if (data.filePath) {
                    console.log('🚀 下载完成，直接开始安装:', data.filePath);
                    try {
                        // 直接打开安装文件并退出
                        console.log('🚀 正在打开安装文件...');
                        await ipcRenderer.invoke('open-installer', data.filePath);

                        // 延迟一下再退出，确保安装文件能正常打开
                        setTimeout(() => {
                            ipcRenderer.send('version-check-user-choice', 'exit', versionInfo);
                        }, 500);

                    } catch (error) {
                        console.error('🚀 打开安装文件出错:', error);
                        // 即使出错也退出应用
                        ipcRenderer.send('version-check-user-choice', 'exit', versionInfo);
                    }
                }
            }, 1500);
        }

        // 🚀 处理下载错误
        function handleDownloadError(error) {
            console.error('🚀 下载错误:', error);

            // 更新状态为错误
            const statusEl = document.getElementById('update-progress-status');
            if (statusEl) {
                statusEl.textContent = `下载失败: ${error.message || error}`;
                statusEl.style.color = '#dc2626';
            }
        }

        // 🚀 根据系统生成对应的文件名
        function generateFileName(version) {
            const userAgent = navigator.userAgent;
            const platform = navigator.platform;

            let fileName = `剪映小助手-v${version}`;

            if (platform.includes('Win') || userAgent.includes('Windows')) {
                fileName += '.exe';
            } else if (platform.includes('Mac') || userAgent.includes('Mac')) {
                fileName += '.dmg';
            } else if (platform.includes('Linux') || userAgent.includes('Linux')) {
                fileName += '.AppImage';
            } else {
                fileName += '.exe';
            }

            console.log('🚀 生成文件名:', { platform, userAgent, fileName });
            return fileName;
        }

        // 显示/隐藏加载状态
        function showLoading(show) {
            const loading = document.getElementById('loading');
            const actions = document.querySelector('.actions');
            
            if (show) {
                loading.style.display = 'flex';
                actions.style.opacity = '0.5';
                actions.style.pointerEvents = 'none';
            } else {
                loading.style.display = 'none';
                actions.style.opacity = '1';
                actions.style.pointerEvents = 'auto';
            }
        }

        // 阻止右键菜单
        document.addEventListener('contextmenu', (e) => {
            e.preventDefault();
        });

        // 阻止拖拽
        document.addEventListener('dragover', (e) => {
            e.preventDefault();
        });

        document.addEventListener('drop', (e) => {
            e.preventDefault();
        });
    </script>
</body>
</html>
