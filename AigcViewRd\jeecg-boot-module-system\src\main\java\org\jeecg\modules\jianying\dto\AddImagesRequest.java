package org.jeecg.modules.jianying.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

/**
 * 批量添加图片请求参数
 *
 * <AUTHOR>
 * @date 2025-01-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AddImagesRequest extends BaseJianyingRequest {

    @ApiModelProperty(value = "草稿地址，使用createDraft_zj输出的draft_url即可（必填）", required = true,
                     example = "https://aigcview-plub.tos-s-cn-guangzhou.volces.com/jianying-assistant/drafts/2025/01/06/draft_1736152800000_1024/draft_content.json")
    @NotBlank(message = "draft_url不能为空")
    @JsonProperty("draft_url")
    private String draftUrl;

    @ApiModelProperty(value = "图片透明度，范围0-1（可选）", example = "0.5")
    @JsonProperty("alpha")
    private Double alpha;

    @ApiModelProperty(value = "图片信息JSON字符串（必填）", required = true,
                     example = "[{\"image_url\":\"https://example.com/img1.jpg\",\"duration\":5000000,\"start\":0,\"end\":5000000,\"in_animation\":\"淡入\",\"out_animation\":\"淡出\",\"group_animation\":\"无\"},{\"image_url\":\"https://example.com/img2.jpg\",\"duration\":5000000,\"start\":5000000,\"end\":10000000,\"in_animation\":\"展开\",\"out_animation\":\"收缩\",\"group_animation\":\"无\"},{\"image_url\":\"https://example.com/img3.jpg\",\"duration\":5000000,\"start\":10000000,\"end\":15000000,\"in_animation\":\"闪现\",\"out_animation\":\"消失\",\"group_animation\":\"无\"}]")
    @NotBlank(message = "image_infos不能为空")
    @JsonProperty("image_infos")
    private String imageInfos;

    @ApiModelProperty(value = "X轴缩放比例（可选）", example = "0.9")
    @JsonProperty("scale_x")
    private Double scaleX;

    @ApiModelProperty(value = "Y轴缩放比例（可选）", example = "0.9")
    @JsonProperty("scale_y")
    private Double scaleY;

    @ApiModelProperty(value = "X轴移动位置（可选）", example = "100")
    @JsonProperty("transform_x")
    private Double transformX;

    @ApiModelProperty(value = "Y轴移动位置（可选）", example = "-100")
    @JsonProperty("transform_y")
    private Double transformY;

    @ApiModelProperty(value = "转场效果名称（可选），比如：水墨", example = "淡入淡出")
    @JsonProperty("transition")
    private String transition;

    @ApiModelProperty(value = "转场效果时长（可选）", example = "1500000")
    @JsonProperty("transition_duration")
    private Integer transitionDuration;

    @Override
    public String getSummary() {
        return "AddImagesRequest{" +
               "accessKey=" + (getAccessKey() != null && getAccessKey().length() > 10 ?
                              getAccessKey().substring(0, 10) + "***" : "null") +
               ", draftUrl=" + (draftUrl != null && draftUrl.length() > 30 ?
                               draftUrl.substring(0, 30) + "***" : draftUrl) +
               ", alpha=" + alpha +
               ", scaleX=" + scaleX +
               ", scaleY=" + scaleY +
               ", transformX=" + transformX +
               ", transformY=" + transformY +
               ", transition=" + transition +
               ", transitionDuration=" + transitionDuration +
               "}";
    }
}
