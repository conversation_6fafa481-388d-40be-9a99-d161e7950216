{"openapi": "3.0.0", "info": {"title": "剪映小助手_智界数据箱 - 视频数据生成器", "description": "根据时间线制作视频数据", "version": "1.0.0"}, "servers": [{"url": "https://www.aigcview.com"}], "paths": {"/jeecg-boot/api/jianying/video_infos": {"post": {"summary": "生成视频数据", "description": "根据时间线制作视频数据", "operationId": "videoInfos_zj", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"access_key": {"type": "string", "description": "访问密钥（必填）", "example": "JianyingAPI_2025_SecureAccess_AigcView", "default": "JianyingAPI_2025_SecureAccess_AigcView"}, "zj_mask": {"type": "string", "description": "视频蒙版，可填写值：圆形，矩形，爱心，星形", "enum": ["圆形", "矩形", "爱心", "星形"], "example": "圆形"}, "zj_video_urls": {"type": "array", "description": "视频列表（必填）", "items": {"type": "string"}, "example": ["https://example.com/video1.mp4", "https://example.com/video2.mp4"]}, "zj_height": {"type": "integer", "description": "视频高度", "example": 1080}, "zj_timelines": {"type": "array", "description": "时间节点，只接收结构：[{\"start\":0,\"end\":4612}]，一般从audio_timeline节点的输出获取（必填）", "items": {"type": "object", "properties": {"start": {"type": "integer"}, "end": {"type": "integer"}}}, "example": [{"start": 0, "end": 4612000}]}, "zj_transition": {"type": "string", "description": "转场", "example": "淡入淡出"}, "zj_transition_duration": {"type": "integer", "description": "转场的时长", "example": 1000000}, "zj_volume": {"type": "number", "description": "音量大小，0-10，默认1", "minimum": 0, "maximum": 10, "example": 1.0}, "zj_width": {"type": "integer", "description": "视频宽度", "example": 1920}}, "required": ["access_key", "zj_video_urls", "zj_timelines"], "additionalProperties": false}}}}, "responses": {"200": {"description": "成功生成视频数据", "content": {"application/json": {"schema": {"type": "object", "properties": {"infos": {"type": "string", "description": "视频信息数组（JSON格式字符串），格式：[{\"video_url\":\"...\",\"width\":1920,\"height\":1080,\"start\":0,\"end\":8256000,\"duration\":8256000,\"mask\":\"2\",\"volume\":5,\"transition\":\"3\",\"transition_duration\":4}]"}}, "required": ["infos"]}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "description": "操作是否成功", "example": false}, "error": {"type": "string", "description": "错误信息"}, "code": {"type": "string", "description": "错误代码"}}, "required": ["success", "error"]}}}}, "500": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "description": "操作是否成功", "example": false}, "error": {"type": "string", "description": "错误信息"}, "code": {"type": "string", "description": "错误代码"}}, "required": ["success", "error"]}}}}}}}}}