package org.jeecg.modules.api.dto;

import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import java.math.BigDecimal;

/**
 * @Description: 插件验证结果
 * @Author: jeecg-boot
 * @Date: 2025-06-14
 * @Version: V1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PluginVerifyResult {
    
    /**
     * 验证是否成功
     */
    private boolean success;
    
    /**
     * 错误消息
     */
    private String errorMessage;
    
    /**
     * 插件名称
     */
    private String pluginName;
    
    /**
     * 需要的金额
     */
    private BigDecimal needAmount;
    
    /**
     * 用户当前余额
     */
    private BigDecimal currentBalance;
    
    /**
     * 扣费后余额
     */
    private BigDecimal balanceAfter;
    
    /**
     * 插件ID
     */
    private String pluginId;
    
    /**
     * 创建成功结果
     * @param pluginName 插件名称
     * @param needAmount 需要金额
     * @param currentBalance 当前余额
     * @param balanceAfter 扣费后余额
     * @param pluginId 插件ID
     * @return 成功结果
     */
    public static PluginVerifyResult success(String pluginName, BigDecimal needAmount,
                                           BigDecimal currentBalance, BigDecimal balanceAfter, String pluginId) {
        PluginVerifyResult result = new PluginVerifyResult();
        result.setSuccess(true);
        result.setPluginName(pluginName);
        result.setNeedAmount(needAmount);
        result.setCurrentBalance(currentBalance);
        result.setBalanceAfter(balanceAfter);
        result.setPluginId(pluginId);
        return result;
    }
    
    /**
     * 创建失败结果
     * @param errorMessage 错误消息
     * @return 失败结果
     */
    public static PluginVerifyResult failure(String errorMessage) {
        PluginVerifyResult result = new PluginVerifyResult();
        result.setSuccess(false);
        result.setErrorMessage(errorMessage);
        return result;
    }
    
    /**
     * 创建余额不足的失败结果
     * @param pluginName 插件名称
     * @param needAmount 需要金额
     * @param currentBalance 当前余额
     * @return 失败结果
     */
    public static PluginVerifyResult insufficientBalance(String pluginName, BigDecimal needAmount, BigDecimal currentBalance) {
        PluginVerifyResult result = new PluginVerifyResult();
        result.setSuccess(false);
        result.setPluginName(pluginName);
        result.setNeedAmount(needAmount);
        result.setCurrentBalance(currentBalance);
        result.setErrorMessage(String.format("余额不足，插件 %s 需要 %.2f 元，当前余额 %.2f 元",
                                            pluginName, needAmount, currentBalance));
        return result;
    }
}
