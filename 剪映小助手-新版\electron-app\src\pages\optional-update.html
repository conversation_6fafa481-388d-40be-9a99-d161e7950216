<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>发现新版本 - 智界剪映小助手</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            color: #333;
            overflow: hidden;
        }

        .container {
            display: flex;
            flex-direction: column;
            height: 100vh;
            padding: 25px;
            text-align: center;
        }

        .header {
            margin-bottom: 25px;
        }

        .icon {
            width: 70px;
            height: 70px;
            margin: 0 auto 15px;
            background: #00b894;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 35px;
            color: white;
            box-shadow: 0 8px 25px rgba(0, 184, 148, 0.3);
        }

        .title {
            font-size: 24px;
            font-weight: 600;
            color: white;
            margin-bottom: 8px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .subtitle {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.9);
            line-height: 1.4;
        }

        .content {
            flex: 1;
            background: white;
            border-radius: 16px;
            padding: 25px;
            margin: 15px 0;
            box-shadow: 0 15px 50px rgba(0, 0, 0, 0.1);
            display: flex;
            flex-direction: column;
        }

        .version-info {
            margin-bottom: 20px;
        }

        .version-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .version-row:last-child {
            border-bottom: none;
        }

        .version-label {
            font-weight: 500;
            color: #666;
            font-size: 14px;
        }

        .version-value {
            font-weight: 600;
            color: #333;
            font-size: 14px;
        }

        .current-version {
            color: #74b9ff;
        }

        .latest-version {
            color: #00b894;
        }

        .update-content {
            flex: 1;
            text-align: left;
        }

        .update-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-bottom: 12px;
            display: flex;
            align-items: center;
        }

        .update-title::before {
            content: "✨";
            margin-right: 6px;
        }

        .update-text {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            line-height: 1.5;
            color: #555;
            border-left: 3px solid #00b894;
            max-height: 120px;
            overflow-y: auto;
            font-size: 14px;
        }

        .actions {
            margin-top: 20px;
            display: flex;
            gap: 10px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            min-width: 100px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #00b894 0%, #00a085 100%);
            color: white;
            box-shadow: 0 3px 12px rgba(0, 184, 148, 0.3);
        }

        .btn-primary:hover {
            transform: translateY(-1px);
            box-shadow: 0 5px 15px rgba(0, 184, 148, 0.4);
        }

        .btn-secondary {
            background: #f8f9fa;
            color: #666;
            border: 1px solid #e9ecef;
        }

        .btn-secondary:hover {
            background: #e9ecef;
            border-color: #dee2e6;
        }

        .btn-text {
            background: transparent;
            color: #74b9ff;
            border: none;
            text-decoration: underline;
            min-width: auto;
            padding: 5px 10px;
        }

        .btn-text:hover {
            color: #0984e3;
        }

        .info-tip {
            background: #e3f2fd;
            border: 1px solid #bbdefb;
            border-radius: 6px;
            padding: 12px;
            margin-bottom: 15px;
            color: #1565c0;
            display: flex;
            align-items: center;
            font-size: 13px;
        }

        .info-tip::before {
            content: "💡";
            margin-right: 8px;
        }

        .loading {
            display: none;
            align-items: center;
            justify-content: center;
            margin-top: 15px;
        }

        .spinner {
            width: 16px;
            height: 16px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #00b894;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 8px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .fade-in {
            animation: fadeIn 0.4s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(15px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body>
    <div class="container fade-in">
        <div class="header">
            <div class="icon">🎉</div>
            <h1 class="title">发现新版本</h1>
            <p class="subtitle">有新的功能和改进等待您体验</p>
        </div>

        <div class="content">
            <div class="info-tip">
                这是一个可选更新，您可以选择现在更新或稍后更新
            </div>

            <div class="version-info">
                <div class="version-row">
                    <span class="version-label">当前版本</span>
                    <span class="version-value current-version" id="currentVersion">-</span>
                </div>
                <div class="version-row">
                    <span class="version-label">最新版本</span>
                    <span class="version-value latest-version" id="latestVersion">-</span>
                </div>
                <div class="version-row">
                    <span class="version-label">发布日期</span>
                    <span class="version-value" id="releaseDate">-</span>
                </div>
            </div>

            <div class="update-content">
                <div class="update-title">更新内容</div>
                <div class="update-text" id="updateContent">
                    正在加载更新信息...
                </div>
            </div>

            <div class="actions">
                <button class="btn btn-primary" id="updateBtn">
                    立即更新
                </button>
                <button class="btn btn-secondary" id="laterBtn">
                    稍后提醒
                </button>
                <button class="btn btn-text" id="skipBtn">
                    跳过此版本
                </button>
            </div>

            <div class="loading" id="loading">
                <div class="spinner"></div>
                <span>正在打开下载页面...</span>
            </div>
        </div>
    </div>

    <script>
        const { ipcRenderer } = require('electron');

        let versionInfo = null;

        // 接收版本信息
        ipcRenderer.on('version-info', (event, data) => {
            versionInfo = data;
            updateUI(data);
        });

        // 更新界面
        function updateUI(data) {
            document.getElementById('currentVersion').textContent = data.currentVersion || '-';
            document.getElementById('latestVersion').textContent = data.latestVersion || '-';
            document.getElementById('releaseDate').textContent = formatDate(data.releaseDate) || '-';
            document.getElementById('updateContent').textContent = data.updateContent || '暂无更新说明';
        }

        // 格式化日期
        function formatDate(dateString) {
            if (!dateString) return '-';
            try {
                const date = new Date(dateString);
                return date.toLocaleDateString('zh-CN', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit'
                });
            } catch (error) {
                return dateString;
            }
        }

        // 立即更新按钮
        document.getElementById('updateBtn').addEventListener('click', async () => {
            if (versionInfo && versionInfo.downloadUrl) {
                showLoading(true);

                try {
                    // 🚀 启动应用内下载
                    console.log('🚀 开始应用内下载更新文件:', versionInfo.downloadUrl);
                    const result = await ipcRenderer.invoke('download-update-file', {
                        downloadUrl: versionInfo.downloadUrl,
                        version: versionInfo.versionNumber,
                        fileName: generateFileName(versionInfo.versionNumber)
                    });

                    // 🔧 [FIX] 移除重复处理 - 下载完成由事件监听器处理
                    if (!result.success) {
                        // 下载失败，回退到浏览器下载
                        console.error('应用内下载失败，回退到浏览器下载:', result.error);
                        showLoading(false);
                        alert('应用内下载失败，将使用浏览器下载');
                        ipcRenderer.send('version-check-user-choice', 'update', versionInfo);
                    }
                } catch (error) {
                    console.error('下载更新文件失败:', error);
                    showLoading(false);
                    alert('下载失败，将使用浏览器下载');
                    ipcRenderer.send('version-check-user-choice', 'update', versionInfo);
                }
            } else {
                alert('下载链接不可用，请联系技术支持');
            }
        });

        // 稍后提醒按钮
        document.getElementById('laterBtn').addEventListener('click', () => {
            ipcRenderer.send('version-check-user-choice', 'later', versionInfo);
        });

        // 跳过此版本按钮
        document.getElementById('skipBtn').addEventListener('click', () => {
            if (confirm('确定要跳过此版本吗？您将不会再收到此版本的更新提醒。')) {
                ipcRenderer.send('version-check-user-choice', 'skip', versionInfo);
            }
        });

        // 🚀 根据系统生成对应的文件名
        function generateFileName(version) {
            const userAgent = navigator.userAgent;
            const platform = navigator.platform;

            let fileName = `剪映小助手-v${version}`;

            if (platform.includes('Win') || userAgent.includes('Windows')) {
                fileName += '.exe';
            } else if (platform.includes('Mac') || userAgent.includes('Mac')) {
                fileName += '.dmg';
            } else if (platform.includes('Linux') || userAgent.includes('Linux')) {
                fileName += '.AppImage';
            } else {
                fileName += '.exe';
            }

            console.log('🚀 生成文件名:', { platform, userAgent, fileName });
            return fileName;
        }

        // 显示/隐藏加载状态
        function showLoading(show) {
            const loading = document.getElementById('loading');
            const actions = document.querySelector('.actions');
            
            if (show) {
                loading.style.display = 'flex';
                actions.style.opacity = '0.5';
                actions.style.pointerEvents = 'none';
            } else {
                loading.style.display = 'none';
                actions.style.opacity = '1';
                actions.style.pointerEvents = 'auto';
            }
        }

        // 阻止右键菜单
        document.addEventListener('contextmenu', (e) => {
            e.preventDefault();
        });

        // 阻止拖拽
        document.addEventListener('dragover', (e) => {
            e.preventDefault();
        });

        document.addEventListener('drop', (e) => {
            e.preventDefault();
        });
    </script>
</body>
</html>
