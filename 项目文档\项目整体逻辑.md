# 🎯 智界Aigc项目整体逻辑

## 📊 项目架构总览

```
智界Aigc生态系统
├── 后端系统 (AigcViewRd)
│   ├── 数据管理
│   ├── API接口
│   ├── 业务逻辑
│   └── 安全控制
├── 管理前端 (AigcViewFe/智界Aigc)
│   ├── 管理员界面
│   ├── 数据管理
│   ├── 系统配置
│   └── 统计分析
└── 官网前端 (待开发)
    ├── 用户界面
    ├── 插件市场
    ├── 教育内容
    └── 个人中心
```

## 🔄 系统间关系逻辑

### 数据流向关系
```
后端数据库 ←→ 后端API ←→ 管理前端 (管理员操作)
     ↓
后端数据库 ←→ 后端API ←→ 官网前端 (用户操作)
```

### 用户角色关系
```
系统管理员 → 管理前端 → 管理插件、用户、内容
插件创作者 → 官网前端 → 上传插件、查看收益
普通用户   → 官网前端 → 购买插件、学习教程
```

## 🏗️ 后端系统逻辑 (AigcViewRd)

### 核心功能模块
```
后端系统/
├── 用户管理模块
│   ├── 用户注册登录
│   ├── 用户信息管理
│   ├── API密钥管理
│   └── 权限控制
├── 插件生态模块
│   ├── 插件商城管理
│   ├── 插件创作者管理
│   ├── 插件调用统计
│   └── 收益分成计算
├── 教育内容模块
│   ├── 视频教程管理
│   ├── 讲师信息管理
│   ├── 课程分类管理
│   └── 学习进度跟踪
├── 交易系统模块
│   ├── 账户余额管理
│   ├── 充值扣费记录
│   ├── 兑换码系统
│   └── 交易流水记录
├── 营销功能模块
│   ├── 签到奖励系统
│   ├── 会员订阅系统
│   ├── 分销推广系统
│   └── 客户案例管理
└── 系统管理模块
    ├── 系统配置管理
    ├── 日志记录管理
    ├── 数据统计分析
    └── 安全监控管理
```

### 数据库设计逻辑
```sql
-- 用户体系
sys_user (系统用户基础表)
├── aicg_user_profile (用户扩展信息)
├── aicg_user_transaction (交易记录)
└── aicg_exchange_code (兑换码)

-- 插件生态
aigc_plub_author (插件创作者)
├── aigc_plub_shop (插件商城)
└── aicg_api_log (API调用日志)

-- 教育内容
aigc_video_teacher (视频讲师)
└── aigc_video_tutorial (视频教程)

-- 营销功能 (新增)
aigc_customer_case (客户案例)
aigc_sign_record (签到记录)
aigc_membership (会员订阅)
aigc_affiliate (分销推广)
aigc_website_config (网站配置)
```

### API接口逻辑
```
/jeecg-boot/
├── /sys/                   # 系统管理接口
├── /plubshop/             # 插件商城接口
├── /plubauthor/           # 插件创作者接口
├── /videotutorial/        # 教程中心接口
├── /videoteacher/         # 视频讲师接口
├── /userCenter/           # 个人中心接口
├── /api/                  # 对外API接口
└── /website/              # 官网专用接口 (新增)
    ├── /header            # 页头数据
    ├── /footer            # 页脚数据
    ├── /home              # 首页数据
    ├── /cases             # 客户案例
    ├── /signin            # 签到功能
    ├── /membership        # 会员功能
    └── /affiliate         # 分销功能
```

## 💻 管理前端逻辑 (AigcViewFe/智界Aigc)

### 系统架构逻辑
```
管理前端/
├── 基础框架 (JeecgBoot)
│   ├── Vue.js 2.6.10
│   ├── Ant Design Vue
│   ├── 权限管理系统
│   └── 代码生成器
├── 核心业务模块 (aigcview/)
│   ├── 插件商城管理 (plubshop/)
│   ├── 插件创作者管理 (plubauthor/)
│   ├── 视频教程管理 (videotutorial/)
│   ├── 视频讲师管理 (videoteacher/)
│   └── 个人中心 (usercenter/)
├── 数据分析模块 (dashboard/)
│   ├── 实时监控面板
│   ├── API调用统计
│   ├── 用户行为分析
│   └── 收益统计报表
└── 系统管理模块 (system/)
    ├── 用户权限管理
    ├── 角色菜单管理
    ├── 字典数据管理
    └── 系统日志管理
```

### 功能使用逻辑
```
管理员登录 → 权限验证 → 功能菜单 → 具体操作
├── 插件管理 → 审核插件 → 设置价格 → 上架下架
├── 创作者管理 → 审核资质 → 查看统计 → 收益结算
├── 教程管理 → 上传视频 → 分类标签 → 发布管理
├── 用户管理 → 查看信息 → 余额操作 → 权限设置
└── 数据分析 → 实时监控 → 统计报表 → 趋势分析
```

## 🌐 官网前端逻辑 (待开发)

### 系统架构逻辑
```
官网前端/
├── 公共组件层
│   ├── Header.vue (页头导航)
│   ├── Footer.vue (页脚信息)
│   ├── Layout.vue (整体布局)
│   └── 业务组件 (插件卡片、视频卡片等)
├── 页面组件层
│   ├── Home.vue (首页营销)
│   ├── Market.vue (插件市场)
│   ├── Cases.vue (客户案例)
│   ├── Tutorials.vue (视频教程)
│   ├── SignIn.vue (签到奖励)
│   ├── Membership.vue (订阅会员)
│   ├── Affiliate.vue (分销推广)
│   └── UserCenter.vue (个人中心)
├── 数据管理层
│   ├── API接口封装
│   ├── 状态管理 (Vuex)
│   ├── 路由管理 (Vue Router)
│   └── 工具函数库
└── 样式主题层
    ├── 响应式布局
    ├── 主题色彩系统
    ├── 组件样式库
    └── 动画效果库
```

### 用户使用逻辑
```
用户访问官网 → 浏览内容 → 注册登录 → 使用功能
├── 首页 → 了解产品 → 查看功能介绍
├── 商城 → 浏览插件 → 查看详情 → 购买使用
├── 教程 → 选择课程 → 观看学习 → 提升技能
├── 个人中心 → 管理信息 → 查看余额 → API管理
├── 签到 → 每日签到 → 获得奖励 → 提升活跃
├── 会员 → 了解权益 → 购买会员 → 享受折扣
└── 分销 → 生成链接 → 推广赚钱 → 查看收益
```

## 🔄 业务流程逻辑

### 插件生态业务流程
```
1. 创作者注册 → 提交资质 → 管理员审核 → 获得权限
2. 开发插件 → 上传商城 → 设置价格 → 制作教程
3. 管理员审核 → 插件上架 → 用户可见 → 开始销售
4. 用户购买 → API调用 → 自动扣费 → 收益分成
5. 数据统计 → 收益结算 → 创作者提现 → 激励创作
```

### 用户成长业务流程
```
1. 访客浏览 → 了解产品 → 注册账号 → 成为用户
2. 观看教程 → 学习使用 → 充值余额 → 购买插件
3. 使用插件 → 每日签到 → 获得奖励 → 提升活跃
4. 升级会员 → 享受折扣 → 深度使用 → 推广赚钱
5. 成为创作者 → 开发插件 → 获得收益 → 生态贡献
```

### 平台运营业务流程
```
1. 内容管理 → 插件审核 → 教程制作 → 案例收集
2. 用户运营 → 活动策划 → 奖励发放 → 会员服务
3. 数据分析 → 用户行为 → 收益统计 → 趋势预测
4. 系统优化 → 功能迭代 → 性能提升 → 安全加固
5. 生态建设 → 创作者招募 → 合作伙伴 → 品牌推广
```

## 🎯 项目发展逻辑

### 第一阶段：基础平台搭建
- 完善后端API接口
- 开发官网核心页面
- 建立基础用户体系
- 上线核心功能模块

### 第二阶段：生态内容丰富
- 招募插件创作者
- 制作教学视频内容
- 收集客户成功案例
- 优化用户体验

### 第三阶段：商业化运营
- 推出会员订阅服务
- 建立分销推广体系
- 开展营销活动
- 扩大用户规模

### 第四阶段：生态系统成熟
- 形成完整的插件生态
- 建立行业影响力
- 探索新的商业模式
- 实现可持续发展
