import { HttpMethodType } from '../../TosExportEnum';
import TOSBase from '../base';
export interface CORSRule {
    AllowedOrigins: string[];
    AllowedMethods: HttpMethodType[];
    AllowedHeaders: string[];
    ExposeHeaders: string[];
    MaxAgeSeconds: number;
    ResponseVary?: boolean;
}
export interface GetBucketCORSInput {
    bucket: string;
}
export interface GetBucketCORSOutput {
    CORSRules: CORSRule[];
}
export declare function getBucketCORS(this: TOSBase, input: GetBucketCORSInput): Promise<import("../base").TosResponse<GetBucketCORSOutput>>;
export interface PutBucketCORSInput {
    bucket: string;
    CORSRules: CORSRule[];
}
export interface PutBucketCORSOutput {
}
export declare function putBucketCORS(this: TOSBase, input: PutBucketCORSInput): Promise<import("../base").TosResponse<DeleteBucketCORSOutput>>;
export interface DeleteBucketCORSInput {
    bucket: string;
}
export interface DeleteBucketCORSOutput {
}
export declare function deleteBucketCORS(this: TOSBase, input: DeleteBucketCORSInput): Promise<import("../base").TosResponse<DeleteBucketCORSOutput>>;
