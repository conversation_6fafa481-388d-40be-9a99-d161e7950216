package org.jeecg.modules.api.mapper;

import java.util.Date;
import java.util.List;
import java.util.Map;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.jeecg.modules.api.entity.AicgOnlineUsers;
import org.jeecg.modules.api.dto.UserActivityUpdateDTO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: 在线用户统计
 * @Author: jeecg-boot
 * @Date: 2025-06-14
 * @Version: V1.0
 */
public interface AicgOnlineUsersMapper extends BaseMapper<AicgOnlineUsers> {

    /**
     * 获取在线用户统计
     */
    @Select("SELECT * FROM v_online_users_stats")
    Map<String, Object> getOnlineUsersStats();

    /**
     * 更新用户最后活跃时间
     */
    @Update("UPDATE aicg_online_users SET last_active_time = NOW() " +
            "WHERE user_id = #{userId} AND status = 1")
    int updateLastActiveTime(@Param("userId") String userId);

    /**
     * 设置用户离线
     */
    @Update("UPDATE aicg_online_users SET status = 0, update_time = NOW() " +
            "WHERE user_id = #{userId}")
    int setUserOffline(@Param("userId") String userId);

    /**
     * 清理离线用户
     */
    @Update("UPDATE aicg_online_users SET status = 0, update_time = NOW() " +
            "WHERE last_active_time < DATE_SUB(NOW(), INTERVAL 1 HOUR) AND status = 1")
    int cleanOfflineUsers();

    /**
     * 获取今日活跃用户数
     */
    @Select("SELECT COUNT(DISTINCT user_id) as count " +
            "FROM aicg_online_users " +
            "WHERE DATE(last_active_time) = CURDATE()")
    int getTodayActiveUsersCount();

    /**
     * 获取当前在线用户数
     */
    @Select("SELECT COUNT(*) as count " +
            "FROM aicg_online_users " +
            "WHERE status = 1 AND last_active_time >= DATE_SUB(NOW(), INTERVAL 15 MINUTE)")
    int getCurrentOnlineUsersCount();

    // ==================== 批量操作方法 ====================

    /**
     * 批量更新用户活跃时间
     * 使用CASE WHEN语句实现高效批量更新
     */
    @Update("<script>" +
            "UPDATE aicg_online_users SET " +
            "last_active_time = CASE user_id " +
            "<foreach collection='userActivities' item='item' separator=' '>" +
            "WHEN #{item.userId} THEN #{item.lastActiveTime} " +
            "</foreach>" +
            "END, " +
            "ip_address = CASE user_id " +
            "<foreach collection='userActivities' item='item' separator=' '>" +
            "WHEN #{item.userId} THEN #{item.ipAddress} " +
            "</foreach>" +
            "END, " +
            "user_agent = CASE user_id " +
            "<foreach collection='userActivities' item='item' separator=' '>" +
            "WHEN #{item.userId} THEN #{item.userAgent} " +
            "</foreach>" +
            "END, " +
            "status = 1, " +
            "update_time = NOW() " +
            "WHERE user_id IN " +
            "<foreach collection='userActivities' item='item' open='(' separator=',' close=')'>" +
            "#{item.userId}" +
            "</foreach>" +
            "</script>")
    int batchUpdateUserActivity(@Param("userActivities") List<UserActivityUpdateDTO> userActivities);

    /**
     * 批量UPSERT操作（插入或更新用户活跃状态）
     * 使用ON DUPLICATE KEY UPDATE实现高效的插入或更新
     */
    @Insert("<script>" +
            "INSERT INTO aicg_online_users " +
            "(id, user_id, session_id, login_time, last_active_time, ip_address, user_agent, status, create_time, update_time) " +
            "VALUES " +
            "<foreach collection='userActivities' item='item' separator=','>" +
            "(#{item.id}, #{item.userId}, #{item.sessionId}, #{item.loginTime}, #{item.lastActiveTime}, " +
            "#{item.ipAddress}, #{item.userAgent}, 1, NOW(), NOW())" +
            "</foreach>" +
            "ON DUPLICATE KEY UPDATE " +
            "last_active_time = VALUES(last_active_time), " +
            "ip_address = VALUES(ip_address), " +
            "user_agent = VALUES(user_agent), " +
            "status = 1, " +
            "update_time = NOW()" +
            "</script>")
    int batchUpsertUserActivity(@Param("userActivities") List<UserActivityUpdateDTO> userActivities);

    /**
     * 批量设置用户离线
     */
    @Update("<script>" +
            "UPDATE aicg_online_users SET " +
            "status = 0, " +
            "update_time = NOW() " +
            "WHERE user_id IN " +
            "<foreach collection='userIds' item='userId' open='(' separator=',' close=')'>" +
            "#{userId}" +
            "</foreach>" +
            "</script>")
    int batchSetUsersOffline(@Param("userIds") List<String> userIds);

    /**
     * 批量清理离线用户（按时间范围）
     */
    @Update("UPDATE aicg_online_users SET " +
            "status = 0, " +
            "update_time = NOW() " +
            "WHERE last_active_time < DATE_SUB(NOW(), INTERVAL #{minutes} MINUTE) " +
            "AND status = 1")
    int batchCleanOfflineUsers(@Param("minutes") int minutes);

    // ==================== 优化的统计查询方法 ====================

    /**
     * 获取多维度在线用户统计
     * 一次查询获取5分钟、15分钟、1小时、今日活跃用户数
     */
    @Select("SELECT " +
            "COUNT(CASE WHEN last_active_time >= DATE_SUB(NOW(), INTERVAL 5 MINUTE) AND status = 1 THEN 1 END) as online_5min, " +
            "COUNT(CASE WHEN last_active_time >= DATE_SUB(NOW(), INTERVAL 15 MINUTE) AND status = 1 THEN 1 END) as online_15min, " +
            "COUNT(CASE WHEN last_active_time >= DATE_SUB(NOW(), INTERVAL 1 HOUR) AND status = 1 THEN 1 END) as online_1hour, " +
            "COUNT(CASE WHEN DATE(last_active_time) = CURDATE() THEN 1 END) as active_today, " +
            "COUNT(CASE WHEN status = 1 THEN 1 END) as total_online, " +
            "COUNT(*) as total_users " +
            "FROM aicg_online_users")
    Map<String, Object> getMultiDimensionStats();

    /**
     * 获取用户活跃度分布统计
     * 按时间段统计活跃用户分布
     */
    @Select("SELECT " +
            "'最近5分钟' as time_range, COUNT(*) as user_count " +
            "FROM aicg_online_users " +
            "WHERE last_active_time >= DATE_SUB(NOW(), INTERVAL 5 MINUTE) AND status = 1 " +
            "UNION ALL " +
            "SELECT " +
            "'5-15分钟前' as time_range, COUNT(*) as user_count " +
            "FROM aicg_online_users " +
            "WHERE last_active_time >= DATE_SUB(NOW(), INTERVAL 15 MINUTE) " +
            "AND last_active_time < DATE_SUB(NOW(), INTERVAL 5 MINUTE) AND status = 1 " +
            "UNION ALL " +
            "SELECT " +
            "'15分钟-1小时前' as time_range, COUNT(*) as user_count " +
            "FROM aicg_online_users " +
            "WHERE last_active_time >= DATE_SUB(NOW(), INTERVAL 1 HOUR) " +
            "AND last_active_time < DATE_SUB(NOW(), INTERVAL 15 MINUTE) AND status = 1 " +
            "UNION ALL " +
            "SELECT " +
            "'1小时以上' as time_range, COUNT(*) as user_count " +
            "FROM aicg_online_users " +
            "WHERE last_active_time < DATE_SUB(NOW(), INTERVAL 1 HOUR) AND status = 1")
    List<Map<String, Object>> getUserActivityDistribution();

    /**
     * 获取用户留存率统计
     * 计算不同时间段的用户留存情况
     */
    @Select("SELECT " +
            "DATE(login_time) as login_date, " +
            "COUNT(DISTINCT user_id) as login_users, " +
            "COUNT(DISTINCT CASE WHEN last_active_time >= DATE_SUB(NOW(), INTERVAL 1 DAY) THEN user_id END) as retained_1day, " +
            "COUNT(DISTINCT CASE WHEN last_active_time >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN user_id END) as retained_7day, " +
            "COUNT(DISTINCT CASE WHEN last_active_time >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN user_id END) as retained_30day " +
            "FROM aicg_online_users " +
            "WHERE login_time >= DATE_SUB(CURDATE(), INTERVAL 30 DAY) " +
            "GROUP BY DATE(login_time) " +
            "ORDER BY login_date DESC " +
            "LIMIT 30")
    List<Map<String, Object>> getUserRetentionStats();

    /**
     * 获取实时在线用户详情（分页）
     * 支持按活跃时间排序的在线用户列表
     */
    @Select("SELECT " +
            "user_id, " +
            "session_id, " +
            "login_time, " +
            "last_active_time, " +
            "ip_address, " +
            "TIMESTAMPDIFF(MINUTE, last_active_time, NOW()) as inactive_minutes, " +
            "CASE " +
            "  WHEN last_active_time >= DATE_SUB(NOW(), INTERVAL 5 MINUTE) THEN '活跃' " +
            "  WHEN last_active_time >= DATE_SUB(NOW(), INTERVAL 15 MINUTE) THEN '一般' " +
            "  WHEN last_active_time >= DATE_SUB(NOW(), INTERVAL 1 HOUR) THEN '不活跃' " +
            "  ELSE '离线' " +
            "END as activity_level " +
            "FROM aicg_online_users " +
            "WHERE status = 1 " +
            "ORDER BY last_active_time DESC " +
            "LIMIT #{offset}, #{limit}")
    List<Map<String, Object>> getOnlineUsersDetail(@Param("offset") int offset, @Param("limit") int limit);

    /**
     * 获取用户活跃趋势（24小时）
     * 按小时统计活跃用户数量趋势
     */
    @Select("SELECT " +
            "DATE_FORMAT(last_active_time, '%Y-%m-%d %H:00:00') as hour_time, " +
            "COUNT(DISTINCT user_id) as active_users " +
            "FROM aicg_online_users " +
            "WHERE last_active_time >= DATE_SUB(NOW(), INTERVAL 24 HOUR) " +
            "GROUP BY DATE_FORMAT(last_active_time, '%Y-%m-%d %H') " +
            "ORDER BY hour_time")
    List<Map<String, Object>> getUserActivityTrend24Hours();

    // ==================== 高级查询和性能优化方法 ====================

    /**
     * 获取用户会话统计
     * 统计用户的会话时长和活跃度
     */
    @Select("SELECT " +
            "user_id, " +
            "COUNT(*) as session_count, " +
            "AVG(TIMESTAMPDIFF(MINUTE, login_time, last_active_time)) as avg_session_duration, " +
            "MAX(TIMESTAMPDIFF(MINUTE, login_time, last_active_time)) as max_session_duration, " +
            "MIN(login_time) as first_login, " +
            "MAX(last_active_time) as last_activity " +
            "FROM aicg_online_users " +
            "WHERE login_time >= DATE_SUB(NOW(), INTERVAL #{days} DAY) " +
            "GROUP BY user_id " +
            "ORDER BY avg_session_duration DESC " +
            "LIMIT #{limit}")
    List<Map<String, Object>> getUserSessionStats(@Param("days") int days, @Param("limit") int limit);

    /**
     * 获取IP地址统计
     * 统计不同IP地址的用户活跃情况
     */
    @Select("SELECT " +
            "ip_address, " +
            "COUNT(DISTINCT user_id) as unique_users, " +
            "COUNT(*) as total_sessions, " +
            "MAX(last_active_time) as last_activity " +
            "FROM aicg_online_users " +
            "WHERE ip_address IS NOT NULL " +
            "AND last_active_time >= DATE_SUB(NOW(), INTERVAL #{hours} HOUR) " +
            "GROUP BY ip_address " +
            "HAVING unique_users >= #{minUsers} " +
            "ORDER BY unique_users DESC, total_sessions DESC " +
            "LIMIT #{limit}")
    List<Map<String, Object>> getIpAddressStats(@Param("hours") int hours,
                                                @Param("minUsers") int minUsers,
                                                @Param("limit") int limit);

    /**
     * 检查用户是否在线
     * 高效检查单个用户的在线状态
     */
    @Select("SELECT " +
            "CASE " +
            "  WHEN COUNT(*) > 0 AND MAX(last_active_time) >= DATE_SUB(NOW(), INTERVAL #{minutes} MINUTE) THEN 1 " +
            "  ELSE 0 " +
            "END as is_online " +
            "FROM aicg_online_users " +
            "WHERE user_id = #{userId} AND status = 1")
    int isUserOnline(@Param("userId") String userId, @Param("minutes") int minutes);

    /**
     * 获取用户最后活跃时间
     * 高效获取用户的最后活跃时间
     */
    @Select("SELECT MAX(last_active_time) as last_active_time " +
            "FROM aicg_online_users " +
            "WHERE user_id = #{userId}")
    Date getUserLastActiveTime(@Param("userId") String userId);

    /**
     * 统计数据库表性能指标
     * 用于监控表的大小和性能
     */
    @Select("SELECT " +
            "COUNT(*) as total_records, " +
            "COUNT(CASE WHEN status = 1 THEN 1 END) as online_records, " +
            "COUNT(CASE WHEN last_active_time >= DATE_SUB(NOW(), INTERVAL 1 DAY) THEN 1 END) as active_1day, " +
            "MIN(create_time) as earliest_record, " +
            "MAX(update_time) as latest_update " +
            "FROM aicg_online_users")
    Map<String, Object> getTablePerformanceStats();

    /**
     * 清理过期数据（软删除）
     * 将长时间未活跃的记录标记为离线
     */
    @Update("UPDATE aicg_online_users SET " +
            "status = 0, " +
            "update_time = NOW() " +
            "WHERE last_active_time < DATE_SUB(NOW(), INTERVAL #{days} DAY) " +
            "AND status = 1")
    int cleanExpiredRecords(@Param("days") int days);

    /**
     * 物理删除过期数据
     * 删除超过指定天数的离线记录（谨慎使用）
     */
    @Update("DELETE FROM aicg_online_users " +
            "WHERE status = 0 " +
            "AND update_time < DATE_SUB(NOW(), INTERVAL #{days} DAY)")
    int physicalDeleteExpiredRecords(@Param("days") int days);

    /**
     * 获取数据库索引使用情况
     * 用于性能监控和优化
     */
    @Select("SHOW INDEX FROM aicg_online_users")
    List<Map<String, Object>> getIndexUsage();
}
