{"openapi": "3.0.0", "info": {"title": "剪映小助手_智界数据箱 - 对象转字符串列表", "description": "对象列表转化成字符串列表", "version": "1.0.0"}, "servers": [{"url": "https://www.aigcview.com"}], "paths": {"/jeecg-boot/api/jianying/objs_to_str_list": {"post": {"summary": "对象转字符串列表", "description": "对象列表转化成字符串列表", "operationId": "objsToStrList_zj", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"access_key": {"type": "string", "description": "访问密钥（必填）", "example": "JianyingAPI_2025_SecureAccess_AigcView", "default": "JianyingAPI_2025_SecureAccess_AigcView"}, "zj_outputs": {"type": "array", "description": "数据对象，接收get_outputs节点的输出（必填）", "items": {"type": "object", "properties": {"zj_outputs": {"type": "string", "description": "输出内容"}}, "required": ["zj_outputs"], "additionalProperties": false}, "example": [{"zj_outputs": "测试1"}, {"zj_outputs": "测试2"}]}}, "required": ["access_key", "zj_outputs"], "additionalProperties": false}}}}, "responses": {"200": {"description": "成功转换为字符串列表", "content": {"application/json": {"schema": {"type": "object", "properties": {"result": {"type": "array", "description": "转换后的字符串列表", "items": {"type": "string"}, "example": ["啊啊", "大大大"]}}, "required": ["result"]}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "description": "操作是否成功", "example": false}, "error": {"type": "string", "description": "错误信息"}, "code": {"type": "string", "description": "错误代码"}}, "required": ["success", "error"]}}}}, "500": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "description": "操作是否成功", "example": false}, "error": {"type": "string", "description": "错误信息"}, "code": {"type": "string", "description": "错误代码"}}, "required": ["success", "error"]}}}}}}}}}