import TOSBase from '../base';
export interface Filter {
    TOSKey?: {
        FilterRules: {
            Name: string;
            Value: string;
        }[];
    };
}
interface CloudFunctionConfiguration {
    Events: string[];
    Filter?: Filter;
    RuleId?: string;
    CloudFunction: string;
}
export interface RocketMQConf {
    InstanceId: string;
    Topic: string;
    AccessKeyId: string;
}
export interface RocketMQConfiguration {
    RuleId: string;
    Role: string;
    Events: string[];
    Filter?: Filter;
    RocketMQ: RocketMQConf;
}
export interface PutBucketNotificationInput {
    bucket: string;
    cloudFunctionConfigurations?: CloudFunctionConfiguration[];
    rocketMQConfigurations?: RocketMQConfiguration[];
}
export interface PutBucketNotificationOutput {
}
/**
 * @deprecated use PutBucketNotificationType2 instead
 */
export declare function putBucketNotification(this: TOSBase, input: PutBucketNotificationInput): Promise<import("../base").TosResponse<PutBucketNotificationOutput>>;
export interface GetBucketNotificationInput {
    bucket: string;
}
export interface GetBucketNotificationOutput {
    CloudFunctionConfigurations: CloudFunctionConfiguration[];
    RocketMQConfigurations: RocketMQConfiguration[];
}
/**
 * @deprecated use GetBucketNotificationType2 instead
 */
export declare function getBucketNotification(this: TOSBase, input: GetBucketNotificationInput): Promise<import("../base").TosResponse<GetBucketNotificationOutput>>;
export {};
