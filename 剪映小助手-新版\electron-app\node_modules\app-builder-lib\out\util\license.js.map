{"version": 3, "file": "license.js", "sourceRoot": "", "sources": ["../../src/util/license.ts"], "names": [], "mappings": ";;;AAAA,6BAA4B;AAC5B,mCAAwD;AAGxD,SAAgB,gBAAgB,CAAC,SAAwB,EAAE,QAA+B;IACxF,OAAO,SAAS;SACb,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;QACb,MAAM,EAAE,GAAG,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAA;QACtC,MAAM,EAAE,GAAG,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAA;QACtC,OAAO,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE,CAAA;IACjD,CAAC,CAAC;SACD,GAAG,CAAC,IAAI,CAAC,EAAE;QACV,IAAI,IAAI,GAAG,YAAY,CAAC,IAAI,CAAC,IAAI,CAAE,CAAC,CAAC,CAAC,CAAA;QACtC,IAAI,cAAc,CAAA;QAClB,IAAI,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;YACvB,cAAc,GAAG,IAAI,CAAA;YACrB,IAAI,GAAG,cAAc,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAA;QACvD,CAAC;aAAM,CAAC;YACN,IAAI,GAAG,IAAI,CAAC,WAAW,EAAE,CAAA;YACzB,cAAc,GAAG,IAAA,wBAAgB,EAAC,IAAI,CAAC,CAAA;QACzC,CAAC;QACD,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,iBAAiB,EAAE,IAAI,CAAC,EAAE,IAAI,EAAE,cAAc,EAAE,QAAQ,EAAG,oBAAoB,CAAC,IAAI,CAAC,EAAE,CAAA;IAC3H,CAAC,CAAC,CAAA;AACN,CAAC;AAnBD,4CAmBC;AAEM,KAAK,UAAU,0BAA0B,CAC9C,MAAiC,EACjC,QAA+B,EAC/B,qBAAoC,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,CAAC;IAE1D,MAAM,aAAa,GAAkB,EAAE,CAAA;IACvC,KAAK,MAAM,IAAI,IAAI,CAAC,SAAS,EAAE,MAAM,CAAC,EAAE,CAAC;QACvC,KAAK,MAAM,GAAG,IAAI,kBAAkB,EAAE,CAAC;YACrC,aAAa,CAAC,IAAI,CAAC,GAAG,IAAI,IAAI,GAAG,EAAE,CAAC,CAAA;YACpC,aAAa,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,WAAW,EAAE,IAAI,GAAG,EAAE,CAAC,CAAA;YAClD,aAAa,CAAC,IAAI,CAAC,GAAG,IAAI,IAAI,GAAG,CAAC,WAAW,EAAE,EAAE,CAAC,CAAA;YAClD,aAAa,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,WAAW,EAAE,IAAI,GAAG,CAAC,WAAW,EAAE,EAAE,CAAC,CAAA;QAClE,CAAC;IACH,CAAC;IAED,OAAO,MAAM,QAAQ,CAAC,WAAW,CAAC,MAAM,EAAE,GAAG,aAAa,CAAC,CAAA;AAC7D,CAAC;AAhBD,gEAgBC;AAEM,KAAK,UAAU,eAAe,CAAC,QAA+B;IACnE,OAAO,gBAAgB,CACrB,CAAC,MAAM,QAAQ,CAAC,YAAY,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE;QACxC,MAAM,IAAI,GAAG,EAAE,CAAC,WAAW,EAAE,CAAA;QAC7B,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,IAAI,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAA;IAChJ,CAAC,CAAC,EACF,QAAQ,CACT,CAAA;AACH,CAAC;AARD,0CAQC", "sourcesContent": ["import * as path from \"path\"\nimport { langIdToName, toLangWithRegion } from \"./langs\"\nimport { PlatformPackager } from \"../platformPackager\"\n\nexport function getLicenseAssets(fileNames: Array<string>, packager: PlatformPackager<any>) {\n  return fileNames\n    .sort((a, b) => {\n      const aW = a.includes(\"_en\") ? 0 : 100\n      const bW = b.includes(\"_en\") ? 0 : 100\n      return aW === bW ? a.localeCompare(b) : aW - bW\n    })\n    .map(file => {\n      let lang = /_([^.]+)\\./.exec(file)![1]\n      let langWithRegion\n      if (lang.includes(\"_\")) {\n        langWithRegion = lang\n        lang = langWithRegion.substring(0, lang.indexOf(\"_\"))\n      } else {\n        lang = lang.toLowerCase()\n        langWithRegion = toLangWithRegion(lang)\n      }\n      return { file: path.join(packager.buildResourcesDir, file), lang, langWithRegion, langName: (langIdToName as any)[lang] }\n    })\n}\n\nexport async function getNotLocalizedLicenseFile(\n  custom: string | null | undefined,\n  packager: PlatformPackager<any>,\n  supportedExtension: Array<string> = [\"rtf\", \"txt\", \"html\"]\n): Promise<string | null> {\n  const possibleFiles: Array<string> = []\n  for (const name of [\"license\", \"eula\"]) {\n    for (const ext of supportedExtension) {\n      possibleFiles.push(`${name}.${ext}`)\n      possibleFiles.push(`${name.toUpperCase()}.${ext}`)\n      possibleFiles.push(`${name}.${ext.toUpperCase()}`)\n      possibleFiles.push(`${name.toUpperCase()}.${ext.toUpperCase()}`)\n    }\n  }\n\n  return await packager.getResource(custom, ...possibleFiles)\n}\n\nexport async function getLicenseFiles(packager: PlatformPackager<any>): Promise<Array<LicenseFile>> {\n  return getLicenseAssets(\n    (await packager.resourceList).filter(it => {\n      const name = it.toLowerCase()\n      return (name.startsWith(\"license_\") || name.startsWith(\"eula_\")) && (name.endsWith(\".rtf\") || name.endsWith(\".txt\") || name.endsWith(\".html\"))\n    }),\n    packager\n  )\n}\n\nexport interface LicenseFile {\n  file: string\n  lang: string\n  langWithRegion: string\n  langName: string\n}\n"]}