{"name": "tos-crc64-js", "version": "0.0.1", "description": "Pure JavaScript implement of CRC64-ECMA182 for Node.js.", "main": "crc64_ecma182.js", "scripts": {"eslint": "eslint .", "postinstall": "node scripts/postinstall.js", "build": "mkdir -p dist && make js && node ./scripts/transform.js", "prepublishOnly": "npm run build", "test": "make test"}, "repository": {"type": "git", "url": "git+https://github.com/MoonBall/crc64-ecma182.js.git"}, "keywords": ["crc64", "crc64-ecma182"], "author": "MoonBall <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/MoonBall/crc64-ecma182.js/issues"}, "homepage": "https://github.com/MoonBall/crc64-ecma182.js#readme", "devDependencies": {"@babel/cli": "^7.23.9", "@babel/core": "^7.24.0", "@babel/plugin-transform-logical-assignment-operators": "^7.23.4", "@babel/plugin-transform-optional-chaining": "^7.23.4", "@types/node": "^20.4.9", "eslint": "^8.46.0", "eslint-config-egg": "^12.2.1", "mocha": "^10.2.0", "semver": "^7.5.4", "should": "^13.2.3", "typescript": "^5.1.6"}, "files": ["dist", "crc64_ecma182.js", "crc64_ecma182.d.ts", "scripts"], "types": "crc64_ecma182.d.ts"}