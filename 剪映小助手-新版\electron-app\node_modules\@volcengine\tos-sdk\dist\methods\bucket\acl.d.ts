import TOSBase from '../base';
import { AclInterface, Acl } from '../../interface';
export declare type GetBucketAclOutput = AclInterface;
export interface PutBucketAclInput {
    bucket?: string;
    acl?: Acl;
    aclBody?: AclInterface;
}
export declare function putBucketAcl(this: TOSBase, input: PutBucketAclInput): Promise<import("../base").TosResponse<unknown>>;
export declare function getBucketAcl(this: TOSBase, bucket?: string): Promise<import("../base").TosResponse<AclInterface>>;
