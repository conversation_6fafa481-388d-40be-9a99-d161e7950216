import TOSBase from '../base';
import { AccessPointStatusType } from '../../TosExportEnum';
export interface AccessPoint {
    Name: string;
    Alias?: string;
    Status: AccessPointStatusType;
    CreatedAt: number;
    Regions: Array<{
        Bucket: string;
        BucketAccountId: number;
        Region: string;
    }>;
}
export interface CreateMultiRegionAccessPointInput {
    name: string;
    regions: Array<{
        Bucket: string;
        BucketAccountId: string;
    }>;
    accountId: string;
}
export interface CreateMultiRegionAccessPointOutput {
}
/**
 * @private unstable method
 */
export declare function createMultiRegionAccessPoint(this: TOSBase, input: CreateMultiRegionAccessPointInput): Promise<import("../base").TosResponse<CreateMultiRegionAccessPointOutput>>;
export interface GetMultiRegionAccessPointInput {
    name: string;
    accountId: string;
}
export interface GetMultiRegionAccessPointOutput extends AccessPoint {
}
/**
 * @private unstable method
 */
export declare function getMultiRegionAccessPoint(this: TOSBase, input: GetMultiRegionAccessPointInput): Promise<import("../base").TosResponse<GetMultiRegionAccessPointOutput>>;
export interface ListMultiRegionAccessPointsInput {
    accountId: string;
    maxResults?: number;
    nextToken?: string;
}
export interface ListMultiRegionAccessPointsOutput {
    AccessPoints: Array<AccessPoint>;
    NextToken?: string;
}
/**
 * @private unstable method
 */
export declare function listMultiRegionAccessPoints(this: TOSBase, input: ListMultiRegionAccessPointsInput): Promise<import("../base").TosResponse<ListMultiRegionAccessPointsOutput>>;
export interface GetMultiRegionAccessPointRoutesInput {
    accountId: string;
    alias: string;
}
export interface AccessPointRoute {
    Bucket: string;
    Region: string;
    TrafficDialPercentage: number;
}
export interface GetMultiRegionAccessPointRoutesOutput {
    Routes?: AccessPointRoute[];
    Alias?: string;
}
/**
 * @private unstable method
 */
export declare function getMultiRegionAccessPointRoutes(this: TOSBase, input: GetMultiRegionAccessPointRoutesInput): Promise<import("../base").TosResponse<GetMultiRegionAccessPointRoutesOutput>>;
export interface DeleteMultiRegionAccessPointInput {
    accountId: string;
    name: string;
}
export interface DeleteMultiRegionAccessPointOutput {
}
export declare function deleteMultiRegionAccessPoint(this: TOSBase, input: DeleteMultiRegionAccessPointInput): Promise<import("../base").TosResponse<DeleteMultiRegionAccessPointOutput>>;
export interface SubmitMultiRegionAccessPointRoutesInput {
    accountId: string;
    alias: string;
    routes: AccessPointRoute[];
}
export interface SubmitMultiRegionAccessPointRoutesOutput {
}
export declare function submitMultiRegionAccessPointRoutes(this: TOSBase, input: SubmitMultiRegionAccessPointRoutesInput): Promise<import("../base").TosResponse<SubmitMultiRegionAccessPointRoutesOutput>>;
