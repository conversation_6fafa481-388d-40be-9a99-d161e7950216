package org.jeecg.modules.demo.apiusage.entity;

import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 用户API使用记录表
 * @Author: jeecg-boot
 * @Date:   2025-06-18
 * @Version: V1.0
 */
@Data
@TableName("aicg_user_api_usage")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="aicg_user_api_usage对象", description="用户API使用记录表")
public class AicgUserApiUsage implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String id;
    
	/**用户ID*/
	@Excel(name = "用户ID", width = 15)
    @ApiModelProperty(value = "用户ID，关联sys_user.id")
    private String userId;
    
	/**API密钥*/
	@Excel(name = "API密钥", width = 30)
    @ApiModelProperty(value = "API密钥")
    private String apiKey;
    
	/**API接口地址*/
	@Excel(name = "API接口地址", width = 50)
    @ApiModelProperty(value = "API接口地址")
    private String apiEndpoint;
    
	/**请求方法*/
	@Excel(name = "请求方法", width = 15)
    @ApiModelProperty(value = "请求方法：GET,POST,PUT,DELETE")
    private String apiMethod;
    
	/**请求参数*/
    @ApiModelProperty(value = "请求参数(JSON格式)")
    private String requestParams;
    
	/**响应状态码*/
	@Excel(name = "响应状态码", width = 15)
    @ApiModelProperty(value = "响应状态码")
    private Integer responseStatus;
    
	/**响应时间*/
	@Excel(name = "响应时间(毫秒)", width = 15)
    @ApiModelProperty(value = "响应时间(毫秒)")
    private Integer responseTime;
    
	/**消耗Token数量*/
	@Excel(name = "消耗Token数量", width = 15)
    @ApiModelProperty(value = "消耗的Token数量")
    private Integer tokensUsed;
    
	/**消耗金额*/
	@Excel(name = "消耗金额", width = 15)
    @ApiModelProperty(value = "消耗金额")
    private BigDecimal costAmount;
    
	/**请求IP地址*/
	@Excel(name = "请求IP地址", width = 20)
    @ApiModelProperty(value = "请求IP地址")
    private String ipAddress;
    
	/**用户代理*/
    @ApiModelProperty(value = "用户代理")
    private String userAgent;
    
	/**错误信息*/
    @ApiModelProperty(value = "错误信息")
    private String errorMessage;
    
	/**调用时间*/
	@Excel(name = "调用时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "调用时间")
    private Date callTime;
    
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;
    
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private Date createTime;
    
	/**所属部门*/
    @ApiModelProperty(value = "所属部门")
    private String sysOrgCode;

    /**插件ID*/
    @Excel(name = "插件ID", width = 30)
    @ApiModelProperty(value = "插件ID，关联aigc_plub_shop.id")
    private String pluginId;

    /**插件唯一标识*/
    @Excel(name = "插件标识", width = 30)
    @ApiModelProperty(value = "插件唯一标识，用户传递的参数")
    private String pluginKey;

    /**插件名称*/
    @Excel(name = "插件名称", width = 30)
    @ApiModelProperty(value = "插件名称，从aigc_plub_shop.plubname获取")
    private String pluginName;
}
