{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\workflow\\components\\AgentMarket.vue?vue&type=template&id=dbf24c8c&scoped=true&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\workflow\\components\\AgentMarket.vue", "mtime": 1754513071550}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\n<div class=\"agent-market\">\n  <!-- 固定的搜索和筛选区域 -->\n  <div class=\"sticky-filters\">\n    <div class=\"market-filters\">\n      <div class=\"filter-row\">\n        <!-- 搜索框 -->\n        <div class=\"search-box\">\n          <div class=\"search-wrapper\">\n            <a-icon type=\"search\" class=\"search-icon\" />\n            <a-input\n              v-model=\"searchQuery\"\n              placeholder=\"搜索智能体名称、描述或标签...\"\n              size=\"large\"\n              @pressEnter=\"handleSearch\"\n              @input=\"handleSearch\"\n              class=\"search-input\"\n            />\n            <a-icon\n              v-if=\"searchQuery\"\n              type=\"close-circle\"\n              class=\"clear-icon\"\n              @click=\"clearSearch\"\n            />\n          </div>\n        </div>\n\n        <!-- 筛选器 -->\n        <div class=\"filter-controls\">\n          <!-- 作者类型筛选 -->\n          <div class=\"filter-group\">\n            <div class=\"filter-buttons\">\n              <button\n                class=\"filter-btn\"\n                :class=\"{ 'active': authorTypeFilter === '' }\"\n                @click=\"setAuthorTypeFilter('')\"\n              >\n                <a-icon type=\"appstore\" />\n                全部\n              </button>\n              <button\n                class=\"filter-btn official\"\n                :class=\"{ 'active': authorTypeFilter === '1' }\"\n                @click=\"setAuthorTypeFilter('1')\"\n              >\n                <a-icon type=\"crown\" />\n                官方\n              </button>\n              <button\n                class=\"filter-btn creator\"\n                :class=\"{ 'active': authorTypeFilter === '2' }\"\n                @click=\"setAuthorTypeFilter('2')\"\n              >\n                <a-icon type=\"user\" />\n                创作者\n              </button>\n            </div>\n          </div>\n\n          <!-- 状态筛选 -->\n          <div class=\"filter-group\">\n            <div class=\"filter-buttons\">\n              <button\n                class=\"filter-btn\"\n                :class=\"{ 'active': purchaseStatusFilter === '' }\"\n                @click=\"setPurchaseStatusFilter('')\"\n              >\n                <a-icon type=\"appstore\" />\n                全部\n              </button>\n              <button\n                class=\"filter-btn purchased\"\n                :class=\"{ 'active': purchaseStatusFilter === 'purchased' }\"\n                @click=\"setPurchaseStatusFilter('purchased')\"\n              >\n                <a-icon type=\"check-circle\" />\n                {{ getPurchaseFilterText() }}\n              </button>\n              <button\n                class=\"filter-btn unpurchased\"\n                :class=\"{ 'active': purchaseStatusFilter === 'unpurchased' }\"\n                @click=\"setPurchaseStatusFilter('unpurchased')\"\n              >\n                <a-icon type=\"shopping\" />\n                未购\n              </button>\n            </div>\n          </div>\n\n          <!-- 重置按钮 -->\n          <div class=\"filter-group\">\n            <div class=\"filter-buttons\">\n              <button\n                class=\"filter-btn reset-btn\"\n                @click=\"resetAllFilters\"\n                :disabled=\"!hasActiveFilters\"\n              >\n                <a-icon type=\"reload\" />\n                重置\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- 智能体列表 -->\n  <div class=\"agent-list\" v-if=\"!loading\">\n    <div class=\"list-header\">\n      <h3 class=\"list-title\">\n        智能体列表\n        <span class=\"list-count\">({{ totalCount }}个)</span>\n      </h3>\n    </div>\n\n    <!-- 智能体卡片网格 -->\n    <div class=\"agent-grid\" v-if=\"agentList.length > 0\">\n      <AgentCard\n        v-for=\"agent in agentList\"\n        :key=\"agent.id\"\n        :agent=\"agent\"\n        @view-detail=\"handleViewDetail\"\n      />\n    </div>\n\n    <!-- 空状态 -->\n    <div v-else class=\"empty-state\">\n      <a-empty\n        description=\"暂无智能体数据\"\n      >\n        <a-button type=\"primary\" @click=\"handleRefresh\">\n          <a-icon type=\"reload\" />\n          刷新数据\n        </a-button>\n      </a-empty>\n    </div>\n\n    <!-- 加载更多提示 -->\n    <div class=\"load-more-wrapper\" v-if=\"agentList.length > 0\">\n      <div v-if=\"loadingMore\" class=\"loading-more\">\n        <a-spin size=\"small\" />\n        <span>正在加载更多...</span>\n      </div>\n      <div v-else-if=\"hasMore\" class=\"load-more-trigger\" ref=\"loadMoreTrigger\">\n        <!-- 滚动到这里触发加载更多 -->\n      </div>\n      <div v-else class=\"no-more-data\">\n        <a-icon type=\"check-circle\" />\n        <span>已加载全部数据 (共{{ totalCount }}个)</span>\n      </div>\n    </div>\n  </div>\n\n  <!-- 加载状态 -->\n  <div v-else class=\"loading-state\">\n    <a-spin size=\"large\" tip=\"正在加载智能体数据...\">\n      <div class=\"loading-placeholder\"></div>\n    </a-spin>\n  </div>\n\n  <!-- 智能体详情弹窗 -->\n  <AgentDetailModal\n    :visible=\"detailModalVisible\"\n    :agentId=\"selectedAgentId\"\n    :isPurchased=\"isSelectedAgentPurchased\"\n    @close=\"handleCloseDetailModal\"\n    @update:visible=\"handleUpdateVisible\"\n    @purchase=\"handlePurchaseFromModal\"\n    @purchase-success=\"handlePurchaseSuccess\"\n  />\n</div>\n", null]}