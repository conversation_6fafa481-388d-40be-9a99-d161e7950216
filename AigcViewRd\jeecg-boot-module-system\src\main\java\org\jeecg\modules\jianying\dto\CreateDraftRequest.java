package org.jeecg.modules.jianying.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 创建草稿请求参数
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CreateDraftRequest extends BaseJianyingRequest {
    
    @ApiModelProperty(value = "视频高度", example = "1080")
    @JsonProperty("height")
    private Integer zjHeight;

    @ApiModelProperty(value = "视频宽度", example = "1920")
    @JsonProperty("width")
    private Integer zjWidth;

    @ApiModelProperty(value = "关联创作者，用来获取推广分成", example = "12345")
    @JsonProperty("user_id")
    private Integer zjUserId;
    
    @Override
    public String getSummary() {
        return "CreateDraftRequest{" +
               "accessKey=" + (getAccessKey() != null && getAccessKey().length() > 10 ? 
                              getAccessKey().substring(0, 10) + "***" : "null") +
               ", height=" + zjHeight +
               ", width=" + zjWidth +
               ", userId=" + zjUserId +
               "}";
    }
}
