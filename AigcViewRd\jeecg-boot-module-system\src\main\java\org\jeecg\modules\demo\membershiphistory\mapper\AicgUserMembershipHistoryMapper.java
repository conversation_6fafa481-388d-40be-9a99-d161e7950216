package org.jeecg.modules.demo.membershiphistory.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.jeecg.modules.demo.membershiphistory.entity.AicgUserMembershipHistory;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: 用户会员订阅历史表
 * @Author: jeecg-boot
 * @Date:   2025-06-18
 * @Version: V1.0
 */
public interface AicgUserMembershipHistoryMapper extends BaseMapper<AicgUserMembershipHistory> {

    /**
     * 根据用户ID查询会员订阅历史
     * @param userId 用户ID
     * @return 会员订阅历史列表
     */
    @Select("SELECT * FROM aicg_user_membership_history WHERE user_id = #{userId} ORDER BY create_time DESC")
    List<AicgUserMembershipHistory> getByUserId(@Param("userId") String userId);
    
    /**
     * 根据用户ID查询当前生效的会员订阅
     * @param userId 用户ID
     * @return 当前生效的会员订阅
     */
    @Select("SELECT * FROM aicg_user_membership_history WHERE user_id = #{userId} AND status = 1 AND start_time <= NOW() AND end_time > NOW() ORDER BY create_time DESC LIMIT 1")
    AicgUserMembershipHistory getCurrentMembership(@Param("userId") String userId);
    
    /**
     * 根据订单ID查询会员订阅记录
     * @param orderId 订单ID
     * @return 会员订阅记录
     */
    @Select("SELECT * FROM aicg_user_membership_history WHERE order_id = #{orderId}")
    AicgUserMembershipHistory getByOrderId(@Param("orderId") String orderId);
    
    /**
     * 查询即将过期的会员订阅（7天内过期）
     * @return 即将过期的会员订阅列表
     */
    @Select("SELECT * FROM aicg_user_membership_history WHERE status = 1 AND end_time > NOW() AND end_time <= DATE_ADD(NOW(), INTERVAL 7 DAY)")
    List<AicgUserMembershipHistory> getExpiringMemberships();
    
    /**
     * 查询已过期但状态未更新的会员订阅
     * @return 已过期的会员订阅列表
     */
    @Select("SELECT * FROM aicg_user_membership_history WHERE status = 1 AND end_time <= NOW()")
    List<AicgUserMembershipHistory> getExpiredMemberships();
}
