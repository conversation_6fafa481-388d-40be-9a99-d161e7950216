/// <reference types="node" />
/**
 * since fs/promises exist after nodejs@14, so we make own fs/promises
 */
import fs from 'fs';
export declare const createWriteStream: typeof fs.createWriteStream;
export declare const createReadStream: typeof fs.createReadStream;
export declare const open: typeof fs.promises.open;
export declare const close: typeof fs.close.__promisify__;
export declare const rename: typeof fs.rename.__promisify__;
export declare const stat: typeof fs.stat.__promisify__;
export declare const mkdir: typeof fs.mkdir.__promisify__;
export declare const writeFile: typeof fs.writeFile.__promisify__;
export declare const write: typeof fs.write.__promisify__;
export declare const appendFile: typeof fs.appendFile.__promisify__;
export declare const rm: typeof fs.unlink.__promisify__;
export declare const readFile: typeof fs.readFile.__promisify__;
export declare const safeMkdirRecursive: (dirName: fs.PathLike) => Promise<void>;
