package org.jeecg.modules.jianying.controller;

import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.jianying.dto.*;
import org.jeecg.modules.jianying.service.JianyingAssistantService;
import org.jeecg.modules.jianying.service.JianyingIdResolverService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

/**
 * 剪映小助手_智界工具箱 API控制器
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
@Api(tags = "剪映小助手-智界工具箱")
@RestController
@RequestMapping(value = "/api/jianying", produces = "application/json;charset=UTF-8")
@Slf4j
public class JianyingToolboxController {
    
    @Autowired
    private JianyingAssistantService jianyingAssistantService;

    @Autowired
    private JianyingIdResolverService jianyingIdResolverService;
    
    /**
     * 创建草稿
     */
    @ApiOperation(value = "创建草稿", notes = "创建一个新的剪映草稿")
    @PostMapping("/create_draft")
    public Object createDraft(@Valid @RequestBody CreateDraftRequest request) {

        try {
            log.info("创建草稿请求: {}", request.getSummary());

            JSONObject result = jianyingAssistantService.createDraft(request);

            if (result.getBoolean("success")) {
                // 直接返回数据对象，完全匹配竞争对手格式
                return ResponseEntity.ok(result.getJSONObject("data"));
            } else {
                // 错误情况返回统一格式
                JSONObject errorResponse = new JSONObject();
                errorResponse.put("error", result.getString("error"));
                return ResponseEntity.status(500).body(errorResponse);
            }

        } catch (Exception e) {
            log.error("创建草稿失败", e);
            // 错误情况返回统一格式
            JSONObject errorResponse = new JSONObject();
            errorResponse.put("success", false);
            errorResponse.put("error", "创建草稿失败: " + e.getMessage());
            errorResponse.put("error_code", "CREATE_DRAFT_ERROR");
            errorResponse.put("timestamp", System.currentTimeMillis());
            return ResponseEntity.status(500).body(errorResponse);
        }
    }
    
    /**
     * 快速创建素材轨道
     */
    @ApiOperation(value = "快速创建素材轨道", notes = "快速创建素材轨道")
    @PostMapping("/easy_create_material")
    public Object easyCreateMaterial(@Valid @RequestBody EasyCreateMaterialRequest request) {

        try {
            log.info("快速创建素材轨道请求: {}", request.getSummary());

            JSONObject result = jianyingAssistantService.easyCreateMaterial(request);

            // 检查是否有错误字段（新格式）
            if (result.containsKey("error")) {
                // 错误情况返回统一格式
                return ResponseEntity.status(500).body(result);
            } else {
                // 成功情况直接返回Service的结果（已经是正确格式：{message, draft_url}）
                return ResponseEntity.ok(result);
            }

        } catch (Exception e) {
            log.error("快速创建素材轨道失败", e);
            JSONObject errorResponse = new JSONObject();
            errorResponse.put("success", false);
            errorResponse.put("error", "快速创建素材轨道失败: " + e.getMessage());
            errorResponse.put("error_code", "EASY_CREATE_MATERIAL_ERROR");
            errorResponse.put("timestamp", System.currentTimeMillis());
            return ResponseEntity.status(500).body(errorResponse);
        }
    }
    
    /**
     * 获取图片出入场动画
     */
    @ApiOperation(value = "获取图片出入场动画", notes = "获取图片出入场动画列表")
    @PostMapping("/get_image_animations")
    public Object getImageAnimations(@Valid @RequestBody GetImageAnimationsRequest request) {
        try {
            log.info("获取图片出入场动画请求: {}", request.getSummary());

            JSONObject result = jianyingAssistantService.getImageAnimations(request);

            // 检查是否有错误
            if (result.containsKey("error")) {
                JSONObject errorResponse = new JSONObject();
                errorResponse.put("error", result.getString("error"));
                return ResponseEntity.status(400).body(errorResponse);
            }

            // 直接返回竞争对手格式：{effects: [...]}
            return ResponseEntity.ok(result);

        } catch (Exception e) {
            log.error("获取图片动画失败", e);
            JSONObject errorResponse = new JSONObject();
            errorResponse.put("success", false);
            errorResponse.put("error", "获取图片动画失败: " + e.getMessage());
            errorResponse.put("error_code", "GET_IMAGE_ANIMATIONS_ERROR");
            errorResponse.put("timestamp", System.currentTimeMillis());
            return ResponseEntity.status(400).body(errorResponse);
        }
    }
    
    /**
     * 添加贴纸
     */
    @ApiOperation(value = "添加贴纸", notes = "向草稿中添加贴纸")
    @PostMapping("/add_sticker")
    public Object addSticker(@Valid @RequestBody AddStickerRequest request) {

        try {
            log.info("添加贴纸请求: {}", request.getSummary());

            JSONObject result = jianyingAssistantService.addSticker(request);

            // 检查是否有错误字段（新格式）
            if (result.containsKey("error")) {
                // 错误情况返回统一格式
                return ResponseEntity.status(500).body(result);
            } else {
                // 成功情况直接返回Service的结果（已经是正确格式：{message, draft_url}）
                return ResponseEntity.ok(result);
            }

        } catch (Exception e) {
            log.error("添加贴纸失败", e);
            JSONObject errorResponse = new JSONObject();
            errorResponse.put("error", "添加贴纸失败: " + e.getMessage());
            return ResponseEntity.status(500).body(errorResponse);
        }
    }
    
    /**
     * 获取音频时长（返回竞争对手格式）
     */
    @ApiOperation(value = "获取音频时长", notes = "获取音频文件的时长信息，使用真实音频解析")
    @PostMapping("/get_audio_duration")
    public Object getAudioDuration(@Valid @RequestBody GetAudioDurationRequest request) {

        try {
            log.info("获取音频时长请求: {}", request.getSummary());

            // Service层返回格式：成功时{duration, message}，失败时{error}
            JSONObject result = jianyingAssistantService.getAudioDuration(request);

            // 检查是否有错误字段
            if (result.containsKey("error")) {
                // 错误情况返回500状态码和错误信息
                return ResponseEntity.status(500).body(result);
            } else {
                // 成功情况返回200状态码
                return ResponseEntity.ok(result);
            }

        } catch (Exception e) {
            log.error("获取音频时长失败", e);
            // Controller层异常也返回错误格式：{"error": "错误信息"}
            JSONObject errorResponse = new JSONObject();
            errorResponse.put("error", "获取音频时长失败: " + e.getMessage());
            return ResponseEntity.status(500).body(errorResponse);
        }
    }

    /**
     * 云渲染视频
     */
    @ApiOperation(value = "云渲染视频", notes = "云渲染视频，接收create_draft输出的链接")
    @PostMapping("/gen_video")
    public Result<?> genVideo(@Valid @RequestBody GenVideoRequest request, HttpServletRequest httpRequest) {

        try {
            // 从拦截器获取用户信息
            String userId = (String) httpRequest.getAttribute("userId");
            String userNickname = (String) httpRequest.getAttribute("userNickname");

            log.info("云渲染视频请求: {}", request.getSummary());

            JSONObject result = jianyingAssistantService.genVideo(request);

            if (result.getBoolean("success")) {
                return Result.OK("渲染任务提交成功", result.getJSONObject("data"));
            } else {
                return Result.error(result.getString("error"));
            }

        } catch (Exception e) {
            log.error("云渲染视频失败", e);
            return Result.error("云渲染视频失败: " + e.getMessage());
        }
    }

    /**
     * 批量添加音频
     */
    @ApiOperation(value = "批量添加音频", notes = "向草稿中批量添加音频文件")
    @PostMapping("/add_audios")
    public Object addAudios(@Valid @RequestBody AddAudiosRequest request) {

        try {
            log.info("批量添加音频请求: {}", request.getSummary());

            JSONObject result = jianyingAssistantService.addAudios(request);

            // 检查是否有错误字段（新格式）
            if (result.containsKey("error")) {
                // 错误情况返回统一格式
                return ResponseEntity.status(500).body(result);
            } else {
                // 成功情况直接返回Service的结果（已经是正确格式：{track_id, draft_url, audio_ids, segment_ids, segment_infos}）
                return ResponseEntity.ok(result);
            }

        } catch (Exception e) {
            log.error("批量添加音频失败", e);
            JSONObject errorResponse = new JSONObject();
            errorResponse.put("success", false);
            errorResponse.put("error", "批量添加音频失败: " + e.getMessage());
            errorResponse.put("error_code", "ADD_AUDIOS_ERROR");
            errorResponse.put("timestamp", System.currentTimeMillis());
            return ResponseEntity.status(500).body(errorResponse);
        }
    }

    /**
     * 添加特效
     */
    @ApiOperation(value = "添加特效", notes = "向草稿中添加特效")
    @PostMapping("/add_effects")
    public Object addEffects(@Valid @RequestBody AddEffectsRequest request) {

        try {
            log.info("添加特效请求: {}", request.getSummary());

            JSONObject result = jianyingAssistantService.addEffects(request.getZjEffectInfos(), request.getZjDraftUrl());

            // 检查是否有错误字段（新格式）
            if (result.containsKey("error")) {
                // 错误情况返回统一格式
                return ResponseEntity.status(500).body(result);
            } else {
                // 成功情况直接返回Service的结果（已经是正确格式：{message, draft_url}）
                return ResponseEntity.ok(result);
            }

        } catch (Exception e) {
            log.error("添加特效失败", e);
            JSONObject errorResponse = new JSONObject();
            errorResponse.put("success", false);
            errorResponse.put("error", "添加特效失败: " + e.getMessage());
            errorResponse.put("error_code", "ADD_EFFECTS_ERROR");
            errorResponse.put("timestamp", System.currentTimeMillis());
            return ResponseEntity.status(500).body(errorResponse);
        }
    }

    /**
     * 批量添加字幕
     */
    @ApiOperation(value = "批量添加字幕", notes = "向草稿中批量添加字幕")
    @PostMapping("/add_captions")
    public Object addCaptions(@Valid @RequestBody AddCaptionsRequest request) {

        try {
            log.info("批量添加字幕请求: {}", request.getSummary());

            // 调用Service处理
            JSONObject result = jianyingAssistantService.addCaptions(request);

            // 检查Service返回的结果是否包含错误
            if (result.containsKey("error")) {
                // Service层返回了错误，转换为规范的错误响应
                JSONObject errorResponse = new JSONObject();
                errorResponse.put("success", false);
                errorResponse.put("error", result.getString("error"));
                errorResponse.put("error_code", "ADD_CAPTIONS_ERROR");
                errorResponse.put("timestamp", System.currentTimeMillis());
                return errorResponse;
            }

            // 正常结果，直接返回
            return result;

        } catch (Exception e) {
            log.error("批量添加字幕失败", e);

            // 返回规范的错误响应（添加错误码）
            JSONObject errorResult = new JSONObject();
            errorResult.put("success", false);
            errorResult.put("error", "批量添加字幕失败: " + e.getMessage());
            errorResult.put("error_code", "ADD_CAPTIONS_ERROR");  // 添加错误码
            errorResult.put("timestamp", System.currentTimeMillis());
            return errorResult;
        }
    }

    /**
     * 批量添加图片
     */
    @ApiOperation(value = "批量添加图片", notes = "向草稿中批量添加图片")
    @PostMapping("/add_images")
    public Object addImages(@Valid @RequestBody AddImagesRequest request) {

        try {
            log.info("批量添加图片请求: {}", request.getSummary());

            JSONObject result = jianyingAssistantService.addImages(request);

            // 检查是否有错误字段（新格式）
            if (result.containsKey("error")) {
                // 错误情况返回统一格式
                return ResponseEntity.status(500).body(result);
            } else {
                // 成功情况直接返回Service的结果（已经是正确格式：{track_id, draft_url, image_ids, segment_ids, segment_infos}）
                return ResponseEntity.ok(result);
            }

        } catch (Exception e) {
            log.error("批量添加图片失败", e);
            JSONObject errorResponse = new JSONObject();
            errorResponse.put("success", false);
            errorResponse.put("error", "批量添加图片失败: " + e.getMessage());
            errorResponse.put("error_code", "ADD_IMAGES_ERROR");
            errorResponse.put("timestamp", System.currentTimeMillis());
            return ResponseEntity.status(500).body(errorResponse);
        }
    }

    /**
     * 批量添加视频（新实现 - 四个核心字段返回格式）
     *
     * 备份说明：原有实现已备份在addVideos_backup方法中
     * 新实现返回四个核心字段：video_ids, draft_url, segment_ids, track_id
     */
    @ApiOperation(value = "批量添加视频", notes = "向草稿中批量添加视频")
    @PostMapping("/add_videos")
    public Object addVideos(@Valid @RequestBody AddVideosRequest request) {
        try {
            log.info("批量添加视频请求: {}", request.getSummary());

            JSONObject result = jianyingAssistantService.addVideos(request);

            // 检查是否有错误字段
            if (result.containsKey("error")) {
                // 错误情况返回错误信息
                return ResponseEntity.status(500).body(result);
            } else {
                // 成功情况直接返回四个核心字段
                return ResponseEntity.ok(result);
            }

        } catch (Exception e) {
            log.error("批量添加视频失败", e);
            JSONObject errorResponse = new JSONObject();
            errorResponse.put("success", false);
            errorResponse.put("error", "批量添加视频失败: " + e.getMessage());
            errorResponse.put("error_code", "ADD_VIDEOS_ERROR");
            errorResponse.put("timestamp", System.currentTimeMillis());
            return ResponseEntity.status(500).body(errorResponse);
        }
    }

    /**
     * 创建文本富文本样式
     */
    @ApiOperation(value = "创建文本富文本样式", notes = "创建文本富文本样式")
    @PostMapping("/add_text_style")
    public Object addTextStyle(@Valid @RequestBody AddTextStyleRequest request) {

        try {
            log.info("创建文本样式请求: {}", request.getSummary());

            JSONObject result = jianyingAssistantService.addTextStyle(request);

            // 检查是否有错误字段（新格式）
            if (result.containsKey("error")) {
                // 错误情况返回统一格式
                return ResponseEntity.status(500).body(result);
            } else {
                // 成功情况直接返回Service的结果（已经是正确格式：{text_style: "..."}）
                return ResponseEntity.ok(result);
            }

        } catch (Exception e) {
            log.error("创建文本样式失败", e);
            JSONObject errorResponse = new JSONObject();
            errorResponse.put("error", "创建文本样式失败: " + e.getMessage());
            return ResponseEntity.status(500).body(errorResponse);
        }
    }

    /**
     * 增加蒙版
     */
    @ApiOperation(value = "增加蒙版", notes = "为指定的segment添加蒙版效果")
    @PostMapping("/add_masks")
    public ResponseEntity<?> addMasks(@Valid @RequestBody AddMasksRequest request) {
        try {
            log.info("增加蒙版请求: {}", request.getSummary());

            // 调用service方法处理
            JSONObject result = jianyingAssistantService.addMasks(request);

            // 直接返回竞争对手格式，不使用Result包装
            return ResponseEntity.ok(result.toJSONString());

        } catch (org.jeecg.modules.jianying.exception.JianyingParameterException e) {
            // 剪映参数验证错误 - 返回400状态码
            log.warn("增加蒙版参数错误: {}", e.getMessage());

            JSONObject errorResponse = new JSONObject();
            errorResponse.put("error", e.getMessage());

            return ResponseEntity.status(400).body(errorResponse.toJSONString());

        } catch (IllegalArgumentException | javax.validation.ValidationException e) {
            // 通用参数验证错误 - 返回400状态码
            log.warn("增加蒙版参数错误: {}", e.getMessage());

            JSONObject errorResponse = new JSONObject();
            errorResponse.put("error", e.getMessage());

            return ResponseEntity.status(400).body(errorResponse.toJSONString());

        } catch (RuntimeException e) {
            // 业务处理错误 - 返回500状态码
            log.error("增加蒙版业务处理失败", e);

            JSONObject errorResponse = new JSONObject();
            errorResponse.put("error", "增加蒙版失败: " + e.getMessage());

            return ResponseEntity.status(500).body(errorResponse.toJSONString());

        } catch (Exception e) {
            // 其他未知错误 - 返回500状态码
            log.error("增加蒙版系统错误", e);

            JSONObject errorResponse = new JSONObject();
            errorResponse.put("error", "增加蒙版失败: " + e.getMessage());

            return ResponseEntity.status(500).body(errorResponse.toJSONString());
        }
    }

    /**
     * 保存草稿（简化版：输入什么就返回什么）
     */
    @ApiOperation(value = "保存草稿", notes = "保存草稿，返回输入的URL和导入指导信息")
    @PostMapping("/save_draft")
    public Object saveDraft(@Valid @RequestBody SaveDraftRequest request) {

        try {
            log.info("保存草稿请求: {}", request.getSummary());

            JSONObject result = jianyingAssistantService.saveDraft(request);

            // 检查是否有错误字段
            if (result.containsKey("error")) {
                // 错误情况返回500状态码和错误信息
                return ResponseEntity.status(500).body(result);
            } else {
                // 成功情况直接返回结果，不包装在Result中
                return ResponseEntity.ok(result);
            }

        } catch (Exception e) {
            log.error("保存草稿失败", e);
            // Controller层异常也返回错误格式：{"error": "错误信息"}
            JSONObject errorResponse = new JSONObject();
            errorResponse.put("error", "保存草稿失败: " + e.getMessage());
            return ResponseEntity.status(500).body(errorResponse);
        }
    }

    /**
     * 添加关键帧
     */
    @ApiOperation(value = "添加关键帧", notes = "向草稿中添加关键帧动画")
    @PostMapping("/add_keyframes")
    public Object addKeyframes(@Valid @RequestBody AddKeyframesRequest request) {

        try {
            log.info("添加关键帧请求: {}", request.getSummary());

            JSONObject result = jianyingAssistantService.addKeyframes(request.getZjKeyframes(), request.getZjDraftUrl());

            // 直接返回Service的结果，让系统自动处理
            return result;

        } catch (Exception e) {
            log.error("添加关键帧失败", e);
            JSONObject errorResponse = new JSONObject();
            errorResponse.put("error", "添加关键帧失败: " + e.getMessage());
            return errorResponse;
        }
    }

    /**
     * 获取文字出入场动画
     */
    @ApiOperation(value = "获取文字出入场动画", notes = "获取文字出入场动画列表")
    @PostMapping("/get_text_animations")
    public Object getTextAnimations(@Valid @RequestBody GetTextAnimationsRequest request) {
        try {
            log.info("获取文字出入场动画请求: {}", request.getSummary());

            JSONObject result = jianyingAssistantService.getTextAnimations(request);

            // 检查是否有错误
            if (result.containsKey("error")) {
                return Result.error(result.getString("error"));
            }

            // 直接返回竞争对手格式：{effects: [...]}
            return ResponseEntity.ok(result);

        } catch (Exception e) {
            log.error("获取文字动画失败", e);
            return Result.error("获取文字动画失败: " + e.getMessage());
        }
    }

    /**
     * 查询视频状态
     */
    @ApiOperation(value = "查询视频状态", notes = "查询云渲染视频的状态")
    @PostMapping("/gen_video_status")
    public Result<?> genVideoStatus(@Valid @RequestBody GenVideoStatusRequest request) {

        try {
            log.info("查询视频状态请求: {}", request.getSummary());

            JSONObject result = jianyingAssistantService.genVideoStatus(request);

            if (result.getBoolean("success")) {
                return Result.OK("查询状态成功", result.getJSONObject("data"));
            } else {
                return Result.error(result.getString("error"));
            }

        } catch (Exception e) {
            log.error("查询视频状态失败", e);
            return Result.error("查询视频状态失败: " + e.getMessage());
        }
    }

    /**
     * 健康检查接口
     */
    @ApiOperation(value = "健康检查", notes = "检查剪映小助手API服务状态")
    @GetMapping("/health")
    public Result<?> health() {
        JSONObject result = new JSONObject();
        result.put("status", "healthy");
        result.put("service", "剪映小助手API");
        result.put("timestamp", System.currentTimeMillis());
        result.put("version", "1.0.0");
        return Result.OK("服务正常", result);
    }

    /**
     * 搜索背景音乐
     */
    @ApiOperation(value = "搜索背景音乐", notes = "搜索剪映背景音乐库")
    @PostMapping("/bgm_search")
    public Object bgmSearch(@Valid @RequestBody BgmSearchRequest request) {
        try {
            log.info("搜索背景音乐请求: {}", request.getSummary());

            // 验证剪映访问密钥（注意：这里不需要额外验证，因为@JianyingAccessKey注解已经处理了）
            // 如果执行到这里，说明访问密钥验证已经通过

            // 调用服务层方法
            Object result = jianyingAssistantService.bgmSearch(request);
            return ResponseEntity.ok(result);

        } catch (Exception e) {
            log.error("搜索背景音乐失败", e);
            JSONObject errorResponse = new JSONObject();
            errorResponse.put("data", new Object[0]);
            errorResponse.put("message", "搜索背景音乐失败: " + e.getMessage());
            errorResponse.put("success", false);
            return ResponseEntity.status(500).body(errorResponse);
        }
    }

    /**
     * 搜索背景音效
     */
    @ApiOperation(value = "搜索背景音效", notes = "搜索剪映背景音效库")
    @PostMapping("/sound_effects_search")
    public Object soundEffectsSearch(@Valid @RequestBody SoundEffectsSearchRequest request) {
        try {
            log.info("搜索背景音效请求: {}", request.getSummary());

            // 验证剪映访问密钥（注意：这里不需要额外验证，因为@JianyingAccessKey注解已经处理了）
            // 如果执行到这里，说明访问密钥验证已经通过

            // 调用服务层方法
            Object result = jianyingAssistantService.soundEffectsSearch(request);
            return ResponseEntity.ok(result);

        } catch (Exception e) {
            log.error("搜索背景音效失败", e);
            JSONObject errorResponse = new JSONObject();
            errorResponse.put("data", new Object[0]);
            errorResponse.put("message", "搜索背景音效失败: " + e.getMessage());
            errorResponse.put("success", false);
            return ResponseEntity.status(500).body(errorResponse);
        }
    }

    /**
     * 清空动画缓存（解决序列化问题）
     */
    @ApiOperation(value = "清空动画缓存", notes = "清空Redis中的动画缓存数据，解决序列化格式不兼容问题")
    @PostMapping("/clear_animation_cache")
    public Result<?> clearAnimationCache() {
        try {
            log.info("清空动画缓存请求");

            jianyingIdResolverService.clearAllAnimationCache();

            return Result.OK("动画缓存清空成功");

        } catch (Exception e) {
            log.error("清空动画缓存失败", e);
            return Result.error("清空动画缓存失败: " + e.getMessage());
        }
    }

    /**
     * 批量添加视频（原有实现备份）
     *
     * 备份时间：2025-01-08
     * 备份原因：重构add_videos接口以匹配add_audios的返回格式
     * 原有实现特点：使用Result.OK()和Result.error()返回格式
     */
    @Deprecated
    public Result<?> addVideos_backup(@Valid @RequestBody AddVideosRequest request) {
        try {
            log.info("批量添加视频请求: {}", request.getSummary());

            JSONObject result = jianyingAssistantService.addVideos(request);

            if (result.getBoolean("success")) {
                return Result.OK("视频添加成功", result.getJSONObject("data"));
            } else {
                return Result.error(result.getString("error"));
            }

        } catch (Exception e) {
            log.error("批量添加视频失败", e);
            return Result.error("批量添加视频失败: " + e.getMessage());
        }
    }
}
