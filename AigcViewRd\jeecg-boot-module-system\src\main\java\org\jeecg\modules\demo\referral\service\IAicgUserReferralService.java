package org.jeecg.modules.demo.referral.service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import org.jeecg.modules.demo.referral.entity.AicgUserReferral;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * @Description: 用户推荐关系表
 * @Author: jeecg-boot
 * @Date:   2025-06-18
 * @Version: V1.0
 */
public interface IAicgUserReferralService extends IService<AicgUserReferral> {

    /**
     * 根据推荐人ID查询推荐记录
     * @param referrerId 推荐人ID
     * @return 推荐记录列表
     */
    List<AicgUserReferral> getByReferrerId(String referrerId);
    
    /**
     * 根据被推荐人ID查询推荐记录
     * @param refereeId 被推荐人ID
     * @return 推荐记录
     */
    AicgUserReferral getByRefereeId(String refereeId);
    
    /**
     * 根据推荐码查询推荐记录
     * @param referralCode 推荐码
     * @return 推荐记录
     */
    AicgUserReferral getByReferralCode(String referralCode);
    
    /**
     * 创建推荐关系
     * @param referrerId 推荐人ID
     * @param refereeId 被推荐人ID
     * @param referralCode 推荐码
     * @param operatorId 操作人ID
     * @return 创建的推荐关系
     */
    AicgUserReferral createReferral(String referrerId, String refereeId, String referralCode, String operatorId);
    
    /**
     * 生成推荐码
     * @param userId 用户ID
     * @return 推荐码
     */
    String generateReferralCode(String userId);
    
    /**
     * 确认推荐关系（被推荐人首次充值时调用）
     * @param refereeId 被推荐人ID
     * @param rechargeAmount 充值金额
     * @param operatorId 操作人ID
     * @return 是否成功
     */
    boolean confirmReferral(String refereeId, BigDecimal rechargeAmount, String operatorId);
    
    /**
     * 标记推荐关系为已奖励
     * @param id 推荐关系ID
     * @param operatorId 操作人ID
     * @return 是否成功
     */
    boolean markAsRewarded(String id, String operatorId);
    
    /**
     * 统计推荐人的推荐数据
     * @param referrerId 推荐人ID
     * @return 推荐统计数据
     */
    Map<String, Object> getReferralStats(String referrerId);
    
    /**
     * 查询待确认的推荐记录
     * @return 待确认的推荐记录列表
     */
    List<AicgUserReferral> getPendingReferrals();
    
    /**
     * 查询已确认但未奖励的推荐记录
     * @return 已确认但未奖励的推荐记录列表
     */
    List<AicgUserReferral> getConfirmedReferrals();
    
    /**
     * 验证推荐码是否有效
     * @param referralCode 推荐码
     * @return 是否有效
     */
    boolean validateReferralCode(String referralCode);
}
