<template>
  <div class="withdrawal-management">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>提现管理</h2>
      <p>管理用户提现申请，审核通过或拒绝申请</p>
    </div>

    <!-- 搜索筛选区域 -->
    <div class="search-section">
      <a-card :bordered="false">
        <a-form layout="inline" :model="searchForm" @submit="handleSearch">
          <a-form-item label="申请状态">
            <a-select v-model="searchForm.status" placeholder="请选择状态" style="width: 120px" allowClear>
              <a-select-option :value="1">待审核</a-select-option>
              <a-select-option :value="2">已发放</a-select-option>
              <a-select-option :value="3">审核拒绝</a-select-option>
              <a-select-option :value="4">已取消</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="申请时间">
            <a-range-picker
              v-model="searchForm.dateRange"
              format="YYYY-MM-DD"
              :placeholder="['开始时间', '结束时间']"
            />
          </a-form-item>
          <a-form-item label="用户名">
            <a-input v-model="searchForm.username" placeholder="请输入用户名" style="width: 150px" />
          </a-form-item>

          <a-form-item label="支付宝信息">
            <a-input v-model="searchForm.alipayInfo" placeholder="支付宝账号或姓名" style="width: 150px" />
          </a-form-item>

          <a-form-item>
            <a-button type="primary" @click="handleSearch" :loading="loading">
              <a-icon type="search" />
              搜索
            </a-button>
            <a-button @click="handleReset" style="margin-left: 8px">
              <a-icon type="reload" />
              重置
            </a-button>
          </a-form-item>
        </a-form>
      </a-card>
    </div>

    <!-- 数据表格 -->
    <div class="table-section">
      <a-card :bordered="false">
        <!-- 表格 -->
        <a-table
          :columns="columns"
          :data-source="dataSource"
          :loading="loading"
          :pagination="pagination"
          row-key="id"
          @change="handleTableChange"
          :scroll="{ x: 1200 }"
        >
          <!-- 用户信息列 -->
          <template slot="userInfo" slot-scope="text, record">
            <div class="user-info" v-if="record">
              <div class="username">{{ record.username || '-' }}</div>
              <div class="user-id">ID: {{ record.user_id || '-' }}</div>
            </div>
            <span v-else>-</span>
          </template>

          <!-- 提现金额列 -->
          <template slot="amount" slot-scope="text, record">
            <div class="amount-info" v-if="record">
              <div class="amount">¥{{ formatNumber(record.withdrawal_amount) }}</div>
            </div>
            <span v-else>-</span>
          </template>

          <!-- 支付宝信息列 -->
          <template slot="alipayInfo" slot-scope="text, record">
            <div class="alipay-info" v-if="record">
              <div class="name">{{ record.alipay_name || '-' }}</div>
              <div class="account">{{ record.alipay_account || '-' }}</div>
            </div>
            <span v-else>-</span>
          </template>

          <!-- 状态列 -->
          <template slot="status" slot-scope="text, record">
            <a-tag :color="getStatusColor(record && record.status)" v-if="record">
              {{ getStatusText(record.status, record.review_remark) }}
            </a-tag>
            <span v-else>-</span>
          </template>

          <!-- 申请时间列 -->
          <template slot="applyTime" slot-scope="text, record">
            <span>{{ record && record.apply_time ? formatDateTime(record.apply_time) : '-' }}</span>
          </template>

          <!-- 审核时间列 -->
          <template slot="reviewTime" slot-scope="text, record">
            <span>{{ record && record.review_time ? formatDateTime(record.review_time) : '-' }}</span>
          </template>

          <!-- 操作列 -->
          <template slot="action" slot-scope="text, record">
            <div class="action-buttons" v-if="record">
              <a-button
                v-if="record.status === 1"
                type="primary"
                size="small"
                @click="handleApprove(record)"
                :loading="record.approving"
              >
                审核通过
              </a-button>

              <a-button
                v-if="record.status === 1"
                type="danger"
                size="small"
                @click="handleReject(record)"
                :loading="record.rejecting"
                style="margin-left: 8px"
              >
                审核拒绝
              </a-button>

              <a-button
                size="small"
                @click="handleViewDetail(record)"
                style="margin-left: 8px"
              >
                查看详情
              </a-button>
            </div>
            <span v-else>-</span>
          </template>
        </a-table>
      </a-card>
    </div>

    <!-- 审核拒绝原因弹窗 -->
    <a-modal
      v-model="showRejectModal"
      title="审核拒绝"
      :footer="null"
      width="500px"
    >
      <div class="reject-modal">
        <a-alert 
          message="请填写拒绝原因" 
          type="warning" 
          show-icon 
          style="margin-bottom: 20px"
        />
        
        <a-form layout="vertical">
          <a-form-item label="拒绝原因" required>
            <a-textarea 
              v-model="rejectReason" 
              placeholder="请输入拒绝原因"
              :rows="4"
              :maxLength="200"
            />
          </a-form-item>
        </a-form>
        
        <div class="modal-actions">
          <a-button @click="showRejectModal = false">
            取消
          </a-button>
          <a-button 
            type="danger" 
            @click="confirmReject"
            :loading="rejecting"
            :disabled="!rejectReason.trim()"
            style="margin-left: 10px"
          >
            确认拒绝
          </a-button>
        </div>
      </div>
    </a-modal>

    <!-- 详情弹窗 -->
    <a-modal
      v-model="showDetailModal"
      title="提现申请详情"
      :footer="null"
      width="600px"
    >
      <div class="detail-modal" v-if="currentRecord">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="申请ID">
            {{ currentRecord.id }}
          </a-descriptions-item>
          <a-descriptions-item label="用户名">
            {{ currentRecord.username }}
          </a-descriptions-item>
          <a-descriptions-item label="提现金额">
            <span class="amount-text">¥{{ formatNumber(currentRecord.withdrawal_amount) }}</span>
          </a-descriptions-item>
          <a-descriptions-item label="申请状态">
            <a-tag :color="getStatusColor(currentRecord.status)">
              {{ getStatusText(currentRecord.status, currentRecord.review_remark) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="真实姓名">
            {{ currentRecord.alipay_name }}
          </a-descriptions-item>
          <a-descriptions-item label="支付宝账号">
            {{ currentRecord.alipay_account }}
          </a-descriptions-item>
          <a-descriptions-item label="申请时间">
            {{ currentRecord.apply_time ? formatDateTime(currentRecord.apply_time) : '-' }}
          </a-descriptions-item>
          <a-descriptions-item label="审核时间">
            {{ currentRecord.review_time ? formatDateTime(currentRecord.review_time) : '-' }}
          </a-descriptions-item>
          <a-descriptions-item label="审核人" v-if="currentRecord.review_by">
            {{ currentRecord.review_by }}
          </a-descriptions-item>
          <a-descriptions-item label="审核备注" v-if="currentRecord.review_remark">
            {{ currentRecord.review_remark }}
          </a-descriptions-item>
        </a-descriptions>
      </div>
    </a-modal>
  </div>
</template>

<script>
export default {
  name: 'AigcWithdrawalList',
  data() {
    return {
      loading: false,
      // 搜索表单
      searchForm: {
        status: undefined,
        dateRange: [],
        username: '',
        alipayInfo: ''
      },
      // 表格数据
      dataSource: [],
      // 分页
      pagination: {
        current: 1,
        pageSize: 10,
        total: 0,
        showSizeChanger: true,
        showQuickJumper: true,
        showTotal: (total) => `共 ${total} 条记录`
      },
      // 表格列定义
      columns: [
        {
          title: '用户信息',
          key: 'userInfo',
          width: 150,
          scopedSlots: { customRender: 'userInfo' }
        },
        {
          title: '提现金额',
          key: 'amount',
          width: 120,
          align: 'right',
          scopedSlots: { customRender: 'amount' }
        },
        {
          title: '支付宝信息',
          key: 'alipayInfo',
          width: 180,
          scopedSlots: { customRender: 'alipayInfo' }
        },
        {
          title: '申请时间',
          dataIndex: 'apply_time',
          key: 'applyTime',
          width: 150,
          scopedSlots: { customRender: 'applyTime' }
        },
        {
          title: '审核时间',
          dataIndex: 'review_time',
          key: 'reviewTime',
          width: 150,
          scopedSlots: { customRender: 'reviewTime' }
        },
        {
          title: '状态',
          dataIndex: 'status',
          key: 'status',
          width: 100,
          scopedSlots: { customRender: 'status' }
        },
        {
          title: '操作',
          key: 'action',
          width: 280,
          fixed: 'right',
          scopedSlots: { customRender: 'action' }
        }
      ],
      // 弹窗状态
      showRejectModal: false,
      showDetailModal: false,
      currentRecord: null,
      rejectReason: '',
      rejecting: false
    }
  },
  mounted() {
    this.loadData()
  },
  methods: {
    // 加载数据
    async loadData() {
      try {
        this.loading = true

        const params = {
          current: this.pagination.current,
          size: this.pagination.pageSize,
          ...this.getSearchParams()
        }

        const response = await this.$http.get('/api/usercenter/admin/withdrawalList', { params })
        console.log('提现列表完整响应:', response)

        // 根据实际返回的数据结构处理
        const data = response.data || response
        console.log('提现列表数据:', data)

        if (data && data.success) {
          this.dataSource = data.result.records || []
          this.pagination.total = data.result.total || 0
          console.log('数据加载成功:', this.dataSource.length, '条记录')
          console.log('完整result结构:', data.result)
          console.log('records数组:', data.result.records)
          console.log('第一条数据结构:', this.dataSource[0])
          console.log('第一条数据的所有属性:', Object.keys(this.dataSource[0] || {}))

          // 打印每个字段的值
          if (this.dataSource[0]) {
            const firstRecord = this.dataSource[0]
            console.log('字段值详情:')
            Object.keys(firstRecord).forEach(key => {
              console.log(`  ${key}:`, firstRecord[key])
            })
          }
        } else {
          const errorMsg = (data && data.message) || '获取数据失败'
          this.$message.error(errorMsg)
          this.dataSource = []
          this.pagination.total = 0
        }
      } catch (error) {
        console.error('加载提现数据失败:', error)
        this.$message.error('加载数据失败')
      } finally {
        this.loading = false
      }
    },

    // 获取搜索参数
    getSearchParams() {
      const params = {}

      if (this.searchForm.status !== undefined) {
        params.status = this.searchForm.status
      }

      if (this.searchForm.username) {
        params.username = this.searchForm.username.trim()
      }

      if (this.searchForm.alipayInfo) {
        params.alipayInfo = this.searchForm.alipayInfo.trim()
      }

      if (this.searchForm.dateRange && this.searchForm.dateRange.length === 2) {
        params.startDate = this.searchForm.dateRange[0].format('YYYY-MM-DD')
        params.endDate = this.searchForm.dateRange[1].format('YYYY-MM-DD')
      }

      return params
    },

    // 搜索
    handleSearch() {
      this.pagination.current = 1
      this.loadData()
    },

    // 重置搜索
    handleReset() {
      this.searchForm = {
        status: undefined,
        dateRange: [],
        username: '',
        alipayInfo: ''
      }
      this.pagination.current = 1
      this.loadData()
    },

    // 表格变化
    handleTableChange(pagination) {
      this.pagination = { ...this.pagination, ...pagination }
      this.loadData()
    },

    // 审核通过
    async handleApprove(record) {
      this.$confirm({
        title: '确认审核通过',
        content: `确定要审核通过用户 ${record.username} 的提现申请吗？\n提现金额：¥${this.formatNumber(record.withdrawal_amount)}`,
        onOk: async () => {
          try {
            this.$set(record, 'approving', true)

            const response = await this.$http.post('/api/usercenter/admin/approveWithdrawal', {
              id: record.id
            })

            // 根据实际返回的数据结构处理
            const data = response.data || response

            if (data.success) {
              this.$message.success('审核通过成功')
              this.loadData()
            } else {
              this.$message.error(data.message || '审核失败')
            }
          } catch (error) {
            console.error('审核通过失败:', error)
            this.$message.error('审核失败，请重试')
          } finally {
            this.$set(record, 'approving', false)
          }
        }
      })
    },

    // 审核拒绝
    handleReject(record) {
      this.currentRecord = record
      this.rejectReason = ''
      this.showRejectModal = true
    },

    // 确认拒绝
    async confirmReject() {
      if (!this.rejectReason.trim()) {
        this.$message.warning('请填写拒绝原因')
        return
      }

      try {
        this.rejecting = true

        const response = await this.$http.post('/api/usercenter/admin/rejectWithdrawal', {
          id: this.currentRecord.id,
          reason: this.rejectReason.trim()
        })

        // 根据实际返回的数据结构处理
        const data = response.data || response

        if (data.success) {
          this.$message.success('审核拒绝成功')
          this.showRejectModal = false
          this.loadData()
        } else {
          this.$message.error(data.message || '审核失败')
        }
      } catch (error) {
        console.error('审核拒绝失败:', error)
        this.$message.error('审核失败，请重试')
      } finally {
        this.rejecting = false
      }
    },

    // 查看详情
    handleViewDetail(record) {
      this.currentRecord = record
      this.showDetailModal = true
    },

    // 获取状态颜色
    getStatusColor(status) {
      const colorMap = {
        1: 'orange',      // 待审核 - 橙色
        2: 'green',       // 已发放 - 绿色
        3: 'red',         // 审核拒绝 - 红色
        4: 'gray' // 已取消 - 灰色
      }
      return colorMap[status] || 'volcano' // 未知状态用火山红色
    },

    // 获取状态文本
    getStatusText(status, reviewRemark) {
      const textMap = {
        1: '待审核',
        2: '已发放',
        3: '审核拒绝',
        4: '已取消'
      }
      let statusText = textMap[status] || '未知状态'

      // 如果是审核拒绝状态且有拒绝原因，则添加原因
      if (status === 3 && reviewRemark) {
        statusText += `（${reviewRemark}）`
      }

      return statusText
    },

    // 格式化数字
    formatNumber(number) {
      if (!number) return '0.00'
      return parseFloat(number).toFixed(2)
    },

    // 格式化日期时间
    formatDateTime(dateString) {
      if (!dateString) return '-'

      try {
        const date = new Date(dateString)
        return date.toLocaleString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit'
        })
      } catch (error) {
        return '-'
      }
    }
  }
}
</script>

<style lang="less" scoped>
.withdrawal-management {
  padding: 24px;
  background: #f0f2f5;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 24px;

  h2 {
    margin: 0 0 8px 0;
    font-size: 24px;
    font-weight: 600;
    color: #262626;
  }

  p {
    margin: 0;
    color: #8c8c8c;
    font-size: 14px;
  }
}

.search-section {
  margin-bottom: 24px;
}

// 表格样式
.ant-table {
  .ant-table-thead > tr > th,
  .ant-table-tbody > tr > td {
    text-align: center !important;
  }
}

// 表格内容样式
.user-info {
  .username {
    font-weight: 500;
    color: #262626;
    margin-bottom: 4px;
  }

  .user-id {
    font-size: 12px;
    color: #8c8c8c;
  }
}

.amount-info {
  .amount {
    font-weight: 600;
    color: #262626;
    font-family: 'Courier New', monospace;
    font-size: 16px;
  }
}

.alipay-info {
  .name {
    font-weight: 500;
    color: #262626;
    margin-bottom: 4px;
  }

  .account {
    font-size: 12px;
    color: #8c8c8c;
    font-family: 'Courier New', monospace;
  }
}

.action-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  justify-content: center;
}

// 弹窗样式
.reject-modal {
  .modal-actions {
    text-align: center;
    margin-top: 20px;
  }
}

.detail-modal {
  .amount-text {
    font-weight: 600;
    color: #52c41a;
    font-family: 'Courier New', monospace;
    font-size: 16px;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .withdrawal-management {
    padding: 16px;
  }

  .action-buttons {
    flex-direction: column;

    .ant-btn {
      width: 100%;
    }
  }
}
</style>
