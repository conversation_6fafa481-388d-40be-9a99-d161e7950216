package org.jeecg.modules.jianying.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;

/**
 * 批量添加视频请求参数
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AddVideosRequest extends BaseJianyingRequest {
    
    @ApiModelProperty(value = "草稿地址，使用create_draft输出的draft_url即可（必填）", required = true,
                     example = "https://example.com/draft/123")
    @NotBlank(message = "draft_url不能为空")
    @JsonProperty("draft_url")
    private String zjDraftUrl;

    @ApiModelProperty(value = "视频信息（必填）", required = true,
                     example = "[{\"video_url\": \"https://example.com/video1.mp4\", \"duration\": 10000000, \"start\": 0, \"end\": 10000000}]")
    @NotBlank(message = "video_infos不能为空")
    @JsonProperty("video_infos")
    private String zjVideoInfos;

    @ApiModelProperty(value = "透明度，范围0-1（可选）", example = "1.0")
    @JsonProperty("alpha")
    @DecimalMin(value = "0.0", message = "透明度不能小于0")
    @DecimalMax(value = "1.0", message = "透明度不能大于1")
    private Double zjAlpha;

    @ApiModelProperty(value = "X轴缩放（可选）", example = "1.0")
    @JsonProperty("scale_x")
    @DecimalMin(value = "0.1", message = "X轴缩放不能小于0.1")
    @DecimalMax(value = "10.0", message = "X轴缩放不能大于10")
    private Double zjScaleX;

    @ApiModelProperty(value = "Y轴缩放（可选）", example = "1.0")
    @JsonProperty("scale_y")
    @DecimalMin(value = "0.1", message = "Y轴缩放不能小于0.1")
    @DecimalMax(value = "10.0", message = "Y轴缩放不能大于10")
    private Double zjScaleY;

    @ApiModelProperty(value = "X轴移动位置（可选）", example = "0.0")
    @JsonProperty("transform_x")
    @DecimalMin(value = "-2000.0", message = "X轴位置不能小于-2000")
    @DecimalMax(value = "2000.0", message = "X轴位置不能大于2000")
    private Double zjTransformX;

    @ApiModelProperty(value = "Y轴移动位置（可选）", example = "0.0")
    @JsonProperty("transform_y")
    @DecimalMin(value = "-2000.0", message = "Y轴位置不能小于-2000")
    @DecimalMax(value = "2000.0", message = "Y轴位置不能大于2000")
    private Double zjTransformY;
    
    @Override
    public String getSummary() {
        return "AddVideosRequest{" +
               "accessKey=" + (getAccessKey() != null && getAccessKey().length() > 10 ?
                              getAccessKey().substring(0, 10) + "***" : "null") +
               ", draftUrl=" + (zjDraftUrl != null && zjDraftUrl.length() > 30 ?
                               zjDraftUrl.substring(0, 30) + "***" : zjDraftUrl) +
               ", videoInfos=" + (zjVideoInfos != null && zjVideoInfos.length() > 50 ?
                                zjVideoInfos.substring(0, 50) + "***" : zjVideoInfos) +
               ", alpha=" + zjAlpha +
               ", scaleX=" + zjScaleX +
               ", scaleY=" + zjScaleY +
               ", transformX=" + zjTransformX +
               ", transformY=" + zjTransformY +
               "}";
    }
}
