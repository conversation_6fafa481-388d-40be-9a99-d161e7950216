{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\workflow\\components\\CreatorCenter.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\workflow\\components\\CreatorCenter.vue", "mtime": 1754512989758}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport RevenueStatsCards from './RevenueStatsCards.vue'\nimport RevenueRanking from './RevenueRanking.vue'\nimport AgentManagement from './AgentManagement.vue'\nimport CreatorAgentForm from './CreatorAgentForm.vue'\n// 🔥 导入提现相关组件\nimport WithdrawModal from '@/components/withdraw/WithdrawModal.vue'\nimport WithdrawRecordsModal from '@/components/withdraw/WithdrawRecordsModal.vue'\nimport { getCreatorAgents, createAgent, updateAgent, deleteAgent, getRevenueStats } from '@/api/creator-agent'\n\nexport default {\n  name: 'CreatorCenter',\n  components: {\n    RevenueStatsCards,\n    RevenueRanking,\n    AgentManagement,\n    CreatorAgentForm,\n    // 🔥 注册提现相关组件\n    WithdrawModal,\n    WithdrawRecordsModal\n  },\n  data() {\n    return {\n      // 加载状态\n      creating: false,\n      statsLoading: false,\n      agentsLoading: false,\n      formLoading: false,\n      \n      // 收益统计数据\n      revenueStats: {},\n      \n      // 智能体列表数据\n      agentList: [],\n      pagination: {\n        current: 1,\n        pageSize: 12,\n        total: 0\n      },\n      \n      // 筛选和搜索\n      searchQuery: '',\n      auditStatusFilter: '',\n\n      // 排序相关\n      sortField: 'totalRevenue',\n      sortOrder: 'desc',\n      \n      // 表单相关\n      formVisible: false,\n      formMode: 'create', // 'create' | 'edit'\n      currentAgent: null,\n      \n\n\n      // 🔥 提现相关\n      showWithdrawModal: false,\n      showWithdrawRecordsModal: false,\n      withdrawLoading: false,\n      withdrawAvailableAmount: 0,\n      withdrawRevenueType: 'agent'\n    }\n  },\n  \n  async created() {\n    await this.initData()\n  },\n  \n  methods: {\n    // 初始化数据\n    async initData() {\n      await Promise.all([\n        this.loadRevenueStats(),\n        this.loadAgentList()\n      ])\n    },\n    \n    // 加载收益统计\n    async loadRevenueStats() {\n      this.statsLoading = true\n      try {\n        const response = await getRevenueStats()\n        if (response.success) {\n          this.revenueStats = response.result\n        } else {\n          this.$message.error(response.message || '获取收益统计失败')\n        }\n      } catch (error) {\n        console.error('加载收益统计失败:', error)\n        this.$message.error('获取收益统计失败')\n      } finally {\n        this.statsLoading = false\n      }\n    },\n    \n    // 加载智能体列表\n    async loadAgentList(resetPage = false) {\n      if (resetPage) {\n        this.pagination.current = 1\n      }\n      \n      this.agentsLoading = true\n      try {\n        const params = {\n          pageNo: this.pagination.current,\n          pageSize: this.pagination.pageSize,\n          agentName: this.searchQuery || undefined,\n          auditStatus: this.auditStatusFilter || undefined,\n          sortField: this.sortField,\n          sortOrder: this.sortOrder\n        }\n        \n        const response = await getCreatorAgents(params)\n        if (response.success) {\n          this.agentList = response.result.records || []\n          this.pagination.total = response.result.total || 0\n        } else {\n          this.$message.error(response.message || '获取智能体列表失败')\n        }\n      } catch (error) {\n        console.error('加载智能体列表失败:', error)\n        this.$message.error('获取智能体列表失败')\n      } finally {\n        this.agentsLoading = false\n      }\n    },\n    \n    // 新增智能体\n    handleCreateAgent() {\n      this.formMode = 'create'\n      this.currentAgent = null\n      this.formVisible = true\n    },\n    \n    // 编辑智能体\n    handleEditAgent(agent) {\n      this.formMode = 'edit'\n      this.currentAgent = { ...agent }\n      this.formVisible = true\n    },\n    \n    // 删除智能体\n    async handleDeleteAgent(agent) {\n      const self = this\n      this.$confirm({\n        title: '确认删除',\n        content: `确定要删除智能体\"${agent.agentName}\"吗？此操作不可恢复。`,\n        okText: '确定',\n        cancelText: '取消',\n        onOk: async () => {\n          try {\n            const response = await deleteAgent(agent.id)\n            if (response.success) {\n              self.$message.success('删除成功')\n              await self.loadAgentList()\n              await self.loadRevenueStats() // 刷新统计数据\n            } else {\n              self.$message.error(response.message || '删除失败')\n            }\n          } catch (error) {\n            console.error('删除智能体失败:', error)\n            self.$message.error('删除失败')\n          }\n        }\n      })\n    },\n    \n\n    \n    // 智能体搜索\n    handleAgentSearch(searchQuery) {\n      this.searchQuery = searchQuery\n      this.loadAgentList(true)\n    },\n\n    // 智能体筛选\n    handleAgentFilter(filters) {\n      this.auditStatusFilter = filters.auditStatus\n      this.loadAgentList(true)\n    },\n\n    // 排序变化\n    handleSortChange(sortParams) {\n      this.sortField = sortParams.sortField\n      this.sortOrder = sortParams.sortOrder\n      this.loadAgentList(true)\n    },\n\n    // 分页变化\n    handlePageChange(page, pageSize) {\n      this.pagination.current = page\n      this.pagination.pageSize = pageSize\n      this.loadAgentList()\n    },\n    \n    // 关闭表单\n    handleCloseForm() {\n      this.formVisible = false\n      this.currentAgent = null\n    },\n    \n    // 提交表单\n    async handleSubmitForm(formData) {\n      this.formLoading = true\n\n      try {\n        let response\n        if (this.formMode === 'create') {\n          response = await createAgent(formData)\n        } else {\n          response = await updateAgent(this.currentAgent.id, formData)\n        }\n\n        if (response.success) {\n\n          // 🔥 成功后确认删除被替换的原始头像文件（与后台管理系统逻辑一致）\n          if (this.$refs && this.$refs.agentForm && this.$refs.agentForm.confirmDeleteOriginalFiles) {\n            this.$refs.agentForm.confirmDeleteOriginalFiles()\n          }\n\n          this.$message.success(this.formMode === 'create' ? '创建成功' : '更新成功')\n          this.handleCloseForm()\n          await this.loadAgentList()\n          await this.loadRevenueStats() // 刷新统计数据\n        } else {\n          console.error('🎯 CreatorCenter: 智能体操作失败:', response.message)\n          this.$message.error(response.message || '操作失败')\n        }\n      } catch (error) {\n        console.error('🎯 CreatorCenter: 提交表单失败:', error)\n        this.$message.error('操作失败: ' + (error.message || '未知错误'))\n      } finally {\n        this.formLoading = false\n      }\n    },\n    \n\n\n    // 刷新所有数据（供父组件调用）\n    async refreshAllData() {\n      try {\n        await Promise.all([\n          this.loadRevenueStats(),\n          this.loadAgentList()\n        ])\n      } catch (error) {\n        console.error('❌ 创作者中心数据刷新失败:', error)\n      }\n    },\n\n    // 🔥 智能体创建完成\n    handleAgentComplete(agent) {\n      this.handleCloseForm()\n      this.loadAgentList()\n      this.loadRevenueStats()\n    },\n\n\n\n    // 🔥 从表单中删除工作流\n    async handleDeleteWorkflowFromForm(workflow) {\n      console.log('🎯 CreatorCenter: 从表单中删除工作流', workflow)\n      try {\n        // 这里调用删除工作流的API\n        // await deleteWorkflow(workflow.id)\n        this.$message.success('工作流删除成功')\n\n        // 刷新表单中的工作流列表\n        if (this.$refs.agentForm && this.$refs.agentForm.loadWorkflowList) {\n          this.$refs.agentForm.loadWorkflowList(workflow.agentId)\n        }\n      } catch (error) {\n        console.error('🎯 CreatorCenter: 删除工作流失败:', error)\n        this.$message.error('删除工作流失败')\n      }\n    },\n\n    // 🔥 ==================== 提现相关方法 ====================\n\n    // 处理提现\n    handleWithdraw(params) {\n      this.withdrawAvailableAmount = params.availableAmount\n      this.withdrawRevenueType = params.revenueType\n      this.showWithdrawModal = true\n    },\n\n    // 打开提现记录弹窗\n    openWithdrawRecordsModal() {\n      this.showWithdrawRecordsModal = true\n    },\n\n    // 提现申请提交\n    async handleWithdrawSubmit(params) {\n\n      this.withdrawLoading = true\n\n      try {\n        const response = await this.$http.post('/api/usercenter/applyWithdrawal', params)\n\n        if (response.success) {\n          this.$notification.success({\n            message: '提现申请成功',\n            description: '您的提现申请已提交，预计1-3个工作日到账'\n          })\n\n          this.showWithdrawModal = false\n\n          // 刷新收益数据\n          await this.loadRevenueStats()\n        } else {\n          this.$notification.error({\n            message: '提现申请失败',\n            description: response.message || '申请失败，请重试'\n          })\n        }\n      } catch (error) {\n        console.error('🔥 CreatorCenter: 提现申请失败:', error)\n        this.$notification.error({\n          message: '提现申请失败',\n          description: error.message || '申请失败，请重试'\n        })\n      } finally {\n        this.withdrawLoading = false\n      }\n    },\n\n    // 取消提现\n    handleWithdrawCancel() {\n      this.showWithdrawModal = false\n    },\n\n    // 关闭提现记录弹窗\n    handleWithdrawRecordsCancel() {\n      this.showWithdrawRecordsModal = false\n    }\n  }\n}\n", {"version": 3, "sources": ["CreatorCenter.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiHA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "CreatorCenter.vue", "sourceRoot": "src/views/website/workflow/components", "sourcesContent": ["<template>\n  <div class=\"creator-center\">\n    <!-- 页面头部 -->\n    <div class=\"creator-header\">\n      <div class=\"header-content\">\n        <div class=\"header-left\">\n          <h1 class=\"page-title\">\n            <a-icon type=\"user\" />\n            创作者中心\n          </h1>\n          <p class=\"page-subtitle\">管理您的智能体，查看收益统计（全部收益归创作者，但VIP和SVIP用户分别是价格的7折和5折，如您选择发布默认同意此方案）</p>\n        </div>\n        <div class=\"header-right\">\n          <a-button type=\"primary\" size=\"large\" @click=\"handleCreateAgent\" :loading=\"creating\">\n            <a-icon type=\"plus\" />\n            新增智能体\n          </a-button>\n        </div>\n      </div>\n    </div>\n\n    <!-- 收益统计卡片 -->\n    <div class=\"stats-cards-section\">\n      <div class=\"section-header\">\n        <h2 class=\"section-title\">收益统计</h2>\n        <div class=\"section-actions\">\n          <a-button\n            type=\"default\"\n            @click=\"openWithdrawRecordsModal\"\n            class=\"records-btn\"\n          >\n            <a-icon type=\"history\" />\n            查看提现记录\n          </a-button>\n        </div>\n      </div>\n\n      <RevenueStatsCards\n        :loading=\"statsLoading\"\n        :data=\"revenueStats\"\n        @refresh=\"loadRevenueStats\"\n        @withdraw=\"handleWithdraw\"\n      />\n    </div>\n\n    <!-- 智能体管理区域 -->\n    <div class=\"agents-section\">\n      <div class=\"section-header\">\n        <h2 class=\"section-title\">我的智能体</h2>\n\n      </div>\n\n      <!-- 智能体列表 -->\n      <AgentManagement\n        :loading=\"agentsLoading\"\n        :agents=\"agentList\"\n        :pagination=\"pagination\"\n        @refresh=\"loadAgentList\"\n        @edit=\"handleEditAgent\"\n        @delete=\"handleDeleteAgent\"\n\n        @page-change=\"handlePageChange\"\n        @search=\"handleAgentSearch\"\n        @filter-change=\"handleAgentFilter\"\n        @sort-change=\"handleSortChange\"\n      />\n    </div>\n\n    <!-- 智能体收益排行 -->\n    <div class=\"ranking-section\">\n      <RevenueRanking\n        :loading=\"statsLoading\"\n        :data=\"revenueStats\"\n      />\n    </div>\n\n    <!-- 智能体表单弹窗 -->\n    <CreatorAgentForm\n      ref=\"agentForm\"\n      :visible=\"formVisible\"\n      :loading=\"formLoading\"\n      :agent=\"currentAgent\"\n      :mode=\"formMode\"\n      @close=\"handleCloseForm\"\n      @submit=\"handleSubmitForm\"\n      @complete=\"handleAgentComplete\"\n\n      @delete-workflow=\"handleDeleteWorkflowFromForm\"\n    />\n\n\n\n    <!-- 🔥 提现弹窗 -->\n    <WithdrawModal\n      :visible=\"showWithdrawModal\"\n      :available-amount=\"withdrawAvailableAmount\"\n      :revenue-type=\"withdrawRevenueType\"\n      :loading=\"withdrawLoading\"\n      @submit=\"handleWithdrawSubmit\"\n      @cancel=\"handleWithdrawCancel\"\n    />\n\n    <!-- 🔥 提现记录弹窗 -->\n    <WithdrawRecordsModal\n      :visible=\"showWithdrawRecordsModal\"\n      :revenue-type=\"'agent'\"\n      @cancel=\"handleWithdrawRecordsCancel\"\n      @refresh=\"loadRevenueStats\"\n    />\n  </div>\n</template>\n\n<script>\nimport RevenueStatsCards from './RevenueStatsCards.vue'\nimport RevenueRanking from './RevenueRanking.vue'\nimport AgentManagement from './AgentManagement.vue'\nimport CreatorAgentForm from './CreatorAgentForm.vue'\n// 🔥 导入提现相关组件\nimport WithdrawModal from '@/components/withdraw/WithdrawModal.vue'\nimport WithdrawRecordsModal from '@/components/withdraw/WithdrawRecordsModal.vue'\nimport { getCreatorAgents, createAgent, updateAgent, deleteAgent, getRevenueStats } from '@/api/creator-agent'\n\nexport default {\n  name: 'CreatorCenter',\n  components: {\n    RevenueStatsCards,\n    RevenueRanking,\n    AgentManagement,\n    CreatorAgentForm,\n    // 🔥 注册提现相关组件\n    WithdrawModal,\n    WithdrawRecordsModal\n  },\n  data() {\n    return {\n      // 加载状态\n      creating: false,\n      statsLoading: false,\n      agentsLoading: false,\n      formLoading: false,\n      \n      // 收益统计数据\n      revenueStats: {},\n      \n      // 智能体列表数据\n      agentList: [],\n      pagination: {\n        current: 1,\n        pageSize: 12,\n        total: 0\n      },\n      \n      // 筛选和搜索\n      searchQuery: '',\n      auditStatusFilter: '',\n\n      // 排序相关\n      sortField: 'totalRevenue',\n      sortOrder: 'desc',\n      \n      // 表单相关\n      formVisible: false,\n      formMode: 'create', // 'create' | 'edit'\n      currentAgent: null,\n      \n\n\n      // 🔥 提现相关\n      showWithdrawModal: false,\n      showWithdrawRecordsModal: false,\n      withdrawLoading: false,\n      withdrawAvailableAmount: 0,\n      withdrawRevenueType: 'agent'\n    }\n  },\n  \n  async created() {\n    await this.initData()\n  },\n  \n  methods: {\n    // 初始化数据\n    async initData() {\n      await Promise.all([\n        this.loadRevenueStats(),\n        this.loadAgentList()\n      ])\n    },\n    \n    // 加载收益统计\n    async loadRevenueStats() {\n      this.statsLoading = true\n      try {\n        const response = await getRevenueStats()\n        if (response.success) {\n          this.revenueStats = response.result\n        } else {\n          this.$message.error(response.message || '获取收益统计失败')\n        }\n      } catch (error) {\n        console.error('加载收益统计失败:', error)\n        this.$message.error('获取收益统计失败')\n      } finally {\n        this.statsLoading = false\n      }\n    },\n    \n    // 加载智能体列表\n    async loadAgentList(resetPage = false) {\n      if (resetPage) {\n        this.pagination.current = 1\n      }\n      \n      this.agentsLoading = true\n      try {\n        const params = {\n          pageNo: this.pagination.current,\n          pageSize: this.pagination.pageSize,\n          agentName: this.searchQuery || undefined,\n          auditStatus: this.auditStatusFilter || undefined,\n          sortField: this.sortField,\n          sortOrder: this.sortOrder\n        }\n        \n        const response = await getCreatorAgents(params)\n        if (response.success) {\n          this.agentList = response.result.records || []\n          this.pagination.total = response.result.total || 0\n        } else {\n          this.$message.error(response.message || '获取智能体列表失败')\n        }\n      } catch (error) {\n        console.error('加载智能体列表失败:', error)\n        this.$message.error('获取智能体列表失败')\n      } finally {\n        this.agentsLoading = false\n      }\n    },\n    \n    // 新增智能体\n    handleCreateAgent() {\n      this.formMode = 'create'\n      this.currentAgent = null\n      this.formVisible = true\n    },\n    \n    // 编辑智能体\n    handleEditAgent(agent) {\n      this.formMode = 'edit'\n      this.currentAgent = { ...agent }\n      this.formVisible = true\n    },\n    \n    // 删除智能体\n    async handleDeleteAgent(agent) {\n      const self = this\n      this.$confirm({\n        title: '确认删除',\n        content: `确定要删除智能体\"${agent.agentName}\"吗？此操作不可恢复。`,\n        okText: '确定',\n        cancelText: '取消',\n        onOk: async () => {\n          try {\n            const response = await deleteAgent(agent.id)\n            if (response.success) {\n              self.$message.success('删除成功')\n              await self.loadAgentList()\n              await self.loadRevenueStats() // 刷新统计数据\n            } else {\n              self.$message.error(response.message || '删除失败')\n            }\n          } catch (error) {\n            console.error('删除智能体失败:', error)\n            self.$message.error('删除失败')\n          }\n        }\n      })\n    },\n    \n\n    \n    // 智能体搜索\n    handleAgentSearch(searchQuery) {\n      this.searchQuery = searchQuery\n      this.loadAgentList(true)\n    },\n\n    // 智能体筛选\n    handleAgentFilter(filters) {\n      this.auditStatusFilter = filters.auditStatus\n      this.loadAgentList(true)\n    },\n\n    // 排序变化\n    handleSortChange(sortParams) {\n      this.sortField = sortParams.sortField\n      this.sortOrder = sortParams.sortOrder\n      this.loadAgentList(true)\n    },\n\n    // 分页变化\n    handlePageChange(page, pageSize) {\n      this.pagination.current = page\n      this.pagination.pageSize = pageSize\n      this.loadAgentList()\n    },\n    \n    // 关闭表单\n    handleCloseForm() {\n      this.formVisible = false\n      this.currentAgent = null\n    },\n    \n    // 提交表单\n    async handleSubmitForm(formData) {\n      this.formLoading = true\n\n      try {\n        let response\n        if (this.formMode === 'create') {\n          response = await createAgent(formData)\n        } else {\n          response = await updateAgent(this.currentAgent.id, formData)\n        }\n\n        if (response.success) {\n\n          // 🔥 成功后确认删除被替换的原始头像文件（与后台管理系统逻辑一致）\n          if (this.$refs && this.$refs.agentForm && this.$refs.agentForm.confirmDeleteOriginalFiles) {\n            this.$refs.agentForm.confirmDeleteOriginalFiles()\n          }\n\n          this.$message.success(this.formMode === 'create' ? '创建成功' : '更新成功')\n          this.handleCloseForm()\n          await this.loadAgentList()\n          await this.loadRevenueStats() // 刷新统计数据\n        } else {\n          console.error('🎯 CreatorCenter: 智能体操作失败:', response.message)\n          this.$message.error(response.message || '操作失败')\n        }\n      } catch (error) {\n        console.error('🎯 CreatorCenter: 提交表单失败:', error)\n        this.$message.error('操作失败: ' + (error.message || '未知错误'))\n      } finally {\n        this.formLoading = false\n      }\n    },\n    \n\n\n    // 刷新所有数据（供父组件调用）\n    async refreshAllData() {\n      try {\n        await Promise.all([\n          this.loadRevenueStats(),\n          this.loadAgentList()\n        ])\n      } catch (error) {\n        console.error('❌ 创作者中心数据刷新失败:', error)\n      }\n    },\n\n    // 🔥 智能体创建完成\n    handleAgentComplete(agent) {\n      this.handleCloseForm()\n      this.loadAgentList()\n      this.loadRevenueStats()\n    },\n\n\n\n    // 🔥 从表单中删除工作流\n    async handleDeleteWorkflowFromForm(workflow) {\n      console.log('🎯 CreatorCenter: 从表单中删除工作流', workflow)\n      try {\n        // 这里调用删除工作流的API\n        // await deleteWorkflow(workflow.id)\n        this.$message.success('工作流删除成功')\n\n        // 刷新表单中的工作流列表\n        if (this.$refs.agentForm && this.$refs.agentForm.loadWorkflowList) {\n          this.$refs.agentForm.loadWorkflowList(workflow.agentId)\n        }\n      } catch (error) {\n        console.error('🎯 CreatorCenter: 删除工作流失败:', error)\n        this.$message.error('删除工作流失败')\n      }\n    },\n\n    // 🔥 ==================== 提现相关方法 ====================\n\n    // 处理提现\n    handleWithdraw(params) {\n      this.withdrawAvailableAmount = params.availableAmount\n      this.withdrawRevenueType = params.revenueType\n      this.showWithdrawModal = true\n    },\n\n    // 打开提现记录弹窗\n    openWithdrawRecordsModal() {\n      this.showWithdrawRecordsModal = true\n    },\n\n    // 提现申请提交\n    async handleWithdrawSubmit(params) {\n\n      this.withdrawLoading = true\n\n      try {\n        const response = await this.$http.post('/api/usercenter/applyWithdrawal', params)\n\n        if (response.success) {\n          this.$notification.success({\n            message: '提现申请成功',\n            description: '您的提现申请已提交，预计1-3个工作日到账'\n          })\n\n          this.showWithdrawModal = false\n\n          // 刷新收益数据\n          await this.loadRevenueStats()\n        } else {\n          this.$notification.error({\n            message: '提现申请失败',\n            description: response.message || '申请失败，请重试'\n          })\n        }\n      } catch (error) {\n        console.error('🔥 CreatorCenter: 提现申请失败:', error)\n        this.$notification.error({\n          message: '提现申请失败',\n          description: error.message || '申请失败，请重试'\n        })\n      } finally {\n        this.withdrawLoading = false\n      }\n    },\n\n    // 取消提现\n    handleWithdrawCancel() {\n      this.showWithdrawModal = false\n    },\n\n    // 关闭提现记录弹窗\n    handleWithdrawRecordsCancel() {\n      this.showWithdrawRecordsModal = false\n    }\n  }\n}\n</script>\n\n<style lang=\"less\" scoped>\n.creator-center {\n  min-height: 100vh;\n  background: #f5f7fa;\n\n  // 页面头部\n  .creator-header {\n    background: #fff;\n    border-bottom: 1px solid #e8eaec;\n    padding: 24px 0;\n    margin-bottom: 24px;\n\n    .header-content {\n      max-width: 1600px;\n      margin: 0 auto;\n      padding: 0 32px;\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n\n      .header-left {\n        .page-title {\n          margin: 0;\n          font-size: 28px;\n          font-weight: 600;\n          color: #1f2937;\n          display: flex;\n          align-items: center;\n          gap: 12px;\n\n          .anticon {\n            color: #1890ff;\n          }\n        }\n\n        .page-subtitle {\n          margin: 8px 0 0 0;\n          color: #6b7280;\n          font-size: 14px;\n        }\n      }\n\n      .header-right {\n        .ant-btn {\n          height: 40px;\n          padding: 0 24px;\n          font-size: 14px;\n          border-radius: 6px;\n          box-shadow: 0 2px 4px rgba(24, 144, 255, 0.2);\n\n          &:hover {\n            transform: translateY(-1px);\n            box-shadow: 0 4px 8px rgba(24, 144, 255, 0.3);\n          }\n        }\n      }\n    }\n  }\n\n  // 统计卡片面板\n  .stats-cards-section {\n    max-width: 1600px;\n    margin: 0 auto 24px;\n    padding: 0 32px;\n  }\n\n  // 收益排行面板\n  .ranking-section {\n    max-width: 1600px;\n    margin: 0 auto 24px;\n    padding: 32px;\n  }\n\n  // 🔥 收益统计区域\n  .stats-cards-section {\n    max-width: 1600px;\n    margin: 0 auto;\n    padding: 0 32px;\n\n    .section-header {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      margin-bottom: 20px;\n\n      .section-title {\n        margin: 0;\n        font-size: 20px;\n        font-weight: 600;\n        color: #1f2937;\n      }\n\n      .section-actions {\n        display: flex;\n        gap: 12px;\n\n        .records-btn {\n          height: 32px;\n          padding: 0 16px;\n          font-size: 13px;\n          border-radius: 6px;\n          border: 1px solid #d9d9d9;\n          background: #fff;\n\n          &:hover {\n            border-color: #1890ff;\n            color: #1890ff;\n          }\n\n          .anticon {\n            font-size: 12px;\n            margin-right: 6px;\n          }\n        }\n      }\n    }\n  }\n\n  // 智能体管理区域\n  .agents-section {\n    max-width: 1600px;\n    margin: 0 auto;\n    padding: 0 32px;\n\n    .section-header {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      margin-bottom: 20px;\n\n      .section-title {\n        margin: 0;\n        font-size: 20px;\n        font-weight: 600;\n        color: #1f2937;\n      }\n\n      .section-actions {\n        display: flex;\n        align-items: center;\n        gap: 12px;\n\n        .ant-select {\n          .ant-select-selection {\n            border-radius: 6px;\n          }\n        }\n\n        .ant-input-search {\n          .ant-input {\n            border-radius: 6px;\n          }\n\n          .ant-input-search-button {\n            border-radius: 0 6px 6px 0;\n          }\n        }\n      }\n    }\n  }\n}\n\n// 响应式设计\n@media (max-width: 768px) {\n  .creator-center {\n    .creator-header {\n      .header-content {\n        flex-direction: column;\n        gap: 16px;\n        text-align: center;\n\n        .header-left {\n          .page-title {\n            font-size: 24px;\n          }\n        }\n      }\n    }\n\n    .stats-cards-section,\n    .ranking-section,\n    .agents-section {\n      padding: 0 20px;\n    }\n\n    .agents-section {\n      .section-header {\n        flex-direction: column;\n        gap: 16px;\n        align-items: stretch;\n\n        .section-actions {\n          justify-content: center;\n          flex-wrap: wrap;\n        }\n      }\n    }\n  }\n}\n</style>\n"]}]}