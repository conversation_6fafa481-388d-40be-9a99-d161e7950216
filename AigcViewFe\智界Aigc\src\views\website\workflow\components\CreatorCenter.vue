<template>
  <div class="creator-center">
    <!-- 页面头部 -->
    <div class="creator-header">
      <div class="header-content">
        <div class="header-left">
          <h1 class="page-title">
            <a-icon type="user" />
            创作者中心
          </h1>
          <p class="page-subtitle">管理您的智能体，查看收益统计（全部收益归创作者，但VIP和SVIP用户分别是价格的7折和5折，如您选择发布默认同意此方案）</p>
        </div>
        <div class="header-right">
          <a-button type="primary" size="large" @click="handleCreateAgent" :loading="creating">
            <a-icon type="plus" />
            新增智能体
          </a-button>
        </div>
      </div>
    </div>

    <!-- 收益统计卡片 -->
    <div class="stats-cards-section">
      <div class="section-header">
        <h2 class="section-title">收益统计</h2>
        <div class="section-actions">
          <a-button
            type="default"
            @click="openWithdrawRecordsModal"
            class="records-btn"
          >
            <a-icon type="history" />
            查看提现记录
          </a-button>
        </div>
      </div>

      <RevenueStatsCards
        :loading="statsLoading"
        :data="revenueStats"
        @refresh="loadRevenueStats"
        @withdraw="handleWithdraw"
      />
    </div>

    <!-- 智能体管理区域 -->
    <div class="agents-section">
      <div class="section-header">
        <h2 class="section-title">我的智能体</h2>

      </div>

      <!-- 智能体列表 -->
      <AgentManagement
        :loading="agentsLoading"
        :agents="agentList"
        :pagination="pagination"
        @refresh="loadAgentList"
        @edit="handleEditAgent"
        @delete="handleDeleteAgent"

        @page-change="handlePageChange"
        @search="handleAgentSearch"
        @filter-change="handleAgentFilter"
        @sort-change="handleSortChange"
      />
    </div>

    <!-- 智能体收益排行 -->
    <div class="ranking-section">
      <RevenueRanking
        :loading="statsLoading"
        :data="revenueStats"
      />
    </div>

    <!-- 智能体表单弹窗 -->
    <CreatorAgentForm
      ref="agentForm"
      :visible="formVisible"
      :loading="formLoading"
      :agent="currentAgent"
      :mode="formMode"
      @close="handleCloseForm"
      @submit="handleSubmitForm"
      @complete="handleAgentComplete"

      @delete-workflow="handleDeleteWorkflowFromForm"
    />



    <!-- 🔥 提现弹窗 -->
    <WithdrawModal
      :visible="showWithdrawModal"
      :available-amount="withdrawAvailableAmount"
      :revenue-type="withdrawRevenueType"
      :loading="withdrawLoading"
      @submit="handleWithdrawSubmit"
      @cancel="handleWithdrawCancel"
    />

    <!-- 🔥 提现记录弹窗 -->
    <WithdrawRecordsModal
      :visible="showWithdrawRecordsModal"
      :revenue-type="'agent'"
      @cancel="handleWithdrawRecordsCancel"
      @refresh="loadRevenueStats"
    />
  </div>
</template>

<script>
import RevenueStatsCards from './RevenueStatsCards.vue'
import RevenueRanking from './RevenueRanking.vue'
import AgentManagement from './AgentManagement.vue'
import CreatorAgentForm from './CreatorAgentForm.vue'
// 🔥 导入提现相关组件
import WithdrawModal from '@/components/withdraw/WithdrawModal.vue'
import WithdrawRecordsModal from '@/components/withdraw/WithdrawRecordsModal.vue'
import { getCreatorAgents, createAgent, updateAgent, deleteAgent, getRevenueStats } from '@/api/creator-agent'

export default {
  name: 'CreatorCenter',
  components: {
    RevenueStatsCards,
    RevenueRanking,
    AgentManagement,
    CreatorAgentForm,
    // 🔥 注册提现相关组件
    WithdrawModal,
    WithdrawRecordsModal
  },
  data() {
    return {
      // 加载状态
      creating: false,
      statsLoading: false,
      agentsLoading: false,
      formLoading: false,
      
      // 收益统计数据
      revenueStats: {},
      
      // 智能体列表数据
      agentList: [],
      pagination: {
        current: 1,
        pageSize: 12,
        total: 0
      },
      
      // 筛选和搜索
      searchQuery: '',
      auditStatusFilter: '',

      // 排序相关
      sortField: 'totalRevenue',
      sortOrder: 'desc',
      
      // 表单相关
      formVisible: false,
      formMode: 'create', // 'create' | 'edit'
      currentAgent: null,
      


      // 🔥 提现相关
      showWithdrawModal: false,
      showWithdrawRecordsModal: false,
      withdrawLoading: false,
      withdrawAvailableAmount: 0,
      withdrawRevenueType: 'agent'
    }
  },
  
  async created() {
    await this.initData()
  },
  
  methods: {
    // 初始化数据
    async initData() {
      await Promise.all([
        this.loadRevenueStats(),
        this.loadAgentList()
      ])
    },
    
    // 加载收益统计
    async loadRevenueStats() {
      this.statsLoading = true
      try {
        const response = await getRevenueStats()
        if (response.success) {
          this.revenueStats = response.result
        } else {
          this.$message.error(response.message || '获取收益统计失败')
        }
      } catch (error) {
        console.error('加载收益统计失败:', error)
        this.$message.error('获取收益统计失败')
      } finally {
        this.statsLoading = false
      }
    },
    
    // 加载智能体列表
    async loadAgentList(resetPage = false) {
      if (resetPage) {
        this.pagination.current = 1
      }
      
      this.agentsLoading = true
      try {
        const params = {
          pageNo: this.pagination.current,
          pageSize: this.pagination.pageSize,
          agentName: this.searchQuery || undefined,
          auditStatus: this.auditStatusFilter || undefined,
          sortField: this.sortField,
          sortOrder: this.sortOrder
        }
        
        const response = await getCreatorAgents(params)
        if (response.success) {
          this.agentList = response.result.records || []
          this.pagination.total = response.result.total || 0
        } else {
          this.$message.error(response.message || '获取智能体列表失败')
        }
      } catch (error) {
        console.error('加载智能体列表失败:', error)
        this.$message.error('获取智能体列表失败')
      } finally {
        this.agentsLoading = false
      }
    },
    
    // 新增智能体
    handleCreateAgent() {
      this.formMode = 'create'
      this.currentAgent = null
      this.formVisible = true
    },
    
    // 编辑智能体
    handleEditAgent(agent) {
      this.formMode = 'edit'
      this.currentAgent = { ...agent }
      this.formVisible = true
    },
    
    // 删除智能体
    async handleDeleteAgent(agent) {
      const self = this
      this.$confirm({
        title: '确认删除',
        content: `确定要删除智能体"${agent.agentName}"吗？此操作不可恢复。`,
        okText: '确定',
        cancelText: '取消',
        onOk: async () => {
          try {
            const response = await deleteAgent(agent.id)
            if (response.success) {
              self.$message.success('删除成功')
              await self.loadAgentList()
              await self.loadRevenueStats() // 刷新统计数据
            } else {
              self.$message.error(response.message || '删除失败')
            }
          } catch (error) {
            console.error('删除智能体失败:', error)
            self.$message.error('删除失败')
          }
        }
      })
    },
    

    
    // 智能体搜索
    handleAgentSearch(searchQuery) {
      this.searchQuery = searchQuery
      this.loadAgentList(true)
    },

    // 智能体筛选
    handleAgentFilter(filters) {
      this.auditStatusFilter = filters.auditStatus
      this.loadAgentList(true)
    },

    // 排序变化
    handleSortChange(sortParams) {
      this.sortField = sortParams.sortField
      this.sortOrder = sortParams.sortOrder
      this.loadAgentList(true)
    },

    // 分页变化
    handlePageChange(page, pageSize) {
      this.pagination.current = page
      this.pagination.pageSize = pageSize
      this.loadAgentList()
    },
    
    // 关闭表单
    handleCloseForm() {
      this.formVisible = false
      this.currentAgent = null
    },
    
    // 提交表单
    async handleSubmitForm(formData) {
      this.formLoading = true

      try {
        let response
        if (this.formMode === 'create') {
          response = await createAgent(formData)
        } else {
          response = await updateAgent(this.currentAgent.id, formData)
        }

        if (response.success) {

          // 🔥 成功后确认删除被替换的原始头像文件（与后台管理系统逻辑一致）
          if (this.$refs && this.$refs.agentForm && this.$refs.agentForm.confirmDeleteOriginalFiles) {
            this.$refs.agentForm.confirmDeleteOriginalFiles()
          }

          this.$message.success(this.formMode === 'create' ? '创建成功' : '更新成功')
          this.handleCloseForm()
          await this.loadAgentList()
          await this.loadRevenueStats() // 刷新统计数据
        } else {
          console.error('🎯 CreatorCenter: 智能体操作失败:', response.message)
          this.$message.error(response.message || '操作失败')
        }
      } catch (error) {
        console.error('🎯 CreatorCenter: 提交表单失败:', error)
        this.$message.error('操作失败: ' + (error.message || '未知错误'))
      } finally {
        this.formLoading = false
      }
    },
    


    // 刷新所有数据（供父组件调用）
    async refreshAllData() {
      try {
        await Promise.all([
          this.loadRevenueStats(),
          this.loadAgentList()
        ])
      } catch (error) {
        console.error('❌ 创作者中心数据刷新失败:', error)
      }
    },

    // 🔥 智能体创建完成
    handleAgentComplete(agent) {
      this.handleCloseForm()
      this.loadAgentList()
      this.loadRevenueStats()
    },



    // 🔥 从表单中删除工作流
    async handleDeleteWorkflowFromForm(workflow) {
      console.log('🎯 CreatorCenter: 从表单中删除工作流', workflow)
      try {
        // 这里调用删除工作流的API
        // await deleteWorkflow(workflow.id)
        this.$message.success('工作流删除成功')

        // 刷新表单中的工作流列表
        if (this.$refs.agentForm && this.$refs.agentForm.loadWorkflowList) {
          this.$refs.agentForm.loadWorkflowList(workflow.agentId)
        }
      } catch (error) {
        console.error('🎯 CreatorCenter: 删除工作流失败:', error)
        this.$message.error('删除工作流失败')
      }
    },

    // 🔥 ==================== 提现相关方法 ====================

    // 处理提现
    handleWithdraw(params) {
      this.withdrawAvailableAmount = params.availableAmount
      this.withdrawRevenueType = params.revenueType
      this.showWithdrawModal = true
    },

    // 打开提现记录弹窗
    openWithdrawRecordsModal() {
      this.showWithdrawRecordsModal = true
    },

    // 提现申请提交
    async handleWithdrawSubmit(params) {

      this.withdrawLoading = true

      try {
        const response = await this.$http.post('/api/usercenter/applyWithdrawal', params)

        if (response.success) {
          this.$notification.success({
            message: '提现申请成功',
            description: '您的提现申请已提交，预计1-3个工作日到账'
          })

          this.showWithdrawModal = false

          // 刷新收益数据
          await this.loadRevenueStats()
        } else {
          this.$notification.error({
            message: '提现申请失败',
            description: response.message || '申请失败，请重试'
          })
        }
      } catch (error) {
        console.error('🔥 CreatorCenter: 提现申请失败:', error)
        this.$notification.error({
          message: '提现申请失败',
          description: error.message || '申请失败，请重试'
        })
      } finally {
        this.withdrawLoading = false
      }
    },

    // 取消提现
    handleWithdrawCancel() {
      this.showWithdrawModal = false
    },

    // 关闭提现记录弹窗
    handleWithdrawRecordsCancel() {
      this.showWithdrawRecordsModal = false
    }
  }
}
</script>

<style lang="less" scoped>
.creator-center {
  min-height: 100vh;
  background: #f5f7fa;

  // 页面头部
  .creator-header {
    background: #fff;
    border-bottom: 1px solid #e8eaec;
    padding: 24px 0;
    margin-bottom: 24px;

    .header-content {
      max-width: 1600px;
      margin: 0 auto;
      padding: 0 32px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .header-left {
        .page-title {
          margin: 0;
          font-size: 28px;
          font-weight: 600;
          color: #1f2937;
          display: flex;
          align-items: center;
          gap: 12px;

          .anticon {
            color: #1890ff;
          }
        }

        .page-subtitle {
          margin: 8px 0 0 0;
          color: #6b7280;
          font-size: 14px;
        }
      }

      .header-right {
        .ant-btn {
          height: 40px;
          padding: 0 24px;
          font-size: 14px;
          border-radius: 6px;
          box-shadow: 0 2px 4px rgba(24, 144, 255, 0.2);

          &:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(24, 144, 255, 0.3);
          }
        }
      }
    }
  }

  // 统计卡片面板
  .stats-cards-section {
    max-width: 1600px;
    margin: 0 auto 24px;
    padding: 0 32px;
  }

  // 收益排行面板
  .ranking-section {
    max-width: 1600px;
    margin: 0 auto 24px;
    padding: 32px;
  }

  // 🔥 收益统计区域
  .stats-cards-section {
    max-width: 1600px;
    margin: 0 auto;
    padding: 0 32px;

    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;

      .section-title {
        margin: 0;
        font-size: 20px;
        font-weight: 600;
        color: #1f2937;
      }

      .section-actions {
        display: flex;
        gap: 12px;

        .records-btn {
          height: 32px;
          padding: 0 16px;
          font-size: 13px;
          border-radius: 6px;
          border: 1px solid #d9d9d9;
          background: #fff;

          &:hover {
            border-color: #1890ff;
            color: #1890ff;
          }

          .anticon {
            font-size: 12px;
            margin-right: 6px;
          }
        }
      }
    }
  }

  // 智能体管理区域
  .agents-section {
    max-width: 1600px;
    margin: 0 auto;
    padding: 0 32px;

    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;

      .section-title {
        margin: 0;
        font-size: 20px;
        font-weight: 600;
        color: #1f2937;
      }

      .section-actions {
        display: flex;
        align-items: center;
        gap: 12px;

        .ant-select {
          .ant-select-selection {
            border-radius: 6px;
          }
        }

        .ant-input-search {
          .ant-input {
            border-radius: 6px;
          }

          .ant-input-search-button {
            border-radius: 0 6px 6px 0;
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .creator-center {
    .creator-header {
      .header-content {
        flex-direction: column;
        gap: 16px;
        text-align: center;

        .header-left {
          .page-title {
            font-size: 24px;
          }
        }
      }
    }

    .stats-cards-section,
    .ranking-section,
    .agents-section {
      padding: 0 20px;
    }

    .agents-section {
      .section-header {
        flex-direction: column;
        gap: 16px;
        align-items: stretch;

        .section-actions {
          justify-content: center;
          flex-wrap: wrap;
        }
      }
    }
  }
}
</style>
