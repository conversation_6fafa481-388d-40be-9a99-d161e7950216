-- =============================================
-- 智界Aigc API调用日志表
-- 创建时间：2025-06-14
-- 版本：V1.0
-- 说明：用于记录所有API调用的详细信息，支持真实数据统计
-- =============================================

-- 1. 创建API调用日志表
DROP TABLE IF EXISTS `aicg_api_log`;
CREATE TABLE `aicg_api_log` (
  `id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键id',
  `user_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户ID',
  `api_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'API类型',
  `api_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT 'API名称',
  `request_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '请求URL',
  `request_method` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '请求方法',
  `request_params` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '请求参数',
  `response_data` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '响应数据',
  `status_code` int(11) DEFAULT NULL COMMENT 'HTTP状态码',
  `success` tinyint(1) DEFAULT 1 COMMENT '是否成功：0-失败，1-成功',
  `error_message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '错误信息',
  `cost_points` decimal(10,2) DEFAULT 0.00 COMMENT '消耗积分',
  `request_time` datetime NOT NULL COMMENT '请求时间',
  `response_time` datetime COMMENT '响应时间',
  `duration_ms` bigint(20) DEFAULT NULL COMMENT '耗时(毫秒)',
  `ip_address` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT 'IP地址',
  `user_agent` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '用户代理',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_api_type` (`api_type`),
  KEY `idx_request_time` (`request_time`),
  KEY `idx_success` (`success`),
  KEY `idx_status_code` (`status_code`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='API调用日志表';

-- 2. 创建在线用户统计表
DROP TABLE IF EXISTS `aicg_online_users`;
CREATE TABLE `aicg_online_users` (
  `id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键id',
  `user_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户ID',
  `session_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '会话ID',
  `login_time` datetime NOT NULL COMMENT '登录时间',
  `last_active_time` datetime NOT NULL COMMENT '最后活跃时间',
  `ip_address` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT 'IP地址',
  `user_agent` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '用户代理',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：0-离线，1-在线',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_session` (`user_id`, `session_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_last_active_time` (`last_active_time`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='在线用户统计表';

-- 3. 创建系统统计缓存表（用于提高查询性能）
DROP TABLE IF EXISTS `aicg_stats_cache`;
CREATE TABLE `aicg_stats_cache` (
  `id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键id',
  `cache_key` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '缓存键',
  `cache_value` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '缓存值',
  `expire_time` datetime COMMENT '过期时间',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_cache_key` (`cache_key`),
  KEY `idx_expire_time` (`expire_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='系统统计缓存表';

-- 4. 插入一些测试数据（用于演示）
INSERT INTO `aicg_api_log` (`id`, `user_id`, `api_type`, `api_name`, `request_url`, `request_method`, `status_code`, `success`, `cost_points`, `request_time`, `response_time`, `duration_ms`, `ip_address`) VALUES
('test001', 'admin', 'HTML生成', 'HTML页面生成', '/api/aigc/generate-html', 'POST', 200, 1, 0.10, NOW() - INTERVAL 1 HOUR, NOW() - INTERVAL 1 HOUR + INTERVAL 2 SECOND, 2000, '127.0.0.1'),
('test002', 'admin', 'API验证', 'API密钥验证', '/api/aigc/validate-key', 'POST', 200, 1, 0.01, NOW() - INTERVAL 2 HOUR, NOW() - INTERVAL 2 HOUR + INTERVAL 1 SECOND, 1000, '127.0.0.1'),
('test003', 'admin', '二维码生成', '二维码生成', '/api/aigc/generate-qr', 'POST', 200, 1, 0.05, NOW() - INTERVAL 3 HOUR, NOW() - INTERVAL 3 HOUR + INTERVAL 1 SECOND, 1500, '127.0.0.1'),
('test004', 'admin', 'HTML生成', 'HTML页面生成', '/api/aigc/generate-html', 'POST', 500, 0, 0.00, NOW() - INTERVAL 4 HOUR, NOW() - INTERVAL 4 HOUR + INTERVAL 5 SECOND, 5000, '127.0.0.1'),
('test005', 'admin', 'API验证', 'API密钥验证', '/api/aigc/validate-key', 'POST', 400, 0, 0.00, NOW() - INTERVAL 5 HOUR, NOW() - INTERVAL 5 HOUR + INTERVAL 1 SECOND, 1000, '127.0.0.1');

-- 5. 插入在线用户测试数据
INSERT INTO `aicg_online_users` (`id`, `user_id`, `session_id`, `login_time`, `last_active_time`, `ip_address`, `status`) VALUES
('online001', 'admin', 'session_admin_001', NOW() - INTERVAL 2 HOUR, NOW(), '127.0.0.1', 1),
('online002', 'user001', 'session_user001_001', NOW() - INTERVAL 1 HOUR, NOW() - INTERVAL 5 MINUTE, '*************', 1),
('online003', 'user002', 'session_user002_001', NOW() - INTERVAL 30 MINUTE, NOW() - INTERVAL 2 MINUTE, '*************', 1);

-- 6. 创建视图用于快速统计
CREATE OR REPLACE VIEW `v_api_stats_today` AS
SELECT 
    user_id,
    COUNT(*) as total_calls,
    SUM(CASE WHEN success = 1 THEN 1 ELSE 0 END) as success_calls,
    SUM(CASE WHEN success = 0 THEN 1 ELSE 0 END) as failed_calls,
    SUM(cost_points) as total_cost,
    AVG(duration_ms) as avg_duration,
    COUNT(DISTINCT api_type) as api_types_used
FROM aicg_api_log 
WHERE DATE(request_time) = CURDATE()
GROUP BY user_id;

CREATE OR REPLACE VIEW `v_api_stats_month` AS
SELECT 
    user_id,
    COUNT(*) as total_calls,
    SUM(CASE WHEN success = 1 THEN 1 ELSE 0 END) as success_calls,
    SUM(CASE WHEN success = 0 THEN 1 ELSE 0 END) as failed_calls,
    SUM(cost_points) as total_cost,
    AVG(duration_ms) as avg_duration,
    COUNT(DISTINCT api_type) as api_types_used
FROM aicg_api_log 
WHERE YEAR(request_time) = YEAR(CURDATE()) AND MONTH(request_time) = MONTH(CURDATE())
GROUP BY user_id;

-- 7. 创建系统级统计视图
CREATE OR REPLACE VIEW `v_system_stats_today` AS
SELECT 
    COUNT(*) as total_calls,
    SUM(CASE WHEN success = 1 THEN 1 ELSE 0 END) as success_calls,
    SUM(CASE WHEN success = 0 THEN 1 ELSE 0 END) as failed_calls,
    SUM(cost_points) as total_cost,
    AVG(duration_ms) as avg_duration,
    COUNT(DISTINCT user_id) as active_users,
    COUNT(DISTINCT api_type) as api_types_used
FROM aicg_api_log 
WHERE DATE(request_time) = CURDATE();

CREATE OR REPLACE VIEW `v_system_stats_month` AS
SELECT 
    COUNT(*) as total_calls,
    SUM(CASE WHEN success = 1 THEN 1 ELSE 0 END) as success_calls,
    SUM(CASE WHEN success = 0 THEN 1 ELSE 0 END) as failed_calls,
    SUM(cost_points) as total_cost,
    AVG(duration_ms) as avg_duration,
    COUNT(DISTINCT user_id) as active_users,
    COUNT(DISTINCT api_type) as api_types_used
FROM aicg_api_log 
WHERE YEAR(request_time) = YEAR(CURDATE()) AND MONTH(request_time) = MONTH(CURDATE());

-- 8. 创建在线用户统计视图
CREATE OR REPLACE VIEW `v_online_users_stats` AS
SELECT 
    COUNT(*) as online_count,
    COUNT(CASE WHEN last_active_time >= NOW() - INTERVAL 5 MINUTE THEN 1 END) as active_5min,
    COUNT(CASE WHEN last_active_time >= NOW() - INTERVAL 15 MINUTE THEN 1 END) as active_15min,
    COUNT(CASE WHEN DATE(last_active_time) = CURDATE() THEN 1 END) as today_active
FROM aicg_online_users 
WHERE status = 1;

-- 9. 创建定时清理过期数据的存储过程
DELIMITER $$
CREATE PROCEDURE `CleanExpiredData`()
BEGIN
    -- 清理30天前的API日志
    DELETE FROM aicg_api_log WHERE create_time < DATE_SUB(NOW(), INTERVAL 30 DAY);
    
    -- 清理离线超过1小时的用户记录
    UPDATE aicg_online_users SET status = 0 
    WHERE last_active_time < DATE_SUB(NOW(), INTERVAL 1 HOUR) AND status = 1;
    
    -- 清理过期的缓存
    DELETE FROM aicg_stats_cache WHERE expire_time < NOW();
END$$
DELIMITER ;

-- 10. 创建索引优化查询性能
CREATE INDEX idx_api_log_user_time ON aicg_api_log(user_id, request_time);
CREATE INDEX idx_api_log_type_time ON aicg_api_log(api_type, request_time);
CREATE INDEX idx_api_log_success_time ON aicg_api_log(success, request_time);
CREATE INDEX idx_online_users_active ON aicg_online_users(status, last_active_time);

-- 完成
SELECT '智界Aigc API调用日志表创建完成！' as message;
