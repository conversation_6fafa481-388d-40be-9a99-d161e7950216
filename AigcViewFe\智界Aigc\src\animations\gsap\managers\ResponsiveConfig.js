/**
 * 响应式配置管理器
 * 根据设备类型和屏幕尺寸调整动画参数
 */

export const responsiveConfig = {
  // 断点配置
  breakpoints: {
    mobile: 768,
    tablet: 1024,
    desktop: 1200
  },

  // 获取当前设备类型
  getDeviceType() {
    const width = window.innerWidth
    if (width < this.breakpoints.mobile) return 'mobile'
    if (width < this.breakpoints.tablet) return 'tablet'
    if (width < this.breakpoints.desktop) return 'desktop'
    return 'large'
  },

  // 获取响应式动画参数
  getAnimationParams(baseParams = {}) {
    const deviceType = this.getDeviceType()
    const multipliers = this.getMultipliers(deviceType)
    
    return {
      ...baseParams,
      duration: (baseParams.duration || 1) * multipliers.duration,
      delay: (baseParams.delay || 0) * multipliers.delay,
      stagger: (baseParams.stagger || 0) * multipliers.stagger
    }
  },

  // 获取设备类型对应的动画倍数
  getMultipliers(deviceType) {
    const multipliers = {
      mobile: {
        duration: 0.8,  // 移动端动画稍快
        delay: 0.7,
        stagger: 0.8
      },
      tablet: {
        duration: 0.9,
        delay: 0.85,
        stagger: 0.9
      },
      desktop: {
        duration: 1,
        delay: 1,
        stagger: 1
      },
      large: {
        duration: 1.1,   // 大屏幕动画稍慢
        delay: 1.1,
        stagger: 1.1
      }
    }

    return multipliers[deviceType] || multipliers.desktop
  },

  // 获取响应式距离值
  getDistance(baseDistance, axis = 'y') {
    const deviceType = this.getDeviceType()
    const multipliers = {
      mobile: 0.6,
      tablet: 0.8,
      desktop: 1,
      large: 1.2
    }

    return baseDistance * (multipliers[deviceType] || 1)
  },

  // 获取响应式缩放值
  getScale(baseScale) {
    const deviceType = this.getDeviceType()
    const adjustments = {
      mobile: -0.05,   // 移动端稍小
      tablet: -0.02,
      desktop: 0,
      large: 0.02      // 大屏幕稍大
    }

    return baseScale + (adjustments[deviceType] || 0)
  },

  // 检查是否为移动设备
  isMobile() {
    return this.getDeviceType() === 'mobile'
  },

  // 检查是否为触摸设备
  isTouchDevice() {
    return 'ontouchstart' in window || navigator.maxTouchPoints > 0
  },

  // 检查是否支持hover
  supportsHover() {
    return window.matchMedia('(hover: hover)').matches
  },

  // 获取性能等级（用于调整动画复杂度）
  getPerformanceLevel() {
    // 简单的性能检测
    const deviceType = this.getDeviceType()
    const memory = navigator.deviceMemory || 4
    const cores = navigator.hardwareConcurrency || 4

    if (deviceType === 'mobile' && memory < 4) return 'low'
    if (deviceType === 'mobile' && memory < 8) return 'medium'
    if (cores < 4) return 'medium'
    return 'high'
  },

  // 根据性能等级调整动画参数
  getPerformanceAdjustedParams(baseParams = {}) {
    const level = this.getPerformanceLevel()
    const adjustments = {
      low: {
        duration: 0.7,
        complexity: 0.5,  // 减少复杂动画
        fps: 30
      },
      medium: {
        duration: 0.85,
        complexity: 0.75,
        fps: 45
      },
      high: {
        duration: 1,
        complexity: 1,
        fps: 60
      }
    }

    const adjustment = adjustments[level]
    return {
      ...baseParams,
      duration: (baseParams.duration || 1) * adjustment.duration
    }
  }
}

// 监听窗口大小变化
let resizeTimeout
window.addEventListener('resize', () => {
  clearTimeout(resizeTimeout)
  resizeTimeout = setTimeout(() => {
    // 可以在这里触发重新计算动画参数的事件
    window.dispatchEvent(new CustomEvent('responsiveConfigUpdate', {
      detail: { deviceType: responsiveConfig.getDeviceType() }
    }))
  }, 250)
})

export default responsiveConfig
