package org.jeecg.modules.jianying.dto;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 特效数据生成器请求参数
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class EffectInfosRequest extends BaseJianyingRequest {
    
    @ApiModelProperty(value = "特效列表（必填）", required = true,
                     example = "[\"金粉闪闪\", \"光影效果\"]")
    @NotEmpty(message = "effects不能为空")
    @JsonProperty("effects")
    private List<String> zjEffects;

    @ApiModelProperty(value = "时间线（必填）", required = true,
                     example = "[{\"start\": 0, \"end\": 5000000}]")
    @NotEmpty(message = "timelines不能为空")
    @JsonProperty("timelines")
    private List<JSONObject> zjTimelines;
    
    @Override
    public String getSummary() {
        return "EffectInfosRequest{" +
               "accessKey=" + (getAccessKey() != null && getAccessKey().length() > 10 ?
                              getAccessKey().substring(0, 10) + "***" : "null") +
               ", effectsCount=" + (zjEffects != null ? zjEffects.size() : 0) +
               ", timelinesCount=" + (zjTimelines != null ? zjTimelines.size() : 0) +
               "}";
    }
}
