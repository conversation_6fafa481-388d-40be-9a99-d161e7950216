<template>
  <a-card :bordered="false">
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline" @keyup.enter.native="searchQuery">
        <a-row :gutter="24">
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="作者类型">
              <j-dict-select-tag placeholder="请选择作者类型" v-model="queryParam.authorType" dictCode="author_type"/>
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="智能体名称">
              <a-input placeholder="请输入智能体名称" v-model="queryParam.agentName"></a-input>
            </a-form-item>
          </a-col>
          <template v-if="toggleSearchStatus">
            <a-col :xl="10" :lg="11" :md="12" :sm="24">
              <a-form-item label="价格（元）">
                <a-input placeholder="请输入最小值" class="query-group-cust" v-model="queryParam.price_begin"></a-input>
                <span class="query-group-split-cust"></span>
                <a-input placeholder="请输入最大值" class="query-group-cust" v-model="queryParam.price_end"></a-input>
              </a-form-item>
            </a-col>
            <a-col :xl="6" :lg="7" :md="8" :sm="24">
              <a-form-item label="审核状态">
                <j-dict-select-tag placeholder="请选择审核状态" v-model="queryParam.auditStatus" dictCode="audit_status"/>
              </a-form-item>
            </a-col>
          </template>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <span style="float: left;overflow: hidden;" class="table-page-search-submitButtons">
              <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
              <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
              <a @click="handleToggleSearch" style="margin-left: 8px">
                {{ toggleSearchStatus ? '收起' : '展开' }}
                <a-icon :type="toggleSearchStatus ? 'up' : 'down'"/>
              </a>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <!-- 查询区域-END -->
    
    <!-- 操作按钮区域 -->
    <div class="table-operator">
      <a-button @click="handleAdd" type="primary" icon="plus">新增</a-button>
      <a-button type="primary" icon="download" @click="handleExportXls('智能体表')">导出</a-button>
      <a-upload name="file" :showUploadList="false" :multiple="false" :headers="tokenHeader" :action="importExcelUrl" @change="handleImportExcel">
        <a-button type="primary" icon="import">导入</a-button>
      </a-upload>
      <!-- 高级查询区域 -->
      <j-super-query :fieldList="superFieldList" ref="superQueryModal" @handleSuperQuery="handleSuperQuery"></j-super-query>
      <a-dropdown v-if="selectedRowKeys.length > 0">
        <a-menu slot="overlay">
          <a-menu-item key="1" @click="batchDel"><a-icon type="delete"/>删除</a-menu-item>
        </a-menu>
        <a-button style="margin-left: 8px"> 批量操作 <a-icon type="down" /></a-button>
      </a-dropdown>
    </div>

    <!-- table区域-begin -->
    <div>
      <div class="ant-alert ant-alert-info" style="margin-bottom: 16px;">
        <i class="anticon anticon-info-circle ant-alert-icon"></i> 已选择 <a style="font-weight: 600">{{ selectedRowKeys.length }}</a>项
        <a style="margin-left: 24px" @click="onClearSelected">清空</a>
      </div>

      <a-table
        ref="table"
        size="middle"
        bordered
        rowKey="id"
        class="j-table-force-nowrap"
        :scroll="{x:true}"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="ipagination"
        :loading="loading"
        :rowSelection="{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}"
        @change="handleTableChange">

        <template slot="htmlSlot" slot-scope="text">
          <div v-html="text"></div>
        </template>
        <template slot="imgSlot" slot-scope="text">
          <span v-if="!text" style="font-size: 12px;font-style: italic;">无图片</span>
          <img v-else :src="getImgView(text)" height="25px" alt="" style="max-width:80px;font-size: 12px;font-style: italic;"/>
        </template>
        <template slot="fileSlot" slot-scope="text">
          <span v-if="!text" style="font-size: 12px;font-style: italic;">无文件</span>
          <a-button
            v-else
            :ghost="true"
            type="primary"
            icon="download"
            size="small"
            @click="downloadFile(text)">
            下载
          </a-button>
        </template>

        <span slot="action" slot-scope="text, record">
          <a @click="handleEdit(record)">编辑</a>

          <a-divider type="vertical" />
          <a-dropdown>
            <a class="ant-dropdown-link">更多 <a-icon type="down" /></a>
            <a-menu slot="overlay">
              <a-menu-item>
                <a @click="handleDetail(record)">详情</a>
              </a-menu-item>
              <a-menu-item>
                <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
                  <a>删除</a>
                </a-popconfirm>
              </a-menu-item>
            </a-menu>
          </a-dropdown>
        </span>

      </a-table>
    </div>

    <aigc-agent-modal ref="modalForm" @ok="modalFormOk"/>
  </a-card>
</template>

<script>

  import { JeecgListMixin } from '@/mixins/JeecgListMixin'
  import AigcAgentModal from './modules/AigcAgentModal'
  import {filterMultiDictText} from '@/components/dict/JDictSelectUtil'
  import '@/assets/less/TableExpand.less'

  export default {
    name: "AigcAgentList",
    mixins:[JeecgListMixin],
    components: {
      AigcAgentModal
    },
    data () {
      return {
        description: '智能体表管理页面',
        // 表头
        columns: [
          {
            title: '#',
            dataIndex: '',
            key:'rowIndex',
            width:60,
            align:"center",
            customRender:function (t,r,index) {
              return parseInt(index)+1;
            }
          },
          {
            title:'作者类型',
            align:"center",
            dataIndex: 'authorType_dictText'
          },
          // 智能体ID字段已隐藏 - 系统自动生成，用户不需要看到
          // {
          //   title:'智能体ID',
          //   align:"center",
          //   dataIndex: 'agentId'
          // },
          {
            title:'智能体名称',
            align:"center",
            dataIndex: 'agentName'
          },
          {
            title:'智能体描述',
            align:"center",
            dataIndex: 'agentDescription'
          },
          {
            title:'智能体头像',
            align:"center",
            dataIndex: 'agentAvatar',
            scopedSlots: {customRender: 'imgSlot'}
          },
          {
            title:'展示视频',
            align:"center",
            dataIndex: 'demoVideo',
            scopedSlots: {customRender: 'fileSlot'}
          },
          {
            title:'体验链接',
            align:"center",
            dataIndex: 'experienceLink'
          },
          {
            title:'价格（元）',
            align:"center",
            dataIndex: 'price'
          },
          {
            title:'审核状态',
            align:"center",
            dataIndex: 'auditStatus_dictText'
          },
          {
            title:'审核备注',
            align:"center",
            dataIndex: 'auditRemark'
          },
          {
            title: '操作',
            dataIndex: 'action',
            align:"center",
            fixed:"right",
            width:147,
            scopedSlots: { customRender: 'action' },
          }
        ],
        url: {
          list: "/aigc_agent/aigcAgent/list",
          delete: "/aigc_agent/aigcAgent/delete",
          deleteBatch: "/aigc_agent/aigcAgent/deleteBatch",
          exportXlsUrl: "/aigc_agent/aigcAgent/exportXls",
          importExcelUrl: "aigc_agent/aigcAgent/importExcel",
          
        },
        dictOptions:{},
        superFieldList:[],
      }
    },
    created() {
      this.getSuperFieldList();
    },
    computed: {
      importExcelUrl: function(){
        return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`;
      }
    },
    methods: {
      initDictConfig(){
      },
      getSuperFieldList(){
        let fieldList=[];
         fieldList.push({type:'string',value:'authorType',text:'作者类型',dictCode:'author_type'})
         // 智能体ID字段已隐藏 - 系统自动生成，用户不需要搜索
         // fieldList.push({type:'string',value:'agentId',text:'智能体ID',dictCode:''})
         fieldList.push({type:'string',value:'agentName',text:'智能体名称',dictCode:''})
         fieldList.push({type:'string',value:'agentDescription',text:'智能体描述',dictCode:''})
         fieldList.push({type:'string',value:'agentAvatar',text:'智能体头像',dictCode:''})
         fieldList.push({type:'string',value:'demoVideo',text:'展示视频',dictCode:''})
         fieldList.push({type:'string',value:'experienceLink',text:'体验链接',dictCode:''})
         fieldList.push({type:'BigDecimal',value:'price',text:'价格（元）',dictCode:''})
         fieldList.push({type:'string',value:'auditStatus',text:'审核状态',dictCode:'audit_status'})
         fieldList.push({type:'string',value:'auditRemark',text:'审核备注',dictCode:''})
        this.superFieldList = fieldList
      }
    }
  }
</script>
<style scoped>
  @import '~@assets/less/common.less';
</style>