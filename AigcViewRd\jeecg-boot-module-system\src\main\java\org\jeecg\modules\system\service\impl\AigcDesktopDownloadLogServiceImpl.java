package org.jeecg.modules.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.system.entity.AigcDesktopDownloadLog;
import org.jeecg.modules.system.mapper.AigcDesktopDownloadLogMapper;
import org.jeecg.modules.system.service.IAigcDesktopDownloadLogService;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 桌面应用下载记录Service实现类
 * 
 * <AUTHOR>
 * @date 2025-01-12
 */
@Slf4j
@Service
public class AigcDesktopDownloadLogServiceImpl extends ServiceImpl<AigcDesktopDownloadLogMapper, AigcDesktopDownloadLog> 
        implements IAigcDesktopDownloadLogService {

    @Override
    public void logDownloadSuccess(String platform, String fileName, String fileDescription, 
                                  String clientIp, String userAgent, String signedUrl, Long responseTime) {
        try {
            AigcDesktopDownloadLog downloadLog = new AigcDesktopDownloadLog();
            downloadLog.setPlatform(platform);
            downloadLog.setFileName(fileName);
            downloadLog.setFileDescription(fileDescription);
            downloadLog.setClientIp(clientIp);
            downloadLog.setUserAgent(userAgent);
            downloadLog.setDownloadStatus(1); // 成功
            downloadLog.setSignedUrl(signedUrl);
            downloadLog.setRequestTime(new Date());
            downloadLog.setResponseTime(responseTime);
            downloadLog.setCreateTime(new Date());
            
            this.save(downloadLog);
            
            log.debug("桌面应用下载成功记录已保存: platform={}, fileName={}, IP={}", 
                     platform, fileName, clientIp);
                     
        } catch (Exception e) {
            log.error("保存桌面应用下载成功记录失败: platform={}, fileName={}, IP={}", 
                     platform, fileName, clientIp, e);
        }
    }

    @Override
    public void logDownloadFailure(String platform, String fileName, String clientIp, 
                                  String userAgent, String errorMessage, Long responseTime) {
        try {
            AigcDesktopDownloadLog downloadLog = new AigcDesktopDownloadLog();
            downloadLog.setPlatform(platform);
            downloadLog.setFileName(fileName);
            downloadLog.setClientIp(clientIp);
            downloadLog.setUserAgent(userAgent);
            downloadLog.setDownloadStatus(0); // 失败
            downloadLog.setErrorMessage(errorMessage);
            downloadLog.setRequestTime(new Date());
            downloadLog.setResponseTime(responseTime);
            downloadLog.setCreateTime(new Date());
            
            this.save(downloadLog);
            
            log.debug("桌面应用下载失败记录已保存: platform={}, fileName={}, IP={}, error={}", 
                     platform, fileName, clientIp, errorMessage);
                     
        } catch (Exception e) {
            log.error("保存桌面应用下载失败记录失败: platform={}, fileName={}, IP={}", 
                     platform, fileName, clientIp, e);
        }
    }

    @Override
    public List<Map<String, Object>> getTodayDownloadStats() {
        try {
            return this.baseMapper.getTodayDownloadStats();
        } catch (Exception e) {
            log.error("获取今日下载统计失败", e);
            return null;
        }
    }

    @Override
    public List<Map<String, Object>> getDownloadTrend(int days) {
        try {
            return this.baseMapper.getDownloadTrend(days);
        } catch (Exception e) {
            log.error("获取下载趋势数据失败: days={}", days, e);
            return null;
        }
    }

    @Override
    public int checkDownloadFrequency(String clientIp, int minutes) {
        try {
            if (oConvertUtils.isEmpty(clientIp)) {
                return 0;
            }
            return this.baseMapper.getDownloadCountByIp(clientIp, minutes);
        } catch (Exception e) {
            log.error("检查下载频率失败: IP={}, minutes={}", clientIp, minutes, e);
            return 0;
        }
    }

    @Override
    public List<Map<String, Object>> getPlatformStats() {
        try {
            return this.baseMapper.getPlatformStats();
        } catch (Exception e) {
            log.error("获取平台统计数据失败", e);
            return null;
        }
    }

    @Override
    public List<AigcDesktopDownloadLog> getRecentDownloads(int limit) {
        try {
            return this.baseMapper.getRecentDownloads(limit);
        } catch (Exception e) {
            log.error("获取最近下载记录失败: limit={}", limit, e);
            return null;
        }
    }

    @Override
    public IPage<AigcDesktopDownloadLog> getRecentDownloadsPage(int pageNo, int pageSize) {
        try {
            Page<AigcDesktopDownloadLog> page = new Page<>(pageNo, pageSize);
            QueryWrapper<AigcDesktopDownloadLog> queryWrapper = new QueryWrapper<>();
            queryWrapper.orderByDesc("request_time");
            return this.page(page, queryWrapper);
        } catch (Exception e) {
            log.error("获取最近下载记录分页失败: pageNo={}, pageSize={}", pageNo, pageSize, e);
            return null;
        }
    }
}
