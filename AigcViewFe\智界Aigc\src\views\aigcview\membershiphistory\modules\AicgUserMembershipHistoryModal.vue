<template>
  <a-modal
    :title="title"
    :width="800"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel"
    cancelText="关闭">
    
    <a-spin :spinning="confirmLoading">
      <a-form-model ref="form" :model="model" :rules="validatorRules" v-bind="layout">
        <a-row>
          <a-col :span="24">
            <a-form-model-item label="用户ID" prop="userId">
              <a-input v-model="model.userId" placeholder="请输入用户ID"></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="订单ID" prop="orderId">
              <a-input v-model="model.orderId" placeholder="请输入订单ID"></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="升级前角色" prop="fromRoleId">
              <a-select v-model="model.fromRoleId" placeholder="请选择升级前角色" allowClear>
                <a-select-option v-for="role in roleOptions" :key="role.id" :value="role.id">
                  {{ role.roleName }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="升级后角色" prop="toRoleId">
              <a-select v-model="model.toRoleId" placeholder="请选择升级后角色" allowClear>
                <a-select-option v-for="role in roleOptions" :key="role.id" :value="role.id">
                  {{ role.roleName }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="会员等级" prop="memberLevel">
              <a-select v-model="model.memberLevel" placeholder="请选择会员等级">
                <a-select-option :value="1">普通用户</a-select-option>
                <a-select-option :value="2">VIP会员</a-select-option>
                <a-select-option :value="3">SVIP会员</a-select-option>
                <a-select-option :value="4">至尊会员</a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="订阅时长(月)" prop="durationMonths">
              <a-input-number v-model="model.durationMonths" :min="1" :max="120" placeholder="请输入订阅时长"></a-input-number>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="订阅金额" prop="amount">
              <a-input-number v-model="model.amount" :min="0" :precision="2" placeholder="请输入订阅金额"></a-input-number>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="状态" prop="status">
              <a-select v-model="model.status" placeholder="请选择状态">
                <a-select-option :value="1">生效中</a-select-option>
                <a-select-option :value="2">已过期</a-select-option>
                <a-select-option :value="3">已取消</a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="开始时间" prop="startTime">
              <a-date-picker 
                v-model="model.startTime" 
                show-time 
                format="YYYY-MM-DD HH:mm:ss"
                placeholder="请选择开始时间"
                style="width: 100%">
              </a-date-picker>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="结束时间" prop="endTime">
              <a-date-picker 
                v-model="model.endTime" 
                show-time 
                format="YYYY-MM-DD HH:mm:ss"
                placeholder="请选择结束时间"
                style="width: 100%">
              </a-date-picker>
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </a-spin>
  </a-modal>
</template>

<script>
  import { httpAction, getAction } from '@/api/manage'
  import moment from 'moment'

  export default {
    name: 'AicgUserMembershipHistoryModal',
    components: {
    },
    data () {
      return {
        layout: {
          labelCol: {
            xs: { span: 24 },
            sm: { span: 5 },
          },
          wrapperCol: {
            xs: { span: 24 },
            sm: { span: 16 },
          },
        },
        title:"操作",
        visible: false,
        model: {},
        confirmLoading: false,
        form: this.$form.createForm(this),
        roleOptions: [],
        validatorRules: {
          userId: [
            { required: true, message: '请输入用户ID!' },
          ],
          toRoleId: [
            { required: true, message: '请选择升级后角色!' },
          ],
          memberLevel: [
            { required: true, message: '请选择会员等级!' },
          ],
          durationMonths: [
            { required: true, message: '请输入订阅时长!' },
          ],
          amount: [
            { required: true, message: '请输入订阅金额!' },
          ],
          startTime: [
            { required: true, message: '请选择开始时间!' },
          ],
          endTime: [
            { required: true, message: '请选择结束时间!' },
          ],
        },
        url: {
          add: "/demo/membershiphistory/add",
          edit: "/demo/membershiphistory/edit",
          queryById: "/demo/membershiphistory/queryById"
        }
      }
    },
    created() {
      this.loadRoleOptions()
    },
    methods: {
      add () {
        this.edit({});
      },
      edit (record) {
        this.form.resetFields();
        this.model = Object.assign({}, record);
        this.visible = true;
        this.$nextTick(() => {
          const formData = {
            userId: this.model.userId,
            orderId: this.model.orderId,
            fromRoleId: this.model.fromRoleId,
            toRoleId: this.model.toRoleId,
            memberLevel: this.model.memberLevel,
            durationMonths: this.model.durationMonths,
            amount: this.model.amount,
            status: this.model.status
          }
          this.form.setFieldsValue(formData)
          if(this.model.startTime) {
            this.model.startTime = moment(this.model.startTime)
          }
          if(this.model.endTime) {
            this.model.endTime = moment(this.model.endTime)
          }
        })
        if(record.id){
          this.title = "编辑";
        }else{
          this.title = "新增";
        }
      },
      close () {
        this.$emit('close');
        this.visible = false;
      },
      handleOk () {
        const that = this;
        // 触发表单验证
        this.$refs.form.validate(valid => {
          if(valid) {
            that.confirmLoading = true;
            let httpurl = '';
            let method = '';
            if(!this.model.id){
              httpurl+=this.url.add;
              method = 'post';
            }else{
              httpurl+=this.url.edit;
              method = 'put';
            }
            let formData = Object.assign(this.model);
            if(formData.startTime) {
              formData.startTime = formData.startTime.format('YYYY-MM-DD HH:mm:ss')
            }
            if(formData.endTime) {
              formData.endTime = formData.endTime.format('YYYY-MM-DD HH:mm:ss')
            }
            httpAction(httpurl,formData,method).then((res)=>{
              if(res.success){
                that.$message.success(res.message);
                that.$emit('ok');
              }else{
                that.$message.warning(res.message);
              }
            }).finally(() => {
              that.confirmLoading = false;
            })
          }
        })
      },
      handleCancel () {
        this.close()
      },
      loadRoleOptions() {
        getAction('/sys/role/list').then(res => {
          if (res.success) {
            this.roleOptions = res.result.records || []
          }
        })
      }
    }
  }
</script>
