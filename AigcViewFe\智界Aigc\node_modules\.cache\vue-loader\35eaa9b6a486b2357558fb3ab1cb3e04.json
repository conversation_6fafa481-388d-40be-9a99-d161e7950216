{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\workflow\\components\\AgentDetailModal.vue?vue&type=template&id=f8301f64&scoped=true&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\workflow\\components\\AgentDetailModal.vue", "mtime": 1754510290845}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["var render = function() {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  return _c(\n    \"div\",\n    { staticClass: \"agent-detail-modal-wrapper\" },\n    [\n      _c(\n        \"a-modal\",\n        {\n          staticClass: \"agent-detail-modal custom-modal\",\n          attrs: {\n            visible: _vm.visible,\n            width: 800,\n            footer: null,\n            closable: false,\n            maskClosable: true,\n            bodyStyle: { padding: 0, borderRadius: \"16px\", overflow: \"hidden\" },\n            centered: true,\n            destroyOnClose: true\n          },\n          on: { cancel: _vm.handleClose }\n        },\n        [\n          _vm.loading\n            ? _c(\n                \"div\",\n                { staticClass: \"loading-container\" },\n                [\n                  _c(\"a-spin\", { attrs: { size: \"large\", tip: \"加载中...\" } }, [\n                    _c(\"div\", { staticClass: \"loading-placeholder\" })\n                  ])\n                ],\n                1\n              )\n            : _c(\"div\", { staticClass: \"modal-content\" }, [\n                _c(\n                  \"div\",\n                  {\n                    staticClass: \"custom-close-button\",\n                    on: { click: _vm.handleClose }\n                  },\n                  [_c(\"a-icon\", { attrs: { type: \"close\" } })],\n                  1\n                ),\n                _c(\"div\", { staticClass: \"modal-background\" }, [\n                  _c(\"div\", { staticClass: \"bg-pattern\" }),\n                  _c(\"div\", { staticClass: \"bg-gradient\" })\n                ]),\n                _c(\"div\", { staticClass: \"agent-info-section\" }, [\n                  _c(\"div\", { staticClass: \"agent-header\" }, [\n                    _c(\"div\", { staticClass: \"agent-avatar\" }, [\n                      _vm.agentDetail.agentAvatar\n                        ? _c(\"img\", {\n                            attrs: {\n                              src: _vm.agentDetail.agentAvatar,\n                              alt: _vm.agentDetail.agentName\n                            },\n                            on: { error: _vm.handleImageError }\n                          })\n                        : _c(\n                            \"div\",\n                            { staticClass: \"avatar-placeholder\" },\n                            [_c(\"a-icon\", { attrs: { type: \"robot\" } })],\n                            1\n                          )\n                    ]),\n                    _c(\"div\", { staticClass: \"agent-info-and-price\" }, [\n                      _c(\"div\", { staticClass: \"agent-basic-info\" }, [\n                        _c(\"h2\", { staticClass: \"agent-name\" }, [\n                          _vm._v(_vm._s(_vm.agentDetail.agentName))\n                        ]),\n                        _c(\"p\", { staticClass: \"agent-description\" }, [\n                          _vm._v(_vm._s(_vm.agentDetail.agentDescription))\n                        ]),\n                        _c(\"div\", { staticClass: \"creator-info\" }, [\n                          _c(\n                            \"div\",\n                            { staticClass: \"creator-avatar\" },\n                            [\n                              _vm.agentDetail.creatorInfo &&\n                              _vm.agentDetail.creatorInfo.avatar\n                                ? _c(\"img\", {\n                                    attrs: {\n                                      src: _vm.agentDetail.creatorInfo.avatar,\n                                      alt: _vm.agentDetail.creatorInfo.name\n                                    },\n                                    on: { error: _vm.handleCreatorAvatarError }\n                                  })\n                                : _c(\"a-icon\", { attrs: { type: \"user\" } })\n                            ],\n                            1\n                          ),\n                          _c(\"div\", { staticClass: \"creator-details\" }, [\n                            _c(\"div\", { staticClass: \"creator-name-line\" }, [\n                              _c(\"span\", { staticClass: \"creator-name\" }, [\n                                _vm._v(_vm._s(_vm.creatorName))\n                              ]),\n                              _c(\"span\", { staticClass: \"creator-type\" }, [\n                                _vm._v(_vm._s(_vm.authorTypeText))\n                              ])\n                            ])\n                          ])\n                        ])\n                      ]),\n                      _c(\"div\", { staticClass: \"price-section\" }, [\n                        _vm.agentDetail.isFree\n                          ? _c(\"div\", { staticClass: \"price-container\" }, [\n                              _c(\"span\", { staticClass: \"free-price\" }, [\n                                _vm._v(\"免费\")\n                              ])\n                            ])\n                          : _vm.isPurchased\n                          ? _c(\"div\", { staticClass: \"price-container\" }, [\n                              _c(\"span\", { staticClass: \"purchased-price\" }, [\n                                _vm._v(\"已购买\")\n                              ])\n                            ])\n                          : _vm.agentDetail.showDiscountPrice\n                          ? _c(\"div\", { staticClass: \"price-container\" }, [\n                              _c(\"span\", { staticClass: \"discount-price\" }, [\n                                _vm._v(\n                                  \"¥\" +\n                                    _vm._s(_vm.agentDetail.discountPrice || 0)\n                                )\n                              ]),\n                              _c(\"span\", { staticClass: \"original-price\" }, [\n                                _vm._v(\n                                  \"¥\" +\n                                    _vm._s(\n                                      _vm.agentDetail.originalPrice ||\n                                        _vm.agentDetail.price ||\n                                        0\n                                    )\n                                )\n                              ])\n                            ])\n                          : _c(\"div\", { staticClass: \"price-container\" }, [\n                              _c(\"span\", { staticClass: \"current-price\" }, [\n                                _vm._v(\n                                  \"¥\" +\n                                    _vm._s(\n                                      _vm.agentDetail.discountPrice ||\n                                        _vm.agentDetail.price ||\n                                        _vm.agentDetail.originalPrice ||\n                                        0\n                                    )\n                                )\n                              ])\n                            ])\n                      ])\n                    ])\n                  ])\n                ]),\n                _vm.agentDetail.demoVideo\n                  ? _c(\"div\", { staticClass: \"demo-video-section\" }, [\n                      _c(\n                        \"h3\",\n                        { staticClass: \"section-title\" },\n                        [\n                          _c(\"a-icon\", { attrs: { type: \"play-circle\" } }),\n                          _vm._v(\"\\n        演示视频\\n      \")\n                        ],\n                        1\n                      ),\n                      _c(\"div\", { staticClass: \"video-container\" }, [\n                        !_vm.videoError\n                          ? _c(\"div\", { staticClass: \"video-wrapper\" }, [\n                              _c(\n                                \"video\",\n                                {\n                                  ref: \"demoVideo\",\n                                  staticClass: \"demo-video\",\n                                  attrs: {\n                                    src: _vm.agentDetail.demoVideo,\n                                    autoplay: _vm.videoAutoplay,\n                                    preload: \"metadata\"\n                                  },\n                                  domProps: { muted: _vm.videoMuted },\n                                  on: {\n                                    loadstart: _vm.handleVideoLoadStart,\n                                    loadeddata: _vm.handleVideoLoaded,\n                                    error: _vm.handleVideoError,\n                                    play: _vm.handleVideoPlay,\n                                    pause: _vm.handleVideoPause,\n                                    timeupdate: _vm.handleVideoTimeUpdate,\n                                    volumechange: _vm.handleVolumeChange,\n                                    click: _vm.toggleVideoPlay\n                                  }\n                                },\n                                [\n                                  _vm._v(\n                                    \"\\n            您的浏览器不支持视频播放\\n          \"\n                                  )\n                                ]\n                              ),\n                              _c(\n                                \"div\",\n                                {\n                                  directives: [\n                                    {\n                                      name: \"show\",\n                                      rawName: \"v-show\",\n                                      value: _vm.showControls,\n                                      expression: \"showControls\"\n                                    }\n                                  ],\n                                  staticClass: \"video-controls\"\n                                },\n                                [\n                                  _c(\n                                    \"div\",\n                                    { staticClass: \"controls-left\" },\n                                    [\n                                      _c(\"a-button\", {\n                                        staticClass: \"control-btn play-btn\",\n                                        attrs: {\n                                          type: \"link\",\n                                          icon: _vm.videoPlaying\n                                            ? \"pause\"\n                                            : \"caret-right\"\n                                        },\n                                        on: { click: _vm.toggleVideoPlay }\n                                      }),\n                                      _c(\n                                        \"span\",\n                                        { staticClass: \"time-display\" },\n                                        [\n                                          _vm._v(\n                                            \"\\n                \" +\n                                              _vm._s(\n                                                _vm.formatTime(_vm.currentTime)\n                                              ) +\n                                              \" / \" +\n                                              _vm._s(\n                                                _vm.formatTime(_vm.duration)\n                                              ) +\n                                              \"\\n              \"\n                                          )\n                                        ]\n                                      )\n                                    ],\n                                    1\n                                  ),\n                                  _c(\n                                    \"div\",\n                                    { staticClass: \"controls-center\" },\n                                    [\n                                      _c(\n                                        \"div\",\n                                        {\n                                          staticClass: \"progress-container\",\n                                          on: { click: _vm.handleProgressClick }\n                                        },\n                                        [\n                                          _c(\n                                            \"div\",\n                                            { staticClass: \"progress-bar\" },\n                                            [\n                                              _c(\"div\", {\n                                                staticClass: \"progress-filled\",\n                                                style: {\n                                                  width:\n                                                    _vm.progressPercent + \"%\"\n                                                }\n                                              }),\n                                              _c(\"div\", {\n                                                staticClass: \"progress-thumb\",\n                                                style: {\n                                                  left:\n                                                    _vm.progressPercent + \"%\"\n                                                }\n                                              })\n                                            ]\n                                          )\n                                        ]\n                                      )\n                                    ]\n                                  ),\n                                  _c(\n                                    \"div\",\n                                    { staticClass: \"controls-right\" },\n                                    [\n                                      _c(\n                                        \"div\",\n                                        {\n                                          staticClass: \"volume-control\",\n                                          on: {\n                                            mouseenter: function($event) {\n                                              _vm.showVolumeSlider = true\n                                            },\n                                            mouseleave: function($event) {\n                                              _vm.showVolumeSlider = false\n                                            }\n                                          }\n                                        },\n                                        [\n                                          _c(\"a-button\", {\n                                            staticClass:\n                                              \"control-btn volume-btn\",\n                                            attrs: {\n                                              type: \"link\",\n                                              icon: _vm.videoMuted\n                                                ? \"sound\"\n                                                : \"sound-filled\"\n                                            },\n                                            on: { click: _vm.toggleMute }\n                                          }),\n                                          _c(\n                                            \"div\",\n                                            {\n                                              directives: [\n                                                {\n                                                  name: \"show\",\n                                                  rawName: \"v-show\",\n                                                  value: _vm.showVolumeSlider,\n                                                  expression: \"showVolumeSlider\"\n                                                }\n                                              ],\n                                              staticClass: \"volume-slider\"\n                                            },\n                                            [\n                                              _c(\"a-slider\", {\n                                                staticClass: \"volume-range\",\n                                                attrs: {\n                                                  min: 0,\n                                                  max: 100,\n                                                  step: 1,\n                                                  vertical: \"\",\n                                                  \"tip-formatter\": null\n                                                },\n                                                on: {\n                                                  change:\n                                                    _vm.handleVolumeSliderChange\n                                                },\n                                                model: {\n                                                  value: _vm.videoVolume,\n                                                  callback: function($$v) {\n                                                    _vm.videoVolume = $$v\n                                                  },\n                                                  expression: \"videoVolume\"\n                                                }\n                                              })\n                                            ],\n                                            1\n                                          )\n                                        ],\n                                        1\n                                      ),\n                                      _c(\"a-button\", {\n                                        staticClass:\n                                          \"control-btn fullscreen-btn\",\n                                        attrs: {\n                                          type: \"link\",\n                                          icon: \"fullscreen\"\n                                        },\n                                        on: { click: _vm.toggleFullscreen }\n                                      })\n                                    ],\n                                    1\n                                  )\n                                ]\n                              ),\n                              _c(\n                                \"div\",\n                                {\n                                  directives: [\n                                    {\n                                      name: \"show\",\n                                      rawName: \"v-show\",\n                                      value: _vm.videoLoading,\n                                      expression: \"videoLoading\"\n                                    }\n                                  ],\n                                  staticClass: \"video-loading\"\n                                },\n                                [\n                                  _c(\n                                    \"a-spin\",\n                                    { attrs: { size: \"large\" } },\n                                    [\n                                      _c(\"a-icon\", {\n                                        staticStyle: { \"font-size\": \"24px\" },\n                                        attrs: {\n                                          slot: \"indicator\",\n                                          type: \"loading\",\n                                          spin: \"\"\n                                        },\n                                        slot: \"indicator\"\n                                      })\n                                    ],\n                                    1\n                                  ),\n                                  _c(\"p\", [_vm._v(\"视频加载中...\")])\n                                ],\n                                1\n                              )\n                            ])\n                          : _vm._e(),\n                        _vm.videoError\n                          ? _c(\n                              \"div\",\n                              { staticClass: \"video-error-placeholder\" },\n                              [\n                                _c(\n                                  \"div\",\n                                  { staticClass: \"error-content\" },\n                                  [\n                                    _c(\"a-icon\", {\n                                      staticClass: \"error-icon\",\n                                      attrs: { type: \"exclamation-circle\" }\n                                    }),\n                                    _c(\"h4\", [_vm._v(\"视频加载失败\")]),\n                                    _c(\"p\", [\n                                      _vm._v(\"抱歉，演示视频暂时无法播放\")\n                                    ]),\n                                    _c(\n                                      \"a-button\",\n                                      {\n                                        attrs: { type: \"primary\", ghost: \"\" },\n                                        on: { click: _vm.retryVideoLoad }\n                                      },\n                                      [\n                                        _c(\"a-icon\", {\n                                          attrs: { type: \"reload\" }\n                                        }),\n                                        _vm._v(\n                                          \"\\n              重新加载\\n            \"\n                                        )\n                                      ],\n                                      1\n                                    )\n                                  ],\n                                  1\n                                )\n                              ]\n                            )\n                          : _vm._e()\n                      ])\n                    ])\n                  : _vm._e(),\n                _c(\"div\", { staticClass: \"workflow-section\" }, [\n                  _c(\"h3\", { staticClass: \"section-title\" }, [\n                    _vm._v(\"\\n        工作流列表\\n        \"),\n                    _c(\"span\", { staticClass: \"workflow-count\" }, [\n                      _vm._v(\"(\" + _vm._s(_vm.workflowList.length) + \"个)\")\n                    ])\n                  ]),\n                  _vm.workflowLoading\n                    ? _c(\n                        \"div\",\n                        { staticClass: \"workflow-loading\" },\n                        [_c(\"a-spin\", { attrs: { tip: \"加载工作流中...\" } })],\n                        1\n                      )\n                    : _vm.workflowList.length > 0\n                    ? _c(\n                        \"div\",\n                        { staticClass: \"workflow-list\" },\n                        _vm._l(_vm.workflowList, function(workflow, index) {\n                          return _c(\n                            \"div\",\n                            { key: workflow.id, staticClass: \"workflow-item\" },\n                            [\n                              _c(\"div\", { staticClass: \"workflow-info\" }, [\n                                _c(\n                                  \"div\",\n                                  { staticClass: \"workflow-sequence\" },\n                                  [_vm._v(_vm._s(index + 1))]\n                                ),\n                                _c(\n                                  \"div\",\n                                  { staticClass: \"workflow-avatar\" },\n                                  [\n                                    workflow.agentAvatar ||\n                                    _vm.agentDetail.agentAvatar\n                                      ? _c(\"img\", {\n                                          attrs: {\n                                            src:\n                                              workflow.agentAvatar ||\n                                              _vm.agentDetail.agentAvatar,\n                                            alt: workflow.workflowName\n                                          },\n                                          on: {\n                                            error: _vm.handleWorkflowImageError\n                                          }\n                                        })\n                                      : _c(\"a-icon\", {\n                                          attrs: { type: \"setting\" }\n                                        })\n                                  ],\n                                  1\n                                ),\n                                _c(\"div\", { staticClass: \"workflow-details\" }, [\n                                  _c(\"h4\", { staticClass: \"workflow-name\" }, [\n                                    _vm._v(_vm._s(workflow.workflowName))\n                                  ]),\n                                  _c(\n                                    \"p\",\n                                    { staticClass: \"workflow-description\" },\n                                    [\n                                      _vm._v(\n                                        _vm._s(workflow.workflowDescription)\n                                      )\n                                    ]\n                                  ),\n                                  _c(\n                                    \"div\",\n                                    { staticClass: \"workflow-params\" },\n                                    [\n                                      _c(\n                                        \"div\",\n                                        { staticClass: \"params-label\" },\n                                        [\n                                          _c(\"a-icon\", {\n                                            attrs: { type: \"setting\" }\n                                          }),\n                                          _c(\"span\", [_vm._v(\"输入参数说明\")])\n                                        ],\n                                        1\n                                      ),\n                                      _c(\n                                        \"div\",\n                                        { staticClass: \"params-content\" },\n                                        [\n                                          _vm._v(\n                                            \"\\n                  \" +\n                                              _vm._s(\n                                                workflow.inputParamsDesc ||\n                                                  \"暂无输入参数说明\"\n                                              ) +\n                                              \"\\n                \"\n                                          )\n                                        ]\n                                      )\n                                    ]\n                                  )\n                                ])\n                              ]),\n                              _c(\n                                \"div\",\n                                { staticClass: \"workflow-actions\" },\n                                [\n                                  !_vm.isPurchased && !_vm.agentDetail.isFree\n                                    ? _c(\n                                        \"a-button\",\n                                        {\n                                          attrs: {\n                                            type: \"default\",\n                                            disabled: \"\"\n                                          },\n                                          on: { click: _vm.handleDownloadTip }\n                                        },\n                                        [\n                                          _c(\"a-icon\", {\n                                            attrs: { type: \"download\" }\n                                          }),\n                                          _vm._v(\n                                            \"\\n                请先购买\\n              \"\n                                          )\n                                        ],\n                                        1\n                                      )\n                                    : _c(\n                                        \"a-button\",\n                                        {\n                                          attrs: {\n                                            type: \"primary\",\n                                            loading:\n                                              _vm.downloadLoading[workflow.id]\n                                          },\n                                          on: {\n                                            click: function($event) {\n                                              return _vm.handleWorkflowDownload(\n                                                workflow\n                                              )\n                                            }\n                                          }\n                                        },\n                                        [\n                                          _c(\"a-icon\", {\n                                            attrs: { type: \"download\" }\n                                          }),\n                                          _vm._v(\n                                            \"\\n                下载\\n              \"\n                                          )\n                                        ],\n                                        1\n                                      )\n                                ],\n                                1\n                              )\n                            ]\n                          )\n                        }),\n                        0\n                      )\n                    : _c(\n                        \"div\",\n                        { staticClass: \"workflow-empty\" },\n                        [\n                          _c(\"a-empty\", {\n                            attrs: { description: \"暂无工作流\" }\n                          })\n                        ],\n                        1\n                      )\n                ]),\n                _c(\n                  \"div\",\n                  { staticClass: \"action-buttons modern-actions\" },\n                  [\n                    _c(\n                      \"a-button\",\n                      {\n                        staticClass: \"close-btn modern-btn-secondary\",\n                        on: { click: _vm.handleClose }\n                      },\n                      [\n                        _c(\"a-icon\", { attrs: { type: \"close\" } }),\n                        _vm._v(\"\\n        关闭\\n      \")\n                      ],\n                      1\n                    ),\n                    _c(\n                      \"div\",\n                      { staticClass: \"primary-actions\" },\n                      [\n                        _vm.isPurchased || _vm.agentDetail.isFree\n                          ? _c(\n                              \"a-button\",\n                              {\n                                staticClass: \"detail-btn modern-btn-outline\",\n                                attrs: { type: \"default\" },\n                                on: { click: _vm.handleViewDetail }\n                              },\n                              [\n                                _c(\"a-icon\", { attrs: { type: \"eye\" } }),\n                                _vm._v(\"\\n            查看详情\\n          \")\n                              ],\n                              1\n                            )\n                          : _c(\n                              \"a-button\",\n                              {\n                                staticClass:\n                                  \"detail-btn modern-btn-outline disabled\",\n                                attrs: { type: \"default\", disabled: \"\" }\n                              },\n                              [\n                                _c(\"a-icon\", { attrs: { type: \"eye\" } }),\n                                _vm._v(\"\\n            查看详情\\n          \")\n                              ],\n                              1\n                            ),\n                        !_vm.isPurchased && !_vm.agentDetail.isFree\n                          ? _c(\n                              \"a-button\",\n                              {\n                                staticClass: \"purchase-btn modern-btn-primary\",\n                                attrs: {\n                                  type: \"primary\",\n                                  loading: _vm.purchaseLoading\n                                },\n                                on: { click: _vm.handlePurchase }\n                              },\n                              [\n                                _c(\"a-icon\", {\n                                  attrs: { type: \"shopping-cart\" }\n                                }),\n                                _vm._v(\"\\n            立即购买\\n          \")\n                              ],\n                              1\n                            )\n                          : _vm._e(),\n                        _vm.agentDetail.experienceLink\n                          ? _c(\n                              \"a-button\",\n                              {\n                                staticClass:\n                                  \"experience-btn modern-btn-outline\",\n                                attrs: { type: \"default\" },\n                                on: { click: _vm.handleExperience }\n                              },\n                              [\n                                _c(\"a-icon\", { attrs: { type: \"robot\" } }),\n                                _vm._v(\"\\n          体验智能体\\n        \")\n                              ],\n                              1\n                            )\n                          : _c(\n                              \"a-button\",\n                              {\n                                staticClass:\n                                  \"experience-btn modern-btn-outline disabled\",\n                                attrs: { type: \"default\", disabled: \"\" }\n                              },\n                              [\n                                _c(\"a-icon\", { attrs: { type: \"robot\" } }),\n                                _vm._v(\"\\n          暂无体验\\n        \")\n                              ],\n                              1\n                            )\n                      ],\n                      1\n                    )\n                  ],\n                  1\n                )\n              ])\n        ]\n      ),\n      _c(\n        \"a-modal\",\n        {\n          staticClass: \"payment-modal\",\n          attrs: {\n            title: \"选择支付方式\",\n            width: 520,\n            footer: null,\n            maskClosable: false,\n            centered: true\n          },\n          model: {\n            value: _vm.showPaymentModal,\n            callback: function($$v) {\n              _vm.showPaymentModal = $$v\n            },\n            expression: \"showPaymentModal\"\n          }\n        },\n        [\n          _c(\"div\", { staticClass: \"payment-content\" }, [\n            _c(\"div\", { staticClass: \"order-info-card\" }, [\n              _c(\"div\", { staticClass: \"order-header\" }, [\n                _c(\n                  \"div\",\n                  { staticClass: \"order-icon\" },\n                  [_c(\"a-icon\", { attrs: { type: \"shopping-cart\" } })],\n                  1\n                ),\n                _c(\"div\", { staticClass: \"order-title\" }, [\n                  _c(\"h3\", [_vm._v(\"订单详情\")]),\n                  _c(\"p\", [_vm._v(\"请确认您的购买信息\")])\n                ])\n              ]),\n              _c(\"div\", { staticClass: \"order-details\" }, [\n                _c(\"div\", { staticClass: \"order-item\" }, [\n                  _c(\"span\", { staticClass: \"label\" }, [_vm._v(\"智能体名称\")]),\n                  _c(\"span\", { staticClass: \"value\" }, [\n                    _vm._v(_vm._s(_vm.orderInfo && _vm.orderInfo.agentName))\n                  ])\n                ]),\n                _c(\"div\", { staticClass: \"order-item total\" }, [\n                  _c(\"span\", { staticClass: \"label\" }, [_vm._v(\"支付金额\")]),\n                  _c(\"span\", { staticClass: \"price\" }, [\n                    _vm._v(\n                      \"¥\" + _vm._s(_vm.orderInfo && _vm.orderInfo.purchasePrice)\n                    )\n                  ])\n                ])\n              ])\n            ]),\n            _c(\"div\", { staticClass: \"payment-methods\" }, [\n              _c(\"div\", { staticClass: \"payment-header\" }, [\n                _c(\"h3\", [_vm._v(\"选择支付方式\")]),\n                _c(\"p\", [_vm._v(\"请选择您偏好的支付方式完成购买\")])\n              ]),\n              _c(\n                \"div\",\n                {\n                  staticClass: \"payment-option\",\n                  class: {\n                    insufficient:\n                      _vm.userBalance <\n                      (_vm.orderInfo && _vm.orderInfo.purchasePrice),\n                    selected: _vm.selectedPaymentMethod === \"balance\"\n                  },\n                  on: {\n                    click: function($event) {\n                      return _vm.selectPaymentMethod(\"balance\")\n                    }\n                  }\n                },\n                [\n                  _c(\n                    \"div\",\n                    { staticClass: \"payment-icon balance\" },\n                    [_c(\"a-icon\", { attrs: { type: \"wallet\" } })],\n                    1\n                  ),\n                  _c(\"div\", { staticClass: \"payment-info\" }, [\n                    _c(\"div\", { staticClass: \"payment-title\" }, [\n                      _vm._v(\"\\n            账户余额支付\\n            \"),\n                      _vm.userBalance <\n                      (_vm.orderInfo && _vm.orderInfo.purchasePrice)\n                        ? _c(\"span\", { staticClass: \"insufficient-tag\" }, [\n                            _vm._v(\"余额不足\")\n                          ])\n                        : _vm._e()\n                    ]),\n                    _c(\"div\", { staticClass: \"payment-desc\" }, [\n                      _vm._v(\"当前余额：¥\" + _vm._s(_vm.userBalance))\n                    ])\n                  ]),\n                  _c(\n                    \"div\",\n                    { staticClass: \"payment-status\" },\n                    [\n                      _vm.selectedPaymentMethod === \"balance\"\n                        ? _c(\"a-icon\", {\n                            staticClass: \"selected-icon\",\n                            attrs: { type: \"check-circle\" }\n                          })\n                        : _vm.userBalance <\n                          (_vm.orderInfo && _vm.orderInfo.purchasePrice)\n                        ? _c(\"a-icon\", {\n                            staticClass: \"insufficient\",\n                            attrs: { type: \"exclamation-circle\" }\n                          })\n                        : _c(\"a-icon\", {\n                            staticClass: \"available\",\n                            attrs: { type: \"wallet\" }\n                          })\n                    ],\n                    1\n                  )\n                ]\n              ),\n              _c(\n                \"div\",\n                {\n                  staticClass: \"payment-option\",\n                  class: { selected: _vm.selectedPaymentMethod === \"alipay\" },\n                  on: {\n                    click: function($event) {\n                      return _vm.selectPaymentMethod(\"alipay\")\n                    }\n                  }\n                },\n                [\n                  _c(\n                    \"div\",\n                    { staticClass: \"payment-icon alipay\" },\n                    [_c(\"a-icon\", { attrs: { type: \"alipay\" } })],\n                    1\n                  ),\n                  _c(\"div\", { staticClass: \"payment-info\" }, [\n                    _c(\"div\", { staticClass: \"payment-title\" }, [\n                      _vm._v(\"\\n            支付宝支付\\n          \")\n                    ]),\n                    _c(\"div\", { staticClass: \"payment-desc\" }, [\n                      _vm._v(\"安全便捷的在线支付\")\n                    ])\n                  ]),\n                  _c(\n                    \"div\",\n                    { staticClass: \"payment-status\" },\n                    [\n                      _vm.selectedPaymentMethod === \"alipay\"\n                        ? _c(\"a-icon\", {\n                            staticClass: \"selected-icon\",\n                            attrs: { type: \"check-circle\" }\n                          })\n                        : _c(\"a-icon\", { attrs: { type: \"right\" } })\n                    ],\n                    1\n                  )\n                ]\n              )\n            ]),\n            _c(\n              \"div\",\n              { staticClass: \"payment-actions\" },\n              [\n                _c(\n                  \"a-button\",\n                  {\n                    staticClass: \"cancel-btn\",\n                    on: {\n                      click: function($event) {\n                        _vm.showPaymentModal = false\n                      }\n                    }\n                  },\n                  [\n                    _c(\"a-icon\", { attrs: { type: \"close\" } }),\n                    _vm._v(\"\\n        取消支付\\n      \")\n                  ],\n                  1\n                ),\n                _c(\n                  \"a-button\",\n                  {\n                    staticClass: \"confirm-payment-btn\",\n                    attrs: {\n                      type: \"primary\",\n                      disabled:\n                        !_vm.selectedPaymentMethod ||\n                        (_vm.selectedPaymentMethod === \"balance\" &&\n                          _vm.userBalance <\n                            (_vm.orderInfo && _vm.orderInfo.purchasePrice)),\n                      loading: _vm.paymentLoading\n                    },\n                    on: { click: _vm.confirmPayment }\n                  },\n                  [\n                    _c(\"a-icon\", { attrs: { type: \"credit-card\" } }),\n                    _vm._v(\n                      \"\\n        确认支付 ¥\" +\n                        _vm._s(_vm.orderInfo && _vm.orderInfo.purchasePrice) +\n                        \"\\n      \"\n                    )\n                  ],\n                  1\n                )\n              ],\n              1\n            )\n          ]),\n          _vm.paymentLoading\n            ? _c(\n                \"div\",\n                { staticClass: \"payment-loading\" },\n                [\n                  _c(\"a-spin\", {\n                    attrs: { size: \"large\", tip: \"处理支付中...\" }\n                  })\n                ],\n                1\n              )\n            : _vm._e()\n        ]\n      )\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"]}