<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>测试图片滑动功能</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            background: linear-gradient(135deg, #ff6b6b, #feca57);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .container {
            max-width: 375px;
            width: 100%;
            background: white;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }

        .images-carousel {
            position: relative;
            width: 100%;
            height: 400px;
            overflow: hidden;
            background: #f5f5f5;
        }

        .carousel-slides {
            display: flex;
            width: 100%;
            height: 100%;
            transition: transform 0.3s ease;
        }

        .image-slide {
            min-width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 48px;
            color: white;
            font-weight: bold;
        }

        .image-slide:nth-child(1) { background: #ff6b6b; }
        .image-slide:nth-child(2) { background: #4ecdc4; }
        .image-slide:nth-child(3) { background: #45b7d1; }
        .image-slide:nth-child(4) { background: #96ceb4; }
        .image-slide:nth-child(5) { background: #feca57; }

        .carousel-dots {
            display: flex;
            justify-content: center;
            padding: 20px;
            gap: 8px;
        }

        .carousel-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #ddd;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .carousel-dot.active {
            background: #ff6b6b;
            transform: scale(1.2);
        }

        .content {
            padding: 20px;
            text-align: center;
        }

        .title {
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }

        .description {
            color: #666;
            line-height: 1.5;
            margin-bottom: 20px;
        }

        .debug-info {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-top: 20px;
            font-size: 12px;
            color: #666;
            text-align: left;
        }

        .debug-info h4 {
            margin-bottom: 10px;
            color: #333;
        }

        .debug-log {
            background: #fff;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
            max-height: 150px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 11px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="images-carousel" id="imagesCarousel">
            <div class="carousel-slides" id="carouselSlides">
                <div class="image-slide">图片 1</div>
                <div class="image-slide">图片 2</div>
                <div class="image-slide">图片 3</div>
                <div class="image-slide">图片 4</div>
                <div class="image-slide">图片 5</div>
            </div>
        </div>

        <div class="carousel-dots" id="carouselDots">
            <div class="carousel-dot active" onclick="goToImage(0)"></div>
            <div class="carousel-dot" onclick="goToImage(1)"></div>
            <div class="carousel-dot" onclick="goToImage(2)"></div>
            <div class="carousel-dot" onclick="goToImage(3)"></div>
            <div class="carousel-dot" onclick="goToImage(4)"></div>
        </div>

        <div class="content">
            <div class="title">触摸滑动测试</div>
            <div class="description">
                在图片区域左右滑动可以切换图片<br>
                当前图片: <span id="currentIndex">1</span> / 5
            </div>

            <div class="debug-info">
                <h4>调试信息</h4>
                <div class="debug-log" id="debugLog"></div>
            </div>
        </div>
    </div>

    <script>
        let currentImageIndex = 0;
        let totalImages = 5;
        let touchStartX = 0;
        let touchEndX = 0;
        let touchStartY = 0;
        let touchEndY = 0;
        let isDragging = false;

        function log(message) {
            const debugLog = document.getElementById('debugLog');
            const time = new Date().toLocaleTimeString();
            debugLog.innerHTML += `[${time}] ${message}<br>`;
            debugLog.scrollTop = debugLog.scrollHeight;
            console.log(message);
        }

        function updateCarousel() {
            const slides = document.getElementById('carouselSlides');
            const dots = document.querySelectorAll('.carousel-dot');
            const currentIndexSpan = document.getElementById('currentIndex');

            slides.style.transform = `translateX(-${currentImageIndex * 100}%)`;
            
            dots.forEach((dot, index) => {
                dot.classList.toggle('active', index === currentImageIndex);
            });

            currentIndexSpan.textContent = currentImageIndex + 1;
            log(`切换到图片 ${currentImageIndex + 1}`);
        }

        function prevImage() {
            currentImageIndex = (currentImageIndex - 1 + totalImages) % totalImages;
            updateCarousel();
        }

        function nextImage() {
            currentImageIndex = (currentImageIndex + 1) % totalImages;
            updateCarousel();
        }

        function goToImage(index) {
            currentImageIndex = index;
            updateCarousel();
        }

        function handleTouchStart(e) {
            touchStartX = e.touches[0].clientX;
            touchStartY = e.touches[0].clientY;
            isDragging = false;
            log(`触摸开始: X=${Math.round(touchStartX)}, Y=${Math.round(touchStartY)}`);
        }

        function handleTouchMove(e) {
            if (!touchStartX) return;
            
            const touchCurrentX = e.touches[0].clientX;
            const touchCurrentY = e.touches[0].clientY;
            
            const deltaX = Math.abs(touchCurrentX - touchStartX);
            const deltaY = Math.abs(touchCurrentY - touchStartY);
            
            if (deltaX > deltaY && deltaX > 10) {
                e.preventDefault();
                isDragging = true;
                log(`水平滑动中: ΔX=${Math.round(deltaX)}, ΔY=${Math.round(deltaY)}`);
            }
        }

        function handleTouchEnd(e) {
            if (!touchStartX || !isDragging) {
                log('触摸结束: 未检测到有效滑动');
                return;
            }
            
            touchEndX = e.changedTouches[0].clientX;
            touchEndY = e.changedTouches[0].clientY;
            
            const deltaX = touchEndX - touchStartX;
            const deltaY = Math.abs(touchEndY - touchStartY);
            
            log(`触摸结束: ΔX=${Math.round(deltaX)}, ΔY=${Math.round(deltaY)}`);
            
            if (Math.abs(deltaX) > 50 && deltaY < 100) {
                if (deltaX > 0) {
                    log('👈 向右滑动，显示上一张图片');
                    prevImage();
                } else {
                    log('👉 向左滑动，显示下一张图片');
                    nextImage();
                }
            } else {
                log('滑动距离不足，未切换图片');
            }
            
            touchStartX = 0;
            touchEndX = 0;
            touchStartY = 0;
            touchEndY = 0;
            isDragging = false;
        }

        // 初始化
        window.onload = function() {
            const imageContainer = document.getElementById('imagesCarousel');
            
            imageContainer.addEventListener('touchstart', handleTouchStart, { passive: false });
            imageContainer.addEventListener('touchmove', handleTouchMove, { passive: false });
            imageContainer.addEventListener('touchend', handleTouchEnd, { passive: false });
            
            log('✅ 触摸滑动功能已启用');
            log('📱 请在移动设备上测试滑动功能');
            
            updateCarousel();
        };
    </script>
</body>
</html>
