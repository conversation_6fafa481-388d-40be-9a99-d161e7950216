package org.jeecg.modules.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @Description: 心跳API请求参数
 * @Author: AigcView
 * @Date: 2025-01-31
 * @Version: V1.0
 */
@Data
@ApiModel(value = "HeartbeatRequest", description = "心跳API请求参数")
public class HeartbeatRequest {

    @ApiModelProperty(value = "API密钥", required = true, example = "your_api_key_here")
    @NotBlank(message = "API密钥不能为空")
    private String apiKey;

    @ApiModelProperty(value = "时间戳（毫秒）", required = false, example = "1640995200000")
    private Long timestamp;

    @ApiModelProperty(value = "签名（用于安全验证）", required = false, example = "signature_hash")
    private String signature;

    @ApiModelProperty(value = "客户端版本", required = false, example = "v1.0.0")
    private String clientVersion;

    @ApiModelProperty(value = "设备信息", required = false, example = "Windows 10, Chrome 96")
    private String deviceInfo;

    @ApiModelProperty(value = "地理位置", required = false, example = "Beijing, China")
    private String location;

    @ApiModelProperty(value = "客户端类型", required = false, example = "WEB/MOBILE/DESKTOP")
    private String clientType;

    @ApiModelProperty(value = "应用版本", required = false, example = "1.2.3")
    private String appVersion;

    @ApiModelProperty(value = "扩展信息（JSON格式）", required = false, example = "{\"extra\":\"data\"}")
    private String extraInfo;

    /**
     * 验证时间戳是否在有效范围内
     * @param maxAgeMinutes 最大允许的时间差（分钟）
     * @return 是否有效
     */
    public boolean isTimestampValid(int maxAgeMinutes) {
        if (timestamp == null) {
            return true; // 时间戳为空时不验证
        }
        
        long currentTime = System.currentTimeMillis();
        long timeDiff = Math.abs(currentTime - timestamp);
        long maxAge = maxAgeMinutes * 60 * 1000L; // 转换为毫秒
        
        return timeDiff <= maxAge;
    }

    /**
     * 获取用于签名验证的字符串
     * @return 签名字符串
     */
    public String getSignatureString() {
        StringBuilder sb = new StringBuilder();
        sb.append("apiKey=").append(apiKey != null ? apiKey : "");
        if (timestamp != null) {
            sb.append("&timestamp=").append(timestamp);
        }
        if (clientVersion != null) {
            sb.append("&clientVersion=").append(clientVersion);
        }
        return sb.toString();
    }

    /**
     * 检查是否包含基本必需参数
     * @return 是否有效
     */
    public boolean isValid() {
        return apiKey != null && !apiKey.trim().isEmpty();
    }

    /**
     * 获取客户端信息摘要
     * @return 客户端信息字符串
     */
    public String getClientSummary() {
        StringBuilder summary = new StringBuilder();
        
        if (clientType != null) {
            summary.append(clientType);
        }
        
        if (clientVersion != null) {
            if (summary.length() > 0) summary.append(" ");
            summary.append("v").append(clientVersion);
        }
        
        if (deviceInfo != null) {
            if (summary.length() > 0) summary.append(" - ");
            summary.append(deviceInfo);
        }
        
        return summary.length() > 0 ? summary.toString() : "Unknown Client";
    }

    /**
     * 转换为日志友好的字符串
     * @return 日志字符串
     */
    public String toLogString() {
        return String.format("HeartbeatRequest{apiKey='%s...', timestamp=%d, clientType='%s', clientVersion='%s'}",
                apiKey != null && apiKey.length() > 8 ? apiKey.substring(0, 8) : "null",
                timestamp,
                clientType,
                clientVersion);
    }
}
