interface CreateTosProxyMiddlewareOpts {
    destHost?: string | ((proxyParam: string) => string);
    /**
     * force change proxy request protocol.
     * usage:
     * 1. change localhost tos-sdk request http protocol to https protocol.
     * 2. TOS server force https protocol
     */
    protocol?: 'http' | 'https';
}
/**
 *
 * @param context is context of http-proxy-middleware. refer: https://github.com/chimurai/http-proxy-middleware
 * @param {CreateTosProxyMiddlewareOpts} opts
 */
export declare function createTosProxyMiddleware(context: string, opts?: CreateTosProxyMiddlewareOpts): import("http-proxy-middleware").RequestHandler;
export {};
