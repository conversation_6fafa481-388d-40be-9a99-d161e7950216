import { Acl, AclInterface } from '../../../interface';
import TOSBase from '../../base';
export interface GetObjectAclInput {
    bucket?: string;
    key: string;
    versionId?: string;
}
export declare type ObjectAclBody = AclInterface & {
    BucketOwnerEntrusted?: boolean;
    IsDefault?: boolean;
};
export declare type GetObjectAclOutput = ObjectAclBody;
export declare function getObjectAcl(this: TOSBase, input: GetObjectAclInput | string): Promise<import("../../base").TosResponse<ObjectAclBody>>;
export interface PutObjectAclInput {
    bucket?: string;
    key: string;
    versionId?: string;
    acl?: Acl;
    aclBody?: ObjectAclBody;
    headers?: {
        [key: string]: string | undefined;
        'x-tos-acl'?: Acl;
    };
}
export declare function putObjectAcl(this: TOSBase, input: PutObjectAclInput): Promise<import("../../base").TosResponse<undefined>>;
