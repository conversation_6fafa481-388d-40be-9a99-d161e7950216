<template>
  <div class="user-center">
    <a-card title="智界Aigc - 个人中心" :bordered="false" class="main-card">
      <!-- 用户基本信息 -->
      <a-row :gutter="24">
        <a-col :span="8">
          <a-card size="small" title="基本信息" class="info-card">
            <div class="user-info">
              <a-avatar :size="80" :src="getAvatarUrl(userInfo.avatar)" icon="user" class="user-avatar" />
              <div class="info-content">
                <h3>{{ userProfile.nickname || userInfo.realname }}</h3>
                <p>用户ID: {{ userInfo.id }}</p>
                <p>注册时间: {{ userInfo.createTime | formatDate }}</p>
              </div>
            </div>
            <a-button type="primary" @click="showEditNickname = true" block>
              编辑昵称
            </a-button>
          </a-card>
        </a-col>
        
        <a-col :span="8">
          <a-card size="small" title="账户余额" class="balance-card">
            <div class="balance-info">
              <h2 class="balance-amount">¥{{ userProfile.accountBalance || 0 }}</h2>
              <p>累计消费: ¥{{ userProfile.totalConsumption || 0 }}</p>
              <p>累计充值: ¥{{ userProfile.totalRecharge || 0 }}</p>
            </div>
            <a-button type="primary" @click="showRecharge = true" block>
              充值
            </a-button>
          </a-card>
        </a-col>
        
        <a-col :span="8">
          <a-card size="small" title="API密钥" class="api-card">
            <div class="api-key-info">
              <a-input 
                :value="maskedApiKey" 
                readonly 
                style="margin-bottom: 8px;"
                placeholder="暂无API密钥"
              />
              <a-button-group style="width: 100%;">
                <a-button @click="toggleApiKeyVisibility" style="width: 50%;">
                  {{ apiKeyVisible ? '隐藏' : '显示' }}
                </a-button>
                <a-button @click="regenerateApiKey" style="width: 50%;">
                  重新生成
                </a-button>
              </a-button-group>
              <p class="api-key-tips">请妥善保管您的API密钥</p>
            </div>
          </a-card>
        </a-col>
      </a-row>
      
      <!-- 功能区域 -->
      <a-row :gutter="24" style="margin-top: 24px;">
        <a-col :span="12">
          <a-card size="small" title="兑换码" class="exchange-card">
            <a-input-search
              v-model="exchangeCode"
              placeholder="请输入兑换码"
              enter-button="兑换"
              @search="useExchangeCode"
            />
            <div style="margin-top: 16px;">
              <a-button @click="loadUsedCodes(); showUsedCodes = true" block>
                查看兑换记录
              </a-button>
            </div>
          </a-card>
        </a-col>
        
        <a-col :span="12">
          <a-card size="small" title="会员信息" class="member-card">
            <div class="member-info">
              <a-tag :color="getMemberColor(userProfile.currentRole)" class="member-tag">
                {{ getMemberText(userProfile.currentRole) }}
              </a-tag>
              <p v-if="userProfile.memberExpireTime">
                到期时间: {{ userProfile.memberExpireTime | formatDate }}
              </p>
              <p v-else>
                永久有效
              </p>
            </div>
          </a-card>
        </a-col>
      </a-row>
      
      <!-- 消费记录 -->
      <a-card title="消费记录" style="margin-top: 24px;" class="transaction-card">
        <a-table
          :columns="transactionColumns"
          :data-source="transactionList"
          :pagination="transactionPagination"
          @change="handleTransactionTableChange"
          size="small"
          :loading="transactionLoading"
        >
          <template slot="transactionType" slot-scope="text">
            <a-tag :color="getTransactionTypeColor(text)">
              {{ getTransactionTypeText(text) }}
            </a-tag>
          </template>
          <template slot="amount" slot-scope="text, record">
            <span :class="record.transactionType === 1 ? 'text-red' : 'text-green'">
              {{ record.transactionType === 1 ? '-' : '+' }}¥{{ text }}
            </span>
          </template>
          <template slot="transactionTime" slot-scope="text">
            {{ text | formatDateTime }}
          </template>
        </a-table>
      </a-card>
    </a-card>
    
    <!-- 编辑昵称弹窗 -->
    <a-modal
      title="编辑昵称"
      :visible="showEditNickname"
      @ok="updateNickname"
      @cancel="cancelEditNickname"
      :confirmLoading="nicknameLoading"
    >
      <a-input 
        v-model="newNickname" 
        placeholder="请输入新昵称" 
        :maxLength="20"
        @pressEnter="updateNickname"
      />
    </a-modal>
    
    <!-- 充值弹窗 -->
    <a-modal
      title="账户充值"
      :visible="showRecharge"
      @ok="handleRecharge"
      @cancel="showRecharge = false"
      :confirmLoading="rechargeLoading"
    >
      <p>请选择充值金额或输入自定义金额：</p>
      <a-radio-group v-model="rechargeAmount" style="margin-bottom: 16px;">
        <a-radio-button :value="10">¥10</a-radio-button>
        <a-radio-button :value="50">¥50</a-radio-button>
        <a-radio-button :value="100">¥100</a-radio-button>
        <a-radio-button :value="500">¥500</a-radio-button>
      </a-radio-group>
      <a-input-number
        v-model="customAmount"
        :min="1"
        :max="10000"
        placeholder="自定义金额"
        style="width: 100%;"
      />
    </a-modal>
    
    <!-- 兑换记录弹窗 -->
    <a-modal
      title="兑换记录"
      :visible="showUsedCodes"
      @cancel="showUsedCodes = false"
      :footer="null"
      width="800px"
    >
      <a-table
        :columns="exchangeCodeColumns"
        :data-source="usedCodesList"
        size="small"
        :pagination="false"
        :loading="exchangeCodesLoading"
      >
        <template slot="codeType" slot-scope="text">
          {{ getExchangeCodeTypeText(text) }}
        </template>
        <template slot="value" slot-scope="text">
          ¥{{ text }}
        </template>
        <template slot="usedTime" slot-scope="text">
          {{ text | formatDateTime }}
        </template>
      </a-table>
    </a-modal>
  </div>
</template>

<script>
import { getUserProfile, updateNickname, regenerateApiKey, getUserTransactions, useExchangeCode, getUserUsedCodes } from '@/api/usercenter'
import { mapGetters } from 'vuex'

export default {
  name: 'UserCenter',
  data() {
    return {
      userProfile: {},
      showEditNickname: false,
      showRecharge: false,
      showUsedCodes: false,
      newNickname: '',
      exchangeCode: '',
      rechargeAmount: 50,
      customAmount: null,
      apiKeyVisible: false,
      transactionList: [],
      usedCodesList: [],
      // 加载状态
      profileLoading: false,
      transactionLoading: false,
      exchangeCodesLoading: false,
      nicknameLoading: false,
      rechargeLoading: false,
      transactionPagination: {
        current: 1,
        pageSize: 10,
        total: 0,
        showSizeChanger: true,
        showQuickJumper: true,
        showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
      },
      transactionColumns: [
        {
          title: '交易时间',
          dataIndex: 'transactionTime',
          key: 'transactionTime',
          width: 150,
          scopedSlots: { customRender: 'transactionTime' },
        },
        {
          title: '交易类型',
          dataIndex: 'transactionType',
          key: 'transactionType',
          width: 100,
          scopedSlots: { customRender: 'transactionType' },
        },
        {
          title: '金额',
          dataIndex: 'amount',
          key: 'amount',
          width: 120,
          scopedSlots: { customRender: 'amount' },
        },
        {
          title: '余额',
          dataIndex: 'balanceAfter',
          key: 'balanceAfter',
          width: 120,
          render: (text) => `¥${text}`,
        },
        {
          title: '描述',
          dataIndex: 'description',
          key: 'description',
          ellipsis: true,
        },
      ],
      exchangeCodeColumns: [
        {
          title: '兑换码',
          dataIndex: 'code',
          key: 'code',
          width: 150,
        },
        {
          title: '类型',
          dataIndex: 'codeType',
          key: 'codeType',
          width: 80,
          scopedSlots: { customRender: 'codeType' },
        },
        {
          title: '价值',
          dataIndex: 'value',
          key: 'value',
          width: 100,
          scopedSlots: { customRender: 'value' },
        },
        {
          title: '使用时间',
          dataIndex: 'usedTime',
          key: 'usedTime',
          width: 150,
          scopedSlots: { customRender: 'usedTime' },
        },
      ],
    }
  },
  computed: {
    ...mapGetters(['userInfo']),
    maskedApiKey() {
      if (!this.userProfile.apiKey) return ''
      if (this.apiKeyVisible) {
        return this.userProfile.apiKey
      }
      const key = this.userProfile.apiKey
      return key.substring(0, 8) + '****' + key.substring(key.length - 4)
    },
  },
  mounted() {
    this.loadUserProfile()
    this.loadTransactions()
  },
  methods: {
    // 获取头像URL（和官网一样的方式）
    getAvatarUrl(avatar) {
      if (!avatar) {
        return '';
      }

      // 如果是完整的URL，直接返回
      if (avatar.startsWith('http://') || avatar.startsWith('https://')) {
        return avatar;
      }

      // 如果是相对路径，使用getFileAccessHttpUrl转换
      return this.getFileAccessHttpUrl(avatar) || '';
    },

    // 添加getFileAccessHttpUrl方法
    getFileAccessHttpUrl(avatar) {
      if (!avatar) return '';

      // 如果已经是完整URL，直接返回
      if (avatar.startsWith('http://') || avatar.startsWith('https://')) {
        return avatar;
      }

      // 如果是TOS文件，使用全局方法
      if (avatar.startsWith('uploads/')) {
        return window.getFileAccessHttpUrl ? window.getFileAccessHttpUrl(avatar) : avatar;
      }

      // 本地文件，使用静态域名
      return this.$store.state.app.staticDomainURL + '/' + avatar;
    },

    async loadUserProfile() {
      try {
        this.profileLoading = true
        const response = await getUserProfile()
        if (response.success) {
          this.userProfile = response.result
        } else {
          this.$message.error(response.message || '加载用户信息失败')
        }
      } catch (error) {
        console.error('加载用户信息失败:', error)
        this.$message.error('加载用户信息失败')
      } finally {
        this.profileLoading = false
      }
    },
    
    async loadTransactions() {
      try {
        this.transactionLoading = true
        const response = await getUserTransactions({
          pageNo: this.transactionPagination.current,
          pageSize: this.transactionPagination.pageSize,
        })
        if (response.success) {
          this.transactionList = response.result.records || []
          this.transactionPagination.total = response.result.total || 0
        } else {
          this.$message.error(response.message || '加载交易记录失败')
        }
      } catch (error) {
        console.error('加载交易记录失败:', error)
        this.$message.error('加载交易记录失败')
      } finally {
        this.transactionLoading = false
      }
    },
    
    async updateNickname() {
      if (!this.newNickname.trim()) {
        this.$message.warning('请输入昵称')
        return
      }
      
      try {
        this.nicknameLoading = true
        const response = await updateNickname({ nickname: this.newNickname })
        if (response.success) {
          this.$message.success('昵称更新成功')
          this.userProfile.nickname = this.newNickname
          this.showEditNickname = false
          this.newNickname = ''
        } else {
          this.$message.error(response.message || '昵称更新失败')
        }
      } catch (error) {
        console.error('昵称更新失败:', error)
        this.$message.error('昵称更新失败')
      } finally {
        this.nicknameLoading = false
      }
    },
    
    cancelEditNickname() {
      this.showEditNickname = false
      this.newNickname = ''
    },
    
    async regenerateApiKey() {
      this.$confirm({
        title: '确认重新生成API密钥？',
        content: '重新生成后，原密钥将失效，请确保已更新相关配置',
        onOk: async () => {
          try {
            const response = await regenerateApiKey()
            if (response.success) {
              this.$message.success('API密钥重新生成成功')
              this.userProfile.apiKey = response.result
              this.apiKeyVisible = true // 生成后自动显示
            } else {
              this.$message.error(response.message || 'API密钥重新生成失败')
            }
          } catch (error) {
            console.error('API密钥重新生成失败:', error)
            this.$message.error('API密钥重新生成失败')
          }
        },
      })
    },
    
    toggleApiKeyVisibility() {
      this.apiKeyVisible = !this.apiKeyVisible
    },
    
    async useExchangeCode() {
      if (!this.exchangeCode.trim()) {
        this.$message.warning('请输入兑换码')
        return
      }
      
      try {
        const response = await useExchangeCode({ code: this.exchangeCode })
        if (response.success) {
          this.$message.success(response.message || '兑换成功')
          this.exchangeCode = ''
          this.loadUserProfile()
          this.loadTransactions()
        } else {
          this.$message.error(response.message || '兑换失败')
        }
      } catch (error) {
        console.error('兑换失败:', error)
        this.$message.error('兑换失败')
      }
    },
    
    handleRecharge() {
      const amount = this.customAmount || this.rechargeAmount
      if (!amount || amount <= 0) {
        this.$message.warning('请选择或输入充值金额')
        return
      }
      
      // 这里应该调用支付接口
      this.$message.info(`充值功能开发中，选择金额：¥${amount}`)
      this.showRecharge = false
      this.customAmount = null
    },
    
    async loadUsedCodes() {
      try {
        this.exchangeCodesLoading = true
        const response = await getUserUsedCodes()
        if (response.success) {
          this.usedCodesList = response.result || []
        } else {
          this.$message.error(response.message || '加载兑换记录失败')
        }
      } catch (error) {
        console.error('加载兑换记录失败:', error)
        this.$message.error('加载兑换记录失败')
      } finally {
        this.exchangeCodesLoading = false
      }
    },
    
    handleTransactionTableChange(pagination) {
      this.transactionPagination.current = pagination.current
      this.transactionPagination.pageSize = pagination.pageSize
      this.loadTransactions()
    },
    
    getMemberColor(role) {
      const colors = {
        '普通用户': 'default',
        'VIP': 'gold',
        'SVIP': 'purple',
        '管理员': 'red'
      }
      return colors[role] || 'default'
    },

    getMemberText(role) {
      // 直接返回角色名称
      return role || '普通用户'
    },
    
    getTransactionTypeColor(type) {
      const colors = { 1: 'red', 2: 'green', 3: 'blue', 4: 'orange' }
      return colors[type] || 'default'
    },
    
    getTransactionTypeText(type) {
      const texts = { 1: '消费', 2: '充值', 3: '退款', 4: '兑换' }
      return texts[type] || '未知'
    },
    
    getExchangeCodeTypeText(type) {
      const texts = { 1: '余额', 2: '会员', 3: '积分' }
      return texts[type] || '未知'
    },
  },
  filters: {
    formatDate(date) {
      if (!date) return ''
      return new Date(date).toLocaleDateString('zh-CN')
    },
    formatDateTime(date) {
      if (!date) return ''
      return new Date(date).toLocaleString('zh-CN')
    },
  },
}
</script>

<style scoped>
.user-center {
  padding: 24px;
  background-color: #f0f2f5;
  min-height: 100vh;
}

.main-card {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-radius: 12px;
}

.main-card .ant-card-head-title {
  color: #1890ff;
  font-size: 20px;
  font-weight: bold;
}

.info-card, .balance-card, .api-card, .exchange-card, .member-card, .transaction-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  border-radius: 8px;
  transition: all 0.3s ease;
}

.info-card:hover, .balance-card:hover, .api-card:hover, .exchange-card:hover, .member-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
}

.user-info {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.user-avatar {
  border: 3px solid #1890ff;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
}

.info-content {
  margin-left: 16px;
  flex: 1;
}

.info-content h3 {
  margin: 0 0 8px 0;
  color: #1890ff;
  font-size: 18px;
  font-weight: bold;
}

.info-content p {
  margin: 4px 0;
  color: #666;
  font-size: 12px;
}

.balance-info {
  text-align: center;
  margin-bottom: 16px;
}

.balance-amount {
  font-size: 36px;
  color: #52c41a;
  margin: 0 0 16px 0;
  font-weight: bold;
  text-shadow: 0 2px 4px rgba(82, 196, 26, 0.2);
}

.balance-info p {
  margin: 4px 0;
  color: #666;
  font-size: 14px;
}

.api-key-info {
  margin-bottom: 16px;
}

.api-key-tips {
  font-size: 12px;
  color: #999;
  margin: 8px 0 0 0;
  text-align: center;
}

.member-info {
  text-align: center;
  padding: 16px 0;
}

.member-tag {
  font-size: 16px;
  padding: 6px 16px;
  border-radius: 20px;
  font-weight: bold;
}

.member-info p {
  margin: 12px 0 0 0;
  color: #666;
  font-size: 14px;
}

.text-red {
  color: #f5222d;
  font-weight: bold;
}

.text-green {
  color: #52c41a;
  font-weight: bold;
}

/* 按钮样式优化 */
.ant-btn {
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.ant-btn-primary {
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
  border: none;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
}

.ant-btn-primary:hover {
  background: linear-gradient(135deg, #40a9ff 0%, #1890ff 100%);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.4);
  transform: translateY(-1px);
}

/* 按钮组样式 */
.ant-btn-group .ant-btn {
  border-radius: 0;
}

.ant-btn-group .ant-btn:first-child {
  border-top-left-radius: 6px;
  border-bottom-left-radius: 6px;
}

.ant-btn-group .ant-btn:last-child {
  border-top-right-radius: 6px;
  border-bottom-right-radius: 6px;
}

/* 表格样式优化 */
.ant-table-tbody > tr > td {
  padding: 12px 16px;
}

.ant-table-tbody > tr:hover > td {
  background-color: #f5f7fa;
}

.ant-table-thead > tr > th {
  background-color: #fafafa;
  font-weight: 600;
  color: #262626;
}

/* 输入框样式 */
.ant-input, .ant-input-number {
  border-radius: 6px;
  border: 1px solid #d9d9d9;
  transition: all 0.3s ease;
}

.ant-input:focus, .ant-input-number:focus {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 兑换码输入框样式 */
.ant-input-search .ant-input-group .ant-input {
  border-radius: 6px 0 0 6px;
}

.ant-input-search .ant-input-group .ant-btn {
  border-radius: 0 6px 6px 0;
  background: linear-gradient(135deg, #52c41a 0%, #389e0d 100%);
  border-color: #52c41a;
}

.ant-input-search .ant-input-group .ant-btn:hover {
  background: linear-gradient(135deg, #73d13d 0%, #52c41a 100%);
  border-color: #73d13d;
}

/* 标签样式 */
.ant-tag {
  border-radius: 16px;
  font-weight: 500;
  padding: 4px 12px;
}

/* 模态框样式优化 */
.ant-modal {
  border-radius: 12px;
  overflow: hidden;
}

.ant-modal-header {
  border-bottom: 1px solid #f0f0f0;
  background: linear-gradient(135deg, #fafafa 0%, #f5f5f5 100%);
}

.ant-modal-title {
  color: #1890ff;
  font-weight: bold;
  font-size: 16px;
}

.ant-modal-body {
  padding: 24px;
}

/* 充值金额选择按钮样式 */
.ant-radio-button-wrapper {
  border-radius: 6px !important;
  margin-right: 8px;
  border: 1px solid #d9d9d9;
  transition: all 0.3s ease;
}

.ant-radio-button-wrapper:hover {
  border-color: #1890ff;
  color: #1890ff;
}

.ant-radio-button-wrapper-checked {
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
  border-color: #1890ff;
  color: white;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
}

/* 卡片标题样式 */
.ant-card-head-title {
  color: #1890ff;
  font-weight: bold;
  font-size: 16px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .user-center {
    padding: 16px;
  }
}

@media (max-width: 768px) {
  .user-center {
    padding: 12px;
  }

  .user-info {
    flex-direction: column;
    text-align: center;
  }

  .info-content {
    margin-left: 0;
    margin-top: 12px;
  }

  .balance-amount {
    font-size: 28px;
  }

  .ant-col {
    margin-bottom: 16px;
  }
}

@media (max-width: 576px) {
  .balance-amount {
    font-size: 24px;
  }

  .member-tag {
    font-size: 14px;
    padding: 4px 12px;
  }

  .ant-btn-group .ant-btn {
    font-size: 12px;
    padding: 4px 8px;
  }
}

/* 加载状态样式 */
.ant-spin-container {
  transition: opacity 0.3s ease;
}

/* 空状态样式 */
.ant-empty {
  margin: 40px 0;
}

/* 分页样式 */
.ant-pagination {
  margin-top: 24px;
  text-align: center;
}

.ant-pagination-item {
  border-radius: 6px;
}

.ant-pagination-item-active {
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
  border-color: #1890ff;
}

.ant-pagination-item-active a {
  color: white;
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.user-center .ant-card {
  animation: fadeInUp 0.6s ease-out;
}

.user-center .ant-card:nth-child(2) {
  animation-delay: 0.1s;
}

.user-center .ant-card:nth-child(3) {
  animation-delay: 0.2s;
}

.user-center .ant-card:nth-child(4) {
  animation-delay: 0.3s;
}
</style>
