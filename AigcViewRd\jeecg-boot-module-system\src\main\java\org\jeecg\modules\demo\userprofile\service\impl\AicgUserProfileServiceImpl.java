package org.jeecg.modules.demo.userprofile.service.impl;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.UUID;

import org.jeecg.modules.demo.userprofile.entity.AicgUserProfile;
import org.jeecg.modules.demo.userprofile.mapper.AicgUserProfileMapper;
import org.jeecg.modules.demo.userprofile.service.IAicgUserProfileService;
import org.jeecg.modules.demo.userrecord.entity.AicgUserRecord;
import org.jeecg.modules.demo.userrecord.service.IAicgUserRecordService;
import org.jeecg.modules.demo.plubshop.entity.AigcPlubShop;
import org.jeecg.modules.demo.plubshop.service.IAigcPlubShopService;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.jeecg.modules.system.service.ISensitiveWordService;
import org.jeecg.modules.system.service.ISysUserService;
import org.jeecg.modules.system.entity.SysUser;
import org.jeecg.common.util.SpringContextUtils;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import lombok.extern.slf4j.Slf4j;

/**
 * @Description: 用户扩展信息表
 * @Author: jeecg-boot
 * @Date: 2025-06-14
 * @Version: V1.0
 */
@Service
@Slf4j
public class AicgUserProfileServiceImpl extends ServiceImpl<AicgUserProfileMapper, AicgUserProfile>
        implements IAicgUserProfileService {

    @Autowired
    private IAicgUserRecordService userRecordService;

    @Autowired
    private IAigcPlubShopService plubShopService;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Override
    public AicgUserProfile getByUserId(String userId) {
        return baseMapper.getByUserId(userId);
    }

    @Override
    public AicgUserProfile getByUsername(String username) {
        return baseMapper.getByUsername(username);
    }

    @Override
    public java.util.Map<String, Object> getUserFullInfo(String username) {
        return baseMapper.getUserFullInfo(username);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AicgUserProfile initUserProfile(String userId, String nickname) {
        AicgUserProfile profile = getByUserId(userId);
        if (profile == null) {
            profile = new AicgUserProfile();
            profile.setUserId(userId);
            profile.setNickname(nickname);
            profile.setAccountBalance(BigDecimal.ZERO);
            profile.setApiKey(generateApiKey());
            profile.setTotalConsumption(BigDecimal.ZERO);
            profile.setTotalRecharge(BigDecimal.ZERO);
            // 会员等级通过角色表管理，不在用户扩展表中存储
            profile.setStatus(1); // 正常状态
            profile.setCreateTime(new Date());
            this.save(profile);
        }
        return profile;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean recharge(String userId, BigDecimal amount, String description, String operatorId) {
        if (amount == null || amount.compareTo(BigDecimal.ZERO) <= 0) {
            return false;
        }

        AicgUserProfile profile = getByUserId(userId);
        if (profile == null) {
            return false;
        }

        BigDecimal balanceBefore = profile.getAccountBalance();
        BigDecimal balanceAfter = balanceBefore.add(amount);

        // 更新余额
        int result = baseMapper.addBalance(userId, amount, operatorId);
        if (result > 0) {
            // 记录交易
            AicgUserRecord record = new AicgUserRecord();
            record.setUserId(userId);
            record.setTransactionType(2); // 充值
            record.setAmount(amount);
            record.setBalanceBefore(balanceBefore);
            record.setBalanceAfter(balanceAfter);
            record.setDescription(description);
            record.setTransactionTime(new Date());
            record.setCreateBy(operatorId);
            record.setCreateTime(new Date());
            userRecordService.save(record);
            return true;
        }
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean consume(String userId, BigDecimal amount, String description, String operatorId) {
        if (amount == null || amount.compareTo(BigDecimal.ZERO) <= 0) {
            return false;
        }

        AicgUserProfile profile = getByUserId(userId);
        if (profile == null || profile.getAccountBalance().compareTo(amount) < 0) {
            return false;
        }

        BigDecimal balanceBefore = profile.getAccountBalance();
        BigDecimal balanceAfter = balanceBefore.subtract(amount);

        // 扣减余额
        int result = baseMapper.deductBalance(userId, amount, operatorId);
        if (result > 0) {
            // 🔥 如果是插件消费，提取插件信息
            String pluginId = null;
            String pluginKey = null;
            String pluginName = null;

            if (description != null && description.contains("调用插件:")) {
                // 从描述中提取插件名称，然后查询插件信息
                String extractedPluginName = description.replace("调用插件: ", "").trim();
                try {
                    AigcPlubShop plugin = plubShopService.getByPluginName(extractedPluginName);
                    if (plugin != null) {
                        pluginId = plugin.getId();
                        pluginKey = plugin.getPluginKey();
                        pluginName = plugin.getPlubname();
                    }
                } catch (Exception e) {
                    // 查询插件信息失败不影响主流程
                    log.warn("查询插件信息失败 - pluginName: {}, 错误: {}", extractedPluginName, e.getMessage());
                }
            }

            // 🔥 记录交易到 aicg_user_transaction 表（订单记录）
            try {
                String transactionId = UUID.randomUUID().toString().replace("-", "");
                String orderType = "other";
                int orderStatus = 3; // 已完成

                if (description != null && description.contains("调用插件:")) {
                    orderType = "plugin"; // 插件类型
                } else if (description != null && description.contains("购买智能体:")) {
                    orderType = "agent"; // 智能体购买类型
                }

                // 🔧 生成订单号
                String orderPrefix = "plugin".equals(orderType) ? "PLG" : "TXN";
                String dateStr = new SimpleDateFormat("yyyyMMdd").format(new Date());
                String orderSuffix = transactionId.substring(transactionId.length() - 8).toUpperCase();
                String relatedOrderId = orderPrefix + dateStr + "_" + orderSuffix;

                String insertSql = "INSERT INTO aicg_user_transaction (" +
                        "id, user_id, transaction_type, amount, balance_before, balance_after, " +
                        "description, related_order_id, transaction_time, create_by, create_time, " +
                        "order_status, order_type, plugin_id, plugin_key, plugin_name" +
                        ") VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

                jdbcTemplate.update(insertSql,
                        transactionId, userId, 1, amount, balanceBefore, balanceAfter,
                        description, relatedOrderId, new Date(), operatorId, new Date(),
                        orderStatus, orderType, pluginId, pluginKey, pluginName);

                log.info("交易记录已保存到订单表 - 用户: {}, 金额: {}, 类型: {}", userId, amount, orderType);
            } catch (Exception e) {
                log.error("保存交易记录到订单表失败", e);
            }

            // 🔧 修复：删除重复的插入逻辑
            // 原来这里会重复插入到 aicg_user_transaction 表，导致生成两条记录
            // 现在只保留上面的直接SQL插入，确保只生成一条正确的记录
            log.info("交易记录插入完成 - 用户: {}, 金额: {}", userId, amount);
            return true;
        }
        return false;
    }

    @Override
    public String regenerateApiKey(String userId, String operatorId) {
        String newApiKey = generateApiKey();
        int result = baseMapper.updateApiKey(userId, newApiKey, operatorId);
        return result > 0 ? newApiKey : null;
    }

    @Override
    public boolean updateNickname(String userId, String nickname, String operatorId) {
        // 🛡️ 安全检查：昵称敏感词校验
        if (nickname != null && !nickname.trim().isEmpty()) {
            try {
                // 注入敏感词服务进行校验
                ISensitiveWordService sensitiveWordService = SpringContextUtils.getBean(ISensitiveWordService.class);
                if (sensitiveWordService != null) {
                    ISensitiveWordService.NicknameValidationResult validationResult = sensitiveWordService
                            .validateNickname(nickname, userId);

                    if (!validationResult.isValid()) {
                        log.warn("🚨 昵称校验失败 - 用户ID: {}, 昵称: {}, 原因: {}",
                                userId, nickname, validationResult.getMessage());
                        return false; // 校验失败，不允许更新
                    }

                    log.info("✅ 昵称校验通过 - 用户ID: {}, 昵称: {}", userId, nickname);
                }
            } catch (Exception e) {
                log.error("昵称敏感词校验异常 - 用户ID: {}, 昵称: {}", userId, nickname, e);
                return false; // 校验异常，为了安全起见不允许更新
            }
        }

        AicgUserProfile profile = getByUserId(userId);
        if (profile != null) {
            profile.setNickname(nickname);
            profile.setUpdateBy(operatorId);
            profile.setUpdateTime(new Date());

            // 🔧 修复：同步更新sys_user表的昵称和真实姓名，确保数据一致性
            try {
                ISysUserService sysUserService = SpringContextUtils.getBean("sysUserServiceImpl",
                        ISysUserService.class);
                if (sysUserService != null) {
                    SysUser sysUser = sysUserService.getById(userId);
                    if (sysUser != null) {
                        sysUser.setNickname(nickname);
                        sysUser.setRealname(nickname); // 同时更新真实姓名，保持一致性
                        sysUser.setUpdateTime(new Date());
                        sysUserService.updateById(sysUser);
                        log.info("🔄 updateNickname: 已同步更新sys_user表昵称和真实姓名，用户ID: {}, 新昵称: {}", userId, nickname);
                    }
                }
            } catch (Exception e) {
                log.error("同步更新sys_user表昵称和真实姓名失败 - 用户ID: {}, 昵称: {}", userId, nickname, e);
                // 不影响主流程，继续更新扩展表
            }

            return this.updateById(profile);
        }
        return false;
    }

    @Override
    public BigDecimal getUserBalance(String userId) {
        AicgUserProfile profile = getByUserId(userId);
        return profile != null ? profile.getAccountBalance() : BigDecimal.ZERO;
    }

    @Override
    public boolean checkBalance(String userId, BigDecimal amount) {
        BigDecimal balance = getUserBalance(userId);
        return balance.compareTo(amount) >= 0;
    }

    @Override
    public AicgUserProfile getByInviteCode(String inviteCode) {
        return baseMapper.getByInviteCode(inviteCode);
    }

    /**
     * 生成API密钥
     * 
     * @return API密钥
     */
    private String generateApiKey() {
        return "ak_" + UUID.randomUUID().toString().replace("-", "");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean preDeductBalance(String userId, BigDecimal amount) {
        try {
            log.info("🔥 开始插件预扣费 - 用户ID: {}, 金额: {}", userId, amount);

            // 🔥 修改：使用frozen_plugin_balance字段替代frozen_balance
            // 原子操作：检查可用余额并冻结到插件专用冻结字段
            String sql = "UPDATE aicg_user_profile SET " +
                    "account_balance = account_balance - ?, " +
                    "frozen_plugin_balance = frozen_plugin_balance + ? " +
                    "WHERE user_id = ? AND account_balance >= ?";

            int result = jdbcTemplate.update(sql, amount, amount, userId, amount);

            if (result > 0) {
                log.info("🔥 插件预扣费成功 - 用户ID: {}, 从账户余额扣除: {}, 冻结到插件余额: {}",
                        userId, amount, amount);
                return true;
            } else {
                log.warn("🔥 插件预扣费失败，账户余额不足 - 用户ID: {}, 需要金额: {}", userId, amount);
                return false;
            }

        } catch (Exception e) {
            log.error("🔥 插件预扣费异常 - 用户ID: {}, 金额: {}, 错误: {}", userId, amount, e.getMessage(), e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean confirmDeductBalance(String userId, BigDecimal amount) {
        try {
            log.info("🔥 开始插件确认扣费 - 用户ID: {}, 金额: {}", userId, amount);

            // 🔥 修改：使用frozen_plugin_balance字段替代frozen_balance
            // 原子操作：从插件冻结余额中扣除，同时更新总消费统计
            String sql = "UPDATE aicg_user_profile SET " +
                    "frozen_plugin_balance = frozen_plugin_balance - ?, " +
                    "total_consumption = IFNULL(total_consumption, 0) + ? " +
                    "WHERE user_id = ? AND frozen_plugin_balance >= ?";

            int result = jdbcTemplate.update(sql, amount, amount, userId, amount);

            if (result > 0) {
                log.info("🔥 插件确认扣费成功 - 用户ID: {}, 从插件冻结余额扣除: {}, 已更新总消费统计",
                        userId, amount);
                return true;
            } else {
                log.error("🔥 插件确认扣费失败，插件冻结余额不足 - 用户ID: {}, 需要金额: {}", userId, amount);
                return false;
            }

        } catch (Exception e) {
            log.error("🔥 插件确认扣费异常 - 用户ID: {}, 金额: {}, 错误: {}", userId, amount, e.getMessage(), e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean refundFrozenBalance(String userId, BigDecimal amount) {
        try {
            log.info("🔥 开始插件退还冻结余额 - 用户ID: {}, 金额: {}", userId, amount);

            // 🔥 修改：使用frozen_plugin_balance字段替代frozen_balance
            // 原子操作：从插件冻结余额中减少，同时返回到账户余额
            String sql = "UPDATE aicg_user_profile SET " +
                    "frozen_plugin_balance = frozen_plugin_balance - ?, " +
                    "account_balance = account_balance + ? " +
                    "WHERE user_id = ? AND frozen_plugin_balance >= ?";

            int result = jdbcTemplate.update(sql, amount, amount, userId, amount);

            if (result > 0) {
                log.info("🔥 插件退还冻结余额成功 - 用户ID: {}, 从插件冻结余额减少: {}, 返回到账户余额: {}",
                        userId, amount, amount);
                return true;
            } else {
                log.error("🔥 插件退还冻结余额失败，插件冻结余额不足 - 用户ID: {}, 需要金额: {}", userId, amount);
                return false;
            }

        } catch (Exception e) {
            log.error("🔥 插件退还冻结余额异常 - 用户ID: {}, 金额: {}, 错误: {}", userId, amount, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public BigDecimal getAvailableBalance(String userId) {
        try {
            // 🔥 修改：可用余额 = 账户余额 - 插件冻结余额
            // 注意：提现冻结余额不影响插件可用余额，因为提现是从其他收益字段扣除的
            String sql = "SELECT (account_balance - IFNULL(frozen_plugin_balance, 0)) AS available_balance " +
                    "FROM aicg_user_profile WHERE user_id = ?";

            BigDecimal availableBalance = jdbcTemplate.queryForObject(sql, BigDecimal.class, userId);

            log.debug("🔥 查询插件可用余额 - 用户ID: {}, 可用余额: {} (账户余额 - 插件冻结余额)",
                    userId, availableBalance);
            return availableBalance != null ? availableBalance : BigDecimal.ZERO;

        } catch (Exception e) {
            log.error("🔥 查询插件可用余额异常 - 用户ID: {}, 错误: {}", userId, e.getMessage(), e);
            return BigDecimal.ZERO;
        }
    }
}
