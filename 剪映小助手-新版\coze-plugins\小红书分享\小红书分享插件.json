{"openapi": "3.0.0", "info": {"title": "智界AIGC小红书一键分享", "description": "📱 一键分享内容到小红书平台！支持图文笔记分享\n\n✨ 核心功能：\n• 图文笔记分享（必须上传1-9张图片）\n• 智能内容生成（标题和正文可自动生成）\n• 图片自动转存和优化\n• PC端二维码分享\n• 移动端直接发布\n• 防重复分享机制\n\n📝 必填参数：\n• API密钥（在智界AIGC平台获取）\n• 插件标识（xiaohongshufabu）\n• 图片文件（1-9张，jpg/png/gif格式）\n\n💰 使用说明：\n• 按次计费，具体价格以平台显示为准\n• 每个内容只能分享一次\n\n🔗 获取API密钥：访问 https://www.aigcview.com 注册账号", "version": "1.0.0", "contact": {"name": "智界AIGC", "email": "<EMAIL>"}}, "servers": [{"url": "https://www.aigcview.com/jeecg-boot/api/aigc/coze/xiaohongshu", "description": "智界AIGC小红书分享服务"}], "paths": {"/generate-share-page": {"post": {"summary": "分享内容到小红书", "description": "📱 一键分享您的内容到小红书平台！\n\n📝 使用说明：\n1. 【必填】输入您的API密钥（在智界AIGC平台获取：www.aigcview.com）\n2. 【必填】上传图片文件（1-9张，支持jpg、png、gif格式）\n3. 【可选】填写标题和内容文字\n4. 一键分享到小红书\n\n✨ 功能特色：\n- 智能内容生成\n- 支持图文分享\n- 创建精美的分享页面\n- 支持PC二维码和移动端直接分享\n- 防重复分享机制\n- 按次计费，具体价格以平台显示为准\n\n💡 提示：图片是必填参数，标题和内容可选", "operationId": "shareToXiaohongshu", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/XiaohongshuRequest"}}}}, "responses": {"200": {"description": "生成成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/XiaohongshuResponse"}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "401": {"description": "API密钥无效或余额不足", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}}, "components": {"schemas": {"XiaohongshuRequest": {"type": "object", "required": ["<PERSON><PERSON><PERSON><PERSON>", "pluginKey", "images"], "properties": {"apiKey": {"type": "string", "description": "用户API密钥，在智界AIGC平台获取", "example": ""}, "pluginKey": {"type": "string", "description": "插件唯一标识，用于后端识别和计费", "example": "xiaohongshufabu", "enum": ["xiaohongshufabu"]}, "shareType": {"type": "string", "enum": ["图文笔记", "视频笔记"], "description": "分享类型：图文笔记支持1-9张图片", "example": "图文笔记", "default": "图文笔记"}, "title": {"type": "string", "description": "笔记标题", "example": "今天的护肤心得分享✨", "maxLength": 100}, "content": {"type": "string", "description": "笔记正文内容", "example": "姐妹们！今天要分享一个超好用的护肤方法...", "maxLength": 5000}, "images": {"type": "array", "items": {"type": "string", "format": "binary"}, "description": "图片文件列表（必填，支持jpg、png格式）", "minItems": 1}}}, "XiaohongshuResponse": {"type": "object", "properties": {"success": {"type": "boolean", "description": "请求是否成功", "example": true}, "message": {"type": "string", "description": "响应消息", "example": "小红书分享页面创建成功！您可以通过以下链接访问分享页面"}, "result": {"type": "object", "properties": {"content": {"type": "object", "description": "生成的内容信息", "properties": {"title": {"type": "string", "description": "生成的标题", "example": "今天的护肤心得分享✨"}, "content": {"type": "string", "description": "生成的正文内容", "example": "姐妹们！今天要分享一个超好用的护肤方法..."}, "images": {"type": "array", "items": {"type": "string"}, "description": "处理后的图片URL列表", "example": ["https://www.aigcview.com/jeecg-boot/sys/common/static/imgs/image1.jpg"]}}}, "pageUrl": {"type": "string", "description": "分享页面URL", "example": "https://www.aigcview.com/jeecg-boot/api/aigc/xiaohongshu/share-page/1751030051166"}, "qrCodeUrl": {"type": "string", "description": "二维码图片链接", "example": "https://www.aigcview.com/jeecg-boot/sys/common/static/qrcodes/qr_1751030051166.png"}, "pageId": {"type": "string", "description": "页面唯一标识", "example": "1751030051166"}, "expireTime": {"type": "string", "description": "页面过期时间（ISO格式）", "example": "2025-06-28T21:14:11"}, "usage": {"type": "object", "description": "使用情况统计", "properties": {"cost": {"type": "number", "description": "本次调用费用", "example": 20.0}, "remainingBalance": {"type": "number", "description": "剩余余额", "example": 1547.88}, "pluginName": {"type": "string", "description": "插件名称", "example": "小红书发布"}}}, "tips": {"type": "string", "description": "使用提示", "example": "PC端点击分享按钮会显示二维码，请用手机微信扫码后分享。移动端可直接分享。每个内容只能分享一次。"}}}, "timestamp": {"type": "integer", "description": "响应时间戳", "example": 1735315451166}}}, "ErrorResponse": {"type": "object", "properties": {"success": {"type": "boolean", "description": "请求是否成功", "example": false}, "message": {"type": "string", "description": "错误消息", "example": "余额不足，请充值后使用。当前余额：¥5.50，本次使用需要：¥20.00"}, "code": {"type": "integer", "description": "错误状态码", "example": 500}, "timestamp": {"type": "integer", "description": "错误发生时间", "example": 1735315451166}}}}}}