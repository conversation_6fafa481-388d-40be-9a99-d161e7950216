<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html>
  <head>
    <title>Logback Log Messages</title>
<style  type="text/css">
table { margin-left: 2em; margin-right: 2em; border-left: 2px solid #AAA; }
TR.even { background: #FFFFFF; }
TR.odd { background: #EAEAEA; }
TR.warn TD.Level, TR.error TD.Level, TR.fatal TD.Level {font-weight: bold; color: #FF4040 }
TD { padding-right: 1ex; padding-left: 1ex; border-right: 2px solid #AAA; }
TD.Time, TD.Date { text-align: right; font-family: courier, monospace; font-size: smaller; }
TD.Thread { text-align: left; }
TD.Level { text-align: right; }
TD.Logger { text-align: left; }
TR.header { background: #596ED5; color: #FFF; font-weight: bold; font-size: larger; }
TD.Exception { background: #A2AEE8; font-family: courier, monospace;}
</style>

  </head>
<body>
<hr/>
<p>Log session start time Sat Jul 19 16:29:18 CST 2025</p><p></p>

<table cellspacing="0">
<tr class="header">
<td class="Level">Level</td>
<td class="Date">Date</td>
<td class="Message">Message</td>
<td class="MethodOfCaller">MethodOfCaller</td>
<td class="FileOfCaller">FileOfCaller</td>
<td class="LineOfCaller">LineOfCaller</td>
</tr>


<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:18,426</td>
<td class="Message">HV000001: Hibernate Validator 6.1.6.Final</td>
<td class="MethodOfCaller">&lt;clinit&gt;</td>
<td class="FileOfCaller">Version.java</td>
<td class="LineOfCaller">21</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:18,449</td>
<td class="Message">Starting JeecgSystemApplication on DESKTOP-G0NDD8J with PID 29828 (D:\AigcView_zj\AigcViewRd\jeecg-boot-module-system\target\classes started by Administrator in D:\AigcView_zj\AigcViewRd)</td>
<td class="MethodOfCaller">logStarting</td>
<td class="FileOfCaller">StartupInfoLogger.java</td>
<td class="LineOfCaller">55</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:18,449</td>
<td class="Message">The following profiles are active: dev</td>
<td class="MethodOfCaller">logStartupProfileInfo</td>
<td class="FileOfCaller">SpringApplication.java</td>
<td class="LineOfCaller">655</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 16:29:18,787</td>
<td class="Message">For Jackson Kotlin classes support please add &quot;com.fasterxml.jackson.module:jackson-module-kotlin&quot; to the classpath</td>
<td class="MethodOfCaller">warn</td>
<td class="FileOfCaller">CompositeLog.java</td>
<td class="LineOfCaller">127</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:19,924</td>
<td class="Message">Multiple Spring Data modules found, entering strict repository configuration mode!</td>
<td class="MethodOfCaller">multipleStoresDetected</td>
<td class="FileOfCaller">RepositoryConfigurationDelegate.java</td>
<td class="LineOfCaller">249</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:19,926</td>
<td class="Message">Bootstrapping Spring Data Redis repositories in DEFAULT mode.</td>
<td class="MethodOfCaller">registerRepositoriesIn</td>
<td class="FileOfCaller">RepositoryConfigurationDelegate.java</td>
<td class="LineOfCaller">127</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:20,048</td>
<td class="Message">Finished Spring Data repository scanning in 112ms. Found 0 Redis repository interfaces.</td>
<td class="MethodOfCaller">registerRepositoriesIn</td>
<td class="FileOfCaller">RepositoryConfigurationDelegate.java</td>
<td class="LineOfCaller">187</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:20,169</td>
<td class="Message"> ******************* init miniDao config [ begin ] *********************** </td>
<td class="MethodOfCaller">miniDaoBeanScannerConfigurer</td>
<td class="FileOfCaller">MinidaoAutoConfiguration.java</td>
<td class="LineOfCaller">23</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:20,169</td>
<td class="Message"> ------ minidao.base-package ------- org.jeecg.modules.jmreport.*</td>
<td class="MethodOfCaller">miniDaoBeanScannerConfigurer</td>
<td class="FileOfCaller">MinidaoAutoConfiguration.java</td>
<td class="LineOfCaller">25</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:20,170</td>
<td class="Message"> *******************  init miniDao config  [ end ] *********************** </td>
<td class="MethodOfCaller">miniDaoBeanScannerConfigurer</td>
<td class="FileOfCaller">MinidaoAutoConfiguration.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:20,246</td>
<td class="Message">register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDao }</td>
<td class="MethodOfCaller">doScan</td>
<td class="FileOfCaller">MiniDaoClassPathMapperScanner.java</td>
<td class="LineOfCaller">48</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:20,247</td>
<td class="Message">register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDataSourceDao }</td>
<td class="MethodOfCaller">doScan</td>
<td class="FileOfCaller">MiniDaoClassPathMapperScanner.java</td>
<td class="LineOfCaller">48</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:20,248</td>
<td class="Message">register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDbDao }</td>
<td class="MethodOfCaller">doScan</td>
<td class="FileOfCaller">MiniDaoClassPathMapperScanner.java</td>
<td class="LineOfCaller">48</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:20,248</td>
<td class="Message">register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDbFieldDao }</td>
<td class="MethodOfCaller">doScan</td>
<td class="FileOfCaller">MiniDaoClassPathMapperScanner.java</td>
<td class="LineOfCaller">48</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:20,248</td>
<td class="Message">register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDbParamDao }</td>
<td class="MethodOfCaller">doScan</td>
<td class="FileOfCaller">MiniDaoClassPathMapperScanner.java</td>
<td class="LineOfCaller">48</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:20,248</td>
<td class="Message">register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDictDao }</td>
<td class="MethodOfCaller">doScan</td>
<td class="FileOfCaller">MiniDaoClassPathMapperScanner.java</td>
<td class="LineOfCaller">48</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:20,248</td>
<td class="Message">register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDictItemDao }</td>
<td class="MethodOfCaller">doScan</td>
<td class="FileOfCaller">MiniDaoClassPathMapperScanner.java</td>
<td class="LineOfCaller">48</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:20,248</td>
<td class="Message">register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportLinkDao }</td>
<td class="MethodOfCaller">doScan</td>
<td class="FileOfCaller">MiniDaoClassPathMapperScanner.java</td>
<td class="LineOfCaller">48</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:20,248</td>
<td class="Message">register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportMapDao }</td>
<td class="MethodOfCaller">doScan</td>
<td class="FileOfCaller">MiniDaoClassPathMapperScanner.java</td>
<td class="LineOfCaller">48</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:20,248</td>
<td class="Message">register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportShareDao }</td>
<td class="MethodOfCaller">doScan</td>
<td class="FileOfCaller">MiniDaoClassPathMapperScanner.java</td>
<td class="LineOfCaller">48</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:20,426</td>
<td class="Message">Bean &#39;(inner bean)#7e7740a5&#39; of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:20,430</td>
<td class="Message">Bean &#39;jimuReportDao&#39; of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:20,431</td>
<td class="Message">Bean &#39;(inner bean)#7e7740a5#1&#39; of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:20,431</td>
<td class="Message">Bean &#39;jimuReportDataSourceDao&#39; of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:20,432</td>
<td class="Message">Bean &#39;(inner bean)#7e7740a5#2&#39; of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:20,433</td>
<td class="Message">Bean &#39;jimuReportDbDao&#39; of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:20,434</td>
<td class="Message">Bean &#39;(inner bean)#7e7740a5#3&#39; of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:20,435</td>
<td class="Message">Bean &#39;jimuReportDbFieldDao&#39; of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:20,436</td>
<td class="Message">Bean &#39;(inner bean)#7e7740a5#4&#39; of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:20,437</td>
<td class="Message">Bean &#39;jimuReportDbParamDao&#39; of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:20,438</td>
<td class="Message">Bean &#39;(inner bean)#7e7740a5#5&#39; of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:20,438</td>
<td class="Message">Bean &#39;jimuReportDictDao&#39; of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:20,439</td>
<td class="Message">Bean &#39;(inner bean)#7e7740a5#6&#39; of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:20,440</td>
<td class="Message">Bean &#39;jimuReportDictItemDao&#39; of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:20,441</td>
<td class="Message">Bean &#39;(inner bean)#7e7740a5#7&#39; of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:20,442</td>
<td class="Message">Bean &#39;jimuReportLinkDao&#39; of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:20,442</td>
<td class="Message">Bean &#39;(inner bean)#7e7740a5#8&#39; of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:20,443</td>
<td class="Message">Bean &#39;jimuReportMapDao&#39; of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:20,444</td>
<td class="Message">Bean &#39;(inner bean)#7e7740a5#9&#39; of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:20,445</td>
<td class="Message">Bean &#39;jimuReportShareDao&#39; of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:20,465</td>
<td class="Message">Bean &#39;spring.redis-org.springframework.boot.autoconfigure.data.redis.RedisProperties&#39; of type [org.springframework.boot.autoconfigure.data.redis.RedisProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:20,469</td>
<td class="Message">Bean &#39;org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration&#39; of type [org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:20,528</td>
<td class="Message">Bean &#39;lettuceClientResources&#39; of type [io.lettuce.core.resource.DefaultClientResources] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:20,592</td>
<td class="Message">Bean &#39;redisConnectionFactory&#39; of type [org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:20,595</td>
<td class="Message">Bean &#39;shiroConfig&#39; of type [org.jeecg.config.shiro.ShiroConfig$$EnhancerBySpringCGLIB$$9bdcfd2b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:20,630</td>
<td class="Message">Bean &#39;shiroRealm&#39; of type [org.jeecg.config.shiro.ShiroRealm] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:21,023</td>
<td class="Message">===============(1)创建缓存管理器RedisCacheManager</td>
<td class="MethodOfCaller">redisCacheManager</td>
<td class="FileOfCaller">ShiroConfig.java</td>
<td class="LineOfCaller">215</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:21,024</td>
<td class="Message">===============(2)创建RedisManager,连接Redis..</td>
<td class="MethodOfCaller">redisManager</td>
<td class="FileOfCaller">ShiroConfig.java</td>
<td class="LineOfCaller">233</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:21,027</td>
<td class="Message">Bean &#39;redisManager&#39; of type [org.crazycake.shiro.RedisManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:21,029</td>
<td class="Message">Bean &#39;securityManager&#39; of type [org.apache.shiro.web.mgt.DefaultWebSecurityManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:21,059</td>
<td class="Message">Bean &#39;authorizationAttributeSourceAdvisor&#39; of type [org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:21,200</td>
<td class="Message">Bean &#39;spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties&#39; of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:21,205</td>
<td class="Message">Bean &#39;com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration&#39; of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$a62f2603] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:21,212</td>
<td class="Message">Bean &#39;dsProcessor&#39; of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:21,221</td>
<td class="Message">Bean &#39;redisConfig&#39; of type [org.jeecg.common.modules.redis.config.RedisConfig$$EnhancerBySpringCGLIB$$b93f0d5b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:21,252</td>
<td class="Message">Bean &#39;org.apache.shiro.spring.boot.autoconfigure.ShiroBeanAutoConfiguration&#39; of type [org.apache.shiro.spring.boot.autoconfigure.ShiroBeanAutoConfiguration$$EnhancerBySpringCGLIB$$4fb992e2] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:21,255</td>
<td class="Message">Bean &#39;eventBus&#39; of type [org.apache.shiro.event.support.DefaultEventBus] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:21,486</td>
<td class="Message">Tomcat initialized with port(s): 8080 (http)</td>
<td class="MethodOfCaller">initialize</td>
<td class="FileOfCaller">TomcatWebServer.java</td>
<td class="LineOfCaller">108</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:21,494</td>
<td class="Message">Initializing ProtocolHandler [&quot;http-nio-8080&quot;]</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">DirectJDKLog.java</td>
<td class="LineOfCaller">173</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:21,494</td>
<td class="Message">Starting service [Tomcat]</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">DirectJDKLog.java</td>
<td class="LineOfCaller">173</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:21,494</td>
<td class="Message">Starting Servlet engine: [Apache Tomcat/9.0.39]</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">DirectJDKLog.java</td>
<td class="LineOfCaller">173</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:21,633</td>
<td class="Message">Initializing Spring embedded WebApplicationContext</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">DirectJDKLog.java</td>
<td class="LineOfCaller">173</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:21,633</td>
<td class="Message">Root WebApplicationContext: initialization completed in 3152 ms</td>
<td class="MethodOfCaller">prepareWebApplicationContext</td>
<td class="FileOfCaller">ServletWebServerApplicationContext.java</td>
<td class="LineOfCaller">285</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:22,713</td>
<td class="Message">{dataSource-1,master} inited</td>
<td class="MethodOfCaller">init</td>
<td class="FileOfCaller">DruidDataSource.java</td>
<td class="LineOfCaller">994</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:22,714</td>
<td class="Message">dynamic-datasource - load a datasource named [master] success</td>
<td class="MethodOfCaller">addDataSource</td>
<td class="FileOfCaller">DynamicRoutingDataSource.java</td>
<td class="LineOfCaller">132</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:22,715</td>
<td class="Message">dynamic-datasource initial loaded [1] datasource,primary datasource named [master]</td>
<td class="MethodOfCaller">afterPropertiesSet</td>
<td class="FileOfCaller">DynamicRoutingDataSource.java</td>
<td class="LineOfCaller">237</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:23,993</td>
<td class="Message"> --- redis config init --- </td>
<td class="MethodOfCaller">redisTemplate</td>
<td class="FileOfCaller">RedisConfig.java</td>
<td class="LineOfCaller">76</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:24,699</td>
<td class="Message">开始初始化TOS客户端...</td>
<td class="MethodOfCaller">initTosClient</td>
<td class="FileOfCaller">TosService.java</td>
<td class="LineOfCaller">86</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:24,700</td>
<td class="Message">TOS配置验证通过 - Region: cn-shanghai, Endpoint: tos-cn-shanghai.volces.com, Bucket: aigcview-tos</td>
<td class="MethodOfCaller">initTosClient</td>
<td class="FileOfCaller">TosService.java</td>
<td class="LineOfCaller">105</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:24,799</td>
<td class="Message">外网TOS客户端初始化成功！</td>
<td class="MethodOfCaller">initTosClient</td>
<td class="FileOfCaller">TosService.java</td>
<td class="LineOfCaller">115</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:24,802</td>
<td class="Message">内网TOS客户端初始化成功 - Endpoint: tos-cn-shanghai.ivolces.com</td>
<td class="MethodOfCaller">initTosClient</td>
<td class="FileOfCaller">TosService.java</td>
<td class="LineOfCaller">125</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:24,802</td>
<td class="Message">正在测试TOS连接...</td>
<td class="MethodOfCaller">testTosConnection</td>
<td class="FileOfCaller">TosService.java</td>
<td class="LineOfCaller">591</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:24,802</td>
<td class="Message">TOS连接测试成功！</td>
<td class="MethodOfCaller">testTosConnection</td>
<td class="FileOfCaller">TosService.java</td>
<td class="LineOfCaller">593</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:26,155</td>
<td class="Message">🔍 开始初始化敏感词库...</td>
<td class="MethodOfCaller">init</td>
<td class="FileOfCaller">SensitiveWordServiceImpl.java</td>
<td class="LineOfCaller">48</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:26,536</td>
<td class="Message">✅ 敏感词库初始化完成</td>
<td class="MethodOfCaller">init</td>
<td class="FileOfCaller">SensitiveWordServiceImpl.java</td>
<td class="LineOfCaller">64</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:27,121</td>
<td class="Message">初始化预定义特效映射完成，共 4 个特效</td>
<td class="MethodOfCaller">initDefaultEffectMapping</td>
<td class="FileOfCaller">JianyingEffectSearchService.java</td>
<td class="LineOfCaller">86</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:27,131</td>
<td class="Message">RestTemplate初始化完成，支持TOS文件下载</td>
<td class="MethodOfCaller">initRestTemplate</td>
<td class="FileOfCaller">CozeApiService.java</td>
<td class="LineOfCaller">64</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:27,205</td>
<td class="Message">开始初始化TOS客户端...</td>
<td class="MethodOfCaller">initTosClient</td>
<td class="FileOfCaller">JianyingProTosService.java</td>
<td class="LineOfCaller">86</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:27,205</td>
<td class="Message">TOS配置验证通过 - Region: cn-shanghai, Endpoint: tos-cn-shanghai.volces.com, Bucket: aigcview-tos</td>
<td class="MethodOfCaller">initTosClient</td>
<td class="FileOfCaller">JianyingProTosService.java</td>
<td class="LineOfCaller">105</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:27,207</td>
<td class="Message">外网TOS客户端初始化成功！</td>
<td class="MethodOfCaller">initTosClient</td>
<td class="FileOfCaller">JianyingProTosService.java</td>
<td class="LineOfCaller">115</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:27,212</td>
<td class="Message">内网TOS客户端初始化成功 - Endpoint: tos-cn-shanghai.ivolces.com</td>
<td class="MethodOfCaller">initTosClient</td>
<td class="FileOfCaller">JianyingProTosService.java</td>
<td class="LineOfCaller">125</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:27,212</td>
<td class="Message">正在测试TOS连接...</td>
<td class="MethodOfCaller">testTosConnection</td>
<td class="FileOfCaller">JianyingProTosService.java</td>
<td class="LineOfCaller">627</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:27,212</td>
<td class="Message">TOS连接测试成功！</td>
<td class="MethodOfCaller">testTosConnection</td>
<td class="FileOfCaller">JianyingProTosService.java</td>
<td class="LineOfCaller">629</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:27,230</td>
<td class="Message">超级剪映小助手 - 初始化预定义特效映射完成，共 4 个特效</td>
<td class="MethodOfCaller">initDefaultEffectMapping</td>
<td class="FileOfCaller">JianyingProEffectSearchService.java</td>
<td class="LineOfCaller">87</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:27,241</td>
<td class="Message">超级剪映小助手 - RestTemplate初始化完成，支持TOS文件下载</td>
<td class="MethodOfCaller">initRestTemplate</td>
<td class="FileOfCaller">JianyingProCozeApiService.java</td>
<td class="LineOfCaller">59</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:27,935</td>
<td class="Message">Using default implementation for ThreadExecutor</td>
<td class="MethodOfCaller">instantiate</td>
<td class="FileOfCaller">StdSchedulerFactory.java</td>
<td class="LineOfCaller">1220</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:27,938</td>
<td class="Message">Job execution threads will use class loader of thread: main</td>
<td class="MethodOfCaller">initialize</td>
<td class="FileOfCaller">SimpleThreadPool.java</td>
<td class="LineOfCaller">268</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:27,949</td>
<td class="Message">Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl</td>
<td class="MethodOfCaller">&lt;init&gt;</td>
<td class="FileOfCaller">SchedulerSignalerImpl.java</td>
<td class="LineOfCaller">61</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:27,949</td>
<td class="Message">Quartz Scheduler v.2.3.2 created.</td>
<td class="MethodOfCaller">&lt;init&gt;</td>
<td class="FileOfCaller">QuartzScheduler.java</td>
<td class="LineOfCaller">229</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:27,955</td>
<td class="Message">Using db table-based data access locking (synchronization).</td>
<td class="MethodOfCaller">initialize</td>
<td class="FileOfCaller">JobStoreSupport.java</td>
<td class="LineOfCaller">672</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:27,957</td>
<td class="Message">JobStoreCMT initialized.</td>
<td class="MethodOfCaller">initialize</td>
<td class="FileOfCaller">JobStoreCMT.java</td>
<td class="LineOfCaller">145</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:27,958</td>
<td class="Message">Scheduler meta-data: Quartz Scheduler (v2.3.2) &#39;MyScheduler&#39; with instanceId &#39;DESKTOP-G0NDD8J1752913767938&#39;
  Scheduler class: &#39;org.quartz.core.QuartzScheduler&#39; - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool &#39;org.quartz.simpl.SimpleThreadPool&#39; - with 10 threads.
  Using job-store &#39;org.springframework.scheduling.quartz.LocalDataSourceJobStore&#39; - which supports persistence. and is clustered.
</td>
<td class="MethodOfCaller">initialize</td>
<td class="FileOfCaller">QuartzScheduler.java</td>
<td class="LineOfCaller">294</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:27,958</td>
<td class="Message">Quartz scheduler &#39;MyScheduler&#39; initialized from an externally provided properties instance.</td>
<td class="MethodOfCaller">instantiate</td>
<td class="FileOfCaller">StdSchedulerFactory.java</td>
<td class="LineOfCaller">1374</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:27,958</td>
<td class="Message">Quartz scheduler version: 2.3.2</td>
<td class="MethodOfCaller">instantiate</td>
<td class="FileOfCaller">StdSchedulerFactory.java</td>
<td class="LineOfCaller">1378</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:27,959</td>
<td class="Message">JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@65880400</td>
<td class="MethodOfCaller">setJobFactory</td>
<td class="FileOfCaller">QuartzScheduler.java</td>
<td class="LineOfCaller">2293</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:31,200</td>
<td class="Message"> --- Init JimuReport Config --- </td>
<td class="MethodOfCaller">afterPropertiesSet</td>
<td class="FileOfCaller">JimuReportConfiguration.java</td>
<td class="LineOfCaller">55</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:32,405</td>
<td class="Message">Exposing 2 endpoint(s) beneath base path &#39;/actuator&#39;</td>
<td class="MethodOfCaller">&lt;init&gt;</td>
<td class="FileOfCaller">EndpointLinksResolver.java</td>
<td class="LineOfCaller">58</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:32,542</td>
<td class="Message"> 代码生成器数据库连接，使用application.yml的DB配置 ###################</td>
<td class="MethodOfCaller">initCodeGenerateDbConfig</td>
<td class="FileOfCaller">CodeGenerateDbConfig.java</td>
<td class="LineOfCaller">46</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:32,559</td>
<td class="Message">Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2ControllerWebMvc#getDocumentation(String, HttpServletRequest)]</td>
<td class="MethodOfCaller">initHandlerMethods</td>
<td class="FileOfCaller">WebMvcPropertySourcedRequestMappingHandlerMapping.java</td>
<td class="LineOfCaller">69</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:32,610</td>
<td class="Message">---创建线程池---</td>
<td class="MethodOfCaller">asyncServiceExecutor</td>
<td class="FileOfCaller">JmReportExecutorConfig.java</td>
<td class="LineOfCaller">21</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:32,611</td>
<td class="Message">Initializing ExecutorService</td>
<td class="MethodOfCaller">initialize</td>
<td class="FileOfCaller">ExecutorConfigurationSupport.java</td>
<td class="LineOfCaller">181</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:32,612</td>
<td class="Message">Initializing ExecutorService &#39;jmReportTaskExecutor&#39;</td>
<td class="MethodOfCaller">initialize</td>
<td class="FileOfCaller">ExecutorConfigurationSupport.java</td>
<td class="LineOfCaller">181</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,214</td>
<td class="Message">Starting ProtocolHandler [&quot;http-nio-8080&quot;]</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">DirectJDKLog.java</td>
<td class="LineOfCaller">173</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,234</td>
<td class="Message">Tomcat started on port(s): 8080 (http) with context path &#39;/jeecg-boot&#39;</td>
<td class="MethodOfCaller">start</td>
<td class="FileOfCaller">TomcatWebServer.java</td>
<td class="LineOfCaller">220</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,235</td>
<td class="Message">Documentation plugins bootstrapped</td>
<td class="MethodOfCaller">start</td>
<td class="FileOfCaller">DocumentationPluginsBootstrapper.java</td>
<td class="LineOfCaller">93</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,238</td>
<td class="Message">Found 1 custom documentation plugin(s)</td>
<td class="MethodOfCaller">bootstrapDocumentationPlugins</td>
<td class="FileOfCaller">AbstractDocumentationPluginsBootstrapper.java</td>
<td class="LineOfCaller">79</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,351</td>
<td class="Message">Scanning for api listing references</td>
<td class="MethodOfCaller">scan</td>
<td class="FileOfCaller">ApiListingReferenceScanner.java</td>
<td class="LineOfCaller">44</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,464</td>
<td class="Message">Initializing Spring DispatcherServlet &#39;dispatcherServlet&#39;</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">DirectJDKLog.java</td>
<td class="LineOfCaller">173</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,464</td>
<td class="Message">Initializing Servlet &#39;dispatcherServlet&#39;</td>
<td class="MethodOfCaller">initServletBean</td>
<td class="FileOfCaller">FrameworkServlet.java</td>
<td class="LineOfCaller">525</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,480</td>
<td class="Message">Completed initialization in 16 ms</td>
<td class="MethodOfCaller">initServletBean</td>
<td class="FileOfCaller">FrameworkServlet.java</td>
<td class="LineOfCaller">547</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,528</td>
<td class="Message">Generating unique operation named: addUsingPOST_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,537</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,540</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,550</td>
<td class="Message">Generating unique operation named: addUsingPOST_2</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,552</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,554</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,555</td>
<td class="Message">Generating unique operation named: editUsingPUT_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,557</td>
<td class="Message">Generating unique operation named: getByUserIdUsingGET_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,559</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_2</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,563</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_2</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,571</td>
<td class="Message">Generating unique operation named: addUsingPOST_3</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,574</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_2</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,575</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_2</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,577</td>
<td class="Message">Generating unique operation named: editUsingPUT_2</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,579</td>
<td class="Message">Generating unique operation named: getByUserIdUsingGET_2</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,584</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_3</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,588</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_3</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,597</td>
<td class="Message">Generating unique operation named: addUsingPOST_4</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,599</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_3</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,600</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_3</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,601</td>
<td class="Message">Generating unique operation named: editUsingPUT_3</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,603</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_4</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,607</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_4</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,615</td>
<td class="Message">Generating unique operation named: addUsingPOST_5</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,616</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_4</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,617</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_4</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,617</td>
<td class="Message">Generating unique operation named: editUsingPUT_4</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,618</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_5</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,621</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_5</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,627</td>
<td class="Message">Generating unique operation named: addUsingPOST_6</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,629</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_5</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,630</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_5</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,631</td>
<td class="Message">Generating unique operation named: editUsingPUT_5</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,635</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_6</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,637</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_6</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,643</td>
<td class="Message">Generating unique operation named: addUsingPOST_7</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,645</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_6</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,646</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_6</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,647</td>
<td class="Message">Generating unique operation named: editUsingPUT_6</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,648</td>
<td class="Message">Generating unique operation named: getByReferrerIdUsingGET_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,650</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_7</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,653</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_7</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,660</td>
<td class="Message">Generating unique operation named: getUsageStatsUsingGET_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,668</td>
<td class="Message">Generating unique operation named: addUsingPOST_8</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,669</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_7</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,671</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_7</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,672</td>
<td class="Message">Generating unique operation named: editUsingPUT_7</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,673</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_8</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,677</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_8</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,683</td>
<td class="Message">Generating unique operation named: addUsingPOST_9</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,685</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_8</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,686</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_8</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,687</td>
<td class="Message">Generating unique operation named: editUsingPUT_8</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,688</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_9</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,692</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_9</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,701</td>
<td class="Message">Generating unique operation named: addUsingPOST_10</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,703</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_9</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,704</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_9</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,705</td>
<td class="Message">Generating unique operation named: editUsingPUT_9</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,708</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_10</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,711</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_10</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,722</td>
<td class="Message">Generating unique operation named: addUsingPOST_11</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,727</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_10</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,728</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_10</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,729</td>
<td class="Message">Generating unique operation named: editUsingPUT_10</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,733</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_11</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,736</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_11</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,741</td>
<td class="Message">Generating unique operation named: addUsingPOST_12</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,742</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_11</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,743</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_11</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,744</td>
<td class="Message">Generating unique operation named: editUsingPUT_11</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,745</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_12</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,747</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_12</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,752</td>
<td class="Message">Generating unique operation named: addUsingPOST_13</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,754</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_12</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,755</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_12</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,757</td>
<td class="Message">Generating unique operation named: editUsingPUT_12</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,758</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_13</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,763</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_13</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,767</td>
<td class="Message">Generating unique operation named: addUsingPOST_14</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,768</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_13</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,770</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_13</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,771</td>
<td class="Message">Generating unique operation named: editUsingPUT_13</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,772</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_14</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,775</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_14</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,779</td>
<td class="Message">Generating unique operation named: addUsingPOST_15</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,780</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_14</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,781</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_14</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,782</td>
<td class="Message">Generating unique operation named: editUsingPUT_14</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,782</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_15</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,785</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_15</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,852</td>
<td class="Message">Generating unique operation named: addAudiosUsingPOST_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,855</td>
<td class="Message">Generating unique operation named: addCaptionsUsingPOST_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,856</td>
<td class="Message">Generating unique operation named: addEffectsUsingPOST_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,858</td>
<td class="Message">Generating unique operation named: addImagesUsingPOST_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,859</td>
<td class="Message">Generating unique operation named: addKeyframesUsingPOST_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,866</td>
<td class="Message">Generating unique operation named: addVideosUsingPOST_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,909</td>
<td class="Message">Generating unique operation named: addUsingPOST_16</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,911</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_15</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,912</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_15</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,913</td>
<td class="Message">Generating unique operation named: editUsingPUT_15</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,914</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_16</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,917</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_16</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,921</td>
<td class="Message">Generating unique operation named: addUsingPOST_17</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,922</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_16</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,923</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_16</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,924</td>
<td class="Message">Generating unique operation named: editUsingPUT_16</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,925</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_17</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,928</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_17</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,931</td>
<td class="Message">Generating unique operation named: addUsingPOST_18</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,932</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_17</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,933</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_17</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,934</td>
<td class="Message">Generating unique operation named: editUsingPUT_17</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,935</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_18</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,938</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_18</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,943</td>
<td class="Message">Generating unique operation named: addUsingPOST_19</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,945</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_18</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,946</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_18</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,948</td>
<td class="Message">Generating unique operation named: editUsingPUT_18</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,949</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_19</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,952</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_19</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,957</td>
<td class="Message">Generating unique operation named: addUsingPOST_20</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,958</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_19</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,959</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_19</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,960</td>
<td class="Message">Generating unique operation named: editUsingPUT_19</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,960</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_20</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,963</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_20</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,970</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_21</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,972</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_20</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,976</td>
<td class="Message">Generating unique operation named: addUsingPOST_21</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,977</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_20</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,979</td>
<td class="Message">Generating unique operation named: editUsingPUT_20</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,982</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_21</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,990</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_22</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,993</td>
<td class="Message">Generating unique operation named: addUsingPOST_22</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,994</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_21</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,997</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_21</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:33,999</td>
<td class="Message">Generating unique operation named: editUsingPUT_21</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:34,009</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_22</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:34,028</td>
<td class="Message">Generating unique operation named: getReferralStatsUsingGET_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:34,042</td>
<td class="Message">Generating unique operation named: sendEmailCodeUsingPOST_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:34,043</td>
<td class="Message">Generating unique operation named: sendSmsCodeUsingPOST_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:34,044</td>
<td class="Message">Generating unique operation named: verifyCodeUsingPOST_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:34,396</td>
<td class="Message">Started JeecgSystemApplication in 16.334 seconds (JVM running for 17.35)</td>
<td class="MethodOfCaller">logStarted</td>
<td class="FileOfCaller">StartupInfoLogger.java</td>
<td class="LineOfCaller">61</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:34,403</td>
<td class="Message">
----------------------------------------------------------
	Application Jeecg-Boot is running! Access URLs:
	Local: 		http://localhost:8080/jeecg-boot/
	External: 	http://**********:8080/jeecg-boot/
	Swagger文档: 	http://**********:8080/jeecg-boot/doc.html
----------------------------------------------------------</td>
<td class="MethodOfCaller">main</td>
<td class="FileOfCaller">JeecgSystemApplication.java</td>
<td class="LineOfCaller">39</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 16:29:35,265</td>
<td class="Message"> LogContent length : 14</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">79</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 16:29:35,265</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">80</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:35,265</td>
<td class="Message"> 用户名:  管理员,退出成功！ </td>
<td class="MethodOfCaller">logout</td>
<td class="FileOfCaller">LoginController.java</td>
<td class="LineOfCaller">216</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:35,266</td>
<td class="Message">🧹 开始清理用户登录缓存，用户：admin</td>
<td class="MethodOfCaller">cleanupUserLoginCache</td>
<td class="FileOfCaller">UserCacheCleanupService.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:29:35,268</td>
<td class="Message">✅ 用户 admin 登录缓存清理完成</td>
<td class="MethodOfCaller">cleanupUserLoginCache</td>
<td class="FileOfCaller">UserCacheCleanupService.java</td>
<td class="LineOfCaller">49</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:49:31,391</td>
<td class="Message">--查询规则--&gt;status = 1</td>
<td class="MethodOfCaller">addEasyQuery</td>
<td class="FileOfCaller">QueryGenerator.java</td>
<td class="LineOfCaller">535</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:49:31,391</td>
<td class="Message">--查询规则--&gt;status = 1</td>
<td class="MethodOfCaller">addEasyQuery</td>
<td class="FileOfCaller">QueryGenerator.java</td>
<td class="LineOfCaller">535</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:49:31,391</td>
<td class="Message">--查询规则--&gt;status = 1</td>
<td class="MethodOfCaller">addEasyQuery</td>
<td class="FileOfCaller">QueryGenerator.java</td>
<td class="LineOfCaller">535</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:49:31,394</td>
<td class="Message">排序规则&gt;&gt;列:null,排序方式:null</td>
<td class="MethodOfCaller">doMultiFieldsOrder</td>
<td class="FileOfCaller">QueryGenerator.java</td>
<td class="LineOfCaller">227</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:49:31,394</td>
<td class="Message">排序规则&gt;&gt;列:null,排序方式:null</td>
<td class="MethodOfCaller">doMultiFieldsOrder</td>
<td class="FileOfCaller">QueryGenerator.java</td>
<td class="LineOfCaller">227</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 16:49:31,394</td>
<td class="Message">排序规则&gt;&gt;列:null,排序方式:null</td>
<td class="MethodOfCaller">doMultiFieldsOrder</td>
<td class="FileOfCaller">QueryGenerator.java</td>
<td class="LineOfCaller">227</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 16:49:31,467</td>
<td class="Message"> LogContent length : 13</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 16:49:31,467</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 16:49:31,479</td>
<td class="Message"> LogContent length : 13</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 16:49:31,480</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 16:49:31,491</td>
<td class="Message"> LogContent length : 12</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 16:49:31,491</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 18:13:59,807</td>
<td class="Message">--查询规则--&gt;status = 1</td>
<td class="MethodOfCaller">addEasyQuery</td>
<td class="FileOfCaller">QueryGenerator.java</td>
<td class="LineOfCaller">535</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 18:13:59,806</td>
<td class="Message">--查询规则--&gt;status = 1</td>
<td class="MethodOfCaller">addEasyQuery</td>
<td class="FileOfCaller">QueryGenerator.java</td>
<td class="LineOfCaller">535</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 18:13:59,806</td>
<td class="Message">--查询规则--&gt;status = 1</td>
<td class="MethodOfCaller">addEasyQuery</td>
<td class="FileOfCaller">QueryGenerator.java</td>
<td class="LineOfCaller">535</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 18:13:59,808</td>
<td class="Message">排序规则&gt;&gt;列:null,排序方式:null</td>
<td class="MethodOfCaller">doMultiFieldsOrder</td>
<td class="FileOfCaller">QueryGenerator.java</td>
<td class="LineOfCaller">227</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 18:13:59,808</td>
<td class="Message">排序规则&gt;&gt;列:null,排序方式:null</td>
<td class="MethodOfCaller">doMultiFieldsOrder</td>
<td class="FileOfCaller">QueryGenerator.java</td>
<td class="LineOfCaller">227</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 18:13:59,808</td>
<td class="Message">排序规则&gt;&gt;列:null,排序方式:null</td>
<td class="MethodOfCaller">doMultiFieldsOrder</td>
<td class="FileOfCaller">QueryGenerator.java</td>
<td class="LineOfCaller">227</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 18:13:59,838</td>
<td class="Message"> LogContent length : 12</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 18:13:59,839</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 18:13:59,839</td>
<td class="Message"> LogContent length : 13</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 18:13:59,839</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 18:13:59,839</td>
<td class="Message"> LogContent length : 13</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 18:13:59,839</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 18:14:18,967</td>
<td class="Message">request URI = /jeecg-boot/sys/dict/getDictItems/plugin_category</td>
<td class="MethodOfCaller">preHandle</td>
<td class="FileOfCaller">SignAuthInterceptor.java</td>
<td class="LineOfCaller">35</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 18:14:18,971</td>
<td class="Message">--查询规则--&gt;status = 1</td>
<td class="MethodOfCaller">addEasyQuery</td>
<td class="FileOfCaller">QueryGenerator.java</td>
<td class="LineOfCaller">535</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 18:14:18,971</td>
<td class="Message">排序规则&gt;&gt;列:null,排序方式:null</td>
<td class="MethodOfCaller">doMultiFieldsOrder</td>
<td class="FileOfCaller">QueryGenerator.java</td>
<td class="LineOfCaller">227</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 18:14:18,995</td>
<td class="Message">Param paramsJsonStr : {}</td>
<td class="MethodOfCaller">getParamsSign</td>
<td class="FileOfCaller">SignUtil.java</td>
<td class="LineOfCaller">48</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 18:14:18,998</td>
<td class="Message">Param Sign : E19D6243CB1945AB4F7202A1B00F77D5</td>
<td class="MethodOfCaller">verifySign</td>
<td class="FileOfCaller">SignUtil.java</td>
<td class="LineOfCaller">35</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 18:14:19,002</td>
<td class="Message"> dictCode : plugin_category</td>
<td class="MethodOfCaller">getDictItems</td>
<td class="FileOfCaller">SysDictController.java</td>
<td class="LineOfCaller">157</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 18:14:19,104</td>
<td class="Message"> LogContent length : 11</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 18:14:19,105</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 18:14:21,870</td>
<td class="Message">request URI = /jeecg-boot/sys/dict/getDictItems/plugin_category</td>
<td class="MethodOfCaller">preHandle</td>
<td class="FileOfCaller">SignAuthInterceptor.java</td>
<td class="LineOfCaller">35</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 18:14:21,871</td>
<td class="Message">Param paramsJsonStr : {}</td>
<td class="MethodOfCaller">getParamsSign</td>
<td class="FileOfCaller">SignUtil.java</td>
<td class="LineOfCaller">48</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 18:14:21,871</td>
<td class="Message">Param Sign : E19D6243CB1945AB4F7202A1B00F77D5</td>
<td class="MethodOfCaller">verifySign</td>
<td class="FileOfCaller">SignUtil.java</td>
<td class="LineOfCaller">35</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 18:14:21,871</td>
<td class="Message"> dictCode : plugin_category</td>
<td class="MethodOfCaller">getDictItems</td>
<td class="FileOfCaller">SysDictController.java</td>
<td class="LineOfCaller">157</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 18:14:21,900</td>
<td class="Message">获取随机推荐插件成功，数量: 6</td>
<td class="MethodOfCaller">getPluginDetail</td>
<td class="FileOfCaller">AigcPlubShopController.java</td>
<td class="LineOfCaller">433</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 18:14:21,936</td>
<td class="Message"> LogContent length : 11</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 18:14:21,936</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 18:14:21,948</td>
<td class="Message">--查询规则--&gt;status = 1</td>
<td class="MethodOfCaller">addEasyQuery</td>
<td class="FileOfCaller">QueryGenerator.java</td>
<td class="LineOfCaller">535</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 18:14:21,948</td>
<td class="Message">排序规则&gt;&gt;列:null,排序方式:null</td>
<td class="MethodOfCaller">doMultiFieldsOrder</td>
<td class="FileOfCaller">QueryGenerator.java</td>
<td class="LineOfCaller">227</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 18:14:21,987</td>
<td class="Message"> LogContent length : 11</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 18:14:21,987</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 18:14:24,612</td>
<td class="Message">--查询规则--&gt;status = 1</td>
<td class="MethodOfCaller">addEasyQuery</td>
<td class="FileOfCaller">QueryGenerator.java</td>
<td class="LineOfCaller">535</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 18:14:24,613</td>
<td class="Message">--查询规则--&gt;status = 1</td>
<td class="MethodOfCaller">addEasyQuery</td>
<td class="FileOfCaller">QueryGenerator.java</td>
<td class="LineOfCaller">535</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 18:14:24,612</td>
<td class="Message">--查询规则--&gt;status = 1</td>
<td class="MethodOfCaller">addEasyQuery</td>
<td class="FileOfCaller">QueryGenerator.java</td>
<td class="LineOfCaller">535</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 18:14:24,613</td>
<td class="Message">排序规则&gt;&gt;列:null,排序方式:null</td>
<td class="MethodOfCaller">doMultiFieldsOrder</td>
<td class="FileOfCaller">QueryGenerator.java</td>
<td class="LineOfCaller">227</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 18:14:24,613</td>
<td class="Message">排序规则&gt;&gt;列:null,排序方式:null</td>
<td class="MethodOfCaller">doMultiFieldsOrder</td>
<td class="FileOfCaller">QueryGenerator.java</td>
<td class="LineOfCaller">227</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 18:14:24,613</td>
<td class="Message">排序规则&gt;&gt;列:null,排序方式:null</td>
<td class="MethodOfCaller">doMultiFieldsOrder</td>
<td class="FileOfCaller">QueryGenerator.java</td>
<td class="LineOfCaller">227</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 18:14:24,620</td>
<td class="Message"> LogContent length : 12</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 18:14:24,620</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 18:14:24,621</td>
<td class="Message"> LogContent length : 13</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 18:14:24,621</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 18:14:24,621</td>
<td class="Message"> LogContent length : 13</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 18:14:24,621</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 18:14:25,874</td>
<td class="Message">--查询规则--&gt;status = 1</td>
<td class="MethodOfCaller">addEasyQuery</td>
<td class="FileOfCaller">QueryGenerator.java</td>
<td class="LineOfCaller">535</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 18:14:25,875</td>
<td class="Message">排序规则&gt;&gt;列:null,排序方式:null</td>
<td class="MethodOfCaller">doMultiFieldsOrder</td>
<td class="FileOfCaller">QueryGenerator.java</td>
<td class="LineOfCaller">227</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 18:14:25,920</td>
<td class="Message"> LogContent length : 11</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 18:14:25,921</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 18:14:32,540</td>
<td class="Message">官网用户登录: admin, 登录类型: website</td>
<td class="MethodOfCaller">login</td>
<td class="FileOfCaller">LoginController.java</td>
<td class="LineOfCaller">169</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 18:14:32,540</td>
<td class="Message">=== 开始统一登录处理 ===</td>
<td class="MethodOfCaller">processLogin</td>
<td class="FileOfCaller">LoginController.java</td>
<td class="LineOfCaller">785</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 18:14:32,540</td>
<td class="Message">用户：admin，登录类型：website</td>
<td class="MethodOfCaller">processLogin</td>
<td class="FileOfCaller">LoginController.java</td>
<td class="LineOfCaller">786</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 18:14:32,540</td>
<td class="Message">=== 开始检查用户admin角色 ===</td>
<td class="MethodOfCaller">checkUserIsAdmin</td>
<td class="FileOfCaller">RoleChecker.java</td>
<td class="LineOfCaller">39</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 18:14:32,540</td>
<td class="Message">用户ID：e9ca23d68d884d4ebb19d07889727dae</td>
<td class="MethodOfCaller">checkUserIsAdmin</td>
<td class="FileOfCaller">RoleChecker.java</td>
<td class="LineOfCaller">40</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 18:14:32,550</td>
<td class="Message">用户 e9ca23d68d884d4ebb19d07889727dae 的角色数量：1</td>
<td class="MethodOfCaller">checkUserIsAdmin</td>
<td class="FileOfCaller">RoleChecker.java</td>
<td class="LineOfCaller">47</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 18:14:32,557</td>
<td class="Message">用户 e9ca23d68d884d4ebb19d07889727dae 具有角色：管理员 (role_code: admin)</td>
<td class="MethodOfCaller">checkUserIsAdmin</td>
<td class="FileOfCaller">RoleChecker.java</td>
<td class="LineOfCaller">58</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 18:14:32,557</td>
<td class="Message">*** 用户 e9ca23d68d884d4ebb19d07889727dae 具有admin角色 ***</td>
<td class="MethodOfCaller">checkUserIsAdmin</td>
<td class="FileOfCaller">RoleChecker.java</td>
<td class="LineOfCaller">60</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 18:14:32,557</td>
<td class="Message">admin用户，允许多设备登录：admin</td>
<td class="MethodOfCaller">processLogin</td>
<td class="FileOfCaller">LoginController.java</td>
<td class="LineOfCaller">808</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 18:14:32,606</td>
<td class="Message">redis remove key:sys:cache:user::admin</td>
<td class="MethodOfCaller">remove</td>
<td class="FileOfCaller">JeecgRedisCacheWriter.java</td>
<td class="LineOfCaller">104</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 18:14:32,648</td>
<td class="Message">开始记录用户在线状态 - 用户ID: e9ca23d68d884d4ebb19d07889727dae, 会话ID: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJyYW5kb20iOjAuNjE1MTczNjM5MDUwNDQ2OCwiZXhwIjoxNzUyOTYzMjcyLCJ1c2VybmFtZSI6ImFkbWluIiwidGltZXN0YW1wIjoxNzUyOTIwMDcyNjA5fQ.8y8_ii-8nj-********************************</td>
<td class="MethodOfCaller">recordUserOnline</td>
<td class="FileOfCaller">LoginController.java</td>
<td class="LineOfCaller">944</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 18:14:32,656</td>
<td class="Message">设置用户 e9ca23d68d884d4ebb19d07889727dae 的其他会话为离线，影响行数: 5</td>
<td class="MethodOfCaller">recordUserOnline</td>
<td class="FileOfCaller">LoginController.java</td>
<td class="LineOfCaller">948</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 18:14:32,657</td>
<td class="Message">准备插入在线用户记录: AicgOnlineUsers(id=null, userId=e9ca23d68d884d4ebb19d07889727dae, sessionId=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJyYW5kb20iOjAuNjE1MTczNjM5MDUwNDQ2OCwiZXhwIjoxNzUyOTYzMjcyLCJ1c2VybmFtZSI6ImFkbWluIiwidGltZXN0YW1wIjoxNzUyOTIwMDcyNjA5fQ.8y8_ii-8nj-********************************, loginTime=Sat Jul 19 18:14:32 CST 2025, lastActiveTime=Sat Jul 19 18:14:32 CST 2025, ipAddress=null, userAgent=null, status=true, createTime=Sat Jul 19 18:14:32 CST 2025, updateTime=Sat Jul 19 18:14:32 CST 2025)</td>
<td class="MethodOfCaller">recordUserOnline</td>
<td class="FileOfCaller">LoginController.java</td>
<td class="LineOfCaller">960</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 18:14:32,678</td>
<td class="Message">用户 e9ca23d68d884d4ebb19d07889727dae 在线状态记录成功，插入行数: 1, 生成ID: 1946513982881820673</td>
<td class="MethodOfCaller">recordUserOnline</td>
<td class="FileOfCaller">LoginController.java</td>
<td class="LineOfCaller">966</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 18:14:32,681</td>
<td class="Message"> LogContent length : 16</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">79</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 18:14:32,682</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">80</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 18:14:33,156</td>
<td class="Message">🔍 getUserCenterOverview - 接收到的TOKEN: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJyYW5kb20iOjAuNjE1MTczNjM5MDUwNDQ2OCwiZXhwIjoxNzUyOTYzMjcyLCJ1c2VybmFtZSI6ImFkbWluIiwidGltZXN0YW1wIjoxNzUyOTIwMDcyNjA5fQ.8y8_ii-8nj-********************************</td>
<td class="MethodOfCaller">getUserCenterOverview</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">118</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 18:14:33,156</td>
<td class="Message">🎯 getUserOrderList - 用户: admin, 订单类型: null, 状态: null</td>
<td class="MethodOfCaller">getUserOrderList</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">456</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 18:14:33,156</td>
<td class="Message">🎯 getUserUsageList - 用户: admin, 分类: null, 关键词: null</td>
<td class="MethodOfCaller">getUserUsageList</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">240</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 18:14:33,157</td>
<td class="Message">🔍 getUserFullInfo - 接收到的TOKEN: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJyYW5kb20iOjAuNjE1MTczNjM5MDUwNDQ2OCwiZXhwIjoxNzUyOTYzMjcyLCJ1c2VybmFtZSI6ImFkbWluIiwidGltZXN0YW1wIjoxNzUyOTIwMDcyNjA5fQ.8y8_ii-8nj-********************************</td>
<td class="MethodOfCaller">getUserFullInfo</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">167</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 18:14:33,158</td>
<td class="Message">🔍 getUserCenterOverview - 解析出的用户名: admin</td>
<td class="MethodOfCaller">getUserCenterOverview</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">121</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 18:14:33,158</td>
<td class="Message">🔍 getUserFullInfo - 解析出的用户名: admin</td>
<td class="MethodOfCaller">getUserFullInfo</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">170</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 18:14:33,167</td>
<td class="Message">🔍 getUserCenterOverview - 查询到的用户扩展信息: AicgUserProfile(id=1933857527785254914, userId=e9ca23d68d884d4ebb19d07889727dae, username=null, nickname=执, phone=null, email=null, avatar=uploads/avatar/2025/07/12/1752277705228_e68b510f.jpeg, accountBalance=100000.00, frozenBalance=0.00, apiKey=ak_cc1a3ac3938b4046815c79b7c03d56b4, passwordChanged=1, totalConsumption=0.00, totalRecharge=0.00, memberExpireTime=Wed Dec 31 23:59:59 CST 2025, myInviteCode=null, usedInviteCode=null, inviterUserId=null, inviteCount=0, registerSource=manual, status=1, createBy=admin, createTime=Sat Jun 14 20:02:19 CST 2025, updateBy=api_system, updateTime=Wed Jul 16 16:32:18 CST 2025, sysOrgCode=A01)</td>
<td class="MethodOfCaller">getUserCenterOverview</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">125</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 18:14:33,181</td>
<td class="Message">🔍 getUserFullInfo - 查询到的用户信息: {account_balance=100000.00, user_create_time=2019-06-21 17:54:10.0, password_changed=1, avatar=uploads/avatar/2025/07/12/1752277705228_e68b510f.jpeg, total_recharge=0.00, member_expire_time=2025-12-31 23:59:59.0, realname=管理员, current_role=管理员, total_consumption=0.00, user_id=e9ca23d68d884d4ebb19d07889727dae, phone=***********, api_key=ak_cc1a3ac3938b4046815c79b7c03d56b4, nickname=执, email=<EMAIL>, username=admin}</td>
<td class="MethodOfCaller">getUserFullInfo</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">174</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 18:14:33,187</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 18:14:33,188</td>
<td class="Message">🔍 getUserCenterOverview - 返回的概览数据: {apiCallsToday=0, apiCallsMonth=0, totalConsumption=0.00, memberDaysLeft=165, accountBalance=100000.00, totalRecharge=0.00}</td>
<td class="MethodOfCaller">getUserCenterOverview</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">150</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 18:14:33,188</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 18:14:33,189</td>
<td class="Message"> LogContent length : 10</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 18:14:33,189</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 18:14:33,196</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 18:14:33,197</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 18:14:33,201</td>
<td class="Message">🎯 getUserUsageList - 查询结果: 总数=0, 当前页数据量=0</td>
<td class="MethodOfCaller">getUserUsageList</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">347</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 18:14:33,203</td>
<td class="Message"> LogContent length : 10</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 18:14:33,204</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 18:14:33,205</td>
<td class="Message">🎯 getUserOrderList - 查询结果: 总数=0, 当前页数据量=0</td>
<td class="MethodOfCaller">getUserOrderList</td>
<td class="FileOfCaller">UserCenterDataController.java</td>
<td class="LineOfCaller">585</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 18:14:33,206</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 18:14:33,207</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 18:14:36,182</td>
<td class="Message">【websocket消息】有新的连接，总数为:1</td>
<td class="MethodOfCaller">onOpen</td>
<td class="FileOfCaller">WebSocket.java</td>
<td class="LineOfCaller">58</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 18:14:36,210</td>
<td class="Message">=== 开始检查用户admin角色 ===</td>
<td class="MethodOfCaller">checkUserIsAdmin</td>
<td class="FileOfCaller">RoleChecker.java</td>
<td class="LineOfCaller">39</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 18:14:36,210</td>
<td class="Message">用户ID：e9ca23d68d884d4ebb19d07889727dae</td>
<td class="MethodOfCaller">checkUserIsAdmin</td>
<td class="FileOfCaller">RoleChecker.java</td>
<td class="LineOfCaller">40</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 18:14:36,215</td>
<td class="Message">用户 e9ca23d68d884d4ebb19d07889727dae 的角色数量：1</td>
<td class="MethodOfCaller">checkUserIsAdmin</td>
<td class="FileOfCaller">RoleChecker.java</td>
<td class="LineOfCaller">47</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 18:14:36,219</td>
<td class="Message">用户 e9ca23d68d884d4ebb19d07889727dae 具有角色：管理员 (role_code: admin)</td>
<td class="MethodOfCaller">checkUserIsAdmin</td>
<td class="FileOfCaller">RoleChecker.java</td>
<td class="LineOfCaller">58</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 18:14:36,219</td>
<td class="Message">*** 用户 e9ca23d68d884d4ebb19d07889727dae 具有admin角色 ***</td>
<td class="MethodOfCaller">checkUserIsAdmin</td>
<td class="FileOfCaller">RoleChecker.java</td>
<td class="LineOfCaller">60</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 18:14:36,230</td>
<td class="Message">计算系统总收入 - 所有用户消费总和: 0.00, admin消费: 0.00, 系统总收入: 0.00</td>
<td class="MethodOfCaller">calculateSystemTotalIncome</td>
<td class="FileOfCaller">AigcApiController.java</td>
<td class="LineOfCaller">716</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 18:14:36,231</td>
<td class="Message">admin用户查看系统总收入: 0.00</td>
<td class="MethodOfCaller">getUserExtendedInfo</td>
<td class="FileOfCaller">AigcApiController.java</td>
<td class="LineOfCaller">656</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 18:14:41,231</td>
<td class="Message"> LogContent length : 8</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 18:14:41,231</td>
<td class="Message"> LogContent length : 11</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 18:14:41,231</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 18:14:41,231</td>
<td class="Message">Invalid bound statement (not found): org.jeecg.modules.base.mapper.BaseCommonMapper.saveLog</td>
<td class="MethodOfCaller">addLog</td>
<td class="FileOfCaller">BaseCommonServiceImpl.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 18:14:46,773</td>
<td class="Message">排序规则&gt;&gt;列:createTime,排序方式:desc</td>
<td class="MethodOfCaller">doMultiFieldsOrder</td>
<td class="FileOfCaller">QueryGenerator.java</td>
<td class="LineOfCaller">227</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 18:14:46,783</td>
<td class="Message">com.baomidou.mybatisplus.extension.plugins.pagination.Page@6979934</td>
<td class="MethodOfCaller">queryPageList</td>
<td class="FileOfCaller">SysUserController.java</td>
<td class="LineOfCaller">135</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 18:16:42,204</td>
<td class="Message">计算系统总收入 - 所有用户消费总和: 0.00, admin消费: 0.00, 系统总收入: 0.00</td>
<td class="MethodOfCaller">calculateSystemTotalIncome</td>
<td class="FileOfCaller">AigcApiController.java</td>
<td class="LineOfCaller">716</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 18:16:42,204</td>
<td class="Message">admin用户查看系统总收入: 0.00</td>
<td class="MethodOfCaller">getUserExtendedInfo</td>
<td class="FileOfCaller">AigcApiController.java</td>
<td class="LineOfCaller">656</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 18:19:49,554</td>
<td class="Message">计算系统总收入 - 所有用户消费总和: 0.00, admin消费: 0.00, 系统总收入: 0.00</td>
<td class="MethodOfCaller">calculateSystemTotalIncome</td>
<td class="FileOfCaller">AigcApiController.java</td>
<td class="LineOfCaller">716</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 18:19:49,554</td>
<td class="Message">admin用户查看系统总收入: 0.00</td>
<td class="MethodOfCaller">getUserExtendedInfo</td>
<td class="FileOfCaller">AigcApiController.java</td>
<td class="LineOfCaller">656</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 18:35:43,388</td>
<td class="Message">计算系统总收入 - 所有用户消费总和: 0.00, admin消费: 0.00, 系统总收入: 0.00</td>
<td class="MethodOfCaller">calculateSystemTotalIncome</td>
<td class="FileOfCaller">AigcApiController.java</td>
<td class="LineOfCaller">716</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 18:35:43,388</td>
<td class="Message">admin用户查看系统总收入: 0.00</td>
<td class="MethodOfCaller">getUserExtendedInfo</td>
<td class="FileOfCaller">AigcApiController.java</td>
<td class="LineOfCaller">656</td>
</tr>

<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-07-19 18:36:15,144</td>
<td class="Message">【websocket消息】连接错误，用户ID: e9ca23d68d884d4ebb19d07889727dae, 错误信息: null</td>
<td class="MethodOfCaller">onError</td>
<td class="FileOfCaller">WebSocket.java</td>
<td class="LineOfCaller">135</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 18:36:15,144</td>
<td class="Message">【websocket消息】错误连接已清理，用户ID: e9ca23d68d884d4ebb19d07889727dae, 剩余连接数: 0</td>
<td class="MethodOfCaller">onError</td>
<td class="FileOfCaller">WebSocket.java</td>
<td class="LineOfCaller">141</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 18:36:15,151</td>
<td class="Message">【websocket消息】连接断开，总数为:0</td>
<td class="MethodOfCaller">onClose</td>
<td class="FileOfCaller">WebSocket.java</td>
<td class="LineOfCaller">68</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 20:36:18,210</td>
<td class="Message">【websocket消息】有新的连接，总数为:1</td>
<td class="MethodOfCaller">onOpen</td>
<td class="FileOfCaller">WebSocket.java</td>
<td class="LineOfCaller">58</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 20:36:19,974</td>
<td class="Message">计算系统总收入 - 所有用户消费总和: 0.00, admin消费: 0.00, 系统总收入: 0.00</td>
<td class="MethodOfCaller">calculateSystemTotalIncome</td>
<td class="FileOfCaller">AigcApiController.java</td>
<td class="LineOfCaller">716</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 20:36:19,974</td>
<td class="Message">admin用户查看系统总收入: 0.00</td>
<td class="MethodOfCaller">getUserExtendedInfo</td>
<td class="FileOfCaller">AigcApiController.java</td>
<td class="LineOfCaller">656</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 20:36:36,526</td>
<td class="Message">计算系统总收入 - 所有用户消费总和: 0.00, admin消费: 0.00, 系统总收入: 0.00</td>
<td class="MethodOfCaller">calculateSystemTotalIncome</td>
<td class="FileOfCaller">AigcApiController.java</td>
<td class="LineOfCaller">716</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 20:36:36,526</td>
<td class="Message">admin用户查看系统总收入: 0.00</td>
<td class="MethodOfCaller">getUserExtendedInfo</td>
<td class="FileOfCaller">AigcApiController.java</td>
<td class="LineOfCaller">656</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 20:37:36,532</td>
<td class="Message">计算系统总收入 - 所有用户消费总和: 0.00, admin消费: 0.00, 系统总收入: 0.00</td>
<td class="MethodOfCaller">calculateSystemTotalIncome</td>
<td class="FileOfCaller">AigcApiController.java</td>
<td class="LineOfCaller">716</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 20:37:36,532</td>
<td class="Message">admin用户查看系统总收入: 0.00</td>
<td class="MethodOfCaller">getUserExtendedInfo</td>
<td class="FileOfCaller">AigcApiController.java</td>
<td class="LineOfCaller">656</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 20:38:55,537</td>
<td class="Message">计算系统总收入 - 所有用户消费总和: 0.00, admin消费: 0.00, 系统总收入: 0.00</td>
<td class="MethodOfCaller">calculateSystemTotalIncome</td>
<td class="FileOfCaller">AigcApiController.java</td>
<td class="LineOfCaller">716</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 20:38:55,537</td>
<td class="Message">admin用户查看系统总收入: 0.00</td>
<td class="MethodOfCaller">getUserExtendedInfo</td>
<td class="FileOfCaller">AigcApiController.java</td>
<td class="LineOfCaller">656</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 20:39:55,547</td>
<td class="Message">计算系统总收入 - 所有用户消费总和: 0.00, admin消费: 0.00, 系统总收入: 0.00</td>
<td class="MethodOfCaller">calculateSystemTotalIncome</td>
<td class="FileOfCaller">AigcApiController.java</td>
<td class="LineOfCaller">716</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 20:39:55,547</td>
<td class="Message">admin用户查看系统总收入: 0.00</td>
<td class="MethodOfCaller">getUserExtendedInfo</td>
<td class="FileOfCaller">AigcApiController.java</td>
<td class="LineOfCaller">656</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 20:40:55,537</td>
<td class="Message">计算系统总收入 - 所有用户消费总和: 0.00, admin消费: 0.00, 系统总收入: 0.00</td>
<td class="MethodOfCaller">calculateSystemTotalIncome</td>
<td class="FileOfCaller">AigcApiController.java</td>
<td class="LineOfCaller">716</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 20:40:55,538</td>
<td class="Message">admin用户查看系统总收入: 0.00</td>
<td class="MethodOfCaller">getUserExtendedInfo</td>
<td class="FileOfCaller">AigcApiController.java</td>
<td class="LineOfCaller">656</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 20:41:55,534</td>
<td class="Message">计算系统总收入 - 所有用户消费总和: 0.00, admin消费: 0.00, 系统总收入: 0.00</td>
<td class="MethodOfCaller">calculateSystemTotalIncome</td>
<td class="FileOfCaller">AigcApiController.java</td>
<td class="LineOfCaller">716</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 20:41:55,534</td>
<td class="Message">admin用户查看系统总收入: 0.00</td>
<td class="MethodOfCaller">getUserExtendedInfo</td>
<td class="FileOfCaller">AigcApiController.java</td>
<td class="LineOfCaller">656</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 20:42:55,542</td>
<td class="Message">计算系统总收入 - 所有用户消费总和: 0.00, admin消费: 0.00, 系统总收入: 0.00</td>
<td class="MethodOfCaller">calculateSystemTotalIncome</td>
<td class="FileOfCaller">AigcApiController.java</td>
<td class="LineOfCaller">716</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 20:42:55,542</td>
<td class="Message">admin用户查看系统总收入: 0.00</td>
<td class="MethodOfCaller">getUserExtendedInfo</td>
<td class="FileOfCaller">AigcApiController.java</td>
<td class="LineOfCaller">656</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 20:43:55,549</td>
<td class="Message">计算系统总收入 - 所有用户消费总和: 0.00, admin消费: 0.00, 系统总收入: 0.00</td>
<td class="MethodOfCaller">calculateSystemTotalIncome</td>
<td class="FileOfCaller">AigcApiController.java</td>
<td class="LineOfCaller">716</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 20:43:55,549</td>
<td class="Message">admin用户查看系统总收入: 0.00</td>
<td class="MethodOfCaller">getUserExtendedInfo</td>
<td class="FileOfCaller">AigcApiController.java</td>
<td class="LineOfCaller">656</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 20:44:55,549</td>
<td class="Message">计算系统总收入 - 所有用户消费总和: 0.00, admin消费: 0.00, 系统总收入: 0.00</td>
<td class="MethodOfCaller">calculateSystemTotalIncome</td>
<td class="FileOfCaller">AigcApiController.java</td>
<td class="LineOfCaller">716</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 20:44:55,549</td>
<td class="Message">admin用户查看系统总收入: 0.00</td>
<td class="MethodOfCaller">getUserExtendedInfo</td>
<td class="FileOfCaller">AigcApiController.java</td>
<td class="LineOfCaller">656</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 20:45:55,533</td>
<td class="Message">计算系统总收入 - 所有用户消费总和: 0.00, admin消费: 0.00, 系统总收入: 0.00</td>
<td class="MethodOfCaller">calculateSystemTotalIncome</td>
<td class="FileOfCaller">AigcApiController.java</td>
<td class="LineOfCaller">716</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 20:45:55,534</td>
<td class="Message">admin用户查看系统总收入: 0.00</td>
<td class="MethodOfCaller">getUserExtendedInfo</td>
<td class="FileOfCaller">AigcApiController.java</td>
<td class="LineOfCaller">656</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 20:46:55,535</td>
<td class="Message">计算系统总收入 - 所有用户消费总和: 0.00, admin消费: 0.00, 系统总收入: 0.00</td>
<td class="MethodOfCaller">calculateSystemTotalIncome</td>
<td class="FileOfCaller">AigcApiController.java</td>
<td class="LineOfCaller">716</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 20:46:55,536</td>
<td class="Message">admin用户查看系统总收入: 0.00</td>
<td class="MethodOfCaller">getUserExtendedInfo</td>
<td class="FileOfCaller">AigcApiController.java</td>
<td class="LineOfCaller">656</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 20:47:55,535</td>
<td class="Message">计算系统总收入 - 所有用户消费总和: 0.00, admin消费: 0.00, 系统总收入: 0.00</td>
<td class="MethodOfCaller">calculateSystemTotalIncome</td>
<td class="FileOfCaller">AigcApiController.java</td>
<td class="LineOfCaller">716</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 20:47:55,535</td>
<td class="Message">admin用户查看系统总收入: 0.00</td>
<td class="MethodOfCaller">getUserExtendedInfo</td>
<td class="FileOfCaller">AigcApiController.java</td>
<td class="LineOfCaller">656</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 20:48:55,541</td>
<td class="Message">计算系统总收入 - 所有用户消费总和: 0.00, admin消费: 0.00, 系统总收入: 0.00</td>
<td class="MethodOfCaller">calculateSystemTotalIncome</td>
<td class="FileOfCaller">AigcApiController.java</td>
<td class="LineOfCaller">716</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 20:48:55,542</td>
<td class="Message">admin用户查看系统总收入: 0.00</td>
<td class="MethodOfCaller">getUserExtendedInfo</td>
<td class="FileOfCaller">AigcApiController.java</td>
<td class="LineOfCaller">656</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 20:49:55,538</td>
<td class="Message">计算系统总收入 - 所有用户消费总和: 0.00, admin消费: 0.00, 系统总收入: 0.00</td>
<td class="MethodOfCaller">calculateSystemTotalIncome</td>
<td class="FileOfCaller">AigcApiController.java</td>
<td class="LineOfCaller">716</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 20:49:55,538</td>
<td class="Message">admin用户查看系统总收入: 0.00</td>
<td class="MethodOfCaller">getUserExtendedInfo</td>
<td class="FileOfCaller">AigcApiController.java</td>
<td class="LineOfCaller">656</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 20:50:55,547</td>
<td class="Message">计算系统总收入 - 所有用户消费总和: 0.00, admin消费: 0.00, 系统总收入: 0.00</td>
<td class="MethodOfCaller">calculateSystemTotalIncome</td>
<td class="FileOfCaller">AigcApiController.java</td>
<td class="LineOfCaller">716</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 20:50:55,548</td>
<td class="Message">admin用户查看系统总收入: 0.00</td>
<td class="MethodOfCaller">getUserExtendedInfo</td>
<td class="FileOfCaller">AigcApiController.java</td>
<td class="LineOfCaller">656</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 20:51:55,540</td>
<td class="Message">计算系统总收入 - 所有用户消费总和: 0.00, admin消费: 0.00, 系统总收入: 0.00</td>
<td class="MethodOfCaller">calculateSystemTotalIncome</td>
<td class="FileOfCaller">AigcApiController.java</td>
<td class="LineOfCaller">716</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 20:51:55,540</td>
<td class="Message">admin用户查看系统总收入: 0.00</td>
<td class="MethodOfCaller">getUserExtendedInfo</td>
<td class="FileOfCaller">AigcApiController.java</td>
<td class="LineOfCaller">656</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 20:52:55,545</td>
<td class="Message">计算系统总收入 - 所有用户消费总和: 0.00, admin消费: 0.00, 系统总收入: 0.00</td>
<td class="MethodOfCaller">calculateSystemTotalIncome</td>
<td class="FileOfCaller">AigcApiController.java</td>
<td class="LineOfCaller">716</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 20:52:55,545</td>
<td class="Message">admin用户查看系统总收入: 0.00</td>
<td class="MethodOfCaller">getUserExtendedInfo</td>
<td class="FileOfCaller">AigcApiController.java</td>
<td class="LineOfCaller">656</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 20:53:55,538</td>
<td class="Message">计算系统总收入 - 所有用户消费总和: 0.00, admin消费: 0.00, 系统总收入: 0.00</td>
<td class="MethodOfCaller">calculateSystemTotalIncome</td>
<td class="FileOfCaller">AigcApiController.java</td>
<td class="LineOfCaller">716</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 20:53:55,538</td>
<td class="Message">admin用户查看系统总收入: 0.00</td>
<td class="MethodOfCaller">getUserExtendedInfo</td>
<td class="FileOfCaller">AigcApiController.java</td>
<td class="LineOfCaller">656</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 20:54:55,548</td>
<td class="Message">计算系统总收入 - 所有用户消费总和: 0.00, admin消费: 0.00, 系统总收入: 0.00</td>
<td class="MethodOfCaller">calculateSystemTotalIncome</td>
<td class="FileOfCaller">AigcApiController.java</td>
<td class="LineOfCaller">716</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 20:54:55,548</td>
<td class="Message">admin用户查看系统总收入: 0.00</td>
<td class="MethodOfCaller">getUserExtendedInfo</td>
<td class="FileOfCaller">AigcApiController.java</td>
<td class="LineOfCaller">656</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 20:55:36,579</td>
<td class="Message">计算系统总收入 - 所有用户消费总和: 0.00, admin消费: 0.00, 系统总收入: 0.00</td>
<td class="MethodOfCaller">calculateSystemTotalIncome</td>
<td class="FileOfCaller">AigcApiController.java</td>
<td class="LineOfCaller">716</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 20:55:36,580</td>
<td class="Message">admin用户查看系统总收入: 0.00</td>
<td class="MethodOfCaller">getUserExtendedInfo</td>
<td class="FileOfCaller">AigcApiController.java</td>
<td class="LineOfCaller">656</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 20:56:44,717</td>
<td class="Message">null Unexpected exception during request: java.io.IOException: 远程主机强迫关闭了一个现有的连接。</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">217</td>
</tr>
<tr><td class="Exception" colspan="6">java.io.IOException: 远程主机强迫关闭了一个现有的连接。
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.nio.ch.SocketDispatcher.read0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.nio.ch.IOUtil.read(IOUtil.java:192)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1134)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:714)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:650)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:576)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.lang.Thread.run(Thread.java:748)
</td></tr>
<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 20:56:44,717</td>
<td class="Message">null Unexpected exception during request: java.io.IOException: 远程主机强迫关闭了一个现有的连接。</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">217</td>
</tr>
<tr><td class="Exception" colspan="6">java.io.IOException: 远程主机强迫关闭了一个现有的连接。
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.nio.ch.SocketDispatcher.read0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.nio.ch.IOUtil.read(IOUtil.java:192)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1134)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:714)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:650)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:576)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.lang.Thread.run(Thread.java:748)
</td></tr>
<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 20:56:44,717</td>
<td class="Message">null Unexpected exception during request: java.io.IOException: 远程主机强迫关闭了一个现有的连接。</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">217</td>
</tr>
<tr><td class="Exception" colspan="6">java.io.IOException: 远程主机强迫关闭了一个现有的连接。
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.nio.ch.SocketDispatcher.read0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.nio.ch.IOUtil.read(IOUtil.java:192)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1134)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:714)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:650)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:576)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.lang.Thread.run(Thread.java:748)
</td></tr>
<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 20:56:44,842</td>
<td class="Message">Reconnecting, last destination was /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 20:56:44,842</td>
<td class="Message">Reconnecting, last destination was /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 20:56:44,842</td>
<td class="Message">Reconnecting, last destination was /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 20:56:46,915</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 20:56:46,915</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 20:56:46,915</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 20:56:51,130</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 20:56:51,130</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 20:56:51,130</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 20:56:53,185</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 20:56:53,185</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 20:56:53,201</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 20:56:57,528</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 20:56:57,528</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 20:56:57,528</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 20:56:59,572</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 20:56:59,572</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 20:56:59,572</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 20:57:04,631</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 20:57:04,630</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 20:57:04,630</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 20:57:06,680</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 20:57:06,680</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 20:57:06,680</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 20:57:11,833</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 20:57:11,833</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 20:57:11,833</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 20:57:13,861</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 20:57:13,877</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 20:57:13,877</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 20:57:18,037</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 20:57:18,037</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 20:57:18,037</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 20:57:20,082</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 20:57:20,081</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 20:57:20,081</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 20:57:28,328</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 20:57:28,328</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 20:57:28,328</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 20:57:30,376</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 20:57:30,376</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 20:57:30,391</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 20:57:46,829</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 20:57:46,830</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 20:57:46,829</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 20:57:48,871</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 20:57:48,871</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 20:57:48,871</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 20:57:55,588</td>
<td class="Message">Authentication failed for token submission [org.jeecg.config.shiro.JwtToken@4a1f2d9f].  Possible unexpected error? (Typical or expected login exceptions should extend from AuthenticationException).</td>
<td class="MethodOfCaller">authenticate</td>
<td class="FileOfCaller">AbstractAuthenticator.java</td>
<td class="LineOfCaller">216</td>
</tr>
<tr><td class="Exception" colspan="6">org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 minute(s)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:273)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.redis.connection.lettuce.LettuceStringCommands.convertLettuceAccessException(LettuceStringCommands.java:799)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.redis.connection.lettuce.LettuceStringCommands.get(LettuceStringCommands.java:68)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.redis.connection.DefaultedRedisConnection.get(DefaultedRedisConnection.java:266)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.redis.core.DefaultValueOperations$1.inRedis(DefaultValueOperations.java:57)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.redis.core.AbstractOperations$ValueDeserializingRedisCallback.doInRedis(AbstractOperations.java:60)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.redis.core.AbstractOperations.execute(AbstractOperations.java:96)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.redis.core.DefaultValueOperations.get(DefaultValueOperations.java:53)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.common.util.RedisUtil.get(RedisUtil.java:95)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.common.util.RedisUtil$$FastClassBySpringCGLIB$$da69ea.invoke(&lt;generated&gt;)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:687)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.common.util.RedisUtil$$EnhancerBySpringCGLIB$$167375af.get(&lt;generated&gt;)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.config.shiro.ShiroRealm.checkUserTokenIsEffect(ShiroRealm.java:115)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.config.shiro.ShiroRealm.doGetAuthenticationInfo(ShiroRealm.java:103)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.realm.AuthenticatingRealm.getAuthenticationInfo(AuthenticatingRealm.java:571)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.authc.pam.ModularRealmAuthenticator.doSingleRealmAuthentication(ModularRealmAuthenticator.java:180)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.authc.pam.ModularRealmAuthenticator.doAuthenticate(ModularRealmAuthenticator.java:273)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.authc.AbstractAuthenticator.authenticate(AbstractAuthenticator.java:198)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.mgt.AuthenticatingSecurityManager.authenticate(AuthenticatingSecurityManager.java:106)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.mgt.DefaultSecurityManager.login(DefaultSecurityManager.java:275)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.subject.support.DelegatingSubject.login(DelegatingSubject.java:260)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.config.shiro.filters.JwtFilter.executeLogin(JwtFilter.java:93)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.config.shiro.filters.JwtFilter.isAccessAllowed(JwtFilter.java:49)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.filter.AccessControlFilter.onPreHandle(AccessControlFilter.java:162)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.filter.PathMatchingFilter.isFilterChainContinued(PathMatchingFilter.java:223)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.filter.PathMatchingFilter.preHandle(PathMatchingFilter.java:198)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.config.shiro.filters.JwtFilter.preHandle(JwtFilter.java:123)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:131)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:125)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:125)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AbstractShiroFilter.executeChain(AbstractShiroFilter.java:450)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AbstractShiroFilter$1.call(AbstractShiroFilter.java:365)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.subject.support.SubjectCallable.doCall(SubjectCallable.java:90)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.subject.support.SubjectCallable.call(SubjectCallable.java:83)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.subject.support.DelegatingSubject.execute(DelegatingSubject.java:387)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AbstractShiroFilter.doFilterInternal(AbstractShiroFilter.java:362)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:125)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:92)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:124)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.boot.actuate.web.trace.servlet.HttpTraceFilter.doFilterInternal(HttpTraceFilter.java:88)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:868)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1590)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.lang.Thread.run(Thread.java:748)
<br />Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 minute(s)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.sun.proxy.$Proxy361.get(Unknown Source)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.redis.connection.lettuce.LettuceStringCommands.get(LettuceStringCommands.java:66)
<br />&nbsp;&nbsp;&nbsp;&nbsp;	... 86 common frames omitted
</td></tr>
<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 20:57:59,875</td>
<td class="Message">【websocket消息】连接断开，总数为:0</td>
<td class="MethodOfCaller">onClose</td>
<td class="FileOfCaller">WebSocket.java</td>
<td class="LineOfCaller">68</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 20:58:18,932</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 20:58:18,932</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 20:58:18,932</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 20:58:20,973</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 20:58:20,973</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 20:58:20,973</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 20:58:51,031</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 20:58:51,031</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 20:58:51,031</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 20:58:53,078</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 20:58:53,078</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 20:58:53,078</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 20:58:55,543</td>
<td class="Message">Authentication failed for token submission [org.jeecg.config.shiro.JwtToken@3dfbf165].  Possible unexpected error? (Typical or expected login exceptions should extend from AuthenticationException).</td>
<td class="MethodOfCaller">authenticate</td>
<td class="FileOfCaller">AbstractAuthenticator.java</td>
<td class="LineOfCaller">216</td>
</tr>
<tr><td class="Exception" colspan="6">org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 minute(s)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:273)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.redis.connection.lettuce.LettuceStringCommands.convertLettuceAccessException(LettuceStringCommands.java:799)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.redis.connection.lettuce.LettuceStringCommands.get(LettuceStringCommands.java:68)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.redis.connection.DefaultedRedisConnection.get(DefaultedRedisConnection.java:266)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.redis.core.DefaultValueOperations$1.inRedis(DefaultValueOperations.java:57)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.redis.core.AbstractOperations$ValueDeserializingRedisCallback.doInRedis(AbstractOperations.java:60)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.redis.core.AbstractOperations.execute(AbstractOperations.java:96)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.redis.core.DefaultValueOperations.get(DefaultValueOperations.java:53)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.common.util.RedisUtil.get(RedisUtil.java:95)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.common.util.RedisUtil$$FastClassBySpringCGLIB$$da69ea.invoke(&lt;generated&gt;)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:687)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.common.util.RedisUtil$$EnhancerBySpringCGLIB$$167375af.get(&lt;generated&gt;)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.config.shiro.ShiroRealm.checkUserTokenIsEffect(ShiroRealm.java:115)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.config.shiro.ShiroRealm.doGetAuthenticationInfo(ShiroRealm.java:103)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.realm.AuthenticatingRealm.getAuthenticationInfo(AuthenticatingRealm.java:571)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.authc.pam.ModularRealmAuthenticator.doSingleRealmAuthentication(ModularRealmAuthenticator.java:180)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.authc.pam.ModularRealmAuthenticator.doAuthenticate(ModularRealmAuthenticator.java:273)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.authc.AbstractAuthenticator.authenticate(AbstractAuthenticator.java:198)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.mgt.AuthenticatingSecurityManager.authenticate(AuthenticatingSecurityManager.java:106)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.mgt.DefaultSecurityManager.login(DefaultSecurityManager.java:275)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.subject.support.DelegatingSubject.login(DelegatingSubject.java:260)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.config.shiro.filters.JwtFilter.executeLogin(JwtFilter.java:93)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.config.shiro.filters.JwtFilter.isAccessAllowed(JwtFilter.java:49)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.filter.AccessControlFilter.onPreHandle(AccessControlFilter.java:162)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.filter.PathMatchingFilter.isFilterChainContinued(PathMatchingFilter.java:223)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.filter.PathMatchingFilter.preHandle(PathMatchingFilter.java:198)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.config.shiro.filters.JwtFilter.preHandle(JwtFilter.java:123)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:131)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:125)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:125)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AbstractShiroFilter.executeChain(AbstractShiroFilter.java:450)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AbstractShiroFilter$1.call(AbstractShiroFilter.java:365)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.subject.support.SubjectCallable.doCall(SubjectCallable.java:90)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.subject.support.SubjectCallable.call(SubjectCallable.java:83)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.subject.support.DelegatingSubject.execute(DelegatingSubject.java:387)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AbstractShiroFilter.doFilterInternal(AbstractShiroFilter.java:362)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:125)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:92)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:124)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.boot.actuate.web.trace.servlet.HttpTraceFilter.doFilterInternal(HttpTraceFilter.java:88)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:868)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1590)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.lang.Thread.run(Thread.java:748)
<br />Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 minute(s)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.sun.proxy.$Proxy361.get(Unknown Source)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.redis.connection.lettuce.LettuceStringCommands.get(LettuceStringCommands.java:66)
<br />&nbsp;&nbsp;&nbsp;&nbsp;	... 86 common frames omitted
</td></tr>
<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 20:59:23,133</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 20:59:23,133</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 20:59:23,133</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 20:59:25,180</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 20:59:25,180</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 20:59:25,180</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 20:59:55,234</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 20:59:55,235</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 20:59:55,234</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 20:59:57,255</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 20:59:57,258</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 20:59:57,259</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:00:27,328</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:00:27,330</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:00:27,330</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:00:29,385</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:00:29,385</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:00:29,386</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:00:59,429</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:00:59,429</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:00:59,429</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:01:01,456</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:01:01,460</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:01:01,461</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:01:31,535</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:01:31,534</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:01:31,534</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:01:33,573</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:01:33,589</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:01:33,603</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:02:03,629</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:02:03,629</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:02:03,629</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:02:05,684</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:02:05,684</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:02:05,699</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:02:35,728</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:02:35,728</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:02:35,728</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:02:37,764</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:02:37,764</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:02:37,764</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:03:07,828</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:03:07,828</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:03:07,828</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:03:09,861</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:03:09,861</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:03:09,863</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:03:39,934</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:03:39,935</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:03:39,934</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:03:41,974</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:03:41,974</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:03:41,974</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:04:12,028</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:04:12,028</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:04:12,028</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:04:14,063</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:04:14,063</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:04:14,064</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:04:44,129</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:04:44,129</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:04:44,129</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:04:46,175</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:04:46,178</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:04:46,178</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:05:16,229</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:05:16,229</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:05:16,229</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:05:18,278</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:05:18,278</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:05:18,279</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:05:48,334</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:05:48,334</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:05:48,334</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:05:50,387</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:05:50,387</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:05:50,388</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:06:20,429</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:06:20,430</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:06:20,429</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:06:22,460</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:06:22,460</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:06:22,460</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:06:52,529</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:06:52,529</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:06:52,529</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:06:54,550</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:06:54,550</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:06:54,565</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:07:24,630</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:07:24,630</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:07:24,629</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:07:26,666</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:07:26,666</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:07:26,666</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:07:56,728</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:07:56,728</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:07:56,728</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:07:58,742</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:07:58,743</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:07:58,758</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:08:28,834</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:08:28,834</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:08:28,834</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:08:30,868</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:08:30,884</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:08:30,884</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:09:00,939</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:09:00,939</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:09:00,939</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:09:02,965</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:09:02,980</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:09:02,980</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:09:33,033</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:09:33,035</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:09:33,035</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:09:35,067</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:09:35,068</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:09:35,081</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:10:05,129</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:10:05,129</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:10:05,129</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:10:07,173</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:10:07,172</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:10:07,173</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:10:37,234</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:10:37,234</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:10:37,234</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:10:39,281</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:10:39,281</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:10:39,281</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:11:09,341</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:11:09,341</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:11:09,341</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:11:11,386</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:11:11,400</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:11:11,400</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:11:41,428</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:11:41,429</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:11:41,429</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:11:43,473</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:11:43,473</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:11:43,486</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:12:13,532</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:12:13,532</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:12:13,532</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:12:15,582</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:12:15,584</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:12:15,584</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:12:45,629</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:12:45,629</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:12:45,629</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:12:47,652</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:12:47,667</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:12:47,667</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:13:17,734</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:13:17,734</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:13:17,734</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:13:19,770</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:13:19,770</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:13:19,771</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:13:49,831</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:13:49,831</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:13:49,831</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:13:51,850</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:13:51,850</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:13:51,865</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:14:21,943</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:14:21,943</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:14:21,943</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:14:23,959</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:14:23,959</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:14:23,974</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:14:54,034</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:14:54,034</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:14:54,035</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:14:56,059</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:14:56,073</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:14:56,073</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:15:26,129</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:15:26,129</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:15:26,129</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:15:28,159</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:15:28,159</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:15:28,159</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:15:58,242</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:15:58,242</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:15:58,242</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:16:00,286</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:16:00,286</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:16:00,302</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:16:30,332</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:16:30,332</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:16:30,332</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:16:32,373</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:16:32,373</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:16:32,373</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:17:02,433</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:17:02,433</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:17:02,433</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:17:04,472</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:17:04,472</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:17:04,472</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:17:34,530</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:17:34,530</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:17:34,530</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:17:36,577</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:17:36,577</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:17:36,578</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:18:06,631</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:18:06,631</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:18:06,631</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:18:08,653</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:18:08,654</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:18:08,668</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:18:38,729</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:18:38,730</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:18:38,730</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:18:40,773</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:18:40,773</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:18:40,773</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:19:10,829</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:19:10,829</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:19:10,829</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:19:12,864</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:19:12,864</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:19:12,864</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:19:42,936</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:19:42,936</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:19:42,936</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:19:44,989</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:19:45,005</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:19:45,005</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:20:15,029</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:20:15,029</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:20:15,029</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:20:17,066</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:20:17,066</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:20:17,066</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:20:47,135</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:20:47,135</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:20:47,135</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:20:49,180</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:20:49,183</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:20:49,183</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:21:19,229</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:21:19,229</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:21:19,229</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:21:21,280</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:21:21,280</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:21:21,280</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:21:51,328</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:21:51,328</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:21:51,328</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:21:53,348</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:21:53,349</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:21:53,361</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:22:23,442</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:22:23,442</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:22:23,442</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:22:25,471</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:22:25,472</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:22:25,474</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:22:55,528</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:22:55,529</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:22:55,528</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:22:57,563</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:22:57,563</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:22:57,566</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:23:27,642</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:23:27,642</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:23:27,642</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:23:29,680</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:23:29,695</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:23:29,695</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:23:59,730</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:23:59,730</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:23:59,730</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:24:01,766</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:24:01,766</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:24:01,766</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:24:31,829</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:24:31,829</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:24:31,829</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:24:33,853</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:24:33,870</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:24:33,870</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:25:03,928</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:25:03,930</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:25:03,928</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:25:05,960</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:25:05,960</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:25:05,960</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:25:36,044</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:25:36,044</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:25:36,044</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:25:38,098</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:25:38,098</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:25:38,098</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:26:08,131</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:26:08,131</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:26:08,131</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:26:10,176</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:26:10,176</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:26:10,176</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:26:40,228</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:26:40,228</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:26:40,228</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:26:42,257</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:26:42,257</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:26:42,272</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:27:12,335</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:27:12,335</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:27:12,335</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:27:14,382</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:27:14,382</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:27:14,382</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:27:44,428</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:27:44,430</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:27:44,429</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:27:46,461</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:27:46,477</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:27:46,477</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:28:16,528</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:28:16,528</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:28:16,528</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:28:18,547</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:28:18,563</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:28:18,563</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:28:48,632</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:28:48,631</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:28:48,631</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:28:50,661</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:28:50,677</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:28:50,677</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:29:20,729</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:29:20,735</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:29:20,729</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:29:22,766</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:29:22,766</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:29:22,766</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:29:52,829</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:29:52,829</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:29:52,829</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:29:54,869</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:29:54,869</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:29:54,869</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:30:24,928</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:30:24,928</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:30:24,928</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:30:26,974</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:30:26,974</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:30:26,989</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:30:57,033</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:30:57,033</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:30:57,033</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:30:59,077</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:30:59,077</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:30:59,093</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:31:29,129</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:31:29,129</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:31:29,129</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:31:31,176</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:31:31,176</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:31:31,177</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:32:01,230</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:32:01,229</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:32:01,229</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:32:03,283</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:32:03,286</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:32:03,302</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:32:33,341</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:32:33,341</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:32:33,341</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:32:35,386</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:32:35,386</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:32:35,386</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:33:05,428</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:33:05,428</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:33:05,429</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:33:07,458</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:33:07,458</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:33:07,475</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:33:37,532</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:33:37,532</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:33:37,532</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:33:39,562</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:33:39,563</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:33:39,578</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:34:09,632</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:34:09,633</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:34:09,633</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:34:11,665</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:34:11,665</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:34:11,682</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:34:41,730</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:34:41,730</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:34:41,730</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:34:43,766</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:34:43,767</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:34:43,782</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:35:13,829</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:35:13,832</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:35:13,829</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:35:15,863</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:35:15,879</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:35:15,879</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:35:45,929</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:35:45,929</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:35:45,929</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:35:47,946</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:35:47,977</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:35:47,977</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:36:18,033</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:36:18,033</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:36:18,033</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:36:20,084</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:36:20,084</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:36:20,084</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:36:50,133</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:36:50,133</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:36:50,133</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:36:52,187</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:36:52,187</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:36:52,188</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:37:22,230</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:37:22,230</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:37:22,230</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:37:24,269</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:37:24,269</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:37:24,269</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:37:54,343</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:37:54,344</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:37:54,344</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:37:56,394</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:37:56,394</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:37:56,397</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:38:26,432</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:38:26,432</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:38:26,432</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:38:28,484</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:38:28,484</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:38:28,484</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:38:58,529</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:38:58,529</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:38:58,529</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:39:00,575</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:39:00,575</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:39:00,575</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:39:30,630</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:39:30,629</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:39:30,629</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:39:32,655</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:39:32,656</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:39:32,670</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:40:02,728</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:40:02,728</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:40:02,728</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:40:04,763</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:40:04,763</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:40:04,778</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:40:34,835</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:40:34,835</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:40:34,835</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:40:36,868</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:40:36,884</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:40:36,884</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:41:06,930</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:41:06,930</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:41:06,930</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:41:08,969</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:41:08,969</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:41:08,984</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:41:39,030</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:41:39,030</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:41:39,030</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:41:41,076</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:41:41,076</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:41:41,076</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:42:11,128</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:42:11,128</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:42:11,128</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:42:13,172</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:42:13,172</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:42:13,172</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:42:43,243</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:42:43,243</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:42:43,243</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:42:45,295</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:42:45,295</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:42:45,299</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:43:15,342</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:43:15,342</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:43:15,342</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:43:17,392</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:43:17,392</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:43:17,393</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:43:47,434</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:43:47,434</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:43:47,434</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:43:49,470</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:43:49,470</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:43:49,471</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:44:19,541</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:44:19,541</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:44:19,541</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:44:21,590</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:44:21,590</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:44:21,590</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:44:51,629</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:44:51,629</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:44:51,629</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:44:53,653</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:44:53,654</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:44:53,653</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:45:23,729</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:45:23,728</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:45:23,728</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:45:25,785</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:45:25,785</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:45:25,785</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:45:55,832</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:45:55,831</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:45:55,832</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:45:57,869</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:45:57,869</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:45:57,875</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:46:27,939</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:46:27,939</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:46:27,939</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:46:29,978</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:46:29,980</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:46:29,995</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:47:00,043</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:47:00,043</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:47:00,043</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:47:02,095</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:47:02,095</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:47:02,095</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:47:32,139</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:47:32,139</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:47:32,139</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:47:34,189</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:47:34,189</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:47:34,189</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:48:04,229</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:48:04,229</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-19 21:48:04,229</td>
<td class="Message">Reconnecting, last destination was 127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:48:06,281</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:48:06,281</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-19 21:48:06,281</td>
<td class="Message">Cannot reconnect to [127.0.0.1:6379]: Connection refused: no further information: /127.0.0.1:6379</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">AbstractInternalLogger.java</td>
<td class="LineOfCaller">151</td>
</tr>
