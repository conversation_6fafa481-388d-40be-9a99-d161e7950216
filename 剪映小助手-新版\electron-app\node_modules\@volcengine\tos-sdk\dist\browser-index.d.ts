import { TosServerError, TosServerCode } from './TosServerError';
import { TosClientError } from './TosClientError';
import { isCancelError as isCancel } from './utils';
import { UploadEventType } from './methods/object/multipart/uploadFile';
import { ACLType, StorageClassType, MetadataDirectiveType, AzRedundancyType, PermissionType, GranteeType, CannedType, HttpMethodType, LifecycleStatusType, StatusType, RedirectType, StorageClassInheritDirectiveType, TierType, VersioningStatusType, ReplicationStatusType, AccessPointStatusType, TransferAccelerationStatusType, MRAPMirrorBackRedirectPolicyType } from './TosExportEnum';
import { CancelError } from './CancelError';
import { ResumableCopyEventType } from './methods/object/multipart/resumableCopyObject';
import { DownloadEventType } from './methods/object/downloadFile';
import { DataTransferType } from './interface';
import { ShareLinkClient } from './ShareLinkClient';
import { InnerClient } from './InnerClient';
import { createDefaultRateLimiter } from './universal/rate-limiter';
declare const CancelToken: import("axios").CancelTokenStatic;
declare class TosClient extends InnerClient {
    static TosServerError: typeof TosServerError;
    static isCancel: typeof isCancel;
    static CancelError: typeof CancelError;
    static TosServerCode: typeof TosServerCode;
    static TosClientError: typeof TosClientError;
    static CancelToken: import("axios").CancelTokenStatic;
    static ACLType: typeof ACLType;
    static StorageClassType: typeof StorageClassType;
    static MetadataDirectiveType: typeof MetadataDirectiveType;
    static AzRedundancyType: typeof AzRedundancyType;
    static PermissionType: typeof PermissionType;
    static GranteeType: typeof GranteeType;
    static CannedType: typeof CannedType;
    static HttpMethodType: typeof HttpMethodType;
    static LifecycleStatusType: typeof LifecycleStatusType;
    static StatusType: typeof StatusType;
    static RedirectType: typeof RedirectType;
    static StorageClassInheritDirectiveType: typeof StorageClassInheritDirectiveType;
    static TierType: typeof TierType;
    static VersioningStatusType: typeof VersioningStatusType;
    static createDefaultRateLimiter: (capacity: number, rate: number) => import("./interface").IRateLimiter;
    static DataTransferType: typeof DataTransferType;
    static UploadEventType: typeof UploadEventType;
    static DownloadEventType: typeof DownloadEventType;
    static ResumableCopyEventType: typeof ResumableCopyEventType;
    static ReplicationStatusType: typeof ReplicationStatusType;
    /** @private unstable */
    static AccessPointStatusType: typeof AccessPointStatusType;
    /** @private unstable */
    static TransferAccelerationStatusType: typeof TransferAccelerationStatusType;
    /** @private unstable */
    static MRAPMirrorBackRedirectPolicyType: typeof MRAPMirrorBackRedirectPolicyType;
    /** @private unstable */
    static ShareLinkClient: typeof ShareLinkClient;
}
export default TosClient;
export { TosClient as TOS, TosClient };
export { TosServerError, TosClientError, isCancel, CancelError, TosServerCode, CancelToken, ACLType, StorageClassType, MetadataDirectiveType, AzRedundancyType, PermissionType, GranteeType, CannedType, HttpMethodType, LifecycleStatusType, RedirectType, StatusType, StorageClassInheritDirectiveType, TierType, VersioningStatusType, createDefaultRateLimiter, DataTransferType, UploadEventType, DownloadEventType, ResumableCopyEventType, ReplicationStatusType, AccessPointStatusType, TransferAccelerationStatusType, ShareLinkClient, MRAPMirrorBackRedirectPolicyType };
