import TOSBase from '../../base';
export interface ListMultipartUploadsInput {
    bucket?: string;
    maxUploads?: number;
    keyMarker?: string;
    uploadIdMarker?: string;
    delimiter?: string;
    encodingType?: string;
    prefix?: string;
}
export interface ListMultipartUploadsOutput {
    Uploads: {
        Key: string;
        UploadId: string;
        StorageClass: string;
        Initiated: string;
    }[];
    CommonPrefixes: string[];
    Delimiter?: string;
    EncodingType?: string;
    KeyMarker?: string;
    NextKeyMarker: string;
    MaxUploads?: string;
    UploadIdMarker?: string;
    NextUploadIdMarker: string;
    Prefix?: string;
    IsTruncated: boolean;
    Bucket: string;
}
export declare function listMultipartUploads(this: TOSBase, input?: ListMultipartUploadsInput): Promise<import("../../base").TosResponse<ListMultipartUploadsOutput>>;
