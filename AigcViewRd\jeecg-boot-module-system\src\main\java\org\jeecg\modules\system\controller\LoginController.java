package org.jeecg.modules.system.controller;

import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson.JSONObject;
import com.aliyuncs.exceptions.ClientException;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.constant.CacheConstant;
import org.jeecg.common.constant.CommonConstant;
import org.jeecg.common.system.api.ISysBaseAPI;
import org.jeecg.common.system.util.JwtUtil;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.*;
import org.jeecg.common.util.encryption.EncryptedString;
import org.jeecg.modules.base.service.BaseCommonService;
import org.jeecg.modules.system.entity.SysDepart;
import org.jeecg.modules.system.entity.SysTenant;
import org.jeecg.modules.system.entity.SysUser;
import org.jeecg.modules.system.model.SysLoginModel;
import org.jeecg.modules.system.service.*;
import org.jeecg.modules.system.service.IAicgVerifyCodeService;
import org.jeecg.modules.system.service.UserCacheCleanupService;
import org.jeecg.modules.system.util.RandImageUtil;
import org.jeecg.modules.system.util.SingleLoginManager;
import org.jeecg.modules.system.util.RoleChecker;
import org.jeecg.modules.api.entity.AicgOnlineUsers;
import org.jeecg.modules.api.mapper.AicgOnlineUsersMapper;
import java.util.HashMap;
import java.util.Map;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR>
 * @since 2018-12-17
 */
@RestController
@RequestMapping("/sys")
@Api(tags="用户登录")
@Slf4j
public class LoginController {
	@Autowired
	private ISysUserService sysUserService;
	@Autowired
	private ISysBaseAPI sysBaseAPI;
	@Autowired
	private ISysLogService logService;
	@Autowired
    private RedisUtil redisUtil;
	@Autowired
    private ISysDepartService sysDepartService;
	@Autowired
	private ISysTenantService sysTenantService;
	@Autowired
    private ISysDictService sysDictService;
	@Resource
	private BaseCommonService baseCommonService;
	@Autowired
	private AicgOnlineUsersMapper onlineUsersMapper;
	@Autowired
	private IAicgVerifyCodeService verifyCodeService;
	@Autowired
	private SingleLoginManager singleLoginManager;
	@Autowired
	private RoleChecker roleChecker;
	@Autowired
	private UserCacheCleanupService userCacheCleanupService;
	@Autowired
	private org.jeecg.modules.system.util.LogoutCacheVerifier logoutCacheVerifier;
	@Autowired
	private org.jeecg.modules.api.service.IUserActivityBatchUpdateService userActivityBatchUpdateService;
	@Autowired
	private org.jeecg.modules.api.service.IUserActivityCacheService userActivityCacheService;
	@Autowired
	private org.jeecg.modules.api.config.UserActivityConfig userActivityConfig;

	private static final String BASE_CHECK_CODES = "qwertyuiplkjhgfdsazxcvbnmQWERTYUPLKJHGFDSAZXCVBNM1234567890";

	@ApiOperation("登录接口")
	@RequestMapping(value = "/login", method = RequestMethod.POST)
	public Result<JSONObject> login(@RequestBody SysLoginModel sysLoginModel, HttpServletRequest request){
		Result<JSONObject> result = new Result<JSONObject>();
		String username = sysLoginModel.getUsername();
		String password = sysLoginModel.getPassword();
		String loginType = sysLoginModel.getLoginType(); // 获取登录类型

		//前端密码加密，后端进行密码解密
		try {
			username = org.jeecg.common.util.encryption.AesEncryptUtil.desEncrypt(username.replaceAll("%2B", "\\+")).trim();
			password = org.jeecg.common.util.encryption.AesEncryptUtil.desEncrypt(password.replaceAll("%2B", "\\+")).trim();
			System.out.println("解密后用户名: " + username);
			System.out.println("解密后密码: " + password);
		} catch (Exception e) {
			System.out.println("解密失败: " + e.getMessage());
			result.error500("登录参数解密失败");
			return result;
		}

		//update-begin-author:taoyan date:20190828 for:校验验证码
        String captcha = sysLoginModel.getCaptcha();
        if(captcha==null){
            result.error500("验证码无效");
            return result;
        }

        String realKey = null; // 声明realKey变量

        // 特殊处理：自动登录验证码绕过机制
        if("AUTO_LOGIN_2025".equals(captcha)) {
            // 自动登录时跳过验证码验证
            System.out.println("检测到自动登录验证码，跳过验证码验证");
        } else {
            // 正常验证码验证流程
            String lowerCaseCaptcha = captcha.toLowerCase();
            realKey = MD5Util.MD5Encode(lowerCaseCaptcha+sysLoginModel.getCheckKey(), "utf-8");
            Object checkCode = redisUtil.get(realKey);
            //当进入登录页时，有一定几率出现验证码错误 #1714
            if(checkCode==null || !checkCode.toString().equals(lowerCaseCaptcha)) {
                result.error500("验证码错误");
                return result;
            }
        }
		//update-end-author:taoyan date:20190828 for:校验验证码
		
		//1. 校验用户是否有效
		//update-begin-author:wangshuai date:20200601 for: 登录代码验证用户是否注销bug，if条件永远为false
		// 临时修改：使用直接的Mapper查询替代LambdaQueryWrapper
		System.out.println("=== 登录调试信息 ===");
		System.out.println("用户名: " + username);
		SysUser sysUser = sysUserService.getUserByName(username);
		System.out.println("查询到的用户: " + (sysUser != null ? sysUser.getUsername() + " - " + sysUser.getRealname() : "null"));
		//update-end-author:wangshuai date:20200601 for: 登录代码验证用户是否注销bug，if条件永远为false
		result = sysUserService.checkUserIsEffective(sysUser);
		if(!result.isSuccess()) {
			return result;
		}
		
		//2. 校验用户名或密码是否正确
		String userpassword = PasswordUtil.encrypt(username, password, sysUser.getSalt());
		String syspassword = sysUser.getPassword();
		if (!syspassword.equals(userpassword)) {
			result.error500("用户名或密码错误");
			return result;
		}

		//3. 校验用户角色权限 - 只对后台管理登录进行admin角色检查
		if ("admin".equals(loginType)) {
			List<String> userRoles = sysUserService.getRole(username);
			boolean hasAdminRole = false;
			if (userRoles != null && !userRoles.isEmpty()) {
				for (String roleCode : userRoles) {
					if ("admin".equals(roleCode)) {
						hasAdminRole = true;
						break;
					}
				}
			}

			if (!hasAdminRole) {
				log.warn("非管理员用户尝试登录后台: " + username + ", 角色: " + userRoles);
				result.error500("ACCESS_DENIED");  // 返回特殊错误码，前端据此跳转404
				return result;
			}
			log.info("管理员用户登录后台: " + username);
		} else {
			log.info("官网用户登录: " + username + ", 登录类型: " + loginType);
		}

		// 转换登录参数
		JSONObject loginParams = new JSONObject();
		loginParams.put("loginType", sysLoginModel.getLoginType());
		loginParams.put("username", username);

		// 统一登录处理
		result = processLogin(sysUser, request, loginParams);

		// 登录成功后的处理
		if (result.isSuccess()) {
			//update-begin--Author:liusq  Date:20210126  for：登录成功，删除redis中的验证码
			if(realKey != null) {
				redisUtil.del(realKey);
			}
			//update-end--Author:liusq  Date:20210126  for：登录成功，删除redis中的验证码

			// 添加登录日志
			LoginUser loginUser = new LoginUser();
			BeanUtils.copyProperties(sysUser, loginUser);
			baseCommonService.addLog("用户名: " + username + ",登录成功！", CommonConstant.LOG_TYPE_1, null,loginUser);
		}
        //update-end--Author:wangshuai  Date:20200714  for：登录日志没有记录人员
		return result;
	}
	
	/**
	 * 退出登录（增强版）
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/logout")
	public Result<Object> logout(HttpServletRequest request,HttpServletResponse response) {
		//用户退出逻辑
	    String token = request.getHeader(CommonConstant.X_ACCESS_TOKEN);
	    if(oConvertUtils.isEmpty(token)) {
	    	return Result.error("退出登录失败！");
	    }
	    String username = JwtUtil.getUsername(token);
		LoginUser sysUser = sysBaseAPI.getUserByName(username);
	    if(sysUser!=null) {
			//update-begin--Author:wangshuai  Date:20200714  for：登出日志没有记录人员
			baseCommonService.addLog("用户名: "+sysUser.getRealname()+",退出成功！", CommonConstant.LOG_TYPE_1, null,sysUser);
			//update-end--Author:wangshuai  Date:20200714  for：登出日志没有记录人员
	    	log.info(" 用户名:  "+sysUser.getRealname()+",退出成功！ ");

	    	// 🆕 记录用户登出状态（集成新的用户活跃状态追踪系统）
	    	recordUserLogout(sysUser.getId(), token, request, "USER_LOGOUT");

	    	// 🆕 使用统一的缓存清理服务，确保完整清理所有相关缓存
	    	userCacheCleanupService.cleanupUserLoginCache(sysUser.getId(), token, sysUser.getUsername());

			// 设置用户离线状态
			onlineUsersMapper.setUserOffline(sysUser.getId());
			// 调用shiro的logout
			SecurityUtils.getSubject().logout();
	    	return Result.ok("退出登录成功！");
	    }else {
	    	return Result.error("Token无效!");
	    }
	}

	/**
	 * 🆕 验证用户退出登录后的缓存清理情况（测试接口 - 仅限管理员）
	 * @param userId 用户ID
	 * @param username 用户名
	 * @return 验证结果
	 */
	@ApiOperation("验证退出登录缓存清理")
	@RequestMapping(value = "/verifyLogoutCache", method = RequestMethod.GET)
	public Result<Object> verifyLogoutCache(@RequestParam String userId, @RequestParam String username, HttpServletRequest request) {
		try {
			// 🆕 安全检查：只允许管理员或用户本人查询
			String token = request.getHeader(CommonConstant.X_ACCESS_TOKEN);
			if (token == null || token.trim().isEmpty()) {
				return Result.error("未授权访问，请先登录");
			}

			String currentUsername = JwtUtil.getUsername(token);
			LoginUser currentUser = sysBaseAPI.getUserByName(currentUsername);

			if (currentUser == null) {
				return Result.error("用户信息无效");
			}

			// 检查是否为管理员或用户本人
			boolean isAdmin = roleChecker.checkUserIsAdmin(currentUser.getId());
			boolean isSelfQuery = currentUsername.equals(username);

			if (!isAdmin && !isSelfQuery) {
				return Result.error("权限不足，只能查询自己的缓存状态");
			}

			org.jeecg.modules.system.util.LogoutCacheVerifier.CacheVerificationResult result =
				logoutCacheVerifier.verifyUserLogoutCache(userId, username);
			Result<Object> successResult = new Result<>();
			successResult.setSuccess(true);
			successResult.setResult(result);
			return successResult;
		} catch (Exception e) {
			log.error("验证退出登录缓存失败", e);
			return Result.error("验证失败: " + e.getMessage());
		}
	}

	/**
	 * 获取访问量
	 * @return
	 */
	@GetMapping("loginfo")
	public Result<JSONObject> loginfo() {
		Result<JSONObject> result = new Result<JSONObject>();
		JSONObject obj = new JSONObject();
		//update-begin--Author:zhangweijian  Date:20190428 for：传入开始时间，结束时间参数
		// 获取一天的开始和结束时间
		Calendar calendar = new GregorianCalendar();
		calendar.set(Calendar.HOUR_OF_DAY, 0);
		calendar.set(Calendar.MINUTE, 0);
		calendar.set(Calendar.SECOND, 0);
		calendar.set(Calendar.MILLISECOND, 0);
		Date dayStart = calendar.getTime();
		calendar.add(Calendar.DATE, 1);
		Date dayEnd = calendar.getTime();
		// 获取系统访问记录
		Long totalVisitCount = logService.findTotalVisitCount();
		obj.put("totalVisitCount", totalVisitCount);
		Long todayVisitCount = logService.findTodayVisitCount(dayStart,dayEnd);
		obj.put("todayVisitCount", todayVisitCount);
		Long todayIp = logService.findTodayIp(dayStart,dayEnd);
		//update-end--Author:zhangweijian  Date:20190428 for：传入开始时间，结束时间参数
		obj.put("todayIp", todayIp);
		result.setResult(obj);
		result.success("登录成功");
		return result;
	}
	
	/**
	 * 获取访问量
	 * @return
	 */
	@GetMapping("visitInfo")
	public Result<List<Map<String,Object>>> visitInfo() {
		Result<List<Map<String,Object>>> result = new Result<List<Map<String,Object>>>();
		Calendar calendar = new GregorianCalendar();
		calendar.set(Calendar.HOUR_OF_DAY,0);
        calendar.set(Calendar.MINUTE,0);
        calendar.set(Calendar.SECOND,0);
        calendar.set(Calendar.MILLISECOND,0);
        calendar.add(Calendar.DAY_OF_MONTH, 1);
        Date dayEnd = calendar.getTime();
        calendar.add(Calendar.DAY_OF_MONTH, -7);
        Date dayStart = calendar.getTime();
        List<Map<String,Object>> list = logService.findVisitCount(dayStart, dayEnd);
		result.setResult(oConvertUtils.toLowerCasePageList(list));
		return result;
	}
	
	
	/**
	 * 登陆成功选择用户当前部门
	 * @param user
	 * @return
	 */
	@RequestMapping(value = "/selectDepart", method = RequestMethod.PUT)
	public Result<JSONObject> selectDepart(@RequestBody SysUser user) {
		Result<JSONObject> result = new Result<JSONObject>();
		String username = user.getUsername();
		if(oConvertUtils.isEmpty(username)) {
			LoginUser sysUser = (LoginUser)SecurityUtils.getSubject().getPrincipal();
			username = sysUser.getUsername();
		}
		String orgCode= user.getOrgCode();
		this.sysUserService.updateUserDepart(username, orgCode);
		SysUser sysUser = sysUserService.getUserByName(username);
		JSONObject obj = new JSONObject();
		obj.put("userInfo", sysUser);
		result.setResult(obj);
		return result;
	}

	/**
	 * 短信登录接口
	 * 
	 * @param jsonObject
	 * @return
	 */
	@PostMapping(value = "/sms")
	public Result<String> sms(@RequestBody JSONObject jsonObject) {
		Result<String> result = new Result<String>();
		String mobile = jsonObject.get("mobile").toString();
		//手机号模式 登录模式: "2"  注册模式: "1"
		String smsmode=jsonObject.get("smsmode").toString();
		log.info(mobile);
		if(oConvertUtils.isEmpty(mobile)){
			result.setMessage("手机号不允许为空！");
			result.setSuccess(false);
			return result;
		}
		Object object = redisUtil.get(mobile);
		if (object != null) {
			result.setMessage("验证码10分钟内，仍然有效！");
			result.setSuccess(false);
			return result;
		}

		//随机数
		String captcha = RandomUtil.randomNumbers(6);
		JSONObject obj = new JSONObject();
    	obj.put("code", captcha);
		try {
			boolean b = false;
			//注册模板
			if (CommonConstant.SMS_TPL_TYPE_1.equals(smsmode)) {
				SysUser sysUser = sysUserService.getUserByPhone(mobile);
				if(sysUser!=null) {
					result.error500(" 手机号已经注册，请直接登录！");
					baseCommonService.addLog("手机号已经注册，请直接登录！", CommonConstant.LOG_TYPE_1, null);
					return result;
				}
				b = DySmsHelper.sendSms(mobile, obj, DySmsEnum.REGISTER_TEMPLATE_CODE);
			}else {
				//登录模式，校验用户有效性
				SysUser sysUser = sysUserService.getUserByPhone(mobile);
				result = sysUserService.checkUserIsEffective(sysUser);
				if(!result.isSuccess()) {
					String message = result.getMessage();
					if("该用户不存在，请注册".equals(message)){
						result.error500("该用户不存在或未绑定手机号");
					}
					return result;
				}
				
				/**
				 * smsmode 短信模板方式  0 .登录模板、1.注册模板、2.忘记密码模板
				 */
				if (CommonConstant.SMS_TPL_TYPE_0.equals(smsmode)) {
					//登录模板
					b = DySmsHelper.sendSms(mobile, obj, DySmsEnum.LOGIN_TEMPLATE_CODE);
				} else if(CommonConstant.SMS_TPL_TYPE_2.equals(smsmode)) {
					//忘记密码模板
					b = DySmsHelper.sendSms(mobile, obj, DySmsEnum.FORGET_PASSWORD_TEMPLATE_CODE);
				}
			}

			if (b == false) {
				result.setMessage("短信验证码发送失败,请稍后重试");
				result.setSuccess(false);
				return result;
			}
			//验证码10分钟内有效
			redisUtil.set(mobile, captcha, 600);
			//update-begin--Author:scott  Date:20190812 for：issues#391
			//result.setResult(captcha);
			//update-end--Author:scott  Date:20190812 for：issues#391
			result.setSuccess(true);

		} catch (ClientException e) {
			e.printStackTrace();
			result.error500(" 短信接口未配置，请联系管理员！");
			return result;
		}
		return result;
	}
	

	/**
	 * 手机号登录接口
	 *
	 * @param jsonObject
	 * @return
	 */
	@ApiOperation("手机号登录接口")
	@PostMapping("/phoneLogin")
	public Result<JSONObject> phoneLogin(@RequestBody JSONObject jsonObject, HttpServletRequest request) {
		Result<JSONObject> result = new Result<JSONObject>();
		String phone = jsonObject.getString("mobile");
		String smsCode = jsonObject.getString("captcha");
		String loginType = jsonObject.getString("loginType"); // 获取登录类型

		// 1. 验证参数
		if (oConvertUtils.isEmpty(phone) || oConvertUtils.isEmpty(smsCode)) {
			result.error500("手机号和验证码不能为空");
			return result;
		}

		// 2. 验证短信验证码（强制登录时跳过验证码验证）
		boolean isForceLogin = "force".equals(loginType);
		if (!isForceLogin) {
			boolean codeValid = verifyCodeService.verifyCode(phone, smsCode, "sms", "login");
			if (!codeValid) {
				result.error500("验证码错误或已过期");
				return result;
			}
		} else {
			log.info("强制登录，跳过短信验证码验证：{}", phone);
		}

		// 3. 校验用户有效性
		SysUser sysUser = sysUserService.getUserByPhone(phone);
		result = sysUserService.checkUserIsEffective(sysUser);
		if(!result.isSuccess()) {
			return result;
		}

		// 4. 校验用户角色权限 - 只对后台管理登录进行admin角色检查
		if ("admin".equals(loginType)) {
			List<String> userRoles = sysUserService.getRole(sysUser.getUsername());
			boolean hasAdminRole = false;
			if (userRoles != null && !userRoles.isEmpty()) {
				for (String roleCode : userRoles) {
					if ("admin".equals(roleCode)) {
						hasAdminRole = true;
						break;
					}
				}
			}

			if (!hasAdminRole) {
				log.warn("非管理员用户尝试通过手机号登录后台: " + phone + ", 角色: " + userRoles);
				result.error500("ACCESS_DENIED");  // 返回特殊错误码，前端据此跳转404
				return result;
			}
			log.info("管理员用户通过手机号登录后台: " + phone);
		} else {
			log.info("官网用户通过手机号登录: " + phone + ", 登录类型: " + loginType);
		}

		// 5. 转换登录参数
		JSONObject loginParams = new JSONObject();
		loginParams.put("loginType", loginType);
		loginParams.put("phone", phone);

		// 6. 统一登录处理
		result = processLogin(sysUser, request, loginParams);

		// 7. 添加登录日志（只有登录成功才记录）
		if (result.isSuccess()) {
			LoginUser loginUser = new LoginUser();
			BeanUtils.copyProperties(sysUser, loginUser);
			baseCommonService.addLog("用户手机号: " + phone + ",登录成功！", CommonConstant.LOG_TYPE_1, null, loginUser);
		}

		return result;
	}

	/**
	 * 邮箱登录接口
	 *
	 * @param jsonObject
	 * @return
	 */
	@ApiOperation("邮箱登录接口")
	@PostMapping("/emailLogin")
	public Result<JSONObject> emailLogin(@RequestBody JSONObject jsonObject, HttpServletRequest request) {
		Result<JSONObject> result = new Result<JSONObject>();
		String email = jsonObject.getString("email");
		String emailCode = jsonObject.getString("emailCode");
		String loginType = jsonObject.getString("loginType"); // 获取登录类型

		// 1. 验证参数
		if (oConvertUtils.isEmpty(email) || oConvertUtils.isEmpty(emailCode)) {
			result.error500("邮箱和验证码不能为空");
			return result;
		}

		// 2. 验证邮箱验证码（强制登录时跳过验证码验证）
		boolean isForceLogin = "force".equals(loginType);
		if (!isForceLogin) {
			boolean codeValid = verifyCodeService.verifyCode(email, emailCode, "email", "login");
			if (!codeValid) {
				result.error500("验证码错误或已过期");
				return result;
			}
		} else {
			log.info("强制登录，跳过邮箱验证码验证：{}", email);
		}

		// 3. 校验用户有效性
		SysUser sysUser = sysUserService.getUserByEmail(email);
		result = sysUserService.checkUserIsEffective(sysUser);
		if(!result.isSuccess()) {
			return result;
		}

		// 4. 校验用户角色权限 - 只对后台管理登录进行admin角色检查
		if ("admin".equals(loginType)) {
			List<String> userRoles = sysUserService.getRole(sysUser.getUsername());
			boolean hasAdminRole = false;
			if (userRoles != null && !userRoles.isEmpty()) {
				for (String roleCode : userRoles) {
					if ("admin".equals(roleCode)) {
						hasAdminRole = true;
						break;
					}
				}
			}

			if (!hasAdminRole) {
				log.warn("非管理员用户尝试通过邮箱登录后台: " + email + ", 角色: " + userRoles);
				result.error500("ACCESS_DENIED");  // 返回特殊错误码，前端据此跳转404
				return result;
			}
			log.info("管理员用户通过邮箱登录后台: " + email);
		} else {
			log.info("官网用户通过邮箱登录: " + email + ", 登录类型: " + loginType);
		}

		// 5. 转换登录参数
		JSONObject loginParams = new JSONObject();
		loginParams.put("loginType", loginType);
		loginParams.put("email", email);

		// 6. 统一登录处理
		result = processLogin(sysUser, request, loginParams);

		// 7. 添加登录日志（只有登录成功才记录）
		if (result.isSuccess()) {
			LoginUser loginUser = new LoginUser();
			BeanUtils.copyProperties(sysUser, loginUser);
			baseCommonService.addLog("用户邮箱: " + email + ",登录成功！", CommonConstant.LOG_TYPE_1, null, loginUser);
		}

		return result;
	}


	/**
	 * 用户信息
	 *
	 * @param sysUser
	 * @param result
	 * @param request
	 * @return
	 */
	private Result<JSONObject> userInfo(SysUser sysUser, Result<JSONObject> result, HttpServletRequest request) {
		String syspassword = sysUser.getPassword();
		String username = sysUser.getUsername();
		// 获取用户部门信息
		JSONObject obj = new JSONObject();
		List<SysDepart> departs = sysDepartService.queryUserDeparts(sysUser.getId());
		obj.put("departs", departs);
		if (departs == null || departs.size() == 0) {
			obj.put("multi_depart", 0);
		} else if (departs.size() == 1) {
			sysUserService.updateUserDepart(username, departs.get(0).getOrgCode());
			obj.put("multi_depart", 1);
		} else {
			//查询当前是否有登录部门
			// update-begin--Author:wangshuai Date:20200805 for：如果用戶为选择部门，数据库为存在上一次登录部门，则取一条存进去
			SysUser sysUserById = sysUserService.getById(sysUser.getId());
			if(oConvertUtils.isEmpty(sysUserById.getOrgCode())){
				sysUserService.updateUserDepart(username, departs.get(0).getOrgCode());
			}
			// update-end--Author:wangshuai Date:20200805 for：如果用戶为选择部门，数据库为存在上一次登录部门，则取一条存进去
			obj.put("multi_depart", 2);
		}
		// update-begin--Author:sunjianlei Date:20210802 for：获取用户租户信息
		String tenantIds = sysUser.getRelTenantIds();
		if (oConvertUtils.isNotEmpty(tenantIds)) {
			List<String> tenantIdList = Arrays.asList(tenantIds.split(","));
			// 该方法仅查询有效的租户，如果返回0个就说明所有的租户均无效。
			List<SysTenant> tenantList = sysTenantService.queryEffectiveTenant(tenantIdList);
			if (tenantList.size() == 0) {
				result.error500("与该用户关联的租户均已被冻结，无法登录！");
				return result;
			} else {
				obj.put("tenantList", tenantList);
			}
		}
		// update-end--Author:sunjianlei Date:20210802 for：获取用户租户信息
		// 生成token
		String token = JwtUtil.sign(username, syspassword);

		// 设置token缓存有效时间
		redisUtil.set(CommonConstant.PREFIX_USER_TOKEN + token, token);
		redisUtil.expire(CommonConstant.PREFIX_USER_TOKEN + token, JwtUtil.EXPIRE_TIME * 2 / 1000);

		obj.put("token", token);
		obj.put("userInfo", sysUser);
		obj.put("sysAllDictItems", sysDictService.queryAllDictItems());

		// 记录用户在线状态
		recordUserOnline(sysUser.getId(), token, request);

		result.setResult(obj);
		result.success("登录成功");
		return result;
	}

	/**
	 * 获取加密字符串
	 * @return
	 */
	@GetMapping(value = "/getEncryptedString")
	public Result<Map<String,String>> getEncryptedString(){
		Result<Map<String,String>> result = new Result<Map<String,String>>();
		Map<String,String> map = new HashMap<String,String>();
		map.put("key", EncryptedString.key);
		map.put("iv",EncryptedString.iv);
		result.setResult(map);
		return result;
	}

	/**
	 * 后台生成图形验证码 ：有效
	 * @param response
	 * @param key
	 */
	@ApiOperation("获取验证码")
	@GetMapping(value = "/randomImage/{key}")
	public Result<String> randomImage(HttpServletResponse response,@PathVariable String key){
		Result<String> res = new Result<String>();
		try {
			String code = RandomUtil.randomString(BASE_CHECK_CODES,4);
			String lowerCaseCode = code.toLowerCase();
			String realKey = MD5Util.MD5Encode(lowerCaseCode+key, "utf-8");
			redisUtil.set(realKey, lowerCaseCode, 60);
			String base64 = RandImageUtil.generate(code);
			res.setSuccess(true);
			res.setResult(base64);
		} catch (Exception e) {
			res.error500("获取验证码出错"+e.getMessage());
			e.printStackTrace();
		}
		return res;
	}
	
	/**
	 * app登录
	 * @param sysLoginModel
	 * @return
	 * @throws Exception
	 */
	@RequestMapping(value = "/mLogin", method = RequestMethod.POST)
	public Result<JSONObject> mLogin(@RequestBody SysLoginModel sysLoginModel, HttpServletRequest request) throws Exception {
		Result<JSONObject> result = new Result<JSONObject>();
		String username = sysLoginModel.getUsername();
		String password = sysLoginModel.getPassword();
		
		//1. 校验用户是否有效
		SysUser sysUser = sysUserService.getUserByName(username);
		result = sysUserService.checkUserIsEffective(sysUser);
		if(!result.isSuccess()) {
			return result;
		}
		
		//2. 校验用户名或密码是否正确
		String userpassword = PasswordUtil.encrypt(username, password, sysUser.getSalt());
		String syspassword = sysUser.getPassword();
		if (!syspassword.equals(userpassword)) {
			result.error500("用户名或密码错误");
			return result;
		}
		
		String orgCode = sysUser.getOrgCode();
		if(oConvertUtils.isEmpty(orgCode)) {
			//如果当前用户无选择部门 查看部门关联信息
			List<SysDepart> departs = sysDepartService.queryUserDeparts(sysUser.getId());
			if (departs == null || departs.size() == 0) {
				result.error500("用户暂未归属部门,不可登录!");
				return result;
			}
			orgCode = departs.get(0).getOrgCode();
			sysUser.setOrgCode(orgCode);
			this.sysUserService.updateUserDepart(username, orgCode);
		}
		// 转换登录参数
		JSONObject loginParams = new JSONObject();
		loginParams.put("loginType", "mobile");
		loginParams.put("username", username);

		// 统一登录处理
		result = processLogin(sysUser, request, loginParams);

		// 添加登录日志（只有登录成功才记录）
		if (result.isSuccess()) {
			baseCommonService.addLog("用户名: " + username + ",登录成功[移动端]！", CommonConstant.LOG_TYPE_1, null);
		}

		return result;
	}

	/**
	 * 图形验证码
	 * @param sysLoginModel
	 * @return
	 */
	@RequestMapping(value = "/checkCaptcha", method = RequestMethod.POST)
	public Result<?> checkCaptcha(@RequestBody SysLoginModel sysLoginModel){
		String captcha = sysLoginModel.getCaptcha();
		String checkKey = sysLoginModel.getCheckKey();
		if(captcha==null){
			return Result.error("验证码无效");
		}
		String lowerCaseCaptcha = captcha.toLowerCase();
		String realKey = MD5Util.MD5Encode(lowerCaseCaptcha+checkKey, "utf-8");
		Object checkCode = redisUtil.get(realKey);
		if(checkCode==null || !checkCode.equals(lowerCaseCaptcha)) {
			return Result.error("验证码错误");
		}
		return Result.ok();
	}



	/**
	 * 通用登录处理方法（统一入口）
	 * @param sysUser 用户信息
	 * @param request 请求对象
	 * @param loginParams 登录参数
	 * @return 登录结果
	 */
	private Result<JSONObject> processLogin(SysUser sysUser, HttpServletRequest request, JSONObject loginParams) {
		Result<JSONObject> result = new Result<JSONObject>();

		try {
			log.info("=== 开始统一登录处理 ===");
			log.info("用户：{}，登录类型：{}", sysUser.getUsername(), loginParams.getString("loginType"));

			// 1. 检查用户角色
			boolean isAdmin = roleChecker.checkUserIsAdmin(sysUser.getId());
			boolean forceLogin = "force".equals(loginParams.getString("loginType"));

			// 2. 非admin用户进行冲突检查
			if (!isAdmin) {
				String currentTokenKey = "current_user_token_" + sysUser.getId();
				Object existingTokenObj = redisUtil.get(currentTokenKey);

				if (existingTokenObj != null && !forceLogin) {
					// 有冲突且非强制登录，返回冲突信息
					log.info("用户 {} 存在登录冲突，返回冲突确认", sysUser.getUsername());
					JSONObject conflictInfo = buildConflictInfo(sysUser.getId(), String.valueOf(existingTokenObj), request);
					result.setCode(4002);
					result.setMessage("检测到其他设备登录");
					result.setSuccess(false);
					result.setResult(conflictInfo);
					return result;
				}
			} else {
				log.info("admin用户，允许多设备登录：{}", sysUser.getUsername());
			}

			// 3. 正常登录流程
			userInfo(sysUser, result, request);

			// 4. 🆕 统一处理单设备登录逻辑（包括强制登录）
			if (result.isSuccess() && !isAdmin) {
				JSONObject resultObj = result.getResult();
				String newToken = (String) resultObj.get("token");
				if (newToken != null) {
					// 🆕 使用SingleLoginManager统一处理，传递强制登录标识
					singleLoginManager.handleSingleLogin(sysUser, newToken, request, forceLogin);
					log.info("已通过SingleLoginManager处理用户登录状态：{}，强制登录：{}", sysUser.getUsername(), forceLogin);
				}
			}

			// 5. 记录设备信息
			if (request != null) {
				recordDeviceInfo(sysUser.getId(), request);
			}

			return result;

		} catch (Exception e) {
			log.error("登录处理失败，用户：{}，错误：{}", sysUser.getUsername(), e.getMessage(), e);
			result.error500("登录处理失败");
			return result;
		}
	}

	/**
	 * 构建冲突信息
	 * @param userId 用户ID
	 * @param existingToken 已存在的Token
	 * @param newRequest 新的登录请求
	 * @return 冲突详情
	 */
	private JSONObject buildConflictInfo(String userId, String existingToken, HttpServletRequest newRequest) {
		JSONObject conflictInfo = new JSONObject();

		try {
			// 获取已登录设备信息
			String existingDeviceKey = "token_device_" + userId;
			Object existingDeviceObj = redisUtil.get(existingDeviceKey);

			if (existingDeviceObj != null) {
				JSONObject existingDevice = JSONObject.parseObject(String.valueOf(existingDeviceObj));
				conflictInfo.put("deviceInfo", existingDevice.getString("deviceInfo"));
				conflictInfo.put("ipAddress", existingDevice.getString("ipAddress"));
				conflictInfo.put("loginTime", existingDevice.getString("loginTime"));
			} else {
				conflictInfo.put("deviceInfo", "未知设备");
				conflictInfo.put("ipAddress", "未知IP");
				conflictInfo.put("loginTime", "未知时间");
			}

			// 新设备信息
			if (newRequest != null) {
				conflictInfo.put("newDeviceInfo", parseDeviceInfo(newRequest));
				conflictInfo.put("newIpAddress", IPUtils.getIpAddr(newRequest));
			}

			conflictInfo.put("message", "检测到您的账号已在其他设备登录，继续登录将自动下线其他设备");

		} catch (Exception e) {
			log.error("构建冲突信息失败", e);
			conflictInfo.put("deviceInfo", "未知设备");
			conflictInfo.put("ipAddress", "未知IP");
			conflictInfo.put("loginTime", "未知时间");
			conflictInfo.put("message", "检测到账号冲突");
		}

		return conflictInfo;
	}

	/**
	 * 记录设备信息
	 * @param userId 用户ID
	 * @param request 请求对象
	 */
	private void recordDeviceInfo(String userId, HttpServletRequest request) {
		try {
			String deviceKey = "token_device_" + userId;
			JSONObject deviceInfo = new JSONObject();
			deviceInfo.put("deviceInfo", parseDeviceInfo(request));
			deviceInfo.put("ipAddress", IPUtils.getIpAddr(request));
			deviceInfo.put("loginTime", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));

			redisUtil.set(deviceKey, deviceInfo.toJSONString());
			redisUtil.expire(deviceKey, 24 * 60 * 60); // 24小时过期

		} catch (Exception e) {
			log.error("记录设备信息失败", e);
		}
	}

	/**
	 * 解析设备信息
	 * @param request 请求对象
	 * @return 设备信息字符串
	 */
	private String parseDeviceInfo(HttpServletRequest request) {
		if (request == null) {
			return "未知设备";
		}

		String userAgent = request.getHeader("User-Agent");

		// 简单解析浏览器和操作系统
		String browser = "未知浏览器";
		String os = "未知系统";

		if (userAgent != null) {
			if (userAgent.contains("Chrome")) browser = "Chrome";
			else if (userAgent.contains("Firefox")) browser = "Firefox";
			else if (userAgent.contains("Safari")) browser = "Safari";
			else if (userAgent.contains("Edge")) browser = "Edge";

			if (userAgent.contains("Windows")) os = "Windows";
			else if (userAgent.contains("Mac")) os = "Mac";
			else if (userAgent.contains("Linux")) os = "Linux";
			else if (userAgent.contains("Android")) os = "Android";
			else if (userAgent.contains("iPhone")) os = "iPhone";
		}

		return String.format("%s浏览器 (%s)", browser, os);
	}

	/**
	 * 记录用户在线状态（增强版）
	 * @param userId 用户ID
	 * @param sessionId 会话ID
	 * @param request HTTP请求对象
	 */
	private void recordUserOnline(String userId, String sessionId, HttpServletRequest request) {
		try {
			log.info("开始记录用户在线状态 - 用户ID: {}, 会话ID: {}", userId, sessionId);

			// 先设置该用户的其他会话为离线
			int offlineCount = onlineUsersMapper.setUserOffline(userId);
			log.info("设置用户 {} 的其他会话为离线，影响行数: {}", userId, offlineCount);

			// 获取详细的登录信息
			String ipAddress = IPUtils.getIpAddr(request);
			String userAgent = request.getHeader("User-Agent");

			// 创建新的在线记录
			AicgOnlineUsers onlineUser = new AicgOnlineUsers();
			onlineUser.setUserId(userId);
			onlineUser.setSessionId(sessionId);
			onlineUser.setLoginTime(new Date());
			onlineUser.setLastActiveTime(new Date());
			onlineUser.setStatus(true);
			onlineUser.setCreateTime(new Date());
			onlineUser.setUpdateTime(new Date());
			onlineUser.setIpAddress(ipAddress);
			onlineUser.setUserAgent(userAgent);

			log.info("准备插入在线用户记录: {}", onlineUser);

			// 插入在线用户记录
			int insertResult = onlineUsersMapper.insert(onlineUser);

			if (insertResult > 0) {
				log.info("用户 {} 在线状态记录成功，插入行数: {}, 生成ID: {}", userId, insertResult, onlineUser.getId());

				// 🆕 集成新的用户活跃状态追踪系统
				integrateUserActivityTracking(userId, sessionId, ipAddress, userAgent, onlineUser);
			} else {
				log.warn("用户 {} 在线状态记录失败，插入行数为0", userId);
			}
		} catch (Exception e) {
			log.error("记录用户在线状态失败 - 用户ID: {}, 错误信息: {}", userId, e.getMessage(), e);
		}
	}

	/**
	 * 集成用户活跃状态追踪系统
	 * @param userId 用户ID
	 * @param sessionId 会话ID
	 * @param ipAddress IP地址
	 * @param userAgent 用户代理
	 * @param onlineUser 在线用户对象
	 */
	private void integrateUserActivityTracking(String userId, String sessionId, String ipAddress, String userAgent, AicgOnlineUsers onlineUser) {
		try {
			// 检查是否启用用户活跃状态追踪
			if (!userActivityConfig.getEnabled()) {
				log.debug("用户活跃状态追踪已禁用，跳过集成");
				return;
			}

			// 🆕 添加到批量更新队列
			boolean addedToQueue = userActivityBatchUpdateService.addActivityUpdate(
				userId, sessionId, "/login", ipAddress, userAgent);

			if (addedToQueue) {
				log.info("用户登录活跃状态已添加到批量更新队列 - 用户ID: {}", userId);
			} else {
				log.warn("用户登录活跃状态添加到批量更新队列失败 - 用户ID: {}", userId);
			}

			// 🆕 缓存用户活跃状态
			boolean cached = userActivityCacheService.cacheUserActivity(userId, onlineUser);
			if (cached) {
				log.debug("用户活跃状态已缓存 - 用户ID: {}", userId);
			} else {
				log.warn("用户活跃状态缓存失败 - 用户ID: {}", userId);
			}

			// 🆕 缓存会话信息
			Map<String, Object> sessionInfo = new HashMap<>();
			sessionInfo.put("loginTime", onlineUser.getLoginTime());
			sessionInfo.put("ipAddress", ipAddress);
			sessionInfo.put("userAgent", userAgent);
			sessionInfo.put("loginType", "LOGIN");

			boolean sessionCached = userActivityCacheService.cacheUserSession(sessionId, userId, sessionInfo);
			if (sessionCached) {
				log.debug("会话信息已缓存 - 会话ID: {}", sessionId);
			} else {
				log.warn("会话信息缓存失败 - 会话ID: {}", sessionId);
			}

		} catch (Exception e) {
			log.error("集成用户活跃状态追踪系统失败 - 用户ID: {}, 错误: {}", userId, e.getMessage(), e);
		}
	}

	/**
	 * 记录用户登出状态（增强版）
	 * @param userId 用户ID
	 * @param sessionId 会话ID
	 * @param request HTTP请求对象
	 * @param logoutReason 登出原因
	 */
	private void recordUserLogout(String userId, String sessionId, HttpServletRequest request, String logoutReason) {
		try {
			log.info("开始记录用户登出状态 - 用户ID: {}, 会话ID: {}, 原因: {}", userId, sessionId, logoutReason);

			// 获取详细的登出信息
			String ipAddress = IPUtils.getIpAddr(request);
			String userAgent = request.getHeader("User-Agent");

			// 🆕 集成新的用户活跃状态追踪系统
			integrateUserLogoutTracking(userId, sessionId, ipAddress, userAgent, logoutReason);

		} catch (Exception e) {
			log.error("记录用户登出状态失败 - 用户ID: {}, 错误信息: {}", userId, e.getMessage(), e);
		}
	}

	/**
	 * 集成用户登出状态追踪系统
	 * @param userId 用户ID
	 * @param sessionId 会话ID
	 * @param ipAddress IP地址
	 * @param userAgent 用户代理
	 * @param logoutReason 登出原因
	 */
	private void integrateUserLogoutTracking(String userId, String sessionId, String ipAddress, String userAgent, String logoutReason) {
		try {
			// 检查是否启用用户活跃状态追踪
			if (!userActivityConfig.getEnabled()) {
				log.debug("用户活跃状态追踪已禁用，跳过登出集成");
				return;
			}

			// 🆕 添加到批量更新队列
			boolean addedToQueue = userActivityBatchUpdateService.addLogoutUpdate(userId, sessionId);

			if (addedToQueue) {
				log.info("用户登出状态已添加到批量更新队列 - 用户ID: {}", userId);
			} else {
				log.warn("用户登出状态添加到批量更新队列失败 - 用户ID: {}", userId);
			}

			// 🆕 清理用户活跃状态缓存
			int cacheCleared = userActivityCacheService.clearUserCache(userId);
			if (cacheCleared > 0) {
				log.debug("用户活跃状态缓存已清理 - 用户ID: {}, 清理数量: {}", userId, cacheCleared);
			} else {
				log.warn("用户活跃状态缓存清理失败 - 用户ID: {}", userId);
			}

			// 🆕 清理会话信息缓存
			boolean sessionCleared = userActivityCacheService.removeSessionCache(sessionId);
			if (sessionCleared) {
				log.debug("会话信息缓存已清理 - 会话ID: {}", sessionId);
			} else {
				log.warn("会话信息缓存清理失败 - 会话ID: {}", sessionId);
			}

			// 🆕 记录登出详细信息到缓存（用于统计分析）
			Map<String, Object> logoutInfo = new HashMap<>();
			logoutInfo.put("logoutTime", new Date());
			logoutInfo.put("ipAddress", ipAddress);
			logoutInfo.put("userAgent", userAgent);
			logoutInfo.put("logoutReason", logoutReason);
			logoutInfo.put("logoutType", "LOGOUT");

			// 使用统计信息缓存方法记录登出信息
			String logoutStatsKey = "logout_info_" + userId + "_" + System.currentTimeMillis();
			boolean logoutInfoCached = userActivityCacheService.cacheOnlineStats(logoutStatsKey, logoutInfo, 3600); // 缓存1小时
			if (logoutInfoCached) {
				log.debug("登出信息已缓存用于统计分析 - 用户ID: {}, 缓存键: {}", userId, logoutStatsKey);
			} else {
				log.warn("登出信息缓存失败 - 用户ID: {}", userId);
			}

		} catch (Exception e) {
			log.error("集成用户登出状态追踪系统失败 - 用户ID: {}, 错误: {}", userId, e.getMessage(), e);
		}
	}

}