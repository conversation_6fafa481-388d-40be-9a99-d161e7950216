package org.jeecg.modules.jianying.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

/**
 * 添加关键帧请求参数
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AddKeyframesRequest extends BaseJianyingRequest {
    
    @ApiModelProperty(value = "草稿地址，使用create_draft输出的draft_url即可（必填）", required = true,
                     example = "https://example.com/draft/123")
    @NotBlank(message = "draft_url不能为空")
    @JsonProperty("draft_url")
    private String zjDraftUrl;

    @ApiModelProperty(value = "关键帧数据（必填）", required = true,
                     example = "[{\"offset\": 5000000, \"property\": \"KFTypePositionX\", \"segment_id\": \"acc5b516-046b-4eae-a179-f686f35e70a8\", \"value\": 0}]")
    @NotBlank(message = "keyframes不能为空")
    @JsonProperty("keyframes")
    private String zjKeyframes;
    
    @Override
    public String getSummary() {
        return "AddKeyframesRequest{" +
               "accessKey=" + (getAccessKey() != null && getAccessKey().length() > 10 ? 
                              getAccessKey().substring(0, 10) + "***" : "null") +
               ", draftUrl=" + (zjDraftUrl != null && zjDraftUrl.length() > 30 ? 
                               zjDraftUrl.substring(0, 30) + "***" : zjDraftUrl) +
               "}";
    }
}
