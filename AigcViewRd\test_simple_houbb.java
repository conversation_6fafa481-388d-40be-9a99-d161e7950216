import com.github.houbb.sensitive.word.support.deny.WordDenys;
import java.util.*;

/**
 * 简单测试houbb库敏感词获取
 */
public class TestSimpleHoubb {
    
    public static void main(String[] args) {
        System.out.println("🚀 测试houbb库敏感词直接获取");
        System.out.println("================================");
        
        try {
            // 直接调用houbb库的默认敏感词API
            List<String> houbbWords = WordDenys.defaults().deny();
            
            System.out.println("✅ 成功获取houbb库敏感词");
            System.out.println("📊 敏感词总数: " + houbbWords.size());
            
            // 显示前20个敏感词作为示例
            System.out.println("\n📝 前20个敏感词示例:");
            for (int i = 0; i < Math.min(20, houbbWords.size()); i++) {
                System.out.println((i + 1) + ". " + houbbWords.get(i));
            }
            
            // 统计不同长度的敏感词
            Map<Integer, Integer> lengthCount = new HashMap<>();
            for (String word : houbbWords) {
                int length = word.length();
                lengthCount.put(length, lengthCount.getOrDefault(length, 0) + 1);
            }
            
            System.out.println("\n📈 敏感词长度分布:");
            for (Map.Entry<Integer, Integer> entry : lengthCount.entrySet()) {
                System.out.println(entry.getKey() + "字符: " + entry.getValue() + "个");
            }
            
            // 测试一些常见敏感词是否在列表中
            String[] testWords = {"法轮功", "台独", "色情", "赌博", "毒品", "暴力"};
            System.out.println("\n🔍 测试常见敏感词:");
            for (String word : testWords) {
                boolean contains = houbbWords.contains(word);
                System.out.println(word + ": " + (contains ? "✅ 包含" : "❌ 不包含"));
            }
            
            System.out.println("\n🎉 测试完成！houbb库包含 " + houbbWords.size() + " 个敏感词");
            
        } catch (Exception e) {
            System.err.println("❌ 测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
