<template>
  <a-card :bordered="false">
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline" @keyup.enter.native="searchQuery">
        <a-row :gutter="24">
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="轮播图标题">
              <a-input placeholder="请输入轮播图标题" v-model="queryParam.title"></a-input>
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="是否启用">
              <j-dict-select-tag placeholder="请选择是否启用" v-model="queryParam.status" dictCode="isTrue"/>
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <span style="float: left;overflow: hidden;" class="table-page-search-submitButtons">
              <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
              <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
              <a @click="handleToggleSearch" style="margin-left: 8px">
                {{ toggleSearchStatus ? '收起' : '展开' }}
                <a-icon :type="toggleSearchStatus ? 'up' : 'down'"/>
              </a>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <!-- 查询区域-END -->

    <!-- 操作按钮区域 -->
    <div class="table-operator">
      <a-button @click="handleAdd" type="primary" icon="plus">新增</a-button>
      <a-button type="primary" icon="download" @click="handleExportXls('首页轮播图')">导出</a-button>
      <a-upload name="file" :showUploadList="false" :multiple="false" :headers="tokenHeader" :action="importExcelUrl" @change="handleImportExcel">
        <a-button type="primary" icon="import">导入</a-button>
      </a-upload>
      <!-- 高级查询区域 -->
      <j-super-query :fieldList="superFieldList" ref="superQueryModal" @handleSuperQuery="handleSuperQuery"></j-super-query>
      <a-dropdown v-if="selectedRowKeys.length > 0">
        <a-menu slot="overlay">
          <a-menu-item key="1" @click="batchDel"><a-icon type="delete"/>删除</a-menu-item>
        </a-menu>
        <a-button style="margin-left: 8px"> 批量操作 <a-icon type="down" /></a-button>
      </a-dropdown>
    </div>

    <!-- table区域-begin -->
    <div>
      <div class="ant-alert ant-alert-info" style="margin-bottom: 16px;">
        <i class="anticon anticon-info-circle ant-alert-icon"></i> 已选择 <a style="font-weight: 600">{{ selectedRowKeys.length }}</a>项
        <a style="margin-left: 24px" @click="onClearSelected">清空</a>
      </div>

      <a-table
        ref="table"
        size="middle"
        :scroll="{x:true}"
        bordered
        rowKey="id"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="ipagination"
        :loading="loading"
        :rowSelection="{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}"
        class="j-table-force-nowrap"
        @change="handleTableChange">

        <template slot="htmlSlot" slot-scope="text">
          <div v-html="text"></div>
        </template>
        <template slot="imgSlot" slot-scope="text">
          <span v-if="!text" style="font-size: 12px;font-style: italic;">无图片</span>
          <img v-else :src="getImgView(text)" height="25px" alt="" style="max-width:80px;font-size: 12px;font-style: italic;"/>
        </template>
        <template slot="fileSlot" slot-scope="text">
          <span v-if="!text" style="font-size: 12px;font-style: italic;">无文件</span>
          <a-button
            v-else
            :ghost="true"
            type="primary"
            icon="download"
            size="small"
            @click="downloadFile(text)">
            下载
          </a-button>
        </template>

        <span slot="action" slot-scope="text, record">
          <a @click="handleEdit(record)">编辑</a>

          <a-divider type="vertical" />
          <a-dropdown>
            <a class="ant-dropdown-link">更多 <a-icon type="down" /></a>
            <a-menu slot="overlay">
              <a-menu-item>
                <a @click="handleDetail(record)">详情</a>
              </a-menu-item>
              <a-menu-item>
                <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
                  <a>删除</a>
                </a-popconfirm>
              </a-menu-item>
            </a-menu>
          </a-dropdown>
        </span>

      </a-table>
    </div>

    <aigc-home-carousel-modal ref="modalForm" @ok="modalFormOk"></aigc-home-carousel-modal>
  </a-card>
</template>

<script>

  import '@/assets/less/TableExpand.less'
  import { mixinDevice } from '@/utils/mixin'
  import { JeecgListMixin } from '@/mixins/JeecgListMixin'
  import AigcHomeCarouselModal from './modules/AigcHomeCarouselModal'
  import {filterMultiDictText, initDictOptions} from '@/components/dict/JDictSelectUtil'

  export default {
    name: 'AigcHomeCarouselList',
    mixins:[JeecgListMixin, mixinDevice],
    components: {
      AigcHomeCarouselModal
    },
    data () {
      return {
        description: '首页轮播图管理页面',
        // 表头
        columns: [
          {
            title: '#',
            dataIndex: '',
            key:'rowIndex',
            width:60,
            align:"center",
            customRender:function (t,r,index) {
              return parseInt(index)+1;
            }
          },
          {
            title:'轮播图标题',
            align:"center",
            dataIndex: 'title'
          },
          {
            title:'轮播图描述',
            align:"center",
            dataIndex: 'description'
          },
          {
            title:'轮播图',
            align:"center",
            dataIndex: 'imageUrl',
            scopedSlots: {customRender: 'imgSlot'}
          },
          {
            title:'标签文字',
            align:"center",
            dataIndex: 'badge'
          },
          {
            title:'按钮文字',
            align:"center",
            dataIndex: 'buttonText'
          },
          {
            title:'按钮跳转链接',
            align:"center",
            dataIndex: 'buttonLink'
          },
          {
            title:'排序序号',
            align:"center",
            dataIndex: 'sortOrder'
          },
          {
            title:'是否启用',
            align:"center",
            dataIndex: 'status',
            customRender: (text) => {
              return filterMultiDictText(this.dictOptions.isTrue, text);
            }
          },
          {
            title: '操作',
            dataIndex: 'action',
            align:"center",
            fixed:"right",
            width:147,
            scopedSlots: { customRender: 'action' }
          }
        ],
        url: {
          list: "/aigc/aigcHomeCarousel/list",
          delete: "/aigc/aigcHomeCarousel/delete",
          deleteBatch: "/aigc/aigcHomeCarousel/deleteBatch",
          exportXlsUrl: "/aigc/aigcHomeCarousel/exportXls",
          importExcelUrl: "aigc/aigcHomeCarousel/importExcel",

        },
        dictOptions:{
          isTrue: []
        },
        superFieldList:[],
      }
    },
    created() {
    this.getSuperFieldList();
    this.initDictConfig();
    },
    computed: {
      importExcelUrl: function(){
        return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`;
      },
    },
    methods: {
      initDictConfig(){
        // 初始化字典 - 是否启用
        initDictOptions('isTrue').then((res) => {
          if (res.success) {
            this.dictOptions.isTrue = res.result;
          }
        });
      },
      getSuperFieldList(){
        let fieldList=[];
        fieldList.push({type:'string',value:'title',text:'轮播图标题',dictCode:''})
        fieldList.push({type:'string',value:'description',text:'轮播图描述',dictCode:''})
        fieldList.push({type:'string',value:'imageUrl',text:'轮播图',dictCode:''})
        fieldList.push({type:'string',value:'badge',text:'标签文字',dictCode:''})
        fieldList.push({type:'string',value:'buttonText',text:'按钮文字',dictCode:''})
        fieldList.push({type:'string',value:'buttonLink',text:'按钮跳转链接',dictCode:''})
        fieldList.push({type:'int',value:'sortOrder',text:'排序序号',dictCode:''})
        fieldList.push({type:'string',value:'status',text:'是否启用',dictCode:'isTrue'})
        this.superFieldList = fieldList
      }
    }
  }
</script>
<style scoped>
  @import '~@assets/less/common.less';
</style>