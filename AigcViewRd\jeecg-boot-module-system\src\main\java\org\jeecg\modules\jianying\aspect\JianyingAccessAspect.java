package org.jeecg.modules.jianying.aspect;

import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.jianying.validator.JianyingAccessValidator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Field;

/**
 * 剪映小助手Access Key验证切面
 * 拦截所有剪映API方法，自动验证access_key参数
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
@Slf4j
@Aspect
@Component
public class JianyingAccessAspect {
    
    @Autowired
    private JianyingAccessValidator accessValidator;
    
    /**
     * 拦截所有剪映API Controller方法
     */
    @Around("execution(* org.jeecg.modules.jianying.controller.*.*(..))")
    public Object validateAccess(ProceedingJoinPoint joinPoint) throws Throwable {
        
        long startTime = System.currentTimeMillis();
        String methodName = joinPoint.getSignature().getName();
        String className = joinPoint.getTarget().getClass().getSimpleName();
        
        try {
            // 获取HTTP请求信息
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes == null) {
                log.error("无法获取HTTP请求上下文");
                return Result.error(500, "系统内部错误");
            }
            
            HttpServletRequest request = attributes.getRequest();
            String clientIp = getClientIp(request);
            String apiPath = request.getRequestURI();
            
            // 从方法参数中提取access_key
            String accessKey = extractAccessKey(joinPoint.getArgs());
            
            // 验证access_key
            if (!accessValidator.validateAccessKey(accessKey)) {
                // 记录访问失败日志
                accessValidator.logAccess(accessKey, apiPath, clientIp, false);
                
                if (accessKey == null || accessKey.trim().isEmpty()) {
                    return accessValidator.getMissingKeyResult();
                } else {
                    return accessValidator.getUnauthorizedResult();
                }
            }
            
            // 记录访问成功日志
            accessValidator.logAccess(accessKey, apiPath, clientIp, true);
            
            // 执行原方法
            Object result = joinPoint.proceed();
            
            // 记录执行时间
            long duration = System.currentTimeMillis() - startTime;
            log.info("剪映API执行成功 - {}.{}, 耗时: {}ms", className, methodName, duration);
            
            return result;
            
        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("剪映API执行异常 - {}.{}, 耗时: {}ms, 错误: {}", 
                     className, methodName, duration, e.getMessage(), e);
            
            // 返回统一的错误响应
            return Result.error(500, "系统内部错误：" + e.getMessage());
        }
    }
    
    /**
     * 从方法参数中提取access_key
     * 
     * @param args 方法参数数组
     * @return access_key值，如果未找到则返回null
     */
    private String extractAccessKey(Object[] args) {
        if (args == null || args.length == 0) {
            return null;
        }
        
        for (Object arg : args) {
            if (arg == null) {
                continue;
            }
            
            try {
                // 通过反射查找accessKey字段
                Field accessKeyField = findAccessKeyField(arg.getClass());
                if (accessKeyField != null) {
                    accessKeyField.setAccessible(true);
                    Object value = accessKeyField.get(arg);
                    return value != null ? value.toString() : null;
                }
            } catch (Exception e) {
                log.debug("从参数对象{}中提取access_key失败: {}", arg.getClass().getSimpleName(), e.getMessage());
            }
        }
        
        return null;
    }
    
    /**
     * 查找包含accessKey字段的Field
     * 
     * @param clazz 要查找的类
     * @return accessKey字段，如果未找到则返回null
     */
    private Field findAccessKeyField(Class<?> clazz) {
        // 查找当前类的字段
        for (Field field : clazz.getDeclaredFields()) {
            if ("accessKey".equals(field.getName()) || "access_key".equals(field.getName())) {
                return field;
            }
        }
        
        // 查找父类的字段
        Class<?> superClass = clazz.getSuperclass();
        if (superClass != null && superClass != Object.class) {
            return findAccessKeyField(superClass);
        }
        
        return null;
    }
    
    /**
     * 获取客户端真实IP地址
     * 
     * @param request HTTP请求对象
     * @return 客户端IP地址
     */
    private String getClientIp(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        
        // 处理多个IP的情况，取第一个
        if (ip != null && ip.contains(",")) {
            ip = ip.split(",")[0].trim();
        }
        
        return ip;
    }
    
    /**
     * 检查是否需要跳过验证的方法
     * 
     * @param methodName 方法名
     * @return true表示跳过验证，false表示需要验证
     */
    private boolean shouldSkipValidation(String methodName) {
        // 可以在这里添加不需要验证access_key的方法
        // 比如健康检查、文档接口等
        return "health".equals(methodName) || 
               "docs".equals(methodName) ||
               methodName.startsWith("get") && methodName.endsWith("Docs");
    }
}
