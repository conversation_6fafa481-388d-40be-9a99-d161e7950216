<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html>
  <head>
    <title>Logback Log Messages</title>
<style  type="text/css">
table { margin-left: 2em; margin-right: 2em; border-left: 2px solid #AAA; }
TR.even { background: #FFFFFF; }
TR.odd { background: #EAEAEA; }
TR.warn TD.Level, TR.error TD.Level, TR.fatal TD.Level {font-weight: bold; color: #FF4040 }
TD { padding-right: 1ex; padding-left: 1ex; border-right: 2px solid #AAA; }
TD.Time, TD.Date { text-align: right; font-family: courier, monospace; font-size: smaller; }
TD.Thread { text-align: left; }
TD.Level { text-align: right; }
TD.Logger { text-align: left; }
TR.header { background: #596ED5; color: #FFF; font-weight: bold; font-size: larger; }
TD.Exception { background: #A2AEE8; font-family: courier, monospace;}
</style>

  </head>
<body>
<hr/>
<p>Log session start time Fri Jul 18 19:01:53 CST 2025</p><p></p>

<table cellspacing="0">
<tr class="header">
<td class="Level">Level</td>
<td class="Date">Date</td>
<td class="Message">Message</td>
<td class="MethodOfCaller">MethodOfCaller</td>
<td class="FileOfCaller">FileOfCaller</td>
<td class="LineOfCaller">LineOfCaller</td>
</tr>


<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:01:53,135</td>
<td class="Message">HV000001: Hibernate Validator 6.1.6.Final</td>
<td class="MethodOfCaller">&lt;clinit&gt;</td>
<td class="FileOfCaller">Version.java</td>
<td class="LineOfCaller">21</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:01:53,171</td>
<td class="Message">Starting JeecgSystemApplication on DESKTOP-G0NDD8J with PID 13976 (D:\AigcView_zj\AigcViewRd\jeecg-boot-module-system\target\classes started by Administrator in D:\AigcView_zj\AigcViewRd)</td>
<td class="MethodOfCaller">logStarting</td>
<td class="FileOfCaller">StartupInfoLogger.java</td>
<td class="LineOfCaller">55</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:01:53,172</td>
<td class="Message">The following profiles are active: dev</td>
<td class="MethodOfCaller">logStartupProfileInfo</td>
<td class="FileOfCaller">SpringApplication.java</td>
<td class="LineOfCaller">655</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-18 19:01:53,620</td>
<td class="Message">For Jackson Kotlin classes support please add &quot;com.fasterxml.jackson.module:jackson-module-kotlin&quot; to the classpath</td>
<td class="MethodOfCaller">warn</td>
<td class="FileOfCaller">CompositeLog.java</td>
<td class="LineOfCaller">127</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:01:54,885</td>
<td class="Message">Multiple Spring Data modules found, entering strict repository configuration mode!</td>
<td class="MethodOfCaller">multipleStoresDetected</td>
<td class="FileOfCaller">RepositoryConfigurationDelegate.java</td>
<td class="LineOfCaller">249</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:01:54,888</td>
<td class="Message">Bootstrapping Spring Data Redis repositories in DEFAULT mode.</td>
<td class="MethodOfCaller">registerRepositoriesIn</td>
<td class="FileOfCaller">RepositoryConfigurationDelegate.java</td>
<td class="LineOfCaller">127</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:01:55,054</td>
<td class="Message">Finished Spring Data repository scanning in 150ms. Found 0 Redis repository interfaces.</td>
<td class="MethodOfCaller">registerRepositoriesIn</td>
<td class="FileOfCaller">RepositoryConfigurationDelegate.java</td>
<td class="LineOfCaller">187</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:01:55,181</td>
<td class="Message"> ******************* init miniDao config [ begin ] *********************** </td>
<td class="MethodOfCaller">miniDaoBeanScannerConfigurer</td>
<td class="FileOfCaller">MinidaoAutoConfiguration.java</td>
<td class="LineOfCaller">23</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:01:55,181</td>
<td class="Message"> ------ minidao.base-package ------- org.jeecg.modules.jmreport.*</td>
<td class="MethodOfCaller">miniDaoBeanScannerConfigurer</td>
<td class="FileOfCaller">MinidaoAutoConfiguration.java</td>
<td class="LineOfCaller">25</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:01:55,182</td>
<td class="Message"> *******************  init miniDao config  [ end ] *********************** </td>
<td class="MethodOfCaller">miniDaoBeanScannerConfigurer</td>
<td class="FileOfCaller">MinidaoAutoConfiguration.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:01:55,262</td>
<td class="Message">register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDao }</td>
<td class="MethodOfCaller">doScan</td>
<td class="FileOfCaller">MiniDaoClassPathMapperScanner.java</td>
<td class="LineOfCaller">48</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:01:55,263</td>
<td class="Message">register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDataSourceDao }</td>
<td class="MethodOfCaller">doScan</td>
<td class="FileOfCaller">MiniDaoClassPathMapperScanner.java</td>
<td class="LineOfCaller">48</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:01:55,263</td>
<td class="Message">register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDbDao }</td>
<td class="MethodOfCaller">doScan</td>
<td class="FileOfCaller">MiniDaoClassPathMapperScanner.java</td>
<td class="LineOfCaller">48</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:01:55,263</td>
<td class="Message">register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDbFieldDao }</td>
<td class="MethodOfCaller">doScan</td>
<td class="FileOfCaller">MiniDaoClassPathMapperScanner.java</td>
<td class="LineOfCaller">48</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:01:55,263</td>
<td class="Message">register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDbParamDao }</td>
<td class="MethodOfCaller">doScan</td>
<td class="FileOfCaller">MiniDaoClassPathMapperScanner.java</td>
<td class="LineOfCaller">48</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:01:55,264</td>
<td class="Message">register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDictDao }</td>
<td class="MethodOfCaller">doScan</td>
<td class="FileOfCaller">MiniDaoClassPathMapperScanner.java</td>
<td class="LineOfCaller">48</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:01:55,264</td>
<td class="Message">register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDictItemDao }</td>
<td class="MethodOfCaller">doScan</td>
<td class="FileOfCaller">MiniDaoClassPathMapperScanner.java</td>
<td class="LineOfCaller">48</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:01:55,264</td>
<td class="Message">register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportLinkDao }</td>
<td class="MethodOfCaller">doScan</td>
<td class="FileOfCaller">MiniDaoClassPathMapperScanner.java</td>
<td class="LineOfCaller">48</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:01:55,264</td>
<td class="Message">register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportMapDao }</td>
<td class="MethodOfCaller">doScan</td>
<td class="FileOfCaller">MiniDaoClassPathMapperScanner.java</td>
<td class="LineOfCaller">48</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:01:55,264</td>
<td class="Message">register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportShareDao }</td>
<td class="MethodOfCaller">doScan</td>
<td class="FileOfCaller">MiniDaoClassPathMapperScanner.java</td>
<td class="LineOfCaller">48</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:01:55,446</td>
<td class="Message">Bean &#39;(inner bean)#7aa01bd9&#39; of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:01:55,450</td>
<td class="Message">Bean &#39;jimuReportDao&#39; of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:01:55,451</td>
<td class="Message">Bean &#39;(inner bean)#7aa01bd9#1&#39; of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:01:55,451</td>
<td class="Message">Bean &#39;jimuReportDataSourceDao&#39; of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:01:55,452</td>
<td class="Message">Bean &#39;(inner bean)#7aa01bd9#2&#39; of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:01:55,453</td>
<td class="Message">Bean &#39;jimuReportDbDao&#39; of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:01:55,454</td>
<td class="Message">Bean &#39;(inner bean)#7aa01bd9#3&#39; of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:01:55,455</td>
<td class="Message">Bean &#39;jimuReportDbFieldDao&#39; of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:01:55,455</td>
<td class="Message">Bean &#39;(inner bean)#7aa01bd9#4&#39; of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:01:55,456</td>
<td class="Message">Bean &#39;jimuReportDbParamDao&#39; of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:01:55,457</td>
<td class="Message">Bean &#39;(inner bean)#7aa01bd9#5&#39; of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:01:55,457</td>
<td class="Message">Bean &#39;jimuReportDictDao&#39; of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:01:55,458</td>
<td class="Message">Bean &#39;(inner bean)#7aa01bd9#6&#39; of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:01:55,459</td>
<td class="Message">Bean &#39;jimuReportDictItemDao&#39; of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:01:55,460</td>
<td class="Message">Bean &#39;(inner bean)#7aa01bd9#7&#39; of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:01:55,460</td>
<td class="Message">Bean &#39;jimuReportLinkDao&#39; of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:01:55,461</td>
<td class="Message">Bean &#39;(inner bean)#7aa01bd9#8&#39; of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:01:55,461</td>
<td class="Message">Bean &#39;jimuReportMapDao&#39; of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:01:55,462</td>
<td class="Message">Bean &#39;(inner bean)#7aa01bd9#9&#39; of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:01:55,462</td>
<td class="Message">Bean &#39;jimuReportShareDao&#39; of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:01:55,481</td>
<td class="Message">Bean &#39;spring.redis-org.springframework.boot.autoconfigure.data.redis.RedisProperties&#39; of type [org.springframework.boot.autoconfigure.data.redis.RedisProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:01:55,484</td>
<td class="Message">Bean &#39;org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration&#39; of type [org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:01:55,553</td>
<td class="Message">Bean &#39;lettuceClientResources&#39; of type [io.lettuce.core.resource.DefaultClientResources] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:01:55,626</td>
<td class="Message">Bean &#39;redisConnectionFactory&#39; of type [org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:01:55,628</td>
<td class="Message">Bean &#39;shiroConfig&#39; of type [org.jeecg.config.shiro.ShiroConfig$$EnhancerBySpringCGLIB$$eedcb369] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:01:55,670</td>
<td class="Message">Bean &#39;shiroRealm&#39; of type [org.jeecg.config.shiro.ShiroRealm] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:01:56,076</td>
<td class="Message">===============(1)创建缓存管理器RedisCacheManager</td>
<td class="MethodOfCaller">redisCacheManager</td>
<td class="FileOfCaller">ShiroConfig.java</td>
<td class="LineOfCaller">215</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:01:56,078</td>
<td class="Message">===============(2)创建RedisManager,连接Redis..</td>
<td class="MethodOfCaller">redisManager</td>
<td class="FileOfCaller">ShiroConfig.java</td>
<td class="LineOfCaller">233</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:01:56,082</td>
<td class="Message">Bean &#39;redisManager&#39; of type [org.crazycake.shiro.RedisManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:01:56,085</td>
<td class="Message">Bean &#39;securityManager&#39; of type [org.apache.shiro.web.mgt.DefaultWebSecurityManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:01:56,124</td>
<td class="Message">Bean &#39;authorizationAttributeSourceAdvisor&#39; of type [org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:01:56,299</td>
<td class="Message">Bean &#39;spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties&#39; of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:01:56,305</td>
<td class="Message">Bean &#39;com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration&#39; of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$f92edc41] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:01:56,314</td>
<td class="Message">Bean &#39;dsProcessor&#39; of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:01:56,326</td>
<td class="Message">Bean &#39;redisConfig&#39; of type [org.jeecg.common.modules.redis.config.RedisConfig$$EnhancerBySpringCGLIB$$c3ec399] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:01:56,360</td>
<td class="Message">Bean &#39;org.apache.shiro.spring.boot.autoconfigure.ShiroBeanAutoConfiguration&#39; of type [org.apache.shiro.spring.boot.autoconfigure.ShiroBeanAutoConfiguration$$EnhancerBySpringCGLIB$$a2b94920] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:01:56,364</td>
<td class="Message">Bean &#39;eventBus&#39; of type [org.apache.shiro.event.support.DefaultEventBus] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:01:56,628</td>
<td class="Message">Tomcat initialized with port(s): 8080 (http)</td>
<td class="MethodOfCaller">initialize</td>
<td class="FileOfCaller">TomcatWebServer.java</td>
<td class="LineOfCaller">108</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:01:56,635</td>
<td class="Message">Initializing ProtocolHandler [&quot;http-nio-8080&quot;]</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">DirectJDKLog.java</td>
<td class="LineOfCaller">173</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:01:56,636</td>
<td class="Message">Starting service [Tomcat]</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">DirectJDKLog.java</td>
<td class="LineOfCaller">173</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:01:56,636</td>
<td class="Message">Starting Servlet engine: [Apache Tomcat/9.0.39]</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">DirectJDKLog.java</td>
<td class="LineOfCaller">173</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:01:56,796</td>
<td class="Message">Initializing Spring embedded WebApplicationContext</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">DirectJDKLog.java</td>
<td class="LineOfCaller">173</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:01:56,796</td>
<td class="Message">Root WebApplicationContext: initialization completed in 3581 ms</td>
<td class="MethodOfCaller">prepareWebApplicationContext</td>
<td class="FileOfCaller">ServletWebServerApplicationContext.java</td>
<td class="LineOfCaller">285</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:01:57,966</td>
<td class="Message">{dataSource-1,master} inited</td>
<td class="MethodOfCaller">init</td>
<td class="FileOfCaller">DruidDataSource.java</td>
<td class="LineOfCaller">994</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:01:57,967</td>
<td class="Message">dynamic-datasource - load a datasource named [master] success</td>
<td class="MethodOfCaller">addDataSource</td>
<td class="FileOfCaller">DynamicRoutingDataSource.java</td>
<td class="LineOfCaller">132</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:01:57,968</td>
<td class="Message">dynamic-datasource initial loaded [1] datasource,primary datasource named [master]</td>
<td class="MethodOfCaller">afterPropertiesSet</td>
<td class="FileOfCaller">DynamicRoutingDataSource.java</td>
<td class="LineOfCaller">237</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:01:59,385</td>
<td class="Message"> --- redis config init --- </td>
<td class="MethodOfCaller">redisTemplate</td>
<td class="FileOfCaller">RedisConfig.java</td>
<td class="LineOfCaller">76</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:00,187</td>
<td class="Message">开始初始化TOS客户端...</td>
<td class="MethodOfCaller">initTosClient</td>
<td class="FileOfCaller">TosService.java</td>
<td class="LineOfCaller">86</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:00,187</td>
<td class="Message">TOS配置验证通过 - Region: cn-shanghai, Endpoint: tos-cn-shanghai.volces.com, Bucket: aigcview-tos</td>
<td class="MethodOfCaller">initTosClient</td>
<td class="FileOfCaller">TosService.java</td>
<td class="LineOfCaller">105</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:00,312</td>
<td class="Message">外网TOS客户端初始化成功！</td>
<td class="MethodOfCaller">initTosClient</td>
<td class="FileOfCaller">TosService.java</td>
<td class="LineOfCaller">115</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:00,314</td>
<td class="Message">内网TOS客户端初始化成功 - Endpoint: tos-cn-shanghai.ivolces.com</td>
<td class="MethodOfCaller">initTosClient</td>
<td class="FileOfCaller">TosService.java</td>
<td class="LineOfCaller">125</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:00,314</td>
<td class="Message">正在测试TOS连接...</td>
<td class="MethodOfCaller">testTosConnection</td>
<td class="FileOfCaller">TosService.java</td>
<td class="LineOfCaller">591</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:00,314</td>
<td class="Message">TOS连接测试成功！</td>
<td class="MethodOfCaller">testTosConnection</td>
<td class="FileOfCaller">TosService.java</td>
<td class="LineOfCaller">593</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:01,386</td>
<td class="Message">🔍 开始初始化敏感词库...</td>
<td class="MethodOfCaller">init</td>
<td class="FileOfCaller">SensitiveWordServiceImpl.java</td>
<td class="LineOfCaller">48</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:01,713</td>
<td class="Message">✅ 敏感词库初始化完成</td>
<td class="MethodOfCaller">init</td>
<td class="FileOfCaller">SensitiveWordServiceImpl.java</td>
<td class="LineOfCaller">64</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:02,013</td>
<td class="Message">初始化预定义特效映射完成，共 4 个特效</td>
<td class="MethodOfCaller">initDefaultEffectMapping</td>
<td class="FileOfCaller">JianyingEffectSearchService.java</td>
<td class="LineOfCaller">86</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:02,019</td>
<td class="Message">RestTemplate初始化完成，支持TOS文件下载</td>
<td class="MethodOfCaller">initRestTemplate</td>
<td class="FileOfCaller">CozeApiService.java</td>
<td class="LineOfCaller">64</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:02,064</td>
<td class="Message">开始初始化TOS客户端...</td>
<td class="MethodOfCaller">initTosClient</td>
<td class="FileOfCaller">JianyingProTosService.java</td>
<td class="LineOfCaller">86</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:02,064</td>
<td class="Message">TOS配置验证通过 - Region: cn-shanghai, Endpoint: tos-cn-shanghai.volces.com, Bucket: aigcview-tos</td>
<td class="MethodOfCaller">initTosClient</td>
<td class="FileOfCaller">JianyingProTosService.java</td>
<td class="LineOfCaller">105</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:02,065</td>
<td class="Message">外网TOS客户端初始化成功！</td>
<td class="MethodOfCaller">initTosClient</td>
<td class="FileOfCaller">JianyingProTosService.java</td>
<td class="LineOfCaller">115</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:02,066</td>
<td class="Message">内网TOS客户端初始化成功 - Endpoint: tos-cn-shanghai.ivolces.com</td>
<td class="MethodOfCaller">initTosClient</td>
<td class="FileOfCaller">JianyingProTosService.java</td>
<td class="LineOfCaller">125</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:02,066</td>
<td class="Message">正在测试TOS连接...</td>
<td class="MethodOfCaller">testTosConnection</td>
<td class="FileOfCaller">JianyingProTosService.java</td>
<td class="LineOfCaller">627</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:02,067</td>
<td class="Message">TOS连接测试成功！</td>
<td class="MethodOfCaller">testTosConnection</td>
<td class="FileOfCaller">JianyingProTosService.java</td>
<td class="LineOfCaller">629</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:02,076</td>
<td class="Message">超级剪映小助手 - 初始化预定义特效映射完成，共 4 个特效</td>
<td class="MethodOfCaller">initDefaultEffectMapping</td>
<td class="FileOfCaller">JianyingProEffectSearchService.java</td>
<td class="LineOfCaller">87</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:02,082</td>
<td class="Message">超级剪映小助手 - RestTemplate初始化完成，支持TOS文件下载</td>
<td class="MethodOfCaller">initRestTemplate</td>
<td class="FileOfCaller">JianyingProCozeApiService.java</td>
<td class="LineOfCaller">59</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:02,454</td>
<td class="Message">Using default implementation for ThreadExecutor</td>
<td class="MethodOfCaller">instantiate</td>
<td class="FileOfCaller">StdSchedulerFactory.java</td>
<td class="LineOfCaller">1220</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:02,456</td>
<td class="Message">Job execution threads will use class loader of thread: main</td>
<td class="MethodOfCaller">initialize</td>
<td class="FileOfCaller">SimpleThreadPool.java</td>
<td class="LineOfCaller">268</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:02,462</td>
<td class="Message">Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl</td>
<td class="MethodOfCaller">&lt;init&gt;</td>
<td class="FileOfCaller">SchedulerSignalerImpl.java</td>
<td class="LineOfCaller">61</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:02,463</td>
<td class="Message">Quartz Scheduler v.2.3.2 created.</td>
<td class="MethodOfCaller">&lt;init&gt;</td>
<td class="FileOfCaller">QuartzScheduler.java</td>
<td class="LineOfCaller">229</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:02,466</td>
<td class="Message">Using db table-based data access locking (synchronization).</td>
<td class="MethodOfCaller">initialize</td>
<td class="FileOfCaller">JobStoreSupport.java</td>
<td class="LineOfCaller">672</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:02,468</td>
<td class="Message">JobStoreCMT initialized.</td>
<td class="MethodOfCaller">initialize</td>
<td class="FileOfCaller">JobStoreCMT.java</td>
<td class="LineOfCaller">145</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:02,468</td>
<td class="Message">Scheduler meta-data: Quartz Scheduler (v2.3.2) &#39;MyScheduler&#39; with instanceId &#39;DESKTOP-G0NDD8J1752836522455&#39;
  Scheduler class: &#39;org.quartz.core.QuartzScheduler&#39; - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool &#39;org.quartz.simpl.SimpleThreadPool&#39; - with 10 threads.
  Using job-store &#39;org.springframework.scheduling.quartz.LocalDataSourceJobStore&#39; - which supports persistence. and is clustered.
</td>
<td class="MethodOfCaller">initialize</td>
<td class="FileOfCaller">QuartzScheduler.java</td>
<td class="LineOfCaller">294</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:02,469</td>
<td class="Message">Quartz scheduler &#39;MyScheduler&#39; initialized from an externally provided properties instance.</td>
<td class="MethodOfCaller">instantiate</td>
<td class="FileOfCaller">StdSchedulerFactory.java</td>
<td class="LineOfCaller">1374</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:02,469</td>
<td class="Message">Quartz scheduler version: 2.3.2</td>
<td class="MethodOfCaller">instantiate</td>
<td class="FileOfCaller">StdSchedulerFactory.java</td>
<td class="LineOfCaller">1378</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:02,469</td>
<td class="Message">JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@2a0881f1</td>
<td class="MethodOfCaller">setJobFactory</td>
<td class="FileOfCaller">QuartzScheduler.java</td>
<td class="LineOfCaller">2293</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:04,417</td>
<td class="Message"> --- Init JimuReport Config --- </td>
<td class="MethodOfCaller">afterPropertiesSet</td>
<td class="FileOfCaller">JimuReportConfiguration.java</td>
<td class="LineOfCaller">55</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:05,082</td>
<td class="Message">Exposing 2 endpoint(s) beneath base path &#39;/actuator&#39;</td>
<td class="MethodOfCaller">&lt;init&gt;</td>
<td class="FileOfCaller">EndpointLinksResolver.java</td>
<td class="LineOfCaller">58</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:05,174</td>
<td class="Message"> 代码生成器数据库连接，使用application.yml的DB配置 ###################</td>
<td class="MethodOfCaller">initCodeGenerateDbConfig</td>
<td class="FileOfCaller">CodeGenerateDbConfig.java</td>
<td class="LineOfCaller">46</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:05,195</td>
<td class="Message">Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2ControllerWebMvc#getDocumentation(String, HttpServletRequest)]</td>
<td class="MethodOfCaller">initHandlerMethods</td>
<td class="FileOfCaller">WebMvcPropertySourcedRequestMappingHandlerMapping.java</td>
<td class="LineOfCaller">69</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:05,269</td>
<td class="Message">---创建线程池---</td>
<td class="MethodOfCaller">asyncServiceExecutor</td>
<td class="FileOfCaller">JmReportExecutorConfig.java</td>
<td class="LineOfCaller">21</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:05,270</td>
<td class="Message">Initializing ExecutorService</td>
<td class="MethodOfCaller">initialize</td>
<td class="FileOfCaller">ExecutorConfigurationSupport.java</td>
<td class="LineOfCaller">181</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:05,273</td>
<td class="Message">Initializing ExecutorService &#39;jmReportTaskExecutor&#39;</td>
<td class="MethodOfCaller">initialize</td>
<td class="FileOfCaller">ExecutorConfigurationSupport.java</td>
<td class="LineOfCaller">181</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:05,986</td>
<td class="Message">Starting ProtocolHandler [&quot;http-nio-8080&quot;]</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">DirectJDKLog.java</td>
<td class="LineOfCaller">173</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,006</td>
<td class="Message">Tomcat started on port(s): 8080 (http) with context path &#39;/jeecg-boot&#39;</td>
<td class="MethodOfCaller">start</td>
<td class="FileOfCaller">TomcatWebServer.java</td>
<td class="LineOfCaller">220</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,007</td>
<td class="Message">Documentation plugins bootstrapped</td>
<td class="MethodOfCaller">start</td>
<td class="FileOfCaller">DocumentationPluginsBootstrapper.java</td>
<td class="LineOfCaller">93</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,010</td>
<td class="Message">Found 1 custom documentation plugin(s)</td>
<td class="MethodOfCaller">bootstrapDocumentationPlugins</td>
<td class="FileOfCaller">AbstractDocumentationPluginsBootstrapper.java</td>
<td class="LineOfCaller">79</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,110</td>
<td class="Message">Scanning for api listing references</td>
<td class="MethodOfCaller">scan</td>
<td class="FileOfCaller">ApiListingReferenceScanner.java</td>
<td class="LineOfCaller">44</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,243</td>
<td class="Message">Generating unique operation named: addUsingPOST_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,249</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,253</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,264</td>
<td class="Message">Generating unique operation named: addUsingPOST_2</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,266</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,267</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,268</td>
<td class="Message">Generating unique operation named: editUsingPUT_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,268</td>
<td class="Message">Generating unique operation named: getByUserIdUsingGET_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,270</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_2</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,274</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_2</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,282</td>
<td class="Message">Generating unique operation named: addUsingPOST_3</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,284</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_2</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,285</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_2</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,287</td>
<td class="Message">Generating unique operation named: editUsingPUT_2</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,288</td>
<td class="Message">Generating unique operation named: getByUserIdUsingGET_2</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,291</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_3</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,295</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_3</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,305</td>
<td class="Message">Generating unique operation named: addUsingPOST_4</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,306</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_3</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,306</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_3</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,308</td>
<td class="Message">Generating unique operation named: editUsingPUT_3</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,312</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_4</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,316</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_4</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,322</td>
<td class="Message">Generating unique operation named: addUsingPOST_5</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,323</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_4</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,324</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_4</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,325</td>
<td class="Message">Generating unique operation named: editUsingPUT_4</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,326</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_5</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,329</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_5</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,335</td>
<td class="Message">Generating unique operation named: addUsingPOST_6</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,336</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_5</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,337</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_5</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,338</td>
<td class="Message">Generating unique operation named: editUsingPUT_5</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,341</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_6</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,344</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_6</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,349</td>
<td class="Message">Generating unique operation named: addUsingPOST_7</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,351</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_6</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,352</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_6</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,353</td>
<td class="Message">Generating unique operation named: editUsingPUT_6</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,354</td>
<td class="Message">Generating unique operation named: getByReferrerIdUsingGET_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,357</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_7</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,360</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_7</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,368</td>
<td class="Message">Generating unique operation named: getUsageStatsUsingGET_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,376</td>
<td class="Message">Generating unique operation named: addUsingPOST_8</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,376</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_7</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,378</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_7</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,380</td>
<td class="Message">Generating unique operation named: editUsingPUT_7</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,381</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_8</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,383</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_8</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,388</td>
<td class="Message">Generating unique operation named: addUsingPOST_9</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,388</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_8</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,389</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_8</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,390</td>
<td class="Message">Generating unique operation named: editUsingPUT_8</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,391</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_9</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,394</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_9</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,403</td>
<td class="Message">Generating unique operation named: addUsingPOST_10</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,406</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_9</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,406</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_9</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,407</td>
<td class="Message">Generating unique operation named: editUsingPUT_9</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,411</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_10</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,415</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_10</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,424</td>
<td class="Message">Generating unique operation named: addUsingPOST_11</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,427</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_10</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,428</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_10</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,429</td>
<td class="Message">Generating unique operation named: editUsingPUT_10</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,434</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_11</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,437</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_11</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,442</td>
<td class="Message">Generating unique operation named: addUsingPOST_12</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,443</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_11</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,444</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_11</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,445</td>
<td class="Message">Generating unique operation named: editUsingPUT_11</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,446</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_12</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,450</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_12</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,456</td>
<td class="Message">Generating unique operation named: addUsingPOST_13</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,457</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_12</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,458</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_12</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,460</td>
<td class="Message">Generating unique operation named: editUsingPUT_12</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,462</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_13</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,466</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_13</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,472</td>
<td class="Message">Generating unique operation named: addUsingPOST_14</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,474</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_13</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,475</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_13</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,476</td>
<td class="Message">Generating unique operation named: editUsingPUT_13</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,477</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_14</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,480</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_14</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,484</td>
<td class="Message">Generating unique operation named: addUsingPOST_15</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,486</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_14</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,487</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_14</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,488</td>
<td class="Message">Generating unique operation named: editUsingPUT_14</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,489</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_15</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,493</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_15</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,579</td>
<td class="Message">Generating unique operation named: addAudiosUsingPOST_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,583</td>
<td class="Message">Generating unique operation named: addCaptionsUsingPOST_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,584</td>
<td class="Message">Generating unique operation named: addEffectsUsingPOST_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,586</td>
<td class="Message">Generating unique operation named: addImagesUsingPOST_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,588</td>
<td class="Message">Generating unique operation named: addKeyframesUsingPOST_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,597</td>
<td class="Message">Generating unique operation named: addVideosUsingPOST_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,641</td>
<td class="Message">Generating unique operation named: addUsingPOST_16</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,643</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_15</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,644</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_15</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,645</td>
<td class="Message">Generating unique operation named: editUsingPUT_15</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,646</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_16</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,648</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_16</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,653</td>
<td class="Message">Generating unique operation named: addUsingPOST_17</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,655</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_16</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,656</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_16</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,659</td>
<td class="Message">Generating unique operation named: editUsingPUT_16</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,660</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_17</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,664</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_17</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,668</td>
<td class="Message">Generating unique operation named: addUsingPOST_18</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,670</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_17</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,671</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_17</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,673</td>
<td class="Message">Generating unique operation named: editUsingPUT_17</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,675</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_18</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,677</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_18</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,681</td>
<td class="Message">Generating unique operation named: addUsingPOST_19</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,682</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_18</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,683</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_18</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,684</td>
<td class="Message">Generating unique operation named: editUsingPUT_18</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,685</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_19</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,688</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_19</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,696</td>
<td class="Message">Generating unique operation named: addUsingPOST_20</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,697</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_19</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,699</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_19</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,700</td>
<td class="Message">Generating unique operation named: editUsingPUT_19</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,701</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_20</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,705</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_20</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,713</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_21</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,714</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_20</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,719</td>
<td class="Message">Generating unique operation named: addUsingPOST_21</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,720</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_20</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,722</td>
<td class="Message">Generating unique operation named: editUsingPUT_20</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,724</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_21</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,733</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_22</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,736</td>
<td class="Message">Generating unique operation named: addUsingPOST_22</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,739</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_21</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,741</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_21</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,742</td>
<td class="Message">Generating unique operation named: editUsingPUT_21</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,755</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_22</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,776</td>
<td class="Message">Generating unique operation named: getReferralStatsUsingGET_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,794</td>
<td class="Message">Generating unique operation named: sendEmailCodeUsingPOST_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,795</td>
<td class="Message">Generating unique operation named: sendSmsCodeUsingPOST_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:06,797</td>
<td class="Message">Generating unique operation named: verifyCodeUsingPOST_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:07,774</td>
<td class="Message">Started JeecgSystemApplication in 15.224 seconds (JVM running for 16.54)</td>
<td class="MethodOfCaller">logStarted</td>
<td class="FileOfCaller">StartupInfoLogger.java</td>
<td class="LineOfCaller">61</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:07,782</td>
<td class="Message">
----------------------------------------------------------
	Application Jeecg-Boot is running! Access URLs:
	Local: 		http://localhost:8080/jeecg-boot/
	External: 	http://**********:8080/jeecg-boot/
	Swagger文档: 	http://**********:8080/jeecg-boot/doc.html
----------------------------------------------------------</td>
<td class="MethodOfCaller">main</td>
<td class="FileOfCaller">JeecgSystemApplication.java</td>
<td class="LineOfCaller">39</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:08,828</td>
<td class="Message">Initializing Spring DispatcherServlet &#39;dispatcherServlet&#39;</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">DirectJDKLog.java</td>
<td class="LineOfCaller">173</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:08,829</td>
<td class="Message">Initializing Servlet &#39;dispatcherServlet&#39;</td>
<td class="MethodOfCaller">initServletBean</td>
<td class="FileOfCaller">FrameworkServlet.java</td>
<td class="LineOfCaller">525</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:02:08,892</td>
<td class="Message">Completed initialization in 62 ms</td>
<td class="MethodOfCaller">initServletBean</td>
<td class="FileOfCaller">FrameworkServlet.java</td>
<td class="LineOfCaller">547</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:03:51,042</td>
<td class="Message">剪映API访问成功 - Path: /jeecg-boot/api/jianying/create_draft, IP: *************, AccessKey: JianyingAP***</td>
<td class="MethodOfCaller">logAccess</td>
<td class="FileOfCaller">JianyingAccessValidator.java</td>
<td class="LineOfCaller">119</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:03:51,043</td>
<td class="Message">剪映API访问验证通过 - Method: JianyingToolboxController.createDraft, IP: unknown, Request: CreateDraftRequest{accessKey=JianyingAP***, height=1080, width=1920, userId=null}</td>
<td class="MethodOfCaller">validateAccessKey</td>
<td class="FileOfCaller">JianyingAccessKeyAspect.java</td>
<td class="LineOfCaller">87</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:03:51,046</td>
<td class="Message">创建草稿请求: CreateDraftRequest{accessKey=JianyingAP***, height=1080, width=1920, userId=null}</td>
<td class="MethodOfCaller">createDraft</td>
<td class="FileOfCaller">JianyingToolboxController.java</td>
<td class="LineOfCaller">46</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:03:51,047</td>
<td class="Message">开始创建基础草稿JSON，参数: CreateDraftRequest{accessKey=JianyingAP***, height=1080, width=1920, userId=null}</td>
<td class="MethodOfCaller">createDraft</td>
<td class="FileOfCaller">JianyingAssistantService.java</td>
<td class="LineOfCaller">75</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:03:51,093</td>
<td class="Message">上传剪映草稿文件到专用桶，Key: jianying-assistant/drafts/2025/07/18/zj_draft_20250718_190351_c4b90ef5.json</td>
<td class="MethodOfCaller">uploadDraftFile</td>
<td class="FileOfCaller">TosService.java</td>
<td class="LineOfCaller">158</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-07-18 19:03:51,656</td>
<td class="Message">[high latency request] requestId: 9d43017a2a193454687a2a19-ac117eec-1ucisb-PuO-cs-tos-front-k2-2, method: PUT, host: aigcview-jianying.tos-cn-shanghai.volces.com, request uri: /jianying-assistant%2Fdrafts%2F2025%2F07%2F18%2Fzj_draft_20250718_190351_c4b90ef5.json, dns cost: 1 ms, connect cost: 365 ms, tls handshake cost: 359 ms, send headers and body cost: 3 ms, wait response cost: 0 ms, request cost: 536 ms
</td>
<td class="MethodOfCaller">printHighLatencyLog</td>
<td class="FileOfCaller">RequestEventListener.java</td>
<td class="LineOfCaller">227</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:03:51,656</td>
<td class="Message">tos: status code:200, request id:9d43017a2a193454687a2a19-ac117eec-1ucisb-PuO-cs-tos-front-k2-2, request cost 552 ms, request 1 times
</td>
<td class="MethodOfCaller">printAccessLogSucceed</td>
<td class="FileOfCaller">RequestTransport.java</td>
<td class="LineOfCaller">262</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:03:51,658</td>
<td class="Message">草稿文件上传成功，系统URL: https://aigcview.cn/jeecg-boot/sys/common/jianying-file/jianying-assistant/drafts/2025/07/18/zj_draft_20250718_190351_c4b90ef5.json, ETag: &quot;bba26f0fc9aff975babe5baa9b5c6069&quot;</td>
<td class="MethodOfCaller">uploadDraftFile</td>
<td class="FileOfCaller">TosService.java</td>
<td class="LineOfCaller">171</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:03:51,661</td>
<td class="Message">基础草稿JSON创建成功: https://aigcview.cn/jeecg-boot/sys/common/jianying-file/jianying-assistant/drafts/2025/07/18/zj_draft_20250718_190351_c4b90ef5.json</td>
<td class="MethodOfCaller">createDraft</td>
<td class="FileOfCaller">JianyingAssistantService.java</td>
<td class="LineOfCaller">98</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:03:51,664</td>
<td class="Message">剪映API执行成功 - JianyingToolboxController.createDraft, 耗时: 623ms</td>
<td class="MethodOfCaller">validateAccess</td>
<td class="FileOfCaller">JianyingAccessAspect.java</td>
<td class="LineOfCaller">77</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:03:51,957</td>
<td class="Message">剪映API访问成功 - Path: /jeecg-boot/api/jianying/add_text_style, IP: **************, AccessKey: JianyingAP***</td>
<td class="MethodOfCaller">logAccess</td>
<td class="FileOfCaller">JianyingAccessValidator.java</td>
<td class="LineOfCaller">119</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:03:51,957</td>
<td class="Message">剪映API访问验证通过 - Method: JianyingToolboxController.addTextStyle, IP: unknown, Request: AddTextStyleRequest{accessKey=JianyingAP***, keyword=文本, text=创建文本富文本样式}</td>
<td class="MethodOfCaller">validateAccessKey</td>
<td class="FileOfCaller">JianyingAccessKeyAspect.java</td>
<td class="LineOfCaller">87</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:03:51,957</td>
<td class="Message">创建文本样式请求: AddTextStyleRequest{accessKey=JianyingAP***, keyword=文本, text=创建文本富文本样式}</td>
<td class="MethodOfCaller">addTextStyle</td>
<td class="FileOfCaller">JianyingToolboxController.java</td>
<td class="LineOfCaller">364</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:03:51,957</td>
<td class="Message">开始创建文本样式: AddTextStyleRequest{accessKey=JianyingAP***, keyword=文本, text=创建文本富文本样式}</td>
<td class="MethodOfCaller">addTextStyle</td>
<td class="FileOfCaller">JianyingAssistantService.java</td>
<td class="LineOfCaller">4242</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:03:51,958</td>
<td class="Message">生成剪映富文本样式: text=创建文本富文本样式, keyword=文本, baseFontSize=15, keywordFontSize=15, keywordColor=ff7100</td>
<td class="MethodOfCaller">generateJianyingTextStyle</td>
<td class="FileOfCaller">JianyingAssistantService.java</td>
<td class="LineOfCaller">4282</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:03:51,958</td>
<td class="Message">颜色解析结果: ff7100 -&gt; [1.0, 0.44313725490196076, 0.0]</td>
<td class="MethodOfCaller">parseColorToRgb</td>
<td class="FileOfCaller">JianyingAssistantService.java</td>
<td class="LineOfCaller">4401</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:03:51,963</td>
<td class="Message">生成的剪映富文本样式: {&quot;styles&quot;:[{&quot;size&quot;:15,&quot;range&quot;:[0,2],&quot;fill&quot;:{&quot;content&quot;:{&quot;solid&quot;:{&quot;color&quot;:[1.0,1.0,1.0]}}},&quot;font&quot;:{&quot;path&quot;:&quot;&quot;,&quot;id&quot;:&quot;&quot;}},{&quot;size&quot;:15,&quot;range&quot;:[2,4],&quot;fill&quot;:{&quot;content&quot;:{&quot;solid&quot;:{&quot;color&quot;:[1.0,0.44313725490196076,0.0]}}},&quot;useLetterColor&quot;:true,&quot;font&quot;:{&quot;path&quot;:&quot;&quot;,&quot;id&quot;:&quot;&quot;}},{&quot;size&quot;:15,&quot;range&quot;:[4,9],&quot;fill&quot;:{&quot;content&quot;:{&quot;solid&quot;:{&quot;color&quot;:[1.0,1.0,1.0]}}},&quot;font&quot;:{&quot;path&quot;:&quot;&quot;,&quot;id&quot;:&quot;&quot;}}],&quot;text&quot;:&quot;创建文本富文本样式&quot;}</td>
<td class="MethodOfCaller">generateJianyingTextStyle</td>
<td class="FileOfCaller">JianyingAssistantService.java</td>
<td class="LineOfCaller">4321</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:03:51,964</td>
<td class="Message">剪映API执行成功 - JianyingToolboxController.addTextStyle, 耗时: 7ms</td>
<td class="MethodOfCaller">validateAccess</td>
<td class="FileOfCaller">JianyingAccessAspect.java</td>
<td class="LineOfCaller">77</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:03:52,263</td>
<td class="Message">剪映API访问成功 - Path: /jeecg-boot/api/jianying/timelines, IP: ************, AccessKey: JianyingAP***</td>
<td class="MethodOfCaller">logAccess</td>
<td class="FileOfCaller">JianyingAccessValidator.java</td>
<td class="LineOfCaller">119</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:03:52,264</td>
<td class="Message">剪映API访问验证通过 - Method: JianyingDataboxController.timelines, IP: unknown, Request: TimelinesRequest{accessKey=JianyingAP***, duration=15000000, num=5, start=null, type=null}</td>
<td class="MethodOfCaller">validateAccessKey</td>
<td class="FileOfCaller">JianyingAccessKeyAspect.java</td>
<td class="LineOfCaller">87</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:03:52,265</td>
<td class="Message">时间线生成器请求: TimelinesRequest{accessKey=JianyingAP***, duration=15000000, num=5, start=null, type=null}</td>
<td class="MethodOfCaller">timelines</td>
<td class="FileOfCaller">JianyingDataboxController.java</td>
<td class="LineOfCaller">188</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:03:52,265</td>
<td class="Message">开始生成时间线: TimelinesRequest{accessKey=JianyingAP***, duration=15000000, num=5, start=null, type=null}</td>
<td class="MethodOfCaller">timelines</td>
<td class="FileOfCaller">JianyingDataboxService.java</td>
<td class="LineOfCaller">634</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:03:52,266</td>
<td class="Message">提取的data对象: {&quot;all_timelines&quot;:[{&quot;start&quot;:0,&quot;end&quot;:15000000}],&quot;timelines&quot;:[{&quot;start&quot;:0,&quot;end&quot;:3000000},{&quot;start&quot;:3000000,&quot;end&quot;:6000000},{&quot;start&quot;:6000000,&quot;end&quot;:9000000},{&quot;start&quot;:9000000,&quot;end&quot;:12000000},{&quot;start&quot;:12000000,&quot;end&quot;:15000000}]}</td>
<td class="MethodOfCaller">timelines</td>
<td class="FileOfCaller">JianyingDataboxController.java</td>
<td class="LineOfCaller">194</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:03:52,266</td>
<td class="Message">剪映API执行成功 - JianyingDataboxController.timelines, 耗时: 3ms</td>
<td class="MethodOfCaller">validateAccess</td>
<td class="FileOfCaller">JianyingAccessAspect.java</td>
<td class="LineOfCaller">77</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:03:52,574</td>
<td class="Message">剪映API访问成功 - Path: /jeecg-boot/api/jianying/caption_infos, IP: *************, AccessKey: JianyingAP***</td>
<td class="MethodOfCaller">logAccess</td>
<td class="FileOfCaller">JianyingAccessValidator.java</td>
<td class="LineOfCaller">119</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:03:52,574</td>
<td class="Message">剪映API访问验证通过 - Method: JianyingDataboxController.captionInfos, IP: unknown, Request: CaptionInfosRequest{accessKey=JianyingAP***, textsCount=4, timelinesCount=5, fontSize=null}</td>
<td class="MethodOfCaller">validateAccessKey</td>
<td class="FileOfCaller">JianyingAccessKeyAspect.java</td>
<td class="LineOfCaller">87</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:03:52,574</td>
<td class="Message">字幕数据生成器请求: CaptionInfosRequest{accessKey=JianyingAP***, textsCount=4, timelinesCount=5, fontSize=null}</td>
<td class="MethodOfCaller">captionInfos</td>
<td class="FileOfCaller">JianyingDataboxController.java</td>
<td class="LineOfCaller">272</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:03:52,574</td>
<td class="Message">开始生成字幕数据: CaptionInfosRequest{accessKey=JianyingAP***, textsCount=4, timelinesCount=5, fontSize=null}</td>
<td class="MethodOfCaller">captionInfos</td>
<td class="FileOfCaller">JianyingDataboxService.java</td>
<td class="LineOfCaller">924</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:03:52,575</td>
<td class="Message">字幕数据生成 - 输入参数: texts数量=4, timelines数量=5</td>
<td class="MethodOfCaller">captionInfos</td>
<td class="FileOfCaller">JianyingDataboxService.java</td>
<td class="LineOfCaller">947</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:03:52,575</td>
<td class="Message">字幕数据生成完成 - 生成了5个字幕对象</td>
<td class="MethodOfCaller">captionInfos</td>
<td class="FileOfCaller">JianyingDataboxService.java</td>
<td class="LineOfCaller">1032</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:03:52,576</td>
<td class="Message">提取的data对象: {&quot;infos&quot;:&quot;[{\&quot;start\&quot;:0,\&quot;end\&quot;:3000000,\&quot;text\&quot;:\&quot;发现稳定版的 caption_infos 接口确实没有缩放、透明度、位置等参数！它只是生成字幕数据的JSON\&quot;},{\&quot;start\&quot;:3000000,\&quot;end\&quot;:6000000,\&quot;text\&quot;:\&quot;不涉及clip属性\&quot;},{\&quot;start\&quot;:6000000,\&quot;end\&quot;:9000000,\&quot;text\&quot;:\&quot;  那问题应该出现在使用这个接口生成的数据的地方\&quot;},{\&quot;start\&quot;:9000000,\&quot;end\&quot;:12000000,\&quot;text\&quot;:\&quot;让我查找稳定版的 add_captions 接口：\&quot;},{\&quot;start\&quot;:12000000,\&quot;end\&quot;:15000000,\&quot;text\&quot;:\&quot;\&quot;}]&quot;}</td>
<td class="MethodOfCaller">captionInfos</td>
<td class="FileOfCaller">JianyingDataboxController.java</td>
<td class="LineOfCaller">278</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:03:52,576</td>
<td class="Message">剪映API执行成功 - JianyingDataboxController.captionInfos, 耗时: 2ms</td>
<td class="MethodOfCaller">validateAccess</td>
<td class="FileOfCaller">JianyingAccessAspect.java</td>
<td class="LineOfCaller">77</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:03:52,933</td>
<td class="Message">剪映API访问成功 - Path: /jeecg-boot/api/jianying/add_captions, IP: **************, AccessKey: JianyingAP***</td>
<td class="MethodOfCaller">logAccess</td>
<td class="FileOfCaller">JianyingAccessValidator.java</td>
<td class="LineOfCaller">119</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:03:52,934</td>
<td class="Message">剪映API访问验证通过 - Method: JianyingToolboxController.addCaptions, IP: unknown, Request: AddCaptionsRequest{accessKey=JianyingAP***, draftUrl=https://aigcview.cn/jeecg-boot***, alpha=null, scaleX=null, scaleY=null}</td>
<td class="MethodOfCaller">validateAccessKey</td>
<td class="FileOfCaller">JianyingAccessKeyAspect.java</td>
<td class="LineOfCaller">87</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:03:52,934</td>
<td class="Message">批量添加字幕请求: AddCaptionsRequest{accessKey=JianyingAP***, draftUrl=https://aigcview.cn/jeecg-boot***, alpha=null, scaleX=null, scaleY=null}</td>
<td class="MethodOfCaller">addCaptions</td>
<td class="FileOfCaller">JianyingToolboxController.java</td>
<td class="LineOfCaller">281</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:03:52,934</td>
<td class="Message">开始批量添加字幕: AddCaptionsRequest{accessKey=JianyingAP***, draftUrl=https://aigcview.cn/jeecg-boot***, alpha=null, scaleX=null, scaleY=null}</td>
<td class="MethodOfCaller">addCaptions</td>
<td class="FileOfCaller">JianyingAssistantService.java</td>
<td class="LineOfCaller">1720</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:03:52,934</td>
<td class="Message">字幕参数 - alpha: 0.7, scaleX: 2.0, scaleY: 2.0, transformX: 50.0, transformY: 50.0</td>
<td class="MethodOfCaller">addCaptions</td>
<td class="FileOfCaller">JianyingAssistantService.java</td>
<td class="LineOfCaller">1739</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:03:52,934</td>
<td class="Message">开始下载草稿JSON: https://aigcview.cn/jeecg-boot/sys/common/jianying-file/jianying-assistant/drafts/2025/07/18/zj_draft_20250718_190351_c4b90ef5.json</td>
<td class="MethodOfCaller">downloadAndParseDraft</td>
<td class="FileOfCaller">CozeApiService.java</td>
<td class="LineOfCaller">841</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:03:52,934</td>
<td class="Message">检测到剪映草稿文件，使用内网TOS下载: https://aigcview.cn/jeecg-boot/sys/common/jianying-file/jianying-assistant/drafts/2025/07/18/zj_draft_20250718_190351_c4b90ef5.json</td>
<td class="MethodOfCaller">downloadAndParseDraft</td>
<td class="FileOfCaller">CozeApiService.java</td>
<td class="LineOfCaller">845</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:03:52,935</td>
<td class="Message">使用TOS SDK下载草稿: /jianying-assistant/drafts/2025/07/18/zj_draft_20250718_190351_c4b90ef5.json</td>
<td class="MethodOfCaller">downloadDraftViaTosSDK</td>
<td class="FileOfCaller">CozeApiService.java</td>
<td class="LineOfCaller">1906</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:03:52,935</td>
<td class="Message">开始使用内网TOS SDK下载剪映文件: jianying-assistant/drafts/2025/07/18/zj_draft_20250718_190351_c4b90ef5.json</td>
<td class="MethodOfCaller">downloadDraftFile</td>
<td class="FileOfCaller">TosService.java</td>
<td class="LineOfCaller">428</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:04:27,997</td>
<td class="Message">tos: request exception: javax.net.ssl.SSLHandshakeException: Remote host closed connection during handshake
</td>
<td class="MethodOfCaller">printAccessLogFailed</td>
<td class="FileOfCaller">RequestTransport.java</td>
<td class="LineOfCaller">267</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-07-18 19:04:28,001</td>
<td class="Message">TOS客户端异常: jianying-assistant/drafts/2025/07/18/zj_draft_20250718_190351_c4b90ef5.json</td>
<td class="MethodOfCaller">downloadDraftFile</td>
<td class="FileOfCaller">TosService.java</td>
<td class="LineOfCaller">463</td>
</tr>
<tr><td class="Exception" colspan="6">com.volcengine.tos.TosClientException: tos: request exception
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.volcengine.tos.internal.RequestHandler.doRequest(RequestHandler.java:110)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.volcengine.tos.internal.RequestHandler.doRequest(RequestHandler.java:89)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.volcengine.tos.internal.TosObjectRequestHandler.getObject(TosObjectRequestHandler.java:250)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.volcengine.tos.TOSV2Client.getObject(TOSV2Client.java:519)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.jianying.service.TosService.downloadDraftFile(TosService.java:441)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.jianying.service.CozeApiService.downloadDraftViaTosSDK(CozeApiService.java:1910)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.jianying.service.CozeApiService.downloadAndParseDraft(CozeApiService.java:857)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.jianying.service.JianyingAssistantService.addCaptions(JianyingAssistantService.java:1743)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.jianying.service.JianyingAssistantService$$FastClassBySpringCGLIB$$b93b33ed.invoke(&lt;generated&gt;)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:687)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.jianying.service.JianyingAssistantService$$EnhancerBySpringCGLIB$$9398e2cb.addCaptions(&lt;generated&gt;)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.jianying.controller.JianyingToolboxController.addCaptions(JianyingToolboxController.java:284)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.jianying.controller.JianyingToolboxController$$FastClassBySpringCGLIB$$48a06f70.invoke(&lt;generated&gt;)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:771)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:749)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:88)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.common.aspect.DictAspect.doAround(DictAspect.java:54)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.lang.reflect.Method.invoke(Method.java:498)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:644)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:633)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:70)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:749)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:88)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.jianying.aspect.JianyingAccessKeyAspect.validateAccessKey(JianyingAccessKeyAspect.java:91)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.lang.reflect.Method.invoke(Method.java:498)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:644)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:633)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:70)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:749)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:88)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.jianying.aspect.JianyingAccessAspect.validateAccess(JianyingAccessAspect.java:73)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.lang.reflect.Method.invoke(Method.java:498)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:644)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:633)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:70)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:749)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:95)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:749)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:691)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.jianying.controller.JianyingToolboxController$$EnhancerBySpringCGLIB$$98f2fd9b.addCaptions(&lt;generated&gt;)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.lang.reflect.Method.invoke(Method.java:498)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at javax.servlet.http.HttpServlet.service(HttpServlet.java:652)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:61)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:125)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:125)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AbstractShiroFilter.executeChain(AbstractShiroFilter.java:450)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AbstractShiroFilter$1.call(AbstractShiroFilter.java:365)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.subject.support.SubjectCallable.doCall(SubjectCallable.java:90)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.subject.support.SubjectCallable.call(SubjectCallable.java:83)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.subject.support.DelegatingSubject.execute(DelegatingSubject.java:387)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AbstractShiroFilter.doFilterInternal(AbstractShiroFilter.java:362)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:125)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:92)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:124)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.boot.actuate.web.trace.servlet.HttpTraceFilter.doFilterInternal(HttpTraceFilter.java:88)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:868)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1590)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.lang.Thread.run(Thread.java:748)
<br />Caused by: javax.net.ssl.SSLHandshakeException: Remote host closed connection during handshake
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.security.ssl.SSLSocketImpl.readRecord(SSLSocketImpl.java:994)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.security.ssl.SSLSocketImpl.performInitialHandshake(SSLSocketImpl.java:1367)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:1395)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:1379)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.connection.RealConnection.connectTls(RealConnection.kt:379)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.connection.RealConnection.establishProtocol(RealConnection.kt:337)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.connection.RealConnection.connect(RealConnection.kt:209)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.connection.ExchangeFinder.findConnection(ExchangeFinder.kt:226)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.connection.ExchangeFinder.findHealthyConnection(ExchangeFinder.kt:106)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.connection.ExchangeFinder.find(ExchangeFinder.kt:74)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.connection.RealCall.initExchange$okhttp(RealCall.kt:255)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.connection.ConnectInterceptor.intercept(ConnectInterceptor.kt:32)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.cache.CacheInterceptor.intercept(CacheInterceptor.kt:95)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.http.BridgeInterceptor.intercept(BridgeInterceptor.kt:83)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.http.RetryAndFollowUpInterceptor.intercept(RetryAndFollowUpInterceptor.kt:76)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.connection.RealCall.getResponseWithInterceptorChain$okhttp(RealCall.kt:201)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.connection.RealCall.execute(RealCall.kt:154)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.volcengine.tos.internal.RequestTransport.roundTrip(RequestTransport.java:187)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.volcengine.tos.internal.RequestHandler.doRequest(RequestHandler.java:108)
<br />&nbsp;&nbsp;&nbsp;&nbsp;	... 138 common frames omitted
<br />Caused by: java.io.EOFException: SSL peer shut down incorrectly
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.security.ssl.InputRecord.read(InputRecord.java:505)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.security.ssl.SSLSocketImpl.readRecord(SSLSocketImpl.java:975)
<br />&nbsp;&nbsp;&nbsp;&nbsp;	... 160 common frames omitted
</td></tr>
<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-07-18 19:04:28,004</td>
<td class="Message">TOS SDK下载草稿失败: /jianying-assistant/drafts/2025/07/18/zj_draft_20250718_190351_c4b90ef5.json</td>
<td class="MethodOfCaller">downloadDraftViaTosSDK</td>
<td class="FileOfCaller">CozeApiService.java</td>
<td class="LineOfCaller">1921</td>
</tr>
<tr><td class="Exception" colspan="6">java.lang.RuntimeException: TOS下载失败: tos: request exception
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.jianying.service.TosService.downloadDraftFile(TosService.java:464)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.jianying.service.CozeApiService.downloadDraftViaTosSDK(CozeApiService.java:1910)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.jianying.service.CozeApiService.downloadAndParseDraft(CozeApiService.java:857)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.jianying.service.JianyingAssistantService.addCaptions(JianyingAssistantService.java:1743)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.jianying.service.JianyingAssistantService$$FastClassBySpringCGLIB$$b93b33ed.invoke(&lt;generated&gt;)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:687)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.jianying.service.JianyingAssistantService$$EnhancerBySpringCGLIB$$9398e2cb.addCaptions(&lt;generated&gt;)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.jianying.controller.JianyingToolboxController.addCaptions(JianyingToolboxController.java:284)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.jianying.controller.JianyingToolboxController$$FastClassBySpringCGLIB$$48a06f70.invoke(&lt;generated&gt;)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:771)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:749)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:88)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.common.aspect.DictAspect.doAround(DictAspect.java:54)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.lang.reflect.Method.invoke(Method.java:498)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:644)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:633)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:70)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:749)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:88)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.jianying.aspect.JianyingAccessKeyAspect.validateAccessKey(JianyingAccessKeyAspect.java:91)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.lang.reflect.Method.invoke(Method.java:498)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:644)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:633)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:70)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:749)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:88)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.jianying.aspect.JianyingAccessAspect.validateAccess(JianyingAccessAspect.java:73)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.lang.reflect.Method.invoke(Method.java:498)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:644)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:633)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:70)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:749)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:95)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:749)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:691)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.jianying.controller.JianyingToolboxController$$EnhancerBySpringCGLIB$$98f2fd9b.addCaptions(&lt;generated&gt;)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.lang.reflect.Method.invoke(Method.java:498)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at javax.servlet.http.HttpServlet.service(HttpServlet.java:652)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:61)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:125)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:125)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AbstractShiroFilter.executeChain(AbstractShiroFilter.java:450)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AbstractShiroFilter$1.call(AbstractShiroFilter.java:365)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.subject.support.SubjectCallable.doCall(SubjectCallable.java:90)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.subject.support.SubjectCallable.call(SubjectCallable.java:83)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.subject.support.DelegatingSubject.execute(DelegatingSubject.java:387)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AbstractShiroFilter.doFilterInternal(AbstractShiroFilter.java:362)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:125)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:92)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:124)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.boot.actuate.web.trace.servlet.HttpTraceFilter.doFilterInternal(HttpTraceFilter.java:88)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:868)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1590)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.lang.Thread.run(Thread.java:748)
<br />Caused by: com.volcengine.tos.TosClientException: tos: request exception
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.volcengine.tos.internal.RequestHandler.doRequest(RequestHandler.java:110)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.volcengine.tos.internal.RequestHandler.doRequest(RequestHandler.java:89)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.volcengine.tos.internal.TosObjectRequestHandler.getObject(TosObjectRequestHandler.java:250)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.volcengine.tos.TOSV2Client.getObject(TOSV2Client.java:519)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.jianying.service.TosService.downloadDraftFile(TosService.java:441)
<br />&nbsp;&nbsp;&nbsp;&nbsp;	... 134 common frames omitted
<br />Caused by: javax.net.ssl.SSLHandshakeException: Remote host closed connection during handshake
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.security.ssl.SSLSocketImpl.readRecord(SSLSocketImpl.java:994)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.security.ssl.SSLSocketImpl.performInitialHandshake(SSLSocketImpl.java:1367)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:1395)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:1379)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.connection.RealConnection.connectTls(RealConnection.kt:379)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.connection.RealConnection.establishProtocol(RealConnection.kt:337)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.connection.RealConnection.connect(RealConnection.kt:209)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.connection.ExchangeFinder.findConnection(ExchangeFinder.kt:226)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.connection.ExchangeFinder.findHealthyConnection(ExchangeFinder.kt:106)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.connection.ExchangeFinder.find(ExchangeFinder.kt:74)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.connection.RealCall.initExchange$okhttp(RealCall.kt:255)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.connection.ConnectInterceptor.intercept(ConnectInterceptor.kt:32)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.cache.CacheInterceptor.intercept(CacheInterceptor.kt:95)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.http.BridgeInterceptor.intercept(BridgeInterceptor.kt:83)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.http.RetryAndFollowUpInterceptor.intercept(RetryAndFollowUpInterceptor.kt:76)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.connection.RealCall.getResponseWithInterceptorChain$okhttp(RealCall.kt:201)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.connection.RealCall.execute(RealCall.kt:154)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.volcengine.tos.internal.RequestTransport.roundTrip(RequestTransport.java:187)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.volcengine.tos.internal.RequestHandler.doRequest(RequestHandler.java:108)
<br />&nbsp;&nbsp;&nbsp;&nbsp;	... 138 common frames omitted
<br />Caused by: java.io.EOFException: SSL peer shut down incorrectly
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.security.ssl.InputRecord.read(InputRecord.java:505)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.security.ssl.SSLSocketImpl.readRecord(SSLSocketImpl.java:975)
<br />&nbsp;&nbsp;&nbsp;&nbsp;	... 160 common frames omitted
</td></tr>
<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-07-18 19:04:28,006</td>
<td class="Message">下载并解析草稿失败: https://aigcview.cn/jeecg-boot/sys/common/jianying-file/jianying-assistant/drafts/2025/07/18/zj_draft_20250718_190351_c4b90ef5.json</td>
<td class="MethodOfCaller">downloadAndParseDraft</td>
<td class="FileOfCaller">CozeApiService.java</td>
<td class="LineOfCaller">893</td>
</tr>
<tr><td class="Exception" colspan="6">java.lang.RuntimeException: TOS SDK下载草稿失败: TOS下载失败: tos: request exception
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.jianying.service.CozeApiService.downloadDraftViaTosSDK(CozeApiService.java:1922)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.jianying.service.CozeApiService.downloadAndParseDraft(CozeApiService.java:857)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.jianying.service.JianyingAssistantService.addCaptions(JianyingAssistantService.java:1743)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.jianying.service.JianyingAssistantService$$FastClassBySpringCGLIB$$b93b33ed.invoke(&lt;generated&gt;)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:687)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.jianying.service.JianyingAssistantService$$EnhancerBySpringCGLIB$$9398e2cb.addCaptions(&lt;generated&gt;)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.jianying.controller.JianyingToolboxController.addCaptions(JianyingToolboxController.java:284)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.jianying.controller.JianyingToolboxController$$FastClassBySpringCGLIB$$48a06f70.invoke(&lt;generated&gt;)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:771)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:749)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:88)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.common.aspect.DictAspect.doAround(DictAspect.java:54)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.lang.reflect.Method.invoke(Method.java:498)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:644)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:633)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:70)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:749)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:88)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.jianying.aspect.JianyingAccessKeyAspect.validateAccessKey(JianyingAccessKeyAspect.java:91)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.lang.reflect.Method.invoke(Method.java:498)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:644)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:633)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:70)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:749)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:88)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.jianying.aspect.JianyingAccessAspect.validateAccess(JianyingAccessAspect.java:73)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.lang.reflect.Method.invoke(Method.java:498)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:644)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:633)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:70)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:749)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:95)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:749)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:691)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.jianying.controller.JianyingToolboxController$$EnhancerBySpringCGLIB$$98f2fd9b.addCaptions(&lt;generated&gt;)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.lang.reflect.Method.invoke(Method.java:498)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at javax.servlet.http.HttpServlet.service(HttpServlet.java:652)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:61)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:125)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:125)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AbstractShiroFilter.executeChain(AbstractShiroFilter.java:450)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AbstractShiroFilter$1.call(AbstractShiroFilter.java:365)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.subject.support.SubjectCallable.doCall(SubjectCallable.java:90)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.subject.support.SubjectCallable.call(SubjectCallable.java:83)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.subject.support.DelegatingSubject.execute(DelegatingSubject.java:387)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AbstractShiroFilter.doFilterInternal(AbstractShiroFilter.java:362)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:125)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:92)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:124)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.boot.actuate.web.trace.servlet.HttpTraceFilter.doFilterInternal(HttpTraceFilter.java:88)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:868)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1590)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.lang.Thread.run(Thread.java:748)
<br />Caused by: java.lang.RuntimeException: TOS下载失败: tos: request exception
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.jianying.service.TosService.downloadDraftFile(TosService.java:464)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.jianying.service.CozeApiService.downloadDraftViaTosSDK(CozeApiService.java:1910)
<br />&nbsp;&nbsp;&nbsp;&nbsp;	... 133 common frames omitted
<br />Caused by: com.volcengine.tos.TosClientException: tos: request exception
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.volcengine.tos.internal.RequestHandler.doRequest(RequestHandler.java:110)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.volcengine.tos.internal.RequestHandler.doRequest(RequestHandler.java:89)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.volcengine.tos.internal.TosObjectRequestHandler.getObject(TosObjectRequestHandler.java:250)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.volcengine.tos.TOSV2Client.getObject(TOSV2Client.java:519)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.jianying.service.TosService.downloadDraftFile(TosService.java:441)
<br />&nbsp;&nbsp;&nbsp;&nbsp;	... 134 common frames omitted
<br />Caused by: javax.net.ssl.SSLHandshakeException: Remote host closed connection during handshake
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.security.ssl.SSLSocketImpl.readRecord(SSLSocketImpl.java:994)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.security.ssl.SSLSocketImpl.performInitialHandshake(SSLSocketImpl.java:1367)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:1395)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:1379)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.connection.RealConnection.connectTls(RealConnection.kt:379)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.connection.RealConnection.establishProtocol(RealConnection.kt:337)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.connection.RealConnection.connect(RealConnection.kt:209)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.connection.ExchangeFinder.findConnection(ExchangeFinder.kt:226)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.connection.ExchangeFinder.findHealthyConnection(ExchangeFinder.kt:106)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.connection.ExchangeFinder.find(ExchangeFinder.kt:74)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.connection.RealCall.initExchange$okhttp(RealCall.kt:255)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.connection.ConnectInterceptor.intercept(ConnectInterceptor.kt:32)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.cache.CacheInterceptor.intercept(CacheInterceptor.kt:95)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.http.BridgeInterceptor.intercept(BridgeInterceptor.kt:83)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.http.RetryAndFollowUpInterceptor.intercept(RetryAndFollowUpInterceptor.kt:76)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.connection.RealCall.getResponseWithInterceptorChain$okhttp(RealCall.kt:201)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.connection.RealCall.execute(RealCall.kt:154)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.volcengine.tos.internal.RequestTransport.roundTrip(RequestTransport.java:187)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.volcengine.tos.internal.RequestHandler.doRequest(RequestHandler.java:108)
<br />&nbsp;&nbsp;&nbsp;&nbsp;	... 138 common frames omitted
<br />Caused by: java.io.EOFException: SSL peer shut down incorrectly
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.security.ssl.InputRecord.read(InputRecord.java:505)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.security.ssl.SSLSocketImpl.readRecord(SSLSocketImpl.java:975)
<br />&nbsp;&nbsp;&nbsp;&nbsp;	... 160 common frames omitted
</td></tr>
<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-07-18 19:04:28,009</td>
<td class="Message">批量添加字幕失败</td>
<td class="MethodOfCaller">addCaptions</td>
<td class="FileOfCaller">JianyingAssistantService.java</td>
<td class="LineOfCaller">1791</td>
</tr>
<tr><td class="Exception" colspan="6">java.lang.RuntimeException: 下载并解析草稿失败: TOS SDK下载草稿失败: TOS下载失败: tos: request exception
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.jianying.service.CozeApiService.downloadAndParseDraft(CozeApiService.java:894)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.jianying.service.JianyingAssistantService.addCaptions(JianyingAssistantService.java:1743)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.jianying.service.JianyingAssistantService$$FastClassBySpringCGLIB$$b93b33ed.invoke(&lt;generated&gt;)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:687)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.jianying.service.JianyingAssistantService$$EnhancerBySpringCGLIB$$9398e2cb.addCaptions(&lt;generated&gt;)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.jianying.controller.JianyingToolboxController.addCaptions(JianyingToolboxController.java:284)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.jianying.controller.JianyingToolboxController$$FastClassBySpringCGLIB$$48a06f70.invoke(&lt;generated&gt;)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:771)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:749)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:88)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.common.aspect.DictAspect.doAround(DictAspect.java:54)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.lang.reflect.Method.invoke(Method.java:498)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:644)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:633)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:70)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:749)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:88)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.jianying.aspect.JianyingAccessKeyAspect.validateAccessKey(JianyingAccessKeyAspect.java:91)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.lang.reflect.Method.invoke(Method.java:498)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:644)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:633)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:70)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:749)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:88)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.jianying.aspect.JianyingAccessAspect.validateAccess(JianyingAccessAspect.java:73)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.lang.reflect.Method.invoke(Method.java:498)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:644)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:633)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:70)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:749)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:95)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:749)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:691)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.jianying.controller.JianyingToolboxController$$EnhancerBySpringCGLIB$$98f2fd9b.addCaptions(&lt;generated&gt;)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.lang.reflect.Method.invoke(Method.java:498)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at javax.servlet.http.HttpServlet.service(HttpServlet.java:652)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:61)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:125)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:125)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AbstractShiroFilter.executeChain(AbstractShiroFilter.java:450)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AbstractShiroFilter$1.call(AbstractShiroFilter.java:365)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.subject.support.SubjectCallable.doCall(SubjectCallable.java:90)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.subject.support.SubjectCallable.call(SubjectCallable.java:83)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.subject.support.DelegatingSubject.execute(DelegatingSubject.java:387)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.AbstractShiroFilter.doFilterInternal(AbstractShiroFilter.java:362)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:125)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:92)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:124)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.boot.actuate.web.trace.servlet.HttpTraceFilter.doFilterInternal(HttpTraceFilter.java:88)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:542)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:868)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1590)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.lang.Thread.run(Thread.java:748)
<br />Caused by: java.lang.RuntimeException: TOS SDK下载草稿失败: TOS下载失败: tos: request exception
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.jianying.service.CozeApiService.downloadDraftViaTosSDK(CozeApiService.java:1922)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.jianying.service.CozeApiService.downloadAndParseDraft(CozeApiService.java:857)
<br />&nbsp;&nbsp;&nbsp;&nbsp;	... 132 common frames omitted
<br />Caused by: java.lang.RuntimeException: TOS下载失败: tos: request exception
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.jianying.service.TosService.downloadDraftFile(TosService.java:464)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.jianying.service.CozeApiService.downloadDraftViaTosSDK(CozeApiService.java:1910)
<br />&nbsp;&nbsp;&nbsp;&nbsp;	... 133 common frames omitted
<br />Caused by: com.volcengine.tos.TosClientException: tos: request exception
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.volcengine.tos.internal.RequestHandler.doRequest(RequestHandler.java:110)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.volcengine.tos.internal.RequestHandler.doRequest(RequestHandler.java:89)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.volcengine.tos.internal.TosObjectRequestHandler.getObject(TosObjectRequestHandler.java:250)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.volcengine.tos.TOSV2Client.getObject(TOSV2Client.java:519)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.jianying.service.TosService.downloadDraftFile(TosService.java:441)
<br />&nbsp;&nbsp;&nbsp;&nbsp;	... 134 common frames omitted
<br />Caused by: javax.net.ssl.SSLHandshakeException: Remote host closed connection during handshake
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.security.ssl.SSLSocketImpl.readRecord(SSLSocketImpl.java:994)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.security.ssl.SSLSocketImpl.performInitialHandshake(SSLSocketImpl.java:1367)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:1395)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:1379)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.connection.RealConnection.connectTls(RealConnection.kt:379)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.connection.RealConnection.establishProtocol(RealConnection.kt:337)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.connection.RealConnection.connect(RealConnection.kt:209)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.connection.ExchangeFinder.findConnection(ExchangeFinder.kt:226)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.connection.ExchangeFinder.findHealthyConnection(ExchangeFinder.kt:106)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.connection.ExchangeFinder.find(ExchangeFinder.kt:74)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.connection.RealCall.initExchange$okhttp(RealCall.kt:255)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.connection.ConnectInterceptor.intercept(ConnectInterceptor.kt:32)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.cache.CacheInterceptor.intercept(CacheInterceptor.kt:95)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.http.BridgeInterceptor.intercept(BridgeInterceptor.kt:83)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.http.RetryAndFollowUpInterceptor.intercept(RetryAndFollowUpInterceptor.kt:76)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.http.RealInterceptorChain.proceed(RealInterceptorChain.kt:109)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.connection.RealCall.getResponseWithInterceptorChain$okhttp(RealCall.kt:201)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at okhttp3.internal.connection.RealCall.execute(RealCall.kt:154)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.volcengine.tos.internal.RequestTransport.roundTrip(RequestTransport.java:187)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at com.volcengine.tos.internal.RequestHandler.doRequest(RequestHandler.java:108)
<br />&nbsp;&nbsp;&nbsp;&nbsp;	... 138 common frames omitted
<br />Caused by: java.io.EOFException: SSL peer shut down incorrectly
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.security.ssl.InputRecord.read(InputRecord.java:505)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at sun.security.ssl.SSLSocketImpl.readRecord(SSLSocketImpl.java:975)
<br />&nbsp;&nbsp;&nbsp;&nbsp;	... 160 common frames omitted
</td></tr>
<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:04:28,011</td>
<td class="Message">剪映API执行成功 - JianyingToolboxController.addCaptions, 耗时: 35078ms</td>
<td class="MethodOfCaller">validateAccess</td>
<td class="FileOfCaller">JianyingAccessAspect.java</td>
<td class="LineOfCaller">77</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:11:45,547</td>
<td class="Message">Shutting down ExecutorService &#39;jmReportTaskExecutor&#39;</td>
<td class="MethodOfCaller">shutdown</td>
<td class="FileOfCaller">ExecutorConfigurationSupport.java</td>
<td class="LineOfCaller">218</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:11:45,550</td>
<td class="Message">Shutting down Quartz Scheduler</td>
<td class="MethodOfCaller">destroy</td>
<td class="FileOfCaller">SchedulerFactoryBean.java</td>
<td class="LineOfCaller">845</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:11:45,550</td>
<td class="Message">Scheduler MyScheduler_$_DESKTOP-G0NDD8J1752836522455 shutting down.</td>
<td class="MethodOfCaller">shutdown</td>
<td class="FileOfCaller">QuartzScheduler.java</td>
<td class="LineOfCaller">666</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:11:45,551</td>
<td class="Message">Scheduler MyScheduler_$_DESKTOP-G0NDD8J1752836522455 paused.</td>
<td class="MethodOfCaller">standby</td>
<td class="FileOfCaller">QuartzScheduler.java</td>
<td class="LineOfCaller">585</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:11:45,551</td>
<td class="Message">Scheduler MyScheduler_$_DESKTOP-G0NDD8J1752836522455 shutdown complete.</td>
<td class="MethodOfCaller">shutdown</td>
<td class="FileOfCaller">QuartzScheduler.java</td>
<td class="LineOfCaller">740</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:11:45,558</td>
<td class="Message">dynamic-datasource start closing ....</td>
<td class="MethodOfCaller">destroy</td>
<td class="FileOfCaller">DynamicRoutingDataSource.java</td>
<td class="LineOfCaller">217</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:11:45,562</td>
<td class="Message">{dataSource-1} closing ...</td>
<td class="MethodOfCaller">close</td>
<td class="FileOfCaller">DruidDataSource.java</td>
<td class="LineOfCaller">2029</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:11:45,571</td>
<td class="Message">{dataSource-1} closed</td>
<td class="MethodOfCaller">close</td>
<td class="FileOfCaller">DruidDataSource.java</td>
<td class="LineOfCaller">2101</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:11:45,571</td>
<td class="Message">dynamic-datasource all closed success,bye</td>
<td class="MethodOfCaller">destroy</td>
<td class="FileOfCaller">DynamicRoutingDataSource.java</td>
<td class="LineOfCaller">221</td>
</tr>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html>
  <head>
    <title>Logback Log Messages</title>
<style  type="text/css">
table { margin-left: 2em; margin-right: 2em; border-left: 2px solid #AAA; }
TR.even { background: #FFFFFF; }
TR.odd { background: #EAEAEA; }
TR.warn TD.Level, TR.error TD.Level, TR.fatal TD.Level {font-weight: bold; color: #FF4040 }
TD { padding-right: 1ex; padding-left: 1ex; border-right: 2px solid #AAA; }
TD.Time, TD.Date { text-align: right; font-family: courier, monospace; font-size: smaller; }
TD.Thread { text-align: left; }
TD.Level { text-align: right; }
TD.Logger { text-align: left; }
TR.header { background: #596ED5; color: #FFF; font-weight: bold; font-size: larger; }
TD.Exception { background: #A2AEE8; font-family: courier, monospace;}
</style>

  </head>
<body>
<hr/>
<p>Log session start time Fri Jul 18 19:12:01 CST 2025</p><p></p>

<table cellspacing="0">
<tr class="header">
<td class="Level">Level</td>
<td class="Date">Date</td>
<td class="Message">Message</td>
<td class="MethodOfCaller">MethodOfCaller</td>
<td class="FileOfCaller">FileOfCaller</td>
<td class="LineOfCaller">LineOfCaller</td>
</tr>


<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:01,341</td>
<td class="Message">HV000001: Hibernate Validator 6.1.6.Final</td>
<td class="MethodOfCaller">&lt;clinit&gt;</td>
<td class="FileOfCaller">Version.java</td>
<td class="LineOfCaller">21</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:01,368</td>
<td class="Message">Starting JeecgSystemApplication on DESKTOP-G0NDD8J with PID 23412 (D:\AigcView_zj\AigcViewRd\jeecg-boot-module-system\target\classes started by Administrator in D:\AigcView_zj\AigcViewRd)</td>
<td class="MethodOfCaller">logStarting</td>
<td class="FileOfCaller">StartupInfoLogger.java</td>
<td class="LineOfCaller">55</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:01,369</td>
<td class="Message">The following profiles are active: dev</td>
<td class="MethodOfCaller">logStartupProfileInfo</td>
<td class="FileOfCaller">SpringApplication.java</td>
<td class="LineOfCaller">655</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-07-18 19:12:01,816</td>
<td class="Message">For Jackson Kotlin classes support please add &quot;com.fasterxml.jackson.module:jackson-module-kotlin&quot; to the classpath</td>
<td class="MethodOfCaller">warn</td>
<td class="FileOfCaller">CompositeLog.java</td>
<td class="LineOfCaller">127</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:03,281</td>
<td class="Message">Multiple Spring Data modules found, entering strict repository configuration mode!</td>
<td class="MethodOfCaller">multipleStoresDetected</td>
<td class="FileOfCaller">RepositoryConfigurationDelegate.java</td>
<td class="LineOfCaller">249</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:03,283</td>
<td class="Message">Bootstrapping Spring Data Redis repositories in DEFAULT mode.</td>
<td class="MethodOfCaller">registerRepositoriesIn</td>
<td class="FileOfCaller">RepositoryConfigurationDelegate.java</td>
<td class="LineOfCaller">127</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:03,436</td>
<td class="Message">Finished Spring Data repository scanning in 142ms. Found 0 Redis repository interfaces.</td>
<td class="MethodOfCaller">registerRepositoriesIn</td>
<td class="FileOfCaller">RepositoryConfigurationDelegate.java</td>
<td class="LineOfCaller">187</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:03,588</td>
<td class="Message"> ******************* init miniDao config [ begin ] *********************** </td>
<td class="MethodOfCaller">miniDaoBeanScannerConfigurer</td>
<td class="FileOfCaller">MinidaoAutoConfiguration.java</td>
<td class="LineOfCaller">23</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:03,589</td>
<td class="Message"> ------ minidao.base-package ------- org.jeecg.modules.jmreport.*</td>
<td class="MethodOfCaller">miniDaoBeanScannerConfigurer</td>
<td class="FileOfCaller">MinidaoAutoConfiguration.java</td>
<td class="LineOfCaller">25</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:03,590</td>
<td class="Message"> *******************  init miniDao config  [ end ] *********************** </td>
<td class="MethodOfCaller">miniDaoBeanScannerConfigurer</td>
<td class="FileOfCaller">MinidaoAutoConfiguration.java</td>
<td class="LineOfCaller">42</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:03,679</td>
<td class="Message">register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDao }</td>
<td class="MethodOfCaller">doScan</td>
<td class="FileOfCaller">MiniDaoClassPathMapperScanner.java</td>
<td class="LineOfCaller">48</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:03,679</td>
<td class="Message">register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDataSourceDao }</td>
<td class="MethodOfCaller">doScan</td>
<td class="FileOfCaller">MiniDaoClassPathMapperScanner.java</td>
<td class="LineOfCaller">48</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:03,679</td>
<td class="Message">register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDbDao }</td>
<td class="MethodOfCaller">doScan</td>
<td class="FileOfCaller">MiniDaoClassPathMapperScanner.java</td>
<td class="LineOfCaller">48</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:03,679</td>
<td class="Message">register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDbFieldDao }</td>
<td class="MethodOfCaller">doScan</td>
<td class="FileOfCaller">MiniDaoClassPathMapperScanner.java</td>
<td class="LineOfCaller">48</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:03,679</td>
<td class="Message">register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDbParamDao }</td>
<td class="MethodOfCaller">doScan</td>
<td class="FileOfCaller">MiniDaoClassPathMapperScanner.java</td>
<td class="LineOfCaller">48</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:03,679</td>
<td class="Message">register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDictDao }</td>
<td class="MethodOfCaller">doScan</td>
<td class="FileOfCaller">MiniDaoClassPathMapperScanner.java</td>
<td class="LineOfCaller">48</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:03,679</td>
<td class="Message">register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportDictItemDao }</td>
<td class="MethodOfCaller">doScan</td>
<td class="FileOfCaller">MiniDaoClassPathMapperScanner.java</td>
<td class="LineOfCaller">48</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:03,680</td>
<td class="Message">register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportLinkDao }</td>
<td class="MethodOfCaller">doScan</td>
<td class="FileOfCaller">MiniDaoClassPathMapperScanner.java</td>
<td class="LineOfCaller">48</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:03,680</td>
<td class="Message">register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportMapDao }</td>
<td class="MethodOfCaller">doScan</td>
<td class="FileOfCaller">MiniDaoClassPathMapperScanner.java</td>
<td class="LineOfCaller">48</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:03,680</td>
<td class="Message">register minidao name is { org.jeecg.modules.jmreport.desreport.dao.JimuReportShareDao }</td>
<td class="MethodOfCaller">doScan</td>
<td class="FileOfCaller">MiniDaoClassPathMapperScanner.java</td>
<td class="LineOfCaller">48</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:03,880</td>
<td class="Message">Bean &#39;(inner bean)#5438fa43&#39; of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:03,883</td>
<td class="Message">Bean &#39;jimuReportDao&#39; of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:03,884</td>
<td class="Message">Bean &#39;(inner bean)#5438fa43#1&#39; of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:03,885</td>
<td class="Message">Bean &#39;jimuReportDataSourceDao&#39; of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:03,886</td>
<td class="Message">Bean &#39;(inner bean)#5438fa43#2&#39; of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:03,886</td>
<td class="Message">Bean &#39;jimuReportDbDao&#39; of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:03,887</td>
<td class="Message">Bean &#39;(inner bean)#5438fa43#3&#39; of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:03,887</td>
<td class="Message">Bean &#39;jimuReportDbFieldDao&#39; of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:03,888</td>
<td class="Message">Bean &#39;(inner bean)#5438fa43#4&#39; of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:03,889</td>
<td class="Message">Bean &#39;jimuReportDbParamDao&#39; of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:03,891</td>
<td class="Message">Bean &#39;(inner bean)#5438fa43#5&#39; of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:03,892</td>
<td class="Message">Bean &#39;jimuReportDictDao&#39; of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:03,892</td>
<td class="Message">Bean &#39;(inner bean)#5438fa43#6&#39; of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:03,893</td>
<td class="Message">Bean &#39;jimuReportDictItemDao&#39; of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:03,894</td>
<td class="Message">Bean &#39;(inner bean)#5438fa43#7&#39; of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:03,894</td>
<td class="Message">Bean &#39;jimuReportLinkDao&#39; of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:03,895</td>
<td class="Message">Bean &#39;(inner bean)#5438fa43#8&#39; of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:03,896</td>
<td class="Message">Bean &#39;jimuReportMapDao&#39; of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:03,896</td>
<td class="Message">Bean &#39;(inner bean)#5438fa43#9&#39; of type [org.jeecgframework.minidao.aop.MiniDaoHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:03,897</td>
<td class="Message">Bean &#39;jimuReportShareDao&#39; of type [org.jeecgframework.minidao.factory.MiniDaoBeanFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:03,917</td>
<td class="Message">Bean &#39;spring.redis-org.springframework.boot.autoconfigure.data.redis.RedisProperties&#39; of type [org.springframework.boot.autoconfigure.data.redis.RedisProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:03,920</td>
<td class="Message">Bean &#39;org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration&#39; of type [org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:03,993</td>
<td class="Message">Bean &#39;lettuceClientResources&#39; of type [io.lettuce.core.resource.DefaultClientResources] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:04,058</td>
<td class="Message">Bean &#39;redisConnectionFactory&#39; of type [org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:04,061</td>
<td class="Message">Bean &#39;shiroConfig&#39; of type [org.jeecg.config.shiro.ShiroConfig$$EnhancerBySpringCGLIB$$81541165] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:04,103</td>
<td class="Message">Bean &#39;shiroRealm&#39; of type [org.jeecg.config.shiro.ShiroRealm] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:04,551</td>
<td class="Message">===============(1)创建缓存管理器RedisCacheManager</td>
<td class="MethodOfCaller">redisCacheManager</td>
<td class="FileOfCaller">ShiroConfig.java</td>
<td class="LineOfCaller">215</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:04,552</td>
<td class="Message">===============(2)创建RedisManager,连接Redis..</td>
<td class="MethodOfCaller">redisManager</td>
<td class="FileOfCaller">ShiroConfig.java</td>
<td class="LineOfCaller">233</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:04,555</td>
<td class="Message">Bean &#39;redisManager&#39; of type [org.crazycake.shiro.RedisManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:04,558</td>
<td class="Message">Bean &#39;securityManager&#39; of type [org.apache.shiro.web.mgt.DefaultWebSecurityManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:04,596</td>
<td class="Message">Bean &#39;authorizationAttributeSourceAdvisor&#39; of type [org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:04,787</td>
<td class="Message">Bean &#39;spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties&#39; of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:04,793</td>
<td class="Message">Bean &#39;com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration&#39; of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$8ba63a3d] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:04,801</td>
<td class="Message">Bean &#39;dsProcessor&#39; of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:04,813</td>
<td class="Message">Bean &#39;redisConfig&#39; of type [org.jeecg.common.modules.redis.config.RedisConfig$$EnhancerBySpringCGLIB$$9eb62195] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:04,849</td>
<td class="Message">Bean &#39;org.apache.shiro.spring.boot.autoconfigure.ShiroBeanAutoConfiguration&#39; of type [org.apache.shiro.spring.boot.autoconfigure.ShiroBeanAutoConfiguration$$EnhancerBySpringCGLIB$$3530a71c] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:04,851</td>
<td class="Message">Bean &#39;eventBus&#39; of type [org.apache.shiro.event.support.DefaultEventBus] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">335</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:05,131</td>
<td class="Message">Tomcat initialized with port(s): 8080 (http)</td>
<td class="MethodOfCaller">initialize</td>
<td class="FileOfCaller">TomcatWebServer.java</td>
<td class="LineOfCaller">108</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:05,139</td>
<td class="Message">Initializing ProtocolHandler [&quot;http-nio-8080&quot;]</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">DirectJDKLog.java</td>
<td class="LineOfCaller">173</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:05,139</td>
<td class="Message">Starting service [Tomcat]</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">DirectJDKLog.java</td>
<td class="LineOfCaller">173</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:05,140</td>
<td class="Message">Starting Servlet engine: [Apache Tomcat/9.0.39]</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">DirectJDKLog.java</td>
<td class="LineOfCaller">173</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:05,297</td>
<td class="Message">Initializing Spring embedded WebApplicationContext</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">DirectJDKLog.java</td>
<td class="LineOfCaller">173</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:05,297</td>
<td class="Message">Root WebApplicationContext: initialization completed in 3880 ms</td>
<td class="MethodOfCaller">prepareWebApplicationContext</td>
<td class="FileOfCaller">ServletWebServerApplicationContext.java</td>
<td class="LineOfCaller">285</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:05,871</td>
<td class="Message">{dataSource-1,master} inited</td>
<td class="MethodOfCaller">init</td>
<td class="FileOfCaller">DruidDataSource.java</td>
<td class="LineOfCaller">994</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:05,872</td>
<td class="Message">dynamic-datasource - load a datasource named [master] success</td>
<td class="MethodOfCaller">addDataSource</td>
<td class="FileOfCaller">DynamicRoutingDataSource.java</td>
<td class="LineOfCaller">132</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:05,873</td>
<td class="Message">dynamic-datasource initial loaded [1] datasource,primary datasource named [master]</td>
<td class="MethodOfCaller">afterPropertiesSet</td>
<td class="FileOfCaller">DynamicRoutingDataSource.java</td>
<td class="LineOfCaller">237</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:07,291</td>
<td class="Message"> --- redis config init --- </td>
<td class="MethodOfCaller">redisTemplate</td>
<td class="FileOfCaller">RedisConfig.java</td>
<td class="LineOfCaller">76</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:08,785</td>
<td class="Message">开始初始化TOS客户端...</td>
<td class="MethodOfCaller">initTosClient</td>
<td class="FileOfCaller">TosService.java</td>
<td class="LineOfCaller">86</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:08,785</td>
<td class="Message">TOS配置验证通过 - Region: cn-shanghai, Endpoint: tos-cn-shanghai.volces.com, Bucket: aigcview-tos</td>
<td class="MethodOfCaller">initTosClient</td>
<td class="FileOfCaller">TosService.java</td>
<td class="LineOfCaller">105</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:08,956</td>
<td class="Message">外网TOS客户端初始化成功！</td>
<td class="MethodOfCaller">initTosClient</td>
<td class="FileOfCaller">TosService.java</td>
<td class="LineOfCaller">115</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:08,960</td>
<td class="Message">内网TOS客户端初始化成功 - Endpoint: tos-cn-shanghai.ivolces.com</td>
<td class="MethodOfCaller">initTosClient</td>
<td class="FileOfCaller">TosService.java</td>
<td class="LineOfCaller">125</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:08,961</td>
<td class="Message">正在测试TOS连接...</td>
<td class="MethodOfCaller">testTosConnection</td>
<td class="FileOfCaller">TosService.java</td>
<td class="LineOfCaller">591</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:08,961</td>
<td class="Message">TOS连接测试成功！</td>
<td class="MethodOfCaller">testTosConnection</td>
<td class="FileOfCaller">TosService.java</td>
<td class="LineOfCaller">593</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:10,516</td>
<td class="Message">🔍 开始初始化敏感词库...</td>
<td class="MethodOfCaller">init</td>
<td class="FileOfCaller">SensitiveWordServiceImpl.java</td>
<td class="LineOfCaller">48</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:10,886</td>
<td class="Message">✅ 敏感词库初始化完成</td>
<td class="MethodOfCaller">init</td>
<td class="FileOfCaller">SensitiveWordServiceImpl.java</td>
<td class="LineOfCaller">64</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:11,483</td>
<td class="Message">初始化预定义特效映射完成，共 4 个特效</td>
<td class="MethodOfCaller">initDefaultEffectMapping</td>
<td class="FileOfCaller">JianyingEffectSearchService.java</td>
<td class="LineOfCaller">86</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:11,495</td>
<td class="Message">RestTemplate初始化完成，支持TOS文件下载</td>
<td class="MethodOfCaller">initRestTemplate</td>
<td class="FileOfCaller">CozeApiService.java</td>
<td class="LineOfCaller">64</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:11,641</td>
<td class="Message">开始初始化TOS客户端...</td>
<td class="MethodOfCaller">initTosClient</td>
<td class="FileOfCaller">JianyingProTosService.java</td>
<td class="LineOfCaller">86</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:11,642</td>
<td class="Message">TOS配置验证通过 - Region: cn-shanghai, Endpoint: tos-cn-shanghai.volces.com, Bucket: aigcview-tos</td>
<td class="MethodOfCaller">initTosClient</td>
<td class="FileOfCaller">JianyingProTosService.java</td>
<td class="LineOfCaller">105</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:11,644</td>
<td class="Message">外网TOS客户端初始化成功！</td>
<td class="MethodOfCaller">initTosClient</td>
<td class="FileOfCaller">JianyingProTosService.java</td>
<td class="LineOfCaller">115</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:11,646</td>
<td class="Message">内网TOS客户端初始化成功 - Endpoint: tos-cn-shanghai.ivolces.com</td>
<td class="MethodOfCaller">initTosClient</td>
<td class="FileOfCaller">JianyingProTosService.java</td>
<td class="LineOfCaller">125</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:11,647</td>
<td class="Message">正在测试TOS连接...</td>
<td class="MethodOfCaller">testTosConnection</td>
<td class="FileOfCaller">JianyingProTosService.java</td>
<td class="LineOfCaller">627</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:11,647</td>
<td class="Message">TOS连接测试成功！</td>
<td class="MethodOfCaller">testTosConnection</td>
<td class="FileOfCaller">JianyingProTosService.java</td>
<td class="LineOfCaller">629</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:11,662</td>
<td class="Message">超级剪映小助手 - 初始化预定义特效映射完成，共 4 个特效</td>
<td class="MethodOfCaller">initDefaultEffectMapping</td>
<td class="FileOfCaller">JianyingProEffectSearchService.java</td>
<td class="LineOfCaller">87</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:11,672</td>
<td class="Message">超级剪映小助手 - RestTemplate初始化完成，支持TOS文件下载</td>
<td class="MethodOfCaller">initRestTemplate</td>
<td class="FileOfCaller">JianyingProCozeApiService.java</td>
<td class="LineOfCaller">59</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:12,310</td>
<td class="Message">Using default implementation for ThreadExecutor</td>
<td class="MethodOfCaller">instantiate</td>
<td class="FileOfCaller">StdSchedulerFactory.java</td>
<td class="LineOfCaller">1220</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:12,314</td>
<td class="Message">Job execution threads will use class loader of thread: main</td>
<td class="MethodOfCaller">initialize</td>
<td class="FileOfCaller">SimpleThreadPool.java</td>
<td class="LineOfCaller">268</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:12,325</td>
<td class="Message">Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl</td>
<td class="MethodOfCaller">&lt;init&gt;</td>
<td class="FileOfCaller">SchedulerSignalerImpl.java</td>
<td class="LineOfCaller">61</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:12,326</td>
<td class="Message">Quartz Scheduler v.2.3.2 created.</td>
<td class="MethodOfCaller">&lt;init&gt;</td>
<td class="FileOfCaller">QuartzScheduler.java</td>
<td class="LineOfCaller">229</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:12,331</td>
<td class="Message">Using db table-based data access locking (synchronization).</td>
<td class="MethodOfCaller">initialize</td>
<td class="FileOfCaller">JobStoreSupport.java</td>
<td class="LineOfCaller">672</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:12,333</td>
<td class="Message">JobStoreCMT initialized.</td>
<td class="MethodOfCaller">initialize</td>
<td class="FileOfCaller">JobStoreCMT.java</td>
<td class="LineOfCaller">145</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:12,334</td>
<td class="Message">Scheduler meta-data: Quartz Scheduler (v2.3.2) &#39;MyScheduler&#39; with instanceId &#39;DESKTOP-G0NDD8J1752837132313&#39;
  Scheduler class: &#39;org.quartz.core.QuartzScheduler&#39; - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool &#39;org.quartz.simpl.SimpleThreadPool&#39; - with 10 threads.
  Using job-store &#39;org.springframework.scheduling.quartz.LocalDataSourceJobStore&#39; - which supports persistence. and is clustered.
</td>
<td class="MethodOfCaller">initialize</td>
<td class="FileOfCaller">QuartzScheduler.java</td>
<td class="LineOfCaller">294</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:12,335</td>
<td class="Message">Quartz scheduler &#39;MyScheduler&#39; initialized from an externally provided properties instance.</td>
<td class="MethodOfCaller">instantiate</td>
<td class="FileOfCaller">StdSchedulerFactory.java</td>
<td class="LineOfCaller">1374</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:12,335</td>
<td class="Message">Quartz scheduler version: 2.3.2</td>
<td class="MethodOfCaller">instantiate</td>
<td class="FileOfCaller">StdSchedulerFactory.java</td>
<td class="LineOfCaller">1378</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:12,335</td>
<td class="Message">JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@62ec4146</td>
<td class="MethodOfCaller">setJobFactory</td>
<td class="FileOfCaller">QuartzScheduler.java</td>
<td class="LineOfCaller">2293</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:15,854</td>
<td class="Message"> --- Init JimuReport Config --- </td>
<td class="MethodOfCaller">afterPropertiesSet</td>
<td class="FileOfCaller">JimuReportConfiguration.java</td>
<td class="LineOfCaller">55</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:17,020</td>
<td class="Message">Exposing 2 endpoint(s) beneath base path &#39;/actuator&#39;</td>
<td class="MethodOfCaller">&lt;init&gt;</td>
<td class="FileOfCaller">EndpointLinksResolver.java</td>
<td class="LineOfCaller">58</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:17,188</td>
<td class="Message"> 代码生成器数据库连接，使用application.yml的DB配置 ###################</td>
<td class="MethodOfCaller">initCodeGenerateDbConfig</td>
<td class="FileOfCaller">CodeGenerateDbConfig.java</td>
<td class="LineOfCaller">46</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:17,220</td>
<td class="Message">Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2ControllerWebMvc#getDocumentation(String, HttpServletRequest)]</td>
<td class="MethodOfCaller">initHandlerMethods</td>
<td class="FileOfCaller">WebMvcPropertySourcedRequestMappingHandlerMapping.java</td>
<td class="LineOfCaller">69</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:17,325</td>
<td class="Message">---创建线程池---</td>
<td class="MethodOfCaller">asyncServiceExecutor</td>
<td class="FileOfCaller">JmReportExecutorConfig.java</td>
<td class="LineOfCaller">21</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:17,327</td>
<td class="Message">Initializing ExecutorService</td>
<td class="MethodOfCaller">initialize</td>
<td class="FileOfCaller">ExecutorConfigurationSupport.java</td>
<td class="LineOfCaller">181</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:17,329</td>
<td class="Message">Initializing ExecutorService &#39;jmReportTaskExecutor&#39;</td>
<td class="MethodOfCaller">initialize</td>
<td class="FileOfCaller">ExecutorConfigurationSupport.java</td>
<td class="LineOfCaller">181</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:18,577</td>
<td class="Message">Starting ProtocolHandler [&quot;http-nio-8080&quot;]</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">DirectJDKLog.java</td>
<td class="LineOfCaller">173</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:18,608</td>
<td class="Message">Tomcat started on port(s): 8080 (http) with context path &#39;/jeecg-boot&#39;</td>
<td class="MethodOfCaller">start</td>
<td class="FileOfCaller">TomcatWebServer.java</td>
<td class="LineOfCaller">220</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:18,611</td>
<td class="Message">Documentation plugins bootstrapped</td>
<td class="MethodOfCaller">start</td>
<td class="FileOfCaller">DocumentationPluginsBootstrapper.java</td>
<td class="LineOfCaller">93</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:18,614</td>
<td class="Message">Found 1 custom documentation plugin(s)</td>
<td class="MethodOfCaller">bootstrapDocumentationPlugins</td>
<td class="FileOfCaller">AbstractDocumentationPluginsBootstrapper.java</td>
<td class="LineOfCaller">79</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:18,862</td>
<td class="Message">Scanning for api listing references</td>
<td class="MethodOfCaller">scan</td>
<td class="FileOfCaller">ApiListingReferenceScanner.java</td>
<td class="LineOfCaller">44</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,077</td>
<td class="Message">Generating unique operation named: addUsingPOST_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,088</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,094</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,112</td>
<td class="Message">Generating unique operation named: addUsingPOST_2</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,116</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,118</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,120</td>
<td class="Message">Generating unique operation named: editUsingPUT_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,122</td>
<td class="Message">Generating unique operation named: getByUserIdUsingGET_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,126</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_2</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,132</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_2</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,145</td>
<td class="Message">Generating unique operation named: addUsingPOST_3</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,149</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_2</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,151</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_2</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,155</td>
<td class="Message">Generating unique operation named: editUsingPUT_2</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,156</td>
<td class="Message">Generating unique operation named: getByUserIdUsingGET_2</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,165</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_3</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,171</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_3</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,187</td>
<td class="Message">Generating unique operation named: addUsingPOST_4</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,190</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_3</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,191</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_3</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,193</td>
<td class="Message">Generating unique operation named: editUsingPUT_3</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,198</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_4</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,204</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_4</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,218</td>
<td class="Message">Generating unique operation named: addUsingPOST_5</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,220</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_4</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,222</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_4</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,224</td>
<td class="Message">Generating unique operation named: editUsingPUT_4</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,226</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_5</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,232</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_5</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,243</td>
<td class="Message">Generating unique operation named: addUsingPOST_6</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,247</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_5</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,249</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_5</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,252</td>
<td class="Message">Generating unique operation named: editUsingPUT_5</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,261</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_6</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,266</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_6</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,278</td>
<td class="Message">Generating unique operation named: addUsingPOST_7</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,283</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_6</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,285</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_6</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,287</td>
<td class="Message">Generating unique operation named: editUsingPUT_6</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,289</td>
<td class="Message">Generating unique operation named: getByReferrerIdUsingGET_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,294</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_7</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,301</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_7</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,316</td>
<td class="Message">Generating unique operation named: getUsageStatsUsingGET_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,334</td>
<td class="Message">Generating unique operation named: addUsingPOST_8</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,337</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_7</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,338</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_7</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,340</td>
<td class="Message">Generating unique operation named: editUsingPUT_7</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,342</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_8</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,347</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_8</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,356</td>
<td class="Message">Generating unique operation named: addUsingPOST_9</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,358</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_8</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,360</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_8</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,361</td>
<td class="Message">Generating unique operation named: editUsingPUT_8</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,363</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_9</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,369</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_9</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,386</td>
<td class="Message">Generating unique operation named: addUsingPOST_10</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,391</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_9</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,393</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_9</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,395</td>
<td class="Message">Generating unique operation named: editUsingPUT_9</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,404</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_10</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,410</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_10</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,425</td>
<td class="Message">Generating unique operation named: addUsingPOST_11</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,435</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_10</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,437</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_10</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,438</td>
<td class="Message">Generating unique operation named: editUsingPUT_10</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,450</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_11</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,455</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_11</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,462</td>
<td class="Message">Generating unique operation named: addUsingPOST_12</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,464</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_11</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,465</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_11</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,467</td>
<td class="Message">Generating unique operation named: editUsingPUT_11</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,468</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_12</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,472</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_12</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,482</td>
<td class="Message">Generating unique operation named: addUsingPOST_13</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,483</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_12</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,484</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_12</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,485</td>
<td class="Message">Generating unique operation named: editUsingPUT_12</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,487</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_13</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,494</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_13</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,500</td>
<td class="Message">Generating unique operation named: addUsingPOST_14</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,501</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_13</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,504</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_13</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,505</td>
<td class="Message">Generating unique operation named: editUsingPUT_13</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,507</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_14</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,512</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_14</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,522</td>
<td class="Message">Generating unique operation named: addUsingPOST_15</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,524</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_14</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,526</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_14</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,528</td>
<td class="Message">Generating unique operation named: editUsingPUT_14</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,529</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_15</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,535</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_15</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,673</td>
<td class="Message">Generating unique operation named: addAudiosUsingPOST_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,678</td>
<td class="Message">Generating unique operation named: addCaptionsUsingPOST_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,681</td>
<td class="Message">Generating unique operation named: addEffectsUsingPOST_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,685</td>
<td class="Message">Generating unique operation named: addImagesUsingPOST_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,688</td>
<td class="Message">Generating unique operation named: addKeyframesUsingPOST_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,702</td>
<td class="Message">Generating unique operation named: addVideosUsingPOST_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,778</td>
<td class="Message">Generating unique operation named: addUsingPOST_16</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,781</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_15</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,783</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_15</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,785</td>
<td class="Message">Generating unique operation named: editUsingPUT_15</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,787</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_16</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,791</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_16</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,799</td>
<td class="Message">Generating unique operation named: addUsingPOST_17</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,801</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_16</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,803</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_16</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,806</td>
<td class="Message">Generating unique operation named: editUsingPUT_16</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,808</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_17</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,813</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_17</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,818</td>
<td class="Message">Generating unique operation named: addUsingPOST_18</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,822</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_17</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,824</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_17</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,825</td>
<td class="Message">Generating unique operation named: editUsingPUT_17</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,827</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_18</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,831</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_18</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,837</td>
<td class="Message">Generating unique operation named: addUsingPOST_19</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,840</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_18</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,841</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_18</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,843</td>
<td class="Message">Generating unique operation named: editUsingPUT_18</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,844</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_19</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,848</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_19</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,858</td>
<td class="Message">Generating unique operation named: addUsingPOST_20</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,860</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_19</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,861</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_19</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,863</td>
<td class="Message">Generating unique operation named: editUsingPUT_19</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,864</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_20</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,869</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_20</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,881</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_21</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,883</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_20</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,888</td>
<td class="Message">Generating unique operation named: addUsingPOST_21</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,891</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_20</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,894</td>
<td class="Message">Generating unique operation named: editUsingPUT_20</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,896</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_21</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,911</td>
<td class="Message">Generating unique operation named: queryPageListUsingGET_22</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,914</td>
<td class="Message">Generating unique operation named: addUsingPOST_22</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,916</td>
<td class="Message">Generating unique operation named: deleteUsingDELETE_21</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,918</td>
<td class="Message">Generating unique operation named: deleteBatchUsingDELETE_21</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,922</td>
<td class="Message">Generating unique operation named: editUsingPUT_21</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,942</td>
<td class="Message">Generating unique operation named: queryByIdUsingGET_22</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:19,978</td>
<td class="Message">Generating unique operation named: getReferralStatsUsingGET_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:20,012</td>
<td class="Message">Generating unique operation named: sendEmailCodeUsingPOST_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:20,013</td>
<td class="Message">Generating unique operation named: sendSmsCodeUsingPOST_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:20,018</td>
<td class="Message">Generating unique operation named: verifyCodeUsingPOST_1</td>
<td class="MethodOfCaller">startingWith</td>
<td class="FileOfCaller">CachingOperationNameGenerator.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:22,007</td>
<td class="Message">Started JeecgSystemApplication in 21.272 seconds (JVM running for 22.525)</td>
<td class="MethodOfCaller">logStarted</td>
<td class="FileOfCaller">StartupInfoLogger.java</td>
<td class="LineOfCaller">61</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:22,016</td>
<td class="Message">
----------------------------------------------------------
	Application Jeecg-Boot is running! Access URLs:
	Local: 		http://localhost:8080/jeecg-boot/
	External: 	http://**********:8080/jeecg-boot/
	Swagger文档: 	http://**********:8080/jeecg-boot/doc.html
----------------------------------------------------------</td>
<td class="MethodOfCaller">main</td>
<td class="FileOfCaller">JeecgSystemApplication.java</td>
<td class="LineOfCaller">39</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:23,130</td>
<td class="Message">Initializing Spring DispatcherServlet &#39;dispatcherServlet&#39;</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">DirectJDKLog.java</td>
<td class="LineOfCaller">173</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:23,131</td>
<td class="Message">Initializing Servlet &#39;dispatcherServlet&#39;</td>
<td class="MethodOfCaller">initServletBean</td>
<td class="FileOfCaller">FrameworkServlet.java</td>
<td class="LineOfCaller">525</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-07-18 19:12:23,150</td>
<td class="Message">Completed initialization in 19 ms</td>
<td class="MethodOfCaller">initServletBean</td>
<td class="FileOfCaller">FrameworkServlet.java</td>
<td class="LineOfCaller">547</td>
</tr>
