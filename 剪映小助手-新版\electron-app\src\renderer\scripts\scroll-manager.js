/**
 * 滚动管理器 - 优化滚动性能，提供流畅的滚动体验
 * 
 * 主要优化：
 * 1. DOM元素缓存 - 减少重复查询
 * 2. requestAnimationFrame - 确保最佳渲染时机
 * 3. 防抖机制 - 避免重复滚动操作
 * 4. Intersection Observer - 智能检测可见性
 * 5. 优化滚动参数 - 提升用户体验
 */
class ScrollManager {
    constructor() {
        this.isScrolling = false
        this.scrollTimeout = null
        this.cachedElements = {}
        this.intersectionObserver = null
        this.performanceMetrics = {
            scrollCount: 0,
            totalTime: 0
        }
        
        console.log('🚀 [SCROLL] 滚动管理器初始化开始')
        this.init()
    }

    /**
     * 初始化滚动管理器
     */
    init() {
        this.initElements()
        this.setupIntersectionObserver()
        this.addCSSOptimizations()
        console.log('✅ [SCROLL] 滚动管理器初始化完成')
    }

    /**
     * 初始化DOM元素缓存
     */
    initElements() {
        // 使用requestIdleCallback在空闲时缓存元素，避免阻塞主线程
        if (window.requestIdleCallback) {
            window.requestIdleCallback(() => {
                this.cacheElements()
            })
        } else {
            // 降级到setTimeout
            setTimeout(() => this.cacheElements(), 0)
        }
    }

    /**
     * 缓存DOM元素
     */
    cacheElements() {
        try {
            this.cachedElements.logsSection = document.querySelector('.download-logs')
            this.cachedElements.logsContent = document.getElementById('logs-content')
            
            console.log('📦 [SCROLL] DOM元素缓存完成:', {
                hasLogsSection: !!this.cachedElements.logsSection,
                hasLogsContent: !!this.cachedElements.logsContent
            })
        } catch (error) {
            console.error('❌ [SCROLL] DOM元素缓存失败:', error)
        }
    }

    /**
     * 设置Intersection Observer用于智能检测元素可见性
     */
    setupIntersectionObserver() {
        if (!('IntersectionObserver' in window)) {
            console.warn('⚠️ [SCROLL] 浏览器不支持Intersection Observer，跳过设置')
            return
        }

        try {
            this.intersectionObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting && entry.intersectionRatio > 0.5) {
                        console.log('👁️ [SCROLL] 目标区域已可见，取消待执行的滚动')
                        this.cancelPendingScroll()
                    }
                })
            }, { 
                threshold: [0.3, 0.5, 0.7],  // 多个阈值，更精确的检测
                rootMargin: '0px 0px -50px 0px'  // 底部留50px边距
            })
            
            console.log('👁️ [SCROLL] Intersection Observer设置完成')
        } catch (error) {
            console.error('❌ [SCROLL] Intersection Observer设置失败:', error)
        }
    }

    /**
     * 添加CSS优化
     */
    addCSSOptimizations() {
        try {
            const style = document.createElement('style')
            style.textContent = `
                .download-logs {
                    /* 启用硬件加速 */
                    transform: translateZ(0);
                    /* 优化滚动性能 */
                    will-change: scroll-position;
                    /* 确保平滑滚动 */
                    scroll-behavior: smooth;
                }
                
                #logs-content {
                    /* 优化内容滚动 */
                    overflow-y: auto;
                    scroll-behavior: smooth;
                }
            `
            document.head.appendChild(style)
            console.log('🎨 [SCROLL] CSS优化已应用')
        } catch (error) {
            console.error('❌ [SCROLL] CSS优化应用失败:', error)
        }
    }

    /**
     * 主要的滚动方法 - ⚡ 瞬间滚动版本
     */
    scrollToLogs() {
        const startTime = performance.now()

        console.log('⚡ [SCROLL] 瞬间滚动到日志区域')

        // 🚀 检查元素是否存在
        if (!this.cachedElements.logsSection) {
            console.warn('⚠️ [SCROLL] 重新缓存元素')
            this.cacheElements()
        }

        // ⚡ 如果元素存在，立即执行滚动
        if (this.cachedElements.logsSection) {
            this.executeScroll(startTime)
        } else {
            console.error('❌ [SCROLL] 日志区域元素未找到')
        }
    }

    /**
     * 执行优化的滚动操作
     */
    performOptimizedScroll(startTime) {
        // 检查是否正在滚动
        if (this.isScrolling) {
            console.log('🔄 [SCROLL] 滚动进行中，跳过重复操作')
            return
        }
        
        // 检查元素是否存在，不存在则重新缓存
        if (!this.cachedElements.logsSection) {
            console.warn('⚠️ [SCROLL] 日志区域元素未找到，重新缓存')
            this.cacheElements()
            
            // 延迟重试
            setTimeout(() => {
                if (this.cachedElements.logsSection) {
                    this.performOptimizedScroll(startTime)
                } else {
                    console.error('❌ [SCROLL] 重新缓存后仍未找到元素，滚动失败')
                }
            }, 50)
            return
        }
        
        // 使用Intersection Observer检查可见性
        if (this.intersectionObserver) {
            this.intersectionObserver.observe(this.cachedElements.logsSection)
        }
        
        // 使用requestAnimationFrame确保在最佳时机执行
        if (window.requestAnimationFrame) {
            requestAnimationFrame(() => {
                this.executeScroll(startTime)
            })
        } else {
            // 降级处理
            this.executeScroll(startTime)
        }
    }

    /**
     * 执行实际的滚动操作 - 🚀 超快速版本
     */
    executeScroll(startTime) {
        this.isScrolling = true

        const { logsSection, logsContent } = this.cachedElements

        try {
            console.log('⚡ [SCROLL] 执行瞬间滚动')

            // 🚀 瞬间滚动参数 - 追求最快响应速度
            const fastScrollOptions = {
                behavior: 'auto',        // 瞬间滚动，无动画延迟
                block: 'start',         // 滚动到顶部，确保日志区域完全可见
                inline: 'nearest'       // 避免水平滚动
            }

            // ⚡ 直接滚动到日志区域 - 一步到位
            logsSection.scrollIntoView(fastScrollOptions)
            console.log('⚡ [SCROLL] 瞬间滚动完成')

            // 🚀 如果有日志内容，也瞬间滚动到底部
            if (logsContent) {
                logsContent.scrollTo({
                    top: logsContent.scrollHeight,
                    behavior: 'auto'  // 瞬间滚动
                })
                console.log('📜 [SCROLL] 日志内容瞬间滚动到底部')
            }

            // 记录性能指标
            this.recordPerformance(startTime)

            // 🚀 立即完成，无延迟
            this.completeScroll()
            
        } catch (error) {
            console.error('❌ [SCROLL] 滚动执行失败:', error)
            this.completeScroll()
        }
    }

    /**
     * 完成滚动操作，清理状态
     */
    completeScroll() {
        this.isScrolling = false
        
        // 停止观察
        if (this.intersectionObserver && this.cachedElements.logsSection) {
            this.intersectionObserver.unobserve(this.cachedElements.logsSection)
        }
        
        console.log('✅ [SCROLL] 滚动操作完成')
    }

    /**
     * 取消待执行的滚动
     */
    cancelPendingScroll() {
        if (this.scrollTimeout) {
            clearTimeout(this.scrollTimeout)
            this.scrollTimeout = null
            console.log('🚫 [SCROLL] 已取消待执行的滚动')
        }
    }

    /**
     * 记录性能指标
     */
    recordPerformance(startTime) {
        const endTime = performance.now()
        const duration = endTime - startTime
        
        this.performanceMetrics.scrollCount++
        this.performanceMetrics.totalTime += duration
        
        const avgTime = this.performanceMetrics.totalTime / this.performanceMetrics.scrollCount
        
        console.log(`📊 [SCROLL] 性能指标: 本次${duration.toFixed(2)}ms, 平均${avgTime.toFixed(2)}ms, 总计${this.performanceMetrics.scrollCount}次`)
    }

    /**
     * 销毁滚动管理器，清理资源
     */
    destroy() {
        this.cancelPendingScroll()

        if (this.intersectionObserver) {
            this.intersectionObserver.disconnect()
        }

        this.cachedElements = {}
        this.isScrolling = false

        console.log('🗑️ [SCROLL] 滚动管理器已销毁')
    }
}

// 🌍 将ScrollManager设为全局可用
window.ScrollManager = new ScrollManager()

// 🧪 添加快速测试方法
window.testScroll = function() {
    console.log('🧪 测试瞬间滚动功能')
    if (window.ScrollManager) {
        window.ScrollManager.scrollToLogs()
    }
}
