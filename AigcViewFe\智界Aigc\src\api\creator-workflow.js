import { axios } from '@/utils/request'

/**
 * 创作者中心 - 工作流管理 API
 */

// API 基础路径
const API_BASE = '/api/creator/workflow'

/**
 * 获取智能体的工作流列表
 * @param {string} agentId - 智能体ID
 * @returns {Promise}
 */
export function getWorkflowList(agentId) {
  return axios({
    url: `${API_BASE}/list/${agentId}`,
    method: 'GET'
  })
}

/**
 * 创建工作流
 * @param {Object} data - 工作流数据
 * @param {string} data.agentId - 智能体ID
 * @param {string} data.workflowName - 工作流名称
 * @param {string} data.workflowDescription - 工作流描述
 * @returns {Promise}
 */
export function createWorkflow(data) {
  return axios({
    url: `${API_BASE}/add`,
    method: 'POST',
    data
  })
}

/**
 * 更新工作流
 * @param {string} id - 工作流ID
 * @param {Object} data - 工作流数据
 * @returns {Promise}
 */
export function updateWorkflow(id, data) {
  return axios({
    url: `${API_BASE}/edit/${id}`,
    method: 'PUT',
    data
  })
}

/**
 * 删除工作流
 * @param {string} id - 工作流ID
 * @returns {Promise}
 */
export function deleteWorkflow(id) {
  return axios({
    url: `${API_BASE}/delete/${id}`,
    method: 'DELETE'
  })
}

/**
 * 获取工作流详情
 * @param {string} id - 工作流ID
 * @returns {Promise}
 */
export function getWorkflowDetail(id) {
  return axios({
    url: `${API_BASE}/detail/${id}`,
    method: 'GET'
  })
}

/**
 * 上传工作流文件
 * @param {File} file - 文件对象
 * @param {string} agentId - 智能体ID
 * @returns {Promise}
 */
export function uploadWorkflowFile(file, agentId) {
  const formData = new FormData()
  formData.append('file', file)
  formData.append('agentId', agentId)
  
  return axios({
    url: `${API_BASE}/upload`,
    method: 'POST',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    timeout: 300000 // 5分钟超时，因为文件可能比较大
  })
}

// 导出所有工作流API函数
export default {
  getWorkflowList,
  createWorkflow,
  updateWorkflow,
  deleteWorkflow,
  getWorkflowDetail,
  uploadWorkflowFile
}
