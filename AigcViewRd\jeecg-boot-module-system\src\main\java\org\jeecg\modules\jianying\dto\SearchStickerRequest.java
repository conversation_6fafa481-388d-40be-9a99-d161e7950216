package org.jeecg.modules.jianying.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

/**
 * 搜索贴纸请求参数
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SearchStickerRequest extends BaseJianyingRequest {
    
    @ApiModelProperty(value = "关键词（必填）", required = true, example = "爱心")
    @NotBlank(message = "keyword不能为空")
    @JsonProperty("keyword")
    private String zjKeyword;
    
    @Override
    public String getSummary() {
        return "SearchStickerRequest{" +
               "accessKey=" + (getAccessKey() != null && getAccessKey().length() > 10 ? 
                              getAccessKey().substring(0, 10) + "***" : "null") +
               ", keyword=" + zjKeyword +
               "}";
    }
}
