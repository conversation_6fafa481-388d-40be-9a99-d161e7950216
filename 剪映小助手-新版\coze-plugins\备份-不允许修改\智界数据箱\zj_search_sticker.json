{"openapi": "3.0.0", "info": {"title": "剪映小助手_智界数据箱 - 贴纸搜索器", "description": "查找贴纸", "version": "1.0.0"}, "servers": [{"url": "https://www.aigcview.com"}], "paths": {"/jeecg-boot/api/jianying/search_sticker": {"post": {"summary": "搜索贴纸", "description": "查找贴纸", "operationId": "searchSticker_zj", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"access_key": {"type": "string", "description": "访问密钥（必填）", "example": "JianyingAPI_2025_SecureAccess_AigcView", "default": "JianyingAPI_2025_SecureAccess_AigcView"}, "zj_keyword": {"type": "string", "description": "关键词（必填）", "example": "爱心"}}, "required": ["access_key", "zj_keyword"], "additionalProperties": false}}}}, "responses": {"200": {"description": "成功搜索贴纸", "content": {"application/json": {"schema": {"type": "object", "properties": {"data": {"type": "array", "description": "搜索到的贴纸列表", "items": {"type": "object", "properties": {"title": {"type": "string", "description": "贴纸标题"}, "sticker": {"type": "object", "description": "贴纸详细信息", "properties": {"large_image": {"type": "object", "properties": {"image_url": {"type": "string", "description": "大图URL"}}}, "preview_cover": {"type": "string", "description": "预览封面"}, "sticker_package": {"type": "object", "properties": {"size": {"type": "integer", "description": "文件大小"}, "width_per_frame": {"type": "integer", "description": "每帧宽度"}, "height_per_frame": {"type": "integer", "description": "每帧高度"}}}, "sticker_type": {"type": "integer", "description": "贴纸类型"}, "track_thumbnail": {"type": "string", "description": "缩略图URL"}}}, "sticker_id": {"type": "string", "description": "贴纸ID"}}}}, "message": {"type": "string", "description": "响应消息"}}, "required": ["data", "message"]}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "description": "操作是否成功", "example": false}, "error": {"type": "string", "description": "错误信息"}, "code": {"type": "string", "description": "错误代码"}}, "required": ["success", "error"]}}}}, "500": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "description": "操作是否成功", "example": false}, "error": {"type": "string", "description": "错误信息"}, "code": {"type": "string", "description": "错误代码"}}, "required": ["success", "error"]}}}}}}}}}