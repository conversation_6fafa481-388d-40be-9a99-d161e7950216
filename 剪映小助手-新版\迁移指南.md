# 迁移指南 - API参数规范化

## 📋 迁移概述

本指南帮助您从旧版本（使用zj_前缀参数）迁移到新版本（标准参数名）。

**迁移版本**: v1.x.x → v2.0.0  
**迁移类型**: 破坏性更新  
**预计迁移时间**: 30-60分钟

## 🚨 重要提醒

⚠️ **破坏性更新**: 新版本不再支持zj_前缀参数  
⚠️ **必须迁移**: 所有使用旧参数名的代码都需要更新  
⚠️ **测试验证**: 迁移后必须进行全面测试

## 📝 迁移检查清单

### 准备阶段
- [ ] 备份现有代码和配置
- [ ] 确认当前使用的API接口
- [ ] 准备测试环境
- [ ] 下载最新版本文档

### 代码迁移
- [ ] 更新API调用代码
- [ ] 更新参数名称
- [ ] 更新错误处理逻辑
- [ ] 更新测试用例

### 配置迁移
- [ ] 更新Coze插件配置
- [ ] 重新导入JSON配置文件
- [ ] 更新环境变量
- [ ] 更新配置文件

### 验证测试
- [ ] 单元测试通过
- [ ] 集成测试通过
- [ ] 端到端测试通过
- [ ] 性能测试通过

### 部署上线
- [ ] 部署到测试环境
- [ ] 验证功能正常
- [ ] 部署到生产环境
- [ ] 监控系统状态

## 🔧 具体迁移步骤

### 1. 代码迁移

#### JavaScript/Node.js 示例

**修改前**:
```javascript
// 旧版本API调用
const response = await fetch('/api/jianying/add_videos', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    access_key: 'JianyingAPI_2025_SecureAccess_AigcView',
    zj_draft_url: 'https://example.com/draft/123',
    zj_video_infos: '[{"video_url": "https://example.com/video.mp4"}]',
    zj_alpha: 1.0,
    zj_scale_x: 1.0,
    zj_scale_y: 1.0
  })
});
```

**修改后**:
```javascript
// 新版本API调用
const response = await fetch('/api/jianying/add_videos', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    access_key: 'JianyingAPI_2025_SecureAccess_AigcView',
    draft_url: 'https://example.com/draft/123',
    video_infos: '[{"video_url": "https://example.com/video.mp4"}]',
    alpha: 1.0,
    scale_x: 1.0,
    scale_y: 1.0
  })
});
```

#### Python 示例

**修改前**:
```python
# 旧版本API调用
import requests

data = {
    'access_key': 'JianyingAPI_2025_SecureAccess_AigcView',
    'zj_draft_url': 'https://example.com/draft/123',
    'zj_audio_infos': '[{"audio_url": "https://example.com/audio.mp3"}]'
}

response = requests.post('/api/jianying/add_audios', json=data)
```

**修改后**:
```python
# 新版本API调用
import requests

data = {
    'access_key': 'JianyingAPI_2025_SecureAccess_AigcView',
    'draft_url': 'https://example.com/draft/123',
    'audio_infos': '[{"audio_url": "https://example.com/audio.mp3"}]'
}

response = requests.post('/api/jianying/add_audios', json=data)
```

### 2. 批量替换脚本

#### 使用sed命令（Linux/Mac）
```bash
# 批量替换zj_前缀
find . -name "*.js" -type f -exec sed -i 's/zj_draft_url/draft_url/g' {} \;
find . -name "*.js" -type f -exec sed -i 's/zj_video_infos/video_infos/g' {} \;
find . -name "*.js" -type f -exec sed -i 's/zj_audio_infos/audio_infos/g' {} \;
# ... 继续其他参数
```

#### 使用PowerShell（Windows）
```powershell
# 批量替换zj_前缀
Get-ChildItem -Path . -Filter "*.js" -Recurse | ForEach-Object {
    (Get-Content $_.FullName) -replace 'zj_draft_url', 'draft_url' | Set-Content $_.FullName
    (Get-Content $_.FullName) -replace 'zj_video_infos', 'video_infos' | Set-Content $_.FullName
    # ... 继续其他参数
}
```

### 3. Coze插件迁移

#### 步骤1: 备份现有配置
```bash
# 导出现有插件配置
cp -r coze-plugins/智界工具箱 coze-plugins/智界工具箱-backup
```

#### 步骤2: 更新配置文件
1. 下载最新的JSON配置文件
2. 在Coze平台删除旧插件
3. 重新导入新的JSON配置文件

#### 步骤3: 验证配置
1. 测试每个API接口
2. 确认参数映射正确
3. 验证响应格式

### 4. 错误处理更新

**修改前**:
```javascript
if (error.message.includes('zj_draft_url不能为空')) {
  console.error('草稿地址参数错误');
}
```

**修改后**:
```javascript
if (error.message.includes('draft_url不能为空')) {
  console.error('草稿地址参数错误');
}
```

## 🧪 测试验证

### 1. 单元测试更新

```javascript
// 更新测试用例
describe('API Tests', () => {
  test('add_videos should work with new parameters', async () => {
    const params = {
      access_key: 'test_key',
      draft_url: 'https://test.com/draft/123',  // 新参数名
      video_infos: '[{"video_url": "test.mp4"}]'  // 新参数名
    };
    
    const response = await api.addVideos(params);
    expect(response.status).toBe(200);
  });
});
```

### 2. 集成测试

```bash
# 运行完整的API测试套件
npm test

# 运行特定的API测试
npm test -- --grep "add_videos"
```

### 3. 手动测试清单

- [ ] create_draft - 创建草稿
- [ ] add_videos - 添加视频
- [ ] add_audios - 添加音频
- [ ] add_images - 添加图片
- [ ] add_captions - 添加字幕
- [ ] add_effects - 添加特效
- [ ] add_masks - 添加蒙版
- [ ] add_sticker - 添加贴纸
- [ ] add_keyframes - 添加关键帧
- [ ] add_text_style - 文本样式
- [ ] easy_create_material - 快速创建
- [ ] gen_video - 视频渲染
- [ ] gen_video_status - 查询状态
- [ ] get_audio_duration - 音频时长
- [ ] get_image_animations - 图片动画
- [ ] get_text_animations - 文字动画
- [ ] save_draft - 保存草稿

## 🚨 常见问题

### Q1: 迁移后API返回400错误
**A**: 检查是否还在使用旧的zj_前缀参数名

### Q2: Coze插件无法正常工作
**A**: 确保重新导入了最新的JSON配置文件

### Q3: 部分功能正常，部分功能异常
**A**: 可能存在遗漏的参数名，使用搜索功能检查所有zj_前缀

### Q4: 测试环境正常，生产环境异常
**A**: 确认生产环境已部署最新版本的后端代码

## 🔍 迁移验证工具

### 参数检查脚本
```bash
#!/bin/bash
# check_migration.sh - 检查是否还有zj_前缀参数

echo "检查JavaScript文件..."
grep -r "zj_" --include="*.js" . && echo "发现zj_前缀参数，需要更新" || echo "JavaScript文件检查通过"

echo "检查JSON配置文件..."
grep -r "zj_" --include="*.json" . && echo "发现zj_前缀参数，需要更新" || echo "JSON文件检查通过"

echo "检查Python文件..."
grep -r "zj_" --include="*.py" . && echo "发现zj_前缀参数，需要更新" || echo "Python文件检查通过"
```

### API测试脚本
```javascript
// test_migration.js - 验证迁移是否成功
const apis = [
  'create_draft',
  'add_videos', 
  'add_audios',
  // ... 其他API
];

async function testMigration() {
  for (const api of apis) {
    try {
      const result = await testAPI(api);
      console.log(`✅ ${api}: 迁移成功`);
    } catch (error) {
      console.log(`❌ ${api}: 迁移失败 - ${error.message}`);
    }
  }
}

testMigration();
```

## 📞 技术支持

如果在迁移过程中遇到问题，请联系：

- **技术支持**: 智界AigcView团队
- **文档地址**: https://www.aigcview.com/docs
- **问题反馈**: 通过官方渠道提交问题

---

**迁移成功后，您将享受到更加标准化和易用的API接口！**
