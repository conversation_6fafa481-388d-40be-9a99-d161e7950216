<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简化轮播图测试</title>
    <script src="https://unpkg.com/vue@2/dist/vue.js"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
        }
        
        .test-info {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .simple-carousel {
            position: relative;
            width: 100%;
            height: 400px;
            overflow: hidden;
            border-radius: 8px;
            background: #f0f0f0;
            margin-bottom: 20px;
        }
        
        .carousel-wrapper {
            position: relative;
            width: 100%;
            height: 100%;
        }
        
        .carousel-slide {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            opacity: 0;
            transition: opacity 0.5s ease;
        }
        
        .carousel-slide.active {
            opacity: 1;
        }
        
        .slide-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .slide-content {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: linear-gradient(transparent, rgba(0,0,0,0.8));
            color: white;
            padding: 20px;
        }
        
        .slide-content h2 {
            margin: 0 0 10px 0;
            font-size: 1.5rem;
        }
        
        .slide-content p {
            margin: 0;
            opacity: 0.9;
        }
        
        .carousel-controls {
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            align-items: center;
            gap: 15px;
            background: rgba(0,0,0,0.6);
            padding: 10px 20px;
            border-radius: 25px;
            transition: opacity 0.3s ease;
        }
        
        .control-btn {
            width: 30px;
            height: 30px;
            background: rgba(255,255,255,0.2);
            border: 1px solid rgba(255,255,255,0.3);
            border-radius: 50%;
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            transition: all 0.3s ease;
        }
        
        .control-btn:hover {
            background: rgba(255,255,255,0.3);
            transform: scale(1.1);
        }
        
        .indicators {
            display: flex;
            gap: 8px;
        }
        
        .indicator {
            width: 24px;
            height: 4px;
            background: rgba(255,255,255,0.3);
            border-radius: 2px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .indicator.active {
            background: white;
        }
        
        .indicator:hover {
            background: rgba(255,255,255,0.6);
        }
        
        .debug-info {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-size: 0.8rem;
        }
        
        .debug-info p {
            margin: 0 0 5px 0;
        }
        
        .debug-info button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 0.7rem;
        }
        
        .manual-controls {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }
        
        .btn {
            padding: 10px 20px;
            background: #3b82f6;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
        }
        
        .btn:hover {
            background: #2563eb;
        }
        
        .log {
            background: #1f2937;
            color: white;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 0.8rem;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="test-container">
            <h1>简化轮播图功能测试</h1>
            
            <div class="test-info">
                <p><strong>当前幻灯片:</strong> {{ currentSlide + 1 }} / {{ slides.length }}</p>
                <p><strong>自动播放:</strong> {{ isPlaying ? '开启' : '关闭' }}</p>
                <p><strong>控制器显示:</strong> {{ controlsVisible ? '显示' : '隐藏' }}</p>
            </div>
            
            <div class="manual-controls">
                <button class="btn" @click="prevSlide">上一张</button>
                <button class="btn" @click="nextSlide">下一张</button>
                <button class="btn" @click="toggleAutoPlay">{{ isPlaying ? '暂停' : '播放' }}</button>
                <button class="btn" @click="goToSlide(0)">回到第一张</button>
                <button class="btn" @click="runTest">自动测试</button>
            </div>
            
            <div class="simple-carousel" @mouseenter="showControls" @mouseleave="hideControls">
                <div class="carousel-wrapper">
                    <!-- 幻灯片 -->
                    <div
                        v-for="(slide, index) in slides"
                        :key="index"
                        class="carousel-slide"
                        :class="{ 'active': index === currentSlide }"
                    >
                        <img :src="slide.image" :alt="slide.title" class="slide-image" />
                        <div class="slide-content">
                            <h2>{{ slide.title }}</h2>
                            <p>{{ slide.description }}</p>
                        </div>
                    </div>
                </div>
                
                <!-- 控制器 -->
                <div class="carousel-controls" v-show="controlsVisible">
                    <button @click="prevSlide" class="control-btn">‹</button>
                    
                    <div class="indicators">
                        <div
                            v-for="(slide, index) in slides"
                            :key="index"
                            class="indicator"
                            :class="{ 'active': index === currentSlide }"
                            @click="goToSlide(index)"
                        ></div>
                    </div>
                    
                    <button @click="nextSlide" class="control-btn">›</button>
                </div>
                
                <!-- 调试信息 -->
                <div class="debug-info">
                    <p>当前: {{ currentSlide + 1 }} / {{ slides.length }}</p>
                    <p>自动播放: {{ isPlaying ? '开启' : '关闭' }}</p>
                    <button @click="toggleAutoPlay">切换播放</button>
                </div>
            </div>
            
            <div class="log">
                <h3>操作日志:</h3>
                <div v-for="(log, index) in logs" :key="index">
                    {{ log }}
                </div>
            </div>
        </div>
    </div>

    <script>
        new Vue({
            el: '#app',
            data: {
                currentSlide: 0,
                isPlaying: true,
                autoPlayTimer: null,
                controlsVisible: false,
                logs: [],
                slides: [
                    {
                        image: './public/plugImg.jpg',
                        title: '第一张测试图片',
                        description: '这是第一张测试图片，用于验证轮播图基础功能'
                    },
                    {
                        image: 'https://images.unsplash.com/photo-1485827404703-89b55fcc595e?w=800&h=400&fit=crop',
                        title: '第二张测试图片',
                        description: '这是第二张测试图片，用于验证切换功能'
                    },
                    {
                        image: 'https://images.unsplash.com/photo-1611224923853-80b023f02d71?w=800&h=400&fit=crop',
                        title: '第三张测试图片',
                        description: '这是第三张测试图片，用于验证循环播放'
                    }
                ]
            },
            mounted() {
                this.log('轮播图初始化完成')
                if (this.isPlaying) {
                    this.startAutoPlay()
                }
            },
            beforeDestroy() {
                this.stopAutoPlay()
            },
            methods: {
                log(message) {
                    const timestamp = new Date().toLocaleTimeString()
                    this.logs.unshift(`[${timestamp}] ${message}`)
                    if (this.logs.length > 10) {
                        this.logs.pop()
                    }
                    console.log(message)
                },
                
                goToSlide(index) {
                    if (index === this.currentSlide) return
                    this.log(`切换到幻灯片 ${index + 1}`)
                    this.currentSlide = index
                },
                
                nextSlide() {
                    const nextIndex = this.currentSlide === this.slides.length - 1 ? 0 : this.currentSlide + 1
                    this.goToSlide(nextIndex)
                },
                
                prevSlide() {
                    const prevIndex = this.currentSlide === 0 ? this.slides.length - 1 : this.currentSlide - 1
                    this.goToSlide(prevIndex)
                },
                
                startAutoPlay() {
                    this.stopAutoPlay()
                    this.autoPlayTimer = setInterval(() => {
                        this.nextSlide()
                    }, 3000)
                    this.log('自动播放已开启')
                },
                
                stopAutoPlay() {
                    if (this.autoPlayTimer) {
                        clearInterval(this.autoPlayTimer)
                        this.autoPlayTimer = null
                        this.log('自动播放已停止')
                    }
                },
                
                toggleAutoPlay() {
                    this.isPlaying = !this.isPlaying
                    if (this.isPlaying) {
                        this.startAutoPlay()
                    } else {
                        this.stopAutoPlay()
                    }
                },
                
                showControls() {
                    this.controlsVisible = true
                },
                
                hideControls() {
                    this.controlsVisible = false
                },
                
                runTest() {
                    this.log('开始自动测试')
                    let step = 0
                    const testSteps = [
                        () => { this.goToSlide(1); this.log('测试: 切换到第2张') },
                        () => { this.goToSlide(2); this.log('测试: 切换到第3张') },
                        () => { this.goToSlide(0); this.log('测试: 回到第1张') },
                        () => { this.toggleAutoPlay(); this.log('测试: 切换自动播放') },
                        () => { this.toggleAutoPlay(); this.log('测试: 恢复自动播放') },
                        () => { this.log('自动测试完成') }
                    ]
                    
                    function runNextStep() {
                        if (step < testSteps.length) {
                            testSteps[step]()
                            step++
                            setTimeout(runNextStep, 1000)
                        }
                    }
                    
                    runNextStep()
                }
            }
        })
    </script>
</body>
</html>
