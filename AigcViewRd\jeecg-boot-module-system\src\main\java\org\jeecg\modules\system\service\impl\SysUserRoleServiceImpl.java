package org.jeecg.modules.system.service.impl;

import org.jeecg.modules.system.entity.SysRole;
import org.jeecg.modules.system.entity.SysUserRole;
import org.jeecg.modules.system.mapper.SysUserRoleMapper;
import org.jeecg.modules.system.service.ISysRoleService;
import org.jeecg.modules.system.service.ISysUserRoleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 * 用户角色表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-21
 */
@Service
@Slf4j
public class SysUserRoleServiceImpl extends ServiceImpl<SysUserRoleMapper, SysUserRole> implements ISysUserRoleService {

    @Autowired
    private ISysRoleService sysRoleService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addUserRole(String userId, String roleCode) {
        try {
            // 根据角色编码查找角色
            SysRole role = sysRoleService.getOne(
                new LambdaQueryWrapper<SysRole>().eq(SysRole::getRoleCode, roleCode)
            );

            if (role == null) {
                log.warn("角色不存在，角色编码：{}", roleCode);
                return;
            }

            // 检查用户角色关系是否已存在
            SysUserRole existUserRole = this.getOne(
                new LambdaQueryWrapper<SysUserRole>()
                    .eq(SysUserRole::getUserId, userId)
                    .eq(SysUserRole::getRoleId, role.getId())
            );

            if (existUserRole != null) {
                log.info("用户角色关系已存在，用户ID：{}，角色编码：{}", userId, roleCode);
                return;
            }

            // 创建用户角色关系
            SysUserRole userRole = new SysUserRole(userId, role.getId());
            this.save(userRole);

            log.info("用户角色关系添加成功，用户ID：{}，角色编码：{}", userId, roleCode);
        } catch (Exception e) {
            log.error("添加用户角色关系失败，用户ID：{}，角色编码：{}，错误：{}", userId, roleCode, e.getMessage(), e);
            throw e;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addUserRoleById(String userId, String roleId) {
        try {
            // 检查用户角色关系是否已存在
            SysUserRole existUserRole = this.getOne(
                new LambdaQueryWrapper<SysUserRole>()
                    .eq(SysUserRole::getUserId, userId)
                    .eq(SysUserRole::getRoleId, roleId)
            );

            if (existUserRole != null) {
                log.info("用户角色关系已存在，用户ID：{}，角色ID：{}", userId, roleId);
                return;
            }

            // 创建用户角色关系
            SysUserRole userRole = new SysUserRole(userId, roleId);
            this.save(userRole);

            log.info("用户角色关系添加成功，用户ID：{}，角色ID：{}", userId, roleId);
        } catch (Exception e) {
            log.error("添加用户角色关系失败，用户ID：{}，角色ID：{}，错误：{}", userId, roleId, e.getMessage(), e);
            throw e;
        }
    }
}
