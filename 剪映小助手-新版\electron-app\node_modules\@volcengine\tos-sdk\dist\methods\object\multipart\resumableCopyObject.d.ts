import TOSBase, { TosResponse } from '../../base';
import { CreateMultipartUploadInput } from './createMultipartUpload';
import { CompleteMultipartUploadOutput } from './completeMultipartUpload';
import { CancelToken } from 'axios';
export interface ResumableCopyObjectInput extends CreateMultipartUploadInput {
    srcBucket: string;
    srcKey: string;
    srcVersionId?: string;
    /**
     * default is 20 MB
     */
    partSize?: number;
    /**
     * the number of request to parallel upload part，default value is 1
     */
    taskNum?: number;
    /**
     * if checkpoint is a string and point to a exist file,
     * the checkpoint record will recover from this file.
     *
     * if checkpoint is a string and point to a directory,
     * the checkpoint will be auto generated,
     * and its name is
     * `{srcBucketName}.{srcObjectName}.{srcVersionId}.{bucketName}.{objectName}.copy`.
     */
    checkpoint?: string | ResumableCopyCheckpointRecord;
    /**
     * the callback of copy event
     */
    copyEventListener?: (event: ResumableCopyEvent) => void;
    /**
     * the simple progress feature
     * percent is [0, 1]
     */
    progress?: (percent: number, checkpoint: ResumableCopyCheckpointRecord) => void;
    /**
     * is axios CancelToken
     */
    cancelToken?: CancelToken;
    /**
     * unit: bit/s
     * server side traffic limit
     **/
    trafficLimit?: number;
}
export interface UploadFileOutput extends CompleteMultipartUploadOutput {
}
export declare enum ResumableCopyEventType {
    CreateMultipartUploadSucceed = 1,
    CreateMultipartUploadFailed = 2,
    UploadPartCopySucceed = 3,
    UploadPartCopyFailed = 4,
    UploadPartCopyAborted = 5,
    CompleteMultipartUploadSucceed = 6,
    CompleteMultipartUploadFailed = 7
}
export interface CopyPartInfo {
    partNumber: number;
    copySourceRangeStart: number;
    copySourceRangeEnd: number;
    etag?: string;
}
export interface ResumableCopyEvent {
    type: ResumableCopyEventType;
    /**
     * has value when event is failed or aborted
     */
    err?: Error;
    bucket: string;
    key: string;
    uploadId?: string;
    checkpointFile?: string;
    copyPartInfo?: CopyPartInfo;
}
export interface ResumableCopyCheckpointRecord {
    bucket: string;
    key: string;
    part_size: number;
    upload_id: string;
    parts_info?: ResumableCopyCheckpointRecordPart[];
    copy_source_object_info: {
        etag: string;
        hash_crc64ecma: string;
        last_modified: string;
        object_size: number;
    };
}
interface ResumableCopyCheckpointRecordPart {
    part_number: number;
    copy_source_range_start: number;
    copy_source_range_end: number;
    etag: string;
    is_completed: boolean;
}
export declare const DEFAULT_PART_SIZE: number;
export declare function resumableCopyObject(this: TOSBase, input: ResumableCopyObjectInput): Promise<TosResponse<UploadFileOutput>>;
export declare function isCancelError(err: any): boolean;
export default resumableCopyObject;
