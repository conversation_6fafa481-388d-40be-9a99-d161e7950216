import TOSBase from '../base';
export interface MirrorBackRule {
    ID: string;
    Condition: {
        HttpCode: number;
        KeyPrefix?: string;
        KeySuffix?: string;
        /** private unstable */
        AllowHost?: string[];
        /** private unstable */
        HttpMethod?: string[];
    };
    Redirect: {
        RedirectType?: 'Mirror' | 'Async';
        FetchSourceOnRedirect?: boolean;
        /** @private unstable */
        FetchSourceOnRedirectWithQuery?: boolean;
        PublicSource?: {
            SourceEndpoint: {
                Primary: string[];
                Follower?: string[];
            };
            FixedEndpoint?: boolean;
        };
        /** @private unstable */
        PrivateSource?: {
            SourceEndpoint: {
                Primary: {
                    Endpoint: string;
                    BucketName: string;
                    CredentialProvider: {
                        Role: string;
                    };
                }[];
            };
        };
        PassQuery?: boolean;
        FollowRedirect?: boolean;
        MirrorHeader?: {
            PassAll?: boolean;
            Pass?: string[];
            Remove?: string[];
            /** private unstable */
            Set?: {
                Key: string;
                Value: string;
            }[];
        };
        Transform?: {
            WithKeyPrefix?: string;
            WithKeySuffix?: string;
            ReplaceKeyPrefix?: {
                KeyPrefix?: string;
                ReplaceWith?: string;
            };
        };
    };
}
export interface PutBucketMirrorBackInput {
    bucket: string;
    rules: MirrorBackRule[];
}
export interface PutBucketMirrorBackOutput {
}
export declare function putBucketMirrorBack(this: TOSBase, input: PutBucketMirrorBackInput): Promise<import("../base").TosResponse<DeleteBucketMirrorBackOutput>>;
export interface GetBucketMirrorBackInput {
    bucket: string;
}
export interface GetBucketMirrorBackOutput {
    Rules: MirrorBackRule[];
}
export declare function getBucketMirrorBack(this: TOSBase, input: GetBucketMirrorBackInput): Promise<import("../base").TosResponse<GetBucketMirrorBackOutput>>;
export interface DeleteBucketMirrorBackInput {
    bucket: string;
}
export interface DeleteBucketMirrorBackOutput {
}
export declare function deleteBucketMirrorBack(this: TOSBase, input: DeleteBucketMirrorBackInput): Promise<import("../base").TosResponse<DeleteBucketMirrorBackOutput>>;
