package org.jeecg.modules.demo.exchangecode.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.jeecg.modules.demo.exchangecode.entity.AicgExchangeCode;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: 兑换码表
 * @Author: jeecg-boot
 * @Date:   2025-06-14
 * @Version: V1.0
 */
public interface AicgExchangeCodeMapper extends BaseMapper<AicgExchangeCode> {

    /**
     * 根据兑换码查询
     * @param code 兑换码
     * @return 兑换码信息
     */
    @Select("SELECT * FROM aicg_exchange_code WHERE code = #{code}")
    AicgExchangeCode getByCode(@Param("code") String code);
    
    /**
     * 使用兑换码
     * @param code 兑换码
     * @param userId 使用者用户ID
     * @param updateBy 更新人
     * @return 更新结果
     */
    @Update("UPDATE aicg_exchange_code SET status = 2, used_by = #{userId}, used_time = NOW(), update_by = #{updateBy}, update_time = NOW() WHERE code = #{code} AND status = 1")
    int useExchangeCode(@Param("code") String code, @Param("userId") String userId, @Param("updateBy") String updateBy);
    
    /**
     * 查询用户使用的兑换码记录
     * @param userId 用户ID
     * @return 兑换码列表
     */
    @Select("SELECT * FROM aicg_exchange_code WHERE used_by = #{userId} ORDER BY used_time DESC")
    List<AicgExchangeCode> getUserUsedCodes(@Param("userId") String userId);
    
    /**
     * 批量更新过期状态
     * @return 更新数量
     */
    @Update("UPDATE aicg_exchange_code SET status = 3, update_time = NOW() WHERE status = 1 AND expire_time < NOW()")
    int updateExpiredCodes();
}
