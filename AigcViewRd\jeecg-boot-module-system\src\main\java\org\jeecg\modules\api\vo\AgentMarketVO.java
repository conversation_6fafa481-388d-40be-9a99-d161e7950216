package org.jeecg.modules.api.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description: 智能体市场展示VO
 * @Author: AigcView
 * @Date: 2025-07-31
 * @Version: V1.0
 */
@ApiModel(value = "AgentMarketVO", description = "智能体市场展示对象")
@Data
public class AgentMarketVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**主键*/
    @ApiModelProperty(value = "主键")
    private String id;

    /**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;

    /**创建日期*/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private Date createTime;

    /**更新人*/
    @ApiModelProperty(value = "更新人")
    private String updateBy;

    /**更新日期*/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private Date updateTime;

    /**作者类型*/
    @ApiModelProperty(value = "作者类型")
    private String authorType;

    /**作者类型显示名称*/
    @ApiModelProperty(value = "作者类型显示名称")
    private String authorTypeName;

    /**智能体ID*/
    @ApiModelProperty(value = "智能体ID")
    private String agentId;

    /**智能体名称*/
    @ApiModelProperty(value = "智能体名称")
    private String agentName;

    /**智能体描述*/
    @ApiModelProperty(value = "智能体描述")
    private String agentDescription;

    /**智能体头像*/
    @ApiModelProperty(value = "智能体头像")
    private String agentAvatar;

    /**展示视频*/
    @ApiModelProperty(value = "展示视频")
    private String demoVideo;

    /**体验链接*/
    @ApiModelProperty(value = "体验链接")
    private String experienceLink;

    /**原价格（元）*/
    @ApiModelProperty(value = "原价格（元）")
    private BigDecimal originalPrice;

    /**折扣价格（元）*/
    @ApiModelProperty(value = "折扣价格（元）")
    private BigDecimal discountPrice;

    /**折扣率（百分比）*/
    @ApiModelProperty(value = "折扣率（百分比）")
    private Integer discountRate;

    /**是否免费*/
    @ApiModelProperty(value = "是否免费")
    private Boolean isFree;

    /**是否有折扣*/
    @ApiModelProperty(value = "是否有折扣")
    private Boolean hasDiscount;

    /**是否显示折扣价格*/
    @ApiModelProperty(value = "是否显示折扣价格")
    private Boolean showDiscountPrice;

    /**审核状态*/
    @ApiModelProperty(value = "审核状态")
    private String auditStatus;

    /**审核备注*/
    @ApiModelProperty(value = "审核备注")
    private String auditRemark;

    // ========== 扩展字段 ==========

    /**创作者名称*/
    @ApiModelProperty(value = "创作者名称")
    private String creatorName;

    /**创作者真实姓名*/
    @ApiModelProperty(value = "创作者真实姓名")
    private String creatorRealName;

    /**创作者头像*/
    @ApiModelProperty(value = "创作者头像")
    private String creatorAvatar;

    /**工作流数量*/
    @ApiModelProperty(value = "工作流数量")
    private Integer workflowCount;

    /**查看次数*/
    @ApiModelProperty(value = "查看次数")
    private Long viewCount;

    /**点赞次数*/
    @ApiModelProperty(value = "点赞次数")
    private Long likeCount;

    /**收藏次数*/
    @ApiModelProperty(value = "收藏次数")
    private Long favoriteCount;

    /**购买次数*/
    @ApiModelProperty(value = "购买次数")
    private Long purchaseCount;

    /**评分*/
    @ApiModelProperty(value = "评分")
    private BigDecimal rating;

    /**评价数量*/
    @ApiModelProperty(value = "评价数量")
    private Long reviewCount;

    /**标签列表*/
    @ApiModelProperty(value = "标签列表")
    private String tags;

    /**是否推荐*/
    @ApiModelProperty(value = "是否推荐")
    private Boolean isRecommended;

    /**推荐权重*/
    @ApiModelProperty(value = "推荐权重")
    private Integer recommendWeight;

    /**是否已购买*/
    @ApiModelProperty(value = "是否已购买")
    private Boolean isPurchased;

    /**封面图片URL（优先使用demoVideo，否则使用agentAvatar）*/
    @ApiModelProperty(value = "封面图片URL")
    private String coverUrl;

    /**价格显示文本*/
    @ApiModelProperty(value = "价格显示文本")
    private String priceDisplayText;

    /**折扣标签文本*/
    @ApiModelProperty(value = "折扣标签文本")
    private String discountTagText;

    // ========== 辅助方法 ==========

    /**
     * 获取封面URL
     */
    public String getCoverUrl() {
        if (this.demoVideo != null && !this.demoVideo.trim().isEmpty()) {
            return this.demoVideo;
        }
        return this.agentAvatar;
    }

    /**
     * 获取价格显示文本
     */
    public String getPriceDisplayText() {
        if (this.isFree != null && this.isFree) {
            return "免费";
        }
        
        if (this.hasDiscount != null && this.hasDiscount && this.discountPrice != null) {
            return "¥" + this.discountPrice;
        }
        
        if (this.originalPrice != null) {
            return "¥" + this.originalPrice;
        }
        
        return "价格面议";
    }

    /**
     * 获取折扣标签文本
     */
    public String getDiscountTagText() {
        if (this.isFree != null && this.isFree) {
            return "免费";
        }
        
        if (this.discountRate != null && this.discountRate > 0) {
            return this.discountRate + "% OFF";
        }
        
        return null;
    }

    /**
     * 获取作者类型显示名称
     */
    public String getAuthorTypeName() {
        if ("1".equals(this.authorType)) {
            return "官方";
        } else if ("2".equals(this.authorType)) {
            return "创作者";
        }
        return "未知";
    }

    /**
     * 设置价格信息
     */
    public void setPriceInfo(BigDecimal originalPrice, BigDecimal discountPrice, Integer discountRate, Boolean isFree) {
        this.originalPrice = originalPrice;
        this.discountPrice = discountPrice;
        this.discountRate = discountRate;
        this.isFree = isFree;
        this.hasDiscount = (discountRate != null && discountRate > 0) || (isFree != null && isFree);
        this.showDiscountPrice = (discountRate != null && discountRate > 0) && (isFree == null || !isFree);
    }

    /**
     * 设置创作者信息
     */
    public void setCreatorInfo(String creatorName, String creatorRealName) {
        this.creatorName = creatorName;
        this.creatorRealName = creatorRealName;
    }

    /**
     * 设置创作者头像
     */
    public void setCreatorAvatar(String creatorAvatar) {
        this.creatorAvatar = creatorAvatar;
    }

    /**
     * 设置统计信息
     */
    public void setStatistics(Long viewCount, Long likeCount, Long favoriteCount, Long purchaseCount) {
        this.viewCount = viewCount != null ? viewCount : 0L;
        this.likeCount = likeCount != null ? likeCount : 0L;
        this.favoriteCount = favoriteCount != null ? favoriteCount : 0L;
        this.purchaseCount = purchaseCount != null ? purchaseCount : 0L;
    }
}
