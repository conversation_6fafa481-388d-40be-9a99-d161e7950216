import TOSBase from '../base';
export interface PreSignedPolicyURLInput {
    bucket?: string;
    /**
     * unit: s
     * default value: 3600
     * range is: [1, 604800]
     */
    expires?: number;
    conditions: PolicySignatureCondition[];
    alternativeEndpoint?: string;
    /**
     * default: false
     * if set true. generate domain will direct use `endpoint` or `alternativeEndpoint`.
     */
    isCustomDomain?: boolean;
}
export interface PreSignedPolicyURLOutput {
    getSignedURLForList(additionalQuery?: Record<string, string>): string;
    getSignedURLForGetOrHead(key: string, additionalQuery?: Record<string, string>): string;
    signedQuery: string;
}
export interface PolicySignatureCondition {
    key: 'key';
    value: string;
    operator?: 'eq' | 'starts-with';
}
export declare function preSignedPolicyURL(this: TOSBase, input: PreSignedPolicyURLInput): PreSignedPolicyURLOutput;
