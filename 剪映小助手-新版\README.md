# 剪映小助手Pro版 - 智界AigcView

## 📖 项目简介

剪映小助手Pro版是智界AigcView开发的革命性升级版本，通过接口合并优化将原来的16个独立接口合并为8个智能一体化接口，实现了操作步骤减半、开发效率翻倍的重大突破。包含Coze平台插件和Electron桌面应用，支持视频、音频、图片、文本、特效等多种素材的智能化批量处理。

## 🎯 Pro版核心优势

- **接口合并优化**：16个接口 → 8个接口，API调用次数减半
- **一体化操作**：两步操作 → 一步操作，操作步骤减半
- **智能参数识别**：系统自动识别参数类型并选择处理模式
- **统一错误处理**：所有接口使用统一的错误处理和响应格式
- **完全代码隔离**：使用独立的jianyingpro包，不影响稳定版

## 🏗️ 项目架构

```
剪映小助手-新版/
├── coze-plugins/           # Coze平台插件配置
│   ├── 智界工具箱/         # 核心功能插件（17个API接口）
│   ├── 智界数据箱/         # 数据查询插件
│   ├── 智界音频库/         # 音频资源插件
│   ├── 小红书分享/         # 社交媒体集成
│   └── 图生视频箱/         # AI视频生成
├── electron-app/           # Electron桌面应用
│   ├── src/               # 应用源码
│   ├── assets/            # 静态资源
│   └── docs/              # 应用文档
└── docs/                  # 项目文档
```

## 🚀 Pro版核心功能 - 8个合并接口

### 一体化操作接口（6个）

| 接口名称 | 合并逻辑 | 智能特性 |
|---------|---------|---------|
| **add_audios** | `audio_infos` + `add_audios` → 单一操作 | 自动从mp3_urls生成音频信息 |
| **add_videos** | `video_infos` + `add_videos` → 单一操作 | 自动从mp4_urls生成视频信息 + 外部URL直接下载 |
| **add_images** | `imgs_infos` + `add_images` → 单一操作 | 自动从image_urls生成图片信息 + 外部URL直接下载 |
| **add_captions** | `caption_infos` + `add_captions` → 单一操作 | 自动处理字幕参数和时间线 |
| **add_effects** | `effect_infos` + `add_effects` → 单一操作 | 自动处理特效参数和应用逻辑 |
| **add_keyframes** | `keyframes_infos` + `add_keyframes` → 单一操作 | 自动处理关键帧数据和时间线 |

### 智能识别接口（2个）

| 接口名称 | 合并逻辑 | 智能特性 |
|---------|---------|---------|
| **generate_timelines** | `timelines` + `audio_timelines` → 智能模式识别 | 根据参数自动选择音频模式或自定义模式 |
| **data_conversion** | `str_to_list` + `objs_to_str_list` + `str_list_to_objs` → 统一接口 | 支持同时输入多种数据类型，自动执行所有可能的转换 |

### 基础版接口参考（31个原始接口）

#### 智界工具箱 - 17个核心API接口

| 功能分类 | API接口 | 描述 |
|---------|---------|------|
| **草稿管理** | `create_draft` | 创建剪映草稿，初始化视频项目 |
| | `save_draft` | 保存草稿到云端存储 |
| **素材添加** | `add_videos` | 批量添加视频素材 |
| | `add_images` | 批量添加图片素材 |
| | `add_audios` | 批量添加音频素材 |
| | `add_captions` | 添加字幕和文本 |
| | `add_sticker` | 添加贴纸素材 |
| **特效处理** | `add_effects` | 添加视频特效 |
| | `add_masks` | 添加蒙版效果 |
| | `add_keyframes` | 添加关键帧动画 |
| **文本样式** | `add_text_style` | 创建富文本样式 |
| **快速创建** | `easy_create_material` | 一键创建多素材轨道 |
| **视频渲染** | `gen_video` | 云端视频渲染 |
| | `gen_video_status` | 查询渲染状态 |
| **工具函数** | `get_audio_duration` | 获取音频时长 |
| | `get_image_animations` | 获取图片动画效果 |
| | `get_text_animations` | 获取文字动画效果 |

### 智界数据箱 - 资源查询

- 贴纸素材搜索
- 音效资源查询
- 动画效果检索
- 字体资源管理

### Electron桌面应用

- 本地剪映草稿管理
- 批量文件处理
- 实时预览功能
- 自动更新机制

## 📋 最新更新 - API参数规范化

### 更新概述

**更新时间**: 2025年1月16日  
**版本**: v2.0.0  
**更新类型**: 重大更新 - API参数规范化

### 主要变更

#### 1. 移除zj_前缀参数

所有API接口参数已从 `zj_` 前缀格式规范化为标准格式：

**修改前**:
```json
{
  "access_key": "JianyingAPI_2025_SecureAccess_AigcView",
  "zj_draft_url": "https://example.com/draft/123",
  "zj_video_infos": "[...]"
}
```

**修改后**:
```json
{
  "access_key": "JianyingAPI_2025_SecureAccess_AigcView", 
  "draft_url": "https://example.com/draft/123",
  "video_infos": "[...]"
}
```

#### 2. 完整修改清单

| 序号 | API接口 | 修改参数数量 | 主要变更 |
|------|---------|-------------|----------|
| 1 | add_audios | 2个 | `zj_audio_infos` → `audio_infos` |
| 2 | add_captions | 15个 | `zj_draft_url` → `draft_url` 等 |
| 3 | add_effects | 2个 | `zj_effect_infos` → `effect_infos` |
| 4 | add_images | 7个 | `zj_image_infos` → `image_infos` |
| 5 | add_keyframes | 2个 | `zj_keyframes` → `keyframes` |
| 6 | add_masks | 11个 | `zj_segment_ids` → `segment_ids` |
| 7 | add_sticker | 7个 | `zj_sticker_id` → `sticker_id` |
| 8 | add_text_style | 5个 | `zj_keyword` → `keyword` |
| 9 | add_videos | 7个 | `zj_video_infos` → `video_infos` |
| 10 | create_draft | 3个 | `zj_width` → `width` |
| 11 | easy_create_material | 8个 | `zj_audio_url` → `audio_url` |
| 12 | gen_video | 2个 | `zj_api_token` → `api_token` |
| 13 | gen_video_status | 1个 | `zj_draft_url` → `draft_url` |
| 14 | get_audio_duration | 1个 | `zj_mp3_url` → `mp3_url` |
| 15 | get_image_animations | 2个 | `zj_mode` → `mode` |
| 16 | get_text_animations | 2个 | `zj_type` → `type` |
| 17 | save_draft | 2个 | `zj_user_id` → `user_id` |

**总计**: 78个参数完成规范化

#### 3. 后端同步更新

- ✅ 17个DTO类的@JsonProperty注解已同步更新
- ✅ 所有验证消息已更新
- ✅ 前后端参数映射100%一致

### 兼容性说明

⚠️ **重要提醒**: 此次更新为**破坏性更新**，旧版本的zj_前缀参数将不再支持。

**升级指南**:
1. 更新所有API调用，移除参数名中的`zj_`前缀
2. 更新Coze插件配置文件
3. 测试所有API接口确保正常工作

## 🛠️ 快速开始

### 环境要求

- Node.js 16.0+
- Java 8+
- MySQL 5.7+
- Redis 3.0+

### 安装部署

1. **克隆项目**
```bash
git clone [项目地址]
cd 剪映小助手-新版
```

2. **配置Coze插件**
```bash
# 导入智界工具箱插件
# 将 coze-plugins/智界工具箱/ 目录下的JSON文件导入到Coze平台
```

3. **启动Electron应用**
```bash
cd electron-app
npm install
npm start
```

### API使用示例

#### 创建草稿
```javascript
const response = await fetch('/jeecg-boot/api/jianying/create_draft', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    access_key: 'JianyingAPI_2025_SecureAccess_AigcView',
    width: 1920,
    height: 1080
  })
});
```

#### 添加视频
```javascript
const response = await fetch('/jeecg-boot/api/jianying/add_videos', {
  method: 'POST', 
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    access_key: 'JianyingAPI_2025_SecureAccess_AigcView',
    draft_url: 'https://example.com/draft/123',
    video_infos: '[{"video_url": "https://example.com/video.mp4", "duration": 10000000}]'
  })
});
```

## 📚 文档目录

- [API测试文档](./API测试文档.md) - 完整的API接口测试指南
- [完整API实现清单](./完整API实现清单.md) - 所有API接口的详细说明
- [Electron应用文档](./electron-app/README.md) - 桌面应用使用指南
- [技术文档](./coze-plugins/技术文档/) - 详细的技术实现文档

## 🔧 技术栈

- **后端**: Java Spring Boot, MySQL, Redis
- **前端**: Vue.js, Element UI
- **桌面应用**: Electron, Node.js
- **AI平台**: Coze (字节跳动)
- **云存储**: 火山引擎TOS
- **CDN**: 火山引擎CDN

## 📞 联系我们

- **官网**: https://www.aigcview.com
- **技术支持**: 智界AigcView团队
- **更新日志**: 查看项目文档获取最新更新信息

## 📄 许可证

本项目由智界AigcView开发和维护，保留所有权利。

## 🔍 详细功能说明

### 核心工作流程

1. **创建草稿** (`create_draft`) - 初始化剪映项目
2. **添加素材** - 使用各种add_*接口添加视频、音频、图片等
3. **应用效果** - 添加特效、蒙版、动画等
4. **保存草稿** (`save_draft`) - 保存到云端
5. **渲染视频** (`gen_video`) - 生成最终视频

### 参数规范化详细说明

#### 修改原因
- **提升API一致性**: 移除非标准的zj_前缀，使参数命名更加直观
- **改善开发体验**: 标准化的参数名更易于理解和使用
- **符合行业规范**: 遵循RESTful API设计最佳实践

#### 修改影响范围
- ✅ **Coze插件配置**: 17个JSON配置文件已更新
- ✅ **后端DTO类**: 17个Java类的@JsonProperty注解已同步
- ✅ **API文档**: 所有接口文档已更新
- ✅ **验证逻辑**: 参数验证消息已同步更新

#### 迁移检查清单
- [ ] 更新所有API调用代码
- [ ] 重新导入Coze插件配置
- [ ] 更新客户端SDK（如有）
- [ ] 测试所有API接口
- [ ] 更新相关文档

## 🧪 测试指南

### API接口测试

使用提供的测试脚本验证API功能：

```bash
# 测试基础功能
node test_simple.js

# 测试音频处理
node test_audio_fallback.js

# 测试文本样式
node test_add_text_style.js
```

### Electron应用测试

```bash
cd electron-app
npm test
```

## 🚨 故障排除

### 常见问题

**Q: API返回参数错误**
A: 检查是否使用了旧的zj_前缀参数，请使用新的标准参数名

**Q: Coze插件无法正常工作**
A: 重新导入最新的JSON配置文件到Coze平台

**Q: 视频渲染失败**
A: 检查draft_url是否有效，确保所有素材文件可访问

### 调试模式

启用详细日志输出：
```bash
export DEBUG=jianying:*
npm start
```

## 🔄 版本历史

### v2.0.0 (2025-01-16)
- 🔥 **重大更新**: API参数规范化
- ✨ 移除所有zj_前缀参数
- 🔧 后端DTO类同步更新
- 📚 完善文档和测试用例

### v1.x.x (历史版本)
- 基础功能实现
- Coze插件集成
- Electron应用开发

## 🤝 贡献指南

### 开发规范
- 遵循现有代码风格
- 添加适当的测试用例
- 更新相关文档
- 确保向后兼容性（除非是重大更新）

### 提交规范
```
feat: 新功能
fix: 修复bug
docs: 文档更新
style: 代码格式调整
refactor: 代码重构
test: 测试相关
chore: 构建过程或辅助工具的变动
```

---

**智界AigcView** - 让AI创作更简单
