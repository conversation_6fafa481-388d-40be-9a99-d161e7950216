import TOSBase from '../base';
import { StorageClassType, TierType } from '../../TosExportEnum';
export declare type JobStatusType = 'New' | 'Preparing' | 'Suspended' | 'Ready' | 'Active' | 'Pausing' | 'Paused' | 'Complete' | 'Cancelling' | 'Cancelled' | 'Failing' | 'Failed';
export declare type DirectiveType = 'COPY' | 'REPLACE' | 'ADD';
export declare type CannedAccessControlListType = 'default' | 'private' | 'public-read';
export declare type PermissionType = 'READ' | 'READ_ACP' | 'WRITE_ACP' | 'FULL_CONTROL';
export declare type PrefixReplaceType = 'true' | 'false';
export declare type ConfirmationRequiredType = '0' | '1';
export interface Tag {
    Key: string;
    Value: string;
}
interface Manifest {
    Location: {
        ETag: string;
        ObjectTrn: string;
        ObjectVersionId?: string;
    };
    Spec: {
        Format: 'TOSInventoryReport_CSV_V1';
    };
}
export interface NewObjectMetadataType {
    SSEAlgorithm?: 'AES256';
    UserMetadata?: {
        member: {
            Key: string;
            Value: string;
        }[];
    };
    'content-type'?: string;
    'content-encoding'?: string;
    'content-language'?: string;
    'cache-control'?: string;
    'content-disposition'?: string;
    expires?: string;
}
export interface NewObjectTaggingType {
    TOSTag?: Tag[];
}
export interface Report {
    Bucket: string;
    Enabled: PrefixReplaceType;
    Format: 'Report_CSV_V1';
    Prefix: string;
    ReportScope: 'AllTasks' | 'FailedTasksOnly';
}
export interface ProgressSummary {
    TotalNumberOfTasks: number;
    NumberOfTasksSucceeded: number;
    NumberOfTasksFailed: number;
}
export interface ListBatchInput {
    accountId: string;
    jobStatuses?: string[];
    nextToken?: string;
    maxResults?: number;
}
export interface UpdateJobPriorityInput {
    jobId: string;
    priority: number;
    accountId: string;
}
export interface UpdateJobStatusInput {
    jobId: string;
    accountId: string;
    requestedJobStatus: 'Ready' | 'Cancelled';
    statusUpdateReason?: string;
}
export interface JobInput {
    JobId: string;
    accountId: string;
}
export declare type DeleteJob = JobInput;
export declare type DescribeJob = JobInput;
export interface AccessControlList {
    TOSGrant: {
        Grantee: {
            Identifier: string;
            TypeIdentifier: 'id';
        };
        Permission: PermissionType;
    }[];
}
export interface TOSPutObjectCopy {
    TOSPutObjectCopy: {
        PrefixReplace: PrefixReplaceType;
        ResourcesPrefix: string;
        TargetKeyPrefix: string;
        StorageClass: StorageClassType;
        AccessControlDirective: DirectiveType;
        CannedAccessControlList?: CannedAccessControlListType;
        AccessControlGrants?: AccessControlList;
        TargetResource: string;
        MetadataDirective: DirectiveType;
        NewObjectMetadata: NewObjectMetadataType;
        TaggingDirective: DirectiveType;
        NewObjectTagging: NewObjectTaggingType;
    };
}
export interface TOSPutObjectAcl {
    TOSPutObjectAcl: {
        AccessControlPolicy: {
            CannedAccessControlList: CannedAccessControlListType;
            AccessControlList: AccessControlList;
        };
    };
}
export interface TOSPutObjectTagging {
    TOSPutObjectTagging: {
        TOSTag: Tag[];
    };
}
export interface TOSRestoreObject {
    TOSRestoreObject: {
        Days: number;
        Tier: TierType;
    };
}
export interface TOSDeleteObjectTagging {
    TOSDeleteObjectTagging: {};
}
export declare type PutJobInput = {
    accountId: string;
    clientRequestToken: string;
    confirmationRequired: '0' | '1';
    description?: string;
    manifest: Manifest;
    priority: string;
    roleTrn: string;
    report?: Report;
    operation?: TOSPutObjectCopy | TOSPutObjectAcl | TOSPutObjectTagging | TOSRestoreObject | TOSDeleteObjectTagging;
};
export interface DescribeJobRes {
    Job: {
        JobId: string;
        ConfirmationRequired: ConfirmationRequiredType;
        Description?: string;
        FailureReasons?: {
            JobFailure: {
                FailureCode: string;
                FailureReason: string;
            };
        };
        Manifest: Manifest;
        Priority: number;
        ProgressSummary: ProgressSummary;
        Report: Report;
        RoleArn: string;
        Status: JobStatusType;
        StatusUpdateReason: string;
        SuspendedDate: string;
        TerminationDate: string;
        CreationTime: string;
        Operation: TOSPutObjectCopy | TOSPutObjectAcl | TOSPutObjectTagging | TOSRestoreObject | TOSDeleteObjectTagging;
    };
}
export interface JobList {
    JobId: string;
    CreationTime: string;
    Operation: 'TOSPutObjectCopy' | 'TOSPutObjectAcl' | 'TOSPutObjectTagging' | 'TOSRestoreObject' | 'TOSDeleteObjectTagging';
    Priority: number;
    ProgressSummary: ProgressSummary;
    Status: JobStatusType;
    TerminationDate: string;
    Description: string;
}
export interface JobListRes {
    Jobs: {
        member: JobList[];
    };
    NextToken: string;
}
/**
 *
 * @private unstable method
 * @description 创建批量任务
 * @param params
 * @returns
 */
export declare function createJob(this: TOSBase, params: PutJobInput): Promise<import("../base").TosResponse<unknown>>;
/**
 * @private unstable method
 * @description  获取批量任务列表
 * @param params
 * @returns
 */
export declare function listJobs(this: TOSBase, params: ListBatchInput): Promise<import("../base").TosResponse<JobListRes>>;
/**
 *
 * @private unstable method
 * @description 更新批量任务优先级
 * @param params
 * @returns
 */
export declare function updateJobPriority(this: TOSBase, params: UpdateJobPriorityInput): Promise<import("../base").TosResponse<unknown>>;
/**
 *
 * @private unstable method
 * @description 更新批量任务优先级
 * @param params
 * @returns
 */
export declare function updateJobStatus(this: TOSBase, params: UpdateJobStatusInput): Promise<import("../base").TosResponse<unknown>>;
/**
 *
 * @private unstable method
 * @description 删除批量任务
 * @param params
 * @returns
 */
export declare function deleteJob(this: TOSBase, params: DeleteJob): Promise<import("../base").TosResponse<unknown>>;
/**
 *
 * @private unstable method
 * @description 获取批量任务详情
 * @param params
 * @returns
 */
export declare function describeJob(this: TOSBase, params: DescribeJob): Promise<import("../base").TosResponse<DescribeJobRes>>;
export {};
