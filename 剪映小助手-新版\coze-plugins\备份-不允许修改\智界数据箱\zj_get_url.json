{"openapi": "3.0.0", "info": {"title": "剪映小助手_智界数据箱 - 链接提取器", "description": "提取链接，用于多值返回变成单值返回", "version": "1.0.0"}, "servers": [{"url": "https://www.aigcview.com"}], "paths": {"/jeecg-boot/api/jianying/get_url": {"post": {"summary": "提取链接", "description": "提取链接，用于多值返回变成单值返回", "operationId": "getUrl_zj", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"access_key": {"type": "string", "description": "访问密钥（必填）", "example": "JianyingAPI_2025_SecureAccess_AigcView", "default": "JianyingAPI_2025_SecureAccess_AigcView"}, "zj_output": {"type": "string", "description": "提取内容（必填）", "example": "https://example.com/file1.mp4,https://example.com/file2.mp4"}}, "required": ["access_key", "zj_output"], "additionalProperties": false}}}}, "responses": {"200": {"description": "成功提取链接", "content": {"application/json": {"schema": {"type": "object", "properties": {"output": {"type": "string", "description": "提取的URL或原文内容"}}, "required": ["output"]}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "description": "操作是否成功", "example": false}, "error": {"type": "string", "description": "错误信息"}, "code": {"type": "string", "description": "错误代码"}}, "required": ["success", "error"]}}}}, "500": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "description": "操作是否成功", "example": false}, "error": {"type": "string", "description": "错误信息"}, "code": {"type": "string", "description": "错误代码"}}, "required": ["success", "error"]}}}}}}}}}