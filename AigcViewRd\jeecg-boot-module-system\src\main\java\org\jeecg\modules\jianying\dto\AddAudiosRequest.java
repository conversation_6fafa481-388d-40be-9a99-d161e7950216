package org.jeecg.modules.jianying.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.NotBlank;

@Data
@EqualsAndHashCode(callSuper = true)
public class AddAudiosRequest extends BaseJianyingRequest {
    
    @ApiModelProperty(value = "音频信息数组（必填）", required = true)
    @NotBlank(message = "audio_infos不能为空")
    @JsonProperty("audio_infos")
    private String zjAudioInfos;

    @ApiModelProperty(value = "草稿地址（必填）", required = true)
    @NotBlank(message = "draft_url不能为空")
    @JsonProperty("draft_url")
    private String zjDraftUrl;

    @Override
    public String getSummary() {
        return "AddAudiosRequest{" +
               "accessKey=" + (getAccessKey() != null && getAccessKey().length() > 10 ?
                              getAccessKey().substring(0, 10) + "***" : "null") +
               ", draftUrl=" + (zjDraftUrl != null && zjDraftUrl.length() > 30 ?
                               zjDraftUrl.substring(0, 30) + "***" : zjDraftUrl) +
               "}";
    }
}
