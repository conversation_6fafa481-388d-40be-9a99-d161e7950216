package org.jeecg.modules.jianyingpro.dto.request;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 一体化特效添加请求
 * 合并 effect_infos + add_effects 的参数
 *
 * <AUTHOR>
 * @since 2025-01-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class JianyingProAddEffectsRequest extends BaseJianyingProRequest {

    // ========== 核心参数 ==========
    @ApiModelProperty(value = "草稿地址，使用create_draft输出的draft_url即可（必填）", required = true,
                     example = "https://example.com/draft/123")
    @NotBlank(message = "draft_url不能为空")
    @JsonProperty("draft_url")
    private String draftUrl;

    @ApiModelProperty(value = "特效名字（必填）", required = true,
                     example = "[\"金粉闪闪\", \"光影流转\"]")
    @NotEmpty(message = "effects不能为空")
    @JsonProperty("effects")
    private List<String> effects;

    @ApiModelProperty(value = "时间线（必填）", required = true,
                     example = "[{\"start\": 0, \"end\": 5000000}]")
    @NotEmpty(message = "timelines不能为空")
    @JsonProperty("timelines")
    private List<JSONObject> timelines;

    @Override
    public String getSummary() {
        return "JianyingProAddEffectsRequest{" +
               "draftUrl=" + (draftUrl != null && draftUrl.length() > 50 ?
                            draftUrl.substring(0, 50) + "***" : draftUrl) +
               ", effectsCount=" + (effects != null ? effects.size() : 0) +
               ", timelinesCount=" + (timelines != null ? timelines.size() : 0) +
               "}";
    }

    @Override
    public void validate() {
        super.validate();

        // effects是必填的（复制自稳定版effect_infos要求）
        if (effects == null || effects.isEmpty()) {
            throw new IllegalArgumentException(
                "参数不完整：effects不能为空"
            );
        }

        // timelines是必填的（复制自稳定版effect_infos要求）
        if (timelines == null || timelines.isEmpty()) {
            throw new IllegalArgumentException(
                "参数不完整：timelines不能为空"
            );
        }

        // 验证effects中不能有空值
        for (String effect : effects) {
            if (effect == null || effect.trim().isEmpty()) {
                throw new IllegalArgumentException(
                    "effects 中不能包含空的特效名称"
                );
            }
        }


    }
}
