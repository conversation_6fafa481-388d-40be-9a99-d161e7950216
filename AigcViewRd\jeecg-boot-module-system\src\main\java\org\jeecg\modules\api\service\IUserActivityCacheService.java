package org.jeecg.modules.api.service;

import org.jeecg.modules.api.dto.UserActivityUpdateDTO;
import org.jeecg.modules.api.entity.AicgOnlineUsers;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @Description: 用户活跃状态缓存服务接口
 * @Author: AigcView
 * @Date: 2025-01-30
 * @Version: V1.0
 */
public interface IUserActivityCacheService {

    // ==================== 用户基本信息缓存 ====================

    /**
     * 缓存用户基本信息
     * @param userId 用户ID
     * @param userInfo 用户信息
     * @return 是否成功
     */
    boolean cacheUserInfo(String userId, Map<String, Object> userInfo);

    /**
     * 获取缓存的用户基本信息
     * @param userId 用户ID
     * @return 用户信息
     */
    Map<String, Object> getCachedUserInfo(String userId);

    /**
     * 批量获取用户基本信息
     * @param userIds 用户ID列表
     * @return 用户信息映射
     */
    Map<String, Map<String, Object>> batchGetUserInfo(List<String> userIds);

    // ==================== 用户活跃状态缓存 ====================

    /**
     * 缓存用户活跃状态
     * @param userId 用户ID
     * @param onlineUser 在线用户信息
     * @return 是否成功
     */
    boolean cacheUserActivity(String userId, AicgOnlineUsers onlineUser);

    /**
     * 获取缓存的用户活跃状态
     * @param userId 用户ID
     * @return 在线用户信息
     */
    AicgOnlineUsers getCachedUserActivity(String userId);

    /**
     * 批量缓存用户活跃状态
     * @param userActivities 用户活跃状态映射
     * @return 成功缓存的数量
     */
    int batchCacheUserActivity(Map<String, AicgOnlineUsers> userActivities);

    /**
     * 批量获取用户活跃状态
     * @param userIds 用户ID列表
     * @return 用户活跃状态映射
     */
    Map<String, AicgOnlineUsers> batchGetUserActivity(List<String> userIds);

    // ==================== 会话缓存 ====================

    /**
     * 缓存用户会话信息
     * @param sessionId 会话ID
     * @param userId 用户ID
     * @param sessionInfo 会话信息
     * @return 是否成功
     */
    boolean cacheUserSession(String sessionId, String userId, Map<String, Object> sessionInfo);

    /**
     * 获取缓存的会话信息
     * @param sessionId 会话ID
     * @return 会话信息
     */
    Map<String, Object> getCachedSession(String sessionId);

    /**
     * 根据会话ID获取用户ID
     * @param sessionId 会话ID
     * @return 用户ID
     */
    String getUserIdBySession(String sessionId);

    /**
     * 删除会话缓存
     * @param sessionId 会话ID
     * @return 是否成功
     */
    boolean removeSessionCache(String sessionId);

    // ==================== 批量更新缓存 ====================

    /**
     * 缓存批量更新数据
     * @param batchKey 批量键
     * @param updateList 更新数据列表
     * @return 是否成功
     */
    boolean cacheBatchUpdate(String batchKey, List<UserActivityUpdateDTO> updateList);

    /**
     * 获取缓存的批量更新数据
     * @param batchKey 批量键
     * @return 更新数据列表
     */
    List<UserActivityUpdateDTO> getCachedBatchUpdate(String batchKey);

    /**
     * 删除批量更新缓存
     * @param batchKey 批量键
     * @return 是否成功
     */
    boolean removeBatchUpdateCache(String batchKey);

    // ==================== 统计信息缓存 ====================

    /**
     * 缓存在线用户统计信息
     * @param statsKey 统计键
     * @param stats 统计信息
     * @param expireSeconds 过期时间（秒）
     * @return 是否成功
     */
    boolean cacheOnlineStats(String statsKey, Map<String, Object> stats, long expireSeconds);

    /**
     * 获取缓存的在线用户统计信息
     * @param statsKey 统计键
     * @return 统计信息
     */
    Map<String, Object> getCachedOnlineStats(String statsKey);

    /**
     * 缓存统计列表数据
     * @param statsKey 统计键
     * @param statsList 统计列表数据
     * @param expireSeconds 过期时间（秒）
     * @return 是否成功
     */
    boolean cacheStatsList(String statsKey, List<Map<String, Object>> statsList, long expireSeconds);

    /**
     * 获取缓存的统计列表数据
     * @param statsKey 统计键
     * @return 统计列表数据
     */
    List<Map<String, Object>> getCachedStatsList(String statsKey);

    /**
     * 缓存用户在线状态
     * @param userId 用户ID
     * @param isOnline 是否在线
     * @param expireSeconds 过期时间（秒）
     * @return 是否成功
     */
    boolean cacheUserOnlineStatus(String userId, boolean isOnline, long expireSeconds);

    /**
     * 获取缓存的用户在线状态
     * @param userId 用户ID
     * @return 是否在线
     */
    Boolean getCachedUserOnlineStatus(String userId);

    /**
     * 缓存整数统计数据
     * @param statsKey 统计键
     * @param value 整数值
     * @param expireSeconds 过期时间（秒）
     * @return 是否成功
     */
    boolean cacheStatsInt(String statsKey, int value, long expireSeconds);

    /**
     * 获取缓存的整数统计数据
     * @param statsKey 统计键
     * @return 整数值
     */
    Integer getCachedStatsInt(String statsKey);

    // ==================== 缓存管理 ====================

    /**
     * 预热用户活跃状态缓存
     * @param userIds 需要预热的用户ID列表
     * @return 预热成功的数量
     */
    int warmupUserActivityCache(List<String> userIds);

    /**
     * 清理过期的缓存数据
     * @return 清理的数量
     */
    int cleanExpiredCache();

    /**
     * 清理指定用户的所有缓存
     * @param userId 用户ID
     * @return 清理的数量
     */
    int clearUserCache(String userId);

    /**
     * 获取缓存统计信息
     * @return 缓存统计
     */
    Map<String, Object> getCacheStats();

    /**
     * 检查缓存连接状态
     * @return 是否连接正常
     */
    boolean checkCacheHealth();

    // ==================== 缓存键管理 ====================

    /**
     * 获取用户信息缓存键
     * @param userId 用户ID
     * @return 缓存键
     */
    String getUserInfoCacheKey(String userId);

    /**
     * 获取用户活跃状态缓存键
     * @param userId 用户ID
     * @return 缓存键
     */
    String getUserActivityCacheKey(String userId);

    /**
     * 获取会话缓存键
     * @param sessionId 会话ID
     * @return 缓存键
     */
    String getSessionCacheKey(String sessionId);

    /**
     * 获取批量更新缓存键
     * @param batchKey 批量键
     * @return 缓存键
     */
    String getBatchUpdateCacheKey(String batchKey);

    /**
     * 获取统计信息缓存键
     * @param statsKey 统计键
     * @return 缓存键
     */
    String getStatsCacheKey(String statsKey);

    // ==================== 高级功能 ====================

    /**
     * 设置缓存过期时间
     * @param key 缓存键
     * @param expireSeconds 过期时间（秒）
     * @return 是否成功
     */
    boolean expire(String key, long expireSeconds);

    /**
     * 获取缓存剩余过期时间
     * @param key 缓存键
     * @return 剩余时间（秒），-1表示永不过期，-2表示键不存在
     */
    long getExpire(String key);

    /**
     * 检查缓存键是否存在
     * @param key 缓存键
     * @return 是否存在
     */
    boolean exists(String key);

    /**
     * 删除缓存
     * @param key 缓存键
     * @return 是否成功
     */
    boolean delete(String key);

    /**
     * 批量删除缓存
     * @param keys 缓存键列表
     * @return 删除的数量
     */
    long batchDelete(List<String> keys);

    /**
     * 根据模式删除缓存
     * @param pattern 键模式（支持通配符*）
     * @return 删除的数量
     */
    long deleteByPattern(String pattern);

    /**
     * 获取匹配模式的所有键
     * @param pattern 键模式（支持通配符*）
     * @return 匹配的键集合
     */
    Set<String> getKeysByPattern(String pattern);
}
