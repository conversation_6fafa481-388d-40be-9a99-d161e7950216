package org.jeecg.modules.system.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

/**
 * @Description: 用户注册请求DTO
 * @Author: jeecg-boot
 * @Date: 2025-06-19
 * @Version: V1.0
 */
@Data
@ApiModel(value = "RegisterDTO", description = "用户注册请求DTO")
public class RegisterDTO {

    @ApiModelProperty(value = "注册类型：phone-手机号,email-邮箱,wechat-微信", required = true)
    @NotBlank(message = "注册类型不能为空")
    @Pattern(regexp = "^(phone|email|wechat)$", message = "注册类型只能是phone、email或wechat")
    private String type;

    @ApiModelProperty(value = "用户名（手机号/邮箱）")
    private String username;

    @ApiModelProperty(value = "密码")
    @Size(min = 8, message = "密码长度不能少于8位")
    private String password;

    @ApiModelProperty(value = "确认密码")
    private String confirmPassword;

    @ApiModelProperty(value = "手机号")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String phone;

    @ApiModelProperty(value = "邮箱")
    @Email(message = "邮箱格式不正确")
    private String email;

    @ApiModelProperty(value = "验证码")
    private String verifyCode;

    @ApiModelProperty(value = "图形验证码")
    private String captcha;

    @ApiModelProperty(value = "图形验证码Key")
    private String captchaKey;

    @ApiModelProperty(value = "邀请码")
    private String inviteCode;

    @ApiModelProperty(value = "邀请码来源：link-链接,manual-手动输入")
    private String inviteSource;

    @ApiModelProperty(value = "昵称")
    private String nickname;

    @ApiModelProperty(value = "微信信息")
    private WechatInfo wechatInfo;

    @Data
    @ApiModel(value = "WechatInfo", description = "微信用户信息")
    public static class WechatInfo {
        @ApiModelProperty(value = "微信OpenID")
        private String openid;

        @ApiModelProperty(value = "微信昵称")
        private String nickname;

        @ApiModelProperty(value = "微信头像")
        private String avatar;

        @ApiModelProperty(value = "微信UnionID")
        private String unionid;
    }
}
