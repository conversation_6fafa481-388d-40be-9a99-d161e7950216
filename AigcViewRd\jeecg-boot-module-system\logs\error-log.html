<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html>
  <head>
    <title>Logback Log Messages</title>
<style  type="text/css">
table { margin-left: 2em; margin-right: 2em; border-left: 2px solid #AAA; }
TR.even { background: #FFFFFF; }
TR.odd { background: #EAEAEA; }
TR.warn TD.Level, TR.error TD.Level, TR.fatal TD.Level {font-weight: bold; color: #FF4040 }
TD { padding-right: 1ex; padding-left: 1ex; border-right: 2px solid #AAA; }
TD.Time, TD.Date { text-align: right; font-family: courier, monospace; font-size: smaller; }
TD.Thread { text-align: left; }
TD.Level { text-align: right; }
TD.Logger { text-align: left; }
TR.header { background: #596ED5; color: #FFF; font-weight: bold; font-size: larger; }
TD.Exception { background: #A2AEE8; font-family: courier, monospace;}
</style>

  </head>
<body>
<hr/>
<p>Log session start time Thu Jun 26 22:29:01 CST 2025</p><p></p>

<table cellspacing="0">
<tr class="header">
<td class="Level">Level</td>
<td class="Date">Date</td>
<td class="Message">Message</td>
<td class="MethodOfCaller">MethodOfCaller</td>
<td class="FileOfCaller">FileOfCaller</td>
<td class="LineOfCaller">LineOfCaller</td>
</tr>


<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-06-26 22:29:23,600</td>
<td class="Message">

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 8080 was already in use.

Action:

Identify and stop the process that&#39;s listening on port 8080 or configure this application to listen on another port.
</td>
<td class="MethodOfCaller">report</td>
<td class="FileOfCaller">LoggingFailureAnalysisReporter.java</td>
<td class="LineOfCaller">40</td>
</tr>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html>
  <head>
    <title>Logback Log Messages</title>
<style  type="text/css">
table { margin-left: 2em; margin-right: 2em; border-left: 2px solid #AAA; }
TR.even { background: #FFFFFF; }
TR.odd { background: #EAEAEA; }
TR.warn TD.Level, TR.error TD.Level, TR.fatal TD.Level {font-weight: bold; color: #FF4040 }
TD { padding-right: 1ex; padding-left: 1ex; border-right: 2px solid #AAA; }
TD.Time, TD.Date { text-align: right; font-family: courier, monospace; font-size: smaller; }
TD.Thread { text-align: left; }
TD.Level { text-align: right; }
TD.Logger { text-align: left; }
TR.header { background: #596ED5; color: #FFF; font-weight: bold; font-size: larger; }
TD.Exception { background: #A2AEE8; font-family: courier, monospace;}
</style>

  </head>
<body>
<hr/>
<p>Log session start time Fri Jun 27 15:27:30 CST 2025</p><p></p>

<table cellspacing="0">
<tr class="header">
<td class="Level">Level</td>
<td class="Date">Date</td>
<td class="Message">Message</td>
<td class="MethodOfCaller">MethodOfCaller</td>
<td class="FileOfCaller">FileOfCaller</td>
<td class="LineOfCaller">LineOfCaller</td>
</tr>


<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-06-27 15:27:37,646</td>
<td class="Message">mapper[org.jeecg.modules.demo.exchangecode.mapper.AicgExchangeCodeMapper.useExchangeCode] is ignored, because it exists, maybe from xml file</td>
<td class="MethodOfCaller">addMappedStatement</td>
<td class="FileOfCaller">MybatisConfiguration.java</td>
<td class="LineOfCaller">100</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-06-27 15:27:37,647</td>
<td class="Message">mapper[org.jeecg.modules.demo.exchangecode.mapper.AicgExchangeCodeMapper.updateExpiredCodes] is ignored, because it exists, maybe from xml file</td>
<td class="MethodOfCaller">addMappedStatement</td>
<td class="FileOfCaller">MybatisConfiguration.java</td>
<td class="LineOfCaller">100</td>
</tr>

<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-06-27 15:27:37,652</td>
<td class="Message">mapper[org.jeecg.modules.demo.exchangecode.mapper.AicgExchangeCodeMapper.getByCode] is ignored, because it exists, maybe from xml file</td>
<td class="MethodOfCaller">addMappedStatement</td>
<td class="FileOfCaller">MybatisConfiguration.java</td>
<td class="LineOfCaller">100</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-06-27 15:27:37,655</td>
<td class="Message">mapper[org.jeecg.modules.demo.exchangecode.mapper.AicgExchangeCodeMapper.getUserUsedCodes] is ignored, because it exists, maybe from xml file</td>
<td class="MethodOfCaller">addMappedStatement</td>
<td class="FileOfCaller">MybatisConfiguration.java</td>
<td class="LineOfCaller">100</td>
</tr>

<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-06-27 15:27:37,917</td>
<td class="Message">mapper[org.jeecg.modules.demo.userprofile.mapper.AicgUserProfileMapper.deductBalance] is ignored, because it exists, maybe from xml file</td>
<td class="MethodOfCaller">addMappedStatement</td>
<td class="FileOfCaller">MybatisConfiguration.java</td>
<td class="LineOfCaller">100</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-06-27 15:27:37,919</td>
<td class="Message">mapper[org.jeecg.modules.demo.userprofile.mapper.AicgUserProfileMapper.getByUserId] is ignored, because it exists, maybe from xml file</td>
<td class="MethodOfCaller">addMappedStatement</td>
<td class="FileOfCaller">MybatisConfiguration.java</td>
<td class="LineOfCaller">100</td>
</tr>

<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-06-27 15:27:37,923</td>
<td class="Message">mapper[org.jeecg.modules.demo.userprofile.mapper.AicgUserProfileMapper.updateApiKey] is ignored, because it exists, maybe from xml file</td>
<td class="MethodOfCaller">addMappedStatement</td>
<td class="FileOfCaller">MybatisConfiguration.java</td>
<td class="LineOfCaller">100</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-06-27 15:27:37,924</td>
<td class="Message">mapper[org.jeecg.modules.demo.userprofile.mapper.AicgUserProfileMapper.addBalance] is ignored, because it exists, maybe from xml file</td>
<td class="MethodOfCaller">addMappedStatement</td>
<td class="FileOfCaller">MybatisConfiguration.java</td>
<td class="LineOfCaller">100</td>
</tr>

<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-06-27 15:27:37,926</td>
<td class="Message">mapper[org.jeecg.modules.demo.userprofile.mapper.AicgUserProfileMapper.updateBalance] is ignored, because it exists, maybe from xml file</td>
<td class="MethodOfCaller">addMappedStatement</td>
<td class="FileOfCaller">MybatisConfiguration.java</td>
<td class="LineOfCaller">100</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-06-27 15:27:48,915</td>
<td class="Message">

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 8080 was already in use.

Action:

Identify and stop the process that&#39;s listening on port 8080 or configure this application to listen on another port.
</td>
<td class="MethodOfCaller">report</td>
<td class="FileOfCaller">LoggingFailureAnalysisReporter.java</td>
<td class="LineOfCaller">40</td>
</tr>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html>
  <head>
    <title>Logback Log Messages</title>
<style  type="text/css">
table { margin-left: 2em; margin-right: 2em; border-left: 2px solid #AAA; }
TR.even { background: #FFFFFF; }
TR.odd { background: #EAEAEA; }
TR.warn TD.Level, TR.error TD.Level, TR.fatal TD.Level {font-weight: bold; color: #FF4040 }
TD { padding-right: 1ex; padding-left: 1ex; border-right: 2px solid #AAA; }
TD.Time, TD.Date { text-align: right; font-family: courier, monospace; font-size: smaller; }
TD.Thread { text-align: left; }
TD.Level { text-align: right; }
TD.Logger { text-align: left; }
TR.header { background: #596ED5; color: #FFF; font-weight: bold; font-size: larger; }
TD.Exception { background: #A2AEE8; font-family: courier, monospace;}
</style>

  </head>
<body>
<hr/>
<p>Log session start time Fri Jun 27 17:53:56 CST 2025</p><p></p>

<table cellspacing="0">
<tr class="header">
<td class="Level">Level</td>
<td class="Date">Date</td>
<td class="Message">Message</td>
<td class="MethodOfCaller">MethodOfCaller</td>
<td class="FileOfCaller">FileOfCaller</td>
<td class="LineOfCaller">LineOfCaller</td>
</tr>


<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-06-27 17:54:03,874</td>
<td class="Message">mapper[org.jeecg.modules.demo.exchangecode.mapper.AicgExchangeCodeMapper.useExchangeCode] is ignored, because it exists, maybe from xml file</td>
<td class="MethodOfCaller">addMappedStatement</td>
<td class="FileOfCaller">MybatisConfiguration.java</td>
<td class="LineOfCaller">100</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-06-27 17:54:03,879</td>
<td class="Message">mapper[org.jeecg.modules.demo.exchangecode.mapper.AicgExchangeCodeMapper.getUserUsedCodes] is ignored, because it exists, maybe from xml file</td>
<td class="MethodOfCaller">addMappedStatement</td>
<td class="FileOfCaller">MybatisConfiguration.java</td>
<td class="LineOfCaller">100</td>
</tr>

<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-06-27 17:54:03,880</td>
<td class="Message">mapper[org.jeecg.modules.demo.exchangecode.mapper.AicgExchangeCodeMapper.updateExpiredCodes] is ignored, because it exists, maybe from xml file</td>
<td class="MethodOfCaller">addMappedStatement</td>
<td class="FileOfCaller">MybatisConfiguration.java</td>
<td class="LineOfCaller">100</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-06-27 17:54:03,881</td>
<td class="Message">mapper[org.jeecg.modules.demo.exchangecode.mapper.AicgExchangeCodeMapper.getByCode] is ignored, because it exists, maybe from xml file</td>
<td class="MethodOfCaller">addMappedStatement</td>
<td class="FileOfCaller">MybatisConfiguration.java</td>
<td class="LineOfCaller">100</td>
</tr>

<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-06-27 17:54:04,135</td>
<td class="Message">mapper[org.jeecg.modules.demo.userprofile.mapper.AicgUserProfileMapper.updateApiKey] is ignored, because it exists, maybe from xml file</td>
<td class="MethodOfCaller">addMappedStatement</td>
<td class="FileOfCaller">MybatisConfiguration.java</td>
<td class="LineOfCaller">100</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-06-27 17:54:04,136</td>
<td class="Message">mapper[org.jeecg.modules.demo.userprofile.mapper.AicgUserProfileMapper.addBalance] is ignored, because it exists, maybe from xml file</td>
<td class="MethodOfCaller">addMappedStatement</td>
<td class="FileOfCaller">MybatisConfiguration.java</td>
<td class="LineOfCaller">100</td>
</tr>

<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-06-27 17:54:04,137</td>
<td class="Message">mapper[org.jeecg.modules.demo.userprofile.mapper.AicgUserProfileMapper.updateBalance] is ignored, because it exists, maybe from xml file</td>
<td class="MethodOfCaller">addMappedStatement</td>
<td class="FileOfCaller">MybatisConfiguration.java</td>
<td class="LineOfCaller">100</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-06-27 17:54:04,137</td>
<td class="Message">mapper[org.jeecg.modules.demo.userprofile.mapper.AicgUserProfileMapper.deductBalance] is ignored, because it exists, maybe from xml file</td>
<td class="MethodOfCaller">addMappedStatement</td>
<td class="FileOfCaller">MybatisConfiguration.java</td>
<td class="LineOfCaller">100</td>
</tr>

<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-06-27 17:54:04,138</td>
<td class="Message">mapper[org.jeecg.modules.demo.userprofile.mapper.AicgUserProfileMapper.getByUserId] is ignored, because it exists, maybe from xml file</td>
<td class="MethodOfCaller">addMappedStatement</td>
<td class="FileOfCaller">MybatisConfiguration.java</td>
<td class="LineOfCaller">100</td>
</tr>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html>
  <head>
    <title>Logback Log Messages</title>
<style  type="text/css">
table { margin-left: 2em; margin-right: 2em; border-left: 2px solid #AAA; }
TR.even { background: #FFFFFF; }
TR.odd { background: #EAEAEA; }
TR.warn TD.Level, TR.error TD.Level, TR.fatal TD.Level {font-weight: bold; color: #FF4040 }
TD { padding-right: 1ex; padding-left: 1ex; border-right: 2px solid #AAA; }
TD.Time, TD.Date { text-align: right; font-family: courier, monospace; font-size: smaller; }
TD.Thread { text-align: left; }
TD.Level { text-align: right; }
TD.Logger { text-align: left; }
TR.header { background: #596ED5; color: #FFF; font-weight: bold; font-size: larger; }
TD.Exception { background: #A2AEE8; font-family: courier, monospace;}
</style>

  </head>
<body>
<hr/>
<p>Log session start time Fri Jun 27 18:17:30 CST 2025</p><p></p>

<table cellspacing="0">
<tr class="header">
<td class="Level">Level</td>
<td class="Date">Date</td>
<td class="Message">Message</td>
<td class="MethodOfCaller">MethodOfCaller</td>
<td class="FileOfCaller">FileOfCaller</td>
<td class="LineOfCaller">LineOfCaller</td>
</tr>


<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-06-27 18:17:38,772</td>
<td class="Message">mapper[org.jeecg.modules.demo.exchangecode.mapper.AicgExchangeCodeMapper.updateExpiredCodes] is ignored, because it exists, maybe from xml file</td>
<td class="MethodOfCaller">addMappedStatement</td>
<td class="FileOfCaller">MybatisConfiguration.java</td>
<td class="LineOfCaller">100</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-06-27 18:17:38,777</td>
<td class="Message">mapper[org.jeecg.modules.demo.exchangecode.mapper.AicgExchangeCodeMapper.getUserUsedCodes] is ignored, because it exists, maybe from xml file</td>
<td class="MethodOfCaller">addMappedStatement</td>
<td class="FileOfCaller">MybatisConfiguration.java</td>
<td class="LineOfCaller">100</td>
</tr>

<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-06-27 18:17:38,778</td>
<td class="Message">mapper[org.jeecg.modules.demo.exchangecode.mapper.AicgExchangeCodeMapper.useExchangeCode] is ignored, because it exists, maybe from xml file</td>
<td class="MethodOfCaller">addMappedStatement</td>
<td class="FileOfCaller">MybatisConfiguration.java</td>
<td class="LineOfCaller">100</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-06-27 18:17:38,779</td>
<td class="Message">mapper[org.jeecg.modules.demo.exchangecode.mapper.AicgExchangeCodeMapper.getByCode] is ignored, because it exists, maybe from xml file</td>
<td class="MethodOfCaller">addMappedStatement</td>
<td class="FileOfCaller">MybatisConfiguration.java</td>
<td class="LineOfCaller">100</td>
</tr>

<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-06-27 18:17:39,035</td>
<td class="Message">mapper[org.jeecg.modules.demo.userprofile.mapper.AicgUserProfileMapper.updateBalance] is ignored, because it exists, maybe from xml file</td>
<td class="MethodOfCaller">addMappedStatement</td>
<td class="FileOfCaller">MybatisConfiguration.java</td>
<td class="LineOfCaller">100</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-06-27 18:17:39,036</td>
<td class="Message">mapper[org.jeecg.modules.demo.userprofile.mapper.AicgUserProfileMapper.deductBalance] is ignored, because it exists, maybe from xml file</td>
<td class="MethodOfCaller">addMappedStatement</td>
<td class="FileOfCaller">MybatisConfiguration.java</td>
<td class="LineOfCaller">100</td>
</tr>

<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-06-27 18:17:39,037</td>
<td class="Message">mapper[org.jeecg.modules.demo.userprofile.mapper.AicgUserProfileMapper.getByUserId] is ignored, because it exists, maybe from xml file</td>
<td class="MethodOfCaller">addMappedStatement</td>
<td class="FileOfCaller">MybatisConfiguration.java</td>
<td class="LineOfCaller">100</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-06-27 18:17:39,040</td>
<td class="Message">mapper[org.jeecg.modules.demo.userprofile.mapper.AicgUserProfileMapper.addBalance] is ignored, because it exists, maybe from xml file</td>
<td class="MethodOfCaller">addMappedStatement</td>
<td class="FileOfCaller">MybatisConfiguration.java</td>
<td class="LineOfCaller">100</td>
</tr>

<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-06-27 18:17:39,041</td>
<td class="Message">mapper[org.jeecg.modules.demo.userprofile.mapper.AicgUserProfileMapper.updateApiKey] is ignored, because it exists, maybe from xml file</td>
<td class="MethodOfCaller">addMappedStatement</td>
<td class="FileOfCaller">MybatisConfiguration.java</td>
<td class="LineOfCaller">100</td>
</tr>
