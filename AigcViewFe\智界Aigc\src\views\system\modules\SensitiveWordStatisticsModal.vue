<template>
  <a-modal
    title="敏感词统计分析"
    :width="1200"
    :visible="visible"
    :footer="null"
    @cancel="handleCancel">
    
    <div class="statistics-container">
      <!-- 时间范围选择 -->
      <div class="time-range-section">
        <a-radio-group v-model="timeRange" @change="handleTimeRangeChange">
          <a-radio-button value="7">最近7天</a-radio-button>
          <a-radio-button value="30">最近30天</a-radio-button>
          <a-radio-button value="90">最近90天</a-radio-button>
        </a-radio-group>
        <a-button type="primary" @click="loadStatistics" :loading="loading" style="margin-left: 16px;">
          <a-icon type="reload"/>
          刷新数据
        </a-button>
      </div>

      <!-- 总体统计卡片 -->
      <div class="overview-cards" v-if="overviewData">
        <a-row :gutter="16">
          <a-col :span="6">
            <a-card>
              <a-statistic
                title="敏感词总数"
                :value="overviewData.total_words"
                :value-style="{ color: '#1890ff' }"
              />
            </a-card>
          </a-col>
          <a-col :span="6">
            <a-card>
              <a-statistic
                title="启用敏感词"
                :value="overviewData.enabled_words"
                :value-style="{ color: '#52c41a' }"
              />
            </a-card>
          </a-col>
          <a-col :span="6">
            <a-card>
              <a-statistic
                title="总命中次数"
                :value="overviewData.total_hits"
                :value-style="{ color: '#fa8c16' }"
              />
            </a-card>
          </a-col>
          <a-col :span="6">
            <a-card>
              <a-statistic
                title="高危敏感词"
                :value="overviewData.high_risk_words"
                :value-style="{ color: '#f5222d' }"
              />
            </a-card>
          </a-col>
        </a-row>
      </div>

      <!-- 图表区域 -->
      <div class="charts-section">
        <a-row :gutter="16">
          <!-- 分类统计饼图 -->
          <a-col :span="12">
            <a-card title="敏感词分类分布" :loading="loading">
              <div ref="categoryChart" style="height: 300px;"></div>
            </a-card>
          </a-col>
          
          <!-- 级别统计饼图 -->
          <a-col :span="12">
            <a-card title="敏感词级别分布" :loading="loading">
              <div ref="levelChart" style="height: 300px;"></div>
            </a-card>
          </a-col>
        </a-row>

        <!-- 命中趋势图 -->
        <a-row :gutter="16" style="margin-top: 16px;">
          <a-col :span="24">
            <a-card title="敏感词命中趋势" :loading="loading">
              <div ref="trendChart" style="height: 300px;"></div>
            </a-card>
          </a-col>
        </a-row>
      </div>

      <!-- 热门敏感词表格 -->
      <div class="top-words-section">
        <a-card title="热门敏感词 TOP 10" :loading="loading">
          <a-table
            :columns="topWordsColumns"
            :dataSource="topWordsData"
            :pagination="false"
            size="small"
            rowKey="word"
          >
            <template slot="level" slot-scope="text, record">
              <a-tag :color="getLevelColor(record.level)">
                {{ record.level_text }}
              </a-tag>
            </template>
            <template slot="hitCount" slot-scope="text">
              <a-badge :count="text" :number-style="{ backgroundColor: '#52c41a' }" />
            </template>
          </a-table>
        </a-card>
      </div>

      <!-- 详细统计表格 -->
      <div class="detail-stats-section">
        <a-tabs>
          <a-tab-pane key="category" tab="分类统计">
            <a-table
              :columns="categoryStatsColumns"
              :dataSource="categoryStatsData"
              :pagination="false"
              size="small"
              rowKey="category"
            />
          </a-tab-pane>
          <a-tab-pane key="level" tab="级别统计">
            <a-table
              :columns="levelStatsColumns"
              :dataSource="levelStatsData"
              :pagination="false"
              size="small"
              rowKey="level"
            />
          </a-tab-pane>
        </a-tabs>
      </div>
    </div>
  </a-modal>
</template>

<script>
  import * as echarts from 'echarts'
  import {
    getSensitiveWordStatistics,
    getSensitiveWordTopHits,
    getSensitiveWordCategoryStats,
    getSensitiveWordLevelStats,
    getSensitiveWordHitTrend
  } from '@/api/system/sensitiveWord'
  import { initDictOptions, filterDictText } from '@/components/dict/JDictSelectUtil'

  export default {
    name: 'SensitiveWordStatisticsModal',
    data() {
      return {
        visible: false,
        loading: false,
        timeRange: '7',

        // 字典数据
        categoryDictOptions: [],

        // 数据
        overviewData: null,
        topWordsData: [],
        categoryStatsData: [],
        levelStatsData: [],
        hitTrendData: [],

        // 图表实例
        categoryChart: null,
        levelChart: null,
        trendChart: null,

        // 表格列定义
        topWordsColumns: [
          { title: '排名', dataIndex: 'rank', width: 60, customRender: (text, record, index) => index + 1 },
          { title: '敏感词', dataIndex: 'word', width: 120 },
          {
            title: '分类',
            dataIndex: 'category',
            width: 80,
            customRender: (text) => {
              return this.getCategoryText(text)
            }
          },
          { title: '级别', dataIndex: 'level', width: 80, scopedSlots: { customRender: 'level' } },
          { title: '命中次数', dataIndex: 'hit_count', width: 100, scopedSlots: { customRender: 'hitCount' } }
        ],

        categoryStatsColumns: [
          {
            title: '分类',
            dataIndex: 'category',
            width: 100,
            customRender: (text) => {
              return this.getCategoryText(text)
            }
          },
          { title: '敏感词数量', dataIndex: 'word_count', width: 120 },
          { title: '总命中次数', dataIndex: 'total_hits', width: 120 },
          { title: '启用数量', dataIndex: 'enabled_count', width: 100 },
          { title: '禁用数量', dataIndex: 'disabled_count', width: 100 }
        ],

        levelStatsColumns: [
          { title: '级别', dataIndex: 'level_text', width: 100 },
          { title: '敏感词数量', dataIndex: 'word_count', width: 120 },
          { title: '总命中次数', dataIndex: 'total_hits', width: 120 },
          { title: '启用数量', dataIndex: 'enabled_count', width: 100 }
        ]
      }
    },

    created() {
      this.initDictConfig()
    },
    
    methods: {
      // 初始化字典配置
      async initDictConfig() {
        try {
          const res = await initDictOptions('sensitive_word_category')
          if (res.success) {
            this.categoryDictOptions = res.result
          }
        } catch (error) {
          console.error('加载敏感词分类字典失败:', error)
        }
      },

      // 获取分类中文文本
      getCategoryText(categoryValue) {
        if (!categoryValue || !this.categoryDictOptions.length) {
          return categoryValue
        }
        return filterDictText(this.categoryDictOptions, categoryValue) || categoryValue
      },

      show() {
        this.visible = true;
        this.loadStatistics();
      },

      handleCancel() {
        this.visible = false;
        this.destroyCharts();
      },

      handleTimeRangeChange() {
        this.loadStatistics();
      },
      
      async loadStatistics() {
        this.loading = true;
        
        try {
          // 并行加载所有统计数据
          const [
            overviewRes,
            topWordsRes,
            categoryStatsRes,
            levelStatsRes,
            hitTrendRes
          ] = await Promise.all([
            getSensitiveWordStatistics(),
            getSensitiveWordTopHits(10),
            getSensitiveWordCategoryStats(),
            getSensitiveWordLevelStats(),
            getSensitiveWordHitTrend(parseInt(this.timeRange))
          ]);
          
          // 处理数据
          if (overviewRes.success) {
            this.overviewData = overviewRes.result;
          }
          
          if (topWordsRes.success) {
            this.topWordsData = topWordsRes.result;
          }
          
          if (categoryStatsRes.success) {
            this.categoryStatsData = categoryStatsRes.result;
          }
          
          if (levelStatsRes.success) {
            this.levelStatsData = levelStatsRes.result;
          }
          
          if (hitTrendRes.success) {
            this.hitTrendData = hitTrendRes.result;
          }
          
          // 渲染图表
          this.$nextTick(() => {
            this.renderCharts();
          });
          
        } catch (error) {
          this.$message.error('加载统计数据失败：' + error.message);
        } finally {
          this.loading = false;
        }
      },
      
      renderCharts() {
        this.renderCategoryChart();
        this.renderLevelChart();
        this.renderTrendChart();
      },
      
      renderCategoryChart() {
        if (!this.$refs.categoryChart) return;

        if (this.categoryChart) {
          this.categoryChart.dispose();
        }

        this.categoryChart = echarts.init(this.$refs.categoryChart);

        const option = {
          tooltip: {
            trigger: 'item',
            formatter: '{a} <br/>{b}: {c} ({d}%)'
          },
          series: [{
            name: '分类分布',
            type: 'pie',
            radius: ['40%', '70%'],
            data: this.categoryStatsData.map(item => ({
              name: this.getCategoryText(item.category),
              value: item.word_count
            }))
          }]
        };

        this.categoryChart.setOption(option);
      },
      
      renderLevelChart() {
        if (!this.$refs.levelChart) return;
        
        if (this.levelChart) {
          this.levelChart.dispose();
        }
        
        this.levelChart = echarts.init(this.$refs.levelChart);
        
        const option = {
          tooltip: {
            trigger: 'item',
            formatter: '{a} <br/>{b}: {c} ({d}%)'
          },
          series: [{
            name: '级别分布',
            type: 'pie',
            radius: ['40%', '70%'],
            data: this.levelStatsData.map(item => ({
              name: item.level_text,
              value: item.word_count
            }))
          }]
        };
        
        this.levelChart.setOption(option);
      },
      
      renderTrendChart() {
        if (!this.$refs.trendChart) return;
        
        if (this.trendChart) {
          this.trendChart.dispose();
        }
        
        this.trendChart = echarts.init(this.$refs.trendChart);
        
        const option = {
          tooltip: {
            trigger: 'axis'
          },
          xAxis: {
            type: 'category',
            data: this.hitTrendData.map(item => item.hit_date)
          },
          yAxis: {
            type: 'value'
          },
          series: [{
            name: '命中次数',
            type: 'line',
            data: this.hitTrendData.map(item => item.hit_count),
            smooth: true
          }]
        };
        
        this.trendChart.setOption(option);
      },
      
      destroyCharts() {
        if (this.categoryChart) {
          this.categoryChart.dispose();
          this.categoryChart = null;
        }
        if (this.levelChart) {
          this.levelChart.dispose();
          this.levelChart = null;
        }
        if (this.trendChart) {
          this.trendChart.dispose();
          this.trendChart = null;
        }
      },
      
      getLevelColor(level) {
        switch (level) {
          case 1: return 'green';
          case 2: return 'orange';
          case 3: return 'red';
          default: return 'default';
        }
      }
    },
    
    beforeDestroy() {
      this.destroyCharts();
    }
  }
</script>

<style lang="less" scoped>
  .statistics-container {
    .time-range-section {
      margin-bottom: 24px;
      display: flex;
      align-items: center;
    }
    
    .overview-cards {
      margin-bottom: 24px;
    }
    
    .charts-section {
      margin-bottom: 24px;
    }
    
    .top-words-section {
      margin-bottom: 24px;
    }
    
    .detail-stats-section {
      margin-bottom: 24px;
    }
  }
</style>
