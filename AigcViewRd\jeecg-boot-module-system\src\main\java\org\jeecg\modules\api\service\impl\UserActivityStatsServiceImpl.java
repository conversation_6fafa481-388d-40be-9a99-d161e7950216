package org.jeecg.modules.api.service.impl;

import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.api.config.UserActivityConfig;
import org.jeecg.modules.api.mapper.AicgOnlineUsersMapper;
import org.jeecg.modules.api.service.IUserActivityCacheService;
import org.jeecg.modules.api.service.IUserActivityStatsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.atomic.AtomicLong;

/**
 * @Description: 用户活跃状态统计服务实现类
 * @Author: AigcView
 * @Date: 2025-01-30
 * @Version: V1.0
 */
@Slf4j
@Service
public class UserActivityStatsServiceImpl implements IUserActivityStatsService {

    @Autowired
    private UserActivityConfig config;

    @Autowired
    private AicgOnlineUsersMapper onlineUsersMapper;

    @Autowired
    private IUserActivityCacheService cacheService;

    // 性能监控计数器
    private final AtomicLong queryCount = new AtomicLong(0);
    private final AtomicLong cacheHitCount = new AtomicLong(0);
    private final AtomicLong totalQueryTime = new AtomicLong(0);

    // ==================== 基础统计方法 ====================

    @Override
    public Map<String, Object> getMultiDimensionStats() {
        String cacheKey = "multi_dimension_stats";
        
        // 尝试从缓存获取
        Map<String, Object> cachedStats = cacheService.getCachedOnlineStats(cacheKey);
        if (cachedStats != null) {
            cacheHitCount.incrementAndGet();
            log.debug("从缓存获取多维度统计数据");
            return cachedStats;
        }

        long startTime = System.currentTimeMillis();
        queryCount.incrementAndGet();

        try {
            Map<String, Object> stats = onlineUsersMapper.getMultiDimensionStats();

            // 添加计算字段
            enrichMultiDimensionStats(stats);

            // 缓存结果
            cacheService.cacheOnlineStats(cacheKey, stats, config.getCacheDuration());
            
            long queryTime = System.currentTimeMillis() - startTime;
            totalQueryTime.addAndGet(queryTime);
            
            log.debug("获取多维度统计数据完成，耗时: {}ms", queryTime);
            return stats;
            
        } catch (Exception e) {
            log.error("获取多维度统计数据失败: {}", e.getMessage(), e);
            return getDefaultStats();
        }
    }

    @Override
    public int getCurrentOnlineUsersCount() {
        String cacheKey = "current_online_count";
        
        // 尝试从缓存获取
        Integer cachedCount = cacheService.getCachedStatsInt(cacheKey);
        if (cachedCount != null) {
            cacheHitCount.incrementAndGet();
            return cachedCount;
        }

        try {
            int count = onlineUsersMapper.getCurrentOnlineUsersCount();

            // 缓存结果（较短的缓存时间）
            cacheService.cacheStatsInt(cacheKey, count, 60); // 1分钟缓存
            
            queryCount.incrementAndGet();
            return count;
            
        } catch (Exception e) {
            log.error("获取当前在线用户数失败: {}", e.getMessage(), e);
            return 0;
        }
    }

    @Override
    public int getTodayActiveUsersCount() {
        String cacheKey = "today_active_count";

        // 尝试从缓存获取
        Integer cachedCount = cacheService.getCachedStatsInt(cacheKey);
        if (cachedCount != null) {
            cacheHitCount.incrementAndGet();
            return cachedCount;
        }

        try {
            int count = onlineUsersMapper.getTodayActiveUsersCount();

            // 缓存结果（5分钟缓存）
            cacheService.cacheStatsInt(cacheKey, count, 300);

            queryCount.incrementAndGet();
            return count;

        } catch (Exception e) {
            log.error("获取今日活跃用户数失败: {}", e.getMessage(), e);
            return 0;
        }
    }

    @Override
    public int getActiveUsersCount(int minutes) {
        try {
            // 使用多维度统计中的数据
            Map<String, Object> stats = getMultiDimensionStats();
            int count = 0;

            switch (minutes) {
                case 5:
                    count = (Integer) stats.getOrDefault("online_5min", 0);
                    break;
                case 15:
                    count = (Integer) stats.getOrDefault("online_15min", 0);
                    break;
                case 60:
                    count = (Integer) stats.getOrDefault("online_1hour", 0);
                    break;
                default:
                    // 对于其他时间范围，需要单独查询
                    count = queryActiveUsersCountByMinutes(minutes);
            }

            queryCount.incrementAndGet();
            return count;

        } catch (Exception e) {
            log.error("获取{}分钟内活跃用户数失败: {}", minutes, e.getMessage(), e);
            return 0;
        }
    }

    // ==================== 高级统计方法 ====================

    @Override
    public List<Map<String, Object>> getUserActivityDistribution() {
        try {
            List<Map<String, Object>> distribution = onlineUsersMapper.getUserActivityDistribution();
            queryCount.incrementAndGet();
            return distribution;

        } catch (Exception e) {
            log.error("获取用户活跃度分布失败: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<Map<String, Object>> getUserRetentionStats() {
        String cacheKey = "retention_stats";

        // 尝试从缓存获取
        List<Map<String, Object>> cachedData = cacheService.getCachedStatsList(cacheKey);
        if (cachedData != null) {
            cacheHitCount.incrementAndGet();
            return cachedData;
        }

        try {
            List<Map<String, Object>> retentionStats = onlineUsersMapper.getUserRetentionStats();

            // 计算留存率百分比
            enrichRetentionStats(retentionStats);

            // 缓存结果（较长缓存时间，因为留存数据变化较慢）
            cacheService.cacheStatsList(cacheKey, retentionStats, config.getCacheDuration() * 2);

            queryCount.incrementAndGet();
            return retentionStats;

        } catch (Exception e) {
            log.error("获取用户留存率统计失败: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<Map<String, Object>> getOnlineUsersDetail(int page, int size) {
        // 在线用户详情不缓存，因为数据变化频繁
        try {
            int offset = (page - 1) * size;
            List<Map<String, Object>> details = onlineUsersMapper.getOnlineUsersDetail(offset, size);
            
            queryCount.incrementAndGet();
            return details;
            
        } catch (Exception e) {
            log.error("获取在线用户详情失败: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<Map<String, Object>> getUserActivityTrend24Hours() {
        String cacheKey = "activity_trend_24h";

        // 尝试从缓存获取
        List<Map<String, Object>> cachedData = cacheService.getCachedStatsList(cacheKey);
        if (cachedData != null) {
            cacheHitCount.incrementAndGet();
            return cachedData;
        }

        try {
            List<Map<String, Object>> trend = onlineUsersMapper.getUserActivityTrend24Hours();

            // 填充缺失的小时数据
            trend = fillMissingHours(trend);

            // 缓存结果
            cacheService.cacheStatsList(cacheKey, trend, config.getCacheDuration());

            queryCount.incrementAndGet();
            return trend;

        } catch (Exception e) {
            log.error("获取24小时活跃趋势失败: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    // ==================== 辅助方法 ====================

    /**
     * 丰富多维度统计数据
     */
    private void enrichMultiDimensionStats(Map<String, Object> stats) {
        if (stats == null) return;
        
        // 计算活跃率
        int totalUsers = (Integer) stats.getOrDefault("total_users", 0);
        int activeToday = (Integer) stats.getOrDefault("active_today", 0);
        
        if (totalUsers > 0) {
            double activeRate = (double) activeToday / totalUsers * 100;
            stats.put("active_rate", Math.round(activeRate * 100.0) / 100.0);
        } else {
            stats.put("active_rate", 0.0);
        }
        
        // 添加统计时间
        stats.put("stats_time", new Date());
        stats.put("stats_timestamp", System.currentTimeMillis());
    }

    /**
     * 丰富留存率统计数据
     */
    private void enrichRetentionStats(List<Map<String, Object>> retentionStats) {
        for (Map<String, Object> stat : retentionStats) {
            int loginUsers = (Integer) stat.getOrDefault("login_users", 0);
            if (loginUsers > 0) {
                int retained1day = (Integer) stat.getOrDefault("retained_1day", 0);
                int retained7day = (Integer) stat.getOrDefault("retained_7day", 0);
                int retained30day = (Integer) stat.getOrDefault("retained_30day", 0);
                
                stat.put("retention_rate_1day", Math.round((double) retained1day / loginUsers * 10000.0) / 100.0);
                stat.put("retention_rate_7day", Math.round((double) retained7day / loginUsers * 10000.0) / 100.0);
                stat.put("retention_rate_30day", Math.round((double) retained30day / loginUsers * 10000.0) / 100.0);
            }
        }
    }

    /**
     * 填充缺失的小时数据
     */
    private List<Map<String, Object>> fillMissingHours(List<Map<String, Object>> trend) {
        // 实现24小时完整数据填充逻辑
        // 这里简化处理，实际应该填充所有24小时的数据
        return trend;
    }

    /**
     * 查询指定分钟数内的活跃用户数
     */
    private int queryActiveUsersCountByMinutes(int minutes) {
        // 这里需要实现自定义时间范围的查询
        // 暂时返回0，后续可以扩展
        return 0;
    }

    /**
     * 获取默认统计数据
     */
    private Map<String, Object> getDefaultStats() {
        Map<String, Object> defaultStats = new HashMap<>();
        defaultStats.put("online_5min", 0);
        defaultStats.put("online_15min", 0);
        defaultStats.put("online_1hour", 0);
        defaultStats.put("active_today", 0);
        defaultStats.put("total_online", 0);
        defaultStats.put("total_users", 0);
        defaultStats.put("active_rate", 0.0);
        defaultStats.put("stats_time", new Date());
        return defaultStats;
    }

    // ==================== 高级统计方法实现 ====================

    @Override
    public List<Map<String, Object>> getUserSessionStats(int days, int limit) {
        try {
            List<Map<String, Object>> sessionStats = onlineUsersMapper.getUserSessionStats(days, limit);
            queryCount.incrementAndGet();
            return sessionStats;

        } catch (Exception e) {
            log.error("获取用户会话统计失败: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<Map<String, Object>> getIpAddressStats(int hours, int minUsers, int limit) {
        String cacheKey = "ip_stats_" + hours + "_" + minUsers + "_" + limit;

        // 尝试从缓存获取
        List<Map<String, Object>> cachedData = cacheService.getCachedStatsList(cacheKey);
        if (cachedData != null) {
            cacheHitCount.incrementAndGet();
            return cachedData;
        }

        try {
            List<Map<String, Object>> ipStats = onlineUsersMapper.getIpAddressStats(hours, minUsers, limit);

            // 缓存结果
            cacheService.cacheStatsList(cacheKey, ipStats, config.getCacheDuration());

            queryCount.incrementAndGet();
            return ipStats;

        } catch (Exception e) {
            log.error("获取IP地址统计失败: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    // ==================== 用户状态查询方法实现 ====================

    @Override
    public boolean isUserOnline(String userId, int minutes) {
        if (userId == null || userId.trim().isEmpty()) {
            return false;
        }

        // 尝试从缓存获取
        Boolean cachedResult = cacheService.getCachedUserOnlineStatus(userId);
        if (cachedResult != null) {
            cacheHitCount.incrementAndGet();
            return cachedResult;
        }

        try {
            int result = onlineUsersMapper.isUserOnline(userId, minutes);
            boolean isOnline = result > 0;

            // 缓存结果（较短缓存时间）
            cacheService.cacheUserOnlineStatus(userId, isOnline, 60); // 1分钟缓存

            queryCount.incrementAndGet();
            return isOnline;

        } catch (Exception e) {
            log.error("检查用户在线状态失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public Date getUserLastActiveTime(String userId) {
        if (userId == null || userId.trim().isEmpty()) {
            return null;
        }

        try {
            Date lastActiveTime = onlineUsersMapper.getUserLastActiveTime(userId);
            queryCount.incrementAndGet();
            return lastActiveTime;

        } catch (Exception e) {
            log.error("获取用户最后活跃时间失败: {}", e.getMessage(), e);
            return null;
        }
    }

    @Override
    public long getUserOnlineDuration(String userId) {
        if (userId == null || userId.trim().isEmpty()) {
            return 0;
        }

        try {
            Date lastActiveTime = getUserLastActiveTime(userId);
            if (lastActiveTime == null) {
                return 0;
            }

            // 计算在线时长（分钟）
            long duration = (System.currentTimeMillis() - lastActiveTime.getTime()) / (1000 * 60);
            return Math.max(0, duration);

        } catch (Exception e) {
            log.error("计算用户在线时长失败: {}", e.getMessage(), e);
            return 0;
        }
    }

    // ==================== 性能监控方法实现 ====================

    @Override
    public Map<String, Object> getTablePerformanceStats() {
        String cacheKey = "table_performance_stats";

        // 尝试从缓存获取
        Map<String, Object> cachedStats = cacheService.getCachedOnlineStats(cacheKey);
        if (cachedStats != null) {
            cacheHitCount.incrementAndGet();
            return cachedStats;
        }

        try {
            Map<String, Object> stats = onlineUsersMapper.getTablePerformanceStats();

            // 缓存结果（较长缓存时间）
            cacheService.cacheOnlineStats(cacheKey, stats, config.getCacheDuration() * 2);

            queryCount.incrementAndGet();
            return stats;

        } catch (Exception e) {
            log.error("获取表性能统计失败: {}", e.getMessage(), e);
            return new HashMap<>();
        }
    }

    @Override
    public Map<String, Object> getStatsPerformanceMetrics() {
        Map<String, Object> metrics = new HashMap<>();

        long totalQueries = queryCount.get();
        long totalCacheHits = cacheHitCount.get();
        long avgQueryTime = totalQueries > 0 ? totalQueryTime.get() / totalQueries : 0;
        double cacheHitRate = totalQueries > 0 ? (double) totalCacheHits / totalQueries * 100 : 0;

        metrics.put("total_queries", totalQueries);
        metrics.put("cache_hits", totalCacheHits);
        metrics.put("cache_hit_rate", Math.round(cacheHitRate * 100.0) / 100.0);
        metrics.put("avg_query_time_ms", avgQueryTime);
        metrics.put("total_query_time_ms", totalQueryTime.get());
        metrics.put("stats_enabled", config.getEnabled());
        metrics.put("performance_monitoring_enabled", config.getEnablePerformanceMonitoring());

        return metrics;
    }

    @Override
    public Map<String, Object> getCacheStats() {
        try {
            return cacheService.getCacheStats();
        } catch (Exception e) {
            log.error("获取缓存统计失败: {}", e.getMessage(), e);
            return new HashMap<>();
        }
    }

    // ==================== 数据清理方法实现 ====================

    @Override
    public int cleanExpiredRecords(int days) {
        try {
            int result = onlineUsersMapper.cleanExpiredRecords(days);
            log.info("清理过期数据完成，清理天数: {}, 清理记录数: {}", days, result);
            return result;

        } catch (Exception e) {
            log.error("清理过期数据失败: {}", e.getMessage(), e);
            return 0;
        }
    }

    @Override
    public int physicalDeleteExpiredRecords(int days) {
        try {
            int result = onlineUsersMapper.physicalDeleteExpiredRecords(days);
            log.warn("物理删除过期数据完成，删除天数: {}, 删除记录数: {}", days, result);
            return result;

        } catch (Exception e) {
            log.error("物理删除过期数据失败: {}", e.getMessage(), e);
            return 0;
        }
    }

    // ==================== 实时统计方法实现 ====================

    @Override
    public Map<String, Object> getRealTimeStatsOverview() {
        Map<String, Object> overview = new HashMap<>();

        try {
            // 获取实时在线用户数
            overview.put("current_online", getCurrentOnlineUsersCount());

            // 获取今日活跃用户数
            overview.put("today_active", getTodayActiveUsersCount());

            // 获取5分钟内活跃用户数
            overview.put("active_5min", getActiveUsersCount(5));

            // 获取15分钟内活跃用户数
            overview.put("active_15min", getActiveUsersCount(15));

            // 获取1小时内活跃用户数
            overview.put("active_1hour", getActiveUsersCount(60));

            // 添加统计时间
            overview.put("stats_time", new Date());
            overview.put("stats_timestamp", System.currentTimeMillis());

            return overview;

        } catch (Exception e) {
            log.error("获取实时统计概览失败: {}", e.getMessage(), e);
            return getDefaultStats();
        }
    }

    @Override
    public Date getStatsLastRefreshTime() {
        // 返回当前时间作为最后刷新时间
        return new Date();
    }

    @Override
    public boolean refreshStatsCache() {
        try {
            // 清理过期缓存
            cacheService.cleanExpiredCache();

            // 预热关键统计数据
            getRealTimeStatsOverview();
            getMultiDimensionStats();
            getUserActivityDistribution();

            log.info("统计缓存刷新完成");
            return true;

        } catch (Exception e) {
            log.error("刷新统计缓存失败: {}", e.getMessage(), e);
            return false;
        }
    }

    // ==================== 报告生成方法实现 ====================

    public Map<String, Object> getDailyReport(Date date) {
        Map<String, Object> report = new HashMap<>();

        try {
            // 获取指定日期的统计数据
            report.put("date", date);
            report.put("active_users", getTodayActiveUsersCount());
            report.put("peak_online", getCurrentOnlineUsersCount());
            report.put("activity_distribution", getUserActivityDistribution());

            return report;

        } catch (Exception e) {
            log.error("生成日报失败: {}", e.getMessage(), e);
            return new HashMap<>();
        }
    }

    @Override
    public Map<String, Object> getWeeklyReport() {
        Map<String, Object> report = new HashMap<>();

        try {
            // 获取周报数据
            report.put("week_start", new Date());
            report.put("retention_stats", getUserRetentionStats());
            report.put("activity_trend", getUserActivityTrend24Hours());

            return report;

        } catch (Exception e) {
            log.error("生成周报失败: {}", e.getMessage(), e);
            return new HashMap<>();
        }
    }

    @Override
    public Map<String, Object> getMonthlyReport() {
        Map<String, Object> report = new HashMap<>();

        try {
            // 获取月报数据
            report.put("month", new Date());
            report.put("monthly_stats", getMultiDimensionStats());
            report.put("user_sessions", getUserSessionStats(30, 100));

            return report;

        } catch (Exception e) {
            log.error("生成月报失败: {}", e.getMessage(), e);
            return new HashMap<>();
        }
    }

    @Override
    public Map<String, Object> exportActivityReport(Date startDate, Date endDate) {
        Map<String, Object> report = new HashMap<>();

        try {
            // 生成活动报告数据
            report.put("start_date", startDate);
            report.put("end_date", endDate);
            report.put("active_users", getTodayActiveUsersCount());
            report.put("online_users", getCurrentOnlineUsersCount());
            report.put("activity_distribution", getUserActivityDistribution());
            report.put("retention_stats", getUserRetentionStats());
            report.put("export_time", new Date());

            return report;

        } catch (Exception e) {
            log.error("导出活动报告失败: {}", e.getMessage(), e);
            return new HashMap<>();
        }
    }
}
