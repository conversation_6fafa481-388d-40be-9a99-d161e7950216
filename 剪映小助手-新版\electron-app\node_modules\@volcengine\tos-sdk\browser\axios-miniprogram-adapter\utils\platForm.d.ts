/// <reference types="miniprogram-api-typings" />
import { AxiosResponse, AxiosRequestConfig } from 'axios';
interface MpResponse extends WechatMiniprogram.RequestSuccessCallbackResult {
    /** 支付宝、钉钉独有 */
    headers: WechatMiniprogram.IAnyObject;
    status: number;
}
interface MpRequestConfig extends WechatMiniprogram.RequestOption {
    /** 仅支付宝、钉钉小程序独有 */
    headers?: WechatMiniprogram.IAnyObject;
}
/**
 * 获取各个平台的请求函数
 */
export declare function getRequest(): (option: WechatMiniprogram.RequestOption) => WechatMiniprogram.RequestTask;
/**
 * 处理各平台返回的响应数据，抹平差异
 * @param mpResponse
 * @param config axios处理过的请求配置对象
 * @param request 小程序的调用发起请求时，传递给小程序api的实际配置
 */
export declare function transformResponse(mpResponse: MpResponse, config: AxiosRequestConfig, mpRequestOption: WechatMiniprogram.RequestOption): AxiosResponse;
/**
 * 处理各平台返回的错误信息，抹平差异
 * @param error 小程序api返回的错误对象
 * @param reject 上层的promise reject 函数
 * @param config
 */
export declare function transformError(error: any, reject: any, config: any): void;
/**
 * 将axios的请求配置，转换成各个平台都支持的请求config
 * @param config
 */
export declare function transformConfig(config: MpRequestConfig): any;
export {};
