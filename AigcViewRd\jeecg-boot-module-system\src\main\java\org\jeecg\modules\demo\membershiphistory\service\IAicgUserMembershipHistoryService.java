package org.jeecg.modules.demo.membershiphistory.service;

import java.util.List;
import org.jeecg.modules.demo.membershiphistory.entity.AicgUserMembershipHistory;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * @Description: 用户会员订阅历史表
 * @Author: jeecg-boot
 * @Date:   2025-06-18
 * @Version: V1.0
 */
public interface IAicgUserMembershipHistoryService extends IService<AicgUserMembershipHistory> {

    /**
     * 根据用户ID查询会员订阅历史
     * @param userId 用户ID
     * @return 会员订阅历史列表
     */
    List<AicgUserMembershipHistory> getByUserId(String userId);
    
    /**
     * 根据用户ID查询当前生效的会员订阅
     * @param userId 用户ID
     * @return 当前生效的会员订阅
     */
    AicgUserMembershipHistory getCurrentMembership(String userId);
    
    /**
     * 根据订单ID查询会员订阅记录
     * @param orderId 订单ID
     * @return 会员订阅记录
     */
    AicgUserMembershipHistory getByOrderId(String orderId);
    
    /**
     * 创建会员订阅记录
     * @param userId 用户ID
     * @param orderId 订单ID
     * @param fromRoleId 升级前角色ID
     * @param toRoleId 升级后角色ID
     * @param memberLevel 会员等级
     * @param durationMonths 订阅时长(月)
     * @param amount 订阅金额
     * @param operatorId 操作人ID
     * @return 创建的会员订阅记录
     */
    AicgUserMembershipHistory createMembershipHistory(String userId, String orderId, String fromRoleId, 
                                                     String toRoleId, Integer memberLevel, Integer durationMonths, 
                                                     java.math.BigDecimal amount, String operatorId);
    
    /**
     * 取消会员订阅
     * @param id 会员订阅记录ID
     * @param operatorId 操作人ID
     * @return 是否成功
     */
    boolean cancelMembership(String id, String operatorId);
    
    /**
     * 查询即将过期的会员订阅（7天内过期）
     * @return 即将过期的会员订阅列表
     */
    List<AicgUserMembershipHistory> getExpiringMemberships();
    
    /**
     * 查询已过期但状态未更新的会员订阅
     * @return 已过期的会员订阅列表
     */
    List<AicgUserMembershipHistory> getExpiredMemberships();
    
    /**
     * 更新过期会员订阅状态
     * @return 更新的记录数
     */
    int updateExpiredMemberships();
}
