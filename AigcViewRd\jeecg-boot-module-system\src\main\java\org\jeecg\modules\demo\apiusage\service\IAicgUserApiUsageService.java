package org.jeecg.modules.demo.apiusage.service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import org.jeecg.modules.demo.apiusage.entity.AicgUserApiUsage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.core.metadata.IPage;

/**
 * @Description: 用户API使用记录表
 * @Author: jeecg-boot
 * @Date:   2025-06-18
 * @Version: V1.0
 */
public interface IAicgUserApiUsageService extends IService<AicgUserApiUsage> {

    /**
     * 根据用户ID查询API使用记录
     * @param userId 用户ID
     * @return API使用记录列表
     */
    List<AicgUserApiUsage> getByUserId(String userId);
    
    /**
     * 根据API密钥查询使用记录
     * @param apiKey API密钥
     * @return API使用记录列表
     */
    List<AicgUserApiUsage> getByApiKey(String apiKey);
    
    /**
     * 记录API使用
     * @param userId 用户ID
     * @param apiKey API密钥
     * @param apiEndpoint API接口地址
     * @param apiMethod 请求方法
     * @param requestParams 请求参数
     * @param responseStatus 响应状态码
     * @param responseTime 响应时间
     * @param tokensUsed 消耗Token数量
     * @param costAmount 消耗金额
     * @param ipAddress 请求IP地址
     * @param userAgent 用户代理
     * @param errorMessage 错误信息
     * @return 创建的使用记录
     */
    AicgUserApiUsage recordUsage(String userId, String apiKey, String apiEndpoint, String apiMethod,
                                String requestParams, Integer responseStatus, Integer responseTime,
                                Integer tokensUsed, BigDecimal costAmount, String ipAddress,
                                String userAgent, String errorMessage);
    
    /**
     * 统计用户API使用数据
     * @param userId 用户ID
     * @param timeRange 时间范围：today, week, month, year
     * @return API使用统计数据
     */
    Map<String, Object> getUsageStats(String userId, String timeRange);
    
    /**
     * 统计API接口调用次数
     * @param userId 用户ID
     * @param timeRange 时间范围
     * @return API接口调用统计
     */
    List<Map<String, Object>> getEndpointStats(String userId, String timeRange);
    
    /**
     * 查询今日API调用记录
     * @param userId 用户ID
     * @return 今日API调用记录
     */
    List<AicgUserApiUsage> getTodayUsage(String userId);
    
    /**
     * 查询错误的API调用记录
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 错误的API调用记录
     */
    List<AicgUserApiUsage> getErrorUsage(String userId, Integer limit);
    
    /**
     * 获取API使用趋势数据
     * @param userId 用户ID
     * @param timeRange 时间范围
     * @return 趋势数据
     */
    List<Map<String, Object>> getUsageTrend(String userId, String timeRange);
    
    /**
     * 检查API调用频率限制
     * @param userId 用户ID
     * @param apiKey API密钥
     * @return 是否超出限制
     */
    boolean checkRateLimit(String userId, String apiKey);

    /**
     * 获取插件使用统计分析
     * @return 插件使用统计数据
     */
    Map<String, Object> getPluginUsageStats();

    /**
     * 查询插件使用记录（包含用户信息）
     * @param pageNo 页码
     * @param pageSize 页大小
     * @param aicgUserApiUsage 查询条件
     * @param hasPluginInfo 是否只查询有插件信息的记录
     * @param callTimeStart 开始时间
     * @param callTimeEnd 结束时间
     * @param userNickname 用户昵称
     * @param parameterMap 其他查询参数
     * @return 分页结果
     */
    IPage<Map<String, Object>> queryPluginUsageWithUserInfo(Integer pageNo, Integer pageSize,
                                                           AicgUserApiUsage aicgUserApiUsage,
                                                           Boolean hasPluginInfo,
                                                           String callTimeStart,
                                                           String callTimeEnd,
                                                           String userNickname,
                                                           Map<String, String[]> parameterMap);

    /**
     * 查询API使用记录（包含用户信息）- 通用版本
     * @param pageNo 页码
     * @param pageSize 页大小
     * @param aicgUserApiUsage 查询条件
     * @param userNickname 用户昵称
     * @param parameterMap 其他查询参数
     * @return 分页结果
     */
    IPage<Map<String, Object>> queryApiUsageWithUserInfo(Integer pageNo, Integer pageSize,
                                                        AicgUserApiUsage aicgUserApiUsage,
                                                        String userNickname,
                                                        Map<String, String[]> parameterMap);
}
