package org.jeecg.modules.jianying.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 对象转字符串列表请求参数
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ObjsToStrListRequest extends BaseJianyingRequest {
    
    @ApiModelProperty(value = "数据对象，接收get_outputs节点的输出（必填）", required = true,
                     example = "[{\"key\": \"value1\"}, {\"key\": \"value2\"}]")
    @NotEmpty(message = "outputs不能为空")
    @JsonProperty("outputs")
    private List<Object> zjOutputs;
    
    @Override
    public String getSummary() {
        return "ObjsToStrListRequest{" +
               "accessKey=" + (getAccessKey() != null && getAccessKey().length() > 10 ? 
                              getAccessKey().substring(0, 10) + "***" : "null") +
               ", outputsCount=" + (zjOutputs != null ? zjOutputs.size() : 0) +
               "}";
    }
}
