# 剪映小助手Pro版 - API接口规范

## 📋 接口概览

### 基本信息
- **项目名称**：剪映小助手Pro版
- **接口数量**：8个合并优化接口
- **基础路径**：`/jeecg-boot/api/jianyingpro`
- **请求方法**：POST
- **Content-Type**：application/json
- **认证方式**：access_key

### 功能说明
将原来16个独立接口合并优化为8个智能一体化接口，大幅简化用户操作流程，提升开发效率。每个接口都将原来的两步操作（信息生成+添加操作）合并为一步。

## 🔧 8个核心接口

### 1. 一体化音频添加
- **接口路径**：`/add_audios`
- **合并逻辑**：`audio_infos` + `add_audios` → 单一操作

### 2. 一体化视频添加
- **接口路径**：`/add_videos`
- **合并逻辑**：`video_infos` + `add_videos` → 单一操作

### 3. 一体化图片添加
- **接口路径**：`/add_images`
- **合并逻辑**：`imgs_infos` + `add_images` → 单一操作

### 4. 一体化字幕添加
- **接口路径**：`/add_captions`
- **合并逻辑**：`caption_infos` + `add_captions` → 单一操作

### 5. 一体化特效添加
- **接口路径**：`/add_effects`
- **合并逻辑**：`effect_infos` + `add_effects` → 单一操作

### 6. 一体化关键帧添加
- **接口路径**：`/add_keyframes`
- **合并逻辑**：`keyframes_infos` + `add_keyframes` → 单一操作

### 7. 智能时间线生成
- **接口路径**：`/generate_timelines`
- **合并逻辑**：`timelines` + `audio_timelines` → 智能模式识别

### 8. 智能数据转换
- **接口路径**：`/data_conversion`
- **合并逻辑**：`str_to_list` + `objs_to_str_list` + `str_list_to_objs` → 统一接口

## 📝 智能数据转换接口详细规范

### 请求头
```http
Content-Type: application/json
```

### 请求体结构（以data_conversion为例）
```json
{
  "access_key": "string",           // 必填
  "input_string": "string",         // 可选
  "input_string_list": ["string"],  // 可选
  "input_object_list": [{}],        // 可选
  "delimiter": "string",            // 可选
  "extract_field": "string"         // 可选
}
```

### 参数详细说明

#### access_key
- **类型**：string
- **必填**：是
- **说明**：访问密钥，用于API认证
- **默认值**：`JianyingAPI_2025_SecureAccess_AigcView`
- **示例**：`"JianyingAPI_2025_SecureAccess_AigcView"`

#### input_string
- **类型**：string
- **必填**：否
- **说明**：字符串数据，会按分隔符转换为列表
- **示例**：`"苹果,香蕉,橙子"`
- **转换结果**：`["苹果", "香蕉", "橙子"]`

#### input_string_list
- **类型**：array[string]
- **必填**：否
- **说明**：字符串列表，会转换为对象列表
- **示例**：`["苹果", "香蕉", "橙子"]`
- **转换结果**：`[{"output": "苹果"}, {"output": "香蕉"}, {"output": "橙子"}]`

#### input_object_list
- **类型**：array[object]
- **必填**：否
- **说明**：对象列表，会智能提取字段转换为字符串列表
- **示例**：`[{"output": "苹果", "color": "红色"}, {"name": "香蕉"}]`
- **转换结果**：根据提取模式而定

#### delimiter
- **类型**：string
- **必填**：否
- **默认值**：`,`
- **说明**：字符串分隔符，用于分割input_string
- **支持的分隔符**：`,` `;` `|` `\n` ` `
- **示例**：`";"`

#### extract_field
- **类型**：string
- **必填**：否
- **默认值**：`output`
- **说明**：从input_object_list中提取的字段名
- **示例**：`"name"`

### 参数组合规则
- 至少需要提供一种输入数据（input_string、input_string_list、input_object_list）
- 可以同时提供多种输入数据，系统会执行所有可能的转换
- delimiter只对input_string有效
- extract_field只对input_object_list有效

## 📤 响应规范

### 成功响应
```json
{
  "success": true,
  "message": "智能数据转换完成，共执行 N 种转换",
  "conversion_count": 3,
  "data": {
    "string_to_list": {
      "list": ["string"],
      "count": 0,
      "delimiter_used": "string"
    },
    "string_list_to_objects": {
      "objects": [{"output": "string"}],
      "count": 0
    },
    "objects_to_string_list": {
      "list": ["string"],
      "count": 0,
      "field_extracted": "string",
      "extraction_mode": "string"
    }
  }
}
```

### 响应字段说明

#### 顶层字段
- **success**：boolean，操作是否成功
- **message**：string，操作结果消息
- **conversion_count**：integer，成功执行的转换数量
- **data**：object，转换结果数据

#### string_to_list（字符串转列表结果）
- **list**：array[string]，分割后的字符串数组
- **count**：integer，分割得到的项目数量
- **delimiter_used**：string，实际使用的分隔符

#### string_list_to_objects（字符串列表转对象结果）
- **objects**：array[object]，转换后的对象数组
- **count**：integer，转换得到的对象数量

#### objects_to_string_list（对象列表转字符串列表结果）
- **list**：array[string]，提取的字符串数组
- **count**：integer，提取得到的字符串数量
- **field_extracted**：string，实际提取的字段名或描述
- **extraction_mode**：string，提取模式（"智能提取" 或 "指定字段"）

### 错误响应
```json
{
  "success": false,
  "error": "错误信息",
  "error_code": "ERROR_CODE",
  "error_message": "详细错误消息",
  "error_details": "错误解决方案"
}
```

### HTTP状态码
- **200**：请求成功
- **400**：参数错误
- **422**：业务逻辑错误
- **500**：系统内部错误

## 🧪 测试用例

### 用例1：单一字符串转换
```http
POST /jeecg-boot/api/jianyingpro/data_conversion
Content-Type: application/json

{
  "access_key": "JianyingAPI_2025_SecureAccess_AigcView",
  "input_string": "苹果,香蕉,橙子"
}
```

**预期响应**：
```json
{
  "success": true,
  "message": "智能数据转换完成，共执行 1 种转换",
  "conversion_count": 1,
  "data": {
    "string_to_list": {
      "list": ["苹果", "香蕉", "橙子"],
      "count": 3,
      "delimiter_used": ","
    }
  }
}
```

### 用例2：智能对象提取
```http
POST /jeecg-boot/api/jianyingpro/data_conversion
Content-Type: application/json

{
  "access_key": "JianyingAPI_2025_SecureAccess_AigcView",
  "input_object_list": [
    {"name": "苹果"},
    {"title": "香蕉", "color": "黄色"}
  ]
}
```

**预期响应**：
```json
{
  "success": true,
  "message": "智能数据转换完成，共执行 1 种转换",
  "conversion_count": 1,
  "data": {
    "objects_to_string_list": {
      "list": ["苹果", "香蕉", "黄色"],
      "count": 3,
      "field_extracted": "智能提取（单字段对象提取唯一字段，多字段对象提取所有字段）",
      "extraction_mode": "智能提取"
    }
  }
}
```

### 用例3：多重转换
```http
POST /jeecg-boot/api/jianyingpro/data_conversion
Content-Type: application/json

{
  "access_key": "JianyingAPI_2025_SecureAccess_AigcView",
  "input_string": "苹果,香蕉",
  "input_string_list": ["橙子", "葡萄"],
  "input_object_list": [{"output": "西瓜"}]
}
```

**预期响应**：包含所有三种转换结果的完整响应。

## ⚠️ 注意事项

### 数据格式要求
- 字符串不能为空或只包含空白字符
- 数组不能为空
- 对象必须包含至少一个字段

### 性能限制
- 单次请求建议不超过1000个数据项
- 字符串长度建议不超过10000字符
- 对象嵌套深度不超过5层

### 安全要求
- access_key必须正确
- 不支持跨域请求（需要配置CORS）
- 建议使用HTTPS协议

---

*API接口规范 v1.0.0 - 2025-07-22*
