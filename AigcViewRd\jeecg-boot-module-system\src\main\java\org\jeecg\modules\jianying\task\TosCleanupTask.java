package org.jeecg.modules.jianying.task;

import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.jianying.config.TosConfig;
import org.jeecg.modules.jianying.service.TosService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * TOS文件定时清理任务
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
@Slf4j
@Component
@ConditionalOnProperty(name = "volcengine.tos.auto-cleanup", havingValue = "true")
public class TosCleanupTask {
    
    @Autowired
    private TosService tosService;
    
    @Autowired
    private TosConfig tosConfig;
    
    /**
     * 定时清理过期文件
     * 每天凌晨2点执行
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void cleanupExpiredFiles() {
        try {
            log.info("开始执行TOS文件清理任务，保留天数：{}", tosConfig.getFileRetentionDays());
            
            tosService.cleanupExpiredFiles();
            
            log.info("TOS文件清理任务执行完成");
            
        } catch (Exception e) {
            log.error("TOS文件清理任务执行失败", e);
        }
    }
}
