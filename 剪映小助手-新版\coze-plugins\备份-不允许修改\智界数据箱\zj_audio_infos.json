{"openapi": "3.0.0", "info": {"title": "剪映小助手_智界数据箱 - 音频数据生成器", "description": "根据时间线制作音频数据", "version": "1.0.0"}, "servers": [{"url": "https://www.aigcview.com"}], "paths": {"/jeecg-boot/api/jianying/audio_infos": {"post": {"summary": "生成音频数据", "description": "根据时间线制作音频数据", "operationId": "audioInfos_zj", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"access_key": {"type": "string", "description": "访问密钥（必填）", "example": "JianyingAPI_2025_SecureAccess_AigcView", "default": "JianyingAPI_2025_SecureAccess_AigcView"}, "zj_audio_effect": {"type": "string", "description": "特效音，eg：教堂，默认无", "example": "教堂"}, "zj_mp3_urls": {"type": "array", "description": "音频列表（必填）", "items": {"type": "string"}, "example": ["https://example.com/audio1.mp3", "https://example.com/audio2.mp3"]}, "zj_timelines": {"type": "array", "description": "时间线（必填）", "items": {"type": "object", "properties": {"start": {"type": "integer"}, "end": {"type": "integer"}}}, "example": [{"start": 0, "end": 4612000}]}, "zj_volume": {"type": "number", "description": "音量大小，0-10，默认1", "minimum": 0, "maximum": 10, "example": 1.0}}, "required": ["access_key", "zj_mp3_urls", "zj_timelines"], "additionalProperties": false}}}}, "responses": {"200": {"description": "成功生成音频数据", "content": {"application/json": {"schema": {"type": "object", "properties": {"infos": {"type": "string", "description": "音频信息数组（JSON格式字符串），格式：[{\"audio_url\":\"...\",\"start\":0,\"end\":1920000}]"}}, "required": ["infos"]}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "description": "操作是否成功", "example": false}, "error": {"type": "string", "description": "错误信息"}, "code": {"type": "string", "description": "错误代码"}}, "required": ["success", "error"]}}}}, "500": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "description": "操作是否成功", "example": false}, "error": {"type": "string", "description": "错误信息"}, "code": {"type": "string", "description": "错误代码"}}, "required": ["success", "error"]}}}}}}}}}