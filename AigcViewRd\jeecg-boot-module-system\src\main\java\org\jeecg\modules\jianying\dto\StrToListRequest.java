package org.jeecg.modules.jianying.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

/**
 * 字符串转列表请求参数
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class StrToListRequest extends BaseJianyingRequest {
    
    @ApiModelProperty(value = "对象内容（必填）", required = true,
                     example = "item1,item2,item3")
    @NotBlank(message = "obj不能为空")
    @JsonProperty("obj")
    private String zjObj;
    
    @Override
    public String getSummary() {
        return "StrToListRequest{" +
               "accessKey=" + (getAccessKey() != null && getAccessKey().length() > 10 ? 
                              getAccessKey().substring(0, 10) + "***" : "null") +
               ", obj=" + (zjObj != null && zjObj.length() > 30 ? 
                          zjObj.substring(0, 30) + "***" : zjObj) +
               "}";
    }
}
