<template>
  <a-modal
    :title="title"
    :width="800"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel"
    cancelText="关闭">
    
    <a-spin :spinning="confirmLoading">
      <a-form-model ref="form" :model="model" :rules="validatorRules" v-bind="layout">
        <a-row>
          <a-col :span="12">
            <a-form-model-item label="推荐关系ID" prop="referralId">
              <a-input v-model="model.referralId" placeholder="请输入推荐关系ID"></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="推荐人ID" prop="referrerId">
              <a-input v-model="model.referrerId" placeholder="请输入推荐人ID"></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="被推荐人ID" prop="refereeId">
              <a-input v-model="model.refereeId" placeholder="请输入被推荐人ID"></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="奖励类型" prop="rewardType">
              <a-select v-model="model.rewardType" placeholder="请选择奖励类型">
                <a-select-option :value="1">注册奖励</a-select-option>
                <a-select-option :value="2">首充奖励</a-select-option>
                <a-select-option :value="3">升级奖励</a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="奖励金额" prop="rewardAmount">
              <a-input-number v-model="model.rewardAmount" :min="0" :precision="2" placeholder="请输入奖励金额" style="width: 100%"></a-input-number>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="状态" prop="status">
              <a-select v-model="model.status" placeholder="请选择状态">
                <a-select-option :value="1">待发放</a-select-option>
                <a-select-option :value="2">已发放</a-select-option>
                <a-select-option :value="3">已取消</a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="触发事件描述" prop="triggerEvent">
              <a-input v-model="model.triggerEvent" placeholder="请输入触发事件描述"></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="交易记录ID" prop="transactionId">
              <a-input v-model="model.transactionId" placeholder="请输入交易记录ID"></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="发放时间" prop="rewardTime">
              <a-date-picker 
                v-model="model.rewardTime" 
                show-time 
                format="YYYY-MM-DD HH:mm:ss"
                placeholder="请选择发放时间"
                style="width: 100%">
              </a-date-picker>
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </a-spin>
  </a-modal>
</template>

<script>
  import { httpAction } from '@/api/manage'
  import moment from 'moment'

  export default {
    name: 'AicgUserReferralRewardModal',
    components: {
    },
    data () {
      return {
        layout: {
          labelCol: {
            xs: { span: 24 },
            sm: { span: 5 },
          },
          wrapperCol: {
            xs: { span: 24 },
            sm: { span: 16 },
          },
        },
        title:"操作",
        visible: false,
        model: {},
        confirmLoading: false,
        form: this.$form.createForm(this),
        validatorRules: {
          referralId: [
            { required: true, message: '请输入推荐关系ID!' },
          ],
          referrerId: [
            { required: true, message: '请输入推荐人ID!' },
          ],
          refereeId: [
            { required: true, message: '请输入被推荐人ID!' },
          ],
          rewardType: [
            { required: true, message: '请选择奖励类型!' },
          ],
          rewardAmount: [
            { required: true, message: '请输入奖励金额!' },
          ],
        },
        url: {
          add: "/demo/referralreward/add",
          edit: "/demo/referralreward/edit",
          queryById: "/demo/referralreward/queryById"
        }
      }
    },
    methods: {
      add () {
        this.edit({});
      },
      edit (record) {
        this.form.resetFields();
        this.model = Object.assign({}, record);
        this.visible = true;
        this.$nextTick(() => {
          const formData = {
            referralId: this.model.referralId,
            referrerId: this.model.referrerId,
            refereeId: this.model.refereeId,
            rewardType: this.model.rewardType,
            rewardAmount: this.model.rewardAmount,
            status: this.model.status,
            triggerEvent: this.model.triggerEvent,
            transactionId: this.model.transactionId
          }
          this.form.setFieldsValue(formData)
          if(this.model.rewardTime) {
            this.model.rewardTime = moment(this.model.rewardTime)
          }
        })
        if(record.id){
          this.title = "编辑";
        }else{
          this.title = "新增";
          this.model.status = 1
        }
      },
      close () {
        this.$emit('close');
        this.visible = false;
      },
      handleOk () {
        const that = this;
        // 触发表单验证
        this.$refs.form.validate(valid => {
          if(valid) {
            that.confirmLoading = true;
            let httpurl = '';
            let method = '';
            if(!this.model.id){
              httpurl+=this.url.add;
              method = 'post';
            }else{
              httpurl+=this.url.edit;
              method = 'put';
            }
            let formData = Object.assign(this.model);
            if(formData.rewardTime) {
              formData.rewardTime = formData.rewardTime.format('YYYY-MM-DD HH:mm:ss')
            }
            httpAction(httpurl,formData,method).then((res)=>{
              if(res.success){
                that.$message.success(res.message);
                that.$emit('ok');
              }else{
                that.$message.warning(res.message);
              }
            }).finally(() => {
              that.confirmLoading = false;
            })
          }
        })
      },
      handleCancel () {
        this.close()
      }
    }
  }
</script>
