package org.jeecg.modules.jianyingpro.dto.request;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 一体化字幕添加请求
 * 合并 caption_infos + add_captions 的参数
 *
 * <AUTHOR>
 * @since 2025-01-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class JianyingProAddCaptionsRequest extends BaseJianyingProRequest {

    // ========== 核心参数 ==========
    @ApiModelProperty(value = "草稿地址，使用create_draft输出的draft_url即可（必填）", required = true,
                     example = "https://example.com/draft/123")
    @NotBlank(message = "draft_url不能为空")
    @JsonProperty("draft_url")
    private String draftUrl;

    @ApiModelProperty(value = "文本列表（必填）", required = true,
                     example = "[\"这是第一段字幕\", \"这是第二段字幕\"]")
    @NotEmpty(message = "texts不能为空")
    @JsonProperty("texts")
    private List<String> texts;

    @ApiModelProperty(value = "时间节点，只接收结构：[{\"start\":0,\"end\":4612}]，一般从audio_timeline节点的输出获取（必填）", required = true,
                     example = "[{\"start\": 0, \"end\": 4612000}]")
    @NotEmpty(message = "timelines不能为空")
    @JsonProperty("timelines")
    private List<JSONObject> timelines;

    @ApiModelProperty(value = "字幕透明度（可选，不设置时使用剪映原生默认值1.0）", example = "0.8")
    @JsonProperty("alpha")
    private Double alpha;

    @ApiModelProperty(value = "字体大小（可选，默认15）", example = "15")
    @JsonProperty("font_size")
    private Integer fontSize;

    @ApiModelProperty(value = "X轴缩放比例（可选，不设置时使用剪映原生默认值1.0）", example = "1.2")
    @JsonProperty("scale_x")
    private Double scaleX;

    @ApiModelProperty(value = "Y轴缩放比例（可选，不设置时使用剪映原生默认值1.0）", example = "1.2")
    @JsonProperty("scale_y")
    private Double scaleY;

    @ApiModelProperty(value = "X轴移动位置（可选，不设置时使用剪映原生默认值0.0）", example = "100.0")
    @JsonProperty("transform_x")
    private Double transformX;

    @ApiModelProperty(value = "Y轴移动位置（可选，不设置时使用剪映原生默认值0.0）", example = "-50.0")
    @JsonProperty("transform_y")
    private Double transformY;

    @ApiModelProperty(value = "文字颜色（可选）", example = "#ff1837")
    @JsonProperty("text_color")
    private String textColor;

    @ApiModelProperty(value = "边框颜色（可选，与稳定版add_captions完全一致）", example = "#fe8a80")
    @JsonProperty("border_color")
    private String borderColor;

    @ApiModelProperty(value = "字体名称（可选）", example = "默认字体")
    @JsonProperty("font")
    private String font;

    @ApiModelProperty(value = "行间距（可选，默认0）", example = "0")
    @JsonProperty("line_spacing")
    private Double lineSpacing;

    @ApiModelProperty(value = "字符间距（可选，与稳定版add_captions完全一致）", example = "0.0")
    @JsonProperty("letter_spacing")
    private Double letterSpacing;

    @ApiModelProperty(value = "字幕对齐方式（可选，默认1居中）", example = "1")
    @JsonProperty("alignment")
    private Integer alignment;

    @ApiModelProperty(value = "文本样式（可选，0默认，1富文本）", example = "0")
    @JsonProperty("style_text")
    private Integer styleText;

    // ========== 来自caption_infos的可选参数 ==========
    @ApiModelProperty(value = "关键词颜色（可选）", example = "red")
    @JsonProperty("keyword_color")
    private String keywordColor;

    @ApiModelProperty(value = "文本里面的重点词列表（可选）", example = "[\"重点\", \"关键词\"]")
    @JsonProperty("keywords")
    private List<String> keywords;

    @ApiModelProperty(value = "关键词字体大小（可选）", example = "18")
    @JsonProperty("keyword_font_size")
    private Integer keywordFontSize;

    @ApiModelProperty(value = "入场动画（可选）", example = "飞入")
    @JsonProperty("in_animation")
    private String inAnimation;

    @ApiModelProperty(value = "出场动画（可选）", example = "消散")
    @JsonProperty("out_animation")
    private String outAnimation;

    @ApiModelProperty(value = "循环动画（可选）", example = "晃动")
    @JsonProperty("loop_animation")
    private String loopAnimation;

    @ApiModelProperty(value = "入场动画时长（可选）", example = "1000000")
    @JsonProperty("in_animation_duration")
    private Long inAnimationDuration;

    @ApiModelProperty(value = "出场动画时长（可选）", example = "1000000")
    @JsonProperty("out_animation_duration")
    private Long outAnimationDuration;

    @ApiModelProperty(value = "循环动画时长（可选）", example = "4000000")
    @JsonProperty("loop_animation_duration")
    private Long loopAnimationDuration;

    @Override
    public String getSummary() {
        return "JianyingProAddCaptionsRequest{" +
               "draftUrl=" + (draftUrl != null && draftUrl.length() > 50 ?
                            draftUrl.substring(0, 50) + "***" : draftUrl) +
               ", textsCount=" + (texts != null ? texts.size() : 0) +
               ", timelinesCount=" + (timelines != null ? timelines.size() : 0) +
               ", fontSize=" + fontSize +
               ", textColor=" + textColor +
               ", borderColor=" + borderColor +
               ", font=" + font +
               ", lineSpacing=" + lineSpacing +
               ", letterSpacing=" + letterSpacing +
               ", keywordsCount=" + (keywords != null ? keywords.size() : 0) +
               ", inAnimation=" + inAnimation +
               ", inAnimationDuration=" + inAnimationDuration +
               "}";
    }

    @Override
    public void validate() {
        super.validate();

        // texts是必填的（复制自稳定版caption_infos要求）
        if (texts == null || texts.isEmpty()) {
            throw new IllegalArgumentException(
                "参数不完整：texts不能为空"
            );
        }

        // timelines是必填的（复制自稳定版caption_infos要求）
        if (timelines == null || timelines.isEmpty()) {
            throw new IllegalArgumentException(
                "参数不完整：timelines不能为空"
            );
        }

        // 验证texts中不能有空值
        for (String text : texts) {
            if (text == null || text.trim().isEmpty()) {
                throw new IllegalArgumentException(
                    "texts 中不能包含空的文本"
                );
            }
        }

        // 验证透明度范围
        if (alpha != null && (alpha < 0 || alpha > 1)) {
            throw new IllegalArgumentException(
                "alpha 参数值必须在 0-1 之间，当前值: " + alpha
            );
        }

        // 验证字体大小范围
        if (fontSize != null && (fontSize <= 0 || fontSize > 200)) {
            throw new IllegalArgumentException(
                "font_size 参数值必须在 1-200 之间，当前值: " + fontSize
            );
        }

        // 验证对齐方式
        if (alignment != null && (alignment < 0 || alignment > 5)) {
            throw new IllegalArgumentException(
                "alignment 参数值必须在 0-5 之间，当前值: " + alignment
            );
        }
    }
}
