import { axios } from '@/utils/request'

/**
 * 创作者中心 - 智能体管理 API
 */

// API 基础路径
const API_BASE = '/api/creator/agent'

/**
 * 获取创作者智能体列表
 * @param {Object} params - 查询参数
 * @param {number} params.pageNo - 页码
 * @param {number} params.pageSize - 页大小
 * @param {string} params.agentName - 智能体名称（可选）
 * @param {string} params.auditStatus - 审核状态（可选）
 * @returns {Promise}
 */
export function getCreatorAgents(params) {
  return axios({
    url: `${API_BASE}/list`,
    method: 'GET',
    params
  })
}

/**
 * 创建智能体
 * @param {Object} data - 智能体数据
 * @param {string} data.agentName - 智能体名称
 * @param {string} data.agentDescription - 智能体描述
 * @param {string} data.agentAvatar - 智能体头像
 * @param {string} data.experienceLink - 体验链接
 * @param {number} data.price - 价格
 * @returns {Promise}
 */
export function createAgent(data) {
  return axios({
    url: `${API_BASE}/add`,
    method: 'POST',
    data
  })
}

/**
 * 更新智能体
 * @param {string} id - 智能体ID
 * @param {Object} data - 智能体数据
 * @returns {Promise}
 */
export function updateAgent(id, data) {
  return axios({
    url: `${API_BASE}/edit/${id}`,
    method: 'PUT',
    data
  })
}

/**
 * 删除智能体
 * @param {string} id - 智能体ID
 * @returns {Promise}
 */
export function deleteAgent(id) {
  return axios({
    url: `${API_BASE}/delete/${id}`,
    method: 'DELETE'
  })
}

/**
 * 获取智能体详情
 * @param {string} id - 智能体ID
 * @returns {Promise}
 */
export function getAgentDetail(id) {
  return axios({
    url: `${API_BASE}/detail/${id}`,
    method: 'GET'
  })
}

/**
 * 获取收益统计
 * @returns {Promise}
 */
export function getRevenueStats() {
  return axios({
    url: `${API_BASE}/revenue/stats`,
    method: 'GET'
  })
}

/**
 * 创作者中心 - 工作流管理 API
 */

// 工作流 API 基础路径
const WORKFLOW_API_BASE = '/api/creator/workflow'

/**
 * 获取智能体的工作流列表
 * @param {string} agentId - 智能体ID
 * @returns {Promise}
 */
export function getWorkflowList(agentId) {
  return axios({
    url: `${WORKFLOW_API_BASE}/list/${agentId}`,
    method: 'GET'
  })
}

/**
 * 创建工作流
 * @param {Object} data - 工作流数据
 * @param {string} data.agentId - 智能体ID
 * @param {string} data.workflowName - 工作流名称
 * @param {string} data.workflowDescription - 工作流描述
 * @returns {Promise}
 */
export function createWorkflow(data) {
  return axios({
    url: `${WORKFLOW_API_BASE}/add`,
    method: 'POST',
    data
  })
}

/**
 * 更新工作流
 * @param {string} id - 工作流ID
 * @param {Object} data - 工作流数据
 * @returns {Promise}
 */
export function updateWorkflow(id, data) {
  return axios({
    url: `${WORKFLOW_API_BASE}/edit/${id}`,
    method: 'PUT',
    data
  })
}

/**
 * 删除工作流
 * @param {string} id - 工作流ID
 * @returns {Promise}
 */
export function deleteWorkflow(id) {
  return axios({
    url: `${WORKFLOW_API_BASE}/delete/${id}`,
    method: 'DELETE'
  })
}

/**
 * 获取工作流详情
 * @param {string} id - 工作流ID
 * @returns {Promise}
 */
export function getWorkflowDetail(id) {
  return axios({
    url: `${WORKFLOW_API_BASE}/detail/${id}`,
    method: 'GET'
  })
}

/**
 * 上传工作流文件
 * @param {File} file - 文件对象
 * @param {string} agentId - 智能体ID
 * @returns {Promise}
 */
export function uploadWorkflowFile(file, agentId) {
  const formData = new FormData()
  formData.append('file', file)
  formData.append('agentId', agentId)
  
  return axios({
    url: `${WORKFLOW_API_BASE}/upload`,
    method: 'POST',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    timeout: 300000 // 5分钟超时，因为文件可能比较大
  })
}

/**
 * 通用文件上传 API
 */

/**
 * 上传文件（通用）
 * @param {FormData} formData - 包含文件的FormData对象
 * @returns {Promise}
 */
export function uploadFile(formData) {
  return axios({
    url: '/sys/common/upload',
    method: 'POST',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    timeout: 60000 // 1分钟超时
  })
}

/**
 * 错误处理工具函数
 */

/**
 * 处理API错误
 * @param {Error} error - 错误对象
 * @param {string} defaultMessage - 默认错误消息
 * @returns {string} 错误消息
 */
export function handleApiError(error, defaultMessage = '操作失败') {
  if (error.response) {
    // 服务器返回错误状态码
    const { status, data } = error.response
    
    switch (status) {
      case 401:
        return '登录已过期，请重新登录'
      case 403:
        return '没有权限执行此操作'
      case 404:
        return '请求的资源不存在'
      case 422:
        return data.message || '请求参数错误'
      case 500:
        return '服务器内部错误'
      default:
        return data.message || defaultMessage
    }
  } else if (error.request) {
    // 网络错误
    return '网络连接失败，请检查网络设置'
  } else {
    // 其他错误
    return error.message || defaultMessage
  }
}

/**
 * 统一的请求拦截器配置
 */
export function setupRequestInterceptors() {
  // 请求拦截器
  axios.interceptors.request.use(
    config => {
      // 添加时间戳防止缓存
      if (config.method === 'get') {
        config.params = {
          ...config.params,
          _t: Date.now()
        }
      }
      
      // 添加请求头
      config.headers = {
        ...config.headers,
        'X-Requested-With': 'XMLHttpRequest'
      }
      
      return config
    },
    error => {
      return Promise.reject(error)
    }
  )
  
  // 响应拦截器
  axios.interceptors.response.use(
    response => {
      const { data } = response
      
      // 统一处理业务错误
      if (data && data.success === false) {
        console.error('API业务错误:', data.message)
        
        // 特殊错误码处理
        if (data.code === 401) {
          // 登录过期，跳转到登录页
          window.location.href = '/login'
          return Promise.reject(new Error('登录已过期'))
        }
      }
      
      return response
    },
    error => {
      console.error('API请求错误:', error)
      return Promise.reject(error)
    }
  )
}

// 导出所有API函数
export default {
  // 智能体管理
  getCreatorAgents,
  createAgent,
  updateAgent,
  deleteAgent,
  getAgentDetail,
  getRevenueStats,
  
  // 工作流管理
  getWorkflowList,
  createWorkflow,
  updateWorkflow,
  deleteWorkflow,
  getWorkflowDetail,
  uploadWorkflowFile,
  
  // 文件上传
  uploadFile,
  
  // 工具函数
  handleApiError,
  setupRequestInterceptors
}
