package org.jeecg.modules.jianying.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

/**
 * 提取URL请求参数
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class GetUrlRequest extends BaseJianyingRequest {
    
    @ApiModelProperty(value = "提取内容（必填）", required = true,
                     example = "https://example.com/file1.mp4,https://example.com/file2.mp4")
    @NotBlank(message = "output不能为空")
    @JsonProperty("output")
    private String zjOutput;
    
    @Override
    public String getSummary() {
        return "GetUrlRequest{" +
               "accessKey=" + (getAccessKey() != null && getAccessKey().length() > 10 ?
                              getAccessKey().substring(0, 10) + "***" : "null") +
               ", output=" + (zjOutput != null && zjOutput.length() > 50 ?
                             zjOutput.substring(0, 50) + "***" : zjOutput) +
               "}";
    }
}
