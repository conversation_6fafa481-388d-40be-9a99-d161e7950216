package org.jeecg.modules.system.entity;

import java.io.Serializable;
import java.util.Date;

import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 敏感词命中记录
 * @Author: jeecg-boot
 * @Date: 2025-06-22
 * @Version: V1.0
 */
@Data
@TableName("sys_sensitive_word_hit_log")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="sys_sensitive_word_hit_log对象", description="敏感词命中记录")
public class SysSensitiveWordHitLog implements Serializable {
    private static final long serialVersionUID = 1L;

    /**主键*/
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String id;
    
    /**敏感词ID*/
    @Excel(name = "敏感词ID", width = 15)
    @ApiModelProperty(value = "敏感词ID")
    private String wordId;
    
    /**敏感词内容*/
    @Excel(name = "敏感词", width = 15)
    @ApiModelProperty(value = "敏感词内容")
    private String word;
    
    /**命中的原文本*/
    @Excel(name = "命中文本", width = 30)
    @ApiModelProperty(value = "命中的原文本")
    private String hitText;
    
    /**触发用户ID*/
    @Excel(name = "用户ID", width = 15)
    @ApiModelProperty(value = "触发用户ID")
    private String userId;
    
    /**IP地址*/
    @Excel(name = "IP地址", width = 15)
    @ApiModelProperty(value = "IP地址")
    private String ipAddress;
    
    /**触发模块*/
    @Excel(name = "触发模块", width = 15)
    @ApiModelProperty(value = "触发模块(昵称校验/内容审核等)")
    private String module;
    
    /**命中时间*/
    @Excel(name = "命中时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "命中时间")
    private Date hitTime;
    
    /**创建时间*/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
}
