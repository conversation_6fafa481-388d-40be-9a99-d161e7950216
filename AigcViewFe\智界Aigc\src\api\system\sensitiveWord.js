import {axios} from '@/utils/request'

/**
 * 敏感词管理API
 */

// 基础CRUD接口
export function getSensitiveWordList(parameter) {
  return axios({
    url: '/sys/sensitiveWord/list',
    method: 'get',
    params: parameter
  })
}

export function deleteSensitiveWord(parameter) {
  return axios({
    url: '/sys/sensitiveWord/delete',
    method: 'delete',
    params: parameter
  })
}

export function batchDeleteSensitiveWord(parameter) {
  return axios({
    url: '/sys/sensitiveWord/deleteBatch',
    method: 'delete',
    params: parameter
  })
}

export function addSensitiveWord(parameter) {
  return axios({
    url: '/sys/sensitiveWord/add',
    method: 'post',
    data: parameter
  })
}

export function editSensitiveWord(parameter) {
  return axios({
    url: '/sys/sensitiveWord/edit',
    method: 'put',
    data: parameter
  })
}

export function getSensitiveWordDetail(parameter) {
  return axios({
    url: '/sys/sensitiveWord/queryById',
    method: 'get',
    params: parameter
  })
}

// 导入导出接口
export function getExportUrl() {
  return `${window._CONFIG['domianURL']}/sys/sensitiveWord/exportXls`
}

export function getImportUrl() {
  return `${window._CONFIG['domianURL']}/sys/sensitiveWord/importExcel`
}

export function importFromHoubb() {
  return axios({
    url: '/sys/sensitiveWord/importFromHoubb',
    method: 'post'
  })
}

// 敏感词检测接口
export function checkSensitiveWord(text) {
  return axios({
    url: '/sys/sensitiveWord/check',
    method: 'post',
    params: { text: text }
  })
}

export function replaceSensitiveWord(text, replacement = '*') {
  return axios({
    url: '/sys/sensitiveWord/replace',
    method: 'post',
    params: { 
      text: text,
      replacement: replacement
    }
  })
}

// 缓存管理接口
export function refreshSensitiveWordCache() {
  return axios({
    url: '/sys/sensitiveWord/refreshCache',
    method: 'post'
  })
}

// 统计分析接口
export function getSensitiveWordStatistics() {
  return axios({
    url: '/sys/sensitiveWord/statistics',
    method: 'get'
  })
}

export function getSensitiveWordTopHits(limit = 10) {
  return axios({
    url: '/sys/sensitiveWord/topHitWords',
    method: 'get',
    params: { limit: limit }
  })
}

export function getSensitiveWordCategoryStats() {
  return axios({
    url: '/sys/sensitiveWord/categoryStatistics',
    method: 'get'
  })
}

export function getSensitiveWordLevelStats() {
  return axios({
    url: '/sys/sensitiveWord/levelStatistics',
    method: 'get'
  })
}

export function getSensitiveWordHitTrend(days = 7) {
  return axios({
    url: '/sys/sensitiveWord/hitTrend',
    method: 'get',
    params: { days: days }
  })
}

export function getSensitiveWordComprehensiveReport(days = 7) {
  return axios({
    url: '/sys/sensitiveWord/comprehensiveReport',
    method: 'get',
    params: { days: days }
  })
}
