{"openapi": "3.0.0", "info": {"title": "剪映小助手_超级剪映小助手 - 批量添加视频", "description": "批量添加视频", "version": "1.0.0"}, "servers": [{"url": "https://www.aigcview.com"}], "paths": {"/jeecg-boot/api/jianyingpro/add_videos": {"post": {"summary": "批量添加视频", "description": "批量添加视频", "operationId": "add_videos_pro", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"access_key": {"type": "string", "description": "访问密钥（必填）", "example": "JianyingAPI_2025_SecureAccess_AigcView", "default": "JianyingAPI_2025_SecureAccess_AigcView"}, "alpha": {"type": "number", "description": "透明度，范围0-1，默认1", "minimum": 0, "maximum": 1, "example": 1.0}, "draft_url": {"type": "string", "description": "草稿地址，使用create_draft输出的draft_url即可（必填）", "example": "https://example.com/draft/123"}, "scale_x": {"type": "number", "description": "X轴缩放", "example": 1.0}, "scale_y": {"type": "number", "description": "Y轴缩放", "example": 1.0}, "transform_x": {"type": "number", "description": "X轴位置", "example": 0.0}, "transform_y": {"type": "number", "description": "Y轴位置", "example": 0.0}, "video_urls": {"type": "array", "items": {"type": "string"}, "description": "视频URL列表（video_infos为空时必填）", "example": ["https://example.com/video1.mp4", "https://example.com/video2.mp4"]}, "timelines": {"type": "array", "items": {"type": "object", "properties": {"start": {"type": "integer"}, "end": {"type": "integer"}}}, "description": "时间节点，只接收结构：[{\"start\":0,\"end\":4612}]，一般从audio_timeline节点的输出获取（必填）", "example": [{"start": 0, "end": 4612000}]}, "mask": {"type": "string", "description": "视频蒙版，可填写值：'线性'，'镜面'，'圆形'，'矩形'，'爱心'，'星形'", "enum": ["圆形", "矩形", "爱心", "星形"], "example": "圆形"}, "height": {"type": "integer", "description": "视频高度", "example": 1080}, "width": {"type": "integer", "description": "视频宽度", "example": 1920}, "transition": {"type": "string", "description": "转场，比如：水墨", "example": "淡入淡出"}, "transition_duration": {"type": "integer", "description": "转场的时长", "example": 1000000}, "volume": {"type": "number", "description": "音量大小，0-10，默认1", "minimum": 0, "maximum": 10, "example": 1}}, "required": ["access_key", "draft_url", "video_urls", "timelines"], "additionalProperties": false}}}}, "responses": {"200": {"description": "成功添加视频", "content": {"application/json": {"schema": {"type": "object", "properties": {"video_ids": {"type": "array", "description": "视频材料ID列表", "items": {"type": "string"}}, "draft_url": {"type": "string", "description": "更新后的草稿地址"}, "segment_ids": {"type": "array", "description": "视频段ID列表", "items": {"type": "string"}}, "track_id": {"type": "string", "description": "视频轨道ID"}, "segment_infos": {"type": "array", "description": "视频段详细信息，包含时间和尺寸信息", "items": {"type": "object", "properties": {"segment_id": {"type": "string", "description": "视频段ID"}, "start": {"type": "integer", "description": "开始时间（微秒）"}, "end": {"type": "integer", "description": "结束时间（微秒）"}, "duration": {"type": "integer", "description": "持续时间（微秒）"}, "width": {"type": "integer", "description": "视频宽度"}, "height": {"type": "integer", "description": "视频高度"}}, "required": ["segment_id", "start", "end", "duration", "width", "height"]}}, "message": {"type": "string", "description": "导入指南信息", "example": "不知道导入的请看：https://www.aigcview.com/JianYingDraft?draft=https://example.com/draft.json"}}, "required": ["video_ids", "draft_url", "segment_ids", "track_id", "segment_infos", "message"], "example": {"video_ids": ["video_uuid_1", "video_uuid_2"], "draft_url": "https://example.com/updated_draft.json", "segment_ids": ["segment_uuid_1", "segment_uuid_2"], "track_id": "track_uuid", "segment_infos": [{"segment_id": "segment_uuid_1", "start": 0, "end": 5000000, "duration": 5000000, "width": 1080, "height": 1920}, {"segment_id": "segment_uuid_2", "start": 5000000, "end": 10000000, "duration": 5000000, "width": 1920, "height": 1080}], "message": "不知道导入的请看：https://www.aigcview.com/JianYingDraft?draft=https://example.com/updated_draft.json"}}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "description": "错误信息", "example": "参数不完整: 草稿地址不能为空"}, "error_code": {"type": "string", "description": "错误码", "example": "PARAM_INCOMPLETE_003"}, "error_message": {"type": "string", "description": "详细错误消息", "example": "参数不完整"}, "error_details": {"type": "string", "description": "错误解决方案", "example": "请提供有效的draft_url参数"}}, "required": ["error", "error_code"]}}}}, "500": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "description": "错误信息", "example": "批量添加视频失败: 服务器内部错误"}}, "required": ["error"]}}}}}}}}}