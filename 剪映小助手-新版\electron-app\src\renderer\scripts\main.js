// 主应用脚本
class App {
    constructor() {
        this.currentPage = 'home'
        this.lastScrolledFileName = null  // 🆕 跟踪最后滚动的文件名，避免重复滚动
        this.init()
    }

    // 初始化应用
    init() {
        this.bindEvents()
        this.initModules()
        this.setupAppearance()
        
        console.log('🚀 剪映小助手启动成功')
    }

    // 绑定全局事件
    bindEvents() {
        // 页面导航
        const navBtns = document.querySelectorAll('.nav-btn')
        navBtns.forEach(btn => {
            btn.addEventListener('click', (e) => {
                const page = e.currentTarget.dataset.page
                this.switchPage(page)
            })
        })

        // 窗口事件
        window.addEventListener('beforeunload', () => {
            this.cleanup()
        })

        // 键盘快捷键
        document.addEventListener('keydown', (e) => {
            this.handleKeyboard(e)
        })
    }

    // 初始化各个模块
    initModules() {
        // 初始化UI管理器
        if (window.UI) {
            UI.init()
        }

        // 初始化导入管理器
        if (window.ImportManager) {
            ImportManager.init()
        }

        // 初始化设置管理器
        if (window.SettingsManager) {
            SettingsManager.init()
        }

        // 初始化历史管理器
        if (window.HistoryManager) {
            HistoryManager.init()
        }

        // 初始化下载进度管理器
        console.log('🚀 [MAIN] 检查DownloadProgressManager:', !!window.DownloadProgressManager)
        console.log('🚀 [MAIN] window对象中所有属性:', Object.keys(window).filter(k => k.includes('Download') || k.includes('Progress')))

        if (window.DownloadProgressManager) {
            console.log('🚀 [MAIN] 开始初始化DownloadProgressManager')
            console.log('🚀 [MAIN] DownloadProgressManager类型:', typeof window.DownloadProgressManager)
            console.log('🚀 [MAIN] DownloadProgressManager方法:', Object.getOwnPropertyNames(Object.getPrototypeOf(window.DownloadProgressManager)))

            try {
                window.DownloadProgressManager.init()
                console.log('✅ [MAIN] DownloadProgressManager初始化成功')
            } catch (error) {
                console.error('❌ [MAIN] DownloadProgressManager初始化失败:', error)
            }
        } else {
            console.error('❌ [MAIN] DownloadProgressManager未找到，可能脚本加载失败')
            console.error('❌ [MAIN] 尝试延迟初始化...')

            // 延迟重试
            setTimeout(() => {
                console.log('🔄 [MAIN] 延迟检查DownloadProgressManager:', !!window.DownloadProgressManager)
                if (window.DownloadProgressManager) {
                    console.log('✅ [MAIN] 延迟找到DownloadProgressManager，开始初始化')
                    window.DownloadProgressManager.init()
                } else {
                    console.error('❌ [MAIN] 延迟检查仍未找到DownloadProgressManager')
                }
            }, 500)
        }

        // 设置IPC监听器
        this.setupIpcListeners()
    }

    // 设置IPC监听器
    setupIpcListeners() {
        if (typeof require !== 'undefined') {
            const { ipcRenderer } = require('electron')

            // 监听来自主进程的关于对话框显示请求
            ipcRenderer.on('show-about-dialog', () => {
                console.log('收到显示关于对话框的请求')
                this.showAbout()
            })

            // 🆕 监听批量下载初始化事件
            ipcRenderer.on('init-batch-download', (event, batchData) => {
                console.log('🚀 [BATCH] 收到批量下载初始化事件:', batchData)

                if (window.DownloadProgressManager) {
                    console.log('🚀 [BATCH] 初始化渲染进程批量下载状态')
                    window.DownloadProgressManager.startBatch(batchData)
                    console.log('🚀 [BATCH] 批量状态初始化完成，当前batchInfo:', window.DownloadProgressManager.batchInfo)
                } else {
                    console.error('❌ [BATCH] DownloadProgressManager不存在，无法初始化批量状态')
                }
            })

            // 监听下载进度
            ipcRenderer.on('download-file-progress', (event, progressData) => {
                console.log('📨 [IPC] 收到下载进度数据:', progressData)
                console.log('📨 [IPC] DownloadProgressManager存在:', !!window.DownloadProgressManager)
                console.log('📨 [IPC] DownloadProgressManager详情:', window.DownloadProgressManager)

                if (window.DownloadProgressManager) {
                    console.log('📊 [IPC] 调用进度管理器更新')
                    console.log('📊 [IPC] 当前可见状态:', window.DownloadProgressManager.isVisible)

                    // 强制显示进度条（如果还没显示）
                    if (!window.DownloadProgressManager.isVisible) {
                        console.log('🎯 [IPC] 强制显示进度条')
                        window.DownloadProgressManager.showProgress()
                    }

                    // 🆕 优化：扩展触发条件，确保快速下载也能滚动
                    const isSystemMessage = progressData.fileName && (
                        progressData.fileName.includes('批量下载') ||
                        progressData.fileName.includes('draft_')
                    )

                    const shouldScroll = !isSystemMessage && (
                        progressData.status === 'starting' ||
                        progressData.progress === 0 ||
                        // 🆕 修复：支持单链接下载（无currentFileIndex的情况）
                        (progressData.progress !== undefined && progressData.progress < 10) ||
                        // 🆕 新增：检测文件名变化，确保新文件开始时滚动
                        (progressData.fileName && window.app?.lastScrolledFileName !== progressData.fileName)
                    )

                    if (shouldScroll) {
                        console.log('🎯 [AUTO] 检测到新文件开始下载，自动滚动到下载日志区域')
                        console.log('🎯 [AUTO] 触发条件:', {
                            status: progressData.status,
                            progress: progressData.progress,
                            currentFileIndex: progressData.currentFileIndex,
                            fileName: progressData.fileName,
                            lastScrolledFileName: window.app?.lastScrolledFileName,
                            isNewFile: window.app?.lastScrolledFileName !== progressData.fileName
                        })

                        // 记录当前文件名，避免同一文件重复滚动
                        if (window.app) {
                            window.app.lastScrolledFileName = progressData.fileName
                        }

                        // 🚀 使用优化的滚动管理器替换原有逻辑
                        if (window.scrollManager) {
                            window.scrollManager.scrollToLogs()
                        } else {
                            // 降级到原有逻辑作为备用方案
                            console.warn('⚠️ [AUTO] 滚动管理器未初始化，使用降级方案')
                            setTimeout(() => {
                                const logsSection = document.querySelector('.download-logs')
                                if (logsSection) {
                                    logsSection.scrollIntoView({
                                        behavior: 'smooth',
                                        block: 'end'
                                    })
                                }
                            }, 500)
                        }
                    }

                    window.DownloadProgressManager.updateProgress(progressData)
                } else {
                    console.error('❌ [IPC] DownloadProgressManager 未找到')
                    console.error('❌ [IPC] window对象:', Object.keys(window))
                }
            })

            // 监听批量下载开始
            ipcRenderer.on('download-batch-start', (event, batchData) => {
                if (window.DownloadProgressManager) {
                    window.DownloadProgressManager.startBatch(batchData)
                }
            })

            // 监听批量下载完成
            ipcRenderer.on('download-batch-complete', (event, batchData) => {
                if (window.DownloadProgressManager) {
                    window.DownloadProgressManager.completeBatch(batchData)
                }
            })

            console.log('✅ IPC监听器设置完成')
        } else {
            console.warn('⚠️ require不可用，无法设置IPC监听器')
        }
    }

    // 设置应用外观
    setupAppearance() {
        // 设置窗口标题
        document.title = '剪映小助手 - 智界AigcView'
        
        // 检查系统主题
        this.detectSystemTheme()
    }

    // 检测系统主题
    detectSystemTheme() {
        if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
            document.body.classList.add('dark-theme')
        }

        // 监听主题变化
        window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
            if (e.matches) {
                document.body.classList.add('dark-theme')
            } else {
                document.body.classList.remove('dark-theme')
            }
        })
    }

    // 切换页面
    switchPage(pageName) {
        if (pageName === this.currentPage) return

        // 更新导航状态
        const navBtns = document.querySelectorAll('.nav-btn')
        navBtns.forEach(btn => {
            btn.classList.remove('active')
            if (btn.dataset.page === pageName) {
                btn.classList.add('active')
            }
        })

        // 切换页面内容
        const pages = document.querySelectorAll('.page')
        pages.forEach(page => {
            page.classList.remove('active')
        })

        const targetPage = document.getElementById(`${pageName}-page`)
        if (targetPage) {
            targetPage.classList.add('active')
            this.currentPage = pageName

            // 页面切换后的处理
            this.onPageSwitch(pageName)
        }
    }

    // 页面切换后的处理
    onPageSwitch(pageName) {
        switch (pageName) {
            case 'home':
                // 不自动检查剪贴板，让用户手动操作
                // setTimeout(() => {
                //     if (window.ImportManager) {
                //         ImportManager.pasteFromClipboard()
                //     }
                // }, 100)
                break

            case 'history':
                // 刷新历史记录
                if (window.HistoryManager) {
                    HistoryManager.loadHistory()
                }
                break

            case 'settings':
                // 刷新设置（但不触发自动检测）
                if (window.SettingsManager) {
                    SettingsManager.loadSettingsWithoutAutoDetect()
                }
                break
        }
    }

    // 处理键盘事件
    handleKeyboard(e) {
        // Ctrl+数字键切换页面
        if (e.ctrlKey && !e.shiftKey && !e.altKey) {
            switch (e.key) {
                case '1':
                    e.preventDefault()
                    this.switchPage('home')
                    break
                case '2':
                    e.preventDefault()
                    this.switchPage('history')
                    break
                case '3':
                    e.preventDefault()
                    this.switchPage('settings')
                    break
            }
        }

        // ESC键关闭模态框
        if (e.key === 'Escape') {
            if (window.UI) {
                UI.hideModal()
            }
        }

        // F5刷新当前页面
        if (e.key === 'F5') {
            e.preventDefault()
            this.refreshCurrentPage()
        }
    }

    // 刷新当前页面
    refreshCurrentPage() {
        this.onPageSwitch(this.currentPage)
        
        if (window.UI) {
            UI.showNotification('🔄 页面已刷新', 'info')
        }
    }

    // 应用清理
    cleanup() {
        console.log('🧹 清理应用资源')
        
        // 清理定时器
        if (this.timers) {
            this.timers.forEach(timer => clearTimeout(timer))
        }

        // 清理事件监听器
        document.removeEventListener('keydown', this.handleKeyboard)
    }

    // 显示应用信息
    async showAbout() {
        try {
            // 从主进程获取版本信息
            const { ipcRenderer } = require('electron')
            console.log('正在获取版本信息...')
            const versionInfo = await ipcRenderer.invoke('get-version-info')
            console.log('版本信息响应:', versionInfo)

            let currentVersion = '1.0.1' // 默认版本号，与package.json保持一致

            if (versionInfo && versionInfo.success && versionInfo.data && versionInfo.data.currentVersion) {
                currentVersion = versionInfo.data.currentVersion
                console.log('使用动态获取的版本号:', currentVersion)
            } else {
                console.warn('版本信息获取失败，使用默认版本号:', currentVersion)
            }

            // 直接显示关于弹窗，不使用UI.showModal的外壳
            this.showAboutModal(currentVersion)

        } catch (error) {
            console.error('获取版本信息失败:', error)
            // 如果获取失败，使用当前package.json的版本号
            this.showAboutModal('1.0.1')
        }
    }

    // 显示关于弹窗（直接操作DOM，避免双重嵌套）
    showAboutModal(currentVersion) {
        // 创建弹窗HTML
        const modalHtml = `
            <div id="about-modal" class="update-modal">
                <div class="update-modal-content about-modal-content">
                    <div class="update-header">
                        <div class="update-icon">ℹ️</div>
                        <h2 class="update-title">关于剪映小助手</h2>
                    </div>

                    <div class="update-body">
                        <div class="about-info-section">
                            <div class="app-logo">
                                <img src="../../assets/icon.png" alt="剪映小助手" width="64" height="64">
                            </div>

                            <div class="app-details">
                                <h3 class="app-name">剪映小助手</h3>
                                <div class="version-info">
                                    <div class="current-version">
                                        <span class="version-label">当前版本：</span>
                                        <span class="version-number">v${currentVersion}</span>
                                    </div>
                                </div>

                                <div class="app-description">
                                    <p>专业的剪映草稿导入工具，支持从扣子平台一键导入草稿到本地剪映。</p>
                                </div>

                                <div class="developer-info">
                                    <p><strong>开发者：</strong>智界AigcView</p>
                                    <p><strong>官网：</strong><a href="#" onclick="app.openWebsite()" class="website-link">www.aigcview.com</a></p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="update-footer">
                        <div class="update-actions">
                            <button class="action-btn primary-btn" onclick="app.hideAboutModal()">
                                <span class="btn-text">确定</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `

        // 添加到页面
        document.body.insertAdjacentHTML('beforeend', modalHtml)

        // 显示弹窗
        const modal = document.getElementById('about-modal')
        if (modal) {
            modal.classList.remove('hidden')
            // 禁用页面滚动
            document.body.style.overflow = 'hidden'
        }
    }

    // 隐藏关于弹窗
    hideAboutModal() {
        const modal = document.getElementById('about-modal')
        if (modal) {
            modal.remove()
            // 恢复页面滚动
            document.body.style.overflow = ''
        }
    }

    // 打开官网
    async openWebsite() {
        try {
            const { ipcRenderer } = require('electron')
            await ipcRenderer.invoke('open-external', 'https://www.aigcview.com')
        } catch (error) {
            console.error('打开网站失败:', error)
        }
    }

    // 打开技术支持
    async openSupport() {
        try {
            const { ipcRenderer } = require('electron')
            await ipcRenderer.invoke('open-external', 'https://www.aigcview.com/support')
        } catch (error) {
            console.error('打开支持页面失败:', error)
        }
    }

    // 检查更新
    async checkUpdate() {
        UI.showNotification('🔍 检查更新中...', 'info')
        
        // 模拟检查更新
        setTimeout(() => {
            UI.showNotification('✅ 当前已是最新版本', 'success')
        }, 2000)
    }

    // 重启应用
    async restartApp() {
        try {
            const { ipcRenderer } = require('electron')
            await ipcRenderer.invoke('restart-app')
        } catch (error) {
            console.error('重启应用失败:', error)
        }
    }
}

// 历史管理器
const HistoryManager = {
    historyClickHandler: null, // 存储事件处理器引用
    isOpening: false, // 防止重复打开

    init() {
        this.loadHistory()
    },

    async loadHistory() {
        try {
            const { ipcRenderer } = require('electron')
            const history = await ipcRenderer.invoke('get-import-history')
            this.renderHistory(history)
        } catch (error) {
            console.error('加载历史失败:', error)
        }
    },

    renderHistory(history) {
        console.log('渲染历史记录，数据:', history)

        const historyList = document.getElementById('history-list')

        if (!history || history.length === 0) {
            console.log('历史记录为空')
            historyList.innerHTML = `
                <div class="empty-state">
                    <div class="empty-icon">📂</div>
                    <h3>暂无导入历史</h3>
                    <p>导入草稿后，历史记录将显示在这里</p>
                </div>
            `
            return
        }

        console.log('渲染历史记录项目数量:', history.length)

        // 先清空容器，移除所有旧的事件监听器
        historyList.innerHTML = ''

        // 逐个创建历史记录项
        history.forEach((item, index) => {
            console.log(`历史记录项 ${index}:`, {
                name: item.name,
                projectPath: item.projectPath,
                draftPath: item.draftPath,
                timestamp: item.timestamp
            })

            // 创建历史记录项元素
            const historyItem = document.createElement('div')
            historyItem.className = 'history-item'

            historyItem.innerHTML = `
                <div class="history-icon">🎬</div>
                <div class="history-content">
                    <h4 class="history-title">${item.name}</h4>
                    <p class="history-time">${new Date(item.timestamp).toLocaleString()}</p>
                    <p class="history-path">${item.projectPath}</p>
                </div>
                <div class="history-actions">
                    <button class="history-btn" data-project-path="${item.projectPath}">
                        打开
                    </button>
                </div>
            `

            historyList.appendChild(historyItem)
        })

        // 使用事件委托，避免重复绑定
        this.bindHistoryEvents(historyList)
    },

    // 绑定历史记录事件（使用事件委托避免重复绑定）
    bindHistoryEvents(historyList) {
        // 移除旧的事件监听器
        historyList.removeEventListener('click', this.historyClickHandler)

        // 创建新的事件处理器
        this.historyClickHandler = (e) => {
            const button = e.target.closest('.history-btn')
            if (button) {
                e.preventDefault()
                e.stopPropagation()

                const projectPath = button.getAttribute('data-project-path')
                console.log('事件委托：点击打开按钮，项目路径:', projectPath)

                if (projectPath) {
                    this.openProject(projectPath)
                }
            }
        }

        // 添加新的事件监听器
        historyList.addEventListener('click', this.historyClickHandler)
    },

    async openProject(projectPath) {
        console.log('渲染进程：点击打开按钮，路径:', projectPath)

        // 防止重复调用
        if (this.isOpening) {
            console.log('防重复：正在打开文件夹，跳过重复调用')
            return
        }

        this.isOpening = true

        try {
            const { ipcRenderer } = require('electron')

            console.log('渲染进程：调用主进程打开文件夹...')
            const result = await ipcRenderer.invoke('open-project-folder', projectPath)

            console.log('渲染进程：主进程返回结果:', result)

            if (result.success) {
                UI.showNotification(`✅ ${result.message}`, 'success')
            } else {
                UI.showNotification(`❌ ${result.error}`, 'error')
            }

        } catch (error) {
            console.error('渲染进程：打开项目失败:', error)
            UI.showNotification(`❌ 打开项目失败: ${error.message}`, 'error')
        } finally {
            // 500ms后重置状态
            setTimeout(() => {
                this.isOpening = false
            }, 500)
        }
    }
}

// 全局变量
window.HistoryManager = HistoryManager

// 启动应用
document.addEventListener('DOMContentLoaded', () => {
    window.app = new App()
})

// 导出应用实例
window.App = App

// 添加全局方法到window对象，供HTML中使用
window.switchPage = (page) => {
    if (window.app) {
        window.app.switchPage(page)
    }
}

// 版本更新模态弹窗管理器
class UpdateModalManager {
    constructor() {
        this.modal = null;
        this.currentUpdateInfo = null;
        this.isForceUpdate = false;
        this.isExiting = false; // 防止重复退出
        this.init();
    }

    init() {
        // 等待DOM加载完成
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                this.setupModal();
            });
        } else {
            this.setupModal();
        }
    }

    setupModal() {
        this.modal = document.getElementById('update-modal');
        this.setupEventListeners();
    }

    setupEventListeners() {
        // 监听来自主进程的更新消息
        if (typeof require !== 'undefined') {
            const { ipcRenderer } = require('electron');

            ipcRenderer.on('show-update-modal', (event, data) => {
                console.log('收到显示更新模态弹窗的消息:', data);
                this.showModal(data.versionInfo, data.forceUpdate);
            });
        } else {
            console.warn('require不可用，无法设置IPC监听器');
        }
    }

    showModal(versionInfo, forceUpdate = false) {
        if (!this.modal) {
            console.error('更新模态弹窗元素未找到');
            return;
        }

        this.currentUpdateInfo = versionInfo;
        this.isForceUpdate = true; // 所有更新都视为强制更新

        // 更新模态弹窗内容
        this.updateModalContent(versionInfo, true); // 所有更新都使用强制更新样式

        // 显示模态弹窗
        this.modal.classList.remove('hidden');

        // 添加强制更新样式
        this.modal.classList.add('force-update');
        // 禁用页面其他交互
        document.body.style.overflow = 'hidden';
    }

    updateModalContent(versionInfo, forceUpdate) {
        // 更新标题 - 所有更新都显示为必须更新
        const title = document.getElementById('update-title');
        if (title) {
            title.textContent = '必须更新';
        }

        // 更新版本信息
        const currentVersion = document.getElementById('current-version');
        const latestVersion = document.getElementById('latest-version');

        if (typeof require !== 'undefined') {
            const { ipcRenderer } = require('electron');
            ipcRenderer.invoke('get-version-info').then(info => {
                if (info.success && currentVersion) {
                    currentVersion.textContent = `v${info.data.currentVersion}`;
                }
            });
        }

        if (latestVersion) {
            latestVersion.textContent = `v${versionInfo.latestVersion}`;
        }

        // 更新内容
        const updateContent = document.getElementById('update-content');
        if (updateContent) {
            updateContent.textContent = versionInfo.updateContent || '暂无更新说明';
        }

        // 更新按钮
        this.updateActionButtons(versionInfo, forceUpdate);
    }

    updateActionButtons(versionInfo, forceUpdate) {
        const actionsContainer = document.getElementById('update-actions');
        if (!actionsContainer) return;

        actionsContainer.innerHTML = '';

        // 所有更新都是强制性的：只有"立即更新"和"退出应用"按钮
        const updateBtn = this.createButton('立即更新', 'primary', () => {
            this.handleUpdate(versionInfo);
        });

        const exitBtn = this.createButton('退出应用', 'danger', () => {
            this.handleExit();
        });

        actionsContainer.appendChild(updateBtn);
        actionsContainer.appendChild(exitBtn);
    }

    createButton(text, type, onClick) {
        const button = document.createElement('button');
        button.className = `update-btn ${type}`;
        button.textContent = text;
        button.addEventListener('click', onClick);
        return button;
    }

    async handleUpdate(versionInfo) {
        // 🚀 保存版本信息供后续使用
        this.currentVersionInfo = versionInfo;

        // 🔧 [FIX] 重置下载完成标志
        this.downloadCompleted = false;

        // 🚀 应用内下载更新文件
        if (versionInfo.downloadUrl) {
            if (typeof require !== 'undefined') {
                const { ipcRenderer } = require('electron');

                try {
                    // 显示下载开始提示
                    this.showDownloadStarted();

                    // 启动应用内下载
                    console.log('🚀 开始应用内下载更新文件:', versionInfo.downloadUrl);
                    const result = await ipcRenderer.invoke('download-update-file', {
                        downloadUrl: versionInfo.downloadUrl,
                        version: versionInfo.latestVersion,
                        fileName: this.generateFileName(versionInfo.latestVersion)
                    });

                    // 🔧 [FIX] 移除重复处理 - 下载完成由事件监听器处理
                    if (!result.success) {
                        // 只处理下载失败的情况
                        console.error('应用内下载失败，回退到浏览器下载:', result.error);
                        this.handleDownloadError({ message: result.error });
                    }
                    // 下载成功的情况由 'download-complete' 事件处理，避免重复

                } catch (error) {
                    console.error('下载更新文件失败:', error);
                    // 处理下载错误
                    this.handleDownloadError(error);
                }
            }
        } else {
            // 没有下载链接，退出应用
            this.handleExit();
        }
    }

    // 显示下载开始提示
    showDownloadStarted() {
        // 更新按钮状态
        const updateBtn = document.querySelector('.update-btn.primary');
        if (updateBtn) {
            updateBtn.textContent = '下载中...';
            updateBtn.disabled = true;
        }

        // 🚀 不再隐藏弹窗，而是在弹窗内显示下载进度
        this.showInModalProgress();

        // 🚀 监听下载进度事件
        this.setupProgressListeners();
    }

    // 🚀 在弹窗内显示下载进度
    showInModalProgress() {
        // 隐藏更新内容，显示下载进度
        const updateContent = document.querySelector('.update-content');
        const progressArea = document.getElementById('update-download-progress');
        const updateActions = document.getElementById('update-actions');

        if (updateContent) {
            updateContent.style.display = 'none';
        }

        if (progressArea) {
            progressArea.classList.remove('hidden');
        }

        // 隐藏操作按钮
        if (updateActions) {
            updateActions.style.display = 'none';
        }

        console.log('🚀 弹窗内下载进度区域已显示');
    }

    // 🚀 设置下载进度监听器
    setupProgressListeners() {
        if (typeof require !== 'undefined') {
            const { ipcRenderer } = require('electron');

            // 监听下载进度事件
            ipcRenderer.on('download-progress', (event, data) => {
                this.updateInModalProgress(data);
            });

            // 监听下载完成事件
            ipcRenderer.on('download-complete', (event, data) => {
                this.handleDownloadComplete(data);
            });

            // 监听下载错误事件
            ipcRenderer.on('download-error', (event, error) => {
                this.handleDownloadError(error);
            });

            console.log('🚀 下载进度监听器已设置');
        }
    }

    // 🚀 更新弹窗内的下载进度
    updateInModalProgress(data) {
        console.log('🚀 更新弹窗内进度:', data);

        // 更新文件名
        const filenameEl = document.getElementById('update-progress-filename');
        if (filenameEl && data.fileName) {
            filenameEl.textContent = data.fileName;
        }

        // 更新状态
        const statusEl = document.getElementById('update-progress-status');
        if (statusEl) {
            if (data.status === 'downloading') {
                statusEl.textContent = '正在下载...';
            } else if (data.status === 'completed') {
                statusEl.textContent = '下载完成';
            } else {
                statusEl.textContent = data.status || '下载中...';
            }
        }

        // 更新进度条
        const progressFill = document.getElementById('update-progress-fill');
        const progressPercent = document.getElementById('update-progress-percent');
        if (progressFill && progressPercent && data.progress !== undefined) {
            const percent = Math.round(data.progress);
            progressFill.style.width = `${percent}%`;
            progressPercent.textContent = `${percent}%`;
        }

        // 更新下载速度
        const speedEl = document.getElementById('update-progress-speed');
        if (speedEl && data.speed) {
            speedEl.textContent = data.speed;
        }

        // 更新文件大小
        const sizeEl = document.getElementById('update-progress-size');
        if (sizeEl && data.downloaded && data.total) {
            sizeEl.textContent = `${data.downloaded} / ${data.total}`;
        }
    }

    // 🚀 处理下载完成
    handleDownloadComplete(data) {
        console.log('🚀 下载完成:', data);

        // 🔧 [FIX] 防重复处理机制
        if (this.downloadCompleted) {
            console.log('🔧 下载已完成，跳过重复处理');
            return;
        }
        this.downloadCompleted = true;

        // 更新状态为完成
        this.updateInModalProgress({
            status: 'completed',
            progress: 100
        });

        // 显示准备安装状态
        setTimeout(() => {
            this.updateInModalProgress({
                status: '准备安装...'
            });
        }, 800);

        // 延迟一下再自动安装，让用户看到完成状态
        setTimeout(() => {
            if (data.filePath) {
                this.showInstallPrompt(data.filePath);
            }
        }, 1500);
    }

    // 🚀 处理下载错误
    handleDownloadError(error) {
        console.error('🚀 下载错误:', error);

        // 更新状态为错误
        const statusEl = document.getElementById('update-progress-status');
        if (statusEl) {
            statusEl.textContent = `下载失败: ${error.message || error}`;
            statusEl.style.color = '#dc2626';
        }

        // 延迟后回退到浏览器下载
        setTimeout(() => {
            alert('应用内下载失败，将使用浏览器下载');
            this.fallbackToBrowserDownload(this.currentVersionInfo?.downloadUrl);
        }, 2000);
    }

    // 🚀 直接安装，无需提示
    showInstallPrompt(filePath) {
        const { ipcRenderer } = require('electron');

        console.log('🚀 下载完成，直接开始安装:', filePath);

        // 🔧 [FIX] 跳过安装提示对话框，直接安装
        try {
            // 直接打开安装文件并退出应用
            console.log('🚀 正在打开安装文件...');
            ipcRenderer.invoke('open-installer', filePath);

            // 延迟一下再退出，确保安装文件能正常打开
            setTimeout(() => {
                this.handleExit();
            }, 500);

        } catch (error) {
            console.error('🚀 打开安装文件出错:', error);
            // 即使出错也退出应用
            this.handleExit();
        }
    }

    // 回退到浏览器下载
    fallbackToBrowserDownload(downloadUrl) {
        const { ipcRenderer } = require('electron');
        console.log('🔄 回退到浏览器下载模式');
        ipcRenderer.invoke('open-external', downloadUrl);
        this.handleExit();
    }

    // 🚀 根据系统生成对应的文件名
    generateFileName(version) {
        const { ipcRenderer } = require('electron');

        // 获取系统信息（同步方式，因为这是渲染进程）
        const userAgent = navigator.userAgent;
        const platform = navigator.platform;

        let fileName = `剪映小助手-v${version}`;

        if (platform.includes('Win') || userAgent.includes('Windows')) {
            fileName += '.exe';
        } else if (platform.includes('Mac') || userAgent.includes('Mac')) {
            fileName += '.dmg';
        } else if (platform.includes('Linux') || userAgent.includes('Linux')) {
            fileName += '.AppImage';
        } else {
            // 默认使用exe
            fileName += '.exe';
        }

        console.log('🚀 生成文件名:', { platform, userAgent, fileName });
        return fileName;
    }

    // 移除了handleLater和handleSkip方法，因为所有更新都是强制的

    handleExit() {
        if (this.isExiting) {
            console.log('已经在退出过程中，忽略重复请求');
            return;
        }

        this.isExiting = true;
        console.log('用户点击退出应用按钮');

        // 禁用退出按钮，防止重复点击
        const exitBtn = document.querySelector('.update-btn.danger');
        if (exitBtn) {
            exitBtn.disabled = true;
            exitBtn.textContent = '正在退出...';
        }

        // 退出应用
        if (typeof require !== 'undefined') {
            const { ipcRenderer } = require('electron');
            console.log('发送app-quit消息到主进程');
            ipcRenderer.send('app-quit');
        } else {
            console.error('require不可用，无法发送退出消息');
        }
    }

    hideModal() {
        if (!this.modal) return;

        this.modal.classList.add('hidden');
        this.modal.classList.remove('force-update');
        document.body.style.overflow = '';

        // 如果是强制更新，重新启用主窗口关闭按钮
        if (this.isForceUpdate && typeof require !== 'undefined') {
            const { ipcRenderer } = require('electron');
            ipcRenderer.send('enable-window-close');
        }

        this.currentUpdateInfo = null;
        this.isForceUpdate = false;
    }
}

// 创建更新模态弹窗管理器实例
window.updateModalManager = new UpdateModalManager();

// 🆕 创建滚动管理器实例（优化滚动性能）
window.scrollManager = new ScrollManager();

// 🧪 测试应用内下载功能
window.testInAppDownload = function() {
    console.log('🧪 测试应用内下载功能');
    const testVersionInfo = {
        downloadUrl: 'https://github.com/electron/electron/releases/download/v25.0.0/electron-v25.0.0-win32-x64.zip',
        latestVersion: '1.0.1',
        versionNumber: '1.0.1'
    };

    if (window.updateModalManager) {
        window.updateModalManager.handleUpdate(testVersionInfo);
    }
};

// 🧪 测试多平台链接解析功能
window.testMultiPlatformUrls = function() {
    console.log('🧪 测试多平台链接解析功能');

    // 测试不同格式的多平台链接
    const testCases = [
        {
            name: '竖线分隔',
            downloadUrl: 'https://aigcview.com/download/desktop/windows/v1.0.0|https://aigcview.com/download/desktop/mac/v1.0.0'
        },
        {
            name: '分号分隔',
            downloadUrl: 'https://aigcview.com/download/desktop/windows/v1.0.0;https://aigcview.com/download/desktop/mac/v1.0.0'
        },
        {
            name: '逗号分隔',
            downloadUrl: 'https://aigcview.com/download/desktop/windows/v1.0.0,https://aigcview.com/download/desktop/mac/v1.0.0'
        },
        {
            name: '单个链接',
            downloadUrl: 'https://aigcview.com/download/desktop/v1.0.0'
        }
    ];

    testCases.forEach(testCase => {
        console.log(`\n🧪 测试 ${testCase.name}:`);
        console.log('原始链接:', testCase.downloadUrl);

        const testVersionInfo = {
            downloadUrl: testCase.downloadUrl,
            latestVersion: '1.0.1',
            versionNumber: '1.0.1'
        };

        if (window.updateModalManager) {
            window.updateModalManager.handleUpdate(testVersionInfo);
        }
    });
};
