// 设置管理模块
const SettingsManager = {
    // 标志：是否正在进行路径设置操作
    isSettingPath: false,

    // 初始化设置功能
    init() {
        this.bindEvents()
        this.loadSettings()
    },

    // 绑定事件
    bindEvents() {
        const browsePathBtn = document.getElementById('browse-path-btn')
        const autoDetectBtn = document.getElementById('auto-detect-btn')
        const testPathBtn = document.getElementById('test-path-btn')
        const websiteLink = document.getElementById('website-link')

        // 浏览路径按钮
        if (browsePathBtn) {
            browsePathBtn.addEventListener('click', () => {
                this.browseJianyingPath()
            })
        }

        // 自动检测按钮
        if (autoDetectBtn) {
            autoDetectBtn.addEventListener('click', () => {
                this.autoDetectJianying()
            })
        }

        // 测试路径按钮
        if (testPathBtn) {
            testPathBtn.addEventListener('click', () => {
                this.testJianyingPath()
            })
        }

        // 官网链接
        if (websiteLink) {
            websiteLink.addEventListener('click', (e) => {
                e.preventDefault()
                this.openWebsite()
            })
        }

        // 版本检查相关事件
        this.bindVersionCheckEvents()

        // 快速操作卡片（检查元素是否存在）
        const checkJianyingCard = document.getElementById('check-jianying-card')
        const autoDetectCard = document.getElementById('auto-detect-card')

        if (checkJianyingCard) {
            checkJianyingCard.addEventListener('click', () => {
                this.testJianyingPath()
            })
        }

        if (autoDetectCard) {
            autoDetectCard.addEventListener('click', () => {
                this.autoDetectJianying()
            })
        }
    },

    // 加载设置
    async loadSettings() {
        try {
            const { ipcRenderer } = require('electron')
            const jianyingPath = await ipcRenderer.invoke('get-jianying-path')

            const pathInput = document.getElementById('jianying-path')
            pathInput.value = jianyingPath || ''

            // 如果没有设置路径且用户没有正在进行路径设置操作，尝试自动检测
            if (!jianyingPath && !this.isSettingPath) {
                setTimeout(() => {
                    // 再次检查是否正在设置路径，避免冲突
                    if (!this.isSettingPath) {
                        this.autoDetectJianying(false) // 静默检测
                    }
                }, 1000)
            }

            // 加载版本检查设置
            await this.loadVersionSettings()
        } catch (error) {
            console.error('加载设置失败:', error)
        }
    },

    // 加载设置（不触发自动检测）
    async loadSettingsWithoutAutoDetect() {
        try {
            const { ipcRenderer } = require('electron')
            const jianyingPath = await ipcRenderer.invoke('get-jianying-path')

            const pathInput = document.getElementById('jianying-path')
            pathInput.value = jianyingPath || ''

            this.updateQuickActionStatus()
        } catch (error) {
            console.error('加载设置失败:', error)
        }
    },

    // 浏览剪映路径
    async browseJianyingPath() {
        try {
            // 设置标志，避免与自动检测冲突
            this.isSettingPath = true

            console.log('开始选择剪映路径...')
            const { ipcRenderer } = require('electron')
            const result = await ipcRenderer.invoke('select-jianying-path')

            console.log('主进程返回结果:', result)

            if (result && result.success) {
                const pathInput = document.getElementById('jianying-path')
                pathInput.value = result.path

                console.log('路径设置成功:', result.path)
                UI.showNotification('✅ 剪映路径设置成功', 'success')
                this.updateQuickActionStatus()
            } else {
                console.log('用户取消选择或选择失败')
                // 用户取消选择，不显示错误提醒
                if (result && result.error) {
                    UI.showNotification('❌ ' + result.error, 'error')
                }
            }
        } catch (error) {
            console.error('选择路径异常:', error)
            UI.showNotification('❌ 选择路径失败: ' + error.message, 'error')
        } finally {
            // 重置标志
            setTimeout(() => {
                this.isSettingPath = false
            }, 2000) // 2秒后重置，确保自动检测不会立即触发
        }
    },

    // 自动检测剪映
    async autoDetectJianying(showNotification = true) {
        try {
            // 如果正在进行手动设置，跳过自动检测
            if (this.isSettingPath) {
                return
            }

            // 设置标志
            this.isSettingPath = true

            if (showNotification) {
                UI.showNotification('🔍 正在自动检测剪映...', 'info')
            }

            const { ipcRenderer } = require('electron')
            const result = await ipcRenderer.invoke('auto-detect-jianying')

            if (result.success) {
                const pathInput = document.getElementById('jianying-path')
                pathInput.value = result.path

                if (showNotification) {
                    UI.showNotification('✅ 自动检测成功！已设置剪映路径', 'success')
                }
                this.updateQuickActionStatus()
            } else {
                if (showNotification) {
                    UI.showNotification('❌ 未找到剪映安装路径，请手动选择', 'warning')
                }
            }
        } catch (error) {
            console.error('自动检测失败:', error)
            if (showNotification) {
                UI.showNotification('❌ 自动检测失败', 'error')
            }
        } finally {
            // 重置标志
            setTimeout(() => {
                this.isSettingPath = false
            }, 1000)
        }
    },

    // 测试剪映路径
    async testJianyingPath() {
        try {
            const pathInput = document.getElementById('jianying-path')
            const jianyingPath = pathInput.value.trim()
            
            if (!jianyingPath) {
                UI.showNotification('⚠️ 请先设置剪映路径', 'warning')
                return
            }

            UI.showNotification('🧪 正在测试剪映路径...', 'info')

            const fs = require('fs')
            
            if (fs.existsSync(jianyingPath)) {
                // 尝试启动剪映进行测试
                const { spawn } = require('child_process')
                
                const testProcess = spawn(jianyingPath, [], {
                    detached: true,
                    stdio: 'ignore'
                })

                testProcess.unref()

                UI.showNotification('✅ 剪映路径测试成功！', 'success')
                this.updateQuickActionStatus()
            } else {
                UI.showNotification('❌ 剪映路径不存在，请重新选择', 'error')
            }
        } catch (error) {
            console.error('测试路径失败:', error)
            UI.showNotification('❌ 测试失败，请检查路径是否正确', 'error')
        }
    },

    // 更新快速操作状态
    updateQuickActionStatus() {
        const pathInput = document.getElementById('jianying-path')

        // 检查元素是否存在，避免空指针错误
        if (!pathInput) {
            console.warn('jianying-path元素不存在')
            return
        }

        // 当前HTML中没有快速操作卡片，所以这里只做基本检查
        // 如果路径有效，可以在这里添加其他UI更新逻辑
        const hasPath = pathInput.value.trim()
        console.log('路径状态更新:', hasPath ? '已设置' : '未设置')
    },

    // 打开官网
    async openWebsite() {
        try {
            const { ipcRenderer } = require('electron')
            await ipcRenderer.invoke('open-external', 'https://www.aigcview.com')
        } catch (error) {
            console.error('打开网站失败:', error)
        }
    },

    // 获取剪映路径
    async getJianyingPath() {
        try {
            const { ipcRenderer } = require('electron')
            return await ipcRenderer.invoke('get-jianying-path')
        } catch (error) {
            console.error('获取剪映路径失败:', error)
            return ''
        }
    },

    // 验证剪映路径是否有效
    async validateJianyingPath() {
        const jianyingPath = await this.getJianyingPath()
        
        if (!jianyingPath) {
            return { valid: false, error: '未设置剪映路径' }
        }

        const fs = require('fs')
        if (!fs.existsSync(jianyingPath)) {
            return { valid: false, error: '剪映路径不存在' }
        }

        return { valid: true, path: jianyingPath }
    },

    // 显示剪映路径设置引导
    showPathSetupGuide() {
        const modal = {
            title: '🎬 设置剪映路径',
            content: `
                <div class="setup-guide">
                    <div class="guide-step">
                        <div class="step-number">1</div>
                        <div class="step-content">
                            <h4>自动检测</h4>
                            <p>点击"自动检测"按钮，系统会自动搜索剪映安装位置</p>
                        </div>
                    </div>
                    <div class="guide-step">
                        <div class="step-number">2</div>
                        <div class="step-content">
                            <h4>手动选择</h4>
                            <p>如果自动检测失败，点击"浏览"按钮手动选择剪映程序</p>
                        </div>
                    </div>
                    <div class="guide-step">
                        <div class="step-number">3</div>
                        <div class="step-content">
                            <h4>测试路径</h4>
                            <p>设置完成后，点击"测试路径"验证是否正确</p>
                        </div>
                    </div>
                </div>
            `,
            actions: [
                {
                    text: '自动检测',
                    type: 'primary',
                    action: () => {
                        UI.hideModal()
                        this.autoDetectJianying()
                    }
                },
                {
                    text: '手动选择',
                    type: 'secondary',
                    action: () => {
                        UI.hideModal()
                        this.browseJianyingPath()
                    }
                }
            ]
        }

        UI.showModal(modal)
    },

    // 绑定版本检查相关事件
    bindVersionCheckEvents() {
        const autoCheckUpdates = document.getElementById('auto-check-updates')
        const checkInterval = document.getElementById('check-interval')
        const showOptionalUpdates = document.getElementById('show-optional-updates')
        const checkUpdatesBtn = document.getElementById('check-updates-btn')

        // 自动检查更新开关
        if (autoCheckUpdates) {
            autoCheckUpdates.addEventListener('change', (e) => {
                this.updateVersionPreference('autoCheck', e.target.checked)
            })
        }

        // 检查间隔选择
        if (checkInterval) {
            checkInterval.addEventListener('change', (e) => {
                this.updateVersionPreference('checkInterval', parseInt(e.target.value))
            })
        }

        // 显示可选更新开关
        if (showOptionalUpdates) {
            showOptionalUpdates.addEventListener('change', (e) => {
                this.updateVersionPreference('showOptionalUpdates', e.target.checked)
            })
        }

        // 检查更新按钮
        if (checkUpdatesBtn) {
            checkUpdatesBtn.addEventListener('click', () => {
                this.checkForUpdates()
            })
        }
    },

    // 加载版本检查设置
    async loadVersionSettings() {
        try {
            const { ipcRenderer } = require('electron')
            const result = await ipcRenderer.invoke('get-version-info')
            if (result.success) {
                const { data } = result
                const preferences = data.userPreferences || {}

                // 更新UI
                const autoCheckUpdates = document.getElementById('auto-check-updates')
                const checkInterval = document.getElementById('check-interval')
                const showOptionalUpdates = document.getElementById('show-optional-updates')
                const lastCheckTime = document.getElementById('last-check-time')

                if (autoCheckUpdates) {
                    autoCheckUpdates.checked = preferences.autoCheck !== false
                }

                if (checkInterval) {
                    checkInterval.value = preferences.checkInterval || 14400000 // 4小时
                }

                if (showOptionalUpdates) {
                    showOptionalUpdates.checked = preferences.showOptionalUpdates !== false
                }

                if (lastCheckTime) {
                    if (data.lastCheckTime && data.lastCheckTime > 0) {
                        const lastCheck = new Date(data.lastCheckTime)
                        lastCheckTime.textContent = `上次检查: ${lastCheck.toLocaleString()}`
                    } else {
                        lastCheckTime.textContent = '从未检查'
                    }
                }

                // 更新应用版本显示（仅设置页面）
                const versionElement = document.getElementById('app-version')
                if (versionElement && data.currentVersion) {
                    versionElement.textContent = `v${data.currentVersion}`
                }
            }
        } catch (error) {
            console.error('加载版本设置失败:', error)
        }
    },

    // 更新版本检查偏好设置
    async updateVersionPreference(key, value) {
        try {
            const { ipcRenderer } = require('electron')
            const preferences = {}
            preferences[key] = value

            const result = await ipcRenderer.invoke('update-version-preferences', preferences)
            if (result.success) {
                UI.showNotification('设置已保存', 'success')
            } else {
                this.showDetailedError('保存设置失败', result.error)
            }
        } catch (error) {
            console.error('更新版本偏好设置失败:', error)
            this.showDetailedError('保存设置失败', error.message)
        }
    },

    // 手动检查更新
    async checkForUpdates() {
        const checkUpdatesBtn = document.getElementById('check-updates-btn')
        const btnText = checkUpdatesBtn.querySelector('.btn-text')
        const originalText = btnText.textContent

        try {
            // 更新按钮状态
            checkUpdatesBtn.disabled = true
            btnText.textContent = '检查中...'

            const { ipcRenderer } = require('electron')
            const result = await ipcRenderer.invoke('check-for-updates')
            if (result.success) {
                // 更新最后检查时间
                const lastCheckTime = document.getElementById('last-check-time')
                if (lastCheckTime) {
                    lastCheckTime.textContent = `上次检查: ${new Date().toLocaleString()}`
                }

                // 显示检查结果
                if (result.data && result.data.hasUpdate) {
                    UI.showNotification(`发现新版本 v${result.data.latestVersion}`, 'info')
                } else {
                    UI.showNotification('已是最新版本', 'success')
                }
            } else {
                this.showDetailedError('检查更新失败', result.error)
            }
        } catch (error) {
            console.error('检查更新失败:', error)
            this.showDetailedError('检查更新失败', error.message)
        } finally {
            // 恢复按钮状态
            checkUpdatesBtn.disabled = false
            btnText.textContent = originalText
        }
    },

    /**
     * 显示详细错误信息
     * @param {string} title 错误标题
     * @param {string} message 错误消息
     */
    showDetailedError(title, message) {
        // 根据错误消息提供更友好的提示
        let userFriendlyMessage = message;
        let suggestions = [];

        if (message.includes('ENOTFOUND') || message.includes('DNS')) {
            userFriendlyMessage = '无法连接到更新服务器';
            suggestions = [
                '检查网络连接是否正常',
                '确认防火墙没有阻止应用访问网络',
                '尝试稍后重试'
            ];
        } else if (message.includes('ETIMEDOUT') || message.includes('timeout')) {
            userFriendlyMessage = '连接超时';
            suggestions = [
                '检查网络连接速度',
                '尝试稍后重试',
                '如果问题持续，请联系技术支持'
            ];
        } else if (message.includes('ECONNREFUSED')) {
            userFriendlyMessage = '服务器拒绝连接';
            suggestions = [
                '服务器可能正在维护',
                '请稍后重试',
                '检查应用是否为最新版本'
            ];
        } else if (message.includes('404')) {
            userFriendlyMessage = '更新服务暂时不可用';
            suggestions = [
                '服务可能正在更新',
                '请稍后重试'
            ];
        } else if (message.includes('500') || message.includes('502') || message.includes('503')) {
            userFriendlyMessage = '服务器暂时出现问题';
            suggestions = [
                '服务器正在处理问题',
                '请稍后重试',
                '如果问题持续，请联系技术支持'
            ];
        }

        // 构建模态框内容
        const modal = {
            title: title,
            content: `
                <div class="error-details">
                    <div class="error-message">
                        <p><strong>错误信息：</strong>${userFriendlyMessage}</p>
                    </div>
                    ${suggestions.length > 0 ? `
                        <div class="error-suggestions">
                            <p><strong>建议解决方案：</strong></p>
                            <ul>
                                ${suggestions.map(s => `<li>${s}</li>`).join('')}
                            </ul>
                        </div>
                    ` : ''}
                    <div class="error-technical">
                        <details>
                            <summary>技术详情</summary>
                            <pre>${message}</pre>
                        </details>
                    </div>
                </div>
            `,
            actions: [
                {
                    text: '确定',
                    type: 'primary',
                    handler: () => UI.hideModal()
                },
                {
                    text: '重试',
                    type: 'secondary',
                    handler: () => {
                        UI.hideModal();
                        this.checkForUpdates();
                    }
                }
            ]
        };

        UI.showModal(modal);
    }
}

// 导出模块
window.SettingsManager = SettingsManager
