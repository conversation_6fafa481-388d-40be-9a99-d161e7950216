package org.jeecg.modules.jianying.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

/**
 * 获取音频时长请求参数
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class GetAudioDurationRequest extends BaseJianyingRequest {
    
    @ApiModelProperty(value = "音频链接（必填）", required = true,
                     example = "https://example.com/audio.mp3")
    @NotBlank(message = "mp3_url不能为空")
    @JsonProperty("mp3_url")
    private String zjMp3Url;
    
    @Override
    public String getSummary() {
        return "GetAudioDurationRequest{" +
               "accessKey=" + (getAccessKey() != null && getAccessKey().length() > 10 ? 
                              getAccessKey().substring(0, 10) + "***" : "null") +
               ", mp3Url=" + (zjMp3Url != null && zjMp3Url.length() > 30 ? 
                             zjMp3Url.substring(0, 30) + "***" : zjMp3Url) +
               "}";
    }
}
