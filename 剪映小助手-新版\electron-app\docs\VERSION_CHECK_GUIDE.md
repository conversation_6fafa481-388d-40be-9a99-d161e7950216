# 版本检查功能使用指南

## 📋 目录

- [功能概述](#功能概述)
- [快速开始](#快速开始)
- [配置选项](#配置选项)
- [用户界面](#用户界面)
- [API接口](#api接口)
- [故障排除](#故障排除)
- [开发指南](#开发指南)

## 🎯 功能概述

版本检查功能为剪映小助手提供了自动版本更新检查和管理能力，包括：

- **自动版本检查**：应用启动时自动检查最新版本
- **强制更新**：支持安全更新等重要版本的强制更新
- **可选更新**：用户可选择是否更新的常规版本
- **后台检查**：定期在后台检查版本更新
- **缓存机制**：减少不必要的网络请求
- **错误处理**：完善的网络异常和错误处理
- **日志记录**：详细的操作日志便于调试

## 🚀 快速开始

### 1. 基本使用

版本检查功能已集成到应用中，无需额外配置即可使用：

```javascript
// 应用启动时自动检查
const updateManager = new UpdateManager({
  apiBaseUrl: 'https://aigcview.com/jeecg-boot',
  programType: 'desktop'
});

// 启动时检查
const canContinue = await updateManager.checkOnStartup();
```

### 2. 手动检查更新

在应用设置页面点击"检查更新"按钮，或通过代码调用：

```javascript
// 手动检查更新
await updateManager.checkForUpdates(true);
```

## ⚙️ 配置选项

### UpdateManager 配置

```javascript
const updateManager = new UpdateManager({
  // API服务器地址
  apiBaseUrl: 'https://aigcview.com/jeecg-boot',

  // 程序类型
  programType: 'desktop',

  // 是否启用后台检查
  enableBackgroundCheck: true,

  // 后台检查间隔（毫秒）
  backgroundCheckInterval: 4 * 60 * 60 * 1000, // 4小时

  // 是否显示可选更新
  showOptionalUpdates: true
});
```

### VersionChecker 配置

```javascript
const versionChecker = new VersionChecker({
  // API基础URL
  apiBaseUrl: 'https://aigcview.com/jeecg-boot',

  // 程序类型
  programType: 'desktop',

  // 检查间隔
  checkInterval: 24 * 60 * 60 * 1000, // 24小时

  // 最大重试次数
  maxRetries: 3,

  // 请求超时时间
  requestTimeout: 10000, // 10秒

  // 是否启用日志
  enableLogging: true
});
```

### 用户偏好设置

用户可以在设置页面配置以下选项：

- **自动检查更新**：控制后台定期检查更新（注意：应用启动时总是会检查一次）
- **检查频率**：后台检查的时间间隔（仅在启用自动检查时生效）
- **显示可选更新**：是否显示非强制性更新提示

### 🔄 检查更新的两种模式

#### 1. 启动时检查（总是执行）
- **触发时机**：每次应用启动时
- **用户控制**：无法关闭（确保用户及时获得重要更新）
- **检查方式**：强制检查，忽略缓存
- **目的**：确保用户不会错过重要的安全更新或功能更新

#### 2. 后台定期检查（可选）
- **触发时机**：应用运行期间定期检查
- **用户控制**：可通过"自动检查更新"开关控制
- **检查方式**：使用缓存，减少网络请求
- **目的**：在应用使用过程中及时发现更新

## 🖥️ 用户界面

### 设置页面

在应用设置页面的"版本更新"部分包含：

1. **自动检查更新开关**
2. **检查频率选择器**
3. **显示可选更新开关**
4. **手动检查更新按钮**
5. **最后检查时间显示**

### 更新提示窗口

#### 强制更新窗口
- 全屏模态窗口
- 显示版本信息和更新内容
- 只有"立即更新"和"退出应用"选项

#### 可选更新窗口
- 普通模态窗口
- 提供"立即更新"、"稍后提醒"、"跳过此版本"选项

## 🔌 API接口

### 后端接口

#### 获取最新版本
```
GET /jeecg-boot/aigcview/versioncontrol/getLatest?programType=desktop
```

响应格式：
```json
{
  "success": true,
  "result": {
    "id": "1",
    "programType": "desktop",
    "versionNumber": "1.0.1",
    "updateContent": "更新内容说明",
    "downloadUrl": "https://aigcview.com/download/desktop/v1.0.1",
    "releaseDate": "2025-01-09T10:00:00Z",
    "isLatest": 1,
    "status": 1
  }
}
```

### IPC接口

#### 手动检查更新
```javascript
const result = await ipcRenderer.invoke('check-for-updates');
```

#### 获取版本信息
```javascript
const info = await ipcRenderer.invoke('get-version-info');
```

#### 更新用户偏好
```javascript
const result = await ipcRenderer.invoke('update-version-preferences', preferences);
```

#### 获取日志
```javascript
const logs = await ipcRenderer.invoke('get-version-logs', limit, level);
```

## 🔧 故障排除

### 常见问题

#### 1. 无法连接到更新服务器

**症状**：显示"无法连接到更新服务器"错误

**可能原因**：
- 网络连接问题
- 防火墙阻止
- DNS解析失败

**解决方案**：
1. 检查网络连接
2. 确认防火墙设置
3. 尝试使用VPN或更换网络
4. 检查DNS设置

#### 2. 连接超时

**症状**：显示"连接超时"错误

**可能原因**：
- 网络速度慢
- 服务器响应慢
- 请求超时设置过短

**解决方案**：
1. 检查网络速度
2. 稍后重试
3. 联系技术支持

#### 3. 服务器错误

**症状**：显示"服务器暂时出现问题"

**可能原因**：
- 服务器维护
- 服务器故障
- API接口问题

**解决方案**：
1. 稍后重试
2. 查看官方公告
3. 联系技术支持

### 调试方法

#### 1. 查看日志

```javascript
// 获取版本检查日志
const logs = await ipcRenderer.invoke('get-version-logs', 100);
console.log('版本检查日志:', logs);
```

#### 2. 手动测试API

```bash
# 测试API接口
curl "https://aigcview.com/jeecg-boot/aigcview/versioncontrol/getLatest?programType=desktop"
```

#### 3. 清除缓存

```javascript
// 清除版本检查缓存
versionChecker.clearCache();
```

## 👨‍💻 开发指南

### 添加新的程序类型

1. 在数据库中添加新的版本记录
2. 更新程序类型字典
3. 修改前端下拉选项

### 自定义强制更新逻辑

```javascript
// 在VersionChecker中修改shouldForceUpdate方法
shouldForceUpdate(versionInfo) {
  const updateContent = versionInfo.updateContent || '';
  
  // 自定义强制更新条件
  const forceKeywords = ['安全', '紧急', '重要', '修复严重'];
  return forceKeywords.some(keyword => updateContent.includes(keyword));
}
```

### 扩展错误处理

```javascript
// 在handleCheckError方法中添加新的错误类型
if (error.code === 'CUSTOM_ERROR') {
  errorType = 'custom_error';
  userMessage = '自定义错误消息';
}
```

### 测试

#### 运行单元测试
```bash
node test/version-check-suite.js
```

#### 运行集成测试
```bash
node test/mock-server.js
```

#### 手动测试
```bash
node test-version-check.js
```

## 📝 更新日志

### v1.0.0 (2025-01-09)
- 初始版本发布
- 支持自动版本检查
- 支持强制更新和可选更新
- 完善的错误处理和日志记录

## 🤝 技术支持

如果遇到问题，请：

1. 查看本文档的故障排除部分
2. 检查应用日志
3. 联系技术支持：<EMAIL>
4. 访问官网：https://www.aigcview.com

## 📦 部署指南

### 后端部署

#### 1. 数据库配置

确保数据库中存在版本控制表：

```sql
-- 版本控制表结构
CREATE TABLE `aigc_version_control` (
  `id` varchar(36) NOT NULL COMMENT '主键ID',
  `program_type` varchar(50) NOT NULL COMMENT '程序类型',
  `version_number` varchar(20) NOT NULL COMMENT '版本号',
  `update_content` text COMMENT '更新内容',
  `download_url` varchar(500) COMMENT '下载链接',
  `release_date` datetime COMMENT '发布日期',
  `is_latest` tinyint(1) DEFAULT '2' COMMENT '是否最新版本：1是，2否',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态：1启用，2禁用',
  `create_by` varchar(50) COMMENT '创建人',
  `create_time` datetime COMMENT '创建时间',
  `update_by` varchar(50) COMMENT '更新人',
  `update_time` datetime COMMENT '更新时间',
  `sys_org_code` varchar(64) COMMENT '组织机构编码',
  PRIMARY KEY (`id`),
  KEY `idx_program_type` (`program_type`),
  KEY `idx_version_number` (`version_number`),
  KEY `idx_is_latest` (`is_latest`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='程序版本控制表';
```

#### 2. API接口暴露

在Shiro配置中添加匿名访问权限：

```java
// ShiroConfig.java
filterChainDefinitionMap.put("/jeecg-boot/aigcview/versioncontrol/list", "anon");
filterChainDefinitionMap.put("/jeecg-boot/aigcview/versioncontrol/getLatest", "anon");
filterChainDefinitionMap.put("/jeecg-boot/aigcview/versioncontrol/getVersionsByType", "anon");
```

#### 3. 初始数据

插入初始版本数据：

```sql
INSERT INTO `aigc_version_control` VALUES
('1', 'desktop', '1.0.0', '初始版本发布', 'https://aigcview.com/download/desktop/v1.0.0', NOW(), 1, 1, 'system', NOW(), NULL, NULL, NULL);
```

### 前端部署

#### 1. 依赖安装

确保安装了必要的依赖：

```bash
npm install axios electron-store
```

#### 2. 文件结构

```
src/
├── utils/
│   ├── VersionChecker.js     # 版本检查核心类
│   └── UpdateManager.js      # 更新管理器
├── windows/
│   └── UpdateWindow.js       # 更新窗口管理
├── pages/
│   ├── force-update.html     # 强制更新页面
│   └── optional-update.html  # 可选更新页面
└── main/
    └── main.js              # 主进程（已集成）
```

#### 3. 主进程集成

在main.js中已完成集成，包括：
- UpdateManager初始化
- 启动时版本检查
- IPC事件处理
- 应用退出清理

### 配置管理

#### 1. 环境配置

```javascript
// 生产环境配置
const updateManager = new UpdateManager({
  apiBaseUrl: 'https://aigcview.com/jeecg-boot',
  programType: 'desktop',
  enableBackgroundCheck: true,
  backgroundCheckInterval: 4 * 60 * 60 * 1000
});

// 开发环境配置
const updateManager = new UpdateManager({
  apiBaseUrl: 'http://localhost:8080/jeecg-boot',
  programType: 'desktop',
  enableBackgroundCheck: false
});
```

#### 2. 版本号管理

确保package.json中的版本号与发布版本一致：

```json
{
  "name": "jianying-assistant",
  "version": "1.0.0"
}
```

---

*本文档由智界AigcView技术团队维护*
