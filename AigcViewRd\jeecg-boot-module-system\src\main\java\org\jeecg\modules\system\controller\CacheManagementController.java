package org.jeecg.modules.system.controller;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.constant.CacheConstant;
import org.jeecg.common.constant.WebsocketConst;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.modules.message.websocket.WebSocket;
import org.jeecg.modules.system.entity.SysDict;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/**
 * 缓存管理控制器
 * 用于管理系统缓存，特别是字典缓存
 */
@RestController
@RequestMapping("/sys/cache")
@Slf4j
public class CacheManagementController extends Je<PERSON>g<PERSON>ontroller<SysDict, org.jeecg.modules.system.service.ISysDictService> {

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private WebSocket webSocket;

    /**
     * 清除所有字典缓存
     */
    @PostMapping("/clearAllDictCache")
    public Result<?> clearAllDictCache() {
        try {
            int clearedCount = 0;
            
            // 1. 清除Redis中的普通字典缓存
            Set<String> dictKeys = redisTemplate.keys(CacheConstant.SYS_DICT_CACHE + "*");
            if (dictKeys != null && !dictKeys.isEmpty()) {
                redisTemplate.delete(dictKeys);
                clearedCount += dictKeys.size();
                log.info("清除普通字典缓存 {} 个", dictKeys.size());
            }
            
            // 2. 清除启用字典缓存
            Set<String> enableDictKeys = redisTemplate.keys(CacheConstant.SYS_ENABLE_DICT_CACHE + "*");
            if (enableDictKeys != null && !enableDictKeys.isEmpty()) {
                redisTemplate.delete(enableDictKeys);
                clearedCount += enableDictKeys.size();
                log.info("清除启用字典缓存 {} 个", enableDictKeys.size());
            }
            
            // 3. 通知所有在线客户端清除前端缓存
            Map<String, Object> message = new HashMap<>();
            message.put("type", "CLEAR_DICT_CACHE");
            message.put("timestamp", System.currentTimeMillis());
            LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            message.put("operator", loginUser != null ? loginUser.getUsername() : "system");
            
            // 发送WebSocket消息（如果有WebSocket服务）
            try {
                sendWebSocketMessage(JSON.toJSONString(message));
            } catch (Exception e) {
                log.warn("发送WebSocket消息失败，但缓存清除成功: {}", e.getMessage());
            }
            
            log.info("管理员 {} 清除了所有字典缓存，共清除 {} 个缓存项",
                loginUser != null ? loginUser.getUsername() : "system", clearedCount);
            
            return Result.OK("缓存清除成功！共清除 " + clearedCount + " 个缓存项，已通知所有客户端刷新");
            
        } catch (Exception e) {
            log.error("清除字典缓存失败", e);
            return Result.error("清除缓存失败: " + e.getMessage());
        }
    }

    /**
     * 清除指定字典缓存
     */
    @PostMapping("/clearSpecificDictCache")
    public Result<?> clearSpecificDictCache(@RequestParam String dictCode) {
        try {
            int clearedCount = 0;
            
            // 清除指定字典的缓存
            String dictCacheKey = CacheConstant.SYS_DICT_CACHE + "::" + dictCode;
            String enableDictCacheKey = CacheConstant.SYS_ENABLE_DICT_CACHE + "::" + dictCode;
            
            if (redisTemplate.hasKey(dictCacheKey)) {
                redisTemplate.delete(dictCacheKey);
                clearedCount++;
            }
            
            if (redisTemplate.hasKey(enableDictCacheKey)) {
                redisTemplate.delete(enableDictCacheKey);
                clearedCount++;
            }
            
            // 通知客户端清除指定字典缓存
            Map<String, Object> message = new HashMap<>();
            message.put("type", "CLEAR_SPECIFIC_DICT_CACHE");
            message.put("dictCode", dictCode);
            message.put("timestamp", System.currentTimeMillis());
            LoginUser loginUser2 = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            message.put("operator", loginUser2 != null ? loginUser2.getUsername() : "system");

            try {
                sendWebSocketMessage(JSON.toJSONString(message));
            } catch (Exception e) {
                log.warn("发送WebSocket消息失败，但缓存清除成功: {}", e.getMessage());
            }

            log.info("管理员 {} 清除了字典 {} 的缓存，共清除 {} 个缓存项",
                loginUser2 != null ? loginUser2.getUsername() : "system", dictCode, clearedCount);
            
            return Result.OK("字典 " + dictCode + " 缓存清除成功！共清除 " + clearedCount + " 个缓存项");
            
        } catch (Exception e) {
            log.error("清除指定字典缓存失败", e);
            return Result.error("清除缓存失败: " + e.getMessage());
        }
    }

    /**
     * 获取缓存统计信息
     */
    @GetMapping("/getCacheStats")
    public Result<?> getCacheStats() {
        try {
            Map<String, Object> stats = new HashMap<>();
            
            // 统计字典缓存数量
            Set<String> dictKeys = redisTemplate.keys(CacheConstant.SYS_DICT_CACHE + "*");
            Set<String> enableDictKeys = redisTemplate.keys(CacheConstant.SYS_ENABLE_DICT_CACHE + "*");
            
            stats.put("dictCacheCount", dictKeys != null ? dictKeys.size() : 0);
            stats.put("enableDictCacheCount", enableDictKeys != null ? enableDictKeys.size() : 0);
            stats.put("totalCacheCount", 
                (dictKeys != null ? dictKeys.size() : 0) + 
                (enableDictKeys != null ? enableDictKeys.size() : 0));
            
            return Result.OK(stats);
            
        } catch (Exception e) {
            log.error("获取缓存统计信息失败", e);
            return Result.error("获取缓存统计失败: " + e.getMessage());
        }
    }

    /**
     * 🔥 发送WebSocket消息给所有在线用户
     * 通知前端清除缓存
     */
    private void sendWebSocketMessage(String message) {
        try {
            // 构造WebSocket消息格式
            Map<String, Object> wsMessage = new HashMap<>();
            wsMessage.put(WebsocketConst.MSG_CMD, WebsocketConst.CMD_TOPIC);
            wsMessage.put(WebsocketConst.MSG_ID, "CACHE_CLEAR");
            wsMessage.put(WebsocketConst.MSG_TXT, message);

            // 发送给所有在线用户
            webSocket.sendMessage(JSON.toJSONString(wsMessage));

            log.info("🔔 WebSocket消息已发送给所有在线用户: {}", message);
        } catch (Exception e) {
            log.error("❌ WebSocket消息发送失败: {}", e.getMessage(), e);
        }
    }
}
