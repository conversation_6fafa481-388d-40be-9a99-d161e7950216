package org.jeecg.modules.system.entity;

/**
 * 验证码发送结果类
 * 用于返回详细的验证码发送结果和错误信息
 * 
 * <AUTHOR>
 * @date 2025-06-23
 */
public class VerifyCodeResult {
    
    /**
     * 是否成功
     */
    private boolean success;
    
    /**
     * 返回消息
     */
    private String message;
    
    /**
     * 错误类型
     */
    private String errorType;
    
    /**
     * 默认构造方法
     */
    public VerifyCodeResult() {
    }
    
    /**
     * 构造方法
     * 
     * @param success 是否成功
     * @param message 消息
     * @param errorType 错误类型
     */
    public VerifyCodeResult(boolean success, String message, String errorType) {
        this.success = success;
        this.message = message;
        this.errorType = errorType;
    }
    
    /**
     * 创建成功结果
     * 
     * @param message 成功消息
     * @return VerifyCodeResult
     */
    public static VerifyCodeResult success(String message) {
        return new VerifyCodeResult(true, message, null);
    }
    
    /**
     * 创建失败结果
     * 
     * @param errorType 错误类型
     * @param message 错误消息
     * @return VerifyCodeResult
     */
    public static VerifyCodeResult error(String errorType, String message) {
        return new VerifyCodeResult(false, message, errorType);
    }
    
    /**
     * 创建失败结果（无错误类型）
     * 
     * @param message 错误消息
     * @return VerifyCodeResult
     */
    public static VerifyCodeResult error(String message) {
        return new VerifyCodeResult(false, message, "UNKNOWN");
    }
    
    // Getter 和 Setter 方法
    
    public boolean isSuccess() {
        return success;
    }
    
    public void setSuccess(boolean success) {
        this.success = success;
    }
    
    public String getMessage() {
        return message;
    }
    
    public void setMessage(String message) {
        this.message = message;
    }
    
    public String getErrorType() {
        return errorType;
    }
    
    public void setErrorType(String errorType) {
        this.errorType = errorType;
    }
    
    @Override
    public String toString() {
        return "VerifyCodeResult{" +
                "success=" + success +
                ", message='" + message + '\'' +
                ", errorType='" + errorType + '\'' +
                '}';
    }
}
