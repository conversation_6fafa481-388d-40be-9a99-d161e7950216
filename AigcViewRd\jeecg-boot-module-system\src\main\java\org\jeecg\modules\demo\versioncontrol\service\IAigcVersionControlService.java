package org.jeecg.modules.demo.versioncontrol.service;

import java.util.List;
import java.util.Map;
import org.jeecg.modules.demo.versioncontrol.entity.AigcVersionControl;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * @Description: 程序版本控制表
 * @Author: jeecg-boot
 * @Date: 2025-01-09
 * @Version: V1.0
 */
public interface IAigcVersionControlService extends IService<AigcVersionControl> {

    /**
     * 根据程序类型查询最新版本
     * @param programType 程序类型
     * @return 最新版本信息
     */
    AigcVersionControl getLatestByProgramType(String programType);

    /**
     * 根据程序类型查询所有版本（按发布日期倒序）
     * @param programType 程序类型
     * @return 版本列表
     */
    List<AigcVersionControl> getVersionsByProgramType(String programType);

    /**
     * 根据程序类型和版本号查询版本信息
     * @param programType 程序类型
     * @param versionNumber 版本号
     * @return 版本信息
     */
    AigcVersionControl getByProgramTypeAndVersion(String programType, String versionNumber);

    /**
     * 设置为最新版本（会自动将同类型的其他版本设为非最新）
     * @param id 版本ID
     * @param updateBy 更新人
     * @return 操作结果
     */
    boolean setAsLatest(String id, String updateBy);

    /**
     * 批量更新状态
     * @param ids 版本ID列表
     * @param status 状态
     * @param updateBy 更新人
     * @return 操作结果
     */
    boolean batchUpdateStatus(List<String> ids, Integer status, String updateBy);

    /**
     * 查询所有程序类型
     * @return 程序类型列表
     */
    List<String> getAllProgramTypes();

    /**
     * 统计各程序类型的版本数量
     * @return 统计结果
     */
    List<Map<String, Object>> countByProgramType();

    /**
     * 查询最近发布的版本（限制数量）
     * @param limit 限制数量
     * @return 最近版本列表
     */
    List<AigcVersionControl> getRecentVersions(Integer limit);

    /**
     * 检查版本号是否已存在（同一程序类型下）
     * @param programType 程序类型
     * @param versionNumber 版本号
     * @param excludeId 排除的ID（用于编辑时排除自己）
     * @return 是否存在
     */
    boolean checkVersionExists(String programType, String versionNumber, String excludeId);

    /**
     * 根据发布日期范围查询版本
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 版本列表
     */
    List<AigcVersionControl> getVersionsByDateRange(String startDate, String endDate);

    /**
     * 搜索版本（支持程序类型、版本号、更新内容模糊查询）
     * @param keyword 关键词
     * @return 版本列表
     */
    List<AigcVersionControl> searchVersions(String keyword);

    /**
     * 新增版本（带业务逻辑验证）
     * @param versionControl 版本信息
     * @param createBy 创建人
     * @return 操作结果
     */
    boolean addVersion(AigcVersionControl versionControl, String createBy);

    /**
     * 更新版本（带业务逻辑验证）
     * @param versionControl 版本信息
     * @param updateBy 更新人
     * @return 操作结果
     */
    boolean updateVersion(AigcVersionControl versionControl, String updateBy);

    /**
     * 删除版本（带业务逻辑验证）
     * @param id 版本ID
     * @return 操作结果
     */
    boolean deleteVersion(String id);

    /**
     * 批量删除版本
     * @param ids 版本ID列表
     * @return 操作结果
     */
    boolean batchDeleteVersions(List<String> ids);

    /**
     * 获取版本历史对比信息
     * @param programType 程序类型
     * @param fromVersion 起始版本
     * @param toVersion 目标版本
     * @return 对比信息
     */
    Map<String, Object> getVersionComparison(String programType, String fromVersion, String toVersion);

    /**
     * 获取程序类型的版本统计信息
     * @param programType 程序类型
     * @return 统计信息
     */
    Map<String, Object> getProgramTypeStatistics(String programType);

    /**
     * 导出版本信息
     * @param programType 程序类型（可选）
     * @return 导出数据
     */
    List<AigcVersionControl> exportVersions(String programType);

    /**
     * 验证版本号格式
     * @param versionNumber 版本号
     * @return 是否有效
     */
    boolean validateVersionNumber(String versionNumber);

    /**
     * 获取下一个建议版本号
     * @param programType 程序类型
     * @param versionType 版本类型：major-主版本，minor-次版本，patch-补丁版本
     * @return 建议版本号
     */
    String getNextSuggestedVersion(String programType, String versionType);
}
