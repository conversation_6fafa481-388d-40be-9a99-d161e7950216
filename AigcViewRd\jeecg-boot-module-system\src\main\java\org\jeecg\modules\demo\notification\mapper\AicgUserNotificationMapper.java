package org.jeecg.modules.demo.notification.mapper;

import java.util.List;
import java.util.Map;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.jeecg.modules.demo.notification.entity.AicgUserNotification;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: 用户通知消息表
 * @Author: jeecg-boot
 * @Date:   2025-06-18
 * @Version: V1.0
 */
public interface AicgUserNotificationMapper extends BaseMapper<AicgUserNotification> {

    /**
     * 根据用户ID查询通知消息
     * @param userId 用户ID
     * @return 通知消息列表
     */
    @Select("SELECT * FROM aicg_user_notification WHERE user_id = #{userId} ORDER BY create_time DESC")
    List<AicgUserNotification> getByUserId(@Param("userId") String userId);
    
    /**
     * 根据用户ID查询未读通知
     * @param userId 用户ID
     * @return 未读通知列表
     */
    @Select("SELECT * FROM aicg_user_notification WHERE user_id = #{userId} AND is_read = 0 ORDER BY create_time DESC")
    List<AicgUserNotification> getUnreadByUserId(@Param("userId") String userId);
    
    /**
     * 统计用户通知数据
     * @param userId 用户ID
     * @return 通知统计数据
     */
    @Select("SELECT " +
            "COUNT(*) as total_notifications, " +
            "COUNT(CASE WHEN is_read = 0 THEN 1 END) as unread_count, " +
            "COUNT(CASE WHEN is_read = 1 THEN 1 END) as read_count, " +
            "COUNT(CASE WHEN type = 1 THEN 1 END) as system_count, " +
            "COUNT(CASE WHEN type = 2 THEN 1 END) as transaction_count, " +
            "COUNT(CASE WHEN type = 3 THEN 1 END) as security_count, " +
            "COUNT(CASE WHEN type = 4 THEN 1 END) as marketing_count " +
            "FROM aicg_user_notification WHERE user_id = #{userId}")
    Map<String, Object> getNotificationStats(@Param("userId") String userId);
    
    /**
     * 根据类型查询通知
     * @param userId 用户ID
     * @param type 通知类型
     * @return 通知列表
     */
    @Select("SELECT * FROM aicg_user_notification WHERE user_id = #{userId} AND type = #{type} ORDER BY create_time DESC")
    List<AicgUserNotification> getByUserIdAndType(@Param("userId") String userId, @Param("type") Integer type);
    
    /**
     * 查询即将过期的通知
     * @return 即将过期的通知列表
     */
    @Select("SELECT * FROM aicg_user_notification WHERE expire_time IS NOT NULL AND expire_time <= DATE_ADD(NOW(), INTERVAL 1 DAY) AND expire_time > NOW()")
    List<AicgUserNotification> getExpiringNotifications();
    
    /**
     * 查询已过期的通知
     * @return 已过期的通知列表
     */
    @Select("SELECT * FROM aicg_user_notification WHERE expire_time IS NOT NULL AND expire_time <= NOW()")
    List<AicgUserNotification> getExpiredNotifications();
    
    /**
     * 批量标记为已读
     * @param userId 用户ID
     * @param ids 通知ID列表
     * @return 更新的记录数
     */
    @Update("<script>" +
            "UPDATE aicg_user_notification SET is_read = 1, read_time = NOW() " +
            "WHERE user_id = #{userId} AND id IN " +
            "<foreach collection='ids' item='id' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach>" +
            "</script>")
    int batchMarkAsRead(@Param("userId") String userId, @Param("ids") List<String> ids);
    
    /**
     * 标记所有未读通知为已读
     * @param userId 用户ID
     * @return 更新的记录数
     */
    @Update("UPDATE aicg_user_notification SET is_read = 1, read_time = NOW() WHERE user_id = #{userId} AND is_read = 0")
    int markAllAsRead(@Param("userId") String userId);
    
    /**
     * 删除过期通知
     * @return 删除的记录数
     */
    @Update("DELETE FROM aicg_user_notification WHERE expire_time IS NOT NULL AND expire_time <= NOW()")
    int deleteExpiredNotifications();
}
