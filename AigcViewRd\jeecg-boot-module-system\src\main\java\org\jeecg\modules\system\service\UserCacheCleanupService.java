package org.jeecg.modules.system.service;

import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.constant.CacheConstant;
import org.jeecg.common.constant.CommonConstant;
import org.jeecg.common.util.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

/**
 * @Description: 用户缓存清理服务
 * @Author: AigcView
 * @Date: 2025-07-14
 * @Version: V1.0
 * 
 * 统一处理用户退出登录时的缓存清理，确保完整清理所有相关缓存数据，
 * 解决退出登录后重新登录仍出现设备冲突的问题。
 */
@Slf4j
@Service
public class UserCacheCleanupService {
    
    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    /**
     * 清理用户所有登录相关缓存（完整版）
     * @param userId 用户ID
     * @param token 当前Token
     * @param username 用户名
     */
    public void cleanupUserLoginCache(String userId, String token, String username) {
        try {
            log.info("🧹 开始清理用户登录缓存，用户：{}", username);
            
            // 1. 清理基础Token缓存
            cleanupBasicTokenCache(userId, token, username);
            
            // 2. 清理单设备登录相关缓存（包含kicked_token清理）
            cleanupSingleLoginCache(userId, token, username);
            
            log.info("✅ 用户 {} 登录缓存清理完成", username);
            
        } catch (Exception e) {
            log.error("❌ 清理用户登录缓存失败，用户：{}，错误：{}", username, e.getMessage(), e);
        }
    }
    
    /**
     * 🆕 原子性清理基础Token缓存（使用Redis批量删除）
     */
    private void cleanupBasicTokenCache(String userId, String token, String username) {
        try {
            // 准备要删除的键列表
            String tokenKey = CommonConstant.PREFIX_USER_TOKEN + token;
            String shiroKey = CommonConstant.PREFIX_USER_SHIRO_CACHE + userId;
            String userCacheKey = String.format("%s::%s", CacheConstant.SYS_USERS_CACHE, username);

            // 🆕 原子性批量删除
            List<String> keysToDelete = Arrays.asList(tokenKey, shiroKey, userCacheKey);
            Long deletedCount = redisTemplate.delete(keysToDelete);

            log.debug("🗑️ 已原子性清理基础缓存，删除了 {} 个键", deletedCount);
            log.debug("   - Token缓存: {}", tokenKey);
            log.debug("   - Shiro权限缓存: {}", shiroKey);
            log.debug("   - 用户信息缓存: {}", userCacheKey);

        } catch (Exception e) {
            log.error("清理基础Token缓存失败", e);
        }
    }
    
    /**
     * 🆕 原子性清理单设备登录相关缓存（使用Redis批量删除）
     */
    private void cleanupSingleLoginCache(String userId, String token, String username) {
        try {
            // 准备要删除的键列表
            String currentTokenKey = "current_user_token_" + userId;
            String deviceKey = "token_device_" + userId;
            String kickedKey = "kicked_token_" + token;

            // 🆕 原子性批量删除
            List<String> keysToDelete = Arrays.asList(currentTokenKey, deviceKey, kickedKey);
            Long deletedCount = redisTemplate.delete(keysToDelete);

            log.debug("🗑️ 已原子性清理单设备登录缓存，删除了 {} 个键", deletedCount);
            log.debug("   - 当前用户Token记录: {}", currentTokenKey);
            log.debug("   - 设备信息记录: {}", deviceKey);
            log.debug("   - kicked_token记录: {}", kickedKey);

        } catch (Exception e) {
            log.error("清理单设备登录缓存失败", e);
        }
    }
    

    
    /**
     * 快速清理用户缓存（仅提供用户ID）
     * @param userId 用户ID
     */
    public void quickCleanupUserCache(String userId) {
        try {
            log.info("🧹 快速清理用户缓存，用户ID：{}", userId);
            
            // 清理单设备登录缓存
            redisUtil.del("current_user_token_" + userId);
            redisUtil.del("token_device_" + userId);
            
            // 清理Shiro权限缓存
            redisUtil.del(CommonConstant.PREFIX_USER_SHIRO_CACHE + userId);
            
            log.info("✅ 用户ID {} 快速缓存清理完成", userId);
            
        } catch (Exception e) {
            log.error("❌ 快速清理用户缓存失败，用户ID：{}，错误：{}", userId, e.getMessage(), e);
        }
    }
    
    /**
     * 检查用户是否还有残留的登录状态缓存
     * @param userId 用户ID
     * @param username 用户名
     * @return true表示有残留缓存
     */
    public boolean hasRemainingLoginCache(String userId, String username) {
        try {
            // 检查关键缓存是否还存在
            boolean hasCurrentToken = redisUtil.hasKey("current_user_token_" + userId);
            boolean hasDeviceInfo = redisUtil.hasKey("token_device_" + userId);
            
            if (hasCurrentToken || hasDeviceInfo) {
                log.warn("⚠️ 用户 {} 仍有残留登录缓存，current_token: {}, device_info: {}", 
                    username, hasCurrentToken, hasDeviceInfo);
                return true;
            }
            
            return false;
            
        } catch (Exception e) {
            log.error("检查残留缓存失败", e);
            return false;
        }
    }
}
