{"version": 3, "file": "httpExecutor.js", "sourceRoot": "", "sources": ["../src/httpExecutor.ts"], "names": [], "mappings": ";;;AAAA,mCAA+D;AAC/D,iCAA0B;AAC1B,2BAAsC;AAGtC,mCAAkC;AAClC,6BAAyB;AACzB,2DAAuD;AACvD,mCAAkC;AAClC,2EAAqF;AAErF,MAAM,KAAK,GAAG,IAAA,eAAM,EAAC,kBAAkB,CAAC,CAAA;AAiBxC,SAAgB,eAAe,CAAC,QAAyB,EAAE,cAA0B,IAAI;IACvF,OAAO,IAAI,SAAS,CAClB,QAAQ,CAAC,UAAU,IAAI,CAAC,CAAC,EACzB,GAAG,QAAQ,CAAC,UAAU,IAAI,QAAQ,CAAC,aAAa,EAAE;QAChD,CAAC,WAAW,IAAI,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;QAC3E,aAAa;QACb,iBAAiB,CAAC,QAAQ,CAAC,OAAO,CAAC,EACrC,WAAW,CACZ,CAAA;AACH,CAAC;AATD,0CASC;AAED,MAAM,iBAAiB,GAAG,IAAI,GAAG,CAAiB;IAChD,CAAC,GAAG,EAAE,mBAAmB,CAAC;IAC1B,CAAC,GAAG,EAAE,aAAa,CAAC;IACpB,CAAC,GAAG,EAAE,WAAW,CAAC;IAClB,CAAC,GAAG,EAAE,WAAW,CAAC;IAClB,CAAC,GAAG,EAAE,oBAAoB,CAAC;IAC3B,CAAC,GAAG,EAAE,gBAAgB,CAAC;IACvB,CAAC,GAAG,EAAE,iBAAiB,CAAC;IACxB,CAAC,GAAG,EAAE,0BAA0B,CAAC;IACjC,CAAC,GAAG,EAAE,uBAAuB,CAAC;IAC9B,CAAC,GAAG,EAAE,aAAa,CAAC;IACpB,CAAC,GAAG,EAAE,qBAAqB,CAAC;IAC5B,CAAC,GAAG,EAAE,iBAAiB,CAAC;IACxB,CAAC,GAAG,EAAE,4BAA4B,CAAC;CACpC,CAAC,CAAA;AAEF,MAAa,SAAU,SAAQ,KAAK;IAClC,YACW,UAAkB,EAC3B,OAAO,GAAG,eAAe,iBAAiB,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,UAAU,EAAE,EACjE,cAA0B,IAAI;QAEvC,KAAK,CAAC,OAAO,CAAC,CAAA;QAJL,eAAU,GAAV,UAAU,CAAQ;QAElB,gBAAW,GAAX,WAAW,CAAmB;QAIvC,IAAI,CAAC,IAAI,GAAG,WAAW,CACtB;QAAC,IAA8B,CAAC,IAAI,GAAG,cAAc,UAAU,EAAE,CAAA;IACpE,CAAC;IAED,aAAa;QACX,OAAO,IAAI,CAAC,UAAU,IAAI,GAAG,IAAI,IAAI,CAAC,UAAU,IAAI,GAAG,CAAA;IACzD,CAAC;CACF;AAfD,8BAeC;AAED,SAAgB,SAAS,CAAC,MAA8B;IACtD,OAAO,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,IAAI,IAAI,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,CAAA;AACnF,CAAC;AAFD,8BAEC;AAMD,MAAsB,YAAY;IAAlC;QACqB,iBAAY,GAAG,EAAE,CAAA;IAmQtC,CAAC;IAjQC,OAAO,CAAC,OAAuB,EAAE,oBAAuC,IAAI,qCAAiB,EAAE,EAAE,IAAqC;QACpI,uBAAuB,CAAC,OAAO,CAAC,CAAA;QAChC,MAAM,IAAI,GAAG,IAAI,IAAI,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA;QAC5D,MAAM,WAAW,GAAG,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS,CAAA;QACxD,IAAI,WAAW,IAAI,IAAI,EAAE,CAAC;YACxB,KAAK,CAAC,IAAK,CAAC,CAAA;YACZ,MAAM,EAAE,OAAO,EAAE,GAAG,IAAI,EAAE,GAAG,OAAO,CAAA;YACpC,OAAO,GAAG;gBACR,MAAM,EAAE,MAAM;gBACd,OAAO,EAAE;oBACP,cAAc,EAAE,kBAAkB;oBAClC,gBAAgB,EAAE,WAAW,CAAC,MAAM;oBACpC,GAAG,OAAO;iBACX;gBACD,GAAG,IAAI;aACR,CAAA;QACH,CAAC;QACD,OAAO,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,iBAAiB,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC,CAAA;IACjF,CAAC;IAED,YAAY,CACV,OAAuB,EACvB,iBAAoC,EACpC,gBAAsE,EACtE,aAAa,GAAG,CAAC;QAEjB,IAAI,KAAK,CAAC,OAAO,EAAE,CAAC;YAClB,KAAK,CAAC,YAAY,iBAAiB,CAAC,OAAO,CAAC,EAAE,CAAC,CAAA;QACjD,CAAC;QAED,OAAO,iBAAiB,CAAC,aAAa,CAAS,CAAC,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,EAAE;YAC3E,MAAM,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC,QAAa,EAAE,EAAE;gBAC5D,IAAI,CAAC;oBACH,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,OAAO,EAAE,iBAAiB,EAAE,OAAO,EAAE,MAAM,EAAE,aAAa,EAAE,gBAAgB,CAAC,CAAA;gBAC7G,CAAC;gBAAC,OAAO,CAAM,EAAE,CAAC;oBAChB,MAAM,CAAC,CAAC,CAAC,CAAA;gBACX,CAAC;YACH,CAAC,CAAC,CAAA;YACF,IAAI,CAAC,0BAA0B,CAAC,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC,OAAO,CAAC,CAAA;YACjE,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,aAAa,EAAE,OAAO,CAAC,EAAE;gBAC1E,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,iBAAiB,EAAE,gBAAgB,EAAE,aAAa,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,CAAA;YAC5G,CAAC,CAAC,CAAA;YACF,gBAAgB,CAAC,OAAO,EAAE,MAAM,CAAC,CAAA;YACjC,QAAQ,CAAC,GAAG,EAAE,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC,CAAA;QACjC,CAAC,CAAC,CAAA;IACJ,CAAC;IAED,oCAAoC;IACpC,2BAA2B;IACjB,mBAAmB,CAAC,OAAY,EAAE,OAAuB,EAAE,MAA8B,EAAE,aAAqB,EAAE,OAA0C;QACpK,0BAA0B;IAC5B,CAAC;IAED,0BAA0B,CAAC,OAAY,EAAE,MAA8B,EAAE,OAAO,GAAG,EAAE,GAAG,IAAI;QAC1F,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC,CAAA;QAChD,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,MAAM,CAAC,CAAA;QAC3B,OAAO,CAAC,EAAE,CAAC,SAAS,EAAE,GAAG,EAAE;YACzB,MAAM,CAAC,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAC,CAAA;QAC7D,CAAC,CAAC,CAAA;IACJ,CAAC;IAEO,cAAc,CACpB,QAAyB,EACzB,OAAuB,EACvB,iBAAoC,EACpC,OAA6B,EAC7B,MAA8B,EAC9B,aAAqB,EACrB,gBAAsE;;QAEtE,IAAI,KAAK,CAAC,OAAO,EAAE,CAAC;YAClB,KAAK,CAAC,aAAa,QAAQ,CAAC,UAAU,IAAI,QAAQ,CAAC,aAAa,sBAAsB,iBAAiB,CAAC,OAAO,CAAC,EAAE,CAAC,CAAA;QACrH,CAAC;QAED,+FAA+F;QAC/F,IAAI,QAAQ,CAAC,UAAU,KAAK,GAAG,EAAE,CAAC;YAChC,mEAAmE;YACnE,MAAM,CACJ,eAAe,CACb,QAAQ,EACR,WAAW,OAAO,CAAC,MAAM,IAAI,KAAK,SAAS,OAAO,CAAC,QAAQ,IAAI,QAAQ,KAAK,OAAO,CAAC,QAAQ,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,EAAE,GAAG,OAAO,CAAC,IAAI;;;CAG7J,CACQ,CACF,CAAA;YACD,OAAM;QACR,CAAC;aAAM,IAAI,QAAQ,CAAC,UAAU,KAAK,GAAG,EAAE,CAAC;YACvC,oBAAoB;YACpB,OAAO,EAAE,CAAA;YACT,OAAM;QACR,CAAC;QAED,MAAM,IAAI,GAAG,MAAA,QAAQ,CAAC,UAAU,mCAAI,CAAC,CAAA;QACrC,MAAM,cAAc,GAAG,IAAI,IAAI,GAAG,IAAI,IAAI,GAAG,GAAG,CAAA;QAChD,MAAM,WAAW,GAAG,aAAa,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAA;QACvD,IAAI,cAAc,IAAI,WAAW,IAAI,IAAI,EAAE,CAAC;YAC1C,IAAI,aAAa,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;gBACtC,MAAM,CAAC,IAAI,CAAC,sBAAsB,EAAE,CAAC,CAAA;gBACrC,OAAM;YACR,CAAC;YAED,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,yBAAyB,CAAC,WAAW,EAAE,OAAO,CAAC,EAAE,iBAAiB,EAAE,gBAAgB,EAAE,aAAa,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,CAAA;YAC/J,OAAM;QACR,CAAC;QAED,QAAQ,CAAC,WAAW,CAAC,MAAM,CAAC,CAAA;QAE5B,IAAI,IAAI,GAAG,EAAE,CAAA;QACb,QAAQ,CAAC,EAAE,CAAC,OAAO,EAAE,MAAM,CAAC,CAAA;QAC5B,QAAQ,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,KAAa,EAAE,EAAE,CAAC,CAAC,IAAI,IAAI,KAAK,CAAC,CAAC,CAAA;QACvD,QAAQ,CAAC,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE;YACtB,IAAI,CAAC;gBACH,IAAI,QAAQ,CAAC,UAAU,IAAI,IAAI,IAAI,QAAQ,CAAC,UAAU,IAAI,GAAG,EAAE,CAAC;oBAC9D,MAAM,WAAW,GAAG,aAAa,CAAC,QAAQ,EAAE,cAAc,CAAC,CAAA;oBAC3D,MAAM,MAAM,GAAG,WAAW,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,WAAW,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAA;oBACvJ,MAAM,CACJ,eAAe,CACb,QAAQ,EACR,WAAW,OAAO,CAAC,MAAM,IAAI,KAAK,SAAS,OAAO,CAAC,QAAQ,IAAI,QAAQ,KAAK,OAAO,CAAC,QAAQ,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,EAAE,GAAG,OAAO,CAAC,IAAI;;;YAGtJ,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI;WACjD,CACE,CACF,CAAA;gBACH,CAAC;qBAAM,CAAC;oBACN,OAAO,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAA;gBAC1C,CAAC;YACH,CAAC;YAAC,OAAO,CAAM,EAAE,CAAC;gBAChB,MAAM,CAAC,CAAC,CAAC,CAAA;YACX,CAAC;QACH,CAAC,CAAC,CAAA;IACJ,CAAC;IAKD,KAAK,CAAC,gBAAgB,CAAC,GAAQ,EAAE,OAAwB;QACvD,OAAO,MAAM,OAAO,CAAC,iBAAiB,CAAC,aAAa,CAAS,CAAC,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,EAAE;YACzF,MAAM,cAAc,GAAa,EAAE,CAAA;YACnC,MAAM,cAAc,GAAG;gBACrB,OAAO,EAAE,OAAO,CAAC,OAAO,IAAI,SAAS;gBACrC,wHAAwH;gBACxH,QAAQ,EAAE,QAAQ;aACnB,CAAA;YACD,mBAAmB,CAAC,GAAG,EAAE,cAAc,CAAC,CAAA;YACxC,uBAAuB,CAAC,cAAc,CAAC,CAAA;YACvC,IAAI,CAAC,UAAU,CACb,cAAc,EACd;gBACE,WAAW,EAAE,IAAI;gBACjB,OAAO;gBACP,QAAQ;gBACR,QAAQ,EAAE,KAAK,CAAC,EAAE;oBAChB,IAAI,KAAK,IAAI,IAAI,EAAE,CAAC;wBAClB,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,CAAA;oBACxC,CAAC;yBAAM,CAAC;wBACN,MAAM,CAAC,KAAK,CAAC,CAAA;oBACf,CAAC;gBACH,CAAC;gBACD,eAAe,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,EAAE;oBACtC,IAAI,cAAc,GAAG,CAAC,CAAA;oBACtB,QAAQ,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,KAAa,EAAE,EAAE;wBACpC,cAAc,IAAI,KAAK,CAAC,MAAM,CAAA;wBAC9B,IAAI,cAAc,GAAG,SAAS,EAAE,CAAC;4BAC/B,QAAQ,CAAC,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC,CAAA;4BACrD,OAAM;wBACR,CAAC;wBACD,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;oBAC5B,CAAC,CAAC,CAAA;oBACF,QAAQ,CAAC,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE;wBACtB,QAAQ,CAAC,IAAI,CAAC,CAAA;oBAChB,CAAC,CAAC,CAAA;gBACJ,CAAC;aACF,EACD,CAAC,CACF,CAAA;QACH,CAAC,CAAC,CAAA;IACJ,CAAC;IAES,UAAU,CAAC,cAA8B,EAAE,OAA4B,EAAE,aAAqB;QACtG,MAAM,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,cAAc,EAAE,CAAC,QAAyB,EAAE,EAAE;YAC/E,IAAI,QAAQ,CAAC,UAAW,IAAI,GAAG,EAAE,CAAC;gBAChC,OAAO,CAAC,QAAQ,CACd,IAAI,KAAK,CACP,oBAAoB,cAAc,CAAC,QAAQ,IAAI,QAAQ,KAAK,cAAc,CAAC,QAAQ,GAAG,cAAc,CAAC,IAAI,aAAa,QAAQ,CAAC,UAAU,KAAK,QAAQ,CAAC,aAAa,EAAE,CACvK,CACF,CAAA;gBACD,OAAM;YACR,CAAC;YAED,oFAAoF;YACpF,2FAA2F;YAC3F,QAAQ,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,QAAQ,CAAC,CAAA;YAEtC,uEAAuE;YACvE,MAAM,WAAW,GAAG,aAAa,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAA;YACvD,IAAI,WAAW,IAAI,IAAI,EAAE,CAAC;gBACxB,IAAI,aAAa,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;oBACtC,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,yBAAyB,CAAC,WAAW,EAAE,cAAc,CAAC,EAAE,OAAO,EAAE,aAAa,EAAE,CAAC,CAAA;gBAChH,CAAC;qBAAM,CAAC;oBACN,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,sBAAsB,EAAE,CAAC,CAAA;gBACjD,CAAC;gBACD,OAAM;YACR,CAAC;YAED,IAAI,OAAO,CAAC,eAAe,IAAI,IAAI,EAAE,CAAC;gBACpC,cAAc,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAA;YACnC,CAAC;iBAAM,CAAC;gBACN,OAAO,CAAC,eAAe,CAAC,QAAQ,EAAE,OAAO,CAAC,QAAQ,CAAC,CAAA;YACrD,CAAC;QACH,CAAC,CAAC,CAAA;QACF,IAAI,CAAC,0BAA0B,CAAC,OAAO,EAAE,OAAO,CAAC,QAAQ,EAAE,cAAc,CAAC,OAAO,CAAC,CAAA;QAClF,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,cAAc,EAAE,OAAO,CAAC,QAAQ,EAAE,aAAa,EAAE,cAAc,CAAC,EAAE;YAClG,IAAI,CAAC,UAAU,CAAC,cAAc,EAAE,OAAO,EAAE,aAAa,EAAE,CAAC,CAAA;QAC3D,CAAC,CAAC,CAAA;QACF,OAAO,CAAC,GAAG,EAAE,CAAA;IACf,CAAC;IAES,sBAAsB;QAC9B,OAAO,IAAI,KAAK,CAAC,yBAAyB,IAAI,CAAC,YAAY,GAAG,CAAC,CAAA;IACjE,CAAC;IAEO,iBAAiB,CAAC,OAAY,EAAE,QAAgC,EAAE,OAAe;QACvF,OAAO,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC,MAAc,EAAE,EAAE;YACtC,MAAM,CAAC,UAAU,CAAC,OAAO,EAAE,GAAG,EAAE;gBAC9B,OAAO,CAAC,KAAK,EAAE,CAAA;gBACf,QAAQ,CAAC,IAAI,KAAK,CAAC,mBAAmB,CAAC,CAAC,CAAA;YAC1C,CAAC,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;IACJ,CAAC;IAED,MAAM,CAAC,yBAAyB,CAAC,WAAmB,EAAE,OAAuB;QAC3E,MAAM,UAAU,GAAG,8BAA8B,CAAC,WAAW,EAAE,EAAE,GAAG,OAAO,EAAE,CAAC,CAAA;QAC9E,MAAM,OAAO,GAAG,UAAU,CAAC,OAAO,CAAA;QAClC,IAAI,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,aAAa,EAAE,CAAC;YAC3B,MAAM,YAAY,GAAG,IAAI,SAAG,CAAC,WAAW,CAAC,CAAA;YACzC,IAAI,YAAY,CAAC,QAAQ,CAAC,QAAQ,CAAC,gBAAgB,CAAC,IAAI,YAAY,CAAC,YAAY,CAAC,GAAG,CAAC,kBAAkB,CAAC,EAAE,CAAC;gBAC1G,OAAO,OAAO,CAAC,aAAa,CAAA;YAC9B,CAAC;QACH,CAAC;QACD,OAAO,UAAU,CAAA;IACnB,CAAC;IAED,MAAM,CAAC,kBAAkB,CAAC,IAAwB,EAAE,UAAU,GAAG,CAAC;QAChE,KAAK,IAAI,aAAa,GAAG,CAAC,GAAI,aAAa,EAAE,EAAE,CAAC;YAC9C,IAAI,CAAC;gBACH,OAAO,IAAI,EAAE,CAAA;YACf,CAAC;YAAC,OAAO,CAAM,EAAE,CAAC;gBAChB,IAAI,aAAa,GAAG,UAAU,IAAI,CAAC,CAAC,CAAC,YAAY,SAAS,IAAI,CAAC,CAAC,aAAa,EAAE,CAAC,IAAI,CAAC,CAAC,IAAI,KAAK,OAAO,CAAC,EAAE,CAAC;oBACxG,SAAQ;gBACV,CAAC;gBACD,MAAM,CAAC,CAAA;YACT,CAAC;QACH,CAAC;IACH,CAAC;CACF;AApQD,oCAoQC;AAYD,SAAgB,8BAA8B,CAAC,GAAW,EAAE,OAAuB;IACjF,MAAM,MAAM,GAAG,uBAAuB,CAAC,OAAO,CAAC,CAAA;IAC/C,mBAAmB,CAAC,IAAI,SAAG,CAAC,GAAG,CAAC,EAAE,MAAM,CAAC,CAAA;IACzC,OAAO,MAAM,CAAA;AACf,CAAC;AAJD,wEAIC;AAED,SAAgB,mBAAmB,CAAC,GAAQ,EAAE,OAAuB;IACnE,OAAO,CAAC,QAAQ,GAAG,GAAG,CAAC,QAAQ,CAAA;IAC/B,OAAO,CAAC,QAAQ,GAAG,GAAG,CAAC,QAAQ,CAAA;IAC/B,IAAI,GAAG,CAAC,IAAI,EAAE,CAAC;QACb,OAAO,CAAC,IAAI,GAAG,GAAG,CAAC,IAAI,CAAA;IACzB,CAAC;SAAM,IAAI,OAAO,CAAC,IAAI,EAAE,CAAC;QACxB,OAAO,OAAO,CAAC,IAAI,CAAA;IACrB,CAAC;IACD,OAAO,CAAC,IAAI,GAAG,GAAG,CAAC,QAAQ,GAAG,GAAG,CAAC,MAAM,CAAA;AAC1C,CAAC;AATD,kDASC;AAED,MAAa,eAAgB,SAAQ,kBAAS;IAK5C,qCAAqC;IACrC,IAAI,MAAM;QACR,OAAO,IAAI,CAAC,OAAO,CAAA;IACrB,CAAC;IAID,YACW,QAAgB,EACR,YAAoB,QAAQ,EAC5B,WAAiC,QAAQ;QAE1D,KAAK,EAAE,CAAA;QAJE,aAAQ,GAAR,QAAQ,CAAQ;QACR,cAAS,GAAT,SAAS,CAAmB;QAC5B,aAAQ,GAAR,QAAQ,CAAiC;QAZpD,YAAO,GAAkB,IAAI,CAAA;QAOrC,oBAAe,GAAG,IAAI,CAAA;QASpB,IAAI,CAAC,QAAQ,GAAG,IAAA,mBAAU,EAAC,SAAS,CAAC,CAAA;IACvC,CAAC;IAED,qCAAqC;IACrC,UAAU,CAAC,KAAa,EAAE,QAAgB,EAAE,QAAa;QACvD,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,CAAA;QAC3B,QAAQ,CAAC,IAAI,EAAE,KAAK,CAAC,CAAA;IACvB,CAAC;IAED,qCAAqC;IACrC,MAAM,CAAC,QAAa;QAClB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;QAElD,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;YACzB,IAAI,CAAC;gBACH,IAAI,CAAC,QAAQ,EAAE,CAAA;YACjB,CAAC;YAAC,OAAO,CAAM,EAAE,CAAC;gBAChB,QAAQ,CAAC,CAAC,CAAC,CAAA;gBACX,OAAM;YACR,CAAC;QACH,CAAC;QAED,QAAQ,CAAC,IAAI,CAAC,CAAA;IAChB,CAAC;IAED,QAAQ;QACN,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,EAAE,CAAC;YACzB,MAAM,IAAA,gBAAQ,EAAC,kBAAkB,EAAE,yBAAyB,CAAC,CAAA;QAC/D,CAAC;QAED,IAAI,IAAI,CAAC,OAAO,KAAK,IAAI,CAAC,QAAQ,EAAE,CAAC;YACnC,MAAM,IAAA,gBAAQ,EAAC,GAAG,IAAI,CAAC,SAAS,gCAAgC,IAAI,CAAC,QAAQ,SAAS,IAAI,CAAC,OAAO,EAAE,EAAE,uBAAuB,CAAC,CAAA;QAChI,CAAC;QAED,OAAO,IAAI,CAAA;IACb,CAAC;CACF;AAvDD,0CAuDC;AAED,SAAS,SAAS,CAAC,UAAqC,EAAE,IAA+B,EAAE,QAAuC;IAChI,IAAI,UAAU,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,UAAU,KAAK,IAAI,EAAE,CAAC;QAC9D,QAAQ,CAAC,IAAI,KAAK,CAAC,+BAA+B,IAAI,YAAY,UAAU,2BAA2B,CAAC,CAAC,CAAA;QACzG,OAAO,KAAK,CAAA;IACd,CAAC;IACD,OAAO,IAAI,CAAA;AACb,CAAC;AAED,SAAgB,aAAa,CAAC,QAAa,EAAE,SAAiB;IAC5D,MAAM,KAAK,GAAG,QAAQ,CAAC,OAAO,CAAC,SAAS,CAAC,CAAA;IACzC,IAAI,KAAK,IAAI,IAAI,EAAE,CAAC;QAClB,OAAO,IAAI,CAAA;IACb,CAAC;SAAM,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;QAChC,eAAe;QACf,OAAO,KAAK,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAA;IAC5D,CAAC;SAAM,CAAC;QACN,OAAO,KAAK,CAAA;IACd,CAAC;AACH,CAAC;AAVD,sCAUC;AAED,SAAS,cAAc,CAAC,OAA4B,EAAE,QAAyB;IAC7E,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,QAAQ,EAAE,iBAAiB,CAAC,EAAE,OAAO,CAAC,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC;QACnG,OAAM;IACR,CAAC;IAED,MAAM,OAAO,GAAe,EAAE,CAAA;IAC9B,IAAI,OAAO,CAAC,OAAO,CAAC,UAAU,IAAI,IAAI,EAAE,CAAC;QACvC,MAAM,aAAa,GAAG,aAAa,CAAC,QAAQ,EAAE,gBAAgB,CAAC,CAAA;QAC/D,IAAI,aAAa,IAAI,IAAI,EAAE,CAAC;YAC1B,OAAO,CAAC,IAAI,CAAC,IAAI,qDAAyB,CAAC,QAAQ,CAAC,aAAa,EAAE,EAAE,CAAC,EAAE,OAAO,CAAC,OAAO,CAAC,iBAAiB,EAAE,OAAO,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAA;QACzI,CAAC;IACH,CAAC;IAED,MAAM,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC,MAAM,CAAA;IACrC,IAAI,MAAM,IAAI,IAAI,EAAE,CAAC;QACnB,OAAO,CAAC,IAAI,CAAC,IAAI,eAAe,CAAC,MAAM,EAAE,QAAQ,EAAE,MAAM,CAAC,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAA;IAC1K,CAAC;SAAM,IAAI,OAAO,CAAC,OAAO,CAAC,IAAI,IAAI,IAAI,EAAE,CAAC;QACxC,OAAO,CAAC,IAAI,CAAC,IAAI,eAAe,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC,CAAA;IAC1E,CAAC;IAED,MAAM,OAAO,GAAG,IAAA,sBAAiB,EAAC,OAAO,CAAC,WAAY,CAAC,CAAA;IACvD,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;IAErB,IAAI,UAAU,GAAG,QAAQ,CAAA;IACzB,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;QAC7B,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,KAAY,EAAE,EAAE;YAClC,OAAO,CAAC,KAAK,EAAE,CAAA;YACf,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,iBAAiB,CAAC,SAAS,EAAE,CAAC;gBACjD,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAA;YACzB,CAAC;QACH,CAAC,CAAC,CAAA;QACF,UAAU,GAAG,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;IACtC,CAAC;IAED,OAAO,CAAC,EAAE,CAAC,QAAQ,EAAE,GAAG,EAAE;QACxB,CAAC;QAAC,OAAO,CAAC,KAAa,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAA;IAC3C,CAAC,CAAC,CAAA;AACJ,CAAC;AAED,SAAgB,uBAAuB,CAAC,OAAuB,EAAE,KAAqB,EAAE,MAA0C;IAChI,IAAI,MAAM,IAAI,IAAI,EAAE,CAAC;QACnB,OAAO,CAAC,MAAM,GAAG,MAAM,CAAA;IACzB,CAAC;IAED,OAAO,CAAC,OAAO,GAAG,EAAE,GAAG,OAAO,CAAC,OAAO,EAAE,CAAA;IACxC,MAAM,OAAO,GAAG,OAAO,CAAC,OAAO,CAAA;IAE/B,IAAI,KAAK,IAAI,IAAI,EAAE,CAAC;QAClB,CAAC;QAAC,OAAe,CAAC,aAAa,GAAG,KAAK,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,KAAK,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS,KAAK,EAAE,CAAA;IACtH,CAAC;IACD,IAAI,OAAO,CAAC,YAAY,CAAC,IAAI,IAAI,EAAE,CAAC;QAClC,OAAO,CAAC,YAAY,CAAC,GAAG,kBAAkB,CAAA;IAC5C,CAAC;IAED,IAAI,MAAM,IAAI,IAAI,IAAI,MAAM,KAAK,KAAK,IAAI,OAAO,CAAC,eAAe,CAAC,IAAI,IAAI,EAAE,CAAC;QAC3E,OAAO,CAAC,eAAe,CAAC,GAAG,UAAU,CAAA;IACvC,CAAC;IAED,4DAA4D;IAC5D,IAAI,OAAO,CAAC,QAAQ,IAAI,IAAI,IAAK,OAAO,CAAC,QAAgB,CAAC,QAAQ,IAAI,IAAI,EAAE,CAAC;QAC3E,OAAO,CAAC,QAAQ,GAAG,QAAQ,CAAA;IAC7B,CAAC;IACD,OAAO,OAAO,CAAA;AAChB,CAAC;AAxBD,0DAwBC;AAED,SAAgB,iBAAiB,CAAC,IAAS,EAAE,YAA0B;IACrE,OAAO,IAAI,CAAC,SAAS,CACnB,IAAI,EACJ,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE;QACd,IACE,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC;YAC9B,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC;YAC9B,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC;YACzB,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC;YACzB,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC;YACtB,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC;YACzB,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC;YACtB,CAAC,YAAY,IAAI,IAAI,IAAI,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,EAChD,CAAC;YACD,OAAO,2BAA2B,CAAA;QACpC,CAAC;QACD,OAAO,KAAK,CAAA;IACd,CAAC,EACD,CAAC,CACF,CAAA;AACH,CAAC;AApBD,8CAoBC", "sourcesContent": ["import { BinaryToTextEncoding, createHash, Hash } from \"crypto\"\nimport _debug from \"debug\"\nimport { createWriteStream } from \"fs\"\nimport { IncomingMessage, OutgoingHttpHeader, OutgoingHttpHeaders, RequestOptions } from \"http\"\nimport { Socket } from \"net\"\nimport { Transform } from \"stream\"\nimport { URL } from \"url\"\nimport { CancellationToken } from \"./CancellationToken\"\nimport { newError } from \"./index\"\nimport { ProgressCallbackTransform, ProgressInfo } from \"./ProgressCallbackTransform\"\n\nconst debug = _debug(\"electron-builder\")\n\nexport interface RequestHeaders extends OutgoingHttpHeaders {\n  [key: string]: OutgoingHttpHeader | undefined\n}\n\nexport interface DownloadOptions {\n  readonly headers?: OutgoingHttpHeaders | null\n  readonly sha2?: string | null\n  readonly sha512?: string | null\n\n  readonly cancellationToken: CancellationToken\n\n  // noinspection JSUnusedLocalSymbols\n  onProgress?: (progress: ProgressInfo) => void\n}\n\nexport function createHttpError(response: IncomingMessage, description: any | null = null) {\n  return new HttpError(\n    response.statusCode || -1,\n    `${response.statusCode} ${response.statusMessage}` +\n      (description == null ? \"\" : \"\\n\" + JSON.stringify(description, null, \"  \")) +\n      \"\\nHeaders: \" +\n      safeStringifyJson(response.headers),\n    description\n  )\n}\n\nconst HTTP_STATUS_CODES = new Map<number, string>([\n  [429, \"Too many requests\"],\n  [400, \"Bad request\"],\n  [403, \"Forbidden\"],\n  [404, \"Not found\"],\n  [405, \"Method not allowed\"],\n  [406, \"Not acceptable\"],\n  [408, \"Request timeout\"],\n  [413, \"Request entity too large\"],\n  [500, \"Internal server error\"],\n  [502, \"Bad gateway\"],\n  [503, \"Service unavailable\"],\n  [504, \"Gateway timeout\"],\n  [505, \"HTTP version not supported\"],\n])\n\nexport class HttpError extends Error {\n  constructor(\n    readonly statusCode: number,\n    message = `HTTP error: ${HTTP_STATUS_CODES.get(statusCode) || statusCode}`,\n    readonly description: any | null = null\n  ) {\n    super(message)\n\n    this.name = \"HttpError\"\n    ;(this as NodeJS.ErrnoException).code = `HTTP_ERROR_${statusCode}`\n  }\n\n  isServerError() {\n    return this.statusCode >= 500 && this.statusCode <= 599\n  }\n}\n\nexport function parseJson(result: Promise<string | null>) {\n  return result.then(it => (it == null || it.length === 0 ? null : JSON.parse(it)))\n}\n\ninterface Request {\n  abort: () => void\n  end: (data?: Buffer) => void\n}\nexport abstract class HttpExecutor<T extends Request> {\n  protected readonly maxRedirects = 10\n\n  request(options: RequestOptions, cancellationToken: CancellationToken = new CancellationToken(), data?: { [name: string]: any } | null): Promise<string | null> {\n    configureRequestOptions(options)\n    const json = data == null ? undefined : JSON.stringify(data)\n    const encodedData = json ? Buffer.from(json) : undefined\n    if (encodedData != null) {\n      debug(json!)\n      const { headers, ...opts } = options\n      options = {\n        method: \"post\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n          \"Content-Length\": encodedData.length,\n          ...headers,\n        },\n        ...opts,\n      }\n    }\n    return this.doApiRequest(options, cancellationToken, it => it.end(encodedData))\n  }\n\n  doApiRequest(\n    options: RequestOptions,\n    cancellationToken: CancellationToken,\n    requestProcessor: (request: T, reject: (error: Error) => void) => void,\n    redirectCount = 0\n  ): Promise<string> {\n    if (debug.enabled) {\n      debug(`Request: ${safeStringifyJson(options)}`)\n    }\n\n    return cancellationToken.createPromise<string>((resolve, reject, onCancel) => {\n      const request = this.createRequest(options, (response: any) => {\n        try {\n          this.handleResponse(response, options, cancellationToken, resolve, reject, redirectCount, requestProcessor)\n        } catch (e: any) {\n          reject(e)\n        }\n      })\n      this.addErrorAndTimeoutHandlers(request, reject, options.timeout)\n      this.addRedirectHandlers(request, options, reject, redirectCount, options => {\n        this.doApiRequest(options, cancellationToken, requestProcessor, redirectCount).then(resolve).catch(reject)\n      })\n      requestProcessor(request, reject)\n      onCancel(() => request.abort())\n    })\n  }\n\n  // noinspection JSUnusedLocalSymbols\n  // eslint-disable-next-line\n  protected addRedirectHandlers(request: any, options: RequestOptions, reject: (error: Error) => void, redirectCount: number, handler: (options: RequestOptions) => void) {\n    // not required for NodeJS\n  }\n\n  addErrorAndTimeoutHandlers(request: any, reject: (error: Error) => void, timeout = 60 * 1000) {\n    this.addTimeOutHandler(request, reject, timeout)\n    request.on(\"error\", reject)\n    request.on(\"aborted\", () => {\n      reject(new Error(\"Request has been aborted by the server\"))\n    })\n  }\n\n  private handleResponse(\n    response: IncomingMessage,\n    options: RequestOptions,\n    cancellationToken: CancellationToken,\n    resolve: (data?: any) => void,\n    reject: (error: Error) => void,\n    redirectCount: number,\n    requestProcessor: (request: T, reject: (error: Error) => void) => void\n  ) {\n    if (debug.enabled) {\n      debug(`Response: ${response.statusCode} ${response.statusMessage}, request options: ${safeStringifyJson(options)}`)\n    }\n\n    // we handle any other >= 400 error on request end (read detailed message in the response body)\n    if (response.statusCode === 404) {\n      // error is clear, we don't need to read detailed error description\n      reject(\n        createHttpError(\n          response,\n          `method: ${options.method || \"GET\"} url: ${options.protocol || \"https:\"}//${options.hostname}${options.port ? `:${options.port}` : \"\"}${options.path}\n\nPlease double check that your authentication token is correct. Due to security reasons, actual status maybe not reported, but 404.\n`\n        )\n      )\n      return\n    } else if (response.statusCode === 204) {\n      // on DELETE request\n      resolve()\n      return\n    }\n\n    const code = response.statusCode ?? 0\n    const shouldRedirect = code >= 300 && code < 400\n    const redirectUrl = safeGetHeader(response, \"location\")\n    if (shouldRedirect && redirectUrl != null) {\n      if (redirectCount > this.maxRedirects) {\n        reject(this.createMaxRedirectError())\n        return\n      }\n\n      this.doApiRequest(HttpExecutor.prepareRedirectUrlOptions(redirectUrl, options), cancellationToken, requestProcessor, redirectCount).then(resolve).catch(reject)\n      return\n    }\n\n    response.setEncoding(\"utf8\")\n\n    let data = \"\"\n    response.on(\"error\", reject)\n    response.on(\"data\", (chunk: string) => (data += chunk))\n    response.on(\"end\", () => {\n      try {\n        if (response.statusCode != null && response.statusCode >= 400) {\n          const contentType = safeGetHeader(response, \"content-type\")\n          const isJson = contentType != null && (Array.isArray(contentType) ? contentType.find(it => it.includes(\"json\")) != null : contentType.includes(\"json\"))\n          reject(\n            createHttpError(\n              response,\n              `method: ${options.method || \"GET\"} url: ${options.protocol || \"https:\"}//${options.hostname}${options.port ? `:${options.port}` : \"\"}${options.path}\n\n          Data:\n          ${isJson ? JSON.stringify(JSON.parse(data)) : data}\n          `\n            )\n          )\n        } else {\n          resolve(data.length === 0 ? null : data)\n        }\n      } catch (e: any) {\n        reject(e)\n      }\n    })\n  }\n\n  // noinspection JSUnusedLocalSymbols\n  abstract createRequest(options: RequestOptions, callback: (response: any) => void): T\n\n  async downloadToBuffer(url: URL, options: DownloadOptions): Promise<Buffer> {\n    return await options.cancellationToken.createPromise<Buffer>((resolve, reject, onCancel) => {\n      const responseChunks: Buffer[] = []\n      const requestOptions = {\n        headers: options.headers || undefined,\n        // because PrivateGitHubProvider requires HttpExecutor.prepareRedirectUrlOptions logic, so, we need to redirect manually\n        redirect: \"manual\",\n      }\n      configureRequestUrl(url, requestOptions)\n      configureRequestOptions(requestOptions)\n      this.doDownload(\n        requestOptions,\n        {\n          destination: null,\n          options,\n          onCancel,\n          callback: error => {\n            if (error == null) {\n              resolve(Buffer.concat(responseChunks))\n            } else {\n              reject(error)\n            }\n          },\n          responseHandler: (response, callback) => {\n            let receivedLength = 0\n            response.on(\"data\", (chunk: Buffer) => {\n              receivedLength += chunk.length\n              if (receivedLength > 524288000) {\n                callback(new Error(\"Maximum allowed size is 500 MB\"))\n                return\n              }\n              responseChunks.push(chunk)\n            })\n            response.on(\"end\", () => {\n              callback(null)\n            })\n          },\n        },\n        0\n      )\n    })\n  }\n\n  protected doDownload(requestOptions: RequestOptions, options: DownloadCallOptions, redirectCount: number) {\n    const request = this.createRequest(requestOptions, (response: IncomingMessage) => {\n      if (response.statusCode! >= 400) {\n        options.callback(\n          new Error(\n            `Cannot download \"${requestOptions.protocol || \"https:\"}//${requestOptions.hostname}${requestOptions.path}\", status ${response.statusCode}: ${response.statusMessage}`\n          )\n        )\n        return\n      }\n\n      // It is possible for the response stream to fail, e.g. when a network is lost while\n      // response stream is in progress. Stop waiting and reject so consumer can catch the error.\n      response.on(\"error\", options.callback)\n\n      // this code not relevant for Electron (redirect event instead handled)\n      const redirectUrl = safeGetHeader(response, \"location\")\n      if (redirectUrl != null) {\n        if (redirectCount < this.maxRedirects) {\n          this.doDownload(HttpExecutor.prepareRedirectUrlOptions(redirectUrl, requestOptions), options, redirectCount++)\n        } else {\n          options.callback(this.createMaxRedirectError())\n        }\n        return\n      }\n\n      if (options.responseHandler == null) {\n        configurePipes(options, response)\n      } else {\n        options.responseHandler(response, options.callback)\n      }\n    })\n    this.addErrorAndTimeoutHandlers(request, options.callback, requestOptions.timeout)\n    this.addRedirectHandlers(request, requestOptions, options.callback, redirectCount, requestOptions => {\n      this.doDownload(requestOptions, options, redirectCount++)\n    })\n    request.end()\n  }\n\n  protected createMaxRedirectError() {\n    return new Error(`Too many redirects (> ${this.maxRedirects})`)\n  }\n\n  private addTimeOutHandler(request: any, callback: (error: Error) => void, timeout: number) {\n    request.on(\"socket\", (socket: Socket) => {\n      socket.setTimeout(timeout, () => {\n        request.abort()\n        callback(new Error(\"Request timed out\"))\n      })\n    })\n  }\n\n  static prepareRedirectUrlOptions(redirectUrl: string, options: RequestOptions): RequestOptions {\n    const newOptions = configureRequestOptionsFromUrl(redirectUrl, { ...options })\n    const headers = newOptions.headers\n    if (headers?.authorization) {\n      const parsedNewUrl = new URL(redirectUrl)\n      if (parsedNewUrl.hostname.endsWith(\".amazonaws.com\") || parsedNewUrl.searchParams.has(\"X-Amz-Credential\")) {\n        delete headers.authorization\n      }\n    }\n    return newOptions\n  }\n\n  static retryOnServerError(task: () => Promise<any>, maxRetries = 3) {\n    for (let attemptNumber = 0; ; attemptNumber++) {\n      try {\n        return task()\n      } catch (e: any) {\n        if (attemptNumber < maxRetries && ((e instanceof HttpError && e.isServerError()) || e.code === \"EPIPE\")) {\n          continue\n        }\n        throw e\n      }\n    }\n  }\n}\n\nexport interface DownloadCallOptions {\n  responseHandler: ((response: IncomingMessage, callback: (error: Error | null) => void) => void) | null\n  onCancel: (callback: () => void) => void\n  callback: (error: Error | null) => void\n\n  options: DownloadOptions\n\n  destination: string | null\n}\n\nexport function configureRequestOptionsFromUrl(url: string, options: RequestOptions) {\n  const result = configureRequestOptions(options)\n  configureRequestUrl(new URL(url), result)\n  return result\n}\n\nexport function configureRequestUrl(url: URL, options: RequestOptions): void {\n  options.protocol = url.protocol\n  options.hostname = url.hostname\n  if (url.port) {\n    options.port = url.port\n  } else if (options.port) {\n    delete options.port\n  }\n  options.path = url.pathname + url.search\n}\n\nexport class DigestTransform extends Transform {\n  private readonly digester: Hash\n\n  private _actual: string | null = null\n\n  // noinspection JSUnusedGlobalSymbols\n  get actual() {\n    return this._actual\n  }\n\n  isValidateOnEnd = true\n\n  constructor(\n    readonly expected: string,\n    private readonly algorithm: string = \"sha512\",\n    private readonly encoding: BinaryToTextEncoding = \"base64\"\n  ) {\n    super()\n\n    this.digester = createHash(algorithm)\n  }\n\n  // noinspection JSUnusedGlobalSymbols\n  _transform(chunk: Buffer, encoding: string, callback: any) {\n    this.digester.update(chunk)\n    callback(null, chunk)\n  }\n\n  // noinspection JSUnusedGlobalSymbols\n  _flush(callback: any): void {\n    this._actual = this.digester.digest(this.encoding)\n\n    if (this.isValidateOnEnd) {\n      try {\n        this.validate()\n      } catch (e: any) {\n        callback(e)\n        return\n      }\n    }\n\n    callback(null)\n  }\n\n  validate() {\n    if (this._actual == null) {\n      throw newError(\"Not finished yet\", \"ERR_STREAM_NOT_FINISHED\")\n    }\n\n    if (this._actual !== this.expected) {\n      throw newError(`${this.algorithm} checksum mismatch, expected ${this.expected}, got ${this._actual}`, \"ERR_CHECKSUM_MISMATCH\")\n    }\n\n    return null\n  }\n}\n\nfunction checkSha2(sha2Header: string | null | undefined, sha2: string | null | undefined, callback: (error: Error | null) => void): boolean {\n  if (sha2Header != null && sha2 != null && sha2Header !== sha2) {\n    callback(new Error(`checksum mismatch: expected ${sha2} but got ${sha2Header} (X-Checksum-Sha2 header)`))\n    return false\n  }\n  return true\n}\n\nexport function safeGetHeader(response: any, headerKey: string) {\n  const value = response.headers[headerKey]\n  if (value == null) {\n    return null\n  } else if (Array.isArray(value)) {\n    // electron API\n    return value.length === 0 ? null : value[value.length - 1]\n  } else {\n    return value\n  }\n}\n\nfunction configurePipes(options: DownloadCallOptions, response: IncomingMessage) {\n  if (!checkSha2(safeGetHeader(response, \"X-Checksum-Sha2\"), options.options.sha2, options.callback)) {\n    return\n  }\n\n  const streams: Array<any> = []\n  if (options.options.onProgress != null) {\n    const contentLength = safeGetHeader(response, \"content-length\")\n    if (contentLength != null) {\n      streams.push(new ProgressCallbackTransform(parseInt(contentLength, 10), options.options.cancellationToken, options.options.onProgress))\n    }\n  }\n\n  const sha512 = options.options.sha512\n  if (sha512 != null) {\n    streams.push(new DigestTransform(sha512, \"sha512\", sha512.length === 128 && !sha512.includes(\"+\") && !sha512.includes(\"Z\") && !sha512.includes(\"=\") ? \"hex\" : \"base64\"))\n  } else if (options.options.sha2 != null) {\n    streams.push(new DigestTransform(options.options.sha2, \"sha256\", \"hex\"))\n  }\n\n  const fileOut = createWriteStream(options.destination!)\n  streams.push(fileOut)\n\n  let lastStream = response\n  for (const stream of streams) {\n    stream.on(\"error\", (error: Error) => {\n      fileOut.close()\n      if (!options.options.cancellationToken.cancelled) {\n        options.callback(error)\n      }\n    })\n    lastStream = lastStream.pipe(stream)\n  }\n\n  fileOut.on(\"finish\", () => {\n    ;(fileOut.close as any)(options.callback)\n  })\n}\n\nexport function configureRequestOptions(options: RequestOptions, token?: string | null, method?: \"GET\" | \"DELETE\" | \"PUT\" | \"POST\"): RequestOptions {\n  if (method != null) {\n    options.method = method\n  }\n\n  options.headers = { ...options.headers }\n  const headers = options.headers\n\n  if (token != null) {\n    ;(headers as any).authorization = token.startsWith(\"Basic\") || token.startsWith(\"Bearer\") ? token : `token ${token}`\n  }\n  if (headers[\"User-Agent\"] == null) {\n    headers[\"User-Agent\"] = \"electron-builder\"\n  }\n\n  if (method == null || method === \"GET\" || headers[\"Cache-Control\"] == null) {\n    headers[\"Cache-Control\"] = \"no-cache\"\n  }\n\n  // do not specify for node (in any case we use https module)\n  if (options.protocol == null && (process.versions as any).electron != null) {\n    options.protocol = \"https:\"\n  }\n  return options\n}\n\nexport function safeStringifyJson(data: any, skippedNames?: Set<string>) {\n  return JSON.stringify(\n    data,\n    (name, value) => {\n      if (\n        name.endsWith(\"Authorization\") ||\n        name.endsWith(\"authorization\") ||\n        name.endsWith(\"Password\") ||\n        name.endsWith(\"PASSWORD\") ||\n        name.endsWith(\"Token\") ||\n        name.includes(\"password\") ||\n        name.includes(\"token\") ||\n        (skippedNames != null && skippedNames.has(name))\n      ) {\n        return \"<stripped sensitive data>\"\n      }\n      return value\n    },\n    2\n  )\n}\n"]}