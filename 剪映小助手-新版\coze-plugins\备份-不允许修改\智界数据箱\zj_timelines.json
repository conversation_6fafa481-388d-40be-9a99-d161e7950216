{"openapi": "3.0.0", "info": {"title": "剪映小助手_智界数据箱 - 时间线生成器", "description": "自定义创建时间线列表", "version": "1.0.0"}, "servers": [{"url": "https://www.aigcview.com"}], "paths": {"/jeecg-boot/api/jianying/timelines": {"post": {"summary": "生成时间线", "description": "自定义创建时间线列表", "operationId": "timelines_zj", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"access_key": {"type": "string", "description": "访问密钥（必填）", "example": "JianyingAPI_2025_SecureAccess_AigcView", "default": "JianyingAPI_2025_SecureAccess_AigcView"}, "zj_duration": {"type": "integer", "description": "总长度，单位微秒，eg：10000000（必填）", "example": 10000000}, "zj_num": {"type": "integer", "description": "时间线上的个数（必填）", "example": 5}, "zj_start": {"type": "integer", "description": "默认开始0，如果设置了，就从start开始", "example": 0}, "zj_type": {"type": "integer", "description": "方式，0平均分，1随机，默认0", "enum": [0, 1], "example": 0}}, "required": ["access_key", "zj_duration", "zj_num"], "additionalProperties": false}}}}, "responses": {"200": {"description": "成功生成时间线", "content": {"application/json": {"schema": {"type": "object", "properties": {"all_timelines": {"type": "array", "description": "整体时间范围", "items": {"type": "object", "properties": {"end": {"type": "integer", "description": "结束时间（微秒）"}, "start": {"type": "integer", "description": "开始时间（微秒）"}}}}, "timelines": {"type": "array", "description": "分割后的时间线数据", "items": {"type": "object", "properties": {"end": {"type": "integer", "description": "结束时间（微秒）"}, "start": {"type": "integer", "description": "开始时间（微秒）"}}}, "example": [{"end": 3333334, "start": 1}, {"end": 6666667, "start": 3333334}]}}, "required": ["all_timelines", "timelines"]}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "description": "操作是否成功", "example": false}, "error": {"type": "string", "description": "错误信息"}, "code": {"type": "string", "description": "错误代码"}}, "required": ["success", "error"]}}}}, "500": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "description": "操作是否成功", "example": false}, "error": {"type": "string", "description": "错误信息"}, "code": {"type": "string", "description": "错误代码"}}, "required": ["success", "error"]}}}}}}}}}