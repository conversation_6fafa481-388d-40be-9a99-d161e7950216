package org.jeecg.modules.system.mapper;

import org.jeecg.modules.system.entity.AicgVerifyCode;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.Date;

/**
 * @Description: 验证码记录表
 * @Author: jeecg-boot
 * @Date: 2025-06-19
 * @Version: V1.0
 */
@Mapper
public interface AicgVerifyCodeMapper extends BaseMapper<AicgVerifyCode> {

    /**
     * 查询最近的有效验证码
     * @param target 目标（手机号/邮箱）
     * @param codeType 验证码类型
     * @param scene 使用场景
     * @return 验证码记录
     */
    @Select("SELECT * FROM aicg_verify_code " +
            "WHERE target = #{target} AND code_type = #{codeType} AND scene = #{scene} " +
            "AND used_status = 0 AND expire_time > NOW() " +
            "ORDER BY create_time DESC LIMIT 1")
    AicgVerifyCode getLatestValidCode(@Param("target") String target, 
                                     @Param("codeType") String codeType, 
                                     @Param("scene") String scene);

    /**
     * 标记验证码为已使用
     * @param id 验证码ID
     * @return 更新行数
     */
    @Update("UPDATE aicg_verify_code SET used_status = 1, use_time = NOW() WHERE id = #{id}")
    int markAsUsed(@Param("id") String id);

    /**
     * 检查发送频率限制
     * @param target 目标（手机号/邮箱）
     * @param codeType 验证码类型
     * @param scene 使用场景
     * @param intervalSeconds 间隔秒数
     * @return 记录数
     */
    @Select("SELECT COUNT(*) FROM aicg_verify_code " +
            "WHERE target = #{target} AND code_type = #{codeType} AND scene = #{scene} " +
            "AND create_time > DATE_SUB(NOW(), INTERVAL #{intervalSeconds} SECOND)")
    int countRecentCodes(@Param("target") String target, 
                        @Param("codeType") String codeType, 
                        @Param("scene") String scene, 
                        @Param("intervalSeconds") int intervalSeconds);

    /**
     * 检查IP发送频率限制
     * @param ipAddress IP地址
     * @param codeType 验证码类型
     * @param scene 使用场景
     * @param intervalSeconds 间隔秒数
     * @return 记录数
     */
    @Select("SELECT COUNT(*) FROM aicg_verify_code " +
            "WHERE ip_address = #{ipAddress} AND code_type = #{codeType} AND scene = #{scene} " +
            "AND create_time > DATE_SUB(NOW(), INTERVAL #{intervalSeconds} SECOND)")
    int countRecentCodesByIp(@Param("ipAddress") String ipAddress, 
                            @Param("codeType") String codeType, 
                            @Param("scene") String scene, 
                            @Param("intervalSeconds") int intervalSeconds);

    /**
     * 统计当天发送的验证码数量
     * @param target 目标（手机号/邮箱）
     * @param codeType 验证码类型
     * @param scene 使用场景
     * @return 当天发送数量
     */
    @Select("SELECT COUNT(*) FROM aicg_verify_code " +
            "WHERE target = #{target} AND code_type = #{codeType} AND scene = #{scene} " +
            "AND DATE(create_time) = CURDATE()")
    int countDailyCodes(@Param("target") String target,
                       @Param("codeType") String codeType,
                       @Param("scene") String scene);

    /**
     * 清理过期验证码
     * @return 清理的记录数
     */
    @Update("DELETE FROM aicg_verify_code WHERE expire_time < NOW()")
    int cleanExpiredCodes();

    /**
     * 删除用户今日的验证码记录
     * @param target 目标（手机号/邮箱）
     * @param codeType 验证码类型
     * @return 删除的记录数
     */
    @Update("DELETE FROM aicg_verify_code " +
            "WHERE target = #{target} AND code_type = #{codeType} " +
            "AND DATE(create_time) = CURDATE()")
    int deleteUserTodayRecords(@Param("target") String target,
                              @Param("codeType") String codeType);

    /**
     * 统计今日验证码发送次数
     * @param target 目标（手机号/邮箱）
     * @param codeType 验证码类型
     * @return 今日发送次数
     */
    @Select("SELECT COUNT(*) FROM aicg_verify_code " +
            "WHERE target = #{target} AND code_type = #{codeType} " +
            "AND DATE(create_time) = CURDATE()")
    int countTodayCodes(@Param("target") String target,
                       @Param("codeType") String codeType);

    /**
     * 统计IP地址近期验证码发送次数
     * @param ipAddress IP地址
     * @param codeType 验证码类型
     * @param hours 小时数（查询多少小时内的记录）
     * @return 近期发送次数
     */
    @Select("SELECT COUNT(*) FROM aicg_verify_code " +
            "WHERE ip_address = #{ipAddress} AND code_type = #{codeType} " +
            "AND create_time > DATE_SUB(NOW(), INTERVAL #{hours} HOUR)")
    int countIpRecentCodes(@Param("ipAddress") String ipAddress,
                          @Param("codeType") String codeType,
                          @Param("hours") int hours);

    /**
     * 统计用户相关IP地址近期验证码发送次数
     * @param target 目标（手机号/邮箱）
     * @param codeType 验证码类型
     * @param hours 小时数（查询多少小时内的记录）
     * @return 近期发送次数
     */
    @Select("SELECT COUNT(*) FROM aicg_verify_code " +
            "WHERE target = #{target} AND code_type = #{codeType} " +
            "AND create_time > DATE_SUB(NOW(), INTERVAL #{hours} HOUR)")
    int countUserRelatedIpRecentCodes(@Param("target") String target,
                                     @Param("codeType") String codeType,
                                     @Param("hours") int hours);

    /**
     * 删除IP地址的近期验证码记录（用于解除IP限制）
     * @param ipAddress IP地址
     * @param codeType 验证码类型
     * @param hours 小时数（删除多少小时内的记录）
     * @return 删除的记录数
     */
    @Delete("DELETE FROM aicg_verify_code " +
            "WHERE ip_address = #{ipAddress} AND code_type = #{codeType} " +
            "AND create_time > DATE_SUB(NOW(), INTERVAL #{hours} HOUR)")
    int deleteIpRecentRecords(@Param("ipAddress") String ipAddress,
                             @Param("codeType") String codeType,
                             @Param("hours") int hours);
}
