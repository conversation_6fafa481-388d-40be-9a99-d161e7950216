package org.jeecg.modules.jianying.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 火山引擎TOS对象存储配置
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
@Data
@Component
@ConfigurationProperties(prefix = "volcengine.tos")
public class TosConfig {
    
    /**
     * 访问密钥ID
     */
    private String accessKey;
    
    /**
     * 访问密钥Secret
     */
    private String secretKey;
    
    /**
     * TOS服务访问域名
     */
    private String endpoint;
    
    /**
     * 地域信息
     */
    private String region;
    
    /**
     * 存储桶名称
     */
    private String bucket;
    
    /**
     * 文件保留天数（默认5天）
     */
    private Integer fileRetentionDays = 5;
    
    /**
     * 是否启用自动清理
     */
    private Boolean autoCleanup = true;
    
    /**
     * 清理时间（默认凌晨2点）
     */
    private String cleanupTime = "02:00";
    
    /**
     * 文件存储基础路径
     */
    private String basePath = "/jianying-assistant/drafts/";
    
    /**
     * 文件名前缀
     */
    private String filePrefix = "zj_draft_";

    /**
     * 🆕 内网访问配置
     */
    private Internal internal = new Internal();

    @Data
    public static class Internal {
        /**
         * 内网端点
         */
        private String endpoint = "tos-cn-shanghai.ivolces.com";

        /**
         * 是否启用内网访问
         */
        private Boolean enabled = true;
    }
}
