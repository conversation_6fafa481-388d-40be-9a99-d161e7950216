<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form-model ref="form" :model="model" :rules="validatorRules" slot="detail">
        <a-row>
          <a-col :span="24">
            <a-form-model-item label="插件名称" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="plubname">
              <a-input v-model="model.plubname" placeholder="请输入插件名称"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="图片" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="plubimg">
              <j-image-upload isMultiple  v-model="model.plubimg" ></j-image-upload>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="插件创作者" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="plubwrite">
              <j-dict-select-tag type="list" v-model="model.plubwrite" dictCode="aigc_plub_author,authorname,id" placeholder="请选择插件创作者" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="插件介绍" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="plubinfo">
              <a-textarea v-model="model.plubinfo" rows="4" placeholder="请输入插件介绍" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="插件教程视频" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="plubvideo">
              <a-button @click="showVideoUploadTip" disabled style="width: 100%">
                <a-icon type="upload" />
                暂不支持上传文件
              </a-button>
              <div style="margin-top: 8px; color: #999; font-size: 12px;">
                视频上传功能暂时禁用，请使用下方的教程链接字段
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="插件教程链接" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="tutorialLink">
              <a-input
                v-model="model.tutorialLink"
                placeholder="请输入插件教程链接，如：https://www.example.com/tutorial"
                style="width: 100%"
              >
                <a-icon slot="prefix" type="link" />
              </a-input>
              <div style="margin-top: 8px; color: #666; font-size: 12px;">
                <a-icon type="info-circle" style="margin-right: 4px;" />
                支持外部视频链接，如B站、YouTube、腾讯视频等平台链接
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="收益信息" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="income">
              <a-input-number v-model="model.income" placeholder="请输入收益信息" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="调用次数" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="usernum">
              <a-input-number v-model="model.usernum" placeholder="请输入调用次数" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="需要点数" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="neednum">
              <a-input-number v-model="model.neednum" placeholder="请输入需要点数" style="width: 100%" />
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </j-form-container>
  </a-spin>
</template>

<script>

  import { httpAction, getAction } from '@/api/manage'
  import { validateDuplicateValue } from '@/utils/util'

  export default {
    name: 'AigcPlubShopForm',
    components: {
    },
    props: {
      //表单禁用
      disabled: {
        type: Boolean,
        default: false,
        required: false
      }
    },
    data () {
      return {
        model:{
         },
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 },
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 },
        },
        confirmLoading: false,
        validatorRules: {
           plubname: [
              { required: true, message: '请输入插件名称!'},
           ],
           plubwrite: [
              { required: true, message: '请输入插件创作者!'},
           ],
           plubinfo: [
              { required: true, message: '请输入插件介绍!'},
           ],
           tutorialLink: [
              { pattern: /^https?:\/\/.+/, message: '请输入有效的链接地址，必须以http://或https://开头!'},
           ],
        },
        url: {
          add: "/plubshop/aigcPlubShop/add",
          edit: "/plubshop/aigcPlubShop/edit",
          queryById: "/plubshop/aigcPlubShop/queryById"
        }
      }
    },
    computed: {
      formDisabled(){
        return this.disabled
      },
    },
    created () {
       //备份model原始值
      this.modelDefault = JSON.parse(JSON.stringify(this.model));
    },
    methods: {
      add () {
        this.edit(this.modelDefault);
      },
      edit (record) {
        this.model = Object.assign({}, record);
        this.visible = true;
      },
      submitForm () {
        const that = this;
        // 触发表单验证
        this.$refs.form.validate(valid => {
          if (valid) {
            that.confirmLoading = true;
            let httpurl = '';
            let method = '';
            if(!this.model.id){
              httpurl+=this.url.add;
              method = 'post';
            }else{
              httpurl+=this.url.edit;
               method = 'put';
            }
            httpAction(httpurl,this.model,method).then((res)=>{
              if(res.success){
                that.$message.success(res.message);
                that.$emit('ok');
              }else{
                that.$message.warning(res.message);
              }
            }).finally(() => {
              that.confirmLoading = false;
            })
          }

        })
      },

      // 显示视频上传提示
      showVideoUploadTip() {
        this.$message.info('暂不支持上传文件，请使用教程链接字段输入外部视频链接');
      },
    }
  }
</script>