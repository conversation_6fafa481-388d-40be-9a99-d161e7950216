package org.jeecg.modules.system.util;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.constant.CommonConstant;
import org.jeecg.common.util.IPUtils;
import org.jeecg.common.util.RedisUtil;
import org.jeecg.common.system.util.JwtUtil;
import org.jeecg.modules.system.entity.SysUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;

/**
 * @Description: 单点登录管理工具类
 * @Author: jeecg-boot
 * @Date: 2025-06-21
 * @Version: V1.0
 */
@Slf4j
@Component
public class SingleLoginManager {

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private RoleChecker roleChecker;

    @Autowired
    private AnnouncementManager announcementManager;
    
    /**
     * 处理单点登录逻辑（统一入口）
     * @param user 用户信息
     * @param newToken 新Token
     * @param request 请求对象
     */
    public void handleSingleLogin(SysUser user, String newToken, HttpServletRequest request) {
        handleSingleLogin(user, newToken, request, false);
    }

    /**
     * 处理单点登录逻辑（完整版）
     * @param user 用户信息
     * @param newToken 新Token
     * @param request 请求对象
     * @param isForceLogin 是否强制登录
     */
    public void handleSingleLogin(SysUser user, String newToken, HttpServletRequest request, boolean isForceLogin) {
        try {
            // 1. 检查是否admin
            boolean isAdmin = roleChecker.checkUserIsAdmin(user.getId());

            if (isAdmin) {
                log.debug("admin用户，允许多设备登录，用户：{}", user.getUsername());
                return;
            }

            // 2. 获取用户当前Token
            String currentTokenKey = "current_user_token_" + user.getId();
            log.info("检查Redis Key：{}，强制登录：{}", currentTokenKey, isForceLogin);
            Object currentTokenObj = redisUtil.get(currentTokenKey);
            log.info("当前Token对象：{}", currentTokenObj != null ? "存在" : "不存在");

            // 3. 如果有旧Token，检查是否需要踢下线
            if (currentTokenObj != null) {
                String currentToken = String.valueOf(currentTokenObj);
                log.info("发现旧Token：{}", currentToken.substring(0, Math.min(8, currentToken.length())) + "***");

                // 🆕 严格比较Token，确保不会错误踢下线相同Token
                if (!currentToken.equals(newToken)) {
                    // 🆕 强制登录时直接踢下线，无需额外检查
                    if (isForceLogin) {
                        log.warn("*** 强制登录：直接踢下线旧Token ***");
                    } else {
                        log.warn("*** 检测到Token冲突！准备踢下线 ***");
                    }

                    log.info("旧Token：{}", currentToken.substring(0, Math.min(8, currentToken.length())) + "***");
                    log.info("新Token：{}", newToken.substring(0, Math.min(8, newToken.length())) + "***");

                    // 🆕 双重验证：确保旧Token确实不同且有效
                    try {
                        String oldUsername = JwtUtil.getUsername(currentToken);
                        String newUsername = JwtUtil.getUsername(newToken);

                        if (oldUsername != null && newUsername != null && oldUsername.equals(newUsername)) {
                            // 标记旧Token为被踢状态
                            markTokenAsKicked(currentToken, request);

                            // 🆕 强制登录时创建不同的系统通告
                            if (request != null) {
                                int announcementType = isForceLogin ? 2 : 1; // 2表示强制登录，1表示自动踢下线
                                announcementManager.createKickOffAnnouncement(user, request, announcementType);
                                log.info("已创建{}系统通告", isForceLogin ? "强制登录" : "踢下线");
                            } else {
                                log.warn("request为null，无法创建系统通告");
                            }

                            // 🆕 立即设置旧Token短期过期（5秒后清理，确保用户体验）
                            redisUtil.expire(CommonConstant.PREFIX_USER_TOKEN + currentToken, 5);
                        } else {
                            log.warn("Token用户名不匹配，跳过踢下线操作");
                        }
                    } catch (Exception e) {
                        log.error("Token解析失败，跳过踢下线操作", e);
                    }
                } else {
                    log.info("Token相同，无需踢下线");
                }
            } else {
                log.info("非admin用户 {} 首次登录或无现有Token", user.getUsername());
            }

            // 4. 🆕 原子性记录新Token（使用带时间的set方法避免set+expire的原子性问题）
            log.info("记录新Token到Redis：{}", currentTokenKey);
            long expireSeconds = JwtUtil.EXPIRE_TIME * 2 / 1000;
            redisUtil.set(currentTokenKey, newToken, expireSeconds);
            log.info("新Token记录完成（原子操作）");

        } catch (Exception e) {
            log.error("处理单点登录失败，用户：{}，错误：{}", user.getUsername(), e.getMessage(), e);
        }
    }

    /**
     * 强制踢下线指定Token
     * @param existingToken 要踢下线的Token
     * @param request 请求对象
     */
    public void handleKickOff(String existingToken, HttpServletRequest request) {
        try {
            log.info("强制踢下线Token：{}", existingToken.substring(0, Math.min(8, existingToken.length())) + "***");

            // 标记Token为被踢状态
            markTokenAsKicked(existingToken, request);

            // 🆕 立即设置Token短期过期（5秒后清理，确保用户体验）
            redisUtil.expire(CommonConstant.PREFIX_USER_TOKEN + existingToken, 5);

        } catch (Exception e) {
            log.error("强制踢下线失败", e);
        }
    }
    
    /**
     * 标记Token被踢下线
     * @param token Token值
     * @param request 请求对象
     */
    private void markTokenAsKicked(String token, HttpServletRequest request) {
        try {
            String kickedKey = "kicked_token_" + token;
            log.info("=== 开始标记Token被踢下线 ===");
            log.info("Token前缀：{}", token.substring(0, Math.min(8, token.length())) + "***");
            log.info("Redis Key：{}", kickedKey);

            JSONObject kickInfo = new JSONObject();
            kickInfo.put("kicked", true);
            kickInfo.put("kickTime", System.currentTimeMillis());
            if (request != null) {
                kickInfo.put("newDevice", parseDeviceInfo(request));
                kickInfo.put("newIP", IPUtils.getIpAddr(request));
            }
            kickInfo.put("reason", "其他设备/浏览器登录");

            String kickInfoStr = kickInfo.toJSONString();
            log.info("踢下线信息：{}", kickInfoStr);

            // 🆕 原子性存储到Redis（使用带时间的set方法避免set+expire的原子性问题）
            boolean setResult = redisUtil.set(kickedKey, kickInfoStr, 300); // 5分钟过期
            log.info("Redis原子性存储结果：{}", setResult);

            // 验证存储是否成功
            Object verifyObj = redisUtil.get(kickedKey);
            log.info("验证Redis存储：{}", verifyObj);

            log.info("标记Token被踢下线完成，Token：{}", token.substring(0, Math.min(8, token.length())) + "***");
        } catch (Exception e) {
            log.error("标记Token被踢下线失败", e);
        }
    }


    
    /**
     * 解析设备信息
     * @param request 请求对象
     * @return 设备信息字符串
     */
    public String parseDeviceInfo(HttpServletRequest request) {
        String userAgent = request.getHeader("User-Agent");
        String ip = IPUtils.getIpAddr(request);
        
        // 简单解析浏览器和操作系统
        String browser = "未知浏览器";
        String os = "未知系统";
        
        if (userAgent != null) {
            if (userAgent.contains("Chrome")) browser = "Chrome";
            else if (userAgent.contains("Firefox")) browser = "Firefox";
            else if (userAgent.contains("Safari")) browser = "Safari";
            else if (userAgent.contains("Edge")) browser = "Edge";
            
            if (userAgent.contains("Windows")) os = "Windows";
            else if (userAgent.contains("Mac")) os = "Mac";
            else if (userAgent.contains("Linux")) os = "Linux";
            else if (userAgent.contains("Android")) os = "Android";
            else if (userAgent.contains("iPhone")) os = "iPhone";
        }
        
        return String.format("%s浏览器 (%s) - %s", browser, os, ip);
    }
}
