<template>
  <div class="tech-login">
    <!-- 动态背景层 -->
    <div class="dynamic-background">
      <!-- 动态网格 -->
      <div class="grid-background"></div>

      <!-- 粒子系统 -->
      <div class="particles-container">
        <div class="particle" v-for="n in particleCount" :key="n" :style="getParticleStyle(n)"></div>
      </div>
    </div>

    <div class="login-container">
      <!-- 登录卡片 -->
      <div class="login-card" ref="loginCard">
        <!-- 头部 -->
        <div class="login-header">
          <div class="logo-section">
            <div class="logo-icon">
              <a-icon type="thunderbolt" />
            </div>
            <h1 class="system-title">智界AIGC</h1>
            <p class="system-subtitle">后台管理系统</p>
          </div>
        </div>

        <!-- 登录表单 -->
        <div class="login-form-section">
          <div class="form-header">
            <h2 class="form-title">登录账户</h2>
            <p class="form-subtitle">请输入您的账号密码</p>
          </div>

          <a-form :form="form" class="login-form" @submit="handleSubmit">
            <login-account
              ref="alogin"
              @validateFail="validateFail"
              @success="requestSuccess"
              @fail="requestFailed"
              @keyup.enter.native="handleSubmit"
            ></login-account>

            <a-form-item class="login-button-item">
              <a-button
                size="large"
                type="primary"
                htmlType="submit"
                class="login-submit-button"
                :loading="loginBtn"
                @click.stop.prevent="handleSubmit"
                :disabled="loginBtn"
                block
              >
                <span v-if="!loginBtn">
                  <a-icon type="login" style="margin-right: 8px;" />
                  登录系统
                </span>
                <span v-else>
                  <a-icon type="loading" style="margin-right: 8px;" />
                  验证中...
                </span>
              </a-button>
            </a-form-item>
          </a-form>

          <!-- 访问官网按钮 -->
          <div class="website-button-section">
            <a-button
              size="large"
              type="default"
              class="website-button"
              @click="goToWebsite"
              block
            >
              <a-icon type="home" />
              探索智界AIGC官网
            </a-button>
          </div>

          <!-- 底部链接 -->
          <div class="login-footer">
            <div class="footer-links">
              <a href="#" class="footer-link">
                <a-icon type="question-circle" />
                忘记密码
              </a>
              <a href="#" class="footer-link">
                <a-icon type="customer-service" />
                技术支持
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 保留原有的功能组件 -->
    <two-step-captcha
      v-if="requiredTwoStepCaptcha"
      :visible="stepCaptchaVisible"
      @success="stepCaptchaSuccess"
      @cancel="stepCaptchaCancel"
    ></two-step-captcha>
    <login-select-tenant ref="loginSelect" @success="loginSelectOk"></login-select-tenant>
  </div>
</template>

<script>
import Vue from 'vue'
import { ACCESS_TOKEN, ENCRYPTED_STRING, USER_NAME, USER_INFO, USER_AUTH, SYS_BUTTON_AUTH, UI_CACHE_DB_DICT_DATA, TENANT_ID, CACHE_INCLUDED_ROUTES } from "@/store/mutation-types"

import ThirdLogin from './third/ThirdLogin'
import LoginSelectTenant from "./LoginSelectTenant"
import TwoStepCaptcha from '@/components/tools/TwoStepCaptcha'
import { encryption, getEncryptedString } from '@/utils/encryption/aesEncrypt'
import { timeFix } from "@/utils/util"
import { deleteAction, getAction, putAction } from "@api/manage";

import LoginAccount from './LoginAccount'
import LoginPhone from './LoginPhone'

export default {
  components: {
    LoginSelectTenant,
    TwoStepCaptcha,
    ThirdLogin,
    LoginAccount,
    LoginPhone
  },
  data () {
    return {
      customActiveKey: 'tab1',
      rememberMe: true,
      loginBtn: false,
      requiredTwoStepCaptcha: false,
      stepCaptchaVisible: false,
      encryptedString: {
        key: "",
        iv: "",
      },
      aas: '',
      paths: '/dashboard/analysis',
      // 添加缺失的form对象
      form: this.$form.createForm(this),
      // 粒子系统配置
      particleCount: this.isMobile() ? 20 : 40,

      // 新增的UI数据
      features: [
        {
          icon: 'dashboard',
          title: '智能仪表板',
          description: '实时监控系统状态和业务数据'
        },
        {
          icon: 'api',
          title: 'API管理',
          description: '统一管理所有API接口和调用'
        },
        {
          icon: 'team',
          title: '用户管理',
          description: '完善的用户权限和角色管理'
        },
        {
          icon: 'setting',
          title: '系统配置',
          description: '灵活的系统参数和功能配置'
        }
      ]
    }
  },
  created () {
    Vue.ls.remove(ACCESS_TOKEN)
    this.getRouterData();
    this.rememberMe = true
    localStorage.clear();
  },
  mounted () {
    // 初始化页面动画
    this.initPageAnimations()

    console.log(this.$route.query)
    var name = this.$route.query.name
    if (name == undefined) return;
    if (name.slice(0, 6) == 'zszxsq') {
      var oneName = name.slice(6)
      var twoName = oneName.split('iwoaksqdsja')[0]
      var params = {
        username: twoName
      }
      this.axios.post('/sys/mmlogin', params).then(response => {
        if (response.code == '200') {
          const result = response.result
          const userInfo = result.userInfo
          Vue.ls.set(ACCESS_TOKEN, result.token, 7 * 24 * 60 * 60 * 1000)
          Vue.ls.set(USER_NAME, userInfo.username, 7 * 24 * 60 * 60 * 1000)
          Vue.ls.set(USER_INFO, userInfo, 7 * 24 * 60 * 60 * 1000)
          Vue.ls.set(UI_CACHE_DB_DICT_DATA, result.sysAllDictItems, 7 * 24 * 60 * 60 * 1000)
          this.aas = userInfo.realname;
          localStorage.setItem('realname', result.role);
          localStorage.setItem('city', result.city);
          localStorage.setItem('county', result.county);
          if (name.slice(0, 6) == 'zszxsq') {
            this.paths = '/dashboard/analysis'
            this.intos()
          }
        }
      })
    }
  },

  methods: {
    // 检测是否为移动设备
    isMobile() {
      return window.innerWidth <= 768;
    },

    // 生成粒子样式
    getParticleStyle(index) {
      const size = Math.random() * 4 + 2; // 2-6px
      const duration = Math.random() * 15 + 10; // 10-25s
      const delay = Math.random() * 20; // 0-20s延迟
      const x = Math.random() * 100; // 0-100%
      const y = Math.random() * 100; // 0-100%
      const opacity = Math.random() * 0.3 + 0.3; // 0.3-0.6

      return {
        width: `${size}px`,
        height: `${size}px`,
        left: `${x}%`,
        top: `${y}%`,
        animationDuration: `${duration}s`,
        animationDelay: `${delay}s`,
        opacity: opacity
      };
    },

    // 初始化页面动画
    initPageAnimations() {
      this.$nextTick(() => {
        // 左侧品牌区域动画
        if (this.$refs.loginBrand) {
          this.$refs.loginBrand.style.opacity = '0'
          this.$refs.loginBrand.style.transform = 'translateX(-50px)'

          setTimeout(() => {
            this.$refs.loginBrand.style.transition = 'all 0.8s ease'
            this.$refs.loginBrand.style.opacity = '1'
            this.$refs.loginBrand.style.transform = 'translateX(0)'
          }, 200)
        }

        // 右侧登录容器动画
        if (this.$refs.loginContainer) {
          this.$refs.loginContainer.style.opacity = '0'
          this.$refs.loginContainer.style.transform = 'translateX(50px)'

          setTimeout(() => {
            this.$refs.loginContainer.style.transition = 'all 0.8s ease'
            this.$refs.loginContainer.style.opacity = '1'
            this.$refs.loginContainer.style.transform = 'translateX(0)'
          }, 400)
        }
      })
    },

    handleTabClick (key) {
      this.customActiveKey = key
    },
    handleRememberMeChange (e) {
      this.rememberMe = e.target.checked
    },
    /**跳转到登录页面的参数-账号获取*/
    getRouterData () {
      this.$nextTick(() => {
        let temp = this.$route.params.username || this.$route.query.username || ''
        if (temp) {
          this.$refs.alogin.acceptUsername(temp)
        }
      })
    },

    //登录
    handleSubmit () {
      this.loginBtn = true;
      if (this.customActiveKey === 'tab1') {
        // 使用账户密码登录
        this.$refs.alogin.handleLogin(this.rememberMe)
      } else {
        //手机号码登录
        this.$refs.plogin.handleLogin(this.rememberMe)
      }
    },
    // 校验失败
    validateFail () {
      this.loginBtn = false;
    },
    // 登录后台成功
    requestSuccess (loginResult) {
      console.log(loginResult.userInfo)
      this.aas = loginResult.userInfo.realname
      this.$refs.loginSelect.show(loginResult)

      let url = "/sys/user/getCurrentUserDeparts"
      getAction(url).then(res => {
        if (res.success) {
          let role = res.result.role
          localStorage.setItem('userRole', role); // ✅ 统一使用 userRole
          let departId = res.result.departId
          localStorage.setItem('departId', departId);
        }
      })
    },
    intos () {
      this.$router.push({ path: this.paths }).catch(() => {
        console.log('登录跳转首页出错,这个错误从哪里来的')
      })
      this.$notification.success({
        message: '欢迎',
        description: `${timeFix()}，欢迎回来`,
      });
    },
    //登录后台失败
    requestFailed (err) {
      let description = ((err.response || {}).data || {}).message || err.message || "请求出现错误，请稍后再试"

      // 检查是否是权限不足错误（非admin角色）
      if (description === 'ACCESS_DENIED') {
        // 跳转到后台404页面，并提示用户返回官网
        this.$router.push('/404')
        return
      }

      this.$notification['error']({
        message: '登录失败',
        description: description,
        duration: 4,
      });
      //账户密码登录错误后更新验证码
      if (this.customActiveKey === 'tab1' && description.indexOf('密码错误') > 0) {
        this.$refs.alogin.handleChangeCheckCode()
      }
      this.loginBtn = false;
    },
    loginSelectOk () {
      this.loginSuccess()
    },
    //登录成功
    async loginSuccess () {
      console.log(this.aas)

      // ✅ 等待角色信息获取完成后再跳转
      try {
        console.log('🔄 等待角色信息获取完成...')
        // 等待一小段时间确保 requestSuccess 中的角色获取完成
        await new Promise(resolve => setTimeout(resolve, 500))

        // 验证角色是否已获取
        const role = localStorage.getItem('userRole')
        console.log('🔍 获取到的角色:', role)

        if (role === 'admin') {
          console.log('✅ 管理员角色确认，跳转到根路径让路由守卫处理')
          this.$router.push({ path: "/" }).catch(() => {
            console.log('登录跳转首页出错,这个错误从哪里来的')
          })
        } else {
          console.log('❌ 非管理员角色，跳转到官网')
          this.$router.push({ path: "/home" }).catch(() => {
            console.log('跳转到官网出错')
          })
        }
      } catch (error) {
        console.error('❌ 角色验证失败:', error)
        this.$router.push({ path: "/home" }).catch(() => {
          console.log('跳转到官网出错')
        })
      }

      this.$notification.success({
        message: '欢迎',
        description: `${timeFix()}，欢迎回来`,
      });
    },

    stepCaptchaSuccess () {
      this.loginSuccess()
    },
    stepCaptchaCancel () {
      this.Logout().then(() => {
        this.loginBtn = false
        this.stepCaptchaVisible = false
      })
    },
    //获取密码加密规则
    getEncrypte () {
      var encryptedString = Vue.ls.get(ENCRYPTED_STRING);
      if (encryptedString == null) {
        getEncryptedString().then((data) => {
          this.encryptedString = data
        });
      } else {
        this.encryptedString = encryptedString;
      }
    },

    // 跳转到官网首页
    goToWebsite () {
      this.$router.push({ path: '/home' }).catch(() => {
        console.log('跳转到官网首页出错')
      })
    }

  }

}
</script>
<style scoped>
/* 科技风格登录页面 */
.tech-login {
  width: 100%;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  margin: 0;
  position: relative;
  overflow: hidden;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 50%, #f1f5f9 100%);
}

/* 动态背景层 */
.dynamic-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  pointer-events: none;
}

/* 动态网格背景 */
.grid-background {
  position: absolute;
  width: 100%;
  height: 100%;
  background-image:
    linear-gradient(rgba(59, 130, 246, 0.15) 1px, transparent 1px),
    linear-gradient(90deg, rgba(59, 130, 246, 0.15) 1px, transparent 1px);
  background-size: 50px 50px;
  animation: gridMove 30s linear infinite, gridPulse 4s ease-in-out infinite alternate;
}

@keyframes gridMove {
  0% {
    transform: translate(0, 0);
  }
  100% {
    transform: translate(50px, 50px);
  }
}

@keyframes gridPulse {
  0% {
    opacity: 0.3;
  }
  100% {
    opacity: 0.6;
  }
}

/* 粒子系统 */
.particles-container {
  position: absolute;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.particle {
  position: absolute;
  background: #3b82f6;
  border-radius: 50%;
  animation: particleFloat infinite linear, particleGlow 3s ease-in-out infinite alternate;
  box-shadow: 0 0 6px rgba(59, 130, 246, 0.4);
}

@keyframes particleFloat {
  0% {
    transform: translateY(100vh) translateX(0) rotate(0deg);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(-100px) translateX(50px) rotate(360deg);
    opacity: 0;
  }
}

@keyframes particleGlow {
  0% {
    box-shadow: 0 0 6px rgba(59, 130, 246, 0.4);
  }
  100% {
    box-shadow: 0 0 12px rgba(59, 130, 246, 0.8);
  }
}

/* 登录容器 */
.login-container {
  width: 100%;
  max-width: 480px;
  margin: 0 auto;
  position: relative;
  z-index: 2;
}

/* 登录卡片 */
.login-card {
  background: #ffffff;
  border-radius: 16px;
  border: 1px solid #e5e7eb;
  box-shadow:
    0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04);
  overflow: hidden;
}

/* 登录头部 */
.login-header {
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  padding: 40px 40px 30px;
  text-align: center;
  position: relative;
  border-bottom: 1px solid #e5e7eb;
}

.login-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse"><path d="M 20 0 L 0 0 0 20" fill="none" stroke="rgba(59,130,246,0.05)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  opacity: 0.6;
}

.logo-section {
  position: relative;
  z-index: 1;
}

.logo-icon {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20px;
  box-shadow: 0 4px 14px 0 rgba(59, 130, 246, 0.3);
}

.logo-icon .anticon {
  font-size: 28px;
  color: white;
}

.system-title {
  font-size: 28px;
  font-weight: 700;
  color: #1f2937;
  margin: 0 0 8px 0;
  letter-spacing: 1px;
}

.system-subtitle {
  font-size: 14px;
  color: #6b7280;
  margin: 0;
  font-weight: 400;
}

/* 登录表单区域 */
.login-form-section {
  padding: 40px;
  background: #ffffff;
}

.form-header {
  text-align: center;
  margin-bottom: 30px;
}

.form-title {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 8px 0;
}

.form-subtitle {
  font-size: 14px;
  color: #6b7280;
  margin: 0;
  line-height: 1.5;
}

/* 登录表单 */
.login-form {
  margin-bottom: 30px;
}

/* 登录按钮 */
.login-button-item {
  margin-bottom: 0;
}

.login-submit-button {
  height: 50px;
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
  border: none;
  border-radius: 8px;
  font-weight: 600;
  font-size: 16px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 14px 0 rgba(59, 130, 246, 0.3);
}

.login-submit-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px 0 rgba(59, 130, 246, 0.4);
  background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
}

.login-submit-button:active {
  transform: translateY(0);
}

.login-submit-button:focus {
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
}

/* 官网按钮区域 */
.website-button-section {
  margin-top: 24px;
  margin-bottom: 24px;
  position: relative;
}

.website-button-section::before {
  content: '';
  position: absolute;
  top: -12px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 1px;
  background: linear-gradient(90deg, transparent, #e5e7eb, transparent);
}

.website-button {
  height: 48px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border: 1px solid #cbd5e1;
  border-radius: 8px;
  font-weight: 600;
  font-size: 15px;
  color: #475569;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.06);
  position: relative;
  overflow: hidden;
}

.website-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.1), transparent);
  transition: left 0.5s ease;
}

.website-button:hover::before {
  left: 100%;
}

.website-button:hover {
  color: #3b82f6;
  border-color: #3b82f6;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 20px 0 rgba(59, 130, 246, 0.2);
}

.website-button:active {
  transform: translateY(-1px);
}

.website-button:focus {
  color: #3b82f6;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.15);
}

.website-button .anticon {
  font-size: 16px;
  margin-right: 8px;
}

/* 登录页脚 */
.login-footer {
  text-align: center;
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #f3f4f6;
}

.footer-links {
  display: flex;
  justify-content: center;
  gap: 30px;
}

.footer-link {
  color: #6b7280;
  text-decoration: none;
  font-weight: 400;
  font-size: 14px;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 6px;
}

.footer-link:hover {
  color: #3b82f6;
}

.footer-link .anticon {
  font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .login-container {
    max-width: 420px;
    margin: 0 20px;
  }

  .login-header {
    padding: 30px 30px 25px;
  }

  .login-form-section {
    padding: 30px;
  }

  .system-title {
    font-size: 24px;
  }

  .form-title {
    font-size: 20px;
  }

  .footer-links {
    flex-direction: column;
    gap: 20px;
  }
}

@media (max-width: 480px) {
  .login-container {
    margin: 0 15px;
  }

  .login-header {
    padding: 25px 20px 20px;
  }

  .login-form-section {
    padding: 25px 20px;
  }

  .system-title {
    font-size: 22px;
  }

  .form-title {
    font-size: 18px;
  }

  .logo-icon {
    width: 50px;
    height: 50px;
  }

  .logo-icon .anticon {
    font-size: 24px;
  }

  .login-submit-button {
    height: 46px;
    font-size: 15px;
  }

  .website-button {
    height: 46px;
    font-size: 14px;
  }

  .website-button .anticon {
    font-size: 15px;
  }
}
</style>

<style>
.valid-error .ant-select-selection__placeholder {
  color: #f5222d;
}

/* 现代简约表单样式 */
.tech-login .ant-input-affix-wrapper,
.tech-login .ant-input {
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  background: #ffffff;
  transition: all 0.3s ease;
  height: 46px;
  font-size: 14px;
  color: #1f2937;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.tech-login .ant-input-affix-wrapper:hover,
.tech-login .ant-input:hover {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.tech-login .ant-input-affix-wrapper:focus,
.tech-login .ant-input:focus,
.tech-login .ant-input-affix-wrapper-focused {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
}

.tech-login .ant-input-prefix {
  color: #6b7280;
  font-size: 16px;
}

.tech-login .ant-input::placeholder {
  color: #9ca3af;
}

.tech-login .ant-form-item {
  margin-bottom: 24px;
}

.tech-login .ant-form-item-label {
  font-weight: 500;
  color: #374151;
}

/* 验证码图片样式 */
.tech-login img {
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid #e5e7eb;
  height: 44px;
  background: #f9fafb;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.tech-login img:hover {
  border-color: #3b82f6;
  transform: scale(1.02);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

/* 表单验证错误样式 */
.tech-login .ant-form-item-has-error .ant-input,
.tech-login .ant-form-item-has-error .ant-input-affix-wrapper {
  border-color: #ef4444;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.tech-login .ant-form-item-has-error .ant-input:hover,
.tech-login .ant-form-item-has-error .ant-input-affix-wrapper:hover {
  border-color: #ef4444;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.2);
}

.tech-login .ant-form-explain {
  color: #ef4444;
  font-size: 12px;
  margin-top: 6px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .grid-background {
    background-size: 30px 30px;
    animation-duration: 20s;
  }

  .particles-container .particle {
    display: none;
  }

  .particles-container .particle:nth-child(-n+15) {
    display: block;
  }

  .login-container {
    max-width: 420px;
    margin: 0 20px;
  }
}

@media (max-width: 480px) {
  .grid-background {
    background-size: 25px 25px;
    opacity: 0.3;
  }

  .particles-container .particle:nth-child(-n+15) {
    display: none;
  }

  .particles-container .particle:nth-child(-n+8) {
    display: block;
  }

  .login-container {
    margin: 0 15px;
  }
}

/* 性能优化 - 减少动画在低性能设备上的影响 */
@media (prefers-reduced-motion: reduce) {
  .grid-background {
    animation: none;
  }

  .particle {
    animation: particleFloat 20s linear infinite;
  }
}
</style>