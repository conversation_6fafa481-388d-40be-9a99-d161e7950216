import TOSBase from '../base';
interface TagSet {
    Tags: {
        Key: string;
        Value: string;
    }[];
}
export interface PutObjectTaggingInput {
    bucket: string;
    key: string;
    versionId?: string;
    tagSet: TagSet;
}
export interface PutObjectTaggingOutput {
}
export declare function putObjectTagging(this: TOSBase, input: PutObjectTaggingInput): Promise<import("../base").TosResponse<PutObjectTaggingOutput>>;
export interface GetObjectTaggingInput {
    bucket: string;
    key: string;
    versionId?: string;
}
export interface GetObjectTaggingOutput {
    TagSet: TagSet;
}
export declare function getObjectTagging(this: TOSBase, input: GetObjectTaggingInput): Promise<import("../base").TosResponse<GetObjectTaggingOutput>>;
export interface DeleteObjectTaggingInput {
    bucket: string;
    key: string;
    versionId?: string;
}
export interface DeleteObjectTaggingOutput {
}
export declare function deleteObjectTagging(this: TOSBase, input: DeleteObjectTaggingInput): Promise<import("../base").TosResponse<DeleteObjectTaggingOutput>>;
export {};
