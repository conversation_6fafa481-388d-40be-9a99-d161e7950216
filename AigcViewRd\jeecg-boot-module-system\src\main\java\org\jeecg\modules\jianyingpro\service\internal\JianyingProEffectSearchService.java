package org.jeecg.modules.jianyingpro.service.internal;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.jianying.dto.EffectInfo;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import javax.annotation.PostConstruct;

/**
 * 超级剪映小助手 - 特效搜索服务
 * 复制自JianyingEffectSearchService，保持所有原有业务逻辑不变
 * 
 * <AUTHOR>
 * @since 2025-01-01
 */
@Slf4j
@Service
public class JianyingProEffectSearchService {
    
    private static final String EFFECT_SEARCH_URL = "https://lv-effect.ulikecam.com/effect/api/search/effects";
    
    // 剪映API固定参数
    private static final String ACCESS_KEY = "0051d530508b11e9b441ed975323ebf8";
    private static final String DEVICE_ID = "3959755708596347";
    private static final String DEVICE_TYPE = "x86_64";
    private static final String PANEL = "effects2";
    private static final String DEVICE_PLATFORM = "windows";
    private static final String APP_VERSION = "5.9.0";
    private static final String APP_LANGUAGE = "zh-Hans";
    private static final String SDK_VERSION = "16.4.0";
    
    // 本地缓存，避免重复API调用
    private final Map<String, CacheEntry> effectCache = new ConcurrentHashMap<>();

    // 缓存过期时间（24小时）
    private static final long CACHE_EXPIRE_TIME = 24 * 60 * 60 * 1000L;

    // 预定义的常用特效映射（降级策略）
    private static final Map<String, EffectInfo> DEFAULT_EFFECT_MAPPING = new HashMap<>();

    private final RestTemplate restTemplate = new RestTemplate();

    /**
     * 缓存条目
     */
    private static class CacheEntry {
        private final EffectInfo effectInfo;
        private final long timestamp;

        public CacheEntry(EffectInfo effectInfo) {
            this.effectInfo = effectInfo;
            this.timestamp = System.currentTimeMillis();
        }

        public EffectInfo getEffectInfo() {
            return effectInfo;
        }

        public boolean isExpired() {
            return System.currentTimeMillis() - timestamp > CACHE_EXPIRE_TIME;
        }
    }

    /**
     * 初始化预定义特效映射
     */
    @PostConstruct
    public void initDefaultEffectMapping() {
        // 添加一些常用特效的默认映射（作为降级策略）
        DEFAULT_EFFECT_MAPPING.put("樱花飘落", createDefaultEffect("樱花飘落", "10348463", "7207718227382637113"));
        DEFAULT_EFFECT_MAPPING.put("春日樱花", createDefaultEffect("春日樱花", "1041304", "6927185086685123086"));
        DEFAULT_EFFECT_MAPPING.put("金粉闪闪", createDefaultEffect("金粉闪闪", "10348464", "7207718227382637114"));
        DEFAULT_EFFECT_MAPPING.put("爱心飘落", createDefaultEffect("爱心飘落", "10348465", "7207718227382637115"));

        log.info("超级剪映小助手 - 初始化预定义特效映射完成，共 {} 个特效", DEFAULT_EFFECT_MAPPING.size());
    }

    /**
     * 创建默认特效信息
     */
    private EffectInfo createDefaultEffect(String title, String effectId, String resourceId) {
        EffectInfo effectInfo = new EffectInfo();
        effectInfo.setName(title);  // 使用setName而不是setTitle
        effectInfo.setEffectId(effectId);
        effectInfo.setResourceId(resourceId);
        // 注释掉setCategory，因为EffectInfo类中没有category字段
        // effectInfo.setCategory("默认特效");
        return effectInfo;
    }
    
    /**
     * 搜索特效信息
     * 
     * @param effectTitle 特效名称
     * @return 特效信息，如果未找到返回null
     */
    public EffectInfo searchEffect(String effectTitle) {
        if (effectTitle == null || effectTitle.trim().isEmpty()) {
            log.warn("特效名称为空，无法搜索");
            return null;
        }
        
        String cleanTitle = effectTitle.trim();
        
        // 先检查缓存
        CacheEntry cacheEntry = effectCache.get(cleanTitle);
        if (cacheEntry != null && !cacheEntry.isExpired()) {
            log.info("从缓存获取特效信息: {}", cleanTitle);
            return cacheEntry.getEffectInfo();
        } else if (cacheEntry != null && cacheEntry.isExpired()) {
            // 缓存过期，移除
            effectCache.remove(cleanTitle);
            log.info("缓存已过期，移除: {}", cleanTitle);
        }
        
        try {
            log.info("开始搜索特效: {}", cleanTitle);
            
            // 构建请求URL
            String url = buildSearchUrl(cleanTitle);
            log.info("请求URL: {}", url);
            
            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36");
            headers.set("Accept", "application/json");
            headers.set("Connection", "keep-alive");
            
            HttpEntity<String> entity = new HttpEntity<>(headers);
            
            // 发送请求
            ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.GET, entity, String.class);
            
            if (response.getStatusCode().is2xxSuccessful()) {
                String responseBody = response.getBody();
                log.info("API响应成功，开始解析结果");
                
                EffectInfo effectInfo = parseEffectResponse(responseBody, cleanTitle);
                
                if (effectInfo != null) {
                    // 缓存结果
                    effectCache.put(cleanTitle, new CacheEntry(effectInfo));
                    log.info("特效搜索成功: {} -> effect_id: {}, resource_id: {}",
                            cleanTitle, effectInfo.getEffectId(), effectInfo.getResourceId());
                    return effectInfo;
                } else {
                    log.warn("API未找到特效: {}，尝试使用默认映射", cleanTitle);
                    // 尝试使用默认映射
                    EffectInfo defaultEffect = DEFAULT_EFFECT_MAPPING.get(cleanTitle);
                    if (defaultEffect != null) {
                        log.info("使用默认特效映射: {} -> effect_id: {}", cleanTitle, defaultEffect.getEffectId());
                        // 缓存默认映射结果
                        effectCache.put(cleanTitle, new CacheEntry(defaultEffect));
                        return defaultEffect;
                    } else {
                        log.warn("未找到特效: {}", cleanTitle);
                        return null;
                    }
                }
            } else {
                log.error("API请求失败，状态码: {}，尝试使用默认映射", response.getStatusCode());
                return getDefaultEffect(cleanTitle);
            }

        } catch (Exception e) {
            log.error("搜索特效失败: {}，尝试使用默认映射", cleanTitle, e);
            return getDefaultEffect(cleanTitle);
        }
    }

    /**
     * 获取默认特效（降级策略）
     */
    private EffectInfo getDefaultEffect(String effectTitle) {
        EffectInfo defaultEffect = DEFAULT_EFFECT_MAPPING.get(effectTitle);
        if (defaultEffect != null) {
            log.info("使用默认特效映射: {} -> effect_id: {}", effectTitle, defaultEffect.getEffectId());
            // 缓存默认映射结果
            effectCache.put(effectTitle, new CacheEntry(defaultEffect));
            return defaultEffect;
        } else {
            log.warn("未找到特效: {}", effectTitle);
            return null;
        }
    }

    /**
     * 构建搜索URL
     */
    private String buildSearchUrl(String keyword) {
        return UriComponentsBuilder.fromHttpUrl(EFFECT_SEARCH_URL)
                .queryParam("access_key", ACCESS_KEY)
                .queryParam("device_id", DEVICE_ID)
                .queryParam("device_type", DEVICE_TYPE)
                .queryParam("panel", PANEL)
                .queryParam("device_platform", DEVICE_PLATFORM)
                .queryParam("app_version", APP_VERSION)
                .queryParam("app_language", APP_LANGUAGE)
                .queryParam("sdk_version", SDK_VERSION)
                .queryParam("keyword", keyword)
                .queryParam("count", 20)
                .queryParam("offset", 0)
                .build()
                .toUriString();
    }

    /**
     * 解析API响应
     */
    private EffectInfo parseEffectResponse(String responseBody, String searchKeyword) {
        try {
            JSONObject response = JSONObject.parseObject(responseBody);
            
            if (response == null) {
                log.warn("API响应为空: {}", searchKeyword);
                return null;
            }
            
            JSONObject data = response.getJSONObject("data");
            if (data == null) {
                log.warn("API响应data为空: {}", searchKeyword);
                return null;
            }
            
            JSONArray effects = data.getJSONArray("effects");
            if (effects == null || effects.isEmpty()) {
                log.warn("API响应effects为空: {}", searchKeyword);
                return null;
            }
            
            // 取第一个匹配的特效
            JSONObject effect = effects.getJSONObject(0);
            if (effect == null) {
                log.warn("API响应第一个特效为空: {}", searchKeyword);
                return null;
            }
            
            // 构建特效信息
            EffectInfo effectInfo = new EffectInfo();
            effectInfo.setName(effect.getString("title"));  // 使用setName而不是setTitle
            effectInfo.setEffectId(effect.getString("effect_id"));
            effectInfo.setResourceId(effect.getString("resource_id"));
            // 注释掉setCategory，因为EffectInfo类中没有category字段
            // effectInfo.setCategory(effect.getString("category"));
            
            // 解析图标URL信息
            JSONObject iconUrl = effect.getJSONObject("icon_url");
            if (iconUrl != null) {
                EffectInfo.FileUrlInfo iconUrlInfo = new EffectInfo.FileUrlInfo();
                iconUrlInfo.setUri(iconUrl.getString("uri"));
                iconUrlInfo.setMd5(iconUrl.getString("md5"));
                
                JSONArray urlList = iconUrl.getJSONArray("url_list");
                if (urlList != null) {
                    String[] urls = new String[urlList.size()];
                    for (int i = 0; i < urlList.size(); i++) {
                        urls[i] = urlList.getString(i);
                    }
                    iconUrlInfo.setUrlList(urls);
                }
                effectInfo.setIconUrl(iconUrlInfo);
            }
            
            return effectInfo;
            
        } catch (Exception e) {
            log.error("解析API响应失败: {}", searchKeyword, e);
            return null;
        }
    }
    
    /**
     * 清空缓存
     */
    public void clearCache() {
        effectCache.clear();
        log.info("特效缓存已清空");
    }

    /**
     * 获取缓存大小
     */
    public int getCacheSize() {
        return effectCache.size();
    }

    /**
     * 获取缓存统计信息
     */
    public Map<String, Object> getCacheStats() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("total_size", effectCache.size());
        stats.put("default_effects", DEFAULT_EFFECT_MAPPING.size());

        long expiredCount = effectCache.values().stream()
                .mapToLong(entry -> entry.isExpired() ? 1 : 0)
                .sum();
        stats.put("expired_count", expiredCount);
        stats.put("valid_count", effectCache.size() - expiredCount);

        return stats;
    }
}
