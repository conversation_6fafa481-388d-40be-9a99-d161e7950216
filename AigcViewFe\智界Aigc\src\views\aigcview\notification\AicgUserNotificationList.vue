<template>
  <a-card :bordered="false">
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline" @keyup.enter.native="searchQuery">
        <a-row :gutter="24">
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="用户ID">
              <a-input placeholder="请输入用户ID" v-model="queryParam.userId"></a-input>
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="通知类型">
              <a-select placeholder="请选择通知类型" v-model="queryParam.type" allowClear>
                <a-select-option :value="1">系统通知</a-select-option>
                <a-select-option :value="2">交易通知</a-select-option>
                <a-select-option :value="3">安全提醒</a-select-option>
                <a-select-option :value="4">营销推送</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="优先级">
              <a-select placeholder="请选择优先级" v-model="queryParam.priority" allowClear>
                <a-select-option :value="1">普通</a-select-option>
                <a-select-option :value="2">重要</a-select-option>
                <a-select-option :value="3">紧急</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="是否已读">
              <a-select placeholder="请选择是否已读" v-model="queryParam.isRead" allowClear>
                <a-select-option :value="0">未读</a-select-option>
                <a-select-option :value="1">已读</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <span class="table-page-search-submitButtons">
              <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
              <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    
    <!-- 操作按钮区域 -->
    <div class="table-operator">
      <a-button @click="handleAdd" type="primary" icon="plus">新增</a-button>
      <a-button type="primary" icon="download" @click="handleExportXls('用户通知消息')">导出</a-button>
      <a-button type="primary" icon="check" @click="batchMarkRead" v-if="selectedRowKeys.length > 0">批量标记已读</a-button>
      <a-dropdown v-if="selectedRowKeys.length > 0">
        <a-menu slot="overlay">
          <a-menu-item key="1" @click="batchDel"><a-icon type="delete"/>删除</a-menu-item>
        </a-menu>
        <a-button style="margin-left: 8px"> 批量操作 <a-icon type="down" /></a-button>
      </a-dropdown>
    </div>

    <!-- table区域-begin -->
    <div>
      <div class="ant-alert ant-alert-info" style="margin-bottom: 16px;">
        <i class="anticon anticon-info-circle ant-alert-icon"></i> 已选择 <a style="font-weight: 600">{{ selectedRowKeys.length }}</a>项
        <a style="margin-left: 24px" @click="onClearSelected">清空</a>
      </div>

      <a-table
        ref="table"
        size="middle"
        :scroll="{x:true}"
        bordered
        rowKey="id"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="ipagination"
        :loading="loading"
        :rowSelection="{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}"
        class="j-table-force-nowrap"
        @change="handleTableChange">

        <template slot="priority" slot-scope="text">
          <a-tag :color="text === 1 ? 'blue' : text === 2 ? 'orange' : 'red'">
            {{ text === 1 ? '普通' : text === 2 ? '重要' : text === 3 ? '紧急' : text }}
          </a-tag>
        </template>

        <template slot="isRead" slot-scope="text">
          <a-tag :color="text === 0 ? 'red' : 'green'">
            {{ text === 0 ? '未读' : '已读' }}
          </a-tag>
        </template>

        <span slot="action" slot-scope="text, record">
          <a @click="handleEdit(record)">编辑</a>
          <a-divider type="vertical" />
          <a-dropdown>
            <a class="ant-dropdown-link">更多 <a-icon type="down" /></a>
            <a-menu slot="overlay">
              <a-menu-item>
                <a @click="handleDetail(record)">详情</a>
              </a-menu-item>
              <a-menu-item v-if="record.isRead === 0">
                <a @click="handleMarkRead(record)">标记已读</a>
              </a-menu-item>
              <a-menu-item>
                <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
                  <a>删除</a>
                </a-popconfirm>
              </a-menu-item>
            </a-menu>
          </a-dropdown>
        </span>

      </a-table>
    </div>

    <aicg-user-notification-modal ref="modalForm" @ok="modalFormOk"></aicg-user-notification-modal>
  </a-card>
</template>

<script>

  import '@/assets/less/TableExpand.less'
  import { mixinDevice } from '@/utils/mixin'
  import { JeecgListMixin } from '@/mixins/JeecgListMixin'
  import AicgUserNotificationModal from './modules/AicgUserNotificationModal.vue'

  export default {
    name: 'AicgUserNotificationList',
    mixins:[JeecgListMixin, mixinDevice],
    components: {
      AicgUserNotificationModal
    },
    data () {
      return {
        description: '用户通知消息管理页面',
        // 表头
        columns: [
          {
            title: '#',
            dataIndex: '',
            key:'rowIndex',
            width:60,
            align:"center",
            customRender:function (t,r,index) {
              return parseInt(index)+1;
            }
          },
          {
            title:'用户ID',
            align:"center",
            dataIndex: 'userId',
            width: 120
          },
          {
            title:'通知标题',
            align:"center",
            dataIndex: 'title',
            width: 200,
            ellipsis: true
          },
          {
            title:'通知类型',
            align:"center",
            dataIndex: 'type',
            width: 100,
            customRender: function (text) {
              const typeMap = {1: '系统通知', 2: '交易通知', 3: '安全提醒', 4: '营销推送'}
              return typeMap[text] || text
            }
          },
          {
            title:'优先级',
            align:"center",
            dataIndex: 'priority',
            width: 80,
            scopedSlots: { customRender: 'priority' }
          },
          {
            title:'是否已读',
            align:"center",
            dataIndex: 'isRead',
            width: 100,
            scopedSlots: { customRender: 'isRead' }
          },
          {
            title:'阅读时间',
            align:"center",
            dataIndex: 'readTime',
            width: 150
          },
          {
            title:'创建时间',
            align:"center",
            dataIndex: 'createTime',
            width: 150
          },
          {
            title: '操作',
            dataIndex: 'action',
            align:"center",
            fixed:"right",
            width:147,
            scopedSlots: { customRender: 'action' }
          }
        ],
        url: {
          list: "/demo/notification/list",
          delete: "/demo/notification/delete",
          deleteBatch: "/demo/notification/deleteBatch",
          exportXlsUrl: "/demo/notification/exportXls",
          importExcelUrl: "demo/notification/importExcel",
        },
        dictOptions:{},
        superFieldList:[],
      }
    },
    created() {
      this.getSuperFieldList();
    },
    computed: {
      importExcelUrl: function(){
        return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`;
      },
    },
    methods: {
      initDictConfig(){
      },
      getSuperFieldList(){
        let fieldList=[];
        fieldList.push({type:'string',value:'userId',text:'用户ID'})
        fieldList.push({type:'string',value:'title',text:'通知标题'})
        fieldList.push({type:'int',value:'type',text:'通知类型'})
        fieldList.push({type:'int',value:'priority',text:'优先级'})
        fieldList.push({type:'int',value:'isRead',text:'是否已读'})
        fieldList.push({type:'Date',value:'readTime',text:'阅读时间'})
        fieldList.push({type:'Date',value:'createTime',text:'创建时间'})
        this.superFieldList = fieldList
      },
      handleMarkRead(record) {
        this.$confirm({
          title: '标记已读',
          content: '确定要标记该通知为已读吗？',
          onOk: () => {
            this.$http.post(`/demo/notification/markRead?id=${record.id}&userId=${record.userId}`).then((res) => {
              if (res.success) {
                this.$message.success('标记成功')
                this.loadData()
              } else {
                this.$message.error(res.message)
              }
            })
          }
        })
      },
      batchMarkRead() {
        if (this.selectedRowKeys.length === 0) {
          this.$message.warning('请选择要标记的通知')
          return
        }
        this.$confirm({
          title: '批量标记已读',
          content: `确定要标记选中的 ${this.selectedRowKeys.length} 条通知为已读吗？`,
          onOk: () => {
            // 获取第一个选中记录的用户ID
            const firstRecord = this.dataSource.find(item => item.id === this.selectedRowKeys[0])
            if (!firstRecord) {
              this.$message.error('获取用户ID失败')
              return
            }
            this.$http.post(`/demo/notification/batchMarkRead?userId=${firstRecord.userId}&ids=${this.selectedRowKeys.join(',')}`).then((res) => {
              if (res.success) {
                this.$message.success(res.message)
                this.loadData()
                this.onClearSelected()
              } else {
                this.$message.error(res.message)
              }
            })
          }
        })
      }
    }
  }
</script>
<style scoped>
  @import '~@assets/less/common.less';
</style>
