<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form-model ref="form" :model="model" :rules="validatorRules" slot="detail">
        <!-- 是否系列视频 -->
        <a-row>
          <a-col :span="12">
            <a-form-model-item label="是否系列视频" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="isseries">
              <j-dict-select-tag type="list" v-model="model.isseries" dictCode="isTrue" placeholder="请选择是否系列视频" @change="handleSeriesChange" />
            </a-form-model-item>
          </a-col>
          <!-- 系列名称 - 仅在是否系列视频为"是"时显示 -->
          <a-col :span="12" v-if="model.isseries == 1">
            <a-form-model-item label="系列名称" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="seriesname">
              <a-input v-model="model.seriesname" placeholder="请输入系列名称" />
            </a-form-model-item>
          </a-col>
        </a-row>

        <!-- 视频链接 -->
        <a-row>
          <a-col :span="12">
            <a-form-model-item label="视频链接" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="videoUrl">
              <a-input v-model="model.videoUrl" placeholder="请输入视频链接" />
            </a-form-model-item>
          </a-col>
          <!-- 讲师 -->
          <a-col :span="12">
            <a-form-model-item label="讲师" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="teacher">
              <j-dict-select-tag type="list" v-model="model.teacher" dictCode="aigc_video_teacher,teachername,id" placeholder="请选择讲师" />
            </a-form-model-item>
          </a-col>
        </a-row>

        <!-- 标题 -->
        <a-row>
          <a-col :span="12">
            <a-form-model-item label="标题" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="titile">
              <a-input v-model="model.titile" placeholder="请输入标题" />
            </a-form-model-item>
          </a-col>
          <!-- 设置等级 -->
          <a-col :span="12">
            <a-form-model-item label="设置等级" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="setlevel">
              <j-dict-select-tag type="list" v-model="model.setlevel" dictCode="setLevel" placeholder="请选择设置等级" />
            </a-form-model-item>
          </a-col>
        </a-row>

        <!-- 上传日期 -->
        <a-row>
          <a-col :span="12">
            <a-form-model-item label="上传日期" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="uptime">
              <j-date placeholder="请选择上传日期" v-model="model.uptime" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <!-- 视频文件 - 禁用显示 -->
          <a-col :span="12">
            <a-form-model-item label="视频文件" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="videofile">
              <j-upload v-model="model.videofile" disabled />
              <div style="color: #999; font-size: 12px; margin-top: 4px;">此字段已禁用</div>
            </a-form-model-item>
          </a-col>
        </a-row>

        <!-- 点击量 - 禁用显示 -->
        <a-row>
          <a-col :span="12">
            <a-form-model-item label="点击量" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="clicknum">
              <a-input-number v-model="model.clicknum" placeholder="请输入点击量" style="width: 100%" disabled />
              <div style="color: #999; font-size: 12px; margin-top: 4px;">此字段已禁用</div>
            </a-form-model-item>
          </a-col>
        </a-row>

        <!-- 课程标签 - 全宽 -->
        <a-row>
          <a-col :span="24">
            <a-form-model-item label="课程标签" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="tag">
              <j-multi-select-tag type="checkbox" v-model="model.tag" dictCode="tag" placeholder="请选择课程标签" />
            </a-form-model-item>
          </a-col>
        </a-row>

        <!-- 课程介绍 - 全宽 -->
        <a-row>
          <a-col :span="24">
            <a-form-model-item label="课程介绍" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="intro">
              <a-textarea v-model="model.intro" rows="4" placeholder="请输入课程介绍" />
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </j-form-container>
  </a-spin>
</template>

<script>

  import { httpAction } from '@/api/manage'

  export default {
    name: 'AigcVideoTutorialForm',
    components: {
    },
    props: {
      //表单禁用
      disabled: {
        type: Boolean,
        default: false,
        required: false
      }
    },
    data () {
      return {
        model:{
          // 设置默认值
          uptime: this.getCurrentDate(), // 上传日期默认为今天
          isseries: 2, // 是否系列视频默认为"否"(2)
          clicknum: 0, // 点击量默认为0
        },
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 },
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 },
        },
        confirmLoading: false,
        validatorRules: {
          titile: [
            { required: true, message: '请输入标题', trigger: 'blur' }
          ],
          videoUrl: [
            { required: true, message: '请输入视频链接', trigger: 'blur' },
            { type: 'url', message: '请输入正确的URL格式', trigger: 'blur' }
          ],
          teacher: [
            { required: true, message: '请选择讲师', trigger: 'change' }
          ],
          uptime: [
            { required: true, message: '请选择上传日期', trigger: 'change' }
          ]
        },
        url: {
          add: "/videotutorial/aigcVideoTutorial/add",
          edit: "/videotutorial/aigcVideoTutorial/edit",
          queryById: "/videotutorial/aigcVideoTutorial/queryById"
        }
      }
    },
    computed: {
      formDisabled(){
        return this.disabled
      },
    },
    created () {
       //备份model原始值
      this.modelDefault = JSON.parse(JSON.stringify(this.model));
    },
    methods: {
      // 获取当前日期
      getCurrentDate() {
        const today = new Date();
        const year = today.getFullYear();
        const month = String(today.getMonth() + 1).padStart(2, '0');
        const day = String(today.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
      },

      // 处理是否系列视频变化
      handleSeriesChange(value) {
        // 如果选择"否"，清空系列名称
        if (value == 2) {
          this.model.seriesname = '';
        }
      },

      add () {
        // 新增时重新设置默认值
        const defaultModel = {
          uptime: this.getCurrentDate(),
          isseries: 2,
          clicknum: 0,
        };
        this.edit(defaultModel);
      },
      edit (record) {
        this.model = Object.assign({}, record);
        // 如果是新增且没有上传日期，设置默认值
        if (!this.model.id && !this.model.uptime) {
          this.model.uptime = this.getCurrentDate();
        }
        this.visible = true;
      },
      submitForm () {
        const that = this;
        // 触发表单验证
        this.$refs.form.validate(valid => {
          if (valid) {
            that.confirmLoading = true;
            let httpurl = '';
            let method = '';
            if(!this.model.id){
              httpurl+=this.url.add;
              method = 'post';
            }else{
              httpurl+=this.url.edit;
               method = 'put';
            }

            // 提交前的数据处理
            const submitData = { ...this.model };

            // 如果不是系列视频，清空系列名称
            if (submitData.isseries == 2) {
              submitData.seriesname = '';
            }

            httpAction(httpurl, submitData, method).then((res)=>{
              if(res.success){
                that.$message.success(res.message);
                that.$emit('ok');
              }else{
                that.$message.warning(res.message);
              }
            }).finally(() => {
              that.confirmLoading = false;
            })
          }

        })
      },
    }
  }
</script>