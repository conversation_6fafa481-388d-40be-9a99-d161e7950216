import { StorageClass } from '../../interface';
import TOSBase from '../base';
import { ReplicationStatusType } from '../../TosExportEnum';
import { RestoreInfo } from './sharedTypes';
export interface HeadObjectInput {
    bucket?: string;
    key: string;
    versionId?: string;
    ifMatch?: string;
    ifModifiedSince?: string;
    ifNoneMatch?: string;
    ifUnmodifiedSince?: string;
    ssecAlgorithm?: string;
    ssecKey?: string;
    ssecKeyMD5?: string;
    headers?: {
        [key: string]: string | undefined;
        'If-Match'?: string;
        'If-Modified-Since'?: string;
        'If-None-Match'?: string;
        'If-Unmodified-Since'?: string;
        'x-tos-server-side-encryption-customer-algorithm'?: string;
        'x-tos-server-side-encryption-customer-key'?: string;
        'x-tos-server-side-encryption-customer-key-md5'?: string;
    };
}
export interface HeadObjectOutput {
    [key: string]: string | undefined | object;
    'content-length': string;
    'last-modified': string;
    'content-md5': string;
    etag: string;
    'x-tos-object-type'?: 'Appendable' | 'Symlink';
    'x-tos-delete-marker'?: string;
    'x-tos-server-side-encryption-customer-algorithm'?: string;
    'x-tos-server-side-encryption-customer-key-md5'?: string;
    'x-tos-version-id'?: string;
    'x-tos-website-redirect-location'?: string;
    'x-tos-hash-crc64ecma'?: string;
    'x-tos-storage-class': StorageClass;
    'x-tos-server-side-encryption'?: string;
    'x-tos-replication-status'?: ReplicationStatusType;
    'x-tos-symlink-target-size'?: string;
    RestoreInfo?: RestoreInfo;
    ReplicationStatus?: ReplicationStatusType;
}
export declare function headObject(this: TOSBase, input: HeadObjectInput | string): Promise<import("../base").TosResponse<HeadObjectOutput>>;
export default headObject;
