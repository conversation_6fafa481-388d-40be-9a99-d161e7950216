<template>
  <a-modal
    :title="title"
    :width="800"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel"
    cancelText="关闭">
    
    <a-spin :spinning="confirmLoading">
      <a-form-model ref="form" :model="model" :rules="validatorRules" v-bind="layout">
        
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item label="敏感词" prop="word">
              <a-input v-model="model.word" placeholder="请输入敏感词" :disabled="!!model.id"/>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="分类" prop="category">
              <j-dict-select-tag placeholder="请选择分类" v-model="model.category" dictCode="sensitive_word_category"/>
            </a-form-model-item>
          </a-col>
        </a-row>

        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item label="敏感级别" prop="level">
              <j-dict-select-tag placeholder="请选择级别" v-model="model.level" dictCode="sensitive_word_level"/>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="状态" prop="status">
              <j-dict-select-tag placeholder="请选择状态" v-model="model.status" dictCode="valid_status"/>
            </a-form-model-item>
          </a-col>
        </a-row>

        <a-row :gutter="24" v-if="model.id">
          <a-col :span="12">
            <a-form-model-item label="命中次数">
              <a-input v-model="model.hitCount" disabled/>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="来源">
              <a-input v-model="model.source" disabled/>
            </a-form-model-item>
          </a-col>
        </a-row>

        <a-row :gutter="24">
          <a-col :span="24">
            <a-form-model-item label="备注" prop="remark">
              <a-textarea v-model="model.remark" placeholder="请输入备注" :rows="3"/>
            </a-form-model-item>
          </a-col>
        </a-row>

        <!-- 敏感词检测区域 -->
        <a-row :gutter="24" v-if="!model.id">
          <a-col :span="24">
            <a-form-model-item label="敏感词检测">
              <div class="sensitive-check-area">
                <a-button type="primary" @click="handleCheckWord" :loading="checkLoading" size="small">
                  检测敏感词
                </a-button>
                <span v-if="checkResult" class="check-result" :class="checkResult.hasSensitiveWord ? 'error' : 'success'">
                  <a-icon :type="checkResult.hasSensitiveWord ? 'close-circle' : 'check-circle'"/>
                  {{ checkResult.message }}
                </span>
              </div>
            </a-form-model-item>
          </a-col>
        </a-row>

      </a-form-model>
    </a-spin>
  </a-modal>
</template>

<script>
  import { httpAction } from '@/api/manage'
  import { checkSensitiveWord } from '@/api/system/sensitiveWord'
  import pick from 'lodash.pick'

  export default {
    name: 'SensitiveWordModal',
    components: {
    },
    data () {
      return {
        layout: {
          labelCol: {
            xs: { span: 24 },
            sm: { span: 5 },
          },
          wrapperCol: {
            xs: { span: 24 },
            sm: { span: 16 },
          },
        },
        title: "操作",
        visible: false,
        model: {},
        confirmLoading: false,
        checkLoading: false,
        checkResult: null,
        form: this.$form.createForm(this),
        validatorRules: {
          word: [
            { required: true, message: '请输入敏感词!' },
            { min: 1, max: 100, message: '敏感词长度在1到100个字符之间!' },
            { pattern: /^[\u4e00-\u9fa5a-zA-Z0-9_\-\s]+$/, message: '敏感词只能包含中文、英文、数字、下划线、连字符和空格!' }
          ],
          category: [
            { required: true, message: '请选择分类!' }
          ],
          level: [
            { required: true, message: '请选择敏感级别!' }
          ],
          status: [
            { required: true, message: '请选择状态!' }
          ]
        },
        url: {
          add: "/sys/sensitiveWord/add",
          edit: "/sys/sensitiveWord/edit",
          queryById: "/sys/sensitiveWord/queryById"
        }
      }
    },
    created () {
    },
    methods: {
      add () {
        this.edit({});
      },
      edit (record) {
        this.form.resetFields();
        this.model = Object.assign({}, record);
        this.checkResult = null;
        this.visible = true;
        this.$nextTick(() => {
          this.form.setFieldsValue(pick(this.model,'word','category','level','status','hitCount','source','remark'))
        })
      },
      detail (record) {
        this.model = Object.assign({}, record);
        this.checkResult = null;
        this.visible = true;
        this.$nextTick(() => {
          this.form.setFieldsValue(pick(this.model,'word','category','level','status','hitCount','source','remark'))
        })
      },
      close () {
        this.$emit('close');
        this.visible = false;
      },
      handleOk () {
        const that = this;
        // 触发表单验证
        this.$refs.form.validate(valid => {
          if (valid) {
            that.confirmLoading = true;
            let httpurl = '';
            let method = '';
            if(!this.model.id){
              httpurl+=this.url.add;
              method = 'post';
            }else{
              httpurl+=this.url.edit;
              method = 'put';
            }
            httpAction(httpurl,this.model,method).then((res)=>{
              if(res.success){
                that.$message.success(res.message);
                that.$emit('ok');
              }else{
                that.$message.warning(res.message);
              }
            }).finally(() => {
              that.confirmLoading = false;
            })
          }
        })
      },
      handleCancel () {
        this.close()
      },
      handleCheckWord () {
        if (!this.model.word) {
          this.$message.warning('请先输入敏感词');
          return;
        }
        
        this.checkLoading = true;
        this.checkResult = null;
        
        checkSensitiveWord(this.model.word).then((res) => {
          if (res.success) {
            this.checkResult = res.result;
          } else {
            this.$message.error('检测失败：' + res.message);
          }
        }).catch((error) => {
          this.$message.error('检测失败：' + error.message);
        }).finally(() => {
          this.checkLoading = false;
        });
      },
      popupCallback(value, row){
        this.model = Object.assign(this.model, row);
      },

    }
  }
</script>

<style lang="less" scoped>
  .sensitive-check-area {
    display: flex;
    align-items: center;
    gap: 12px;
    
    .check-result {
      display: flex;
      align-items: center;
      gap: 4px;
      font-size: 12px;
      
      &.success {
        color: #52c41a;
      }
      
      &.error {
        color: #ff4d4f;
      }
    }
  }
</style>
