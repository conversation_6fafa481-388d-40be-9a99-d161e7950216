/// <reference types="node" />
import { SignersV4 } from './signatureV4';
export declare const retryNamespace = "__retryConfig__";
export declare const retrySignatureNamespace = "__retrySignature__";
export interface RetryConfig {
    makeRetryStream?: () => NodeJS.ReadableStream | undefined;
    beforeRetry?: () => void;
}
interface RetrySignature {
    signOpt: any;
    sigInst: SignersV4;
}
declare module 'axios' {
    interface AxiosRequestConfig {
        __retryConfig__?: RetryConfig;
        __retrySignature__?: RetrySignature;
    }
}
export declare const makeAxiosInst: (maxRetryCount: number) => import("axios").AxiosInstance;
export {};
