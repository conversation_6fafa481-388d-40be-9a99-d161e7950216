package org.jeecg.modules.api.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.modules.demo.userprofile.entity.AicgUserProfile;
import org.jeecg.modules.demo.userprofile.service.IAicgUserProfileService;
import org.jeecg.modules.api.service.IAigcApiService;
import org.jeecg.modules.api.service.impl.AigcApiServiceImpl;
import org.jeecg.modules.demo.plubshop.entity.AigcPlubShop;
import org.jeecg.modules.demo.plubshop.service.IAigcPlubShopService;
import org.jeecg.modules.api.service.VideoGenerationService;
import org.jeecg.modules.api.exception.VideoGenerationException;
import org.jeecg.modules.api.util.VideoParameterValidator;
import org.jeecg.modules.system.entity.SysRole;
import org.jeecg.modules.system.entity.SysUserRole;
import org.jeecg.modules.system.service.ISysRoleService;
import org.jeecg.modules.system.service.ISysUserService;
import org.jeecg.modules.system.service.ISysUserRoleService;
import org.jeecg.modules.demo.apiusage.service.IAicgUserApiUsageService;
import org.springframework.jdbc.core.JdbcTemplate;
import com.fasterxml.jackson.databind.ObjectMapper;

import javax.servlet.http.HttpServletRequest;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.UUID;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;

import java.util.*;

/**
 * 扣子图生视频插件Controller
 * 
 * <AUTHOR>
 * @since 2025-01-16
 */
@Api(tags = "扣子图生视频插件")
@RestController
@RequestMapping("/api/coze/video")
@Slf4j
public class CozeVideoController {

    @Autowired
    private IAigcApiService aigcApiService;

    @Autowired
    private IAicgUserProfileService userProfileService;

    @Autowired
    private ISysUserService sysUserService;

    @Autowired
    private ISysUserRoleService sysUserRoleService;

    @Autowired
    private IAigcPlubShopService plubShopService;

    @Autowired
    private ISysRoleService sysRoleService;

    @Autowired
    private IAicgUserApiUsageService apiUsageService;

    @Autowired
    private VideoGenerationService videoGenerationService;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    /**
     * 创建视频生成任务
     */
    @ApiOperation(value = "创建视频生成任务", notes = "扣子插件专用：根据参数动态定价并创建图生视频任务")
    @PostMapping("/generate-task")
    public Map<String, Object> generateVideoTask(@RequestBody Map<String, Object> params, HttpServletRequest request) {
        long requestStartTime = System.currentTimeMillis(); // 记录请求开始时间
        log.info("图生视频任务创建请求开始，参数: {}", params);

        try {
            // 1. 🔥 参数验证和标准化
            log.info("开始参数验证...");
            VideoParameterValidator.validateAllParameters(params);
            log.info("参数验证通过，开始标准化...");
            VideoParameterValidator.normalizeParameters(params);
            log.info("参数标准化完成，标准化后参数: {}", params);
        } catch (VideoGenerationException e) {
            log.error("参数验证失败: {}", e.getErrorMessage(), e);
            throw e; // 重新抛出，让异常处理器处理
        } catch (Exception e) {
            log.error("参数验证过程中发生未知异常", e);
            throw VideoGenerationException.invalidParameter("参数验证失败: " + e.getMessage());
        }

        String apiKey = (String) params.get("apiKey");

        // 2. API-Key验证（不扣费）
        AicgUserProfile userProfile = aigcApiService.getUserByApiKey(apiKey);
        if (userProfile == null || userProfile.getStatus() != 1) {
            throw VideoGenerationException.apiKeyInvalid("API-Key无效或已被禁用");
        }

        // 3. 获取标准化后的参数
        String model = (String) params.get("model");
        String resolution = (String) params.get("resolution");
        Integer duration = (Integer) params.get("duration");
        String prompt = (String) params.get("prompt");
        String imageUrl = (String) params.get("image_url");
        Boolean asyn = (Boolean) params.getOrDefault("asyn", false);

        log.info("图生视频参数验证通过 - 用户: {}, 模型: {}, 分辨率: {}, 时长: {}秒, 同步模式: {}",
                userProfile.getUserId(), model, resolution, duration, asyn);

        // 4. 🔥 参数验证和价格计算
        PriceCalculationResult priceResult = calculateVideoPrice(model, resolution, duration, userProfile.getUserId());
        if (!priceResult.isValid()) {
            throw VideoGenerationException.invalidParameter(priceResult.getErrorMessage());
        }

        BigDecimal finalPrice = priceResult.getPrice();
        String userId = userProfile.getUserId();

        // 5. 🔥 预扣费：先冻结金额，不立即扣除（参考小红书接口）
        boolean preDeductSuccess = userProfileService.preDeductBalance(userId, finalPrice);
        if (!preDeductSuccess) {
            throw VideoGenerationException.insufficientBalance(String.format("余额不足或系统繁忙，需要%.2f元，请稍后重试", finalPrice));
        }

        log.info("预扣费成功，用户ID: {}, 冻结金额: {}", userId, finalPrice);

        try {
            // 6. 调用豆包API生成视频（业务逻辑执行阶段）
            Map<String, Object> videoResult = videoGenerationService.createVideoTask(params, userProfile);

            log.info("豆包API调用成功，任务ID: {}, 模式: {}", videoResult.get("task_id"), asyn ? "同步" : "异步");

            // 7. 🔥 豆包API调用成功，立即确认扣费（从冻结余额转为实际扣费）
            if (finalPrice.compareTo(BigDecimal.ZERO) > 0) {
                boolean confirmSuccess = userProfileService.confirmDeductBalance(userId, finalPrice);
                if (!confirmSuccess) {
                    throw new RuntimeException("确认扣费失败，冻结余额不足");
                }
                log.info("确认扣费成功，用户ID: {}, 扣费金额: {}", userId, finalPrice);
            } else {
                log.info("免费服务，跳过扣费，用户ID: {}", userId);
            }

            // 8. 🔥 数据联动更新（完全参考小红书接口）
            try {
                log.info("=== 开始更新插件统计数据 ===");

                // 查询插件信息
                AigcPlubShop plugin = plubShopService.getByPluginKey("coze_video_generate");
                if (plugin != null) {
                    // 🔥 使用与小红书接口完全相同的调用方式
                    ((AigcApiServiceImpl) aigcApiService).performDataLinkage(userId, plugin, finalPrice);
                    log.info("✅ 插件统计数据更新成功 - 插件: {}, 用户: {}, 金额: {}",
                            plugin.getPlubname(), userId, finalPrice);
                } else {
                    log.warn("⚠️ 未找到插件信息，跳过统计更新: coze_video_generate");
                }

                log.info("=== 插件统计数据更新完成 ===");
            } catch (Exception e) {
                log.error("更新插件统计数据失败，但不影响主功能", e);
            }

            // 9. 🔥 生成交易记录（参考小红书接口）
            try {
                generateTransactionRecord(userId, "coze_video_generate", "图生视频", finalPrice);
                log.info("交易记录已保存 - 用户: {}, 接口: 图生视频, 费用: {}", userId, finalPrice);
            } catch (Exception e) {
                log.error("保存交易记录失败", e);
                // 不影响主流程，继续执行
            }

            // 10. 🔥 记录API使用情况（参考小红书接口）
            try {
                recordApiUsage(request, userId, apiKey,
                             "coze_video_generate", "图生视频",
                             200, requestStartTime, null, finalPrice, null);
                log.info("API使用记录已保存 - 用户: {}, 接口: 图生视频, 费用: {}", userId, finalPrice);
            } catch (Exception e) {
                log.error("保存API使用记录失败", e);
                // 不影响主流程，继续执行
            }

            // 10. 🔥 根据asyn参数决定返回方式
            if (asyn) {
                // 同步模式：等待视频生成完成再返回
                return handleSyncMode(videoResult, finalPrice, userProfile, priceResult, model, resolution, duration);
            } else {
                // 异步模式：立即返回任务ID
                return handleAsyncMode(videoResult, finalPrice, userProfile, priceResult, model, resolution, duration);
            }

        } catch (Exception e) {
            // 🔥 业务执行失败，退还冻结余额（参考小红书接口的回滚机制）
            log.error("图生视频业务执行失败，开始退还冻结余额，用户ID: {}, 金额: {}", userId, finalPrice, e);

            // 尝试退还冻结余额，最多重试3次（参考小红书接口）
            boolean refundSuccess = false;
            for (int i = 0; i < 3; i++) {
                refundSuccess = userProfileService.refundFrozenBalance(userId, finalPrice);
                if (refundSuccess) {
                    log.info("冻结余额退还成功，用户ID: {}, 退还金额: {}, 重试次数: {}", userId, finalPrice, i + 1);
                    break;
                } else {
                    log.warn("冻结余额退还失败，第{}次重试，用户ID: {}, 金额: {}", i + 1, userId, finalPrice);
                    // 立即重试，不等待（数据库操作失败通常是瞬时的）
                }
            }

            if (!refundSuccess) {
                log.error("❌ 严重错误：冻结余额退还失败，需要人工处理！用户ID: {}, 金额: {}", userId, finalPrice);
                // 可以考虑发送告警通知或记录到特殊的错误表
            }

            // 重新抛出原始异常
            if (e instanceof VideoGenerationException) {
                throw (VideoGenerationException) e;
            } else {
                throw VideoGenerationException.systemError("图生视频生成失败: " + e.getMessage(), e);
            }
        }
    }

    /**
     * 查询视频生成任务状态
     */
    @ApiOperation(value = "查询视频任务状态", notes = "查询图生视频任务的生成状态和结果")
    @PostMapping("/query-task")
    public Map<String, Object> queryVideoTask(@RequestBody Map<String, Object> params) {
        String taskId = (String) params.get("task_id");
        Integer waitCnt = (Integer) params.getOrDefault("wait_cnt", 1);

        if (taskId == null || taskId.trim().isEmpty()) {
            throw VideoGenerationException.invalidParameter("任务ID不能为空");
        }

        // 验证wait_cnt参数
        if (waitCnt < 1 || waitCnt > 10) {
            throw VideoGenerationException.invalidParameter("等待次数必须在1-10之间");
        }

        log.info("查询视频任务状态 - 任务ID: {}, 等待次数: {}", taskId, waitCnt);

        // 查询任务状态（支持轮询等待）
        Map<String, Object> taskStatus = videoGenerationService.queryTaskStatusWithWait(taskId, waitCnt);

        return taskStatus;
    }

    /**
     * 取消视频生成任务
     */
    @ApiOperation(value = "取消视频任务", notes = "取消正在进行的图生视频任务")
    @PostMapping("/cancel-task")
    public Map<String, Object> cancelVideoTask(@RequestBody Map<String, Object> params) {
        String taskId = (String) params.get("task_id");

        if (taskId == null || taskId.trim().isEmpty()) {
            throw VideoGenerationException.invalidParameter("任务ID不能为空");
        }

        // 取消任务（无需扣费）
        Map<String, Object> cancelResult = videoGenerationService.cancelVideoTask(taskId);

        return cancelResult;
    }

    /**
     * 🔥 图生视频价格计算核心逻辑
     */
    private PriceCalculationResult calculateVideoPrice(String model, String resolution, Integer duration, String userId) {
        try {
            log.debug("开始计算价格 - 模型: {}, 分辨率: {}, 时长: {}秒, 用户: {}", model, resolution, duration, userId);

            // 1. 参数验证（使用验证工具类）
            if (!VideoParameterValidator.isValidModel(model)) {
                throw VideoGenerationException.modelNotSupported("不支持的模型: " + model);
            }

            if (!VideoParameterValidator.isValidResolution(resolution)) {
                throw VideoGenerationException.resolutionNotSupported("不支持的分辨率: " + resolution);
            }

            if (!VideoParameterValidator.isValidDuration(duration)) {
                throw VideoGenerationException.durationNotSupported("不支持的时长: " + duration + "秒");
            }

            // 2. 🔥 特殊规则验证：pro模型不支持720p
            if ("doubao-seedance-1-0-pro-250528".equals(model) && "720p".equals(resolution)) {
                throw VideoGenerationException.invalidParameter("pro模型不支持720p分辨率，请选择480p或1080p");
            }

            // 3. 获取用户角色
            String userLevel = getUserLevel(userId);

            // 4. 🔥 根据定价表计算价格
            BigDecimal price = getFixedPrice(model, resolution, duration, userLevel);

            log.info("图生视频价格计算完成 - 模型: {}, 分辨率: {}, 时长: {}秒, 用户等级: {}, 价格: {}",
                model, resolution, duration, userLevel, price);

            return PriceCalculationResult.success(price, userLevel, price);

        } catch (VideoGenerationException e) {
            log.error("图生视频价格计算业务异常: {}", e.getErrorMessage(), e);
            return PriceCalculationResult.error(e.getErrorMessage());
        } catch (Exception e) {
            log.error("图生视频价格计算系统异常", e);
            return PriceCalculationResult.error("价格计算失败：" + e.getMessage());
        }
    }

    /**
     * 获取用户等级
     */
    private String getUserLevel(String userId) {
        try {
            // 查询用户角色
            QueryWrapper<org.jeecg.modules.system.entity.SysUserRole> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("user_id", userId);
            List<org.jeecg.modules.system.entity.SysUserRole> userRoles = sysUserRoleService.list(queryWrapper);
            
            if (userRoles.isEmpty()) {
                return "user"; // 默认普通用户
            }

            // 获取角色代码列表
            List<String> roleCodes = new ArrayList<>();
            for (SysUserRole userRole : userRoles) {
                SysRole role = sysRoleService.getById(userRole.getRoleId());
                if (role != null) {
                    roleCodes.add(role.getRoleCode());
                }
            }

            // 角色优先级：admin > SVIP > VIP > user
            if (roleCodes.contains("admin")) {
                return "admin";
            } else if (roleCodes.contains("SVIP")) {
                return "SVIP";
            } else if (roleCodes.contains("VIP")) {
                return "VIP";
            } else {
                return "user";
            }
        } catch (Exception e) {
            log.error("获取用户等级失败", e);
            return "user"; // 异常时返回普通用户
        }
    }

    private String getModelDisplayName(String model) {
        return VideoParameterValidator.getModelDisplayName(model);
    }

    /**
     * 处理异步模式（立即返回任务ID）
     */
    private Map<String, Object> handleAsyncMode(Map<String, Object> videoResult, BigDecimal finalPrice,
                                               AicgUserProfile userProfile, PriceCalculationResult priceResult,
                                               String model, String resolution, Integer duration) {
        Map<String, Object> response = new HashMap<>();
        response.put("task_id", videoResult.get("task_id"));

        // 🔥 修复：确保status有默认值，避免返回null
        String status = (String) videoResult.get("status");
        if (status == null || status.trim().isEmpty()) {
            status = "pending";  // 默认状态为pending
        }
        response.put("status", status);

        // 🔥 修复：确保message有默认值
        String message = (String) videoResult.get("message");
        if (message == null || message.trim().isEmpty()) {
            message = "视频生成任务已创建，请使用task_id查询结果";
        }
        response.put("message", message);

        response.put("deductedAmount", finalPrice);
        response.put("balanceAfter", userProfile.getAccountBalance().subtract(finalPrice));

        Map<String, Object> pricingDetails = new HashMap<>();
        pricingDetails.put("model", model);
        pricingDetails.put("resolution", resolution);
        pricingDetails.put("duration", duration);
        pricingDetails.put("userLevel", priceResult.getUserLevel());
        pricingDetails.put("finalPrice", finalPrice);
        response.put("pricingDetails", pricingDetails);

        log.info("异步模式返回 - 任务ID: {}, 状态: {}, 扣费: {}元",
                videoResult.get("task_id"), status, finalPrice);
        return response;
    }

    /**
     * 处理同步模式（等待视频生成完成）
     */
    private Map<String, Object> handleSyncMode(Map<String, Object> videoResult, BigDecimal finalPrice,
                                              AicgUserProfile userProfile, PriceCalculationResult priceResult,
                                              String model, String resolution, Integer duration) {
        String taskId = (String) videoResult.get("task_id");
        log.info("同步模式 - 开始等待视频生成完成，任务ID: {}", taskId);

        // 轮询查询任务状态，最多等待5分钟
        int maxWaitTime = 300; // 5分钟
        int pollInterval = 5;  // 5秒轮询一次
        int waitedTime = 0;

        while (waitedTime < maxWaitTime) {
            try {
                Thread.sleep(pollInterval * 1000);
                waitedTime += pollInterval;

                // 查询任务状态
                Map<String, Object> taskStatus = videoGenerationService.queryTaskStatus(taskId);
                String status = (String) taskStatus.get("status");

                log.debug("同步模式轮询 - 任务ID: {}, 状态: {}, 已等待: {}秒", taskId, status, waitedTime);

                if ("completed".equals(status)) {
                    // 任务完成，返回完整结果
                    Map<String, Object> response = new HashMap<>();
                    response.put("task_id", taskId);
                    response.put("status", status);
                    response.put("message", taskStatus.get("message"));
                    response.put("video_url", taskStatus.get("video_url"));
                    response.put("deductedAmount", finalPrice);
                    response.put("balanceAfter", userProfile.getAccountBalance().subtract(finalPrice));
                    Map<String, Object> pricingDetails = new HashMap<>();
                    pricingDetails.put("model", model);
                    pricingDetails.put("resolution", resolution);
                    pricingDetails.put("duration", duration);
                    pricingDetails.put("userLevel", priceResult.getUserLevel());
                    pricingDetails.put("finalPrice", finalPrice);
                    response.put("pricingDetails", pricingDetails);

                    log.info("同步模式完成 - 任务ID: {}, 视频URL: {}, 总等待时间: {}秒",
                            taskId, taskStatus.get("video_url"), waitedTime);
                    return response;

                } else if ("failed".equals(status)) {
                    // 任务失败
                    log.error("同步模式失败 - 任务ID: {}, 失败原因: {}", taskId, taskStatus.get("failure_reason"));
                    throw VideoGenerationException.douBaoApiError("视频生成失败: " + taskStatus.get("failure_reason"));
                }
                // 继续等待（running或pending状态）

            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.error("同步模式等待被中断", e);
                throw VideoGenerationException.systemError("视频生成等待被中断", e);
            }
        }

        // 超时返回异步结果
        log.warn("同步模式超时 - 任务ID: {}, 等待时间: {}秒，返回异步结果", taskId, waitedTime);
        Map<String, Object> response = new HashMap<>();
        response.put("task_id", taskId);
        response.put("status", "running");
        response.put("message", "视频生成超时，请使用task_id查询结果");
        response.put("deductedAmount", finalPrice);
        response.put("balanceAfter", userProfile.getAccountBalance().subtract(finalPrice));

        return response;
    }





    /**
     * 🔥 根据定价表获取固定价格
     */
    private BigDecimal getFixedPrice(String model, String resolution, Integer duration, String userLevel) {
        // 构建价格查询key
        String priceKey = model + "_" + resolution + "_" + duration + "_" + userLevel;

        // 🔥 完整定价表（根据您提供的价格表）
        Map<String, BigDecimal> priceTable = new HashMap<>();

        // doubao-seedance-1-0-lite-i2v-250428 模型定价
        // 480p分辨率
        priceTable.put("doubao-seedance-1-0-lite-i2v-250428_480p_5_user", new BigDecimal("0.69"));
        priceTable.put("doubao-seedance-1-0-lite-i2v-250428_480p_5_VIP", new BigDecimal("0.59"));
        priceTable.put("doubao-seedance-1-0-lite-i2v-250428_480p_5_SVIP", new BigDecimal("0.49"));
        priceTable.put("doubao-seedance-1-0-lite-i2v-250428_480p_5_admin", new BigDecimal("0.49"));

        priceTable.put("doubao-seedance-1-0-lite-i2v-250428_480p_10_user", new BigDecimal("1.38"));
        priceTable.put("doubao-seedance-1-0-lite-i2v-250428_480p_10_VIP", new BigDecimal("1.18"));
        priceTable.put("doubao-seedance-1-0-lite-i2v-250428_480p_10_SVIP", new BigDecimal("0.98"));
        priceTable.put("doubao-seedance-1-0-lite-i2v-250428_480p_10_admin", new BigDecimal("0.98"));

        // 720p分辨率
        priceTable.put("doubao-seedance-1-0-lite-i2v-250428_720p_5_user", new BigDecimal("1.23"));
        priceTable.put("doubao-seedance-1-0-lite-i2v-250428_720p_5_VIP", new BigDecimal("1.13"));
        priceTable.put("doubao-seedance-1-0-lite-i2v-250428_720p_5_SVIP", new BigDecimal("1.03"));
        priceTable.put("doubao-seedance-1-0-lite-i2v-250428_720p_5_admin", new BigDecimal("1.03"));

        priceTable.put("doubao-seedance-1-0-lite-i2v-250428_720p_10_user", new BigDecimal("2.46"));
        priceTable.put("doubao-seedance-1-0-lite-i2v-250428_720p_10_VIP", new BigDecimal("2.26"));
        priceTable.put("doubao-seedance-1-0-lite-i2v-250428_720p_10_SVIP", new BigDecimal("2.06"));
        priceTable.put("doubao-seedance-1-0-lite-i2v-250428_720p_10_admin", new BigDecimal("2.06"));

        // 1080p分辨率
        priceTable.put("doubao-seedance-1-0-lite-i2v-250428_1080p_5_user", new BigDecimal("2.45"));
        priceTable.put("doubao-seedance-1-0-lite-i2v-250428_1080p_5_VIP", new BigDecimal("2.35"));
        priceTable.put("doubao-seedance-1-0-lite-i2v-250428_1080p_5_SVIP", new BigDecimal("2.25"));
        priceTable.put("doubao-seedance-1-0-lite-i2v-250428_1080p_5_admin", new BigDecimal("2.25"));

        priceTable.put("doubao-seedance-1-0-lite-i2v-250428_1080p_10_user", new BigDecimal("4.90"));
        priceTable.put("doubao-seedance-1-0-lite-i2v-250428_1080p_10_VIP", new BigDecimal("4.70"));
        priceTable.put("doubao-seedance-1-0-lite-i2v-250428_1080p_10_SVIP", new BigDecimal("4.50"));
        priceTable.put("doubao-seedance-1-0-lite-i2v-250428_1080p_10_admin", new BigDecimal("4.50"));

        // doubao-seedance-1-0-pro-250528 模型定价
        // 480p分辨率
        priceTable.put("doubao-seedance-1-0-pro-250528_480p_5_user", new BigDecimal("0.93"));
        priceTable.put("doubao-seedance-1-0-pro-250528_480p_5_VIP", new BigDecimal("0.83"));
        priceTable.put("doubao-seedance-1-0-pro-250528_480p_5_SVIP", new BigDecimal("0.73"));
        priceTable.put("doubao-seedance-1-0-pro-250528_480p_5_admin", new BigDecimal("0.73"));

        priceTable.put("doubao-seedance-1-0-pro-250528_480p_10_user", new BigDecimal("1.86"));
        priceTable.put("doubao-seedance-1-0-pro-250528_480p_10_VIP", new BigDecimal("1.66"));
        priceTable.put("doubao-seedance-1-0-pro-250528_480p_10_SVIP", new BigDecimal("1.46"));
        priceTable.put("doubao-seedance-1-0-pro-250528_480p_10_admin", new BigDecimal("1.46"));

        // 1080p分辨率（pro模型不支持720p）
        priceTable.put("doubao-seedance-1-0-pro-250528_1080p_5_user", new BigDecimal("3.67"));
        priceTable.put("doubao-seedance-1-0-pro-250528_1080p_5_VIP", new BigDecimal("3.57"));
        priceTable.put("doubao-seedance-1-0-pro-250528_1080p_5_SVIP", new BigDecimal("3.47"));
        priceTable.put("doubao-seedance-1-0-pro-250528_1080p_5_admin", new BigDecimal("3.47"));

        priceTable.put("doubao-seedance-1-0-pro-250528_1080p_10_user", new BigDecimal("7.34"));
        priceTable.put("doubao-seedance-1-0-pro-250528_1080p_10_VIP", new BigDecimal("7.14"));
        priceTable.put("doubao-seedance-1-0-pro-250528_1080p_10_SVIP", new BigDecimal("6.94"));
        priceTable.put("doubao-seedance-1-0-pro-250528_1080p_10_admin", new BigDecimal("6.94"));

        // 查找价格
        BigDecimal price = priceTable.get(priceKey);
        if (price == null) {
            log.error("未找到价格配置: {}", priceKey);
            throw new RuntimeException("未找到对应的价格配置");
        }

        return price;
    }

    // ==================== 数据记录辅助方法（参考小红书接口） ====================

    /**
     * 🔥 生成交易记录（参考小红书接口的generateTransactionRecord方法）
     */
    private void generateTransactionRecord(String userId, String pluginKey, String pluginName, BigDecimal amount) {
        try {
            log.info("🔥 开始生成交易记录 - 用户ID: {}, 插件: {}, 金额: {}", userId, pluginKey, amount);

            // 获取用户当前余额信息
            AicgUserProfile userProfile = userProfileService.getByUserId(userId);
            if (userProfile == null) {
                log.error("❌ 生成交易记录失败：用户不存在 - 用户ID: {}", userId);
                return;
            }
            log.info("✅ 用户信息获取成功 - 当前余额: {}", userProfile.getAccountBalance());

            // 🔥 完全按照小红书接口的逻辑插入交易记录
            String transactionId = UUID.randomUUID().toString().replace("-", "");
            String orderType = "plugin"; // 插件订单类型
            int orderStatus = 3; // 已完成

            // 生成订单号（按照小红书接口的格式）
            String orderPrefix = "VID";
            String dateStr = new SimpleDateFormat("yyyyMMdd").format(new Date());
            String orderSuffix = transactionId.substring(transactionId.length() - 8).toUpperCase();
            String relatedOrderId = orderPrefix + dateStr + "_" + orderSuffix;

            String insertSql = "INSERT INTO aicg_user_transaction (" +
                "id, user_id, transaction_type, amount, balance_before, balance_after, " +
                "description, related_order_id, transaction_time, create_by, create_time, " +
                "order_status, order_type, plugin_id, plugin_key, plugin_name" +
                ") VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

            jdbcTemplate.update(insertSql,
                transactionId,
                userId,
                1, // 消费类型
                amount,
                userProfile.getAccountBalance().add(amount), // 扣费前余额
                userProfile.getAccountBalance(), // 扣费后余额
                "调用插件: " + pluginName,
                relatedOrderId,
                new Date(),
                "api_system",
                new Date(),
                orderStatus,
                orderType,
                null, // pluginId (图生视频不是插件商店的插件)
                pluginKey,
                pluginName
            );

            log.info("✅ 交易记录生成成功 - 用户ID: {}, 插件: {}, 金额: {}, 订单号: {}",
                    userId, pluginName, amount, relatedOrderId);

        } catch (Exception e) {
            log.error("❌ 生成交易记录失败 - 用户ID: {}, 插件: {}, 金额: {}, 错误: {}",
                    userId, pluginKey, amount, e.getMessage(), e);
        }
    }

    /**
     * 🔥 记录API使用情况（参考小红书接口的recordApiUsage方法）
     */
    private void recordApiUsage(HttpServletRequest request, String userId, String apiKey,
                               String pluginKey, String pluginName, int responseStatus,
                               long startTime, Integer tokensUsed, BigDecimal costAmount, String errorMessage) {
        try {
            log.info("=== recordApiUsage 方法开始 ===");
            long responseTime = System.currentTimeMillis() - startTime;
            String requestParams = buildRequestParams(pluginKey, pluginName);
            String ipAddress = getClientIpAddress(request);
            String userAgent = request != null ? request.getHeader("User-Agent") : null;

            log.info("准备调用 apiUsageService.recordUsage，参数：");
            log.info("  userId: {}", userId);
            log.info("  apiKey: {}", apiKey);
            log.info("  endpoint: /api/coze/video/generate-task");
            log.info("  method: POST");
            log.info("  requestParams: {}", requestParams);
            log.info("  responseStatus: {}", responseStatus);
            log.info("  responseTime: {}", responseTime);
            log.info("  tokensUsed: {}", tokensUsed);
            log.info("  costAmount: {}", costAmount);
            log.info("  ipAddress: {}", ipAddress);
            log.info("  userAgent: {}", userAgent);
            log.info("  errorMessage: {}", errorMessage);

            // 调用标准的API使用记录服务
            Object result = apiUsageService.recordUsage(
                userId,
                apiKey,
                "/api/coze/video/generate-task",
                "POST",
                requestParams,
                responseStatus,
                (int) responseTime,
                tokensUsed,
                costAmount,
                ipAddress,
                userAgent,
                errorMessage
            );

            log.info("apiUsageService.recordUsage 调用成功，返回结果: {}", result != null ? result.toString() : "null");
            log.info("API使用记录保存成功 - 用户: {}, 插件: {} ({}), 费用: {}",
                    userId, pluginName, pluginKey, costAmount);
        } catch (Exception e) {
            log.error("记录API使用情况失败", e);
            throw e; // 重新抛出异常以便上层捕获
        }
    }

    /**
     * 构建请求参数JSON
     */
    private String buildRequestParams(String pluginKey, String pluginName) {
        Map<String, Object> params = new HashMap<>();
        params.put("pluginKey", pluginKey);
        params.put("pluginName", pluginName);
        params.put("serviceType", "图生视频");
        try {
            return new ObjectMapper().writeValueAsString(params);
        } catch (Exception e) {
            log.warn("构建请求参数JSON失败: {}", e.getMessage());
            return "{\"pluginKey\":\"" + pluginKey + "\",\"pluginName\":\"" + pluginName + "\"}";
        }
    }

    /**
     * 获取客户端IP地址
     */
    private String getClientIpAddress(HttpServletRequest request) {
        if (request == null) {
            return "127.0.0.1"; // 默认本地IP
        }

        String ip = request.getHeader("X-Forwarded-For");
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        // 如果是多级代理，取第一个IP
        if (ip != null && ip.contains(",")) {
            ip = ip.split(",")[0].trim();
        }
        return ip != null ? ip : "127.0.0.1";
    }
}

/**
 * 价格计算结果类
 */
class PriceCalculationResult {
    private boolean valid;
    private BigDecimal price;
    private String userLevel;
    private BigDecimal originalPrice;
    private String errorMessage;

    public PriceCalculationResult(boolean valid, BigDecimal price, String userLevel, BigDecimal originalPrice, String errorMessage) {
        this.valid = valid;
        this.price = price;
        this.userLevel = userLevel;
        this.originalPrice = originalPrice;
        this.errorMessage = errorMessage;
    }

    public static PriceCalculationResult success(BigDecimal price, String userLevel, BigDecimal originalPrice) {
        return new PriceCalculationResult(true, price, userLevel, originalPrice, null);
    }

    public static PriceCalculationResult error(String errorMessage) {
        return new PriceCalculationResult(false, null, null, null, errorMessage);
    }

    // Getters
    public boolean isValid() { return valid; }
    public BigDecimal getPrice() { return price; }
    public String getUserLevel() { return userLevel; }
    public BigDecimal getOriginalPrice() { return originalPrice; }
    public String getErrorMessage() { return errorMessage; }
}
