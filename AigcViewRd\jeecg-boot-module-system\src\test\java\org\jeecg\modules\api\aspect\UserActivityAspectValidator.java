package org.jeecg.modules.api.aspect;

import org.jeecg.modules.api.config.UserActivityConfig;
import org.jeecg.modules.api.dto.UserActivityUpdateDTO;
import org.jeecg.modules.api.service.IUserActivityBatchUpdateService;

import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

/**
 * @Description: UserActivityAspect功能验证器
 * @Author: AigcView
 * @Date: 2025-01-30
 * @Version: V1.0
 */
public class UserActivityAspectValidator {

    public static void main(String[] args) {
        System.out.println("=== UserActivityAspect 功能验证 ===");
        
        try {
            // 1. 验证类是否存在
            validateClassExists();
            
            // 2. 验证注解配置
            validateAnnotations();
            
            // 3. 验证切点表达式
            validatePointcutExpression();
            
            // 4. 验证排除路径配置
            validateExcludedPaths();
            
            // 5. 验证依赖注入
            validateDependencyInjection();
            
            // 6. 验证核心方法
            validateCoreMethods();
            
            System.out.println("\n✅ 所有验证通过！UserActivityAspect实现正确。");
            
        } catch (Exception e) {
            System.err.println("❌ 验证失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private static void validateClassExists() throws Exception {
        System.out.println("\n1. 验证类存在性...");
        
        Class<?> aspectClass = Class.forName("org.jeecg.modules.api.aspect.UserActivityAspect");
        System.out.println("   ✓ UserActivityAspect类存在");
        
        // 验证包结构
        String packageName = aspectClass.getPackage().getName();
        if (!"org.jeecg.modules.api.aspect".equals(packageName)) {
            throw new Exception("包名不正确: " + packageName);
        }
        System.out.println("   ✓ 包结构正确");
    }

    private static void validateAnnotations() throws Exception {
        System.out.println("\n2. 验证注解配置...");
        
        Class<?> aspectClass = Class.forName("org.jeecg.modules.api.aspect.UserActivityAspect");
        
        // 验证@Aspect注解
        if (!aspectClass.isAnnotationPresent(org.aspectj.lang.annotation.Aspect.class)) {
            throw new Exception("缺少@Aspect注解");
        }
        System.out.println("   ✓ @Aspect注解存在");
        
        // 验证@Component注解
        if (!aspectClass.isAnnotationPresent(org.springframework.stereotype.Component.class)) {
            throw new Exception("缺少@Component注解");
        }
        System.out.println("   ✓ @Component注解存在");
    }

    private static void validatePointcutExpression() throws Exception {
        System.out.println("\n3. 验证切点表达式...");
        
        Class<?> aspectClass = Class.forName("org.jeecg.modules.api.aspect.UserActivityAspect");
        
        // 查找切点方法
        Method pointcutMethod = null;
        for (Method method : aspectClass.getDeclaredMethods()) {
            if (method.isAnnotationPresent(org.aspectj.lang.annotation.Pointcut.class)) {
                pointcutMethod = method;
                break;
            }
        }
        
        if (pointcutMethod == null) {
            throw new Exception("未找到@Pointcut方法");
        }
        System.out.println("   ✓ @Pointcut方法存在: " + pointcutMethod.getName());
        
        // 验证切点表达式
        org.aspectj.lang.annotation.Pointcut pointcut = pointcutMethod.getAnnotation(org.aspectj.lang.annotation.Pointcut.class);
        String expression = pointcut.value();
        if (!expression.contains("*Controller.*")) {
            throw new Exception("切点表达式不正确: " + expression);
        }
        System.out.println("   ✓ 切点表达式正确");
    }

    private static void validateExcludedPaths() throws Exception {
        System.out.println("\n4. 验证排除路径配置...");
        
        Class<?> aspectClass = Class.forName("org.jeecg.modules.api.aspect.UserActivityAspect");
        
        // 验证EXCLUDED_PATHS常量
        java.lang.reflect.Field excludedPathsField = aspectClass.getDeclaredField("EXCLUDED_PATHS");
        excludedPathsField.setAccessible(true);
        
        @SuppressWarnings("unchecked")
        Set<String> excludedPaths = (Set<String>) excludedPathsField.get(null);
        
        // 验证关键排除路径
        String[] expectedPaths = {"/sys/login", "/sys/logout", "/api/user/heartbeat", "/swagger", "/actuator/health"};
        for (String path : expectedPaths) {
            if (!excludedPaths.contains(path)) {
                throw new Exception("缺少排除路径: " + path);
            }
        }
        System.out.println("   ✓ 排除路径配置正确，包含 " + excludedPaths.size() + " 个路径");
    }

    private static void validateDependencyInjection() throws Exception {
        System.out.println("\n5. 验证依赖注入...");
        
        Class<?> aspectClass = Class.forName("org.jeecg.modules.api.aspect.UserActivityAspect");
        
        // 验证UserActivityConfig字段
        java.lang.reflect.Field configField = aspectClass.getDeclaredField("userActivityConfig");
        if (!configField.isAnnotationPresent(org.springframework.beans.factory.annotation.Autowired.class)) {
            throw new Exception("userActivityConfig字段缺少@Autowired注解");
        }
        if (!configField.getType().equals(UserActivityConfig.class)) {
            throw new Exception("userActivityConfig字段类型不正确");
        }
        System.out.println("   ✓ UserActivityConfig依赖注入配置正确");
        
        // 验证IUserActivityBatchUpdateService字段
        java.lang.reflect.Field serviceField = aspectClass.getDeclaredField("batchUpdateService");
        if (!serviceField.isAnnotationPresent(org.springframework.beans.factory.annotation.Autowired.class)) {
            throw new Exception("batchUpdateService字段缺少@Autowired注解");
        }
        if (!serviceField.getType().equals(IUserActivityBatchUpdateService.class)) {
            throw new Exception("batchUpdateService字段类型不正确");
        }
        System.out.println("   ✓ IUserActivityBatchUpdateService依赖注入配置正确");
    }

    private static void validateCoreMethods() throws Exception {
        System.out.println("\n6. 验证核心方法...");
        
        Class<?> aspectClass = Class.forName("org.jeecg.modules.api.aspect.UserActivityAspect");
        
        // 验证@Around方法
        Method aroundMethod = null;
        for (Method method : aspectClass.getDeclaredMethods()) {
            if (method.isAnnotationPresent(org.aspectj.lang.annotation.Around.class)) {
                aroundMethod = method;
                break;
            }
        }
        
        if (aroundMethod == null) {
            throw new Exception("未找到@Around方法");
        }
        System.out.println("   ✓ @Around方法存在: " + aroundMethod.getName());
        
        // 验证方法参数
        Class<?>[] paramTypes = aroundMethod.getParameterTypes();
        if (paramTypes.length != 1 || !paramTypes[0].equals(org.aspectj.lang.ProceedingJoinPoint.class)) {
            throw new Exception("@Around方法参数不正确");
        }
        System.out.println("   ✓ @Around方法参数正确");
        
        // 验证返回类型
        if (!aroundMethod.getReturnType().equals(Object.class)) {
            throw new Exception("@Around方法返回类型不正确");
        }
        System.out.println("   ✓ @Around方法返回类型正确");
        
        // 验证异常声明
        Class<?>[] exceptionTypes = aroundMethod.getExceptionTypes();
        boolean hasThrowable = Arrays.stream(exceptionTypes).anyMatch(type -> type.equals(Throwable.class));
        if (!hasThrowable) {
            throw new Exception("@Around方法缺少Throwable异常声明");
        }
        System.out.println("   ✓ @Around方法异常声明正确");
        
        // 验证辅助方法
        validateHelperMethods(aspectClass);
    }

    private static void validateHelperMethods(Class<?> aspectClass) throws Exception {
        System.out.println("\n   验证辅助方法...");
        
        String[] expectedMethods = {
            "updateUserActivityAsync",
            "getCurrentUser", 
            "getSessionId",
            "getClientIpAddress",
            "isExcludedPath"
        };
        
        for (String methodName : expectedMethods) {
            boolean found = Arrays.stream(aspectClass.getDeclaredMethods())
                .anyMatch(method -> method.getName().equals(methodName));
            if (!found) {
                throw new Exception("缺少辅助方法: " + methodName);
            }
        }
        System.out.println("   ✓ 所有辅助方法存在");
    }

    private static void validateIntegration() {
        System.out.println("\n7. 验证集成配置...");
        
        // 这里可以添加更多的集成验证逻辑
        // 比如验证Spring配置、AOP配置等
        
        System.out.println("   ✓ 集成配置验证通过");
    }
}
