package org.jeecg.modules.jianyingpro.service.internal;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.jianying.dto.GetImageAnimationsRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 超级剪映小助手 - ID解析服务
 * 复制自JianyingIdResolverService，保持所有原有业务逻辑不变
 * 
 * <AUTHOR>
 * @since 2025-01-01
 */
@Slf4j
@Service
public class JianyingProIdResolverService {
    
    @Autowired
    private JianyingProAssistantService jianyingAssistantService;
    
    @Autowired
    private CacheManager cacheManager;
    
    @Autowired
    private RestTemplate restTemplate;
    
    // 转场API配置
    private static final String TRANSITION_API_URL = "https://lv-api-sinfonlinec.ulikecam.com/artist/v1/effect/search";
    
    // 转场本地缓存（24小时过期）
    private final Map<String, TransitionCacheEntry> transitionCache = new ConcurrentHashMap<>();
    private static final long TRANSITION_CACHE_EXPIRE_TIME = 24 * 60 * 60 * 1000L;

    /**
     * 动画信息类
     */
    public static class AnimationInfo {
        private String id;
        private String resourceId;  // 添加resourceId字段
        private String categoryId;  // 添加categoryId字段
        private String name;
        private String type;
        private String category;
        private String panel;

        // 构造函数
        public AnimationInfo() {}

        public AnimationInfo(String id, String name, String type, String category, String panel) {
            this.id = id;
            this.name = name;
            this.type = type;
            this.category = category;
            this.panel = panel;
        }

        // Getter和Setter方法
        public String getId() { return id; }
        public void setId(String id) { this.id = id; }
        public String getResourceId() { return resourceId; }  // 添加resourceId的getter
        public void setResourceId(String resourceId) { this.resourceId = resourceId; }  // 添加resourceId的setter
        public String getCategoryId() { return categoryId; }  // 添加categoryId的getter
        public void setCategoryId(String categoryId) { this.categoryId = categoryId; }  // 添加categoryId的setter
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
        public String getType() { return type; }
        public void setType(String type) { this.type = type; }
        public String getCategory() { return category; }
        public void setCategory(String category) { this.category = category; }
        public String getPanel() { return panel; }
        public void setPanel(String panel) { this.panel = panel; }
    }

    /**
     * 转场信息类
     */
    public static class TransitionInfo {
        private String resourceId;
        private String effectId;
        private String name;
        private String category;
        private boolean isOverlap = false;  // 添加isOverlap字段

        // 构造函数
        public TransitionInfo() {}

        public TransitionInfo(String resourceId, String effectId, String name, String category) {
            this.resourceId = resourceId;
            this.effectId = effectId;
            this.name = name;
            this.category = category;
        }

        // Getter和Setter方法
        public String getResourceId() { return resourceId; }
        public void setResourceId(String resourceId) { this.resourceId = resourceId; }
        public String getEffectId() { return effectId; }
        public void setEffectId(String effectId) { this.effectId = effectId; }
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
        public String getCategory() { return category; }
        public void setCategory(String category) { this.category = category; }
        public boolean isOverlap() { return isOverlap; }
        public void setOverlap(boolean overlap) { this.isOverlap = overlap; }
    }

    /**
     * 转场缓存条目
     */
    private static class TransitionCacheEntry {
        private final TransitionInfo transitionInfo;
        private final long timestamp;

        public TransitionCacheEntry(TransitionInfo transitionInfo) {
            this.transitionInfo = transitionInfo;
            this.timestamp = System.currentTimeMillis();
        }

        public TransitionInfo getTransitionInfo() {
            return transitionInfo;
        }

        public boolean isExpired() {
            return System.currentTimeMillis() - timestamp > TRANSITION_CACHE_EXPIRE_TIME;
        }
    }

    /**
     * 根据动画名称查找动画信息（图片动画）
     */
    public AnimationInfo findAnimationByName(String animationName, String animationType) {
        return findAnimationByName(animationName, animationType, "image");
    }

    /**
     * 根据动画名称查找动画信息（支持指定面板类型）
     */
    public AnimationInfo findAnimationByName(String animationName, String animationType, String panel) {
        if (animationName == null || animationName.trim().isEmpty()) {
            log.warn("动画名称为空，无法查找");
            return null;
        }

        String cleanName = animationName.trim();
        log.info("开始查找动画: name={}, type={}, panel={}", cleanName, animationType, panel);

        // 1. 先从Redis缓存中查找
        AnimationInfo animation = findInSpecificCache(cleanName, animationType, panel);
        if (animation != null) {
            log.info("从缓存中找到动画: {}", cleanName);
            return animation;
        }

        // 2. 如果没找到，触发对应类型的API调用更新缓存
        log.info("缓存中未找到动画，触发API更新缓存: type={}, panel={}", animationType, panel);
        refreshSpecificAnimationCache(animationType, panel);

        // 3. 再次查找
        animation = findInSpecificCache(cleanName, animationType, panel);
        if (animation != null) {
            log.info("API更新后找到动画: {}", cleanName);
            return animation;
        }

        // 4. 如果还是没找到，可能是类型判断错误，加载所有类型
        log.warn("指定类型未找到动画，尝试加载所有类型: {}", cleanName);
        refreshAllAnimationCache(panel);

        // 5. 最后一次查找（遍历所有类型）
        animation = findInAllCache(cleanName, panel);
        if (animation != null) {
            log.info("全量更新后找到动画: {}", cleanName);
            return animation;
        }

        log.error("未找到动画: {}", cleanName);
        return null;
    }

    /**
     * 根据转场名称查找转场信息
     */
    public TransitionInfo findTransitionByName(String transitionName) {
        if (transitionName == null || transitionName.trim().isEmpty()) {
            log.warn("转场名称为空，无法查找");
            return null;
        }

        String cleanName = transitionName.trim();
        log.info("开始查找转场: {}", cleanName);

        // 1. 先检查本地缓存
        TransitionCacheEntry cacheEntry = transitionCache.get(cleanName);
        if (cacheEntry != null && !cacheEntry.isExpired()) {
            log.info("从本地缓存中找到转场: {}", cleanName);
            return cacheEntry.getTransitionInfo();
        } else if (cacheEntry != null && cacheEntry.isExpired()) {
            // 缓存过期，移除
            transitionCache.remove(cleanName);
            log.info("转场缓存已过期，移除: {}", cleanName);
        }
        
        // 2. 调用转场API
        try {
            TransitionInfo transitionInfo = callTransitionAPI(cleanName);
            if (transitionInfo != null) {
                // 缓存结果
                transitionCache.put(cleanName, new TransitionCacheEntry(transitionInfo));
                log.info("转场API调用成功: {} -> resource_id: {}, effect_id: {}",
                        cleanName, transitionInfo.getResourceId(), transitionInfo.getEffectId());
                return transitionInfo;
            } else {
                log.warn("转场API未找到: {}", cleanName);
                return null;
            }
        } catch (Exception e) {
            log.error("转场API调用失败: {}", cleanName, e);
            return null;
        }
    }

    /**
     * 从指定类型的Redis缓存中查找动画（图片动画）
     */
    private AnimationInfo findInSpecificCache(String animationName, String animationType) {
        return findInSpecificCache(animationName, animationType, "image");
    }

    /**
     * 从指定类型的Redis缓存中查找动画（支持指定面板类型）
     */
    private AnimationInfo findInSpecificCache(String animationName, String animationType, String panel) {
        try {
            String cacheKey = String.format("jianying:%s_animations:%s", panel, animationType);
            Cache cache = cacheManager.getCache("jianyingCache");
            if (cache != null) {
                Cache.ValueWrapper wrapper = cache.get(cacheKey);
                if (wrapper != null && wrapper.get() != null) {
                    JSONArray animations = (JSONArray) wrapper.get();
                    return findAnimationInArray(animations, animationName);
                }
            }
        } catch (Exception e) {
            log.warn("从Redis缓存查找动画失败: {}", animationName, e);
        }
        return null;
    }

    /**
     * 从所有类型的缓存中查找动画
     */
    private AnimationInfo findInAllCache(String animationName, String panel) {
        String[] animationTypes = {"in", "out", "combo", "loop"};
        
        for (String type : animationTypes) {
            AnimationInfo animation = findInSpecificCache(animationName, type, panel);
            if (animation != null) {
                return animation;
            }
        }
        return null;
    }

    /**
     * 刷新指定类型的动画缓存
     */
    private void refreshSpecificAnimationCache(String animationType, String panel) {
        try {
            GetImageAnimationsRequest request = new GetImageAnimationsRequest();
            request.setZjType(animationType);  // 使用正确的字段名
            // 注意：GetImageAnimationsRequest中没有panel字段，所以注释掉
            // request.setZjPanel(panel);

            // 调用助手服务的API来更新缓存
            jianyingAssistantService.getImageAnimations(request);
            log.info("刷新动画缓存成功: type={}, panel={}", animationType, panel);
        } catch (Exception e) {
            log.error("刷新动画缓存失败: type={}, panel={}", animationType, panel, e);
        }
    }

    /**
     * 刷新所有类型的动画缓存
     */
    private void refreshAllAnimationCache(String panel) {
        String[] animationTypes = {"in", "out", "combo", "loop"};
        
        for (String type : animationTypes) {
            refreshSpecificAnimationCache(type, panel);
        }
    }

    /**
     * 在动画数组中查找指定名称的动画
     */
    private AnimationInfo findAnimationInArray(JSONArray animations, String animationName) {
        if (animations == null || animations.isEmpty()) {
            return null;
        }

        for (int i = 0; i < animations.size(); i++) {
            JSONObject animation = animations.getJSONObject(i);
            if (animation != null) {
                String name = animation.getString("name");
                if (animationName.equals(name)) {
                    AnimationInfo info = new AnimationInfo();
                    info.setId(animation.getString("id"));
                    info.setName(name);
                    info.setType(animation.getString("type"));
                    info.setCategory(animation.getString("category"));
                    info.setPanel(animation.getString("panel"));
                    return info;
                }
            }
        }
        return null;
    }

    /**
     * 调用转场API
     */
    private TransitionInfo callTransitionAPI(String transitionName) {
        try {
            // 构建请求URL（完全复制自稳定版）
            String url = TRANSITION_API_URL + "?aid=3704&version_name=5.9.0&device_platform=windows";

            // 构建请求体（完全复制自稳定版）
            JSONObject requestBody = new JSONObject();
            requestBody.put("count", 1);
            requestBody.put("effect_type", 19); // 转场类型（修复：使用稳定版成功的19）
            requestBody.put("query", transitionName);

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            HttpEntity<String> entity = new HttpEntity<>(requestBody.toJSONString(), headers);

            // 发送请求
            ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.POST, entity, String.class);

            if (response.getStatusCode() == HttpStatus.OK) {
                String responseBody = response.getBody();
                return parseTransitionResponseStable(responseBody, transitionName);
            } else {
                log.error("转场API请求失败，状态码: {}", response.getStatusCode());
                return null;
            }

        } catch (Exception e) {
            log.error("调用转场API失败: {}", transitionName, e);
            return null;
        }
    }

    /**
     * 解析转场API响应（完全复制自稳定版parseTransitionResponse逻辑）
     */
    private TransitionInfo parseTransitionResponseStable(String responseBody, String searchKeyword) {
        try {
            JSONObject responseData = JSONObject.parseObject(responseBody);

            // 检查返回状态
            String ret = responseData.getString("ret");
            if (!"0".equals(ret)) {
                log.error("转场API返回错误: ret={}, errmsg={}", ret, responseData.getString("errmsg"));
                return null;
            }

            // 解析数据
            JSONObject data = responseData.getJSONObject("data");
            JSONArray effectItemList = data.getJSONArray("effect_item_list");

            if (effectItemList != null && effectItemList.size() > 0) {
                JSONObject firstEffect = effectItemList.getJSONObject(0);
                JSONObject commonAttr = firstEffect.getJSONObject("common_attr");

                if (commonAttr != null) {
                    String resourceId = commonAttr.getString("id");
                    String effectId = commonAttr.getString("effect_id");
                    String title = commonAttr.getString("title");

                    // 解析is_overlap
                    boolean isOverlap = true; // 默认值
                    String sdkExtra = commonAttr.getString("sdk_extra");
                    if (sdkExtra != null && !sdkExtra.isEmpty()) {
                        try {
                            JSONObject sdkExtraObj = JSONObject.parseObject(sdkExtra);
                            JSONObject transition = sdkExtraObj.getJSONObject("transition");
                            if (transition != null && transition.containsKey("isOverlap")) {
                                isOverlap = transition.getBoolean("isOverlap");
                            }
                        } catch (Exception e) {
                            log.warn("解析sdk_extra失败，使用默认is_overlap=true: {}", sdkExtra, e);
                        }
                    }

                    TransitionInfo transitionInfo = new TransitionInfo(resourceId, effectId, title, "基础");
                    transitionInfo.setOverlap(isOverlap);
                    return transitionInfo;
                }
            }

            log.warn("转场API响应中未找到有效数据: {}", searchKeyword);
            return null;

        } catch (Exception e) {
            log.error("解析转场API响应失败: {}", searchKeyword, e);
            return null;
        }
    }

    /**
     * 解析转场API响应（旧版本，保留兼容性）
     */
    private TransitionInfo parseTransitionResponse(String responseBody, String searchKeyword) {
        try {
            JSONObject response = JSONObject.parseObject(responseBody);
            
            if (response == null) {
                log.warn("转场API响应为空: {}", searchKeyword);
                return null;
            }
            
            JSONObject data = response.getJSONObject("data");
            if (data == null) {
                log.warn("转场API响应data为空: {}", searchKeyword);
                return null;
            }
            
            JSONArray effects = data.getJSONArray("effects");
            if (effects == null || effects.isEmpty()) {
                log.warn("转场API响应effects为空: {}", searchKeyword);
                return null;
            }
            
            // 取第一个匹配的转场
            JSONObject effect = effects.getJSONObject(0);
            if (effect == null) {
                log.warn("转场API响应第一个转场为空: {}", searchKeyword);
                return null;
            }
            
            // 构建转场信息
            TransitionInfo transitionInfo = new TransitionInfo();
            transitionInfo.setName(effect.getString("name"));
            transitionInfo.setResourceId(effect.getString("resource_id"));
            transitionInfo.setEffectId(effect.getString("effect_id"));
            transitionInfo.setCategory(effect.getString("category"));
            
            return transitionInfo;
            
        } catch (Exception e) {
            log.error("解析转场API响应失败: {}", searchKeyword, e);
            return null;
        }
    }
}
