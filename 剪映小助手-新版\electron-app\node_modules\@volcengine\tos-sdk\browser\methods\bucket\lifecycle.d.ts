import { StorageClassType } from '../../TosExportEnum';
import TOSBase from '../base';
interface LifecycleRule {
    ID?: string;
    Prefix?: string;
    Status: 'Enabled' | 'Disabled';
    Filter?: {
        GreaterThanIncludeEqual?: 'Enabled' | 'Disabled';
        LessThanIncludeEqual?: 'Enabled' | 'Disabled';
        /** unit bit */
        ObjectSizeGreaterThan?: number;
        /** unit bit */
        ObjectSizeLessThan?: number;
    };
    Expiration?: {
        Date?: string;
        Days?: number;
    };
    Days?: number;
    Date?: string;
    NoncurrentVersionExpiration?: {
        NoncurrentDays?: number;
        NoncurrentDate?: string;
    };
    AbortIncompleteMultipartUpload?: {
        DaysAfterInitiation?: number;
    };
    DaysAfterInitiation?: number;
    Transitions?: {
        StorageClass: StorageClassType;
        Days?: number;
        Date?: string;
    }[];
    /**
     * @private unstable
     */
    AccessTimeTransitions?: {
        StorageClass: StorageClassType;
        Days?: number;
    }[];
    /**
     * @private unstable
     */
    NoncurrentVersionAccessTimeTransitions?: {
        StorageClass: StorageClassType;
        NoncurrentDays?: number;
    }[];
    NoncurrentVersionTransitions?: {
        StorageClass?: StorageClassType;
        NoncurrentDays?: number;
        NoncurrentDate?: string;
    }[];
    Tags?: {
        Key?: string;
        Value?: string;
    }[];
}
export interface PutBucketLifecycleInput {
    bucket: string;
    rules: LifecycleRule[];
    allowSameActionOverlap?: boolean;
}
export interface PutBucketLifecycleOutput {
}
export declare function putBucketLifecycle(this: TOSBase, input: PutBucketLifecycleInput): Promise<import("../base").TosResponse<DeleteBucketLifecycleOutput>>;
export interface GetBucketLifecycleInput {
    bucket: string;
}
export interface GetBucketLifecycleOutput {
    Rules: LifecycleRule[];
    AllowSameActionOverlap?: boolean;
}
export declare function getBucketLifecycle(this: TOSBase, input: GetBucketLifecycleInput): Promise<import("../base").TosResponse<GetBucketLifecycleOutput>>;
export interface DeleteBucketLifecycleInput {
    bucket: string;
}
export interface DeleteBucketLifecycleOutput {
}
export declare function deleteBucketLifecycle(this: TOSBase, input: DeleteBucketLifecycleInput): Promise<import("../base").TosResponse<DeleteBucketLifecycleOutput>>;
export {};
