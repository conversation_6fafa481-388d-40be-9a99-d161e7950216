{"name": "@volcengine/tos-sdk", "version": "2.7.4", "license": "MIT", "files": ["browser", "dist", "miniprogram_dist"], "repository": {"type": "git", "url": "https://github.com/volcengine/ve-tos-js-sdk"}, "engines": {"node": ">=10"}, "scripts": {"start": "tsdx watch", "build": "node ./scripts/build.js", "build:node": "tsdx build --target node --name TOS --tsconfig tsconfig-build.json", "build:browser": "tsdx build --format cjs,esm,umd --name TOS --tsconfig tsconfig-build.json", "build:proxy": "tsc --esModuleInterop --declaration --outDir ./out ./src/proxy.ts", "test": "yarn build && yarn test:node && cp ./coverage/coverage-final.json ./coverage/coverage-node-final.json && yarn test:browser && cp ./coverage/coverage-final.json ./coverage/coverage-browser-final.json && node ./scripts/mapCoverage.js", "test:node": "NODE_ENV=development TARGET_ENVIRONMENT=node tsdx test test/node/** --coverage --config=test/node/jest.config.js --testEnvironment=node --no-cache -t ", "test:browser": "NODE_ENV=development TARGET_ENVIRONMENT=browser tsdx test test/browser/** --coverage --config=test/browser/jest.config.js --testEnvironment=node --no-cache -t ", "docs": "npx typedoc src/*", "deploy:docs": "./scripts/deployDocs.sh", "lint": "tsdx lint", "prettier": "npx prettier --write src/**/*.ts "}, "husky": {"hooks": {"pre-commit": "yarn prettier"}}, "prettier": {"printWidth": 80, "semi": true, "singleQuote": true, "trailingComma": "es5"}, "browserslist": ["Chrome >= 70"], "jest": {"roots": ["./test"], "moduleNameMapper": {"^@/(.+)$": "<rootDir>/src/$1"}, "coverageReporters": ["json", "cobertura"], "collectCoverageFrom": ["src/**/{!(safer.js),}"]}, "typings": "dist/index.d.ts", "main": "dist/index.js", "module": "dist/tos.esm.js", "browser": {"dist/index.js": "./browser/index.js", "dist/tos.esm.js": "./browser/tos.esm.js"}, "miniprogram": "miniprogram_dist", "author": "<EMAIL>", "devDependencies": {"@types/debug": "^4.1.7", "@rollup/plugin-alias": "^3.1.5", "@types/crypto-js": "^4.1.1", "@types/express": "^4.17.13", "@types/lodash": "^4.14.172", "@types/node": "^17.0.24", "dotenv": "16.0.0", "execa": "^5.1.1", "form-data": "^4.0.0", "fs-extra": "^10.0.0", "husky": "^4.3.6", "istanbul-api": "^3.0.0", "miniprogram-api-typings": "^2.10.1-1", "rollup-plugin-node-builtins": "^2.1.2", "rollup-plugin-node-globals": "^1.4.0", "tsdx2": "^0.14.1", "tslib": "^2.3.0", "typedoc": "^0.21.9", "prettier": "^2.8.8", "typescript": "^4.3.5"}, "dependencies": {"tos-crc64-js": "0.0.1", "axios": "^0.21.1", "axios-adapter-uniapp": "^0.1.4", "crypto-js": "^4.2.0", "debug": "^4.3.4", "http-proxy-middleware": "^2.0.1", "lodash": "^4.17.21", "qs": "^6.11.2", "type-fest": "^1.4.0"}, "resolutions": {"**/typescript": "^4.3.5"}}