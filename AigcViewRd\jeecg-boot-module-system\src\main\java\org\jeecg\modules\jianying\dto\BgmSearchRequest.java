package org.jeecg.modules.jianying.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

/**
 * 背景音乐搜索请求参数
 * 
 * <AUTHOR>
 * @date 2025-01-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BgmSearchRequest extends BaseJianyingRequest {
    
    @ApiModelProperty(value = "搜索关键词（必填）", required = true, example = "我的天空")
    @NotBlank(message = "搜索关键词不能为空")
    @JsonProperty("keyword")
    private String zjKeyword;

    @ApiModelProperty(value = "音乐类型筛选：0-默认所有，1-VIP（筛选paid_type为subscribe），2-免费（筛选paid_type为free）", required = true, example = "0")
    @NotBlank(message = "音乐类型不能为空")
    @Pattern(regexp = "^[0-2]$", message = "音乐类型只能是0、1或2")
    @JsonProperty("type")
    private String zjType = "0"; // 默认值为"0"
    
    @Override
    public String getSummary() {
        return "BgmSearchRequest{" +
               "accessKey=" + (getAccessKey() != null && getAccessKey().length() > 10 ? 
                              getAccessKey().substring(0, 10) + "***" : "null") +
               ", keyword=" + zjKeyword +
               ", type=" + zjType +
               "}";
    }
    
    /**
     * 验证请求参数
     * 
     * @return true表示验证通过，false表示验证失败
     */
    @Override
    public boolean validateBase() {
        if (!super.validateBase()) {
            return false;
        }
        
        if (zjKeyword == null || zjKeyword.trim().isEmpty()) {
            return false;
        }
        
        // 如果zjType为空，设置默认值
        if (zjType == null || zjType.trim().isEmpty()) {
            zjType = "0";
        }

        if (!zjType.matches("^[0-2]$")) {
            return false;
        }
        
        return true;
    }
}
