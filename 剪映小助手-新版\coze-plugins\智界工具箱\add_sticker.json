{"openapi": "3.0.0", "info": {"title": "剪映小助手_智界工具箱 - 添加贴纸", "description": "添加贴纸，批处理时，并发要设置成1", "version": "1.0.0"}, "servers": [{"url": "https://www.aigcview.com"}], "paths": {"/jeecg-boot/api/jianying/add_sticker": {"post": {"summary": "添加贴纸", "description": "添加贴纸，批处理时，并发要设置成1", "operationId": "add_sticker", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"access_key": {"type": "string", "description": "访问密钥（必填）", "example": "JianyingAPI_2025_SecureAccess_AigcView", "default": "JianyingAPI_2025_SecureAccess_AigcView"}, "end": {"type": "integer", "description": "结束时间（必填）", "example": 5000000}, "scale": {"type": "number", "description": "缩放，0-5", "minimum": 0, "maximum": 5, "example": 1.0}, "start": {"type": "integer", "description": "开始时间（必填）", "example": 0}, "sticker_id": {"type": "string", "description": "贴纸ID，从智界数据箱search_sticker里面获取（必填）", "example": "sticker_123456"}, "transform_x": {"type": "integer", "description": "剪映的x位置", "example": 100}, "transform_y": {"type": "integer", "description": "剪映的y位置", "example": 200}, "draft_url": {"type": "string", "description": "草稿地址（必填）", "example": "https://example.com/draft/123"}}, "required": ["access_key", "end", "start", "sticker_id", "draft_url"], "additionalProperties": false}}}}, "responses": {"200": {"description": "成功添加贴纸", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "description": "响应消息", "example": "不知道导入的请看：https://www.aigcview.com/JianYingDraft?draft=https://aigcview-plub.tos-s3-cn-guangzhou.volces.com/jianying-assistant/drafts/2025/01/05/draft_123456.json"}, "draft_url": {"type": "string", "description": "更新后的草稿地址", "example": "https://aigcview-plub.tos-s3-cn-guangzhou.volces.com/jianying-assistant/drafts/2025/01/05/draft_123456.json"}}, "required": ["message", "draft_url"]}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "description": "错误信息", "example": "参数验证失败"}}, "required": ["error"]}}}}, "500": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "description": "错误信息", "example": "添加贴纸失败: 内部服务器错误"}}, "required": ["error"]}}}}}}}}}