package org.jeecg.modules.demo.referralreward.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.demo.referralreward.entity.AicgUserReferralReward;
import org.jeecg.modules.demo.referralreward.service.IAicgUserReferralRewardService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;

/**
 * @Description: 推荐奖励记录表
 * @Author: jeecg-boot
 * @Date:   2025-06-18
 * @Version: V1.0
 */
@Api(tags="推荐奖励记录表")
@RestController
@RequestMapping("/demo/referralreward")
@Slf4j
public class AicgUserReferralRewardController extends JeecgController<AicgUserReferralReward, IAicgUserReferralRewardService> {
	@Autowired
	private IAicgUserReferralRewardService aicgUserReferralRewardService;
	
	/**
	 * 分页列表查询
	 */
	@AutoLog(value = "推荐奖励记录表-分页列表查询")
	@ApiOperation(value="推荐奖励记录表-分页列表查询", notes="推荐奖励记录表-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(AicgUserReferralReward aicgUserReferralReward,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<AicgUserReferralReward> queryWrapper = QueryGenerator.initQueryWrapper(aicgUserReferralReward, req.getParameterMap());
		Page<AicgUserReferralReward> page = new Page<AicgUserReferralReward>(pageNo, pageSize);
		IPage<AicgUserReferralReward> pageList = aicgUserReferralRewardService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 * 添加
	 */
	@AutoLog(value = "推荐奖励记录表-添加")
	@ApiOperation(value="推荐奖励记录表-添加", notes="推荐奖励记录表-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody AicgUserReferralReward aicgUserReferralReward) {
		aicgUserReferralRewardService.save(aicgUserReferralReward);
		return Result.OK("添加成功！");
	}
	
	/**
	 * 编辑
	 */
	@AutoLog(value = "推荐奖励记录表-编辑")
	@ApiOperation(value="推荐奖励记录表-编辑", notes="推荐奖励记录表-编辑")
	@PutMapping(value = "/edit")
	public Result<?> edit(@RequestBody AicgUserReferralReward aicgUserReferralReward) {
		aicgUserReferralRewardService.updateById(aicgUserReferralReward);
		return Result.OK("编辑成功!");
	}
	
	/**
	 * 通过id删除
	 */
	@AutoLog(value = "推荐奖励记录表-通过id删除")
	@ApiOperation(value="推荐奖励记录表-通过id删除", notes="推荐奖励记录表-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		aicgUserReferralRewardService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 * 批量删除
	 */
	@AutoLog(value = "推荐奖励记录表-批量删除")
	@ApiOperation(value="推荐奖励记录表-批量删除", notes="推荐奖励记录表-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.aicgUserReferralRewardService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 */
	@AutoLog(value = "推荐奖励记录表-通过id查询")
	@ApiOperation(value="推荐奖励记录表-通过id查询", notes="推荐奖励记录表-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		AicgUserReferralReward aicgUserReferralReward = aicgUserReferralRewardService.getById(id);
		if(aicgUserReferralReward==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(aicgUserReferralReward);
	}

    /**
	 * 导出excel
	 */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, AicgUserReferralReward aicgUserReferralReward) {
        return super.exportXls(request, aicgUserReferralReward, AicgUserReferralReward.class, "推荐奖励记录表");
    }

    /**
     * 通过excel导入数据
     */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, AicgUserReferralReward.class);
    }
    
    /**
     * 根据推荐人ID查询奖励记录
     */
    @AutoLog(value = "根据推荐人ID查询奖励记录")
    @ApiOperation(value="根据推荐人ID查询奖励记录", notes="根据推荐人ID查询奖励记录")
    @GetMapping(value = "/getByReferrerId")
    public Result<?> getByReferrerId(@RequestParam(name="referrerId",required=true) String referrerId) {
        List<AicgUserReferralReward> list = aicgUserReferralRewardService.getByReferrerId(referrerId);
        return Result.OK(list);
    }
    
    /**
     * 获取奖励统计
     */
    @AutoLog(value = "获取奖励统计")
    @ApiOperation(value="获取奖励统计", notes="获取奖励统计")
    @GetMapping(value = "/getStats")
    public Result<?> getRewardStats(@RequestParam(name="referrerId",required=true) String referrerId) {
        Map<String, Object> stats = aicgUserReferralRewardService.getRewardStats(referrerId);
        return Result.OK(stats);
    }
    
    /**
     * 发放奖励
     */
    @AutoLog(value = "发放奖励")
    @ApiOperation(value="发放奖励", notes="发放奖励")
    @PostMapping(value = "/pay")
    public Result<?> payReward(@RequestParam(name="id",required=true) String id,
                              @RequestParam(name="transactionId",required=false) String transactionId) {
        boolean success = aicgUserReferralRewardService.payReward(id, transactionId, "system");
        if (success) {
            return Result.OK("发放成功!");
        } else {
            return Result.error("发放失败!");
        }
    }
    
    /**
     * 取消奖励
     */
    @AutoLog(value = "取消奖励")
    @ApiOperation(value="取消奖励", notes="取消奖励")
    @PostMapping(value = "/cancel")
    public Result<?> cancelReward(@RequestParam(name="id",required=true) String id) {
        boolean success = aicgUserReferralRewardService.cancelReward(id, "system");
        if (success) {
            return Result.OK("取消成功!");
        } else {
            return Result.error("取消失败!");
        }
    }
    
    /**
     * 批量发放奖励
     */
    @AutoLog(value = "批量发放奖励")
    @ApiOperation(value="批量发放奖励", notes="批量发放奖励")
    @PostMapping(value = "/batchPay")
    public Result<?> batchPayRewards(@RequestParam(name="ids",required=true) String ids) {
        List<String> rewardIds = Arrays.asList(ids.split(","));
        int successCount = aicgUserReferralRewardService.batchPayRewards(rewardIds, "system");
        return Result.OK("成功发放 " + successCount + " 条奖励");
    }
}
