package org.jeecg.modules.jianying.dto;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 字幕数据生成器请求参数
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CaptionInfosRequest extends BaseJianyingRequest {
    
    @ApiModelProperty(value = "文本列表（必填）", required = true,
                     example = "[\"这是第一段字幕\", \"这是第二段字幕\"]")
    @NotEmpty(message = "texts不能为空")
    @JsonProperty("texts")
    private List<String> zjTexts;

    @ApiModelProperty(value = "时间节点，只接收结构：[{\"start\":0,\"end\":4612}]，一般从audio_timeline节点的输出获取（必填）", required = true,
                     example = "[{\"start\": 0, \"end\": 4612000}]")
    @NotEmpty(message = "timelines不能为空")
    @JsonProperty("timelines")
    private List<JSONObject> zjTimelines;

    @ApiModelProperty(value = "文字大小", example = "30")
    @JsonProperty("font_size")
    private Integer zjFontSize;

    @ApiModelProperty(value = "关键词字大小", example = "35")
    @JsonProperty("keyword_font_size")
    private Integer zjKeywordFontSize;

    @ApiModelProperty(value = "对应剪映的循环动画名字，多个动画请用英文|分割，比如：扫光|晃动", example = "扫光|晃动")
    @JsonProperty("loop_animation")
    private String zjLoopAnimation;

    @ApiModelProperty(value = "出场动画时长", example = "1000000")
    @JsonProperty("out_animation_duration")
    private Integer zjOutAnimationDuration;

    @ApiModelProperty(value = "循环动画时长", example = "2000000")
    @JsonProperty("loop_animation_duration")
    private Integer zjLoopAnimationDuration;

    @ApiModelProperty(value = "对应剪映的出场动画名字，多个动画请用英文|分割，比如：消散|闭幕", example = "消散|闭幕")
    @JsonProperty("out_animation")
    private String zjOutAnimation;

    @ApiModelProperty(value = "对应剪映的入场动画名字，多个动画请用英文|分割，比如：飞入|放大", example = "飞入|放大")
    @JsonProperty("in_animation")
    private String zjInAnimation;

    @ApiModelProperty(value = "入场动画时长", example = "1000000")
    @JsonProperty("in_animation_duration")
    private Integer zjInAnimationDuration;

    @ApiModelProperty(value = "关键词颜色", example = "#ff0000")
    @JsonProperty("keyword_color")
    private String zjKeywordColor;

    @ApiModelProperty(value = "文本里面的重点词列表", example = "[\"重点\", \"关键词\"]")
    @JsonProperty("keywords")
    private List<String> zjKeywords;
    
    @Override
    public String getSummary() {
        return "CaptionInfosRequest{" +
               "accessKey=" + (getAccessKey() != null && getAccessKey().length() > 10 ? 
                              getAccessKey().substring(0, 10) + "***" : "null") +
               ", textsCount=" + (zjTexts != null ? zjTexts.size() : 0) +
               ", timelinesCount=" + (zjTimelines != null ? zjTimelines.size() : 0) +
               ", fontSize=" + zjFontSize +
               "}";
    }
}
