
LumiManager = LumiManager or {} ---@type LumiManager
LumiManager.__typename = "LumiManager"
LumiManager.__supername = "ScriptComponent"
LumiManager.__scriptname = ""
LumiManager.__scriptpath = ""


LumiEffect = LumiEffect or {} ---@type LumiEffect
LumiEffect.__typename = "LumiEffect"
LumiEffect.__supername = "ScriptComponent"
LumiEffect.__scriptname = ""
LumiEffect.__scriptpath = ""


ScriptCompContrast = ScriptCompContrast or {} ---@type ScriptCompContrast
ScriptCompContrast.__typename = "ScriptCompContrast"
ScriptCompContrast.__supername = "ScriptComponent"
ScriptCompContrast.__scriptname = ""
ScriptCompContrast.__scriptpath = ""


LumiLayer = LumiLayer or {} ---@type LumiLayer
LumiLayer.__typename = "LumiLayer"
LumiLayer.__supername = "ScriptComponent"
LumiLayer.__scriptname = ""
LumiLayer.__scriptpath = ""


Lumi3DShape = Lumi3DShape or {} ---@type Lumi3DShape
Lumi3DShape.__typename = "Lumi3DShape"
Lumi3DShape.__supername = "ScriptComponent"
Lumi3DShape.__scriptname = ""
Lumi3DShape.__scriptpath = ""


ScriptCompExposure = ScriptCompExposure or {} ---@type ScriptCompExposure
ScriptCompExposure.__typename = "ScriptCompExposure"
ScriptCompExposure.__supername = "ScriptComponent"
ScriptCompExposure.__scriptname = ""
ScriptCompExposure.__scriptpath = ""

