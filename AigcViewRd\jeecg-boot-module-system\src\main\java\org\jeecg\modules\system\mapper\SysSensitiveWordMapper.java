package org.jeecg.modules.system.mapper;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.system.entity.SysSensitiveWord;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: 敏感词管理
 * @Author: jeecg-boot
 * @Date: 2025-06-22
 * @Version: V1.0
 */
public interface SysSensitiveWordMapper extends BaseMapper<SysSensitiveWord> {

    /**
     * 获取所有启用的敏感词
     * @return 敏感词列表
     */
    List<String> getEnabledWords();

    /**
     * 批量插入敏感词
     * @param words 敏感词列表
     * @return 插入数量
     */
    int batchInsert(@Param("words") List<SysSensitiveWord> words);

    /**
     * 增加敏感词命中次数
     * @param word 敏感词
     * @return 更新数量
     */
    int incrementHitCount(@Param("word") String word);

    /**
     * 获取敏感词统计信息
     * @return 统计信息
     */
    Map<String, Object> getStatistics();

    /**
     * 获取热门敏感词TOP10
     * @return 热门敏感词列表
     */
    List<Map<String, Object>> getTopHitWords(@Param("limit") int limit);

    /**
     * 按分类统计敏感词数量
     * @return 分类统计
     */
    List<Map<String, Object>> getCategoryStatistics();

    /**
     * 按级别统计敏感词数量
     * @return 级别统计
     */
    List<Map<String, Object>> getLevelStatistics();

    /**
     * 检查敏感词是否存在
     * @param word 敏感词
     * @return 敏感词信息
     */
    SysSensitiveWord checkWordExists(@Param("word") String word);

    /**
     * 获取敏感词详细信息（包含统计）
     * @param word 敏感词
     * @return 详细信息
     */
    Map<String, Object> getWordDetail(@Param("word") String word);
}
