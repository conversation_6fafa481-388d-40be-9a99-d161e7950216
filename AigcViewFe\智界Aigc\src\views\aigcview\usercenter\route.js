// 智界Aigc 个人中心路由配置示例
// 将此配置添加到主路由文件中

export const userCenterRoutes = [
  {
    path: '/aigcview/usercenter',
    name: 'UserCenter',
    component: () => import('@/views/aigcview/usercenter/UserCenter'),
    meta: {
      title: '个人中心',
      icon: 'user',
      requireAuth: true, // 需要登录
      keepAlive: true,
      // 面包屑导航
      breadcrumb: [
        {
          breadcrumbName: '智界Aigc',
          path: '/aigcview'
        },
        {
          breadcrumbName: '个人中心',
          path: '/aigcview/usercenter'
        }
      ]
    }
  },
  {
    path: '/aigcview/usercenter/profile',
    name: 'UserProfile',
    component: () => import('@/views/aigcview/usercenter/UserCenter'),
    meta: {
      title: '个人资料',
      icon: 'profile',
      requireAuth: true,
      keepAlive: true,
      hidden: true // 隐藏在菜单中，通过个人中心页面内部导航访问
    }
  },
  {
    path: '/aigcview/usercenter/transactions',
    name: 'UserTransactions',
    component: () => import('@/views/aigcview/usercenter/UserCenter'),
    meta: {
      title: '交易记录',
      icon: 'transaction',
      requireAuth: true,
      keepAlive: true,
      hidden: true
    }
  },
  {
    path: '/aigcview/usercenter/exchange',
    name: 'ExchangeCode',
    component: () => import('@/views/aigcview/usercenter/UserCenter'),
    meta: {
      title: '兑换码',
      icon: 'gift',
      requireAuth: true,
      keepAlive: true,
      hidden: true
    }
  }
]

// 菜单配置示例
export const userCenterMenus = [
  {
    path: '/aigcview',
    name: 'AigcView',
    component: 'layouts/RouteView',
    meta: {
      title: '智界Aigc',
      icon: 'robot',
      permission: ['user']
    },
    children: [
      {
        path: '/aigcview/usercenter',
        name: 'UserCenter',
        component: 'aigcview/usercenter/UserCenter',
        meta: {
          title: '个人中心',
          icon: 'user',
          permission: ['user']
        }
      },
      // 其他智界Aigc相关页面...
      {
        path: '/aigcview/plubauthor',
        name: 'PlubAuthor',
        component: 'aigcview/plubauthor/AigcPlubAuthorList',
        meta: {
          title: '作者管理',
          icon: 'team',
          permission: ['admin']
        }
      },
      {
        path: '/aigcview/plubshop',
        name: 'PlubShop',
        component: 'aigcview/plubshop/AigcPlubShopList',
        meta: {
          title: '商店管理',
          icon: 'shop',
          permission: ['admin']
        }
      },
      {
        path: '/aigcview/videoteacher',
        name: 'VideoTeacher',
        component: 'aigcview/videoteacher/AigcVideoTeacherList',
        meta: {
          title: '视频教师',
          icon: 'video-camera',
          permission: ['admin']
        }
      },
      {
        path: '/aigcview/videotutorial',
        name: 'VideoTutorial',
        component: 'aigcview/videotutorial/AigcVideoTutorialList',
        meta: {
          title: '视频教程',
          icon: 'play-circle',
          permission: ['admin']
        }
      }
    ]
  }
]

// 权限配置示例
export const userCenterPermissions = [
  {
    id: 'aigcview:usercenter:view',
    name: '个人中心查看',
    description: '允许用户查看个人中心页面'
  },
  {
    id: 'aigcview:usercenter:edit',
    name: '个人信息编辑',
    description: '允许用户编辑个人信息'
  },
  {
    id: 'aigcview:usercenter:recharge',
    name: '账户充值',
    description: '允许用户进行账户充值'
  },
  {
    id: 'aigcview:usercenter:exchange',
    name: '兑换码使用',
    description: '允许用户使用兑换码'
  },
  {
    id: 'aigcview:usercenter:apikey',
    name: 'API密钥管理',
    description: '允许用户管理API密钥'
  }
]

// 导航守卫示例
export const userCenterGuards = {
  // 路由前置守卫
  beforeEnter: (to, from, next) => {
    // 检查用户是否登录
    const token = localStorage.getItem('Access-Token')
    if (!token) {
      next('/user/login')
      return
    }
    
    // 检查用户权限
    const userInfo = JSON.parse(localStorage.getItem('USER_INFO') || '{}')
    const hasPermission = userInfo.permissions?.includes('aigcview:usercenter:view')
    
    if (!hasPermission) {
      next('/exception/403')
      return
    }
    
    next()
  }
}

// 使用示例：
// 在 router/index.js 中添加：
/*
import { userCenterRoutes } from '@/views/aigcview/usercenter/route'

const routes = [
  // 其他路由...
  ...userCenterRoutes,
  // 或者作为子路由添加到现有路由中
  {
    path: '/aigcview',
    component: () => import('@/layouts/BasicLayout'),
    children: userCenterRoutes
  }
]
*/

// 在菜单配置文件中添加：
/*
import { userCenterMenus } from '@/views/aigcview/usercenter/route'

export const asyncRouterMap = [
  // 其他菜单...
  ...userCenterMenus
]
*/
