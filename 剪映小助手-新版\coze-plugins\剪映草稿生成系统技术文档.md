# 剪映草稿生成系统技术文档

## 📋 概述

本文档详细记录了智界AigcView剪映小助手中`easy_create_material`接口的完整实现逻辑，以及解决"媒体丢失"问题的关键技术方案。

---

## 🏗️ 系统架构

### 整体架构图
```
用户请求 → Java后端API → 生成草稿JSON → TOS云存储 → Electron客户端 → 本地草稿文件 → 剪映软件
```

### 技术栈
- **后端**: Java Spring Boot + FastJSON
- **前端**: Electron + Node.js
- **存储**: 火山引擎TOS
- **目标软件**: 剪映专业版

---

## 🔄 接口工作流程

### 第1阶段：Java后端处理

#### 1.1 接口入口
```java
@PostMapping("/easy_create_material")
public Result<JSONObject> easyCreateMaterial(@RequestBody EasyCreateMaterialRequest request)
```

#### 1.2 核心处理逻辑
```java
// CozeApiService.java
public JSONObject easyCreateMaterial(EasyCreateMaterialRequest request) {
    // 1. 参数验证
    validateRequest(request);
    
    // 2. 生成草稿JSON
    JSONObject draftJson = generateDraftJson(request);
    
    // 3. 上传到TOS存储
    String fileUrl = tosService.uploadDraftFile(draftJson.toJSONString());
    
    // 4. 返回结果
    return buildResponse(fileUrl);
}
```

#### 1.3 关键数据结构生成
```java
// 音频素材路径 - 使用固定占位符ID
audioMaterial.put("path", 
    "##_draftpath_placeholder_0E685133-18CE-45ED-8CB8-2904A212EC80_##\\" 
    + audioFolderId + "\\" + audioFileName);
```

### 第2阶段：Electron客户端处理

#### 2.1 JSON下载与解析
```javascript
// 下载草稿JSON文件
const response = await fetch(jsonUrl);
const draftJson = await response.json();

// 解析音频素材信息
const audioMaterials = draftJson.materials?.audios || [];
```

#### 2.2 本地文件结构生成
```javascript
// 创建草稿文件夹
const projectDir = path.join(baseDir, projectId);
fs.mkdirSync(projectDir, { recursive: true });

// 创建音频素材文件夹
const materialDir = path.join(projectDir, audioFolderId);
fs.mkdirSync(materialDir, { recursive: true });

// 下载音频文件
await downloadAudioFile(audioUrl, path.join(materialDir, audioFileName));
```

#### 2.3 配置文件生成
```javascript
// 生成所有必需的JSON配置文件
await fs.promises.writeFile(path.join(projectDir, 'draft_content.json'), draftContent, 'utf8');
await fs.promises.writeFile(path.join(projectDir, 'draft_info.json'), draftContent, 'utf8');
await fs.promises.writeFile(path.join(projectDir, 'draft_meta_info.json'), metaInfo, 'utf8');
await fs.promises.writeFile(path.join(projectDir, `${audioFolderId}.json`), draftContent, 'utf8');
```

---

## 🚨 核心问题分析

### 问题现象
- 剪映显示"媒体丢失"
- 草稿可以导入但无法播放音频
- 文件结构和JSON格式都正确

### 根本原因
**剪映的占位符ID验证机制**

剪映内部有一个隐藏的路径解析系统，只接受特定的占位符ID。路径格式：
```
##_draftpath_placeholder_[PLACEHOLDER_ID]_##\folder\file.ext
```

### 问题根源对比

#### ❌ 错误实现（动态ID）
```java
// Java后端
String draftUuid = UUID.randomUUID().toString().toUpperCase();
audioMaterial.put("path", "##_draftpath_placeholder_" + draftUuid + "_##\\...");

// Electron前端  
"path": `##_draftpath_placeholder_${projectId.toUpperCase()}_##\\...`
```

#### ✅ 正确实现（固定ID）
```java
// Java后端
audioMaterial.put("path", 
    "##_draftpath_placeholder_0E685133-18CE-45ED-8CB8-2904A212EC80_##\\...");

// Electron前端
"path": `##_draftpath_placeholder_0E685133-18CE-45ED-8CB8-2904A212EC80_##\\...`
```

---

## 🔧 解决方案详解

### 关键发现
通过对比竞争对手的成功案例，发现：
1. **占位符ID是固定的**：`0E685133-18CE-45ED-8CB8-2904A212EC80`
2. **其他ID可以动态生成**：草稿ID、音频素材ID、draft_id等
3. **前后端必须使用相同的占位符ID**

### 技术实现

#### Java后端修改
```java
// CozeApiService.java 第386行
// 修改前：
audioMaterial.put("path", "##_draftpath_placeholder_" + draftUuid + "_##\\" + audioFolderId + "\\" + audioFileName);

// 修改后：
audioMaterial.put("path", "##_draftpath_placeholder_0E685133-18CE-45ED-8CB8-2904A212EC80_##\\" + audioFolderId + "\\" + audioFileName);
```

#### Electron前端修改
```javascript
// main.js updateFinalDraftFiles函数
// 修改前：
"path": `##_draftpath_placeholder_${projectId.toUpperCase()}_##\\${audioFolderName}\\${audioFileName}`

// 修改后：
"path": `##_draftpath_placeholder_0E685133-18CE-45ED-8CB8-2904A212EC80_##\\${audioFolderName}\\${audioFileName}`
```

### 优化策略
1. **固定占位符ID**：确保剪映识别
2. **动态其他ID**：保证草稿唯一性
3. **前后端同步**：避免不一致问题

---

## 📊 数据流转详解

### 完整数据流
```
1. 用户请求 → API接收参数
   ↓
2. 生成草稿JSON → 包含固定占位符ID的路径
   ↓  
3. 上传到TOS → 返回下载URL
   ↓
4. Electron下载JSON → 解析音频信息
   ↓
5. 下载音频文件 → 创建本地文件夹
   ↓
6. 生成配置文件 → 使用相同的固定占位符ID
   ↓
7. 剪映识别草稿 → 成功播放 ✅
```

### 关键数据结构

#### 草稿JSON结构
```json
{
  "id": "动态生成的草稿ID",
  "materials": {
    "audios": [{
      "id": "动态生成的音频素材ID",
      "path": "##_draftpath_placeholder_0E685133-18CE-45ED-8CB8-2904A212EC80_##\\音频文件夹\\音频文件.mp3"
    }]
  },
  "tracks": [{
    "segments": [{
      "material_id": "引用上面的音频素材ID"
    }]
  }]
}
```

#### 本地文件结构
```
草稿文件夹/
├── draft_content.json          # 主要内容
├── draft_info.json            # 草稿信息  
├── draft_meta_info.json       # 元数据
├── attachment_pc_common.json  # 附件配置
├── draft_agency_config.json   # 代理配置
├── template.tmp               # 模板文件
├── [音频文件夹UUID].json      # 素材JSON
└── [音频文件夹UUID]/          # 音频文件夹
    └── [音频文件UUID].mp3     # 实际音频文件
```

---

## ⚠️ 重要注意事项

### 1. 占位符ID的重要性
- **绝对不能随意修改**占位符ID `0E685133-18CE-45ED-8CB8-2904A212EC80`
- 这是剪映识别草稿的**关键标识符**
- 前后端必须使用**完全相同**的占位符ID

### 2. ID生成策略
- **占位符ID**：固定使用 `0E685133-18CE-45ED-8CB8-2904A212EC80`
- **草稿ID**：动态生成UUID，确保唯一性
- **音频素材ID**：动态生成UUID，用于轨道引用
- **draft_id**：动态生成UUID，用于元数据

### 3. 路径格式要求
- 必须使用**反斜杠** `\\` 作为路径分隔符
- 路径格式：`##_draftpath_placeholder_[固定ID]_##\\文件夹\\文件名`
- 文件夹名和文件名必须是**有效的UUID格式**

---

## 🧪 测试验证

### 测试流程
1. **后端测试**：调用API生成草稿JSON，检查占位符ID
2. **前端测试**：下载JSON并生成本地文件，验证路径一致性  
3. **集成测试**：在剪映中导入草稿，确认可以正常播放
4. **回归测试**：多次生成不同的草稿，确保稳定性

### 验证要点
- ✅ 占位符ID必须是 `0E685133-18CE-45ED-8CB8-2904A212EC80`
- ✅ 音频文件路径格式正确
- ✅ 轨道段正确引用音频素材ID
- ✅ 所有JSON文件格式符合剪映要求
- ✅ 文件夹结构完整

---

## 🔮 未来优化方向

### 1. 多占位符ID支持
- 研究其他有效的占位符ID
- 建立占位符ID池，提高兼容性

### 2. 自动化测试
- 开发自动化测试工具
- 集成到CI/CD流程中

### 3. 错误诊断
- 增强错误诊断能力
- 提供详细的错误信息和修复建议

### 4. 性能优化
- 优化文件生成速度
- 减少网络传输开销

---

## 📚 技术总结

这次问题的解决过程揭示了几个重要的技术原则：

1. **逆向工程的复杂性**：第三方软件往往有隐藏的验证机制
2. **细节决定成败**：一个看似无关紧要的ID可能是关键
3. **对比分析的价值**：通过分析成功案例找到关键差异
4. **系统性思维**：需要从整个数据流的角度分析问题
5. **前后端协同**：确保所有组件使用一致的配置

**最终成功的关键**：使用固定的占位符ID `0E685133-18CE-45ED-8CB8-2904A212EC80`，这是剪映识别和解析草稿路径的核心标识符。

---

*文档版本：v1.0*  
*最后更新：2025-01-05*  
*作者：智界AigcView技术团队*
