import TOSBase from '../base';
export interface PutBucketRenameInput {
    bucket?: string;
    renameEnable: boolean;
}
export interface PutBucketRenameOutput {
}
export declare function putBucketRename(this: TOSBase, input: PutBucketRenameInput): Promise<import("../base").TosResponse<PutBucketRenameOutput>>;
export interface GetBucketRenameInput {
    bucket?: string;
}
export interface GetBucketRenameOutput {
    RenameEnable: boolean;
}
export declare function getBucketRename(this: TOSBase, input: GetBucketRenameInput): Promise<import("../base").TosResponse<GetBucketRenameOutput>>;
export interface DeleteBucketRenameInput {
    bucket?: string;
}
export interface DeleteBucketRenameOutput {
}
export declare function deleteBucketRename(this: TOSBase, input: DeleteBucketRenameInput): Promise<import("../base").TosResponse<DeleteBucketRenameOutput>>;
