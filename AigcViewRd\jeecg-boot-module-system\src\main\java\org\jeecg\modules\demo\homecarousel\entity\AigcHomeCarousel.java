package org.jeecg.modules.demo.homecarousel.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 首页轮播图
 * @Author: jeecg-boot
 * @Date:   2025-06-17
 * @Version: V1.0
 */
@Data
@TableName("aigc_home_carousel")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="aigc_home_carousel对象", description="首页轮播图")
public class AigcHomeCarousel implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private java.lang.String id;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private java.util.Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private java.util.Date updateTime;
	/**所属部门*/
    @ApiModelProperty(value = "所属部门")
    private java.lang.String sysOrgCode;
	/**轮播图标题*/
	@Excel(name = "轮播图标题", width = 15)
    @ApiModelProperty(value = "轮播图标题")
    private java.lang.String title;
	/**轮播图描述*/
	@Excel(name = "轮播图描述", width = 15)
    @ApiModelProperty(value = "轮播图描述")
    private java.lang.String description;
	/**轮播图*/
	@Excel(name = "轮播图", width = 15)
    @ApiModelProperty(value = "轮播图")
    private java.lang.String imageUrl;
	/**标签文字*/
	@Excel(name = "标签文字", width = 15)
    @ApiModelProperty(value = "标签文字")
    private java.lang.String badge;
	/**按钮文字*/
	@Excel(name = "按钮文字", width = 15)
    @ApiModelProperty(value = "按钮文字")
    private java.lang.String buttonText;
	/**按钮跳转链接*/
	@Excel(name = "按钮跳转链接", width = 15)
    @ApiModelProperty(value = "按钮跳转链接")
    private java.lang.String buttonLink;
	/**排序序号*/
	@Excel(name = "排序序号", width = 15)
    @ApiModelProperty(value = "排序序号")
    private java.lang.Integer sortOrder;
	/**是否启用*/
	@Excel(name = "是否启用", width = 15)
    @ApiModelProperty(value = "是否启用")
    private java.lang.String status;
}
