{"openapi": "3.0.0", "info": {"title": "剪映小助手_智界工具箱 - 查询视频状态", "description": "查询视频状态", "version": "1.0.0"}, "servers": [{"url": "https://www.aigcview.com"}], "paths": {"/jeecg-boot/api/jianying/gen_video_status": {"post": {"summary": "查询视频状态", "description": "查询视频状态", "operationId": "gen_video_status", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"access_key": {"type": "string", "description": "访问密钥（必填）", "example": "JianyingAPI_2025_SecureAccess_AigcView", "default": "JianyingAPI_2025_SecureAccess_AigcView"}, "draft_url": {"type": "string", "description": "草稿链接（必填）", "example": "https://example.com/draft/123"}}, "required": ["access_key", "draft_url"], "additionalProperties": false}}}}, "responses": {"200": {"description": "成功查询视频状态", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "description": "操作是否成功"}, "data": {"type": "object", "properties": {"status": {"type": "string", "description": "任务状态"}, "video_url": {"type": "string", "description": "视频地址"}, "progress": {"type": "integer", "description": "进度百分比"}}}, "message": {"type": "string", "description": "响应消息"}}, "required": ["success", "data", "message"]}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "description": "操作是否成功", "example": false}, "error": {"type": "string", "description": "错误信息"}, "code": {"type": "string", "description": "错误代码"}}, "required": ["success", "error"]}}}}, "500": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "description": "操作是否成功", "example": false}, "error": {"type": "string", "description": "错误信息"}, "code": {"type": "string", "description": "错误代码"}}, "required": ["success", "error"]}}}}}}}}}