package org.jeecg.modules.demo.userprofile.mapper;

import java.util.List;
import java.math.BigDecimal;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.jeecg.modules.demo.userprofile.entity.AicgUserProfile;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: 用户扩展信息表
 * @Author: jeecg-boot
 * @Date:   2025-06-14
 * @Version: V1.0
 */
public interface AicgUserProfileMapper extends BaseMapper<AicgUserProfile> {

    /**
     * 根据用户ID查询用户扩展信息
     * @param userId 用户ID
     * @return 用户扩展信息
     */
    @Select("SELECT * FROM aicg_user_profile WHERE user_id = #{userId}")
    AicgUserProfile getByUserId(@Param("userId") String userId);

    /**
     * 根据用户名查询用户扩展信息（联合查询sys_user表）
     * @param username 用户名
     * @return 用户扩展信息
     */
    @Select("SELECT p.* FROM aicg_user_profile p " +
            "INNER JOIN sys_user u ON p.user_id = u.id " +
            "WHERE u.username = #{username}")
    AicgUserProfile getByUsername(@Param("username") String username);

    /**
     * 获取用户完整信息（联合查询sys_user表和角色表）
     * @param username 用户名
     * @return 用户完整信息
     */
    @Select("SELECT u.id as user_id, u.username, u.realname, u.email, u.phone, p.avatar, u.create_time as user_create_time, " +
            "p.nickname, p.account_balance, p.total_consumption, p.total_recharge, p.member_expire_time, p.api_key, p.password_changed, " +
            "r.role_name as current_role " +
            "FROM sys_user u " +
            "LEFT JOIN aicg_user_profile p ON u.id = p.user_id " +
            "LEFT JOIN sys_user_role ur ON u.id = ur.user_id " +
            "LEFT JOIN sys_role r ON ur.role_id = r.id " +
            "WHERE u.username = #{username}")
    java.util.Map<String, Object> getUserFullInfo(@Param("username") String username);
    
    /**
     * 更新用户余额
     * @param userId 用户ID
     * @param newBalance 新余额
     * @param updateBy 更新人
     * @return 更新结果
     */
    @Update("UPDATE aicg_user_profile SET account_balance = #{newBalance}, update_by = #{updateBy}, update_time = NOW() WHERE user_id = #{userId}")
    int updateBalance(@Param("userId") String userId, @Param("newBalance") BigDecimal newBalance, @Param("updateBy") String updateBy);
    
    /**
     * 增加用户余额
     * @param userId 用户ID
     * @param amount 增加金额
     * @param updateBy 更新人
     * @return 更新结果
     */
    @Update("UPDATE aicg_user_profile SET account_balance = account_balance + #{amount}, total_recharge = total_recharge + #{amount}, update_by = #{updateBy}, update_time = NOW() WHERE user_id = #{userId}")
    int addBalance(@Param("userId") String userId, @Param("amount") BigDecimal amount, @Param("updateBy") String updateBy);
    
    /**
     * 扣减用户余额
     * @param userId 用户ID
     * @param amount 扣减金额
     * @param updateBy 更新人
     * @return 更新结果
     */
    @Update("UPDATE aicg_user_profile SET account_balance = account_balance - #{amount}, total_consumption = total_consumption + #{amount}, update_by = #{updateBy}, update_time = NOW() WHERE user_id = #{userId} AND account_balance >= #{amount}")
    int deductBalance(@Param("userId") String userId, @Param("amount") BigDecimal amount, @Param("updateBy") String updateBy);
    
    /**
     * 重新生成API密钥
     * @param userId 用户ID
     * @param apiKey 新API密钥
     * @param updateBy 更新人
     * @return 更新结果
     */
    @Update("UPDATE aicg_user_profile SET api_key = #{apiKey}, update_by = #{updateBy}, update_time = NOW() WHERE user_id = #{userId}")
    int updateApiKey(@Param("userId") String userId, @Param("apiKey") String apiKey, @Param("updateBy") String updateBy);

    /**
     * 根据邀请码查询用户扩展信息
     * @param inviteCode 邀请码
     * @return 用户扩展信息
     */
    @Select("SELECT * FROM aicg_user_profile WHERE my_invite_code = #{inviteCode}")
    AicgUserProfile getByInviteCode(@Param("inviteCode") String inviteCode);

    /**
     * 根据昵称查询用户扩展信息（用于昵称重复性检查）
     * @param nickname 昵称
     * @return 用户扩展信息列表
     */
    @Select("SELECT * FROM aicg_user_profile WHERE nickname = #{nickname}")
    List<AicgUserProfile> selectByNickname(@Param("nickname") String nickname);
}
