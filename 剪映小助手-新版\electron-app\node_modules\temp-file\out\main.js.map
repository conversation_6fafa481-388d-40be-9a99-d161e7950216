{"version": 3, "file": "main.js", "sourceRoot": "", "sources": ["../src/main.ts"], "names": [], "mappings": ";;;AAAA,uCAA6F;AAC7F,2BAAyB;AACzB,6BAA4B;AAE5B,IAAI,cAAc,GAAG,CAAC,CAAA;AACtB,MAAM,cAAc,GAAG,IAAI,GAAG,EAAU,CAAA;AAExC,uCAAuC;AACvC,MAAM,aAAa,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,IAAI,IAAI,CAAC,GAAG,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAA;AAE9E,SAAgB,WAAW,CAAC,MAAkC;IAC5D,OAAO,GAAG,MAAM,IAAI,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,MAAM,GAAG,GAAG,aAAa,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAA;AACnG,CAAC;AAFD,kCAEC;AAED,IAAI,cAAsC,CAAA;AAC1C,IAAI,WAA0B,CAAA;AAE9B,SAAS,cAAc;IACrB,IAAI,cAAc,IAAI,IAAI,EAAE;QAC1B,OAAO,cAAc,CAAA;KACtB;IAED,IAAI,WAAW,IAAI,IAAI,EAAE;QACvB,OAAO,OAAO,CAAC,OAAO,CAAC,WAAW,CAAC,CAAA;KACpC;IAED,MAAM,YAAY,GAAG,OAAO,CAAC,GAAG,CAAC,mBAAmB,IAAI,WAAM,EAAE,CAAA;IAChE,MAAM,qBAAqB,GAAG,OAAO,CAAC,GAAG,CAAC,sCAAsC,KAAK,OAAO,CAAA;IAC5F,cAAc,GAAG,kBAAO,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;SACpD,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,mBAAQ,CAAC,EAAE,CAAC,CAAC;SACxB,IAAI,CAAC,GAAG,CAAC,EAAE;QACV,IAAI,qBAAqB,EAAE;YACzB,WAAW,CAAC,GAAG,CAAC,CAAA;SACjB;QACD,WAAW,GAAG,GAAG,CAAA;QACjB,OAAO,GAAG,CAAA;IACZ,CAAC,CAAC,CAAA;IACJ,OAAO,cAAc,CAAA;AACvB,CAAC;AAED,SAAS,WAAW,CAAC,GAAW;IAC9B,OAAO,CAAC,iBAAiB,CAAC,CAAC,CAAC,QAA6B,EAAE,EAAE;QAC3D,MAAM,QAAQ,GAAG,KAAK,CAAC,IAAI,CAAC,cAAc,CAAC,CAAA;QAC3C,cAAc,CAAC,KAAK,EAAE,CAAA;QAEtB,IAAI,QAAQ,IAAI,IAAI,EAAE;YACpB,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE;gBAC9B,OAAO,CAAC,WAAW,EAAE,CAAA;aACtB;YAED,IAAI;gBACF,qBAAU,CAAC,GAAG,CAAC,CAAA;aAChB;YACD,OAAO,CAAC,EAAE;gBACR,WAAW,CAAC,CAAC,EAAE,GAAG,CAAC,CAAA;aACpB;YACD,OAAM;SACP;QAED,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,CAAC;aAC1C,IAAI,CAAC,GAAG,EAAE,CAAC,iBAAM,CAAC,GAAG,CAAC,CAAC;aACvB,IAAI,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE,CAAC;aACtB,KAAK,CAAC,CAAC,CAAC,EAAE;YACT,IAAI;gBACF,WAAW,CAAC,CAAC,EAAE,GAAG,CAAC,CAAA;aACpB;oBACO;gBACN,QAAQ,EAAE,CAAA;aACX;QACH,CAAC,CAAC,CAAA;IACN,CAAC,CAAC,CAAA;AACJ,CAAC;AAED,SAAS,WAAW,CAAC,CAAM,EAAE,IAAY;IACvC,IAAI,CAAC,CAAC,IAAI,KAAK,OAAO,IAAI,CAAC,CAAC,IAAI,KAAK,QAAQ,EAAE;QAC7C,kGAAkG;QAClG,OAAO,CAAC,IAAI,CAAC,4BAA4B,IAAI,MAAM,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,CAAC,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAA;KAChF;AACH,CAAC;AAeD,MAAa,MAAM;IAIjB,YAA6B,YAAoB,EAAE;QAAtB,cAAS,GAAT,SAAS,CAAa;QAH3C,cAAS,GAAwB,EAAE,CAAA;QACnC,eAAU,GAAG,KAAK,CAAA;IAG1B,CAAC;IAED,yDAAyD;IACzD,IAAI,WAAW;QACb,OAAO,cAAc,EAAE,CAAA;IACzB,CAAC;IAED,UAAU,CAAC,OAA4B;QACrC,OAAO,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,IAAI,CAAC,CAAA;IACxC,CAAC;IAED,aAAa,CAAC,OAA4B;QACxC,OAAO,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,IAAI,CAAC;aACnC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,oBAAS,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC,CAAA;IAC7C,CAAC;IAED,WAAW,CAAC,OAA4B,EAAE,KAAK,GAAG,KAAK;QACrD,OAAO,cAAc,EAAE;aACpB,IAAI,CAAC,WAAW,CAAC,EAAE;YAClB,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;gBACpB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAA;gBACtB,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,CAAA;aACzB;YAED,MAAM,MAAM,GAAG,OAAO,CAAC,OAAO,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAA;YAC/D,MAAM,MAAM,GAAG,OAAO,CAAC,OAAO,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAA;YAC/D,MAAM,UAAU,GAAG,MAAM,IAAI,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,MAAM,GAAG,CAAA;YACrD,MAAM,UAAU,GAAG,MAAM,IAAI,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,MAAM,EAAE,CAAC,CAAA;YACzF,MAAM,MAAM,GAAG,GAAG,WAAW,GAAG,IAAI,CAAC,GAAG,GAAG,UAAU,GAAG,CAAC,cAAc,EAAE,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,GAAG,UAAU,EAAE,CAAA;YACtG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;gBAClB,IAAI,EAAE,MAAM;gBACZ,KAAK;gBACL,QAAQ,EAAE,OAAO,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,QAAQ;aACpD,CAAC,CAAA;YACF,OAAO,MAAM,CAAA;QACf,CAAC,CAAC,CAAA;IACN,CAAC;IAED,WAAW;QACT,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAA;QAChC,cAAc,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;QAC3B,IAAI,CAAC,UAAU,GAAG,KAAK,CAAA;QACvB,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;YAC1B,OAAM;SACP;QAED,IAAI,CAAC,SAAS,GAAG,EAAE,CAAA;QAEnB,KAAK,MAAM,IAAI,IAAI,SAAS,EAAE;YAC5B,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,EAAE;gBACzB,wCAAwC;gBACxC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;gBACxB,SAAQ;aACT;YAED,IAAI;gBACF,IAAI,IAAI,CAAC,KAAK,EAAE;oBACd,qBAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;iBACtB;qBACI;oBACH,qBAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;iBACtB;aACF;YACD,OAAO,CAAC,EAAE;gBACR,WAAW,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,CAAA;aAC1B;SACF;IACH,CAAC;IAED,OAAO;QACL,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAA;QAChC,cAAc,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;QAE3B,IAAI,CAAC,UAAU,GAAG,KAAK,CAAA;QACvB,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;YAC1B,OAAO,OAAO,CAAC,OAAO,EAAE,CAAA;SACzB;QACD,IAAI,CAAC,SAAS,GAAG,EAAE,CAAA;QAEnB,IAAI,cAAc,CAAC,IAAI,KAAK,CAAC,EAAE;YAC7B,MAAM,GAAG,GAAG,WAAW,CAAA;YACvB,IAAI,GAAG,IAAI,IAAI,EAAE;gBACf,OAAO,OAAO,CAAC,OAAO,EAAE,CAAA;aACzB;YAED,WAAW,GAAG,IAAI,CAAA;YAClB,cAAc,GAAG,IAAI,CAAA;YACrB,OAAO,iBAAM,CAAC,GAAG,CAAC,CAAA;SACnB;QAED,OAAO,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE;YACpC,IAAI,EAAE,CAAC,QAAQ,IAAI,IAAI,EAAE;gBACvB,OAAO,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC,CAAA;aAC5B;YAED,OAAO,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,iBAAM,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,iBAAM,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC;iBAClD,KAAK,CAAC,CAAC,CAAC,EAAE;gBACT,WAAW,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,CAAA;YACzB,CAAC,CAAC,CAAA;QACN,CAAC,CAAC,CAAC,CAAA;IACL,CAAC;IAED,QAAQ;QACN,OAAO,IAAI,CAAC,SAAS,CAAA;IACvB,CAAC;CACF;AA9GD,wBA8GC;AAED,SAAS,OAAO,CAAC,CAAiB;IAChC,OAAO,CAAC,IAAI,IAAI,IAAI,CAAC,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAA;AAC/C,CAAC", "sourcesContent": ["import {ensureDir, mkdtemp, realpath, remove, removeSync, unlink, unlinkSync} from \"fs-extra\"\nimport {tmpdir} from \"os\"\nimport * as path from \"path\"\n\nlet tmpFileCounter = 0\nconst tmpDirManagers = new Set<TmpDir>()\n\n// add date to avoid use stale temp dir\nconst tempDirPrefix = `${process.pid.toString(36)}-${Date.now().toString(36)}`\n\nexport function getTempName(prefix?: string | null | undefined): string {\n  return `${prefix == null ? \"\" : `${prefix}-`}${tempDirPrefix}-${(tmpFileCounter++).toString(36)}`\n}\n\nlet tempDirPromise: Promise<string> | null\nlet tempBaseDir: string | null\n\nfunction getBaseTempDir(): Promise<string> {\n  if (tempDirPromise != null) {\n    return tempDirPromise\n  }\n\n  if (tempBaseDir != null) {\n    return Promise.resolve(tempBaseDir)\n  }\n\n  const systemTmpDir = process.env.APP_BUILDER_TMP_DIR || tmpdir()\n  const isEnsureRemovedOnExit = process.env.TMP_DIR_MANAGER_ENSURE_REMOVED_ON_EXIT !== \"false\"\n  tempDirPromise = mkdtemp(path.join(systemTmpDir, \"t-\"))\n    .then(it => realpath(it))\n    .then(dir => {\n      if (isEnsureRemovedOnExit) {\n        addExitHook(dir)\n      }\n      tempBaseDir = dir\n      return dir\n    })\n  return tempDirPromise\n}\n\nfunction addExitHook(dir: string) {\n  require(\"async-exit-hook\")((callback: (() => void) | null) => {\n    const managers = Array.from(tmpDirManagers)\n    tmpDirManagers.clear()\n\n    if (callback == null) {\n      for (const manager of managers) {\n        manager.cleanupSync()\n      }\n\n      try {\n        removeSync(dir)\n      }\n      catch (e) {\n        handleError(e, dir)\n      }\n      return\n    }\n\n    Promise.all(managers.map(it => it.cleanup()))\n      .then(() => remove(dir))\n      .then(() => callback())\n      .catch(e => {\n        try {\n          handleError(e, dir)\n        }\n        finally {\n          callback()\n        }\n      })\n  })\n}\n\nfunction handleError(e: any, file: string) {\n  if (e.code !== \"EPERM\" && e.code !== \"ENOENT\") {\n    // use only console.* instead of our warn on exit (otherwise nodeEmoji can be required on request)\n    console.warn(`Cannot delete temporary \"${file}\": ${(e.stack || e).toString()}`)\n  }\n}\n\ninterface TempFileInfo {\n  isDir: boolean\n  path: string\n  disposer?: ((file: string) => Promise<void>) | null\n}\n\nexport interface GetTempFileOptions {\n  prefix?: string | null\n  suffix?: string | null\n\n  disposer?: ((file: string) => Promise<void>) | null\n}\n\nexport class TmpDir {\n  private tempFiles: Array<TempFileInfo> = []\n  private registered = false\n\n  constructor(private readonly debugName: string = \"\") {\n  }\n\n  // noinspection JSMethodCanBeStatic,JSUnusedGlobalSymbols\n  get rootTempDir(): Promise<string> {\n    return getBaseTempDir()\n  }\n\n  getTempDir(options?: GetTempFileOptions): Promise<string> {\n    return this.getTempFile(options, true)\n  }\n\n  createTempDir(options?: GetTempFileOptions): Promise<string> {\n    return this.getTempFile(options, true)\n      .then(it => ensureDir(it).then(() => it))\n  }\n\n  getTempFile(options?: GetTempFileOptions, isDir = false): Promise<string> {\n    return getBaseTempDir()\n      .then(baseTempDir => {\n        if (!this.registered) {\n          this.registered = true\n          tmpDirManagers.add(this)\n        }\n\n        const prefix = nullize(options == null ? null : options.prefix)\n        const suffix = nullize(options == null ? null : options.suffix)\n        const namePrefix = prefix == null ? \"\" : `${prefix}-`\n        const nameSuffix = suffix == null ? \"\" : (suffix.startsWith(\".\") ? suffix : `-${suffix}`)\n        const result = `${baseTempDir}${path.sep}${namePrefix}${(tmpFileCounter++).toString(36)}${nameSuffix}`\n        this.tempFiles.push({\n          path: result,\n          isDir,\n          disposer: options == null ? null : options.disposer,\n        })\n        return result\n      })\n  }\n\n  cleanupSync() {\n    const tempFiles = this.tempFiles\n    tmpDirManagers.delete(this)\n    this.registered = false\n    if (tempFiles.length === 0) {\n      return\n    }\n\n    this.tempFiles = []\n\n    for (const file of tempFiles) {\n      if (file.disposer != null) {\n        // noinspection JSIgnoredPromiseFromCall\n        file.disposer(file.path)\n        continue\n      }\n\n      try {\n        if (file.isDir) {\n          removeSync(file.path)\n        }\n        else {\n          unlinkSync(file.path)\n        }\n      }\n      catch (e) {\n        handleError(e, file.path)\n      }\n    }\n  }\n\n  cleanup(): Promise<any> {\n    const tempFiles = this.tempFiles\n    tmpDirManagers.delete(this)\n\n    this.registered = false\n    if (tempFiles.length === 0) {\n      return Promise.resolve()\n    }\n    this.tempFiles = []\n\n    if (tmpDirManagers.size === 0) {\n      const dir = tempBaseDir\n      if (dir == null) {\n        return Promise.resolve()\n      }\n\n      tempBaseDir = null\n      tempDirPromise = null\n      return remove(dir)\n    }\n\n    return Promise.all(tempFiles.map(it => {\n      if (it.disposer != null) {\n        return it.disposer(it.path)\n      }\n\n      return (it.isDir ? remove(it.path) : unlink(it.path))\n        .catch(e => {\n          handleError(e, it.path)\n        })\n    }))\n  }\n\n  toString() {\n    return this.debugName\n  }\n}\n\nfunction nullize(s?: string | null) {\n  return s == null || s.length === 0 ? null : s\n}"]}