package org.jeecg.modules.demo.videoteacher.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 视频讲师
 * @Author: jeecg-boot
 * @Date:   2025-06-14
 * @Version: V1.0
 */
@Data
@TableName("aigc_video_teacher")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="aigc_video_teacher对象", description="视频讲师")
public class AigcVideoTeacher implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private java.lang.String id;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private java.util.Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private java.util.Date updateTime;
	/**所属部门*/
    @ApiModelProperty(value = "所属部门")
    private java.lang.String sysOrgCode;
	/**讲师名*/
	@Excel(name = "讲师名", width = 15)
    @ApiModelProperty(value = "讲师名")
    private java.lang.String teachername;
	/**讲师介绍*/
	@Excel(name = "讲师介绍", width = 15)
    @ApiModelProperty(value = "讲师介绍")
    private java.lang.String teacherinfo;
	/**学习人数*/
	@Excel(name = "学习人数", width = 15)
    @ApiModelProperty(value = "学习人数")
    private java.lang.Integer studyperson;
	/**课程数量*/
	@Excel(name = "课程数量", width = 15)
    @ApiModelProperty(value = "课程数量")
    private java.lang.Integer coursenum;
}
