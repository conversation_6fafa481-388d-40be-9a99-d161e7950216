<template>
  <div class="agent-market">
    <!-- 固定的搜索和筛选区域 -->
    <div class="sticky-filters">
      <div class="market-filters">
        <div class="filter-row">
          <!-- 搜索框 -->
          <div class="search-box">
            <div class="search-wrapper">
              <a-icon type="search" class="search-icon" />
              <a-input
                v-model="searchQuery"
                placeholder="搜索智能体名称、描述或标签..."
                size="large"
                @pressEnter="handleSearch"
                @input="handleSearch"
                class="search-input"
              />
              <a-icon
                v-if="searchQuery"
                type="close-circle"
                class="clear-icon"
                @click="clearSearch"
              />
            </div>
          </div>

          <!-- 筛选器 -->
          <div class="filter-controls">
            <!-- 作者类型筛选 -->
            <div class="filter-group">
              <div class="filter-buttons">
                <button
                  class="filter-btn"
                  :class="{ 'active': authorTypeFilter === '' }"
                  @click="setAuthorTypeFilter('')"
                >
                  <a-icon type="appstore" />
                  全部
                </button>
                <button
                  class="filter-btn official"
                  :class="{ 'active': authorTypeFilter === '1' }"
                  @click="setAuthorTypeFilter('1')"
                >
                  <a-icon type="crown" />
                  官方
                </button>
                <button
                  class="filter-btn creator"
                  :class="{ 'active': authorTypeFilter === '2' }"
                  @click="setAuthorTypeFilter('2')"
                >
                  <a-icon type="user" />
                  创作者
                </button>
              </div>
            </div>

            <!-- 状态筛选 -->
            <div class="filter-group">
              <div class="filter-buttons">
                <button
                  class="filter-btn"
                  :class="{ 'active': purchaseStatusFilter === '' }"
                  @click="setPurchaseStatusFilter('')"
                >
                  <a-icon type="appstore" />
                  全部
                </button>
                <button
                  class="filter-btn purchased"
                  :class="{ 'active': purchaseStatusFilter === 'purchased' }"
                  @click="setPurchaseStatusFilter('purchased')"
                >
                  <a-icon type="check-circle" />
                  {{ getPurchaseFilterText() }}
                </button>
                <button
                  class="filter-btn unpurchased"
                  :class="{ 'active': purchaseStatusFilter === 'unpurchased' }"
                  @click="setPurchaseStatusFilter('unpurchased')"
                >
                  <a-icon type="shopping" />
                  未购
                </button>
              </div>
            </div>

            <!-- 重置按钮 -->
            <div class="filter-group">
              <div class="filter-buttons">
                <button
                  class="filter-btn reset-btn"
                  @click="resetAllFilters"
                  :disabled="!hasActiveFilters"
                >
                  <a-icon type="reload" />
                  重置
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 智能体列表 -->
    <div class="agent-list" v-if="!loading">
      <div class="list-header">
        <h3 class="list-title">
          智能体列表
          <span class="list-count">({{ totalCount }}个)</span>
        </h3>
      </div>

      <!-- 智能体卡片网格 -->
      <div class="agent-grid" v-if="agentList.length > 0">
        <AgentCard
          v-for="agent in agentList"
          :key="agent.id"
          :agent="agent"
          @view-detail="handleViewDetail"
        />
      </div>

      <!-- 空状态 -->
      <div v-else class="empty-state">
        <a-empty
          description="暂无智能体数据"
        >
          <a-button type="primary" @click="handleRefresh">
            <a-icon type="reload" />
            刷新数据
          </a-button>
        </a-empty>
      </div>

      <!-- 加载更多提示 -->
      <div class="load-more-wrapper" v-if="agentList.length > 0">
        <div v-if="loadingMore" class="loading-more">
          <a-spin size="small" />
          <span>正在加载更多...</span>
        </div>
        <div v-else-if="hasMore" class="load-more-trigger" ref="loadMoreTrigger">
          <!-- 滚动到这里触发加载更多 -->
        </div>
        <div v-else class="no-more-data">
          <a-icon type="check-circle" />
          <span>已加载全部数据 (共{{ totalCount }}个)</span>
        </div>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-else class="loading-state">
      <a-spin size="large" tip="正在加载智能体数据...">
        <div class="loading-placeholder"></div>
      </a-spin>
    </div>

    <!-- 智能体详情弹窗 -->
    <AgentDetailModal
      :visible="detailModalVisible"
      :agentId="selectedAgentId"
      :isPurchased="isSelectedAgentPurchased"
      @close="handleCloseDetailModal"
      @update:visible="handleUpdateVisible"
      @purchase="handlePurchaseFromModal"
      @purchase-success="handlePurchaseSuccess"
    />
  </div>
</template>

<script>
import AgentCard from './AgentCard.vue'
import AgentDetailModal from './AgentDetailModal.vue'
import { getUserRole } from '@/api/usercenter'

export default {
  name: 'AgentMarket',
  components: {
    AgentCard,
    AgentDetailModal
  },
  data() {
    return {
      loading: false,
      loadingMore: false,
      searchQuery: '',
      authorTypeFilter: '',
      purchaseStatusFilter: '', // 购买状态筛选：'' | 'purchased' | 'free'
      agentList: [],
      userRole: 'user', // 用户角色
      currentPage: 1,
      pageSize: 16,
      totalCount: 0,
      hasMore: true,
      // 详情弹窗相关
      detailModalVisible: false, // 详情弹窗显示状态
      selectedAgentId: '', // 选中的智能体ID
      selectedAgent: null, // 选中的智能体数据
      purchasedAgents: [], // 已购买的智能体ID列表
      debounceSearch: null // 防抖搜索函数
    }
  },
  computed: {
    // 检查选中的智能体是否已购买（使用智能体对象的购买状态）
    isSelectedAgentPurchased() {
      if (!this.selectedAgent) {
        return false
      }
      return this.selectedAgent.isPurchased || false
    },

    // 检查是否有激活的筛选条件
    hasActiveFilters() {
      return this.searchQuery !== '' ||
             this.authorTypeFilter !== '' ||
             this.purchaseStatusFilter !== ''
    }
  },
  async mounted() {
    await this.loadUserRole()
    // 🔥 不再需要单独加载购买状态，后端列表API已包含购买状态
    await this.loadAgentList()
    this.setupIntersectionObserver()
    this.initDebounceSearch()
  },

  beforeDestroy() {
    if (this.observer) {
      this.observer.disconnect()
    }
  },
  methods: {
    // 加载用户角色（实时API获取，与Membership页面保持一致）
    async loadUserRole() {
      try {
        const response = await getUserRole()
        if (response && response.success) {
          this.userRole = response.result.role_code || 'user'
        } else {
          this.userRole = 'user'
        }
      } catch (error) {
        console.error('🔍 AgentMarket: 获取用户角色异常:', error)
        this.userRole = 'user'
      }
    },

    // 加载智能体列表（首次加载或搜索时重置）
    async loadAgentList(reset = true) {
      if (reset) {
        this.loading = true
        this.currentPage = 1
        this.agentList = []
        this.hasMore = true
      } else {
        this.loadingMore = true
      }

      try {
        const params = {
          pageNo: this.currentPage,
          pageSize: this.pageSize,
          agentName: this.searchQuery || undefined,
          authorType: this.authorTypeFilter || undefined,
          auditStatus: '2', // 只显示审核通过的
          purchaseStatus: this.purchaseStatusFilter || undefined // 添加购买状态筛选
        }

        // 调用后端API
        const response = await this.$http.get('/api/agent/market/list', { params })

        // 兼容不同的响应格式
        const data = response.data || response
        if (data && data.success) {
          const newRecords = data.result.records || []

          // 直接使用后端返回的价格信息，不再前端计算
          const processedRecords = newRecords.map(agent => {
            // 后端已经计算好了所有价格信息，直接使用
            return {
              ...agent
              // 后端返回的字段：discountPrice, originalPrice, showDiscountPrice, isFree, isPurchased 等
            }
          })

          if (reset) {
            this.agentList = processedRecords
          } else {
            this.agentList.push(...processedRecords)
          }

          // 使用后端返回的总数（后端已经根据筛选条件返回正确的总数）
          this.totalCount = data.result.total || 0

          // 判断是否还有更多数据
          this.hasMore = this.agentList.length < this.totalCount

          // 如果有数据，准备下一页
          if (newRecords.length > 0) {
            this.currentPage++
          }
        } else {
          this.$message.error((data && data.message) || '获取智能体列表失败')
        }
      } catch (error) {
        console.error('加载智能体列表失败:', error)
        this.$message.error('加载智能体列表失败，请稍后重试')
      } finally {
        this.loading = false
        this.loadingMore = false

        // 重新设置IntersectionObserver，确保监听新的DOM元素
        if (reset) {
          this.$nextTick(() => {
            this.setupIntersectionObserver()
          })
        }
      }
    },

    // 临时模拟数据
    async loadMockData() {
      // 模拟API延迟
      await new Promise(resolve => setTimeout(resolve, 500))

      this.agentList = []
      this.totalCount = 0
      this.hasMore = false
    },

    // 获取购买筛选的显示文本
    getPurchaseFilterText() {
      if (this.userRole === 'SVIP') {
        return '已购/SVIP免费'
      } else if (this.userRole === 'VIP') {
        return '已购买'
      } else {
        return '已购买'
      }
    },

    // 加载更多数据
    async loadMore() {
      if (!this.hasMore || this.loadingMore) {
        return
      }
      await this.loadAgentList(false)
    },

    // 计算价格和推广标签显示
    calculatePrice(agent) {
      let showSvipPromo = false
      let showDiscountPrice = false
      let discountRate = 1 // 默认无折扣
      let isFree = false // 是否免费

      // 🔥 已购买的智能体不显示任何推广标签（使用后端返回的购买状态）
      if (agent.isPurchased) {
        return {
          showSvipPromo: false,
          showDiscountPrice: false,
          discountRate: 1,
          isFree: false,
          isPurchased: true
        }
      }

      // 根据用户角色计算价格和推广显示
      if (this.userRole === null || this.userRole === 'user' || this.userRole === 'admin') {
        // 未登录、普通用户或管理员：显示SVIP推广标签
        showSvipPromo = true
        showDiscountPrice = false
      } else if (this.userRole === 'VIP') {
        // VIP用户：显示7折价格，不显示推广标签
        showSvipPromo = false
        showDiscountPrice = true
        discountRate = 0.7 // VIP 7折
      } else if (this.userRole === 'SVIP') {
        // SVIP用户：根据作者类型计算价格
        showSvipPromo = false

        if (agent && (agent.authorType === 1 || agent.authorType === '1')) {
          // 官方智能体：免费
          isFree = true
          discountRate = 0
          showDiscountPrice = false // 免费时不显示折扣价格
        } else if (agent && (agent.authorType === 2 || agent.authorType === '2')) {
          // 创作者智能体：5折
          isFree = false
          showDiscountPrice = true
          discountRate = 0.5
        }
      }



      return {
        showSvipPromo,
        showDiscountPrice,
        discountRate,
        isFree,
        isPurchased: false
      }
    },

    // 搜索处理
    handleSearch() {
      this.scrollToTop()
      this.loadAgentList(true)
    },

    // 筛选变化处理
    handleFilterChange() {
      this.scrollToTop()
      this.loadAgentList(true)
    },

    // 设置作者类型筛选
    setAuthorTypeFilter(value) {
      this.authorTypeFilter = value
      this.handleFilterChange()
    },

    // 设置购买状态筛选
    setPurchaseStatusFilter(value) {
      this.purchaseStatusFilter = value
      this.handleFilterChange()
    },

    // 重置所有筛选条件
    resetAllFilters() {
      this.searchQuery = ''
      this.authorTypeFilter = ''
      this.purchaseStatusFilter = ''
      this.handleFilterChange()
    },

    // 滚动到顶部
    scrollToTop() {
      // 滚动到页面顶部
      window.scrollTo({
        top: 0,
        behavior: 'smooth'
      })
    },

    // 设置滚动监听
    setupIntersectionObserver() {
      // 先断开旧的observer
      if (this.observer) {
        this.observer.disconnect()
        this.observer = null
      }

      this.$nextTick(() => {
        const target = this.$refs.loadMoreTrigger
        if (!target) return

        this.observer = new IntersectionObserver((entries) => {
          entries.forEach(entry => {
            if (entry.isIntersecting && this.hasMore && !this.loadingMore) {
              this.loadMore()
            }
          })
        }, {
          rootMargin: '100px' // 提前100px开始加载
        })

        this.observer.observe(target)
      })
    },

    // 查看详情
    async handleViewDetail(agent) {
      // 🔒 服务端验证购买状态，防止前端缓存篡改
      try {
        const response = await this.$http.get(`/api/agent/market/purchase/check/${agent.id}`)

        if (response && response.success) {
          // 更新智能体的真实购买状态，但保持SVIP免费的显示逻辑
          const backendPurchased = response.result.isPurchased

          // 如果是SVIP用户对官方智能体，保持免费状态显示，不改为已购买
          if (this.userRole === 'SVIP' && agent.authorType === '1' && agent.isFree) {
            // 保持SVIP免费状态，不更新为已购买
            agent.isPurchased = backendPurchased // 内部状态更新（用于权限判断）
          } else {
            // 其他情况正常更新购买状态
            agent.isPurchased = backendPurchased
          }
        }
      } catch (error) {
        console.error('❌ 购买状态验证出错:', error)
        // 验证失败时，为了安全起见，假设未购买
        agent.isPurchased = false
      }

      // 🎯 显示详情弹窗（无论是否购买）
      this.selectedAgent = agent
      this.selectedAgentId = agent.id
      this.detailModalVisible = true
    },

    // 关闭详情弹窗
    handleCloseDetailModal() {
      this.detailModalVisible = false
      this.selectedAgent = null
      this.selectedAgentId = ''
    },

    // 处理弹窗visible状态更新
    handleUpdateVisible(visible) {
      this.detailModalVisible = visible
      if (!visible) {
        this.selectedAgent = null
        this.selectedAgentId = ''
      }
    },

    // 从弹窗发起购买
    handlePurchaseFromModal(agent) {
      // 购买逻辑已在AgentDetailModal中实现
    },

    // 购买成功回调
    handlePurchaseSuccess(agentId) {
      this.onPurchaseSuccess(agentId)
      this.$message.success('购买成功！您现在可以下载该智能体的所有工作流了')
    },

    // 🔥 已废弃：加载已购买的智能体列表（现在由后端列表API直接返回购买状态）
    async loadPurchasedAgents() {
      // 此方法已不再使用，购买状态现在由后端列表API直接返回
    },

    // 检查智能体是否已购买（现在直接使用后端返回的状态）
    isAgentPurchased(agentId) {
      // 在智能体列表中查找对应的智能体，使用后端返回的购买状态
      const agent = this.agentList.find(a => a.id === agentId)
      return agent ? agent.isPurchased : false
    },

    // 购买成功后更新状态
    async onPurchaseSuccess(agentId) {
      // 🔄 重新加载列表以获取最新的购买状态（后端会返回更新后的购买状态）
      this.loadAgentList(true) // 重置并重新加载

      // 🔄 刷新用户角色（防止购买会员后角色状态不更新）
      await this.loadUserRole()
    },

    // 初始化防抖搜索
    initDebounceSearch() {
      this.debounceSearch = this.debounce(() => {
        this.handleSearch()
      }, 300)
    },

    // 防抖函数
    debounce(func, wait) {
      let timeout
      return function executedFunction(...args) {
        const later = () => {
          clearTimeout(timeout)
          func(...args)
        }
        clearTimeout(timeout)
        timeout = setTimeout(later, wait)
      }
    },

    // 刷新数据
    handleRefresh() {
      this.loadAgentList()
    },

    // 获取创作者数量
    getCreatorCount() {
      return this.agentList.filter(agent => agent.authorType === '2').length
    },

    // 清空搜索
    clearSearch() {
      this.searchQuery = ''
      this.handleSearch()
    },

    // 清除所有筛选
    clearAllFilters() {
      this.searchQuery = ''
      this.authorTypeFilter = ''
      this.purchaseStatusFilter = ''
      this.handleFilterChange()
    }
  }
}
</script>

<style scoped>
.agent-market {
  padding: 0;
}

/* 固定的搜索和筛选区域 */
.sticky-filters {
  position: sticky;
  top: 156px; /* 顶部导航栏100px + tab栏60px */
  z-index: 100;
  background: white;
  padding: 1rem 0;
  margin-bottom: 1rem;
  border-bottom: 1px solid #f1f5f9;
}

.market-filters {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  padding: 2rem;
  border-radius: 20px;
  box-shadow: 0 8px 32px rgba(59, 130, 246, 0.12), 0 2px 8px rgba(0, 0, 0, 0.04);
  max-width: 1600px;
  margin: 0 auto;
  border: 1px solid rgba(59, 130, 246, 0.1);
}

.filter-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 2rem;
}

.search-box {
  flex: 1;
  max-width: 600px;
}

.search-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  border: 2px solid #cbd5e1;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.search-wrapper:hover {
  border-color: #3b82f6;
  box-shadow: 0 6px 20px rgba(59, 130, 246, 0.2);
  transform: translateY(-1px);
}

.search-wrapper:focus-within {
  border-color: #3b82f6;
  box-shadow: 0 8px 24px rgba(59, 130, 246, 0.3);
  transform: translateY(-1px);
}

.search-icon {
  position: absolute;
  left: 16px;
  z-index: 2;
  color: #64748b;
  font-size: 18px;
  transition: color 0.3s ease;
}

.search-wrapper:focus-within .search-icon {
  color: #3b82f6;
}

.clear-icon {
  position: absolute;
  right: 16px;
  z-index: 2;
  color: #94a3b8;
  font-size: 16px;
  cursor: pointer;
  transition: color 0.3s ease;
}

.clear-icon:hover {
  color: #64748b;
}

.search-input {
  flex: 1;
  border: none !important;
  box-shadow: none !important;
  padding-left: 48px !important;
  padding-right: 48px !important;
  font-size: 16px;
  height: 48px;
  background: transparent;
}

.search-input:focus {
  border: none !important;
  box-shadow: none !important;
}

.filter-controls {
  display: flex;
  align-items: flex-start;
  gap: 2rem;
  flex-wrap: wrap;
}

/* 筛选组 */
.filter-group {
  display: flex;
  align-items: center;
}

/* 筛选按钮组 */
.filter-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

/* 筛选按钮 */
.filter-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  background: white;
  color: #64748b;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.filter-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(79, 70, 229, 0.1), transparent);
  transition: left 0.5s ease;
}

.filter-btn:hover::before {
  left: 100%;
}

.filter-btn:hover {
  border-color: #4f46e5;
  color: #4f46e5;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(79, 70, 229, 0.15);
}

/* 激活状态 */
.filter-btn.active {
  background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
  border-color: #4f46e5;
  color: white;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3);
}

.filter-btn.active:hover {
  background: linear-gradient(135deg, #4338ca 0%, #6d28d9 100%);
  border-color: #4338ca;
  box-shadow: 0 6px 16px rgba(79, 70, 229, 0.4);
}

/* 特殊按钮样式 */
.filter-btn.official.active {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  border-color: #f59e0b;
}

.filter-btn.official.active:hover {
  background: linear-gradient(135deg, #d97706 0%, #b45309 100%);
  border-color: #d97706;
}

.filter-btn.creator.active {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
  border-color: #3b82f6;
}

.filter-btn.creator.active:hover {
  background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
  border-color: #2563eb;
}

.filter-btn.purchased.active {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  border-color: #10b981;
}

.filter-btn.purchased.active:hover {
  background: linear-gradient(135deg, #059669 0%, #047857 100%);
  border-color: #059669;
}

.filter-btn.unpurchased.active {
  background: linear-gradient(135deg, #f97316 0%, #ea580c 100%);
  border-color: #f97316;
}

.filter-btn.unpurchased.active:hover {
  background: linear-gradient(135deg, #ea580c 0%, #c2410c 100%);
  border-color: #ea580c;
}

/* 按钮图标 */
.filter-btn .anticon {
  font-size: 16px;
}

/* 重置按钮特殊样式 */
.filter-btn.reset-btn {
  border-color: #f97316;
  color: #f97316;
}

.filter-btn.reset-btn:hover:not(:disabled) {
  border-color: #ea580c;
  color: #ea580c;
  background: linear-gradient(135deg, #fff7ed 0%, #fed7aa 100%);
}

.filter-btn.reset-btn:disabled {
  border-color: #e2e8f0;
  color: #94a3b8;
  background: #f8fafc;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.filter-btn.reset-btn:disabled:hover {
  border-color: #e2e8f0;
  color: #94a3b8;
  background: #f8fafc;
  transform: none;
  box-shadow: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .filter-controls {
    gap: 1rem;
  }

  .filter-buttons {
    gap: 6px;
  }

  .filter-btn {
    padding: 6px 12px;
    font-size: 13px;
  }

  .filter-btn .anticon {
    font-size: 14px;
  }

  .filter-btn.reset-btn {
    padding: 6px 10px;
  }
}

/* 智能体列表 */
.agent-list {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.list-header {
  padding: 2rem 2rem 1rem 2rem;
  border-bottom: 1px solid #f1f5f9;
}

.list-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
}

.list-count {
  color: #64748b;
  font-weight: 400;
  font-size: 1rem;
}

/* 智能体网格 */
.agent-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 1.5rem;
  padding: 2rem;
  max-width: 1600px;
  margin: 0 auto;
}

/* 空状态 */
.empty-state {
  padding: 4rem 2rem;
  text-align: center;
}

/* 懒加载相关样式 */
.load-more-wrapper {
  padding: 2rem;
  border-top: 1px solid #f1f5f9;
  display: flex;
  justify-content: center;
  max-width: 1600px;
  margin: 0 auto;
}

.loading-more {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #64748b;
  font-size: 0.875rem;
}

.load-more-trigger {
  height: 20px;
  width: 100%;
}

.no-more-data {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #94a3b8;
  font-size: 0.875rem;
}

.no-more-data .anticon {
  color: #10b981;
}

/* 加载状态 */
.loading-state {
  padding: 4rem 2rem;
  text-align: center;
}

.loading-placeholder {
  height: 400px;
  background: transparent;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .filter-row {
    flex-direction: column;
    gap: 1rem;
  }

  .search-box {
    width: 100%;
  }

  .filter-controls {
    width: 100%;
    justify-content: space-between;
  }

  .agent-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
    padding: 1rem;
  }

  .market-filters,
  .list-header,
  .load-more-wrapper {
    padding: 1rem;
  }
}
</style>
