{"openapi": "3.0.0", "info": {"title": "剪映小助手_智界数据箱 - 字幕数据生成器", "description": "根据时间线制作字幕数据", "version": "1.0.0"}, "servers": [{"url": "https://www.aigcview.com"}], "paths": {"/jeecg-boot/api/jianying/caption_infos": {"post": {"summary": "生成字幕数据", "description": "根据时间线制作字幕数据", "operationId": "captionInfos_zj", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"access_key": {"type": "string", "description": "访问密钥（必填）", "example": "JianyingAPI_2025_SecureAccess_AigcView", "default": "JianyingAPI_2025_SecureAccess_AigcView"}, "zj_font_size": {"type": "integer", "description": "文字大小", "example": 30}, "zj_keyword_font_size": {"type": "integer", "description": "关键词字大小", "example": 35}, "zj_loop_animation": {"type": "string", "description": "对应剪映的循环动画名字，多个动画请用英文|分割，比如：扫光|晃动", "example": "扫光|晃动"}, "zj_out_animation_duration": {"type": "integer", "description": "出场动画时长", "example": 1000000}, "zj_loop_animation_duration": {"type": "integer", "description": "循环动画时长", "example": 2000000}, "zj_out_animation": {"type": "string", "description": "对应剪映的出场动画名字，多个动画请用英文|分割，比如：消散|闭幕", "example": "消散|闭幕"}, "zj_texts": {"type": "array", "description": "文本列表（必填）", "items": {"type": "string"}, "example": ["这是第一段字幕", "这是第二段字幕"]}, "zj_timelines": {"type": "array", "description": "时间节点，只接收结构：[{\"start\":0,\"end\":4612}]，一般从audio_timeline节点的输出获取（必填）", "items": {"type": "object", "properties": {"start": {"type": "integer"}, "end": {"type": "integer"}}}, "example": [{"start": 0, "end": 4612000}]}, "zj_in_animation": {"type": "string", "description": "对应剪映的入场动画名字，多个动画请用英文|分割，比如：飞入|放大", "example": "飞入|放大"}, "zj_in_animation_duration": {"type": "integer", "description": "入场动画时长", "example": 1000000}, "zj_keyword_color": {"type": "string", "description": "关键词颜色", "example": "#ff0000"}, "zj_keywords": {"type": "array", "description": "文本里面的重点词列表", "items": {"type": "string"}, "example": ["重点", "关键词"]}}, "required": ["access_key", "zj_texts", "zj_timelines"], "additionalProperties": false}}}}, "responses": {"200": {"description": "成功生成字幕数据", "content": {"application/json": {"schema": {"type": "object", "properties": {"infos": {"type": "string", "description": "字幕信息数组（JSON格式字符串），格式：[{\"start\":0,\"end\":1920000,\"text\":\"字幕内容\"}]"}}, "required": ["infos"]}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "description": "操作是否成功", "example": false}, "error": {"type": "string", "description": "错误信息"}, "code": {"type": "string", "description": "错误代码"}}, "required": ["success", "error"]}}}}, "500": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "description": "操作是否成功", "example": false}, "error": {"type": "string", "description": "错误信息"}, "code": {"type": "string", "description": "错误代码"}}, "required": ["success", "error"]}}}}}}}}}