# 剪映小助手 - 版本检查功能

## 🎯 功能简介

剪映小助手的版本检查功能提供了完整的应用版本管理解决方案，支持自动检查、强制更新、可选更新等多种场景。

## ✨ 主要特性

- 🔄 **自动版本检查** - 应用启动时自动检查最新版本
- 🚨 **强制更新支持** - 安全更新等重要版本的强制更新机制
- 🎛️ **用户友好界面** - 直观的更新提示和设置界面
- 📱 **后台检查** - 定期在后台检查版本更新
- 💾 **智能缓存** - 减少不必要的网络请求
- 🛡️ **错误处理** - 完善的网络异常和错误处理机制
- 📝 **详细日志** - 完整的操作日志记录便于调试

## 🏗️ 架构设计

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   UpdateManager │    │  VersionChecker │    │   UpdateWindow  │
│                 │    │                 │    │                 │
│ - 统一管理      │◄──►│ - 版本检查      │    │ - 更新提示      │
│ - 用户交互      │    │ - 缓存管理      │    │ - 用户选择      │
│ - 后台检查      │    │ - 日志记录      │    │ - 界面显示      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │   Main Process  │
                    │                 │
                    │ - 应用启动      │
                    │ - IPC处理       │
                    │ - 生命周期管理  │
                    └─────────────────┘
```

## 🚀 快速开始

### 1. 基本使用

版本检查功能已完全集成到应用中：

```javascript
// 自动启动检查
app.whenReady().then(async () => {
  const canContinue = await updateManager.checkOnStartup();
  if (canContinue) {
    createWindow();
  }
});
```

### 2. 手动检查

在设置页面点击"检查更新"按钮，或通过代码：

```javascript
await updateManager.checkForUpdates(true);
```

## 📋 用户操作流程

### 应用启动流程

```mermaid
graph TD
    A[应用启动] --> B[版本检查]
    B --> C{需要更新?}
    C -->|否| D[正常启动]
    C -->|是| E{强制更新?}
    E -->|是| F[显示强制更新窗口]
    E -->|否| G[显示可选更新提示]
    F --> H{用户选择}
    H -->|更新| I[打开下载链接]
    H -->|退出| J[应用退出]
    G --> K{用户选择}
    K -->|立即更新| I
    K -->|稍后提醒| D
    K -->|跳过版本| D
    I --> D
```

### 设置页面操作

1. **自动检查更新** - 开启/关闭自动检查
2. **检查频率** - 设置后台检查间隔
3. **显示可选更新** - 控制是否显示非强制更新
4. **手动检查** - 立即检查最新版本

## 🔧 配置说明

### 环境配置

```javascript
// 生产环境
const config = {
  apiBaseUrl: 'https://aigcview.com/jeecg-boot',
  programType: 'desktop',
  enableBackgroundCheck: true,
  backgroundCheckInterval: 4 * 60 * 60 * 1000 // 4小时
};

// 开发环境
const config = {
  apiBaseUrl: 'http://localhost:8080/jeecg-boot',
  programType: 'desktop',
  enableBackgroundCheck: false
};
```

### 用户偏好设置

用户可配置的选项：
- 自动检查更新（默认：开启）
- 检查频率（默认：4小时）
- 显示可选更新（默认：开启）
- 跳过的版本列表

## 🧪 测试

### 运行测试

```bash
# 单元测试
node test/version-check-suite.js

# 集成测试
node test/mock-server.js

# 手动测试
node test-version-check.js
```

### 测试覆盖

- ✅ 版本比较算法
- ✅ 强制更新判断
- ✅ 缓存机制
- ✅ 用户偏好设置
- ✅ 日志功能
- ✅ 错误处理
- ✅ 网络异常模拟

## 📚 文档

- [详细使用指南](./VERSION_CHECK_GUIDE.md) - 完整的功能说明和配置指南
- [API文档](./VERSION_CHECK_GUIDE.md#api接口) - 接口说明和参数
- [故障排除](./VERSION_CHECK_GUIDE.md#故障排除) - 常见问题解决方案

## 🔍 调试

### 查看日志

```javascript
// 获取版本检查日志
const logs = await ipcRenderer.invoke('get-version-logs', 100);

// 按级别过滤
const errorLogs = await ipcRenderer.invoke('get-version-logs', 50, 'error');
```

### 清除缓存

```javascript
// 清除版本检查缓存
versionChecker.clearCache();
```

### 手动测试API

```bash
curl "https://aigcview.com/jeecg-boot/aigcview/versioncontrol/getLatest?programType=desktop"
```

## 🚨 注意事项

1. **网络依赖** - 版本检查需要网络连接
2. **权限要求** - 确保应用有网络访问权限
3. **防火墙设置** - 可能需要配置防火墙规则
4. **版本号格式** - 使用语义化版本号（如1.0.0）
5. **缓存机制** - 24小时内重复检查会使用缓存

## 🤝 贡献

如需贡献代码或报告问题：

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 发起 Pull Request

## 📞 技术支持

- 📧 邮箱：<EMAIL>
- 🌐 官网：https://www.aigcview.com
- 📱 QQ群：[待补充]

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](../LICENSE) 文件了解详情。

---

**智界AigcView** - 让AI创作更简单 🚀
