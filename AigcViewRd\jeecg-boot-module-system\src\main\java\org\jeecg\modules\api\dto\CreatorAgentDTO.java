package org.jeecg.modules.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Description: 创作者智能体数据传输对象
 * @Author: 智界AIGC
 * @Date: 2025-08-04
 * @Version: V1.0
 */
@Data
@ApiModel(value = "CreatorAgentDTO", description = "创作者智能体数据传输对象")
public class CreatorAgentDTO implements Serializable {
    
    private static final long serialVersionUID = 1L;

    /**智能体名称*/
    @ApiModelProperty(value = "智能体名称", required = true)
    @NotBlank(message = "智能体名称不能为空")
    @Length(max = 100, message = "智能体名称长度不能超过100个字符")
    private String agentName;

    /**智能体描述*/
    @ApiModelProperty(value = "智能体描述", required = true)
    @NotBlank(message = "智能体描述不能为空")
    @Length(max = 1000, message = "智能体描述长度不能超过1000个字符")
    private String agentDescription;

    /**智能体头像*/
    @ApiModelProperty(value = "智能体头像URL")
    @Length(max = 500, message = "智能体头像URL长度不能超过500个字符")
    private String agentAvatar;

    /**体验链接*/
    @ApiModelProperty(value = "体验链接")
    @Length(max = 500, message = "体验链接长度不能超过500个字符")
    private String experienceLink;

    /**价格（元）*/
    @ApiModelProperty(value = "价格（元）", required = true)
    @NotNull(message = "价格不能为空")
    @DecimalMin(value = "0.00", message = "价格不能小于0")
    private BigDecimal price;

    // 注意：以下字段不在DTO中，由后端自动处理
    // - agentId: 系统自动生成
    // - authorType: 默认设置为"2"（创作者）
    // - auditStatus: 默认设置为"1"（待审核）
    // - createBy: 当前登录用户
    // - demoVideo: 保持为空，创作者中心不支持视频上传
}
