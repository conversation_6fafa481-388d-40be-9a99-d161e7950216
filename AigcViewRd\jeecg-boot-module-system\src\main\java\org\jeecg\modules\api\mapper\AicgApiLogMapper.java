package org.jeecg.modules.api.mapper;

import java.util.List;
import java.util.Map;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.jeecg.modules.api.entity.AicgApiLog;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: API调用日志
 * @Author: jeecg-boot
 * @Date: 2025-06-14
 * @Version: V1.0
 */
public interface AicgApiLogMapper extends BaseMapper<AicgApiLog> {

    /**
     * 获取用户今日API统计
     */
    @Select("SELECT * FROM v_api_stats_today WHERE user_id = #{userId}")
    Map<String, Object> getUserTodayStats(@Param("userId") String userId);

    /**
     * 获取用户本月API统计
     */
    @Select("SELECT * FROM v_api_stats_month WHERE user_id = #{userId}")
    Map<String, Object> getUserMonthStats(@Param("userId") String userId);

    /**
     * 获取系统今日API统计
     */
    @Select("SELECT * FROM v_system_stats_today")
    Map<String, Object> getSystemTodayStats();

    /**
     * 获取系统本月API统计
     */
    @Select("SELECT * FROM v_system_stats_month")
    Map<String, Object> getSystemMonthStats();

    /**
     * 获取用户昨日API统计
     */
    @Select("SELECT COUNT(*) as total_calls, " +
            "SUM(CASE WHEN success = 1 THEN 1 ELSE 0 END) as success_calls, " +
            "SUM(CASE WHEN success = 0 THEN 1 ELSE 0 END) as failed_calls " +
            "FROM aicg_api_log " +
            "WHERE user_id = #{userId} AND DATE(request_time) = DATE_SUB(CURDATE(), INTERVAL 1 DAY)")
    Map<String, Object> getUserYesterdayStats(@Param("userId") String userId);

    /**
     * 获取用户上月API统计
     */
    @Select("SELECT COUNT(*) as total_calls, " +
            "SUM(CASE WHEN success = 1 THEN 1 ELSE 0 END) as success_calls, " +
            "SUM(CASE WHEN success = 0 THEN 1 ELSE 0 END) as failed_calls " +
            "FROM aicg_api_log " +
            "WHERE user_id = #{userId} " +
            "AND YEAR(request_time) = YEAR(DATE_SUB(CURDATE(), INTERVAL 1 MONTH)) " +
            "AND MONTH(request_time) = MONTH(DATE_SUB(CURDATE(), INTERVAL 1 MONTH))")
    Map<String, Object> getUserLastMonthStats(@Param("userId") String userId);

    /**
     * 获取系统昨日API统计
     */
    @Select("SELECT COUNT(*) as total_calls, " +
            "SUM(CASE WHEN success = 1 THEN 1 ELSE 0 END) as success_calls, " +
            "SUM(CASE WHEN success = 0 THEN 1 ELSE 0 END) as failed_calls " +
            "FROM aicg_api_log " +
            "WHERE DATE(request_time) = DATE_SUB(CURDATE(), INTERVAL 1 DAY)")
    Map<String, Object> getSystemYesterdayStats();

    /**
     * 获取系统上月API统计
     */
    @Select("SELECT COUNT(*) as total_calls, " +
            "SUM(CASE WHEN success = 1 THEN 1 ELSE 0 END) as success_calls, " +
            "SUM(CASE WHEN success = 0 THEN 1 ELSE 0 END) as failed_calls " +
            "FROM aicg_api_log " +
            "WHERE YEAR(request_time) = YEAR(DATE_SUB(CURDATE(), INTERVAL 1 MONTH)) " +
            "AND MONTH(request_time) = MONTH(DATE_SUB(CURDATE(), INTERVAL 1 MONTH))")
    Map<String, Object> getSystemLastMonthStats();

    /**
     * 获取用户最近API调用记录
     */
    @Select("SELECT id, api_type, request_time, success, cost_points, status_code, error_message " +
            "FROM aicg_api_log " +
            "WHERE user_id = #{userId} " +
            "ORDER BY request_time DESC " +
            "LIMIT #{limit}")
    List<Map<String, Object>> getUserRecentCalls(@Param("userId") String userId, @Param("limit") int limit);

    /**
     * 获取系统最近API调用记录
     */
    @Select("SELECT id, user_id, api_type, request_time, success, cost_points, status_code, error_message " +
            "FROM aicg_api_log " +
            "ORDER BY request_time DESC " +
            "LIMIT #{limit}")
    List<Map<String, Object>> getSystemRecentCalls(@Param("limit") int limit);

    /**
     * 获取用户24小时趋势数据
     */
    @Select("SELECT " +
            "DATE_FORMAT(request_time, '%Y-%m-%d %H') as time_label, " +
            "COUNT(*) as call_count, " +
            "SUM(CASE WHEN success = 1 THEN 1 ELSE 0 END) as success_count, " +
            "SUM(CASE WHEN success = 0 THEN 1 ELSE 0 END) as error_count " +
            "FROM aicg_api_log " +
            "WHERE user_id = #{userId} AND request_time >= DATE_SUB(NOW(), INTERVAL 24 HOUR) " +
            "GROUP BY DATE_FORMAT(request_time, '%Y-%m-%d %H') " +
            "ORDER BY DATE_FORMAT(request_time, '%Y-%m-%d %H')")
    List<Map<String, Object>> getUserTrendData(@Param("userId") String userId);

    /**
     * 获取系统24小时趋势数据
     */
    @Select("SELECT " +
            "DATE_FORMAT(request_time, '%Y-%m-%d %H') as time_label, " +
            "COUNT(*) as call_count, " +
            "SUM(CASE WHEN success = 1 THEN 1 ELSE 0 END) as success_count, " +
            "SUM(CASE WHEN success = 0 THEN 1 ELSE 0 END) as error_count " +
            "FROM aicg_api_log " +
            "WHERE request_time >= DATE_SUB(NOW(), INTERVAL 24 HOUR) " +
            "GROUP BY DATE_FORMAT(request_time, '%Y-%m-%d %H') " +
            "ORDER BY DATE_FORMAT(request_time, '%Y-%m-%d %H')")
    List<Map<String, Object>> getSystemTrendData();

    /**
     * 获取用户API分布数据
     */
    @Select("SELECT api_type as name, COUNT(*) as value " +
            "FROM aicg_api_log " +
            "WHERE user_id = #{userId} " +
            "GROUP BY api_type " +
            "ORDER BY value DESC")
    List<Map<String, Object>> getUserApiDistribution(@Param("userId") String userId);

    /**
     * 获取系统API分布数据
     */
    @Select("SELECT api_type as name, COUNT(*) as value " +
            "FROM aicg_api_log " +
            "GROUP BY api_type " +
            "ORDER BY value DESC")
    List<Map<String, Object>> getSystemApiDistribution();

    /**
     * 获取用户错误统计数据（最近7天）
     */
    @Select("SELECT " +
            "DATE_FORMAT(request_time, '%Y-%m-%d') as date, " +
            "DATE_FORMAT(request_time, '%W') as day_name, " +
            "SUM(CASE WHEN status_code >= 400 AND status_code < 500 THEN 1 ELSE 0 END) as error_4xx, " +
            "SUM(CASE WHEN status_code >= 500 THEN 1 ELSE 0 END) as error_5xx, " +
            "SUM(CASE WHEN duration_ms > 30000 THEN 1 ELSE 0 END) as timeout_errors " +
            "FROM aicg_api_log " +
            "WHERE user_id = #{userId} AND request_time >= DATE_SUB(CURDATE(), INTERVAL 7 DAY) " +
            "GROUP BY DATE_FORMAT(request_time, '%Y-%m-%d'), DATE_FORMAT(request_time, '%W') " +
            "ORDER BY DATE_FORMAT(request_time, '%Y-%m-%d')")
    List<Map<String, Object>> getUserErrorStats(@Param("userId") String userId);

    /**
     * 获取系统错误统计数据（最近7天）
     */
    @Select("SELECT " +
            "DATE_FORMAT(request_time, '%Y-%m-%d') as date, " +
            "DATE_FORMAT(request_time, '%W') as day_name, " +
            "SUM(CASE WHEN status_code >= 400 AND status_code < 500 THEN 1 ELSE 0 END) as error_4xx, " +
            "SUM(CASE WHEN status_code >= 500 THEN 1 ELSE 0 END) as error_5xx, " +
            "SUM(CASE WHEN duration_ms > 30000 THEN 1 ELSE 0 END) as timeout_errors " +
            "FROM aicg_api_log " +
            "WHERE request_time >= DATE_SUB(CURDATE(), INTERVAL 7 DAY) " +
            "GROUP BY DATE_FORMAT(request_time, '%Y-%m-%d'), DATE_FORMAT(request_time, '%W') " +
            "ORDER BY DATE_FORMAT(request_time, '%Y-%m-%d')")
    List<Map<String, Object>> getSystemErrorStats();
}
