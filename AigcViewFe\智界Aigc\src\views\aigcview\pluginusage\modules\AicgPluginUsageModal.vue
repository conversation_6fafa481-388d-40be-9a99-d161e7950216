<template>
  <a-modal
    :title="title"
    :width="800"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel"
    cancelText="关闭">
    
    <a-spin :spinning="confirmLoading">
      <a-form-model ref="form" :model="model" :rules="validatorRules" v-bind="layout">
        
        <a-form-model-item label="用户ID" prop="userId">
          <a-input v-model="model.userId" placeholder="请输入用户ID" :disabled="!!model.id"/>
        </a-form-model-item>
        
        <a-form-model-item label="API密钥" prop="apiKey">
          <a-input v-model="model.apiKey" placeholder="请输入API密钥" :disabled="!!model.id"/>
        </a-form-model-item>
        
        <a-form-model-item label="API接口" prop="apiEndpoint">
          <a-input v-model="model.apiEndpoint" placeholder="请输入API接口地址" :disabled="!!model.id"/>
        </a-form-model-item>
        
        <a-form-model-item label="请求方法" prop="apiMethod">
          <a-select v-model="model.apiMethod" placeholder="请选择请求方法" :disabled="!!model.id">
            <a-select-option value="GET">GET</a-select-option>
            <a-select-option value="POST">POST</a-select-option>
            <a-select-option value="PUT">PUT</a-select-option>
            <a-select-option value="DELETE">DELETE</a-select-option>
          </a-select>
        </a-form-model-item>
        
        <a-form-model-item label="插件ID" prop="pluginId">
          <a-input v-model="model.pluginId" placeholder="请输入插件ID" :disabled="!!model.id"/>
        </a-form-model-item>
        
        <a-form-model-item label="插件标识" prop="pluginKey">
          <a-input v-model="model.pluginKey" placeholder="请输入插件Key" :disabled="!!model.id"/>
        </a-form-model-item>
        
        <a-form-model-item label="插件名称" prop="pluginName">
          <a-input v-model="model.pluginName" placeholder="请输入插件名称" :disabled="!!model.id"/>
        </a-form-model-item>
        
        <a-form-model-item label="响应状态" prop="responseStatus">
          <a-input-number v-model="model.responseStatus" placeholder="请输入响应状态码" :disabled="!!model.id" style="width: 100%"/>
        </a-form-model-item>
        
        <a-form-model-item label="响应时间(ms)" prop="responseTime">
          <a-input-number v-model="model.responseTime" placeholder="请输入响应时间" :disabled="!!model.id" style="width: 100%"/>
        </a-form-model-item>
        
        <a-form-model-item label="消耗Token" prop="tokensUsed">
          <a-input-number v-model="model.tokensUsed" placeholder="请输入消耗Token数量" :disabled="!!model.id" style="width: 100%"/>
        </a-form-model-item>
        
        <a-form-model-item label="消耗金额" prop="costAmount">
          <a-input-number v-model="model.costAmount" placeholder="请输入消耗金额" :precision="2" :disabled="!!model.id" style="width: 100%"/>
        </a-form-model-item>
        
        <a-form-model-item label="IP地址" prop="ipAddress">
          <a-input v-model="model.ipAddress" placeholder="请输入IP地址" :disabled="!!model.id"/>
        </a-form-model-item>
        
        <a-form-model-item label="用户代理" prop="userAgent">
          <a-textarea v-model="model.userAgent" placeholder="请输入用户代理信息" :rows="2" :disabled="!!model.id"/>
        </a-form-model-item>
        
        <a-form-model-item label="请求参数" prop="requestParams">
          <a-textarea v-model="model.requestParams" placeholder="请输入请求参数" :rows="4" :disabled="!!model.id"/>
        </a-form-model-item>
        
        <a-form-model-item label="错误信息" prop="errorMessage" v-if="model.errorMessage">
          <a-textarea v-model="model.errorMessage" placeholder="错误信息" :rows="3" :disabled="true"/>
        </a-form-model-item>
        
        <a-form-model-item label="调用时间" prop="callTime">
          <a-date-picker 
            v-model="model.callTime" 
            placeholder="请选择调用时间" 
            format="YYYY-MM-DD HH:mm:ss"
            show-time
            :disabled="!!model.id"
            style="width: 100%"/>
        </a-form-model-item>
        
      </a-form-model>
    </a-spin>
  </a-modal>
</template>

<script>
  import { httpAction } from '@/api/manage'

  export default {
    name: 'AicgPluginUsageModal',
    components: {
    },
    data () {
      return {
        layout: {
          labelCol: {
            xs: { span: 24 },
            sm: { span: 5 },
          },
          wrapperCol: {
            xs: { span: 24 },
            sm: { span: 16 },
          },
        },
        title:"操作",
        visible: false,
        model: {},
        confirmLoading: false,
        form: this.$form.createForm(this),
        validatorRules: {
          userId: [
            { required: true, message: '请输入用户ID!' },
          ],
          apiEndpoint: [
            { required: true, message: '请输入API接口地址!' },
          ],
          apiMethod: [
            { required: true, message: '请选择请求方法!' },
          ],
          responseStatus: [
            { required: true, message: '请输入响应状态码!' },
          ],
        },
        url: {
          add: "/demo/apiusage/add",
          edit: "/demo/apiusage/edit",
        }
      }
    },
    created () {
    },
    methods: {
      add () {
        this.edit({});
      },
      edit (record) {
        this.form.resetFields();
        this.model = Object.assign({}, record);
        this.visible = true;
        this.$nextTick(() => {
          // 设置表单字段值
          const formData = {
            userId: this.model.userId,
            apiKey: this.model.apiKey,
            apiEndpoint: this.model.apiEndpoint,
            apiMethod: this.model.apiMethod,
            pluginId: this.model.pluginId,
            pluginKey: this.model.pluginKey,
            pluginName: this.model.pluginName,
            responseStatus: this.model.responseStatus,
            responseTime: this.model.responseTime,
            tokensUsed: this.model.tokensUsed,
            costAmount: this.model.costAmount,
            ipAddress: this.model.ipAddress,
            userAgent: this.model.userAgent,
            requestParams: this.model.requestParams,
            errorMessage: this.model.errorMessage,
            callTime: this.model.callTime
          }
          this.form.setFieldsValue(formData)
        })
      },
      close () {
        this.$emit('close');
        this.visible = false;
      },
      handleOk () {
        const that = this;
        // 触发表单验证
        this.$refs.form.validate(valid => {
          if (valid) {
            that.confirmLoading = true;
            let httpurl = '';
            let method = '';
            if(!this.model.id){
              httpurl+=this.url.add;
              method = 'post';
            }else{
              httpurl+=this.url.edit;
               method = 'put';
            }
            httpAction(httpurl,this.model,method).then((res)=>{
              if(res.success){
                that.$message.success(res.message);
                that.$emit('ok');
              }else{
                that.$message.warning(res.message);
              }
            }).finally(() => {
              that.confirmLoading = false;
            })
          }
        })
      },
      handleCancel () {
        this.close()
      },
      popupCallback(row){
        this.model = Object.assign(this.model, row);
      },
    }
  }
</script>
