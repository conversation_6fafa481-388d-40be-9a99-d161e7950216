/**
 * 更新提示窗口
 * 用于显示版本更新信息和处理用户交互
 */

const { BrowserWindow, ipcMain } = require('electron');
const path = require('path');

class UpdateWindow {
  constructor() {
    this.window = null;
    this.versionInfo = null;
  }

  /**
   * 显示更新窗口
   * @param {Object} versionInfo 版本信息
   * @param {boolean} forceUpdate 是否强制更新
   */
  show(versionInfo, forceUpdate = false) {
    this.versionInfo = versionInfo;
    
    if (this.window) {
      this.window.close();
    }
    
    // 创建更新窗口
    this.window = new BrowserWindow({
      width: forceUpdate ? 600 : 500,
      height: forceUpdate ? 500 : 400,
      modal: true,
      resizable: false,
      minimizable: false,
      maximizable: false,
      closable: !forceUpdate, // 强制更新时不允许关闭
      alwaysOnTop: true,
      center: true,
      title: forceUpdate ? '必须更新' : '发现新版本',
      icon: path.join(__dirname, '../assets/icon.png'),
      webPreferences: {
        nodeIntegration: true,
        contextIsolation: false,
        enableRemoteModule: true
      }
    });

    // 加载更新页面
    const htmlFile = forceUpdate ? 'force-update.html' : 'optional-update.html';
    const htmlPath = path.join(__dirname, '../pages', htmlFile);

    console.log('加载更新页面:', htmlPath);
    this.window.loadFile(htmlPath);

    // 窗口准备就绪后发送版本信息
    this.window.webContents.once('dom-ready', () => {
      this.window.webContents.send('version-info', {
        ...versionInfo,
        forceUpdate: forceUpdate
      });
    });

    // 处理窗口关闭事件
    this.window.on('closed', () => {
      this.window = null;
    });

    // 如果是强制更新，阻止用户关闭窗口
    if (forceUpdate) {
      this.window.on('close', (event) => {
        event.preventDefault();
        // 可以在这里显示确认对话框
      });
    }

    this.window.show();
    this.window.focus();
  }

  /**
   * 关闭更新窗口
   */
  close() {
    if (this.window) {
      this.window.destroy();
      this.window = null;
    }
  }

  /**
   * 检查窗口是否打开
   * @returns {boolean} 窗口是否打开
   */
  isOpen() {
    return this.window && !this.window.isDestroyed();
  }
}

module.exports = UpdateWindow;
