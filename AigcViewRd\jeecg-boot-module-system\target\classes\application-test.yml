server:
  port: 8080
  tomcat:
    max-swallow-size: -1
  error:
    include-exception: true
    include-stacktrace: ALWAYS
    include-message: ALWAYS
  servlet:
    context-path: /jeecg-boot
  compression:
    enabled: true
    min-response-size: 1024
    mime-types: application/javascript,application/json,application/xml,text/html,text/xml,text/plain,text/css,image/*

management:
  endpoints:
    web:
      exposure:
        include: metrics,httptrace

spring:
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB
  mail:
    host: smtp.qq.com
    username: <EMAIL>
    password: zrkvgrwjdwdwdejfae
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true
            required: true
  ## quartz定时任务,采用数据库方式
  quartz:
    job-store-type: jdbc
    initialize-schema: embedded
    #定时任务启动开关，true-开  false-关
    auto-startup: true
    #启动时更新己存在的Job
    overwrite-existing-jobs: true
    properties:
      org:
        quartz:
          scheduler:
            instanceName: MyScheduler
            instanceId: AUTO
          jobStore:
            class: org.quartz.impl.jdbcjobstore.JobStoreTX
            driverDelegateClass: org.quartz.impl.jdbcjobstore.StdJDBCDelegate
            tablePrefix: QRTZ_
            isClustered: true
            misfireThreshold: 60000
            clusterCheckinInterval: 10000
          threadPool:
            class: org.quartz.simpl.SimpleThreadPool
            threadCount: 10
            threadPriority: 5
            threadsInheritContextClassLoaderOfInitializingThread: true
  #json 时间戳统一转换
  jackson:
    date-format:   yyyy-MM-dd HH:mm:ss
    time-zone:   GMT+8
  aop:
    proxy-target-class: true
  activiti:
    check-process-definitions: false
    #启用作业执行器
    async-executor-activate: false
    #启用异步执行器
    job-executor-activate: false
  jpa:
    open-in-view: false
  #配置freemarker
  freemarker:
    # 设置模板后缀名
    suffix: .ftl
    # 设置文档类型
    content-type: text/html
    # 设置页面编码格式
    charset: UTF-8
    # 设置页面缓存
    cache: false
    prefer-file-system-access: false
    # 设置ftl文件路径
    template-loader-path:
      - classpath:/templates
  # 设置静态文件路径，js,css等
  mvc:
    static-path-pattern: /**
  resource:
    static-locations: classpath:/static/,classpath:/public/
  autoconfigure:
    exclude: com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure
  datasource:
    druid:
      stat-view-servlet:
        enabled: true
        loginUsername: admin
        loginPassword: 123456
        allow:
      web-stat-filter:
        enabled: true
    dynamic:
      druid: # 全局druid参数，绝大部分值和默认保持一致。(现已支持的参数如下,不清楚含义不要乱设置)
        # 连接池的配置信息
        # 初始化大小，最小，最大
        initial-size: 5
        min-idle: 5
        maxActive: 20
        # 配置获取连接等待超时的时间
        maxWait: 60000
        # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
        timeBetweenEvictionRunsMillis: 60000
        # 配置一个连接在池中最小生存的时间，单位是毫秒
        minEvictableIdleTimeMillis: 300000
        validationQuery: SELECT 1 FROM DUAL
        testWhileIdle: true
        testOnBorrow: false
        testOnReturn: false
        # 打开PSCache，并且指定每个连接上PSCache的大小
        poolPreparedStatements: true
        maxPoolPreparedStatementPerConnectionSize: 20
        # 配置监控统计拦截的filters，去掉后监控界面sql无法统计，'wall'用于防火墙
        filters: stat,wall,slf4j
        # 通过connectProperties属性来打开mergeSql功能；慢SQL记录
        connectionProperties: druid.stat.mergeSql\=true;druid.stat.slowSqlMillis\=5000
      datasource:
          master:
            url: *************************************************************************************************************************************************************************
            username: root
            password: root
            driver-class-name: com.mysql.cj.jdbc.Driver
          # 多数据源配置
          #multi-datasource1:
            #url: ***************************************************************************************************************************************************************************************************************************
            #username: root
            #password: root
            #driver-class-name: com.mysql.cj.jdbc.Driver
  #redis 配置
  redis:
     database: 0
     host: *************
     lettuce:
       pool:
         max-active: 8   #最大连接数据库连接数,设 -1 为没有限制
         max-idle: 8     #最大等待连接中的数量,设 0 为没有限制
         max-wait: -1ms  #最大建立连接等待时间。如果超过此时间将接到异常。设为-1表示无限制。
         min-idle: 0     #最小等待连接中的数量,设 0 为没有限制
       shutdown-timeout: 100ms
     password: ''
     port: 6379
#mybatis plus 设置
mybatis-plus:
   mapper-locations: classpath*:org/jeecg/modules/**/xml/*Mapper.xml
   global-config:
    # 关闭MP3.0自带的banner
    banner: false
    db-config:
      #主键类型
      id-type: ASSIGN_ID
      # 默认数据库表下划线命名
      table-underline: true
   configuration:
    # 这个配置会将执行的sql打印出来，在开发或测试的时候可以用
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    # 返回类型为Map,显示null对应的字段
    call-setters-on-nulls: true
#jeecg专用配置
minidao :
  base-package: org.jeecg.modules.jmreport.*
jeecg :
  # 签名密钥串(前后端要一致，正式发布请自行修改)
  signatureSecret: dd05f1c54d63749eda95f9fa6d49v442a
  # 本地：local\Minio：minio\阿里云：alioss
  uploadType: local
  path :
    #文件上传根目录 设置
    upload: D://opt//upFiles
    #webapp文件路径
    webapp: D://opt//webapp
  shiro:
     excludeUrls: /test/jeecgDemo/demo3,/test/jeecgDemo/redisDemo/**,/category/**,/visual/**,/map/**,/jmreport/bigscreen2/**,/aigc/aigcHomeCarousel/list,/aigc/websiteFeatures/list,/aigc/websiteStats/list,/api/auth/**,/api/aigc/verify-apikey,/api/aigc/xiaohongshu/**,/api/aigc/coze/**,/api/coze/video/**,/api/coze/image/**,/api/aigc/image/**,/api/aigc/html/**,/api/aigc/qrcode/**,/api/jianying/**,/api/jianyingpro/**,/plubshop/aigcPlubShop/getPluginDetail,/plubshop/aigcPlubShop/list,/sys/dict/getDictItems/**,/api/agent/market/list
  #阿里云oss存储和大鱼短信秘钥配置
  oss:
    accessKey: LTAI5tSWPfBoqXs1yKT6hMqh
    secretKey: ******************************
    endpoint: oss-cn-beijing.aliyuncs.com
    bucketName: jeecgdev
    staticDomain: https://static.jeecg.com
  # 阿里云短信服务配置
  sms:
    accessKeyId: LTAI5tSWPfBoqXs1yKT6hMqh
    accessKeySecret: ******************************
    signName: 岳阳卓软信息技术
    templateCode: SMS_490740286
  # ElasticSearch 设置
  elasticsearch:
    cluster-name: jeecg-ES
    cluster-nodes: 81.70.47.128:9200
    check-enabled: false
  # 表单设计器配置
  desform:
    # 主题颜色（仅支持 16进制颜色代码）
    theme-color: "#1890ff"
    # 文件、图片上传方式，可选项：qiniu（七牛云）、system（跟随系统配置）
    upload-type: system
    map:
      # 配置百度地图的AK，申请地址：https://lbs.baidu.com/apiconsole/key?application=key#/home
      baidu: ??
  # 在线预览文件服务器地址配置
  file-view-domain: http://127.0.0.1:8012
  # minio文件上传
  minio:
    minio_url: http://minio.jeecg.com
    minio_name: ??
    minio_pass: ??
    bucketName: ??
  #大屏报表参数设置
  jmreport:
    mode: prod
    #数据字典是否进行saas数据隔离，自己看自己的字典
    saas: false
    #是否需要校验token
    is_verify_token: false
    #必须校验方法
    verify_methods: remove,delete,save,add,update
  #Wps在线文档
  wps:
    domain: https://wwo.wps.cn/office/
    appid: ??
    appsecret: ??
  #xxl-job配置
  xxljob:
    enabled: false
    adminAddresses: http://127.0.0.1:9080/xxl-job-admin
    appname: ${spring.application.name}
    accessToken: ''
    address: 127.0.0.1:30007
    ip: 127.0.0.1
    port: 30007
    logPath: logs/jeecg/job/jobhandler/
    logRetentionDays: 30
  route:
    config:
      data-id: jeecg-gateway-router
      group: DEFAULT_GROUP
      #自定义路由配置 yml nacos database
      data-type: database
  #分布式锁配置
  redisson:
    address: 127.0.0.1:6379
    password:
    type: STANDALONE
    enabled: true
#Mybatis输出sql日志
logging:
  level:
    org.jeecg.modules.system.mapper : info
#cas单点登录
cas:
  prefixUrl: http://cas.example.org:8443/cas
#swagger
knife4j:
  #开启增强配置
  enable: true
  #开启生产环境屏蔽
  production: false
  basic:
    enable: true
    username: jeecg
    password: jeecg1314
#第三方登录
justauth:
  enabled: true
  type:
    GITHUB:
      client-id: ??
      client-secret: ??
      redirect-uri: http://sso.test.com:8080/jeecg-boot/sys/thirdLogin/github/callback
    WECHAT_ENTERPRISE:
      client-id: ??
      client-secret: ??
      redirect-uri: http://sso.test.com:8080/jeecg-boot/sys/thirdLogin/wechat_enterprise/callback
      agent-id: ??
    DINGTALK:
      client-id: ??
      client-secret: ??
      redirect-uri: http://sso.test.com:8080/jeecg-boot/sys/thirdLogin/dingtalk/callback
    WECHAT_OPEN:
      client-id: ??
      client-secret: ??
      redirect-uri: http://sso.test.com:8080/jeecg-boot/sys/thirdLogin/wechat_open/callback
  cache:
    type: default
    prefix: 'demo::'
    timeout: 1h
#第三方APP对接
third-app:
  enabled: false
  type:
    #企业微信
    WECHAT_ENTERPRISE:
      enabled: false
      #CORP_ID
      client-id: ??
      #SECRET
      client-secret: ??
      #自建应用id
      agent-id: ??
      #自建应用秘钥（新版企微需要配置）
      # agent-app-secret: ??
    #钉钉
    DINGTALK:
      enabled: false
      # appKey
      client-id: ??
      # appSecret
      client-secret: ??
      agent-id: ??

# 智界Aigc测试环境配置
aigc:
  # HTML文件存储路径
  html:
    storage:
      path: C:/aigcview/html/
  # 二维码文件存储路径
  qrcode:
    storage:
      path: C:/aigcview/qrcode/
  # 基础访问URL - 测试环境
  base:
    url: https://www.aigcview.com  # 测试环境也使用正式域名
  # API配置
  api:
    # HTML安全配置
    html-security:
      enabled: true
      max-content-size: 2097152         # 2MB
      allow-external-resources: false
    # 文件存储配置
    file-storage:
      html-path: C:/aigcview/html/
      qrcode-path: C:/aigcview/qrcode/
      retention-days: 30
      enable-cleanup: true
      max-file-count: 100000
  # 用户活跃状态追踪配置 - 测试环境
  user-activity:
    # 基础开关配置
    enabled: true                           # 测试环境启用
    test-mode: false                        # 测试环境不使用测试模式

    # 缓存配置
    cache-duration: 300                     # 缓存持续时间（秒）- 5分钟
    cache-expire-time: 600                  # Redis缓存过期时间（秒）- 10分钟
    redis-key-prefix: "aigc:test:user:activity:" # 测试环境Redis键前缀

    # 批量处理配置
    batch-size: 50                          # 测试环境使用较小批量
    batch-interval: 15                      # 批量更新间隔（秒）

    # 性能配置
    performance-threshold: 1500             # 测试环境阈值稍高
    enable-performance-monitoring: true     # 启用性能监控
    performance-monitoring-sample-rate: 20 # 测试环境更高采样率

    # 核心API列表
    critical-apis:
      - "/payment/"
      - "/login"
      - "/logout"
      - "/order/"
      - "/api/auth/"

    # 灰度发布配置
    rollout-percentage: 50                  # 测试环境50%灰度

    # 重试配置
    max-retry-count: 2                      # 测试环境减少重试次数
    retry-interval: 1500                    # 重试间隔（毫秒）

    # 用户状态管理
    offline-user-cleanup-interval: 30       # 测试环境更频繁清理
    user-offline-threshold: 10              # 测试环境更短离线阈值

    # 异步处理配置
    enable-async-processing: true           # 启用异步处理
    async-thread-pool-size: 3               # 测试环境较小线程池
    queue-capacity: 500                     # 测试环境较小队列

    # 降级机制配置
    enable-degradation: true                # 启用降级机制
    degradation-recovery-time: 180          # 测试环境更快恢复