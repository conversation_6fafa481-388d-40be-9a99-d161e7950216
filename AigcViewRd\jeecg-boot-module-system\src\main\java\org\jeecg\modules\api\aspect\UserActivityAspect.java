package org.jeecg.modules.api.aspect;

import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.api.config.UserActivityConfig;
import org.jeecg.modules.api.dto.UserActivityUpdateDTO;
import org.jeecg.modules.api.service.IUserActivityBatchUpdateService;
import org.jeecg.modules.api.service.IUserActivityCacheService;
import org.jeecg.modules.api.service.IUserActivityPerformanceMonitor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

/**
 * @Description: 用户活跃状态追踪AOP切面
 * @Author: AigcView
 * @Date: 2025-01-30
 * @Version: V1.0
 */
@Slf4j
@Aspect
@Component
public class UserActivityAspect {

    @Autowired
    private UserActivityConfig userActivityConfig;

    @Autowired
    private IUserActivityBatchUpdateService batchUpdateService;

    @Autowired
    private IUserActivityCacheService cacheService;

    @Autowired
    private IUserActivityPerformanceMonitor performanceMonitor;

    /**
     * 排除的API路径 - 这些API不需要追踪用户活跃状态
     */
    private static final Set<String> EXCLUDED_PATHS = new HashSet<>(Arrays.asList(
        "/sys/login",           // 登录API
        "/sys/logout",          // 登出API
        "/api/user/heartbeat",  // 心跳API（避免循环调用）
        "/sys/randomImage",     // 验证码API
        "/sys/checkCaptcha",    // 验证码校验API
        "/actuator/health",     // 健康检查API
        "/swagger",             // Swagger文档API
        "/doc.html",            // API文档
        "/favicon.ico",         // 图标请求
        "/druid",               // Druid监控
        "/websocket"            // WebSocket连接
    ));

    /**
     * 定义切点：拦截所有Controller方法
     * 排除系统内部调用和静态资源请求
     */
    @Pointcut("execution(public * org.jeecg.modules..*.*Controller.*(..)) && " +
              "!execution(* org.jeecg.modules.api.controller.AigcApiController.heartbeat(..))")
    public void controllerMethods() {}

    /**
     * 环绕通知：在API调用前后进行用户活跃状态追踪
     */
    @Around("controllerMethods()")
    public Object trackUserActivity(ProceedingJoinPoint joinPoint) throws Throwable {
        long startTime = System.currentTimeMillis();
        Object result = null;
        boolean success = false;
        String requestUri = null;
        boolean isCriticalApi = false;

        try {
            // 1. 快速检查：配置是否启用
            if (!userActivityConfig.getEnabled()) {
                return joinPoint.proceed();
            }

            // 2. 获取请求信息
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes == null) {
                return joinPoint.proceed();
            }

            HttpServletRequest request = attributes.getRequest();
            requestUri = request.getRequestURI();
            isCriticalApi = userActivityConfig.isCriticalApi(requestUri);

            // 3. 检查是否为排除的API路径
            if (isExcludedPath(requestUri)) {
                return joinPoint.proceed();
            }

            // 4. 检查降级状态 - 非关键API在降级时跳过追踪
            if (performanceMonitor.shouldDegradeApi(requestUri, isCriticalApi)) {
                log.debug("API降级中，跳过用户活跃状态追踪 - 路径: {}, 关键API: {}", requestUri, isCriticalApi);
                return joinPoint.proceed();
            }

            // 5. 获取当前用户信息
            LoginUser currentUser = getCurrentUser();
            if (currentUser == null) {
                // 用户未登录，不追踪活跃状态
                return joinPoint.proceed();
            }

            // 6. 检查灰度发布：是否需要为该用户追踪活跃状态
            if (!userActivityConfig.isInRollout(currentUser.getUsername())) {
                return joinPoint.proceed();
            }

            // 7. 执行原方法
            result = joinPoint.proceed();
            success = true;

            // 8. 异步更新用户活跃状态（仅在非降级状态下或关键API）
            if (!performanceMonitor.isDegraded() || isCriticalApi) {
                updateUserActivityAsync(currentUser, request, requestUri, startTime);
            }

            return result;

        } catch (Exception e) {
            success = false;
            // 用户活跃状态追踪异常不应影响主业务
            log.error("用户活跃状态追踪异常 - 路径: {}, 错误: {}", requestUri, e.getMessage(), e);
            return joinPoint.proceed();
        } finally {
            // 9. 记录性能监控数据
            long responseTime = System.currentTimeMillis() - startTime;
            if (userActivityConfig.getEnablePerformanceMonitoring() && requestUri != null) {
                performanceMonitor.recordApiPerformance(requestUri, responseTime, isCriticalApi, success);

                log.debug("用户活跃状态追踪性能 - 路径: {}, 响应时间: {}ms, 成功: {}, 关键API: {}",
                    requestUri, responseTime, success, isCriticalApi);
            }
        }
    }

    /**
     * 异步更新用户活跃状态
     */
    private void updateUserActivityAsync(LoginUser user, HttpServletRequest request,
                                       String requestUri, long startTime) {
        try {
            // 1. 收集用户活跃信息
            String userId = user.getId();
            String sessionId = getSessionId(request);
            String ipAddress = getClientIpAddress(request);
            String userAgent = request.getHeader("User-Agent");

            // 2. 判断API类型
            boolean isCriticalApi = userActivityConfig.isCriticalApi(requestUri);

            // 3. 缓存用户基本信息（如果缓存中不存在）
            cacheUserInfoIfNeeded(user);

            // 4. 缓存会话信息
            cacheSessionInfoIfNeeded(sessionId, userId, request);

            // 5. 创建更新DTO
            UserActivityUpdateDTO updateDTO = UserActivityUpdateDTO.createActivityUpdate(
                userId, sessionId, requestUri, ipAddress, userAgent
            );

            // 6. 设置API相关信息
            updateDTO.setIsCriticalApi(isCriticalApi);

            // 7. 提交到批量更新服务
            batchUpdateService.addToUpdateQueue(updateDTO);
            
            // 6. 记录调试日志
            if (userActivityConfig.getTestMode()) {
                log.debug("用户活跃状态追踪 - 用户: {}, API: {}, 核心API: {}, 响应时间: {}ms", 
                    user.getUsername(), requestUri, isCriticalApi, 
                    System.currentTimeMillis() - startTime);
            }
            
        } catch (Exception e) {
            log.error("异步更新用户活跃状态失败 - 用户: {}, API: {}, 错误: {}", 
                user.getUsername(), requestUri, e.getMessage(), e);
        }
    }

    /**
     * 获取当前登录用户
     */
    private LoginUser getCurrentUser() {
        try {
            Object principal = SecurityUtils.getSubject().getPrincipal();
            if (principal instanceof LoginUser) {
                return (LoginUser) principal;
            }
        } catch (Exception e) {
            log.debug("获取当前用户失败: {}", e.getMessage());
        }
        return null;
    }

    /**
     * 获取会话ID
     */
    private String getSessionId(HttpServletRequest request) {
        try {
            HttpSession session = request.getSession(false);
            return session != null ? session.getId() : "unknown";
        } catch (Exception e) {
            log.debug("获取会话ID失败: {}", e.getMessage());
            return "unknown";
        }
    }

    /**
     * 获取客户端真实IP地址
     */
    private String getClientIpAddress(HttpServletRequest request) {
        try {
            return oConvertUtils.getIpAddrByRequest(request);
        } catch (Exception e) {
            log.debug("获取客户端IP失败: {}", e.getMessage());
            return "unknown";
        }
    }

    /**
     * 检查是否为排除的API路径
     */
    private boolean isExcludedPath(String requestUri) {
        if (requestUri == null) {
            return true;
        }
        
        // 检查完全匹配
        if (EXCLUDED_PATHS.contains(requestUri)) {
            return true;
        }
        
        // 检查前缀匹配
        for (String excludedPath : EXCLUDED_PATHS) {
            if (requestUri.startsWith(excludedPath)) {
                return true;
            }
        }
        
        // 排除静态资源
        if (requestUri.contains(".") && 
            (requestUri.endsWith(".js") || requestUri.endsWith(".css") || 
             requestUri.endsWith(".png") || requestUri.endsWith(".jpg") || 
             requestUri.endsWith(".ico") || requestUri.endsWith(".gif"))) {
            return true;
        }
        
        return false;
    }



    // ==================== 缓存相关辅助方法 ====================

    /**
     * 缓存用户基本信息（如果缓存中不存在）
     */
    private void cacheUserInfoIfNeeded(LoginUser user) {
        try {
            String userId = user.getId();

            // 检查缓存中是否已存在用户信息
            if (cacheService.getCachedUserInfo(userId) == null) {
                // 构建用户信息映射
                java.util.Map<String, Object> userInfo = new java.util.HashMap<>();
                userInfo.put("userId", userId);
                userInfo.put("username", user.getUsername());
                userInfo.put("realname", user.getRealname());
                userInfo.put("avatar", user.getAvatar());
                userInfo.put("orgCode", user.getOrgCode());
                userInfo.put("telephone", user.getPhone());
                userInfo.put("email", user.getEmail());
                userInfo.put("cacheTime", System.currentTimeMillis());

                // 缓存用户信息
                cacheService.cacheUserInfo(userId, userInfo);
                log.debug("缓存用户基本信息 - 用户ID: {}, 用户名: {}", userId, user.getUsername());
            }

        } catch (Exception e) {
            // 缓存失败不影响主业务
            log.error("缓存用户基本信息失败 - 用户ID: {}, 错误: {}", user.getId(), e.getMessage());
        }
    }

    /**
     * 缓存会话信息（如果缓存中不存在）
     */
    private void cacheSessionInfoIfNeeded(String sessionId, String userId, HttpServletRequest request) {
        try {
            if (sessionId != null && userId != null) {
                // 检查缓存中是否已存在会话信息
                if (cacheService.getCachedSession(sessionId) == null) {
                    // 构建会话信息映射
                    java.util.Map<String, Object> sessionInfo = new java.util.HashMap<>();
                    sessionInfo.put("sessionId", sessionId);
                    sessionInfo.put("userId", userId);
                    sessionInfo.put("ipAddress", getClientIpAddress(request));
                    sessionInfo.put("userAgent", request.getHeader("User-Agent"));
                    sessionInfo.put("createTime", System.currentTimeMillis());
                    sessionInfo.put("lastAccessTime", System.currentTimeMillis());

                    // 缓存会话信息
                    cacheService.cacheUserSession(sessionId, userId, sessionInfo);
                    log.debug("缓存会话信息 - 会话ID: {}, 用户ID: {}", sessionId, userId);
                }
            }

        } catch (Exception e) {
            // 缓存失败不影响主业务
            log.error("缓存会话信息失败 - 会话ID: {}, 用户ID: {}, 错误: {}", sessionId, userId, e.getMessage());
        }
    }
}
