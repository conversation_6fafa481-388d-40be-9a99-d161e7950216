package org.jeecg.modules.demo.plubshop.service;

import org.jeecg.modules.demo.plubshop.entity.AigcPlubShop;
import com.baomidou.mybatisplus.extension.service.IService;
import java.math.BigDecimal;

/**
 * @Description: 插件商城
 * @Author: jeecg-boot
 * @Date:   2025-06-14
 * @Version: V1.0
 */
public interface IAigcPlubShopService extends IService<AigcPlubShop> {

    /**
     * 根据插件名称查询插件信息
     * @param pluginName 插件名称
     * @return 插件信息
     */
    AigcPlubShop getByPluginName(String pluginName);

    /**
     * 根据插件唯一标识查询插件信息
     * @param pluginKey 插件唯一标识
     * @return 插件信息
     */
    AigcPlubShop getByPluginKey(String pluginKey);

    /**
     * 更新插件收益和调用次数
     * @param pluginId 插件ID
     * @param amount 收益金额
     * @return 是否成功
     */
    boolean updatePluginStats(String pluginId, BigDecimal amount);

}
