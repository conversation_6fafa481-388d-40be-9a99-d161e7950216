-- ================================
-- 工作流表添加输入参数说明字段
-- 执行时间：2025-08-06
-- 功能：为 aigc_workflow 表添加 input_params_desc 字段
-- ================================

-- 检查字段是否已存在
SELECT '开始检查 input_params_desc 字段是否存在...' as message;

SET @column_exists = (
    SELECT COUNT(*) 
    FROM information_schema.COLUMNS 
    WHERE TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME = 'aigc_workflow' 
    AND COLUMN_NAME = 'input_params_desc'
);

-- 如果字段不存在，则添加字段
SET @sql = IF(@column_exists = 0, 
    'ALTER TABLE aigc_workflow ADD COLUMN input_params_desc TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT ''输入参数说明'' AFTER workflow_description;',
    'SELECT "input_params_desc字段已存在，跳过添加" as message;'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 验证字段添加结果
SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN '✅ input_params_desc 字段已成功添加或已存在'
        ELSE '❌ input_params_desc 字段添加失败'
    END as result
FROM information_schema.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'aigc_workflow' 
AND COLUMN_NAME = 'input_params_desc';

-- 显示表结构确认
SELECT '当前 aigc_workflow 表结构：' as message;
DESCRIBE aigc_workflow;

-- 执行完成提示
SELECT 
    '数据库升级完成！' as message,
    '工作流表已添加输入参数说明字段' as note,
    '现在可以在创建工作流时填写输入参数说明了' as next_step;
