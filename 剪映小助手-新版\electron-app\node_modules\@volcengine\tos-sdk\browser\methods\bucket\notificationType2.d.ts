import TOSBase from '../base';
export interface NotificationFilter {
    TOSKey?: {
        FilterRules: {
            Name: string;
            Value: string;
        }[];
    };
}
export interface DestinationRocketMQ {
    Role: string;
    InstanceId: string;
    Topic: string;
    AccessKeyId: string;
}
export interface NotificationDestination {
    RocketMQ?: DestinationRocketMQ[];
    VeFaaS?: {
        FunctionId: string;
    }[];
}
export interface NotificationRule {
    RuleId: string;
    Events: string[];
    Filter?: NotificationFilter;
    Destination: NotificationDestination;
}
export interface PutBucketNotificationInput {
    bucket: string;
    Rules: NotificationRule[];
    Version?: string;
}
export interface PutBucketNotificationOutput {
}
export declare function putBucketNotificationType2(this: TOSBase, input: PutBucketNotificationInput): Promise<import("../base").TosResponse<PutBucketNotificationOutput>>;
export interface GetBucketNotificationInput {
    bucket: string;
}
export interface GetBucketNotificationOutput {
    Rules: NotificationRule[];
    Version?: string;
}
export declare function getBucketNotificationType2(this: TOSBase, input: GetBucketNotificationInput): Promise<import("../base").TosResponse<GetBucketNotificationOutput>>;
