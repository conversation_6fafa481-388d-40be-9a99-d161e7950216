package org.jeecg.modules.demo.exchangecode.controller;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.io.IOException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.demo.exchangecode.entity.AicgExchangeCode;
import org.jeecg.modules.demo.exchangecode.service.IAicgExchangeCodeService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.vo.LoginUser;
import org.apache.shiro.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * @Description: 兑换码表
 * @Author: jeecg-boot
 * @Date:   2025-06-14
 * @Version: V1.0
 */
@Api(tags="兑换码表")
@RestController
@RequestMapping("/demo/exchangecode")
@Slf4j
public class AicgExchangeCodeController extends JeecgController<AicgExchangeCode, IAicgExchangeCodeService> {
	@Autowired
	private IAicgExchangeCodeService aicgExchangeCodeService;
	
	/**
	 * 分页列表查询
	 *
	 * @param aicgExchangeCode
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	@AutoLog(value = "兑换码表-分页列表查询")
	@ApiOperation(value="兑换码表-分页列表查询", notes="兑换码表-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(AicgExchangeCode aicgExchangeCode,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<AicgExchangeCode> queryWrapper = QueryGenerator.initQueryWrapper(aicgExchangeCode, req.getParameterMap());
		Page<AicgExchangeCode> page = new Page<AicgExchangeCode>(pageNo, pageSize);
		IPage<AicgExchangeCode> pageList = aicgExchangeCodeService.page(page, queryWrapper);
		return Result.ok(pageList);
	}
	
	/**
	 * 生成兑换码
	 *
	 * @param codeType 兑换码类型：1-余额，2-会员，3-积分
	 * @param value 兑换价值
	 * @param expireTime 过期时间
	 * @param batchNo 批次号
	 * @param count 生成数量
	 * @return
	 */
	@AutoLog(value = "生成兑换码")
	@ApiOperation(value="生成兑换码", notes="生成兑换码")
	@PostMapping(value = "/generate")
	public Result<?> generateExchangeCodes(@RequestParam Integer codeType,
										  @RequestParam BigDecimal value,
										  @RequestParam @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss") Date expireTime,
										  @RequestParam(required = false) String batchNo,
										  @RequestParam(defaultValue = "1") Integer count) {
		LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
		if (oConvertUtils.isEmpty(batchNo)) {
			batchNo = "BATCH_" + System.currentTimeMillis();
		}
		
		List<AicgExchangeCode> codes = aicgExchangeCodeService.generateExchangeCodes(codeType, value, expireTime, batchNo, count, sysUser.getId());
		return Result.OK("兑换码生成成功", codes);
	}
	
	/**
	 * 使用兑换码
	 *
	 * @param code 兑换码
	 * @return
	 */
	@AutoLog(value = "使用兑换码")
	@ApiOperation(value="使用兑换码", notes="使用兑换码")
	@PostMapping(value = "/use")
	public Result<?> useExchangeCode(@RequestParam String code) {
		LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
		String result = aicgExchangeCodeService.useExchangeCode(code, sysUser.getId(), sysUser.getId());
		
		if (result.contains("成功")) {
			return Result.OK(result);
		} else {
			return Result.error(result);
		}
	}
	
	/**
	 * 验证兑换码
	 *
	 * @param code 兑换码
	 * @return
	 */
	@AutoLog(value = "验证兑换码")
	@ApiOperation(value="验证兑换码", notes="验证兑换码")
	@GetMapping(value = "/validate")
	public Result<?> validateExchangeCode(@RequestParam String code) {
		String result = aicgExchangeCodeService.validateExchangeCode(code);
		
		if ("valid".equals(result)) {
			AicgExchangeCode exchangeCode = aicgExchangeCodeService.getByCode(code);
			return Result.OK("兑换码有效", exchangeCode);
		} else {
			return Result.error(result);
		}
	}
	
	/**
	 * 获取用户使用的兑换码记录
	 *
	 * @return
	 */
	@AutoLog(value = "获取用户使用的兑换码记录")
	@ApiOperation(value="获取用户使用的兑换码记录", notes="获取用户使用的兑换码记录")
	@GetMapping(value = "/userUsedCodes")
	public Result<?> getUserUsedCodes() {
		LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
		List<AicgExchangeCode> codes = aicgExchangeCodeService.getUserUsedCodes(sysUser.getId());
		return Result.OK(codes);
	}
	
	/**
	 * 更新过期兑换码状态
	 *
	 * @return
	 */
	@AutoLog(value = "更新过期兑换码状态")
	@ApiOperation(value="更新过期兑换码状态", notes="更新过期兑换码状态")
	@PostMapping(value = "/updateExpired")
	public Result<?> updateExpiredCodes() {
		int count = aicgExchangeCodeService.updateExpiredCodes();
		return Result.OK("更新了 " + count + " 个过期兑换码");
	}
	
	/**
	 * 添加
	 *
	 * @param aicgExchangeCode
	 * @return
	 */
	@AutoLog(value = "兑换码表-添加")
	@ApiOperation(value="兑换码表-添加", notes="兑换码表-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody AicgExchangeCode aicgExchangeCode) {
		aicgExchangeCodeService.save(aicgExchangeCode);
		return Result.OK("添加成功！");
	}
	
	/**
	 * 编辑
	 *
	 * @param aicgExchangeCode
	 * @return
	 */
	@AutoLog(value = "兑换码表-编辑")
	@ApiOperation(value="兑换码表-编辑", notes="兑换码表-编辑")
	@PutMapping(value = "/edit")
	public Result<?> edit(@RequestBody AicgExchangeCode aicgExchangeCode) {
		aicgExchangeCodeService.updateById(aicgExchangeCode);
		return Result.OK("编辑成功!");
	}
	
	/**
	 * 通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "兑换码表-通过id删除")
	@ApiOperation(value="兑换码表-通过id删除", notes="兑换码表-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		aicgExchangeCodeService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 * 批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "兑换码表-批量删除")
	@ApiOperation(value="兑换码表-批量删除", notes="兑换码表-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.aicgExchangeCodeService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "兑换码表-通过id查询")
	@ApiOperation(value="兑换码表-通过id查询", notes="兑换码表-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		AicgExchangeCode aicgExchangeCode = aicgExchangeCodeService.getById(id);
		if(aicgExchangeCode==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(aicgExchangeCode);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param aicgExchangeCode
    */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, AicgExchangeCode aicgExchangeCode) {
        return super.exportXls(request, aicgExchangeCode, AicgExchangeCode.class, "兑换码表");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, AicgExchangeCode.class);
    }

}
