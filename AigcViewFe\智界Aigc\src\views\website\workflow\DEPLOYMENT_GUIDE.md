# 创作者中心部署指南

## 📋 部署清单

### 后端部署文件
```
AigcViewRd/jeecg-boot-module-system/src/main/java/org/jeecg/modules/api/
├── controller/
│   ├── CreatorAgentController.java      # 智能体管理控制器
│   └── CreatorWorkflowController.java   # 工作流管理控制器
├── dto/
│   └── CreatorAgentDTO.java            # 数据传输对象
└── vo/
    └── CreatorRevenueStatsVO.java      # 收益统计视图对象
```

### 前端部署文件
```
AigcViewFe/智界Aigc/src/views/website/workflow/
├── WorkflowCenter.vue                  # 主页面（已更新）
├── components/
│   ├── CreatorCenter.vue              # 创作者中心主组件
│   ├── AgentManagement.vue            # 智能体管理组件
│   ├── CreatorAgentForm.vue           # 智能体表单组件
│   ├── WorkflowManagement.vue         # 工作流管理组件
│   └── RevenueStats.vue               # 收益统计组件
├── route.js                           # 路由配置
├── CREATOR_CENTER_TEST.md             # 测试指南
└── DEPLOYMENT_GUIDE.md                # 部署指南

AigcViewFe/智界Aigc/src/api/
├── creator-agent.js                   # 智能体API
├── creator-workflow.js                # 工作流API
└── common.js                          # 通用API
```

## 🚀 部署步骤

### 1. 后端部署

#### 1.1 代码部署
```bash
# 1. 确保所有后端文件已添加到项目中
# 2. 检查import语句是否正确
# 3. 确保数据库表结构已更新

# 编译项目
cd AigcViewRd
mvn clean compile

# 运行测试
mvn test

# 打包项目
mvn clean package -DskipTests
```

#### 1.2 数据库配置
```sql
-- 确保以下表存在并有正确的字段：
-- aigc_agent (智能体表)
-- aigc_workflow (工作流表)  
-- aicg_user_transaction (用户交易表)

-- 检查表结构
DESCRIBE aigc_agent;
DESCRIBE aigc_workflow;
DESCRIBE aicg_user_transaction;
```

#### 1.3 权限配置
在 `ShiroConfig.java` 中确保以下接口已配置权限：
```java
// 创作者中心API需要登录验证
filterChainDefinitionMap.put("/api/creator/**", "jwt");
```

### 2. 前端部署

#### 2.1 依赖安装
```bash
cd AigcViewFe/智界Aigc
npm install
```

#### 2.2 代码检查
```bash
# 检查语法错误
npm run lint

# 修复可自动修复的问题
npm run lint:fix
```

#### 2.3 构建测试
```bash
# 开发环境测试
npm run serve

# 生产环境构建
npm run build
```

### 3. 配置验证

#### 3.1 路由配置检查
确保 `router.config.js` 中包含工作流中心路由：
```javascript
{
  path: '/workflow-center',
  name: 'WorkflowCenter',
  component: () => import('@/views/website/workflow/WorkflowCenter.vue'),
  meta: {
    title: '工作流中心 - 智界AIGC',
    description: '智能体市场和创作者中心，发现和使用优质AI智能体'
  }
}
```

#### 3.2 API配置检查
确保API基础路径配置正确：
```javascript
// creator-agent.js
const API_BASE = '/api/creator/agent'

// creator-workflow.js  
const API_BASE = '/api/creator/workflow'
```

### 4. 环境配置

#### 4.1 开发环境
```bash
# 后端
cd AigcViewRd
mvn spring-boot:run

# 前端
cd AigcViewFe/智界Aigc
npm run serve
```

#### 4.2 生产环境
```bash
# 后端 - 使用生产配置
java -jar -Dspring.profiles.active=prod target/jeecg-boot-module-system-3.7.1.jar

# 前端 - 构建生产版本
npm run build
# 将dist目录部署到Web服务器
```

### 5. 功能验证

#### 5.1 基础功能验证
```bash
# 1. 访问工作流中心
curl http://localhost:3000/workflow-center

# 2. 测试API接口（需要登录Token）
curl -H "Authorization: Bearer YOUR_TOKEN" \
     http://localhost:8080/jeecg-boot/api/creator/agent/list

# 3. 测试文件上传
curl -X POST \
     -H "Authorization: Bearer YOUR_TOKEN" \
     -F "file=@test.zip" \
     -F "agentId=test123" \
     http://localhost:8080/jeecg-boot/api/creator/workflow/upload
```

#### 5.2 权限验证
```bash
# 测试未登录访问（应该返回401）
curl http://localhost:8080/jeecg-boot/api/creator/agent/list

# 测试跨用户访问（应该返回403或空数据）
curl -H "Authorization: Bearer OTHER_USER_TOKEN" \
     http://localhost:8080/jeecg-boot/api/creator/agent/list
```

## 🔧 配置说明

### 1. 文件上传配置

#### 1.1 TOS存储配置
确保 `application.yml` 中TOS配置正确：
```yaml
volcengine:
  tos:
    general:
      bucket: aigcview-tos
      base-path: uploads
    signed-url:
      expire-hours: 24
```

#### 1.2 文件大小限制
```yaml
spring:
  servlet:
    multipart:
      max-file-size: 50MB
      max-request-size: 50MB
```

### 2. 数据库配置

#### 2.1 连接池配置
```yaml
spring:
  datasource:
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
```

#### 2.2 事务配置
确保事务管理器配置正确，支持跨表操作。

### 3. 缓存配置

#### 3.1 Redis配置（可选）
```yaml
spring:
  redis:
    host: localhost
    port: 6379
    timeout: 3000
    jedis:
      pool:
        max-active: 20
        max-idle: 10
        min-idle: 5
```

## 🚨 注意事项

### 1. 安全考虑
- 确保所有API都有适当的权限验证
- 文件上传需要类型和大小验证
- 敏感数据需要加密存储

### 2. 性能考虑
- 大文件上传需要进度显示
- 列表数据需要分页处理
- 图片需要压缩和缓存

### 3. 兼容性考虑
- 确保前端组件在主流浏览器中正常工作
- API响应格式需要保持一致
- 错误处理需要用户友好

### 4. 监控和日志
- 添加关键操作的日志记录
- 监控API响应时间和错误率
- 设置文件上传和下载的监控

## 📊 性能优化建议

### 1. 前端优化
- 使用懒加载减少初始加载时间
- 图片使用WebP格式和CDN
- 启用Gzip压缩

### 2. 后端优化
- 数据库查询优化和索引
- 使用缓存减少数据库压力
- API响应数据精简

### 3. 网络优化
- 启用HTTP/2
- 使用CDN加速静态资源
- 合理设置缓存策略

## 🔍 故障排查

### 1. 常见问题
- 页面空白：检查路由配置和组件引入
- API调用失败：检查网络和权限配置
- 文件上传失败：检查文件大小和类型限制

### 2. 日志查看
```bash
# 后端日志
tail -f logs/jeecgboot.log

# 前端控制台
# 打开浏览器开发者工具查看Console和Network
```

### 3. 数据库检查
```sql
-- 检查数据完整性
SELECT COUNT(*) FROM aigc_agent WHERE create_by = 'username';
SELECT COUNT(*) FROM aigc_workflow WHERE agent_id = 'agent_id';
```

## ✅ 部署检查清单

- [ ] 后端代码编译无错误
- [ ] 前端代码构建无错误
- [ ] 数据库表结构正确
- [ ] API接口权限配置正确
- [ ] 文件上传功能正常
- [ ] 路由配置正确
- [ ] 页面访问正常
- [ ] 权限验证正常
- [ ] 错误处理正常
- [ ] 性能表现良好
