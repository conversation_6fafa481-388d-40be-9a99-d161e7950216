<template>
  <div class="heartbeat-test-page">
    <a-card title="🔄 心跳API测试页面" class="test-card">
      <div class="test-controls">
        <a-space>
          <a-button type="primary" @click="runBasicTest" :loading="testing.basic">
            基础心跳测试
          </a-button>
          <a-button type="primary" @click="runSmartTest" :loading="testing.smart">
            智能心跳测试
          </a-button>
          <a-button type="primary" @click="runBatchTest" :loading="testing.batch">
            批量操作测试
          </a-button>
          <a-button type="primary" @click="runFullSuite" :loading="testing.full">
            完整测试套件
          </a-button>
          <a-button @click="clearResults">清空结果</a-button>
        </a-space>
      </div>

      <!-- 测试结果展示 -->
      <div class="test-results" v-if="testResults.length > 0">
        <a-divider>测试结果</a-divider>
        <div class="result-item" v-for="(result, index) in testResults" :key="index">
          <a-alert
            :type="result.success ? 'success' : 'error'"
            :message="result.title"
            :description="result.description"
            show-icon
            closable
          />
          <div class="result-details" v-if="result.details">
            <pre>{{ JSON.stringify(result.details, null, 2) }}</pre>
          </div>
        </div>
      </div>

      <!-- 性能监控 -->
      <div class="performance-monitor" v-if="performanceData">
        <a-divider>性能监控</a-divider>
        <a-row :gutter="16">
          <a-col :span="6">
            <a-statistic title="总测试数" :value="performanceData.totalTests" />
          </a-col>
          <a-col :span="6">
            <a-statistic title="成功率" :value="performanceData.successRate" suffix="%" />
          </a-col>
          <a-col :span="6">
            <a-statistic title="平均响应时间" :value="performanceData.avgResponseTime" suffix="ms" />
          </a-col>
          <a-col :span="6">
            <a-statistic title="最大响应时间" :value="performanceData.maxResponseTime" suffix="ms" />
          </a-col>
        </a-row>
      </div>

      <!-- 心跳状态监控 -->
      <div class="heartbeat-status" v-if="heartbeatState">
        <a-divider>心跳状态监控</a-divider>
        <a-descriptions bordered :column="2">
          <a-descriptions-item label="心跳状态">
            <a-tag :color="heartbeatState.isActive ? 'green' : 'red'">
              {{ heartbeatState.isActive ? '活跃' : '停止' }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="网络状态">
            <a-tag :color="heartbeatState.isOnline ? 'green' : 'red'">
              {{ heartbeatState.isOnline ? '在线' : '离线' }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="总发送次数">
            {{ heartbeatStats.totalSent }}
          </a-descriptions-item>
          <a-descriptions-item label="成功次数">
            {{ heartbeatStats.totalSuccess }}
          </a-descriptions-item>
          <a-descriptions-item label="失败次数">
            {{ heartbeatStats.totalFailure }}
          </a-descriptions-item>
          <a-descriptions-item label="平均响应时间">
            {{ heartbeatStats.avgResponseTime }}ms
          </a-descriptions-item>
        </a-descriptions>
      </div>

      <!-- API配置展示 -->
      <div class="api-config">
        <a-divider>API配置信息</a-divider>
        <a-collapse>
          <a-collapse-panel key="1" header="当前页面配置">
            <pre>{{ JSON.stringify(currentConfig, null, 2) }}</pre>
          </a-collapse-panel>
          <a-collapse-panel key="2" header="环境配置">
            <pre>{{ JSON.stringify(envConfig, null, 2) }}</pre>
          </a-collapse-panel>
        </a-collapse>
      </div>
    </a-card>
  </div>
</template>

<script>
import { HeartbeatMixin } from '@/mixins/HeartbeatMixin'
import { HeartbeatApi } from '@/api/heartbeat'
import { getApiConfig, getEnvironmentConfig, getApiKey } from '@/api/heartbeatConfig'
import { HeartbeatTester, quickTest, testSingleApi } from '@/api/heartbeatTester'

export default {
  name: 'HeartbeatTest',
  mixins: [HeartbeatMixin],
  
  data() {
    return {
      // 心跳配置 - 测试页面使用高频心跳
      heartbeatConfig: {
        enabled: true,
        interval: 10000,  // 10秒测试间隔
        apiKey: getApiKey('admin'),
        pageType: 'test',
        enableRetry: true,
        maxRetries: 3,
        enableCache: false, // 测试时禁用缓存
        enableDebugLog: true
      },
      
      // 测试状态
      testing: {
        basic: false,
        smart: false,
        batch: false,
        full: false
      },
      
      // 测试结果
      testResults: [],
      
      // 性能数据
      performanceData: null,
      
      // 配置信息
      currentConfig: null,
      envConfig: null,
      
      // 测试器实例
      tester: null
    }
  },
  
  created() {
    this.initializeTest()
  },
  
  methods: {
    /**
     * 初始化测试环境
     */
    initializeTest() {
      // 获取配置信息
      this.currentConfig = getApiConfig('test', this.heartbeatConfig)
      this.envConfig = getEnvironmentConfig()
      
      // 创建测试器实例
      this.tester = new HeartbeatTester({
        enableLog: true,
        enablePerformanceTest: true
      })
      
      this.addTestResult({
        success: true,
        title: '测试环境初始化成功',
        description: '心跳测试页面已准备就绪，可以开始测试'
      })
    },
    
    /**
     * 运行基础心跳测试
     */
    async runBasicTest() {
      this.testing.basic = true
      
      try {
        this.addTestResult({
          success: true,
          title: '开始基础心跳测试',
          description: '测试基础心跳发送功能...'
        })
        
        const result = await this.tester.testBasicHeartbeat('test')
        
        this.addTestResult({
          success: result.success,
          title: `基础心跳测试${result.success ? '成功' : '失败'}`,
          description: `响应时间: ${result.responseTime}ms`,
          details: result.response || result.error
        })
        
        this.updatePerformanceData()
        
      } catch (error) {
        this.addTestResult({
          success: false,
          title: '基础心跳测试异常',
          description: error.message,
          details: error
        })
      } finally {
        this.testing.basic = false
      }
    },
    
    /**
     * 运行智能心跳测试
     */
    async runSmartTest() {
      this.testing.smart = true
      
      try {
        this.addTestResult({
          success: true,
          title: '开始智能心跳测试',
          description: '测试智能心跳（带重试和缓存）功能...'
        })
        
        const result = await this.tester.testSmartHeartbeat('test')
        
        this.addTestResult({
          success: result.success,
          title: `智能心跳测试${result.success ? '成功' : '失败'}`,
          description: `响应时间: ${result.responseTime}ms`,
          details: result.response || result.error
        })
        
        this.updatePerformanceData()
        
      } catch (error) {
        this.addTestResult({
          success: false,
          title: '智能心跳测试异常',
          description: error.message,
          details: error
        })
      } finally {
        this.testing.smart = false
      }
    },
    
    /**
     * 运行批量操作测试
     */
    async runBatchTest() {
      this.testing.batch = true
      
      try {
        this.addTestResult({
          success: true,
          title: '开始批量操作测试',
          description: '测试批量用户状态查询功能...'
        })
        
        const result = await this.tester.testBatchOperations()
        
        this.addTestResult({
          success: result.success,
          title: `批量操作测试${result.success ? '成功' : '失败'}`,
          description: `响应时间: ${result.responseTime}ms`,
          details: result.response || result.error
        })
        
        this.updatePerformanceData()
        
      } catch (error) {
        this.addTestResult({
          success: false,
          title: '批量操作测试异常',
          description: error.message,
          details: error
        })
      } finally {
        this.testing.batch = false
      }
    },
    
    /**
     * 运行完整测试套件
     */
    async runFullSuite() {
      this.testing.full = true
      
      try {
        this.addTestResult({
          success: true,
          title: '开始完整测试套件',
          description: '运行所有测试项目...'
        })
        
        const result = await this.tester.runFullTestSuite()
        
        this.addTestResult({
          success: result.success,
          title: `完整测试套件${result.success ? '成功' : '失败'}`,
          description: `总时间: ${result.totalTime}ms, 成功: ${result.successCount}/${result.totalCount}`,
          details: result.results
        })
        
        // 更新性能数据
        this.performanceData = {
          totalTests: result.performanceMetrics.totalTests,
          successRate: result.performanceMetrics.totalTests > 0 
            ? ((result.performanceMetrics.successCount / result.performanceMetrics.totalTests) * 100).toFixed(2)
            : 0,
          avgResponseTime: Math.round(result.performanceMetrics.averageResponseTime),
          maxResponseTime: Math.round(result.performanceMetrics.maxResponseTime)
        }
        
      } catch (error) {
        this.addTestResult({
          success: false,
          title: '完整测试套件异常',
          description: error.message,
          details: error
        })
      } finally {
        this.testing.full = false
      }
    },
    
    /**
     * 添加测试结果
     */
    addTestResult(result) {
      this.testResults.unshift({
        ...result,
        timestamp: new Date().toLocaleTimeString()
      })
      
      // 限制结果数量
      if (this.testResults.length > 20) {
        this.testResults = this.testResults.slice(0, 20)
      }
    },
    
    /**
     * 更新性能数据
     */
    updatePerformanceData() {
      if (this.tester) {
        const metrics = this.tester.performanceMetrics
        this.performanceData = {
          totalTests: metrics.totalTests,
          successRate: metrics.totalTests > 0 
            ? ((metrics.successCount / metrics.totalTests) * 100).toFixed(2)
            : 0,
          avgResponseTime: Math.round(metrics.averageResponseTime),
          maxResponseTime: Math.round(metrics.maxResponseTime)
        }
      }
    },
    
    /**
     * 清空测试结果
     */
    clearResults() {
      this.testResults = []
      this.performanceData = null
      if (this.tester) {
        this.tester.cleanup()
      }
      
      this.addTestResult({
        success: true,
        title: '测试结果已清空',
        description: '可以开始新的测试'
      })
    }
  }
}
</script>

<style scoped>
.heartbeat-test-page {
  padding: 20px;
}

.test-card {
  max-width: 1200px;
  margin: 0 auto;
}

.test-controls {
  margin-bottom: 20px;
}

.test-results {
  margin-top: 20px;
}

.result-item {
  margin-bottom: 16px;
}

.result-details {
  margin-top: 8px;
  background: #f5f5f5;
  padding: 12px;
  border-radius: 4px;
  font-size: 12px;
  max-height: 200px;
  overflow-y: auto;
}

.performance-monitor {
  margin-top: 20px;
}

.heartbeat-status {
  margin-top: 20px;
}

.api-config {
  margin-top: 20px;
}

pre {
  font-size: 12px;
  line-height: 1.4;
  max-height: 300px;
  overflow-y: auto;
}
</style>
