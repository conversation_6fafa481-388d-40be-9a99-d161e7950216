{"openapi": "3.0.0", "info": {"title": "剪映小助手_智界数据箱 - 图片数据生成器", "description": "根据时间线制作图片数据", "version": "1.0.0"}, "servers": [{"url": "https://www.aigcview.com"}], "paths": {"/jeecg-boot/api/jianying/imgs_infos": {"post": {"summary": "生成图片数据", "description": "根据时间线制作图片数据", "operationId": "imgs_infos", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"access_key": {"type": "string", "description": "访问密钥（必填）", "example": "JianyingAPI_2025_SecureAccess_AigcView", "default": "JianyingAPI_2025_SecureAccess_AigcView"}, "out_animation_duration": {"type": "integer", "description": "对应剪映的出场动画时长（可选）", "example": 1000000}, "timelines": {"type": "array", "description": "时间节点，只接收结构：[{\"start\":0,\"end\":4612}]，一般从audio_timelines节点的输出获取", "items": {"type": "object", "properties": {"start": {"type": "integer", "description": "开始时间"}, "end": {"type": "integer", "description": "结束时间"}}, "required": ["start", "end"]}, "example": [{"start": 0, "end": 4612}]}, "transition": {"type": "string", "description": "对应剪映的转场动画名字（可选），比如：水墨", "example": "淡入淡出"}, "height": {"type": "integer", "description": "图片高度", "example": 1080}, "imgs": {"type": "array", "description": "图片列表，格式[\"https://a.png\",\"https://a.png\"]", "items": {"type": "string", "description": "图片列表"}, "example": ["https://a.png", "https://a.png"]}, "group_animation": {"type": "string", "description": "对应剪映的组合动画名字（可选），多个动画请用英文|分割，比如：旋转降落|缩放", "example": "动画1|动画2"}, "out_animation": {"type": "string", "description": "对应剪映的出场动画名字（可选），多个动画请用英文|分割，比如：旋转|脉冲", "example": "动画1|动画2"}, "width": {"type": "integer", "description": "图片宽度", "example": 1920}, "in_animation": {"type": "string", "description": "对应剪映的入场动画名字（可选），多个动画请用英文|分割，比如：渐显|放大", "example": "动画1|动画2"}, "in_animation_duration": {"type": "integer", "description": "对应剪映的入场动画时长（可选）", "example": 1000000}, "group_animation_duration": {"type": "integer", "description": "对应剪映的组合动画时长（可选）", "example": 2000000}, "transition_duration": {"type": "integer", "description": "对应剪映的转场动画时长 ，范围是500000-2500000（可选）", "minimum": 500000, "maximum": 2500000, "example": 1500000}}, "required": ["access_key", "timelines", "imgs"], "additionalProperties": false}}}}, "responses": {"200": {"description": "成功生成图片数据", "content": {"application/json": {"schema": {"type": "object", "properties": {"infos": {"type": "string", "description": "图片信息数组（JSON格式字符串），格式：[{\"image_url\":\"...\",\"width\":1024,\"height\":1024,\"start\":0,\"end\":1920000}]"}}, "required": ["infos"]}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "description": "操作是否成功", "example": false}, "error": {"type": "string", "description": "错误信息"}, "code": {"type": "string", "description": "错误代码"}}, "required": ["success", "error"]}}}}, "500": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "description": "操作是否成功", "example": false}, "error": {"type": "string", "description": "错误信息"}, "code": {"type": "string", "description": "错误代码"}}, "required": ["success", "error"]}}}}}}}}}