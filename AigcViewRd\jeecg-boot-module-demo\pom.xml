<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>jeecg-boot-parent</artifactId>
        <groupId>org.jeecgframework.boot</groupId>
        <version>2.4.6</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>jeecg-boot-module-demo</artifactId>

    <dependencies>
        <dependency>
            <groupId>org.jeecgframework.boot</groupId>
            <artifactId>jeecg-boot-base-core</artifactId>
        </dependency>
        <!--引入微服务启动依赖 starter
      <dependency>
          <groupId>org.jeecgframework.boot</groupId>
          <artifactId>jeecg-boot-starter-cloud</artifactId>
      </dependency>
        <dependency>
            <groupId>org.jeecgframework.boot</groupId>
            <artifactId>jeecg-boot-starter-job</artifactId>
        </dependency>-->
    </dependencies>
</project>