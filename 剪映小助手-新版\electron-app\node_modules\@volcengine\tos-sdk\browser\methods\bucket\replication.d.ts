import { StorageClassInheritDirectiveType, StorageClassType } from '../../TosExportEnum';
import TOSBase from '../base';
export interface ReplicationTag {
    Key: string;
    Value: string;
}
export interface ReplicationRule {
    ID: string;
    Status: string;
    PrefixSet?: string[];
    Destination: {
        Bucket: string;
        Location: string;
        StorageClass?: StorageClassType;
        StorageClassInheritDirective: StorageClassInheritDirectiveType;
    };
    /**
     *  @private unstable: internal(default) |tos_acc
     */
    TransferType?: string;
    HistoricalObjectReplication: 'Enabled' | 'Disabled';
    /** @private unstable */
    Tags?: ReplicationTag[];
    AccessControlTranslation?: {
        Owner: string;
    };
}
export interface PutBucketReplicationInput {
    bucket: string;
    role: string;
    rules: ReplicationRule[];
}
export interface PutBucketReplicationOutput {
}
export declare function putBucketReplication(this: TOSBase, input: PutBucketReplicationInput): Promise<import("../base").TosResponse<DeleteBucketReplicationOutput>>;
export interface GetBucketReplicationInput {
    bucket: string;
    progress?: string;
    ruleId?: string;
}
export interface GetBucketReplicationOutput {
    Role: string;
    Rules: ReplicationRule[];
}
export declare function getBucketReplication(this: TOSBase, input: GetBucketReplicationInput): Promise<import("../base").TosResponse<GetBucketReplicationOutput>>;
export interface DeleteBucketReplicationInput {
    bucket: string;
}
export interface DeleteBucketReplicationOutput {
}
export declare function deleteBucketReplication(this: TOSBase, input: DeleteBucketReplicationInput): Promise<import("../base").TosResponse<DeleteBucketReplicationOutput>>;
