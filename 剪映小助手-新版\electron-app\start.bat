@echo off
title JianYing Assistant - Startup Script

echo.
echo ========================================
echo    JianYing Assistant - AigcView
echo ========================================
echo.

:: Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Node.js not found
    echo Please install Node.js: https://nodejs.org/
    echo.
    pause
    exit /b 1
)

echo [OK] Node.js installed:
node --version

:: Check if npm is available
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] npm not available
    echo.
    pause
    exit /b 1
)

echo [OK] npm installed:
npm --version

:: Check if node_modules exists
if not exist "node_modules" (
    echo.
    echo [INFO] First run, installing dependencies...
    echo.
    npm install
    if %errorlevel% neq 0 (
        echo [ERROR] Dependencies installation failed
        echo.
        pause
        exit /b 1
    )
    echo [OK] Dependencies installed successfully
)

:: Start application
echo.
echo [INFO] Starting JianYing Assistant...
echo.

:: Check if dev mode parameter is passed
if "%1"=="dev" (
    echo [INFO] Starting in development mode
    npm run dev
) else (
    echo [INFO] Starting in production mode
    npm start
)

if %errorlevel% neq 0 (
    echo.
    echo [ERROR] Application failed to start
    echo.
    pause
    exit /b 1
)

echo.
echo [INFO] Application closed
pause
