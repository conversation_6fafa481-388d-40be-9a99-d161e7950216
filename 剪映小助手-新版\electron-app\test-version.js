/**
 * 版本号测试脚本
 * 用于验证package.json中的版本号是否正确读取
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 测试版本号获取...\n');

try {
    // 读取package.json
    const packageJsonPath = path.join(__dirname, 'package.json');
    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
    
    console.log('📦 package.json 版本号:', packageJson.version);
    
    // 模拟Electron的app.getVersion()行为
    console.log('⚡ 模拟 app.getVersion():', packageJson.version);
    
    // 检查版本号格式
    const versionRegex = /^\d+\.\d+\.\d+$/;
    if (versionRegex.test(packageJson.version)) {
        console.log('✅ 版本号格式正确');
    } else {
        console.log('❌ 版本号格式不正确');
    }
    
    console.log('\n🎯 测试结果: 版本号读取正常');
    
} catch (error) {
    console.error('❌ 测试失败:', error.message);
}
