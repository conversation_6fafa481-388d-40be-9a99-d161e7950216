import TOSBase from '../base';
import { Acl, StorageClass } from '../../interface';
import { AzRedundancyType, StorageClassType } from '../../TosExportEnum';
export interface Bucket {
    CreationDate: string;
    ExtranetEndpoint: string;
    IntranetEndpoint: string;
    Location: string;
    Name: string;
    Owner: {
        ID: string;
    };
    BucketType?: string;
}
export interface ListBucketOutput {
    Buckets: Bucket[];
}
export interface PutBucketInput {
    bucket?: string;
    acl?: Acl;
    grantFullControl?: string;
    grantRead?: string;
    grantReadAcp?: string;
    grantWrite?: string;
    grantWriteAcp?: string;
    storageClass?: StorageClassType;
    azRedundancy?: AzRedundancyType;
    projectName?: string;
    bucketType?: string;
    headers?: {
        [key: string]: string | undefined;
        ['x-tos-acl']?: Acl;
        ['x-tos-grant-full-control']?: string;
        ['x-tos-grant-read']?: string;
        ['x-tos-grant-read-acp']?: string;
        ['x-tos-grant-write']?: string;
        ['x-tos-grant-write-acp']?: string;
        ['x-tos-storage-class']?: StorageClass;
    };
}
export interface ListBucketInput {
    projectName?: string;
}
export declare function listBuckets(this: TOSBase, input?: ListBucketInput): Promise<import("../base").TosResponse<ListBucketOutput>>;
export declare function createBucket(this: TOSBase, input: PutBucketInput): Promise<import("../base").TosResponse<unknown>>;
export declare function deleteBucket(this: TOSBase, bucket?: string): Promise<import("../base").TosResponse<unknown>>;
export interface HeadBucketOutput {
    ['x-tos-bucket-region']: string;
    ['x-tos-storage-class']: StorageClass;
    ['x-tos-bucket-type']?: string;
    ProjectName?: string;
}
export declare function headBucket(this: TOSBase, bucket?: string): Promise<import("../base").TosResponse<HeadBucketOutput>>;
export interface PutBucketStorageClassInput {
    bucket: string;
    storageClass: StorageClassType;
}
export interface PutBucketStorageClassOutput {
}
export declare function putBucketStorageClass(this: TOSBase, input: PutBucketStorageClassInput): Promise<import("../base").TosResponse<PutBucketStorageClassOutput>>;
