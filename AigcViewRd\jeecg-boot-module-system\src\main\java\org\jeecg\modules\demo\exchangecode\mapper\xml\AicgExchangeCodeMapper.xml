<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.demo.exchangecode.mapper.AicgExchangeCodeMapper">

    <!-- 根据兑换码查询 -->
    <select id="getByCode" parameterType="String" resultType="org.jeecg.modules.demo.exchangecode.entity.AicgExchangeCode">
        SELECT * FROM aicg_exchange_code WHERE code = #{code}
    </select>
    
    <!-- 使用兑换码 -->
    <update id="useExchangeCode">
        UPDATE aicg_exchange_code 
        SET status = 2, 
            used_by = #{userId}, 
            used_time = NOW(), 
            update_by = #{updateBy}, 
            update_time = NOW() 
        WHERE code = #{code} 
        AND status = 1
    </update>
    
    <!-- 查询用户使用的兑换码记录 -->
    <select id="getUserUsedCodes" parameterType="String" resultType="org.jeecg.modules.demo.exchangecode.entity.AicgExchangeCode">
        SELECT * FROM aicg_exchange_code 
        WHERE used_by = #{userId} 
        ORDER BY used_time DESC
    </select>
    
    <!-- 批量更新过期状态 -->
    <update id="updateExpiredCodes">
        UPDATE aicg_exchange_code 
        SET status = 3, update_time = NOW() 
        WHERE status = 1 
        AND expire_time &lt; NOW()
    </update>
    
    <!-- 查询批次兑换码统计 -->
    <select id="getBatchStats" parameterType="String" resultType="java.util.Map">
        SELECT 
            batch_no,
            COUNT(*) as total_count,
            SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as unused_count,
            SUM(CASE WHEN status = 2 THEN 1 ELSE 0 END) as used_count,
            SUM(CASE WHEN status = 3 THEN 1 ELSE 0 END) as expired_count,
            MIN(create_time) as create_time,
            MAX(expire_time) as expire_time
        FROM aicg_exchange_code 
        WHERE batch_no = #{batchNo}
        GROUP BY batch_no
    </select>
    
    <!-- 查询即将过期的兑换码 -->
    <select id="getExpiringCodes" parameterType="int" resultType="org.jeecg.modules.demo.exchangecode.entity.AicgExchangeCode">
        SELECT * FROM aicg_exchange_code 
        WHERE status = 1 
        AND expire_time BETWEEN NOW() AND DATE_ADD(NOW(), INTERVAL #{days} DAY)
        ORDER BY expire_time ASC
    </select>
    
    <!-- 查询兑换码使用统计 -->
    <select id="getUsageStats" resultType="java.util.Map">
        SELECT 
            code_type,
            COUNT(*) as total_count,
            SUM(CASE WHEN status = 2 THEN 1 ELSE 0 END) as used_count,
            SUM(CASE WHEN status = 2 THEN value ELSE 0 END) as used_value,
            AVG(CASE WHEN status = 2 THEN value ELSE NULL END) as avg_value
        FROM aicg_exchange_code 
        GROUP BY code_type
    </select>

</mapper>
