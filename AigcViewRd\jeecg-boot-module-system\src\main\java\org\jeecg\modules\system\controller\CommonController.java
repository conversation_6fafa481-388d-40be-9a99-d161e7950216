package org.jeecg.modules.system.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.constant.CommonConstant;
import org.jeecg.common.system.api.ISysBaseAPI;
import org.jeecg.common.util.CommonUtils;
import org.jeecg.common.util.RestUtil;
import org.jeecg.common.util.TokenUtils;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.jianying.service.TosService;
import org.jeecg.modules.system.service.IAigcDesktopDownloadLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.http.server.ServletServerHttpRequest;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.FileCopyUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.HandlerMapping;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import org.jeecg.modules.system.entity.AigcDesktopDownloadLog;
import org.apache.shiro.SecurityUtils;
import org.jeecg.modules.system.entity.SysUser;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.modules.system.util.RoleChecker;
import java.net.URLDecoder;
/**
 * <p>
 * 用户表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-20
 */
@Slf4j
@RestController
@RequestMapping("/sys/common")
public class CommonController {

    @Autowired
    private ISysBaseAPI sysBaseAPI;

    @Autowired
    private TosService tosService;

    @Autowired
    private IAigcDesktopDownloadLogService downloadLogService;

    @Autowired
    private RoleChecker roleChecker;

    @Value(value = "${jeecg.path.upload}")
    private String uploadpath;

    /**
     * 本地：local minio：minio 阿里：alioss TOS：tos
     */
    @Value(value="${jeecg.uploadType}")
    private String uploadType;

    /**
     * <AUTHOR>
     * @return
     */
    @GetMapping("/403")
    public Result<?> noauth()  {
        return Result.error("没有权限，请联系管理员授权");
    }

    /**
     * 文件上传统一方法
     * @param request
     * @param response
     * @return
     */
    @PostMapping(value = "/upload")
    public Result<?> upload(HttpServletRequest request, HttpServletResponse response) {
        Result<?> result = new Result<>();
        String savePath = "";
        String bizPath = request.getParameter("biz");
        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
        MultipartFile file = multipartRequest.getFile("file");// 获取上传文件对象
        if(oConvertUtils.isEmpty(bizPath)){
            if(CommonConstant.UPLOAD_TYPE_OSS.equals(uploadType)){
                //未指定目录，则用阿里云默认目录 upload
                bizPath = "upload";
                //result.setMessage("使用阿里云文件上传时，必须添加目录！");
                //result.setSuccess(false);
                //return result;
            }else{
                bizPath = "";
            }
        }
        if(CommonConstant.UPLOAD_TYPE_LOCAL.equals(uploadType)){
            //update-begin-author:lvdandan date:20200928 for:修改JEditor编辑器本地上传
            savePath = this.uploadLocal(file,bizPath);
            //update-begin-author:lvdandan date:20200928 for:修改JEditor编辑器本地上传
            /**  富文本编辑器及markdown本地上传时，采用返回链接方式
            //针对jeditor编辑器如何使 lcaol模式，采用 base64格式存储
            String jeditor = request.getParameter("jeditor");
            if(oConvertUtils.isNotEmpty(jeditor)){
                result.setMessage(CommonConstant.UPLOAD_TYPE_LOCAL);
                result.setSuccess(true);
                return result;
            }else{
                savePath = this.uploadLocal(file,bizPath);
            }
            */
        }else if("tos".equals(uploadType)){
            // TOS存储
            try {
                savePath = tosService.uploadGeneralFile(file, bizPath);
            } catch (Exception e) {
                log.error("TOS文件上传失败", e);
                result.setSuccess(false);
                result.setMessage("文件上传失败：" + e.getMessage());
                return result;
            }
        }else{
            //update-begin-author:taoyan date:20200814 for:文件上传改造
            savePath = CommonUtils.upload(file, bizPath, uploadType);
            //update-end-author:taoyan date:20200814 for:文件上传改造
        }
        if(oConvertUtils.isNotEmpty(savePath)){
            result.setMessage(savePath);
            result.setSuccess(true);
        }else {
            result.setMessage("上传失败！");
            result.setSuccess(false);
        }
        return result;
    }

    /**
     * 删除文件
     * @param filePath 文件路径
     * @return
     */
    @DeleteMapping(value = "/deleteFile")
    public Result<?> deleteFile(@RequestParam("filePath") String filePath) {
        Result<?> result = new Result<>();

        try {
            if (oConvertUtils.isEmpty(filePath)) {
                result.setSuccess(false);
                result.setMessage("文件路径不能为空");
                return result;
            }

            log.info("开始删除文件: {}", filePath);

            // 判断文件来源：TOS文件 vs 本地文件
            if (filePath.startsWith("uploads/")) {
                // TOS文件，使用TOS删除
                boolean deleted = tosService.deleteFile(filePath);
                if (deleted) {
                    result.setSuccess(true);
                    result.setMessage("TOS文件删除成功");
                    log.info("TOS文件删除成功: {}", filePath);
                } else {
                    result.setSuccess(false);
                    result.setMessage("TOS文件删除失败");
                    log.error("TOS文件删除失败: {}", filePath);
                }
            } else {
                // 本地文件删除
                String fullPath = uploadpath + File.separator + filePath;
                File file = new File(fullPath);

                if (file.exists()) {
                    boolean deleted = file.delete();
                    if (deleted) {
                        result.setSuccess(true);
                        result.setMessage("本地文件删除成功");
                        log.info("本地文件删除成功: {}", fullPath);
                    } else {
                        result.setSuccess(false);
                        result.setMessage("本地文件删除失败");
                        log.error("本地文件删除失败: {}", fullPath);
                    }
                } else {
                    result.setSuccess(false);
                    result.setMessage("文件不存在");
                    log.warn("要删除的文件不存在: {}", fullPath);
                }
            }

        } catch (Exception e) {
            log.error("删除文件异常: {}", filePath, e);
            result.setSuccess(false);
            result.setMessage("删除文件异常: " + e.getMessage());
        }

        return result;
    }

    /**
     * 获取默认头像URL
     * @return
     */
    @GetMapping("/default-avatar-url")
    public Result<String> getDefaultAvatarUrl() {
        Result<String> result = new Result<String>();
        try {
            String defaultAvatarUrl = tosService.getDefaultAvatarUrl();
            result.setSuccess(true);
            result.setResult(defaultAvatarUrl);
        } catch (Exception e) {
            log.error("获取默认头像URL失败", e);
            result.setSuccess(false);
            result.setMessage("获取默认头像URL失败");
            // 降级到本地默认头像
            result.setResult("/default-avatar.png");
        }
        return result;
    }

    /**
     * 获取默认插件图片URL
     * @return
     */
    @GetMapping("/default-plugin-image-url")
    public Result<String> getDefaultPluginImageUrl() {
        Result<String> result = new Result<String>();
        try {
            String defaultPluginImageUrl = tosService.getDefaultPluginImageUrl();
            result.setSuccess(true);
            result.setResult(defaultPluginImageUrl);
        } catch (Exception e) {
            log.error("获取默认插件图片URL失败", e);
            result.setSuccess(false);
            result.setMessage("获取默认插件图片URL失败");
            // 降级到本地默认插件图片
            result.setResult("/plugImg.jpg");
        }
        return result;
    }



    /**
     * 本地文件上传
     * @param mf 文件
     * @param bizPath  自定义路径
     * @return
     */
    private String uploadLocal(MultipartFile mf,String bizPath){
        try {
            String ctxPath = uploadpath;
            String fileName = null;
            File file = new File(ctxPath + File.separator + bizPath + File.separator );
            if (!file.exists()) {
                file.mkdirs();// 创建文件根目录
            }
            String orgName = mf.getOriginalFilename();// 获取文件名
            orgName = CommonUtils.getFileName(orgName);
            if(orgName.indexOf(".")!=-1){
                fileName = orgName.substring(0, orgName.lastIndexOf(".")) + "_" + System.currentTimeMillis() + orgName.substring(orgName.lastIndexOf("."));
            }else{
                fileName = orgName+ "_" + System.currentTimeMillis();
            }
            String savePath = file.getPath() + File.separator + fileName;
            File savefile = new File(savePath);
            FileCopyUtils.copy(mf.getBytes(), savefile);
            String dbpath = null;
            if(oConvertUtils.isNotEmpty(bizPath)){
                dbpath = bizPath + File.separator + fileName;
            }else{
                dbpath = fileName;
            }
            if (dbpath.contains("\\")) {
                dbpath = dbpath.replace("\\", "/");
            }
            return dbpath;
        } catch (IOException e) {
            log.error(e.getMessage(), e);
        }
        return "";
    }

//	@PostMapping(value = "/upload2")
//	public Result<?> upload2(HttpServletRequest request, HttpServletResponse response) {
//		Result<?> result = new Result<>();
//		try {
//			String ctxPath = uploadpath;
//			String fileName = null;
//			String bizPath = "files";
//			String tempBizPath = request.getParameter("biz");
//			if(oConvertUtils.isNotEmpty(tempBizPath)){
//				bizPath = tempBizPath;
//			}
//			String nowday = new SimpleDateFormat("yyyyMMdd").format(new Date());
//			File file = new File(ctxPath + File.separator + bizPath + File.separator + nowday);
//			if (!file.exists()) {
//				file.mkdirs();// 创建文件根目录
//			}
//			MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
//			MultipartFile mf = multipartRequest.getFile("file");// 获取上传文件对象
//			String orgName = mf.getOriginalFilename();// 获取文件名
//			fileName = orgName.substring(0, orgName.lastIndexOf(".")) + "_" + System.currentTimeMillis() + orgName.substring(orgName.indexOf("."));
//			String savePath = file.getPath() + File.separator + fileName;
//			File savefile = new File(savePath);
//			FileCopyUtils.copy(mf.getBytes(), savefile);
//			String dbpath = bizPath + File.separator + nowday + File.separator + fileName;
//			if (dbpath.contains("\\")) {
//				dbpath = dbpath.replace("\\", "/");
//			}
//			result.setMessage(dbpath);
//			result.setSuccess(true);
//		} catch (IOException e) {
//			result.setSuccess(false);
//			result.setMessage(e.getMessage());
//			log.error(e.getMessage(), e);
//		}
//		return result;
//	}

    /**
     * 预览图片&下载文件
     * 请求地址：http://localhost:8080/common/static/{user/20190119/e1fe9925bc315c60addea1b98eb1cb1349547719_1547866868179.jpg}
     *
     * @param request
     * @param response
     */
    @GetMapping(value = "/static/**")
    public void view(HttpServletRequest request, HttpServletResponse response) {
        // ISO-8859-1 ==> UTF-8 进行编码转换
        String imgPath = extractPathFromPattern(request);
        if(oConvertUtils.isEmpty(imgPath) || imgPath=="null"){
            return;
        }

        // 🆕 判断文件来源：TOS文件 vs 本地文件（双桶策略）
        if (imgPath.startsWith("uploads/") ||
            imgPath.startsWith("electron/") ||
            imgPath.startsWith("jianying-assistant/") ||
            imgPath.startsWith("defaults/")) {
            // TOS文件，生成访问URL并重定向
            try {
                String fileUrl = tosService.generateFileUrl(imgPath);
                if (oConvertUtils.isNotEmpty(fileUrl)) {
                    log.debug("TOS文件重定向: {} -> {}", imgPath, fileUrl);
                    response.sendRedirect(fileUrl);
                    return;
                } else {
                    log.warn("TOS文件URL生成失败: {}", imgPath);
                }
            } catch (Exception e) {
                log.error("生成TOS文件访问URL失败: {}", imgPath, e);
            }
            // 如果访问失败，返回404
            response.setStatus(404);
            return;
        }

        // 🆕 处理默认插件图片（从TOS获取，优先CDN）
        if (imgPath.equals("defaults/plugin-default.jpg")) {
            try {
                String fileUrl = tosService.getDefaultPluginImageUrl();
                if (oConvertUtils.isNotEmpty(fileUrl)) {
                    response.sendRedirect(fileUrl);
                    return;
                }
            } catch (Exception e) {
                log.error("获取默认插件图片URL失败: {}", imgPath, e);
            }
            // 如果TOS访问失败，降级到本地默认图片
            imgPath = "plugImg.jpg";
        }

        // 本地文件处理（保持原有逻辑）
        InputStream inputStream = null;
        OutputStream outputStream = null;
        try {
            imgPath = imgPath.replace("..", "").replace("../","");
            if (imgPath.endsWith(",")) {
                imgPath = imgPath.substring(0, imgPath.length() - 1);
            }
            String filePath = uploadpath + File.separator + imgPath;
            File file = new File(filePath);
            if(!file.exists()){
                response.setStatus(404);
                throw new RuntimeException("文件["+imgPath+"]不存在..");
            }
            response.setContentType("application/force-download");// 设置强制下载不打开
            response.addHeader("Content-Disposition", "attachment;fileName=" + new String(file.getName().getBytes("UTF-8"),"iso-8859-1"));
            inputStream = new BufferedInputStream(new FileInputStream(filePath));
            outputStream = response.getOutputStream();
            byte[] buf = new byte[1024];
            int len;
            while ((len = inputStream.read(buf)) > 0) {
                outputStream.write(buf, 0, len);
            }
            response.flushBuffer();
        } catch (IOException e) {
            log.error("预览文件失败" + e.getMessage());
            response.setStatus(404);
            e.printStackTrace();
        } finally {
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    log.error(e.getMessage(), e);
                }
            }
            if (outputStream != null) {
                try {
                    outputStream.close();
                } catch (IOException e) {
                    log.error(e.getMessage(), e);
                }
            }
        }

    }

//	/**
//	 * 下载文件
//	 * 请求地址：http://localhost:8080/common/download/{user/20190119/e1fe9925bc315c60addea1b98eb1cb1349547719_1547866868179.jpg}
//	 *
//	 * @param request
//	 * @param response
//	 * @throws Exception
//	 */
//	@GetMapping(value = "/download/**")
//	public void download(HttpServletRequest request, HttpServletResponse response) throws Exception {
//		// ISO-8859-1 ==> UTF-8 进行编码转换
//		String filePath = extractPathFromPattern(request);
//		// 其余处理略
//		InputStream inputStream = null;
//		OutputStream outputStream = null;
//		try {
//			filePath = filePath.replace("..", "");
//			if (filePath.endsWith(",")) {
//				filePath = filePath.substring(0, filePath.length() - 1);
//			}
//			String localPath = uploadpath;
//			String downloadFilePath = localPath + File.separator + filePath;
//			File file = new File(downloadFilePath);
//	         if (file.exists()) {
//	         	response.setContentType("application/force-download");// 设置强制下载不打开            
//	 			response.addHeader("Content-Disposition", "attachment;fileName=" + new String(file.getName().getBytes("UTF-8"),"iso-8859-1"));
//	 			inputStream = new BufferedInputStream(new FileInputStream(file));
//	 			outputStream = response.getOutputStream();
//	 			byte[] buf = new byte[1024];
//	 			int len;
//	 			while ((len = inputStream.read(buf)) > 0) {
//	 				outputStream.write(buf, 0, len);
//	 			}
//	 			response.flushBuffer();
//	         }
//
//		} catch (Exception e) {
//			log.info("文件下载失败" + e.getMessage());
//			// e.printStackTrace();
//		} finally {
//			if (inputStream != null) {
//				try {
//					inputStream.close();
//				} catch (IOException e) {
//					e.printStackTrace();
//				}
//			}
//			if (outputStream != null) {
//				try {
//					outputStream.close();
//				} catch (IOException e) {
//					e.printStackTrace();
//				}
//			}
//		}
//
//	}

    /**
     * 🆕 获取TOS文件的访问URL（优先CDN）
     * @param filePath TOS文件路径
     * @return CDN URL或签名URL
     */
    @GetMapping("/tos-file-url")
    public Result getTosFileUrl(@RequestParam("filePath") String filePath) {
        try {
            if (oConvertUtils.isEmpty(filePath)) {
                return Result.error("文件路径不能为空");
            }

            // 只处理TOS文件（以uploads/、electron/或jianying-assistant/开头）
            if (!filePath.startsWith("uploads/") && !filePath.startsWith("electron/") && !filePath.startsWith("jianying-assistant/")) {
                return Result.error("只支持TOS文件访问");
            }

            // 🆕 生成文件访问URL（优先CDN）
            String fileUrl = tosService.generateFileUrl(filePath);

            if (oConvertUtils.isEmpty(fileUrl)) {
                return Result.error("生成文件访问URL失败");
            }

            return Result.OK(fileUrl);

        } catch (Exception e) {
            log.error("获取文件访问URL失败: {}", filePath, e);
            return Result.error("获取文件访问URL失败: " + e.getMessage());
        }
    }

    /**
     * @功能：pdf预览Iframe
     * @param modelAndView
     * @return
     */
    @RequestMapping("/pdf/pdfPreviewIframe")
    public ModelAndView pdfPreviewIframe(ModelAndView modelAndView) {
        modelAndView.setViewName("pdfPreviewIframe");
        return modelAndView;
    }

    /**
     *  把指定URL后的字符串全部截断当成参数
     *  这么做是为了防止URL中包含中文或者特殊字符（/等）时，匹配不了的问题
     * @param request
     * @return
     */
    private static String extractPathFromPattern(final HttpServletRequest request) {
        String path = (String) request.getAttribute(HandlerMapping.PATH_WITHIN_HANDLER_MAPPING_ATTRIBUTE);
        String bestMatchPattern = (String) request.getAttribute(HandlerMapping.BEST_MATCHING_PATTERN_ATTRIBUTE);
        return new AntPathMatcher().extractPathWithinPattern(bestMatchPattern, path);
    }

    /**
     * 中转HTTP请求，解决跨域问题
     *
     * @param url 必填：请求地址
     * @return
     */
    @RequestMapping("/transitRESTful")
    public Result transitRESTful(@RequestParam("url") String url, HttpServletRequest request) {
        try {
            ServletServerHttpRequest httpRequest = new ServletServerHttpRequest(request);
            // 中转请求method、body
            HttpMethod method = httpRequest.getMethod();
            JSONObject params;
            try {
                params = JSON.parseObject(JSON.toJSONString(httpRequest.getBody()));
            } catch (Exception e) {
                params = new JSONObject();
            }
            // 中转请求问号参数
            JSONObject variables = JSON.parseObject(JSON.toJSONString(request.getParameterMap()));
            variables.remove("url");
            // 在 headers 里传递Token
            String token = TokenUtils.getTokenByRequest(request);
            HttpHeaders headers = new HttpHeaders();
            headers.set("X-Access-Token", token);
            // 发送请求
            String httpURL = URLDecoder.decode(url, "UTF-8");
            ResponseEntity<String> response = RestUtil.request(httpURL, method, headers , variables, params, String.class);
            // 封装返回结果
            Result<Object> result = new Result<>();
            int statusCode = response.getStatusCodeValue();
            result.setCode(statusCode);
            result.setSuccess(statusCode == 200);
            String responseBody = response.getBody();
            try {
                // 尝试将返回结果转为JSON
                Object json = JSON.parse(responseBody);
                result.setResult(json);
            } catch (Exception e) {
                // 转成JSON失败，直接返回原始数据
                result.setResult(responseBody);
            }
            return result;
        } catch (Exception e) {
            log.debug("中转HTTP请求失败", e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 获取桌面应用下载链接（公开接口，无需登录）
     * 安全措施：
     * 1. 硬编码文件白名单，只允许下载指定的桌面应用文件
     * 2. 添加访问频率限制，防止接口滥用
     * 3. 记录详细的下载日志，便于监控和统计
     * 4. 不暴露通用的TOS签名URL生成逻辑
     *
     * @param platform 平台类型 (windows/mac)
     * @param request HTTP请求对象，用于获取客户端信息
     * @return 下载链接
     */
    @GetMapping("/desktop-app-download")
    public Result getDesktopAppDownloadUrl(@RequestParam("platform") String platform,
                                         HttpServletRequest request) {
        long startTime = System.currentTimeMillis();
        String clientIp = null;
        String userAgent = null;
        String fileName = null;

        try {
            // 1. 参数验证
            if (oConvertUtils.isEmpty(platform)) {
                return Result.error("平台类型不能为空");
            }

            // 2. 获取客户端信息用于日志记录和频率限制
            clientIp = getClientIpAddress(request);
            userAgent = request.getHeader("User-Agent");

            // 3. 数据库频率限制检查（替代内存缓存）
            int recentDownloads = downloadLogService.checkDownloadFrequency(clientIp, 1); // 1分钟内
            if (recentDownloads >= 2) { // 1分钟内最多2次下载
                long responseTime = System.currentTimeMillis() - startTime;
                downloadLogService.logDownloadFailure(platform, fileName, clientIp, userAgent,
                    "下载频率限制：1分钟内超过2次下载", responseTime);
                log.warn("桌面应用下载频率限制触发: IP={}, platform={}, 1分钟内下载{}次",
                        clientIp, platform, recentDownloads);
                return Result.error("下载请求过于频繁，请稍后再试");
            }

            // 4. 定义支持的平台和对应的文件名（硬编码白名单）
            String fileDescription;
            switch (platform.toLowerCase()) {
                case "windows":
                    fileName = "剪映小助手-智界-Windows.zip";
                    fileDescription = "Windows版剪映小助手";
                    break;
                case "mac":
                    fileName = "剪映小助手-智界-Mac.zip";
                    fileDescription = "Mac版剪映小助手";
                    break;
                default:
                    log.warn("不支持的平台类型请求: platform={}, IP={}", platform, clientIp);
                    return Result.error("不支持的平台类型，仅支持windows和mac");
            }

            // 5. 构建文件路径（限制在electron目录下）
            String filePath = "electron/" + fileName;

            // 6. 生成TOS签名URL
            String signedUrl = tosService.generateSignedUrl(filePath);

            if (oConvertUtils.isEmpty(signedUrl)) {
                long responseTime = System.currentTimeMillis() - startTime;
                downloadLogService.logDownloadFailure(platform, fileName, clientIp, userAgent,
                    "生成TOS签名URL失败", responseTime);
                log.error("生成桌面应用下载链接失败: platform={}, fileName={}, IP={}",
                         platform, fileName, clientIp);
                return Result.error("生成下载链接失败，请稍后重试");
            }

            // 7. 计算响应时间并记录成功日志到数据库
            long responseTime = System.currentTimeMillis() - startTime;
            downloadLogService.logDownloadSuccess(platform, fileName, fileDescription,
                clientIp, userAgent, signedUrl, responseTime);

            // 8. 记录详细的控制台日志
            log.info("桌面应用下载请求成功: platform={}, fileName={}, description={}, IP={}, UserAgent={}, responseTime={}ms",
                    platform, fileName, fileDescription, clientIp, userAgent, responseTime);

            return Result.OK(signedUrl);

        } catch (Exception e) {
            // 记录异常到数据库
            long responseTime = System.currentTimeMillis() - startTime;
            downloadLogService.logDownloadFailure(platform, fileName, clientIp, userAgent,
                "系统异常: " + e.getMessage(), responseTime);

            log.error("获取桌面应用下载链接异常: platform={}, error={}", platform, e.getMessage(), e);
            return Result.error("系统异常，请稍后重试");
        }
    }

    /**
     * 剪映助手文件下载接口
     * 根据内部路径下载TOS文件
     * @param filePath 文件内部路径 (如: /jianying-assistant/drafts/2025/07/12/xxx.json)
     * @param response HTTP响应
     */
    @GetMapping("/jianying-file/**")
    public void downloadJianyingFile(HttpServletRequest request, HttpServletResponse response) {
        try {
            // 提取文件路径
            String requestURI = request.getRequestURI();
            String filePath = requestURI.substring(requestURI.indexOf("/jianying-file/") + "/jianying-file".length());

            log.info("剪映助手文件下载请求: {}", filePath);

            // 安全检查：防止路径遍历攻击
            if (filePath.contains("..") || !filePath.startsWith("/jianying-assistant/")) {
                response.setStatus(HttpServletResponse.SC_FORBIDDEN);
                response.getWriter().write("Access denied");
                return;
            }

            // 使用TOS服务下载文件
            String fileContent = tosService.downloadDraftFile(filePath);

            if (fileContent == null || fileContent.isEmpty()) {
                response.setStatus(HttpServletResponse.SC_NOT_FOUND);
                response.getWriter().write("File not found");
                return;
            }

            // 设置响应头
            response.setContentType("application/json;charset=UTF-8");
            response.setHeader("Content-Disposition", "attachment; filename=\"" +
                              filePath.substring(filePath.lastIndexOf("/") + 1) + "\"");
            response.setHeader("Cache-Control", "private, max-age=3600");

            // 输出文件内容
            response.getWriter().write(fileContent);
            response.getWriter().flush();

            log.info("剪映助手文件下载成功: {}", filePath);

        } catch (Exception e) {
            log.error("剪映助手文件下载失败", e);
            try {
                response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
                response.getWriter().write("Download failed: " + e.getMessage());
            } catch (Exception ex) {
                log.error("写入错误响应失败", ex);
            }
        }
    }

    /**
     * 获取桌面应用下载统计数据（管理员接口）
     * @param days 统计天数，默认7天
     * @param pageNo 页码，默认1
     * @param pageSize 每页大小，默认10
     * @return 统计数据
     */
    @GetMapping("/desktop-app-download-stats")
    public Result getDesktopAppDownloadStats(@RequestParam(value = "days", defaultValue = "7") int days,
                                           @RequestParam(value = "pageNo", defaultValue = "1") int pageNo,
                                           @RequestParam(value = "pageSize", defaultValue = "10") int pageSize,
                                           HttpServletRequest request) {
        try {
            // 权限验证：只有管理员可以查看下载统计
            LoginUser currentUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            if (currentUser == null) {
                return Result.error("用户未登录");
            }

            // 检查是否为管理员角色
            boolean isAdmin = roleChecker.checkUserIsAdmin(currentUser.getId());
            if (!isAdmin) {
                log.warn("非管理员用户尝试访问下载统计: username={}, userId={}, IP={}",
                        currentUser.getUsername(), currentUser.getId(), getClientIpAddress(request));
                return Result.error("权限不足，仅管理员可查看下载统计");
            }
            // 获取今日统计
            List<Map<String, Object>> todayStats = downloadLogService.getTodayDownloadStats();

            // 获取趋势数据
            List<Map<String, Object>> trendData = downloadLogService.getDownloadTrend(days);

            // 获取平台统计
            List<Map<String, Object>> platformStats = downloadLogService.getPlatformStats();

            // 🔥 获取最近下载记录（支持分页）
            IPage<AigcDesktopDownloadLog> recentDownloadsPage = downloadLogService.getRecentDownloadsPage(pageNo, pageSize);

            Map<String, Object> result = new HashMap<>();
            result.put("todayStats", todayStats);
            result.put("trendData", trendData);
            result.put("platformStats", platformStats);
            result.put("recentDownloads", recentDownloadsPage.getRecords());
            result.put("recentDownloadsTotal", recentDownloadsPage.getTotal());
            result.put("recentDownloadsCurrent", recentDownloadsPage.getCurrent());
            result.put("recentDownloadsSize", recentDownloadsPage.getSize());

            return Result.OK(result);

        } catch (Exception e) {
            log.error("获取桌面应用下载统计失败", e);
            return Result.error("获取统计数据失败: " + e.getMessage());
        }
    }

    // ==================== 桌面应用下载安全辅助方法 ====================

    /**
     * 下载频率限制缓存（简单的内存缓存，生产环境建议使用Redis）
     * Key: IP地址, Value: 最后下载时间戳
     */
    private static final Map<String, Long> downloadRateLimitCache = new ConcurrentHashMap<>();

    /**
     * 下载频率限制间隔（毫秒）- 同一IP 30秒内只能下载一次
     */
    private static final long DOWNLOAD_RATE_LIMIT_INTERVAL = 30 * 1000L;

    /**
     * 获取客户端真实IP地址
     * 考虑代理服务器和负载均衡的情况
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }

        // 处理多个IP的情况，取第一个
        if (ip != null && ip.contains(",")) {
            ip = ip.split(",")[0].trim();
        }

        return ip;
    }

    /**
     * 检查下载频率限制
     * @param clientIp 客户端IP
     * @return true-允许下载，false-频率限制
     */
    private boolean checkDownloadRateLimit(String clientIp) {
        if (oConvertUtils.isEmpty(clientIp)) {
            return true; // 无法获取IP时允许下载
        }

        Long lastDownloadTime = downloadRateLimitCache.get(clientIp);
        if (lastDownloadTime == null) {
            return true; // 首次下载
        }

        long currentTime = System.currentTimeMillis();
        return (currentTime - lastDownloadTime) >= DOWNLOAD_RATE_LIMIT_INTERVAL;
    }

    /**
     * 更新下载频率限制记录
     * @param clientIp 客户端IP
     */
    private void updateDownloadRateLimit(String clientIp) {
        if (oConvertUtils.isNotEmpty(clientIp)) {
            downloadRateLimitCache.put(clientIp, System.currentTimeMillis());

            // 清理过期的缓存记录（保留最近1小时的记录）
            cleanupExpiredRateLimitCache();
        }
    }

    /**
     * 清理过期的频率限制缓存
     * 避免内存泄漏，只保留最近1小时的记录
     */
    private void cleanupExpiredRateLimitCache() {
        long currentTime = System.currentTimeMillis();
        long expireTime = 60 * 60 * 1000L; // 1小时

        downloadRateLimitCache.entrySet().removeIf(entry ->
            (currentTime - entry.getValue()) > expireTime
        );
    }

}
