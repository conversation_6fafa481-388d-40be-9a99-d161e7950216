/**
 * 🔧 心跳API配置管理
 * 提供心跳API的配置管理和环境适配
 * 
 * <AUTHOR>
 * @since 2025-01-30
 */

// ==================== API配置常量 ====================

/**
 * 🌐 API端点配置
 */
export const API_ENDPOINTS = {
  // 核心心跳API
  HEARTBEAT: '/jeecg-boot/api/heartbeat',
  HEARTBEAT_CONFIG: '/jeecg-boot/api/heartbeat/config',
  HEARTBEAT_STATUS: '/jeecg-boot/api/heartbeat/status',
  HEARTBEAT_HISTORY: '/jeecg-boot/api/heartbeat/history',
  HEARTBEAT_PERFORMANCE: '/jeecg-boot/api/heartbeat/performance',
  
  // 用户状态API
  USER_ONLINE_STATUS: '/jeecg-boot/api/user/online-status',
  USER_ACTIVITY_INFO: '/jeecg-boot/api/user/activity-info',
  USER_BATCH_STATUS: '/jeecg-boot/api/user/batch-online-status',
  
  // 统计数据API
  ONLINE_USERS_STATS: '/jeecg-boot/api/online-users/stats',
  DASHBOARD_DATA: '/jeecg-boot/api/dashboard-data',
  USAGE_STATS: '/jeecg-boot/api/admin/usage-stats'
}

/**
 * ⚙️ 默认配置参数
 */
export const DEFAULT_CONFIG = {
  // 请求配置
  timeout: 30000,           // 30秒超时
  retryAttempts: 3,         // 最大重试次数
  retryDelay: 1000,         // 重试延迟（毫秒）
  backoffMultiplier: 2,     // 退避倍数
  
  // 缓存配置
  enableCache: true,        // 启用缓存
  cacheTTL: 30000,         // 缓存生存时间（30秒）
  cachePrefix: 'heartbeat_', // 缓存键前缀
  
  // 批量操作配置
  maxBatchSize: 100,        // 最大批量大小
  concurrency: 5,           // 并发数限制
  
  // 性能监控配置
  enablePerformanceLog: true,  // 启用性能日志
  slowRequestThreshold: 2000,  // 慢请求阈值（毫秒）
  
  // 错误处理配置
  enableErrorLog: true,     // 启用错误日志
  enableRetry: true,        // 启用重试机制
  retryableErrors: [        // 可重试的错误码
    'NETWORK_ERROR',
    'TIMEOUT',
    'SERVER_ERROR_500',
    'BAD_GATEWAY_502',
    'SERVICE_UNAVAILABLE_503',
    'GATEWAY_TIMEOUT_504'
  ]
}

/**
 * 🎯 页面类型配置映射
 */
export const PAGE_TYPE_CONFIG = {
  home: {
    interval: 20000,          // 20秒间隔
    priority: 'high',         // 高优先级
    enableCache: true,
    cacheTTL: 15000          // 15秒缓存
  },
  market: {
    interval: 30000,          // 30秒间隔
    priority: 'medium',       // 中优先级
    enableCache: true,
    cacheTTL: 30000          // 30秒缓存
  },
  profile: {
    interval: 30000,          // 30秒间隔
    priority: 'medium',       // 中优先级
    enableCache: true,
    cacheTTL: 30000          // 30秒缓存
  },
  admin: {
    interval: 15000,          // 15秒间隔
    priority: 'highest',      // 最高优先级
    enableCache: false,       // 禁用缓存（实时性要求高）
    cacheTTL: 0
  },
  tutorial: {
    interval: 60000,          // 60秒间隔
    priority: 'low',          // 低优先级
    enableCache: true,
    cacheTTL: 60000          // 60秒缓存
  },
  default: {
    interval: 30000,          // 30秒间隔
    priority: 'medium',       // 中优先级
    enableCache: true,
    cacheTTL: 30000          // 30秒缓存
  }
}

// ==================== 环境配置 ====================

/**
 * 🌍 获取环境特定配置
 * @returns {Object} 环境配置
 */
export function getEnvironmentConfig() {
  const isDevelopment = process.env.NODE_ENV === 'development'
  const isProduction = process.env.NODE_ENV === 'production'
  
  return {
    // 调试配置
    enableDebugLog: isDevelopment,
    enableVerboseLog: isDevelopment,
    enablePerformanceLog: isDevelopment,
    
    // 缓存配置
    enableCache: isProduction, // 生产环境启用缓存
    cacheTTL: isDevelopment ? 10000 : 30000, // 开发环境短缓存
    
    // 重试配置
    retryAttempts: isDevelopment ? 1 : 3, // 开发环境减少重试
    retryDelay: isDevelopment ? 500 : 1000,
    
    // 超时配置
    timeout: isDevelopment ? 10000 : 30000, // 开发环境短超时
    
    // 错误处理
    enableErrorLog: true,
    enableErrorReport: isProduction, // 生产环境启用错误上报
    
    // 性能监控
    enablePerformanceMonitor: isProduction,
    slowRequestThreshold: isDevelopment ? 1000 : 2000
  }
}

/**
 * 🔧 获取完整配置
 * @param {String} pageType 页面类型
 * @param {Object} customConfig 自定义配置
 * @returns {Object} 完整配置
 */
export function getApiConfig(pageType = 'default', customConfig = {}) {
  const envConfig = getEnvironmentConfig()
  const pageConfig = PAGE_TYPE_CONFIG[pageType] || PAGE_TYPE_CONFIG.default
  
  return {
    ...DEFAULT_CONFIG,
    ...envConfig,
    ...pageConfig,
    ...customConfig,
    
    // 确保关键字段存在
    pageType,
    timestamp: Date.now(),
    userAgent: navigator.userAgent,
    pageUrl: window.location.href
  }
}

// ==================== API密钥管理 ====================

/**
 * 🔑 API密钥配置
 */
export const API_KEYS = {
  // 页面专用密钥
  HOME: 'home-page-heartbeat-key',
  MARKET: 'market-page-heartbeat-key',
  PROFILE: 'usercenter-page-heartbeat-key',
  ADMIN: 'admin-dashboard-heartbeat-key',
  TUTORIAL: 'tutorial-page-heartbeat-key',
  DEFAULT: 'default-page-heartbeat-key',
  
  // 功能专用密钥
  MONITORING: 'monitoring-api-key',
  ANALYTICS: 'analytics-api-key',
  PERFORMANCE: 'performance-api-key'
}

/**
 * 🔑 获取页面API密钥
 * @param {String} pageType 页面类型
 * @returns {String} API密钥
 */
export function getApiKey(pageType) {
  const keyMap = {
    home: API_KEYS.HOME,
    market: API_KEYS.MARKET,
    profile: API_KEYS.PROFILE,
    admin: API_KEYS.ADMIN,
    tutorial: API_KEYS.TUTORIAL,
    default: API_KEYS.DEFAULT
  }
  
  return keyMap[pageType] || API_KEYS.DEFAULT
}

// ==================== 错误码映射 ====================

/**
 * 🚨 错误码配置
 */
export const ERROR_CODES = {
  // 网络错误
  NETWORK_ERROR: { code: 'NETWORK_001', message: '网络连接失败', retryable: true },
  TIMEOUT: { code: 'NETWORK_002', message: '请求超时', retryable: true },
  
  // 服务器错误
  SERVER_ERROR_500: { code: 'SERVER_001', message: '服务器内部错误', retryable: true },
  BAD_GATEWAY_502: { code: 'SERVER_002', message: '网关错误', retryable: true },
  SERVICE_UNAVAILABLE_503: { code: 'SERVER_003', message: '服务不可用', retryable: true },
  GATEWAY_TIMEOUT_504: { code: 'SERVER_004', message: '网关超时', retryable: true },
  
  // 客户端错误
  BAD_REQUEST_400: { code: 'CLIENT_001', message: '请求参数错误', retryable: false },
  UNAUTHORIZED_401: { code: 'CLIENT_002', message: '未授权访问', retryable: false },
  FORBIDDEN_403: { code: 'CLIENT_003', message: '访问被禁止', retryable: false },
  NOT_FOUND_404: { code: 'CLIENT_004', message: '资源不存在', retryable: false },
  
  // 业务错误
  INVALID_API_KEY: { code: 'BUSINESS_001', message: 'API密钥无效', retryable: false },
  RATE_LIMIT_EXCEEDED: { code: 'BUSINESS_002', message: '请求频率超限', retryable: true },
  INSUFFICIENT_BALANCE: { code: 'BUSINESS_003', message: '余额不足', retryable: false }
}

/**
 * 🔍 获取错误信息
 * @param {String|Number} errorCode 错误码
 * @returns {Object} 错误信息
 */
export function getErrorInfo(errorCode) {
  // 数字错误码映射
  const numericCodeMap = {
    400: 'BAD_REQUEST_400',
    401: 'UNAUTHORIZED_401',
    403: 'FORBIDDEN_403',
    404: 'NOT_FOUND_404',
    500: 'SERVER_ERROR_500',
    502: 'BAD_GATEWAY_502',
    503: 'SERVICE_UNAVAILABLE_503',
    504: 'GATEWAY_TIMEOUT_504'
  }
  
  const mappedCode = numericCodeMap[errorCode] || errorCode
  return ERROR_CODES[mappedCode] || {
    code: 'UNKNOWN_ERROR',
    message: '未知错误',
    retryable: false
  }
}

// ==================== 导出配置 ====================

export default {
  API_ENDPOINTS,
  DEFAULT_CONFIG,
  PAGE_TYPE_CONFIG,
  API_KEYS,
  ERROR_CODES,
  getEnvironmentConfig,
  getApiConfig,
  getApiKey,
  getErrorInfo
}
