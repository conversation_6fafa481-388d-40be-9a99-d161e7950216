package org.jeecg.modules.demo.plubauthor.service;

import org.jeecg.modules.demo.plubauthor.entity.AigcPlubAuthor;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * @Description: 插件创作者
 * @Author: jeecg-boot
 * @Date:   2025-06-14
 * @Version: V1.0
 */
public interface IAigcPlubAuthorService extends IService<AigcPlubAuthor> {

    /**
     * 更新插件创作者的使用总数
     * @param authorId 创作者ID
     * @return 是否成功
     */
    boolean updateAuthorUsageCount(String authorId);

    /**
     * 更新指定作者的插件数
     * @param authorId 创作者ID
     * @return 是否成功
     */
    boolean updateAuthorPluginCount(String authorId);

    /**
     * 批量更新所有作者的插件数
     * @return 更新的作者数量
     */
    int updateAllAuthorPluginCounts();

    /**
     * 更新插件创作者的累计收益
     * @param authorId 创作者ID
     * @param amount 收益金额
     * @return 是否成功
     */
    boolean updateAuthorTotalIncome(String authorId, java.math.BigDecimal amount);

    /**
     * 获取作者详情（包含字典转换）
     * @param authorId 创作者ID
     * @return 作者信息（包含字典转换后的字段）
     */
    AigcPlubAuthor getAuthorWithDictText(String authorId);

}
