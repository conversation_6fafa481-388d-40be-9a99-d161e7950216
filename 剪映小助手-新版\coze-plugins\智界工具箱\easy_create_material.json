{"openapi": "3.0.0", "info": {"title": "剪映小助手_智界工具箱 - 快速创建素材轨道", "description": "快速创建素材轨道，一键添加音频、图片、文本等多种素材", "version": "1.0.0"}, "servers": [{"url": "https://www.aigcview.com"}], "paths": {"/jeecg-boot/api/jianying/easy_create_material": {"post": {"summary": "快速创建素材轨道", "description": "快速创建素材轨道，一键添加音频、图片、文本等多种素材", "operationId": "easy_create_material", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"access_key": {"type": "string", "description": "访问密钥（必填）", "example": "JianyingAPI_2025_SecureAccess_AigcView", "default": "JianyingAPI_2025_SecureAccess_AigcView"}, "audio_url": {"type": "string", "description": "音频，轨道的开始结束以音频为主（必填）", "example": "https://example.com/audio.mp3"}, "draft_url": {"type": "string", "description": "草稿地址（必填）", "example": "https://example.com/draft/123"}, "video_url": {"type": "string", "description": "视频", "example": "https://example.com/video.mp4"}, "text": {"type": "string", "description": "字幕", "example": "这是字幕内容"}, "text_transform_x": {"type": "number", "description": "字幕x轴的位置", "example": 0.5}, "text_transform_y": {"type": "number", "description": "字幕y轴的位置", "example": 0.8}, "font_size": {"type": "integer", "description": "字幕字体大小", "example": 30}, "img_url": {"type": "string", "description": "图片", "example": "https://example.com/image.jpg"}, "text_color": {"type": "string", "description": "字幕颜色", "example": "#ffffff"}}, "required": ["access_key", "audio_url", "draft_url"], "additionalProperties": false}}}}, "responses": {"200": {"description": "成功创建素材轨道", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "description": "响应消息", "example": "不知道导入的请看：https://www.aigcview.com/JianYingDraft?draft=https://aigcview-plub.tos-s3-cn-guangzhou.volces.com/jianying-assistant/drafts/2025/01/05/draft_123456.json"}, "draft_url": {"type": "string", "description": "草稿地址", "example": "https://aigcview-plub.tos-s3-cn-guangzhou.volces.com/jianying-assistant/drafts/2025/01/05/draft_123456.json"}}, "required": ["message", "draft_url"]}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "description": "操作是否成功", "example": false}, "error": {"type": "string", "description": "错误信息"}, "code": {"type": "string", "description": "错误代码"}}, "required": ["success", "error"]}}}}, "500": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "description": "操作是否成功", "example": false}, "error": {"type": "string", "description": "错误信息"}, "code": {"type": "string", "description": "错误代码"}}, "required": ["success", "error"]}}}}}}}}}