<template>
  <div class="agent-detail-page">
    <!-- 时间轴式导航 -->
    <div class="timeline-nav">
      <div class="timeline-line"></div>
      <div
        v-for="(item, index) in navItems"
        :key="item.id"
        class="timeline-node"
        :class="{ 'active': activeNavItem === item.id }"
        :style="{ top: (index * 120 + 100) + 'px' }"
        @click="scrollToSection(item.id)"
      >
        <div class="node-dot"></div>
        <div class="node-label">{{ item.title }}</div>
      </div>
    </div>

    <!-- 权限检查加载状态 -->
    <div v-if="isCheckingPermission" class="permission-loading">
      <a-spin size="large">
        <div class="loading-text">正在验证访问权限...</div>
      </a-spin>
    </div>

    <!-- 未登录提示 -->
    <div v-else-if="!isLoggedIn" class="permission-denied">
      <div class="permission-content">
        <a-icon type="lock" class="permission-icon" />
        <h2>需要登录访问</h2>
        <p>请先登录您的账户以查看智能体详细信息</p>
        <a-button type="primary" size="large" @click="goToLogin">
          立即登录
        </a-button>
      </div>
    </div>

    <!-- 未购买提示 -->
    <div v-else-if="!isPurchased" class="permission-denied">
      <div class="permission-content">
        <a-icon type="shopping-cart" class="permission-icon" />
        <h2>需要购买访问</h2>
        <p>您还未购买此智能体，请先购买后再查看详细信息</p>
        <div class="action-buttons">
          <a-button size="large" @click="goBack">
            返回列表
          </a-button>
          <a-button type="primary" size="large" @click="goToPurchase">
            立即购买
          </a-button>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div v-else class="main-content">
      <!-- 页面头部 -->
      <div class="page-header">
        <a-button @click="goBack" class="back-button">
          <a-icon type="arrow-left" />
          返回
        </a-button>
        <div class="breadcrumb">
          <a-breadcrumb>
            <a-breadcrumb-item>
              <router-link to="/workflow-center">工作流中心</router-link>
            </a-breadcrumb-item>
            <a-breadcrumb-item>智能体详情</a-breadcrumb-item>
          </a-breadcrumb>
        </div>
      </div>

      <!-- 智能体基本信息 -->
      <div id="agent-info" class="agent-info-section">
        <div class="agent-basic-info">
          <div class="agent-avatar">
            <img 
              :src="agentDetail.agentAvatar || defaultAvatar" 
              :alt="agentDetail.agentName"
              @error="handleAvatarError"
            />
          </div>
          <div class="agent-details">
            <h1 class="agent-name">{{ agentDetail.agentName || '智能体名称' }}</h1>
            <div class="agent-description">
              <p>{{ agentDetail.agentDescription || '暂无描述' }}</p>
            </div>
            <div class="creator-info">
              <img
                :src="(agentDetail.creatorInfo && agentDetail.creatorInfo.avatar) || agentDetail.creatorAvatar || defaultCreatorAvatar"
                :alt="(agentDetail.creatorInfo && agentDetail.creatorInfo.nickname) || agentDetail.creatorNickname || '创作者'"
                class="creator-avatar"
                @error="handleCreatorAvatarError"
              />
              <span class="creator-name">{{ (agentDetail.creatorInfo && agentDetail.creatorInfo.nickname) || agentDetail.creatorNickname || '创作者' }}</span>
              <span class="author-type-badge" :class="authorTypeClass">
                {{ authorTypeText }}
              </span>
            </div>
          </div>
        </div>

        <!-- 阅读提示 -->
        <div class="reading-tip" @click="scrollToUsageGuide">
          <a-icon type="info-circle" class="tip-icon" />
          <span class="tip-text">
            请往下滑或点此快速下滑，仔细阅读工作流导入使用说明
          </span>
          <a-icon type="arrow-down" class="arrow-icon" />
        </div>
      </div>

      <!-- 演示视频区域 -->
      <div id="demo-video" v-if="agentDetail.demoVideo" class="video-section">
        <div class="video-header" @click="toggleVideoCollapse">
          <h3>演示视频</h3>
          <a-icon
            :type="videoCollapsed ? 'down' : 'up'"
            class="collapse-icon"
          />
        </div>
        <div v-show="!videoCollapsed" class="video-container">
          <video
            ref="videoPlayer"
            :src="agentDetail.demoVideo"
            controls
            autoplay
            muted
            preload="metadata"
            @loadstart="onVideoLoadStart"
            @loadeddata="onVideoLoaded"
            @error="onVideoError"
            @play="onVideoPlay"
            @pause="onVideoPause"
          >
            您的浏览器不支持视频播放
          </video>
          <div v-if="videoError" class="video-error">
            <a-icon type="exclamation-circle" />
            <span>视频加载失败，请稍后重试</span>
          </div>
        </div>
      </div>

      <!-- 工作流导入使用说明 -->
      <div id="usage-guide" class="usage-guide-section">
        <div class="guide-header" @click="toggleGuideCollapse">
          <h3>工作流导入使用说明</h3>
          <a-icon
            :type="guideCollapsed ? 'down' : 'up'"
            class="collapse-icon"
          />
        </div>
        <div v-show="!guideCollapsed" class="guide-content">
          <div class="guide-step">
            <div class="step-number">1</div>
            <div class="step-content">
              <p class="step-text">点击下载工作流，下载完成是一个压缩包。</p>
              <div class="step-image" @click="previewImage('https://cdn.aigcview.com/defaults/export.png')">
                <img
                  src="https://cdn.aigcview.com/defaults/export.png"
                  alt="下载工作流示例图"
                  @error="handleGuideImageError"
                />
                <div class="image-overlay">
                  <a-icon type="zoom-in" />
                  <span>点击查看大图</span>
                </div>
              </div>
            </div>
          </div>

          <div class="guide-step">
            <div class="step-number">2</div>
            <div class="step-content">
              <p class="step-text">
                <a
                  href="https://www.coze.cn/space"
                  target="_blank"
                  class="coze-link"
                >
                  点此快速跳转Coze工作空间
                  <a-icon type="external-link" />
                </a>
                ，选择需要放置的工作空间，点击右上角导入按钮，选择下载好的工作流压缩包即可完成。<span class="highlight-text">（压缩包无需解压）</span>
              </p>
              <div class="step-image" @click="previewImage('https://cdn.aigcview.com/defaults/import.png')">
                <img
                  src="https://cdn.aigcview.com/defaults/import.png"
                  alt="导入工作流示例图"
                  @error="handleGuideImageError"
                />
                <div class="image-overlay">
                  <a-icon type="zoom-in" />
                  <span>点击查看大图</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 工作流列表区域 -->
      <div id="workflow-list" class="workflow-section">
        <h3>关联工作流 ({{ workflowList.length }}个)</h3>
        <div v-if="workflowLoading" class="workflow-loading">
          <a-spin>加载工作流列表...</a-spin>
        </div>
        <div v-else-if="workflowList.length === 0" class="workflow-empty">
          <a-empty description="暂无关联工作流" />
        </div>
        <div v-else class="workflow-list">
          <div
            v-for="workflow in workflowList"
            :key="workflow.id"
            class="workflow-card"
          >
            <!-- 工作流头像（使用智能体头像） -->
            <div class="workflow-avatar">
              <img
                :src="agentDetail.agentAvatar || defaultAgentAvatar"
                :alt="workflow.workflowName"
                class="workflow-image"
                @error="handleWorkflowImageError"
              />
            </div>

            <div class="workflow-info">
              <h4 class="workflow-name">{{ workflow.workflowName }}</h4>
              <p class="workflow-description">{{ workflow.workflowDescription || '暂无描述' }}</p>

              <!-- 🔥 新增：输入参数说明 -->
              <div class="workflow-params">
                <div class="params-label">
                  <a-icon type="setting" />
                  <span>输入参数说明</span>
                </div>
                <div class="params-content">
                  {{ workflow.inputParamsDesc || '暂无输入参数说明' }}
                </div>
              </div>
            </div>

            <div class="workflow-actions">
              <a-button
                type="primary"
                size="default"
                class="download-btn"
                @click="handleWorkflowDownload(workflow)"
                :loading="workflow.downloading"
                :disabled="!workflow.workflowPackage"
              >
                <a-icon type="cloud-download" />
                <span>下载工作流</span>
              </a-button>
            </div>
          </div>
        </div>

        <!-- 使用提示 -->
        <div v-if="workflowList.length > 0" class="workflow-usage-tip">
          <a-icon type="info-circle" class="tip-icon" />
          <span class="tip-text">
            点击下载工作流是一个压缩包，将下载好的压缩包导入进Coze工作空间即可使用。<span class="highlight-text">（压缩包无需解压）</span>
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getUserRole } from '@/utils/roleUtils'

export default {
  name: 'AgentDetailPage',
  data() {
    return {
      // 权限状态
      isCheckingPermission: true,
      isLoggedIn: false,
      isPurchased: false,
      
      // 数据状态
      agentDetail: {},
      workflowList: [],
      workflowLoading: false,
      
      // 视频状态
      videoError: false,
      videoCollapsed: false,

      // 使用说明状态
      guideCollapsed: false,

      // 导航状态
      activeNavItem: 'agent-info',
      navItems: [
        { id: 'agent-info', title: '智能体信息' },
        { id: 'demo-video', title: '演示视频' },
        { id: 'usage-guide', title: '使用说明' },
        { id: 'workflow-list', title: '工作流列表' }
      ],
      
      // 默认图片
      defaultAvatar: '/default-avatar.png', // 使用项目统一的默认头像路径
      defaultCreatorAvatar: '/default-avatar.png' // 创作者也使用相同的默认头像
    }
  },
  
  computed: {
    agentId() {
      return this.$route.query.agentId
    },
    
    authorTypeClass() {
      if (this.agentDetail.authorType === '1' || this.agentDetail.authorType === 1) {
        return 'official'
      }
      return 'creator'
    },
    
    authorTypeText() {
      if (this.agentDetail.authorType === '1' || this.agentDetail.authorType === 1) {
        return '官方'
      }
      return '创作者'
    }
  },
  
  async created() {
    await this.checkPermissions()
    if (this.isLoggedIn && this.isPurchased) {
      await this.loadAgentData()
    }
  },

  mounted() {
    // 添加滚动监听
    window.addEventListener('scroll', this.handleScroll)
    // 初始化当前区域 - 确保默认为第一个区域
    this.$nextTick(() => {
      // 页面加载时默认激活第一个区域
      this.activeNavItem = 'agent-info'
      console.log('📍 页面加载，默认激活:', this.activeNavItem)

      // 延迟执行滚动检测，避免初始化时的错误激活
      setTimeout(() => {
        this.handleScroll()
      }, 500)
    })
  },

  beforeDestroy() {
    // 移除滚动监听
    window.removeEventListener('scroll', this.handleScroll)
  },
  
  methods: {
    // 权限检查
    async checkPermissions() {
      try {
        console.log('🔍 开始权限检查, agentId:', this.agentId)
        console.log('🚨 DEBUG: AgentDetailPage 代码已更新 - 版本2024')

        // 检查登录状态 - getUserRole是异步函数
        const userRole = await getUserRole()
        this.isLoggedIn = !!userRole

        console.log('🔍 登录状态检查:', {
          userRole,
          isLoggedIn: this.isLoggedIn
        })

        if (!this.isLoggedIn) {
          console.log('🔍 用户未登录，停止权限检查')
          this.isCheckingPermission = false
          return
        }

        // 检查购买状态 - 使用服务端API验证
        console.log('🔍 即将调用 checkPurchaseStatus 方法...')
        this.isPurchased = await this.checkPurchaseStatus()
        console.log('🔍 checkPurchaseStatus 方法调用完成，返回值:', this.isPurchased)

        console.log('🔍 购买状态检查:', {
          isPurchased: this.isPurchased,
          agentId: this.agentId
        })

        this.isCheckingPermission = false
      } catch (error) {
        console.error('🔍 权限检查失败:', error)
        this.isCheckingPermission = false
      }
    },
    
    // 检查购买状态 - 使用服务端API验证
    async checkPurchaseStatus() {
      try {
        console.log('🔍 AgentDetailPage - 验证购买状态, agentId:', this.agentId)
        const response = await this.$http.get(`/api/agent/market/purchase/check/${this.agentId}`)
        console.log('🔍 AgentDetailPage - 购买状态API响应:', response)

        if (response && response.success) {
          const isPurchased = response.result.isPurchased
          console.log('✅ AgentDetailPage - 购买状态验证完成:', isPurchased)
          return isPurchased
        } else {
          console.warn('⚠️ AgentDetailPage - 购买状态验证失败')
          return false
        }
      } catch (error) {
        console.error('❌ AgentDetailPage - 购买状态验证出错:', error)
        return false
      }
    },
    
    // 加载智能体数据
    async loadAgentData() {
      try {
        // 🔥 只需要加载智能体详情，工作流列表已包含在详情接口中
        await this.loadAgentDetail()
      } catch (error) {
        console.error('加载数据失败:', error)
        this.$message.error('加载数据失败，请稍后重试')
      }
    },
    
    // 加载智能体详情
    async loadAgentDetail() {
      try {
        console.log('🔍 开始加载智能体详情, agentId:', this.agentId)

        const response = await this.$http.get(`/api/agent/market/detail/${this.agentId}`)
        console.log('🔍 智能体详情API响应:', response)

        if (response.success) {
          this.agentDetail = response.result
          console.log('🔍 智能体详情加载成功:', this.agentDetail)

          // 动态设置页面标题
          document.title = `${this.agentDetail.agentName || '智能体详情'} - 智界AIGC`

          // 🔥 直接从详情接口获取工作流列表，无需单独请求
          if (this.agentDetail.workflowList) {
            this.workflowList = this.agentDetail.workflowList
            console.log('🔍 工作流列表已从详情接口获取:', this.workflowList)
          } else {
            this.workflowList = []
          }
        } else {
          throw new Error(response.message || '获取智能体详情失败')
        }
      } catch (error) {
        console.error('🔍 加载智能体详情失败:', error)
        this.$message.error('加载智能体详情失败: ' + error.message)
        throw error
      }
    },
    
    // 🔥 加载工作流列表 - 已废弃，现在直接从智能体详情接口获取
    // async loadWorkflowList() {
    //   this.workflowLoading = true
    //   try {
    //     console.log('🔍 开始加载工作流列表, agentId:', this.agentId)
    //     const response = await this.$http.get(`/api/agent/market/${this.agentId}/workflows`)
    //     console.log('🔍 工作流列表API响应:', response)
    //     if (response.success) {
    //       this.workflowList = response.result || []
    //       console.log('🔍 工作流列表加载成功:', this.workflowList)
    //     } else {
    //       throw new Error(response.message || '获取工作流列表失败')
    //     }
    //   } catch (error) {
    //     console.error('🔍 加载工作流列表失败:', error)
    //     this.$message.error('加载工作流列表失败: ' + error.message)
    //     this.workflowList = []
    //   } finally {
    //     this.workflowLoading = false
    //   }
    // },
    
    // 导航方法
    goBack() {
      this.$router.go(-1)
    },
    
    goToLogin() {
      this.$router.push({
        path: '/login',
        query: { redirect: this.$route.fullPath }
      })
    },
    
    goToPurchase() {
      this.$router.push('/workflow-center')
    },
    
    // 图片错误处理
    handleAvatarError(event) {
      event.target.src = this.defaultAvatar
    },
    
    handleCreatorAvatarError(event) {
      event.target.src = this.defaultCreatorAvatar
    },

    // 工作流图片错误处理
    handleWorkflowImageError(event) {
      event.target.src = this.defaultAgentAvatar
    },
    
    // 视频事件处理
    onVideoLoadStart() {
      this.videoError = false
      console.log('🎬 视频开始加载')
    },
    
    onVideoLoaded() {
      console.log('🎬 视频加载完成')
    },
    
    onVideoError() {
      this.videoError = true
      console.error('🎬 视频加载失败')
    },
    
    onVideoPlay() {
      console.log('🎬 视频开始播放')
    },
    
    onVideoPause() {
      console.log('🎬 视频暂停播放')
    },

    // 切换视频折叠状态
    toggleVideoCollapse() {
      this.videoCollapsed = !this.videoCollapsed
      console.log('🎬 视频折叠状态:', this.videoCollapsed ? '折叠' : '展开')
    },

    // 切换使用说明折叠状态
    toggleGuideCollapse() {
      this.guideCollapsed = !this.guideCollapsed
      console.log('📖 使用说明折叠状态:', this.guideCollapsed ? '折叠' : '展开')
    },

    // 处理使用说明图片加载错误
    handleGuideImageError(event) {
      console.warn('📖 使用说明图片加载失败:', event.target.src)
      event.target.style.display = 'none'
    },

    // 预览图片
    previewImage(imageUrl) {
      // 禁用页面滚动
      document.body.style.overflow = 'hidden'

      // 缩放和位置状态
      let scale = 1
      let translateX = 0
      let translateY = 0
      let isDragging = false
      let lastX = 0
      let lastY = 0
      let animationId = null

      // 创建图片预览模态框
      const modal = document.createElement('div')
      modal.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.9);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 9999;
        cursor: default;
      `

      const img = document.createElement('img')
      img.src = imageUrl
      img.style.cssText = `
        max-width: 90%;
        max-height: 90%;
        object-fit: contain;
        border-radius: 8px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
        user-select: none;
        cursor: grab;
      `

      // 添加操作提示
      const tip = document.createElement('div')
      tip.style.cssText = `
        position: absolute;
        top: 20px;
        left: 50%;
        transform: translateX(-50%);
        background: rgba(0, 0, 0, 0.7);
        color: white;
        padding: 8px 16px;
        border-radius: 20px;
        font-size: 14px;
        z-index: 10000;
        pointer-events: none;
        opacity: 0.8;
      `
      tip.textContent = '滚轮缩放 | 拖拽移动 | 双击重置 | ESC关闭'

      // 更新图片变换
      const updateTransform = (withTransition = false) => {
        // 拖拽时不使用过渡动画，其他操作使用过渡动画
        img.style.transition = withTransition ? 'transform 0.3s ease' : 'none'
        img.style.transform = `translate(${translateX}px, ${translateY}px) scale(${scale})`
      }

      modal.appendChild(img)
      modal.appendChild(tip)
      document.body.appendChild(modal)

      // 滚轮缩放功能
      const handleWheel = (e) => {
        e.preventDefault()
        const delta = e.deltaY > 0 ? -0.1 : 0.1
        const newScale = Math.max(0.5, Math.min(5, scale + delta))

        if (newScale !== scale) {
          scale = newScale
          updateTransform(true) // 缩放使用过渡动画
          modal.style.cursor = 'grab' // 始终显示可拖拽状态
        }
      }

      // 拖拽功能
      const handleMouseDown = (e) => {
        if (e.target === img) {
          e.preventDefault()
          e.stopPropagation()
          isDragging = true
          lastX = e.clientX
          lastY = e.clientY
          modal.style.cursor = 'grabbing'
        }
      }

      const handleMouseMove = (e) => {
        if (isDragging) {
          e.preventDefault()

          // 取消之前的动画帧
          if (animationId) {
            cancelAnimationFrame(animationId)
          }

          // 使用 requestAnimationFrame 优化性能
          animationId = requestAnimationFrame(() => {
            const deltaX = e.clientX - lastX
            const deltaY = e.clientY - lastY
            translateX += deltaX
            translateY += deltaY
            lastX = e.clientX
            lastY = e.clientY
            updateTransform(false) // 拖拽不使用过渡动画，实现即时响应
          })
        }
      }

      const handleMouseUp = (e) => {
        if (isDragging) {
          e.preventDefault()
          e.stopPropagation()
          isDragging = false
          modal.style.cursor = 'grab' // 始终显示可拖拽状态
        }
      }

      // 双击重置
      const handleDoubleClick = () => {
        scale = 1
        translateX = 0
        translateY = 0
        updateTransform(true) // 重置使用过渡动画
        modal.style.cursor = 'grab' // 始终显示可拖拽状态
      }

      // 关闭模态框
      const closeModal = () => {
        // 恢复页面滚动
        document.body.style.overflow = ''

        // 取消动画帧
        if (animationId) {
          cancelAnimationFrame(animationId)
        }

        // 移除事件监听器
        modal.removeEventListener('wheel', handleWheel)
        modal.removeEventListener('mousedown', handleMouseDown)
        document.removeEventListener('mousemove', handleMouseMove)
        document.removeEventListener('mouseup', handleMouseUp)
        modal.removeEventListener('dblclick', handleDoubleClick)
        document.removeEventListener('keydown', handleKeyDown)

        // 移除模态框
        document.body.removeChild(modal)
      }

      // 键盘ESC关闭
      const handleKeyDown = (e) => {
        if (e.key === 'Escape') {
          closeModal()
        }
      }

      // 点击背景关闭（但不包括图片，且不在拖拽状态）
      const handleModalClick = (e) => {
        if (e.target === modal && !isDragging) {
          closeModal()
        }
      }

      // 添加事件监听器
      modal.addEventListener('wheel', handleWheel)
      modal.addEventListener('mousedown', handleMouseDown)
      document.addEventListener('mousemove', handleMouseMove)
      document.addEventListener('mouseup', handleMouseUp)
      modal.addEventListener('dblclick', handleDoubleClick)
      modal.addEventListener('click', handleModalClick)
      document.addEventListener('keydown', handleKeyDown)

      console.log('🔍 图片预览:', imageUrl)
    },

    // 滚动到指定区域
    scrollToSection(sectionId) {
      const element = document.getElementById(sectionId)
      if (element) {
        element.scrollIntoView({
          behavior: 'smooth',
          block: 'start'
        })
        this.activeNavItem = sectionId
        console.log('🎯 滚动到区域:', sectionId)
      }
    },

    // 快速滚动到使用说明
    scrollToUsageGuide() {
      this.scrollToSection('usage-guide')
      console.log('📖 快速跳转到使用说明')
    },

    // 监听滚动事件，更新活跃导航项
    handleScroll() {
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop
      const windowHeight = window.innerHeight
      const documentHeight = document.documentElement.scrollHeight

      // 如果在页面顶部，直接激活第一个区域
      if (scrollTop < 100) {
        if (this.activeNavItem !== 'agent-info') {
          this.activeNavItem = 'agent-info'
          console.log('📍 页面顶部，激活:', this.activeNavItem)
        }
        return
      }

      // 获取所有区域的位置
      const sections = this.navItems.map(item => {
        const element = document.getElementById(item.id)
        return {
          id: item.id,
          offsetTop: element ? element.offsetTop - 150 : 0,
          element: element
        }
      })

      // 检查是否滚动到页面底部
      const isAtBottom = scrollTop + windowHeight >= documentHeight - 50

      let currentSection = sections[0].id

      if (isAtBottom) {
        // 如果在页面底部，激活最后一个区域
        currentSection = sections[sections.length - 1].id
      } else {
        // 找到当前滚动位置对应的区域
        for (let i = sections.length - 1; i >= 0; i--) {
          if (scrollTop >= sections[i].offsetTop) {
            currentSection = sections[i].id
            break
          }
        }
      }

      if (this.activeNavItem !== currentSection) {
        this.activeNavItem = currentSection
        console.log('📍 当前区域:', currentSection, isAtBottom ? '(页面底部)' : '')
      }
    },

    // 工作流下载
    async handleWorkflowDownload(workflow) {
      try {
        console.log('🔍 开始下载工作流:', workflow)

        // 检查是否有下载地址
        if (!workflow.workflowPackage && !workflow.packagePath) {
          this.$message.error('工作流压缩包地址不存在')
          return
        }

        // 设置下载状态
        this.$set(workflow, 'downloading', true)

        // 🌐 直接使用CDN地址下载，无需调用后端API
        const downloadUrl = workflow.workflowPackage || workflow.packagePath
        console.log('🔍 使用CDN地址下载工作流:', downloadUrl)

        // 创建下载链接
        const link = document.createElement('a')
        link.href = downloadUrl
        link.download = `${workflow.workflowName || '工作流'}.zip`
        link.target = '_blank'

        // 触发下载
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)

        this.$message.success(`工作流 "${workflow.workflowName}" 下载已开始`)

      } catch (error) {
        console.error('🔍 工作流下载失败:', error)
        this.$message.error('下载失败: ' + error.message)
      } finally {
        // 清除下载状态
        this.$set(workflow, 'downloading', false)
      }
    }
  }
}
</script>

<style lang="less" scoped>
.agent-detail-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  position: relative;

  // 时间轴式导航
  .timeline-nav {
    position: fixed;
    left: calc(50% - 650px);
    top: 200px;
    z-index: 1000;

    .timeline-line {
      position: absolute;
      left: 50%;
      top: 0;
      transform: translateX(-50%);
      width: 2px;
      height: 580px;
      background: linear-gradient(to bottom, #e6f4ff 0%, #1890ff 50%, #e6f4ff 100%);
      border-radius: 1px;
    }

    .timeline-node {
      position: absolute;
      left: 50%;
      transform: translateX(-50%);
      cursor: pointer;
      transition: all 0.3s ease;

      .node-label {
        position: absolute;
        right: 20px;
        top: 50%;
        transform: translateY(-50%);
        font-size: 13px;
        color: #8c8c8c;
        font-weight: 500;
        white-space: nowrap;
        transition: all 0.3s ease;
        opacity: 0.8;
        text-align: right;
      }

      .node-dot {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        background: #d9d9d9;
        border: 2px solid white;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
        transition: all 0.3s ease;
        position: relative;
        z-index: 2;
      }

      &:hover {
        .node-dot {
          background: #40a9ff;
          transform: scale(1.3);
          box-shadow: 0 4px 16px rgba(24, 144, 255, 0.4);
        }

        .node-label {
          color: #40a9ff;
          opacity: 1;
        }
      }

      &.active {
        .node-dot {
          background: #1890ff;
          transform: scale(1.4);
          box-shadow: 0 4px 16px rgba(24, 144, 255, 0.6);
          border-color: #1890ff;
        }

        .node-label {
          color: #1890ff;
          font-weight: 600;
          opacity: 1;
        }
      }
    }
  }

  // 权限检查加载状态
  .permission-loading {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;

    .loading-text {
      margin-top: 16px;
      font-size: 16px;
      color: #666;
    }
  }

  // 权限拒绝页面
  .permission-denied {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;

    .permission-content {
      text-align: center;
      padding: 48px;
      background: white;
      border-radius: 16px;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
      max-width: 400px;

      .permission-icon {
        font-size: 64px;
        color: #1890ff;
        margin-bottom: 24px;
      }

      h2 {
        font-size: 24px;
        margin-bottom: 16px;
        color: #262626;
      }

      p {
        font-size: 16px;
        color: #666;
        margin-bottom: 32px;
        line-height: 1.6;
      }

      .action-buttons {
        display: flex;
        gap: 16px;
        justify-content: center;
      }
    }
  }

  // 主要内容区域
  .main-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 24px;
    position: relative;

    // 页面头部
    .page-header {
      display: flex;
      align-items: center;
      padding: 20px 0;

      .back-button {
        margin-right: 20px;
        border-radius: 8px;
        height: 40px;
        padding: 0 16px;
        font-size: 15px;
        font-weight: 500;
        border: 1px solid #d9d9d9;
        background: white;
        color: #595959;
        transition: all 0.3s ease;

        &:hover {
          border-color: #1890ff;
          color: #1890ff;
          background: #f6f9ff;
        }

        .anticon {
          margin-right: 6px;
          font-size: 14px;
        }
      }

      .breadcrumb {
        flex: 1;

        /deep/ .ant-breadcrumb {
          font-size: 16px;

          .ant-breadcrumb-link {
            color: #8c8c8c;
            font-weight: 400;
            transition: color 0.3s ease;

            &:hover {
              color: #1890ff;
            }
          }

          .ant-breadcrumb-separator {
            color: #d9d9d9;
            margin: 0 12px;
          }

          .ant-breadcrumb-link a {
            color: #1890ff;
            text-decoration: none;
            font-weight: 500;

            &:hover {
              color: #096dd9;
            }
          }
        }
      }
    }

    // 智能体信息区域
    .agent-info-section {
      background: white;
      border-radius: 16px;
      padding: 32px;
      margin-bottom: 24px;
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);

      .agent-basic-info {
        display: flex;
        gap: 24px;

        .agent-avatar {
          flex-shrink: 0;

          img {
            width: 120px;
            height: 120px;
            border-radius: 16px;
            object-fit: cover;
            border: 3px solid #f0f0f0;
          }
        }

        .agent-details {
          flex: 1;

          .agent-name {
            font-size: 28px;
            font-weight: 600;
            margin-bottom: 16px;
            color: #262626;
          }

          .creator-info {
            display: flex;
            align-items: center;
            margin-bottom: 16px;

            .creator-avatar {
              width: 32px;
              height: 32px;
              border-radius: 50%;
              margin-right: 8px;
              object-fit: cover;
            }

            .creator-name {
              font-size: 16px;
              color: #595959;
              margin-right: 12px;
            }

            .author-type-badge {
              padding: 4px 12px;
              border-radius: 12px;
              font-size: 12px;
              font-weight: 500;

              &.official {
                background: linear-gradient(135deg, #52c41a 0%, #389e0d 100%);
                color: white;
              }

              &.creator {
                background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
                color: white;
              }
            }
          }

          .agent-description {
            margin-bottom: 20px;

            p {
              font-size: 16px;
              line-height: 1.6;
              color: #595959;
            }
          }

          .agent-stats {
            display: flex;
            gap: 32px;

            .stat-item {
              display: flex;
              flex-direction: column;

              .stat-label {
                font-size: 14px;
                color: #8c8c8c;
                margin-bottom: 4px;
              }

              .stat-value {
                font-size: 20px;
                font-weight: 600;
                color: #1890ff;
              }
            }
          }
        }
      }

      // 阅读提示
      .reading-tip {
        margin-top: 24px;
        padding: 16px 20px;
        background: linear-gradient(135deg, #fff7e6 0%, #ffecc7 100%);
        border: 1px solid #ffd591;
        border-radius: 12px;
        display: flex;
        align-items: center;
        gap: 12px;
        animation: tipPulse 2s ease-in-out infinite;
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
          background: linear-gradient(135deg, #fff1d6 0%, #ffe7b3 100%);
          border-color: #ffb84d;
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(255, 181, 77, 0.3);
        }

        &:active {
          transform: translateY(0);
          box-shadow: 0 2px 6px rgba(255, 181, 77, 0.2);
        }

        .tip-icon {
          color: #fa8c16;
          font-size: 16px;
          flex-shrink: 0;
        }

        .tip-text {
          color: #262626;
          font-size: 14px;
          line-height: 1.6;
          font-weight: 500;
          flex: 1;
        }

        .arrow-icon {
          color: #fa8c16;
          font-size: 16px;
          animation: arrowBounce 1.5s ease-in-out infinite;
        }
      }
    }

    // 视频区域
    .video-section {
      background: white;
      border-radius: 16px;
      padding: 32px;
      margin-bottom: 24px;
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);

      .video-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        cursor: pointer;
        user-select: none;
        transition: all 0.3s ease;
        padding: 8px 0;
        border-radius: 8px;

        &:hover {
          background-color: #f5f5f5;
          padding: 8px 12px;
        }

        h3 {
          font-size: 20px;
          font-weight: 600;
          margin: 0;
          color: #262626;
        }

        .collapse-icon {
          font-size: 16px;
          color: #8c8c8c;
          transition: all 0.3s ease;

          &:hover {
            color: #1890ff;
          }
        }
      }

      .video-container {
        position: relative;
        margin-top: 20px;
        transition: all 0.3s ease;

        video {
          width: 100%;
          max-height: 500px;
          border-radius: 12px;
          background: #000;
        }

        .video-error {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          color: #ff4d4f;
          font-size: 16px;

          .anticon {
            margin-right: 8px;
          }
        }
      }
    }

    // 使用说明区域
    .usage-guide-section {
      background: white;
      border-radius: 16px;
      padding: 32px;
      margin-bottom: 24px;
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);

      .guide-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        cursor: pointer;
        user-select: none;
        transition: all 0.3s ease;
        padding: 8px 0;
        border-radius: 8px;

        &:hover {
          background-color: #f5f5f5;
          padding: 8px 12px;
        }

        h3 {
          font-size: 20px;
          font-weight: 600;
          margin: 0;
          color: #262626;
        }

        .collapse-icon {
          font-size: 16px;
          color: #8c8c8c;
          transition: all 0.3s ease;

          &:hover {
            color: #1890ff;
          }
        }
      }

      .guide-content {
        margin-top: 24px;

        .guide-step {
          display: flex;
          align-items: flex-start;
          gap: 16px;
          margin-bottom: 32px;

          &:last-child {
            margin-bottom: 0;
          }

          .step-number {
            width: 32px;
            height: 32px;
            background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 16px;
            flex-shrink: 0;
            margin-top: 0;
          }

          .step-content {
            flex: 1;

            .step-text {
              font-size: 15px;
              line-height: 1.6;
              color: #262626;
              margin-bottom: 16px;

              .coze-link {
                color: #1890ff;
                text-decoration: none;
                font-weight: 500;
                transition: color 0.3s ease;

                &:hover {
                  color: #096dd9;
                  text-decoration: underline;
                }

                .anticon {
                  margin-left: 4px;
                  font-size: 12px;
                }
              }

              .highlight-text {
                color: #ff4d4f;
                font-weight: 600;
                background: linear-gradient(135deg, #fff2f0 0%, #ffece8 100%);
                padding: 2px 6px;
                border-radius: 4px;
                border: 1px solid #ffccc7;
              }
            }

            .step-image {
              border-radius: 12px;
              overflow: hidden;
              border: 1px solid #f0f0f0;
              box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
              position: relative;
              cursor: pointer;
              transition: all 0.3s ease;

              &:hover {
                box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
                transform: translateY(-2px);

                .image-overlay {
                  opacity: 1;
                }

                img {
                  transform: scale(1.05);
                }
              }

              img {
                width: 100%;
                height: auto;
                display: block;
                transition: transform 0.3s ease;
              }

              .image-overlay {
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(0, 0, 0, 0.6);
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                opacity: 0;
                transition: opacity 0.3s ease;
                color: white;
                font-size: 14px;

                .anticon {
                  font-size: 24px;
                  margin-bottom: 8px;
                }

                span {
                  font-weight: 500;
                }
              }
            }
          }
        }
      }
    }

    // 工作流区域
    .workflow-section {
      background: white;
      border-radius: 16px;
      padding: 32px;
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);

      h3 {
        font-size: 20px;
        font-weight: 600;
        margin-bottom: 20px;
        color: #262626;
      }

      .workflow-loading {
        text-align: center;
        padding: 40px;
      }

      .workflow-empty {
        text-align: center;
        padding: 40px;
      }

      .workflow-list {
        display: flex;
        flex-direction: column;
        gap: 20px;

        .workflow-card {
          border: 1px solid #f0f0f0;
          border-radius: 12px;
          padding: 24px;
          transition: all 0.3s ease;
          display: flex;
          align-items: center;
          gap: 20px;
          width: 100%;
          min-height: 120px;

          &:hover {
            border-color: #1890ff;
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);
            transform: translateY(-2px);
          }

          .workflow-avatar {
            flex-shrink: 0;

            .workflow-image {
              width: 80px;
              height: 80px;
              border-radius: 12px;
              object-fit: cover;
              border: 2px solid #f0f0f0;
              transition: all 0.3s ease;
            }
          }

          .workflow-info {
            flex: 1;
            min-width: 0;
            display: flex;
            flex-direction: column;
            justify-content: center;

            .workflow-name {
              font-size: 18px;
              font-weight: 600;
              margin-bottom: 12px;
              color: #262626;
              line-height: 1.4;
              word-wrap: break-word;
              word-break: break-all;
            }

            .workflow-description {
              font-size: 14px;
              color: #8c8c8c;
              line-height: 1.5;
              display: -webkit-box;
              -webkit-line-clamp: 2;
              -webkit-box-orient: vertical;
              overflow: hidden;
              margin-bottom: 12px;
            }

            // 🔥 新增：输入参数说明样式
            .workflow-params {
              .params-label {
                display: flex;
                align-items: center;
                gap: 6px;
                font-size: 13px;
                font-weight: 600;
                color: #1890ff;
                margin-bottom: 6px;

                .anticon {
                  font-size: 12px;
                  color: #1890ff;
                }
              }

              .params-content {
                font-size: 13px;
                color: #262626;
                line-height: 1.4;
                background: linear-gradient(135deg, #e6f7ff 0%, #f0f9ff 100%);
                padding: 10px 14px;
                border-radius: 8px;
                border: 1px solid #91d5ff;
                border-left: 4px solid #1890ff;
                display: -webkit-box;
                -webkit-line-clamp: 3;
                -webkit-box-orient: vertical;
                overflow: hidden;
                word-wrap: break-word;
                font-weight: 500;
                box-shadow: 0 2px 4px rgba(24, 144, 255, 0.1);
              }
            }
          }

          .workflow-actions {
            flex-shrink: 0;
            display: flex;
            align-items: center;
            justify-content: center;

            .download-btn {
              background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
              border: none;
              border-radius: 8px;
              height: 40px;
              padding: 0 20px;
              font-weight: 500;
              box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
              transition: all 0.3s ease;

              &:hover {
                background: linear-gradient(135deg, #40a9ff 0%, #1890ff 100%);
                box-shadow: 0 4px 12px rgba(24, 144, 255, 0.4);
                transform: translateY(-1px);
              }

              &:active {
                transform: translateY(0);
              }

              &:disabled {
                background: #f5f5f5;
                color: #bfbfbf;
                box-shadow: none;
                cursor: not-allowed;

                &:hover {
                  background: #f5f5f5;
                  transform: none;
                }
              }

              .anticon {
                margin-right: 6px;
                font-size: 16px;
              }

              span {
                font-size: 14px;
              }
            }
          }
        }
      }

      // 使用提示
      .workflow-usage-tip {
        margin-top: 24px;
        padding: 16px 20px;
        background: linear-gradient(135deg, #f6f9ff 0%, #e6f4ff 100%);
        border: 1px solid #b3d8ff;
        border-radius: 12px;
        display: flex;
        align-items: flex-start;
        gap: 12px;

        .tip-icon {
          color: #1890ff;
          font-size: 16px;
          margin-top: 2px;
          flex-shrink: 0;
        }

        .tip-text {
          color: #262626;
          font-size: 14px;
          line-height: 1.6;
          font-weight: 400;

          .highlight-text {
            color: #ff4d4f;
            font-weight: 600;
            background: linear-gradient(135deg, #fff2f0 0%, #ffece8 100%);
            padding: 2px 6px;
            border-radius: 4px;
            border: 1px solid #ffccc7;
          }
        }
      }
    }
  }
}

// 动画效果
@keyframes tipPulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.02);
  }
}

@keyframes arrowBounce {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-3px);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .agent-detail-page {
    // 移动端时间轴导航
    .timeline-nav {
      left: 20px;
      top: 150px;

      .timeline-line {
        height: 400px;
      }

      .timeline-node {
        .node-label {
          font-size: 12px;
          right: 16px;
        }

        .node-dot {
          width: 10px;
          height: 10px;
        }

        &:hover .node-dot {
          transform: scale(1.2);
        }

        &.active .node-dot {
          transform: scale(1.3);
        }
      }
    }

    .main-content {
      padding: 16px;

      .page-header {
        margin-bottom: 20px;
        padding: 16px 0;
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;

        .back-button {
          margin-right: 0;
          margin-bottom: 8px;
          height: 36px;
          font-size: 14px;
        }

        .breadcrumb {
          /deep/ .ant-breadcrumb {
            font-size: 14px;

            .ant-breadcrumb-separator {
              margin: 0 8px;
            }
          }
        }
      }

      .agent-info-section {
        padding: 20px;

        .agent-basic-info {
          flex-direction: column;
          text-align: center;

          .agent-details {
            .agent-stats {
              justify-content: center;
            }
          }
        }

        .reading-tip {
          margin-top: 20px;
          padding: 14px 16px;
          gap: 10px;

          .tip-icon {
            font-size: 14px;
          }

          .tip-text {
            font-size: 13px;
            line-height: 1.5;
          }

          .arrow-icon {
            font-size: 14px;
          }
        }
      }

      .video-section,
      .usage-guide-section,
      .workflow-section {
        padding: 20px;
      }

      .usage-guide-section {
        .guide-content {
          margin-top: 20px;

          .guide-step {
            gap: 12px;
            margin-bottom: 24px;

            .step-number {
              width: 28px;
              height: 28px;
              font-size: 14px;
            }

            .step-content {
              .step-text {
                font-size: 14px;
                margin-bottom: 12px;
              }

              .step-image {
                border-radius: 8px;
              }
            }
          }
        }
      }

      .workflow-list {
        gap: 16px;

        .workflow-card {
          flex-direction: column;
          text-align: center;
          gap: 16px;
          padding: 20px;

          .workflow-avatar {
            align-self: center;

            .workflow-image {
              width: 50px;
              height: 50px;
            }
          }

          .workflow-info {
            .workflow-name {
              white-space: normal;
              text-align: center;
            }

            // 🔥 移动端输入参数说明样式优化
            .workflow-params {
              text-align: left;
              margin-top: 12px;

              .params-label {
                justify-content: center;
                font-size: 12px;
                font-weight: 600;
                color: #1890ff;
              }

              .params-content {
                font-size: 12px;
                padding: 8px 12px;
                text-align: left;
                -webkit-line-clamp: 2; // 移动端显示更少行数
                color: #262626;
                font-weight: 500;
                background: linear-gradient(135deg, #e6f7ff 0%, #f0f9ff 100%);
                border: 1px solid #91d5ff;
                border-left: 3px solid #1890ff;
                box-shadow: 0 1px 3px rgba(24, 144, 255, 0.1);
              }
            }
          }

          .workflow-actions {
            align-self: center;

            .download-btn {
              width: 100%;
              max-width: 200px;
            }
          }
        }
      }

      .workflow-usage-tip {
        margin-top: 20px;
        padding: 14px 16px;
        gap: 10px;

        .tip-icon {
          font-size: 14px;
        }

        .tip-text {
          font-size: 13px;
          line-height: 1.5;
        }
      }
    }
  }
}
</style>
