{"openapi": "3.0.0", "info": {"title": "剪映小助手_智界工具箱 - 云渲染视频", "description": "云渲染视频，接收create_draft输出的链接", "version": "1.0.0"}, "servers": [{"url": "https://www.aigcview.com"}], "paths": {"/jeecg-boot/api/jianying/gen_video": {"post": {"summary": "云渲染视频", "description": "云渲染视频，接收create_draft输出的链接", "operationId": "gen_video", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"access_key": {"type": "string", "description": "访问密钥（必填）", "example": "JianyingAPI_2025_SecureAccess_AigcView", "default": "JianyingAPI_2025_SecureAccess_AigcView"}, "api_token": {"type": "string", "description": "API令牌（必填）", "example": "your_api_token_here"}, "draft_url": {"type": "string", "description": "草稿地址（必填）", "example": "https://example.com/draft/123"}}, "required": ["access_key", "api_token", "draft_url"], "additionalProperties": false}}}}, "responses": {"200": {"description": "成功提交渲染任务", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "description": "操作是否成功"}, "data": {"type": "object", "properties": {"video_url": {"type": "string", "description": "生成的视频地址"}, "task_id": {"type": "string", "description": "任务ID"}}}, "message": {"type": "string", "description": "响应消息"}}, "required": ["success", "data", "message"]}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "description": "操作是否成功", "example": false}, "error": {"type": "string", "description": "错误信息"}, "code": {"type": "string", "description": "错误代码"}}, "required": ["success", "error"]}}}}, "500": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "description": "操作是否成功", "example": false}, "error": {"type": "string", "description": "错误信息"}, "code": {"type": "string", "description": "错误代码"}}, "required": ["success", "error"]}}}}}}}}}