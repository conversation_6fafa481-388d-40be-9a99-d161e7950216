package org.jeecg.modules.demo.apiusage.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.demo.apiusage.entity.AicgUserApiUsage;
import org.jeecg.modules.demo.apiusage.service.IAicgUserApiUsageService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;

/**
 * @Description: 用户API使用记录表
 * @Author: jeecg-boot
 * @Date:   2025-06-18
 * @Version: V1.0
 */
@Api(tags="用户API使用记录表")
@RestController
@RequestMapping("/demo/apiusage")
@Slf4j
public class AicgUserApiUsageController extends JeecgController<AicgUserApiUsage, IAicgUserApiUsageService> {
	@Autowired
	private IAicgUserApiUsageService aicgUserApiUsageService;
	
	/**
	 * 分页列表查询
	 */
	@AutoLog(value = "用户API使用记录表-分页列表查询")
	@ApiOperation(value="用户API使用记录表-分页列表查询", notes="用户API使用记录表-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(AicgUserApiUsage aicgUserApiUsage,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   @RequestParam(name="userNickname", required=false) String userNickname,
								   HttpServletRequest req) {

		// 🔥 使用自定义查询方法，包含用户昵称
		IPage<Map<String, Object>> pageList = aicgUserApiUsageService.queryApiUsageWithUserInfo(
			pageNo, pageSize, aicgUserApiUsage, userNickname, req.getParameterMap()
		);

		return Result.OK(pageList);
	}
	
	/**
	 * 添加
	 */
	@AutoLog(value = "用户API使用记录表-添加")
	@ApiOperation(value="用户API使用记录表-添加", notes="用户API使用记录表-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody AicgUserApiUsage aicgUserApiUsage) {
		aicgUserApiUsageService.save(aicgUserApiUsage);
		return Result.OK("添加成功！");
	}
	
	/**
	 * 通过id查询
	 */
	@AutoLog(value = "用户API使用记录表-通过id查询")
	@ApiOperation(value="用户API使用记录表-通过id查询", notes="用户API使用记录表-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		AicgUserApiUsage aicgUserApiUsage = aicgUserApiUsageService.getById(id);
		if(aicgUserApiUsage==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(aicgUserApiUsage);
	}

    /**
	 * 导出excel
	 */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, AicgUserApiUsage aicgUserApiUsage) {
        return super.exportXls(request, aicgUserApiUsage, AicgUserApiUsage.class, "用户API使用记录表");
    }

    /**
     * 通过excel导入数据
     */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, AicgUserApiUsage.class);
    }
    
    /**
     * 根据用户ID查询API使用记录
     */
    @AutoLog(value = "根据用户ID查询API使用记录")
    @ApiOperation(value="根据用户ID查询API使用记录", notes="根据用户ID查询API使用记录")
    @GetMapping(value = "/getByUserId")
    public Result<?> getByUserId(@RequestParam(name="userId",required=true) String userId) {
        List<AicgUserApiUsage> list = aicgUserApiUsageService.getByUserId(userId);
        return Result.OK(list);
    }
    
    /**
     * 获取API使用统计
     */
    @AutoLog(value = "获取API使用统计")
    @ApiOperation(value="获取API使用统计", notes="获取API使用统计")
    @GetMapping(value = "/getStats")
    public Result<?> getUsageStats(@RequestParam(name="userId",required=true) String userId,
                                  @RequestParam(name="timeRange",defaultValue="week") String timeRange) {
        Map<String, Object> stats = aicgUserApiUsageService.getUsageStats(userId, timeRange);
        return Result.OK(stats);
    }
    
    /**
     * 获取API使用趋势
     */
    @AutoLog(value = "获取API使用趋势")
    @ApiOperation(value="获取API使用趋势", notes="获取API使用趋势")
    @GetMapping(value = "/getTrend")
    public Result<?> getUsageTrend(@RequestParam(name="userId",required=true) String userId,
                                  @RequestParam(name="timeRange",defaultValue="week") String timeRange) {
        List<Map<String, Object>> trend = aicgUserApiUsageService.getUsageTrend(userId, timeRange);
        return Result.OK(trend);
    }
    
    /**
     * 获取接口统计
     */
    @AutoLog(value = "获取接口统计")
    @ApiOperation(value="获取接口统计", notes="获取接口统计")
    @GetMapping(value = "/getEndpointStats")
    public Result<?> getEndpointStats(@RequestParam(name="userId",required=true) String userId,
                                     @RequestParam(name="timeRange",defaultValue="week") String timeRange) {
        List<Map<String, Object>> stats = aicgUserApiUsageService.getEndpointStats(userId, timeRange);
        return Result.OK(stats);
    }

    /**
     * 🔥 插件使用记录分页查询 - 专门用于后台管理（包含用户昵称）
     */
    @AutoLog(value = "插件使用记录-分页列表查询")
    @ApiOperation(value="插件使用记录-分页列表查询", notes="插件使用记录-分页列表查询")
    @GetMapping(value = "/pluginUsageList")
    public Result<?> queryPluginUsagePageList(AicgUserApiUsage aicgUserApiUsage,
                                              @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
                                              @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
                                              @RequestParam(name="hasPluginInfo", defaultValue="false") Boolean hasPluginInfo,
                                              @RequestParam(name="callTimeStart", required=false) String callTimeStart,
                                              @RequestParam(name="callTimeEnd", required=false) String callTimeEnd,
                                              @RequestParam(name="userNickname", required=false) String userNickname,
                                              HttpServletRequest req) {

        // 🔥 使用自定义查询方法，包含用户昵称
        IPage<Map<String, Object>> pageList = aicgUserApiUsageService.queryPluginUsageWithUserInfo(
            pageNo, pageSize, aicgUserApiUsage, hasPluginInfo, callTimeStart, callTimeEnd, userNickname, req.getParameterMap()
        );

        return Result.OK(pageList);
    }

    /**
     * 🔥 获取插件使用统计分析
     */
    @AutoLog(value = "获取插件使用统计分析")
    @ApiOperation(value="获取插件使用统计分析", notes="获取插件使用统计分析")
    @GetMapping(value = "/getPluginStats")
    public Result<?> getPluginUsageStats() {
        Map<String, Object> stats = aicgUserApiUsageService.getPluginUsageStats();
        return Result.OK(stats);
    }
}
