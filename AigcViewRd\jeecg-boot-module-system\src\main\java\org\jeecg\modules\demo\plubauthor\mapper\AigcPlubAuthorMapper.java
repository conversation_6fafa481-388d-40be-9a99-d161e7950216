package org.jeecg.modules.demo.plubauthor.mapper;

import java.util.List;
import java.math.BigDecimal;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;
import org.jeecg.modules.demo.plubauthor.entity.AigcPlubAuthor;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: 插件创作者
 * @Author: jeecg-boot
 * @Date:   2025-06-14
 * @Version: V1.0
 */
public interface AigcPlubAuthorMapper extends BaseMapper<AigcPlubAuthor> {

    /**
     * 更新插件创作者的使用总数
     * @param authorId 创作者ID
     * @return 更新结果
     */
    @Update("UPDATE aigc_plub_author SET plubusenum = plubusenum + 1, update_time = NOW() WHERE id = #{authorId}")
    int updateAuthorUsageCount(@Param("authorId") String authorId);

    /**
     * 根据插件商城数据更新作者的插件数
     * @param authorId 创作者ID
     * @return 更新结果
     */
    @Update("UPDATE aigc_plub_author SET plubnum = (SELECT COUNT(*) FROM aigc_plub_shop WHERE plubwrite = #{authorId}), update_time = NOW() WHERE id = #{authorId}")
    int updateAuthorPluginCount(@Param("authorId") String authorId);

    /**
     * 批量更新所有作者的插件数
     * @return 更新结果
     */
    @Update("UPDATE aigc_plub_author a SET a.plubnum = (SELECT COUNT(*) FROM aigc_plub_shop p WHERE p.plubwrite = a.id), a.update_time = NOW()")
    int updateAllAuthorPluginCounts();

    /**
     * 更新插件创作者的累计收益
     * @param authorId 创作者ID
     * @param amount 收益金额
     * @return 更新结果
     */
    @Update("UPDATE aigc_plub_author SET total_income = total_income + #{amount}, update_time = NOW() WHERE id = #{authorId}")
    int updateAuthorTotalIncome(@Param("authorId") String authorId, @Param("amount") BigDecimal amount);

}
