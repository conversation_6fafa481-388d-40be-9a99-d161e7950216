export interface ISign {
    signature(opt: ISigOptions, expiredAt: number, credentials?: ISigCredentials): string;
    signatureHeader(opt: ISigOptions, expiredAt?: number, credentials?: ISigCredentials): Map<string, string>;
    gnrCopySig(opt: ISigOptions, credentials: ISigCredentials): {
        key: string;
        value: string;
    };
    getSignatureQuery(opt: ISigOptions, expiredAt: number): {
        [key: string]: any;
    };
    getSignature(reqOpts: ISigOptions, expiredAt: number): {
        key: string;
        value: string;
    };
}
export interface ISigCredentials {
    GetSecretKey(): string;
    GetAccessKey(): string;
}
export interface ISigPolicyQuery {
    policy: {
        conditions: (string[] | {
            bucket: string;
        } | {
            key: string;
        })[];
    };
}
export interface ISigOptions {
    sigName?: string;
    endpoints?: string;
    bucket?: string;
    headers?: {
        [key: string]: string | undefined;
    };
    region?: string;
    serviceName?: string;
    algorithm?: string;
    path: string;
    method: string;
    query?: string;
    datetime?: string;
    host?: string;
    port?: number;
}
export interface ISigQueryOptions extends Omit<ISigOptions, 'query'> {
    query?: Record<string, any>;
}
export declare const SIG_QUERY: {
    algorithm: string;
    expiration: string;
    signame: string;
    signature: string;
    v4_algorithm: string;
    v4_credential: string;
    v4_date: string;
    v4_expires: string;
    v4_signedHeaders: string;
    v4_security_token: string;
    v4_signature: string;
    v4_content_sha: string;
    v4_policy: string;
};
export declare function isDefaultPort(port?: number): boolean;
interface ISignV4Opt {
    algorithm?: string;
    region?: string;
    serviceName?: string;
    securityToken?: string;
    bucket: string;
}
/**
 * @api private
 */
export declare class SignersV4 implements ISign {
    private options;
    private credentials;
    constructor(opt: ISignV4Opt, credentials: ISigCredentials);
    signature: (opt: ISigOptions, expiredAt: number, credentials?: ISigCredentials | undefined) => string;
    signatureHeader: (opt: ISigOptions, expiredAt?: number | undefined, credentials?: ISigCredentials | undefined) => Map<string, string>;
    gnrCopySig: (opt: ISigOptions, credentials: ISigCredentials) => {
        key: string;
        value: string;
    };
    getSignature: (opt: ISigOptions, expiredAt: number) => {
        key: '';
        value: '';
    };
    getSignatureQuery: (opt: ISigQueryOptions, expiredAt: number) => {
        [key: string]: any;
    };
    getSignaturePolicyQuery: (opt: ISigPolicyQuery, expiredAt: number) => {
        [key: string]: any;
    };
    private hexEncodedBodyHash;
    private authorization;
    private getDateTime;
    private credentialString;
    private createScope;
    private getSigningKey;
    private stringToSign;
    private hexEncodedHash;
    private canonicalString;
    private canonicalStringPolicy;
    private canonicalHeaders;
    private canonicalHeaderValues;
    private signedHeaders;
    /**
     * ! * ' () aren't transformed by encodeUrl, so they need be handled
     */
    private getEncodePath;
}
export declare class ISigV4Credentials implements ISigCredentials {
    securityToken: string;
    secretAccessKey: string;
    accessKeyId: string;
    constructor(securityToken?: string, secretAccessKey?: string, accessKeyId?: string);
    GetAccessKey(): string;
    GetSecretKey(): string;
}
export {};
