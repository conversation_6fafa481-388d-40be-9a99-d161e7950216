package org.jeecg.modules.demo.referral.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.demo.referral.entity.AicgUserReferral;
import org.jeecg.modules.demo.referral.service.IAicgUserReferralService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;

/**
 * @Description: 用户推荐关系表
 * @Author: jeecg-boot
 * @Date:   2025-06-18
 * @Version: V1.0
 */
@Api(tags="用户推荐关系表")
@RestController
@RequestMapping("/demo/referral")
@Slf4j
public class AicgUserReferralController extends JeecgController<AicgUserReferral, IAicgUserReferralService> {
	@Autowired
	private IAicgUserReferralService aicgUserReferralService;
	
	/**
	 * 分页列表查询
	 */
	@AutoLog(value = "用户推荐关系表-分页列表查询")
	@ApiOperation(value="用户推荐关系表-分页列表查询", notes="用户推荐关系表-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(AicgUserReferral aicgUserReferral,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<AicgUserReferral> queryWrapper = QueryGenerator.initQueryWrapper(aicgUserReferral, req.getParameterMap());
		Page<AicgUserReferral> page = new Page<AicgUserReferral>(pageNo, pageSize);
		IPage<AicgUserReferral> pageList = aicgUserReferralService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 * 添加
	 */
	@AutoLog(value = "用户推荐关系表-添加")
	@ApiOperation(value="用户推荐关系表-添加", notes="用户推荐关系表-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody AicgUserReferral aicgUserReferral) {
		aicgUserReferralService.save(aicgUserReferral);
		return Result.OK("添加成功！");
	}
	
	/**
	 * 编辑
	 */
	@AutoLog(value = "用户推荐关系表-编辑")
	@ApiOperation(value="用户推荐关系表-编辑", notes="用户推荐关系表-编辑")
	@PutMapping(value = "/edit")
	public Result<?> edit(@RequestBody AicgUserReferral aicgUserReferral) {
		aicgUserReferralService.updateById(aicgUserReferral);
		return Result.OK("编辑成功!");
	}
	
	/**
	 * 通过id删除
	 */
	@AutoLog(value = "用户推荐关系表-通过id删除")
	@ApiOperation(value="用户推荐关系表-通过id删除", notes="用户推荐关系表-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		aicgUserReferralService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 * 批量删除
	 */
	@AutoLog(value = "用户推荐关系表-批量删除")
	@ApiOperation(value="用户推荐关系表-批量删除", notes="用户推荐关系表-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.aicgUserReferralService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 */
	@AutoLog(value = "用户推荐关系表-通过id查询")
	@ApiOperation(value="用户推荐关系表-通过id查询", notes="用户推荐关系表-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		AicgUserReferral aicgUserReferral = aicgUserReferralService.getById(id);
		if(aicgUserReferral==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(aicgUserReferral);
	}

    /**
	 * 导出excel
	 */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, AicgUserReferral aicgUserReferral) {
        return super.exportXls(request, aicgUserReferral, AicgUserReferral.class, "用户推荐关系表");
    }

    /**
     * 通过excel导入数据
     */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, AicgUserReferral.class);
    }
    
    /**
     * 根据推荐人ID查询推荐记录
     */
    @AutoLog(value = "根据推荐人ID查询推荐记录")
    @ApiOperation(value="根据推荐人ID查询推荐记录", notes="根据推荐人ID查询推荐记录")
    @GetMapping(value = "/getByReferrerId")
    public Result<?> getByReferrerId(@RequestParam(name="referrerId",required=true) String referrerId) {
        List<AicgUserReferral> list = aicgUserReferralService.getByReferrerId(referrerId);
        return Result.OK(list);
    }
    
    /**
     * 生成推荐码
     */
    @AutoLog(value = "生成推荐码")
    @ApiOperation(value="生成推荐码", notes="生成推荐码")
    @PostMapping(value = "/generateCode")
    public Result<?> generateReferralCode(@RequestParam(name="userId",required=true) String userId) {
        String referralCode = aicgUserReferralService.generateReferralCode(userId);
        return Result.OK("推荐码生成成功", referralCode);
    }
    
    /**
     * 验证推荐码
     */
    @AutoLog(value = "验证推荐码")
    @ApiOperation(value="验证推荐码", notes="验证推荐码")
    @GetMapping(value = "/validateCode")
    public Result<?> validateReferralCode(@RequestParam(name="referralCode",required=true) String referralCode) {
        boolean isValid = aicgUserReferralService.validateReferralCode(referralCode);
        if (isValid) {
            AicgUserReferral referral = aicgUserReferralService.getByReferralCode(referralCode);
            return Result.OK("推荐码有效", referral);
        } else {
            return Result.error("推荐码无效");
        }
    }
    
    /**
     * 获取推荐统计
     */
    @AutoLog(value = "获取推荐统计")
    @ApiOperation(value="获取推荐统计", notes="获取推荐统计")
    @GetMapping(value = "/getStats")
    public Result<?> getReferralStats(@RequestParam(name="referrerId",required=true) String referrerId) {
        Map<String, Object> stats = aicgUserReferralService.getReferralStats(referrerId);
        return Result.OK(stats);
    }
    
    /**
     * 确认推荐关系
     */
    @AutoLog(value = "确认推荐关系")
    @ApiOperation(value="确认推荐关系", notes="确认推荐关系")
    @PostMapping(value = "/confirm")
    public Result<?> confirmReferral(@RequestParam(name="refereeId",required=true) String refereeId,
                                    @RequestParam(name="rechargeAmount",required=true) java.math.BigDecimal rechargeAmount) {
        boolean success = aicgUserReferralService.confirmReferral(refereeId, rechargeAmount, "system");
        if (success) {
            return Result.OK("确认成功!");
        } else {
            return Result.error("确认失败!");
        }
    }
    
    /**
     * 标记为已奖励
     */
    @AutoLog(value = "标记为已奖励")
    @ApiOperation(value="标记为已奖励", notes="标记为已奖励")
    @PostMapping(value = "/markRewarded")
    public Result<?> markAsRewarded(@RequestParam(name="id",required=true) String id) {
        boolean success = aicgUserReferralService.markAsRewarded(id, "system");
        if (success) {
            return Result.OK("标记成功!");
        } else {
            return Result.error("标记失败!");
        }
    }
}
