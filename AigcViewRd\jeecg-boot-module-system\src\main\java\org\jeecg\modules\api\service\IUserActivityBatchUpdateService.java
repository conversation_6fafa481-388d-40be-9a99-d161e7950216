package org.jeecg.modules.api.service;

import org.jeecg.modules.api.dto.UserActivityUpdateDTO;

import java.util.Map;

/**
 * @Description: 用户活跃状态批量更新服务接口
 * @Author: AigcView
 * @Date: 2025-01-30
 * @Version: V1.0
 */
public interface IUserActivityBatchUpdateService {

    /**
     * 添加用户活跃状态更新到队列
     * @param updateDTO 更新数据
     * @return 是否添加成功
     */
    boolean addToUpdateQueue(UserActivityUpdateDTO updateDTO);

    /**
     * 异步批量处理
     */
    void processBatchAsync();

    /**
     * 同步批量处理
     */
    void processBatchSync();

    /**
     * 执行批量更新
     * @param batchData 批量数据
     * @return 成功更新的数量
     */
    int executeBatchUpdate(java.util.List<UserActivityUpdateDTO> batchData);

    /**
     * 获取队列大小
     * @return 当前队列大小
     */
    int getQueueSize();

    /**
     * 获取性能统计信息
     * @return 性能统计
     */
    Map<String, Object> getPerformanceStats();

    /**
     * 强制执行批量更新
     * @return 处理的数据量
     */
    int forceBatchUpdate();

    /**
     * 清空队列
     * @return 清空的数据量
     */
    int clearQueue();

    /**
     * 添加用户登录状态更新
     * @param userId 用户ID
     * @param sessionId 会话ID
     * @param ipAddress IP地址
     * @param userAgent 用户代理
     * @return 是否添加成功
     */
    default boolean addLoginUpdate(String userId, String sessionId, String ipAddress, String userAgent) {
        UserActivityUpdateDTO dto = UserActivityUpdateDTO.createLoginUpdate(userId, sessionId, ipAddress, userAgent);
        return addToUpdateQueue(dto);
    }

    /**
     * 添加用户登出状态更新
     * @param userId 用户ID
     * @param sessionId 会话ID
     * @return 是否添加成功
     */
    default boolean addLogoutUpdate(String userId, String sessionId) {
        UserActivityUpdateDTO dto = UserActivityUpdateDTO.createLogoutUpdate(userId, sessionId);
        return addToUpdateQueue(dto);
    }

    /**
     * 添加用户活跃状态更新
     * @param userId 用户ID
     * @param sessionId 会话ID
     * @param apiPath API路径
     * @param ipAddress IP地址
     * @param userAgent 用户代理
     * @return 是否添加成功
     */
    default boolean addActivityUpdate(String userId, String sessionId, String apiPath, String ipAddress, String userAgent) {
        UserActivityUpdateDTO dto = UserActivityUpdateDTO.createActivityUpdate(userId, sessionId, apiPath, ipAddress, userAgent);
        return addToUpdateQueue(dto);
    }
}
