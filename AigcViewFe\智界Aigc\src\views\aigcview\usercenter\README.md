# 智界Aigc 个人中心组件

## 📋 功能概述

智界Aigc个人中心是一个完整的用户管理界面，提供以下核心功能：

- **个人信息管理**：头像展示、昵称编辑、基本信息查看
- **账户余额管理**：余额显示、充值功能、消费统计
- **API密钥管理**：密钥显示/隐藏、重新生成功能
- **兑换码系统**：兑换码使用、兑换记录查看
- **会员信息**：会员等级显示、到期时间管理
- **交易记录**：完整的消费记录、充值历史、余额变动明细

## 🗂️ 文件结构

```
usercenter/
├── UserCenter.vue          # 主组件文件
├── route.js                # 路由配置示例
├── README.md              # 使用说明文档
└── (未来可扩展)
    ├── components/        # 子组件
    ├── mixins/           # 混入
    └── utils/            # 工具函数
```

## 🚀 快速开始

### 1. 路由配置

在您的路由文件中添加个人中心路由：

```javascript
// router/index.js
import UserCenter from '@/views/aigcview/usercenter/UserCenter'

const routes = [
  {
    path: '/aigcview/usercenter',
    name: 'UserCenter',
    component: UserCenter,
    meta: {
      title: '个人中心',
      requireAuth: true
    }
  }
]
```

### 2. 菜单配置

在菜单配置中添加个人中心入口：

```javascript
// config/router.config.js
{
  path: '/aigcview',
  name: 'AigcView',
  component: 'layouts/RouteView',
  meta: { title: '智界Aigc', icon: 'robot' },
  children: [
    {
      path: '/aigcview/usercenter',
      name: 'UserCenter',
      component: 'aigcview/usercenter/UserCenter',
      meta: { title: '个人中心', icon: 'user' }
    }
  ]
}
```

### 3. API接口配置

确保后端API接口正常运行，主要接口包括：

- `GET /demo/userprofile/current` - 获取用户扩展信息
- `GET /demo/userprofile/transactions` - 获取交易记录
- `POST /demo/userprofile/updateNickname` - 更新昵称
- `POST /demo/userprofile/regenerateApiKey` - 重新生成API密钥
- `POST /demo/exchangecode/use` - 使用兑换码
- `GET /demo/exchangecode/userUsedCodes` - 获取兑换记录

## 🎨 组件特性

### 响应式设计
- 支持桌面端、平板、手机等多种设备
- 自适应布局，在不同屏幕尺寸下都有良好的显示效果

### 用户体验优化
- 加载状态提示
- 错误处理和友好的错误提示
- 操作确认机制
- 数据实时更新

### 安全特性
- API密钥脱敏显示
- 重要操作需要确认
- 权限控制支持

### 性能优化
- 组件懒加载
- 数据分页加载
- 防抖处理

## 🔧 自定义配置

### 主题定制

您可以通过修改CSS变量来自定义主题：

```css
:root {
  --primary-color: #1890ff;
  --success-color: #52c41a;
  --warning-color: #faad14;
  --error-color: #f5222d;
}
```

### 功能开关

在组件中可以通过props控制功能显示：

```vue
<UserCenter 
  :show-recharge="true"
  :show-exchange="true"
  :show-api-key="true"
  :show-member-info="true"
/>
```

### 数据格式

组件期望的数据格式：

```javascript
// 用户扩展信息
userProfile: {
  id: "string",
  userId: "string",
  nickname: "string",
  accountBalance: 100.00,
  apiKey: "ak_xxxxxxxx",
  totalConsumption: 50.00,
  totalRecharge: 150.00,
  memberLevel: 1, // 1-普通用户，2-VIP，3-SVIP
  memberExpireTime: "2025-12-31 23:59:59",
  status: 1 // 1-正常，2-冻结
}

// 交易记录
transaction: {
  id: "string",
  userId: "string",
  transactionType: 1, // 1-消费，2-充值，3-退款，4-兑换
  amount: 50.00,
  balanceBefore: 100.00,
  balanceAfter: 50.00,
  description: "AI对话消费",
  transactionTime: "2025-06-14 10:00:00"
}
```

## 🐛 常见问题

### Q: 页面加载时显示"加载用户信息失败"
A: 请检查：
1. 用户是否已登录
2. 后端API接口是否正常
3. 网络连接是否正常
4. 用户是否有访问权限

### Q: API密钥不显示
A: 请确认：
1. 用户是否已初始化扩展信息
2. 后端是否正确返回API密钥
3. 用户是否有查看API密钥的权限

### Q: 兑换码使用失败
A: 请检查：
1. 兑换码是否正确
2. 兑换码是否已过期
3. 兑换码是否已被使用
4. 用户是否有使用兑换码的权限

### Q: 充值功能不可用
A: 充值功能需要：
1. 集成支付接口
2. 配置支付参数
3. 实现支付回调处理

## 🔄 更新日志

### v1.0.0 (2025-06-14)
- ✅ 初始版本发布
- ✅ 基本个人信息管理
- ✅ 账户余额显示
- ✅ API密钥管理
- ✅ 兑换码系统
- ✅ 交易记录查询
- ✅ 响应式设计
- ✅ 完整的错误处理

### 计划中的功能
- 🔄 支付接口集成
- 🔄 会员权益详细展示
- 🔄 积分系统
- 🔄 消息通知
- 🔄 数据导出功能
- 🔄 多语言支持

## 📞 技术支持

如果您在使用过程中遇到问题，请：

1. 查看本文档的常见问题部分
2. 检查浏览器控制台的错误信息
3. 确认后端API接口状态
4. 联系开发团队获取支持

## 📄 许可证

本组件遵循项目整体许可证协议。

---

**开发团队**: 智界Aigc开发组  
**最后更新**: 2025-06-14  
**版本**: v1.0.0
