package org.jeecg.modules.system.service.impl;

import com.github.houbb.sensitive.word.bs.SensitiveWordBs;
import com.github.houbb.sensitive.word.core.SensitiveWordHelper;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.demo.userprofile.entity.AicgUserProfile;
import org.jeecg.modules.demo.userprofile.mapper.AicgUserProfileMapper;
import org.jeecg.modules.system.service.ISensitiveWordService;
import org.jeecg.modules.system.service.ISysSensitiveWordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * @Description: 敏感词服务实现类
 * @Author: jeecg-boot
 * @Date: 2025-06-22
 * @Version: V1.0
 */
@Service
@Slf4j
public class SensitiveWordServiceImpl implements ISensitiveWordService {

    @Autowired
    private AicgUserProfileMapper userProfileMapper;

    @Autowired
    private ISysSensitiveWordService sysSensitiveWordService;

    private SensitiveWordBs sensitiveWordBs;

    // 🛡️ 安全防护：频率限制
    private final ConcurrentHashMap<String, AtomicLong> ipRequestCount = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<String, Long> ipLastRequestTime = new ConcurrentHashMap<>();
    
    // 频率限制配置
    private static final int MAX_REQUESTS_PER_MINUTE = 30; // 每分钟最多30次请求
    private static final long MINUTE_IN_MILLIS = 60 * 1000L;

    @PostConstruct
    public void init() {
        try {
            log.info("🔍 开始初始化敏感词库...");
            
            // 初始化敏感词检测器，启用多种检测策略
            this.sensitiveWordBs = SensitiveWordBs.newInstance()
                    .ignoreCase(true)           // 忽略大小写
                    .ignoreWidth(true)          // 忽略全角半角
                    .ignoreNumStyle(true)       // 忽略数字样式
                    .ignoreChineseStyle(true)   // 忽略中文样式
                    .ignoreEnglishStyle(true)   // 忽略英文样式
                    .ignoreRepeat(true)         // 忽略重复字符
                    .enableNumCheck(false)      // 不检测纯数字（避免误判）
                    .enableEmailCheck(false)    // 不检测邮箱（昵称场景不需要）
                    .enableUrlCheck(false)      // 不检测URL（昵称场景不需要）
                    .enableWordCheck(true)      // 启用敏感词检测
                    .init();
            
            log.info("✅ 敏感词库初始化完成");
        } catch (Exception e) {
            log.error("❌ 敏感词库初始化失败：{}", e.getMessage(), e);
            // 初始化失败时使用默认的简单实现
            this.sensitiveWordBs = null;
        }
    }

    @Override
    public boolean containsSensitiveWord(String text) {
        if (oConvertUtils.isEmpty(text)) {
            return false;
        }
        
        try {
            if (sensitiveWordBs != null) {
                return sensitiveWordBs.contains(text);
            } else {
                // 降级处理：使用默认实现
                return SensitiveWordHelper.contains(text);
            }
        } catch (Exception e) {
            log.error("敏感词检测异常：{}", e.getMessage(), e);
            return false; // 异常时不阻塞业务
        }
    }

    @Override
    public String findFirstSensitiveWord(String text) {
        if (oConvertUtils.isEmpty(text)) {
            return null;
        }
        
        try {
            if (sensitiveWordBs != null) {
                return sensitiveWordBs.findFirst(text);
            } else {
                // 降级处理：使用默认实现
                return SensitiveWordHelper.findFirst(text);
            }
        } catch (Exception e) {
            log.error("敏感词查找异常：{}", e.getMessage(), e);
            return null;
        }
    }

    @Override
    public String replaceSensitiveWord(String text, char replacement) {
        if (oConvertUtils.isEmpty(text)) {
            return text;
        }

        try {
            // 根据文档，SensitiveWordBs和SensitiveWordHelper都只有replace(String)方法
            // 需要使用自定义替换策略来实现字符替换
            if (sensitiveWordBs != null) {
                // 如果是默认的*替换，直接使用replace()
                if (replacement == '*') {
                    return sensitiveWordBs.replace(text);
                } else {
                    // 对于其他字符，先用*替换，再手动替换为目标字符
                    String result = sensitiveWordBs.replace(text);
                    return result.replace('*', replacement);
                }
            } else {
                // 降级处理：使用默认实现
                return SensitiveWordHelper.replace(text, replacement);
            }
        } catch (Exception e) {
            log.error("敏感词替换异常：{}", e.getMessage(), e);
            return text; // 异常时返回原文本
        }
    }

    @Override
    public String replaceSensitiveWord(String text) {
        if (oConvertUtils.isEmpty(text)) {
            return text;
        }

        try {
            if (sensitiveWordBs != null) {
                return sensitiveWordBs.replace(text);
            } else {
                // 降级处理：使用默认实现
                return SensitiveWordHelper.replace(text);
            }
        } catch (Exception e) {
            log.error("敏感词替换异常：{}", e.getMessage(), e);
            return text; // 异常时返回原文本
        }
    }

    @Override
    public NicknameValidationResult validateNickname(String nickname, String currentUserId) {
        // 🛡️ 安全检查：参数验证
        if (oConvertUtils.isEmpty(nickname)) {
            return NicknameValidationResult.fail("昵称不能为空", "FORMAT");
        }

        // 🛡️ 安全检查：长度限制
        if (nickname.length() < 2) {
            return NicknameValidationResult.fail("昵称长度不能少于2个字符", "LENGTH");
        }
        if (nickname.length() > 20) {
            return NicknameValidationResult.fail("昵称长度不能超过20个字符", "LENGTH");
        }

        // 🛡️ 安全检查：格式验证（只允许中文、英文、数字、下划线）
        if (!nickname.matches("^[\\u4e00-\\u9fa5a-zA-Z0-9_]+$")) {
            return NicknameValidationResult.fail("昵称只能包含中文、英文、数字和下划线", "FORMAT");
        }

        // 🛡️ 安全检查：敏感词检测 - 使用新的数据库敏感词服务
        try {
            ISysSensitiveWordService.SensitiveWordCheckResult checkResult =
                sysSensitiveWordService.checkSensitiveWords(nickname, currentUserId, "昵称校验", null);

            if (checkResult.isHasSensitiveWord()) {
                log.warn("🚨 昵称包含敏感词 - 昵称: {}, 敏感词: {}, 用户: {}",
                    nickname, checkResult.getSensitiveWords(), currentUserId);
                return NicknameValidationResult.fail(
                    "昵称包含敏感词：" + String.join(", ", checkResult.getSensitiveWords()),
                    "SENSITIVE");
            }
        } catch (Exception e) {
            log.warn("数据库敏感词检测失败，使用备用检测 - 昵称: {}, 用户: {}, 错误: {}",
                nickname, currentUserId, e.getMessage());

            // 降级处理：使用houbb库检测
            try {
                if (containsSensitiveWord(nickname)) {
                    String sensitiveWord = findFirstSensitiveWord(nickname);
                    log.warn("🚨 检测到敏感词昵称（备用检测）：{}, 敏感词：{}", nickname, sensitiveWord);
                    return NicknameValidationResult.fail("昵称包含敏感词，请重新输入", "SENSITIVE");
                }
            } catch (Exception e2) {
                log.error("备用敏感词检测也失败 - 昵称: {}, 用户: {}", nickname, currentUserId, e2);
                // 敏感词检测异常时，为了安全起见，拒绝该昵称
                return NicknameValidationResult.fail("昵称检测失败，请重试", "SENSITIVE");
            }
        }

        // 🛡️ 安全检查：重复性检查
        try {
            List<AicgUserProfile> existingProfiles = userProfileMapper.selectByNickname(nickname);
            if (existingProfiles != null && !existingProfiles.isEmpty()) {
                // 如果是当前用户自己的昵称，则允许
                boolean isCurrentUser = existingProfiles.stream()
                        .anyMatch(profile -> currentUserId != null && currentUserId.equals(profile.getUserId()));
                
                if (!isCurrentUser) {
                    log.warn("🚨 检测到重复昵称：{}", nickname);
                    return NicknameValidationResult.fail("该昵称已被使用，请选择其他昵称", "DUPLICATE");
                }
            }
        } catch (Exception e) {
            log.error("昵称重复性检查异常：{}", e.getMessage(), e);
            return NicknameValidationResult.fail("昵称检查失败，请重试", "DUPLICATE");
        }

        return NicknameValidationResult.success();
    }

    /**
     * 🛡️ 安全防护：检查IP请求频率
     * @param ipAddress IP地址
     * @return 是否允许请求
     */
    public boolean checkRateLimit(String ipAddress) {
        if (oConvertUtils.isEmpty(ipAddress)) {
            return true; // 无法获取IP时允许请求
        }

        long currentTime = System.currentTimeMillis();
        
        // 清理过期的记录
        ipLastRequestTime.entrySet().removeIf(entry -> 
            currentTime - entry.getValue() > MINUTE_IN_MILLIS);
        ipRequestCount.entrySet().removeIf(entry -> 
            !ipLastRequestTime.containsKey(entry.getKey()));

        // 检查当前IP的请求次数
        AtomicLong count = ipRequestCount.computeIfAbsent(ipAddress, k -> new AtomicLong(0));
        Long lastRequestTime = ipLastRequestTime.get(ipAddress);

        if (lastRequestTime == null || currentTime - lastRequestTime > MINUTE_IN_MILLIS) {
            // 重置计数
            count.set(1);
            ipLastRequestTime.put(ipAddress, currentTime);
            return true;
        } else {
            // 增加计数
            long currentCount = count.incrementAndGet();
            ipLastRequestTime.put(ipAddress, currentTime);
            
            if (currentCount > MAX_REQUESTS_PER_MINUTE) {
                log.warn("🚨 IP请求频率过高：{}, 当前次数：{}", ipAddress, currentCount);
                return false;
            }
            return true;
        }
    }
}
