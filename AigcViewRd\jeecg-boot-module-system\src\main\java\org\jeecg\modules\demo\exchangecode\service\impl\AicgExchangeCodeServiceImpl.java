package org.jeecg.modules.demo.exchangecode.service.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Random;

import org.jeecg.modules.demo.exchangecode.entity.AicgExchangeCode;
import org.jeecg.modules.demo.exchangecode.mapper.AicgExchangeCodeMapper;
import org.jeecg.modules.demo.exchangecode.service.IAicgExchangeCodeService;
import org.jeecg.modules.demo.userprofile.service.IAicgUserProfileService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: 兑换码表
 * @Author: jeecg-boot
 * @Date:   2025-06-14
 * @Version: V1.0
 */
@Service
public class AicgExchangeCodeServiceImpl extends ServiceImpl<AicgExchangeCodeMapper, AicgExchangeCode> implements IAicgExchangeCodeService {

    @Autowired
    private IAicgUserProfileService userProfileService;

    @Override
    public AicgExchangeCode getByCode(String code) {
        return baseMapper.getByCode(code);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<AicgExchangeCode> generateExchangeCodes(Integer codeType, BigDecimal value, Date expireTime, String batchNo, Integer count, String operatorId) {
        List<AicgExchangeCode> codes = new ArrayList<>();
        List<String> codeStrings = generateCodeStrings(count);
        
        for (String codeString : codeStrings) {
            AicgExchangeCode exchangeCode = new AicgExchangeCode();
            exchangeCode.setCode(codeString);
            exchangeCode.setCodeType(codeType);
            exchangeCode.setValue(value);
            exchangeCode.setStatus(1); // 未使用
            exchangeCode.setExpireTime(expireTime);
            exchangeCode.setBatchNo(batchNo);
            exchangeCode.setCreateBy(operatorId);
            exchangeCode.setCreateTime(new Date());
            codes.add(exchangeCode);
        }
        
        this.saveBatch(codes);
        return codes;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String useExchangeCode(String code, String userId, String operatorId) {
        // 验证兑换码
        String validateResult = validateExchangeCode(code);
        if (!"valid".equals(validateResult)) {
            return validateResult;
        }
        
        AicgExchangeCode exchangeCode = getByCode(code);
        
        // 使用兑换码
        int result = baseMapper.useExchangeCode(code, userId, operatorId);
        if (result <= 0) {
            return "兑换码使用失败";
        }
        
        // 根据兑换码类型处理
        switch (exchangeCode.getCodeType()) {
            case 1: // 余额
                boolean rechargeResult = userProfileService.recharge(userId, exchangeCode.getValue(), "兑换码充值：" + code, operatorId);
                if (!rechargeResult) {
                    throw new RuntimeException("余额充值失败");
                }
                return "成功兑换余额：" + exchangeCode.getValue() + "元";
            case 2: // 会员
                // TODO: 实现会员兑换逻辑
                return "成功兑换会员权益";
            case 3: // 积分
                // TODO: 实现积分兑换逻辑
                return "成功兑换积分：" + exchangeCode.getValue();
            default:
                return "未知的兑换码类型";
        }
    }

    @Override
    public List<AicgExchangeCode> getUserUsedCodes(String userId) {
        return baseMapper.getUserUsedCodes(userId);
    }

    @Override
    public int updateExpiredCodes() {
        return baseMapper.updateExpiredCodes();
    }

    @Override
    public String validateExchangeCode(String code) {
        if (code == null || code.trim().isEmpty()) {
            return "兑换码不能为空";
        }
        
        AicgExchangeCode exchangeCode = getByCode(code);
        if (exchangeCode == null) {
            return "兑换码不存在";
        }
        
        if (exchangeCode.getStatus() == 2) {
            return "兑换码已使用";
        }
        
        if (exchangeCode.getStatus() == 3) {
            return "兑换码已过期";
        }
        
        if (exchangeCode.getExpireTime() != null && exchangeCode.getExpireTime().before(new Date())) {
            return "兑换码已过期";
        }
        
        return "valid";
    }

    @Override
    public List<String> generateCodeStrings(Integer count) {
        List<String> codes = new ArrayList<>();
        Random random = new Random();
        String chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
        
        for (int i = 0; i < count; i++) {
            StringBuilder code = new StringBuilder();
            code.append("AIGC");
            for (int j = 0; j < 8; j++) {
                code.append(chars.charAt(random.nextInt(chars.length())));
            }
            codes.add(code.toString());
        }
        
        return codes;
    }
}
