package org.jeecg.modules.jianying.config;

import org.jeecg.modules.jianying.interceptor.JianyingApiInterceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * 剪映小助手Web配置
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
@Configuration
public class JianyingWebConfig implements WebMvcConfigurer {
    
    @Autowired
    private JianyingApiInterceptor jianyingApiInterceptor;
    
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(jianyingApiInterceptor)
                .addPathPatterns(
                    "/api/jianying/gen_video"         // 只拦截需要API Token的工具
                )
                .excludePathPatterns(
                    "/api/jianying/health",           // 健康检查接口
                    "/api/jianying/docs/**"           // 文档接口
                );
    }
}
