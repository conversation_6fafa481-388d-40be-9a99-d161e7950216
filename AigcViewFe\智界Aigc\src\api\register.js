import { axios } from '@/utils/request'

/**
 * 注册相关API
 */

// 检查用户名是否可用
export function checkUsername(username, type) {
  return axios({
    url: '/api/auth/checkUsername',
    method: 'get',
    params: { username, type }
  })
}

// 验证邀请码
export function validateInviteCode(code) {
  return axios({
    url: '/api/auth/validateInvite',
    method: 'get',
    params: { code }
  })
}

// 发送短信验证码
export function sendSmsCode(phone, scene = 'register') {
  return axios({
    url: '/api/auth/sendSmsCode',
    method: 'post',
    params: { phone, scene }
  })
}

// 发送邮箱验证码
export function sendEmailCode(email, scene = 'register') {
  return axios({
    url: '/api/auth/sendEmailCode',
    method: 'post',
    params: { email, scene }
  })
}

// 验证验证码
export function verifyCode(target, code, codeType, scene = 'register') {
  return axios({
    url: '/api/auth/verifyCode',
    method: 'post',
    params: { target, code, codeType, scene }
  })
}

// 用户注册
export function register(registerData) {
  return axios({
    url: '/api/auth/register',
    method: 'post',
    data: registerData
  })
}

// 获取图形验证码
export function getCaptcha() {
  return axios({
    url: '/api/auth/captcha',
    method: 'get'
  })
}

// 生成微信登录二维码
export function generateWechatQrCode(scene, inviteCode) {
  return axios({
    url: '/api/auth/wechat/qrcode',
    method: 'get',
    params: { scene, inviteCode }
  })
}

// 检查微信扫码状态
export function checkWechatScanStatus(sceneId) {
  return axios({
    url: '/api/auth/wechat/check',
    method: 'get',
    params: { scene: sceneId }
  })
}
