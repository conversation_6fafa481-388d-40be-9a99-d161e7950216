package org.jeecg.modules.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.jeecg.modules.system.entity.AigcDesktopDownloadLog;

import java.util.List;
import java.util.Map;

/**
 * 桌面应用下载记录Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-12
 */
public interface IAigcDesktopDownloadLogService extends IService<AigcDesktopDownloadLog> {

    /**
     * 记录下载成功日志
     * @param platform 平台类型
     * @param fileName 文件名
     * @param fileDescription 文件描述
     * @param clientIp 客户端IP
     * @param userAgent 用户代理
     * @param signedUrl 签名URL
     * @param responseTime 响应时间
     */
    void logDownloadSuccess(String platform, String fileName, String fileDescription, 
                           String clientIp, String userAgent, String signedUrl, Long responseTime);

    /**
     * 记录下载失败日志
     * @param platform 平台类型
     * @param fileName 文件名
     * @param clientIp 客户端IP
     * @param userAgent 用户代理
     * @param errorMessage 错误信息
     * @param responseTime 响应时间
     */
    void logDownloadFailure(String platform, String fileName, String clientIp, 
                           String userAgent, String errorMessage, Long responseTime);

    /**
     * 获取今日下载统计
     * @return 统计数据
     */
    List<Map<String, Object>> getTodayDownloadStats();

    /**
     * 获取下载趋势数据
     * @param days 天数
     * @return 趋势数据
     */
    List<Map<String, Object>> getDownloadTrend(int days);

    /**
     * 检查IP下载频率
     * @param clientIp 客户端IP
     * @param minutes 分钟数
     * @return 下载次数
     */
    int checkDownloadFrequency(String clientIp, int minutes);

    /**
     * 获取平台统计数据
     * @return 平台统计
     */
    List<Map<String, Object>> getPlatformStats();

    /**
     * 获取最近下载记录
     * @param limit 限制数量
     * @return 下载记录
     */
    List<AigcDesktopDownloadLog> getRecentDownloads(int limit);

    /**
     * 🔥 获取最近下载记录（分页）
     * @param pageNo 页码
     * @param pageSize 每页大小
     * @return 分页下载记录
     */
    IPage<AigcDesktopDownloadLog> getRecentDownloadsPage(int pageNo, int pageSize);
}
