import { TOSBase, type TOSConstructorOptions } from './methods/base';
import { getObjectV2 } from './methods/object/getObject';
import headObject from './methods/object/headObject';
import { listObjectVersions, listObjects } from './methods/object/listObjects';
import downloadFile from './methods/object/downloadFile';
import { listObjectsType2 } from './methods/object/listObjectsType2';
export interface ShareLinkClientOptions extends Omit<TOSConstructorOptions, 'region' | 'accessKeyId' | 'accessKeySecret' | 'endpoint' | 'bucket'> {
    policyUrl: string;
}
/** @private unstable */
export declare class ShareLinkClient extends TOSBase {
    shareLinkClientOpts: ShareLinkClientOptions;
    private parsedPolicyUrlVal;
    modifyAxiosInst(): void;
    constructor(_opts: ShareLinkClientOptions);
    private initParsedPolicyUrlVal;
    headObject: typeof headObject;
    getObjectV2: typeof getObjectV2;
    listObjects: typeof listObjects;
    listObjectsType2: typeof listObjectsType2;
    listObjectVersions: typeof listObjectVersions;
    downloadFile: typeof downloadFile;
}
