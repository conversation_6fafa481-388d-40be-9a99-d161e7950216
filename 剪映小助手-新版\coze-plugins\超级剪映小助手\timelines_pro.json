{"openapi": "3.0.0", "info": {"title": "剪映小助手_超级剪映小助手 - 智能时间线生成", "description": "智能时间线生成", "version": "1.0.0"}, "servers": [{"url": "https://www.aigcview.com"}], "paths": {"/jeecg-boot/api/jianyingpro/generate_timelines": {"post": {"summary": "智能时间线生成", "description": "智能时间线生成", "operationId": "timelines_pro", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"access_key": {"type": "string", "description": "访问密钥（必填）", "example": "JianyingAPI_2025_SecureAccess_AigcView", "default": "JianyingAPI_2025_SecureAccess_AigcView"}, "audio_urls": {"type": "array", "items": {"type": "string"}, "description": "音频文件URL列表（音频模式）", "example": ["https://example.com/audio1.mp3"]}, "duration": {"type": "integer", "description": "总时长（微秒，自定义模式）", "example": 30000000}, "num": {"type": "integer", "description": "分段数量（自定义模式）", "example": 5}, "start": {"type": "integer", "description": "起始时间（微秒，可选）", "example": 0}}, "required": ["access_key"], "additionalProperties": false}}}}, "responses": {"200": {"description": "时间线生成成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "description": "操作是否成功"}, "message": {"type": "string", "description": "操作结果消息"}, "data": {"type": "object", "properties": {"timelines": {"type": "array", "items": {"type": "object", "properties": {"start": {"type": "integer"}, "end": {"type": "integer"}}}, "description": "生成的时间线数组"}, "all_timelines": {"type": "array", "items": {"type": "object", "properties": {"start": {"type": "integer"}, "end": {"type": "integer"}}}, "description": "整体时间线数组"}}}}, "required": ["success", "message", "data"]}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "description": "错误信息", "example": "参数冲突: 不能同时提供音频模式参数(audio_urls)和自定义模式参数(duration+num)，请选择其中一种模式"}, "error_code": {"type": "string", "description": "错误码", "example": "PARAM_CONFLICT_004"}, "error_message": {"type": "string", "description": "详细错误消息", "example": "参数冲突"}, "error_details": {"type": "string", "description": "错误解决方案", "example": "不能同时提供音频模式参数(audio_urls)和自定义模式参数(duration+num)，请选择其中一种模式"}}, "required": ["error", "error_code"]}}}}, "500": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "description": "错误信息", "example": "时间线生成失败: 服务器内部错误"}}, "required": ["error"]}}}}}}}}}