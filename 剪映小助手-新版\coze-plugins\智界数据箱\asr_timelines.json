{"openapi": "3.0.0", "info": {"title": "剪映小助手_智界数据箱 - 语音识别时间线", "description": "字幕识别获取时间线列表", "version": "1.0.0"}, "servers": [{"url": "https://www.aigcview.com"}], "paths": {"/jeecg-boot/api/jianying/asr_timelines": {"post": {"summary": "生成语音识别时间线", "description": "字幕识别获取时间线列表", "operationId": "asr_timelines", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"access_key": {"type": "string", "description": "访问密钥（必填）", "example": "JianyingAPI_2025_SecureAccess_AigcView", "default": "JianyingAPI_2025_SecureAccess_AigcView"}, "content_chunks": {"type": "array", "description": "字幕识别插件的输出内容（必填）", "items": {"type": "object"}, "example": [{"text": "这是识别的文本", "start_time": 0, "end_time": 2000}]}}, "required": ["access_key", "content_chunks"], "additionalProperties": false}}}}, "responses": {"200": {"description": "成功生成语音识别时间线", "content": {"application/json": {"schema": {"type": "object", "properties": {"timelines": {"type": "string", "description": "时间线数组（JSON格式字符串），格式：[{\"start\":0,\"end\":1920000}]"}}, "required": ["timelines"]}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "description": "操作是否成功", "example": false}, "error": {"type": "string", "description": "错误信息"}, "code": {"type": "string", "description": "错误代码"}}, "required": ["success", "error"]}}}}, "500": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "description": "操作是否成功", "example": false}, "error": {"type": "string", "description": "错误信息"}, "code": {"type": "string", "description": "错误代码"}}, "required": ["success", "error"]}}}}}}}}}