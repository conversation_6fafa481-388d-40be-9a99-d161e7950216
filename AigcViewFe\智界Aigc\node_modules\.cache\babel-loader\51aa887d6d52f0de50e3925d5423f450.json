{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\agent\\AgentDetailPage.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\agent\\AgentDetailPage.vue", "mtime": 1754511159792}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["import _regeneratorRuntime from \"D:/AigcView_zj/AigcViewFe/\\u667A\\u754CAigc/node_modules/@babel/runtime/regenerator\";\n\nfunction asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }\n\nfunction _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"next\", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"throw\", err); } _next(undefined); }); }; }\n\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nimport { getUserRole } from '@/utils/roleUtils';\nexport default {\n  name: 'AgentDetailPage',\n  data: function data() {\n    return {\n      // 权限状态\n      isCheckingPermission: true,\n      isLoggedIn: false,\n      isPurchased: false,\n      // 数据状态\n      agentDetail: {},\n      workflowList: [],\n      workflowLoading: false,\n      // 视频状态\n      videoError: false,\n      videoCollapsed: false,\n      // 使用说明状态\n      guideCollapsed: false,\n      // 导航状态\n      activeNavItem: 'agent-info',\n      navItems: [{\n        id: 'agent-info',\n        title: '智能体信息'\n      }, {\n        id: 'demo-video',\n        title: '演示视频'\n      }, {\n        id: 'usage-guide',\n        title: '使用说明'\n      }, {\n        id: 'workflow-list',\n        title: '工作流列表'\n      }],\n      // 默认图片\n      defaultAvatar: '/default-avatar.png',\n      // 使用项目统一的默认头像路径\n      defaultCreatorAvatar: '/default-avatar.png' // 创作者也使用相同的默认头像\n\n    };\n  },\n  computed: {\n    agentId: function agentId() {\n      return this.$route.query.agentId;\n    },\n    authorTypeClass: function authorTypeClass() {\n      if (this.agentDetail.authorType === '1' || this.agentDetail.authorType === 1) {\n        return 'official';\n      }\n\n      return 'creator';\n    },\n    authorTypeText: function authorTypeText() {\n      if (this.agentDetail.authorType === '1' || this.agentDetail.authorType === 1) {\n        return '官方';\n      }\n\n      return '创作者';\n    }\n  },\n  created: function () {\n    var _created = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee() {\n      return _regeneratorRuntime.wrap(function _callee$(_context) {\n        while (1) {\n          switch (_context.prev = _context.next) {\n            case 0:\n              _context.next = 2;\n              return this.checkPermissions();\n\n            case 2:\n              if (!(this.isLoggedIn && this.isPurchased)) {\n                _context.next = 5;\n                break;\n              }\n\n              _context.next = 5;\n              return this.loadAgentData();\n\n            case 5:\n            case \"end\":\n              return _context.stop();\n          }\n        }\n      }, _callee, this);\n    }));\n\n    function created() {\n      return _created.apply(this, arguments);\n    }\n\n    return created;\n  }(),\n  mounted: function mounted() {\n    var _this = this;\n\n    // 添加滚动监听\n    window.addEventListener('scroll', this.handleScroll); // 初始化当前区域 - 确保默认为第一个区域\n\n    this.$nextTick(function () {\n      // 页面加载时默认激活第一个区域\n      _this.activeNavItem = 'agent-info';\n      console.log('📍 页面加载，默认激活:', _this.activeNavItem); // 延迟执行滚动检测，避免初始化时的错误激活\n\n      setTimeout(function () {\n        _this.handleScroll();\n      }, 500);\n    });\n  },\n  beforeDestroy: function beforeDestroy() {\n    // 移除滚动监听\n    window.removeEventListener('scroll', this.handleScroll);\n  },\n  methods: {\n    // 权限检查\n    checkPermissions: function () {\n      var _checkPermissions = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee2() {\n        var userRole;\n        return _regeneratorRuntime.wrap(function _callee2$(_context2) {\n          while (1) {\n            switch (_context2.prev = _context2.next) {\n              case 0:\n                _context2.prev = 0;\n                console.log('🔍 开始权限检查, agentId:', this.agentId);\n                console.log('🚨 DEBUG: AgentDetailPage 代码已更新 - 版本2024'); // 检查登录状态 - getUserRole是异步函数\n\n                _context2.next = 5;\n                return getUserRole();\n\n              case 5:\n                userRole = _context2.sent;\n                this.isLoggedIn = !!userRole;\n                console.log('🔍 登录状态检查:', {\n                  userRole: userRole,\n                  isLoggedIn: this.isLoggedIn\n                });\n\n                if (this.isLoggedIn) {\n                  _context2.next = 12;\n                  break;\n                }\n\n                console.log('🔍 用户未登录，停止权限检查');\n                this.isCheckingPermission = false;\n                return _context2.abrupt(\"return\");\n\n              case 12:\n                // 检查购买状态 - 使用服务端API验证\n                console.log('🔍 即将调用 checkPurchaseStatus 方法...');\n                _context2.next = 15;\n                return this.checkPurchaseStatus();\n\n              case 15:\n                this.isPurchased = _context2.sent;\n                console.log('🔍 checkPurchaseStatus 方法调用完成，返回值:', this.isPurchased);\n                console.log('🔍 购买状态检查:', {\n                  isPurchased: this.isPurchased,\n                  agentId: this.agentId\n                });\n                this.isCheckingPermission = false;\n                _context2.next = 25;\n                break;\n\n              case 21:\n                _context2.prev = 21;\n                _context2.t0 = _context2[\"catch\"](0);\n                console.error('🔍 权限检查失败:', _context2.t0);\n                this.isCheckingPermission = false;\n\n              case 25:\n              case \"end\":\n                return _context2.stop();\n            }\n          }\n        }, _callee2, this, [[0, 21]]);\n      }));\n\n      function checkPermissions() {\n        return _checkPermissions.apply(this, arguments);\n      }\n\n      return checkPermissions;\n    }(),\n    // 检查购买状态 - 使用服务端API验证\n    checkPurchaseStatus: function () {\n      var _checkPurchaseStatus = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee3() {\n        var response, isPurchased;\n        return _regeneratorRuntime.wrap(function _callee3$(_context3) {\n          while (1) {\n            switch (_context3.prev = _context3.next) {\n              case 0:\n                _context3.prev = 0;\n                console.log('🔍 AgentDetailPage - 验证购买状态, agentId:', this.agentId);\n                _context3.next = 4;\n                return this.$http.get(\"/api/agent/market/purchase/check/\".concat(this.agentId));\n\n              case 4:\n                response = _context3.sent;\n                console.log('🔍 AgentDetailPage - 购买状态API响应:', response);\n\n                if (!(response && response.success)) {\n                  _context3.next = 12;\n                  break;\n                }\n\n                isPurchased = response.result.isPurchased;\n                console.log('✅ AgentDetailPage - 购买状态验证完成:', isPurchased);\n                return _context3.abrupt(\"return\", isPurchased);\n\n              case 12:\n                console.warn('⚠️ AgentDetailPage - 购买状态验证失败');\n                return _context3.abrupt(\"return\", false);\n\n              case 14:\n                _context3.next = 20;\n                break;\n\n              case 16:\n                _context3.prev = 16;\n                _context3.t0 = _context3[\"catch\"](0);\n                console.error('❌ AgentDetailPage - 购买状态验证出错:', _context3.t0);\n                return _context3.abrupt(\"return\", false);\n\n              case 20:\n              case \"end\":\n                return _context3.stop();\n            }\n          }\n        }, _callee3, this, [[0, 16]]);\n      }));\n\n      function checkPurchaseStatus() {\n        return _checkPurchaseStatus.apply(this, arguments);\n      }\n\n      return checkPurchaseStatus;\n    }(),\n    // 加载智能体数据\n    loadAgentData: function () {\n      var _loadAgentData = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee4() {\n        return _regeneratorRuntime.wrap(function _callee4$(_context4) {\n          while (1) {\n            switch (_context4.prev = _context4.next) {\n              case 0:\n                _context4.prev = 0;\n                _context4.next = 3;\n                return this.loadAgentDetail();\n\n              case 3:\n                _context4.next = 9;\n                break;\n\n              case 5:\n                _context4.prev = 5;\n                _context4.t0 = _context4[\"catch\"](0);\n                console.error('加载数据失败:', _context4.t0);\n                this.$message.error('加载数据失败，请稍后重试');\n\n              case 9:\n              case \"end\":\n                return _context4.stop();\n            }\n          }\n        }, _callee4, this, [[0, 5]]);\n      }));\n\n      function loadAgentData() {\n        return _loadAgentData.apply(this, arguments);\n      }\n\n      return loadAgentData;\n    }(),\n    // 加载智能体详情\n    loadAgentDetail: function () {\n      var _loadAgentDetail = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee5() {\n        var response;\n        return _regeneratorRuntime.wrap(function _callee5$(_context5) {\n          while (1) {\n            switch (_context5.prev = _context5.next) {\n              case 0:\n                _context5.prev = 0;\n                console.log('🔍 开始加载智能体详情, agentId:', this.agentId);\n                _context5.next = 4;\n                return this.$http.get(\"/api/agent/market/detail/\".concat(this.agentId));\n\n              case 4:\n                response = _context5.sent;\n                console.log('🔍 智能体详情API响应:', response);\n\n                if (!response.success) {\n                  _context5.next = 13;\n                  break;\n                }\n\n                this.agentDetail = response.result;\n                console.log('🔍 智能体详情加载成功:', this.agentDetail); // 动态设置页面标题\n\n                document.title = \"\".concat(this.agentDetail.agentName || '智能体详情', \" - \\u667A\\u754CAIGC\"); // 🔥 直接从详情接口获取工作流列表，无需单独请求\n\n                if (this.agentDetail.workflowList) {\n                  this.workflowList = this.agentDetail.workflowList;\n                  console.log('🔍 工作流列表已从详情接口获取:', this.workflowList);\n                } else {\n                  this.workflowList = [];\n                }\n\n                _context5.next = 14;\n                break;\n\n              case 13:\n                throw new Error(response.message || '获取智能体详情失败');\n\n              case 14:\n                _context5.next = 21;\n                break;\n\n              case 16:\n                _context5.prev = 16;\n                _context5.t0 = _context5[\"catch\"](0);\n                console.error('🔍 加载智能体详情失败:', _context5.t0);\n                this.$message.error('加载智能体详情失败: ' + _context5.t0.message);\n                throw _context5.t0;\n\n              case 21:\n              case \"end\":\n                return _context5.stop();\n            }\n          }\n        }, _callee5, this, [[0, 16]]);\n      }));\n\n      function loadAgentDetail() {\n        return _loadAgentDetail.apply(this, arguments);\n      }\n\n      return loadAgentDetail;\n    }(),\n    // 🔥 加载工作流列表 - 已废弃，现在直接从智能体详情接口获取\n    // async loadWorkflowList() {\n    //   this.workflowLoading = true\n    //   try {\n    //     console.log('🔍 开始加载工作流列表, agentId:', this.agentId)\n    //     const response = await this.$http.get(`/api/agent/market/${this.agentId}/workflows`)\n    //     console.log('🔍 工作流列表API响应:', response)\n    //     if (response.success) {\n    //       this.workflowList = response.result || []\n    //       console.log('🔍 工作流列表加载成功:', this.workflowList)\n    //     } else {\n    //       throw new Error(response.message || '获取工作流列表失败')\n    //     }\n    //   } catch (error) {\n    //     console.error('🔍 加载工作流列表失败:', error)\n    //     this.$message.error('加载工作流列表失败: ' + error.message)\n    //     this.workflowList = []\n    //   } finally {\n    //     this.workflowLoading = false\n    //   }\n    // },\n    // 导航方法\n    goBack: function goBack() {\n      this.$router.go(-1);\n    },\n    goToLogin: function goToLogin() {\n      this.$router.push({\n        path: '/login',\n        query: {\n          redirect: this.$route.fullPath\n        }\n      });\n    },\n    goToPurchase: function goToPurchase() {\n      this.$router.push('/workflow-center');\n    },\n    // 图片错误处理\n    handleAvatarError: function handleAvatarError(event) {\n      event.target.src = this.defaultAvatar;\n    },\n    handleCreatorAvatarError: function handleCreatorAvatarError(event) {\n      event.target.src = this.defaultCreatorAvatar;\n    },\n    // 工作流图片错误处理\n    handleWorkflowImageError: function handleWorkflowImageError(event) {\n      event.target.src = this.defaultAgentAvatar;\n    },\n    // 视频事件处理\n    onVideoLoadStart: function onVideoLoadStart() {\n      this.videoError = false;\n      console.log('🎬 视频开始加载');\n    },\n    onVideoLoaded: function onVideoLoaded() {\n      console.log('🎬 视频加载完成');\n    },\n    onVideoError: function onVideoError() {\n      this.videoError = true;\n      console.error('🎬 视频加载失败');\n    },\n    onVideoPlay: function onVideoPlay() {\n      console.log('🎬 视频开始播放');\n    },\n    onVideoPause: function onVideoPause() {\n      console.log('🎬 视频暂停播放');\n    },\n    // 切换视频折叠状态\n    toggleVideoCollapse: function toggleVideoCollapse() {\n      this.videoCollapsed = !this.videoCollapsed;\n      console.log('🎬 视频折叠状态:', this.videoCollapsed ? '折叠' : '展开');\n    },\n    // 切换使用说明折叠状态\n    toggleGuideCollapse: function toggleGuideCollapse() {\n      this.guideCollapsed = !this.guideCollapsed;\n      console.log('📖 使用说明折叠状态:', this.guideCollapsed ? '折叠' : '展开');\n    },\n    // 处理使用说明图片加载错误\n    handleGuideImageError: function handleGuideImageError(event) {\n      console.warn('📖 使用说明图片加载失败:', event.target.src);\n      event.target.style.display = 'none';\n    },\n    // 预览图片\n    previewImage: function previewImage(imageUrl) {\n      // 禁用页面滚动\n      document.body.style.overflow = 'hidden'; // 缩放和位置状态\n\n      var scale = 1;\n      var translateX = 0;\n      var translateY = 0;\n      var isDragging = false;\n      var lastX = 0;\n      var lastY = 0;\n      var animationId = null; // 创建图片预览模态框\n\n      var modal = document.createElement('div');\n      modal.style.cssText = \"\\n        position: fixed;\\n        top: 0;\\n        left: 0;\\n        width: 100%;\\n        height: 100%;\\n        background: rgba(0, 0, 0, 0.9);\\n        display: flex;\\n        align-items: center;\\n        justify-content: center;\\n        z-index: 9999;\\n        cursor: default;\\n      \";\n      var img = document.createElement('img');\n      img.src = imageUrl;\n      img.style.cssText = \"\\n        max-width: 90%;\\n        max-height: 90%;\\n        object-fit: contain;\\n        border-radius: 8px;\\n        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);\\n        user-select: none;\\n        cursor: grab;\\n      \"; // 添加操作提示\n\n      var tip = document.createElement('div');\n      tip.style.cssText = \"\\n        position: absolute;\\n        top: 20px;\\n        left: 50%;\\n        transform: translateX(-50%);\\n        background: rgba(0, 0, 0, 0.7);\\n        color: white;\\n        padding: 8px 16px;\\n        border-radius: 20px;\\n        font-size: 14px;\\n        z-index: 10000;\\n        pointer-events: none;\\n        opacity: 0.8;\\n      \";\n      tip.textContent = '滚轮缩放 | 拖拽移动 | 双击重置 | ESC关闭'; // 更新图片变换\n\n      var updateTransform = function updateTransform() {\n        var withTransition = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : false;\n        // 拖拽时不使用过渡动画，其他操作使用过渡动画\n        img.style.transition = withTransition ? 'transform 0.3s ease' : 'none';\n        img.style.transform = \"translate(\".concat(translateX, \"px, \").concat(translateY, \"px) scale(\").concat(scale, \")\");\n      };\n\n      modal.appendChild(img);\n      modal.appendChild(tip);\n      document.body.appendChild(modal); // 滚轮缩放功能\n\n      var handleWheel = function handleWheel(e) {\n        e.preventDefault();\n        var delta = e.deltaY > 0 ? -0.1 : 0.1;\n        var newScale = Math.max(0.5, Math.min(5, scale + delta));\n\n        if (newScale !== scale) {\n          scale = newScale;\n          updateTransform(true); // 缩放使用过渡动画\n\n          modal.style.cursor = 'grab'; // 始终显示可拖拽状态\n        }\n      }; // 拖拽功能\n\n\n      var handleMouseDown = function handleMouseDown(e) {\n        if (e.target === img) {\n          e.preventDefault();\n          e.stopPropagation();\n          isDragging = true;\n          lastX = e.clientX;\n          lastY = e.clientY;\n          modal.style.cursor = 'grabbing';\n        }\n      };\n\n      var handleMouseMove = function handleMouseMove(e) {\n        if (isDragging) {\n          e.preventDefault(); // 取消之前的动画帧\n\n          if (animationId) {\n            cancelAnimationFrame(animationId);\n          } // 使用 requestAnimationFrame 优化性能\n\n\n          animationId = requestAnimationFrame(function () {\n            var deltaX = e.clientX - lastX;\n            var deltaY = e.clientY - lastY;\n            translateX += deltaX;\n            translateY += deltaY;\n            lastX = e.clientX;\n            lastY = e.clientY;\n            updateTransform(false); // 拖拽不使用过渡动画，实现即时响应\n          });\n        }\n      };\n\n      var handleMouseUp = function handleMouseUp(e) {\n        if (isDragging) {\n          e.preventDefault();\n          e.stopPropagation();\n          isDragging = false;\n          modal.style.cursor = 'grab'; // 始终显示可拖拽状态\n        }\n      }; // 双击重置\n\n\n      var handleDoubleClick = function handleDoubleClick() {\n        scale = 1;\n        translateX = 0;\n        translateY = 0;\n        updateTransform(true); // 重置使用过渡动画\n\n        modal.style.cursor = 'grab'; // 始终显示可拖拽状态\n      }; // 关闭模态框\n\n\n      var closeModal = function closeModal() {\n        // 恢复页面滚动\n        document.body.style.overflow = ''; // 取消动画帧\n\n        if (animationId) {\n          cancelAnimationFrame(animationId);\n        } // 移除事件监听器\n\n\n        modal.removeEventListener('wheel', handleWheel);\n        modal.removeEventListener('mousedown', handleMouseDown);\n        document.removeEventListener('mousemove', handleMouseMove);\n        document.removeEventListener('mouseup', handleMouseUp);\n        modal.removeEventListener('dblclick', handleDoubleClick);\n        document.removeEventListener('keydown', handleKeyDown); // 移除模态框\n\n        document.body.removeChild(modal);\n      }; // 键盘ESC关闭\n\n\n      var handleKeyDown = function handleKeyDown(e) {\n        if (e.key === 'Escape') {\n          closeModal();\n        }\n      }; // 点击背景关闭（但不包括图片，且不在拖拽状态）\n\n\n      var handleModalClick = function handleModalClick(e) {\n        if (e.target === modal && !isDragging) {\n          closeModal();\n        }\n      }; // 添加事件监听器\n\n\n      modal.addEventListener('wheel', handleWheel);\n      modal.addEventListener('mousedown', handleMouseDown);\n      document.addEventListener('mousemove', handleMouseMove);\n      document.addEventListener('mouseup', handleMouseUp);\n      modal.addEventListener('dblclick', handleDoubleClick);\n      modal.addEventListener('click', handleModalClick);\n      document.addEventListener('keydown', handleKeyDown);\n      console.log('🔍 图片预览:', imageUrl);\n    },\n    // 滚动到指定区域\n    scrollToSection: function scrollToSection(sectionId) {\n      var element = document.getElementById(sectionId);\n\n      if (element) {\n        element.scrollIntoView({\n          behavior: 'smooth',\n          block: 'start'\n        });\n        this.activeNavItem = sectionId;\n        console.log('🎯 滚动到区域:', sectionId);\n      }\n    },\n    // 快速滚动到使用说明\n    scrollToUsageGuide: function scrollToUsageGuide() {\n      this.scrollToSection('usage-guide');\n      console.log('📖 快速跳转到使用说明');\n    },\n    // 监听滚动事件，更新活跃导航项\n    handleScroll: function handleScroll() {\n      var scrollTop = window.pageYOffset || document.documentElement.scrollTop;\n      var windowHeight = window.innerHeight;\n      var documentHeight = document.documentElement.scrollHeight; // 如果在页面顶部，直接激活第一个区域\n\n      if (scrollTop < 100) {\n        if (this.activeNavItem !== 'agent-info') {\n          this.activeNavItem = 'agent-info';\n          console.log('📍 页面顶部，激活:', this.activeNavItem);\n        }\n\n        return;\n      } // 获取所有区域的位置\n\n\n      var sections = this.navItems.map(function (item) {\n        var element = document.getElementById(item.id);\n        return {\n          id: item.id,\n          offsetTop: element ? element.offsetTop - 150 : 0,\n          element: element\n        };\n      }); // 检查是否滚动到页面底部\n\n      var isAtBottom = scrollTop + windowHeight >= documentHeight - 50;\n      var currentSection = sections[0].id;\n\n      if (isAtBottom) {\n        // 如果在页面底部，激活最后一个区域\n        currentSection = sections[sections.length - 1].id;\n      } else {\n        // 找到当前滚动位置对应的区域\n        for (var i = sections.length - 1; i >= 0; i--) {\n          if (scrollTop >= sections[i].offsetTop) {\n            currentSection = sections[i].id;\n            break;\n          }\n        }\n      }\n\n      if (this.activeNavItem !== currentSection) {\n        this.activeNavItem = currentSection;\n        console.log('📍 当前区域:', currentSection, isAtBottom ? '(页面底部)' : '');\n      }\n    },\n    // 工作流下载\n    handleWorkflowDownload: function () {\n      var _handleWorkflowDownload = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee6(workflow) {\n        var downloadUrl, link;\n        return _regeneratorRuntime.wrap(function _callee6$(_context6) {\n          while (1) {\n            switch (_context6.prev = _context6.next) {\n              case 0:\n                _context6.prev = 0;\n                console.log('🔍 开始下载工作流:', workflow); // 检查是否有下载地址\n\n                if (!(!workflow.workflowPackage && !workflow.packagePath)) {\n                  _context6.next = 5;\n                  break;\n                }\n\n                this.$message.error('工作流压缩包地址不存在');\n                return _context6.abrupt(\"return\");\n\n              case 5:\n                // 设置下载状态\n                this.$set(workflow, 'downloading', true); // 🌐 直接使用CDN地址下载，无需调用后端API\n\n                downloadUrl = workflow.workflowPackage || workflow.packagePath;\n                console.log('🔍 使用CDN地址下载工作流:', downloadUrl); // 创建下载链接\n\n                link = document.createElement('a');\n                link.href = downloadUrl;\n                link.download = \"\".concat(workflow.workflowName || '工作流', \".zip\");\n                link.target = '_blank'; // 触发下载\n\n                document.body.appendChild(link);\n                link.click();\n                document.body.removeChild(link);\n                this.$message.success(\"\\u5DE5\\u4F5C\\u6D41 \\\"\".concat(workflow.workflowName, \"\\\" \\u4E0B\\u8F7D\\u5DF2\\u5F00\\u59CB\"));\n                _context6.next = 22;\n                break;\n\n              case 18:\n                _context6.prev = 18;\n                _context6.t0 = _context6[\"catch\"](0);\n                console.error('🔍 工作流下载失败:', _context6.t0);\n                this.$message.error('下载失败: ' + _context6.t0.message);\n\n              case 22:\n                _context6.prev = 22;\n                // 清除下载状态\n                this.$set(workflow, 'downloading', false);\n                return _context6.finish(22);\n\n              case 25:\n              case \"end\":\n                return _context6.stop();\n            }\n          }\n        }, _callee6, this, [[0, 18, 22, 25]]);\n      }));\n\n      function handleWorkflowDownload(_x) {\n        return _handleWorkflowDownload.apply(this, arguments);\n      }\n\n      return handleWorkflowDownload;\n    }()\n  }\n};", {"version": 3, "sources": ["AgentDetailPage.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgRA,SAAA,WAAA,QAAA,mBAAA;AAEA,eAAA;AACA,EAAA,IAAA,EAAA,iBADA;AAEA,EAAA,IAFA,kBAEA;AACA,WAAA;AACA;AACA,MAAA,oBAAA,EAAA,IAFA;AAGA,MAAA,UAAA,EAAA,KAHA;AAIA,MAAA,WAAA,EAAA,KAJA;AAMA;AACA,MAAA,WAAA,EAAA,EAPA;AAQA,MAAA,YAAA,EAAA,EARA;AASA,MAAA,eAAA,EAAA,KATA;AAWA;AACA,MAAA,UAAA,EAAA,KAZA;AAaA,MAAA,cAAA,EAAA,KAbA;AAeA;AACA,MAAA,cAAA,EAAA,KAhBA;AAkBA;AACA,MAAA,aAAA,EAAA,YAnBA;AAoBA,MAAA,QAAA,EAAA,CACA;AAAA,QAAA,EAAA,EAAA,YAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OADA,EAEA;AAAA,QAAA,EAAA,EAAA,YAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAFA,EAGA;AAAA,QAAA,EAAA,EAAA,aAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAHA,EAIA;AAAA,QAAA,EAAA,EAAA,eAAA;AAAA,QAAA,KAAA,EAAA;AAAA,OAJA,CApBA;AA2BA;AACA,MAAA,aAAA,EAAA,qBA5BA;AA4BA;AACA,MAAA,oBAAA,EAAA,qBA7BA,CA6BA;;AA7BA,KAAA;AA+BA,GAlCA;AAoCA,EAAA,QAAA,EAAA;AACA,IAAA,OADA,qBACA;AACA,aAAA,KAAA,MAAA,CAAA,KAAA,CAAA,OAAA;AACA,KAHA;AAKA,IAAA,eALA,6BAKA;AACA,UAAA,KAAA,WAAA,CAAA,UAAA,KAAA,GAAA,IAAA,KAAA,WAAA,CAAA,UAAA,KAAA,CAAA,EAAA;AACA,eAAA,UAAA;AACA;;AACA,aAAA,SAAA;AACA,KAVA;AAYA,IAAA,cAZA,4BAYA;AACA,UAAA,KAAA,WAAA,CAAA,UAAA,KAAA,GAAA,IAAA,KAAA,WAAA,CAAA,UAAA,KAAA,CAAA,EAAA;AACA,eAAA,IAAA;AACA;;AACA,aAAA,KAAA;AACA;AAjBA,GApCA;AAwDA,EAAA,OAxDA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,qBAyDA,KAAA,gBAAA,EAzDA;;AAAA;AAAA,oBA0DA,KAAA,UAAA,IAAA,KAAA,WA1DA;AAAA;AAAA;AAAA;;AAAA;AAAA,qBA2DA,KAAA,aAAA,EA3DA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AA+DA,EAAA,OA/DA,qBA+DA;AAAA;;AACA;AACA,IAAA,MAAA,CAAA,gBAAA,CAAA,QAAA,EAAA,KAAA,YAAA,EAFA,CAGA;;AACA,SAAA,SAAA,CAAA,YAAA;AACA;AACA,MAAA,KAAA,CAAA,aAAA,GAAA,YAAA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,eAAA,EAAA,KAAA,CAAA,aAAA,EAHA,CAKA;;AACA,MAAA,UAAA,CAAA,YAAA;AACA,QAAA,KAAA,CAAA,YAAA;AACA,OAFA,EAEA,GAFA,CAAA;AAGA,KATA;AAUA,GA7EA;AA+EA,EAAA,aA/EA,2BA+EA;AACA;AACA,IAAA,MAAA,CAAA,mBAAA,CAAA,QAAA,EAAA,KAAA,YAAA;AACA,GAlFA;AAoFA,EAAA,OAAA,EAAA;AACA;AACA,IAAA,gBAFA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAIA,gBAAA,OAAA,CAAA,GAAA,CAAA,qBAAA,EAAA,KAAA,OAAA;AACA,gBAAA,OAAA,CAAA,GAAA,CAAA,0CAAA,EALA,CAOA;;AAPA;AAAA,uBAQA,WAAA,EARA;;AAAA;AAQA,gBAAA,QARA;AASA,qBAAA,UAAA,GAAA,CAAA,CAAA,QAAA;AAEA,gBAAA,OAAA,CAAA,GAAA,CAAA,YAAA,EAAA;AACA,kBAAA,QAAA,EAAA,QADA;AAEA,kBAAA,UAAA,EAAA,KAAA;AAFA,iBAAA;;AAXA,oBAgBA,KAAA,UAhBA;AAAA;AAAA;AAAA;;AAiBA,gBAAA,OAAA,CAAA,GAAA,CAAA,iBAAA;AACA,qBAAA,oBAAA,GAAA,KAAA;AAlBA;;AAAA;AAsBA;AACA,gBAAA,OAAA,CAAA,GAAA,CAAA,mCAAA;AAvBA;AAAA,uBAwBA,KAAA,mBAAA,EAxBA;;AAAA;AAwBA,qBAAA,WAxBA;AAyBA,gBAAA,OAAA,CAAA,GAAA,CAAA,oCAAA,EAAA,KAAA,WAAA;AAEA,gBAAA,OAAA,CAAA,GAAA,CAAA,YAAA,EAAA;AACA,kBAAA,WAAA,EAAA,KAAA,WADA;AAEA,kBAAA,OAAA,EAAA,KAAA;AAFA,iBAAA;AAKA,qBAAA,oBAAA,GAAA,KAAA;AAhCA;AAAA;;AAAA;AAAA;AAAA;AAkCA,gBAAA,OAAA,CAAA,KAAA,CAAA,YAAA;AACA,qBAAA,oBAAA,GAAA,KAAA;;AAnCA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAuCA;AACA,IAAA,mBAxCA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA0CA,gBAAA,OAAA,CAAA,GAAA,CAAA,uCAAA,EAAA,KAAA,OAAA;AA1CA;AAAA,uBA2CA,KAAA,KAAA,CAAA,GAAA,4CAAA,KAAA,OAAA,EA3CA;;AAAA;AA2CA,gBAAA,QA3CA;AA4CA,gBAAA,OAAA,CAAA,GAAA,CAAA,iCAAA,EAAA,QAAA;;AA5CA,sBA8CA,QAAA,IAAA,QAAA,CAAA,OA9CA;AAAA;AAAA;AAAA;;AA+CA,gBAAA,WA/CA,GA+CA,QAAA,CAAA,MAAA,CAAA,WA/CA;AAgDA,gBAAA,OAAA,CAAA,GAAA,CAAA,+BAAA,EAAA,WAAA;AAhDA,kDAiDA,WAjDA;;AAAA;AAmDA,gBAAA,OAAA,CAAA,IAAA,CAAA,+BAAA;AAnDA,kDAoDA,KApDA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;AAuDA,gBAAA,OAAA,CAAA,KAAA,CAAA,+BAAA;AAvDA,kDAwDA,KAxDA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AA4DA;AACA,IAAA,aA7DA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uBAgEA,KAAA,eAAA,EAhEA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;AAkEA,gBAAA,OAAA,CAAA,KAAA,CAAA,SAAA;AACA,qBAAA,QAAA,CAAA,KAAA,CAAA,cAAA;;AAnEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAuEA;AACA,IAAA,eAxEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA0EA,gBAAA,OAAA,CAAA,GAAA,CAAA,wBAAA,EAAA,KAAA,OAAA;AA1EA;AAAA,uBA4EA,KAAA,KAAA,CAAA,GAAA,oCAAA,KAAA,OAAA,EA5EA;;AAAA;AA4EA,gBAAA,QA5EA;AA6EA,gBAAA,OAAA,CAAA,GAAA,CAAA,gBAAA,EAAA,QAAA;;AA7EA,qBA+EA,QAAA,CAAA,OA/EA;AAAA;AAAA;AAAA;;AAgFA,qBAAA,WAAA,GAAA,QAAA,CAAA,MAAA;AACA,gBAAA,OAAA,CAAA,GAAA,CAAA,eAAA,EAAA,KAAA,WAAA,EAjFA,CAmFA;;AACA,gBAAA,QAAA,CAAA,KAAA,aAAA,KAAA,WAAA,CAAA,SAAA,IAAA,OAAA,yBApFA,CAsFA;;AACA,oBAAA,KAAA,WAAA,CAAA,YAAA,EAAA;AACA,uBAAA,YAAA,GAAA,KAAA,WAAA,CAAA,YAAA;AACA,kBAAA,OAAA,CAAA,GAAA,CAAA,mBAAA,EAAA,KAAA,YAAA;AACA,iBAHA,MAGA;AACA,uBAAA,YAAA,GAAA,EAAA;AACA;;AA5FA;AAAA;;AAAA;AAAA,sBA8FA,IAAA,KAAA,CAAA,QAAA,CAAA,OAAA,IAAA,WAAA,CA9FA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;AAiGA,gBAAA,OAAA,CAAA,KAAA,CAAA,eAAA;AACA,qBAAA,QAAA,CAAA,KAAA,CAAA,gBAAA,aAAA,OAAA;AAlGA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAuGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA,IAAA,MA9HA,oBA8HA;AACA,WAAA,OAAA,CAAA,EAAA,CAAA,CAAA,CAAA;AACA,KAhIA;AAkIA,IAAA,SAlIA,uBAkIA;AACA,WAAA,OAAA,CAAA,IAAA,CAAA;AACA,QAAA,IAAA,EAAA,QADA;AAEA,QAAA,KAAA,EAAA;AAAA,UAAA,QAAA,EAAA,KAAA,MAAA,CAAA;AAAA;AAFA,OAAA;AAIA,KAvIA;AAyIA,IAAA,YAzIA,0BAyIA;AACA,WAAA,OAAA,CAAA,IAAA,CAAA,kBAAA;AACA,KA3IA;AA6IA;AACA,IAAA,iBA9IA,6BA8IA,KA9IA,EA8IA;AACA,MAAA,KAAA,CAAA,MAAA,CAAA,GAAA,GAAA,KAAA,aAAA;AACA,KAhJA;AAkJA,IAAA,wBAlJA,oCAkJA,KAlJA,EAkJA;AACA,MAAA,KAAA,CAAA,MAAA,CAAA,GAAA,GAAA,KAAA,oBAAA;AACA,KApJA;AAsJA;AACA,IAAA,wBAvJA,oCAuJA,KAvJA,EAuJA;AACA,MAAA,KAAA,CAAA,MAAA,CAAA,GAAA,GAAA,KAAA,kBAAA;AACA,KAzJA;AA2JA;AACA,IAAA,gBA5JA,8BA4JA;AACA,WAAA,UAAA,GAAA,KAAA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,WAAA;AACA,KA/JA;AAiKA,IAAA,aAjKA,2BAiKA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,WAAA;AACA,KAnKA;AAqKA,IAAA,YArKA,0BAqKA;AACA,WAAA,UAAA,GAAA,IAAA;AACA,MAAA,OAAA,CAAA,KAAA,CAAA,WAAA;AACA,KAxKA;AA0KA,IAAA,WA1KA,yBA0KA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,WAAA;AACA,KA5KA;AA8KA,IAAA,YA9KA,0BA8KA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,WAAA;AACA,KAhLA;AAkLA;AACA,IAAA,mBAnLA,iCAmLA;AACA,WAAA,cAAA,GAAA,CAAA,KAAA,cAAA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,YAAA,EAAA,KAAA,cAAA,GAAA,IAAA,GAAA,IAAA;AACA,KAtLA;AAwLA;AACA,IAAA,mBAzLA,iCAyLA;AACA,WAAA,cAAA,GAAA,CAAA,KAAA,cAAA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,cAAA,EAAA,KAAA,cAAA,GAAA,IAAA,GAAA,IAAA;AACA,KA5LA;AA8LA;AACA,IAAA,qBA/LA,iCA+LA,KA/LA,EA+LA;AACA,MAAA,OAAA,CAAA,IAAA,CAAA,gBAAA,EAAA,KAAA,CAAA,MAAA,CAAA,GAAA;AACA,MAAA,KAAA,CAAA,MAAA,CAAA,KAAA,CAAA,OAAA,GAAA,MAAA;AACA,KAlMA;AAoMA;AACA,IAAA,YArMA,wBAqMA,QArMA,EAqMA;AACA;AACA,MAAA,QAAA,CAAA,IAAA,CAAA,KAAA,CAAA,QAAA,GAAA,QAAA,CAFA,CAIA;;AACA,UAAA,KAAA,GAAA,CAAA;AACA,UAAA,UAAA,GAAA,CAAA;AACA,UAAA,UAAA,GAAA,CAAA;AACA,UAAA,UAAA,GAAA,KAAA;AACA,UAAA,KAAA,GAAA,CAAA;AACA,UAAA,KAAA,GAAA,CAAA;AACA,UAAA,WAAA,GAAA,IAAA,CAXA,CAaA;;AACA,UAAA,KAAA,GAAA,QAAA,CAAA,aAAA,CAAA,KAAA,CAAA;AACA,MAAA,KAAA,CAAA,KAAA,CAAA,OAAA;AAcA,UAAA,GAAA,GAAA,QAAA,CAAA,aAAA,CAAA,KAAA,CAAA;AACA,MAAA,GAAA,CAAA,GAAA,GAAA,QAAA;AACA,MAAA,GAAA,CAAA,KAAA,CAAA,OAAA,mOA/BA,CAyCA;;AACA,UAAA,GAAA,GAAA,QAAA,CAAA,aAAA,CAAA,KAAA,CAAA;AACA,MAAA,GAAA,CAAA,KAAA,CAAA,OAAA;AAcA,MAAA,GAAA,CAAA,WAAA,GAAA,4BAAA,CAzDA,CA2DA;;AACA,UAAA,eAAA,GAAA,SAAA,eAAA,GAAA;AAAA,YAAA,cAAA,uEAAA,KAAA;AACA;AACA,QAAA,GAAA,CAAA,KAAA,CAAA,UAAA,GAAA,cAAA,GAAA,qBAAA,GAAA,MAAA;AACA,QAAA,GAAA,CAAA,KAAA,CAAA,SAAA,uBAAA,UAAA,iBAAA,UAAA,uBAAA,KAAA;AACA,OAJA;;AAMA,MAAA,KAAA,CAAA,WAAA,CAAA,GAAA;AACA,MAAA,KAAA,CAAA,WAAA,CAAA,GAAA;AACA,MAAA,QAAA,CAAA,IAAA,CAAA,WAAA,CAAA,KAAA,EApEA,CAsEA;;AACA,UAAA,WAAA,GAAA,SAAA,WAAA,CAAA,CAAA,EAAA;AACA,QAAA,CAAA,CAAA,cAAA;AACA,YAAA,KAAA,GAAA,CAAA,CAAA,MAAA,GAAA,CAAA,GAAA,CAAA,GAAA,GAAA,GAAA;AACA,YAAA,QAAA,GAAA,IAAA,CAAA,GAAA,CAAA,GAAA,EAAA,IAAA,CAAA,GAAA,CAAA,CAAA,EAAA,KAAA,GAAA,KAAA,CAAA,CAAA;;AAEA,YAAA,QAAA,KAAA,KAAA,EAAA;AACA,UAAA,KAAA,GAAA,QAAA;AACA,UAAA,eAAA,CAAA,IAAA,CAAA,CAFA,CAEA;;AACA,UAAA,KAAA,CAAA,KAAA,CAAA,MAAA,GAAA,MAAA,CAHA,CAGA;AACA;AACA,OAVA,CAvEA,CAmFA;;;AACA,UAAA,eAAA,GAAA,SAAA,eAAA,CAAA,CAAA,EAAA;AACA,YAAA,CAAA,CAAA,MAAA,KAAA,GAAA,EAAA;AACA,UAAA,CAAA,CAAA,cAAA;AACA,UAAA,CAAA,CAAA,eAAA;AACA,UAAA,UAAA,GAAA,IAAA;AACA,UAAA,KAAA,GAAA,CAAA,CAAA,OAAA;AACA,UAAA,KAAA,GAAA,CAAA,CAAA,OAAA;AACA,UAAA,KAAA,CAAA,KAAA,CAAA,MAAA,GAAA,UAAA;AACA;AACA,OATA;;AAWA,UAAA,eAAA,GAAA,SAAA,eAAA,CAAA,CAAA,EAAA;AACA,YAAA,UAAA,EAAA;AACA,UAAA,CAAA,CAAA,cAAA,GADA,CAGA;;AACA,cAAA,WAAA,EAAA;AACA,YAAA,oBAAA,CAAA,WAAA,CAAA;AACA,WANA,CAQA;;;AACA,UAAA,WAAA,GAAA,qBAAA,CAAA,YAAA;AACA,gBAAA,MAAA,GAAA,CAAA,CAAA,OAAA,GAAA,KAAA;AACA,gBAAA,MAAA,GAAA,CAAA,CAAA,OAAA,GAAA,KAAA;AACA,YAAA,UAAA,IAAA,MAAA;AACA,YAAA,UAAA,IAAA,MAAA;AACA,YAAA,KAAA,GAAA,CAAA,CAAA,OAAA;AACA,YAAA,KAAA,GAAA,CAAA,CAAA,OAAA;AACA,YAAA,eAAA,CAAA,KAAA,CAAA,CAPA,CAOA;AACA,WARA,CAAA;AASA;AACA,OApBA;;AAsBA,UAAA,aAAA,GAAA,SAAA,aAAA,CAAA,CAAA,EAAA;AACA,YAAA,UAAA,EAAA;AACA,UAAA,CAAA,CAAA,cAAA;AACA,UAAA,CAAA,CAAA,eAAA;AACA,UAAA,UAAA,GAAA,KAAA;AACA,UAAA,KAAA,CAAA,KAAA,CAAA,MAAA,GAAA,MAAA,CAJA,CAIA;AACA;AACA,OAPA,CArHA,CA8HA;;;AACA,UAAA,iBAAA,GAAA,SAAA,iBAAA,GAAA;AACA,QAAA,KAAA,GAAA,CAAA;AACA,QAAA,UAAA,GAAA,CAAA;AACA,QAAA,UAAA,GAAA,CAAA;AACA,QAAA,eAAA,CAAA,IAAA,CAAA,CAJA,CAIA;;AACA,QAAA,KAAA,CAAA,KAAA,CAAA,MAAA,GAAA,MAAA,CALA,CAKA;AACA,OANA,CA/HA,CAuIA;;;AACA,UAAA,UAAA,GAAA,SAAA,UAAA,GAAA;AACA;AACA,QAAA,QAAA,CAAA,IAAA,CAAA,KAAA,CAAA,QAAA,GAAA,EAAA,CAFA,CAIA;;AACA,YAAA,WAAA,EAAA;AACA,UAAA,oBAAA,CAAA,WAAA,CAAA;AACA,SAPA,CASA;;;AACA,QAAA,KAAA,CAAA,mBAAA,CAAA,OAAA,EAAA,WAAA;AACA,QAAA,KAAA,CAAA,mBAAA,CAAA,WAAA,EAAA,eAAA;AACA,QAAA,QAAA,CAAA,mBAAA,CAAA,WAAA,EAAA,eAAA;AACA,QAAA,QAAA,CAAA,mBAAA,CAAA,SAAA,EAAA,aAAA;AACA,QAAA,KAAA,CAAA,mBAAA,CAAA,UAAA,EAAA,iBAAA;AACA,QAAA,QAAA,CAAA,mBAAA,CAAA,SAAA,EAAA,aAAA,EAfA,CAiBA;;AACA,QAAA,QAAA,CAAA,IAAA,CAAA,WAAA,CAAA,KAAA;AACA,OAnBA,CAxIA,CA6JA;;;AACA,UAAA,aAAA,GAAA,SAAA,aAAA,CAAA,CAAA,EAAA;AACA,YAAA,CAAA,CAAA,GAAA,KAAA,QAAA,EAAA;AACA,UAAA,UAAA;AACA;AACA,OAJA,CA9JA,CAoKA;;;AACA,UAAA,gBAAA,GAAA,SAAA,gBAAA,CAAA,CAAA,EAAA;AACA,YAAA,CAAA,CAAA,MAAA,KAAA,KAAA,IAAA,CAAA,UAAA,EAAA;AACA,UAAA,UAAA;AACA;AACA,OAJA,CArKA,CA2KA;;;AACA,MAAA,KAAA,CAAA,gBAAA,CAAA,OAAA,EAAA,WAAA;AACA,MAAA,KAAA,CAAA,gBAAA,CAAA,WAAA,EAAA,eAAA;AACA,MAAA,QAAA,CAAA,gBAAA,CAAA,WAAA,EAAA,eAAA;AACA,MAAA,QAAA,CAAA,gBAAA,CAAA,SAAA,EAAA,aAAA;AACA,MAAA,KAAA,CAAA,gBAAA,CAAA,UAAA,EAAA,iBAAA;AACA,MAAA,KAAA,CAAA,gBAAA,CAAA,OAAA,EAAA,gBAAA;AACA,MAAA,QAAA,CAAA,gBAAA,CAAA,SAAA,EAAA,aAAA;AAEA,MAAA,OAAA,CAAA,GAAA,CAAA,UAAA,EAAA,QAAA;AACA,KA1XA;AA4XA;AACA,IAAA,eA7XA,2BA6XA,SA7XA,EA6XA;AACA,UAAA,OAAA,GAAA,QAAA,CAAA,cAAA,CAAA,SAAA,CAAA;;AACA,UAAA,OAAA,EAAA;AACA,QAAA,OAAA,CAAA,cAAA,CAAA;AACA,UAAA,QAAA,EAAA,QADA;AAEA,UAAA,KAAA,EAAA;AAFA,SAAA;AAIA,aAAA,aAAA,GAAA,SAAA;AACA,QAAA,OAAA,CAAA,GAAA,CAAA,WAAA,EAAA,SAAA;AACA;AACA,KAvYA;AAyYA;AACA,IAAA,kBA1YA,gCA0YA;AACA,WAAA,eAAA,CAAA,aAAA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,cAAA;AACA,KA7YA;AA+YA;AACA,IAAA,YAhZA,0BAgZA;AACA,UAAA,SAAA,GAAA,MAAA,CAAA,WAAA,IAAA,QAAA,CAAA,eAAA,CAAA,SAAA;AACA,UAAA,YAAA,GAAA,MAAA,CAAA,WAAA;AACA,UAAA,cAAA,GAAA,QAAA,CAAA,eAAA,CAAA,YAAA,CAHA,CAKA;;AACA,UAAA,SAAA,GAAA,GAAA,EAAA;AACA,YAAA,KAAA,aAAA,KAAA,YAAA,EAAA;AACA,eAAA,aAAA,GAAA,YAAA;AACA,UAAA,OAAA,CAAA,GAAA,CAAA,aAAA,EAAA,KAAA,aAAA;AACA;;AACA;AACA,OAZA,CAcA;;;AACA,UAAA,QAAA,GAAA,KAAA,QAAA,CAAA,GAAA,CAAA,UAAA,IAAA,EAAA;AACA,YAAA,OAAA,GAAA,QAAA,CAAA,cAAA,CAAA,IAAA,CAAA,EAAA,CAAA;AACA,eAAA;AACA,UAAA,EAAA,EAAA,IAAA,CAAA,EADA;AAEA,UAAA,SAAA,EAAA,OAAA,GAAA,OAAA,CAAA,SAAA,GAAA,GAAA,GAAA,CAFA;AAGA,UAAA,OAAA,EAAA;AAHA,SAAA;AAKA,OAPA,CAAA,CAfA,CAwBA;;AACA,UAAA,UAAA,GAAA,SAAA,GAAA,YAAA,IAAA,cAAA,GAAA,EAAA;AAEA,UAAA,cAAA,GAAA,QAAA,CAAA,CAAA,CAAA,CAAA,EAAA;;AAEA,UAAA,UAAA,EAAA;AACA;AACA,QAAA,cAAA,GAAA,QAAA,CAAA,QAAA,CAAA,MAAA,GAAA,CAAA,CAAA,CAAA,EAAA;AACA,OAHA,MAGA;AACA;AACA,aAAA,IAAA,CAAA,GAAA,QAAA,CAAA,MAAA,GAAA,CAAA,EAAA,CAAA,IAAA,CAAA,EAAA,CAAA,EAAA,EAAA;AACA,cAAA,SAAA,IAAA,QAAA,CAAA,CAAA,CAAA,CAAA,SAAA,EAAA;AACA,YAAA,cAAA,GAAA,QAAA,CAAA,CAAA,CAAA,CAAA,EAAA;AACA;AACA;AACA;AACA;;AAEA,UAAA,KAAA,aAAA,KAAA,cAAA,EAAA;AACA,aAAA,aAAA,GAAA,cAAA;AACA,QAAA,OAAA,CAAA,GAAA,CAAA,UAAA,EAAA,cAAA,EAAA,UAAA,GAAA,QAAA,GAAA,EAAA;AACA;AACA,KA9bA;AAgcA;AACA,IAAA,sBAjcA;AAAA,+GAicA,QAjcA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAmcA,gBAAA,OAAA,CAAA,GAAA,CAAA,aAAA,EAAA,QAAA,EAncA,CAqcA;;AArcA,sBAscA,CAAA,QAAA,CAAA,eAAA,IAAA,CAAA,QAAA,CAAA,WAtcA;AAAA;AAAA;AAAA;;AAucA,qBAAA,QAAA,CAAA,KAAA,CAAA,aAAA;AAvcA;;AAAA;AA2cA;AACA,qBAAA,IAAA,CAAA,QAAA,EAAA,aAAA,EAAA,IAAA,EA5cA,CA8cA;;AACA,gBAAA,WA/cA,GA+cA,QAAA,CAAA,eAAA,IAAA,QAAA,CAAA,WA/cA;AAgdA,gBAAA,OAAA,CAAA,GAAA,CAAA,kBAAA,EAAA,WAAA,EAhdA,CAkdA;;AACA,gBAAA,IAndA,GAmdA,QAAA,CAAA,aAAA,CAAA,GAAA,CAndA;AAodA,gBAAA,IAAA,CAAA,IAAA,GAAA,WAAA;AACA,gBAAA,IAAA,CAAA,QAAA,aAAA,QAAA,CAAA,YAAA,IAAA,KAAA;AACA,gBAAA,IAAA,CAAA,MAAA,GAAA,QAAA,CAtdA,CAwdA;;AACA,gBAAA,QAAA,CAAA,IAAA,CAAA,WAAA,CAAA,IAAA;AACA,gBAAA,IAAA,CAAA,KAAA;AACA,gBAAA,QAAA,CAAA,IAAA,CAAA,WAAA,CAAA,IAAA;AAEA,qBAAA,QAAA,CAAA,OAAA,gCAAA,QAAA,CAAA,YAAA;AA7dA;AAAA;;AAAA;AAAA;AAAA;AAgeA,gBAAA,OAAA,CAAA,KAAA,CAAA,aAAA;AACA,qBAAA,QAAA,CAAA,KAAA,CAAA,WAAA,aAAA,OAAA;;AAjeA;AAAA;AAmeA;AACA,qBAAA,IAAA,CAAA,QAAA,EAAA,aAAA,EAAA,KAAA;AApeA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;AApFA,CAAA", "sourcesContent": ["<template>\n  <div class=\"agent-detail-page\">\n    <!-- 时间轴式导航 -->\n    <div class=\"timeline-nav\">\n      <div class=\"timeline-line\"></div>\n      <div\n        v-for=\"(item, index) in navItems\"\n        :key=\"item.id\"\n        class=\"timeline-node\"\n        :class=\"{ 'active': activeNavItem === item.id }\"\n        :style=\"{ top: (index * 120 + 100) + 'px' }\"\n        @click=\"scrollToSection(item.id)\"\n      >\n        <div class=\"node-dot\"></div>\n        <div class=\"node-label\">{{ item.title }}</div>\n      </div>\n    </div>\n\n    <!-- 权限检查加载状态 -->\n    <div v-if=\"isCheckingPermission\" class=\"permission-loading\">\n      <a-spin size=\"large\">\n        <div class=\"loading-text\">正在验证访问权限...</div>\n      </a-spin>\n    </div>\n\n    <!-- 未登录提示 -->\n    <div v-else-if=\"!isLoggedIn\" class=\"permission-denied\">\n      <div class=\"permission-content\">\n        <a-icon type=\"lock\" class=\"permission-icon\" />\n        <h2>需要登录访问</h2>\n        <p>请先登录您的账户以查看智能体详细信息</p>\n        <a-button type=\"primary\" size=\"large\" @click=\"goToLogin\">\n          立即登录\n        </a-button>\n      </div>\n    </div>\n\n    <!-- 未购买提示 -->\n    <div v-else-if=\"!isPurchased\" class=\"permission-denied\">\n      <div class=\"permission-content\">\n        <a-icon type=\"shopping-cart\" class=\"permission-icon\" />\n        <h2>需要购买访问</h2>\n        <p>您还未购买此智能体，请先购买后再查看详细信息</p>\n        <div class=\"action-buttons\">\n          <a-button size=\"large\" @click=\"goBack\">\n            返回列表\n          </a-button>\n          <a-button type=\"primary\" size=\"large\" @click=\"goToPurchase\">\n            立即购买\n          </a-button>\n        </div>\n      </div>\n    </div>\n\n    <!-- 主要内容区域 -->\n    <div v-else class=\"main-content\">\n      <!-- 页面头部 -->\n      <div class=\"page-header\">\n        <a-button @click=\"goBack\" class=\"back-button\">\n          <a-icon type=\"arrow-left\" />\n          返回\n        </a-button>\n        <div class=\"breadcrumb\">\n          <a-breadcrumb>\n            <a-breadcrumb-item>\n              <router-link to=\"/workflow-center\">工作流中心</router-link>\n            </a-breadcrumb-item>\n            <a-breadcrumb-item>智能体详情</a-breadcrumb-item>\n          </a-breadcrumb>\n        </div>\n      </div>\n\n      <!-- 智能体基本信息 -->\n      <div id=\"agent-info\" class=\"agent-info-section\">\n        <div class=\"agent-basic-info\">\n          <div class=\"agent-avatar\">\n            <img \n              :src=\"agentDetail.agentAvatar || defaultAvatar\" \n              :alt=\"agentDetail.agentName\"\n              @error=\"handleAvatarError\"\n            />\n          </div>\n          <div class=\"agent-details\">\n            <h1 class=\"agent-name\">{{ agentDetail.agentName || '智能体名称' }}</h1>\n            <div class=\"agent-description\">\n              <p>{{ agentDetail.agentDescription || '暂无描述' }}</p>\n            </div>\n            <div class=\"creator-info\">\n              <img\n                :src=\"(agentDetail.creatorInfo && agentDetail.creatorInfo.avatar) || agentDetail.creatorAvatar || defaultCreatorAvatar\"\n                :alt=\"(agentDetail.creatorInfo && agentDetail.creatorInfo.nickname) || agentDetail.creatorNickname || '创作者'\"\n                class=\"creator-avatar\"\n                @error=\"handleCreatorAvatarError\"\n              />\n              <span class=\"creator-name\">{{ (agentDetail.creatorInfo && agentDetail.creatorInfo.nickname) || agentDetail.creatorNickname || '创作者' }}</span>\n              <span class=\"author-type-badge\" :class=\"authorTypeClass\">\n                {{ authorTypeText }}\n              </span>\n            </div>\n          </div>\n        </div>\n\n        <!-- 阅读提示 -->\n        <div class=\"reading-tip\" @click=\"scrollToUsageGuide\">\n          <a-icon type=\"info-circle\" class=\"tip-icon\" />\n          <span class=\"tip-text\">\n            请往下滑或点此快速下滑，仔细阅读工作流导入使用说明\n          </span>\n          <a-icon type=\"arrow-down\" class=\"arrow-icon\" />\n        </div>\n      </div>\n\n      <!-- 演示视频区域 -->\n      <div id=\"demo-video\" v-if=\"agentDetail.demoVideo\" class=\"video-section\">\n        <div class=\"video-header\" @click=\"toggleVideoCollapse\">\n          <h3>演示视频</h3>\n          <a-icon\n            :type=\"videoCollapsed ? 'down' : 'up'\"\n            class=\"collapse-icon\"\n          />\n        </div>\n        <div v-show=\"!videoCollapsed\" class=\"video-container\">\n          <video\n            ref=\"videoPlayer\"\n            :src=\"agentDetail.demoVideo\"\n            controls\n            autoplay\n            muted\n            preload=\"metadata\"\n            @loadstart=\"onVideoLoadStart\"\n            @loadeddata=\"onVideoLoaded\"\n            @error=\"onVideoError\"\n            @play=\"onVideoPlay\"\n            @pause=\"onVideoPause\"\n          >\n            您的浏览器不支持视频播放\n          </video>\n          <div v-if=\"videoError\" class=\"video-error\">\n            <a-icon type=\"exclamation-circle\" />\n            <span>视频加载失败，请稍后重试</span>\n          </div>\n        </div>\n      </div>\n\n      <!-- 工作流导入使用说明 -->\n      <div id=\"usage-guide\" class=\"usage-guide-section\">\n        <div class=\"guide-header\" @click=\"toggleGuideCollapse\">\n          <h3>工作流导入使用说明</h3>\n          <a-icon\n            :type=\"guideCollapsed ? 'down' : 'up'\"\n            class=\"collapse-icon\"\n          />\n        </div>\n        <div v-show=\"!guideCollapsed\" class=\"guide-content\">\n          <div class=\"guide-step\">\n            <div class=\"step-number\">1</div>\n            <div class=\"step-content\">\n              <p class=\"step-text\">点击下载工作流，下载完成是一个压缩包。</p>\n              <div class=\"step-image\" @click=\"previewImage('https://cdn.aigcview.com/defaults/export.png')\">\n                <img\n                  src=\"https://cdn.aigcview.com/defaults/export.png\"\n                  alt=\"下载工作流示例图\"\n                  @error=\"handleGuideImageError\"\n                />\n                <div class=\"image-overlay\">\n                  <a-icon type=\"zoom-in\" />\n                  <span>点击查看大图</span>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <div class=\"guide-step\">\n            <div class=\"step-number\">2</div>\n            <div class=\"step-content\">\n              <p class=\"step-text\">\n                <a\n                  href=\"https://www.coze.cn/space\"\n                  target=\"_blank\"\n                  class=\"coze-link\"\n                >\n                  点此快速跳转Coze工作空间\n                  <a-icon type=\"external-link\" />\n                </a>\n                ，选择需要放置的工作空间，点击右上角导入按钮，选择下载好的工作流压缩包即可完成。<span class=\"highlight-text\">（压缩包无需解压）</span>\n              </p>\n              <div class=\"step-image\" @click=\"previewImage('https://cdn.aigcview.com/defaults/import.png')\">\n                <img\n                  src=\"https://cdn.aigcview.com/defaults/import.png\"\n                  alt=\"导入工作流示例图\"\n                  @error=\"handleGuideImageError\"\n                />\n                <div class=\"image-overlay\">\n                  <a-icon type=\"zoom-in\" />\n                  <span>点击查看大图</span>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- 工作流列表区域 -->\n      <div id=\"workflow-list\" class=\"workflow-section\">\n        <h3>关联工作流 ({{ workflowList.length }}个)</h3>\n        <div v-if=\"workflowLoading\" class=\"workflow-loading\">\n          <a-spin>加载工作流列表...</a-spin>\n        </div>\n        <div v-else-if=\"workflowList.length === 0\" class=\"workflow-empty\">\n          <a-empty description=\"暂无关联工作流\" />\n        </div>\n        <div v-else class=\"workflow-list\">\n          <div\n            v-for=\"workflow in workflowList\"\n            :key=\"workflow.id\"\n            class=\"workflow-card\"\n          >\n            <!-- 工作流头像（使用智能体头像） -->\n            <div class=\"workflow-avatar\">\n              <img\n                :src=\"agentDetail.agentAvatar || defaultAgentAvatar\"\n                :alt=\"workflow.workflowName\"\n                class=\"workflow-image\"\n                @error=\"handleWorkflowImageError\"\n              />\n            </div>\n\n            <div class=\"workflow-info\">\n              <h4 class=\"workflow-name\">{{ workflow.workflowName }}</h4>\n              <p class=\"workflow-description\">{{ workflow.workflowDescription || '暂无描述' }}</p>\n\n              <!-- 🔥 新增：输入参数说明 -->\n              <div class=\"workflow-params\">\n                <div class=\"params-label\">\n                  <a-icon type=\"setting\" />\n                  <span>输入参数说明</span>\n                </div>\n                <div class=\"params-content\">\n                  {{ workflow.inputParamsDesc || '暂无输入参数说明' }}\n                </div>\n              </div>\n            </div>\n\n            <div class=\"workflow-actions\">\n              <a-button\n                type=\"primary\"\n                size=\"default\"\n                class=\"download-btn\"\n                @click=\"handleWorkflowDownload(workflow)\"\n                :loading=\"workflow.downloading\"\n                :disabled=\"!workflow.workflowPackage\"\n              >\n                <a-icon type=\"cloud-download\" />\n                <span>下载工作流</span>\n              </a-button>\n            </div>\n          </div>\n        </div>\n\n        <!-- 使用提示 -->\n        <div v-if=\"workflowList.length > 0\" class=\"workflow-usage-tip\">\n          <a-icon type=\"info-circle\" class=\"tip-icon\" />\n          <span class=\"tip-text\">\n            点击下载工作流是一个压缩包，将下载好的压缩包导入进Coze工作空间即可使用。<span class=\"highlight-text\">（压缩包无需解压）</span>\n          </span>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { getUserRole } from '@/utils/roleUtils'\n\nexport default {\n  name: 'AgentDetailPage',\n  data() {\n    return {\n      // 权限状态\n      isCheckingPermission: true,\n      isLoggedIn: false,\n      isPurchased: false,\n      \n      // 数据状态\n      agentDetail: {},\n      workflowList: [],\n      workflowLoading: false,\n      \n      // 视频状态\n      videoError: false,\n      videoCollapsed: false,\n\n      // 使用说明状态\n      guideCollapsed: false,\n\n      // 导航状态\n      activeNavItem: 'agent-info',\n      navItems: [\n        { id: 'agent-info', title: '智能体信息' },\n        { id: 'demo-video', title: '演示视频' },\n        { id: 'usage-guide', title: '使用说明' },\n        { id: 'workflow-list', title: '工作流列表' }\n      ],\n      \n      // 默认图片\n      defaultAvatar: '/default-avatar.png', // 使用项目统一的默认头像路径\n      defaultCreatorAvatar: '/default-avatar.png' // 创作者也使用相同的默认头像\n    }\n  },\n  \n  computed: {\n    agentId() {\n      return this.$route.query.agentId\n    },\n    \n    authorTypeClass() {\n      if (this.agentDetail.authorType === '1' || this.agentDetail.authorType === 1) {\n        return 'official'\n      }\n      return 'creator'\n    },\n    \n    authorTypeText() {\n      if (this.agentDetail.authorType === '1' || this.agentDetail.authorType === 1) {\n        return '官方'\n      }\n      return '创作者'\n    }\n  },\n  \n  async created() {\n    await this.checkPermissions()\n    if (this.isLoggedIn && this.isPurchased) {\n      await this.loadAgentData()\n    }\n  },\n\n  mounted() {\n    // 添加滚动监听\n    window.addEventListener('scroll', this.handleScroll)\n    // 初始化当前区域 - 确保默认为第一个区域\n    this.$nextTick(() => {\n      // 页面加载时默认激活第一个区域\n      this.activeNavItem = 'agent-info'\n      console.log('📍 页面加载，默认激活:', this.activeNavItem)\n\n      // 延迟执行滚动检测，避免初始化时的错误激活\n      setTimeout(() => {\n        this.handleScroll()\n      }, 500)\n    })\n  },\n\n  beforeDestroy() {\n    // 移除滚动监听\n    window.removeEventListener('scroll', this.handleScroll)\n  },\n  \n  methods: {\n    // 权限检查\n    async checkPermissions() {\n      try {\n        console.log('🔍 开始权限检查, agentId:', this.agentId)\n        console.log('🚨 DEBUG: AgentDetailPage 代码已更新 - 版本2024')\n\n        // 检查登录状态 - getUserRole是异步函数\n        const userRole = await getUserRole()\n        this.isLoggedIn = !!userRole\n\n        console.log('🔍 登录状态检查:', {\n          userRole,\n          isLoggedIn: this.isLoggedIn\n        })\n\n        if (!this.isLoggedIn) {\n          console.log('🔍 用户未登录，停止权限检查')\n          this.isCheckingPermission = false\n          return\n        }\n\n        // 检查购买状态 - 使用服务端API验证\n        console.log('🔍 即将调用 checkPurchaseStatus 方法...')\n        this.isPurchased = await this.checkPurchaseStatus()\n        console.log('🔍 checkPurchaseStatus 方法调用完成，返回值:', this.isPurchased)\n\n        console.log('🔍 购买状态检查:', {\n          isPurchased: this.isPurchased,\n          agentId: this.agentId\n        })\n\n        this.isCheckingPermission = false\n      } catch (error) {\n        console.error('🔍 权限检查失败:', error)\n        this.isCheckingPermission = false\n      }\n    },\n    \n    // 检查购买状态 - 使用服务端API验证\n    async checkPurchaseStatus() {\n      try {\n        console.log('🔍 AgentDetailPage - 验证购买状态, agentId:', this.agentId)\n        const response = await this.$http.get(`/api/agent/market/purchase/check/${this.agentId}`)\n        console.log('🔍 AgentDetailPage - 购买状态API响应:', response)\n\n        if (response && response.success) {\n          const isPurchased = response.result.isPurchased\n          console.log('✅ AgentDetailPage - 购买状态验证完成:', isPurchased)\n          return isPurchased\n        } else {\n          console.warn('⚠️ AgentDetailPage - 购买状态验证失败')\n          return false\n        }\n      } catch (error) {\n        console.error('❌ AgentDetailPage - 购买状态验证出错:', error)\n        return false\n      }\n    },\n    \n    // 加载智能体数据\n    async loadAgentData() {\n      try {\n        // 🔥 只需要加载智能体详情，工作流列表已包含在详情接口中\n        await this.loadAgentDetail()\n      } catch (error) {\n        console.error('加载数据失败:', error)\n        this.$message.error('加载数据失败，请稍后重试')\n      }\n    },\n    \n    // 加载智能体详情\n    async loadAgentDetail() {\n      try {\n        console.log('🔍 开始加载智能体详情, agentId:', this.agentId)\n\n        const response = await this.$http.get(`/api/agent/market/detail/${this.agentId}`)\n        console.log('🔍 智能体详情API响应:', response)\n\n        if (response.success) {\n          this.agentDetail = response.result\n          console.log('🔍 智能体详情加载成功:', this.agentDetail)\n\n          // 动态设置页面标题\n          document.title = `${this.agentDetail.agentName || '智能体详情'} - 智界AIGC`\n\n          // 🔥 直接从详情接口获取工作流列表，无需单独请求\n          if (this.agentDetail.workflowList) {\n            this.workflowList = this.agentDetail.workflowList\n            console.log('🔍 工作流列表已从详情接口获取:', this.workflowList)\n          } else {\n            this.workflowList = []\n          }\n        } else {\n          throw new Error(response.message || '获取智能体详情失败')\n        }\n      } catch (error) {\n        console.error('🔍 加载智能体详情失败:', error)\n        this.$message.error('加载智能体详情失败: ' + error.message)\n        throw error\n      }\n    },\n    \n    // 🔥 加载工作流列表 - 已废弃，现在直接从智能体详情接口获取\n    // async loadWorkflowList() {\n    //   this.workflowLoading = true\n    //   try {\n    //     console.log('🔍 开始加载工作流列表, agentId:', this.agentId)\n    //     const response = await this.$http.get(`/api/agent/market/${this.agentId}/workflows`)\n    //     console.log('🔍 工作流列表API响应:', response)\n    //     if (response.success) {\n    //       this.workflowList = response.result || []\n    //       console.log('🔍 工作流列表加载成功:', this.workflowList)\n    //     } else {\n    //       throw new Error(response.message || '获取工作流列表失败')\n    //     }\n    //   } catch (error) {\n    //     console.error('🔍 加载工作流列表失败:', error)\n    //     this.$message.error('加载工作流列表失败: ' + error.message)\n    //     this.workflowList = []\n    //   } finally {\n    //     this.workflowLoading = false\n    //   }\n    // },\n    \n    // 导航方法\n    goBack() {\n      this.$router.go(-1)\n    },\n    \n    goToLogin() {\n      this.$router.push({\n        path: '/login',\n        query: { redirect: this.$route.fullPath }\n      })\n    },\n    \n    goToPurchase() {\n      this.$router.push('/workflow-center')\n    },\n    \n    // 图片错误处理\n    handleAvatarError(event) {\n      event.target.src = this.defaultAvatar\n    },\n    \n    handleCreatorAvatarError(event) {\n      event.target.src = this.defaultCreatorAvatar\n    },\n\n    // 工作流图片错误处理\n    handleWorkflowImageError(event) {\n      event.target.src = this.defaultAgentAvatar\n    },\n    \n    // 视频事件处理\n    onVideoLoadStart() {\n      this.videoError = false\n      console.log('🎬 视频开始加载')\n    },\n    \n    onVideoLoaded() {\n      console.log('🎬 视频加载完成')\n    },\n    \n    onVideoError() {\n      this.videoError = true\n      console.error('🎬 视频加载失败')\n    },\n    \n    onVideoPlay() {\n      console.log('🎬 视频开始播放')\n    },\n    \n    onVideoPause() {\n      console.log('🎬 视频暂停播放')\n    },\n\n    // 切换视频折叠状态\n    toggleVideoCollapse() {\n      this.videoCollapsed = !this.videoCollapsed\n      console.log('🎬 视频折叠状态:', this.videoCollapsed ? '折叠' : '展开')\n    },\n\n    // 切换使用说明折叠状态\n    toggleGuideCollapse() {\n      this.guideCollapsed = !this.guideCollapsed\n      console.log('📖 使用说明折叠状态:', this.guideCollapsed ? '折叠' : '展开')\n    },\n\n    // 处理使用说明图片加载错误\n    handleGuideImageError(event) {\n      console.warn('📖 使用说明图片加载失败:', event.target.src)\n      event.target.style.display = 'none'\n    },\n\n    // 预览图片\n    previewImage(imageUrl) {\n      // 禁用页面滚动\n      document.body.style.overflow = 'hidden'\n\n      // 缩放和位置状态\n      let scale = 1\n      let translateX = 0\n      let translateY = 0\n      let isDragging = false\n      let lastX = 0\n      let lastY = 0\n      let animationId = null\n\n      // 创建图片预览模态框\n      const modal = document.createElement('div')\n      modal.style.cssText = `\n        position: fixed;\n        top: 0;\n        left: 0;\n        width: 100%;\n        height: 100%;\n        background: rgba(0, 0, 0, 0.9);\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        z-index: 9999;\n        cursor: default;\n      `\n\n      const img = document.createElement('img')\n      img.src = imageUrl\n      img.style.cssText = `\n        max-width: 90%;\n        max-height: 90%;\n        object-fit: contain;\n        border-radius: 8px;\n        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);\n        user-select: none;\n        cursor: grab;\n      `\n\n      // 添加操作提示\n      const tip = document.createElement('div')\n      tip.style.cssText = `\n        position: absolute;\n        top: 20px;\n        left: 50%;\n        transform: translateX(-50%);\n        background: rgba(0, 0, 0, 0.7);\n        color: white;\n        padding: 8px 16px;\n        border-radius: 20px;\n        font-size: 14px;\n        z-index: 10000;\n        pointer-events: none;\n        opacity: 0.8;\n      `\n      tip.textContent = '滚轮缩放 | 拖拽移动 | 双击重置 | ESC关闭'\n\n      // 更新图片变换\n      const updateTransform = (withTransition = false) => {\n        // 拖拽时不使用过渡动画，其他操作使用过渡动画\n        img.style.transition = withTransition ? 'transform 0.3s ease' : 'none'\n        img.style.transform = `translate(${translateX}px, ${translateY}px) scale(${scale})`\n      }\n\n      modal.appendChild(img)\n      modal.appendChild(tip)\n      document.body.appendChild(modal)\n\n      // 滚轮缩放功能\n      const handleWheel = (e) => {\n        e.preventDefault()\n        const delta = e.deltaY > 0 ? -0.1 : 0.1\n        const newScale = Math.max(0.5, Math.min(5, scale + delta))\n\n        if (newScale !== scale) {\n          scale = newScale\n          updateTransform(true) // 缩放使用过渡动画\n          modal.style.cursor = 'grab' // 始终显示可拖拽状态\n        }\n      }\n\n      // 拖拽功能\n      const handleMouseDown = (e) => {\n        if (e.target === img) {\n          e.preventDefault()\n          e.stopPropagation()\n          isDragging = true\n          lastX = e.clientX\n          lastY = e.clientY\n          modal.style.cursor = 'grabbing'\n        }\n      }\n\n      const handleMouseMove = (e) => {\n        if (isDragging) {\n          e.preventDefault()\n\n          // 取消之前的动画帧\n          if (animationId) {\n            cancelAnimationFrame(animationId)\n          }\n\n          // 使用 requestAnimationFrame 优化性能\n          animationId = requestAnimationFrame(() => {\n            const deltaX = e.clientX - lastX\n            const deltaY = e.clientY - lastY\n            translateX += deltaX\n            translateY += deltaY\n            lastX = e.clientX\n            lastY = e.clientY\n            updateTransform(false) // 拖拽不使用过渡动画，实现即时响应\n          })\n        }\n      }\n\n      const handleMouseUp = (e) => {\n        if (isDragging) {\n          e.preventDefault()\n          e.stopPropagation()\n          isDragging = false\n          modal.style.cursor = 'grab' // 始终显示可拖拽状态\n        }\n      }\n\n      // 双击重置\n      const handleDoubleClick = () => {\n        scale = 1\n        translateX = 0\n        translateY = 0\n        updateTransform(true) // 重置使用过渡动画\n        modal.style.cursor = 'grab' // 始终显示可拖拽状态\n      }\n\n      // 关闭模态框\n      const closeModal = () => {\n        // 恢复页面滚动\n        document.body.style.overflow = ''\n\n        // 取消动画帧\n        if (animationId) {\n          cancelAnimationFrame(animationId)\n        }\n\n        // 移除事件监听器\n        modal.removeEventListener('wheel', handleWheel)\n        modal.removeEventListener('mousedown', handleMouseDown)\n        document.removeEventListener('mousemove', handleMouseMove)\n        document.removeEventListener('mouseup', handleMouseUp)\n        modal.removeEventListener('dblclick', handleDoubleClick)\n        document.removeEventListener('keydown', handleKeyDown)\n\n        // 移除模态框\n        document.body.removeChild(modal)\n      }\n\n      // 键盘ESC关闭\n      const handleKeyDown = (e) => {\n        if (e.key === 'Escape') {\n          closeModal()\n        }\n      }\n\n      // 点击背景关闭（但不包括图片，且不在拖拽状态）\n      const handleModalClick = (e) => {\n        if (e.target === modal && !isDragging) {\n          closeModal()\n        }\n      }\n\n      // 添加事件监听器\n      modal.addEventListener('wheel', handleWheel)\n      modal.addEventListener('mousedown', handleMouseDown)\n      document.addEventListener('mousemove', handleMouseMove)\n      document.addEventListener('mouseup', handleMouseUp)\n      modal.addEventListener('dblclick', handleDoubleClick)\n      modal.addEventListener('click', handleModalClick)\n      document.addEventListener('keydown', handleKeyDown)\n\n      console.log('🔍 图片预览:', imageUrl)\n    },\n\n    // 滚动到指定区域\n    scrollToSection(sectionId) {\n      const element = document.getElementById(sectionId)\n      if (element) {\n        element.scrollIntoView({\n          behavior: 'smooth',\n          block: 'start'\n        })\n        this.activeNavItem = sectionId\n        console.log('🎯 滚动到区域:', sectionId)\n      }\n    },\n\n    // 快速滚动到使用说明\n    scrollToUsageGuide() {\n      this.scrollToSection('usage-guide')\n      console.log('📖 快速跳转到使用说明')\n    },\n\n    // 监听滚动事件，更新活跃导航项\n    handleScroll() {\n      const scrollTop = window.pageYOffset || document.documentElement.scrollTop\n      const windowHeight = window.innerHeight\n      const documentHeight = document.documentElement.scrollHeight\n\n      // 如果在页面顶部，直接激活第一个区域\n      if (scrollTop < 100) {\n        if (this.activeNavItem !== 'agent-info') {\n          this.activeNavItem = 'agent-info'\n          console.log('📍 页面顶部，激活:', this.activeNavItem)\n        }\n        return\n      }\n\n      // 获取所有区域的位置\n      const sections = this.navItems.map(item => {\n        const element = document.getElementById(item.id)\n        return {\n          id: item.id,\n          offsetTop: element ? element.offsetTop - 150 : 0,\n          element: element\n        }\n      })\n\n      // 检查是否滚动到页面底部\n      const isAtBottom = scrollTop + windowHeight >= documentHeight - 50\n\n      let currentSection = sections[0].id\n\n      if (isAtBottom) {\n        // 如果在页面底部，激活最后一个区域\n        currentSection = sections[sections.length - 1].id\n      } else {\n        // 找到当前滚动位置对应的区域\n        for (let i = sections.length - 1; i >= 0; i--) {\n          if (scrollTop >= sections[i].offsetTop) {\n            currentSection = sections[i].id\n            break\n          }\n        }\n      }\n\n      if (this.activeNavItem !== currentSection) {\n        this.activeNavItem = currentSection\n        console.log('📍 当前区域:', currentSection, isAtBottom ? '(页面底部)' : '')\n      }\n    },\n\n    // 工作流下载\n    async handleWorkflowDownload(workflow) {\n      try {\n        console.log('🔍 开始下载工作流:', workflow)\n\n        // 检查是否有下载地址\n        if (!workflow.workflowPackage && !workflow.packagePath) {\n          this.$message.error('工作流压缩包地址不存在')\n          return\n        }\n\n        // 设置下载状态\n        this.$set(workflow, 'downloading', true)\n\n        // 🌐 直接使用CDN地址下载，无需调用后端API\n        const downloadUrl = workflow.workflowPackage || workflow.packagePath\n        console.log('🔍 使用CDN地址下载工作流:', downloadUrl)\n\n        // 创建下载链接\n        const link = document.createElement('a')\n        link.href = downloadUrl\n        link.download = `${workflow.workflowName || '工作流'}.zip`\n        link.target = '_blank'\n\n        // 触发下载\n        document.body.appendChild(link)\n        link.click()\n        document.body.removeChild(link)\n\n        this.$message.success(`工作流 \"${workflow.workflowName}\" 下载已开始`)\n\n      } catch (error) {\n        console.error('🔍 工作流下载失败:', error)\n        this.$message.error('下载失败: ' + error.message)\n      } finally {\n        // 清除下载状态\n        this.$set(workflow, 'downloading', false)\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"less\" scoped>\n.agent-detail-page {\n  min-height: 100vh;\n  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);\n  position: relative;\n\n  // 时间轴式导航\n  .timeline-nav {\n    position: fixed;\n    left: calc(50% - 650px);\n    top: 200px;\n    z-index: 1000;\n\n    .timeline-line {\n      position: absolute;\n      left: 50%;\n      top: 0;\n      transform: translateX(-50%);\n      width: 2px;\n      height: 580px;\n      background: linear-gradient(to bottom, #e6f4ff 0%, #1890ff 50%, #e6f4ff 100%);\n      border-radius: 1px;\n    }\n\n    .timeline-node {\n      position: absolute;\n      left: 50%;\n      transform: translateX(-50%);\n      cursor: pointer;\n      transition: all 0.3s ease;\n\n      .node-label {\n        position: absolute;\n        right: 20px;\n        top: 50%;\n        transform: translateY(-50%);\n        font-size: 13px;\n        color: #8c8c8c;\n        font-weight: 500;\n        white-space: nowrap;\n        transition: all 0.3s ease;\n        opacity: 0.8;\n        text-align: right;\n      }\n\n      .node-dot {\n        width: 12px;\n        height: 12px;\n        border-radius: 50%;\n        background: #d9d9d9;\n        border: 2px solid white;\n        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);\n        transition: all 0.3s ease;\n        position: relative;\n        z-index: 2;\n      }\n\n      &:hover {\n        .node-dot {\n          background: #40a9ff;\n          transform: scale(1.3);\n          box-shadow: 0 4px 16px rgba(24, 144, 255, 0.4);\n        }\n\n        .node-label {\n          color: #40a9ff;\n          opacity: 1;\n        }\n      }\n\n      &.active {\n        .node-dot {\n          background: #1890ff;\n          transform: scale(1.4);\n          box-shadow: 0 4px 16px rgba(24, 144, 255, 0.6);\n          border-color: #1890ff;\n        }\n\n        .node-label {\n          color: #1890ff;\n          font-weight: 600;\n          opacity: 1;\n        }\n      }\n    }\n  }\n\n  // 权限检查加载状态\n  .permission-loading {\n    display: flex;\n    justify-content: center;\n    align-items: center;\n    height: 100vh;\n\n    .loading-text {\n      margin-top: 16px;\n      font-size: 16px;\n      color: #666;\n    }\n  }\n\n  // 权限拒绝页面\n  .permission-denied {\n    display: flex;\n    justify-content: center;\n    align-items: center;\n    height: 100vh;\n\n    .permission-content {\n      text-align: center;\n      padding: 48px;\n      background: white;\n      border-radius: 16px;\n      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\n      max-width: 400px;\n\n      .permission-icon {\n        font-size: 64px;\n        color: #1890ff;\n        margin-bottom: 24px;\n      }\n\n      h2 {\n        font-size: 24px;\n        margin-bottom: 16px;\n        color: #262626;\n      }\n\n      p {\n        font-size: 16px;\n        color: #666;\n        margin-bottom: 32px;\n        line-height: 1.6;\n      }\n\n      .action-buttons {\n        display: flex;\n        gap: 16px;\n        justify-content: center;\n      }\n    }\n  }\n\n  // 主要内容区域\n  .main-content {\n    max-width: 1200px;\n    margin: 0 auto;\n    padding: 24px;\n    position: relative;\n\n    // 页面头部\n    .page-header {\n      display: flex;\n      align-items: center;\n      padding: 20px 0;\n\n      .back-button {\n        margin-right: 20px;\n        border-radius: 8px;\n        height: 40px;\n        padding: 0 16px;\n        font-size: 15px;\n        font-weight: 500;\n        border: 1px solid #d9d9d9;\n        background: white;\n        color: #595959;\n        transition: all 0.3s ease;\n\n        &:hover {\n          border-color: #1890ff;\n          color: #1890ff;\n          background: #f6f9ff;\n        }\n\n        .anticon {\n          margin-right: 6px;\n          font-size: 14px;\n        }\n      }\n\n      .breadcrumb {\n        flex: 1;\n\n        /deep/ .ant-breadcrumb {\n          font-size: 16px;\n\n          .ant-breadcrumb-link {\n            color: #8c8c8c;\n            font-weight: 400;\n            transition: color 0.3s ease;\n\n            &:hover {\n              color: #1890ff;\n            }\n          }\n\n          .ant-breadcrumb-separator {\n            color: #d9d9d9;\n            margin: 0 12px;\n          }\n\n          .ant-breadcrumb-link a {\n            color: #1890ff;\n            text-decoration: none;\n            font-weight: 500;\n\n            &:hover {\n              color: #096dd9;\n            }\n          }\n        }\n      }\n    }\n\n    // 智能体信息区域\n    .agent-info-section {\n      background: white;\n      border-radius: 16px;\n      padding: 32px;\n      margin-bottom: 24px;\n      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);\n\n      .agent-basic-info {\n        display: flex;\n        gap: 24px;\n\n        .agent-avatar {\n          flex-shrink: 0;\n\n          img {\n            width: 120px;\n            height: 120px;\n            border-radius: 16px;\n            object-fit: cover;\n            border: 3px solid #f0f0f0;\n          }\n        }\n\n        .agent-details {\n          flex: 1;\n\n          .agent-name {\n            font-size: 28px;\n            font-weight: 600;\n            margin-bottom: 16px;\n            color: #262626;\n          }\n\n          .creator-info {\n            display: flex;\n            align-items: center;\n            margin-bottom: 16px;\n\n            .creator-avatar {\n              width: 32px;\n              height: 32px;\n              border-radius: 50%;\n              margin-right: 8px;\n              object-fit: cover;\n            }\n\n            .creator-name {\n              font-size: 16px;\n              color: #595959;\n              margin-right: 12px;\n            }\n\n            .author-type-badge {\n              padding: 4px 12px;\n              border-radius: 12px;\n              font-size: 12px;\n              font-weight: 500;\n\n              &.official {\n                background: linear-gradient(135deg, #52c41a 0%, #389e0d 100%);\n                color: white;\n              }\n\n              &.creator {\n                background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);\n                color: white;\n              }\n            }\n          }\n\n          .agent-description {\n            margin-bottom: 20px;\n\n            p {\n              font-size: 16px;\n              line-height: 1.6;\n              color: #595959;\n            }\n          }\n\n          .agent-stats {\n            display: flex;\n            gap: 32px;\n\n            .stat-item {\n              display: flex;\n              flex-direction: column;\n\n              .stat-label {\n                font-size: 14px;\n                color: #8c8c8c;\n                margin-bottom: 4px;\n              }\n\n              .stat-value {\n                font-size: 20px;\n                font-weight: 600;\n                color: #1890ff;\n              }\n            }\n          }\n        }\n      }\n\n      // 阅读提示\n      .reading-tip {\n        margin-top: 24px;\n        padding: 16px 20px;\n        background: linear-gradient(135deg, #fff7e6 0%, #ffecc7 100%);\n        border: 1px solid #ffd591;\n        border-radius: 12px;\n        display: flex;\n        align-items: center;\n        gap: 12px;\n        animation: tipPulse 2s ease-in-out infinite;\n        cursor: pointer;\n        transition: all 0.3s ease;\n\n        &:hover {\n          background: linear-gradient(135deg, #fff1d6 0%, #ffe7b3 100%);\n          border-color: #ffb84d;\n          transform: translateY(-2px);\n          box-shadow: 0 4px 12px rgba(255, 181, 77, 0.3);\n        }\n\n        &:active {\n          transform: translateY(0);\n          box-shadow: 0 2px 6px rgba(255, 181, 77, 0.2);\n        }\n\n        .tip-icon {\n          color: #fa8c16;\n          font-size: 16px;\n          flex-shrink: 0;\n        }\n\n        .tip-text {\n          color: #262626;\n          font-size: 14px;\n          line-height: 1.6;\n          font-weight: 500;\n          flex: 1;\n        }\n\n        .arrow-icon {\n          color: #fa8c16;\n          font-size: 16px;\n          animation: arrowBounce 1.5s ease-in-out infinite;\n        }\n      }\n    }\n\n    // 视频区域\n    .video-section {\n      background: white;\n      border-radius: 16px;\n      padding: 32px;\n      margin-bottom: 24px;\n      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);\n\n      .video-header {\n        display: flex;\n        align-items: center;\n        justify-content: space-between;\n        cursor: pointer;\n        user-select: none;\n        transition: all 0.3s ease;\n        padding: 8px 0;\n        border-radius: 8px;\n\n        &:hover {\n          background-color: #f5f5f5;\n          padding: 8px 12px;\n        }\n\n        h3 {\n          font-size: 20px;\n          font-weight: 600;\n          margin: 0;\n          color: #262626;\n        }\n\n        .collapse-icon {\n          font-size: 16px;\n          color: #8c8c8c;\n          transition: all 0.3s ease;\n\n          &:hover {\n            color: #1890ff;\n          }\n        }\n      }\n\n      .video-container {\n        position: relative;\n        margin-top: 20px;\n        transition: all 0.3s ease;\n\n        video {\n          width: 100%;\n          max-height: 500px;\n          border-radius: 12px;\n          background: #000;\n        }\n\n        .video-error {\n          position: absolute;\n          top: 50%;\n          left: 50%;\n          transform: translate(-50%, -50%);\n          color: #ff4d4f;\n          font-size: 16px;\n\n          .anticon {\n            margin-right: 8px;\n          }\n        }\n      }\n    }\n\n    // 使用说明区域\n    .usage-guide-section {\n      background: white;\n      border-radius: 16px;\n      padding: 32px;\n      margin-bottom: 24px;\n      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);\n\n      .guide-header {\n        display: flex;\n        align-items: center;\n        justify-content: space-between;\n        cursor: pointer;\n        user-select: none;\n        transition: all 0.3s ease;\n        padding: 8px 0;\n        border-radius: 8px;\n\n        &:hover {\n          background-color: #f5f5f5;\n          padding: 8px 12px;\n        }\n\n        h3 {\n          font-size: 20px;\n          font-weight: 600;\n          margin: 0;\n          color: #262626;\n        }\n\n        .collapse-icon {\n          font-size: 16px;\n          color: #8c8c8c;\n          transition: all 0.3s ease;\n\n          &:hover {\n            color: #1890ff;\n          }\n        }\n      }\n\n      .guide-content {\n        margin-top: 24px;\n\n        .guide-step {\n          display: flex;\n          align-items: flex-start;\n          gap: 16px;\n          margin-bottom: 32px;\n\n          &:last-child {\n            margin-bottom: 0;\n          }\n\n          .step-number {\n            width: 32px;\n            height: 32px;\n            background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);\n            color: white;\n            border-radius: 50%;\n            display: flex;\n            align-items: center;\n            justify-content: center;\n            font-weight: 600;\n            font-size: 16px;\n            flex-shrink: 0;\n            margin-top: 0;\n          }\n\n          .step-content {\n            flex: 1;\n\n            .step-text {\n              font-size: 15px;\n              line-height: 1.6;\n              color: #262626;\n              margin-bottom: 16px;\n\n              .coze-link {\n                color: #1890ff;\n                text-decoration: none;\n                font-weight: 500;\n                transition: color 0.3s ease;\n\n                &:hover {\n                  color: #096dd9;\n                  text-decoration: underline;\n                }\n\n                .anticon {\n                  margin-left: 4px;\n                  font-size: 12px;\n                }\n              }\n\n              .highlight-text {\n                color: #ff4d4f;\n                font-weight: 600;\n                background: linear-gradient(135deg, #fff2f0 0%, #ffece8 100%);\n                padding: 2px 6px;\n                border-radius: 4px;\n                border: 1px solid #ffccc7;\n              }\n            }\n\n            .step-image {\n              border-radius: 12px;\n              overflow: hidden;\n              border: 1px solid #f0f0f0;\n              box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n              position: relative;\n              cursor: pointer;\n              transition: all 0.3s ease;\n\n              &:hover {\n                box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);\n                transform: translateY(-2px);\n\n                .image-overlay {\n                  opacity: 1;\n                }\n\n                img {\n                  transform: scale(1.05);\n                }\n              }\n\n              img {\n                width: 100%;\n                height: auto;\n                display: block;\n                transition: transform 0.3s ease;\n              }\n\n              .image-overlay {\n                position: absolute;\n                top: 0;\n                left: 0;\n                right: 0;\n                bottom: 0;\n                background: rgba(0, 0, 0, 0.6);\n                display: flex;\n                flex-direction: column;\n                align-items: center;\n                justify-content: center;\n                opacity: 0;\n                transition: opacity 0.3s ease;\n                color: white;\n                font-size: 14px;\n\n                .anticon {\n                  font-size: 24px;\n                  margin-bottom: 8px;\n                }\n\n                span {\n                  font-weight: 500;\n                }\n              }\n            }\n          }\n        }\n      }\n    }\n\n    // 工作流区域\n    .workflow-section {\n      background: white;\n      border-radius: 16px;\n      padding: 32px;\n      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);\n\n      h3 {\n        font-size: 20px;\n        font-weight: 600;\n        margin-bottom: 20px;\n        color: #262626;\n      }\n\n      .workflow-loading {\n        text-align: center;\n        padding: 40px;\n      }\n\n      .workflow-empty {\n        text-align: center;\n        padding: 40px;\n      }\n\n      .workflow-list {\n        display: flex;\n        flex-direction: column;\n        gap: 20px;\n\n        .workflow-card {\n          border: 1px solid #f0f0f0;\n          border-radius: 12px;\n          padding: 24px;\n          transition: all 0.3s ease;\n          display: flex;\n          align-items: center;\n          gap: 20px;\n          width: 100%;\n          min-height: 120px;\n\n          &:hover {\n            border-color: #1890ff;\n            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);\n            transform: translateY(-2px);\n          }\n\n          .workflow-avatar {\n            flex-shrink: 0;\n\n            .workflow-image {\n              width: 80px;\n              height: 80px;\n              border-radius: 12px;\n              object-fit: cover;\n              border: 2px solid #f0f0f0;\n              transition: all 0.3s ease;\n            }\n          }\n\n          .workflow-info {\n            flex: 1;\n            min-width: 0;\n            display: flex;\n            flex-direction: column;\n            justify-content: center;\n\n            .workflow-name {\n              font-size: 18px;\n              font-weight: 600;\n              margin-bottom: 12px;\n              color: #262626;\n              line-height: 1.4;\n              word-wrap: break-word;\n              word-break: break-all;\n            }\n\n            .workflow-description {\n              font-size: 14px;\n              color: #8c8c8c;\n              line-height: 1.5;\n              display: -webkit-box;\n              -webkit-line-clamp: 2;\n              -webkit-box-orient: vertical;\n              overflow: hidden;\n              margin-bottom: 12px;\n            }\n\n            // 🔥 新增：输入参数说明样式\n            .workflow-params {\n              .params-label {\n                display: flex;\n                align-items: center;\n                gap: 6px;\n                font-size: 13px;\n                font-weight: 600;\n                color: #1890ff;\n                margin-bottom: 6px;\n\n                .anticon {\n                  font-size: 12px;\n                  color: #1890ff;\n                }\n              }\n\n              .params-content {\n                font-size: 13px;\n                color: #262626;\n                line-height: 1.4;\n                background: linear-gradient(135deg, #e6f7ff 0%, #f0f9ff 100%);\n                padding: 10px 14px;\n                border-radius: 8px;\n                border: 1px solid #91d5ff;\n                border-left: 4px solid #1890ff;\n                display: -webkit-box;\n                -webkit-line-clamp: 3;\n                -webkit-box-orient: vertical;\n                overflow: hidden;\n                word-wrap: break-word;\n                font-weight: 500;\n                box-shadow: 0 2px 4px rgba(24, 144, 255, 0.1);\n              }\n            }\n          }\n\n          .workflow-actions {\n            flex-shrink: 0;\n            display: flex;\n            align-items: center;\n            justify-content: center;\n\n            .download-btn {\n              background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);\n              border: none;\n              border-radius: 8px;\n              height: 40px;\n              padding: 0 20px;\n              font-weight: 500;\n              box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);\n              transition: all 0.3s ease;\n\n              &:hover {\n                background: linear-gradient(135deg, #40a9ff 0%, #1890ff 100%);\n                box-shadow: 0 4px 12px rgba(24, 144, 255, 0.4);\n                transform: translateY(-1px);\n              }\n\n              &:active {\n                transform: translateY(0);\n              }\n\n              &:disabled {\n                background: #f5f5f5;\n                color: #bfbfbf;\n                box-shadow: none;\n                cursor: not-allowed;\n\n                &:hover {\n                  background: #f5f5f5;\n                  transform: none;\n                }\n              }\n\n              .anticon {\n                margin-right: 6px;\n                font-size: 16px;\n              }\n\n              span {\n                font-size: 14px;\n              }\n            }\n          }\n        }\n      }\n\n      // 使用提示\n      .workflow-usage-tip {\n        margin-top: 24px;\n        padding: 16px 20px;\n        background: linear-gradient(135deg, #f6f9ff 0%, #e6f4ff 100%);\n        border: 1px solid #b3d8ff;\n        border-radius: 12px;\n        display: flex;\n        align-items: flex-start;\n        gap: 12px;\n\n        .tip-icon {\n          color: #1890ff;\n          font-size: 16px;\n          margin-top: 2px;\n          flex-shrink: 0;\n        }\n\n        .tip-text {\n          color: #262626;\n          font-size: 14px;\n          line-height: 1.6;\n          font-weight: 400;\n\n          .highlight-text {\n            color: #ff4d4f;\n            font-weight: 600;\n            background: linear-gradient(135deg, #fff2f0 0%, #ffece8 100%);\n            padding: 2px 6px;\n            border-radius: 4px;\n            border: 1px solid #ffccc7;\n          }\n        }\n      }\n    }\n  }\n}\n\n// 动画效果\n@keyframes tipPulse {\n  0%, 100% {\n    transform: scale(1);\n  }\n  50% {\n    transform: scale(1.02);\n  }\n}\n\n@keyframes arrowBounce {\n  0%, 100% {\n    transform: translateY(0);\n  }\n  50% {\n    transform: translateY(-3px);\n  }\n}\n\n// 响应式设计\n@media (max-width: 768px) {\n  .agent-detail-page {\n    // 移动端时间轴导航\n    .timeline-nav {\n      left: 20px;\n      top: 150px;\n\n      .timeline-line {\n        height: 400px;\n      }\n\n      .timeline-node {\n        .node-label {\n          font-size: 12px;\n          right: 16px;\n        }\n\n        .node-dot {\n          width: 10px;\n          height: 10px;\n        }\n\n        &:hover .node-dot {\n          transform: scale(1.2);\n        }\n\n        &.active .node-dot {\n          transform: scale(1.3);\n        }\n      }\n    }\n\n    .main-content {\n      padding: 16px;\n\n      .page-header {\n        margin-bottom: 20px;\n        padding: 16px 0;\n        flex-direction: column;\n        align-items: flex-start;\n        gap: 12px;\n\n        .back-button {\n          margin-right: 0;\n          margin-bottom: 8px;\n          height: 36px;\n          font-size: 14px;\n        }\n\n        .breadcrumb {\n          /deep/ .ant-breadcrumb {\n            font-size: 14px;\n\n            .ant-breadcrumb-separator {\n              margin: 0 8px;\n            }\n          }\n        }\n      }\n\n      .agent-info-section {\n        padding: 20px;\n\n        .agent-basic-info {\n          flex-direction: column;\n          text-align: center;\n\n          .agent-details {\n            .agent-stats {\n              justify-content: center;\n            }\n          }\n        }\n\n        .reading-tip {\n          margin-top: 20px;\n          padding: 14px 16px;\n          gap: 10px;\n\n          .tip-icon {\n            font-size: 14px;\n          }\n\n          .tip-text {\n            font-size: 13px;\n            line-height: 1.5;\n          }\n\n          .arrow-icon {\n            font-size: 14px;\n          }\n        }\n      }\n\n      .video-section,\n      .usage-guide-section,\n      .workflow-section {\n        padding: 20px;\n      }\n\n      .usage-guide-section {\n        .guide-content {\n          margin-top: 20px;\n\n          .guide-step {\n            gap: 12px;\n            margin-bottom: 24px;\n\n            .step-number {\n              width: 28px;\n              height: 28px;\n              font-size: 14px;\n            }\n\n            .step-content {\n              .step-text {\n                font-size: 14px;\n                margin-bottom: 12px;\n              }\n\n              .step-image {\n                border-radius: 8px;\n              }\n            }\n          }\n        }\n      }\n\n      .workflow-list {\n        gap: 16px;\n\n        .workflow-card {\n          flex-direction: column;\n          text-align: center;\n          gap: 16px;\n          padding: 20px;\n\n          .workflow-avatar {\n            align-self: center;\n\n            .workflow-image {\n              width: 50px;\n              height: 50px;\n            }\n          }\n\n          .workflow-info {\n            .workflow-name {\n              white-space: normal;\n              text-align: center;\n            }\n\n            // 🔥 移动端输入参数说明样式优化\n            .workflow-params {\n              text-align: left;\n              margin-top: 12px;\n\n              .params-label {\n                justify-content: center;\n                font-size: 12px;\n                font-weight: 600;\n                color: #1890ff;\n              }\n\n              .params-content {\n                font-size: 12px;\n                padding: 8px 12px;\n                text-align: left;\n                -webkit-line-clamp: 2; // 移动端显示更少行数\n                color: #262626;\n                font-weight: 500;\n                background: linear-gradient(135deg, #e6f7ff 0%, #f0f9ff 100%);\n                border: 1px solid #91d5ff;\n                border-left: 3px solid #1890ff;\n                box-shadow: 0 1px 3px rgba(24, 144, 255, 0.1);\n              }\n            }\n          }\n\n          .workflow-actions {\n            align-self: center;\n\n            .download-btn {\n              width: 100%;\n              max-width: 200px;\n            }\n          }\n        }\n      }\n\n      .workflow-usage-tip {\n        margin-top: 20px;\n        padding: 14px 16px;\n        gap: 10px;\n\n        .tip-icon {\n          font-size: 14px;\n        }\n\n        .tip-text {\n          font-size: 13px;\n          line-height: 1.5;\n        }\n      }\n    }\n  }\n}\n</style>\n"], "sourceRoot": "src/views/website/agent"}]}