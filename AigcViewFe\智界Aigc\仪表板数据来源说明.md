# 智界Aigc 仪表板数据来源说明

## 📊 当前数据状态

由于项目尚未正式上线，仪表板页面采用了**混合数据模式**：部分数据来自真实数据库，部分数据为演示模拟数据。

## 🔍 详细数据来源分析

### 1. **用户信息区域** ✅ 真实数据
- **数据来源**: `aicg_user_profile` 表
- **包含字段**:
  - 用户昵称、头像
  - 会员等级 (普通/VIP/SVIP)
  - 账户余额
  - 总消费金额、总充值金额

### 2. **API统计数据** 🔄 混合数据
- **真实部分**: 
  - 今日/本月API调用次数 (从 `aicg_user_record` 表统计消费记录)
  - 增长率计算 (基于真实数据对比)
- **模拟部分**:
  ```java
  // 后端代码中的模拟逻辑
  double successRate = 95.0 + Math.random() * 4.0; // 95-99%
  double successRateGrowth = Math.random() * 2.0; // 0-2%
  ```

### 3. **实时统计数据** 🎭 完全模拟
- **在线用户数**: 
  - 普通用户: 50-150 随机数
  - 管理员: 150-350 随机数
- **今日活跃用户数**:
  - 普通用户: 200-500 随机数
  - 管理员: 500-1000 随机数 (如果数据库查询为0)

### 4. **最近调用记录** 🔄 混合数据
- **真实部分**: 从 `aicg_user_record` 表查询最近的消费记录
- **模拟部分**: 如果记录不足5条，自动补充模拟数据
  ```java
  // 如果记录不足，添加一些模拟数据
  while (recentCalls.size() < Math.min(limit, 5)) {
      Map<String, Object> call = new HashMap<>();
      call.put("id", "mock_" + System.currentTimeMillis());
      call.put("apiType", getRandomApiType());
      call.put("success", Math.random() > 0.1); // 90%成功率
      // ...
  }
  ```

### 5. **图表数据** 🎭 完全模拟

#### **API调用趋势图**
```java
// 生成最近24小时的数据
for (int i = 23; i >= 0; i--) {
    int calls = (int)(Math.random() * 50) + 10;        // 个人: 10-60次
    int calls = (int)(Math.random() * 500) + 100;      // 系统: 100-600次
    int success = (int)(calls * (0.9 + Math.random() * 0.09));
    int errors = calls - success;
}
```

#### **API使用分布图**
```java
// 硬编码的固定数值
String[] apiTypes = {"HTML生成", "API验证", "文件访问", "二维码生成", "其他"};
int[] values = {1048, 735, 580, 484, 300};           // 个人级别
int[] values = {10480, 7350, 5800, 4840, 3000};      // 系统级别
```

#### **错误统计图**
```java
// 硬编码的一周错误数据
List<Integer> error4xx = Arrays.asList(5, 3, 8, 2, 6, 4, 1);      // 个人级别
List<Integer> error4xx = Arrays.asList(50, 30, 80, 20, 60, 40, 10); // 系统级别
```

## 🏷️ 前端标识

为了明确告知用户当前数据状态，已在前端添加了**"演示数据"**标签：

- ✅ API调用趋势图 - 橙色标签
- ✅ API使用分布图 - 橙色标签  
- ✅ 错误统计图 - 橙色标签
- ✅ 系统状态监控 - 小号橙色标签

## 🚀 上线后的数据迁移计划

### 阶段1: 基础数据收集
1. **API调用日志表** - 记录每次API调用的详细信息
   ```sql
   CREATE TABLE aicg_api_log (
       id VARCHAR(32) PRIMARY KEY,
       user_id VARCHAR(32),
       api_type VARCHAR(50),
       request_time DATETIME,
       response_time DATETIME,
       status_code INT,
       success BOOLEAN,
       error_message TEXT,
       cost_points DECIMAL(10,2)
   );
   ```

2. **实时在线用户统计** - 基于Session或Redis实现
3. **成功率统计** - 基于API调用日志计算

### 阶段2: 图表数据真实化
1. **趋势图数据** - 从API调用日志按时间聚合
2. **分布图数据** - 从API调用日志按类型统计
3. **错误统计** - 从API调用日志按错误类型和时间统计

### 阶段3: 高级功能
1. **实时数据推送** - WebSocket实现
2. **自定义时间范围** - 支持任意时间段查询
3. **数据导出功能** - 支持Excel/PDF导出

## 💡 当前建议

### 对于开发测试
- 保持现有混合模式，确保功能完整性
- 继续完善真实数据部分的逻辑
- 为模拟数据添加更多变化，提高真实感

### 对于演示展示
- 现有的演示数据已经足够展示系统功能
- 橙色标签清楚标识了数据性质
- 可以向客户说明这是功能演示版本

### 对于正式上线
- 需要实现完整的API调用日志记录
- 需要实现真实的在线用户统计
- 需要移除所有模拟数据生成逻辑

## 📋 技术债务清单

1. **高优先级**:
   - [ ] 实现API调用日志记录
   - [ ] 实现真实成功率统计
   - [ ] 实现真实在线用户统计

2. **中优先级**:
   - [ ] 优化图表数据查询性能
   - [ ] 实现数据缓存机制
   - [ ] 添加数据异常处理

3. **低优先级**:
   - [ ] 实现实时数据推送
   - [ ] 添加更多图表类型
   - [ ] 实现数据导出功能

---

**总结**: 当前仪表板功能完整，用户体验良好，已明确标识演示数据。在项目正式上线前，需要逐步将模拟数据替换为真实数据收集机制。

**更新时间**: 2025-06-14  
**状态**: 混合数据模式 (部分真实 + 部分模拟)
