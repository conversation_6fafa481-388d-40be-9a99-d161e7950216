package org.jeecg.modules.api.config;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.core.env.Environment;

import static org.junit.jupiter.api.Assertions.*;

/**
 * @Description: 配置文件验证测试
 * @Author: AigcView
 * @Date: 2025-01-30
 * @Version: V1.0
 */
@SpringBootTest
@ActiveProfiles("dev")
public class ConfigurationValidationTest {

    @Autowired
    private UserActivityConfig userActivityConfig;

    @Autowired
    private Environment environment;

    @Test
    public void testUserActivityConfigInjection() {
        // 验证配置类是否正确注入
        assertNotNull(userActivityConfig, "用户活跃状态配置应该被正确注入");
    }

    @Test
    public void testBasicConfiguration() {
        // 验证基础配置
        assertTrue(userActivityConfig.getEnabled(), "开发环境应该启用用户活跃状态追踪");
        assertFalse(userActivityConfig.getTestMode(), "开发环境不应该使用测试模式");
        
        // 验证缓存配置
        assertEquals(300, userActivityConfig.getCacheDuration(), "缓存持续时间应该是300秒");
        assertEquals(600, userActivityConfig.getCacheExpireTime(), "缓存过期时间应该是600秒");
        assertEquals("aigc:user:activity:", userActivityConfig.getRedisKeyPrefix(), "Redis键前缀应该正确");
    }

    @Test
    public void testBatchConfiguration() {
        // 验证批量处理配置
        assertEquals(100, userActivityConfig.getBatchSize(), "开发环境批量大小应该是100");
        assertEquals(10, userActivityConfig.getBatchInterval(), "开发环境批量间隔应该是10秒");
    }

    @Test
    public void testPerformanceConfiguration() {
        // 验证性能配置
        assertEquals(1000, userActivityConfig.getPerformanceThreshold(), "性能阈值应该是1000毫秒");
        assertTrue(userActivityConfig.getEnablePerformanceMonitoring(), "应该启用性能监控");
        assertEquals(10, userActivityConfig.getPerformanceMonitoringSampleRate(), "性能监控采样率应该是10");
    }

    @Test
    public void testCriticalApisConfiguration() {
        // 验证核心API配置
        assertNotNull(userActivityConfig.getCriticalApis(), "核心API列表不应该为空");
        assertFalse(userActivityConfig.getCriticalApis().isEmpty(), "核心API列表应该包含API");
        assertTrue(userActivityConfig.getCriticalApis().contains("/payment/"), "应该包含支付API");
        assertTrue(userActivityConfig.getCriticalApis().contains("/login"), "应该包含登录API");
        assertTrue(userActivityConfig.getCriticalApis().contains("/logout"), "应该包含登出API");
    }

    @Test
    public void testRolloutConfiguration() {
        // 验证灰度发布配置
        assertEquals(100, userActivityConfig.getRolloutPercentage(), "开发环境灰度比例应该是100%");
    }

    @Test
    public void testRetryConfiguration() {
        // 验证重试配置
        assertEquals(3, userActivityConfig.getMaxRetryCount(), "最大重试次数应该是3");
        assertEquals(1000, userActivityConfig.getRetryInterval(), "重试间隔应该是1000毫秒");
    }

    @Test
    public void testUserStatusConfiguration() {
        // 验证用户状态管理配置
        assertEquals(60, userActivityConfig.getOfflineUserCleanupInterval(), "离线用户清理间隔应该是60分钟");
        assertEquals(15, userActivityConfig.getUserOfflineThreshold(), "用户离线阈值应该是15分钟");
    }

    @Test
    public void testAsyncConfiguration() {
        // 验证异步处理配置
        assertTrue(userActivityConfig.getEnableAsyncProcessing(), "应该启用异步处理");
        assertEquals(5, userActivityConfig.getAsyncThreadPoolSize(), "异步线程池大小应该是5");
        assertEquals(1000, userActivityConfig.getQueueCapacity(), "队列容量应该是1000");
    }

    @Test
    public void testDegradationConfiguration() {
        // 验证降级机制配置
        assertTrue(userActivityConfig.getEnableDegradation(), "应该启用降级机制");
        assertEquals(300, userActivityConfig.getDegradationRecoveryTime(), "降级恢复时间应该是300秒");
    }

    @Test
    public void testConfigurationMethods() {
        // 验证配置类的业务方法
        String redisKey = userActivityConfig.getRedisKey("test_user");
        assertTrue(redisKey.startsWith("aigc:user:activity:"), "Redis键应该包含正确前缀");
        assertTrue(redisKey.contains("test_user"), "Redis键应该包含用户ID");

        // 验证核心API检测
        assertTrue(userActivityConfig.isCriticalApi("/payment/create"), "支付API应该被识别为核心API");
        assertTrue(userActivityConfig.isCriticalApi("/login"), "登录API应该被识别为核心API");
        assertFalse(userActivityConfig.isCriticalApi("/api/user/profile"), "普通API不应该被识别为核心API");

        // 验证灰度发布逻辑
        assertTrue(userActivityConfig.isInRollout("test_user"), "开发环境应该处理所有用户");
    }

    @Test
    public void testEnvironmentSpecificConfiguration() {
        // 验证环境特定配置
        String activeProfile = environment.getProperty("spring.profiles.active");
        if ("dev".equals(activeProfile)) {
            assertEquals(100, userActivityConfig.getRolloutPercentage(), "开发环境灰度比例应该是100%");
            assertEquals(100, userActivityConfig.getBatchSize(), "开发环境批量大小应该是100");
        }
    }

    @Test
    public void testConfigurationValidation() {
        // 验证配置约束
        assertTrue(userActivityConfig.getBatchSize() > 0, "批量大小应该大于0");
        assertTrue(userActivityConfig.getBatchInterval() > 0, "批量间隔应该大于0");
        assertTrue(userActivityConfig.getPerformanceThreshold() > 0, "性能阈值应该大于0");
        assertTrue(userActivityConfig.getRolloutPercentage() >= 0 && userActivityConfig.getRolloutPercentage() <= 100, 
                  "灰度比例应该在0-100之间");
        assertTrue(userActivityConfig.getMaxRetryCount() >= 0, "最大重试次数应该大于等于0");
        assertTrue(userActivityConfig.getRetryInterval() > 0, "重试间隔应该大于0");
        assertTrue(userActivityConfig.getOfflineUserCleanupInterval() > 0, "离线用户清理间隔应该大于0");
        assertTrue(userActivityConfig.getUserOfflineThreshold() > 0, "用户离线阈值应该大于0");
        assertTrue(userActivityConfig.getAsyncThreadPoolSize() > 0, "异步线程池大小应该大于0");
        assertTrue(userActivityConfig.getQueueCapacity() > 0, "队列容量应该大于0");
        assertTrue(userActivityConfig.getDegradationRecoveryTime() > 0, "降级恢复时间应该大于0");
    }

    @Test
    public void testRedisKeyGeneration() {
        // 测试Redis键生成
        String userKey = userActivityConfig.getRedisKey("user123");
        String expectedPrefix = "aigc:user:activity:";
        assertTrue(userKey.startsWith(expectedPrefix), "Redis键应该包含正确前缀");
        assertTrue(userKey.contains("user123"), "Redis键应该包含用户ID");
        
        // 测试不同用户生成不同的键
        String userKey1 = userActivityConfig.getRedisKey("user1");
        String userKey2 = userActivityConfig.getRedisKey("user2");
        assertNotEquals(userKey1, userKey2, "不同用户应该生成不同的Redis键");
    }

    @Test
    public void testCriticalApiDetection() {
        // 测试核心API检测逻辑
        assertTrue(userActivityConfig.isCriticalApi("/payment/"), "支付路径应该被识别为核心API");
        assertTrue(userActivityConfig.isCriticalApi("/payment/create"), "支付创建API应该被识别为核心API");
        assertTrue(userActivityConfig.isCriticalApi("/login"), "登录API应该被识别为核心API");
        assertTrue(userActivityConfig.isCriticalApi("/logout"), "登出API应该被识别为核心API");
        assertTrue(userActivityConfig.isCriticalApi("/order/create"), "订单API应该被识别为核心API");
        assertTrue(userActivityConfig.isCriticalApi("/api/auth/verify"), "认证API应该被识别为核心API");
        
        // 测试非核心API
        assertFalse(userActivityConfig.isCriticalApi("/api/user/profile"), "用户资料API不应该被识别为核心API");
        assertFalse(userActivityConfig.isCriticalApi("/api/system/info"), "系统信息API不应该被识别为核心API");
        assertFalse(userActivityConfig.isCriticalApi("/static/images/logo.png"), "静态资源不应该被识别为核心API");
    }

    @Test
    public void testRolloutLogic() {
        // 测试灰度发布逻辑
        // 在开发环境中，rollout-percentage是100%，所以所有用户都应该被处理
        assertTrue(userActivityConfig.isInRollout("user1"), "用户1应该被处理");
        assertTrue(userActivityConfig.isInRollout("user2"), "用户2应该被处理");
        assertTrue(userActivityConfig.isInRollout("test_user"), "测试用户应该被处理");
        assertTrue(userActivityConfig.isInRollout("admin"), "管理员用户应该被处理");
    }

    @Test
    public void testConfigurationConsistency() {
        // 验证配置的一致性
        // 批量间隔不应该大于缓存持续时间
        assertTrue(userActivityConfig.getBatchInterval() <= userActivityConfig.getCacheDuration(), 
                  "批量间隔不应该大于缓存持续时间");
        
        // 缓存持续时间不应该大于缓存过期时间
        assertTrue(userActivityConfig.getCacheDuration() <= userActivityConfig.getCacheExpireTime(), 
                  "缓存持续时间不应该大于缓存过期时间");
        
        // 用户离线阈值应该合理
        assertTrue(userActivityConfig.getUserOfflineThreshold() <= 60, 
                  "用户离线阈值不应该超过60分钟");
        
        // 队列容量应该大于批量大小
        assertTrue(userActivityConfig.getQueueCapacity() >= userActivityConfig.getBatchSize(), 
                  "队列容量应该大于等于批量大小");
    }

    @Test
    public void testEnvironmentProperties() {
        // 验证Spring环境属性
        assertNotNull(environment.getProperty("aigc.user-activity.enabled"), "应该能读取enabled属性");
        assertNotNull(environment.getProperty("aigc.user-activity.batch-size"), "应该能读取batch-size属性");
        assertNotNull(environment.getProperty("aigc.user-activity.rollout-percentage"), "应该能读取rollout-percentage属性");
        
        // 验证属性值类型转换
        Boolean enabled = environment.getProperty("aigc.user-activity.enabled", Boolean.class);
        assertNotNull(enabled, "enabled属性应该能转换为Boolean类型");
        
        Integer batchSize = environment.getProperty("aigc.user-activity.batch-size", Integer.class);
        assertNotNull(batchSize, "batch-size属性应该能转换为Integer类型");
        assertTrue(batchSize > 0, "batch-size应该大于0");
    }

    @Test
    public void testConfigurationToString() {
        // 验证配置类的toString方法（如果有的话）
        String configString = userActivityConfig.toString();
        assertNotNull(configString, "配置类toString不应该为空");
        // 可以验证toString包含关键信息，但这取决于具体实现
    }
}
