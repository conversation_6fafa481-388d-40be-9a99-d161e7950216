<template>
  <div class="img">
    <a-upload
      name="file"
      listType="picture-card"
      :multiple="isMultiple"
      :action="dummyAction"
      :fileList="fileList"
      :beforeUpload="beforeUpload"
      :disabled="disabled"
      :customRequest="customRequest"
      @change="handleChange"
      @preview="handlePreview"
      @remove="handleRemove"
      :key="uploadKey"
    >
      <div v-if="fileList.length < maxCount">
        <a-icon type="plus" />
        <div class="ant-upload-text">{{ text }}</div>
      </div>
    </a-upload>
    
    <!-- 预览模态框 -->
    <a-modal :visible="previewVisible" :footer="null" @cancel="handleCancel">
      <img alt="预览" style="width: 100%" :src="previewImage" />
    </a-modal>
  </div>
</template>

<script>
import { ACCESS_TOKEN } from '@/store/mutation-types'

export default {
  name: 'JImageUploadDeferred',
  props: {
    value: {
      type: [String, Array],
      required: false
    },
    text: {
      type: String,
      required: false,
      default: "上传"
    },
    bizPath: {
      type: String,
      required: false,
      default: "temp"
    },
    disabled: {
      type: Boolean,
      required: false,
      default: false
    },
    isMultiple: {
      type: Boolean,
      required: false,
      default: false
    },
    number: {
      type: Number,
      required: false,
      default: 0
    }
  },
  
  data() {
    return {
      fileList: [],
      previewVisible: false,
      previewImage: '',
      pendingFiles: [], // 待上传的文件列表
      dummyAction: 'dummy', // 虚拟上传地址
      maxCount: this.isMultiple ? (this.number || 10) : 1,
      uploadKey: Date.now(), // 🔥 强制重新渲染的key

      // 🔥 文件变更追踪
      originalFiles: [], // 原始文件列表（用于回滚）
      deletedFiles: [], // 被删除的原始文件列表
      isInitialized: false // 是否已初始化
    }
  },
  
  watch: {
    value: {
      handler(val) {
        this.initFileList(val)
      },
      immediate: true
    }
  },
  
  methods: {
    // 初始化文件列表
    initFileList(value) {
      if (!value) {
        this.fileList = []
        this.originalFiles = []
        return
      }

      const urls = Array.isArray(value) ? value : value.split(',').filter(url => url.trim())
      this.fileList = urls.map((url, index) => ({
        uid: `existing-${index}`,
        name: this.getFileName(url),
        status: 'done',
        url: this.getFullUrl(url),
        response: { message: url },
        isExisting: true // 标记为已存在的文件
      }))

      // 🔥 保存原始文件列表（用于回滚和删除追踪）
      if (!this.isInitialized) {
        this.originalFiles = [...this.fileList]
        this.deletedFiles = []
        this.isInitialized = true
        console.log('🎯 JImageUploadDeferred: 初始化原始文件列表:', this.originalFiles.map(f => f.response.message))
      }
    },
    
    // 获取文件名
    getFileName(url) {
      return url.split('/').pop() || 'image.jpg'
    },
    
    // 获取完整URL
    getFullUrl(url) {
      if (url.startsWith('http')) {
        return url
      }
      return window._CONFIG['staticDomainURL'] + '/' + url
    },
    
    // 自定义上传请求（不实际上传）
    customRequest(options) {
      const { file, onSuccess } = options

      console.log('🎯 JImageUploadDeferred: customRequest被调用:', file.name, 'uid:', file.uid)

      // 添加到待上传列表
      this.pendingFiles.push(file)

      console.log('🎯 JImageUploadDeferred: pendingFiles长度:', this.pendingFiles.length)
      console.log('🎯 JImageUploadDeferred: pendingFiles内容:', this.pendingFiles.map(f => f.name))

      // 创建本地预览URL
      const reader = new FileReader()
      reader.onload = (e) => {
        // 模拟上传成功，返回本地预览URL
        const response = {
          success: true,
          message: 'pending', // 标记为待上传
          url: e.target.result // 本地预览URL
        }

        onSuccess(response, file)
        console.log('🎯 JImageUploadDeferred: 文件已添加到待上传列表，本地预览URL已设置:', file.name)
      }

      reader.readAsDataURL(file)
    },
    
    // 上传前检查
    beforeUpload(file) {
      // 检查文件类型
      const isImage = file.type.indexOf('image/') === 0
      if (!isImage) {
        this.$message.error('只能上传图片文件!')
        return false
      }
      
      // 检查文件大小（5MB）
      const isLt5M = file.size / 1024 / 1024 < 5
      if (!isLt5M) {
        this.$message.error('图片大小不能超过 5MB!')
        return false
      }
      
      // 检查数量限制
      if (!this.isMultiple && this.fileList.length >= 1) {
        this.$message.error('只能上传一张图片!')
        return false
      }
      
      if (this.isMultiple && this.fileList.length >= this.maxCount) {
        this.$message.error(`最多只能上传 ${this.maxCount} 张图片!`)
        return false
      }
      
      console.log('🎯 JImageUploadDeferred: beforeUpload检查通过，允许上传:', file.name)
      return true // 允许上传，让customRequest处理
    },

    // 处理文件变化（同步fileList但避免在removed时修改）
    handleChange(info) {
      console.log('🎯 JImageUploadDeferred: handleChange触发:', info.file.status, info.file.name)
      console.log('🎯 JImageUploadDeferred: handleChange完整info:', info)
      console.log('🎯 JImageUploadDeferred: fileList长度变化:', this.fileList.length, '->', info.fileList.length)

      if (info.file.status === 'done') {
        console.log('🎯 JImageUploadDeferred: 文件上传完成（模拟）:', info.file.name)

        // 🔥 同步fileList并设置自定义属性
        this.fileList = [...info.fileList]

        // 标记为待上传文件
        const fileInList = this.fileList.find(f => f.uid === info.file.uid)
        if (fileInList && info.file.response && info.file.response.message === 'pending') {
          fileInList.isPending = true
          console.log('🎯 JImageUploadDeferred: 文件标记为待上传:', info.file.name)
        }

      } else if (info.file.status === 'uploading') {
        console.log('🎯 JImageUploadDeferred: 文件上传中:', info.file.name)
        // 同步fileList以显示上传进度
        this.fileList = [...info.fileList]

      } else if (info.file.status === 'error') {
        console.error('🎯 JImageUploadDeferred: 文件上传失败:', info.file.name)
        this.fileList = [...info.fileList]

      } else if (info.file.status === 'removed') {
        console.log('🎯 JImageUploadDeferred: 文件被删除:', info.file.name)

        // 🔥 先处理业务逻辑
        this.handleFileRemoval(info.file)

        // 🔥 然后同步fileList（移除被删除的文件）
        this.fileList = [...info.fileList]
        console.log('🎯 JImageUploadDeferred: 删除后同步fileList，当前长度:', this.fileList.length)
      }

      console.log('🎯 JImageUploadDeferred: fileList已同步，当前长度:', this.fileList.length)
    },

    // 处理文件删除逻辑（从handleChange和handleRemove调用）
    handleFileRemoval(file) {
      console.log('🎯 JImageUploadDeferred: 处理文件删除:', file.name, 'isPending:', file.isPending, 'isExisting:', file.isExisting)

      // 🔥 从this.fileList中查找文件的真实属性
      const fileInList = this.fileList.find(f => f.uid === file.uid)
      const isPendingFile = fileInList && fileInList.isPending
      const isExistingFile = fileInList && fileInList.isExisting

      console.log('🎯 JImageUploadDeferred: 从fileList查找到的属性:', {
        找到文件: !!fileInList,
        isPending: isPendingFile,
        isExisting: isExistingFile
      })

      if (isPendingFile) {
        // 从待上传列表中移除
        this.pendingFiles = this.pendingFiles.filter(f => f.uid !== file.uid)
        console.log('🎯 JImageUploadDeferred: 从待上传列表移除文件:', file.name)
        console.log('🎯 JImageUploadDeferred: 剩余pendingFiles:', this.pendingFiles.map(f => f.name))
      } else if (isExistingFile) {
        // 🔥 如果是原始文件，添加到删除列表（不立即删除）
        const filePath = fileInList.response && fileInList.response.message
        if (filePath && filePath !== 'pending') {
          this.deletedFiles.push(filePath)
          console.log('🎯 JImageUploadDeferred: 标记原始文件为删除:', filePath)
        }
      }

      this.emitChange()
    },

    // 移除文件
    handleRemove(file) {
      console.log('🎯 JImageUploadDeferred: handleRemove被调用:', file.name)
      this.handleFileRemoval(file)

      // 更新fileList（虽然handleChange也会更新，但这里确保一致性）
      this.fileList = this.fileList.filter(f => f.uid !== file.uid)
    },
    
    // 预览图片
    handlePreview(file) {
      this.previewImage = file.url || file.thumbUrl
      this.previewVisible = true
    },
    
    // 关闭预览
    handleCancel() {
      this.previewVisible = false
    },
    
    // 发出值变化事件
    emitChange() {
      console.log('🎯 JImageUploadDeferred: emitChange开始，当前fileList:', this.fileList.length)

      // 详细检查每个文件的状态
      this.fileList.forEach((file, index) => {
        console.log(`🎯 JImageUploadDeferred: 文件${index}:`, {
          name: file.name,
          isExisting: file.isExisting,
          isPending: file.isPending,
          response: file.response,
          status: file.status,
          url: file.url,  // 🔥 检查预览URL
          thumbUrl: file.thumbUrl  // 🔥 检查缩略图URL
        })
      })

      // 🔥 只发出已确认保存的文件（不包括待上传文件）
      const existingFiles = this.fileList
        .filter(file => {
          const isValid = file.isExisting && file.response && file.response.message !== 'pending'
          console.log(`🎯 JImageUploadDeferred: 文件${file.name}过滤结果:`, isValid, {
            isExisting: file.isExisting,
            isPending: file.isPending,
            hasResponse: !!file.response,
            message: file.response && file.response.message
          })
          return isValid
        })
        .map(file => file.response.message)

      // 🔥 多选时返回逗号分隔的字符串，单选时返回第一个文件或空字符串
      const value = this.isMultiple ? existingFiles.join(',') : (existingFiles[0] || '')

      console.log('🎯 JImageUploadDeferred: 发出change事件:', value, '文件列表:', existingFiles)
      console.log('🎯 JImageUploadDeferred: 注意：待上传文件不包含在change事件中，但在界面中可见')
      this.$emit('change', value)
    },
    
    // 执行实际上传（供父组件调用）
    async performUpload() {
      if (this.pendingFiles.length === 0) {
        console.log('🎯 JImageUploadDeferred: 没有待上传的文件')
        return []
      }
      
      console.log(`🎯 JImageUploadDeferred: 开始上传 ${this.pendingFiles.length} 个文件`)
      
      const uploadPromises = this.pendingFiles.map(file => this.uploadSingleFile(file))
      
      try {
        const results = await Promise.all(uploadPromises)
        
        // 更新fileList，将待上传文件标记为已上传
        this.fileList.forEach(fileItem => {
          if (fileItem.isPending) {
            const result = results.find(r => r.originalUid === fileItem.uid)
            if (result && result.success) {
              fileItem.response = { message: result.filePath }
              fileItem.isExisting = true
              fileItem.isPending = false
              fileItem.url = this.getFullUrl(result.filePath)
            }
          }
        })
        
        // 清空待上传列表
        this.pendingFiles = []
        
        // 发出变化事件
        this.emitChange()
        
        console.log('🎯 JImageUploadDeferred: 所有文件上传完成')
        return results.filter(r => r.success).map(r => r.filePath)
        
      } catch (error) {
        console.error('🎯 JImageUploadDeferred: 文件上传失败:', error)
        this.$message.error('文件上传失败: ' + error.message)
        throw error
      }
    },
    
    // 上传单个文件
    async uploadSingleFile(file) {
      return new Promise((resolve, reject) => {
        const formData = new FormData()
        formData.append('file', file)
        formData.append('biz', this.bizPath)
        
        const xhr = new XMLHttpRequest()
        
        xhr.upload.onprogress = (e) => {
          if (e.lengthComputable) {
            const percent = Math.round((e.loaded / e.total) * 100)
            console.log(`🎯 JImageUploadDeferred: ${file.name} 上传进度: ${percent}%`)
          }
        }
        
        xhr.onload = () => {
          if (xhr.status === 200) {
            try {
              const response = JSON.parse(xhr.responseText)
              if (response.success) {
                resolve({
                  success: true,
                  filePath: response.message,
                  originalUid: file.uid
                })
              } else {
                reject(new Error(response.message || '上传失败'))
              }
            } catch (e) {
              reject(new Error('响应解析失败'))
            }
          } else {
            reject(new Error(`HTTP ${xhr.status}: ${xhr.statusText}`))
          }
        }
        
        xhr.onerror = () => {
          reject(new Error('网络错误'))
        }
        
        xhr.open('POST', window._CONFIG['domianURL'] + '/sys/common/upload')
        xhr.setRequestHeader('X-Access-Token', this.$ls.get(ACCESS_TOKEN))
        xhr.send(formData)
      })
    },
    
    // 获取当前值（只包括已确认的文件）
    getCurrentValue() {
      const existingFiles = this.fileList
        .filter(file => file.isExisting && file.response && file.response.message !== 'pending')
        .map(file => file.response.message)

      const result = this.isMultiple ? existingFiles.join(',') : (existingFiles[0] || '')
      console.log('🎯 JImageUploadDeferred: getCurrentValue返回:', result, '文件列表:', existingFiles)
      return result
    },
    
    // 检查是否有待上传文件
    hasPendingFiles() {
      const result = this.pendingFiles.length > 0
      console.log('🎯 JImageUploadDeferred: hasPendingFiles检查:', result, 'pendingFiles长度:', this.pendingFiles.length)
      console.log('🎯 JImageUploadDeferred: pendingFiles内容:', this.pendingFiles.map(f => f.name))
      return result
    },

    // 🔥 检查是否有文件变更（新增、删除）
    hasChanges() {
      return this.pendingFiles.length > 0 || this.deletedFiles.length > 0
    },

    // 🔥 回滚所有变更（用户取消时调用）
    rollbackChanges() {
      console.log('🎯 JImageUploadDeferred: 回滚所有变更')

      // 恢复原始文件列表
      this.fileList = [...this.originalFiles]

      // 清空变更记录
      this.pendingFiles = []
      this.deletedFiles = []

      // 发出变化事件
      this.emitChange()

      console.log('🎯 JImageUploadDeferred: 变更已回滚')
    },

    // 🔥 确认删除原始文件（保存成功后调用）
    async confirmDeleteOriginalFiles() {
      if (this.deletedFiles.length === 0) {
        console.log('🎯 JImageUploadDeferred: 没有需要删除的原始文件')
        return
      }

      console.log(`🎯 JImageUploadDeferred: 开始删除 ${this.deletedFiles.length} 个原始文件:`, this.deletedFiles)

      try {
        // 调用后端API删除文件
        const deletePromises = this.deletedFiles.map(filePath => this.deleteServerFile(filePath))
        await Promise.all(deletePromises)

        console.log('🎯 JImageUploadDeferred: 原始文件删除完成')

        // 清空删除列表
        this.deletedFiles = []

      } catch (error) {
        console.error('🎯 JImageUploadDeferred: 删除原始文件失败:', error)
        // 删除失败不影响主流程，只记录日志
      }
    },

    // 🔥 删除服务器文件
    async deleteServerFile(filePath) {
      return new Promise((resolve, reject) => {
        const xhr = new XMLHttpRequest()

        xhr.onload = () => {
          if (xhr.status === 200) {
            try {
              const response = JSON.parse(xhr.responseText)
              if (response.success) {
                console.log(`🎯 JImageUploadDeferred: 文件删除成功: ${filePath}`)
                resolve()
              } else {
                reject(new Error(response.message || '删除失败'))
              }
            } catch (e) {
              reject(new Error('响应解析失败'))
            }
          } else {
            reject(new Error(`HTTP ${xhr.status}: ${xhr.statusText}`))
          }
        }

        xhr.onerror = () => {
          reject(new Error('网络错误'))
        }

        // 🔥 提取相对路径，确保传递正确的文件路径给后端
        const relativePath = this.extractRelativePathForDelete(filePath)
        console.log(`🎯 JImageUploadDeferred: 删除文件 - 原始路径: ${filePath}, 相对路径: ${relativePath}`)

        // 调用后端删除文件接口
        xhr.open('DELETE', window._CONFIG['domianURL'] + '/sys/common/deleteFile?filePath=' + encodeURIComponent(relativePath))
        xhr.setRequestHeader('X-Access-Token', this.$ls.get('Access-Token'))
        xhr.send()
      })
    },

    // 🔥 提取相对路径用于删除（与CreatorAgentForm中的逻辑一致）
    extractRelativePathForDelete(url) {
      console.log('🎯 JImageUploadDeferred: 提取删除路径 - 输入URL:', url)

      // 如果是完整的TOS URL，提取相对路径
      if (url.includes('aigcview-tos.tos-cn-shanghai.volces.com/')) {
        const match = url.match(/uploads\/[^?]+/)
        if (match) {
          const relativePath = match[0]
          console.log('🎯 JImageUploadDeferred: 从TOS URL提取相对路径:', relativePath)
          return relativePath
        }
      }

      // 如果是CDN URL，提取相对路径
      if (url.includes('cdn.aigcview.com/')) {
        const match = url.match(/uploads\/[^?]+/)
        if (match) {
          const relativePath = match[0]
          console.log('🎯 JImageUploadDeferred: 从CDN URL提取相对路径:', relativePath)
          return relativePath
        }
      }

      // 如果已经是相对路径（以uploads/开头），直接返回
      if (url.startsWith('uploads/')) {
        console.log('🎯 JImageUploadDeferred: 已经是相对路径:', url)
        return url
      }

      // 其他情况，直接返回原值
      console.log('🎯 JImageUploadDeferred: 无法识别的URL格式，返回原值:', url)
      return url
    }
  },
  
  model: {
    prop: 'value',
    event: 'change'
  }
}
</script>

<style scoped>
::v-deep .ant-upload-select-picture-card i {
  font-size: 32px;
  color: #999;
}

::v-deep .ant-upload-select-picture-card .ant-upload-text {
  margin-top: 8px;
  color: #666;
}
</style>
