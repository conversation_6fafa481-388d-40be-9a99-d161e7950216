.panel {
  overflow: hidden;
  text-align: left;
  margin: 0;
  border: 0;
  -moz-border-radius: 0 0 0 0;
  -webkit-border-radius: 0 0 0 0;
  border-radius: 0 0 0 0;
}
.panel-header,
.panel-body {
  border-width: 1px;
  border-style: solid;
}
.panel-header {
  padding: 5px;
  position: relative;
}
.panel-title {
  background: url('images/blank.gif') no-repeat;
}
.panel-header-noborder {
  border-width: 0 0 1px 0;
}
.panel-body {
  overflow: auto;
  border-top-width: 0;
  padding: 0;
}
.panel-body-noheader {
  border-top-width: 1px;
}
.panel-body-noborder {
  border-width: 0px;
}
.panel-body-nobottom {
  border-bottom-width: 0;
}
.panel-with-icon {
  padding-left: 18px;
}
.panel-icon,
.panel-tool {
  position: absolute;
  top: 50%;
  margin-top: -8px;
  height: 16px;
  overflow: hidden;
}
.panel-icon {
  left: 5px;
  width: 16px;
}
.panel-tool {
  right: 5px;
  width: auto;
}
.panel-tool a {
  display: inline-block;
  width: 16px;
  height: 16px;
  opacity: 0.6;
  filter: alpha(opacity=60);
  margin: 0 0 0 2px;
  vertical-align: top;
}
.panel-tool a:hover {
  opacity: 1;
  filter: alpha(opacity=100);
  background-color: #777;
  -moz-border-radius: 3px 3px 3px 3px;
  -webkit-border-radius: 3px 3px 3px 3px;
  border-radius: 3px 3px 3px 3px;
}
.panel-loading {
  padding: 11px 0px 10px 30px;
}
.panel-noscroll {
  overflow: hidden;
}
.panel-fit,
.panel-fit body {
  height: 100%;
  margin: 0;
  padding: 0;
  border: 0;
  overflow: hidden;
}
.panel-loading {
  background: url('images/loading.gif') no-repeat 10px 10px;
}
.panel-tool-close {
  background: url('images/panel_tools.png') no-repeat -16px 0px;
}
.panel-tool-min {
  background: url('images/panel_tools.png') no-repeat 0px 0px;
}
.panel-tool-max {
  background: url('images/panel_tools.png') no-repeat 0px -16px;
}
.panel-tool-restore {
  background: url('images/panel_tools.png') no-repeat -16px -16px;
}
.panel-tool-collapse {
  background: url('images/panel_tools.png') no-repeat -32px 0;
}
.panel-tool-expand {
  background: url('images/panel_tools.png') no-repeat -32px -16px;
}
.panel-header,
.panel-body {
  border-color: #000;
}
.panel-header {
  background-color: #3d3d3d;
  background: -webkit-linear-gradient(top,#454545 0,#383838 100%);
  background: -moz-linear-gradient(top,#454545 0,#383838 100%);
  background: -o-linear-gradient(top,#454545 0,#383838 100%);
  background: linear-gradient(to bottom,#454545 0,#383838 100%);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr=#454545,endColorstr=#383838,GradientType=0);
}
.panel-body {
  background-color: #666;
  color: #fff;
  font-size: 12px;
}
.panel-title {
  font-size: 12px;  
  color: #fff;
  height: 20px;
  line-height: 20px;
}
.panel-footer {
  border: 1px solid #000;
  overflow: hidden;
  background: #555;
}
.panel-footer-noborder {
  border-width: 1px 0 0 0;
}
.panel-hleft,
.panel-hright {
  position: relative;
}
.panel-hleft>.panel-body,
.panel-hright>.panel-body {
  position: absolute;
}
.panel-hleft>.panel-header {
  float: left;
}
.panel-hright>.panel-header {
  float: right;
}
.panel-hleft>.panel-body {
  border-top-width: 1px;
  border-left-width: 0;
}
.panel-hright>.panel-body {
  border-top-width: 1px;
  border-right-width: 0;
}
.panel-hleft>.panel-body-nobottom {
  border-bottom-width: 1px;
  border-right-width: 0;
}
.panel-hright>.panel-body-nobottom {
  border-bottom-width: 1px;
  border-left-width: 0;
}
.panel-hleft>.panel-footer {
  position: absolute;
  right: 0;
}
.panel-hright>.panel-footer {
  position: absolute;
  left: 0;
}
.panel-hleft>.panel-header-noborder {
  border-width: 0 1px 0 0;
}
.panel-hright>.panel-header-noborder {
  border-width: 0 0 0 1px;
}
.panel-hleft>.panel-body-noborder {
  border-width: 0;
}
.panel-hright>.panel-body-noborder {
  border-width: 0;
}
.panel-hleft>.panel-body-noheader {
  border-left-width: 1px;
}
.panel-hright>.panel-body-noheader {
  border-right-width: 1px;
}
.panel-hleft>.panel-footer-noborder {
  border-width: 0 0 0 1px;
}
.panel-hright>.panel-footer-noborder {
  border-width: 0 1px 0 0;
}
.panel-hleft>.panel-header .panel-icon,
.panel-hright>.panel-header .panel-icon {
  margin-top: 0;
  top: 5px;
  left: 50%;
  margin-left: -8px;
}
.panel-hleft>.panel-header .panel-title,
.panel-hright>.panel-header .panel-title {
  position: absolute;
  min-width: 16px;
  left: 25px;
  top: 5px;
  bottom: auto;
  white-space: nowrap;
  word-wrap: normal;
  -webkit-transform: rotate(90deg);
  -webkit-transform-origin: 0 0;
  -moz-transform: rotate(90deg);
  -moz-transform-origin: 0 0;
  -o-transform: rotate(90deg);
  -o-transform-origin: 0 0;
  transform: rotate(90deg);
  transform-origin: 0 0;
}
.panel-hleft>.panel-header .panel-title-up,
.panel-hright>.panel-header .panel-title-up {
  position: absolute;
  min-width: 16px;
  left: 21px;
  top: auto;
  bottom: 0px;
  text-align: right;
  white-space: nowrap;
  word-wrap: normal;
  -webkit-transform: rotate(-90deg);
  -webkit-transform-origin: 0 0;
  -moz-transform: rotate(-90deg);
  -moz-transform-origin: 0 0;
  -o-transform: rotate(-90deg);
  -o-transform-origin: 0 0;
  transform: rotate(-90deg);
  transform-origin: 0 16px;
}
.panel-hleft>.panel-header .panel-with-icon.panel-title-up,
.panel-hright>.panel-header .panel-with-icon.panel-title-up {
  padding-left: 0;
  padding-right: 18px;
}
.panel-hleft>.panel-header .panel-tool,
.panel-hright>.panel-header .panel-tool {
  top: auto;
  bottom: 5px;
  width: 16px;
  height: auto;
  left: 50%;
  margin-left: -8px;
  margin-top: 0;
}
.panel-hleft>.panel-header .panel-tool a,
.panel-hright>.panel-header .panel-tool a {
  margin: 2px 0 0 0;
}
.accordion {
  overflow: hidden;
  border-width: 1px;
  border-style: solid;
}
.accordion .accordion-header {
  border-width: 0 0 1px;
  cursor: pointer;
}
.accordion .accordion-body {
  border-width: 0 0 1px;
}
.accordion-noborder {
  border-width: 0;
}
.accordion-noborder .accordion-header {
  border-width: 0 0 1px;
}
.accordion-noborder .accordion-body {
  border-width: 0 0 1px;
}
.accordion-collapse {
  background: url('images/accordion_arrows.png') no-repeat 0 0;
}
.accordion-expand {
  background: url('images/accordion_arrows.png') no-repeat -16px 0;
}
.accordion {
  background: #666;
  border-color: #000;
}
.accordion .accordion-header {
  background: #3d3d3d;
  filter: none;
}
.accordion .accordion-header-selected {
  background: #990000;
}
.accordion .accordion-header-selected .panel-title {
  color: #fff;
}
.accordion .panel-last > .accordion-header {
  border-bottom-color: #3d3d3d;
}
.accordion .panel-last > .accordion-body {
  border-bottom-color: #666;
}
.accordion .panel-last > .accordion-header-selected,
.accordion .panel-last > .accordion-header-border {
  border-bottom-color: #000;
}
.accordion> .panel-hleft {
  float: left;
}
.accordion> .panel-hleft>.panel-header {
  border-width: 0 1px 0 0;
}
.accordion> .panel-hleft> .panel-body {
  border-width: 0 1px 0 0;
}
.accordion> .panel-hleft.panel-last > .accordion-header {
  border-right-color: #3d3d3d;
}
.accordion> .panel-hleft.panel-last > .accordion-body {
  border-right-color: #666;
}
.accordion> .panel-hleft.panel-last > .accordion-header-selected,
.accordion> .panel-hleft.panel-last > .accordion-header-border {
  border-right-color: #000;
}
.accordion> .panel-hright {
  float: right;
}
.accordion> .panel-hright>.panel-header {
  border-width: 0 0 0 1px;
}
.accordion> .panel-hright> .panel-body {
  border-width: 0 0 0 1px;
}
.accordion> .panel-hright.panel-last > .accordion-header {
  border-left-color: #3d3d3d;
}
.accordion> .panel-hright.panel-last > .accordion-body {
  border-left-color: #666;
}
.accordion> .panel-hright.panel-last > .accordion-header-selected,
.accordion> .panel-hright.panel-last > .accordion-header-border {
  border-left-color: #000;
}
.window {


  overflow: hidden;
  padding: 5px;
  border-width: 1px;
  border-style: solid;
}
.window .window-header {
  background: transparent;
  padding: 0px 0px 6px 0px;
}
.window .window-body {
  border-width: 1px;
  border-style: solid;
  border-top-width: 0px;
}
.window .window-body-noheader {
  border-top-width: 1px;
}
.window .panel-body-nobottom {
  border-bottom-width: 0;
}
.window .window-header .panel-icon,
.window .window-header .panel-tool {
  top: 50%;
  margin-top: -11px;
}
.window .window-header .panel-icon {
  left: 1px;
}
.window .window-header .panel-tool {
  right: 1px;
}
.window .window-header .panel-with-icon {
  padding-left: 18px;
}
.window-proxy {
  position: absolute;
  overflow: hidden;
}
.window-proxy-mask {
  position: absolute;
  filter: alpha(opacity=5);
  opacity: 0.05;
}
.window-mask {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  filter: alpha(opacity=40);
  opacity: 0.40;
  font-size: 1px;
  overflow: hidden;
}
.window,
.window-shadow {
  position: absolute;
  -moz-border-radius: 5px 5px 5px 5px;
  -webkit-border-radius: 5px 5px 5px 5px;
  border-radius: 5px 5px 5px 5px;
}
.window-shadow {
  background: #777;
  -moz-box-shadow: 2px 2px 3px #787878;
  -webkit-box-shadow: 2px 2px 3px #787878;
  box-shadow: 2px 2px 3px #787878;
  filter: progid:DXImageTransform.Microsoft.Blur(pixelRadius=2,MakeShadow=false,ShadowOpacity=0.2);
}
.window,
.window .window-body {
  border-color: #000;
}
.window {
  background-color: #3d3d3d;
  background: -webkit-linear-gradient(top,#454545 0,#383838 20%);
  background: -moz-linear-gradient(top,#454545 0,#383838 20%);
  background: -o-linear-gradient(top,#454545 0,#383838 20%);
  background: linear-gradient(to bottom,#454545 0,#383838 20%);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr=#454545,endColorstr=#383838,GradientType=0);
}
.window-proxy {
  border: 1px dashed #000;
}
.window-proxy-mask,
.window-mask {
  background: #000;
}
.window .panel-footer {
  border: 1px solid #000;
  position: relative;
  top: -1px;
}
.window-thinborder {
  padding: 0;
}
.window-thinborder .window-header {
  padding: 5px 5px 6px 5px;
}
.window-thinborder .window-body {
  border-width: 0px;
}
.window-thinborder .window-footer {
  border-left: transparent;
  border-right: transparent;
  border-bottom: transparent;
}
.window-thinborder .window-header .panel-icon,
.window-thinborder .window-header .panel-tool {
  margin-top: -9px;
  margin-left: 5px;
  margin-right: 5px;
}
.window-noborder {
  border: 0;
}
.window.panel-hleft .window-header {
  padding: 0 6px 0 0;
}
.window.panel-hright .window-header {
  padding: 0 0 0 6px;
}
.window.panel-hleft>.panel-header .panel-title {
  top: auto;
  left: 16px;
}
.window.panel-hright>.panel-header .panel-title {
  top: auto;
  right: 16px;
}
.window.panel-hleft>.panel-header .panel-title-up,
.window.panel-hright>.panel-header .panel-title-up {
  bottom: 0;
}
.window.panel-hleft .window-body {
  border-width: 1px 1px 1px 0;
}
.window.panel-hright .window-body {
  border-width: 1px 0 1px 1px;
}
.window.panel-hleft .window-header .panel-icon {
  top: 1px;
  margin-top: 0;
  left: 0;
}
.window.panel-hright .window-header .panel-icon {
  top: 1px;
  margin-top: 0;
  left: auto;
  right: 1px;
}
.window.panel-hleft .window-header .panel-tool,
.window.panel-hright .window-header .panel-tool {
  margin-top: 0;
  top: auto;
  bottom: 1px;
  right: auto;
  margin-right: 0;
  left: 50%;
  margin-left: -11px;
}
.window.panel-hright .window-header .panel-tool {
  left: auto;
  right: 1px;
}
.window-thinborder.panel-hleft .window-header {
  padding: 5px 6px 5px 5px;
}
.window-thinborder.panel-hright .window-header {
  padding: 5px 5px 5px 6px;
}
.window-thinborder.panel-hleft>.panel-header .panel-title {
  left: 21px;
}
.window-thinborder.panel-hleft>.panel-header .panel-title-up,
.window-thinborder.panel-hright>.panel-header .panel-title-up {
  bottom: 5px;
}
.window-thinborder.panel-hleft .window-header .panel-icon,
.window-thinborder.panel-hright .window-header .panel-icon {
  margin-top: 5px;
}
.window-thinborder.panel-hleft .window-header .panel-tool,
.window-thinborder.panel-hright .window-header .panel-tool {
  left: 16px;
  bottom: 5px;
}
.dialog-content {
  overflow: auto;
}
.dialog-toolbar {
  position: relative;
  padding: 2px 5px;
}
.dialog-tool-separator {
  float: left;
  height: 24px;
  border-left: 1px solid #444;
  border-right: 1px solid #777;
  margin: 2px 1px;
}
.dialog-button {
  position: relative;
  top: -1px;
  padding: 5px;
  text-align: right;
}
.dialog-button .l-btn {
  margin-left: 5px;
}
.dialog-toolbar,
.dialog-button {
  background: #555;
  border-width: 1px;
  border-style: solid;
}
.dialog-toolbar {
  border-color: #000 #000 #222 #000;
}
.dialog-button {
  border-color: #222 #000 #000 #000;
}
.window-thinborder .dialog-toolbar {
  border-left: transparent;
  border-right: transparent;
  border-top-color: #555;
}
.window-thinborder .dialog-button {
  top: 0px;
  padding: 5px 8px 8px 8px;
  border-left: transparent;
  border-right: transparent;
  border-bottom: transparent;
}
.l-btn {
  text-decoration: none;
  display: inline-block;
  overflow: hidden;
  margin: 0;
  padding: 0;
  cursor: pointer;
  outline: none;
  text-align: center;
  vertical-align: middle;
  line-height: normal;
}
.l-btn-plain {
  border-width: 0;
  padding: 1px;
}
.l-btn-left {
  display: inline-block;
  position: relative;
  overflow: hidden;
  margin: 0;
  padding: 0;
  vertical-align: top;
}
.l-btn-text {
  display: inline-block;
  vertical-align: top;
  width: auto;
  line-height: 28px;
  font-size: 12px;
  padding: 0;
  margin: 0 6px;
}
.l-btn-icon {
  display: inline-block;
  width: 16px;
  height: 16px;
  line-height: 16px;
  position: absolute;
  top: 50%;
  margin-top: -8px;
  font-size: 1px;
}
.l-btn span span .l-btn-empty {
  display: inline-block;
  margin: 0;
  width: 16px;
  height: 24px;
  font-size: 1px;
  vertical-align: top;
}
.l-btn span .l-btn-icon-left {
  padding: 0 0 0 20px;
  background-position: left center;
}
.l-btn span .l-btn-icon-right {
  padding: 0 20px 0 0;
  background-position: right center;
}
.l-btn-icon-left .l-btn-text {
  margin: 0 6px 0 26px;
}
.l-btn-icon-left .l-btn-icon {
  left: 6px;
}
.l-btn-icon-right .l-btn-text {
  margin: 0 26px 0 6px;
}
.l-btn-icon-right .l-btn-icon {
  right: 6px;
}
.l-btn-icon-top .l-btn-text {
  margin: 20px 4px 0 4px;
}
.l-btn-icon-top .l-btn-icon {
  top: 4px;
  left: 50%;
  margin: 0 0 0 -8px;
}
.l-btn-icon-bottom .l-btn-text {
  margin: 0 4px 20px 4px;
}
.l-btn-icon-bottom .l-btn-icon {
  top: auto;
  bottom: 4px;
  left: 50%;
  margin: 0 0 0 -8px;
}
.l-btn-left .l-btn-empty {
  margin: 0 6px;
  width: 16px;
}
.l-btn-plain:hover {
  padding: 0;
}
.l-btn-focus {
  outline: #0000FF dotted thin;
}
.l-btn-large .l-btn-text {
  line-height: 44px;
}
.l-btn-large .l-btn-icon {
  width: 32px;
  height: 32px;
  line-height: 32px;
  margin-top: -16px;
}
.l-btn-large .l-btn-icon-left .l-btn-text {
  margin-left: 40px;
}
.l-btn-large .l-btn-icon-right .l-btn-text {
  margin-right: 40px;
}
.l-btn-large .l-btn-icon-top .l-btn-text {
  margin-top: 36px;
  line-height: 24px;
  min-width: 32px;
}
.l-btn-large .l-btn-icon-top .l-btn-icon {
  margin: 0 0 0 -16px;
}
.l-btn-large .l-btn-icon-bottom .l-btn-text {
  margin-bottom: 36px;
  line-height: 24px;
  min-width: 32px;
}
.l-btn-large .l-btn-icon-bottom .l-btn-icon {
  margin: 0 0 0 -16px;
}
.l-btn-large .l-btn-left .l-btn-empty {
  margin: 0 6px;
  width: 32px;
}
.l-btn {
  color: #fff;
  background: #777;
  background-repeat: repeat-x;
  border: 1px solid #555;
  background: -webkit-linear-gradient(top,#919191 0,#6a6a6a 100%);
  background: -moz-linear-gradient(top,#919191 0,#6a6a6a 100%);
  background: -o-linear-gradient(top,#919191 0,#6a6a6a 100%);
  background: linear-gradient(to bottom,#919191 0,#6a6a6a 100%);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr=#919191,endColorstr=#6a6a6a,GradientType=0);
  -moz-border-radius: 5px 5px 5px 5px;
  -webkit-border-radius: 5px 5px 5px 5px;
  border-radius: 5px 5px 5px 5px;
}
.l-btn:hover {
  background: #777;
  color: #fff;
  border: 1px solid #555;
  filter: none;
}
.l-btn-plain {
  background: transparent;
  border-width: 0;
  filter: none;
}
.l-btn-outline {
  border-width: 1px;
  border-color: #555;
  padding: 0;
}
.l-btn-plain:hover {
  background: #777;
  color: #fff;
  border: 1px solid #555;
  -moz-border-radius: 5px 5px 5px 5px;
  -webkit-border-radius: 5px 5px 5px 5px;
  border-radius: 5px 5px 5px 5px;
}
.l-btn-disabled,
.l-btn-disabled:hover {
  opacity: 0.5;
  cursor: default;
  background: #777;
  color: #fff;
  background: -webkit-linear-gradient(top,#919191 0,#6a6a6a 100%);
  background: -moz-linear-gradient(top,#919191 0,#6a6a6a 100%);
  background: -o-linear-gradient(top,#919191 0,#6a6a6a 100%);
  background: linear-gradient(to bottom,#919191 0,#6a6a6a 100%);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr=#919191,endColorstr=#6a6a6a,GradientType=0);
}
.l-btn-disabled .l-btn-text,
.l-btn-disabled .l-btn-icon {
  filter: alpha(opacity=50);
}
.l-btn-plain-disabled,
.l-btn-plain-disabled:hover {
  background: transparent;
  filter: alpha(opacity=50);
}
.l-btn-selected,
.l-btn-selected:hover {
  background: #000;
  filter: none;
}
.l-btn-plain-selected,
.l-btn-plain-selected:hover {
  background: #000;
}
.textbox {
  position: relative;
  border: 1px solid #000;
  background-color: #fff;
  vertical-align: middle;
  display: inline-block;
  overflow: hidden;
  white-space: nowrap;
  margin: 0;
  padding: 0;
  -moz-border-radius: 5px 5px 5px 5px;
  -webkit-border-radius: 5px 5px 5px 5px;
  border-radius: 5px 5px 5px 5px;
}
.textbox .textbox-text {
  font-size: 12px;
  border: 0;
  margin: 0;
  padding: 0 4px;
  white-space: normal;
  vertical-align: top;
  outline-style: none;
  resize: none;
  -moz-border-radius: 5px 5px 5px 5px;
  -webkit-border-radius: 5px 5px 5px 5px;
  border-radius: 5px 5px 5px 5px;
  height: 28px;
  line-height: 28px;
}
.textbox textarea.textbox-text {
  line-height: normal;
}
.textbox .textbox-text::-ms-clear,
.textbox .textbox-text::-ms-reveal {
  display: none;
}
.textbox textarea.textbox-text {
  white-space: pre-wrap;
}
.textbox .textbox-prompt {
  font-size: 12px;
  color: #aaa;
}
.textbox .textbox-bgicon {
  background-position: 3px center;
  padding-left: 21px;
}
.textbox .textbox-button,
.textbox .textbox-button:hover {
  position: absolute;
  top: 0;
  padding: 0;
  vertical-align: top;
  -moz-border-radius: 0 0 0 0;
  -webkit-border-radius: 0 0 0 0;
  border-radius: 0 0 0 0;
}
.textbox .textbox-button-right,
.textbox .textbox-button-right:hover {
  right: 0;
  border-width: 0 0 0 1px;
}
.textbox .textbox-button-left,
.textbox .textbox-button-left:hover {
  left: 0;
  border-width: 0 1px 0 0;
}
.textbox .textbox-button-top,
.textbox .textbox-button-top:hover {
  left: 0;
  border-width: 0 0 1px 0;
}
.textbox .textbox-button-bottom,
.textbox .textbox-button-bottom:hover {
  top: auto;
  bottom: 0;
  left: 0;
  border-width: 1px 0 0 0;
}
.textbox-addon {
  position: absolute;
  top: 0;
}
.textbox-label {
  display: inline-block;
  width: 80px;
  height: 30px;
  line-height: 30px;
  vertical-align: middle;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin: 0;
  padding-right: 5px;
}
.textbox-label-after {
  padding-left: 5px;
  padding-right: 0;
}
.textbox-label-top {
  display: block;
  width: auto;
  padding: 0;
}
.textbox-disabled,
.textbox-label-disabled {
  opacity: 0.6;
  filter: alpha(opacity=60);
}
.textbox-icon {
  display: inline-block;
  width: 18px;
  height: 20px;
  overflow: hidden;
  vertical-align: top;
  background-position: center center;
  cursor: pointer;
  opacity: 0.6;
  filter: alpha(opacity=60);
  text-decoration: none;
  outline-style: none;
}
.textbox-icon-disabled,
.textbox-icon-readonly {
  cursor: default;
}
.textbox-icon:hover {
  opacity: 1.0;
  filter: alpha(opacity=100);
}
.textbox-icon-disabled:hover {
  opacity: 0.6;
  filter: alpha(opacity=60);
}
.textbox-focused {
  border-color: #000000;
  -moz-box-shadow: 0 0 3px 0 #000;
  -webkit-box-shadow: 0 0 3px 0 #000;
  box-shadow: 0 0 3px 0 #000;
}
.textbox-invalid {
  border-color: #ffa8a8;
  background-color: #fff3f3;
}
.passwordbox-open {
  background: url('images/passwordbox_open.png') no-repeat center center;
}
.passwordbox-close {
  background: url('images/passwordbox_close.png') no-repeat center center;
}
.filebox .textbox-value {
  vertical-align: top;
  position: absolute;
  top: 0;
  left: -5000px;
}
.filebox-label {
  display: inline-block;
  position: absolute;
  width: 100%;
  height: 100%;
  cursor: pointer;
  left: 0;
  top: 0;
  z-index: 10;
  background: url('images/blank.gif') no-repeat;
}
.l-btn-disabled .filebox-label {
  cursor: default;
}
.combo-arrow {
  width: 18px;
  height: 20px;
  overflow: hidden;
  display: inline-block;
  vertical-align: top;
  cursor: pointer;
  opacity: 0.6;
  filter: alpha(opacity=60);
}
.combo-arrow-hover {
  opacity: 1.0;
  filter: alpha(opacity=100);
}
.combo-panel {
  overflow: auto;
}
.combo-arrow {
  background: url('images/combo_arrow.png') no-repeat center center;
}
.combo-panel {
  background-color: #666;
}
.combo-arrow {
  background-color: #3d3d3d;
}
.combo-arrow-hover {
  background-color: #777;
}
.combo-arrow:hover {
  background-color: #777;
}
.combo .textbox-icon-disabled:hover {
  cursor: default;
}
.combobox-item,
.combobox-group,
.combobox-stick {
  font-size: 12px;
  padding: 6px 4px;
  line-height: 20px;
}
.combobox-item-disabled {
  opacity: 0.5;
  filter: alpha(opacity=50);
}
.combobox-gitem {
  padding-left: 10px;
}
.combobox-group,
.combobox-stick {
  font-weight: bold;
}
.combobox-stick {
  position: absolute;
  top: 1px;
  left: 1px;
  right: 1px;
  background: inherit;
}
.combobox-item-hover {
  background-color: #777;
  color: #fff;
}
.combobox-item-selected {
  background-color: #990000;
  color: #fff;
}
.combobox-icon {
  display: inline-block;
  width: 16px;
  height: 16px;
  vertical-align: middle;
  margin-right: 2px;
}
.tagbox {
  cursor: text;
}
.tagbox .textbox-text {
  float: left;
}
.tagbox-label {
  position: relative;
  display: block;
  margin: 4px 0 0 4px;
  padding: 0 20px 0 4px;
  float: left;
  vertical-align: top;
  text-decoration: none;
  -moz-border-radius: 5px 5px 5px 5px;
  -webkit-border-radius: 5px 5px 5px 5px;
  border-radius: 5px 5px 5px 5px;
  background: #777;
  color: #fff;
}
.tagbox-remove {
  background: url('images/tagbox_icons.png') no-repeat -16px center;
  position: absolute;
  display: block;
  width: 16px;
  height: 16px;
  right: 2px;
  top: 50%;
  margin-top: -8px;
  opacity: 0.6;
  filter: alpha(opacity=60);
}
.tagbox-remove:hover {
  opacity: 1;
  filter: alpha(opacity=100);
}
.textbox-disabled .tagbox-label {
  cursor: default;
}
.textbox-disabled .tagbox-remove:hover {
  cursor: default;
  opacity: 0.6;
  filter: alpha(opacity=60);
}
.layout {
  position: relative;
  overflow: hidden;
  margin: 0;
  padding: 0;
  z-index: 0;
}
.layout-panel {
  position: absolute;
  overflow: hidden;
}
.layout-body {
  min-width: 1px;
  min-height: 1px;
}
.layout-panel-east,
.layout-panel-west {
  z-index: 2;
}
.layout-panel-north,
.layout-panel-south {
  z-index: 3;
}
.layout-expand {
  position: absolute;
  padding: 0px;
  font-size: 1px;
  cursor: pointer;
  z-index: 1;
}
.layout-expand .panel-header,
.layout-expand .panel-body {
  background: transparent;
  filter: none;
  overflow: hidden;
}
.layout-expand .panel-header {
  border-bottom-width: 0px;
}
.layout-expand .panel-body {
  position: relative;
}
.layout-expand .panel-body .panel-icon {
  margin-top: 0;
  top: 0;
  left: 50%;
  margin-left: -8px;
}
.layout-expand-west .panel-header .panel-icon,
.layout-expand-east .panel-header .panel-icon {
  display: none;
}
.layout-expand-title {
  position: absolute;
  top: 0;
  left: 21px;
  white-space: nowrap;
  word-wrap: normal;
  -webkit-transform: rotate(90deg);
  -webkit-transform-origin: 0 0;
  -moz-transform: rotate(90deg);
  -moz-transform-origin: 0 0;
  -o-transform: rotate(90deg);
  -o-transform-origin: 0 0;
  transform: rotate(90deg);
  transform-origin: 0 0;
}
.layout-expand-title-up {
  position: absolute;
  top: 0;
  left: 0;
  text-align: right;
  padding-left: 5px;
  white-space: nowrap;
  word-wrap: normal;
  -webkit-transform: rotate(-90deg);
  -webkit-transform-origin: 0 0;
  -moz-transform: rotate(-90deg);
  -moz-transform-origin: 0 0;
  -o-transform: rotate(-90deg);
  -o-transform-origin: 0 0;
  transform: rotate(-90deg);
  transform-origin: 0 0;
}
.layout-expand-with-icon {
  top: 18px;
}
.layout-expand .panel-body-noheader .layout-expand-title,
.layout-expand .panel-body-noheader .panel-icon {
  top: 5px;
}
.layout-expand .panel-body-noheader .layout-expand-with-icon {
  top: 23px;
}
.layout-split-proxy-h,
.layout-split-proxy-v {
  position: absolute;
  font-size: 1px;
  display: none;
  z-index: 5;
}
.layout-split-proxy-h {
  width: 5px;
  cursor: e-resize;
}
.layout-split-proxy-v {
  height: 5px;
  cursor: n-resize;
}
.layout-mask {
  position: absolute;
  background: #fafafa;
  filter: alpha(opacity=10);
  opacity: 0.10;
  z-index: 4;
}
.layout-button-up {
  background: url('images/layout_arrows.png') no-repeat -16px -16px;
}
.layout-button-down {
  background: url('images/layout_arrows.png') no-repeat -16px 0;
}
.layout-button-left {
  background: url('images/layout_arrows.png') no-repeat 0 0;
}
.layout-button-right {
  background: url('images/layout_arrows.png') no-repeat 0 -16px;
}
.layout-split-proxy-h,
.layout-split-proxy-v {
  background-color: #cccccc;
}
.layout-split-north {
  border-bottom: 5px solid #444;
}
.layout-split-south {
  border-top: 5px solid #444;
}
.layout-split-east {
  border-left: 5px solid #444;
}
.layout-split-west {
  border-right: 5px solid #444;
}
.layout-expand {
  background-color: #3d3d3d;
}
.layout-expand-over {
  background-color: #3d3d3d;
}
.tabs-container {
  overflow: hidden;
}
.tabs-header {
  border-width: 1px;
  border-style: solid;
  border-bottom-width: 0;
  position: relative;
  padding: 0;
  padding-top: 2px;
  overflow: hidden;
}
.tabs-scroller-left,
.tabs-scroller-right {
  position: absolute;
  top: auto;
  bottom: 0;
  width: 18px;
  font-size: 1px;
  display: none;
  cursor: pointer;
  border-width: 1px;
  border-style: solid;
}
.tabs-scroller-left {
  left: 0;
}
.tabs-scroller-right {
  right: 0;
}
.tabs-tool {
  position: absolute;
  bottom: 0;
  padding: 1px;
  overflow: hidden;
  border-width: 1px;
  border-style: solid;
}
.tabs-header-plain .tabs-tool {
  padding: 0 1px;
}
.tabs-wrap {
  position: relative;
  left: 0;
  overflow: hidden;
  width: 100%;
  margin: 0;
  padding: 0;
}
.tabs-scrolling {
  margin-left: 18px;
  margin-right: 18px;
}
.tabs-disabled {
  opacity: 0.3;
  filter: alpha(opacity=30);
}
.tabs {
  list-style-type: none;
  height: 26px;
  margin: 0px;
  padding: 0px;
  padding-left: 4px;
  width: 50000px;
  border-style: solid;
  border-width: 0 0 1px 0;
}
.tabs li {
  float: left;
  display: inline-block;
  margin: 0 4px -1px 0;
  padding: 0;
  position: relative;
  border: 0;
}
.tabs li a.tabs-inner {
  display: inline-block;
  text-decoration: none;
  margin: 0;
  padding: 0 10px;
  height: 25px;
  line-height: 25px;
  text-align: center;
  white-space: nowrap;
  border-width: 1px;
  border-style: solid;
  -moz-border-radius: 5px 5px 0 0;
  -webkit-border-radius: 5px 5px 0 0;
  border-radius: 5px 5px 0 0;
}
.tabs li.tabs-selected a.tabs-inner {
  font-weight: bold;
  outline: none;
}
.tabs li.tabs-selected a:hover.tabs-inner {
  cursor: default;
  pointer: default;
}
.tabs li a.tabs-close,
.tabs-p-tool {
  position: absolute;
  font-size: 1px;
  display: block;
  height: 12px;
  padding: 0;
  top: 50%;
  margin-top: -6px;
  overflow: hidden;
}
.tabs li a.tabs-close {
  width: 12px;
  right: 5px;
  opacity: 0.6;
  filter: alpha(opacity=60);
}
.tabs-p-tool {
  right: 16px;
}
.tabs-p-tool a {
  display: inline-block;
  font-size: 1px;
  width: 12px;
  height: 12px;
  margin: 0;
  opacity: 0.6;
  filter: alpha(opacity=60);
}
.tabs li a:hover.tabs-close,
.tabs-p-tool a:hover {
  opacity: 1;
  filter: alpha(opacity=100);
  cursor: hand;
  cursor: pointer;
}
.tabs-with-icon {
  padding-left: 18px;
}
.tabs-icon {
  position: absolute;
  width: 16px;
  height: 16px;
  left: 10px;
  top: 50%;
  margin-top: -8px;
}
.tabs-title {
  font-size: 12px;
}
.tabs-closable {
  padding-right: 8px;
}
.tabs-panels {
  margin: 0px;
  padding: 0px;
  border-width: 1px;
  border-style: solid;
  border-top-width: 0;
  overflow: hidden;
}
.tabs-header-bottom {
  border-width: 0 1px 1px 1px;
  padding: 0 0 2px 0;
}
.tabs-header-bottom .tabs {
  border-width: 1px 0 0 0;
}
.tabs-header-bottom .tabs li {
  margin: -1px 4px 0 0;
}
.tabs-header-bottom .tabs li a.tabs-inner {
  -moz-border-radius: 0 0 5px 5px;
  -webkit-border-radius: 0 0 5px 5px;
  border-radius: 0 0 5px 5px;
}
.tabs-header-bottom .tabs-tool {
  top: 0;
}
.tabs-header-bottom .tabs-scroller-left,
.tabs-header-bottom .tabs-scroller-right {
  top: 0;
  bottom: auto;
}
.tabs-panels-top {
  border-width: 1px 1px 0 1px;
}
.tabs-header-left {
  float: left;
  border-width: 1px 0 1px 1px;
  padding: 0;
}
.tabs-header-right {
  float: right;
  border-width: 1px 1px 1px 0;
  padding: 0;
}
.tabs-header-left .tabs-wrap,
.tabs-header-right .tabs-wrap {
  height: 100%;
}
.tabs-header-left .tabs {
  height: 100%;
  padding: 4px 0 0 2px;
  border-width: 0 1px 0 0;
}
.tabs-header-right .tabs {
  height: 100%;
  padding: 4px 2px 0 0;
  border-width: 0 0 0 1px;
}
.tabs-header-left .tabs li,
.tabs-header-right .tabs li {
  display: block;
  width: 100%;
  position: relative;
}
.tabs-header-left .tabs li {
  left: auto;
  right: 0;
  margin: 0 -1px 4px 0;
  float: right;
}
.tabs-header-right .tabs li {
  left: 0;
  right: auto;
  margin: 0 0 4px -1px;
  float: left;
}
.tabs-justified li a.tabs-inner {
  padding-left: 0;
  padding-right: 0;
}
.tabs-header-left .tabs li a.tabs-inner {
  display: block;
  text-align: left;
  padding-left: 10px;
  padding-right: 10px;
  -moz-border-radius: 5px 0 0 5px;
  -webkit-border-radius: 5px 0 0 5px;
  border-radius: 5px 0 0 5px;
}
.tabs-header-right .tabs li a.tabs-inner {
  display: block;
  text-align: left;
  padding-left: 10px;
  padding-right: 10px;
  -moz-border-radius: 0 5px 5px 0;
  -webkit-border-radius: 0 5px 5px 0;
  border-radius: 0 5px 5px 0;
}
.tabs-panels-right {
  float: right;
  border-width: 1px 1px 1px 0;
}
.tabs-panels-left {
  float: left;
  border-width: 1px 0 1px 1px;
}
.tabs-header-noborder,
.tabs-panels-noborder {
  border: 0px;
}
.tabs-header-plain {
  border: 0px;
  background: transparent;
}
.tabs-pill {
  padding-bottom: 3px;
}
.tabs-header-bottom .tabs-pill {
  padding-top: 3px;
  padding-bottom: 0;
}
.tabs-header-left .tabs-pill {
  padding-right: 3px;
}
.tabs-header-right .tabs-pill {
  padding-left: 3px;
}
.tabs-header .tabs-pill li a.tabs-inner {
  -moz-border-radius: 5px 5px 5px 5px;
  -webkit-border-radius: 5px 5px 5px 5px;
  border-radius: 5px 5px 5px 5px;
}
.tabs-header-narrow,
.tabs-header-narrow .tabs-narrow {
  padding: 0;
}
.tabs-narrow li,
.tabs-header-bottom .tabs-narrow li {
  margin-left: 0;
  margin-right: -1px;
}

.tabs-narrow li.tabs-last,
.tabs-header-bottom .tabs-narrow li.tabs-last {
  margin-right: 0;
}
.tabs-header-left .tabs-narrow,
.tabs-header-right .tabs-narrow {
  padding-top: 0;
}
.tabs-header-left .tabs-narrow li {
  margin-bottom: -1px;
  margin-right: -1px;
}
.tabs-header-left .tabs-narrow li.tabs-last,
.tabs-header-right .tabs-narrow li.tabs-last {
  margin-bottom: 0;
}
.tabs-header-right .tabs-narrow li {
  margin-bottom: -1px;
  margin-left: -1px;
}
.tabs-scroller-left {
  background: #3d3d3d url('images/tabs_icons.png') no-repeat 1px center;
}
.tabs-scroller-right {
  background: #3d3d3d url('images/tabs_icons.png') no-repeat -15px center;
}
.tabs li a.tabs-close {
  background: url('images/tabs_icons.png') no-repeat -34px center;
}
.tabs li a.tabs-inner:hover {
  background: #777;
  color: #fff;
  filter: none;
}
.tabs li.tabs-selected a.tabs-inner {
  background-color: #666;
  color: #fff;
  background: -webkit-linear-gradient(top,#454545 0,#666 100%);
  background: -moz-linear-gradient(top,#454545 0,#666 100%);
  background: -o-linear-gradient(top,#454545 0,#666 100%);
  background: linear-gradient(to bottom,#454545 0,#666 100%);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr=#454545,endColorstr=#666,GradientType=0);
}
.tabs-header-bottom .tabs li.tabs-selected a.tabs-inner {
  background: -webkit-linear-gradient(top,#666 0,#454545 100%);
  background: -moz-linear-gradient(top,#666 0,#454545 100%);
  background: -o-linear-gradient(top,#666 0,#454545 100%);
  background: linear-gradient(to bottom,#666 0,#454545 100%);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr=#666,endColorstr=#454545,GradientType=0);
}
.tabs-header-left .tabs li.tabs-selected a.tabs-inner {
  background: -webkit-linear-gradient(left,#454545 0,#666 100%);
  background: -moz-linear-gradient(left,#454545 0,#666 100%);
  background: -o-linear-gradient(left,#454545 0,#666 100%);
  background: linear-gradient(to right,#454545 0,#666 100%);
  background-repeat: repeat-y;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr=#454545,endColorstr=#666,GradientType=1);
}
.tabs-header-right .tabs li.tabs-selected a.tabs-inner {
  background: -webkit-linear-gradient(left,#666 0,#454545 100%);
  background: -moz-linear-gradient(left,#666 0,#454545 100%);
  background: -o-linear-gradient(left,#666 0,#454545 100%);
  background: linear-gradient(to right,#666 0,#454545 100%);
  background-repeat: repeat-y;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr=#666,endColorstr=#454545,GradientType=1);
}
.tabs li a.tabs-inner {
  color: #fff;
  background-color: #3d3d3d;
  background: -webkit-linear-gradient(top,#454545 0,#383838 100%);
  background: -moz-linear-gradient(top,#454545 0,#383838 100%);
  background: -o-linear-gradient(top,#454545 0,#383838 100%);
  background: linear-gradient(to bottom,#454545 0,#383838 100%);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr=#454545,endColorstr=#383838,GradientType=0);
}
.tabs-header,
.tabs-tool {
  background-color: #3d3d3d;
}
.tabs-header-plain {
  background: transparent;
}
.tabs-header,
.tabs-scroller-left,
.tabs-scroller-right,
.tabs-tool,
.tabs,
.tabs-panels,
.tabs li a.tabs-inner,
.tabs li.tabs-selected a.tabs-inner,
.tabs-header-bottom .tabs li.tabs-selected a.tabs-inner,
.tabs-header-left .tabs li.tabs-selected a.tabs-inner,
.tabs-header-right .tabs li.tabs-selected a.tabs-inner {
  border-color: #000;
}
.tabs-p-tool a:hover,
.tabs li a:hover.tabs-close,
.tabs-scroller-over {
  background-color: #777;
}
.tabs li.tabs-selected a.tabs-inner {
  border-bottom: 1px solid #666;
}
.tabs-header-bottom .tabs li.tabs-selected a.tabs-inner {
  border-top: 1px solid #666;
}
.tabs-header-left .tabs li.tabs-selected a.tabs-inner {
  border-right: 1px solid #666;
}
.tabs-header-right .tabs li.tabs-selected a.tabs-inner {
  border-left: 1px solid #666;
}
.tabs-header .tabs-pill li.tabs-selected a.tabs-inner {
  background: #990000;
  color: #fff;
  filter: none;
  border-color: #000;
}
.datagrid .panel-body {
  overflow: hidden;
  position: relative;
}
.datagrid-view {
  position: relative;
  overflow: hidden;
}
.datagrid-view1,
.datagrid-view2 {
  position: absolute;
  overflow: hidden;
  top: 0;
}
.datagrid-view1 {
  left: 0;
}
.datagrid-view2 {
  right: 0;
}
.datagrid-mask {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  opacity: 0.3;
  filter: alpha(opacity=30);
  display: none;
}
.datagrid-mask-msg {
  position: absolute;
  top: 50%;
  margin-top: -20px;
  padding: 10px 5px 10px 30px;
  width: auto;
  height: 16px;
  border-width: 2px;
  border-style: solid;
  display: none;
}
.datagrid-empty {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 25px;
  line-height: 25px;
  text-align: center;
}
.datagrid-sort-icon {
  padding: 0;
  display: none;
}
.datagrid-toolbar {
  height: auto;
  padding: 1px 2px;
  border-width: 0 0 1px 0;
  border-style: solid;
}
.datagrid-btn-separator {
  float: left;
  height: 24px;
  border-left: 1px solid #444;
  border-right: 1px solid #777;
  margin: 2px 1px;
}
.datagrid .datagrid-pager {
  display: block;
  margin: 0;
  border-width: 1px 0 0 0;
  border-style: solid;
}
.datagrid .datagrid-pager-top {
  border-width: 0 0 1px 0;
}
.datagrid-header {
  overflow: hidden;
  cursor: default;
  border-width: 0 0 1px 0;
  border-style: solid;
}
.datagrid-header-inner {
  float: left;
  width: 10000px;
}
.datagrid-header-row,
.datagrid-row {
  height: 32px;
}
.datagrid-header td,
.datagrid-body td,
.datagrid-footer td {
  border-width: 0 1px 1px 0;
  border-style: dotted;
  margin: 0;
  padding: 0;
}
.datagrid-cell,
.datagrid-cell-group,
.datagrid-header-rownumber,
.datagrid-cell-rownumber {
  margin: 0;
  padding: 0 4px;
  white-space: nowrap;
  word-wrap: normal;
  overflow: hidden;
  height: 18px;
  line-height: 18px;
  font-size: 12px;
}
.datagrid-header .datagrid-cell {
  height: auto;
}
.datagrid-header .datagrid-cell span {
  font-size: 12px;
}
.datagrid-cell-group {
  text-align: center;
  text-overflow: ellipsis;
}
.datagrid-header-rownumber,
.datagrid-cell-rownumber {
  width: 30px;
  text-align: center;
  margin: 0;
  padding: 0;
}
.datagrid-body {
  margin: 0;
  padding: 0;
  overflow: auto;
  zoom: 1;
}
.datagrid-view1 .datagrid-body-inner {
  padding-bottom: 20px;
}
.datagrid-view1 .datagrid-body {
  overflow: hidden;
}
.datagrid-footer {
  overflow: hidden;
}
.datagrid-footer-inner {
  border-width: 1px 0 0 0;
  border-style: solid;
  width: 10000px;
  float: left;
}
.datagrid-row-editing .datagrid-cell {
  height: auto;
}
.datagrid-header-check,
.datagrid-cell-check {
  padding: 0;
  width: 27px;
  height: 18px;
  font-size: 1px;
  text-align: center;
  overflow: hidden;
}
.datagrid-header-check input,
.datagrid-cell-check input {
  margin: 0;
  padding: 0;
  width: 15px;
  height: 18px;
}
.datagrid-resize-proxy {
  position: absolute;
  width: 1px;
  height: 10000px;
  top: 0;
  cursor: e-resize;
  display: none;
}
.datagrid-body .datagrid-editable {
  margin: 0;
  padding: 0;
}
.datagrid-body .datagrid-editable table {
  width: 100%;
  height: 100%;
}
.datagrid-body .datagrid-editable td {
  border: 0;
  margin: 0;
  padding: 0;
}
.datagrid-view .datagrid-editable-input {
  margin: 0;
  padding: 2px 4px;
  border: 1px solid #000;
  font-size: 12px;
  outline-style: none;
  -moz-border-radius: 0 0 0 0;
  -webkit-border-radius: 0 0 0 0;
  border-radius: 0 0 0 0;
}
.datagrid-view .validatebox-invalid {
  border-color: #ffa8a8;
}
.datagrid-sort .datagrid-sort-icon {
  display: inline;
  padding: 0 13px 0 0;
  background: url('images/datagrid_icons.png') no-repeat -64px center;
}
.datagrid-sort-desc .datagrid-sort-icon {
  display: inline;
  padding: 0 13px 0 0;
  background: url('images/datagrid_icons.png') no-repeat -16px center;
}
.datagrid-sort-asc .datagrid-sort-icon {
  display: inline;
  padding: 0 13px 0 0;
  background: url('images/datagrid_icons.png') no-repeat 0px center;
}
.datagrid-row-collapse {
  background: url('images/datagrid_icons.png') no-repeat -48px center;
}
.datagrid-row-expand {
  background: url('images/datagrid_icons.png') no-repeat -32px center;
}
.datagrid-mask-msg {
  background: #666 url('images/loading.gif') no-repeat scroll 5px center;
}
.datagrid-header,
.datagrid-td-rownumber {
  background-color: #444;
  background: -webkit-linear-gradient(top,#4c4c4c 0,#3f3f3f 100%);
  background: -moz-linear-gradient(top,#4c4c4c 0,#3f3f3f 100%);
  background: -o-linear-gradient(top,#4c4c4c 0,#3f3f3f 100%);
  background: linear-gradient(to bottom,#4c4c4c 0,#3f3f3f 100%);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr=#4c4c4c,endColorstr=#3f3f3f,GradientType=0);
}
.datagrid-cell-rownumber {
  color: #fff;
}
.datagrid-resize-proxy {
  background: #cccccc;
}
.datagrid-mask {
  background: #000;
}
.datagrid-mask-msg {
  border-color: #000;
}
.datagrid-toolbar,
.datagrid-pager {
  background: #555;
}
.datagrid-header,
.datagrid-toolbar,
.datagrid-pager,
.datagrid-footer-inner {
  border-color: #222;
}
.datagrid-header td,
.datagrid-body td,
.datagrid-footer td {
  border-color: #222;
}
.datagrid-htable,
.datagrid-btable,
.datagrid-ftable {
  color: #fff;
  border-collapse: separate;
}
.datagrid-row-alt {
  background: #555;
}
.datagrid-row-over,
.datagrid-header td.datagrid-header-over {
  background: #777;
  color: #fff;
  cursor: default;
}
.datagrid-row-selected {
  background: #990000;
  color: #fff;
}
.datagrid-row-editing .textbox,
.datagrid-row-editing .textbox-text {
  -moz-border-radius: 0 0 0 0;
  -webkit-border-radius: 0 0 0 0;
  border-radius: 0 0 0 0;
}
.datagrid-header .datagrid-filter-row td.datagrid-header-over {
  background: inherit;
}
.propertygrid .datagrid-view1 .datagrid-body td {
  padding-bottom: 1px;
  border-width: 0 1px 0 0;
}
.propertygrid .datagrid-group {
  overflow: hidden;
  border-width: 0 0 1px 0;
  border-style: solid;
}
.propertygrid .datagrid-group span {
  font-weight: bold;
}
.propertygrid .datagrid-view1 .datagrid-body td {
  border-color: #222;
}
.propertygrid .datagrid-view1 .datagrid-group {
  border-color: #3d3d3d;
}
.propertygrid .datagrid-view2 .datagrid-group {
  border-color: #222;
}
.propertygrid .datagrid-group,
.propertygrid .datagrid-view1 .datagrid-body,
.propertygrid .datagrid-view1 .datagrid-row-over,
.propertygrid .datagrid-view1 .datagrid-row-selected {
  background: #3d3d3d;
}
.datalist .datagrid-header {
  border-width: 0;
}
.datalist .datagrid-group,
.m-list .m-list-group {
  height: 25px;
  line-height: 25px;
  font-weight: bold;
  overflow: hidden;
  background-color: #444;
  border-style: solid;
  border-width: 0 0 1px 0;
  border-color: #222;
}
.datalist .datagrid-group-expander {
  display: none;
}
.datalist .datagrid-group-title {
  padding: 0 4px;
}
.datalist .datagrid-btable {
  width: 100%;
  table-layout: fixed;
}
.datalist .datagrid-row td {
  border-style: solid;
  border-left-color: transparent;
  border-right-color: transparent;
  border-bottom-width: 0;
}
.datalist-lines .datagrid-row td {
  border-bottom-width: 1px;
}
.datalist .datagrid-cell,
.m-list li {
  width: auto;
  height: auto;
  padding: 2px 4px;
  line-height: 18px;
  position: relative;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}
.datalist-link,
.m-list li>a {
  display: block;
  position: relative;
  cursor: pointer;
  color: #fff;
  text-decoration: none;
  overflow: hidden;
  margin: -2px -4px;
  padding: 2px 4px;
  padding-right: 16px;
  line-height: 18px;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}
.datalist-link::after,
.m-list li>a::after {
  position: absolute;
  display: block;
  width: 8px;
  height: 8px;
  content: '';
  right: 6px;
  top: 50%;
  margin-top: -4px;
  border-style: solid;
  border-width: 1px 1px 0 0;
  -ms-transform: rotate(45deg);
  -moz-transform: rotate(45deg);
  -webkit-transform: rotate(45deg);
  -o-transform: rotate(45deg);
  transform: rotate(45deg);
}
.m-list {
  margin: 0;
  padding: 0;
  list-style: none;
}
.m-list li {
  border-style: solid;
  border-width: 0 0 1px 0;
  border-color: #222;
}
.m-list li>a:hover {
  background: #777;
  color: #fff;
}
.m-list .m-list-group {
  padding: 0 4px;
}
.pagination {
  zoom: 1;
  padding: 2px;
}
.pagination table {
  float: left;
  height: 30px;
}
.pagination td {
  border: 0;
}
.pagination-btn-separator {
  float: left;
  height: 24px;
  border-left: 1px solid #444;
  border-right: 1px solid #777;
  margin: 3px 1px;
}
.pagination .pagination-num {
  border-width: 1px;
  border-style: solid;
  margin: 0 2px;
  padding: 2px;
  width: 3em;
  height: auto;
  text-align: center;
  font-size: 12px;
}
.pagination-page-list {
  margin: 0px 6px;
  padding: 1px 2px;
  width: auto;
  height: auto;
  border-width: 1px;
  border-style: solid;
}
.pagination-info {
  float: right;
  margin: 0 6px;
  padding: 0;
  height: 30px;
  line-height: 30px;
  font-size: 12px;
}
.pagination span {
  font-size: 12px;
}
.pagination-link .l-btn-text {
  box-sizing: border-box;
  text-align: center;
  margin: 0;
  padding: 0 .5em;
  width: auto;
  min-width: 28px;
}
.pagination-first {
  background: url('images/pagination_icons.png') no-repeat 0 center;
}
.pagination-prev {
  background: url('images/pagination_icons.png') no-repeat -16px center;
}
.pagination-next {
  background: url('images/pagination_icons.png') no-repeat -32px center;
}
.pagination-last {
  background: url('images/pagination_icons.png') no-repeat -48px center;
}
.pagination-load {
  background: url('images/pagination_icons.png') no-repeat -64px center;
}
.pagination-loading {
  background: url('images/loading.gif') no-repeat center center;
}
.pagination-page-list,
.pagination .pagination-num {
  border-color: #000;
}
.calendar {
  border-width: 1px;
  border-style: solid;
  padding: 1px;
  overflow: hidden;
}
.calendar table {
  table-layout: fixed;
  border-collapse: separate;
  font-size: 12px;
  width: 100%;
  height: 100%;
}
.calendar table td,
.calendar table th {
  font-size: 12px;
}
.calendar-noborder {
  border: 0;
}
.calendar-header {
  position: relative;
  height: 28px;
}
.calendar-title {
  text-align: center;
  height: 28px;
}
.calendar-title span {
  position: relative;
  display: inline-block;
  top: 0px;
  padding: 0 3px;
  height: 28px;
  line-height: 28px;
  font-size: 12px;
  cursor: pointer;
  -moz-border-radius: 5px 5px 5px 5px;
  -webkit-border-radius: 5px 5px 5px 5px;
  border-radius: 5px 5px 5px 5px;
}
.calendar-prevmonth,
.calendar-nextmonth,
.calendar-prevyear,
.calendar-nextyear {
  position: absolute;
  top: 50%;
  margin-top: -8px;
  width: 16px;
  height: 16px;
  cursor: pointer;
  font-size: 1px;
  -moz-border-radius: 5px 5px 5px 5px;
  -webkit-border-radius: 5px 5px 5px 5px;
  border-radius: 5px 5px 5px 5px;
}
.calendar-prevmonth {
  left: 20px;
  background: url('images/calendar_arrows.png') no-repeat -16px 0;
}
.calendar-nextmonth {
  right: 20px;
  background: url('images/calendar_arrows.png') no-repeat -32px 0;
}
.calendar-prevyear {
  left: 3px;
  background: url('images/calendar_arrows.png') no-repeat 0px 0;
}
.calendar-nextyear {
  right: 3px;
  background: url('images/calendar_arrows.png') no-repeat -48px 0;
}
.calendar-body {
  position: relative;
}
.calendar-body th,
.calendar-body td {
  text-align: center;
}
.calendar-day {
  border: 0;
  padding: 1px;
  cursor: pointer;
  -moz-border-radius: 5px 5px 5px 5px;
  -webkit-border-radius: 5px 5px 5px 5px;
  border-radius: 5px 5px 5px 5px;
}
.calendar-other-month {
  opacity: 0.3;
  filter: alpha(opacity=30);
}
.calendar-disabled {
  opacity: 0.6;
  filter: alpha(opacity=60);
  cursor: default;
}
.calendar-menu {
  position: absolute;
  top: 0;
  left: 0;
  width: 180px;
  height: 150px;
  padding: 5px;
  font-size: 12px;
  display: none;
  overflow: hidden;
}
.calendar-menu-year-inner {
  text-align: center;
  padding-bottom: 5px;
}
.calendar-menu-year {
  width: 80px;
  line-height: 26px;
  text-align: center;
  border-width: 1px;
  border-style: solid;
  outline-style: none;
  resize: none;
  margin: 0;
  padding: 0;
  font-weight: bold;
  font-size: 12px;
  -moz-border-radius: 5px 5px 5px 5px;
  -webkit-border-radius: 5px 5px 5px 5px;
  border-radius: 5px 5px 5px 5px;
}
.calendar-menu-prev,
.calendar-menu-next {
  display: inline-block;
  width: 25px;
  height: 28px;
  vertical-align: top;
  cursor: pointer;
  -moz-border-radius: 5px 5px 5px 5px;
  -webkit-border-radius: 5px 5px 5px 5px;
  border-radius: 5px 5px 5px 5px;
}
.calendar-menu-prev {
  margin-right: 10px;
  background: url('images/calendar_arrows.png') no-repeat 5px center;
}
.calendar-menu-next {
  margin-left: 10px;
  background: url('images/calendar_arrows.png') no-repeat -44px center;
}
.calendar-menu-month {
  text-align: center;
  cursor: pointer;
  font-weight: bold;
  -moz-border-radius: 5px 5px 5px 5px;
  -webkit-border-radius: 5px 5px 5px 5px;
  border-radius: 5px 5px 5px 5px;
}
.calendar-body th,
.calendar-menu-month {
  color: #ffffff;
}
.calendar-day {
  color: #fff;
}
.calendar-sunday {
  color: #CC2222;
}
.calendar-saturday {
  color: #00ee00;
}
.calendar-today {
  color: #0000ff;
}
.calendar-menu-year {
  border-color: #000;
}
.calendar {
  border-color: #000;
}
.calendar-header {
  background: #3d3d3d;
}
.calendar-body,
.calendar-menu {
  background: #666;
}
.calendar-body th {
  background: #555;
  padding: 4px 0;
}
.calendar-hover,
.calendar-nav-hover,
.calendar-menu-hover {
  background-color: #777;
  color: #fff;
}
.calendar-hover {
  border: 1px solid #555;
  padding: 0;
}
.calendar-selected {
  background-color: #990000;
  color: #fff;
  border: 1px solid #AA0000;
  padding: 0;
}
.datebox-calendar-inner {
  height: 250px;
}
.datebox-button {
  padding: 4px 0;
  text-align: center;
}
.datebox-button a {
  line-height: 22px;
  font-size: 12px;
  font-weight: bold;
  text-decoration: none;
  opacity: 0.6;
  filter: alpha(opacity=60);
}
.datebox-button a:hover {
  opacity: 1.0;
  filter: alpha(opacity=100);
}
.datebox-current,
.datebox-close {
  float: left;
}
.datebox-close {
  float: right;
}
.datebox .combo-arrow {
  background-image: url('images/datebox_arrow.png');
  background-position: center center;
}
.datebox-button {
  background-color: #555;
}
.datebox-button a {
  color: #fff;
}
.spinner-arrow {
  display: inline-block;
  overflow: hidden;
  vertical-align: top;
  margin: 0;
  padding: 0;
  opacity: 1.0;
  filter: alpha(opacity=100);
  width: 18px;
}
.spinner-arrow.spinner-button-top,
.spinner-arrow.spinner-button-bottom,
.spinner-arrow.spinner-button-left,
.spinner-arrow.spinner-button-right {
  background-color: #3d3d3d;
}
.spinner-arrow-up,
.spinner-arrow-down {
  opacity: 0.6;
  filter: alpha(opacity=60);
  display: block;
  font-size: 1px;
  width: 18px;
  height: 10px;
  width: 100%;
  height: 50%;
  color: #fff;
  outline-style: none;
  background-color: #3d3d3d;
}
.spinner-button-updown {
  opacity: 1.0;
}
.spinner-button-updown .spinner-button-top,
.spinner-button-updown .spinner-button-bottom {
  position: relative;
  display: block;
  width: 100%;
  height: 50%;
}
.spinner-button-updown .spinner-arrow-up,
.spinner-button-updown .spinner-arrow-down {
  opacity: 1.0;
  filter: alpha(opacity=100);
  cursor: pointer;
  width: 16px;
  height: 16px;
  top: 50%;
  left: 50%;
  margin-top: -8px;
  margin-left: -8px;
  position: absolute;
}
.spinner-button-updown .spinner-button-top,
.spinner-button-updown .spinner-button-bottom {
  cursor: pointer;
  opacity: 0.6;
  filter: alpha(opacity=60);
}
.spinner-button-updown .spinner-button-top:hover,
.spinner-button-updown .spinner-button-bottom:hover {
  opacity: 1.0;
  filter: alpha(opacity=100);
}
.spinner-button-updown .spinner-arrow-up,
.spinner-button-updown .spinner-arrow-down,
.spinner-button-updown .spinner-arrow-up:hover,
.spinner-button-updown .spinner-arrow-down:hover {
  background-color: transparent;
}
.spinner-arrow-hover {
  background-color: #777;
  opacity: 1.0;
  filter: alpha(opacity=100);
}
.spinner-button-top:hover,
.spinner-button-bottom:hover,
.spinner-button-left:hover,
.spinner-button-right:hover,
.spinner-arrow-up:hover,
.spinner-arrow-down:hover {
  opacity: 1.0;
  filter: alpha(opacity=100);
  background-color: #777;
}
.textbox-disabled .spinner-button-top:hover,
.textbox-disabled .spinner-button-bottom:hover,
.textbox-disabled .spinner-button-left:hover,
.textbox-disabled .spinner-button-right:hover,
.textbox-icon-disabled .spinner-arrow-up:hover,
.textbox-icon-disabled .spinner-arrow-down:hover {
  opacity: 0.6;
  filter: alpha(opacity=60);
  background-color: #3d3d3d;
  cursor: default;
}
.spinner .textbox-icon-disabled {
  opacity: 0.6;
  filter: alpha(opacity=60);
}
.spinner-arrow-up {
  background: url('images/spinner_arrows.png') no-repeat 1px center;
  background-color: #3d3d3d;
}
.spinner-arrow-down {
  background: url('images/spinner_arrows.png') no-repeat -15px center;
  background-color: #3d3d3d;
}
.spinner-button-up {
  background: url('images/spinner_arrows.png') no-repeat -32px center;
}
.spinner-button-down {
  background: url('images/spinner_arrows.png') no-repeat -48px center;
}
.progressbar {
  border-width: 1px;
  border-style: solid;
  -moz-border-radius: 5px 5px 5px 5px;
  -webkit-border-radius: 5px 5px 5px 5px;
  border-radius: 5px 5px 5px 5px;
  overflow: hidden;
  position: relative;
}
.progressbar-text {
  text-align: center;
  position: absolute;
}
.progressbar-value {
  position: relative;
  overflow: hidden;
  width: 0;
  -moz-border-radius: 5px 0 0 5px;
  -webkit-border-radius: 5px 0 0 5px;
  border-radius: 5px 0 0 5px;
}
.progressbar {
  border-color: #000;
}
.progressbar-text {
  color: #fff;
  font-size: 12px;
}
.progressbar-value,
.progressbar-value .progressbar-text {
  background-color: #990000;
  color: #fff;
}
.searchbox-button {
  width: 18px;
  height: 20px;
  overflow: hidden;
  display: inline-block;
  vertical-align: top;
  cursor: pointer;
  opacity: 0.6;
  filter: alpha(opacity=60);
}
.searchbox-button-hover {
  opacity: 1.0;
  filter: alpha(opacity=100);
}
.searchbox .l-btn-plain {
  border: 0;
  padding: 0;
  vertical-align: top;
  opacity: 0.6;
  filter: alpha(opacity=60);
  -moz-border-radius: 0 0 0 0;
  -webkit-border-radius: 0 0 0 0;
  border-radius: 0 0 0 0;
}
.searchbox .l-btn-plain:hover {
  border: 0;
  padding: 0;
  opacity: 1.0;
  filter: alpha(opacity=100);
  -moz-border-radius: 0 0 0 0;
  -webkit-border-radius: 0 0 0 0;
  border-radius: 0 0 0 0;
}
.searchbox a.m-btn-plain-active {
  -moz-border-radius: 0 0 0 0;
  -webkit-border-radius: 0 0 0 0;
  border-radius: 0 0 0 0;
}
.searchbox .m-btn-active {
  border-width: 0 1px 0 0;
  -moz-border-radius: 0 0 0 0;
  -webkit-border-radius: 0 0 0 0;
  border-radius: 0 0 0 0;
}
.searchbox .textbox-button-right {
  border-width: 0 0 0 1px;
}
.searchbox .textbox-button-left {
  border-width: 0 1px 0 0;
}
.searchbox-button {
  background: url('images/searchbox_button.png') no-repeat center center;
}
.searchbox .l-btn-plain {
  background: #3d3d3d;
}
.searchbox .l-btn-plain-disabled,
.searchbox .l-btn-plain-disabled:hover {
  opacity: 0.5;
  filter: alpha(opacity=50);
}
.slider-disabled {
  opacity: 0.5;
  filter: alpha(opacity=50);
}
.slider-h {
  height: 22px;
}
.slider-v {
  width: 22px;
}
.slider-inner {
  position: relative;
  height: 6px;
  top: 7px;
  border-width: 1px;
  border-style: solid;
  border-radius: 5px;
}
.slider-handle {
  position: absolute;
  display: block;
  outline: none;
  width: 20px;
  height: 20px;
  top: 50%;
  margin-top: -10px;
  margin-left: -10px;
}
.slider-tip {
  position: absolute;
  display: inline-block;
  line-height: 12px;
  font-size: 12px;
  white-space: nowrap;
  top: -22px;
}
.slider-rule {
  position: relative;
  top: 15px;
}
.slider-rule span {
  position: absolute;
  display: inline-block;
  font-size: 0;
  height: 5px;
  border-width: 0 0 0 1px;
  border-style: solid;
}
.slider-rulelabel {
  position: relative;
  top: 20px;
}
.slider-rulelabel span {
  position: absolute;
  display: inline-block;
  font-size: 12px;
}
.slider-v .slider-inner {
  width: 6px;
  left: 7px;
  top: 0;
  float: left;
}
.slider-v .slider-handle {
  left: 50%;
  margin-top: -10px;
}
.slider-v .slider-tip {
  left: -10px;
  margin-top: -6px;
}
.slider-v .slider-rule {
  float: left;
  top: 0;
  left: 16px;
}
.slider-v .slider-rule span {
  width: 5px;
  height: 'auto';
  border-left: 0;
  border-width: 1px 0 0 0;
  border-style: solid;
}
.slider-v .slider-rulelabel {
  float: left;
  top: 0;
  left: 23px;
}
.slider-handle {
  background: url('images/slider_handle.png') no-repeat;
}
.slider-inner {
  border-color: #000;
  background: #3d3d3d;
}
.slider-rule span {
  border-color: #000;
}
.slider-rulelabel span {
  color: #fff;
}
.menu {
  position: absolute;
  margin: 0;
  padding: 2px;
  border-width: 1px;
  border-style: solid;
  overflow: hidden;
}
.menu-inline {
  position: relative;
}
.menu-item {
  position: relative;
  margin: 0;
  padding: 0;
  overflow: hidden;
  white-space: nowrap;
  cursor: pointer;
  border-width: 1px;
  border-style: solid;
}
.menu-text {
  height: 20px;
  line-height: 20px;
  float: left;
  padding-left: 28px;
}
.menu-icon {
  position: absolute;
  width: 16px;
  height: 16px;
  left: 2px;
  top: 50%;
  margin-top: -8px;
}
.menu-rightarrow {
  position: absolute;
  width: 16px;
  height: 16px;
  right: 0;
  top: 50%;
  margin-top: -8px;
}
.menu-line {
  position: absolute;
  left: 26px;
  top: 0;
  height: 2000px;
  font-size: 1px;
}
.menu-sep {
  margin: 3px 0px 3px 25px;
  font-size: 1px;
}
.menu-noline .menu-line {
  display: none;
}
.menu-noline .menu-sep {
  margin-left: 0;
  margin-right: 0;
}
.menu-active {
  -moz-border-radius: 5px 5px 5px 5px;
  -webkit-border-radius: 5px 5px 5px 5px;
  border-radius: 5px 5px 5px 5px;
}
.menu-item-disabled {
  opacity: 0.5;
  filter: alpha(opacity=50);
  cursor: default;
}
.menu-text,
.menu-text span {
  font-size: 12px;
}
.menu-shadow {
  position: absolute;
  -moz-border-radius: 5px 5px 5px 5px;
  -webkit-border-radius: 5px 5px 5px 5px;
  border-radius: 5px 5px 5px 5px;
  background: #777;
  -moz-box-shadow: 2px 2px 3px #787878;
  -webkit-box-shadow: 2px 2px 3px #787878;
  box-shadow: 2px 2px 3px #787878;
  filter: progid:DXImageTransform.Microsoft.Blur(pixelRadius=2,MakeShadow=false,ShadowOpacity=0.2);
}
.menu-rightarrow {
  background: url('images/menu_arrows.png') no-repeat -32px center;
}
.menu-line {
  border-left: 1px solid #444;
  border-right: 1px solid #777;
}
.menu-sep {
  border-top: 1px solid #444;
  border-bottom: 1px solid #777;
}
.menu {
  background-color: #666;
  border-color: #444;
  color: #fff;
}
.menu-content {
  background: #666;
}
.menu-item {
  border-color: transparent;
  _border-color: #666;
}
.menu-active {
  border-color: #555;
  color: #fff;
  background: #777;
}
.menu-active-disabled {
  border-color: transparent;
  background: transparent;
  color: #fff;
}
.m-btn-downarrow,
.s-btn-downarrow {
  display: inline-block;
  position: absolute;
  width: 16px;
  height: 16px;
  font-size: 1px;
  right: 0;
  top: 50%;
  margin-top: -8px;
}
.m-btn-active,
.s-btn-active {
  background: #777;
  color: #fff;
  border: 1px solid #555;
  filter: none;
}
.m-btn-plain-active,
.s-btn-plain-active {
  background: transparent;
  padding: 0;
  border-width: 1px;
  border-style: solid;
  -moz-border-radius: 5px 5px 5px 5px;
  -webkit-border-radius: 5px 5px 5px 5px;
  border-radius: 5px 5px 5px 5px;
}
.m-btn .l-btn-left .l-btn-text {
  margin-right: 20px;
}
.m-btn .l-btn-icon-right .l-btn-text {
  margin-right: 40px;
}
.m-btn .l-btn-icon-right .l-btn-icon {
  right: 20px;
}
.m-btn .l-btn-icon-top .l-btn-text {
  margin-right: 4px;
  margin-bottom: 14px;
}
.m-btn .l-btn-icon-bottom .l-btn-text {
  margin-right: 4px;
  margin-bottom: 34px;
}
.m-btn .l-btn-icon-bottom .l-btn-icon {
  top: auto;
  bottom: 20px;
}
.m-btn .l-btn-icon-top .m-btn-downarrow,
.m-btn .l-btn-icon-bottom .m-btn-downarrow {
  top: auto;
  bottom: 0px;
  left: 50%;
  margin-left: -8px;
}
.m-btn-line {
  display: inline-block;
  position: absolute;
  font-size: 1px;
  display: none;
}
.m-btn .l-btn-left .m-btn-line {
  right: 0;
  width: 16px;
  height: 500px;
  border-style: solid;
  border-color: #cccccc;
  border-width: 0 0 0 1px;
}
.m-btn .l-btn-icon-top .m-btn-line,
.m-btn .l-btn-icon-bottom .m-btn-line {
  left: 0;
  bottom: 0;
  width: 500px;
  height: 16px;
  border-width: 1px 0 0 0;
}
.m-btn-large .l-btn-icon-right .l-btn-text {
  margin-right: 56px;
}
.m-btn-large .l-btn-icon-bottom .l-btn-text {
  margin-bottom: 50px;
}
.m-btn-downarrow,
.s-btn-downarrow {
  background: url('images/menu_arrows.png') no-repeat 0 center;
}
.m-btn-plain-active,
.s-btn-plain-active {
  border-color: #555;
  background-color: #777;
  color: #fff;
}
.s-btn:hover .m-btn-line,
.s-btn-active .m-btn-line,
.s-btn-plain-active .m-btn-line {
  display: inline-block;
}
.l-btn:hover .s-btn-downarrow,
.s-btn-active .s-btn-downarrow,
.s-btn-plain-active .s-btn-downarrow {
  border-style: solid;
  border-color: #cccccc;
  border-width: 0 0 0 1px;
}
.switchbutton {
  text-decoration: none;
  display: inline-block;
  overflow: hidden;
  vertical-align: middle;
  margin: 0;
  padding: 0;
  cursor: pointer;
  background: #555;
  border: 1px solid #555;
  -moz-border-radius: 5px 5px 5px 5px;
  -webkit-border-radius: 5px 5px 5px 5px;
  border-radius: 5px 5px 5px 5px;
}
.switchbutton-inner {
  display: inline-block;
  overflow: hidden;
  position: relative;
  top: -1px;
  left: -1px;
}
.switchbutton-on,
.switchbutton-off,
.switchbutton-handle {
  display: inline-block;
  text-align: center;
  height: 100%;
  float: left;
  font-size: 12px;
  -moz-border-radius: 5px 5px 5px 5px;
  -webkit-border-radius: 5px 5px 5px 5px;
  border-radius: 5px 5px 5px 5px;
}
.switchbutton-on {
  background: #990000;
  color: #fff;
}
.switchbutton-off {
  background-color: #666;
  color: #fff;
}
.switchbutton-on,
.switchbutton-reversed .switchbutton-off {
  -moz-border-radius: 5px 0 0 5px;
  -webkit-border-radius: 5px 0 0 5px;
  border-radius: 5px 0 0 5px;
}
.switchbutton-off,
.switchbutton-reversed .switchbutton-on {
  -moz-border-radius: 0 5px 5px 0;
  -webkit-border-radius: 0 5px 5px 0;
  border-radius: 0 5px 5px 0;
}
.switchbutton-handle {
  position: absolute;
  top: 0;
  left: 50%;
  background-color: #666;
  color: #fff;
  border: 1px solid #555;
  -moz-box-shadow: 0 0 3px 0 #555;
  -webkit-box-shadow: 0 0 3px 0 #555;
  box-shadow: 0 0 3px 0 #555;
}
.switchbutton-value {
  position: absolute;
  top: 0;
  left: -5000px;
}
.switchbutton-disabled {
  opacity: 0.5;
  filter: alpha(opacity=50);
}
.switchbutton-disabled,
.switchbutton-readonly {
  cursor: default;
}
.messager-body {
  padding: 10px 10px 30px 10px;
  overflow: auto;
}
.messager-button {
  text-align: center;
  padding: 5px;
}
.messager-button .l-btn {
  width: 70px;
}
.messager-icon {
  float: left;
  width: 32px;
  height: 32px;
  margin: 0 10px 10px 0;
}
.messager-error {
  background: url('images/messager_icons.png') no-repeat scroll -64px 0;
}
.messager-info {
  background: url('images/messager_icons.png') no-repeat scroll 0 0;
}
.messager-question {
  background: url('images/messager_icons.png') no-repeat scroll -32px 0;
}
.messager-warning {
  background: url('images/messager_icons.png') no-repeat scroll -96px 0;
}
.messager-progress {
  padding: 10px;
}
.messager-p-msg {
  margin-bottom: 5px;
}
.messager-body .messager-input {
  width: 100%;
  padding: 4px 0;
  outline-style: none;
  border: 1px solid #000;
}
.window-thinborder .messager-button {
  padding-bottom: 8px;
}
.tree {
  margin: 0;
  padding: 0;
  list-style-type: none;
}
.tree li {
  white-space: nowrap;
}
.tree li ul {
  list-style-type: none;
  margin: 0;
  padding: 0;
}
.tree-node {
  height: 26px;
  white-space: nowrap;
  cursor: pointer;
}
.tree-hit {
  cursor: pointer;
}
.tree-expanded,
.tree-collapsed,
.tree-folder,
.tree-file,
.tree-checkbox,
.tree-indent {
  display: inline-block;
  width: 16px;
  height: 18px;
  margin: 4px 0;
  vertical-align: middle;
  overflow: hidden;
}
.tree-expanded {
  background: url('images/tree_icons.png') no-repeat -18px 0px;
}
.tree-expanded-hover {
  background: url('images/tree_icons.png') no-repeat -50px 0px;
}
.tree-collapsed {
  background: url('images/tree_icons.png') no-repeat 0px 0px;
}
.tree-collapsed-hover {
  background: url('images/tree_icons.png') no-repeat -32px 0px;
}
.tree-lines .tree-expanded,
.tree-lines .tree-root-first .tree-expanded {
  background: url('images/tree_icons.png') no-repeat -144px 0;
}
.tree-lines .tree-collapsed,
.tree-lines .tree-root-first .tree-collapsed {
  background: url('images/tree_icons.png') no-repeat -128px 0;
}
.tree-lines .tree-node-last .tree-expanded,
.tree-lines .tree-root-one .tree-expanded {
  background: url('images/tree_icons.png') no-repeat -80px 0;
}
.tree-lines .tree-node-last .tree-collapsed,
.tree-lines .tree-root-one .tree-collapsed {
  background: url('images/tree_icons.png') no-repeat -64px 0;
}
.tree-line {
  background: url('images/tree_icons.png') no-repeat -176px 0;
}
.tree-join {
  background: url('images/tree_icons.png') no-repeat -192px 0;
}
.tree-joinbottom {
  background: url('images/tree_icons.png') no-repeat -160px 0;
}
.tree-folder {
  background: url('images/tree_icons.png') no-repeat -208px 0;
}
.tree-folder-open {
  background: url('images/tree_icons.png') no-repeat -224px 0;
}
.tree-file {
  background: url('images/tree_icons.png') no-repeat -240px 0;
}
.tree-loading {
  background: url('images/loading.gif') no-repeat center center;
}
.tree-checkbox0 {
  background: url('images/tree_icons.png') no-repeat -208px -18px;
}
.tree-checkbox1 {
  background: url('images/tree_icons.png') no-repeat -224px -18px;
}
.tree-checkbox2 {
  background: url('images/tree_icons.png') no-repeat -240px -18px;
}
.tree-title {
  font-size: 12px;
  display: inline-block;
  text-decoration: none;
  vertical-align: middle;
  white-space: nowrap;
  padding: 0 2px;
  margin: 4px 0;
  height: 18px;
  line-height: 18px;
}
.tree-node-proxy {
  font-size: 12px;
  line-height: 20px;
  padding: 0 2px 0 20px;
  border-width: 1px;
  border-style: solid;
  z-index: 9900000;
}
.tree-dnd-icon {
  display: inline-block;
  position: absolute;
  width: 16px;
  height: 18px;
  left: 2px;
  top: 50%;
  margin-top: -9px;
}
.tree-dnd-yes {
  background: url('images/tree_icons.png') no-repeat -256px 0;
}
.tree-dnd-no {
  background: url('images/tree_icons.png') no-repeat -256px -18px;
}
.tree-node-top {
  border-top: 1px dotted red;
}
.tree-node-bottom {
  border-bottom: 1px dotted red;
}
.tree-node-append .tree-title {
  border: 1px dotted red;
}
.tree-editor {
  border: 1px solid #000;
  font-size: 12px;
  height: 26px;
  line-height: 26px;
  padding: 0 4px;
  margin: 0;
  width: 80px;
  outline-style: none;
  vertical-align: middle;
  position: absolute;
  top: 0;
}
.tree-node-proxy {
  background-color: #666;
  color: #fff;
  border-color: #000;
}
.tree-node-hover {
  background: #777;
  color: #fff;
}
.tree-node-selected {
  background: #990000;
  color: #fff;
}
.tree-node-hidden {
  display: none;
}
.validatebox-invalid {
  border-color: #ffa8a8;
  background-color: #fff3f3;
  color: #000;
}
.tooltip {
  position: absolute;
  display: none;
  z-index: 9900000;
  outline: none;
  opacity: 1;
  filter: alpha(opacity=100);
  padding: 5px;
  border-width: 1px;
  border-style: solid;
  border-radius: 5px;
  -moz-border-radius: 5px 5px 5px 5px;
  -webkit-border-radius: 5px 5px 5px 5px;
  border-radius: 5px 5px 5px 5px;
}
.tooltip-content {
  font-size: 12px;
}
.tooltip-arrow-outer,
.tooltip-arrow {
  position: absolute;
  width: 0;
  height: 0;
  line-height: 0;
  font-size: 0;
  border-style: solid;
  border-width: 6px;
  border-color: transparent;
  _border-color: tomato;
  _filter: chroma(color=tomato);
}
.tooltip-arrow {
  display: none \9;
}
.tooltip-right .tooltip-arrow-outer {
  left: 0;
  top: 50%;
  margin: -6px 0 0 -13px;
}
.tooltip-right .tooltip-arrow {
  left: 0;
  top: 50%;
  margin: -6px 0 0 -12px;
}
.tooltip-left .tooltip-arrow-outer {
  right: 0;
  top: 50%;
  margin: -6px -13px 0 0;
}
.tooltip-left .tooltip-arrow {
  right: 0;
  top: 50%;
  margin: -6px -12px 0 0;
}
.tooltip-top .tooltip-arrow-outer {
  bottom: 0;
  left: 50%;
  margin: 0 0 -13px -6px;
}
.tooltip-top .tooltip-arrow {
  bottom: 0;
  left: 50%;
  margin: 0 0 -12px -6px;
}
.tooltip-bottom .tooltip-arrow-outer {
  top: 0;
  left: 50%;
  margin: -13px 0 0 -6px;
}
.tooltip-bottom .tooltip-arrow {
  top: 0;
  left: 50%;
  margin: -12px 0 0 -6px;
}
.tooltip {
  background-color: #666;
  border-color: #000;
  color: #fff;
}
.tooltip-right .tooltip-arrow-outer {
  border-right-color: #000;
}
.tooltip-right .tooltip-arrow {
  border-right-color: #666;
}
.tooltip-left .tooltip-arrow-outer {
  border-left-color: #000;
}
.tooltip-left .tooltip-arrow {
  border-left-color: #666;
}
.tooltip-top .tooltip-arrow-outer {
  border-top-color: #000;
}
.tooltip-top .tooltip-arrow {
  border-top-color: #666;
}
.tooltip-bottom .tooltip-arrow-outer {
  border-bottom-color: #000;
}
.tooltip-bottom .tooltip-arrow {
  border-bottom-color: #666;
}
.radiobutton {
  position: relative;
  border: 2px solid #AA0000;
  border-radius: 50%;
}
.radiobutton-inner {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: #AA0000;
  border-radius: 50%;
  transform: scale(.6);
}
.radiobutton-disabled {
  opacity: 0.6;
}
.radiobutton-value {
  position: absolute;
  overflow: hidden;
  width: 1px;
  height: 1px;
  left: -999px;
}
.checkbox {
  position: relative;
  border: 2px solid #AA0000;
  -moz-border-radius: 5px 5px 5px 5px;
  -webkit-border-radius: 5px 5px 5px 5px;
  border-radius: 5px 5px 5px 5px;
}
.checkbox-checked {
  border: 0;
  background: #AA0000;
}
.checkbox-inner {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
}
.checkbox path {
  stroke-width: 2px;
}
.checkbox-disabled {
  opacity: 0.6;
}
.checkbox-value {
  position: absolute;
  overflow: hidden;
  width: 1px;
  height: 1px;
  left: -999px;
}