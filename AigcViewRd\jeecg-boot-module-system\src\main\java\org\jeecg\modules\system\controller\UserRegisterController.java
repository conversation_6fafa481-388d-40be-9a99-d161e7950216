package org.jeecg.modules.system.controller;

import org.jeecg.modules.system.service.IUserRegisterService;
import org.jeecg.modules.system.service.IAicgVerifyCodeService;
import org.jeecg.modules.system.dto.RegisterDTO;
import org.jeecg.modules.system.entity.VerifyCodeResult;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.util.IPUtils;
import org.jeecg.common.util.oConvertUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

/**
 * @Description: 用户注册控制器
 * @Author: jeecg-boot
 * @Date: 2025-06-19
 * @Version: V1.0
 */
@Api(tags = "用户注册")
@RestController
@RequestMapping("/api/auth")
@Slf4j
public class UserRegisterController {

    @Autowired
    private IUserRegisterService userRegisterService;

    @Autowired
    private IAicgVerifyCodeService verifyCodeService;

    /**
     * 用户注册
     */
    @ApiOperation(value = "用户注册", notes = "支持手机号、邮箱、微信注册")
    @PostMapping("/register")
    public Result<?> register(@Valid @RequestBody RegisterDTO registerDTO, HttpServletRequest request) {
        try {
            String ipAddress = IPUtils.getIpAddr(request);
            log.info("用户注册请求，类型：{}，IP：{}", registerDTO.getType(), ipAddress);
            
            return userRegisterService.register(registerDTO, ipAddress);
        } catch (Exception e) {
            log.error("用户注册异常：{}", e.getMessage(), e);
            return Result.error("注册失败，请稍后重试");
        }
    }

    /**
     * 检查用户名是否可用
     */
    @ApiOperation(value = "检查用户名", notes = "检查手机号或邮箱是否已被注册")
    @GetMapping("/checkUsername")
    public Result<?> checkUsername(
            @ApiParam(value = "用户名（手机号/邮箱）", required = true) @RequestParam String username,
            @ApiParam(value = "类型：phone-手机号，email-邮箱", required = true) @RequestParam String type) {
        try {
            if (oConvertUtils.isEmpty(username) || oConvertUtils.isEmpty(type)) {
                return Result.error("参数不能为空");
            }
            
            return userRegisterService.checkUsername(username, type);
        } catch (Exception e) {
            log.error("检查用户名异常：{}", e.getMessage(), e);
            return Result.error("检查失败，请稍后重试");
        }
    }

    /**
     * 验证邀请码
     */
    @ApiOperation(value = "验证邀请码", notes = "验证邀请码是否有效")
    @GetMapping("/validateInvite")
    public Result<?> validateInviteCode(
            @ApiParam(value = "邀请码", required = true) @RequestParam String code) {
        try {
            if (oConvertUtils.isEmpty(code)) {
                return Result.error("邀请码不能为空");
            }
            
            return userRegisterService.validateInviteCode(code);
        } catch (Exception e) {
            log.error("验证邀请码异常：{}", e.getMessage(), e);
            return Result.error("验证失败，请稍后重试");
        }
    }

    /**
     * 发送短信验证码
     */
    @ApiOperation(value = "发送短信验证码", notes = "发送手机号注册验证码")
    @PostMapping("/sendSmsCode")
    public Result<?> sendSmsCode(
            @ApiParam(value = "手机号", required = true) @RequestParam String phone,
            @ApiParam(value = "使用场景", required = true) @RequestParam(defaultValue = "register") String scene,
            HttpServletRequest request) {
        try {
            if (oConvertUtils.isEmpty(phone)) {
                return Result.error("手机号不能为空");
            }

            // 验证手机号格式
            if (!phone.matches("^1[3-9]\\d{9}$")) {
                return Result.error("手机号格式不正确");
            }

            String ipAddress = IPUtils.getIpAddr(request);
            VerifyCodeResult result = verifyCodeService.sendSmsCode(phone, scene, ipAddress);

            if (result.isSuccess()) {
                return Result.OK(result.getMessage());
            } else {
                return Result.error(result.getMessage());
            }
        } catch (Exception e) {
            log.error("发送短信验证码异常：{}", e.getMessage(), e);
            return Result.error("发送失败，请稍后重试");
        }
    }

    /**
     * 发送邮箱验证码
     */
    @ApiOperation(value = "发送邮箱验证码", notes = "发送邮箱注册验证码")
    @PostMapping("/sendEmailCode")
    public Result<?> sendEmailCode(
            @ApiParam(value = "邮箱", required = true) @RequestParam String email,
            @ApiParam(value = "使用场景", required = true) @RequestParam(defaultValue = "register") String scene,
            HttpServletRequest request) {
        try {
            if (oConvertUtils.isEmpty(email)) {
                return Result.error("邮箱不能为空");
            }

            // 验证邮箱格式
            if (!email.matches("^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$")) {
                return Result.error("邮箱格式不正确");
            }

            String ipAddress = IPUtils.getIpAddr(request);
            VerifyCodeResult result = verifyCodeService.sendEmailCode(email, scene, ipAddress);

            if (result.isSuccess()) {
                return Result.OK(result.getMessage());
            } else {
                return Result.error(result.getMessage());
            }
        } catch (Exception e) {
            log.error("发送邮箱验证码异常：{}", e.getMessage(), e);
            return Result.error("发送失败，请稍后重试");
        }
    }

    /**
     * 验证验证码
     */
    @ApiOperation(value = "验证验证码", notes = "验证短信或邮箱验证码")
    @PostMapping("/verifyCode")
    public Result<?> verifyCode(
            @ApiParam(value = "目标（手机号/邮箱）", required = true) @RequestParam String target,
            @ApiParam(value = "验证码", required = true) @RequestParam String code,
            @ApiParam(value = "验证码类型：sms-短信，email-邮箱", required = true) @RequestParam String codeType,
            @ApiParam(value = "使用场景", required = true) @RequestParam(defaultValue = "register") String scene) {
        try {
            if (oConvertUtils.isEmpty(target) || oConvertUtils.isEmpty(code) || oConvertUtils.isEmpty(codeType)) {
                return Result.error("参数不能为空");
            }

            boolean valid = verifyCodeService.verifyCode(target, code, codeType, scene);
            
            if (valid) {
                return Result.OK("验证码验证成功");
            } else {
                return Result.error("验证码错误或已过期");
            }
        } catch (Exception e) {
            log.error("验证验证码异常：{}", e.getMessage(), e);
            return Result.error("验证失败，请稍后重试");
        }
    }

    /**
     * 获取图形验证码
     */
    @ApiOperation(value = "获取图形验证码", notes = "获取图形验证码")
    @GetMapping("/captcha")
    public Result<?> getCaptcha() {
        try {
            // TODO: 实现图形验证码生成
            // 这里可以使用现有的验证码生成工具
            return Result.OK("图形验证码获取成功");
        } catch (Exception e) {
            log.error("获取图形验证码异常：{}", e.getMessage(), e);
            return Result.error("获取失败，请稍后重试");
        }
    }
}
