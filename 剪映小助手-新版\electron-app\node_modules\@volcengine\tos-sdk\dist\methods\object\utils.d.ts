/// <reference types="node" />
import { Headers, SupportObjectBody } from '../../interface';
import { CRCCls } from '../../universal/crc';
import { IRateLimiter } from '../../universal/rate-limiter';
import { RestoreInfo } from './sharedTypes';
export declare const getObjectInputKey: (input: string | {
    key: string;
}) => string;
export declare const DEFAULT_CONTENT_TYPE = "application/octet-stream";
export declare function lookupMimeType(key: string): string | undefined;
export declare function validateObjectName(input: {
    key: string;
} | string): void;
export declare function getSize(body: unknown, headers?: Headers): number | null;
interface GetNewBodyConfigIn<T> {
    body: T;
    dataTransferCallback: (n: number) => void;
    beforeRetry?: () => void;
    makeRetryStream?: () => NodeJS.ReadableStream | undefined;
    enableCRC: boolean;
    rateLimiter?: IRateLimiter;
}
interface GetNewBodyConfigOut<T> {
    body: T | NodeJS.ReadableStream;
    beforeRetry?: () => void;
    makeRetryStream?: () => NodeJS.ReadableStream | undefined;
    crc?: CRCCls;
}
interface GetEmitReadBodyConfigIn<T> {
    body: T;
    dataTransferCallback: (n: number) => void;
    makeRetryStream?: () => NodeJS.ReadableStream | undefined;
    rateLimiter?: IRateLimiter;
}
interface GetEmitReadBodyConfigOut<T> {
    body: T | NodeJS.ReadableStream;
    makeRetryStream?: () => NodeJS.ReadableStream | undefined;
}
export declare function getEmitReadBodyConfig<T extends SupportObjectBody>({ body, dataTransferCallback, makeRetryStream, rateLimiter, }: GetEmitReadBodyConfigIn<T>): GetEmitReadBodyConfigOut<T>;
export declare function getCRCBodyConfig<T extends SupportObjectBody>({ body, beforeRetry, makeRetryStream, enableCRC, }: GetNewBodyConfigIn<T>): Promise<GetNewBodyConfigOut<T>>;
export declare function getNewBodyConfig<T extends SupportObjectBody>(input: GetNewBodyConfigIn<T>): Promise<GetNewBodyConfigOut<T>>;
export declare function getCopySourceHeaderValue(srcBucket: string, srcKey: string): string;
export declare function isValidRateLimiter(rateLimiter?: IRateLimiter): boolean;
export declare function validateCheckpoint(cp: undefined | string | Object): void;
export declare const getRestoreInfoFromHeaders: (headers: Headers) => RestoreInfo | undefined;
export {};
