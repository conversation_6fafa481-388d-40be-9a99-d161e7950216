#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
插件作者插件数更新功能测试脚本
测试插件增删改时自动更新作者插件数的功能
"""

import requests
import json
import mysql.connector
import time

# 配置
BASE_URL = "http://localhost:8080"
DB_CONFIG = {
    'host': '127.0.0.1',
    'user': 'root',
    'password': 'root',
    'database': 'AigcView',
    'charset': 'utf8mb4'
}

def get_db_connection():
    """获取数据库连接"""
    try:
        conn = mysql.connector.connect(**DB_CONFIG)
        return conn
    except Exception as e:
        print(f"数据库连接失败: {e}")
        return None

def get_author_plugin_count(author_id):
    """获取作者的插件数（从数据库）"""
    conn = get_db_connection()
    if not conn:
        return None
    
    try:
        cursor = conn.cursor(dictionary=True)
        query = """
        SELECT 
            a.authorname, a.plubnum,
            (SELECT COUNT(*) FROM aigc_plub_shop p WHERE p.plubwrite = a.id) as actual_count
        FROM aigc_plub_author a 
        WHERE a.id = %s
        """
        cursor.execute(query, (author_id,))
        result = cursor.fetchone()
        cursor.close()
        conn.close()
        return result
    except Exception as e:
        print(f"查询数据库异常: {e}")
        if conn:
            conn.close()
        return None

def test_batch_update_plugin_counts():
    """测试批量更新所有作者插件数"""
    print("=== 测试1: 批量更新所有作者插件数 ===")
    
    url = f"{BASE_URL}/plubauthor/aigcPlubAuthor/updateAllPluginCounts"
    
    try:
        response = requests.post(url)
        result = response.json()
        
        print(f"状态码: {response.status_code}")
        print(f"响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
        
        if result.get("success"):
            print("✅ 批量更新插件数成功")
            return True
        else:
            print("❌ 批量更新插件数失败")
            return False
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

def test_single_author_update():
    """测试单个作者插件数更新"""
    print("\n=== 测试2: 单个作者插件数更新 ===")
    
    # 使用AigcView王的ID
    author_id = "1933808003163283458"
    
    # 获取更新前的数据
    print("更新前的数据:")
    before_data = get_author_plugin_count(author_id)
    if before_data:
        print(f"  作者: {before_data['authorname']}")
        print(f"  记录的插件数: {before_data['plubnum']}")
        print(f"  实际插件数: {before_data['actual_count']}")
    
    url = f"{BASE_URL}/plubauthor/aigcPlubAuthor/updatePluginCount"
    data = {"id": author_id}
    
    try:
        response = requests.post(url, params=data)
        result = response.json()
        
        print(f"API响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
        
        if result.get("success"):
            print("✅ 单个作者插件数更新成功")
            
            # 获取更新后的数据
            time.sleep(1)
            print("更新后的数据:")
            after_data = get_author_plugin_count(author_id)
            if after_data:
                print(f"  作者: {after_data['authorname']}")
                print(f"  记录的插件数: {after_data['plubnum']}")
                print(f"  实际插件数: {after_data['actual_count']}")
                
                if after_data['plubnum'] == after_data['actual_count']:
                    print("✅ 插件数已正确同步")
                else:
                    print("❌ 插件数同步失败")
            
            return True
        else:
            print("❌ 单个作者插件数更新失败")
            return False
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

def test_add_plugin_auto_update():
    """测试添加插件时自动更新作者插件数"""
    print("\n=== 测试3: 添加插件自动更新作者插件数 ===")
    
    author_id = "1933808003163283458"  # AigcView王
    
    # 获取添加前的数据
    print("添加插件前的数据:")
    before_data = get_author_plugin_count(author_id)
    if before_data:
        print(f"  作者: {before_data['authorname']}")
        print(f"  插件数: {before_data['plubnum']}")
    
    # 添加一个测试插件
    url = f"{BASE_URL}/plubshop/aigcPlubShop/add"
    plugin_data = {
        "plubname": "测试插件_" + str(int(time.time())),
        "plubwrite": author_id,
        "plubinfo": "这是一个测试插件",
        "neednum": 5,
        "income": 0,
        "usernum": 0
    }
    
    try:
        response = requests.post(url, json=plugin_data)
        result = response.json()
        
        print(f"添加插件API响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
        
        if result.get("success"):
            print("✅ 插件添加成功")
            
            # 获取添加后的数据
            time.sleep(1)
            print("添加插件后的数据:")
            after_data = get_author_plugin_count(author_id)
            if after_data:
                print(f"  作者: {after_data['authorname']}")
                print(f"  插件数: {after_data['plubnum']}")
                
                if after_data['plubnum'] == before_data['plubnum'] + 1:
                    print("✅ 作者插件数自动更新成功")
                else:
                    print("❌ 作者插件数未自动更新")
            
            return True
        else:
            print("❌ 插件添加失败")
            return False
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

def show_all_authors_status():
    """显示所有作者的插件数状态"""
    print("\n=== 所有作者插件数状态 ===")
    
    conn = get_db_connection()
    if not conn:
        return
    
    try:
        cursor = conn.cursor(dictionary=True)
        query = """
        SELECT 
            a.id, a.authorname, a.plubnum, a.plubusenum,
            (SELECT COUNT(*) FROM aigc_plub_shop p WHERE p.plubwrite = a.id) as actual_plugin_count
        FROM aigc_plub_author a
        ORDER BY a.authorname
        """
        cursor.execute(query)
        results = cursor.fetchall()
        
        print(f"{'作者名称':<15} {'记录插件数':<10} {'实际插件数':<10} {'使用总数':<10} {'状态':<10}")
        print("-" * 70)
        
        for row in results:
            status = "✅ 正确" if row['plubnum'] == row['actual_plugin_count'] else "❌ 不一致"
            print(f"{row['authorname']:<15} {row['plubnum']:<10} {row['actual_plugin_count']:<10} {row['plubusenum']:<10} {status:<10}")
        
        cursor.close()
        conn.close()
        
    except Exception as e:
        print(f"查询异常: {e}")
        if conn:
            conn.close()

def main():
    """主测试函数"""
    print("插件作者插件数更新功能测试")
    print("=" * 50)
    
    # 显示当前状态
    show_all_authors_status()
    
    tests = [
        test_batch_update_plugin_counts,
        test_single_author_update,
        test_add_plugin_auto_update
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        if test_func():
            passed += 1
    
    # 显示最终状态
    show_all_authors_status()
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！")
    else:
        print("⚠️  部分测试失败，请检查实现")

if __name__ == "__main__":
    main()
