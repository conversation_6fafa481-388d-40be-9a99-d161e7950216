local util = nil      ---@type Util

local AETools = AETools or {}     ---@class AETools
AETools.__index = AETools


function AETools:new(_util, ...)
    local self = setmetatable({}, AETools)
    util = _util
    self.key_frame_info = {}
    return self
end

function AETools:addKeyFrameInfo(in_val, out_val, frame, val)
    local key_frame_count = #self.key_frame_info
    if key_frame_count == 0 and frame > 0 then
        self.key_frame_info[key_frame_count + 1] = {
            ["v_in"] = in_val,
            ["v_out"] = out_val,
            ["cur_frame"] = 0,
            ["value"] = val
        }
    end

    key_frame_count = #self.key_frame_info
    self.key_frame_info[key_frame_count + 1] = {
        ["v_in"] = in_val,
        ["v_out"] = out_val,
        ["cur_frame"] = frame,
        ["value"] = val
    }
    -- Amaz.LOGI(tostring(#self.key_frame_info).." lrc add "..tostring(self.key_frame_info), tostring(val))
    self:_updateKeyFrameInfo()
end

function AETools:_updateKeyFrameInfo()
    if self.key_frame_info and #self.key_frame_info > 0 then
        self.finish_frame_time = self.key_frame_info[#self.key_frame_info]["cur_frame"]
    end
end

function AETools:getCurPartVal(_progress, hard_cut)
    
    local part_id, part_progress = self:_getCurPart(_progress)

    local frame1 = self.key_frame_info[part_id-1]
    local frame2 = self.key_frame_info[part_id]

    if hard_cut == true then
        return frame1["value"]
    end

    local info1 = frame1["v_out"]
    local info2 = frame2["v_in"]


    local average = (info1[1] + info2[1]) * 0.5 + 0.001
    local affect_val1 = info1[2]/100
    local affect_val2 = 1-info2[2]/100

    local bezier_val = {
        affect_val1,            
        math.abs(info1[1]/average * affect_val1),     
        affect_val2,            
        math.abs(1-info2[1]/average * affect_val2)
    }

    local progress = util.bezier(bezier_val)(part_progress, 0, 1, 1)

    if type(frame1["value"]) == "number" then
        return util.mix(frame1["value"], frame2["value"], progress)
    end

    local res = {}
    for i = 1, #frame1["value"] do
        res[i] = util.mix(frame1["value"][i], frame2["value"][i], progress)
    end
    return res

end

function AETools:_getCurPart(progress)
    if progress > 0.999 then
        return #self.key_frame_info, 1
    end

    for i = 1, #self.key_frame_info do
        local info = self.key_frame_info[i]
        if progress < info["cur_frame"]/self.finish_frame_time then
            return i, util.remap01(
                self.key_frame_info[i-1]["cur_frame"]/self.finish_frame_time,
                self.key_frame_info[i]["cur_frame"]/self.finish_frame_time,
                progress
            )
        end
    end
end

function AETools:clear()
    self.key_frame_info = {}
    self:_updateKeyFrameInfo()
end

function AETools:test()
    Amaz.LOGI("lrc "..tostring(self.key_frame_info), tostring(#self.key_frame_info))
end

return AETools