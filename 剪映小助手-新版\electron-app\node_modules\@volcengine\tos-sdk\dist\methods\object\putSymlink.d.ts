import TOSBase, { TosResponse } from '../base';
import { Acl } from '../../interface';
import { StorageClassType } from '../../TosExportEnum';
export interface PutSymInput {
    bucket?: string;
    key: string;
    symLinkTargetKey: string;
    symLinkTargetBucket?: string;
    forbidOverwrite?: boolean;
    acl?: Acl;
    meta?: Record<string, string>;
    storageClass?: StorageClassType;
    headers?: {
        'x-tos-symlink-target': string;
        'x-tos-symlink-bucket'?: string;
        'x-tos-forbid-overwrite'?: string;
        'x-tos-acl'?: Acl;
        'x-tos-storage-class'?: string;
        [key: string]: string | undefined;
    };
}
export interface PutSymOutput {
    VersionID?: string;
}
/**
 * @private unstable method
 */
export declare function putSymlink(this: TOSBase, input: PutSymInput): Promise<TosResponse<PutSymOutput>>;
export declare function _putSymlink(this: TOSBase, input: PutSymInput): Promise<TosResponse<PutSymOutput>>;
export default putSymlink;
