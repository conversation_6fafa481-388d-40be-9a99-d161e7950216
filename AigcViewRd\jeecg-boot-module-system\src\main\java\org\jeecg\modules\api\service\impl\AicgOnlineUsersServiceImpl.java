package org.jeecg.modules.api.service.impl;

import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.api.config.UserActivityConfig;
import org.jeecg.modules.api.entity.AicgOnlineUsers;
import org.jeecg.modules.api.mapper.AicgOnlineUsersMapper;
import org.jeecg.modules.api.service.IAicgOnlineUsersService;
import org.jeecg.modules.api.service.IUserActivityBatchUpdateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 在线用户统计服务实现
 * @Author: AigcView
 * @Date: 2025-01-30
 * @Version: V1.0
 */
@Slf4j
@Service
public class AicgOnlineUsersServiceImpl extends ServiceImpl<AicgOnlineUsersMapper, AicgOnlineUsers> implements IAicgOnlineUsersService {

    @Autowired
    private UserActivityConfig config;

    @Autowired
    private IUserActivityBatchUpdateService batchUpdateService;

    @Override
    public Map<String, Object> getOnlineUsersStats() {
        try {
            return baseMapper.getOnlineUsersStats();
        } catch (Exception e) {
            log.error("获取在线用户统计异常: {}", e.getMessage(), e);
            return new HashMap<>();
        }
    }

    @Override
    public boolean updateLastActiveTime(String userId) {
        try {
            int result = baseMapper.updateLastActiveTime(userId);
            if (result > 0) {
                log.debug("更新用户最后活跃时间成功: {}", userId);
                return true;
            } else {
                log.warn("更新用户最后活跃时间失败，用户可能不在线: {}", userId);
                return false;
            }
        } catch (Exception e) {
            log.error("更新用户最后活跃时间异常: {}, 错误: {}", userId, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean setUserOffline(String userId) {
        try {
            int result = baseMapper.setUserOffline(userId);
            if (result > 0) {
                log.info("设置用户离线成功: {}", userId);
                return true;
            } else {
                log.warn("设置用户离线失败，用户可能已经离线: {}", userId);
                return false;
            }
        } catch (Exception e) {
            log.error("设置用户离线异常: {}, 错误: {}", userId, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public int cleanOfflineUsers() {
        try {
            int result = baseMapper.cleanOfflineUsers();
            if (result > 0) {
                log.info("清理离线用户成功，清理数量: {}", result);
            }
            return result;
        } catch (Exception e) {
            log.error("清理离线用户异常: {}", e.getMessage(), e);
            return 0;
        }
    }

    @Override
    public int getTodayActiveUsersCount() {
        try {
            return baseMapper.getTodayActiveUsersCount();
        } catch (Exception e) {
            log.error("获取今日活跃用户数异常: {}", e.getMessage(), e);
            return 0;
        }
    }

    @Override
    public int getCurrentOnlineUsersCount() {
        try {
            return baseMapper.getCurrentOnlineUsersCount();
        } catch (Exception e) {
            log.error("获取当前在线用户数异常: {}", e.getMessage(), e);
            return 0;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean recordUserLogin(String userId, String sessionId, String ipAddress, String userAgent) {
        try {
            log.info("记录用户登录状态 - 用户ID: {}, 会话ID: {}", userId, sessionId);

            // 如果启用了批量更新服务，使用批量更新
            if (config.getEnabled() && batchUpdateService != null) {
                return batchUpdateService.addLoginUpdate(userId, sessionId, ipAddress, userAgent);
            }

            // 否则直接更新数据库
            return recordUserLoginDirect(userId, sessionId, ipAddress, userAgent);

        } catch (Exception e) {
            log.error("记录用户登录状态异常: {}, 错误: {}", userId, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 直接记录用户登录状态（不使用批量更新）
     */
    private boolean recordUserLoginDirect(String userId, String sessionId, String ipAddress, String userAgent) {
        // 先设置该用户的其他会话为离线
        baseMapper.setUserOffline(userId);

        // 创建新的在线记录
        AicgOnlineUsers onlineUser = new AicgOnlineUsers();
        onlineUser.setUserId(userId);
        onlineUser.setSessionId(sessionId);
        onlineUser.setLoginTime(new Date());
        onlineUser.setLastActiveTime(new Date());
        onlineUser.setIpAddress(ipAddress);
        onlineUser.setUserAgent(userAgent);
        onlineUser.setStatus(true);
        onlineUser.setCreateTime(new Date());
        onlineUser.setUpdateTime(new Date());

        int result = baseMapper.insert(onlineUser);
        return result > 0;
    }

    @Override
    public boolean recordUserLogout(String userId) {
        try {
            log.info("记录用户登出状态 - 用户ID: {}", userId);

            // 如果启用了批量更新服务，使用批量更新
            if (config.getEnabled() && batchUpdateService != null) {
                return batchUpdateService.addLogoutUpdate(userId, "");
            }

            // 否则直接更新数据库
            return setUserOffline(userId);

        } catch (Exception e) {
            log.error("记录用户登出状态异常: {}, 错误: {}", userId, e.getMessage(), e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchUpdateActiveTime(List<String> userIds) {
        if (userIds == null || userIds.isEmpty()) {
            return 0;
        }

        int successCount = 0;
        for (String userId : userIds) {
            try {
                if (updateLastActiveTime(userId)) {
                    successCount++;
                }
            } catch (Exception e) {
                log.error("批量更新用户活跃时间异常: {}, 错误: {}", userId, e.getMessage(), e);
            }
        }

        log.info("批量更新用户活跃时间完成 - 总数: {}, 成功: {}", userIds.size(), successCount);
        return successCount;
    }

    @Override
    public AicgOnlineUsers getUserOnlineStatus(String userId) {
        try {
            LambdaQueryWrapper<AicgOnlineUsers> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(AicgOnlineUsers::getUserId, userId)
                       .eq(AicgOnlineUsers::getStatus, true)
                       .orderByDesc(AicgOnlineUsers::getLastActiveTime)
                       .last("LIMIT 1");
            
            return baseMapper.selectOne(queryWrapper);
        } catch (Exception e) {
            log.error("获取用户在线状态异常: {}, 错误: {}", userId, e.getMessage(), e);
            return null;
        }
    }

    @Override
    public List<AicgOnlineUsers> getOnlineUsersList(int limit) {
        try {
            LambdaQueryWrapper<AicgOnlineUsers> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(AicgOnlineUsers::getStatus, true)
                       .orderByDesc(AicgOnlineUsers::getLastActiveTime)
                       .last("LIMIT " + Math.max(1, Math.min(limit, 1000))); // 限制在1-1000之间
            
            return baseMapper.selectList(queryWrapper);
        } catch (Exception e) {
            log.error("获取在线用户列表异常: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    @Override
    public Map<String, Object> getUserActivityStats(Date startDate, Date endDate) {
        Map<String, Object> stats = new HashMap<>();
        
        try {
            // 构建查询条件
            LambdaQueryWrapper<AicgOnlineUsers> queryWrapper = new LambdaQueryWrapper<>();
            if (startDate != null) {
                queryWrapper.ge(AicgOnlineUsers::getLastActiveTime, startDate);
            }
            if (endDate != null) {
                queryWrapper.le(AicgOnlineUsers::getLastActiveTime, endDate);
            }

            List<AicgOnlineUsers> users = baseMapper.selectList(queryWrapper);
            
            // 统计活跃用户数
            long activeUsers = users.stream()
                    .map(AicgOnlineUsers::getUserId)
                    .distinct()
                    .count();
            
            // 统计在线用户数
            long onlineUsers = users.stream()
                    .filter(user -> user.getStatus() != null && user.getStatus())
                    .map(AicgOnlineUsers::getUserId)
                    .distinct()
                    .count();

            stats.put("activeUsers", activeUsers);
            stats.put("onlineUsers", onlineUsers);
            stats.put("totalSessions", users.size());
            stats.put("startDate", startDate);
            stats.put("endDate", endDate);
            
        } catch (Exception e) {
            log.error("获取用户活跃度统计异常: {}", e.getMessage(), e);
        }
        
        return stats;
    }

    @Override
    public Map<String, Object> getRealTimeOnlineStats() {
        Map<String, Object> stats = new HashMap<>();
        
        try {
            // 当前在线用户数
            int currentOnline = getCurrentOnlineUsersCount();
            
            // 今日活跃用户数
            int todayActive = getTodayActiveUsersCount();
            
            // 最近15分钟活跃用户数
            LambdaQueryWrapper<AicgOnlineUsers> recentWrapper = new LambdaQueryWrapper<>();
            recentWrapper.ge(AicgOnlineUsers::getLastActiveTime, 
                    new Date(System.currentTimeMillis() - 15 * 60 * 1000));
            int recentActive = baseMapper.selectCount(recentWrapper).intValue();
            
            stats.put("currentOnline", currentOnline);
            stats.put("todayActive", todayActive);
            stats.put("recentActive", recentActive);
            stats.put("timestamp", new Date());
            
        } catch (Exception e) {
            log.error("获取实时在线统计异常: {}", e.getMessage(), e);
        }
        
        return stats;
    }

    @Override
    public long getUserOnlineDuration(String userId, Date date) {
        try {
            // 获取指定日期的用户在线记录
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(date);
            calendar.set(Calendar.HOUR_OF_DAY, 0);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);
            Date startOfDay = calendar.getTime();
            
            calendar.add(Calendar.DAY_OF_MONTH, 1);
            Date endOfDay = calendar.getTime();
            
            LambdaQueryWrapper<AicgOnlineUsers> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(AicgOnlineUsers::getUserId, userId)
                       .ge(AicgOnlineUsers::getLoginTime, startOfDay)
                       .lt(AicgOnlineUsers::getLoginTime, endOfDay)
                       .orderByAsc(AicgOnlineUsers::getLoginTime);
            
            List<AicgOnlineUsers> sessions = baseMapper.selectList(queryWrapper);
            
            long totalDuration = 0;
            for (AicgOnlineUsers session : sessions) {
                Date loginTime = session.getLoginTime();
                Date lastActiveTime = session.getLastActiveTime();
                
                if (loginTime != null && lastActiveTime != null) {
                    long duration = lastActiveTime.getTime() - loginTime.getTime();
                    totalDuration += Math.max(0, duration);
                }
            }
            
            // 返回分钟数
            return totalDuration / (1000 * 60);
            
        } catch (Exception e) {
            log.error("获取用户在线时长异常: {}, 错误: {}", userId, e.getMessage(), e);
            return 0;
        }
    }

    @Override
    public Map<String, Object> getPopularTimeStats(Date date) {
        Map<String, Object> stats = new HashMap<>();
        
        try {
            // 获取指定日期的所有在线记录
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(date);
            calendar.set(Calendar.HOUR_OF_DAY, 0);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);
            Date startOfDay = calendar.getTime();
            
            calendar.add(Calendar.DAY_OF_MONTH, 1);
            Date endOfDay = calendar.getTime();
            
            LambdaQueryWrapper<AicgOnlineUsers> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.ge(AicgOnlineUsers::getLastActiveTime, startOfDay)
                       .lt(AicgOnlineUsers::getLastActiveTime, endOfDay);
            
            List<AicgOnlineUsers> users = baseMapper.selectList(queryWrapper);
            
            // 按小时统计活跃用户数
            Map<Integer, Set<String>> hourlyUsers = new HashMap<>();
            for (int hour = 0; hour < 24; hour++) {
                hourlyUsers.put(hour, new HashSet<>());
            }
            
            for (AicgOnlineUsers user : users) {
                if (user.getLastActiveTime() != null) {
                    calendar.setTime(user.getLastActiveTime());
                    int hour = calendar.get(Calendar.HOUR_OF_DAY);
                    hourlyUsers.get(hour).add(user.getUserId());
                }
            }
            
            // 转换为统计数据
            Map<String, Integer> hourlyStats = new HashMap<>();
            for (Map.Entry<Integer, Set<String>> entry : hourlyUsers.entrySet()) {
                hourlyStats.put(String.format("%02d:00", entry.getKey()), entry.getValue().size());
            }
            
            stats.put("hourlyStats", hourlyStats);
            stats.put("date", date);
            
        } catch (Exception e) {
            log.error("获取热门时段统计异常: {}", e.getMessage(), e);
        }
        
        return stats;
    }

    @Override
    public boolean isUserOnline(String userId) {
        try {
            AicgOnlineUsers onlineStatus = getUserOnlineStatus(userId);
            if (onlineStatus == null) {
                return false;
            }
            
            // 检查最后活跃时间是否在阈值内
            long thresholdMillis = config.getUserOfflineThreshold() * 60 * 1000L;
            long timeSinceLastActive = System.currentTimeMillis() - onlineStatus.getLastActiveTime().getTime();
            
            return timeSinceLastActive <= thresholdMillis;
            
        } catch (Exception e) {
            log.error("检查用户是否在线异常: {}, 错误: {}", userId, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public Date getUserLastActiveTime(String userId) {
        try {
            AicgOnlineUsers onlineStatus = getUserOnlineStatus(userId);
            return onlineStatus != null ? onlineStatus.getLastActiveTime() : null;
        } catch (Exception e) {
            log.error("获取用户最后活跃时间异常: {}, 错误: {}", userId, e.getMessage(), e);
            return null;
        }
    }

    @Override
    public boolean forceUserOffline(String userId, String reason) {
        try {
            log.warn("强制用户下线 - 用户ID: {}, 原因: {}", userId, reason);
            return setUserOffline(userId);
        } catch (Exception e) {
            log.error("强制用户下线异常: {}, 错误: {}", userId, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public List<AicgOnlineUsers> getUserSessions(String userId) {
        try {
            LambdaQueryWrapper<AicgOnlineUsers> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(AicgOnlineUsers::getUserId, userId)
                       .orderByDesc(AicgOnlineUsers::getLastActiveTime)
                       .last("LIMIT 10"); // 最多返回10个会话
            
            return baseMapper.selectList(queryWrapper);
        } catch (Exception e) {
            log.error("获取用户会话信息异常: {}, 错误: {}", userId, e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    @Override
    public int cleanOfflineUsersBefore(Date beforeTime) {
        try {
            LambdaQueryWrapper<AicgOnlineUsers> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(AicgOnlineUsers::getStatus, false)
                       .lt(AicgOnlineUsers::getUpdateTime, beforeTime);
            
            int result = baseMapper.delete(queryWrapper);
            if (result > 0) {
                log.info("清理指定时间之前的离线用户记录成功，清理数量: {}", result);
            }
            return result;
        } catch (Exception e) {
            log.error("清理指定时间之前的离线用户记录异常: {}", e.getMessage(), e);
            return 0;
        }
    }

    @Override
    public boolean updateUserActivity(String userId, String sessionId, String apiPath, String ipAddress, String userAgent) {
        try {
            // 如果启用了批量更新服务，使用批量更新
            if (config.getEnabled() && batchUpdateService != null) {
                return batchUpdateService.addActivityUpdate(userId, sessionId, apiPath, ipAddress, userAgent);
            }

            // 否则直接更新数据库
            return updateLastActiveTime(userId);

        } catch (Exception e) {
            log.error("更新用户活跃状态异常: {}, 错误: {}", userId, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public Map<String, Object> getUserLocationStats() {
        Map<String, Object> stats = new HashMap<>();

        try {
            // 获取所有在线用户的IP地址统计
            LambdaQueryWrapper<AicgOnlineUsers> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(AicgOnlineUsers::getStatus, true)
                       .isNotNull(AicgOnlineUsers::getIpAddress);

            List<AicgOnlineUsers> onlineUsers = baseMapper.selectList(queryWrapper);

            // 按IP地址段统计（简单的地理位置分析）
            Map<String, Integer> locationStats = new HashMap<>();
            for (AicgOnlineUsers user : onlineUsers) {
                String ipAddress = user.getIpAddress();
                if (ipAddress != null && !ipAddress.isEmpty()) {
                    String location = getLocationFromIP(ipAddress);
                    locationStats.put(location, locationStats.getOrDefault(location, 0) + 1);
                }
            }

            stats.put("locationStats", locationStats);
            stats.put("totalOnlineUsers", onlineUsers.size());

        } catch (Exception e) {
            log.error("获取用户地理位置统计异常: {}", e.getMessage(), e);
        }

        return stats;
    }

    @Override
    public Map<String, Object> getUserDeviceStats() {
        Map<String, Object> stats = new HashMap<>();

        try {
            // 获取所有在线用户的设备信息统计
            LambdaQueryWrapper<AicgOnlineUsers> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(AicgOnlineUsers::getStatus, true)
                       .isNotNull(AicgOnlineUsers::getUserAgent);

            List<AicgOnlineUsers> onlineUsers = baseMapper.selectList(queryWrapper);

            // 按设备类型统计
            Map<String, Integer> deviceStats = new HashMap<>();
            Map<String, Integer> browserStats = new HashMap<>();
            Map<String, Integer> osStats = new HashMap<>();

            for (AicgOnlineUsers user : onlineUsers) {
                String userAgent = user.getUserAgent();
                if (userAgent != null && !userAgent.isEmpty()) {
                    String deviceType = getDeviceTypeFromUserAgent(userAgent);
                    String browser = getBrowserFromUserAgent(userAgent);
                    String os = getOSFromUserAgent(userAgent);

                    deviceStats.put(deviceType, deviceStats.getOrDefault(deviceType, 0) + 1);
                    browserStats.put(browser, browserStats.getOrDefault(browser, 0) + 1);
                    osStats.put(os, osStats.getOrDefault(os, 0) + 1);
                }
            }

            stats.put("deviceStats", deviceStats);
            stats.put("browserStats", browserStats);
            stats.put("osStats", osStats);
            stats.put("totalOnlineUsers", onlineUsers.size());

        } catch (Exception e) {
            log.error("获取用户设备统计异常: {}", e.getMessage(), e);
        }

        return stats;
    }

    @Override
    public List<Map<String, Object>> getUserActivityTrend(int days) {
        List<Map<String, Object>> trend = new ArrayList<>();

        try {
            Calendar calendar = Calendar.getInstance();

            for (int i = days - 1; i >= 0; i--) {
                calendar.setTime(new Date());
                calendar.add(Calendar.DAY_OF_MONTH, -i);
                calendar.set(Calendar.HOUR_OF_DAY, 0);
                calendar.set(Calendar.MINUTE, 0);
                calendar.set(Calendar.SECOND, 0);
                Date startOfDay = calendar.getTime();

                calendar.add(Calendar.DAY_OF_MONTH, 1);
                Date endOfDay = calendar.getTime();

                // 获取当天活跃用户数
                LambdaQueryWrapper<AicgOnlineUsers> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.ge(AicgOnlineUsers::getLastActiveTime, startOfDay)
                           .lt(AicgOnlineUsers::getLastActiveTime, endOfDay);

                List<AicgOnlineUsers> dayUsers = baseMapper.selectList(queryWrapper);
                long activeUsers = dayUsers.stream()
                        .map(AicgOnlineUsers::getUserId)
                        .distinct()
                        .count();

                Map<String, Object> dayData = new HashMap<>();
                dayData.put("date", startOfDay);
                dayData.put("activeUsers", activeUsers);
                dayData.put("totalSessions", dayUsers.size());

                trend.add(dayData);
            }

        } catch (Exception e) {
            log.error("获取用户活跃度趋势异常: {}", e.getMessage(), e);
        }

        return trend;
    }

    @Override
    public int getPeakOnlineUsers(Date date) {
        try {
            // 获取指定日期的峰值在线用户数
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(date);
            calendar.set(Calendar.HOUR_OF_DAY, 0);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);
            Date startOfDay = calendar.getTime();

            calendar.add(Calendar.DAY_OF_MONTH, 1);
            Date endOfDay = calendar.getTime();

            // 按小时统计在线用户数，找出峰值
            int peakUsers = 0;
            for (int hour = 0; hour < 24; hour++) {
                calendar.setTime(startOfDay);
                calendar.add(Calendar.HOUR_OF_DAY, hour);
                Date hourStart = calendar.getTime();

                calendar.add(Calendar.HOUR_OF_DAY, 1);
                Date hourEnd = calendar.getTime();

                LambdaQueryWrapper<AicgOnlineUsers> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(AicgOnlineUsers::getStatus, true)
                           .ge(AicgOnlineUsers::getLastActiveTime, hourStart)
                           .lt(AicgOnlineUsers::getLastActiveTime, hourEnd);

                int hourUsers = baseMapper.selectCount(queryWrapper).intValue();
                peakUsers = Math.max(peakUsers, hourUsers);
            }

            return peakUsers;

        } catch (Exception e) {
            log.error("获取峰值在线用户数异常: {}", e.getMessage(), e);
            return 0;
        }
    }

    @Override
    public Map<String, Object> getUserRetentionStats(Date startDate, Date endDate) {
        Map<String, Object> stats = new HashMap<>();

        try {
            // 获取时间范围内的所有用户
            LambdaQueryWrapper<AicgOnlineUsers> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.ge(AicgOnlineUsers::getLoginTime, startDate)
                       .le(AicgOnlineUsers::getLoginTime, endDate);

            List<AicgOnlineUsers> users = baseMapper.selectList(queryWrapper);

            // 按日期分组统计用户
            Map<String, Set<String>> dailyUsers = new HashMap<>();
            Calendar calendar = Calendar.getInstance();

            for (AicgOnlineUsers user : users) {
                calendar.setTime(user.getLoginTime());
                String dateKey = String.format("%04d-%02d-%02d",
                    calendar.get(Calendar.YEAR),
                    calendar.get(Calendar.MONTH) + 1,
                    calendar.get(Calendar.DAY_OF_MONTH));

                dailyUsers.computeIfAbsent(dateKey, k -> new HashSet<>()).add(user.getUserId());
            }

            // 计算留存率
            List<String> sortedDates = dailyUsers.keySet().stream().sorted().collect(Collectors.toList());
            Map<String, Double> retentionRates = new HashMap<>();

            for (int i = 0; i < sortedDates.size() - 1; i++) {
                String currentDate = sortedDates.get(i);
                String nextDate = sortedDates.get(i + 1);

                Set<String> currentUsers = dailyUsers.get(currentDate);
                Set<String> nextUsers = dailyUsers.get(nextDate);

                if (currentUsers != null && nextUsers != null && !currentUsers.isEmpty()) {
                    long retainedUsers = currentUsers.stream()
                            .filter(nextUsers::contains)
                            .count();

                    double retentionRate = (double) retainedUsers / currentUsers.size() * 100;
                    retentionRates.put(currentDate + " -> " + nextDate, retentionRate);
                }
            }

            stats.put("retentionRates", retentionRates);
            stats.put("dailyUsers", dailyUsers.entrySet().stream()
                    .collect(Collectors.toMap(
                            Map.Entry::getKey,
                            entry -> entry.getValue().size()
                    )));

        } catch (Exception e) {
            log.error("获取用户留存率统计异常: {}", e.getMessage(), e);
        }

        return stats;
    }

    @Override
    public Map<String, Object> getNewUserStats(Date date) {
        Map<String, Object> stats = new HashMap<>();

        try {
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(date);
            calendar.set(Calendar.HOUR_OF_DAY, 0);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);
            Date startOfDay = calendar.getTime();

            calendar.add(Calendar.DAY_OF_MONTH, 1);
            Date endOfDay = calendar.getTime();

            // 获取当天首次登录的用户
            LambdaQueryWrapper<AicgOnlineUsers> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.ge(AicgOnlineUsers::getLoginTime, startOfDay)
                       .lt(AicgOnlineUsers::getLoginTime, endOfDay);

            List<AicgOnlineUsers> dayUsers = baseMapper.selectList(queryWrapper);

            // 检查这些用户是否为新用户（之前没有登录记录）
            Set<String> newUsers = new HashSet<>();
            for (AicgOnlineUsers user : dayUsers) {
                LambdaQueryWrapper<AicgOnlineUsers> historyWrapper = new LambdaQueryWrapper<>();
                historyWrapper.eq(AicgOnlineUsers::getUserId, user.getUserId())
                             .lt(AicgOnlineUsers::getLoginTime, startOfDay);

                int historyCount = baseMapper.selectCount(historyWrapper).intValue();
                if (historyCount == 0) {
                    newUsers.add(user.getUserId());
                }
            }

            stats.put("newUserCount", newUsers.size());
            stats.put("totalUserCount", dayUsers.stream()
                    .map(AicgOnlineUsers::getUserId)
                    .collect(Collectors.toSet()).size());
            stats.put("date", date);

        } catch (Exception e) {
            log.error("获取新用户统计异常: {}", e.getMessage(), e);
        }

        return stats;
    }

    @Override
    public Map<String, Object> getUserActivityDistribution() {
        Map<String, Object> stats = new HashMap<>();

        try {
            // 获取所有用户的活跃度分布
            LambdaQueryWrapper<AicgOnlineUsers> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.isNotNull(AicgOnlineUsers::getLastActiveTime);

            List<AicgOnlineUsers> allUsers = baseMapper.selectList(queryWrapper);

            // 按活跃度分类
            Map<String, Integer> activityDistribution = new HashMap<>();
            activityDistribution.put("高活跃(今日)", 0);
            activityDistribution.put("中活跃(3天内)", 0);
            activityDistribution.put("低活跃(7天内)", 0);
            activityDistribution.put("不活跃(7天以上)", 0);

            long now = System.currentTimeMillis();
            long oneDayMillis = 24 * 60 * 60 * 1000L;

            for (AicgOnlineUsers user : allUsers) {
                if (user.getLastActiveTime() != null) {
                    long timeSinceActive = now - user.getLastActiveTime().getTime();

                    if (timeSinceActive <= oneDayMillis) {
                        activityDistribution.put("高活跃(今日)", activityDistribution.get("高活跃(今日)") + 1);
                    } else if (timeSinceActive <= 3 * oneDayMillis) {
                        activityDistribution.put("中活跃(3天内)", activityDistribution.get("中活跃(3天内)") + 1);
                    } else if (timeSinceActive <= 7 * oneDayMillis) {
                        activityDistribution.put("低活跃(7天内)", activityDistribution.get("低活跃(7天内)") + 1);
                    } else {
                        activityDistribution.put("不活跃(7天以上)", activityDistribution.get("不活跃(7天以上)") + 1);
                    }
                }
            }

            stats.put("activityDistribution", activityDistribution);
            stats.put("totalUsers", allUsers.size());

        } catch (Exception e) {
            log.error("获取用户活跃度分布异常: {}", e.getMessage(), e);
        }

        return stats;
    }

    @Override
    public int cleanZombieSessions() {
        try {
            // 清理超过配置阈值的僵尸会话
            long thresholdMillis = config.getUserOfflineThreshold() * 60 * 1000L;
            Date thresholdTime = new Date(System.currentTimeMillis() - thresholdMillis);

            LambdaQueryWrapper<AicgOnlineUsers> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(AicgOnlineUsers::getStatus, true)
                       .lt(AicgOnlineUsers::getLastActiveTime, thresholdTime);

            // 将这些会话设置为离线
            AicgOnlineUsers updateEntity = new AicgOnlineUsers();
            updateEntity.setStatus(false);
            updateEntity.setUpdateTime(new Date());

            int result = baseMapper.update(updateEntity, queryWrapper);
            if (result > 0) {
                log.info("清理僵尸会话成功，清理数量: {}", result);
            }
            return result;

        } catch (Exception e) {
            log.error("清理僵尸会话异常: {}", e.getMessage(), e);
            return 0;
        }
    }

    @Override
    public List<AicgOnlineUsers> getUserOnlineHistory(String userId, Date startDate, Date endDate) {
        try {
            LambdaQueryWrapper<AicgOnlineUsers> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(AicgOnlineUsers::getUserId, userId);

            if (startDate != null) {
                queryWrapper.ge(AicgOnlineUsers::getLoginTime, startDate);
            }
            if (endDate != null) {
                queryWrapper.le(AicgOnlineUsers::getLoginTime, endDate);
            }

            queryWrapper.orderByDesc(AicgOnlineUsers::getLoginTime);

            return baseMapper.selectList(queryWrapper);

        } catch (Exception e) {
            log.error("获取用户在线状态历史异常: {}, 错误: {}", userId, e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    @Override
    public Map<String, Object> getSystemLoadStats() {
        Map<String, Object> stats = new HashMap<>();

        try {
            // 当前在线用户数
            int currentOnline = getCurrentOnlineUsersCount();

            // 今日活跃用户数
            int todayActive = getTodayActiveUsersCount();

            // 系统负载评估
            String loadLevel = "正常";
            if (currentOnline > 1000) {
                loadLevel = "高负载";
            } else if (currentOnline > 500) {
                loadLevel = "中负载";
            }

            // 批量更新服务状态
            Map<String, Object> batchServiceStats = null;
            if (batchUpdateService != null) {
                batchServiceStats = batchUpdateService.getPerformanceStats();
            }

            stats.put("currentOnline", currentOnline);
            stats.put("todayActive", todayActive);
            stats.put("loadLevel", loadLevel);
            stats.put("batchServiceStats", batchServiceStats);
            stats.put("timestamp", new Date());

        } catch (Exception e) {
            log.error("获取系统负载统计异常: {}", e.getMessage(), e);
        }

        return stats;
    }

    /**
     * 从IP地址获取地理位置（简单实现）
     */
    private String getLocationFromIP(String ipAddress) {
        if (ipAddress == null || ipAddress.isEmpty()) {
            return "未知";
        }

        // 简单的IP地址分析
        if (ipAddress.startsWith("127.") || ipAddress.equals("localhost")) {
            return "本地";
        } else if (ipAddress.startsWith("192.168.") || ipAddress.startsWith("10.") || ipAddress.startsWith("172.")) {
            return "内网";
        } else if (ipAddress.startsWith("114.") || ipAddress.startsWith("223.")) {
            return "中国大陆";
        } else {
            return "海外";
        }
    }

    /**
     * 从User-Agent获取设备类型
     */
    private String getDeviceTypeFromUserAgent(String userAgent) {
        if (userAgent == null || userAgent.isEmpty()) {
            return "未知";
        }

        String ua = userAgent.toLowerCase();
        if (ua.contains("mobile") || ua.contains("android") || ua.contains("iphone")) {
            return "移动设备";
        } else if (ua.contains("tablet") || ua.contains("ipad")) {
            return "平板设备";
        } else {
            return "桌面设备";
        }
    }

    /**
     * 从User-Agent获取浏览器类型
     */
    private String getBrowserFromUserAgent(String userAgent) {
        if (userAgent == null || userAgent.isEmpty()) {
            return "未知";
        }

        String ua = userAgent.toLowerCase();
        if (ua.contains("chrome")) {
            return "Chrome";
        } else if (ua.contains("firefox")) {
            return "Firefox";
        } else if (ua.contains("safari")) {
            return "Safari";
        } else if (ua.contains("edge")) {
            return "Edge";
        } else if (ua.contains("opera")) {
            return "Opera";
        } else {
            return "其他";
        }
    }

    /**
     * 从User-Agent获取操作系统
     */
    private String getOSFromUserAgent(String userAgent) {
        if (userAgent == null || userAgent.isEmpty()) {
            return "未知";
        }

        String ua = userAgent.toLowerCase();
        if (ua.contains("windows")) {
            return "Windows";
        } else if (ua.contains("mac")) {
            return "macOS";
        } else if (ua.contains("linux")) {
            return "Linux";
        } else if (ua.contains("android")) {
            return "Android";
        } else if (ua.contains("ios") || ua.contains("iphone") || ua.contains("ipad")) {
            return "iOS";
        } else {
            return "其他";
        }
    }
}
