package org.jeecg.modules.demo.referral.service.impl;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import org.jeecg.modules.demo.referral.entity.AicgUserReferral;
import org.jeecg.modules.demo.referral.mapper.AicgUserReferralMapper;
import org.jeecg.modules.demo.referral.service.IAicgUserReferralService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: 用户推荐关系表
 * @Author: jeecg-boot
 * @Date:   2025-06-18
 * @Version: V1.0
 */
@Service
public class AicgUserReferralServiceImpl extends ServiceImpl<AicgUserReferralMapper, AicgUserReferral> implements IAicgUserReferralService {

    @Override
    public List<AicgUserReferral> getByReferrerId(String referrerId) {
        return baseMapper.getByReferrerId(referrerId);
    }

    @Override
    public AicgUserReferral getByRefereeId(String refereeId) {
        return baseMapper.getByRefereeId(refereeId);
    }

    @Override
    public AicgUserReferral getByReferralCode(String referralCode) {
        return baseMapper.getByReferralCode(referralCode);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AicgUserReferral createReferral(String referrerId, String refereeId, String referralCode, String operatorId) {
        // 检查是否已存在推荐关系
        AicgUserReferral existing = getByRefereeId(refereeId);
        if (existing != null) {
            throw new RuntimeException("该用户已存在推荐关系");
        }
        
        AicgUserReferral referral = new AicgUserReferral();
        referral.setReferrerId(referrerId);
        referral.setRefereeId(refereeId);
        referral.setReferralCode(referralCode);
        referral.setRegisterTime(new Date());
        referral.setStatus(1); // 待确认
        referral.setCreateBy(operatorId);
        referral.setCreateTime(new Date());
        
        this.save(referral);
        return referral;
    }

    @Override
    public String generateReferralCode(String userId) {
        // 生成8位推荐码：用户ID后6位 + 2位随机数
        String userIdSuffix = userId.length() >= 6 ? userId.substring(userId.length() - 6) : userId;
        String randomSuffix = String.format("%02d", (int)(Math.random() * 100));
        return userIdSuffix + randomSuffix;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean confirmReferral(String refereeId, BigDecimal rechargeAmount, String operatorId) {
        AicgUserReferral referral = getByRefereeId(refereeId);
        if (referral != null && referral.getStatus() == 1) {
            referral.setFirstRechargeTime(new Date());
            referral.setFirstRechargeAmount(rechargeAmount);
            referral.setStatus(2); // 已确认
            referral.setUpdateBy(operatorId);
            referral.setUpdateTime(new Date());
            return this.updateById(referral);
        }
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean markAsRewarded(String id, String operatorId) {
        AicgUserReferral referral = this.getById(id);
        if (referral != null && referral.getStatus() == 2) {
            referral.setStatus(3); // 已奖励
            referral.setUpdateBy(operatorId);
            referral.setUpdateTime(new Date());
            return this.updateById(referral);
        }
        return false;
    }

    @Override
    public Map<String, Object> getReferralStats(String referrerId) {
        return baseMapper.getReferralStats(referrerId);
    }

    @Override
    public List<AicgUserReferral> getPendingReferrals() {
        return baseMapper.getPendingReferrals();
    }

    @Override
    public List<AicgUserReferral> getConfirmedReferrals() {
        return baseMapper.getConfirmedReferrals();
    }

    @Override
    public boolean validateReferralCode(String referralCode) {
        AicgUserReferral referral = getByReferralCode(referralCode);
        return referral != null;
    }
}
