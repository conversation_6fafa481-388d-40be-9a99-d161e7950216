package org.jeecg.modules.jianying.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.jeecg.modules.jianying.dto.CreateDraftRequest;
import org.springframework.stereotype.Service;

import java.util.UUID;

/**
 * 草稿内容生成器 - 生成完整的竞争对手格式的剪映草稿JSON结构
 * 
 * <AUTHOR>
 * @date 2025-01-04
 */
@Service
public class DraftContentGenerator {

    /**
     * 生成完整的草稿内容JSON
     * 
     * @param request 创建草稿请求
     * @param draftId 草稿ID (UUID格式)
     * @return 完整的草稿JSON对象
     */
    public JSONObject generateDraftContent(CreateDraftRequest request, String draftId) {
        // 使用LinkedHashMap保证字段顺序
        JSONObject draft = new JSONObject(new java.util.LinkedHashMap<>());
        
        // 基础配置
        draft.put("canvas_config", generateCanvasConfig(request));
        draft.put("color_space", -1);
        draft.put("config", generateConfig());
        draft.put("cover", null);
        draft.put("create_time", 0);
        draft.put("duration", 10000000); // 10秒默认时长
        draft.put("extra_info", null);
        draft.put("fps", 30);
        draft.put("free_render_index_mode_on", false);
        draft.put("group_container", null);
        draft.put("id", draftId);
        draft.put("is_drop_frame_timecode", false);
        draft.put("keyframe_graph_list", new JSONArray());
        
        // 关键帧系统
        draft.put("keyframes", generateKeyframes());
        
        // 歌词效果
        draft.put("lyrics_effects", new JSONArray());
        
        // 素材系统
        draft.put("materials", generateMaterials());
        
        // 可变配置
        draft.put("mutable_config", null);
        draft.put("name", "");
        draft.put("new_version", "110.0.0");
        draft.put("path", "");
        draft.put("relationships", new JSONArray());
        draft.put("render_index_track_mode_on", true);
        draft.put("retouch_cover", null);
        draft.put("source", "default");
        draft.put("static_cover_image_path", "");
        draft.put("time_marks", null);
        
        // 轨道系统
        draft.put("tracks", generateTracks());
        
        draft.put("update_time", 0);
        draft.put("version", 360000);
        
        // 平台信息
        draft.put("platform", generatePlatformInfo());
        draft.put("last_modified_platform", generateLastModifiedPlatformInfo());
        
        return draft;
    }

    /**
     * 生成画布配置（匹配竞争对手字段顺序：height, ratio, width）
     */
    private JSONObject generateCanvasConfig(CreateDraftRequest request) {
        JSONObject canvasConfig = new JSONObject(new java.util.LinkedHashMap<>());
        canvasConfig.put("height", request.getZjHeight() != null ? request.getZjHeight() : 1024);
        canvasConfig.put("ratio", "original");
        canvasConfig.put("width", request.getZjWidth() != null ? request.getZjWidth() : 1024);
        return canvasConfig;
    }

    /**
     * 生成配置对象
     */
    private JSONObject generateConfig() {
        JSONObject config = new JSONObject(new java.util.LinkedHashMap<>());
        config.put("adjust_max_index", 1);
        config.put("attachment_info", new JSONArray());
        config.put("combination_max_index", 1);
        config.put("export_range", null);
        config.put("extract_audio_last_index", 1);
        config.put("lyrics_recognition_id", "");
        config.put("lyrics_sync", true);
        config.put("lyrics_taskinfo", new JSONArray());
        config.put("maintrack_adsorb", true);
        config.put("material_save_mode", 0);
        config.put("multi_language_current", "none");
        config.put("multi_language_list", new JSONArray());
        config.put("multi_language_main", "none");
        config.put("multi_language_mode", "none");
        config.put("original_sound_last_index", 1);
        config.put("record_audio_last_index", 1);
        config.put("sticker_max_index", 1);
        config.put("subtitle_keywords_config", null);
        config.put("subtitle_recognition_id", "");
        config.put("subtitle_sync", true);
        config.put("subtitle_taskinfo", new JSONArray());
        config.put("system_font_list", new JSONArray());
        config.put("video_mute", false);
        config.put("zoom_info_params", null);
        return config;
    }

    /**
     * 生成关键帧系统
     */
    private JSONObject generateKeyframes() {
        JSONObject keyframes = new JSONObject(new java.util.LinkedHashMap<>());
        keyframes.put("adjusts", new JSONArray());
        keyframes.put("audios", new JSONArray());
        keyframes.put("effects", new JSONArray());
        keyframes.put("filters", new JSONArray());
        keyframes.put("handwrites", new JSONArray());
        keyframes.put("stickers", new JSONArray());
        keyframes.put("texts", new JSONArray());
        keyframes.put("videos", new JSONArray());
        return keyframes;
    }

    /**
     * 生成素材系统 - 包含所有竞争对手的素材类型
     */
    private JSONObject generateMaterials() {
        // 使用LinkedHashMap保证字段顺序
        JSONObject materials = new JSONObject(new java.util.LinkedHashMap<>());
        
        // 按照竞争对手的顺序和类型添加所有素材数组
        materials.put("ai_translates", new JSONArray());
        materials.put("audio_balances", new JSONArray());
        materials.put("audio_effects", new JSONArray());
        materials.put("audio_fades", new JSONArray());
        materials.put("audio_track_indexes", new JSONArray());
        materials.put("audios", new JSONArray());
        materials.put("beats", new JSONArray());
        materials.put("canvases", new JSONArray());
        materials.put("chromas", new JSONArray());
        materials.put("color_curves", new JSONArray());
        materials.put("masks", new JSONArray());
        materials.put("digital_humans", new JSONArray());
        materials.put("drafts", new JSONArray());
        materials.put("effects", new JSONArray());
        materials.put("flowers", new JSONArray());
        materials.put("green_screens", new JSONArray());
        materials.put("handwrites", new JSONArray());
        materials.put("hsl", new JSONArray());
        materials.put("images", new JSONArray());
        materials.put("log_color_wheels", new JSONArray());
        materials.put("loudnesses", new JSONArray());
        materials.put("manual_deformations", new JSONArray());
        materials.put("material_animations", new JSONArray());
        materials.put("material_colors", new JSONArray());
        materials.put("multi_language_refs", new JSONArray());
        materials.put("placeholder_infos", new JSONArray());
        materials.put("placeholders", new JSONArray());
        materials.put("plugin_effects", new JSONArray());
        materials.put("primary_color_wheels", new JSONArray());
        materials.put("realtime_denoises", new JSONArray());
        materials.put("shapes", new JSONArray());
        materials.put("smart_crops", new JSONArray());
        materials.put("smart_relights", new JSONArray());
        materials.put("sound_channel_mappings", new JSONArray());
        materials.put("speeds", new JSONArray());
        materials.put("stickers", new JSONArray());
        materials.put("tail_leaders", new JSONArray());
        materials.put("text_templates", new JSONArray());
        materials.put("texts", new JSONArray());
        materials.put("time_marks", new JSONArray());
        materials.put("transitions", new JSONArray());
        materials.put("video_effects", new JSONArray());
        materials.put("video_trackings", new JSONArray());
        materials.put("videos", new JSONArray());
        materials.put("vocal_beautifys", new JSONArray());
        materials.put("vocal_separations", new JSONArray());
        
        return materials;
    }

    /**
     * 生成轨道系统（与竞争对手一致：只有1个video轨道）
     */
    private JSONArray generateTracks() {
        JSONArray tracks = new JSONArray();

        // 只创建1个主视频轨道 (flag=0) - 与竞争对手完全一致
        JSONObject videoTrack = new JSONObject(new java.util.LinkedHashMap<>());
        videoTrack.put("attribute", 0);
        videoTrack.put("flag", 0);
        videoTrack.put("id", UUID.randomUUID().toString().toUpperCase());
        videoTrack.put("segments", new JSONArray());
        videoTrack.put("type", "video");
        tracks.add(videoTrack);

        return tracks;
    }

    /**
     * 生成平台信息（匹配竞争对手字段顺序）
     */
    private JSONObject generatePlatformInfo() {
        JSONObject platform = new JSONObject(new java.util.LinkedHashMap<>());
        platform.put("app_id", 3704);
        platform.put("app_source", "lv");  // platform 保持 "lv"
        platform.put("app_version", "5.9.0");
        platform.put("device_id", "9d624d7e23dc4e43a0ed4020b8e3e90e");
        platform.put("hard_disk_id", "f242a67014ec01ecdc2a9280da37adb1");
        platform.put("mac_address", "0c233adaa327abd93e3d6b5dae6d75e2");
        platform.put("os", "mac");
        platform.put("os_version", "14.5");
        return platform;
    }

    /**
     * 生成最后修改平台信息（使用 "cc" 标识）
     */
    private JSONObject generateLastModifiedPlatformInfo() {
        JSONObject platform = new JSONObject(new java.util.LinkedHashMap<>());
        platform.put("app_id", 3704);
        platform.put("app_source", "cc");  // last_modified_platform 使用 "cc"
        platform.put("app_version", "5.9.0");
        platform.put("device_id", "9d624d7e23dc4e43a0ed4020b8e3e90e");
        platform.put("hard_disk_id", "f242a67014ec01ecdc2a9280da37adb1");
        platform.put("mac_address", "0c233adaa327abd93e3d6b5dae6d75e2");
        platform.put("os", "mac");
        platform.put("os_version", "14.5");
        return platform;
    }
}
