"use strict";function e(e){return e&&"object"==typeof e&&"default"in e?e.default:e}Object.defineProperty(exports,"__esModule",{value:!0});var t,a=e(require("axios")),o=e(require("lodash/get")),n=e(require("lodash/set")),i=e(require("qs")),s=e(require("debug")),r=e(require("crypto-js/hmac-sha256")),c=e(require("crypto-js/sha256")),p=e(require("crypto-js/md5")),l=e(require("crypto-js/enc-base64")),u=e(require("crypto-js/enc-hex")),d=e(require("crypto-js/enc-utf8")),h=e(require("lodash/cloneDeep")),m=e(require("axios/lib/utils")),f=e(require("axios/lib/core/settle")),y=e(require("axios/lib/helpers/buildURL")),g=e(require("axios/lib/core/buildFullPath")),x=e(require("axios/lib/core/createError")),b=e(require("axios-adapter-uniapp"));class k extends Error{constructor(e){const{data:t}=e;super(t.Message),this.code=void 0,this.data=void 0,this.statusCode=void 0,this.headers=void 0,this.requestId=void 0,this.id2=void 0,Object.setPrototypeOf(this,k.prototype),this.data=t,this.code=t.Code,this.statusCode=e.status,this.headers=e.headers,this.requestId=e.headers["x-tos-request-id"],this.id2=e.headers["x-tos-id-2"]}}(t=exports.TosServerCode||(exports.TosServerCode={})).NoSuchBucket="NoSuchBucket",t.NoSuchKey="NoSuchKey",t.AccessDenied="AccessDenied",t.MalformedAcl="MalformedAclError",t.UnexpectedContent="UnexpectedContent",t.InvalidRequest="InvalidRequest",t.MissingSecurityHeader="MissingSecurityHeader",t.InvalidArgument="InvalidArgument",t.EntityTooSmall="EntityTooSmall",t.InvalidBucketName="InvalidBucketName",t.BucketNotEmpty="BucketNotEmpty",t.TooManyBuckets="TooManyBuckets",t.BucketAlreadyExists="BucketAlreadyExists",t.MalformedBody="MalformedBody",t.NoSuchLifecycleConfiguration="NoSuchLifecycleConfiguration",t.ReplicationConfigurationNotFound="ReplicationConfigurationNotFoundError",t.InvalidLocationConstraint="InvalidLocationConstraint",t.AuthorizationQueryParametersError="AuthorizationQueryParametersError",t.RequestTimeTooSkewed="RequestTimeTooSkewed",t.SignatureDoesNotMatch="SignatureDoesNotMatch",t.RequestedRangeNotSatisfiable="Requested Range Not Satisfiable",t.PreconditionFailed="PreconditionFailed",t.BadDigest="BadDigest",t.InvalidDigest="InvalidDigest",t.EntityTooLarge="EntityTooLarge",t.UnImplemented="UnImplemented",t.MethodNotAllowed="MethodNotAllowed",t.InvalidAccessKeyId="InvalidAccessKeyId",t.InvalidSecurityToken="InvalidSecurityToken",t.ContentSHA256Mismatch="ContentSHA256Mismatch",t.ExceedQPSLimit="ExceedQPSLimit",t.ExceedRateLimit="ExceedRateLimit",t.NoSuchCORSConfiguration="NoSuchCORSConfiguration",t.NoSuchMirrorConfiguration="NoSuchMirrorConfiguration",t.NoSuchWebsiteConfiguration="NoSuchWebsiteConfiguration",t.MissingRequestBody="MissingRequestBodyError",t.BucketAlreadyOwnedByYou="BucketAlreadyOwnedByYou",t.NoSuchBucketPolicy="NoSuchBucketPolicy",t.PolicyTooLarge="PolicyTooLarge",t.MalformedPolicy="MalformedPolicy",t.InvalidKey="InvalidKey",t.MirrorFailed="MirrorFailed",t.Timeout="Timeout",t.OffsetNotMatched="OffsetNotMatched",t.NotAppendable="NotAppendable",t.ContextCanceled="ContextCanceled",t.InternalError="InternalError",t.TooManyRequests="TooManyRequests",t.TimeOut="TimeOut",t.ConcurrencyUpdateObjectLimit="ConcurrencyUpdateObjectLimit",t.DuplicateUpload="DuplicateUpload",t.DuplicateObject="DuplicateObject",t.InvalidVersionId="InvalidVersionId",t.StorageClassNotMatch="StorageClassNotMatch",t.UploadStatusNotUploading="UploadStatusNotUploading",t.PartSizeNotMatch="PartSizeNotMatch",t.NoUploadPart="NoUploadPart",t.PartsLenInvalid="PartsLenInvalid",t.PartsIdxSmall="PartsIdxSmall",t.PartSizeSmall="PartSizeSmall",t.PrefixNotNextKeyPrefix="PrefixNotNextKeyPrefix",t.InvalidPart="InvalidPart",t.InvalidPartOffset="InvalidPartOffset",t.MismatchObject="MismatchObject",t.UploadStatusMismatch="UploadStatusMismatch",t.CompletingStatusNoExpiration="CompletingStatusNoExpiration",t.Found="Found",t.InvalidRedirectLocation="InvalidRedirectLocation";class v extends Error{constructor(e){super(e),Object.setPrototypeOf(this,v.prototype)}}class T extends Error{constructor(e){super(e),Object.setPrototypeOf(this,T.prototype)}}const S=e=>t=>{if(null==e||"object"!=typeof e)return;const a=o(e,t);Array.isArray(a)||n(e,t,null==a?[]:[a])},w=e=>{const t=a=>Array.isArray(a)?a.map(e=>t(e)):"string"==typeof a?e(a):"object"==typeof a&&null!=a?Object.keys(a).reduce((e,o)=>(e[t(o)]=a[o],e),{}):a;return t},C=w(e=>e.replace(/[A-Z]/g,"-$&").toLowerCase()),E=w(e=>e[0].toUpperCase()+e.slice(1)),R=e=>{const t=[];return Object.keys(e).sort().forEach(a=>{t.push(`${encodeURIComponent(a)}=${encodeURIComponent(e[a])}`)}),t.join("&")},B=e=>{const t=e||{},a={};Object.keys(t).forEach(e=>{null!=t[e]&&(a[e]=t[e])});const o={};return Object.keys(a).forEach(e=>{const t=e.toLowerCase();o[t]=a[e]}),o},I=e=>{var t;return"string"==typeof e&&(e={url:e}),e&&null==(null==(t=e)?void 0:t.needProxyParams)&&(e.needProxyParams=!0),e};async function P(e){try{return[null,await e]}catch(e){return[e,null]}}function O(e){return"undefined"!=typeof Blob&&e instanceof Blob}function M(e){return"undefined"!=typeof Buffer&&e instanceof Buffer}function D(e){return e?Object.keys(e).map(t=>{const a=""+e[t];return`${encodeURIComponent(t)}=${encodeURIComponent(a)}`}).join("&"):""}function j(e){return e instanceof T}const A=e=>"string"==typeof e?e:e.toUTCString(),U={projectName:"x-tos-project-name",encodingType:"encoding-type",cacheControl:"cache-control",contentDisposition:"content-disposition",contentLength:"content-length",contentMD5:"content-md5",contentSHA256:"x-tos-content-sha256",contentEncoding:"content-encoding",contentLanguage:"content-language",contentType:"content-type",expires:["expires",e=>e.toUTCString()],range:"range",ifMatch:"if-match",ifModifiedSince:["if-modified-since",A],ifNoneMatch:"if-none-match",ifUnmodifiedSince:["if-unmodified-since",A],acl:"x-tos-acl",grantFullControl:"x-tos-grant-full-control",grantRead:"x-tos-grant-read",grantReadAcp:"x-tos-grant-read-acp",grantWrite:"x-tos-grant-write",grantWriteAcp:"x-tos-grant-write-acp",serverSideEncryption:"x-tos-server-side-encryption",serverSideDataEncryption:"x-tos-server-side-data-encryption",ssecAlgorithm:"x-tos-server-side-encryption-customer-algorithm",ssecKey:"x-tos-server-side-encryption-customer-key",ssecKeyMD5:"x-tos-server-side-encryption-customer-key-md5",copySourceRange:"x-tos-copy-source-range",copySourceIfMatch:"x-tos-copy-source-if-match",copySourceIfModifiedSince:["x-tos-copy-source-if-modified-since",A],copySourceIfNoneMatch:"x-tos-copy-source-if-none-match",copySourceIfUnmodifiedSince:"x-tos-copy-source-if-unmodified-since",copySourceSSECAlgorithm:"x-tos-copy-source-server-side-encryption-customer-algorithm",copySourceSSECKey:"x-tos-copy-source-server-side-encryption-customer-key",copySourceSSECKeyMD5:"x-tos-copy-source-server-side-encryption-customer-key-MD5",metadataDirective:"x-tos-metadata-directive",meta:e=>Object.keys(e).reduce((t,a)=>(t["x-tos-meta-"+a]=""+e[a],t),{}),websiteRedirectLocation:"x-tos-website-redirect-location",storageClass:"x-tos-storage-class",azRedundancy:"x-tos-az-redundancy",trafficLimit:"x-tos-traffic-limit",callback:"x-tos-callback",callbackVar:"x-tos-callback-var",allowSameActionOverlap:["x-tos-allow-same-action-overlap",e=>String(e)],symLinkTargetKey:"x-tos-symlink-target",symLinkTargetBucket:"x-tos-symlink-bucket",forbidOverwrite:"x-tos-forbid-overwrite",bucketType:"x-tos-bucket-type",recursiveMkdir:"x-tos-recursive-mkdir"},L={versionId:"versionId",process:"x-tos-process",saveBucket:"x-tos-save-bucket",saveObject:"x-tos-save-object",responseCacheControl:"response-cache-control",responseContentDisposition:"response-content-disposition",responseContentEncoding:"response-content-encoding",responseContentLanguage:"response-content-language",responseContentType:"response-content-type",responseExpires:["response-expires",e=>e.toUTCString()]};function _(e,t){if(!t.length)return;const a=e.headers||{};function o(e,t){null==a[e]&&(a[e]=t)}e.headers=a,t.forEach(t=>{const a=U[t];if(!a)throw new v(`\`${t}\` isn't in keys of \`requestHeadersMap\``);const n=e[t];if(null==n)return;if("string"==typeof a)return o(a,""+n);if(Array.isArray(a))return o(a[0],a[1](n));const i=a(n);Object.entries(i).forEach(([e,t])=>{o(e,t)})})}const N=e=>i.stringify(e);function z(e,t){return{data:e,statusCode:t.statusCode,headers:t.headers,requestId:t.requestId,id2:t.id2}}function q(e,t){const a=t["x-tos-hash-crc64ecma"];if(null==a)return void console.warn("No x-tos-hash-crc64ecma in response's headers, please see https://www.volcengine.com/docs/6349/127737 to add `x-tos-hash-crc64ecma` to Expose-Headers field.");const o="string"==typeof e?e:e.getCrc64();if(o!==a)throw new v(`validate file crc64 failed. Expect crc64 ${a}, actual crc64 ${o}. Please try again.`)}var K;!function(e){e.LastModified="last-modified",e.ContentLength="content-length",e.AcceptEncoding="accept-encoding",e.ContentEncoding="content-encoding",e.ContentMD5="content-md5",e.TosRawContentLength="x-tos-raw-content-length",e.TosTrailer="x-tos-trailer",e.TosHashCrc64ecma="x-tos-hash-crc64ecma",e.TosContentSha256="x-tos-content-sha256",e.TosDecodedContentLength="x-tos-decoded-content-length",e.TosEc="x-tos-ec",e.TosRequestId="x-tos-request-id"}(K||(K={}));const F=e=>{let t=Promise.resolve();return async()=>(t=t.then(()=>e()),t)},H=(e,t)=>{e&&"destroy"in e&&"function"==typeof e.destroy&&"destroyed"in e&&!e.destroyed&&e.destroy(t)};async function G(e){e=this.normalizeObjectInput(e);const t=B(e.headers);return e.headers=t,_(e,["encodingType","cacheControl","contentDisposition","contentEncoding","contentLanguage","contentType","expires","acl","grantFullControl","grantRead","grantReadAcp","grantWriteAcp","ssecAlgorithm","ssecKey","ssecKeyMD5","serverSideEncryption","serverSideDataEncryption","meta","websiteRedirectLocation","storageClass","forbidOverwrite"]),this.setObjectContentTypeHeader(e,t),this._fetchObject(e,"POST",{uploads:""},t,"")}const $=(e,t,a=!1)=>{let o=t;t<5242880&&(o=5242880,a&&console.warn(`partSize has been set to ${o}, because the partSize you provided is less than the minimal size of multipart`));const n=Math.ceil(e/1e4);return t<n&&(o=n,a&&console.warn(`partSize has been set to ${o}, because the partSize you provided causes the number of part excesses 10,000`)),o};async function V(e){const{uploadId:t,...a}=e,o=await this._fetchObject(e,"GET",{uploadId:t,...C(a)},{});return S(o.data)("Parts"),o}const W={"3gp":"video/3gpp","7z":"application/x-7z-compressed",abw:"application/x-abiword",ai:"application/postscript",aif:"audio/x-aiff",aifc:"audio/x-aiff",aiff:"audio/x-aiff",alc:"chemical/x-alchemy",amr:"audio/amr",anx:"application/annodex",apk:"application/vnd.android.package-archive",appcache:"text/cache-manifest",art:"image/x-jg",asc:"text/plain",asf:"video/x-ms-asf",aso:"chemical/x-ncbi-asn1-binary",asx:"video/x-ms-asf",atom:"application/atom+xml",atomcat:"application/atomcat+xml",atomsrv:"application/atomserv+xml",au:"audio/basic",avi:"video/x-msvideo",awb:"audio/amr-wb",axa:"audio/annodex",axv:"video/annodex",b:"chemical/x-molconn-Z",bak:"application/x-trash",bat:"application/x-msdos-program",bcpio:"application/x-bcpio",bib:"text/x-bibtex",bin:"application/octet-stream",bmp:"image/x-ms-bmp",boo:"text/x-boo",book:"application/x-maker",brf:"text/plain",bsd:"chemical/x-crossfire",c:"text/x-csrc","c++":"text/x-c++src",c3d:"chemical/x-chem3d",cab:"application/x-cab",cac:"chemical/x-cache",cache:"chemical/x-cache",cap:"application/vnd.tcpdump.pcap",cascii:"chemical/x-cactvs-binary",cat:"application/vnd.ms-pki.seccat",cbin:"chemical/x-cactvs-binary",cbr:"application/x-cbr",cbz:"application/x-cbz",cc:"text/x-c++src",cda:"application/x-cdf",cdf:"application/x-cdf",cdr:"image/x-coreldraw",cdt:"image/x-coreldrawtemplate",cdx:"chemical/x-cdx",cdy:"application/vnd.cinderella",cef:"chemical/x-cxf",cer:"chemical/x-cerius",chm:"chemical/x-chemdraw",chrt:"application/x-kchart",cif:"chemical/x-cif",class:"application/java-vm",cls:"text/x-tex",cmdf:"chemical/x-cmdf",cml:"chemical/x-cml",cod:"application/vnd.rim.cod",com:"application/x-msdos-program",cpa:"chemical/x-compass",cpio:"application/x-cpio",cpp:"text/x-c++src",cpt:"application/mac-compactpro",cr2:"image/x-canon-cr2",crl:"application/x-pkcs7-crl",crt:"application/x-x509-ca-cert",crw:"image/x-canon-crw",csd:"audio/csound",csf:"chemical/x-cache-csf",csh:"application/x-csh",csm:"chemical/x-csml",csml:"chemical/x-csml",css:"text/css",csv:"text/csv",ctab:"chemical/x-cactvs-binary",ctx:"chemical/x-ctx",cu:"application/cu-seeme",cub:"chemical/x-gaussian-cube",cxf:"chemical/x-cxf",cxx:"text/x-c++src",d:"text/x-dsrc",davmount:"application/davmount+xml",dcm:"application/dicom",dcr:"application/x-director",ddeb:"application/vnd.debian.binary-package",dif:"video/dv",diff:"text/x-diff",dir:"application/x-director",djv:"image/vnd.djvu",djvu:"image/vnd.djvu",dl:"video/dl",dll:"application/x-msdos-program",dmg:"application/x-apple-diskimage",dms:"application/x-dms",doc:"application/msword",docm:"application/vnd.ms-word.document.macroEnabled.12",docx:"application/vnd.openxmlformats-officedocument.wordprocessingml.document",dot:"application/msword",dotm:"application/vnd.ms-word.template.macroEnabled.12",dotx:"application/vnd.openxmlformats-officedocument.wordprocessingml.template",dv:"video/dv",dvi:"application/x-dvi",dx:"chemical/x-jcamp-dx",dxr:"application/x-director",emb:"chemical/x-embl-dl-nucleotide",embl:"chemical/x-embl-dl-nucleotide",eml:"message/rfc822",eot:"application/vnd.ms-fontobject",eps:"application/postscript",eps2:"application/postscript",eps3:"application/postscript",epsf:"application/postscript",epsi:"application/postscript",erf:"image/x-epson-erf",es:"application/ecmascript",etx:"text/x-setext",exe:"application/x-msdos-program",ez:"application/andrew-inset",fb:"application/x-maker",fbdoc:"application/x-maker",fch:"chemical/x-gaussian-checkpoint",fchk:"chemical/x-gaussian-checkpoint",fig:"application/x-xfig",flac:"audio/flac",fli:"video/fli",flv:"video/x-flv",fm:"application/x-maker",frame:"application/x-maker",frm:"application/x-maker",gal:"chemical/x-gaussian-log",gam:"chemical/x-gamess-input",gamin:"chemical/x-gamess-input",gan:"application/x-ganttproject",gau:"chemical/x-gaussian-input",gcd:"text/x-pcs-gcd",gcf:"application/x-graphing-calculator",gcg:"chemical/x-gcg8-sequence",gen:"chemical/x-genbank",gf:"application/x-tex-gf",gif:"image/gif",gjc:"chemical/x-gaussian-input",gjf:"chemical/x-gaussian-input",gl:"video/gl",gnumeric:"application/x-gnumeric",gpt:"chemical/x-mopac-graph",gsf:"application/x-font",gsm:"audio/x-gsm",gtar:"application/x-gtar",gz:"application/gzip",h:"text/x-chdr","h++":"text/x-c++hdr",hdf:"application/x-hdf",hh:"text/x-c++hdr",hin:"chemical/x-hin",hpp:"text/x-c++hdr",hqx:"application/mac-binhex40",hs:"text/x-haskell",hta:"application/hta",htc:"text/x-component",htm:"text/html",html:"text/html",hwp:"application/x-hwp",hxx:"text/x-c++hdr",ica:"application/x-ica",ice:"x-conference/x-cooltalk",ico:"image/vnd.microsoft.icon",ics:"text/calendar",icz:"text/calendar",ief:"image/ief",iges:"model/iges",igs:"model/iges",iii:"application/x-iphone",info:"application/x-info",inp:"chemical/x-gamess-input",ins:"application/x-internet-signup",iso:"application/x-iso9660-image",isp:"application/x-internet-signup",ist:"chemical/x-isostar",istr:"chemical/x-isostar",jad:"text/vnd.sun.j2me.app-descriptor",jam:"application/x-jam",jar:"application/java-archive",java:"text/x-java",jdx:"chemical/x-jcamp-dx",jmz:"application/x-jmol",jng:"image/x-jng",jnlp:"application/x-java-jnlp-file",jp2:"image/jp2",jpe:"image/jpeg",jpeg:"image/jpeg",jpf:"image/jpx",jpg:"image/jpeg",jpg2:"image/jp2",jpm:"image/jpm",jpx:"image/jpx",js:"application/javascript",json:"application/json",kar:"audio/midi",key:"application/pgp-keys",kil:"application/x-killustrator",kin:"chemical/x-kinemage",kml:"application/vnd.google-earth.kml+xml",kmz:"application/vnd.google-earth.kmz",kpr:"application/x-kpresenter",kpt:"application/x-kpresenter",ksp:"application/x-kspread",kwd:"application/x-kword",kwt:"application/x-kword",latex:"application/x-latex",lha:"application/x-lha",lhs:"text/x-literate-haskell",lin:"application/bbolin",lsf:"video/x-la-asf",lsx:"video/x-la-asf",ltx:"text/x-tex",ly:"text/x-lilypond",lyx:"application/x-lyx",lzh:"application/x-lzh",lzx:"application/x-lzx",m3g:"application/m3g",m3u:"audio/x-mpegurl",m3u8:"application/x-mpegURL",m4a:"audio/mpeg",maker:"application/x-maker",man:"application/x-troff-man",mbox:"application/mbox",mcif:"chemical/x-mmcif",mcm:"chemical/x-macmolecule",mdb:"application/msaccess",me:"application/x-troff-me",mesh:"model/mesh",mid:"audio/midi",midi:"audio/midi",mif:"application/x-mif",mkv:"video/x-matroska",mm:"application/x-freemind",mmd:"chemical/x-macromodel-input",mmf:"application/vnd.smaf",mml:"text/mathml",mmod:"chemical/x-macromodel-input",mng:"video/x-mng",moc:"text/x-moc",mol:"chemical/x-mdl-molfile",mol2:"chemical/x-mol2",moo:"chemical/x-mopac-out",mop:"chemical/x-mopac-input",mopcrt:"chemical/x-mopac-input",mov:"video/quicktime",movie:"video/x-sgi-movie",mp2:"audio/mpeg",mp3:"audio/mpeg",mp4:"video/mp4",mpc:"chemical/x-mopac-input",mpe:"video/mpeg",mpeg:"video/mpeg",mpega:"audio/mpeg",mpg:"video/mpeg",mpga:"audio/mpeg",mph:"application/x-comsol",mpv:"video/x-matroska",ms:"application/x-troff-ms",msh:"model/mesh",msi:"application/x-msi",mvb:"chemical/x-mopac-vib",mxf:"application/mxf",mxu:"video/vnd.mpegurl",nb:"application/mathematica",nbp:"application/mathematica",nc:"application/x-netcdf",nef:"image/x-nikon-nef",nwc:"application/x-nwc",o:"application/x-object",oda:"application/oda",odb:"application/vnd.oasis.opendocument.database",odc:"application/vnd.oasis.opendocument.chart",odf:"application/vnd.oasis.opendocument.formula",odg:"application/vnd.oasis.opendocument.graphics",odi:"application/vnd.oasis.opendocument.image",odm:"application/vnd.oasis.opendocument.text-master",odp:"application/vnd.oasis.opendocument.presentation",ods:"application/vnd.oasis.opendocument.spreadsheet",odt:"application/vnd.oasis.opendocument.text",oga:"audio/ogg",ogg:"audio/ogg",ogv:"video/ogg",ogx:"application/ogg",old:"application/x-trash",one:"application/onenote",onepkg:"application/onenote",onetmp:"application/onenote",onetoc2:"application/onenote",opf:"application/oebps-package+xml",opus:"audio/ogg",orc:"audio/csound",orf:"image/x-olympus-orf",otf:"application/font-sfnt",otg:"application/vnd.oasis.opendocument.graphics-template",oth:"application/vnd.oasis.opendocument.text-web",otp:"application/vnd.oasis.opendocument.presentation-template",ots:"application/vnd.oasis.opendocument.spreadsheet-template",ott:"application/vnd.oasis.opendocument.text-template",oza:"application/x-oz-application",p:"text/x-pascal",p7r:"application/x-pkcs7-certreqresp",pac:"application/x-ns-proxy-autoconfig",pas:"text/x-pascal",pat:"image/x-coreldrawpattern",patch:"text/x-diff",pbm:"image/x-portable-bitmap",pcap:"application/vnd.tcpdump.pcap",pcf:"application/x-font-pcf","pcf.Z":"application/x-font-pcf",pcx:"image/pcx",pdb:"chemical/x-pdb",pdf:"application/pdf",pfa:"application/x-font",pfb:"application/x-font",pfr:"application/font-tdpfr",pgm:"image/x-portable-graymap",pgn:"application/x-chess-pgn",pgp:"application/pgp-encrypted",php:"#application/x-httpd-php",php3:"#application/x-httpd-php3",php3p:"#application/x-httpd-php3-preprocessed",php4:"#application/x-httpd-php4",php5:"#application/x-httpd-php5",phps:"#application/x-httpd-php-source",pht:"#application/x-httpd-php",phtml:"#application/x-httpd-php",pk:"application/x-tex-pk",pl:"text/x-perl",pls:"audio/x-scpls",pm:"text/x-perl",png:"image/png",pnm:"image/x-portable-anymap",pot:"text/plain",potm:"application/vnd.ms-powerpoint.template.macroEnabled.12",potx:"application/vnd.openxmlformats-officedocument.presentationml.template",ppam:"application/vnd.ms-powerpoint.addin.macroEnabled.12",ppm:"image/x-portable-pixmap",pps:"application/vnd.ms-powerpoint",ppsm:"application/vnd.ms-powerpoint.slideshow.macroEnabled.12",ppsx:"application/vnd.openxmlformats-officedocument.presentationml.slideshow",ppt:"application/vnd.ms-powerpoint",pptm:"application/vnd.ms-powerpoint.presentation.macroEnabled.12",pptx:"application/vnd.openxmlformats-officedocument.presentationml.presentation",prf:"application/pics-rules",prt:"chemical/x-ncbi-asn1-ascii",ps:"application/postscript",psd:"image/x-photoshop",py:"text/x-python",pyc:"application/x-python-code",pyo:"application/x-python-code",qgs:"application/x-qgis",qt:"video/quicktime",qtl:"application/x-quicktimeplayer",ra:"audio/x-pn-realaudio",ram:"audio/x-pn-realaudio",rar:"application/rar",ras:"image/x-cmu-raster",rb:"application/x-ruby",rd:"chemical/x-mdl-rdfile",rdf:"application/rdf+xml",rdp:"application/x-rdp",rgb:"image/x-rgb",rhtml:"#application/x-httpd-eruby",rm:"audio/x-pn-realaudio",roff:"application/x-troff",ros:"chemical/x-rosdal",rpm:"application/x-redhat-package-manager",rss:"application/x-rss+xml",rtf:"application/rtf",rtx:"text/richtext",rxn:"chemical/x-mdl-rxnfile",scala:"text/x-scala",sce:"application/x-scilab",sci:"application/x-scilab",sco:"audio/csound",scr:"application/x-silverlight",sct:"text/scriptlet",sd:"chemical/x-mdl-sdfile",sd2:"audio/x-sd2",sda:"application/vnd.stardivision.draw",sdc:"application/vnd.stardivision.calc",sdd:"application/vnd.stardivision.impress",sds:"application/vnd.stardivision.chart",sdw:"application/vnd.stardivision.writer",ser:"application/java-serialized-object",sfd:"application/vnd.font-fontforge-sfd",sfv:"text/x-sfv",sgf:"application/x-go-sgf",sgl:"application/vnd.stardivision.writer-global",sh:"application/x-sh",shar:"application/x-shar",shp:"application/x-qgis",shtml:"text/html",shx:"application/x-qgis",sid:"audio/prs.sid",sig:"application/pgp-signature",sik:"application/x-trash",silo:"model/mesh",sis:"application/vnd.symbian.install",sisx:"x-epoc/x-sisx-app",sit:"application/x-stuffit",sitx:"application/x-stuffit",skd:"application/x-koan",skm:"application/x-koan",skp:"application/x-koan",skt:"application/x-koan",sldm:"application/vnd.ms-powerpoint.slide.macroEnabled.12",sldx:"application/vnd.openxmlformats-officedocument.presentationml.slide",smi:"application/smil+xml",smil:"application/smil+xml",snd:"audio/basic",spc:"chemical/x-galactic-spc",spl:"application/x-futuresplash",spx:"audio/ogg",sql:"application/x-sql",src:"application/x-wais-source",srt:"text/plain",stc:"application/vnd.sun.xml.calc.template",std:"application/vnd.sun.xml.draw.template",sti:"application/vnd.sun.xml.impress.template",stw:"application/vnd.sun.xml.writer.template",sty:"text/x-tex",sv4cpio:"application/x-sv4cpio",sv4crc:"application/x-sv4crc",svg:"image/svg+xml",svgz:"image/svg+xml",sw:"chemical/x-swissprot",swf:"application/x-shockwave-flash",swfl:"application/x-shockwave-flash",sxc:"application/vnd.sun.xml.calc",sxd:"application/vnd.sun.xml.draw",sxg:"application/vnd.sun.xml.writer.global",sxi:"application/vnd.sun.xml.impress",sxm:"application/vnd.sun.xml.math",sxw:"application/vnd.sun.xml.writer",t:"application/x-troff",tar:"application/x-tar",taz:"application/x-gtar-compressed",tcl:"application/x-tcl",tex:"text/x-tex",texi:"application/x-texinfo",texinfo:"application/x-texinfo",text:"text/plain",tgf:"chemical/x-mdl-tgf",tgz:"application/x-gtar-compressed",thmx:"application/vnd.ms-officetheme",tif:"image/tiff",tiff:"image/tiff",tk:"text/x-tcl",tm:"text/texmacs",torrent:"application/x-bittorrent",tr:"application/x-troff",ts:"video/MP2T",tsp:"application/dsptype",tsv:"text/tab-separated-values",ttf:"application/font-sfnt",ttl:"text/turtle",txt:"text/plain",uls:"text/iuls",ustar:"application/x-ustar",val:"chemical/x-ncbi-asn1-binary",vcard:"text/vcard",vcd:"application/x-cdlink",vcf:"text/vcard",vcs:"text/x-vcalendar",vmd:"chemical/x-vmd",vms:"chemical/x-vamas-iso14976",vrm:"x-world/x-vrml",vrml:"model/vrml",vsd:"application/vnd.visio",vss:"application/vnd.visio",vst:"application/vnd.visio",vsw:"application/vnd.visio",wad:"application/x-doom",wasm:"application/wasm",wav:"audio/wav",wax:"audio/x-ms-wax",wbmp:"image/vnd.wap.wbmp",wbxml:"application/vnd.wap.wbxml",webm:"video/webm",wk:"application/x-123",wm:"video/x-ms-wm",wma:"audio/x-ms-wma",wmd:"application/x-ms-wmd",wml:"text/vnd.wap.wml",wmlc:"application/vnd.wap.wmlc",wmls:"text/vnd.wap.wmlscript",wmlsc:"application/vnd.wap.wmlscriptc",wmv:"video/x-ms-wmv",wmx:"video/x-ms-wmx",wmz:"application/x-ms-wmz",woff:"application/font-woff",wp5:"application/vnd.wordperfect5.1",wpd:"application/vnd.wordperfect",wrl:"model/vrml",wsc:"text/scriptlet",wvx:"video/x-ms-wvx",wz:"application/x-wingz",x3d:"model/x3d+xml",x3db:"model/x3d+binary",x3dv:"model/x3d+vrml",xbm:"image/x-xbitmap",xcf:"application/x-xcf",xcos:"application/x-scilab-xcos",xht:"application/xhtml+xml",xhtml:"application/xhtml+xml",xlam:"application/vnd.ms-excel.addin.macroEnabled.12",xlb:"application/vnd.ms-excel",xls:"application/vnd.ms-excel",xlsb:"application/vnd.ms-excel.sheet.binary.macroEnabled.12",xlsm:"application/vnd.ms-excel.sheet.macroEnabled.12",xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",xlt:"application/vnd.ms-excel",xltm:"application/vnd.ms-excel.template.macroEnabled.12",xltx:"application/vnd.openxmlformats-officedocument.spreadsheetml.template",xml:"application/xml",xpi:"application/x-xpinstall",xpm:"image/x-xpixmap",xsd:"application/xml",xsl:"application/xslt+xml",xslt:"application/xslt+xml",xspf:"application/xspf+xml",xtel:"chemical/x-xtel",xul:"application/vnd.mozilla.xul+xml",xwd:"image/x-xwindowdump",xyz:"chemical/x-xyz",xz:"application/x-xz",zip:"application/zip"};let Q=null;Q={__proto__:null,CRC:class{reset(){}async updateBlob(){throw new v("Not implemented.(CRC may cause browser lag.)")}update(e){throw new v("Not implemented.(CRC may cause browser lag.)")}}};const{combineCrc64:J}=Q;let X=null;X={__proto__:null,createDefaultRateLimiter:function(e,t){throw Error("no implemention in browser environment")},createRateLimiterStream:function(e){throw Error("no implemention in browser environment")}};const{createDefaultRateLimiter:Y}=X;var Z;function ee(e){if(("string"==typeof e?e:e.key).length<1)throw new v("invalid object name, the length must be greater than 1")}function te(e,t){if(M(e))return e.length;if(O(e))return e.size;if(t&&t["content-length"]){const e=+t["content-length"];if(e>=0)return e}return null}async function ae(e){const t=function({body:e}){return{body:e,makeRetryStream:void 0}}(e);return async function({body:e,beforeRetry:t,makeRetryStream:a}){return{body:e,beforeRetry:t,makeRetryStream:a}}(e={...e,...t})}function oe(e,t){return`/${e}/${encodeURIComponent(t)}`}!function(e){e.HeaderRestore="x-tos-restore",e.HeaderRestoreExpiryDays="x-tos-restore-expiry-days",e.HeaderRestoreRequestDate="x-tos-restore-request-date",e.HeaderRestoreTier="x-tos-restore-tier",e.HeaderProjectName="x-tos-project-name",e.HeaderReplicationStatus="x-tos-replication-status"}(Z||(Z={}));const ne=e=>{if(!e)return;const t=null==e?void 0:e[Z.HeaderRestore];if(t){var a,o,n;const s=null!=(a=null==(o=(null!=t?t:"").split('",')[1])||null==o.split||null==(n=o.split("="))?void 0:n[1])?a:"",r='ongoing-request="true"'===(null==t?void 0:t.trim()),c={RestoreStatus:{OngoingRequest:r,ExpiryDate:s}};var i;return r&&(c.RestoreParam={ExpiryDays:e[Z.HeaderRestoreExpiryDays]?Number(e[Z.HeaderRestoreExpiryDays]):0,RequestDate:null!=(i=e[Z.HeaderRestoreRequestDate])?i:"",Tier:e[Z.HeaderRestoreTier]}),c}};var ie;(ie=exports.DataTransferType||(exports.DataTransferType={}))[ie.Started=1]="Started",ie[ie.Rw=2]="Rw",ie[ie.Succeed=3]="Succeed",ie[ie.Failed=4]="Failed";const se=s("TOS"),re=["content-length","user-agent","host"];function ce(e){switch(e){case"utf-8":return d;case"base64":return l;case"hex":return u;default:throw new v("The coding is not supported")}}function pe(e,t){return t?e.toString(ce(t)):e}const le=function(e,t){if(M(e))throw new v("not support buffer in browser environment");return pe(p(e),t)};let ue=null;ue={__proto__:null,hmacSha256:function(e,t,a){return pe(r(t,e),a)},hashSha256:function(e,t){return pe(c(e),t)},hashMd5:le,parse:function(e,t){return ce(t).parse(e)},stringify:function(e,t){return ce(t).stringify(e)}};const{hmacSha256:de,hashSha256:he,hashMd5:me,parse:fe,stringify:ye}=ue;async function ge(e){const{uploadId:t,partNumber:a,body:o,enableContentMD5:n=!1}=e,i=B(e.headers);e.headers=i,_(e,["trafficLimit","ssecAlgorithm","ssecKey","ssecKeyMD5"]);const s=te(o);s&&null==i["content-length"]&&(i["content-length"]=s.toFixed(0)),n&&null==i["content-md5"]&&console.warn("current not support enableMD5Checksum");const r=te(e.body,i),c=null!=r;c||!e.dataTransferStatusChange&&!e.progress||console.warn("Don't get totalSize of uploadPart's body, the `dataTransferStatusChange` callback will not trigger. You can use `uploadPartFromFile` instead");let p=0;const{dataTransferStatusChange:l,progress:u}=e,d=(e,t=0)=>{if(!c||t<0)return;if(!l&&!u)return;p+=t,null==l||l({type:e,rwOnceBytes:t,consumedBytes:p,totalBytes:r});const a=0===r?e===exports.DataTransferType.Succeed?1:0:p/r;1===a?e===exports.DataTransferType.Succeed&&(null==u||u(a)):null==u||u(a)},h=await ae({body:e.body,dataTransferCallback:e=>d(exports.DataTransferType.Rw,e),beforeRetry:e.beforeRetry,makeRetryStream:e.makeRetryStream,enableCRC:this.opts.enableCRC,rateLimiter:e.rateLimiter});d(exports.DataTransferType.Started);const[m,f]=await P((async()=>{const o=await this._fetchObject(e,"PUT",{partNumber:a,uploadId:t},i,h.body,{handleResponse:e=>({partNumber:a,ETag:e.headers.etag,serverSideEncryption:e.headers["x-tos-server-side-encryption"],serverSideDataEncryption:e.headers["x-tos-server-side-data-encryption"],serverSideEncryptionKeyId:e.headers["x-tos-server-side-encryption-kms-key-id"],ssecAlgorithm:e.headers["x-tos-server-side-encryption-customer-algorithm"],ssecKeyMD5:e.headers["x-tos-server-side-encryption-customer-key-MD5"],hashCrc64ecma:e.headers["x-tos-hash-crc64ecma"]}),axiosOpts:{__retryConfig__:{beforeRetry:()=>{p=0,null==h.beforeRetry||h.beforeRetry()},makeRetryStream:h.makeRetryStream},onUploadProgress:e=>{d(exports.DataTransferType.Rw,e.loaded-p)}}});return this.opts.enableCRC&&h.crc&&q(h.crc,o.headers),o})());if(f&&!f.data.ETag)throw new v("No ETag in uploadPart's response headers, please see https://www.volcengine.com/docs/6349/127737 to fix CORS problem");if(m||!f)throw d(exports.DataTransferType.Failed),m;return d(exports.DataTransferType.Succeed),f}async function xe(e){return ge.call(this,e)}async function be(e){throw new v("uploadPartFromFile doesn't support in browser environment")}async function ke(e){var t;e.headers=null!=(t=e.headers)?t:{},_(e,["callback","callbackVar","forbidOverwrite"]);const a=t=>{const a=t.headers,o={VersionID:a["x-tos-version-id"],ETag:a.etag,Bucket:e.bucket||this.opts.bucket||"",Location:a.location,HashCrc64ecma:a["x-tos-hash-crc64ecma"],Key:e.key,...t.data};return e.callback&&(o.CallbackResult=""+JSON.stringify(t.data)),o};if(e.completeAll){var o;if((null==(o=e.parts)?void 0:o.length)>0)throw new v("Should not specify both 'completeAll' and 'parts' params.");return this._fetchObject(e,"POST",{uploadId:e.uploadId},{...e.headers,"x-tos-complete-all":"yes"},void 0,{handleResponse:a})}return this._fetchObject(e,"POST",{uploadId:e.uploadId},{...e.headers},{Parts:e.parts.map(e=>({ETag:e.eTag,PartNumber:e.partNumber}))},{handleResponse:a})}var ve;(ve=exports.UploadEventType||(exports.UploadEventType={}))[ve.CreateMultipartUploadSucceed=1]="CreateMultipartUploadSucceed",ve[ve.CreateMultipartUploadFailed=2]="CreateMultipartUploadFailed",ve[ve.UploadPartSucceed=3]="UploadPartSucceed",ve[ve.UploadPartFailed=4]="UploadPartFailed",ve[ve.UploadPartAborted=5]="UploadPartAborted",ve[ve.CompleteMultipartUploadSucceed=6]="CompleteMultipartUploadSucceed",ve[ve.CompleteMultipartUploadFailed=7]="CompleteMultipartUploadFailed";const Te=[403,404,405];async function Se(e){var t,a,o;const{cancelToken:n,enableContentMD5:i=!1}=e,s=B(e.headers);e.headers=s,_(e,["encodingType","cacheControl","contentDisposition","contentEncoding","contentLanguage","contentType","expires","acl","grantFullControl","grantRead","grantReadAcp","grantWriteAcp","ssecAlgorithm","ssecKey","ssecKeyMD5","serverSideEncryption","serverSideDataEncryption","meta","websiteRedirectLocation","storageClass"]);const r=()=>n&&!!n.reason,c=await(async()=>null)(),p=await(async()=>{const{file:t}=e;if(c)return c.size;if(M(t))return t.length;if(O(t))return t.size;throw new v("`file` must be string, Buffer, File or Blob")})(),l=await(async()=>"object"==typeof e.checkpoint?{record:e.checkpoint}:{})();await(async()=>{var e;if(c&&null!=(e=l.record)&&e.file_info){var t;const{last_modified:e,file_size:a}=null==(t=l.record)?void 0:t.file_info;c.mtimeMs===e&&c.size===a||(console.warn(`The file has been modified since ${new Date(e)}, so the checkpoint file is invalid, and specified file will be uploaded again.`),delete l.record)}})();const u=$(p,e.partSize||(null==(t=l.record)?void 0:t.part_size)||20971520,!0);l.record&&l.record.part_size!==u&&(console.warn("The partSize param does not equal the partSize in checkpoint file, so the checkpoint file is invalid, and specified file will be uploaded again."),delete l.record);let d=e.bucket||this.opts.bucket||"";const h=e.key;let m="",f=[];const y=function(e,t){const a=[];for(let o=0;;++o){const n=o*t,i=Math.min(t,e-n);if(a.push({offset:n,partSize:i,partNumber:o+1}),(o+1)*t>=e)break}return a}(p,u),g=((null==(a=l.record)?void 0:a.parts_info)||[]).filter(e=>e.is_completed).reduce((e,t)=>e+t.part_size,0);let x=g;const b=(null==(o=l.record)?void 0:o.parts_info)||[],S=new Map;b.forEach(e=>S.set(e.part_number,e));const w=()=>{const e={bucket:d,key:h,part_size:u,upload_id:m,parts_info:b};return c&&(e.file_info={last_modified:c.mtimeMs,file_size:c.size}),e},C=t=>{if(!e.uploadEventChange)return;const a={bucket:d,uploadId:m,key:h,...t};l.filePath&&(a.checkpointFile=l.filePath),e.uploadEventChange(a)};let E;!function(e){e[e.start=1]="start",e[e.uploadPartSucceed=2]="uploadPartSucceed",e[e.completeMultipartUploadSucceed=3]="completeMultipartUploadSucceed"}(E||(E={}));const R=t=>{e.progress&&(x===p&&t===E.uploadPartSucceed||e.progress(t===E.start&&0===p?0:p?x/p:1,w()))};let I=g;const{dataTransferStatusChange:D}=e,A=(e,t=0)=>{D&&(I+=t,null==D||D({type:e,rwOnceBytes:t,consumedBytes:I,totalBytes:p}))},U=F(async()=>{}),L=async(e,t)=>{let a=S.get(e.partNumber);a||(a={part_number:e.partNumber,offset:e.offset,part_size:e.partSize,is_completed:!1,etag:"",hash_crc64ecma:""},b.push(a),S.set(a.part_number,a)),t.err||(a.is_completed=!0,a.etag=t.res.ETag,a.hash_crc64ecma=t.res.hashCrc64ecma),await U();const o={partNumber:a.part_number,partSize:a.part_size,offset:a.offset};if(t.err){const e=t.err;let a=exports.UploadEventType.UploadPartFailed;return e instanceof k&&Te.includes(e.statusCode)&&(a=exports.UploadEventType.UploadPartAborted),void C({type:a,err:e,uploadPartInfo:o})}o.etag=t.res.ETag,x+=o.partSize,C({type:exports.UploadEventType.UploadPartSucceed,uploadPartInfo:o}),R(E.uploadPartSucceed)};if(l.record){d=l.record.bucket,m=l.record.upload_id;const e=new Set((l.record.parts_info||[]).filter(e=>e.is_completed).map(e=>e.part_number));f=y.filter(t=>!e.has(t.partNumber))}else{try{const{data:t}=await G.call(this,e);if(r())throw new T("cancel uploadFile");var N;d=t.Bucket,m=t.UploadId,l.filePathIsPlaceholder&&(l.filePath=null==(N=l.filePath)?void 0:N.replace("@@checkpoint-file-placeholder@@",function(e,t){return`${t}.${me(`${e}.${t}`,"hex")}.upload`.replace(/[\\/]/g,"")}(d,h))),C({type:exports.UploadEventType.CreateMultipartUploadSucceed})}catch(e){const t=e;throw C({type:exports.UploadEventType.CreateMultipartUploadFailed,err:t}),t}f=y}R(E.start),A(exports.DataTransferType.Started);const[z,q]=await P((async()=>{let t=null,a=0;if(await Promise.all(Array.from({length:e.taskNum||1}).map(async()=>{for(;;){const n=a++;if(n>=f.length)return;const c=f[n];let p=0;const l=void 0;try{function o(e,t){const{offset:a,partSize:o}=t,n=a+o;if(l)return l.make();if(O(e))return e.slice(a,n);if(M(e))return e.slice(a,n);throw new v("`file` must be string, Buffer, File or Blob")}const{data:t}=await ge.call(this,{bucket:d,key:h,uploadId:m,body:o(e.file,c),enableContentMD5:i,makeRetryStream:null==l?void 0:l.make,beforeRetry:()=>{I-=p,p=0},partNumber:c.partNumber,headers:{"content-length":""+c.partSize,"x-tos-server-side-encryption-customer-algorithm":s["x-tos-server-side-encryption-customer-algorithm"],"x-tos-server-side-encryption-customer-key":s["x-tos-server-side-encryption-customer-key"],"x-tos-server-side-encryption-customer-key-md5":s["x-tos-server-side-encryption-customer-key-md5"]},dataTransferStatusChange(e){e.type===exports.DataTransferType.Rw&&(r()||(p+=e.rwOnceBytes,A(e.type,e.rwOnceBytes)))},trafficLimit:e.trafficLimit,rateLimiter:e.rateLimiter});if(r())throw new T("cancel uploadFile");await L(c,{res:t})}catch(e){H(null==l?void 0:l.getLastStream(),e);const a=e;if(I-=p,p=0,j(a))throw a;if(r())throw new T("cancel uploadFile");t||(t=a),await L(c,{err:a})}}})),t)throw t;const o=(w().parts_info||[]).map(e=>({eTag:e.etag,partNumber:e.part_number})),[n,c]=await P(ke.call(this,{bucket:d,key:h,uploadId:m,parts:o}));if(n||!c)throw C({type:exports.UploadEventType.CompleteMultipartUploadFailed}),n;if(C({type:exports.UploadEventType.CompleteMultipartUploadSucceed}),R(E.completeMultipartUploadSucceed),await(async()=>{})(),this.opts.enableCRC&&c.data.HashCrc64ecma&&function(e){var t,a,o;const n=(null==(t=e.file_info)?void 0:t.file_size)||0;let i="0";const s=null!=(a=null==(o=e.parts_info)||null==o.sort?void 0:o.sort((e,t)=>e.part_number-t.part_number))?a:[];for(const e of s)i=J(i,e.hash_crc64ecma,Math.min(e.part_size,n-e.offset));return i}(w())!==c.data.HashCrc64ecma)throw new v("crc of entire file mismatch.");return c})());if(z||!q)throw A(exports.DataTransferType.Failed),z;return A(exports.DataTransferType.Succeed),q}var we,Ce,Ee,Re,Be,Ie,Pe,Oe,Me,De,je,Ae,Ue,Le,_e,Ne,ze,qe,Ke;async function Fe(e){const t="string"==typeof e?{key:e}:e,a=B(t.headers);t.headers=a;const o={};return t.versionId&&(o.versionId=t.versionId),_(t,["ifMatch","ifModifiedSince","ifNoneMatch","ifUnmodifiedSince","ssecAlgorithm","ssecKey","ssecKeyMD5"]),this._fetchObject(e,"HEAD",o,(null==t?void 0:t.headers)||{},void 0,{handleResponse:e=>{const t={...e.headers,ReplicationStatus:e.headers[Z.HeaderReplicationStatus]},a=ne(e.headers);return a&&(t.RestoreInfo=a),t}})}async function He(e){const{uploadId:t,partNumber:a}=e,o=B(e.headers);if(e.headers=o,_(e,["copySourceRange","copySourceSSECAlgorithm","copySourceSSECKey","copySourceSSECKeyMD5","ssecAlgorithm","ssecKey","ssecKeyMD5","trafficLimit"]),e.srcBucket&&e.srcKey){var n;let t=oe(e.srcBucket,e.srcKey);e.srcVersionID&&(t+="?versionId="+e.srcVersionID),o["x-tos-copy-source"]=null!=(n=o["x-tos-copy-source"])?n:t}if(null==e.copySourceRange&&(null!=e.copySourceRangeStart||null!=e.copySourceRangeEnd)){var i;const t=`bytes=${null!=e.copySourceRangeStart?""+e.copySourceRangeStart:""}-${null!=e.copySourceRangeEnd?""+e.copySourceRangeEnd:""}`;o["x-tos-copy-source-range"]=null!=(i=o["x-tos-copy-source-range"])?i:t}const[s,r]=await P(this._fetchObject(e,"PUT",{partNumber:a,uploadId:t},o,void 0,{handleResponse:e=>({...e.data,SSECAlgorithm:e.headers[U.ssecAlgorithm],SSECKeyMD5:e.headers[U.ssecKeyMD5]})}));if(s||!r||!r.data.ETag)throw s;return r}async function Ge(e){const t=B(e.headers);if(e.headers=t,_(e,["cacheControl","contentDisposition","contentEncoding","contentLanguage","contentType","expires","copySourceIfMatch","copySourceIfModifiedSince","copySourceIfNoneMatch","copySourceIfUnmodifiedSince","copySourceSSECAlgorithm","copySourceSSECKey","copySourceSSECKeyMD5","acl","grantFullControl","grantRead","grantReadAcp","grantWriteAcp","ssecAlgorithm","ssecKey","ssecKeyMD5","serverSideEncryption","metadataDirective","meta","websiteRedirectLocation","storageClass","trafficLimit","forbidOverwrite","ifMatch"]),e.srcBucket&&e.srcKey){var a;let o=oe(e.srcBucket,e.srcKey);e.srcVersionID&&(o+="?versionId="+e.srcVersionID),t["x-tos-copy-source"]=null!=(a=t["x-tos-copy-source"])?a:o}const[o,n]=await P(this._fetchObject(e,"PUT",{},t));if(o||!n||!n.data.ETag)throw o;return n}(we=exports.ACLType||(exports.ACLType={})).ACLPrivate="private",we.ACLPublicRead="public-read",we.ACLPublicReadWrite="public-read-write",we.ACLAuthenticatedRead="authenticated-read",we.ACLBucketOwnerRead="bucket-owner-read",we.ACLBucketOwnerFullControl="bucket-owner-full-control",we.ACLBucketOwnerEntrusted="bucket-owner-entrusted",we.ACLDefault="default",(Ce=exports.StorageClassType||(exports.StorageClassType={})).StorageClassStandard="STANDARD",Ce.StorageClassIa="IA",Ce.StorageClassArchiveFr="ARCHIVE_FR",Ce.StorageClassColdArchive="COLD_ARCHIVE",Ce.StorageClassIntelligentTiering="INTELLIGENT_TIERING",Ce.StorageClassArchive="ARCHIVE",(Ee=exports.MetadataDirectiveType||(exports.MetadataDirectiveType={})).MetadataDirectiveCopy="COPY",Ee.MetadataDirectiveReplace="REPLACE",(Re=exports.AzRedundancyType||(exports.AzRedundancyType={})).AzRedundancySingleAz="single-az",Re.AzRedundancyMultiAz="multi-az",(Be=exports.PermissionType||(exports.PermissionType={})).PermissionRead="READ",Be.PermissionWrite="WRITE",Be.PermissionReadAcp="READ_ACP",Be.PermissionWriteAcp="WRITE_ACP",Be.PermissionFullControl="FULL_CONTROL",Be.PermissionReadNONLIST="READ_NON_LIST",(Ie=exports.GranteeType||(exports.GranteeType={})).GranteeGroup="Group",Ie.GranteeUser="CanonicalUser",(Pe=exports.CannedType||(exports.CannedType={})).CannedAllUsers="AllUsers",Pe.CannedAuthenticatedUsers="AuthenticatedUsers",(Oe=exports.HttpMethodType||(exports.HttpMethodType={})).HttpMethodGet="GET",Oe.HttpMethodPut="PUT",Oe.HttpMethodPost="POST",Oe.HttpMethodDelete="DELETE",Oe.HttpMethodHead="HEAD",(Me=exports.StorageClassInheritDirectiveType||(exports.StorageClassInheritDirectiveType={})).StorageClassInheritDirectiveDestinationBucket="DESTINATION_BUCKET",Me.StorageClassInheritDirectiveSourceObject="SOURCE_OBJECT",(De=exports.ReplicationStatusType||(exports.ReplicationStatusType={})).Complete="COMPLETE",De.Pending="PENDING",De.Failed="FAILED",De.Replica="REPLICA",(je=exports.LifecycleStatusType||(exports.LifecycleStatusType={})).Enabled="Enabled",je.Disabled="Disabled",(Ae=exports.RedirectType||(exports.RedirectType={})).Mirror="Mirror",Ae.Async="Async",(Ue=exports.StatusType||(exports.StatusType={})).Enabled="Enabled",Ue.Disabled="Disabled",(Le=exports.TierType||(exports.TierType={})).TierStandard="Standard",Le.TierExpedited="Expedited",Le.TierBulk="Bulk",(_e=exports.VersioningStatusType||(exports.VersioningStatusType={})).Enabled="Enabled",_e.Suspended="Suspended",_e.NotSet="",_e.Enable="Enabled",_e.Disable="",(Ne=exports.AccessPointStatusType||(exports.AccessPointStatusType={})).Ready="READY",Ne.Creating="CREATING",Ne.Created="CREATED",Ne.Deleting="DELETING",(ze=exports.TransferAccelerationStatusType||(exports.TransferAccelerationStatusType={})).Activating="AccelerationActivating",ze.Activated="AccelerationActivated",ze.Terminated="AccelerationTerminated",(qe=exports.MRAPMirrorBackRedirectPolicyType||(exports.MRAPMirrorBackRedirectPolicyType={})).ClosestFirst="Closest-First",qe.LatestFirst="Latest-First",(Ke=exports.ResumableCopyEventType||(exports.ResumableCopyEventType={}))[Ke.CreateMultipartUploadSucceed=1]="CreateMultipartUploadSucceed",Ke[Ke.CreateMultipartUploadFailed=2]="CreateMultipartUploadFailed",Ke[Ke.UploadPartCopySucceed=3]="UploadPartCopySucceed",Ke[Ke.UploadPartCopyFailed=4]="UploadPartCopyFailed",Ke[Ke.UploadPartCopyAborted=5]="UploadPartCopyAborted",Ke[Ke.CompleteMultipartUploadSucceed=6]="CompleteMultipartUploadSucceed",Ke[Ke.CompleteMultipartUploadFailed=7]="CompleteMultipartUploadFailed";const $e=[403,404,405];async function Ve(e){var t,a,o;const{cancelToken:n}=e,i=()=>n&&!!n.reason,{data:s}=await Fe.call(this,{bucket:e.srcBucket,key:e.srcKey,versionId:e.srcVersionId}),r=s.etag,c=+s["content-length"],p=await(async()=>"object"==typeof e.checkpoint?{record:e.checkpoint}:{})();await(async()=>{var e;if(null!=(e=p.record)&&e.copy_source_object_info){var t;const{last_modified:e,object_size:a}=null==(t=p.record)?void 0:t.copy_source_object_info;s["last-modified"]===e&&+s["content-length"]===a||(console.warn(`The file has been modified since ${new Date(e)}, so the checkpoint file is invalid, and specified file will be uploaded again.`),delete p.record)}})();const l=$(c,e.partSize||(null==(t=p.record)?void 0:t.part_size)||20971520,!0);p.record&&p.record.part_size!==l&&(console.warn("The partSize param does not equal the partSize in checkpoint file, so the checkpoint file is invalid, and specified file will be uploaded again."),delete p.record);let u=e.bucket||this.opts.bucket||"";const d=e.key;let m="",f=[];const y=function(e,t){const a=[];for(let o=0;;++o){const n=o*t,i=Math.min(t,e-n);if(a.push({offset:n,partSize:i,partNumber:o+1}),(o+1)*t>=e)break}return a}(c,l);let g=((null==(a=p.record)?void 0:a.parts_info)||[]).filter(e=>e.is_completed).reduce((e,t)=>e+t.copy_source_range_end-t.copy_source_range_start+1,0);const x=(null==(o=p.record)?void 0:o.parts_info)||[],b=new Map;x.forEach(e=>b.set(e.part_number,e));const S=()=>({bucket:u,key:d,part_size:l,upload_id:m,parts_info:x,copy_source_object_info:{last_modified:s["last-modified"],etag:s.etag,hash_crc64ecma:s["x-tos-hash-crc64ecma"]||"",object_size:+s["content-length"]}}),w=t=>{if(!e.copyEventListener)return;const a={bucket:u,uploadId:m,key:d,...t};p.filePath&&(a.checkpointFile=p.filePath),e.copyEventListener(a)};let C;!function(e){e[e.start=1]="start",e[e.uploadPartSucceed=2]="uploadPartSucceed",e[e.completeMultipartUploadSucceed=3]="completeMultipartUploadSucceed"}(C||(C={}));const E=t=>{e.progress&&(g===c&&t===C.uploadPartSucceed||e.progress(t===C.start&&0===c?0:c?g/c:1,S()))},R=F(async()=>{}),B=async(e,t)=>{let a=b.get(e.partNumber);const o=e.offset,n=Math.min(e.offset+l-1,c-1);a||(a={part_number:e.partNumber,copy_source_range_start:o,copy_source_range_end:n,is_completed:!1,etag:""},x.push(a),b.set(a.part_number,a)),t.err||(a.is_completed=!0,a.etag=t.res.ETag),await R();const i={partNumber:a.part_number,copySourceRangeEnd:a.copy_source_range_end,copySourceRangeStart:a.copy_source_range_start};if(t.err){const e=t.err;let a=exports.ResumableCopyEventType.UploadPartCopyFailed;return e instanceof k&&$e.includes(e.statusCode)&&(a=exports.ResumableCopyEventType.UploadPartCopyAborted),void w({type:a,err:e,copyPartInfo:i})}i.etag=t.res.ETag,g+=i.copySourceRangeEnd-i.copySourceRangeStart+1,w({type:exports.ResumableCopyEventType.UploadPartCopySucceed,copyPartInfo:i}),E(C.uploadPartSucceed)};if(p.record){u=p.record.bucket,m=p.record.upload_id;const e=new Set((p.record.parts_info||[]).filter(e=>e.is_completed).map(e=>e.part_number));f=y.filter(t=>!e.has(t.partNumber))}else{try{const{data:t}=await G.call(this,h(e));if(i())throw new T("cancel uploadFile");var I;u=t.Bucket,m=t.UploadId,p.filePathIsPlaceholder&&(p.filePath=null==(I=p.filePath)?void 0:I.replace("@@checkpoint-file-placeholder@@",[(O={...e,bucket:u}).srcBucket,O.srcKey,O.srcVersionId,O.bucket,O.key,"copy"].filter(Boolean).join(".").replace(/[\\/]/g,""))),w({type:exports.ResumableCopyEventType.CreateMultipartUploadSucceed})}catch(e){const t=e;throw w({type:exports.ResumableCopyEventType.CreateMultipartUploadFailed,err:t}),t}f=y}var O;return E(C.start),0===c?(async()=>{let t=oe(e.srcBucket,e.srcKey);e.srcVersionId&&(t+="?versionId="+e.srcVersionId);const a={...e.headers,"x-tos-copy-source":t,"x-tos-copy-source-if-match":r},[o,n]=await P(Ge.call(this,{bucket:e.bucket,key:e.key,headers:a,trafficLimit:e.trafficLimit}));if(o||!n)throw w({type:exports.ResumableCopyEventType.UploadPartCopyFailed}),o;return E(C.completeMultipartUploadSucceed),w({type:exports.ResumableCopyEventType.UploadPartCopySucceed,copyPartInfo:{partNumber:0,copySourceRangeStart:0,copySourceRangeEnd:0}}),w({type:exports.ResumableCopyEventType.CompleteMultipartUploadSucceed}),{...n,data:{ETag:n.headers.etag||"",Bucket:u,Key:d,Location:`http${this.opts.secure?"s":""}://${u}.${this.opts.endpoint}/${d}`,VersionID:n.headers["x-tos-version-id"],HashCrc64ecma:n.headers["x-tos-hash-crc64ecma"]}}})():(async()=>{let t=null,a=0;if(await Promise.all(Array.from({length:e.taskNum||1}).map(async()=>{for(;;){const o=a++;if(o>=f.length)return;const n=f[o];try{let t=oe(e.srcBucket,e.srcKey);e.srcVersionId&&(t+="?versionId="+e.srcVersionId);const a=`bytes=${n.offset}-${n.offset+n.partSize-1}`,o={...e.headers,"x-tos-copy-source":t,"x-tos-copy-source-if-match":r,"x-tos-copy-source-range":a};n.partSize||delete o["x-tos-copy-source-range"];const{data:s}=await He.call(this,{bucket:u,key:d,uploadId:m,partNumber:n.partNumber,headers:o,trafficLimit:e.trafficLimit});if(i())throw new T("cancel resumableCopyObject");await B(n,{res:s})}catch(e){const a=e;if(We(a))throw a;if(i())throw new T("cancel resumableCopyObject");t||(t=a),await B(n,{err:a})}}})),t)throw t;const o=(S().parts_info||[]).map(e=>({eTag:e.etag,partNumber:e.part_number})),[n,s]=await P(ke.call(this,{bucket:u,key:d,uploadId:m,parts:o}));if(n||!s)throw w({type:exports.ResumableCopyEventType.CompleteMultipartUploadFailed}),n;w({type:exports.ResumableCopyEventType.CompleteMultipartUploadSucceed}),E(C.completeMultipartUploadSucceed);const c=S().copy_source_object_info.hash_crc64ecma,p=s.data.HashCrc64ecma;if(this.opts.enableCRC&&c&&p&&c!==p)throw new v(`validate file crc64 failed. Expect crc64 ${c}, actual crc64 ${p}. Please try again.`);return await(async()=>{})(),s})()}function We(e){return e instanceof T}async function Qe(e){const t="string"==typeof e?{key:e}:e,a={};t.versionId&&(a.versionId=t.versionId);const o=B(null==t?void 0:t.headers),n=(null==t?void 0:t.response)||{};return Object.keys(n).forEach(e=>{const t=n[e];null!=t&&(a["response-"+e]=t)}),this._fetchObject(e,"GET",a,o,void 0,{axiosOpts:{responseType:"arraybuffer"}})}const Je=["blob"];async function Xe(e){const t="string"==typeof e?{key:e}:e,a=B(t.headers);t.headers=a;const o=t.dataType||"stream";t.dataType=o,function(e){let t="node",a=[];if(t="browser",a=Je,!a.includes(e))throw new v(`The value of \`dataType\` only supports \`${a.join(" | ")}\` in browser environment`)}(o);const n={},i=(null==t?void 0:t.response)||{};if(Object.keys(i).forEach(e=>{const t=i[e];null!=t&&(n["response-"+e]=t)}),function(e,t,a){function o(e,a){null==t[e]&&(t[e]=a)}a.length&&a.forEach(t=>{const a=L[t];if(!a)throw new v(`\`${t}\` isn't in keys of \`requestQueryMap\``);const n=e[t];if(null==n)return;if("string"==typeof a)return o(a,""+n);if(Array.isArray(a))return o(a[0],a[1](n));const i=a(n);Object.entries(i).forEach(([e,t])=>{o(e,t)})})}(t,n,["versionId","process","saveBucket","saveObject","responseCacheControl","responseContentDisposition","responseContentEncoding","responseContentLanguage","responseContentType","responseExpires"]),_(t,["ifMatch","ifModifiedSince","ifNoneMatch","ifUnmodifiedSince","ssecAlgorithm","ssecKey","ssecKeyMD5","range","trafficLimit"]),null==t.range&&(null!=t.rangeStart||null!=t.rangeEnd)){var s;const e=`bytes=${null!=t.rangeStart?""+t.rangeStart:""}-${null!=t.rangeEnd?""+t.rangeEnd:""}`;a.range=null!=(s=a.range)?s:e}let r=0,c=-1;const{dataTransferStatusChange:p,progress:l}=t,u=(e,t=0)=>{if(t<0)return;if(!p&&!l)return;r+=t,null==p||p({type:e,rwOnceBytes:t,consumedBytes:r,totalBytes:c});const a=c<0?0:0===c?e===exports.DataTransferType.Succeed?1:0:r/c;1===a?e===exports.DataTransferType.Succeed&&(null==l||l(a)):null==l||l(a)};u(exports.DataTransferType.Started);const[d,h]=await P(this._fetchObject(e,"GET",n,a,void 0,{axiosOpts:{responseType:"arraybuffer",onDownloadProgress:e=>{c=e.total,u(exports.DataTransferType.Rw,e.loaded-r)}}}));if(d||!h)throw u(exports.DataTransferType.Failed),d;let m=h.headers,f=h.data;c=+(m["content-length"]||0),"blob"===o&&(f=new Blob([h.data],{type:m["content-type"]})),u(exports.DataTransferType.Succeed);const y={...h,data:{content:f,etag:m.etag||"",lastModified:m["last-modified"]||"",hashCrc64ecma:m["x-tos-hash-crc64ecma"]||"",ReplicationStatus:m[Z.HeaderReplicationStatus]}},g=ne(m);return g&&(y.data.RestoreInfo=g),y}async function Ye(e){throw new v("getObjectToFile doesn't support in browser environment")}var Ze;async function et(e){throw new v("`downloadFile` is not supported in browser environment")}function tt(e){return!e||80===e||443===e}(Ze=exports.DownloadEventType||(exports.DownloadEventType={}))[Ze.CreateTempFileSucceed=1]="CreateTempFileSucceed",Ze[Ze.CreateTempFileFailed=2]="CreateTempFileFailed",Ze[Ze.DownloadPartSucceed=3]="DownloadPartSucceed",Ze[Ze.DownloadPartFailed=4]="DownloadPartFailed",Ze[Ze.DownloadPartAborted=5]="DownloadPartAborted",Ze[Ze.RenameTempFileSucceed=6]="RenameTempFileSucceed",Ze[Ze.RenameTempFileFailed=7]="RenameTempFileFailed";const at="request";class ot{constructor(e,t){this.options=void 0,this.credentials=void 0,this.signature=(e,t,a)=>{a||(a=this.credentials);const o=[],n=this.credentialString(e.datetime);return o.push(this.options.algorithm+" Credential="+a.GetAccessKey()+"/"+n),o.push("SignedHeaders="+this.signedHeaders(e)),o.push("Signature="+this.authorization(e,a,0)),o.join(", ")},this.signatureHeader=(e,t,a)=>{e.datetime=this.getDateTime();const o=new Map;e.headers||(e.headers={}),e.headers.host=""+e.host,tt(e.port)||(e.headers.host+=":"+e.port),e.endpoints&&(e.headers.host=`${this.options.bucket}.${e.endpoints}`),o.set("host",e.headers.host),o.set("x-tos-date",e.datetime),o.set("x-tos-content-sha256",this.hexEncodedBodyHash()),this.options.securityToken&&o.set("x-tos-security-token",this.options.securityToken),o.forEach((t,a)=>{a.startsWith("x-tos")&&(e.headers[a]=t)}),e.path=this.getEncodePath(e.path);const n=this.signature(e,0,a);return o.set("authorization",n),o},this.gnrCopySig=(e,t)=>({key:"",value:""}),this.getSignature=(e,t)=>({key:"",value:""}),this.getSignatureQuery=(e,t)=>{e.datetime=this.getDateTime(),e.headers||(e.headers={}),e.headers.host=""+e.host,tt(e.port)||(e.headers.host+=":"+e.port),e.path=this.getEncodePath(e.path),e.endpoints&&(e.headers.host=`${this.options.bucket}.${e.endpoints}`),e.headers["X-Tos-Date"]=e.datetime;const a=this.credentialString(e.datetime),o={...e.query||{},"X-Tos-Algorithm":this.options.algorithm,"X-Tos-Content-Sha256":this.hexEncodedBodyHash(),"X-Tos-Credential":this.credentials.GetAccessKey()+"/"+a,"X-Tos-Date":e.datetime,"X-Tos-Expires":""+t,"X-Tos-SignedHeaders":this.signedHeaders(e)};return this.options.securityToken&&(o["X-Tos-Security-Token"]=this.options.securityToken),e.query=R(o),o["X-Tos-Signature"]=this.authorization(e,this.credentials,t),o},this.getSignaturePolicyQuery=(e,t)=>{e.datetime=this.getDateTime();const a=this.credentialString(e.datetime),o={"X-Tos-Algorithm":this.options.algorithm,"X-Tos-Credential":this.credentials.GetAccessKey()+"/"+a,"X-Tos-Date":e.datetime,"X-Tos-Expires":""+t,"X-Tos-Policy":ye(fe(JSON.stringify(e.policy),"utf-8"),"base64")};return this.options.securityToken&&(o["X-Tos-Security-Token"]=this.options.securityToken),e.query=R(o),o["X-Tos-Signature"]=this.authorization(e,this.credentials,t),o},this.hexEncodedBodyHash=()=>"UNSIGNED-PAYLOAD",this.authorization=(e,t,a)=>{if(!e.datetime)return"";const o=this.getSigningKey(t,e.datetime.substr(0,8));return de(o,this.stringToSign(e.datetime,e),"hex")},this.getDateTime=()=>new Date((new Date).toUTCString()).toISOString().replace(/\..+/,"").replace(/-/g,"").replace(/:/g,"")+"Z",this.credentialString=e=>this.createScope(e.substr(0,8),this.options.region,this.options.serviceName),this.createScope=(e,t,a)=>[e.substr(0,8),t,a,at].join("/"),this.getSigningKey=(e,t)=>{const a=de(e.GetSecretKey(),t),o=de(a,this.options.region),n=de(o,this.options.serviceName);return de(n,at)},this.stringToSign=(e,t)=>{if(!this.options.algorithm)return"";const a=[];a.push(this.options.algorithm),a.push(e),a.push(this.credentialString(e));const o="policy"in t?this.canonicalStringPolicy(t):this.canonicalString(t);return a.push(this.hexEncodedHash(o)),a.join("\n")},this.hexEncodedHash=e=>he(e,"hex"),this.canonicalString=e=>{const t=[];return t.push(e.method),t.push(e.path),t.push(this.getEncodePath(e.query,!1)),t.push(this.canonicalHeaders(e)+"\n"),t.push(this.signedHeaders(e)),t.push(this.hexEncodedBodyHash()),t.join("\n")},this.canonicalStringPolicy=e=>{const t=[];return t.push(this.getEncodePath(e.query,!1)),t.push(this.hexEncodedBodyHash()),t.join("\n")},this.canonicalHeaders=e=>{const t=[],a=it(e.headers);for(let o of a){const a=e.headers[o];o=o.toLowerCase(),t.push(o+":"+this.canonicalHeaderValues(a.toString()))}return t.join("\n")},this.canonicalHeaderValues=e=>e.replace(/\s+/g," ").replace(/^\s+|\s+$/g,""),this.signedHeaders=e=>{const t=[],a=it(e.headers);for(let e of a)e=e.toLowerCase(),t.push(e);return t.sort().join(";")},this.options=e,this.credentials=t}getEncodePath(e,t=!0){if(!e)return"";let a=e;return t&&(a=e.replace(/%2F/g,"/")),a=a.replace(/\(/g,"%28"),a=a.replace(/\)/g,"%29"),a=a.replace(/!/g,"%21"),a=a.replace(/\*/g,"%2A"),a=a.replace(/\'/g,"%27"),a}}class nt{constructor(e,t,a){this.securityToken=void 0,this.secretAccessKey=void 0,this.accessKeyId=void 0,this.accessKeyId=a,this.secretAccessKey=t,this.securityToken=e}GetAccessKey(){return this.accessKeyId}GetSecretKey(){return this.secretAccessKey}}function it(e){const t=[];return Object.keys(e||{}).forEach(a=>{("host"===a||a.startsWith("x-tos-"))&&null!=e[a]&&t.push(a)}),t.sort()}let st="wexin";function rt(e,{transformRequestOption:t=(e=>e)}={}){const a=function(){switch(!0){case"object"==typeof wx:return st="wexin",wx.request.bind(wx);case"object"==typeof swan:return st="baidu",swan.request.bind(swan);case"object"==typeof dd:return st="dd",dd.httpRequest.bind(dd);case"object"==typeof my:return st="alipay",(my.request||my.httpRequest).bind(my);default:return wx.request.bind(wx)}}();return new Promise((o,n)=>{let i,s=e.data,r=e.headers;const c={method:e.method&&e.method.toUpperCase()||"GET",url:y(g(e.baseURL,e.url),e.params,e.paramsSerializer),timeout:e.timeout,success:t=>{const a=function(e,t,a){const o=e.statusCode||e.status;let n="";return 200===o?n="OK":400===o&&(n="Bad Request"),{data:e.data,status:o,statusText:n,headers:e.header||e.headers,config:t,request:a}}(t,e,c);f(o,n,a)},fail:t=>{!function(e,t,a){switch(st){case"wexin":-1!==e.errMsg.indexOf("request:fail abort")?t(x("Request aborted",a,"ECONNABORTED","")):-1!==e.errMsg.indexOf("timeout")?t(x("timeout of "+a.timeout+"ms exceeded",a,"ECONNABORTED","")):t(x("Network Error",a,null,""));break;case"dd":case"alipay":[14,19].includes(e.error)?t(x("Request aborted",a,"ECONNABORTED","",e)):[13].includes(e.error)?t(x("timeout of "+a.timeout+"ms exceeded",a,"ECONNABORTED","",e)):t(x("Network Error",a,null,"",e));break;case"baidu":t(x("Network Error",a,null,""))}}(t,n,e)},complete(){i=void 0}};if(e.auth){const[t,a]=[e.auth.username||"",e.auth.password||""];r.Authorization="Basic "+function(e){const t=String(e);let a,o,n=0,i="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",s="";for(;t.charAt(0|n)||(i="=",n%1);s+=i.charAt(63&a>>8-n%1*8)){if(o=t.charCodeAt(n+=3/4),o>255)throw new Error("'btoa' failed: The string to be encoded contains characters outside of the Latin1 range.");a=a<<8|o}return s}(t+":"+a)}m.forEach(r,(function(e,t){const a=t.toLowerCase();(void 0===s&&"content-type"===a||"referer"===a)&&delete r[t]})),c.header=r,e.responseType&&(c.responseType=e.responseType),e.cancelToken&&e.cancelToken.promise.then((function(e){i&&(i.abort(),n(e),i=void 0)})),(e=>{try{return"string"==typeof e&&e.length&&(e=JSON.parse(e))&&"[object Object]"===Object.prototype.toString.call(e)}catch(e){return!1}})(s)&&(s=JSON.parse(s)),void 0!==s&&(c.data=s),i=a(t(function(e){var t;return["alipay","dd"].includes(st)&&(e.headers=e.header,delete e.header,"dd"===st&&"GET"!==e.method&&"application/json"===(null==(t=e.headers)?void 0:t["Content-Type"])&&"[object Object]"===Object.prototype.toString.call(e.data)&&(e.data=JSON.stringify(e.data))),e}(c)))})}class ct{constructor(e){this.opts=void 0,this.axiosInst=void 0,this.userAgent=void 0,this.httpAgent=void 0,this.httpsAgent=void 0,this.getObjectPath=e=>{const t="string"!=typeof e&&e.bucket||this.opts.bucket,a="string"==typeof e?e:e.key;if(!t)throw new v("Must provide bucket param");return`/${t}/${encodeURIComponent(a)}`},this.setObjectContentTypeHeader=(e,t)=>{if(null!=t["content-type"])return;let a="application/octet-stream";const o=(e=>"string"==typeof e?e:e.key)(e);this.opts.autoRecognizeContentType&&(a=function(e){const t=e.lastIndexOf(".");if(t<=0)return;const a=e.slice(t+1).toLowerCase();return W[a]}(o)||a),a&&(t["content-type"]=a)},this.getNormalDataFromError=z,this.opts=this.normalizeOpts(e),this.userAgent=this.getUserAgent(),this.axiosInst=(e=>{const t=a.create();t.defaults.auth=void 0,t.defaults.responseType="json",t.defaults.params=void 0,t.defaults.headers={},t.defaults.withCredentials=!1,t.defaults.maxContentLength=-1,t.defaults.maxBodyLength=-1,t.defaults.maxRedirects=0,t.defaults.validateStatus=function(e){return e>=200&&e<300},t.defaults.decompress=!1,t.defaults.transitional={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},t.interceptors.request.use(e=>e.headers?(Object.keys(e.headers).forEach(t=>{re.includes(t.toLowerCase())&&delete e.headers[t]}),e):e);const o=e=>{var t;return e.headers=e.headers||e.header||(null==e||null==(t=e.response)?void 0:t.headers)||{},e};function n(e){Object.entries(e).forEach(([t,a])=>{const[o,n]=function(e){try{return[null,decodeURI(a)]}catch(e){return[e,null]}}();if(o||null==n||n===a)return;let i=[];const s=(""+a).match(/./gu),r=n.match(/./gu);for(let e=0,t=0;e<r.length;){const a=r[e];if(a===s[t]){i.push(a),++e,++t;continue}const o=encodeURIComponent(a);a.length>1||a.charCodeAt(0)>=128?i.push(a):i.push(o),++e,t+=o.length}e[t]=i.join("")})}return t.interceptors.response.use(o,e=>(o(e),Promise.reject(e))),t.interceptors.response.use(e=>e.headers?(n(e.headers),e):e,async e=>{var t;if(!a.isAxiosError(e))return Promise.reject(e);const o=null==(t=e.response)?void 0:t.headers;return o?(n(o),Promise.reject(e)):Promise.reject(e)}),t.interceptors.response.use(void 0,async a=>{var o;const{config:n}=a;if(!n)return Promise.reject(a);n.__retryConfig__||(n.__retryConfig__={});const i=n.__retryConfig__,s=null!=(o=i.retryCount)?o:0;let r=n.data;if(!((function(e){var t;return!e.response&&Boolean(e.code)||e.response&&!(null!=(t=e.response.headers)&&t["x-tos-request-id"])}(a)||function(e){if(!e.response)return!1;const{status:t}=e.response;return 429===t||t>=500}(a))&&s<e))return Promise.reject(a);const c=n.__retrySignature__;if(c){const{signOpt:e,sigInst:t}=c;t.signatureHeader(e).forEach((e,t)=>{n.headers[t]=e})}se("retryConfig: ",n);const p={...n,data:r,__retryConfig__:{...i,retryCount:s+1}};return null==i.beforeRetry||i.beforeRetry(),t(p)}),t})(this.opts.maxRetryCount)}normalizeOpts(e){var t,a;["accessKeyId","accessKeySecret","stsToken","region","endpoint"].forEach(t=>{const a=e[t];"string"==typeof a&&(e[t]=a.trim())});const o=["accessKeyId","accessKeySecret","region"].filter(t=>!e[t]).join(", ");if(o)throw new v(`lack params: ${o}.`);const n=e.endpoint||`tos-${e.region}.volces.com`;if(!n)throw new v("the value of param region is invalid, correct values are cn-beijing, cn-nantong etc.");if(n.includes("s3"))throw new v("do not support s3 endpoint, please use tos endpoint.");const i=null==e.secure||!!e.secure,s=(e,t)=>null==e?t:e;if(null!=(t=e.enableCRC)&&t)throw new v("not support crc in browser environment");return{...e,endpoint:n,secure:i,enableVerifySSL:s(e.enableVerifySSL,!0),autoRecognizeContentType:s(e.autoRecognizeContentType,!0),requestTimeout:s(e.requestTimeout,12e4),connectionTimeout:s(e.connectionTimeout,1e4),maxConnections:s(e.maxConnections,1024),idleConnectionTime:s(e.idleConnectionTime,3e4),maxRetryCount:s(e.maxRetryCount,3),enableCRC:null!=(a=e.enableCRC)&&a,requestAdapter:pt()}}getUserAgent(){return"ve-tos-browserjs-sdk/v2.7.4"}async fetch(e,t,o,n,i,s){const r=(null==s?void 0:s.handleResponse)||(e=>e.data);if(i&&(null==s?void 0:s.needMd5)){const e=me(JSON.stringify(i),"base64");n["content-md5"]=e}const[c,p]=(()=>null!=s&&s.subdomainBucket&&this.opts.forcePathStyle?[this.opts.endpoint,`/${s.subdomainBucket}${t}`]:null!=s&&s.subdomainBucket&&!this.opts.isCustomDomain?/^(\d|:)/.test(this.opts.endpoint)?[this.opts.endpoint,`/${s.subdomainBucket}${t}`]:[`${null==s?void 0:s.subdomainBucket}.${this.opts.endpoint}`,t]:[this.opts.endpoint,t])();t=p,n=(e=>{const t={};return Object.entries(e).forEach(([e,a])=>{t[e]=(""+a).match(/./gu).map(e=>e.length>1||e.charCodeAt(0)>=128?encodeURIComponent(e):e).join("")}),t})(n);const l={endpoints:void 0,bucket:"",method:e,headers:{...n},path:t,query:R(o),host:c},u=new nt(this.opts.stsToken,this.opts.accessKeySecret,this.opts.accessKeyId),d=new ot({algorithm:"TOS4-HMAC-SHA256",region:this.opts.region,serviceName:"tos",bucket:"",securityToken:this.opts.stsToken},u),h=d.signatureHeader(l),m={...n},f={method:e,baseURL:`http${this.opts.secure?"s":""}://${c}`,url:t,params:o,headers:m,data:i};h.forEach((e,t)=>{f.headers[t]=e});const y=I(this.opts.proxy);if(null!=y&&y.url&&!this.opts.proxyHost)f.baseURL=y.url,null!=y&&y.needProxyParams&&(f.params["x-proxy-tos-host"]=c,delete m.host);else if(this.opts.proxyHost){if(!this.opts.proxyPort)throw new v("The `proxyPort` is required if `proxyHost` is truly.");f.proxy={host:this.opts.proxyHost,port:this.opts.proxyPort,protocol:"http"}}m["user-agent"]=this.userAgent,this.opts.requestTimeout>0&&Infinity!==this.opts.requestTimeout&&(f.timeout=this.opts.requestTimeout);try{const e={...f};delete e.httpAgent,delete e.httpsAgent,se("reqOpts: ",e);const t=await this.axiosInst({maxBodyLength:Infinity,maxContentLength:Infinity,adapter:this.opts.requestAdapter,...f,...(null==s?void 0:s.axiosOpts)||{},__retrySignature__:{signOpt:l,sigInst:d}});return{data:r(t),statusCode:t.status,headers:t.headers,requestId:t.headers["x-tos-request-id"],id2:t.headers["x-tos-id-2"]}}catch(e){var g,x;if(a.isAxiosError(e)&&null!=(g=e.response)&&null!=(x=g.headers)&&x["x-tos-request-id"]){const t=e.response;throw se("TosServerError response: ",t),new k(t)}throw se("err: ",e),e}}async fetchBucket(e,t,a,o,n,i){const s=e||this.opts.bucket;if(!s)throw new v("Must provide bucket param");return this.fetch(t,"/",a,o,n,{...i,subdomainBucket:s})}async _fetchObject(e,t,a,o,n,i){const s="string"!=typeof e&&e.bucket||this.opts.bucket,r="string"==typeof e?e:e.key;if(!s)throw new v("Must provide bucket param");return ee(r),this.fetch(t,"/"+encodeURIComponent(r),a,o,n,{...i,subdomainBucket:s})}getSignatureQuery(e){const t=new nt(this.opts.stsToken,this.opts.accessKeySecret,this.opts.accessKeyId),a=new ot({algorithm:"TOS4-HMAC-SHA256",region:this.opts.endpoint,serviceName:"tos",bucket:e.bucket,securityToken:this.opts.stsToken},t);return"policy"in e?a.getSignaturePolicyQuery({policy:e.policy},e.expires):a.getSignatureQuery({method:e.method,path:e.path,endpoints:e.subdomain?e.endpoint:void 0,host:e.endpoint,query:e.query},e.expires)}normalizeBucketInput(e){return"string"==typeof e?{bucket:e}:e}normalizeObjectInput(e){return"string"==typeof e?{key:e}:e}}function pt(){if("undefined"==typeof window||void 0===window.location)switch(!0){case"undefined"!=typeof wx:case"undefined"!=typeof swan:case"undefined"!=typeof dd:case"undefined"!=typeof my:return rt;case"undefined"!=typeof uni:return b;default:return}}async function lt(e={}){const{...t}=e,a=await this.fetchBucket(e.bucket,"GET",C(t),{}),o=S(a.data);return o("CommonPrefixes"),o("Contents"),o("Versions"),o("DeleteMarkers"),a}async function ut(e={}){const{...t}=e,a=await this.fetchBucket(e.bucket,"GET",C({versions:"",...t}),{}),o=S(a.data);return o("CommonPrefixes"),o("Versions"),o("DeleteMarkers"),a}async function dt(e={}){const{listOnlyOnce:t=!1}=e;let a;if(e.maxKeys||(e.maxKeys=1e3),t)a=await ht.call(this,e);else{const t=e.maxKeys;let o={...e,maxKeys:t};for(;;){const e=await ht.call(this,o);if(null==a?a=e:(a={...e,data:a.data},a.data.KeyCount+=e.data.KeyCount,a.data.IsTruncated=e.data.IsTruncated,a.data.NextContinuationToken=e.data.NextContinuationToken,a.data.Contents=a.data.Contents.concat(e.data.Contents),a.data.CommonPrefixes=a.data.CommonPrefixes.concat(e.data.CommonPrefixes)),!e.data.IsTruncated||a.data.KeyCount>=t)break;o.continuationToken=e.data.NextContinuationToken,o.maxKeys=o.maxKeys-e.data.KeyCount}}return a}async function ht(e){const{...t}=e,a=await this.fetchBucket(e.bucket,"GET",{"list-type":2,...C(t)},{}),o=S(a.data);return o("CommonPrefixes"),o("Contents"),a}class mt extends ct{modifyAxiosInst(){this.axiosInst.interceptors.request.use(e=>{const t=e.headers||{};return delete t.authorization,t.host=this.parsedPolicyUrlVal.host,e.baseURL=this.parsedPolicyUrlVal.origin,e.paramsSerializer=e=>{const t=N(e);return[this.parsedPolicyUrlVal.search,t].filter(e=>e.trim()).join("&")},e})}constructor(e){super({...e,bucket:"fake-bucket",region:"fake-region",accessKeyId:"fake-accessKeyId",accessKeySecret:"fake-accessKeySecret",endpoint:"fake-endpoint.com"}),this.shareLinkClientOpts=void 0,this.parsedPolicyUrlVal=void 0,this.headObject=Fe,this.getObjectV2=Xe,this.listObjects=lt,this.listObjectsType2=dt,this.listObjectVersions=ut,this.downloadFile=et,this.shareLinkClientOpts=e,this.parsedPolicyUrlVal=this.initParsedPolicyUrlVal(),this.modifyAxiosInst()}initParsedPolicyUrlVal(){const e=this.shareLinkClientOpts.policyUrl.match(/(https?:\/\/(?:[^@]+@)?([^/?]+))[^?]*\?(.+)/);if(!e)throw new v("the `policyUrl` param is invalid");return{origin:e[1],host:e[2],search:e[3]}}}async function ft(e={}){const t={};(null==e?void 0:e.projectName)&&_({...e,headers:t},["projectName"]);const a=await this.fetch("GET","/",{},t);return S(a.data)("Buckets"),a}async function yt(e){const t=e.bucket||this.opts.bucket;if(t){if(t.length<3||t.length>63)throw new v("invalid bucket name, the length must be [3, 63]");if(!/^([a-z]|-|\d)+$/.test(t))throw new v("invalid bucket name, the character set is illegal");if(/^-/.test(t)||/-$/.test(t))throw new v("invalid bucket name, the bucket name can be neither starting with '-' nor ending with '-'")}const a=e.headers=B(e.headers);return _(e,["acl","grantFullControl","grantRead","grantReadAcp","grantWrite","grantWriteAcp","storageClass","azRedundancy","bucketType"]),(null==e?void 0:e.projectName)&&_(e,["projectName"]),await this.fetchBucket(e.bucket,"PUT",{},a)}async function gt(e){return this.fetchBucket(e,"DELETE",{},{})}async function xt(e){return this.fetchBucket(e,"HEAD",{},{},void 0,{handleResponse:e=>({...e.headers,ProjectName:e.headers[Z.HeaderProjectName]})})}async function bt(e){const{bucket:t,storageClass:a}=e;return this.fetchBucket(t,"PUT",{storageClass:""},{"x-tos-storage-class":a})}async function kt(e){const t={};return e.acl&&(t["x-tos-acl"]=e.acl),await this.fetchBucket(e.bucket,"PUT",{acl:""},t,e.aclBody,{needMd5:!0})}async function vt(e){const t=await this.fetchBucket(e,"GET",{acl:""},{});return S(t.data)("Grants"),t}async function Tt(e){return St.call(this,e)}async function St(e){const t=(e=this.normalizeObjectInput(e)).headers=B(e.headers);_(e,["contentLength","contentMD5","contentSHA256","cacheControl","contentDisposition","contentEncoding","contentLanguage","contentType","expires","acl","grantFullControl","grantRead","grantReadAcp","grantWrite","grantWriteAcp","ssecAlgorithm","ssecKey","ssecKeyMD5","serverSideEncryption","serverSideDataEncryption","meta","websiteRedirectLocation","storageClass","trafficLimit","callback","callbackVar","forbidOverwrite","ifMatch"]),this.setObjectContentTypeHeader(e,t);const a=te(e.body,t),o=null!=a;o||!e.dataTransferStatusChange&&!e.progress||console.warn("Don't get totalSize of putObject's body, the `dataTransferStatusChange` and `progress` callback will not trigger. You can use `putObjectFromFile` instead");let n=0;const{dataTransferStatusChange:i,progress:s}=e,r=(e,t=0)=>{if(!o||t<0)return;if(!i&&!s)return;n+=t,null==i||i({type:e,rwOnceBytes:t,consumedBytes:n,totalBytes:a});const r=0===a?e===exports.DataTransferType.Succeed?1:0:n/a;1===r?e===exports.DataTransferType.Succeed&&(null==s||s(r)):null==s||s(r)},c=await ae({body:e.body,dataTransferCallback:e=>r(exports.DataTransferType.Rw,e),makeRetryStream:e.makeRetryStream,enableCRC:this.opts.enableCRC,rateLimiter:e.rateLimiter});r(exports.DataTransferType.Started);const[p,l]=await P((async()=>{const a=await this._fetchObject(e,"PUT",{},t,c.body||"",{handleResponse:t=>{var a;const o={...t.headers};return null!=(a=e)&&a.callback&&t.data&&(o.CallbackResult=""+JSON.stringify(t.data)),o},axiosOpts:{__retryConfig__:{beforeRetry:()=>{n=0,null==c.beforeRetry||c.beforeRetry()},makeRetryStream:c.makeRetryStream},onUploadProgress:e=>{r(exports.DataTransferType.Rw,e.loaded-n)}}});return this.opts.enableCRC&&c.crc&&q(c.crc,a.headers),a})());if(p||!l)throw r(exports.DataTransferType.Failed),p;return r(exports.DataTransferType.Succeed),l}async function wt(e){throw B(e.headers),new v("putObjectFromFile doesn't support in browser environment")}async function Ct(e){const t=e.headers=B(e.headers);return _(e,["acl","grantFullControl","grantRead","grantReadAcp","grantWriteAcp","ssecAlgorithm","ssecKey","ssecKeyMD5","meta","storageClass"]),await this._fetchObject(e,"POST",{fetch:""},t,{URL:e.url,IgnoreSameKey:e.ignoreSameKey,ContentMD5:e.contentMD5},{needMd5:!0})}async function Et(e){const t=e.headers=B(e.headers);return _(e,["acl","grantFullControl","grantRead","grantReadAcp","grantWriteAcp","ssecAlgorithm","ssecKey","ssecKeyMD5","meta","storageClass"]),await this._fetchObject(e,"POST",{fetchTask:""},t,{URL:e.url,IgnoreSameKey:e.ignoreSameKey,ContentMD5:e.contentMD5,Object:e.key},{needMd5:!0})}function Rt(e){ee(e);const t="string"==typeof e?{key:e}:e,a=t.alternativeEndpoint||this.opts.endpoint,o=!t.alternativeEndpoint&&!t.isCustomDomain,n=t.bucket||this.opts.bucket||"";if(o&&!n)throw new v("Must provide bucket param");const[i,s,r]=(()=>{const e=encodeURIComponent(t.key),i=t.key.split("/").map(e=>encodeURIComponent(e)).join("/");return o?[`${n}.${a}`,"/"+i,"/"+e]:[a,"/"+i,"/"+e]})(),c=t.query||{},p=(e,t)=>{null==c[e]&&null!=t&&(c[e]=t)},l=t.response||{};Object.keys(l).forEach(e=>{const t=e,a=C(t);p("response-"+a,l[t])}),t.versionId&&p("versionId",t.versionId);const u=this.getSignatureQuery({bucket:n,method:t.method||"GET",path:r,endpoint:a,subdomain:o,expires:t.expires||1800,query:c}),d=I(this.opts.proxy);let h=`http${this.opts.secure?"s":""}://${i}`;return null!=d&&d.url&&(h=d.url.replace(/\/+$/g,""),null!=d&&d.needProxyParams&&(u["x-proxy-tos-host"]=i)),`${h}${s}?${Object.keys(u).map(e=>`${encodeURIComponent(e)}=${encodeURIComponent(u[e])}`).join("&")}`}async function Bt(e){const t="string"==typeof e?{key:e}:e,a={};return t.versionId&&(a.versionId=t.versionId),t.skipTrash&&(a.skipTrash=t.skipTrash),t.recursive&&(a.recursive=t.recursive),await this._fetchObject(e,"DELETE",a,{},{},{handleResponse:e=>e.headers})}async function It(e){return e.headers=e.headers||{},_(e,["recursiveMkdir","forbidOverwrite"]),this._fetchObject(e,"PUT",{rename:"",name:e.newKey},e.headers,"")}async function Pt(e){const t={Quiet:e.quiet,Objects:e.objects.map(e=>({Key:e.key,VersionId:e.versionId}))},a={delete:""};e.skipTrash&&(a.skipTrash=e.skipTrash),e.recursive&&(a.recursive=e.recursive);const o=await this.fetchBucket(e.bucket,"POST",a,{},t),n=S(o.data);return n("Deleted"),n("Error"),o}async function Ot(e){const t="string"==typeof e?{key:e}:e,a={acl:""};t.versionId&&(a.versionId=t.versionId);const o=await this._fetchObject(e,"GET",a,{});return S(o.data)("Grants"),o}async function Mt(e){const t=e.headers=B(e.headers),a={acl:""};return e.versionId&&(a.versionId=e.versionId),_(e,["acl"]),this._fetchObject(e,"PUT",a,t,e.aclBody)}async function Dt(e){return this._fetchObject(e,"DELETE",{uploadId:e.uploadId},{})}async function jt(e={}){const{...t}=e,a=await this.fetchBucket(e.bucket,"GET",{uploads:"",...C(t)},{}),o=S(a.data);return o("Uploads"),o("CommonPrefixes"),a}async function At(e){const t=e=this.normalizeObjectInput(e),a=e.headers=B(e.headers);_(e,["contentLength","cacheControl","contentDisposition","contentEncoding","contentLanguage","contentType","expires","acl","grantFullControl","grantRead","grantReadAcp","grantWriteAcp","meta","websiteRedirectLocation","storageClass","trafficLimit"]),this.setObjectContentTypeHeader(e,a);const o=te(e.body,a),n=null!=o;if(!n)throw new v("appendObject needs to know the content length in advance");if(a["content-length"]=a["content-length"]||""+o,this.opts.enableCRC&&0!==e.offset&&!e.preHashCrc64ecma)throw new v("must provide preHashCrc64ecma if enableCRC is true and offset is non-zero");let i=0;const{dataTransferStatusChange:s,progress:r}=e,c=(e,t=0)=>{if(!n||t<0)return;if(!s&&!r)return;i+=t,null==s||s({type:e,rwOnceBytes:t,consumedBytes:i,totalBytes:o});const a=0===o?e===exports.DataTransferType.Succeed?1:0:i/o;1===a?e===exports.DataTransferType.Succeed&&(null==r||r(a)):null==r||r(a)},p=await ae({body:e.body,dataTransferCallback:e=>c(exports.DataTransferType.Rw,e),makeRetryStream:void 0,enableCRC:this.opts.enableCRC,rateLimiter:e.rateLimiter});c(exports.DataTransferType.Started);const[l,u]=await P((async()=>{const n=await this._fetchObject(e,"POST",{append:"",offset:t.offset},a,p.body||"",{handleResponse:e=>({...e.headers,nextAppendOffset:+e.headers["x-tos-next-append-offset"],hashCrc64ecma:e.headers["x-tos-hash-crc64ecma"]}),axiosOpts:{__retryConfig__:{beforeRetry:()=>{i=0,null==p.beforeRetry||p.beforeRetry()},makeRetryStream:p.makeRetryStream},onUploadProgress:e=>{c(exports.DataTransferType.Rw,e.loaded-i)}}});return this.opts.enableCRC&&p.crc&&q(J(t.preHashCrc64ecma||"0",p.crc.getCrc64(),o),n.headers),n})());if(l||!u)throw c(exports.DataTransferType.Failed),l;return c(exports.DataTransferType.Succeed),u}async function Ut(e){const t="string"==typeof e?{key:e}:e,a=t.headers=B(t.headers);_(t,["cacheControl","contentDisposition","contentEncoding","contentLanguage","contentType","expires","meta"]);const o={metadata:""};return t.versionId&&(o.versionId=t.versionId),this._fetchObject(e,"POST",o,a)}async function Lt(e){ee(e),e=this.normalizeObjectInput(e);const{expiresIn:t=3600,key:a}=e,o=e.bucket||this.opts.bucket,n={...e.fields},i=[...e.conditions||[]];if(!o)throw new v("Must provide bucket param");const s=this.opts.accessKeySecret,r=new Date,c=_t({date:new Date(r.valueOf()+1e3*t),type:"ISO"}),p=_t(),l=p.substring(0,8),u="tos",d="request",h=de(s,l),m=de(h,this.opts.region),f=de(m,u),y=de(f,d),g={key:a,"x-tos-algorithm":"TOS4-HMAC-SHA256","x-tos-date":p,"x-tos-credential":[this.opts.accessKeyId,l,this.opts.region,u,d].join("/")};this.opts.stsToken&&(g["x-tos-security-token"]=this.opts.stsToken),i.push({bucket:o}),Object.entries(g).forEach(([e,t])=>{n[e]=t}),Object.entries(n).forEach(([e,t])=>{i.push({[e]:""+t})});const x=JSON.stringify({expiration:c,conditions:i}),b=ye(fe(x,"utf-8"),"base64"),k=de(y,b,"hex");return n.policy=b,n["x-tos-signature"]=k,n}function _t(e){const{date:t=new Date,type:a="Z"}=e||{};return"ISO"===a?t.toISOString():t.toISOString().replace(/\..+/,"").replace(/-/g,"").replace(/:/g,"")+"Z"}const Nt={getBucketCustomDomain:!0,getBucketIntelligenttiering:!0,getBucketInventory:!0,listBucketInventory:!0,getBucketMirrorBack:!0,getBucketNotification:!0,getBucketPolicy:!0,getBucketRealTimeLog:!0,getBucketReplication:!0,getBucketTagging:!0,getBucketWebsite:!0};function zt(e,t){const{enableCatchEmptyServerError:a,methodKey:o,defaultResponse:n}=t;if(e instanceof k)if(a){if(404===e.statusCode)return z(n,e)}else if(void 0===a&&404===e.statusCode&&Nt[o])return z(n,e);throw e}async function qt(e){return!this.opts.enableOptimizeMethodBehavior&&void 0!==this.opts.enableOptimizeMethodBehavior||e.policy.Statement.length?await this.fetchBucket(e.bucket,"PUT",{policy:""},{},e.policy,{needMd5:!0}):Ft.call(this,e.bucket)}async function Kt(e){try{const t=await this.fetchBucket(e,"GET",{policy:""},{});return t.data.Statement.forEach(e=>{const t=S(e);Object.keys(e.Condition||{}).forEach(a=>{Object.keys(e.Condition[a]).forEach(e=>{t(`Condition["${a}"]["${e}"]`)})})}),t}catch(e){return zt(e,{enableCatchEmptyServerError:this.opts.enableOptimizeMethodBehavior,methodKey:"getBucketPolicy",defaultResponse:{Statement:[],Version:"2012-10-17"}})}}async function Ft(e){return this.fetchBucket(e,"DELETE",{policy:""},{})}async function Ht(e){return this.fetchBucket(e,"GET",{versioning:""},{})}async function Gt(e){return this.fetchBucket(e.bucket,"PUT",{versioning:""},{},{Status:e.status})}function $t(e){const t=Vt.call(this,e);Wt(e.conditions);const a=`http${this.opts.secure?"s":""}://${e.alternativeEndpoint||(e.isCustomDomain?this.opts.endpoint:`${t.bucket}.${this.opts.endpoint}`)}`,o=D(this.getSignatureQuery({bucket:t.bucket,expires:t.expires,policy:{conditions:t.conditions}}));return{getSignedURLForList:e=>{const t=D(e),n=[o,t].filter(Boolean).join("&");return`${a}?${n}`},getSignedURLForGetOrHead:(e,t)=>{const n=D(t),i=[o,n].filter(Boolean).join("&"),s=e.split("/").map(e=>encodeURIComponent(e)).join("/");return`${a}/${s}?${i}`},signedQuery:o}}function Vt(e){const t=e.bucket||this.opts.bucket;if(!t)throw new v("Must provide bucket param");Wt(e.conditions);const a=e.conditions.map(e=>[e.operator||"eq","$key",e.value]);return a.push(["eq","$bucket",t]),{bucket:t,expires:e.expires||3600,conditions:a}}function Wt(e){if(e.length<1)throw new v("The `conditions` field of `PreSignedPolicyURLInput` must has one item at least");for(const t of e){if("key"!==t.key)throw new v("The `key` field of `PolicySignatureCondition` must be `'key'`");if(t.operator&&"eq"!==t.operator&&"starts-with"!==t.operator)throw new v("The `operator` field of `PolicySignatureCondition` must be `'eq'` or `'starts-with'`")}}async function Qt(e){const{bucket:t}=e;return this.fetchBucket(t,"GET",{location:""},{})}async function Jt(e){try{const{bucket:t}=e;return await this.fetchBucket(t,"GET",{cors:""},{})}catch(e){return zt(e,{defaultResponse:{CORSRules:[]},enableCatchEmptyServerError:this.opts.enableOptimizeMethodBehavior,methodKey:"getBucketCORS"})}}async function Xt(e){const{bucket:t,CORSRules:a}=e;return this.opts.enableOptimizeMethodBehavior&&!a.length?Yt.call(this,{bucket:t}):this.fetchBucket(t,"PUT",{cors:""},{},{CORSRules:a})}async function Yt(e){const{bucket:t}=e;return this.fetchBucket(t,"DELETE",{cors:""},{})}async function Zt(e){const{bucket:t,rules:a}=e;if(this.opts.enableOptimizeMethodBehavior&&!a.length)return ta.call(this,{bucket:t});const o={};return _({...e,headers:o},["allowSameActionOverlap"]),this.fetchBucket(t,"PUT",{lifecycle:""},o,{Rules:a})}async function ea(e){try{const{bucket:t}=e;return await this.fetchBucket(t,"GET",{lifecycle:""},{},{},{handleResponse:e=>({AllowSameActionOverlap:e.headers["x-tos-allow-same-action-overlap"],Rules:e.data.Rules})})}catch(e){return zt(e,{enableCatchEmptyServerError:this.opts.enableOptimizeMethodBehavior,methodKey:"getBucketLifecycle",defaultResponse:{Rules:[]}})}}async function ta(e){const{bucket:t}=e;return this.fetchBucket(t,"DELETE",{lifecycle:""},{})}async function aa(e){const{bucket:t,rule:a}=e;return this.fetchBucket(t,"PUT",{encryption:""},{"Content-MD5":le(JSON.stringify({Rule:a}),"base64")},{Rule:a})}async function oa(e){const{bucket:t}=e;return this.fetchBucket(t,"GET",{encryption:""},{})}async function na(e){const{bucket:t}=e;return this.fetchBucket(t,"DELETE",{encryption:""},{})}async function ia(e){const{bucket:t,rules:a}=e;return this.opts.enableOptimizeMethodBehavior&&!a.length?ra.call(this,{bucket:t}):this.fetchBucket(t,"PUT",{mirror:""},{},{Rules:a})}async function sa(e){const{bucket:t}=e;try{return await this.fetchBucket(t,"GET",{mirror:""},{})}catch(e){return zt(e,{enableCatchEmptyServerError:this.opts.enableOptimizeMethodBehavior,methodKey:"getBucketMirrorBack",defaultResponse:{Rules:[]}})}}async function ra(e){const{bucket:t}=e;return this.fetchBucket(t,"DELETE",{mirror:""},{})}async function ca(e){const{tagSet:t,versionId:a}=e,o=B({versionId:a});return this._fetchObject(e,"PUT",{tagging:"",...o},{},{TagSet:t})}async function pa(e){const{versionId:t}=e,a=B({versionId:t}),o=await this._fetchObject(e,"GET",{tagging:"",...a},{});return S(o.data.TagSet)("Tags"),o}async function la(e){const{versionId:t}=e,a=B({versionId:t});return this._fetchObject(e,"DELETE",{tagging:"",...a},{})}async function ua(e){const{bucket:t,rules:a,role:o}=e;return this.opts.enableOptimizeMethodBehavior&&!a.length?ha.call(this,{bucket:t}):this.fetchBucket(t,"PUT",{replication:""},{},{Role:o,Rules:a})}async function da(e){const{bucket:t,progress:a,ruleId:o}=e,n={replication:"",progress:a||""};null!=o&&(n["rule-id"]=""+o);try{return await this.fetchBucket(t,"GET",n,{})}catch(e){return zt(e,{enableCatchEmptyServerError:this.opts.enableOptimizeMethodBehavior,methodKey:"getBucketReplication",defaultResponse:{Rules:[],Role:""}})}}async function ha(e){const{bucket:t}=e;return this.fetchBucket(t,"DELETE",{replication:""},{})}async function ma(e){const{bucket:t,...a}=e,o=E(a);return this.fetchBucket(t,"PUT",{website:""},{},{...o})}async function fa(e){const{bucket:t}=e;try{return this.fetchBucket(t,"GET",{website:""},{})}catch(e){return zt(e,{enableCatchEmptyServerError:this.opts.enableOptimizeMethodBehavior,methodKey:"getBucketWebsite",defaultResponse:{RoutingRules:[]}})}}async function ya(e){const{bucket:t}=e;return this.fetchBucket(t,"DELETE",{website:""},{})}async function ga(e){const{bucket:t,...a}=e,o=E(a);return this.fetchBucket(t,"PUT",{notification:""},{},{...o})}async function xa(e){const{bucket:t}=e;try{return await this.fetchBucket(t,"GET",{notification:""},{})}catch(e){return zt(e,{enableCatchEmptyServerError:this.opts.enableOptimizeMethodBehavior,methodKey:"getBucketNotification",defaultResponse:{CloudFunctionConfigurations:[],RocketMQConfigurations:[]}})}}async function ba(e){const{bucket:t,...a}=e,o=E(a);return this.fetchBucket(t,"PUT",{customdomain:""},{},{...o})}async function ka(e){try{const{bucket:t}=e;return await this.fetchBucket(t,"GET",{customdomain:""},{})}catch(e){return zt(e,{defaultResponse:{CustomDomainRules:[]},enableCatchEmptyServerError:this.opts.enableOptimizeMethodBehavior,methodKey:"getBucketCustomDomain"})}}async function va(e){const{bucket:t,customDomain:a}=e;return this.fetchBucket(t,"DELETE",{customdomain:a},{})}async function Ta(e){const{bucket:t,...a}=e,o=E(a);return this.fetchBucket(t,"PUT",{realtimeLog:""},{},{...o})}async function Sa(e){const{bucket:t}=e;try{return await this.fetchBucket(t,"GET",{realtimeLog:""},{})}catch(e){return zt(e,{enableCatchEmptyServerError:this.opts.enableOptimizeMethodBehavior,methodKey:"getBucketRealTimeLog",defaultResponse:{}})}}async function wa(e){const{bucket:t}=e;return this.fetchBucket(t,"DELETE",{realtimeLog:""},{})}var Ca,Ea,Ra,Ba,Ia,Pa,Oa;async function Ma(e){try{return await this.fetchBucket(e.bucket,"GET",{inventory:"",id:e.id},{})}catch(e){return zt(e,{enableCatchEmptyServerError:this.opts.enableOptimizeMethodBehavior,methodKey:"getBucketInventory",defaultResponse:void 0})}}async function Da(e){const t={inventory:"",...e.continuationToken?{"continuation-token":e.continuationToken}:null};try{return await this.fetchBucket(e.bucket,"GET",t,{})}catch(e){return zt(e,{enableCatchEmptyServerError:this.opts.enableOptimizeMethodBehavior,methodKey:"listBucketInventory",defaultResponse:{InventoryConfigurations:[]}})}}async function ja(e){return this.fetchBucket(e.bucket,"DELETE",{inventory:"",id:e.id},{})}function Aa(e){return this.fetchBucket(e.bucket,"PUT",{inventory:"",id:e.inventoryConfiguration.Id},{},e.inventoryConfiguration)}async function Ua(e){const{accountId:t,...a}=e,o=E(a);return await this.fetch("POST","/jobs",{},{"x-tos-account-id":t},{...o})}async function La(e){const{accountId:t,maxResults:a=1e3,...o}=e;return await this.fetch("GET","/jobs",{maxResults:a,...o},{"x-tos-account-id":t},{},{axiosOpts:{paramsSerializer:N}})}async function _a(e){const{accountId:t,jobId:a,priority:o}=e;return await this.fetch("POST",`/jobs/${a}/priority`,{priority:o},{"x-tos-account-id":t},{},{needMd5:!0})}async function Na(e){const{accountId:t,jobId:a,requestedJobStatus:o,statusUpdateReason:n}=e;return await this.fetch("POST",`/jobs/${a}/status`,{requestedJobStatus:o,statusUpdateReason:n},{"x-tos-account-id":t},{},{needMd5:!0})}async function za(e){const{accountId:t,JobId:a}=e;return await this.fetch("DELETE","/jobs/"+a,{},{"x-tos-account-id":t},{})}async function qa(e){const{accountId:t,JobId:a}=e;return await this.fetch("GET","/jobs/"+a,{},{"x-tos-account-id":t},{})}async function Ka(e){return await this.fetchBucket(e.bucket,"PUT",{tagging:""},{},e.tagging,{needMd5:!0})}async function Fa({bucket:e}){try{return await this.fetchBucket(e,"GET",{tagging:""},{})}catch(e){return zt(e,{enableCatchEmptyServerError:this.opts.enableOptimizeMethodBehavior,methodKey:"getBucketTagging",defaultResponse:{TagSet:{Tags:[]}}})}}async function Ha({bucket:e}){return this.fetchBucket(e,"DELETE",{tagging:""},{})}async function Ga(e){return await this.fetchBucket(e.bucket,"PUT",{payByTraffic:""},{},e.payByTraffic)}async function $a({bucket:e}){return await this.fetchBucket(e,"GET",{payByTraffic:""},{})}async function Va(e){const{bucket:t}=e;try{return await this.fetchBucket(t,"GET",{imageStyleBriefInfo:""},{})}catch(e){if(e instanceof k&&404===e.statusCode)return this.getNormalDataFromError({BucketName:t,ImageStyleBriefInfo:[]},e);throw e}}async function Wa(e){try{return await this.fetchBucket(e,"GET",{imageStyle:""},{})}catch(e){if(e instanceof k&&404===e.statusCode)return this.getNormalDataFromError({ImageStyles:[]},e);throw e}}async function Qa(e){try{const{bucket:t,styleName:a}=e;return await this.fetchBucket(t,"GET",{imageStyleContent:"",styleName:a},{})}catch(e){if(e instanceof k&&404===e.statusCode)return this.getNormalDataFromError({ImageStyles:[]},e);throw e}}async function Ja(e,t){try{return await this.fetchBucket(e,"GET",{imageStyle:"",styleName:t},{})}catch(e){if(e instanceof k&&404===e.statusCode)return this.getNormalDataFromError(null,e);throw e}}async function Xa(e){const{bucket:t,styleName:a,content:o,styleObjectPrefix:n}=e;try{return await this.fetchBucket(t,"PUT",{imageStyle:"",styleName:a,styleObjectPrefix:n},{},{Content:o})}catch(e){if(e instanceof k&&404===e.statusCode)return this.getNormalDataFromError(null,e);throw e}}async function Ya(e){const{styleName:t,styleObjectPrefix:a,bucket:o}=e;try{return await this.fetchBucket(o,"DELETE",{imageStyle:"",styleName:t,styleObjectPrefix:a},{})}catch(e){if(e instanceof k&&404===e.statusCode)return this.getNormalDataFromError(null,e);throw e}}async function Za(e,t){try{return await this.fetchBucket(e,"PUT",{originalImageProtect:""},{},t)}catch(e){if(e instanceof k&&404===e.statusCode)return this.getNormalDataFromError(null,e);throw e}}async function eo(e){try{return await this.fetchBucket(e,"GET",{originalImageProtect:""},{})}catch(e){if(e instanceof k&&404===e.statusCode)return this.getNormalDataFromError(null,e);throw e}}async function to(e){const{bucket:t,Separator:a,SeparatorSuffix:o}=e;try{return await this.fetchBucket(t,"PUT",{imageStyleSeparator:""},{},{Separator:a,SeparatorSuffix:o})}catch(e){if(e instanceof k&&404===e.statusCode)return this.getNormalDataFromError(null,e);throw e}}async function ao(e){try{return await this.fetchBucket(e,"GET",{imageStyleSeparator:""},{})}catch(e){if(e instanceof k&&404===e.statusCode)return this.getNormalDataFromError(null,e);throw e}}async function oo(e){try{return await this.fetchBucket(e,"GET",{intelligenttiering:""},{})}catch(e){return zt(e,{enableCatchEmptyServerError:this.opts.enableOptimizeMethodBehavior,methodKey:"getBucketIntelligenttiering",defaultResponse:{}})}}async function no(e){const{bucket:t,...a}=e,o=E(a);return this.fetchBucket(t,"PUT",{rename:""},{},{...o})}async function io(e){const{bucket:t}=e;return await this.fetchBucket(t,"GET",{rename:""},{})}async function so(e){const{bucket:t}=e;return this.fetchBucket(t,"DELETE",{rename:""},{})}async function ro(e){const{versionId:t,...a}=e,o={restore:""};t&&(o.versionId=t);const n=E(a);return this._fetchObject(e,"POST",o,{},n)}async function co(e){const{accountId:t}=e;return await this.fetch("GET","/storagelens",{},{"x-tos-account-id":t},{},{axiosOpts:{paramsSerializer:N}})}async function po(e){const{accountId:t,Id:a}=e;return await this.fetch("DELETE","/storagelens",{id:a},{"x-tos-account-id":t},{},{needMd5:!0})}async function lo(e){const{accountId:t,Id:a}=e;return await this.fetch("GET","/storagelens",{id:a},{"x-tos-account-id":t},{},{needMd5:!0})}async function uo(e){const{accountId:t,Id:a,...o}=e;return await this.fetch("PUT","/storagelens",{id:a},{"x-tos-account-id":t},{...o,Id:a},{needMd5:!0})}async function ho(e){const{bucket:t,...a}=e,o=E(a);return this.fetchBucket(t,"PUT",{notification_v2:""},{},{...o})}async function mo(e){const{bucket:t}=e;try{return await this.fetchBucket(t,"GET",{notification_v2:""},{})}catch(e){return zt(e,{enableCatchEmptyServerError:this.opts.enableOptimizeMethodBehavior,methodKey:"getBucketNotificationType2",defaultResponse:{Rules:[]}})}}async function fo(e){return yo.call(this,e)}async function yo(e){const t=e.headers=B(e.headers);return _(e,["symLinkTargetKey","symLinkTargetBucket","forbidOverwrite","acl","storageClass","meta"]),this._fetchObject(e,"PUT",{symlink:""},t,void 0,{handleResponse(e){const{headers:t}=e;return{VersionID:t["x-tos-version-id"]}}})}async function go(e){return xo.call(this,e)}async function xo(e){const t={symlink:""};return e.versionId&&(t.versionId=e.versionId),this._fetchObject(e,"GET",t,{},void 0,{handleResponse:e=>{const{headers:t}=e;return{VersionID:t["x-tos-version-id"],SymlinkTargetKey:t["x-tos-symlink-target"],SymlinkTargetBucket:t["x-tos-symlink-bucket"],LastModified:t["last-modified"]}}})}async function bo(e){const{bucket:t,...a}=e,o=E(a);return this.fetchBucket(t,"PUT",{transferAcceleration:""},{},{...o})}async function ko(e){try{const{bucket:t}=e,a={};return e.getStatus&&(a["x-tos-get-bucket-acceleration-status"]="true"),await this.fetchBucket(t,"GET",{transferAcceleration:""},a)}catch(e){return zt(e,{enableCatchEmptyServerError:this.opts.enableOptimizeMethodBehavior,methodKey:"getBucketTransferAcceleration",defaultResponse:{TransferAccelerationConfiguration:{Enabled:"false",Status:exports.TransferAccelerationStatusType.Terminated}}})}}async function vo(e){const{bucket:t,status:a}=e;return this.fetchBucket(t,"PUT",{accessmonitor:""},{},{Status:a})}async function To(e){try{const{bucket:t}=e;return await this.fetchBucket(t,"GET",{accessmonitor:""},{})}catch(e){return zt(e,{enableCatchEmptyServerError:this.opts.enableOptimizeMethodBehavior,methodKey:"getBucketAccessMonitor",defaultResponse:{}})}}async function So(e){const{accountId:t}=e;return await this.fetch("GET","/qospolicy",{},{"x-tos-account-id":t},{},{})}async function wo(e){const{accountId:t,...a}=e;return await this.fetch("PUT","/qospolicy",{},{"x-tos-account-id":t},{...a},{})}async function Co(e){const{accountId:t}=e;return await this.fetch("DELETE","/qospolicy",{},{"x-tos-account-id":t},{},{})}async function Eo(e){const{accountId:t,name:a,regions:o}=e;return await this.fetch("POST","/mrap",{name:a},{"x-tos-account-id":t},{Name:a,Regions:o},{})}async function Ro(e){const{name:t,accountId:a}=e;return await this.fetch("GET","/mrap",{name:t},{"x-tos-account-id":a},{},{})}async function Bo(e){const{accountId:t,...a}=e;return await this.fetch("GET","/mrap",{...a},{"x-tos-account-id":t},{},{})}async function Io(e){const{accountId:t,alias:a}=e;return await this.fetch("GET","/mrap/routes",{alias:a},{"x-tos-account-id":t})}async function Po(e){const{name:t,accountId:a}=e;return await this.fetch("DELETE","/mrap",{name:t},{"x-tos-account-id":a})}async function Oo(e){const{routes:t,accountId:a,alias:o}=e;return await this.fetch("PATCH","/mrap/routes",{alias:o},{"x-tos-account-id":a},{Routes:t})}!function(e){e.Daily="Daily",e.Weekly="Weekly"}(Ca||(Ca={})),function(e){e.All="All",e.Current="Current"}(Ea||(Ea={})),function(e){e.Size="Size",e.LastModifiedDat="LastModifiedDate",e.ETag="ETag",e.StorageClass="StorageClass",e.IsMultipartUploaded="IsMultipartUploaded",e.EncryptionStatus="EncryptionStatus",e.CRC64="CRC64",e.ReplicationStatus="ReplicationStatus"}(Ra||(Ra={})),function(e){e.StringEquals="StringEquals",e.StringNotEquals="StringNotEquals",e.StringEqualsIgnoreCase="StringEqualsIgnoreCase",e.StringNotEqualsIgnoreCase="StringNotEqualsIgnoreCase",e.StringLike="StringLike",e.StringNotLike="StringNotLike"}(Ba||(Ba={})),function(e){e.DateEquals="DateEquals",e.DateNotEquals="DateNotEquals",e.DateLessThan="DateLessThan",e.DateLessThanEquals="DateLessThanEquals",e.DateGreaterThan="DateGreaterThan",e.DateGreaterThanEquals="DateGreaterThanEquals"}(Ia||(Ia={})),function(e){e.IpAddress="IpAddress",e.NotIpAddress="NotIpAddress"}(Pa||(Pa={})),function(e){e.WritesQps="WritesQps",e.ReadsQps="ReadsQps",e.ListQps="ListQps",e.WritesRate="WritesRate",e.ReadsRate="ReadsRate"}(Oa||(Oa={}));const Mo=async function(e){const{accountId:t,alias:a,rules:o}=e;return this.opts.enableOptimizeMethodBehavior&&!o.length?jo.call(this,{accountId:t,alias:a}):await this.fetch("PUT","/mrap/mirror",{alias:a},{"x-tos-account-id":t},{Rules:o},{handleResponse:()=>({})})},Do=async function(e){const{accountId:t,alias:a}=e;try{const e=await this.fetch("GET","/mrap/mirror",{alias:a},{"x-tos-account-id":t},{},{});return S(e.data)("Rules"),e}catch(e){return zt(e,{enableCatchEmptyServerError:this.opts.enableOptimizeMethodBehavior,methodKey:"getMultiRegionAccessPointMirrorBack",defaultResponse:{Rules:[]}})}},jo=async function(e){const{accountId:t,alias:a}=e;return await this.fetch("DELETE","/mrap/mirror",{alias:a},{"x-tos-account-id":t},{},{handleResponse:()=>({})})};async function Ao(e){const{bucket:t,enable:a}=e;return await this.fetchBucket(t,"PUT",{privateM3U8:""},{},{Enable:a})}async function Uo(e){const{bucket:t}=e;try{return await this.fetchBucket(t,"GET",{privateM3U8:""},{})}catch(e){return zt(e,{enableCatchEmptyServerError:this.opts.enableOptimizeMethodBehavior,methodKey:"getBucketPrivateM3U8",defaultResponse:{Enable:!1}})}}async function Lo(e){const{bucket:t,...a}=e,o=E(a);return this.fetchBucket(t,"PUT",{trash:""},{},{...o})}async function _o(e){const{bucket:t}=e;return await this.fetchBucket(t,"GET",{trash:""},{})}class No extends ct{constructor(...e){super(...e),this.createBucket=yt,this.headBucket=xt,this.deleteBucket=gt,this.listBuckets=ft,this.getBucketLocation=Qt,this.putBucketStorageClass=bt,this.getBucketAcl=vt,this.putBucketAcl=kt,this.getBucketPolicy=Kt,this.putBucketPolicy=qt,this.deleteBucketPolicy=Ft,this.getBucketVersioning=Ht,this.putBucketVersioning=Gt,this.getBucketCORS=Jt,this.putBucketCORS=Xt,this.deleteBucketCORS=Yt,this.putBucketLifecycle=Zt,this.getBucketLifecycle=ea,this.deleteBucketLifecycle=ta,this.putBucketEncryption=aa,this.getBucketEncryption=oa,this.deleteBucketEncryption=na,this.putBucketMirrorBack=ia,this.getBucketMirrorBack=sa,this.deleteBucketMirrorBack=ra,this.putBucketReplication=ua,this.getBucketReplication=da,this.deleteBucketReplication=ha,this.putBucketWebsite=ma,this.getBucketWebsite=fa,this.deleteBucketWebsite=ya,this.putBucketNotification=ga,this.getBucketNotification=xa,this.putBucketCustomDomain=ba,this.getBucketCustomDomain=ka,this.deleteBucketCustomDomain=va,this.putBucketRealTimeLog=Ta,this.getBucketRealTimeLog=Sa,this.deleteBucketRealTimeLog=wa,this.getBucketInventory=Ma,this.listBucketInventory=Da,this.putBucketInventory=Aa,this.deleteBucketInventory=ja,this.putBucketTagging=Ka,this.getBucketTagging=Fa,this.deleteBucketTagging=Ha,this.putBucketPayByTraffic=Ga,this.getBucketPayByTraffic=$a,this.getBucketImageStyle=Ja,this.getBucketImageStyleList=Wa,this.getBucketImageStyleListByName=Qa,this.getImageStyleBriefInfo=Va,this.deleteBucketImageStyle=Ya,this.putBucketImageStyle=Xa,this.putBucketImageStyleSeparator=to,this.putBucketImageProtect=Za,this.getBucketImageProtect=eo,this.getBucketImageStyleSeparator=ao,this.putBucketRename=no,this.getBucketRename=io,this.deleteBucketRename=so,this.putBucketTransferAcceleration=bo,this.getBucketTransferAcceleration=ko,this.copyObject=Ge,this.resumableCopyObject=Ve,this.deleteObject=Bt,this.deleteMultiObjects=Pt,this.getObject=Qe,this.getObjectV2=Xe,this.getObjectToFile=Ye,this.getObjectAcl=Ot,this.headObject=Fe,this.appendObject=At,this.listObjects=lt,this.renameObject=It,this.fetchObject=Ct,this.putFetchTask=Et,this.listObjectsType2=dt,this.listObjectVersions=ut,this.putObject=Tt,this.putObjectFromFile=wt,this.putObjectAcl=Mt,this.setObjectMeta=Ut,this.createMultipartUpload=G,this.uploadPart=xe,this.uploadPartFromFile=be,this.completeMultipartUpload=ke,this.abortMultipartUpload=Dt,this.uploadPartCopy=He,this.listMultipartUploads=jt,this.listParts=V,this.downloadFile=et,this.putObjectTagging=ca,this.getObjectTagging=pa,this.deleteObjectTagging=la,this.listJobs=La,this.createJob=Ua,this.deleteJob=za,this.describeJob=qa,this.updateJobStatus=Na,this.updateJobPriority=_a,this.restoreObject=ro,this.uploadFile=Se,this.getPreSignedUrl=Rt,this.calculatePostSignature=Lt,this.preSignedPostSignature=Lt,this.preSignedPolicyURL=$t,this.getBucketIntelligenttiering=oo,this.listStorageLens=co,this.deleteStorageLens=po,this.getStorageLens=lo,this.putStorageLens=uo,this.putBucketNotificationType2=ho,this.getBucketNotificationType2=mo,this.putSymlink=fo,this.getSymlink=go,this.putBucketAccessMonitor=vo,this.getBucketAccessMonitor=To,this.putQosPolicy=wo,this.getQosPolicy=So,this.deleteQosPolicy=Co,this.createMultiRegionAccessPoint=Eo,this.getMultiRegionAccessPoint=Ro,this.listMultiRegionAccessPoints=Bo,this.getMultiRegionAccessPointRoutes=Io,this.deleteMultiRegionAccessPoint=Po,this.submitMultiRegionAccessPointRoutes=Oo,this.putMultiRegionAccessPointMirrorBack=Mo,this.getMultiRegionAccessPointMirrorBack=Do,this.deleteMultiRegionAccessPointMirrorBack=jo,this.putBucketPrivateM3U8=Ao,this.getBucketPrivateM3U8=Uo,this.putBucketTrash=Lo,this.getBucketTrash=_o}}const zo=a.CancelToken;class qo extends No{}qo.TosServerError=k,qo.isCancel=j,qo.CancelError=T,qo.TosServerCode=exports.TosServerCode,qo.TosClientError=v,qo.CancelToken=zo,qo.ACLType=exports.ACLType,qo.StorageClassType=exports.StorageClassType,qo.MetadataDirectiveType=exports.MetadataDirectiveType,qo.AzRedundancyType=exports.AzRedundancyType,qo.PermissionType=exports.PermissionType,qo.GranteeType=exports.GranteeType,qo.CannedType=exports.CannedType,qo.HttpMethodType=exports.HttpMethodType,qo.LifecycleStatusType=exports.LifecycleStatusType,qo.StatusType=exports.StatusType,qo.RedirectType=exports.RedirectType,qo.StorageClassInheritDirectiveType=exports.StorageClassInheritDirectiveType,qo.TierType=exports.TierType,qo.VersioningStatusType=exports.VersioningStatusType,qo.createDefaultRateLimiter=Y,qo.DataTransferType=exports.DataTransferType,qo.UploadEventType=exports.UploadEventType,qo.DownloadEventType=exports.DownloadEventType,qo.ResumableCopyEventType=exports.ResumableCopyEventType,qo.ReplicationStatusType=exports.ReplicationStatusType,qo.AccessPointStatusType=exports.AccessPointStatusType,qo.TransferAccelerationStatusType=exports.TransferAccelerationStatusType,qo.MRAPMirrorBackRedirectPolicyType=exports.MRAPMirrorBackRedirectPolicyType,qo.ShareLinkClient=mt,exports.CancelError=T,exports.CancelToken=zo,exports.ShareLinkClient=mt,exports.TOS=qo,exports.TosClient=qo,exports.TosClientError=v,exports.TosServerError=k,exports.createDefaultRateLimiter=Y,exports.default=qo,exports.isCancel=j;
//# sourceMappingURL=tos.cjs.production.min.js.map
