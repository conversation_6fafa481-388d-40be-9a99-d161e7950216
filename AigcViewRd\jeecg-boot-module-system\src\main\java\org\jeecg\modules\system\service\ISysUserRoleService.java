package org.jeecg.modules.system.service;

import java.util.Map;

import org.jeecg.modules.system.entity.SysUserRole;

import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 用户角色表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-21
 */
public interface ISysUserRoleService extends IService<SysUserRole> {

    /**
     * 添加用户角色关系
     * @param userId 用户ID
     * @param roleCode 角色编码
     */
    void addUserRole(String userId, String roleCode);

    /**
     * 添加用户角色关系
     * @param userId 用户ID
     * @param roleId 角色ID
     */
    void addUserRoleById(String userId, String roleId);
}
