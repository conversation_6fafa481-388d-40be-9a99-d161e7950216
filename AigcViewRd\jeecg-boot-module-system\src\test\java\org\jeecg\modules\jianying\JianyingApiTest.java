package org.jeecg.modules.jianying;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.JeecgSystemApplication;
import org.jeecg.modules.jianying.aspect.JianyingAccessKeyAspect;
import org.jeecg.modules.jianying.dto.CreateDraftRequest;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;

/**
 * 剪映小助手API测试类
 * 测试access_key验证和参数验证功能
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
@Slf4j
@SpringBootTest(classes = JeecgSystemApplication.class)
@AutoConfigureWebMvc
@ActiveProfiles("test")
public class JianyingApiTest {
    
    @Autowired
    private MockMvc mockMvc;
    
    /**
     * 测试有效的access_key
     */
    @Test
    public void testValidAccessKey() throws Exception {
        CreateDraftRequest request = new CreateDraftRequest();
        request.setAccessKey(JianyingAccessKeyAspect.getExpectedAccessKey());
        request.setZjHeight(1080);
        request.setZjWidth(1920);
        request.setZjUserId(12345);
        
        String requestJson = JSONObject.toJSONString(request);
        log.info("测试有效access_key - 请求参数: {}", requestJson);
        
        mockMvc.perform(MockMvcRequestBuilders.post("/api/jianying/create_draft")
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestJson))
                .andDo(print())
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.jsonPath("$.success").value(true));
    }
    
    /**
     * 测试无效的access_key
     */
    @Test
    public void testInvalidAccessKey() throws Exception {
        CreateDraftRequest request = new CreateDraftRequest();
        request.setAccessKey("invalid_access_key");
        request.setZjHeight(1080);
        request.setZjWidth(1920);
        request.setZjUserId(12345);
        
        String requestJson = JSONObject.toJSONString(request);
        log.info("测试无效access_key - 请求参数: {}", requestJson);
        
        mockMvc.perform(MockMvcRequestBuilders.post("/api/jianying/create_draft")
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestJson))
                .andDo(print())
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.jsonPath("$.success").value(false))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(401))
                .andExpect(MockMvcResultMatchers.jsonPath("$.message").value("访问被拒绝：access_key无效"));
    }
    
    /**
     * 测试缺少access_key
     */
    @Test
    public void testMissingAccessKey() throws Exception {
        CreateDraftRequest request = new CreateDraftRequest();
        // 不设置access_key
        request.setZjHeight(1080);
        request.setZjWidth(1920);
        request.setZjUserId(12345);
        
        String requestJson = JSONObject.toJSONString(request);
        log.info("测试缺少access_key - 请求参数: {}", requestJson);
        
        mockMvc.perform(MockMvcRequestBuilders.post("/api/jianying/create_draft")
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestJson))
                .andDo(print())
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.jsonPath("$.success").value(false))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(401))
                .andExpect(MockMvcResultMatchers.jsonPath("$.message").value("访问被拒绝：缺少access_key参数"));
    }
    
    /**
     * 测试参数验证 - 空access_key
     */
    @Test
    public void testEmptyAccessKey() throws Exception {
        CreateDraftRequest request = new CreateDraftRequest();
        request.setAccessKey(""); // 空字符串
        request.setZjHeight(1080);
        request.setZjWidth(1920);
        request.setZjUserId(12345);
        
        String requestJson = JSONObject.toJSONString(request);
        log.info("测试空access_key - 请求参数: {}", requestJson);
        
        mockMvc.perform(MockMvcRequestBuilders.post("/api/jianying/create_draft")
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestJson))
                .andDo(print())
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.jsonPath("$.success").value(false))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(401))
                .andExpect(MockMvcResultMatchers.jsonPath("$.message").value("访问被拒绝：缺少access_key参数"));
    }
    
    /**
     * 测试JSON格式错误
     */
    @Test
    public void testInvalidJsonFormat() throws Exception {
        String invalidJson = "{\"access_key\":\"test\",\"zj_height\":}"; // 无效JSON
        log.info("测试无效JSON格式 - 请求参数: {}", invalidJson);
        
        mockMvc.perform(MockMvcRequestBuilders.post("/api/jianying/create_draft")
                .contentType(MediaType.APPLICATION_JSON)
                .content(invalidJson))
                .andDo(print())
                .andExpect(MockMvcResultMatchers.status().isBadRequest());
    }
    
    /**
     * 测试Request对象的getSummary方法
     */
    @Test
    public void testRequestSummaryMethod() {
        CreateDraftRequest request = new CreateDraftRequest();
        request.setAccessKey(JianyingAccessKeyAspect.getExpectedAccessKey());
        request.setZjHeight(1080);
        request.setZjWidth(1920);
        request.setZjUserId(12345);
        
        String summary = request.getSummary();
        log.info("Request Summary: {}", summary);
        
        // 验证summary包含关键信息但隐藏敏感信息
        assert summary.contains("CreateDraftRequest");
        assert summary.contains("height=1080");
        assert summary.contains("width=1920");
        assert summary.contains("userId=12345");
        assert summary.contains("***"); // access_key应该被部分隐藏
        assert !summary.contains(JianyingAccessKeyAspect.getExpectedAccessKey()); // 完整access_key不应该出现
    }
    
    /**
     * 测试access_key验证工具方法
     */
    @Test
    public void testAccessKeyValidationMethods() {
        String validKey = JianyingAccessKeyAspect.getExpectedAccessKey();
        String invalidKey = "invalid_key";
        
        // 测试有效key
        assert JianyingAccessKeyAspect.isValidAccessKey(validKey);
        log.info("有效access_key验证通过: {}", validKey.substring(0, 10) + "***");
        
        // 测试无效key
        assert !JianyingAccessKeyAspect.isValidAccessKey(invalidKey);
        log.info("无效access_key验证失败: {}", invalidKey);
        
        // 测试null key
        assert !JianyingAccessKeyAspect.isValidAccessKey(null);
        log.info("null access_key验证失败");
        
        // 测试空字符串key
        assert !JianyingAccessKeyAspect.isValidAccessKey("");
        log.info("空字符串access_key验证失败");
    }
    
    /**
     * 测试不同API端点的access_key验证
     */
    @Test
    public void testDifferentApiEndpoints() throws Exception {
        String validAccessKey = JianyingAccessKeyAspect.getExpectedAccessKey();
        
        // 测试智界工具箱API
        CreateDraftRequest toolboxRequest = new CreateDraftRequest();
        toolboxRequest.setAccessKey(validAccessKey);
        toolboxRequest.setZjHeight(1080);
        toolboxRequest.setZjWidth(1920);
        
        mockMvc.perform(MockMvcRequestBuilders.post("/api/jianying/create_draft")
                .contentType(MediaType.APPLICATION_JSON)
                .content(JSONObject.toJSONString(toolboxRequest)))
                .andDo(print())
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.jsonPath("$.success").value(true));
        
        log.info("智界工具箱API access_key验证通过");
    }
}
