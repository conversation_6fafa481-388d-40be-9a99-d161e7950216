package org.jeecg.modules.demo.userrecord.entity;

import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 用户交易记录表
 * @Author: jeecg-boot
 * @Date:   2025-06-14
 * @Version: V2.0
 */
@Data
@TableName("aicg_user_transaction")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="aicg_user_transaction对象", description="用户交易记录表")
public class AicgUserRecord implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String id;

	/**用户ID*/
	@Excel(name = "用户ID", width = 15)
    @ApiModelProperty(value = "用户ID")
    private String userId;

	/**交易类型：1-消费，2-充值，3-退款，4-兑换*/
	@Excel(name = "交易类型", width = 15, dicCode = "transaction_type")
    @Dict(dicCode = "transaction_type")
    @ApiModelProperty(value = "交易类型：1-消费，2-充值，3-退款，4-兑换")
    private Integer transactionType;

	/**交易金额*/
	@Excel(name = "交易金额", width = 15)
    @ApiModelProperty(value = "交易金额")
    private BigDecimal amount;

	/**交易前余额*/
	@Excel(name = "交易前余额", width = 15)
    @ApiModelProperty(value = "交易前余额")
    private BigDecimal balanceBefore;

	/**交易后余额*/
	@Excel(name = "交易后余额", width = 15)
    @ApiModelProperty(value = "交易后余额")
    private BigDecimal balanceAfter;

	/**交易描述*/
	@Excel(name = "交易描述", width = 30)
    @ApiModelProperty(value = "交易描述")
    private String description;

	/**关联订单ID*/
    @ApiModelProperty(value = "关联订单ID")
    private String relatedOrderId;

	/**交易时间*/
	@Excel(name = "交易时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "交易时间")
    private Date transactionTime;

	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;

	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private Date createTime;

	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private String updateBy;

	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private Date updateTime;

	/**所属部门*/
    @ApiModelProperty(value = "所属部门")
    private String sysOrgCode;

    /**插件ID*/
    @Excel(name = "插件ID", width = 30)
    @ApiModelProperty(value = "插件ID，关联aigc_plub_shop.id")
    private String pluginId;

    /**插件唯一标识*/
    @Excel(name = "插件标识", width = 30)
    @ApiModelProperty(value = "插件唯一标识")
    private String pluginKey;

    /**插件名称*/
    @Excel(name = "插件名称", width = 30)
    @ApiModelProperty(value = "插件名称")
    private String pluginName;

    /**订单状态：1-待支付，2-已支付，3-已完成，4-已取消，5-已退款*/
    @Excel(name = "订单状态", width = 15, dicCode = "order_status")
    @Dict(dicCode = "order_status")
    @ApiModelProperty(value = "订单状态：1-待支付，2-已支付，3-已完成，4-已取消，5-已退款")
    private Integer orderStatus;

    /**订单类型：plugin-插件购买，membership-会员订阅，recharge-账户充值*/
    @Excel(name = "订单类型", width = 20)
    @ApiModelProperty(value = "订单类型：plugin-插件购买，membership-会员订阅，recharge-账户充值")
    private String orderType;

    /**产品信息JSON：存储商品详细信息*/
    @ApiModelProperty(value = "产品信息JSON：存储商品详细信息")
    private String productInfo;

    /**订单过期时间（用于15分钟倒计时）*/
    @Excel(name = "过期时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "订单过期时间（用于15分钟倒计时）")
    private Date expireTime;
}
