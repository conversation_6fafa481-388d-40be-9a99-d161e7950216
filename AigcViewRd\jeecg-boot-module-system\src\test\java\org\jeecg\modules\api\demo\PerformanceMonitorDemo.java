package org.jeecg.modules.api.demo;

import org.jeecg.modules.api.config.UserActivityConfig;
import org.jeecg.modules.api.service.IUserActivityPerformanceMonitor;
import org.jeecg.modules.api.service.impl.UserActivityPerformanceMonitorImpl;

import java.util.Map;
import java.util.List;
import java.util.Random;

/**
 * @Description: 性能监控和降级机制演示程序
 * @Author: AigcView
 * @Date: 2025-01-30
 * @Version: V1.0
 */
public class PerformanceMonitorDemo {

    private static final Random random = new Random();
    
    public static void main(String[] args) {
        System.out.println("=== 用户活跃状态性能监控和降级机制演示 ===\n");
        
        // 初始化配置和服务
        UserActivityConfig config = createTestConfig();
        UserActivityPerformanceMonitorImpl monitor = new UserActivityPerformanceMonitorImpl();
        
        // 使用反射设置配置（模拟Spring注入）
        try {
            java.lang.reflect.Field configField = UserActivityPerformanceMonitorImpl.class.getDeclaredField("userActivityConfig");
            configField.setAccessible(true);
            configField.set(monitor, config);
            monitor.init();
        } catch (Exception e) {
            System.err.println("初始化失败: " + e.getMessage());
            return;
        }
        
        // 演示1：基础性能监控
        demonstrateBasicPerformanceMonitoring(monitor);
        
        // 演示2：性能告警机制
        demonstratePerformanceAlerts(monitor);
        
        // 演示3：降级机制
        demonstrateDegradationMechanism(monitor);
        
        // 演示4：系统监控
        demonstrateSystemMonitoring(monitor);
        
        // 演示5：综合场景
        demonstrateComprehensiveScenario(monitor);
        
        System.out.println("\n=== 演示完成 ===");
    }
    
    /**
     * 演示基础性能监控功能
     */
    private static void demonstrateBasicPerformanceMonitoring(IUserActivityPerformanceMonitor monitor) {
        System.out.println("📊 演示1：基础性能监控功能");
        System.out.println("----------------------------------------");
        
        // 模拟不同API的调用
        String[] apis = {"/api/user/info", "/api/order/list", "/api/payment/create", "/api/data/export"};
        
        for (int i = 0; i < 20; i++) {
            String api = apis[random.nextInt(apis.length)];
            long responseTime = 200 + random.nextInt(800); // 200-1000ms
            boolean success = random.nextDouble() > 0.1; // 90%成功率
            boolean isCritical = api.contains("payment"); // 支付API为关键API
            
            monitor.recordApiPerformance(api, responseTime, isCritical, success);
        }
        
        // 显示统计结果
        System.out.println("系统整体性能统计：");
        Map<String, Object> systemStats = monitor.getSystemPerformanceStats();
        printStats(systemStats);
        
        System.out.println("\n各API性能统计：");
        for (String api : apis) {
            Map<String, Object> apiStats = monitor.getApiPerformanceStats(api);
            if ((Long) apiStats.get("totalCalls") > 0) {
                System.out.printf("  %s: 调用%d次, 平均响应时间%.1fms, 成功率%.1f%%\n",
                    api, apiStats.get("totalCalls"), apiStats.get("avgResponseTime"), apiStats.get("successRate"));
            }
        }
        
        System.out.println();
    }
    
    /**
     * 演示性能告警机制
     */
    private static void demonstratePerformanceAlerts(IUserActivityPerformanceMonitor monitor) {
        System.out.println("🚨 演示2：性能告警机制");
        System.out.println("----------------------------------------");
        
        // 模拟慢API调用触发告警
        monitor.recordApiPerformance("/api/slow/query", 1500L, false, true);
        monitor.recordApiPerformance("/api/critical/payment", 800L, true, true);
        monitor.recordApiPerformance("/api/very/slow", 2000L, false, false);
        
        List<Map<String, Object>> alerts = monitor.getPerformanceAlerts();
        System.out.printf("触发了 %d 个性能告警：\n", alerts.size());
        
        for (Map<String, Object> alert : alerts) {
            System.out.printf("  ⚠️  %s - API: %s, 响应时间: %dms, 阈值: %dms\n",
                alert.get("type"), alert.get("apiPath"), alert.get("responseTime"), alert.get("threshold"));
        }
        
        System.out.println();
    }
    
    /**
     * 演示降级机制
     */
    private static void demonstrateDegradationMechanism(IUserActivityPerformanceMonitor monitor) {
        System.out.println("⬇️ 演示3：降级机制");
        System.out.println("----------------------------------------");
        
        System.out.println("当前降级状态: " + (monitor.isDegraded() ? "已降级" : "正常"));
        
        // 手动触发降级
        System.out.println("手动触发系统降级...");
        boolean triggered = monitor.triggerDegradation("演示降级机制", 5);
        System.out.println("降级触发结果: " + (triggered ? "成功" : "失败"));
        
        // 检查降级状态
        Map<String, Object> degradationStatus = monitor.getDegradationStatus();
        System.out.println("降级状态详情:");
        printStats(degradationStatus);
        
        // 测试API降级判断
        System.out.println("\nAPI降级判断测试:");
        System.out.println("  普通API /api/normal 是否降级: " + monitor.shouldDegradeApi("/api/normal", false));
        System.out.println("  关键API /api/critical 是否降级: " + monitor.shouldDegradeApi("/api/critical", true));
        
        // 手动恢复
        System.out.println("\n手动恢复系统...");
        boolean recovered = monitor.recoverFromDegradation("演示完成");
        System.out.println("恢复结果: " + (recovered ? "成功" : "失败"));
        System.out.println("当前状态: " + (monitor.isDegraded() ? "已降级" : "正常"));
        
        System.out.println();
    }
    
    /**
     * 演示系统监控功能
     */
    private static void demonstrateSystemMonitoring(IUserActivityPerformanceMonitor monitor) {
        System.out.println("🖥️ 演示4：系统监控功能");
        System.out.println("----------------------------------------");
        
        // 系统负载
        Map<String, Object> systemLoad = monitor.getSystemLoad();
        System.out.println("系统负载:");
        System.out.printf("  CPU使用率: %.2f%%, 负载等级: %s\n", 
            systemLoad.get("cpuUsage"), systemLoad.get("loadLevel"));
        
        // JVM内存
        Map<String, Object> memoryInfo = monitor.getJvmMemoryInfo();
        System.out.println("JVM内存:");
        System.out.printf("  堆内存使用率: %.2f%%, 内存等级: %s\n", 
            memoryInfo.get("heapUsagePercentage"), memoryInfo.get("memoryLevel"));
        
        // 线程池
        Map<String, Object> threadPool = monitor.getThreadPoolStatus();
        System.out.println("线程池:");
        System.out.printf("  线程数: %d, 峰值: %d, 线程等级: %s\n", 
            threadPool.get("threadCount"), threadPool.get("peakThreadCount"), threadPool.get("threadLevel"));
        
        // 系统健康检查
        Map<String, Object> health = monitor.checkSystemHealth();
        System.out.println("系统健康状态: " + (health.size() > 0 ? "正常" : "异常"));
        
        System.out.println();
    }
    
    /**
     * 演示综合场景
     */
    private static void demonstrateComprehensiveScenario(IUserActivityPerformanceMonitor monitor) {
        System.out.println("🎯 演示5：综合场景 - 高负载下的自动降级");
        System.out.println("----------------------------------------");
        
        System.out.println("模拟高负载场景...");
        
        // 模拟大量慢API调用
        for (int i = 0; i < 10; i++) {
            long responseTime = 1200 + random.nextInt(800); // 1200-2000ms 超过阈值
            monitor.recordApiPerformance("/api/heavy/load", responseTime, false, true);
        }
        
        // 检查是否触发自动降级
        boolean shouldDegrade = monitor.shouldDegrade();
        System.out.println("系统是否需要降级: " + shouldDegrade);
        
        if (shouldDegrade) {
            System.out.println("⚠️ 检测到高负载，建议启动降级机制");
        }
        
        // 显示最近5分钟的性能统计
        Map<String, Object> recentStats = monitor.getRecentPerformanceStats(5);
        System.out.println("\n最近5分钟性能统计:");
        printStats(recentStats);
        
        // 重置统计数据
        System.out.println("\n重置性能统计数据...");
        boolean reset = monitor.resetPerformanceStats();
        System.out.println("重置结果: " + (reset ? "成功" : "失败"));
        
        Map<String, Object> statsAfterReset = monitor.getSystemPerformanceStats();
        System.out.printf("重置后调用次数: %d\n", statsAfterReset.get("totalCalls"));
        
        System.out.println();
    }
    
    /**
     * 创建测试配置
     */
    private static UserActivityConfig createTestConfig() {
        UserActivityConfig config = new UserActivityConfig();
        config.setEnabled(true);
        config.setPerformanceThreshold(1000L);
        config.setEnablePerformanceMonitoring(true);
        config.setEnableDegradation(true);
        return config;
    }
    
    /**
     * 打印统计信息
     */
    private static void printStats(Map<String, Object> stats) {
        for (Map.Entry<String, Object> entry : stats.entrySet()) {
            if (!entry.getKey().equals("timestamp")) {
                System.out.printf("  %s: %s\n", entry.getKey(), entry.getValue());
            }
        }
    }
}
