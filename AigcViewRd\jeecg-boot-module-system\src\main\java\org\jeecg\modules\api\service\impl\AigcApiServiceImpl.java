package org.jeecg.modules.api.service.impl;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;



import org.jeecg.modules.api.service.IAigcApiService;
import org.jeecg.modules.api.dto.PluginVerifyResult;
import org.jeecg.modules.api.entity.AicgApiLog;
import org.jeecg.modules.api.mapper.AicgApiLogMapper;
import org.jeecg.modules.demo.userprofile.entity.AicgUserProfile;
import org.jeecg.modules.demo.userprofile.service.IAicgUserProfileService;
import org.jeecg.modules.demo.plubshop.entity.AigcPlubShop;
import org.jeecg.modules.demo.plubshop.service.IAigcPlubShopService;
import org.jeecg.modules.demo.plubauthor.service.IAigcPlubAuthorService;
import org.jeecg.modules.api.util.SecurityUtil;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;

import lombok.extern.slf4j.Slf4j;

/**
 * @Description: 智界Aigc API服务实现类
 * @Author: jeecg-boot
 * @Date: 2025-06-14
 * @Version: V1.0
 */
@Service
@Slf4j
public class AigcApiServiceImpl implements IAigcApiService {

    @Autowired
    private IAicgUserProfileService userProfileService;

    @Autowired
    private IAigcPlubShopService plubShopService;

    @Autowired
    private IAigcPlubAuthorService plubAuthorService;

    @Autowired
    private AicgApiLogMapper apiLogMapper;

    @Autowired
    private JdbcTemplate jdbcTemplate;


    // 注意：HTML安全检查已统一使用SecurityUtil.checkHtmlSecurity()方法

    @Override
    public AicgUserProfile getUserByApiKey(String apiKey) {
        try {
            QueryWrapper<AicgUserProfile> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("api_key", apiKey);
            queryWrapper.eq("status", 1); // 只查询正常状态的用户
            
            AicgUserProfile userProfile = userProfileService.getOne(queryWrapper);
            if (userProfile != null) {
                log.debug("根据API-Key查询到用户: {}", userProfile.getUserId());
            }
            return userProfile;
            
        } catch (Exception e) {
            log.error("根据API-Key查询用户异常: {}", e.getMessage(), e);
            return null;
        }
    }

    @Override
    public boolean checkRateLimit(String userId) {
        // 移除频率限制，直接返回true允许所有请求
        log.debug("用户 {} 频率限制检查已禁用，允许请求", userId);
        return true;
    }

    @Override
    public void recordApiUsage(String userId, String apiType, String details) {
        try {
            // 记录API使用日志到数据库
            log.info("API使用记录 - 用户: {}, 类型: {}, 详情: {}", userId, apiType, details);

            // 创建API日志记录
            AicgApiLog apiLog = new AicgApiLog();
            apiLog.setUserId(userId);
            apiLog.setApiType(apiType);
            apiLog.setApiName(apiType);
            apiLog.setRequestTime(new Date());
            apiLog.setResponseTime(new Date());
            apiLog.setSuccess(true);
            apiLog.setStatusCode(200);
            apiLog.setCostPoints(BigDecimal.valueOf(0.01)); // 默认消耗
            apiLog.setDurationMs(100L); // 默认耗时
            apiLog.setCreateTime(new Date());

            // 保存到数据库
            apiLogMapper.insert(apiLog);

        } catch (Exception e) {
            log.error("记录API使用异常: {}", e.getMessage(), e);
        }
    }

    @Override
    public boolean verifySignature(String apiKey, String content, String timestamp, String signature) {
        try {
            String expectedSignature = SecurityUtil.generateSignature(apiKey, content, timestamp);
            return signature.equals(expectedSignature);
        } catch (Exception e) {
            log.error("验证签名异常: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public String checkHtmlSecurity(String htmlContent) {
        // 统一使用SecurityUtil中的安全检查方法（包含小红书白名单）
        return org.jeecg.modules.api.util.SecurityUtil.checkHtmlSecurity(htmlContent);
    }

    @Override
    public boolean deductBalance(String userId, BigDecimal amount, String description) {
        try {
            return userProfileService.consume(userId, amount, description, "api_system");
        } catch (Exception e) {
            log.error("扣减用户余额异常: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public Map<String, Object> getApiUsageStats(String userId, Date startDate, Date endDate) {
        Map<String, Object> stats = new HashMap<>();
        try {
            // TODO: 实现从数据库查询API使用统计
            stats.put("totalCalls", 0);
            stats.put("successCalls", 0);
            stats.put("failedCalls", 0);
            stats.put("totalCost", BigDecimal.ZERO);

            return stats;

        } catch (Exception e) {
            log.error("获取API使用统计异常: {}", e.getMessage(), e);
            return stats;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public PluginVerifyResult verifyPluginAndDeduct(String userId, String pluginKey) {
        try {
            log.info("开始验证插件调用权限，用户ID: {}, 插件标识: {}", userId, pluginKey);

            // 1. 查询插件信息
            AigcPlubShop plugin = plubShopService.getByPluginKey(pluginKey);
            if (plugin == null) {
                log.warn("插件不存在: {}", pluginKey);
                return PluginVerifyResult.failure("插件不存在: " + pluginKey);
            }

            // 2. 获取用户信息
            AicgUserProfile userProfile = userProfileService.getByUserId(userId);
            if (userProfile == null) {
                log.warn("用户不存在: {}", userId);
                return PluginVerifyResult.failure("用户不存在");
            }

            // 3. 检查用户余额是否足够
            BigDecimal currentBalance = userProfile.getAccountBalance();
            BigDecimal needAmount = plugin.getNeednum();

            if (currentBalance.compareTo(needAmount) < 0) {
                log.warn("用户余额不足，用户ID: {}, 当前余额: {}, 需要金额: {}", userId, currentBalance, needAmount);
                return PluginVerifyResult.insufficientBalance(plugin.getPlubname(), needAmount, currentBalance);
            }

            // 4. 扣除用户余额
            String description = "调用插件: " + plugin.getPlubname();
            boolean deductSuccess = userProfileService.consume(userId, needAmount, description, "api_system");
            if (!deductSuccess) {
                log.error("扣除用户余额失败，用户ID: {}, 金额: {}", userId, needAmount);
                return PluginVerifyResult.failure("扣费失败，请稍后重试");
            }

            // 5. 🔥 数据联动更新 - 扣费成功后进行完整的数据联动
            try {
                performDataLinkage(userId, plugin, needAmount);
                log.info("数据联动更新成功 - 插件: {} ({}), 用户: {}, 金额: {}",
                        plugin.getPlubname(), plugin.getPluginKey(), userId, needAmount);
            } catch (Exception e) {
                log.error("数据联动更新失败 - 插件: {}, 用户: {}", pluginKey, userId, e);
                // 不抛出异常，避免影响主流程
            }

            // 7. 计算扣费后余额
            BigDecimal balanceAfter = currentBalance.subtract(needAmount);

            log.info("插件调用验证成功，用户ID: {}, 插件: {}, 扣费: {}, 余额: {} -> {}",
                    userId, plugin.getPlubname(), needAmount, currentBalance, balanceAfter);

            return PluginVerifyResult.success(plugin.getPlubname(), needAmount, currentBalance, balanceAfter, plugin.getId());

        } catch (Exception e) {
            log.error("验证插件调用权限异常，用户ID: {}, 插件标识: {}", userId, pluginKey, e);
            return PluginVerifyResult.failure("系统异常，请稍后重试");
        }
    }

    /**
     * 🔥 数据联动更新 - 插件调用成功后的完整数据联动
     * 注意：改为public方法以确保@Transactional注解生效
     */
    @Transactional(rollbackFor = Exception.class)
    public void performDataLinkage(String userId, AigcPlubShop plugin, BigDecimal amount) {
        try {
            // 🔥 删除重复的交易记录生成，因为 consume() 方法已经生成了交易记录
            // generateTransactionRecord(userId, plugin, amount);

            // 1. 更新插件商城统计
            updatePluginShopStats(plugin.getId(), amount);

            // 2. 更新插件创作者统计
            updatePluginAuthorStats(plugin.getPlubwrite(), amount);

            // 3. 更新用户扩展统计
            updateUserProfileStats(userId, amount);

            log.info("数据联动更新成功 - 插件ID: {}, 创作者ID: {}, 用户ID: {}, 金额: {}",
                    plugin.getId(), plugin.getPlubwrite(), userId, amount);

        } catch (Exception e) {
            log.error("数据联动更新失败 - 插件: {}, 用户: {}, 错误: {}", plugin.getPluginKey(), userId, e.getMessage(), e);
            // 数据联动失败不影响主流程，只记录日志
        }
    }

    // 🔥 删除重复的交易记录生成方法，因为 consume() 方法已经处理了交易记录

    /**
     * 更新插件商城统计
     */
    private void updatePluginShopStats(String pluginId, BigDecimal amount) {
        String sql = "UPDATE aigc_plub_shop SET " +
                    "usernum = usernum + 1, " +                    // 总调用次数+1
                    "income = income + ?, " +                      // 总收益累加
                    "monthly_calls = monthly_calls + 1, " +        // 本月调用次数+1
                    "monthly_income = monthly_income + ?, " +      // 本月收益累加
                    "last_used_time = NOW(), " +                   // 更新最后使用时间
                    "update_time = NOW() " +                       // 更新修改时间
                    "WHERE id = ?";

        int result = jdbcTemplate.update(sql, amount, amount, pluginId);

        if (result > 0) {
            log.info("插件商城统计更新成功 - 插件ID: {}, 金额: {}", pluginId, amount);
        } else {
            log.error("插件商城统计更新失败 - 插件ID: {}, 可能插件不存在", pluginId);
        }
    }

    /**
     * 更新插件创作者统计
     */
    private void updatePluginAuthorStats(String authorId, BigDecimal amount) {
        if (authorId == null || authorId.trim().isEmpty()) {
            log.warn("创作者ID为空，跳过创作者统计更新");
            return;
        }

        String sql = "UPDATE aigc_plub_author SET " +
                    "plubusenum = plubusenum + 1, " +              // 总使用次数+1
                    "total_income = total_income + ?, " +          // 总收益累加
                    "monthly_calls = monthly_calls + 1, " +        // 本月调用次数+1
                    "monthly_income = monthly_income + ?, " +      // 本月收益累加
                    "last_active_time = NOW(), " +                 // 更新最后活跃时间
                    "update_time = NOW() " +                       // 更新修改时间
                    "WHERE id = ?";

        int result = jdbcTemplate.update(sql, amount, amount, authorId);

        if (result > 0) {
            log.info("创作者统计更新成功 - 创作者ID: {}, 金额: {}", authorId, amount);
        } else {
            log.error("创作者统计更新失败 - 创作者ID: {}, 可能创作者不存在", authorId);
        }
    }

    /**
     * 更新用户扩展统计
     * 注意：account_balance 和 total_consumption 已经在 deductBalance() 中更新了，这里只更新插件相关统计
     */
    private void updateUserProfileStats(String userId, BigDecimal amount) {
        String sql = "UPDATE aicg_user_profile SET " +
                    "plugin_call_count = plugin_call_count + 1, " +    // 插件调用次数+1
                    "monthly_plugin_cost = monthly_plugin_cost + ?, " + // 本月插件消费累加
                    "last_plugin_call_time = NOW(), " +                // 更新最后调用时间
                    "update_time = NOW() " +                           // 更新修改时间
                    "WHERE user_id = ?";

        int result = jdbcTemplate.update(sql, amount, userId);

        if (result > 0) {
            log.info("用户插件统计更新成功 - 用户ID: {}, 金额: {}", userId, amount);
        } else {
            log.error("用户插件统计更新失败 - 用户ID: {}, 可能用户不存在", userId);
        }
    }
}
