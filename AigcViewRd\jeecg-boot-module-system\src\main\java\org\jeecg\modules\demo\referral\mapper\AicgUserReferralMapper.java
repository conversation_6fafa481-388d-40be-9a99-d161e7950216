package org.jeecg.modules.demo.referral.mapper;

import java.util.List;
import java.util.Map;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.jeecg.modules.demo.referral.entity.AicgUserReferral;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: 用户推荐关系表
 * @Author: jeecg-boot
 * @Date:   2025-06-18
 * @Version: V1.0
 */
public interface AicgUserReferralMapper extends BaseMapper<AicgUserReferral> {

    /**
     * 根据推荐人ID查询推荐记录
     * @param referrerId 推荐人ID
     * @return 推荐记录列表
     */
    @Select("SELECT * FROM aicg_user_referral WHERE referrer_id = #{referrerId} ORDER BY create_time DESC")
    List<AicgUserReferral> getByReferrerId(@Param("referrerId") String referrerId);
    
    /**
     * 根据被推荐人ID查询推荐记录
     * @param refereeId 被推荐人ID
     * @return 推荐记录
     */
    @Select("SELECT * FROM aicg_user_referral WHERE referee_id = #{refereeId}")
    AicgUserReferral getByRefereeId(@Param("refereeId") String refereeId);
    
    /**
     * 根据推荐码查询推荐记录
     * @param referralCode 推荐码
     * @return 推荐记录
     */
    @Select("SELECT * FROM aicg_user_referral WHERE referral_code = #{referralCode}")
    AicgUserReferral getByReferralCode(@Param("referralCode") String referralCode);
    
    /**
     * 统计推荐人的推荐数据
     * @param referrerId 推荐人ID
     * @return 推荐统计数据
     */
    @Select("SELECT " +
            "COUNT(*) as total_referrals, " +
            "COUNT(CASE WHEN status = 3 THEN 1 END) as rewarded_referrals, " +
            "COUNT(CASE WHEN first_recharge_amount > 0 THEN 1 END) as recharged_referrals, " +
            "COALESCE(SUM(first_recharge_amount), 0) as total_recharge_amount " +
            "FROM aicg_user_referral WHERE referrer_id = #{referrerId}")
    Map<String, Object> getReferralStats(@Param("referrerId") String referrerId);
    
    /**
     * 查询待确认的推荐记录
     * @return 待确认的推荐记录列表
     */
    @Select("SELECT * FROM aicg_user_referral WHERE status = 1")
    List<AicgUserReferral> getPendingReferrals();
    
    /**
     * 查询已确认但未奖励的推荐记录
     * @return 已确认但未奖励的推荐记录列表
     */
    @Select("SELECT * FROM aicg_user_referral WHERE status = 2")
    List<AicgUserReferral> getConfirmedReferrals();
}
