package org.jeecg.modules.api.util;

import java.security.MessageDigest;
import java.util.regex.Pattern;

import lombok.extern.slf4j.Slf4j;

/**
 * @Description: 安全工具类
 * @Author: jeecg-boot
 * @Date: 2025-06-14
 * @Version: V1.0
 */
@Slf4j
public class SecurityUtil {

    // 危险HTML标签模式
    private static final Pattern DANGEROUS_TAGS = Pattern.compile(
        "(?i)<\\s*(script|iframe|object|embed|form|input|meta|link|style|base|applet|audio|video|source|track)\\s*[^>]*>",
        Pattern.CASE_INSENSITIVE | Pattern.MULTILINE
    );
    
    // 危险属性模式
    private static final Pattern DANGEROUS_ATTRIBUTES = Pattern.compile(
        "(?i)(on\\w+|javascript:|vbscript:|data:text/html|expression\\s*\\()",
        Pattern.CASE_INSENSITIVE
    );
    
    // 危险协议模式
    private static final Pattern DANGEROUS_PROTOCOLS = Pattern.compile(
        "(?i)(javascript:|vbscript:|data:|file:|ftp:|about:|chrome:|resource:)",
        Pattern.CASE_INSENSITIVE
    );
    
    // SQL注入模式
    private static final Pattern SQL_INJECTION = Pattern.compile(
        "(?i)(union|select|insert|update|delete|drop|create|alter|exec|execute|script|declare|cast|convert)",
        Pattern.CASE_INSENSITIVE
    );

    /**
     * 生成请求签名
     * @param apiKey API密钥
     * @param content 内容
     * @param timestamp 时间戳
     * @return 签名
     */
    public static String generateSignature(String apiKey, String content, String timestamp) {
        try {
            // 构建签名字符串：apiKey + content + timestamp
            String signString = apiKey + content + timestamp;
            
            // 使用SHA-256生成签名
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] hash = digest.digest(signString.getBytes("UTF-8"));
            
            // 转换为十六进制字符串
            StringBuilder hexString = new StringBuilder();
            for (byte b : hash) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            
            return hexString.toString();
            
        } catch (Exception e) {
            log.error("生成签名异常: {}", e.getMessage(), e);
            throw new RuntimeException("签名生成失败", e);
        }
    }

    /**
     * 验证请求签名
     * @param apiKey API密钥
     * @param content 内容
     * @param timestamp 时间戳
     * @param signature 签名
     * @return 是否验证通过
     */
    public static boolean verifySignature(String apiKey, String content, String timestamp, String signature) {
        try {
            String expectedSignature = generateSignature(apiKey, content, timestamp);
            return signature.equals(expectedSignature);
        } catch (Exception e) {
            log.error("验证签名异常: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * HTML内容安全检查
     * @param htmlContent HTML内容
     * @return 检查结果，"safe"表示安全，其他表示具体的安全问题
     */
    public static String checkHtmlSecurity(String htmlContent) {
        try {
            if (htmlContent == null || htmlContent.trim().isEmpty()) {
                return "HTML内容不能为空";
            }

            // 🔥 小红书分享页面白名单检查
            log.info("开始HTML安全检查，内容长度: {}", htmlContent.length());
            log.debug("HTML内容前500字符: {}", htmlContent.length() > 500 ? htmlContent.substring(0, 500) : htmlContent);

            // 特殊处理：占位符内容直接通过（用于小红书分享的两阶段生成）
            if ("PLACEHOLDER_CONTENT".equals(htmlContent)) {
                log.info("✅ 检测到占位符内容，直接通过安全检查（小红书分享两阶段生成）");
                return "safe";
            }

            if (isXiaohongshuSharePage(htmlContent)) {
                log.info("✅ 检测到小红书分享页面，允许使用JavaScript和事件处理器");
                return "safe"; // 小红书分享页面直接通过安全检查
            } else {
                log.warn("❌ 未识别为小红书分享页面，将进行严格安全检查");
            }

            // 检查危险标签
            if (DANGEROUS_TAGS.matcher(htmlContent).find()) {
                return "包含危险的HTML标签（script、iframe、object等）";
            }

            // 检查危险属性
            if (DANGEROUS_ATTRIBUTES.matcher(htmlContent).find()) {
                return "包含危险的HTML属性（事件处理器、javascript等）";
            }

            // 检查危险协议
            if (DANGEROUS_PROTOCOLS.matcher(htmlContent).find()) {
                return "包含危险的协议（javascript、data等）";
            }

            // 检查SQL注入
            if (SQL_INJECTION.matcher(htmlContent).find()) {
                return "包含可能的SQL注入代码";
            }

            // 检查是否包含外部资源引用
            if (containsExternalResources(htmlContent)) {
                return "不允许引用外部资源";
            }

            // 检查是否包含恶意注释
            if (containsMaliciousComments(htmlContent)) {
                return "包含可疑的HTML注释";
            }

            return "safe";

        } catch (Exception e) {
            log.error("HTML安全检查异常: {}", e.getMessage(), e);
            return "安全检查异常";
        }
    }

    /**
     * 检查是否包含外部资源引用
     */
    private static boolean containsExternalResources(String htmlContent) {
        // 🔥 小红书分享页面允许外部资源
        if (isXiaohongshuSharePage(htmlContent)) {
            return false; // 小红书分享页面允许外部资源
        }

        String lowerContent = htmlContent.toLowerCase();

        // 检查外部链接
        if (lowerContent.contains("src=") &&
            (lowerContent.contains("http://") || lowerContent.contains("https://") || lowerContent.contains("//"))) {
            return true;
        }

        // 检查外部样式
        if (lowerContent.contains("href=") &&
            (lowerContent.contains("http://") || lowerContent.contains("https://") || lowerContent.contains("//"))) {
            return true;
        }

        // 检查@import
        if (lowerContent.contains("@import")) {
            return true;
        }

        return false;
    }

    /**
     * 检查是否包含恶意注释
     */
    private static boolean containsMaliciousComments(String htmlContent) {
        // 检查条件注释（IE特有）
        if (htmlContent.contains("<!--[if") || htmlContent.contains("<![endif]-->")) {
            return true;
        }

        // 检查包含脚本的注释
        if (htmlContent.contains("<!--") && htmlContent.toLowerCase().contains("script")) {
            return true;
        }

        return false;
    }

    /**
     * 检查是否为小红书分享页面
     * @param htmlContent HTML内容
     * @return 是否为小红书分享页面
     */
    private static boolean isXiaohongshuSharePage(String htmlContent) {
        if (htmlContent == null) {
            log.debug("HTML内容为null，不是小红书分享页面");
            return false;
        }

        String lowerContent = htmlContent.toLowerCase();
        log.debug("开始检查小红书分享页面特征，内容长度: {}", htmlContent.length());

        // 检查小红书分享页面的特征标识
        boolean hasXhsTitle = lowerContent.contains("小红书分享") || lowerContent.contains("xiaohongshu") || lowerContent.contains("智界aigc");
        boolean hasXhsScript = lowerContent.contains("xhs-1.0.1.js") || lowerContent.contains("window.xhs") || lowerContent.contains("fe-static.xhscdn.com");
        boolean hasShareFunction = lowerContent.contains("sharetoxiaohongshu") || lowerContent.contains("handleshare");
        boolean hasXhsClass = lowerContent.contains("xiaohongshu-container") || lowerContent.contains("share-btn");

        // 新增更多识别特征
        boolean hasShareText = lowerContent.contains("分享到小红书") || lowerContent.contains("share to xiaohongshu");
        boolean hasQrCode = lowerContent.contains("qrcode") || lowerContent.contains("二维码") || lowerContent.contains("qrmodal");
        boolean hasXhsContent = lowerContent.contains("xiaohongshu_") || lowerContent.contains("xhs_");

        // 新增小红书特有的特征
        boolean hasXhsSpecific = lowerContent.contains("手机扫码分享") || lowerContent.contains("小红书js sdk") || lowerContent.contains("xhscdn.com");
        boolean hasShareModal = lowerContent.contains("modal-overlay") || lowerContent.contains("closeqrmodal");
        boolean hasXhsApi = lowerContent.contains("window.xhs.share") || lowerContent.contains("shareinfo");

        // 满足任意两个条件即认为是小红书分享页面
        int matchCount = 0;
        if (hasXhsTitle) matchCount++;
        if (hasXhsScript) matchCount++;
        if (hasShareFunction) matchCount++;
        if (hasXhsClass) matchCount++;
        if (hasShareText) matchCount++;
        if (hasQrCode) matchCount++;
        if (hasXhsContent) matchCount++;
        if (hasXhsSpecific) matchCount++;
        if (hasShareModal) matchCount++;
        if (hasXhsApi) matchCount++;

        boolean isXhsPage = matchCount >= 2;

        if (isXhsPage) {
            log.info("✅ 识别为小红书分享页面 - 匹配数:{}/10", matchCount);
            log.info("特征详情 - 标题:{}, 脚本:{}, 函数:{}, 样式:{}, 分享文本:{}, 二维码:{}, 内容:{}, 特有:{}, 弹窗:{}, API:{}",
                hasXhsTitle, hasXhsScript, hasShareFunction, hasXhsClass, hasShareText, hasQrCode, hasXhsContent, hasXhsSpecific, hasShareModal, hasXhsApi);
        } else {
            log.warn("❌ 未识别为小红书分享页面 - 匹配数:{}/10", matchCount);
            log.warn("特征详情 - 标题:{}, 脚本:{}, 函数:{}, 样式:{}, 分享文本:{}, 二维码:{}, 内容:{}, 特有:{}, 弹窗:{}, API:{}",
                hasXhsTitle, hasXhsScript, hasShareFunction, hasXhsClass, hasShareText, hasQrCode, hasXhsContent, hasXhsSpecific, hasShareModal, hasXhsApi);

            // 输出HTML内容的前1000字符用于调试
            log.debug("HTML内容前1000字符: {}", htmlContent.length() > 1000 ? htmlContent.substring(0, 1000) : htmlContent);
        }

        return isXhsPage;
    }

    /**
     * 清理HTML内容（移除危险元素）
     * @param htmlContent 原始HTML内容
     * @return 清理后的HTML内容
     */
    public static String sanitizeHtml(String htmlContent) {
        if (htmlContent == null) {
            return "";
        }
        
        String cleaned = htmlContent;
        
        // 移除危险标签
        cleaned = DANGEROUS_TAGS.matcher(cleaned).replaceAll("");
        
        // 移除危险属性
        cleaned = DANGEROUS_ATTRIBUTES.matcher(cleaned).replaceAll("");
        
        // 移除危险协议
        cleaned = DANGEROUS_PROTOCOLS.matcher(cleaned).replaceAll("");
        
        return cleaned;
    }

    /**
     * 生成安全的文件名
     * @param originalName 原始文件名
     * @return 安全的文件名
     */
    public static String generateSafeFileName(String originalName) {
        if (originalName == null || originalName.trim().isEmpty()) {
            return "file_" + System.currentTimeMillis();
        }
        
        // 移除危险字符
        String safeName = originalName.replaceAll("[^a-zA-Z0-9._-]", "_");
        
        // 限制长度
        if (safeName.length() > 100) {
            safeName = safeName.substring(0, 100);
        }
        
        // 确保有扩展名
        if (!safeName.contains(".")) {
            safeName += ".html";
        }
        
        return safeName;
    }

    /**
     * 验证API-Key格式
     * @param apiKey API密钥
     * @return 是否格式正确
     */
    public static boolean isValidApiKeyFormat(String apiKey) {
        if (apiKey == null || apiKey.trim().isEmpty()) {
            return false;
        }
        
        // 检查格式：ak_开头，总长度35位
        return apiKey.startsWith("ak_") && apiKey.length() == 35 && 
               apiKey.matches("^ak_[a-zA-Z0-9]{32}$");
    }

    /**
     * 生成随机API密钥
     * @return API密钥
     */
    public static String generateApiKey() {
        String chars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
        StringBuilder apiKey = new StringBuilder("ak_");
        
        for (int i = 0; i < 32; i++) {
            int index = (int) (Math.random() * chars.length());
            apiKey.append(chars.charAt(index));
        }
        
        return apiKey.toString();
    }
}
