# 🏗️ 智界Aigc技术架构文档

## 📁 项目整体结构

```
智界Aigc项目/
├── AigcViewRd/              # 后端系统（Spring Boot）
├── AigcViewFe/智界Aigc/      # 管理前端（Vue.js + JeecgBoot）
└── 官网前端/                # 用户官网（Vue.js，待开发）
```

## 🔧 技术栈选择

### 后端技术栈
- **Spring Boot 2.3.5** - 核心框架
- **MyBatis-Plus 3.4.3** - ORM框架
- **Apache Shiro** - 安全框架
- **MySQL 8.0** - 主数据库
- **Redis** - 缓存数据库
- **Druid** - 数据库连接池

### 前端技术栈
- **Vue.js 2.6.10** - 核心框架
- **Ant Design Vue 1.7.2** - UI组件库
- **Vue Router** - 路由管理
- **Vuex** - 状态管理
- **Axios** - HTTP请求库
- **ECharts 5.6.0** - 数据可视化

### 部署架构
- **Windows Server** - 服务器系统
- **Nginx** - Web服务器和反向代理
- **Java 8** - 运行环境
- **域名**：www.aigcview.com（HTTPS）

## 🗄️ 数据库架构

### 核心业务表
```sql
-- 用户扩展信息表
aicg_user_profile (用户ID、昵称、余额、API密钥、会员等级等)

-- 插件商城表
aigc_plub_shop (插件名称、图片、创作者、介绍、价格、收益等)

-- 插件创作者表
aigc_plub_author (创作者名称、插件数、使用总数、累计收益等)

-- 视频教程表
aigc_video_tutorial (标题、视频文件、讲师、等级、标签等)

-- 视频讲师表
aigc_video_teacher (讲师名、介绍、学习人数、课程数量等)

-- 用户交易记录表  
aicg_user_transaction (交易类型、金额、余额变动、描述等)

-- 兑换码表
aicg_exchange_code (兑换码、类型、价值、状态、过期时间等)

-- API调用日志表
aicg_api_log (用户ID、接口、参数、响应、状态等)
```

### 新增表（官网功能）
```sql
-- 客户案例表
aigc_customer_case (标题、图片、描述、链接等)

-- 签到记录表
aigc_sign_record (用户ID、签到日期、奖励类型、连续天数等)

-- 会员订阅表
aigc_membership (用户ID、会员等级、开始时间、结束时间等)

-- 分销推广表
aigc_affiliate (用户ID、推广码、推广收益、推广人数等)

-- 网站配置表
aigc_website_config (配置键、配置值、配置类型、描述等)
```

## 🌐 API架构设计

### 现有API接口
```
/jeecg-boot/
├── /sys/                   # 系统管理接口
├── /plubshop/             # 插件商城接口
├── /plubauthor/           # 插件创作者接口
├── /videotutorial/        # 视频教程接口
├── /videoteacher/         # 视频讲师接口
├── /userCenter/           # 个人中心接口
└── /api/                  # 对外API接口
```

### 新增官网API
```
/api/website/
├── /header                # 页头数据接口
├── /footer                # 页脚数据接口
├── /home                  # 首页数据接口
├── /market                # 商城数据接口（复用现有）
├── /cases                 # 客户案例接口
├── /tutorials             # 教程中心接口（复用现有）
├── /signin                # 签到功能接口
├── /membership            # 会员功能接口
└── /affiliate             # 分销功能接口
```

## 📱 前端架构设计

### 管理前端架构（现有）
```
AigcViewFe/智界Aigc/src/
├── views/
│   ├── dashboard/         # 仪表板
│   ├── aigcview/          # 核心业务模块
│   │   ├── plubshop/      # 插件商城管理
│   │   ├── plubauthor/    # 插件创作者管理
│   │   ├── videotutorial/ # 视频教程管理
│   │   ├── videoteacher/  # 视频讲师管理
│   │   └── usercenter/    # 个人中心
│   └── system/            # 系统管理
├── components/            # 组件库
├── api/                   # API接口封装
├── router/                # 路由配置
└── store/                 # 状态管理
```

### 官网前端架构（待开发）
```
官网前端/src/
├── views/
│   ├── Home.vue           # 首页
│   ├── Market.vue         # 商城
│   ├── Cases.vue          # 客户案例
│   ├── Tutorials.vue      # 使用说明
│   ├── SignIn.vue         # 签到奖励
│   ├── Membership.vue     # 订阅会员
│   ├── Affiliate.vue      # 分销推广
│   └── UserCenter.vue     # 个人中心
├── components/
│   ├── layout/
│   │   ├── Header.vue     # 页头组件（可复用）
│   │   ├── Footer.vue     # 页脚组件（可复用）
│   │   └── Layout.vue     # 整体布局
│   ├── common/
│   │   ├── PluginCard.vue # 插件卡片组件
│   │   ├── VideoCard.vue  # 视频卡片组件
│   │   └── UserAvatar.vue # 用户头像组件
│   └── business/
│       ├── SignCalendar.vue # 签到日历
│       └── MemberCard.vue   # 会员卡片
├── api/                   # API接口封装
├── router/                # 路由配置
└── utils/                 # 工具函数
```

## 🔐 安全架构

### 多层安全防护
1. **API密钥验证** - 确保调用者身份合法
2. **请求签名验证** - SHA256防篡改和重放攻击
3. **时间戳验证** - 5分钟有效期防重放
4. **内容安全检查** - 防止恶意代码注入
5. **权限控制** - 基于Shiro的完整权限体系

### 认证流程
```
用户登录 → 获取Token → Token存储 → 权限获取 → 
动态路由生成 → 权限验证 → 页面访问控制
```

## 🚀 部署架构

### Nginx配置
```nginx
# 官网路由
location / {
    try_files $uri $uri/ /index.html;
}

# 管理系统路由  
location /admin {
    try_files $uri $uri/ /admin/index.html;
}

# API代理
location /jeecg-boot/ {
    proxy_pass http://backend_servers;
}
```

### 服务器配置
- **操作系统**：Windows Server
- **Web服务器**：Nginx for Windows
- **应用服务器**：Spring Boot内嵌Tomcat
- **数据库**：MySQL 8.0 + Redis
- **SSL证书**：已配置HTTPS

## 📊 性能优化

### 前端优化
- 路由级别懒加载
- 组件按需引入
- 静态资源CDN
- 图片压缩优化

### 后端优化
- 数据库连接池
- Redis缓存机制
- API响应压缩
- SQL查询优化

### 部署优化
- Nginx静态资源缓存
- Gzip压缩
- HTTP/2支持
- 负载均衡配置
