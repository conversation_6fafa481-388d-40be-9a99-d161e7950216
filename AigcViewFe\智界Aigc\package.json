{"name": "vue-antd-jeecg", "version": "2.4.6", "private": true, "scripts": {"pre": "cnpm install || yarn --registry https://registry.npm.taobao.org || npm install --registry https://registry.npm.taobao.org ", "serve": "vue-cli-service serve", "build:test": "vue-cli-service build --mode test", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"@antv/data-set": "^0.11.4", "@jeecg/antd-online-mini": "2.4.6-beta5", "@tinymce/tinymce-vue": "^2.1.0", "@toast-ui/editor": "^2.1.2", "ant-design-vue": "^1.7.2", "axios": "^0.18.0", "china-area-data": "^5.0.1", "clipboard": "^2.0.4", "codemirror": "^5.46.0", "cron-parser": "^2.10.0", "dayjs": "^1.8.0", "dom-align": "1.12.0", "echarts": "^5.6.0", "enquire.js": "^2.1.6", "gsap": "^3.13.0", "js-cookie": "^2.2.0", "lodash.get": "^4.4.2", "lodash.pick": "^4.4.0", "md5": "^2.2.1", "nprogress": "^0.2.0", "tinymce": "^5.3.2", "viser-vue": "^2.4.8", "vue": "^2.6.10", "vue-area-linkage": "^5.1.0", "vue-cropper": "^0.5.4", "vue-i18n": "^8.7.0", "vue-loader": "^15.7.0", "vue-ls": "^3.2.0", "vue-photo-preview": "^1.1.3", "vue-print-nb-jeecg": "^1.0.9", "vue-router": "^3.0.1", "vue-splitpane": "^1.0.4", "vuedraggable": "^2.20.0", "vuex": "^3.1.0", "vxe-table": "2.9.13", "vxe-table-plugin-antd": "1.8.10", "xe-utils": "2.4.8"}, "devDependencies": {"@babel/polyfill": "^7.2.5", "@vue/cli-plugin-babel": "^3.3.0", "@vue/cli-plugin-eslint": "^3.3.0", "@vue/cli-service": "^3.12.1", "@vue/eslint-config-standard": "^4.0.0", "babel-eslint": "7.2.3", "chokidar": "^4.0.3", "compression-webpack-plugin": "^3.1.0", "cross-env": "^7.0.3", "eslint": "^5.16.0", "eslint-plugin-vue": "^5.1.0", "html-webpack-plugin": "^4.2.0", "less": "^3.9.0", "less-loader": "^4.1.0", "mini-css-extract-plugin": "^1.6.2", "vue-template-compiler": "^2.6.10", "webpack": "^4.47.0", "webpack-bundle-analyzer": "^4.10.2"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/strongly-recommended", "@vue/standard"], "parserOptions": {"parser": "babel-es<PERSON>"}, "rules": {"generator-star-spacing": "off", "no-mixed-operators": 0, "vue/max-attributes-per-line": [2, {"singleline": 5, "multiline": {"max": 1, "allowFirstLine": false}}], "vue/attribute-hyphenation": 0, "vue/html-self-closing": 0, "vue/component-name-in-template-casing": 0, "vue/html-closing-bracket-spacing": 0, "vue/singleline-html-element-content-newline": 0, "vue/no-unused-components": 0, "vue/multiline-html-element-content-newline": 0, "vue/no-use-v-if-with-v-for": 0, "vue/html-closing-bracket-newline": 0, "vue/no-parsing-error": 0, "no-tabs": 0, "indent": ["off", 2], "no-console": 0, "space-before-function-paren": 0}}, "postcss": {"plugins": {"autoprefixer": {}}}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 10"]}