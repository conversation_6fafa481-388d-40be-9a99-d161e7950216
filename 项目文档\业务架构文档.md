# 📋 智界Aigc业务架构文档

## 🎯 项目定位

**智界Aigc** 是一个"AI插件生态系统 + 在线教育平台"，专注于AI能力的插件化和商业化。

### 商业模式
类似于"AI时代的App Store"，让AI能力像手机应用一样便捷地被集成和使用。

## 🧭 官网导航栏（8个模块）

```
首页 | 商城 | 客户案例 | 教程中心 | 签到奖励 | 订阅会员 | 分销推广 | 个人中心
```

## 📋 完整的官网架构

### 1. 首页
- **轮播图**（展示优势）
- **功能介绍**（四大核心功能）
  - 小红书自动生成爆款图文笔记
  - 小红书自动生成爆款视频笔记
  - 小红书一键自动发布
  - 剪映小助手
- **页脚**

### 2. 商城 [使用现有系统数据]
- **插件市场展示**
- 基于现有 `plubshop` + `plubauthor` 数据结构
- 插件信息：名称、图片、创作者、介绍、教程视频、价格、调用次数

### 3. 客户案例
- **图文**（左右结构）展示
- 展示成功案例，建立用户信任

### 4. 教程中心 [使用现有系统数据]
- **视频教程中心**
- 基于现有 `videotutorial` + `videoteacher` 数据结构
- 分级教学、系列课程、专业讲师

### 5. 签到奖励（开发中…）
- **每日签到功能**
- **奖励机制**
- 提升用户粘性和活跃度

### 6. 订阅会员（开发中…）
- **token折扣**
- **会员体系**
- 不同等级享受不同优惠

### 7. 分销推广（开发中…）
- **15%分成**
- **推广系统**
- 用户推广获得佣金收益

### 8. 个人中心 [使用现有系统数据]
- **用户信息管理**：头像、昵称
- **API-Key管理**：生成、重置、使用统计
- **账户管理**：余额、充值、消费记录
- **兑换码功能**：兑换各种权益

## 🎯 开发优先级

### 第一阶段（立即开发）：
1. **首页** - 营销门面，吸引用户
2. **商城** - 核心变现功能
3. **个人中心** - 用户体验核心
4. **教程中心** - 用户教育，降低门槛

### 第二阶段（功能完善）：
5. **客户案例** - 信任建立，提升转化

### 第三阶段（新功能开发）：
6. **签到奖励** - 用户粘性提升
7. **订阅会员** - 商业化升级
8. **分销推广** - 生态扩展

## 🔄 完整的商业闭环

### 插件生态循环：
```
插件创作者注册 → 开发AI插件 → 上传到商城 → 制作教程视频
     ↓
用户学习教程 → 理解插件价值 → 通过API调用 → 按次付费
     ↓
平台收取佣金 → 创作者获得分成 → 激励更多创作 → 生态扩大
```

### 用户价值链：
```
营销展示（首页）→ 产品了解（商城）→ 学习使用（教程）→
注册使用（个人中心）→ 持续使用（签到）→ 深度使用（会员）→
推广获益（分销）
```

## 💼 目标用户群体

### 1. 插件创作者（开发者）
- 有技术能力的开发者
- 希望通过插件开发获得收益
- 需要平台提供技术支持和用户流量

### 2. 插件使用者（企业/个人）
- 需要特定功能但不想自己开发
- 愿意为便捷的解决方案付费
- 通过API调用的方式集成到自己的系统中

### 3. 学习者（技术爱好者）
- 想学习插件开发技术
- 通过视频教程提升技能
- 可能从学习者转变为创作者

## 💰 收入模式

1. **插件销售收入** - 用户购买插件API调用
2. **会员订阅收入** - 用户购买会员享受折扣
3. **分销佣金收入** - 用户推广获得15%分成
4. **教育内容收入** - 高级教程可能收费

## 🏆 竞争优势

1. **双重收入模式** - 插件交易 + 教育内容
2. **网络效应** - 插件越多用户越多，用户越多开发者越多
3. **技术门槛低** - 通过教育让非技术用户也能使用AI
4. **竞争壁垒高** - 生态系统难以复制
