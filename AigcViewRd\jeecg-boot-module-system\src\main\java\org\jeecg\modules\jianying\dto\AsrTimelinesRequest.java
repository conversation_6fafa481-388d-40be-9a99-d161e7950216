package org.jeecg.modules.jianying.dto;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 语音识别时间线请求参数
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AsrTimelinesRequest extends BaseJianyingRequest {
    
    @ApiModelProperty(value = "字幕识别插件的输出内容（必填）", required = true,
                     example = "[{\"text\": \"这是识别的文本\", \"start_time\": 0, \"end_time\": 2000}]")
    @NotEmpty(message = "content_chunks不能为空")
    @JsonProperty("content_chunks")
    private List<JSONObject> zjContentChunks;
    
    @Override
    public String getSummary() {
        return "AsrTimelinesRequest{" +
               "accessKey=" + (getAccessKey() != null && getAccessKey().length() > 10 ? 
                              getAccessKey().substring(0, 10) + "***" : "null") +
               ", chunksCount=" + (zjContentChunks != null ? zjContentChunks.size() : 0) +
               "}";
    }
}
