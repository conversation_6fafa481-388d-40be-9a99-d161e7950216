package org.jeecg.modules.jianyingpro.service.internal;

import com.volcengine.tos.TOSV2;
import com.volcengine.tos.TOSV2ClientBuilder;
import com.volcengine.tos.TosClientException;
import com.volcengine.tos.TosServerException;

import com.volcengine.tos.model.object.PutObjectInput;
import com.volcengine.tos.model.object.PutObjectOutput;
import com.volcengine.tos.model.object.GetObjectV2Input;
import com.volcengine.tos.model.object.GetObjectV2Output;
import com.volcengine.tos.model.object.PreSignedURLInput;
import com.volcengine.tos.model.object.PreSignedURLOutput;
import com.volcengine.tos.model.object.DeleteObjectInput;
import com.volcengine.tos.model.object.DeleteObjectOutput;

import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.jianying.config.TosConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.PostConstruct;
import java.io.ByteArrayInputStream;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.nio.file.Path;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.UUID;

/**
 * 火山引擎TOS对象存储服务
 *
 * <AUTHOR>
 * @date 2025-01-01
 */
@Slf4j
@Service
public class JianyingProTosService {

    @Autowired
    private TosConfig tosConfig;

    private TOSV2 tosClient;           // 外网TOS客户端（CDN回源）
    private TOSV2 internalTosClient;   // 🆕 内网TOS客户端（免费流量）

    // 通用文件存储配置
    @Value("${volcengine.tos.general.bucket:aigcview-tos}")
    private String generalBucket;

    @Value("${volcengine.tos.general.base-path:uploads}")
    private String generalBasePath;

    @Value("${volcengine.tos.signed-url.expire-hours:24}")
    private Integer signedUrlExpireHours;

    // 🆕 CDN配置
    @Value("${volcengine.tos.cdn.enabled:false}")
    private Boolean cdnEnabled;

    @Value("${volcengine.tos.cdn.domain:}")
    private String cdnDomain;

    @Value("${volcengine.tos.cdn.fallback-to-tos:true}")
    private Boolean fallbackToTos;

    // 🔒 剪映助手专用配置（独立桶，无CDN）
    @Value("${volcengine.tos.jianying.bucket:aigcview-jianying}")
    private String jianyingBucket;

    @Value("${volcengine.tos.jianying.base-path:jianying-assistant}")
    private String jianyingBasePath;
    
    /**
     * 初始化TOS客户端
     * 根据官方文档：https://www.volcengine.com/docs/6349/79917
     */
    @PostConstruct
    public void initTosClient() {
        try {
            log.info("开始初始化TOS客户端...");

            // 验证配置参数
            if (tosConfig == null) {
                throw new IllegalArgumentException("TOS配置对象为空");
            }
            if (tosConfig.getRegion() == null || tosConfig.getRegion().trim().isEmpty()) {
                throw new IllegalArgumentException("TOS Region配置为空");
            }
            if (tosConfig.getEndpoint() == null || tosConfig.getEndpoint().trim().isEmpty()) {
                throw new IllegalArgumentException("TOS Endpoint配置为空");
            }
            if (tosConfig.getAccessKey() == null || tosConfig.getAccessKey().trim().isEmpty()) {
                throw new IllegalArgumentException("TOS AccessKey配置为空");
            }
            if (tosConfig.getSecretKey() == null || tosConfig.getSecretKey().trim().isEmpty()) {
                throw new IllegalArgumentException("TOS SecretKey配置为空");
            }

            log.info("TOS配置验证通过 - Region: {}, Endpoint: {}, Bucket: {}",
                    tosConfig.getRegion(), tosConfig.getEndpoint(), tosConfig.getBucket());

            // 🌐 创建外网TOS客户端（用于CDN回源）
            tosClient = new TOSV2ClientBuilder().build(
                    tosConfig.getRegion(),
                    tosConfig.getEndpoint(),
                    tosConfig.getAccessKey(),
                    tosConfig.getSecretKey()
            );
            log.info("外网TOS客户端初始化成功！");

            // 🔒 创建内网TOS客户端（用于剪映草稿，免费流量）
            if (tosConfig.getInternal() != null && tosConfig.getInternal().getEnabled()) {
                internalTosClient = new TOSV2ClientBuilder().build(
                        tosConfig.getRegion(),
                        tosConfig.getInternal().getEndpoint(),
                        tosConfig.getAccessKey(),
                        tosConfig.getSecretKey()
                );
                log.info("内网TOS客户端初始化成功 - Endpoint: {}", tosConfig.getInternal().getEndpoint());
            }

            // 测试连接
            testTosConnection();

        } catch (Exception e) {
            log.error("TOS客户端初始化失败 - 详细信息: Region={}, Endpoint={}, AccessKey={}, 错误: {}",
                    tosConfig != null ? tosConfig.getRegion() : "null",
                    tosConfig != null ? tosConfig.getEndpoint() : "null",
                    tosConfig != null && tosConfig.getAccessKey() != null ?
                        tosConfig.getAccessKey().substring(0, Math.min(8, tosConfig.getAccessKey().length())) + "..." : "null",
                    e.getMessage(), e);
            throw new RuntimeException("TOS客户端初始化失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 🔒 上传剪映草稿JSON文件（使用专用桶，无CDN回源）
     *
     * @param jsonContent JSON文件内容
     * @return 文件访问URL
     */
    public String uploadDraftFile(String jsonContent) {
        try {
            // 生成文件路径和名称
            String filePath = generateFilePath();
            String fileName = generateFileName();
            String fullPath = filePath + fileName;

            // 移除开头斜杠，确保Key一致性
            String cleanPath = fullPath.startsWith("/") ? fullPath.substring(1) : fullPath;

            log.info("上传剪映草稿文件到专用桶，Key: {}", cleanPath);

            // 🔒 上传到剪映专用桶（无CDN回源）
            PutObjectInput putObjectInput = new PutObjectInput()
                    .setBucket(jianyingBucket)
                    .setKey(cleanPath)
                    .setContent(new ByteArrayInputStream(jsonContent.getBytes("UTF-8")))
                    .setContentLength((long) jsonContent.getBytes("UTF-8").length);

            PutObjectOutput putObjectOutput = tosClient.putObject(putObjectInput);

            // 🔧 修复：返回系统URL，让前端通过后端访问（用户友好）
            String systemUrl = generateSystemFileUrl("/" + cleanPath);
            log.info("草稿文件上传成功，系统URL: {}, ETag: {}", systemUrl, putObjectOutput.getEtag());
            return systemUrl;

        } catch (TosClientException e) {
            log.error("TOS客户端异常", e);
            throw new RuntimeException("草稿文件上传失败: " + e.getMessage(), e);
        } catch (TosServerException e) {
            log.error("TOS服务器异常", e);
            throw new RuntimeException("草稿文件上传失败: " + e.getMessage(), e);
        } catch (Exception e) {
            log.error("草稿文件上传异常", e);
            throw new RuntimeException("草稿文件上传失败", e);
        }
    }
    
    /**
     * 生成文件存储路径
     * 格式: /jianying-assistant/drafts/YYYY/MM/DD/
     */
    private String generateFilePath() {
        LocalDateTime now = LocalDateTime.now();
        return tosConfig.getBasePath() + 
               now.format(DateTimeFormatter.ofPattern("yyyy/MM/dd/"));
    }
    
    /**
     * 生成文件名
     * 格式: zj_draft_时间戳_随机码.json
     */
    private String generateFileName() {
        LocalDateTime now = LocalDateTime.now();
        String timestamp = now.format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        String randomCode = UUID.randomUUID().toString().substring(0, 8);
        return tosConfig.getFilePrefix() + timestamp + "_" + randomCode + ".json";
    }
    
    /**
     * 生成系统文件访问URL（通过系统接口访问，不暴露TOS，智能路径选择）
     */
    public String generateSystemFileUrl(String filePath) {
        // 确保文件路径格式正确
        String cleanPath = filePath.startsWith("/") ? filePath : "/" + filePath;

        // 🔒 剪映文件使用专用路径，与Electron应用期望的格式一致
        if (cleanPath.startsWith("/jianying-assistant/")) {
            String baseUrl = "https://aigcview.cn/jeecg-boot/sys/common/jianying-file";
            return baseUrl + cleanPath;
        }

        // 🌐 其他文件使用通用静态文件路径
        String baseUrl = "https://aigcview.cn/jeecg-boot/sys/common/static";
        return baseUrl + cleanPath;
    }

    /**
     * 🆕 生成文件访问URL（混合网络访问策略）
     */
    public String generateFileUrl(String filePath) {
        // 🔍 参数验证
        if (filePath == null || filePath.trim().isEmpty()) {
            log.warn("文件路径为空，无法生成URL");
            return null;
        }

        // 确保filePath不以斜杠开头，避免双斜杠问题
        String cleanPath = filePath.startsWith("/") ? filePath.substring(1) : filePath;

        // 🔒 剪映草稿文件：使用内网TOS访问（免费流量）
        if (isJianyingDraftFile(cleanPath)) {
            return generateInternalTosSignedUrl(cleanPath);
        }

        // 🌐 通用桶文件（uploads/、electron/、defaults/）：强制CDN访问
        if (cleanPath.startsWith("uploads/") || cleanPath.startsWith("electron/") || cleanPath.startsWith("defaults/")) {
            if (Boolean.TRUE.equals(cdnEnabled) && oConvertUtils.isNotEmpty(cdnDomain)) {
                String cdnUrl = cdnDomain + "/" + cleanPath;
                log.debug("通用桶文件强制CDN访问: {} -> {}", cleanPath, cdnUrl);
                return cdnUrl;
            } else {
                log.warn("CDN未启用，通用桶文件无法访问: {}", cleanPath);
                throw new RuntimeException("CDN未启用，通用桶文件无法访问");
            }
        }

        // 🔄 其他文件：降级到TOS签名URL
        log.debug("其他文件使用TOS签名URL: {}", cleanPath);

        // 🔄 降级到TOS签名URL（仅在CDN失败或禁用时）
        if (Boolean.TRUE.equals(fallbackToTos)) {
            return generateTosSignedUrl(cleanPath);
        }

        return "";
    }

    /**
     * 🔄 生成TOS签名URL（私有方法，作为降级方案，智能桶选择）
     */
    private String generateTosSignedUrl(String cleanPath) {
        try {
            // 🚨 通用桶文件不应该走到这里，应该强制CDN访问
            if (cleanPath.startsWith("uploads/") || cleanPath.startsWith("electron/") || cleanPath.startsWith("defaults/")) {
                log.error("通用桶文件不应该生成TOS签名URL，应该强制CDN访问: {}", cleanPath);
                throw new RuntimeException("通用桶文件必须通过CDN访问，请检查CDN配置");
            }

            // 🔒 智能选择桶：剪映文件使用专用桶，其他文件使用通用桶
            String targetBucket = cleanPath.startsWith("jianying-assistant/") ? jianyingBucket : generalBucket;
            log.debug("生成签名URL使用桶: {}", targetBucket);

            PreSignedURLInput input = new PreSignedURLInput()
                    .setBucket(targetBucket)
                    .setKey(cleanPath)
                    .setHttpMethod("GET")
                    .setExpires(signedUrlExpireHours * 3600);

            PreSignedURLOutput output = tosClient.preSignedURL(input);
            log.debug("生成TOS签名URL: {} -> {}", cleanPath, output.getSignedUrl());
            return output.getSignedUrl();
        } catch (Exception e) {
            log.error("生成TOS签名URL失败: {}", cleanPath, e);
            return "";
        }
    }
    
    /**
     * 上传ZIP文件到TOS存储
     *
     * @param zipFilePath ZIP文件路径
     * @param folderName 文件夹名称
     * @return 文件访问URL
     */
    public String uploadZipFile(Path zipFilePath, String folderName) {
        try {
            // 生成文件路径：/jianying-assistant/drafts/YYYY/MM/DD/draft_package_folderName.zip
            LocalDateTime now = LocalDateTime.now();
            String datePath = now.format(DateTimeFormatter.ofPattern("yyyy/MM/dd"));
            String fileName = "draft_package_" + folderName + ".zip";
            String filePath = "jianying-assistant/drafts/" + datePath + "/" + fileName;

            // 读取ZIP文件
            try (FileInputStream fis = new FileInputStream(zipFilePath.toFile())) {
                // 🔒 使用剪映专用桶上传ZIP文件（无CDN回源）
                PutObjectInput putObjectInput = PutObjectInput.builder()
                        .bucket(jianyingBucket)  // 🔧 修复：使用剪映专用桶
                        .key(filePath)
                        .content(fis)
                        .contentLength(zipFilePath.toFile().length())
                        .build();

                PutObjectOutput putObjectOutput = tosClient.putObject(putObjectInput);

                // 🔧 修复：返回系统URL，避免在非内网环境生成内网TOS签名URL失败
                String systemUrl = generateSystemFileUrl("/" + filePath);
                log.info("ZIP文件上传成功: {}, ETag: {}", systemUrl, putObjectOutput.getEtag());

                return systemUrl;
            }

        } catch (TosClientException e) {
            log.error("TOS客户端异常", e);
            throw new RuntimeException("上传ZIP文件失败: " + e.getMessage(), e);
        } catch (TosServerException e) {
            log.error("TOS服务端异常", e);
            throw new RuntimeException("上传ZIP文件失败: " + e.getMessage(), e);
        } catch (IOException e) {
            log.error("文件读取异常", e);
            throw new RuntimeException("读取ZIP文件失败: " + e.getMessage(), e);
        } catch (Exception e) {
            log.error("上传ZIP文件时发生未知异常", e);
            throw new RuntimeException("上传ZIP文件失败: " + e.getMessage(), e);
        }
    }

    /**
     * 使用指定的对象键上传草稿文件（用于覆盖保存）
     *
     * @param jsonContent JSON文件内容
     * @param objectKey TOS对象键
     */
    public void uploadDraftFileWithKey(String jsonContent, String objectKey) {
        try {
            // 移除开头的斜杠（如果有）
            String cleanPath = objectKey.startsWith("/") ? objectKey.substring(1) : objectKey;

            log.info("使用指定对象键上传草稿文件: {}", cleanPath);

            // 🔒 使用剪映专用桶（无CDN回源）
            PutObjectInput putObjectInput = new PutObjectInput()
                    .setBucket(jianyingBucket)
                    .setKey(cleanPath)
                    .setContent(new ByteArrayInputStream(jsonContent.getBytes("UTF-8")))
                    .setContentLength((long) jsonContent.getBytes("UTF-8").length);

            PutObjectOutput putObjectOutput = tosClient.putObject(putObjectInput);

            log.info("草稿文件上传成功: {}, ETag: {}", cleanPath, putObjectOutput.getEtag());

        } catch (TosClientException e) {
            log.error("TOS客户端异常", e);
            throw new RuntimeException("草稿文件上传失败: " + e.getMessage(), e);
        } catch (TosServerException e) {
            log.error("TOS服务器异常", e);
            throw new RuntimeException("草稿文件上传失败: " + e.getMessage(), e);
        } catch (Exception e) {
            log.error("草稿文件上传异常", e);
            throw new RuntimeException("草稿文件上传失败", e);
        }
    }

    /**
     * 覆盖更新已存在的草稿文件（使用剪映专用桶，无CDN回源）
     *
     * @param filePath 文件路径（如：/jianying-assistant/drafts/2025/07/04/file.json）
     * @param jsonContent JSON文件内容
     */
    public void overwriteDraftFile(String filePath, String jsonContent) {
        try {
            // 移除开头的斜杠（如果有）
            String cleanPath = filePath.startsWith("/") ? filePath.substring(1) : filePath;

            log.info("开始覆盖剪映文件: {}", cleanPath);

            // 🔒 使用剪映专用桶（无CDN回源）
            PutObjectInput putObjectInput = new PutObjectInput()
                    .setBucket(jianyingBucket)
                    .setKey(cleanPath)
                    .setContent(new ByteArrayInputStream(jsonContent.getBytes("UTF-8")))
                    .setContentLength((long) jsonContent.getBytes("UTF-8").length);

            PutObjectOutput putObjectOutput = tosClient.putObject(putObjectInput);

            log.info("文件覆盖成功: {}, ETag: {}", cleanPath, putObjectOutput.getEtag());

        } catch (TosClientException e) {
            log.error("TOS客户端异常", e);
            throw new RuntimeException("文件覆盖失败: " + e.getMessage(), e);
        } catch (TosServerException e) {
            log.error("TOS服务器异常", e);
            throw new RuntimeException("文件覆盖失败: " + e.getMessage(), e);
        } catch (Exception e) {
            log.error("文件覆盖异常", e);
            throw new RuntimeException("文件覆盖失败", e);
        }
    }

    /**
     * 上传音频文件到TOS（使用剪映专用桶，无CDN回源）
     *
     * @param filePath 文件路径（如：/jianying-assistant/drafts/2025/07/04/audioId/file.mp3）
     * @param audioData 音频文件数据
     */
    public void uploadAudioFile(String filePath, byte[] audioData) {
        try {
            // 移除开头的斜杠（如果有）
            String cleanPath = filePath.startsWith("/") ? filePath.substring(1) : filePath;

            log.info("开始上传剪映音频文件: {}, 大小: {} bytes", cleanPath, audioData.length);

            // 🔒 使用剪映专用桶上传音频文件（无CDN回源）
            PutObjectInput putObjectInput = new PutObjectInput()
                    .setBucket(jianyingBucket)
                    .setKey(cleanPath)
                    .setContent(new ByteArrayInputStream(audioData))
                    .setContentLength((long) audioData.length);

            PutObjectOutput putObjectOutput = tosClient.putObject(putObjectInput);

            log.info("音频文件上传成功: {}, ETag: {}", cleanPath, putObjectOutput.getEtag());

        } catch (TosClientException e) {
            log.error("TOS客户端异常", e);
            throw new RuntimeException("音频文件上传失败: " + e.getMessage(), e);
        } catch (TosServerException e) {
            log.error("TOS服务器异常", e);
            throw new RuntimeException("音频文件上传失败: " + e.getMessage(), e);
        } catch (Exception e) {
            log.error("音频文件上传异常", e);
            throw new RuntimeException("音频文件上传失败", e);
        }
    }

    /**
     * 下载草稿文件内容（使用内网TOS SDK - 免费流量，专用桶）
     *
     * @param filePath 文件路径（如：jianying-assistant/drafts/2025/07/04/file.json）
     * @return 文件内容
     */
    public String downloadDraftFile(String filePath) {
        try {
            // 移除开头的斜杠（如果有）
            String cleanPath = filePath.startsWith("/") ? filePath.substring(1) : filePath;

            log.info("开始使用内网TOS SDK下载剪映文件: {}", cleanPath);

            // 🔒 剪映草稿文件必须使用内网TOS客户端（免费流量）
            if (internalTosClient == null) {
                log.error("内网TOS客户端未初始化，剪映草稿文件无法下载");
                throw new RuntimeException("内网TOS客户端未初始化，请检查网络环境或配置");
            }

            // 🔒 使用剪映专用桶（无CDN回源）
            GetObjectV2Input getObjectInput = new GetObjectV2Input()
                    .setBucket(jianyingBucket)
                    .setKey(cleanPath);

            GetObjectV2Output getObjectOutput = internalTosClient.getObject(getObjectInput);

            if (getObjectOutput != null && getObjectOutput.getContent() != null) {
                // 读取输入流内容
                try (InputStream inputStream = getObjectOutput.getContent();
                     java.io.ByteArrayOutputStream outputStream = new java.io.ByteArrayOutputStream()) {

                    byte[] buffer = new byte[1024];
                    int length;
                    while ((length = inputStream.read(buffer)) != -1) {
                        outputStream.write(buffer, 0, length);
                    }

                    String content = outputStream.toString(StandardCharsets.UTF_8.name());
                    log.info("TOS SDK下载成功: {}, 大小: {} 字符", cleanPath, content.length());
                    return content;
                }
            } else {
                throw new RuntimeException("TOS SDK下载失败，返回内容为空");
            }

        } catch (TosClientException e) {
            log.error("TOS客户端异常: {}", filePath, e);
            throw new RuntimeException("TOS下载失败: " + e.getMessage(), e);
        } catch (TosServerException e) {
            log.error("TOS服务器异常: {}", filePath, e);
            throw new RuntimeException("TOS下载失败: " + e.getMessage(), e);
        } catch (Exception e) {
            log.error("TOS下载异常: {}", filePath, e);
            throw new RuntimeException("TOS下载失败: " + e.getMessage(), e);
        }
    }

    /**
     * 通用文件上传到TOS（用于系统文件上传）
     *
     * @param file 上传的文件
     * @param bizPath 业务路径（如：temp、avatar等）
     * @return 文件在数据库中存储的相对路径
     */
    public String uploadGeneralFile(MultipartFile file, String bizPath) throws Exception {
        try {
            // 生成文件路径：uploads/bizPath/yyyy/MM/dd/filename
            LocalDateTime now = LocalDateTime.now();
            String datePath = now.format(DateTimeFormatter.ofPattern("yyyy/MM/dd"));
            String fileName = generateUniqueFileName(file.getOriginalFilename());
            String objectKey = generalBasePath + "/" + bizPath + "/" + datePath + "/" + fileName;

            log.info("上传通用文件到TOS，Key: {}", objectKey);

            // 上传到TOS
            PutObjectInput putObjectInput = new PutObjectInput()
                    .setBucket(generalBucket)
                    .setKey(objectKey)
                    .setContent(file.getInputStream())
                    .setContentLength(file.getSize());

            PutObjectOutput putObjectOutput = tosClient.putObject(putObjectInput);

            log.info("通用文件上传成功: {}, ETag: {}", objectKey, putObjectOutput.getEtag());

            // 返回相对路径，用于数据库存储
            return objectKey;

        } catch (Exception e) {
            log.error("通用文件上传失败", e);
            throw new RuntimeException("文件上传失败: " + e.getMessage(), e);
        }
    }

    /**
     * 生成文件的签名访问URL（私有访问）
     * 兼容方法，委托给generateFileUrl
     *
     * @param objectKey TOS对象键（如：uploads/temp/2025/01/11/xxx.jpg）
     * @return 签名访问URL
     */
    public String generateSignedUrl(String objectKey) {
        return generateFileUrl(objectKey);
    }

    /**
     * 删除TOS中的文件（智能选择桶）
     *
     * @param objectKey TOS对象键（如：uploads/avatar/2025/01/11/xxx.jpg 或 jianying-assistant/drafts/xxx.json）
     * @return 是否删除成功
     */
    public boolean deleteFile(String objectKey) {
        try {
            // 清理objectKey，确保格式正确
            String cleanKey = objectKey.startsWith("/") ? objectKey.substring(1) : objectKey;

            log.info("开始删除TOS文件: {}", cleanKey);

            // 🔒 智能选择桶：剪映文件使用专用桶，其他文件使用通用桶
            String targetBucket = cleanKey.startsWith("jianying-assistant/") ? jianyingBucket : generalBucket;
            log.info("使用桶: {}", targetBucket);

            // 删除文件
            DeleteObjectInput input = new DeleteObjectInput()
                    .setBucket(targetBucket)
                    .setKey(cleanKey);

            DeleteObjectOutput output = tosClient.deleteObject(input);

            log.info("TOS文件删除成功: {}", cleanKey);
            return true;

        } catch (Exception e) {
            log.error("TOS文件删除失败: {}", objectKey, e);
            return false;
        }
    }

    /**
     * 删除旧头像文件（如果是TOS文件）
     *
     * @param oldAvatarPath 旧头像路径
     */
    public void deleteOldAvatarIfExists(String oldAvatarPath) {
        if (oldAvatarPath != null && oldAvatarPath.startsWith("uploads/")) {
            log.info("检测到旧头像为TOS文件，准备删除: {}", oldAvatarPath);
            boolean deleted = deleteFile(oldAvatarPath);
            if (deleted) {
                log.info("旧头像删除成功: {}", oldAvatarPath);
            } else {
                log.warn("旧头像删除失败: {}", oldAvatarPath);
            }
        } else {
            log.debug("旧头像不是TOS文件，跳过删除: {}", oldAvatarPath);
        }
    }

    /**
     * 生成唯一文件名
     */
    private String generateUniqueFileName(String originalFilename) {
        String suffix = "";
        if (originalFilename != null && originalFilename.contains(".")) {
            suffix = originalFilename.substring(originalFilename.lastIndexOf("."));
        }
        return System.currentTimeMillis() + "_" + UUID.randomUUID().toString().substring(0, 8) + suffix;
    }

    /**
     * 测试TOS连接
     */
    private void testTosConnection() {
        try {
            // 尝试列出桶信息来测试连接
            log.info("正在测试TOS连接...");
            // 这里可以添加一个简单的桶操作来验证连接
            log.info("TOS连接测试成功！");
        } catch (Exception e) {
            log.warn("TOS连接测试失败，但不影响初始化: {}", e.getMessage());
        }
    }



    /**
     * 🆕 获取默认头像URL（优先CDN）
     */
    public String getDefaultAvatarUrl() {
        try {
            String defaultAvatarKey = "uploads/system/default-avatar.png";
            return generateFileUrl(defaultAvatarKey);
        } catch (Exception e) {
            log.error("获取默认头像URL失败", e);
            // 降级到本地默认头像
            return "/default-avatar.png";
        }
    }

    /**
     * 🆕 获取默认插件图片URL（优先CDN）
     */
    public String getDefaultPluginImageUrl() {
        try {
            String defaultPluginImageKey = "defaults/plugin-default.jpg";
            return generateFileUrl(defaultPluginImageKey);
        } catch (Exception e) {
            log.error("获取默认插件图片URL失败", e);
            // 降级到本地默认插件图片
            return "/plugImg.jpg";
        }
    }

    /**
     * 🔒 判断是否为剪映草稿文件
     *
     * @param filePath 文件路径
     * @return 是否为剪映草稿文件
     */
    private boolean isJianyingDraftFile(String filePath) {
        return filePath != null && filePath.startsWith("jianying-assistant/drafts/");
    }

    /**
     * 🔒 使用内网TOS客户端生成签名URL（免费流量）
     *
     * @param filePath 文件路径
     * @return 签名URL
     */
    private String generateInternalTosSignedUrl(String filePath) {
        try {
            if (internalTosClient == null) {
                log.error("内网TOS客户端未初始化，剪映草稿文件无法访问");
                throw new RuntimeException("内网TOS客户端未初始化，请检查网络环境或配置");
            }

            // 🔒 使用内网TOS客户端生成剪映专用桶的签名URL
            PreSignedURLInput input = new PreSignedURLInput()
                    .setBucket(jianyingBucket)  // 🔧 修复：使用剪映专用桶
                    .setKey(filePath)
                    .setHttpMethod("GET")
                    .setExpires(signedUrlExpireHours * 3600); // 转换为秒

            PreSignedURLOutput output = internalTosClient.preSignedURL(input);
            String signedUrl = output.getSignedUrl();

            log.debug("生成内网TOS签名URL成功，文件路径: {}", filePath);
            return signedUrl;

        } catch (Exception e) {
            log.error("生成内网TOS签名URL失败: {}", filePath, e);
            // 🚨 不降级，直接抛出异常
            throw new RuntimeException("内网TOS访问失败: " + e.getMessage(), e);
        }
    }

    /**
     * 删除过期文件（定时任务调用，支持双桶清理）
     */
    public void cleanupExpiredFiles() {
        try {
            log.info("开始清理过期文件，保留天数: {}", tosConfig.getFileRetentionDays());

            // 计算过期时间
            LocalDateTime expireTime = LocalDateTime.now().minusDays(tosConfig.getFileRetentionDays());
            String expireDateStr = expireTime.format(DateTimeFormatter.ofPattern("yyyy/MM/dd"));

            // 🔒 清理剪映专用桶中的过期文件
            cleanupExpiredFilesInBucket(jianyingBucket, "jianying-assistant/drafts/", expireDateStr);

            log.info("过期文件清理完成");

        } catch (Exception e) {
            log.error("清理过期文件失败", e);
        }
    }

    /**
     * 清理指定桶中的过期文件
     */
    private void cleanupExpiredFilesInBucket(String bucket, String prefix, String expireDateStr) {
        try {
            log.info("清理桶 {} 中前缀为 {} 的过期文件（早于 {}）", bucket, prefix, expireDateStr);

            // 🔧 实现文件列举和删除逻辑
            // 注意：这里只是框架，具体实现需要根据业务需求调整

            // 1. 列举指定前缀的文件
            // ListObjectsV2Input listInput = new ListObjectsV2Input()
            //     .setBucket(bucket)
            //     .setPrefix(prefix)
            //     .setMaxKeys(1000);

            // 2. 遍历文件，检查创建时间
            // 3. 删除过期文件

            log.info("桶 {} 中的过期文件清理完成", bucket);

        } catch (Exception e) {
            log.error("清理桶 {} 中的过期文件失败", bucket, e);
        }
    }
}
