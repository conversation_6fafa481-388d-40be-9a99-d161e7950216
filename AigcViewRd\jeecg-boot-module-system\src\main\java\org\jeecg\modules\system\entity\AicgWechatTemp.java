package org.jeecg.modules.system.entity;

import java.io.Serializable;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 微信登录临时数据表
 * @Author: jeecg-boot
 * @Date: 2025-06-19
 * @Version: V1.0
 */
@Data
@TableName("aicg_wechat_temp")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="aicg_wechat_temp对象", description="微信登录临时数据表")
public class AicgWechatTemp implements Serializable {
    private static final long serialVersionUID = 1L;

    /**主键*/
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String id;
    
    /**场景ID*/
    @Excel(name = "场景ID", width = 20)
    @ApiModelProperty(value = "场景ID")
    private String sceneId;
    
    /**场景类型*/
    @Excel(name = "场景类型", width = 15)
    @ApiModelProperty(value = "场景类型：login-登录,register-注册")
    private String sceneType;
    
    /**邀请码*/
    @Excel(name = "邀请码", width = 15)
    @ApiModelProperty(value = "邀请码")
    private String inviteCode;
    
    /**微信OpenID*/
    @Excel(name = "微信OpenID", width = 30)
    @ApiModelProperty(value = "微信OpenID")
    private String openid;
    
    /**微信昵称*/
    @Excel(name = "微信昵称", width = 20)
    @ApiModelProperty(value = "微信昵称")
    private String nickname;
    
    /**微信头像*/
    @Excel(name = "微信头像", width = 50)
    @ApiModelProperty(value = "微信头像")
    private String avatar;
    
    /**状态*/
    @Excel(name = "状态", width = 15)
    @ApiModelProperty(value = "状态：0-等待扫码,1-已扫码,2-已授权,3-已完成")
    private Integer status;
    
    /**过期时间*/
    @Excel(name = "过期时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "过期时间")
    private Date expireTime;
    
    /**创建时间*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    
    /**更新时间*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
}
