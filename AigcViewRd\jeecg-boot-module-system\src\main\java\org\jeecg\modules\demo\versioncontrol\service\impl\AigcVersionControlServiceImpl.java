package org.jeecg.modules.demo.versioncontrol.service.impl;

import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import org.jeecg.modules.demo.versioncontrol.entity.AigcVersionControl;
import org.jeecg.modules.demo.versioncontrol.mapper.AigcVersionControlMapper;
import org.jeecg.modules.demo.versioncontrol.service.IAigcVersionControlService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;

/**
 * @Description: 程序版本控制表
 * @Author: jeecg-boot
 * @Date: 2025-01-09
 * @Version: V1.0
 */
@Service
@Slf4j
public class AigcVersionControlServiceImpl extends ServiceImpl<AigcVersionControlMapper, AigcVersionControl> implements IAigcVersionControlService {

    @Autowired
    private AigcVersionControlMapper aigcVersionControlMapper;

    // 版本号正则表达式：支持 x.y.z 格式
    private static final Pattern VERSION_PATTERN = Pattern.compile("^\\d+\\.\\d+\\.\\d+$");

    @Override
    public AigcVersionControl getLatestByProgramType(String programType) {
        return aigcVersionControlMapper.getLatestByProgramType(programType);
    }

    @Override
    public List<AigcVersionControl> getVersionsByProgramType(String programType) {
        return aigcVersionControlMapper.getVersionsByProgramType(programType);
    }

    @Override
    public AigcVersionControl getByProgramTypeAndVersion(String programType, String versionNumber) {
        return aigcVersionControlMapper.getByProgramTypeAndVersion(programType, versionNumber);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean setAsLatest(String id, String updateBy) {
        try {
            // 1. 获取要设置的版本信息
            AigcVersionControl version = this.getById(id);
            if (version == null) {
                log.error("版本不存在，ID: {}", id);
                return false;
            }

            // 2. 先将同类型的所有版本设为非最新
            int clearResult = aigcVersionControlMapper.clearLatestByProgramType(version.getProgramType(), updateBy);
            log.info("清除程序类型 {} 的最新版本标记，影响记录数: {}", version.getProgramType(), clearResult);

            // 3. 设置指定版本为最新
            int setResult = aigcVersionControlMapper.setAsLatest(id, updateBy);
            log.info("设置版本为最新，ID: {}, 影响记录数: {}", id, setResult);

            return setResult > 0;
        } catch (Exception e) {
            log.error("设置最新版本失败，ID: {}, 错误: {}", id, e.getMessage(), e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchUpdateStatus(List<String> ids, Integer status, String updateBy) {
        try {
            if (ids == null || ids.isEmpty()) {
                return false;
            }
            int result = aigcVersionControlMapper.batchUpdateStatus(ids, status, updateBy);
            log.info("批量更新状态，影响记录数: {}", result);
            return result > 0;
        } catch (Exception e) {
            log.error("批量更新状态失败，错误: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public List<String> getAllProgramTypes() {
        return aigcVersionControlMapper.getAllProgramTypes();
    }

    @Override
    public List<Map<String, Object>> countByProgramType() {
        return aigcVersionControlMapper.countByProgramType();
    }

    @Override
    public List<AigcVersionControl> getRecentVersions(Integer limit) {
        if (limit == null || limit <= 0) {
            limit = 10; // 默认查询10条
        }
        return aigcVersionControlMapper.getRecentVersions(limit);
    }

    @Override
    public boolean checkVersionExists(String programType, String versionNumber, String excludeId) {
        int count = aigcVersionControlMapper.checkVersionExists(programType, versionNumber, excludeId);
        return count > 0;
    }

    @Override
    public List<AigcVersionControl> getVersionsByDateRange(String startDate, String endDate) {
        return aigcVersionControlMapper.getVersionsByDateRange(startDate, endDate);
    }

    @Override
    public List<AigcVersionControl> searchVersions(String keyword) {
        if (keyword == null || keyword.trim().isEmpty()) {
            return new ArrayList<>();
        }
        return aigcVersionControlMapper.searchVersions(keyword.trim());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addVersion(AigcVersionControl versionControl, String createBy) {
        try {
            // 1. 验证版本号格式
            if (!validateVersionNumber(versionControl.getVersionNumber())) {
                log.error("版本号格式不正确: {}", versionControl.getVersionNumber());
                return false;
            }

            // 2. 检查版本号是否已存在
            if (checkVersionExists(versionControl.getProgramType(), versionControl.getVersionNumber(), null)) {
                log.error("版本号已存在: {} - {}", versionControl.getProgramType(), versionControl.getVersionNumber());
                return false;
            }

            // 3. 设置创建信息
            versionControl.setCreateBy(createBy);
            versionControl.setCreateTime(new Date());
            
            // 4. 如果设置为最新版本，需要先清除同类型的其他最新版本
            if (versionControl.isLatestVersion()) {
                // 先将同类型的所有版本设为非最新（2-否）
                aigcVersionControlMapper.clearLatestByProgramType(versionControl.getProgramType(), createBy);
            }

            // 5. 保存版本信息
            boolean result = this.save(versionControl);
            log.info("新增版本成功: {} - {}", versionControl.getProgramType(), versionControl.getVersionNumber());
            return result;
        } catch (Exception e) {
            log.error("新增版本失败，错误: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateVersion(AigcVersionControl versionControl, String updateBy) {
        try {
            // 1. 验证版本号格式
            if (!validateVersionNumber(versionControl.getVersionNumber())) {
                log.error("版本号格式不正确: {}", versionControl.getVersionNumber());
                return false;
            }

            // 2. 检查版本号是否已存在（排除自己）
            if (checkVersionExists(versionControl.getProgramType(), versionControl.getVersionNumber(), versionControl.getId())) {
                log.error("版本号已存在: {} - {}", versionControl.getProgramType(), versionControl.getVersionNumber());
                return false;
            }

            // 3. 设置更新信息
            versionControl.setUpdateBy(updateBy);
            versionControl.setUpdateTime(new Date());

            // 4. 如果设置为最新版本，需要先清除同类型的其他最新版本
            if (versionControl.isLatestVersion()) {
                aigcVersionControlMapper.clearLatestByProgramType(versionControl.getProgramType(), updateBy);
            }

            // 5. 更新版本信息
            boolean result = this.updateById(versionControl);
            log.info("更新版本成功: {} - {}", versionControl.getProgramType(), versionControl.getVersionNumber());
            return result;
        } catch (Exception e) {
            log.error("更新版本失败，错误: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteVersion(String id) {
        try {
            // 1. 获取版本信息
            AigcVersionControl version = this.getById(id);
            if (version == null) {
                log.error("版本不存在，ID: {}", id);
                return false;
            }

            // 2. 如果是最新版本，需要检查是否有其他版本可以设为最新
            if (version.isLatestVersion()) {
                List<AigcVersionControl> otherVersions = getVersionsByProgramType(version.getProgramType())
                    .stream()
                    .filter(v -> !v.getId().equals(id))
                    .collect(Collectors.toList());
                
                if (!otherVersions.isEmpty()) {
                    // 将最新的版本设为最新版本
                    AigcVersionControl latestVersion = otherVersions.get(0);
                    latestVersion.setAsLatest();
                    this.updateById(latestVersion);
                    log.info("删除最新版本后，自动设置新的最新版本: {}", latestVersion.getVersionNumber());
                }
            }

            // 3. 删除版本
            boolean result = this.removeById(id);
            log.info("删除版本成功，ID: {}", id);
            return result;
        } catch (Exception e) {
            log.error("删除版本失败，ID: {}, 错误: {}", id, e.getMessage(), e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchDeleteVersions(List<String> ids) {
        try {
            if (ids == null || ids.isEmpty()) {
                return false;
            }

            // 逐个删除，确保业务逻辑正确执行
            for (String id : ids) {
                deleteVersion(id);
            }

            log.info("批量删除版本成功，数量: {}", ids.size());
            return true;
        } catch (Exception e) {
            log.error("批量删除版本失败，错误: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public Map<String, Object> getVersionComparison(String programType, String fromVersion, String toVersion) {
        Map<String, Object> result = new HashMap<>();
        
        AigcVersionControl from = getByProgramTypeAndVersion(programType, fromVersion);
        AigcVersionControl to = getByProgramTypeAndVersion(programType, toVersion);
        
        result.put("fromVersion", from);
        result.put("toVersion", to);
        result.put("hasComparison", from != null && to != null);
        
        return result;
    }

    @Override
    public Map<String, Object> getProgramTypeStatistics(String programType) {
        Map<String, Object> result = new HashMap<>();
        
        List<AigcVersionControl> versions = getVersionsByProgramType(programType);
        AigcVersionControl latest = getLatestByProgramType(programType);
        
        result.put("totalVersions", versions.size());
        result.put("latestVersion", latest);
        result.put("versions", versions);
        
        return result;
    }

    @Override
    public List<AigcVersionControl> exportVersions(String programType) {
        if (programType != null && !programType.trim().isEmpty()) {
            return getVersionsByProgramType(programType);
        } else {
            return this.list();
        }
    }

    @Override
    public boolean validateVersionNumber(String versionNumber) {
        if (versionNumber == null || versionNumber.trim().isEmpty()) {
            return false;
        }
        return VERSION_PATTERN.matcher(versionNumber.trim()).matches();
    }

    @Override
    public String getNextSuggestedVersion(String programType, String versionType) {
        AigcVersionControl latest = getLatestByProgramType(programType);
        if (latest == null) {
            return "1.0.0"; // 默认初始版本
        }
        
        String currentVersion = latest.getVersionNumber();
        String[] parts = currentVersion.split("\\.");
        
        if (parts.length != 3) {
            return "1.0.0";
        }
        
        try {
            int major = Integer.parseInt(parts[0]);
            int minor = Integer.parseInt(parts[1]);
            int patch = Integer.parseInt(parts[2]);
            
            switch (versionType.toLowerCase()) {
                case "major":
                    return (major + 1) + ".0.0";
                case "minor":
                    return major + "." + (minor + 1) + ".0";
                case "patch":
                default:
                    return major + "." + minor + "." + (patch + 1);
            }
        } catch (NumberFormatException e) {
            log.error("解析版本号失败: {}", currentVersion);
            return "1.0.0";
        }
    }
}
