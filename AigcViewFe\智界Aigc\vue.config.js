// vue.config.js - 终极性能优化版
const CompressionPlugin = require('compression-webpack-plugin');
const { BundleAnalyzerPlugin } = require('webpack-bundle-analyzer');

module.exports = {
  publicPath: '/',
  productionSourceMap: false,
  lintOnSave: false,

  configureWebpack: config => {
    // 现有的alias配置
    config.resolve.alias = {
      '@': require('path').resolve(__dirname, 'src'),
      '@api': require('path').resolve(__dirname, 'src/api'),
      '@comp': require('path').resolve(__dirname, 'src/components'),
      '@views': require('path').resolve(__dirname, 'src/views'),
      '@assets': require('path').resolve(__dirname, 'src/assets'),
      '@utils': require('path').resolve(__dirname, 'src/utils')
    };

    // 生产环境优化
    if (process.env.NODE_ENV === 'production') {
      // Gzip压缩
      config.plugins.push(
        new CompressionPlugin({
          algorithm: 'gzip',
          test: /\.(js|css|html|svg)$/,
          threshold: 8192,
          minRatio: 0.8,
          deleteOriginalAssets: false
        })
      );

      // 包分析工具（可选，用于分析包大小）
      if (process.env.ANALYZE) {
        config.plugins.push(new BundleAnalyzerPlugin());
      }

      // 激进的代码分割优化 - 减少chunk数量
      config.optimization = {
        ...config.optimization,
        splitChunks: {
          chunks: 'all',
          minSize: 30000,
          maxSize: 500000,
          minChunks: 1,
          maxAsyncRequests: 10,  // 减少异步请求数量
          maxInitialRequests: 5, // 减少初始请求数量
          cacheGroups: {
            default: false,
            vendors: false,
            // 合并所有第三方库到一个文件
            vendor: {
              test: /[\\/]node_modules[\\/]/,
              name: 'vendors',
              chunks: 'all',
              priority: 10,
              enforce: true
            },
            // 合并所有公共代码到一个文件
            common: {
              name: 'common',
              minChunks: 2,
              chunks: 'all',
              priority: 5,
              enforce: true
            }
          }
        }
      };

      // 移除console.log
      if (config.optimization.minimizer && config.optimization.minimizer[0]) {
        config.optimization.minimizer[0].options.terserOptions.compress.drop_console = true;
      }
    }
  },

  // 链式配置
  chainWebpack: config => {
    // 预加载优化
    config.plugin('preload').tap(options => {
      options[0] = {
        rel: 'preload',
        include: 'initial',
        fileBlacklist: [/\.map$/, /hot-update\.js$/]
      };
      return options;
    });

    // 预获取优化
    config.plugin('prefetch').tap(options => {
      options[0].fileBlacklist = options[0].fileBlacklist || [];
      options[0].fileBlacklist.push(/runtime\..*\.js$/);
      return options;
    });
  },

  css: {
    loaderOptions: {
      less: {
        modifyVars: {
          'primary-color': '#1890FF',
          'link-color': '#1890FF',
          'border-radius-base': '4px',
        },
        javascriptEnabled: true,
      }
    }
  },

  devServer: {
    port: 3000,
    proxy: {
      '/jeecg-boot': {
        target: 'http://localhost:8080',
        ws: false,
        changeOrigin: true
      },
    }
  }
}