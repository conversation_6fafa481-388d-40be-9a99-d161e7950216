import { axios } from '@/utils/request'

const api = {
  // 用户扩展信息相关接口
  userProfile: '/demo/userprofile',
  userTransactions: '/demo/userprofile/transactions',
  updateNickname: '/demo/userprofile/updateNickname',
  regenerateApiKey: '/demo/userprofile/regenerateApiKey',
  adminRecharge: '/demo/userprofile/adminRecharge',

  // 兑换码相关接口
  exchangeCode: '/demo/exchangecode',
  useExchangeCode: '/demo/exchangecode/use',
  validateExchangeCode: '/demo/exchangecode/validate',
  userUsedCodes: '/demo/exchangecode/userUsedCodes',
  generateExchangeCode: '/demo/exchangecode/generate',
  updateExpiredCodes: '/demo/exchangecode/updateExpired',

  // 交易记录相关接口
  userRecord: '/demo/userrecord',

  // 仪表板数据接口
  dashboardData: '/api/aigc/dashboard-data',

  // 桌面应用下载统计接口
  desktopDownloadStats: '/sys/common/desktop-app-download-stats',

  // 用户中心验证码接口
  userCenterSmsCode: '/api/usercenter/sendSmsCode',
  userCenterEmailCode: '/api/usercenter/sendEmailCode',
  userCenterVerifyCode: '/api/usercenter/verifyCode',

  // 昵称校验接口
  validateNickname: '/api/usercenter/validateNickname',
}

/**
 * 获取当前用户的扩展信息
 */
export function getUserProfile() {
  return axios({
    url: api.userProfile + '/current',
    method: 'get'
  })
}

/**
 * 获取用户交易记录
 * @param {Object} params - 查询参数
 * @param {number} params.pageNo - 页码
 * @param {number} params.pageSize - 每页大小
 */
export function getUserTransactions(params) {
  return axios({
    url: api.userTransactions,
    method: 'get',
    params
  })
}

/**
 * 更新用户昵称
 * @param {Object} data - 请求数据
 * @param {string} data.nickname - 新昵称
 */
export function updateNickname(data) {
  return axios({
    url: api.updateNickname,
    method: 'post',
    params: data
  })
}

/**
 * 重新生成API密钥
 */
export function regenerateApiKey() {
  return axios({
    url: api.regenerateApiKey,
    method: 'post'
  })
}

/**
 * 管理员充值接口
 * @param {Object} data - 充值数据
 * @param {string} data.userId - 用户ID
 * @param {number} data.amount - 充值金额
 * @param {string} data.description - 充值描述
 */
export function adminRecharge(data) {
  return axios({
    url: api.adminRecharge,
    method: 'post',
    params: data
  })
}

/**
 * 使用兑换码
 * @param {Object} data - 兑换码数据
 * @param {string} data.code - 兑换码
 */
export function useExchangeCode(data) {
  return axios({
    url: api.useExchangeCode,
    method: 'post',
    params: data
  })
}

/**
 * 验证兑换码
 * @param {Object} params - 查询参数
 * @param {string} params.code - 兑换码
 */
export function validateExchangeCode(params) {
  return axios({
    url: api.validateExchangeCode,
    method: 'get',
    params
  })
}

/**
 * 获取用户使用的兑换码记录
 */
export function getUserUsedCodes() {
  return axios({
    url: api.userUsedCodes,
    method: 'get'
  })
}

/**
 * 生成兑换码
 * @param {Object} data - 生成参数
 * @param {number} data.codeType - 兑换码类型：1-余额，2-会员，3-积分
 * @param {number} data.value - 兑换价值
 * @param {string} data.expireTime - 过期时间
 * @param {string} data.batchNo - 批次号
 * @param {number} data.count - 生成数量
 */
export function generateExchangeCode(data) {
  return axios({
    url: api.generateExchangeCode,
    method: 'post',
    params: data
  })
}

/**
 * 更新过期兑换码状态
 */
export function updateExpiredCodes() {
  return axios({
    url: api.updateExpiredCodes,
    method: 'post'
  })
}

/**
 * 获取兑换码列表
 * @param {Object} params - 查询参数
 */
export function getExchangeCodeList(params) {
  return axios({
    url: api.exchangeCode + '/list',
    method: 'get',
    params
  })
}

/**
 * 添加兑换码
 * @param {Object} data - 兑换码数据
 */
export function addExchangeCode(data) {
  return axios({
    url: api.exchangeCode + '/add',
    method: 'post',
    data
  })
}

/**
 * 编辑兑换码
 * @param {Object} data - 兑换码数据
 */
export function editExchangeCode(data) {
  return axios({
    url: api.exchangeCode + '/edit',
    method: 'put',
    data
  })
}

/**
 * 删除兑换码
 * @param {string} id - 兑换码ID
 */
export function deleteExchangeCode(id) {
  return axios({
    url: api.exchangeCode + '/delete',
    method: 'delete',
    params: { id }
  })
}

/**
 * 批量删除兑换码
 * @param {string} ids - 兑换码ID列表，逗号分隔
 */
export function batchDeleteExchangeCode(ids) {
  return axios({
    url: api.exchangeCode + '/deleteBatch',
    method: 'delete',
    params: { ids }
  })
}

/**
 * 获取用户扩展信息列表
 * @param {Object} params - 查询参数
 */
export function getUserProfileList(params) {
  return axios({
    url: api.userProfile + '/list',
    method: 'get',
    params
  })
}

/**
 * 添加用户扩展信息
 * @param {Object} data - 用户扩展信息数据
 */
export function addUserProfile(data) {
  return axios({
    url: api.userProfile + '/add',
    method: 'post',
    data
  })
}

/**
 * 编辑用户扩展信息
 * @param {Object} data - 用户扩展信息数据
 */
export function editUserProfile(data) {
  return axios({
    url: api.userProfile + '/edit',
    method: 'put',
    data
  })
}

/**
 * 删除用户扩展信息
 * @param {string} id - 用户扩展信息ID
 */
export function deleteUserProfile(id) {
  return axios({
    url: api.userProfile + '/delete',
    method: 'delete',
    params: { id }
  })
}

/**
 * 获取交易记录列表
 * @param {Object} params - 查询参数
 */
export function getUserRecordList(params) {
  return axios({
    url: api.userRecord + '/list',
    method: 'get',
    params
  })
}

/**
 * 添加交易记录
 * @param {Object} data - 交易记录数据
 */
export function addUserRecord(data) {
  return axios({
    url: api.userRecord + '/add',
    method: 'post',
    data
  })
}

/**
 * 编辑交易记录
 * @param {Object} data - 交易记录数据
 */
export function editUserRecord(data) {
  return axios({
    url: api.userRecord + '/edit',
    method: 'put',
    data
  })
}

/**
 * 删除交易记录
 * @param {string} id - 交易记录ID
 */
export function deleteUserRecord(id) {
  return axios({
    url: api.userRecord + '/delete',
    method: 'delete',
    params: { id }
  })
}

/**
 * 用户充值接口（对接支付）
 * @param {Object} data - 充值数据
 * @param {number} data.amount - 充值金额
 * @param {string} data.paymentMethod - 支付方式
 */
export function userRecharge(data) {
  return axios({
    url: '/api/payment/recharge',
    method: 'post',
    data
  })
}

/**
 * 获取用户余额
 */
export function getUserBalance() {
  return axios({
    url: api.userProfile + '/balance',
    method: 'get'
  })
}

/**
 * 检查用户余额是否足够
 * @param {Object} params - 查询参数
 * @param {number} params.amount - 需要的金额
 */
export function checkUserBalance(params) {
  return axios({
    url: api.userProfile + '/checkBalance',
    method: 'get',
    params
  })
}

/**
 * 获取用户统计信息
 */
export function getUserStats() {
  return axios({
    url: api.userProfile + '/stats',
    method: 'get'
  })
}

/**
 * 获取仪表板数据
 * @param {Object} params - 查询参数
 * @param {string} params.timeRange - 时间范围：today、week、month
 */
export function getDashboardData(params) {
  return axios({
    url: api.dashboardData,
    method: 'get',
    params
  })
}

/**
 * 获取桌面应用下载统计数据（仅管理员）
 * @param {Object} params - 查询参数
 * @param {number} params.days - 统计天数，默认7天
 */
export function getDesktopDownloadStats(params) {
  return axios({
    url: api.desktopDownloadStats,
    method: 'get',
    params
  })
}

// ==================== 官网个人中心专用接口 ====================

/**
 * 获取个人中心概览数据
 */
export function getUserCenterOverview() {
  return axios({
    url: '/api/usercenter/overview',
    method: 'get'
  })
}

/**
 * 获取用户完整信息
 */
export function getUserFullInfo() {
  return axios({
    url: '/api/usercenter/userFullInfo',
    method: 'get'
  })
}

/**
 * 获取最近活动记录
 */
export function getRecentActivities(params) {
  return axios({
    url: '/api/usercenter/recentActivities',
    method: 'get',
    params
  })
}



/**
 * 更新用户基本信息
 */
export function updateUserInfo(data) {
  return axios({
    url: '/api/usercenter/updateUserInfo',
    method: 'post',
    data
  })
}

/**
 * 更新用户扩展信息
 */
export function updateUserProfile(data) {
  return axios({
    url: '/api/usercenter/updateUserProfile',
    method: 'post',
    data
  })
}

/**
 * 获取消费趋势图表数据
 */
export function getConsumptionChart(params) {
  return axios({
    url: '/api/usercenter/consumptionChart',
    method: 'get',
    params
  })
}

/**
 * 修改用户密码
 */
export function changeUserPassword(data) {
  return axios({
    url: '/api/usercenter/changePassword',
    method: 'post',
    data
  })
}

/**
 * 上传头像文件 - 使用标准文件上传接口
 */
export function uploadAvatarFile(file) {
  const formData = new FormData()
  formData.append('file', file)
  formData.append('biz', 'avatar') // 业务类型

  return axios({
    url: '/sys/common/upload',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

/**
 * 更新用户头像到数据库
 */
export function updateUserAvatarUrl(avatarUrl) {
  return axios({
    url: '/api/usercenter/updateAvatarUrl',
    method: 'post',
    data: { avatar: avatarUrl }
  })
}

/**
 * 获取交易记录列表
 */
export function getTransactionList(params) {
  return axios({
    url: '/api/usercenter/transactionList',
    method: 'get',
    params
  })
}

/**
 * 获取交易统计数据
 */
export function getTransactionStats() {
  return axios({
    url: '/api/usercenter/transactionStats',
    method: 'get'
  })
}

/**
 * 导出交易记录
 * @param {Object} params - 导出参数
 * @param {string} params.transactionType - 交易类型
 * @param {string} params.status - 交易状态
 * @param {string} params.startDate - 开始日期
 * @param {string} params.endDate - 结束日期
 * @param {string} params.keyword - 搜索关键词
 */
export function exportTransactions(params) {
  return axios({
    url: '/api/usercenter/exportTransactions',
    method: 'get',
    params,
    responseType: 'blob' // 重要：设置响应类型为blob用于文件下载
  })
}

/**
 * 获取充值选项配置
 */
export function getRechargeOptions() {
  return axios({
    url: '/api/usercenter/rechargeOptions',
    method: 'get'
  })
}

/**
 * 创建充值订单
 */
export function createRechargeOrder(data) {
  return axios({
    url: '/api/usercenter/createRechargeOrder',
    method: 'post',
    data
  })
}

/**
 * 获取会员信息
 */
export function getMembershipInfo() {
  return axios({
    url: '/api/usercenter/membership',
    method: 'get'
  })
}

/**
 * 获取会员等级配置
 */
export function getMembershipLevels() {
  return axios({
    url: '/api/usercenter/membershipLevels',
    method: 'get'
  })
}

/**
 * 会员升级
 */
export function upgradeMembership(data) {
  return axios({
    url: '/api/usercenter/upgradeMembership',
    method: 'post',
    data
  })
}

/**
 * 获取会员历史记录
 */
export function getMembershipHistory(params) {
  return axios({
    url: '/api/usercenter/membershipHistory',
    method: 'get',
    params
  })
}

/**
 * 获取推荐统计信息
 */
export function getReferralStats() {
  return axios({
    url: '/api/usercenter/referralStats',
    method: 'get'
  })
}

/**
 * 生成推荐链接
 */
export function generateReferralLink(data) {
  return axios({
    url: '/api/usercenter/generateReferralLink',
    method: 'post',
    data
  })
}

/**
 * 获取用户角色信息
 */
export function getUserRole() {
  return axios({
    url: '/api/usercenter/userRole',
    method: 'get'
  })
}

/**
 * 获取等级配置信息
 */
export function getLevelConfig() {
  return axios({
    url: '/api/usercenter/levelConfig',
    method: 'get'
  })
}

/**
 * 获取用户当前等级
 */
export function getCurrentLevel() {
  return axios({
    url: '/api/usercenter/currentLevel',
    method: 'get'
  })
}

/**
 * 获取推荐记录列表
 */
export function getReferralList(params) {
  return axios({
    url: '/api/usercenter/referralList',
    method: 'get',
    params
  })
}

/**
 * 申请提现
 */
export function requestWithdrawal(data) {
  return axios({
    url: '/api/usercenter/requestWithdrawal',
    method: 'post',
    data
  })
}

/**
 * 获取提现历史记录
 */
export function getWithdrawalHistory(params) {
  return axios({
    url: '/api/usercenter/withdrawalHistory',
    method: 'get',
    params
  })
}

/**
 * 获取订单记录列表
 */
export function getUserOrderList(params) {
  return axios({
    url: '/api/usercenter/orders',
    method: 'get',
    params
  })
}

/**
 * 获取订单详情
 */
export function getUserOrderDetail(orderId) {
  return axios({
    url: `/api/usercenter/orders/${orderId}`,
    method: 'get'
  })
}

/**
 * 获取订单统计数据
 */
export function getOrderStats() {
  return axios({
    url: '/api/usercenter/orderStats',
    method: 'get'
  })
}

/**
 * 导出订单数据
 */
export function exportOrders(params) {
  return axios({
    url: '/api/usercenter/exportOrders',
    method: 'get',
    params,
    responseType: 'blob' // 重要：设置响应类型为blob
  })
}

/**
 * 创建会员订阅订单
 */
export function createMembershipOrder(data) {
  return axios({
    url: '/api/usercenter/createMembershipOrder',
    method: 'post',
    data
  })
}

/**
 * 支付订单
 */
export function payOrder(data) {
  return axios({
    url: '/api/usercenter/payOrder',
    method: 'post',
    data
  })
}

/**
 * 获取API调用统计
 */
export function getApiUsageStats(params) {
  return axios({
    url: '/api/usercenter/apiUsageStats',
    method: 'get',
    params
  })
}

/**
 * 获取插件使用历史
 */
export function getPluginUsageHistory(params) {
  return axios({
    url: '/api/usercenter/pluginUsageHistory',
    method: 'get',
    params
  })
}

/**
 * 获取用户使用记录列表
 */
export function getUserUsageList(params) {
  return axios({
    url: '/api/usercenter/usageList',
    method: 'get',
    params
  })
}

/**
 * 导出使用记录数据
 */
export function exportUsageRecords(params) {
  return axios({
    url: '/api/usercenter/exportUsageRecords',
    method: 'get',
    params,
    responseType: 'blob' // 重要：设置响应类型为blob
  })
}

/**
 * 获取系统通知（调用后台接口）
 * 注意：不要在URL前加/jeecg-boot，因为axios已经配置了baseURL
 */
export function getSystemNotifications(params = {}) {
  return axios({
    url: '/sys/message/sysMessageTemplate/list',
    method: 'get',
    params: {
      templateType: '系统',
      pageNo: 1,
      pageSize: 20,
      ...params
    }
  })
}

// ==================== 用户中心验证码接口 ====================

/**
 * 发送短信验证码（用户中心专用，需要token）
 * @param {string} phone 手机号
 * @param {string} scene 使用场景，默认changePhone
 */
export function sendUserCenterSmsCode(phone, scene = 'changePhone') {
  return axios({
    url: api.userCenterSmsCode,
    method: 'post',
    params: { phone, scene }
  })
}

/**
 * 发送邮箱验证码（用户中心专用，需要token）
 * @param {string} email 邮箱
 * @param {string} scene 使用场景，默认changeEmail
 */
export function sendUserCenterEmailCode(email, scene = 'changeEmail') {
  return axios({
    url: api.userCenterEmailCode,
    method: 'post',
    params: { email, scene }
  })
}

/**
 * 验证验证码（用户中心专用，需要token）
 * @param {string} target 目标（手机号/邮箱）
 * @param {string} code 验证码
 * @param {string} codeType 验证码类型：sms-短信，email-邮箱
 * @param {string} scene 使用场景
 */
export function verifyUserCenterCode(target, code, codeType, scene) {
  return axios({
    url: api.userCenterVerifyCode,
    method: 'post',
    params: { target, code, codeType, scene }
  })
}

/**
 * 验证昵称是否可用（需要token）
 * @param {string} nickname 昵称
 */
export function validateNickname(nickname) {
  return axios({
    url: api.validateNickname,
    method: 'post',
    params: { nickname }
  })
}