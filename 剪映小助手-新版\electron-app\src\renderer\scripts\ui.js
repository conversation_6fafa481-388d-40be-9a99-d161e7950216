// UI管理模块
const UI = {
    // 通知防抖缓存
    notificationCache: new Map(),

    // 初始化UI
    init() {
        this.bindModalEvents()
        this.setupNotifications()
    },

    // 绑定模态框事件
    bindModalEvents() {
        const modalOverlay = document.getElementById('modal-overlay')
        const modalClose = document.getElementById('modal-close')

        // 点击遮罩关闭
        modalOverlay.addEventListener('click', (e) => {
            if (e.target === modalOverlay) {
                this.hideModal()
            }
        })

        // 点击关闭按钮
        modalClose.addEventListener('click', () => {
            this.hideModal()
        })

        // ESC键关闭
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && !modalOverlay.classList.contains('hidden')) {
                this.hideModal()
            }
        })
    },

    // 设置通知系统
    setupNotifications() {
        // 确保通知容器存在
        if (!document.getElementById('notification-container')) {
            const container = document.createElement('div')
            container.id = 'notification-container'
            container.className = 'notification-container'
            document.body.appendChild(container)
        }
    },

    // 显示模态框
    showModal(options) {
        const modalOverlay = document.getElementById('modal-overlay')
        const modalTitle = document.getElementById('modal-title')
        const modalContent = document.getElementById('modal-content')
        const modalActions = document.getElementById('modal-actions')

        // 设置标题
        modalTitle.textContent = options.title || '提示'

        // 设置内容
        modalContent.innerHTML = options.content || ''

        // 设置按钮
        modalActions.innerHTML = ''
        if (options.actions && options.actions.length > 0) {
            options.actions.forEach(action => {
                const button = document.createElement('button')
                button.textContent = action.text
                button.className = `modal-btn ${action.type || 'primary'}`
                button.addEventListener('click', () => {
                    if (action.action) {
                        action.action()
                    }
                })
                modalActions.appendChild(button)
            })
        } else {
            // 默认确定按钮
            const button = document.createElement('button')
            button.textContent = '确定'
            button.className = 'modal-btn primary'
            button.addEventListener('click', () => {
                this.hideModal()
            })
            modalActions.appendChild(button)
        }

        // 显示模态框
        modalOverlay.classList.remove('hidden')
        
        // 聚焦到第一个按钮
        setTimeout(() => {
            const firstBtn = modalActions.querySelector('.modal-btn')
            if (firstBtn) {
                firstBtn.focus()
            }
        }, 100)
    },

    // 隐藏模态框
    hideModal() {
        const modalOverlay = document.getElementById('modal-overlay')
        modalOverlay.classList.add('hidden')
    },

    // 显示通知
    showNotification(message, type = 'info', duration = 3000) {
        const container = document.getElementById('notification-container')

        // 防抖：检查短时间内是否有相同通知
        const cacheKey = `${message}_${type}`
        const now = Date.now()
        const lastTime = this.notificationCache.get(cacheKey)

        if (lastTime && (now - lastTime) < 1000) { // 1秒内防重复
            console.log('防抖：1秒内重复通知，跳过显示')
            return null
        }

        this.notificationCache.set(cacheKey, now)

        // 防重复：检查是否已有相同消息和类型的通知
        const existingNotifications = container.querySelectorAll('.notification')
        for (let existing of existingNotifications) {
            const existingMessage = existing.querySelector('.notification-message')
            const existingType = existing.classList.contains(`notification-${type}`)
            if (existingMessage && existingMessage.textContent === message && existingType) {
                console.log('防重复：相同消息和类型的通知已存在，跳过显示')
                return existing
            }
        }

        // 创建通知元素
        const notification = document.createElement('div')
        notification.className = `notification notification-${type}`
        
        // 设置图标
        const icons = {
            success: '✅',
            error: '❌',
            warning: '⚠️',
            info: 'ℹ️'
        }
        
        notification.innerHTML = `
            <div class="notification-content">
                <span class="notification-icon">${icons[type] || icons.info}</span>
                <span class="notification-message">${message}</span>
            </div>
            <button class="notification-close">×</button>
        `

        // 添加关闭事件
        const closeBtn = notification.querySelector('.notification-close')
        closeBtn.addEventListener('click', () => {
            this.removeNotification(notification)
        })

        // 限制通知数量（最多显示3个）
        const allNotifications = container.querySelectorAll('.notification')
        if (allNotifications.length >= 3) {
            // 移除最旧的通知
            this.removeNotification(allNotifications[0])
        }

        // 添加到容器
        container.appendChild(notification)

        // 显示动画
        setTimeout(() => {
            notification.classList.add('show')
        }, 10)

        // 自动移除
        if (duration > 0) {
            setTimeout(() => {
                this.removeNotification(notification)
            }, duration)
        }

        return notification
    },

    // 移除通知
    removeNotification(notification) {
        if (notification && notification.parentNode) {
            notification.classList.remove('show')
            notification.classList.add('hide')
            
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification)
                }
            }, 300)
        }
    },

    // 切换页面
    switchPage(pageName) {
        if (window.app) {
            window.app.switchPage(pageName)
        }
    },

    // 显示确认对话框
    showConfirm(options) {
        const modal = {
            title: options.title || '确认',
            content: `
                <div class="confirm-content">
                    <div class="confirm-icon">${options.icon || '❓'}</div>
                    <div class="confirm-message">${options.message || '确定要执行此操作吗？'}</div>
                </div>
            `,
            actions: [
                {
                    text: options.cancelText || '取消',
                    type: 'secondary',
                    action: () => {
                        this.hideModal()
                        if (options.onCancel) {
                            options.onCancel()
                        }
                    }
                },
                {
                    text: options.confirmText || '确定',
                    type: 'primary',
                    action: () => {
                        this.hideModal()
                        if (options.onConfirm) {
                            options.onConfirm()
                        }
                    }
                }
            ]
        }
        
        this.showModal(modal)
    },

    // 显示加载状态
    showLoading(message = '加载中...') {
        const modal = {
            title: '请稍候',
            content: `
                <div class="loading-content">
                    <div class="loading-spinner"></div>
                    <div class="loading-message">${message}</div>
                </div>
            `,
            actions: []
        }
        
        this.showModal(modal)
    },

    // 隐藏加载状态
    hideLoading() {
        this.hideModal()
    },

    // 显示错误对话框
    showError(message, title = '错误') {
        const modal = {
            title: title,
            content: `
                <div class="error-content">
                    <div class="error-icon">❌</div>
                    <div class="error-message">${message}</div>
                </div>
            `,
            actions: [
                {
                    text: '确定',
                    type: 'primary',
                    action: () => this.hideModal()
                }
            ]
        }
        
        this.showModal(modal)
    },

    // 显示成功对话框
    showSuccess(message, title = '成功') {
        const modal = {
            title: title,
            content: `
                <div class="success-content">
                    <div class="success-icon">✅</div>
                    <div class="success-message">${message}</div>
                </div>
            `,
            actions: [
                {
                    text: '确定',
                    type: 'primary',
                    action: () => this.hideModal()
                }
            ]
        }
        
        this.showModal(modal)
    },

    // 显示输入对话框
    showPrompt(options) {
        const inputId = 'prompt-input-' + Date.now()
        
        const modal = {
            title: options.title || '输入',
            content: `
                <div class="prompt-content">
                    <div class="prompt-message">${options.message || '请输入内容：'}</div>
                    <input 
                        type="${options.type || 'text'}" 
                        id="${inputId}"
                        class="prompt-input" 
                        placeholder="${options.placeholder || ''}"
                        value="${options.defaultValue || ''}"
                    >
                </div>
            `,
            actions: [
                {
                    text: options.cancelText || '取消',
                    type: 'secondary',
                    action: () => {
                        this.hideModal()
                        if (options.onCancel) {
                            options.onCancel()
                        }
                    }
                },
                {
                    text: options.confirmText || '确定',
                    type: 'primary',
                    action: () => {
                        const input = document.getElementById(inputId)
                        const value = input ? input.value : ''
                        this.hideModal()
                        if (options.onConfirm) {
                            options.onConfirm(value)
                        }
                    }
                }
            ]
        }
        
        this.showModal(modal)
        
        // 聚焦到输入框
        setTimeout(() => {
            const input = document.getElementById(inputId)
            if (input) {
                input.focus()
                input.select()
            }
        }, 100)
    },

    // 工具方法：格式化文件大小
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes'
        
        const k = 1024
        const sizes = ['Bytes', 'KB', 'MB', 'GB']
        const i = Math.floor(Math.log(bytes) / Math.log(k))
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    },

    // 工具方法：格式化时间
    formatTime(timestamp) {
        const date = new Date(timestamp)
        const now = new Date()
        const diff = now - date
        
        if (diff < 60000) { // 1分钟内
            return '刚刚'
        } else if (diff < 3600000) { // 1小时内
            return Math.floor(diff / 60000) + '分钟前'
        } else if (diff < 86400000) { // 1天内
            return Math.floor(diff / 3600000) + '小时前'
        } else {
            return date.toLocaleDateString()
        }
    }
}

// 导出模块
window.UI = UI
