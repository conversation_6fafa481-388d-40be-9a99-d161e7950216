{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\agent\\AgentDetailPage.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\agent\\AgentDetailPage.vue", "mtime": 1754511159792}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { getUserRole } from '@/utils/roleUtils'\n\nexport default {\n  name: 'AgentDetailPage',\n  data() {\n    return {\n      // 权限状态\n      isCheckingPermission: true,\n      isLoggedIn: false,\n      isPurchased: false,\n      \n      // 数据状态\n      agentDetail: {},\n      workflowList: [],\n      workflowLoading: false,\n      \n      // 视频状态\n      videoError: false,\n      videoCollapsed: false,\n\n      // 使用说明状态\n      guideCollapsed: false,\n\n      // 导航状态\n      activeNavItem: 'agent-info',\n      navItems: [\n        { id: 'agent-info', title: '智能体信息' },\n        { id: 'demo-video', title: '演示视频' },\n        { id: 'usage-guide', title: '使用说明' },\n        { id: 'workflow-list', title: '工作流列表' }\n      ],\n      \n      // 默认图片\n      defaultAvatar: '/default-avatar.png', // 使用项目统一的默认头像路径\n      defaultCreatorAvatar: '/default-avatar.png' // 创作者也使用相同的默认头像\n    }\n  },\n  \n  computed: {\n    agentId() {\n      return this.$route.query.agentId\n    },\n    \n    authorTypeClass() {\n      if (this.agentDetail.authorType === '1' || this.agentDetail.authorType === 1) {\n        return 'official'\n      }\n      return 'creator'\n    },\n    \n    authorTypeText() {\n      if (this.agentDetail.authorType === '1' || this.agentDetail.authorType === 1) {\n        return '官方'\n      }\n      return '创作者'\n    }\n  },\n  \n  async created() {\n    await this.checkPermissions()\n    if (this.isLoggedIn && this.isPurchased) {\n      await this.loadAgentData()\n    }\n  },\n\n  mounted() {\n    // 添加滚动监听\n    window.addEventListener('scroll', this.handleScroll)\n    // 初始化当前区域 - 确保默认为第一个区域\n    this.$nextTick(() => {\n      // 页面加载时默认激活第一个区域\n      this.activeNavItem = 'agent-info'\n      console.log('📍 页面加载，默认激活:', this.activeNavItem)\n\n      // 延迟执行滚动检测，避免初始化时的错误激活\n      setTimeout(() => {\n        this.handleScroll()\n      }, 500)\n    })\n  },\n\n  beforeDestroy() {\n    // 移除滚动监听\n    window.removeEventListener('scroll', this.handleScroll)\n  },\n  \n  methods: {\n    // 权限检查\n    async checkPermissions() {\n      try {\n        console.log('🔍 开始权限检查, agentId:', this.agentId)\n        console.log('🚨 DEBUG: AgentDetailPage 代码已更新 - 版本2024')\n\n        // 检查登录状态 - getUserRole是异步函数\n        const userRole = await getUserRole()\n        this.isLoggedIn = !!userRole\n\n        console.log('🔍 登录状态检查:', {\n          userRole,\n          isLoggedIn: this.isLoggedIn\n        })\n\n        if (!this.isLoggedIn) {\n          console.log('🔍 用户未登录，停止权限检查')\n          this.isCheckingPermission = false\n          return\n        }\n\n        // 检查购买状态 - 使用服务端API验证\n        console.log('🔍 即将调用 checkPurchaseStatus 方法...')\n        this.isPurchased = await this.checkPurchaseStatus()\n        console.log('🔍 checkPurchaseStatus 方法调用完成，返回值:', this.isPurchased)\n\n        console.log('🔍 购买状态检查:', {\n          isPurchased: this.isPurchased,\n          agentId: this.agentId\n        })\n\n        this.isCheckingPermission = false\n      } catch (error) {\n        console.error('🔍 权限检查失败:', error)\n        this.isCheckingPermission = false\n      }\n    },\n    \n    // 检查购买状态 - 使用服务端API验证\n    async checkPurchaseStatus() {\n      try {\n        console.log('🔍 AgentDetailPage - 验证购买状态, agentId:', this.agentId)\n        const response = await this.$http.get(`/api/agent/market/purchase/check/${this.agentId}`)\n        console.log('🔍 AgentDetailPage - 购买状态API响应:', response)\n\n        if (response && response.success) {\n          const isPurchased = response.result.isPurchased\n          console.log('✅ AgentDetailPage - 购买状态验证完成:', isPurchased)\n          return isPurchased\n        } else {\n          console.warn('⚠️ AgentDetailPage - 购买状态验证失败')\n          return false\n        }\n      } catch (error) {\n        console.error('❌ AgentDetailPage - 购买状态验证出错:', error)\n        return false\n      }\n    },\n    \n    // 加载智能体数据\n    async loadAgentData() {\n      try {\n        // 🔥 只需要加载智能体详情，工作流列表已包含在详情接口中\n        await this.loadAgentDetail()\n      } catch (error) {\n        console.error('加载数据失败:', error)\n        this.$message.error('加载数据失败，请稍后重试')\n      }\n    },\n    \n    // 加载智能体详情\n    async loadAgentDetail() {\n      try {\n        console.log('🔍 开始加载智能体详情, agentId:', this.agentId)\n\n        const response = await this.$http.get(`/api/agent/market/detail/${this.agentId}`)\n        console.log('🔍 智能体详情API响应:', response)\n\n        if (response.success) {\n          this.agentDetail = response.result\n          console.log('🔍 智能体详情加载成功:', this.agentDetail)\n\n          // 动态设置页面标题\n          document.title = `${this.agentDetail.agentName || '智能体详情'} - 智界AIGC`\n\n          // 🔥 直接从详情接口获取工作流列表，无需单独请求\n          if (this.agentDetail.workflowList) {\n            this.workflowList = this.agentDetail.workflowList\n            console.log('🔍 工作流列表已从详情接口获取:', this.workflowList)\n          } else {\n            this.workflowList = []\n          }\n        } else {\n          throw new Error(response.message || '获取智能体详情失败')\n        }\n      } catch (error) {\n        console.error('🔍 加载智能体详情失败:', error)\n        this.$message.error('加载智能体详情失败: ' + error.message)\n        throw error\n      }\n    },\n    \n    // 🔥 加载工作流列表 - 已废弃，现在直接从智能体详情接口获取\n    // async loadWorkflowList() {\n    //   this.workflowLoading = true\n    //   try {\n    //     console.log('🔍 开始加载工作流列表, agentId:', this.agentId)\n    //     const response = await this.$http.get(`/api/agent/market/${this.agentId}/workflows`)\n    //     console.log('🔍 工作流列表API响应:', response)\n    //     if (response.success) {\n    //       this.workflowList = response.result || []\n    //       console.log('🔍 工作流列表加载成功:', this.workflowList)\n    //     } else {\n    //       throw new Error(response.message || '获取工作流列表失败')\n    //     }\n    //   } catch (error) {\n    //     console.error('🔍 加载工作流列表失败:', error)\n    //     this.$message.error('加载工作流列表失败: ' + error.message)\n    //     this.workflowList = []\n    //   } finally {\n    //     this.workflowLoading = false\n    //   }\n    // },\n    \n    // 导航方法\n    goBack() {\n      this.$router.go(-1)\n    },\n    \n    goToLogin() {\n      this.$router.push({\n        path: '/login',\n        query: { redirect: this.$route.fullPath }\n      })\n    },\n    \n    goToPurchase() {\n      this.$router.push('/workflow-center')\n    },\n    \n    // 图片错误处理\n    handleAvatarError(event) {\n      event.target.src = this.defaultAvatar\n    },\n    \n    handleCreatorAvatarError(event) {\n      event.target.src = this.defaultCreatorAvatar\n    },\n\n    // 工作流图片错误处理\n    handleWorkflowImageError(event) {\n      event.target.src = this.defaultAgentAvatar\n    },\n    \n    // 视频事件处理\n    onVideoLoadStart() {\n      this.videoError = false\n      console.log('🎬 视频开始加载')\n    },\n    \n    onVideoLoaded() {\n      console.log('🎬 视频加载完成')\n    },\n    \n    onVideoError() {\n      this.videoError = true\n      console.error('🎬 视频加载失败')\n    },\n    \n    onVideoPlay() {\n      console.log('🎬 视频开始播放')\n    },\n    \n    onVideoPause() {\n      console.log('🎬 视频暂停播放')\n    },\n\n    // 切换视频折叠状态\n    toggleVideoCollapse() {\n      this.videoCollapsed = !this.videoCollapsed\n      console.log('🎬 视频折叠状态:', this.videoCollapsed ? '折叠' : '展开')\n    },\n\n    // 切换使用说明折叠状态\n    toggleGuideCollapse() {\n      this.guideCollapsed = !this.guideCollapsed\n      console.log('📖 使用说明折叠状态:', this.guideCollapsed ? '折叠' : '展开')\n    },\n\n    // 处理使用说明图片加载错误\n    handleGuideImageError(event) {\n      console.warn('📖 使用说明图片加载失败:', event.target.src)\n      event.target.style.display = 'none'\n    },\n\n    // 预览图片\n    previewImage(imageUrl) {\n      // 禁用页面滚动\n      document.body.style.overflow = 'hidden'\n\n      // 缩放和位置状态\n      let scale = 1\n      let translateX = 0\n      let translateY = 0\n      let isDragging = false\n      let lastX = 0\n      let lastY = 0\n      let animationId = null\n\n      // 创建图片预览模态框\n      const modal = document.createElement('div')\n      modal.style.cssText = `\n        position: fixed;\n        top: 0;\n        left: 0;\n        width: 100%;\n        height: 100%;\n        background: rgba(0, 0, 0, 0.9);\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        z-index: 9999;\n        cursor: default;\n      `\n\n      const img = document.createElement('img')\n      img.src = imageUrl\n      img.style.cssText = `\n        max-width: 90%;\n        max-height: 90%;\n        object-fit: contain;\n        border-radius: 8px;\n        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);\n        user-select: none;\n        cursor: grab;\n      `\n\n      // 添加操作提示\n      const tip = document.createElement('div')\n      tip.style.cssText = `\n        position: absolute;\n        top: 20px;\n        left: 50%;\n        transform: translateX(-50%);\n        background: rgba(0, 0, 0, 0.7);\n        color: white;\n        padding: 8px 16px;\n        border-radius: 20px;\n        font-size: 14px;\n        z-index: 10000;\n        pointer-events: none;\n        opacity: 0.8;\n      `\n      tip.textContent = '滚轮缩放 | 拖拽移动 | 双击重置 | ESC关闭'\n\n      // 更新图片变换\n      const updateTransform = (withTransition = false) => {\n        // 拖拽时不使用过渡动画，其他操作使用过渡动画\n        img.style.transition = withTransition ? 'transform 0.3s ease' : 'none'\n        img.style.transform = `translate(${translateX}px, ${translateY}px) scale(${scale})`\n      }\n\n      modal.appendChild(img)\n      modal.appendChild(tip)\n      document.body.appendChild(modal)\n\n      // 滚轮缩放功能\n      const handleWheel = (e) => {\n        e.preventDefault()\n        const delta = e.deltaY > 0 ? -0.1 : 0.1\n        const newScale = Math.max(0.5, Math.min(5, scale + delta))\n\n        if (newScale !== scale) {\n          scale = newScale\n          updateTransform(true) // 缩放使用过渡动画\n          modal.style.cursor = 'grab' // 始终显示可拖拽状态\n        }\n      }\n\n      // 拖拽功能\n      const handleMouseDown = (e) => {\n        if (e.target === img) {\n          e.preventDefault()\n          e.stopPropagation()\n          isDragging = true\n          lastX = e.clientX\n          lastY = e.clientY\n          modal.style.cursor = 'grabbing'\n        }\n      }\n\n      const handleMouseMove = (e) => {\n        if (isDragging) {\n          e.preventDefault()\n\n          // 取消之前的动画帧\n          if (animationId) {\n            cancelAnimationFrame(animationId)\n          }\n\n          // 使用 requestAnimationFrame 优化性能\n          animationId = requestAnimationFrame(() => {\n            const deltaX = e.clientX - lastX\n            const deltaY = e.clientY - lastY\n            translateX += deltaX\n            translateY += deltaY\n            lastX = e.clientX\n            lastY = e.clientY\n            updateTransform(false) // 拖拽不使用过渡动画，实现即时响应\n          })\n        }\n      }\n\n      const handleMouseUp = (e) => {\n        if (isDragging) {\n          e.preventDefault()\n          e.stopPropagation()\n          isDragging = false\n          modal.style.cursor = 'grab' // 始终显示可拖拽状态\n        }\n      }\n\n      // 双击重置\n      const handleDoubleClick = () => {\n        scale = 1\n        translateX = 0\n        translateY = 0\n        updateTransform(true) // 重置使用过渡动画\n        modal.style.cursor = 'grab' // 始终显示可拖拽状态\n      }\n\n      // 关闭模态框\n      const closeModal = () => {\n        // 恢复页面滚动\n        document.body.style.overflow = ''\n\n        // 取消动画帧\n        if (animationId) {\n          cancelAnimationFrame(animationId)\n        }\n\n        // 移除事件监听器\n        modal.removeEventListener('wheel', handleWheel)\n        modal.removeEventListener('mousedown', handleMouseDown)\n        document.removeEventListener('mousemove', handleMouseMove)\n        document.removeEventListener('mouseup', handleMouseUp)\n        modal.removeEventListener('dblclick', handleDoubleClick)\n        document.removeEventListener('keydown', handleKeyDown)\n\n        // 移除模态框\n        document.body.removeChild(modal)\n      }\n\n      // 键盘ESC关闭\n      const handleKeyDown = (e) => {\n        if (e.key === 'Escape') {\n          closeModal()\n        }\n      }\n\n      // 点击背景关闭（但不包括图片，且不在拖拽状态）\n      const handleModalClick = (e) => {\n        if (e.target === modal && !isDragging) {\n          closeModal()\n        }\n      }\n\n      // 添加事件监听器\n      modal.addEventListener('wheel', handleWheel)\n      modal.addEventListener('mousedown', handleMouseDown)\n      document.addEventListener('mousemove', handleMouseMove)\n      document.addEventListener('mouseup', handleMouseUp)\n      modal.addEventListener('dblclick', handleDoubleClick)\n      modal.addEventListener('click', handleModalClick)\n      document.addEventListener('keydown', handleKeyDown)\n\n      console.log('🔍 图片预览:', imageUrl)\n    },\n\n    // 滚动到指定区域\n    scrollToSection(sectionId) {\n      const element = document.getElementById(sectionId)\n      if (element) {\n        element.scrollIntoView({\n          behavior: 'smooth',\n          block: 'start'\n        })\n        this.activeNavItem = sectionId\n        console.log('🎯 滚动到区域:', sectionId)\n      }\n    },\n\n    // 快速滚动到使用说明\n    scrollToUsageGuide() {\n      this.scrollToSection('usage-guide')\n      console.log('📖 快速跳转到使用说明')\n    },\n\n    // 监听滚动事件，更新活跃导航项\n    handleScroll() {\n      const scrollTop = window.pageYOffset || document.documentElement.scrollTop\n      const windowHeight = window.innerHeight\n      const documentHeight = document.documentElement.scrollHeight\n\n      // 如果在页面顶部，直接激活第一个区域\n      if (scrollTop < 100) {\n        if (this.activeNavItem !== 'agent-info') {\n          this.activeNavItem = 'agent-info'\n          console.log('📍 页面顶部，激活:', this.activeNavItem)\n        }\n        return\n      }\n\n      // 获取所有区域的位置\n      const sections = this.navItems.map(item => {\n        const element = document.getElementById(item.id)\n        return {\n          id: item.id,\n          offsetTop: element ? element.offsetTop - 150 : 0,\n          element: element\n        }\n      })\n\n      // 检查是否滚动到页面底部\n      const isAtBottom = scrollTop + windowHeight >= documentHeight - 50\n\n      let currentSection = sections[0].id\n\n      if (isAtBottom) {\n        // 如果在页面底部，激活最后一个区域\n        currentSection = sections[sections.length - 1].id\n      } else {\n        // 找到当前滚动位置对应的区域\n        for (let i = sections.length - 1; i >= 0; i--) {\n          if (scrollTop >= sections[i].offsetTop) {\n            currentSection = sections[i].id\n            break\n          }\n        }\n      }\n\n      if (this.activeNavItem !== currentSection) {\n        this.activeNavItem = currentSection\n        console.log('📍 当前区域:', currentSection, isAtBottom ? '(页面底部)' : '')\n      }\n    },\n\n    // 工作流下载\n    async handleWorkflowDownload(workflow) {\n      try {\n        console.log('🔍 开始下载工作流:', workflow)\n\n        // 检查是否有下载地址\n        if (!workflow.workflowPackage && !workflow.packagePath) {\n          this.$message.error('工作流压缩包地址不存在')\n          return\n        }\n\n        // 设置下载状态\n        this.$set(workflow, 'downloading', true)\n\n        // 🌐 直接使用CDN地址下载，无需调用后端API\n        const downloadUrl = workflow.workflowPackage || workflow.packagePath\n        console.log('🔍 使用CDN地址下载工作流:', downloadUrl)\n\n        // 创建下载链接\n        const link = document.createElement('a')\n        link.href = downloadUrl\n        link.download = `${workflow.workflowName || '工作流'}.zip`\n        link.target = '_blank'\n\n        // 触发下载\n        document.body.appendChild(link)\n        link.click()\n        document.body.removeChild(link)\n\n        this.$message.success(`工作流 \"${workflow.workflowName}\" 下载已开始`)\n\n      } catch (error) {\n        console.error('🔍 工作流下载失败:', error)\n        this.$message.error('下载失败: ' + error.message)\n      } finally {\n        // 清除下载状态\n        this.$set(workflow, 'downloading', false)\n      }\n    }\n  }\n}\n", {"version": 3, "sources": ["AgentDetailPage.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAg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file": "AgentDetailPage.vue", "sourceRoot": "src/views/website/agent", "sourcesContent": ["<template>\n  <div class=\"agent-detail-page\">\n    <!-- 时间轴式导航 -->\n    <div class=\"timeline-nav\">\n      <div class=\"timeline-line\"></div>\n      <div\n        v-for=\"(item, index) in navItems\"\n        :key=\"item.id\"\n        class=\"timeline-node\"\n        :class=\"{ 'active': activeNavItem === item.id }\"\n        :style=\"{ top: (index * 120 + 100) + 'px' }\"\n        @click=\"scrollToSection(item.id)\"\n      >\n        <div class=\"node-dot\"></div>\n        <div class=\"node-label\">{{ item.title }}</div>\n      </div>\n    </div>\n\n    <!-- 权限检查加载状态 -->\n    <div v-if=\"isCheckingPermission\" class=\"permission-loading\">\n      <a-spin size=\"large\">\n        <div class=\"loading-text\">正在验证访问权限...</div>\n      </a-spin>\n    </div>\n\n    <!-- 未登录提示 -->\n    <div v-else-if=\"!isLoggedIn\" class=\"permission-denied\">\n      <div class=\"permission-content\">\n        <a-icon type=\"lock\" class=\"permission-icon\" />\n        <h2>需要登录访问</h2>\n        <p>请先登录您的账户以查看智能体详细信息</p>\n        <a-button type=\"primary\" size=\"large\" @click=\"goToLogin\">\n          立即登录\n        </a-button>\n      </div>\n    </div>\n\n    <!-- 未购买提示 -->\n    <div v-else-if=\"!isPurchased\" class=\"permission-denied\">\n      <div class=\"permission-content\">\n        <a-icon type=\"shopping-cart\" class=\"permission-icon\" />\n        <h2>需要购买访问</h2>\n        <p>您还未购买此智能体，请先购买后再查看详细信息</p>\n        <div class=\"action-buttons\">\n          <a-button size=\"large\" @click=\"goBack\">\n            返回列表\n          </a-button>\n          <a-button type=\"primary\" size=\"large\" @click=\"goToPurchase\">\n            立即购买\n          </a-button>\n        </div>\n      </div>\n    </div>\n\n    <!-- 主要内容区域 -->\n    <div v-else class=\"main-content\">\n      <!-- 页面头部 -->\n      <div class=\"page-header\">\n        <a-button @click=\"goBack\" class=\"back-button\">\n          <a-icon type=\"arrow-left\" />\n          返回\n        </a-button>\n        <div class=\"breadcrumb\">\n          <a-breadcrumb>\n            <a-breadcrumb-item>\n              <router-link to=\"/workflow-center\">工作流中心</router-link>\n            </a-breadcrumb-item>\n            <a-breadcrumb-item>智能体详情</a-breadcrumb-item>\n          </a-breadcrumb>\n        </div>\n      </div>\n\n      <!-- 智能体基本信息 -->\n      <div id=\"agent-info\" class=\"agent-info-section\">\n        <div class=\"agent-basic-info\">\n          <div class=\"agent-avatar\">\n            <img \n              :src=\"agentDetail.agentAvatar || defaultAvatar\" \n              :alt=\"agentDetail.agentName\"\n              @error=\"handleAvatarError\"\n            />\n          </div>\n          <div class=\"agent-details\">\n            <h1 class=\"agent-name\">{{ agentDetail.agentName || '智能体名称' }}</h1>\n            <div class=\"agent-description\">\n              <p>{{ agentDetail.agentDescription || '暂无描述' }}</p>\n            </div>\n            <div class=\"creator-info\">\n              <img\n                :src=\"(agentDetail.creatorInfo && agentDetail.creatorInfo.avatar) || agentDetail.creatorAvatar || defaultCreatorAvatar\"\n                :alt=\"(agentDetail.creatorInfo && agentDetail.creatorInfo.nickname) || agentDetail.creatorNickname || '创作者'\"\n                class=\"creator-avatar\"\n                @error=\"handleCreatorAvatarError\"\n              />\n              <span class=\"creator-name\">{{ (agentDetail.creatorInfo && agentDetail.creatorInfo.nickname) || agentDetail.creatorNickname || '创作者' }}</span>\n              <span class=\"author-type-badge\" :class=\"authorTypeClass\">\n                {{ authorTypeText }}\n              </span>\n            </div>\n          </div>\n        </div>\n\n        <!-- 阅读提示 -->\n        <div class=\"reading-tip\" @click=\"scrollToUsageGuide\">\n          <a-icon type=\"info-circle\" class=\"tip-icon\" />\n          <span class=\"tip-text\">\n            请往下滑或点此快速下滑，仔细阅读工作流导入使用说明\n          </span>\n          <a-icon type=\"arrow-down\" class=\"arrow-icon\" />\n        </div>\n      </div>\n\n      <!-- 演示视频区域 -->\n      <div id=\"demo-video\" v-if=\"agentDetail.demoVideo\" class=\"video-section\">\n        <div class=\"video-header\" @click=\"toggleVideoCollapse\">\n          <h3>演示视频</h3>\n          <a-icon\n            :type=\"videoCollapsed ? 'down' : 'up'\"\n            class=\"collapse-icon\"\n          />\n        </div>\n        <div v-show=\"!videoCollapsed\" class=\"video-container\">\n          <video\n            ref=\"videoPlayer\"\n            :src=\"agentDetail.demoVideo\"\n            controls\n            autoplay\n            muted\n            preload=\"metadata\"\n            @loadstart=\"onVideoLoadStart\"\n            @loadeddata=\"onVideoLoaded\"\n            @error=\"onVideoError\"\n            @play=\"onVideoPlay\"\n            @pause=\"onVideoPause\"\n          >\n            您的浏览器不支持视频播放\n          </video>\n          <div v-if=\"videoError\" class=\"video-error\">\n            <a-icon type=\"exclamation-circle\" />\n            <span>视频加载失败，请稍后重试</span>\n          </div>\n        </div>\n      </div>\n\n      <!-- 工作流导入使用说明 -->\n      <div id=\"usage-guide\" class=\"usage-guide-section\">\n        <div class=\"guide-header\" @click=\"toggleGuideCollapse\">\n          <h3>工作流导入使用说明</h3>\n          <a-icon\n            :type=\"guideCollapsed ? 'down' : 'up'\"\n            class=\"collapse-icon\"\n          />\n        </div>\n        <div v-show=\"!guideCollapsed\" class=\"guide-content\">\n          <div class=\"guide-step\">\n            <div class=\"step-number\">1</div>\n            <div class=\"step-content\">\n              <p class=\"step-text\">点击下载工作流，下载完成是一个压缩包。</p>\n              <div class=\"step-image\" @click=\"previewImage('https://cdn.aigcview.com/defaults/export.png')\">\n                <img\n                  src=\"https://cdn.aigcview.com/defaults/export.png\"\n                  alt=\"下载工作流示例图\"\n                  @error=\"handleGuideImageError\"\n                />\n                <div class=\"image-overlay\">\n                  <a-icon type=\"zoom-in\" />\n                  <span>点击查看大图</span>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <div class=\"guide-step\">\n            <div class=\"step-number\">2</div>\n            <div class=\"step-content\">\n              <p class=\"step-text\">\n                <a\n                  href=\"https://www.coze.cn/space\"\n                  target=\"_blank\"\n                  class=\"coze-link\"\n                >\n                  点此快速跳转Coze工作空间\n                  <a-icon type=\"external-link\" />\n                </a>\n                ，选择需要放置的工作空间，点击右上角导入按钮，选择下载好的工作流压缩包即可完成。<span class=\"highlight-text\">（压缩包无需解压）</span>\n              </p>\n              <div class=\"step-image\" @click=\"previewImage('https://cdn.aigcview.com/defaults/import.png')\">\n                <img\n                  src=\"https://cdn.aigcview.com/defaults/import.png\"\n                  alt=\"导入工作流示例图\"\n                  @error=\"handleGuideImageError\"\n                />\n                <div class=\"image-overlay\">\n                  <a-icon type=\"zoom-in\" />\n                  <span>点击查看大图</span>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- 工作流列表区域 -->\n      <div id=\"workflow-list\" class=\"workflow-section\">\n        <h3>关联工作流 ({{ workflowList.length }}个)</h3>\n        <div v-if=\"workflowLoading\" class=\"workflow-loading\">\n          <a-spin>加载工作流列表...</a-spin>\n        </div>\n        <div v-else-if=\"workflowList.length === 0\" class=\"workflow-empty\">\n          <a-empty description=\"暂无关联工作流\" />\n        </div>\n        <div v-else class=\"workflow-list\">\n          <div\n            v-for=\"workflow in workflowList\"\n            :key=\"workflow.id\"\n            class=\"workflow-card\"\n          >\n            <!-- 工作流头像（使用智能体头像） -->\n            <div class=\"workflow-avatar\">\n              <img\n                :src=\"agentDetail.agentAvatar || defaultAgentAvatar\"\n                :alt=\"workflow.workflowName\"\n                class=\"workflow-image\"\n                @error=\"handleWorkflowImageError\"\n              />\n            </div>\n\n            <div class=\"workflow-info\">\n              <h4 class=\"workflow-name\">{{ workflow.workflowName }}</h4>\n              <p class=\"workflow-description\">{{ workflow.workflowDescription || '暂无描述' }}</p>\n\n              <!-- 🔥 新增：输入参数说明 -->\n              <div class=\"workflow-params\">\n                <div class=\"params-label\">\n                  <a-icon type=\"setting\" />\n                  <span>输入参数说明</span>\n                </div>\n                <div class=\"params-content\">\n                  {{ workflow.inputParamsDesc || '暂无输入参数说明' }}\n                </div>\n              </div>\n            </div>\n\n            <div class=\"workflow-actions\">\n              <a-button\n                type=\"primary\"\n                size=\"default\"\n                class=\"download-btn\"\n                @click=\"handleWorkflowDownload(workflow)\"\n                :loading=\"workflow.downloading\"\n                :disabled=\"!workflow.workflowPackage\"\n              >\n                <a-icon type=\"cloud-download\" />\n                <span>下载工作流</span>\n              </a-button>\n            </div>\n          </div>\n        </div>\n\n        <!-- 使用提示 -->\n        <div v-if=\"workflowList.length > 0\" class=\"workflow-usage-tip\">\n          <a-icon type=\"info-circle\" class=\"tip-icon\" />\n          <span class=\"tip-text\">\n            点击下载工作流是一个压缩包，将下载好的压缩包导入进Coze工作空间即可使用。<span class=\"highlight-text\">（压缩包无需解压）</span>\n          </span>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { getUserRole } from '@/utils/roleUtils'\n\nexport default {\n  name: 'AgentDetailPage',\n  data() {\n    return {\n      // 权限状态\n      isCheckingPermission: true,\n      isLoggedIn: false,\n      isPurchased: false,\n      \n      // 数据状态\n      agentDetail: {},\n      workflowList: [],\n      workflowLoading: false,\n      \n      // 视频状态\n      videoError: false,\n      videoCollapsed: false,\n\n      // 使用说明状态\n      guideCollapsed: false,\n\n      // 导航状态\n      activeNavItem: 'agent-info',\n      navItems: [\n        { id: 'agent-info', title: '智能体信息' },\n        { id: 'demo-video', title: '演示视频' },\n        { id: 'usage-guide', title: '使用说明' },\n        { id: 'workflow-list', title: '工作流列表' }\n      ],\n      \n      // 默认图片\n      defaultAvatar: '/default-avatar.png', // 使用项目统一的默认头像路径\n      defaultCreatorAvatar: '/default-avatar.png' // 创作者也使用相同的默认头像\n    }\n  },\n  \n  computed: {\n    agentId() {\n      return this.$route.query.agentId\n    },\n    \n    authorTypeClass() {\n      if (this.agentDetail.authorType === '1' || this.agentDetail.authorType === 1) {\n        return 'official'\n      }\n      return 'creator'\n    },\n    \n    authorTypeText() {\n      if (this.agentDetail.authorType === '1' || this.agentDetail.authorType === 1) {\n        return '官方'\n      }\n      return '创作者'\n    }\n  },\n  \n  async created() {\n    await this.checkPermissions()\n    if (this.isLoggedIn && this.isPurchased) {\n      await this.loadAgentData()\n    }\n  },\n\n  mounted() {\n    // 添加滚动监听\n    window.addEventListener('scroll', this.handleScroll)\n    // 初始化当前区域 - 确保默认为第一个区域\n    this.$nextTick(() => {\n      // 页面加载时默认激活第一个区域\n      this.activeNavItem = 'agent-info'\n      console.log('📍 页面加载，默认激活:', this.activeNavItem)\n\n      // 延迟执行滚动检测，避免初始化时的错误激活\n      setTimeout(() => {\n        this.handleScroll()\n      }, 500)\n    })\n  },\n\n  beforeDestroy() {\n    // 移除滚动监听\n    window.removeEventListener('scroll', this.handleScroll)\n  },\n  \n  methods: {\n    // 权限检查\n    async checkPermissions() {\n      try {\n        console.log('🔍 开始权限检查, agentId:', this.agentId)\n        console.log('🚨 DEBUG: AgentDetailPage 代码已更新 - 版本2024')\n\n        // 检查登录状态 - getUserRole是异步函数\n        const userRole = await getUserRole()\n        this.isLoggedIn = !!userRole\n\n        console.log('🔍 登录状态检查:', {\n          userRole,\n          isLoggedIn: this.isLoggedIn\n        })\n\n        if (!this.isLoggedIn) {\n          console.log('🔍 用户未登录，停止权限检查')\n          this.isCheckingPermission = false\n          return\n        }\n\n        // 检查购买状态 - 使用服务端API验证\n        console.log('🔍 即将调用 checkPurchaseStatus 方法...')\n        this.isPurchased = await this.checkPurchaseStatus()\n        console.log('🔍 checkPurchaseStatus 方法调用完成，返回值:', this.isPurchased)\n\n        console.log('🔍 购买状态检查:', {\n          isPurchased: this.isPurchased,\n          agentId: this.agentId\n        })\n\n        this.isCheckingPermission = false\n      } catch (error) {\n        console.error('🔍 权限检查失败:', error)\n        this.isCheckingPermission = false\n      }\n    },\n    \n    // 检查购买状态 - 使用服务端API验证\n    async checkPurchaseStatus() {\n      try {\n        console.log('🔍 AgentDetailPage - 验证购买状态, agentId:', this.agentId)\n        const response = await this.$http.get(`/api/agent/market/purchase/check/${this.agentId}`)\n        console.log('🔍 AgentDetailPage - 购买状态API响应:', response)\n\n        if (response && response.success) {\n          const isPurchased = response.result.isPurchased\n          console.log('✅ AgentDetailPage - 购买状态验证完成:', isPurchased)\n          return isPurchased\n        } else {\n          console.warn('⚠️ AgentDetailPage - 购买状态验证失败')\n          return false\n        }\n      } catch (error) {\n        console.error('❌ AgentDetailPage - 购买状态验证出错:', error)\n        return false\n      }\n    },\n    \n    // 加载智能体数据\n    async loadAgentData() {\n      try {\n        // 🔥 只需要加载智能体详情，工作流列表已包含在详情接口中\n        await this.loadAgentDetail()\n      } catch (error) {\n        console.error('加载数据失败:', error)\n        this.$message.error('加载数据失败，请稍后重试')\n      }\n    },\n    \n    // 加载智能体详情\n    async loadAgentDetail() {\n      try {\n        console.log('🔍 开始加载智能体详情, agentId:', this.agentId)\n\n        const response = await this.$http.get(`/api/agent/market/detail/${this.agentId}`)\n        console.log('🔍 智能体详情API响应:', response)\n\n        if (response.success) {\n          this.agentDetail = response.result\n          console.log('🔍 智能体详情加载成功:', this.agentDetail)\n\n          // 动态设置页面标题\n          document.title = `${this.agentDetail.agentName || '智能体详情'} - 智界AIGC`\n\n          // 🔥 直接从详情接口获取工作流列表，无需单独请求\n          if (this.agentDetail.workflowList) {\n            this.workflowList = this.agentDetail.workflowList\n            console.log('🔍 工作流列表已从详情接口获取:', this.workflowList)\n          } else {\n            this.workflowList = []\n          }\n        } else {\n          throw new Error(response.message || '获取智能体详情失败')\n        }\n      } catch (error) {\n        console.error('🔍 加载智能体详情失败:', error)\n        this.$message.error('加载智能体详情失败: ' + error.message)\n        throw error\n      }\n    },\n    \n    // 🔥 加载工作流列表 - 已废弃，现在直接从智能体详情接口获取\n    // async loadWorkflowList() {\n    //   this.workflowLoading = true\n    //   try {\n    //     console.log('🔍 开始加载工作流列表, agentId:', this.agentId)\n    //     const response = await this.$http.get(`/api/agent/market/${this.agentId}/workflows`)\n    //     console.log('🔍 工作流列表API响应:', response)\n    //     if (response.success) {\n    //       this.workflowList = response.result || []\n    //       console.log('🔍 工作流列表加载成功:', this.workflowList)\n    //     } else {\n    //       throw new Error(response.message || '获取工作流列表失败')\n    //     }\n    //   } catch (error) {\n    //     console.error('🔍 加载工作流列表失败:', error)\n    //     this.$message.error('加载工作流列表失败: ' + error.message)\n    //     this.workflowList = []\n    //   } finally {\n    //     this.workflowLoading = false\n    //   }\n    // },\n    \n    // 导航方法\n    goBack() {\n      this.$router.go(-1)\n    },\n    \n    goToLogin() {\n      this.$router.push({\n        path: '/login',\n        query: { redirect: this.$route.fullPath }\n      })\n    },\n    \n    goToPurchase() {\n      this.$router.push('/workflow-center')\n    },\n    \n    // 图片错误处理\n    handleAvatarError(event) {\n      event.target.src = this.defaultAvatar\n    },\n    \n    handleCreatorAvatarError(event) {\n      event.target.src = this.defaultCreatorAvatar\n    },\n\n    // 工作流图片错误处理\n    handleWorkflowImageError(event) {\n      event.target.src = this.defaultAgentAvatar\n    },\n    \n    // 视频事件处理\n    onVideoLoadStart() {\n      this.videoError = false\n      console.log('🎬 视频开始加载')\n    },\n    \n    onVideoLoaded() {\n      console.log('🎬 视频加载完成')\n    },\n    \n    onVideoError() {\n      this.videoError = true\n      console.error('🎬 视频加载失败')\n    },\n    \n    onVideoPlay() {\n      console.log('🎬 视频开始播放')\n    },\n    \n    onVideoPause() {\n      console.log('🎬 视频暂停播放')\n    },\n\n    // 切换视频折叠状态\n    toggleVideoCollapse() {\n      this.videoCollapsed = !this.videoCollapsed\n      console.log('🎬 视频折叠状态:', this.videoCollapsed ? '折叠' : '展开')\n    },\n\n    // 切换使用说明折叠状态\n    toggleGuideCollapse() {\n      this.guideCollapsed = !this.guideCollapsed\n      console.log('📖 使用说明折叠状态:', this.guideCollapsed ? '折叠' : '展开')\n    },\n\n    // 处理使用说明图片加载错误\n    handleGuideImageError(event) {\n      console.warn('📖 使用说明图片加载失败:', event.target.src)\n      event.target.style.display = 'none'\n    },\n\n    // 预览图片\n    previewImage(imageUrl) {\n      // 禁用页面滚动\n      document.body.style.overflow = 'hidden'\n\n      // 缩放和位置状态\n      let scale = 1\n      let translateX = 0\n      let translateY = 0\n      let isDragging = false\n      let lastX = 0\n      let lastY = 0\n      let animationId = null\n\n      // 创建图片预览模态框\n      const modal = document.createElement('div')\n      modal.style.cssText = `\n        position: fixed;\n        top: 0;\n        left: 0;\n        width: 100%;\n        height: 100%;\n        background: rgba(0, 0, 0, 0.9);\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        z-index: 9999;\n        cursor: default;\n      `\n\n      const img = document.createElement('img')\n      img.src = imageUrl\n      img.style.cssText = `\n        max-width: 90%;\n        max-height: 90%;\n        object-fit: contain;\n        border-radius: 8px;\n        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);\n        user-select: none;\n        cursor: grab;\n      `\n\n      // 添加操作提示\n      const tip = document.createElement('div')\n      tip.style.cssText = `\n        position: absolute;\n        top: 20px;\n        left: 50%;\n        transform: translateX(-50%);\n        background: rgba(0, 0, 0, 0.7);\n        color: white;\n        padding: 8px 16px;\n        border-radius: 20px;\n        font-size: 14px;\n        z-index: 10000;\n        pointer-events: none;\n        opacity: 0.8;\n      `\n      tip.textContent = '滚轮缩放 | 拖拽移动 | 双击重置 | ESC关闭'\n\n      // 更新图片变换\n      const updateTransform = (withTransition = false) => {\n        // 拖拽时不使用过渡动画，其他操作使用过渡动画\n        img.style.transition = withTransition ? 'transform 0.3s ease' : 'none'\n        img.style.transform = `translate(${translateX}px, ${translateY}px) scale(${scale})`\n      }\n\n      modal.appendChild(img)\n      modal.appendChild(tip)\n      document.body.appendChild(modal)\n\n      // 滚轮缩放功能\n      const handleWheel = (e) => {\n        e.preventDefault()\n        const delta = e.deltaY > 0 ? -0.1 : 0.1\n        const newScale = Math.max(0.5, Math.min(5, scale + delta))\n\n        if (newScale !== scale) {\n          scale = newScale\n          updateTransform(true) // 缩放使用过渡动画\n          modal.style.cursor = 'grab' // 始终显示可拖拽状态\n        }\n      }\n\n      // 拖拽功能\n      const handleMouseDown = (e) => {\n        if (e.target === img) {\n          e.preventDefault()\n          e.stopPropagation()\n          isDragging = true\n          lastX = e.clientX\n          lastY = e.clientY\n          modal.style.cursor = 'grabbing'\n        }\n      }\n\n      const handleMouseMove = (e) => {\n        if (isDragging) {\n          e.preventDefault()\n\n          // 取消之前的动画帧\n          if (animationId) {\n            cancelAnimationFrame(animationId)\n          }\n\n          // 使用 requestAnimationFrame 优化性能\n          animationId = requestAnimationFrame(() => {\n            const deltaX = e.clientX - lastX\n            const deltaY = e.clientY - lastY\n            translateX += deltaX\n            translateY += deltaY\n            lastX = e.clientX\n            lastY = e.clientY\n            updateTransform(false) // 拖拽不使用过渡动画，实现即时响应\n          })\n        }\n      }\n\n      const handleMouseUp = (e) => {\n        if (isDragging) {\n          e.preventDefault()\n          e.stopPropagation()\n          isDragging = false\n          modal.style.cursor = 'grab' // 始终显示可拖拽状态\n        }\n      }\n\n      // 双击重置\n      const handleDoubleClick = () => {\n        scale = 1\n        translateX = 0\n        translateY = 0\n        updateTransform(true) // 重置使用过渡动画\n        modal.style.cursor = 'grab' // 始终显示可拖拽状态\n      }\n\n      // 关闭模态框\n      const closeModal = () => {\n        // 恢复页面滚动\n        document.body.style.overflow = ''\n\n        // 取消动画帧\n        if (animationId) {\n          cancelAnimationFrame(animationId)\n        }\n\n        // 移除事件监听器\n        modal.removeEventListener('wheel', handleWheel)\n        modal.removeEventListener('mousedown', handleMouseDown)\n        document.removeEventListener('mousemove', handleMouseMove)\n        document.removeEventListener('mouseup', handleMouseUp)\n        modal.removeEventListener('dblclick', handleDoubleClick)\n        document.removeEventListener('keydown', handleKeyDown)\n\n        // 移除模态框\n        document.body.removeChild(modal)\n      }\n\n      // 键盘ESC关闭\n      const handleKeyDown = (e) => {\n        if (e.key === 'Escape') {\n          closeModal()\n        }\n      }\n\n      // 点击背景关闭（但不包括图片，且不在拖拽状态）\n      const handleModalClick = (e) => {\n        if (e.target === modal && !isDragging) {\n          closeModal()\n        }\n      }\n\n      // 添加事件监听器\n      modal.addEventListener('wheel', handleWheel)\n      modal.addEventListener('mousedown', handleMouseDown)\n      document.addEventListener('mousemove', handleMouseMove)\n      document.addEventListener('mouseup', handleMouseUp)\n      modal.addEventListener('dblclick', handleDoubleClick)\n      modal.addEventListener('click', handleModalClick)\n      document.addEventListener('keydown', handleKeyDown)\n\n      console.log('🔍 图片预览:', imageUrl)\n    },\n\n    // 滚动到指定区域\n    scrollToSection(sectionId) {\n      const element = document.getElementById(sectionId)\n      if (element) {\n        element.scrollIntoView({\n          behavior: 'smooth',\n          block: 'start'\n        })\n        this.activeNavItem = sectionId\n        console.log('🎯 滚动到区域:', sectionId)\n      }\n    },\n\n    // 快速滚动到使用说明\n    scrollToUsageGuide() {\n      this.scrollToSection('usage-guide')\n      console.log('📖 快速跳转到使用说明')\n    },\n\n    // 监听滚动事件，更新活跃导航项\n    handleScroll() {\n      const scrollTop = window.pageYOffset || document.documentElement.scrollTop\n      const windowHeight = window.innerHeight\n      const documentHeight = document.documentElement.scrollHeight\n\n      // 如果在页面顶部，直接激活第一个区域\n      if (scrollTop < 100) {\n        if (this.activeNavItem !== 'agent-info') {\n          this.activeNavItem = 'agent-info'\n          console.log('📍 页面顶部，激活:', this.activeNavItem)\n        }\n        return\n      }\n\n      // 获取所有区域的位置\n      const sections = this.navItems.map(item => {\n        const element = document.getElementById(item.id)\n        return {\n          id: item.id,\n          offsetTop: element ? element.offsetTop - 150 : 0,\n          element: element\n        }\n      })\n\n      // 检查是否滚动到页面底部\n      const isAtBottom = scrollTop + windowHeight >= documentHeight - 50\n\n      let currentSection = sections[0].id\n\n      if (isAtBottom) {\n        // 如果在页面底部，激活最后一个区域\n        currentSection = sections[sections.length - 1].id\n      } else {\n        // 找到当前滚动位置对应的区域\n        for (let i = sections.length - 1; i >= 0; i--) {\n          if (scrollTop >= sections[i].offsetTop) {\n            currentSection = sections[i].id\n            break\n          }\n        }\n      }\n\n      if (this.activeNavItem !== currentSection) {\n        this.activeNavItem = currentSection\n        console.log('📍 当前区域:', currentSection, isAtBottom ? '(页面底部)' : '')\n      }\n    },\n\n    // 工作流下载\n    async handleWorkflowDownload(workflow) {\n      try {\n        console.log('🔍 开始下载工作流:', workflow)\n\n        // 检查是否有下载地址\n        if (!workflow.workflowPackage && !workflow.packagePath) {\n          this.$message.error('工作流压缩包地址不存在')\n          return\n        }\n\n        // 设置下载状态\n        this.$set(workflow, 'downloading', true)\n\n        // 🌐 直接使用CDN地址下载，无需调用后端API\n        const downloadUrl = workflow.workflowPackage || workflow.packagePath\n        console.log('🔍 使用CDN地址下载工作流:', downloadUrl)\n\n        // 创建下载链接\n        const link = document.createElement('a')\n        link.href = downloadUrl\n        link.download = `${workflow.workflowName || '工作流'}.zip`\n        link.target = '_blank'\n\n        // 触发下载\n        document.body.appendChild(link)\n        link.click()\n        document.body.removeChild(link)\n\n        this.$message.success(`工作流 \"${workflow.workflowName}\" 下载已开始`)\n\n      } catch (error) {\n        console.error('🔍 工作流下载失败:', error)\n        this.$message.error('下载失败: ' + error.message)\n      } finally {\n        // 清除下载状态\n        this.$set(workflow, 'downloading', false)\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"less\" scoped>\n.agent-detail-page {\n  min-height: 100vh;\n  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);\n  position: relative;\n\n  // 时间轴式导航\n  .timeline-nav {\n    position: fixed;\n    left: calc(50% - 650px);\n    top: 200px;\n    z-index: 1000;\n\n    .timeline-line {\n      position: absolute;\n      left: 50%;\n      top: 0;\n      transform: translateX(-50%);\n      width: 2px;\n      height: 580px;\n      background: linear-gradient(to bottom, #e6f4ff 0%, #1890ff 50%, #e6f4ff 100%);\n      border-radius: 1px;\n    }\n\n    .timeline-node {\n      position: absolute;\n      left: 50%;\n      transform: translateX(-50%);\n      cursor: pointer;\n      transition: all 0.3s ease;\n\n      .node-label {\n        position: absolute;\n        right: 20px;\n        top: 50%;\n        transform: translateY(-50%);\n        font-size: 13px;\n        color: #8c8c8c;\n        font-weight: 500;\n        white-space: nowrap;\n        transition: all 0.3s ease;\n        opacity: 0.8;\n        text-align: right;\n      }\n\n      .node-dot {\n        width: 12px;\n        height: 12px;\n        border-radius: 50%;\n        background: #d9d9d9;\n        border: 2px solid white;\n        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);\n        transition: all 0.3s ease;\n        position: relative;\n        z-index: 2;\n      }\n\n      &:hover {\n        .node-dot {\n          background: #40a9ff;\n          transform: scale(1.3);\n          box-shadow: 0 4px 16px rgba(24, 144, 255, 0.4);\n        }\n\n        .node-label {\n          color: #40a9ff;\n          opacity: 1;\n        }\n      }\n\n      &.active {\n        .node-dot {\n          background: #1890ff;\n          transform: scale(1.4);\n          box-shadow: 0 4px 16px rgba(24, 144, 255, 0.6);\n          border-color: #1890ff;\n        }\n\n        .node-label {\n          color: #1890ff;\n          font-weight: 600;\n          opacity: 1;\n        }\n      }\n    }\n  }\n\n  // 权限检查加载状态\n  .permission-loading {\n    display: flex;\n    justify-content: center;\n    align-items: center;\n    height: 100vh;\n\n    .loading-text {\n      margin-top: 16px;\n      font-size: 16px;\n      color: #666;\n    }\n  }\n\n  // 权限拒绝页面\n  .permission-denied {\n    display: flex;\n    justify-content: center;\n    align-items: center;\n    height: 100vh;\n\n    .permission-content {\n      text-align: center;\n      padding: 48px;\n      background: white;\n      border-radius: 16px;\n      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\n      max-width: 400px;\n\n      .permission-icon {\n        font-size: 64px;\n        color: #1890ff;\n        margin-bottom: 24px;\n      }\n\n      h2 {\n        font-size: 24px;\n        margin-bottom: 16px;\n        color: #262626;\n      }\n\n      p {\n        font-size: 16px;\n        color: #666;\n        margin-bottom: 32px;\n        line-height: 1.6;\n      }\n\n      .action-buttons {\n        display: flex;\n        gap: 16px;\n        justify-content: center;\n      }\n    }\n  }\n\n  // 主要内容区域\n  .main-content {\n    max-width: 1200px;\n    margin: 0 auto;\n    padding: 24px;\n    position: relative;\n\n    // 页面头部\n    .page-header {\n      display: flex;\n      align-items: center;\n      padding: 20px 0;\n\n      .back-button {\n        margin-right: 20px;\n        border-radius: 8px;\n        height: 40px;\n        padding: 0 16px;\n        font-size: 15px;\n        font-weight: 500;\n        border: 1px solid #d9d9d9;\n        background: white;\n        color: #595959;\n        transition: all 0.3s ease;\n\n        &:hover {\n          border-color: #1890ff;\n          color: #1890ff;\n          background: #f6f9ff;\n        }\n\n        .anticon {\n          margin-right: 6px;\n          font-size: 14px;\n        }\n      }\n\n      .breadcrumb {\n        flex: 1;\n\n        /deep/ .ant-breadcrumb {\n          font-size: 16px;\n\n          .ant-breadcrumb-link {\n            color: #8c8c8c;\n            font-weight: 400;\n            transition: color 0.3s ease;\n\n            &:hover {\n              color: #1890ff;\n            }\n          }\n\n          .ant-breadcrumb-separator {\n            color: #d9d9d9;\n            margin: 0 12px;\n          }\n\n          .ant-breadcrumb-link a {\n            color: #1890ff;\n            text-decoration: none;\n            font-weight: 500;\n\n            &:hover {\n              color: #096dd9;\n            }\n          }\n        }\n      }\n    }\n\n    // 智能体信息区域\n    .agent-info-section {\n      background: white;\n      border-radius: 16px;\n      padding: 32px;\n      margin-bottom: 24px;\n      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);\n\n      .agent-basic-info {\n        display: flex;\n        gap: 24px;\n\n        .agent-avatar {\n          flex-shrink: 0;\n\n          img {\n            width: 120px;\n            height: 120px;\n            border-radius: 16px;\n            object-fit: cover;\n            border: 3px solid #f0f0f0;\n          }\n        }\n\n        .agent-details {\n          flex: 1;\n\n          .agent-name {\n            font-size: 28px;\n            font-weight: 600;\n            margin-bottom: 16px;\n            color: #262626;\n          }\n\n          .creator-info {\n            display: flex;\n            align-items: center;\n            margin-bottom: 16px;\n\n            .creator-avatar {\n              width: 32px;\n              height: 32px;\n              border-radius: 50%;\n              margin-right: 8px;\n              object-fit: cover;\n            }\n\n            .creator-name {\n              font-size: 16px;\n              color: #595959;\n              margin-right: 12px;\n            }\n\n            .author-type-badge {\n              padding: 4px 12px;\n              border-radius: 12px;\n              font-size: 12px;\n              font-weight: 500;\n\n              &.official {\n                background: linear-gradient(135deg, #52c41a 0%, #389e0d 100%);\n                color: white;\n              }\n\n              &.creator {\n                background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);\n                color: white;\n              }\n            }\n          }\n\n          .agent-description {\n            margin-bottom: 20px;\n\n            p {\n              font-size: 16px;\n              line-height: 1.6;\n              color: #595959;\n            }\n          }\n\n          .agent-stats {\n            display: flex;\n            gap: 32px;\n\n            .stat-item {\n              display: flex;\n              flex-direction: column;\n\n              .stat-label {\n                font-size: 14px;\n                color: #8c8c8c;\n                margin-bottom: 4px;\n              }\n\n              .stat-value {\n                font-size: 20px;\n                font-weight: 600;\n                color: #1890ff;\n              }\n            }\n          }\n        }\n      }\n\n      // 阅读提示\n      .reading-tip {\n        margin-top: 24px;\n        padding: 16px 20px;\n        background: linear-gradient(135deg, #fff7e6 0%, #ffecc7 100%);\n        border: 1px solid #ffd591;\n        border-radius: 12px;\n        display: flex;\n        align-items: center;\n        gap: 12px;\n        animation: tipPulse 2s ease-in-out infinite;\n        cursor: pointer;\n        transition: all 0.3s ease;\n\n        &:hover {\n          background: linear-gradient(135deg, #fff1d6 0%, #ffe7b3 100%);\n          border-color: #ffb84d;\n          transform: translateY(-2px);\n          box-shadow: 0 4px 12px rgba(255, 181, 77, 0.3);\n        }\n\n        &:active {\n          transform: translateY(0);\n          box-shadow: 0 2px 6px rgba(255, 181, 77, 0.2);\n        }\n\n        .tip-icon {\n          color: #fa8c16;\n          font-size: 16px;\n          flex-shrink: 0;\n        }\n\n        .tip-text {\n          color: #262626;\n          font-size: 14px;\n          line-height: 1.6;\n          font-weight: 500;\n          flex: 1;\n        }\n\n        .arrow-icon {\n          color: #fa8c16;\n          font-size: 16px;\n          animation: arrowBounce 1.5s ease-in-out infinite;\n        }\n      }\n    }\n\n    // 视频区域\n    .video-section {\n      background: white;\n      border-radius: 16px;\n      padding: 32px;\n      margin-bottom: 24px;\n      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);\n\n      .video-header {\n        display: flex;\n        align-items: center;\n        justify-content: space-between;\n        cursor: pointer;\n        user-select: none;\n        transition: all 0.3s ease;\n        padding: 8px 0;\n        border-radius: 8px;\n\n        &:hover {\n          background-color: #f5f5f5;\n          padding: 8px 12px;\n        }\n\n        h3 {\n          font-size: 20px;\n          font-weight: 600;\n          margin: 0;\n          color: #262626;\n        }\n\n        .collapse-icon {\n          font-size: 16px;\n          color: #8c8c8c;\n          transition: all 0.3s ease;\n\n          &:hover {\n            color: #1890ff;\n          }\n        }\n      }\n\n      .video-container {\n        position: relative;\n        margin-top: 20px;\n        transition: all 0.3s ease;\n\n        video {\n          width: 100%;\n          max-height: 500px;\n          border-radius: 12px;\n          background: #000;\n        }\n\n        .video-error {\n          position: absolute;\n          top: 50%;\n          left: 50%;\n          transform: translate(-50%, -50%);\n          color: #ff4d4f;\n          font-size: 16px;\n\n          .anticon {\n            margin-right: 8px;\n          }\n        }\n      }\n    }\n\n    // 使用说明区域\n    .usage-guide-section {\n      background: white;\n      border-radius: 16px;\n      padding: 32px;\n      margin-bottom: 24px;\n      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);\n\n      .guide-header {\n        display: flex;\n        align-items: center;\n        justify-content: space-between;\n        cursor: pointer;\n        user-select: none;\n        transition: all 0.3s ease;\n        padding: 8px 0;\n        border-radius: 8px;\n\n        &:hover {\n          background-color: #f5f5f5;\n          padding: 8px 12px;\n        }\n\n        h3 {\n          font-size: 20px;\n          font-weight: 600;\n          margin: 0;\n          color: #262626;\n        }\n\n        .collapse-icon {\n          font-size: 16px;\n          color: #8c8c8c;\n          transition: all 0.3s ease;\n\n          &:hover {\n            color: #1890ff;\n          }\n        }\n      }\n\n      .guide-content {\n        margin-top: 24px;\n\n        .guide-step {\n          display: flex;\n          align-items: flex-start;\n          gap: 16px;\n          margin-bottom: 32px;\n\n          &:last-child {\n            margin-bottom: 0;\n          }\n\n          .step-number {\n            width: 32px;\n            height: 32px;\n            background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);\n            color: white;\n            border-radius: 50%;\n            display: flex;\n            align-items: center;\n            justify-content: center;\n            font-weight: 600;\n            font-size: 16px;\n            flex-shrink: 0;\n            margin-top: 0;\n          }\n\n          .step-content {\n            flex: 1;\n\n            .step-text {\n              font-size: 15px;\n              line-height: 1.6;\n              color: #262626;\n              margin-bottom: 16px;\n\n              .coze-link {\n                color: #1890ff;\n                text-decoration: none;\n                font-weight: 500;\n                transition: color 0.3s ease;\n\n                &:hover {\n                  color: #096dd9;\n                  text-decoration: underline;\n                }\n\n                .anticon {\n                  margin-left: 4px;\n                  font-size: 12px;\n                }\n              }\n\n              .highlight-text {\n                color: #ff4d4f;\n                font-weight: 600;\n                background: linear-gradient(135deg, #fff2f0 0%, #ffece8 100%);\n                padding: 2px 6px;\n                border-radius: 4px;\n                border: 1px solid #ffccc7;\n              }\n            }\n\n            .step-image {\n              border-radius: 12px;\n              overflow: hidden;\n              border: 1px solid #f0f0f0;\n              box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n              position: relative;\n              cursor: pointer;\n              transition: all 0.3s ease;\n\n              &:hover {\n                box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);\n                transform: translateY(-2px);\n\n                .image-overlay {\n                  opacity: 1;\n                }\n\n                img {\n                  transform: scale(1.05);\n                }\n              }\n\n              img {\n                width: 100%;\n                height: auto;\n                display: block;\n                transition: transform 0.3s ease;\n              }\n\n              .image-overlay {\n                position: absolute;\n                top: 0;\n                left: 0;\n                right: 0;\n                bottom: 0;\n                background: rgba(0, 0, 0, 0.6);\n                display: flex;\n                flex-direction: column;\n                align-items: center;\n                justify-content: center;\n                opacity: 0;\n                transition: opacity 0.3s ease;\n                color: white;\n                font-size: 14px;\n\n                .anticon {\n                  font-size: 24px;\n                  margin-bottom: 8px;\n                }\n\n                span {\n                  font-weight: 500;\n                }\n              }\n            }\n          }\n        }\n      }\n    }\n\n    // 工作流区域\n    .workflow-section {\n      background: white;\n      border-radius: 16px;\n      padding: 32px;\n      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);\n\n      h3 {\n        font-size: 20px;\n        font-weight: 600;\n        margin-bottom: 20px;\n        color: #262626;\n      }\n\n      .workflow-loading {\n        text-align: center;\n        padding: 40px;\n      }\n\n      .workflow-empty {\n        text-align: center;\n        padding: 40px;\n      }\n\n      .workflow-list {\n        display: flex;\n        flex-direction: column;\n        gap: 20px;\n\n        .workflow-card {\n          border: 1px solid #f0f0f0;\n          border-radius: 12px;\n          padding: 24px;\n          transition: all 0.3s ease;\n          display: flex;\n          align-items: center;\n          gap: 20px;\n          width: 100%;\n          min-height: 120px;\n\n          &:hover {\n            border-color: #1890ff;\n            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);\n            transform: translateY(-2px);\n          }\n\n          .workflow-avatar {\n            flex-shrink: 0;\n\n            .workflow-image {\n              width: 80px;\n              height: 80px;\n              border-radius: 12px;\n              object-fit: cover;\n              border: 2px solid #f0f0f0;\n              transition: all 0.3s ease;\n            }\n          }\n\n          .workflow-info {\n            flex: 1;\n            min-width: 0;\n            display: flex;\n            flex-direction: column;\n            justify-content: center;\n\n            .workflow-name {\n              font-size: 18px;\n              font-weight: 600;\n              margin-bottom: 12px;\n              color: #262626;\n              line-height: 1.4;\n              word-wrap: break-word;\n              word-break: break-all;\n            }\n\n            .workflow-description {\n              font-size: 14px;\n              color: #8c8c8c;\n              line-height: 1.5;\n              display: -webkit-box;\n              -webkit-line-clamp: 2;\n              -webkit-box-orient: vertical;\n              overflow: hidden;\n              margin-bottom: 12px;\n            }\n\n            // 🔥 新增：输入参数说明样式\n            .workflow-params {\n              .params-label {\n                display: flex;\n                align-items: center;\n                gap: 6px;\n                font-size: 13px;\n                font-weight: 600;\n                color: #1890ff;\n                margin-bottom: 6px;\n\n                .anticon {\n                  font-size: 12px;\n                  color: #1890ff;\n                }\n              }\n\n              .params-content {\n                font-size: 13px;\n                color: #262626;\n                line-height: 1.4;\n                background: linear-gradient(135deg, #e6f7ff 0%, #f0f9ff 100%);\n                padding: 10px 14px;\n                border-radius: 8px;\n                border: 1px solid #91d5ff;\n                border-left: 4px solid #1890ff;\n                display: -webkit-box;\n                -webkit-line-clamp: 3;\n                -webkit-box-orient: vertical;\n                overflow: hidden;\n                word-wrap: break-word;\n                font-weight: 500;\n                box-shadow: 0 2px 4px rgba(24, 144, 255, 0.1);\n              }\n            }\n          }\n\n          .workflow-actions {\n            flex-shrink: 0;\n            display: flex;\n            align-items: center;\n            justify-content: center;\n\n            .download-btn {\n              background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);\n              border: none;\n              border-radius: 8px;\n              height: 40px;\n              padding: 0 20px;\n              font-weight: 500;\n              box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);\n              transition: all 0.3s ease;\n\n              &:hover {\n                background: linear-gradient(135deg, #40a9ff 0%, #1890ff 100%);\n                box-shadow: 0 4px 12px rgba(24, 144, 255, 0.4);\n                transform: translateY(-1px);\n              }\n\n              &:active {\n                transform: translateY(0);\n              }\n\n              &:disabled {\n                background: #f5f5f5;\n                color: #bfbfbf;\n                box-shadow: none;\n                cursor: not-allowed;\n\n                &:hover {\n                  background: #f5f5f5;\n                  transform: none;\n                }\n              }\n\n              .anticon {\n                margin-right: 6px;\n                font-size: 16px;\n              }\n\n              span {\n                font-size: 14px;\n              }\n            }\n          }\n        }\n      }\n\n      // 使用提示\n      .workflow-usage-tip {\n        margin-top: 24px;\n        padding: 16px 20px;\n        background: linear-gradient(135deg, #f6f9ff 0%, #e6f4ff 100%);\n        border: 1px solid #b3d8ff;\n        border-radius: 12px;\n        display: flex;\n        align-items: flex-start;\n        gap: 12px;\n\n        .tip-icon {\n          color: #1890ff;\n          font-size: 16px;\n          margin-top: 2px;\n          flex-shrink: 0;\n        }\n\n        .tip-text {\n          color: #262626;\n          font-size: 14px;\n          line-height: 1.6;\n          font-weight: 400;\n\n          .highlight-text {\n            color: #ff4d4f;\n            font-weight: 600;\n            background: linear-gradient(135deg, #fff2f0 0%, #ffece8 100%);\n            padding: 2px 6px;\n            border-radius: 4px;\n            border: 1px solid #ffccc7;\n          }\n        }\n      }\n    }\n  }\n}\n\n// 动画效果\n@keyframes tipPulse {\n  0%, 100% {\n    transform: scale(1);\n  }\n  50% {\n    transform: scale(1.02);\n  }\n}\n\n@keyframes arrowBounce {\n  0%, 100% {\n    transform: translateY(0);\n  }\n  50% {\n    transform: translateY(-3px);\n  }\n}\n\n// 响应式设计\n@media (max-width: 768px) {\n  .agent-detail-page {\n    // 移动端时间轴导航\n    .timeline-nav {\n      left: 20px;\n      top: 150px;\n\n      .timeline-line {\n        height: 400px;\n      }\n\n      .timeline-node {\n        .node-label {\n          font-size: 12px;\n          right: 16px;\n        }\n\n        .node-dot {\n          width: 10px;\n          height: 10px;\n        }\n\n        &:hover .node-dot {\n          transform: scale(1.2);\n        }\n\n        &.active .node-dot {\n          transform: scale(1.3);\n        }\n      }\n    }\n\n    .main-content {\n      padding: 16px;\n\n      .page-header {\n        margin-bottom: 20px;\n        padding: 16px 0;\n        flex-direction: column;\n        align-items: flex-start;\n        gap: 12px;\n\n        .back-button {\n          margin-right: 0;\n          margin-bottom: 8px;\n          height: 36px;\n          font-size: 14px;\n        }\n\n        .breadcrumb {\n          /deep/ .ant-breadcrumb {\n            font-size: 14px;\n\n            .ant-breadcrumb-separator {\n              margin: 0 8px;\n            }\n          }\n        }\n      }\n\n      .agent-info-section {\n        padding: 20px;\n\n        .agent-basic-info {\n          flex-direction: column;\n          text-align: center;\n\n          .agent-details {\n            .agent-stats {\n              justify-content: center;\n            }\n          }\n        }\n\n        .reading-tip {\n          margin-top: 20px;\n          padding: 14px 16px;\n          gap: 10px;\n\n          .tip-icon {\n            font-size: 14px;\n          }\n\n          .tip-text {\n            font-size: 13px;\n            line-height: 1.5;\n          }\n\n          .arrow-icon {\n            font-size: 14px;\n          }\n        }\n      }\n\n      .video-section,\n      .usage-guide-section,\n      .workflow-section {\n        padding: 20px;\n      }\n\n      .usage-guide-section {\n        .guide-content {\n          margin-top: 20px;\n\n          .guide-step {\n            gap: 12px;\n            margin-bottom: 24px;\n\n            .step-number {\n              width: 28px;\n              height: 28px;\n              font-size: 14px;\n            }\n\n            .step-content {\n              .step-text {\n                font-size: 14px;\n                margin-bottom: 12px;\n              }\n\n              .step-image {\n                border-radius: 8px;\n              }\n            }\n          }\n        }\n      }\n\n      .workflow-list {\n        gap: 16px;\n\n        .workflow-card {\n          flex-direction: column;\n          text-align: center;\n          gap: 16px;\n          padding: 20px;\n\n          .workflow-avatar {\n            align-self: center;\n\n            .workflow-image {\n              width: 50px;\n              height: 50px;\n            }\n          }\n\n          .workflow-info {\n            .workflow-name {\n              white-space: normal;\n              text-align: center;\n            }\n\n            // 🔥 移动端输入参数说明样式优化\n            .workflow-params {\n              text-align: left;\n              margin-top: 12px;\n\n              .params-label {\n                justify-content: center;\n                font-size: 12px;\n                font-weight: 600;\n                color: #1890ff;\n              }\n\n              .params-content {\n                font-size: 12px;\n                padding: 8px 12px;\n                text-align: left;\n                -webkit-line-clamp: 2; // 移动端显示更少行数\n                color: #262626;\n                font-weight: 500;\n                background: linear-gradient(135deg, #e6f7ff 0%, #f0f9ff 100%);\n                border: 1px solid #91d5ff;\n                border-left: 3px solid #1890ff;\n                box-shadow: 0 1px 3px rgba(24, 144, 255, 0.1);\n              }\n            }\n          }\n\n          .workflow-actions {\n            align-self: center;\n\n            .download-btn {\n              width: 100%;\n              max-width: 200px;\n            }\n          }\n        }\n      }\n\n      .workflow-usage-tip {\n        margin-top: 20px;\n        padding: 14px 16px;\n        gap: 10px;\n\n        .tip-icon {\n          font-size: 14px;\n        }\n\n        .tip-text {\n          font-size: 13px;\n          line-height: 1.5;\n        }\n      }\n    }\n  }\n}\n</style>\n"]}]}