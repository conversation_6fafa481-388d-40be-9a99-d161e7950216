/**
 * 网络诊断工具
 * 用于诊断版本检查的网络连接问题
 */

const https = require('https');
const dns = require('dns');
const { promisify } = require('util');

class NetworkDiagnostic {
  constructor() {
    this.dnsLookup = promisify(dns.lookup);
  }

  /**
   * 诊断网络连接
   * @param {string} url 要测试的URL
   * @returns {Object} 诊断结果
   */
  async diagnose(url) {
    const result = {
      url: url,
      timestamp: new Date().toISOString(),
      tests: {}
    };

    try {
      const urlObj = new URL(url);
      const hostname = urlObj.hostname;
      const port = urlObj.port || (urlObj.protocol === 'https:' ? 443 : 80);

      console.log(`🔍 开始网络诊断: ${url}`);

      // 1. DNS解析测试
      result.tests.dns = await this.testDNS(hostname);

      // 2. TCP连接测试
      result.tests.tcp = await this.testTCP(hostname, port);

      // 3. HTTPS握手测试
      if (urlObj.protocol === 'https:') {
        result.tests.https = await this.testHTTPS(hostname, port);
      }

      // 4. HTTP请求测试
      result.tests.http = await this.testHTTP(url);

      // 5. 生成诊断建议
      result.suggestions = this.generateSuggestions(result.tests);

      console.log('🎯 网络诊断完成:', result);
      return result;

    } catch (error) {
      result.error = error.message;
      result.suggestions = ['网络诊断过程中发生错误，请检查网络连接'];
      return result;
    }
  }

  /**
   * DNS解析测试
   */
  async testDNS(hostname) {
    try {
      const start = Date.now();
      const address = await this.dnsLookup(hostname);
      const duration = Date.now() - start;

      return {
        success: true,
        address: address.address,
        family: address.family,
        duration: duration,
        message: `DNS解析成功: ${hostname} -> ${address.address} (${duration}ms)`
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        message: `DNS解析失败: ${error.message}`
      };
    }
  }

  /**
   * TCP连接测试
   */
  async testTCP(hostname, port) {
    return new Promise((resolve) => {
      const start = Date.now();
      const socket = new (require('net').Socket)();

      const timeout = setTimeout(() => {
        socket.destroy();
        resolve({
          success: false,
          error: 'Connection timeout',
          message: `TCP连接超时: ${hostname}:${port}`
        });
      }, 10000);

      socket.connect(port, hostname, () => {
        clearTimeout(timeout);
        const duration = Date.now() - start;
        socket.destroy();
        resolve({
          success: true,
          duration: duration,
          message: `TCP连接成功: ${hostname}:${port} (${duration}ms)`
        });
      });

      socket.on('error', (error) => {
        clearTimeout(timeout);
        resolve({
          success: false,
          error: error.message,
          message: `TCP连接失败: ${error.message}`
        });
      });
    });
  }

  /**
   * HTTPS握手测试
   */
  async testHTTPS(hostname, port) {
    return new Promise((resolve) => {
      const start = Date.now();
      
      const options = {
        hostname: hostname,
        port: port,
        method: 'HEAD',
        rejectUnauthorized: false // 忽略证书验证
      };

      const req = https.request(options, (res) => {
        const duration = Date.now() - start;
        resolve({
          success: true,
          statusCode: res.statusCode,
          duration: duration,
          message: `HTTPS握手成功: ${hostname}:${port} (${duration}ms)`
        });
      });

      req.on('error', (error) => {
        resolve({
          success: false,
          error: error.message,
          message: `HTTPS握手失败: ${error.message}`
        });
      });

      req.setTimeout(10000, () => {
        req.destroy();
        resolve({
          success: false,
          error: 'Request timeout',
          message: `HTTPS握手超时: ${hostname}:${port}`
        });
      });

      req.end();
    });
  }

  /**
   * HTTP请求测试
   */
  async testHTTP(url) {
    return new Promise((resolve) => {
      const start = Date.now();
      
      const urlObj = new URL(url);
      const options = {
        hostname: urlObj.hostname,
        port: urlObj.port || (urlObj.protocol === 'https:' ? 443 : 80),
        path: urlObj.pathname + urlObj.search,
        method: 'GET',
        headers: {
          'User-Agent': 'NetworkDiagnostic/1.0'
        },
        rejectUnauthorized: false
      };

      const protocol = urlObj.protocol === 'https:' ? https : require('http');
      
      const req = protocol.request(options, (res) => {
        const duration = Date.now() - start;
        let data = '';
        
        res.on('data', (chunk) => {
          data += chunk;
        });
        
        res.on('end', () => {
          resolve({
            success: res.statusCode >= 200 && res.statusCode < 300,
            statusCode: res.statusCode,
            duration: duration,
            responseSize: data.length,
            message: `HTTP请求完成: ${res.statusCode} (${duration}ms, ${data.length} bytes)`
          });
        });
      });

      req.on('error', (error) => {
        resolve({
          success: false,
          error: error.message,
          message: `HTTP请求失败: ${error.message}`
        });
      });

      req.setTimeout(15000, () => {
        req.destroy();
        resolve({
          success: false,
          error: 'Request timeout',
          message: 'HTTP请求超时'
        });
      });

      req.end();
    });
  }

  /**
   * 生成诊断建议
   */
  generateSuggestions(tests) {
    const suggestions = [];

    if (!tests.dns?.success) {
      suggestions.push('DNS解析失败，请检查网络连接或尝试更换DNS服务器');
    }

    if (!tests.tcp?.success) {
      suggestions.push('TCP连接失败，可能是防火墙阻止或网络不稳定');
    }

    if (!tests.https?.success) {
      suggestions.push('HTTPS握手失败，可能是SSL/TLS配置问题');
    }

    if (!tests.http?.success) {
      suggestions.push('HTTP请求失败，可能是服务器问题或网络中断');
    }

    if (tests.dns?.success && tests.tcp?.success && tests.https?.success && !tests.http?.success) {
      suggestions.push('网络连接正常但HTTP请求失败，可能是服务器端问题');
    }

    if (suggestions.length === 0) {
      suggestions.push('网络连接正常，如果仍有问题请检查应用配置');
    }

    return suggestions;
  }
}

module.exports = NetworkDiagnostic;
