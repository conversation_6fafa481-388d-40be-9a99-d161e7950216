<template>
  <div class="aigc-dashboard">
    <a-spin :spinning="loading" tip="加载仪表板数据中...">
    <!-- 顶部欢迎区域 -->
    <div class="welcome-section">
      <a-card :bordered="false" class="welcome-card">
        <div class="welcome-content">
          <div class="welcome-left">
            <h1 class="welcome-title">欢迎回来，{{ userInfo.nickname || '用户' }}！</h1>
            <p class="welcome-subtitle">{{ getCurrentTimeGreeting() }}</p>
            <div class="user-stats">
              <a-tag color="blue" class="user-level-tag">
                {{ getUserLevelText(userInfo.memberLevel) }}
              </a-tag>
              <a-tag v-if="isAdmin" color="red" class="admin-tag">
                系统管理员
              </a-tag>
              <span class="balance-info">
                账户余额：<span class="balance-amount">¥{{ userInfo.accountBalance || 0 }}</span>
              </span>
            </div>
          </div>
          <div class="welcome-right">
            <div class="avatar-section">
              <a-avatar :size="80" :src="userInfo.avatar" class="user-avatar">
                {{ userInfo.nickname ? userInfo.nickname.charAt(0) : 'U' }}
              </a-avatar>
              <div class="online-status">
                <a-badge status="processing" text="在线" />
              </div>
            </div>
            <div class="quick-actions">
              <a-button type="primary" size="large" @click="goToWebsite" class="website-btn">
                <a-icon type="home" />
                进入官网首页
              </a-button>
            </div>
          </div>
        </div>
      </a-card>
    </div>

    <!-- 统计卡片区域 -->
    <div class="stats-section">
      <a-row :gutter="24">
        <a-col :xl="6" :lg="12" :md="12" :sm="24" :xs="24">
          <a-card :bordered="false" class="stat-card">
            <a-statistic
              :title="isAdmin ? '今日系统调用' : '今日API调用'"
              :value="apiStats.todayCalls"
              :precision="0"
              suffix="次"
              :value-style="{ color: '#3f8600' }"
            >
              <template #prefix>
                <a-icon type="api" />
              </template>
            </a-statistic>
            <div class="stat-trend">
              <a-icon type="arrow-up" style="color: #3f8600" />
              <span style="color: #3f8600">{{ apiStats.todayGrowth }}%</span>
              <span class="trend-text">较昨日</span>
            </div>
          </a-card>
        </a-col>

        <a-col :xl="6" :lg="12" :md="12" :sm="24" :xs="24">
          <a-card :bordered="false" class="stat-card">
            <a-statistic
              :title="isAdmin ? '本月系统调用' : '本月总调用'"
              :value="apiStats.monthCalls"
              :precision="0"
              suffix="次"
              :value-style="{ color: '#1890ff' }"
            >
              <template #prefix>
                <a-icon type="bar-chart" />
              </template>
            </a-statistic>
            <div class="stat-trend">
              <a-icon type="arrow-up" style="color: #1890ff" />
              <span style="color: #1890ff">{{ apiStats.monthGrowth }}%</span>
              <span class="trend-text">较上月</span>
            </div>
          </a-card>
        </a-col>

        <a-col :xl="6" :lg="12" :md="12" :sm="24" :xs="24">
          <a-card :bordered="false" class="stat-card">
            <a-statistic
              title="成功率"
              :value="apiStats.successRate"
              :precision="2"
              suffix="%"
              :value-style="{ color: '#722ed1' }"
            >
              <template #prefix>
                <a-icon type="check-circle" />
              </template>
            </a-statistic>
            <div class="stat-trend">
              <a-icon type="arrow-up" style="color: #722ed1" />
              <span style="color: #722ed1">{{ apiStats.successRateGrowth }}%</span>
              <span class="trend-text">较昨日</span>
            </div>
          </a-card>
        </a-col>

        <a-col :xl="6" :lg="12" :md="12" :sm="24" :xs="24">
          <a-card :bordered="false" class="stat-card">
            <a-statistic
              :title="isAdmin ? '系统总收入' : '总消费金额'"
              :value="userInfo.totalConsumption || 0"
              :precision="2"
              prefix="¥"
              :value-style="{ color: '#fa8c16' }"
            >
              <template #prefix>
                <a-icon type="dollar" />
              </template>
            </a-statistic>
            <div class="stat-trend">
              <a-icon type="arrow-up" style="color: #fa8c16" />
              <span style="color: #fa8c16">{{ isAdmin ? '系统累计' : '本月累计' }}</span>
            </div>
          </a-card>
        </a-col>

        <!-- 桌面应用下载统计（仅管理员可见） -->
        <a-col v-if="isAdmin" :xl="6" :lg="12" :md="12" :sm="24" :xs="24">
          <a-card :bordered="false" class="stat-card">
            <a-statistic
              title="今日应用下载"
              :value="downloadStats.todayTotal"
              :precision="0"
              suffix="次"
              :value-style="{ color: '#722ed1' }"
            >
              <template #prefix>
                <a-icon type="download" />
              </template>
            </a-statistic>
            <div class="stat-trend">
              <a-icon type="windows" style="color: #1890ff; margin-right: 4px" />
              <span style="color: #1890ff">{{ downloadStats.todayWindows }}</span>
              <a-icon type="apple" style="color: #52c41a; margin-left: 8px; margin-right: 4px" />
              <span style="color: #52c41a">{{ downloadStats.todayMac }}</span>
            </div>
          </a-card>
        </a-col>

      </a-row>
    </div>

    <!-- 图表和实时监控区域 -->
    <div class="charts-section">
      <a-row :gutter="24">
        <!-- API调用趋势图 -->
        <a-col :xl="16" :lg="24" :md="24" :sm="24" :xs="24">
          <a-card :bordered="false" :title="isAdmin ? '系统API调用趋势' : 'API调用趋势'" class="chart-card">
            <template #extra>
              <a-radio-group v-model="chartTimeRange" @change="onTimeRangeChange">
                <a-radio-button value="today">今日</a-radio-button>
                <a-radio-button value="week">本周</a-radio-button>
                <a-radio-button value="month">本月</a-radio-button>
              </a-radio-group>
            </template>
            <div id="apiTrendChart" style="height: 400px;"></div>
          </a-card>
        </a-col>

        <!-- 系统状态监控 -->
        <a-col :xl="8" :lg="24" :md="24" :sm="24" :xs="24">
          <a-card :bordered="false" title="系统状态" class="monitor-card">
            <template #extra>
              <a-badge :status="monitorStatus" :text="monitorStatusText" />
            </template>

            <!-- 系统运行状态 -->
            <div class="monitor-item">
              <div class="monitor-label">系统状态</div>
              <div class="monitor-value">
                <a-tag color="green">正常运行</a-tag>
              </div>
            </div>

            <!-- 当前在线用户 -->
            <div class="monitor-item">
              <div class="monitor-label">{{ isAdmin ? '系统在线用户' : '在线用户' }}</div>
              <div class="monitor-value">
                {{ realTimeStats.onlineUsers || 0 }} 人
              </div>
            </div>

            <!-- 今日活跃度 -->
            <div class="monitor-item">
              <div class="monitor-label">{{ isAdmin ? '系统今日活跃' : '今日活跃' }}</div>
              <div class="monitor-value">
                <span class="activity-value">{{ realTimeStats.todayActiveUsers || 0 }}</span>
                <span class="activity-unit">人次</span>
              </div>
            </div>

            <!-- 最近调用记录 -->
            <div class="recent-calls">
              <h4>{{ isAdmin ? '系统最近调用记录' : '最近调用记录' }}</h4>
              <div class="call-list">
                <div
                  v-for="call in recentCalls"
                  :key="call.id"
                  class="call-item"
                  :class="{ 'call-success': call.success, 'call-error': !call.success }"
                >
                  <div class="call-time">{{ formatTime(call.time) }}</div>
                  <div class="call-api">{{ call.apiType }}</div>
                  <div v-if="isAdmin && call.userId" class="call-user">{{ call.userId }}</div>
                  <div class="call-status">
                    <a-icon :type="call.success ? 'check-circle' : 'close-circle'" />
                  </div>
                </div>
              </div>
            </div>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 详细统计区域 -->
    <div class="detail-stats-section">
      <a-row :gutter="24">
        <!-- API使用分布 -->
        <a-col :xl="12" :lg="24" :md="24" :sm="24" :xs="24">
          <a-card :bordered="false" title="API使用分布" class="chart-card">
            <div id="apiDistributionChart" style="height: 300px;"></div>
          </a-card>
        </a-col>

        <!-- 错误统计 -->
        <a-col :xl="12" :lg="24" :md="24" :sm="24" :xs="24">
          <a-card :bordered="false" title="错误统计" class="chart-card">
            <div id="errorStatsChart" style="height: 300px;"></div>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 桌面应用下载统计区域（仅管理员可见） -->
    <div v-if="isAdmin" class="download-stats-section">
      <a-row :gutter="24">
        <!-- 下载趋势图 -->
        <a-col :xl="16" :lg="24" :md="24" :sm="24" :xs="24">
          <a-card :bordered="false" title="桌面应用下载趋势" class="chart-card">
            <template #extra>
              <a-radio-group v-model="downloadTimeRange" @change="onDownloadTimeRangeChange">
                <a-radio-button value="7">最近7天</a-radio-button>
                <a-radio-button value="14">最近14天</a-radio-button>
                <a-radio-button value="30">最近30天</a-radio-button>
              </a-radio-group>
            </template>
            <div id="downloadTrendChart" style="height: 400px;"></div>
          </a-card>
        </a-col>

        <!-- 下载统计详情 -->
        <a-col :xl="8" :lg="24" :md="24" :sm="24" :xs="24">
          <a-card :bordered="false" title="下载统计详情" class="chart-card">
            <div class="download-stats-detail">
              <!-- 平台统计 -->
              <div class="platform-stats">
                <h4>平台分布</h4>
                <div class="platform-item">
                  <a-icon type="windows" style="color: #1890ff; margin-right: 8px" />
                  <span>Windows</span>
                  <span class="platform-count">{{ downloadStats.totalWindows }}次</span>
                </div>
                <div class="platform-item">
                  <a-icon type="apple" style="color: #52c41a; margin-right: 8px" />
                  <span>Mac</span>
                  <span class="platform-count">{{ downloadStats.totalMac }}次</span>
                </div>
              </div>

              <!-- 成功率统计 -->
              <div class="success-rate-stats">
                <h4>下载成功率</h4>
                <a-progress
                  :percent="downloadStats.successRate"
                  :stroke-color="downloadStats.successRate >= 95 ? '#52c41a' : '#faad14'"
                  :format="percent => `${percent}%`"
                />
                <p class="success-detail">
                  成功：{{ downloadStats.successCount }}次 /
                  失败：{{ downloadStats.failCount }}次
                </p>
              </div>

              <!-- 独立IP统计 -->
              <div class="ip-stats">
                <h4>独立访问IP</h4>
                <div class="ip-count">{{ downloadStats.uniqueIpCount }}</div>
                <p class="ip-detail">今日独立下载用户数</p>
              </div>
            </div>
          </a-card>
        </a-col>
      </a-row>

      <!-- 最近下载记录 -->
      <a-row :gutter="24" style="margin-top: 24px;">
        <a-col :span="24">
          <a-card :bordered="false" title="最近下载记录" class="chart-card">
            <a-table
              :columns="downloadRecordColumns"
              :data-source="downloadStats.recentDownloads"
              :pagination="downloadRecordPagination"
              size="small"
              @change="handleDownloadRecordTableChange"
            >
              <template slot="platform" slot-scope="text">
                <a-tag :color="text === 'windows' ? 'blue' : 'green'">
                  <a-icon :type="text === 'windows' ? 'windows' : 'apple'" />
                  {{ text === 'windows' ? 'Windows' : 'Mac' }}
                </a-tag>
              </template>
              <template slot="downloadStatus" slot-scope="text">
                <a-tag :color="text === 1 ? 'green' : 'red'">
                  <a-icon :type="text === 1 ? 'check-circle' : 'close-circle'" />
                  {{ text === 1 ? '成功' : '失败' }}
                </a-tag>
              </template>
              <template slot="requestTime" slot-scope="text">
                {{ formatDateTime(text) }}
              </template>
              <template slot="responseTime" slot-scope="text">
                <span :style="{ color: text > 1000 ? '#ff4d4f' : '#52c41a' }">
                  {{ text }}ms
                </span>
              </template>
            </a-table>
          </a-card>
        </a-col>
      </a-row>
    </div>
    </a-spin>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import { getDashboardData, getDesktopDownloadStats } from '@/api/usercenter'
import { HeartbeatMixin } from '@/mixins/HeartbeatMixin'
import { getCurrentPageConfig } from '@/utils/heartbeatConfig'

export default {
  name: 'Analysis',
  mixins: [HeartbeatMixin],
  data() {
    return {
      // 心跳配置 - 管理后台使用高频心跳
      heartbeatConfig: getCurrentPageConfig('admin', {
        apiKey: 'admin-dashboard-heartbeat-key', // 管理后台专用API密钥
        enableDebugLog: process.env.NODE_ENV === 'development',
      }),

      // 用户信息
      userInfo: {
        nickname: '智界用户',
        memberLevel: 1,
        accountBalance: 0,
        avatar: ''
      },

      // API统计数据
      apiStats: {
        todayCalls: 0,
        todayGrowth: 0,
        monthCalls: 0,
        monthGrowth: 0,
        successRate: 99.0,
        successRateGrowth: 0
      },

      // 实时统计
      realTimeStats: {
        onlineUsers: 0,
        todayActiveUsers: 0
      },

      // 监控状态
      monitorStatus: 'processing',
      monitorStatusText: '正常运行',

      // 图表时间范围
      chartTimeRange: 'today',

      // 最近调用记录
      recentCalls: [],

      // 图表数据
      chartData: {
        trendData: null,
        distributionData: null,
        errorStatsData: null
      },

      // 加载状态
      loading: false,

      // 定时器
      refreshTimer: null,
      chartInstances: {},

      // 用户角色信息
      isAdmin: false,
      userRole: 'user',

      // 桌面应用下载统计数据
      downloadStats: {
        todayTotal: 0,
        todayWindows: 0,
        todayMac: 0,
        totalWindows: 0,
        totalMac: 0,
        successRate: 100,
        successCount: 0,
        failCount: 0,
        uniqueIpCount: 0,
        recentDownloads: []
      },

      // 下载统计时间范围
      downloadTimeRange: '7',

      // 🔥 下载记录分页配置
      downloadRecordPagination: {
        current: 1,
        pageSize: 10,
        total: 0,
        showSizeChanger: true,
        showQuickJumper: true,
        showTotal: (total, range) => `共 ${total} 条记录`,
        pageSizeOptions: ['10', '20', '50', '100']
      },

      // 下载记录表格列定义
      downloadRecordColumns: [
        {
          title: '平台',
          dataIndex: 'platform',
          key: 'platform',
          scopedSlots: { customRender: 'platform' },
          width: 100
        },
        {
          title: '文件名',
          dataIndex: 'fileName',
          key: 'fileName',
          ellipsis: true
        },
        {
          title: '客户端IP',
          dataIndex: 'clientIp',
          key: 'clientIp',
          width: 120
        },
        {
          title: '状态',
          dataIndex: 'downloadStatus',
          key: 'downloadStatus',
          scopedSlots: { customRender: 'downloadStatus' },
          width: 80
        },
        {
          title: '响应时间',
          dataIndex: 'responseTime',
          key: 'responseTime',
          scopedSlots: { customRender: 'responseTime' },
          width: 100
        },
        {
          title: '下载时间',
          dataIndex: 'requestTime',
          key: 'requestTime',
          scopedSlots: { customRender: 'requestTime' },
          width: 160
        }
      ]
    }
  },

  mounted() {
    this.initUserRole()
    this.loadDashboardData()
    // 取消定时刷新：管理员手动刷新即可，减少服务器压力
    // this.startRealTimeMonitoring()
  },

  beforeDestroy() {
    if (this.refreshTimer) {
      clearInterval(this.refreshTimer)
    }
    // 销毁图表实例
    Object.values(this.chartInstances).forEach(chart => {
      if (chart) {
        chart.dispose()
      }
    })
  },

  methods: {
    // 初始化用户角色信息
    initUserRole() {
      try {
        const role = localStorage.getItem('userRole') // ✅ 统一使用 userRole
        this.userRole = role || 'user'
        this.isAdmin = role && role.toLowerCase() === 'admin'
        console.log('用户角色:', this.userRole, '是否管理员:', this.isAdmin)

        // 如果是管理员，延迟加载下载统计数据
        if (this.isAdmin) {
          this.$nextTick(() => {
            this.loadDownloadStats()
          })
        }
      } catch (error) {
        console.error('获取用户角色失败:', error)
        this.userRole = 'user'
        this.isAdmin = false
      }
    },

    // 获取当前时间问候语
    getCurrentTimeGreeting() {
      const hour = new Date().getHours()
      if (hour < 6) return '夜深了，注意休息'
      if (hour < 12) return '早上好，新的一天开始了'
      if (hour < 18) return '下午好，工作顺利'
      return '晚上好，辛苦了'
    },

    // 获取用户等级文本
    getUserLevelText(level) {
      const levels = { 1: '普通用户', 2: 'VIP用户', 3: 'SVIP用户' }
      return levels[level] || '普通用户'
    },

    // 跳转到官网首页
    goToWebsite() {
      this.$router.push('/home')
    },

    // 格式化日期时间
    formatDateTime(dateTime) {
      if (!dateTime) return '-'
      const date = new Date(dateTime)
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })
    },

    // 下载统计时间范围变化
    onDownloadTimeRangeChange() {
      // 重置分页到第一页
      this.downloadRecordPagination.current = 1
      this.loadDownloadStats()
    },

    // 🔥 下载记录表格分页变化处理
    handleDownloadRecordTableChange(pagination) {
      this.downloadRecordPagination.current = pagination.current
      this.downloadRecordPagination.pageSize = pagination.pageSize
      this.loadDownloadStats()
    },

    // 🔥 加载桌面应用下载统计数据（支持分页）
    async loadDownloadStats() {
      if (!this.isAdmin) return

      try {
        const response = await getDesktopDownloadStats({
          days: parseInt(this.downloadTimeRange),
          pageNo: this.downloadRecordPagination.current,
          pageSize: this.downloadRecordPagination.pageSize
        })

        if (response.success && response.result) {
          const data = response.result

          // 处理今日统计数据
          if (data.todayStats && data.todayStats.length > 0) {
            let todayWindows = 0
            let todayMac = 0
            let todayTotal = 0

            data.todayStats.forEach(stat => {
              if (stat.platform === 'windows') {
                todayWindows = stat.downloadCount || 0
              } else if (stat.platform === 'mac') {
                todayMac = stat.downloadCount || 0
              }
              todayTotal += stat.downloadCount || 0
            })

            this.downloadStats.todayWindows = todayWindows
            this.downloadStats.todayMac = todayMac
            this.downloadStats.todayTotal = todayTotal
          }

          // 处理平台统计数据
          if (data.platformStats && data.platformStats.length > 0) {
            let totalWindows = 0
            let totalMac = 0

            data.platformStats.forEach(stat => {
              if (stat.platform === 'windows') {
                totalWindows = stat.totalCount || 0
              } else if (stat.platform === 'mac') {
                totalMac = stat.totalCount || 0
              }
            })

            this.downloadStats.totalWindows = totalWindows
            this.downloadStats.totalMac = totalMac
          }

          // 处理成功率统计
          if (data.todayStats && data.todayStats.length > 0) {
            let totalSuccess = 0
            let totalFail = 0
            let uniqueIps = new Set()

            data.todayStats.forEach(stat => {
              totalSuccess += stat.successCount || 0
              totalFail += stat.failCount || 0
              if (stat.uniqueIpCount) {
                // 这里简化处理，实际应该去重
                uniqueIps.add(stat.platform)
              }
            })

            this.downloadStats.successCount = totalSuccess
            this.downloadStats.failCount = totalFail
            this.downloadStats.uniqueIpCount = data.todayStats.reduce((sum, stat) =>
              sum + (stat.uniqueIpCount || 0), 0)

            const total = totalSuccess + totalFail
            this.downloadStats.successRate = total > 0 ?
              Math.round((totalSuccess / total) * 100) : 100
          }

          // 🔥 处理最近下载记录（支持分页）
          if (data.recentDownloads) {
            this.downloadStats.recentDownloads = data.recentDownloads.map(record => ({
              ...record,
              key: record.id
            }))

            // 更新分页信息
            this.downloadRecordPagination.total = data.recentDownloadsTotal || 0
            this.downloadRecordPagination.current = data.recentDownloadsCurrent || 1
            this.downloadRecordPagination.pageSize = data.recentDownloadsSize || 10
          }

          // 处理趋势数据并更新图表
          if (data.trendData) {
            this.updateDownloadTrendChart(data.trendData)
          }

          console.log('桌面应用下载统计数据加载成功:', this.downloadStats)
        }

      } catch (error) {
        console.error('加载桌面应用下载统计失败:', error)
        this.$message.error('加载下载统计数据失败')
      }
    },

    // 更新下载趋势图表
    updateDownloadTrendChart(trendData) {
      this.$nextTick(() => {
        const chartDom = document.getElementById('downloadTrendChart')
        if (!chartDom) return

        let chart = this.chartInstances.downloadTrendChart
        if (!chart) {
          chart = echarts.init(chartDom)
          this.chartInstances.downloadTrendChart = chart
        }

        // 处理趋势数据
        const dates = []
        const windowsData = []
        const macData = []
        const totalData = []

        // 按日期分组数据
        const dataByDate = {}
        trendData.forEach(item => {
          const date = item.downloadDate
          if (!dataByDate[date]) {
            dataByDate[date] = { windows: 0, mac: 0 }
          }
          if (item.platform === 'windows') {
            dataByDate[date].windows = item.downloadCount || 0
          } else if (item.platform === 'mac') {
            dataByDate[date].mac = item.downloadCount || 0
          }
        })

        // 转换为图表数据
        Object.keys(dataByDate).sort().forEach(date => {
          dates.push(date)
          const windows = dataByDate[date].windows
          const mac = dataByDate[date].mac
          windowsData.push(windows)
          macData.push(mac)
          totalData.push(windows + mac)
        })

        const option = {
          title: {
            text: '桌面应用下载趋势',
            left: 'center',
            textStyle: {
              fontSize: 16,
              fontWeight: 'normal'
            }
          },
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'cross'
            },
            formatter: function(params) {
              let result = `<div style="font-weight: bold;">${params[0].axisValue}</div>`
              params.forEach(param => {
                result += `<div style="margin: 4px 0;">
                  <span style="display: inline-block; width: 10px; height: 10px; background: ${param.color}; margin-right: 8px;"></span>
                  ${param.seriesName}: ${param.value}次
                </div>`
              })
              return result
            }
          },
          legend: {
            data: ['Windows', 'Mac', '总计'],
            bottom: 10
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '15%',
            containLabel: true
          },
          xAxis: {
            type: 'category',
            boundaryGap: false,
            data: dates,
            axisLabel: {
              formatter: function(value) {
                return value.substring(5) // 只显示月-日
              }
            }
          },
          yAxis: {
            type: 'value',
            name: '下载次数',
            nameTextStyle: {
              color: '#666'
            }
          },
          series: [
            {
              name: 'Windows',
              type: 'line',
              data: windowsData,
              smooth: true,
              symbol: 'circle',
              symbolSize: 6,
              lineStyle: {
                width: 3
              },
              itemStyle: {
                color: '#1890ff'
              },
              areaStyle: {
                color: {
                  type: 'linear',
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    { offset: 0, color: 'rgba(24, 144, 255, 0.3)' },
                    { offset: 1, color: 'rgba(24, 144, 255, 0.1)' }
                  ]
                }
              }
            },
            {
              name: 'Mac',
              type: 'line',
              data: macData,
              smooth: true,
              symbol: 'circle',
              symbolSize: 6,
              lineStyle: {
                width: 3
              },
              itemStyle: {
                color: '#52c41a'
              },
              areaStyle: {
                color: {
                  type: 'linear',
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    { offset: 0, color: 'rgba(82, 196, 26, 0.3)' },
                    { offset: 1, color: 'rgba(82, 196, 26, 0.1)' }
                  ]
                }
              }
            },
            {
              name: '总计',
              type: 'line',
              data: totalData,
              smooth: true,
              symbol: 'diamond',
              symbolSize: 8,
              lineStyle: {
                width: 2,
                type: 'dashed'
              },
              itemStyle: {
                color: '#722ed1'
              }
            }
          ]
        }

        chart.setOption(option)
      })
    },

    // 格式化时间
    formatTime(time) {
      return new Date(time).toLocaleTimeString()
    },

    // 时间范围变化
    async onTimeRangeChange() {
      try {
        // 重新获取对应时间范围的数据
        const response = await getDashboardData({ timeRange: this.chartTimeRange })
        if (response.success && response.result.chartData && response.result.chartData.trendData) {
          this.chartData.trendData = response.result.chartData.trendData
          this.updateTrendChart()
        }
      } catch (error) {
        console.error('更新趋势图数据失败:', error)
      }
    },

    // 初始化图表
    initCharts() {
      this.$nextTick(() => {
        this.initTrendChart()
        this.initDistributionChart()
        this.initErrorStatsChart()
      })
    },

    // 初始化趋势图
    initTrendChart() {
      const chartDom = document.getElementById('apiTrendChart')
      if (!chartDom) return

      const chart = echarts.init(chartDom)
      this.chartInstances.trendChart = chart

      const trendData = this.chartData.trendData
      if (!trendData) return

      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross'
          }
        },
        legend: {
          data: ['API调用次数', '成功次数', '失败次数']
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: trendData.timeLabels || []
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            name: 'API调用次数',
            type: 'line',
            smooth: true,
            data: trendData.callCounts || []
          },
          {
            name: '成功次数',
            type: 'line',
            smooth: true,
            data: trendData.successCounts || []
          },
          {
            name: '失败次数',
            type: 'line',
            smooth: true,
            data: trendData.errorCounts || []
          }
        ]
      }

      chart.setOption(option)
    },

    // 初始化分布图
    initDistributionChart() {
      const chartDom = document.getElementById('apiDistributionChart')
      if (!chartDom) return

      const chart = echarts.init(chartDom)
      this.chartInstances.distributionChart = chart

      const distributionData = this.chartData.distributionData
      if (!distributionData) return

      const option = {
        tooltip: {
          trigger: 'item'
        },
        legend: {
          orient: 'vertical',
          left: 'left'
        },
        series: [
          {
            name: 'API使用分布',
            type: 'pie',
            radius: '50%',
            data: distributionData.data || [],
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }
        ]
      }

      chart.setOption(option)
    },

    // 初始化错误统计图
    initErrorStatsChart() {
      const chartDom = document.getElementById('errorStatsChart')
      if (!chartDom) return

      const chart = echarts.init(chartDom)
      this.chartInstances.errorStatsChart = chart

      const errorStatsData = this.chartData.errorStatsData
      if (!errorStatsData) return

      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        legend: {
          data: ['4xx错误', '5xx错误', '超时错误']
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: errorStatsData.categories || []
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            name: '4xx错误',
            type: 'bar',
            stack: 'total',
            data: errorStatsData.error4xx || []
          },
          {
            name: '5xx错误',
            type: 'bar',
            stack: 'total',
            data: errorStatsData.error5xx || []
          },
          {
            name: '超时错误',
            type: 'bar',
            stack: 'total',
            data: errorStatsData.timeoutErrors || []
          }
        ]
      }

      chart.setOption(option)
    },

    // 更新趋势图
    updateTrendChart() {
      if (this.chartInstances.trendChart && this.chartData.trendData) {
        const trendData = this.chartData.trendData
        const option = {
          xAxis: {
            data: trendData.timeLabels || []
          },
          series: [
            {
              data: trendData.callCounts || []
            },
            {
              data: trendData.successCounts || []
            },
            {
              data: trendData.errorCounts || []
            }
          ]
        }
        this.chartInstances.trendChart.setOption(option)
      }
    },

    // 开始实时监控
    startRealTimeMonitoring() {
      this.refreshTimer = setInterval(() => {
        this.refreshDashboardData()
      }, 60000) // 每60秒刷新一次数据，避免频繁请求
    },

    // 刷新仪表板数据
    async refreshDashboardData() {
      try {
        // 静默刷新，不显示loading
        const response = await getDashboardData()

        if (response.success) {
          const data = response.result

          // 只更新实时数据，不重新初始化图表
          if (data.realTimeStats) {
            this.realTimeStats = {
              onlineUsers: data.realTimeStats.onlineUsers || 0,
              todayActiveUsers: data.realTimeStats.todayActiveUsers || 0
            }
          }

          if (data.recentCalls) {
            this.recentCalls = data.recentCalls.map(call => ({
              id: call.id,
              time: new Date(call.time),
              apiType: call.apiType,
              success: call.success,
              userId: call.userId // 添加用户ID字段，用于管理员角色显示
            }))
          }

          // 更新图表数据
          if (data.chartData && data.chartData.trendData) {
            this.chartData.trendData = data.chartData.trendData
            this.updateTrendChart()
          }
        }

      } catch (error) {
        console.error('刷新仪表板数据失败:', error)
      }
    },

    // 加载仪表板数据
    async loadDashboardData() {
      try {
        this.loading = true

        // 调用仪表板数据API
        const response = await getDashboardData({ timeRange: this.chartTimeRange })

        if (response.success) {
          const data = response.result

          // 更新角色信息
          if (data.isAdmin !== undefined) {
            this.isAdmin = data.isAdmin
          }
          if (data.userRole) {
            this.userRole = data.userRole
          }

          // 更新用户信息
          if (data.userInfo) {
            this.userInfo = {
              nickname: data.userInfo.nickname || '智界用户',
              memberLevel: data.userInfo.memberLevel || 1,
              accountBalance: data.userInfo.accountBalance || 0,
              avatar: data.userInfo.avatar || '',
              totalConsumption: data.userInfo.totalConsumption || 0,
              totalRecharge: data.userInfo.totalRecharge || 0
            }
          }

          // 更新API统计
          if (data.apiStats) {
            this.apiStats = {
              todayCalls: data.apiStats.todayCalls || 0,
              todayGrowth: data.apiStats.todayGrowth || 0,
              monthCalls: data.apiStats.monthCalls || 0,
              monthGrowth: data.apiStats.monthGrowth || 0,
              successRate: data.apiStats.successRate || 99.0,
              successRateGrowth: data.apiStats.successRateGrowth || 0
            }
          }

          // 更新实时统计
          if (data.realTimeStats) {
            this.realTimeStats = {
              onlineUsers: data.realTimeStats.onlineUsers || 0,
              todayActiveUsers: data.realTimeStats.todayActiveUsers || 0
            }
          }

          // 更新最近调用记录
          if (data.recentCalls) {
            this.recentCalls = data.recentCalls.map(call => ({
              id: call.id,
              time: new Date(call.time),
              apiType: call.apiType,
              success: call.success,
              userId: call.userId // 添加用户ID字段，用于管理员角色显示
            }))
          }

          // 更新图表数据
          if (data.chartData) {
            this.chartData = data.chartData
          }

          // 初始化图表
          this.$nextTick(() => {
            this.initCharts()
          })

        } else {
          this.$message.error('加载仪表板数据失败：' + response.message)
        }

      } catch (error) {
        console.error('加载仪表板数据失败:', error)
        this.$message.error('加载仪表板数据失败，请稍后重试')
      } finally {
        this.loading = false
      }
    }
  }
}
</script>

<style lang="less" scoped>
.aigc-dashboard {
  padding: 24px;
  background: #f0f2f5;
  min-height: 100vh;

  .welcome-section {
    margin-bottom: 24px;

    .welcome-card {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border-radius: 12px;
      overflow: hidden;

      /deep/ .ant-card-body {
        padding: 32px;
      }

      .welcome-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
        color: white;

        .welcome-left {
          flex: 1;

          .welcome-title {
            color: white;
            font-size: 28px;
            font-weight: 600;
            margin-bottom: 8px;
            margin-top: 0;
          }

          .welcome-subtitle {
            color: rgba(255, 255, 255, 0.8);
            font-size: 16px;
            margin-bottom: 16px;
          }

          .user-stats {
            display: flex;
            align-items: center;
            gap: 16px;

            .user-level-tag {
              font-weight: 500;
            }

            .admin-tag {
              font-weight: 600;
              margin-left: 8px;
            }

            .balance-info {
              color: rgba(255, 255, 255, 0.9);
              font-size: 14px;

              .balance-amount {
                font-weight: 600;
                font-size: 18px;
                color: #52c41a;
              }
            }
          }
        }

        .welcome-right {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 24px;

          .avatar-section {
            text-align: center;

            .user-avatar {
              background: rgba(255, 255, 255, 0.2);
              color: white;
              font-size: 32px;
              font-weight: 600;
              margin-bottom: 8px;
            }

            .online-status {
              /deep/ .ant-badge-status-text {
                color: rgba(255, 255, 255, 0.8);
              }
            }
          }

          .quick-actions {
            .website-btn {
              background: linear-gradient(135deg, #10b981 0%, #059669 100%);
              border: none;
              border-radius: 12px;
              padding: 12px 24px;
              font-size: 16px;
              font-weight: 600;
              height: auto;
              box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
              transition: all 0.3s ease;

              &:hover {
                background: linear-gradient(135deg, #059669 0%, #047857 100%);
                transform: translateY(-2px);
                box-shadow: 0 6px 20px rgba(16, 185, 129, 0.4);
              }

              &:focus {
                background: linear-gradient(135deg, #059669 0%, #047857 100%);
                box-shadow: 0 6px 20px rgba(16, 185, 129, 0.4);
              }

              .anticon {
                margin-right: 8px;
                font-size: 16px;
              }
            }
          }
        }
      }
    }
  }

  .stats-section {
    margin-bottom: 24px;

    .stat-card {
      border-radius: 8px;
      transition: all 0.3s ease;
      margin-bottom: 16px;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
      }

      /deep/ .ant-card-body {
        padding: 24px;
      }

      .stat-trend {
        margin-top: 12px;
        display: flex;
        align-items: center;
        gap: 4px;

        .trend-text {
          color: #666;
          font-size: 12px;
        }
      }
    }
  }

  .charts-section {
    margin-bottom: 24px;

    .chart-card {
      border-radius: 8px;
      margin-bottom: 16px;

      /deep/ .ant-card-head {
        border-bottom: 1px solid #f0f0f0;
      }
    }

    .monitor-card {
      border-radius: 8px;

      .monitor-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 0;
        border-bottom: 1px solid #f0f0f0;

        &:last-child {
          border-bottom: none;
        }

        .monitor-label {
          font-weight: 500;
          color: #666;
        }

        .monitor-value {
          display: flex;
          flex-direction: column;
          align-items: flex-end;
          gap: 4px;

          .activity-value {
            font-weight: 600;
            font-size: 16px;
            color: #1890ff;
          }

          .activity-unit {
            font-size: 12px;
            color: #666;
            margin-left: 4px;
          }
        }
      }

      .recent-calls {
        margin-top: 16px;

        h4 {
          margin-bottom: 12px;
          color: #333;
          font-size: 14px;
        }

        .call-list {
          max-height: 200px;
          overflow-y: auto;

          .call-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 12px;
            margin-bottom: 4px;
            border-radius: 4px;
            font-size: 12px;
            transition: all 0.2s ease;

            &.call-success {
              background: #f6ffed;
              border-left: 3px solid #52c41a;
            }

            &.call-error {
              background: #fff2f0;
              border-left: 3px solid #ff4d4f;
            }

            .call-time {
              color: #666;
              font-size: 11px;
            }

            .call-api {
              font-weight: 500;
              color: #333;
            }

            .call-user {
              font-size: 10px;
              color: #999;
              margin-top: 2px;
            }

            .call-status {
              .anticon-check-circle {
                color: #52c41a;
              }

              .anticon-close-circle {
                color: #ff4d4f;
              }
            }
          }
        }
      }
    }
  }

  .detail-stats-section {
    .chart-card {
      border-radius: 8px;

      /deep/ .ant-card-head {
        border-bottom: 1px solid #f0f0f0;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .aigc-dashboard {
    padding: 16px;

    .welcome-section .welcome-card .welcome-content {
      flex-direction: column;
      text-align: center;
      gap: 16px;

      .welcome-left {
        .welcome-title {
          font-size: 24px;
        }

        .user-stats {
          justify-content: center;
        }
      }
    }

    .stats-section {
      .stat-card {
        margin-bottom: 12px;

        /deep/ .ant-card-body {
          padding: 16px;
        }
      }
    }
  }
}

// 深色主题适配
@media (prefers-color-scheme: dark) {
  .aigc-dashboard {
    background: #141414;

    .welcome-section .welcome-card {
      background: linear-gradient(135deg, #434343 0%, #000000 100%);
    }

    .stat-card,
    .chart-card,
    .monitor-card {
      /deep/ .ant-card {
        background: #1f1f1f;
        border-color: #303030;
      }
    }
  }

  // 桌面应用下载统计样式
  .download-stats-section {
    margin-top: 24px;
  }

  .download-stats-detail {
    .platform-stats {
      margin-bottom: 24px;

      h4 {
        margin-bottom: 12px;
        color: #666;
        font-size: 14px;
        font-weight: 500;
      }

      .platform-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 8px 0;
        border-bottom: 1px solid #f0f0f0;

        &:last-child {
          border-bottom: none;
        }

        .platform-count {
          font-weight: 600;
          color: #1890ff;
        }
      }
    }

    .success-rate-stats {
      margin-bottom: 24px;

      h4 {
        margin-bottom: 12px;
        color: #666;
        font-size: 14px;
        font-weight: 500;
      }

      .success-detail {
        margin-top: 8px;
        font-size: 12px;
        color: #999;
        text-align: center;
      }
    }

    .ip-stats {
      h4 {
        margin-bottom: 12px;
        color: #666;
        font-size: 14px;
        font-weight: 500;
      }

      .ip-count {
        font-size: 32px;
        font-weight: 600;
        color: #722ed1;
        text-align: center;
        margin-bottom: 8px;
      }

      .ip-detail {
        font-size: 12px;
        color: #999;
        text-align: center;
        margin: 0;
      }
    }
  }

  // 暗色主题适配
  .dark-theme {
    .download-stats-detail {
      .platform-stats {
        h4 {
          color: #ccc;
        }

        .platform-item {
          border-bottom-color: #303030;
        }
      }

      .success-rate-stats {
        h4 {
          color: #ccc;
        }

        .success-detail {
          color: #666;
        }
      }

      .ip-stats {
        h4 {
          color: #ccc;
        }

        .ip-detail {
          color: #666;
        }
      }
    }
  }

  // 🎨 下载记录表格状态标签样式优化
  .ant-table {
    .ant-tag {
      display: inline-flex;
      align-items: center;
      gap: 4px;
      font-weight: 500;

      // 成功状态
      &.ant-tag-green {
        background-color: #f6ffed;
        border-color: #b7eb8f;
        color: #389e0d;

        .anticon {
          color: #52c41a;
        }
      }

      // 失败状态
      &.ant-tag-red {
        background-color: #fff2f0;
        border-color: #ffccc7;
        color: #cf1322;

        .anticon {
          color: #ff4d4f;
        }
      }

      // 平台标签
      &.ant-tag-blue {
        background-color: #f0f5ff;
        border-color: #adc6ff;
        color: #1d39c4;
      }
    }
  }
}
</style>