import TOSBase from '../base';
interface AccessLogConfiguration {
    UseServiceTopic: boolean;
    TLSProjectID?: string;
    TLSTopicID?: string;
}
interface RealTimeLogConfiguration {
    Role: string;
    AccessLogConfiguration: AccessLogConfiguration;
}
export interface PutBucketRealTimeLogInput {
    bucket: string;
    realTimeLogConfiguration: RealTimeLogConfiguration;
}
export interface PutBucketRealTimeLogOutput {
}
export declare function putBucketRealTimeLog(this: TOSBase, input: PutBucketRealTimeLogInput): Promise<import("../base").TosResponse<PutBucketRealTimeLogOutput>>;
export interface GetBucketRealTimeLogInput {
    bucket: string;
}
export interface GetBucketRealTimeLogOutput {
    RealTimeLogConfiguration?: RealTimeLogConfiguration;
}
export declare function getBucketRealTimeLog(this: TOSBase, input: GetBucketRealTimeLogInput): Promise<import("../base").TosResponse<GetBucketRealTimeLogOutput>>;
export interface DeleteBucketRealTimeLogInput {
    bucket: string;
}
export interface DeleteBucketRealTimeLogOutput {
}
export declare function deleteBucketRealTimeLog(this: TOSBase, input: DeleteBucketRealTimeLogInput): Promise<import("../base").TosResponse<DeleteBucketRealTimeLogOutput>>;
export {};
