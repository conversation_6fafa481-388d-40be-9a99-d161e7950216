{"openapi": "3.0.0", "info": {"title": "剪映小助手_超级剪映小助手 - 批量添加图片", "description": "批量添加图片", "version": "1.0.0"}, "servers": [{"url": "https://www.aigcview.com"}], "paths": {"/jeecg-boot/api/jianyingpro/add_images": {"post": {"summary": "批量添加图片", "description": "批量添加图片", "operationId": "add_images_pro", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"access_key": {"type": "string", "description": "访问密钥（必填）", "example": "JianyingAPI_2025_SecureAccess_AigcView", "default": "JianyingAPI_2025_SecureAccess_AigcView"}, "draft_url": {"type": "string", "description": "草稿地址，使用create_draft输出的draft_url即可（必填）", "example": "https://aigcview-plub.tos-s-cn-guangzhou.volces.com/jianying-assistant/drafts/2025/01/06/draft_1736152800000_1024/draft_content.json"}, "imgs": {"type": "array", "items": {"type": "string"}, "description": "图片列表，格式[\"https://a.png\",\"https://a.png\"]（必填）", "example": ["https://example.com/img1.png", "https://example.com/img2.png"]}, "timelines": {"type": "array", "items": {"type": "object", "properties": {"start": {"type": "integer"}, "end": {"type": "integer"}}}, "description": "时间节点，只接收结构：[{\"start\":0,\"end\":4612}]，一般从audio_timelines节点的输出获取（必填）", "example": [{"start": 0, "end": 4612}]}, "out_animation_duration": {"type": "integer", "description": "对应剪映的出场动画时长", "example": 1000000}, "transition": {"type": "string", "description": "对应剪映的转场动画名字，比如：水墨", "example": "淡入淡出"}, "height": {"type": "integer", "description": "图片高度", "example": 1080}, "group_animation": {"type": "string", "description": "对应剪映的组合动画名字（可选），多个动画请用英文|分割，比如：旋转降落|缩放", "example": "动画1|动画2"}, "out_animation": {"type": "string", "description": "对应剪映的出场动画名字（可选），多个动画请用英文|分割，比如：旋转|脉冲", "example": "动画1|动画2"}, "width": {"type": "integer", "description": "图片宽度", "example": 1920}, "in_animation": {"type": "string", "description": "对应剪映的入场动画名字（可选），多个动画请用英文|分割，比如：渐显|放大", "example": "动画1|动画2"}, "in_animation_duration": {"type": "integer", "description": "对应剪映的入场动画时长", "example": 1000000}, "group_animation_duration": {"type": "integer", "description": "对应剪映的组合动画时长", "example": 2000000}, "transition_duration": {"type": "integer", "description": "对应剪映的转场动画时长", "example": 1500000}, "alpha": {"type": "number", "description": "图片透明度，范围0-1", "minimum": 0, "maximum": 1, "example": 0.5}, "scale_x": {"type": "number", "description": "X轴缩放比例", "example": 0.9}, "scale_y": {"type": "number", "description": "Y轴缩放比例", "example": 0.9}, "transform_x": {"type": "number", "description": "X轴移动位置", "example": 100}, "transform_y": {"type": "number", "description": "Y轴移动位置", "example": -100}}, "required": ["access_key", "draft_url", "imgs", "timelines"], "additionalProperties": false}}}}, "responses": {"200": {"description": "成功添加图片", "content": {"application/json": {"schema": {"type": "object", "properties": {"image_ids": {"type": "array", "description": "图片材料ID列表", "items": {"type": "string"}}, "draft_url": {"type": "string", "description": "更新后的草稿地址"}, "segment_ids": {"type": "array", "description": "图片段ID列表", "items": {"type": "string"}}, "track_id": {"type": "string", "description": "图片轨道ID"}, "segment_infos": {"type": "array", "description": "片段详细信息，包含时间和尺寸信息", "items": {"type": "object", "properties": {"segment_id": {"type": "string", "description": "片段ID"}, "start": {"type": "integer", "description": "开始时间（微秒）"}, "end": {"type": "integer", "description": "结束时间（微秒）"}, "duration": {"type": "integer", "description": "持续时间（微秒）"}, "width": {"type": "integer", "description": "图片宽度"}, "height": {"type": "integer", "description": "图片高度"}}}, "example": [{"segment_id": "segment1", "start": 0, "end": 5000000, "duration": 5000000, "width": 1024, "height": 1024}, {"segment_id": "segment2", "start": 5000000, "end": 10000000, "duration": 5000000, "width": 1920, "height": 1080}]}, "message": {"type": "string", "description": "导入指南信息", "example": "不知道导入的请看：https://www.aigcview.com/JianYingDraft?draft=https://example.com/draft.json"}}, "required": ["track_id", "draft_url", "image_ids", "segment_ids", "segment_infos", "message"]}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "description": "错误信息", "example": "参数不完整: 草稿地址不能为空"}, "error_code": {"type": "string", "description": "错误码", "example": "PARAM_INCOMPLETE_003"}, "error_message": {"type": "string", "description": "详细错误消息", "example": "参数不完整"}, "error_details": {"type": "string", "description": "错误解决方案", "example": "请提供有效的draft_url参数"}}, "required": ["error", "error_code"]}}}}, "500": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "description": "错误信息", "example": "批量添加图片失败: 服务器内部错误"}}, "required": ["error"]}}}}}}}}}