package org.jeecg.modules.demo.plubshop.mapper;

import java.util.List;
import java.math.BigDecimal;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.jeecg.modules.demo.plubshop.entity.AigcPlubShop;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: 插件商城
 * @Author: jeecg-boot
 * @Date:   2025-06-14
 * @Version: V1.0
 */
public interface AigcPlubShopMapper extends BaseMapper<AigcPlubShop> {

    /**
     * 根据插件名称查询插件信息
     * @param pluginName 插件名称
     * @return 插件信息
     */
    @Select("SELECT * FROM aigc_plub_shop WHERE plubname = #{pluginName}")
    AigcPlubShop getByPluginName(@Param("pluginName") String pluginName);

    /**
     * 根据插件唯一标识查询插件信息
     * @param pluginKey 插件唯一标识
     * @return 插件信息
     */
    @Select("SELECT * FROM aigc_plub_shop WHERE plugin_key = #{pluginKey}")
    AigcPlubShop getByPluginKey(@Param("pluginKey") String pluginKey);

    /**
     * 更新插件收益和调用次数
     * @param pluginId 插件ID
     * @param amount 收益金额
     * @return 更新结果
     */
    @Update("UPDATE aigc_plub_shop SET income = income + #{amount}, usernum = usernum + 1, update_time = NOW() WHERE id = #{pluginId}")
    int updatePluginStats(@Param("pluginId") String pluginId, @Param("amount") BigDecimal amount);

}
