{"openapi": "3.0.0", "info": {"title": "剪映小助手_智界数据箱 - 特效数据生成器", "description": "根据时间线关联特效", "version": "1.0.0"}, "servers": [{"url": "https://www.aigcview.com"}], "paths": {"/jeecg-boot/api/jianying/effect_infos": {"post": {"summary": "生成特效数据", "description": "根据时间线关联特效", "operationId": "effectInfos_zj", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"access_key": {"type": "string", "description": "访问密钥（必填）", "example": "JianyingAPI_2025_SecureAccess_AigcView", "default": "JianyingAPI_2025_SecureAccess_AigcView"}, "zj_effects": {"type": "array", "description": "特效名字（必填）", "items": {"type": "string"}, "example": ["金粉闪闪", "光影流转"]}, "zj_timelines": {"type": "array", "description": "时间节点，只接收结构：[{\"start\":0,\"end\":4612}]，一般从audio_timeline节点的输出获取（必填）", "items": {"type": "object", "properties": {"start": {"type": "integer"}, "end": {"type": "integer"}}}, "example": [{"start": 0, "end": 4612000}]}}, "required": ["access_key", "zj_effects", "zj_timelines"], "additionalProperties": false}}}}, "responses": {"200": {"description": "成功生成特效数据", "content": {"application/json": {"schema": {"type": "object", "properties": {"infos": {"type": "string", "description": "特效信息数组（JSON格式字符串），格式：[{\"effect_title\":\"火\",\"start\":0,\"end\":1176000},{\"start\":3456000,\"end\":7104000}]"}}, "required": ["infos"]}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "description": "操作是否成功", "example": false}, "error": {"type": "string", "description": "错误信息"}, "code": {"type": "string", "description": "错误代码"}}, "required": ["success", "error"]}}}}, "500": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "description": "操作是否成功", "example": false}, "error": {"type": "string", "description": "错误信息"}, "code": {"type": "string", "description": "错误代码"}}, "required": ["success", "error"]}}}}}}}}}