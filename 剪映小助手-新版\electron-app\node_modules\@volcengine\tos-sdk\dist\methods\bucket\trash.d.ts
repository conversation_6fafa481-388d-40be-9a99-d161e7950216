import TOSBase from '../base';
export interface PutBucketTrashInput {
    bucket?: string;
    Trash: {
        TrashPath: string;
        CleanInterval: number;
        Status: 'Enabled' | 'Disabled';
    };
}
export interface PutBucketTrashOutput {
}
export declare function putBucketTrash(this: TOSBase, input: PutBucketTrashInput): Promise<import("../base").TosResponse<PutBucketTrashOutput>>;
export interface GetBucketTrashInput {
    bucket?: string;
}
export interface GetBucketTrashOutput {
    Trash: {
        TrashPath: string;
        CleanInterval: number;
        Status: 'Enabled' | 'Disabled';
    };
}
export declare function getBucketTrash(this: TOSBase, input: GetBucketTrashInput): Promise<import("../base").TosResponse<GetBucketTrashOutput>>;
