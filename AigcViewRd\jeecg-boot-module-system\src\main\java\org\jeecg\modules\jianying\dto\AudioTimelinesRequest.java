package org.jeecg.modules.jianying.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 音频时间线生成器请求参数
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AudioTimelinesRequest extends BaseJianyingRequest {
    
    @ApiModelProperty(value = "音频链接数组（必填）", required = true,
                     example = "[\"https://example.com/audio1.mp3\", \"https://example.com/audio2.mp3\"]")
    @NotEmpty(message = "links不能为空")
    @JsonProperty("links")
    private List<String> zjLinks;
    
    @Override
    public String getSummary() {
        return "AudioTimelinesRequest{" +
               "accessKey=" + (getAccessKey() != null && getAccessKey().length() > 10 ? 
                              getAccessKey().substring(0, 10) + "***" : "null") +
               ", linksCount=" + (zjLinks != null ? zjLinks.size() : 0) +
               "}";
    }
}
