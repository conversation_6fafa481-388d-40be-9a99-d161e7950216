package org.jeecg.modules.api.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 豆包图生视频API服务
 * 
 * <AUTHOR>
 * @since 2025-01-16
 */
@Service
@Slf4j
public class DouBaoVideoApiService {

    @Value("${doubao.video.api.key:2979ec55-05c0-4952-bf0c-c8ac5816ef4c}")
    private String douBaoApiKey;

    @Value("${doubao.video.api.url:https://ark.cn-beijing.volces.com}")
    private String douBaoApiUrl;

    @Value("${doubao.video.api.version:v3}")
    private String apiVersion;

    /**
     * 创建视频生成任务
     */
    public Map<String, Object> createVideoTask(Map<String, Object> params) {
        try {
            log.info("开始调用豆包图生视频API创建任务，参数: {}", params);

            // 1. 构建请求URL
            String url = String.format("%s/api/%s/contents/generations/tasks", douBaoApiUrl, apiVersion);

            // 2. 构建请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.setBearerAuth(douBaoApiKey);

            // 3. 构建请求体
            Map<String, Object> requestBody = buildVideoTaskRequest(params);

            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);

            // 4. 调用豆包API
            RestTemplate restTemplate = new RestTemplate();
            ResponseEntity<String> response = restTemplate.postForEntity(url, entity, String.class);

            log.info("豆包API响应状态: {}, 响应体: {}", response.getStatusCode(), response.getBody());

            // 5. 解析响应
            if (response.getStatusCode() == HttpStatus.OK) {
                JSONObject responseJson = JSON.parseObject(response.getBody());
                
                // 检查是否有错误
                if (responseJson.containsKey("error")) {
                    JSONObject error = responseJson.getJSONObject("error");
                    String errorMessage = error.getString("message");
                    log.error("豆包API返回错误: {}", errorMessage);
                    throw new RuntimeException("豆包API错误: " + errorMessage);
                }

                // 提取任务信息
                String taskId = responseJson.getString("id");
                String status = responseJson.getString("status");

                // 🔥 修复：确保status有值，豆包API创建任务时状态通常为pending或running
                if (status == null || status.trim().isEmpty()) {
                    status = "pending";  // 默认状态
                    log.warn("豆包API响应中status为空，设置默认状态为pending");
                }

                Map<String, Object> result = new HashMap<>();
                result.put("task_id", taskId);
                result.put("status", status);
                result.put("message", "视频生成任务已创建");
                result.put("estimated_time", 120); // 预估120秒完成

                log.info("豆包视频任务创建成功: taskId={}, status={}, 完整响应: {}",
                        taskId, status, responseJson.toJSONString());
                return result;
            } else {
                throw new RuntimeException("豆包API调用失败，状态码: " + response.getStatusCode());
            }

        } catch (Exception e) {
            log.error("调用豆包图生视频API失败", e);
            throw new RuntimeException("调用豆包API失败: " + e.getMessage());
        }
    }

    /**
     * 查询视频生成任务状态
     */
    public Map<String, Object> queryTaskStatus(String taskId) {
        try {
            log.info("查询豆包视频任务状态: {}", taskId);

            // 1. 构建请求URL
            String url = String.format("%s/api/%s/contents/generations/tasks/%s", douBaoApiUrl, apiVersion, taskId);

            // 2. 构建请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setBearerAuth(douBaoApiKey);

            HttpEntity<String> entity = new HttpEntity<>(headers);

            // 3. 调用豆包API
            RestTemplate restTemplate = new RestTemplate();
            ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.GET, entity, String.class);

            log.info("豆包任务查询响应: {}", response.getBody());

            // 4. 解析响应
            if (response.getStatusCode() == HttpStatus.OK) {
                JSONObject responseJson = JSON.parseObject(response.getBody());
                log.debug("解析后的JSON对象: {}", responseJson.toJSONString());
                
                // 检查是否有错误
                if (responseJson.containsKey("error")) {
                    JSONObject error = responseJson.getJSONObject("error");
                    String errorMessage = error.getString("message");
                    log.error("豆包API查询错误: {}", errorMessage);
                    throw new RuntimeException("豆包API错误: " + errorMessage);
                }

                // 提取任务状态信息
                String status = responseJson.getString("status");
                String createdAt = responseJson.getString("created_at");
                String updatedAt = responseJson.getString("updated_at");
                
                Map<String, Object> result = new HashMap<>();
                result.put("task_id", taskId);
                result.put("status", status);
                result.put("created_at", createdAt);
                result.put("updated_at", updatedAt);

                // 🔥 修复：豆包API使用succeeded状态，视频URL在content字段
                if (("completed".equals(status) || "succeeded".equals(status))) {
                    // 检查content字段（豆包API的实际结构）
                    if (responseJson.containsKey("content")) {
                        JSONObject content = responseJson.getJSONObject("content");
                        if (content.containsKey("video_url")) {
                            result.put("video_url", content.getString("video_url"));
                            result.put("message", "视频生成完成");
                            // 🔥 统一状态为completed，便于前端处理
                            result.put("status", "completed");
                        } else {
                            log.warn("任务成功但未找到video_url，content内容: {}", content.toJSONString());
                            result.put("message", "视频生成完成，但未找到视频链接");
                        }
                    } else if (responseJson.containsKey("output")) {
                        // 兼容其他可能的响应格式
                        JSONObject output = responseJson.getJSONObject("output");
                        if (output.containsKey("video_url")) {
                            result.put("video_url", output.getString("video_url"));
                            result.put("message", "视频生成完成");
                            result.put("status", "completed");
                        }
                    } else {
                        log.warn("任务成功但未找到content或output字段，完整响应: {}", responseJson.toJSONString());
                        result.put("message", "视频生成完成，但未找到视频链接");
                    }
                } else if ("failed".equals(status)) {
                    String failureReason = responseJson.getString("failure_reason");
                    result.put("failure_reason", failureReason);
                    result.put("message", "视频生成失败: " + failureReason);
                } else if ("running".equals(status) || "processing".equals(status)) {
                    result.put("message", "视频正在生成中...");
                } else if ("pending".equals(status) || "queued".equals(status)) {
                    result.put("message", "任务等待中...");
                } else {
                    result.put("message", "任务状态: " + status);
                    log.info("未知任务状态: {}, 完整响应: {}", status, responseJson.toJSONString());
                }
                
                log.info("豆包视频任务状态查询成功: taskId={}, status={}", taskId, status);
                return result;
            } else {
                throw new RuntimeException("豆包API查询失败，状态码: " + response.getStatusCode());
            }

        } catch (Exception e) {
            log.error("查询豆包视频任务状态失败", e);
            throw new RuntimeException("查询任务状态失败: " + e.getMessage());
        }
    }

    /**
     * 构建视频任务请求体
     */
    private Map<String, Object> buildVideoTaskRequest(Map<String, Object> params) {
        Map<String, Object> request = new HashMap<>();

        // 基本参数
        request.put("model", params.getOrDefault("model", "doubao-seedance-1-0-lite-i2v-250428"));

        // 🔥 修复：content需要是数组格式
        List<Map<String, Object>> contentArray = new ArrayList<>();

        // 获取所有参数
        String prompt = (String) params.get("prompt");
        String resolution = (String) params.getOrDefault("resolution", "480p");
        Integer duration = (Integer) params.getOrDefault("duration", 5);
        Boolean camerafixed = (Boolean) params.getOrDefault("camerafixed", false);
        String imageUrl = (String) params.get("image_url");
        String endImageUrl = (String) params.get("end_image_url");

        // 构建完整的提示词（包含参数）
        StringBuilder fullPrompt = new StringBuilder(prompt);
        fullPrompt.append(" --rs ").append(resolution);  // 🔥 修复：使用--rs而不是--resolution
        fullPrompt.append(" --dur ").append(duration);
        fullPrompt.append(" --cf ").append(camerafixed);

        // 如果是图生视频，添加adaptive比例
        if (imageUrl != null && !imageUrl.trim().isEmpty()) {
            fullPrompt.append(" --ratio adaptive");
        }

        Map<String, Object> textContent = new HashMap<>();
        textContent.put("type", "text");
        textContent.put("text", fullPrompt.toString());
        contentArray.add(textContent);

        // 添加图片内容（如果是图生视频）
        if (imageUrl != null && !imageUrl.trim().isEmpty()) {
            Map<String, Object> imageContent = new HashMap<>();
            imageContent.put("type", "image_url");

            Map<String, Object> imageUrlObj = new HashMap<>();
            imageUrlObj.put("url", imageUrl);
            imageContent.put("image_url", imageUrlObj);

            contentArray.add(imageContent);
        }

        // 添加尾帧图片内容（如果提供）
        if (endImageUrl != null && !endImageUrl.trim().isEmpty()) {
            Map<String, Object> endImageContent = new HashMap<>();
            endImageContent.put("type", "image_url");
            endImageContent.put("role", "last_frame");

            Map<String, Object> endImageUrlObj = new HashMap<>();
            endImageUrlObj.put("url", endImageUrl);
            endImageContent.put("image_url", endImageUrlObj);

            contentArray.add(endImageContent);
        }

        request.put("content", contentArray);

        log.info("构建豆包API请求体: {}", JSON.toJSONString(request));
        return request;
    }

    /**
     * 取消视频生成任务
     */
    public Map<String, Object> cancelVideoTask(String taskId) {
        try {
            log.info("取消豆包视频任务: {}", taskId);

            // 1. 构建请求URL
            String url = String.format("%s/api/%s/contents/generations/tasks/%s", douBaoApiUrl, apiVersion, taskId);

            // 2. 构建请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setBearerAuth(douBaoApiKey);

            HttpEntity<String> entity = new HttpEntity<>(headers);

            // 3. 调用豆包API
            RestTemplate restTemplate = new RestTemplate();
            ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.DELETE, entity, String.class);

            log.info("豆包任务取消响应: {}", response.getBody());

            // 4. 解析响应
            if (response.getStatusCode() == HttpStatus.OK || response.getStatusCode() == HttpStatus.NO_CONTENT) {
                Map<String, Object> result = new HashMap<>();
                result.put("task_id", taskId);
                result.put("status", "cancelled");
                result.put("message", "任务已取消");
                
                log.info("豆包视频任务取消成功: {}", taskId);
                return result;
            } else {
                throw new RuntimeException("豆包API取消任务失败，状态码: " + response.getStatusCode());
            }

        } catch (Exception e) {
            log.error("取消豆包视频任务失败", e);
            throw new RuntimeException("取消任务失败: " + e.getMessage());
        }
    }
}
