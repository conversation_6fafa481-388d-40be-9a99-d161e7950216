package org.jeecg.modules.jianyingpro.controller;

import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.jianyingpro.dto.request.*;
import org.jeecg.modules.jianyingpro.service.JianyingProService;
// import org.jeecg.modules.jianying.aspect.JianyingAccessKey; // 暂时注释，避免依赖问题
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 超级剪映小助手Controller
 * 提供8个一体化操作接口，简化用户操作流程
 * 
 * <AUTHOR>
 * @since 2025-01-01
 */
@Slf4j
@RestController
@RequestMapping("/api/jianyingpro")
// @JianyingAccessKey // 暂时注释，避免依赖问题
@Api(tags = "超级剪映小助手")
public class JianyingProController {
    
    @Autowired
    private JianyingProService jianyingProService;

    /**
     * 统一错误响应处理
     */
    private ResponseEntity<Object> handleResponse(JSONObject result) {
        // 检查是否有错误字段（支持error和error_code两种格式）
        if (result.containsKey("error") || result.containsKey("error_code")) {
            // 根据错误类型返回不同的HTTP状态码
            if (result.containsKey("error_code")) {
                String errorCode = result.getString("error_code");
                if (errorCode.startsWith("PARAM_")) {
                    return ResponseEntity.status(400).body(result); // 参数错误返回400
                } else if (errorCode.startsWith("BUSINESS_")) {
                    return ResponseEntity.status(422).body(result); // 业务错误返回422
                } else {
                    return ResponseEntity.status(500).body(result); // 系统错误返回500
                }
            } else {
                return ResponseEntity.status(500).body(result); // 默认500错误
            }
        } else {
            return ResponseEntity.ok(result);
        }
    }
    
    /**
     * 一体化音频添加
     * 合并 audio_infos + add_audios 为单一操作
     */
    @ApiOperation(value = "一体化音频添加", notes = "自动生成音频数据并添加到草稿，支持智能参数识别")
    @PostMapping("/add_audios")
    public Object addAudios(@Valid @RequestBody JianyingProAddAudiosRequest request) {
        try {
            log.info("一体化音频添加请求: {}", request.getSummary());
            JSONObject result = jianyingProService.addAudios(request);

            // 使用统一错误处理
            return handleResponse(result);
        } catch (Exception e) {
            log.error("一体化音频添加失败", e);
            JSONObject errorResult = new JSONObject();
            errorResult.put("error", "一体化音频添加失败: " + e.getMessage());
            return ResponseEntity.status(500).body(errorResult);
        }
    }
    
    /**
     * 一体化视频添加
     * 合并 video_infos + add_videos 为单一操作
     */
    @ApiOperation(value = "一体化视频添加", notes = "自动生成视频数据并添加到草稿，支持智能参数识别")
    @PostMapping("/add_videos")
    public Object addVideos(@Valid @RequestBody JianyingProAddVideosRequest request) {
        try {
            log.info("一体化视频添加请求: {}", request.getSummary());
            JSONObject result = jianyingProService.addVideos(request);

            // 使用统一错误处理
            return handleResponse(result);
        } catch (Exception e) {
            log.error("一体化视频添加失败", e);
            JSONObject errorResult = new JSONObject();
            errorResult.put("error", "一体化视频添加失败: " + e.getMessage());
            return ResponseEntity.status(500).body(errorResult);
        }
    }
    
    /**
     * 智能时间线生成
     * 合并 timelines + audio_timelines 为智能接口
     */
    @ApiOperation(value = "智能时间线生成", notes = "根据参数自动选择时间线生成模式，支持音频时间线和自定义时间线")
    @PostMapping("/generate_timelines")
    public Object generateTimelines(@Valid @RequestBody JianyingProTimelinesRequest request) {
        try {
            log.info("智能时间线生成请求: {}", request.getSummary());
            JSONObject result = jianyingProService.generateTimelines(request);

            // 使用统一错误处理
            return handleResponse(result);
        } catch (Exception e) {
            log.error("智能时间线生成失败", e);
            JSONObject errorResult = new JSONObject();
            errorResult.put("error", "智能时间线生成失败: " + e.getMessage());
            return ResponseEntity.status(500).body(errorResult);
        }
    }
    
    /**
     * 智能数据转换
     * 支持同时输入多种数据类型，系统自动进行所有可能的转换
     */
    @ApiOperation(value = "智能数据转换", notes = "支持同时输入字符串、字符串列表、对象列表，系统自动进行所有可能的转换")
    @PostMapping("/data_conversion")
    public Object dataConversion(@Valid @RequestBody JianyingProDataConversionRequest request) {
        try {
            log.info("智能数据转换请求: {}", request.getSummary());
            log.info("请求详细内容: string_to_list={}, string_list_to_objects={}, objects_to_string_list={}",
                    request.getInputString(), request.getInputStringList(), request.getInputObjectList());
            JSONObject result = jianyingProService.dataConversion(request);
            log.info("智能数据转换响应: {}", result.toJSONString());

            // 使用统一错误处理
            return handleResponse(result);
        } catch (Exception e) {
            log.error("多模式数据转换失败", e);
            JSONObject errorResult = new JSONObject();
            errorResult.put("error", "多模式数据转换失败: " + e.getMessage());
            return ResponseEntity.status(500).body(errorResult);
        }
    }
    
    /**
     * 一体化图片添加
     * 合并 imgs_infos + add_images 为单一操作
     */
    @ApiOperation(value = "一体化图片添加", notes = "自动生成图片数据并添加到草稿，支持智能参数识别")
    @PostMapping("/add_images")
    public Object addImages(@Valid @RequestBody JianyingProAddImagesRequest request) {
        try {
            log.info("一体化图片添加请求: {}", request.getSummary());
            JSONObject result = jianyingProService.addImages(request);

            // 使用统一错误处理
            return handleResponse(result);
        } catch (Exception e) {
            log.error("一体化图片添加失败", e);
            JSONObject errorResult = new JSONObject();
            errorResult.put("error", "一体化图片添加失败: " + e.getMessage());
            return ResponseEntity.status(500).body(errorResult);
        }
    }

    /**
     * 一体化字幕添加
     * 合并 caption_infos + add_captions 为单一操作
     */
    @ApiOperation(value = "一体化字幕添加", notes = "自动生成字幕数据并添加到草稿，支持智能参数识别")
    @PostMapping("/add_captions")
    public Object addCaptions(@Valid @RequestBody JianyingProAddCaptionsRequest request) {
        try {
            log.info("一体化字幕添加请求: {}", request.getSummary());
            JSONObject result = jianyingProService.addCaptions(request);

            // 使用统一错误处理
            return handleResponse(result);
        } catch (Exception e) {
            log.error("一体化字幕添加失败", e);
            JSONObject errorResult = new JSONObject();
            errorResult.put("error", "一体化字幕添加失败: " + e.getMessage());
            return ResponseEntity.status(500).body(errorResult);
        }
    }

    /**
     * 一体化特效添加
     * 合并 effect_infos + add_effects 为单一操作
     */
    @ApiOperation(value = "一体化特效添加", notes = "自动生成特效数据并添加到草稿，支持智能参数识别")
    @PostMapping("/add_effects")
    public Object addEffects(@Valid @RequestBody JianyingProAddEffectsRequest request) {
        try {
            log.info("一体化特效添加请求: {}", request.getSummary());
            JSONObject result = jianyingProService.addEffects(request);

            // 使用统一错误处理
            return handleResponse(result);
        } catch (Exception e) {
            log.error("一体化特效添加失败", e);
            JSONObject errorResult = new JSONObject();
            errorResult.put("error", "一体化特效添加失败: " + e.getMessage());
            return ResponseEntity.status(500).body(errorResult);
        }
    }

    /**
     * 一体化关键帧添加
     * 合并 keyframes_infos + add_keyframes 为单一操作
     */
    @ApiOperation(value = "一体化关键帧添加", notes = "自动生成关键帧数据并添加到草稿，支持智能参数识别")
    @PostMapping("/add_keyframes")
    public Object addKeyframes(@Valid @RequestBody JianyingProAddKeyframesRequest request) {
        try {
            log.info("一体化关键帧添加请求: {}", request.getSummary());
            JSONObject result = jianyingProService.addKeyframes(request);

            // 使用统一错误处理
            return handleResponse(result);
        } catch (Exception e) {
            log.error("一体化关键帧添加失败", e);
            JSONObject errorResult = new JSONObject();
            errorResult.put("error", "一体化关键帧添加失败: " + e.getMessage());
            return ResponseEntity.status(500).body(errorResult);
        }
    }


}
