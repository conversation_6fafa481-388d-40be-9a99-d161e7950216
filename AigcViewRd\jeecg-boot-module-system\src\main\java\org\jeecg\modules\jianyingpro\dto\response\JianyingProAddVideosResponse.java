package org.jeecg.modules.jianyingpro.dto.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 一体化视频添加响应
 * 
 * <AUTHOR>
 * @since 2025-01-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class JianyingProAddVideosResponse extends BaseJianyingProResponse {
    
    @ApiModelProperty(value = "更新后的草稿地址")
    @JsonProperty("draft_url")
    private String draftUrl;
    
    @ApiModelProperty(value = "操作提示信息")
    @JsonProperty("message")
    private String message;
    
    @ApiModelProperty(value = "处理的视频数量")
    @JsonProperty("video_count")
    private Integer videoCount;
    
    @ApiModelProperty(value = "成功处理的视频数量")
    @JsonProperty("success_count")
    private Integer successCount;
    
    @ApiModelProperty(value = "失败的视频数量")
    @JsonProperty("failed_count")
    private Integer failedCount;
    
    @ApiModelProperty(value = "使用占位符的视频数量")
    @JsonProperty("placeholder_count")
    private Integer placeholderCount;
    
    @ApiModelProperty(value = "处理耗时（毫秒）")
    @JsonProperty("processing_time")
    private Long processingTime;
    
    @ApiModelProperty(value = "生成的轨道ID")
    @JsonProperty("track_id")
    private String trackId;
    
    @ApiModelProperty(value = "视频材料ID列表")
    @JsonProperty("video_material_ids")
    private java.util.List<String> videoMaterialIds;
    
    @ApiModelProperty(value = "视频段ID列表")
    @JsonProperty("video_segment_ids")
    private java.util.List<String> videoSegmentIds;
    
    @ApiModelProperty(value = "调试信息（开发环境）")
    @JsonProperty("debug_info")
    private String debugInfo;
}
