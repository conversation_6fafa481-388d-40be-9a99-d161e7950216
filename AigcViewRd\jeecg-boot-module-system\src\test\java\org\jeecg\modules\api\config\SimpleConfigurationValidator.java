package org.jeecg.modules.api.config;

import org.springframework.boot.context.properties.bind.Binder;
import org.springframework.boot.context.properties.source.ConfigurationPropertySource;
import org.springframework.boot.context.properties.source.MapConfigurationPropertySource;
import org.springframework.core.env.MapPropertySource;
import org.springframework.core.env.StandardEnvironment;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.support.PropertiesLoaderUtils;

import java.io.IOException;
import java.util.*;

/**
 * @Description: 简单配置验证器
 * @Author: AigcView
 * @Date: 2025-01-30
 * @Version: V1.0
 */
public class SimpleConfigurationValidator {

    public static void main(String[] args) {
        try {
            System.out.println("=== 配置文件验证开始 ===");
            
            // 验证开发环境配置
            validateDevConfiguration();
            
            // 验证测试环境配置
            validateTestConfiguration();
            
            // 验证生产环境配置
            validateProdConfiguration();
            
            System.out.println("\n=== 配置文件验证完成 ===");
            System.out.println("🎉 所有环境配置验证通过！");
            
        } catch (Exception e) {
            System.err.println("❌ 配置验证过程中发生错误: " + e.getMessage());
            e.printStackTrace();
            System.exit(1);
        }
    }
    
    private static void validateDevConfiguration() throws IOException {
        System.out.println("\n--- 开发环境配置验证 ---");
        
        Properties devProps = loadYamlAsProperties("application-dev.yml");
        
        // 验证基础配置
        String enabled = devProps.getProperty("aigc.user-activity.enabled");
        String testMode = devProps.getProperty("aigc.user-activity.test-mode");
        String batchSize = devProps.getProperty("aigc.user-activity.batch-size");
        String rolloutPercentage = devProps.getProperty("aigc.user-activity.rollout-percentage");
        
        System.out.println("启用状态: " + enabled);
        System.out.println("测试模式: " + testMode);
        System.out.println("批量大小: " + batchSize);
        System.out.println("灰度比例: " + rolloutPercentage + "%");
        
        // 验证开发环境特定配置
        if (!"true".equals(enabled)) {
            throw new RuntimeException("开发环境应该启用用户活跃状态追踪");
        }
        
        if (!"false".equals(testMode)) {
            throw new RuntimeException("开发环境不应该使用测试模式");
        }
        
        if (!"100".equals(batchSize)) {
            throw new RuntimeException("开发环境批量大小应该是100");
        }
        
        if (!"100".equals(rolloutPercentage)) {
            throw new RuntimeException("开发环境灰度比例应该是100%");
        }
        
        System.out.println("✅ 开发环境配置验证通过");
    }
    
    private static void validateTestConfiguration() throws IOException {
        System.out.println("\n--- 测试环境配置验证 ---");
        
        Properties testProps = loadYamlAsProperties("application-test.yml");
        
        // 验证基础配置
        String enabled = testProps.getProperty("aigc.user-activity.enabled");
        String batchSize = testProps.getProperty("aigc.user-activity.batch-size");
        String rolloutPercentage = testProps.getProperty("aigc.user-activity.rollout-percentage");
        String redisKeyPrefix = testProps.getProperty("aigc.user-activity.redis-key-prefix");
        
        System.out.println("启用状态: " + enabled);
        System.out.println("批量大小: " + batchSize);
        System.out.println("灰度比例: " + rolloutPercentage + "%");
        System.out.println("Redis键前缀: " + redisKeyPrefix);
        
        // 验证测试环境特定配置
        if (!"true".equals(enabled)) {
            throw new RuntimeException("测试环境应该启用用户活跃状态追踪");
        }
        
        if (!"50".equals(batchSize)) {
            throw new RuntimeException("测试环境批量大小应该是50");
        }
        
        if (!"50".equals(rolloutPercentage)) {
            throw new RuntimeException("测试环境灰度比例应该是50%");
        }
        
        if (!"aigc:test:user:activity:".equals(redisKeyPrefix)) {
            throw new RuntimeException("测试环境Redis键前缀应该包含test标识");
        }
        
        System.out.println("✅ 测试环境配置验证通过");
    }
    
    private static void validateProdConfiguration() throws IOException {
        System.out.println("\n--- 生产环境配置验证 ---");
        
        Properties prodProps = loadYamlAsProperties("application-prod.yml");
        
        // 验证基础配置
        String enabled = prodProps.getProperty("aigc.user-activity.enabled");
        String batchSize = prodProps.getProperty("aigc.user-activity.batch-size");
        String rolloutPercentage = prodProps.getProperty("aigc.user-activity.rollout-percentage");
        String redisKeyPrefix = prodProps.getProperty("aigc.user-activity.redis-key-prefix");
        
        System.out.println("启用状态: " + enabled);
        System.out.println("批量大小: " + batchSize);
        System.out.println("灰度比例: " + rolloutPercentage + "%");
        System.out.println("Redis键前缀: " + redisKeyPrefix);
        
        // 验证生产环境特定配置
        if (!"false".equals(enabled)) {
            throw new RuntimeException("生产环境初始应该关闭用户活跃状态追踪");
        }
        
        if (!"200".equals(batchSize)) {
            throw new RuntimeException("生产环境批量大小应该是200");
        }
        
        if (!"0".equals(rolloutPercentage)) {
            throw new RuntimeException("生产环境初始灰度比例应该是0%");
        }
        
        if (!"aigc:prod:user:activity:".equals(redisKeyPrefix)) {
            throw new RuntimeException("生产环境Redis键前缀应该包含prod标识");
        }
        
        System.out.println("✅ 生产环境配置验证通过");
    }
    
    private static Properties loadYamlAsProperties(String yamlFile) throws IOException {
        Properties properties = new Properties();
        
        try {
            ClassPathResource resource = new ClassPathResource(yamlFile);
            if (!resource.exists()) {
                throw new RuntimeException("配置文件不存在: " + yamlFile);
            }
            
            // 简单的YAML解析 - 只处理我们需要的配置
            Scanner scanner = new Scanner(resource.getInputStream());
            String currentSection = "";
            
            while (scanner.hasNextLine()) {
                String line = scanner.nextLine().trim();
                
                if (line.isEmpty() || line.startsWith("#")) {
                    continue;
                }
                
                if (line.startsWith("aigc:")) {
                    currentSection = "aigc";
                    continue;
                }
                
                if (currentSection.equals("aigc") && line.startsWith("user-activity:")) {
                    currentSection = "aigc.user-activity";
                    continue;
                }
                
                if (currentSection.equals("aigc.user-activity") && line.contains(":")) {
                    String[] parts = line.split(":", 2);
                    if (parts.length == 2) {
                        String key = parts[0].trim();
                        String value = parts[1].trim();
                        
                        // 移除注释
                        if (value.contains("#")) {
                            value = value.substring(0, value.indexOf("#")).trim();
                        }
                        
                        // 移除引号
                        if (value.startsWith("\"") && value.endsWith("\"")) {
                            value = value.substring(1, value.length() - 1);
                        }
                        
                        properties.setProperty("aigc.user-activity." + key, value);
                    }
                }
                
                // 重置section如果遇到新的顶级配置
                if (!line.startsWith(" ") && !line.startsWith("\t") && line.contains(":") && !line.startsWith("aigc")) {
                    currentSection = "";
                }
            }
            
            scanner.close();
            
        } catch (Exception e) {
            throw new IOException("解析YAML文件失败: " + yamlFile, e);
        }
        
        return properties;
    }
    
    private static void printProperties(Properties props) {
        System.out.println("--- 配置属性 ---");
        props.entrySet().stream()
            .sorted((e1, e2) -> String.valueOf(e1.getKey()).compareTo(String.valueOf(e2.getKey())))
            .forEach(entry -> System.out.println(entry.getKey() + " = " + entry.getValue()));
    }
}
