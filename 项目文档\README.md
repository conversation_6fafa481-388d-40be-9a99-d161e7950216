# 🌟 智界Aigc项目完整文档

## 📋 文档目录

- [业务架构文档](./业务架构文档.md) - 产品功能和商业模式说明
- [技术架构文档](./技术架构文档.md) - 技术实现和系统设计
- [开发规范文档](./开发规范文档.md) - 核心开发原则和代码规范
- [项目整体逻辑](./项目整体逻辑.md) - 完整的项目架构和模块关系

## 🎯 快速理解项目

### 项目定位
**智界Aigc** 是一个"AI插件生态系统 + 在线教育平台"，通过插件市场实现AI能力的商业化，通过教育内容降低使用门槛。

### 核心价值
- **插件创作者**：开发AI插件，获得收益分成
- **普通用户**：购买使用AI插件，学习相关教程
- **平台方**：提供生态平台，获得交易佣金

### 系统组成
1. **后端系统** - 提供API接口和数据管理
2. **管理前端** - 管理员使用的后台管理系统
3. **官网前端** - 用户使用的官方网站

### 技术栈
- **后端**：Spring Boot + MySQL + Redis
- **管理前端**：Vue.js + Ant Design Vue（JeecgBoot框架）
- **官网前端**：Vue.js + 组件化设计

## 🚀 快速开始

### 开发环境要求
- Java 8+
- Node.js 14+
- MySQL 8.0+
- Redis 6.0+

### 项目启动顺序
1. 启动后端服务（端口8080）
2. 启动管理前端（开发环境）
3. 启动官网前端（开发环境）

### 访问地址
- **官网**：https://www.aigcview.com
- **管理后台**：https://www.aigcview.com/admin
- **API文档**：https://www.aigcview.com/jeecg-boot/doc.html

## 📞 联系方式

如有疑问，请联系项目负责人或查看详细文档。
