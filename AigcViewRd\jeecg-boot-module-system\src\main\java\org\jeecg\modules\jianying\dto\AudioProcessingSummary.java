package org.jeecg.modules.jianying.dto;

import lombok.Data;

/**
 * 音频处理摘要类
 * 用于统计音频处理的整体结果信息
 * 
 * <AUTHOR>
 * @date 2025-07-08
 */
@Data
public class AudioProcessingSummary {
    
    /** 总文件数 */
    private int totalFiles;
    
    /** 成功处理的文件数 */
    private int successfulFiles;
    
    /** 失败的文件数 */
    private int failedFiles;
    
    /** 使用占位符的文件数 */
    private int placeholderFiles;
    
    /** 处理总耗时 */
    private String processingTime;
    
    /**
     * 默认构造函数
     */
    public AudioProcessingSummary() {
        this.totalFiles = 0;
        this.successfulFiles = 0;
        this.failedFiles = 0;
        this.placeholderFiles = 0;
        this.processingTime = "0s";
    }
    
    /**
     * 增加成功文件计数
     */
    public void incrementSuccessful() {
        this.successfulFiles++;
    }
    
    /**
     * 增加失败文件计数
     */
    public void incrementFailed() {
        this.failedFiles++;
    }
    
    /**
     * 增加占位符文件计数
     */
    public void incrementPlaceholder() {
        this.placeholderFiles++;
        this.failedFiles++; // 占位符也算失败
    }
    
    /**
     * 设置处理时间（毫秒转换为可读格式）
     */
    public void setProcessingTimeMs(long processingTimeMs) {
        if (processingTimeMs < 1000) {
            this.processingTime = processingTimeMs + "ms";
        } else {
            this.processingTime = String.format("%.1fs", processingTimeMs / 1000.0);
        }
    }
    
    /**
     * 获取成功率（百分比）
     */
    public double getSuccessRate() {
        if (totalFiles == 0) {
            return 0.0;
        }
        return (double) successfulFiles / totalFiles * 100;
    }
    
    /**
     * 获取失败率（百分比）
     */
    public double getFailureRate() {
        if (totalFiles == 0) {
            return 0.0;
        }
        return (double) failedFiles / totalFiles * 100;
    }
    
    /**
     * 判断是否有失败的文件
     */
    public boolean hasFailures() {
        return failedFiles > 0;
    }
    
    /**
     * 判断是否全部成功
     */
    public boolean isAllSuccessful() {
        return totalFiles > 0 && failedFiles == 0;
    }
}
