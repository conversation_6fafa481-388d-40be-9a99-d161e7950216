<template>
  <a-card :bordered="false">
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline" @keyup.enter.native="searchQuery">
        <a-row :gutter="24">
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="创作者名称">
              <a-input placeholder="请输入创作者名称" v-model="queryParam.authorname"></a-input>
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="插件数">
              <a-input placeholder="请输入插件数" v-model="queryParam.plubnum"></a-input>
            </a-form-item>
          </a-col>
          <template v-if="toggleSearchStatus">
            <a-col :xl="6" :lg="7" :md="8" :sm="24">
              <a-form-item label="插件使用总数">
                <a-input placeholder="请输入插件使用总数" v-model="queryParam.plubusenum"></a-input>
              </a-form-item>
            </a-col>
          </template>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <span style="float: left;overflow: hidden;" class="table-page-search-submitButtons">
              <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
              <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
              <a @click="handleToggleSearch" style="margin-left: 8px">
                {{ toggleSearchStatus ? '收起' : '展开' }}
                <a-icon :type="toggleSearchStatus ? 'up' : 'down'"/>
              </a>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <!-- 查询区域-END -->

    <!-- 操作按钮区域 -->
    <div class="table-operator">
      <!-- 新增按钮：admin用户始终显示，非admin用户只有在没有数据时才显示 -->
      <a-button
        v-if="isAdmin || !hasAuthorRecord"
        @click="handleAdd"
        type="primary"
        icon="plus">
        新增
      </a-button>

      <!-- admin用户才能看到的功能 -->
      <template v-if="isAdmin">
        <a-button type="primary" icon="download" @click="handleExportXls('插件创作者')">导出</a-button>
        <a-upload name="file" :showUploadList="false" :multiple="false" :headers="tokenHeader" :action="importExcelUrl" @change="handleImportExcel">
          <a-button type="primary" icon="import">导入</a-button>
        </a-upload>
        <a-button type="default" icon="sync" @click="handleUpdateAllPluginCounts" :loading="updateAllLoading">更新所有插件数</a-button>
        <!-- 高级查询区域 -->
        <j-super-query :fieldList="superFieldList" ref="superQueryModal" @handleSuperQuery="handleSuperQuery"></j-super-query>
        <a-dropdown v-if="selectedRowKeys.length > 0">
          <a-menu slot="overlay">
            <a-menu-item key="1" @click="batchDel"><a-icon type="delete"/>删除</a-menu-item>
          </a-menu>
          <a-button style="margin-left: 8px"> 批量操作 <a-icon type="down" /></a-button>
        </a-dropdown>
      </template>

      <!-- 非admin用户的提示信息 -->
      <div v-if="!isAdmin && hasAuthorRecord" style="color: #999; font-size: 12px; margin-top: 8px;">
        您已创建作者信息，如需修改请点击编辑
      </div>
    </div>

    <!-- table区域-begin -->
    <div>
      <div class="ant-alert ant-alert-info" style="margin-bottom: 16px;">
        <i class="anticon anticon-info-circle ant-alert-icon"></i> 已选择 <a style="font-weight: 600">{{ selectedRowKeys.length }}</a>项
        <a style="margin-left: 24px" @click="onClearSelected">清空</a>
      </div>

      <a-table
        ref="table"
        size="middle"
        :scroll="{x:true}"
        bordered
        rowKey="id"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="ipagination"
        :loading="loading"
        :rowSelection="{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}"
        class="j-table-force-nowrap"
        @change="handleTableChange">

        <template slot="htmlSlot" slot-scope="text">
          <div v-html="text"></div>
        </template>
        <template slot="imgSlot" slot-scope="text">
          <span v-if="!text" style="font-size: 12px;font-style: italic;">无图片</span>
          <img v-else :src="getImgView(text)" height="25px" alt="" style="max-width:80px;font-size: 12px;font-style: italic;"/>
        </template>
        <template slot="fileSlot" slot-scope="text">
          <span v-if="!text" style="font-size: 12px;font-style: italic;">无文件</span>
          <a-button
            v-else
            :ghost="true"
            type="primary"
            icon="download"
            size="small"
            @click="downloadFile(text)">
            下载
          </a-button>
        </template>

        <span slot="action" slot-scope="text, record">
          <a @click="handleEdit(record)">编辑</a>

          <!-- 只有admin用户才能看到更多操作 -->
          <template v-if="isAdmin">
            <a-divider type="vertical" />
            <a-dropdown>
              <a class="ant-dropdown-link">更多 <a-icon type="down" /></a>
              <a-menu slot="overlay">
                <a-menu-item>
                  <a @click="handleDetail(record)">详情</a>
                </a-menu-item>
                <a-menu-item>
                  <a @click="handleUpdatePluginCount(record.id)" :disabled="record.updateLoading">
                    <a-icon type="sync" :spin="record.updateLoading" />
                    更新插件数
                  </a>
                </a-menu-item>
                <a-menu-item>
                  <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
                    <a>删除</a>
                  </a-popconfirm>
                </a-menu-item>
              </a-menu>
            </a-dropdown>
          </template>
        </span>

      </a-table>
    </div>

    <aigc-plub-author-modal ref="modalForm" @ok="modalFormOk"></aigc-plub-author-modal>
  </a-card>
</template>

<script>

  import '@/assets/less/TableExpand.less'
  import { mixinDevice } from '@/utils/mixin'
  import { JeecgListMixin } from '@/mixins/JeecgListMixin'
  import AigcPlubAuthorModal from './modules/AigcPlubAuthorModal'
  import { filterObj } from '@/utils/util'

  export default {
    name: 'AigcPlubAuthorList',
    mixins:[JeecgListMixin, mixinDevice],
    components: {
      AigcPlubAuthorModal
    },
    data () {
      return {
        description: '插件创作者管理页面',
        updateAllLoading: false, // 批量更新插件数的loading状态
        // 表头
        columns: [
          {
            title: '#',
            dataIndex: '',
            key:'rowIndex',
            width:60,
            align:"center",
            customRender:function (t,r,index) {
              return parseInt(index)+1;
            }
          },
          {
            title:'创作者名称',
            align:"center",
            dataIndex: 'authorname'
          },
          {
            title:'作者职位',
            align:"center",
            dataIndex: 'title_dictText'
          },
          {
            title:'专业领域',
            align:"center",
            dataIndex: 'expertise_dictText',
            customRender: function (text) {
              if (!text) return '-';
              // 如果是多个领域，用标签显示
              const expertiseList = text.split(',');
              if (expertiseList.length > 2) {
                return expertiseList.slice(0, 2).join(', ') + '...';
              }
              return text;
            }
          },
          {
            title:'插件数',
            align:"center",
            dataIndex: 'plubnum'
          },
          {
            title:'插件使用总数',
            align:"center",
            dataIndex: 'plubusenum'
          },
          {
            title:'累计收益',
            align:"center",
            dataIndex: 'totalIncome',
            customRender: function (text) {
              return text ? '¥' + parseFloat(text).toFixed(2) : '¥0.00';
            }
          },
          {
            title:'创作者简介',
            align:"center",
            dataIndex: 'createinfo'
          },
          {
            title: '操作',
            dataIndex: 'action',
            align:"center",
            fixed:"right",
            width:147,
            scopedSlots: { customRender: 'action' }
          }
        ],
        url: {
          list: "/plubauthor/aigcPlubAuthor/list",
          delete: "/plubauthor/aigcPlubAuthor/delete",
          deleteBatch: "/plubauthor/aigcPlubAuthor/deleteBatch",
          exportXlsUrl: "/plubauthor/aigcPlubAuthor/exportXls",
          importExcelUrl: "plubauthor/aigcPlubAuthor/importExcel",
          
        },
        dictOptions:{},
        superFieldList:[],
      }
    },
    created() {
    this.getSuperFieldList();
    },
    computed: {
      importExcelUrl: function(){
        return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`;
      },
      isAdmin() {
        // 判断当前用户是否为admin角色（基于role_code）
        const userRole = localStorage.getItem('userRole');
        return userRole && userRole.toLowerCase().includes('admin');
      },
      // 非admin用户是否已有作者记录
      hasAuthorRecord() {
        return this.dataSource && this.dataSource.length > 0;
      }
    },
    methods: {
      // 重写getQueryParams方法，添加权限控制
      getQueryParams() {
        let param = Object.assign({}, this.queryParam, this.isorter, this.filters);

        // 非admin用户只能看到自己创建的数据
        if (!this.isAdmin) {
          param.create_by = this.$store.getters.userInfo.username;
        }

        param.field = this.getQueryField();
        param.pageNo = this.ipagination.current;
        param.pageSize = this.ipagination.pageSize;
        return filterObj(param);
      },
      initDictConfig(){
      },
      getSuperFieldList(){
        let fieldList=[];
        fieldList.push({type:'string',value:'authorname',text:'创作者名称',dictCode:''})
        fieldList.push({type:'int',value:'plubnum',text:'插件数',dictCode:''})
        fieldList.push({type:'int',value:'plubusenum',text:'插件使用总数',dictCode:''})
        fieldList.push({type:'decimal',value:'totalIncome',text:'累计收益',dictCode:''})
        fieldList.push({type:'string',value:'createinfo',text:'创作者简介',dictCode:''})
        this.superFieldList = fieldList
      },
      // 更新单个作者的插件数
      handleUpdatePluginCount(authorId) {
        const that = this;
        // 设置单个记录的loading状态
        const record = this.dataSource.find(item => item.id === authorId);
        if (record) {
          this.$set(record, 'updateLoading', true);
        }

        this.$http.post('/plubauthor/aigcPlubAuthor/updatePluginCount', null, {
          params: { id: authorId }
        }).then((res) => {
          if (res.success) {
            that.$message.success('更新插件数成功！');
            // 刷新列表
            that.loadData();
          } else {
            that.$message.error(res.message || '更新插件数失败！');
          }
        }).catch((error) => {
          console.error('更新插件数异常:', error);
          that.$message.error('更新插件数异常！');
        }).finally(() => {
          // 清除loading状态
          if (record) {
            this.$set(record, 'updateLoading', false);
          }
        });
      },
      // 批量更新所有作者的插件数
      handleUpdateAllPluginCounts() {
        const that = this;
        this.updateAllLoading = true;

        this.$http.post('/plubauthor/aigcPlubAuthor/updateAllPluginCounts').then((res) => {
          if (res.success) {
            that.$message.success(res.message || '批量更新插件数成功！');
            // 刷新列表
            that.loadData();
          } else {
            that.$message.error(res.message || '批量更新插件数失败！');
          }
        }).catch((error) => {
          console.error('批量更新插件数异常:', error);
          that.$message.error('批量更新插件数异常！');
        }).finally(() => {
          this.updateAllLoading = false;
        });
      }
    }
  }
</script>
<style scoped>
  @import '~@assets/less/common.less';
</style>