# 智界Aigc 用户扩展功能实现说明

## 📋 功能概述

本次实现为智界Aigc项目添加了完整的用户扩展功能，包括：

- **个人中心**：头像、昵称、账户余额、API密钥管理
- **交易系统**：消费记录、充值记录、余额变动明细
- **兑换码系统**：生成、使用、管理兑换码
- **会员体系**：会员等级、权益管理

## 🗄️ 数据库设计

### 1. 用户扩展信息表 (aicg_user_profile)
```sql
-- 存储用户的扩展信息，如昵称、余额、API密钥等
CREATE TABLE `aicg_user_profile` (
  `id` varchar(32) NOT NULL COMMENT '主键id',
  `user_id` varchar(32) NOT NULL COMMENT '用户ID',
  `nickname` varchar(100) COMMENT '昵称',
  `account_balance` decimal(10,2) DEFAULT 0.00 COMMENT '账户余额',
  `api_key` varchar(255) COMMENT 'API密钥',
  `total_consumption` decimal(10,2) DEFAULT 0.00 COMMENT '累计消费',
  `total_recharge` decimal(10,2) DEFAULT 0.00 COMMENT '累计充值',
  `member_level` tinyint(2) DEFAULT 1 COMMENT '会员等级',
  -- 其他字段...
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_id` (`user_id`)
);
```

### 2. 交易记录表 (aicg_user_transaction)
```sql
-- 记录所有用户的交易行为
CREATE TABLE `aicg_user_transaction` (
  `id` varchar(32) NOT NULL COMMENT '主键id',
  `user_id` varchar(32) NOT NULL COMMENT '用户ID',
  `transaction_type` tinyint(2) NOT NULL COMMENT '交易类型：1-消费，2-充值，3-退款，4-兑换',
  `amount` decimal(10,2) NOT NULL COMMENT '交易金额',
  `balance_before` decimal(10,2) NOT NULL COMMENT '交易前余额',
  `balance_after` decimal(10,2) NOT NULL COMMENT '交易后余额',
  `description` varchar(500) COMMENT '交易描述',
  `transaction_time` datetime NOT NULL COMMENT '交易时间',
  -- 其他字段...
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_transaction_time` (`transaction_time`)
);
```

### 3. 兑换码表 (aicg_exchange_code)
```sql
-- 管理兑换码的生成、使用和状态
CREATE TABLE `aicg_exchange_code` (
  `id` varchar(32) NOT NULL COMMENT '主键id',
  `code` varchar(50) NOT NULL COMMENT '兑换码',
  `code_type` tinyint(2) NOT NULL COMMENT '兑换码类型：1-余额，2-会员，3-积分',
  `value` decimal(10,2) NOT NULL COMMENT '兑换价值',
  `status` tinyint(2) DEFAULT 1 COMMENT '状态：1-未使用，2-已使用，3-已过期',
  `expire_time` datetime COMMENT '过期时间',
  `used_by` varchar(32) COMMENT '使用者用户ID',
  `used_time` datetime COMMENT '使用时间',
  -- 其他字段...
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_code` (`code`)
);
```

## 🏗️ 后端架构

### 实体类 (Entity)
- `AicgUserProfile` - 用户扩展信息实体
- `AicgUserRecord` - 交易记录实体（重构后）
- `AicgExchangeCode` - 兑换码实体

### 数据访问层 (Mapper)
- `AicgUserProfileMapper` - 用户扩展信息数据访问
- `AicgUserRecordMapper` - 交易记录数据访问
- `AicgExchangeCodeMapper` - 兑换码数据访问

### 业务逻辑层 (Service)
- `IAicgUserProfileService` - 用户扩展信息业务接口
- `IAicgUserRecordService` - 交易记录业务接口
- `IAicgExchangeCodeService` - 兑换码业务接口

### 控制器层 (Controller)
- `AicgUserProfileController` - 用户扩展信息API
- `AicgExchangeCodeController` - 兑换码管理API

## 🎨 前端实现

### 个人中心页面 (UserCenter.vue)
- 用户基本信息展示和编辑
- 账户余额显示和充值功能
- API密钥管理（显示/隐藏/重新生成）
- 兑换码使用功能
- 交易记录查询和展示

### API接口封装 (userCenter.js)
- 用户扩展信息相关接口
- 兑换码相关接口
- 交易记录相关接口

## 🔧 核心功能实现

### 1. 用户余额管理
```java
// 充值功能
public boolean recharge(String userId, BigDecimal amount, String description, String operatorId) {
    // 1. 验证参数
    // 2. 更新用户余额
    // 3. 记录交易
    // 4. 事务管理
}

// 消费功能
public boolean consume(String userId, BigDecimal amount, String description, String operatorId) {
    // 1. 检查余额
    // 2. 扣减余额
    // 3. 记录交易
    // 4. 事务管理
}
```

### 2. 兑换码系统
```java
// 生成兑换码
public List<AicgExchangeCode> generateExchangeCodes(Integer codeType, BigDecimal value, Date expireTime, String batchNo, Integer count, String operatorId) {
    // 1. 生成唯一兑换码
    // 2. 批量保存
    // 3. 返回结果
}

// 使用兑换码
public String useExchangeCode(String code, String userId, String operatorId) {
    // 1. 验证兑换码
    // 2. 更新状态
    // 3. 执行兑换逻辑
    // 4. 事务管理
}
```

### 3. API密钥管理
```java
// 重新生成API密钥
public String regenerateApiKey(String userId, String operatorId) {
    String newApiKey = generateApiKey();
    int result = baseMapper.updateApiKey(userId, newApiKey, operatorId);
    return result > 0 ? newApiKey : null;
}

private String generateApiKey() {
    return "ak_" + UUID.randomUUID().toString().replace("-", "");
}
```

## 📊 数据字典配置

系统自动添加了以下数据字典：

1. **交易类型 (transaction_type)**
   - 1: 消费
   - 2: 充值
   - 3: 退款
   - 4: 兑换

2. **兑换码类型 (exchange_code_type)**
   - 1: 余额
   - 2: 会员
   - 3: 积分

3. **兑换码状态 (exchange_code_status)**
   - 1: 未使用
   - 2: 已使用
   - 3: 已过期

## 🚀 部署说明

### 1. 数据库更新
执行增量SQL脚本：
```bash
mysql -u username -p database_name < db/增量SQL/智界Aigc用户扩展功能.sql
```

### 2. 后端部署
- 确保所有新增的Java类已编译
- 重启后端服务

### 3. 前端部署
- 将新增的Vue组件和API文件部署到前端项目
- 更新路由配置
- 重新构建前端项目

## 🔒 安全考虑

1. **API密钥安全**
   - 前端显示时进行脱敏处理
   - 重新生成需要用户确认

2. **余额操作安全**
   - 所有金额操作都有事务保护
   - 余额扣减前检查余额充足性
   - 记录详细的操作日志

3. **兑换码安全**
   - 兑换码具有唯一性
   - 支持过期时间控制
   - 防止重复使用

## 📈 性能优化

1. **数据库索引**
   - 用户ID索引
   - 交易时间索引
   - 兑换码索引

2. **查询优化**
   - 分页查询
   - 条件查询优化
   - 避免N+1查询

3. **缓存策略**
   - 用户余额缓存
   - API密钥缓存
   - 会员信息缓存

## 🧪 测试建议

1. **单元测试**
   - Service层业务逻辑测试
   - Mapper层数据访问测试

2. **集成测试**
   - API接口测试
   - 事务回滚测试

3. **压力测试**
   - 并发充值测试
   - 并发兑换码使用测试

## 📝 后续扩展

1. **支付集成**
   - 接入第三方支付平台
   - 支持多种支付方式

2. **会员体系完善**
   - 会员权益详细定义
   - 会员升级规则

3. **积分系统**
   - 积分获取规则
   - 积分兑换商城

4. **统计报表**
   - 用户消费统计
   - 兑换码使用统计
   - 收入统计分析

## 🆘 常见问题

### Q: 如何初始化用户扩展信息？
A: 系统会在用户首次访问个人中心时自动初始化用户扩展信息。

### Q: 兑换码过期如何处理？
A: 系统提供了定时任务接口，可以定期更新过期兑换码的状态。

### Q: 如何保证余额操作的一致性？
A: 所有余额操作都在事务中进行，并且使用了数据库级别的余额检查。

---

**开发完成时间**: 2025-06-14  
**版本**: V1.0  
**开发者**: Augment Agent
