package org.jeecg.modules.demo.apiusage.service.impl;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.jeecg.modules.demo.apiusage.entity.AicgUserApiUsage;
import org.jeecg.modules.demo.apiusage.mapper.AicgUserApiUsageMapper;
import org.jeecg.modules.demo.apiusage.service.IAicgUserApiUsageService;
import org.jeecg.modules.demo.plubshop.entity.AigcPlubShop;
import org.jeecg.modules.demo.plubshop.service.IAigcPlubShopService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import lombok.extern.slf4j.Slf4j;

/**
 * @Description: 用户API使用记录表
 * @Author: jeecg-boot
 * @Date:   2025-06-18
 * @Version: V1.0
 */
@Service
@Slf4j
public class AicgUserApiUsageServiceImpl extends ServiceImpl<AicgUserApiUsageMapper, AicgUserApiUsage> implements IAicgUserApiUsageService {

    @Autowired
    private IAigcPlubShopService plubShopService;

    @Override
    public List<AicgUserApiUsage> getByUserId(String userId) {
        return baseMapper.getByUserId(userId);
    }

    @Override
    public List<AicgUserApiUsage> getByApiKey(String apiKey) {
        return baseMapper.getByApiKey(apiKey);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AicgUserApiUsage recordUsage(String userId, String apiKey, String apiEndpoint, String apiMethod,
                                       String requestParams, Integer responseStatus, Integer responseTime,
                                       Integer tokensUsed, BigDecimal costAmount, String ipAddress,
                                       String userAgent, String errorMessage) {

        // 🔥 提取插件信息
        String pluginKey = extractPluginKey(requestParams);
        String pluginId = null;
        String pluginName = null;

        if (pluginKey != null && !pluginKey.isEmpty()) {
            try {
                AigcPlubShop plugin = plubShopService.getByPluginKey(pluginKey);
                if (plugin != null) {
                    pluginId = plugin.getId();
                    pluginName = plugin.getPlubname();
                }
            } catch (Exception e) {
                // 查询插件信息失败不影响主流程
                log.warn("查询插件信息失败 - pluginKey: {}, 错误: {}", pluginKey, e.getMessage());
            }
        }

        AicgUserApiUsage usage = new AicgUserApiUsage();
        usage.setUserId(userId);
        usage.setApiKey(apiKey);
        usage.setApiEndpoint(apiEndpoint);
        usage.setApiMethod(apiMethod);
        usage.setRequestParams(requestParams);
        usage.setResponseStatus(responseStatus);
        usage.setResponseTime(responseTime);
        usage.setTokensUsed(tokensUsed);
        usage.setCostAmount(costAmount);
        usage.setIpAddress(ipAddress);
        usage.setUserAgent(userAgent);
        usage.setErrorMessage(errorMessage);
        usage.setCallTime(new Date());
        usage.setCreateTime(new Date());

        // 🔥 补充插件信息
        usage.setPluginId(pluginId);
        usage.setPluginKey(pluginKey);
        usage.setPluginName(pluginName);

        this.save(usage);

        log.info("API使用记录保存成功 - 用户: {}, 接口: {}, 插件: {} ({})",
                userId, apiEndpoint, pluginName, pluginKey);

        return usage;
    }

    @Override
    public Map<String, Object> getUsageStats(String userId, String timeRange) {
        String[] timeRangeArray = getTimeRange(timeRange);
        return baseMapper.getUsageStats(userId, timeRangeArray[0], timeRangeArray[1]);
    }

    @Override
    public List<Map<String, Object>> getEndpointStats(String userId, String timeRange) {
        String[] timeRangeArray = getTimeRange(timeRange);
        return baseMapper.getEndpointStats(userId, timeRangeArray[0], timeRangeArray[1]);
    }

    @Override
    public List<AicgUserApiUsage> getTodayUsage(String userId) {
        return baseMapper.getTodayUsage(userId);
    }

    @Override
    public List<AicgUserApiUsage> getErrorUsage(String userId, Integer limit) {
        return baseMapper.getErrorUsage(userId, limit);
    }

    @Override
    public List<Map<String, Object>> getUsageTrend(String userId, String timeRange) {
        // 根据时间范围生成趋势数据
        List<Map<String, Object>> trendData = new ArrayList<>();
        
        // 这里应该根据实际需求实现趋势数据查询
        // 为了演示，返回模拟数据
        for (int i = 0; i < 7; i++) {
            Map<String, Object> data = new HashMap<>();
            data.put("date", LocalDateTime.now().minusDays(6-i).format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
            data.put("calls", (int)(Math.random() * 100) + 50);
            data.put("success", (int)(Math.random() * 90) + 45);
            data.put("errors", (int)(Math.random() * 10) + 1);
            trendData.add(data);
        }
        
        return trendData;
    }

    @Override
    public boolean checkRateLimit(String userId, String apiKey) {
        // 检查最近1分钟的调用次数
        QueryWrapper<AicgUserApiUsage> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId)
                   .eq("api_key", apiKey)
                   .ge("call_time", LocalDateTime.now().minusMinutes(1));
        
        long count = this.count(queryWrapper);
        
        // 假设限制为每分钟100次调用
        return count >= 100;
    }
    
    /**
     * 根据时间范围获取开始和结束时间
     */
    private String[] getTimeRange(String timeRange) {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime start;
        
        switch (timeRange.toLowerCase()) {
            case "today":
                start = now.toLocalDate().atStartOfDay();
                break;
            case "week":
                start = now.minusWeeks(1);
                break;
            case "month":
                start = now.minusMonths(1);
                break;
            case "year":
                start = now.minusYears(1);
                break;
            default:
                start = now.minusDays(7);
        }
        
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        return new String[]{start.format(formatter), now.format(formatter)};
    }

    /**
     * 从请求参数中提取插件Key
     */
    private String extractPluginKey(String requestParams) {
        if (requestParams == null || requestParams.isEmpty()) {
            return null;
        }

        // 🔧 修复：支持多种格式的插件Key提取
        String pluginKey = null;

        // 1. 尝试从JSON格式提取：{"pluginKey":"value"}
        String jsonPattern = "\"pluginKey\"\\s*:\\s*\"([^\"]+)\"";
        java.util.regex.Pattern pattern = java.util.regex.Pattern.compile(jsonPattern);
        java.util.regex.Matcher matcher = pattern.matcher(requestParams);
        if (matcher.find()) {
            pluginKey = matcher.group(1);
        }

        // 2. 如果JSON格式失败，尝试简单格式：pluginKey: value
        if (pluginKey == null) {
            String simplePattern = "pluginKey:\\s*([a-zA-Z0-9_-]+)";
            pattern = java.util.regex.Pattern.compile(simplePattern);
            matcher = pattern.matcher(requestParams);
            if (matcher.find()) {
                pluginKey = matcher.group(1);
            }
        }

        // 3. 验证插件Key格式（只包含字母、数字、下划线、连字符）
        if (pluginKey != null && pluginKey.matches("^[a-zA-Z0-9_-]+$")) {
            log.info("成功提取插件Key: {} from requestParams: {}", pluginKey, requestParams);
            return pluginKey;
        }

        log.warn("无法提取插件Key from requestParams: {}", requestParams);
        return null;
    }

    @Override
    public Map<String, Object> getPluginUsageStats() {
        Map<String, Object> stats = new HashMap<>();

        try {
            // 🔥 基础统计数据
            QueryWrapper<AicgUserApiUsage> allWrapper = new QueryWrapper<>();
            allWrapper.isNotNull("plugin_key");

            long totalCalls = this.count(allWrapper);

            QueryWrapper<AicgUserApiUsage> successWrapper = new QueryWrapper<>();
            successWrapper.isNotNull("plugin_key").eq("response_status", 200);
            long successCalls = this.count(successWrapper);

            long errorCalls = totalCalls - successCalls;
            double successRate = totalCalls > 0 ? (double) successCalls / totalCalls * 100 : 0;

            stats.put("totalCalls", totalCalls);
            stats.put("successCalls", successCalls);
            stats.put("errorCalls", errorCalls);
            stats.put("successRate", Math.round(successRate * 100.0) / 100.0);

            // 🔥 计算总Token和总金额
            QueryWrapper<AicgUserApiUsage> tokenWrapper = new QueryWrapper<>();
            tokenWrapper.isNotNull("plugin_key").isNotNull("tokens_used");
            List<AicgUserApiUsage> tokenRecords = this.list(tokenWrapper);

            long totalTokens = tokenRecords.stream()
                .mapToLong(record -> record.getTokensUsed() != null ? record.getTokensUsed() : 0)
                .sum();

            QueryWrapper<AicgUserApiUsage> costWrapper = new QueryWrapper<>();
            costWrapper.isNotNull("plugin_key").isNotNull("cost_amount");
            List<AicgUserApiUsage> costRecords = this.list(costWrapper);

            BigDecimal totalCost = costRecords.stream()
                .map(record -> record.getCostAmount() != null ? record.getCostAmount() : BigDecimal.ZERO)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

            stats.put("totalTokens", totalTokens);
            stats.put("totalCost", totalCost);

            // 🔥 计算平均响应时间
            QueryWrapper<AicgUserApiUsage> timeWrapper = new QueryWrapper<>();
            timeWrapper.isNotNull("plugin_key").isNotNull("response_time");
            List<AicgUserApiUsage> timeRecords = this.list(timeWrapper);

            double avgResponseTime = timeRecords.stream()
                .mapToInt(record -> record.getResponseTime() != null ? record.getResponseTime() : 0)
                .average()
                .orElse(0.0);

            stats.put("avgResponseTime", Math.round(avgResponseTime * 100.0) / 100.0);

            // 🔥 活跃用户数
            QueryWrapper<AicgUserApiUsage> userWrapper = new QueryWrapper<>();
            userWrapper.isNotNull("plugin_key").select("DISTINCT user_id");
            List<AicgUserApiUsage> userRecords = this.list(userWrapper);
            stats.put("activeUsers", userRecords.size());

            // 🔥 热门插件排行（前10）
            List<Map<String, Object>> hotPlugins = baseMapper.getHotPlugins(10);
            for (int i = 0; i < hotPlugins.size(); i++) {
                hotPlugins.get(i).put("rank", i + 1);
            }
            stats.put("hotPlugins", hotPlugins);

            // 🔥 活跃用户排行（前10）
            List<Map<String, Object>> activeUserList = baseMapper.getActiveUsers(10);
            for (int i = 0; i < activeUserList.size(); i++) {
                activeUserList.get(i).put("rank", i + 1);
            }
            stats.put("activeUserList", activeUserList);

        } catch (Exception e) {
            log.error("获取插件使用统计失败", e);
            // 返回默认值
            stats.put("totalCalls", 0);
            stats.put("successCalls", 0);
            stats.put("errorCalls", 0);
            stats.put("successRate", 0.0);
            stats.put("totalTokens", 0);
            stats.put("totalCost", BigDecimal.ZERO);
            stats.put("avgResponseTime", 0.0);
            stats.put("activeUsers", 0);
            stats.put("hotPlugins", new ArrayList<>());
            stats.put("activeUserList", new ArrayList<>());
        }

        return stats;
    }

    @Override
    public IPage<Map<String, Object>> queryPluginUsageWithUserInfo(Integer pageNo, Integer pageSize,
                                                                  AicgUserApiUsage aicgUserApiUsage,
                                                                  Boolean hasPluginInfo,
                                                                  String callTimeStart,
                                                                  String callTimeEnd,
                                                                  String userNickname,
                                                                  Map<String, String[]> parameterMap) {

        Page<Map<String, Object>> page = new Page<>(pageNo, pageSize);

        // 🔥 使用自定义SQL查询，关联用户表获取昵称
        IPage<Map<String, Object>> result = baseMapper.queryPluginUsageWithUserInfo(
            page, aicgUserApiUsage, hasPluginInfo, callTimeStart, callTimeEnd, userNickname, parameterMap
        );

        return result;
    }

    @Override
    public IPage<Map<String, Object>> queryApiUsageWithUserInfo(Integer pageNo, Integer pageSize,
                                                               AicgUserApiUsage aicgUserApiUsage,
                                                               String userNickname,
                                                               Map<String, String[]> parameterMap) {

        Page<Map<String, Object>> page = new Page<>(pageNo, pageSize);

        // 🔥 使用自定义SQL查询，关联用户表获取昵称（通用版本，不限制插件）
        IPage<Map<String, Object>> result = baseMapper.queryApiUsageWithUserInfo(
            page, aicgUserApiUsage, userNickname, parameterMap
        );

        return result;
    }
}
