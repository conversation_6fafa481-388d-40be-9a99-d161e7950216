package org.jeecg.modules.demo.versioncontrol.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 公开版本信息DTO
 * 用于对外API接口，只包含必要的版本信息，不包含敏感的内部管理字段
 * 
 * <AUTHOR>
 * @date 2025-01-09
 */
@Data
@ApiModel(value = "PublicVersionInfo", description = "公开版本信息")
public class PublicVersionInfo {

    @ApiModelProperty(value = "程序类型")
    private String programType;

    @ApiModelProperty(value = "版本号")
    private String versionNumber;

    @ApiModelProperty(value = "更新内容")
    private String updateContent;

    @ApiModelProperty(value = "下载链接")
    private String downloadUrl;

    @ApiModelProperty(value = "发布日期")
    private Date releaseDate;

    @ApiModelProperty(value = "是否最新版本：1-是，2-否")
    private Integer isLatest;

    /**
     * 从AigcVersionControl实体转换为公开版本信息
     * 
     * @param entity 版本控制实体
     * @return 公开版本信息
     */
    public static PublicVersionInfo fromEntity(org.jeecg.modules.demo.versioncontrol.entity.AigcVersionControl entity) {
        if (entity == null) {
            return null;
        }
        
        PublicVersionInfo dto = new PublicVersionInfo();
        dto.setProgramType(entity.getProgramType());
        dto.setVersionNumber(entity.getVersionNumber());
        dto.setUpdateContent(entity.getUpdateContent());
        dto.setDownloadUrl(entity.getDownloadUrl());
        dto.setReleaseDate(entity.getReleaseDate());
        dto.setIsLatest(entity.getIsLatest());
        
        return dto;
    }
}
