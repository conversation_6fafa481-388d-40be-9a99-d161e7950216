import TOSBase from '../base';
declare type Protocol = 'http' | 'https';
interface RedirectAllRequestsTo {
    HostName: string;
    Protocol?: Protocol;
}
interface IndexDocument {
    Suffix: string;
    ForbiddenSubDir?: boolean;
}
interface ErrorDocument {
    Key?: string;
}
interface RoutingRule {
    Condition: {
        HttpErrorCodeReturnedEquals?: number;
        KeyPrefixEquals?: string;
    };
    Redirect: {
        HostName?: string;
        HttpRedirectCode?: number;
        Protocol?: Protocol;
        ReplaceKeyPrefixWith?: string;
        ReplaceKeyWith?: string;
    };
}
export interface PutBucketWebsiteInput {
    bucket: string;
    redirectAllRequestsTo?: RedirectAllRequestsTo;
    indexDocument?: IndexDocument;
    errorDocument?: ErrorDocument;
    routingRules?: RoutingRule[];
}
export interface PutBucketWebsiteOutput {
}
export declare function putBucketWebsite(this: TOSBase, input: PutBucketWebsiteInput): Promise<import("../base").TosResponse<PutBucketWebsiteOutput>>;
export interface GetBucketWebsiteInput {
    bucket: string;
}
export interface GetBucketWebsiteOutput {
    RedirectAllRequestsTo?: RedirectAllRequestsTo;
    IndexDocument?: IndexDocument;
    ErrorDocument?: ErrorDocument;
    RoutingRules?: RoutingRule[];
}
export declare function getBucketWebsite(this: TOSBase, input: GetBucketWebsiteInput): Promise<import("../base").TosResponse<GetBucketWebsiteOutput>>;
export interface DeleteBucketWebsiteInput {
    bucket: string;
}
export interface DeleteBucketWebsiteOutput {
}
export declare function deleteBucketWebsite(this: TOSBase, input: DeleteBucketWebsiteInput): Promise<import("../base").TosResponse<DeleteBucketWebsiteOutput>>;
export {};
