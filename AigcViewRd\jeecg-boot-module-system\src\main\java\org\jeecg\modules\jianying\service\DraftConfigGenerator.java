package org.jeecg.modules.jianying.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.springframework.stereotype.Service;

/**
 * 草稿配置文件生成器 - 生成剪映草稿所需的各种配置文件
 * 
 * <AUTHOR>
 * @date 2025-01-04
 */
@Service
public class DraftConfigGenerator {

    /**
     * 生成草稿元数据信息 (draft_meta_info.json)
     * 
     * @param folderName 文件夹名称 (UUID)
     * @param draftId 草稿内部ID (UUID)
     * @param draftPath 草稿文件夹路径
     * @return draft_meta_info.json内容
     */
    public JSONObject generateDraftMetaInfo(String folderName, String draftId, String draftPath) {
        JSONObject metaInfo = new JSONObject();
        
        // 云同步配置
        metaInfo.put("cloud_draft_cover", true);
        metaInfo.put("cloud_draft_sync", true);
        metaInfo.put("cloud_package_completed_time", "");
        metaInfo.put("draft_cloud_capcut_purchase_info", "");
        metaInfo.put("draft_cloud_last_action_download", false);
        metaInfo.put("draft_cloud_package_type", "");
        metaInfo.put("draft_cloud_purchase_info", "");
        metaInfo.put("draft_cloud_template_id", "");
        metaInfo.put("draft_cloud_tutorial_info", "");
        metaInfo.put("draft_cloud_videocut_purchase_info", "");
        
        // 草稿基础信息
        metaInfo.put("draft_cover", "draft_cover.jpg");
        metaInfo.put("draft_deeplink_url", "");
        
        // 企业信息
        JSONObject enterpriseInfo = new JSONObject();
        enterpriseInfo.put("draft_enterprise_extra", "");
        enterpriseInfo.put("draft_enterprise_id", "");
        enterpriseInfo.put("draft_enterprise_name", "");
        enterpriseInfo.put("enterprise_material", new JSONArray());
        metaInfo.put("draft_enterprise_info", enterpriseInfo);
        
        // 路径和ID信息
        metaInfo.put("draft_fold_path", draftPath);
        metaInfo.put("draft_id", draftId);
        metaInfo.put("draft_is_ae_produce", false);
        metaInfo.put("draft_is_ai_packaging_used", false);
        metaInfo.put("draft_is_ai_shorts", false);
        metaInfo.put("draft_is_ai_translate", false);
        metaInfo.put("draft_is_article_video_draft", false);
        metaInfo.put("draft_is_cloud_temp_draft", false);
        metaInfo.put("draft_is_from_deeplink", "false");
        metaInfo.put("draft_is_invisible", false);
        
        // 素材信息
        JSONArray draftMaterials = new JSONArray();
        for (int i = 0; i <= 8; i++) {
            if (i == 4 || i == 5) continue; // 跳过4和5
            JSONObject material = new JSONObject();
            material.put("type", i);
            material.put("value", new JSONArray());
            draftMaterials.add(material);
        }
        metaInfo.put("draft_materials", draftMaterials);
        
        metaInfo.put("draft_materials_copied_info", new JSONArray());
        metaInfo.put("draft_name", folderName);
        metaInfo.put("draft_need_rename_folder", false);
        metaInfo.put("draft_new_version", "");
        metaInfo.put("draft_removable_storage_device", "");
        metaInfo.put("draft_root_path", "C:\\Users\\<USER>\\AppData\\Local\\JianyingPro\\User Data\\Projects\\com.lveditor.draft");
        metaInfo.put("draft_segment_extra_info", new JSONArray());
        metaInfo.put("draft_timeline_materials_size_", 2075);  // 匹配竞争对手的值
        metaInfo.put("draft_type", "");
        
        // 时间戳信息
        long currentTime = System.currentTimeMillis() * 1000; // 转换为微秒
        metaInfo.put("tm_draft_cloud_completed", "");
        metaInfo.put("tm_draft_cloud_entry_id", -1);
        metaInfo.put("tm_draft_cloud_modified", 0);
        metaInfo.put("tm_draft_cloud_parent_entry_id", -1);
        metaInfo.put("tm_draft_cloud_space_id", -1);
        metaInfo.put("tm_draft_cloud_user_id", -1);
        metaInfo.put("tm_draft_create", currentTime);
        metaInfo.put("tm_draft_modified", currentTime + 1000000); // 稍后的时间
        metaInfo.put("tm_draft_removed", 0);
        metaInfo.put("tm_duration", 0);
        
        return metaInfo;
    }

    /**
     * 生成代理配置 (draft_agency_config.json)
     * 
     * @return draft_agency_config.json内容
     */
    public JSONObject generateDraftAgencyConfig() {
        JSONObject agencyConfig = new JSONObject();
        agencyConfig.put("marterials", null); // 注意：这里保持原始的拼写错误
        agencyConfig.put("use_converter", false);
        agencyConfig.put("video_resolution", 720);
        return agencyConfig;
    }

    /**
     * 生成PC端附件配置 (attachment_pc_common.json)
     * 
     * @return attachment_pc_common.json内容
     */
    public JSONObject generateAttachmentPcCommon() {
        JSONObject attachmentConfig = new JSONObject();
        attachmentConfig.put("pc_feature_flag", 0);
        attachmentConfig.put("template_item_infos", new JSONArray());
        attachmentConfig.put("unlock_template_ids", new JSONArray());
        return attachmentConfig;
    }

    /**
     * 生成模板文件 (template.tmp)
     * 
     * @param templateId 模板ID
     * @return template.tmp内容
     */
    public JSONObject generateTemplate(String templateId) {
        JSONObject template = new JSONObject();
        
        // 基础配置 - 注意模板的画布配置是0x0
        JSONObject canvasConfig = new JSONObject();
        canvasConfig.put("height", 0);
        canvasConfig.put("ratio", "original");
        canvasConfig.put("width", 0);
        template.put("canvas_config", canvasConfig);
        
        template.put("color_space", -1);
        
        // 简化的配置对象
        JSONObject config = new JSONObject();
        config.put("adjust_max_index", 1);
        config.put("attachment_info", new JSONArray());
        config.put("combination_max_index", 1);
        config.put("export_range", null);
        config.put("extract_audio_last_index", 1);
        config.put("lyrics_recognition_id", "");
        config.put("lyrics_sync", true);
        config.put("lyrics_taskinfo", new JSONArray());
        config.put("maintrack_adsorb", true);
        config.put("material_save_mode", 0);
        config.put("original_sound_last_index", 1);
        config.put("record_audio_last_index", 1);
        config.put("sticker_max_index", 1);
        config.put("subtitle_recognition_id", "");
        config.put("subtitle_sync", true);
        config.put("subtitle_taskinfo", new JSONArray());
        config.put("video_mute", false);
        config.put("zoom_info_params", null);
        template.put("config", config);
        
        template.put("cover", null);
        template.put("create_time", 0);
        template.put("duration", 0);
        template.put("extra_info", null);
        template.put("fps", 30);
        template.put("free_render_index_mode_on", false);
        template.put("group_container", null);
        template.put("id", templateId);
        
        // 关键帧系统
        JSONObject keyframes = new JSONObject();
        keyframes.put("adjusts", new JSONArray());
        keyframes.put("audios", new JSONArray());
        keyframes.put("effects", new JSONArray());
        keyframes.put("filters", new JSONArray());
        keyframes.put("handwrites", new JSONArray());
        keyframes.put("stickers", new JSONArray());
        keyframes.put("texts", new JSONArray());
        keyframes.put("videos", new JSONArray());
        template.put("keyframes", keyframes);
        
        // 平台信息 - 使用较旧的版本
        JSONObject platform = new JSONObject();
        platform.put("app_id", 3704);
        platform.put("app_source", "cc");  // 修改为 "cc" 以匹配竞争对手
        platform.put("app_version", "3.9.0");
        platform.put("device_id", "ece93fefa6d73d0879f6cf6251e212fd");
        platform.put("hard_disk_id", "da04071d52c1f66c291bc2f5b1f0e87f");
        platform.put("mac_address", "864b94efa06c9456b10992695eba19c4");
        platform.put("os", "mac");
        platform.put("os_version", "12.5.1");

        template.put("last_modified_platform", platform);
        
        // 简化的素材系统
        JSONObject materials = new JSONObject();
        String[] materialTypes = {
            "audio_balances", "audio_effects", "audio_fades", "audios", "beats",
            "canvases", "chromas", "color_curves", "drafts", "effects",
            "handwrites", "hsl", "images", "log_color_wheels", "manual_deformations",
            "masks", "material_animations", "placeholders", "plugin_effects",
            "primary_color_wheels", "realtime_denoises", "sound_channel_mappings",
            "speeds", "stickers", "tail_leaders", "text_templates", "texts",
            "transitions", "video_effects", "video_trackings", "videos"
        };
        
        for (String type : materialTypes) {
            materials.put(type, new JSONArray());
        }
        template.put("materials", materials);
        
        template.put("mutable_config", null);
        template.put("name", "");
        template.put("new_version", "69.0.0"); // 模板使用较旧的版本
        template.put("platform", platform);
        template.put("relationships", new JSONArray());
        template.put("render_index_track_mode_on", false);
        template.put("retouch_cover", null);
        template.put("source", "default");
        template.put("static_cover_image_path", "");
        template.put("tracks", new JSONArray()); // 模板的tracks是空数组
        template.put("update_time", 0);
        template.put("version", 360000);
        
        return template;
    }
}
