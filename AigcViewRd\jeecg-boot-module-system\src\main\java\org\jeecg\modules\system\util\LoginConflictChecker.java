package org.jeecg.modules.system.util;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.util.IPUtils;
import org.jeecg.common.util.RedisUtil;
import org.jeecg.modules.system.entity.SysUser;
import org.jeecg.modules.system.vo.LoginConflictResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * @Description: 通用登录冲突检查器
 * @Author: jeecg-boot
 * @Date: 2025-06-21
 * @Version: V1.0
 */
@Slf4j
@Component
public class LoginConflictChecker {
    
    @Autowired
    private RedisUtil redisUtil;
    
    @Autowired
    private RoleChecker roleChecker;
    
    /**
     * 检查登录冲突（统一入口）
     * @param user 用户信息
     * @param request 请求对象
     * @param forceLogin 是否强制登录
     * @return 冲突检查结果
     */
    public LoginConflictResult checkLoginConflict(SysUser user, HttpServletRequest request, boolean forceLogin) {
        try {
            log.info("=== 开始登录冲突检查 ===");
            log.info("用户：{}，强制登录：{}", user.getUsername(), forceLogin);
            
            // 1. admin用户不检查冲突
            boolean isAdmin = roleChecker.checkUserIsAdmin(user.getId());
            if (isAdmin) {
                log.info("admin用户，跳过冲突检查，用户：{}", user.getUsername());
                return LoginConflictResult.noConflict();
            }
            
            // 2. 检查是否有其他登录
            String currentTokenKey = "current_user_token_" + user.getId();
            Object existingTokenObj = redisUtil.get(currentTokenKey);
            
            if (existingTokenObj == null) {
                log.info("用户 {} 首次登录，无冲突", user.getUsername());
                return LoginConflictResult.noConflict();
            }
            
            String existingToken = String.valueOf(existingTokenObj);
            log.info("发现用户 {} 已有登录Token：{}", user.getUsername(), existingToken.substring(0, Math.min(20, existingToken.length())) + "...");
            
            if (forceLogin) {
                log.info("用户 {} 选择强制登录，准备踢下线其他设备", user.getUsername());
                return LoginConflictResult.forceLogin(existingToken);
            }
            
            // 3. 有冲突且非强制登录，构建冲突信息
            JSONObject conflictInfo = buildConflictInfo(user.getId(), existingToken, request);
            log.info("用户 {} 存在登录冲突，返回冲突信息", user.getUsername());
            return LoginConflictResult.hasConflict(conflictInfo);
            
        } catch (Exception e) {
            log.error("登录冲突检查失败，用户：{}，错误：{}", user.getUsername(), e.getMessage(), e);
            // 出错时默认无冲突，保证登录流程正常
            return LoginConflictResult.noConflict();
        }
    }
    
    /**
     * 构建冲突信息
     * @param userId 用户ID
     * @param existingToken 已存在的Token
     * @param newRequest 新的登录请求
     * @return 冲突详情
     */
    private JSONObject buildConflictInfo(String userId, String existingToken, HttpServletRequest newRequest) {
        JSONObject conflictInfo = new JSONObject();
        
        try {
            // 获取已登录设备信息（从Token创建时间推算）
            String existingDeviceKey = "token_device_" + userId;
            Object existingDeviceObj = redisUtil.get(existingDeviceKey);
            
            if (existingDeviceObj != null) {
                JSONObject existingDevice = JSONObject.parseObject(String.valueOf(existingDeviceObj));
                conflictInfo.put("deviceInfo", existingDevice.getString("deviceInfo"));
                conflictInfo.put("ipAddress", existingDevice.getString("ipAddress"));
                conflictInfo.put("loginTime", existingDevice.getString("loginTime"));
            } else {
                // 如果没有设备信息，使用默认值
                conflictInfo.put("deviceInfo", "未知设备");
                conflictInfo.put("ipAddress", "未知IP");
                conflictInfo.put("loginTime", "未知时间");
            }
            
            // 新设备信息
            if (newRequest != null) {
                conflictInfo.put("newDeviceInfo", parseDeviceInfo(newRequest));
                conflictInfo.put("newIpAddress", IPUtils.getIpAddr(newRequest));
            }
            
            // 安全提示
            conflictInfo.put("message", "检测到您的账号已在其他设备登录，继续登录将自动下线其他设备");
            
        } catch (Exception e) {
            log.error("构建冲突信息失败", e);
            conflictInfo.put("deviceInfo", "未知设备");
            conflictInfo.put("ipAddress", "未知IP");
            conflictInfo.put("loginTime", "未知时间");
            conflictInfo.put("message", "检测到账号冲突");
        }
        
        return conflictInfo;
    }
    
    /**
     * 记录设备登录信息
     * @param userId 用户ID
     * @param request 请求对象
     */
    public void recordDeviceInfo(String userId, HttpServletRequest request) {
        try {
            if (request == null) {
                return;
            }
            
            String deviceKey = "token_device_" + userId;
            JSONObject deviceInfo = new JSONObject();
            deviceInfo.put("deviceInfo", parseDeviceInfo(request));
            deviceInfo.put("ipAddress", IPUtils.getIpAddr(request));
            deviceInfo.put("loginTime", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
            
            redisUtil.set(deviceKey, deviceInfo.toJSONString());
            redisUtil.expire(deviceKey, 24 * 60 * 60); // 24小时过期
            
        } catch (Exception e) {
            log.error("记录设备信息失败", e);
        }
    }
    
    /**
     * 解析设备信息
     * @param request 请求对象
     * @return 设备信息字符串
     */
    private String parseDeviceInfo(HttpServletRequest request) {
        if (request == null) {
            return "未知设备";
        }
        
        String userAgent = request.getHeader("User-Agent");
        String ip = IPUtils.getIpAddr(request);
        
        // 简单解析浏览器和操作系统
        String browser = "未知浏览器";
        String os = "未知系统";
        
        if (userAgent != null) {
            if (userAgent.contains("Chrome")) browser = "Chrome";
            else if (userAgent.contains("Firefox")) browser = "Firefox";
            else if (userAgent.contains("Safari")) browser = "Safari";
            else if (userAgent.contains("Edge")) browser = "Edge";
            
            if (userAgent.contains("Windows")) os = "Windows";
            else if (userAgent.contains("Mac")) os = "Mac";
            else if (userAgent.contains("Linux")) os = "Linux";
            else if (userAgent.contains("Android")) os = "Android";
            else if (userAgent.contains("iPhone")) os = "iPhone";
        }
        
        return String.format("%s浏览器 (%s)", browser, os);
    }
}
