# JeecgBoot官网和后台管理并存方案

## 📋 方案概述

本方案解决了JeecgBoot框架中官网展示和后台管理系统并存的路由架构问题，实现了：
- 官网和后台管理系统的路由完全分离
- 基于用户角色的智能路由跳转
- 动态路由和静态路由的协调工作
- 统一的用户角色管理机制

## 🚨 问题背景

### 原始问题
在JeecgBoot项目中同时部署官网和后台管理系统时，遇到以下关键问题：

1. **路由冲突问题**
   - 静态路由定义：`/` -> 官网首页
   - 动态路由生成：`/` -> 后台管理系统
   - 两者争夺同一路径，导致路由解析混乱

2. **角色存储不一致**
   - 后台管理登录：存储为 `localStorage.setItem('role', role)`
   - 官网登录：存储为 `localStorage.setItem('userRole', role)`
   - 角色工具函数：读取 `localStorage.getItem('userRole')`
   - 导致角色获取失败，权限判断错误

3. **跳转时序错误**
   - 登录成功后直接跳转到 `/dashboard/analysis`
   - 但动态路由尚未生成，路径不存在
   - 被通配符路由捕获，跳转到404页面

## 🎯 解决方案

### 1. 路由架构重构

#### 1.1 静态路由配置优化
**文件：`src/config/router.config.js`**

```javascript
export const constantRouterMap = [
  // 用户认证相关路由
  {
    path: '/user',
    component: UserLayout,
    // ...
  },

  // ✅ 官网页面使用简洁的一级路径
  {
    path: '/home',
    name: 'WebsiteHome',
    component: () => import('@/views/website/home/<USER>')
  },
  {
    path: '/market',
    name: 'WebsiteMarket', 
    component: () => import('@/views/website/market/Market.vue')
  },
  // ... 其他官网页面

  // ✅ 根路径处理 - 让 permission.js 处理复杂逻辑
  {
    path: '/',
    beforeEnter: (to, from, next) => {
      next()
    }
  },

  // 全局通配符路由
  {
    path: '*',
    redirect: '/not-found'
  }
]

// ✅ 移除所有静态后台路由定义，完全由动态路由处理
export const asyncRouterMap = []
```

#### 1.2 跳转路径配置
**文件：`src/store/mutation-types.js`**

```javascript
export const INDEX_MAIN_PAGE_PATH = '/dashboard/analysis'  // ✅ 匹配动态路由
export const WEBSITE_HOME_PATH = '/home'  // ✅ 官网首页简洁路径
```

### 2. 角色管理统一化

#### 2.1 角色工具函数
**文件：`src/utils/roleUtils.js`**

```javascript
import Vue from 'vue'
import { getAction } from '@/api/manage'

/**
 * 检查用户是否是管理员
 * @returns {Promise<boolean>}
 */
export async function isAdmin() {
  try {
    // 检查localStorage中的角色
    const storedRole = localStorage.getItem('userRole')  // ✅ 统一使用 userRole
    if (storedRole) {
      return checkAdminRole(storedRole)
    }
    
    // 如果本地没有，从服务器获取
    if (Vue.ls.get('ACCESS_TOKEN')) {
      const res = await getAction("/sys/user/getCurrentUserDeparts")
      if (res.success && res.result.role) {
        const role = res.result.role
        localStorage.setItem('userRole', role)  // ✅ 统一存储key
        return checkAdminRole(role)
      }
    }
    
    return false
  } catch (error) {
    console.error('检查管理员角色失败:', error)
    return false
  }
}

/**
 * 检查角色是否是管理员角色
 */
function checkAdminRole(role) {
  if (!role) return false
  const adminRoleCode = 'admin'  // ✅ 基于数据库真实数据
  const roles = role.toLowerCase().split(',').map(r => r.trim())
  return roles.includes(adminRoleCode)
}

// ... 其他角色相关函数
```

#### 2.2 登录页面角色存储统一
**后台管理登录页面：`src/views/user/Login.vue`**

```javascript
// 登录成功后的角色获取
let url = "/sys/user/getCurrentUserDeparts"
getAction(url).then(res => {
  if (res.success) {
    let role = res.result.role
    localStorage.setItem('userRole', role); // ✅ 统一使用 userRole
    let departId = res.result.departId
    localStorage.setItem('departId', departId);
  }
})
```

**官网登录页面：`src/views/website/auth/Login.vue`**

```javascript
// 获取用户角色信息
const roleRes = await getAction("/sys/user/getCurrentUserDeparts")
if (roleRes.success) {
  const userRole = roleRes.result.role
  localStorage.setItem('userRole', userRole || '')  // ✅ 统一使用 userRole
}
```

### 3. 路由守卫智能化

#### 3.1 路由守卫重构
**文件：`src/permission.js`**

```javascript
// ✅ 官网页面：使用简洁路径，所有用户都可以访问
const websitePages = ['/home', '/market', '/cases', '/tutorials', '/signin', 
                     '/membership', '/affiliate', '/usercenter', '/login', '/not-found']

router.beforeEach(async (to, from, next) => {
  if (Vue.ls.get(ACCESS_TOKEN)) {
    /* 已登录用户 */
    
    // 确保角色信息已加载
    const userRole = await getUserRole()
    const isAdminUser = await isAdmin()
    
    if (to.path === '/') {
      // 根路径访问，根据角色决定去向
      if (isAdminUser) {
        // 管理员用户，加载动态路由后进入后台
        await loadDynamicRoutesIfNeeded()
        next({ path: INDEX_MAIN_PAGE_PATH })
      } else {
        // 普通用户，重定向到官网首页
        next({ path: '/home' })
      }
    } else if (websitePages.indexOf(to.path) !== -1) {
      // 官网页面，所有用户都可以访问
      next()
    } else {
      // 后台页面，需要管理员角色
      if (isAdminUser) {
        await loadDynamicRoutesIfNeeded()
        next()
      } else {
        next({ path: '/home' })
      }
    }
  } else {
    /* 未登录用户 */
    if (to.path === '/') {
      next({ path: '/home' })  // 重定向到官网首页
    }
    // ... 其他处理逻辑
  }
})
```

### 4. 登录跳转策略优化

#### 4.1 后台管理登录优化
**文件：`src/views/user/Login.vue`**

```javascript
//登录成功
async loginSuccess () {
  // ✅ 等待角色信息获取完成后再跳转
  await new Promise(resolve => setTimeout(resolve, 500))
  
  // 验证角色是否已获取
  const role = localStorage.getItem('userRole')
  
  if (role === 'admin') {
    // ✅ 管理员跳转到根路径，让路由守卫处理动态路由加载
    this.$router.push({ path: "/" })
  } else {
    // 普通用户跳转到官网
    this.$router.push({ path: "/home" })
  }
}
```

## 🔧 实施步骤

### 步骤1：路由配置重构
1. 修改 `src/config/router.config.js`
   - 移除静态路由中的 `/admin` 定义
   - 官网页面改为使用简洁一级路径
   - 根路径交给路由守卫处理

2. 修改 `src/store/mutation-types.js`
   - 更新跳转路径配置

### 步骤2：角色管理统一
1. 创建 `src/utils/roleUtils.js` 角色工具函数
2. 修改所有登录页面的角色存储逻辑
3. 修改所有读取角色的地方

### 步骤3：路由守卫重构
1. 重写 `src/permission.js` 路由守卫逻辑
2. 添加调试信息便于问题排查
3. 实现基于角色的智能路由

### 步骤4：登录跳转优化
1. 修改后台管理登录成功后的跳转逻辑
2. 修改官网登录成功后的跳转逻辑
3. 添加角色获取等待机制

### 步骤5：退出登录优化
1. 修改 `src/store/modules/user.js`
2. 在退出时清除角色信息

## ✅ 验证方法

### 1. 未登录用户测试
- 访问 `www.domain.com` → 应该跳转到 `/home`（官网首页）
- 访问官网各个页面 → 应该正常显示

### 2. 普通用户测试  
- 登录成功后访问 `www.domain.com` → 应该跳转到 `/home`
- 尝试访问后台页面 → 应该被重定向到官网

### 3. 管理员用户测试
- 登录成功后访问 `www.domain.com` → 应该进入后台管理系统
- 后台功能正常使用
- 可以正常访问官网页面

## 🎯 方案优势

1. **路由职责清晰** - 官网和后台管理完全分离
2. **用户体验优化** - 根据用户角色提供最佳访问路径  
3. **架构扩展性强** - 便于后期功能扩展
4. **维护成本低** - 统一的角色管理机制
5. **安全性高** - 严格的权限控制

## 📝 注意事项

1. **数据库角色配置** - 确保管理员角色编码为 `admin`
2. **路由缓存清理** - 部署后清理浏览器缓存
3. **调试信息** - 生产环境可移除调试日志
4. **兼容性测试** - 测试各种用户角色和访问场景

## 🔍 问题排查过程详录

### 问题发现阶段
1. **现象**：后台管理登录成功后跳转到 `/not-found` 页面
2. **初步分析**：怀疑是路由配置问题
3. **调试方法**：在路由守卫中添加详细日志

### 关键发现
通过控制台调试发现了三个核心问题：

#### 发现1：路由冲突
```javascript
// 静态路由定义
{
  path: '/',
  component: BlankLayout,
  children: [{ path: '', component: WebsiteHome }]
}

// 动态路由生成
{
  path: '/',
  component: TabLayout,
  children: [...]
}
```
**问题**：同一路径被定义两次，Vue Router优先使用先注册的路由

#### 发现2：角色存储不一致
```javascript
// 后台登录存储
localStorage.setItem('role', role)

// 角色工具读取
localStorage.getItem('userRole')

// 官网登录存储
localStorage.setItem('userRole', userRole)
```
**问题**：key不匹配导致角色获取失败

#### 发现3：跳转时序错误
```javascript
// 错误的跳转方式
this.$router.push({ path: "/dashboard/analysis" })  // 路径不存在

// 正确的跳转方式
this.$router.push({ path: "/" })  // 让路由守卫处理
```
**问题**：直接跳转到不存在的动态路由路径

### 调试技巧
1. **路由守卫调试**：添加详细的console.log
2. **角色验证调试**：实时监控localStorage变化
3. **动态路由调试**：打印路由生成过程

## 🛠️ 技术实现细节

### 动态路由生成机制
```javascript
/**
 * 加载动态路由（如果尚未加载）
 */
async function loadDynamicRoutesIfNeeded() {
  if (store.getters.permissionList.length === 0) {
    // 1. 获取权限数据
    const res = await store.dispatch('GetPermissionList')
    const menuData = res.result.menu

    // 2. 生成动态路由
    const constRoutes = generateIndexRouter(menuData)

    // 3. 更新应用路由状态
    await store.dispatch('UpdateAppRouter', { constRoutes })

    // 4. 添加到路由器
    router.addRoutes(store.getters.addRouters)
  }
}
```

### 角色权限验证流程
```javascript
用户登录 → 获取TOKEN → 调用角色接口 → 存储角色信息 → 路由守卫验证 → 决定跳转目标
```

### 路由决策树
```
访问根路径 /
├── 未登录 → 跳转到 /home (官网首页)
├── 已登录
    ├── 管理员角色 → 加载动态路由 → 跳转到 /dashboard/analysis
    └── 普通用户 → 跳转到 /home (官网首页)
```

## 📊 性能优化

### 1. 路由懒加载
所有页面组件都使用动态导入：
```javascript
component: () => import('@/views/website/home/<USER>')
```

### 2. 角色缓存机制
- 首次获取后存储在localStorage
- 避免重复的网络请求
- 退出登录时清理缓存

### 3. 动态路由缓存
- 权限列表长度判断避免重复加载
- 路由状态管理优化

## 🔒 安全考虑

### 1. 权限验证双重保险
- 前端路由守卫验证
- 后端接口权限验证

### 2. 角色信息保护
- 敏感信息不存储在localStorage
- 定期清理过期数据

### 3. 路由访问控制
- 白名单机制
- 动态权限验证

## 🚀 部署注意事项

### 1. 环境配置
- 确保后端接口正常
- 数据库角色数据正确

### 2. 缓存清理
- 部署后清理浏览器缓存
- 清理CDN缓存

### 3. 监控验证
- 检查各种用户角色的访问情况
- 监控路由跳转是否正常

---

*本方案已在智界AIGC项目中成功实施，解决了官网和后台管理系统并存的所有路由冲突问题。下次遇到类似问题，可直接按照本方案操作。*
