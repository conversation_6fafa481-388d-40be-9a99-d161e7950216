<template>
  <div class="logo-image-container" :class="containerClass">
    <img 
      :src="logoSrc" 
      :alt="alt"
      :class="['logo-image', imageClass]"
      :style="imageStyle"
      @error="handleImageError"
      @load="handleImageLoad"
    />
    <!-- Fallback: 如果图片加载失败，显示原来的图标 -->
    <div v-if="showFallback" :class="['logo-fallback', fallbackClass]">
      <a-icon type="thunderbolt" />
      <span v-if="showText" class="fallback-text">{{ fallbackText }}</span>
    </div>
  </div>
</template>

<script>
export default {
  name: 'LogoImage',
  props: {
    // 尺寸预设：small(32px), medium(48px), large(80px), custom
    size: {
      type: String,
      default: 'medium',
      validator: value => ['small', 'medium', 'large', 'custom'].includes(value)
    },
    // 自定义尺寸（当size为custom时使用）
    customSize: {
      type: [String, Number],
      default: null
    },
    // 图片alt属性
    alt: {
      type: String,
      default: '智界AIGC'
    },
    // 是否显示fallback文字
    showText: {
      type: Boolean,
      default: false
    },
    // fallback文字内容
    fallbackText: {
      type: String,
      default: '智界AIGC'
    },
    // 额外的CSS类
    containerClass: {
      type: String,
      default: ''
    },
    imageClass: {
      type: String,
      default: ''
    },
    fallbackClass: {
      type: String,
      default: ''
    },
    // 是否启用悬停效果
    hover: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      showFallback: false,
      imageLoaded: false
    }
  },
  computed: {
    logoSrc() {
      // 🔥 优先使用CDN上的logo文件
      if (window.getFileAccessHttpUrl) {
        try {
          const cdnLogoUrl = window.getFileAccessHttpUrl('defaults/logo.png')
          console.debug('使用CDN logo:', cdnLogoUrl)
          return cdnLogoUrl
        } catch (error) {
          console.warn('CDN logo获取失败，使用本地fallback:', error)
        }
      }

      // 🔄 降级：使用本地logo文件
      try {
        return require('@/assets/logo/logo.png')
      } catch (error) {
        console.warn('本地Logo文件未找到，使用fallback')
        this.showFallback = true
        return ''
      }
    },
    imageStyle() {
      const sizeMap = {
        small: '32px',
        medium: '48px',
        large: '80px',
        custom: this.customSize ? (typeof this.customSize === 'number' ? `${this.customSize}px` : this.customSize) : '48px'
      }
      
      const size = sizeMap[this.size]
      return {
        width: size,
        height: size
      }
    }
  },
  methods: {
    handleImageError() {
      console.warn('Logo图片加载失败，显示fallback')
      this.showFallback = true
      this.imageLoaded = false
    },
    handleImageLoad() {
      this.imageLoaded = true
      this.showFallback = false
    }
  }
}
</script>

<style scoped>
.logo-image-container {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.logo-image {
  display: block;
  object-fit: contain;
  transition: all 0.3s ease;
}

.logo-image-container.hover .logo-image:hover {
  transform: scale(1.05);
  filter: brightness(1.1);
}

.logo-fallback {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #3b82f6;
  font-weight: 600;
}

.logo-fallback .anticon {
  font-size: inherit;
}

.fallback-text {
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .logo-image-container.responsive .logo-image {
    width: 32px !important;
    height: 32px !important;
  }
}

@media (max-width: 480px) {
  .logo-image-container.responsive .logo-image {
    width: 28px !important;
    height: 28px !important;
  }
}
</style>
