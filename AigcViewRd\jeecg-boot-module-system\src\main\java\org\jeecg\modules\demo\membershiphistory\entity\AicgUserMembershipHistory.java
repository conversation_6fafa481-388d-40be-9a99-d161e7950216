package org.jeecg.modules.demo.membershiphistory.entity;

import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 用户会员订阅历史表
 * @Author: jeecg-boot
 * @Date:   2025-06-18
 * @Version: V1.0
 */
@Data
@TableName("aicg_user_membership_history")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="aicg_user_membership_history对象", description="用户会员订阅历史表")
public class AicgUserMembershipHistory implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String id;
    
	/**用户ID*/
	@Excel(name = "用户ID", width = 15)
    @ApiModelProperty(value = "用户ID，关联sys_user.id")
    private String userId;
    
	/**关联订单ID*/
	@Excel(name = "关联订单ID", width = 15)
    @ApiModelProperty(value = "关联订单ID，关联ces_order_main.id")
    private String orderId;
    
	/**升级前角色ID*/
	@Excel(name = "升级前角色ID", width = 15)
    @ApiModelProperty(value = "升级前角色ID，关联sys_role.id")
    private String fromRoleId;
    
	/**升级后角色ID*/
	@Excel(name = "升级后角色ID", width = 15)
    @ApiModelProperty(value = "升级后角色ID，关联sys_role.id")
    private String toRoleId;
    
	/**会员等级*/
	@Excel(name = "会员等级", width = 15)
    @ApiModelProperty(value = "会员等级：1=普通,2=VIP,3=SVIP,4=至尊")
    private Integer memberLevel;
    
	/**订阅时长(月)*/
	@Excel(name = "订阅时长(月)", width = 15)
    @ApiModelProperty(value = "订阅时长(月)")
    private Integer durationMonths;
    
	/**订阅金额*/
	@Excel(name = "订阅金额", width = 15)
    @ApiModelProperty(value = "订阅金额")
    private BigDecimal amount;
    
	/**开始时间*/
	@Excel(name = "开始时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "开始时间")
    private Date startTime;
    
	/**结束时间*/
	@Excel(name = "结束时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "结束时间")
    private Date endTime;
    
	/**状态*/
	@Excel(name = "状态", width = 15)
    @ApiModelProperty(value = "状态：1=生效中,2=已过期,3=已取消")
    private Integer status;
    
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;
    
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private Date createTime;
    
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private String updateBy;
    
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private Date updateTime;
    
	/**所属部门*/
    @ApiModelProperty(value = "所属部门")
    private String sysOrgCode;
}
