-- =============================================
-- 小红书分享次数限制功能数据库脚本
-- 创建时间：2025-06-27
-- 版本：V1.0
-- 说明：为aicg_user_api_usage表添加分享次数限制相关字段
-- =============================================

-- 检查字段是否存在，如果不存在则添加
SET @sql = '';

-- 检查 share_attempts 字段
SELECT COUNT(*) INTO @col_exists 
FROM information_schema.columns 
WHERE table_schema = DATABASE() 
  AND table_name = 'aicg_user_api_usage' 
  AND column_name = 'share_attempts';

IF @col_exists = 0 THEN
    SET @sql = CONCAT(@sql, 'ALTER TABLE aicg_user_api_usage ADD COLUMN share_attempts INT DEFAULT 0 COMMENT "分享尝试次数";');
END IF;

-- 检查 max_share_attempts 字段
SELECT COUNT(*) INTO @col_exists 
FROM information_schema.columns 
WHERE table_schema = DATABASE() 
  AND table_name = 'aicg_user_api_usage' 
  AND column_name = 'max_share_attempts';

IF @col_exists = 0 THEN
    SET @sql = CONCAT(@sql, 'ALTER TABLE aicg_user_api_usage ADD COLUMN max_share_attempts INT DEFAULT 3 COMMENT "最大分享尝试次数";');
END IF;

-- 检查 last_attempt_time 字段
SELECT COUNT(*) INTO @col_exists 
FROM information_schema.columns 
WHERE table_schema = DATABASE() 
  AND table_name = 'aicg_user_api_usage' 
  AND column_name = 'last_attempt_time';

IF @col_exists = 0 THEN
    SET @sql = CONCAT(@sql, 'ALTER TABLE aicg_user_api_usage ADD COLUMN last_attempt_time DATETIME COMMENT "最后尝试时间";');
END IF;

-- 检查 share_status 字段
SELECT COUNT(*) INTO @col_exists 
FROM information_schema.columns 
WHERE table_schema = DATABASE() 
  AND table_name = 'aicg_user_api_usage' 
  AND column_name = 'share_status';

IF @col_exists = 0 THEN
    SET @sql = CONCAT(@sql, 'ALTER TABLE aicg_user_api_usage ADD COLUMN share_status TINYINT(1) DEFAULT 0 COMMENT "分享状态：0-未分享，1-已分享";');
END IF;

-- 检查 share_time 字段
SELECT COUNT(*) INTO @col_exists 
FROM information_schema.columns 
WHERE table_schema = DATABASE() 
  AND table_name = 'aicg_user_api_usage' 
  AND column_name = 'share_time';

IF @col_exists = 0 THEN
    SET @sql = CONCAT(@sql, 'ALTER TABLE aicg_user_api_usage ADD COLUMN share_time DATETIME COMMENT "分享时间";');
END IF;

-- 检查 share_platform 字段
SELECT COUNT(*) INTO @col_exists 
FROM information_schema.columns 
WHERE table_schema = DATABASE() 
  AND table_name = 'aicg_user_api_usage' 
  AND column_name = 'share_platform';

IF @col_exists = 0 THEN
    SET @sql = CONCAT(@sql, 'ALTER TABLE aicg_user_api_usage ADD COLUMN share_platform VARCHAR(50) COMMENT "分享平台";');
END IF;

-- 检查 page_id 字段
SELECT COUNT(*) INTO @col_exists 
FROM information_schema.columns 
WHERE table_schema = DATABASE() 
  AND table_name = 'aicg_user_api_usage' 
  AND column_name = 'page_id';

IF @col_exists = 0 THEN
    SET @sql = CONCAT(@sql, 'ALTER TABLE aicg_user_api_usage ADD COLUMN page_id VARCHAR(100) COMMENT "页面ID";');
END IF;

-- 执行SQL
IF LENGTH(@sql) > 0 THEN
    PREPARE stmt FROM @sql;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;
    SELECT '✅ 小红书分享次数限制字段添加完成' AS result;
ELSE
    SELECT '✅ 所有字段已存在，无需添加' AS result;
END IF;

-- 创建相关索引
CREATE INDEX IF NOT EXISTS idx_page_id_plugin ON aicg_user_api_usage(page_id, plugin_key);
CREATE INDEX IF NOT EXISTS idx_share_status ON aicg_user_api_usage(share_status);
CREATE INDEX IF NOT EXISTS idx_last_attempt_time ON aicg_user_api_usage(last_attempt_time);

-- 显示表结构确认
SELECT 
    COLUMN_NAME as '字段名',
    DATA_TYPE as '数据类型',
    IS_NULLABLE as '允许空值',
    COLUMN_DEFAULT as '默认值',
    COLUMN_COMMENT as '注释'
FROM information_schema.columns 
WHERE table_schema = DATABASE() 
  AND table_name = 'aicg_user_api_usage'
  AND COLUMN_NAME IN ('share_attempts', 'max_share_attempts', 'last_attempt_time', 'share_status', 'share_time', 'share_platform', 'page_id')
ORDER BY ORDINAL_POSITION;
