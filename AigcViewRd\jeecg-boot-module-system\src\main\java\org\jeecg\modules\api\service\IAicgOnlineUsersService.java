package org.jeecg.modules.api.service;

import org.jeecg.modules.api.entity.AicgOnlineUsers;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @Description: 在线用户统计服务接口
 * @Author: AigcView
 * @Date: 2025-01-30
 * @Version: V1.0
 */
public interface IAicgOnlineUsersService extends IService<AicgOnlineUsers> {

    /**
     * 获取在线用户统计
     * @return 统计数据
     */
    Map<String, Object> getOnlineUsersStats();

    /**
     * 更新用户最后活跃时间
     * @param userId 用户ID
     * @return 更新结果
     */
    boolean updateLastActiveTime(String userId);

    /**
     * 设置用户离线
     * @param userId 用户ID
     * @return 更新结果
     */
    boolean setUserOffline(String userId);

    /**
     * 清理离线用户
     * @return 清理的用户数量
     */
    int cleanOfflineUsers();

    /**
     * 获取今日活跃用户数
     * @return 今日活跃用户数
     */
    int getTodayActiveUsersCount();

    /**
     * 获取当前在线用户数
     * @return 当前在线用户数
     */
    int getCurrentOnlineUsersCount();

    /**
     * 记录用户登录状态
     * @param userId 用户ID
     * @param sessionId 会话ID
     * @param ipAddress IP地址
     * @param userAgent 用户代理
     * @return 是否成功
     */
    boolean recordUserLogin(String userId, String sessionId, String ipAddress, String userAgent);

    /**
     * 记录用户登出状态
     * @param userId 用户ID
     * @return 是否成功
     */
    boolean recordUserLogout(String userId);

    /**
     * 批量更新用户活跃时间
     * @param userIds 用户ID列表
     * @return 更新成功的数量
     */
    int batchUpdateActiveTime(List<String> userIds);

    /**
     * 获取用户在线状态
     * @param userId 用户ID
     * @return 在线状态信息
     */
    AicgOnlineUsers getUserOnlineStatus(String userId);

    /**
     * 获取在线用户列表
     * @param limit 限制数量
     * @return 在线用户列表
     */
    List<AicgOnlineUsers> getOnlineUsersList(int limit);

    /**
     * 获取用户活跃度统计
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 活跃度统计
     */
    Map<String, Object> getUserActivityStats(Date startDate, Date endDate);

    /**
     * 获取实时在线用户统计
     * @return 实时统计数据
     */
    Map<String, Object> getRealTimeOnlineStats();

    /**
     * 获取用户在线时长统计
     * @param userId 用户ID
     * @param date 日期
     * @return 在线时长（分钟）
     */
    long getUserOnlineDuration(String userId, Date date);

    /**
     * 获取热门时段统计
     * @param date 日期
     * @return 热门时段统计
     */
    Map<String, Object> getPopularTimeStats(Date date);

    /**
     * 检查用户是否在线
     * @param userId 用户ID
     * @return 是否在线
     */
    boolean isUserOnline(String userId);

    /**
     * 获取用户最后活跃时间
     * @param userId 用户ID
     * @return 最后活跃时间
     */
    Date getUserLastActiveTime(String userId);

    /**
     * 强制用户下线
     * @param userId 用户ID
     * @param reason 下线原因
     * @return 是否成功
     */
    boolean forceUserOffline(String userId, String reason);

    /**
     * 获取用户会话信息
     * @param userId 用户ID
     * @return 会话信息列表
     */
    List<AicgOnlineUsers> getUserSessions(String userId);

    /**
     * 清理指定时间之前的离线用户记录
     * @param beforeTime 指定时间
     * @return 清理的记录数
     */
    int cleanOfflineUsersBefore(Date beforeTime);

    /**
     * 获取用户地理位置统计
     * @return 地理位置统计
     */
    Map<String, Object> getUserLocationStats();

    /**
     * 获取用户设备统计
     * @return 设备统计
     */
    Map<String, Object> getUserDeviceStats();

    /**
     * 获取用户活跃度趋势
     * @param days 天数
     * @return 活跃度趋势数据
     */
    List<Map<String, Object>> getUserActivityTrend(int days);

    /**
     * 获取峰值在线用户数
     * @param date 日期
     * @return 峰值在线用户数
     */
    int getPeakOnlineUsers(Date date);

    /**
     * 获取用户留存率统计
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 留存率统计
     */
    Map<String, Object> getUserRetentionStats(Date startDate, Date endDate);

    /**
     * 获取新用户统计
     * @param date 日期
     * @return 新用户统计
     */
    Map<String, Object> getNewUserStats(Date date);

    /**
     * 获取用户活跃度分布
     * @return 活跃度分布统计
     */
    Map<String, Object> getUserActivityDistribution();

    /**
     * 检查并清理僵尸会话
     * @return 清理的会话数
     */
    int cleanZombieSessions();

    /**
     * 获取用户在线状态历史
     * @param userId 用户ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 在线状态历史
     */
    List<AicgOnlineUsers> getUserOnlineHistory(String userId, Date startDate, Date endDate);

    /**
     * 更新用户活跃状态（集成批量更新服务）
     * @param userId 用户ID
     * @param sessionId 会话ID
     * @param apiPath API路径
     * @param ipAddress IP地址
     * @param userAgent 用户代理
     * @return 是否成功
     */
    boolean updateUserActivity(String userId, String sessionId, String apiPath, String ipAddress, String userAgent);

    /**
     * 获取系统负载统计
     * @return 系统负载统计
     */
    Map<String, Object> getSystemLoadStats();
}
