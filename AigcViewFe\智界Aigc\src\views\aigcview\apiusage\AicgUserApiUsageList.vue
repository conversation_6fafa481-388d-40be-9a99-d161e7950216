<template>
  <a-card :bordered="false">
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline" @keyup.enter.native="searchQuery">
        <a-row :gutter="24">
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="用户ID">
              <a-input placeholder="请输入用户ID" v-model="queryParam.userId"></a-input>
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="用户昵称">
              <a-input placeholder="请输入用户昵称" v-model="queryParam.userNickname"></a-input>
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="API接口">
              <a-input placeholder="请输入API接口地址" v-model="queryParam.apiEndpoint"></a-input>
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="请求方法">
              <a-select placeholder="请选择请求方法" v-model="queryParam.apiMethod" allowClear>
                <a-select-option value="GET">GET</a-select-option>
                <a-select-option value="POST">POST</a-select-option>
                <a-select-option value="PUT">PUT</a-select-option>
                <a-select-option value="DELETE">DELETE</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="响应状态">
              <a-select placeholder="请选择响应状态" v-model="queryParam.responseStatus" allowClear>
                <a-select-option :value="200">200 成功</a-select-option>
                <a-select-option :value="400">400 请求错误</a-select-option>
                <a-select-option :value="401">401 未授权</a-select-option>
                <a-select-option :value="403">403 禁止访问</a-select-option>
                <a-select-option :value="404">404 未找到</a-select-option>
                <a-select-option :value="500">500 服务器错误</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <span class="table-page-search-submitButtons">
              <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
              <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    
    <!-- 操作按钮区域 -->
    <div class="table-operator">
      <a-button type="primary" icon="download" @click="handleExportXls('API使用记录')">导出</a-button>
      <a-button type="primary" icon="bar-chart" @click="showStats">统计分析</a-button>
    </div>

    <!-- table区域-begin -->
    <div>
      <a-table
        ref="table"
        size="middle"
        :scroll="{x:true}"
        bordered
        rowKey="id"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="ipagination"
        :loading="loading"
        class="j-table-force-nowrap"
        @change="handleTableChange">

        <template slot="responseStatus" slot-scope="text">
          <a-tag :color="text === 200 ? 'green' : 'red'">
            {{ text === 200 ? '成功' : `失败(${text})` }}
          </a-tag>
        </template>

        <span slot="action" slot-scope="text, record">
          <a @click="handleDetail(record)">详情</a>
          <a-divider type="vertical" />
          <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
            <a>删除</a>
          </a-popconfirm>
        </span>

      </a-table>
    </div>

    <!-- 详情模态框 -->
    <a-modal
      title="API使用记录详情"
      :width="800"
      :visible="detailVisible"
      @cancel="detailVisible = false"
      :footer="null">
      <div v-if="detailData">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="用户ID">{{ detailData.user_id }}</a-descriptions-item>
          <a-descriptions-item label="用户昵称">{{ detailData.userNickname || '-' }}</a-descriptions-item>
          <a-descriptions-item label="API密钥">{{ detailData.api_key }}</a-descriptions-item>
          <a-descriptions-item label="API接口">{{ detailData.api_endpoint }}</a-descriptions-item>
          <a-descriptions-item label="请求方法">{{ detailData.api_method }}</a-descriptions-item>
          <a-descriptions-item label="响应状态">
            <a-tag :color="detailData.response_status === 200 ? 'green' : 'red'">
              {{ detailData.response_status }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="响应时间">{{ detailData.response_time }}ms</a-descriptions-item>
          <a-descriptions-item label="消耗Token">{{ detailData.tokens_used || '-' }}</a-descriptions-item>
          <a-descriptions-item label="消耗金额">¥{{ detailData.cost_amount || '0.00' }}</a-descriptions-item>
          <a-descriptions-item label="IP地址">{{ detailData.ip_address }}</a-descriptions-item>
          <a-descriptions-item label="调用时间">{{ formatDateTime(detailData.call_time) }}</a-descriptions-item>
          <a-descriptions-item label="创建时间">{{ formatDateTime(detailData.create_time) }}</a-descriptions-item>
        </a-descriptions>

        <h4 style="margin-top: 16px;">请求参数</h4>
        <pre style="background: #f5f5f5; padding: 10px; border-radius: 4px; max-height: 200px; overflow-y: auto;">{{ detailData.request_params }}</pre>

        <div v-if="detailData.error_message">
          <h4>错误信息</h4>
          <a-alert :message="detailData.error_message" type="error" />
        </div>

        <div v-if="detailData.plugin_name">
          <h4>插件信息</h4>
          <a-descriptions :column="2" bordered>
            <a-descriptions-item label="插件名称">{{ detailData.plugin_name }}</a-descriptions-item>
            <a-descriptions-item label="插件标识">{{ detailData.plugin_key }}</a-descriptions-item>
          </a-descriptions>
        </div>
      </div>
    </a-modal>

    <!-- 统计分析模态框 -->
    <a-modal
      title="API使用统计分析"
      :width="1000"
      :visible="statsVisible"
      @cancel="statsVisible = false"
      :footer="null">
      <div v-if="statsData">
        <a-row :gutter="16">
          <a-col :span="6">
            <a-statistic title="总调用次数" :value="statsData.total_calls" />
          </a-col>
          <a-col :span="6">
            <a-statistic title="成功调用" :value="statsData.success_calls" />
          </a-col>
          <a-col :span="6">
            <a-statistic title="错误调用" :value="statsData.error_calls" />
          </a-col>
          <a-col :span="6">
            <a-statistic title="平均响应时间" :value="statsData.avg_response_time" suffix="ms" />
          </a-col>
        </a-row>
        <a-divider />
        <a-row :gutter="16">
          <a-col :span="12">
            <a-statistic title="总消耗Token" :value="statsData.total_tokens" />
          </a-col>
          <a-col :span="12">
            <a-statistic title="总消耗金额" :value="statsData.total_cost" prefix="¥" :precision="2" />
          </a-col>
        </a-row>
      </div>
    </a-modal>
  </a-card>
</template>

<script>

  import '@/assets/less/TableExpand.less'
  import { mixinDevice } from '@/utils/mixin'
  import { JeecgListMixin } from '@/mixins/JeecgListMixin'

  export default {
    name: 'AicgUserApiUsageList',
    mixins:[JeecgListMixin, mixinDevice],
    components: {
    },
    data () {
      return {
        description: 'API使用记录管理页面',
        statsVisible: false,
        statsData: null,
        detailVisible: false,
        detailData: null,
        // 表头
        columns: [
          {
            title: '#',
            dataIndex: '',
            key:'rowIndex',
            width:60,
            align:"center",
            customRender:function (t,r,index) {
              return parseInt(index)+1;
            }
          },
          {
            title:'用户ID',
            align:"center",
            dataIndex: 'user_id',
            width: 120
          },
          {
            title:'用户昵称',
            align:"center",
            dataIndex: 'userNickname',
            width: 120,
            ellipsis: true
          },
          {
            title:'API接口',
            align:"center",
            dataIndex: 'api_endpoint',
            width: 200,
            ellipsis: true
          },
          {
            title:'请求方法',
            align:"center",
            dataIndex: 'api_method',
            width: 80
          },
          {
            title:'响应状态',
            align:"center",
            dataIndex: 'response_status',
            width: 100,
            scopedSlots: { customRender: 'responseStatus' }
          },
          {
            title:'响应时间(ms)',
            align:"center",
            dataIndex: 'response_time',
            width: 120,
            customRender: function (text) {
              return text ? `${text}ms` : '-'
            }
          },
          {
            title:'消耗Token',
            align:"center",
            dataIndex: 'tokens_used',
            width: 100,
            customRender: function (text) {
              return text || '-'
            }
          },
          {
            title:'消耗金额',
            align:"center",
            dataIndex: 'cost_amount',
            width: 100,
            customRender: function (text) {
              return text ? `¥${text}` : '-'
            }
          },
          {
            title:'调用时间',
            align:"center",
            dataIndex: 'call_time',
            width: 150,
            customRender: function (text) {
              if (text) {
                return new Date(text).toLocaleString('zh-CN', {
                  year: 'numeric',
                  month: '2-digit',
                  day: '2-digit',
                  hour: '2-digit',
                  minute: '2-digit',
                  second: '2-digit'
                })
              }
              return '-'
            }
          },
          {
            title: '操作',
            dataIndex: 'action',
            align:"center",
            fixed:"right",
            width:120,
            scopedSlots: { customRender: 'action' }
          }
        ],
        url: {
          list: "/demo/apiusage/list",
          delete: "/demo/apiusage/delete",
          deleteBatch: "/demo/apiusage/deleteBatch",
          exportXlsUrl: "/demo/apiusage/exportXls",
          importExcelUrl: "demo/apiusage/importExcel",
          edit: "/demo/apiusage/queryById", // 用于详情查看
        },
        dictOptions:{},
        superFieldList:[],
      }
    },
    created() {
      this.getSuperFieldList();
    },
    computed: {
      importExcelUrl: function(){
        return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`;
      },
    },
    methods: {
      initDictConfig(){
      },
      getSuperFieldList(){
        let fieldList=[];
        fieldList.push({type:'string',value:'user_id',text:'用户ID'})
        fieldList.push({type:'string',value:'userNickname',text:'用户昵称'})
        fieldList.push({type:'string',value:'api_endpoint',text:'API接口地址'})
        fieldList.push({type:'string',value:'api_method',text:'请求方法'})
        fieldList.push({type:'int',value:'response_status',text:'响应状态码'})
        fieldList.push({type:'int',value:'response_time',text:'响应时间'})
        fieldList.push({type:'int',value:'tokens_used',text:'消耗Token数量'})
        fieldList.push({type:'BigDecimal',value:'cost_amount',text:'消耗金额'})
        fieldList.push({type:'Date',value:'call_time',text:'调用时间'})
        this.superFieldList = fieldList
      },
      showStats() {
        // 获取统计数据
        this.$http.get('/demo/apiusage/getStats?userId=&timeRange=month').then(res => {
          if (res.success) {
            this.statsData = res.result
            this.statsVisible = true
          } else {
            this.$message.error(res.message)
          }
        })
      },
      // 重写详情方法，避免使用JeecgListMixin的编辑功能
      handleDetail(record) {
        this.detailData = record
        this.detailVisible = true
      },
      // 格式化时间显示
      formatDateTime(dateTime) {
        if (!dateTime) return '-'
        return new Date(dateTime).toLocaleString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit'
        })
      }
    }
  }
</script>
<style scoped>
  @import '~@assets/less/common.less';
</style>
