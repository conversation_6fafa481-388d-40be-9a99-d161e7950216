import TOSBase from '../base';
export interface EncryptionData {
    Rule: EncryptionDataRule;
}
export interface EncryptionDataRule {
    ApplyServerSideEncryptionByDefault: {
        SSEAlgorithm: string;
        KMSMasterKeyID?: string;
        /** @private unstable */
        KMSDataEncryption?: string;
    };
}
export declare function putBucketEncryption(this: TOSBase, input: {
    rule: EncryptionDataRule;
} & {
    bucket?: string;
}): Promise<import("../base").TosResponse<unknown>>;
export declare function getBucketEncryption(this: TOSBase, input: {
    bucket?: string;
}): Promise<import("../base").TosResponse<EncryptionData>>;
export declare function deleteBucketEncryption(this: TOSBase, input: {
    bucket?: string;
}): Promise<import("../base").TosResponse<unknown>>;
