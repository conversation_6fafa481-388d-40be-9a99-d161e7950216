<template>
  <a-modal
    :title="title"
    :width="800"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel"
    cancelText="关闭">
    
    <a-spin :spinning="confirmLoading">
      <a-form-model ref="form" :model="model" :rules="validatorRules" v-bind="layout">
        <a-row>
          <a-col :span="12">
            <a-form-model-item label="程序类型" prop="programType">
              <j-dict-select-tag placeholder="请选择程序类型" v-model="model.programType" dictCode="program_type" :disabled="!!model.id" @change="handleProgramTypeChange"/>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="版本号" prop="versionNumber">
              <a-input v-model="model.versionNumber" placeholder="请输入版本号，如：1.0.0">
                <a-button slot="addonAfter" @click="getSuggestedVersion" :disabled="!model.programType">建议</a-button>
              </a-input>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="12">
            <a-form-model-item label="发布日期" prop="releaseDate">
              <j-date placeholder="请选择发布日期" v-model="model.releaseDate" :show-time="true" date-format="YYYY-MM-DD HH:mm:ss"/>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="是否最新版本" prop="isLatest">
              <j-dict-select-tag placeholder="请选择" v-model="model.isLatest" dictCode="isTrue"/>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="12">
            <a-form-model-item label="状态" prop="status">
              <j-dict-select-tag placeholder="请选择状态" v-model="model.status" dictCode="valid_status"/>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="24">
            <a-form-model-item label="更新内容" prop="updateContent">
              <a-textarea v-model="model.updateContent" :rows="6" placeholder="请输入详细的更新内容说明"/>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="24">
            <a-form-model-item label="下载链接" prop="downloadUrl">
              <a-input v-model="model.downloadUrl" placeholder="请输入下载链接，如：https://aigcview.com/download/desktop/v1.0.0"/>
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </a-spin>
  </a-modal>
</template>

<script>
  import { httpAction, getAction } from '@/api/manage'
  import { validateDuplicateValue } from '@/utils/util'
  import moment from 'moment'

  export default {
    name: 'AigcVersionControlModal',
    components: {
    },
    data () {
      return {
        layout: {
          labelCol: {
            xs: { span: 24 },
            sm: { span: 5 },
          },
          wrapperCol: {
            xs: { span: 24 },
            sm: { span: 16 },
          },
        },
        title:"操作",
        visible: false,
        model: {},
        confirmLoading: false,
        validatorRules: {
          programType: [
            { required: true, message: '请选择程序类型!' },
          ],
          versionNumber: [
            { required: true, message: '请输入版本号!' },
            { pattern: /^\d+\.\d+\.\d+$/, message: '版本号格式不正确，请使用x.y.z格式!' },
            { validator: this.validateVersionNumber }
          ],
          releaseDate: [
            { required: true, message: '请选择发布日期!' },
          ],
          updateContent: [
            { max: 5000, message: '更新内容不能超过5000个字符!' },
          ],
          downloadUrl: [
            { required: true, message: '请输入下载链接!' },
            { type: 'url', message: '请输入正确的URL格式!' },
          ],
        },
        url: {
          add: "/aigcview/versioncontrol/add",
          edit: "/aigcview/versioncontrol/edit",
          queryById: "/aigcview/versioncontrol/queryById",
          checkVersionExists: "/aigcview/versioncontrol/checkVersionExists",
          getNextSuggestedVersion: "/aigcview/versioncontrol/getNextSuggestedVersion"
        }
      }
    },
    created () {
    },
    methods: {
      add () {
        this.edit({});
      },
      edit (record) {
        this.model = Object.assign({}, record)
        this.visible = true

        // 如果是新增，设置默认值
        if (!this.model.id) {
          this.model.isLatest = 2  // 默认为"否"（isTrue字典：1是，2否）
          this.model.status = 1
          this.model.releaseDate = moment().format('YYYY-MM-DD HH:mm:ss')
        }

        console.log('表单数据', this.model)
      },
      close () {
        this.$refs.form.resetFields()
        this.$emit('close')
        this.visible = false
      },
      handleOk () {
        const that = this;
        // 触发表单验证
        this.$refs.form.validate(valid => {
          if (valid) {
            that.confirmLoading = true;
            let httpurl = '';
            let method = '';
            if(!this.model.id){
              httpurl+=this.url.add;
              method = 'post';
            }else{
              httpurl+=this.url.edit;
              method = 'put';
            }
            httpAction(httpurl,this.model,method).then((res)=>{
              if(res.success){
                that.$message.success(res.message);
                that.$emit('ok');
              }else{
                that.$message.warning(res.message);
              }
            }).finally(() => {
              that.confirmLoading = false;
            })
          }
        })
      },
      handleCancel () {
        this.close()
      },
      // 版本号验证
      validateVersionNumber(rule, value, callback) {
        if (!value) {
          callback();
          return;
        }
        
        // 检查版本号是否已存在
        if (this.model.programType) {
          const params = {
            programType: this.model.programType,
            versionNumber: value,
            excludeId: this.model.id || ''
          };
          
          getAction(this.url.checkVersionExists, params).then((res) => {
            if (res.success && res.result) {
              callback(new Error('该程序类型下版本号已存在!'));
            } else {
              callback();
            }
          }).catch(() => {
            callback();
          });
        } else {
          callback();
        }
      },
      // 获取建议版本号
      getSuggestedVersion() {
        if (!this.model.programType) {
          this.$message.warning('请先选择程序类型');
          return;
        }
        
        const params = {
          programType: this.model.programType,
          versionType: 'patch' // 默认获取补丁版本
        };
        
        getAction(this.url.getNextSuggestedVersion, params).then((res) => {
          if (res.success) {
            this.model.versionNumber = res.result;
            this.$message.success('已自动填入建议版本号');
          } else {
            this.$message.warning('获取建议版本号失败');
          }
        });
      },
      // 程序类型改变时清空版本号
      handleProgramTypeChange() {
        this.model.versionNumber = ''
        console.log('程序类型改变，清空版本号')
      }
    }
  }
</script>

<style lang="less" scoped>
  /** Button按钮间距 */
  .ant-btn {
    margin-left: 30px;
    margin-bottom: 30px;
    float: right;
  }
</style>
