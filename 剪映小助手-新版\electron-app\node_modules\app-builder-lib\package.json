{"name": "app-builder-lib", "description": "electron-builder lib", "version": "24.13.3", "main": "out/index.js", "files": ["out", "templates", "scheme.json", "certs/root_certs.keychain"], "repository": {"type": "git", "url": "git+https://github.com/electron-userland/electron-builder.git", "directory": "packages/app-builder-lib"}, "engines": {"node": ">=14.0.0"}, "keywords": ["electron", "builder", "build", "installer", "install", "packager", "pack", "nsis", "app", "dmg", "pkg", "msi", "exe", "setup", "Windows", "OS X", "MacOS", "<PERSON>", "appx", "snap", "flatpak", "portable"], "author": "<PERSON>", "license": "MIT", "bugs": "https://github.com/electron-userland/electron-builder/issues", "homepage": "https://github.com/electron-userland/electron-builder", "dependencies": {"@develar/schema-utils": "~2.6.5", "@electron/notarize": "2.2.1", "@electron/osx-sign": "1.0.5", "@electron/universal": "1.5.1", "@malept/flatpak-bundler": "^0.4.0", "@types/fs-extra": "9.0.13", "async-exit-hook": "^2.0.1", "bluebird-lst": "^1.0.9", "chromium-pickle-js": "^0.2.0", "debug": "^4.3.4", "ejs": "^3.1.8", "form-data": "^4.0.0", "fs-extra": "^10.1.0", "hosted-git-info": "^4.1.0", "is-ci": "^3.0.0", "isbinaryfile": "^5.0.0", "js-yaml": "^4.1.0", "lazy-val": "^1.0.5", "minimatch": "^5.1.1", "read-config-file": "6.3.2", "sanitize-filename": "^1.6.3", "semver": "^7.3.8", "tar": "^6.1.12", "temp-file": "^3.4.0", "builder-util": "24.13.1", "electron-publish": "24.13.1", "builder-util-runtime": "9.2.4"}, "///": "babel in devDependencies for proton tests", "devDependencies": {"@babel/core": "7.15.5", "@babel/plugin-proposal-class-properties": "7.14.5", "@babel/plugin-proposal-decorators": "7.15.4", "@babel/plugin-proposal-do-expressions": "7.14.5", "@babel/plugin-proposal-export-default-from": "7.14.5", "@babel/plugin-proposal-export-namespace-from": "7.14.5", "@babel/plugin-proposal-function-bind": "7.14.5", "@babel/plugin-proposal-function-sent": "7.14.5", "@babel/plugin-proposal-json-strings": "7.14.5", "@babel/plugin-proposal-logical-assignment-operators": "7.14.5", "@babel/plugin-proposal-nullish-coalescing-operator": "7.14.5", "@babel/plugin-proposal-numeric-separator": "7.14.5", "@babel/plugin-proposal-optional-chaining": "7.14.5", "@babel/plugin-proposal-pipeline-operator": "7.15.0", "@babel/plugin-proposal-throw-expressions": "7.14.5", "@babel/plugin-syntax-dynamic-import": "7.8.3", "@babel/plugin-syntax-import-meta": "7.10.4", "@babel/preset-env": "7.15.6", "@babel/preset-react": "7.14.5", "@types/debug": "4.1.7", "@types/ejs": "3.1.0", "@types/hosted-git-info": "3.0.2", "@types/is-ci": "3.0.0", "@types/js-yaml": "4.0.3", "@types/semver": "7.3.8", "@types/tar": "^6.1.3", "dmg-builder": "24.13.3", "electron-builder-squirrel-windows": "24.13.3"}, "peerDependencies": {"dmg-builder": "24.13.3", "electron-builder-squirrel-windows": "24.13.3"}, "//": "electron-builder-squirrel-windows and dmg-builder added as dev dep for tests (as otherwise `require` doesn't work using Yarn 2)", "typings": "./out/index.d.ts"}