/// <reference types="node" />
export declare const hmacSha256: (key: string, message: string, decoding?: "base64" | "hex" | undefined) => string;
export declare const hashSha256: (message: string, decoding?: "base64" | "hex" | undefined) => string;
export declare const hashMd5: (message: string | Buffer, decoding?: "base64" | "hex" | undefined) => string;
export declare const parse: (str: string, encoding: 'utf-8' | 'base64' | 'hex') => CryptoJS.lib.WordArray;
export declare const stringify: (str: CryptoJS.lib.WordArray, decoding: 'utf-8' | 'base64' | 'hex') => string;
