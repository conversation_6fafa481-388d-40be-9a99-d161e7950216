package org.jeecg.modules.jianying.dto;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 关键帧数据生成器请求参数
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class KeyframesInfosRequest extends BaseJianyingRequest {
    
    @ApiModelProperty(value = "关键帧类型（必填）", required = true,
                     example = "KFTypeAlpha")
    @NotBlank(message = "ctype不能为空")
    @JsonProperty("ctype")
    private String zjCtype;

    @ApiModelProperty(value = "需要放置关键帧的位置比例（必填）", required = true,
                     example = "0|100")
    @NotBlank(message = "offsets不能为空")
    @JsonProperty("offsets")
    private String zjOffsets;

    @ApiModelProperty(value = "轨道数据，add_images节点输出（必填）", required = true,
                     example = "[{\"id\": \"segment1\", \"type\": \"video\"}]")
    @NotEmpty(message = "segment_infos不能为空")
    @JsonProperty("segment_infos")
    private List<JSONObject> zjSegmentInfos;

    @ApiModelProperty(value = "对应offsets的值，长度要一致（必填）", required = true,
                     example = "1|2")
    @NotBlank(message = "values不能为空")
    @JsonProperty("values")
    private String zjValues;

    @ApiModelProperty(value = "视频宽度", example = "1920")
    @JsonProperty("width")
    private Integer zjWidth;

    @ApiModelProperty(value = "视频高度", example = "1080")
    @JsonProperty("height")
    private Integer zjHeight;
    
    @Override
    public String getSummary() {
        return "KeyframesInfosRequest{" +
               "accessKey=" + (getAccessKey() != null && getAccessKey().length() > 10 ?
                              getAccessKey().substring(0, 10) + "***" : "null") +
               ", ctype=" + zjCtype +
               ", offsets=" + zjOffsets +
               ", segmentInfosCount=" + (zjSegmentInfos != null ? zjSegmentInfos.size() : 0) +
               ", values=" + zjValues +
               ", width=" + zjWidth +
               ", height=" + zjHeight +
               "}";
    }
}
