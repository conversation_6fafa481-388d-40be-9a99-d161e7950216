/**
 * Electron应用版本检查器
 * 负责检查应用版本更新，支持强制更新和可选更新
 */

const { app, shell, BrowserWindow, ipcMain } = require('electron');
const axios = require('axios');
const Store = require('electron-store');
const path = require('path');
const fs = require('fs');
const NetworkDiagnostic = require('./NetworkDiagnostic');

class VersionChecker {
  constructor(options = {}) {
    this.apiBaseUrl = options.apiBaseUrl || 'https://aigcview.com/jeecg-boot';
    this.programType = options.programType || 'desktop';
    this.checkInterval = options.checkInterval || 24 * 60 * 60 * 1000; // 24小时
    this.maxRetries = options.maxRetries || 3;
    this.requestTimeout = options.requestTimeout || 10000; // 10秒
    this.enableLogging = options.enableLogging !== false; // 默认启用日志
    this.networkDiagnostic = new NetworkDiagnostic();
    
    // 初始化本地存储
    this.store = new Store({
      name: 'version-check-cache',
      defaults: {
        lastCheckTime: 0,
        cachedVersionInfo: null,
        userPreferences: {
          autoCheck: true,
          skippedVersions: [],
          reminderDelay: 3600000 // 1小时
        }
      }
    });
    
    // 从package.json读取应用版本，而不是Electron版本
    this.currentVersion = this.getAppVersion();

    // 初始化日志系统
    this.initLogging();

    // 绑定IPC事件
    this.setupIpcHandlers();

    this.log('VersionChecker 初始化完成', {
      currentVersion: this.currentVersion,
      apiBaseUrl: this.apiBaseUrl,
      programType: this.programType
    });
  }

  /**
   * 设置IPC事件处理器
   * 注意：IPC处理器统一在main.js中注册，这里只处理内部逻辑
   */
  setupIpcHandlers() {
    // 这里不再注册IPC处理器，避免与main.js中的注册冲突
    // 所有IPC处理器都在main.js中统一管理
    this.log('VersionChecker IPC处理器设置完成');
  }

  /**
   * 检查版本更新
   * @param {boolean} forceCheck 是否强制检查（忽略缓存）
   * @returns {Promise<Object>} 版本检查结果
   */
  async checkForUpdates(forceCheck = false) {
    try {
      this.log('开始检查版本更新', { forceCheck });

      // 检查是否需要使用缓存
      if (!forceCheck && this.isCacheValid()) {
        this.log('使用缓存的版本信息');
        const cachedInfo = this.store.get('cachedVersionInfo');
        return this.processVersionInfo(cachedInfo);
      }

      // 从服务器获取最新版本信息
      const versionInfo = await this.fetchLatestVersion();

      // 缓存版本信息
      this.store.set('cachedVersionInfo', versionInfo);
      this.store.set('lastCheckTime', Date.now());

      this.log('版本检查完成', {
        hasUpdate: versionInfo ? true : false,
        latestVersion: versionInfo?.versionNumber
      });

      return this.processVersionInfo(versionInfo);

    } catch (error) {
      this.log('版本检查失败', { error: error.message }, 'error');
      return this.handleCheckError(error);
    }
  }

  /**
   * 从服务器获取最新版本信息
   * @returns {Promise<Object>} 版本信息
   */
  async fetchLatestVersion() {
    let lastError;
    
    for (let attempt = 1; attempt <= this.maxRetries; attempt++) {
      try {
        this.log(`尝试获取版本信息 (${attempt}/${this.maxRetries})`, {
          apiUrl: `${this.apiBaseUrl}/aigcview/versioncontrol/getLatest`,
          programType: this.programType
        });

        const response = await axios.get(`${this.apiBaseUrl}/aigcview/versioncontrol/getLatest`, {
          params: { programType: this.programType },
          timeout: this.requestTimeout,
          headers: {
            'User-Agent': `AigcView-Desktop/${this.currentVersion}`,
            'Accept': 'application/json',
            'Cache-Control': 'no-cache'
          },
          // 添加HTTPS配置，解决TLS连接问题
          httpsAgent: new (require('https').Agent)({
            rejectUnauthorized: false, // 开发环境可以忽略证书验证
            keepAlive: true,
            timeout: this.requestTimeout
          }),
          // 添加重试配置
          validateStatus: function (status) {
            return status >= 200 && status < 300;
          }
        });

        if (response.data && response.data.success) {
          this.log('成功获取版本信息', {
            version: response.data.result?.versionNumber,
            updateContent: response.data.result?.updateContent?.substring(0, 100) + '...'
          });
          return response.data.result;
        } else {
          throw new Error(response.data?.message || '服务器返回错误');
        }

      } catch (error) {
        lastError = error;

        // 详细的错误信息记录
        const errorInfo = {
          message: error.message,
          code: error.code,
          attempt: attempt,
          maxRetries: this.maxRetries,
          url: `${this.apiBaseUrl}/aigcview/versioncontrol/getLatest`,
          timeout: this.requestTimeout
        };

        // 根据错误类型提供更具体的信息
        if (error.code === 'ECONNRESET') {
          errorInfo.suggestion = '网络连接被重置，可能是防火墙或代理问题';
        } else if (error.code === 'ENOTFOUND') {
          errorInfo.suggestion = 'DNS解析失败，请检查网络连接';
        } else if (error.code === 'ETIMEDOUT') {
          errorInfo.suggestion = '连接超时，请检查网络速度';
        }

        this.log(`第${attempt}次尝试失败`, errorInfo, 'warn');

        if (attempt < this.maxRetries) {
          // 等待后重试，间隔递增
          const delay = attempt * 2000; // 增加重试间隔
          this.log(`等待${delay}ms后重试...`);
          await this.delay(delay);
        }
      }
    }

    // 所有重试都失败了，进行网络诊断
    this.log('所有重试都失败，开始网络诊断...', {
      totalAttempts: this.maxRetries,
      finalError: lastError.message
    }, 'error');

    // 异步进行网络诊断，不阻塞主流程
    this.performNetworkDiagnostic();

    throw lastError;
  }

  /**
   * 处理版本信息
   * @param {Object} versionInfo 版本信息
   * @returns {Object} 处理结果
   */
  processVersionInfo(versionInfo) {
    if (!versionInfo) {
      return { hasUpdate: false, error: '未获取到版本信息' };
    }

    const latestVersion = versionInfo.versionNumber;
    const compareResult = this.compareVersions(this.currentVersion, latestVersion);

    // 🚀 解析下载链接，根据当前系统选择对应的链接
    const downloadUrl = this.parseDownloadUrl(versionInfo.downloadUrl);

    const result = {
      hasUpdate: compareResult < 0,
      currentVersion: this.currentVersion,
      latestVersion: latestVersion,
      updateContent: versionInfo.updateContent,
      downloadUrl: downloadUrl,
      originalDownloadUrl: versionInfo.downloadUrl, // 保留原始链接用于调试
      releaseDate: versionInfo.releaseDate,
      forceUpdate: this.shouldForceUpdate(versionInfo),
      versionInfo: versionInfo
    };

    console.log('版本检查结果:', result);
    return result;
  }

  /**
   * 🚀 解析下载链接，根据当前系统选择对应的链接
   * @param {string} downloadUrlField 下载链接字段
   * @returns {string} 解析后的下载链接
   */
  parseDownloadUrl(downloadUrlField) {
    if (!downloadUrlField) {
      this.log('下载链接字段为空', {}, 'warn');
      return '';
    }

    // 获取当前系统类型
    const currentPlatform = this.getCurrentPlatform();
    this.log('当前系统平台', { platform: currentPlatform });

    // 尝试解析多平台链接
    const parsedUrls = this.extractPlatformUrls(downloadUrlField);

    if (parsedUrls.count === 1) {
      // 只有一个链接，直接使用
      this.log('检测到单个下载链接，直接使用', { url: parsedUrls.single });
      return parsedUrls.single;
    }

    // 多个链接，根据系统选择
    const selectedUrl = parsedUrls[currentPlatform] || parsedUrls.windows || parsedUrls.single;

    this.log('多平台链接解析完成', {
      originalField: downloadUrlField,
      parsedUrls: parsedUrls,
      currentPlatform: currentPlatform,
      selectedUrl: selectedUrl
    });

    return selectedUrl || '';
  }

  /**
   * 获取当前系统平台
   * @returns {string} 平台标识
   */
  getCurrentPlatform() {
    const os = require('os');
    const platform = os.platform();

    switch (platform) {
      case 'win32':
        return 'windows';
      case 'darwin':
        return 'mac';
      case 'linux':
        return 'linux';
      default:
        this.log('未知系统平台，默认使用Windows', { platform }, 'warn');
        return 'windows';
    }
  }

  /**
   * 从下载链接字段中提取各平台的链接
   * @param {string} downloadUrlField 下载链接字段
   * @returns {Object} 解析结果
   */
  extractPlatformUrls(downloadUrlField) {
    const result = {
      count: 0,
      single: '',
      windows: '',
      mac: '',
      linux: ''
    };

    // 支持多种分隔符：| ; , \n
    const separators = ['|', ';', ',', '\n'];
    let urls = [downloadUrlField];

    // 尝试用不同分隔符分割
    for (const separator of separators) {
      if (downloadUrlField.includes(separator)) {
        urls = downloadUrlField.split(separator).map(url => url.trim()).filter(url => url);
        this.log('使用分隔符解析链接', { separator, urls });
        break;
      }
    }

    result.count = urls.length;

    if (urls.length === 1) {
      // 单个链接
      result.single = urls[0];
    } else if (urls.length === 2) {
      // 两个链接：第一个Windows，第二个Mac
      result.windows = urls[0];
      result.mac = urls[1];
      this.log('解析为双平台链接', { windows: result.windows, mac: result.mac });
    } else if (urls.length === 3) {
      // 三个链接：Windows, Mac, Linux
      result.windows = urls[0];
      result.mac = urls[1];
      result.linux = urls[2];
      this.log('解析为三平台链接', { windows: result.windows, mac: result.mac, linux: result.linux });
    } else {
      // 其他情况，使用第一个链接
      result.single = urls[0];
      this.log('链接数量异常，使用第一个链接', { count: urls.length, selected: result.single }, 'warn');
    }

    return result;
  }

  /**
   * 比较版本号
   * @param {string} version1 版本1
   * @param {string} version2 版本2
   * @returns {number} -1: version1 < version2, 0: 相等, 1: version1 > version2
   */
  compareVersions(version1, version2) {
    const v1Parts = version1.split('.').map(Number);
    const v2Parts = version2.split('.').map(Number);
    
    const maxLength = Math.max(v1Parts.length, v2Parts.length);
    
    for (let i = 0; i < maxLength; i++) {
      const v1Part = v1Parts[i] || 0;
      const v2Part = v2Parts[i] || 0;
      
      if (v1Part > v2Part) return 1;
      if (v1Part < v2Part) return -1;
    }
    
    return 0;
  }

  /**
   * 判断是否应该强制更新
   * @param {Object} versionInfo 版本信息
   * @returns {boolean} 是否强制更新
   */
  shouldForceUpdate(versionInfo) {
    // 这里可以根据业务需求定义强制更新的条件
    // 例如：安全更新、重大版本更新等
    
    // 示例：如果更新内容包含"安全"或"紧急"关键词，则强制更新
    const updateContent = versionInfo.updateContent || '';
    const forceKeywords = ['安全', '紧急', '重要', '修复严重'];
    
    return forceKeywords.some(keyword => updateContent.includes(keyword));
  }

  /**
   * 检查缓存是否有效
   * @returns {boolean} 缓存是否有效
   */
  isCacheValid() {
    const lastCheckTime = this.store.get('lastCheckTime', 0);
    const now = Date.now();
    return (now - lastCheckTime) < this.checkInterval;
  }

  /**
   * 处理检查错误
   * @param {Error} error 错误对象
   * @returns {Object} 错误处理结果
   */
  handleCheckError(error) {
    this.log('版本检查异常', {
      error: error.message,
      code: error.code,
      stack: error.stack
    }, 'error');

    // 分析错误类型
    let errorType = 'unknown';
    let userMessage = '检查更新时发生未知错误';

    if (error.code === 'ENOTFOUND') {
      errorType = 'dns_error';
      userMessage = '无法解析服务器地址，请检查网络连接';
    } else if (error.code === 'ECONNREFUSED') {
      errorType = 'connection_refused';
      userMessage = '无法连接到更新服务器';
    } else if (error.code === 'ETIMEDOUT') {
      errorType = 'timeout';
      userMessage = '连接超时，请检查网络状况';
    } else if (error.response?.status === 404) {
      errorType = 'not_found';
      userMessage = '更新服务暂时不可用';
    } else if (error.response?.status >= 500) {
      errorType = 'server_error';
      userMessage = '服务器暂时出现问题';
    }

    // 尝试使用缓存数据
    const cachedInfo = this.store.get('cachedVersionInfo');
    if (cachedInfo) {
      this.log('网络异常，使用缓存数据', { errorType });
      return {
        ...this.processVersionInfo(cachedInfo),
        networkError: true,
        errorType: errorType,
        errorMessage: userMessage,
        originalError: error.message,
        fallbackUsed: true
      };
    }

    // 无缓存数据，返回错误信息
    this.log('无缓存数据可用，返回错误状态', { errorType });
    return {
      hasUpdate: false,
      networkError: true,
      errorType: errorType,
      errorMessage: userMessage,
      originalError: error.message,
      fallback: true
    };
  }

  /**
   * 处理用户选择
   * @param {string} choice 用户选择 ('update', 'later', 'skip')
   * @param {Object} versionInfo 版本信息
   */
  handleUserChoice(choice, versionInfo) {
    console.log('用户选择:', choice);
    
    switch (choice) {
      case 'update':
        this.openDownloadPage(versionInfo.downloadUrl);
        break;
        
      case 'later':
        this.setReminder(versionInfo);
        break;
        
      case 'skip':
        this.skipVersion(versionInfo.versionNumber);
        break;
        
      case 'exit':
        if (versionInfo.forceUpdate) {
          app.quit();
        }
        break;
    }
    
    // 关闭更新窗口
    if (this.updateWindow) {
      this.updateWindow.close();
      this.updateWindow = null;
    }
  }

  /**
   * 打开下载页面
   * @param {string} downloadUrl 下载链接
   */
  openDownloadPage(downloadUrl) {
    if (downloadUrl) {
      shell.openExternal(downloadUrl);
      console.log('已打开下载页面:', downloadUrl);
    } else {
      console.error('下载链接为空');
    }
  }

  /**
   * 设置提醒
   * @param {Object} versionInfo 版本信息
   */
  setReminder(versionInfo) {
    const reminderDelay = this.store.get('userPreferences.reminderDelay', 3600000);
    
    setTimeout(() => {
      console.log('提醒时间到，重新检查更新');
      this.checkForUpdates(true);
    }, reminderDelay);
    
    console.log(`已设置${reminderDelay / 60000}分钟后提醒`);
  }

  /**
   * 跳过版本
   * @param {string} version 要跳过的版本号
   */
  skipVersion(version) {
    const skippedVersions = this.store.get('userPreferences.skippedVersions', []);
    if (!skippedVersions.includes(version)) {
      skippedVersions.push(version);
      this.store.set('userPreferences.skippedVersions', skippedVersions);
      console.log('已跳过版本:', version);
    }
  }

  /**
   * 延迟函数
   * @param {number} ms 延迟毫秒数
   * @returns {Promise} Promise对象
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 清除缓存
   */
  clearCache() {
    this.store.delete('cachedVersionInfo');
    this.store.delete('lastCheckTime');
    console.log('已清除版本检查缓存');
  }

  /**
   * 获取用户偏好设置
   * @returns {Object} 用户偏好设置
   */
  getUserPreferences() {
    return this.store.get('userPreferences');
  }

  /**
   * 更新用户偏好设置
   * @param {Object} preferences 偏好设置
   */
  updateUserPreferences(preferences) {
    this.store.set('userPreferences', { ...this.getUserPreferences(), ...preferences });
  }

  /**
   * 获取应用版本号（从package.json）
   * @returns {string} 应用版本号
   */
  getAppVersion() {
    try {
      // 获取应用根目录的package.json路径
      const packageJsonPath = path.join(__dirname, '../../package.json');
      console.log('尝试读取package.json路径:', packageJsonPath);

      if (!fs.existsSync(packageJsonPath)) {
        console.warn('package.json文件不存在，尝试其他路径');
        // 尝试从app.getAppPath()获取
        const appPath = app.getAppPath();
        const altPath = path.join(appPath, 'package.json');
        console.log('尝试备选路径:', altPath);

        if (fs.existsSync(altPath)) {
          const packageJson = JSON.parse(fs.readFileSync(altPath, 'utf8'));
          console.log('从备选路径读取到版本:', packageJson.version);
          return packageJson.version;
        }
      } else {
        const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
        console.log('从主路径读取到版本:', packageJson.version);
        return packageJson.version;
      }

      throw new Error('无法找到package.json文件');
    } catch (error) {
      console.error('读取package.json失败，使用Electron版本作为备选:', error.message);
      return app.getVersion();
    }
  }

  /**
   * 初始化日志系统
   */
  initLogging() {
    this.logStore = new Store({
      name: 'version-check-logs',
      defaults: {
        logs: [],
        maxLogs: 1000 // 最多保存1000条日志
      }
    });
  }

  /**
   * 记录日志
   * @param {string} message 日志消息
   * @param {Object} data 附加数据
   * @param {string} level 日志级别 (info, warn, error)
   */
  log(message, data = {}, level = 'info') {
    if (!this.enableLogging) return;

    const logEntry = {
      timestamp: new Date().toISOString(),
      level: level,
      message: message,
      data: data,
      version: this.currentVersion,
      programType: this.programType
    };

    // 控制台输出
    const consoleMethod = level === 'error' ? 'error' : level === 'warn' ? 'warn' : 'log';
    console[consoleMethod](`[VersionChecker] ${message}`, data);

    // 存储到本地
    this.saveLog(logEntry);
  }

  /**
   * 保存日志到本地存储
   * @param {Object} logEntry 日志条目
   */
  saveLog(logEntry) {
    try {
      const logs = this.logStore.get('logs', []);
      const maxLogs = this.logStore.get('maxLogs', 1000);

      logs.push(logEntry);

      // 保持日志数量在限制内
      if (logs.length > maxLogs) {
        logs.splice(0, logs.length - maxLogs);
      }

      this.logStore.set('logs', logs);
    } catch (error) {
      console.error('保存版本检查日志失败:', error);
    }
  }

  /**
   * 获取日志
   * @param {number} limit 限制数量
   * @param {string} level 日志级别过滤
   * @returns {Array} 日志数组
   */
  getLogs(limit = 100, level = null) {
    try {
      let logs = this.logStore.get('logs', []);

      // 按级别过滤
      if (level) {
        logs = logs.filter(log => log.level === level);
      }

      // 按时间倒序排列
      logs.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));

      // 限制数量
      return logs.slice(0, limit);
    } catch (error) {
      console.error('获取版本检查日志失败:', error);
      return [];
    }
  }

  /**
   * 清除日志
   */
  clearLogs() {
    try {
      this.logStore.set('logs', []);
      this.log('日志已清除');
    } catch (error) {
      console.error('清除版本检查日志失败:', error);
    }
  }

  /**
   * 导出日志
   * @returns {string} 日志的JSON字符串
   */
  exportLogs() {
    try {
      const logs = this.getLogs(1000); // 导出最近1000条日志
      return JSON.stringify(logs, null, 2);
    } catch (error) {
      console.error('导出版本检查日志失败:', error);
      return '[]';
    }
  }

  /**
   * 执行网络诊断
   */
  async performNetworkDiagnostic() {
    try {
      const url = `${this.apiBaseUrl}/aigcview/versioncontrol/getLatest`;
      this.log('开始网络诊断', { url });

      const diagnostic = await this.networkDiagnostic.diagnose(url);

      this.log('网络诊断完成', {
        success: !diagnostic.error,
        suggestions: diagnostic.suggestions,
        tests: Object.keys(diagnostic.tests || {}).map(key => ({
          test: key,
          success: diagnostic.tests[key]?.success,
          message: diagnostic.tests[key]?.message
        }))
      });

      return diagnostic;
    } catch (error) {
      this.log('网络诊断失败', { error: error.message }, 'error');
      return null;
    }
  }
}

module.exports = VersionChecker;
