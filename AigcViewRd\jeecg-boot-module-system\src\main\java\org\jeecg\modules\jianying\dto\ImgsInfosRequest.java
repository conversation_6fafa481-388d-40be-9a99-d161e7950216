package org.jeecg.modules.jianying.dto;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 图片数据生成器请求参数
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ImgsInfosRequest extends BaseJianyingRequest {
    
    @ApiModelProperty(value = "时间节点（必填）", required = true,
                     example = "[{\"start\": 0, \"end\": 4612000}]")
    @NotEmpty(message = "timelines不能为空")
    @JsonProperty("timelines")
    private List<JSONObject> zjTimelines;

    @ApiModelProperty(value = "图片列表（必填）", required = true,
                     example = "[\"https://example.com/img1.png\", \"https://example.com/img2.png\"]")
    @NotEmpty(message = "imgs不能为空")
    @JsonProperty("imgs")
    private List<String> zjImgs;

    @ApiModelProperty(value = "对应剪映的出场动画时长（可选）", example = "1000000")
    @JsonProperty("out_animation_duration")
    private Integer zjOutAnimationDuration;

    @ApiModelProperty(value = "对应剪映的转场动画名字（可选）", example = "淡入淡出")
    @JsonProperty("transition")
    private String zjTransition;

    @ApiModelProperty(value = "图片高度", example = "1080")
    @JsonProperty("height")
    private Integer zjHeight;

    @ApiModelProperty(value = "对应剪映的组合动画名字（可选）", example = "动画1|动画2")
    @JsonProperty("group_animation")
    private String zjGroupAnimation;

    @ApiModelProperty(value = "对应剪映的出场动画名字（可选）", example = "动画1|动画2")
    @JsonProperty("out_animation")
    private String zjOutAnimation;

    @ApiModelProperty(value = "图片宽度", example = "1920")
    @JsonProperty("width")
    private Integer zjWidth;

    @ApiModelProperty(value = "对应剪映的入场动画名字（可选）", example = "动画1|动画2")
    @JsonProperty("in_animation")
    private String zjInAnimation;

    @ApiModelProperty(value = "对应剪映的入场动画时长（可选）", example = "1000000")
    @JsonProperty("in_animation_duration")
    private Integer zjInAnimationDuration;

    @ApiModelProperty(value = "对应剪映的组合动画时长（可选）", example = "2000000")
    @JsonProperty("group_animation_duration")
    private Integer zjGroupAnimationDuration;

    @ApiModelProperty(value = "对应剪映的转场动画时长（可选）", example = "1500000")
    @JsonProperty("transition_duration")
    private Integer zjTransitionDuration;
    
    @Override
    public String getSummary() {
        return "ImgsInfosRequest{" +
               "accessKey=" + (getAccessKey() != null && getAccessKey().length() > 10 ? 
                              getAccessKey().substring(0, 10) + "***" : "null") +
               ", timelinesCount=" + (zjTimelines != null ? zjTimelines.size() : 0) +
               ", imgsCount=" + (zjImgs != null ? zjImgs.size() : 0) +
               "}";
    }
}
