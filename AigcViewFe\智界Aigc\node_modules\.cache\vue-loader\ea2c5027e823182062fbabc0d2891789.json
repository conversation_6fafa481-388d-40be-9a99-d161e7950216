{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\workflow\\components\\AgentManagement.vue?vue&type=template&id=66227f41&scoped=true&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\workflow\\components\\AgentManagement.vue", "mtime": 1754512044865}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["var render = function() {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  return _c(\"div\", { staticClass: \"agent-management\" }, [\n    _c(\"div\", { staticClass: \"market-filters\" }, [\n      _c(\"div\", { staticClass: \"filter-row\" }, [\n        _c(\"div\", { staticClass: \"search-box\" }, [\n          _c(\n            \"div\",\n            { staticClass: \"search-wrapper\" },\n            [\n              _c(\"a-icon\", {\n                staticClass: \"search-icon\",\n                attrs: { type: \"search\" }\n              }),\n              _c(\"a-input\", {\n                staticClass: \"search-input\",\n                attrs: {\n                  placeholder: \"搜索智能体名称、描述...\",\n                  size: \"large\"\n                },\n                on: { pressEnter: _vm.handleSearch, input: _vm.handleSearch },\n                model: {\n                  value: _vm.searchQuery,\n                  callback: function($$v) {\n                    _vm.searchQuery = $$v\n                  },\n                  expression: \"searchQuery\"\n                }\n              }),\n              _vm.searchQuery\n                ? _c(\"a-icon\", {\n                    staticClass: \"clear-icon\",\n                    attrs: { type: \"close-circle\" },\n                    on: { click: _vm.clearSearch }\n                  })\n                : _vm._e()\n            ],\n            1\n          )\n        ]),\n        _c(\"div\", { staticClass: \"filter-controls\" }, [\n          _c(\"div\", { staticClass: \"filter-group\" }, [\n            _c(\"div\", { staticClass: \"filter-buttons\" }, [\n              _c(\n                \"button\",\n                {\n                  staticClass: \"filter-btn\",\n                  class: { active: _vm.auditStatusFilter === \"\" },\n                  on: {\n                    click: function($event) {\n                      return _vm.setAuditStatusFilter(\"\")\n                    }\n                  }\n                },\n                [\n                  _c(\"a-icon\", { attrs: { type: \"appstore\" } }),\n                  _vm._v(\"\\n              全部\\n            \")\n                ],\n                1\n              ),\n              _c(\n                \"button\",\n                {\n                  staticClass: \"filter-btn pending\",\n                  class: { active: _vm.auditStatusFilter === \"1\" },\n                  on: {\n                    click: function($event) {\n                      return _vm.setAuditStatusFilter(\"1\")\n                    }\n                  }\n                },\n                [\n                  _c(\"a-icon\", { attrs: { type: \"clock-circle\" } }),\n                  _vm._v(\"\\n              待审核\\n            \")\n                ],\n                1\n              ),\n              _c(\n                \"button\",\n                {\n                  staticClass: \"filter-btn approved\",\n                  class: { active: _vm.auditStatusFilter === \"2\" },\n                  on: {\n                    click: function($event) {\n                      return _vm.setAuditStatusFilter(\"2\")\n                    }\n                  }\n                },\n                [\n                  _c(\"a-icon\", { attrs: { type: \"check-circle\" } }),\n                  _vm._v(\"\\n              已通过\\n            \")\n                ],\n                1\n              ),\n              _c(\n                \"button\",\n                {\n                  staticClass: \"filter-btn rejected\",\n                  class: { active: _vm.auditStatusFilter === \"3\" },\n                  on: {\n                    click: function($event) {\n                      return _vm.setAuditStatusFilter(\"3\")\n                    }\n                  }\n                },\n                [\n                  _c(\"a-icon\", { attrs: { type: \"close-circle\" } }),\n                  _vm._v(\"\\n              已拒绝\\n            \")\n                ],\n                1\n              )\n            ])\n          ]),\n          _c(\"div\", { staticClass: \"filter-group\" }, [\n            _c(\n              \"div\",\n              { staticClass: \"sort-controls\" },\n              [\n                _c(\"span\", { staticClass: \"sort-label\" }, [_vm._v(\"排序：\")]),\n                _c(\n                  \"a-select\",\n                  {\n                    staticStyle: { width: \"120px\", \"margin-right\": \"8px\" },\n                    attrs: { size: \"default\" },\n                    on: { change: _vm.handleSortChange },\n                    model: {\n                      value: _vm.sortField,\n                      callback: function($$v) {\n                        _vm.sortField = $$v\n                      },\n                      expression: \"sortField\"\n                    }\n                  },\n                  [\n                    _c(\n                      \"a-select-option\",\n                      { attrs: { value: \"totalRevenue\" } },\n                      [_vm._v(\"总收益\")]\n                    ),\n                    _c(\"a-select-option\", { attrs: { value: \"salesCount\" } }, [\n                      _vm._v(\"销售次数\")\n                    ]),\n                    _c(\"a-select-option\", { attrs: { value: \"createTime\" } }, [\n                      _vm._v(\"创建时间\")\n                    ])\n                  ],\n                  1\n                ),\n                _c(\n                  \"a-button\",\n                  {\n                    staticClass: \"sort-order-btn\",\n                    attrs: {\n                      icon:\n                        _vm.sortOrder === \"desc\"\n                          ? \"sort-descending\"\n                          : \"sort-ascending\",\n                      title: _vm.sortOrder === \"desc\" ? \"降序\" : \"升序\"\n                    },\n                    on: { click: _vm.toggleSortOrder }\n                  },\n                  [\n                    _vm._v(\n                      \"\\n              \" +\n                        _vm._s(_vm.sortOrder === \"desc\" ? \"降序\" : \"升序\") +\n                        \"\\n            \"\n                    )\n                  ]\n                )\n              ],\n              1\n            )\n          ])\n        ])\n      ])\n    ]),\n    !_vm.loading && _vm.agents.length > 0\n      ? _c(\n          \"div\",\n          { staticClass: \"agent-grid\" },\n          _vm._l(_vm.agents, function(agent) {\n            return _c(\n              \"div\",\n              {\n                key: agent.id,\n                staticClass: \"agent-card\",\n                class: {\n                  pending: agent.auditStatus === \"1\",\n                  approved: agent.auditStatus === \"2\",\n                  rejected: agent.auditStatus === \"3\"\n                }\n              },\n              [\n                _c(\"div\", { staticClass: \"card-header\" }, [\n                  _c(\"div\", { staticClass: \"agent-avatar\" }, [\n                    agent.agentAvatar\n                      ? _c(\"img\", {\n                          attrs: {\n                            src: agent.agentAvatar,\n                            alt: agent.agentName\n                          },\n                          on: { error: _vm.handleImageError }\n                        })\n                      : _c(\n                          \"div\",\n                          { staticClass: \"avatar-placeholder\" },\n                          [_c(\"a-icon\", { attrs: { type: \"robot\" } })],\n                          1\n                        )\n                  ]),\n                  _c(\"div\", { staticClass: \"agent-info\" }, [\n                    _c(\n                      \"h3\",\n                      {\n                        staticClass: \"agent-name\",\n                        attrs: { title: agent.agentName }\n                      },\n                      [_vm._v(_vm._s(agent.agentName))]\n                    ),\n                    _c(\n                      \"div\",\n                      { staticClass: \"agent-meta\" },\n                      [\n                        _c(\n                          \"a-tag\",\n                          {\n                            staticClass: \"status-tag\",\n                            attrs: {\n                              color: _vm.getStatusColor(agent.auditStatus)\n                            }\n                          },\n                          [\n                            _vm._v(\n                              \"\\n              \" +\n                                _vm._s(agent.auditStatusText) +\n                                \"\\n            \"\n                            )\n                          ]\n                        )\n                      ],\n                      1\n                    )\n                  ]),\n                  _c(\n                    \"div\",\n                    { staticClass: \"card-actions\" },\n                    [\n                      _c(\n                        \"a-dropdown\",\n                        { attrs: { trigger: [\"click\"] } },\n                        [\n                          _c(\n                            \"a-button\",\n                            { attrs: { type: \"text\", size: \"small\" } },\n                            [_c(\"a-icon\", { attrs: { type: \"more\" } })],\n                            1\n                          ),\n                          _c(\n                            \"a-menu\",\n                            { attrs: { slot: \"overlay\" }, slot: \"overlay\" },\n                            [\n                              _c(\n                                \"a-menu-item\",\n                                {\n                                  key: \"edit\",\n                                  attrs: { disabled: !agent.editable },\n                                  on: {\n                                    click: function($event) {\n                                      return _vm.handleEdit(agent)\n                                    }\n                                  }\n                                },\n                                [\n                                  _c(\"a-icon\", { attrs: { type: \"edit\" } }),\n                                  _vm._v(\n                                    \"\\n                编辑\\n              \"\n                                  )\n                                ],\n                                1\n                              ),\n                              _c(\n                                \"a-menu-item\",\n                                {\n                                  key: \"delete\",\n                                  attrs: { disabled: !agent.deletable },\n                                  on: {\n                                    click: function($event) {\n                                      return _vm.handleDelete(agent)\n                                    }\n                                  }\n                                },\n                                [\n                                  _c(\"a-icon\", { attrs: { type: \"delete\" } }),\n                                  _vm._v(\n                                    \"\\n                删除\\n              \"\n                                  )\n                                ],\n                                1\n                              )\n                            ],\n                            1\n                          )\n                        ],\n                        1\n                      )\n                    ],\n                    1\n                  )\n                ]),\n                _c(\"div\", { staticClass: \"card-content\" }, [\n                  _c(\n                    \"div\",\n                    {\n                      staticClass: \"agent-description\",\n                      attrs: { title: agent.agentDescription }\n                    },\n                    [\n                      _c(\"p\", [\n                        _vm._v(_vm._s(agent.agentDescription || \"暂无描述\"))\n                      ])\n                    ]\n                  ),\n                  _c(\"div\", { staticClass: \"agent-stats\" }, [\n                    _c(\"div\", { staticClass: \"stat-item\" }, [\n                      _c(\"span\", { staticClass: \"stat-label\" }, [\n                        _vm._v(\"价格\")\n                      ]),\n                      _c(\"span\", { staticClass: \"stat-value price\" }, [\n                        _vm._v(\"¥\" + _vm._s(agent.price || 0))\n                      ])\n                    ]),\n                    _c(\"div\", { staticClass: \"stat-item\" }, [\n                      _c(\"span\", { staticClass: \"stat-label\" }, [\n                        _vm._v(\"工作流\")\n                      ]),\n                      _c(\"span\", { staticClass: \"stat-value\" }, [\n                        _vm._v(_vm._s(agent.workflowCount || 0))\n                      ])\n                    ]),\n                    agent.auditStatus === \"2\"\n                      ? _c(\"div\", { staticClass: \"stat-item\" }, [\n                          _c(\"span\", { staticClass: \"stat-label\" }, [\n                            _vm._v(\"销售\")\n                          ]),\n                          _c(\"span\", { staticClass: \"stat-value\" }, [\n                            _vm._v(_vm._s(agent.salesCount || 0))\n                          ])\n                        ])\n                      : _vm._e()\n                  ]),\n                  agent.auditStatus === \"2\"\n                    ? _c(\"div\", { staticClass: \"stat-item revenue\" }, [\n                        _c(\"div\", { staticClass: \"stat-content\" }, [\n                          _c(\"span\", { staticClass: \"stat-label\" }, [\n                            _vm._v(\"总收益\")\n                          ]),\n                          _c(\"span\", { staticClass: \"stat-value revenue\" }, [\n                            _vm._v(\n                              \"¥\" + _vm._s(_vm.formatMoney(agent.totalRevenue))\n                            )\n                          ])\n                        ])\n                      ])\n                    : _vm._e(),\n                  agent.auditStatus === \"3\" && agent.auditRemark\n                    ? _c(\"div\", { staticClass: \"audit-remark\" }, [\n                        _c(\"div\", { staticClass: \"remark-label\" }, [\n                          _vm._v(\"拒绝原因：\")\n                        ]),\n                        _c(\"div\", { staticClass: \"remark-content\" }, [\n                          _vm._v(_vm._s(agent.auditRemark))\n                        ])\n                      ])\n                    : _vm._e()\n                ]),\n                _c(\"div\", { staticClass: \"card-footer\" }, [\n                  _c(\"div\", { staticClass: \"create-time\" }, [\n                    _vm._v(\n                      \"\\n          创建时间：\" +\n                        _vm._s(_vm.formatDate(agent.createTime)) +\n                        \"\\n        \"\n                    )\n                  ]),\n                  agent.experienceLink\n                    ? _c(\"div\", { staticClass: \"experience-link\" }, [\n                        _c(\n                          \"a\",\n                          {\n                            staticClass: \"link-button\",\n                            attrs: {\n                              href: agent.experienceLink,\n                              target: \"_blank\"\n                            }\n                          },\n                          [\n                            _c(\"a-icon\", { attrs: { type: \"link\" } }),\n                            _vm._v(\"\\n            体验链接\\n          \")\n                          ],\n                          1\n                        )\n                      ])\n                    : _vm._e()\n                ])\n              ]\n            )\n          }),\n          0\n        )\n      : _vm._e(),\n    _vm.loading\n      ? _c(\n          \"div\",\n          { staticClass: \"loading-container\" },\n          [\n            _c(\"a-spin\", { attrs: { size: \"large\" } }, [\n              _c(\"div\", { staticClass: \"loading-text\" }, [_vm._v(\"加载中...\")])\n            ])\n          ],\n          1\n        )\n      : _vm._e(),\n    !_vm.loading && _vm.agents.length === 0\n      ? _c(\n          \"div\",\n          { staticClass: \"empty-container\" },\n          [\n            _c(\n              \"a-empty\",\n              { attrs: { description: \"暂无智能体\", image: _vm.emptyImage } },\n              [\n                _c(\n                  \"a-button\",\n                  {\n                    attrs: { type: \"primary\" },\n                    on: { click: _vm.handleCreate }\n                  },\n                  [\n                    _c(\"a-icon\", { attrs: { type: \"plus\" } }),\n                    _vm._v(\"\\n        创建第一个智能体\\n      \")\n                  ],\n                  1\n                )\n              ],\n              1\n            )\n          ],\n          1\n        )\n      : _vm._e(),\n    !_vm.loading && _vm.agents.length > 0\n      ? _c(\"div\", { staticClass: \"pagination-container\" }, [\n          _c(\n            \"div\",\n            { staticClass: \"pagination-wrapper\" },\n            [\n              _c(\"a-pagination\", {\n                staticClass: \"custom-pagination\",\n                attrs: {\n                  current: _vm.pagination.current,\n                  total: _vm.pagination.total,\n                  \"page-size\": _vm.pagination.pageSize,\n                  \"page-size-options\": [\"12\", \"24\", \"36\", \"48\"],\n                  \"show-size-changer\": true,\n                  \"show-quick-jumper\": true,\n                  \"show-total\": function(total, range) {\n                    return (\n                      \"第 \" +\n                      range[0] +\n                      \"-\" +\n                      range[1] +\n                      \" 条，共 \" +\n                      total +\n                      \" 条\"\n                    )\n                  }\n                },\n                on: {\n                  change: _vm.handlePageChange,\n                  showSizeChange: _vm.handlePageSizeChange\n                },\n                scopedSlots: _vm._u(\n                  [\n                    {\n                      key: \"buildOptionText\",\n                      fn: function(props) {\n                        return [\n                          _c(\"span\", [_vm._v(_vm._s(props.value) + \"条/页\")])\n                        ]\n                      }\n                    }\n                  ],\n                  null,\n                  false,\n                  198454938\n                )\n              })\n            ],\n            1\n          )\n        ])\n      : _vm._e()\n  ])\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"]}