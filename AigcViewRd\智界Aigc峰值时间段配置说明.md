# 智界Aigc 峰值时间段配置说明

## 🕐 峰值时间段概述

智界Aigc API支持峰值时间段配置，在指定时间段内为用户提供更高的API调用频率限制，以应对业务高峰期的访问需求。

## ⏰ 峰值时间段设置

### 默认配置
```yaml
峰值时间段: 9:00 - 18:00 (工作时间)
峰值倍数: 2.0倍
周末启用: 否
节假日启用: 是
```

### 计算公式
```
峰值限制 = 基础限制 + 额外配额
```

## 📊 频率限制对比表

### 普通用户（会员等级1）
| 时间段 | 每分钟限制 | 每小时限制 | 计算方式 |
|--------|------------|------------|----------|
| 非峰值时间 | 60次 | 3,000次 | 基础限制 |
| 峰值时间 | **100次** | **5,000次** | 60+40 / 3,000+2,000 |
| **提升** | **+67%** | **+67%** | 峰值时间段适度提升 |

### VIP用户（会员等级2）
| 时间段 | 每分钟限制 | 每小时限制 | 计算方式 |
|--------|------------|------------|----------|
| 非峰值时间 | 120次 | 6,000次 | 基础限制 |
| 峰值时间 | **200次** | **10,000次** | 120+80 / 6,000+4,000 |
| **提升** | **+67%** | **+67%** | 峰值时间段适度提升 |

### SVIP用户（会员等级3）
| 时间段 | 每分钟限制 | 每小时限制 | 计算方式 |
|--------|------------|------------|----------|
| 非峰值时间 | 300次 | 15,000次 | 基础限制 |
| 峰值时间 | **500次** | **25,000次** | 300+200 / 15,000+10,000 |
| **提升** | **+67%** | **+67%** | 峰值时间段适度提升 |

## 🎯 峰值时间段优势

### 1. 业务高峰支持
- **工作时间**：9:00-18:00 提供最高性能
- **访问量激增**：自动应对突发访问需求
- **用户体验**：减少频率限制触发

### 2. 智能时间管理
- **工作日优化**：工作时间提供更高配额
- **周末保护**：非工作时间保持基础限制
- **节假日支持**：特殊时期保持高性能

### 3. 会员差异化
- **普通用户**：峰值时间100次/分钟
- **VIP用户**：峰值时间200次/分钟
- **SVIP用户**：峰值时间500次/分钟

## ⚙️ 配置参数详解

### application-dev.yml 配置
```yaml
aigc:
  api:
    rate-limit:
      peak-hours:
        enabled: true                    # 启用峰值时间段
        start-hour: 9                    # 开始时间 9:00
        end-hour: 18                     # 结束时间 18:00
        multiplier: 1.0                  # 峰值倍数（不使用倍数）
        bonus-requests-per-minute: 40    # 普通用户额外配额/分钟 (60+40=100)
        bonus-requests-per-hour: 2000    # 普通用户额外配额/小时 (3000+2000=5000)
        vip-bonus-requests-per-minute: 80     # VIP额外配额/分钟 (120+80=200)
        vip-bonus-requests-per-hour: 4000     # VIP额外配额/小时 (6000+4000=10000)
        svip-bonus-requests-per-minute: 200   # SVIP额外配额/分钟 (300+200=500)
        svip-bonus-requests-per-hour: 10000   # SVIP额外配额/小时 (15000+10000=25000)
        enable-on-weekends: false        # 周末不启用
        enable-on-holidays: true         # 节假日启用
```

### 参数说明
- **enabled**: 是否启用峰值时间段功能
- **start-hour/end-hour**: 峰值时间段范围（24小时制）
- **multiplier**: 基础限制的倍数
- **bonus-requests-***: 额外增加的请求配额
- **enable-on-weekends**: 周末是否启用峰值
- **enable-on-holidays**: 节假日是否启用峰值

## 🔍 监控接口

### 1. 峰值时间段配置查询
```bash
GET /api/aigc/peak-hours-info
```

**响应示例**：
```json
{
  "success": true,
  "result": {
    "enabled": true,
    "startHour": 9,
    "endHour": 18,
    "multiplier": 2.0,
    "currentlyInPeakHours": true,
    "description": "峰值时间段: 09:00 - 18:00 (倍数: 2.0x, 周末: 禁用)",
    "rateLimits": {
      "普通用户": {
        "normalPerMinute": 300,
        "peakPerMinute": 800
      }
    }
  }
}
```

### 2. 用户频率限制状态查询
```bash
GET /api/aigc/user-rate-limit-status?apiKey=ak_xxx
```

**响应示例**：
```json
{
  "success": true,
  "result": {
    "userId": "1400726588175749122",
    "memberLevel": 2,
    "currentLimits": {
      "perMinute": 1600,
      "perHour": 50000,
      "isPeakHours": true
    },
    "baseLimits": {
      "perMinute": 600,
      "perHour": 20000
    }
  }
}
```

## 📈 性能提升效果

### 峰值时间段性能对比
| 用户类型 | 基础限制/分钟 | 峰值限制/分钟 | 提升倍数 | 额外支持并发 |
|---------|---------------|---------------|----------|-------------|
| 普通用户 | 60次 | 100次 | 1.67倍 | +40次/分钟 |
| VIP用户 | 120次 | 200次 | 1.67倍 | +80次/分钟 |
| SVIP用户 | 300次 | 500次 | 1.67倍 | +200次/分钟 |

### 系统总体性能
- **峰值时间总容量**：合理控制，确保系统稳定
- **非峰值时间**：保持稳定的基础性能
- **自动切换**：无需人工干预，自动识别时间段

## 🎛️ 管理建议

### 1. 峰值时间调整
根据业务特点调整峰值时间段：
```yaml
# 电商业务（晚间高峰）
start-hour: 19
end-hour: 23

# 金融业务（交易时间）
start-hour: 9
end-hour: 15

# 全球业务（24小时）
start-hour: 0
end-hour: 24
```

### 2. 倍数优化
根据服务器性能调整倍数：
```yaml
# 高性能服务器
multiplier: 3.0

# 标准服务器
multiplier: 2.0

# 资源受限
multiplier: 1.5
```

### 3. 监控告警
- 峰值时间段使用率监控
- 服务器负载监控
- 用户体验指标监控

## 🚀 最佳实践

### 1. 客户端适配
```javascript
// 客户端应根据峰值时间段调整请求策略
function getOptimalRequestRate() {
    const hour = new Date().getHours();
    const isPeakHours = hour >= 9 && hour < 18;
    
    return isPeakHours ? 
        { maxConcurrent: 50, interval: 100 } :  // 峰值时间
        { maxConcurrent: 20, interval: 200 };   // 非峰值时间
}
```

### 2. 业务规划
- 重要任务安排在峰值时间段
- 批量处理避开峰值时间
- 定期分析使用模式

### 3. 成本优化
- 根据实际使用情况调整配置
- 避免过度配置造成资源浪费
- 定期评估峰值时间段效果

---

**配置版本**: V1.0  
**更新时间**: 2025-06-14  
**维护团队**: 智界Aigc开发组
