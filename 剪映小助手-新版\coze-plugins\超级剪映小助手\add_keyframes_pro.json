{"openapi": "3.0.0", "info": {"title": "剪映小助手_超级剪映小助手 - 批量添加关键帧", "description": "批量添加关键帧", "version": "1.0.0"}, "servers": [{"url": "https://www.aigcview.com"}], "paths": {"/jeecg-boot/api/jianyingpro/add_keyframes": {"post": {"summary": "批量添加关键帧", "description": "批量添加关键帧", "operationId": "add_keyframes_pro", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"access_key": {"type": "string", "description": "访问密钥（必填）", "example": "JianyingAPI_2025_SecureAccess_AigcView", "default": "JianyingAPI_2025_SecureAccess_AigcView"}, "draft_url": {"type": "string", "description": "草稿地址，使用create_draft输出的draft_url即可（必填）", "example": "https://example.com/draft/123"}, "ctype": {"type": "string", "description": "关键帧类型： KFTypePositionX: X轴移动，值会被除以对应片段的width进行归一化 KFTypePositionY: Y轴移动，值会被除以对应片段的height进行归一化 KFTypeRotation: 旋转角度，值范围必须在0-360度之间 KFTypeScaleX: 均匀缩放，值范围必须在0.01-5之间 KFTypeAlpha: 透明度，值范围必须在0-1之间", "enum": ["KFTypePositionX", "KFTypePositionY", "KFTypeRotation", "KFTypeScaleX", "KFTypeAlpha"], "example": "KFTypePositionX"}, "offsets": {"type": "string", "description": "需要放置关键帧的位置比例，eg：0|100 这个就是代表在开始和结尾放置，0|50|100代表在开头，中间，结尾放置3个关键帧", "example": "0|100"}, "segment_infos": {"type": "array", "description": "轨道数据，add_images_pro或add_videos_pro节点输出的segment_infos即可（必填）。现在包含每个片段的尺寸信息，用于精确的坐标归一化", "items": {"type": "object", "properties": {"segment_id": {"type": "string", "description": "段ID"}, "start": {"type": "integer", "description": "开始时间（微秒）"}, "end": {"type": "integer", "description": "结束时间（微秒）"}, "duration": {"type": "integer", "description": "持续时间（微秒）"}, "width": {"type": "integer", "description": "片段素材宽度（用于X坐标归一化）"}, "height": {"type": "integer", "description": "片段素材高度（用于Y坐标归一化）"}}}, "example": [{"segment_id": "bf50e7c7-d60d-49f4-b9d6-09095ed3988f", "start": 0, "end": 5000000, "duration": 5000000, "width": 1024, "height": 1024}]}, "values": {"type": "string", "description": "对应offsets的值，长度要一致，比如1|2，或者1|2|1", "example": "1|2"}}, "required": ["access_key", "draft_url", "ctype", "offsets", "segment_infos", "values"], "additionalProperties": false}}}}, "responses": {"200": {"description": "成功添加关键帧", "content": {"application/json": {"schema": {"type": "object", "properties": {"draft_url": {"type": "string", "description": "更新后的草稿地址"}, "message": {"type": "string", "description": "导入指南信息", "example": "不知道导入的请看：https://www.aigcview.com/JianYingDraft?draft=https://example.com/draft.json"}}, "required": ["draft_url", "message"]}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "description": "错误信息", "example": "参数不完整: 草稿地址不能为空"}, "error_code": {"type": "string", "description": "错误码", "example": "PARAM_INCOMPLETE_003"}, "error_message": {"type": "string", "description": "详细错误消息", "example": "参数不完整"}, "error_details": {"type": "string", "description": "错误解决方案", "example": "请提供有效的draft_url参数"}}, "required": ["error", "error_code"]}}}}, "500": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "description": "错误信息", "example": "批量添加关键帧失败: 服务器内部错误"}}, "required": ["error"]}}}}}}}}}