[{"ClassName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Super": "ScriptComponent", "FilePath": "lua/LumiFamily/LumiManager.lua", "FileAbsPath": "/Users/<USER>/Desktop/立体反转/0/立体反转4/AmazingFeature/lua/LumiFamily/LumiManager.lua", "Properties": [{"VarName": "debugTime", "VarType": "Double", "Comment": "", "AnnoItems": [{"ItemType": "UI", "Attributes": [{"AttrType": "Range", "RawValue": "", "Values": [0.0, 6.0]}, {"AttrType": "Drag", "RawValue": "", "Values": []}]}]}, {"VarName": "autoPlay", "VarType": "Bool", "Comment": ""}, {"VarName": "lumiEffectRoot", "VarType": "Transform", "Comment": ""}, {"VarName": "start_render_layer", "VarType": "Int64", "Comment": ""}, {"VarName": "start_render_order", "VarType": "Int64", "Comment": ""}], "Methods": [{"FuncName": "new", "Params": [], "Comment": ""}, {"FuncName": "onStart", "Params": [], "Comment": ""}, {"FuncName": "onDestroy", "Params": [], "Comment": ""}, {"FuncName": "getCameraCount", "Params": [], "Comment": "", "AnnoItems": [{"ItemType": "UI", "Attributes": [{"AttrType": "<PERSON><PERSON>", "RawValue": "", "Values": ["Re<PERSON><PERSON>"]}]}]}, {"FuncName": "Re<PERSON><PERSON>", "Params": [], "Comment": ""}, {"FuncName": "registerLumiObj", "Params": [], "Comment": ""}, {"FuncName": "updateCameraLayerAndOrder", "Params": [], "Comment": ""}, {"FuncName": "updateRtPingpong", "Params": [], "Comment": ""}, {"FuncName": "updateOutputRtSize", "Params": [], "Comment": ""}, {"FuncName": "initVideoAnimationLua", "Params": [], "Comment": ""}, {"FuncName": "onUpdate", "Params": [], "Comment": ""}, {"FuncName": "onEvent", "Params": [], "Comment": ""}], "Comment": "", "ExportFiles": []}, {"ClassName": "LumiEffect", "Super": "ScriptComponent", "FilePath": "lua/LumiFamily/LumiEffect.lua", "FileAbsPath": "/Users/<USER>/Desktop/立体反转/0/立体反转4/AmazingFeature/lua/LumiFamily/LumiEffect.lua", "Properties": [], "Methods": [{"FuncName": "new", "Params": [], "Comment": ""}, {"FuncName": "constructor", "Params": [], "Comment": ""}, {"FuncName": "onStart", "Params": [], "Comment": ""}, {"FuncName": "onDestroy", "Params": [], "Comment": ""}, {"FuncName": "updateMaterials", "Params": [], "Comment": ""}, {"FuncName": "onUpdate", "Params": [], "Comment": ""}], "Comment": "", "ExportFiles": []}, {"ClassName": "ScriptCompContrast", "Super": "ScriptComponent", "FilePath": "effects/LumiContrast/lua/ScriptCompContrast.lua", "FileAbsPath": "/Users/<USER>/Desktop/立体反转/0/立体反转4/AmazingFeature/effects/LumiContrast/lua/ScriptCompContrast.lua", "Properties": [{"VarName": "contrastIntensity", "VarType": "Double", "Comment": "", "AnnoItems": [{"ItemType": "UI", "Attributes": [{"AttrType": "Range", "RawValue": "", "Values": [0.0, 2.0]}, {"AttrType": "Drag", "RawValue": "", "Values": []}]}]}, {"VarName": "pivot", "VarType": "Double", "Comment": "", "AnnoItems": [{"ItemType": "UI", "Attributes": [{"AttrType": "Range", "RawValue": "", "Values": [0.0, 1.0]}, {"AttrType": "Drag", "RawValue": "", "Values": []}]}]}, {"VarName": "InputTex", "VarType": "Texture", "Comment": ""}, {"VarName": "OutputTex", "VarType": "Texture", "Comment": ""}], "Methods": [{"FuncName": "new", "Params": [], "Comment": ""}, {"FuncName": "setEffectAttr", "Params": [], "Comment": ""}, {"FuncName": "onStart", "Params": [], "Comment": ""}, {"FuncName": "onUpdate", "Params": [], "Comment": ""}], "Comment": "", "ExportFiles": []}, {"ClassName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Super": "ScriptComponent", "FilePath": "effects/LumiLayer/lua/LumiLayer.lua", "FileAbsPath": "/Users/<USER>/Desktop/立体反转/0/立体反转4/AmazingFeature/effects/LumiLayer/lua/LumiLayer.lua", "Properties": [{"VarName": "hasTransform", "VarType": "Bool", "Comment": ""}, {"VarName": "active_cam_fovx", "VarType": "Double", "Comment": ""}, {"VarName": "compositeSize", "VarType": "Vector2f", "Comment": ""}, {"VarName": "layerSize", "VarType": "Vector2f", "Comment": ""}, {"VarName": "anchorPoint", "VarType": "Vector3f", "Comment": ""}, {"VarName": "orientation", "VarType": "Vector3f", "Comment": ""}, {"VarName": "position", "VarType": "Vector3f", "Comment": ""}, {"VarName": "scale", "VarType": "Vector3f", "Comment": ""}, {"VarName": "xRotation", "VarType": "Double", "Comment": "", "AnnoItems": [{"ItemType": "UI", "Attributes": [{"AttrType": "Range", "RawValue": "", "Values": [-360.0, 360.0]}, {"AttrType": "Drag", "RawValue": "", "Values": []}]}]}, {"VarName": "yRotation", "VarType": "Double", "Comment": "", "AnnoItems": [{"ItemType": "UI", "Attributes": [{"AttrType": "Range", "RawValue": "", "Values": [-360.0, 360.0]}, {"AttrType": "Drag", "RawValue": "", "Values": []}]}]}, {"VarName": "rotation", "VarType": "Double", "Comment": "", "AnnoItems": [{"ItemType": "UI", "Attributes": [{"AttrType": "Range", "RawValue": "", "Values": [-360.0, 360.0]}, {"AttrType": "Drag", "RawValue": "", "Values": []}]}]}, {"VarName": "opacity", "VarType": "Double", "Comment": "", "AnnoItems": [{"ItemType": "UI", "Attributes": [{"AttrType": "Range", "RawValue": "", "Values": [0.0, 100.0]}, {"AttrType": "Drag", "RawValue": "", "Values": []}]}]}, {"VarName": "mirrorEdge", "VarType": "Bool", "Comment": ""}, {"VarName": "hasMatte", "VarType": "Bool", "Comment": ""}, {"VarName": "matteMode", "VarType": "String", "Comment": "", "AnnoItems": [{"ItemType": "UI", "Attributes": [{"AttrType": "Option", "RawValue": "", "Values": ["Alpha", "<PERSON><PERSON>", "AlphaInverted", "LumaInverted"]}]}]}, {"VarName": "maskTex", "VarType": "Texture", "Comment": ""}, {"VarName": "hasBlend", "VarType": "Bool", "Comment": ""}, {"VarName": "blendMode", "VarType": "String", "Comment": "", "AnnoItems": [{"ItemType": "UI", "Attributes": [{"AttrType": "Option", "RawValue": "", "Values": ["Normal", "Add", "Multiply", "Difference", "Overlay", "Darken", "Lighten", "SoftLight", "HardLight", "<PERSON><PERSON>", "Saturation", "Color", "Screen", "ColorBurn", "LinearBurn", "ColorDodge", "LinearDodge", "VividLight", "LinearLight", "PinLight", "HardMix", "Exclusion", "Subtract", "Divide", "Luminosity", "LighterColor", "DarkerColor", "Dissolve"]}]}]}, {"VarName": "layerType", "VarType": "String", "Comment": "", "AnnoItems": [{"ItemType": "UI", "Attributes": [{"AttrType": "Option", "RawValue": "", "Values": ["Adjustment", "Precomp", "Solid", "Image", "Video", "Sequence"]}]}]}, {"VarName": "transformX", "VarType": "Double", "Comment": ""}, {"VarName": "boolPosX", "VarType": "Bool", "Comment": ""}, {"VarName": "baseTex", "VarType": "Texture", "Comment": ""}, {"VarName": "InputTex", "VarType": "Texture", "Comment": ""}, {"VarName": "OutputTex", "VarType": "Texture", "Comment": ""}], "Methods": [{"FuncName": "new", "Params": [], "Comment": ""}, {"FuncName": "setVisible", "Params": [], "Comment": ""}, {"FuncName": "setEntityVisible", "Params": [], "Comment": ""}, {"FuncName": "onStart", "Params": [], "Comment": ""}, {"FuncName": "setEffectAttr", "Params": [], "Comment": ""}, {"FuncName": "_rotateByAEAxis", "Params": [], "Comment": ""}, {"FuncName": "updateTRS", "Params": [], "Comment": ""}, {"FuncName": "onUpdate", "Params": [], "Comment": ""}], "Comment": "", "ExportFiles": []}, {"ClassName": "Lumi3DShape", "Super": "ScriptComponent", "FilePath": "effects/Lumi3DShape/lua/Lumi3DShape.lua", "FileAbsPath": "/Users/<USER>/Desktop/立体反转/0/立体反转4/AmazingFeature/effects/Lumi3DShape/lua/Lumi3DShape.lua", "Properties": [{"VarName": "InputTex", "VarType": "Texture", "Comment": ""}, {"VarName": "OutputTex", "VarType": "Texture", "Comment": ""}, {"VarName": "fov", "VarType": "Double", "Comment": "", "AnnoItems": [{"ItemType": "UI", "Attributes": [{"AttrType": "Range", "RawValue": "", "Values": [0.0, 180.0]}, {"AttrType": "Drag", "RawValue": "", "Values": []}]}]}, {"VarName": "position", "VarType": "Vector3f", "Comment": ""}, {"VarName": "scale", "VarType": "Vector3f", "Comment": ""}, {"VarName": "rotate", "VarType": "Vector3f", "Comment": ""}, {"VarName": "meshSize", "VarType": "Vector3f", "Comment": ""}, {"VarName": "meshResX", "VarType": "Double", "Comment": "", "AnnoItems": [{"ItemType": "UI", "Attributes": [{"AttrType": "Range", "RawValue": "", "Values": [1.0, 100.0]}, {"AttrType": "Drag", "RawValue": "", "Values": [1.0]}]}]}, {"VarName": "meshResY", "VarType": "Double", "Comment": "", "AnnoItems": [{"ItemType": "UI", "Attributes": [{"AttrType": "Range", "RawValue": "", "Values": [1.0, 100.0]}, {"AttrType": "Drag", "RawValue": "", "Values": [1.0]}]}]}, {"VarName": "meshResZ", "VarType": "Double", "Comment": "", "AnnoItems": [{"ItemType": "UI", "Attributes": [{"AttrType": "Range", "RawValue": "", "Values": [1.0, 100.0]}, {"AttrType": "Drag", "RawValue": "", "Values": [1.0]}]}]}, {"VarName": "reverseUV", "VarType": "Bool", "Comment": ""}, {"VarName": "cullBack", "VarType": "Bool", "Comment": ""}, {"VarName": "translucency", "VarType": "Bool", "Comment": ""}, {"VarName": "twoSide", "VarType": "Bool", "Comment": ""}, {"VarName": "meshType", "VarType": "String", "Comment": "", "AnnoItems": [{"ItemType": "UI", "Attributes": [{"AttrType": "Option", "RawValue": "", "Values": ["quad", "ellipse", "ring", "cube", "sphere", "cylinder", "donut"]}]}]}, {"VarName": "texFillMode", "VarType": "String", "Comment": "", "AnnoItems": [{"ItemType": "UI", "Attributes": [{"AttrType": "Option", "RawValue": "", "Values": ["stretch", "fill", "fit", "repeat&stretch", "repeat&fill"]}]}]}, {"VarName": "uvScaleX", "VarType": "Double", "Comment": "", "AnnoItems": [{"ItemType": "UI", "Attributes": [{"AttrType": "Range", "RawValue": "", "Values": [0.01, 100.0]}, {"AttrType": "Drag", "RawValue": "", "Values": []}]}]}, {"VarName": "uvScaleY", "VarType": "Double", "Comment": "", "AnnoItems": [{"ItemType": "UI", "Attributes": [{"AttrType": "Range", "RawValue": "", "Values": [0.01, 100.0]}, {"AttrType": "Drag", "RawValue": "", "Values": []}]}]}, {"VarName": "uvWrapMode", "VarType": "String", "Comment": "", "AnnoItems": [{"ItemType": "UI", "Attributes": [{"AttrType": "Option", "RawValue": "", "Values": ["clamp", "repeat", "mirror", "black", "white", "translucent"]}]}]}, {"VarName": "enableMask", "VarType": "Bool", "Comment": ""}, {"VarName": "boolResult", "VarType": "Bool", "Comment": ""}, {"VarName": "boolcube", "VarType": "Bool", "Comment": ""}, {"VarName": "mask", "VarType": "Texture", "Comment": ""}, {"VarName": "maskChannel", "VarType": "String", "Comment": "", "AnnoItems": [{"ItemType": "UI", "Attributes": [{"AttrType": "Option", "RawValue": "", "Values": ["R", "G", "B", "A"]}]}]}], "Methods": [{"FuncName": "new", "Params": [], "Comment": ""}, {"FuncName": "constructor", "Params": [], "Comment": ""}, {"FuncName": "onStart", "Params": [], "Comment": ""}, {"FuncName": "updateMat", "Params": [], "Comment": ""}, {"FuncName": "rotateByAEAxis", "Params": [], "Comment": ""}, {"FuncName": "onUpdate", "Params": [], "Comment": ""}, {"FuncName": "setEffectAttr", "Params": [], "Comment": ""}], "Comment": "", "ExportFiles": []}, {"ClassName": "ScriptCompExposure", "Super": "ScriptComponent", "FilePath": "effects/LumiExposure/lua/ScriptCompExposure.lua", "FileAbsPath": "/Users/<USER>/Desktop/立体反转/0/立体反转4/AmazingFeature/effects/LumiExposure/lua/ScriptCompExposure.lua", "Properties": [{"VarName": "channelType", "VarType": "String", "Comment": "", "AnnoItems": [{"ItemType": "UI", "Attributes": [{"AttrType": "Option", "RawValue": "", "Values": ["Main Channels", "Single Channel"]}]}]}, {"VarName": "exposure", "VarType": "Double", "Comment": "", "AnnoItems": [{"ItemType": "UI", "Attributes": [{"AttrType": "Range", "RawValue": "", "Values": [-10.0, 10.0]}, {"AttrType": "Drag", "RawValue": "", "Values": []}]}]}, {"VarName": "offset", "VarType": "Double", "Comment": "", "AnnoItems": [{"ItemType": "UI", "Attributes": [{"AttrType": "Range", "RawValue": "", "Values": [-2.0, 2.0]}, {"AttrType": "Drag", "RawValue": "", "Values": []}]}]}, {"VarName": "grayscaleCorrection", "VarType": "Double", "Comment": "", "AnnoItems": [{"ItemType": "UI", "Attributes": [{"AttrType": "Range", "RawValue": "", "Values": [0.1, 10.0]}, {"AttrType": "Drag", "RawValue": "", "Values": []}]}]}, {"VarName": "redExposure", "VarType": "Double", "Comment": "", "AnnoItems": [{"ItemType": "UI", "Attributes": [{"AttrType": "Range", "RawValue": "", "Values": [-10.0, 10.0]}, {"AttrType": "Drag", "RawValue": "", "Values": []}]}]}, {"VarName": "redOffset", "VarType": "Double", "Comment": "", "AnnoItems": [{"ItemType": "UI", "Attributes": [{"AttrType": "Range", "RawValue": "", "Values": [-2.0, 2.0]}, {"AttrType": "Drag", "RawValue": "", "Values": []}]}]}, {"VarName": "redGrayscaleCorrection", "VarType": "Double", "Comment": "", "AnnoItems": [{"ItemType": "UI", "Attributes": [{"AttrType": "Range", "RawValue": "", "Values": [0.1, 10.0]}, {"AttrType": "Drag", "RawValue": "", "Values": []}]}]}, {"VarName": "greenExposure", "VarType": "Double", "Comment": "", "AnnoItems": [{"ItemType": "UI", "Attributes": [{"AttrType": "Range", "RawValue": "", "Values": [-10.0, 10.0]}, {"AttrType": "Drag", "RawValue": "", "Values": []}]}]}, {"VarName": "greenOffset", "VarType": "Double", "Comment": "", "AnnoItems": [{"ItemType": "UI", "Attributes": [{"AttrType": "Range", "RawValue": "", "Values": [-2.0, 2.0]}, {"AttrType": "Drag", "RawValue": "", "Values": []}]}]}, {"VarName": "greenGrayscaleCorrection", "VarType": "Double", "Comment": "", "AnnoItems": [{"ItemType": "UI", "Attributes": [{"AttrType": "Range", "RawValue": "", "Values": [0.1, 10.0]}, {"AttrType": "Drag", "RawValue": "", "Values": []}]}]}, {"VarName": "blueExposure", "VarType": "Double", "Comment": "", "AnnoItems": [{"ItemType": "UI", "Attributes": [{"AttrType": "Range", "RawValue": "", "Values": [-10.0, 10.0]}, {"AttrType": "Drag", "RawValue": "", "Values": []}]}]}, {"VarName": "blueOffset", "VarType": "Double", "Comment": "", "AnnoItems": [{"ItemType": "UI", "Attributes": [{"AttrType": "Range", "RawValue": "", "Values": [-2.0, 2.0]}, {"AttrType": "Drag", "RawValue": "", "Values": []}]}]}, {"VarName": "blueGrayscaleCorrection", "VarType": "Double", "Comment": "", "AnnoItems": [{"ItemType": "UI", "Attributes": [{"AttrType": "Range", "RawValue": "", "Values": [0.1, 10.0]}, {"AttrType": "Drag", "RawValue": "", "Values": []}]}]}, {"VarName": "noUseLinearLight", "VarType": "Bool", "Comment": ""}, {"VarName": "InputTex", "VarType": "Texture", "Comment": ""}, {"VarName": "OutputTex", "VarType": "Texture", "Comment": ""}], "Methods": [{"FuncName": "new", "Params": [], "Comment": ""}, {"FuncName": "setEffectAttr", "Params": [], "Comment": ""}, {"FuncName": "onStart", "Params": [], "Comment": ""}, {"FuncName": "onUpdate", "Params": [], "Comment": ""}], "Comment": "", "ExportFiles": []}]