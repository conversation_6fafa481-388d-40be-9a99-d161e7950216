package org.jeecg.modules.demo.versioncontrol.entity;

import java.io.Serializable;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * @Description: 程序版本控制表
 * @Author: jeecg-boot
 * @Date: 2025-01-09
 * @Version: V1.0
 */
@Data
@TableName("aigc_version_control")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="aigc_version_control对象", description="程序版本控制表")
public class AigcVersionControl implements Serializable {
    private static final long serialVersionUID = 1L;

    /**主键ID*/
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键ID")
    private String id;

    /**程序类型*/
    @Excel(name = "程序类型", width = 15, dicCode = "program_type")
    @ApiModelProperty(value = "程序类型：frontend-前端,backend-后端,desktop-桌面应用,plugin-扣子插件")
    @NotBlank(message = "程序类型不能为空")
    @Size(max = 50, message = "程序类型长度不能超过50个字符")
    private String programType;

    /**版本号*/
    @Excel(name = "版本号", width = 15)
    @ApiModelProperty(value = "版本号，如1.0.0、2.1.3等")
    @NotBlank(message = "版本号不能为空")
    @Size(max = 20, message = "版本号长度不能超过20个字符")
    private String versionNumber;

    /**更新内容*/
    @Excel(name = "更新内容", width = 30)
    @ApiModelProperty(value = "更新内容详细说明")
    @Size(max = 5000, message = "更新内容长度不能超过5000个字符")
    private String updateContent;

    /**下载链接*/
    @Excel(name = "下载链接", width = 30)
    @ApiModelProperty(value = "下载链接")
    @Size(max = 500, message = "下载链接长度不能超过500个字符")
    private String downloadUrl;

    /**发布日期*/
    @Excel(name = "发布日期", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "发布日期")
    @NotNull(message = "发布日期不能为空")
    private Date releaseDate;

    /**是否最新版本*/
    @Excel(name = "是否最新版本", width = 15, dicCode = "isTrue")
    @ApiModelProperty(value = "是否最新版本：1-是，2-否")
    private Integer isLatest;

    /**状态*/
    @Excel(name = "状态", width = 15, dicCode = "valid_status")
    @ApiModelProperty(value = "状态：1-启用，0-禁用")
    private Integer status;

    /**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;

    /**创建时间*/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**更新人*/
    @ApiModelProperty(value = "更新人")
    private String updateBy;

    /**更新时间*/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    /**所属部门*/
    @ApiModelProperty(value = "所属部门")
    private String sysOrgCode;

    // ========== 虚拟字段（不存在于数据库中） ==========

    /**程序类型显示名称*/
    @TableField(exist = false)
    @ApiModelProperty(value = "程序类型显示名称")
    private String programTypeText;

    /**状态显示名称*/
    @TableField(exist = false)
    @ApiModelProperty(value = "状态显示名称")
    private String statusText;

    /**是否最新版本显示名称*/
    @TableField(exist = false)
    @ApiModelProperty(value = "是否最新版本显示名称")
    private String isLatestText;

    /**是否启用*/
    @TableField(exist = false)
    @ApiModelProperty(value = "是否启用")
    private Boolean enabled;

    /**是否最新版本*/
    @TableField(exist = false)
    @ApiModelProperty(value = "是否最新版本")
    private Boolean latestVersion;

    // 构造函数
    public AigcVersionControl() {
        this.isLatest = 2;  // 默认不是最新版本（isTrue字典：1是，2否）
        this.status = 1;    // 默认启用状态
    }

    /**
     * 获取程序类型显示名称
     * @return 程序类型显示名称
     */
    public String getProgramTypeText() {
        if (this.programTypeText != null) {
            return this.programTypeText;
        }
        if (programType == null) {
            return "";
        }
        switch (programType) {
            case "frontend":
                this.programTypeText = "前端";
                break;
            case "backend":
                this.programTypeText = "后端";
                break;
            case "desktop":
                this.programTypeText = "桌面应用";
                break;
            case "plugin":
                this.programTypeText = "扣子插件";
                break;
            default:
                this.programTypeText = programType;
        }
        return this.programTypeText;
    }

    /**
     * 获取状态显示名称
     * @return 状态显示名称
     */
    public String getStatusText() {
        if (this.statusText != null) {
            return this.statusText;
        }
        if (status == null) {
            return "";
        }
        this.statusText = status == 1 ? "启用" : "禁用";
        return this.statusText;
    }

    /**
     * 获取是否最新版本显示名称
     * @return 是否最新版本显示名称
     */
    public String getIsLatestText() {
        if (this.isLatestText != null) {
            return this.isLatestText;
        }
        if (isLatest == null) {
            return "";
        }
        this.isLatestText = isLatest == 1 ? "是" : "否";
        return this.isLatestText;
    }

    /**
     * 获取是否启用
     * @return 是否启用
     */
    public Boolean getEnabled() {
        if (this.enabled != null) {
            return this.enabled;
        }
        this.enabled = status != null && status == 1;
        return this.enabled;
    }

    /**
     * 获取是否最新版本
     * @return 是否最新版本
     */
    public Boolean getLatestVersion() {
        if (this.latestVersion != null) {
            return this.latestVersion;
        }
        this.latestVersion = isLatest != null && isLatest == 1;  // isTrue字典：1是
        return this.latestVersion;
    }

    /**
     * 设置为最新版本
     */
    public void setAsLatest() {
        this.isLatest = 1;  // isTrue字典：1是
    }

    /**
     * 设置为历史版本
     */
    public void setAsHistory() {
        this.isLatest = 2;  // isTrue字典：2否
    }

    /**
     * 启用版本
     */
    public void enable() {
        this.status = 1;
    }

    /**
     * 禁用版本
     */
    public void disable() {
        this.status = 0;
    }

    /**
     * 判断是否为最新版本
     * @return true-是最新版本，false-不是最新版本
     */
    public boolean isLatestVersion() {
        return isLatest != null && isLatest == 1;  // isTrue字典：1是
    }

    /**
     * 判断是否启用
     * @return true-启用，false-禁用
     */
    public boolean isEnabled() {
        return status != null && status == 1;
    }

    @Override
    public String toString() {
        return "AigcVersionControl{" +
                "id='" + id + '\'' +
                ", programType='" + programType + '\'' +
                ", versionNumber='" + versionNumber + '\'' +
                ", updateContent='" + updateContent + '\'' +
                ", releaseDate=" + releaseDate +
                ", isLatest=" + isLatest +
                ", status=" + status +
                ", createBy='" + createBy + '\'' +
                ", createTime=" + createTime +
                ", updateBy='" + updateBy + '\'' +
                ", updateTime=" + updateTime +
                ", sysOrgCode='" + sysOrgCode + '\'' +
                '}';
    }
}
