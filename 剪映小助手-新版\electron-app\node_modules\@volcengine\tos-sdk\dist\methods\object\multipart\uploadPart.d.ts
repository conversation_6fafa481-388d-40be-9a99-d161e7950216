/// <reference types="node" />
import TOSBase from '../../base';
import { DataTransferStatus } from '../../../interface';
import { IRateLimiter } from '../../../universal/rate-limiter';
export interface UploadPartInput {
    body: Blob | Buffer | NodeJS.ReadableStream;
    bucket?: string;
    key: string;
    partNumber: number;
    uploadId: string;
    dataTransferStatusChange?: (status: DataTransferStatus) => void;
    /**
     * the simple progress feature
     * percent is [0, 1].
     *
     * since uploadPart is stateless, so if `uploadPart` fail and you retry it,
     * `percent` will start from 0 again rather than from the previous value.
     */
    progress?: (percent: number) => void;
    /**
     * unit: bit/s
     * server side traffic limit
     **/
    trafficLimit?: number;
    /**
     * only works for nodejs environment
     */
    rateLimiter?: IRateLimiter;
    ssecAlgorithm?: string;
    ssecKey?: string;
    ssecKeyMD5?: string;
    headers?: {
        [key: string]: string | undefined;
        'content-length'?: string;
        'content-md5'?: string;
        'x-tos-server-side-encryption-customer-algorithm'?: string;
        'x-tos-server-side-encryption-customer-key'?: string;
        'x-tos-server-side-encryption-customer-key-MD5'?: string;
    };
}
export interface UploadPartInputInner extends UploadPartInput {
    makeRetryStream?: () => NodeJS.ReadableStream | undefined;
    beforeRetry?: () => void;
    /**
     * default: false
     */
    enableContentMD5?: boolean;
}
export interface UploadPartOutput {
    partNumber: number;
    ETag: string;
    ssecAlgorithm?: string;
    ssecKeyMD5?: string;
    hashCrc64ecma: string;
    serverSideEncryption?: string;
    serverSideEncryptionKeyId?: string;
    /** @private unstable */
    serverSideDataEncryption?: string;
}
export declare function _uploadPart(this: TOSBase, input: UploadPartInputInner): Promise<import("../../base").TosResponse<UploadPartOutput>>;
export declare function uploadPart(this: TOSBase, input: UploadPartInput): Promise<import("../../base").TosResponse<UploadPartOutput>>;
interface UploadPartFromFileInput extends Omit<UploadPartInput, 'body'> {
    filePath: string;
    /**
     * default: 0
     */
    offset?: number;
    /**
     * default: file size
     */
    partSize?: number;
}
export declare function uploadPartFromFile(this: TOSBase, input: UploadPartFromFileInput): Promise<import("../../base").TosResponse<UploadPartOutput>>;
export {};
