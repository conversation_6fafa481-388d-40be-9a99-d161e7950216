<template>
  <div class="quantum-jump-container" :class="{ 'visible': showButton }" ref="quantumContainer">
    <button class="quantum-jump-btn" @click="scrollToTop" ref="quantumBtn">
      <div class="btn-icon">
        <a-icon type="rocket" />
      </div>
      <div class="btn-text">量子跳跃</div>
      <div class="btn-glow"></div>
      <div class="btn-particles" ref="particles"></div>
      <div class="quantum-waves"></div>
    </button>
  </div>
</template>

<script>
import { gsap } from "gsap"
import { ScrollToPlugin } from "gsap/ScrollToPlugin"

gsap.registerPlugin(ScrollToPlugin)

export default {
  name: 'QuantumJump',
  props: {
    // 滚动多少像素后显示按钮
    showAfterScroll: {
      type: Number,
      default: 100
    },
    // 按钮位置配置
    position: {
      type: Object,
      default: () => ({
        bottom: '4rem',
        right: '2rem'
      })
    },
    // 滚动动画时长
    scrollDuration: {
      type: Number,
      default: 1.5
    }
  },
  data() {
    return {
      showButton: false
    }
  },
  mounted() {
    this.initQuantumJump()
    this.applyPosition()
  },
  beforeDestroy() {
    window.removeEventListener('scroll', this.handleScroll)
  },
  methods: {
    // 初始化量子跳跃功能
    initQuantumJump() {
      window.addEventListener('scroll', this.handleScroll)
      this.initQuantumParticles()
    },

    // 应用位置样式
    applyPosition() {
      if (this.$refs.quantumContainer) {
        const container = this.$refs.quantumContainer
        Object.keys(this.position).forEach(key => {
          container.style[key] = this.position[key]
        })
      }
    },

    // 滚动监听 - 只要往下滑就显示
    handleScroll() {
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop
      this.showButton = scrollTop > this.showAfterScroll
    },

    // 量子跳跃动画
    scrollToTop() {
      // 触发量子粒子爆发效果
      this.triggerQuantumExplosion()
      
      // 平滑滚动到顶部
      const self = this
      gsap.to(window, {
        duration: this.scrollDuration,
        scrollTo: { y: 0, autoKill: false },
        ease: "power2.out",
        onComplete: function() {
          // 量子跳跃完成后的回调
          self.onQuantumJumpComplete()
        }
      })
    },

    // 初始化量子粒子效果
    initQuantumParticles() {
      this.$nextTick(function() {
        const particlesContainer = this.$refs.particles
        if (!particlesContainer) return

        // 创建量子能量粒子
        for (let i = 0; i < 15; i++) {
          const particle = document.createElement('div')
          particle.className = 'quantum-particle'
          particle.style.cssText = 'position: absolute; width: 4px; height: 4px; background: #3b82f6; border-radius: 50%; opacity: 0; box-shadow: 0 0 6px #3b82f6;'
          particlesContainer.appendChild(particle)
        }
      })
    },

    // 量子粒子爆发效果
    triggerQuantumExplosion() {
      const particlesContainer = this.$refs.particles
      if (!particlesContainer) return
      
      const particles = particlesContainer.querySelectorAll('.quantum-particle')
      if (!particles) return

      particles.forEach(function(particle, index) {
        const angle = (index / particles.length) * Math.PI * 2
        const distance = 80 + Math.random() * 60
        const x = Math.cos(angle) * distance
        const y = Math.sin(angle) * distance

        gsap.fromTo(particle, {
          x: 0,
          y: 0,
          opacity: 1,
          scale: 0.3
        }, {
          duration: 1.2,
          x: x,
          y: y,
          opacity: 0,
          scale: 2,
          ease: "power2.out",
          delay: index * 0.03
        })
      })
    },

    // 量子跳跃完成回调
    onQuantumJumpComplete() {
      // 按钮成功动画
      gsap.to(this.$refs.quantumBtn, {
        duration: 0.4,
        scale: 1.15,
        yoyo: true,
        repeat: 1,
        ease: "power2.inOut"
      })
    }
  }
}
</script>

<style scoped>
/* 量子跳跃按钮容器 */
.quantum-jump-container {
  position: fixed;
  z-index: 999;
  opacity: 0;
  visibility: hidden;
  transform: translateY(20px);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.quantum-jump-container.visible {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

/* 量子跳跃按钮 */
.quantum-jump-btn {
  position: relative;
  width: 130px;
  height: 65px;
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 30%, #334155 70%, #475569 100%);
  border: 2px solid rgba(59, 130, 246, 0.4);
  border-radius: 32px;
  color: white;
  cursor: pointer;
  overflow: hidden;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.6rem;
  box-shadow: 
    0 6px 25px rgba(0, 0, 0, 0.4),
    0 0 25px rgba(59, 130, 246, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1),
    inset 0 -1px 0 rgba(0, 0, 0, 0.2);
}

.quantum-jump-btn:hover {
  transform: translateY(-4px);
  border-color: rgba(59, 130, 246, 0.8);
  box-shadow: 
    0 12px 40px rgba(0, 0, 0, 0.5),
    0 0 40px rgba(59, 130, 246, 0.5),
    inset 0 1px 0 rgba(255, 255, 255, 0.2),
    inset 0 -1px 0 rgba(0, 0, 0, 0.3);
}

/* 按钮图标 */
.btn-icon {
  font-size: 1.3rem;
  color: #60a5fa;
  transition: all 0.3s ease;
  z-index: 2;
}

.quantum-jump-btn:hover .btn-icon {
  color: #93c5fd;
  transform: translateY(-3px);
  animation: quantumRocket 0.8s ease-in-out;
}

@keyframes quantumRocket {
  0% { transform: translateY(0) rotate(0deg); }
  25% { transform: translateY(-6px) rotate(-15deg); }
  50% { transform: translateY(-8px) rotate(0deg); }
  75% { transform: translateY(-4px) rotate(10deg); }
  100% { transform: translateY(-3px) rotate(0deg); }
}

/* 按钮文字 */
.btn-text {
  font-size: 0.9rem;
  font-weight: 700;
  color: rgba(255, 255, 255, 0.95);
  transition: all 0.3s ease;
  z-index: 2;
  white-space: nowrap;
  letter-spacing: 0.5px;
}

.quantum-jump-btn:hover .btn-text {
  color: white;
  text-shadow: 0 0 15px rgba(59, 130, 246, 0.8);
}

/* 量子发光效果 */
.btn-glow {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.15) 0%, rgba(168, 85, 247, 0.15) 100%);
  border-radius: 32px;
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 1;
}

.quantum-jump-btn:hover .btn-glow {
  opacity: 1;
  animation: quantumPulse 2.5s ease-in-out infinite;
}

@keyframes quantumPulse {
  0%, 100% { opacity: 0.4; transform: scale(1); }
  50% { opacity: 0.8; transform: scale(1.08); }
}

/* 量子波纹效果 */
.quantum-waves {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 100%;
  height: 100%;
  transform: translate(-50%, -50%);
  border-radius: 32px;
  z-index: 0;
}

.quantum-waves::before,
.quantum-waves::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 2px solid rgba(59, 130, 246, 0.3);
  border-radius: 32px;
  opacity: 0;
}

.quantum-jump-btn:hover .quantum-waves::before {
  animation: quantumWave1 2s ease-out infinite;
}

.quantum-jump-btn:hover .quantum-waves::after {
  animation: quantumWave2 2s ease-out infinite 0.5s;
}

@keyframes quantumWave1 {
  0% { transform: scale(1); opacity: 0.6; }
  100% { transform: scale(1.5); opacity: 0; }
}

@keyframes quantumWave2 {
  0% { transform: scale(1); opacity: 0.4; }
  100% { transform: scale(1.8); opacity: 0; }
}

/* 粒子容器 */
.btn-particles {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 3;
}

.quantum-particle {
  position: absolute;
  border-radius: 50%;
  pointer-events: none;
}

/* 量子光束扫过效果 */
.quantum-jump-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    rgba(59, 130, 246, 0.3),
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.6s ease;
  z-index: 1;
}

.quantum-jump-btn:hover::before {
  left: 100%;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .quantum-jump-btn {
    width: 110px;
    height: 55px;
    border-radius: 28px;
  }
  
  .btn-icon {
    font-size: 1.1rem;
  }
  
  .btn-text {
    font-size: 0.8rem;
  }
}
</style>
