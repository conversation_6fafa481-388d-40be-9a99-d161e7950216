package org.jeecg.modules.demo.plubauthor.service.impl;

import org.jeecg.modules.demo.plubauthor.entity.AigcPlubAuthor;
import org.jeecg.modules.demo.plubauthor.mapper.AigcPlubAuthorMapper;
import org.jeecg.modules.demo.plubauthor.service.IAigcPlubAuthorService;
import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;

/**
 * @Description: 插件创作者
 * @Author: jeecg-boot
 * @Date:   2025-06-14
 * @Version: V1.0
 */
@Slf4j
@Service
public class AigcPlubAuthorServiceImpl extends ServiceImpl<AigcPlubAuthorMapper, AigcPlubAuthor> implements IAigcPlubAuthorService {

    @Override
    public boolean updateAuthorUsageCount(String authorId) {
        try {
            int result = baseMapper.updateAuthorUsageCount(authorId);
            if (result > 0) {
                log.info("更新插件创作者使用总数成功，创作者ID: {}", authorId);
                return true;
            } else {
                log.warn("更新插件创作者使用总数失败，创作者ID: {}", authorId);
                return false;
            }
        } catch (Exception e) {
            log.error("更新插件创作者使用总数异常: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean updateAuthorPluginCount(String authorId) {
        try {
            int result = baseMapper.updateAuthorPluginCount(authorId);
            if (result > 0) {
                log.info("更新插件创作者插件数成功，创作者ID: {}", authorId);
                return true;
            } else {
                log.warn("更新插件创作者插件数失败，创作者ID: {}", authorId);
                return false;
            }
        } catch (Exception e) {
            log.error("更新插件创作者插件数异常: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public int updateAllAuthorPluginCounts() {
        try {
            int result = baseMapper.updateAllAuthorPluginCounts();
            log.info("批量更新所有作者插件数完成，更新数量: {}", result);
            return result;
        } catch (Exception e) {
            log.error("批量更新所有作者插件数异常: {}", e.getMessage(), e);
            return 0;
        }
    }

    @Override
    public boolean updateAuthorTotalIncome(String authorId, java.math.BigDecimal amount) {
        try {
            int result = baseMapper.updateAuthorTotalIncome(authorId, amount);
            if (result > 0) {
                log.info("更新插件创作者累计收益成功，创作者ID: {}, 收益金额: {}", authorId, amount);
                return true;
            } else {
                log.warn("更新插件创作者累计收益失败，创作者ID: {}", authorId);
                return false;
            }
        } catch (Exception e) {
            log.error("更新插件创作者累计收益异常: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public AigcPlubAuthor getAuthorWithDictText(String authorId) {
        try {
            // 使用QueryWrapper查询，这样会触发字典转换AOP
            QueryWrapper<AigcPlubAuthor> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("id", authorId);
            AigcPlubAuthor author = this.getOne(queryWrapper);

            if (author != null) {
                log.info("获取作者信息成功，作者ID: {}, 作者名称: {}", authorId, author.getAuthorname());
            } else {
                log.warn("未找到作者信息，作者ID: {}", authorId);
            }

            return author;
        } catch (Exception e) {
            log.error("获取作者信息异常，作者ID: {}, 错误: {}", authorId, e.getMessage(), e);
            return null;
        }
    }

}
