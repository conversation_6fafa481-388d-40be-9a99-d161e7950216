<template>
  <div class="agent-management">
    <!-- 筛选和搜索 -->
    <div class="market-filters">
      <div class="filter-row">
        <!-- 搜索框 -->
        <div class="search-box">
          <div class="search-wrapper">
            <a-icon type="search" class="search-icon" />
            <a-input
              v-model="searchQuery"
              placeholder="搜索智能体名称、描述..."
              size="large"
              @pressEnter="handleSearch"
              @input="handleSearch"
              class="search-input"
            />
            <a-icon
              v-if="searchQuery"
              type="close-circle"
              class="clear-icon"
              @click="clearSearch"
            />
          </div>
        </div>

        <!-- 筛选器 -->
        <div class="filter-controls">
          <!-- 审核状态筛选 -->
          <div class="filter-group">
            <div class="filter-buttons">
              <button
                class="filter-btn"
                :class="{ 'active': auditStatusFilter === '' }"
                @click="setAuditStatusFilter('')"
              >
                <a-icon type="appstore" />
                全部
              </button>
              <button
                class="filter-btn pending"
                :class="{ 'active': auditStatusFilter === '1' }"
                @click="setAuditStatusFilter('1')"
              >
                <a-icon type="clock-circle" />
                待审核
              </button>
              <button
                class="filter-btn approved"
                :class="{ 'active': auditStatusFilter === '2' }"
                @click="setAuditStatusFilter('2')"
              >
                <a-icon type="check-circle" />
                已通过
              </button>
              <button
                class="filter-btn rejected"
                :class="{ 'active': auditStatusFilter === '3' }"
                @click="setAuditStatusFilter('3')"
              >
                <a-icon type="close-circle" />
                已拒绝
              </button>
            </div>
          </div>

          <!-- 排序控件 -->
          <div class="filter-group">
            <div class="sort-controls">
              <span class="sort-label">排序：</span>
              <a-select
                v-model="sortField"
                size="default"
                style="width: 120px; margin-right: 8px;"
                @change="handleSortChange"
              >
                <a-select-option value="totalRevenue">总收益</a-select-option>
                <a-select-option value="salesCount">销售次数</a-select-option>
                <a-select-option value="createTime">创建时间</a-select-option>
              </a-select>
              <a-button
                :icon="sortOrder === 'desc' ? 'sort-descending' : 'sort-ascending'"
                @click="toggleSortOrder"
                :title="sortOrder === 'desc' ? '降序' : '升序'"
                class="sort-order-btn"
              >
                {{ sortOrder === 'desc' ? '降序' : '升序' }}
              </a-button>
            </div>
          </div>

        </div>
      </div>
    </div>

    <!-- 智能体卡片列表 -->
    <div class="agent-grid" v-if="!loading && agents.length > 0">
      <div 
        v-for="agent in agents" 
        :key="agent.id" 
        class="agent-card"
        :class="{ 'pending': agent.auditStatus === '1', 'approved': agent.auditStatus === '2', 'rejected': agent.auditStatus === '3' }"
      >
        <!-- 卡片头部 -->
        <div class="card-header">
          <div class="agent-avatar">
            <img 
              v-if="agent.agentAvatar" 
              :src="agent.agentAvatar" 
              :alt="agent.agentName"
              @error="handleImageError"
            />
            <div v-else class="avatar-placeholder">
              <a-icon type="robot" />
            </div>
          </div>
          
          <div class="agent-info">
            <h3 class="agent-name" :title="agent.agentName">{{ agent.agentName }}</h3>
            <div class="agent-meta">
              <a-tag
                :color="getStatusColor(agent.auditStatus)"
                class="status-tag"
              >
                {{ agent.auditStatusText }}
              </a-tag>
            </div>
          </div>
          
          <!-- 操作按钮 -->
          <div class="card-actions">
            <a-dropdown :trigger="['click']">
              <a-button type="text" size="small">
                <a-icon type="more" />
              </a-button>
              <a-menu slot="overlay">
                <a-menu-item key="edit" @click="handleEdit(agent)" :disabled="!agent.editable">
                  <a-icon type="edit" />
                  编辑
                </a-menu-item>

                <a-menu-item key="delete" @click="handleDelete(agent)" :disabled="!agent.deletable">
                  <a-icon type="delete" />
                  删除
                </a-menu-item>
              </a-menu>
            </a-dropdown>
          </div>
        </div>
        
        <!-- 卡片内容 -->
        <div class="card-content">
          <div class="agent-description" :title="agent.agentDescription">
            <p>{{ agent.agentDescription || '暂无描述' }}</p>
          </div>
          
          <!-- 统计信息 -->
          <div class="agent-stats">
            <div class="stat-item">
              <span class="stat-label">价格</span>
              <span class="stat-value price">¥{{ agent.price || 0 }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">工作流</span>
              <span class="stat-value">{{ agent.workflowCount || 0 }}</span>
            </div>
            <div class="stat-item" v-if="agent.auditStatus === '2'">
              <span class="stat-label">销售</span>
              <span class="stat-value">{{ agent.salesCount || 0 }}</span>
            </div>
          </div>
          
          <!-- 收益信息（仅已通过状态显示） -->
          <div class="stat-item revenue" v-if="agent.auditStatus === '2'">
            <div class="stat-content">
              <span class="stat-label">总收益</span>
              <span class="stat-value revenue">¥{{ formatMoney(agent.totalRevenue) }}</span>
            </div>
          </div>
          
          <!-- 审核备注（拒绝状态显示） -->
          <div class="audit-remark" v-if="agent.auditStatus === '3' && agent.auditRemark">
            <div class="remark-label">拒绝原因：</div>
            <div class="remark-content">{{ agent.auditRemark }}</div>
          </div>
        </div>
        
        <!-- 卡片底部 -->
        <div class="card-footer">
          <div class="create-time">
            创建时间：{{ formatDate(agent.createTime) }}
          </div>
          <!-- 体验链接 -->
          <div class="experience-link" v-if="agent.experienceLink">
            <a :href="agent.experienceLink" target="_blank" class="link-button">
              <a-icon type="link" />
              体验链接
            </a>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 加载状态 -->
    <div class="loading-container" v-if="loading">
      <a-spin size="large">
        <div class="loading-text">加载中...</div>
      </a-spin>
    </div>
    
    <!-- 空状态 -->
    <div class="empty-container" v-if="!loading && agents.length === 0">
      <a-empty 
        description="暂无智能体"
        :image="emptyImage"
      >
        <a-button type="primary" @click="handleCreate">
          <a-icon type="plus" />
          创建第一个智能体
        </a-button>
      </a-empty>
    </div>
    
    <!-- 分页 -->
    <div class="pagination-container" v-if="!loading && agents.length > 0">
      <div class="pagination-wrapper">
        <a-pagination
          :current="pagination.current"
          :total="pagination.total"
          :page-size="pagination.pageSize"
          :page-size-options="['12', '24', '36', '48']"
          :show-size-changer="true"
          :show-quick-jumper="true"
          :show-total="(total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`"
          @change="handlePageChange"
          @showSizeChange="handlePageSizeChange"
          class="custom-pagination"
        >
          <template slot="buildOptionText" slot-scope="props">
            <span>{{ props.value }}条/页</span>
          </template>
        </a-pagination>
      </div>
    </div>
  </div>
</template>

<script>
import { Empty } from 'ant-design-vue'

export default {
  name: 'AgentManagement',
  props: {
    loading: {
      type: Boolean,
      default: false
    },
    agents: {
      type: Array,
      default: () => []
    },
    pagination: {
      type: Object,
      default: () => ({
        current: 1,
        pageSize: 12,
        total: 0
      })
    }
  },
  
  data() {
    return {
      emptyImage: Empty.PRESENTED_IMAGE_SIMPLE,
      // 筛选相关
      searchQuery: '',
      auditStatusFilter: '',
      // 排序相关
      sortField: 'totalRevenue',  // 默认按总收益排序
      sortOrder: 'desc'           // 默认降序
    }
  },
  
  methods: {
    // 获取状态颜色
    getStatusColor(status) {
      const colorMap = {
        '1': 'orange',   // 待审核
        '2': 'green',    // 已通过
        '3': 'red'       // 已拒绝
      }
      return colorMap[status] || 'default'
    },

    // 获取状态图标
    getStatusIcon(status) {
      const iconMap = {
        '1': 'clock-circle',    // 待审核
        '2': 'check-circle',    // 已通过
        '3': 'close-circle'     // 已拒绝
      }
      return iconMap[status] || 'question-circle'
    },
    
    // 格式化金额
    formatMoney(amount) {
      if (!amount) return '0.00'
      return Number(amount).toFixed(2)
    },
    
    // 格式化日期
    formatDate(dateStr) {
      if (!dateStr) return ''
      const date = new Date(dateStr)
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      })
    },

    // 搜索处理
    handleSearch() {
      this.$emit('search', this.searchQuery)
    },

    // 清除搜索
    clearSearch() {
      this.searchQuery = ''
      this.handleSearch()
    },

    // 排序字段变化
    handleSortChange() {
      this.emitSortChange()
    },

    // 切换排序方向
    toggleSortOrder() {
      this.sortOrder = this.sortOrder === 'desc' ? 'asc' : 'desc'
      this.emitSortChange()
    },

    // 发送排序变化事件
    emitSortChange() {
      this.$emit('sort-change', {
        sortField: this.sortField,
        sortOrder: this.sortOrder
      })
    },

    // 设置审核状态筛选
    setAuditStatusFilter(status) {
      this.auditStatusFilter = status
      this.$emit('filter-change', {
        auditStatus: this.auditStatusFilter
      })
    },

    // 处理图片加载错误
    handleImageError(event) {
      event.target.style.display = 'none'
      event.target.nextElementSibling.style.display = 'flex'
    },
    
    // 编辑智能体
    handleEdit(agent) {
      this.$emit('edit', agent)
    },
    
    // 删除智能体
    handleDelete(agent) {
      this.$emit('delete', agent)
    },
    
    // 🔥 显示工作流已发布提示弹窗
    showWorkflowPublishedModal() {
      // 创建完全自定义的美观弹窗
      this.$confirm({
        title: '', // 空标题
        icon: () => this.$createElement('span'), // 返回空span来完全去掉图标

        content: () => this.$createElement('div', {
          class: 'custom-modal-content'
        }, [
          // 顶部装饰条
          this.$createElement('div', {
            style: {
              height: '4px',
              background: 'linear-gradient(90deg, #ffa726 0%, #ff9800 100%)',
              borderRadius: '2px 2px 0 0',
              marginBottom: '32px'
            }
          }),

          // 图标区域
          this.$createElement('div', {
            style: {
              textAlign: 'center',
              marginBottom: '24px'
            }
          }, [
            this.$createElement('div', {
              style: {
                width: '64px',
                height: '64px',
                background: 'linear-gradient(135deg, #ffa726 0%, #ff9800 100%)',
                borderRadius: '50%',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                margin: '0 auto',
                boxShadow: '0 8px 32px rgba(255, 167, 38, 0.3)'
              }
            }, [
              this.$createElement('a-icon', {
                props: {
                  type: 'lock',
                  style: {
                    fontSize: '28px',
                    color: '#ffffff'
                  }
                }
              })
            ])
          ]),

          // 标题
          this.$createElement('div', {
            style: {
              textAlign: 'center',
              fontSize: '24px',
              fontWeight: '700',
              color: '#1a202c',
              marginBottom: '16px',
              letterSpacing: '-0.5px'
            }
          }, '工作流已锁定'),

          // 内容
          this.$createElement('div', {
            style: {
              textAlign: 'center',
              fontSize: '16px',
              color: '#4a5568',
              lineHeight: '1.6',
              marginBottom: '8px'
            }
          }, '您的工作流已成功发布并锁定'),

          this.$createElement('div', {
            style: {
              textAlign: 'center',
              fontSize: '14px',
              color: '#718096',
              marginBottom: '32px'
            }
          }, '如需修改请联系管理员解锁')
        ]),

        centered: true,
        width: 480,
        maskClosable: true,
        cancelButtonProps: { style: { display: 'none' } },
        okText: '我知道了',
        okButtonProps: {
          style: {
            background: 'linear-gradient(135deg, #ffa726 0%, #ff9800 100%)',
            border: 'none',
            borderRadius: '12px',
            height: '48px',
            fontSize: '16px',
            fontWeight: '600',
            padding: '0 40px',
            boxShadow: '0 4px 20px rgba(255, 167, 38, 0.4)',
            transition: 'all 0.3s ease',
            color: '#ffffff'
          }
        },

        onOk: () => {
          console.log('🎯 AgentManagement: 用户确认工作流已发布提示')
        },

        class: 'workflow-published-modal'
      })
    },
    
    // 创建智能体
    handleCreate() {
      this.$emit('create')
    },
    
    // 分页变化
    handlePageChange(page, pageSize) {
      this.$emit('page-change', page, pageSize)
      // 滚动到创作者中心顶部
      this.scrollToCreatorCenter()
    },

    // 页面大小变化
    handlePageSizeChange(current, size) {
      this.$emit('page-change', current, size)
      // 滚动到创作者中心顶部
      this.scrollToCreatorCenter()
    },

    // 滚动到创作者中心顶部
    scrollToCreatorCenter() {
      this.$nextTick(() => {
        // 查找创作者中心容器
        const creatorCenter = document.querySelector('.creator-center')
        if (creatorCenter) {
          creatorCenter.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
          })
        } else {
          // 如果找不到创作者中心容器，滚动到页面顶部
          window.scrollTo({
            top: 0,
            behavior: 'smooth'
          })
        }
      })
    },
    
    // 刷新列表
    handleRefresh() {
      this.$emit('refresh')
    }
  }
}
</script>

<style lang="less" scoped>
.agent-management {
  // 筛选器样式
  .market-filters {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    padding: 2rem;
    border-radius: 20px;
    box-shadow: 0 8px 32px rgba(59, 130, 246, 0.12), 0 2px 8px rgba(0, 0, 0, 0.04);
    margin-bottom: 2rem;
    border: 1px solid rgba(59, 130, 246, 0.1);
  }

  .filter-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 2rem;
  }

  .search-box {
    flex: 1;
    max-width: 600px;
  }

  .search-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
    border: 2px solid #cbd5e1;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    &:hover {
      border-color: #3b82f6;
      box-shadow: 0 6px 20px rgba(59, 130, 246, 0.2);
      transform: translateY(-1px);
    }

    &:focus-within {
      border-color: #3b82f6;
      box-shadow: 0 8px 24px rgba(59, 130, 246, 0.3);
      transform: translateY(-1px);
    }
  }

  .search-icon {
    position: absolute;
    left: 16px;
    z-index: 2;
    color: #64748b;
    font-size: 18px;
    transition: color 0.3s ease;
  }

  .search-wrapper:focus-within .search-icon {
    color: #3b82f6;
  }

  .clear-icon {
    position: absolute;
    right: 16px;
    z-index: 2;
    color: #94a3b8;
    font-size: 16px;
    cursor: pointer;
    transition: color 0.3s ease;

    &:hover {
      color: #64748b;
    }
  }

  .search-input {
    flex: 1;
    border: none !important;
    box-shadow: none !important;
    padding-left: 48px !important;
    padding-right: 48px !important;
    font-size: 16px;
    height: 48px;
    background: transparent;

    &:focus {
      border: none !important;
      box-shadow: none !important;
    }
  }

  .filter-controls {
    display: flex;
    align-items: flex-start;
    gap: 2rem;
    flex-wrap: wrap;
  }

  .filter-group {
    display: flex;
    align-items: center;
  }

  // 🔥 排序控件样式
  .sort-controls {
    display: flex;
    align-items: center;
    gap: 8px;

    .sort-label {
      font-size: 14px;
      color: #666;
      font-weight: 500;
    }

    .sort-order-btn {
      height: 32px;
      border: 1px solid #d9d9d9;
      border-radius: 6px;
      background: #fff;
      color: #666;
      font-size: 12px;
      transition: all 0.2s ease;

      &:hover {
        border-color: #40a9ff;
        color: #40a9ff;
      }
    }
  }

  .filter-buttons {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
  }

  .filter-btn {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 8px 16px;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    background: white;
    color: #64748b;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(79, 70, 229, 0.1), transparent);
      transition: left 0.5s ease;
    }

    &:hover::before {
      left: 100%;
    }

    &:hover {
      border-color: #4f46e5;
      color: #4f46e5;
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(79, 70, 229, 0.15);
    }

    &.active {
      background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
      border-color: #4f46e5;
      color: white;
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3);

      &:hover {
        background: linear-gradient(135deg, #4338ca 0%, #6d28d9 100%);
      }
    }
  }

  // 智能体网格布局
  .agent-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 24px;
    margin-bottom: 24px;
  }

  // 智能体卡片
  .agent-card {
    background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
    border-radius: 20px;
    border: 2px solid #f1f5f9;
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.06), 0 2px 8px rgba(0, 0, 0, 0.04);

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 4px;
      background: linear-gradient(90deg, #e2e8f0, #e2e8f0);
      transition: all 0.3s ease;
      z-index: 1;
    }

    &:hover {
      transform: translateY(-6px);
      box-shadow: 0 20px 48px rgba(0, 0, 0, 0.12), 0 8px 24px rgba(0, 0, 0, 0.08);
      border-color: #cbd5e1;

      &::before {
        background: linear-gradient(90deg, #3b82f6, #8b5cf6);
      }
    }

    // 状态样式
    &.pending {
      &::before {
        background: linear-gradient(90deg, #f59e0b, #d97706);
      }

      &:hover::before {
        background: linear-gradient(90deg, #d97706, #b45309);
      }
    }

    &.approved {
      &::before {
        background: linear-gradient(90deg, #10b981, #059669);
      }

      &:hover::before {
        background: linear-gradient(90deg, #059669, #047857);
      }
    }

    &.rejected {
      &::before {
        background: linear-gradient(90deg, #ef4444, #dc2626);
      }

      &:hover::before {
        background: linear-gradient(90deg, #dc2626, #b91c1c);
      }
    }

    // 卡片头部
    .card-header {
      padding: 24px 24px 20px;
      background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
      border-bottom: 1px solid #e2e8f0;
      display: flex;
      align-items: flex-start;
      gap: 16px;
      position: relative;

      .agent-avatar {
        width: 64px;
        height: 64px;
        border-radius: 16px;
        overflow: hidden;
        flex-shrink: 0;
        position: relative;
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
        border: 3px solid white;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
          transition: transform 0.3s ease;
        }

        &:hover img {
          transform: scale(1.05);
        }

        .avatar-placeholder {
          width: 100%;
          height: 100%;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          display: flex;
          align-items: center;
          justify-content: center;
          color: #fff;
          font-size: 24px;
        }
      }

      .agent-info {
        flex: 1;
        min-width: 0;

        .agent-name {
          margin: 0 0 8px 0;
          font-size: 20px;
          font-weight: 700;
          color: #1e293b;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          line-height: 1.3;
          background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          background-clip: text;
        }

        .agent-meta {
          display: flex;
          align-items: center;
          gap: 8px;

          .status-tag {
            font-size: 13px;
            font-weight: 600;
            border-radius: 8px;
            padding: 4px 12px;
            border: none;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
          }
        }
      }

      .card-actions {
        .ant-btn {
          border: none;
          box-shadow: none;

          &:hover {
            background: #f5f5f5;
          }
        }
      }
    }

    // 卡片内容
    .card-content {
      padding: 20px 24px;

      .agent-description {
        margin: 0 0 20px 0;
        height: 62px;
        background: #f8fafc;
        border-radius: 10px;
        border-left: 3px solid #e2e8f0;
        display: flex;
        align-items: center;
        padding: 0 16px;

        p {
          color: #64748b;
          font-size: 14px;
          line-height: 1.5;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
          word-break: break-word;
          margin: 0;
          width: 100%;
        }
      }

      .agent-stats {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 12px;
        margin-bottom: 16px;
      }

      .stat-item {
        background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
        border: 2px solid #e2e8f0;
        border-radius: 10px;
        padding: 12px;
        text-align: center;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;

        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          height: 2px;
          background: linear-gradient(90deg, #e2e8f0, #e2e8f0);
          transition: all 0.3s ease;
        }

        &:hover {
          transform: translateY(-1px);
          box-shadow: 0 6px 16px rgba(0, 0, 0, 0.08);
          border-color: #cbd5e1;
        }

        .stat-content {
          display: flex;
          flex-direction: column;
          align-items: center;
        }

        .stat-label {
          display: block;
          font-size: 11px;
          color: #64748b;
          margin-bottom: 4px;
          font-weight: 500;
        }

        .stat-value {
          display: block;
          font-size: 14px;
          font-weight: 600;
          color: #1e293b;

          &.price {
            color: #059669;
          }

          &.revenue {
            color: #dc2626;
          }
        }

        &.revenue {
          &::before {
            background: linear-gradient(90deg, #dc2626, #b91c1c);
          }
        }
      }

      .audit-remark {
        background: #fef2f2;
        border: 1px solid #fecaca;
        border-radius: 6px;
        padding: 12px;
        margin-bottom: 16px;

        .remark-label {
          font-size: 12px;
          color: #dc2626;
          font-weight: 600;
          margin-bottom: 4px;
        }

        .remark-content {
          font-size: 14px;
          color: #7f1d1d;
          line-height: 1.4;
        }
      }
    }

    // 卡片底部
    .card-footer {
      padding: 12px 16px;
      background: #fafbfc;
      border-top: 1px solid #f0f0f0;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .create-time,
      .update-time {
        font-size: 12px;
        color: #9ca3af;
        line-height: 1.4;
      }

      .experience-link {
        .link-button {
          display: inline-flex;
          align-items: center;
          gap: 4px;
          padding: 6px 10px;
          background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
          color: white;
          text-decoration: none;
          font-size: 11px;
          font-weight: 500;
          border-radius: 6px;
          transition: all 0.3s ease;
          box-shadow: 0 2px 6px rgba(59, 130, 246, 0.2);

          &:hover {
            background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
            transform: translateY(-1px);
            box-shadow: 0 3px 8px rgba(59, 130, 246, 0.3);
            color: white;
            text-decoration: none;
          }

          .anticon {
            font-size: 11px;
          }
        }
      }
    }
  }

  // 加载状态
  .loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 200px;

    .loading-text {
      margin-top: 16px;
      color: #6b7280;
    }
  }

  // 空状态
  .empty-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 300px;
    background: #fff;
    border-radius: 12px;
    border: 1px solid #e8eaec;
  }

  // 分页
  .pagination-container {
    display: flex;
    justify-content: center;
    margin-top: 32px;
    padding: 32px 0;
  }

  .pagination-wrapper {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    padding: 24px 32px;
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(59, 130, 246, 0.12), 0 2px 8px rgba(0, 0, 0, 0.04);
    border: 1px solid rgba(59, 130, 246, 0.1);
    backdrop-filter: blur(10px);
  }

  .custom-pagination {
    :global(.ant-pagination-item) {
      border: 2px solid #e2e8f0;
      border-radius: 12px;
      background: white;
      margin: 0 4px;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

      &:hover {
        border-color: #3b82f6;
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
      }

      a {
        color: #64748b;
        font-weight: 500;
        transition: color 0.3s ease;
      }

      &:hover a {
        color: #3b82f6;
      }
    }

    :global(.ant-pagination-item-active) {
      background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
      border-color: #3b82f6;
      transform: translateY(-1px);
      box-shadow: 0 6px 16px rgba(59, 130, 246, 0.3);

      a {
        color: white;
        font-weight: 600;
      }

      &:hover {
        background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
        border-color: #2563eb;
      }
    }

    :global(.ant-pagination-prev),
    :global(.ant-pagination-next) {
      border: 2px solid #e2e8f0;
      border-radius: 12px;
      background: white;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

      &:hover {
        border-color: #3b82f6;
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
      }

      a {
        color: #64748b;
        transition: color 0.3s ease;
      }

      &:hover a {
        color: #3b82f6;
      }
    }

    :global(.ant-pagination-options) {
      margin-left: 16px;

      .ant-select .ant-select-selector {
        border: 2px solid #e2e8f0;
        border-radius: 12px;
        background: white;
        transition: all 0.3s ease;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

        &:hover {
          border-color: #3b82f6;
          box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
        }
      }
    }

    :global(.ant-pagination-options-quick-jumper) input {
      border: 2px solid #e2e8f0;
      border-radius: 12px;
      background: white;
      transition: all 0.3s ease;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

      &:hover {
        border-color: #3b82f6;
        box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
      }

      &:focus {
        border-color: #3b82f6;
        box-shadow: 0 6px 16px rgba(59, 130, 246, 0.3);
      }
    }

    :global(.ant-pagination-total-text) {
      color: #64748b;
      font-weight: 500;
      margin-right: 16px;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .agent-management {
    .agent-grid {
      grid-template-columns: 1fr;
      gap: 16px;
    }

    .agent-card {
      .card-header {
        .agent-info {
          .agent-meta {
            flex-direction: column;
            align-items: flex-start;
            gap: 4px;
          }
        }
      }

      .card-content {
        .agent-stats {
          grid-template-columns: repeat(2, 1fr);
          gap: 8px;
        }

        .stat-item {
          padding: 10px;

          .stat-label {
            font-size: 10px;
          }

          .stat-value {
            font-size: 12px;
          }
        }
      }
    }
  }

  // 筛选器响应式设计
  @media (max-width: 1200px) {
    .market-filters {
      padding: 1.5rem;

      .filter-row {
        flex-direction: column;
        gap: 1.5rem;
        align-items: stretch;
      }

      .search-box {
        max-width: none;
      }

      .filter-controls {
        gap: 1rem;
      }
    }
  }

  @media (max-width: 768px) {
    .market-filters {
      padding: 1rem;
      border-radius: 12px;

      .filter-row {
        gap: 1rem;
      }

      .filter-controls {
        flex-direction: column;
        gap: 1rem;
      }

      .filter-buttons {
        justify-content: center;
      }

      .filter-btn {
        padding: 6px 12px;
        font-size: 13px;
      }
    }
  }

  // 分页响应式设计
  @media (max-width: 768px) {
    .pagination-wrapper {
      padding: 16px 20px;
      border-radius: 12px;
    }

    .custom-pagination {
      :global(.ant-pagination-item),
      :global(.ant-pagination-prev),
      :global(.ant-pagination-next) {
        margin: 0 2px;
        border-radius: 8px;
      }

      :global(.ant-pagination-options) {
        margin-left: 8px;
        margin-top: 8px;
      }

      :global(.ant-pagination-total-text) {
        margin-right: 8px;
        font-size: 12px;
      }
    }
  }

  // 🔥 工作流已发布提示弹窗样式 - 全新现代化设计
  :global(.workflow-published-modal) {
    .ant-modal-content {
      border-radius: 24px !important;
      overflow: hidden;
      box-shadow: 0 32px 64px rgba(0, 0, 0, 0.12), 0 0 0 1px rgba(255, 255, 255, 0.05) !important;
      border: none;
      background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
      backdrop-filter: blur(20px);
    }

    .ant-modal-header {
      display: none !important; // 完全隐藏默认头部
    }

    // 🔥 隐藏左上角的默认图标
    .ant-modal-confirm-body-wrapper {
      .ant-modal-confirm-body {
        .ant-modal-confirm-content {
          margin-left: 0 !important; // 去掉图标占位的左边距
        }
      }
    }

    // 🔥 隐藏默认的标题和图标区域
    .ant-modal-confirm-title {
      display: none !important;
    }

    .ant-modal-confirm-btns {
      margin-top: 0 !important;
    }

    // 🔥 只在这个弹窗中隐藏anticon
    .ant-modal-confirm-body > .anticon {
      display: none !important;
    }

    .ant-modal-body {
      padding: 40px 32px 32px !important;
      background: transparent;

      .custom-modal-content {
        position: relative;
      }
    }

    .ant-modal-footer {
      border-top: none !important;
      padding: 0 32px 40px !important;
      text-align: center;
      background: transparent;

      .ant-btn {
        min-width: 160px;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        overflow: hidden;

        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: -100%;
          width: 100%;
          height: 100%;
          background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
          transition: left 0.5s;
        }

        &:hover {
          transform: translateY(-2px) scale(1.02);
          box-shadow: 0 12px 40px rgba(255, 167, 38, 0.5) !important;

          &::before {
            left: 100%;
          }
        }

        &:active {
          transform: translateY(-1px) scale(1.01);
        }
      }
    }

    // 关闭按钮样式
    .ant-modal-close {
      top: 20px;
      right: 20px;
      z-index: 10;

      .ant-modal-close-x {
        width: 36px;
        height: 36px;
        line-height: 36px;
        border-radius: 50%;
        background: rgba(0, 0, 0, 0.04);
        transition: all 0.3s ease;

        &:hover {
          background: rgba(0, 0, 0, 0.08);
          transform: rotate(90deg);
        }
      }
    }

    // 弹窗动画效果 - 更流畅的动画
    &.ant-modal {
      .ant-modal-content {
        animation: modalSlideIn 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
      }
    }

    @keyframes modalSlideIn {
      from {
        opacity: 0;
        transform: scale(0.8) translateY(-20px) rotateX(10deg);
      }
      to {
        opacity: 1;
        transform: scale(1) translateY(0) rotateX(0deg);
      }
    }

    // 遮罩层样式
    &.ant-modal .ant-modal-mask {
      background: rgba(0, 0, 0, 0.6);
      backdrop-filter: blur(8px);
    }
  }
}
</style>
