package org.jeecg.modules.system.service.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.jeecg.modules.system.entity.SysSensitiveWordHitLog;
import org.jeecg.modules.system.mapper.SysSensitiveWordHitLogMapper;
import org.jeecg.modules.system.service.ISysSensitiveWordHitLogService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import lombok.extern.slf4j.Slf4j;

/**
 * @Description: 敏感词命中记录服务实现
 * @Author: jeecg-boot
 * @Date: 2025-06-22
 * @Version: V1.0
 */
@Service
@Slf4j
public class SysSensitiveWordHitLogServiceImpl extends ServiceImpl<SysSensitiveWordHitLogMapper, SysSensitiveWordHitLog> 
    implements ISysSensitiveWordHitLogService {

    @Override
    public List<Map<String, Object>> getHitTrend(int days) {
        return baseMapper.getHitTrend(days);
    }

    @Override
    public List<Map<String, Object>> getUserHitStatistics(String userId, int days) {
        return baseMapper.getUserHitStatistics(userId, days);
    }

    @Override
    public List<Map<String, Object>> getModuleHitStatistics(int days) {
        return baseMapper.getModuleHitStatistics(days);
    }

    @Override
    public List<Map<String, Object>> getIpHitStatistics(int days) {
        return baseMapper.getIpHitStatistics(days);
    }

    @Override
    public int cleanExpiredLogs(int days) {
        try {
            int cleanCount = baseMapper.cleanExpiredLogs(days);
            log.info("清理过期敏感词命中记录完成，清理数量: {}", cleanCount);
            return cleanCount;
        } catch (Exception e) {
            log.error("清理过期敏感词命中记录失败", e);
            return 0;
        }
    }

    @Override
    public Map<String, Object> getComprehensiveReport(int days) {
        Map<String, Object> report = new HashMap<>();
        
        try {
            // 获取命中趋势
            List<Map<String, Object>> hitTrend = getHitTrend(days);
            report.put("hitTrend", hitTrend);
            
            // 获取用户统计（TOP 10）
            List<Map<String, Object>> userStats = getUserHitStatistics(null, days);
            if (userStats.size() > 10) {
                userStats = userStats.subList(0, 10);
            }
            report.put("topUsers", userStats);
            
            // 获取模块统计
            List<Map<String, Object>> moduleStats = getModuleHitStatistics(days);
            report.put("moduleStats", moduleStats);
            
            // 获取IP统计（TOP 10）
            List<Map<String, Object>> ipStats = getIpHitStatistics(days);
            if (ipStats.size() > 10) {
                ipStats = ipStats.subList(0, 10);
            }
            report.put("topIps", ipStats);
            
            // 计算总体统计
            int totalHits = hitTrend.stream()
                .mapToInt(item -> Integer.parseInt(item.get("hit_count").toString()))
                .sum();
            
            int uniqueWords = hitTrend.stream()
                .mapToInt(item -> Integer.parseInt(item.get("unique_words").toString()))
                .max().orElse(0);
            
            int uniqueUsers = hitTrend.stream()
                .mapToInt(item -> Integer.parseInt(item.get("unique_users").toString()))
                .max().orElse(0);
            
            Map<String, Object> summary = new HashMap<>();
            summary.put("totalHits", totalHits);
            summary.put("uniqueWords", uniqueWords);
            summary.put("uniqueUsers", uniqueUsers);
            summary.put("avgHitsPerDay", totalHits / Math.max(days, 1));
            
            report.put("summary", summary);
            
        } catch (Exception e) {
            log.error("生成敏感词综合统计报告失败", e);
            report.put("error", "生成报告失败: " + e.getMessage());
        }
        
        return report;
    }
}
