{"openapi": "3.0.0", "info": {"title": "剪映小助手_图生视频箱 - 创建视频生成任务", "description": "调用豆包图生视频插件创建视频生成任务", "version": "1.0.0"}, "servers": [{"url": "https://www.aigcview.com"}], "paths": {"/jeecg-boot/api/coze/video/generate-task": {"post": {"summary": "创建视频生成任务", "description": "调用豆包图生视频模型，支持同步异步生成。", "operationId": "create_video_task", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"apiKey": {"type": "string", "description": "API令牌（必填），获取地址：https://www.aigcview.com/usercenter", "example": "your_api_token_here"}, "model": {"type": "string", "description": "模型：'doubao-seedance-1-0-lite-i2v-250428','doubao-seedance-1-0-pro-250528'", "example": "doubao-seedance-1-0-lite-i2v-250428", "default": "doubao-seedance-1-0-lite-i2v-250428", "enum": ["doubao-seedance-1-0-lite-i2v-250428", "doubao-seedance-1-0-pro-250528"]}, "prompt": {"type": "string", "description": "提示词", "example": "一只可爱的小猫在花园里玩耍，阳光明媚，画面温馨"}, "image_url": {"type": "string", "description": "首帧图片链接（图片宽度需大于300px）", "example": "https://example.com/image.jpg"}, "resolution": {"type": "string", "description": "视频分辨率，默认值：480p，可选值：\"480p\"，\"720p\"，\"1080p\",\"doubao-seedance-1-0-pro-250528\"模型不支持\"720p\"，如果输入\"720p\"则默认替换成\"480p\"", "example": "480p", "default": "480p", "enum": ["480p", "720p", "1080p"]}, "duration": {"type": "integer", "description": "时长，默认5s,可选值：5/10", "example": 5, "default": 5, "enum": [5, 10]}, "asyn": {"type": "boolean", "description": "是否同步返回内容，开启后这个节点会等待成功才结束，有可能会出现超时失败。请按需使用", "example": false, "default": false}, "camerafixed": {"type": "boolean", "description": "是否固定摄像头。枚举值： true：固定摄像头。平台会在用户提示词中追加固定摄像头，实际效果不保证。 false：不固定摄像头。", "example": false, "default": false}, "end_image_url": {"type": "string", "description": "尾帧图片链接（图片宽度需大于300px）（doubao-seedance-1-0-pro-250528模型不支持尾帧）", "example": "https://example.com/end_image.jpg"}}, "required": ["<PERSON><PERSON><PERSON><PERSON>", "prompt", "image_url"], "additionalProperties": false}}}}, "responses": {"200": {"description": "成功创建视频生成任务", "content": {"application/json": {"schema": {"type": "object", "properties": {"task_id": {"type": "string", "description": "视频生成任务ID", "example": "cgt-20250716135931-jv2ts"}, "status": {"type": "string", "description": "任务状态：pending(等待中)、running(执行中)、completed(已完成)、failed(失败)", "example": "pending", "enum": ["pending", "running", "completed", "failed"]}, "message": {"type": "string", "description": "状态描述信息", "example": "视频生成任务已创建，请使用task_id查询结果"}, "video_url": {"type": "string", "description": "生成的视频下载链接（仅当asyn=true且任务完成时返回）", "example": "https://ark-content-generation-cn-beijing.tos-cn-beijing.volces.com/video.mp4"}, "deductedAmount": {"type": "number", "description": "实际扣费金额（元）", "example": 0.69}, "balanceAfter": {"type": "number", "description": "扣费后账户余额（元）", "example": 99.31}, "pricingDetails": {"type": "object", "description": "定价详情", "properties": {"model": {"type": "string", "description": "使用的模型", "example": "doubao-seedance-1-0-lite-i2v-250428"}, "resolution": {"type": "string", "description": "视频分辨率", "example": "480p"}, "duration": {"type": "integer", "description": "视频时长（秒）", "example": 5}, "userLevel": {"type": "string", "description": "用户等级", "example": "普通用户"}, "finalPrice": {"type": "number", "description": "最终价格（元）", "example": 0.69}}}}, "required": ["task_id", "status", "message", "deductedAmount", "balanceAfter"]}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "description": "错误信息", "example": "创建视频生成任务失败: 提示词不能为空"}}, "required": ["error"]}}}}, "401": {"description": "API令牌无效", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "description": "错误信息", "example": "创建视频生成任务失败: API令牌无效或已过期"}}, "required": ["error"]}}}}, "500": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "description": "错误信息", "example": "图生视频生成失败: 豆包API调用失败"}}, "required": ["error"]}}}}}}}}}