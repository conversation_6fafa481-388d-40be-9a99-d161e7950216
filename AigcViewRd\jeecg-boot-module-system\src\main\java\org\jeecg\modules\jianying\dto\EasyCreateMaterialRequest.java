package org.jeecg.modules.jianying.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

/**
 * 快速创建素材请求参数
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class EasyCreateMaterialRequest extends BaseJianyingRequest {
    
    @ApiModelProperty(value = "草稿地址（必填）", required = true,
                     example = "https://example.com/draft/123")
    @NotBlank(message = "draft_url不能为空")
    @JsonProperty("draft_url")
    private String zjDraftUrl;

    @ApiModelProperty(value = "音频链接（必填）", required = true,
                     example = "https://example.com/audio.mp3")
    @NotBlank(message = "audio_url不能为空")
    @JsonProperty("audio_url")
    private String zjAudioUrl;

    @ApiModelProperty(value = "视频链接", example = "https://example.com/video.mp4")
    @JsonProperty("video_url")
    private String zjVideoUrl;

    @ApiModelProperty(value = "图片链接", example = "https://example.com/image.jpg")
    @JsonProperty("img_url")
    private String zjImgUrl;

    @ApiModelProperty(value = "文本内容", example = "这是文本内容")
    @JsonProperty("text")
    private String zjText;

    @ApiModelProperty(value = "字幕x轴的位置", example = "0.5")
    @JsonProperty("text_transform_x")
    private Double zjTextTransformX;

    @ApiModelProperty(value = "字幕y轴的位置", example = "0.8")
    @JsonProperty("text_transform_y")
    private Double zjTextTransformY;

    @ApiModelProperty(value = "字幕字体大小", example = "30")
    @JsonProperty("font_size")
    private Integer zjFontSize;

    @ApiModelProperty(value = "字幕颜色", example = "#ffffff")
    @JsonProperty("text_color")
    private String zjTextColor;
    
    @Override
    public String getSummary() {
        return "EasyCreateMaterialRequest{" +
               "accessKey=" + (getAccessKey() != null && getAccessKey().length() > 10 ? 
                              getAccessKey().substring(0, 10) + "***" : "null") +
               ", draftUrl=" + (zjDraftUrl != null && zjDraftUrl.length() > 30 ? 
                               zjDraftUrl.substring(0, 30) + "***" : zjDraftUrl) +
               ", hasVideo=" + (zjVideoUrl != null) +
               ", hasImage=" + (zjImgUrl != null) +
               "}";
    }
}
