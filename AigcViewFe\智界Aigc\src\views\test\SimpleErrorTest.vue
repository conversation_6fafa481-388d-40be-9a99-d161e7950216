<template>
  <div class="simple-error-test">
    <a-card title="服务器异常测试">
      <p>点击下面的按钮测试服务器异常页面：</p>
      <br>
      
      <a-button 
        type="primary" 
        size="large" 
        @click="testServerError"
        :loading="testing"
      >
        测试服务器连接异常
      </a-button>
      
      <br><br>
      
      <a-button
        type="default"
        @click="goToServerError"
      >
        直接访问异常页面
      </a-button>

      <br><br>

      <a-button
        type="danger"
        size="large"
        @click="testErrorHandler"
      >
        测试错误处理器
      </a-button>
      
      <br><br>
      
      <div v-if="testResult" class="test-result">
        <a-alert 
          :message="testResult.title" 
          :description="testResult.description"
          :type="testResult.type"
          show-icon
        />
      </div>
    </a-card>
  </div>
</template>

<script>
import axios from 'axios'
import { handleServerConnectionError } from '@/utils/simpleErrorHandler'

export default {
  name: 'SimpleErrorTest',
  data() {
    return {
      testing: false,
      testResult: null
    }
  },
  methods: {
    // 测试服务器连接异常
    async testServerError() {
      this.testing = true
      this.testResult = null
      
      try {
        // 请求一个不存在的端口，模拟服务器连接异常
        await axios.get('http://localhost:9999/test-connection', {
          timeout: 3000
        })
        
        this.testResult = {
          title: '测试失败',
          description: '预期应该出现连接错误，但请求成功了',
          type: 'warning'
        }
      } catch (error) {
        console.log('捕获到错误:', error)
        
        if (error.code === 'ERR_CONNECTION_REFUSED' || 
            error.message.includes('ERR_CONNECTION_REFUSED')) {
          this.testResult = {
            title: '测试成功',
            description: '成功触发连接被拒绝错误，应该会自动跳转到服务器异常页面',
            type: 'success'
          }
        } else {
          this.testResult = {
            title: '测试结果',
            description: `捕获到错误: ${error.message}`,
            type: 'info'
          }
        }
      } finally {
        this.testing = false
      }
    },
    
    // 直接跳转到服务器异常页面
    goToServerError() {
      this.$router.push({
        name: 'ServerError',
        query: {
          errorType: 'CONNECTION_REFUSED',
          errorMessage: 'Test connection refused error',
          timestamp: Date.now()
        }
      })
    },

    // 测试错误处理器
    testErrorHandler() {
      // 创建一个模拟的连接错误
      const mockError = new Error('Network Error')
      mockError.code = 'ERR_CONNECTION_REFUSED'

      console.log('🧪 测试错误处理器，模拟错误:', mockError)

      // 直接调用错误处理器
      const result = handleServerConnectionError(mockError)

      this.testResult = {
        title: '错误处理器测试',
        description: `错误处理器返回: ${result}，请查看控制台日志`,
        type: result ? 'success' : 'warning'
      }
    }
  }
}
</script>

<style lang="less" scoped>
.simple-error-test {
  padding: 24px;
  max-width: 600px;
  margin: 0 auto;
  
  .test-result {
    margin-top: 20px;
  }
  
  .ant-btn {
    margin-right: 16px;
    margin-bottom: 16px;
  }
}
</style>
