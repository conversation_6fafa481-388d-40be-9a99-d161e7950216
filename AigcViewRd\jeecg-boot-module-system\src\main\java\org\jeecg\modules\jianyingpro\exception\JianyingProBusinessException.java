package org.jeecg.modules.jianyingpro.exception;

import org.jeecg.modules.jianyingpro.enums.JianyingProErrorCode;

/**
 * 超级剪映小助手业务异常
 * 用于抛出业务逻辑相关的异常
 * 
 * <AUTHOR>
 * @since 2025-01-01
 */
public class JianyingProBusinessException extends RuntimeException {
    
    private final JianyingProErrorCode errorCode;
    private final String solution;
    
    public JianyingProBusinessException(JianyingProErrorCode errorCode) {
        super(errorCode.getMessage());
        this.errorCode = errorCode;
        this.solution = errorCode.getSolution();
    }
    
    public JianyingProBusinessException(JianyingProErrorCode errorCode, String customMessage) {
        super(customMessage);
        this.errorCode = errorCode;
        this.solution = errorCode.getSolution();
    }
    
    public JianyingProBusinessException(JianyingProErrorCode errorCode, String customMessage, String customSolution) {
        super(customMessage);
        this.errorCode = errorCode;
        this.solution = customSolution;
    }
    
    public JianyingProBusinessException(JianyingProErrorCode errorCode, Throwable cause) {
        super(errorCode.getMessage(), cause);
        this.errorCode = errorCode;
        this.solution = errorCode.getSolution();
    }
    
    public JianyingProBusinessException(JianyingProErrorCode errorCode, String customMessage, Throwable cause) {
        super(customMessage, cause);
        this.errorCode = errorCode;
        this.solution = errorCode.getSolution();
    }
    
    public JianyingProErrorCode getErrorCode() {
        return errorCode;
    }
    
    public String getSolution() {
        return solution;
    }
    
    /**
     * 创建参数错误异常
     */
    public static JianyingProBusinessException paramError(String message) {
        return new JianyingProBusinessException(JianyingProErrorCode.PARAM_INVALID_002, message);
    }
    
    /**
     * 创建业务处理错误异常
     */
    public static JianyingProBusinessException businessError(String message) {
        return new JianyingProBusinessException(JianyingProErrorCode.BUSINESS_AUDIO_PROCESS_101, message);
    }
    
    /**
     * 创建文件处理错误异常
     */
    public static JianyingProBusinessException fileError(String message) {
        return new JianyingProBusinessException(JianyingProErrorCode.BUSINESS_FILE_ACCESS_106, message);
    }
    
    /**
     * 创建第三方API错误异常
     */
    public static JianyingProBusinessException apiError(String message) {
        return new JianyingProBusinessException(JianyingProErrorCode.BUSINESS_THIRD_PARTY_API_107, message);
    }
}
