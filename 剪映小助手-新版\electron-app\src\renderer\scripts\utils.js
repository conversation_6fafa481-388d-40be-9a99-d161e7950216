// 工具函数模块
const Utils = {
    // 验证URL格式
    validateUrl(url) {
        if (!url || typeof url !== 'string') return false
        
        try {
            new URL(url)
            return true
        } catch {
            return false
        }
    },

    // 验证剪映草稿URL
    validateDraftUrl(url) {
        if (!this.validateUrl(url)) return false
        
        const tosPattern = /^https:\/\/aigcview-plub\.tos-s3-cn-guangzhou\.volces\.com\/jianying-assistant\/drafts\/.+\.json$/
        return tosPattern.test(url)
    },

    // 从URL提取文件名
    extractFileName(url) {
        try {
            const urlObj = new URL(url)
            const pathname = urlObj.pathname
            return pathname.split('/').pop() || 'unknown'
        } catch {
            return 'unknown'
        }
    },

    // 格式化文件大小
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes'
        
        const k = 1024
        const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB']
        const i = Math.floor(Math.log(bytes) / Math.log(k))
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    },

    // 格式化时间
    formatTime(timestamp) {
        const date = new Date(timestamp)
        return date.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        })
    },

    // 格式化相对时间
    formatRelativeTime(timestamp) {
        const now = Date.now()
        const diff = now - timestamp
        
        if (diff < 60000) { // 1分钟内
            return '刚刚'
        } else if (diff < 3600000) { // 1小时内
            return Math.floor(diff / 60000) + '分钟前'
        } else if (diff < 86400000) { // 1天内
            return Math.floor(diff / 3600000) + '小时前'
        } else if (diff < 604800000) { // 1周内
            return Math.floor(diff / 86400000) + '天前'
        } else {
            return this.formatTime(timestamp)
        }
    },

    // 生成唯一ID
    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2)
    },

    // 防抖函数
    debounce(func, wait) {
        let timeout
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout)
                func(...args)
            }
            clearTimeout(timeout)
            timeout = setTimeout(later, wait)
        }
    },

    // 节流函数
    throttle(func, limit) {
        let inThrottle
        return function(...args) {
            if (!inThrottle) {
                func.apply(this, args)
                inThrottle = true
                setTimeout(() => inThrottle = false, limit)
            }
        }
    },

    // 深拷贝对象
    deepClone(obj) {
        if (obj === null || typeof obj !== 'object') return obj
        if (obj instanceof Date) return new Date(obj.getTime())
        if (obj instanceof Array) return obj.map(item => this.deepClone(item))
        if (typeof obj === 'object') {
            const clonedObj = {}
            for (const key in obj) {
                if (obj.hasOwnProperty(key)) {
                    clonedObj[key] = this.deepClone(obj[key])
                }
            }
            return clonedObj
        }
    },

    // 检查是否为空值
    isEmpty(value) {
        if (value === null || value === undefined) return true
        if (typeof value === 'string') return value.trim() === ''
        if (Array.isArray(value)) return value.length === 0
        if (typeof value === 'object') return Object.keys(value).length === 0
        return false
    },

    // 安全的JSON解析
    safeJsonParse(str, defaultValue = null) {
        try {
            return JSON.parse(str)
        } catch {
            return defaultValue
        }
    },

    // 安全的JSON字符串化
    safeJsonStringify(obj, defaultValue = '{}') {
        try {
            return JSON.stringify(obj)
        } catch {
            return defaultValue
        }
    },

    // 检查文件扩展名
    getFileExtension(filename) {
        return filename.split('.').pop().toLowerCase()
    },

    // 验证文件类型
    isValidFileType(filename, allowedTypes) {
        const extension = this.getFileExtension(filename)
        return allowedTypes.includes(extension)
    },

    // 生成随机字符串
    randomString(length = 8) {
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
        let result = ''
        for (let i = 0; i < length; i++) {
            result += chars.charAt(Math.floor(Math.random() * chars.length))
        }
        return result
    },

    // 复制文本到剪贴板
    async copyToClipboard(text) {
        try {
            await navigator.clipboard.writeText(text)
            return true
        } catch {
            // 降级方案
            try {
                const textArea = document.createElement('textarea')
                textArea.value = text
                textArea.style.position = 'fixed'
                textArea.style.opacity = '0'
                document.body.appendChild(textArea)
                textArea.select()
                document.execCommand('copy')
                document.body.removeChild(textArea)
                return true
            } catch {
                return false
            }
        }
    },

    // 从剪贴板读取文本
    async readFromClipboard() {
        try {
            return await navigator.clipboard.readText()
        } catch {
            return ''
        }
    },

    // 下载文件
    downloadFile(url, filename) {
        const link = document.createElement('a')
        link.href = url
        link.download = filename
        link.style.display = 'none'
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
    },

    // 格式化错误信息
    formatError(error) {
        if (typeof error === 'string') return error
        if (error && error.message) return error.message
        if (error && error.toString) return error.toString()
        return '未知错误'
    },

    // 检查网络连接
    async checkNetworkConnection() {
        try {
            const response = await fetch('https://www.baidu.com', {
                method: 'HEAD',
                mode: 'no-cors',
                cache: 'no-cache'
            })
            return true
        } catch {
            return false
        }
    },

    // 等待指定时间
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms))
    },

    // 重试函数
    async retry(fn, maxAttempts = 3, delay = 1000) {
        let lastError
        
        for (let attempt = 1; attempt <= maxAttempts; attempt++) {
            try {
                return await fn()
            } catch (error) {
                lastError = error
                if (attempt < maxAttempts) {
                    await this.sleep(delay * attempt)
                }
            }
        }
        
        throw lastError
    },

    // 获取操作系统信息
    getOSInfo() {
        const userAgent = navigator.userAgent
        let os = 'Unknown'
        
        if (userAgent.includes('Windows')) os = 'Windows'
        else if (userAgent.includes('Mac')) os = 'macOS'
        else if (userAgent.includes('Linux')) os = 'Linux'
        
        return {
            platform: os,
            userAgent: userAgent
        }
    },

    // 检查是否为开发环境
    isDevelopment() {
        return process && process.env && process.env.NODE_ENV === 'development'
    },

    // 日志记录
    log(level, message, data = null) {
        const timestamp = new Date().toISOString()
        const logEntry = {
            timestamp,
            level,
            message,
            data
        }
        
        console[level] || console.log(`[${timestamp}] ${level.toUpperCase()}: ${message}`, data)
        
        // 在生产环境中可以发送到日志服务
        if (!this.isDevelopment()) {
            // TODO: 发送到日志服务
        }
    },

    // 性能监控
    performance: {
        marks: new Map(),
        
        mark(name) {
            Utils.performance.marks.set(name, performance.now())
        },
        
        measure(name, startMark) {
            const startTime = Utils.performance.marks.get(startMark)
            if (startTime) {
                const duration = performance.now() - startTime
                Utils.log('info', `Performance: ${name}`, { duration: `${duration.toFixed(2)}ms` })
                return duration
            }
            return 0
        }
    }
}

// 导出工具函数
window.Utils = Utils
