package org.jeecg.modules.jianyingpro.dto.request;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 一体化图片添加请求
 * 合并 imgs_infos + add_images 的参数
 *
 * <AUTHOR>
 * @since 2025-01-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class JianyingProAddImagesRequest extends BaseJianyingProRequest {

    // ========== 核心参数 ==========
    @ApiModelProperty(value = "草稿地址，使用create_draft输出的draft_url即可（必填）", required = true,
                     example = "https://aigcview-plub.tos-s-cn-guangzhou.volces.com/jianying-assistant/drafts/2025/01/06/draft_1736152800000_1024/draft_content.json")
    @NotBlank(message = "draft_url不能为空")
    @JsonProperty("draft_url")
    private String draftUrl;

    @ApiModelProperty(value = "图片列表，格式[\"https://a.png\",\"https://a.png\"]（必填）", required = true,
                     example = "[\"https://example.com/img1.png\", \"https://example.com/img2.png\"]")
    @NotEmpty(message = "imgs不能为空")
    @JsonProperty("imgs")
    private List<String> imgs;

    @ApiModelProperty(value = "时间节点，只接收结构：[{\"start\":0,\"end\":4612}]，一般从audio_timelines节点的输出获取（必填）", required = true,
                     example = "[{\"start\": 0, \"end\": 4612}]")
    @NotEmpty(message = "timelines不能为空")
    @JsonProperty("timelines")
    private List<JSONObject> timelines;

    // ========== 来自imgs_infos的可选参数 ==========
    @ApiModelProperty(value = "对应剪映的出场动画时长（可选）", example = "1000000")
    @JsonProperty("out_animation_duration")
    private Integer outAnimationDuration;

    @ApiModelProperty(value = "对应剪映的转场动画名字（可选），比如：水墨", example = "淡入淡出")
    @JsonProperty("transition")
    private String transition;

    @ApiModelProperty(value = "图片高度", example = "1080")
    @JsonProperty("height")
    private Integer height;

    @ApiModelProperty(value = "对应剪映的组合动画名字（可选）", example = "动画1|动画2")
    @JsonProperty("group_animation")
    private String groupAnimation;

    @ApiModelProperty(value = "对应剪映的出场动画名字（可选）", example = "动画1|动画2")
    @JsonProperty("out_animation")
    private String outAnimation;

    @ApiModelProperty(value = "图片宽度", example = "1920")
    @JsonProperty("width")
    private Integer width;

    @ApiModelProperty(value = "对应剪映的入场动画名字（可选）", example = "动画1|动画2")
    @JsonProperty("in_animation")
    private String inAnimation;

    @ApiModelProperty(value = "对应剪映的入场动画时长（可选）", example = "1000000")
    @JsonProperty("in_animation_duration")
    private Integer inAnimationDuration;

    @ApiModelProperty(value = "对应剪映的组合动画时长（可选）", example = "2000000")
    @JsonProperty("group_animation_duration")
    private Integer groupAnimationDuration;

    @ApiModelProperty(value = "对应剪映的转场动画时长（可选）", example = "1500000")
    @JsonProperty("transition_duration")
    private Integer transitionDuration;

    // ========== 来自add_images的可选参数 ==========
    @ApiModelProperty(value = "图片透明度，范围0-1（可选）", example = "0.5")
    @JsonProperty("alpha")
    private Double alpha;

    @ApiModelProperty(value = "X轴缩放比例（可选）", example = "0.9")
    @JsonProperty("scale_x")
    private Double scaleX;

    @ApiModelProperty(value = "Y轴缩放比例（可选）", example = "0.9")
    @JsonProperty("scale_y")
    private Double scaleY;

    @ApiModelProperty(value = "X轴移动位置（可选）", example = "100")
    @JsonProperty("transform_x")
    private Double transformX;

    @ApiModelProperty(value = "Y轴移动位置（可选）", example = "-100")
    @JsonProperty("transform_y")
    private Double transformY;



    @Override
    public String getSummary() {
        return "JianyingProAddImagesRequest{" +
               "draftUrl=" + (draftUrl != null && draftUrl.length() > 50 ?
                            draftUrl.substring(0, 50) + "***" : draftUrl) +
               ", imgsCount=" + (imgs != null ? imgs.size() : 0) +
               ", timelinesCount=" + (timelines != null ? timelines.size() : 0) +
               ", alpha=" + alpha +
               ", scaleX=" + scaleX + ", scaleY=" + scaleY +
               ", transformX=" + transformX + ", transformY=" + transformY +
               "}";
    }

    @Override
    public void validate() {
        super.validate();

        // imgs是必填的（复制自稳定版imgs_infos要求）
        if (imgs == null || imgs.isEmpty()) {
            throw new IllegalArgumentException(
                "参数不完整：imgs不能为空"
            );
        }

        // timelines是必填的（复制自稳定版imgs_infos要求）
        if (timelines == null || timelines.isEmpty()) {
            throw new IllegalArgumentException(
                "参数不完整：timelines不能为空"
            );
        }

        // 验证imgs中不能有空值
        for (String url : imgs) {
            if (url == null || url.trim().isEmpty()) {
                throw new IllegalArgumentException(
                    "imgs 中不能包含空的URL"
                );
            }
        }

        // 验证透明度范围
        if (alpha != null && (alpha < 0 || alpha > 1)) {
            throw new IllegalArgumentException(
                "alpha 参数值必须在 0-1 之间，当前值: " + alpha
            );
        }
    }
}
