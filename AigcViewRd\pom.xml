<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <groupId>org.jeecgframework.boot</groupId>
  <artifactId>jeecg-boot-parent</artifactId>
  <version>2.4.6</version>
  <packaging>pom</packaging>
  
  	<parent>
	    <groupId>org.springframework.boot</groupId>
	    <artifactId>spring-boot-starter-parent</artifactId>
	    <version>2.3.5.RELEASE</version>
	    <relativePath/>
	</parent>

	<properties>
		<jeecgboot.version>2.4.6</jeecgboot.version>
		<java.version>1.8</java.version>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <spring-cloud.version>Hoxton.SR8</spring-cloud.version>
        <spring-cloud-alibaba.version>2.2.3.RELEASE</spring-cloud-alibaba.version>
		<xxl-job-core.version>2.2.0</xxl-job-core.version>
        <fastjson.version>1.2.78</fastjson.version>
        <lombok.version>1.18.20</lombok.version>
		<knife4j-spring-boot-starter.version>2.0.9</knife4j-spring-boot-starter.version>
		<knife4j-spring-ui.version>2.0.9</knife4j-spring-ui.version>
		<!-- 数据库驱动 -->
		<postgresql.version>42.2.6</postgresql.version>
		<ojdbc6.version>11.2.0.3</ojdbc6.version>
		<sqljdbc4.version>4.0</sqljdbc4.version>
		<mysql-connector-java.version>8.0.21</mysql-connector-java.version>
		<!-- 动态数据源-->
		<dynamic-datasource-spring-boot-starter.version>3.2.0</dynamic-datasource-spring-boot-starter.version>
		<hutool.version>5.3.8</hutool.version>
		<redisson.version>3.16.1</redisson.version>
		<commons-beanutils.version>1.9.4</commons-beanutils.version>
		<guava.version>29.0-jre</guava.version>
		<mybatis-plus.version>3.4.3.1</mybatis-plus.version>
		<druid.version>1.1.22</druid.version>
		<commons.version>2.6</commons.version>
		<aliyun-java-sdk-dysmsapi.version>2.1.0</aliyun-java-sdk-dysmsapi.version>
		<aliyun.oss.version>3.11.2</aliyun.oss.version>
		<shiro.version>1.7.1</shiro.version>
		<java-jwt.version>3.11.0</java-jwt.version>
		<shiro-redis.version>3.1.0</shiro-redis.version>
		<codegenerate.version>1.3.6</codegenerate.version>
		<autopoi-web.version>1.3.5</autopoi-web.version>
		<minio.version>8.0.3</minio.version>
		<justauth-spring-boot-starter.version>1.3.4</justauth-spring-boot-starter.version>
		<dom4j.version>1.6.1</dom4j.version>
		<qiniu-java-sdk.version>7.4.0</qiniu-java-sdk.version>
	</properties>

	<modules>
        <module>jeecg-boot-base</module>
        <module>jeecg-boot-module-demo</module>
        <module>jeecg-boot-module-system</module>
        <!--需要微服务，请打开
        <module>jeecg-boot-starter</module>
        <module>jeecg-cloud-module</module>-->
	</modules>

	<distributionManagement>
	  	<repository>
            <id>jeecg</id>
            <name>jeecg Repository</name>
            <url>http://maven.jeecg.com:8090/nexus/content/repositories/jeecg</url>
        </repository>
        <snapshotRepository>
	        <id>jeecg-snapshots</id>
            <name>jeecg Snapshot Repository</name>
            <url>http://maven.jeecg.com:8090/nexus/content/repositories/snapshots/</url>
	    </snapshotRepository>
	</distributionManagement>
	<repositories>
		<repository>
            <id>aliyun</id>
            <name>aliyun Repository</name>
            <url>https://maven.aliyun.com/repository/public</url>
            <snapshots>
				<enabled>false</enabled>
			</snapshots>
        </repository>
		<repository>
            <id>jeecg</id>
            <name>jeecg Repository</name>
            <url>https://maven.jeecg.org/nexus/content/repositories/jeecg</url>
            <snapshots>
				<enabled>false</enabled>
			</snapshots>
        </repository>
	</repositories>

	<dependencies>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope>
		</dependency>
		<!-- Lombok -->
		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
			<version>${lombok.version}</version>
			<scope>provided</scope>
		</dependency>
		<!-- json -->
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>fastjson</artifactId>
			<version>${fastjson.version}</version>
		</dependency>
	</dependencies>

	<dependencyManagement>
		<dependencies>
			<!-- spring-cloud-->
			<dependency>
				<groupId>org.springframework.cloud</groupId>
				<artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>
			<!-- spring-cloud-alibaba -->
			<dependency>
				<groupId>com.alibaba.cloud</groupId>
				<artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${spring-cloud-alibaba.version}</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>

			<!-- system 模块-->
			<dependency>
				<groupId>org.jeecgframework.boot</groupId>
				<artifactId>jeecg-boot-module-system</artifactId>
				<version>${jeecgboot.version}</version>
			</dependency>

			<!-- jeecg tools -->
			<dependency>
				<groupId>org.jeecgframework.boot</groupId>
				<artifactId>jeecg-boot-base-tools</artifactId>
				<version>${jeecgboot.version}</version>
			</dependency>
			<!-- jeecg core -->
			<dependency>
				<groupId>org.jeecgframework.boot</groupId>
				<artifactId>jeecg-boot-base-core</artifactId>
				<version>${jeecgboot.version}</version>
			</dependency>
			<!-- system 单体 api -->
			<dependency>
				<groupId>org.jeecgframework.boot</groupId>
				<artifactId>jeecg-system-local-api</artifactId>
				<version>${jeecgboot.version}</version>
			</dependency>
			<!-- system 微服务 api -->
			<dependency>
				<groupId>org.jeecgframework.boot</groupId>
				<artifactId>jeecg-system-cloud-api</artifactId>
				<version>${jeecgboot.version}</version>
			</dependency>

            <!--微服务模块-->
            <dependency>
                <groupId>org.jeecgframework.boot</groupId>
                <artifactId>jeecg-boot-starter-cloud</artifactId>
                <version>${jeecgboot.version}</version>
            </dependency>

			<!--xxl-job定时任务-->
			<dependency>
				<groupId>org.jeecgframework.boot</groupId>
				<artifactId>jeecg-boot-starter-job</artifactId>
				<version>${jeecgboot.version}</version>
			</dependency>

			<!--redis分布式锁-->
			<dependency>
				<groupId>org.jeecgframework.boot</groupId>
				<artifactId>jeecg-boot-starter-lock</artifactId>
				<version>${jeecgboot.version}</version>
			</dependency>

			<!--rabbitmq消息队列-->
			<dependency>
				<groupId>org.jeecgframework.boot</groupId>
				<artifactId>jeecg-boot-starter-rabbitmq</artifactId>
				<version>${jeecgboot.version}</version>
			</dependency>

			<!-- 🔐 微信开发工具包 - 公众号 -->
			<dependency>
				<groupId>com.github.binarywang</groupId>
				<artifactId>weixin-java-mp</artifactId>
				<version>4.1.0</version>
			</dependency>

			<!-- 🔐 微信开发工具包 - 通用包 -->
			<dependency>
				<groupId>com.github.binarywang</groupId>
				<artifactId>weixin-java-common</artifactId>
				<version>4.1.0</version>
			</dependency>

			<!-- 七牛云SDK -->
			<dependency>
				<groupId>com.qiniu</groupId>
				<artifactId>qiniu-java-sdk</artifactId>
				<version>${qiniu-java-sdk.version}</version>
				<exclusions>
					<exclusion>
						<artifactId>okhttp</artifactId>
						<groupId>com.squareup.okhttp3</groupId>
					</exclusion>
				</exclusions>
			</dependency>
			<!-- dom4j -->
			<dependency>
				<groupId>dom4j</groupId>
				<artifactId>dom4j</artifactId>
				<version>${dom4j.version}</version>
			</dependency>
			<!-- redisson -->
			<dependency>
				<groupId>org.redisson</groupId>
				<artifactId>redisson</artifactId>
				<version>${redisson.version}</version>
			</dependency>

			<!-- guava工具类 -->
			<dependency>
				<groupId>com.google.guava</groupId>
				<artifactId>guava</artifactId>
				<version>${guava.version}</version>
			</dependency>

			<!-- hutool工具类-->
			<dependency>
				<groupId>cn.hutool</groupId>
				<artifactId>hutool-core</artifactId>
				<version>${hutool.version}</version>
			</dependency>
			<dependency>
				<groupId>cn.hutool</groupId>
				<artifactId>hutool-crypto</artifactId>
				<version>${hutool.version}</version>
			</dependency>

			<!-- commons-beanutils -->
			<dependency>
				<groupId>commons-beanutils</groupId>
				<artifactId>commons-beanutils</artifactId>
				<version>${commons-beanutils.version}</version>
			</dependency>

			<dependency>
				<groupId>commons-fileupload</groupId>
				<artifactId>commons-fileupload</artifactId>
				<version>1.4</version>
				<exclusions>
					<exclusion>
						<artifactId>commons-io</artifactId>
						<groupId>commons-io</groupId>
					</exclusion>
				</exclusions>
			</dependency>

			<!-- justauth第三方登录  -->
			<dependency>
				<groupId>com.xkcoding.justauth</groupId>
				<artifactId>justauth-spring-boot-starter</artifactId>
				<version>${justauth-spring-boot-starter.version}</version>
				<exclusions>
					<exclusion>
						<artifactId>hutool-core</artifactId>
						<groupId>cn.hutool</groupId>
					</exclusion>
					<exclusion>
						<artifactId>fastjson</artifactId>
						<groupId>com.alibaba</groupId>
					</exclusion>
				</exclusions>
			</dependency>
			<dependency>
				<groupId>com.squareup.okhttp3</groupId>
				<artifactId>okhttp</artifactId>
				<version>4.4.1</version>
			</dependency>

			<dependency>
				<groupId>io.minio</groupId>
				<artifactId>minio</artifactId>
				<version>${minio.version}</version>
				<exclusions>
					<exclusion>
						<artifactId>okio</artifactId>
						<groupId>com.squareup.okio</groupId>
					</exclusion>
					<exclusion>
						<artifactId>okhttp</artifactId>
						<groupId>com.squareup.okhttp3</groupId>
					</exclusion>
				</exclusions>
			</dependency>
		</dependencies>
	</dependencyManagement>

	<build>
		<plugins>
			<!--<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
			</plugin>
			 指定JDK编译版本 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>
            <!-- 打包跳过测试 -->
            <plugin>
		        <groupId>org.apache.maven.plugins</groupId>
		        <artifactId>maven-surefire-plugin</artifactId>
		        <configuration>
		          <skipTests>true</skipTests>
		        </configuration>
	        </plugin>
	         <!-- 避免font文件的二进制文件格式压缩破坏 -->
	         <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <configuration>
                    <nonFilteredFileExtensions>
                        <nonFilteredFileExtension>woff</nonFilteredFileExtension>
                        <nonFilteredFileExtension>woff2</nonFilteredFileExtension>
                        <nonFilteredFileExtension>eot</nonFilteredFileExtension>
                        <nonFilteredFileExtension>ttf</nonFilteredFileExtension>
                        <nonFilteredFileExtension>svg</nonFilteredFileExtension>
                    </nonFilteredFileExtensions>
                </configuration>
            </plugin>
		</plugins>
		<resources>
			<resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
            </resource>
			<resource>
				<directory>src/main/java</directory>
				<includes>
					<include>**/*.xml</include>
					<include>**/*.json</include>
					<include>**/*.ftl</include>
				</includes>
			</resource>
		</resources>
	</build>
    <!-- 环境 -->
    <profiles>
        <!-- 开发 -->
        <profile>
            <id>dev</id>
            <activation>
                <!--默认激活配置-->
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <!--当前环境-->
                <profile.name>dev</profile.name>
                <!--配置文件前缀-->
                <prefix.name>jeecg</prefix.name>
                <!--Nacos配置中心地址-->
                <config.server-addr>127.0.0.1:8848</config.server-addr>
                <!--Nacos配置中心命名空间,用于支持多环境.这里必须使用ID，不能使用名称,默认为空-->
                <config.namespace></config.namespace>
                <!--Nacos配置分组名称-->
                <config.group>DEFAULT_GROUP</config.group>
                <!--Nacos服务发现地址-->
                <discovery.server-addr>127.0.0.1:8848</discovery.server-addr>
            </properties>
        </profile>
        <!-- 测试 -->
        <profile>
            <id>test</id>
            <properties>
                <!--当前环境-->
                <profile.name>test</profile.name>
                <!--配置文件前缀-->
                <prefix.name>jeecg</prefix.name>
                <!--Nacos配置中心地址-->
                <config.server-addr>127.0.0.1:8848</config.server-addr>
                <!--Nacos配置中心命名空间,用于支持多环境.这里必须使用ID，不能使用名称,默认为空-->
                <config.namespace></config.namespace>
                <!--Nacos配置分组名称-->
                <config.group>DEFAULT_GROUP</config.group>
                <!--Nacos服务发现地址-->
                <discovery.server-addr>127.0.0.1:8848</discovery.server-addr>
            </properties>
        </profile>
        <!-- 生产 -->
        <profile>
            <id>prod</id>
            <properties>
                <!--当前环境,生产环境为空-->
                <profile.name>prod</profile.name>
                <!--配置文件前缀-->
                <prefix.name>jeecg</prefix.name>
                <!--Nacos配置中心地址-->
                <config.server-addr>127.0.0.1:8848</config.server-addr>
                <!--Nacos配置中心命名空间,用于支持多环境.这里必须使用ID，不能使用名称,默认为空-->
                <config.namespace></config.namespace>
                <!--Nacos配置分组名称-->
                <config.group>DEFAULT_GROUP</config.group>
                <!--Nacos服务发现地址-->
                <discovery.server-addr>127.0.0.1:8848</discovery.server-addr>
            </properties>
        </profile>
    </profiles>
</project>