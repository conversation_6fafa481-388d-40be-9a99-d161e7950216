package org.jeecg.modules.demo.notification.service;

import java.util.Date;
import java.util.List;
import java.util.Map;
import org.jeecg.modules.demo.notification.entity.AicgUserNotification;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * @Description: 用户通知消息表
 * @Author: jeecg-boot
 * @Date:   2025-06-18
 * @Version: V1.0
 */
public interface IAicgUserNotificationService extends IService<AicgUserNotification> {

    /**
     * 根据用户ID查询通知消息
     * @param userId 用户ID
     * @return 通知消息列表
     */
    List<AicgUserNotification> getByUserId(String userId);
    
    /**
     * 根据用户ID查询未读通知
     * @param userId 用户ID
     * @return 未读通知列表
     */
    List<AicgUserNotification> getUnreadByUserId(String userId);
    
    /**
     * 创建通知消息
     * @param userId 用户ID
     * @param title 通知标题
     * @param content 通知内容
     * @param type 通知类型
     * @param priority 优先级
     * @param relatedId 关联业务ID
     * @param relatedType 关联业务类型
     * @param actionUrl 操作链接
     * @param expireTime 过期时间
     * @param operatorId 操作人ID
     * @return 创建的通知消息
     */
    AicgUserNotification createNotification(String userId, String title, String content, Integer type,
                                           Integer priority, String relatedId, String relatedType,
                                           String actionUrl, Date expireTime, String operatorId);
    
    /**
     * 标记通知为已读
     * @param id 通知ID
     * @param userId 用户ID
     * @param operatorId 操作人ID
     * @return 是否成功
     */
    boolean markAsRead(String id, String userId, String operatorId);

    /**
     * 标记为未读
     * @param id 通知ID
     * @param userId 用户ID
     * @param operatorId 操作者ID
     * @return 是否成功
     */
    boolean markAsUnread(String id, String userId, String operatorId);
    
    /**
     * 批量标记为已读
     * @param userId 用户ID
     * @param ids 通知ID列表
     * @param operatorId 操作人ID
     * @return 成功标记的数量
     */
    int batchMarkAsRead(String userId, List<String> ids, String operatorId);
    
    /**
     * 标记所有未读通知为已读
     * @param userId 用户ID
     * @param operatorId 操作人ID
     * @return 成功标记的数量
     */
    int markAllAsRead(String userId, String operatorId);
    
    /**
     * 统计用户通知数据
     * @param userId 用户ID
     * @return 通知统计数据
     */
    Map<String, Object> getNotificationStats(String userId);
    
    /**
     * 根据类型查询通知
     * @param userId 用户ID
     * @param type 通知类型
     * @return 通知列表
     */
    List<AicgUserNotification> getByUserIdAndType(String userId, Integer type);
    
    /**
     * 查询即将过期的通知
     * @return 即将过期的通知列表
     */
    List<AicgUserNotification> getExpiringNotifications();
    
    /**
     * 查询已过期的通知
     * @return 已过期的通知列表
     */
    List<AicgUserNotification> getExpiredNotifications();
    
    /**
     * 删除过期通知
     * @return 删除的记录数
     */
    int deleteExpiredNotifications();
    
    /**
     * 发送系统通知
     * @param userId 用户ID
     * @param title 标题
     * @param content 内容
     * @param operatorId 操作人ID
     * @return 创建的通知
     */
    AicgUserNotification sendSystemNotification(String userId, String title, String content, String operatorId);
    
    /**
     * 发送交易通知
     * @param userId 用户ID
     * @param title 标题
     * @param content 内容
     * @param transactionId 交易ID
     * @param operatorId 操作人ID
     * @return 创建的通知
     */
    AicgUserNotification sendTransactionNotification(String userId, String title, String content, 
                                                    String transactionId, String operatorId);
    
    /**
     * 发送安全提醒
     * @param userId 用户ID
     * @param title 标题
     * @param content 内容
     * @param operatorId 操作人ID
     * @return 创建的通知
     */
    AicgUserNotification sendSecurityAlert(String userId, String title, String content, String operatorId);
    
    /**
     * 发送营销推送
     * @param userId 用户ID
     * @param title 标题
     * @param content 内容
     * @param actionUrl 操作链接
     * @param expireTime 过期时间
     * @param operatorId 操作人ID
     * @return 创建的通知
     */
    AicgUserNotification sendMarketingNotification(String userId, String title, String content, 
                                                  String actionUrl, Date expireTime, String operatorId);
}
