# Logo替换部署指南

## 🎯 部署概述

本指南将帮助您完成AigcView官网前端项目的logo替换部署工作。

## 📁 文件放置

### 1. 主Logo文件
将提供的 `logo.png` (364x364px) 放置到以下位置：
```
AigcViewFe/智界Aigc/src/assets/logo/logo.png
```

### 2. Favicon文件
将提供的 `favicon.ico` (16x16px) 放置到以下位置：
```
AigcViewFe/智界Aigc/public/favicon.ico
```

### 3. 页面加载Logo
将 `logo.png` 复制到public目录（用于加载页面）：
```
AigcViewFe/智界Aigc/public/logo.png
```

## 🔧 部署步骤

### 步骤1：创建logo目录
```bash
mkdir -p AigcViewFe/智界Aigc/src/assets/logo
```

### 步骤2：放置logo文件
```bash
# 将logo.png放置到assets/logo目录
cp logo.png AigcViewFe/智界Aigc/src/assets/logo/

# 将logo.png复制到public目录（用于加载页面）
cp logo.png AigcViewFe/智界Aigc/public/

# 将favicon.ico放置到public目录
cp favicon.ico AigcViewFe/智界Aigc/public/
```

### 步骤3：验证文件结构
确保文件结构如下：
```
AigcViewFe/智界Aigc/
├── src/
│   ├── assets/
│   │   └── logo/
│   │       ├── logo.png          # 主Logo文件
│   │       └── README.md          # Logo说明文档
│   └── components/
│       └── common/
│           └── LogoImage.vue      # Logo组件
├── public/
│   ├── logo.png                   # 加载页面Logo
│   ├── favicon.ico                # 浏览器图标
│   └── site.webmanifest          # PWA清单文件
└── logo-deployment-guide.md      # 本部署指南
```

## 🚀 启动测试

### 1. 安装依赖（如需要）
```bash
cd AigcViewFe/智界Aigc
npm install
```

### 2. 启动开发服务器
```bash
npm run serve
```

### 3. 访问测试页面
- 官网首页：http://localhost:8080
- 登录页面：http://localhost:8080/login
- 管理后台：http://localhost:8080/admin

## ✅ 验证清单

### 1. 官网页面验证
- [ ] 访问首页，检查Header中的logo显示
- [ ] 滚动到页面底部，检查Footer中的logo显示
- [ ] 测试logo悬停效果
- [ ] 点击logo确认跳转到首页

### 2. 登录页面验证
- [ ] 访问登录页面，检查大尺寸logo显示
- [ ] 确认动画效果正常
- [ ] 测试响应式设计

### 3. 管理后台验证
- [ ] 登录管理后台
- [ ] 检查侧边栏logo显示
- [ ] 测试深色/浅色主题切换

### 4. 浏览器图标验证
- [ ] 检查浏览器标签页图标
- [ ] 添加到收藏夹测试
- [ ] 移动端添加到主屏幕测试

## 🔧 故障排除

### 问题1：Logo图片不显示
**可能原因**：
- 文件路径不正确
- 文件权限问题
- 图片格式不支持

**解决方案**：
1. 检查文件是否放置在正确位置
2. 确认文件名大小写正确
3. 检查图片文件是否损坏
4. 查看浏览器控制台错误信息

### 问题2：Fallback图标显示
**说明**：这是正常的备用机制
**检查**：
1. 确认logo.png文件存在
2. 检查网络连接
3. 查看控制台是否有加载错误

### 问题3：响应式显示异常
**解决方案**：
1. 清除浏览器缓存
2. 检查CSS样式是否冲突
3. 测试不同设备和浏览器

## 📱 移动端测试

### iOS设备
1. 使用Safari浏览器访问网站
2. 点击分享按钮 → "添加到主屏幕"
3. 检查主屏幕图标显示

### Android设备
1. 使用Chrome浏览器访问网站
2. 点击菜单 → "添加到主屏幕"
3. 检查主屏幕图标显示

## 🎨 自定义配置

### 修改Logo尺寸
如需调整logo尺寸，编辑 `src/config/logo.js`：
```javascript
export const LOGO_SIZE_MAP = {
  small: '32px',    // 管理后台
  medium: '48px',   // 官网Header/Footer
  large: '80px'     // 登录页面
}
```

### 修改Fallback样式
编辑各组件中的CSS样式类：
- `.brand-logo-fallback` - Header fallback
- `.footer-logo-fallback` - Footer fallback
- `.login-logo-fallback` - 登录页面fallback

## 📞 技术支持

如遇到问题，请检查：
1. 浏览器控制台错误信息
2. 网络请求状态
3. 文件路径和权限
4. 参考测试清单进行逐项检查

部署完成后，建议进行全面测试以确保所有功能正常运行。
