package org.jeecg.modules.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 桌面应用下载记录实体类
 * 
 * <AUTHOR>
 * @date 2025-01-12
 */
@Data
@TableName("aigc_desktop_download_log")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "AigcDesktopDownloadLog对象", description = "桌面应用下载记录")
public class AigcDesktopDownloadLog implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键ID")
    private String id;

    /**
     * 下载平台
     */
    @Excel(name = "下载平台", width = 15)
    @ApiModelProperty(value = "下载平台：windows/mac")
    private String platform;

    /**
     * 文件名称
     */
    @Excel(name = "文件名称", width = 30)
    @ApiModelProperty(value = "文件名称")
    private String fileName;

    /**
     * 文件描述
     */
    @Excel(name = "文件描述", width = 30)
    @ApiModelProperty(value = "文件描述")
    private String fileDescription;

    /**
     * 客户端IP地址
     */
    @Excel(name = "客户端IP", width = 15)
    @ApiModelProperty(value = "客户端IP地址")
    private String clientIp;

    /**
     * 用户代理信息
     */
    @ApiModelProperty(value = "用户代理信息")
    private String userAgent;

    /**
     * 下载状态
     */
    @Excel(name = "下载状态", width = 10, replace = {"成功_1", "失败_0"})
    @ApiModelProperty(value = "下载状态：1-成功，0-失败")
    private Integer downloadStatus;

    /**
     * 错误信息
     */
    @Excel(name = "错误信息", width = 30)
    @ApiModelProperty(value = "错误信息（失败时记录）")
    private String errorMessage;

    /**
     * 请求时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "请求时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "请求时间")
    private Date requestTime;

    /**
     * 响应时间（毫秒）
     */
    @Excel(name = "响应时间(ms)", width = 15)
    @ApiModelProperty(value = "响应时间（毫秒）")
    private Long responseTime;

    /**
     * 生成的签名URL
     */
    @ApiModelProperty(value = "生成的签名URL（可选，用于调试）")
    private String signedUrl;

    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "创建时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
}
