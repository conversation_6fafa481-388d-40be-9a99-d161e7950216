{"openapi": "3.0.0", "info": {"title": "剪映小助手_智界工具箱 - 创建文本富文本样式", "description": "创建文本富文本样式", "version": "1.0.0"}, "servers": [{"url": "https://www.aigcview.com"}], "paths": {"/jeecg-boot/api/jianying/add_text_style": {"post": {"summary": "创建文本富文本样式", "description": "创建文本富文本样式", "operationId": "addTextStyle_zj", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"access_key": {"type": "string", "description": "访问密钥（必填）", "example": "JianyingAPI_2025_SecureAccess_AigcView", "default": "JianyingAPI_2025_SecureAccess_AigcView"}, "zj_font_size": {"type": "integer", "description": "字体大小，默认15", "example": 15}, "zj_keyword": {"type": "string", "description": "关键词（必填）", "example": "重要内容"}, "zj_keyword_color": {"type": "string", "description": "关键词颜色，默认：#ff7100", "example": "#ff7100"}, "zj_keyword_font_size": {"type": "integer", "description": "关键词字体大小，默认15", "example": 15}, "zj_text": {"type": "string", "description": "文本内容（必填）", "example": "这是包含重要内容的文本"}}, "required": ["access_key", "zj_keyword", "zj_text"], "additionalProperties": false}}}}, "responses": {"200": {"description": "成功创建文本样式", "content": {"application/json": {"schema": {"type": "object", "properties": {"text_style": {"type": "string", "description": "剪映富文本样式JSON字符串", "example": "{\"styles\":[{\"fill\":{\"content\":{\"solid\":{\"color\":[1,1,1]}}},\"range\":[0,3],\"size\":10,\"font\":{\"id\":\"\",\"path\":\"\"}},{\"fill\":{\"content\":{\"solid\":{\"color\":[0.9098039215686274,0.09411764705882353,0.09411764705882353]}}},\"range\":[3,5],\"size\":15,\"font\":{\"id\":\"\",\"path\":\"\"},\"useLetterColor\":true},{\"fill\":{\"content\":{\"solid\":{\"color\":[1,1,1]}}},\"range\":[5,13],\"size\":10,\"font\":{\"id\":\"\",\"path\":\"\"}}],\"text\":\"智界d阿萨aigcview\"}"}}, "required": ["text_style"]}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "description": "错误信息"}}, "required": ["error"]}}}}, "500": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "description": "错误信息"}}, "required": ["error"]}}}}}}}}}