package org.jeecg.modules.system.service;

import org.jeecg.modules.system.dto.RegisterDTO;
import org.jeecg.common.api.vo.Result;

/**
 * @Description: 用户注册服务接口
 * @Author: jeecg-boot
 * @Date: 2025-06-19
 * @Version: V1.0
 */
public interface IUserRegisterService {

    /**
     * 用户注册
     * @param registerDTO 注册信息
     * @param ipAddress IP地址
     * @return 注册结果
     */
    Result<?> register(RegisterDTO registerDTO, String ipAddress);

    /**
     * 检查用户名是否可用
     * @param username 用户名
     * @param type 类型：phone-手机号，email-邮箱
     * @return 是否可用
     */
    Result<?> checkUsername(String username, String type);

    /**
     * 验证邀请码
     * @param inviteCode 邀请码
     * @return 验证结果
     */
    Result<?> validateInviteCode(String inviteCode);

    /**
     * 生成邀请码
     * @param userId 用户ID
     * @return 邀请码
     */
    String generateInviteCode(String userId);

    /**
     * 验证密码强度
     * @param password 密码
     * @return 是否符合要求
     */
    boolean validatePassword(String password);

    /**
     * 检查注册频率限制
     * @param ipAddress IP地址
     * @param phone 手机号
     * @param email 邮箱
     * @return 是否可以注册
     */
    boolean canRegister(String ipAddress, String phone, String email);
}
