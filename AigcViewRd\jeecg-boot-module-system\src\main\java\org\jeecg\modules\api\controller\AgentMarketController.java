package org.jeecg.modules.api.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.util.JwtUtil;
import org.jeecg.modules.demo.aigc_agent.entity.AigcAgent;
import org.jeecg.modules.demo.aigc_agent.service.IAigcAgentService;
import org.jeecg.modules.demo.userprofile.entity.AicgUserProfile;
import org.jeecg.modules.demo.userprofile.service.IAicgUserProfileService;
import org.jeecg.modules.demo.aigc_agent.entity.AigcWorkflow;
import org.jeecg.modules.demo.aigc_agent.service.IAigcWorkflowService;

import org.jeecg.modules.api.vo.AgentMarketVO;
import org.jeecg.modules.jianying.service.TosService;
import org.jeecg.modules.system.service.ISysUserService;
import org.jeecg.modules.system.service.AlipayService;
import org.jeecg.modules.system.entity.SysUser;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import com.alibaba.fastjson.JSON;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.stream.Collectors;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;

/**
 * @Description: 智能体市场API
 * @Author: AigcView
 * @Date: 2025-07-31
 * @Version: V1.0
 */
@Api(tags = "智能体市场API")
@RestController
@RequestMapping("/api/agent/market")
@Slf4j
public class AgentMarketController {

    @Autowired
    private IAigcAgentService aigcAgentService;

    @Autowired
    private IAicgUserProfileService userProfileService;

    @Autowired
    private TosService tosService;

    @Autowired
    private AlipayService alipayService;

    @Autowired
    private ISysUserService sysUserService;

    @Autowired
    private IAigcWorkflowService aigcWorkflowService;

    @Autowired
    private JdbcTemplate jdbcTemplate;



    /**
     * 获取智能体市场列表
     * 注意：此接口为公开接口，无需登录即可访问，但不返回敏感信息
     */
    @AutoLog(value = "智能体市场-分页列表查询")
    @ApiOperation(value = "智能体市场-分页列表查询", notes = "获取审核通过的智能体列表，支持搜索和筛选，公开接口无需认证")
    @GetMapping(value = "/list")
    public Result<?> getAgentMarketList(
            @ApiParam("页码") @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
            @ApiParam("页大小") @RequestParam(name = "pageSize", defaultValue = "12") Integer pageSize,
            @ApiParam("智能体名称") @RequestParam(name = "agentName", required = false) String agentName,
            @ApiParam("作者类型") @RequestParam(name = "authorType", required = false) String authorType,
            @ApiParam("购买状态") @RequestParam(name = "purchaseStatus", required = false) String purchaseStatus,
            HttpServletRequest request) {

        try {
            // 构建查询条件
            QueryWrapper<AigcAgent> queryWrapper = new QueryWrapper<>();
            
            // 只查询审核通过的智能体
            queryWrapper.eq("audit_status", "2");
            
            // 按名称搜索
            if (StringUtils.hasText(agentName)) {
                queryWrapper.like("agent_name", agentName);
            }
            
            // 按作者类型筛选
            if (StringUtils.hasText(authorType)) {
                queryWrapper.eq("author_type", authorType);
            }
            
            // 按创建时间倒序排列
            queryWrapper.orderByDesc("create_time");

            // 根据是否有购买状态筛选决定查询方式
            IPage<AigcAgent> pageList;
            if ("purchased".equals(purchaseStatus) || "unpurchased".equals(purchaseStatus)) {
                // 有购买状态筛选时，查询更多数据以确保筛选后有足够的记录
                Page<AigcAgent> page = new Page<>(1, pageNo * pageSize * 3); // 查询3倍数据
                pageList = aigcAgentService.page(page, queryWrapper);
            } else {
                // 无筛选时正常分页
                Page<AigcAgent> page = new Page<>(pageNo, pageSize);
                pageList = aigcAgentService.page(page, queryWrapper);
            }

            // 获取用户角色用于价格计算
            String userRole = getUserRole(request);

            // 🔥 获取当前用户的购买记录（一次性查询，避免N+1问题）
            Set<String> purchasedAgentIds = new HashSet<>();
            try {
                String username = JwtUtil.getUserNameByToken(request);
                if (StringUtils.hasText(username)) {
                    SysUser user = sysUserService.getUserByName(username);
                    if (user != null) {
                        String sql = "SELECT agent_id FROM aicg_user_agent_purchase WHERE user_id = ? AND status = 1";
                        List<String> purchased = jdbcTemplate.queryForList(sql, String.class, user.getId());
                        purchasedAgentIds.addAll(purchased);
                        log.debug("🔍 用户 {} 已购买智能体数量: {}", username, purchasedAgentIds.size());
                    }
                }
            } catch (Exception e) {
                log.debug("获取用户购买记录失败（可能未登录）: {}", e.getMessage());
            }

            // 转换为VO对象并处理返回数据
            List<AigcAgent> records = pageList.getRecords();
            List<AgentMarketVO> voList = new ArrayList<>();

            for (AigcAgent agent : records) {
                AgentMarketVO vo = new AgentMarketVO();
                BeanUtils.copyProperties(agent, vo);

                // 处理智能体头像和视频的CDN URL
                if (StringUtils.hasText(agent.getAgentAvatar())) {
                    vo.setAgentAvatar(tosService.generateFileUrl(agent.getAgentAvatar()));
                }
                if (StringUtils.hasText(agent.getDemoVideo())) {
                    vo.setDemoVideo(tosService.generateFileUrl(agent.getDemoVideo()));
                }

                // 设置原价格
                vo.setOriginalPrice(agent.getPrice());

                // 🔥 设置购买状态（后端直接返回，前端无需再次查询）
                boolean isPurchased = purchasedAgentIds.contains(agent.getId());
                vo.setIsPurchased(isPurchased);
                log.debug("🔍 智能体 {} 购买状态: {}", agent.getAgentName(), isPurchased);

                // 获取创作者信息
                enrichAgentWithCreatorInfo(vo, agent.getCreateBy());

                // 获取工作流数量
                enrichAgentWithWorkflowCount(vo, agent.getId());

                // 计算价格折扣
                enrichAgentWithPriceInfo(vo, userRole, agent.getAuthorType());

                // 设置统计信息（暂时使用默认值）
                vo.setStatistics(0L, 0L, 0L, 0L);

                voList.add(vo);
            }

            // 根据购买状态筛选
            if ("purchased".equals(purchaseStatus)) {
                voList = filterPurchasedOrFree(voList, userRole);
            } else if ("unpurchased".equals(purchaseStatus)) {
                voList = filterUnpurchased(voList, userRole);
            }

            // 如果有筛选，需要手动分页
            if ("purchased".equals(purchaseStatus) || "unpurchased".equals(purchaseStatus)) {
                // 手动分页
                int startIndex = (pageNo - 1) * pageSize;
                int endIndex = Math.min(startIndex + pageSize, voList.size());

                if (startIndex < voList.size()) {
                    voList = voList.subList(startIndex, endIndex);
                } else {
                    voList = new ArrayList<>();
                }
            }

            // 构建返回的分页对象
            Page<AgentMarketVO> voPage = new Page<>(pageNo, pageSize);
            voPage.setRecords(voList);

            // 设置总数
            if ("purchased".equals(purchaseStatus) || "unpurchased".equals(purchaseStatus)) {
                // 有筛选时，计算准确总数
                long totalCount = calculateFilteredTotal(queryWrapper, purchaseStatus, userRole, purchasedAgentIds);
                voPage.setTotal(totalCount);
            } else {
                voPage.setTotal(pageList.getTotal());
            }

            voPage.setSize(pageList.getSize());
            voPage.setCurrent(pageList.getCurrent());
            voPage.setPages(pageList.getPages());

            return Result.OK(voPage);

        } catch (Exception e) {
            log.error("获取智能体市场列表失败", e);
            return Result.error("获取智能体市场列表失败");
        }
    }

    /**
     * 筛选已购买或免费的智能体
     */
    private List<AgentMarketVO> filterPurchasedOrFree(List<AgentMarketVO> agents, String userRole) {
        return agents.stream().filter(agent -> {
            // 1. 已购买的智能体
            if (agent.getIsPurchased() != null && agent.getIsPurchased()) {
                return true;
            }

            // 2. 根据用户角色判断免费智能体
            if ("SVIP".equals(userRole)) {
                // SVIP用户：官方智能体免费
                return "1".equals(agent.getAuthorType()) &&
                       agent.getIsFree() != null && agent.getIsFree();
            } else {
                // VIP和普通用户：只显示已购买的（没有免费智能体）
                return false;
            }
        }).collect(Collectors.toList());
    }

    /**
     * 筛选未购买的智能体
     */
    private List<AgentMarketVO> filterUnpurchased(List<AgentMarketVO> agents, String userRole) {
        return agents.stream().filter(agent -> {
            // 1. 未购买的智能体
            if (agent.getIsPurchased() == null || !agent.getIsPurchased()) {
                // 2. 排除SVIP用户的免费智能体（因为免费相当于已拥有）
                if ("SVIP".equals(userRole) && "1".equals(agent.getAuthorType()) &&
                    agent.getIsFree() != null && agent.getIsFree()) {
                    return false; // SVIP免费智能体不算未购买
                }
                return true; // 其他未购买的智能体
            }
            return false;
        }).collect(Collectors.toList());
    }

    /**
     * 计算筛选后的总数
     */
    private long calculateFilteredTotal(QueryWrapper<AigcAgent> queryWrapper, String purchaseStatus,
                                       String userRole, Set<String> purchasedAgentIds) {
        // 查询所有符合条件的智能体
        List<AigcAgent> allAgents = aigcAgentService.list(queryWrapper);

        // 转换为VO并计算价格信息
        List<AgentMarketVO> allVoList = new ArrayList<>();
        for (AigcAgent agent : allAgents) {
            AgentMarketVO vo = new AgentMarketVO();
            BeanUtils.copyProperties(agent, vo);
            vo.setOriginalPrice(agent.getPrice());
            boolean isPurchased = purchasedAgentIds.contains(agent.getId());
            vo.setIsPurchased(isPurchased);
            enrichAgentWithPriceInfo(vo, userRole, agent.getAuthorType());
            allVoList.add(vo);
        }

        // 根据购买状态筛选
        List<AgentMarketVO> filteredList;
        if ("purchased".equals(purchaseStatus)) {
            filteredList = filterPurchasedOrFree(allVoList, userRole);
        } else {
            filteredList = filterUnpurchased(allVoList, userRole);
        }

        return filteredList.size();
    }

    /**
     * 获取智能体详情
     */
    @AutoLog(value = "智能体详情-查询")
    @ApiOperation(value = "智能体详情-查询", notes = "获取智能体详细信息，包括基本信息和价格计算")
    @GetMapping(value = "/detail/{agentId}")
    public Result<?> getAgentDetail(@PathVariable String agentId, HttpServletRequest request) {
        try {
            log.info("🔍 获取智能体详情 - ID: {}", agentId);

            // 查询智能体基本信息
            AigcAgent agent = aigcAgentService.getById(agentId);
            if (agent == null) {
                return Result.error("智能体不存在");
            }

            // 检查审核状态
            if (!"2".equals(agent.getAuditStatus())) {
                return Result.error("智能体未通过审核");
            }

            // 构建返回数据
            Map<String, Object> result = new HashMap<>();
            result.put("id", agent.getId());
            result.put("agentName", agent.getAgentName());
            result.put("agentDescription", agent.getAgentDescription());

            // 🔥 智能体头像使用CDN URL
            String agentAvatarUrl = "";
            if (StringUtils.hasText(agent.getAgentAvatar())) {
                agentAvatarUrl = tosService.generateFileUrl(agent.getAgentAvatar());
                log.debug("智能体头像CDN URL: {} -> {}", agent.getAgentAvatar(), agentAvatarUrl);
            }
            result.put("agentAvatar", agentAvatarUrl);

            // 🔥 演示视频使用CDN URL
            String demoVideoUrl = "";
            if (StringUtils.hasText(agent.getDemoVideo())) {
                demoVideoUrl = tosService.generateFileUrl(agent.getDemoVideo());
                log.debug("演示视频CDN URL: {} -> {}", agent.getDemoVideo(), demoVideoUrl);
            }
            result.put("demoVideo", demoVideoUrl);

            result.put("price", agent.getPrice());
            result.put("authorType", agent.getAuthorType());
            result.put("experienceLink", agent.getExperienceLink());

            // 获取真实的创作者信息
            Map<String, Object> creatorInfo = getCreatorInfo(agent.getCreateBy(), agent.getAuthorType());
            result.put("creatorInfo", creatorInfo);

            // 获取用户角色并计算价格信息
            String userRole = getUserRole(request);
            Map<String, Object> priceInfo = calculatePriceInfo(agent, userRole);
            result.putAll(priceInfo);

            // 🔥 直接包含工作流列表，减少前端请求次数
            try {
                // 🔐 检查用户购买状态
                String username = null;
                boolean isPurchased = false;
                String token = request.getHeader("X-Access-Token");
                if (StringUtils.hasText(token)) {
                    username = JwtUtil.getUsername(token);
                    if (StringUtils.hasText(username)) {
                        SysUser user = sysUserService.getUserByName(username);
                        if (user != null) {
                            String sql = "SELECT COUNT(*) FROM aicg_user_agent_purchase WHERE user_id = ? AND agent_id = ? AND status = 1";
                            Integer count = jdbcTemplate.queryForObject(sql, Integer.class, user.getId(), agentId);
                            isPurchased = count != null && count > 0;

                            // 如果没有购买记录，检查SVIP用户对官方智能体的免费权限
                            if (!isPurchased) {
                                if ("SVIP".equals(userRole) && "1".equals(agent.getAuthorType())) {
                                    isPurchased = true;
                                    log.info("🎯 SVIP用户对官方智能体免费访问 - 用户: {}, 智能体: {}", username, agentId);
                                }
                            }

                            log.info("🔍 用户购买状态检查 - 用户: {}, 智能体: {}, 已购买: {}", username, agentId, isPurchased);
                        }
                    }
                }

                // 查询工作流列表
                QueryWrapper<AigcWorkflow> workflowQuery = new QueryWrapper<>();
                workflowQuery.eq("agent_id", agentId);
                workflowQuery.orderByAsc("create_time");
                List<AigcWorkflow> workflowList = aigcWorkflowService.list(workflowQuery);

                List<Map<String, Object>> workflowData = new ArrayList<>();
                for (AigcWorkflow workflow : workflowList) {
                    Map<String, Object> workflowInfo = new HashMap<>();
                    workflowInfo.put("id", workflow.getId());
                    workflowInfo.put("workflowName", workflow.getWorkflowName());
                    workflowInfo.put("workflowDescription", workflow.getWorkflowDescription());
                    workflowInfo.put("inputParamsDesc", workflow.getInputParamsDesc()); // 🔥 新增输入参数说明
                    workflowInfo.put("workflowId", workflow.getWorkflowId());
                    workflowInfo.put("isPurchased", isPurchased);

                    // 🔐 只有已购买用户才返回工作流压缩包下载地址
                    if (isPurchased && StringUtils.hasText(workflow.getWorkflowPackage())) {
                        // 已购买用户可以看到下载地址，添加CDN前缀
                        String packagePath = workflow.getWorkflowPackage();

                        // 🌐 为工作流压缩包添加CDN前缀
                        if (!packagePath.startsWith("http")) {
                            // 如果不是完整URL，添加CDN前缀
                            String cdnDomain = "https://cdn.aigcview.com";
                            if (!packagePath.startsWith("/")) {
                                packagePath = "/" + packagePath;
                            }
                            packagePath = cdnDomain + packagePath;
                        }

                        workflowInfo.put("workflowPackage", packagePath);
                        workflowInfo.put("packagePath", packagePath); // 兼容前端字段名
                        log.debug("✅ 用户已购买，返回工作流CDN下载地址 - 工作流: {}, URL: {}", workflow.getWorkflowName(), packagePath);
                    } else {
                        // 未购买用户不返回下载地址
                        log.debug("🔒 用户未购买，隐藏工作流下载地址 - 工作流: {}", workflow.getWorkflowName());
                    }

                    workflowData.add(workflowInfo);
                }

                result.put("workflowList", workflowData);
                result.put("isPurchased", isPurchased);

                log.info("✅ 智能体详情查询成功 - 名称: {}, 工作流数量: {}", agent.getAgentName(), workflowData.size());
            } catch (Exception e) {
                log.warn("⚠️ 获取工作流列表失败，但不影响智能体详情返回: {}", e.getMessage());
                result.put("workflowList", new ArrayList<>());
                result.put("isPurchased", false);
            }

            return Result.OK(result);

        } catch (Exception e) {
            log.error("❌ 获取智能体详情失败 - ID: {}", agentId, e);
            return Result.error("获取智能体详情失败: " + e.getMessage());
        }
    }

    /**
     * 获取智能体的工作流列表
     */
    @AutoLog(value = "智能体工作流-列表查询")
    @ApiOperation(value = "智能体工作流-列表查询", notes = "获取指定智能体下的所有工作流")
    @GetMapping(value = "/{agentId}/workflows")
    public Result<?> getAgentWorkflows(@PathVariable String agentId, HttpServletRequest request) {
        try {
            log.info("🔍 获取智能体工作流列表 - 智能体ID: {}", agentId);

            // 验证智能体是否存在
            AigcAgent agent = aigcAgentService.getById(agentId);
            if (agent == null) {
                return Result.error("智能体不存在");
            }

            // 检查审核状态
            if (!"2".equals(agent.getAuditStatus())) {
                return Result.error("智能体未通过审核");
            }

            // 🔐 检查用户购买状态
            boolean isPurchased = false;
            String username = null;

            try {
                String token = request.getHeader("X-Access-Token");
                if (StringUtils.hasText(token)) {
                    username = JwtUtil.getUsername(token);
                    if (StringUtils.hasText(username)) {
                        // 🔐 检查用户是否已购买该智能体
                        SysUser user = sysUserService.getUserByName(username);
                        if (user != null) {
                            String sql = "SELECT COUNT(*) FROM aicg_user_agent_purchase WHERE user_id = ? AND agent_id = ? AND status = 1";
                            Integer count = jdbcTemplate.queryForObject(sql, Integer.class, user.getId(), agentId);
                            isPurchased = count != null && count > 0;
                            log.info("🔍 用户购买状态检查 - 用户: {}, 智能体: {}, 已购买: {}", username, agentId, isPurchased);
                        }
                    }
                }
            } catch (Exception e) {
                log.warn("🔍 用户身份验证失败: {}", e.getMessage());
            }

            // 查询真实的工作流数据
            List<Map<String, Object>> workflows = new ArrayList<>();

            try {
                // 从数据库查询该智能体的工作流
                QueryWrapper<AigcWorkflow> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("agent_id", agentId);
                List<AigcWorkflow> workflowList = aigcWorkflowService.list(queryWrapper);

                if (workflowList != null && !workflowList.isEmpty()) {
                    // 有真实工作流数据时，转换为前端需要的格式
                    for (AigcWorkflow workflow : workflowList) {
                        Map<String, Object> workflowData = new HashMap<>();
                        workflowData.put("id", workflow.getWorkflowId());

                        // 🔥 前端期望的字段名是 workflowName、workflowDescription 和 inputParamsDesc
                        workflowData.put("workflowName", workflow.getWorkflowName());
                        workflowData.put("workflowDescription", workflow.getWorkflowDescription());
                        workflowData.put("inputParamsDesc", workflow.getInputParamsDesc()); // 🔥 新增输入参数说明

                        // 🔥 智能体头像使用CDN URL
                        String avatarUrl = "";
                        if (StringUtils.hasText(agent.getAgentAvatar())) {
                            avatarUrl = tosService.generateFileUrl(agent.getAgentAvatar());
                        }
                        workflowData.put("agentAvatar", avatarUrl);

                        // 🔒 工作流列表不返回下载地址，实际下载在智能体详情页处理
                        // 移除敏感的下载地址信息，防止安全漏洞
                        log.debug("🔒 工作流列表不返回下载地址，确保安全");

                        workflowData.put("type", 1); // 默认类型
                        workflowData.put("createTime", workflow.getCreateTime());
                        workflows.add(workflowData);

                        log.debug("工作流数据: ID={}, 名称={}, 描述={}",
                            workflow.getWorkflowId(), workflow.getWorkflowName(), workflow.getWorkflowDescription());
                    }
                    log.info("✅ 查询到真实工作流数据 - 数量: {}", workflowList.size());
                } else {
                    // 没有真实工作流数据时，根据智能体类型生成示例工作流
                    log.info("⚠️ 未找到真实工作流数据，生成示例数据");

                    if ("1".equals(agent.getAuthorType())) {
                        // 官方智能体 - 更多工作流
                        workflows.add(createWorkflowData("demo-1", agent.getAgentName() + " - 基础对话流程",
                            "适用于日常对话和基础问答的工作流程", agent.getAgentAvatar(), 1));
                        workflows.add(createWorkflowData("demo-2", agent.getAgentName() + " - 高级分析流程",
                            "用于复杂数据分析和深度思考的高级工作流", agent.getAgentAvatar(), 2));
                        workflows.add(createWorkflowData("demo-3", agent.getAgentName() + " - 创意生成流程",
                            "专门用于创意内容生成和头脑风暴的工作流", agent.getAgentAvatar(), 3));
                    } else {
                        // 创作者智能体 - 较少工作流
                        workflows.add(createWorkflowData("demo-1", agent.getAgentName() + " - 核心功能流程",
                            "智能体的核心功能和主要应用场景", agent.getAgentAvatar(), 1));
                        workflows.add(createWorkflowData("demo-2", agent.getAgentName() + " - 扩展应用流程",
                            "扩展功能和特殊应用场景的工作流程", agent.getAgentAvatar(), 2));
                    }
                }
            } catch (Exception e) {
                log.error("❌ 查询工作流数据失败，使用示例数据: {}", e.getMessage());

                // 查询失败时生成示例数据
                if ("1".equals(agent.getAuthorType())) {
                    workflows.add(createWorkflowData("fallback-1", agent.getAgentName() + " - 基础功能",
                        "智能体的基础功能演示", agent.getAgentAvatar(), 1));
                } else {
                    workflows.add(createWorkflowData("fallback-1", agent.getAgentName() + " - 核心功能",
                        "智能体的核心功能演示", agent.getAgentAvatar(), 1));
                }
            }

            log.info("✅ 智能体工作流列表查询成功 - 数量: {}", workflows.size());

            return Result.OK(workflows);

        } catch (Exception e) {
            log.error("❌ 获取智能体工作流列表失败 - 智能体ID: {}", agentId, e);
            return Result.error("获取工作流列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户已购买的智能体列表
     */
    @AutoLog(value = "用户购买记录-查询")
    @ApiOperation(value = "用户购买记录-查询", notes = "获取当前用户已购买的智能体ID列表")
    @GetMapping(value = "/purchase/list")
    public Result<?> getUserPurchasedAgents(HttpServletRequest request) {
        try {
            // 获取用户信息
            String token = request.getHeader("X-Access-Token");
            if (!StringUtils.hasText(token)) {
                return Result.error("用户未登录");
            }

            String username = JwtUtil.getUsername(token);
            if (!StringUtils.hasText(username)) {
                return Result.error("无效的用户令牌");
            }

            log.info("🔍 获取用户已购买智能体列表 - 用户: {}", username);

            // 🔐 查询用户购买记录表
            List<String> purchasedAgentIds = new ArrayList<>();
            SysUser user = sysUserService.getUserByName(username);
            if (user != null) {
                String sql = "SELECT agent_id FROM aicg_user_agent_purchase WHERE user_id = ? AND status = 1 ORDER BY purchase_time DESC";
                purchasedAgentIds = jdbcTemplate.queryForList(sql, String.class, user.getId());
            }

            // 模拟数据 - 可以根据需要添加一些测试数据
            // purchasedAgentIds.add("test-agent-id-1");

            log.info("✅ 用户已购买智能体列表查询成功 - 数量: {}", purchasedAgentIds.size());

            return Result.OK(purchasedAgentIds);

        } catch (Exception e) {
            log.error("❌ 获取用户已购买智能体列表失败", e);
            return Result.error("获取购买记录失败: " + e.getMessage());
        }
    }

    /**
     * 检查智能体购买状态
     */
    @AutoLog(value = "智能体购买状态-检查")
    @ApiOperation(value = "智能体购买状态-检查", notes = "检查用户是否已购买指定智能体")
    @GetMapping(value = "/purchase/check/{agentId}")
    public Result<?> checkAgentPurchaseStatus(@PathVariable String agentId, HttpServletRequest request) {
        try {
            // 获取用户信息
            String token = request.getHeader("X-Access-Token");
            if (!StringUtils.hasText(token)) {
                // 未登录用户返回未购买状态
                Map<String, Object> result = new HashMap<>();
                result.put("isPurchased", false);
                result.put("purchaseTime", null);
                result.put("purchasePrice", null);
                return Result.OK(result);
            }

            String username = JwtUtil.getUsername(token);
            if (!StringUtils.hasText(username)) {
                return Result.error("无效的用户令牌");
            }

            log.info("🔍 检查智能体购买状态 - 用户: {}, 智能体ID: {}", username, agentId);

            // 🔐 查询用户购买记录和智能体信息
            Map<String, Object> result = new HashMap<>();
            SysUser user = sysUserService.getUserByName(username);
            boolean isPurchased = false;

            if (user != null) {
                // 1. 先检查数据库购买记录
                String sql = "SELECT COUNT(*) FROM aicg_user_agent_purchase WHERE user_id = ? AND agent_id = ? AND status = 1";
                Integer count = jdbcTemplate.queryForObject(sql, Integer.class, user.getId(), agentId);
                isPurchased = count != null && count > 0;

                // 2. 如果没有购买记录，检查SVIP用户对官方智能体的免费权限
                if (!isPurchased) {
                    String userRole = getUserRole(request);
                    AigcAgent agent = aigcAgentService.getById(agentId);

                    if ("SVIP".equals(userRole) && agent != null && "1".equals(agent.getAuthorType())) {
                        // SVIP用户对官方智能体有免费访问权限
                        isPurchased = true;
                        log.info("🎯 SVIP用户对官方智能体免费访问 - 用户: {}, 智能体: {}", username, agentId);
                    }
                }
            }

            result.put("isPurchased", isPurchased);
            result.put("purchaseTime", null);
            result.put("purchasePrice", null);

            log.info("✅ 智能体购买状态检查完成 - 结果: {}", isPurchased ? "已购买/有权限" : "未购买");

            return Result.OK(result);

        } catch (Exception e) {
            log.error("❌ 检查智能体购买状态失败 - 智能体ID: {}", agentId, e);
            return Result.error("检查购买状态失败: " + e.getMessage());
        }
    }

    /**
     * 购买智能体
     */
    @AutoLog(value = "智能体购买-创建订单")
    @ApiOperation(value = "智能体购买-创建订单", notes = "用户购买智能体，创建购买记录和交易订单")
    @PostMapping(value = "/purchase")
    @Transactional(rollbackFor = Exception.class)
    public Result<?> purchaseAgent(@RequestBody Map<String, Object> purchaseData, HttpServletRequest request) {
        try {
            // 获取用户信息
            String token = request.getHeader("X-Access-Token");
            if (!StringUtils.hasText(token)) {
                return Result.error("用户未登录");
            }

            String username = JwtUtil.getUsername(token);
            if (!StringUtils.hasText(username)) {
                return Result.error("无效的用户令牌");
            }

            String agentId = (String) purchaseData.get("agentId");
            String agentName = (String) purchaseData.get("agentName");
            BigDecimal purchasePrice = new BigDecimal(purchaseData.get("purchasePrice").toString());
            BigDecimal originalPrice = new BigDecimal(purchaseData.get("originalPrice").toString());
            Integer discountRate = (Integer) purchaseData.get("discountRate");

            log.info("🛒 用户购买智能体 - 用户: {}, 智能体: {}, 价格: {}", username, agentName, purchasePrice);

            // 获取用户信息
            SysUser user = sysUserService.getUserByName(username);
            if (user == null) {
                return Result.error("用户不存在");
            }

            // 验证智能体是否存在
            AigcAgent agent = aigcAgentService.getById(agentId);
            if (agent == null) {
                return Result.error("智能体不存在");
            }

            // 获取用户角色信息 - 通过用户名判断
            String userRole = "普通用户"; // 默认值
            if ("admin".equals(username)) {
                userRole = "管理员";
            } else if (user.getIsAuthor() != null && user.getIsAuthor() == 1) {
                userRole = "创作者";
            }

            // 检查用户是否已有该智能体的购买记录
            String checkPurchaseSql = "SELECT id, status FROM aicg_user_agent_purchase WHERE user_id = ? AND agent_id = ?";
            List<Map<String, Object>> existingRecords = jdbcTemplate.queryForList(checkPurchaseSql, user.getId(), agentId);

            if (!existingRecords.isEmpty()) {
                Map<String, Object> existingRecord = existingRecords.get(0);
                Integer status = (Integer) existingRecord.get("status");
                String existingPurchaseId = (String) existingRecord.get("id");

                if (status == 1) {
                    // 已购买，不能重复购买
                    return Result.error("您已购买过该智能体，无需重复购买");
                } else if (status == 0) {
                    // 有待支付记录，返回现有记录信息
                    log.info("🔄 复用现有待支付记录 - 购买ID: {}", existingPurchaseId);

                    // 返回现有订单信息
                    Map<String, Object> orderInfo = new HashMap<>();
                    orderInfo.put("purchaseId", existingPurchaseId);
                    orderInfo.put("transactionId", "TXN_" + System.currentTimeMillis()); // 生成新的交易ID
                    orderInfo.put("agentId", agentId);
                    orderInfo.put("agentName", agentName);
                    orderInfo.put("purchasePrice", purchasePrice);
                    orderInfo.put("originalPrice", originalPrice);
                    orderInfo.put("discountRate", discountRate);
                    orderInfo.put("userRole", userRole);
                    orderInfo.put("status", "pending_payment"); // 待支付状态

                    log.info("✅ 复用智能体购买订单 - 用户: {}, 智能体: {}, 价格: {}", username, agentName, purchasePrice);
                    return Result.OK(orderInfo);
                }
            }

            // 创建购买记录 - 初始状态为待支付
            String insertSql = "INSERT INTO aicg_user_agent_purchase (" +
                "id, user_id, agent_id, transaction_id, purchase_price, original_price, discount_rate, " +
                "user_role_at_purchase, purchase_time, agent_name, agent_description, author_type, author_name, " +
                "status, create_by, create_time) " +
                "VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW(), ?, ?, ?, ?, 0, ?, NOW())";

            // 生成UUID格式的ID
            String purchaseId = UUID.randomUUID().toString().replace("-", "");
            String transactionId = "TXN_" + System.currentTimeMillis();

            jdbcTemplate.update(insertSql,
                purchaseId,
                user.getId(),
                agentId,
                transactionId,
                purchasePrice,
                originalPrice,
                discountRate,
                userRole,
                agentName,
                agent.getAgentDescription() != null ? agent.getAgentDescription() : "",
                agent.getAuthorType() != null ? agent.getAuthorType() : "",
                agent.getCreateBy() != null ? agent.getCreateBy() : "",
                username
            );

            log.info("✅ 购买记录创建成功（待支付状态） - 购买ID: {}, 交易ID: {}", purchaseId, transactionId);

            // 🔥 返回订单信息，等待用户选择支付方式
            Map<String, Object> orderInfo = new HashMap<>();
            orderInfo.put("purchaseId", purchaseId);
            orderInfo.put("transactionId", transactionId);
            orderInfo.put("agentId", agentId);
            orderInfo.put("agentName", agentName);
            orderInfo.put("purchasePrice", purchasePrice);
            orderInfo.put("originalPrice", originalPrice);
            orderInfo.put("discountRate", discountRate);
            orderInfo.put("userRole", userRole);
            orderInfo.put("status", "pending_payment"); // 待支付状态

            log.info("✅ 智能体购买订单创建成功 - 用户: {}, 智能体: {}, 价格: {}", username, agentName, purchasePrice);

            return Result.OK(orderInfo);

        } catch (Exception e) {
            log.error("❌ 智能体购买失败", e);
            return Result.error("购买失败: " + e.getMessage());
        }
    }

    /**
     * 工作流下载权限验证
     */
    @AutoLog(value = "工作流下载-权限验证")
    @ApiOperation(value = "工作流下载-权限验证", notes = "验证用户是否有权限下载指定工作流")
    @GetMapping(value = "/workflow/download/check")
    public Result<?> checkWorkflowDownloadPermission(@RequestParam String workflowId,
                                                     @RequestParam String agentId,
                                                     HttpServletRequest request) {
        try {
            // 获取用户信息
            String token = request.getHeader("X-Access-Token");
            if (!StringUtils.hasText(token)) {
                return Result.error("用户未登录");
            }

            String username = JwtUtil.getUsername(token);
            if (!StringUtils.hasText(username)) {
                return Result.error("无效的用户令牌");
            }

            log.info("🔍 检查工作流下载权限 - 用户: {}, 工作流: {}, 智能体: {}", username, workflowId, agentId);

            // 验证智能体是否存在
            AigcAgent agent = aigcAgentService.getById(agentId);
            if (agent == null) {
                return Result.error("智能体不存在");
            }

            // 🔐 检查用户是否已购买该智能体或有免费权限
            boolean hasPermission = false;
            SysUser user = sysUserService.getUserByName(username);
            if (user != null) {
                String sql = "SELECT COUNT(*) FROM aicg_user_agent_purchase WHERE user_id = ? AND agent_id = ? AND status = 1";
                Integer count = jdbcTemplate.queryForObject(sql, Integer.class, user.getId(), agentId);
                hasPermission = count != null && count > 0;

                // 如果没有购买记录，检查SVIP用户对官方智能体的免费权限
                if (!hasPermission) {
                    String userRole = getUserRole(request);
                    if ("SVIP".equals(userRole) && "1".equals(agent.getAuthorType())) {
                        hasPermission = true;
                        log.info("🎯 SVIP用户对官方智能体免费下载权限 - 用户: {}, 智能体: {}", username, agentId);
                    }
                }

                log.info("🔍 下载权限检查 - 用户: {}, 智能体: {}, 有权限: {}", username, agentId, hasPermission);
            }

            Map<String, Object> result = new HashMap<>();
            result.put("hasPermission", hasPermission);
            result.put("workflowId", workflowId);
            result.put("agentId", agentId);

            if (hasPermission) {
                // 🔐 生成真实的工作流下载链接
                AigcWorkflow workflow = aigcWorkflowService.getById(workflowId);
                if (workflow != null && StringUtils.hasText(workflow.getWorkflowPackage())) {
                    String downloadUrl = tosService.generateFileUrl(workflow.getWorkflowPackage());
                    result.put("downloadUrl", downloadUrl);
                    result.put("message", "有权限下载");
                    log.info("✅ 生成下载链接成功 - 工作流: {}", workflowId);
                } else {
                    result.put("downloadUrl", "");
                    result.put("message", "工作流文件不存在");
                }
            } else {
                result.put("message", "请先购买该智能体");
            }

            log.info("✅ 工作流下载权限检查完成 - 结果: {}", hasPermission ? "有权限" : "无权限");

            return Result.OK(result);

        } catch (Exception e) {
            log.error("❌ 检查工作流下载权限失败", e);
            return Result.error("权限检查失败: " + e.getMessage());
        }
    }

    /**
     * 获取创作者信息
     * @param createBy 创建者用户ID
     * @param authorType 作者类型：1-官方，2-创作者
     * @return 创作者信息Map
     */
    private Map<String, Object> getCreatorInfo(String createBy, String authorType) {
        Map<String, Object> creatorInfo = new HashMap<>();

        try {
            // 统一查询真实用户信息，不再根据authorType区分
            if (StringUtils.hasText(createBy)) {
                // 查询用户基本信息
                SysUser user = sysUserService.getUserByName(createBy);
                if (user != null) {
                    String creatorName = "创作者";
                    String creatorAvatar = tosService.getDefaultAvatarUrl();

                    // 查询用户扩展信息
                    AicgUserProfile userProfile = userProfileService.getByUserId(user.getId());

                    if (userProfile != null && StringUtils.hasText(userProfile.getNickname())) {
                        // 优先使用扩展表中的昵称
                        creatorName = userProfile.getNickname();

                        // 处理头像URL
                        if (StringUtils.hasText(userProfile.getAvatar())) {
                            // 有头像时使用TOS服务生成正确的头像URL
                            creatorAvatar = tosService.generateFileUrl(userProfile.getAvatar());
                            log.debug("获取创作者头像成功: userId={}, avatarPath={}, avatarUrl={}",
                                user.getId(), userProfile.getAvatar(), creatorAvatar);
                        } else {
                            // 没有头像时使用TOS默认头像
                            creatorAvatar = tosService.getDefaultAvatarUrl();
                            log.debug("使用默认头像: userId={}, defaultUrl={}", user.getId(), creatorAvatar);
                        }
                    } else {
                        // 扩展表没有昵称时，优先使用真实姓名，否则使用用户名
                        creatorName = StringUtils.hasText(user.getRealname()) ? user.getRealname() : user.getUsername();
                        // 没有扩展信息时使用TOS默认头像
                        creatorAvatar = tosService.getDefaultAvatarUrl();
                        log.debug("没有扩展信息，使用默认头像: userId={}, defaultUrl={}", user.getId(), creatorAvatar);
                    }

                    creatorInfo.put("name", creatorName);
                    creatorInfo.put("nickname", creatorName);
                    creatorInfo.put("avatar", creatorAvatar);

                    log.debug("创作者信息设置完成: createBy={}, creatorName={}, creatorAvatar={}",
                        createBy, creatorName, creatorAvatar);
                } else {
                    // 用户不存在时使用默认信息
                    creatorInfo.put("name", "未知创作者");
                    creatorInfo.put("nickname", "未知创作者");
                    creatorInfo.put("avatar", tosService.getDefaultAvatarUrl());
                    log.warn("创作者用户不存在: createBy={}", createBy);
                }
            } else {
                // createBy为空时使用默认信息
                creatorInfo.put("name", "匿名创作者");
                creatorInfo.put("nickname", "匿名创作者");
                creatorInfo.put("avatar", tosService.getDefaultAvatarUrl());
                log.warn("创作者ID为空");
            }
        } catch (Exception e) {
            log.error("获取创作者信息失败: createBy={}, authorType={}", createBy, authorType, e);
            // 异常时返回默认信息
            creatorInfo.put("name", "未知创作者");
            creatorInfo.put("nickname", "未知创作者");
            creatorInfo.put("avatar", tosService.getDefaultAvatarUrl());
        }

        return creatorInfo;
    }

    /**
     * 获取智能体市场统计信息
     */
    @AutoLog(value = "智能体市场-统计信息")
    @ApiOperation(value = "智能体市场-统计信息", notes = "获取智能体总数、创作者数量等统计信息")
    @GetMapping(value = "/stats")
    public Result<?> getMarketStats() {
        try {
            Map<String, Object> stats = new HashMap<>();
            
            // 统计审核通过的智能体总数
            QueryWrapper<AigcAgent> totalWrapper = new QueryWrapper<>();
            totalWrapper.eq("audit_status", "2");
            long totalAgents = aigcAgentService.count(totalWrapper);
            
            // 统计创作者数量（去重）
            String creatorCountSql = "SELECT COUNT(DISTINCT create_by) FROM aigc_agent WHERE audit_status = '2' AND author_type = '2'";
            List<Map<String, Object>> creatorResult = jdbcTemplate.queryForList(creatorCountSql);
            long totalCreators = creatorResult.isEmpty() ? 0 : ((Number) creatorResult.get(0).get("COUNT(DISTINCT create_by)")).longValue();
            
            stats.put("totalAgents", totalAgents);
            stats.put("totalCreators", totalCreators);
            
            return Result.OK(stats);
            
        } catch (Exception e) {
            log.error("获取智能体市场统计信息失败", e);
            return Result.error("获取统计信息失败");
        }
    }

    /**
     * 获取用户角色
     */
    private String getUserRole(HttpServletRequest request) {
        try {
            String token = request.getHeader("X-Access-Token");
            if (!StringUtils.hasText(token)) {
                return "user"; // 默认普通用户
            }

            String username = JwtUtil.getUsername(token);
            AicgUserProfile userProfile = userProfileService.getByUsername(username);
            if (userProfile == null) {
                return "user";
            }

            String userId = userProfile.getUserId();

            // 查询用户角色
            String sql = "SELECT r.role_code FROM sys_user u " +
                        "JOIN sys_user_role ur ON u.id = ur.user_id " +
                        "JOIN sys_role r ON ur.role_id = r.id " +
                        "WHERE u.id = ? AND r.role_code IN ('user', 'VIP', 'SVIP')";

            List<Map<String, Object>> roleResult = jdbcTemplate.queryForList(sql, userId);

            if (!roleResult.isEmpty()) {
                String userRole = (String) roleResult.get(0).get("role_code");
                if ("VIP".equals(userRole) || "SVIP".equals(userRole)) {
                    return userRole;
                }
            }

            return "user";

        } catch (Exception e) {
            log.error("获取用户角色失败", e);
            return "user";
        }
    }

    /**
     * 丰富智能体的创作者信息（从用户扩展表获取nickname和avatar）
     */
    private void enrichAgentWithCreatorInfo(AgentMarketVO vo, String createBy) {
        try {
            if (StringUtils.hasText(createBy)) {
                // 1. 先获取用户基本信息
                SysUser user = sysUserService.getUserByName(createBy);
                if (user != null) {
                    // 2. 根据用户ID获取扩展表中的昵称和头像
                    AicgUserProfile userProfile = userProfileService.getByUserId(user.getId());

                    String creatorName;
                    String creatorAvatar = null;

                    if (userProfile != null && StringUtils.hasText(userProfile.getNickname())) {
                        // 优先使用扩展表中的昵称
                        creatorName = userProfile.getNickname();

                        // 处理头像URL（参考订阅会员快速充值的头像逻辑）
                        if (StringUtils.hasText(userProfile.getAvatar())) {
                            // 有头像时使用TOS服务生成正确的头像URL（支持CDN优先）
                            creatorAvatar = tosService.generateFileUrl(userProfile.getAvatar());
                            log.debug("获取创作者头像成功: userId={}, avatarPath={}, avatarUrl={}",
                                user.getId(), userProfile.getAvatar(), creatorAvatar);
                        } else {
                            // 没有头像时使用TOS默认头像
                            creatorAvatar = tosService.getDefaultAvatarUrl();
                            log.debug("使用默认头像: userId={}, defaultUrl={}", user.getId(), creatorAvatar);
                        }
                    } else {
                        // 扩展表没有昵称时，优先使用真实姓名，否则使用用户名
                        creatorName = StringUtils.hasText(user.getRealname()) ? user.getRealname() : user.getUsername();
                        // 没有扩展信息时使用TOS默认头像
                        creatorAvatar = tosService.getDefaultAvatarUrl();
                        log.debug("没有扩展信息，使用默认头像: userId={}, defaultUrl={}", user.getId(), creatorAvatar);
                    }

                    // 设置创作者信息到VO
                    vo.setCreatorInfo(user.getUsername(), user.getRealname());
                    vo.setCreatorName(creatorName);
                    vo.setCreatorAvatar(creatorAvatar);

                    log.debug("创作者信息设置完成: createBy={}, creatorName={}, creatorAvatar={}",
                        createBy, creatorName, creatorAvatar);
                } else {
                    // 用户不存在时使用TOS默认头像
                    String defaultAvatarUrl = tosService.getDefaultAvatarUrl();
                    vo.setCreatorAvatar(defaultAvatarUrl);
                    log.debug("用户不存在，使用默认头像: createBy={}, defaultUrl={}", createBy, defaultAvatarUrl);
                }
            } else {
                // 没有创建人信息时使用TOS默认头像
                String defaultAvatarUrl = tosService.getDefaultAvatarUrl();
                vo.setCreatorAvatar(defaultAvatarUrl);
                log.debug("没有创建人信息，使用默认头像: defaultUrl={}", defaultAvatarUrl);
            }
        } catch (Exception e) {
            log.warn("获取创作者信息失败，创建人: {}", createBy, e);
            // 异常时使用TOS默认头像
            try {
                String defaultAvatarUrl = tosService.getDefaultAvatarUrl();
                vo.setCreatorAvatar(defaultAvatarUrl);
            } catch (Exception ex) {
                log.error("获取默认头像也失败", ex);
            }
        }
    }

    /**
     * 丰富智能体的工作流数量信息
     */
    private void enrichAgentWithWorkflowCount(AgentMarketVO vo, String agentId) {
        try {
            String sql = "SELECT COUNT(*) FROM aigc_workflow WHERE agent_id = ?";
            List<Map<String, Object>> countResult = jdbcTemplate.queryForList(sql, agentId);

            if (!countResult.isEmpty()) {
                int workflowCount = ((Number) countResult.get(0).get("COUNT(*)")).intValue();
                vo.setWorkflowCount(workflowCount);
            } else {
                vo.setWorkflowCount(0);
            }
        } catch (Exception e) {
            log.warn("获取工作流数量失败: {}", e.getMessage());
            vo.setWorkflowCount(0);
        }
    }

    /**
     * 计算智能体价格信息（用于详情接口）
     */
    private Map<String, Object> calculatePriceInfo(AigcAgent agent, String userRole) {
        Map<String, Object> priceInfo = new HashMap<>();

        BigDecimal originalPrice = agent.getPrice() != null ? agent.getPrice() : BigDecimal.ZERO;
        BigDecimal discountPrice = originalPrice;
        int discountRate = 0;
        boolean isFree = false;
        boolean showSvipPromo = false;
        boolean showDiscountPrice = false;

        // 根据作者类型和用户角色计算折扣
        if ("1".equals(agent.getAuthorType())) {
            // 官方智能体
            if ("SVIP".equals(userRole)) {
                isFree = true;
                discountPrice = BigDecimal.ZERO;
                discountRate = 100;
                showDiscountPrice = false;
            } else if ("VIP".equals(userRole)) {
                discountPrice = originalPrice.multiply(new BigDecimal("0.7")).setScale(2, RoundingMode.DOWN);
                discountRate = 70; // 7折 = 70%，前端用 discountRate / 100
                showDiscountPrice = true;
            } else {
                // 普通用户或未登录用户显示SVIP推广
                showSvipPromo = true;
                showDiscountPrice = false;
            }
        } else if ("2".equals(agent.getAuthorType())) {
            // 创作者智能体
            if ("SVIP".equals(userRole)) {
                discountPrice = originalPrice.multiply(new BigDecimal("0.5")).setScale(2, RoundingMode.DOWN);
                discountRate = 50; // 5折 = 50%，前端用 discountRate / 100
                showDiscountPrice = true;
            } else if ("VIP".equals(userRole)) {
                discountPrice = originalPrice.multiply(new BigDecimal("0.7")).setScale(2, RoundingMode.DOWN);
                discountRate = 70; // 7折 = 70%，前端用 discountRate / 100
                showDiscountPrice = true;
            } else {
                // 普通用户或未登录用户显示SVIP推广
                showSvipPromo = true;
                showDiscountPrice = false;
            }
        }

        priceInfo.put("originalPrice", originalPrice);
        priceInfo.put("discountPrice", discountPrice);
        priceInfo.put("discountRate", discountRate);
        priceInfo.put("isFree", isFree);
        priceInfo.put("showSvipPromo", showSvipPromo);
        priceInfo.put("showDiscountPrice", showDiscountPrice);



        return priceInfo;
    }

    /**
     * 丰富智能体的价格信息（包含折扣计算）
     */
    private void enrichAgentWithPriceInfo(AgentMarketVO vo, String userRole, String authorType) {
        try {
            BigDecimal originalPrice = vo.getOriginalPrice() != null ? vo.getOriginalPrice() : BigDecimal.ZERO;
            BigDecimal discountPrice = originalPrice;
            int discountRate = 0;
            boolean isFree = false;

            // 根据作者类型和用户角色计算折扣
            if ("1".equals(authorType)) {
                // 官方智能体
                if ("SVIP".equals(userRole)) {
                    isFree = true;
                    discountPrice = BigDecimal.ZERO;
                    discountRate = 100;
                } else if ("VIP".equals(userRole)) {
                    discountPrice = originalPrice.multiply(new BigDecimal("0.7")).setScale(2, RoundingMode.DOWN);
                    discountRate = 70; // 7折 = 70%
                }
            } else if ("2".equals(authorType)) {
                // 创作者智能体
                if ("SVIP".equals(userRole)) {
                    discountPrice = originalPrice.multiply(new BigDecimal("0.5")).setScale(2, RoundingMode.DOWN);
                    discountRate = 50; // 5折 = 50%
                } else if ("VIP".equals(userRole)) {
                    discountPrice = originalPrice.multiply(new BigDecimal("0.7")).setScale(2, RoundingMode.DOWN);
                    discountRate = 70; // 7折 = 70%
                }
            }

            // 设置价格信息到VO
            vo.setPriceInfo(originalPrice, discountPrice, discountRate, isFree);

        } catch (Exception e) {
            log.warn("计算价格折扣失败: {}", e.getMessage());
            // 设置默认价格信息
            vo.setPriceInfo(vo.getOriginalPrice(), vo.getOriginalPrice(), 0, false);
        }
    }

    /**
     * 创建工作流数据
     */
    private Map<String, Object> createWorkflowData(String id, String name, String description, String avatar, int sequence) {
        Map<String, Object> workflow = new HashMap<>();
        workflow.put("id", id);
        workflow.put("workflowName", name);
        workflow.put("workflowDescription", description);

        // 🔥 头像使用CDN URL
        String avatarUrl = "";
        if (StringUtils.hasText(avatar)) {
            avatarUrl = tosService.generateFileUrl(avatar);
        }
        workflow.put("agentAvatar", avatarUrl);

        workflow.put("sequence", sequence);
        return workflow;
    }

    /**
     * 余额支付智能体
     */
    @AutoLog(value = "智能体购买-余额支付")
    @ApiOperation(value = "智能体购买-余额支付", notes = "使用账户余额支付智能体购买订单")
    @PostMapping(value = "/purchase/balance-pay")
    @Transactional(rollbackFor = Exception.class)
    public Result<?> balancePayAgent(@RequestBody Map<String, Object> paymentData, HttpServletRequest request) {
        try {
            // 获取用户信息
            String token = request.getHeader("X-Access-Token");
            if (!StringUtils.hasText(token)) {
                return Result.error("用户未登录");
            }

            String username = JwtUtil.getUsername(token);
            if (!StringUtils.hasText(username)) {
                return Result.error("无效的用户令牌");
            }

            String purchaseId = (String) paymentData.get("purchaseId");
            String agentId = (String) paymentData.get("agentId");
            BigDecimal purchasePrice = new BigDecimal(paymentData.get("purchasePrice").toString());

            log.info("💰 用户余额支付智能体 - 用户: {}, 购买ID: {}, 金额: {}", username, purchaseId, purchasePrice);

            // 获取用户信息
            SysUser user = sysUserService.getUserByName(username);
            if (user == null) {
                return Result.error("用户不存在");
            }

            // 检查是否已有该用户+智能体的购买记录
            String existingRecordSql = "SELECT id, status FROM aicg_user_agent_purchase WHERE user_id = (SELECT id FROM sys_user WHERE username = ?) AND agent_id = ?";
            List<Map<String, Object>> existingRecords = jdbcTemplate.queryForList(existingRecordSql, username, agentId);

            String actualPurchaseId = purchaseId;

            if (!existingRecords.isEmpty()) {
                Map<String, Object> existingRecord = existingRecords.get(0);
                Integer status = (Integer) existingRecord.get("status");
                String existingId = (String) existingRecord.get("id");

                if (status == 1) {
                    // 已购买，不能重复购买
                    return Result.error("您已购买过该智能体");
                } else if (status == 0) {
                    // 有待支付记录，复用现有记录
                    actualPurchaseId = existingId;
                    log.info("🔄 复用现有待支付记录 - 购买ID: {}", actualPurchaseId);
                }
            } else {
                // 检查传入的purchaseId是否存在
                String checkSql = "SELECT COUNT(*) FROM aicg_user_agent_purchase WHERE id = ? AND create_by = ? AND status = 0";
                Integer count = jdbcTemplate.queryForObject(checkSql, Integer.class, purchaseId, username);
                if (count == null || count == 0) {
                    return Result.error("购买记录不存在或状态不正确");
                }
            }

            // 检查用户余额是否足够
            if (!userProfileService.checkBalance(user.getId(), purchasePrice)) {
                BigDecimal currentBalance = userProfileService.getUserBalance(user.getId());
                return Result.error("账户余额不足，当前余额：¥" + currentBalance + "，需要：¥" + purchasePrice);
            }

            // 使用consume方法扣减余额并创建正确的智能体购买交易记录
            boolean deductSuccess = userProfileService.consume(user.getId(), purchasePrice,
                "购买智能体: " + paymentData.get("agentName"), username);

            if (!deductSuccess) {
                return Result.error("余额扣减失败，请重试");
            }

            // 支付成功：更新状态并分配创作者收益（consume方法已创建交易记录，不再重复创建）
            updatePurchaseStatusAndDistributeEarnings(actualPurchaseId, agentId, purchasePrice, username);

            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("message", "余额支付成功！您现在可以使用该智能体了");
            result.put("purchaseId", actualPurchaseId);
            result.put("paymentMethod", "balance");
            result.put("paidAmount", purchasePrice);

            log.info("✅ 智能体余额支付成功 - 用户: {}, 购买ID: {}, 金额: {}", username, actualPurchaseId, purchasePrice);

            return Result.OK(result);

        } catch (Exception e) {
            log.error("❌ 智能体余额支付失败", e);
            return Result.error("余额支付失败: " + e.getMessage());
        }
    }

    /**
     * 支付宝支付智能体
     */
    @AutoLog(value = "智能体购买-支付宝支付")
    @ApiOperation(value = "智能体购买-支付宝支付", notes = "使用支付宝支付智能体购买订单")
    @PostMapping(value = "/purchase/alipay")
    @Transactional(rollbackFor = Exception.class)
    public Result<?> alipayAgent(@RequestBody Map<String, Object> paymentData, HttpServletRequest request) {
        try {
            // 获取用户信息
            String token = request.getHeader("X-Access-Token");
            if (!StringUtils.hasText(token)) {
                return Result.error("用户未登录");
            }

            String username = JwtUtil.getUsername(token);
            if (!StringUtils.hasText(username)) {
                return Result.error("无效的用户令牌");
            }

            String purchaseId = (String) paymentData.get("purchaseId");
            String agentId = (String) paymentData.get("agentId");
            String agentName = (String) paymentData.get("agentName");
            BigDecimal purchasePrice = new BigDecimal(paymentData.get("purchasePrice").toString());

            log.info("💳 用户支付宝支付智能体 - 用户: {}, 购买ID: {}, 金额: {}", username, purchaseId, purchasePrice);

            // 检查是否已有该用户+智能体的购买记录
            String existingRecordSql = "SELECT id, status FROM aicg_user_agent_purchase WHERE user_id = (SELECT id FROM sys_user WHERE username = ?) AND agent_id = ?";
            List<Map<String, Object>> existingRecords = jdbcTemplate.queryForList(existingRecordSql, username, agentId);

            String actualPurchaseId = purchaseId;

            if (!existingRecords.isEmpty()) {
                Map<String, Object> existingRecord = existingRecords.get(0);
                Integer status = (Integer) existingRecord.get("status");
                String existingId = (String) existingRecord.get("id");

                if (status == 1) {
                    // 已购买，不能重复购买
                    return Result.error("您已购买过该智能体");
                } else if (status == 0) {
                    // 有待支付记录，复用现有记录
                    actualPurchaseId = existingId;
                    log.info("🔄 复用现有待支付记录 - 购买ID: {}", actualPurchaseId);
                }
            } else {
                // 检查传入的purchaseId是否存在
                String checkSql = "SELECT COUNT(*) FROM aicg_user_agent_purchase WHERE id = ? AND create_by = ? AND status = 0";
                Integer count = jdbcTemplate.queryForObject(checkSql, Integer.class, purchaseId, username);
                if (count == null || count == 0) {
                    return Result.error("购买记录不存在或状态不正确");
                }
            }

            // 创建支付宝支付的交易记录（待支付状态）
            createAlipayTransactionRecord(actualPurchaseId, agentId, agentName, purchasePrice, username);

            // 调用支付宝支付服务
            String subject = "智界Aigc智能体 - " + agentName;
            String body = "购买智能体：" + agentName + "，金额：¥" + purchasePrice;

            String payForm = alipayService.createPayOrder(actualPurchaseId, purchasePrice, subject, body);

            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("purchaseId", actualPurchaseId);
            result.put("payForm", payForm);
            result.put("paymentMethod", "alipay");
            result.put("amount", purchasePrice);

            log.info("✅ 智能体支付宝订单创建成功 - 用户: {}, 购买ID: {}, 金额: {}", username, actualPurchaseId, purchasePrice);

            return Result.OK(result);

        } catch (Exception e) {
            log.error("❌ 智能体支付宝支付失败", e);
            return Result.error("支付宝支付失败: " + e.getMessage());
        }
    }



    /**
     * 🔥 创建智能体购买的统一交易记录
     * 与订阅会员保持一致的订单记录系统
     */
    @Transactional(rollbackFor = Exception.class)
    private void createAgentPurchaseTransaction(String purchaseId, String agentId, BigDecimal purchasePrice, String username) {
        try {
            log.info("📝 创建智能体购买交易记录 - 购买ID: {}, 智能体ID: {}, 金额: {}, 用户: {}",
                    purchaseId, agentId, purchasePrice, username);

            // 获取智能体信息
            String agentSql = "SELECT agent_name, agent_description, create_by FROM aigc_agent WHERE id = ?";
            List<Map<String, Object>> agentResult = jdbcTemplate.queryForList(agentSql, agentId);

            if (agentResult.isEmpty()) {
                log.warn("⚠️ 未找到智能体信息 - 智能体ID: {}", agentId);
                return;
            }

            Map<String, Object> agent = agentResult.get(0);
            String agentName = (String) agent.get("agent_name");
            String agentDescription = (String) agent.get("agent_description");
            String authorName = (String) agent.get("create_by");

            // 构建产品信息JSON（与订阅会员格式保持一致）
            Map<String, Object> productInfo = new HashMap<>();
            productInfo.put("agentId", agentId);
            productInfo.put("agentName", agentName);
            productInfo.put("agentDescription", agentDescription);
            productInfo.put("authorName", authorName);
            productInfo.put("purchaseId", purchaseId);
            productInfo.put("purchaseType", "agent");

            String productInfoJson = new com.fasterxml.jackson.databind.ObjectMapper().writeValueAsString(productInfo);

            // 插入统一交易记录表
            String transactionId = generateTransactionId();
            String insertTransactionSql = "INSERT INTO aicg_user_transaction (" +
                "id, user_id, order_id, order_type, transaction_type, amount, order_status, " +
                "product_info, payment_method, create_by, create_time, update_by, update_time) " +
                "VALUES (?, (SELECT id FROM sys_user WHERE username = ?), ?, 'agent', 6, ?, 3, ?, 'balance', ?, NOW(), ?, NOW())";

            jdbcTemplate.update(insertTransactionSql,
                transactionId, username, purchaseId, purchasePrice, productInfoJson, username, username);

            log.info("✅ 智能体购买交易记录创建成功 - 交易ID: {}, 购买ID: {}", transactionId, purchaseId);

        } catch (Exception e) {
            log.error("❌ 创建智能体购买交易记录失败 - 购买ID: {}, 错误: {}", purchaseId, e.getMessage(), e);
            // 不抛出异常，避免影响主要的购买流程
        }
    }

    /**
     * 生成交易ID
     */
    private String generateTransactionId() {
        return "TXN" + System.currentTimeMillis() + String.format("%04d", (int)(Math.random() * 10000));
    }

    /**
     * 🔥 处理支付流程（已废弃，保留用于兼容）
     * 当前阶段模拟支付成功，未来可接入真实支付接口
     */
    @Deprecated
    private boolean processPayment(String purchaseId, String transactionId, BigDecimal amount, String username) {
        try {
            log.info("💳 开始处理支付 - 购买ID: {}, 交易ID: {}, 金额: {}, 用户: {}",
                    purchaseId, transactionId, amount, username);

            // 🔥 当前阶段：模拟支付流程
            // 未来可以在这里接入真实的支付接口（支付宝、微信支付等）

            // 模拟支付处理时间
            Thread.sleep(500);

            // 模拟支付成功（当前阶段100%成功，未来根据真实支付结果返回）
            boolean paymentResult = true;

            if (paymentResult) {
                log.info("✅ 支付处理成功 - 购买ID: {}, 交易ID: {}", purchaseId, transactionId);
            } else {
                log.warn("❌ 支付处理失败 - 购买ID: {}, 交易ID: {}", purchaseId, transactionId);
            }

            return paymentResult;

        } catch (Exception e) {
            log.error("❌ 支付处理异常 - 购买ID: {}, 错误: {}", purchaseId, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 🔥 支付成功确认并分配创作者收益
     * 只有在支付成功后才会调用此方法
     */
    @Transactional(rollbackFor = Exception.class)
    private void confirmPaymentAndDistributeEarnings(String purchaseId, String agentId, BigDecimal purchasePrice, String purchaserUsername) {
        try {
            log.info("🎯 开始支付确认和收益分配 - 购买ID: {}, 智能体ID: {}, 金额: {}, 购买者: {}",
                    purchaseId, agentId, purchasePrice, purchaserUsername);

            // 1. 更新购买记录状态为已支付
            String updateStatusSql = "UPDATE aicg_user_agent_purchase SET status = 1, update_time = NOW() WHERE id = ?";
            int statusUpdateResult = jdbcTemplate.update(updateStatusSql, purchaseId);

            if (statusUpdateResult > 0) {
                log.info("✅ 购买记录状态更新成功 - 购买ID: {}", purchaseId);
            } else {
                log.error("❌ 购买记录状态更新失败 - 购买ID: {}", purchaseId);
                throw new RuntimeException("购买记录状态更新失败");
            }

            // 2. 分配创作者收益
            distributeAgentEarnings(agentId, purchasePrice, purchaserUsername);

            // 3. 创建统一的交易记录（与订阅会员保持一致）
            createAgentPurchaseTransaction(purchaseId, agentId, purchasePrice, purchaserUsername);

            log.info("🎉 支付确认和收益分配完成 - 购买ID: {}", purchaseId);

        } catch (Exception e) {
            log.error("❌ 支付确认和收益分配失败 - 购买ID: {}, 错误: {}", purchaseId, e.getMessage(), e);
            throw e; // 重新抛出异常，触发事务回滚
        }
    }

    /**
     * 🔥 更新购买状态并分配收益（不创建重复的交易记录）
     * 用于余额支付，因为consume方法已经创建了交易记录
     */
    @Transactional(rollbackFor = Exception.class)
    private void updatePurchaseStatusAndDistributeEarnings(String purchaseId, String agentId, BigDecimal purchasePrice, String purchaserUsername) {
        try {
            log.info("🔄 开始更新购买状态并分配收益 - 购买ID: {}", purchaseId);

            // 1. 更新购买记录状态为已支付
            String updateSql = "UPDATE aicg_user_agent_purchase SET status = 1, purchase_time = NOW(), update_time = NOW() WHERE id = ?";
            int updateResult = jdbcTemplate.update(updateSql, purchaseId);

            if (updateResult == 0) {
                throw new RuntimeException("更新购买记录状态失败，购买ID: " + purchaseId);
            }

            log.info("✅ 购买记录状态更新成功 - 购买ID: {}", purchaseId);

            // 2. 分配创作者收益
            distributeAgentEarnings(agentId, purchasePrice, purchaserUsername);

            log.info("🎉 购买状态更新和收益分配完成 - 购买ID: {}", purchaseId);

        } catch (Exception e) {
            log.error("❌ 支付确认和收益分配失败 - 购买ID: {}, 错误: {}", purchaseId, e.getMessage(), e);
            throw e; // 重新抛出异常，触发事务回滚
        }
    }

    /**
     * 🔥 智能体创作者收益分配
     * 在支付成功确认后调用，更新销售次数和创作者收益
     */
    @Transactional(rollbackFor = Exception.class)
    private void distributeAgentEarnings(String agentId, BigDecimal purchasePrice, String purchaserUsername) {
        try {
            log.info("🎯 开始分配智能体收益 - 智能体ID: {}, 购买金额: {}, 购买者: {}",
                    agentId, purchasePrice, purchaserUsername);

            // 1. 获取智能体信息
            AigcAgent agent = aigcAgentService.getById(agentId);
            if (agent == null) {
                log.error("❌ 智能体不存在，无法分配收益 - 智能体ID: {}", agentId);
                return;
            }

            String creatorUsername = agent.getCreateBy();
            if (!StringUtils.hasText(creatorUsername)) {
                log.error("❌ 智能体创建者信息为空，无法分配收益 - 智能体ID: {}", agentId);
                return;
            }

            // 2. 更新智能体销售次数和收益字段
            String updateAgentSql = "UPDATE aigc_agent SET " +
                "sales_count = IFNULL(sales_count, 0) + 1, " +  // 销售次数+1
                "agent_revenue = IFNULL(agent_revenue, 0) + ?, " +  // 累加总收益
                "month_revenue = CASE " +
                    "WHEN DATE_FORMAT(last_sale_time, '%Y-%m') = DATE_FORMAT(NOW(), '%Y-%m') " +
                    "THEN IFNULL(month_revenue, 0) + ? " +  // 同月累加
                    "ELSE ? " +  // 新月重置
                "END, " +
                "last_sale_time = NOW() " +  // 更新最后销售时间
                "WHERE id = ?";

            int agentUpdateResult = jdbcTemplate.update(updateAgentSql, purchasePrice, purchasePrice, purchasePrice, agentId);

            if (agentUpdateResult > 0) {
                log.info("✅ 智能体数据更新成功 - 智能体ID: {}, 收益: {}", agentId, purchasePrice);
            } else {
                log.warn("⚠️ 智能体数据更新失败 - 智能体ID: {}", agentId);
            }

            // 3. 获取创作者用户ID
            SysUser creator = sysUserService.getUserByName(creatorUsername);
            if (creator == null) {
                log.error("❌ 创作者用户不存在，无法分配收益 - 创作者用户名: {}", creatorUsername);
                return;
            }

            // 4. 更新创作者收益（收益 = 用户实际支付金额）
            String updateEarningsSql = "UPDATE aicg_user_profile SET agent_earnings = IFNULL(agent_earnings, 0) + ? WHERE user_id = ?";
            int earningsUpdateResult = jdbcTemplate.update(updateEarningsSql, purchasePrice, creator.getId());

            if (earningsUpdateResult > 0) {
                log.info("✅ 创作者收益更新成功 - 创作者: {}, 收益金额: {}, 用户ID: {}",
                        creatorUsername, purchasePrice, creator.getId());
            } else {
                log.warn("⚠️ 创作者收益更新失败 - 创作者: {}, 用户ID: {}", creatorUsername, creator.getId());
            }

            // 5. 记录收益分配日志
            log.info("🎉 智能体收益分配完成 - 智能体: {} ({}), 创作者: {}, 收益: {}, 购买者: {}",
                    agent.getAgentName(), agentId, creatorUsername, purchasePrice, purchaserUsername);

        } catch (Exception e) {
            log.error("❌ 智能体收益分配失败 - 智能体ID: {}, 购买金额: {}, 错误: {}",
                    agentId, purchasePrice, e.getMessage(), e);
            // 收益分配失败不影响购买流程，只记录错误日志
        }
    }

    /**
     * 🔥 创建支付宝支付的交易记录（待支付状态）
     * 用于统一的支付宝异步通知处理
     */
    @Transactional(rollbackFor = Exception.class)
    private void createAlipayTransactionRecord(String purchaseId, String agentId, String agentName, BigDecimal purchasePrice, String username) {
        try {
            log.info("📝 创建支付宝支付交易记录 - 购买ID: {}, 智能体: {}, 金额: {}, 用户: {}",
                    purchaseId, agentName, purchasePrice, username);

            // 获取用户ID
            SysUser user = sysUserService.getUserByName(username);
            if (user == null) {
                log.error("❌ 用户不存在，无法创建交易记录 - 用户名: {}", username);
                return;
            }

            // 构建产品信息JSON
            Map<String, Object> productInfo = new HashMap<>();
            productInfo.put("agentId", agentId);
            productInfo.put("agentName", agentName);
            productInfo.put("paymentMethod", "alipay");
            String productInfoJson = JSON.toJSONString(productInfo);

            // 插入交易记录表（待支付状态）
            String transactionId = generateTransactionId();
            String insertTransactionSql = "INSERT INTO aicg_user_transaction (" +
                "id, user_id, related_order_id, order_type, transaction_type, amount, order_status, " +
                "product_info, description, create_by, create_time, update_by, update_time) " +
                "VALUES (?, ?, ?, 'agent', 6, ?, 1, ?, ?, ?, NOW(), ?, NOW())";

            jdbcTemplate.update(insertTransactionSql,
                transactionId, user.getId(), purchaseId, purchasePrice, productInfoJson,
                "智能体购买 - " + agentName, username, username);

            log.info("✅ 支付宝支付交易记录创建成功 - 交易ID: {}, 购买ID: {}", transactionId, purchaseId);

        } catch (Exception e) {
            log.error("❌ 创建支付宝支付交易记录失败 - 购买ID: {}, 错误: {}", purchaseId, e.getMessage(), e);
            // 不抛出异常，避免影响支付流程
        }
    }

}
