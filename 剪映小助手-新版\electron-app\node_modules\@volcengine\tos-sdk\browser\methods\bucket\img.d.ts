import TOSBase, { TosResponse } from '../base';
export interface ImageStyle {
    Name: string;
    Content: string;
    CreateTime: string;
    LastModifyTime: string;
}
export interface BucketImgStyle {
    bucket?: string;
    imageStyles: {
        ImageStyles: ImageStyle[];
    };
}
export interface ImageBriefInfo {
    Name: string;
    BucketLevelContent: string;
    PrefixCount: number;
}
export interface BucketImageBriefInfo {
    BucketName: string;
    ImageStyleBriefInfo: ImageBriefInfo[];
}
export interface GetImageStyleBriefInfoInput {
    bucket: string;
}
/**
 * @private unstable method
 */
export declare function getImageStyleBriefInfo(this: TOSBase, req: GetImageStyleBriefInfoInput): Promise<TosResponse<BucketImageBriefInfo>>;
/**
 * @private unstable method
 */
export declare function getBucketImageStyleList(this: TOSBase, bucket: string): Promise<TosResponse<BucketImgStyle['imageStyles']>>;
export interface GetBucketImageStyleListByNameInput {
    bucket: string;
    styleName: string;
}
/**
 * @private unstable method
 */
export declare function getBucketImageStyleListByName(this: TOSBase, req: GetBucketImageStyleListByNameInput): Promise<TosResponse<BucketImgStyle['imageStyles']>>;
/**
 * @private unstable method
 */
export declare function getBucketImageStyle(this: TOSBase, bucket: string, styleName: string): Promise<TosResponse<ImageStyle | null>>;
export interface PutBucketImageStyleInput {
    bucket: string;
    styleName: string;
    content: string;
    styleObjectPrefix?: string;
}
/**
 * @private unstable method
 */
export declare function putBucketImageStyle(this: TOSBase, req: PutBucketImageStyleInput): Promise<TosResponse<any>>;
export interface DeleteBucketImageStyleInput {
    bucket: string;
    styleName: string;
    styleObjectPrefix?: string;
}
/**
 * @private unstable method
 */
export declare function deleteBucketImageStyle(this: TOSBase, req: DeleteBucketImageStyleInput): Promise<TosResponse<any>>;
export interface BucketImgProtect {
    Enable: boolean;
    Suffixes?: string[];
    OIPRules?: any[];
    Prefix?: string;
    Suffix?: string;
}
/**
 * @private unstable method
 */
export declare function putBucketImageProtect(this: TOSBase, bucket: string, data: BucketImgProtect): Promise<TosResponse<any>>;
/**
 * @private unstable method
 */
export declare function getBucketImageProtect(this: TOSBase, bucket: string): Promise<TosResponse<any>>;
export declare type BucketImgProtectStyleSeparator = '-' | '_' | '!' | '\\';
export declare type BucketImgStyleSeparatorAffixes = Partial<Record<BucketImgProtectStyleSeparator, string>>;
export interface PutBucketImageStyleSeparatorInput {
    bucket: string;
    Separator: BucketImgProtectStyleSeparator[];
    SeparatorSuffix?: BucketImgStyleSeparatorAffixes;
}
/**
 * @private unstable method
 */
export declare function putBucketImageStyleSeparator(this: TOSBase, req: PutBucketImageStyleSeparatorInput): Promise<TosResponse<any>>;
/**
 * @private unstable method
 */
export declare function getBucketImageStyleSeparator(this: TOSBase, bucket: string): Promise<TosResponse<any>>;
