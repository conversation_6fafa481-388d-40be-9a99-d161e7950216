import TOSBase, { TosResponse } from '../base';
import { DataTransferStatus } from '../../interface';
import { HeadObjectInput, HeadObjectOutput } from './headObject';
import { CancelToken } from 'axios';
import { IRateLimiter } from '../../universal/rate-limiter';
export interface DownloadFileCheckpointRecord {
    bucket: string;
    key: string;
    version_id?: string;
    part_size: number;
    object_info: {
        etag: string;
        hash_crc64ecma: string;
        object_size: number;
        last_modified: string;
    };
    file_info: {
        file_path: string;
        temp_file_path: string;
    };
    parts_info: DownloadFileCheckpointRecordPartInfo[];
}
export interface DownloadFileCheckpointRecordPartInfo {
    part_number: number;
    range_start: number;
    range_end: number;
    hash_crc64ecma: string;
    is_completed: boolean;
}
export interface DownloadFileInput extends HeadObjectInput {
    filePath: string;
    /**
     * @private unstable tempFilePath
     */
    tempFilePath?: string;
    /**
     * default is 20 MB
     *
     * unit: B
     */
    partSize?: number;
    /**
     * the number of request to parallel upload part，default value is 1
     */
    taskNum?: number;
    /**
     * if checkpoint is a string and point to a exist file,
     * the checkpoint record will recover from this file.
     *
     * if checkpoint is a string and point to a directory,
     * the checkpoint will be auto generated,
     * and its name is `{bucketName}_{objectName}.{uploadId}`.
     */
    checkpoint?: string | DownloadFileCheckpointRecord;
    dataTransferStatusChange?: (status: DataTransferStatus) => void;
    /**
     * the simple progress feature
     * percent is [0, 1]
     */
    progress?: (percent: number, checkpoint: DownloadFileCheckpointRecord) => void;
    /**
     * the feature of pause and continue downloading
     */
    downloadEventChange?: (event: DownloadEvent) => void;
    /**
     * cancel this upload progress
     */
    cancelToken?: CancelToken;
    /**
     * unit: bit/s
     * server side traffic limit
     **/
    trafficLimit?: number;
    /**
     * only works for nodejs environment
     */
    rateLimiter?: IRateLimiter;
    /**
     * @private unstable
     * custom rename file to support not overwrite file
     */
    customRenameFileAfterDownloadCompleted?: (tempFilePath: string, filePath: string) => void;
}
export interface DownloadFileOutput extends HeadObjectOutput {
}
export interface DownloadEvent {
    type: DownloadEventType;
    err?: Error;
    bucket: string;
    key: string;
    versionId?: string;
    filePath: string;
    checkpointFile?: string;
    downloadPartInfo?: DownloadPartInfo;
}
export interface DownloadPartInfo {
    partNumber: number;
    rangeStart: number;
    rangeEnd: number;
}
export declare enum DownloadEventType {
    CreateTempFileSucceed = 1,
    CreateTempFileFailed = 2,
    DownloadPartSucceed = 3,
    DownloadPartFailed = 4,
    DownloadPartAborted = 5,
    RenameTempFileSucceed = 6,
    RenameTempFileFailed = 7
}
export declare function downloadFile(this: TOSBase, input: DownloadFileInput): Promise<TosResponse<DownloadFileOutput>>;
export default downloadFile;
