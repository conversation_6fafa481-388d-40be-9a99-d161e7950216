/**
 * 模拟服务器，用于测试版本检查功能
 * 提供各种测试场景的API响应
 */

const http = require('http');
const url = require('url');

class MockVersionServer {
  constructor(port = 3001) {
    this.port = port;
    this.server = null;
    this.scenarios = {
      normal: this.normalResponse,
      noUpdate: this.noUpdateResponse,
      forceUpdate: this.forceUpdateResponse,
      serverError: this.serverErrorResponse,
      timeout: this.timeoutResponse,
      invalidData: this.invalidDataResponse
    };
    this.currentScenario = 'normal';
  }

  /**
   * 启动模拟服务器
   */
  start() {
    return new Promise((resolve, reject) => {
      this.server = http.createServer((req, res) => {
        this.handleRequest(req, res);
      });

      this.server.listen(this.port, (error) => {
        if (error) {
          reject(error);
        } else {
          console.log(`🚀 模拟版本服务器启动在端口 ${this.port}`);
          resolve();
        }
      });
    });
  }

  /**
   * 停止模拟服务器
   */
  stop() {
    return new Promise((resolve) => {
      if (this.server) {
        this.server.close(() => {
          console.log('🛑 模拟版本服务器已停止');
          resolve();
        });
      } else {
        resolve();
      }
    });
  }

  /**
   * 设置测试场景
   */
  setScenario(scenario) {
    if (this.scenarios[scenario]) {
      this.currentScenario = scenario;
      console.log(`📝 切换到测试场景: ${scenario}`);
    } else {
      console.error(`❌ 未知的测试场景: ${scenario}`);
    }
  }

  /**
   * 处理请求
   */
  handleRequest(req, res) {
    const parsedUrl = url.parse(req.url, true);
    const pathname = parsedUrl.pathname;
    const query = parsedUrl.query;

    console.log(`📥 收到请求: ${req.method} ${pathname}`);

    // 设置CORS头
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

    if (req.method === 'OPTIONS') {
      res.writeHead(200);
      res.end();
      return;
    }

    // 路由处理
    if (pathname === '/aigcview/versioncontrol/getLatest') {
      this.handleVersionRequest(req, res, query);
    } else {
      res.writeHead(404, { 'Content-Type': 'application/json' });
      res.end(JSON.stringify({ error: 'Not Found' }));
    }
  }

  /**
   * 处理版本查询请求
   */
  handleVersionRequest(req, res, query) {
    const scenario = this.scenarios[this.currentScenario];
    if (scenario) {
      scenario.call(this, req, res, query);
    } else {
      this.normalResponse(req, res, query);
    }
  }

  /**
   * 正常响应场景
   */
  normalResponse(req, res, query) {
    const response = {
      success: true,
      result: {
        id: '1',
        programType: query.programType || 'desktop',
        versionNumber: '1.0.1',
        updateContent: '新增功能：\n1. 优化用户界面\n2. 修复已知问题\n3. 提升性能',
        downloadUrl: 'https://aigcview.com/download/desktop/v1.0.1',
        releaseDate: new Date().toISOString(),
        isLatest: 1,
        status: 1
      }
    };

    res.writeHead(200, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify(response));
  }

  /**
   * 无更新场景
   */
  noUpdateResponse(req, res, query) {
    const response = {
      success: true,
      result: {
        id: '1',
        programType: query.programType || 'desktop',
        versionNumber: '1.0.0', // 与当前版本相同
        updateContent: '当前版本',
        downloadUrl: 'https://aigcview.com/download/desktop/v1.0.0',
        releaseDate: new Date().toISOString(),
        isLatest: 1,
        status: 1
      }
    };

    res.writeHead(200, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify(response));
  }

  /**
   * 强制更新场景
   */
  forceUpdateResponse(req, res, query) {
    const response = {
      success: true,
      result: {
        id: '1',
        programType: query.programType || 'desktop',
        versionNumber: '1.1.0',
        updateContent: '安全更新：修复重要安全漏洞，建议立即更新',
        downloadUrl: 'https://aigcview.com/download/desktop/v1.1.0',
        releaseDate: new Date().toISOString(),
        isLatest: 1,
        status: 1
      }
    };

    res.writeHead(200, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify(response));
  }

  /**
   * 服务器错误场景
   */
  serverErrorResponse(req, res, query) {
    res.writeHead(500, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify({ error: 'Internal Server Error' }));
  }

  /**
   * 超时场景
   */
  timeoutResponse(req, res, query) {
    // 不响应，模拟超时
    console.log('⏰ 模拟超时，不发送响应');
  }

  /**
   * 无效数据场景
   */
  invalidDataResponse(req, res, query) {
    const response = {
      success: false,
      message: '数据格式错误'
    };

    res.writeHead(200, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify(response));
  }
}

/**
 * 集成测试类
 */
class IntegrationTest {
  constructor() {
    this.mockServer = new MockVersionServer();
    this.testResults = [];
  }

  /**
   * 运行集成测试
   */
  async runIntegrationTests() {
    console.log('🧪 开始运行集成测试');
    
    try {
      // 启动模拟服务器
      await this.mockServer.start();
      
      // 运行各种场景测试
      await this.testNormalScenario();
      await this.testNoUpdateScenario();
      await this.testForceUpdateScenario();
      await this.testServerErrorScenario();
      
      this.printResults();
      
    } catch (error) {
      console.error('❌ 集成测试失败:', error);
    } finally {
      // 停止模拟服务器
      await this.mockServer.stop();
    }
  }

  /**
   * 测试正常更新场景
   */
  async testNormalScenario() {
    console.log('\n📋 测试正常更新场景');
    this.mockServer.setScenario('normal');
    
    const VersionChecker = require('../src/utils/VersionChecker');
    const versionChecker = new VersionChecker({
      apiBaseUrl: 'http://localhost:3001',
      programType: 'desktop'
    });
    
    const result = await versionChecker.checkForUpdates(true);
    
    if (result.hasUpdate && result.latestVersion === '1.0.1') {
      console.log('✅ 正常更新场景测试通过');
      this.testResults.push({ name: '正常更新场景', status: 'PASS' });
    } else {
      console.log('❌ 正常更新场景测试失败');
      this.testResults.push({ name: '正常更新场景', status: 'FAIL' });
    }
  }

  /**
   * 测试无更新场景
   */
  async testNoUpdateScenario() {
    console.log('\n📋 测试无更新场景');
    this.mockServer.setScenario('noUpdate');
    
    const VersionChecker = require('../src/utils/VersionChecker');
    const versionChecker = new VersionChecker({
      apiBaseUrl: 'http://localhost:3001',
      programType: 'desktop'
    });
    
    const result = await versionChecker.checkForUpdates(true);
    
    if (!result.hasUpdate) {
      console.log('✅ 无更新场景测试通过');
      this.testResults.push({ name: '无更新场景', status: 'PASS' });
    } else {
      console.log('❌ 无更新场景测试失败');
      this.testResults.push({ name: '无更新场景', status: 'FAIL' });
    }
  }

  /**
   * 测试强制更新场景
   */
  async testForceUpdateScenario() {
    console.log('\n📋 测试强制更新场景');
    this.mockServer.setScenario('forceUpdate');
    
    const VersionChecker = require('../src/utils/VersionChecker');
    const versionChecker = new VersionChecker({
      apiBaseUrl: 'http://localhost:3001',
      programType: 'desktop'
    });
    
    const result = await versionChecker.checkForUpdates(true);
    
    if (result.hasUpdate && result.forceUpdate) {
      console.log('✅ 强制更新场景测试通过');
      this.testResults.push({ name: '强制更新场景', status: 'PASS' });
    } else {
      console.log('❌ 强制更新场景测试失败');
      this.testResults.push({ name: '强制更新场景', status: 'FAIL' });
    }
  }

  /**
   * 测试服务器错误场景
   */
  async testServerErrorScenario() {
    console.log('\n📋 测试服务器错误场景');
    this.mockServer.setScenario('serverError');
    
    const VersionChecker = require('../src/utils/VersionChecker');
    const versionChecker = new VersionChecker({
      apiBaseUrl: 'http://localhost:3001',
      programType: 'desktop',
      maxRetries: 1
    });
    
    const result = await versionChecker.checkForUpdates(true);
    
    if (result.networkError && result.errorType === 'server_error') {
      console.log('✅ 服务器错误场景测试通过');
      this.testResults.push({ name: '服务器错误场景', status: 'PASS' });
    } else {
      console.log('❌ 服务器错误场景测试失败');
      this.testResults.push({ name: '服务器错误场景', status: 'FAIL' });
    }
  }

  /**
   * 打印测试结果
   */
  printResults() {
    console.log('\n📊 集成测试结果:');
    this.testResults.forEach(result => {
      const status = result.status === 'PASS' ? '✅' : '❌';
      console.log(`${status} ${result.name}`);
    });
  }
}

// 如果直接运行此文件，启动集成测试
if (require.main === module) {
  const integrationTest = new IntegrationTest();
  integrationTest.runIntegrationTests();
}

module.exports = { MockVersionServer, IntegrationTest };
