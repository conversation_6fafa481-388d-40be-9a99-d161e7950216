<template>
  <a-modal
    title="敏感词检测测试"
    :width="900"
    :visible="visible"
    :footer="null"
    @cancel="handleCancel">
    
    <div class="sensitive-test-container">
      <!-- 输入区域 -->
      <div class="test-input-section">
        <h4>输入测试文本</h4>
        <a-textarea
          v-model="testText"
          placeholder="请输入要检测的文本内容..."
          :rows="6"
          :maxLength="5000"
          showCount
        />
        
        <div class="test-actions">
          <a-button type="primary" @click="handleCheck" :loading="checkLoading">
            <a-icon type="search"/>
            检测敏感词
          </a-button>
          <a-button @click="handleReplace" :loading="replaceLoading" :disabled="!checkResult || !checkResult.hasSensitiveWord">
            <a-icon type="edit"/>
            替换敏感词
          </a-button>
          <a-button @click="handleClear">
            <a-icon type="clear"/>
            清空
          </a-button>
        </div>
      </div>

      <!-- 检测结果区域 -->
      <div class="test-result-section" v-if="checkResult">
        <h4>检测结果</h4>
        
        <!-- 结果概览 -->
        <div class="result-summary">
          <a-alert
            :type="checkResult.hasSensitiveWord ? 'error' : 'success'"
            :message="checkResult.message"
            :description="checkResult.hasSensitiveWord ? `发现 ${checkResult.sensitiveWords.length} 个敏感词` : '文本内容安全'"
            showIcon
          />
        </div>

        <!-- 敏感词列表 -->
        <div v-if="checkResult.hasSensitiveWord && checkResult.sensitiveWords.length > 0" class="sensitive-words-list">
          <h5>检测到的敏感词：</h5>
          <div class="words-tags">
            <a-tag
              v-for="(word, index) in checkResult.sensitiveWords"
              :key="index"
              color="red"
              class="sensitive-word-tag"
            >
              {{ word }}
            </a-tag>
          </div>
        </div>

        <!-- 替换结果 -->
        <div v-if="replaceResult" class="replace-result">
          <h5>替换后的文本：</h5>
          <div class="replaced-text">
            <a-textarea
              :value="replaceResult"
              :rows="4"
              readonly
              class="replaced-textarea"
            />
            <a-button
              type="link"
              size="small"
              @click="copyReplacedText"
              class="copy-btn"
            >
              <a-icon type="copy"/>
              复制
            </a-button>
          </div>
        </div>
      </div>

      <!-- 快速测试示例 -->
      <div class="quick-test-section">
        <h4>快速测试示例</h4>
        <div class="example-buttons">
          <a-button
            v-for="(example, index) in testExamples"
            :key="index"
            size="small"
            @click="loadExample(example.text)"
            class="example-btn"
          >
            {{ example.name }}
          </a-button>
        </div>
      </div>

      <!-- 统计信息 -->
      <div class="test-stats" v-if="testStats">
        <h4>检测统计</h4>
        <a-row :gutter="16">
          <a-col :span="6">
            <a-statistic title="检测次数" :value="testStats.testCount" />
          </a-col>
          <a-col :span="6">
            <a-statistic title="发现敏感词" :value="testStats.sensitiveCount" />
          </a-col>
          <a-col :span="6">
            <a-statistic title="安全文本" :value="testStats.safeCount" />
          </a-col>
          <a-col :span="6">
            <a-statistic title="检测成功率" :value="testStats.successRate" suffix="%" />
          </a-col>
        </a-row>
      </div>
    </div>
  </a-modal>
</template>

<script>
  import { checkSensitiveWord, replaceSensitiveWord } from '@/api/system/sensitiveWord'

  export default {
    name: 'SensitiveWordTestModal',
    data() {
      return {
        visible: false,
        testText: '',
        checkResult: null,
        replaceResult: '',
        checkLoading: false,
        replaceLoading: false,
        testStats: {
          testCount: 0,
          sensitiveCount: 0,
          safeCount: 0,
          successRate: 100
        },
        testExamples: [
          { name: '正常文本', text: '这是一段正常的文本内容，没有任何问题。' },
          { name: '包含敏感词', text: '这段文本可能包含一些敏感词汇，需要进行检测。' },
          { name: '长文本测试', text: '这是一段比较长的文本内容，用来测试系统对长文本的处理能力。在实际应用中，用户可能会输入各种长度的文本，系统需要能够快速准确地检测出其中的敏感词汇，并给出相应的处理建议。' },
          { name: '混合内容', text: '这段文本包含中文、English、数字123、特殊符号@#$%，测试多语言混合内容的检测效果。' }
        ]
      }
    },
    methods: {
      show() {
        this.visible = true;
        this.resetForm();
      },
      
      handleCancel() {
        this.visible = false;
      },
      
      resetForm() {
        this.testText = '';
        this.checkResult = null;
        this.replaceResult = '';
      },
      
      handleCheck() {
        if (!this.testText.trim()) {
          this.$message.warning('请输入要检测的文本');
          return;
        }
        
        this.checkLoading = true;
        this.checkResult = null;
        this.replaceResult = '';
        
        checkSensitiveWord(this.testText).then((res) => {
          if (res.success) {
            this.checkResult = res.result;
            this.updateTestStats(res.result.hasSensitiveWord);
          } else {
            this.$message.error('检测失败：' + res.message);
          }
        }).catch((error) => {
          this.$message.error('检测失败：' + error.message);
        }).finally(() => {
          this.checkLoading = false;
        });
      },
      
      handleReplace() {
        if (!this.testText.trim()) {
          this.$message.warning('请输入要替换的文本');
          return;
        }
        
        this.replaceLoading = true;
        
        replaceSensitiveWord(this.testText, '*').then((res) => {
          if (res.success) {
            this.replaceResult = res.result;
          } else {
            this.$message.error('替换失败：' + res.message);
          }
        }).catch((error) => {
          this.$message.error('替换失败：' + error.message);
        }).finally(() => {
          this.replaceLoading = false;
        });
      },
      
      handleClear() {
        this.resetForm();
      },
      
      loadExample(text) {
        this.testText = text;
        this.checkResult = null;
        this.replaceResult = '';
      },
      
      copyReplacedText() {
        if (this.replaceResult) {
          this.$copyText(this.replaceResult).then(() => {
            this.$message.success('已复制到剪贴板');
          }).catch(() => {
            this.$message.error('复制失败');
          });
        }
      },
      
      updateTestStats(hasSensitive) {
        this.testStats.testCount++;
        if (hasSensitive) {
          this.testStats.sensitiveCount++;
        } else {
          this.testStats.safeCount++;
        }
        this.testStats.successRate = Math.round((this.testStats.safeCount / this.testStats.testCount) * 100);
      }
    }
  }
</script>

<style lang="less" scoped>
  .sensitive-test-container {
    .test-input-section {
      margin-bottom: 24px;
      
      h4 {
        margin-bottom: 12px;
        color: #262626;
        font-weight: 600;
      }
      
      .test-actions {
        margin-top: 12px;
        display: flex;
        gap: 8px;
      }
    }
    
    .test-result-section {
      margin-bottom: 24px;
      
      h4, h5 {
        margin-bottom: 12px;
        color: #262626;
        font-weight: 600;
      }
      
      .result-summary {
        margin-bottom: 16px;
      }
      
      .sensitive-words-list {
        margin-bottom: 16px;
        
        .words-tags {
          display: flex;
          flex-wrap: wrap;
          gap: 8px;
          
          .sensitive-word-tag {
            font-size: 12px;
            padding: 4px 8px;
          }
        }
      }
      
      .replace-result {
        .replaced-text {
          position: relative;
          
          .replaced-textarea {
            background-color: #f5f5f5;
          }
          
          .copy-btn {
            position: absolute;
            top: 8px;
            right: 8px;
          }
        }
      }
    }
    
    .quick-test-section {
      margin-bottom: 24px;
      
      h4 {
        margin-bottom: 12px;
        color: #262626;
        font-weight: 600;
      }
      
      .example-buttons {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
        
        .example-btn {
          font-size: 12px;
        }
      }
    }
    
    .test-stats {
      padding: 16px;
      background-color: #fafafa;
      border-radius: 6px;
      
      h4 {
        margin-bottom: 16px;
        color: #262626;
        font-weight: 600;
      }
    }
  }
</style>
