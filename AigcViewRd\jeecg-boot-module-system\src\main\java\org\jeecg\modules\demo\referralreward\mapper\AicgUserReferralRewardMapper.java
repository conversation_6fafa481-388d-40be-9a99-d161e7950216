package org.jeecg.modules.demo.referralreward.mapper;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.jeecg.modules.demo.referralreward.entity.AicgUserReferralReward;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: 推荐奖励记录表
 * @Author: jeecg-boot
 * @Date:   2025-06-18
 * @Version: V1.0
 */
public interface AicgUserReferralRewardMapper extends BaseMapper<AicgUserReferralReward> {

    /**
     * 根据推荐人ID查询奖励记录
     * @param referrerId 推荐人ID
     * @return 奖励记录列表
     */
    @Select("SELECT * FROM aicg_user_referral_reward WHERE referrer_id = #{referrerId} ORDER BY create_time DESC")
    List<AicgUserReferralReward> getByReferrerId(@Param("referrerId") String referrerId);
    
    /**
     * 根据推荐关系ID查询奖励记录
     * @param referralId 推荐关系ID
     * @return 奖励记录列表
     */
    @Select("SELECT * FROM aicg_user_referral_reward WHERE referral_id = #{referralId}")
    List<AicgUserReferralReward> getByReferralId(@Param("referralId") String referralId);
    
    /**
     * 统计推荐人的奖励数据
     * @param referrerId 推荐人ID
     * @return 奖励统计数据
     */
    @Select("SELECT " +
            "COUNT(*) as total_rewards, " +
            "COUNT(CASE WHEN status = 2 THEN 1 END) as paid_rewards, " +
            "COALESCE(SUM(reward_amount), 0) as total_reward_amount, " +
            "COALESCE(SUM(CASE WHEN status = 2 THEN reward_amount ELSE 0 END), 0) as paid_reward_amount, " +
            "COALESCE(SUM(CASE WHEN status = 1 THEN reward_amount ELSE 0 END), 0) as pending_reward_amount " +
            "FROM aicg_user_referral_reward WHERE referrer_id = #{referrerId}")
    Map<String, Object> getRewardStats(@Param("referrerId") String referrerId);
    
    /**
     * 查询待发放的奖励记录
     * @return 待发放的奖励记录列表
     */
    @Select("SELECT * FROM aicg_user_referral_reward WHERE status = 1 ORDER BY create_time ASC")
    List<AicgUserReferralReward> getPendingRewards();
    
    /**
     * 查询已发放的奖励记录
     * @param referrerId 推荐人ID
     * @return 已发放的奖励记录列表
     */
    @Select("SELECT * FROM aicg_user_referral_reward WHERE referrer_id = #{referrerId} AND status = 2 ORDER BY reward_time DESC")
    List<AicgUserReferralReward> getPaidRewards(@Param("referrerId") String referrerId);
    
    /**
     * 计算推荐人的可提现金额
     * @param referrerId 推荐人ID
     * @return 可提现金额
     */
    @Select("SELECT COALESCE(SUM(reward_amount), 0) FROM aicg_user_referral_reward WHERE referrer_id = #{referrerId} AND status = 2")
    BigDecimal getAvailableRewardAmount(@Param("referrerId") String referrerId);
}
