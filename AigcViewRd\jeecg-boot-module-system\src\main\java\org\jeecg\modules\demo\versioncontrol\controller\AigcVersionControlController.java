package org.jeecg.modules.demo.versioncontrol.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.demo.versioncontrol.entity.AigcVersionControl;
import org.jeecg.modules.demo.versioncontrol.service.IAigcVersionControlService;
import org.jeecg.modules.demo.versioncontrol.dto.PublicVersionInfo;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.system.base.controller.JeecgController;

/**
 * @Description: 程序版本控制表
 *
 * 🔒 安全说明：
 * - 公开接口（无需登录）：/getLatest - 仅返回必要的版本信息，不包含敏感字段
 * - 内部管理接口（需要登录）：其他所有接口 - 包含完整的管理信息，包括创建人、更新人、组织机构等
 *
 * @Author: jeecg-boot
 * @Date: 2025-01-09
 * @Version: V1.0
 */
@Api(tags="程序版本控制管理")
@RestController
@RequestMapping("/aigcview/versioncontrol")
@Slf4j
public class AigcVersionControlController extends JeecgController<AigcVersionControl, IAigcVersionControlService> {
	@Autowired
	private IAigcVersionControlService aigcVersionControlService;
	
	/**
	 * 分页列表查询
	 *
	 * @param aigcVersionControl
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	@AutoLog(value = "程序版本控制-分页列表查询")
	@ApiOperation(value="程序版本控制-分页列表查询", notes="程序版本控制-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(AigcVersionControl aigcVersionControl,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   @RequestParam(name="isLatest", required=false) Integer isLatest,
								   HttpServletRequest req) {
		try {
			// 创建查询条件，只包含数据库实际字段
			QueryWrapper<AigcVersionControl> queryWrapper = new QueryWrapper<>();

			// 手动添加查询条件，避免虚拟字段干扰
			if (aigcVersionControl.getProgramType() != null && !aigcVersionControl.getProgramType().isEmpty()) {
				queryWrapper.eq("program_type", aigcVersionControl.getProgramType());
			}
			if (aigcVersionControl.getVersionNumber() != null && !aigcVersionControl.getVersionNumber().isEmpty()) {
				queryWrapper.like("version_number", aigcVersionControl.getVersionNumber());
			}

			// 是否最新：直接从请求参数获取，只有明确传值时才添加查询条件
			if (isLatest != null && (isLatest == 1 || isLatest == 2)) {
				queryWrapper.eq("is_latest", isLatest);
				log.info("添加is_latest查询条件: {}", isLatest);
			} else {
				log.info("未添加is_latest查询条件，查询全部数据");
			}

			if (aigcVersionControl.getStatus() != null) {
				queryWrapper.eq("status", aigcVersionControl.getStatus());
			}

			// 默认按发布日期倒序排列
			queryWrapper.orderByDesc("release_date");

			Page<AigcVersionControl> page = new Page<AigcVersionControl>(pageNo, pageSize);
			IPage<AigcVersionControl> pageList = aigcVersionControlService.page(page, queryWrapper);
			return Result.OK(pageList);
		} catch (Exception e) {
			log.error("查询程序版本控制列表失败", e);
			return Result.error("查询失败：" + e.getMessage());
		}
	}
	
	/**
	 * 添加
	 *
	 * @param aigcVersionControl
	 * @return
	 */
	@AutoLog(value = "程序版本控制-添加")
	@ApiOperation(value="程序版本控制-添加", notes="程序版本控制-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody AigcVersionControl aigcVersionControl) {
		LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
		String username = sysUser.getUsername();
		
		boolean result = aigcVersionControlService.addVersion(aigcVersionControl, username);
		if (result) {
			return Result.OK("添加成功！");
		} else {
			return Result.error("添加失败！");
		}
	}
	
	/**
	 * 编辑
	 *
	 * @param aigcVersionControl
	 * @return
	 */
	@AutoLog(value = "程序版本控制-编辑")
	@ApiOperation(value="程序版本控制-编辑", notes="程序版本控制-编辑")
	@PutMapping(value = "/edit")
	public Result<?> edit(@RequestBody AigcVersionControl aigcVersionControl) {
		LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
		String username = sysUser.getUsername();
		
		boolean result = aigcVersionControlService.updateVersion(aigcVersionControl, username);
		if (result) {
			return Result.OK("编辑成功!");
		} else {
			return Result.error("编辑失败！");
		}
	}

	/**
	 * 获取指定程序类型的最新版本（公开接口，无需登录）
	 * 只返回必要的版本信息，不包含敏感的内部管理字段
	 *
	 * @param programType 程序类型
	 * @return 公开版本信息
	 */
	@AutoLog(value = "程序版本控制-获取最新版本")
	@ApiOperation(value="获取指定程序类型的最新版本", notes="获取指定程序类型的最新版本，无需登录，只返回必要信息")
	@GetMapping(value = "/getLatest")
	public Result<?> getLatestVersion(@RequestParam(name="programType", required=true) String programType) {
		try {
			AigcVersionControl latestVersion = aigcVersionControlService.getLatestByProgramType(programType);
			if (latestVersion == null) {
				return Result.error("未找到该程序类型的版本信息");
			}

			// 转换为公开版本信息，过滤敏感字段
			PublicVersionInfo publicInfo = PublicVersionInfo.fromEntity(latestVersion);
			return Result.OK(publicInfo);
		} catch (Exception e) {
			log.error("获取最新版本失败", e);
			return Result.error("获取版本信息失败：" + e.getMessage());
		}
	}

	/**
	 * 获取指定程序类型的所有版本（公开接口，无需登录）
	 *
	 * @param programType 程序类型
	 * @return
	 */
	@AutoLog(value = "程序版本控制-获取程序类型版本列表")
	@ApiOperation(value="获取指定程序类型的所有版本", notes="获取指定程序类型的所有版本，无需登录")
	@GetMapping(value = "/getVersionsByType")
	public Result<?> getVersionsByType(@RequestParam(name="programType", required=true) String programType) {
		try {
			List<AigcVersionControl> versions = aigcVersionControlService.getVersionsByProgramType(programType);
			return Result.OK(versions);
		} catch (Exception e) {
			log.error("获取版本列表失败", e);
			return Result.error("获取版本列表失败：" + e.getMessage());
		}
	}

	/**
	 * 通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "程序版本控制-通过id删除")
	@ApiOperation(value="程序版本控制-通过id删除", notes="程序版本控制-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		boolean result = aigcVersionControlService.deleteVersion(id);
		if (result) {
			return Result.OK("删除成功!");
		} else {
			return Result.error("删除失败！");
		}
	}
	
	/**
	 * 批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "程序版本控制-批量删除")
	@ApiOperation(value="程序版本控制-批量删除", notes="程序版本控制-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		List<String> idList = Arrays.asList(ids.split(","));
		boolean result = aigcVersionControlService.batchDeleteVersions(idList);
		if (result) {
			return Result.OK("批量删除成功!");
		} else {
			return Result.error("批量删除失败！");
		}
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "程序版本控制-通过id查询")
	@ApiOperation(value="程序版本控制-通过id查询", notes="程序版本控制-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		AigcVersionControl aigcVersionControl = aigcVersionControlService.getById(id);
		if(aigcVersionControl==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(aigcVersionControl);
	}

	/**
	 * 设置为最新版本
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "程序版本控制-设置为最新版本")
	@ApiOperation(value="程序版本控制-设置为最新版本", notes="程序版本控制-设置为最新版本")
	@PutMapping(value = "/setAsLatest")
	public Result<?> setAsLatest(@RequestParam(name="id",required=true) String id) {
		LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
		String username = sysUser.getUsername();
		
		boolean result = aigcVersionControlService.setAsLatest(id, username);
		if (result) {
			return Result.OK("设置成功!");
		} else {
			return Result.error("设置失败！");
		}
	}

	/**
	 * 批量更新状态
	 *
	 * @param ids
	 * @param status
	 * @return
	 */
	@AutoLog(value = "程序版本控制-批量更新状态")
	@ApiOperation(value="程序版本控制-批量更新状态", notes="程序版本控制-批量更新状态")
	@PutMapping(value = "/batchUpdateStatus")
	public Result<?> batchUpdateStatus(@RequestParam(name="ids",required=true) String ids,
									   @RequestParam(name="status",required=true) Integer status) {
		LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
		String username = sysUser.getUsername();
		
		List<String> idList = Arrays.asList(ids.split(","));
		boolean result = aigcVersionControlService.batchUpdateStatus(idList, status, username);
		if (result) {
			return Result.OK("批量更新成功!");
		} else {
			return Result.error("批量更新失败！");
		}
	}

	/**
	 * 查询所有程序类型
	 *
	 * @return
	 */
	@AutoLog(value = "程序版本控制-查询所有程序类型")
	@ApiOperation(value="程序版本控制-查询所有程序类型", notes="程序版本控制-查询所有程序类型")
	@GetMapping(value = "/getAllProgramTypes")
	public Result<?> getAllProgramTypes() {
		List<String> types = aigcVersionControlService.getAllProgramTypes();
		return Result.OK(types);
	}

	/**
	 * 统计各程序类型的版本数量
	 *
	 * @return
	 */
	@AutoLog(value = "程序版本控制-统计版本数量")
	@ApiOperation(value="程序版本控制-统计版本数量", notes="程序版本控制-统计版本数量")
	@GetMapping(value = "/countByType")
	public Result<?> countByProgramType() {
		List<Map<String, Object>> statistics = aigcVersionControlService.countByProgramType();
		return Result.OK(statistics);
	}

	/**
	 * 查询最近发布的版本
	 *
	 * @param limit
	 * @return
	 */
	@AutoLog(value = "程序版本控制-查询最近版本")
	@ApiOperation(value="程序版本控制-查询最近版本", notes="程序版本控制-查询最近版本")
	@GetMapping(value = "/getRecentVersions")
	public Result<?> getRecentVersions(@RequestParam(name="limit", defaultValue="10") Integer limit) {
		List<AigcVersionControl> versions = aigcVersionControlService.getRecentVersions(limit);
		return Result.OK(versions);
	}

	/**
	 * 检查版本号是否存在
	 *
	 * @param programType
	 * @param versionNumber
	 * @param excludeId
	 * @return
	 */
	@AutoLog(value = "程序版本控制-检查版本号")
	@ApiOperation(value="程序版本控制-检查版本号", notes="程序版本控制-检查版本号")
	@GetMapping(value = "/checkVersionExists")
	public Result<?> checkVersionExists(@RequestParam(name="programType",required=true) String programType,
										@RequestParam(name="versionNumber",required=true) String versionNumber,
										@RequestParam(name="excludeId",required=false) String excludeId) {
		boolean exists = aigcVersionControlService.checkVersionExists(programType, versionNumber, excludeId);
		return Result.OK(exists);
	}

	/**
	 * 获取下一个建议版本号
	 *
	 * @param programType
	 * @param versionType
	 * @return
	 */
	@AutoLog(value = "程序版本控制-获取建议版本号")
	@ApiOperation(value="程序版本控制-获取建议版本号", notes="程序版本控制-获取建议版本号")
	@GetMapping(value = "/getNextSuggestedVersion")
	public Result<?> getNextSuggestedVersion(@RequestParam(name="programType",required=true) String programType,
											 @RequestParam(name="versionType", defaultValue="patch") String versionType) {
		String suggestedVersion = aigcVersionControlService.getNextSuggestedVersion(programType, versionType);
		return Result.OK(suggestedVersion);
	}

	/**
	 * 版本对比
	 *
	 * @param programType
	 * @param fromVersion
	 * @param toVersion
	 * @return
	 */
	@AutoLog(value = "程序版本控制-版本对比")
	@ApiOperation(value="程序版本控制-版本对比", notes="程序版本控制-版本对比")
	@GetMapping(value = "/compareVersions")
	public Result<?> compareVersions(@RequestParam(name="programType",required=true) String programType,
									 @RequestParam(name="fromVersion",required=true) String fromVersion,
									 @RequestParam(name="toVersion",required=true) String toVersion) {
		Map<String, Object> comparison = aigcVersionControlService.getVersionComparison(programType, fromVersion, toVersion);
		return Result.OK(comparison);
	}

	/**
	 * 导出excel
	 *
	 * @param request
	 * @param aigcVersionControl
	 */
	@RequestMapping(value = "/exportXls")
	public ModelAndView exportXls(HttpServletRequest request, AigcVersionControl aigcVersionControl) {
		return super.exportXls(request, aigcVersionControl, AigcVersionControl.class, "程序版本控制表");
	}

	/**
	 * 通过excel导入数据
	 *
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/importExcel", method = RequestMethod.POST)
	public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
		return super.importExcel(request, response, AigcVersionControl.class);
	}
}
