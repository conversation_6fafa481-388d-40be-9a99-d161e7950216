<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form-model ref="form" :model="model" :rules="validatorRules" slot="detail">
        <a-row>
          <a-col :span="24">
            <a-form-model-item label="创作者名称" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="authorname">
              <a-input v-model="model.authorname" placeholder="请输入创作者名称"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="插件数" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="plubnum">
              <a-input-number
                v-model="model.plubnum"
                placeholder="系统自动统计"
                style="width: 100%"
                :disabled="true" />
              <div style="color: #999; font-size: 12px; margin-top: 4px;">
                此字段由系统自动统计，无需手动输入
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="插件使用总数" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="plubusenum">
              <a-input-number
                v-model="model.plubusenum"
                placeholder="系统自动统计"
                style="width: 100%"
                :disabled="true" />
              <div style="color: #999; font-size: 12px; margin-top: 4px;">
                此字段由系统自动统计，无需手动输入
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="创作者简介" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="createinfo">
              <a-textarea v-model="model.createinfo" rows="4" placeholder="请输入创作者简介" />
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </j-form-container>
  </a-spin>
</template>

<script>

  import { httpAction, getAction } from '@/api/manage'
  import { validateDuplicateValue } from '@/utils/util'

  export default {
    name: 'AigcPlubAuthorForm',
    components: {
    },
    props: {
      //表单禁用
      disabled: {
        type: Boolean,
        default: false,
        required: false
      }
    },
    data () {
      return {
        model:{
          plubnum: 0,        // 插件数默认为0
          plubusenum: 0      // 插件使用总数默认为0
         },
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 },
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 },
        },
        confirmLoading: false,
        validatorRules: {
        },
        url: {
          add: "/plubauthor/aigcPlubAuthor/add",
          edit: "/plubauthor/aigcPlubAuthor/edit",
          queryById: "/plubauthor/aigcPlubAuthor/queryById"
        }
      }
    },
    computed: {
      formDisabled(){
        return this.disabled
      },
    },
    created () {
       //备份model原始值
      this.modelDefault = JSON.parse(JSON.stringify(this.model));
    },
    methods: {
      add () {
        // 🔥 新增时设置默认值
        const defaultModel = Object.assign({}, this.modelDefault, {
          plubnum: 0,        // 插件数默认为0
          plubusenum: 0      // 插件使用总数默认为0
        });
        this.edit(defaultModel);
      },
      edit (record) {
        this.model = Object.assign({}, record);
        this.visible = true;
      },
      submitForm () {
        const that = this;
        // 触发表单验证
        this.$refs.form.validate(valid => {
          if (valid) {
            that.confirmLoading = true;
            let httpurl = '';
            let method = '';
            if(!this.model.id){
              httpurl+=this.url.add;
              method = 'post';
            }else{
              httpurl+=this.url.edit;
               method = 'put';
            }
            httpAction(httpurl,this.model,method).then((res)=>{
              if(res.success){
                that.$message.success(res.message);
                that.$emit('ok');
              }else{
                that.$message.warning(res.message);
              }
            }).finally(() => {
              that.confirmLoading = false;
            })
          }
         
        })
      },
    }
  }
</script>