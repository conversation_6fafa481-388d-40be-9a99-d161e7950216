package org.jeecg.modules.system.service.impl;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.jeecg.modules.system.entity.SysSensitiveWord;
import org.jeecg.modules.system.entity.SysSensitiveWordHitLog;
import org.jeecg.modules.system.mapper.SysSensitiveWordMapper;
import org.jeecg.modules.system.mapper.SysSensitiveWordHitLogMapper;
import org.jeecg.modules.system.service.ISysSensitiveWordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.houbb.sensitive.word.bs.SensitiveWordBs;
import com.github.houbb.sensitive.word.support.deny.WordDenys;

import lombok.extern.slf4j.Slf4j;

/**
 * @Description: 敏感词管理服务实现
 * @Author: jeecg-boot
 * @Date: 2025-06-22
 * @Version: V1.0
 */
@Service
@Slf4j
public class SysSensitiveWordServiceImpl extends ServiceImpl<SysSensitiveWordMapper, SysSensitiveWord> 
    implements ISysSensitiveWordService {

    @Autowired
    private SysSensitiveWordHitLogMapper hitLogMapper;

    // 敏感词缓存 - 使用DFA算法的Trie树结构
    private volatile Map<String, Object> sensitiveWordMap = new ConcurrentHashMap<>();
    
    // 敏感词列表缓存
    private volatile Set<String> sensitiveWordSet = new HashSet<>();
    
    // 缓存是否已初始化
    private volatile boolean cacheInitialized = false;

    @Override
    @Cacheable(value = "sensitiveWords", key = "'enabled'")
    public List<String> getEnabledWords() {
        return baseMapper.getEnabledWords();
    }

    @Override
    @CacheEvict(value = "sensitiveWords", allEntries = true)
    public void refreshCache() {
        log.info("🔄 开始刷新敏感词缓存");
        
        try {
            // 从数据库获取最新的敏感词
            List<String> words = getEnabledWords();
            
            // 重建DFA树
            buildSensitiveWordMap(words);
            
            // 更新敏感词集合
            this.sensitiveWordSet = new HashSet<>(words);
            this.cacheInitialized = true;
            
            log.info("✅ 敏感词缓存刷新完成，共加载 {} 个敏感词", words.size());
        } catch (Exception e) {
            log.error("❌ 敏感词缓存刷新失败", e);
            throw new RuntimeException("敏感词缓存刷新失败: " + e.getMessage());
        }
    }

    @Override
    public SensitiveWordCheckResult checkSensitiveWords(String text) {
        return checkSensitiveWords(text, null, null, null);
    }

    @Override
    public SensitiveWordCheckResult checkSensitiveWords(String text, String userId, String module, String ipAddress) {
        if (StringUtils.isBlank(text)) {
            return new SensitiveWordCheckResult(false, new ArrayList<>(), "文本为空");
        }

        // 确保缓存已初始化
        ensureCacheInitialized();

        try {
            // 使用DFA算法检测敏感词
            List<String> foundWords = findSensitiveWords(text);
            
            if (!foundWords.isEmpty()) {
                // 记录命中日志和统计
                for (String word : foundWords) {
                    // 异步增加命中次数
                    incrementHitCount(word);
                    
                    // 记录详细日志
                    if (StringUtils.isNotBlank(userId) || StringUtils.isNotBlank(module)) {
                        recordHitLog(word, text, userId, module, ipAddress);
                    }
                }
                
                String message = String.format("检测到 %d 个敏感词: %s", 
                    foundWords.size(), String.join(", ", foundWords));
                
                return new SensitiveWordCheckResult(true, foundWords, message);
            }
            
            return new SensitiveWordCheckResult(false, new ArrayList<>(), "未检测到敏感词");
            
        } catch (Exception e) {
            log.error("敏感词检测异常: {}", e.getMessage(), e);
            return new SensitiveWordCheckResult(false, new ArrayList<>(), "检测异常: " + e.getMessage());
        }
    }

    @Override
    public String replaceSensitiveWords(String text, String replacement) {
        if (StringUtils.isBlank(text)) {
            return text;
        }

        // 确保缓存已初始化
        ensureCacheInitialized();

        try {
            return replaceSensitiveWordsInternal(text, replacement);
        } catch (Exception e) {
            log.error("敏感词替换异常: {}", e.getMessage(), e);
            return text; // 异常时返回原文本
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ImportResult importSensitiveWords(MultipartFile file, String createBy) {
        // TODO: 实现Excel导入功能
        // 这里需要使用EasyExcel或POI来解析Excel文件
        return new ImportResult(false, "Excel导入功能待实现", 0, 0, 0);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ImportResult importFromHoubb(String createBy) {
        log.info("🚀 开始从houbb库导入敏感词数据");
        
        try {
            // 获取houbb库的敏感词
            Set<String> houbbWords = extractHoubbSensitiveWords();
            
            if (houbbWords.isEmpty()) {
                return new ImportResult(false, "未能从houbb库获取到敏感词数据", 0, 0, 0);
            }

            // 批量插入到数据库
            List<SysSensitiveWord> wordList = new ArrayList<>();
            Date now = new Date();
            
            for (String word : houbbWords) {
                if (StringUtils.isNotBlank(word) && word.length() <= 100) {
                    SysSensitiveWord entity = new SysSensitiveWord();
                    entity.setId(UUID.randomUUID().toString().replace("-", ""));
                    entity.setWord(word.trim());
                    entity.setCategory(categorizeWord(word)); // 智能分类
                    entity.setLevel(calculateRiskLevel(word)); // 智能评级
                    entity.setStatus(1); // 启用
                    entity.setHitCount(0);
                    entity.setSource("HOUBB");
                    entity.setCreateBy(createBy);
                    entity.setCreateTime(now);
                    entity.setRemark("从houbb敏感词库导入");
                    
                    wordList.add(entity);
                }
            }

            // 分批插入，避免SQL过长
            int batchSize = 1000;
            int successCount = 0;
            int failCount = 0;
            
            for (int i = 0; i < wordList.size(); i += batchSize) {
                int endIndex = Math.min(i + batchSize, wordList.size());
                List<SysSensitiveWord> batch = wordList.subList(i, endIndex);
                
                try {
                    int insertCount = baseMapper.batchInsert(batch);
                    successCount += insertCount;
                } catch (Exception e) {
                    log.warn("批量插入敏感词失败: {}", e.getMessage());
                    failCount += batch.size();
                }
            }

            // 刷新缓存
            refreshCache();
            
            String message = String.format("导入完成！总计: %d, 成功: %d, 失败: %d", 
                wordList.size(), successCount, failCount);
            
            log.info("✅ houbb敏感词导入完成: {}", message);
            
            return new ImportResult(true, message, wordList.size(), successCount, failCount);
            
        } catch (Exception e) {
            log.error("❌ 从houbb库导入敏感词失败", e);
            return new ImportResult(false, "导入失败: " + e.getMessage(), 0, 0, 0);
        }
    }

    @Override
    public Map<String, Object> getStatistics() {
        return baseMapper.getStatistics();
    }

    @Override
    public List<Map<String, Object>> getTopHitWords(int limit) {
        return baseMapper.getTopHitWords(limit);
    }

    @Override
    public List<Map<String, Object>> getCategoryStatistics() {
        return baseMapper.getCategoryStatistics();
    }

    @Override
    public List<Map<String, Object>> getLevelStatistics() {
        return baseMapper.getLevelStatistics();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void incrementHitCount(String word) {
        try {
            baseMapper.incrementHitCount(word);
        } catch (Exception e) {
            log.warn("增加敏感词命中次数失败: word={}, error={}", word, e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void recordHitLog(String word, String hitText, String userId, String module, String ipAddress) {
        try {
            // 查找敏感词ID
            SysSensitiveWord sensitiveWord = baseMapper.checkWordExists(word);
            
            SysSensitiveWordHitLog hitLog = new SysSensitiveWordHitLog();
            hitLog.setId(UUID.randomUUID().toString().replace("-", ""));
            hitLog.setWordId(sensitiveWord != null ? sensitiveWord.getId() : null);
            hitLog.setWord(word);
            hitLog.setHitText(hitText.length() > 1000 ? hitText.substring(0, 1000) + "..." : hitText);
            hitLog.setUserId(userId);
            hitLog.setIpAddress(ipAddress);
            hitLog.setModule(module);
            hitLog.setHitTime(new Date());
            hitLog.setCreateTime(new Date());
            
            hitLogMapper.insert(hitLog);
        } catch (Exception e) {
            log.warn("记录敏感词命中日志失败: word={}, error={}", word, e.getMessage());
        }
    }

    /**
     * 确保缓存已初始化
     */
    private void ensureCacheInitialized() {
        if (!cacheInitialized) {
            synchronized (this) {
                if (!cacheInitialized) {
                    refreshCache();
                }
            }
        }
    }

    /**
     * 构建敏感词DFA树
     */
    private void buildSensitiveWordMap(List<String> words) {
        Map<String, Object> map = new HashMap<>();
        
        for (String word : words) {
            if (StringUtils.isBlank(word)) continue;
            
            Map<String, Object> currentMap = map;
            char[] chars = word.toCharArray();
            
            for (int i = 0; i < chars.length; i++) {
                String key = String.valueOf(chars[i]);
                
                if (currentMap.containsKey(key)) {
                    currentMap = (Map<String, Object>) currentMap.get(key);
                } else {
                    Map<String, Object> newMap = new HashMap<>();
                    currentMap.put(key, newMap);
                    currentMap = newMap;
                }
                
                // 最后一个字符，标记为结束
                if (i == chars.length - 1) {
                    currentMap.put("isEnd", true);
                }
            }
        }
        
        this.sensitiveWordMap = map;
    }

    /**
     * 使用DFA算法查找敏感词
     */
    private List<String> findSensitiveWords(String text) {
        List<String> result = new ArrayList<>();
        char[] chars = text.toCharArray();
        
        for (int i = 0; i < chars.length; i++) {
            int length = checkSensitiveWord(chars, i);
            if (length > 0) {
                String sensitiveWord = new String(chars, i, length);
                result.add(sensitiveWord);
                i += length - 1; // 跳过已检测的字符
            }
        }
        
        return result.stream().distinct().collect(Collectors.toList());
    }

    /**
     * 检查从指定位置开始是否为敏感词
     */
    private int checkSensitiveWord(char[] chars, int startIndex) {
        Map<String, Object> currentMap = sensitiveWordMap;
        int wordLength = 0;
        
        for (int i = startIndex; i < chars.length; i++) {
            String key = String.valueOf(chars[i]);
            
            if (currentMap.containsKey(key)) {
                wordLength++;
                currentMap = (Map<String, Object>) currentMap.get(key);
                
                // 检查是否为完整敏感词
                if (currentMap.containsKey("isEnd") && (Boolean) currentMap.get("isEnd")) {
                    return wordLength;
                }
            } else {
                break;
            }
        }
        
        return 0;
    }

    /**
     * 替换敏感词
     */
    private String replaceSensitiveWordsInternal(String text, String replacement) {
        StringBuilder result = new StringBuilder();
        char[] chars = text.toCharArray();
        
        for (int i = 0; i < chars.length; i++) {
            int length = checkSensitiveWord(chars, i);
            if (length > 0) {
                // 替换敏感词
                for (int j = 0; j < length; j++) {
                    result.append(replacement);
                }
                i += length - 1;
            } else {
                result.append(chars[i]);
            }
        }
        
        return result.toString();
    }

    /**
     * 从houbb库提取敏感词（直接调用API获取完整词库）
     */
    private Set<String> extractHoubbSensitiveWords() {
        try {
            log.info("🚀 开始从houbb库提取敏感词...");

            Set<String> words = new HashSet<>();

            // 方法1: 直接调用houbb库的默认敏感词API（最简单有效的方法）
            try {
                List<String> houbbDefaultWords = WordDenys.defaults().deny();
                words.addAll(houbbDefaultWords);
                log.info("✅ 从houbb库默认API获取敏感词 {} 个", houbbDefaultWords.size());
            } catch (Exception e) {
                log.warn("houbb默认API调用失败: {}", e.getMessage());
            }

            // 方法2: 添加预定义敏感词（补充更多词汇）
            Set<String> defaultWords = getDefaultSensitiveWords();
            words.addAll(defaultWords);
            log.info("添加预定义敏感词 {} 个", defaultWords.size());

            // 方法3: 添加网络常见敏感词
            Set<String> networkWords = getNetworkSensitiveWords();
            words.addAll(networkWords);
            log.info("添加网络敏感词 {} 个", networkWords.size());

            log.info("✅ 总共提取到 {} 个敏感词", words.size());
            return words;

        } catch (Exception e) {
            log.error("❌ 从houbb库提取敏感词失败", e);
            return getDefaultSensitiveWords();
        }
    }







    /**
     * 获取预定义的敏感词列表（大幅扩展版）
     */
    private Set<String> getDefaultSensitiveWords() {
        Set<String> words = new HashSet<>();

        // 政治类敏感词（扩展到50+个）
        words.addAll(Arrays.asList(
            "法轮功", "台独", "藏独", "疆独", "港独", "反动", "邪教", "民运",
            "六四", "天安门", "血腥镇压", "民主运动", "政治改革", "一党专政",
            "颠覆国家", "分裂国家", "煽动颠覆", "危害国家安全", "泄露国家机密",
            "间谍", "叛国", "卖国", "汉奸", "走狗", "反华", "辱华", "精日",
            "皇汉", "民族主义", "种族歧视", "宗教极端", "恐怖组织", "ISIS",
            "基地组织", "塔利班", "东突", "世维会", "达赖", "班禅", "活佛",
            "转世", "喇嘛", "僧侣", "佛教", "伊斯兰", "穆斯林", "清真",
            "阿拉", "真主", "圣战", "吉哈德", "殉教", "烈士", "圣城",
            "麦加", "麦地那", "耶路撒冷", "十字军", "圣殿", "教皇", "主教"
        ));

        // 色情类敏感词（扩展到80+个）
        words.addAll(Arrays.asList(
            "色情", "黄色", "成人", "裸体", "性爱", "做爱", "强奸", "轮奸",
            "卖淫", "嫖娼", "援交", "包养", "小姐", "鸡头", "皮条客", "妓女",
            "性交", "交配", "性器官", "生殖器", "阴茎", "阴道", "乳房", "胸部",
            "臀部", "大腿", "私处", "下体", "春药", "壮阳", "催情", "性药",
            "避孕套", "安全套", "情趣用品", "成人用品", "充气娃娃", "飞机杯",
            "自慰", "手淫", "撸管", "打飞机", "意淫", "性幻想", "性梦", "春梦",
            "一夜情", "约炮", "开房", "床戏", "激情", "高潮", "射精", "精液",
            "处女", "处男", "破处", "开苞", "初夜", "第一次", "性经验", "性技巧",
            "性感", "诱惑", "勾引", "调情", "挑逗", "撩骚", "发骚", "骚货",
            "淫荡", "淫乱", "淫秽", "下流", "猥亵", "变态", "露骨", "赤裸"
        ));

        // 暴力类敏感词（扩展到60+个）
        words.addAll(Arrays.asList(
            "杀人", "自杀", "爆炸", "恐怖", "炸弹", "枪支", "刀具", "血腥",
            "暴力", "打架", "斗殴", "械斗", "恐怖主义", "恐怖分子", "ISIS",
            "谋杀", "凶杀", "屠杀", "灭门", "血案", "惨案", "命案", "死亡",
            "尸体", "死尸", "腐尸", "骸骨", "白骨", "骨灰", "火化", "埋葬",
            "手枪", "步枪", "冲锋枪", "机关枪", "狙击枪", "火箭筒", "手雷",
            "地雷", "炸药", "TNT", "C4", "硝酸甘油", "雷管", "引爆", "爆破",
            "砍刀", "匕首", "短刀", "长刀", "武士刀", "军刀", "菜刀", "剪刀",
            "斧头", "锤子", "棍棒", "铁棍", "钢管", "砖头", "石块", "玻璃",
            "毒药", "毒液", "毒气", "毒粉", "毒针", "毒镖", "毒箭", "毒蛇"
        ));

        // 赌博类敏感词（扩展到50+个）
        words.addAll(Arrays.asList(
            "赌博", "赌场", "博彩", "老虎机", "百家乐", "21点", "轮盘", "骰子",
            "赌球", "赌马", "彩票", "六合彩", "时时彩", "北京赛车", "幸运飞艇",
            "德州扑克", "梭哈", "炸金花", "斗地主", "麻将", "牌九", "骰宝",
            "龙虎斗", "牛牛", "三公", "推筒子", "压大小", "猜单双", "摇骰子",
            "赌注", "下注", "押注", "投注", "庄家", "闲家", "对子", "顺子",
            "同花", "葫芦", "四条", "同花顺", "皇家同花顺", "筹码", "赌资",
            "赌债", "欠债", "高利贷", "放贷", "讨债", "催债", "赌瘾", "戒赌"
        ));

        // 毒品类敏感词（扩展到60+个）
        words.addAll(Arrays.asList(
            "毒品", "海洛因", "冰毒", "摇头丸", "大麻", "鸦片", "可卡因", "吗啡",
            "K粉", "麻古", "神仙水", "止咳水", "安非他命", "迷幻药", "致幻剂",
            "白粉", "白面", "面粉", "四号", "杜冷丁", "美沙酮", "芬太尼",
            "曲马多", "氯胺酮", "三唑仑", "咪达唑仑", "γ-羟基丁酸", "GHB",
            "LSD", "摇脚丸", "快乐丸", "忘情水", "蒙汗药", "迷魂药", "春药",
            "吸毒", "注射", "静脉注射", "肌肉注射", "鼻吸", "口服", "烟吸",
            "毒瘾", "戒毒", "戒断", "毒贩", "贩毒", "制毒", "运毒", "藏毒",
            "毒窝", "毒品交易", "毒品买卖", "毒品走私", "毒品犯罪", "禁毒"
        ));

        // 违法类敏感词（扩展到80+个）
        words.addAll(Arrays.asList(
            "走私", "贩毒", "洗钱", "诈骗", "传销", "非法集资", "高利贷", "黑社会",
            "黑帮", "暴力催收", "套路贷", "校园贷", "裸贷", "网络诈骗", "电信诈骗",
            "偷盗", "盗窃", "抢劫", "抢夺", "敲诈", "勒索", "绑架", "拐卖",
            "人口贩卖", "拐骗", "拐卖儿童", "拐卖妇女", "强迫劳动", "奴役",
            "非法拘禁", "非法搜查", "非法侵入", "破门而入", "入室盗窃", "撬锁",
            "假币", "伪钞", "制假", "售假", "假冒", "仿冒", "盗版", "侵权",
            "偷税", "漏税", "逃税", "避税", "税务欺诈", "发票造假", "虚开发票",
            "合同诈骗", "信用卡诈骗", "保险诈骗", "证券诈骗", "期货诈骗",
            "网络钓鱼", "木马病毒", "黑客攻击", "数据泄露", "隐私窃取",
            "身份盗用", "信息诈骗", "虚假广告", "虚假宣传", "误导消费者"
        ));

        // 广告类敏感词（扩展到60+个）
        words.addAll(Arrays.asList(
            "代开发票", "刻章", "办证", "假证", "身份证", "毕业证", "学位证", "资格证",
            "驾驶证", "行驶证", "房产证", "结婚证", "离婚证", "出生证", "死亡证明",
            "代办", "包过", "包拿证", "快速办理", "一手货源", "厂家直销",
            "低价批发", "清仓甩卖", "跳楼价", "亏本甩卖", "最后三天", "限时抢购",
            "免费试用", "免费领取", "0元购", "1元秒杀", "9.9包邮", "全网最低价",
            "微商", "代理", "招代理", "一件代发", "无需囤货", "躺着赚钱",
            "日赚千元", "月入过万", "轻松赚钱", "在家赚钱", "兼职赚钱", "副业",
            "刷单", "刷好评", "刷信誉", "刷流量", "刷粉丝", "买粉丝", "涨粉",
            "加微信", "扫码", "二维码", "群发", "推广", "引流", "拉人头"
        ));

        // 网络安全类敏感词（新增50+个）
        words.addAll(Arrays.asList(
            "黑客", "破解", "入侵", "攻击", "病毒", "木马", "蠕虫", "后门",
            "漏洞", "0day", "exploit", "payload", "shell", "webshell", "backdoor",
            "钓鱼", "社工", "撞库", "拖库", "脱裤", "肉鸡", "僵尸网络", "DDOS",
            "SQL注入", "XSS", "CSRF", "缓冲区溢出", "提权", "横向移动", "APT",
            "暗网", "深网", "洋葱路由", "TOR", "匿名", "代理", "VPN", "翻墙",
            "破解软件", "注册机", "激活码", "序列号", "盗版", "免费下载",
            "种子", "磁力链接", "BT下载", "迅雷", "网盘", "云盘", "分享链接"
        ));

        // 社会负面类敏感词（新增40+个）
        words.addAll(Arrays.asList(
            "自残", "割腕", "跳楼", "上吊", "服毒", "烧炭", "安乐死", "轻生",
            "抑郁", "焦虑", "精神病", "神经病", "疯子", "傻子", "白痴", "智障",
            "残疾", "瘸子", "瞎子", "聋子", "哑巴", "侏儒", "畸形", "怪胎",
            "歧视", "种族歧视", "性别歧视", "地域歧视", "职业歧视", "年龄歧视",
            "霸凌", "校园霸凌", "网络霸凌", "语言暴力", "冷暴力", "家暴",
            "虐待", "虐童", "虐老", "虐动物", "遗弃", "抛弃", "不孝", "啃老"
        ));

        log.info("生成预定义敏感词 {} 个", words.size());
        return words;
    }

    /**
     * 获取网络常见敏感词（补充更多词汇）
     */
    private Set<String> getNetworkSensitiveWords() {
        Set<String> words = new HashSet<>();

        // 网络流行语敏感词（50+个）
        words.addAll(Arrays.asList(
            "傻逼", "煞笔", "沙比", "傻比", "脑残", "智障", "白痴", "弱智",
            "废物", "垃圾", "渣渣", "屌丝", "loser", "失败者", "废柴", "菜鸡",
            "狗东西", "畜生", "禽兽", "人渣", "败类", "贱人", "婊子", "妓女",
            "操你妈", "草你妈", "日你妈", "艹你妈", "你妈逼", "你妈的", "滚蛋",
            "滚开", "滚远点", "去死", "死开", "找死", "作死", "该死", "活该",
            "恶心", "变态", "猥琐", "下流", "无耻", "卑鄙", "龌龊", "肮脏"
        ));

        // 地域歧视类敏感词（30+个）
        words.addAll(Arrays.asList(
            "河南骗子", "东北大汉", "上海小气", "北京大爷", "广东佬", "福建人",
            "温州人", "安徽人", "江西老表", "湖南辣妹", "四川妹子", "重庆火锅",
            "新疆人", "西藏人", "内蒙古", "宁夏人", "青海人", "甘肃人",
            "乡巴佬", "土包子", "农民工", "打工仔", "外地人", "外来户",
            "城乡结合部", "贫困县", "落后地区", "偏远山区", "不发达地区"
        ));

        // 职业歧视类敏感词（25+个）
        words.addAll(Arrays.asList(
            "服务员", "保安", "清洁工", "保洁", "环卫工", "快递员", "外卖员",
            "司机", "工人", "农民", "民工", "临时工", "小时工", "钟点工",
            "家政", "保姆", "月嫂", "育儿嫂", "护工", "看护", "陪护",
            "低端人口", "底层人民", "社会底层", "下等人", "贱民"
        ));

        // 年龄歧视类敏感词（20+个）
        words.addAll(Arrays.asList(
            "老不死", "老东西", "老家伙", "老头子", "老太婆", "糟老头",
            "小屁孩", "熊孩子", "死小孩", "小兔崽子", "小王八蛋", "小杂种",
            "中年油腻", "油腻大叔", "更年期", "老年痴呆", "老糊涂", "老顽固",
            "90后", "00后", "佛系青年", "啃老族"
        ));

        log.info("生成网络敏感词 {} 个", words.size());
        return words;
    }

    /**
     * 智能分类敏感词
     */
    private String categorizeWord(String word) {
        // 简单的关键词分类逻辑
        if (word.contains("政治") || word.contains("党") || word.contains("政府")) {
            return "political";
        } else if (word.contains("色情") || word.contains("性")) {
            return "pornographic";
        } else if (word.contains("暴力") || word.contains("杀") || word.contains("死")) {
            return "violent";
        } else if (word.contains("广告") || word.contains("推广")) {
            return "advertisement";
        } else if (word.contains("赌") || word.contains("博彩")) {
            return "gambling";
        } else if (word.contains("毒品") || word.contains("吸毒")) {
            return "drugs";
        } else if (word.contains("违法") || word.contains("犯罪")) {
            return "illegal";
        }
        return "other";
    }

    /**
     * 智能评估风险级别
     */
    private Integer calculateRiskLevel(String word) {
        // 简单的风险评估逻辑
        String category = categorizeWord(word);
        
        switch (category) {
            case "political":
            case "illegal":
            case "drugs":
                return 3; // 高危
            case "violent":
            case "gambling":
                return 2; // 中危
            default:
                return 1; // 低危
        }
    }
}
