/* 全局样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Microsoft YaHei', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #333;
    overflow: hidden;
    user-select: none;
}

#app {
    height: 100vh;
    display: flex;
    flex-direction: column;
}

/* 顶部导航 */
.header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    padding: 0 20px;
    height: 70px;
    display: flex;
    align-items: center;
    -webkit-app-region: drag;
}

.header-content {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    display: flex;
    align-items: center;
    gap: 12px;
}

.logo-icon {
    width: 32px;
    height: 32px;
    border-radius: 8px;
}

.logo-text {
    font-size: 20px;
    font-weight: 600;
    color: #2d3748;
    margin: 0;
}

.logo-subtitle {
    font-size: 12px;
    color: #718096;
    background: #e2e8f0;
    padding: 2px 8px;
    border-radius: 12px;
}

.nav {
    display: flex;
    gap: 8px;
    -webkit-app-region: no-drag;
}

.nav-btn {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 8px 16px;
    border: none;
    background: transparent;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 14px;
    color: #4a5568;
}

.nav-btn:hover {
    background: rgba(0, 0, 0, 0.05);
    color: #2d3748;
}

.nav-btn.active {
    background: #667eea;
    color: white;
}

.nav-icon {
    font-size: 16px;
}

.nav-text {
    font-weight: 500;
}

/* 主内容区域 */
.main {
    flex: 1;
    overflow: hidden;
    position: relative;
}

.page {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    opacity: 0;
    transform: translateX(20px);
    transition: all 0.3s ease;
    overflow-y: auto;
    display: none;
}

.page.active {
    opacity: 1;
    transform: translateX(0);
    display: block;
}

.page-content {
    padding: 30px 40px;
    max-width: 1000px;
    margin: 0 auto;
    width: 100%;
}

.page-title {
    font-size: 28px;
    font-weight: 600;
    color: white;
    margin-bottom: 20px;
    text-align: center;
}

/* 导入区域 */
.import-section {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    padding: 30px;
    margin-bottom: 30px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.section-title {
    font-size: 24px;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 8px;
    text-align: center;
}

.section-desc {
    color: #718096;
    text-align: center;
    margin-bottom: 30px;
    line-height: 1.5;
}

.input-group {
    margin-bottom: 30px;
}

.input-label {
    display: block;
    font-weight: 500;
    color: #4a5568;
    margin-bottom: 8px;
}

.input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
}

.url-input {
    flex: 1;
    padding: 12px 50px 12px 16px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 14px;
    transition: all 0.2s ease;
    background: white;
}

.url-textarea {
    flex: 1;
    padding: 16px 50px 16px 16px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 14px;
    transition: all 0.2s ease;
    background: white;
    resize: none;
    height: 120px;
    font-family: 'Consolas', 'Monaco', monospace;
    line-height: 1.5;
    overflow-y: auto;
}

.url-input:focus,
.url-textarea:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.url-input.valid,
.url-textarea.valid {
    border-color: #48bb78;
}

.url-input.invalid,
.url-textarea.invalid {
    border-color: #f56565;
}

.paste-btn {
    position: absolute;
    right: 8px;
    width: 32px;
    height: 32px;
    border: none;
    background: #f7fafc;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 14px;
}

.paste-btn:hover {
    background: #edf2f7;
}

.validation-message {
    margin-top: 8px;
    font-size: 12px;
    min-height: 16px;
}

.validation-message.valid {
    color: #48bb78;
}

.validation-message.invalid {
    color: #f56565;
}

.action-group {
    text-align: center;
}

.import-btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 14px 32px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 12px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);
    margin-bottom: 16px;
}

.import-btn:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.import-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.import-hint {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    color: #718096;
    font-size: 12px;
}



/* 快速操作 */
.quick-actions {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    padding: 30px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.section-subtitle {
    font-size: 18px;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 20px;
    text-align: center;
}

.action-cards {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
}

.action-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    border: 1px solid #e2e8f0;
    transition: all 0.2s ease;
    cursor: pointer;
}

.action-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.card-icon {
    font-size: 24px;
    margin-bottom: 12px;
}

.card-title {
    font-size: 14px;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 4px;
}

.card-desc {
    font-size: 12px;
    color: #718096;
    margin-bottom: 16px;
    line-height: 1.4;
}

.card-btn {
    width: 100%;
    padding: 8px 16px;
    background: #f7fafc;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.card-btn:hover {
    background: #edf2f7;
}

/* 工具类 */
.hidden {
    display: none !important;
}

.text-center {
    text-align: center;
}

.mb-4 {
    margin-bottom: 16px;
}

.mt-4 {
    margin-top: 16px;
}

/* 下载进度样式 */
.download-progress-section {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    padding: 30px;
    margin: 30px 0;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.current-download-progress {
    background: white;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 20px;
    border: 1px solid #e2e8f0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.file-info {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    gap: 12px;
}

.file-icon {
    font-size: 24px;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f7fafc;
    border-radius: 8px;
}

.file-details h4 {
    margin: 0;
    font-size: 16px;
    color: #2d3748;
    font-weight: 600;
}

.file-details .file-type {
    font-size: 12px;
    color: #718096;
    margin: 4px 0;
    text-transform: uppercase;
}

.file-size {
    font-size: 14px;
    color: #4a5568;
    margin-left: auto;
}

.progress-bar {
    width: 100%;
    height: 12px;
    background-color: #e2e8f0;
    border-radius: 6px;
    overflow: hidden;
    margin: 15px 0;
    position: relative;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    border-radius: 6px;
    transition: width 0.3s ease;
    width: 0%;
    position: relative;
    overflow: hidden;
}

.progress-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.progress-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 11px;
    font-weight: 600;
    color: #2d3748;
    text-shadow: 0 1px 2px rgba(255,255,255,0.8);
    z-index: 1;
}

.download-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 16px;
    margin-top: 15px;
}

.stat-item {
    text-align: center;
    padding: 12px;
    background: #f7fafc;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
}

.stat-label {
    font-size: 11px;
    color: #718096;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 4px;
}

.stat-value {
    font-size: 14px;
    color: #2d3748;
    font-weight: 600;
}

.batch-progress {
    background: white;
    border-radius: 12px;
    padding: 20px;
    border: 1px solid #e2e8f0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.batch-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.batch-title {
    font-weight: 600;
    color: #2d3748;
    margin: 0;
    font-size: 16px;
}

.batch-counter {
    font-size: 14px;
    color: #718096;
    background: #f7fafc;
    padding: 4px 12px;
    border-radius: 12px;
    border: 1px solid #e2e8f0;
}

.batch-progress-bar {
    width: 100%;
    height: 8px;
    background-color: #e2e8f0;
    border-radius: 4px;
    overflow: hidden;
    position: relative;
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
}

.batch-progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #48bb78 0%, #38a169 100%);
    border-radius: 4px;
    transition: width 0.3s ease;
    width: 0%;
}

.batch-progress-text {
    position: absolute;
    top: 50%;
    right: 8px;
    transform: translateY(-50%);
    font-size: 10px;
    font-weight: 600;
    color: #2d3748;
}
