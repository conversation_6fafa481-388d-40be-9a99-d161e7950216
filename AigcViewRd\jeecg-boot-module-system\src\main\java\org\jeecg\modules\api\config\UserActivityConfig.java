package org.jeecg.modules.api.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;

/**
 * @Description: 用户活跃状态追踪配置类
 * @Author: AigcView
 * @Date: 2025-01-30
 * @Version: V1.0
 */
@Data
@Component
@Validated
@ConfigurationProperties(prefix = "aigc.user-activity")
public class UserActivityConfig {
    
    /**
     * 是否启用用户活跃状态追踪
     * 默认启用，生产环境可以通过配置文件控制
     */
    @NotNull
    private Boolean enabled = true;
    
    /**
     * 测试模式（只记录日志，不实际更新数据库）
     * 用于灰度发布和功能验证
     */
    @NotNull
    private Boolean testMode = false;
    
    /**
     * 缓存持续时间（秒）
     * 用户活跃状态在Redis中的缓存时间
     * 建议值：300秒（5分钟）
     */
    @Min(60)
    @Max(3600)
    private Integer cacheDuration = 300;
    
    /**
     * 批量更新大小
     * 每次批量更新数据库的记录数量
     * 建议值：50-200，根据数据库性能调整
     */
    @Min(10)
    @Max(1000)
    private Integer batchSize = 100;
    
    /**
     * 批量更新间隔（秒）
     * 批量更新任务的执行间隔
     * 建议值：10-60秒
     */
    @Min(5)
    @Max(300)
    private Integer batchInterval = 10;
    
    /**
     * 性能降级阈值（毫秒）
     * 当API响应时间超过此阈值时，启动降级机制
     * 建议值：1000ms
     */
    @Min(100)
    @Max(10000)
    private Long performanceThreshold = 1000L;
    
    /**
     * 核心API列表（这些API使用最轻量级处理）
     * 对于支付、登录等核心业务API，使用最小化的性能影响策略
     */
    private List<String> criticalApis = Arrays.asList(
        "/payment/", "/login", "/logout", "/order/", "/api/auth/"
    );
    
    /**
     * 灰度发布比例（0-100）
     * 控制新功能的用户覆盖比例
     * 0表示关闭，100表示全量开启
     */
    @Min(0)
    @Max(100)
    private Integer rolloutPercentage = 100;
    
    /**
     * Redis键前缀
     * 用于区分不同环境和应用的缓存数据
     */
    private String redisKeyPrefix = "aigc:user:activity:";
    
    /**
     * 缓存过期时间（秒）
     * Redis缓存的过期时间，应该大于cacheDuration
     */
    @Min(120)
    @Max(7200)
    private Integer cacheExpireTime = 600;
    
    /**
     * 最大重试次数
     * 数据库操作失败时的重试次数
     */
    @Min(0)
    @Max(5)
    private Integer maxRetryCount = 3;
    
    /**
     * 重试间隔（毫秒）
     * 重试操作之间的等待时间
     */
    @Min(100)
    @Max(5000)
    private Long retryInterval = 1000L;
    
    /**
     * 是否启用性能监控
     * 开启后会记录详细的性能指标
     */
    @NotNull
    private Boolean enablePerformanceMonitoring = true;
    
    /**
     * 性能监控采样率（0-100）
     * 控制性能监控的采样比例，避免过多的监控数据
     */
    @Min(0)
    @Max(100)
    private Integer performanceMonitoringSampleRate = 10;
    
    /**
     * 离线用户清理间隔（分钟）
     * 定期清理长时间未活跃的用户记录
     */
    @Min(5)
    @Max(1440)
    private Integer offlineUserCleanupInterval = 60;
    
    /**
     * 用户离线阈值（分钟）
     * 超过此时间未活跃的用户将被标记为离线
     */
    @Min(5)
    @Max(120)
    private Integer userOfflineThreshold = 15;
    
    /**
     * 是否启用异步处理
     * 开启后用户活跃状态更新将异步执行
     */
    @NotNull
    private Boolean enableAsyncProcessing = true;
    
    /**
     * 异步线程池大小
     * 处理用户活跃状态更新的线程池大小
     */
    @Min(1)
    @Max(20)
    private Integer asyncThreadPoolSize = 5;
    
    /**
     * 队列容量
     * 异步处理队列的最大容量
     */
    @Min(100)
    @Max(10000)
    private Integer queueCapacity = 1000;
    
    /**
     * 是否启用降级机制
     * 在系统高负载时自动降级功能
     */
    @NotNull
    private Boolean enableDegradation = true;
    
    /**
     * 降级恢复时间（秒）
     * 降级后多长时间尝试恢复正常功能
     */
    @Min(30)
    @Max(3600)
    private Integer degradationRecoveryTime = 300;
    
    /**
     * 检查配置是否有效
     * @return 配置是否有效
     */
    public boolean isValid() {
        return enabled != null && 
               cacheDuration != null && cacheDuration > 0 &&
               batchSize != null && batchSize > 0 &&
               batchInterval != null && batchInterval > 0 &&
               performanceThreshold != null && performanceThreshold > 0 &&
               rolloutPercentage != null && rolloutPercentage >= 0 && rolloutPercentage <= 100;
    }
    
    /**
     * 获取Redis完整键名
     * @param suffix 键后缀
     * @return 完整的Redis键名
     */
    public String getRedisKey(String suffix) {
        return redisKeyPrefix + suffix;
    }
    
    /**
     * 判断是否为核心API
     * @param apiPath API路径
     * @return 是否为核心API
     */
    public boolean isCriticalApi(String apiPath) {
        if (apiPath == null || criticalApis == null) {
            return false;
        }
        return criticalApis.stream().anyMatch(apiPath::contains);
    }
    
    /**
     * 判断是否在灰度发布范围内
     * @param userId 用户ID
     * @return 是否在灰度范围内
     */
    public boolean isInRollout(String userId) {
        if (rolloutPercentage == null || rolloutPercentage == 0) {
            return false;
        }
        if (rolloutPercentage >= 100) {
            return true;
        }
        // 基于用户ID的哈希值进行灰度控制
        int hash = Math.abs(userId.hashCode() % 100);
        return hash < rolloutPercentage;
    }

    /**
     * 获取用户离线阈值（分钟）
     * @return 用户离线阈值
     */
    public Integer getOfflineThresholdMinutes() {
        return userOfflineThreshold;
    }
}
