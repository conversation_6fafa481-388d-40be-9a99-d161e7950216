<!DOCTYPE html>
<html lang="zh-cmn-Hans">
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width,initial-scale=1.0">
  <title>智界AIGC - 全局加载器测试</title>
  <style>
    html, body, #app {
      height: 100%;
      margin: 0px;
      padding: 0px;
    }

    #loader-wrapper {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 999999;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
    }

    #loader {
      display: block;
      position: relative;
      width: 80px;
      height: 80px;
      border-radius: 50%;
      border: 4px solid rgba(255, 255, 255, 0.2);
      border-top-color: #ffffff;
      border-right-color: #ffffff;
      animation: spin 1.5s linear infinite;
      z-index: 1001;
      margin-bottom: 2rem;
    }

    /* 智界AIGC Logo */
    .loader-logo {
      position: relative;
      margin-bottom: 1rem;
      z-index: 1002;
    }

    .loader-logo-icon {
      width: 60px;
      height: 60px;
      background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
      border-radius: 16px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 2rem;
      font-weight: bold;
      margin: 0 auto 1rem;
      box-shadow: 0 8px 32px rgba(59, 130, 246, 0.3);
      animation: logoFloat 3s ease-in-out infinite;
    }

    .loader-logo-text {
      color: white;
      font-size: 1.8rem;
      font-weight: 700;
      text-align: center;
      margin-bottom: 0.5rem;
      letter-spacing: 2px;
    }

    .loader-logo-subtitle {
      color: rgba(255, 255, 255, 0.8);
      font-size: 1rem;
      text-align: center;
      font-weight: 400;
    }

    @keyframes logoFloat {
      0%, 100% { transform: translateY(0px); }
      50% { transform: translateY(-10px); }
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    #loader-wrapper .loader-section {
      position: fixed;
      top: 0;
      width: 51%;
      height: 100%;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      z-index: 1000;
      transform: translateX(0);
    }

    #loader-wrapper .loader-section.section-left {
      left: 0;
    }

    #loader-wrapper .loader-section.section-right {
      right: 0;
    }

    /* Loaded */
    .loaded #loader-wrapper .loader-section.section-left {
      transform: translateX(-100%);
      transition: all 0.7s 0.3s cubic-bezier(0.645, 0.045, 0.355, 1.000);
    }

    .loaded #loader-wrapper .loader-section.section-right {
      transform: translateX(100%);
      transition: all 0.7s 0.3s cubic-bezier(0.645, 0.045, 0.355, 1.000);
    }

    .loaded #loader {
      opacity: 0;
      transition: all 0.3s ease-out;
    }

    .loaded #loader-wrapper {
      visibility: hidden;
      transform: translateY(-100%);
      transition: all 0.3s 1s ease-out;
    }

    #loader-wrapper .load_title {
      color: #ffffff;
      font-size: 1.1rem;
      font-weight: 500;
      text-align: center;
      z-index: 1002;
      opacity: 0.9;
      line-height: 1.6;
      margin-top: 1rem;
      letter-spacing: 1px;
    }

    #loader-wrapper .load_title span {
      font-weight: 400;
      font-style: normal;
      font-size: 0.9rem;
      color: rgba(255, 255, 255, 0.7);
      display: block;
      margin-top: 0.5rem;
    }

    /* 加载进度条 */
    .loader-progress {
      width: 200px;
      height: 4px;
      background: rgba(255, 255, 255, 0.2);
      border-radius: 2px;
      margin: 1.5rem auto 0;
      overflow: hidden;
      position: relative;
    }

    .loader-progress-bar {
      height: 100%;
      background: linear-gradient(90deg, #ffffff, rgba(255, 255, 255, 0.8));
      border-radius: 2px;
      animation: progressMove 2s ease-in-out infinite;
    }

    @keyframes progressMove {
      0% { width: 0%; }
      50% { width: 70%; }
      100% { width: 100%; }
    }

    /* 测试内容样式 */
    .test-content {
      padding: 2rem;
      text-align: center;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    }

    .test-content h1 {
      color: #1e293b;
      margin-bottom: 1rem;
    }

    .test-content p {
      color: #64748b;
      margin-bottom: 2rem;
      line-height: 1.6;
    }

    .test-btn {
      padding: 12px 24px;
      background: #3b82f6;
      color: white;
      border: none;
      border-radius: 8px;
      cursor: pointer;
      font-size: 1rem;
      font-weight: 500;
      transition: all 0.3s ease;
    }

    .test-btn:hover {
      background: #2563eb;
      transform: translateY(-2px);
    }
  </style>
</head>

<body>
  <div id="app">
    <div id="loader-wrapper">
      <div class="loader-section section-left"></div>
      <div class="loader-section section-right"></div>
      
      <!-- 智界AIGC Logo -->
      <div class="loader-logo">
        <div class="loader-logo-icon">智</div>
        <div class="loader-logo-text">智界AIGC</div>
        <div class="loader-logo-subtitle">AI驱动的内容生成平台</div>
      </div>
      
      <!-- 加载动画 -->
      <div id="loader"></div>
      
      <!-- 加载文字和进度条 -->
      <div class="load_title">
        正在初始化系统
        <span>请稍候，正在为您准备最佳体验...</span>
        <div class="loader-progress">
          <div class="loader-progress-bar"></div>
        </div>
      </div>
    </div>

    <!-- 页面内容 -->
    <div class="test-content">
      <h1>智界AIGC 全局加载器测试</h1>
      <p>这是测试页面内容，用于验证全局加载器的效果。</p>
      <p>加载器已经完成，现在显示的是实际页面内容。</p>
      <button class="test-btn" onclick="showLoader()">重新显示加载器</button>
      <button class="test-btn" onclick="hideLoader()">隐藏加载器</button>
    </div>
  </div>

  <script>
    // 模拟页面加载完成
    window.addEventListener('load', function() {
      // 延迟3秒后隐藏加载器，模拟真实加载时间
      setTimeout(function() {
        document.body.classList.add('loaded');
      }, 3000);
    });

    // 测试函数
    function showLoader() {
      document.body.classList.remove('loaded');
      document.getElementById('loader-wrapper').style.visibility = 'visible';
      document.getElementById('loader-wrapper').style.transform = 'translateY(0)';
      
      // 3秒后自动隐藏
      setTimeout(function() {
        document.body.classList.add('loaded');
      }, 3000);
    }

    function hideLoader() {
      document.body.classList.add('loaded');
    }
  </script>
</body>
</html>
