@echo off
title 剪映小助手启动器

echo.
echo ========================================
echo        剪映小助手 - 智界AigcView
echo ========================================
echo.

:: 检查Node.js
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [错误] 未找到Node.js
    echo 请先安装Node.js: https://nodejs.org/
    echo.
    pause
    exit /b 1
)

echo [成功] Node.js已安装
node --version

:: 检查npm
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [错误] npm不可用
    echo.
    pause
    exit /b 1
)

echo [成功] npm已安装
npm --version

:: 检查依赖
if not exist "node_modules" (
    echo.
    echo [信息] 首次运行，正在安装依赖...
    echo.
    npm install
    if %errorlevel% neq 0 (
        echo [错误] 依赖安装失败
        echo.
        pause
        exit /b 1
    )
    echo [成功] 依赖安装完成
)

:: 启动应用
echo.
echo [信息] 正在启动剪映小助手...
echo.

if "%1"=="dev" (
    echo [信息] 开发模式启动
    npm run dev
) else (
    echo [信息] 生产模式启动
    npm start
)

if %errorlevel% neq 0 (
    echo.
    echo [错误] 应用启动失败
    echo.
    pause
    exit /b 1
)

echo.
echo [信息] 应用已关闭
pause
