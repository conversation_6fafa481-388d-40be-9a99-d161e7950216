package org.jeecg.modules.jianyingpro.config;

import org.springframework.context.annotation.Configuration;

/**
 * 超级剪映小助手配置类
 * 
 * <AUTHOR>
 * @since 2025-01-01
 */
@Configuration
public class JianyingProConfig {
    
    /**
     * 错误码前缀
     */
    public static final String ERROR_CODE_PREFIX = "JIANYINGPRO_";
    
    /**
     * 参数错误码前缀
     */
    public static final String PARAM_ERROR_PREFIX = "PARAM_";
    
    /**
     * 业务错误码前缀
     */
    public static final String BUSINESS_ERROR_PREFIX = "BUSINESS_";
    
    /**
     * 系统错误码前缀
     */
    public static final String SYSTEM_ERROR_PREFIX = "SYSTEM_";
    
    /**
     * 默认请求超时时间（毫秒）
     */
    public static final int DEFAULT_TIMEOUT = 30000;
    
    /**
     * 音频处理超时时间（毫秒）
     */
    public static final int AUDIO_PROCESS_TIMEOUT = 60000;
    
    /**
     * 视频处理超时时间（毫秒）
     */
    public static final int VIDEO_PROCESS_TIMEOUT = 120000;
}
