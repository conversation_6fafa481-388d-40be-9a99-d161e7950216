# Electron客户端功能测试用例

## 测试环境准备
1. 启动Electron应用
2. 确保剪映路径已正确设置
3. 准备测试链接

## 测试用例1：详细日志功能
### 测试目标
验证下载日志是否详细，不再显示"下载任务：当前xxx"

### 测试步骤
1. 输入单个草稿链接
2. 点击"导入到剪映"
3. 观察下载日志

### 预期结果
- ❌ 不应该显示："下载任务：当前xxx"
- ✅ 应该显示：
  - "📥 开始下载草稿JSON: [URL]"
  - "✅ 草稿JSON下载完成 (耗时: XXXms, 大小: XXX 字符)"
  - "📥 开始下载音频文件: [文件名]"
  - "✅ 音频文件下载完成: [文件名] (XXXkB, 耗时: XXXms)"

## 测试用例2：链接检测重复提示修复
### 测试目标
验证链接检测提示只显示一次

### 测试步骤
1. 清空输入框
2. 输入一个有效的草稿链接
3. 观察通知和日志

### 预期结果
- ✅ 只显示一次检测提示
- ❌ 不应该出现重复的检测消息

## 测试用例3：输入框内容保持
### 测试目标
验证导入后输入框内容不被清空

### 测试步骤
1. 输入草稿链接
2. 点击"导入到剪映"
3. 等待导入完成
4. 检查输入框内容

### 预期结果
- ✅ 输入框内容应该保持不变
- ✅ 如果链接有效，导入按钮应该保持可用状态

## 测试用例4：多链接批量下载
### 测试目标
验证多行链接的批量处理功能

### 测试步骤
1. 在输入框中输入多行链接（每行一个）：
```
https://www.aigcview.com/JianYingDraft?draft=https://aigcview-plub.tos-cn-guangzhou.volces.com/jianying-assistant/drafts/2025/07/06/zj_draft_1.json
https://www.aigcview.com/JianYingDraft?draft=https://aigcview-plub.tos-cn-guangzhou.volces.com/jianying-assistant/drafts/2025/07/06/zj_draft_2.json
https://aigcview-plub.tos-cn-guangzhou.volces.com/jianying-assistant/drafts/2025/07/06/zj_draft_3.json
```
2. 点击"导入到剪映"
3. 观察处理过程

### 预期结果
- ✅ 应该识别出3个链接
- ✅ 应该逐个处理每个链接
- ✅ 应该显示批量处理结果
- ✅ 控制台应该显示："找到链接数量: 3"

## 测试用例5：智能链接提取
### 测试目标
验证从包装链接中提取真实TOS地址的功能

### 测试步骤
1. 输入包装格式的链接：
```
https://www.aigcview.com/JianYingDraft?draft=https://aigcview-plub.tos-cn-guangzhou.volces.com/jianying-assistant/drafts/2025/07/06/zj_draft_20250706_023204_32039d51.json
```
2. 点击"导入到剪映"
3. 检查控制台日志

### 预期结果
- ✅ 控制台应该显示："原始链接: [包装链接]"
- ✅ 控制台应该显示："提取的TOS链接: [真实TOS链接]"
- ✅ 应该成功下载和导入草稿

## 测试用例6：混合链接格式处理
### 测试目标
验证同时处理多种链接格式

### 测试步骤
1. 输入混合格式的链接：
```
https://www.aigcview.com/JianYingDraft?draft=https://aigcview-plub.tos-cn-guangzhou.volces.com/jianying-assistant/drafts/2025/07/06/zj_draft_1.json
https://aigcview.com/JianYingDraft?draft=https://aigcview-plub.tos-cn-guangzhou.volces.com/jianying-assistant/drafts/2025/07/06/zj_draft_2.json
https://aigcview-plub.tos-cn-guangzhou.volces.com/jianying-assistant/drafts/2025/07/06/zj_draft_3.json
```
2. 点击"导入到剪映"

### 预期结果
- ✅ 应该正确识别和提取所有3个链接
- ✅ 应该成功处理所有格式

## 测试用例7：批量处理结果显示
### 测试目标
验证批量处理的结果显示

### 测试场景A：全部成功
- 输入3个有效链接
- 预期：显示"批量导入成功！成功导入 3 个草稿"

### 测试场景B：部分成功
- 输入2个有效链接 + 1个无效链接
- 预期：显示"批量导入部分成功：成功 2 个，失败 1 个"

### 测试场景C：全部失败
- 输入3个无效链接
- 预期：显示"批量导入失败：所有 3 个草稿都导入失败"

## 验收标准
- [ ] 所有测试用例通过
- [ ] 日志信息详细且准确
- [ ] 无重复提示
- [ ] 输入框行为正确
- [ ] 多链接批量处理正常
- [ ] 链接提取功能正确
- [ ] 批量结果显示准确
