import TOSBase from '../../base';
export interface UploadPartCopyInput {
    bucket?: string;
    key: string;
    partNumber: number;
    uploadId: string;
    srcBucket?: string;
    srcKey?: string;
    srcVersionID?: string;
    copySourceRange?: string;
    copySourceRangeStart?: number;
    copySourceRangeEnd?: number;
    copySourceSSECAlgorithm?: string;
    copySourceSSECKey?: string;
    copySourceSSECKeyMD5?: string;
    ssecAlgorithm?: string;
    ssecKey?: string;
    ssecKeyMD5?: string;
    /**
     * unit: bit/s
     * server side traffic limit
     **/
    trafficLimit?: number;
    headers?: {
        [key: string]: string | undefined;
        'x-tos-copy-source'?: string;
        'x-tos-copy-source-range'?: string;
        'x-tos-copy-source-if-match'?: string;
        'x-tos-copy-source-if-modified-since'?: string;
        'x-tos-copy-source-if-none-match'?: string;
        'x-tos-copy-source-if-unmodified-since'?: string;
        'x-tos-copy-source-server-side-encryption-customer-algorithm'?: string;
        'x-tos-copy-source-server-side-encryption-customer-key'?: string;
        'x-tos-copy-source-server-side-encryption-customer-key-MD5'?: string;
    };
}
export interface UploadPartCopyOutput {
    ETag: string;
    LastModified: string;
    SSECAlgorithm: string;
    SSECKeyMD5: string;
}
export declare function uploadPartCopy(this: TOSBase, input: UploadPartCopyInput): Promise<import("../../base").TosResponse<UploadPartCopyOutput>>;
