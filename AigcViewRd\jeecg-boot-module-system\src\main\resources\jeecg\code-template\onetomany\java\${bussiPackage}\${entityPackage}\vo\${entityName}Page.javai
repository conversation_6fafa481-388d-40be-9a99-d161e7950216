package ${bussiPackage}.${entityPackage}.vo;

import java.util.List;
import ${bussiPackage}.${entityPackage}.entity.${entityName};
<#list subTables as sub>
import ${bussiPackage}.${entityPackage}.entity.${sub.entityName};
</#list>
import lombok.Data;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecgframework.poi.excel.annotation.ExcelCollection;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import java.util.Date;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * @Description: ${tableVo.ftlDescription}
 * @Author: jeecg-boot
 * @Date:   ${.now?string["yyyy-MM-dd"]}
 * @Version: V1.0
 */
@Data
@ApiModel(value="${tableName}Page对象", description="${tableVo.ftlDescription}")
public class ${entityName}Page {
	
    <#list originalColumns as po>
	/**${po.filedComment}*/
	<#if po.fieldName == primaryKeyField>
	<#else>
    <#if po.fieldType =='java.util.Date'>
  	<#if po.fieldDbType =='date'>
  	@Excel(name = "${po.filedComment}", width = 15, format = "yyyy-MM-dd")
  	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
  	<#elseif po.fieldDbType =='datetime'>
  	@Excel(name = "${po.filedComment}", width = 20, format = "yyyy-MM-dd HH:mm:ss")
  	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
  	</#if>
  	<#else>
  	@Excel(name = "${po.filedComment}", width = 15)
    </#if>
  </#if>
	private <#if po.fieldType=='java.sql.Blob'>byte[]<#else>${po.fieldType}</#if> ${po.fieldName};
	</#list>
	
	<#list subTables as sub>
	@ExcelCollection(name="${sub.ftlDescription}")
	@ApiModelProperty(value = "${sub.ftlDescription}")
	private List<${sub.entityName}> ${sub.entityName?uncap_first}List;
	</#list>
	
}
