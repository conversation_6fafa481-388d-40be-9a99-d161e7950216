package org.jeecg.modules.api.config;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.core.env.Environment;

/**
 * @Description: 配置验证运行器
 * @Author: AigcView
 * @Date: 2025-01-30
 * @Version: V1.0
 */
@SpringBootApplication(scanBasePackages = "org.jeecg.modules")
public class ConfigurationValidationRunner {

    public static void main(String[] args) {
        System.setProperty("spring.profiles.active", "dev");
        
        try {
            ConfigurableApplicationContext context = SpringApplication.run(ConfigurationValidationRunner.class, args);
            
            // 获取配置类
            UserActivityConfig config = context.getBean(UserActivityConfig.class);
            Environment env = context.getBean(Environment.class);
            
            System.out.println("=== 配置验证开始 ===");
            
            // 验证配置注入
            if (config != null) {
                System.out.println("✅ UserActivityConfig 配置类注入成功");
            } else {
                System.out.println("❌ UserActivityConfig 配置类注入失败");
                return;
            }
            
            // 验证基础配置
            System.out.println("\n--- 基础配置验证 ---");
            System.out.println("启用状态: " + config.getEnabled());
            System.out.println("测试模式: " + config.getTestMode());
            System.out.println("缓存持续时间: " + config.getCacheDuration() + "秒");
            System.out.println("Redis键前缀: " + config.getRedisKeyPrefix());
            
            // 验证批量处理配置
            System.out.println("\n--- 批量处理配置验证 ---");
            System.out.println("批量大小: " + config.getBatchSize());
            System.out.println("批量间隔: " + config.getBatchInterval() + "秒");
            
            // 验证性能配置
            System.out.println("\n--- 性能配置验证 ---");
            System.out.println("性能阈值: " + config.getPerformanceThreshold() + "毫秒");
            System.out.println("性能监控: " + config.getEnablePerformanceMonitoring());
            System.out.println("监控采样率: " + config.getPerformanceMonitoringSampleRate() + "%");
            
            // 验证灰度发布配置
            System.out.println("\n--- 灰度发布配置验证 ---");
            System.out.println("灰度比例: " + config.getRolloutPercentage() + "%");
            
            // 验证核心API配置
            System.out.println("\n--- 核心API配置验证 ---");
            if (config.getCriticalApis() != null) {
                System.out.println("核心API数量: " + config.getCriticalApis().size());
                config.getCriticalApis().forEach(api -> System.out.println("  - " + api));
            }
            
            // 验证重试配置
            System.out.println("\n--- 重试配置验证 ---");
            System.out.println("最大重试次数: " + config.getMaxRetryCount());
            System.out.println("重试间隔: " + config.getRetryInterval() + "毫秒");
            
            // 验证用户状态管理配置
            System.out.println("\n--- 用户状态管理配置验证 ---");
            System.out.println("离线用户清理间隔: " + config.getOfflineUserCleanupInterval() + "分钟");
            System.out.println("用户离线阈值: " + config.getUserOfflineThreshold() + "分钟");
            
            // 验证异步处理配置
            System.out.println("\n--- 异步处理配置验证 ---");
            System.out.println("异步处理: " + config.getEnableAsyncProcessing());
            System.out.println("线程池大小: " + config.getAsyncThreadPoolSize());
            System.out.println("队列容量: " + config.getQueueCapacity());
            
            // 验证降级机制配置
            System.out.println("\n--- 降级机制配置验证 ---");
            System.out.println("降级机制: " + config.getEnableDegradation());
            System.out.println("降级恢复时间: " + config.getDegradationRecoveryTime() + "秒");
            
            // 验证配置方法
            System.out.println("\n--- 配置方法验证 ---");
            String testRedisKey = config.getRedisKey("test_user");
            System.out.println("Redis键生成: " + testRedisKey);
            
            boolean isCritical1 = config.isCriticalApi("/payment/create");
            boolean isCritical2 = config.isCriticalApi("/api/user/profile");
            System.out.println("核心API检测 (/payment/create): " + isCritical1);
            System.out.println("核心API检测 (/api/user/profile): " + isCritical2);
            
            boolean inRollout1 = config.isInRollout("test_user");
            boolean inRollout2 = config.isInRollout("admin");
            System.out.println("灰度发布检测 (test_user): " + inRollout1);
            System.out.println("灰度发布检测 (admin): " + inRollout2);
            
            // 验证配置有效性
            System.out.println("\n--- 配置有效性验证 ---");
            boolean isValid = config.isValid();
            System.out.println("配置有效性: " + (isValid ? "✅ 有效" : "❌ 无效"));
            
            // 验证环境属性
            System.out.println("\n--- 环境属性验证 ---");
            String enabledProp = env.getProperty("aigc.user-activity.enabled");
            String batchSizeProp = env.getProperty("aigc.user-activity.batch-size");
            String rolloutProp = env.getProperty("aigc.user-activity.rollout-percentage");
            System.out.println("环境属性 enabled: " + enabledProp);
            System.out.println("环境属性 batch-size: " + batchSizeProp);
            System.out.println("环境属性 rollout-percentage: " + rolloutProp);
            
            // 验证配置约束
            System.out.println("\n--- 配置约束验证 ---");
            boolean constraintsValid = true;
            
            if (config.getBatchSize() <= 0) {
                System.out.println("❌ 批量大小应该大于0");
                constraintsValid = false;
            }
            
            if (config.getBatchInterval() <= 0) {
                System.out.println("❌ 批量间隔应该大于0");
                constraintsValid = false;
            }
            
            if (config.getRolloutPercentage() < 0 || config.getRolloutPercentage() > 100) {
                System.out.println("❌ 灰度比例应该在0-100之间");
                constraintsValid = false;
            }
            
            if (config.getBatchInterval() > config.getCacheDuration()) {
                System.out.println("❌ 批量间隔不应该大于缓存持续时间");
                constraintsValid = false;
            }
            
            if (config.getCacheDuration() > config.getCacheExpireTime()) {
                System.out.println("❌ 缓存持续时间不应该大于缓存过期时间");
                constraintsValid = false;
            }
            
            if (constraintsValid) {
                System.out.println("✅ 所有配置约束验证通过");
            }
            
            System.out.println("\n=== 配置验证完成 ===");
            
            if (isValid && constraintsValid) {
                System.out.println("🎉 配置验证全部通过！");
                System.exit(0);
            } else {
                System.out.println("⚠️ 配置验证存在问题，请检查配置文件");
                System.exit(1);
            }
            
        } catch (Exception e) {
            System.err.println("❌ 配置验证过程中发生错误: " + e.getMessage());
            e.printStackTrace();
            System.exit(1);
        }
    }
}
