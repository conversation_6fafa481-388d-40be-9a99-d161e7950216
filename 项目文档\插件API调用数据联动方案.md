# 🔗 插件API调用数据联动方案 V2.1

## 📋 **方案概述**

本方案基于现有数据库表结构，通过完善字段和联动逻辑，实现插件API调用成功后的完整数据追踪和统计更新。用户通过 `pluginKey`（插件唯一标识）调用API，系统通过该标识查询完整插件信息，并进行全方位的数据联动更新。

## 🎯 **设计原则**

1. **基于现有表结构** - 不新增表，只在现有表上添加必要字段
2. **pluginKey驱动** - 用户传递pluginKey，后端查询完整插件信息
3. **事务一致性** - 所有联动更新在同一事务中完成
4. **数据完整性** - 确保每次插件调用都有完整的数据记录
5. **性能优化** - 异步处理非关键统计数据
6. **可扩展性** - 为未来功能扩展预留字段

## 📋 **业务流程梳理**

```
用户调用插件API
├── 传递参数: { apiKey, pluginKey, ...其他参数 }
├── 后端接收: pluginKey = "xiaohongshufabu"
├── 验证流程:
│   ├── 验证 apiKey 有效性
│   ├── 通过 pluginKey 查询 aigc_plub_shop 表
│   ├── 获取插件信息: id, plubname, plubwrite, neednum
│   ├── 检查用户余额是否充足
│   └── 扣除用户余额
├── 执行插件功能 (如生成小红书内容)
└── 🔥 触发数据联动更新 (核心部分)
```

## 📊 **数据库表结构完善**

### 1. **aicg_user_api_usage** (API使用记录表)
**新增字段**：
```sql
ALTER TABLE aicg_user_api_usage
ADD COLUMN plugin_id VARCHAR(36) COMMENT '插件ID - 关联aigc_plub_shop.id',
ADD COLUMN plugin_key VARCHAR(100) COMMENT '插件唯一标识 - 用户传递的参数',
ADD COLUMN plugin_name VARCHAR(100) COMMENT '插件名称 - 从aigc_plub_shop.plubname获取';

-- 添加索引
CREATE INDEX idx_plugin_id ON aicg_user_api_usage(plugin_id);
CREATE INDEX idx_plugin_key ON aicg_user_api_usage(plugin_key);
```

### 2. **aicg_user_transaction** (交易记录表)
**新增字段**：
```sql
ALTER TABLE aicg_user_transaction
ADD COLUMN plugin_id VARCHAR(36) COMMENT '插件ID - 关联aigc_plub_shop.id',
ADD COLUMN plugin_key VARCHAR(100) COMMENT '插件唯一标识',
ADD COLUMN plugin_name VARCHAR(100) COMMENT '插件名称';

-- 添加索引
CREATE INDEX idx_plugin_id ON aicg_user_transaction(plugin_id);
```

### 3. **aigc_plub_shop** (插件商城表)
**新增字段**：
```sql
ALTER TABLE aigc_plub_shop
ADD COLUMN last_used_time DATETIME COMMENT '最后使用时间',
ADD COLUMN monthly_income DECIMAL(10,2) DEFAULT 0.00 COMMENT '本月收益',
ADD COLUMN monthly_calls INT DEFAULT 0 COMMENT '本月调用次数';

-- 添加索引
CREATE INDEX idx_last_used_time ON aigc_plub_shop(last_used_time);
CREATE INDEX idx_monthly_income ON aigc_plub_shop(monthly_income);
```

### 4. **aigc_plub_author** (插件创作者表)
**新增字段**：
```sql
ALTER TABLE aigc_plub_author
ADD COLUMN monthly_income DECIMAL(10,2) DEFAULT 0.00 COMMENT '本月收益',
ADD COLUMN monthly_calls INT DEFAULT 0 COMMENT '本月调用次数',
ADD COLUMN last_active_time DATETIME COMMENT '最后活跃时间';

-- 添加索引
CREATE INDEX idx_monthly_income ON aigc_plub_author(monthly_income);
CREATE INDEX idx_last_active_time ON aigc_plub_author(last_active_time);
```

### 5. **aicg_user_profile** (用户扩展表)
**新增字段**：
```sql
ALTER TABLE aicg_user_profile
ADD COLUMN plugin_call_count INT DEFAULT 0 COMMENT '插件调用总次数',
ADD COLUMN last_plugin_call_time DATETIME COMMENT '最后插件调用时间',
ADD COLUMN monthly_plugin_cost DECIMAL(10,2) DEFAULT 0.00 COMMENT '本月插件消费';

-- 添加索引
CREATE INDEX idx_plugin_call_count ON aicg_user_profile(plugin_call_count);
CREATE INDEX idx_last_plugin_call_time ON aicg_user_profile(last_plugin_call_time);
```

## 🔄 **完整数据联动流程**

### **阶段1: API验证和扣费** ✅ (已实现)
```java
// 在 AigcApiServiceImpl.verifyPluginAndDeduct() 中
public PluginVerifyResult verifyPluginAndDeduct(String userId, String pluginKey) {
    // 1. 通过 pluginKey 查询插件信息
    AigcPlubShop plugin = plubShopService.getByPluginKey(pluginKey);

    // 2. 验证和扣费逻辑...

    // 3. 🔥 扣费成功后触发数据联动
    if (deductSuccess) {
        // 调用数据联动方法
        performDataLinkage(userId, plugin, needAmount);
    }

    return result;
}
```

### **阶段2: 数据联动更新** ❌ (需要实现)
```java
@Transactional(rollbackFor = Exception.class)
private void performDataLinkage(String userId, AigcPlubShop plugin, BigDecimal amount) {
    try {
        // 1. 更新插件商城统计
        updatePluginShopStats(plugin.getId(), amount);

        // 2. 更新插件创作者统计
        updatePluginAuthorStats(plugin.getPlubwrite(), amount);

        // 3. 更新用户扩展统计
        updateUserProfileStats(userId, amount);

        log.info("数据联动更新成功 - 插件: {} ({}), 用户: {}, 金额: {}",
                plugin.getPlubname(), plugin.getPluginKey(), userId, amount);

    } catch (Exception e) {
        log.error("数据联动更新失败 - 插件: {}, 用户: {}", plugin.getPluginKey(), userId, e);
        // 不抛出异常，避免影响主流程
    }
}
```

### **阶段3: 补充记录表信息** ❌ (需要实现)
在记录API使用和交易时，补充插件相关信息。

## 🛠️ **详细技术实现**

### **1. 更新插件商城统计**
```java
private void updatePluginShopStats(String pluginId, BigDecimal amount) {
    String sql = "UPDATE aigc_plub_shop SET " +
                "usernum = usernum + 1, " +                    // 总调用次数+1
                "income = income + ?, " +                      // 总收益累加
                "monthly_calls = monthly_calls + 1, " +        // 本月调用次数+1
                "monthly_income = monthly_income + ?, " +      // 本月收益累加
                "last_used_time = NOW(), " +                   // 更新最后使用时间
                "update_time = NOW() " +                       // 更新修改时间
                "WHERE id = ?";

    int result = jdbcTemplate.update(sql, amount, amount, pluginId);

    if (result > 0) {
        log.info("插件商城统计更新成功 - 插件ID: {}, 金额: {}", pluginId, amount);
    } else {
        log.warn("插件商城统计更新失败 - 插件ID: {}", pluginId);
    }
}
```

### **2. 更新插件创作者统计**
```java
private void updatePluginAuthorStats(String authorId, BigDecimal amount) {
    String sql = "UPDATE aigc_plub_author SET " +
                "plubusenum = plubusenum + 1, " +              // 总使用次数+1
                "total_income = total_income + ?, " +          // 总收益累加
                "monthly_calls = monthly_calls + 1, " +        // 本月调用次数+1
                "monthly_income = monthly_income + ?, " +      // 本月收益累加
                "last_active_time = NOW(), " +                 // 更新最后活跃时间
                "update_time = NOW() " +                       // 更新修改时间
                "WHERE id = ?";

    int result = jdbcTemplate.update(sql, amount, amount, authorId);

    if (result > 0) {
        log.info("创作者统计更新成功 - 创作者ID: {}, 金额: {}", authorId, amount);
    } else {
        log.warn("创作者统计更新失败 - 创作者ID: {}", authorId);
    }
}
```

### **3. 更新用户扩展统计**
```java
private void updateUserProfileStats(String userId, BigDecimal amount) {
    String sql = "UPDATE aicg_user_profile SET " +
                "total_consumption = total_consumption + ?, " +    // 累计消费累加
                "plugin_call_count = plugin_call_count + 1, " +    // 插件调用次数+1
                "monthly_plugin_cost = monthly_plugin_cost + ?, " + // 本月插件消费累加
                "last_plugin_call_time = NOW(), " +                // 更新最后调用时间
                "update_time = NOW() " +                           // 更新修改时间
                "WHERE user_id = ?";

    int result = jdbcTemplate.update(sql, amount, amount, userId);

    if (result > 0) {
        log.info("用户统计更新成功 - 用户ID: {}, 金额: {}", userId, amount);
    } else {
        log.warn("用户统计更新失败 - 用户ID: {}", userId);
    }
}
```

### **4. 补充API使用记录的插件信息**
```java
// 修改 AicgUserApiUsageServiceImpl.recordUsage() 方法
public void recordUsage(String userId, String apiKey, String apiEndpoint,
                       String apiMethod, String requestParams,
                       int responseStatus, int responseTime, Integer tokensUsed,
                       BigDecimal costAmount, String ipAddress, String userAgent,
                       String errorMessage) {

    // 提取插件信息
    String pluginKey = extractPluginKey(requestParams);
    String pluginId = null;
    String pluginName = null;

    if (pluginKey != null && !pluginKey.isEmpty()) {
        try {
            AigcPlubShop plugin = plubShopService.getByPluginKey(pluginKey);
            if (plugin != null) {
                pluginId = plugin.getId();
                pluginName = plugin.getPlubname();
            }
        } catch (Exception e) {
            log.warn("查询插件信息失败 - pluginKey: {}", pluginKey, e);
        }
    }

    // 创建记录
    AicgUserApiUsage usage = new AicgUserApiUsage();
    usage.setId(IdWorker.getIdStr());
    usage.setUserId(userId);
    usage.setApiKey(apiKey);
    usage.setApiEndpoint(apiEndpoint);
    usage.setApiMethod(apiMethod);
    usage.setRequestParams(requestParams);
    usage.setResponseStatus(responseStatus);
    usage.setResponseTime(responseTime);
    usage.setTokensUsed(tokensUsed);
    usage.setCostAmount(costAmount);
    usage.setIpAddress(ipAddress);
    usage.setUserAgent(userAgent);
    usage.setErrorMessage(errorMessage);
    usage.setCallTime(new Date());
    usage.setCreateTime(new Date());

    // 🔥 补充插件信息
    usage.setPluginId(pluginId);
    usage.setPluginKey(pluginKey);
    usage.setPluginName(pluginName);

    save(usage);

    log.info("API使用记录保存成功 - 用户: {}, 接口: {}, 插件: {} ({})",
            userId, apiEndpoint, pluginName, pluginKey);
}
```

### **5. 补充交易记录的插件信息（双表写入机制）**
```java
// 修改 AicgUserProfileServiceImpl.consume() 方法
@Override
@Transactional(rollbackFor = Exception.class)
public boolean consume(String userId, BigDecimal amount, String description, String operatorId) {
    if (amount == null || amount.compareTo(BigDecimal.ZERO) <= 0) {
        return false;
    }

    AicgUserProfile profile = getByUserId(userId);
    if (profile == null || profile.getAccountBalance().compareTo(amount) < 0) {
        return false;
    }

    BigDecimal balanceBefore = profile.getAccountBalance();
    BigDecimal balanceAfter = balanceBefore.subtract(amount);

    // 扣减余额
    int result = baseMapper.deductBalance(userId, amount, operatorId);
    if (result > 0) {
        // 🔥 如果是插件消费，提取插件信息
        String pluginId = null;
        String pluginKey = null;
        String pluginName = null;

        if (description != null && description.contains("调用插件:")) {
            // 从描述中提取插件名称，然后查询插件信息
            String extractedPluginName = description.replace("调用插件: ", "").trim();
            try {
                AigcPlubShop plugin = plubShopService.getByPluginName(extractedPluginName);
                if (plugin != null) {
                    pluginId = plugin.getId();
                    pluginKey = plugin.getPluginKey();
                    pluginName = plugin.getPlubname();
                }
            } catch (Exception e) {
                log.warn("查询插件信息失败 - pluginName: {}, 错误: {}", extractedPluginName, e.getMessage());
            }
        }

        // 🔥 记录交易到订单表（aicg_user_transaction）
        try {
            String transactionId = UUID.randomUUID().toString().replace("-", "");
            String orderType = "other";
            int orderStatus = 3; // 已完成

            if (description != null && description.contains("调用插件:")) {
                orderType = "plugin"; // 插件类型
            }

            String insertSql = "INSERT INTO aicg_user_transaction (" +
                "id, user_id, transaction_type, amount, balance_before, balance_after, " +
                "description, transaction_time, create_by, create_time, " +
                "order_status, order_type, plugin_id, plugin_key, plugin_name" +
                ") VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

            jdbcTemplate.update(insertSql,
                transactionId, userId, 1, amount, balanceBefore, balanceAfter,
                description, new Date(), operatorId, new Date(),
                orderStatus, orderType, pluginId, pluginKey, pluginName
            );

            log.info("交易记录已保存到订单表 - 用户: {}, 金额: {}, 类型: {}", userId, amount, orderType);
        } catch (Exception e) {
            log.error("保存交易记录到订单表失败", e);
        }

        // 同时记录到详细记录表（aicg_user_record）
        AicgUserRecord record = new AicgUserRecord();
        record.setUserId(userId);
        record.setTransactionType(1); // 消费
        record.setAmount(amount);
        record.setBalanceBefore(balanceBefore);
        record.setBalanceAfter(balanceAfter);
        record.setDescription(description);
        record.setTransactionTime(new Date());
        record.setCreateBy(operatorId);
        record.setCreateTime(new Date());

        // 🔥 补充插件信息
        record.setPluginId(pluginId);
        record.setPluginKey(pluginKey);
        record.setPluginName(pluginName);

        userRecordService.save(record);
        return true;
    }
    return false;
}
```

## 📈 **数据联动效果验证**

### **验证SQL查询**
```sql
-- 1. 验证插件统计是否正确
SELECT
    ps.plubname,
    ps.plugin_key,
    ps.usernum as total_calls,
    ps.income as total_income,
    ps.monthly_calls,
    ps.monthly_income,
    ps.last_used_time
FROM aigc_plub_shop ps
WHERE ps.plugin_key = 'xiaohongshufabu';

-- 2. 验证创作者统计是否正确
SELECT
    pa.authorname,
    pa.plubusenum as total_usage,
    pa.total_income,
    pa.monthly_calls,
    pa.monthly_income,
    pa.last_active_time
FROM aigc_plub_author pa
WHERE pa.id = (SELECT plubwrite FROM aigc_plub_shop WHERE plugin_key = 'xiaohongshufabu');

-- 3. 验证用户统计是否正确
SELECT
    up.nickname,
    up.total_consumption,
    up.plugin_call_count,
    up.monthly_plugin_cost,
    up.last_plugin_call_time
FROM aicg_user_profile up
WHERE up.user_id = 'your_user_id';

-- 4. 验证API使用记录是否包含插件信息
SELECT
    ua.api_endpoint,
    ua.plugin_key,
    ua.plugin_name,
    ua.cost_amount,
    ua.call_time
FROM aicg_user_api_usage ua
WHERE ua.user_id = 'your_user_id'
AND ua.plugin_key IS NOT NULL
ORDER BY ua.call_time DESC;

-- 5. 验证交易记录是否包含插件信息
SELECT
    ut.description,
    ut.plugin_key,
    ut.plugin_name,
    ut.amount,
    ut.transaction_time
FROM aicg_user_transaction ut
WHERE ut.user_id = 'your_user_id'
AND ut.plugin_key IS NOT NULL
ORDER BY ut.transaction_time DESC;
```

## 🎯 **数据联动价值**

### **运营价值**
1. **插件热度排行** - `ORDER BY usernum DESC`
2. **插件收益排行** - `ORDER BY income DESC`
3. **月度收益统计** - `SUM(monthly_income)`
4. **创作者排行榜** - `ORDER BY total_income DESC`
5. **用户活跃度分析** - `ORDER BY plugin_call_count DESC`

### **用户价值**
1. **个人消费统计** - 显示总消费和月消费
2. **使用频率分析** - 显示调用次数和最后使用时间
3. **详细使用记录** - 完整的插件使用历史

### **商业价值**
1. **精确的分成结算** - 基于准确的收益数据
2. **数据驱动的产品优化** - 基于真实使用数据
3. **用户价值分析** - 识别高价值用户和行为模式

## 🚀 **实施计划**

### **第一步: 数据库字段扩展**
```bash
# 执行所有 ALTER TABLE 语句
# 创建相关索引
# 验证字段添加成功
```

### **第二步: 代码实现**
```bash
# 1. 实现数据联动方法
# 2. 修改API使用记录逻辑
# 3. 修改交易记录逻辑
# 4. 添加日志和异常处理
```

### **第三步: 测试验证**
```bash
# 1. 单元测试各个联动方法
# 2. 集成测试完整流程
# 3. 验证数据一致性
# 4. 性能测试
```

### **第四步: 上线部署**
```bash
# 1. 备份现有数据
# 2. 执行数据库变更
# 3. 部署新代码
# 4. 监控数据联动效果
```

## ⚠️ **注意事项**

1. **事务一致性** - 所有联动更新必须在同一事务中完成
2. **异常处理** - 联动更新失败不应影响主流程
3. **性能考虑** - 大量数据时考虑异步处理
4. **数据备份** - 执行ALTER TABLE前必须备份数据
5. **索引优化** - 根据查询需求添加合适索引
6. **pluginKey准确性** - 确保用户传递的pluginKey能正确查询到插件信息

---

**文档版本**: V2.1
**创建时间**: 2025-06-21
**更新时间**: 2025-06-21
**状态**: 已实施 ✅
**负责人**: 开发团队

### **V2.1 更新内容**
- ✅ 实现双表写入机制：同时写入 `aicg_user_transaction` 和 `aicg_user_record` 表
- ✅ 自动设置订单状态和类型：插件调用自动设为已完成状态
- ✅ 完善插件信息补充：自动提取和补充完整的插件信息
- ✅ 修复订单记录页面显示问题：正确显示订单状态、类型和统计数据
