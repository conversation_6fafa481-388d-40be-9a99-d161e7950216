package org.jeecg.modules.system.service;

import java.util.List;
import java.util.Map;

import org.jeecg.modules.system.entity.SysSensitiveWord;
import org.springframework.web.multipart.MultipartFile;

import com.baomidou.mybatisplus.extension.service.IService;

/**
 * @Description: 敏感词管理
 * @Author: jeecg-boot
 * @Date: 2025-06-22
 * @Version: V1.0
 */
public interface ISysSensitiveWordService extends IService<SysSensitiveWord> {

    /**
     * 获取所有启用的敏感词
     * @return 敏感词列表
     */
    List<String> getEnabledWords();

    /**
     * 刷新敏感词缓存
     */
    void refreshCache();

    /**
     * 检测文本中的敏感词
     * @param text 待检测文本
     * @return 检测结果
     */
    SensitiveWordCheckResult checkSensitiveWords(String text);

    /**
     * 检测文本中的敏感词（带用户信息）
     * @param text 待检测文本
     * @param userId 用户ID
     * @param module 模块名称
     * @param ipAddress IP地址
     * @return 检测结果
     */
    SensitiveWordCheckResult checkSensitiveWords(String text, String userId, String module, String ipAddress);

    /**
     * 替换文本中的敏感词
     * @param text 待替换文本
     * @param replacement 替换字符
     * @return 替换后的文本
     */
    String replaceSensitiveWords(String text, String replacement);

    /**
     * 批量导入敏感词
     * @param file Excel文件
     * @param createBy 创建人
     * @return 导入结果
     */
    ImportResult importSensitiveWords(MultipartFile file, String createBy);

    /**
     * 从houbb库导入敏感词
     * @param createBy 创建人
     * @return 导入结果
     */
    ImportResult importFromHoubb(String createBy);

    /**
     * 获取敏感词统计信息
     * @return 统计信息
     */
    Map<String, Object> getStatistics();

    /**
     * 获取热门敏感词
     * @param limit 数量限制
     * @return 热门敏感词列表
     */
    List<Map<String, Object>> getTopHitWords(int limit);

    /**
     * 获取分类统计
     * @return 分类统计
     */
    List<Map<String, Object>> getCategoryStatistics();

    /**
     * 获取级别统计
     * @return 级别统计
     */
    List<Map<String, Object>> getLevelStatistics();

    /**
     * 增加敏感词命中次数
     * @param word 敏感词
     */
    void incrementHitCount(String word);

    /**
     * 记录敏感词命中日志
     * @param word 敏感词
     * @param hitText 命中文本
     * @param userId 用户ID
     * @param module 模块
     * @param ipAddress IP地址
     */
    void recordHitLog(String word, String hitText, String userId, String module, String ipAddress);

    /**
     * 敏感词检测结果
     */
    class SensitiveWordCheckResult {
        private boolean hasSensitiveWord;
        private List<String> sensitiveWords;
        private String message;
        private String replacedText;

        public SensitiveWordCheckResult() {}

        public SensitiveWordCheckResult(boolean hasSensitiveWord, List<String> sensitiveWords, String message) {
            this.hasSensitiveWord = hasSensitiveWord;
            this.sensitiveWords = sensitiveWords;
            this.message = message;
        }

        // Getters and Setters
        public boolean isHasSensitiveWord() { return hasSensitiveWord; }
        public void setHasSensitiveWord(boolean hasSensitiveWord) { this.hasSensitiveWord = hasSensitiveWord; }
        
        public List<String> getSensitiveWords() { return sensitiveWords; }
        public void setSensitiveWords(List<String> sensitiveWords) { this.sensitiveWords = sensitiveWords; }
        
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
        
        public String getReplacedText() { return replacedText; }
        public void setReplacedText(String replacedText) { this.replacedText = replacedText; }
    }

    /**
     * 导入结果
     */
    class ImportResult {
        private boolean success;
        private String message;
        private int totalCount;
        private int successCount;
        private int failCount;
        private List<String> errorMessages;

        public ImportResult() {}

        public ImportResult(boolean success, String message, int totalCount, int successCount, int failCount) {
            this.success = success;
            this.message = message;
            this.totalCount = totalCount;
            this.successCount = successCount;
            this.failCount = failCount;
        }

        // Getters and Setters
        public boolean isSuccess() { return success; }
        public void setSuccess(boolean success) { this.success = success; }
        
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
        
        public int getTotalCount() { return totalCount; }
        public void setTotalCount(int totalCount) { this.totalCount = totalCount; }
        
        public int getSuccessCount() { return successCount; }
        public void setSuccessCount(int successCount) { this.successCount = successCount; }
        
        public int getFailCount() { return failCount; }
        public void setFailCount(int failCount) { this.failCount = failCount; }
        
        public List<String> getErrorMessages() { return errorMessages; }
        public void setErrorMessages(List<String> errorMessages) { this.errorMessages = errorMessages; }
    }
}
