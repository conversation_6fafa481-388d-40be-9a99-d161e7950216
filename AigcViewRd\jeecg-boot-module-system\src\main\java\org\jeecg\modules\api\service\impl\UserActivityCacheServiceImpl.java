package org.jeecg.modules.api.service.impl;

import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.util.RedisUtil;
import org.jeecg.modules.api.config.UserActivityConfig;
import org.jeecg.modules.api.dto.UserActivityUpdateDTO;
import org.jeecg.modules.api.entity.AicgOnlineUsers;
import org.jeecg.modules.api.service.IUserActivityCacheService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.Duration;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @Description: 用户活跃状态缓存服务实现
 * @Author: AigcView
 * @Date: 2025-01-30
 * @Version: V1.0
 */
@Slf4j
@Service
public class UserActivityCacheServiceImpl implements IUserActivityCacheService {

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private UserActivityConfig userActivityConfig;

    // ==================== 缓存键前缀常量 ====================
    private static final String USER_INFO_PREFIX = "user:";
    private static final String USER_ACTIVITY_PREFIX = "status:";
    private static final String SESSION_PREFIX = "session:";
    private static final String BATCH_UPDATE_PREFIX = "batch:";
    private static final String STATS_PREFIX = "stats:";

    // ==================== 用户基本信息缓存 ====================

    @Override
    public boolean cacheUserInfo(String userId, Map<String, Object> userInfo) {
        try {
            if (userId == null || userInfo == null) {
                return false;
            }
            
            String cacheKey = getUserInfoCacheKey(userId);
            redisTemplate.opsForValue().set(cacheKey, userInfo, 
                userActivityConfig.getCacheExpireTime(), TimeUnit.SECONDS);
            
            log.debug("缓存用户信息成功 - 用户ID: {}, 缓存键: {}", userId, cacheKey);
            return true;
            
        } catch (Exception e) {
            log.error("缓存用户信息失败 - 用户ID: {}, 错误: {}", userId, e.getMessage(), e);
            return false;
        }
    }

    @Override
    @SuppressWarnings("unchecked")
    public Map<String, Object> getCachedUserInfo(String userId) {
        try {
            if (userId == null) {
                return null;
            }
            
            String cacheKey = getUserInfoCacheKey(userId);
            Object cached = redisTemplate.opsForValue().get(cacheKey);
            
            if (cached instanceof Map) {
                log.debug("获取缓存用户信息成功 - 用户ID: {}", userId);
                return (Map<String, Object>) cached;
            }
            
            log.debug("缓存用户信息未命中 - 用户ID: {}", userId);
            return null;
            
        } catch (Exception e) {
            log.error("获取缓存用户信息失败 - 用户ID: {}, 错误: {}", userId, e.getMessage(), e);
            return null;
        }
    }

    @Override
    public Map<String, Map<String, Object>> batchGetUserInfo(List<String> userIds) {
        Map<String, Map<String, Object>> result = new HashMap<>();
        
        if (CollectionUtils.isEmpty(userIds)) {
            return result;
        }
        
        try {
            // 构建缓存键列表
            List<String> cacheKeys = userIds.stream()
                .map(this::getUserInfoCacheKey)
                .collect(Collectors.toList());
            
            // 批量获取
            List<Object> cachedValues = redisTemplate.opsForValue().multiGet(cacheKeys);
            
            // 组装结果
            for (int i = 0; i < userIds.size(); i++) {
                Object cached = cachedValues.get(i);
                if (cached instanceof Map) {
                    @SuppressWarnings("unchecked")
                    Map<String, Object> userInfo = (Map<String, Object>) cached;
                    result.put(userIds.get(i), userInfo);
                }
            }
            
            log.debug("批量获取用户信息 - 请求: {}, 命中: {}", userIds.size(), result.size());
            return result;
            
        } catch (Exception e) {
            log.error("批量获取用户信息失败 - 错误: {}", e.getMessage(), e);
            return result;
        }
    }

    // ==================== 用户活跃状态缓存 ====================

    @Override
    public boolean cacheUserActivity(String userId, AicgOnlineUsers onlineUser) {
        try {
            if (userId == null || onlineUser == null) {
                return false;
            }
            
            String cacheKey = getUserActivityCacheKey(userId);
            redisTemplate.opsForValue().set(cacheKey, onlineUser, 
                userActivityConfig.getCacheExpireTime(), TimeUnit.SECONDS);
            
            log.debug("缓存用户活跃状态成功 - 用户ID: {}", userId);
            return true;
            
        } catch (Exception e) {
            log.error("缓存用户活跃状态失败 - 用户ID: {}, 错误: {}", userId, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public AicgOnlineUsers getCachedUserActivity(String userId) {
        try {
            if (userId == null) {
                return null;
            }
            
            String cacheKey = getUserActivityCacheKey(userId);
            Object cached = redisTemplate.opsForValue().get(cacheKey);
            
            if (cached instanceof AicgOnlineUsers) {
                log.debug("获取缓存用户活跃状态成功 - 用户ID: {}", userId);
                return (AicgOnlineUsers) cached;
            }
            
            log.debug("缓存用户活跃状态未命中 - 用户ID: {}", userId);
            return null;
            
        } catch (Exception e) {
            log.error("获取缓存用户活跃状态失败 - 用户ID: {}, 错误: {}", userId, e.getMessage(), e);
            return null;
        }
    }

    @Override
    public int batchCacheUserActivity(Map<String, AicgOnlineUsers> userActivities) {
        if (CollectionUtils.isEmpty(userActivities)) {
            return 0;
        }
        
        int successCount = 0;
        try {
            // 使用批量操作优化性能
            for (Map.Entry<String, AicgOnlineUsers> entry : userActivities.entrySet()) {
                try {
                    String userId = entry.getKey();
                    AicgOnlineUsers onlineUser = entry.getValue();
                    String cacheKey = getUserActivityCacheKey(userId);
                    redisTemplate.opsForValue().set(cacheKey, onlineUser,
                        userActivityConfig.getCacheExpireTime(), TimeUnit.SECONDS);
                    successCount++;
                } catch (Exception e) {
                    log.error("批量缓存用户活跃状态失败 - 用户ID: {}, 错误: {}", entry.getKey(), e.getMessage());
                }
            }

            log.debug("批量缓存用户活跃状态完成 - 总数: {}, 成功: {}", userActivities.size(), successCount);
            
        } catch (Exception e) {
            log.error("批量缓存用户活跃状态失败 - 错误: {}", e.getMessage(), e);
        }
        
        return successCount;
    }

    @Override
    public Map<String, AicgOnlineUsers> batchGetUserActivity(List<String> userIds) {
        Map<String, AicgOnlineUsers> result = new HashMap<>();
        
        if (CollectionUtils.isEmpty(userIds)) {
            return result;
        }
        
        try {
            // 构建缓存键列表
            List<String> cacheKeys = userIds.stream()
                .map(this::getUserActivityCacheKey)
                .collect(Collectors.toList());
            
            // 批量获取
            List<Object> cachedValues = redisTemplate.opsForValue().multiGet(cacheKeys);
            
            // 组装结果
            for (int i = 0; i < userIds.size(); i++) {
                Object cached = cachedValues.get(i);
                if (cached instanceof AicgOnlineUsers) {
                    result.put(userIds.get(i), (AicgOnlineUsers) cached);
                }
            }
            
            log.debug("批量获取用户活跃状态 - 请求: {}, 命中: {}", userIds.size(), result.size());
            return result;
            
        } catch (Exception e) {
            log.error("批量获取用户活跃状态失败 - 错误: {}", e.getMessage(), e);
            return result;
        }
    }

    // ==================== 会话缓存 ====================

    @Override
    public boolean cacheUserSession(String sessionId, String userId, Map<String, Object> sessionInfo) {
        try {
            if (sessionId == null || userId == null) {
                return false;
            }
            
            // 会话信息中包含用户ID
            Map<String, Object> fullSessionInfo = new HashMap<>(sessionInfo != null ? sessionInfo : new HashMap<>());
            fullSessionInfo.put("userId", userId);
            fullSessionInfo.put("cacheTime", System.currentTimeMillis());
            
            String cacheKey = getSessionCacheKey(sessionId);
            redisTemplate.opsForValue().set(cacheKey, fullSessionInfo, 
                userActivityConfig.getCacheExpireTime(), TimeUnit.SECONDS);
            
            log.debug("缓存会话信息成功 - 会话ID: {}, 用户ID: {}", sessionId, userId);
            return true;
            
        } catch (Exception e) {
            log.error("缓存会话信息失败 - 会话ID: {}, 用户ID: {}, 错误: {}", sessionId, userId, e.getMessage(), e);
            return false;
        }
    }

    @Override
    @SuppressWarnings("unchecked")
    public Map<String, Object> getCachedSession(String sessionId) {
        try {
            if (sessionId == null) {
                return null;
            }
            
            String cacheKey = getSessionCacheKey(sessionId);
            Object cached = redisTemplate.opsForValue().get(cacheKey);
            
            if (cached instanceof Map) {
                log.debug("获取缓存会话信息成功 - 会话ID: {}", sessionId);
                return (Map<String, Object>) cached;
            }
            
            log.debug("缓存会话信息未命中 - 会话ID: {}", sessionId);
            return null;
            
        } catch (Exception e) {
            log.error("获取缓存会话信息失败 - 会话ID: {}, 错误: {}", sessionId, e.getMessage(), e);
            return null;
        }
    }

    @Override
    public String getUserIdBySession(String sessionId) {
        try {
            Map<String, Object> sessionInfo = getCachedSession(sessionId);
            if (sessionInfo != null && sessionInfo.containsKey("userId")) {
                return String.valueOf(sessionInfo.get("userId"));
            }
            return null;
            
        } catch (Exception e) {
            log.error("根据会话ID获取用户ID失败 - 会话ID: {}, 错误: {}", sessionId, e.getMessage(), e);
            return null;
        }
    }

    @Override
    public boolean removeSessionCache(String sessionId) {
        try {
            if (sessionId == null) {
                return false;
            }

            String cacheKey = getSessionCacheKey(sessionId);
            Boolean deleted = redisTemplate.delete(cacheKey);

            log.debug("删除会话缓存 - 会话ID: {}, 结果: {}", sessionId, deleted);
            return Boolean.TRUE.equals(deleted);

        } catch (Exception e) {
            log.error("删除会话缓存失败 - 会话ID: {}, 错误: {}", sessionId, e.getMessage(), e);
            return false;
        }
    }

    // ==================== 批量更新缓存 ====================

    @Override
    public boolean cacheBatchUpdate(String batchKey, List<UserActivityUpdateDTO> updateList) {
        try {
            if (batchKey == null || CollectionUtils.isEmpty(updateList)) {
                return false;
            }

            String cacheKey = getBatchUpdateCacheKey(batchKey);
            // 批量更新数据缓存时间较短，只缓存30分钟
            redisTemplate.opsForValue().set(cacheKey, updateList, 30, TimeUnit.MINUTES);

            log.debug("缓存批量更新数据成功 - 批量键: {}, 数据量: {}", batchKey, updateList.size());
            return true;

        } catch (Exception e) {
            log.error("缓存批量更新数据失败 - 批量键: {}, 错误: {}", batchKey, e.getMessage(), e);
            return false;
        }
    }

    @Override
    @SuppressWarnings("unchecked")
    public List<UserActivityUpdateDTO> getCachedBatchUpdate(String batchKey) {
        try {
            if (batchKey == null) {
                return null;
            }

            String cacheKey = getBatchUpdateCacheKey(batchKey);
            Object cached = redisTemplate.opsForValue().get(cacheKey);

            if (cached instanceof List) {
                log.debug("获取缓存批量更新数据成功 - 批量键: {}", batchKey);
                return (List<UserActivityUpdateDTO>) cached;
            }

            log.debug("缓存批量更新数据未命中 - 批量键: {}", batchKey);
            return null;

        } catch (Exception e) {
            log.error("获取缓存批量更新数据失败 - 批量键: {}, 错误: {}", batchKey, e.getMessage(), e);
            return null;
        }
    }

    @Override
    public boolean removeBatchUpdateCache(String batchKey) {
        try {
            if (batchKey == null) {
                return false;
            }

            String cacheKey = getBatchUpdateCacheKey(batchKey);
            Boolean deleted = redisTemplate.delete(cacheKey);

            log.debug("删除批量更新缓存 - 批量键: {}, 结果: {}", batchKey, deleted);
            return Boolean.TRUE.equals(deleted);

        } catch (Exception e) {
            log.error("删除批量更新缓存失败 - 批量键: {}, 错误: {}", batchKey, e.getMessage(), e);
            return false;
        }
    }

    // ==================== 统计信息缓存 ====================

    @Override
    public boolean cacheOnlineStats(String statsKey, Map<String, Object> stats, long expireSeconds) {
        try {
            if (statsKey == null || stats == null) {
                return false;
            }

            String cacheKey = getStatsCacheKey(statsKey);
            redisTemplate.opsForValue().set(cacheKey, stats, expireSeconds, TimeUnit.SECONDS);

            log.debug("缓存统计信息成功 - 统计键: {}, 过期时间: {}秒", statsKey, expireSeconds);
            return true;

        } catch (Exception e) {
            log.error("缓存统计信息失败 - 统计键: {}, 错误: {}", statsKey, e.getMessage(), e);
            return false;
        }
    }

    @Override
    @SuppressWarnings("unchecked")
    public Map<String, Object> getCachedOnlineStats(String statsKey) {
        try {
            if (statsKey == null) {
                return null;
            }

            String cacheKey = getStatsCacheKey(statsKey);
            Object cached = redisTemplate.opsForValue().get(cacheKey);

            if (cached instanceof Map) {
                log.debug("获取缓存统计信息成功 - 统计键: {}", statsKey);
                return (Map<String, Object>) cached;
            }

            log.debug("缓存统计信息未命中 - 统计键: {}", statsKey);
            return null;

        } catch (Exception e) {
            log.error("获取缓存统计信息失败 - 统计键: {}, 错误: {}", statsKey, e.getMessage(), e);
            return null;
        }
    }

    // ==================== 缓存管理 ====================

    @Override
    public int warmupUserActivityCache(List<String> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return 0;
        }

        int successCount = 0;
        try {
            log.info("开始预热用户活跃状态缓存 - 用户数量: {}", userIds.size());

            // 这里可以从数据库批量查询用户活跃状态，然后缓存
            // 由于需要依赖数据库服务，这里先提供框架，具体实现可以后续完善
            for (String userId : userIds) {
                try {
                    // TODO: 从数据库查询用户活跃状态
                    // AicgOnlineUsers onlineUser = onlineUsersService.getByUserId(userId);
                    // if (onlineUser != null) {
                    //     cacheUserActivity(userId, onlineUser);
                    //     successCount++;
                    // }

                    // 暂时只记录日志
                    log.debug("预热用户缓存 - 用户ID: {}", userId);
                    successCount++;

                } catch (Exception e) {
                    log.error("预热用户缓存失败 - 用户ID: {}, 错误: {}", userId, e.getMessage());
                }
            }

            log.info("用户活跃状态缓存预热完成 - 总数: {}, 成功: {}", userIds.size(), successCount);

        } catch (Exception e) {
            log.error("用户活跃状态缓存预热失败 - 错误: {}", e.getMessage(), e);
        }

        return successCount;
    }

    @Override
    public int cleanExpiredCache() {
        int cleanedCount = 0;
        try {
            log.info("开始清理过期缓存数据");

            // 获取所有相关的缓存键
            String pattern = userActivityConfig.getRedisKeyPrefix() + "*";
            Set<String> keys = redisTemplate.keys(pattern);

            if (!CollectionUtils.isEmpty(keys)) {
                for (String key : keys) {
                    try {
                        Long expire = redisTemplate.getExpire(key, TimeUnit.SECONDS);
                        // 如果键已过期或即将过期（剩余时间小于60秒）
                        if (expire != null && expire <= 60) {
                            Boolean deleted = redisTemplate.delete(key);
                            if (Boolean.TRUE.equals(deleted)) {
                                cleanedCount++;
                            }
                        }
                    } catch (Exception e) {
                        log.error("清理缓存键失败 - 键: {}, 错误: {}", key, e.getMessage());
                    }
                }
            }

            log.info("过期缓存数据清理完成 - 清理数量: {}", cleanedCount);

        } catch (Exception e) {
            log.error("清理过期缓存数据失败 - 错误: {}", e.getMessage(), e);
        }

        return cleanedCount;
    }

    @Override
    public int clearUserCache(String userId) {
        if (userId == null) {
            return 0;
        }

        int clearedCount = 0;
        try {
            log.info("开始清理用户缓存 - 用户ID: {}", userId);

            // 清理用户相关的所有缓存
            List<String> keysToDelete = Arrays.asList(
                getUserInfoCacheKey(userId),
                getUserActivityCacheKey(userId)
            );

            for (String key : keysToDelete) {
                try {
                    Boolean deleted = redisTemplate.delete(key);
                    if (Boolean.TRUE.equals(deleted)) {
                        clearedCount++;
                    }
                } catch (Exception e) {
                    log.error("删除用户缓存键失败 - 键: {}, 错误: {}", key, e.getMessage());
                }
            }

            log.info("用户缓存清理完成 - 用户ID: {}, 清理数量: {}", userId, clearedCount);

        } catch (Exception e) {
            log.error("清理用户缓存失败 - 用户ID: {}, 错误: {}", userId, e.getMessage(), e);
        }

        return clearedCount;
    }

    @Override
    public Map<String, Object> getCacheStats() {
        Map<String, Object> stats = new HashMap<>();

        try {
            // 获取Redis基本信息
            Properties info = redisTemplate.getConnectionFactory().getConnection().info();

            // 统计缓存键数量
            String pattern = userActivityConfig.getRedisKeyPrefix() + "*";
            Set<String> keys = redisTemplate.keys(pattern);

            stats.put("totalKeys", keys != null ? keys.size() : 0);
            stats.put("keyPrefix", userActivityConfig.getRedisKeyPrefix());
            stats.put("cacheExpireTime", userActivityConfig.getCacheExpireTime());
            stats.put("redisInfo", info);
            stats.put("checkTime", new Date());

            // 分类统计
            if (!CollectionUtils.isEmpty(keys)) {
                long userInfoCount = keys.stream().filter(key -> key.contains(USER_INFO_PREFIX)).count();
                long userActivityCount = keys.stream().filter(key -> key.contains(USER_ACTIVITY_PREFIX)).count();
                long sessionCount = keys.stream().filter(key -> key.contains(SESSION_PREFIX)).count();
                long batchCount = keys.stream().filter(key -> key.contains(BATCH_UPDATE_PREFIX)).count();
                long statsCount = keys.stream().filter(key -> key.contains(STATS_PREFIX)).count();

                stats.put("userInfoKeys", userInfoCount);
                stats.put("userActivityKeys", userActivityCount);
                stats.put("sessionKeys", sessionCount);
                stats.put("batchUpdateKeys", batchCount);
                stats.put("statsKeys", statsCount);
            }

            log.debug("获取缓存统计信息成功");

        } catch (Exception e) {
            log.error("获取缓存统计信息失败 - 错误: {}", e.getMessage(), e);
            stats.put("error", e.getMessage());
        }

        return stats;
    }

    @Override
    public boolean checkCacheHealth() {
        try {
            // 执行简单的ping操作
            String testKey = userActivityConfig.getRedisKeyPrefix() + "health:check";
            String testValue = "ping:" + System.currentTimeMillis();

            redisTemplate.opsForValue().set(testKey, testValue, 10, TimeUnit.SECONDS);
            Object retrieved = redisTemplate.opsForValue().get(testKey);

            boolean healthy = testValue.equals(retrieved);

            // 清理测试键
            redisTemplate.delete(testKey);

            log.debug("缓存健康检查 - 结果: {}", healthy);
            return healthy;

        } catch (Exception e) {
            log.error("缓存健康检查失败 - 错误: {}", e.getMessage(), e);
            return false;
        }
    }

    // ==================== 缓存键管理 ====================

    @Override
    public String getUserInfoCacheKey(String userId) {
        return userActivityConfig.getRedisKeyPrefix() + USER_INFO_PREFIX + userId;
    }

    @Override
    public String getUserActivityCacheKey(String userId) {
        return userActivityConfig.getRedisKeyPrefix() + USER_ACTIVITY_PREFIX + userId;
    }

    @Override
    public String getSessionCacheKey(String sessionId) {
        return userActivityConfig.getRedisKeyPrefix() + SESSION_PREFIX + sessionId;
    }

    @Override
    public String getBatchUpdateCacheKey(String batchKey) {
        return userActivityConfig.getRedisKeyPrefix() + BATCH_UPDATE_PREFIX + batchKey;
    }

    @Override
    public String getStatsCacheKey(String statsKey) {
        return userActivityConfig.getRedisKeyPrefix() + STATS_PREFIX + statsKey;
    }

    // ==================== 高级功能 ====================

    @Override
    public boolean expire(String key, long expireSeconds) {
        try {
            if (key == null || expireSeconds <= 0) {
                return false;
            }

            Boolean result = redisTemplate.expire(key, expireSeconds, TimeUnit.SECONDS);
            log.debug("设置缓存过期时间 - 键: {}, 过期时间: {}秒, 结果: {}", key, expireSeconds, result);
            return Boolean.TRUE.equals(result);

        } catch (Exception e) {
            log.error("设置缓存过期时间失败 - 键: {}, 错误: {}", key, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public long getExpire(String key) {
        try {
            if (key == null) {
                return -2;
            }

            Long expire = redisTemplate.getExpire(key, TimeUnit.SECONDS);
            return expire != null ? expire : -2;

        } catch (Exception e) {
            log.error("获取缓存过期时间失败 - 键: {}, 错误: {}", key, e.getMessage(), e);
            return -2;
        }
    }

    @Override
    public boolean exists(String key) {
        try {
            if (key == null) {
                return false;
            }

            Boolean exists = redisTemplate.hasKey(key);
            return Boolean.TRUE.equals(exists);

        } catch (Exception e) {
            log.error("检查缓存键是否存在失败 - 键: {}, 错误: {}", key, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean delete(String key) {
        try {
            if (key == null) {
                return false;
            }

            Boolean deleted = redisTemplate.delete(key);
            log.debug("删除缓存 - 键: {}, 结果: {}", key, deleted);
            return Boolean.TRUE.equals(deleted);

        } catch (Exception e) {
            log.error("删除缓存失败 - 键: {}, 错误: {}", key, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public long batchDelete(List<String> keys) {
        if (CollectionUtils.isEmpty(keys)) {
            return 0;
        }

        try {
            Long deletedCount = redisTemplate.delete(keys);
            log.debug("批量删除缓存 - 键数量: {}, 删除数量: {}", keys.size(), deletedCount);
            return deletedCount != null ? deletedCount : 0;

        } catch (Exception e) {
            log.error("批量删除缓存失败 - 键数量: {}, 错误: {}", keys.size(), e.getMessage(), e);
            return 0;
        }
    }

    @Override
    public long deleteByPattern(String pattern) {
        if (pattern == null) {
            return 0;
        }

        try {
            Set<String> keys = redisTemplate.keys(pattern);
            if (CollectionUtils.isEmpty(keys)) {
                return 0;
            }

            Long deletedCount = redisTemplate.delete(keys);
            log.debug("根据模式删除缓存 - 模式: {}, 匹配键数: {}, 删除数量: {}",
                pattern, keys.size(), deletedCount);
            return deletedCount != null ? deletedCount : 0;

        } catch (Exception e) {
            log.error("根据模式删除缓存失败 - 模式: {}, 错误: {}", pattern, e.getMessage(), e);
            return 0;
        }
    }

    @Override
    public Set<String> getKeysByPattern(String pattern) {
        if (pattern == null) {
            return new HashSet<>();
        }

        try {
            Set<String> keys = redisTemplate.keys(pattern);
            log.debug("根据模式获取缓存键 - 模式: {}, 匹配数量: {}", pattern, keys != null ? keys.size() : 0);
            return keys != null ? keys : new HashSet<>();

        } catch (Exception e) {
            log.error("根据模式获取缓存键失败 - 模式: {}, 错误: {}", pattern, e.getMessage(), e);
            return new HashSet<>();
        }
    }

    // ==================== 新增缓存方法实现 ====================

    @Override
    public boolean cacheStatsList(String statsKey, List<Map<String, Object>> statsList, long expireSeconds) {
        try {
            String cacheKey = getStatsCacheKey(statsKey);
            redisTemplate.opsForValue().set(cacheKey, statsList, Duration.ofSeconds(expireSeconds));
            log.debug("缓存统计列表数据成功: {}", statsKey);
            return true;
        } catch (Exception e) {
            log.error("缓存统计列表数据失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    @SuppressWarnings("unchecked")
    public List<Map<String, Object>> getCachedStatsList(String statsKey) {
        try {
            String cacheKey = getStatsCacheKey(statsKey);
            Object cached = redisTemplate.opsForValue().get(cacheKey);
            if (cached instanceof List) {
                return (List<Map<String, Object>>) cached;
            }
            return null;
        } catch (Exception e) {
            log.error("获取缓存统计列表数据失败: {}", e.getMessage(), e);
            return null;
        }
    }

    @Override
    public boolean cacheUserOnlineStatus(String userId, boolean isOnline, long expireSeconds) {
        try {
            String cacheKey = getUserActivityCacheKey(userId) + ":online_status";
            redisTemplate.opsForValue().set(cacheKey, isOnline, Duration.ofSeconds(expireSeconds));
            log.debug("缓存用户在线状态成功: {} -> {}", userId, isOnline);
            return true;
        } catch (Exception e) {
            log.error("缓存用户在线状态失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public Boolean getCachedUserOnlineStatus(String userId) {
        try {
            String cacheKey = getUserActivityCacheKey(userId) + ":online_status";
            Object cached = redisTemplate.opsForValue().get(cacheKey);
            if (cached instanceof Boolean) {
                return (Boolean) cached;
            }
            return null;
        } catch (Exception e) {
            log.error("获取缓存用户在线状态失败: {}", e.getMessage(), e);
            return null;
        }
    }

    @Override
    public boolean cacheStatsInt(String statsKey, int value, long expireSeconds) {
        try {
            String cacheKey = getStatsCacheKey(statsKey);
            redisTemplate.opsForValue().set(cacheKey, value, Duration.ofSeconds(expireSeconds));
            log.debug("缓存整数统计数据成功: {} -> {}", statsKey, value);
            return true;
        } catch (Exception e) {
            log.error("缓存整数统计数据失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public Integer getCachedStatsInt(String statsKey) {
        try {
            String cacheKey = getStatsCacheKey(statsKey);
            Object cached = redisTemplate.opsForValue().get(cacheKey);
            if (cached instanceof Integer) {
                return (Integer) cached;
            }
            return null;
        } catch (Exception e) {
            log.error("获取缓存整数统计数据失败: {}", e.getMessage(), e);
            return null;
        }
    }
}
