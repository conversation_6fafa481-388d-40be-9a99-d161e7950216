package org.jeecg.modules.api.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * @Description: 创作者收益统计视图对象
 * @Author: 智界AIGC
 * @Date: 2025-08-04
 * @Version: V1.0
 */
@Data
@ApiModel(value = "CreatorRevenueStatsVO", description = "创作者收益统计视图对象")
public class CreatorRevenueStatsVO implements Serializable {
    
    private static final long serialVersionUID = 1L;

    /**总收益*/
    @ApiModelProperty(value = "总收益（元）")
    private BigDecimal totalRevenue;

    /**本月收益*/
    @ApiModelProperty(value = "本月收益（元）")
    private BigDecimal monthRevenue;

    /**昨日收益*/
    @ApiModelProperty(value = "昨日收益（元）")
    private BigDecimal yesterdayRevenue;

    /**总销售次数*/
    @ApiModelProperty(value = "总销售次数")
    private Integer totalSales;

    /**本月销售次数*/
    @ApiModelProperty(value = "本月销售次数")
    private Integer monthSales;

    /**昨日销售次数*/
    @ApiModelProperty(value = "昨日销售次数")
    private Integer yesterdaySales;

    /**智能体总数*/
    @ApiModelProperty(value = "智能体总数")
    private Integer agentCount;

    /**已审核通过智能体数量*/
    @ApiModelProperty(value = "已审核通过智能体数量")
    private Integer approvedAgentCount;

    /**待审核智能体数量*/
    @ApiModelProperty(value = "待审核智能体数量")
    private Integer pendingAgentCount;

    /**已拒绝智能体数量*/
    @ApiModelProperty(value = "已拒绝智能体数量")
    private Integer rejectedAgentCount;

    /**工作流总数*/
    @ApiModelProperty(value = "工作流总数")
    private Integer workflowCount;

    /**智能体收益明细列表*/
    @ApiModelProperty(value = "智能体收益明细列表")
    private List<AgentRevenueItem> agentRevenueList;

    /**
     * 智能体收益明细项
     */
    @Data
    @ApiModel(value = "AgentRevenueItem", description = "智能体收益明细项")
    public static class AgentRevenueItem implements Serializable {
        
        private static final long serialVersionUID = 1L;

        /**智能体ID*/
        @ApiModelProperty(value = "智能体ID")
        private String agentId;

        /**智能体名称*/
        @ApiModelProperty(value = "智能体名称")
        private String agentName;

        /**智能体头像*/
        @ApiModelProperty(value = "智能体头像")
        private String agentAvatar;

        /**价格*/
        @ApiModelProperty(value = "价格（元）")
        private BigDecimal price;

        /**总收益*/
        @ApiModelProperty(value = "总收益（元）")
        private BigDecimal totalRevenue;

        /**本月收益*/
        @ApiModelProperty(value = "本月收益（元）")
        private BigDecimal monthRevenue;

        /**总销售次数*/
        @ApiModelProperty(value = "总销售次数")
        private Integer salesCount;

        /**本月销售次数*/
        @ApiModelProperty(value = "本月销售次数")
        private Integer monthSalesCount;

        /**审核状态*/
        @ApiModelProperty(value = "审核状态：1-待审核，2-已通过，3-已拒绝")
        private String auditStatus;

        /**审核状态文本*/
        @ApiModelProperty(value = "审核状态文本")
        private String auditStatusText;

        /**工作流数量*/
        @ApiModelProperty(value = "工作流数量")
        private Integer workflowCount;
    }
}
