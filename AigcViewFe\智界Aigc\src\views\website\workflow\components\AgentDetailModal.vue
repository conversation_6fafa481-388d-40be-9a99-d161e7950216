<template>
  <div class="agent-detail-modal-wrapper">
    <a-modal
      :visible="visible"
      :width="800"
      :footer="null"
      :closable="false"
      :maskClosable="true"
      @cancel="handleClose"
      class="agent-detail-modal custom-modal"
      :bodyStyle="{ padding: 0, borderRadius: '16px', overflow: 'hidden' }"
      :centered="true"
      :destroyOnClose="true"
    >
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <a-spin size="large" tip="加载中...">
        <div class="loading-placeholder"></div>
      </a-spin>
    </div>

    <!-- 主要内容 -->
    <div v-else class="modal-content">
      <!-- 自定义关闭按钮 -->
      <div class="custom-close-button" @click="handleClose">
        <a-icon type="close" />
      </div>

      <!-- 背景装饰 -->
      <div class="modal-background">
        <div class="bg-pattern"></div>
        <div class="bg-gradient"></div>
      </div>
      <!-- 1. 智能体基本信息区域 -->
      <div class="agent-info-section">
        <div class="agent-header">
          <!-- 智能体头像 -->
          <div class="agent-avatar">
            <img
              v-if="agentDetail.agentAvatar"
              :src="agentDetail.agentAvatar"
              :alt="agentDetail.agentName"
              @error="handleImageError"
            />
            <div v-else class="avatar-placeholder">
              <a-icon type="robot" />
            </div>
          </div>

          <!-- 智能体基本信息和价格 -->
          <div class="agent-info-and-price">
            <!-- 智能体基本信息 -->
            <div class="agent-basic-info">
              <h2 class="agent-name">{{ agentDetail.agentName }}</h2>
              <p class="agent-description">{{ agentDetail.agentDescription }}</p>

              <!-- 创作者信息 -->
              <div class="creator-info">
                <div class="creator-avatar">
                  <img
                    v-if="agentDetail.creatorInfo && agentDetail.creatorInfo.avatar"
                    :src="agentDetail.creatorInfo.avatar"
                    :alt="agentDetail.creatorInfo.name"
                    @error="handleCreatorAvatarError"
                  />
                  <a-icon v-else type="user" />
                </div>
                <div class="creator-details">
                  <div class="creator-name-line">
                    <span class="creator-name">{{ creatorName }}</span>
                    <span class="creator-type">{{ authorTypeText }}</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- 价格信息 -->
            <div class="price-section">
                <!-- SVIP免费优先显示（即使已购买也显示免费） -->
                <div v-if="agentDetail.isFree" class="price-container">
                  <span class="free-price">免费</span>
                </div>
                <div v-else-if="isPurchased" class="price-container">
                  <span class="purchased-price">已购买</span>
                </div>
                <div v-else-if="agentDetail.showDiscountPrice" class="price-container">
                  <span class="discount-price">¥{{ agentDetail.discountPrice || 0 }}</span>
                  <span class="original-price">¥{{ agentDetail.originalPrice || agentDetail.price || 0 }}</span>
                </div>
                <div v-else class="price-container">
                  <span class="current-price">¥{{ agentDetail.discountPrice || agentDetail.price || agentDetail.originalPrice || 0 }}</span>
                </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 2. 演示视频区域 -->
      <div v-if="agentDetail.demoVideo" class="demo-video-section">
        <h3 class="section-title">
          <a-icon type="play-circle" />
          演示视频
        </h3>
        <div class="video-container">
          <div class="video-wrapper" v-if="!videoError">
            <video
              ref="demoVideo"
              :src="agentDetail.demoVideo"
              class="demo-video"
              :muted="videoMuted"
              :autoplay="videoAutoplay"
              preload="metadata"
              @loadstart="handleVideoLoadStart"
              @loadeddata="handleVideoLoaded"
              @error="handleVideoError"
              @play="handleVideoPlay"
              @pause="handleVideoPause"
              @timeupdate="handleVideoTimeUpdate"
              @volumechange="handleVolumeChange"
              @click="toggleVideoPlay"
            >
              您的浏览器不支持视频播放
            </video>

            <!-- 自定义视频控制栏 -->
            <div class="video-controls" v-show="showControls">
              <div class="controls-left">
                <!-- 播放/暂停按钮 -->
                <a-button
                  type="link"
                  :icon="videoPlaying ? 'pause' : 'caret-right'"
                  @click="toggleVideoPlay"
                  class="control-btn play-btn"
                />

                <!-- 时间显示 -->
                <span class="time-display">
                  {{ formatTime(currentTime) }} / {{ formatTime(duration) }}
                </span>
              </div>

              <div class="controls-center">
                <!-- 进度条 -->
                <div class="progress-container" @click="handleProgressClick">
                  <div class="progress-bar">
                    <div
                      class="progress-filled"
                      :style="{ width: progressPercent + '%' }"
                    ></div>
                    <div
                      class="progress-thumb"
                      :style="{ left: progressPercent + '%' }"
                    ></div>
                  </div>
                </div>
              </div>

              <div class="controls-right">
                <!-- 音量控制 -->
                <div class="volume-control" @mouseenter="showVolumeSlider = true" @mouseleave="showVolumeSlider = false">
                  <a-button
                    type="link"
                    :icon="videoMuted ? 'sound' : 'sound-filled'"
                    @click="toggleMute"
                    class="control-btn volume-btn"
                  />
                  <div class="volume-slider" v-show="showVolumeSlider">
                    <a-slider
                      v-model="videoVolume"
                      :min="0"
                      :max="100"
                      :step="1"
                      vertical
                      :tip-formatter="null"
                      @change="handleVolumeSliderChange"
                      class="volume-range"
                    />
                  </div>
                </div>

                <!-- 全屏按钮 -->
                <a-button
                  type="link"
                  icon="fullscreen"
                  @click="toggleFullscreen"
                  class="control-btn fullscreen-btn"
                />
              </div>
            </div>

            <!-- 视频加载状态 -->
            <div class="video-loading" v-show="videoLoading">
              <a-spin size="large">
                <a-icon slot="indicator" type="loading" style="font-size: 24px;" spin />
              </a-spin>
              <p>视频加载中...</p>
            </div>
          </div>

          <!-- 视频加载失败占位符 -->
          <div class="video-error-placeholder" v-if="videoError">
            <div class="error-content">
              <a-icon type="exclamation-circle" class="error-icon" />
              <h4>视频加载失败</h4>
              <p>抱歉，演示视频暂时无法播放</p>
              <a-button @click="retryVideoLoad" type="primary" ghost>
                <a-icon type="reload" />
                重新加载
              </a-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 3. 工作流列表区域 -->
      <div class="workflow-section">
        <h3 class="section-title">
          工作流列表
          <span class="workflow-count">({{ workflowList.length }}个)</span>
        </h3>
        
        <div v-if="workflowLoading" class="workflow-loading">
          <a-spin tip="加载工作流中..." />
        </div>
        
        <div v-else-if="workflowList.length > 0" class="workflow-list">
          <div
            v-for="(workflow, index) in workflowList"
            :key="workflow.id"
            class="workflow-item"
          >
            <div class="workflow-info">
              <div class="workflow-sequence">{{ index + 1 }}</div>
              <div class="workflow-avatar">
                <img
                  v-if="workflow.agentAvatar || agentDetail.agentAvatar"
                  :src="workflow.agentAvatar || agentDetail.agentAvatar"
                  :alt="workflow.workflowName"
                  @error="handleWorkflowImageError"
                />
                <a-icon v-else type="setting" />
              </div>
              <div class="workflow-details">
                <h4 class="workflow-name">{{ workflow.workflowName }}</h4>
                <p class="workflow-description">{{ workflow.workflowDescription }}</p>

                <!-- 🔥 新增：输入参数说明 -->
                <div class="workflow-params">
                  <div class="params-label">
                    <a-icon type="setting" />
                    <span>输入参数说明</span>
                  </div>
                  <div class="params-content">
                    {{ workflow.inputParamsDesc || '暂无输入参数说明' }}
                  </div>
                </div>
              </div>
            </div>
            
            <!-- 下载按钮 -->
            <div class="workflow-actions">
                <!-- 未购买且非免费时显示购买提示 -->
                <a-button
                  v-if="!isPurchased && !agentDetail.isFree"
                  type="default"
                  disabled
                  @click="handleDownloadTip"
                >
                  <a-icon type="download" />
                  请先购买
                </a-button>
                <!-- 已购买或免费时可以下载 -->
                <a-button
                  v-else
                  type="primary"
                  @click="handleWorkflowDownload(workflow)"
                  :loading="downloadLoading[workflow.id]"
                >
                  <a-icon type="download" />
                  下载
                </a-button>
            </div>
          </div>
        </div>
        
        <div v-else class="workflow-empty">
          <a-empty description="暂无工作流" />
        </div>
      </div>

      <!-- 4. 底部操作按钮区域 -->
      <div class="action-buttons modern-actions">
        <a-button @click="handleClose" class="close-btn modern-btn-secondary">
          <a-icon type="close" />
          关闭
        </a-button>

        <div class="primary-actions">
            <!-- 查看详情按钮：已购买或免费时可点击 -->
            <a-button
              v-if="isPurchased || agentDetail.isFree"
              type="default"
              @click="handleViewDetail"
              class="detail-btn modern-btn-outline"
            >
              <a-icon type="eye" />
              查看详情
            </a-button>
            <a-button
              v-else
              type="default"
              disabled
              class="detail-btn modern-btn-outline disabled"
            >
              <a-icon type="eye" />
              查看详情
            </a-button>

            <!-- 购买按钮：只有在未购买且非免费时才显示 -->
            <a-button
              v-if="!isPurchased && !agentDetail.isFree"
              type="primary"
              @click="handlePurchase"
              :loading="purchaseLoading"
              class="purchase-btn modern-btn-primary"
            >
              <a-icon type="shopping-cart" />
              立即购买
            </a-button>

          <a-button
            v-if="agentDetail.experienceLink"
            type="default"
            @click="handleExperience"
            class="experience-btn modern-btn-outline"
          >
            <a-icon type="robot" />
            体验智能体
          </a-button>
          <a-button
            v-else
            type="default"
            disabled
            class="experience-btn modern-btn-outline disabled"
          >
            <a-icon type="robot" />
            暂无体验
          </a-button>
        </div>
      </div>
    </div>
  </a-modal>

  <!-- 支付方式选择弹窗 -->
  <a-modal
    v-model="showPaymentModal"
    title="选择支付方式"
    :width="520"
    :footer="null"
    :maskClosable="false"
    :centered="true"
    class="payment-modal"
  >
    <div class="payment-content">
      <!-- 订单信息卡片 -->
      <div class="order-info-card">
        <div class="order-header">
          <div class="order-icon">
            <a-icon type="shopping-cart" />
          </div>
          <div class="order-title">
            <h3>订单详情</h3>
            <p>请确认您的购买信息</p>
          </div>
        </div>
        <div class="order-details">
          <div class="order-item">
            <span class="label">智能体名称</span>
            <span class="value">{{ orderInfo && orderInfo.agentName }}</span>
          </div>
          <div class="order-item total">
            <span class="label">支付金额</span>
            <span class="price">¥{{ orderInfo && orderInfo.purchasePrice }}</span>
          </div>
        </div>
      </div>

      <!-- 支付方式选择 -->
      <div class="payment-methods">
        <div class="payment-header">
          <h3>选择支付方式</h3>
          <p>请选择您偏好的支付方式完成购买</p>
        </div>

        <!-- 余额支付 -->
        <div class="payment-option" @click="selectPaymentMethod('balance')" :class="{
          'insufficient': userBalance < (orderInfo && orderInfo.purchasePrice),
          'selected': selectedPaymentMethod === 'balance'
        }">
          <div class="payment-icon balance">
            <a-icon type="wallet" />
          </div>
          <div class="payment-info">
            <div class="payment-title">
              账户余额支付
              <span v-if="userBalance < (orderInfo && orderInfo.purchasePrice)" class="insufficient-tag">余额不足</span>
            </div>
            <div class="payment-desc">当前余额：¥{{ userBalance }}</div>
          </div>
          <div class="payment-status">
            <a-icon v-if="selectedPaymentMethod === 'balance'" type="check-circle" class="selected-icon" />
            <a-icon v-else-if="userBalance < (orderInfo && orderInfo.purchasePrice)" type="exclamation-circle" class="insufficient" />
            <a-icon v-else type="wallet" class="available" />
          </div>
        </div>

        <!-- 支付宝支付 -->
        <div class="payment-option" @click="selectPaymentMethod('alipay')" :class="{ 'selected': selectedPaymentMethod === 'alipay' }">
          <div class="payment-icon alipay">
            <a-icon type="alipay" />
          </div>
          <div class="payment-info">
            <div class="payment-title">
              支付宝支付
            </div>
            <div class="payment-desc">安全便捷的在线支付</div>
          </div>
          <div class="payment-status">
            <a-icon v-if="selectedPaymentMethod === 'alipay'" type="check-circle" class="selected-icon" />
            <a-icon v-else type="right" />
          </div>
        </div>
      </div>

      <div class="payment-actions">
        <a-button @click="showPaymentModal = false" class="cancel-btn">
          <a-icon type="close" />
          取消支付
        </a-button>

        <a-button
          type="primary"
          @click="confirmPayment"
          :disabled="!selectedPaymentMethod || (selectedPaymentMethod === 'balance' && userBalance < (orderInfo && orderInfo.purchasePrice))"
          :loading="paymentLoading"
          class="confirm-payment-btn"
        >
          <a-icon type="credit-card" />
          确认支付 ¥{{ orderInfo && orderInfo.purchasePrice }}
        </a-button>
      </div>
    </div>

    <div v-if="paymentLoading" class="payment-loading">
      <a-spin size="large" tip="处理支付中..." />
    </div>
  </a-modal>
  </div>
</template>

<script>
export default {
  name: 'AgentDetailModal',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    agentId: {
      type: String,
      default: ''
    },
    isPurchased: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      loading: false,
      workflowLoading: false,
      purchaseLoading: false,
      downloadLoading: {}, // 工作流下载loading状态
      agentDetail: {}, // 智能体详情
      workflowList: [], // 工作流列表

      // 视频播放相关状态
      videoLoading: false,
      videoError: false,
      videoPlaying: false,
      videoMuted: true, // 默认静音以支持自动播放
      videoAutoplay: true,
      currentTime: 0,
      duration: 0,
      videoVolume: 50,
      showControls: true,
      showVolumeSlider: false,
      progressPercent: 0,

      // 支付相关
      showPaymentModal: false,
      orderInfo: null,
      paymentLoading: false,
      userBalance: 0,
      selectedPaymentMethod: null // 选中的支付方式
    }
  },
  computed: {
    // 创作者名称
    creatorName() {
      if (this.agentDetail.creatorInfo) {
        // 优先显示昵称，其次显示姓名
        return this.agentDetail.creatorInfo.nickname || this.agentDetail.creatorInfo.name || '未知创作者'
      }
      return '未知创作者'
    },

    // 作者类型文本
    authorTypeText() {
      if (this.agentDetail.authorType === '1') {
        return '官方'
      } else if (this.agentDetail.authorType === '2') {
        return '创作者'
      }
      return '未知'
    },

    // 最终价格（考虑折扣）
    finalPrice() {
      if (!this.agentDetail) return 0
      if (this.agentDetail.isFree) return 0

      // 安全地获取价格，确保返回数字
      const originalPrice = parseFloat(this.agentDetail.price || this.agentDetail.originalPrice) || 0
      const discountPrice = parseFloat(this.agentDetail.discountPrice) || originalPrice

      return this.agentDetail.showDiscountPrice ? discountPrice : originalPrice
    }
  },
  watch: {
    visible(newVal) {
      if (newVal && this.agentId) {
        this.loadAgentDetail()
        this.loadWorkflowList()
      } else {
        this.resetData()
      }
    }
  },
  methods: {
    // 加载智能体详情
    async loadAgentDetail() {
      this.loading = true
      try {

        // 🔥 使用this.$http来自动携带token
        const response = await this.$http.get(`/api/agent/market/detail/${this.agentId}`)
        const data = response.data || response

        // 使用axios获取的数据
        if (data.success && data.result) {
          this.agentDetail = data.result
        } else {
          throw new Error(data.message || '获取智能体详情失败')
        }
      } catch (error) {
        console.error('加载智能体详情失败:', error)
        this.$message.error('加载智能体详情失败')

        // 失败时使用父组件传入的基础数据作为后备方案
        const baseAgent = this.$parent.selectedAgent || {}
        this.agentDetail = {
          id: this.agentId,
          agentName: baseAgent.agentName || '智能体',
          agentDescription: baseAgent.agentDescription || baseAgent.description || '暂无描述',
          agentAvatar: baseAgent.agentAvatar || '',
          demoVideo: baseAgent.demoVideo || '',
          experienceLink: baseAgent.experienceLink || '',
          price: baseAgent.price || 0,
          originalPrice: baseAgent.originalPrice || baseAgent.price || 0,
          authorType: baseAgent.authorType || '1',
          showSvipPromo: baseAgent.showSvipPromo || false,
          showDiscountPrice: baseAgent.showDiscountPrice || false,
          discountRate: baseAgent.discountRate || 1,
          isFree: baseAgent.isFree || false,
          creatorInfo: {
            name: '未知创作者',
            nickname: '未知创作者',
            avatar: ''
          }
        }
        console.log('⚠️ 使用后备数据:', this.agentDetail)
      } finally {
        this.loading = false
      }
    },

    // 加载工作流列表
    async loadWorkflowList() {
      this.workflowLoading = true
      try {
        console.log('🔍 开始加载工作流列表 - 智能体ID:', this.agentId)

        // 🔥 直接使用fetch来避免axios拦截器问题
        const response = await fetch(`/jeecg-boot/api/agent/market/${this.agentId}/workflows`)
        const data = await response.json()

        if (data.success && data.result) {
          this.workflowList = data.result
          console.log('✅ 工作流列表加载成功:', this.workflowList.length, '个工作流')
        } else {
          throw new Error(data.message || '获取工作流列表失败')
        }
      } catch (error) {
        console.error('❌ 加载工作流列表失败:', error)
        this.$message.error('加载工作流列表失败: ' + (error.message || error))

        // 失败时设置空列表
        this.workflowList = []
        console.log('⚠️ 工作流列表设置为空')
      } finally {
        this.workflowLoading = false
      }
    },

    // 重置数据
    resetData() {
      this.agentDetail = {}
      this.workflowList = []
      this.downloadLoading = {}

      // 重置视频状态
      this.videoLoading = false
      this.videoError = false
      this.videoPlaying = false
      this.currentTime = 0
      this.duration = 0
      this.progressPercent = 0
      this.showVolumeSlider = false

      // 停止视频播放
      if (this.$refs.demoVideo) {
        this.$refs.demoVideo.pause()
        this.$refs.demoVideo.currentTime = 0
      }
    },

    // 关闭弹窗
    handleClose() {
      this.$emit('close')
    },

    // 购买操作
    handlePurchase() {
      // 直接进入购买流程，跳过确认弹窗
      this.processPurchase()
    },

    // 处理购买流程
    async processPurchase() {
      this.purchaseLoading = true
      try {
        // 🛒 第一步：创建购买订单
        const purchaseData = {
          agentId: this.agentDetail.id,
          agentName: this.agentDetail.agentName,
          purchasePrice: this.finalPrice,
          originalPrice: this.agentDetail.price || this.agentDetail.originalPrice,
          discountRate: this.agentDetail.discountRate || 100
        }

        console.log('🛒 发送购买请求:', purchaseData)
        const response = await this.$http.post('/api/agent/market/purchase', purchaseData)
        console.log('🛒 购买响应:', response)

        if (response.success) {
          // 🛒 第二步：获取用户余额并显示支付方式选择弹窗
          await this.loadUserBalance()
          this.orderInfo = response.result
          this.selectedPaymentMethod = null // 重置选中的支付方式
          this.showPaymentModal = true
        } else {
          throw new Error(response.message || '创建订单失败')
        }

      } catch (error) {
        console.error('创建订单失败:', error)
        if (error.response && error.response.data && error.response.data.message) {
          this.$message.error(error.response.data.message)
        } else {
          this.$message.error('创建订单失败，请稍后重试')
        }
      } finally {
        this.purchaseLoading = false
      }
    },

    // 选择支付方式
    selectPaymentMethod(paymentMethod) {
      // 如果是余额支付且余额不足，不允许选择
      if (paymentMethod === 'balance' && this.userBalance < (this.orderInfo && this.orderInfo.purchasePrice)) {
        this.$message.warning('账户余额不足，请选择其他支付方式')
        return
      }

      this.selectedPaymentMethod = paymentMethod
      console.log('选择支付方式:', paymentMethod)
    },

    // 确认支付
    async confirmPayment() {
      if (!this.selectedPaymentMethod) {
        this.$message.warning('请选择支付方式')
        return
      }

      if (!this.orderInfo) {
        this.$message.error('订单信息错误')
        return
      }

      try {
        this.paymentLoading = true

        if (this.selectedPaymentMethod === 'balance') {
          await this.handleBalancePayment()
        } else if (this.selectedPaymentMethod === 'alipay') {
          await this.handleAlipayPayment()
        }
      } catch (error) {
        console.error('支付处理失败:', error)
        if (error.response && error.response.data && error.response.data.message) {
          this.$message.error(error.response.data.message)
        } else {
          this.$message.error('支付失败，请重试')
        }
      } finally {
        this.paymentLoading = false
      }
    },

    // 处理支付方式选择
    async handlePayment(paymentMethod) {
      if (!this.orderInfo) {
        this.$message.error('订单信息错误')
        return
      }

      try {
        this.paymentLoading = true

        if (paymentMethod === 'balance') {
          await this.handleBalancePayment()
        } else if (paymentMethod === 'alipay') {
          await this.handleAlipayPayment()
        }

      } catch (error) {
        console.error('支付处理失败:', error)
        if (error.response && error.response.data && error.response.data.message) {
          this.$message.error(error.response.data.message)
        } else {
          this.$message.error('支付失败，请重试')
        }
      } finally {
        this.paymentLoading = false
      }
    },

    // 余额支付
    async handleBalancePayment() {
      console.log('💰 开始余额支付')

      const paymentData = {
        purchaseId: this.orderInfo.purchaseId,
        agentId: this.orderInfo.agentId,
        agentName: this.orderInfo.agentName,
        purchasePrice: this.orderInfo.purchasePrice
      }

      const response = await this.$http.post('/api/agent/market/purchase/balance-pay', paymentData)

      if (response.success) {
        this.$message.success('余额支付成功！您现在可以使用该智能体了')

        // 通知父组件更新购买状态
        this.$emit('purchase-success', this.agentDetail.id)

        // 关闭弹窗
        this.showPaymentModal = false
        this.handleClose()
      } else {
        throw new Error(response.message || '余额支付失败')
      }
    },

    // 支付宝支付
    async handleAlipayPayment() {
      console.log('💳 开始支付宝支付')

      const paymentData = {
        purchaseId: this.orderInfo.purchaseId,
        agentId: this.orderInfo.agentId,
        agentName: this.orderInfo.agentName,
        purchasePrice: this.orderInfo.purchasePrice
      }

      const response = await this.$http.post('/api/agent/market/purchase/alipay', paymentData)

      if (response.success) {
        const payForm = response.result.payForm

        if (!payForm) {
          this.$message.error('支付表单为空')
          return
        }

        // 创建表单并提交到支付宝
        const div = document.createElement('div')
        div.innerHTML = payForm
        document.body.appendChild(div)

        const form = div.querySelector('form')
        if (form) {
          console.log('🔍 找到支付表单，准备提交')
          form.submit()

          // 关闭支付选择弹窗
          this.showPaymentModal = false
        } else {
          console.error('🔍 未找到支付表单')
          this.$message.error('支付表单创建失败')
        }

        // 清理DOM
        setTimeout(() => {
          if (document.body.contains(div)) {
            document.body.removeChild(div)
          }
        }, 1000)
      } else {
        throw new Error(response.message || '支付宝支付失败')
      }
    },

    // 查看详情
    handleViewDetail() {
      if (!this.isPurchased && !this.agentDetail.isFree) {
        this.$message.warning('请先购买该智能体后查看详情')
        return
      }

      // 🔥 跳转到智能体详细页面
      console.log('🔍 跳转到智能体详细页面, agentId:', this.agentId)

      // 先保存 agentId，避免弹窗关闭时被清空
      const agentId = this.agentId

      // 通过emit关闭弹窗，而不是直接修改prop
      this.$emit('update:visible', false)

      // 使用nextTick确保弹窗关闭后再跳转
      this.$nextTick(() => {
        this.$router.push({
          name: 'AgentDetailPage',
          query: {
            agentId: agentId
          }
        }).catch(err => {
          // 捕获路由导航错误，避免控制台报错
          if (err.name !== 'NavigationDuplicated') {
            console.error('路由跳转失败:', err)
          }
        })

        console.log('🔗 跳转到智能体详情页面')
      })
    },

    // 体验智能体
    handleExperience() {
      if (!this.agentDetail.experienceLink) {
        this.$message.warning('该智能体暂未提供体验链接')
        return
      }

      try {
        // 在新窗口打开体验链接
        window.open(this.agentDetail.experienceLink, '_blank')
        console.log('🔗 打开体验链接:', this.agentDetail.experienceLink)
        this.$message.success('正在打开体验页面...')
      } catch (error) {
        console.error('打开体验链接失败:', error)
        this.$message.error('打开体验页面失败')
      }
    },

    // 下载提示
    handleDownloadTip() {
      this.$message.warning('请先购买智能体后再下载工作流')
    },

    // 处理工作流下载
    async handleWorkflowDownload(workflow) {
      console.log('下载工作流:', workflow)

      // 检查购买状态
      if (!this.isPurchased && !this.agentDetail.isFree) {
        this.$message.warning('请先购买该智能体后再下载工作流')
        return
      }

      // 设置下载状态
      this.$set(this.downloadLoading, workflow.id, true)

      try {
        // 已购买用户 - 跳转到下载页面
        await this.navigateToDownloadPage(workflow)
      } catch (error) {
        console.error('下载工作流失败:', error)
        this.$message.error('下载失败，请稍后重试')
      } finally {
        this.$set(this.downloadLoading, workflow.id, false)
      }
    },

    // 跳转到工作流下载页面
    async navigateToDownloadPage(workflow) {
      try {
        // TODO: 实现跳转到工作流下载页面的逻辑
        // 这里应该跳转到一个专门的下载页面，而不是直接下载文件

        // 模拟跳转延迟
        await new Promise(resolve => setTimeout(resolve, 800))

        const downloadUrl = `/workflow/download/${workflow.id}?agentId=${this.agentDetail.id}`

        // 使用路由跳转
        this.$router.push({
          path: '/workflow/download',
          query: {
            workflowId: workflow.id,
            agentId: this.agentDetail.id,
            workflowName: workflow.workflowName
          }
        })

        console.log('🔗 跳转到工作流下载页面:', downloadUrl)
        this.$message.success('正在跳转到下载页面...')
      } catch (error) {
        console.error('跳转下载页面失败:', error)
        this.$message.error('跳转失败，请稍后重试')
      }
    },

    // ========== 视频播放控制方法 ==========

    // 视频开始加载
    handleVideoLoadStart() {
      console.log('🎬 视频开始加载')
      this.videoLoading = true
      this.videoError = false
    },

    // 视频加载完成
    handleVideoLoaded() {
      console.log('🎬 视频加载完成')
      this.videoLoading = false
      this.videoError = false

      const video = this.$refs.demoVideo
      if (video) {
        this.duration = video.duration || 0
        this.videoVolume = Math.round(video.volume * 100)

        // 尝试自动播放（静音状态下）
        if (this.videoAutoplay && this.videoMuted) {
          this.playVideo()
        }
      }
    },

    // 视频加载错误
    handleVideoError(event) {
      console.error('🎬 视频加载失败:', event)
      this.videoLoading = false
      this.videoError = true
      this.videoPlaying = false
      this.$message.error('视频加载失败')
    },

    // 视频播放事件
    handleVideoPlay() {
      console.log('🎬 视频开始播放')
      this.videoPlaying = true
    },

    // 视频暂停事件
    handleVideoPause() {
      console.log('🎬 视频暂停播放')
      this.videoPlaying = false
    },

    // 视频时间更新
    handleVideoTimeUpdate() {
      const video = this.$refs.demoVideo
      if (video && video.duration) {
        this.currentTime = video.currentTime
        this.progressPercent = (video.currentTime / video.duration) * 100
      }
    },

    // 音量变化事件
    handleVolumeChange() {
      const video = this.$refs.demoVideo
      if (video) {
        this.videoVolume = Math.round(video.volume * 100)
        this.videoMuted = video.muted
      }
    },

    // 切换播放/暂停
    toggleVideoPlay() {
      const video = this.$refs.demoVideo
      if (!video) return

      if (this.videoPlaying) {
        video.pause()
      } else {
        this.playVideo()
      }
    },

    // 播放视频
    async playVideo() {
      const video = this.$refs.demoVideo
      if (!video) return

      try {
        await video.play()
        console.log('🎬 视频播放成功')
      } catch (error) {
        console.error('🎬 视频播放失败:', error)
        // 如果自动播放失败，可能是因为浏览器策略，尝试静音播放
        if (!this.videoMuted) {
          this.videoMuted = true
          video.muted = true
          try {
            await video.play()
            this.$message.info('视频已静音播放，点击音量按钮开启声音')
          } catch (retryError) {
            console.error('🎬 静音播放也失败:', retryError)
            this.$message.warning('视频播放失败，请手动点击播放')
          }
        }
      }
    },

    // 切换静音
    toggleMute() {
      const video = this.$refs.demoVideo
      if (!video) return

      this.videoMuted = !this.videoMuted
      video.muted = this.videoMuted

      console.log('🎬 切换静音状态:', this.videoMuted ? '静音' : '有声')
    },

    // 处理进度条点击
    handleProgressClick(event) {
      const video = this.$refs.demoVideo
      if (!video || !video.duration) return

      const progressContainer = event.currentTarget
      const rect = progressContainer.getBoundingClientRect()
      const clickX = event.clientX - rect.left
      const progressWidth = rect.width
      const clickPercent = clickX / progressWidth

      const newTime = clickPercent * video.duration
      video.currentTime = newTime

      console.log('🎬 跳转到时间:', this.formatTime(newTime))
    },

    // 音量滑块变化
    handleVolumeSliderChange(value) {
      const video = this.$refs.demoVideo
      if (!video) return

      this.videoVolume = value
      video.volume = value / 100

      // 如果音量大于0，自动取消静音
      if (value > 0 && this.videoMuted) {
        this.videoMuted = false
        video.muted = false
      }

      console.log('🎬 调整音量:', value + '%')
    },

    // 切换全屏
    toggleFullscreen() {
      const video = this.$refs.demoVideo
      if (!video) return

      if (video.requestFullscreen) {
        video.requestFullscreen()
      } else if (video.webkitRequestFullscreen) {
        video.webkitRequestFullscreen()
      } else if (video.mozRequestFullScreen) {
        video.mozRequestFullScreen()
      } else if (video.msRequestFullscreen) {
        video.msRequestFullscreen()
      }

      console.log('🎬 切换全屏模式')
    },

    // 重新加载视频
    retryVideoLoad() {
      console.log('🎬 重新加载视频')
      this.videoError = false
      this.videoLoading = true

      const video = this.$refs.demoVideo
      if (video) {
        video.load()
      }
    },

    // 格式化时间显示
    formatTime(seconds) {
      if (!seconds || isNaN(seconds)) return '00:00'

      const minutes = Math.floor(seconds / 60)
      const remainingSeconds = Math.floor(seconds % 60)

      return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`
    },

    // 图片加载错误处理
    handleImageError(event) {
      event.target.style.display = 'none'
    },

    // 创作者头像加载错误处理
    handleCreatorAvatarError(event) {
      event.target.style.display = 'none'
    },

    // 工作流图片加载错误处理
    handleWorkflowImageError(event) {
      event.target.style.display = 'none'
    },

    // 加载用户余额
    async loadUserBalance() {
      try {
        const response = await this.$http.get('/api/usercenter/overview')
        if (response.success) {
          this.userBalance = response.result.accountBalance || 0
        }
      } catch (error) {
        console.error('获取用户余额失败:', error)
        this.userBalance = 0
      }
    }
  }
}
</script>

<style scoped>
/* 弹窗样式 */
.agent-detail-modal {
  top: 20px;
}

.agent-detail-modal .ant-modal-body {
  max-height: 80vh;
  overflow-y: auto;
  padding: 0;
}

/* 自定义滚动条 */
.agent-detail-modal .ant-modal-body::-webkit-scrollbar {
  width: 6px;
}

.agent-detail-modal .ant-modal-body::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.agent-detail-modal .ant-modal-body::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.agent-detail-modal .ant-modal-body::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 弹窗动画优化 */
.agent-detail-modal .ant-modal {
  animation: modalFadeIn 0.3s ease-out;
}

@keyframes modalFadeIn {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* 加载状态 */
.loading-container {
  padding: 60px 0;
  text-align: center;
}

.loading-placeholder {
  height: 200px;
}

/* 主要内容 */
.modal-content {
  padding: 0;
}

/* 1. 智能体基本信息区域 */
.agent-info-section {
  padding: 32px;
  border-bottom: 1px solid rgba(226, 232, 240, 0.6);
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  color: #1a202c;
  position: relative;
  overflow: hidden;
}

.agent-header {
  display: flex;
  align-items: flex-start;
  gap: 24px;
  position: relative;
  z-index: 1;
  padding-right: 60px; /* 为关闭按钮留出空间 */
}

.agent-info-and-price {
  display: flex;
  flex: 1;
  justify-content: space-between;
  align-items: flex-start;
  gap: 24px;
}

.agent-avatar {
  width: 96px;
  height: 96px;
  border-radius: 20px;
  overflow: hidden;
  background: rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  flex-shrink: 0;
  border: 3px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
}

.agent-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f5f5;
  color: #999;
  font-size: 32px;
}

.agent-basic-info {
  flex: 1;
  min-width: 0;
}

.agent-name {
  margin: 0 0 12px 0;
  font-size: 28px;
  font-weight: 700;
  color: #1a202c;
  line-height: 1.2;
  text-shadow: none;
}

.agent-description {
  margin: 0 0 20px 0;
  color: #4a5568;
  font-size: 16px;
  line-height: 1.6;
  text-shadow: none;
}

.creator-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.creator-avatar {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  overflow: hidden;
  background: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
}

.creator-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.creator-avatar .anticon {
  font-size: 12px;
  color: #999;
}

.creator-details {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.creator-name-line {
  display: flex;
  align-items: center;
  gap: 8px;
}

.creator-name {
  font-size: 15px;
  font-weight: 600;
  color: #2d3748;
  text-shadow: none;
}

.creator-type {
  font-size: 11px;
  color: #6b7280;
  background: #f3f4f6;
  padding: 2px 6px;
  border-radius: 8px;
  display: inline-block;
  font-weight: 500;
  border: 1px solid #e5e7eb;
}

.price-section {
  flex-shrink: 0;
  text-align: right;
}

.price-container {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
}

.free-price {
  font-size: 20px;
  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 600;
  text-shadow: 0 2px 4px rgba(139, 92, 246, 0.2);
}

.discount-price {
  font-size: 20px;
  font-weight: 600;
  color: #f59e0b;
}

.original-price {
  font-size: 14px;
  color: #9ca3af;
  text-decoration: line-through;
}

.current-price {
  font-size: 20px;
  font-weight: 600;
  color: #059669;
}

.purchased-price {
  font-size: 20px;
  font-weight: 600;
  color: #10b981;
}

/* 2. 演示视频区域 */
.demo-video-section {
  padding: 24px;
  border-bottom: 1px solid #f0f0f0;
}

.section-title {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #2d3748;
  display: flex;
  align-items: center;
  gap: 8px;
}

.section-title .anticon {
  color: #4299e1;
}

.video-container {
  border-radius: 12px;
  overflow: hidden;
  background: #000;
  position: relative;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.video-wrapper {
  position: relative;
  width: 100%;
  height: 300px;
}

.demo-video {
  width: 100%;
  height: 100%;
  object-fit: cover;
  cursor: pointer;
  transition: transform 0.2s ease;
}

.demo-video:hover {
  transform: scale(1.02);
}

/* 视频控制栏 */
.video-controls {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
  padding: 16px;
  display: flex;
  align-items: center;
  gap: 12px;
  transition: opacity 0.3s ease;
}

.controls-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.controls-center {
  flex: 1;
  margin: 0 16px;
}

.controls-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

.control-btn {
  color: white !important;
  border: none !important;
  background: transparent !important;
  padding: 4px 8px !important;
  height: auto !important;
  font-size: 16px;
  transition: all 0.2s ease;
}

.control-btn:hover {
  color: #4299e1 !important;
  background: rgba(255, 255, 255, 0.1) !important;
  transform: scale(1.1);
}

.time-display {
  color: white;
  font-size: 12px;
  font-family: monospace;
  min-width: 80px;
}

/* 进度条 */
.progress-container {
  cursor: pointer;
  padding: 8px 0;
}

.progress-bar {
  height: 4px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
  position: relative;
  overflow: hidden;
}

.progress-filled {
  height: 100%;
  background: linear-gradient(90deg, #4299e1, #3182ce);
  border-radius: 2px;
  transition: width 0.1s ease;
}

.progress-thumb {
  position: absolute;
  top: 50%;
  width: 12px;
  height: 12px;
  background: white;
  border-radius: 50%;
  transform: translate(-50%, -50%);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  transition: left 0.1s ease;
}

/* 音量控制 */
.volume-control {
  position: relative;
}

.volume-slider {
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  padding: 12px 8px;
  border-radius: 6px;
  margin-bottom: 8px;
  height: 100px;
}

.volume-range {
  height: 80px;
}

.volume-range .ant-slider-rail {
  background: rgba(255, 255, 255, 0.3);
}

.volume-range .ant-slider-track {
  background: #4299e1;
}

.volume-range .ant-slider-handle {
  border-color: #4299e1;
}

/* 视频加载状态 */
.video-loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: white;
  z-index: 10;
}

.video-loading p {
  margin-top: 16px;
  color: white;
  font-size: 14px;
}

/* 视频错误占位符 */
.video-error-placeholder {
  width: 100%;
  height: 300px;
  background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px dashed #cbd5e0;
}

.error-content {
  text-align: center;
  color: #718096;
}

.error-icon {
  font-size: 48px;
  color: #f56565;
  margin-bottom: 16px;
}

.error-content h4 {
  margin: 0 0 8px 0;
  color: #2d3748;
  font-size: 18px;
  font-weight: 600;
}

.error-content p {
  margin: 0 0 16px 0;
  color: #718096;
  font-size: 14px;
}

/* 3. 工作流列表区域 */
.workflow-section {
  padding: 24px;
  border-bottom: 1px solid #f0f0f0;
}

.workflow-count {
  font-size: 14px;
  color: #718096;
  font-weight: normal;
}

.workflow-loading {
  padding: 40px 0;
  text-align: center;
}

.workflow-list {
  margin-top: 16px;
}

.workflow-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  margin-bottom: 12px;
  background: #fff;
  transition: all 0.2s ease;
}

.workflow-item:hover {
  border-color: #cbd5e0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.workflow-item:last-child {
  margin-bottom: 0;
}

.workflow-info {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
  min-width: 0;
}

.workflow-sequence {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: #4299e1;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
  flex-shrink: 0;
}

.workflow-avatar {
  width: 40px;
  height: 40px;
  border-radius: 6px;
  overflow: hidden;
  background: #f7fafc;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.workflow-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.workflow-avatar .anticon {
  font-size: 16px;
  color: #a0aec0;
}

.workflow-details {
  flex: 1;
  min-width: 0;
}

.workflow-name {
  margin: 0 0 4px 0;
  font-size: 14px;
  font-weight: 500;
  color: #2d3748;
  line-height: 1.2;
}

.workflow-description {
  margin: 0 0 8px 0;
  font-size: 12px;
  color: #718096;
  line-height: 1.3;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 🔥 新增：输入参数说明样式 */
.workflow-params .params-label {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 11px;
  font-weight: 600;
  color: #1890ff;
  margin-bottom: 4px;
}

.workflow-params .params-label .anticon {
  font-size: 10px;
  color: #1890ff;
}

.workflow-params .params-content {
  font-size: 11px;
  color: #262626;
  line-height: 1.3;
  background: linear-gradient(135deg, #e6f7ff 0%, #f0f9ff 100%);
  padding: 8px 10px;
  border-radius: 6px;
  border: 1px solid #91d5ff;
  border-left: 3px solid #1890ff;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-wrap: break-word;
  font-weight: 500;
  box-shadow: 0 1px 3px rgba(24, 144, 255, 0.08);
}

.workflow-actions {
  flex-shrink: 0;
  margin-left: 16px;
}

.workflow-empty {
  padding: 40px 0;
  text-align: center;
}

/* 4. 底部操作按钮区域 */
.action-buttons {
  padding: 20px 24px;
  background: #f8fafc;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  border-top: 1px solid #e2e8f0;
  position: sticky;
  bottom: 0;
  z-index: 10;
  backdrop-filter: blur(10px);
}

.action-buttons .ant-btn {
  min-width: 80px;
}

.close-btn {
  margin-right: auto;
}

.purchase-btn {
  background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
  border-color: #4299e1;
  color: white;
  font-weight: 600;
  box-shadow: 0 2px 8px rgba(66, 153, 225, 0.3);
  transition: all 0.3s ease;
}

.purchase-btn:hover {
  background: linear-gradient(135deg, #3182ce 0%, #2c5aa0 100%);
  border-color: #3182ce;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(66, 153, 225, 0.4);
}

.disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .agent-detail-modal {
    top: 10px;
    margin: 0 10px;
  }

  .agent-detail-modal .ant-modal {
    max-width: none;
    width: calc(100vw - 20px) !important;
  }

  .agent-header {
    flex-direction: column;
    gap: 16px;
  }

  .agent-avatar {
    align-self: center;
  }

  .price-section {
    text-align: center;
  }

  .workflow-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .workflow-actions {
    margin-left: 0;
    align-self: stretch;
  }

  .workflow-actions .ant-btn {
    width: 100%;
  }

  .action-buttons {
    flex-direction: column;
  }

  .close-btn {
    margin-right: 0;
    order: 1;
  }
}

/* ===== 现代化弹窗样式美化 ===== */

/* 自定义弹窗样式 */
.custom-modal :deep(.ant-modal) {
  border-radius: 16px !important;
  overflow: hidden !important;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25) !important;
}

.custom-modal :deep(.ant-modal-content) {
  border-radius: 16px !important;
  overflow: hidden !important;
  background: #ffffff !important;
}

.custom-modal :deep(.ant-modal-body) {
  padding: 0 !important;
  border-radius: 16px !important;
  overflow: hidden !important;
}

/* 确保弹窗容器也有圆角 */
.agent-detail-modal :deep(.ant-modal) {
  border-radius: 16px !important;
  overflow: hidden !important;
}

.agent-detail-modal :deep(.ant-modal-content) {
  border-radius: 16px !important;
  overflow: hidden !important;
}

.agent-detail-modal :deep(.ant-modal-body) {
  border-radius: 16px !important;
  overflow: hidden !important;
}

/* 自定义关闭按钮 */
.custom-close-button {
  position: absolute;
  top: 20px;
  right: 20px;
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 1000;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.custom-close-button:hover {
  background: rgba(255, 255, 255, 1);
  transform: scale(1.1);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.custom-close-button .anticon {
  font-size: 18px;
  color: #64748b;
}

/* 背景装饰 */
.modal-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 0;
}

.bg-pattern {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 200px;
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
  opacity: 0.6;
}

.bg-gradient {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100px;
  background: linear-gradient(to top, rgba(148, 163, 184, 0.08) 0%, transparent 100%);
}

/* 现代化按钮样式 */
.modern-actions {
  background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
  border-top: 1px solid rgba(226, 232, 240, 0.6);
  backdrop-filter: blur(20px);
  padding: 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 16px;
}

.primary-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.modern-btn-primary {
  background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%) !important;
  border: none !important;
  color: white !important;
  font-weight: 600 !important;
  padding: 8px 24px !important;
  height: 44px !important;
  border-radius: 12px !important;
  box-shadow: 0 4px 14px rgba(79, 70, 229, 0.3) !important;
  transition: all 0.3s ease !important;
  display: flex !important;
  align-items: center !important;
  gap: 8px !important;
}

.modern-btn-primary:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 8px 25px rgba(79, 70, 229, 0.4) !important;
  background: linear-gradient(135deg, #5b52f0 0%, #8b5cf6 100%) !important;
}

.modern-btn-outline {
  background: rgba(255, 255, 255, 0.8) !important;
  border: 2px solid rgba(79, 70, 229, 0.2) !important;
  color: #4f46e5 !important;
  font-weight: 500 !important;
  padding: 8px 20px !important;
  height: 44px !important;
  border-radius: 12px !important;
  backdrop-filter: blur(10px) !important;
  transition: all 0.3s ease !important;
  display: flex !important;
  align-items: center !important;
  gap: 8px !important;
}

.modern-btn-outline:hover {
  background: rgba(79, 70, 229, 0.05) !important;
  border-color: rgba(79, 70, 229, 0.4) !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 12px rgba(79, 70, 229, 0.15) !important;
}

.modern-btn-secondary {
  background: rgba(100, 116, 139, 0.1) !important;
  border: 2px solid rgba(100, 116, 139, 0.2) !important;
  color: #64748b !important;
  font-weight: 500 !important;
  padding: 8px 20px !important;
  height: 44px !important;
  border-radius: 12px !important;
  backdrop-filter: blur(10px) !important;
  transition: all 0.3s ease !important;
  display: flex !important;
  align-items: center !important;
  gap: 8px !important;
}

.modern-btn-secondary:hover {
  background: rgba(100, 116, 139, 0.15) !important;
  border-color: rgba(100, 116, 139, 0.3) !important;
  color: #475569 !important;
}

.modern-btn-outline.disabled,
.modern-btn-secondary.disabled {
  opacity: 0.5 !important;
  cursor: not-allowed !important;
  transform: none !important;
}

.modern-btn-outline.disabled:hover,
.modern-btn-secondary.disabled:hover {
  transform: none !important;
  box-shadow: none !important;
}

/* 支付弹窗样式 */
.payment-modal :deep(.ant-modal-content) {
  border-radius: 16px;
  overflow: hidden;
}

.payment-content {
  padding: 20px 0;
}

.order-info {
  margin-bottom: 24px;
  padding: 16px;
  background: #f8fafc;
  border-radius: 12px;
}

.order-info h3 {
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
}

.order-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.order-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
}

.order-item .price {
  font-size: 18px;
  font-weight: 600;
  color: #dc2626;
}

/* 订单信息卡片样式 */
.order-info-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 24px;
  color: white;
  position: relative;
  overflow: hidden;
}

.order-info-card::before {
  content: '';
  position: absolute;
  top: -50%;
  right: -50%;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  transform: rotate(45deg);
}

.order-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  position: relative;
  z-index: 1;
}

.order-icon {
  width: 48px;
  height: 48px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
}

.order-icon .anticon {
  font-size: 24px;
  color: white;
}

.order-title h3 {
  margin: 0;
  font-size: 20px;
  font-weight: 700;
  color: white;
}

.order-title p {
  margin: 4px 0 0 0;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
}

.order-details {
  position: relative;
  z-index: 1;
}

.order-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.order-item:last-child {
  border-bottom: none;
}

.order-item.total {
  padding-top: 16px;
  margin-top: 8px;
  border-top: 2px solid rgba(255, 255, 255, 0.3);
}

.order-item .label {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.9);
}

.order-item .value {
  font-size: 16px;
  font-weight: 600;
  color: white;
}

.order-item .price {
  font-size: 24px;
  font-weight: 700;
  color: #fbbf24;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* 支付方式样式 */
.payment-header {
  margin-bottom: 20px;
  text-align: center;
}

.payment-header h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 700;
  color: #1e293b;
}

.payment-header p {
  margin: 0;
  font-size: 14px;
  color: #64748b;
}

.payment-option {
  display: flex;
  align-items: center;
  padding: 20px;
  border: 2px solid #e2e8f0;
  border-radius: 16px;
  margin-bottom: 16px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: white;
  position: relative;
  overflow: hidden;
}

.payment-option::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(79, 70, 229, 0.1), transparent);
  transition: left 0.5s ease;
}

.payment-option:hover::before {
  left: 100%;
}

.payment-option:hover {
  border-color: #4f46e5;
  background: #f8fafc;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(79, 70, 229, 0.15);
}

.payment-option.insufficient {
  border-color: #ef4444;
  background: #fef2f2;
  cursor: not-allowed;
  opacity: 0.7;
}

.payment-option.selected {
  border-color: #4f46e5;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  box-shadow: 0 8px 25px rgba(79, 70, 229, 0.2);
  transform: translateY(-1px);
}

.payment-option.selected::before {
  left: 100%;
}

.payment-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  position: relative;
  z-index: 1;
}

.payment-icon.balance {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.payment-icon.alipay {
  background: linear-gradient(135deg, #1677ff 0%, #0958d9 100%);
}

.payment-icon .anticon {
  font-size: 24px;
  color: white;
}

.payment-info {
  flex: 1;
  position: relative;
  z-index: 1;
}

.payment-title {
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 6px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.payment-desc {
  font-size: 14px;
  color: #64748b;
}

.payment-status {
  color: #94a3b8;
  position: relative;
  z-index: 1;
}

.payment-status .anticon {
  font-size: 20px;
}

.payment-status .available {
  color: #10b981;
}

.payment-status .insufficient {
  color: #ef4444;
}

.payment-status .selected-icon {
  color: #4f46e5;
  font-size: 22px;
}

.insufficient-tag {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: white;
  font-size: 12px;
  font-weight: 600;
  padding: 2px 8px;
  border-radius: 12px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* 支付弹窗样式 */
.payment-modal .ant-modal-content {
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
}

.payment-modal .ant-modal-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-bottom: none;
  padding: 24px 32px;
}

.payment-modal .ant-modal-title {
  color: white;
  font-size: 20px;
  font-weight: 700;
  text-align: center;
}

.payment-modal .ant-modal-close {
  color: white;
  opacity: 0.8;
}

.payment-modal .ant-modal-close:hover {
  opacity: 1;
}

.payment-modal .ant-modal-body {
  padding: 32px;
  background: #fafafa;
}

.payment-content {
  position: relative;
}

.payment-actions {
  margin-top: 32px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 24px;
  border-top: 1px solid #e2e8f0;
  gap: 16px;
}

.cancel-btn {
  background: white;
  border: 2px solid #e2e8f0;
  color: #64748b;
  font-weight: 600;
  height: 48px;
  padding: 0 32px;
  border-radius: 12px;
  transition: all 0.3s ease;
}

.cancel-btn:hover {
  border-color: #94a3b8;
  color: #475569;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.cancel-btn .anticon {
  margin-right: 8px;
}

.confirm-payment-btn {
  background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
  border-color: #4f46e5;
  color: white;
  font-weight: 600;
  height: 48px;
  padding: 0 32px;
  border-radius: 12px;
  transition: all 0.3s ease;
  flex: 1;
  max-width: 280px;
}

.confirm-payment-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #4338ca 0%, #6d28d9 100%);
  border-color: #4338ca;
  transform: translateY(-1px);
  box-shadow: 0 8px 25px rgba(79, 70, 229, 0.4);
}

.confirm-payment-btn:disabled {
  background: #e2e8f0;
  border-color: #e2e8f0;
  color: #94a3b8;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.confirm-payment-btn .anticon {
  margin-right: 8px;
}

.payment-loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.95);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  border-radius: 16px;
}

.payment-loading .ant-spin-text {
  color: #4f46e5;
  font-weight: 600;
  margin-top: 16px;
}
</style>
