package org.jeecg.modules.demo.apiusage.mapper;

import java.util.List;
import java.util.Map;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.jeecg.modules.demo.apiusage.entity.AicgUserApiUsage;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;

/**
 * @Description: 用户API使用记录表
 * @Author: jeecg-boot
 * @Date:   2025-06-18
 * @Version: V1.0
 */
public interface AicgUserApiUsageMapper extends BaseMapper<AicgUserApiUsage> {

    /**
     * 根据用户ID查询API使用记录
     * @param userId 用户ID
     * @return API使用记录列表
     */
    @Select("SELECT * FROM aicg_user_api_usage WHERE user_id = #{userId} ORDER BY call_time DESC")
    List<AicgUserApiUsage> getByUserId(@Param("userId") String userId);
    
    /**
     * 根据API密钥查询使用记录
     * @param apiKey API密钥
     * @return API使用记录列表
     */
    @Select("SELECT * FROM aicg_user_api_usage WHERE api_key = #{apiKey} ORDER BY call_time DESC")
    List<AicgUserApiUsage> getByApiKey(@Param("apiKey") String apiKey);
    
    /**
     * 统计用户API使用数据
     * @param userId 用户ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return API使用统计数据
     */
    @Select("SELECT " +
            "COUNT(*) as total_calls, " +
            "COUNT(CASE WHEN response_status = 200 THEN 1 END) as success_calls, " +
            "COUNT(CASE WHEN response_status != 200 THEN 1 END) as error_calls, " +
            "COALESCE(AVG(response_time), 0) as avg_response_time, " +
            "COALESCE(SUM(tokens_used), 0) as total_tokens, " +
            "COALESCE(SUM(cost_amount), 0) as total_cost " +
            "FROM aicg_user_api_usage " +
            "WHERE user_id = #{userId} AND call_time BETWEEN #{startTime} AND #{endTime}")
    Map<String, Object> getUsageStats(@Param("userId") String userId, 
                                     @Param("startTime") String startTime, 
                                     @Param("endTime") String endTime);
    
    /**
     * 统计API接口调用次数
     * @param userId 用户ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return API接口调用统计
     */
    @Select("SELECT api_endpoint, COUNT(*) as call_count, " +
            "COUNT(CASE WHEN response_status = 200 THEN 1 END) as success_count, " +
            "COALESCE(AVG(response_time), 0) as avg_response_time " +
            "FROM aicg_user_api_usage " +
            "WHERE user_id = #{userId} AND call_time BETWEEN #{startTime} AND #{endTime} " +
            "GROUP BY api_endpoint ORDER BY call_count DESC")
    List<Map<String, Object>> getEndpointStats(@Param("userId") String userId, 
                                              @Param("startTime") String startTime, 
                                              @Param("endTime") String endTime);
    
    /**
     * 查询今日API调用记录
     * @param userId 用户ID
     * @return 今日API调用记录
     */
    @Select("SELECT * FROM aicg_user_api_usage " +
            "WHERE user_id = #{userId} AND DATE(call_time) = CURDATE() " +
            "ORDER BY call_time DESC")
    List<AicgUserApiUsage> getTodayUsage(@Param("userId") String userId);
    
    /**
     * 查询错误的API调用记录
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 错误的API调用记录
     */
    @Select("SELECT * FROM aicg_user_api_usage " +
            "WHERE user_id = #{userId} AND response_status != 200 " +
            "ORDER BY call_time DESC LIMIT #{limit}")
    List<AicgUserApiUsage> getErrorUsage(@Param("userId") String userId, @Param("limit") Integer limit);

    /**
     * 获取热门插件排行
     * @param limit 限制数量
     * @return 热门插件列表
     */
    @Select("SELECT plugin_name as pluginName, COUNT(*) as callCount " +
            "FROM aicg_user_api_usage " +
            "WHERE plugin_key IS NOT NULL AND plugin_name IS NOT NULL " +
            "GROUP BY plugin_name " +
            "ORDER BY callCount DESC " +
            "LIMIT #{limit}")
    List<Map<String, Object>> getHotPlugins(@Param("limit") Integer limit);

    /**
     * 获取活跃用户排行
     * @param limit 限制数量
     * @return 活跃用户列表
     */
    @Select("SELECT user_id as userId, COUNT(*) as callCount " +
            "FROM aicg_user_api_usage " +
            "WHERE plugin_key IS NOT NULL " +
            "GROUP BY user_id " +
            "ORDER BY callCount DESC " +
            "LIMIT #{limit}")
    List<Map<String, Object>> getActiveUsers(@Param("limit") Integer limit);

    /**
     * 查询插件使用记录（包含用户昵称）
     * @param page 分页参数
     * @param aicgUserApiUsage 查询条件
     * @param hasPluginInfo 是否只查询有插件信息的记录
     * @param callTimeStart 开始时间
     * @param callTimeEnd 结束时间
     * @param userNickname 用户昵称
     * @param parameterMap 其他查询参数
     * @return 分页结果
     */
    @Select("<script>" +
            "SELECT " +
            "    u.id, u.user_id, u.api_key, u.api_endpoint, u.api_method, " +
            "    u.request_params, u.response_status, u.response_time, " +
            "    u.tokens_used, u.cost_amount, u.ip_address, u.user_agent, " +
            "    u.error_message, u.call_time, u.create_time, " +
            "    u.plugin_id, u.plugin_key, u.plugin_name, " +
            "    p.nickname as userNickname " +
            "FROM aicg_user_api_usage u " +
            "LEFT JOIN aicg_user_profile p ON u.user_id COLLATE utf8mb4_general_ci = p.user_id COLLATE utf8mb4_general_ci " +
            "WHERE 1=1 " +
            "<if test='hasPluginInfo != null and hasPluginInfo == true'>" +
            "    AND u.plugin_key IS NOT NULL " +
            "</if>" +
            "<if test='aicgUserApiUsage.userId != null and aicgUserApiUsage.userId != &quot;&quot;'>" +
            "    AND u.user_id LIKE CONCAT('%', #{aicgUserApiUsage.userId}, '%') " +
            "</if>" +
            "<if test='aicgUserApiUsage.pluginName != null and aicgUserApiUsage.pluginName != &quot;&quot;'>" +
            "    AND u.plugin_name LIKE CONCAT('%', #{aicgUserApiUsage.pluginName}, '%') " +
            "</if>" +
            "<if test='aicgUserApiUsage.pluginKey != null and aicgUserApiUsage.pluginKey != &quot;&quot;'>" +
            "    AND u.plugin_key LIKE CONCAT('%', #{aicgUserApiUsage.pluginKey}, '%') " +
            "</if>" +
            "<if test='aicgUserApiUsage.responseStatus != null'>" +
            "    AND u.response_status = #{aicgUserApiUsage.responseStatus} " +
            "</if>" +
            "<if test='callTimeStart != null and callTimeStart != &quot;&quot;'>" +
            "    AND u.call_time &gt;= CONCAT(#{callTimeStart}, ' 00:00:00') " +
            "</if>" +
            "<if test='callTimeEnd != null and callTimeEnd != &quot;&quot;'>" +
            "    AND u.call_time &lt;= CONCAT(#{callTimeEnd}, ' 23:59:59') " +
            "</if>" +
            "<if test='userNickname != null and userNickname != &quot;&quot;'>" +
            "    AND p.nickname LIKE CONCAT('%', #{userNickname}, '%') " +
            "</if>" +
            "ORDER BY u.call_time DESC " +
            "</script>")
    IPage<Map<String, Object>> queryPluginUsageWithUserInfo(IPage<Map<String, Object>> page,
                                                           @Param("aicgUserApiUsage") AicgUserApiUsage aicgUserApiUsage,
                                                           @Param("hasPluginInfo") Boolean hasPluginInfo,
                                                           @Param("callTimeStart") String callTimeStart,
                                                           @Param("callTimeEnd") String callTimeEnd,
                                                           @Param("userNickname") String userNickname,
                                                           @Param("parameterMap") Map<String, String[]> parameterMap);

    /**
     * 查询API使用记录（包含用户昵称）- 通用版本
     * @param page 分页参数
     * @param aicgUserApiUsage 查询条件
     * @param userNickname 用户昵称
     * @param parameterMap 其他查询参数
     * @return 分页结果
     */
    @Select("<script>" +
            "SELECT " +
            "    u.id, u.user_id, u.api_key, u.api_endpoint, u.api_method, " +
            "    u.request_params, u.response_status, u.response_time, " +
            "    u.tokens_used, u.cost_amount, u.ip_address, u.user_agent, " +
            "    u.error_message, u.call_time, u.create_time, " +
            "    u.plugin_id, u.plugin_key, u.plugin_name, " +
            "    p.nickname as userNickname " +
            "FROM aicg_user_api_usage u " +
            "LEFT JOIN aicg_user_profile p ON u.user_id COLLATE utf8mb4_general_ci = p.user_id COLLATE utf8mb4_general_ci " +
            "WHERE 1=1 " +
            "<if test='aicgUserApiUsage.userId != null and aicgUserApiUsage.userId != &quot;&quot;'>" +
            "    AND u.user_id LIKE CONCAT('%', #{aicgUserApiUsage.userId}, '%') " +
            "</if>" +
            "<if test='aicgUserApiUsage.apiEndpoint != null and aicgUserApiUsage.apiEndpoint != &quot;&quot;'>" +
            "    AND u.api_endpoint LIKE CONCAT('%', #{aicgUserApiUsage.apiEndpoint}, '%') " +
            "</if>" +
            "<if test='aicgUserApiUsage.apiMethod != null and aicgUserApiUsage.apiMethod != &quot;&quot;'>" +
            "    AND u.api_method = #{aicgUserApiUsage.apiMethod} " +
            "</if>" +
            "<if test='aicgUserApiUsage.responseStatus != null'>" +
            "    AND u.response_status = #{aicgUserApiUsage.responseStatus} " +
            "</if>" +
            "<if test='userNickname != null and userNickname != &quot;&quot;'>" +
            "    AND p.nickname LIKE CONCAT('%', #{userNickname}, '%') " +
            "</if>" +
            "ORDER BY u.call_time DESC " +
            "</script>")
    IPage<Map<String, Object>> queryApiUsageWithUserInfo(IPage<Map<String, Object>> page,
                                                        @Param("aicgUserApiUsage") AicgUserApiUsage aicgUserApiUsage,
                                                        @Param("userNickname") String userNickname,
                                                        @Param("parameterMap") Map<String, String[]> parameterMap);
}
