package org.jeecg.modules.demo.exchangecode.entity;

import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 兑换码表
 * @Author: jeecg-boot
 * @Date:   2025-06-14
 * @Version: V1.0
 */
@Data
@TableName("aicg_exchange_code")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="aicg_exchange_code对象", description="兑换码表")
public class AicgExchangeCode implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String id;
    
	/**兑换码*/
	@Excel(name = "兑换码", width = 20)
    @ApiModelProperty(value = "兑换码")
    private String code;
    
	/**兑换码类型：1-余额，2-会员，3-积分*/
	@Excel(name = "兑换码类型", width = 15, dicCode = "exchange_code_type")
    @Dict(dicCode = "exchange_code_type")
    @ApiModelProperty(value = "兑换码类型：1-余额，2-会员，3-积分")
    private Integer codeType;
    
	/**兑换价值*/
	@Excel(name = "兑换价值", width = 15)
    @ApiModelProperty(value = "兑换价值")
    private BigDecimal value;
    
	/**状态：1-未使用，2-已使用，3-已过期*/
	@Excel(name = "状态", width = 15, dicCode = "exchange_code_status")
    @Dict(dicCode = "exchange_code_status")
    @ApiModelProperty(value = "状态：1-未使用，2-已使用，3-已过期")
    private Integer status;
    
	/**过期时间*/
	@Excel(name = "过期时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "过期时间")
    private Date expireTime;
    
	/**使用者用户ID*/
	@Excel(name = "使用者用户ID", width = 15)
    @ApiModelProperty(value = "使用者用户ID")
    private String usedBy;
    
	/**使用时间*/
	@Excel(name = "使用时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "使用时间")
    private Date usedTime;
    
	/**批次号*/
	@Excel(name = "批次号", width = 15)
    @ApiModelProperty(value = "批次号")
    private String batchNo;
    
	/**备注*/
	@Excel(name = "备注", width = 30)
    @ApiModelProperty(value = "备注")
    private String remark;
    
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;
    
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private Date createTime;
    
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private String updateBy;
    
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private Date updateTime;
    
	/**所属部门*/
    @ApiModelProperty(value = "所属部门")
    private String sysOrgCode;
}
