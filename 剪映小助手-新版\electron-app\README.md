# 剪映小助手 - Electron桌面应用

## 🎬 项目简介

剪映小助手是一款专业的剪映草稿导入工具，支持从扣子平台一键导入草稿到本地剪映。

### ✨ 核心功能

- **🚀 一键导入** - 输入草稿链接，点击导入，自动下载并在剪映中打开
- **📥 智能下载** - 自动从TOS存储下载草稿文件，支持进度显示
- **🎯 路径管理** - 智能检测剪映安装路径，支持手动配置
- **📂 历史记录** - 完整的导入历史，方便管理和回顾
- **⚡ 快捷操作** - 支持剪贴板检测、快捷键等便捷功能

## 🛠️ 技术栈

- **Electron** - 跨平台桌面应用框架
- **Node.js** - 后端逻辑处理
- **HTML/CSS/JavaScript** - 前端界面
- **Electron Store** - 本地数据存储

## 📦 安装和运行

### 开发环境

1. **克隆项目**
```bash
git clone https://github.com/aigcview/jianying-assistant.git
cd jianying-assistant/electron-app
```

2. **安装依赖**
```bash
npm install
```

3. **开发模式运行**
```bash
npm run dev
```

4. **生产模式运行**
```bash
npm start
```

### 打包发布

1. **打包应用**
```bash
npm run build
```

2. **仅打包不发布**
```bash
npm run pack
```

3. **Windows平台打包**
```bash
npm run build-win
```

## 🎯 使用指南

### 首次使用

1. **启动应用** - 双击运行剪映小助手
2. **设置剪映路径** - 首次使用需要设置剪映安装路径
   - 点击"自动检测"让系统自动查找
   - 或点击"浏览"手动选择剪映程序
3. **测试路径** - 点击"测试路径"验证设置是否正确

### 导入草稿

1. **获取草稿链接** - 在扣子平台使用剪映小助手插件，获取草稿下载链接
2. **粘贴链接** - 在应用中粘贴草稿链接
3. **一键导入** - 点击"导入到剪映"按钮
4. **等待完成** - 系统自动下载草稿并在剪映中打开

### 快捷键

- **Ctrl+V** - 检测剪贴板中的草稿链接
- **Ctrl+Enter** - 快速导入当前链接
- **Ctrl+1/2/3** - 切换到首页/历史/设置页面
- **F5** - 刷新当前页面
- **ESC** - 关闭模态框

## 📁 项目结构

```
electron-app/
├── src/
│   ├── main/                 # 主进程
│   │   └── main.js          # 主进程入口
│   └── renderer/            # 渲染进程
│       ├── index.html       # 主页面
│       ├── scripts/         # JavaScript文件
│       │   ├── main.js      # 主应用逻辑
│       │   ├── import.js    # 导入功能
│       │   ├── settings.js  # 设置管理
│       │   ├── ui.js        # UI工具
│       │   └── utils.js     # 工具函数
│       └── styles/          # 样式文件
│           ├── main.css     # 主样式
│           └── components.css # 组件样式
├── assets/                  # 资源文件
│   └── icon.png            # 应用图标
├── package.json            # 项目配置
└── README.md              # 说明文档
```

## 🔧 配置说明

### 应用配置

应用使用 `electron-store` 进行本地配置存储：

- **剪映路径** - `jianyingPath`
- **导入历史** - `importHistory`
- **应用设置** - `appSettings`

### 支持的草稿链接格式

```
https://aigcview-plub.tos-s3-cn-guangzhou.volces.com/jianying-assistant/drafts/YYYY/MM/DD/zj_draft_timestamp_randomcode.json
```

## 🐛 故障排除

### 常见问题

1. **无法检测到剪映路径**
   - 确保剪映已正确安装
   - 尝试手动选择剪映程序路径
   - 检查是否有足够的文件访问权限

2. **下载失败**
   - 检查网络连接
   - 确认草稿链接格式正确
   - 检查防火墙设置

3. **导入失败**
   - 确认剪映路径设置正确
   - 检查剪映是否正在运行
   - 尝试重新启动应用

### 日志查看

开发模式下可以通过开发者工具查看详细日志：
- 按 `F12` 打开开发者工具
- 查看 Console 面板中的日志信息

## 🤝 贡献指南

欢迎提交 Issue 和 Pull Request！

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 📄 许可证

MIT License

## 📞 技术支持

- **官网**: https://www.aigcview.com
- **技术支持**: <EMAIL>
- **用户群**: 加入QQ群获取技术支持

## 🎉 更新日志

### v1.0.0 (2025-01-01)

- ✨ 首次发布
- 🚀 支持一键导入剪映草稿
- 📥 智能下载和进度显示
- 🎯 自动检测剪映路径
- 📂 完整的历史记录管理
- ⚡ 快捷键和剪贴板支持

---

**剪映小助手** - 让视频创作更简单！🎬✨
