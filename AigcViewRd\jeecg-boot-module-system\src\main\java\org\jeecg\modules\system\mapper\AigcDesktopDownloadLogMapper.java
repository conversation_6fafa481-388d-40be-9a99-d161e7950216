package org.jeecg.modules.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.jeecg.modules.system.entity.AigcDesktopDownloadLog;

import java.util.List;
import java.util.Map;

/**
 * 桌面应用下载记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-12
 */
@Mapper
public interface AigcDesktopDownloadLogMapper extends BaseMapper<AigcDesktopDownloadLog> {

    /**
     * 查询今日下载统计
     * @return 统计结果
     */
    @Select("SELECT platform, " +
            "COUNT(*) as downloadCount, " +
            "COUNT(DISTINCT client_ip) as uniqueIpCount, " +
            "SUM(CASE WHEN download_status = 1 THEN 1 ELSE 0 END) as successCount, " +
            "SUM(CASE WHEN download_status = 0 THEN 1 ELSE 0 END) as failCount " +
            "FROM aigc_desktop_download_log " +
            "WHERE DATE(request_time) = CURDATE() " +
            "GROUP BY platform")
    List<Map<String, Object>> getTodayDownloadStats();

    /**
     * 查询最近N天的下载趋势
     * @param days 天数
     * @return 趋势数据
     */
    @Select("SELECT DATE(request_time) as downloadDate, " +
            "platform, " +
            "COUNT(*) as downloadCount, " +
            "COUNT(DISTINCT client_ip) as uniqueIpCount " +
            "FROM aigc_desktop_download_log " +
            "WHERE request_time >= DATE_SUB(CURDATE(), INTERVAL #{days} DAY) " +
            "GROUP BY DATE(request_time), platform " +
            "ORDER BY downloadDate DESC, platform")
    List<Map<String, Object>> getDownloadTrend(@Param("days") int days);

    /**
     * 查询指定IP的下载记录数量（用于频率限制）
     * @param clientIp 客户端IP
     * @param minutes 分钟数
     * @return 下载次数
     */
    @Select("SELECT COUNT(*) FROM aigc_desktop_download_log " +
            "WHERE client_ip = #{clientIp} " +
            "AND request_time >= DATE_SUB(NOW(), INTERVAL #{minutes} MINUTE)")
    int getDownloadCountByIp(@Param("clientIp") String clientIp, @Param("minutes") int minutes);

    /**
     * 查询平台下载总数
     * @return 平台统计
     */
    @Select("SELECT platform, COUNT(*) as totalCount " +
            "FROM aigc_desktop_download_log " +
            "GROUP BY platform")
    List<Map<String, Object>> getPlatformStats();

    /**
     * 查询最近的下载记录
     * @param limit 限制数量
     * @return 下载记录
     */
    @Select("SELECT * FROM aigc_desktop_download_log " +
            "ORDER BY request_time DESC " +
            "LIMIT #{limit}")
    List<AigcDesktopDownloadLog> getRecentDownloads(@Param("limit") int limit);
}
