import com.github.houbb.sensitive.word.bs.SensitiveWordBs;
import java.util.*;

/**
 * 测试houbb库敏感词提取效果
 */
public class TestHoubbExtraction {
    
    public static void main(String[] args) {
        System.out.println("🔍 测试houbb库敏感词提取效果");
        System.out.println("================================");
        
        try {
            // 初始化houbb库
            SensitiveWordBs sensitiveWordBs = SensitiveWordBs.newInstance().init();
            
            // 测试候选敏感词
            String[] testWords = {
                // 政治类
                "法轮功", "台独", "藏独", "疆独", "港独", "反动", "邪教",
                "六四", "天安门", "血腥镇压", "民主运动", "政治改革",
                
                // 色情类
                "色情", "黄色", "成人", "裸体", "性爱", "做爱", "强奸",
                "卖淫", "嫖娼", "援交", "包养", "小姐", "鸡头",
                
                // 暴力类
                "杀人", "自杀", "爆炸", "恐怖", "炸弹", "枪支", "刀具",
                "暴力", "打架", "斗殴", "械斗", "恐怖主义", "ISIS",
                
                // 赌博类
                "赌博", "赌场", "博彩", "老虎机", "百家乐", "21点",
                "赌球", "赌马", "彩票", "六合彩", "时时彩",
                
                // 毒品类
                "毒品", "海洛因", "冰毒", "摇头丸", "大麻", "鸦片",
                "K粉", "麻古", "神仙水", "止咳水", "安非他命",
                
                // 违法类
                "走私", "贩毒", "洗钱", "诈骗", "传销", "非法集资",
                "黑社会", "黑帮", "暴力催收", "套路贷", "校园贷",
                
                // 网络敏感词
                "傻逼", "煞笔", "沙比", "脑残", "智障", "白痴",
                "废物", "垃圾", "屌丝", "狗东西", "畜生", "人渣",
                
                // 正常词汇（应该不被识别）
                "你好", "世界", "中国", "北京", "上海", "学习",
                "工作", "生活", "美好", "幸福", "快乐", "健康"
            };
            
            List<String> detectedWords = new ArrayList<>();
            List<String> normalWords = new ArrayList<>();
            
            System.out.println("📊 开始测试敏感词检测...");
            
            for (String word : testWords) {
                if (sensitiveWordBs.contains(word)) {
                    detectedWords.add(word);
                    System.out.println("✅ 敏感词: " + word);
                } else {
                    normalWords.add(word);
                    System.out.println("⭕ 正常词: " + word);
                }
            }
            
            System.out.println("\n📈 统计结果:");
            System.out.println("总测试词汇: " + testWords.length + " 个");
            System.out.println("检测到敏感词: " + detectedWords.size() + " 个");
            System.out.println("正常词汇: " + normalWords.size() + " 个");
            System.out.println("敏感词检测率: " + String.format("%.1f", (double)detectedWords.size() / testWords.length * 100) + "%");
            
            System.out.println("\n🎯 检测到的敏感词列表:");
            for (String word : detectedWords) {
                System.out.println("- " + word);
            }
            
            System.out.println("\n🔍 测试文本检测:");
            String[] testTexts = {
                "这是一个包含法轮功的测试文本",
                "赌博和色情内容不被允许",
                "正常的工作和生活内容",
                "毒品和暴力是违法的",
                "我爱我的祖国中国"
            };
            
            for (String text : testTexts) {
                List<String> foundWords = sensitiveWordBs.findAll(text);
                System.out.println("文本: " + text);
                System.out.println("发现敏感词: " + foundWords);
                System.out.println("替换后: " + sensitiveWordBs.replace(text));
                System.out.println("---");
            }
            
        } catch (Exception e) {
            System.err.println("❌ 测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
