package org.jeecg.modules.system.service;

/**
 * @Description: 敏感词服务接口
 * @Author: jeecg-boot
 * @Date: 2025-06-22
 * @Version: V1.0
 */
public interface ISensitiveWordService {

    /**
     * 检查文本是否包含敏感词
     * @param text 待检查的文本
     * @return 是否包含敏感词
     */
    boolean containsSensitiveWord(String text);

    /**
     * 获取文本中的第一个敏感词
     * @param text 待检查的文本
     * @return 第一个敏感词，如果没有则返回null
     */
    String findFirstSensitiveWord(String text);

    /**
     * 替换文本中的敏感词
     * @param text 待处理的文本
     * @param replacement 替换字符，默认为*
     * @return 替换后的文本
     */
    String replaceSensitiveWord(String text, char replacement);

    /**
     * 替换文本中的敏感词（使用默认*替换）
     * @param text 待处理的文本
     * @return 替换后的文本
     */
    String replaceSensitiveWord(String text);

    /**
     * 验证昵称是否合法
     * @param nickname 昵称
     * @param currentUserId 当前用户ID（用于排除自己）
     * @return 验证结果
     */
    NicknameValidationResult validateNickname(String nickname, String currentUserId);

    /**
     * 检查IP请求频率限制
     * @param ipAddress IP地址
     * @return 是否允许请求
     */
    boolean checkRateLimit(String ipAddress);

    /**
     * 昵称验证结果类
     */
    class NicknameValidationResult {
        private boolean valid;
        private String message;
        private String errorType; // DUPLICATE, SENSITIVE, FORMAT, LENGTH

        public NicknameValidationResult(boolean valid, String message, String errorType) {
            this.valid = valid;
            this.message = message;
            this.errorType = errorType;
        }

        public static NicknameValidationResult success() {
            return new NicknameValidationResult(true, "昵称可用", null);
        }

        public static NicknameValidationResult fail(String message, String errorType) {
            return new NicknameValidationResult(false, message, errorType);
        }

        // Getters
        public boolean isValid() { return valid; }
        public String getMessage() { return message; }
        public String getErrorType() { return errorType; }
    }
}
