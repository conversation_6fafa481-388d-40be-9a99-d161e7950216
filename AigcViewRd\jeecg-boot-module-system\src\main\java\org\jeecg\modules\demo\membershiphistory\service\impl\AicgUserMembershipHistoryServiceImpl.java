package org.jeecg.modules.demo.membershiphistory.service.impl;

import java.math.BigDecimal;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

import org.jeecg.modules.demo.membershiphistory.entity.AicgUserMembershipHistory;
import org.jeecg.modules.demo.membershiphistory.mapper.AicgUserMembershipHistoryMapper;
import org.jeecg.modules.demo.membershiphistory.service.IAicgUserMembershipHistoryService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: 用户会员订阅历史表
 * @Author: jeecg-boot
 * @Date:   2025-06-18
 * @Version: V1.0
 */
@Service
public class AicgUserMembershipHistoryServiceImpl extends ServiceImpl<AicgUserMembershipHistoryMapper, AicgUserMembershipHistory> implements IAicgUserMembershipHistoryService {

    @Override
    public List<AicgUserMembershipHistory> getByUserId(String userId) {
        return baseMapper.getByUserId(userId);
    }

    @Override
    public AicgUserMembershipHistory getCurrentMembership(String userId) {
        return baseMapper.getCurrentMembership(userId);
    }

    @Override
    public AicgUserMembershipHistory getByOrderId(String orderId) {
        return baseMapper.getByOrderId(orderId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AicgUserMembershipHistory createMembershipHistory(String userId, String orderId, String fromRoleId, 
                                                             String toRoleId, Integer memberLevel, Integer durationMonths, 
                                                             BigDecimal amount, String operatorId) {
        AicgUserMembershipHistory history = new AicgUserMembershipHistory();
        history.setUserId(userId);
        history.setOrderId(orderId);
        history.setFromRoleId(fromRoleId);
        history.setToRoleId(toRoleId);
        history.setMemberLevel(memberLevel);
        history.setDurationMonths(durationMonths);
        history.setAmount(amount);
        
        // 计算开始和结束时间
        Date startTime = new Date();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(startTime);
        calendar.add(Calendar.MONTH, durationMonths);
        Date endTime = calendar.getTime();
        
        history.setStartTime(startTime);
        history.setEndTime(endTime);
        history.setStatus(1); // 生效中
        history.setCreateBy(operatorId);
        history.setCreateTime(new Date());
        
        this.save(history);
        return history;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean cancelMembership(String id, String operatorId) {
        AicgUserMembershipHistory history = this.getById(id);
        if (history != null && history.getStatus() == 1) {
            history.setStatus(3); // 已取消
            history.setUpdateBy(operatorId);
            history.setUpdateTime(new Date());
            return this.updateById(history);
        }
        return false;
    }

    @Override
    public List<AicgUserMembershipHistory> getExpiringMemberships() {
        return baseMapper.getExpiringMemberships();
    }

    @Override
    public List<AicgUserMembershipHistory> getExpiredMemberships() {
        return baseMapper.getExpiredMemberships();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateExpiredMemberships() {
        UpdateWrapper<AicgUserMembershipHistory> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("status", 1)
                    .le("end_time", new Date())
                    .set("status", 2)
                    .set("update_time", new Date());
        // 先查询符合条件的记录数
        long count = this.count(new UpdateWrapper<AicgUserMembershipHistory>()
                .eq("status", 1)
                .le("end_time", new Date()));

        // 执行更新
        boolean result = this.update(updateWrapper);

        return result ? (int) count : 0;
    }
}
