<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.demo.versioncontrol.mapper.AigcVersionControlMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="org.jeecg.modules.demo.versioncontrol.entity.AigcVersionControl">
        <id column="id" property="id" />
        <result column="program_type" property="programType" />
        <result column="version_number" property="versionNumber" />
        <result column="update_content" property="updateContent" />
        <result column="release_date" property="releaseDate" />
        <result column="is_latest" property="isLatest" />
        <result column="status" property="status" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="sys_org_code" property="sysOrgCode" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, program_type, version_number, update_content, release_date, 
        is_latest, status, create_by, create_time, update_by, update_time, sys_org_code
    </sql>

    <!-- 根据程序类型查询最新版本 -->
    <select id="getLatestByProgramType" parameterType="String" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM aigc_version_control 
        WHERE program_type = #{programType} 
        AND is_latest = 1 
        AND status = 1 
        ORDER BY release_date DESC 
        LIMIT 1
    </select>

    <!-- 根据程序类型查询所有版本 -->
    <select id="getVersionsByProgramType" parameterType="String" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM aigc_version_control 
        WHERE program_type = #{programType} 
        AND status = 1 
        ORDER BY release_date DESC
    </select>

    <!-- 根据程序类型和版本号查询版本信息 -->
    <select id="getByProgramTypeAndVersion" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM aigc_version_control 
        WHERE program_type = #{programType} 
        AND version_number = #{versionNumber}
    </select>

    <!-- 将指定程序类型的所有版本设置为非最新版本 -->
    <update id="clearLatestByProgramType">
        UPDATE aigc_version_control
        SET is_latest = 2,
            update_by = #{updateBy},
            update_time = NOW()
        WHERE program_type = #{programType}
    </update>

    <!-- 设置指定版本为最新版本 -->
    <update id="setAsLatest">
        UPDATE aigc_version_control 
        SET is_latest = 1, 
            update_by = #{updateBy}, 
            update_time = NOW() 
        WHERE id = #{id}
    </update>

    <!-- 批量更新状态 -->
    <update id="batchUpdateStatus">
        UPDATE aigc_version_control 
        SET status = #{status}, 
            update_by = #{updateBy}, 
            update_time = NOW() 
        WHERE id IN 
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 查询所有程序类型 -->
    <select id="getAllProgramTypes" resultType="String">
        SELECT DISTINCT program_type 
        FROM aigc_version_control 
        WHERE status = 1 
        ORDER BY program_type
    </select>

    <!-- 统计各程序类型的版本数量 -->
    <select id="countByProgramType" resultType="java.util.Map">
        SELECT program_type, COUNT(*) as count 
        FROM aigc_version_control 
        WHERE status = 1 
        GROUP BY program_type
    </select>

    <!-- 查询最近发布的版本 -->
    <select id="getRecentVersions" parameterType="Integer" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM aigc_version_control 
        WHERE status = 1 
        ORDER BY release_date DESC 
        LIMIT #{limit}
    </select>

    <!-- 检查版本号是否已存在 -->
    <select id="checkVersionExists" resultType="Integer">
        SELECT COUNT(*) 
        FROM aigc_version_control 
        WHERE program_type = #{programType} 
        AND version_number = #{versionNumber}
        <if test="excludeId != null and excludeId != ''">
            AND id != #{excludeId}
        </if>
    </select>

    <!-- 根据发布日期范围查询版本 -->
    <select id="getVersionsByDateRange" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM aigc_version_control 
        WHERE status = 1 
        AND release_date &gt;= #{startDate}
        AND release_date &lt;= #{endDate}
        ORDER BY release_date DESC
    </select>

    <!-- 搜索版本 -->
    <select id="searchVersions" parameterType="String" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM aigc_version_control 
        WHERE status = 1 
        AND (program_type LIKE CONCAT('%', #{keyword}, '%') 
        OR version_number LIKE CONCAT('%', #{keyword}, '%') 
        OR update_content LIKE CONCAT('%', #{keyword}, '%')) 
        ORDER BY release_date DESC
    </select>

    <!-- 复杂查询：带条件的分页查询 -->
    <select id="selectVersionsWithConditions" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM aigc_version_control 
        <where>
            <if test="programType != null and programType != ''">
                AND program_type = #{programType}
            </if>
            <if test="versionNumber != null and versionNumber != ''">
                AND version_number LIKE CONCAT('%', #{versionNumber}, '%')
            </if>
            <if test="isLatest != null">
                AND is_latest = #{isLatest}
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
            <if test="startDate != null and startDate != ''">
                AND release_date &gt;= #{startDate}
            </if>
            <if test="endDate != null and endDate != ''">
                AND release_date &lt;= #{endDate}
            </if>
            <if test="keyword != null and keyword != ''">
                AND (program_type LIKE CONCAT('%', #{keyword}, '%') 
                OR version_number LIKE CONCAT('%', #{keyword}, '%') 
                OR update_content LIKE CONCAT('%', #{keyword}, '%'))
            </if>
        </where>
        ORDER BY release_date DESC
    </select>

</mapper>
