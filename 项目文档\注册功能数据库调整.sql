-- 智界AIGC注册功能数据库调整脚本
-- 执行时间：2025-06-19
-- 说明：为支持用户注册功能，需要对现有表进行字段扩展和新增表

-- ================================
-- 1. 扩展 aicg_user_profile 表
-- ================================

-- 添加用户基础信息冗余字段（便于查询）
ALTER TABLE aicg_user_profile ADD COLUMN username VARCHAR(100) COMMENT '用户名，冗余字段便于查询';
ALTER TABLE aicg_user_profile ADD COLUMN phone VARCHAR(20) COMMENT '手机号，冗余字段便于查询';
ALTER TABLE aicg_user_profile ADD COLUMN email VARCHAR(100) COMMENT '邮箱，冗余字段便于查询';

-- 添加邀请码相关字段
ALTER TABLE aicg_user_profile ADD COLUMN my_invite_code VARCHAR(20) UNIQUE COMMENT '我的邀请码（用于邀请别人）';
ALTER TABLE aicg_user_profile ADD COLUMN used_invite_code VARCHAR(20) COMMENT '我使用的邀请码（注册时填写的）';
ALTER TABLE aicg_user_profile ADD COLUMN inviter_user_id VARCHAR(32) COMMENT '邀请我的人的用户ID';
ALTER TABLE aicg_user_profile ADD COLUMN invite_count INT DEFAULT 0 COMMENT '我邀请的人数统计';

-- 添加注册来源字段
ALTER TABLE aicg_user_profile ADD COLUMN register_source VARCHAR(20) DEFAULT 'manual' COMMENT '注册来源：phone-手机号,email-邮箱,wechat-微信';

-- 添加索引
ALTER TABLE aicg_user_profile ADD INDEX idx_my_invite_code (my_invite_code);
ALTER TABLE aicg_user_profile ADD INDEX idx_used_invite_code (used_invite_code);
ALTER TABLE aicg_user_profile ADD INDEX idx_inviter_user_id (inviter_user_id);

-- ================================
-- 2. 扩展 sys_user 表
-- ================================

-- 添加微信相关字段
ALTER TABLE sys_user ADD COLUMN wechat_openid VARCHAR(100) UNIQUE COMMENT '微信OpenID';
ALTER TABLE sys_user ADD COLUMN wechat_unionid VARCHAR(100) COMMENT '微信UnionID';

-- 添加注册来源字段
ALTER TABLE sys_user ADD COLUMN register_source VARCHAR(20) DEFAULT 'manual' COMMENT '注册来源：phone-手机号,email-邮箱,wechat-微信';

-- 添加验证状态字段
ALTER TABLE sys_user ADD COLUMN email_verified TINYINT DEFAULT 0 COMMENT '邮箱是否已验证：0-未验证,1-已验证';
ALTER TABLE sys_user ADD COLUMN phone_verified TINYINT DEFAULT 0 COMMENT '手机号是否已验证：0-未验证,1-已验证';

-- 添加索引
ALTER TABLE sys_user ADD INDEX idx_wechat_openid (wechat_openid);
ALTER TABLE sys_user ADD INDEX idx_register_source (register_source);

-- ================================
-- 3. 创建验证码记录表
-- ================================

CREATE TABLE aicg_verify_code (
  id VARCHAR(32) NOT NULL PRIMARY KEY COMMENT '主键ID',
  code_type VARCHAR(20) NOT NULL COMMENT '验证码类型：sms-短信,email-邮箱,captcha-图形',
  target VARCHAR(100) NOT NULL COMMENT '目标（手机号/邮箱）',
  code VARCHAR(10) NOT NULL COMMENT '验证码',
  scene VARCHAR(50) NOT NULL COMMENT '使用场景：register-注册,login-登录,reset-重置密码',
  ip_address VARCHAR(50) COMMENT '请求IP',
  used_status TINYINT DEFAULT 0 COMMENT '使用状态：0-未使用,1-已使用',
  expire_time DATETIME NOT NULL COMMENT '过期时间',
  create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  use_time DATETIME COMMENT '使用时间',
  INDEX idx_target_type (target, code_type),
  INDEX idx_expire_time (expire_time),
  INDEX idx_scene (scene),
  INDEX idx_create_time (create_time)
) COMMENT='验证码记录表';

-- ================================
-- 4. 创建微信登录临时数据表
-- ================================

CREATE TABLE aicg_wechat_temp (
  id VARCHAR(32) NOT NULL PRIMARY KEY COMMENT '主键ID',
  scene_id VARCHAR(100) NOT NULL UNIQUE COMMENT '场景ID',
  scene_type VARCHAR(20) NOT NULL COMMENT '场景类型：login-登录,register-注册',
  invite_code VARCHAR(20) COMMENT '邀请码',
  openid VARCHAR(100) COMMENT '微信OpenID',
  nickname VARCHAR(100) COMMENT '微信昵称',
  avatar VARCHAR(500) COMMENT '微信头像',
  status TINYINT DEFAULT 0 COMMENT '状态：0-等待扫码,1-已扫码,2-已授权,3-已完成',
  expire_time DATETIME NOT NULL COMMENT '过期时间',
  create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  INDEX idx_scene_id (scene_id),
  INDEX idx_expire_time (expire_time),
  INDEX idx_openid (openid),
  INDEX idx_status (status)
) COMMENT='微信登录临时数据表';

-- ================================
-- 5. 为现有用户生成邀请码（可选）
-- ================================

-- 为现有用户生成邀请码的存储过程
DELIMITER $$

CREATE PROCEDURE GenerateInviteCodesForExistingUsers()
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE user_id_var VARCHAR(32);
    DECLARE invite_code_var VARCHAR(20);
    DECLARE user_cursor CURSOR FOR 
        SELECT user_id FROM aicg_user_profile WHERE my_invite_code IS NULL OR my_invite_code = '';
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;

    OPEN user_cursor;
    read_loop: LOOP
        FETCH user_cursor INTO user_id_var;
        IF done THEN
            LEAVE read_loop;
        END IF;
        
        -- 生成8位随机邀请码
        SET invite_code_var = UPPER(SUBSTRING(MD5(CONCAT(user_id_var, UNIX_TIMESTAMP(), RAND())), 1, 8));
        
        -- 确保邀请码唯一性
        WHILE EXISTS(SELECT 1 FROM aicg_user_profile WHERE my_invite_code = invite_code_var) DO
            SET invite_code_var = UPPER(SUBSTRING(MD5(CONCAT(user_id_var, UNIX_TIMESTAMP(), RAND())), 1, 8));
        END WHILE;
        
        -- 更新用户邀请码
        UPDATE aicg_user_profile SET my_invite_code = invite_code_var WHERE user_id = user_id_var;
        
    END LOOP;
    CLOSE user_cursor;
END$$

DELIMITER ;

-- 执行存储过程为现有用户生成邀请码
-- CALL GenerateInviteCodesForExistingUsers();

-- ================================
-- 6. 插入默认配置数据
-- ================================

-- 插入验证码配置（如果需要配置表的话）
-- INSERT INTO sys_config (config_key, config_value, config_desc) VALUES 
-- ('sms.code.expire.minutes', '5', '短信验证码有效期（分钟）'),
-- ('email.code.expire.minutes', '5', '邮箱验证码有效期（分钟）'),
-- ('captcha.code.expire.minutes', '2', '图形验证码有效期（分钟）'),
-- ('sms.send.interval.seconds', '60', '短信发送间隔（秒）'),
-- ('email.send.interval.seconds', '60', '邮箱发送间隔（秒）'),
-- ('register.ip.limit.hourly', '5', '同一IP每小时注册限制次数');

-- ================================
-- 7. 清理过期数据的定时任务（可选）
-- ================================

-- 创建清理过期验证码的事件
-- CREATE EVENT IF NOT EXISTS clean_expired_verify_codes
-- ON SCHEDULE EVERY 1 HOUR
-- DO
--   DELETE FROM aicg_verify_code WHERE expire_time < NOW();

-- 创建清理过期微信临时数据的事件
-- CREATE EVENT IF NOT EXISTS clean_expired_wechat_temp
-- ON SCHEDULE EVERY 1 HOUR  
-- DO
--   DELETE FROM aicg_wechat_temp WHERE expire_time < NOW();

-- ================================
-- 执行完成提示
-- ================================

SELECT '数据库调整完成！' as message, 
       '请检查所有表结构是否正确创建' as note,
       '如需为现有用户生成邀请码，请执行：CALL GenerateInviteCodesForExistingUsers();' as optional_step;
