package org.jeecg.modules.demo.exchangecode.service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import org.jeecg.modules.demo.exchangecode.entity.AicgExchangeCode;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * @Description: 兑换码表
 * @Author: jeecg-boot
 * @Date:   2025-06-14
 * @Version: V1.0
 */
public interface IAicgExchangeCodeService extends IService<AicgExchangeCode> {

    /**
     * 根据兑换码查询
     * @param code 兑换码
     * @return 兑换码信息
     */
    AicgExchangeCode getByCode(String code);
    
    /**
     * 生成兑换码
     * @param codeType 兑换码类型：1-余额，2-会员，3-积分
     * @param value 兑换价值
     * @param expireTime 过期时间
     * @param batchNo 批次号
     * @param count 生成数量
     * @param operatorId 操作人ID
     * @return 生成的兑换码列表
     */
    List<AicgExchangeCode> generateExchangeCodes(Integer codeType, BigDecimal value, Date expireTime, String batchNo, Integer count, String operatorId);
    
    /**
     * 使用兑换码
     * @param code 兑换码
     * @param userId 使用者用户ID
     * @param operatorId 操作人ID
     * @return 使用结果信息
     */
    String useExchangeCode(String code, String userId, String operatorId);
    
    /**
     * 查询用户使用的兑换码记录
     * @param userId 用户ID
     * @return 兑换码列表
     */
    List<AicgExchangeCode> getUserUsedCodes(String userId);
    
    /**
     * 批量更新过期状态
     * @return 更新数量
     */
    int updateExpiredCodes();
    
    /**
     * 验证兑换码是否有效
     * @param code 兑换码
     * @return 验证结果信息
     */
    String validateExchangeCode(String code);
    
    /**
     * 批量生成兑换码字符串
     * @param count 生成数量
     * @return 兑换码列表
     */
    List<String> generateCodeStrings(Integer count);
}
