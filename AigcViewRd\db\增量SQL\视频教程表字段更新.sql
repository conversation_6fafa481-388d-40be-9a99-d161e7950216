-- =============================================
-- 智界Aigc 视频教程表字段更新
-- 创建时间：2025-06-29
-- 版本：V1.0
-- 说明：为视频教程表添加视频链接和是否系列视频字段
-- =============================================

-- 1. 检查表结构 - 确认字段是否已存在
DESCRIBE `aigc_video_tutorial`;

-- 2. 经检查发现 isseries 和 video_url 字段都已存在，但需要设置默认值
-- 修改 isseries 字段，添加默认值
ALTER TABLE `aigc_video_tutorial`
MODIFY COLUMN `isseries` VARCHAR(32) DEFAULT '2' COMMENT '是否系列视频：1-是，2-否';

-- 3. 为现有的空值记录设置默认值
UPDATE `aigc_video_tutorial`
SET `isseries` = '2'
WHERE `isseries` IS NULL OR `isseries` = '';

-- 4. 添加索引优化查询性能（如果索引不存在的话）
-- 检查现有索引
SHOW INDEX FROM `aigc_video_tutorial`;

-- 添加索引（如果不存在）
ALTER TABLE `aigc_video_tutorial`
ADD INDEX `idx_video_url` (`video_url`),
ADD INDEX `idx_series_name` (`seriesname`),
ADD INDEX `idx_isseries` (`isseries`);

-- 5. 验证字段和索引
SELECT
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS
WHERE TABLE_SCHEMA = DATABASE()
  AND TABLE_NAME = 'aigc_video_tutorial'
  AND COLUMN_NAME IN ('video_url', 'isseries')
ORDER BY ORDINAL_POSITION;

-- 6. 验证现有数据的默认值设置
SELECT
    COUNT(*) as total_records,
    COUNT(CASE WHEN isseries = '2' THEN 1 END) as default_no_series,
    COUNT(CASE WHEN isseries = '1' THEN 1 END) as is_series,
    COUNT(CASE WHEN isseries IS NULL OR isseries = '' THEN 1 END) as null_values
FROM `aigc_video_tutorial`;

-- =============================================
-- 执行完成提示
-- =============================================
SELECT '视频教程表字段优化完成！' as message,
       'isseries 字段已设置默认值为2(否)' as note,
       '已添加相关索引优化查询性能' as reminder;
