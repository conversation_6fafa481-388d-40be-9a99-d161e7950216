{"openapi": "3.0.0", "info": {"title": "剪映小助手_智界数据箱 - 关键帧数据生成", "description": "生成关键帧数据，用于创建动画效果和过渡效果", "version": "1.0.0"}, "servers": [{"url": "https://www.aigcview.com"}], "paths": {"/jeecg-boot/api/jianying/keyframes_infos": {"post": {"summary": "生成关键帧数据", "description": "生成关键帧数据，用于创建动画效果和过渡效果", "operationId": "keyframes_infos", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"access_key": {"type": "string", "description": "访问密钥（必填）", "example": "JianyingAPI_2025_SecureAccess_AigcView", "default": "JianyingAPI_2025_SecureAccess_AigcView"}, "ctype": {"type": "string", "description": "关键帧类型： KFTypePositionX: X轴移动，需要提供width参数，值会被除以width进行归一化 KFTypePositionY: Y轴移动，需要提供height参数，值会被除以height进行归一化 KFTypeRotation: 旋转角度，值范围必须在0-360度之间 KFTypeScaleX: 均匀缩放，值范围必须在0.01-5之间 KFTypeAlpha: 透明度，值范围必须在0-1之间", "enum": ["KFTypePositionX", "KFTypePositionY", "KFTypeRotation", "KFTypeScaleX", "KFTypeAlpha"], "example": "KFTypePositionX"}, "offsets": {"type": "string", "description": "需要放置关键帧的位置比例，eg：0|100 这个就是代表在开始和结尾放置，0|50|100代表在开头，中间，结尾放置3个关键帧", "example": "0|100"}, "segment_infos": {"type": "array", "description": "轨道数据，add_images节点输出", "items": {"type": "object", "properties": {"segment_id": {"type": "string", "description": "轨道ID"}, "start": {"type": "integer", "description": "开始时间"}, "end": {"type": "integer", "description": "结束时间"}, "duration": {"type": "integer", "description": "持续时间（可选）"}}, "required": ["segment_id", "start", "end"]}, "example": [{"segment_id": "bf50e7c7-d60d-49f4-b9d6-09095ed3988f", "start": 0, "end": 5000000, "duration": 5000000}]}, "values": {"type": "string", "description": "对应offsets的值，长度要一致，比如1|2，或者1|2|1", "example": "1|2"}, "width": {"type": "integer", "description": "图片宽度", "example": 1080}, "height": {"type": "integer", "description": "图片高度", "example": 1920}}, "required": ["access_key", "ctype", "offsets", "segment_infos", "values"], "additionalProperties": false}}}}, "responses": {"200": {"description": "成功生成关键帧数据", "content": {"application/json": {"schema": {"type": "object", "properties": {"keyframes_infos": {"type": "string", "description": "生成的关键帧数据（JSON格式字符串），包含offset、property、segment_id、value字段"}}, "required": ["keyframes_infos"]}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "description": "操作是否成功", "example": false}, "error": {"type": "string", "description": "错误信息"}, "code": {"type": "string", "description": "错误代码"}}, "required": ["success", "error"]}}}}, "500": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "description": "操作是否成功", "example": false}, "error": {"type": "string", "description": "错误信息"}, "code": {"type": "string", "description": "错误代码"}}, "required": ["success", "error"]}}}}}}}}}