# 🌐 智界AIGC路由访问说明

## 📋 访问地址配置

### 🏠 **官网页面（默认访问）**
- **首页**：`http://localhost:3000/` 或 `http://localhost:3000/#/home`
- **插件中心**：`http://localhost:3000/#/market`（待开发）
- **客户案例**：`http://localhost:3000/#/cases`（待开发）
- **教程中心**：`http://localhost:3000/#/tutorials`（待开发）
- **签到奖励**：`http://localhost:3000/#/signin`（待开发）
- **订阅会员**：`http://localhost:3000/#/membership`（待开发）
- **分销推广**：`http://localhost:3000/#/affiliate`（待开发）
- **个人中心**：`http://localhost:3000/#/usercenter`（待开发）

### 🔐 **后台管理系统（管理员登录后自动跳转）**
- **管理系统首页**：`http://localhost:3000/#/dashboard/analysis`
- **插件中心管理**：`http://localhost:3000/#/cjsc`
- **视频教程管理**：`http://localhost:3000/#/spjc`
- **用户中心管理**：`http://localhost:3000/#/views/aigcview/usercenter/UserCenter`
- **系统管理**：`http://localhost:3000/#/isystem`

### 👤 **用户认证页面**
- **登录页面**：`http://localhost:3000/#/user/login`
- **注册页面**：`http://localhost:3000/#/user/register`

## 🎯 **访问逻辑说明**

### **默认访问流程**
1. 用户直接访问 `http://localhost:3000/`
2. 自动跳转到官网首页 `/home`
3. 无需登录即可浏览官网所有页面

### **后台管理访问流程**
1. 管理员访问 `http://localhost:3000/user/login` 登录
2. 系统验证管理员角色
3. 登录成功后自动跳转到后台管理系统
4. 可直接访问所有后台管理功能

### **权限控制**
- **官网页面**：无需登录，所有用户可访问
- **后台管理**：需要登录 + 相应权限
- **用户认证**：登录注册页面

## 🔧 **开发测试建议**

### **测试官网功能**
1. 直接访问 `http://localhost:3000/`
2. 查看首页动效和交互
3. 测试响应式设计
4. 验证GSAP动画效果

### **测试后台管理**
1. 访问 `http://localhost:3000/user/login`
2. 使用管理员账号登录
3. 登录后自动跳转到后台管理系统
4. 测试各个业务模块

## 📱 **移动端访问**
- 官网页面完全支持移动端访问
- 后台管理建议在桌面端使用
- 响应式设计确保各设备兼容

## 🚀 **部署后的访问方式**

### **生产环境**
- **官网**：`https://www.aigcview.com/`
- **后台管理登录**：`https://www.aigcview.com/user/login`

### **开发环境**
- **官网**：`http://localhost:3000/`
- **后台管理登录**：`http://localhost:3000/user/login`

## ⚠️ **注意事项**

1. **路径区分**：
   - 官网页面：简洁一级路径（如 `/home`、`/market`）
   - 后台管理：基于角色自动跳转，无需手动输入路径

2. **权限控制**：
   - 官网页面在白名单中，无需登录
   - 后台管理需要登录验证

3. **开发调试**：
   - 首次访问建议清除浏览器缓存
   - 检查控制台是否有错误信息
   - 确保GSAP库正确加载

## 🔍 **故障排除**

### **如果官网无法访问**
1. 检查路由配置是否正确
2. 确认组件路径是否存在
3. 查看控制台错误信息

### **如果后台管理无法访问**
1. 确认是否使用 `/admin` 前缀
2. 检查登录状态
3. 验证权限配置

### **如果动画效果异常**
1. 确认GSAP库已正确安装
2. 检查组件是否正确引入
3. 查看浏览器兼容性

---

**开发完成！现在您可以启动项目并测试各个功能了！** 🎉
