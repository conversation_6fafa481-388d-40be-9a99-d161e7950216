package org.jeecg.modules.demo.aigc_agent.controller;

import java.io.UnsupportedEncodingException;
import java.io.IOException;
import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.UUID;
import java.security.SecureRandom;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.vo.LoginUser;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.demo.aigc_agent.entity.AigcWorkflow;
import org.jeecg.modules.demo.aigc_agent.entity.AigcAgent;
import org.jeecg.modules.demo.aigc_agent.vo.AigcAgentPage;
import org.jeecg.modules.demo.aigc_agent.service.IAigcAgentService;
import org.jeecg.modules.demo.aigc_agent.service.IAigcWorkflowService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;

 /**
 * @Description: 智能体表
 * @Author: jeecg-boot
 * @Date:   2025-07-31
 * @Version: V1.0
 */
@Api(tags="智能体表")
@RestController
@RequestMapping("/aigc_agent/aigcAgent")
@Slf4j
public class AigcAgentController {
	@Autowired
	private IAigcAgentService aigcAgentService;
	@Autowired
	private IAigcWorkflowService aigcWorkflowService;
	
	/**
	 * 分页列表查询
	 *
	 * @param aigcAgent
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	@AutoLog(value = "智能体表-分页列表查询")
	@ApiOperation(value="智能体表-分页列表查询", notes="智能体表-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(AigcAgent aigcAgent,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<AigcAgent> queryWrapper = QueryGenerator.initQueryWrapper(aigcAgent, req.getParameterMap());
		Page<AigcAgent> page = new Page<AigcAgent>(pageNo, pageSize);
		IPage<AigcAgent> pageList = aigcAgentService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param aigcAgentPage
	 * @return
	 */
	@AutoLog(value = "智能体表-添加")
	@ApiOperation(value="智能体表-添加", notes="智能体表-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody AigcAgentPage aigcAgentPage) {
		AigcAgent aigcAgent = new AigcAgent();
		BeanUtils.copyProperties(aigcAgentPage, aigcAgent);

		// 🔧 自动生成智能体ID，用户不需要输入
		if (aigcAgent.getAgentId() == null || aigcAgent.getAgentId().trim().isEmpty()) {
			aigcAgent.setAgentId(generateUniqueAgentId());
		}

		aigcAgentService.saveMain(aigcAgent, aigcAgentPage.getAigcWorkflowList());
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param aigcAgentPage
	 * @return
	 */
	@AutoLog(value = "智能体表-编辑")
	@ApiOperation(value="智能体表-编辑", notes="智能体表-编辑")
	@PutMapping(value = "/edit")
	public Result<?> edit(@RequestBody AigcAgentPage aigcAgentPage) {
		AigcAgent aigcAgent = new AigcAgent();
		BeanUtils.copyProperties(aigcAgentPage, aigcAgent);
		AigcAgent aigcAgentEntity = aigcAgentService.getById(aigcAgent.getId());
		if(aigcAgentEntity==null) {
			return Result.error("未找到对应数据");
		}
		aigcAgentService.updateMain(aigcAgent, aigcAgentPage.getAigcWorkflowList());
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "智能体表-通过id删除")
	@ApiOperation(value="智能体表-通过id删除", notes="智能体表-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		aigcAgentService.delMain(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "智能体表-批量删除")
	@ApiOperation(value="智能体表-批量删除", notes="智能体表-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.aigcAgentService.delBatchMain(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功！");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "智能体表-通过id查询")
	@ApiOperation(value="智能体表-通过id查询", notes="智能体表-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		AigcAgent aigcAgent = aigcAgentService.getById(id);
		if(aigcAgent==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(aigcAgent);

	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "工作流表通过主表ID查询")
	@ApiOperation(value="工作流表主表ID查询", notes="工作流表-通主表ID查询")
	@GetMapping(value = "/queryAigcWorkflowByMainId")
	public Result<?> queryAigcWorkflowListByMainId(@RequestParam(name="id",required=true) String id) {
		List<AigcWorkflow> aigcWorkflowList = aigcWorkflowService.selectByMainId(id);
		return Result.OK(aigcWorkflowList);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param aigcAgent
    */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, AigcAgent aigcAgent) {
      // Step.1 组装查询条件查询数据
      QueryWrapper<AigcAgent> queryWrapper = QueryGenerator.initQueryWrapper(aigcAgent, request.getParameterMap());
      LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();

      //Step.2 获取导出数据
      List<AigcAgent> queryList = aigcAgentService.list(queryWrapper);
      // 过滤选中数据
      String selections = request.getParameter("selections");
      List<AigcAgent> aigcAgentList = new ArrayList<AigcAgent>();
      if(oConvertUtils.isEmpty(selections)) {
          aigcAgentList = queryList;
      }else {
          List<String> selectionList = Arrays.asList(selections.split(","));
          aigcAgentList = queryList.stream().filter(item -> selectionList.contains(item.getId())).collect(Collectors.toList());
      }

      // Step.3 组装pageList
      List<AigcAgentPage> pageList = new ArrayList<AigcAgentPage>();
      for (AigcAgent main : aigcAgentList) {
          AigcAgentPage vo = new AigcAgentPage();
          BeanUtils.copyProperties(main, vo);
          List<AigcWorkflow> aigcWorkflowList = aigcWorkflowService.selectByMainId(main.getId());
          vo.setAigcWorkflowList(aigcWorkflowList);
          pageList.add(vo);
      }

      // Step.4 AutoPoi 导出Excel
      ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
      mv.addObject(NormalExcelConstants.FILE_NAME, "智能体表列表");
      mv.addObject(NormalExcelConstants.CLASS, AigcAgentPage.class);
      mv.addObject(NormalExcelConstants.PARAMS, new ExportParams("智能体表数据", "导出人:"+sysUser.getRealname(), "智能体表"));
      mv.addObject(NormalExcelConstants.DATA_LIST, pageList);
      return mv;
    }

    /**
    * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
      MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
      Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();
      for (Map.Entry<String, MultipartFile> entity : fileMap.entrySet()) {
          MultipartFile file = entity.getValue();// 获取上传文件对象
          ImportParams params = new ImportParams();
          params.setTitleRows(2);
          params.setHeadRows(1);
          params.setNeedSave(true);
          try {
              List<AigcAgentPage> list = ExcelImportUtil.importExcel(file.getInputStream(), AigcAgentPage.class, params);
              for (AigcAgentPage page : list) {
                  AigcAgent po = new AigcAgent();
                  BeanUtils.copyProperties(page, po);
                  aigcAgentService.saveMain(po, page.getAigcWorkflowList());
              }
              return Result.OK("文件导入成功！数据行数:" + list.size());
          } catch (Exception e) {
              log.error(e.getMessage(),e);
              return Result.error("文件导入失败:"+e.getMessage());
          } finally {
              try {
                  file.getInputStream().close();
              } catch (IOException e) {
                  e.printStackTrace();
              }
          }
      }
      return Result.OK("文件导入失败！");
    }

    /**
     * 生成唯一的智能体ID
     * 格式：AGENT_ + 8位随机字符串
     */
    private String generateUniqueAgentId() {
        String prefix = "AGENT_";
        String characters = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
        SecureRandom random = new SecureRandom();
        StringBuilder sb = new StringBuilder(prefix);

        // 生成8位随机字符串
        for (int i = 0; i < 8; i++) {
            sb.append(characters.charAt(random.nextInt(characters.length())));
        }

        String agentId = sb.toString();

        // 检查是否已存在，如果存在则重新生成
        while (aigcAgentService.lambdaQuery().eq(AigcAgent::getAgentId, agentId).exists()) {
            sb = new StringBuilder(prefix);
            for (int i = 0; i < 8; i++) {
                sb.append(characters.charAt(random.nextInt(characters.length())));
            }
            agentId = sb.toString();
        }

        return agentId;
    }

}
