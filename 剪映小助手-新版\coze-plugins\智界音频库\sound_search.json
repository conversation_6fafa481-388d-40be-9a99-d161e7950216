{"openapi": "3.0.0", "info": {"title": "剪映小助手_智界音频库 - 背景音效搜索", "description": "搜索剪映背景音效库", "version": "1.0.0"}, "servers": [{"url": "https://www.aigcview.com"}], "paths": {"/jeecg-boot/api/jianying/sound_effects_search": {"post": {"summary": "搜索背景音效", "description": "搜索剪映背景音效库", "operationId": "sound_search", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"access_key": {"type": "string", "description": "访问密钥（必填）", "example": "JianyingAPI_2025_SecureAccess_AigcView", "default": "JianyingAPI_2025_SecureAccess_AigcView"}, "keyword": {"type": "string", "description": "搜索关键词（必填）", "example": "打字声"}}, "required": ["access_key", "keyword"], "additionalProperties": false}}}}, "responses": {"200": {"description": "成功搜索背景音效", "content": {"application/json": {"schema": {"type": "object", "properties": {"data": {"type": "array", "description": "背景音效列表", "items": {"type": "object", "properties": {"title": {"type": "string", "description": "音效标题", "example": "打字声"}, "url": {"type": "string", "description": "音效URL", "example": "https://example.com/sound_effect.mp3"}}, "required": ["title", "url"]}}, "message": {"type": "string", "description": "响应消息", "example": "成功搜索音效: 打字声"}, "success": {"type": "boolean", "description": "是否成功", "example": true}}, "required": ["data", "message", "success"]}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"type": "object", "properties": {"data": {"type": "array", "description": "空数据", "items": {"type": "object"}, "example": []}, "message": {"type": "string", "description": "错误消息", "example": "搜索音效失败: 参数错误"}, "success": {"type": "boolean", "description": "是否成功", "example": false}}, "required": ["data", "message", "success"]}}}}}}}}}