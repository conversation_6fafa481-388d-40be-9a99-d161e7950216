<template>
  <j-modal
    :title="title"
    :width="width"
    :visible="visible"
    switchFullscreen
    @ok="handleOk"
    :okButtonProps="{ class:{'jee-hidden': disableSubmit} }"
    @cancel="handleCancel"
    cancelText="关闭">
    <aigc-plub-shop-form ref="realForm" @ok="submitCallback" :disabled="disableSubmit"></aigc-plub-shop-form>
  </j-modal>
</template>

<script>

  import AigcPlubShopForm from './AigcPlubShopForm'
  export default {
    name: 'AigcPlubShopModal',
    components: {
      AigcPlubShopForm
    },
    data () {
      return {
        title:'',
        width:1200,
        visible: false,
        disableSubmit: false
      }
    },
    methods: {
      add () {
        this.visible=true
        this.$nextTick(()=>{
          this.$refs.realForm.add();
        })
      },
      edit (record) {
        this.visible=true
        this.$nextTick(()=>{
          this.$refs.realForm.edit(record);
        })
      },
      close () {
        this.$emit('close');
        this.visible = false;
      },
      handleOk () {
        this.$refs.realForm.submitForm();
      },
      submitCallback(){
        this.$emit('ok');
        this.visible = false;
      },
      handleCancel () {
        // 🔥 关闭前回滚图片变更
        if (this.$refs.realForm && this.$refs.realForm.handleClose) {
          this.$refs.realForm.handleClose()
        }
        this.close()
      }
    }
  }
</script>