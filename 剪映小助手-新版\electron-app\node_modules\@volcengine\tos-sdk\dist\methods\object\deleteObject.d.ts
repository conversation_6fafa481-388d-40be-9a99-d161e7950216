import TOSBase from '../base';
export interface DeleteObjectInput {
    bucket?: string;
    key: string;
    versionId?: string;
    /**@private unstable */
    skipTrash?: string;
    /**@private unstable */
    recursive?: string;
}
export interface DeleteObjectOutput {
    [key: string]: string | undefined;
    ['x-tos-delete-marker']: string;
    ['x-tos-version-id']: string;
}
export declare function deleteObject(this: TOSBase, input: DeleteObjectInput | string): Promise<import("../base").TosResponse<DeleteObjectOutput>>;
export default deleteObject;
