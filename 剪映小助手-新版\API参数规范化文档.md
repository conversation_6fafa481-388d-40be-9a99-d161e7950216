# API参数规范化文档

## 📋 概述

**更新时间**: 2025年1月16日  
**版本**: v2.0.0  
**更新类型**: 重大更新 - API参数规范化  
**影响范围**: 全部17个API接口，78个参数

## 🎯 规范化目标

### 主要目标
1. **移除非标准前缀**: 去除所有参数名中的`zj_`前缀
2. **统一命名规范**: 采用标准的下划线命名法（snake_case）
3. **提升API一致性**: 确保所有接口参数命名风格统一
4. **改善开发体验**: 使参数名更加直观易懂

### 设计原则
- **简洁明了**: 参数名直接反映其功能
- **一致性**: 相同功能的参数在不同接口中使用相同命名
- **标准化**: 遵循RESTful API设计最佳实践
- **向前兼容**: 保持功能不变，仅修改参数名

## 📊 完整修改清单

### 1. add_audios - 批量添加音频

| 修改前 | 修改后 | 说明 |
|--------|--------|------|
| `zj_audio_infos` | `audio_infos` | 音频信息数组 |
| `zj_draft_url` | `draft_url` | 草稿地址 |

**修改数量**: 2个参数

### 2. add_captions - 添加字幕

| 修改前 | 修改后 | 说明 |
|--------|--------|------|
| `zj_draft_url` | `draft_url` | 草稿地址 |
| `zj_captions` | `captions` | 字幕内容 |
| `zj_alpha` | `alpha` | 透明度 |
| `zj_scale_x` | `scale_x` | X轴缩放 |
| `zj_scale_y` | `scale_y` | Y轴缩放 |
| `zj_transform_x` | `transform_x` | X轴位置 |
| `zj_transform_y` | `transform_y` | Y轴位置 |
| `zj_font` | `font` | 字体 |
| `zj_font_size` | `font_size` | 字体大小 |
| `zj_text_color` | `text_color` | 文字颜色 |
| `zj_border_color` | `border_color` | 边框颜色 |
| `zj_alignment` | `alignment` | 对齐方式 |
| `zj_line_spacing` | `line_spacing` | 行间距 |
| `zj_letter_spacing` | `letter_spacing` | 字符间距 |
| `zj_style_text` | `style_text` | 文本样式 |

**修改数量**: 15个参数

### 3. add_effects - 添加特效

| 修改前 | 修改后 | 说明 |
|--------|--------|------|
| `zj_draft_url` | `draft_url` | 草稿地址 |
| `zj_effect_infos` | `effect_infos` | 特效信息 |

**修改数量**: 2个参数

### 4. add_images - 批量添加图片

| 修改前 | 修改后 | 说明 |
|--------|--------|------|
| `zj_draft_url` | `draft_url` | 草稿地址 |
| `zj_alpha` | `alpha` | 透明度 |
| `zj_image_infos` | `image_infos` | 图片信息 |
| `zj_scale_x` | `scale_x` | X轴缩放 |
| `zj_scale_y` | `scale_y` | Y轴缩放 |
| `zj_transform_x` | `transform_x` | X轴位置 |
| `zj_transform_y` | `transform_y` | Y轴位置 |

**修改数量**: 7个参数

### 5. add_keyframes - 添加关键帧

| 修改前 | 修改后 | 说明 |
|--------|--------|------|
| `zj_draft_url` | `draft_url` | 草稿地址 |
| `zj_keyframes` | `keyframes` | 关键帧数据 |

**修改数量**: 2个参数

### 6. add_masks - 增加蒙版

| 修改前 | 修改后 | 说明 |
|--------|--------|------|
| `zj_height` | `height` | 蒙版高度 |
| `zj_name` | `name` | 蒙版名称 |
| `zj_roundCorner` | `roundCorner` | 圆角 |
| `zj_segment_ids` | `segment_ids` | 片段ID |
| `zj_X` | `X` | X轴位置 |
| `zj_Y` | `Y` | Y轴位置 |
| `zj_draft_url` | `draft_url` | 草稿地址 |
| `zj_feather` | `feather` | 羽化 |
| `zj_invert` | `invert` | 反转 |
| `zj_rotation` | `rotation` | 旋转角度 |
| `zj_width` | `width` | 蒙版宽度 |

**修改数量**: 11个参数

### 7. add_sticker - 添加贴纸

| 修改前 | 修改后 | 说明 |
|--------|--------|------|
| `zj_end` | `end` | 结束时间 |
| `zj_scale` | `scale` | 缩放 |
| `zj_start` | `start` | 开始时间 |
| `zj_sticker_id` | `sticker_id` | 贴纸ID |
| `zj_transform_x` | `transform_x` | X轴位置 |
| `zj_transform_y` | `transform_y` | Y轴位置 |
| `zj_draft_url` | `draft_url` | 草稿地址 |

**修改数量**: 7个参数

### 8. add_text_style - 创建文本样式

| 修改前 | 修改后 | 说明 |
|--------|--------|------|
| `zj_font_size` | `font_size` | 字体大小 |
| `zj_keyword` | `keyword` | 关键词 |
| `zj_keyword_color` | `keyword_color` | 关键词颜色 |
| `zj_keyword_font_size` | `keyword_font_size` | 关键词字体大小 |
| `zj_text` | `text` | 文本内容 |

**修改数量**: 5个参数

### 9. add_videos - 批量添加视频

| 修改前 | 修改后 | 说明 |
|--------|--------|------|
| `zj_alpha` | `alpha` | 透明度 |
| `zj_draft_url` | `draft_url` | 草稿地址 |
| `zj_scale_x` | `scale_x` | X轴缩放 |
| `zj_scale_y` | `scale_y` | Y轴缩放 |
| `zj_transform_x` | `transform_x` | X轴位置 |
| `zj_transform_y` | `transform_y` | Y轴位置 |
| `zj_video_infos` | `video_infos` | 视频信息 |

**修改数量**: 7个参数

### 10. create_draft - 创建草稿

| 修改前 | 修改后 | 说明 |
|--------|--------|------|
| `zj_height` | `height` | 视频高度 |
| `zj_user_id` | `user_id` | 用户ID |
| `zj_width` | `width` | 视频宽度 |

**修改数量**: 3个参数

### 11. easy_create_material - 快速创建素材

| 修改前 | 修改后 | 说明 |
|--------|--------|------|
| `zj_audio_url` | `audio_url` | 音频链接 |
| `zj_draft_url` | `draft_url` | 草稿地址 |
| `zj_video_url` | `video_url` | 视频链接 |
| `zj_text` | `text` | 文本内容 |
| `zj_text_transform_y` | `text_transform_y` | 文本Y轴位置 |
| `zj_font_size` | `font_size` | 字体大小 |
| `zj_img_url` | `img_url` | 图片链接 |
| `zj_text_color` | `text_color` | 文本颜色 |

**修改数量**: 8个参数

### 12. gen_video - 云渲染视频

| 修改前 | 修改后 | 说明 |
|--------|--------|------|
| `zj_api_token` | `api_token` | API令牌 |
| `zj_draft_url` | `draft_url` | 草稿地址 |

**修改数量**: 2个参数

### 13. gen_video_status - 查询视频状态

| 修改前 | 修改后 | 说明 |
|--------|--------|------|
| `zj_draft_url` | `draft_url` | 草稿地址 |

**修改数量**: 1个参数

### 14. get_audio_duration - 获取音频时长

| 修改前 | 修改后 | 说明 |
|--------|--------|------|
| `zj_mp3_url` | `mp3_url` | 音频链接 |

**修改数量**: 1个参数

### 15. get_image_animations - 获取图片动画

| 修改前 | 修改后 | 说明 |
|--------|--------|------|
| `zj_mode` | `mode` | 动画模式 |
| `zj_type` | `type` | 动画类型 |

**修改数量**: 2个参数

### 16. get_text_animations - 获取文字动画

| 修改前 | 修改后 | 说明 |
|--------|--------|------|
| `zj_mode` | `mode` | 动画模式 |
| `zj_type` | `type` | 动画类型 |

**修改数量**: 2个参数

### 17. save_draft - 保存草稿

| 修改前 | 修改后 | 说明 |
|--------|--------|------|
| `zj_user_id` | `user_id` | 用户ID |
| `zj_draft_url` | `draft_url` | 草稿地址 |

**修改数量**: 2个参数

## 📈 统计汇总

| 统计项 | 数量 |
|--------|------|
| **修改的API接口** | 17个 |
| **修改的参数总数** | 78个 |
| **最多参数的接口** | add_captions (15个) |
| **最少参数的接口** | gen_video_status (1个) |
| **平均每接口参数数** | 4.6个 |

## 🔄 后端同步更新

### DTO类修改
所有对应的Java DTO类已同步更新：

- ✅ 17个DTO类的@JsonProperty注解已更新
- ✅ 所有验证消息已同步修改
- ✅ 前后端参数映射100%一致

### 修改示例
```java
// 修改前
@JsonProperty("zj_draft_url")
@NotBlank(message = "zj_draft_url不能为空")
private String zjDraftUrl;

// 修改后  
@JsonProperty("draft_url")
@NotBlank(message = "draft_url不能为空")
private String zjDraftUrl;
```

## ⚠️ 重要提醒

### 破坏性更新
此次更新为**破坏性更新**，使用旧参数名的API调用将返回错误。

### 迁移步骤
1. **更新API调用代码**: 移除所有参数名中的`zj_`前缀
2. **重新导入Coze插件**: 使用最新的JSON配置文件
3. **测试验证**: 确保所有API接口正常工作
4. **更新文档**: 同步更新相关技术文档

### 兼容性检查
```bash
# 检查是否还在使用旧参数名
grep -r "zj_" your_project_files/
```

---

**智界AigcView** - 专业的剪映自动化解决方案
