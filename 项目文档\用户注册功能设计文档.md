# 智界AIGC用户注册功能设计文档

## 📋 文档信息
- **创建时间**: 2025-06-19
- **版本**: v1.0
- **状态**: 设计阶段
- **负责人**: 开发团队

---

## 🎯 功能概述

智界AIGC官网用户注册功能，支持三种注册方式，完整的邀请码机制，与现有角色权限体系无缝集成。

### 核心特性
- 三种注册方式：手机号、邮箱、微信扫码
- 完整邀请码机制：链接邀请 + 手动输入
- 角色即会员等级：user、VIP、SVIP、admin
- 安全验证：短信验证码、邮箱验证码、图形验证码
- 自动登录：注册成功后自动登录跳转

---

## 🚀 注册方式设计

### 1. 手机号注册
```
流程：输入手机号 → 图形验证码 → 发送短信验证码 → 输入短信验证码 → 设置密码 → 确认密码 → 邀请码 → 同意协议 → 提交注册
路由：/register?type=phone
验证：阿里云短信服务
```

### 2. 邮箱注册
```
流程：输入邮箱 → 图形验证码 → 发送邮箱验证码 → 输入邮箱验证码 → 设置密码 → 确认密码 → 邀请码 → 同意协议 → 提交注册
路由：/register?type=email
验证：SMTP邮件服务
```

### 3. 微信扫码注册
```
流程：显示二维码 → 用户扫码 → 关注公众号 → 获取微信信息 → 自动注册(携带邀请码) → 登录成功
路由：/register?type=wechat
验证：微信公众号授权
```

---

## 🔗 邀请码机制设计

### 邀请码状态控制
```javascript
// 情况1：通过邀请链接进入
URL: /register?invite=ABC123
状态：邀请码输入框 disabled（禁止编辑）
显示：ABC123（自动填充且不可修改）
提示：通过邀请链接注册

// 情况2：直接访问注册页面
URL: /register
状态：邀请码输入框 enabled（允许手动输入）
显示：空白（用户可以手动输入邀请码）
提示：请输入邀请码（可选）
```

### 邀请码数据流
```
URL检查 → localStorage存储 → 页面状态控制 → 表单验证 → 后端验证 → 建立邀请关系
```

### 微信注册邀请码传递
```
邀请链接 → 生成二维码时携带邀请码 → Redis存储场景数据 → 微信回调获取 → 建立推荐关系
```

---

## 🗄️ 数据库设计

### 需要新增的字段

#### aicg_user_profile表新增字段：
```sql
ALTER TABLE aicg_user_profile ADD COLUMN username VARCHAR(100) COMMENT '用户名，冗余字段便于查询';
ALTER TABLE aicg_user_profile ADD COLUMN phone VARCHAR(20) COMMENT '手机号，冗余字段便于查询';
ALTER TABLE aicg_user_profile ADD COLUMN email VARCHAR(100) COMMENT '邮箱，冗余字段便于查询';
ALTER TABLE aicg_user_profile ADD COLUMN my_invite_code VARCHAR(20) UNIQUE COMMENT '我的邀请码（用于邀请别人）';
ALTER TABLE aicg_user_profile ADD COLUMN used_invite_code VARCHAR(20) COMMENT '我使用的邀请码（注册时填写的）';
ALTER TABLE aicg_user_profile ADD COLUMN inviter_user_id VARCHAR(32) COMMENT '邀请我的人的用户ID';
ALTER TABLE aicg_user_profile ADD COLUMN invite_count INT DEFAULT 0 COMMENT '我邀请的人数统计';
ALTER TABLE aicg_user_profile ADD COLUMN register_source VARCHAR(20) DEFAULT 'manual' COMMENT '注册来源：phone-手机号,email-邮箱,wechat-微信';
```

#### sys_user表新增字段：
```sql
ALTER TABLE sys_user ADD COLUMN wechat_openid VARCHAR(100) UNIQUE COMMENT '微信OpenID';
ALTER TABLE sys_user ADD COLUMN wechat_unionid VARCHAR(100) COMMENT '微信UnionID';
ALTER TABLE sys_user ADD COLUMN register_source VARCHAR(20) DEFAULT 'manual' COMMENT '注册来源：phone-手机号,email-邮箱,wechat-微信';
ALTER TABLE sys_user ADD COLUMN email_verified TINYINT DEFAULT 0 COMMENT '邮箱是否已验证：0-未验证,1-已验证';
ALTER TABLE sys_user ADD COLUMN phone_verified TINYINT DEFAULT 0 COMMENT '手机号是否已验证：0-未验证,1-已验证';
```

### 需要新增的表

#### 验证码记录表：
```sql
CREATE TABLE aicg_verify_code (
  id VARCHAR(32) NOT NULL PRIMARY KEY COMMENT '主键ID',
  code_type VARCHAR(20) NOT NULL COMMENT '验证码类型：sms-短信,email-邮箱,captcha-图形',
  target VARCHAR(100) NOT NULL COMMENT '目标（手机号/邮箱）',
  code VARCHAR(10) NOT NULL COMMENT '验证码',
  scene VARCHAR(50) NOT NULL COMMENT '使用场景：register-注册,login-登录,reset-重置密码',
  ip_address VARCHAR(50) COMMENT '请求IP',
  used_status TINYINT DEFAULT 0 COMMENT '使用状态：0-未使用,1-已使用',
  expire_time DATETIME NOT NULL COMMENT '过期时间',
  create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  use_time DATETIME COMMENT '使用时间',
  INDEX idx_target_type (target, code_type),
  INDEX idx_expire_time (expire_time)
) COMMENT='验证码记录表';
```

#### 微信登录临时数据表：
```sql
CREATE TABLE aicg_wechat_temp (
  id VARCHAR(32) NOT NULL PRIMARY KEY COMMENT '主键ID',
  scene_id VARCHAR(100) NOT NULL UNIQUE COMMENT '场景ID',
  scene_type VARCHAR(20) NOT NULL COMMENT '场景类型：login-登录,register-注册',
  invite_code VARCHAR(20) COMMENT '邀请码',
  openid VARCHAR(100) COMMENT '微信OpenID',
  nickname VARCHAR(100) COMMENT '微信昵称',
  avatar VARCHAR(500) COMMENT '微信头像',
  status TINYINT DEFAULT 0 COMMENT '状态：0-等待扫码,1-已扫码,2-已授权,3-已完成',
  expire_time DATETIME NOT NULL COMMENT '过期时间',
  create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  INDEX idx_scene_id (scene_id),
  INDEX idx_expire_time (expire_time)
) COMMENT='微信登录临时数据表';
```

---

## 👥 会员等级与角色统一

### 设计原则
- **移除独立的会员等级字段**，直接使用角色表
- **角色即会员等级**：user、VIP、SVIP、admin
- **会员升级 = 角色变更**
- **前端通过角色判断会员权限**

### 角色配置
```sql
-- 当前角色表数据
admin   - 管理员
VIP     - VIP会员  
SVIP    - 超级VIP
user    - 普通用户（默认）
```

### 会员等级获取逻辑
```javascript
// 角色优先级：admin > SVIP > VIP > user
async getUserMemberLevel() {
  const userRole = await getUserRole()
  return userRole || 'user'
}
```

---

## 🔧 接口设计

### 注册前验证接口
```
GET  /api/auth/checkPhone?phone=13800138000      # 检查手机号是否已注册
GET  /api/auth/checkEmail?email=<EMAIL>      # 检查邮箱是否已注册
GET  /api/auth/validateInvite?code=ABC123        # 验证邀请码有效性
POST /api/auth/sendSmsCode                       # 发送短信验证码
POST /api/auth/sendEmailCode                     # 发送邮箱验证码
GET  /api/auth/captcha                           # 获取图形验证码
```

### 注册接口
```
POST /api/auth/register
{
  "type": "phone|email|wechat",
  "username": "手机号/邮箱/微信openid",
  "password": "加密密码",
  "phone": "手机号",
  "email": "邮箱",
  "verifyCode": "验证码",
  "captcha": "图形验证码",
  "inviteCode": "邀请码",
  "inviteSource": "link|manual",
  "wechatInfo": {
    "openid": "微信openid",
    "nickname": "微信昵称",
    "avatar": "微信头像"
  }
}
```

### 微信登录接口
```
GET  /api/auth/wechat/qrcode                     # 生成微信登录二维码
GET  /api/auth/wechat/check?scene=SCENE_ID       # 检查扫码状态
POST /api/auth/wechat/callback                   # 微信回调处理
```

---

## 📱 前端组件结构

```
/register
├── RegisterPage.vue                 # 注册页面主组件
├── components/
│   ├── PhoneRegister.vue           # 手机号注册组件
│   ├── EmailRegister.vue           # 邮箱注册组件
│   ├── WechatRegister.vue          # 微信注册组件
│   ├── InviteCodeInput.vue         # 邀请码输入组件
│   ├── CaptchaInput.vue            # 图形验证码组件
│   └── PasswordInput.vue           # 密码输入组件
└── mixins/
    ├── registerMixin.js            # 注册通用逻辑
    └── inviteMixin.js              # 邀请码处理逻辑
```

---

## 🔒 安全策略

### 防刷机制
- 同一IP限制发送频率
- 同一手机号/邮箱限制发送频率  
- 图形验证码防机器注册

### 验证码安全
- 5分钟有效期
- 使用后立即失效
- 加密存储

### 密码安全
- 前端加密 + 后端二次加密
- 密码强度验证：8位以上，包含字母数字

---

## 📋 配置需求清单

### 1. 阿里云短信服务配置
```
- AccessKey ID
- AccessKey Secret  
- 短信签名名称
- 短信模板CODE
- 短信模板内容格式
```

### 2. 邮箱服务配置
```
- SMTP服务器地址
- SMTP端口
- 发送邮箱账号
- 发送邮箱密码/授权码
- 邮件模板设计（HTML格式）
```

### 3. 微信公众号配置
```
- 微信公众号AppID
- 微信公众号AppSecret
- 公众号原始ID
- 服务器配置Token
- 服务器配置EncodingAESKey
- 公众号二维码场景值前缀
```

### 4. 业务规则配置
```
- 密码复杂度要求
- 验证码有效期
- 验证码发送频率限制
- 邀请码生成规则
- 新用户默认权限和初始数据
```

### 5. UI设计需求
```
- 注册页面整体布局风格
- 表单验证提示样式
- 成功/失败状态提示样式
- 邀请码输入框的禁用状态样式
- 微信二维码展示样式
```

### 6. 文案内容
```
- 用户协议和隐私政策内容
- 各种错误提示文案
- 成功注册后的欢迎文案
- 邀请码相关提示文案
```

---

## 🚀 开发计划

### 第一阶段（核心功能）
1. 手机号注册
2. 邮箱注册  
3. 邀请码机制
4. 数据库表结构调整

### 第二阶段（增强功能）
1. 微信扫码注册
2. 完善的安全策略
3. 用户体验优化

### 第三阶段（优化功能）
1. UI/UX优化
2. 性能优化
3. 监控和日志
4. 数据统计分析

---

## 📝 注册成功后数据创建流程

```sql
-- 1. 创建基础用户
INSERT INTO sys_user (id, username, realname, password, phone, email, 
                     wechat_openid, register_source, phone_verified, email_verified,
                     status, create_time) 
VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 1, NOW());

-- 2. 分配默认角色（普通用户）
INSERT INTO sys_user_role (user_id, role_id) 
SELECT ?, id FROM sys_role WHERE role_code = 'user';

-- 3. 创建用户扩展信息
INSERT INTO aicg_user_profile (
  id, user_id, username, nickname, phone, email,
  account_balance, total_consumption, total_recharge,
  my_invite_code, used_invite_code, inviter_user_id,
  register_source, create_time
) VALUES (?, ?, ?, ?, ?, ?, 0.00, 0.00, 0.00, ?, ?, ?, ?, NOW());

-- 4. 如果有邀请码，创建推荐关系
INSERT INTO aicg_user_referral (
  id, referrer_id, referee_id, referral_code, register_time, status, create_time
) VALUES (?, ?, ?, ?, NOW(), 1, NOW());

-- 5. 更新邀请人的邀请统计
UPDATE aicg_user_profile SET invite_count = invite_count + 1 
WHERE user_id = ?;
```

---

## 🎯 注册后流程

```
注册成功 → 自动登录 → 生成JWT Token → 跳转个人中心/首页 → 显示欢迎信息
```

---

*本文档将随着开发进度持续更新和完善。*
