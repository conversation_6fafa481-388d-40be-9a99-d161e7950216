package org.jeecg.modules.demo.versioncontrol.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.jeecg.modules.demo.versioncontrol.entity.AigcVersionControl;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: 程序版本控制表
 * @Author: jeecg-boot
 * @Date: 2025-01-09
 * @Version: V1.0
 */
public interface AigcVersionControlMapper extends BaseMapper<AigcVersionControl> {

    /**
     * 根据程序类型查询最新版本
     * @param programType 程序类型
     * @return 最新版本信息
     */
    @Select("SELECT * FROM aigc_version_control WHERE program_type = #{programType} AND is_latest = 1 AND status = 1 ORDER BY release_date DESC LIMIT 1")
    AigcVersionControl getLatestByProgramType(@Param("programType") String programType);

    /**
     * 根据程序类型查询所有版本（按发布日期倒序）
     * @param programType 程序类型
     * @return 版本列表
     */
    @Select("SELECT * FROM aigc_version_control WHERE program_type = #{programType} AND status = 1 ORDER BY release_date DESC")
    List<AigcVersionControl> getVersionsByProgramType(@Param("programType") String programType);

    /**
     * 根据程序类型和版本号查询版本信息
     * @param programType 程序类型
     * @param versionNumber 版本号
     * @return 版本信息
     */
    @Select("SELECT * FROM aigc_version_control WHERE program_type = #{programType} AND version_number = #{versionNumber}")
    AigcVersionControl getByProgramTypeAndVersion(@Param("programType") String programType, @Param("versionNumber") String versionNumber);

    /**
     * 将指定程序类型的所有版本设置为非最新版本
     * @param programType 程序类型
     * @param updateBy 更新人
     * @return 更新记录数
     */
    @Update("UPDATE aigc_version_control SET is_latest = 2, update_by = #{updateBy}, update_time = NOW() WHERE program_type = #{programType}")
    int clearLatestByProgramType(@Param("programType") String programType, @Param("updateBy") String updateBy);

    /**
     * 设置指定版本为最新版本
     * @param id 版本ID
     * @param updateBy 更新人
     * @return 更新记录数
     */
    @Update("UPDATE aigc_version_control SET is_latest = 1, update_by = #{updateBy}, update_time = NOW() WHERE id = #{id}")
    int setAsLatest(@Param("id") String id, @Param("updateBy") String updateBy);

    /**
     * 批量更新状态
     * @param ids 版本ID列表
     * @param status 状态
     * @param updateBy 更新人
     * @return 更新记录数
     */
    @Update("<script>" +
            "UPDATE aigc_version_control SET status = #{status}, update_by = #{updateBy}, update_time = NOW() " +
            "WHERE id IN " +
            "<foreach collection='ids' item='id' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach>" +
            "</script>")
    int batchUpdateStatus(@Param("ids") List<String> ids, @Param("status") Integer status, @Param("updateBy") String updateBy);

    /**
     * 查询所有程序类型
     * @return 程序类型列表
     */
    @Select("SELECT DISTINCT program_type FROM aigc_version_control WHERE status = 1 ORDER BY program_type")
    List<String> getAllProgramTypes();

    /**
     * 统计各程序类型的版本数量
     * @return 统计结果
     */
    @Select("SELECT program_type, COUNT(*) as count FROM aigc_version_control WHERE status = 1 GROUP BY program_type")
    List<java.util.Map<String, Object>> countByProgramType();

    /**
     * 查询最近发布的版本（限制数量）
     * @param limit 限制数量
     * @return 最近版本列表
     */
    @Select("SELECT * FROM aigc_version_control WHERE status = 1 ORDER BY release_date DESC LIMIT #{limit}")
    List<AigcVersionControl> getRecentVersions(@Param("limit") Integer limit);

    /**
     * 检查版本号是否已存在（同一程序类型下）
     * @param programType 程序类型
     * @param versionNumber 版本号
     * @param excludeId 排除的ID（用于编辑时排除自己）
     * @return 存在的记录数
     */
    @Select("<script>" +
            "SELECT COUNT(*) FROM aigc_version_control " +
            "WHERE program_type = #{programType} AND version_number = #{versionNumber} " +
            "<if test='excludeId != null and excludeId != \"\"'>" +
            "AND id != #{excludeId} " +
            "</if>" +
            "</script>")
    int checkVersionExists(@Param("programType") String programType, 
                          @Param("versionNumber") String versionNumber, 
                          @Param("excludeId") String excludeId);

    /**
     * 根据发布日期范围查询版本
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 版本列表
     */
    @Select("SELECT * FROM aigc_version_control " +
            "WHERE status = 1 " +
            "AND release_date >= #{startDate} " +
            "AND release_date <= #{endDate} " +
            "ORDER BY release_date DESC")
    List<AigcVersionControl> getVersionsByDateRange(@Param("startDate") String startDate, @Param("endDate") String endDate);

    /**
     * 搜索版本（支持程序类型、版本号、更新内容模糊查询）
     * @param keyword 关键词
     * @return 版本列表
     */
    @Select("SELECT * FROM aigc_version_control " +
            "WHERE status = 1 " +
            "AND (program_type LIKE CONCAT('%', #{keyword}, '%') " +
            "OR version_number LIKE CONCAT('%', #{keyword}, '%') " +
            "OR update_content LIKE CONCAT('%', #{keyword}, '%')) " +
            "ORDER BY release_date DESC")
    List<AigcVersionControl> searchVersions(@Param("keyword") String keyword);
}
