import TOSBase from '../base';
import { MRAPMirrorBackRedirectPolicyType } from '../../TosExportEnum';
export interface MirrorBackRule {
    ID?: string;
    Status: 'Enabled' | 'Disabled';
    Condition?: {
        HttpCode?: number[];
        KeyPrefix?: string[];
    };
    Redirect?: {
        RedirectPolicy: MRAPMirrorBackRedirectPolicyType;
    };
}
export interface PutMultiRegionAccessPointMirrorBackInput {
    accountId: string;
    alias: string;
    rules: MirrorBackRule[];
}
export interface PutMultiRegionAccessPointMirrorBackOutput {
}
/**
 * @private unstable method
 */
export declare const putMultiRegionAccessPointMirrorBack: (this: TOSBase, input: PutMultiRegionAccessPointMirrorBackInput) => Promise<import("../base").TosResponse<DeleteMultiRegionAccessPointMirrorBackOutput>>;
export interface GetMultiRegionAccessPointMirrorBackInput {
    accountId: string;
    alias: string;
}
export interface GetMultiRegionAccessPointMirrorBackOutput {
    Rules: MirrorBackRule[];
}
/**
 * @private unstable method
 */
export declare const getMultiRegionAccessPointMirrorBack: (this: TOSBase, input: GetMultiRegionAccessPointMirrorBackInput) => Promise<import("../base").TosResponse<GetMultiRegionAccessPointMirrorBackOutput>>;
export interface DeleteMultiRegionAccessPointMirrorBackInput {
    accountId: string;
    alias: string;
}
export interface DeleteMultiRegionAccessPointMirrorBackOutput {
}
/**
 * @private unstable method
 */
export declare const deleteMultiRegionAccessPointMirrorBack: (this: TOSBase, input: DeleteMultiRegionAccessPointMirrorBackInput) => Promise<import("../base").TosResponse<DeleteMultiRegionAccessPointMirrorBackOutput>>;
