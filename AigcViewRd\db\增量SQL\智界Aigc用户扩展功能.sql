-- =============================================
-- 智界Aigc 用户扩展功能数据库脚本
-- 创建时间：2025-06-14
-- 版本：V1.0
-- 说明：包含用户扩展信息表、交易记录表、兑换码表
-- =============================================

-- 1. 扩展sys_user表，添加用户扩展字段
ALTER TABLE `sys_user` ADD COLUMN `nickname` varchar(100) COMMENT '昵称' AFTER `realname`;
ALTER TABLE `sys_user` ADD COLUMN `account_balance` decimal(10,2) DEFAULT 0.00 COMMENT '账户余额' AFTER `nickname`;
ALTER TABLE `sys_user` ADD COLUMN `api_key` varchar(255) COMMENT 'API密钥' AFTER `account_balance`;

-- 2. 创建用户扩展信息表
DROP TABLE IF EXISTS `aicg_user_profile`;
CREATE TABLE `aicg_user_profile` (
  `id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键id',
  `user_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户ID',
  `nickname` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '昵称',
  `account_balance` decimal(10,2) DEFAULT 0.00 COMMENT '账户余额',
  `api_key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT 'API密钥',
  `total_consumption` decimal(10,2) DEFAULT 0.00 COMMENT '累计消费',
  `total_recharge` decimal(10,2) DEFAULT 0.00 COMMENT '累计充值',
  `member_level` tinyint(2) DEFAULT 1 COMMENT '会员等级：1-普通用户，2-VIP用户，3-SVIP用户',
  `member_expire_time` datetime COMMENT '会员到期时间',
  `status` tinyint(2) DEFAULT 1 COMMENT '状态：1-正常，2-冻结',
  `create_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '创建人',
  `create_time` datetime COMMENT '创建时间',
  `update_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '更新人',
  `update_time` datetime COMMENT '更新时间',
  `sys_org_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '所属部门',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_user_id` (`user_id`) USING BTREE,
  KEY `idx_status` (`status`) USING BTREE,
  KEY `idx_member_level` (`member_level`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='用户扩展信息表';

-- 3. 重建交易记录表（替换原来的aicg_user_record表）
DROP TABLE IF EXISTS `aicg_user_record`;
DROP TABLE IF EXISTS `aicg_user_transaction`;
CREATE TABLE `aicg_user_transaction` (
  `id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键id',
  `user_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户ID',
  `transaction_type` tinyint(2) NOT NULL COMMENT '交易类型：1-消费，2-充值，3-退款，4-兑换',
  `amount` decimal(10,2) NOT NULL COMMENT '交易金额',
  `balance_before` decimal(10,2) NOT NULL COMMENT '交易前余额',
  `balance_after` decimal(10,2) NOT NULL COMMENT '交易后余额',
  `description` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '交易描述',
  `related_order_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '关联订单ID',
  `transaction_time` datetime NOT NULL COMMENT '交易时间',
  `create_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '创建人',
  `create_time` datetime COMMENT '创建时间',
  `update_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '更新人',
  `update_time` datetime COMMENT '更新时间',
  `sys_org_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '所属部门',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_user_id` (`user_id`) USING BTREE,
  KEY `idx_transaction_time` (`transaction_time`) USING BTREE,
  KEY `idx_transaction_type` (`transaction_type`) USING BTREE,
  KEY `idx_user_time` (`user_id`, `transaction_time`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='用户交易记录表';

-- 4. 创建兑换码表
DROP TABLE IF EXISTS `aicg_exchange_code`;
CREATE TABLE `aicg_exchange_code` (
  `id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键id',
  `code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '兑换码',
  `code_type` tinyint(2) NOT NULL COMMENT '兑换码类型：1-余额，2-会员，3-积分',
  `value` decimal(10,2) NOT NULL COMMENT '兑换价值',
  `status` tinyint(2) DEFAULT 1 COMMENT '状态：1-未使用，2-已使用，3-已过期',
  `expire_time` datetime COMMENT '过期时间',
  `used_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '使用者用户ID',
  `used_time` datetime COMMENT '使用时间',
  `batch_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '批次号',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '备注',
  `create_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '创建人',
  `create_time` datetime COMMENT '创建时间',
  `update_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '更新人',
  `update_time` datetime COMMENT '更新时间',
  `sys_org_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '所属部门',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_code` (`code`) USING BTREE,
  KEY `idx_status` (`status`) USING BTREE,
  KEY `idx_expire_time` (`expire_time`) USING BTREE,
  KEY `idx_batch_no` (`batch_no`) USING BTREE,
  KEY `idx_used_by` (`used_by`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='兑换码表';

-- 5. 添加数据字典
INSERT INTO `sys_dict` (`id`, `dict_name`, `dict_code`, `description`, `del_flag`, `create_by`, `create_time`) VALUES 
('aicg_transaction_type_dict', '交易类型', 'transaction_type', '用户交易记录类型字典', 0, 'admin', NOW());

INSERT INTO `sys_dict_item` (`id`, `dict_id`, `item_text`, `item_value`, `description`, `sort_order`, `status`, `create_by`, `create_time`) VALUES 
('aicg_transaction_type_1', 'aicg_transaction_type_dict', '消费', '1', '用户消费', 1, 1, 'admin', NOW()),
('aicg_transaction_type_2', 'aicg_transaction_type_dict', '充值', '2', '用户充值', 2, 1, 'admin', NOW()),
('aicg_transaction_type_3', 'aicg_transaction_type_dict', '退款', '3', '用户退款', 3, 1, 'admin', NOW()),
('aicg_transaction_type_4', 'aicg_transaction_type_dict', '兑换', '4', '兑换码兑换', 4, 1, 'admin', NOW());

INSERT INTO `sys_dict` (`id`, `dict_name`, `dict_code`, `description`, `del_flag`, `create_by`, `create_time`) VALUES 
('aicg_exchange_code_type_dict', '兑换码类型', 'exchange_code_type', '兑换码类型字典', 0, 'admin', NOW());

INSERT INTO `sys_dict_item` (`id`, `dict_id`, `item_text`, `item_value`, `description`, `sort_order`, `status`, `create_by`, `create_time`) VALUES 
('aicg_exchange_code_type_1', 'aicg_exchange_code_type_dict', '余额', '1', '余额兑换码', 1, 1, 'admin', NOW()),
('aicg_exchange_code_type_2', 'aicg_exchange_code_type_dict', '会员', '2', '会员兑换码', 2, 1, 'admin', NOW()),
('aicg_exchange_code_type_3', 'aicg_exchange_code_type_dict', '积分', '3', '积分兑换码', 3, 1, 'admin', NOW());

INSERT INTO `sys_dict` (`id`, `dict_name`, `dict_code`, `description`, `del_flag`, `create_by`, `create_time`) VALUES 
('aicg_exchange_code_status_dict', '兑换码状态', 'exchange_code_status', '兑换码状态字典', 0, 'admin', NOW());

INSERT INTO `sys_dict_item` (`id`, `dict_id`, `item_text`, `item_value`, `description`, `sort_order`, `status`, `create_by`, `create_time`) VALUES 
('aicg_exchange_code_status_1', 'aicg_exchange_code_status_dict', '未使用', '1', '兑换码未使用', 1, 1, 'admin', NOW()),
('aicg_exchange_code_status_2', 'aicg_exchange_code_status_dict', '已使用', '2', '兑换码已使用', 2, 1, 'admin', NOW()),
('aicg_exchange_code_status_3', 'aicg_exchange_code_status_dict', '已过期', '3', '兑换码已过期', 3, 1, 'admin', NOW());

-- 6. 创建定时任务更新过期兑换码的存储过程
DELIMITER $$
CREATE PROCEDURE UpdateExpiredExchangeCodes()
BEGIN
    UPDATE aicg_exchange_code 
    SET status = 3, update_time = NOW() 
    WHERE status = 1 AND expire_time < NOW();
END$$
DELIMITER ;

-- 执行完成提示
SELECT '智界Aigc用户扩展功能数据库脚本执行完成！' as message;
