<template>
  <a-modal
    :title="modalTitle"
    :visible="visible"
    :width="900"
    :confirm-loading="stepLoading"
    :mask-closable="false"
    :centered="true"
    :body-style="{ padding: '0' }"
    :footer="null"
    class="creator-agent-modal"
    @cancel="handleCancel"
  >
    <!-- 步骤条 -->
    <div class="step-header">
      <a-steps :current="currentStep" class="custom-steps">
        <a-step title="智能体信息" description="填写基本信息" />
        <a-step title="工作流配置" description="配置工作流" />
        <a-step title="完成创建" description="创建完成" />
      </a-steps>
    </div>

    <!-- 步骤内容 -->
    <div class="step-content">
      <!-- 第一步：智能体基本信息 -->
      <div v-show="currentStep === 0" class="step-panel">
        <div class="panel-header">
          <h3 class="panel-title">
            <a-icon type="robot" />
            智能体基本信息
          </h3>
          <p class="panel-desc">请填写智能体的基本信息，这些信息将展示给用户</p>
        </div>

        <a-form-model
          ref="form"
          :model="formData"
          :rules="rules"
          class="modern-form"
        >
          <!-- 智能体名称 -->
          <div class="form-card">
            <a-form-model-item prop="agentName">
              <div class="field-label">
                <a-icon type="robot" />
                智能体名称
                <span class="required-star">*</span>
              </div>
              <a-input
                v-model="formData.agentName"
                placeholder="请输入智能体名称"
                size="large"
                class="modern-input"
                :max-length="100"
                show-count
              />
              <div class="field-tips">
                为您的智能体起一个吸引人的名称
              </div>
            </a-form-model-item>
          </div>

          <!-- 智能体描述 -->
          <div class="form-card">
            <a-form-model-item prop="agentDescription">
              <div class="field-label">
                <a-icon type="file-text" />
                智能体描述
                <span class="required-star">*</span>
              </div>
              <a-textarea
                v-model="formData.agentDescription"
                placeholder="请详细描述您的智能体功能和特点"
                :rows="4"
                :max-length="1000"
                show-count
                class="modern-textarea"
              />
              <div class="field-tips">
                详细描述有助于用户了解您的智能体功能
              </div>
            </a-form-model-item>
          </div>

          <!-- 智能体头像 -->
          <div class="form-card">
            <a-form-model-item prop="agentAvatar">
              <div class="field-label">
                <a-icon type="picture" />
                智能体头像
                <span class="required-star">*</span>
              </div>
              <j-image-upload-deferred
                ref="avatarUpload"
                v-model="formData.agentAvatar"
                :isMultiple="false"
                bizPath="agent-avatar"
                text="上传头像">
              </j-image-upload-deferred>
              <div class="field-tips">
                支持 JPG、PNG 格式，文件大小不超过 5MB
              </div>
            </a-form-model-item>
          </div>

          <!-- 体验链接 -->
          <div class="form-card">
            <a-form-model-item prop="experienceLink">
              <div class="field-label">
                <a-icon type="link" />
                体验链接
                <span class="optional">（可选）</span>
              </div>
              <a-input
                v-model="formData.experienceLink"
                placeholder="请输入体验链接"
                size="large"
                class="modern-input"
                :max-length="500"
              />
              <div class="field-tips">
                用户可以通过此链接体验您的智能体功能
              </div>
            </a-form-model-item>
          </div>

          <!-- 价格 -->
          <div class="form-card">
            <a-form-model-item prop="price">
              <div class="field-label">
                <a-icon type="dollar" />
                价格设置
                <span class="required-star">*</span>
              </div>
              <a-input-number
                v-model="formData.price"
                placeholder="请输入价格"
                :min="0"
                :max="99999"
                :precision="2"
                :step="0.01"
                size="large"
                class="modern-input-number"
              >
                <template slot="addonBefore">¥</template>
              </a-input-number>
              <div class="field-tips">
                设置智能体的使用价格，用户购买后可以使用您的智能体
              </div>
            </a-form-model-item>
          </div>
        </a-form-model>

        <!-- 第一步底部按钮 -->
        <div class="step-footer">
          <a-button @click="handleCancel" :disabled="stepLoading">
            取消
          </a-button>
          <a-button type="primary" @click="handleNext" :loading="stepLoading" class="next-btn">
            下一步：配置工作流
            <a-icon type="arrow-right" />
          </a-button>
        </div>
      </div>

      <!-- 第二步：工作流配置 -->
      <div v-show="currentStep === 1" class="step-panel">
        <div class="panel-header">
          <h3 class="panel-title">
            <a-icon type="apartment" />
            工作流配置
          </h3>
          <p class="panel-desc">为您的智能体配置工作流，提升智能体的功能和效率</p>
        </div>

        <!-- 已创建的智能体信息 -->
        <div class="created-agent-info" v-if="createdAgent">
          <div class="agent-summary">
            <div class="agent-avatar">
              <img :src="getFullAvatarUrl(createdAgent.agentAvatar)" :alt="createdAgent.agentName" v-if="createdAgent.agentAvatar" />
              <a-icon type="robot" v-else />
            </div>
            <div class="agent-details">
              <h4>{{ createdAgent.agentName }}</h4>
              <p>{{ createdAgent.agentDescription }}</p>
              <span class="agent-price">¥{{ createdAgent.price }}</span>
            </div>
          </div>
        </div>

        <!-- 工作流配置区域 -->
        <div class="workflow-section">
          <!-- 工作流表单 -->
          <div class="workflow-form">
            <div class="form-header">
              <h3>
                <a-icon type="apartment" />
                新增工作流
              </h3>
              <p>为您的智能体添加工作流，让它更加智能和实用</p>
            </div>

            <a-form-model ref="workflowFormRef" :model="workflowFormData" :rules="workflowRules" layout="vertical">
              <a-form-model-item label="工作流名称" prop="workflowName">
                <a-input
                  v-model="workflowFormData.workflowName"
                  placeholder="请输入工作流名称，如：文档生成助手"
                  :maxLength="30"
                  show-count
                />
              </a-form-model-item>

              <a-form-model-item label="工作流描述" prop="workflowDescription">
                <a-textarea
                  v-model="workflowFormData.workflowDescription"
                  placeholder="请描述工作流的功能和用途，帮助用户了解其作用"
                  :rows="3"
                  :maxLength="200"
                  show-count
                />
              </a-form-model-item>

              <a-form-model-item label="输入参数说明" prop="inputParamsDesc">
                <a-textarea
                  v-model="workflowFormData.inputParamsDesc"
                  placeholder="格式：参数:值 或 参数:&quot;值&quot; 或 参数:'值'（例如：name:&quot;张三&quot;,age:25,city:'北京'）"
                  :rows="4"
                  :maxLength="10000"
                  @blur="handleInputParamsBlur"
                />
                <div style="color: #666; font-size: 12px; margin-top: 4px;">
                  * 必填项，例如：name:&quot;张三&quot;,age:25,city:'北京' 支持中英文冒号逗号
                </div>
              </a-form-model-item>

              <a-form-model-item label="工作流文件" prop="workflowPackage">
                <!-- 文件上传区域 -->
                <div class="workflow-file-upload">
                  <!-- 已上传文件显示 -->
                  <div v-if="workflowFileInfo" class="uploaded-file-info">
                    <div class="file-item" :class="{ 'saved-file': workflowFileInfo.isSaved }">
                      <a-icon type="file-zip" class="file-icon" />
                      <span class="file-name">{{ workflowFileInfo.originalName || workflowFileInfo.name }}</span>
                      <!-- 🔥 已保存的文件显示状态标签 -->
                      <a-tag v-if="workflowFileInfo.isSaved" color="green" size="small" style="margin-left: 8px;">
                        已保存
                      </a-tag>
                      <a-button
                        type="link"
                        size="small"
                        @click="handleRemoveWorkflowFile"
                        class="remove-btn"
                        :title="workflowFileInfo.isSaved ? '重新选择文件' : '删除文件'"
                      >
                        <a-icon :type="workflowFileInfo.isSaved ? 'edit' : 'delete'" />
                        {{ workflowFileInfo.isSaved ? '重选' : '删除' }}
                      </a-button>
                    </div>
                  </div>

                  <!-- 上传按钮 -->
                  <div v-else class="upload-area">
                    <a-upload
                      ref="workflowUpload"
                      name="file"
                      :multiple="false"
                      :before-upload="beforeWorkflowUpload"
                      :show-upload-list="false"
                      @change="handleWorkflowFileSelect"
                      accept=".zip"
                      :customRequest="() => {}"
                    >
                      <a-button :loading="workflowUploading">
                        <a-icon type="upload" />
                        选择工作流压缩包
                      </a-button>
                    </a-upload>
                  </div>
                </div>

                <div class="upload-tip">
                  <a-icon type="exclamation-circle" style="color: #faad14;" />
                  <strong>温馨提示：</strong>只支持 .zip 格式，文件大小不超过 5MB
                </div>
              </a-form-model-item>
            </a-form-model>

            <!-- 新增下一个工作流按钮 -->
            <div class="add-next-workflow" v-show="currentStep === 1">
              <a-button type="dashed" block @click="addNextWorkflow" class="add-next-btn">
                <a-icon type="plus" />
                新增下一个工作流
              </a-button>
            </div>
          </div>

          <!-- 工作流列表 -->
          <div class="temp-workflows" v-if="tempWorkflowList.length > 0">
            <a-divider>工作流列表 ({{ tempWorkflowList.length }})</a-divider>
            <div class="workflow-list">
              <div
                v-for="(workflow, index) in tempWorkflowList"
                :key="workflow.id"
                class="workflow-item"
                :class="{
                  'editing': currentWorkflowIndex === index,
                  'has-error': workflowValidationErrors[workflow.id] && !workflowValidationErrors[workflow.id].isValid
                }"
              >
                <div class="workflow-info">
                  <h4 class="workflow-name">
                    {{ workflow.workflowName }}
                    <!-- 状态标签 -->
                    <a-tag v-if="workflow.status === 'saved'" color="green" size="small">已保存</a-tag>
                    <a-tag v-else-if="workflow.status === 'draft'" color="orange" size="small">新增</a-tag>
                    <a-tag v-if="currentWorkflowIndex === index" color="blue" size="small">编辑中</a-tag>
                    <a-tag v-if="workflowValidationErrors[workflow.id] && !workflowValidationErrors[workflow.id].isValid" color="red" size="small">
                      <a-icon type="exclamation-circle" />
                      有错误
                    </a-tag>
                  </h4>
                  <p class="workflow-desc">{{ workflow.workflowDescription || '暂无描述' }}</p>

                  <!-- 🔥 验证错误提示 -->
                  <div v-if="workflowValidationErrors[workflow.id] && !workflowValidationErrors[workflow.id].isValid" class="workflow-errors">
                    <a-alert
                      type="error"
                      size="small"
                      show-icon
                      :message="`请补充：${workflowValidationErrors[workflow.id].errors.join('、')}`"
                    />
                  </div>

                </div>
                <div class="workflow-actions">
                  <a-button size="small" @click="loadWorkflowFromTemp(index)" :disabled="currentWorkflowIndex === index">
                    <a-icon type="edit" />
                    编辑
                  </a-button>
                  <a-button size="small" type="danger" @click="deleteWorkflowFromTemp(index)">
                    <a-icon type="delete" />
                    删除
                  </a-button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 第二步底部按钮 -->
        <div class="step-footer">
          <a-button @click="handlePrev" :disabled="stepLoading">
            <a-icon type="arrow-left" />
            上一步
          </a-button>
          <a-button type="primary" @click="handleComplete" :loading="stepLoading" class="complete-btn">
            完成创建
            <a-icon type="check" />
          </a-button>
        </div>
      </div>

      <!-- 第三步：创建完成 -->
      <div v-if="currentStep === 2" class="step-content">
        <div class="step-header">
          <h3>
            <a-icon type="check-circle" style="color: #52c41a; margin-right: 8px;" />
            创建完成
          </h3>
          <p>智能体创建流程已完成</p>
        </div>

        <div class="success-content">
          <div class="success-icon">
            <a-icon type="check-circle" style="font-size: 64px; color: #52c41a;" />
          </div>
          <div class="success-message">
            <h2>恭喜您，已成功提交智能体，请耐心等待审核！</h2>
            <p>您的智能体信息已提交至平台，我们将在1-3个工作日内完成审核。</p>
            <p>审核结果将通过站内消息通知您，请注意查收。</p>
          </div>
        </div>

        <!-- 第三步底部按钮 -->
        <div class="step-footer">
          <a-button type="primary" @click="handleCloseModal" size="large">
            关闭
          </a-button>
        </div>
      </div>
    </div>
  </a-modal>
</template>

<script>
import JImageUploadDeferred from '@/components/jeecg/JImageUploadDeferred'
import { createAgent, updateAgent } from '@/api/creator-agent'
import { createWorkflow, updateWorkflow, getWorkflowList } from '@/api/creator-workflow'

export default {
  name: 'CreatorAgentForm',
  components: {
    JImageUploadDeferred
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    loading: {
      type: Boolean,
      default: false
    },
    agent: {
      type: Object,
      default: null
    },
    mode: {
      type: String,
      default: 'create' // 'create' | 'edit'
    }
  },

  data() {
    return {
      currentStep: 0, // 当前步骤 0=第一步，1=第二步
      stepLoading: false, // 步骤操作加载状态
      createdAgent: null, // 第一步创建的智能体信息

      formData: {
        agentId: '',
        agentName: '',
        agentDescription: '',
        agentAvatar: '',
        experienceLink: '',
        price: 0
      },

      // 🔥 工作流表单数据
      workflowFormData: {
        workflowName: '',
        workflowDescription: '',
        inputParamsDesc: '',
        workflowPackage: ''
      },

      // 🔥 工作流前端暂存管理（新的数据管理机制）
      tempWorkflowList: [], // 前端暂存的工作流列表
      currentWorkflowIndex: -1, // 当前正在编辑的工作流索引 (-1表示新建)

      // 🔥 工作流验证错误状态管理
      workflowValidationErrors: {}, // 格式：{ workflowId: { errors: ['缺少工作流描述', '缺少压缩包文件'], isValid: false } }
      workflowFileList: [], // 当前工作流的文件列表（File对象，未上传）

      // 🔥 工作流文件上传相关（延迟上传机制）
      workflowFileInfo: null, // 当前选择的文件信息（用于显示）
      workflowUploading: false, // 上传状态
      workflowList: [], // 已保存的工作流列表（从后端加载）
      workflowLoading: false,

      rules: {
        agentName: [
          { required: true, message: '请输入智能体名称', trigger: 'blur' },
          { min: 2, max: 100, message: '智能体名称长度在 2 到 100 个字符', trigger: 'blur' }
        ],
        agentDescription: [
          { required: true, message: '请输入智能体描述', trigger: 'blur' },
          { min: 2, max: 1000, message: '智能体描述长度在 2 到 1000 个字符', trigger: 'blur' }
        ],
        agentAvatar: [
          { required: true, message: '请上传智能体头像', trigger: 'change' }
        ],
        experienceLink: [
          { type: 'url', message: '请输入正确的URL格式', trigger: 'blur' }
        ],
        price: [
          { required: true, message: '请输入价格', trigger: 'blur' },
          { type: 'number', min: 0, max: 99999, message: '价格范围在 0 到 99999 元', trigger: 'blur' }
        ]
      },

      // 🔥 工作流表单验证规则（所有字段必填）
      workflowRules: {
        workflowName: [
          { required: true, message: '工作流名称为必填项', trigger: 'blur' },
          { min: 2, max: 30, message: '工作流名称长度在 2 到 30 个字符', trigger: 'blur' },
          { pattern: /^[a-zA-Z0-9\u4e00-\u9fa5\s\-_]+$/, message: '工作流名称只能包含中英文、数字、空格、横线和下划线', trigger: 'blur' }
        ],
        workflowDescription: [
          { required: true, message: '工作流描述为必填项', trigger: 'blur' },
          { min: 2, max: 200, message: '工作流描述长度在 2 到 200 个字符', trigger: 'blur' }
        ],
        inputParamsDesc: [
          { required: true, message: '请输入参数说明', trigger: 'blur' },
          { min: 2, message: '参数说明至少需要2个字符', trigger: 'blur' },
          { max: 10000, message: '参数说明长度不能超过10000个字符', trigger: 'blur' },
          {
            pattern: /^[^:：]+[:：](?:"[^"]*"|'[^']*'|[^:：,，]+)(?:[,，][^:：]+[:：](?:"[^"]*"|'[^']*'|[^:：,，]+))*$/,
            message: '请按照格式填写：参数:值 或 参数:"值" 或 参数:\'值\'',
            trigger: 'blur'
          }
        ],
        workflowPackage: [
          // 🔥 移除自动验证，改为手动验证，避免文件选择后仍提示必填
        ]
      }
    }
  },

  computed: {
    modalTitle() {
      return this.mode === 'create' ? '新增智能体' : '编辑智能体'
    },

    // 🔥 文件上传配置（与后台管理系统一致）
    uploadAction() {
      return `${window._CONFIG['domianURL']}/sys/common/upload`
    },

    uploadHeaders() {
      return {
        'X-Access-Token': this.$ls.get('Access-Token')
      }
    },

    uploadData() {
      return {
        'isup': 1,
        'biz': '' // 工作流文件使用空的biz，后台会自动设置为workflow路径
      }
    }
  },

  watch: {
    visible(val) {
      if (val) {
        this.initForm()
        // 🔥 新增和编辑模式都从第一步开始，让用户可以修改基本信息
        this.currentStep = 0

        // 🔥 弹窗打开时立即滚动到顶部
        this.scrollToTop()

        if (this.mode === 'edit' && this.agent) {
          this.createdAgent = { ...this.agent }
          // 🔥 编辑模式下立即加载工作流数据，确保数据回填
          this.$nextTick(() => {
            this.loadWorkflowList(this.agent.id)
          })
        }
      } else {
        // 🔥 弹窗关闭时清空所有数据，避免数据污染
        this.resetForm()
        this.clearAllWorkflowData()
      }
    },

    agent: {
      handler(val) {
        if (val && this.visible) {
          this.initForm()
          if (this.mode === 'edit') {
            this.createdAgent = { ...val }
            // 🔥 编辑模式下立即加载工作流数据，确保数据回填
            this.$nextTick(() => {
              this.loadWorkflowList(val.id)
            })
          }
        }
      },
      deep: true
    }
  },

  methods: {
    // 初始化表单
    initForm() {
      if (this.mode === 'edit' && this.agent) {
        this.formData = {
          agentName: this.agent.agentName || '',
          agentDescription: this.agent.agentDescription || '',
          agentAvatar: this.agent.agentAvatar || '',
          experienceLink: this.agent.experienceLink || '',
          price: this.agent.price || 0
        }
      } else {
        this.formData = {
          agentName: '',
          agentDescription: '',
          agentAvatar: '',
          experienceLink: '',
          price: 0
        }

        // 🔥 新增智能体时清空所有暂存工作流数据，确保每个智能体的创建流程是独立的
        this.clearAllWorkflowData()
      }

      // 清除验证状态
      this.$nextTick(() => {
        if (this.$refs.form) {
          this.$refs.form.clearValidate()
        }
      })
    },

    // 重置表单
    resetForm() {
      this.currentStep = 0
      this.stepLoading = false
      this.createdAgent = null

      this.formData = {
        agentId: '',
        agentName: '',
        agentDescription: '',
        agentAvatar: '',
        experienceLink: '',
        price: 0
      }
      this.workflowList = []

      if (this.$refs.form) {
        this.$refs.form.clearValidate()
      }
    },

    // 🔥 第一步：下一步按钮
    async handleNext() {
      // 🔥 先进行表单验证
      const originalAvatar = this.formData.agentAvatar
      const hasPendingAvatar = this.$refs.avatarUpload && this.$refs.avatarUpload.hasPendingFiles()

      if (!this.formData.agentAvatar && hasPendingAvatar) {
        this.formData.agentAvatar = 'pending_upload'
      }

      this.$refs.form.validate(async (valid) => {
        this.formData.agentAvatar = originalAvatar

        if (valid) {
          this.stepLoading = true
          try {
            // 🔥 上传头像
            await this.uploadPendingImages()

            // 🔥 提交智能体信息
            const agentData = {
              agentName: this.formData.agentName.trim(),
              agentDescription: this.formData.agentDescription.trim(),
              agentAvatar: this.processAvatarValue(this.formData.agentAvatar),
              experienceLink: this.formData.experienceLink.trim(),
              price: this.formData.price
            }



            // 🔥 根据模式调用不同的API
            let resultAgent
            if (this.mode === 'create') {
              // 新增模式：创建新智能体
              resultAgent = await this.createAgentStep(agentData)
              this.$message.success('智能体创建成功，请配置工作流')
            } else {
              // 编辑模式：更新现有智能体
              resultAgent = await this.updateAgentStep(agentData)
              this.$message.success('智能体更新成功，请配置工作流')
            }

            this.createdAgent = resultAgent

            // 进入第二步
            this.currentStep = 1
            this.loadWorkflowList(resultAgent.id) // 🔥 使用主键ID

            // 🔥 滚动到顶部，确保用户看到完整的第二步内容
            this.scrollToTop()

          } catch (error) {
            console.error('🎯 CreatorAgentForm: 第一步提交失败:', error)
            this.$message.error('智能体创建失败: ' + (error.message || '未知错误'))
          } finally {
            this.stepLoading = false
          }
        } else {
          this.$message.error('请检查表单信息')
        }
      })
    },

    // 🔥 第二步：上一步按钮
    handlePrev() {
      this.currentStep = 0

      // 🔥 滚动到顶部，确保用户看到完整的第一步内容
      this.scrollToTop()
    },

    // 🔥 第二步：完成按钮（批量保存所有工作流）
    async handleComplete() {
      try {
        console.log('🎯 CreatorAgentForm: 完成创建，开始批量处理工作流')
        this.stepLoading = true

        // 🔥 自动暂存当前表单数据（统一处理，避免重复保存）
        console.log('🎯 CreatorAgentForm: 第一步 - 自动暂存当前表单数据')
        console.log('🎯 CreatorAgentForm: 当前暂存列表长度:', this.tempWorkflowList.length)
        console.log('🎯 CreatorAgentForm: 当前编辑索引:', this.currentWorkflowIndex)
        const saveResult = this.autoSaveCurrentWorkflow()
        console.log('🎯 CreatorAgentForm: 自动暂存结果:', saveResult)
        console.log('🎯 CreatorAgentForm: 暂存后列表长度:', this.tempWorkflowList.length)

        // 检查是否有暂存的工作流需要保存
        if (this.tempWorkflowList.length === 0) {
          console.log('🎯 CreatorAgentForm: 无工作流数据，直接完成')
          this.$message.success('智能体创建完成！')
          this.$emit('complete', this.createdAgent)
          this.handleCancel()
          return
        }

        console.log(`🎯 CreatorAgentForm: 第二步 - 开始验证所有 ${this.tempWorkflowList.length} 个工作流`)

        // 🔥 第二步：验证所有工作流的完整性（包括刚暂存的）
        const validationResult = this.validateAllWorkflows()
        if (!validationResult.isValid) {
          console.error('🎯 CreatorAgentForm: 工作流验证失败:', validationResult.errors)

          // 🔥 第三步：智能错误处理 - 回填第一个有错误的工作流到表单
          this.handleValidationErrors(validationResult)
          this.stepLoading = false
          return
        }

        console.log(`🎯 CreatorAgentForm: 第三步 - 验证通过，开始批量保存 ${this.tempWorkflowList.length} 个工作流`)

        // 批量上传文件和保存工作流
        const savedWorkflows = await this.batchSaveWorkflows()

        this.$message.success(`智能体和 ${savedWorkflows.length} 个工作流创建完成！`)

        // 🔥 进入第三步成功页面，而不是直接关闭弹窗
        this.currentStep = 2
        this.scrollToTop()

        console.log('🎯 CreatorAgentForm: 进入第三步成功页面')

      } catch (error) {
        console.error('🎯 CreatorAgentForm: 完成创建失败:', error)
        this.$message.error('工作流保存失败: ' + (error.message || '未知错误'))
      } finally {
        this.stepLoading = false
      }
    },

    // 🔥 关闭弹窗（第三步完成后）
    handleCloseModal() {
      console.log('🎯 CreatorAgentForm: 用户点击关闭按钮')
      this.$emit('complete', this.createdAgent)
      this.handleCancel()
    },

    // 🔥 批量保存所有暂存的工作流
    async batchSaveWorkflows() {
      const savedWorkflows = []
      const totalCount = this.tempWorkflowList.length

      for (let i = 0; i < this.tempWorkflowList.length; i++) {
        const workflow = this.tempWorkflowList[i]
        console.log(`🎯 CreatorAgentForm: 保存工作流 ${i + 1}/${totalCount}: ${workflow.workflowName}`)

        try {
          // 🔥 区分已保存和新增工作流的处理逻辑
          if (workflow.status === 'saved') {
            // 已保存的工作流：检查是否有实际修改（包括文件修改）
            const originalWorkflow = workflow.originalWorkflow
            const hasTextChanges = originalWorkflow && (
              originalWorkflow.workflowName !== workflow.workflowName ||
              originalWorkflow.workflowDescription !== workflow.workflowDescription ||
              originalWorkflow.inputParamsDesc !== workflow.inputParamsDesc
            )

            // 🔥 检查是否重新选择了文件
            const hasFileChanges = workflow.workflowFile !== null
            const hasChanges = hasTextChanges || hasFileChanges

            if (hasChanges) {
              console.log(`🎯 CreatorAgentForm: 已保存工作流有修改，开始更新: ${workflow.workflowName}`)
              console.log(`🎯 CreatorAgentForm: 文本修改: ${hasTextChanges}, 文件修改: ${hasFileChanges}`)

              let fileUrl = originalWorkflow.workflowPackage // 默认使用原有文件路径

              // 🔥 如果用户重新选择了文件，先上传新文件
              if (hasFileChanges) {
                console.log(`🎯 CreatorAgentForm: 检测到文件变更，上传新文件: ${workflow.workflowFile.name}`)
                fileUrl = await this.uploadWorkflowFile(workflow.workflowFile, workflow.workflowName)
                console.log(`🎯 CreatorAgentForm: 新文件上传成功: ${fileUrl}`)
              }

              // 🔥 调用更新工作流API
              const updateData = {
                agentId: this.createdAgent.id, // 🔥 后端必填字段
                workflowName: workflow.workflowName,
                workflowDescription: workflow.workflowDescription,
                inputParamsDesc: workflow.inputParamsDesc,
                workflowPackage: fileUrl // 🔥 使用新文件路径或原有路径
              }

              console.log('🎯 CreatorAgentForm: 调用工作流更新API:', updateData)
              console.log('🎯 CreatorAgentForm: 更新前的原始数据:', originalWorkflow)
              const response = await updateWorkflow(originalWorkflow.id, updateData)
              console.log('🎯 CreatorAgentForm: 更新API响应:', response)

              if (response.success) {
                const updatedWorkflow = response.result || { ...originalWorkflow, ...updateData }
                savedWorkflows.push(updatedWorkflow)

                // 🔥 同步更新暂存列表中的数据，确保界面显示最新状态
                this.tempWorkflowList[i] = {
                  ...workflow,
                  // 🔥 确保所有字段都同步到最新状态
                  workflowName: updatedWorkflow.workflowName || workflow.workflowName,
                  workflowDescription: updatedWorkflow.workflowDescription || workflow.workflowDescription,
                  inputParamsDesc: updatedWorkflow.inputParamsDesc || workflow.inputParamsDesc,
                  originalWorkflow: updatedWorkflow // 更新原始数据引用
                }

                console.log('🎯 CreatorAgentForm: 暂存列表已同步更新:', this.tempWorkflowList[i])

                console.log(`🎯 CreatorAgentForm: 工作流 ${workflow.workflowName} 更新成功`)
              } else {
                throw new Error(response.message || '工作流更新API调用失败')
              }
            } else {
              console.log(`🎯 CreatorAgentForm: 已保存工作流无修改，跳过保存: ${workflow.workflowName}`)
              savedWorkflows.push(originalWorkflow) // 使用原始数据
            }
          } else {
            // 新增的工作流：需要上传文件并创建
            console.log(`🎯 CreatorAgentForm: 新增工作流，开始上传文件: ${workflow.workflowFile ? workflow.workflowFile.name : 'null'}`)
            const fileUrl = await this.uploadWorkflowFile(workflow.workflowFile, workflow.workflowName)

            // 保存工作流数据
            const workflowData = {
              agentId: this.createdAgent.id,
              workflowName: workflow.workflowName,
              workflowDescription: workflow.workflowDescription,
              inputParamsDesc: workflow.inputParamsDesc, // 🔥 前端验证确保不为空
              workflowPackage: fileUrl
            }

            // 🔥 调用工作流创建API
            console.log('🎯 CreatorAgentForm: 调用工作流创建API:', workflowData)
            const response = await createWorkflow(workflowData)

            if (response.success) {
              savedWorkflows.push(response.result)
              console.log(`🎯 CreatorAgentForm: 工作流 ${workflow.workflowName} API调用成功`)
            } else {
              throw new Error(response.message || '工作流创建API调用失败')
            }
          }

          console.log(`🎯 CreatorAgentForm: 工作流 ${workflow.workflowName} 保存成功`)

        } catch (error) {
          console.error(`🎯 CreatorAgentForm: 工作流 ${workflow.workflowName} 保存失败:`, error)
          throw new Error(`工作流"${workflow.workflowName}"保存失败: ${error.message}`)
        }
      }

      return savedWorkflows
    },

    // 🔥 上传单个工作流文件
    async uploadWorkflowFile(file, workflowName) {
      return new Promise((resolve, reject) => {
        const formData = new FormData()
        formData.append('file', file)
        formData.append('isup', '1')
        formData.append('biz', '') // 工作流文件使用空的biz

        // 生成重命名后的文件名
        const renamedFileName = this.generateWorkflowFileName(file.name, workflowName)

        // 使用fetch进行文件上传
        fetch(this.uploadAction, {
          method: 'POST',
          headers: this.uploadHeaders,
          body: formData
        })
        .then(response => response.json())
        .then(result => {
          if (result.success) {
            console.log(`🎯 CreatorAgentForm: 文件上传成功: ${file.name} -> ${result.message}`)
            resolve(result.message)
          } else {
            reject(new Error(result.message || '文件上传失败'))
          }
        })
        .catch(error => {
          console.error(`🎯 CreatorAgentForm: 文件上传失败:`, error)
          reject(error)
        })
      })
    },

    // 🔥 创建智能体（第一步）
    async createAgentStep(agentData) {
      try {
        console.log('🎯 CreatorAgentForm: 调用创建智能体API...', agentData)
        const response = await createAgent(agentData)

        if (response.success) {
          console.log('🎯 CreatorAgentForm: 智能体创建成功', response.result)

          // 🔥 确认删除被替换的原始头像文件
          this.confirmDeleteOriginalFiles()

          return response.result
        } else {
          throw new Error(response.message || '创建智能体失败')
        }
      } catch (error) {
        console.error('🎯 CreatorAgentForm: 创建智能体失败:', error)
        throw error
      }
    },

    // 🔥 更新智能体（编辑模式第一步）
    async updateAgentStep(agentData) {
      try {
        console.log('🎯 CreatorAgentForm: 调用更新智能体API...', agentData)

        // 获取智能体ID
        const agentId = this.agent.id || this.agent.agentId
        if (!agentId) {
          throw new Error('智能体ID不存在')
        }

        const response = await updateAgent(agentId, agentData)

        if (response.success) {
          console.log('🎯 CreatorAgentForm: 智能体更新成功', response.result)

          // 🔥 确认删除被替换的原始头像文件
          this.confirmDeleteOriginalFiles()

          return response.result
        } else {
          throw new Error(response.message || '更新智能体失败')
        }
      } catch (error) {
        console.error('🎯 CreatorAgentForm: 更新智能体失败:', error)
        throw error
      }
    },

    // 🔥 处理头像值，确保返回字符串格式
    processAvatarValue(avatarValue) {
      console.log('🎯 CreatorAgentForm: 处理头像值 - 原始值:', avatarValue, typeof avatarValue)

      // 如果是空值，返回空字符串
      if (!avatarValue) {
        return ''
      }

      // 如果是数组，取第一个元素
      if (Array.isArray(avatarValue)) {
        console.log('🎯 CreatorAgentForm: 头像值是数组，取第一个元素:', avatarValue[0])
        return this.processAvatarValue(avatarValue[0]) // 递归处理
      }

      // 如果是对象，尝试获取url字段
      if (typeof avatarValue === 'object' && avatarValue.url) {
        console.log('🎯 CreatorAgentForm: 头像值是对象，取url字段:', avatarValue.url)
        return this.processAvatarValue(avatarValue.url) // 递归处理
      }

      // 如果是字符串，处理URL
      if (typeof avatarValue === 'string') {
        return this.extractRelativePath(avatarValue)
      }

      // 其他情况，转换为字符串
      console.log('🎯 CreatorAgentForm: 头像值转换为字符串:', String(avatarValue))
      return String(avatarValue)
    },

    // 🔥 提取相对路径，避免TOS URL双重包装
    extractRelativePath(url) {
      console.log('🎯 CreatorAgentForm: 提取相对路径 - 输入URL:', url)

      // 如果是完整的TOS URL，提取相对路径
      if (url.includes('aigcview-tos.tos-cn-shanghai.volces.com/')) {
        // 提取 uploads/ 开头的路径
        const match = url.match(/uploads\/[^?]+/)
        if (match) {
          const relativePath = match[0]
          console.log('🎯 CreatorAgentForm: 从TOS URL提取相对路径:', relativePath)
          return relativePath
        }
      }

      // 如果是CDN URL，提取相对路径
      if (url.includes('cdn.aigcview.com/')) {
        const match = url.match(/uploads\/[^?]+/)
        if (match) {
          const relativePath = match[0]
          console.log('🎯 CreatorAgentForm: 从CDN URL提取相对路径:', relativePath)
          return relativePath
        }
      }

      // 如果已经是相对路径（以uploads/开头），直接返回
      if (url.startsWith('uploads/')) {
        console.log('🎯 CreatorAgentForm: 已经是相对路径:', url)
        return url
      }

      // 其他情况，直接返回原值
      console.log('🎯 CreatorAgentForm: 无法识别的URL格式，返回原值:', url)
      return url
    },

    // 🔥 获取完整的头像URL（用于显示）
    getFullAvatarUrl(avatarPath) {
      if (!avatarPath) {
        return ''
      }

      // 如果已经是完整URL，直接返回
      if (avatarPath.startsWith('http')) {
        return avatarPath
      }

      // 如果是相对路径，转换为CDN URL
      if (avatarPath.startsWith('uploads/')) {
        const cdnUrl = `https://cdn.aigcview.com/${avatarPath}`
        console.log('🎯 CreatorAgentForm: 转换头像URL:', avatarPath, '->', cdnUrl)
        return cdnUrl
      }

      // 其他情况，尝试拼接CDN前缀
      const cdnUrl = `https://cdn.aigcview.com/${avatarPath}`
      console.log('🎯 CreatorAgentForm: 拼接头像URL:', avatarPath, '->', cdnUrl)
      return cdnUrl
    },

    // 提交表单（保留原方法，但在分步模式下不使用）
    async handleSubmit() {
      console.log('🎯 CreatorAgentForm: 开始提交智能体表单...')

      // 🔥 先进行表单验证，验证通过后再上传头像
      // 🔥 验证前检查头像：如果当前值为空但有待上传文件，则临时设置一个值通过验证
      const originalAvatar = this.formData.agentAvatar
      const hasPendingAvatar = this.$refs.avatarUpload && this.$refs.avatarUpload.hasPendingFiles()

      if (!this.formData.agentAvatar && hasPendingAvatar) {
        console.log('🎯 CreatorAgentForm: 检测到待上传头像，临时设置头像值以通过验证')
        this.formData.agentAvatar = 'pending_upload' // 临时值
      }

      // 表单验证
      this.$refs.form.validate(async (valid) => {
        // 恢复原始值
        this.formData.agentAvatar = originalAvatar

        if (valid) {
          console.log('🎯 CreatorAgentForm: 表单验证通过，开始上传头像...')

          try {
            // 🔥 表单验证通过后才上传头像
            await this.uploadPendingImages()

            // 创建提交数据，只包含需要的字段
            const submitData = {
              agentName: this.formData.agentName.trim(),
              agentDescription: this.formData.agentDescription.trim(),
              agentAvatar: this.processAvatarValue(this.formData.agentAvatar),
              experienceLink: this.formData.experienceLink.trim(),
              price: this.formData.price
            }

            console.log('🎯 CreatorAgentForm: 头像上传成功，提交数据:', submitData)
            this.$emit('submit', submitData)

          } catch (error) {
            console.error('🎯 CreatorAgentForm: 头像上传失败:', error)
            this.$message.error('头像上传失败: ' + (error.message || '未知错误'))
          }
        } else {
          console.log('🎯 CreatorAgentForm: 表单验证失败，不上传头像')
          this.$message.error('请检查表单信息')
        }
      })
    },

    // 取消
    handleCancel() {
      console.log('🎯 CreatorAgentForm: 用户取消，执行清理操作...')

      // 🔥 回滚头像变更
      this.rollbackChanges()

      // 🔥 清空所有工作流数据，避免数据污染
      this.clearAllWorkflowData()

      // 🔥 重置表单数据
      this.resetForm()

      console.log('🎯 CreatorAgentForm: 所有数据已清理完成')
      this.$emit('close')
    },

    // 🔥 上传待处理的图片（与后台管理系统逻辑一致）
    async uploadPendingImages() {
      console.log('🎯 CreatorAgentForm: 开始上传待处理的头像...')

      if (this.$refs.avatarUpload && this.$refs.avatarUpload.hasPendingFiles()) {
        console.log('🎯 CreatorAgentForm: 检测到待上传头像，开始上传...')

        try {
          // 执行上传并获取最终的URL
          const finalUrl = await this.$refs.avatarUpload.performUpload()
          console.log('🎯 CreatorAgentForm: 头像上传成功，URL:', finalUrl)

          // 更新formData中的头像URL
          this.formData.agentAvatar = finalUrl

          return finalUrl
        } catch (error) {
          console.error('🎯 CreatorAgentForm: 头像上传失败:', error)
          throw new Error('头像上传失败: ' + (error.message || '未知错误'))
        }
      } else {
        console.log('🎯 CreatorAgentForm: 没有待上传的头像')
        return this.formData.agentAvatar
      }
    },

    // 🔥 确认删除原始文件（与后台管理系统逻辑一致）
    confirmDeleteOriginalFiles() {
      console.log('🎯 CreatorAgentForm: 确认删除被替换的原始头像文件...')

      if (this.$refs.avatarUpload && this.$refs.avatarUpload.confirmDeleteOriginalFiles) {
        this.$refs.avatarUpload.confirmDeleteOriginalFiles()
        console.log('🎯 CreatorAgentForm: 原始头像文件删除确认完成')
      }
    },

    // 🔥 回滚变更（与后台管理系统逻辑一致）
    rollbackChanges() {
      console.log('🎯 CreatorAgentForm: 回滚头像变更...')

      if (this.$refs.avatarUpload && this.$refs.avatarUpload.rollbackChanges) {
        this.$refs.avatarUpload.rollbackChanges()
        console.log('🎯 CreatorAgentForm: 头像变更回滚完成')
      }
    },

    // 🔥 加载工作流列表
    async loadWorkflowList(agentId) {
      if (!agentId) return

      this.workflowLoading = true
      try {
        console.log('🎯 CreatorAgentForm: 加载工作流列表...', agentId)

        // 🔥 调用获取工作流列表的API
        const response = await getWorkflowList(agentId)

        if (response.success) {
          this.workflowList = response.result || []
          console.log('🎯 CreatorAgentForm: API返回工作流数据:', this.workflowList)

          // 🔥 编辑模式下：将现有工作流数据回填到暂存区域
          if (this.mode === 'edit' && this.workflowList.length > 0) {
            this.convertWorkflowsToTempList(this.workflowList)
            console.log('🎯 CreatorAgentForm: 编辑模式 - 工作流数据已回填到暂存区域')
          } else if (this.mode === 'edit') {
            console.log('🎯 CreatorAgentForm: 编辑模式 - 该智能体暂无工作流数据')
          }
        } else {
          console.error('🎯 CreatorAgentForm: API返回失败:', response.message)
          this.$message.error(response.message || '获取工作流列表失败')
          this.workflowList = []
        }

        console.log('🎯 CreatorAgentForm: 工作流列表加载完成，共', this.workflowList.length, '个工作流')
      } catch (error) {
        console.error('🎯 CreatorAgentForm: 加载工作流列表失败:', error)
        this.$message.error('加载工作流列表失败: ' + (error.message || '网络错误'))
        this.workflowList = []
      } finally {
        this.workflowLoading = false
      }
    },

    // 🔥 将现有工作流数据转换为暂存格式（编辑模式回填）
    convertWorkflowsToTempList(workflows) {
      console.log('🎯 CreatorAgentForm: 开始转换工作流数据到暂存区域')
      console.log('🎯 CreatorAgentForm: 原始工作流数据:', workflows)

      // 清空现有暂存数据
      this.tempWorkflowList = []

      // 转换每个工作流为暂存格式
      workflows.forEach((workflow, index) => {
        console.log(`🎯 CreatorAgentForm: 处理工作流 ${index + 1}:`, {
          id: workflow.id,
          workflowName: workflow.workflowName,
          workflowDescription: workflow.workflowDescription,
          inputParamsDesc: workflow.inputParamsDesc,
          workflowPackage: workflow.workflowPackage,
          createTime: workflow.createTime
        })

        // 🔥 处理已保存工作流的文件信息
        const packageFileName = workflow.workflowPackage || ''
        const displayFileName = packageFileName ?
          (packageFileName.includes('/') ? packageFileName.split('/').pop() : packageFileName) :
          '已上传文件'

        const tempWorkflow = {
          id: workflow.id || Date.now() + index,
          workflowName: workflow.workflowName || '',
          workflowDescription: workflow.workflowDescription || '',
          inputParamsDesc: workflow.inputParamsDesc || '', // 🔥 确保包含输入参数说明
          workflowFile: null, // 已保存的工作流没有文件对象
          fileName: displayFileName, // 🔥 显示文件名而不是完整路径
          fileSize: 0, // 已保存的工作流无法获取文件大小
          workflowPackage: workflow.workflowPackage || '', // 🔥 保存完整的包路径
          status: 'saved', // 标记为已保存状态
          createTime: new Date(workflow.createTime || Date.now()),
          originalWorkflow: workflow // 保存原始工作流数据引用
        }

        this.tempWorkflowList.push(tempWorkflow)
        console.log(`🎯 CreatorAgentForm: 工作流 "${workflow.workflowName}" 已转换为暂存格式:`, tempWorkflow)
      })

      console.log(`🎯 CreatorAgentForm: 共转换 ${workflows.length} 个工作流到暂存区域`)
      console.log('🎯 CreatorAgentForm: 最终暂存列表:', this.tempWorkflowList)
    },

    // 🔥 工作流文件上传前安全检查
    beforeWorkflowUpload(file) {
      console.log('🎯 CreatorAgentForm: 工作流文件安全检查开始:', file.name)

      // 🛡️ 1. 文件名安全检查
      if (!this.isSecureFileName(file.name)) {
        this.$message.error('文件名包含不安全字符，请重命名后重试!')
        return false
      }

      // 🛡️ 2. 严格检查文件扩展名（只允许.zip）
      const fileName = file.name.toLowerCase()
      if (!fileName.endsWith('.zip')) {
        this.$message.error('只能上传 .zip 格式的文件!')
        return false
      }

      // 🛡️ 3. 严格检查MIME类型
      const allowedMimeTypes = [
        'application/zip',
        'application/x-zip-compressed',
        'application/x-zip'
      ]
      if (!allowedMimeTypes.includes(file.type)) {
        this.$message.error('文件类型不正确，只允许上传ZIP压缩包!')
        return false
      }

      // 🛡️ 4. 检查文件大小 (5MB)
      const maxSize = 5 * 1024 * 1024 // 5MB
      if (file.size > maxSize) {
        this.$message.error('文件大小不能超过 5MB!')
        return false
      }

      // 🛡️ 5. 检查文件大小不能为0
      if (file.size === 0) {
        this.$message.error('文件不能为空!')
        return false
      }

      console.log('🎯 CreatorAgentForm: 工作流文件安全检查通过')
      return false // 阻止默认上传，使用延迟上传机制
    },

    // 🛡️ 文件名安全检查
    isSecureFileName(fileName) {
      // 检查危险字符
      const dangerousChars = /[<>:"|?*\x00-\x1f]/
      if (dangerousChars.test(fileName)) {
        return false
      }

      // 检查路径遍历
      if (fileName.includes('..') || fileName.includes('./') || fileName.includes('.\\')) {
        return false
      }

      // 检查保留名称（Windows）
      const reservedNames = /^(CON|PRN|AUX|NUL|COM[1-9]|LPT[1-9])(\.|$)/i
      if (reservedNames.test(fileName)) {
        return false
      }

      // 检查文件名长度
      if (fileName.length > 255) {
        return false
      }

      return true
    },

    // 🛡️ 验证工作流数据安全性
    validateWorkflowData() {
      const { workflowName, workflowDescription, inputParamsDesc } = this.workflowFormData

      // 检查工作流名称
      if (!workflowName || workflowName.trim().length === 0) {
        this.$message.error('工作流名称不能为空')
        return false
      }

      // 检查名称中的危险字符
      const namePattern = /^[a-zA-Z0-9\u4e00-\u9fa5\s\-_]+$/
      if (!namePattern.test(workflowName)) {
        this.$message.error('工作流名称包含不允许的字符')
        return false
      }

      // 检查工作流描述
      if (!workflowDescription || workflowDescription.trim().length === 0) {
        this.$message.error('工作流描述不能为空')
        return false
      }

      // 检查描述长度
      if (workflowDescription.length < 10 || workflowDescription.length > 200) {
        this.$message.error('工作流描述长度必须在10-200字符之间')
        return false
      }

      // 检查是否包含潜在的脚本注入
      const scriptPattern = /<script|javascript:|on\w+\s*=/i
      if (scriptPattern.test(workflowName) || scriptPattern.test(workflowDescription) || scriptPattern.test(inputParamsDesc)) {
        this.$message.error('输入内容包含不安全的脚本代码')
        return false
      }

      return true
    },

    // 🔥 工作流文件选择处理（延迟上传机制）
    handleWorkflowFileSelect(info) {
      console.log('🎯 CreatorAgentForm: 工作流文件选择:', info.file ? info.file.name : 'no file', 'status:', info.file ? info.file.status : 'no status')

      // 处理文件选择（不进行实际上传）
      if (info.fileList && info.fileList.length > 0) {
        const file = info.fileList[0].originFileObj || info.fileList[0]

        // 🛡️ 文件大小验证（5MB限制）
        const maxSize = 5 * 1024 * 1024 // 5MB
        if (file.size > maxSize) {
          console.error('🎯 CreatorAgentForm: 文件大小超过限制:', file.size, '字节')
          this.$message.error('文件大小不能超过 5MB!')

          // 🔥 立即清理文件状态，不保存任何信息
          this.workflowFileList = []
          this.workflowFileInfo = null

          // 强制清空上传组件的文件列表
          this.$nextTick(() => {
            // 通过ref清空上传组件（如果有ref的话）
            const uploadComponent = this.$refs.workflowUpload
            if (uploadComponent) {
              uploadComponent.fileList = []
            }
          })

          console.log('🎯 CreatorAgentForm: 文件已清理，不保存超大文件')
          return // 直接返回，不执行后续逻辑
        }

        // 🛡️ 文件大小不能为0
        if (file.size === 0) {
          console.error('🎯 CreatorAgentForm: 文件大小为0')
          this.$message.error('文件不能为空!')

          // 清理文件状态
          this.workflowFileList = []
          this.workflowFileInfo = null
          return
        }

        // 🛡️ 文件名安全检查
        if (!this.isSecureFileName(file.name)) {
          console.error('🎯 CreatorAgentForm: 文件名不安全:', file.name)
          this.$message.error('文件名包含不安全字符，请重命名后重试!')

          // 清理文件状态
          this.workflowFileList = []
          this.workflowFileInfo = null
          return
        }

        // 🛡️ 文件类型检查（扩展名）
        const fileName = file.name.toLowerCase()
        if (!fileName.endsWith('.zip')) {
          console.error('🎯 CreatorAgentForm: 文件扩展名不正确:', file.name)
          this.$message.error('只能上传 .zip 格式的文件!')

          // 清理文件状态
          this.workflowFileList = []
          this.workflowFileInfo = null
          return
        }

        // 🛡️ 文件类型检查（MIME类型）
        const allowedMimeTypes = [
          'application/zip',
          'application/x-zip-compressed',
          'application/x-zip'
        ]
        if (!allowedMimeTypes.includes(file.type)) {
          console.error('🎯 CreatorAgentForm: 文件MIME类型不正确:', file.type)
          this.$message.error('文件类型不正确，只允许上传ZIP压缩包!')

          // 清理文件状态
          this.workflowFileList = []
          this.workflowFileInfo = null
          return
        }

        // ✅ 所有验证通过，保存文件信息
        this.workflowFileList = [file]
        this.workflowFileInfo = {
          name: file.name,
          originalName: file.name,
          size: file.size,
          file: file, // 保存原始File对象
          isSaved: false // 🔥 新选择的文件标记为未保存状态
        }

        console.log('🎯 CreatorAgentForm: 工作流文件已选择，等待最终上传:', file.name)
        this.$message.success('工作流文件已选择，将在完成创建时上传')

      } else if (info.fileList && info.fileList.length === 0) {
        // 文件被移除
        this.workflowFileList = []
        this.workflowFileInfo = null
        console.log('🎯 CreatorAgentForm: 工作流文件已移除')
      }
    },

    // 🔥 生成工作流文件名（与后台管理系统一致的重命名逻辑）
    generateWorkflowFileName(originalName, customWorkflowName = null) {
      // 获取文件扩展名
      const lastDotIndex = originalName.lastIndexOf('.')
      const extension = lastDotIndex > -1 ? originalName.substring(lastDotIndex) : ''

      // 生成时间戳
      const timestamp = new Date().getTime()

      // 生成随机数
      const random = Math.floor(Math.random() * 10000).toString().padStart(4, '0')

      // 智能体名称（清理特殊字符）
      const agentName = this.formData.agentName
        ? this.formData.agentName.replace(/[^a-zA-Z0-9\u4e00-\u9fa5]/g, '_').substring(0, 20)
        : 'workflow'

      // 工作流名称（清理特殊字符）
      const workflowName = (customWorkflowName || this.workflowFormData.workflowName)
        ? (customWorkflowName || this.workflowFormData.workflowName).replace(/[^a-zA-Z0-9\u4e00-\u9fa5]/g, '_').substring(0, 20)
        : 'default'

      // 生成新文件名：智能体名称_工作流名称_时间戳_随机数.扩展名
      const newFileName = `${agentName}_${workflowName}_${timestamp}_${random}${extension}`

      console.log('🎯 CreatorAgentForm: 文件重命名:', originalName, '->', newFileName)
      return newFileName
    },

    // 🔥 移除工作流文件
    handleWorkflowRemove(file) {
      console.log('🎯 CreatorAgentForm: 移除工作流文件:', file.name)
      this.workflowFileList = []
      this.workflowFormData.workflowPackage = ''
      this.workflowFileInfo = null
    },

    // 🔥 移除工作流文件（用于文件信息显示区域）
    handleRemoveWorkflowFile() {
      console.log('🎯 CreatorAgentForm: 移除工作流文件')
      this.workflowFileList = []
      this.workflowFormData.workflowPackage = ''
      this.workflowFileInfo = null
      this.$message.success('工作流文件已移除')
    },

    // 🔥 格式化文件大小
    formatFileSize(bytes) {
      if (!bytes) return '0 B'
      const k = 1024
      const sizes = ['B', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    },

    // 🔥 提交工作流表单
    handleWorkflowSubmit() {
      return new Promise((resolve, reject) => {
        // 🛡️ 1. 验证表单（Vue 2 + Ant Design Vue 方式）
        this.$refs.workflowFormRef.validate((valid) => {
          if (!valid) {
            this.$message.error('请完善表单信息')
            reject(new Error('表单验证失败'))
            return
          }

          this.performWorkflowSubmit().then(resolve).catch(reject)
        })
      })
    },

    // 🔥 执行工作流提交
    async performWorkflowSubmit() {
      try {
        // 🛡️ 2. 二次验证文件
        if (this.workflowFileList.length === 0) {
          this.$message.error('请选择工作流文件')
          throw new Error('未选择工作流文件')
        }

        const file = this.workflowFileList[0]

        // 🛡️ 3. 提交前再次安全检查
        if (!this.isSecureFileName(file.name)) {
          this.$message.error('文件名不安全，请重新选择文件')
          throw new Error('文件名不安全')
        }

        if (!file.name.toLowerCase().endsWith('.zip')) {
          this.$message.error('只能提交ZIP格式的文件')
          throw new Error('文件格式不正确')
        }

        if (file.size > 5 * 1024 * 1024) {
          this.$message.error('文件大小超过5MB限制')
          throw new Error('文件大小超限')
        }

        // 🛡️ 4. 验证表单数据安全性
        if (!this.validateWorkflowData()) {
          throw new Error('表单数据验证失败')
        }

        this.stepLoading = true
        console.log('🎯 CreatorAgentForm: 提交工作流表单:', this.workflowFormData)

        // TODO: 调用工作流创建API
        // const workflowData = {
        //   agentId: this.createdAgent.id,
        //   workflowName: this.workflowFormData.workflowName,
        //   workflowDescription: this.workflowFormData.workflowDescription,
        //   workflowFile: this.workflowFileList[0]
        // }
        // const response = await createWorkflow(workflowData)

        // 模拟成功
        await new Promise(resolve => setTimeout(resolve, 1000))

        this.$message.success('工作流创建成功!')

        // 重新加载工作流列表
        this.loadWorkflowList(this.createdAgent.id)

        // 重置表单
        this.resetWorkflowForm()

        return true
      } catch (error) {
        console.error('🎯 CreatorAgentForm: 工作流创建失败:', error)
        this.$message.error('工作流创建失败: ' + (error.message || '未知错误'))
        throw error
      } finally {
        this.stepLoading = false
      }
    },

    // 🔥 重置工作流表单
    resetWorkflowForm() {
      this.workflowFormData = {
        workflowName: '',
        workflowDescription: '',
        inputParamsDesc: '',
        workflowPackage: ''
      }
      this.workflowFileList = []
      this.workflowFileInfo = null
      this.currentWorkflowIndex = -1

      // Vue 2 + Ant Design Vue 方式重置表单
      this.$nextTick(() => {
        if (this.$refs.workflowFormRef) {
          this.$refs.workflowFormRef.resetFields()
        }
      })

      console.log('🎯 CreatorAgentForm: 工作流表单已重置')
    },

    // 🔥 清空所有工作流数据（解决数据污染问题）
    clearAllWorkflowData() {
      // 清空暂存工作流列表
      this.tempWorkflowList = []

      // 重置工作流表单
      this.resetWorkflowForm()

      // 重置工作流相关状态
      this.workflowFileList = []
      this.workflowFileInfo = null
      this.currentWorkflowIndex = -1

      // 🔥 清空工作流验证错误状态
      this.workflowValidationErrors = {}

      // 🔥 重置工作流上传状态
      this.workflowUploading = false

      // 🔥 清空已保存的工作流列表
      this.workflowList = []
      this.workflowLoading = false

      console.log('🎯 CreatorAgentForm: 所有工作流数据已清空')
    },

    // 🔥 处理输入参数说明失焦事件，手动触发验证
    handleInputParamsBlur() {
      console.log('🎯 CreatorAgentForm: 输入参数说明失焦，触发验证')
      this.$nextTick(() => {
        if (this.$refs.workflowFormRef) {
          this.$refs.workflowFormRef.validateField('inputParamsDesc', (errorMessage) => {
            if (errorMessage) {
              console.log('🎯 CreatorAgentForm: 输入参数说明验证失败:', errorMessage)
            } else {
              console.log('🎯 CreatorAgentForm: 输入参数说明验证通过')
            }
          })
        }
      })
    },

    // 🔥 滚动弹窗到顶部（提升用户体验）
    scrollToTop() {
      this.$nextTick(() => {
        console.log('🎯 CreatorAgentForm: 开始查找滚动容器...')

        // 🔥 优先查找步骤内容容器（真正的滚动容器）
        const stepContent = document.querySelector('.step-content')

        if (stepContent) {
          console.log('🎯 CreatorAgentForm: 找到步骤内容容器:', {
            scrollTop: stepContent.scrollTop,
            scrollHeight: stepContent.scrollHeight,
            clientHeight: stepContent.clientHeight
          })

          const beforeScrollTop = stepContent.scrollTop
          stepContent.scrollTop = 0
          const afterScrollTop = stepContent.scrollTop

          console.log(`🎯 CreatorAgentForm: 步骤内容滚动 - 滚动前: ${beforeScrollTop}, 滚动后: ${afterScrollTop}`)

          if (beforeScrollTop !== afterScrollTop || beforeScrollTop === 0) {
            console.log('🎯 CreatorAgentForm: 步骤内容滚动成功')
            return
          }
        }

        // 🔥 备用方案：尝试其他可能的滚动容器
        const selectors = [
          '.creator-agent-modal .ant-modal-body',
          '.ant-modal-body',
          '.creator-agent-modal .ant-modal-content',
          '.ant-modal-content',
          '.creator-agent-modal',
          '.ant-modal-wrap'
        ]

        let scrollContainer = null
        let usedSelector = ''

        for (const selector of selectors) {
          const element = document.querySelector(selector)
          if (element) {
            console.log(`🎯 CreatorAgentForm: 找到元素 ${selector}:`, element)
            console.log(`🎯 CreatorAgentForm: 元素当前 scrollTop: ${element.scrollTop}, scrollHeight: ${element.scrollHeight}, clientHeight: ${element.clientHeight}`)

            // 检查元素是否可滚动
            if (element.scrollHeight > element.clientHeight || element.scrollTop > 0) {
              scrollContainer = element
              usedSelector = selector
              break
            }
          }
        }

        if (scrollContainer) {
          const beforeScrollTop = scrollContainer.scrollTop
          scrollContainer.scrollTop = 0
          const afterScrollTop = scrollContainer.scrollTop

          console.log(`🎯 CreatorAgentForm: 使用选择器 ${usedSelector} 滚动到顶部`)
          console.log(`🎯 CreatorAgentForm: 滚动前: ${beforeScrollTop}, 滚动后: ${afterScrollTop}`)

          if (beforeScrollTop === afterScrollTop && beforeScrollTop > 0) {
            console.warn('🎯 CreatorAgentForm: 滚动可能没有生效，尝试其他方法')
            // 尝试平滑滚动
            scrollContainer.scrollTo({ top: 0, behavior: 'smooth' })
          }
        } else {
          console.warn('🎯 CreatorAgentForm: 未找到可滚动的容器，延迟重试')
          // 🔥 延迟重试，并尝试更多选择器
          setTimeout(() => {
            const allModalElements = document.querySelectorAll('[class*="modal"]')
            console.log('🎯 CreatorAgentForm: 所有modal相关元素:', allModalElements)

            for (const element of allModalElements) {
              if (element.scrollHeight > element.clientHeight || element.scrollTop > 0) {
                element.scrollTop = 0
                console.log('🎯 CreatorAgentForm: 延迟重试成功，使用元素:', element)
                return
              }
            }

            console.error('🎯 CreatorAgentForm: 延迟重试仍未找到可滚动容器')
          }, 200)
        }
      })
    },

    // 🔥 新增下一个工作流（前端暂存机制）
    addNextWorkflow() {
      console.log('🎯 CreatorAgentForm: 新增下一个工作流')

      // 🔥 自动暂存当前数据（智能保护机制）
      this.autoSaveCurrentWorkflow()

      // 重置表单，准备添加新工作流
      this.resetWorkflowForm()
      this.currentWorkflowIndex = -1 // 设置为新建状态

      // 🔥 滚动到顶部，确保用户看到新工作流表单的开始
      this.scrollToTop()

      this.$message.success('可以继续添加新工作流')
    },

    // 🔥 验证当前工作流数据
    validateCurrentWorkflowData() {
      // 检查工作流名称
      if (!this.workflowFormData.workflowName.trim()) {
        this.$message.error('请输入工作流名称')
        return false
      }

      // 🔥 严格检查工作流名称长度（防止数据库字段溢出）
      if (this.workflowFormData.workflowName.trim().length > 30) {
        this.$message.error('工作流名称不能超过30个字符')
        return false
      }

      // 检查工作流描述
      if (!this.workflowFormData.workflowDescription.trim()) {
        this.$message.error('请输入工作流描述')
        return false
      }

      // 🔥 严格检查工作流描述长度
      if (this.workflowFormData.workflowDescription.trim().length > 200) {
        this.$message.error('工作流描述不能超过200个字符')
        return false
      }

      // 🔥 检查输入参数说明（后端必填字段）
      if (!this.workflowFormData.inputParamsDesc.trim()) {
        this.$message.error('请输入参数说明')
        return false
      }

      // 🔥 检查输入参数说明格式（与后台管理一致的正则验证）
      const inputParamsPattern = /^[^:：]+[:：](?:"[^"]*"|'[^']*'|[^:：,，]+)(?:[,，][^:：]+[:：](?:"[^"]*"|'[^']*'|[^:：,，]+))*$/
      if (!inputParamsPattern.test(this.workflowFormData.inputParamsDesc.trim())) {
        this.$message.error('请按照格式填写：参数:值 或 参数:"值" 或 参数:\'值\'')
        return false
      }

      // 🔥 检查工作流文件（区分新增和已保存工作流）
      console.log('🎯 CreatorAgentForm: 文件验证 - workflowFileList.length:', this.workflowFileList.length)
      console.log('🎯 CreatorAgentForm: 文件验证 - workflowFileInfo:', this.workflowFileInfo)
      console.log('🎯 CreatorAgentForm: 文件验证 - workflowFileInfo.isSaved:', this.workflowFileInfo && this.workflowFileInfo.isSaved)

      // 🔥 文件验证逻辑：
      // 1. 如果有新选择的文件（workflowFileList.length > 0），验证通过
      // 2. 如果没有新文件，但有已保存的文件信息（workflowFileInfo.isSaved），验证通过
      // 3. 其他情况验证失败
      const hasNewFile = this.workflowFileList.length > 0
      const hasSavedFile = this.workflowFileInfo && this.workflowFileInfo.isSaved

      if (!hasNewFile && !hasSavedFile) {
        this.$message.error('工作流文件为必填项，请上传ZIP压缩包')
        return false
      }

      return true
    },

    // 🔥 保存当前工作流到暂存列表（支持任意字段的暂存）
    saveCurrentWorkflowToTemp() {
      // 🔥 生成默认工作流名称（如果用户没有输入）
      let workflowName = this.workflowFormData.workflowName.trim()
      if (!workflowName) {
        // 如果没有名称，生成一个默认名称
        const timestamp = new Date().toLocaleString('zh-CN')
        workflowName = `工作流_${timestamp}`
      }

      // 🔥 智能处理工作流状态和文件信息
      const isEditingExisting = this.currentWorkflowIndex >= 0
      const existingWorkflow = isEditingExisting ? this.tempWorkflowList[this.currentWorkflowIndex] : null

      console.log('🎯 CreatorAgentForm: saveCurrentWorkflowToTemp - isEditingExisting:', isEditingExisting)
      console.log('🎯 CreatorAgentForm: saveCurrentWorkflowToTemp - existingWorkflow:', existingWorkflow)
      console.log('🎯 CreatorAgentForm: saveCurrentWorkflowToTemp - existingWorkflow.status:', existingWorkflow ? existingWorkflow.status : 'null')

      // 🔥 保持已保存工作流的状态和文件信息
      const workflowData = {
        id: isEditingExisting ? existingWorkflow.id : Date.now(),
        workflowName: workflowName,
        workflowDescription: this.workflowFormData.workflowDescription.trim(),
        inputParamsDesc: this.workflowFormData.inputParamsDesc.trim(),
        workflowFile: this.workflowFileList.length > 0 ? this.workflowFileList[0] : (existingWorkflow ? existingWorkflow.workflowFile : null),
        fileName: this.workflowFileInfo ? this.workflowFileInfo.name : (existingWorkflow ? existingWorkflow.fileName : ''),
        fileSize: this.workflowFileInfo ? this.workflowFileInfo.size : (existingWorkflow ? existingWorkflow.fileSize : 0),
        // 🔥 保持已保存工作流的状态，新工作流设为draft
        status: (existingWorkflow && existingWorkflow.status === 'saved') ? 'saved' : 'draft',
        createTime: isEditingExisting ? existingWorkflow.createTime : new Date(),
        // 🔥 保持已保存工作流的包路径信息
        workflowPackage: existingWorkflow ? existingWorkflow.workflowPackage : '',
        originalWorkflow: existingWorkflow ? existingWorkflow.originalWorkflow : null
      }

      console.log('🎯 CreatorAgentForm: saveCurrentWorkflowToTemp - 创建的workflowData:', workflowData)
      console.log('🎯 CreatorAgentForm: saveCurrentWorkflowToTemp - workflowData.status:', workflowData.status)
      console.log('🎯 CreatorAgentForm: saveCurrentWorkflowToTemp - workflowData.workflowPackage:', workflowData.workflowPackage)

      if (this.currentWorkflowIndex >= 0) {
        // 更新现有工作流
        console.log('🎯 CreatorAgentForm: 更新现有工作流，索引:', this.currentWorkflowIndex)
        this.tempWorkflowList.splice(this.currentWorkflowIndex, 1, workflowData)
        console.log('🎯 CreatorAgentForm: 更新暂存工作流:', workflowData)
      } else {
        // 添加新工作流
        console.log('🎯 CreatorAgentForm: 添加新工作流，当前列表长度:', this.tempWorkflowList.length)
        this.tempWorkflowList.push(workflowData)
        console.log('🎯 CreatorAgentForm: 添加暂存工作流:', workflowData)
        console.log('🎯 CreatorAgentForm: 添加后列表长度:', this.tempWorkflowList.length)
      }

      // 🔥 实时更新验证状态
      this.updateWorkflowValidation(workflowData)
    },

    // 🔥 从暂存列表加载工作流数据到表单（带自动暂存保护）
    loadWorkflowFromTemp(index, skipAutoSave = false) {
      if (index < 0 || index >= this.tempWorkflowList.length) {
        console.error('🎯 CreatorAgentForm: 无效的工作流索引:', index)
        return
      }

      // 🔥 自动暂存当前正在编辑的数据（数据保护机制）
      // 在验证错误处理时跳过自动暂存，避免循环
      if (!skipAutoSave) {
        this.autoSaveCurrentWorkflow()
      }

      const workflow = this.tempWorkflowList[index]
      console.log('🎯 CreatorAgentForm: 加载暂存工作流:', workflow)

      // 加载表单数据
      this.workflowFormData = {
        workflowName: workflow.workflowName,
        workflowDescription: workflow.workflowDescription,
        inputParamsDesc: workflow.inputParamsDesc || '',
        workflowPackage: ''
      }

      // 🔥 加载文件数据（区分新增和已保存工作流）
      if (workflow.status === 'saved' && workflow.fileName) {
        // 已保存的工作流：显示文件信息但没有File对象
        this.workflowFileList = [] // 已保存的工作流没有File对象
        this.workflowFileInfo = {
          name: workflow.fileName,
          originalName: workflow.fileName,
          size: workflow.fileSize || 0,
          file: null, // 已保存的工作流没有File对象
          isSaved: true, // 标记为已保存状态
          packagePath: workflow.workflowPackage || '' // 保存包路径
        }
        console.log('🎯 CreatorAgentForm: 加载已保存工作流的文件信息:', this.workflowFileInfo)
      } else if (workflow.workflowFile && workflow.fileName) {
        // 新增的工作流：有完整的File对象
        this.workflowFileList = [workflow.workflowFile]
        this.workflowFileInfo = {
          name: workflow.fileName,
          originalName: workflow.fileName,
          size: workflow.fileSize || 0,
          file: workflow.workflowFile,
          isSaved: false
        }
        console.log('🎯 CreatorAgentForm: 加载新增工作流的文件信息:', this.workflowFileInfo)
      } else {
        // 没有文件信息
        this.workflowFileList = []
        this.workflowFileInfo = null
        console.log('🎯 CreatorAgentForm: 工作流没有文件信息')
      }

      // 设置当前编辑索引
      this.currentWorkflowIndex = index

      // 在验证错误处理时不显示加载提示
      if (!skipAutoSave) {
        this.$message.info(`已加载工作流: ${workflow.workflowName}`)

        // 🔥 滚动到第二步顶部，确保用户看到完整的工作流表单
        this.scrollToTop()
      }
    },

    // 🔥 自动暂存当前工作流数据（智能数据保护）
    autoSaveCurrentWorkflow() {
      // 🔥 检查当前是否有任意工作流数据需要保存（包括已保存的工作流）
      const hasCurrentData = this.workflowFormData.workflowName.trim() ||
                             this.workflowFormData.workflowDescription.trim() ||
                             this.workflowFileList.length > 0 ||
                             (this.workflowFileInfo && this.workflowFileInfo.isSaved)

      if (!hasCurrentData) {
        console.log('🎯 CreatorAgentForm: 当前无数据，跳过自动暂存')
        return false // 返回false表示没有暂存任何数据
      }

      // 如果当前正在编辑已存在的工作流，直接更新
      if (this.currentWorkflowIndex >= 0) {
        console.log('🎯 CreatorAgentForm: 自动更新当前编辑的工作流')
        this.saveCurrentWorkflowToTemp()
        return true // 返回true表示已暂存数据
      }

      // 🔥 如果是新工作流，只要有任意数据就自动暂存
      console.log('🎯 CreatorAgentForm: 自动暂存新工作流数据（有任意输入）')
      this.saveCurrentWorkflowToTemp()

      // 在完成创建流程中不显示暂存提示，避免干扰用户
      if (!this.stepLoading) {
        this.$message.info('当前工作流数据已自动暂存')
      }

      return true // 返回true表示已暂存数据
    },

    // 🔥 验证所有工作流的完整性
    validateAllWorkflows() {
      console.log('🎯 CreatorAgentForm: 开始验证所有工作流的完整性')

      // 清空之前的验证错误
      this.workflowValidationErrors = {}

      let isAllValid = true
      let invalidCount = 0
      const allErrors = []

      // 遍历所有暂存的工作流
      for (let i = 0; i < this.tempWorkflowList.length; i++) {
        const workflow = this.tempWorkflowList[i]
        const errors = []

        // 验证工作流名称
        if (!workflow.workflowName || !workflow.workflowName.trim()) {
          errors.push('缺少工作流名称')
        }

        // 验证工作流描述
        if (!workflow.workflowDescription || !workflow.workflowDescription.trim()) {
          errors.push('缺少工作流描述')
        }

        // 🔥 验证输入参数说明（后端必填字段）
        if (!workflow.inputParamsDesc || !workflow.inputParamsDesc.trim()) {
          errors.push('缺少参数说明')
        } else {
          // 🔥 验证输入参数说明格式（与后台管理一致的正则验证）
          const inputParamsPattern = /^[^:：]+[:：](?:"[^"]*"|'[^']*'|[^:：,，]+)(?:[,，][^:：]+[:：](?:"[^"]*"|'[^']*'|[^:：,，]+))*$/
          if (!inputParamsPattern.test(workflow.inputParamsDesc.trim())) {
            errors.push('参数说明格式不正确')
          }
        }

        // 🔥 验证工作流文件（区分新增和已保存工作流）
        if (workflow.status === 'saved') {
          // 已保存的工作流：只需要有文件名或包路径
          if (!workflow.fileName && !workflow.workflowPackage) {
            errors.push('缺少压缩包文件')
          }
        } else {
          // 新增的工作流：需要有File对象和文件名
          if (!workflow.workflowFile || !workflow.fileName) {
            errors.push('缺少压缩包文件')
          }
        }

        // 记录验证结果
        const isValid = errors.length === 0
        this.workflowValidationErrors[workflow.id] = {
          errors: errors,
          isValid: isValid,
          workflowName: workflow.workflowName || `工作流${i + 1}`
        }

        if (!isValid) {
          isAllValid = false
          invalidCount++
          allErrors.push(`${workflow.workflowName || `工作流${i + 1}`}: ${errors.join('、')}`)
        }
      }

      const result = {
        isValid: isAllValid,
        invalidCount: invalidCount,
        errors: allErrors,
        validationDetails: this.workflowValidationErrors
      }

      console.log('🎯 CreatorAgentForm: 工作流验证结果:', result)
      return result
    },

    // 🔥 实时更新单个工作流的验证状态
    updateWorkflowValidation(workflow) {
      const errors = []

      // 验证工作流名称
      if (!workflow.workflowName || !workflow.workflowName.trim()) {
        errors.push('缺少工作流名称')
      }

      // 验证工作流描述
      if (!workflow.workflowDescription || !workflow.workflowDescription.trim()) {
        errors.push('缺少工作流描述')
      }

      // 🔥 验证输入参数说明（与批量验证保持一致）
      if (!workflow.inputParamsDesc || !workflow.inputParamsDesc.trim()) {
        errors.push('缺少参数说明')
      } else {
        // 🔥 验证输入参数说明格式（与后台管理一致的正则验证）
        const inputParamsPattern = /^[^:：]+[:：](?:"[^"]*"|'[^']*'|[^:：,，]+)(?:[,，][^:：]+[:：](?:"[^"]*"|'[^']*'|[^:：,，]+))*$/
        if (!inputParamsPattern.test(workflow.inputParamsDesc.trim())) {
          errors.push('参数说明格式不正确')
        }
      }

      // 🔥 验证工作流文件（区分新增和已保存工作流）
      if (workflow.status === 'saved') {
        // 已保存的工作流：只需要有文件名或包路径
        if (!workflow.fileName && !workflow.workflowPackage) {
          errors.push('缺少压缩包文件')
        }
      } else {
        // 新增的工作流：需要有File对象和文件名
        if (!workflow.workflowFile || !workflow.fileName) {
          errors.push('缺少压缩包文件')
        }
      }

      // 更新验证状态
      const isValid = errors.length === 0
      this.$set(this.workflowValidationErrors, workflow.id, {
        errors: errors,
        isValid: isValid,
        workflowName: workflow.workflowName || '未命名工作流'
      })

      console.log(`🎯 CreatorAgentForm: 更新工作流验证状态 - ${workflow.workflowName}: ${isValid ? '有效' : '无效'}`, errors)
    },

    // 🔥 智能错误处理 - 回填第一个有错误的工作流到表单
    handleValidationErrors(validationResult) {
      console.log('🎯 CreatorAgentForm: 开始智能错误处理')

      // 找到第一个有错误的工作流
      let firstErrorWorkflowIndex = -1
      let firstErrorWorkflow = null

      for (let i = 0; i < this.tempWorkflowList.length; i++) {
        const workflow = this.tempWorkflowList[i]
        const validationInfo = validationResult.validationDetails[workflow.id]

        if (validationInfo && !validationInfo.isValid) {
          firstErrorWorkflowIndex = i
          firstErrorWorkflow = workflow
          break
        }
      }

      if (firstErrorWorkflowIndex >= 0 && firstErrorWorkflow) {
        console.log(`🎯 CreatorAgentForm: 找到第一个错误工作流: ${firstErrorWorkflow.workflowName} (索引: ${firstErrorWorkflowIndex})`)

        // 回填错误工作流到表单中（跳过自动暂存避免循环）
        this.loadWorkflowFromTemp(firstErrorWorkflowIndex, true)

        // 显示详细的错误信息
        const errorInfo = validationResult.validationDetails[firstErrorWorkflow.id]
        const errorMessage = `工作流"${firstErrorWorkflow.workflowName}"缺少必填信息：${errorInfo.errors.join('、')}`

        this.$message.error(errorMessage)

        // 显示总体错误统计
        setTimeout(() => {
          this.$message.warning(`共有 ${validationResult.invalidCount} 个工作流存在问题，请逐一完善后再提交`)
        }, 1000)

        console.log('🎯 CreatorAgentForm: 错误工作流已回填到表单，用户可以直接修改')
      } else {
        // 如果没找到错误工作流（理论上不应该发生）
        this.$message.error(`请完善工作流信息后再提交，共有 ${validationResult.invalidCount} 个工作流存在问题`)
      }
    },

    // 🔥 从暂存列表删除工作流
    deleteWorkflowFromTemp(index) {
      if (index < 0 || index >= this.tempWorkflowList.length) {
        console.error('🎯 CreatorAgentForm: 无效的工作流索引:', index)
        return
      }

      const workflow = this.tempWorkflowList[index]
      this.$confirm({
        title: '删除工作流',
        content: `确定要删除工作流"${workflow.workflowName}"吗？`,
        onOk: () => {
          this.tempWorkflowList.splice(index, 1)
          console.log('🎯 CreatorAgentForm: 删除暂存工作流:', workflow)

          // 如果删除的是当前正在编辑的工作流，重置表单
          if (this.currentWorkflowIndex === index) {
            this.resetWorkflowForm()
            this.currentWorkflowIndex = -1
            // 🔥 删除当前编辑的工作流后滚动到顶部
            this.scrollToTop()
          } else if (this.currentWorkflowIndex > index) {
            // 调整当前编辑索引
            this.currentWorkflowIndex--
          }

          this.$message.success('工作流已删除')
        }
      })
    },

    // 格式化日期
    formatDate(date) {
      if (!date) return ''
      return new Date(date).toLocaleString('zh-CN')
    }
  }
}
</script>

<style lang="less" scoped>
// 上传提示
.upload-tips {
  margin-top: 8px;

  p {
    margin: 0;
    font-size: 12px;
    color: #666;
    line-height: 1.4;
  }

  .tip-note {
    color: #1890ff;
    font-weight: 500;
    margin-top: 4px;
  }
}

// 字段提示
.field-tips {
  margin-top: 4px;
  font-size: 12px;
  color: #999;
  line-height: 1.4;
}

// 表单通知
.form-notice {
  margin-top: 24px;

  .ant-alert {
    border-radius: 6px;

    .ant-alert-message {
      font-weight: 600;
    }

    .ant-alert-description {
      margin-top: 4px;
      line-height: 1.5;
    }
  }
}

// 表单样式优化
.ant-form-model {
  .ant-form-model-item {
    margin-bottom: 24px;

    .ant-form-model-item-label {
      label {
        font-weight: 600;
        color: #1f2937;

        &::after {
          content: '';
        }
      }
    }

    .ant-input,
    .ant-input-number,
    .ant-select-selection,
    .ant-input-number-input {
      border-radius: 6px;
      border-color: #d1d5db;

      &:focus,
      &:hover {
        border-color: #1890ff;
      }
    }

    .ant-textarea {
      border-radius: 6px;
      border-color: #d1d5db;

      &:focus,
      &:hover {
        border-color: #1890ff;
      }
    }

    .ant-input-number {
      width: 100%;

      .ant-input-number-input {
        border: none;
      }
    }
  }
}

// 模态框样式
.ant-modal {
  .ant-modal-header {
    border-radius: 8px 8px 0 0;

    .ant-modal-title {
      font-weight: 600;
      color: #1f2937;
    }
  }

  .ant-modal-body {
    padding: 24px;
  }

  .ant-modal-footer {
    border-radius: 0 0 8px 8px;

    .ant-btn {
      border-radius: 6px;

      &.ant-btn-primary {
        background: #1890ff;
        border-color: #1890ff;

        &:hover {
          background: #40a9ff;
          border-color: #40a9ff;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .ant-modal {
    margin: 0;
    max-width: 100vw;

    .ant-modal-content {
      border-radius: 0;
    }
  }

  .ant-form-model {
    .ant-form-model-item {
      .ant-form-model-item-label {
        text-align: left;
      }
    }
  }
}

// 弹窗样式
:global(.creator-agent-modal) {
  .ant-modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-bottom: none;
    border-radius: 12px 12px 0 0;

    .ant-modal-title {
      color: white;
      font-weight: 600;
      font-size: 20px;
    }
  }

  .ant-modal-close {
    color: white;

    &:hover {
      color: rgba(255, 255, 255, 0.8);
    }
  }

  .ant-modal-body {
    padding: 0;
    max-height: 80vh;
    overflow: hidden;
  }

  .ant-modal-content {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  }
}

// 步骤条样式
.step-header {
  padding: 24px 32px;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-bottom: 1px solid #e2e8f0;

  .custom-steps {
    :global(.ant-steps-item-title) {
      font-weight: 600;
      font-size: 16px;
    }

    :global(.ant-steps-item-description) {
      color: #64748b;
    }

    :global(.ant-steps-item-process .ant-steps-item-icon) {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border-color: #667eea;
    }

    :global(.ant-steps-item-finish .ant-steps-item-icon) {
      background: #10b981;
      border-color: #10b981;
    }
  }
}

// 步骤内容
.step-content {
  min-height: 500px;
  max-height: 60vh;
  overflow-y: auto;
}

.step-panel {
  padding: 32px;

  .panel-header {
    text-align: center;
    margin-bottom: 32px;

    .panel-title {
      font-size: 24px;
      font-weight: 700;
      color: #1e293b;
      margin: 0 0 8px 0;

      .anticon {
        margin-right: 12px;
        color: #667eea;
      }
    }

    .panel-desc {
      font-size: 16px;
      color: #64748b;
      margin: 0;
    }
  }
}

// 现代化表单样式
.modern-form {
  .form-card {
    background: white;
    border-radius: 12px;
    padding: 24px;
    margin-bottom: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    border: 1px solid #e2e8f0;
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
      border-color: #cbd5e1;
    }

    .field-label {
      font-size: 16px;
      font-weight: 600;
      color: #1e293b;
      margin-bottom: 12px;
      display: flex;
      align-items: center;

      .anticon {
        margin-right: 8px;
        color: #667eea;
        font-size: 18px;
      }

      .optional {
        font-size: 14px;
        font-weight: 400;
        color: #94a3b8;
        margin-left: 8px;
      }

      // 🔥 必填星号样式
      .required-star {
        color: #ff4d4f;
        font-size: 16px;
        font-weight: 600;
        margin-left: 4px;
      }
    }

    .modern-input,
    .modern-textarea,
    .modern-input-number {
      border-radius: 8px;
      border: 2px solid #e2e8f0;
      transition: all 0.3s ease;

      &:hover {
        border-color: #cbd5e1;
      }

      &:focus,
      &.ant-input-focused {
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
      }
    }

    .modern-input-number {
      width: 100%;

      :global(.ant-input-number-input) {
        border: none;
        border-radius: 6px;
      }
    }

    .field-tips {
      margin-top: 8px;
      font-size: 14px;
      color: #64748b;
      line-height: 1.5;
    }
  }
}

// 步骤底部按钮
.step-footer {
  padding: 24px 32px;
  border-top: 1px solid #e2e8f0;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  display: flex;
  justify-content: space-between;
  align-items: center;

  .ant-btn {
    height: 44px;
    padding: 0 24px;
    font-size: 16px;
    font-weight: 500;
    border-radius: 8px;
    transition: all 0.3s ease;

    &:not(.ant-btn-primary) {
      border: 2px solid #e2e8f0;
      color: #64748b;

      &:hover {
        border-color: #cbd5e1;
        color: #475569;
      }
    }
  }

  .step-actions {
    display: flex;
    gap: 12px;
    align-items: center;
  }

  .next-btn,
  .complete-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;

    &:hover {
      background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
    }

    .anticon {
      margin-left: 8px;
    }
  }
}

// 已创建智能体信息
.created-agent-info {
  margin-bottom: 24px;

  .agent-summary {
    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
    border: 2px solid #0ea5e9;
    border-radius: 12px;
    padding: 20px;
    display: flex;
    align-items: center;
    gap: 16px;

    .agent-avatar {
      width: 60px;
      height: 60px;
      border-radius: 12px;
      overflow: hidden;
      background: white;
      display: flex;
      align-items: center;
      justify-content: center;
      border: 2px solid #0ea5e9;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      .anticon {
        font-size: 24px;
        color: #0ea5e9;
      }
    }

    .agent-details {
      flex: 1;

      h4 {
        margin: 0 0 4px 0;
        font-size: 18px;
        font-weight: 700;
        color: #0c4a6e;
      }

      p {
        margin: 0 0 8px 0;
        font-size: 14px;
        color: #0369a1;
        line-height: 1.4;
      }

      .agent-price {
        font-size: 16px;
        font-weight: 600;
        color: #059669;
        background: rgba(5, 150, 105, 0.1);
        padding: 4px 8px;
        border-radius: 6px;
      }
    }
  }
}

// 工作流管理区域
.workflow-section {
  border: 1px solid #e8eaec;
  border-radius: 8px;
  padding: 16px;
  background: #fafbfc;

  .workflow-notice {
    margin-bottom: 16px;
  }

  .workflow-form {
    .form-header {
      margin-bottom: 24px;
      text-align: center;

      h3 {
        margin: 0 0 8px 0;
        font-size: 18px;
        font-weight: 600;
        color: #333;

        .anticon {
          margin-right: 8px;
          color: #1890ff;
        }
      }

      p {
        margin: 0;
        font-size: 14px;
        color: #666;
      }
    }

    .ant-form-item {
      margin-bottom: 20px;
    }

    .upload-tip {
      margin-top: 8px;
      padding: 8px 12px;
      font-size: 12px;
      color: #666;
      background: #fff7e6;
      border: 1px solid #ffd591;
      border-radius: 4px;
      line-height: 1.4;

      .anticon {
        margin-right: 4px;
      }

      strong {
        color: #fa8c16;
      }
    }
  }

  .add-next-workflow {
    margin-top: 20px;
    padding-top: 16px;
    border-top: 1px dashed #e8eaec;

    .add-next-btn {
      height: 48px;
      border: 2px dashed #52c41a;
      color: #52c41a;
      font-size: 14px;
      font-weight: 500;
      transition: all 0.3s;

      &:hover {
        border-color: #389e0d;
        color: #389e0d;
        background: #f6ffed;
      }

      .anticon {
        margin-right: 8px;
      }
    }
  }

  .workflow-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;

    .workflow-title {
      font-size: 14px;
      font-weight: 600;
      color: #333;

      .anticon {
        margin-right: 6px;
        color: #1890ff;
      }
    }
  }

  .workflow-list {
    .workflow-item {
      background: white;
      border: 1px solid #e8eaec;
      border-radius: 6px;
      padding: 12px;
      margin-bottom: 8px;
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      transition: all 0.3s ease;

      &:hover {
        border-color: #1890ff;
        box-shadow: 0 2px 8px rgba(24, 144, 255, 0.1);
      }

      &:last-child {
        margin-bottom: 0;
      }

      &.pending {
        border-color: #faad14;
        background: #fffbe6;

        &:hover {
          border-color: #faad14;
          box-shadow: 0 2px 8px rgba(250, 173, 20, 0.2);
        }
      }

      .workflow-info {
        flex: 1;

        .workflow-name {
          margin: 0 0 4px 0;
          font-size: 14px;
          font-weight: 600;
          color: #333;
        }

        .workflow-desc {
          margin: 0 0 4px 0;
          font-size: 12px;
          color: #666;
          line-height: 1.4;
        }

        .workflow-time {
          font-size: 11px;
          color: #999;
        }

        .workflow-status {
          font-size: 11px;
          color: #faad14;
          background: #fff7e6;
          padding: 2px 6px;
          border-radius: 4px;
          border: 1px solid #ffd591;
        }
      }

      .workflow-actions {
        display: flex;
        gap: 8px;

        .ant-btn {
          padding: 4px 8px;
          height: auto;
          font-size: 12px;
        }
      }
    }
  }

  // 🔥 暂存工作流列表特殊样式
  .temp-workflows {
    .workflow-item {
      &.editing {
        border-color: #1890ff;
        background: #f0f8ff;
        box-shadow: 0 2px 8px rgba(24, 144, 255, 0.15);
      }

      // 🔥 验证错误状态样式
      &.has-error {
        border-color: #ff4d4f;
        background: #fff2f0;
        box-shadow: 0 2px 8px rgba(255, 77, 79, 0.15);

        .workflow-name {
          color: #ff4d4f;
        }
      }

      .workflow-meta {
        display: flex;
        gap: 16px;
        font-size: 12px;
        color: #999;
        margin-top: 8px;

        .workflow-file, .workflow-size {
          display: flex;
          align-items: center;
        }
      }
    }
  }

  // 🔥 工作流文件上传样式
  .workflow-file-upload {
    .uploaded-file-info {
      .file-item {
        display: flex;
        align-items: center;
        padding: 8px 12px;
        background: #f5f5f5;
        border: 1px solid #d9d9d9;
        border-radius: 6px;
        margin-bottom: 8px;

        .file-icon {
          color: #1890ff;
          margin-right: 8px;
          font-size: 16px;
        }

        .file-name {
          flex: 1;
          font-size: 14px;
          color: #333;
          margin-right: 8px;
        }

        .remove-btn {
          padding: 4px 8px;
          font-size: 12px;
        }

        // 🔥 已保存文件的特殊样式
        &.saved-file {
          background: #f6ffed;
          border-color: #b7eb8f;

          .file-icon {
            color: #52c41a;
          }

          .file-name {
            color: #389e0d;
            font-weight: 500;
          }
        }
      }

      // 🔥 验证错误提示样式
      .workflow-errors {
        margin-top: 12px;
        margin-bottom: 8px;

        .ant-alert {
          border-radius: 4px;

          .ant-alert-message {
            font-size: 12px;
            line-height: 1.4;
          }
        }
      }
    }
  }

  .workflow-empty {
    text-align: center;
    padding: 20px;

    .ant-empty {
      margin: 0;
    }
  }

  .workflow-add-item {
    margin-top: 16px;

    .add-workflow-btn {
      height: 48px;
      border: 2px dashed #d9d9d9;
      color: #666;
      font-size: 14px;
      transition: all 0.3s;

      &:hover {
        border-color: #1890ff;
        color: #1890ff;
      }

      .anticon {
        margin-right: 8px;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  :global(.creator-agent-modal) {
    .ant-modal {
      width: 95% !important;
      margin: 10px auto;
    }

    .ant-modal-body {
      padding: 16px;
    }
  }

  .modal-footer {
    padding: 12px 16px;

    .ant-btn {
      width: 48%;
      margin-left: 4%;

      &:first-child {
        margin-left: 0;
      }
    }
  }

  .workflow-section {
    .workflow-item {
      flex-direction: column;
      gap: 12px;

      .workflow-actions {
        align-self: stretch;
        justify-content: flex-end;
      }
    }
  }
}

// 第三步成功页面样式
.success-content {
  text-align: center;
  padding: 40px 20px;

  .success-icon {
    margin-bottom: 24px;
  }

  .success-message {
    h2 {
      color: #52c41a;
      font-size: 24px;
      font-weight: 600;
      margin-bottom: 16px;
    }

    p {
      color: #666;
      font-size: 14px;
      line-height: 1.6;
      margin-bottom: 8px;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}
</style>
