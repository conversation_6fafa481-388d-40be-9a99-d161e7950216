/**
 * 🧪 心跳API测试工具
 * 提供心跳API的测试和验证功能
 * 
 * <AUTHOR>
 * @since 2025-01-30
 */

import { HeartbeatApi } from './heartbeat'
import { getApiConfig, getApiKey } from './heartbeatConfig'

// ==================== 测试工具类 ====================

/**
 * 🧪 心跳API测试器
 */
export class HeartbeatTester {
  constructor(options = {}) {
    this.options = {
      enableLog: true,
      enablePerformanceTest: true,
      enableStressTest: false,
      ...options
    }
    
    this.testResults = []
    this.performanceMetrics = {
      totalTests: 0,
      successCount: 0,
      failureCount: 0,
      averageResponseTime: 0,
      minResponseTime: Infinity,
      maxResponseTime: 0
    }
  }

  /**
   * 📝 记录测试日志
   * @param {String} level 日志级别
   * @param {String} message 日志消息
   * @param {Object} data 附加数据
   */
  log(level, message, data = null) {
    if (!this.options.enableLog) return
    
    const timestamp = new Date().toISOString()
    const logEntry = { timestamp, level, message, data }
    
    console.log(`[${timestamp}] [${level.toUpperCase()}] ${message}`, data || '')
    this.testResults.push(logEntry)
  }

  /**
   * 🔄 测试基础心跳发送
   * @param {String} pageType 页面类型
   * @returns {Promise<Object>} 测试结果
   */
  async testBasicHeartbeat(pageType = 'default') {
    const startTime = Date.now()
    this.log('info', `开始测试基础心跳发送 - 页面类型: ${pageType}`)
    
    try {
      const heartbeatData = {
        pageType,
        apiKey: getApiKey(pageType),
        testMode: true,
        testId: `test_${Date.now()}`
      }
      
      const response = await HeartbeatApi.send(heartbeatData)
      const responseTime = Date.now() - startTime
      
      this.updatePerformanceMetrics(responseTime, response.success)
      
      if (response.success) {
        this.log('success', `基础心跳测试成功 - 响应时间: ${responseTime}ms`, response)
        return { success: true, responseTime, response }
      } else {
        this.log('error', `基础心跳测试失败 - 响应时间: ${responseTime}ms`, response)
        return { success: false, responseTime, response }
      }
      
    } catch (error) {
      const responseTime = Date.now() - startTime
      this.updatePerformanceMetrics(responseTime, false)
      this.log('error', `基础心跳测试异常 - 响应时间: ${responseTime}ms`, error)
      return { success: false, responseTime, error: error.message }
    }
  }

  /**
   * 🔄 测试智能心跳（带重试和缓存）
   * @param {String} pageType 页面类型
   * @returns {Promise<Object>} 测试结果
   */
  async testSmartHeartbeat(pageType = 'default') {
    const startTime = Date.now()
    this.log('info', `开始测试智能心跳 - 页面类型: ${pageType}`)
    
    try {
      const heartbeatData = {
        pageType,
        apiKey: getApiKey(pageType),
        testMode: true,
        testId: `smart_test_${Date.now()}`
      }
      
      const options = {
        enableCache: true,
        enableRetry: true,
        retryOptions: { maxRetries: 2, retryDelay: 500 }
      }
      
      const response = await HeartbeatApi.smart(heartbeatData, options)
      const responseTime = Date.now() - startTime
      
      this.updatePerformanceMetrics(responseTime, response.success)
      
      if (response.success) {
        this.log('success', `智能心跳测试成功 - 响应时间: ${responseTime}ms`, response)
        return { success: true, responseTime, response }
      } else {
        this.log('error', `智能心跳测试失败 - 响应时间: ${responseTime}ms`, response)
        return { success: false, responseTime, response }
      }
      
    } catch (error) {
      const responseTime = Date.now() - startTime
      this.updatePerformanceMetrics(responseTime, false)
      this.log('error', `智能心跳测试异常 - 响应时间: ${responseTime}ms`, error)
      return { success: false, responseTime, error: error.message }
    }
  }

  /**
   * 📊 测试批量操作
   * @returns {Promise<Object>} 测试结果
   */
  async testBatchOperations() {
    const startTime = Date.now()
    this.log('info', '开始测试批量操作')
    
    try {
      // 模拟用户ID列表
      const userIds = ['user1', 'user2', 'user3', 'user4', 'user5']
      
      const response = await HeartbeatApi.batchStatus(userIds, {
        includeDetails: true
      })
      
      const responseTime = Date.now() - startTime
      this.updatePerformanceMetrics(responseTime, response.success)
      
      if (response.success) {
        this.log('success', `批量操作测试成功 - 响应时间: ${responseTime}ms`, response)
        return { success: true, responseTime, response }
      } else {
        this.log('error', `批量操作测试失败 - 响应时间: ${responseTime}ms`, response)
        return { success: false, responseTime, response }
      }
      
    } catch (error) {
      const responseTime = Date.now() - startTime
      this.updatePerformanceMetrics(responseTime, false)
      this.log('error', `批量操作测试异常 - 响应时间: ${responseTime}ms`, error)
      return { success: false, responseTime, error: error.message }
    }
  }

  /**
   * 📈 测试统计数据获取
   * @returns {Promise<Object>} 测试结果
   */
  async testStatsRetrieval() {
    const startTime = Date.now()
    this.log('info', '开始测试统计数据获取')
    
    try {
      const apiKey = getApiKey('admin')
      
      const response = await HeartbeatApi.getRealTime(apiKey, {
        timeRange: '1h',
        metrics: ['online_users', 'api_calls']
      })
      
      const responseTime = Date.now() - startTime
      this.updatePerformanceMetrics(responseTime, response.success)
      
      if (response.success) {
        this.log('success', `统计数据测试成功 - 响应时间: ${responseTime}ms`, response)
        return { success: true, responseTime, response }
      } else {
        this.log('error', `统计数据测试失败 - 响应时间: ${responseTime}ms`, response)
        return { success: false, responseTime, response }
      }
      
    } catch (error) {
      const responseTime = Date.now() - startTime
      this.updatePerformanceMetrics(responseTime, false)
      this.log('error', `统计数据测试异常 - 响应时间: ${responseTime}ms`, error)
      return { success: false, responseTime, error: error.message }
    }
  }

  /**
   * 🚀 运行完整测试套件
   * @returns {Promise<Object>} 完整测试结果
   */
  async runFullTestSuite() {
    this.log('info', '开始运行完整测试套件')
    const suiteStartTime = Date.now()
    
    const testResults = {
      basicHeartbeat: {},
      smartHeartbeat: {},
      batchOperations: {},
      statsRetrieval: {}
    }
    
    try {
      // 1. 测试基础心跳
      testResults.basicHeartbeat = await this.testBasicHeartbeat('home')
      
      // 2. 测试智能心跳
      testResults.smartHeartbeat = await this.testSmartHeartbeat('market')
      
      // 3. 测试批量操作
      testResults.batchOperations = await this.testBatchOperations()
      
      // 4. 测试统计数据
      testResults.statsRetrieval = await this.testStatsRetrieval()
      
      const totalTime = Date.now() - suiteStartTime
      const successCount = Object.values(testResults).filter(r => r.success).length
      const totalCount = Object.keys(testResults).length
      
      this.log('info', `测试套件完成 - 总时间: ${totalTime}ms, 成功: ${successCount}/${totalCount}`)
      
      return {
        success: successCount === totalCount,
        totalTime,
        successCount,
        totalCount,
        results: testResults,
        performanceMetrics: this.performanceMetrics
      }
      
    } catch (error) {
      this.log('error', '测试套件执行异常', error)
      return {
        success: false,
        error: error.message,
        results: testResults,
        performanceMetrics: this.performanceMetrics
      }
    }
  }

  /**
   * 📊 更新性能指标
   * @param {Number} responseTime 响应时间
   * @param {Boolean} success 是否成功
   */
  updatePerformanceMetrics(responseTime, success) {
    this.performanceMetrics.totalTests++
    
    if (success) {
      this.performanceMetrics.successCount++
    } else {
      this.performanceMetrics.failureCount++
    }
    
    this.performanceMetrics.minResponseTime = Math.min(
      this.performanceMetrics.minResponseTime, 
      responseTime
    )
    this.performanceMetrics.maxResponseTime = Math.max(
      this.performanceMetrics.maxResponseTime, 
      responseTime
    )
    
    // 计算平均响应时间
    const totalResponseTime = this.performanceMetrics.averageResponseTime * 
      (this.performanceMetrics.totalTests - 1) + responseTime
    this.performanceMetrics.averageResponseTime = totalResponseTime / this.performanceMetrics.totalTests
  }

  /**
   * 📋 获取测试报告
   * @returns {Object} 测试报告
   */
  getTestReport() {
    return {
      testResults: this.testResults,
      performanceMetrics: this.performanceMetrics,
      summary: {
        totalTests: this.performanceMetrics.totalTests,
        successRate: this.performanceMetrics.totalTests > 0 
          ? (this.performanceMetrics.successCount / this.performanceMetrics.totalTests * 100).toFixed(2) + '%'
          : '0%',
        averageResponseTime: Math.round(this.performanceMetrics.averageResponseTime) + 'ms',
        responseTimeRange: `${Math.round(this.performanceMetrics.minResponseTime)}ms - ${Math.round(this.performanceMetrics.maxResponseTime)}ms`
      }
    }
  }

  /**
   * 🧹 清理测试数据
   */
  cleanup() {
    this.testResults = []
    this.performanceMetrics = {
      totalTests: 0,
      successCount: 0,
      failureCount: 0,
      averageResponseTime: 0,
      minResponseTime: Infinity,
      maxResponseTime: 0
    }
    
    // 清理心跳缓存
    HeartbeatApi.clearCache()
    
    this.log('info', '测试数据已清理')
  }
}

// ==================== 便捷方法 ====================

/**
 * 🚀 快速测试心跳API
 * @param {Object} options 测试选项
 * @returns {Promise<Object>} 测试结果
 */
export async function quickTest(options = {}) {
  const tester = new HeartbeatTester(options)
  const result = await tester.runFullTestSuite()
  const report = tester.getTestReport()
  
  console.log('🧪 心跳API测试报告:', report)
  
  return { result, report }
}

/**
 * 🔧 测试单个API方法
 * @param {String} method API方法名
 * @param {Array} args 方法参数
 * @returns {Promise<Object>} 测试结果
 */
export async function testSingleApi(method, ...args) {
  const startTime = Date.now()
  
  try {
    if (!HeartbeatApi[method]) {
      throw new Error(`API方法 ${method} 不存在`)
    }
    
    const response = await HeartbeatApi[method](...args)
    const responseTime = Date.now() - startTime
    
    console.log(`✅ ${method} 测试成功 - 响应时间: ${responseTime}ms`, response)
    
    return { success: true, responseTime, response }
    
  } catch (error) {
    const responseTime = Date.now() - startTime
    
    console.error(`❌ ${method} 测试失败 - 响应时间: ${responseTime}ms`, error)
    
    return { success: false, responseTime, error: error.message }
  }
}

// ==================== 导出 ====================

export default {
  HeartbeatTester,
  quickTest,
  testSingleApi
}
