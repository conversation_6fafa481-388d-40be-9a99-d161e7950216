# 图生视频插件完整测试文档

## 📋 测试概述

本文档提供图生视频插件从扣子调用到视频生成完成的完整测试流程，包括定价计算、扣费、API调用等各环节的验证。

## 🔧 测试环境准备

### 1. 服务器配置
- **后端服务**: `http://localhost:8080/jeecg-boot`
- **前端服务**: `http://localhost:3000`
- **数据库**: MySQL (确保用户有足够余额)
- **豆包API**: 配置正确的API密钥

### 2. 测试数据准备
```sql
-- 确保测试用户有足够余额
UPDATE aicg_user_profile 
SET account_balance = 100.00 
WHERE api_key = 'ak_your_test_api_key';

-- 确保插件配置存在
INSERT INTO aigc_plub_shop (plugin_key, plubname, neednum, status) 
VALUES ('tushengshipin', '图生视频生成器', 1.00, 1)
ON DUPLICATE KEY UPDATE status = 1;
```

## 🧪 测试用例

### 测试用例1：基础功能测试（轻量级模型 + 480p + 5秒）

#### 请求参数
```json
{
  "apiKey": "ak_your_test_api_key",
  "model": "doubao-seedance-1-0-lite-i2v-250428",
  "prompt": "一只可爱的小猫在花园里玩耍，阳光明媚，画面温馨",
  "image_url": "https://example.com/test_image.jpg",
  "resolution": "480p",
  "duration": 5,
  "camerafixed": false
}
```

#### 预期结果
- **普通用户**: 扣费 0.69元
- **VIP用户**: 扣费 0.59元  
- **SVIP用户**: 扣费 0.49元
- **管理员**: 扣费 0.49元

#### 测试命令
```bash
curl -X POST "http://localhost:8080/jeecg-boot/api/coze/video/generate-task" \
  -H "Content-Type: application/json" \
  -d '{
    "apiKey": "ak_your_test_api_key",
    "model": "doubao-seedance-1-0-lite-i2v-250428",
    "prompt": "一只可爱的小猫在花园里玩耍，阳光明媚，画面温馨",
    "image_url": "https://example.com/test_image.jpg",
    "resolution": "480p",
    "duration": 5
  }'
```

### 测试用例2：专业级模型测试（pro模型 + 1080p + 10秒）

#### 请求参数
```json
{
  "apiKey": "ak_your_test_api_key",
  "model": "doubao-seedance-1-0-pro-250528",
  "prompt": "城市夜景，霓虹灯闪烁，车流穿梭",
  "image_url": "https://example.com/city_night.jpg",
  "resolution": "1080p",
  "duration": 10
}
```

#### 预期结果
- **普通用户**: 扣费 7.34元
- **VIP用户**: 扣费 7.14元
- **SVIP用户**: 扣费 6.94元
- **管理员**: 扣费 6.94元

### 测试用例3：参数验证测试

#### 3.1 pro模型 + 720p（应该失败）
```json
{
  "apiKey": "ak_your_test_api_key",
  "model": "doubao-seedance-1-0-pro-250528",
  "resolution": "720p",
  "prompt": "测试",
  "image_url": "https://example.com/test.jpg"
}
```
**预期结果**: 返回错误 "pro模型不支持720p分辨率，请选择480p或1080p"

#### 3.2 无效API密钥测试
```json
{
  "apiKey": "invalid_api_key",
  "prompt": "测试",
  "image_url": "https://example.com/test.jpg"
}
```
**预期结果**: 返回错误 "API-Key无效或已被禁用"

#### 3.3 余额不足测试
```sql
-- 先设置用户余额为0.1元
UPDATE aicg_user_profile 
SET account_balance = 0.10 
WHERE api_key = 'ak_your_test_api_key';
```
**预期结果**: 返回错误 "余额不足，需要X.XX元，当前余额0.10元"

### 测试用例4：任务查询测试

#### 查询任务状态
```bash
curl -X POST "http://localhost:8080/jeecg-boot/api/coze/video/query-task" \
  -H "Content-Type: application/json" \
  -d '{
    "task_id": "task_abc123def456"
  }'
```

#### 预期响应格式
```json
{
  "task_id": "task_abc123def456",
  "status": "completed",
  "message": "视频生成完成",
  "created_at": "2025-01-16T10:30:00Z",
  "updated_at": "2025-01-16T10:32:15Z",
  "video_url": "https://cdn.aigcview.com/videos/generated_video_123.mp4"
}
```

## 📊 定价验证表

| 模型 | 分辨率 | 时长 | 普通用户 | VIP用户 | SVIP用户 | 管理员 |
|------|--------|------|----------|---------|----------|--------|
| lite | 480p | 5秒 | 0.69元 | 0.59元 | 0.49元 | 0.49元 |
| lite | 480p | 10秒 | 1.38元 | 1.18元 | 0.98元 | 0.98元 |
| lite | 720p | 5秒 | 1.23元 | 1.13元 | 1.03元 | 1.03元 |
| lite | 720p | 10秒 | 2.46元 | 2.26元 | 2.06元 | 2.06元 |
| lite | 1080p | 5秒 | 2.45元 | 2.35元 | 2.25元 | 2.25元 |
| lite | 1080p | 10秒 | 4.90元 | 4.70元 | 4.50元 | 4.50元 |
| pro | 480p | 5秒 | 0.93元 | 0.83元 | 0.73元 | 0.73元 |
| pro | 480p | 10秒 | 1.86元 | 1.66元 | 1.46元 | 1.46元 |
| pro | 1080p | 5秒 | 3.67元 | 3.57元 | 3.47元 | 3.47元 |
| pro | 1080p | 10秒 | 7.34元 | 7.14元 | 6.94元 | 6.94元 |

## 🔍 测试检查点

### 1. 参数验证检查
- [ ] API密钥格式验证
- [ ] 必填参数验证（prompt, image_url）
- [ ] 模型支持验证
- [ ] 分辨率支持验证
- [ ] 时长支持验证
- [ ] URL格式验证
- [ ] 参数组合规则验证（pro模型不支持720p）

### 2. 定价计算检查
- [ ] 基础价格计算正确
- [ ] 用户等级折扣应用正确
- [ ] 不同参数组合价格计算正确
- [ ] 价格精度保持2位小数

### 3. 扣费流程检查
- [ ] 余额检查正确
- [ ] 扣费操作成功
- [ ] 扣费记录生成
- [ ] 余额更新正确

### 4. API调用检查
- [ ] 豆包API调用成功
- [ ] 请求参数构建正确
- [ ] 响应解析正确
- [ ] 错误处理完善

### 5. 日志记录检查
- [ ] 关键操作有日志记录
- [ ] 错误信息记录详细
- [ ] 日志级别设置合理
- [ ] 敏感信息已脱敏

## 🚨 常见问题排查

### 1. 扣费失败
- 检查用户余额是否充足
- 检查用户状态是否正常
- 检查数据库连接是否正常

### 2. 豆包API调用失败
- 检查API密钥配置
- 检查网络连接
- 检查请求参数格式

### 3. 价格计算错误
- 检查定价表数据是否正确
- 检查用户角色查询是否正确
- 检查计算逻辑是否有误

### 4. 参数验证失败
- 检查参数格式是否正确
- 检查必填参数是否缺失
- 检查参数组合是否符合规则

## ✅ 测试完成标准

1. **功能完整性**: 所有核心功能正常工作
2. **定价准确性**: 所有定价计算结果正确
3. **错误处理**: 异常情况处理得当
4. **性能表现**: 响应时间在可接受范围内
5. **日志完整**: 关键操作都有详细日志

## 📝 测试报告模板

```
测试时间: 2025-01-16
测试人员: [姓名]
测试环境: [环境描述]

测试结果:
✅ 基础功能测试 - 通过
✅ 定价计算测试 - 通过  
✅ 参数验证测试 - 通过
✅ 错误处理测试 - 通过
❌ 性能测试 - 需优化

问题记录:
1. [问题描述]
2. [问题描述]

建议:
1. [改进建议]
2. [改进建议]
```
