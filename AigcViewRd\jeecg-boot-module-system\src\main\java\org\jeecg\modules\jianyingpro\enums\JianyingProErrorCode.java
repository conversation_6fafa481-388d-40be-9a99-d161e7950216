package org.jeecg.modules.jianyingpro.enums;

/**
 * 超级剪映小助手错误码枚举
 * 
 * <AUTHOR>
 * @since 2025-01-01
 */
public enum JianyingProErrorCode {
    
    // ========== 参数错误 (PARAM_) ==========
    PARAM_CONFLICT_001("PARAM_CONFLICT_001", "参数冲突：不能同时提供多种模式的参数", "请检查参数配置，确保只使用一种模式的参数"),
    PARAM_INVALID_002("PARAM_INVALID_002", "参数无效：参数值不符合要求", "请检查参数值是否符合API文档要求"),
    PARAM_INCOMPLETE_003("PARAM_INCOMPLETE_003", "参数不完整：缺少必要参数", "请补充必填参数"),
    PARAM_CONFLICT_004("PARAM_CONFLICT_004", "时间线模式冲突：不能同时提供音频模式和自定义模式参数", "请选择音频模式(audio_urls)或自定义模式(duration+num)中的一种"),
    PARAM_FORMAT_004("PARAM_FORMAT_004", "参数格式错误：参数格式不正确", "请检查参数格式是否正确"),
    PARAM_RANGE_005("PARAM_RANGE_005", "参数范围错误：参数值超出允许范围", "请调整参数值到允许范围内"),
    PARAM_MISSING_001("PARAM_MISSING_001", "参数缺失：缺少必要参数", "请检查并补充必要参数"),

    // ========== 业务错误 (BUSINESS_) ==========
    BUSINESS_AUDIO_PROCESS_101("BUSINESS_AUDIO_PROCESS_101", "音频处理失败", "请检查音频文件URL是否有效，格式是否支持"),
    BUSINESS_VIDEO_PROCESS_102("BUSINESS_VIDEO_PROCESS_102", "视频处理失败", "请检查视频文件URL是否有效，格式是否支持"),
    BUSINESS_TIMELINE_CALC_103("BUSINESS_TIMELINE_CALC_103", "时间线计算失败", "请检查时间线参数是否合理"),
    BUSINESS_DRAFT_OPERATION_104("BUSINESS_DRAFT_OPERATION_104", "草稿操作失败", "请检查草稿URL是否有效，文件是否存在"),
    BUSINESS_DATA_CONVERSION_105("BUSINESS_DATA_CONVERSION_105", "数据转换失败", "请检查输入数据格式是否正确"),
    BUSINESS_FILE_ACCESS_106("BUSINESS_FILE_ACCESS_106", "文件访问失败", "请检查文件URL是否可访问"),
    BUSINESS_THIRD_PARTY_API_107("BUSINESS_THIRD_PARTY_API_107", "第三方API调用失败", "请稍后重试，或联系技术支持"),
    BUSINESS_DRAFT_001("BUSINESS_DRAFT_001", "草稿处理失败", "请检查草稿文件是否有效"),

    // ========== 系统错误 (SYSTEM_) ==========
    SYSTEM_ERROR_500("SYSTEM_ERROR_500", "系统内部错误", "请联系技术支持"),
    SYSTEM_ERROR_501("SYSTEM_ERROR_501", "系统处理错误", "请联系技术支持"),
    SYSTEM_TIMEOUT_501("SYSTEM_TIMEOUT_501", "系统超时", "请稍后重试"),
    SYSTEM_NETWORK_502("SYSTEM_NETWORK_502", "网络连接错误", "请检查网络连接"),
    SYSTEM_STORAGE_503("SYSTEM_STORAGE_503", "存储服务错误", "请稍后重试"),
    SYSTEM_UNKNOWN_504("SYSTEM_UNKNOWN_504", "未知系统错误", "请联系技术支持");

    private final String code;
    private final String message;
    private final String solution;
    private final int httpStatus;

    JianyingProErrorCode(String code, String message, String solution) {
        this.code = code;
        this.message = message;
        this.solution = solution;
        this.httpStatus = 400; // 默认400错误
    }

    public String getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }

    public String getSolution() {
        return solution;
    }

    public int getHttpStatus() {
        return httpStatus;
    }

    /**
     * 根据错误码查找枚举值
     */
    public static JianyingProErrorCode fromCode(String code) {
        for (JianyingProErrorCode errorCode : values()) {
            if (errorCode.getCode().equals(code)) {
                return errorCode;
            }
        }
        return SYSTEM_UNKNOWN_504; // 默认返回未知错误
    }
    
    /**
     * 根据错误码获取枚举
     */
    public static JianyingProErrorCode getByCode(String code) {
        for (JianyingProErrorCode errorCode : values()) {
            if (errorCode.getCode().equals(code)) {
                return errorCode;
            }
        }
        return SYSTEM_UNKNOWN_504;
    }
    
    /**
     * 判断是否为参数错误
     */
    public boolean isParamError() {
        return code.startsWith("PARAM_");
    }
    
    /**
     * 判断是否为业务错误
     */
    public boolean isBusinessError() {
        return code.startsWith("BUSINESS_");
    }
    
    /**
     * 判断是否为系统错误
     */
    public boolean isSystemError() {
        return code.startsWith("SYSTEM_");
    }
}
