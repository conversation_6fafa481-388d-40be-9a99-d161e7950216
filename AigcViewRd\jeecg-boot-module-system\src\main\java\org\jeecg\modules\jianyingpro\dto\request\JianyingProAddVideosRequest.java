package org.jeecg.modules.jianyingpro.dto.request;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 一体化视频添加请求
 * 合并 video_infos + add_videos 的参数
 * 
 * <AUTHOR>
 * @since 2025-01-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class JianyingProAddVideosRequest extends BaseJianyingProRequest {
    
    // ========== 核心参数 ==========
    @ApiModelProperty(value = "草稿地址（必填）", required = true)
    @NotBlank(message = "draft_url不能为空")
    @JsonProperty("draft_url")
    private String draftUrl;

    @ApiModelProperty(value = "视频URL列表（必填）", required = true)
    @NotEmpty(message = "video_urls不能为空")
    @JsonProperty("video_urls")
    private List<String> videoUrls;

    @ApiModelProperty(value = "时间节点，只接收结构：[{\"start\":0,\"end\":4612}]，一般从audio_timeline节点的输出获取（必填）", required = true,
                     example = "[{\"start\": 0, \"end\": 4612000}]")
    @NotEmpty(message = "timelines不能为空")
    @JsonProperty("timelines")
    private List<JSONObject> timelines;
    
    @ApiModelProperty(value = "视频特效（可选）")
    @JsonProperty("video_effect")
    private String videoEffect;

    // ========== 来自video_infos的遗漏参数 ==========
    @ApiModelProperty(value = "视频蒙版，可填写值：圆形，矩形，爱心，星形（可选）", example = "圆形")
    @JsonProperty("mask")
    private String mask;

    @ApiModelProperty(value = "视频高度", example = "1080")
    @JsonProperty("height")
    private Integer height;

    @ApiModelProperty(value = "视频宽度", example = "1920")
    @JsonProperty("width")
    private Integer width;

    @ApiModelProperty(value = "转场", example = "淡入淡出")
    @JsonProperty("transition")
    private String transition;

    @ApiModelProperty(value = "转场的时长", example = "1000000")
    @JsonProperty("transition_duration")
    private Integer transitionDuration;

    @ApiModelProperty(value = "音量大小，0-10，默认1", example = "1.0")
    @JsonProperty("volume")
    private Double volume;

    // ========== 来自add_videos的遗漏参数 ==========
    @ApiModelProperty(value = "透明度，范围0-1，默认1", example = "1.0")
    @JsonProperty("alpha")
    private Double alpha;

    @ApiModelProperty(value = "X轴缩放", example = "1.0")
    @JsonProperty("scale_x")
    private Double scaleX;

    @ApiModelProperty(value = "Y轴缩放", example = "1.0")
    @JsonProperty("scale_y")
    private Double scaleY;

    @ApiModelProperty(value = "X轴位置", example = "0.0")
    @JsonProperty("transform_x")
    private Double transformX;

    @ApiModelProperty(value = "Y轴位置", example = "0.0")
    @JsonProperty("transform_y")
    private Double transformY;
    
    @Override
    public String getSummary() {
        return "JianyingProAddVideosRequest{" +
               "draftUrl=" + (draftUrl != null && draftUrl.length() > 50 ?
                            draftUrl.substring(0, 50) + "***" : draftUrl) +
               ", videoUrlsCount=" + (videoUrls != null ? videoUrls.size() : 0) +
               ", videoUrls=" + videoUrls +
               ", mask=" + mask +
               ", height=" + height +
               ", width=" + width +
               ", transition=" + transition +
               ", transitionDuration=" + transitionDuration +
               ", alpha=" + alpha +
               ", scaleX=" + scaleX +
               ", scaleY=" + scaleY +
               ", transformX=" + transformX +
               ", transformY=" + transformY +
               ", volume=" + volume +
               "}";
    }
    
    @Override
    public void validate() {
        super.validate();

        // video_urls是必填的（复制自稳定版video_infos要求）
        if (videoUrls == null || videoUrls.isEmpty()) {
            throw new IllegalArgumentException(
                "参数不完整：video_urls不能为空"
            );
        }

        // timelines是必填的（复制自稳定版video_infos要求）
        if (timelines == null || timelines.isEmpty()) {
            throw new IllegalArgumentException(
                "参数不完整：timelines不能为空"
            );
        }
    }
}
