/**
 * 插件分类字典服务
 * 统一管理插件分类的获取和显示
 */

import { ajaxGetDictItems } from '@/api/api'

class CategoryService {
  constructor() {
    this.categories = []
    this.categoryMap = new Map()
    this.loading = false
    this.loaded = false
  }

  /**
   * 获取插件分类列表
   * @param {Boolean} forceRefresh 是否强制刷新
   * @returns {Promise<Array>} 分类列表
   */
  async getCategories(forceRefresh = false) {
    if (this.loaded && !forceRefresh) {
      return this.categories
    }

    if (this.loading) {
      // 如果正在加载，等待加载完成
      return new Promise((resolve) => {
        const checkLoaded = () => {
          if (this.loaded) {
            resolve(this.categories)
          } else {
            setTimeout(checkLoaded, 100)
          }
        }
        checkLoaded()
      })
    }

    this.loading = true

    try {
      console.log('🔄 从服务器获取插件分类数据...')
      console.log('🔍 调用接口: /sys/dict/getDictItems/plugin_category')
      const response = await ajaxGetDictItems('plugin_category')

      console.log('🔍 服务器响应:', response)
      if (response.success && response.result) {
        this.categories = response.result

        // 🔥 添加组合插件分类
        const combinedCategory = {
          value: 'combine',
          text: '组合插件'
        }

        // 检查是否已存在组合插件分类
        const existingCombined = this.categories.find(cat => cat.value === 'combine')
        if (!existingCombined) {
          this.categories.push(combinedCategory)
          console.log('✅ 已添加组合插件分类')
        }

        this.buildCategoryMap()
        this.loaded = true
        console.log('✅ 插件分类数据加载成功:', this.categories)
        console.log('✅ 分类数量:', this.categories.length)
      } else {
        console.error('❌ 获取插件分类数据失败:', response.message)
        this.categories = []
      }
    } catch (error) {
      console.error('❌ 获取插件分类数据异常:', error)
      this.categories = []
    } finally {
      this.loading = false
    }

    return this.categories
  }

  /**
   * 构建分类映射表
   */
  buildCategoryMap() {
    this.categoryMap.clear()
    this.categories.forEach(category => {
      this.categoryMap.set(category.value, category.text || category.label)
    })
  }

  /**
   * 根据分类值获取分类文本
   * @param {String} categoryValue 分类值
   * @returns {String} 分类文本
   */
  getCategoryText(categoryValue) {
    if (!categoryValue) {
      return '未知分类'
    }

    // 如果还没有加载分类数据，返回原值
    if (!this.loaded) {
      return categoryValue
    }

    return this.categoryMap.get(categoryValue) || categoryValue
  }

  /**
   * 获取分类颜色（用于标签显示）
   * @param {String} categoryValue 分类值
   * @returns {String} 颜色值
   */
  getCategoryColor(categoryValue) {
    // 根据分类值生成一致的颜色
    const colors = ['blue', 'purple', 'red', 'orange', 'green', 'cyan', 'pink', 'gold']
    if (!categoryValue) return 'default'
    
    // 使用分类值的哈希来确定颜色，确保同一分类总是相同颜色
    let hash = 0
    for (let i = 0; i < categoryValue.length; i++) {
      hash = categoryValue.charCodeAt(i) + ((hash << 5) - hash)
    }
    const colorIndex = Math.abs(hash) % colors.length
    return colors[colorIndex]
  }

  /**
   * 检查分类是否存在
   * @param {String} categoryValue 分类值
   * @returns {Boolean} 是否存在
   */
  categoryExists(categoryValue) {
    return this.categoryMap.has(categoryValue)
  }

  /**
   * 刷新分类数据
   * @returns {Promise<Array>} 分类列表
   */
  async refresh() {
    this.loaded = false
    this.clearCache()
    console.log('🔄 强制刷新分类数据，清除所有缓存')
    return this.getCategories(true)
  }

  /**
   * 清除缓存
   */
  clearCache() {
    this.categories = []
    this.categoryMap.clear()
    this.loaded = false
    this.loading = false
  }
}

// 创建单例实例
const categoryService = new CategoryService()

// 🔥 监听WebSocket缓存清除消息
if (typeof window !== 'undefined') {
  // 等待Vue实例加载完成后再监听
  setTimeout(() => {
    if (window.Vue && window.Vue.prototype.$bus) {
      window.Vue.prototype.$bus.$on('websocket-message', (message) => {
        try {
          const data = JSON.parse(message)

          // 检查是否是缓存清除消息
          if (data.msgId === 'CACHE_CLEAR' || data.cmd === 'topic') {
            console.log('🔔 收到缓存清除通知:', data.msgTxt)

            // 清除分类服务缓存
            categoryService.clearCache()

            // 清除localStorage字典缓存
            localStorage.removeItem('__DICT_DB_CACHE__')

            // 显示通知
            if (window.Vue.prototype.$message) {
              window.Vue.prototype.$message.info('🔄 系统缓存已更新，数据已刷新')
            }

            console.log('✅ 前端缓存已自动清除')
          }
        } catch (error) {
          console.error('处理WebSocket缓存清除消息失败:', error)
        }
      })
    }
  }, 1000)
}

export default categoryService

/**
 * Vue插件安装函数
 * 将categoryService注入到Vue实例中
 */
export function install(Vue) {
  Vue.prototype.$categoryService = categoryService
}

/**
 * 便捷的获取分类文本函数
 * @param {String} categoryValue 分类值
 * @returns {String} 分类文本
 */
export function getCategoryText(categoryValue) {
  return categoryService.getCategoryText(categoryValue)
}

/**
 * 便捷的获取分类颜色函数
 * @param {String} categoryValue 分类值
 * @returns {String} 颜色值
 */
export function getCategoryColor(categoryValue) {
  return categoryService.getCategoryColor(categoryValue)
}
