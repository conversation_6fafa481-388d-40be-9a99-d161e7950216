package org.jeecg.modules.jianying.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

/**
 * 背景音效搜索请求参数
 * 
 * <AUTHOR>
 * @date 2025-01-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SoundEffectsSearchRequest extends BaseJianyingRequest {
    
    @ApiModelProperty(value = "搜索关键词（必填）", required = true, example = "打字声")
    @NotBlank(message = "搜索关键词不能为空")
    @JsonProperty("keyword")
    private String zjKeyword;
    
    @Override
    public String getSummary() {
        return "SoundEffectsSearchRequest{" +
               "accessKey=" + (getAccessKey() != null && getAccessKey().length() > 10 ? 
                              getAccessKey().substring(0, 10) + "***" : "null") +
               ", keyword=" + zjKeyword +
               "}";
    }
    
    /**
     * 验证请求参数
     * 
     * @return true表示验证通过，false表示验证失败
     */
    @Override
    public boolean validateBase() {
        if (!super.validateBase()) {
            return false;
        }
        
        if (zjKeyword == null || zjKeyword.trim().isEmpty()) {
            return false;
        }
        
        return true;
    }
}
