{"version": 3, "file": "tos.cjs.production.min.js", "sources": ["../src/TosServerError.ts", "../src/TosClientError.ts", "../src/CancelError.ts", "../src/utils.ts", "../src/methods/object/multipart/createMultipartUpload.ts", "../src/methods/object/multipart/listParts.ts", "../src/mime-types.ts", "../src/universal/crc.ts", "../src/universal/crc.browser.ts", "../src/universal/rate-limiter.ts", "../src/universal/rate-limiter.browser.ts", "../src/methods/object/sharedTypes.ts", "../src/methods/object/utils.ts", "../src/interface.ts", "../src/log.ts", "../src/axios.ts", "../src/universal/crypto.browser.ts", "../src/universal/crypto.ts", "../src/methods/object/multipart/uploadPart.ts", "../src/methods/object/multipart/completeMultipartUpload.ts", "../src/methods/object/multipart/uploadFile.ts", "../src/TosExportEnum.ts", "../src/methods/object/multipart/resumableCopyObject.ts", "../src/methods/object/headObject.ts", "../src/methods/object/multipart/uploadPartCopy.ts", "../src/methods/object/copyObject.ts", "../src/methods/object/getObject.ts", "../src/methods/object/downloadFile.ts", "../src/signatureV4.ts", "../src/axios-miniprogram-adapter/utils/platForm.ts", "../src/axios-miniprogram-adapter/index.ts", "../src/axios-miniprogram-adapter/utils/encoder.ts", "../src/methods/base.ts", "../src/methods/object/listObjects.ts", "../src/methods/object/listObjectsType2.ts", "../src/ShareLinkClient.ts", "../src/methods/bucket/base.ts", "../src/methods/bucket/acl.ts", "../src/methods/object/putObject.ts", "../src/methods/object/fetch.ts", "../src/methods/object/getPreSignedUrl.ts", "../src/methods/object/deleteObject.ts", "../src/methods/object/renameObject.ts", "../src/methods/object/deleteMultiObjects.ts", "../src/methods/object/acl/index.ts", "../src/methods/object/multipart/abortMultipartUpload.ts", "../src/methods/object/multipart/listMultipartUploads.ts", "../src/methods/object/appendObject.ts", "../src/methods/object/setObjectMeta.ts", "../src/methods/object/calculatePostSignature.ts", "../src/handleEmptyServerError.ts", "../src/methods/bucket/policy.ts", "../src/methods/bucket/versioning.ts", "../src/methods/object/preSignedPolicyURL.ts", "../src/methods/bucket/getBucketLocation.ts", "../src/methods/bucket/cors.ts", "../src/methods/bucket/lifecycle.ts", "../src/methods/bucket/encryption.ts", "../src/methods/bucket/mirrorback.ts", "../src/methods/object/tagging.ts", "../src/methods/bucket/replication.ts", "../src/methods/bucket/website.ts", "../src/methods/bucket/notification.ts", "../src/methods/bucket/customDomain.ts", "../src/methods/bucket/realTimeLog.ts", "../src/methods/bucket/inventory.ts", "../src/methods/qosPolicy/index.ts", "../src/methods/batch/index.ts", "../src/methods/bucket/tag.ts", "../src/methods/bucket/payByTraffic.ts", "../src/methods/bucket/img.ts", "../src/methods/bucket/intelligenttiering.ts", "../src/methods/bucket/rename.ts", "../src/methods/object/restoreObject.ts", "../src/methods/storageLens/index.ts", "../src/methods/bucket/notificationType2.ts", "../src/methods/object/putSymlink.ts", "../src/methods/object/getSymlink.ts", "../src/methods/bucket/acceleration.ts", "../src/methods/bucket/accessMonitor.ts", "../src/methods/mrap/index.tsx", "../src/methods/mrap/mirror.tsx", "../src/methods/bucket/media.ts", "../src/methods/bucket/trash.ts", "../src/InnerClient.ts", "../src/browser-index.ts"], "sourcesContent": ["import { AxiosResponse } from 'axios';\nimport { Headers } from './interface';\n\nexport interface TosServerErrorData {\n  Code: string;\n  HostId: string;\n  Message: string;\n  RequestId: string;\n  EC?: string;\n}\n\nexport class TosServerError extends Error {\n  /**\n   * is original from backend, equals `data.Code`\n   */\n  public code: string;\n\n  /**\n   * the body when backend errors\n   */\n  public data: TosServerErrorData;\n  /**\n   * status code\n   */\n  public statusCode: number;\n  /**\n   * response headers\n   */\n  public headers: Headers;\n\n  /**\n   * identifies the errored request, equals to headers['x-tos-request-id'].\n   * If you has any question about the request, please send the requestId and id2 to TOS worker.\n   */\n  public requestId: string;\n\n  /**\n   * identifies the errored request, equals to headers['x-tos-id-2'].\n   * If you has any question about the request, please send the requestId and id2 to TOS worker.\n   */\n  public id2: string;\n\n  constructor(response: AxiosResponse<TosServerErrorData>) {\n    const { data } = response;\n    super(data.Message);\n\n    // https://www.dannyguo.com/blog/how-to-fix-instanceof-not-working-for-custom-errors-in-typescript/\n    Object.setPrototypeOf(this, TosServerError.prototype);\n\n    this.data = data;\n    this.code = data.Code;\n    this.statusCode = response.status;\n    this.headers = response.headers;\n    this.requestId = response.headers['x-tos-request-id'];\n    this.id2 = response.headers['x-tos-id-2'];\n  }\n}\n\nexport default TosServerError;\n\nexport enum TosServerCode {\n  NoSuchBucket = 'NoSuchBucket',\n  NoSuchKey = 'NoSuchKey',\n  AccessDenied = 'AccessDenied',\n  MalformedAcl = 'MalformedAclError',\n  UnexpectedContent = 'UnexpectedContent',\n  InvalidRequest = 'InvalidRequest',\n  MissingSecurityHeader = 'MissingSecurityHeader',\n  InvalidArgument = 'InvalidArgument',\n  EntityTooSmall = 'EntityTooSmall',\n  InvalidBucketName = 'InvalidBucketName',\n  BucketNotEmpty = 'BucketNotEmpty',\n  TooManyBuckets = 'TooManyBuckets',\n  BucketAlreadyExists = 'BucketAlreadyExists',\n  MalformedBody = 'MalformedBody',\n  NoSuchLifecycleConfiguration = 'NoSuchLifecycleConfiguration',\n  ReplicationConfigurationNotFound = 'ReplicationConfigurationNotFoundError',\n  InvalidLocationConstraint = 'InvalidLocationConstraint',\n  AuthorizationQueryParametersError = 'AuthorizationQueryParametersError',\n  RequestTimeTooSkewed = 'RequestTimeTooSkewed',\n  SignatureDoesNotMatch = 'SignatureDoesNotMatch',\n  RequestedRangeNotSatisfiable = 'Requested Range Not Satisfiable',\n  PreconditionFailed = 'PreconditionFailed',\n  BadDigest = 'BadDigest',\n  InvalidDigest = 'InvalidDigest',\n  EntityTooLarge = 'EntityTooLarge',\n  UnImplemented = 'UnImplemented',\n  MethodNotAllowed = 'MethodNotAllowed',\n  InvalidAccessKeyId = 'InvalidAccessKeyId',\n  InvalidSecurityToken = 'InvalidSecurityToken',\n  ContentSHA256Mismatch = 'ContentSHA256Mismatch',\n  ExceedQPSLimit = 'ExceedQPSLimit',\n  ExceedRateLimit = 'ExceedRateLimit',\n  NoSuchCORSConfiguration = 'NoSuchCORSConfiguration',\n  NoSuchMirrorConfiguration = 'NoSuchMirrorConfiguration',\n  NoSuchWebsiteConfiguration = 'NoSuchWebsiteConfiguration',\n  MissingRequestBody = 'MissingRequestBodyError',\n  BucketAlreadyOwnedByYou = 'BucketAlreadyOwnedByYou',\n  NoSuchBucketPolicy = 'NoSuchBucketPolicy',\n  PolicyTooLarge = 'PolicyTooLarge',\n  MalformedPolicy = 'MalformedPolicy',\n  InvalidKey = 'InvalidKey',\n  MirrorFailed = 'MirrorFailed',\n  Timeout = 'Timeout',\n  OffsetNotMatched = 'OffsetNotMatched',\n  NotAppendable = 'NotAppendable',\n  ContextCanceled = 'ContextCanceled',\n  InternalError = 'InternalError',\n  TooManyRequests = 'TooManyRequests',\n  TimeOut = 'TimeOut',\n  ConcurrencyUpdateObjectLimit = 'ConcurrencyUpdateObjectLimit',\n  DuplicateUpload = 'DuplicateUpload',\n  DuplicateObject = 'DuplicateObject',\n  InvalidVersionId = 'InvalidVersionId',\n  StorageClassNotMatch = 'StorageClassNotMatch',\n  UploadStatusNotUploading = 'UploadStatusNotUploading',\n  PartSizeNotMatch = 'PartSizeNotMatch',\n  NoUploadPart = 'NoUploadPart',\n  PartsLenInvalid = 'PartsLenInvalid',\n  PartsIdxSmall = 'PartsIdxSmall',\n  PartSizeSmall = 'PartSizeSmall',\n  PrefixNotNextKeyPrefix = 'PrefixNotNextKeyPrefix',\n  InvalidPart = 'InvalidPart',\n  InvalidPartOffset = 'InvalidPartOffset',\n  MismatchObject = 'MismatchObject',\n  UploadStatusMismatch = 'UploadStatusMismatch',\n  CompletingStatusNoExpiration = 'CompletingStatusNoExpiration',\n  Found = 'Found',\n  InvalidRedirectLocation = 'InvalidRedirectLocation',\n}\n", "export class TosClientError extends Error {\n  constructor(message: string) {\n    super(message);\n\n    // https://www.dannyguo.com/blog/how-to-fix-instanceof-not-working-for-custom-errors-in-typescript/\n    Object.setPrototypeOf(this, TosClientError.prototype);\n  }\n}\n\nexport default TosClientError;\n", "export class CancelError extends Error {\n  constructor(message: string) {\n    super(message);\n\n    // https://www.dannyguo.com/blog/how-to-fix-instanceof-not-working-for-custom-errors-in-typescript/\n    Object.setPrototypeOf(this, CancelError.prototype);\n  }\n}\n", "import { Readable } from 'stream';\nimport {\n  CamelCasedPropertiesDeep,\n  KebabCasedPropertiesDeep,\n  PascalCasedPropertiesDeep,\n} from 'type-fest';\nimport get from 'lodash/get';\nimport set from 'lodash/set';\nimport { CancelError } from './CancelError';\nimport TosClientError from './TosClientError';\nimport { Headers } from './interface';\nimport { TOSConstructorOptions, TosResponse } from './methods/base';\nimport qs from 'qs';\nimport TosServerError from './TosServerError';\nimport { CRCCls } from './universal/crc';\nimport * as fsp from './nodejs/fs-promises';\nimport { ReadStream, WriteStream } from 'fs';\n\n// obj[key] must be a array\nexport const makeArrayProp = (obj: unknown) => (key: string) => {\n  if (obj == null || typeof obj !== 'object') {\n    return;\n  }\n\n  const value = get(obj, key);\n  if (!Array.isArray(value)) {\n    set(obj, key, value == null ? [] : [value]);\n  }\n};\n\nconst makeConvertProp = (convertMethod: (prop: string) => string) => {\n  const finalMethod = <T = unknown>(target: T): T => {\n    if (Array.isArray(target)) {\n      return target.map((it) => finalMethod(it)) as unknown as T;\n    }\n\n    if (typeof target === 'string') {\n      return convertMethod(target) as unknown as T;\n    }\n\n    if (typeof target === 'object' && target != null) {\n      type Obj = Record<string, unknown>;\n      const ret = Object.keys(target).reduce((acc: Obj, key: string) => {\n        const nextKey = finalMethod(key);\n        acc[nextKey] = (target as Obj)[key];\n        return acc;\n      }, {});\n      return ret as unknown as T;\n    }\n\n    return target;\n  };\n\n  return finalMethod;\n};\n\nexport const covertCamelCase2Kebab = makeConvertProp((camelCase: string) => {\n  return camelCase.replace(/[A-Z]/g, '-$&').toLowerCase();\n}) as <T = unknown>(target: T) => KebabCasedPropertiesDeep<T>;\n\nexport const convertUpperCamelCase2Normal = makeConvertProp(\n  (upperCamelCase: string) => {\n    return upperCamelCase[0].toLocaleLowerCase() + upperCamelCase.slice(1);\n  }\n) as <T = unknown>(target: T) => CamelCasedPropertiesDeep<T>;\n\nexport const convertNormalCamelCase2Upper = makeConvertProp(\n  (normalCamelCase: string) => {\n    return normalCamelCase[0].toUpperCase() + normalCamelCase.slice(1);\n  }\n) as <T = unknown>(target: T) => PascalCasedPropertiesDeep<T>;\n\nexport const getSortedQueryString = (query: Record<string, any>) => {\n  const searchParts: string[] = [];\n  Object.keys(query)\n    .sort()\n    .forEach((key) => {\n      searchParts.push(\n        `${encodeURIComponent(key)}=${encodeURIComponent(query[key])}`\n      );\n    });\n  return searchParts.join('&');\n};\n\nexport const normalizeHeadersKey = <T extends Headers>(\n  headers: T | undefined\n): T => {\n  const headers1: Headers = headers || {};\n  const headers2: Headers = {};\n  Object.keys(headers1).forEach((key: string) => {\n    if (headers1[key] != null) {\n      headers2[key] = headers1[key];\n    }\n  });\n\n  const headers3: Headers = {};\n  Object.keys(headers2).forEach((key: string) => {\n    const newKey = key.toLowerCase();\n    headers3[newKey] = headers2[key];\n  });\n\n  return headers3 as T;\n};\n\nexport const encodeHeadersValue = (headers: Headers) => {\n  const header2: Headers = {};\n  Object.entries(headers).forEach(([key, value]) => {\n    header2[key] = `${value}`\n      // reference:\n      //  https://stackoverflow.com/questions/38345372/why-is-length-2\n      .match(/./gu)!\n      .map((ch: string) => {\n        if (ch.length > 1 || ch.charCodeAt(0) >= 128) {\n          return encodeURIComponent(ch);\n        }\n        return ch;\n      })\n      .join('');\n  });\n  return header2;\n};\n\n// TODO: getRegion from endpoint, maybe user passes it is better.\nexport const getRegion = (endpoint: string) => {\n  const region = endpoint.match(/-(\\w+).volces.com/);\n  if (!region) {\n    return 'cn-beijing';\n  }\n  return `cn-${region[1]}`;\n};\n\nexport const getEndpoint = (region: string) => {\n  return `tos-${region}.volces.com`;\n};\n\nexport const normalizeProxy = (proxy: TOSConstructorOptions['proxy']) => {\n  if (typeof proxy === 'string') {\n    proxy = {\n      url: proxy,\n    };\n  }\n\n  if (\n    proxy &&\n    proxy?.needProxyParams == null &&\n    process.env.TARGET_ENVIRONMENT === 'browser'\n  ) {\n    proxy.needProxyParams = true;\n  }\n\n  return proxy;\n};\n\nexport async function safeAwait<T>(\n  p: T\n): Promise<[null, Awaited<T>] | [any, null]> {\n  try {\n    const v = await p;\n    return [null, v];\n  } catch (err) {\n    return [err, null];\n  }\n}\n\nexport function safeSync<T>(func: () => T): [any, null] | [null, T] {\n  try {\n    const ret = func();\n    return [null, ret];\n  } catch (err) {\n    return [err, null];\n  }\n}\n\nexport function isBlob(obj: unknown): obj is Blob {\n  return typeof Blob !== 'undefined' && obj instanceof Blob;\n}\n\nexport function isBuffer(obj: unknown): obj is Buffer {\n  return typeof Buffer !== 'undefined' && obj instanceof Buffer;\n}\n\nexport function isReadable(obj: unknown): obj is NodeJS.ReadableStream {\n  if (process.env.TARGET_ENVIRONMENT !== 'node') {\n    return false;\n  }\n\n  return obj instanceof Readable;\n}\n\nexport function isValidNumber(v: number): v is number {\n  return !!v || v == 0;\n}\n\nexport function obj2QueryStr(v?: Record<string, unknown>) {\n  if (!v) {\n    return '';\n  }\n  return Object.keys(v)\n    .map((key) => {\n      const vStr = `${v[key]}`;\n      return `${encodeURIComponent(key)}=${encodeURIComponent(vStr)}`;\n    })\n    .join('&');\n}\n\nexport function isCancelError(err: any) {\n  return err instanceof CancelError;\n}\n\nexport const DEFAULT_PART_SIZE = 20 * 1024 * 1024; // 20 MB\n\nexport const getGMTDateStr = (v: Date) => {\n  return v.toUTCString();\n};\nconst gmtDateOrStr = (v: Date | string) => {\n  if (typeof v === 'string') {\n    return v;\n  }\n  return v.toUTCString();\n};\n\nexport const requestHeadersMap: Record<\n  string,\n  string | [string, (v: any) => string] | ((v: any) => Record<string, string>)\n> = {\n  projectName: 'x-tos-project-name',\n  encodingType: 'encoding-type',\n  cacheControl: 'cache-control',\n  contentDisposition: 'content-disposition',\n  contentLength: 'content-length',\n  contentMD5: 'content-md5',\n  contentSHA256: 'x-tos-content-sha256',\n  contentEncoding: 'content-encoding',\n  contentLanguage: 'content-language',\n  contentType: 'content-type',\n  expires: ['expires', getGMTDateStr],\n  range: 'range',\n\n  ifMatch: 'if-match',\n  ifModifiedSince: ['if-modified-since', gmtDateOrStr],\n  ifNoneMatch: 'if-none-match',\n  ifUnmodifiedSince: ['if-unmodified-since', gmtDateOrStr],\n\n  acl: 'x-tos-acl',\n  grantFullControl: 'x-tos-grant-full-control',\n  grantRead: 'x-tos-grant-read',\n  grantReadAcp: 'x-tos-grant-read-acp',\n  grantWrite: 'x-tos-grant-write',\n  grantWriteAcp: 'x-tos-grant-write-acp',\n\n  serverSideEncryption: 'x-tos-server-side-encryption',\n  serverSideDataEncryption: 'x-tos-server-side-data-encryption',\n  ssecAlgorithm: 'x-tos-server-side-encryption-customer-algorithm',\n  ssecKey: 'x-tos-server-side-encryption-customer-key',\n  ssecKeyMD5: 'x-tos-server-side-encryption-customer-key-md5',\n\n  copySourceRange: 'x-tos-copy-source-range',\n  copySourceIfMatch: 'x-tos-copy-source-if-match',\n  copySourceIfModifiedSince: [\n    'x-tos-copy-source-if-modified-since',\n    gmtDateOrStr,\n  ],\n  copySourceIfNoneMatch: 'x-tos-copy-source-if-none-match',\n  copySourceIfUnmodifiedSince: 'x-tos-copy-source-if-unmodified-since',\n  copySourceSSECAlgorithm:\n    'x-tos-copy-source-server-side-encryption-customer-algorithm',\n  copySourceSSECKey: 'x-tos-copy-source-server-side-encryption-customer-key',\n  copySourceSSECKeyMD5:\n    'x-tos-copy-source-server-side-encryption-customer-key-MD5',\n\n  metadataDirective: 'x-tos-metadata-directive',\n  meta: (v: any) => {\n    return Object.keys(v).reduce((prev, key) => {\n      prev[`x-tos-meta-${key}`] = `${v[key]}`;\n      return prev;\n    }, {} as Record<string, string>);\n  },\n  websiteRedirectLocation: 'x-tos-website-redirect-location',\n  storageClass: 'x-tos-storage-class',\n  azRedundancy: 'x-tos-az-redundancy',\n  trafficLimit: 'x-tos-traffic-limit',\n  callback: 'x-tos-callback',\n  callbackVar: 'x-tos-callback-var',\n  allowSameActionOverlap: ['x-tos-allow-same-action-overlap', (v) => String(v)],\n  symLinkTargetKey: 'x-tos-symlink-target',\n  symLinkTargetBucket: 'x-tos-symlink-bucket',\n  forbidOverwrite: 'x-tos-forbid-overwrite',\n  bucketType: 'x-tos-bucket-type',\n  recursiveMkdir: 'x-tos-recursive-mkdir',\n};\n// type RequestHeadersMapKeys = keyof typeof requestHeadersMap;\n\nexport const requestQueryMap: Record<\n  string,\n  string | [string, (v: any) => string] | ((v: any) => Record<string, string>)\n> = {\n  versionId: 'versionId',\n  process: 'x-tos-process',\n  saveBucket: 'x-tos-save-bucket',\n  saveObject: 'x-tos-save-object',\n\n  responseCacheControl: 'response-cache-control',\n  responseContentDisposition: 'response-content-disposition',\n  responseContentEncoding: 'response-content-encoding',\n  responseContentLanguage: 'response-content-language',\n  responseContentType: 'response-content-type',\n  responseExpires: ['response-expires', (v: Date) => v.toUTCString()],\n};\n\nexport function fillRequestHeaders<T extends { headers?: Headers }>(\n  v: T,\n  // keys: (keyof T & RequestHeadersMapKeys)[]\n  keys: (keyof T & string)[]\n) {\n  if (!keys.length) {\n    return;\n  }\n\n  const headers = v.headers || {};\n  v.headers = headers;\n\n  function setOneHeader(k: string, v: string) {\n    if (headers[k] == null) {\n      headers[k] = v;\n    }\n  }\n\n  keys.forEach((k) => {\n    const confV = requestHeadersMap[k];\n    if (!confV) {\n      // maybe warning\n      throw new TosClientError(\n        `\\`${k}\\` isn't in keys of \\`requestHeadersMap\\``\n      );\n    }\n\n    const oriValue = v[k];\n    if (oriValue == null) {\n      return;\n    }\n\n    const oriValueStr = `${oriValue}`;\n    if (typeof confV === 'string') {\n      return setOneHeader(confV, oriValueStr);\n    }\n\n    if (Array.isArray(confV)) {\n      const newKey = confV[0];\n      const newValue = confV[1](oriValue);\n      return setOneHeader(newKey, newValue);\n    }\n\n    const obj = confV(oriValue);\n    Object.entries(obj).forEach(([k, v]) => {\n      setOneHeader(k, v);\n    });\n  });\n}\n\nexport function fillRequestQuery<T>(\n  v: T,\n  query: Record<string, unknown>,\n  keys: (keyof T & string)[]\n) {\n  if (!keys.length) {\n    return;\n  }\n\n  function setOneKey(k: string, v: string) {\n    if (query[k] == null) {\n      query[k] = v;\n    }\n  }\n\n  keys.forEach((k) => {\n    const confV = requestQueryMap[k];\n    if (!confV) {\n      // maybe warning\n      throw new TosClientError(`\\`${k}\\` isn't in keys of \\`requestQueryMap\\``);\n    }\n\n    const oriValue = v[k];\n    if (oriValue == null) {\n      return;\n    }\n\n    const oriValueStr = `${oriValue}`;\n    if (typeof confV === 'string') {\n      return setOneKey(confV, oriValueStr);\n    }\n\n    if (Array.isArray(confV)) {\n      const newKey = confV[0];\n      const newValue = confV[1](oriValue);\n      return setOneKey(newKey, newValue);\n    }\n\n    const obj = confV(oriValue);\n    Object.entries(obj).forEach(([k, v]) => {\n      setOneKey(k, v);\n    });\n  });\n}\n\nexport const paramsSerializer = (params: Record<string, string>) => {\n  return qs.stringify(params);\n};\n\nexport function getNormalDataFromError<T>(\n  data: T,\n  err: TosServerError\n): TosResponse<T> {\n  return {\n    data,\n    statusCode: err.statusCode,\n    headers: err.headers,\n    requestId: err.requestId,\n    id2: err.id2,\n  };\n}\nexport const streamToBuf = async (\n  stream: NodeJS.ReadableStream\n): Promise<Buffer> => {\n  let buf = Buffer.from([]);\n  return new Promise((resolve, reject) => {\n    stream.on('data', (data) => {\n      buf = Buffer.concat([buf, data]);\n    });\n    stream.on('end', () => {\n      resolve(buf);\n    });\n    stream.on('error', (err) => {\n      reject(err);\n    });\n  });\n};\n\nexport function checkCRC64WithHeaders(crc: CRCCls | string, headers: Headers) {\n  const serverCRC64 = headers['x-tos-hash-crc64ecma'];\n  if (serverCRC64 == null) {\n    if (process.env.TARGET_ENVIRONMENT === 'browser') {\n      console.warn(\n        \"No x-tos-hash-crc64ecma in response's headers, please see https://www.volcengine.com/docs/6349/127737 to add `x-tos-hash-crc64ecma` to Expose-Headers field.\"\n      );\n    } else {\n    }\n    return;\n  }\n\n  const crcStr = typeof crc === 'string' ? crc : crc.getCrc64();\n  if (crcStr !== serverCRC64) {\n    throw new TosClientError(\n      `validate file crc64 failed. Expect crc64 ${serverCRC64}, actual crc64 ${crcStr}. Please try again.`\n    );\n  }\n}\n\nexport const makeStreamErrorHandler = (prefix?: string) => (err: any) => {\n  console.log(`${prefix || ''} stream error:`, err);\n};\n\nexport enum HttpHeader {\n  LastModified = 'last-modified',\n  ContentLength = 'content-length',\n  AcceptEncoding = 'accept-encoding',\n  ContentEncoding = 'content-encoding',\n  ContentMD5 = 'content-md5',\n  TosRawContentLength = 'x-tos-raw-content-length',\n  TosTrailer = 'x-tos-trailer',\n  TosHashCrc64ecma = 'x-tos-hash-crc64ecma',\n  TosContentSha256 = 'x-tos-content-sha256',\n  TosDecodedContentLength = 'x-tos-decoded-content-length',\n  TosEc = 'x-tos-ec',\n  TosRequestId = 'x-tos-request-id',\n}\n\n/**\n * make async tasks serial\n * @param makeTask\n * @returns\n */\nexport const makeSerialAsyncTask = (makeTask: () => Promise<void>) => {\n  let lastTask = Promise.resolve();\n  return async () => {\n    lastTask = lastTask.then(() => makeTask());\n    return lastTask;\n  };\n};\n\nexport const safeParseCheckpointFile = async (filePath: string) => {\n  try {\n    return JSON.parse(await fsp.readFile(filePath, 'utf-8'));\n  } catch (err) {\n    console.warn(\"checkpoint's content is not a valid JSON\");\n    return undefined;\n  }\n};\n\nexport const makeRetryStreamAutoClose = (\n  makeStream: () => NodeJS.ReadableStream | ReadStream\n) => {\n  let lastStream: ReadStream | NodeJS.ReadableStream | null = null;\n  const makeRetryStream = () => {\n    if (lastStream) {\n      tryDestroy(\n        lastStream,\n        new Error('retry new stream by makeRetryStreamAutoClose')\n      );\n    }\n\n    lastStream = makeStream();\n    return lastStream;\n  };\n\n  return {\n    getLastStream: () => lastStream,\n    make: makeRetryStream,\n  };\n};\n\nexport const tryDestroy = (\n  stream:\n    | NodeJS.ReadableStream\n    | ReadStream\n    | NodeJS.WritableStream\n    | WriteStream\n    | null\n    | undefined,\n  err: any\n) => {\n  if (stream && 'destroy' in stream && typeof stream.destroy === 'function') {\n    if ('destroyed' in stream && !stream.destroyed) {\n      stream.destroy(err);\n    }\n  }\n};\nexport const pipeStreamWithErrorHandle = <\n  Src extends NodeJS.ReadableStream | ReadStream,\n  Dest extends NodeJS.WritableStream | WriteStream\n>(\n  src: Src,\n  dest: Dest,\n  label: string\n): Dest => {\n  dest.on('error', makeStreamErrorHandler(label));\n  src.on('error', (err) => tryDestroy(dest, err));\n  dest.on('error', (err) => tryDestroy(src, err));\n  return src.pipe(dest) as Dest;\n};\n", "import TOSBase from '../../base';\nimport { fillRequestHeaders, normalizeHeadersKey } from '../../../utils';\nimport { Acl } from '../../../interface';\nimport { StorageClassType } from '../../../TosExportEnum';\n\nexport interface CreateMultipartUploadInput {\n  bucket?: string;\n  key: string;\n\n  encodingType?: string;\n  cacheControl?: string;\n  contentDisposition?: string;\n  contentEncoding?: string;\n  contentLanguage?: string;\n  contentType?: string;\n  expires?: Date;\n\n  acl?: Acl;\n  grantFullControl?: string;\n  grantRead?: string;\n  grantReadAcp?: string;\n  grantWriteAcp?: string;\n\n  ssecAlgorithm?: string;\n  ssecKey?: string;\n  ssecKeyMD5?: string;\n\n  serverSideEncryption?: string;\n  /** @private unstable */\n  serverSideDataEncryption?: string;\n\n  meta?: Record<string, string>;\n  websiteRedirectLocation?: string;\n  storageClass?: StorageClassType;\n  forbidOverwrite?: boolean;\n\n  headers?: {\n    [key: string]: string | undefined;\n    'encoding-type'?: string;\n    'Content-Disposition'?: string;\n    'x-tos-acl'?: Acl;\n    'content-type'?: string;\n    'x-tos-grant-full-control'?: string;\n    'x-tos-grant-read'?: string;\n    'x-tos-grant-read-acp'?: string;\n    'x-tos-grant-write-acp'?: string;\n    'x-tos-server-side-encryption-customer-algorithm'?: string;\n    'x-tos-server-side-encryption-customer-key'?: string;\n    'x-tos-server-side-encryption-customer-key-md5'?: string;\n    'x-tos-website-redirect-location'?: string;\n    'x-tos-storage-class'?: string;\n    'x-tos-server-side-encryption'?: string;\n    ['x-tos-forbid-overwrite']?: string;\n  };\n}\n\nexport interface CreateMultipartUploadOutput {\n  UploadId: string;\n  Bucket: string;\n  Key: string;\n  EncodingType?: string;\n}\n\nexport async function createMultipartUpload(\n  this: TOSBase,\n  input: CreateMultipartUploadInput | string\n) {\n  input = this.normalizeObjectInput(input);\n  const headers = normalizeHeadersKey(input.headers);\n  input.headers = headers;\n  fillRequestHeaders(input, [\n    'encodingType',\n    'cacheControl',\n    'contentDisposition',\n    'contentEncoding',\n    'contentLanguage',\n    'contentType',\n    'expires',\n\n    'acl',\n    'grantFullControl',\n    'grantRead',\n    'grantReadAcp',\n    'grantWriteAcp',\n\n    'ssecAlgorithm',\n    'ssecKey',\n    'ssecKeyMD5',\n    'serverSideEncryption',\n    'serverSideDataEncryption',\n\n    'meta',\n    'websiteRedirectLocation',\n    'storageClass',\n    'forbidOverwrite',\n  ]);\n\n  this.setObjectContentTypeHeader(input, headers);\n\n  return this._fetchObject<CreateMultipartUploadOutput>(\n    input,\n    'POST',\n    { uploads: '' },\n    headers,\n    ''\n  );\n}\n", "import { covertCamelCase2Kebab, makeArrayProp } from '../../../utils';\nimport TOSBase from '../../base';\n\ninterface ListPartInput {\n  bucket?: string;\n  key: string;\n  uploadId: string;\n  maxParts?: number;\n  partNumberMarker?: number;\n  encodingType?: string;\n}\n\ninterface ListPartOutput {\n  Bucket: string;\n  Key: string;\n  UploadId: string;\n  PartNumberMarker: number;\n  NextPartNumberMarker: number;\n  MaxParts: number;\n  IsTruncated: boolean;\n  StorageClass: string;\n  Owner: { ID: string; DisplayName: string };\n  Parts: {\n    PartNumber: number;\n    LastModified: string;\n    ETag: string;\n    Size: number;\n  }[];\n}\n\n// the part except last one must be >= 5 MB\n// the last part is no size limit\nexport const MIN_PART_SIZE_EXCEPT_LAST_ONE = 5 * 1024 * 1024;\nexport const MAX_PART_NUMBER = 10000;\n\nexport const calculateSafePartSize = (\n  totalSize: number,\n  expectPartSize: number,\n  showWarning = false\n) => {\n  let partSize = expectPartSize;\n  if (expectPartSize < MIN_PART_SIZE_EXCEPT_LAST_ONE) {\n    partSize = MIN_PART_SIZE_EXCEPT_LAST_ONE;\n    if (showWarning) {\n      console.warn(\n        `partSize has been set to ${partSize}, because the partSize you provided is less than the minimal size of multipart`\n      );\n    }\n  }\n  const minSize = Math.ceil(totalSize / MAX_PART_NUMBER);\n  if (expectPartSize < minSize) {\n    partSize = minSize;\n    if (showWarning) {\n      console.warn(\n        `partSize has been set to ${partSize}, because the partSize you provided causes the number of part excesses 10,000`\n      );\n    }\n  }\n\n  return partSize;\n};\n\nexport async function listParts(this: TOSBase, input: ListPartInput) {\n  const { bucket, key, uploadId, ...nextQuery } = input;\n  const ret = await this._fetchObject<ListPartOutput>(\n    input,\n    'GET',\n    {\n      uploadId,\n      ...covertCamelCase2Kebab(nextQuery),\n    },\n    {}\n  );\n  const arrayProp = makeArrayProp(ret.data);\n  arrayProp('Parts');\n\n  return ret;\n}\n", "// alias with GoSDK\n// refer https://github.com/volcengine/ve-tos-golang-sdk/blob/main/tos/mime.go\nexport const mimeTypes: Record<string, string | undefined> = {\n  '3gp': 'video/3gpp',\n  '7z': 'application/x-7z-compressed',\n  abw: 'application/x-abiword',\n  ai: 'application/postscript',\n  aif: 'audio/x-aiff',\n  aifc: 'audio/x-aiff',\n  aiff: 'audio/x-aiff',\n  alc: 'chemical/x-alchemy',\n  amr: 'audio/amr',\n  anx: 'application/annodex',\n  apk: 'application/vnd.android.package-archive',\n  appcache: 'text/cache-manifest',\n  art: 'image/x-jg',\n  asc: 'text/plain',\n  asf: 'video/x-ms-asf',\n  aso: 'chemical/x-ncbi-asn1-binary',\n  asx: 'video/x-ms-asf',\n  atom: 'application/atom+xml',\n  atomcat: 'application/atomcat+xml',\n  atomsrv: 'application/atomserv+xml',\n  au: 'audio/basic',\n  avi: 'video/x-msvideo',\n  awb: 'audio/amr-wb',\n  axa: 'audio/annodex',\n  axv: 'video/annodex',\n  b: 'chemical/x-molconn-Z',\n  bak: 'application/x-trash',\n  bat: 'application/x-msdos-program',\n  bcpio: 'application/x-bcpio',\n  bib: 'text/x-bibtex',\n  bin: 'application/octet-stream',\n  bmp: 'image/x-ms-bmp',\n  boo: 'text/x-boo',\n  book: 'application/x-maker',\n  brf: 'text/plain',\n  bsd: 'chemical/x-crossfire',\n  c: 'text/x-csrc',\n  'c++': 'text/x-c++src',\n  c3d: 'chemical/x-chem3d',\n  cab: 'application/x-cab',\n  cac: 'chemical/x-cache',\n  cache: 'chemical/x-cache',\n  cap: 'application/vnd.tcpdump.pcap',\n  cascii: 'chemical/x-cactvs-binary',\n  cat: 'application/vnd.ms-pki.seccat',\n  cbin: 'chemical/x-cactvs-binary',\n  cbr: 'application/x-cbr',\n  cbz: 'application/x-cbz',\n  cc: 'text/x-c++src',\n  cda: 'application/x-cdf',\n  cdf: 'application/x-cdf',\n  cdr: 'image/x-coreldraw',\n  cdt: 'image/x-coreldrawtemplate',\n  cdx: 'chemical/x-cdx',\n  cdy: 'application/vnd.cinderella',\n  cef: 'chemical/x-cxf',\n  cer: 'chemical/x-cerius',\n  chm: 'chemical/x-chemdraw',\n  chrt: 'application/x-kchart',\n  cif: 'chemical/x-cif',\n  class: 'application/java-vm',\n  cls: 'text/x-tex',\n  cmdf: 'chemical/x-cmdf',\n  cml: 'chemical/x-cml',\n  cod: 'application/vnd.rim.cod',\n  com: 'application/x-msdos-program',\n  cpa: 'chemical/x-compass',\n  cpio: 'application/x-cpio',\n  cpp: 'text/x-c++src',\n  cpt: 'application/mac-compactpro',\n  cr2: 'image/x-canon-cr2',\n  crl: 'application/x-pkcs7-crl',\n  crt: 'application/x-x509-ca-cert',\n  crw: 'image/x-canon-crw',\n  csd: 'audio/csound',\n  csf: 'chemical/x-cache-csf',\n  csh: 'application/x-csh',\n  csm: 'chemical/x-csml',\n  csml: 'chemical/x-csml',\n  css: 'text/css',\n  csv: 'text/csv',\n  ctab: 'chemical/x-cactvs-binary',\n  ctx: 'chemical/x-ctx',\n  cu: 'application/cu-seeme',\n  cub: 'chemical/x-gaussian-cube',\n  cxf: 'chemical/x-cxf',\n  cxx: 'text/x-c++src',\n  d: 'text/x-dsrc',\n  davmount: 'application/davmount+xml',\n  dcm: 'application/dicom',\n  dcr: 'application/x-director',\n  ddeb: 'application/vnd.debian.binary-package',\n  dif: 'video/dv',\n  diff: 'text/x-diff',\n  dir: 'application/x-director',\n  djv: 'image/vnd.djvu',\n  djvu: 'image/vnd.djvu',\n  dl: 'video/dl',\n  dll: 'application/x-msdos-program',\n  dmg: 'application/x-apple-diskimage',\n  dms: 'application/x-dms',\n  doc: 'application/msword',\n  docm: 'application/vnd.ms-word.document.macroEnabled.12',\n  docx: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',\n  dot: 'application/msword',\n  dotm: 'application/vnd.ms-word.template.macroEnabled.12',\n  dotx: 'application/vnd.openxmlformats-officedocument.wordprocessingml.template',\n  dv: 'video/dv',\n  dvi: 'application/x-dvi',\n  dx: 'chemical/x-jcamp-dx',\n  dxr: 'application/x-director',\n  emb: 'chemical/x-embl-dl-nucleotide',\n  embl: 'chemical/x-embl-dl-nucleotide',\n  eml: 'message/rfc822',\n  eot: 'application/vnd.ms-fontobject',\n  eps: 'application/postscript',\n  eps2: 'application/postscript',\n  eps3: 'application/postscript',\n  epsf: 'application/postscript',\n  epsi: 'application/postscript',\n  erf: 'image/x-epson-erf',\n  es: 'application/ecmascript',\n  etx: 'text/x-setext',\n  exe: 'application/x-msdos-program',\n  ez: 'application/andrew-inset',\n  fb: 'application/x-maker',\n  fbdoc: 'application/x-maker',\n  fch: 'chemical/x-gaussian-checkpoint',\n  fchk: 'chemical/x-gaussian-checkpoint',\n  fig: 'application/x-xfig',\n  flac: 'audio/flac',\n  fli: 'video/fli',\n  flv: 'video/x-flv',\n  fm: 'application/x-maker',\n  frame: 'application/x-maker',\n  frm: 'application/x-maker',\n  gal: 'chemical/x-gaussian-log',\n  gam: 'chemical/x-gamess-input',\n  gamin: 'chemical/x-gamess-input',\n  gan: 'application/x-ganttproject',\n  gau: 'chemical/x-gaussian-input',\n  gcd: 'text/x-pcs-gcd',\n  gcf: 'application/x-graphing-calculator',\n  gcg: 'chemical/x-gcg8-sequence',\n  gen: 'chemical/x-genbank',\n  gf: 'application/x-tex-gf',\n  gif: 'image/gif',\n  gjc: 'chemical/x-gaussian-input',\n  gjf: 'chemical/x-gaussian-input',\n  gl: 'video/gl',\n  gnumeric: 'application/x-gnumeric',\n  gpt: 'chemical/x-mopac-graph',\n  gsf: 'application/x-font',\n  gsm: 'audio/x-gsm',\n  gtar: 'application/x-gtar',\n  gz: 'application/gzip',\n  h: 'text/x-chdr',\n  'h++': 'text/x-c++hdr',\n  hdf: 'application/x-hdf',\n  hh: 'text/x-c++hdr',\n  hin: 'chemical/x-hin',\n  hpp: 'text/x-c++hdr',\n  hqx: 'application/mac-binhex40',\n  hs: 'text/x-haskell',\n  hta: 'application/hta',\n  htc: 'text/x-component',\n  htm: 'text/html',\n  html: 'text/html',\n  hwp: 'application/x-hwp',\n  hxx: 'text/x-c++hdr',\n  ica: 'application/x-ica',\n  ice: 'x-conference/x-cooltalk',\n  ico: 'image/vnd.microsoft.icon',\n  ics: 'text/calendar',\n  icz: 'text/calendar',\n  ief: 'image/ief',\n  iges: 'model/iges',\n  igs: 'model/iges',\n  iii: 'application/x-iphone',\n  info: 'application/x-info',\n  inp: 'chemical/x-gamess-input',\n  ins: 'application/x-internet-signup',\n  iso: 'application/x-iso9660-image',\n  isp: 'application/x-internet-signup',\n  ist: 'chemical/x-isostar',\n  istr: 'chemical/x-isostar',\n  jad: 'text/vnd.sun.j2me.app-descriptor',\n  jam: 'application/x-jam',\n  jar: 'application/java-archive',\n  java: 'text/x-java',\n  jdx: 'chemical/x-jcamp-dx',\n  jmz: 'application/x-jmol',\n  jng: 'image/x-jng',\n  jnlp: 'application/x-java-jnlp-file',\n  jp2: 'image/jp2',\n  jpe: 'image/jpeg',\n  jpeg: 'image/jpeg',\n  jpf: 'image/jpx',\n  jpg: 'image/jpeg',\n  jpg2: 'image/jp2',\n  jpm: 'image/jpm',\n  jpx: 'image/jpx',\n  js: 'application/javascript',\n  json: 'application/json',\n  kar: 'audio/midi',\n  key: 'application/pgp-keys',\n  kil: 'application/x-killustrator',\n  kin: 'chemical/x-kinemage',\n  kml: 'application/vnd.google-earth.kml+xml',\n  kmz: 'application/vnd.google-earth.kmz',\n  kpr: 'application/x-kpresenter',\n  kpt: 'application/x-kpresenter',\n  ksp: 'application/x-kspread',\n  kwd: 'application/x-kword',\n  kwt: 'application/x-kword',\n  latex: 'application/x-latex',\n  lha: 'application/x-lha',\n  lhs: 'text/x-literate-haskell',\n  lin: 'application/bbolin',\n  lsf: 'video/x-la-asf',\n  lsx: 'video/x-la-asf',\n  ltx: 'text/x-tex',\n  ly: 'text/x-lilypond',\n  lyx: 'application/x-lyx',\n  lzh: 'application/x-lzh',\n  lzx: 'application/x-lzx',\n  m3g: 'application/m3g',\n  m3u: 'audio/x-mpegurl',\n  m3u8: 'application/x-mpegURL',\n  m4a: 'audio/mpeg',\n  maker: 'application/x-maker',\n  man: 'application/x-troff-man',\n  mbox: 'application/mbox',\n  mcif: 'chemical/x-mmcif',\n  mcm: 'chemical/x-macmolecule',\n  mdb: 'application/msaccess',\n  me: 'application/x-troff-me',\n  mesh: 'model/mesh',\n  mid: 'audio/midi',\n  midi: 'audio/midi',\n  mif: 'application/x-mif',\n  mkv: 'video/x-matroska',\n  mm: 'application/x-freemind',\n  mmd: 'chemical/x-macromodel-input',\n  mmf: 'application/vnd.smaf',\n  mml: 'text/mathml',\n  mmod: 'chemical/x-macromodel-input',\n  mng: 'video/x-mng',\n  moc: 'text/x-moc',\n  mol: 'chemical/x-mdl-molfile',\n  mol2: 'chemical/x-mol2',\n  moo: 'chemical/x-mopac-out',\n  mop: 'chemical/x-mopac-input',\n  mopcrt: 'chemical/x-mopac-input',\n  mov: 'video/quicktime',\n  movie: 'video/x-sgi-movie',\n  mp2: 'audio/mpeg',\n  mp3: 'audio/mpeg',\n  mp4: 'video/mp4',\n  mpc: 'chemical/x-mopac-input',\n  mpe: 'video/mpeg',\n  mpeg: 'video/mpeg',\n  mpega: 'audio/mpeg',\n  mpg: 'video/mpeg',\n  mpga: 'audio/mpeg',\n  mph: 'application/x-comsol',\n  mpv: 'video/x-matroska',\n  ms: 'application/x-troff-ms',\n  msh: 'model/mesh',\n  msi: 'application/x-msi',\n  mvb: 'chemical/x-mopac-vib',\n  mxf: 'application/mxf',\n  mxu: 'video/vnd.mpegurl',\n  nb: 'application/mathematica',\n  nbp: 'application/mathematica',\n  nc: 'application/x-netcdf',\n  nef: 'image/x-nikon-nef',\n  nwc: 'application/x-nwc',\n  o: 'application/x-object',\n  oda: 'application/oda',\n  odb: 'application/vnd.oasis.opendocument.database',\n  odc: 'application/vnd.oasis.opendocument.chart',\n  odf: 'application/vnd.oasis.opendocument.formula',\n  odg: 'application/vnd.oasis.opendocument.graphics',\n  odi: 'application/vnd.oasis.opendocument.image',\n  odm: 'application/vnd.oasis.opendocument.text-master',\n  odp: 'application/vnd.oasis.opendocument.presentation',\n  ods: 'application/vnd.oasis.opendocument.spreadsheet',\n  odt: 'application/vnd.oasis.opendocument.text',\n  oga: 'audio/ogg',\n  ogg: 'audio/ogg',\n  ogv: 'video/ogg',\n  ogx: 'application/ogg',\n  old: 'application/x-trash',\n  one: 'application/onenote',\n  onepkg: 'application/onenote',\n  onetmp: 'application/onenote',\n  onetoc2: 'application/onenote',\n  opf: 'application/oebps-package+xml',\n  opus: 'audio/ogg',\n  orc: 'audio/csound',\n  orf: 'image/x-olympus-orf',\n  otf: 'application/font-sfnt',\n  otg: 'application/vnd.oasis.opendocument.graphics-template',\n  oth: 'application/vnd.oasis.opendocument.text-web',\n  otp: 'application/vnd.oasis.opendocument.presentation-template',\n  ots: 'application/vnd.oasis.opendocument.spreadsheet-template',\n  ott: 'application/vnd.oasis.opendocument.text-template',\n  oza: 'application/x-oz-application',\n  p: 'text/x-pascal',\n  p7r: 'application/x-pkcs7-certreqresp',\n  pac: 'application/x-ns-proxy-autoconfig',\n  pas: 'text/x-pascal',\n  pat: 'image/x-coreldrawpattern',\n  patch: 'text/x-diff',\n  pbm: 'image/x-portable-bitmap',\n  pcap: 'application/vnd.tcpdump.pcap',\n  pcf: 'application/x-font-pcf',\n  'pcf.Z': 'application/x-font-pcf',\n  pcx: 'image/pcx',\n  pdb: 'chemical/x-pdb',\n  pdf: 'application/pdf',\n  pfa: 'application/x-font',\n  pfb: 'application/x-font',\n  pfr: 'application/font-tdpfr',\n  pgm: 'image/x-portable-graymap',\n  pgn: 'application/x-chess-pgn',\n  pgp: 'application/pgp-encrypted',\n  php: '#application/x-httpd-php',\n  php3: '#application/x-httpd-php3',\n  php3p: '#application/x-httpd-php3-preprocessed',\n  php4: '#application/x-httpd-php4',\n  php5: '#application/x-httpd-php5',\n  phps: '#application/x-httpd-php-source',\n  pht: '#application/x-httpd-php',\n  phtml: '#application/x-httpd-php',\n  pk: 'application/x-tex-pk',\n  pl: 'text/x-perl',\n  pls: 'audio/x-scpls',\n  pm: 'text/x-perl',\n  png: 'image/png',\n  pnm: 'image/x-portable-anymap',\n  pot: 'text/plain',\n  potm: 'application/vnd.ms-powerpoint.template.macroEnabled.12',\n  potx: 'application/vnd.openxmlformats-officedocument.presentationml.template',\n  ppam: 'application/vnd.ms-powerpoint.addin.macroEnabled.12',\n  ppm: 'image/x-portable-pixmap',\n  pps: 'application/vnd.ms-powerpoint',\n  ppsm: 'application/vnd.ms-powerpoint.slideshow.macroEnabled.12',\n  ppsx: 'application/vnd.openxmlformats-officedocument.presentationml.slideshow',\n  ppt: 'application/vnd.ms-powerpoint',\n  pptm: 'application/vnd.ms-powerpoint.presentation.macroEnabled.12',\n  pptx: 'application/vnd.openxmlformats-officedocument.presentationml.presentation',\n  prf: 'application/pics-rules',\n  prt: 'chemical/x-ncbi-asn1-ascii',\n  ps: 'application/postscript',\n  psd: 'image/x-photoshop',\n  py: 'text/x-python',\n  pyc: 'application/x-python-code',\n  pyo: 'application/x-python-code',\n  qgs: 'application/x-qgis',\n  qt: 'video/quicktime',\n  qtl: 'application/x-quicktimeplayer',\n  ra: 'audio/x-pn-realaudio',\n  ram: 'audio/x-pn-realaudio',\n  rar: 'application/rar',\n  ras: 'image/x-cmu-raster',\n  rb: 'application/x-ruby',\n  rd: 'chemical/x-mdl-rdfile',\n  rdf: 'application/rdf+xml',\n  rdp: 'application/x-rdp',\n  rgb: 'image/x-rgb',\n  rhtml: '#application/x-httpd-eruby',\n  rm: 'audio/x-pn-realaudio',\n  roff: 'application/x-troff',\n  ros: 'chemical/x-rosdal',\n  rpm: 'application/x-redhat-package-manager',\n  rss: 'application/x-rss+xml',\n  rtf: 'application/rtf',\n  rtx: 'text/richtext',\n  rxn: 'chemical/x-mdl-rxnfile',\n  scala: 'text/x-scala',\n  sce: 'application/x-scilab',\n  sci: 'application/x-scilab',\n  sco: 'audio/csound',\n  scr: 'application/x-silverlight',\n  sct: 'text/scriptlet',\n  sd: 'chemical/x-mdl-sdfile',\n  sd2: 'audio/x-sd2',\n  sda: 'application/vnd.stardivision.draw',\n  sdc: 'application/vnd.stardivision.calc',\n  sdd: 'application/vnd.stardivision.impress',\n  sds: 'application/vnd.stardivision.chart',\n  sdw: 'application/vnd.stardivision.writer',\n  ser: 'application/java-serialized-object',\n  sfd: 'application/vnd.font-fontforge-sfd',\n  sfv: 'text/x-sfv',\n  sgf: 'application/x-go-sgf',\n  sgl: 'application/vnd.stardivision.writer-global',\n  sh: 'application/x-sh',\n  shar: 'application/x-shar',\n  shp: 'application/x-qgis',\n  shtml: 'text/html',\n  shx: 'application/x-qgis',\n  sid: 'audio/prs.sid',\n  sig: 'application/pgp-signature',\n  sik: 'application/x-trash',\n  silo: 'model/mesh',\n  sis: 'application/vnd.symbian.install',\n  sisx: 'x-epoc/x-sisx-app',\n  sit: 'application/x-stuffit',\n  sitx: 'application/x-stuffit',\n  skd: 'application/x-koan',\n  skm: 'application/x-koan',\n  skp: 'application/x-koan',\n  skt: 'application/x-koan',\n  sldm: 'application/vnd.ms-powerpoint.slide.macroEnabled.12',\n  sldx: 'application/vnd.openxmlformats-officedocument.presentationml.slide',\n  smi: 'application/smil+xml',\n  smil: 'application/smil+xml',\n  snd: 'audio/basic',\n  spc: 'chemical/x-galactic-spc',\n  spl: 'application/x-futuresplash',\n  spx: 'audio/ogg',\n  sql: 'application/x-sql',\n  src: 'application/x-wais-source',\n  srt: 'text/plain',\n  stc: 'application/vnd.sun.xml.calc.template',\n  std: 'application/vnd.sun.xml.draw.template',\n  sti: 'application/vnd.sun.xml.impress.template',\n  stw: 'application/vnd.sun.xml.writer.template',\n  sty: 'text/x-tex',\n  sv4cpio: 'application/x-sv4cpio',\n  sv4crc: 'application/x-sv4crc',\n  svg: 'image/svg+xml',\n  svgz: 'image/svg+xml',\n  sw: 'chemical/x-swissprot',\n  swf: 'application/x-shockwave-flash',\n  swfl: 'application/x-shockwave-flash',\n  sxc: 'application/vnd.sun.xml.calc',\n  sxd: 'application/vnd.sun.xml.draw',\n  sxg: 'application/vnd.sun.xml.writer.global',\n  sxi: 'application/vnd.sun.xml.impress',\n  sxm: 'application/vnd.sun.xml.math',\n  sxw: 'application/vnd.sun.xml.writer',\n  t: 'application/x-troff',\n  tar: 'application/x-tar',\n  taz: 'application/x-gtar-compressed',\n  tcl: 'application/x-tcl',\n  tex: 'text/x-tex',\n  texi: 'application/x-texinfo',\n  texinfo: 'application/x-texinfo',\n  text: 'text/plain',\n  tgf: 'chemical/x-mdl-tgf',\n  tgz: 'application/x-gtar-compressed',\n  thmx: 'application/vnd.ms-officetheme',\n  tif: 'image/tiff',\n  tiff: 'image/tiff',\n  tk: 'text/x-tcl',\n  tm: 'text/texmacs',\n  torrent: 'application/x-bittorrent',\n  tr: 'application/x-troff',\n  ts: 'video/MP2T',\n  tsp: 'application/dsptype',\n  tsv: 'text/tab-separated-values',\n  ttf: 'application/font-sfnt',\n  ttl: 'text/turtle',\n  txt: 'text/plain',\n  uls: 'text/iuls',\n  ustar: 'application/x-ustar',\n  val: 'chemical/x-ncbi-asn1-binary',\n  vcard: 'text/vcard',\n  vcd: 'application/x-cdlink',\n  vcf: 'text/vcard',\n  vcs: 'text/x-vcalendar',\n  vmd: 'chemical/x-vmd',\n  vms: 'chemical/x-vamas-iso14976',\n  vrm: 'x-world/x-vrml',\n  vrml: 'model/vrml',\n  vsd: 'application/vnd.visio',\n  vss: 'application/vnd.visio',\n  vst: 'application/vnd.visio',\n  vsw: 'application/vnd.visio',\n  wad: 'application/x-doom',\n  wasm: 'application/wasm',\n  wav: 'audio/wav',\n  wax: 'audio/x-ms-wax',\n  wbmp: 'image/vnd.wap.wbmp',\n  wbxml: 'application/vnd.wap.wbxml',\n  webm: 'video/webm',\n  wk: 'application/x-123',\n  wm: 'video/x-ms-wm',\n  wma: 'audio/x-ms-wma',\n  wmd: 'application/x-ms-wmd',\n  wml: 'text/vnd.wap.wml',\n  wmlc: 'application/vnd.wap.wmlc',\n  wmls: 'text/vnd.wap.wmlscript',\n  wmlsc: 'application/vnd.wap.wmlscriptc',\n  wmv: 'video/x-ms-wmv',\n  wmx: 'video/x-ms-wmx',\n  wmz: 'application/x-ms-wmz',\n  woff: 'application/font-woff',\n  wp5: 'application/vnd.wordperfect5.1',\n  wpd: 'application/vnd.wordperfect',\n  wrl: 'model/vrml',\n  wsc: 'text/scriptlet',\n  wvx: 'video/x-ms-wvx',\n  wz: 'application/x-wingz',\n  x3d: 'model/x3d+xml',\n  x3db: 'model/x3d+binary',\n  x3dv: 'model/x3d+vrml',\n  xbm: 'image/x-xbitmap',\n  xcf: 'application/x-xcf',\n  xcos: 'application/x-scilab-xcos',\n  xht: 'application/xhtml+xml',\n  xhtml: 'application/xhtml+xml',\n  xlam: 'application/vnd.ms-excel.addin.macroEnabled.12',\n  xlb: 'application/vnd.ms-excel',\n  xls: 'application/vnd.ms-excel',\n  xlsb: 'application/vnd.ms-excel.sheet.binary.macroEnabled.12',\n  xlsm: 'application/vnd.ms-excel.sheet.macroEnabled.12',\n  xlsx: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',\n  xlt: 'application/vnd.ms-excel',\n  xltm: 'application/vnd.ms-excel.template.macroEnabled.12',\n  xltx: 'application/vnd.openxmlformats-officedocument.spreadsheetml.template',\n  xml: 'application/xml',\n  xpi: 'application/x-xpinstall',\n  xpm: 'image/x-xpixmap',\n  xsd: 'application/xml',\n  xsl: 'application/xslt+xml',\n  xslt: 'application/xslt+xml',\n  xspf: 'application/xspf+xml',\n  xtel: 'chemical/x-xtel',\n  xul: 'application/vnd.mozilla.xul+xml',\n  xwd: 'image/x-xwindowdump',\n  xyz: 'chemical/x-xyz',\n  xz: 'application/x-xz',\n  zip: 'application/zip',\n};\n\nexport default mimeTypes;\n", "import * as browserCRC from './crc.browser';\nimport * as nodejsCRC from '../nodejs/crc.node';\n\ntype CRCModule = typeof nodejsCRC;\nexport type CRCCls = nodejsCRC.CRC;\nlet crcModule = null as unknown as CRCModule;\n\nif (process.env.TARGET_ENVIRONMENT === 'node') {\n  crcModule = nodejsCRC;\n} else {\n  crcModule = browserCRC as unknown as CRCModule;\n}\n\nconst { CRC, combineCrc64 } = crcModule;\nexport { CRC, combineCrc64 };\n", "import TosClientError from '../TosClientError';\n\n// not enabled in browser environment, because:\n// 1. crcjs maybe make browser long task\n// 2. the size of webassembly version's crc is a bit large, it's 1.2MB when uncompressed.\nexport class CRC {\n  reset() {}\n\n  async updateBlob(): Promise<string> {\n    throw new TosClientError('Not implemented.(CRC may cause browser lag.)');\n  }\n\n  update(_value: Buffer): string {\n    throw new TosClientError('Not implemented.(CRC may cause browser lag.)');\n  }\n}\n", "import * as moduleBrowser from './rate-limiter.browser';\nimport * as moduleNode from '../nodejs/rate-limiter';\nimport { IRateLimiter } from '../interface';\n\ninterface RateLimiterModule {\n  createDefaultRateLimiter(capacity: number, rate: number): IRateLimiter;\n  createRateLimiterStream(\n    stream: NodeJS.ReadableStream,\n    rateLimiter: IRateLimiter\n  ): NodeJS.ReadableStream;\n}\n\nlet rateLimiter = null as unknown as RateLimiterModule;\nif (process.env.TARGET_ENVIRONMENT === 'node') {\n  rateLimiter = moduleNode as unknown as RateLimiterModule;\n} else {\n  rateLimiter = moduleBrowser as unknown as RateLimiterModule;\n}\n\nconst { createDefaultRateLimiter, createRateLimiterStream } = rateLimiter;\n\nexport { createDefaultRateLimiter, createRateLimiterStream };\nexport type { IRateLimiter };\n", "import { IRateLimiter } from '../interface';\n\nexport function createDefaultRateLimiter(\n  _capacity: number,\n  _rate: number\n): IRateLimiter {\n  throw Error('no implemention in browser environment');\n}\nexport function createRateLimiterStream(\n  _rateLimiter: IRateLimiter\n): NodeJS.ReadableStream {\n  throw Error('no implemention in browser environment');\n}\n", "import { TierType } from '../../TosExportEnum';\n\nexport interface RestoreInfo {\n  RestoreStatus: RestoreStatus;\n  RestoreParam?: RestoreParam;\n}\n\nexport type RestoreStatus = {\n  OngoingRequest: boolean;\n  ExpiryDate?: string;\n};\n\nexport type RestoreParam = {\n  RequestDate: string;\n  ExpiryDays: number;\n  Tier: TierType;\n};\n\nexport enum TosHeader {\n  HeaderRestore = 'x-tos-restore',\n  HeaderRestoreExpiryDays = 'x-tos-restore-expiry-days',\n  HeaderRestoreRequestDate = 'x-tos-restore-request-date',\n  HeaderRestoreTier = 'x-tos-restore-tier',\n  HeaderProjectName = 'x-tos-project-name',\n  HeaderReplicationStatus = 'x-tos-replication-status',\n}\n\nexport const RestoreOngoingRequestTrueStr = 'ongoing-request=\"true\"';\nexport const RestoreOngoingRequestFalseReg = 'ongoing-request=\"false\"';\n", "import TosClientError from '../../TosClientError';\nimport mimeTypes from '../../mime-types';\nimport { Headers, SupportObjectBody } from '../../interface';\nimport { createReadNReadStream } from '../../nodejs/EmitReadStream';\nimport { isBuffer, isBlob, isReadable } from '../../utils';\nimport { CRC, CRCCls } from '../../universal/crc';\nimport {\n  IRateLimiter,\n  createRateLimiterStream,\n} from '../../universal/rate-limiter';\nimport { Buffer2Stream } from '../../nodejs/buffer2Stream';\nimport { createCrcReadStream } from '../../nodejs/CrcReadStream';\nimport {\n  RestoreInfo,\n  RestoreOngoingRequestTrueStr,\n  TosHeader,\n} from './sharedTypes';\nimport { TierType } from '../../TosExportEnum';\n\nexport const getObjectInputKey = (input: string | { key: string }): string => {\n  return typeof input === 'string' ? input : input.key;\n};\n\nexport const DEFAULT_CONTENT_TYPE = 'application/octet-stream';\n\nexport function lookupMimeType(key: string) {\n  const lastDotIndex = key.lastIndexOf('.');\n\n  if (lastDotIndex <= 0) {\n    return undefined;\n  }\n\n  const extName = key.slice(lastDotIndex + 1).toLowerCase();\n\n  return mimeTypes[extName];\n}\n\n// for all object methods\nexport function validateObjectName(input: { key: string } | string) {\n  const key = typeof input === 'string' ? input : input.key;\n  if (key.length < 1) {\n    throw new TosClientError(\n      'invalid object name, the length must be greater than 1'\n    );\n  }\n}\n\nexport function getSize(body: unknown, headers?: Headers) {\n  if (isBuffer(body)) {\n    return body.length;\n  }\n  if (isBlob(body)) {\n    return body.size;\n  }\n  if (headers && headers['content-length']) {\n    const v = +headers['content-length'];\n    if (v >= 0) {\n      return v;\n    }\n  }\n  return null;\n}\n\ninterface GetNewBodyConfigIn<T> {\n  body: T;\n  dataTransferCallback: (n: number) => void;\n  beforeRetry?: () => void;\n  makeRetryStream?: () => NodeJS.ReadableStream | undefined;\n  enableCRC: boolean;\n  rateLimiter?: IRateLimiter;\n}\ninterface GetNewBodyConfigOut<T> {\n  body: T | NodeJS.ReadableStream;\n  beforeRetry?: () => void;\n  makeRetryStream?: () => NodeJS.ReadableStream | undefined;\n  crc?: CRCCls;\n}\n\ninterface GetEmitReadBodyConfigIn<T> {\n  body: T;\n  dataTransferCallback: (n: number) => void;\n  makeRetryStream?: () => NodeJS.ReadableStream | undefined;\n  rateLimiter?: IRateLimiter;\n}\ninterface GetEmitReadBodyConfigOut<T> {\n  body: T | NodeJS.ReadableStream;\n  makeRetryStream?: () => NodeJS.ReadableStream | undefined;\n}\n\nexport function getEmitReadBodyConfig<T extends SupportObjectBody>({\n  body,\n  dataTransferCallback,\n  makeRetryStream,\n  rateLimiter,\n}: GetEmitReadBodyConfigIn<T>): GetEmitReadBodyConfigOut<T> {\n  let newBody: T | NodeJS.ReadableStream = body;\n\n  const getDefaultRet = () => ({\n    body: newBody,\n    makeRetryStream: undefined,\n  });\n\n  if (process.env.TARGET_ENVIRONMENT !== 'node') {\n    return getDefaultRet();\n  }\n\n  if (isBuffer(newBody)) {\n    const bodyBuf = newBody;\n    makeRetryStream = () => new Buffer2Stream(bodyBuf);\n    newBody = new Buffer2Stream(bodyBuf);\n  }\n\n  if (isReadable(newBody)) {\n    if (rateLimiter && isValidRateLimiter(rateLimiter)) {\n      newBody = createRateLimiterStream(newBody, rateLimiter);\n    }\n    newBody = createReadNReadStream(newBody, dataTransferCallback);\n\n    if (makeRetryStream) {\n      const oriMakeRetryStream = makeRetryStream;\n      return {\n        body: newBody,\n        makeRetryStream: () => {\n          let stream = oriMakeRetryStream();\n          if (!stream) {\n            return stream;\n          }\n\n          if (rateLimiter && isValidRateLimiter(rateLimiter)) {\n            stream = createRateLimiterStream(stream, rateLimiter);\n          }\n          stream = createReadNReadStream(stream, dataTransferCallback);\n          return stream;\n        },\n      };\n    }\n  }\n\n  return getDefaultRet();\n}\n\nexport async function getCRCBodyConfig<T extends SupportObjectBody>({\n  body,\n  beforeRetry,\n  makeRetryStream,\n  enableCRC,\n}: GetNewBodyConfigIn<T>): Promise<GetNewBodyConfigOut<T>> {\n  if (process.env.TARGET_ENVIRONMENT === 'browser' || !enableCRC) {\n    return {\n      body,\n      beforeRetry,\n      makeRetryStream,\n    };\n  }\n\n  let newBody: T | NodeJS.ReadableStream = body;\n  const crc = new CRC();\n  if (isReadable(body)) {\n    newBody = createCrcReadStream(body, crc);\n    if (makeRetryStream) {\n      const oriMakeRetryStream = makeRetryStream;\n      makeRetryStream = () => {\n        const stream = oriMakeRetryStream();\n        if (!stream) {\n          return stream;\n        }\n        return createCrcReadStream(stream, crc);\n      };\n    }\n  }\n\n  return {\n    body: newBody,\n    beforeRetry: () => {\n      crc.reset();\n      beforeRetry?.();\n    },\n    makeRetryStream,\n    crc,\n  };\n}\n\nexport async function getNewBodyConfig<T extends SupportObjectBody>(\n  input: GetNewBodyConfigIn<T>\n): Promise<GetNewBodyConfigOut<T>> {\n  const config1 = getEmitReadBodyConfig(input);\n  input = { ...input, ...config1 } as GetNewBodyConfigIn<T>;\n  const config2 = getCRCBodyConfig(input);\n  return config2;\n}\n\nexport function getCopySourceHeaderValue(srcBucket: string, srcKey: string) {\n  return `/${srcBucket}/${encodeURIComponent(srcKey)}`;\n}\n\nexport function isValidRateLimiter(rateLimiter?: IRateLimiter) {\n  if (!rateLimiter?.Acquire || !(rateLimiter?.Acquire instanceof Function)) {\n    throw new TosClientError(`The rateLimiter is not valid function`);\n  }\n  return true;\n}\n\nexport function validateCheckpoint(cp: undefined | string | Object) {\n  if (process.env.TARGET_ENVIRONMENT === 'node' && typeof cp === 'object') {\n    console.warn(\n      `The \\`checkpoint\\` parameter should be passed as a string in node.js environment, representing a file or directory.` +\n        `Passing a checkpoint object to it will be removed in the future.`\n    );\n  }\n}\n\nexport const getRestoreInfoFromHeaders = (headers: Headers) => {\n  if (!headers) return;\n  const headerStoreValue = headers?.[TosHeader.HeaderRestore];\n\n  if (headerStoreValue) {\n    /**\n     * value example:\n     * X-Tos-Restore: ongoing-request=\"false\", expiry-date=\"Fri, 19 Apr 2024 00:00:00 GMT\"\n     */\n    const ExpiryDate =\n      (headerStoreValue ?? '').split('\",')[1]?.split?.('=')?.[1] ?? '';\n    const OngoingRequest =\n      headerStoreValue?.trim() === RestoreOngoingRequestTrueStr ? true : false;\n    const restoreInfo: RestoreInfo = {\n      RestoreStatus: {\n        OngoingRequest,\n        ExpiryDate,\n      },\n    };\n    if (OngoingRequest) {\n      restoreInfo.RestoreParam = {\n        ExpiryDays: headers[TosHeader.HeaderRestoreExpiryDays]\n          ? Number(headers[TosHeader.HeaderRestoreExpiryDays])\n          : 0,\n        RequestDate: headers[TosHeader.HeaderRestoreRequestDate] ?? '',\n        Tier: headers[TosHeader.HeaderRestoreTier] as TierType,\n      };\n    }\n    return restoreInfo;\n  }\n  return;\n};\n", "import {\n  ACLType,\n  CannedType,\n  PermissionType,\n  StorageClassType,\n} from './TosExportEnum';\n\nexport type Headers = { [key: string]: string | undefined };\n\nexport interface AclInterface {\n  Owner: { ID: string };\n  Grants: {\n    Grantee: {\n      ID?: string;\n      DisplayName?: string;\n      Type: string;\n      Canned?: CannedType;\n    };\n    Permission: PermissionType;\n  }[];\n  /**\n   * @private unstable property only for bucket ACL\n   */\n  BucketAclDelivered?: boolean;\n  /**\n   * @private unstable property only for object ACL\n   */\n  IsDefault?: boolean;\n}\n\nexport type Acl = ACLType;\nexport type StorageClass = StorageClassType;\n\nexport type ServerSideEncryption = 'AES256';\n\nexport interface DataTransferStatus {\n  /**\n   * has read or wrote bytes\n   */\n  consumedBytes: number;\n\n  /**\n   * totalBytes maybe 0 or -1.\n   * `-1` means unkown totalBytes, for example when starting to download an object\n   */\n  totalBytes: number;\n\n  /**\n   * transferred bytes in this transfer\n   */\n  rwOnceBytes: number;\n\n  type: DataTransferType;\n}\n\nexport enum DataTransferType {\n  Started = 1, // data transfer start\n  Rw = 2, // one transfer\n  Succeed = 3, // data transfer succeed\n  Failed = 4, // data transfer failed\n}\n\nexport type SupportObjectBody =\n  | File\n  | Blob\n  | Buffer\n  | NodeJS.ReadableStream\n  | undefined;\n\nexport type StringKeys<T> = Extract<\n  { [K in keyof T]: T[K] extends string | undefined ? K : never }[keyof T],\n  string\n>;\n\nexport interface IRateLimiter {\n  Acquire: (want: number) => Promise<{\n    ok: boolean;\n    /**\n     * unit: milliseconds\n     */\n    timeToWait: number;\n  }>;\n}\n", "import createDebug from 'debug';\n\nexport const TOS = createDebug('TOS');\n", "import axios from 'axios';\nimport { Readable } from 'stream';\nimport { getSortedQueryString, safeSync } from './utils';\nimport { ISigV4Credentials, SignersV4 } from './signatureV4';\nimport * as log from './log';\n\nexport const retryNamespace = '__retryConfig__';\nexport const retrySignatureNamespace = '__retrySignature__';\n\nexport interface RetryConfig {\n  // 对于文件流重试应该重新生成新的文件流\n  makeRetryStream?: () => NodeJS.ReadableStream | undefined;\n\n  beforeRetry?: () => void;\n}\n\ninterface InnerRetryConfig extends RetryConfig {\n  retryCount?: number;\n}\n\ninterface RetrySignature {\n  signOpt: any;\n  sigInst: SignersV4;\n}\n\ndeclare module 'axios' {\n  interface AxiosRequestConfig {\n    __retryConfig__?: RetryConfig;\n    __retrySignature__?: RetrySignature;\n  }\n}\n\nfunction isNetworkError(error: any) {\n  // no response or no requestId, ignore no network(error.code is undefined)\n  return (\n    (!error.response && Boolean(error.code)) ||\n    (error.response && !error.response.headers?.['x-tos-request-id'])\n  );\n}\n\nfunction isCanRetryStatusCode(error: any) {\n  if (!error.response) {\n    return false;\n  }\n\n  const { status } = error.response;\n  if (status === 429 || status >= 500) {\n    return true;\n  }\n  return false;\n}\n\nconst BROWSER_NEED_DELETE_HEADERS = ['content-length', 'user-agent', 'host'];\n\nexport const makeAxiosInst = (maxRetryCount: number) => {\n  const axiosInst = axios.create();\n  // set `axiosInst` default values to avoid being affected by the global default values of axios\n  axiosInst.defaults.auth = undefined;\n  axiosInst.defaults.responseType = 'json';\n  axiosInst.defaults.params = undefined;\n  axiosInst.defaults.headers = {};\n  axiosInst.defaults.withCredentials = false;\n  axiosInst.defaults.maxContentLength = -1;\n  axiosInst.defaults.maxBodyLength = -1;\n  axiosInst.defaults.maxRedirects = 0;\n  axiosInst.defaults.validateStatus = function (status) {\n    return status >= 200 && status < 300; // default\n  };\n  axiosInst.defaults.decompress = false;\n  axiosInst.defaults.transitional = {\n    silentJSONParsing: true,\n    forcedJSONParsing: true,\n    clarifyTimeoutError: false,\n  };\n\n  // delete browser headers\n  if (process.env.TARGET_ENVIRONMENT === 'browser') {\n    axiosInst.interceptors.request.use((config) => {\n      if (!config.headers) {\n        return config;\n      }\n\n      Object.keys(config.headers).forEach((key) => {\n        if (BROWSER_NEED_DELETE_HEADERS.includes(key.toLowerCase())) {\n          delete config.headers[key];\n        }\n      });\n\n      return config;\n    });\n  }\n\n  // headers\n  const ensureHeaders = (v: any) => {\n    v.headers = v.headers || v.header || v?.response?.headers || {};\n    return v;\n  };\n  axiosInst.interceptors.response.use(ensureHeaders, (error) => {\n    ensureHeaders(error);\n    return Promise.reject(error);\n  });\n\n  // decode header. Encode headers' value by encodeHeadersValue method before calling axios\n  function handleResponseHeader(headers: Record<string, string>) {\n    Object.entries(headers).forEach(([key, value]) => {\n      const [err, decodedValue] = safeSync(() => decodeURI(value));\n      if (err || decodedValue == null || decodedValue === value) {\n        return;\n      }\n      let sArr = [];\n      const valueArr = `${value}`.match(/./gu)!;\n      const decodedValueArr = decodedValue.match(/./gu)!;\n      for (let i = 0, j = 0; i < decodedValueArr.length; ) {\n        const ch = decodedValueArr[i];\n        if (ch === valueArr[j]) {\n          sArr.push(ch);\n          ++i;\n          ++j;\n          continue;\n        }\n\n        const encodedCh = encodeURIComponent(ch);\n        if (ch.length > 1 || ch.charCodeAt(0) >= 128) {\n          sArr.push(ch);\n        } else {\n          sArr.push(encodedCh);\n        }\n        ++i;\n        j += encodedCh.length;\n      }\n      headers[key] = sArr.join('');\n    });\n  }\n  axiosInst.interceptors.response.use(\n    (res) => {\n      if (!res.headers) {\n        return res;\n      }\n      handleResponseHeader(res.headers);\n      return res;\n    },\n    async (error) => {\n      if (!axios.isAxiosError(error)) {\n        return Promise.reject(error);\n      }\n\n      const headers = error.response?.headers;\n      if (!headers) {\n        return Promise.reject(error);\n      }\n      handleResponseHeader(headers);\n      return Promise.reject(error);\n    }\n  );\n\n  // retry\n  axiosInst.interceptors.response.use(undefined, async (error) => {\n    const { config } = error;\n    if (!config) {\n      return Promise.reject(error);\n    }\n\n    if (!config[retryNamespace]) {\n      config[retryNamespace] = {};\n    }\n    const retryConfig: InnerRetryConfig = config[retryNamespace];\n    const retryCount = retryConfig.retryCount ?? 0;\n\n    let newData = config.data;\n    const canRetryData = (() => {\n      if (process.env.TARGET_ENVIRONMENT === 'node') {\n        if (config.data && config.data instanceof Readable) {\n          const v = retryConfig.makeRetryStream?.();\n          if (!v) {\n            return false;\n          }\n          newData = v;\n        }\n      }\n      return true;\n    })();\n\n    const canRetry =\n      (isNetworkError(error) || isCanRetryStatusCode(error)) &&\n      retryCount < maxRetryCount &&\n      canRetryData;\n\n    if (!canRetry) {\n      return Promise.reject(error);\n    }\n\n    const retrySignature = config[retrySignatureNamespace] as RetrySignature;\n    if (retrySignature) {\n      const { signOpt, sigInst } = retrySignature;\n      const signatureHeaders = sigInst.signatureHeader(signOpt);\n      signatureHeaders.forEach((value, key) => {\n        config.headers[key] = value;\n      });\n    }\n\n    //console.log('config: ', config)\n    log.TOS('retryConfig: ', config);\n    const nextConfig = {\n      ...config,\n      data: newData,\n      [retryNamespace]: {\n        ...retryConfig,\n        retryCount: retryCount + 1,\n      },\n    };\n\n    retryConfig.beforeRetry?.();\n    return axiosInst(nextConfig);\n  });\n\n  return axiosInst;\n};\n", "import cryptoHmacSha256 from 'crypto-js/hmac-sha256';\nimport cryptoHashSha256 from 'crypto-js/sha256';\nimport cryptoHashMd5 from 'crypto-js/md5';\nimport cryptoEncBase64 from 'crypto-js/enc-base64';\nimport cryptoEncHex from 'crypto-js/enc-hex';\nimport cryptoEncUtf8 from 'crypto-js/enc-utf8';\nimport TosClientError from '../TosClientError';\nimport { isBuffer } from '../utils';\n\nfunction getEnc(coding: 'utf-8' | 'base64' | 'hex') {\n  switch (coding) {\n    case 'utf-8':\n      return cryptoEncUtf8;\n    case 'base64':\n      return cryptoEncBase64;\n    case 'hex':\n      return cryptoEncHex;\n    default:\n      throw new TosClientError('The coding is not supported');\n  }\n}\n\nfunction decode(v: any, decoding?: 'base64' | 'hex'): string {\n  if (!decoding) {\n    return v;\n  }\n\n  return v.toString(getEnc(decoding));\n}\n\nexport const hmacSha256 = function hmacSha256(\n  key: string,\n  message: string,\n  decoding?: 'base64' | 'hex'\n) {\n  return decode(cryptoHmacSha256(message, key), decoding);\n};\n\nexport const hashSha256 = function hashSha256(\n  message: string,\n  decoding?: 'base64' | 'hex'\n) {\n  return decode(cryptoHashSha256(message), decoding);\n};\n\nexport const hashMd5 = function hashMd5(\n  message: string | Buffer,\n  decoding?: 'base64' | 'hex'\n) {\n  if (isBuffer(message)) {\n    throw new TosClientError('not support buffer in browser environment');\n  }\n\n  return decode(cryptoHashMd5(message), decoding);\n};\n\nexport const parse = function parse(\n  str: string,\n  encoding: 'utf-8' | 'base64' | 'hex'\n) {\n  return getEnc(encoding).parse(str);\n};\n\nexport const stringify = function stringify(\n  str: CryptoJS.lib.WordArray,\n  decoding: 'utf-8' | 'base64' | 'hex'\n) {\n  return getEnc(decoding).stringify(str);\n};\n", "import * as cryptoBrowser from './crypto.browser';\nimport * as cryptoNode from '../nodejs/crypto.nodejs';\n\ninterface CryptoModule {\n  hmacSha256: (\n    key: string,\n    message: string,\n    decoding?: 'base64' | 'hex'\n  ) => string;\n  hashSha256: (message: string, decoding?: 'base64' | 'hex') => string;\n  hashMd5: (message: string | Buffer, decoding?: 'base64' | 'hex') => string;\n  parse: (str: string, encoding: 'utf-8' | 'base64' | 'hex') => string;\n  stringify: (str: string, decoding: 'utf-8' | 'base64' | 'hex') => string;\n}\n\nlet crypto = null as unknown as CryptoModule;\nif (process.env.TARGET_ENVIRONMENT === 'node') {\n  crypto = cryptoNode as unknown as CryptoModule;\n} else {\n  crypto = cryptoBrowser as unknown as CryptoModule;\n}\n\nconst { hmacSha256, hashSha256, hashMd5, parse, stringify } = crypto;\n\nexport { hmacSha256, hashSha256, hashMd5, parse, stringify };\n", "import { getNewBodyConfig, getSize } from '../utils';\nimport TOSBase from '../../base';\nimport TosClientError from '../../../TosClientError';\nimport { ReadStream, Stats } from 'fs';\nimport * as fsp from '../../../nodejs/fs-promises';\nimport { DataTransferStatus, DataTransferType } from '../../../interface';\nimport {\n  checkCRC64WithHeaders,\n  fillRequestHeaders,\n  isReadable,\n  makeRetryStreamAutoClose,\n  normalizeHeadersKey,\n  safeAwait,\n  tryDestroy,\n} from '../../../utils';\nimport { retryNamespace } from '../../../axios';\nimport { hashMd5 } from '../../../universal/crypto';\nimport { IRateLimiter } from '../../../universal/rate-limiter';\n\nexport interface UploadPartInput {\n  body: Blob | Buffer | NodeJS.ReadableStream;\n  bucket?: string;\n  key: string;\n  partNumber: number;\n  uploadId: string;\n  dataTransferStatusChange?: (status: DataTransferStatus) => void;\n  /**\n   * the simple progress feature\n   * percent is [0, 1].\n   *\n   * since uploadPart is stateless, so if `uploadPart` fail and you retry it,\n   * `percent` will start from 0 again rather than from the previous value.\n   */\n  progress?: (percent: number) => void;\n  /**\n   * unit: bit/s\n   * server side traffic limit\n   **/\n  trafficLimit?: number;\n  /**\n   * only works for nodejs environment\n   */\n  rateLimiter?: IRateLimiter;\n\n  ssecAlgorithm?: string;\n  ssecKey?: string;\n  ssecKeyMD5?: string;\n\n  headers?: {\n    [key: string]: string | undefined;\n    'content-length'?: string;\n    'content-md5'?: string;\n    'x-tos-server-side-encryption-customer-algorithm'?: string;\n    'x-tos-server-side-encryption-customer-key'?: string;\n    'x-tos-server-side-encryption-customer-key-MD5'?: string;\n  };\n}\n\nexport interface UploadPartInputInner extends UploadPartInput {\n  makeRetryStream?: () => NodeJS.ReadableStream | undefined;\n  beforeRetry?: () => void;\n  /**\n   * default: false\n   */\n  enableContentMD5?: boolean;\n}\n\nexport interface UploadPartOutput {\n  partNumber: number;\n  ETag: string;\n  ssecAlgorithm?: string;\n  ssecKeyMD5?: string;\n  hashCrc64ecma: string;\n  serverSideEncryption?: string;\n  serverSideEncryptionKeyId?: string;\n  /** @private unstable */\n  serverSideDataEncryption?: string;\n}\n\nexport async function _uploadPart(this: TOSBase, input: UploadPartInputInner) {\n  const { uploadId, partNumber, body, enableContentMD5 = false } = input;\n  const headers = normalizeHeadersKey(input.headers);\n  input.headers = headers;\n  fillRequestHeaders(input, [\n    'trafficLimit',\n    'ssecAlgorithm',\n    'ssecKey',\n    'ssecKeyMD5',\n  ]);\n\n  const size = getSize(body);\n  if (size && headers['content-length'] == null) {\n    headers['content-length'] = size.toFixed(0);\n  }\n  if (enableContentMD5 && headers['content-md5'] == null) {\n    // current only support in nodejs\n    if (\n      process.env.TARGET_ENVIRONMENT === 'node' &&\n      isReadable(body) &&\n      input.makeRetryStream\n    ) {\n      const newStream = input.makeRetryStream();\n      if (newStream) {\n        let allContent = Buffer.from([]);\n        for await (const chunk of newStream) {\n          allContent = Buffer.concat([\n            allContent,\n            typeof chunk === 'string' ? Buffer.from(chunk) : chunk,\n          ]);\n        }\n        const md5 = hashMd5(allContent, 'base64');\n        headers['content-md5'] = md5;\n      }\n    } else {\n      console.warn(`current not support enableMD5Checksum`);\n    }\n  }\n\n  const totalSize = getSize(input.body, headers);\n  const totalSizeValid = totalSize != null;\n  if (!totalSizeValid && (input.dataTransferStatusChange || input.progress)) {\n    console.warn(\n      `Don't get totalSize of uploadPart's body, the \\`dataTransferStatusChange\\` callback will not trigger. You can use \\`uploadPartFromFile\\` instead`\n    );\n  }\n\n  let consumedBytes = 0;\n  const { dataTransferStatusChange, progress } = input;\n  const triggerDataTransfer = (\n    type: DataTransferType,\n    rwOnceBytes: number = 0\n  ) => {\n    // request cancel will make rwOnceBytes < 0 in browser\n    if (!totalSizeValid || rwOnceBytes < 0) {\n      return;\n    }\n    if (!dataTransferStatusChange && !progress) {\n      return;\n    }\n    consumedBytes += rwOnceBytes;\n\n    dataTransferStatusChange?.({\n      type,\n      rwOnceBytes,\n      consumedBytes,\n      totalBytes: totalSize,\n    });\n\n    const progressValue = (() => {\n      if (totalSize === 0) {\n        if (type === DataTransferType.Succeed) {\n          return 1;\n        }\n        return 0;\n      }\n      return consumedBytes / totalSize;\n    })();\n    if (progressValue === 1) {\n      if (type === DataTransferType.Succeed) {\n        progress?.(progressValue);\n      } else {\n        // not exec progress\n      }\n    } else {\n      progress?.(progressValue);\n    }\n  };\n  const bodyConfig = await getNewBodyConfig({\n    body: input.body,\n    dataTransferCallback: (n) => triggerDataTransfer(DataTransferType.Rw, n),\n    beforeRetry: input.beforeRetry,\n    makeRetryStream: input.makeRetryStream,\n    enableCRC: this.opts.enableCRC,\n    rateLimiter: input.rateLimiter,\n  });\n\n  triggerDataTransfer(DataTransferType.Started);\n  const task = async () => {\n    const res = await this._fetchObject<UploadPartOutput>(\n      input,\n      'PUT',\n      { partNumber, uploadId },\n      headers,\n      bodyConfig.body,\n      {\n        handleResponse: (res) => ({\n          partNumber,\n          ETag: res.headers.etag,\n          serverSideEncryption: res.headers['x-tos-server-side-encryption'],\n          serverSideDataEncryption:\n            res.headers['x-tos-server-side-data-encryption'],\n          serverSideEncryptionKeyId:\n            res.headers['x-tos-server-side-encryption-kms-key-id'],\n          ssecAlgorithm:\n            res.headers['x-tos-server-side-encryption-customer-algorithm'],\n          ssecKeyMD5:\n            res.headers['x-tos-server-side-encryption-customer-key-MD5'],\n          hashCrc64ecma: res.headers['x-tos-hash-crc64ecma'],\n        }),\n        axiosOpts: {\n          [retryNamespace]: {\n            beforeRetry: () => {\n              consumedBytes = 0;\n              bodyConfig.beforeRetry?.();\n            },\n            makeRetryStream: bodyConfig.makeRetryStream,\n          },\n          onUploadProgress: (event) => {\n            triggerDataTransfer(\n              DataTransferType.Rw,\n              event.loaded - consumedBytes\n            );\n          },\n        },\n      }\n    );\n    if (this.opts.enableCRC && bodyConfig.crc) {\n      checkCRC64WithHeaders(bodyConfig.crc, res.headers);\n    }\n    return res;\n  };\n  const [err, res] = await safeAwait(task());\n\n  // FAQ: no etag\n  if (process.env.TARGET_ENVIRONMENT === 'browser') {\n    if (res && !res.data.ETag) {\n      throw new TosClientError(\n        \"No ETag in uploadPart's response headers, please see https://www.volcengine.com/docs/6349/127737 to fix CORS problem\"\n      );\n    }\n  }\n\n  if (err || !res) {\n    triggerDataTransfer(DataTransferType.Failed);\n    throw err;\n  }\n\n  triggerDataTransfer(DataTransferType.Succeed);\n  return res;\n}\n\nexport async function uploadPart(this: TOSBase, input: UploadPartInput) {\n  return _uploadPart.call(this, input);\n}\n\ninterface UploadPartFromFileInput extends Omit<UploadPartInput, 'body'> {\n  filePath: string;\n  /**\n   * default: 0\n   */\n  offset?: number;\n\n  /**\n   * default: file size\n   */\n  partSize?: number;\n}\nexport async function uploadPartFromFile(\n  this: TOSBase,\n  input: UploadPartFromFileInput\n) {\n  if (process.env.TARGET_ENVIRONMENT !== 'node') {\n    throw new TosClientError(\n      \"uploadPartFromFile doesn't support in browser environment\"\n    );\n  }\n\n  const stats: Stats = await fsp.stat(input.filePath);\n  const start = input.offset ?? 0;\n  const end = Math.min(stats.size, start + (input.partSize ?? stats.size));\n  const makeRetryStream = makeRetryStreamAutoClose(() =>\n    fsp.createReadStream(input.filePath, {\n      start,\n      end: end - 1,\n    })\n  );\n\n  try {\n    return await _uploadPart.call(this, {\n      ...input,\n      body: makeRetryStream.make(),\n      headers: {\n        ...(input.headers || {}),\n        ['content-length']: `${end - start}`,\n      },\n      makeRetryStream: makeRetryStream.make,\n    });\n  } catch (err) {\n    tryDestroy(makeRetryStream.getLastStream(), err);\n    throw err;\n  }\n}\n", "import TosClientError from '../../../TosClientError';\nimport { fillRequestHeaders } from '../../../utils';\nimport TOSBase from '../../base';\n\nexport interface CompleteMultipartUploadInput {\n  bucket?: string;\n  key: string;\n  uploadId: string;\n  parts: {\n    eTag: string;\n    partNumber: number;\n  }[];\n  /**\n   * when true `parts` param need to be empty array\n   */\n  completeAll?: boolean;\n\n  callback?: string;\n  callbackVar?: string;\n  forbidOverwrite?: boolean;\n\n  headers?: {\n    ['x-tos-forbid-overwrite']?: string;\n  };\n}\n\nexport type UploadedPart = {\n  PartNumber: number;\n  ETag: string;\n};\n\nexport interface CompleteMultipartUploadOutput {\n  Bucket: string;\n  Key: string;\n  ETag: string;\n  Location: string;\n  VersionID?: string;\n  HashCrc64ecma?: string;\n  /** the field has a value when completeAll is true\n   * when specify callback, the field will not has a value\n   */\n  CompletedParts?: UploadedPart[];\n  CallbackResult?: string;\n}\n\nexport async function completeMultipartUpload(\n  this: TOSBase,\n  input: CompleteMultipartUploadInput\n) {\n  input.headers = input.headers ?? {};\n  fillRequestHeaders(input, ['callback', 'callbackVar', 'forbidOverwrite']);\n\n  const handleResponse = (response: {\n    headers: { [x: string]: any };\n    data: CompleteMultipartUploadOutput;\n  }) => {\n    const bucket = input.bucket || this.opts.bucket || '';\n    const headers = response.headers;\n    const result: CompleteMultipartUploadOutput = {\n      ...{\n        VersionID: headers['x-tos-version-id'],\n        ETag: headers['etag'],\n        Bucket: bucket,\n        Location: headers['location'],\n        HashCrc64ecma: headers['x-tos-hash-crc64ecma'],\n        Key: input.key,\n      },\n      ...response.data,\n    };\n    if (input.callback) {\n      result.CallbackResult = `${JSON.stringify(response.data)}`;\n    }\n    return result;\n  };\n  if (input.completeAll) {\n    if (input.parts?.length > 0) {\n      throw new TosClientError(\n        `Should not specify both 'completeAll' and 'parts' params.`\n      );\n    }\n    return this._fetchObject<CompleteMultipartUploadOutput>(\n      input,\n      'POST',\n      {\n        uploadId: input.uploadId,\n      },\n      {\n        ...input.headers,\n        'x-tos-complete-all': 'yes',\n      },\n      undefined,\n      {\n        handleResponse,\n      }\n    );\n  }\n\n  return this._fetchObject<CompleteMultipartUploadOutput>(\n    input,\n    'POST',\n    {\n      uploadId: input.uploadId,\n    },\n    {\n      ...input.headers,\n    },\n    {\n      Parts: input.parts.map((it) => ({\n        ETag: it.eTag,\n        PartNumber: it.partNumber,\n      })),\n    },\n    {\n      handleResponse,\n    }\n  );\n}\n", "import TOSBase, { TosResponse } from '../../base';\nimport {\n  createMultipartUpload,\n  CreateMultipartUploadInput,\n} from './createMultipartUpload';\n\nimport { calculateSafePartSize } from './listParts';\nimport { Stats } from 'fs';\nimport { UploadPartOutput, _uploadPart } from './uploadPart';\nimport TosServerError from '../../../TosServerError';\nimport {\n  completeMultipartUpload,\n  CompleteMultipartUploadOutput,\n} from './completeMultipartUpload';\nimport { CancelToken } from 'axios';\nimport * as fsp from '../../../nodejs/fs-promises';\nimport path from 'path';\nimport TosClientError from '../../../TosClientError';\nimport { DataTransferStatus, DataTransferType } from '../../../interface';\nimport {\n  safeAwait,\n  isBlob,\n  isBuffer,\n  isCancel<PERSON>rror,\n  DEFAULT_PART_SIZE,\n  normalizeHeadersKey,\n  fillRequestHeaders,\n  makeSerialAsyncTask,\n  safeParseCheckpointFile,\n  makeRetryStreamAutoClose,\n  tryDestroy,\n} from '../../../utils';\nimport { EmptyReadStream } from '../../../nodejs/EmptyReadStream';\nimport { CancelError } from '../../../CancelError';\nimport { hashMd5 } from '../../../universal/crypto';\nimport { IRateLimiter } from '../../../interface';\nimport { validateCheckpoint } from '../utils';\nimport { combineCrc64 } from '../../../universal/crc';\n\nexport interface UploadFileInput extends CreateMultipartUploadInput {\n  /**\n   * if the type of `file` is string,\n   * `file` represents the file path that will be uploaded\n   */\n  file: string | File | Blob | Buffer;\n\n  /**\n   * default is 20 MB\n   *\n   * unit: B\n   */\n  partSize?: number;\n\n  /**\n   * the number of request to parallel upload part，default value is 1\n   */\n  taskNum?: number;\n\n  // TODO: default file name is not aligned.\n  /**\n   * if checkpoint is a string and point to a exist file,\n   * the checkpoint record will recover from this file.\n   *\n   * if checkpoint is a string and point to a directory,\n   * the checkpoint will be auto generated,\n   * and its name is `{bucketName}_{objectName}.{uploadId}`.\n   */\n  checkpoint?: string | CheckpointRecord;\n\n  dataTransferStatusChange?: (status: DataTransferStatus) => void;\n\n  /**\n   * the feature of pause and continue uploading\n   */\n  uploadEventChange?: (event: UploadEvent) => void;\n\n  /**\n   * the simple progress feature\n   * percent is [0, 1]\n   */\n  progress?: (percent: number, checkpoint: CheckpointRecord) => void;\n\n  /**\n   * cancel this upload progress\n   */\n  cancelToken?: CancelToken;\n\n  /**\n   * enable md5 checksum to uploadPart method\n   *\n   * default: false\n   */\n  enableContentMD5?: boolean;\n\n  /**\n   * unit: bit/s\n   * server side traffic limit\n   **/\n  trafficLimit?: number;\n\n  /**\n   * only works for nodejs environment\n   */\n  rateLimiter?: IRateLimiter;\n}\n\nexport interface UploadFileOutput extends CompleteMultipartUploadOutput {}\n\nexport enum UploadEventType {\n  CreateMultipartUploadSucceed = 1,\n  CreateMultipartUploadFailed = 2,\n  UploadPartSucceed = 3,\n  UploadPartFailed = 4,\n  UploadPartAborted = 5,\n  CompleteMultipartUploadSucceed = 6,\n  CompleteMultipartUploadFailed = 7,\n}\n\nexport interface UploadPartInfo {\n  partNumber: number;\n  partSize: number;\n  offset: number;\n\n  // has value when upload part succeed\n  etag?: string;\n\n  // not support\n  // hashCrc64ecma?: number;\n}\n\nexport interface UploadEvent {\n  type: UploadEventType;\n\n  /**\n   * has value when event is failed or aborted\n   */\n  err?: Error;\n\n  bucket: string;\n  key: string;\n  uploadId: string;\n  checkpointFile?: string;\n  uploadPartInfo?: UploadPartInfo;\n}\n\nexport interface CheckpointRecord {\n  bucket: string;\n  key: string;\n  part_size: number;\n  upload_id: string;\n  parts_info?: CheckpointRecordPart[];\n  // Information about the file to be uploaded\n  file_info?: {\n    last_modified: number;\n    file_size: number;\n  };\n\n  // TODO: Not support the fields below\n  // ssec_algorithm?: string;\n  // ssec_key_md5?: string;\n  // encoding_type?: string;\n}\n\ninterface CheckpointRecordPart {\n  part_number: number;\n  part_size: number;\n  offset: number;\n  etag: string;\n  hash_crc64ecma: string;\n  is_completed: boolean;\n}\n\ninterface CheckpointRichInfo {\n  filePath?: string | undefined;\n\n  filePathIsPlaceholder?: boolean;\n\n  record?: CheckpointRecord;\n}\n\ninterface Task {\n  partSize: number;\n  offset: number;\n  partNumber: number;\n}\n\nconst CHECKPOINT_FILE_NAME_PLACEHOLDER = '@@checkpoint-file-placeholder@@';\nconst FILE_PARAM_CHECK_MSG = '`file` must be string, Buffer, File or Blob';\nconst ABORT_ERROR_STATUS_CODE = [403, 404, 405];\n\nexport async function uploadFile(\n  this: TOSBase,\n  input: UploadFileInput\n): Promise<TosResponse<UploadFileOutput>> {\n  const { cancelToken, enableContentMD5 = false } = input;\n  const headers = normalizeHeadersKey(input.headers);\n  input.headers = headers;\n  fillRequestHeaders(input, [\n    'encodingType',\n    'cacheControl',\n    'contentDisposition',\n    'contentEncoding',\n    'contentLanguage',\n    'contentType',\n    'expires',\n\n    'acl',\n    'grantFullControl',\n    'grantRead',\n    'grantReadAcp',\n    'grantWriteAcp',\n\n    'ssecAlgorithm',\n    'ssecKey',\n    'ssecKeyMD5',\n    'serverSideEncryption',\n    'serverSideDataEncryption',\n    'meta',\n    'websiteRedirectLocation',\n    'storageClass',\n  ]);\n\n  const isCancel = () => cancelToken && !!cancelToken.reason;\n  validateCheckpoint(input.checkpoint);\n\n  const fileStats: Stats | null = await (async () => {\n    if (\n      process.env.TARGET_ENVIRONMENT === 'node' &&\n      typeof input.file === 'string'\n    ) {\n      return fsp.stat(input.file);\n    }\n    return null;\n  })();\n\n  const fileSize = await (async () => {\n    const { file } = input;\n    if (fileStats) {\n      return fileStats.size;\n    }\n    if (isBuffer(file)) {\n      return file.length;\n    }\n    if (isBlob(file)) {\n      return file.size;\n    }\n    throw new TosClientError(FILE_PARAM_CHECK_MSG);\n  })();\n\n  const checkpointRichInfo = await (async (): Promise<CheckpointRichInfo> => {\n    if (process.env.TARGET_ENVIRONMENT === 'node') {\n      if (typeof input.checkpoint === 'string') {\n        const { checkpoint } = input;\n        // file doesn't exist when stat is null\n        let checkpointStat: Stats | null = null;\n        try {\n          checkpointStat = await fsp.stat(checkpoint);\n        } catch (_err) {\n          // TODO: remove any\n          const err = _err as any;\n          if (err.code === 'ENOENT') {\n            // file doesn't exist\n          } else {\n            throw err;\n          }\n        }\n\n        const isDirectory = (() => {\n          if (checkpointStat) {\n            return checkpointStat.isDirectory();\n          }\n          return checkpoint.endsWith('/');\n        })();\n\n        // TODO: this is not a right decision\n        // filePath will generated by uploadId, use placeholder temporarily\n        const filePath = isDirectory\n          ? path.resolve(checkpoint, CHECKPOINT_FILE_NAME_PLACEHOLDER)\n          : // ensure relative path require\n            path.resolve(checkpoint);\n        const dirPath = path.dirname(filePath);\n        // ensure directory exist\n        await fsp.safeMkdirRecursive(dirPath);\n\n        if (isDirectory) {\n          return {\n            filePath,\n            filePathIsPlaceholder: true,\n          };\n        }\n\n        try {\n          const record = checkpointStat\n            ? await safeParseCheckpointFile(filePath)\n            : undefined;\n          return {\n            filePath,\n            filePathIsPlaceholder: false,\n            // filePath is json file\n            // TODO: validate json schema\n            record,\n          };\n        } catch (error) {\n          console.warn(\n            'the checkpoint file is invalid JSON format. please check checkpoint file'\n          );\n          throw error;\n        }\n      }\n    }\n\n    if (typeof input.checkpoint === 'object') {\n      return {\n        record: input.checkpoint,\n      };\n    }\n\n    return {};\n  })();\n\n  // check if file info is matched\n  await (async () => {\n    if (fileStats && checkpointRichInfo.record?.file_info) {\n      const { last_modified, file_size } = checkpointRichInfo.record?.file_info;\n      if (fileStats.mtimeMs !== last_modified || fileStats.size !== file_size) {\n        console.warn(\n          `The file has been modified since ${new Date(\n            last_modified\n          )}, so the checkpoint file is invalid, and specified file will be uploaded again.`\n        );\n        delete checkpointRichInfo.record;\n      }\n    }\n  })();\n\n  const partSize = calculateSafePartSize(\n    fileSize,\n    input.partSize || checkpointRichInfo.record?.part_size || DEFAULT_PART_SIZE,\n    true\n  );\n\n  // check partSize is matched\n  if (\n    checkpointRichInfo.record &&\n    checkpointRichInfo.record.part_size !== partSize\n  ) {\n    console.warn(\n      'The partSize param does not equal the partSize in checkpoint file, ' +\n        'so the checkpoint file is invalid, and specified file will be uploaded again.'\n    );\n    delete checkpointRichInfo.record;\n  }\n\n  let bucket = input.bucket || this.opts.bucket || '';\n  const key = input.key;\n  let uploadId = '';\n  let tasks: Task[] = [];\n  const allTasks: Task[] = getAllTasks(fileSize, partSize);\n  const initConsumedBytes = (checkpointRichInfo.record?.parts_info || [])\n    .filter((it) => it.is_completed)\n    .reduce((prev, it) => prev + it.part_size, 0);\n  let consumedBytesForProgress = initConsumedBytes;\n\n  // recorded tasks\n  const recordedTasks = checkpointRichInfo.record?.parts_info || [];\n  const recordedTaskMap: Map<number, CheckpointRecordPart> = new Map();\n  recordedTasks.forEach((it) => recordedTaskMap.set(it.part_number, it));\n\n  const getCheckpointContent = () => {\n    const checkpointContent: CheckpointRecord = {\n      bucket,\n      key,\n      part_size: partSize,\n      upload_id: uploadId,\n      parts_info: recordedTasks,\n    };\n    if (fileStats) {\n      checkpointContent.file_info = {\n        last_modified: fileStats.mtimeMs,\n        file_size: fileStats.size,\n      };\n    }\n    return checkpointContent;\n  };\n  const triggerUploadEvent = (\n    e: Omit<UploadEvent, 'bucket' | 'uploadId' | 'key' | 'checkpointFile'>\n  ) => {\n    if (!input.uploadEventChange) {\n      return;\n    }\n\n    const event: UploadEvent = {\n      bucket,\n      uploadId,\n      key,\n      ...e,\n    };\n    if (checkpointRichInfo.filePath) {\n      event.checkpointFile = checkpointRichInfo.filePath;\n    }\n\n    input.uploadEventChange(event);\n  };\n  enum TriggerProgressEventType {\n    start = 1,\n    uploadPartSucceed = 2,\n    completeMultipartUploadSucceed = 3,\n  }\n  const triggerProgressEvent = (type: TriggerProgressEventType) => {\n    if (!input.progress) {\n      return;\n    }\n\n    const percent = (() => {\n      if (type === TriggerProgressEventType.start && fileSize === 0) {\n        return 0;\n      }\n      return !fileSize ? 1 : consumedBytesForProgress / fileSize;\n    })();\n\n    if (\n      consumedBytesForProgress === fileSize &&\n      type === TriggerProgressEventType.uploadPartSucceed\n    ) {\n      // 100% 仅在 complete 后处理，以便 100% 可以拉取到新对象\n    } else {\n      input.progress(percent, getCheckpointContent());\n    }\n  };\n  let consumedBytes = initConsumedBytes;\n  const { dataTransferStatusChange } = input;\n  const triggerDataTransfer = (\n    type: DataTransferType,\n    rwOnceBytes: number = 0\n  ) => {\n    if (!dataTransferStatusChange) {\n      return;\n    }\n    consumedBytes += rwOnceBytes;\n\n    dataTransferStatusChange?.({\n      type,\n      rwOnceBytes,\n      consumedBytes,\n      totalBytes: fileSize,\n    });\n  };\n  const writeCheckpointFile = makeSerialAsyncTask(async () => {\n    if (\n      process.env.TARGET_ENVIRONMENT === 'node' &&\n      checkpointRichInfo.filePath\n    ) {\n      const content = JSON.stringify(getCheckpointContent(), null, 2);\n      const dirPath = path.dirname(checkpointRichInfo.filePath); // ensure directory exist\n      await fsp.safeMkdirRecursive(dirPath);\n      await fsp.writeFile(checkpointRichInfo.filePath, content, 'utf-8');\n    }\n  });\n  const rmCheckpointFile = async () => {\n    if (\n      process.env.TARGET_ENVIRONMENT === 'node' &&\n      checkpointRichInfo.filePath\n    ) {\n      await fsp.rm(checkpointRichInfo.filePath).catch((err: any) => {\n        // eat err\n        console.warn(\n          'remove checkpoint file failure, you can remove it by hand.\\n',\n          `checkpoint file path: ${checkpointRichInfo.filePath}\\n`,\n          err.message\n        );\n      });\n    }\n  };\n\n  /**\n   *\n   * @param task one part task\n   * @param uploadPartRes upload part failed if `uploadPartRes` is Error\n   */\n  const updateAfterUploadPart = async (\n    task: Task,\n    uploadPartRes:\n      | {\n          res: UploadPartOutput;\n          err?: null;\n        }\n      | {\n          err: Error;\n        }\n  ) => {\n    let existRecordTask = recordedTaskMap.get(task.partNumber);\n    if (!existRecordTask) {\n      existRecordTask = {\n        part_number: task.partNumber,\n        offset: task.offset,\n        part_size: task.partSize,\n        is_completed: false,\n        etag: '',\n        hash_crc64ecma: '',\n      };\n      recordedTasks.push(existRecordTask);\n      recordedTaskMap.set(existRecordTask.part_number, existRecordTask);\n    }\n\n    if (!uploadPartRes.err) {\n      existRecordTask.is_completed = true;\n      existRecordTask.etag = uploadPartRes.res.ETag;\n      existRecordTask.hash_crc64ecma = uploadPartRes.res.hashCrc64ecma;\n    }\n\n    await writeCheckpointFile();\n    const uploadPartInfo: UploadPartInfo = {\n      partNumber: existRecordTask.part_number,\n      partSize: existRecordTask.part_size,\n      offset: existRecordTask.offset,\n    };\n\n    if (uploadPartRes.err) {\n      const err = uploadPartRes.err;\n      let type: UploadEventType = UploadEventType.UploadPartFailed;\n\n      if (err instanceof TosServerError) {\n        if (ABORT_ERROR_STATUS_CODE.includes(err.statusCode)) {\n          type = UploadEventType.UploadPartAborted;\n        }\n      }\n\n      triggerUploadEvent({\n        type,\n        err,\n        uploadPartInfo,\n      });\n      return;\n    }\n\n    uploadPartInfo.etag = uploadPartRes.res.ETag;\n    consumedBytesForProgress += uploadPartInfo.partSize;\n\n    triggerUploadEvent({\n      type: UploadEventType.UploadPartSucceed,\n      uploadPartInfo,\n    });\n    triggerProgressEvent(TriggerProgressEventType.uploadPartSucceed);\n  };\n\n  if (checkpointRichInfo.record) {\n    bucket = checkpointRichInfo.record.bucket;\n    uploadId = checkpointRichInfo.record.upload_id;\n\n    // checkpoint info exists, so need to calculate remain tasks\n    const uploadedPartSet: Set<number> = new Set(\n      (checkpointRichInfo.record.parts_info || [])\n        .filter((it) => it.is_completed)\n        .map((it) => it.part_number)\n    );\n    tasks = allTasks.filter((it) => !uploadedPartSet.has(it.partNumber));\n  } else {\n    // createMultipartUpload will check bucket\n    try {\n      const { data: multipartRes } = await createMultipartUpload.call(\n        this,\n        input\n      );\n      if (isCancel()) {\n        throw new CancelError('cancel uploadFile');\n      }\n\n      bucket = multipartRes.Bucket;\n      uploadId = multipartRes.UploadId;\n      if (checkpointRichInfo.filePathIsPlaceholder) {\n        checkpointRichInfo.filePath = checkpointRichInfo.filePath?.replace(\n          `${CHECKPOINT_FILE_NAME_PLACEHOLDER}`,\n          getDefaultCheckpointFilePath(bucket, key)\n        );\n      }\n\n      triggerUploadEvent({\n        type: UploadEventType.CreateMultipartUploadSucceed,\n      });\n    } catch (_err) {\n      const err = _err as Error;\n      triggerUploadEvent({\n        type: UploadEventType.CreateMultipartUploadFailed,\n        err,\n      });\n      throw err;\n    }\n\n    tasks = allTasks;\n  }\n\n  triggerProgressEvent(TriggerProgressEventType.start);\n  const handleTasks = async () => {\n    let firstErr: Error | null = null;\n    let index = 0;\n\n    // TODO: how to test parallel does work, measure time is not right\n    await Promise.all(\n      Array.from({ length: input.taskNum || 1 }).map(async () => {\n        while (true) {\n          const currentIndex = index++;\n          if (currentIndex >= tasks.length) {\n            return;\n          }\n\n          const curTask = tasks[currentIndex];\n          let consumedBytesThisTask = 0;\n          const makeRetryStream = getMakeRetryStream(input.file, curTask);\n          try {\n            function getBody(file: UploadFileInput['file'], task: Task) {\n              const { offset: start, partSize } = task;\n              const end = start + partSize;\n\n              if (makeRetryStream) {\n                return makeRetryStream.make();\n              }\n\n              if (isBlob(file)) {\n                return file.slice(start, end);\n              }\n              if (isBuffer(file)) {\n                return file.slice(start, end);\n              }\n              throw new TosClientError(FILE_PARAM_CHECK_MSG);\n            }\n\n            const { data: uploadPartRes } = await _uploadPart.call(this, {\n              bucket,\n              key,\n              uploadId,\n              body: getBody(input.file, curTask),\n              enableContentMD5,\n              makeRetryStream: makeRetryStream?.make,\n              beforeRetry: () => {\n                consumedBytes -= consumedBytesThisTask;\n                consumedBytesThisTask = 0;\n              },\n              partNumber: curTask.partNumber,\n              headers: {\n                ['content-length']: `${curTask.partSize}`,\n                ['x-tos-server-side-encryption-customer-algorithm']:\n                  headers['x-tos-server-side-encryption-customer-algorithm'],\n                ['x-tos-server-side-encryption-customer-key']:\n                  headers['x-tos-server-side-encryption-customer-key'],\n                ['x-tos-server-side-encryption-customer-key-md5']:\n                  headers['x-tos-server-side-encryption-customer-key-md5'],\n              },\n              dataTransferStatusChange(status) {\n                if (status.type !== DataTransferType.Rw) {\n                  return;\n                }\n                if (isCancel()) {\n                  return;\n                }\n                consumedBytesThisTask += status.rwOnceBytes;\n                triggerDataTransfer(status.type, status.rwOnceBytes);\n              },\n              trafficLimit: input.trafficLimit,\n              rateLimiter: input.rateLimiter,\n            });\n\n            if (isCancel()) {\n              throw new CancelError('cancel uploadFile');\n            }\n\n            await updateAfterUploadPart(curTask, { res: uploadPartRes });\n          } catch (_err) {\n            tryDestroy(makeRetryStream?.getLastStream(), _err);\n\n            const err = _err as any;\n            consumedBytes -= consumedBytesThisTask;\n            consumedBytesThisTask = 0;\n\n            if (isCancelError(err)) {\n              throw err;\n            }\n\n            if (isCancel()) {\n              throw new CancelError('cancel uploadFile');\n            }\n\n            if (!firstErr) {\n              firstErr = err;\n            }\n            await updateAfterUploadPart(curTask, { err });\n          }\n        }\n      })\n    );\n\n    if (firstErr) {\n      throw firstErr;\n    }\n\n    const parts = (getCheckpointContent().parts_info || []).map((it) => ({\n      eTag: it.etag,\n      partNumber: it.part_number,\n    }));\n\n    const [err, res] = await safeAwait(\n      completeMultipartUpload.call(this, {\n        bucket,\n        key,\n        uploadId,\n        parts,\n      })\n    );\n\n    if (err || !res) {\n      triggerUploadEvent({\n        type: UploadEventType.CompleteMultipartUploadFailed,\n      });\n      throw err;\n    }\n\n    triggerUploadEvent({\n      type: UploadEventType.CompleteMultipartUploadSucceed,\n    });\n    triggerProgressEvent(\n      TriggerProgressEventType.completeMultipartUploadSucceed\n    );\n    await rmCheckpointFile();\n\n    if (\n      this.opts.enableCRC &&\n      res.data.HashCrc64ecma &&\n      combineCRCInParts(getCheckpointContent()) !== res.data.HashCrc64ecma\n    ) {\n      throw new TosClientError('crc of entire file mismatch.');\n    }\n\n    return res;\n  };\n\n  triggerDataTransfer(DataTransferType.Started);\n  const [err, res] = await safeAwait(handleTasks());\n  if (err || !res) {\n    triggerDataTransfer(DataTransferType.Failed);\n    throw err;\n  }\n  triggerDataTransfer(DataTransferType.Succeed);\n  return res;\n}\n\nexport default uploadFile;\n\n/**\n * 即使 totalSize 是 0，也需要一个 Part，否则 Server 端会报错 read request body failed\n */\nfunction getAllTasks(totalSize: number, partSize: number) {\n  const tasks: Task[] = [];\n  for (let i = 0; ; ++i) {\n    const offset = i * partSize;\n    const currPartSize = Math.min(partSize, totalSize - offset);\n\n    tasks.push({\n      offset,\n      partSize: currPartSize,\n      partNumber: i + 1,\n    });\n\n    if ((i + 1) * partSize >= totalSize) {\n      break;\n    }\n  }\n\n  return tasks;\n}\n\nfunction getMakeRetryStream(file: UploadFileInput['file'], task: Task) {\n  const { offset: start, partSize } = task;\n  const end = start + partSize;\n\n  if (process.env.TARGET_ENVIRONMENT === 'node' && typeof file === 'string') {\n    return makeRetryStreamAutoClose(() => {\n      if (!partSize) {\n        return new EmptyReadStream();\n      }\n      return fsp.createReadStream(file, {\n        start,\n        end: end - 1,\n      });\n    });\n  }\n\n  return undefined;\n}\n\nfunction getDefaultCheckpointFilePath(bucket: string, key: string) {\n  const originPath = `${key}.${hashMd5(`${bucket}.${key}`, 'hex')}.upload`;\n  const normalizePath = originPath.replace(/[\\\\/]/g, '');\n  return normalizePath;\n}\n\nfunction combineCRCInParts(cp: CheckpointRecord) {\n  const size = cp.file_info?.file_size || 0;\n  let res = '0';\n  const sortedPartsInfo =\n    cp.parts_info?.sort?.((a, b) => a.part_number - b.part_number) ?? [];\n  for (const part of sortedPartsInfo) {\n    res = combineCrc64(\n      res,\n      part.hash_crc64ecma,\n      Math.min(part.part_size, size - part.offset)\n    );\n  }\n  return res;\n}\n", "export enum ACLType {\n  ACLPrivate = 'private',\n  ACLPublicRead = 'public-read',\n  ACLPublicReadWrite = 'public-read-write',\n  ACLAuthenticatedRead = 'authenticated-read',\n  ACLBucketOwnerRead = 'bucket-owner-read',\n  ACLBucketOwnerFullControl = 'bucket-owner-full-control',\n  // only works for object ACL\n  ACLBucketOwnerEntrusted = 'bucket-owner-entrusted',\n  /**\n   * @private unstable value for object ACL\n   */\n  ACLDefault = 'default',\n}\n\nexport enum StorageClassType {\n  // storage-class will inherit from bucket if uploading object without `x-tos-storage-class` header\n  StorageClassStandard = 'STANDARD',\n  StorageClassIa = 'IA',\n  StorageClassArchiveFr = 'ARCHIVE_FR',\n  StorageClassColdArchive = 'COLD_ARCHIVE',\n  StorageClassIntelligentTiering = 'INTELLIGENT_TIERING',\n  StorageClassArchive = 'ARCHIVE',\n}\n\nexport enum MetadataDirectiveType {\n  MetadataDirectiveCopy = 'COPY',\n  MetadataDirectiveReplace = 'REPLACE',\n}\n\nexport enum AzRedundancyType {\n  AzRedundancySingleAz = 'single-az',\n  AzRedundancyMultiAz = 'multi-az',\n}\n\nexport enum PermissionType {\n  PermissionRead = 'READ',\n  PermissionWrite = 'WRITE',\n  PermissionReadAcp = 'READ_ACP',\n  PermissionWriteAcp = 'WRITE_ACP',\n  PermissionFullControl = 'FULL_CONTROL',\n  /**\n   * @private unstable value for ACL\n   */\n  PermissionReadNONLIST = 'READ_NON_LIST',\n}\n\nexport enum GranteeType {\n  GranteeGroup = 'Group',\n  GranteeUser = 'CanonicalUser',\n}\n\nexport enum CannedType {\n  CannedAllUsers = 'AllUsers',\n  CannedAuthenticatedUsers = 'AuthenticatedUsers',\n}\n\nexport enum HttpMethodType {\n  HttpMethodGet = 'GET',\n  HttpMethodPut = 'PUT',\n  HttpMethodPost = 'POST',\n  HttpMethodDelete = 'DELETE',\n  HttpMethodHead = 'HEAD',\n}\n\nexport enum StorageClassInheritDirectiveType {\n  StorageClassInheritDirectiveDestinationBucket = 'DESTINATION_BUCKET',\n  StorageClassInheritDirectiveSourceObject = 'SOURCE_OBJECT',\n}\n\nexport enum ReplicationStatusType {\n  Complete = 'COMPLETE',\n  Pending = 'PENDING',\n  Failed = 'FAILED',\n  Replica = 'REPLICA',\n}\n\nexport enum LifecycleStatusType {\n  Enabled = 'Enabled',\n  Disabled = 'Disabled',\n}\n\nexport enum RedirectType {\n  Mirror = 'Mirror',\n  Async = 'Async',\n}\n\nexport enum StatusType {\n  Enabled = 'Enabled',\n  Disabled = 'Disabled',\n}\n\nexport enum TierType {\n  TierStandard = 'Standard',\n  TierExpedited = 'Expedited',\n  TierBulk = 'Bulk',\n}\n\nexport enum VersioningStatusType {\n  Enabled = 'Enabled',\n  Suspended = 'Suspended',\n  NotSet = '',\n\n  /**\n   * @deprecated use `Enabled` instead\n   */\n  Enable = 'Enabled',\n  /**\n   * @deprecated use `NotSet` instead\n   */\n  Disable = '',\n}\n\n/**\n * @private unstable\n */\nexport enum AccessPointStatusType {\n  Ready = 'READY',\n  Creating = 'CREATING',\n  Created = 'CREATED',\n  Deleting = 'DELETING',\n}\n\n/**\n * @private unstable\n */\nexport enum TransferAccelerationStatusType {\n  Activating = 'AccelerationActivating',\n  Activated = 'AccelerationActivated',\n  Terminated = 'AccelerationTerminated',\n}\n\n/**\n * @private unstable\n */\nexport enum MRAPMirrorBackRedirectPolicyType {\n  ClosestFirst = 'Closest-First',\n  LatestFirst = 'Latest-First',\n}\n", "import TOSBase, { TosResponse } from '../../base';\nimport {\n  createMultipartUpload,\n  CreateMultipartUploadInput,\n} from './createMultipartUpload';\n\nimport { calculateSafePartSize } from './listParts';\nimport { Stats } from 'fs';\nimport { UploadPartOutput, _uploadPart } from './uploadPart';\nimport TosServerError from '../../../TosServerError';\nimport {\n  completeMultipartUpload,\n  CompleteMultipartUploadOutput,\n} from './completeMultipartUpload';\nimport { CancelToken } from 'axios';\nimport * as fsp from '../../../nodejs/fs-promises';\nimport path from 'path';\nimport {\n  makeSerialAsyncTask,\n  safeAwait,\n  safeParseCheckpointFile,\n} from '../../../utils';\nimport { CancelError } from '../../../CancelError';\nimport headObject from '../headObject';\nimport { uploadPartCopy, UploadPartCopyOutput } from './uploadPartCopy';\nimport { Headers } from '../../../interface';\nimport copyObject from '../copyObject';\nimport { getCopySourceHeaderValue, validateCheckpoint } from '../utils';\nimport cloneDeep from 'lodash/cloneDeep';\nimport TosClientError from '../../../TosClientError';\n\nexport interface ResumableCopyObjectInput extends CreateMultipartUploadInput {\n  srcBucket: string;\n  srcKey: string;\n  srcVersionId?: string;\n\n  /**\n   * default is 20 MB\n   */\n  partSize?: number;\n\n  /**\n   * the number of request to parallel upload part，default value is 1\n   */\n  taskNum?: number;\n\n  /**\n   * if checkpoint is a string and point to a exist file,\n   * the checkpoint record will recover from this file.\n   *\n   * if checkpoint is a string and point to a directory,\n   * the checkpoint will be auto generated,\n   * and its name is\n   * `{srcBucketName}.{srcObjectName}.{srcVersionId}.{bucketName}.{objectName}.copy`.\n   */\n  checkpoint?: string | ResumableCopyCheckpointRecord;\n\n  /**\n   * the callback of copy event\n   */\n  copyEventListener?: (event: ResumableCopyEvent) => void;\n\n  /**\n   * the simple progress feature\n   * percent is [0, 1]\n   */\n  progress?: (\n    percent: number,\n    checkpoint: ResumableCopyCheckpointRecord\n  ) => void;\n\n  /**\n   * is axios CancelToken\n   */\n  cancelToken?: CancelToken;\n\n  /**\n   * unit: bit/s\n   * server side traffic limit\n   **/\n  trafficLimit?: number;\n}\n\nexport interface UploadFileOutput extends CompleteMultipartUploadOutput {}\n\nexport enum ResumableCopyEventType {\n  CreateMultipartUploadSucceed = 1,\n  CreateMultipartUploadFailed = 2,\n  UploadPartCopySucceed = 3,\n  UploadPartCopyFailed = 4,\n  UploadPartCopyAborted = 5,\n  CompleteMultipartUploadSucceed = 6,\n  CompleteMultipartUploadFailed = 7,\n}\n\nexport interface CopyPartInfo {\n  partNumber: number;\n  copySourceRangeStart: number;\n  copySourceRangeEnd: number;\n\n  // has value when upload part succeed\n  etag?: string;\n}\n\nexport interface ResumableCopyEvent {\n  type: ResumableCopyEventType;\n\n  /**\n   * has value when event is failed or aborted\n   */\n  err?: Error;\n\n  bucket: string;\n  key: string;\n  uploadId?: string;\n  checkpointFile?: string;\n  copyPartInfo?: CopyPartInfo;\n}\n\nexport interface ResumableCopyCheckpointRecord {\n  bucket: string;\n  key: string;\n  part_size: number;\n  upload_id: string;\n  parts_info?: ResumableCopyCheckpointRecordPart[];\n  // Information about the file to be uploaded\n  copy_source_object_info: {\n    etag: string;\n    hash_crc64ecma: string;\n    last_modified: string;\n    object_size: number;\n  };\n  // TODO: more information\n}\n\ninterface ResumableCopyCheckpointRecordPart {\n  part_number: number;\n  copy_source_range_start: number;\n  copy_source_range_end: number;\n  etag: string;\n  is_completed: boolean;\n}\n\ninterface CheckpointRichInfo {\n  filePath?: string | undefined;\n\n  filePathIsPlaceholder?: boolean;\n\n  record?: ResumableCopyCheckpointRecord;\n}\n\ninterface Task {\n  partSize: number;\n  offset: number;\n  partNumber: number;\n}\n\nconst CHECKPOINT_FILE_NAME_PLACEHOLDER = '@@checkpoint-file-placeholder@@';\nconst ABORT_ERROR_STATUS_CODE = [403, 404, 405];\nexport const DEFAULT_PART_SIZE = 20 * 1024 * 1024; // 20 MB\n\nexport async function resumableCopyObject(\n  this: TOSBase,\n  input: ResumableCopyObjectInput\n): Promise<TosResponse<UploadFileOutput>> {\n  const { cancelToken } = input;\n  const isCancel = () => cancelToken && !!cancelToken.reason;\n  validateCheckpoint(input.checkpoint);\n\n  const { data: objectStats } = await headObject.call(this, {\n    bucket: input.srcBucket,\n    key: input.srcKey,\n    versionId: input.srcVersionId,\n  });\n  const etag = objectStats['etag'];\n  const objectSize = +objectStats['content-length'];\n\n  const checkpointRichInfo = await (async (): Promise<CheckpointRichInfo> => {\n    if (process.env.TARGET_ENVIRONMENT === 'node') {\n      if (typeof input.checkpoint === 'string') {\n        const { checkpoint } = input;\n        // file doesn't exist when stat is null\n        let checkpointStat: Stats | null = null;\n        try {\n          checkpointStat = await fsp.stat(checkpoint);\n        } catch (_err) {\n          // TODO: remove any\n          const err = _err as any;\n          if (err.code === 'ENOENT') {\n            // file doesn't exist\n          } else {\n            throw err;\n          }\n        }\n\n        const isDirectory = (() => {\n          if (checkpointStat) {\n            return checkpointStat.isDirectory();\n          }\n          return checkpoint.endsWith('/');\n        })();\n\n        // filePath will generated by uploadId, use placeholder temporarily\n        const filePath = isDirectory\n          ? path.resolve(checkpoint, CHECKPOINT_FILE_NAME_PLACEHOLDER)\n          : path.resolve(checkpoint);\n        const dirPath = path.dirname(filePath);\n        // ensure directory exist\n        await fsp.safeMkdirRecursive(dirPath);\n\n        if (isDirectory) {\n          return {\n            filePath,\n            filePathIsPlaceholder: true,\n          };\n        }\n\n        return {\n          filePath,\n          filePathIsPlaceholder: false,\n          // filePath is json file\n          // TODO: validate json schema\n          record: checkpointStat\n            ? await safeParseCheckpointFile(filePath)\n            : undefined,\n        };\n      }\n    }\n\n    if (typeof input.checkpoint === 'object') {\n      return {\n        record: input.checkpoint,\n      };\n    }\n\n    return {};\n  })();\n\n  // check if file info is matched\n  await (async () => {\n    if (checkpointRichInfo.record?.copy_source_object_info) {\n      const { last_modified, object_size } =\n        checkpointRichInfo.record?.copy_source_object_info;\n      if (\n        // TODO: `last-modified` aligns to number\n        objectStats['last-modified'] !== last_modified ||\n        +objectStats['content-length'] !== object_size\n      ) {\n        console.warn(\n          `The file has been modified since ${new Date(\n            last_modified\n          )}, so the checkpoint file is invalid, and specified file will be uploaded again.`\n        );\n        delete checkpointRichInfo.record;\n      }\n    }\n  })();\n\n  const partSize = calculateSafePartSize(\n    objectSize,\n    input.partSize || checkpointRichInfo.record?.part_size || DEFAULT_PART_SIZE,\n    true\n  );\n\n  // check partSize is matched\n  if (\n    checkpointRichInfo.record &&\n    checkpointRichInfo.record.part_size !== partSize\n  ) {\n    console.warn(\n      'The partSize param does not equal the partSize in checkpoint file, ' +\n        'so the checkpoint file is invalid, and specified file will be uploaded again.'\n    );\n    delete checkpointRichInfo.record;\n  }\n\n  let bucket = input.bucket || this.opts.bucket || '';\n  const key = input.key;\n  let uploadId = '';\n  let tasks: Task[] = [];\n  const allTasks: Task[] = getAllTasks(objectSize, partSize);\n  const initConsumedBytes = (checkpointRichInfo.record?.parts_info || [])\n    .filter((it) => it.is_completed)\n    .reduce(\n      (prev, it) =>\n        prev + it.copy_source_range_end - it.copy_source_range_start + 1,\n      0\n    );\n  let consumedBytesForProgress = initConsumedBytes;\n\n  // recorded tasks\n  const recordedTasks = checkpointRichInfo.record?.parts_info || [];\n  const recordedTaskMap: Map<number, ResumableCopyCheckpointRecordPart> =\n    new Map();\n  recordedTasks.forEach((it) => recordedTaskMap.set(it.part_number, it));\n\n  const getCheckpointContent = () => {\n    const checkpointContent: ResumableCopyCheckpointRecord = {\n      bucket,\n      key,\n      part_size: partSize,\n      upload_id: uploadId,\n      parts_info: recordedTasks,\n      copy_source_object_info: {\n        last_modified: objectStats['last-modified'],\n        etag: objectStats.etag,\n        hash_crc64ecma: objectStats['x-tos-hash-crc64ecma'] || '',\n        object_size: +objectStats['content-length'],\n      },\n    };\n    return checkpointContent;\n  };\n  const triggerUploadEvent = (\n    e: Omit<\n      ResumableCopyEvent,\n      'bucket' | 'uploadId' | 'key' | 'checkpointFile'\n    >\n  ) => {\n    if (!input.copyEventListener) {\n      return;\n    }\n\n    const event: ResumableCopyEvent = {\n      bucket,\n      uploadId,\n      key,\n      ...e,\n    };\n    if (checkpointRichInfo.filePath) {\n      event.checkpointFile = checkpointRichInfo.filePath;\n    }\n\n    input.copyEventListener(event);\n  };\n  enum TriggerProgressEventType {\n    start = 1,\n    uploadPartSucceed = 2,\n    completeMultipartUploadSucceed = 3,\n  }\n  const triggerProgressEvent = (type: TriggerProgressEventType) => {\n    if (!input.progress) {\n      return;\n    }\n\n    const percent = (() => {\n      if (type === TriggerProgressEventType.start && objectSize === 0) {\n        return 0;\n      }\n      return !objectSize ? 1 : consumedBytesForProgress / objectSize;\n    })();\n\n    if (\n      consumedBytesForProgress === objectSize &&\n      type === TriggerProgressEventType.uploadPartSucceed\n    ) {\n      // 100% 仅在 complete 后处理，以便 100% 可以拉取到新对象\n    } else {\n      input.progress(percent, getCheckpointContent());\n    }\n  };\n\n  const writeCheckpointFile = makeSerialAsyncTask(async () => {\n    if (\n      process.env.TARGET_ENVIRONMENT === 'node' &&\n      checkpointRichInfo.filePath\n    ) {\n      const content = JSON.stringify(getCheckpointContent(), null, 2);\n      const dirPath = path.dirname(checkpointRichInfo.filePath); // ensure directory exist\n      await fsp.safeMkdirRecursive(dirPath);\n      await fsp.writeFile(checkpointRichInfo.filePath, content, 'utf-8');\n    }\n  });\n  const rmCheckpointFile = async () => {\n    if (\n      process.env.TARGET_ENVIRONMENT === 'node' &&\n      checkpointRichInfo.filePath\n    ) {\n      await fsp.rm(checkpointRichInfo.filePath).catch((err: any) => {\n        // eat err\n        console.warn(\n          'remove checkpoint file failure, you can remove it by hand.\\n',\n          `checkpoint file path: ${checkpointRichInfo.filePath}\\n`,\n          err.message\n        );\n      });\n    }\n  };\n\n  /**\n   *\n   * @param task one part task\n   * @param uploadPartRes upload part failed if `uploadPartRes` is Error\n   */\n  const updateAfterUploadPart = async (\n    task: Task,\n    uploadPartRes:\n      | {\n          res: UploadPartCopyOutput;\n          err?: null;\n        }\n      | {\n          err: Error;\n        }\n  ) => {\n    let existRecordTask = recordedTaskMap.get(task.partNumber);\n    const rangeStart = task.offset;\n    const rangeEnd = Math.min(task.offset + partSize - 1, objectSize - 1);\n    if (!existRecordTask) {\n      existRecordTask = {\n        part_number: task.partNumber,\n        copy_source_range_start: rangeStart,\n        copy_source_range_end: rangeEnd,\n        is_completed: false,\n        etag: '',\n      };\n      recordedTasks.push(existRecordTask);\n      recordedTaskMap.set(existRecordTask.part_number, existRecordTask);\n    }\n\n    if (!uploadPartRes.err) {\n      existRecordTask.is_completed = true;\n      existRecordTask.etag = uploadPartRes.res.ETag;\n    }\n\n    await writeCheckpointFile();\n    const copyPartInfo: CopyPartInfo = {\n      partNumber: existRecordTask.part_number,\n      copySourceRangeEnd: existRecordTask.copy_source_range_end,\n      copySourceRangeStart: existRecordTask.copy_source_range_start,\n    };\n\n    if (uploadPartRes.err) {\n      const err = uploadPartRes.err;\n      let type: ResumableCopyEventType =\n        ResumableCopyEventType.UploadPartCopyFailed;\n\n      if (err instanceof TosServerError) {\n        if (ABORT_ERROR_STATUS_CODE.includes(err.statusCode)) {\n          type = ResumableCopyEventType.UploadPartCopyAborted;\n        }\n      }\n\n      triggerUploadEvent({\n        type,\n        err,\n        copyPartInfo,\n      });\n      return;\n    }\n\n    copyPartInfo.etag = uploadPartRes.res.ETag;\n    consumedBytesForProgress +=\n      copyPartInfo.copySourceRangeEnd - copyPartInfo.copySourceRangeStart + 1;\n\n    triggerUploadEvent({\n      type: ResumableCopyEventType.UploadPartCopySucceed,\n      copyPartInfo,\n    });\n    triggerProgressEvent(TriggerProgressEventType.uploadPartSucceed);\n  };\n\n  if (checkpointRichInfo.record) {\n    bucket = checkpointRichInfo.record.bucket;\n    uploadId = checkpointRichInfo.record.upload_id;\n\n    // checkpoint info exists, so need to calculate remain tasks\n    const uploadedPartSet: Set<number> = new Set(\n      (checkpointRichInfo.record.parts_info || [])\n        .filter((it) => it.is_completed)\n        .map((it) => it.part_number)\n    );\n    tasks = allTasks.filter((it) => !uploadedPartSet.has(it.partNumber));\n  } else {\n    // createMultipartUpload will check bucket\n    try {\n      const { data: multipartRes } = await createMultipartUpload.call(\n        this,\n        cloneDeep(input)\n      );\n      if (isCancel()) {\n        throw new CancelError('cancel uploadFile');\n      }\n\n      bucket = multipartRes.Bucket;\n      uploadId = multipartRes.UploadId;\n      if (checkpointRichInfo.filePathIsPlaceholder) {\n        checkpointRichInfo.filePath = checkpointRichInfo.filePath?.replace(\n          `${CHECKPOINT_FILE_NAME_PLACEHOLDER}`,\n          getDefaultCheckpointFilePath({\n            ...input,\n            bucket,\n          })\n        );\n      }\n\n      triggerUploadEvent({\n        type: ResumableCopyEventType.CreateMultipartUploadSucceed,\n      });\n    } catch (_err) {\n      const err = _err as Error;\n      triggerUploadEvent({\n        type: ResumableCopyEventType.CreateMultipartUploadFailed,\n        err,\n      });\n      throw err;\n    }\n\n    tasks = allTasks;\n  }\n\n  const handleTasks = async () => {\n    let firstErr: Error | null = null;\n    let index = 0;\n\n    // TODO: how to test parallel does work, measure time is not right\n    await Promise.all(\n      Array.from({ length: input.taskNum || 1 }).map(async () => {\n        while (true) {\n          const currentIndex = index++;\n          if (currentIndex >= tasks.length) {\n            return;\n          }\n\n          const curTask = tasks[currentIndex];\n          try {\n            let copySource = getCopySourceHeaderValue(\n              input.srcBucket,\n              input.srcKey\n            );\n            if (input.srcVersionId) {\n              copySource += `?versionId=${input.srcVersionId}`;\n            }\n            const copyRange = `bytes=${curTask.offset}-${\n              curTask.offset + curTask.partSize - 1\n            }`;\n            const headers: Headers = {\n              ...input.headers,\n              ['x-tos-copy-source']: copySource,\n              ['x-tos-copy-source-if-match']: etag,\n              ['x-tos-copy-source-range']: copyRange,\n            };\n\n            if (!curTask.partSize) {\n              delete headers['x-tos-copy-source-range'];\n            }\n            const { data: uploadPartRes } = await uploadPartCopy.call(this, {\n              bucket,\n              key,\n              uploadId,\n              partNumber: curTask.partNumber,\n              headers,\n              trafficLimit: input.trafficLimit,\n            });\n\n            if (isCancel()) {\n              throw new CancelError('cancel resumableCopyObject');\n            }\n\n            await updateAfterUploadPart(curTask, { res: uploadPartRes });\n          } catch (_err) {\n            const err = _err as any;\n\n            if (isCancelError(err)) {\n              throw err;\n            }\n\n            if (isCancel()) {\n              throw new CancelError('cancel resumableCopyObject');\n            }\n\n            if (!firstErr) {\n              firstErr = err;\n            }\n            await updateAfterUploadPart(curTask, { err });\n          }\n        }\n      })\n    );\n\n    if (firstErr) {\n      throw firstErr;\n    }\n\n    const parts = (getCheckpointContent().parts_info || []).map((it) => ({\n      eTag: it.etag,\n      partNumber: it.part_number,\n    }));\n\n    const [err, res] = await safeAwait(\n      completeMultipartUpload.call(this, {\n        bucket,\n        key,\n        uploadId,\n        parts,\n      })\n    );\n\n    if (err || !res) {\n      triggerUploadEvent({\n        type: ResumableCopyEventType.CompleteMultipartUploadFailed,\n      });\n      throw err;\n    }\n\n    triggerUploadEvent({\n      type: ResumableCopyEventType.CompleteMultipartUploadSucceed,\n    });\n    triggerProgressEvent(\n      TriggerProgressEventType.completeMultipartUploadSucceed\n    );\n\n    const sourceCRC64 =\n      getCheckpointContent().copy_source_object_info.hash_crc64ecma;\n    const actualCrc64 = res.data.HashCrc64ecma;\n    if (\n      this.opts.enableCRC &&\n      sourceCRC64 &&\n      actualCrc64 &&\n      sourceCRC64 !== actualCrc64\n    ) {\n      throw new TosClientError(\n        `validate file crc64 failed. Expect crc64 ${sourceCRC64}, actual crc64 ${actualCrc64}. Please try again.`\n      );\n    }\n\n    await rmCheckpointFile();\n\n    return res;\n  };\n\n  const handleEmptyObj = async (): Promise<TosResponse<UploadFileOutput>> => {\n    let copySource = getCopySourceHeaderValue(input.srcBucket, input.srcKey);\n    if (input.srcVersionId) {\n      copySource += `?versionId=${input.srcVersionId}`;\n    }\n    const headers: Headers = {\n      ...input.headers,\n      ['x-tos-copy-source']: copySource,\n      ['x-tos-copy-source-if-match']: etag,\n    };\n\n    const [err, res] = await safeAwait(\n      copyObject.call(this, {\n        bucket: input.bucket,\n        key: input.key,\n        headers,\n        trafficLimit: input.trafficLimit,\n      })\n    );\n    if (err || !res) {\n      triggerUploadEvent({\n        type: ResumableCopyEventType.UploadPartCopyFailed,\n      });\n      throw err;\n    }\n\n    triggerProgressEvent(\n      TriggerProgressEventType.completeMultipartUploadSucceed\n    );\n    triggerUploadEvent({\n      type: ResumableCopyEventType.UploadPartCopySucceed,\n      copyPartInfo: {\n        partNumber: 0,\n        copySourceRangeStart: 0,\n        copySourceRangeEnd: 0,\n      },\n    });\n    triggerUploadEvent({\n      type: ResumableCopyEventType.CompleteMultipartUploadSucceed,\n    });\n\n    return {\n      ...res,\n      data: {\n        ETag: res.headers['etag'] || '',\n        Bucket: bucket,\n        Key: key,\n        Location: `http${this.opts.secure ? 's' : ''}://${bucket}.${\n          this.opts.endpoint\n        }/${key}`,\n        VersionID: res.headers['x-tos-version-id'],\n        HashCrc64ecma: res.headers['x-tos-hash-crc64ecma'],\n      },\n    };\n  };\n\n  triggerProgressEvent(TriggerProgressEventType.start);\n  return objectSize === 0 ? handleEmptyObj() : handleTasks();\n}\n\nexport function isCancelError(err: any) {\n  return err instanceof CancelError;\n}\n\nexport default resumableCopyObject;\n\n/**\n * 即使 totalSize 是 0，也需要一个 Part，否则 Server 端会报错 read request body failed\n */\nfunction getAllTasks(totalSize: number, partSize: number) {\n  const tasks: Task[] = [];\n  for (let i = 0; ; ++i) {\n    const offset = i * partSize;\n    const currPartSize = Math.min(partSize, totalSize - offset);\n\n    tasks.push({\n      offset,\n      partSize: currPartSize,\n      partNumber: i + 1,\n    });\n\n    if ((i + 1) * partSize >= totalSize) {\n      break;\n    }\n  }\n\n  return tasks;\n}\n\nfunction getDefaultCheckpointFilePath(\n  opts: Pick<\n    ResumableCopyObjectInput,\n    'srcBucket' | 'srcKey' | 'srcVersionId' | 'key'\n  > & {\n    bucket: string;\n  }\n) {\n  const originPath = [\n    opts.srcBucket,\n    opts.srcKey,\n    opts.srcVersionId,\n    opts.bucket,\n    opts.key,\n    'copy',\n  ]\n    .filter(Boolean)\n    .join('.');\n\n  const normalizePath = originPath.replace(/[\\\\/]/g, '');\n  return normalizePath;\n}\n", "import { StorageClass } from '../../interface';\nimport { fillRequestHeaders, normalizeHeadersKey } from '../../utils';\nimport TOSBase from '../base';\nimport { ReplicationStatusType } from '../../TosExportEnum';\nimport { RestoreInfo, TosHeader } from './sharedTypes';\nimport { getRestoreInfoFromHeaders } from './utils';\n\nexport interface HeadObjectInput {\n  bucket?: string;\n  key: string;\n  versionId?: string;\n\n  ifMatch?: string;\n  ifModifiedSince?: string;\n  ifNoneMatch?: string;\n  ifUnmodifiedSince?: string;\n\n  ssecAlgorithm?: string;\n  ssecKey?: string;\n  ssecKeyMD5?: string;\n\n  headers?: {\n    [key: string]: string | undefined;\n    'If-Match'?: string;\n    'If-Modified-Since'?: string;\n    'If-None-Match'?: string;\n    'If-Unmodified-Since'?: string;\n    'x-tos-server-side-encryption-customer-algorithm'?: string;\n    'x-tos-server-side-encryption-customer-key'?: string;\n    'x-tos-server-side-encryption-customer-key-md5'?: string;\n  };\n}\n\nexport interface HeadObjectOutput {\n  [key: string]: string | undefined | object;\n  'content-length': string;\n  'last-modified': string;\n  'content-md5': string;\n  etag: string;\n  'x-tos-object-type'?: 'Appendable' | 'Symlink';\n  'x-tos-delete-marker'?: string;\n  'x-tos-server-side-encryption-customer-algorithm'?: string;\n  'x-tos-server-side-encryption-customer-key-md5'?: string;\n  'x-tos-version-id'?: string;\n  'x-tos-website-redirect-location'?: string;\n  'x-tos-hash-crc64ecma'?: string;\n  'x-tos-storage-class': StorageClass;\n  'x-tos-server-side-encryption'?: string;\n  'x-tos-replication-status'?: ReplicationStatusType;\n  'x-tos-symlink-target-size'?: string;\n  RestoreInfo?: RestoreInfo;\n  ReplicationStatus?: ReplicationStatusType;\n}\n\nexport async function headObject(\n  this: TOSBase,\n  input: HeadObjectInput | string\n) {\n  const normalizedInput = typeof input === 'string' ? { key: input } : input;\n  const headers = normalizeHeadersKey(normalizedInput.headers);\n  normalizedInput.headers = headers;\n\n  const query: Record<string, any> = {};\n  if (normalizedInput.versionId) {\n    query.versionId = normalizedInput.versionId;\n  }\n\n  fillRequestHeaders(normalizedInput, [\n    'ifMatch',\n    'ifModifiedSince',\n    'ifNoneMatch',\n    'ifUnmodifiedSince',\n    'ssecAlgorithm',\n    'ssecKey',\n    'ssecKeyMD5',\n  ]);\n\n  return this._fetchObject<HeadObjectOutput>(\n    input,\n    'HEAD',\n    query,\n    normalizedInput?.headers || {},\n    undefined,\n    {\n      handleResponse: (res) => {\n        const result = {\n          ...res.headers,\n          ReplicationStatus: res.headers[TosHeader.HeaderReplicationStatus],\n        };\n        const info = getRestoreInfoFromHeaders(res.headers);\n\n        if (info) {\n          result.RestoreInfo = info;\n        }\n        return result;\n      },\n    }\n  );\n}\n\nexport default headObject;\n", "import {\n  fillRequestHeaders,\n  safeAwait,\n  normalizeHeadersKey,\n  requestHeadersMap,\n} from '../../../utils';\nimport TOSBase from '../../base';\nimport { getCopySourceHeaderValue } from '../utils';\n\nexport interface UploadPartCopyInput {\n  bucket?: string;\n  key: string;\n  partNumber: number;\n  uploadId: string;\n\n  srcBucket?: string;\n  srcKey?: string;\n  srcVersionID?: string;\n  copySourceRange?: string;\n  copySourceRangeStart?: number;\n  copySourceRangeEnd?: number;\n  copySourceSSECAlgorithm?: string;\n  copySourceSSECKey?: string;\n  copySourceSSECKeyMD5?: string;\n  ssecAlgorithm?: string;\n  ssecKey?: string;\n  ssecKeyMD5?: string;\n  /**\n   * unit: bit/s\n   * server side traffic limit\n   **/\n  trafficLimit?: number;\n  headers?: {\n    [key: string]: string | undefined;\n    'x-tos-copy-source'?: string;\n    'x-tos-copy-source-range'?: string;\n    'x-tos-copy-source-if-match'?: string;\n    'x-tos-copy-source-if-modified-since'?: string;\n    'x-tos-copy-source-if-none-match'?: string;\n    'x-tos-copy-source-if-unmodified-since'?: string;\n    'x-tos-copy-source-server-side-encryption-customer-algorithm'?: string;\n    'x-tos-copy-source-server-side-encryption-customer-key'?: string;\n    'x-tos-copy-source-server-side-encryption-customer-key-MD5'?: string;\n  };\n}\n\nexport interface UploadPartCopyOutput {\n  ETag: string;\n  LastModified: string;\n  SSECAlgorithm: string;\n  SSECKeyMD5: string;\n}\n\nexport async function uploadPartCopy(\n  this: TOSBase,\n  input: UploadPartCopyInput\n) {\n  const { uploadId, partNumber } = input;\n  const headers = normalizeHeadersKey(input.headers);\n  input.headers = headers;\n  fillRequestHeaders(input, [\n    'copySourceRange',\n    'copySourceSSECAlgorithm',\n    'copySourceSSECKey',\n    'copySourceSSECKeyMD5',\n    'ssecAlgorithm',\n    'ssecKey',\n    'ssecKeyMD5',\n    'trafficLimit',\n  ]);\n  if (input.srcBucket && input.srcKey) {\n    let copySource = getCopySourceHeaderValue(input.srcBucket, input.srcKey);\n    if (input.srcVersionID) {\n      copySource += `?versionId=${input.srcVersionID}`;\n    }\n    headers['x-tos-copy-source'] = headers['x-tos-copy-source'] ?? copySource;\n  }\n\n  if (\n    input.copySourceRange == null &&\n    (input.copySourceRangeStart != null || input.copySourceRangeEnd != null)\n  ) {\n    const start =\n      input.copySourceRangeStart != null ? `${input.copySourceRangeStart}` : '';\n    const end =\n      input.copySourceRangeEnd != null ? `${input.copySourceRangeEnd}` : '';\n    const copyRange = `bytes=${start}-${end}`;\n    headers['x-tos-copy-source-range'] =\n      headers['x-tos-copy-source-range'] ?? copyRange;\n  }\n\n  const [err, res] = await safeAwait(\n    this._fetchObject<UploadPartCopyOutput>(\n      input,\n      'PUT',\n      { partNumber, uploadId },\n      headers,\n      undefined,\n      {\n        handleResponse(response) {\n          return {\n            ...response.data,\n            SSECAlgorithm:\n              response.headers[requestHeadersMap['ssecAlgorithm'] as string],\n            SSECKeyMD5:\n              response.headers[requestHeadersMap['ssecKeyMD5'] as string],\n          };\n        },\n      }\n    )\n  );\n\n  if (err || !res || !res.data.ETag) {\n    // TODO: throw TosServerErr\n    throw err;\n  }\n\n  return res;\n}\n", "import {\n  safeAwait,\n  normalizeHeadersKey,\n  fillRequestHeaders,\n} from '../../utils';\nimport { StorageClass, ServerSideEncryption, Acl } from '../../interface';\nimport TOSBase, { TosResponse } from '../base';\nimport { StorageClassType } from '../../TosExportEnum';\nimport { getCopySourceHeaderValue } from './utils';\n\nexport interface CopyObjectInput {\n  bucket?: string;\n  key: string;\n\n  srcBucket?: string;\n  srcKey?: string;\n  srcVersionID?: string;\n  cacheControl?: string;\n  contentDisposition?: string;\n  contentEncoding?: string;\n  contentLanguage?: string;\n  contentType?: string;\n  expires?: Date;\n\n  copySourceIfMatch?: string;\n  copySourceIfModifiedSince?: string | Date;\n  copySourceIfNoneMatch?: string;\n  copySourceIfUnmodifiedSince?: string;\n  copySourceSSECAlgorithm?: string;\n  copySourceSSECKey?: string;\n  copySourceSSECKeyMD5?: string;\n\n  ssecAlgorithm?: string;\n  ssecKey?: string;\n  ssecKeyMD5?: string;\n  serverSideEncryption?: string;\n  /**\n   * unit: bit/s\n   * server side traffic limit\n   **/\n  trafficLimit?: number;\n\n  acl?: Acl;\n  grantFullControl?: string;\n  grantRead?: string;\n  grantReadAcp?: string;\n  grantWriteAcp?: string;\n\n  metadataDirective?: string;\n  meta?: Record<string, string>;\n  websiteRedirectLocation?: string;\n  storageClass?: StorageClassType;\n  ifMatch?: string;\n  forbidOverwrite?: boolean;\n\n  headers?: {\n    [key: string]: string | undefined;\n    ['x-tos-copy-source']?: string;\n    ['x-tos-acl']?: string;\n    ['x-tos-copy-source-if-match']?: string;\n    ['x-tos-copy-source-if-modified-since']?: string;\n    ['x-tos-copy-source-if-none-match']?: string;\n    ['x-tos-copy-source-if-unmodified-since']?: string;\n    ['x-tos-copy-source-server-side-encryption-customer-algorithm']?: string;\n    ['x-tos-copy-source-server-side-encryption-customer-key']?: string;\n    ['x-tos-copy-source-server-side-encryption-customer-key-MD5']?: string;\n    ['x-tos-grant-full-control']?: string;\n    ['x-tos-grant-read']?: string;\n    ['x-tos-grant-read-acp']?: string;\n    ['x-tos-metadata-directive']?: string;\n    ['x-tos-website-redirect-location']?: string;\n    ['x-tos-storage-class']?: StorageClass;\n    ['x-tos-server-side-encryption']?: ServerSideEncryption;\n    ['x-tos-forbid-overwrite']?: string;\n    'If-Match'?: string;\n  };\n}\n\ninterface CopyObjectBody {\n  ETag: string;\n}\n\nexport interface CopyObjectOutput extends CopyObjectBody {}\n\nexport async function copyObject(\n  this: TOSBase,\n  input: CopyObjectInput\n): Promise<TosResponse<CopyObjectOutput>> {\n  const headers = normalizeHeadersKey(input.headers);\n  input.headers = headers;\n  fillRequestHeaders(input, [\n    'cacheControl',\n    'contentDisposition',\n    'contentEncoding',\n    'contentLanguage',\n    'contentType',\n    'expires',\n\n    'copySourceIfMatch',\n    'copySourceIfModifiedSince',\n    'copySourceIfNoneMatch',\n    'copySourceIfUnmodifiedSince',\n    'copySourceSSECAlgorithm',\n    'copySourceSSECKey',\n    'copySourceSSECKeyMD5',\n\n    'acl',\n    'grantFullControl',\n    'grantRead',\n    'grantReadAcp',\n    'grantWriteAcp',\n\n    'ssecAlgorithm',\n    'ssecKey',\n    'ssecKeyMD5',\n    'serverSideEncryption',\n\n    'metadataDirective',\n    'meta',\n    'websiteRedirectLocation',\n    'storageClass',\n    'trafficLimit',\n    'forbidOverwrite',\n    'ifMatch',\n  ]);\n  if (input.srcBucket && input.srcKey) {\n    let copySource = getCopySourceHeaderValue(input.srcBucket, input.srcKey);\n    if (input.srcVersionID) {\n      copySource += `?versionId=${input.srcVersionID}`;\n    }\n    headers['x-tos-copy-source'] = headers['x-tos-copy-source'] ?? copySource;\n  }\n\n  const [err, res] = await safeAwait(\n    this._fetchObject<CopyObjectBody>(input, 'PUT', {}, headers)\n  );\n\n  if (err || !res || !res.data.ETag) {\n    // TODO: throw TosServerErr\n    throw err;\n  }\n  return res;\n}\n\nexport default copyObject;\n", "import { createWriteStream } from '../../nodejs/fs-promises';\nimport TosClientError from '../../TosClientError';\nimport { DataTransferStatus, DataTransferType, Headers } from '../../interface';\nimport {\n  fillRequestHeaders,\n  fillRequestQ<PERSON>y,\n  streamToBuf,\n  isReadable,\n  normalize<PERSON><PERSON><PERSON><PERSON>ey,\n  safeAwait,\n} from '../../utils';\nimport TOSBase, { TosResponse } from '../base';\nimport {\n  IRateLimiter,\n  createRateLimiterStream,\n} from '../../universal/rate-limiter';\nimport { getRestoreInfoFromHeaders, isValidRateLimiter } from './utils';\nimport { createReadNReadStream } from '../../nodejs/EmitReadStream';\nimport { RestoreInfo, TosHeader } from './sharedTypes';\nimport { ReplicationStatusType } from '../../TosExportEnum';\n\nexport interface GetObjectInput {\n  bucket?: string;\n  key: string;\n  versionId?: string;\n\n  headers?: {\n    [key: string]: string | undefined;\n    'If-Modified-Since'?: string;\n    'If-Unmodified-Since'?: string;\n    'If-Match'?: string;\n    'If-None-Match'?: string;\n    'x-tos-server-side-encryption-customer-key'?: string;\n    'x-tos-server-side-encryption-customer-key-md5'?: string;\n    'x-tos-server-side-encryption-customer-algorithm'?: string;\n    Range?: string;\n  };\n  response?: Headers & {\n    'cache-control'?: string;\n    'content-disposition'?: string;\n    'content-encoding'?: string;\n    'content-language'?: string;\n    'content-type'?: string;\n    expires?: string;\n  };\n}\n\n/**\n * @deprecated use getObjectV2 instead\n * @returns arraybuffer\n */\nexport async function getObject(this: TOSBase, input: GetObjectInput | string) {\n  const normalizedInput = typeof input === 'string' ? { key: input } : input;\n  const query: Record<string, any> = {};\n  if (normalizedInput.versionId) {\n    query.versionId = normalizedInput.versionId;\n  }\n  const headers: Headers = normalizeHeadersKey(normalizedInput?.headers);\n  const response: Partial<Headers> = normalizedInput?.response || {};\n  Object.keys(response).forEach((key: string) => {\n    const v = response[key];\n    if (v != null) {\n      query[`response-${key}`] = v;\n    }\n  });\n\n  // TODO: maybe need to return response's headers\n  return this._fetchObject<Buffer>(input, 'GET', query, headers, undefined, {\n    axiosOpts: { responseType: 'arraybuffer' },\n  });\n}\n\ntype DataType = 'stream' | 'buffer' | 'blob';\nexport interface GetObjectV2Input {\n  bucket?: string;\n  key: string;\n  versionId?: string;\n\n  /**\n   * The type of return value, 'stream' | 'blob'\n   * default: 'stream'\n   *\n   * nodejs environment can use 'stream' and 'buffer'\n   * browser environment can use 'blob'\n   */\n  dataType?: DataType;\n\n  ifMatch?: string;\n  ifModifiedSince?: string | Date;\n  ifNoneMatch?: string;\n  ifUnmodifiedSince?: string | Date;\n\n  ssecAlgorithm?: string;\n  ssecKey?: string;\n  ssecKeyMD5?: string;\n  /**\n   * unit: bit/s\n   * server side traffic limit\n   **/\n  trafficLimit?: number;\n  /**\n   * only works for nodejs environment\n   */\n  rateLimiter?: IRateLimiter;\n\n  range?: string;\n  rangeStart?: number;\n  rangeEnd?: number;\n\n  process?: string;\n  // need base64url encode\n  saveBucket?: string;\n  // need base64url encode\n  saveObject?: string;\n\n  responseCacheControl?: string;\n  responseContentDisposition?: string;\n  responseContentEncoding?: string;\n  responseContentLanguage?: string;\n  responseContentType?: string;\n  responseExpires?: Date;\n\n  dataTransferStatusChange?: (status: DataTransferStatus) => void;\n  /**\n   * the simple progress feature\n   * percent is [0, 1].\n   */\n  progress?: (percent: number) => void;\n\n  headers?: {\n    [key: string]: string | undefined;\n    'If-Modified-Since'?: string;\n    'If-Unmodified-Since'?: string;\n    'If-Match'?: string;\n    'If-None-Match'?: string;\n    'x-tos-server-side-encryption-customer-key'?: string;\n    'x-tos-server-side-encryption-customer-key-md5'?: string;\n    'x-tos-server-side-encryption-customer-algorithm'?: string;\n    range?: string;\n  };\n  /**\n   * @deprecated use responseXxx options instead\n   */\n  response?: Headers & {\n    'cache-control'?: string;\n    'content-disposition'?: string;\n    'content-encoding'?: string;\n    'content-language'?: string;\n    'content-type'?: string;\n    expires?: string;\n  };\n}\nexport interface GetObjectV2Output {\n  content: NodeJS.ReadableStream | Buffer | Blob;\n  etag: string;\n  lastModified: string;\n\n  // object created before tos server supports crc, hashCrc64ecma will be empty string\n  hashCrc64ecma: string;\n  RestoreInfo?: RestoreInfo;\n  ReplicationStatus?: ReplicationStatusType;\n}\n\nexport interface GetObjectV2OutputStream\n  extends Omit<GetObjectV2Output, 'content'> {\n  content: NodeJS.ReadableStream;\n}\nexport interface GetObjectV2InputBuffer\n  extends Omit<GetObjectV2Input, 'dataType'> {\n  dataType: 'buffer';\n}\nexport interface GetObjectV2OutputBuffer\n  extends Omit<GetObjectV2Output, 'content'> {\n  content: Buffer;\n}\nexport interface GetObjectV2InputBlob\n  extends Omit<GetObjectV2Input, 'dataType'> {\n  dataType: 'blob';\n}\nexport interface GetObjectV2OutputBlob\n  extends Omit<GetObjectV2Output, 'content'> {\n  content: Blob;\n}\n\nconst NODEJS_DATATYPE: DataType[] = ['stream', 'buffer'];\nconst BROWSER_DATATYPE: DataType[] = ['blob'];\n\nfunction checkSupportDataType(dataType: DataType) {\n  let environment: 'node' | 'browser' = 'node';\n  let supportDataTypes: DataType[] = [];\n  if (process.env.TARGET_ENVIRONMENT === 'node') {\n    environment = 'node';\n    supportDataTypes = NODEJS_DATATYPE;\n  } else {\n    environment = 'browser';\n    supportDataTypes = BROWSER_DATATYPE;\n  }\n  if (!supportDataTypes.includes(dataType)) {\n    throw new TosClientError(\n      `The value of \\`dataType\\` only supports \\`${supportDataTypes.join(\n        ' | '\n      )}\\` in ${environment} environment`\n    );\n  }\n}\n\n/**\n * `getObjectV2` default returns stream, using `dataType` param to return other type(eg: buffer, blob)\n */\nasync function getObjectV2(\n  this: TOSBase,\n  input: GetObjectV2InputBlob\n): Promise<TosResponse<GetObjectV2OutputBlob>>;\nasync function getObjectV2(\n  this: TOSBase,\n  input: GetObjectV2InputBuffer\n): Promise<TosResponse<GetObjectV2OutputBuffer>>;\nasync function getObjectV2(\n  this: TOSBase,\n  input: GetObjectV2Input | string\n): Promise<TosResponse<GetObjectV2OutputStream>>;\nasync function getObjectV2(\n  this: TOSBase,\n  input: GetObjectV2Input | string\n): Promise<TosResponse<GetObjectV2Output>> {\n  const normalizedInput = typeof input === 'string' ? { key: input } : input;\n  const headers = normalizeHeadersKey(normalizedInput.headers);\n  normalizedInput.headers = headers;\n  const dataType = normalizedInput.dataType || 'stream';\n  normalizedInput.dataType = dataType;\n\n  checkSupportDataType(dataType);\n\n  const query: Record<string, unknown> = {};\n  const response: Partial<Headers> = normalizedInput?.response || {};\n  Object.keys(response).forEach((key: string) => {\n    const v = response[key];\n    if (v != null) {\n      query[`response-${key}`] = v;\n    }\n  });\n  fillRequestQuery(normalizedInput, query, [\n    'versionId',\n    'process',\n    'saveBucket',\n    'saveObject',\n    'responseCacheControl',\n    'responseContentDisposition',\n    'responseContentEncoding',\n    'responseContentLanguage',\n    'responseContentType',\n    'responseExpires',\n  ]);\n\n  fillRequestHeaders(normalizedInput, [\n    'ifMatch',\n    'ifModifiedSince',\n    'ifNoneMatch',\n    'ifUnmodifiedSince',\n\n    'ssecAlgorithm',\n    'ssecKey',\n    'ssecKeyMD5',\n\n    'range',\n    'trafficLimit',\n  ]);\n\n  if (\n    normalizedInput.range == null &&\n    (normalizedInput.rangeStart != null || normalizedInput.rangeEnd != null)\n  ) {\n    const start =\n      normalizedInput.rangeStart != null ? `${normalizedInput.rangeStart}` : '';\n    const end =\n      normalizedInput.rangeEnd != null ? `${normalizedInput.rangeEnd}` : '';\n    const range = `bytes=${start}-${end}`;\n    headers['range'] = headers['range'] ?? range;\n  }\n\n  const responseType = (() => {\n    if (process.env.TARGET_ENVIRONMENT === 'node') {\n      return 'stream';\n    }\n    return 'arraybuffer';\n  })();\n\n  let consumedBytes = 0;\n  // totalSize is unknown when start download\n  let totalSize = -1;\n  const { dataTransferStatusChange, progress } = normalizedInput;\n  const triggerDataTransfer = (\n    type: DataTransferType,\n    rwOnceBytes: number = 0\n  ) => {\n    // request cancel will make rwOnceBytes < 0 in browser\n    if (rwOnceBytes < 0) {\n      return;\n    }\n    if (!dataTransferStatusChange && !progress) {\n      return;\n    }\n    consumedBytes += rwOnceBytes;\n    dataTransferStatusChange?.({\n      type,\n      rwOnceBytes,\n      consumedBytes,\n      totalBytes: totalSize,\n    });\n    const progressValue = (() => {\n      // `totalSize` is unknown if it's in start or fail\n      if (totalSize < 0) {\n        return 0;\n      }\n\n      if (totalSize === 0) {\n        if (type === DataTransferType.Succeed) {\n          return 1;\n        }\n        return 0;\n      }\n      return consumedBytes / totalSize;\n    })();\n    if (progressValue === 1) {\n      if (type === DataTransferType.Succeed) {\n        progress?.(progressValue);\n      } else {\n        // not exec progress\n      }\n    } else {\n      progress?.(progressValue);\n    }\n  };\n\n  triggerDataTransfer(DataTransferType.Started);\n  const [err, res] = await safeAwait(\n    this._fetchObject<any>(input, 'GET', query, headers, undefined, {\n      axiosOpts: {\n        responseType,\n        onDownloadProgress: (event) => {\n          totalSize = event.total;\n          triggerDataTransfer(\n            DataTransferType.Rw,\n            event.loaded - consumedBytes\n          );\n        },\n      },\n    })\n  );\n  if (err || !res) {\n    triggerDataTransfer(DataTransferType.Failed);\n    throw err;\n  }\n\n  let resHeaders = res.headers;\n  let newData: NodeJS.ReadableStream | Blob | Buffer = res.data;\n  totalSize = +(resHeaders['content-length'] || 0);\n\n  if (process.env.TARGET_ENVIRONMENT === 'node') {\n    // res.data must be a stream in nodejs environment\n    if (isReadable(newData)) {\n      if (\n        normalizedInput.rateLimiter &&\n        isValidRateLimiter(normalizedInput.rateLimiter)\n      ) {\n        newData = createRateLimiterStream(\n          newData as NodeJS.ReadableStream,\n          normalizedInput.rateLimiter\n        );\n      }\n\n      newData = createReadNReadStream(newData, (n) =>\n        triggerDataTransfer(DataTransferType.Rw, n)\n      );\n      newData.on('end', () => triggerDataTransfer(DataTransferType.Succeed));\n\n      if (dataType === 'buffer') {\n        // consume stream after `createRateLimiterStream`\n        newData = await streamToBuf(newData);\n      }\n    } else {\n      // should not enter this branch\n    }\n  } else {\n    // 浏览器环境\n    if (dataType === 'blob') {\n      newData = new Blob([res.data], {\n        type: resHeaders['content-type'],\n      });\n    }\n    triggerDataTransfer(DataTransferType.Succeed);\n  }\n\n  const actualRes: TosResponse<GetObjectV2Output> = {\n    ...res,\n    data: {\n      content: newData,\n      etag: resHeaders['etag'] || '',\n      lastModified: resHeaders['last-modified'] || '',\n      hashCrc64ecma: resHeaders['x-tos-hash-crc64ecma'] || '',\n      ReplicationStatus: resHeaders[TosHeader.HeaderReplicationStatus] as\n        | ReplicationStatusType\n        | undefined,\n    },\n  };\n\n  const info = getRestoreInfoFromHeaders(resHeaders);\n  if (info) {\n    actualRes.data.RestoreInfo = info;\n  }\n  return actualRes;\n}\n\ninterface GetObjectToFileInput extends Omit<GetObjectV2Input, 'dataType'> {\n  filePath: string;\n}\ninterface GetObjectToFileOutput extends Omit<GetObjectV2Output, 'content'> {}\n\nexport async function getObjectToFile(\n  this: TOSBase,\n  input: GetObjectToFileInput\n): Promise<TosResponse<GetObjectToFileOutput>> {\n  if (process.env.TARGET_ENVIRONMENT !== 'node') {\n    throw new TosClientError(\n      \"getObjectToFile doesn't support in browser environment\"\n    );\n  }\n\n  return new Promise(async (resolve, reject) => {\n    const getObjectRes = await getObjectV2.call(this, input);\n    const stream = getObjectRes.data.content;\n\n    const fsWriteStream = createWriteStream(input.filePath);\n    stream.pipe(fsWriteStream);\n    stream.on('error', (err) => fsWriteStream.destroy(err));\n    fsWriteStream.on('error', (err) => reject(err));\n    fsWriteStream.on('finish', () => {\n      const newData: any = { ...getObjectRes.data };\n      delete newData.content;\n      resolve({ ...getObjectRes, data: { ...newData } });\n    });\n  });\n}\n\nexport { getObjectV2 };\n", "import TOSBase, { TosResponse } from '../base';\nimport {\n  DEFAULT_PART_SIZE,\n  isCancelError,\n  makeSerialAsyncTask,\n  safeParseCheckpointFile,\n} from '../../utils';\nimport * as fsp from '../../nodejs/fs-promises';\nimport { DataTransferStatus, DataTransferType } from '../../interface';\nimport headObject, { HeadObjectInput, HeadObjectOutput } from './headObject';\nimport { CancelToken } from 'axios';\nimport { Stats } from 'fs';\nimport path from 'path';\nimport TosClientError from '../../TosClientError';\nimport { getObjectV2, GetObjectV2Output } from './getObject';\nimport TosServerError from '../../TosServerError';\nimport { CancelError } from '../../CancelError';\nimport { IRateLimiter } from '../../universal/rate-limiter';\nimport { validateCheckpoint } from './utils';\nimport { createCrcReadStream } from '../../nodejs/CrcReadStream';\nimport { CRC } from '../../universal/crc';\nimport { combineCrc64 } from '../../universal/crc';\n\nexport interface DownloadFileCheckpointRecord {\n  bucket: string;\n  key: string;\n  version_id?: string;\n  part_size: number;\n\n  object_info: {\n    etag: string;\n    hash_crc64ecma: string;\n    object_size: number;\n    last_modified: string;\n  };\n\n  file_info: {\n    file_path: string;\n    temp_file_path: string;\n  };\n\n  parts_info: DownloadFileCheckpointRecordPartInfo[];\n}\n\nexport interface DownloadFileCheckpointRecordPartInfo {\n  part_number: number;\n  range_start: number;\n  range_end: number;\n  hash_crc64ecma: string;\n  is_completed: boolean;\n}\n\nexport interface DownloadFileInput extends HeadObjectInput {\n  filePath: string;\n  /**\n   * @private unstable tempFilePath\n   */\n  tempFilePath?: string;\n\n  /**\n   * default is 20 MB\n   *\n   * unit: B\n   */\n  partSize?: number;\n\n  /**\n   * the number of request to parallel upload part，default value is 1\n   */\n  taskNum?: number;\n\n  /**\n   * if checkpoint is a string and point to a exist file,\n   * the checkpoint record will recover from this file.\n   *\n   * if checkpoint is a string and point to a directory,\n   * the checkpoint will be auto generated,\n   * and its name is `{bucketName}_{objectName}.{uploadId}`.\n   */\n  checkpoint?: string | DownloadFileCheckpointRecord;\n\n  dataTransferStatusChange?: (status: DataTransferStatus) => void;\n\n  /**\n   * the simple progress feature\n   * percent is [0, 1]\n   */\n  progress?: (\n    percent: number,\n    checkpoint: DownloadFileCheckpointRecord\n  ) => void;\n\n  /**\n   * the feature of pause and continue downloading\n   */\n  downloadEventChange?: (event: DownloadEvent) => void;\n\n  /**\n   * cancel this upload progress\n   */\n  cancelToken?: CancelToken;\n  /**\n   * unit: bit/s\n   * server side traffic limit\n   **/\n  trafficLimit?: number;\n  /**\n   * only works for nodejs environment\n   */\n  rateLimiter?: IRateLimiter;\n\n  /**\n   * @private unstable\n   * custom rename file to support not overwrite file\n   */\n  customRenameFileAfterDownloadCompleted?: (\n    tempFilePath: string,\n    filePath: string\n  ) => void;\n}\nexport interface DownloadFileOutput extends HeadObjectOutput {}\n\nexport interface DownloadEvent {\n  type: DownloadEventType;\n  err?: Error;\n\n  bucket: string;\n  key: string;\n  versionId?: string;\n  filePath: string;\n  checkpointFile?: string;\n  downloadPartInfo?: DownloadPartInfo;\n}\n\nexport interface DownloadPartInfo {\n  partNumber: number;\n  rangeStart: number;\n  rangeEnd: number;\n}\n\nexport enum DownloadEventType {\n  CreateTempFileSucceed = 1,\n  CreateTempFileFailed,\n  DownloadPartSucceed,\n  DownloadPartFailed,\n  DownloadPartAborted,\n  RenameTempFileSucceed,\n  RenameTempFileFailed,\n}\n\ninterface CheckpointRichInfo {\n  filePath?: string | undefined;\n\n  filePathIsPlaceholder?: boolean;\n\n  record?: DownloadFileCheckpointRecord;\n}\n\ninterface Task {\n  partSize: number;\n  offset: number;\n  partNumber: number;\n}\n\nconst CHECKPOINT_FILE_NAME_PLACEHOLDER = '@@checkpoint-file-placeholder@@';\nconst ABORT_ERROR_STATUS_CODE = [403, 404, 405];\n\nexport async function downloadFile(\n  this: TOSBase,\n  input: DownloadFileInput\n): Promise<TosResponse<DownloadFileOutput>> {\n  if (process.env.TARGET_ENVIRONMENT === 'browser') {\n    throw new TosClientError(\n      '`downloadFile` is not supported in browser environment'\n    );\n  }\n  const { cancelToken, versionId } = input;\n  const isCancel = () => cancelToken && !!cancelToken.reason;\n  validateCheckpoint(input.checkpoint);\n\n  const headObjectRes = await headObject.call(this, {\n    bucket: input.bucket,\n    key: input.key,\n    versionId,\n  });\n  const { data: objectStats } = headObjectRes;\n  const etag = objectStats['etag'];\n  const symlinkTargetSize = objectStats['x-tos-symlink-target-size'] ?? 0;\n  const objectSize =\n    objectStats['x-tos-object-type'] === 'Symlink'\n      ? +symlinkTargetSize\n      : +objectStats['content-length'];\n\n  const checkpointRichInfo = await (async (): Promise<CheckpointRichInfo> => {\n    if (process.env.TARGET_ENVIRONMENT === 'node') {\n      if (typeof input.checkpoint === 'string') {\n        const { checkpoint } = input;\n        // file doesn't exist when stat is null\n        let checkpointStat: Stats | null = null;\n        try {\n          checkpointStat = await fsp.stat(checkpoint);\n        } catch (_err) {\n          // TODO: remove any\n          const err = _err as any;\n          if (err.code === 'ENOENT') {\n            // file doesn't exist\n          } else {\n            throw err;\n          }\n        }\n\n        const isDirectory = (() => {\n          if (checkpointStat) {\n            return checkpointStat.isDirectory();\n          }\n          return checkpoint.endsWith('/');\n        })();\n\n        // filePath will generated by uploadId, use placeholder temporarily\n        const filePath = isDirectory\n          ? path.resolve(checkpoint, CHECKPOINT_FILE_NAME_PLACEHOLDER)\n          : checkpoint;\n        const dirPath = path.dirname(filePath);\n        // ensure directory exist\n        await fsp.safeMkdirRecursive(dirPath);\n\n        if (isDirectory) {\n          return {\n            filePath,\n            filePathIsPlaceholder: true,\n          };\n        }\n\n        return {\n          filePath,\n          filePathIsPlaceholder: false,\n          // filePath is json file\n          // TODO: validate json schema\n          record: checkpointStat\n            ? await safeParseCheckpointFile(filePath)\n            : undefined,\n        };\n      }\n    }\n\n    if (typeof input.checkpoint === 'object') {\n      return {\n        record: input.checkpoint,\n      };\n    }\n\n    return {};\n  })();\n\n  // check if file info is matched\n  await (async () => {\n    if (checkpointRichInfo.record?.object_info) {\n      const { last_modified, object_size } =\n        checkpointRichInfo.record?.object_info;\n      if (\n        // TODO: `last-modified` aligns to number\n        objectStats['last-modified'] !== last_modified ||\n        objectSize !== object_size\n      ) {\n        console.warn(\n          `The file has been modified since ${new Date(\n            last_modified\n          )}, so the checkpoint file is invalid, and specified object will be downloaded again.`\n        );\n        delete checkpointRichInfo.record;\n      }\n    }\n  })();\n\n  const partSize =\n    input.partSize || checkpointRichInfo.record?.part_size || DEFAULT_PART_SIZE;\n\n  // check partSize is matched\n  if (\n    checkpointRichInfo.record &&\n    checkpointRichInfo.record.part_size !== partSize\n  ) {\n    console.warn(\n      'The partSize param does not equal the partSize in checkpoint file, ' +\n        'so the checkpoint file is invalid, and specified object will be downloaded again.'\n    );\n    delete checkpointRichInfo.record;\n  }\n\n  let bucket = input.bucket || this.opts.bucket || '';\n  const key = input.key;\n  const filePath = await (async () => {\n    let filePathStats: Stats | null = null;\n    try {\n      filePathStats = await fsp.stat(input.filePath);\n    } catch (_err) {\n      const err = _err as any;\n      if (err.code === 'ENOENT') {\n        // file doesn't exist\n      } else {\n        throw err;\n      }\n    }\n\n    const isDirectory = (() => {\n      if (filePathStats) {\n        return filePathStats.isDirectory();\n      }\n      return input.filePath.endsWith('/');\n    })();\n    const filePath = isDirectory\n      ? path.resolve(input.filePath, key)\n      : input.filePath;\n\n    const dirPath = path.dirname(filePath);\n    await fsp.safeMkdirRecursive(dirPath);\n\n    return filePath;\n  })();\n  const [tempFilePath, isExist] = await (async () => {\n    const tempFilePath = input.tempFilePath\n      ? input.tempFilePath\n      : filePath + '.temp';\n    let isExist = true;\n    try {\n      await fsp.stat(tempFilePath);\n    } catch (_err) {\n      const err = _err as any;\n      if (err.code === 'ENOENT') {\n        isExist = false;\n        // file doesn't exist\n      } else {\n        throw err;\n      }\n    }\n    return [tempFilePath, isExist];\n  })();\n  if (checkpointRichInfo.record) {\n    if (!isExist) {\n      console.warn(\n        \"The temp file doesn't not exist \" +\n          'so the checkpoint file is invalid, and specified object will be downloaded again.'\n      );\n      delete checkpointRichInfo.record;\n    }\n  }\n\n  let tasks: Task[] = [];\n  const allTasks: Task[] = getAllTasks(objectSize, partSize);\n  const initConsumedBytes = (checkpointRichInfo.record?.parts_info || [])\n    .filter((it) => it.is_completed)\n    .reduce((prev, it) => prev + (it.range_end - it.range_start + 1), 0);\n\n  // recorded tasks\n  const recordedTasks = checkpointRichInfo.record?.parts_info || [];\n  const recordedTaskMap: Map<number, DownloadFileCheckpointRecordPartInfo> =\n    new Map();\n  recordedTasks.forEach((it) => recordedTaskMap.set(it.part_number, it));\n\n  const nextEnsureCloseFd = async () => {\n    const getCheckpointContent = () => {\n      const checkpointContent: DownloadFileCheckpointRecord = {\n        bucket,\n        key,\n        version_id: versionId,\n        part_size: partSize,\n        parts_info: recordedTasks,\n        file_info: {\n          file_path: filePath,\n          temp_file_path: tempFilePath,\n        },\n        object_info: {\n          last_modified: objectStats['last-modified'],\n          etag: etag,\n          hash_crc64ecma: objectStats['x-tos-hash-crc64ecma'] || '',\n          object_size: objectSize,\n        },\n      };\n      return checkpointContent;\n    };\n    const triggerDownloadEvent = (\n      e: Omit<\n        DownloadEvent,\n        'bucket' | 'versionId' | 'key' | 'checkpointFile' | 'filePath'\n      >\n    ) => {\n      if (!input.downloadEventChange) {\n        return;\n      }\n\n      const event: DownloadEvent = {\n        bucket,\n        versionId,\n        key,\n        filePath,\n        ...e,\n      };\n      if (checkpointRichInfo.filePath) {\n        event.checkpointFile = checkpointRichInfo.filePath;\n      }\n\n      input.downloadEventChange(event);\n    };\n\n    let consumedBytesForProgress = initConsumedBytes;\n    enum TriggerProgressEventType {\n      start = 0,\n      downloadPartSucceed = 1,\n      renameTempFileSucceed = 2,\n    }\n    const triggerProgressEvent = (type: TriggerProgressEventType) => {\n      if (!input.progress) {\n        return;\n      }\n\n      const percent = (() => {\n        if (type === TriggerProgressEventType.start && objectSize === 0) {\n          return 0;\n        }\n        return !objectSize ? 1 : consumedBytesForProgress / objectSize;\n      })();\n\n      if (\n        consumedBytesForProgress === objectSize &&\n        type === TriggerProgressEventType.downloadPartSucceed\n      ) {\n        // 100% 仅在 complete 后处理，以便 100% 可以拉取到新对象\n      } else {\n        input.progress(percent, getCheckpointContent());\n      }\n    };\n    let consumedBytes = initConsumedBytes;\n    const { dataTransferStatusChange } = input;\n    const triggerDataTransfer = (\n      type: DataTransferType,\n      rwOnceBytes: number = 0\n    ) => {\n      if (!dataTransferStatusChange) {\n        return;\n      }\n      consumedBytes += rwOnceBytes;\n\n      dataTransferStatusChange?.({\n        type,\n        rwOnceBytes,\n        consumedBytes,\n        totalBytes: objectSize,\n      });\n    };\n    const writeCheckpointFile = makeSerialAsyncTask(async () => {\n      if (\n        process.env.TARGET_ENVIRONMENT === 'node' &&\n        checkpointRichInfo.filePath\n      ) {\n        const content = JSON.stringify(getCheckpointContent(), null, 2);\n        const dirPath = path.dirname(checkpointRichInfo.filePath); // ensure directory exist\n\n        await fsp.safeMkdirRecursive(dirPath);\n        await fsp.writeFile(checkpointRichInfo.filePath, content, 'utf-8');\n      }\n    });\n    const rmCheckpointFile = async () => {\n      if (\n        process.env.TARGET_ENVIRONMENT === 'node' &&\n        checkpointRichInfo.filePath\n      ) {\n        await fsp.rm(checkpointRichInfo.filePath).catch((err: any) => {\n          // eat err\n          console.warn(\n            'remove checkpoint file failure, you can remove it by hand.\\n',\n            `checkpoint file path: ${checkpointRichInfo.filePath}\\n`,\n            err.message\n          );\n        });\n      }\n    };\n\n    /**\n     *\n     * @param task one part task\n     * @param downloadPartRes upload part failed if `downloadPartRes` is Error\n     */\n    const updateAfterDownloadPart = async (\n      task: Task,\n      downloadPartRes:\n        | {\n            res: GetObjectV2Output & { rangeHashCrc64ecma: string };\n            err?: null;\n          }\n        | {\n            err: Error;\n          }\n    ) => {\n      let existRecordTask = recordedTaskMap.get(task.partNumber);\n      const rangeStart = task.offset;\n      const rangeEnd = Math.min(task.offset + partSize - 1, objectSize - 1);\n      if (!existRecordTask) {\n        existRecordTask = {\n          part_number: task.partNumber,\n          range_start: rangeStart,\n          range_end: rangeEnd,\n          hash_crc64ecma: '',\n          is_completed: false,\n        };\n        recordedTasks.push(existRecordTask);\n        recordedTaskMap.set(existRecordTask.part_number, existRecordTask);\n      }\n\n      if (!downloadPartRes.err) {\n        existRecordTask.is_completed = true;\n        existRecordTask.hash_crc64ecma = downloadPartRes.res.rangeHashCrc64ecma;\n      }\n\n      await writeCheckpointFile();\n      const downloadPartInfo: DownloadPartInfo = {\n        partNumber: existRecordTask.part_number,\n        rangeStart,\n        rangeEnd,\n      };\n\n      if (downloadPartRes.err) {\n        const err = downloadPartRes.err;\n        let type: DownloadEventType = DownloadEventType.DownloadPartFailed;\n\n        if (err instanceof TosServerError) {\n          if (ABORT_ERROR_STATUS_CODE.includes(err.statusCode)) {\n            type = DownloadEventType.DownloadPartAborted;\n          }\n        }\n\n        triggerDownloadEvent({\n          type,\n          err,\n          downloadPartInfo: downloadPartInfo,\n        });\n        return;\n      }\n\n      consumedBytesForProgress +=\n        downloadPartInfo.rangeEnd - downloadPartInfo.rangeStart + 1;\n\n      triggerDownloadEvent({\n        type: DownloadEventType.DownloadPartSucceed,\n        downloadPartInfo: downloadPartInfo,\n      });\n      triggerProgressEvent(TriggerProgressEventType.downloadPartSucceed);\n    };\n\n    if (checkpointRichInfo.record) {\n      bucket = checkpointRichInfo.record.bucket;\n\n      // checkpoint info exists, so need to calculate remain tasks\n      const uploadedPartSet: Set<number> = new Set(\n        (checkpointRichInfo.record.parts_info || [])\n          .filter((it) => it.is_completed)\n          .map((it) => it.part_number)\n      );\n      tasks = allTasks.filter((it) => !uploadedPartSet.has(it.partNumber));\n    } else {\n      try {\n        // create temp file\n        await fsp.writeFile(tempFilePath, '', {\n          flag: 'w+',\n        });\n      } catch (_err) {\n        const err = _err as any;\n        triggerDownloadEvent({\n          type: DownloadEventType.CreateTempFileFailed,\n          err,\n        });\n        throw err;\n      }\n\n      if (checkpointRichInfo.filePathIsPlaceholder) {\n        checkpointRichInfo.filePath = checkpointRichInfo.filePath?.replace(\n          `${CHECKPOINT_FILE_NAME_PLACEHOLDER}`,\n          getDefaultCheckpointFilePath(bucket, key, versionId)\n        );\n      }\n\n      triggerDownloadEvent({\n        type: DownloadEventType.CreateTempFileSucceed,\n      });\n      triggerDataTransfer(DataTransferType.Started);\n      tasks = allTasks;\n    }\n\n    const handleTasks = async () => {\n      let firstErr: Error | null = null;\n      let index = 0;\n\n      // TODO: how to test parallel does work, measure time is not right\n      await Promise.all(\n        Array.from({ length: input.taskNum || 1 }).map(async () => {\n          while (true) {\n            const currentIndex = index++;\n            if (currentIndex >= tasks.length) {\n              return;\n            }\n\n            const curTask = tasks[currentIndex];\n            let consumedBytesThisTask = 0;\n            try {\n              const res = await getObjectV2.call(this, {\n                bucket,\n                key,\n                versionId,\n                headers: {\n                  'if-match': etag,\n                  range: `bytes=${curTask.offset}-${Math.min(\n                    curTask.offset + curTask.partSize - 1,\n                    objectSize - 1\n                  )}`,\n                },\n                trafficLimit: input.trafficLimit,\n                rateLimiter: input.rateLimiter,\n                dataTransferStatusChange(status) {\n                  if (status.type !== DataTransferType.Rw) {\n                    return;\n                  }\n                  if (isCancel()) {\n                    return;\n                  }\n                  consumedBytesThisTask += status.rwOnceBytes;\n                  triggerDataTransfer(DataTransferType.Rw, status.rwOnceBytes);\n                },\n              });\n\n              // need to handle stream's error event before throw a error\n              // if (isCancel()) {\n              //   throw new CancelError('cancel downloadFile');\n              // }\n\n              let dataStream = res.data.content;\n              const crcInst = new CRC();\n              if (\n                process.env.TARGET_ENVIRONMENT === 'node' &&\n                this.opts.enableCRC\n              ) {\n                dataStream = createCrcReadStream(dataStream, crcInst);\n              }\n              await new Promise((resolve, reject) => {\n                const writeStream = fsp.createWriteStream(tempFilePath, {\n                  start: curTask.offset,\n                  flags: 'r+',\n                });\n\n                let isErr = false;\n                let err: any = null;\n                writeStream.on('close', () => {\n                  if (isErr) {\n                    reject(err);\n                  } else {\n                    resolve(undefined);\n                  }\n                });\n\n                writeStream.on('error', (_err) => {\n                  isErr = true;\n                  err = _err;\n                });\n\n                dataStream.pipe(writeStream);\n                dataStream.on('error', (err) => writeStream.destroy(err));\n                function handleOnceCancel() {\n                  if (isCancel()) {\n                    reject(new CancelError('cancel downloadFile'));\n                    // fix windows\n                    writeStream.end();\n                    dataStream.unpipe(writeStream);\n                    dataStream.off('data', handleOnceCancel);\n                  }\n                }\n                dataStream.on('data', handleOnceCancel);\n              });\n\n              if (isCancel()) {\n                throw new CancelError('cancel downloadFile');\n              }\n\n              await updateAfterDownloadPart(curTask, {\n                res: { ...res.data, rangeHashCrc64ecma: crcInst.getCrc64() },\n              });\n            } catch (_err) {\n              const err = _err as any;\n              consumedBytes -= consumedBytesThisTask;\n              consumedBytesThisTask = 0;\n\n              if (isCancelError(err)) {\n                throw err;\n              }\n\n              if (isCancel()) {\n                throw new CancelError('cancel downloadFile');\n              }\n\n              if (!firstErr) {\n                firstErr = err;\n              }\n              await updateAfterDownloadPart(curTask, { err });\n            }\n          }\n        })\n      );\n\n      if (firstErr) {\n        throw firstErr;\n      }\n\n      const serverCRC64 = headObjectRes.data['x-tos-hash-crc64ecma'];\n      if (this.opts.enableCRC && serverCRC64) {\n        const actualCrc64 = combineCRCInParts(getCheckpointContent());\n        if (actualCrc64 !== serverCRC64) {\n          throw new TosClientError(\n            `validate file crc64 failed. Expect crc64 ${serverCRC64}, actual crc64 ${actualCrc64}. Please try again.`\n          );\n        }\n      }\n    };\n\n    const handleEmptyObj = async () => {};\n\n    triggerProgressEvent(TriggerProgressEventType.start);\n    objectSize === 0 ? await handleEmptyObj() : await handleTasks();\n\n    try {\n      if (typeof input.customRenameFileAfterDownloadCompleted === 'function') {\n        await input.customRenameFileAfterDownloadCompleted(\n          tempFilePath,\n          filePath\n        );\n      } else {\n        await fsp.rename(tempFilePath, filePath);\n      }\n    } catch (_err) {\n      const err = _err as any;\n      triggerDownloadEvent({\n        type: DownloadEventType.RenameTempFileFailed,\n        err,\n      });\n      triggerDataTransfer(DataTransferType.Failed);\n      throw err;\n    }\n\n    triggerDownloadEvent({\n      type: DownloadEventType.RenameTempFileSucceed,\n    });\n    triggerProgressEvent(TriggerProgressEventType.renameTempFileSucceed);\n    triggerDataTransfer(DataTransferType.Succeed);\n    await rmCheckpointFile();\n\n    return headObjectRes;\n  };\n\n  try {\n    return await nextEnsureCloseFd();\n  } finally {\n    // there is no global fd, don't need to close fd\n  }\n}\n\nexport default downloadFile;\n\n/**\n * 即使 totalSize 是 0，也需要一个 Part，否则 Server 端会报错 read request body failed\n */\nfunction getAllTasks(totalSize: number, partSize: number) {\n  const tasks: Task[] = [];\n  for (let i = 0; ; ++i) {\n    const offset = i * partSize;\n    const currPartSize = Math.min(partSize, totalSize - offset);\n\n    tasks.push({\n      offset,\n      partSize: currPartSize,\n      partNumber: i + 1,\n    });\n\n    if ((i + 1) * partSize >= totalSize) {\n      break;\n    }\n  }\n\n  return tasks;\n}\n\nfunction getDefaultCheckpointFilePath(\n  bucket: string,\n  key: string,\n  versionId?: string\n) {\n  const originPath = `${bucket}_${key}.${versionId}.json`;\n  const normalizePath = originPath.replace(/[\\\\/]/g, '');\n  return normalizePath;\n}\n\nfunction combineCRCInParts(cp: DownloadFileCheckpointRecord) {\n  let res = '0';\n  const sortedPartsInfo =\n    cp.parts_info?.sort?.((a, b) => a.part_number - b.part_number) ?? [];\n  for (const part of sortedPartsInfo) {\n    res = combineCrc64(\n      res,\n      part.hash_crc64ecma,\n      part.range_end - part.range_start + 1\n    );\n  }\n  return res;\n}\n", "// @ts-nocheck\nimport { hashSha256, hmacSha256, stringify, parse } from './universal/crypto';\nimport { getSortedQueryString } from './utils';\n\nexport interface ISign {\n  signature(\n    opt: ISigOptions,\n    expiredAt: number,\n    credentials?: ISigCredentials\n  ): string;\n  signatureHeader(\n    opt: ISigOptions,\n    expiredAt?: number,\n    credentials?: ISigCredentials\n  ): Map<string, string>;\n  gnrCopySig(\n    opt: ISigOptions,\n    credentials: ISigCredentials\n  ): { key: string; value: string };\n  getSignatureQuery(\n    opt: ISigOptions,\n    expiredAt: number\n  ): { [key: string]: any };\n  getSignature(\n    reqOpts: ISigOptions,\n    expiredAt: number\n  ): { key: string; value: string };\n}\n\nexport interface ISigCredentials {\n  GetSecretKey(): string;\n  GetAccessKey(): string;\n}\n\nexport interface ISigPolicyQuery {\n  policy: {\n    conditions: (string[] | { bucket: string } | { key: string })[];\n  };\n}\n\nexport interface ISigOptions {\n  sigName?: string;\n  endpoints?: string;\n  bucket?: string;\n  headers?: { [key: string]: string | undefined };\n  region?: string;\n  serviceName?: string;\n  algorithm?: string;\n  path: string;\n  method: string;\n  query?: string;\n  datetime?: string;\n  host?: string;\n  port?: number;\n}\n\nexport interface ISigQueryOptions extends Omit<ISigOptions, 'query'> {\n  query?: Record<string, any>;\n}\n\nexport const SIG_QUERY = {\n  algorithm: 'tos-algorithm',\n  expiration: 'tos-expiration',\n  signame: 'tos-signame',\n  signature: 'tos-signature',\n\n  v4_algorithm: 'X-Tos-Algorithm',\n  v4_credential: 'X-Tos-Credential',\n  v4_date: 'X-Tos-Date',\n  v4_expires: 'X-Tos-Expires',\n  v4_signedHeaders: 'X-Tos-SignedHeaders',\n  v4_security_token: 'X-Tos-Security-Token',\n  v4_signature: 'X-Tos-Signature',\n  v4_content_sha: 'X-Tos-Content-Sha256',\n  v4_policy: 'X-Tos-Policy',\n\n\n};\n\nexport function isDefaultPort(port?: number) {\n  if (port && port !== 80 && port !== 443) {\n    return false;\n  }\n  return true;\n}\n\n/**\n * @api private\n */\nconst v4Identifier = 'request';\n\ninterface ISignV4Opt {\n  algorithm?: string;\n  region?: string;\n  serviceName?: string;\n  securityToken?: string;\n  bucket: string;\n}\n/**\n * @api private\n */\nexport class SignersV4 implements ISign {\n  private options: ISignV4Opt;\n  private credentials: ISigCredentials;\n  constructor(opt: ISignV4Opt, credentials: ISigCredentials) {\n    this.options = opt;\n    this.credentials = credentials;\n  }\n\n  /*\n   * normal v4 signature\n   * */\n  public signature = (\n    opt: ISigOptions,\n    expiredAt: number,\n    credentials?: ISigCredentials\n  ) => {\n    if (!credentials) {\n      credentials = this.credentials;\n    }\n    const parts: string[] = [];\n    const datatime = opt.datetime as string;\n    const credString = this.credentialString(datatime);\n    parts.push(\n      this.options.algorithm +\n        ' Credential=' +\n        credentials.GetAccessKey() +\n        '/' +\n        credString\n    );\n\n    // console.log(this.algorithm + ' Credential=' +\n    //   credentials.accessKeyId + '/' + credString)\n\n    parts.push('SignedHeaders=' + this.signedHeaders(opt));\n    parts.push('Signature=' + this.authorization(opt, credentials, 0));\n    return parts.join(', ');\n  };\n\n  public signatureHeader = (\n    opt: ISigOptions,\n    expiredAt?: number,\n    credentials?: ISigCredentials\n  ): Map<string, string> => {\n    // const datetime = (new Date(new Date().toUTCString())).Format(\"yyyyMMddTHHmmssZ\")\n    opt.datetime = this.getDateTime();\n    const header = new Map<string, string>();\n    /* istanbul ignore if */\n    if (!opt.headers) {\n      const h: { [key: string]: string } = {};\n      opt.headers = h;\n    }\n\n    opt.headers.host = `${opt.host}`;\n    /* istanbul ignore if */\n    if (!isDefaultPort(opt.port)) {\n      opt.headers.host += ':' + opt.port;\n    }\n    /* istanbul ignore if */\n    if (opt.endpoints) {\n      opt.headers.host = `${this.options.bucket}.${opt.endpoints}`;\n    }\n\n    header.set('host', opt.headers.host); // opt.endpoints as string)\n    header.set('x-tos-date', opt.datetime); // opt.datetime)\n    /* istanbul ignore if\n      if (opt.endpoints) {\n          let bucket = this.options.bucket;\n          if (opt.bucket) {\n              bucket = opt.bucket;\n          }\n          if (!opt.path || opt.path === '/' || opt.path === `/${bucket}`) {\n              opt.path = '/' + bucket;\n          } else {\n              opt.path = '/' + bucket + opt.path;\n          }\n      }\n      */\n    header.set('x-tos-content-sha256', this.hexEncodedBodyHash());\n    if (this.options.securityToken) {\n      header.set('x-tos-security-token', this.options.securityToken);\n    }\n    // x-tos- must to be signatured\n    header.forEach((value, key) => {\n      if (key.startsWith('x-tos')) {\n        opt.headers[key] = value;\n      }\n    });\n    opt.path = this.getEncodePath(opt.path);\n    const sign = this.signature(opt, 0, credentials);\n    header.set('authorization', sign);\n\n    return header;\n  };\n\n  public gnrCopySig = (\n    opt: ISigOptions,\n    credentials: ISigCredentials\n  ): { key: string; value: string } => {\n    return { key: '', value: '' };\n  };\n\n  public getSignature = (\n    opt: ISigOptions,\n    expiredAt: number\n  ): { key: ''; value: '' } => {\n    return { key: '', value: '' };\n  };\n\n  public getSignatureQuery = (\n    opt: ISigQueryOptions,\n    expiredAt: number\n  ): { [key: string]: any } => {\n    opt.datetime = this.getDateTime();\n    if (!opt.headers) {\n      const h: { [key: string]: string } = {};\n      opt.headers = h;\n    }\n\n    opt.headers.host = `${opt.host}`;\n    if (!isDefaultPort(opt.port)) {\n      opt.headers.host += ':' + opt.port;\n    }\n\n    opt.path = this.getEncodePath(opt.path);\n    if (opt.endpoints) {\n      opt.headers.host = `${this.options.bucket}.${opt.endpoints}`;\n      // opt.path = `${opt.path}`;\n    }\n\n    opt.headers[SIG_QUERY.v4_date] = opt.datetime;\n    const credString = this.credentialString(opt.datetime as string);\n    const res = {\n      ...(opt.query || {}),\n      [SIG_QUERY.v4_algorithm]: this.options.algorithm,\n      [SIG_QUERY.v4_content_sha]: this.hexEncodedBodyHash(),\n      [SIG_QUERY.v4_credential]:\n        this.credentials.GetAccessKey() + '/' + credString,\n      [SIG_QUERY.v4_date]: opt.datetime,\n      [SIG_QUERY.v4_expires]: '' + expiredAt,\n      [SIG_QUERY.v4_signedHeaders]: this.signedHeaders(opt),\n    };\n    if (this.options.securityToken) {\n      res[SIG_QUERY.v4_security_token] = this.options.securityToken;\n    }\n    opt.query = getSortedQueryString(res);\n\n    res[SIG_QUERY.v4_signature] = this.authorization(\n      opt,\n      this.credentials,\n      expiredAt\n    );\n    return res;\n  };\n\n  public getSignaturePolicyQuery = (\n    opt: ISigPolicyQuery,\n    expiredAt: number\n  ): { [key: string]: any } => {\n    opt.datetime = this.getDateTime();\n\n    const credString = this.credentialString(opt.datetime as string);\n    const res = {\n      [SIG_QUERY.v4_algorithm]: this.options.algorithm,\n      [SIG_QUERY.v4_credential]:\n        this.credentials.GetAccessKey() + '/' + credString,\n      [SIG_QUERY.v4_date]: opt.datetime,\n      [SIG_QUERY.v4_expires]: '' + expiredAt,\n      [SIG_QUERY.v4_policy]: stringify(\n        parse(JSON.stringify(opt.policy), 'utf-8'),\n        'base64'\n      ),\n    };\n    if (this.options.securityToken) {\n      res[SIG_QUERY.v4_security_token] = this.options.securityToken;\n    }\n    opt.query = getSortedQueryString(res);\n\n    res[SIG_QUERY.v4_signature] = this.authorization(\n      opt,\n      this.credentials,\n      expiredAt\n    );\n    return res;\n  };\n\n  private hexEncodedBodyHash = () => {\n    return 'UNSIGNED-PAYLOAD';\n    // return this.hexEncodedHash('');\n  };\n\n  private authorization = (\n    opt: ISigOptions,\n    credentials: ISigCredentials,\n    expiredAt: number\n  ) => {\n    /* istanbul ignore if */\n    if (!opt.datetime) {\n      return '';\n    }\n\n    const signingKey = this.getSigningKey(\n      credentials,\n      opt.datetime.substr(0, 8)\n    );\n    // console.log(\n    // 'signingKey:',\n    //  signingKey,\n    //  'sign:',\n    //  this.stringToSign(opt.datetime, opt)\n    //  );\n    return hmacSha256(signingKey, this.stringToSign(opt.datetime, opt), 'hex');\n  };\n\n  private getDateTime = () => {\n    const date = new Date(new Date().toUTCString());\n    const datetime =\n      date\n        .toISOString()\n        .replace(/\\..+/, '')\n        .replace(/-/g, '')\n        .replace(/:/g, '') + 'Z';\n    return datetime;\n  };\n  private credentialString = (datetime: string) => {\n    return this.createScope(\n      datetime.substr(0, 8),\n      this.options.region,\n      this.options.serviceName\n    );\n  };\n\n  private createScope = (date, region, serviceName) => {\n    return [date.substr(0, 8), region, serviceName, v4Identifier].join('/');\n  };\n\n  private getSigningKey = (credentials: ISigCredentials, date) => {\n    const kDate = hmacSha256(credentials.GetSecretKey(), date);\n    const kRegion = hmacSha256(kDate, this.options.region as string);\n    const kService = hmacSha256(kRegion, this.options.serviceName as string);\n    const signingKey = hmacSha256(kService, v4Identifier);\n\n    return signingKey;\n  };\n\n  private stringToSign = (datetime: string, opt: ISigOptions) => {\n    /* istanbul ignore if */\n    if (!this.options.algorithm) {\n      return '';\n    }\n\n    const parts: string[] = [];\n    parts.push(this.options.algorithm);\n    parts.push(datetime);\n    parts.push(this.credentialString(datetime));\n    const canonicalString =\n      'policy' in opt\n        ? this.canonicalStringPolicy(opt)\n        : this.canonicalString(opt);\n    // console.log('canonicalString',this.canonicalString(opt),' code:',this.hexEncodedHash(this.canonicalString(opt)));\n    parts.push(this.hexEncodedHash(canonicalString));\n    return parts.join('\\n');\n  };\n\n  private hexEncodedHash = string => {\n    return hashSha256(string, 'hex');\n  };\n\n  private canonicalString = (opt: ISigOptions) => {\n    const parts: any[] = [];\n    parts.push(opt.method);\n    parts.push(opt.path);\n    parts.push(this.getEncodePath(opt.query as string, false));\n    parts.push(this.canonicalHeaders(opt) + '\\n');\n    parts.push(this.signedHeaders(opt));\n    parts.push(this.hexEncodedBodyHash());\n    return parts.join('\\n');\n  };\n\n  private canonicalStringPolicy = (opt: ISigOptions) => {\n    const parts: any[] = [];\n    parts.push(this.getEncodePath(opt.query as string, false));\n    parts.push(this.hexEncodedBodyHash());\n    return parts.join('\\n');\n  };\n\n  private canonicalHeaders = (opt: ISigOptions) => {\n    const parts: string[] = [];\n    const needSignHeaders = getNeedSignedHeaders(opt.headers);\n\n    for (let key of needSignHeaders) {\n      const value = opt.headers[key];\n      key = key.toLowerCase();\n      parts.push(key + ':' + this.canonicalHeaderValues(value.toString()));\n    }\n\n    return parts.join('\\n');\n  };\n\n  private canonicalHeaderValues = (values: string) => {\n    return values.replace(/\\s+/g, ' ').replace(/^\\s+|\\s+$/g, '');\n  };\n\n  private signedHeaders = (opt: ISigOptions) => {\n    const keys: string[] = [];\n    const needSignHeaders = getNeedSignedHeaders(opt.headers);\n\n    for (let key of needSignHeaders) {\n      key = key.toLowerCase();\n      keys.push(key);\n    }\n\n    return keys.sort().join(';');\n  };\n\n  /**\n   * ! * ' () aren't transformed by encodeUrl, so they need be handled\n   */\n  private getEncodePath(path: string, encodeAll: boolean = true): string {\n    if (!path) {\n      return '';\n    }\n\n    let tmpPath = path;\n    if (encodeAll) {\n      tmpPath = path.replace(/%2F/g, '/');\n    }\n    tmpPath = tmpPath.replace(/\\(/g, '%28');\n    tmpPath = tmpPath.replace(/\\)/g, '%29');\n    tmpPath = tmpPath.replace(/!/g, '%21');\n    tmpPath = tmpPath.replace(/\\*/g, '%2A');\n    tmpPath = tmpPath.replace(/\\'/g, '%27');\n    return tmpPath;\n  }\n}\n\nexport class ISigV4Credentials implements ISigCredentials {\n  public securityToken: string;\n  public secretAccessKey: string;\n  public accessKeyId: string;\n\n  constructor(\n    securityToken?: string,\n    secretAccessKey?: string,\n    accessKeyId?: string\n  ) {\n    this.accessKeyId = accessKeyId as string;\n    this.secretAccessKey = secretAccessKey as string;\n    this.securityToken = securityToken as string;\n  }\n\n  public GetAccessKey(): string {\n    return this.accessKeyId;\n  }\n\n  public GetSecretKey(): string {\n    return this.secretAccessKey;\n  }\n}\n\nfunction getNeedSignedHeaders(headers: Record<string, unknown> | undefined) {\n  const needSignHeaders: string[] = [];\n  Object.keys(headers || {}).forEach((key: string) => {\n    if (key === 'host' || key.startsWith('x-tos-')) {\n      if (headers[key] != null) {\n        needSignHeaders.push(key);\n      }\n    }\n  });\n  return needSignHeaders.sort();\n}\n", "/**\n * forked from  https://github.com/bigmeow/axios-miniprogram-adapter\n * author bigMeow\n */\n//@ts-ignore\nimport createError from 'axios/lib/core/createError';\nimport { AxiosResponse, AxiosRequestConfig } from 'axios';\ninterface MpResponse extends WechatMiniprogram.RequestSuccessCallbackResult {\n  /** 支付宝、钉钉独有 */\n  headers: WechatMiniprogram.IAnyObject;\n  status: number;\n}\n\ninterface MpRequestConfig extends WechatMiniprogram.RequestOption {\n  /** 仅支付宝、钉钉小程序独有 */\n  headers?: WechatMiniprogram.IAnyObject;\n}\n\nconst enum EnumPlatForm {\n  weixin = 'wexin',\n  zhifubao = 'alipay',\n  baidu = 'baidu',\n  dingding = 'dd',\n}\n\nlet platFormName: EnumPlatForm = EnumPlatForm.weixin;\n\n/**\n * 获取各个平台的请求函数\n */\nexport function getRequest(): (\n  option: WechatMiniprogram.RequestOption\n) => WechatMiniprogram.RequestTask {\n  switch (true) {\n    case typeof wx === 'object':\n      platFormName = EnumPlatForm.weixin;\n      return wx.request.bind(wx);\n    case typeof swan === 'object':\n      platFormName = EnumPlatForm.baidu;\n      return swan.request.bind(swan);\n    case typeof dd === 'object':\n      platFormName = EnumPlatForm.dingding;\n      // https://open.dingtalk.com/document/orgapp-client/send-network-requests\n      return dd.httpRequest.bind(dd);\n    case typeof my === 'object':\n      /**\n       * remark:\n       * 支付宝客户端已不再维护 my.httpRequest，建议使用 my.request。另外，钉钉客户端尚不支持 my.request。若在钉钉客户端开发小程序，则需要使用 my.httpRequest。\n       * my.httpRequest的请求头默认值为{'content-type': 'application/x-www-form-urlencoded'}。\n       * my.request的请求头默认值为{'content-type': 'application/json'}。\n       * 还有个 dd.httpRequest\n       */\n      platFormName = EnumPlatForm.zhifubao;\n      return (my.request || my.httpRequest).bind(my);\n    default:\n      return wx.request.bind(wx);\n  }\n}\n\n/**\n * 处理各平台返回的响应数据，抹平差异\n * @param mpResponse\n * @param config axios处理过的请求配置对象\n * @param request 小程序的调用发起请求时，传递给小程序api的实际配置\n */\nexport function transformResponse(\n  mpResponse: MpResponse,\n  config: AxiosRequestConfig,\n  mpRequestOption: WechatMiniprogram.RequestOption\n): AxiosResponse {\n  const headers = mpResponse.header || mpResponse.headers;\n  const status = mpResponse.statusCode || mpResponse.status;\n\n  let statusText = '';\n  if (status === 200) {\n    statusText = 'OK';\n  } else if (status === 400) {\n    statusText = 'Bad Request';\n  }\n\n  const response: AxiosResponse = {\n    data: mpResponse.data,\n    status,\n    statusText,\n    headers,\n    config,\n    request: mpRequestOption,\n  };\n  return response;\n}\n\n/**\n * 处理各平台返回的错误信息，抹平差异\n * @param error 小程序api返回的错误对象\n * @param reject 上层的promise reject 函数\n * @param config\n */\nexport function transformError(error: any, reject: any, config: any) {\n  switch (platFormName) {\n    case EnumPlatForm.weixin:\n      if (error.errMsg.indexOf('request:fail abort') !== -1) {\n        // Handle request cancellation (as opposed to a manual cancellation)\n        reject(createError('Request aborted', config, 'ECONNABORTED', ''));\n      } else if (error.errMsg.indexOf('timeout') !== -1) {\n        // timeout\n        reject(\n          createError(\n            'timeout of ' + config.timeout + 'ms exceeded',\n            config,\n            'ECONNABORTED',\n            ''\n          )\n        );\n      } else {\n        // NetWordError\n        reject(createError('Network Error', config, null, ''));\n      }\n      break;\n    case EnumPlatForm.dingding:\n    case EnumPlatForm.zhifubao:\n      // https://docs.alipay.com/mini/api/network\n      if ([14, 19].includes(error.error)) {\n        reject(\n          createError('Request aborted', config, 'ECONNABORTED', '', error)\n        );\n      } else if ([13].includes(error.error)) {\n        // timeout\n        reject(\n          createError(\n            'timeout of ' + config.timeout + 'ms exceeded',\n            config,\n            'ECONNABORTED',\n            '',\n            error\n          )\n        );\n      } else {\n        // NetWordError\n        reject(createError('Network Error', config, null, '', error));\n      }\n      break;\n    case EnumPlatForm.baidu:\n      // TODO error.errCode\n      reject(createError('Network Error', config, null, ''));\n      break;\n  }\n}\n\n/**\n * 将axios的请求配置，转换成各个平台都支持的请求config\n * @param config\n */\nexport function transformConfig(config: MpRequestConfig): any {\n  if ([EnumPlatForm.zhifubao, EnumPlatForm.dingding].includes(platFormName)) {\n    config.headers = config.header;\n    delete config.header;\n    if (\n      EnumPlatForm.dingding === platFormName &&\n      config.method !== 'GET' &&\n      config.headers?.['Content-Type'] === 'application/json' &&\n      Object.prototype.toString.call(config.data) === '[object Object]'\n    ) {\n      // Content-Type为application/json时，data参数只支持json字符串，需要手动调用JSON.stringify进行序列化\n      config.data = JSON.stringify(config.data);\n    }\n  }\n  return config;\n}\n", "/**\n * forked from  https://github.com/bigmeow/axios-miniprogram-adapter\n * author bigMeow\n */\nimport { AxiosRequestConfig, AxiosPromise } from 'axios';\n//@ts-ignore\nimport utils from 'axios/lib/utils';\n//@ts-ignore\nimport settle from 'axios/lib/core/settle';\n//@ts-ignore\nimport buildURL from 'axios/lib/helpers/buildURL';\n//@ts-ignore\nimport buildFullPath from 'axios/lib/core/buildFullPath';\nimport encode from './utils/encoder';\nimport {\n  getRequest,\n  transformError,\n  transformResponse,\n  transformConfig,\n} from './utils/platForm';\n\nconst isJSONstr = (str: string | any[]) => {\n  try {\n    return (\n      typeof str === 'string' &&\n      str.length &&\n      (str = JSON.parse(str)) &&\n      Object.prototype.toString.call(str) === '[object Object]'\n    );\n  } catch (error) {\n    return false;\n  }\n};\nexport default function mpAdapter(\n  config: AxiosRequestConfig,\n  {\n    transformRequestOption = (requestOption) => requestOption,\n  }: {\n    transformRequestOption?: (requestOption: any) => any;\n  } = {}\n): AxiosPromise {\n  const request = getRequest();\n  return new Promise((resolve, reject) => {\n    let requestTask: void | WechatMiniprogram.RequestTask;\n    let requestData = config.data;\n    let requestHeaders = config.headers;\n    // baidu miniprogram only support upperCase\n    let requestMethod = (config.method && config.method.toUpperCase()) || 'GET';\n    // miniprogram network request config\n    const mpRequestOption: WechatMiniprogram.RequestOption = {\n      method: requestMethod as\n        | 'OPTIONS'\n        | 'GET'\n        | 'HEAD'\n        | 'POST'\n        | 'PUT'\n        | 'DELETE'\n        | 'TRACE'\n        | 'CONNECT',\n      url: buildURL(\n        buildFullPath(config.baseURL, config.url),\n        config.params,\n        config.paramsSerializer\n      ),\n      timeout: config.timeout,\n      // Listen for success\n      success: (mpResponse: any) => {\n        const response = transformResponse(mpResponse, config, mpRequestOption);\n        settle(resolve, reject, response);\n      },\n      // Handle request Exception\n      fail: (error) => {\n        transformError(error, reject, config);\n      },\n      complete() {\n        requestTask = undefined;\n      },\n    };\n\n    // HTTP basic authentication\n    if (config.auth) {\n      const [username, password] = [\n        config.auth.username || '',\n        config.auth.password || '',\n      ];\n      requestHeaders.Authorization =\n        'Basic ' + encode(username + ':' + password);\n    }\n\n    // Add headers to the request\n    utils.forEach(\n      requestHeaders,\n      function setRequestHeader(_val: any, key: string) {\n        const _header = key.toLowerCase();\n        if (\n          (typeof requestData === 'undefined' && _header === 'content-type') ||\n          _header === 'referer'\n        ) {\n          // Remove Content-Type if data is undefined\n          // And the miniprogram document said that '设置请求的 header，header 中不能设置 Referer'\n          delete requestHeaders[key];\n        }\n      }\n    );\n    mpRequestOption.header = requestHeaders;\n\n    // Add responseType to request if needed\n    if (config.responseType) {\n      mpRequestOption.responseType = config.responseType as\n        | 'text'\n        | 'arraybuffer';\n    }\n\n    if (config.cancelToken) {\n      // Handle cancellation\n      config.cancelToken.promise.then(function onCanceled(cancel) {\n        if (!requestTask) {\n          return;\n        }\n        requestTask.abort();\n        reject(cancel);\n        // Clean up request\n        requestTask = undefined;\n      });\n    }\n    // Converting JSON strings to objects is handed over to the MiniPrograme\n    if (isJSONstr(requestData)) {\n      requestData = JSON.parse(requestData);\n    }\n    if (requestData !== undefined) {\n      mpRequestOption.data = requestData;\n    }\n    requestTask = request(\n      transformRequestOption(transformConfig(mpRequestOption))\n    );\n  });\n}\n", "/**\n * forked from  https://github.com/bigmeow/axios-miniprogram-adapter\n * author bigMeow\n */\n\nconst chars =\n  'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=';\n// encoder\nfunction encoder(input: string) {\n  const str = String(input);\n  // initialize result and counter\n  let block;\n  let charCode;\n  let idx = 0;\n  let map = chars;\n  let output = '';\n  for (\n    ;\n    // if the next str index does not exist:\n    //   change the mapping table to \"=\"\n    //   check if d has no fractional digits\n    str.charAt(idx | 0) || ((map = '='), idx % 1);\n    // \"8 - idx % 1 * 8\" generates the sequence 2, 4, 6, 8\n    output += map.charAt(63 & (block >> (8 - (idx % 1) * 8)))\n  ) {\n    charCode = str.charCodeAt((idx += 3 / 4));\n    if (charCode > 0xff) {\n      throw new Error(\n        \"'btoa' failed: The string to be encoded contains characters outside of the Latin1 range.\"\n      );\n    }\n    //@ts-expect-error\n    block = (block << 8) | charCode;\n  }\n  return output;\n}\n\nexport default encoder;\n", "import { hashMd5 } from '../universal/crypto';\nimport axios, {\n  AxiosAdapter,\n  AxiosInstance,\n  AxiosRequestConfig,\n  AxiosResponse,\n  Method,\n} from 'axios';\nimport { ISigV4Credentials, SignersV4 } from '../signatureV4';\nimport { Headers, StringKeys } from '../interface';\nimport TosServerError, { TosServerErrorData } from '../TosServerError';\nimport {\n  encodeHeadersValue,\n  getEndpoint,\n  getNormalDataFromError,\n  getSortedQueryString,\n  normalizeProxy,\n} from '../utils';\nimport version from '../version';\nimport { TosAgent } from '../nodejs/TosAgent';\nimport TosClientError from '../TosClientError';\nimport {\n  DEFAULT_CONTENT_TYPE,\n  getObjectInputKey,\n  lookupMimeType,\n  validateObjectName,\n} from './object/utils';\nimport { makeAxiosInst } from '../axios';\nimport type { CRCCls } from '../universal/crc';\nimport * as log from '../log';\nimport mpAdapter from '../axios-miniprogram-adapter';\nimport uniappAdapter from 'axios-adapter-uniapp';\nimport os from 'os';\nimport { retrySignatureNamespace } from '../axios';\n\nexport interface TOSConstructorOptions {\n  accessKeyId: string;\n  accessKeySecret: string;\n  stsToken?: string;\n  bucket?: string;\n  endpoint?: string;\n  /**\n   * default value: true\n   * when using proxyHost&proxyPort, it needs to be set to false\n   */\n  secure?: boolean;\n  region: string;\n\n  /**\n   * proxy for web, use it with the middleware of `./proxy`\n   */\n  proxy?:\n    | string\n    | {\n        url: string;\n        needProxyParams?: boolean;\n      };\n  /**\n   * proxy to general http proxy server, this feature doesn't work in browser environment.\n   * only support http proxy server.\n   * proxyHost and proxyPort are required if the proxy function works.\n   * HINT: need set `secure` field false\n   */\n  proxyHost?: string;\n  proxyPort?: number;\n  // username and password don't be supported currently\n  // proxyUsername?: string;\n  // proxyPassword?: string;\n\n  /**\n   * default value: true\n   */\n  enableVerifySSL?: boolean;\n\n  /**\n   * default value: true\n   */\n  autoRecognizeContentType?: boolean;\n\n  /**\n   * unit: ms\n   * default value: 120s\n   * disable if value <= 0\n   */\n  requestTimeout?: number;\n\n  /**\n   * unit: ms\n   * default value: 10s\n   * disable if value <= 0\n   */\n  connectionTimeout?: number;\n\n  /**\n   * default value: 1024\n   */\n  maxConnections?: number;\n\n  /**\n   * unit: ms\n   * default value: 30s\n   */\n  idleConnectionTime?: number;\n\n  /**\n   * default value: 3\n   *\n   * disable if value <= 0\n   */\n  maxRetryCount?: number;\n\n  // TODO: need more efficient way, 1min for 10M currently\n  /**\n   * default value: false\n   *\n   * CRC executed by js is slow currently, it's default value will be true if it is fast enough.\n   */\n  enableCRC?: boolean;\n\n  /**\n   * set request adapter to send request.\n   */\n  requestAdapter?: AxiosAdapter;\n\n  /**\n   * default value: false  ${bucket}.${endpoint}\n   * if true request will not combine `${bucket}.${endpoint}`\n   */\n  isCustomDomain?: boolean;\n\n  /**\n   * @private unstable option: false | true | undefined\n   * default value: undefined\n   * true:\n   * Allow SDK to internally catch server errors for 404 and return default values\n   * Allow SDK to internally change some put methods to delete methods when pass empty value\n   */\n  enableOptimizeMethodBehavior?: boolean;\n  /**\n   * @private unstable option\n   */\n  forcePathStyle?: boolean;\n\n  userAgentProductName?: string;\n  userAgentSoftName?: string;\n  userAgentSoftVersion?: string;\n  userAgentCustomizedKeyValues?: Record<string, string>;\n}\n\ninterface NormalizedTOSConstructorOptions extends TOSConstructorOptions {\n  secure: boolean;\n  endpoint: string;\n  enableVerifySSL: boolean;\n  autoRecognizeContentType: boolean;\n  requestTimeout: number;\n  connectionTimeout: number;\n  maxConnections: number;\n  idleConnectionTime: number;\n  maxRetryCount: number;\n  enableCRC: boolean;\n}\n\ninterface GetSignatureQueryUrlInput {\n  bucket: string;\n  method: Method;\n  path: string;\n  subdomain: boolean;\n  endpoint: string;\n  // unit: second\n  expires: number;\n  query?: Record<string, any>;\n}\n\ninterface GetSignaturePolicyQueryInput {\n  bucket: string;\n  expires: number;\n  policy: {\n    conditions: (string[] | { bucket: string } | { key: string })[];\n  };\n}\n\ntype GetSignatureQueryInput =\n  | GetSignatureQueryUrlInput\n  | GetSignaturePolicyQueryInput;\n\ninterface FetchOpts<T> {\n  needMd5?: boolean;\n  handleResponse?: (response: AxiosResponse<T>) => T;\n  subdomainBucket?: string;\n  axiosOpts?: AxiosRequestConfig;\n}\n\nexport interface TosResponse<T> {\n  data: T;\n\n  statusCode: number;\n  headers: Headers;\n  /**\n   * identifies the errored request, equals to headers['x-tos-request-id'].\n   * If you has any question about the request, please send the requestId and id2 to TOS worker.\n   */\n  requestId: string;\n\n  /**\n   * identifies the errored request, equals to headers['x-tos-id-2'].\n   * If you has any question about the request, please send the requestId and id2 to TOS worker.\n   */\n  id2: string;\n}\n\nexport class TOSBase {\n  opts: NormalizedTOSConstructorOptions;\n\n  axiosInst: AxiosInstance;\n\n  userAgent: string;\n\n  private httpAgent: unknown;\n  private httpsAgent: unknown;\n\n  constructor(_opts: TOSConstructorOptions) {\n    this.opts = this.normalizeOpts(_opts);\n\n    if (process.env.TARGET_ENVIRONMENT === 'node') {\n      this.httpAgent = TosAgent({ tosOpts: { ...this.opts, isHttps: false } });\n      // fix axios issue, it uses `httpsAgent` although http proxy is enabled.\n      const isProxy = !!this.opts.proxyHost;\n      this.httpsAgent = TosAgent({\n        tosOpts: { ...this.opts, isHttps: !isProxy },\n      });\n    }\n\n    this.userAgent = this.getUserAgent();\n    this.axiosInst = makeAxiosInst(this.opts.maxRetryCount);\n  }\n\n  private normalizeOpts(_opts: TOSConstructorOptions) {\n    // 对字符串参数做 trim 操作\n    const trimKeys = [\n      'accessKeyId',\n      'accessKeySecret',\n      'stsToken',\n      'region',\n      'endpoint',\n    ] as const;\n    trimKeys.forEach((key) => {\n      const value = _opts[key];\n      if (typeof value === 'string') {\n        // maybe undefined\n        _opts[key] = value.trim();\n      }\n    });\n\n    const mustKeys = ['accessKeyId', 'accessKeySecret', 'region'];\n    const mustKeysErrorStr = mustKeys\n      .filter((key) => !(_opts as any)[key])\n      .join(', ');\n\n    if (mustKeysErrorStr) {\n      throw new TosClientError(`lack params: ${mustKeysErrorStr}.`);\n    }\n\n    const endpoint = _opts.endpoint || getEndpoint(_opts.region);\n    if (!endpoint) {\n      throw new TosClientError(\n        `the value of param region is invalid, correct values are cn-beijing, cn-nantong etc.`\n      );\n    }\n\n    if (endpoint.includes('s3')) {\n      throw new TosClientError(\n        `do not support s3 endpoint, please use tos endpoint.`\n      );\n    }\n\n    const secure = _opts.secure == null ? true : !!_opts.secure;\n    const _default = <T extends unknown>(\n      v: T | undefined | null,\n      defaultValue: T\n    ) => (v == null ? defaultValue : v);\n\n    const enableCRC = _opts.enableCRC ?? false;\n    if (enableCRC && process.env.TARGET_ENVIRONMENT === 'browser') {\n      throw new TosClientError('not support crc in browser environment');\n    }\n\n    return {\n      ..._opts,\n      endpoint,\n      secure,\n      enableVerifySSL: _default(_opts.enableVerifySSL, true),\n      autoRecognizeContentType: _default(_opts.autoRecognizeContentType, true),\n      requestTimeout: _default(_opts.requestTimeout, 120_000),\n      connectionTimeout: _default(_opts.connectionTimeout, 10_000),\n      maxConnections: _default(_opts.maxConnections, 1024),\n      idleConnectionTime: _default(_opts.idleConnectionTime, 30_000),\n      maxRetryCount: _default(_opts.maxRetryCount, 3),\n      enableCRC: _opts.enableCRC ?? false,\n      requestAdapter: getAdapter(),\n    };\n  }\n\n  private getUserAgent() {\n    // ve-tos-go-sdk/v2.0.0 (linux/amd64;go1.17.0)\n    const language =\n      process.env.TARGET_ENVIRONMENT === 'browser' ? 'browserjs' : 'nodejs';\n    const sdkVersion = `ve-tos-${language}-sdk/v${version}`;\n    if (process.env.TARGET_ENVIRONMENT === 'browser') {\n      return sdkVersion;\n    }\n\n    const osType = (() => {\n      const oriType = os.type();\n      const aliasType: Record<string, string> = {\n        Linux: 'linux',\n        Darwin: 'darwin',\n        Windows_NT: 'windows',\n      };\n      return aliasType[oriType] || oriType;\n    })();\n    const nodeVersion = (() => {\n      return process.version.replaceAll('v', '');\n    })();\n    const stdStr = `${sdkVersion} (${osType}/${process.arch};nodejs${nodeVersion})`;\n    const moreStr = (() => {\n      const { userAgentProductName, userAgentSoftName, userAgentSoftVersion } =\n        this.opts;\n      let customStr = Object.entries(\n        this.opts.userAgentCustomizedKeyValues || {}\n      )\n        .map(([k, v]) => {\n          return `${k}/${v}`;\n        })\n        .join(';');\n      customStr = customStr ? `(${customStr})` : '';\n\n      if (\n        !userAgentProductName &&\n        !userAgentSoftName &&\n        !userAgentSoftVersion &&\n        !customStr\n      ) {\n        return '';\n      }\n      const defaultValue = 'undefined';\n      const productSoftStr = [\n        userAgentProductName,\n        userAgentSoftName,\n        userAgentSoftVersion,\n      ]\n        .map((it) => it || defaultValue)\n        .join('/');\n\n      return [productSoftStr, customStr].filter(Boolean).join(' ');\n    })();\n\n    return [stdStr, moreStr].filter(Boolean).join(' -- ');\n  }\n\n  protected async fetch<Data>(\n    method: Method,\n    path: string,\n    query: Record<string, any>,\n    headers: Headers,\n    body?: Object | File | Blob | NodeJS.ReadableStream,\n    opts?: FetchOpts<Data>\n  ): Promise<TosResponse<Data>> {\n    const handleResponse = opts?.handleResponse || ((res) => res.data);\n    const needMd5 = opts?.needMd5 || false;\n\n    if (body && needMd5) {\n      const md5String = hashMd5(JSON.stringify(body), 'base64');\n      headers['content-md5'] = md5String;\n    }\n\n    const [endpoint, newPath] = (() => {\n      if (opts?.subdomainBucket && this.opts.forcePathStyle) {\n        return [this.opts.endpoint, `/${opts.subdomainBucket}${path}`];\n      }\n      // if isCustomDomain true, not add subdomainBucket\n      if (opts?.subdomainBucket && !this.opts.isCustomDomain) {\n        // endpoint is ip address\n        if (/^(\\d|:)/.test(this.opts.endpoint)) {\n          return [this.opts.endpoint, `/${opts.subdomainBucket}${path}`];\n        }\n        return [`${opts?.subdomainBucket}.${this.opts.endpoint}`, path];\n      }\n      return [this.opts.endpoint, path];\n    })();\n    path = newPath;\n\n    headers = encodeHeadersValue(headers);\n\n    const signOpt = {\n      // TODO: delete endpoints and buckets\n      endpoints: undefined,\n      bucket: '',\n\n      method,\n      headers: { ...headers },\n      path,\n      query: getSortedQueryString(query),\n      host: endpoint,\n    };\n\n    const signv4 = new ISigV4Credentials(\n      this.opts.stsToken,\n      this.opts.accessKeySecret,\n      this.opts.accessKeyId\n    );\n\n    const sig = new SignersV4(\n      {\n        algorithm: 'TOS4-HMAC-SHA256',\n        region: this.opts.region,\n        serviceName: 'tos',\n        bucket: '',\n        securityToken: this.opts.stsToken,\n      },\n      signv4\n    );\n\n    const signatureHeaders = sig.signatureHeader(signOpt);\n    const reqHeaders = { ...headers };\n\n    const reqOpts: AxiosRequestConfig = {\n      method,\n      baseURL: `http${this.opts.secure ? 's' : ''}://${endpoint}`,\n      url: path,\n      params: query,\n      headers: reqHeaders,\n      data: body,\n    };\n\n    signatureHeaders.forEach((value, key) => {\n      reqOpts.headers[key] = value;\n    });\n\n    const normalizedProxy = normalizeProxy(this.opts.proxy);\n    if (normalizedProxy?.url && !this.opts.proxyHost) {\n      // proxy for nodejs middleware server\n      reqOpts.baseURL = normalizedProxy.url;\n      if (normalizedProxy?.needProxyParams) {\n        reqOpts.params['x-proxy-tos-host'] = endpoint;\n        delete reqHeaders['host'];\n      }\n    } else if (this.opts.proxyHost) {\n      if (!this.opts.proxyPort) {\n        throw new TosClientError(\n          'The `proxyPort` is required if `proxyHost` is truly.'\n        );\n      }\n\n      // proxy for general proxy server\n      reqOpts.proxy = {\n        host: this.opts.proxyHost,\n        port: this.opts.proxyPort,\n        protocol: 'http',\n      };\n    }\n\n    reqHeaders['user-agent'] = this.userAgent;\n    if (this.opts.requestTimeout > 0 && this.opts.requestTimeout !== Infinity) {\n      reqOpts.timeout = this.opts.requestTimeout;\n    }\n\n    if (process.env.TARGET_ENVIRONMENT === 'node') {\n      reqOpts.httpAgent = this.httpAgent;\n      reqOpts.httpsAgent = this.httpsAgent;\n    }\n\n    try {\n      const logReqOpts = { ...reqOpts };\n      delete logReqOpts.httpAgent;\n      delete logReqOpts.httpsAgent;\n      log.TOS('reqOpts: ', logReqOpts);\n      const res = await this.axiosInst({\n        ...{\n          maxBodyLength: Infinity,\n          maxContentLength: Infinity,\n          adapter: this.opts.requestAdapter,\n        },\n        ...reqOpts,\n        ...(opts?.axiosOpts || {}),\n        [retrySignatureNamespace]: {\n          signOpt,\n          sigInst: sig,\n        },\n      });\n\n      const data = handleResponse(res);\n      return {\n        data,\n        statusCode: res.status,\n        headers: res.headers,\n        requestId: res.headers['x-tos-request-id'],\n        id2: res.headers['x-tos-id-2'],\n      };\n    } catch (err) {\n      if (\n        axios.isAxiosError(err) &&\n        err.response?.headers?.['x-tos-request-id']\n      ) {\n        // it's ServerError only if `RequestId` exists\n        const response: AxiosResponse<TosServerErrorData> = err.response;\n        log.TOS('TosServerError response: ', response);\n        const err2 = new TosServerError(response);\n        throw err2;\n      }\n\n      // it is neither ServerError nor ClientError, it's other error\n      log.TOS('err: ', err);\n      throw err;\n    }\n  }\n\n  protected async fetchBucket<Data>(\n    bucket: string | undefined,\n    method: Method,\n    query: any,\n    headers: Headers,\n    body?: Object | File | Blob | NodeJS.ReadableStream,\n    opts?: FetchOpts<Data>\n  ): Promise<TosResponse<Data>> {\n    const actualBucket = bucket || this.opts.bucket;\n    if (!actualBucket) {\n      throw new TosClientError('Must provide bucket param');\n    }\n    return this.fetch(method, '/', query, headers, body, {\n      ...opts,\n      subdomainBucket: actualBucket,\n    });\n  }\n\n  protected async _fetchObject<Data>(\n    input: { bucket?: string; key: string } | string,\n    method: Method,\n    query: any,\n    headers: Headers,\n    body?: Object | File | Blob | NodeJS.ReadableStream,\n    opts?: FetchOpts<Data>\n  ): Promise<TosResponse<Data>> {\n    const actualBucket =\n      (typeof input !== 'string' && input.bucket) || this.opts.bucket;\n    const actualKey = typeof input === 'string' ? input : input.key;\n    if (!actualBucket) {\n      throw new TosClientError('Must provide bucket param');\n    }\n    validateObjectName(actualKey);\n\n    return this.fetch(\n      method,\n      `/${encodeURIComponent(actualKey)}`,\n      query,\n      headers,\n      body,\n      {\n        ...opts,\n        subdomainBucket: actualBucket,\n      }\n    );\n  }\n\n  protected getSignatureQuery(\n    input: GetSignatureQueryInput\n  ): Record<string, string> {\n    const signv4 = new ISigV4Credentials(\n      this.opts.stsToken,\n      this.opts.accessKeySecret,\n      this.opts.accessKeyId\n    );\n\n    const sig = new SignersV4(\n      {\n        algorithm: 'TOS4-HMAC-SHA256',\n        region: this.opts.endpoint,\n        serviceName: 'tos',\n        // SignV4 uses this.options.bucket, so set it here\n        bucket: input.bucket,\n        securityToken: this.opts.stsToken,\n      },\n      signv4\n    );\n\n    if ('policy' in input) {\n      return sig.getSignaturePolicyQuery(\n        {\n          policy: input.policy,\n        },\n        input.expires\n      );\n    } else {\n      return sig.getSignatureQuery(\n        {\n          method: input.method,\n          path: input.path,\n          endpoints: input.subdomain ? input.endpoint : undefined,\n          host: input.endpoint,\n          query: input.query,\n        },\n        input.expires\n      );\n    }\n  }\n\n  protected getObjectPath = (\n    opts: { bucket?: string; key: string } | string\n  ) => {\n    const actualBucket =\n      (typeof opts !== 'string' && opts.bucket) || this.opts.bucket;\n    const actualKey = typeof opts === 'string' ? opts : opts.key;\n    if (!actualBucket) {\n      throw new TosClientError('Must provide bucket param');\n    }\n    return `/${actualBucket}/${encodeURIComponent(actualKey)}`;\n  };\n\n  protected normalizeBucketInput<T extends { bucket: string }>(\n    input: T | string\n  ): T {\n    return (typeof input === 'string' ? { bucket: input } : input) as T;\n  }\n  protected normalizeObjectInput<T extends { key: string }>(\n    input: T | string\n  ): T {\n    return (typeof input === 'string' ? { key: input } : input) as T;\n  }\n\n  protected setObjectContentTypeHeader = (\n    input: string | { key: string },\n    headers: Headers\n  ): void => {\n    if (headers['content-type'] != null) {\n      return;\n    }\n\n    let mimeType = DEFAULT_CONTENT_TYPE;\n    const key = getObjectInputKey(input);\n\n    if (this.opts.autoRecognizeContentType) {\n      mimeType = lookupMimeType(key) || mimeType;\n    }\n\n    if (mimeType) {\n      headers['content-type'] = mimeType;\n    }\n  };\n\n  protected getNormalDataFromError = getNormalDataFromError;\n}\n\nexport default TOSBase;\n\nfunction getAdapter(): AxiosAdapter | undefined {\n  if (process.env.TARGET_ENVIRONMENT === 'node') {\n    // nodejs env\n    return undefined;\n  }\n  if (typeof window !== 'undefined' && typeof window.location !== 'undefined') {\n    // browser env\n    return undefined;\n  }\n\n  switch (true) {\n    case typeof wx !== 'undefined':\n    case typeof swan !== 'undefined':\n    case typeof dd !== 'undefined':\n    case typeof my !== 'undefined':\n      return mpAdapter as AxiosAdapter;\n    case typeof uni !== 'undefined':\n      return uniappAdapter as AxiosAdapter;\n    default:\n      return undefined;\n  }\n}\n", "import { covertCamelCase2Kebab, makeArrayProp } from '../../utils';\nimport TOSBase from '../base';\n\nexport interface ListObjectsInput {\n  bucket?: string;\n  continuationToken?: string;\n  delimiter?: string;\n  encodingType?: string;\n  fetchOwner?: string;\n  maxKeys?: string | number;\n  prefix?: string;\n  marker?: string;\n\n  /**\n   * use `marker` instead of `startAfter`\n   */\n  startAfter?: string;\n  /**\n   * equal to listObjectVersions when input\n   */\n  versions?: string;\n  listType?: string;\n  versionIdMarker?: string;\n  /**\n   * only works when pass versions field\n   */\n  keyMarker?: string;\n}\n\nexport interface ListObjectsContentItem {\n  ETag: string;\n  Key: string;\n  // \"2021-08-02T09:53:27.000Z\"\n  LastModified: string;\n  Owner: { ID: string; DisplayName: string };\n  Size: number;\n  StorageClass: string;\n}\n\nexport interface ListObjectsVersionItem {\n  ETag: string;\n  IsLatest: boolean;\n  Key: string;\n  LastModified: string;\n  Owner: { ID: string; DisplayName: string };\n  Size: number;\n  StorageClass: string;\n  VersionId: string;\n}\n\nexport interface ListObjectDeleteMarkerItem {\n  ETag: string;\n  IsLatest: boolean;\n  Key: string;\n  LastModified: string;\n  Owner: { ID: string; DisplayName: string };\n  Size: number;\n  StorageClass: string;\n  VersionId: string;\n}\n\nexport interface ListedCommonPrefix {\n  Prefix: string;\n}\nexport interface ListObjectsOutput {\n  CommonPrefixes: ListedCommonPrefix[];\n  Contents: ListObjectsContentItem[];\n  IsTruncated: boolean;\n  Marker: string;\n  MaxKeys: number;\n  KeyMarker?: string;\n  Name: string;\n  Prefix: string;\n  ContinuationToken?: string;\n  NextContinuationToken?: string;\n  Delimiter?: string;\n  EncodingType?: string;\n  NextMarker?: string;\n  VersionIdMarker?: string;\n  Versions: ListObjectsVersionItem[];\n  NextKeyMarker?: string;\n  DeleteMarkers: ListObjectDeleteMarkerItem[];\n  NextVersionIdMarker?: string;\n}\n\nclass TOSListObjects extends TOSBase {\n  listObjects = listObjects;\n  listObjectVersions = listObjectVersions;\n}\n\n/**\n *\n * @deprecated use listObjectsType2 instead\n * @returns\n */\nexport async function listObjects(\n  this: TOSListObjects,\n  input: ListObjectsInput = {}\n) {\n  const { bucket, ...nextQuery } = input;\n  const ret = await this.fetchBucket<ListObjectsOutput>(\n    input.bucket,\n    'GET',\n    covertCamelCase2Kebab(nextQuery),\n    {}\n  );\n  const arrayProp = makeArrayProp(ret.data);\n  arrayProp('CommonPrefixes');\n  arrayProp('Contents');\n  arrayProp('Versions');\n  arrayProp('DeleteMarkers');\n  return ret;\n}\n\nexport type ListObjectVersionsInput = Pick<\n  ListObjectsInput,\n  | 'bucket'\n  | 'prefix'\n  | 'delimiter'\n  | 'keyMarker'\n  | 'versionIdMarker'\n  | 'maxKeys'\n  | 'encodingType'\n>;\n\nexport interface listObjectVersionsOutput {\n  Name: string;\n  Prefix: string;\n  KeyMarker?: string;\n  VersionIdMarker?: string;\n  MaxKeys: number;\n  Delimiter?: string;\n  IsTruncated: boolean;\n  EncodingType?: string;\n  NextKeyMarker?: string;\n  NextVersionIdMarker?: string;\n  CommonPrefixes: ListedCommonPrefix[];\n  Versions: ListObjectsVersionItem[];\n  DeleteMarkers: ListObjectDeleteMarkerItem[];\n}\n\nexport async function listObjectVersions(\n  this: TOSListObjects,\n  input: ListObjectVersionsInput = {}\n) {\n  const { bucket, ...nextQuery } = input;\n  const ret = await this.fetchBucket<listObjectVersionsOutput>(\n    input.bucket,\n    'GET',\n    covertCamelCase2Kebab({ versions: '', ...nextQuery }),\n    {}\n  );\n  const arrayProp = makeArrayProp(ret.data);\n  arrayProp('CommonPrefixes');\n  arrayProp('Versions');\n  arrayProp('DeleteMarkers');\n  return ret;\n}\n", "import { covertCamelCase2Kebab, makeArrayProp } from '../../utils';\nimport TOSBase, { TosResponse } from '../base';\n\nexport interface ListObjectsType2Input {\n  bucket?: string;\n  prefix?: string;\n  delimiter?: string;\n  encodingType?: string;\n  /**\n   * if not specify `maxKeys` field, default maxKeys value is 1000.\n   */\n  maxKeys?: number;\n  continuationToken?: string;\n  startAfter?: string;\n  /**\n   * default value: false\n   * if set false, the method will keep fetch objects until get `maxKeys` objects.\n   * if set true,  the method will fetch objects once\n   */\n  listOnlyOnce?: boolean;\n}\n\nexport interface ListObjectsType2ContentItem {\n  ETag: string;\n  Key: string;\n  // \"2021-08-02T09:53:27.000Z\"\n  LastModified: string;\n  Owner?: { ID: string; DisplayName: string };\n  Size: number;\n  StorageClass: string;\n  HashCrc64ecma?: string;\n}\n\nexport interface ListObjectsType2VersionItem {\n  ETag: string;\n  IsLatest: boolean;\n  Key: string;\n  LastModified: string;\n  Owner: { ID: string; DisplayName: string };\n  Size: number;\n  StorageClass: string;\n  VersionId: string;\n}\n\nexport interface ListObjectDeleteMarkerItem {\n  ETag: string;\n  IsLatest: boolean;\n  Key: string;\n  LastModified: string;\n  Owner: { ID: string; DisplayName: string };\n  Size: number;\n  StorageClass: string;\n  VersionId: string;\n}\n\nexport interface ListedCommonPrefix {\n  Prefix: string;\n}\n\nexport interface ListObjectsType2Output {\n  Name: string;\n  Prefix: string;\n  MaxKeys: number;\n  Delimiter?: string;\n  EncodingType?: string;\n  IsTruncated: boolean;\n  KeyCount: number;\n  StartAfter?: string;\n  ContinuationToken?: string;\n  NextContinuationToken?: string;\n  CommonPrefixes: ListedCommonPrefix[];\n  Contents: ListObjectsType2ContentItem[];\n}\n\nclass TOSListObjectsType2 extends TOSBase {\n  listObjectsType2 = listObjectsType2;\n}\nconst DefaultListMaxKeys = 1000;\n\nexport async function listObjectsType2(\n  this: TOSListObjectsType2,\n  input: ListObjectsType2Input = {}\n): Promise<TosResponse<ListObjectsType2Output>> {\n  const { listOnlyOnce = false } = input;\n\n  let output;\n  if (!input.maxKeys) {\n    input.maxKeys = DefaultListMaxKeys;\n  }\n\n  if (listOnlyOnce) {\n    output = await listObjectsType2Once.call(this, input);\n  } else {\n    const maxKeys = input.maxKeys;\n    let params = {\n      ...input,\n      maxKeys,\n    };\n    while (true) {\n      const res = await listObjectsType2Once.call(this, params);\n      if (output == null) {\n        output = res;\n      } else {\n        output = {\n          ...res,\n          data: output.data,\n        };\n        output.data.KeyCount += res.data.KeyCount;\n        output.data.IsTruncated = res.data.IsTruncated;\n        output.data.NextContinuationToken = res.data.NextContinuationToken;\n        output.data.Contents = output.data.Contents.concat(res.data.Contents);\n        output.data.CommonPrefixes = output.data.CommonPrefixes.concat(\n          res.data.CommonPrefixes\n        );\n      }\n\n      if (!res.data.IsTruncated || output.data.KeyCount >= maxKeys) {\n        break;\n      }\n\n      params.continuationToken = res.data.NextContinuationToken;\n      params.maxKeys = params.maxKeys - res.data.KeyCount;\n    }\n  }\n\n  return output;\n}\nasync function listObjectsType2Once(\n  this: TOSListObjectsType2,\n  input: ListObjectsType2Input\n) {\n  const { bucket, ...nextQuery } = input;\n\n  const ret = await this.fetchBucket<ListObjectsType2Output>(\n    input.bucket,\n    'GET',\n    {\n      'list-type': 2,\n      ...covertCamelCase2Kebab(nextQuery),\n    },\n    {}\n  );\n  const arrayProp = makeArrayProp(ret.data);\n  arrayProp('CommonPrefixes');\n  arrayProp('Contents');\n  return ret;\n}\n", "import { TosClientError } from './browser-index';\nimport { TOSBase, type TOSConstructorOptions } from './methods/base';\nimport { paramsSerializer } from './utils';\nimport { getObjectV2 } from './methods/object/getObject';\nimport headObject from './methods/object/headObject';\nimport { listObjectVersions, listObjects } from './methods/object/listObjects';\nimport downloadFile from './methods/object/downloadFile';\nimport { listObjectsType2 } from './methods/object/listObjectsType2';\n\nexport interface ShareLinkClientOptions\n  extends Omit<\n    TOSConstructorOptions,\n    'region' | 'accessKeyId' | 'accessKeySecret' | 'endpoint' | 'bucket'\n  > {\n  policyUrl: string;\n}\n\ninterface ParsedPolicyUrlVal {\n  origin: string;\n  host: string;\n  search: string;\n}\n\n/** @private unstable */\nexport class ShareLinkClient extends TOSBase {\n  shareLinkClientOpts: ShareLinkClientOptions;\n\n  private parsedPolicyUrlVal: ParsedPolicyUrlVal;\n\n  modifyAxiosInst() {\n    const axiosInst = this.axiosInst;\n\n    axiosInst.interceptors.request.use((config) => {\n      const headers = config.headers || {};\n      delete headers['authorization'];\n      headers['host'] = this.parsedPolicyUrlVal.host;\n      config.baseURL = this.parsedPolicyUrlVal.origin;\n      config.paramsSerializer = (params) => {\n        const addQueryStr = paramsSerializer(params);\n        return [this.parsedPolicyUrlVal.search, addQueryStr]\n          .filter((it) => it.trim())\n          .join('&');\n      };\n      return config;\n    });\n  }\n\n  constructor(_opts: ShareLinkClientOptions) {\n    super({\n      ..._opts,\n\n      bucket: 'fake-bucket',\n      region: 'fake-region',\n      accessKeyId: 'fake-accessKeyId',\n      accessKeySecret: 'fake-accessKeySecret',\n      endpoint: 'fake-endpoint.com',\n    });\n\n    this.shareLinkClientOpts = _opts;\n    this.parsedPolicyUrlVal = this.initParsedPolicyUrlVal();\n    this.modifyAxiosInst();\n  }\n\n  private initParsedPolicyUrlVal(): ParsedPolicyUrlVal {\n    const reg = /(https?:\\/\\/(?:[^@]+@)?([^/?]+))[^?]*\\?(.+)/;\n    const matched = this.shareLinkClientOpts.policyUrl.match(reg);\n    if (!matched) {\n      throw new TosClientError('the `policyUrl` param is invalid');\n    }\n    return {\n      origin: matched[1],\n      host: matched[2],\n      search: matched[3],\n    };\n  }\n\n  headObject = headObject;\n  getObjectV2 = getObjectV2;\n  listObjects = listObjects;\n  listObjectsType2 = listObjectsType2;\n  listObjectVersions = listObjectVersions;\n  downloadFile = downloadFile;\n}\n", "import TOSBase from '../base';\nimport { Acl, Headers, StorageClass } from '../../interface';\nimport {\n  fillRequestHeaders,\n  makeArrayProp,\n  normalizeHeadersKey,\n} from '../../utils';\nimport TosClientError from '../../TosClientError';\nimport { AzRedundancyType, StorageClassType } from '../../TosExportEnum';\nimport { TosHeader } from '../object/sharedTypes';\n\nexport interface Bucket {\n  // '2021-07-20T09:22:05.000Z'\n  CreationDate: string;\n  ExtranetEndpoint: string;\n  IntranetEndpoint: string;\n  Location: string;\n  Name: string;\n  Owner: { ID: string };\n  BucketType?: string;\n}\n\nexport interface ListBucketOutput {\n  Buckets: Bucket[];\n}\n\nexport interface PutBucketInput {\n  bucket?: string;\n  acl?: Acl;\n  grantFullControl?: string;\n  grantRead?: string;\n  grantReadAcp?: string;\n  grantWrite?: string;\n  grantWriteAcp?: string;\n  storageClass?: StorageClassType;\n  azRedundancy?: AzRedundancyType;\n  projectName?: string;\n  bucketType?: string;\n  headers?: {\n    [key: string]: string | undefined;\n    ['x-tos-acl']?: Acl;\n    ['x-tos-grant-full-control']?: string;\n    ['x-tos-grant-read']?: string;\n    ['x-tos-grant-read-acp']?: string;\n    ['x-tos-grant-write']?: string;\n    ['x-tos-grant-write-acp']?: string;\n    ['x-tos-storage-class']?: StorageClass;\n  };\n}\nexport interface ListBucketInput {\n  projectName?: string;\n}\nexport async function listBuckets(this: TOSBase, input: ListBucketInput = {}) {\n  const headers = {};\n  /**\n   * empty string is invalid value\n   */\n  input?.projectName &&\n    fillRequestHeaders({ ...input, headers }, ['projectName']);\n  const res = await this.fetch<ListBucketOutput>('GET', '/', {}, headers);\n  const arrayProp = makeArrayProp(res.data);\n  arrayProp('Buckets');\n\n  return res;\n}\n\nexport async function createBucket(this: TOSBase, input: PutBucketInput) {\n  const actualBucket = input.bucket || this.opts.bucket;\n  // these errors are only for creating bucket\n  if (actualBucket) {\n    if (actualBucket.length < 3 || actualBucket.length > 63) {\n      throw new TosClientError(\n        'invalid bucket name, the length must be [3, 63]'\n      );\n    }\n    if (!/^([a-z]|-|\\d)+$/.test(actualBucket)) {\n      throw new TosClientError(\n        'invalid bucket name, the character set is illegal'\n      );\n    }\n    if (/^-/.test(actualBucket) || /-$/.test(actualBucket)) {\n      throw new TosClientError(\n        `invalid bucket name, the bucket name can be neither starting with '-' nor ending with '-'`\n      );\n    }\n  }\n  const headers = (input.headers = normalizeHeadersKey(input.headers));\n\n  fillRequestHeaders(input, [\n    'acl',\n    'grantFullControl',\n    'grantRead',\n    'grantReadAcp',\n    'grantWrite',\n    'grantWriteAcp',\n    'storageClass',\n    'azRedundancy',\n    'bucketType',\n  ]);\n\n  /**\n   * empty string is invalid value\n   */\n  input?.projectName && fillRequestHeaders(input, ['projectName']);\n\n  const res = await this.fetchBucket(input.bucket, 'PUT', {}, headers);\n  return res;\n}\n\nexport async function deleteBucket(this: TOSBase, bucket?: string) {\n  return this.fetchBucket(bucket, 'DELETE', {}, {});\n}\n\nexport interface HeadBucketOutput {\n  ['x-tos-bucket-region']: string;\n  ['x-tos-storage-class']: StorageClass;\n  ['x-tos-bucket-type']?: string;\n  ProjectName?: string;\n}\n\nexport async function headBucket(this: TOSBase, bucket?: string) {\n  return this.fetchBucket<HeadBucketOutput>(bucket, 'HEAD', {}, {}, undefined, {\n    handleResponse: (res) => {\n      return {\n        ...res.headers,\n        ProjectName: res.headers[TosHeader.HeaderProjectName],\n      };\n    },\n  });\n}\n\nexport interface PutBucketStorageClassInput {\n  bucket: string;\n  storageClass: StorageClassType;\n}\n\nexport interface PutBucketStorageClassOutput {}\n\nexport async function putBucketStorageClass(\n  this: TOSBase,\n  input: PutBucketStorageClassInput\n) {\n  const { bucket, storageClass } = input;\n\n  return this.fetchBucket<PutBucketStorageClassOutput>(\n    bucket,\n    'PUT',\n    { storageClass: '' },\n    {\n      'x-tos-storage-class': storageClass,\n    }\n  );\n}\n", "import TOSBase from '../base';\nimport { Headers, AclInterface, Acl } from '../../interface';\nimport { makeArrayProp } from '../../utils';\n\nexport type GetBucketAclOutput = AclInterface;\n\nexport interface PutBucketAclInput {\n  bucket?: string;\n  acl?: Acl;\n  aclBody?: AclInterface;\n}\n\nexport async function putBucketAcl(this: TOSBase, input: PutBucketAclInput) {\n  const headers: Headers = {};\n  if (input.acl) headers['x-tos-acl'] = input.acl;\n\n  const res = await this.fetchBucket(\n    input.bucket,\n    'PUT',\n    { acl: '' },\n    headers,\n    input.aclBody,\n    { needMd5: true }\n  );\n  return res;\n}\n\nexport async function getBucketAcl(this: TOSBase, bucket?: string) {\n  const res = await this.fetchBucket<GetBucketAclOutput>(\n    bucket,\n    'GET',\n    {\n      acl: '',\n    },\n    {}\n  );\n  const arrayProp = makeArrayProp(res.data);\n  arrayProp('Grants');\n  return res;\n}\n", "import TOSBase, { TosResponse } from '../base';\nimport {\n  checkCRC64WithHeaders,\n  fillRequestHeaders,\n  makeRetryStreamAutoClose,\n  normalizeHeaders<PERSON>ey,\n  safeAwait,\n  tryDestroy,\n} from '../../utils';\nimport {\n  Acl,\n  DataTransferStatus,\n  DataTransferType,\n  SupportObjectBody,\n} from '../../interface';\nimport TosClientError from '../../TosClientError';\nimport * as fsp from '../../nodejs/fs-promises';\nimport { Stats, ReadStream } from 'fs';\nimport { getSize, getNewBodyConfig } from './utils';\nimport { retryNamespace } from '../../axios';\nimport { IRateLimiter } from '../../universal/rate-limiter';\nimport { StorageClassType } from '../../TosExportEnum';\n\nexport interface PutObjectInput {\n  bucket?: string;\n  key: string;\n  /**\n   * body is empty buffer if it's falsy.\n   */\n  body?: SupportObjectBody;\n\n  contentLength?: number;\n  contentMD5?: string;\n  contentSHA256?: string;\n  cacheControl?: string;\n  contentDisposition?: string;\n  contentEncoding?: string;\n  contentLanguage?: string;\n  contentType?: string;\n  expires?: Date;\n\n  acl?: Acl;\n  grantFullControl?: string;\n  grantRead?: string;\n  grantReadAcp?: string;\n  grantWrite?: string;\n  grantWriteAcp?: string;\n\n  ssecAlgorithm?: string;\n  ssecKey?: string;\n  ssecKeyMD5?: string;\n  serverSideEncryption?: string;\n  /**\n   * @private unstable\n   */\n  serverSideDataEncryption?: string;\n\n  meta?: Record<string, string>;\n  websiteRedirectLocation?: string;\n  storageClass?: StorageClassType;\n  ifMatch?: string;\n\n  dataTransferStatusChange?: (status: DataTransferStatus) => void;\n\n  /**\n   * the simple progress feature\n   * percent is [0, 1].\n   *\n   * since putObject is stateless, so if `putObject` fail and you retry it,\n   * `percent` will start from 0 again rather than from the previous value.\n   * if you need `percent` start from the previous value, you can use `uploadFile` instead.\n   */\n  progress?: (percent: number) => void;\n  /**\n   * unit: bit/s\n   * server side traffic limit\n   **/\n  trafficLimit?: number;\n  /**\n   * only works for nodejs environment\n   **/\n  rateLimiter?: IRateLimiter;\n\n  forbidOverwrite?: boolean;\n\n  callback?: string;\n  callbackVar?: string;\n\n  headers?: {\n    [key: string]: string | undefined;\n    'content-length'?: string;\n    'content-type'?: string;\n    'content-md5'?: string;\n    'cache-control'?: string;\n    expires?: string;\n    'x-tos-acl'?: Acl;\n    'x-tos-grant-full-control'?: string;\n    'x-tos-grant-read'?: string;\n    'x-tos-grant-read-acp'?: string;\n    'x-tos-grant-write-acp'?: string;\n    'x-tos-server-side-encryption-customer-algorithm'?: string;\n    'x-tos-server-side-encryption-customer-key'?: string;\n    'x-tos-server-side-encryption-customer-key-md5'?: string;\n    'x-tos-website-redirect-location'?: string;\n    'x-tos-storage-class'?: string;\n    'x-tos-server-side-encryption'?: string;\n    'x-tos-forbid-overwrite'?: string;\n    'If-Match'?: string;\n  };\n}\n\ninterface PutObjectInputInner extends PutObjectInput {\n  makeRetryStream?: () => NodeJS.ReadableStream | undefined;\n}\n\nexport interface PutObjectOutput {\n  'x-tos-server-side-encryption-customer-algorithm'?: string;\n  'x-tos-server-side-encryption-customer-key-md5'?: string;\n  'x-tos-version-id'?: string;\n  'x-tos-hash-crc64ecma'?: string;\n  'x-tos-server-side-encryption'?: string;\n  CallbackResult?: string;\n}\n\nexport async function putObject(this: TOSBase, input: PutObjectInput | string) {\n  return _putObject.call(this, input);\n}\n\nexport async function _putObject(\n  this: TOSBase,\n  input: PutObjectInputInner | string\n): Promise<TosResponse<PutObjectOutput>> {\n  input = this.normalizeObjectInput(input);\n  const headers = (input.headers = normalizeHeadersKey(input.headers));\n  fillRequestHeaders(input, [\n    'contentLength',\n    'contentMD5',\n    'contentSHA256',\n    'cacheControl',\n    'contentDisposition',\n    'contentEncoding',\n    'contentLanguage',\n    'contentType',\n    'expires',\n    'acl',\n    'grantFullControl',\n    'grantRead',\n    'grantReadAcp',\n    'grantWrite',\n    'grantWriteAcp',\n    'ssecAlgorithm',\n    'ssecKey',\n    'ssecKeyMD5',\n    'serverSideEncryption',\n    'serverSideDataEncryption',\n    'meta',\n    'websiteRedirectLocation',\n    'storageClass',\n    'trafficLimit',\n    'callback',\n    'callbackVar',\n    'forbidOverwrite',\n    'ifMatch',\n  ]);\n  this.setObjectContentTypeHeader(input, headers);\n\n  const totalSize = getSize(input.body, headers);\n  const totalSizeValid = totalSize != null;\n\n  if (!totalSizeValid && (input.dataTransferStatusChange || input.progress)) {\n    console.warn(\n      `Don't get totalSize of putObject's body, the \\`dataTransferStatusChange\\` and \\`progress\\` callback will not trigger. You can use \\`putObjectFromFile\\` instead`\n    );\n  }\n\n  let consumedBytes = 0;\n  const { dataTransferStatusChange, progress } = input;\n  const triggerDataTransfer = (\n    type: DataTransferType,\n    rwOnceBytes: number = 0\n  ) => {\n    // request cancel will make rwOnceBytes < 0 in browser\n    if (!totalSizeValid || rwOnceBytes < 0) {\n      return;\n    }\n    if (!dataTransferStatusChange && !progress) {\n      return;\n    }\n    consumedBytes += rwOnceBytes;\n\n    dataTransferStatusChange?.({\n      type,\n      rwOnceBytes,\n      consumedBytes,\n      totalBytes: totalSize,\n    });\n    const progressValue = (() => {\n      if (totalSize === 0) {\n        if (type === DataTransferType.Succeed) {\n          return 1;\n        }\n        return 0;\n      }\n      return consumedBytes / totalSize;\n    })();\n    if (progressValue === 1) {\n      if (type === DataTransferType.Succeed) {\n        progress?.(progressValue);\n      } else {\n        // not exec progress\n      }\n    } else {\n      progress?.(progressValue);\n    }\n  };\n\n  const bodyConfig = await getNewBodyConfig({\n    body: input.body,\n    dataTransferCallback: (n) => triggerDataTransfer(DataTransferType.Rw, n),\n    makeRetryStream: input.makeRetryStream,\n    enableCRC: this.opts.enableCRC,\n    rateLimiter: input.rateLimiter,\n  });\n\n  triggerDataTransfer(DataTransferType.Started);\n\n  const task = async () => {\n    const res = await this._fetchObject<PutObjectOutput>(\n      input,\n      'PUT',\n      {},\n      headers,\n      bodyConfig.body || '',\n      {\n        handleResponse: (res) => {\n          const result = { ...res.headers };\n          if ((input as PutObjectInputInner)?.callback && res.data) {\n            result.CallbackResult = `${JSON.stringify(res.data)}`;\n          }\n          return result;\n        },\n        axiosOpts: {\n          [retryNamespace]: {\n            beforeRetry: () => {\n              consumedBytes = 0;\n              bodyConfig.beforeRetry?.();\n            },\n            makeRetryStream: bodyConfig.makeRetryStream,\n          },\n          onUploadProgress: (event) => {\n            triggerDataTransfer(\n              DataTransferType.Rw,\n              event.loaded - consumedBytes\n            );\n          },\n        },\n      }\n    );\n    if (this.opts.enableCRC && bodyConfig.crc) {\n      checkCRC64WithHeaders(bodyConfig.crc, res.headers);\n    }\n    return res;\n  };\n  const [err, res] = await safeAwait(task());\n\n  if (err || !res) {\n    triggerDataTransfer(DataTransferType.Failed);\n    throw err;\n  }\n\n  triggerDataTransfer(DataTransferType.Succeed);\n  return res;\n}\n\ninterface PutObjectFromFileInput extends Omit<PutObjectInput, 'body'> {\n  filePath: string;\n}\n\nexport async function putObjectFromFile(\n  this: TOSBase,\n  input: PutObjectFromFileInput\n): Promise<TosResponse<PutObjectOutput>> {\n  const normalizedHeaders = normalizeHeadersKey(input.headers);\n  if (process.env.TARGET_ENVIRONMENT !== 'node') {\n    throw new TosClientError(\n      \"putObjectFromFile doesn't support in browser environment\"\n    );\n  }\n\n  if (!normalizedHeaders['content-length']) {\n    const stats: Stats = await fsp.stat(input.filePath);\n    normalizedHeaders['content-length'] = `${stats.size}`;\n  }\n\n  const makeRetryStream = makeRetryStreamAutoClose(() =>\n    fsp.createReadStream(input.filePath)\n  );\n\n  try {\n    return await _putObject.call(this, {\n      ...input,\n      body: makeRetryStream.make(),\n      headers: normalizedHeaders,\n      makeRetryStream: makeRetryStream.make,\n    });\n  } catch (err) {\n    tryDestroy(makeRetryStream.getLastStream(), err);\n    throw err;\n  }\n}\n\nexport default putObject;\n", "import { StorageClassType } from '../../TosExportEnum';\nimport { Acl } from '../../interface';\nimport { fillRequestHeaders, normalizeHeadersKey } from '../../utils';\nimport TOSBase from '../base';\n\nexport interface FetchObjectInput {\n  bucket?: string;\n  key: string;\n  url: string;\n  ignoreSameKey?: boolean;\n\n  acl?: Acl;\n  grantFullControl?: string;\n  grantRead?: string;\n  grantReadAcp?: string;\n  grantWriteAcp?: string;\n  storageClass?: StorageClassType;\n\n  ssecAlgorithm?: string;\n  ssecKey?: string;\n  ssecKeyMD5?: string;\n  meta?: Record<string, string>;\n\n  // contentMD5 is the base64 encoded of object's md5\n  contentMD5?: string;\n\n  headers?: {\n    [key: string]: string | undefined;\n  };\n}\n\nexport interface FetchObjectOutput {\n  VersionID?: string;\n  Etag: string;\n  SSECAlgorithm?: string;\n  SSECKeyMD5?: string;\n}\n\nexport async function fetchObject(this: TOSBase, input: FetchObjectInput) {\n  const headers = (input.headers = normalizeHeadersKey(input.headers));\n  fillRequestHeaders(input, [\n    'acl',\n    'grantFullControl',\n    'grantRead',\n    'grantReadAcp',\n    'grantWriteAcp',\n    'ssecAlgorithm',\n    'ssecKey',\n    'ssecKeyMD5',\n    'meta',\n    'storageClass',\n  ]);\n  const res = await this._fetchObject<FetchObjectOutput>(\n    input,\n    'POST',\n    {\n      fetch: '',\n    },\n    headers,\n    {\n      URL: input.url,\n      IgnoreSameKey: input.ignoreSameKey,\n      ContentMD5: input.contentMD5,\n    },\n    {\n      needMd5: true,\n    }\n  );\n  return res;\n}\n\nexport interface PutFetchTaskInput {\n  bucket?: string;\n  key: string;\n  url: string;\n  ignoreSameKey?: boolean;\n\n  acl?: Acl;\n  grantFullControl?: string;\n  grantRead?: string;\n  grantReadAcp?: string;\n  grantWriteAcp?: string;\n  storageClass?: StorageClassType;\n\n  ssecAlgorithm?: string;\n  ssecKey?: string;\n  ssecKeyMD5?: string;\n  meta?: Record<string, string>;\n\n  // contentMD5 is the base64 encoded of object's md5\n  contentMD5?: string;\n\n  headers?: {\n    [key: string]: string | undefined;\n  };\n}\n\nexport interface PutFetchTaskOutput {\n  TaskId: string;\n}\n\nexport async function putFetchTask(this: TOSBase, input: PutFetchTaskInput) {\n  const headers = (input.headers = normalizeHeadersKey(input.headers));\n  fillRequestHeaders(input, [\n    'acl',\n    'grantFullControl',\n    'grantRead',\n    'grantReadAcp',\n    'grantWriteAcp',\n    'ssecAlgorithm',\n    'ssecKey',\n    'ssecKeyMD5',\n    'meta',\n    'storageClass',\n  ]);\n\n  const res = await this._fetchObject<PutFetchTaskOutput>(\n    input,\n    'POST',\n    {\n      fetchTask: '',\n    },\n    headers,\n    {\n      URL: input.url,\n      IgnoreSameKey: input.ignoreSameKey,\n      ContentMD5: input.contentMD5,\n      Object: input.key,\n    },\n    {\n      needMd5: true,\n    }\n  );\n  return res;\n}\n", "import TosClientError from '../../TosClientError';\nimport { covertCamelCase2Kebab, normalizeProxy } from '../../utils';\nimport TOSBase from '../base';\nimport { validateObjectName } from './utils';\n\nexport interface GetPreSignedUrlInput {\n  bucket?: string;\n  key: string;\n  /**\n   * default: 'GET'\n   */\n  method?: 'GET' | 'PUT';\n  /**\n   * unit: second, default: 1800\n   */\n  expires?: number;\n  alternativeEndpoint?: string;\n  response?: {\n    contentType?: string;\n    contentDisposition?: string;\n  };\n  versionId?: string;\n  query?: Record<string, string>;\n  /**\n   * default: false\n   * if set true. generate domain will direct use `endpoint` or `alternativeEndpoint`.\n   */\n  isCustomDomain?: boolean;\n}\n\nexport function getPreSignedUrl(\n  this: TOSBase,\n  input: GetPreSignedUrlInput | string\n) {\n  validateObjectName(input);\n  const normalizedInput = typeof input === 'string' ? { key: input } : input;\n  const endpoint = normalizedInput.alternativeEndpoint || this.opts.endpoint;\n  const subdomain =\n    normalizedInput.alternativeEndpoint || normalizedInput.isCustomDomain\n      ? false\n      : true;\n  const bucket = normalizedInput.bucket || this.opts.bucket || '';\n  if (subdomain && !bucket) {\n    throw new TosClientError('Must provide bucket param');\n  }\n\n  const [newHost, newPath, signingPath] = (() => {\n    const encodedKey = encodeURIComponent(normalizedInput.key);\n    const objectKeyPath = normalizedInput.key\n      .split('/')\n      .map((it) => encodeURIComponent(it))\n      .join('/');\n\n    if (subdomain) {\n      return [`${bucket}.${endpoint}`, `/${objectKeyPath}`, `/${encodedKey}`];\n    }\n    return [endpoint, `/${objectKeyPath}`, `/${encodedKey}`];\n  })();\n\n  const nextQuery: Record<string, any> = normalizedInput.query || {};\n  const setOneQuery = (k: string, v?: string) => {\n    if (nextQuery[k] == null && v != null) {\n      nextQuery[k] = v;\n    }\n  };\n  const response = normalizedInput.response || {};\n  Object.keys(response).forEach((_key) => {\n    const key = _key as keyof typeof response;\n    const kebabKey = covertCamelCase2Kebab(key);\n    setOneQuery(`response-${kebabKey}`, response[key]);\n  });\n  if (normalizedInput.versionId) {\n    setOneQuery('versionId', normalizedInput.versionId);\n  }\n\n  const query = this.getSignatureQuery({\n    bucket,\n    method: normalizedInput.method || 'GET',\n    path: signingPath,\n    endpoint,\n    subdomain,\n    expires: normalizedInput.expires || 1800,\n    query: nextQuery,\n  });\n\n  const normalizedProxy = normalizeProxy(this.opts.proxy);\n  let baseURL = `http${this.opts.secure ? 's' : ''}://${newHost}`;\n  if (normalizedProxy?.url) {\n    // if `baseURL` ends with '/'，we filter it.\n    // because `newPath` starts with '/'\n    baseURL = normalizedProxy.url.replace(/\\/+$/g, '');\n    if (normalizedProxy?.needProxyParams) {\n      query['x-proxy-tos-host'] = newHost;\n    }\n  }\n\n  const queryStr = Object.keys(query)\n    .map((key) => {\n      return `${encodeURIComponent(key)}=${encodeURIComponent(query[key])}`;\n    })\n    .join('&');\n\n  return `${baseURL}${newPath}?${queryStr}`;\n}\n\nexport default getPreSignedUrl;\n", "import TOSBase from '../base';\n\nexport interface DeleteObjectInput {\n  bucket?: string;\n  key: string;\n  versionId?: string;\n  /**@private unstable */\n  skipTrash?: string;\n  /**@private unstable */\n  recursive?: string;\n}\n\nexport interface DeleteObjectOutput {\n  [key: string]: string | undefined;\n  ['x-tos-delete-marker']: string;\n  ['x-tos-version-id']: string;\n}\n\nexport async function deleteObject(\n  this: TOSBase,\n  input: DeleteObjectInput | string\n) {\n  const normalizedInput = typeof input === 'string' ? { key: input } : input;\n  const query: Record<string, any> = {};\n  if (normalizedInput.versionId) {\n    query.versionId = normalizedInput.versionId;\n  }\n  if (normalizedInput.skipTrash) {\n    query.skipTrash = normalizedInput.skipTrash;\n  }\n  if (normalizedInput.recursive) {\n    query.recursive = normalizedInput.recursive;\n  }\n  const res = await this._fetchObject<DeleteObjectOutput>(\n    input,\n    'DELETE',\n    query,\n    {},\n    {},\n    { handleResponse: (res) => res.headers }\n  );\n  return res;\n}\n\nexport default deleteObject;\n", "import { fillRequestHeaders, normalizeHeadersKey } from '../../utils';\nimport TOSBase from '../base';\n\nexport interface RenameObjectInput {\n  bucket?: string;\n  key: string;\n  newKey: string;\n  recursiveMkdir?: boolean;\n  forbidOverwrite?: boolean;\n  headers?: {\n    [key: string]: string | undefined;\n  };\n}\n\nexport async function renameObject(this: TOSBase, input: RenameObjectInput) {\n  input.headers = input.headers || {};\n  fillRequestHeaders(input, ['recursiveMkdir', 'forbidOverwrite']);\n  return this._fetchObject<undefined>(\n    input,\n    'PUT',\n    { rename: '', name: input.newKey },\n    input.headers,\n    ''\n  );\n}\n\nexport default renameObject;\n", "import { makeArrayProp } from '../../utils';\nimport TOSBase from '../base';\n\nexport interface DeleteMultiObjectsInput {\n  bucket?: string;\n  /**\n   * default: false\n   */\n  quiet?: boolean;\n  objects: {\n    key: string;\n    versionId?: string;\n  }[];\n  /**@private unstable */\n  skipTrash?: string;\n  /**@private unstable */\n  recursive?: string;\n}\n\nexport interface DeleteMultiObjectsOutput {\n  Deleted: {\n    Key: string;\n    VersionId: string;\n    DeleteMarker?: boolean;\n    DeleteMarkerVersionId?: string;\n  }[];\n\n  Error: {\n    Code: string;\n    Message: string;\n    Key: string;\n    VersionId: string;\n  }[];\n}\n\nexport async function deleteMultiObjects(\n  this: TOSBase,\n  input: DeleteMultiObjectsInput\n) {\n  const body = {\n    Quiet: input.quiet,\n    Objects: input.objects.map((it) => ({\n      Key: it.key,\n      VersionId: it.versionId,\n    })),\n  };\n\n  const query: Record<string, string> = {\n    delete: '',\n  };\n\n  if (input.skipTrash) {\n    query.skipTrash = input.skipTrash;\n  }\n\n  if (input.recursive) {\n    query.recursive = input.recursive;\n  }\n\n  const res = await this.fetchBucket<DeleteMultiObjectsOutput>(\n    input.bucket,\n    'POST',\n    query,\n    {},\n    body\n  );\n\n  const arrayProp = makeArrayProp(res.data);\n  arrayProp('Deleted');\n  arrayProp('Error');\n\n  return res;\n}\n\nexport default deleteMultiObjects;\n", "import { Acl, AclInterface } from '../../../interface';\nimport { fillRequestHeaders, makeArrayProp, normalizeHeadersKey } from '../../../utils';\nimport TOSBase from '../../base';\n\nexport interface GetObjectAclInput {\n  bucket?: string;\n  key: string;\n  versionId?: string;\n}\n\nexport type ObjectAclBody = AclInterface & {\n  BucketOwnerEntrusted?: boolean;\n  IsDefault?: boolean;\n};\n\nexport type GetObjectAclOutput = ObjectAclBody;\n\nexport async function getObjectAcl(\n  this: TOSBase,\n  input: GetObjectAclInput | string\n) {\n  const normalizedInput = typeof input === 'string' ? { key: input } : input;\n  const query: Record<string, any> = { acl: '' };\n  if (normalizedInput.versionId) {\n    query.versionId = normalizedInput.versionId;\n  }\n\n  const res = await this._fetchObject<GetObjectAclOutput>(input, 'GET', query, {});\n\n  const arrayProp = makeArrayProp(res.data);\n  arrayProp('Grants');\n\n  return res;\n}\n\nexport interface PutObjectAclInput {\n  bucket?: string;\n  key: string;\n  versionId?: string;\n  acl?: Acl;\n  aclBody?: ObjectAclBody;\n  headers?: {\n    [key: string]: string | undefined;\n    'x-tos-acl'?: Acl;\n  };\n}\n\nexport async function putObjectAcl(this: TOSBase, input: PutObjectAclInput) {\n  const headers = (input.headers = normalizeHeadersKey(input.headers));\n  const query: Record<string, any> = { acl: '' };\n  if (input.versionId) {\n    query.versionId = input.versionId;\n  }\n  fillRequestHeaders(input, ['acl']);\n\n  return this._fetchObject<undefined>(\n    input,\n    'PUT',\n    query,\n    headers,\n    input.aclBody\n  );\n}\n", "import TOSBase from '../../base';\n\nexport interface AbortMultipartUploadInput {\n  bucket?: string;\n  key: string;\n  uploadId: string;\n}\n\nexport async function abortMultipartUpload(\n  this: TOSBase,\n  input: AbortMultipartUploadInput\n) {\n  return this._fetchObject<undefined>(\n    input,\n    'DELETE',\n    {\n      uploadId: input.uploadId,\n    },\n    {}\n  );\n}\n", "import { covertCamelCase2Kebab, makeArrayProp } from '../../../utils';\nimport TOSBase from '../../base';\n\nexport interface ListMultipartUploadsInput {\n  bucket?: string;\n  maxUploads?: number;\n  keyMarker?: string;\n  uploadIdMarker?: string;\n  delimiter?: string;\n  encodingType?: string;\n  prefix?: string;\n}\n\nexport interface ListMultipartUploadsOutput {\n  Uploads: {\n    Key: string;\n    UploadId: string;\n    StorageClass: string;\n    Initiated: string;\n  }[];\n  CommonPrefixes: string[];\n  Delimiter?: string;\n  EncodingType?: string;\n  KeyMarker?: string;\n  NextKeyMarker: string;\n  MaxUploads?: string;\n  UploadIdMarker?: string;\n  NextUploadIdMarker: string;\n  Prefix?: string;\n  IsTruncated: boolean;\n  Bucket: string;\n}\n\nexport async function listMultipartUploads(\n  this: TOSBase,\n  input: ListMultipartUploadsInput = {}\n) {\n  const { bucket, ...nextQuery } = input;\n  const ret = await this.fetchBucket<ListMultipartUploadsOutput>(\n    input.bucket,\n    'GET',\n    {\n      uploads: '',\n      ...covertCamelCase2Kebab(nextQuery),\n    },\n    {}\n  );\n\n  const arrayProp = makeArrayProp(ret.data);\n  arrayProp('Uploads');\n  arrayProp('CommonPrefixes');\n\n  return ret;\n}\n", "import TOSBase from '../base';\nimport {\n  checkCRC64WithHeaders,\n  fillRequestHeaders,\n  normalizeHead<PERSON><PERSON>ey,\n  safeAwait,\n} from '../../utils';\nimport { Acl, DataTransferStatus, DataTransferType } from '../../interface';\nimport { IRateLimiter } from '../../universal/rate-limiter';\nimport { getNewBodyConfig, getSize } from './utils';\nimport { StorageClassType } from '../../TosExportEnum';\nimport { retryNamespace } from '../../axios';\nimport TosClientError from '../../TosClientError';\nimport { combineCrc64 } from '../../universal/crc';\n\nexport interface AppendObjectInput {\n  bucket?: string;\n  key: string;\n  offset: number;\n  // body is empty buffer if it's falsy\n  body?: File | Blob | Buffer | NodeJS.ReadableStream;\n\n  // must provide preHashCrc64ecma if enableCRC is true and offset is non-zero\n  preHashCrc64ecma?: string;\n\n  /**\n   * unit: bit/s\n   * server side traffic limit\n   **/\n  trafficLimit?: number;\n  /**\n   * only works for nodejs environment\n   */\n  rateLimiter?: IRateLimiter;\n\n  contentLength?: number;\n  cacheControl?: string;\n  contentDisposition?: string;\n  contentEncoding?: string;\n  contentLanguage?: string;\n  contentType?: string;\n  expires?: Date;\n\n  acl?: Acl;\n  grantFullControl?: string;\n  grantRead?: string;\n  grantReadAcp?: string;\n  grantWriteAcp?: string;\n\n  meta?: Record<string, string>;\n  websiteRedirectLocation?: string;\n  storageClass?: StorageClassType;\n\n  dataTransferStatusChange?: (status: DataTransferStatus) => void;\n\n  /**\n   * the simple progress feature\n   * percent is [0, 1].\n   *\n   * since appendObject is stateless, so if `appendObject` fail and you retry it,\n   * `percent` will start from 0 again rather than from the previous value.\n   * if you need `percent` start from the previous value, you can use `uploadFile` instead.\n   */\n  progress?: (percent: number) => void;\n\n  headers?: {\n    [key: string]: string | undefined;\n    'Cache-Control'?: string;\n    'x-tos-acl'?: Acl;\n    'x-tos-grant-full-control'?: string;\n    'x-tos-grant-read'?: string;\n    'x-tos-grant-read-acp'?: string;\n    'x-tos-grant-write-acp'?: string;\n    'x-tos-website-redirect-location'?: string;\n    'x-tos-storage-class'?: string;\n  };\n}\n\nexport interface AppendObjectOutput {\n  nextAppendOffset: number;\n  hashCrc64ecma: string;\n  'x-tos-version-id'?: string;\n  'x-tos-hash-crc64ecma'?: string;\n  'x-tos-next-append-offset'?: string;\n}\n\nexport async function appendObject(\n  this: TOSBase,\n  input: AppendObjectInput | string\n) {\n  const normalizedInput = (input = this.normalizeObjectInput(input));\n  const headers = (input.headers = normalizeHeadersKey(input.headers));\n  fillRequestHeaders(input, [\n    'contentLength',\n    'cacheControl',\n    'contentDisposition',\n    'contentEncoding',\n    'contentLanguage',\n    'contentType',\n    'expires',\n    'acl',\n    'grantFullControl',\n    'grantRead',\n    'grantReadAcp',\n    'grantWriteAcp',\n    'meta',\n    'websiteRedirectLocation',\n    'storageClass',\n    'trafficLimit',\n  ]);\n  this.setObjectContentTypeHeader(input, headers);\n\n  const totalSize = getSize(input.body, headers);\n  const totalSizeValid = totalSize != null;\n  if (!totalSizeValid) {\n    throw new TosClientError(\n      `appendObject needs to know the content length in advance`\n    );\n  }\n  headers['content-length'] = headers['content-length'] || `${totalSize}`;\n\n  if (this.opts.enableCRC && input.offset !== 0 && !input.preHashCrc64ecma) {\n    throw new TosClientError(\n      'must provide preHashCrc64ecma if enableCRC is true and offset is non-zero'\n    );\n  }\n\n  let consumedBytes = 0;\n  const { dataTransferStatusChange, progress } = input;\n  const triggerDataTransfer = (\n    type: DataTransferType,\n    rwOnceBytes: number = 0\n  ) => {\n    // request cancel will make rwOnceBytes < 0 in browser\n    if (!totalSizeValid || rwOnceBytes < 0) {\n      return;\n    }\n    if (!dataTransferStatusChange && !progress) {\n      return;\n    }\n    consumedBytes += rwOnceBytes;\n\n    dataTransferStatusChange?.({\n      type,\n      rwOnceBytes,\n      consumedBytes,\n      totalBytes: totalSize,\n    });\n    const progressValue = (() => {\n      if (totalSize === 0) {\n        if (type === DataTransferType.Succeed) {\n          return 1;\n        }\n        return 0;\n      }\n      return consumedBytes / totalSize;\n    })();\n    if (progressValue === 1) {\n      if (type === DataTransferType.Succeed) {\n        progress?.(progressValue);\n      } else {\n        // not exec progress\n      }\n    } else {\n      progress?.(progressValue);\n    }\n  };\n\n  const bodyConfig = await getNewBodyConfig({\n    body: input.body,\n    dataTransferCallback: (n) => triggerDataTransfer(DataTransferType.Rw, n),\n    makeRetryStream: undefined,\n    enableCRC: this.opts.enableCRC,\n    rateLimiter: input.rateLimiter,\n  });\n\n  triggerDataTransfer(DataTransferType.Started);\n  const task = async () => {\n    const res = await this._fetchObject<AppendObjectOutput>(\n      input,\n      'POST',\n      { append: '', offset: normalizedInput.offset },\n      headers,\n      bodyConfig.body || '',\n      {\n        handleResponse: (res) => ({\n          ...res.headers,\n          nextAppendOffset: +res.headers['x-tos-next-append-offset'],\n          hashCrc64ecma: res.headers['x-tos-hash-crc64ecma'],\n        }),\n        axiosOpts: {\n          [retryNamespace]: {\n            beforeRetry: () => {\n              consumedBytes = 0;\n              bodyConfig.beforeRetry?.();\n            },\n            makeRetryStream: bodyConfig.makeRetryStream,\n          },\n          onUploadProgress: (event) => {\n            triggerDataTransfer(\n              DataTransferType.Rw,\n              event.loaded - consumedBytes\n            );\n          },\n        },\n      }\n    );\n    if (this.opts.enableCRC && bodyConfig.crc) {\n      const appendObjectCrc = combineCrc64(\n        normalizedInput.preHashCrc64ecma || '0',\n        bodyConfig.crc.getCrc64(),\n        totalSize\n      );\n      checkCRC64WithHeaders(appendObjectCrc, res.headers);\n    }\n    return res;\n  };\n  const [err, res] = await safeAwait(task());\n\n  if (err || !res) {\n    triggerDataTransfer(DataTransferType.Failed);\n    throw err;\n  }\n\n  triggerDataTransfer(DataTransferType.Succeed);\n  return res;\n}\n\nexport default appendObject;\n", "import { fillRequestHeaders, normalizeHeadersKey } from '../../utils';\nimport TOSBase from '../base';\n\nexport interface SetObjectMetaInput {\n  bucket?: string;\n  key: string;\n  versionId?: string;\n\n  // object meta data\n  cacheControl?: string;\n  contentDisposition?: string;\n  contentEncoding?: string;\n  contentLanguage?: string;\n  contentType?: string;\n  expires?: Date;\n  meta?: Record<string, string>;\n\n  headers?: {\n    [key: string]: string | undefined;\n    'Cache-Control'?: string;\n    'Content-Disposition'?: string;\n    Expires?: string;\n    'Content-Type'?: string;\n    'Content-Language'?: string;\n  };\n}\n\nexport async function setObjectMeta(\n  this: TOSBase,\n  input: SetObjectMetaInput | string\n) {\n  const normalizedInput = typeof input === 'string' ? { key: input } : input;\n  const headers = (normalizedInput.headers = normalizeHeadersKey(\n    normalizedInput.headers\n  ));\n  fillRequestHeaders(normalizedInput, [\n    'cacheControl',\n    'contentDisposition',\n    'contentEncoding',\n    'contentLanguage',\n    'contentType',\n    'expires',\n    'meta',\n  ]);\n  const query: Record<string, any> = { metadata: '' };\n  if (normalizedInput.versionId) {\n    query.versionId = normalizedInput.versionId;\n  }\n\n  return this._fetchObject<undefined>(input, 'POST', query, headers);\n}\n\nexport default setObjectMeta;\n", "import TOSBase from '../base';\nimport { parse, stringify, hmacSha256 } from '../../universal/crypto';\nimport TosClientError from '../../TosClientError';\nimport { validateObjectName } from './utils';\n\nexport type PostSignatureCondition =\n  | {\n      [key: string]: string;\n    }\n  | ['eq', string, string]\n  | ['starts-with', string, string]\n  | ['content-length-range', number, number];\n\nexport interface CalculatePostSignatureInput {\n  bucket?: string;\n  key: string;\n  // unit: seconds, default: 3600(1 hour)\n  expiresIn?: number;\n  fields?: Record<string, unknown>;\n  conditions?: PostSignatureCondition[];\n}\n\nexport async function calculatePostSignature(\n  this: TOSBase,\n  input: CalculatePostSignatureInput | string\n) {\n  validateObjectName(input);\n  input = this.normalizeObjectInput(input);\n  const { expiresIn = 3600, key } = input;\n  const bucket = input.bucket || this.opts.bucket;\n  const fields = { ...input.fields };\n  const conditions = [...(input.conditions || [])];\n\n  if (!bucket) {\n    throw new TosClientError('Must provide bucket param');\n  }\n\n  const accessKeySecret = this.opts.accessKeySecret;\n  const date = new Date();\n  const expirationDateStr = getDateTimeStr({\n    date: new Date(date.valueOf() + expiresIn * 1000),\n    type: 'ISO',\n  });\n  const dateStr = getDateTimeStr();\n  const date8Str = dateStr.substring(0, 8);\n  const service = 'tos';\n  const requestStr = 'request';\n\n  const kDate = hmacSha256(accessKeySecret, date8Str);\n  const kRegion = hmacSha256(kDate, this.opts.region);\n  const kService = hmacSha256(kRegion, service);\n  const signingKey = hmacSha256(kService, requestStr);\n\n  const credential = [\n    this.opts.accessKeyId,\n    date8Str,\n    this.opts.region,\n    service,\n    requestStr,\n  ].join('/');\n\n  const addedInForm: Record<string, string> = {\n    key,\n    'x-tos-algorithm': 'TOS4-HMAC-SHA256',\n    'x-tos-date': dateStr,\n    'x-tos-credential': credential,\n  };\n  if (this.opts.stsToken) {\n    addedInForm['x-tos-security-token'] = this.opts.stsToken;\n  }\n\n  conditions.push({ bucket });\n  Object.entries(addedInForm).forEach(([key, value]) => {\n    fields[key] = value;\n  });\n  Object.entries(fields).forEach(([key, value]) => {\n    conditions.push({ [key]: `${value}` });\n  });\n\n  const policy = {\n    expiration: expirationDateStr,\n    conditions,\n  };\n  const policyStr = JSON.stringify(policy);\n  const policyBase64 = stringify(parse(policyStr, 'utf-8'), 'base64');\n  const signature = hmacSha256(signingKey, policyBase64, 'hex');\n\n  fields.policy = policyBase64;\n  fields['x-tos-signature'] = signature;\n\n  return fields;\n}\n\n/**\n *\n * Z for 20130728T000000Z\n * ISO for 2007-12-01T12:00:00.000Z\n * @param opt\n * @returns\n */\nfunction getDateTimeStr(opt?: { date?: Date; type?: 'Z' | 'ISO' }) {\n  const { date = new Date(), type = 'Z' } = opt || {};\n  if (type === 'ISO') {\n    return date.toISOString();\n  }\n\n  const dateTime =\n    date.toISOString().replace(/\\..+/, '').replace(/-/g, '').replace(/:/g, '') +\n    'Z';\n\n  return dateTime;\n}\n\nexport default calculatePostSignature;\n", "import TosServerError from './TosServerError';\nimport { getNormalDataFromError } from './utils';\n\nconst defaultEmptyMethodMap: Record<string, boolean> = {\n  getBucketCustomDomain: true,\n  getBucketIntelligenttiering: true,\n  getBucketInventory: true,\n  listBucketInventory: true,\n  getBucketMirrorBack: true,\n  getBucketNotification: true,\n  getBucketPolicy: true,\n  getBucketRealTimeLog: true,\n  getBucketReplication: true,\n  getBucketTagging: true,\n  getBucketWebsite: true,\n};\n\nexport function handleEmptyServerError<T>(\n  err: Error | TosServerError | unknown,\n  opts: {\n    defaultResponse: T;\n    enableCatchEmptyServerError?: boolean;\n    methodKey: string;\n  }\n) {\n  const { enableCatchEmptyServerError, methodKey, defaultResponse } = opts;\n  if (err instanceof TosServerError) {\n    if (enableCatchEmptyServerError) {\n      if (err.statusCode === 404) {\n        return getNormalDataFromError(defaultResponse, err);\n      }\n    }\n    // 在本次更改前已经有一些接口对404做了catch处理，在不显式声明enableCatchEmptyServerError的情况下，保持原样，不做break change\n    else if (enableCatchEmptyServerError === undefined) {\n      if (err.statusCode === 404 && defaultEmptyMethodMap[methodKey]) {\n        return getNormalDataFromError(defaultResponse, err);\n      }\n    }\n  }\n  throw err;\n}\n", "import { makeArrayProp } from '../../utils';\nimport { handleEmptyServerError } from '../../handleEmptyServerError';\nimport TOSBase, { TosResponse } from '../base';\n\nexport interface BucketPolicyStatement {\n  Sid: string;\n  Effect: 'Allow' | 'Deny';\n  Action?: string | string[];\n  NotAction?: string | string[];\n  Condition?: {\n    [key in string]: {\n      [key in string]: string[];\n    };\n  };\n  Principal?: string[];\n  NotPrincipal?: string[];\n  Resource?: string | string[];\n  NotResource?: string | string[];\n}\n\nexport interface GetBucketPolicyOutput {\n  Statement: BucketPolicyStatement[];\n  Version: string;\n}\n\ninterface PutBucketPolicyInputPolicy\n  extends Omit<GetBucketPolicyOutput, 'Version'> {\n  Version?: string;\n}\n\nexport interface PutBucketPolicyInput {\n  bucket?: string;\n  policy: PutBucketPolicyInputPolicy;\n}\n\nexport async function putBucketPolicy(\n  this: TOSBase,\n  input: PutBucketPolicyInput\n) {\n  if (\n    (this.opts.enableOptimizeMethodBehavior ||\n      this.opts.enableOptimizeMethodBehavior === undefined) &&\n    !input.policy.Statement.length\n  ) {\n    return deleteBucketPolicy.call(this, input.bucket);\n  }\n\n  const res = await this.fetchBucket(\n    input.bucket,\n    'PUT',\n    { policy: '' },\n    {},\n    input.policy,\n    { needMd5: true }\n  );\n  return res;\n}\n\nexport async function getBucketPolicy(\n  this: TOSBase,\n  bucket?: string\n): Promise<TosResponse<GetBucketPolicyOutput>> {\n  try {\n    const res = await this.fetchBucket<GetBucketPolicyOutput>(\n      bucket,\n      'GET',\n      {\n        policy: '',\n      },\n      {}\n    );\n    res.data.Statement.forEach((it: any) => {\n      const arrayProp = makeArrayProp(it);\n\n      Object.keys(it.Condition || {}).forEach((key) => {\n        Object.keys(it.Condition[key]).forEach((key2) => {\n          arrayProp(`Condition[\"${key}\"][\"${key2}\"]`);\n        });\n      });\n    });\n    return res;\n  } catch (error) {\n    return handleEmptyServerError<GetBucketPolicyOutput>(error, {\n      enableCatchEmptyServerError: this.opts.enableOptimizeMethodBehavior,\n      methodKey: 'getBucketPolicy',\n      defaultResponse: {\n        Statement: [],\n        Version: '2012-10-17',\n      },\n    });\n  }\n}\n\nexport async function deleteBucketPolicy(this: TOSBase, bucket?: string) {\n  return this.fetchBucket(bucket, 'DELETE', { policy: '' }, {});\n}\n", "import { VersioningStatusType } from '../../TosExportEnum';\nimport TOSBase from '../base';\n\n// for backward compatibility\nexport { VersioningStatusType as BucketVersioningStatus };\n\nexport type PutBucketVersioningInputStatus =\n  | VersioningStatusType.Enable\n  | VersioningStatusType.Enabled\n  | VersioningStatusType.Suspended;\n\nexport interface GetBucketVersioningOutput {\n  Status: VersioningStatusType;\n}\n\nexport interface PutBucketVersioningInput {\n  bucket?: string;\n  status: PutBucketVersioningInputStatus;\n}\n\nexport async function getBucketVersioning(this: TOSBase, bucket?: string) {\n  return this.fetchBucket<GetBucketVersioningOutput>(\n    bucket,\n    'GET',\n    { versioning: '' },\n    {}\n  );\n}\n\nexport async function putBucketVersioning(\n  this: TOSBase,\n  input: PutBucketVersioningInput\n) {\n  return this.fetchBucket(\n    input.bucket,\n    'PUT',\n    { versioning: '' },\n    {},\n    {\n      Status: input.status,\n    }\n  );\n}\n", "import TosClientError from '../../TosClientError';\nimport { obj2QueryStr } from '../../utils';\nimport TOSBase from '../base';\n\nexport interface PreSignedPolicyURLInput {\n  bucket?: string;\n  /**\n   * unit: s\n   * default value: 3600\n   * range is: [1, 604800]\n   */\n  expires?: number;\n\n  conditions: PolicySignatureCondition[];\n\n  alternativeEndpoint?: string;\n  /**\n   * default: false\n   * if set true. generate domain will direct use `endpoint` or `alternativeEndpoint`.\n   */\n  isCustomDomain?: boolean;\n}\n\nexport interface PreSignedPolicyURLOutput {\n  getSignedURLForList(additionalQuery?: Record<string, string>): string;\n\n  // since conditions maybe includes multi exact key, so key isn't predictable\n  getSignedURLForGetOrHead(\n    key: string,\n    additionalQuery?: Record<string, string>\n  ): string;\n\n  signedQuery: string;\n}\n\nexport interface PolicySignatureCondition {\n  key: 'key';\n  value: string;\n  operator?: 'eq' | 'starts-with';\n}\n\ninterface NormalizedInput {\n  bucket: string;\n  /**\n   * unit: s\n   * default value: 3600\n   * range is: [1, 604800]\n   */\n  expires: number;\n\n  conditions: NormalizedPolicySignatureCondition[];\n\n  alternativeEndpoint?: string;\n}\n\ntype NormalizedPolicySignatureCondition = [\n  'eq' | 'starts-with',\n  '$key' | '$bucket',\n  string\n];\n\nexport function preSignedPolicyURL(\n  this: TOSBase,\n  input: PreSignedPolicyURLInput\n): PreSignedPolicyURLOutput {\n  const normalizedInput = normalizeInput.call(this, input);\n\n  validateConditions(input.conditions);\n\n  const endpoint =\n    input.alternativeEndpoint ||\n    (input.isCustomDomain\n      ? this.opts.endpoint\n      : `${normalizedInput.bucket}.${this.opts.endpoint}`);\n\n  const baseURL = `http${this.opts.secure ? 's' : ''}://${endpoint}`;\n\n  const query = this.getSignatureQuery({\n    bucket: normalizedInput.bucket,\n    expires: normalizedInput.expires,\n    policy: {\n      conditions: normalizedInput.conditions,\n    },\n  });\n\n  const queryStr = obj2QueryStr(query);\n\n  const getSignedURLForList: PreSignedPolicyURLOutput['getSignedURLForList'] = (\n    additionalQuery\n  ) => {\n    const str2 = obj2QueryStr(additionalQuery);\n    const q = [queryStr, str2].filter(Boolean).join('&');\n    return `${baseURL}?${q}`;\n  };\n  const getSignedURLForGetOrHead: PreSignedPolicyURLOutput['getSignedURLForGetOrHead'] =\n    (key, additionalQuery) => {\n      const str2 = obj2QueryStr(additionalQuery);\n      const q = [queryStr, str2].filter(Boolean).join('&');\n      // keep   '/'\n      const keyPath = key\n        .split('/')\n        .map((it) => encodeURIComponent(it))\n        .join('/');\n      return `${baseURL}/${keyPath}?${q}`;\n    };\n  return {\n    getSignedURLForList,\n    getSignedURLForGetOrHead,\n    signedQuery: queryStr,\n  };\n}\n\nfunction normalizeInput(\n  this: TOSBase,\n  input: PreSignedPolicyURLInput\n): NormalizedInput {\n  const actualBucket = input.bucket || this.opts.bucket;\n  const defaultExpires = 3600;\n\n  if (!actualBucket) {\n    throw new TosClientError('Must provide bucket param');\n  }\n\n  validateConditions(input.conditions);\n  const normalizedConditions: NormalizedPolicySignatureCondition[] =\n    input.conditions.map((it) => [it.operator || 'eq', '$key', it.value]);\n  normalizedConditions.push(['eq', '$bucket', actualBucket]);\n\n  return {\n    bucket: actualBucket,\n    expires: input.expires || defaultExpires,\n    conditions: normalizedConditions,\n  };\n}\n\nfunction validateConditions(conditions: PolicySignatureCondition[]) {\n  if (conditions.length < 1) {\n    throw new TosClientError(\n      'The `conditions` field of `PreSignedPolicyURLInput` must has one item at least'\n    );\n  }\n\n  for (const it of conditions) {\n    if (it.key !== 'key') {\n      throw new TosClientError(\n        \"The `key` field of `PolicySignatureCondition` must be `'key'`\"\n      );\n    }\n\n    if (it.operator && it.operator !== 'eq' && it.operator !== 'starts-with') {\n      throw new TosClientError(\n        \"The `operator` field of `PolicySignatureCondition` must be `'eq'` or `'starts-with'`\"\n      );\n    }\n  }\n}\n", "import TOSBase from '../base';\n\nexport interface GetBucketLocationInput {\n  bucket: string;\n}\n\nexport interface GetBucketLocationOutput {\n  ExtranetEndpoint: string;\n  IntranetEndpoint: string;\n  Region: string;\n}\n\nexport async function getBucketLocation(\n  this: TOSBase,\n  input: GetBucketLocationInput\n) {\n  const { bucket } = input;\n\n  return this.fetchBucket<GetBucketLocationOutput>(\n    bucket,\n    'GET',\n    { location: '' },\n    {}\n  );\n}\n", "import { HttpMethodType } from '../../TosExportEnum';\nimport { handleEmptyServerError } from '../../handleEmptyServerError';\nimport TOSBase from '../base';\n\nexport interface CORSRule {\n  AllowedOrigins: string[];\n  AllowedMethods: HttpMethodType[];\n  AllowedHeaders: string[];\n  ExposeHeaders: string[];\n  MaxAgeSeconds: number;\n  ResponseVary?: boolean;\n}\n\nexport interface GetBucketCORSInput {\n  bucket: string;\n}\n\nexport interface GetBucketCORSOutput {\n  CORSRules: CORSRule[];\n}\n\nexport async function getBucketCORS(this: TOSBase, input: GetBucketCORSInput) {\n  try {\n    const { bucket } = input;\n\n    return await this.fetchBucket<GetBucketCORSOutput>(\n      bucket,\n      'GET',\n      { cors: '' },\n      {}\n    );\n  } catch (error) {\n    return handleEmptyServerError<GetBucketCORSOutput>(error, {\n      defaultResponse: { CORSRules: [] },\n      enableCatchEmptyServerError: this.opts.enableOptimizeMethodBehavior,\n      methodKey: 'getBucketCORS',\n    });\n  }\n}\n\nexport interface PutBucketCORSInput {\n  bucket: string;\n  CORSRules: CORSRule[];\n}\n\nexport interface PutBucketCORSOutput {}\n\nexport async function putBucketCORS(this: TOSBase, input: PutBucketCORSInput) {\n  const { bucket, CORSRules } = input;\n  if (this.opts.enableOptimizeMethodBehavior && !CORSRules.length) {\n    return deleteBucketCORS.call(this, { bucket });\n  }\n  return this.fetchBucket<PutBucketCORSOutput>(\n    bucket,\n    'PUT',\n    { cors: '' },\n    {},\n    { CORSRules }\n  );\n}\n\nexport interface DeleteBucketCORSInput {\n  bucket: string;\n}\n\nexport interface DeleteBucketCORSOutput {}\n\nexport async function deleteBucketCORS(\n  this: TOSBase,\n  input: DeleteBucketCORSInput\n) {\n  const { bucket } = input;\n\n  return this.fetchBucket<DeleteBucketCORSOutput>(\n    bucket,\n    'DELETE',\n    { cors: '' },\n    {}\n  );\n}\n", "import { StorageClassType } from '../../TosExportEnum';\nimport TOSBase from '../base';\nimport { fillRequestHeaders, normalizeHeadersKey } from '../../utils';\nimport { handleEmptyServerError } from '../../handleEmptyServerError';\n\ninterface LifecycleRule {\n  ID?: string;\n  Prefix?: string;\n  Status: 'Enabled' | 'Disabled';\n  Filter?: {\n    GreaterThanIncludeEqual?: 'Enabled' | 'Disabled';\n    LessThanIncludeEqual?: 'Enabled' | 'Disabled';\n    /** unit bit */\n    ObjectSizeGreaterThan?: number;\n    /** unit bit */\n    ObjectSizeLessThan?: number;\n  };\n  Expiration?: { Date?: string; Days?: number };\n  Days?: number;\n  Date?: string;\n  NoncurrentVersionExpiration?: {\n    NoncurrentDays?: number;\n    NoncurrentDate?: string;\n  };\n  AbortIncompleteMultipartUpload?: { DaysAfterInitiation?: number };\n  DaysAfterInitiation?: number;\n  Transitions?: {\n    StorageClass: StorageClassType;\n    Days?: number;\n    Date?: string;\n  }[];\n  /**\n   * @private unstable\n   */\n  AccessTimeTransitions?: {\n    StorageClass: StorageClassType;\n    Days?: number;\n  }[];\n  /**\n   * @private unstable\n   */\n  NoncurrentVersionAccessTimeTransitions?: {\n    StorageClass: StorageClassType;\n    NoncurrentDays?: number;\n  }[];\n  NoncurrentVersionTransitions?: {\n    StorageClass?: StorageClassType;\n    NoncurrentDays?: number;\n    NoncurrentDate?: string;\n  }[];\n  Tags?: {\n    Key?: string;\n    Value?: string;\n  }[];\n}\n\nexport interface PutBucketLifecycleInput {\n  bucket: string;\n  rules: LifecycleRule[];\n  allowSameActionOverlap?: boolean;\n}\n\nexport interface PutBucketLifecycleOutput {}\n\nexport async function putBucketLifecycle(\n  this: TOSBase,\n  input: PutBucketLifecycleInput\n) {\n  const { bucket, rules } = input;\n  if (this.opts.enableOptimizeMethodBehavior && !rules.length) {\n    return deleteBucketLifecycle.call(this, { bucket });\n  }\n\n  const headers = {};\n  fillRequestHeaders({ ...input, headers }, ['allowSameActionOverlap']);\n\n  return this.fetchBucket<PutBucketLifecycleOutput>(\n    bucket,\n    'PUT',\n    { lifecycle: '' },\n    headers,\n    {\n      Rules: rules,\n    }\n  );\n}\n\nexport interface GetBucketLifecycleInput {\n  bucket: string;\n}\n\nexport interface GetBucketLifecycleOutput {\n  Rules: LifecycleRule[];\n  AllowSameActionOverlap?: boolean;\n}\n\nexport async function getBucketLifecycle(\n  this: TOSBase,\n  input: GetBucketLifecycleInput\n) {\n  try {\n    const { bucket } = input;\n\n    return await this.fetchBucket<GetBucketLifecycleOutput>(\n      bucket,\n      'GET',\n      { lifecycle: '' },\n      {},\n      {},\n      {\n        handleResponse: (res) => {\n          return {\n            AllowSameActionOverlap:\n              res.headers['x-tos-allow-same-action-overlap'],\n            Rules: res.data.Rules,\n          };\n        },\n      }\n    );\n  } catch (error) {\n    return handleEmptyServerError<GetBucketLifecycleOutput>(error, {\n      enableCatchEmptyServerError: this.opts.enableOptimizeMethodBehavior,\n      methodKey: 'getBucketLifecycle',\n      defaultResponse: {\n        Rules: [],\n      },\n    });\n  }\n}\n\nexport interface DeleteBucketLifecycleInput {\n  bucket: string;\n}\n\nexport interface DeleteBucketLifecycleOutput {}\n\nexport async function deleteBucketLifecycle(\n  this: TOSBase,\n  input: DeleteBucketLifecycleInput\n) {\n  const { bucket } = input;\n\n  return this.fetchBucket<DeleteBucketLifecycleOutput>(\n    bucket,\n    'DELETE',\n    { lifecycle: '' },\n    {}\n  );\n}\n", "import TOSBase from '../base';\nimport { hashMd5 } from '../../universal/crypto.browser';\n\nexport interface EncryptionData {\n  Rule: EncryptionDataRule;\n}\nexport interface EncryptionDataRule {\n  ApplyServerSideEncryptionByDefault: {\n    // SSEAlgorithm support 'kms' and 'AES256' and 'sm4'\n    SSEAlgorithm: string;\n    KMSMasterKeyID?: string;\n    /** @private unstable */\n    KMSDataEncryption?: string;\n  };\n}\n\nexport async function putBucketEncryption(\n  this: TOSBase,\n  input: { rule: EncryptionDataRule } & { bucket?: string }\n) {\n  const { bucket, rule } = input;\n\n  return this.fetchBucket(\n    bucket,\n    'PUT',\n    { encryption: '' },\n    {\n      'Content-MD5': hashMd5(\n        JSON.stringify({\n          Rule: rule,\n        }),\n        'base64'\n      ),\n    },\n    {\n      Rule: rule,\n    }\n  );\n}\n\nexport async function getBucketEncryption(\n  this: TOSBase,\n  input: { bucket?: string }\n) {\n  const { bucket } = input;\n\n  return this.fetchBucket<EncryptionData>(\n    bucket,\n    'GET',\n    { encryption: '' },\n    {}\n  );\n}\n\nexport async function deleteBucketEncryption(\n  this: TOSBase,\n  input: { bucket?: string }\n) {\n  const { bucket } = input;\n\n  return this.fetchBucket(bucket, 'DELETE', { encryption: '' }, {});\n}\n", "import { handleEmptyServerError } from '../../handleEmptyServerError';\nimport TOSBase from '../base';\n\nconst CommonQueryKey = 'mirror';\n\nexport interface MirrorBackRule {\n  ID: string;\n  Condition: {\n    HttpCode: number;\n    KeyPrefix?: string;\n    KeySuffix?: string;\n    /** private unstable */\n    AllowHost?: string[];\n    /** private unstable */\n    HttpMethod?: string[];\n  };\n  Redirect: {\n    RedirectType?: 'Mirror' | 'Async';\n    FetchSourceOnRedirect?: boolean;\n    /** @private unstable */\n    FetchSourceOnRedirectWithQuery?: boolean;\n    PublicSource?: {\n      SourceEndpoint: {\n        Primary: string[];\n        Follower?: string[];\n      };\n      FixedEndpoint?: boolean;\n    };\n    /** @private unstable */\n    PrivateSource?: {\n      SourceEndpoint: {\n        Primary: {\n          Endpoint: string;\n          BucketName: string;\n          CredentialProvider: { Role: string };\n        }[];\n      };\n    };\n    PassQuery?: boolean;\n    FollowRedirect?: boolean;\n    MirrorHeader?: {\n      PassAll?: boolean;\n      Pass?: string[];\n      Remove?: string[];\n      /** private unstable */\n      Set?: { Key: string; Value: string }[];\n    };\n\n    Transform?: {\n      WithKeyPrefix?: string;\n      WithKeySuffix?: string;\n      ReplaceKeyPrefix?: {\n        KeyPrefix?: string;\n        ReplaceWith?: string;\n      };\n    };\n  };\n}\n\nexport interface PutBucketMirrorBackInput {\n  bucket: string;\n  rules: MirrorBackRule[];\n}\n\nexport interface PutBucketMirrorBackOutput {}\n\nexport async function putBucketMirrorBack(\n  this: TOSBase,\n  input: PutBucketMirrorBackInput\n) {\n  const { bucket, rules } = input;\n  if (this.opts.enableOptimizeMethodBehavior && !rules.length) {\n    return deleteBucketMirrorBack.call(this, { bucket });\n  }\n\n  return this.fetchBucket<PutBucketMirrorBackOutput>(\n    bucket,\n    'PUT',\n    { [CommonQueryKey]: '' },\n    {},\n    {\n      Rules: rules,\n    }\n  );\n}\n\nexport interface GetBucketMirrorBackInput {\n  bucket: string;\n}\n\nexport interface GetBucketMirrorBackOutput {\n  Rules: MirrorBackRule[];\n}\n\nexport async function getBucketMirrorBack(\n  this: TOSBase,\n  input: GetBucketMirrorBackInput\n) {\n  const { bucket } = input;\n\n  try {\n    return await this.fetchBucket<GetBucketMirrorBackOutput>(\n      bucket,\n      'GET',\n      { [CommonQueryKey]: '' },\n      {}\n    );\n  } catch (error) {\n    return handleEmptyServerError<GetBucketMirrorBackOutput>(error, {\n      enableCatchEmptyServerError: this.opts.enableOptimizeMethodBehavior,\n      methodKey: 'getBucketMirrorBack',\n      defaultResponse: {\n        Rules: [],\n      },\n    });\n  }\n}\n\nexport interface DeleteBucketMirrorBackInput {\n  bucket: string;\n}\n\nexport interface DeleteBucketMirrorBackOutput {}\n\nexport async function deleteBucketMirrorBack(\n  this: TOSBase,\n  input: DeleteBucketMirrorBackInput\n) {\n  const { bucket } = input;\n\n  return this.fetchBucket<DeleteBucketMirrorBackOutput>(\n    bucket,\n    'DELETE',\n    { [CommonQueryKey]: '' },\n    {}\n  );\n}\n", "import { makeArrayProp, normalizeHead<PERSON><PERSON>ey } from '../../utils';\nimport TOSBase from '../base';\n\nconst CommonQueryKey = 'tagging';\n\ninterface TagSet {\n  Tags: {\n    Key: string;\n    Value: string;\n  }[];\n}\n\nexport interface PutObjectTaggingInput {\n  bucket: string;\n  key: string;\n  versionId?: string;\n  tagSet: TagSet;\n}\n\nexport interface PutObjectTaggingOutput {}\n\nexport async function putObjectTagging(\n  this: TOSBase,\n  input: PutObjectTaggingInput\n) {\n  const { tagSet, versionId } = input;\n  const headers = normalizeHeadersKey({\n    versionId,\n  });\n\n  return this._fetchObject<PutObjectTaggingOutput>(\n    input,\n    'PUT',\n    { [CommonQueryKey]: '', ...headers },\n    {},\n    {\n      TagSet: tagSet,\n    }\n  );\n}\n\nexport interface GetObjectTaggingInput {\n  bucket: string;\n  key: string;\n  versionId?: string;\n}\n\nexport interface GetObjectTaggingOutput {\n  TagSet: TagSet;\n}\n\nexport async function getObjectTagging(\n  this: TOSBase,\n  input: GetObjectTaggingInput\n) {\n  const { versionId } = input;\n  const headers = normalizeHeadersKey({\n    versionId,\n  });\n  const res = await this._fetchObject<GetObjectTaggingOutput>(\n    input,\n\n    'GET',\n    { [CommonQueryKey]: '', ...headers },\n    {}\n  );\n  makeArrayProp(res.data.TagSet)('Tags');\n  return res;\n}\n\nexport interface DeleteObjectTaggingInput {\n  bucket: string;\n  key: string;\n  versionId?: string;\n}\n\nexport interface DeleteObjectTaggingOutput {}\n\nexport async function deleteObjectTagging(\n  this: TOSBase,\n  input: DeleteObjectTaggingInput\n) {\n  const { versionId } = input;\n  const headers = normalizeHeadersKey({\n    versionId,\n  });\n\n  return this._fetchObject<DeleteObjectTaggingOutput>(\n    input,\n    'DELETE',\n    { [CommonQueryKey]: '', ...headers },\n    {}\n  );\n}\n", "import {\n  ACLType,\n  StorageClassInheritDirectiveType,\n  StorageClassType,\n} from '../../TosExportEnum';\nimport { handleEmptyServerError } from '../../handleEmptyServerError';\nimport TOSBase from '../base';\n\nconst CommonQueryKey = 'replication';\n\nexport interface ReplicationTag {\n  Key: string;\n  Value: string;\n}\n\nexport interface ReplicationRule {\n  ID: string;\n  Status: string;\n  PrefixSet?: string[];\n  Destination: {\n    Bucket: string;\n    Location: string;\n    StorageClass?: StorageClassType;\n    StorageClassInheritDirective: StorageClassInheritDirectiveType;\n  };\n  /**\n   *  @private unstable: internal(default) |tos_acc\n   */\n  TransferType?: string;\n  HistoricalObjectReplication: 'Enabled' | 'Disabled';\n  /** @private unstable */\n  Tags?: ReplicationTag[];\n  AccessControlTranslation?: {\n    Owner: string; //\"BucketOwnerEntrusted\"\n  };\n}\n\nexport interface PutBucketReplicationInput {\n  bucket: string;\n  role: string;\n  rules: ReplicationRule[];\n}\n\nexport interface PutBucketReplicationOutput {}\n\nexport async function putBucketReplication(\n  this: TOSBase,\n  input: PutBucketReplicationInput\n) {\n  const { bucket, rules, role } = input;\n  if (this.opts.enableOptimizeMethodBehavior && !rules.length) {\n    return deleteBucketReplication.call(this, { bucket });\n  }\n\n  return this.fetchBucket<PutBucketReplicationOutput>(\n    bucket,\n    'PUT',\n    { [CommonQueryKey]: '' },\n    {},\n    {\n      Role: role,\n      Rules: rules,\n    }\n  );\n}\n\nexport interface GetBucketReplicationInput {\n  bucket: string;\n  progress?: string;\n  ruleId?: string;\n}\n\nexport interface GetBucketReplicationOutput {\n  Role: string;\n  Rules: ReplicationRule[];\n}\n\nexport async function getBucketReplication(\n  this: TOSBase,\n  input: GetBucketReplicationInput\n) {\n  const { bucket, progress, ruleId } = input;\n  const query: Record<string, string> = {\n    [CommonQueryKey]: '',\n    progress: progress || '',\n  };\n  if (ruleId != null) {\n    query['rule-id'] = `${ruleId}`;\n  }\n\n  try {\n    return await this.fetchBucket<GetBucketReplicationOutput>(\n      bucket,\n      'GET',\n      query,\n      {}\n    );\n  } catch (err) {\n    return handleEmptyServerError<GetBucketReplicationOutput>(err, {\n      enableCatchEmptyServerError: this.opts.enableOptimizeMethodBehavior,\n      methodKey: 'getBucketReplication',\n      defaultResponse: {\n        Rules: [],\n        Role: '',\n      },\n    });\n  }\n}\n\nexport interface DeleteBucketReplicationInput {\n  bucket: string;\n}\n\nexport interface DeleteBucketReplicationOutput {}\n\nexport async function deleteBucketReplication(\n  this: TOSBase,\n  input: DeleteBucketReplicationInput\n) {\n  const { bucket } = input;\n\n  return this.fetchBucket<DeleteBucketReplicationOutput>(\n    bucket,\n    'DELETE',\n    { [CommonQueryKey]: '' },\n    {}\n  );\n}\n", "import { convertNormalCamelCase2Upper } from '../../utils';\nimport { handleEmptyServerError } from '../../handleEmptyServerError';\nimport TOSBase from '../base';\n\nconst CommonQueryKey = 'website';\n\ntype Protocol = 'http' | 'https';\n\ninterface RedirectAllRequestsTo {\n  HostName: string;\n  Protocol?: Protocol;\n}\n\ninterface IndexDocument {\n  Suffix: string;\n  ForbiddenSubDir?: boolean;\n}\ninterface ErrorDocument {\n  Key?: string;\n}\ninterface RoutingRule {\n  Condition: {\n    HttpErrorCodeReturnedEquals?: number;\n    KeyPrefixEquals?: string;\n  };\n  Redirect: {\n    HostName?: string;\n    HttpRedirectCode?: number;\n    Protocol?: Protocol;\n    ReplaceKeyPrefixWith?: string;\n    ReplaceKeyWith?: string;\n  };\n}\n\nexport interface PutBucketWebsiteInput {\n  bucket: string;\n  redirectAllRequestsTo?: RedirectAllRequestsTo;\n  indexDocument?: IndexDocument;\n  errorDocument?: ErrorDocument;\n  routingRules?: RoutingRule[];\n}\n\nexport interface PutBucketWebsiteOutput {}\n\nexport async function putBucketWebsite(\n  this: TOSBase,\n  input: PutBucketWebsiteInput\n) {\n  const { bucket, ...otherProps } = input;\n\n  const body = convertNormalCamelCase2Upper(otherProps);\n  return this.fetchBucket<PutBucketWebsiteOutput>(\n    bucket,\n    'PUT',\n    { [CommonQueryKey]: '' },\n    {},\n    {\n      ...body,\n    }\n  );\n}\n\nexport interface GetBucketWebsiteInput {\n  bucket: string;\n}\n\nexport interface GetBucketWebsiteOutput {\n  RedirectAllRequestsTo?: RedirectAllRequestsTo;\n  IndexDocument?: IndexDocument;\n  ErrorDocument?: ErrorDocument;\n  RoutingRules?: RoutingRule[];\n}\n\nexport async function getBucketWebsite(\n  this: TOSBase,\n  input: GetBucketWebsiteInput\n) {\n  const { bucket } = input;\n\n  try {\n    return this.fetchBucket<GetBucketWebsiteOutput>(\n      bucket,\n      'GET',\n      { [CommonQueryKey]: '' },\n      {}\n    );\n  } catch (error) {\n    return handleEmptyServerError<GetBucketWebsiteOutput>(error, {\n      enableCatchEmptyServerError: this.opts.enableOptimizeMethodBehavior,\n      methodKey: 'getBucketWebsite',\n      defaultResponse: {\n        RoutingRules: [],\n      },\n    });\n  }\n}\n\nexport interface DeleteBucketWebsiteInput {\n  bucket: string;\n}\n\nexport interface DeleteBucketWebsiteOutput {}\n\nexport async function deleteBucketWebsite(\n  this: TOSBase,\n  input: DeleteBucketWebsiteInput\n) {\n  const { bucket } = input;\n\n  return this.fetchBucket<DeleteBucketWebsiteOutput>(\n    bucket,\n    'DELETE',\n    { [CommonQueryKey]: '' },\n    {}\n  );\n}\n", "import { convertNormalCamelCase2Upper } from '../../utils';\nimport { handleEmptyServerError } from '../../handleEmptyServerError';\nimport TOSBase from '../base';\n\nconst CommonQueryKey = 'notification';\n\nexport interface Filter {\n  TOSKey?: {\n    FilterRules: {\n      Name: string;\n      Value: string;\n    }[];\n  };\n}\ninterface CloudFunctionConfiguration {\n  Events: string[];\n  Filter?: Filter;\n  RuleId?: string;\n  CloudFunction: string;\n}\n\nexport interface RocketMQConf {\n  InstanceId: string;\n  Topic: string;\n  AccessKeyId: string;\n}\nexport interface RocketMQConfiguration {\n  RuleId: string;\n  Role: string;\n  Events: string[]; // 支持的值在不断增加，不定义成枚举\n  Filter?: Filter;\n  RocketMQ: RocketMQConf;\n}\n\nexport interface PutBucketNotificationInput {\n  bucket: string;\n  cloudFunctionConfigurations?: CloudFunctionConfiguration[];\n  rocketMQConfigurations?: RocketMQConfiguration[];\n}\n\nexport interface PutBucketNotificationOutput {}\n\n/**\n * @deprecated use PutBucketNotificationType2 instead\n */\nexport async function putBucketNotification(\n  this: TOSBase,\n  input: PutBucketNotificationInput\n) {\n  const { bucket, ...otherProps } = input;\n\n  const body = convertNormalCamelCase2Upper(otherProps);\n  return this.fetchBucket<PutBucketNotificationOutput>(\n    bucket,\n    'PUT',\n    { [CommonQueryKey]: '' },\n    {},\n    {\n      ...body,\n    }\n  );\n}\n\nexport interface GetBucketNotificationInput {\n  bucket: string;\n}\n\nexport interface GetBucketNotificationOutput {\n  CloudFunctionConfigurations: CloudFunctionConfiguration[];\n  RocketMQConfigurations: RocketMQConfiguration[];\n}\n\n/**\n * @deprecated use GetBucketNotificationType2 instead\n */\nexport async function getBucketNotification(\n  this: TOSBase,\n  input: GetBucketNotificationInput\n) {\n  const { bucket } = input;\n  try {\n    return await this.fetchBucket<GetBucketNotificationOutput>(\n      bucket,\n      'GET',\n      { [CommonQueryKey]: '' },\n      {}\n    );\n  } catch (error) {\n    return handleEmptyServerError<GetBucketNotificationOutput>(error, {\n      enableCatchEmptyServerError: this.opts.enableOptimizeMethodBehavior,\n      methodKey: 'getBucketNotification',\n      defaultResponse: {\n        CloudFunctionConfigurations: [],\n        RocketMQConfigurations: [],\n      },\n    });\n  }\n}\n", "import { convertNormalCamelCase2Upper } from '../../utils';\nimport { handleEmptyServerError } from '../../handleEmptyServerError';\nimport TOSBase from '../base';\n\nconst CommonQueryKey = 'customdomain';\n\nexport interface CustomDomainRule {\n  Domain: string;\n  Cname: string;\n  Forbidden?: boolean;\n  ForbiddenReason?: string;\n  CertId?: string;\n  CertStatus?: string;\n  /**@private unstable\n   * value tos|s3\n   * */\n  Protocol?: string;\n}\n\nexport interface PutBucketCustomDomainInput {\n  bucket: string;\n  customDomainRule: {\n    Domain: string;\n    CertId?: string;\n    /**@private unstable\n     * value tos|s3\n     * */\n    Protocol?: string;\n  };\n}\n\nexport interface PutBucketCustomDomainOutput {}\n\nexport async function putBucketCustomDomain(\n  this: TOSBase,\n  input: PutBucketCustomDomainInput\n) {\n  const { bucket, ...otherProps } = input;\n\n  const body = convertNormalCamelCase2Upper(otherProps);\n  return this.fetchBucket<PutBucketCustomDomainOutput>(\n    bucket,\n    'PUT',\n    { [CommonQueryKey]: '' },\n    {},\n    {\n      ...body,\n    }\n  );\n}\n\nexport interface GetBucketCustomDomainInput {\n  bucket: string;\n}\n\nexport interface GetBucketCustomDomainOutput {\n  CustomDomainRules: CustomDomainRule[];\n}\n\nexport async function getBucketCustomDomain(\n  this: TOSBase,\n  input: GetBucketCustomDomainInput\n) {\n  try {\n    const { bucket } = input;\n    return await this.fetchBucket<GetBucketCustomDomainOutput>(\n      bucket,\n      'GET',\n      { [CommonQueryKey]: '' },\n      {}\n    );\n  } catch (error) {\n    return handleEmptyServerError<GetBucketCustomDomainOutput>(error, {\n      defaultResponse: { CustomDomainRules: [] },\n      enableCatchEmptyServerError: this.opts.enableOptimizeMethodBehavior,\n      methodKey: 'getBucketCustomDomain',\n    });\n  }\n}\n\nexport interface DeleteBucketCustomDomainInput {\n  bucket: string;\n  customDomain: string;\n}\n\nexport interface DeleteBucketCustomDomainOutput {}\n\nexport async function deleteBucketCustomDomain(\n  this: TOSBase,\n  input: DeleteBucketCustomDomainInput\n) {\n  const { bucket, customDomain } = input;\n\n  return this.fetchBucket<DeleteBucketCustomDomainOutput>(\n    bucket,\n    'DELETE',\n    { customdomain: customDomain },\n    {}\n  );\n}\n", "import { convertNormalCamelCase2Upper } from '../../utils';\nimport { handleEmptyServerError } from '../../handleEmptyServerError';\nimport TOSBase from '../base';\n\nconst CommonQueryKey = 'realtimeLog';\n\ninterface AccessLogConfiguration {\n  UseServiceTopic: boolean;\n  TLSProjectID?: string;\n  TLSTopicID?: string;\n}\n\ninterface RealTimeLogConfiguration {\n  Role: string;\n  AccessLogConfiguration: AccessLogConfiguration;\n}\n\nexport interface PutBucketRealTimeLogInput {\n  bucket: string;\n  realTimeLogConfiguration: RealTimeLogConfiguration;\n}\n\nexport interface PutBucketRealTimeLogOutput {}\n\nexport async function putBucketRealTimeLog(\n  this: TOSBase,\n  input: PutBucketRealTimeLogInput\n) {\n  const { bucket, ...otherProps } = input;\n\n  const body = convertNormalCamelCase2Upper(otherProps);\n  return this.fetchBucket<PutBucketRealTimeLogOutput>(\n    bucket,\n    'PUT',\n    { [CommonQueryKey]: '' },\n    {},\n    {\n      ...body,\n    }\n  );\n}\n\nexport interface GetBucketRealTimeLogInput {\n  bucket: string;\n}\n\nexport interface GetBucketRealTimeLogOutput {\n  RealTimeLogConfiguration?: RealTimeLogConfiguration;\n}\n\nexport async function getBucketRealTimeLog(\n  this: TOSBase,\n  input: GetBucketRealTimeLogInput\n) {\n  const { bucket } = input;\n\n  try {\n    return await this.fetchBucket<GetBucketRealTimeLogOutput>(\n      bucket,\n      'GET',\n      { [CommonQueryKey]: '' },\n      {}\n    );\n  } catch (error) {\n    return handleEmptyServerError<GetBucketRealTimeLogOutput>(error, {\n      enableCatchEmptyServerError: this.opts.enableOptimizeMethodBehavior,\n      methodKey: 'getBucketRealTimeLog',\n      defaultResponse: {},\n    });\n  }\n}\n\nexport interface DeleteBucketRealTimeLogInput {\n  bucket: string;\n}\n\nexport interface DeleteBucketRealTimeLogOutput {}\n\nexport async function deleteBucketRealTimeLog(\n  this: TOSBase,\n  input: DeleteBucketRealTimeLogInput\n) {\n  const { bucket } = input;\n\n  return this.fetchBucket<DeleteBucketRealTimeLogOutput>(\n    bucket,\n    'DELETE',\n    { [CommonQueryKey]: '' },\n    {}\n  );\n}\n", "import { handleEmptyServerError } from '../../handleEmptyServerError';\nimport TOSBase, { TosResponse } from '../base';\n\n/**\n * 清单文件导出周期\n */\nexport enum ScheduleFrequency {\n  /** 按天 */\n  Daily = 'Daily',\n  /** 按周 */\n  Weekly = 'Weekly',\n}\n\n/**\n * 清单包含Object版本信息值\n */\nexport enum IncludedObjectVersions {\n  /** 全部 */\n  All = 'All',\n  /** 当前版本 */\n  Current = 'Current',\n}\n\n/**\n * 清单配置项\n */\nexport enum InventoryOptionalFields {\n  /** Object的大小 */\n  Size = 'Size',\n  /** Object的最后修改时间 */\n  LastModifiedDat = 'LastModifiedDate',\n  /** 标识Object的内容 */\n  ETag = 'ETag',\n  /** Object的存储类型 */\n  StorageClass = 'StorageClass',\n  /** 是否为通过分片上传的Object */\n  IsMultipartUploaded = 'IsMultipartUploaded',\n  /** Object是否加密 */\n  EncryptionStatus = 'EncryptionStatus',\n  CRC64 = 'CRC64',\n  /** crr复制状态 */\n  ReplicationStatus = 'ReplicationStatus',\n}\n\n/**\n * 桶清单\n */\nexport interface BucketInventoryItem {\n  /** 清单名称 */\n  Id: string;\n  /** 清单功能是否启用 */\n  IsEnabled: boolean;\n  /** 清单筛选的前缀 */\n  Filter?: {\n    /** 筛选规则的匹配前缀 */\n    Prefix?: string;\n  };\n  /** 存放清单结果 */\n  Destination: {\n    /** Bucket 信息 */\n    TOSBucketDestination: {\n      /** 清单文件的文件格式 */\n      Format: string;\n      /** Bucket 所有者授予的账户ID */\n      AccountId: string;\n      /** 角色名称 */\n      Role: string;\n      /** 存放导出的清单文件的 Bucket */\n      Bucket: string;\n      /** 清单文件的存储路径前缀 */\n      Prefix?: string;\n    };\n  };\n  /** 存放清单导出周期信息 */\n  Schedule: {\n    /** 导出的周期 */\n    Frequency: ScheduleFrequency;\n  };\n  /** 是否在清单中包含 Object 版本信息 */\n  IncludedObjectVersions: string;\n  /** 配置项 */\n  OptionalFields?: {\n    Field: InventoryOptionalFields[];\n  };\n}\n\nexport interface PutBucketInventoryInput {\n  bucket: string;\n  inventoryConfiguration: BucketInventoryItem;\n}\n\nexport interface PutBucketInventoryOutput {}\n\nexport interface GetBucketInventoryInput {\n  bucket: string;\n  id: string;\n}\n\nexport type GetBucketInventoryOutput = BucketInventoryItem | undefined;\nexport interface ListBucketInventoryInput {\n  bucket: string;\n  continuationToken?: string;\n}\n\nexport interface ListBucketInventoryOutput {\n  InventoryConfigurations: BucketInventoryItem[];\n  IsTruncated?: boolean;\n  NextContinuationToken?: string;\n}\n\nexport interface DeleteBucketInventoryInput {\n  bucket: string;\n  id: string;\n}\n\nexport interface DeleteBucketInventoryOutput {}\n\n/**\n * 获取桶清单详情信息\n */\nexport async function getBucketInventory(\n  this: TOSBase,\n  req: GetBucketInventoryInput\n): Promise<TosResponse<GetBucketInventoryOutput>> {\n  try {\n    const res = await this.fetchBucket<GetBucketInventoryOutput>(\n      req.bucket,\n      'GET',\n      {\n        inventory: '',\n        id: req.id,\n      },\n      {}\n    );\n\n    return res;\n  } catch (error) {\n    return handleEmptyServerError<GetBucketInventoryOutput>(error, {\n      enableCatchEmptyServerError: this.opts.enableOptimizeMethodBehavior,\n      methodKey: 'getBucketInventory',\n      defaultResponse: undefined,\n    });\n  }\n}\n\n/**\n * 分页获取桶清单信息\n */\nexport async function listBucketInventory(\n  this: TOSBase,\n  req: ListBucketInventoryInput\n): Promise<TosResponse<ListBucketInventoryOutput>> {\n  const params = {\n    inventory: '',\n    ...(req.continuationToken\n      ? { 'continuation-token': req.continuationToken }\n      : null),\n  };\n  try {\n    const res = await this.fetchBucket<ListBucketInventoryOutput>(\n      req.bucket,\n      'GET',\n      params,\n      {}\n    );\n    return res;\n  } catch (error) {\n    return handleEmptyServerError<ListBucketInventoryOutput>(error, {\n      enableCatchEmptyServerError: this.opts.enableOptimizeMethodBehavior,\n      methodKey: 'listBucketInventory',\n      defaultResponse: {\n        InventoryConfigurations: [],\n      },\n    });\n  }\n}\n\n/**\n * 删除桶清单\n */\nexport async function deleteBucketInventory(\n  this: TOSBase,\n  req: DeleteBucketInventoryInput\n): Promise<TosResponse<DeleteBucketInventoryOutput>> {\n  return this.fetchBucket(\n    req.bucket,\n    'DELETE',\n    { inventory: '', id: req.id },\n    {}\n  );\n}\n\n/**\n * 更新桶清单\n */\nexport function putBucketInventory(\n  this: TOSBase,\n  req: PutBucketInventoryInput\n): Promise<TosResponse<PutBucketInventoryOutput>> {\n  return this.fetchBucket(\n    req.bucket,\n    'PUT',\n    { inventory: '', id: req.inventoryConfiguration.Id },\n    {},\n    req.inventoryConfiguration\n  );\n}\n", "/** @file TOS 支持 QoSPolicy(流控策略管理) 相关接口  */\nimport { MergeExclusive } from 'type-fest';\nimport TOSBase from '../base';\n\nexport enum StringOp {\n  StringEquals = 'StringEquals',\n  StringNotEquals = 'StringNotEquals',\n  StringEqualsIgnoreCase = 'StringEqualsIgnoreCase',\n  StringNotEqualsIgnoreCase = 'StringNotEqualsIgnoreCase',\n  StringLike = 'StringLike',\n  StringNotLike = 'StringNotLike',\n}\nexport enum DateOp {\n  DateEquals = 'DateEquals',\n  DateNotEquals = 'DateNotEquals',\n  DateLessThan = 'DateLessThan',\n  DateLessThanEquals = 'DateLessThanEquals',\n  DateGreaterThan = 'DateGreaterThan',\n  DateGreaterThanEquals = 'DateGreaterThanEquals',\n}\nexport enum IpOp {\n  IpAddress = 'IpAddress',\n  NotIpAddress = 'NotIpAddress',\n}\n\n/** (共三种)条件的运算符 */\nexport type QosOp = StringOp | DateOp | IpOp;\n\n/** 服务端模型 - 条件键值对 */\nexport type QosConditions = {\n  [operator in string]?: {\n    [key in string]: string[];\n  };\n};\n\n/** 流控类别 */\nexport enum QuotaType {\n  /** 写Qps */\n  WritesQps = 'WritesQps',\n  /** 读Qps */\n  ReadsQps = 'ReadsQps',\n  /** list类Qps */\n  ListQps = 'ListQps',\n  /** 写带宽 */\n  WritesRate = 'WritesRate',\n  /** 读带宽 */\n  ReadsRate = 'ReadsRate',\n}\nexport type QuotaTypes = {\n  /** 写类 action Qps，取值为正整数 */\n  [QuotaType.WritesQps]?: string;\n  /** 读类 action Qps，取值为正整数 */\n  [QuotaType.ReadsQps]?: string;\n  /** list 类 action Qps，取值为正整数 */\n  [QuotaType.ListQps]?: string;\n  /** 写类 action 带宽，单位为 Mbps，取值为正整数 */\n  [QuotaType.WritesRate]?: string;\n  /** 读类 action 带宽，单位为 Mbps，取值为正整数 */\n  [QuotaType.ReadsRate]?: string;\n};\n\nexport type QosStatement = MergeExclusive<\n  /** 适用的资源列表，不支持两个属性同时使用 */\n  { Resource: string | string[] },\n  { NotResource: string | string[] }\n> &\n  MergeExclusive<\n    /** 适用的账户、用户或者角色，不支持两个属性同时使用 */\n    { Principal: string[] },\n    { NotPrincipal: string[] }\n  > & {\n    /** 策略名称，以区分不同的策略 */\n    Sid: string;\n    /** 流控策略的配额 */\n    Quota: QuotaTypes;\n    /** 指定策略在哪些情况下适用 quota */\n    Condition?: QosConditions;\n  };\n\n/** 服务端预期接收的数据模型 */\nexport interface QosPolicy {\n  /** 策略列表 */\n  Statement: QosStatement[];\n  /** API 接口版本号 */\n  Version?: string;\n  /** Cas 版本号 */\n  CasVersion?: string;\n}\n\nexport interface QosPolicyBaseInput {\n  accountId: string;\n}\n\nexport interface GetQosPolicyInput extends QosPolicyBaseInput {}\nexport interface GetQosPolicyOutput extends QosPolicy {\n  Version: string;\n  CasVersion: string;\n}\n\nexport interface PutQosPolicyInput extends QosPolicy, QosPolicyBaseInput {}\n\nexport interface DeleteQosPolicyInput extends QosPolicyBaseInput {}\n\n/**\n * @private unstable method\n * @description 拉取流控策略列表\n * @param {GetQosPolicyInput}\n * @returns {GetQosPolicyOutput}\n */\nexport async function getQosPolicy(this: TOSBase, params: GetQosPolicyInput) {\n  const { accountId } = params;\n  const res = await this.fetch<GetQosPolicyOutput>(\n    'GET',\n    '/qospolicy',\n    {},\n    {\n      'x-tos-account-id': accountId,\n    },\n    {},\n    {}\n  );\n\n  return res;\n}\n\n/**\n * @private unstable method\n * @description 更新流控策略列表 覆盖全部 QosPolicy\n * @param {PutQosPolicyInput}\n */\nexport async function putQosPolicy(this: TOSBase, params: PutQosPolicyInput) {\n  const { accountId, ...restParams } = params;\n  const res = await this.fetch(\n    'PUT',\n    '/qospolicy',\n    {},\n    {\n      'x-tos-account-id': accountId,\n    },\n    {\n      ...restParams,\n    },\n    {}\n  );\n\n  return res;\n}\n\n/**\n * @private unstable method\n * @description 拉取流控策略列表\n * @param {DeleteQosPolicyInput}\n */\nexport async function deleteQosPolicy(\n  this: TOSBase,\n  params: DeleteQosPolicyInput\n) {\n  const { accountId } = params;\n  const res = await this.fetch(\n    'DELETE',\n    '/qospolicy',\n    {},\n    {\n      'x-tos-account-id': accountId,\n    },\n    {},\n    {}\n  );\n\n  return res;\n}\n", "import TOSBase from '../base';\nimport { convertNormalCamelCase2Upper, paramsSerializer } from '../../utils';\nimport { StorageClassType, TierType } from '../../TosExportEnum';\nexport type JobStatusType =\n  | 'New'\n  | 'Preparing'\n  | 'Suspended'\n  | 'Ready'\n  | 'Active'\n  | 'Pausing'\n  | 'Paused'\n  | 'Complete'\n  | 'Cancelling'\n  | 'Cancelled'\n  | 'Failing'\n  | 'Failed';\n\nexport type DirectiveType = 'COPY' | 'REPLACE' | 'ADD';\nexport type CannedAccessControlListType = 'default' | 'private' | 'public-read';\nexport type PermissionType = 'READ' | 'READ_ACP' | 'WRITE_ACP' | 'FULL_CONTROL';\nexport type PrefixReplaceType = 'true' | 'false';\nexport type ConfirmationRequiredType = '0' | '1';\n\nexport interface Tag {\n  Key: string;\n  Value: string;\n}\n\ninterface Manifest {\n  Location: {\n    ETag: string;\n    ObjectTrn: string;\n    ObjectVersionId?: string;\n  };\n  Spec: {\n    Format: 'TOSInventoryReport_CSV_V1';\n  };\n}\n\nexport interface NewObjectMetadataType {\n  SSEAlgorithm?: 'AES256';\n  UserMetadata?: {\n    member: { Key: string; Value: string }[];\n  };\n  'content-type'?: string;\n  'content-encoding'?: string;\n  'content-language'?: string;\n  'cache-control'?: string;\n  'content-disposition'?: string;\n  expires?: string;\n}\nexport interface NewObjectTaggingType {\n  TOSTag?: Tag[];\n}\nexport interface Report {\n  Bucket: string;\n  Enabled: PrefixReplaceType;\n  Format: 'Report_CSV_V1';\n  Prefix: string;\n  ReportScope: 'AllTasks' | 'FailedTasksOnly';\n}\n\nexport interface ProgressSummary {\n  TotalNumberOfTasks: number;\n  NumberOfTasksSucceeded: number;\n  NumberOfTasksFailed: number;\n}\n\nexport interface ListBatchInput {\n  accountId: string;\n  jobStatuses?: string[];\n  nextToken?: string;\n  maxResults?: number;\n}\n\nexport interface UpdateJobPriorityInput {\n  jobId: string;\n  priority: number;\n  accountId: string;\n}\nexport interface UpdateJobStatusInput {\n  jobId: string;\n  accountId: string;\n  requestedJobStatus: 'Ready' | 'Cancelled';\n  statusUpdateReason?: string;\n}\n\nexport interface JobInput {\n  JobId: string;\n  accountId: string;\n}\n\nexport type DeleteJob = JobInput;\nexport type DescribeJob = JobInput;\n\nexport interface AccessControlList {\n  TOSGrant: {\n    Grantee: {\n      Identifier: string;\n      TypeIdentifier: 'id';\n    };\n    Permission: PermissionType;\n  }[];\n}\nexport interface TOSPutObjectCopy {\n  TOSPutObjectCopy: {\n    PrefixReplace: PrefixReplaceType;\n    ResourcesPrefix: string;\n    TargetKeyPrefix: string;\n    StorageClass: StorageClassType;\n    AccessControlDirective: DirectiveType;\n    CannedAccessControlList?: CannedAccessControlListType;\n    AccessControlGrants?: AccessControlList;\n    TargetResource: string;\n    MetadataDirective: DirectiveType;\n    NewObjectMetadata: NewObjectMetadataType;\n    TaggingDirective: DirectiveType;\n    NewObjectTagging: NewObjectTaggingType;\n  };\n}\n\nexport interface TOSPutObjectAcl {\n  TOSPutObjectAcl: {\n    AccessControlPolicy: {\n      CannedAccessControlList: CannedAccessControlListType;\n      AccessControlList: AccessControlList;\n    };\n  };\n}\n\nexport interface TOSPutObjectTagging {\n  TOSPutObjectTagging: {\n    TOSTag: Tag[];\n  };\n}\n\nexport interface TOSRestoreObject {\n  TOSRestoreObject: {\n    Days: number;\n    Tier: TierType;\n  };\n}\n\nexport interface TOSDeleteObjectTagging {\n  TOSDeleteObjectTagging: {};\n}\n\nexport type PutJobInput = {\n  accountId: string;\n  clientRequestToken: string;\n  confirmationRequired: '0' | '1';\n  description?: string;\n  manifest: Manifest;\n  priority: string;\n  roleTrn: string;\n  report?: Report;\n  operation?:\n    | TOSPutObjectCopy\n    | TOSPutObjectAcl\n    | TOSPutObjectTagging\n    | TOSRestoreObject\n    | TOSDeleteObjectTagging;\n};\n\nexport interface DescribeJobRes {\n  Job: {\n    JobId: string;\n    ConfirmationRequired: ConfirmationRequiredType;\n    Description?: string;\n    FailureReasons?: {\n      JobFailure: {\n        FailureCode: string;\n        FailureReason: string;\n      };\n    };\n    Manifest: Manifest;\n    Priority: number;\n    ProgressSummary: ProgressSummary;\n    Report: Report;\n    RoleArn: string;\n    Status: JobStatusType;\n    StatusUpdateReason: string;\n    SuspendedDate: string;\n    TerminationDate: string;\n    CreationTime: string;\n    Operation:\n      | TOSPutObjectCopy\n      | TOSPutObjectAcl\n      | TOSPutObjectTagging\n      | TOSRestoreObject\n      | TOSDeleteObjectTagging;\n  };\n}\n\nexport interface JobList {\n  JobId: string;\n  CreationTime: string;\n  Operation:\n    | 'TOSPutObjectCopy'\n    | 'TOSPutObjectAcl'\n    | 'TOSPutObjectTagging'\n    | 'TOSRestoreObject'\n    | 'TOSDeleteObjectTagging';\n  Priority: number;\n  ProgressSummary: ProgressSummary;\n  Status: JobStatusType;\n  TerminationDate: string;\n  Description: string;\n}\nexport interface JobListRes {\n  Jobs: {\n    member: JobList[];\n  };\n  NextToken: string;\n}\n\n/**\n *\n * @private unstable method\n * @description 创建批量任务\n * @param params\n * @returns\n */\nexport async function createJob(this: TOSBase, params: PutJobInput) {\n  const { accountId, ...reset } = params;\n  const data = convertNormalCamelCase2Upper(reset);\n  const res = await this.fetch(\n    'POST',\n    '/jobs',\n    {},\n    {\n      'x-tos-account-id': accountId,\n    },\n    {\n      ...data,\n    }\n  );\n  return res;\n}\n\n/**\n * @private unstable method\n * @description  获取批量任务列表\n * @param params\n * @returns\n */\nexport async function listJobs(this: TOSBase, params: ListBatchInput) {\n  const { accountId, maxResults = 1000, ...others } = params;\n  const res = await this.fetch<JobListRes>(\n    'GET',\n    '/jobs',\n    {\n      maxResults,\n      ...others,\n    },\n    {\n      'x-tos-account-id': accountId,\n    },\n    {},\n    {\n      axiosOpts: {\n        paramsSerializer,\n      },\n    }\n  );\n  return res;\n}\n\n/**\n *\n * @private unstable method\n * @description 更新批量任务优先级\n * @param params\n * @returns\n */\nexport async function updateJobPriority(\n  this: TOSBase,\n  params: UpdateJobPriorityInput\n) {\n  const { accountId, jobId: JobId, priority } = params;\n  const res = await this.fetch(\n    'POST',\n    `/jobs/${JobId}/priority`,\n    {\n      priority,\n    },\n    {\n      'x-tos-account-id': accountId,\n    },\n    {},\n    {\n      needMd5: true,\n    }\n  );\n  return res;\n}\n\n/**\n *\n * @private unstable method\n * @description 更新批量任务优先级\n * @param params\n * @returns\n */\nexport async function updateJobStatus(\n  this: TOSBase,\n  params: UpdateJobStatusInput\n) {\n  const {\n    accountId,\n    jobId: JobId,\n    requestedJobStatus,\n    statusUpdateReason,\n  } = params;\n  const res = await this.fetch(\n    'POST',\n    `/jobs/${JobId}/status`,\n    {\n      requestedJobStatus,\n      statusUpdateReason,\n    },\n    {\n      'x-tos-account-id': accountId,\n    },\n    {},\n    {\n      needMd5: true,\n    }\n  );\n  return res;\n}\n\n/**\n *\n * @private unstable method\n * @description 删除批量任务\n * @param params\n * @returns\n */\nexport async function deleteJob(this: TOSBase, params: DeleteJob) {\n  const { accountId, JobId } = params;\n  const res = await this.fetch(\n    'DELETE',\n    `/jobs/${JobId}`,\n    {},\n    {\n      'x-tos-account-id': accountId,\n    },\n    {}\n  );\n  return res;\n}\n\n/**\n *\n * @private unstable method\n * @description 获取批量任务详情\n * @param params\n * @returns\n */\nexport async function describeJob(this: TOSBase, params: DescribeJob) {\n  const { accountId, JobId } = params;\n  const res = await this.fetch<DescribeJobRes>(\n    'GET',\n    `/jobs/${JobId}`,\n    {},\n    {\n      'x-tos-account-id': accountId,\n    },\n    {}\n  );\n  return res;\n}\n", "import { handleEmptyServerError } from '../../handleEmptyServerError';\nimport TOSBase, { TosResponse } from '../base';\n\ninterface TagSet {\n  Tags: {\n    Key: string;\n    Value: string;\n  }[];\n}\n\nexport interface PutBucketTaggingInput {\n  bucket?: string;\n  tagging: {\n    TagSet: TagSet;\n  };\n}\n\nexport interface GetBucketTaggingInput {\n  bucket: string;\n}\nexport interface GetBucketTaggingOutput {\n  TagSet: TagSet;\n}\n\n/**\n * @private unstable method\n */\nexport async function putBucketTagging(\n  this: TOSBase,\n  input: PutBucketTaggingInput\n) {\n  const res = await this.fetchBucket(\n    input.bucket,\n    'PUT',\n    { tagging: '' },\n    {},\n    input.tagging,\n    {\n      needMd5: true,\n    }\n  );\n  return res;\n}\n\n/**\n * @private unstable method\n */\nexport async function getBucketTagging(\n  this: TOSBase,\n  { bucket }: GetBucketTaggingInput\n): Promise<TosResponse<GetBucketTaggingOutput>> {\n  try {\n    const res = await this.fetchBucket<GetBucketTaggingOutput>(\n      bucket,\n      'GET',\n      {\n        tagging: '',\n      },\n      {}\n    );\n    return res;\n  } catch (error) {\n    return handleEmptyServerError<GetBucketTaggingOutput>(error, {\n      enableCatchEmptyServerError: this.opts.enableOptimizeMethodBehavior,\n      methodKey: 'getBucketTagging',\n      defaultResponse: {\n        TagSet: {\n          Tags: [],\n        },\n      },\n    });\n  }\n}\n\nexport interface DeleteBucketTaggingInput {\n  bucket: string;\n}\n\n/**\n * @private unstable method\n */\nexport async function deleteBucketTagging(\n  this: TOSBase,\n  { bucket }: DeleteBucketTaggingInput\n) {\n  return this.fetchBucket(bucket, 'DELETE', { tagging: '' }, {});\n}\n", "import TOSBase, { TosResponse } from '../base';\n\nexport interface BucketPayByTraffic {\n  ChargeType: 'FlowOut' | 'Bandwidth';\n  ActiveType: 'NextDay' | 'NextMonth';\n}\n\nexport interface PutBucketPayByTrafficInput {\n  bucket?: string;\n  payByTraffic: BucketPayByTraffic;\n}\n\nexport type GetBucketPayByTrafficOutput = BucketPayByTraffic & {\n  ActiveTime: string;\n};\n\n/**\n * @private unstable method\n */\nexport async function putBucketPayByTraffic(\n  this: TOSBase,\n  input: PutBucketPayByTrafficInput\n) {\n  const res = await this.fetchBucket(\n    input.bucket,\n    'PUT',\n    { payByTraffic: '' },\n    {},\n    input.payByTraffic\n  );\n  return res;\n}\n\ninterface GetBucketPayByTrafficInput {\n  bucket: string;\n}\n/**\n * @private unstable method\n */\nexport async function getBucketPayByTraffic(\n  this: TOSBase,\n  { bucket }: GetBucketPayByTrafficInput\n): Promise<TosResponse<GetBucketPayByTrafficOutput>> {\n  const res = await this.fetchBucket<GetBucketPayByTrafficOutput>(\n    bucket,\n    'GET',\n    {\n      payByTraffic: '',\n    },\n    {}\n  );\n  return res;\n}\n", "import TOSBase, { TosResponse } from '../base';\nimport TosServerError from '../../TosServerError';\nexport interface ImageStyle {\n  Name: string;\n  Content: string;\n  CreateTime: string;\n  LastModifyTime: string;\n}\n\nexport interface BucketImgStyle {\n  bucket?: string;\n  imageStyles: { ImageStyles: ImageStyle[] };\n}\n\nexport interface ImageBriefInfo {\n  Name: string;\n  BucketLevelContent: string;\n  PrefixCount: number;\n}\n\nexport interface BucketImageBriefInfo {\n  BucketName: string;\n  ImageStyleBriefInfo: ImageBriefInfo[];\n}\n\nexport interface GetImageStyleBriefInfoInput {\n  bucket: string;\n}\n\n/**\n * @private unstable method\n */\nexport async function getImageStyleBriefInfo(\n  this: TOSBase,\n  req: GetImageStyleBriefInfoInput\n): Promise<TosResponse<BucketImageBriefInfo>> {\n  const { bucket } = req;\n  try {\n    const res = await this.fetchBucket<BucketImageBriefInfo>(\n      bucket,\n      'GET',\n      {\n        imageStyleBriefInfo: '',\n      },\n      {}\n    );\n    return res;\n  } catch (err) {\n    if (err instanceof TosServerError) {\n      if (err.statusCode === 404) {\n        return this.getNormalDataFromError(\n          {\n            BucketName: bucket,\n            ImageStyleBriefInfo: [],\n          },\n          err\n        );\n      }\n    }\n\n    throw err;\n  }\n}\n\n/**\n * @private unstable method\n */\nexport async function getBucketImageStyleList(\n  this: TOSBase,\n  bucket: string\n): Promise<TosResponse<BucketImgStyle['imageStyles']>> {\n  try {\n    const res = await this.fetchBucket<BucketImgStyle['imageStyles']>(\n      bucket,\n      'GET',\n      {\n        imageStyle: '',\n      },\n      {}\n    );\n    return res;\n  } catch (err) {\n    if (err instanceof TosServerError) {\n      if (err.statusCode === 404) {\n        return this.getNormalDataFromError(\n          {\n            ImageStyles: [],\n          },\n          err\n        );\n      }\n    }\n\n    throw err;\n  }\n}\n\nexport interface GetBucketImageStyleListByNameInput {\n  bucket: string;\n  styleName: string;\n}\n\n/**\n * @private unstable method\n */\nexport async function getBucketImageStyleListByName(\n  this: TOSBase,\n  req: GetBucketImageStyleListByNameInput\n): Promise<TosResponse<BucketImgStyle['imageStyles']>> {\n  try {\n    const { bucket, styleName } = req;\n    const res = await this.fetchBucket<BucketImgStyle['imageStyles']>(\n      bucket,\n      'GET',\n      {\n        imageStyleContent: '',\n        styleName,\n      },\n      {}\n    );\n    return res;\n  } catch (err) {\n    if (err instanceof TosServerError) {\n      if (err.statusCode === 404) {\n        return this.getNormalDataFromError(\n          {\n            ImageStyles: [],\n          },\n          err\n        );\n      }\n    }\n\n    throw err;\n  }\n}\n\n/**\n * @private unstable method\n */\nexport async function getBucketImageStyle(\n  this: TOSBase,\n  bucket: string,\n  styleName: string\n): Promise<TosResponse<ImageStyle | null>> {\n  try {\n    const res = await this.fetchBucket<ImageStyle>(\n      bucket,\n      'GET',\n      {\n        imageStyle: '',\n        styleName,\n      },\n      {}\n    );\n    return res;\n  } catch (err) {\n    if (err instanceof TosServerError) {\n      if (err.statusCode === 404) {\n        return this.getNormalDataFromError(null, err);\n      }\n    }\n\n    throw err;\n  }\n}\n\nexport interface PutBucketImageStyleInput {\n  bucket: string;\n  styleName: string;\n  content: string;\n  styleObjectPrefix?: string;\n}\n\n/**\n * @private unstable method\n */\nexport async function putBucketImageStyle(\n  this: TOSBase,\n  req: PutBucketImageStyleInput\n): Promise<TosResponse<any>> {\n  const { bucket, styleName, content, styleObjectPrefix } = req;\n  try {\n    const res = await this.fetchBucket<any>(\n      bucket,\n      'PUT',\n      {\n        imageStyle: '',\n        styleName,\n        styleObjectPrefix,\n      },\n      {},\n      { Content: content }\n    );\n    return res;\n  } catch (err) {\n    if (err instanceof TosServerError) {\n      if (err.statusCode === 404) {\n        return this.getNormalDataFromError(null, err);\n      }\n    }\n\n    throw err;\n  }\n}\n\nexport interface DeleteBucketImageStyleInput {\n  bucket: string;\n  styleName: string;\n  styleObjectPrefix?: string;\n}\n\n/**\n * @private unstable method\n */\nexport async function deleteBucketImageStyle(\n  this: TOSBase,\n  req: DeleteBucketImageStyleInput\n): Promise<TosResponse<any>> {\n  const { styleName, styleObjectPrefix, bucket } = req;\n  try {\n    const res = await this.fetchBucket<any>(\n      bucket,\n      'DELETE',\n      {\n        imageStyle: '',\n        styleName,\n        styleObjectPrefix,\n      },\n      {}\n    );\n    return res;\n  } catch (err) {\n    if (err instanceof TosServerError) {\n      if (err.statusCode === 404) {\n        return this.getNormalDataFromError(null, err);\n      }\n    }\n    throw err;\n  }\n}\n\nexport interface BucketImgProtect {\n  Enable: boolean;\n  Suffixes?: string[];\n  //原图保护规则（OSS支持10条），一期暂时不做，预留接口\n  OIPRules?: any[];\n  Prefix?: string;\n  Suffix?: string;\n}\n\n/**\n * @private unstable method\n */\nexport async function putBucketImageProtect(\n  this: TOSBase,\n  bucket: string,\n  data: BucketImgProtect\n): Promise<TosResponse<any>> {\n  try {\n    const res = await this.fetchBucket<any>(\n      bucket,\n      'PUT',\n      {\n        originalImageProtect: '',\n      },\n      {},\n      data\n    );\n    return res;\n  } catch (err) {\n    if (err instanceof TosServerError) {\n      if (err.statusCode === 404) {\n        return this.getNormalDataFromError(null, err);\n      }\n    }\n    throw err;\n  }\n}\n\n/**\n * @private unstable method\n */\nexport async function getBucketImageProtect(\n  this: TOSBase,\n  bucket: string\n): Promise<TosResponse<any>> {\n  try {\n    const res = await this.fetchBucket<any>(\n      bucket,\n      'GET',\n      {\n        originalImageProtect: '',\n      },\n      {}\n    );\n    return res;\n  } catch (err) {\n    if (err instanceof TosServerError) {\n      if (err.statusCode === 404) {\n        return this.getNormalDataFromError(null, err);\n      }\n    }\n    throw err;\n  }\n}\nexport type BucketImgProtectStyleSeparator = '-' | '_' | '!' | '\\\\';\n\nexport type BucketImgStyleSeparatorAffixes = Partial<\n  Record<BucketImgProtectStyleSeparator, string>\n>;\n\nexport interface PutBucketImageStyleSeparatorInput {\n  bucket: string;\n  Separator: BucketImgProtectStyleSeparator[];\n  SeparatorSuffix?: BucketImgStyleSeparatorAffixes;\n}\n\n/**\n * @private unstable method\n */\nexport async function putBucketImageStyleSeparator(\n  this: TOSBase,\n  req: PutBucketImageStyleSeparatorInput\n): Promise<TosResponse<any>> {\n  const { bucket, Separator, SeparatorSuffix } = req;\n  try {\n    const res = await this.fetchBucket<any>(\n      bucket,\n      'PUT',\n      {\n        imageStyleSeparator: '',\n      },\n      {},\n      { Separator, SeparatorSuffix }\n    );\n    return res;\n  } catch (err) {\n    if (err instanceof TosServerError) {\n      if (err.statusCode === 404) {\n        return this.getNormalDataFromError(null, err);\n      }\n    }\n    throw err;\n  }\n}\n\n/**\n * @private unstable method\n */\nexport async function getBucketImageStyleSeparator(\n  this: TOSBase,\n  bucket: string\n): Promise<TosResponse<any>> {\n  try {\n    const res = await this.fetchBucket<any>(\n      bucket,\n      'GET',\n      {\n        imageStyleSeparator: '',\n      },\n      {}\n    );\n    return res;\n  } catch (err) {\n    if (err instanceof TosServerError) {\n      if (err.statusCode === 404) {\n        return this.getNormalDataFromError(null, err);\n      }\n    }\n    throw err;\n  }\n}\n", "import { handleEmptyServerError } from '../../handleEmptyServerError';\nimport TOSBase, { TosResponse } from '../base';\n\nexport interface BucketIntelligenttieringOutput {\n  Status?: 'Enabled' | 'Disabled';\n  Transitions?: {\n    Days: number;\n    AccessTier: 'INFREQUENT' | 'ARCHIVEFR';\n  }[];\n}\n\nexport async function getBucketIntelligenttiering(\n  this: TOSBase,\n  bucket?: string\n): Promise<TosResponse<BucketIntelligenttieringOutput>> {\n  try {\n    const res = await this.fetchBucket<BucketIntelligenttieringOutput>(\n      bucket,\n      'GET',\n      { intelligenttiering: '' },\n      {}\n    );\n    return res;\n  } catch (error) {\n    return handleEmptyServerError<BucketIntelligenttieringOutput>(error, {\n      enableCatchEmptyServerError: this.opts.enableOptimizeMethodBehavior,\n      methodKey: 'getBucketIntelligenttiering',\n      defaultResponse: {},\n    });\n  }\n}\n", "import { convertNormalCamelCase2Upper } from '../../utils';\nimport TOSBase from '../base';\n\nconst CommonQueryKey = 'rename';\n\nexport interface PutBucketRenameInput {\n  bucket?: string;\n  renameEnable: boolean;\n}\n\nexport interface PutBucketRenameOutput {}\n\nexport async function putBucketRename(\n  this: TOSBase,\n  input: PutBucketRenameInput\n) {\n  const { bucket, ...otherProps } = input;\n\n  const body = convertNormalCamelCase2Upper(otherProps);\n  return this.fetchBucket<PutBucketRenameOutput>(\n    bucket,\n    'PUT',\n    { [CommonQueryKey]: '' },\n    {},\n    {\n      ...body,\n    }\n  );\n}\n\nexport interface GetBucketRenameInput {\n  bucket?: string;\n}\n\nexport interface GetBucketRenameOutput {\n  RenameEnable: boolean;\n}\n\nexport async function getBucketRename(\n  this: TOSBase,\n  input: GetBucketRenameInput\n) {\n  const { bucket } = input;\n  return await this.fetchBucket<GetBucketRenameOutput>(\n    bucket,\n    'GET',\n    { [CommonQueryKey]: '' },\n    {}\n  );\n}\n\nexport interface DeleteBucketRenameInput {\n  bucket?: string;\n}\n\nexport interface DeleteBucketRenameOutput {}\n\nexport async function deleteBucketRename(\n  this: TOSBase,\n  input: DeleteBucketRenameInput\n) {\n  const { bucket } = input;\n\n  return this.fetchBucket<DeleteBucketRenameOutput>(\n    bucket,\n    'DELETE',\n    { [CommonQueryKey]: '' },\n    {}\n  );\n}\n", "import { TierType } from '../../TosExportEnum';\nimport { convertNormalCamelCase2Upper } from '../../utils';\nimport TOSBase from '../base';\n\nexport interface RestoreObjectInput {\n  bucket?: string;\n  key: string;\n  versionId?: string;\n  days: number;\n  restoreJobParameters?: {\n    Tier: TierType;\n  };\n}\n\nexport async function restoreObject(this: TOSBase, input: RestoreObjectInput) {\n  const { bucket, key, versionId, ...otherProps } = input;\n  const query: Record<string, any> = { restore: '' };\n  if (versionId) {\n    query.versionId = versionId;\n  }\n  const body = convertNormalCamelCase2Upper(otherProps);\n\n  return this._fetchObject<undefined>(input, 'POST', query, {}, body);\n}\n\nexport default restoreObject;\n", "import TOSBase from '../base';\nimport { paramsSerializer } from '../../utils';\n\nexport interface IMetaData {\n  accountId: string;\n}\n\nexport interface StorageLensInput extends IMetaData {\n  Id: string;\n}\ninterface IStrategy {\n  Buckets: {\n    Bucket: string[];\n  };\n  Regions: {\n    Region: string[];\n  };\n}\n\ninterface IPrefixLevel {\n  StorageMetrics: {\n    IsEnabled: boolean;\n    SelectionCriteria: {\n      MaxDepth?: number;\n      MinStorageBytesPercentage?: number;\n      Delimiter: string;\n      Prefixes?: string[];\n    };\n  };\n}\nexport interface StorageLensConfigurationInput extends StorageLensInput {\n  Region: string;\n  IsEnabled: boolean;\n  AccountLevel: {\n    BucketLevel: {\n      /** @deprecated will be removed soon */\n      ActivityMetrics?: {\n        IsEnabled: boolean;\n      };\n      HotStatsMetrics?: {\n        IsEnabled: boolean;\n        Actions: string[]; //当前默认前端传这个，方便后期扩action\n      };\n      PrefixLevel?: IPrefixLevel;\n    };\n  };\n  DataExport?: {\n    BucketDestination?: {\n      Bucket: string;\n      Prefix?: string;\n      OutputSchemaVersion: string;\n      Format: string;\n      Role: string;\n    };\n  };\n  Include?: IStrategy;\n  Exclude?: IStrategy;\n}\nexport type StorageLensConfigurationOutput = StorageLensConfigurationInput;\n\n/**\n * @private unstable method\n * @description 获取数据透视列表\n * @param params\n * @returns\n */\nexport async function listStorageLens(this: TOSBase, params: IMetaData) {\n  const { accountId } = params;\n  const res = await this.fetch<StorageLensConfigurationOutput[]>(\n    'GET',\n    '/storagelens',\n    {},\n    {\n      'x-tos-account-id': accountId,\n    },\n    {},\n    {\n      axiosOpts: {\n        paramsSerializer,\n      },\n    }\n  );\n  return res;\n}\n\n/**\n * @private unstable method\n * @description 删除数据透视记录\n * @param params\n * @returns\n */\nexport async function deleteStorageLens(\n  this: TOSBase,\n  params: StorageLensInput\n) {\n  const { accountId, Id } = params;\n  const res = await this.fetch(\n    'DELETE',\n    `/storagelens`,\n    {\n      id: Id,\n    },\n    {\n      'x-tos-account-id': accountId,\n    },\n    {},\n    {\n      needMd5: true,\n    }\n  );\n  return res;\n}\n\n/**\n * @private unstable method\n * @description 获取数据透视详情\n * @param params\n * @returns\n */\nexport async function getStorageLens(this: TOSBase, params: StorageLensInput) {\n  const { accountId, Id } = params;\n  const res = await this.fetch<StorageLensConfigurationOutput>(\n    'GET',\n    `/storagelens`,\n    {\n      id: Id,\n    },\n    {\n      'x-tos-account-id': accountId,\n    },\n    {},\n    {\n      needMd5: true,\n    }\n  );\n  return res;\n}\n/**\n * @private unstable method\n * @description 提交数据透视记录\n * @param params\n * @returns\n */\nexport async function putStorageLens(\n  this: TOSBase,\n  params: StorageLensConfigurationInput\n) {\n  const { accountId, Id, ...rest } = params;\n\n  const res = await this.fetch(\n    'PUT',\n    `/storagelens`,\n    {\n      id: Id,\n    },\n    {\n      'x-tos-account-id': accountId,\n    },\n    {\n      ...rest,\n      Id,\n    },\n    {\n      needMd5: true,\n    }\n  );\n  return res;\n}\n", "import { convertNormalCamelCase2Upper } from '../../utils';\nimport { handleEmptyServerError } from '../../handleEmptyServerError';\nimport TOSBase from '../base';\n\nconst CommonQueryKey = 'notification_v2';\n\nexport interface NotificationFilter {\n  TOSKey?: {\n    FilterRules: {\n      Name: string;\n      Value: string;\n    }[];\n  };\n}\n\nexport interface DestinationRocketMQ {\n  Role: string;\n  InstanceId: string;\n  Topic: string;\n  AccessKeyId: string;\n}\n\nexport interface NotificationDestination {\n  RocketMQ?: DestinationRocketMQ[];\n  VeFaaS?: { FunctionId: string }[];\n}\n\nexport interface NotificationRule {\n  RuleId: string;\n  Events: string[];\n  Filter?: NotificationFilter;\n  Destination: NotificationDestination;\n}\n\nexport interface PutBucketNotificationInput {\n  bucket: string;\n  Rules: NotificationRule[];\n  Version?: string;\n}\n\nexport interface PutBucketNotificationOutput {}\n\nexport async function putBucketNotificationType2(\n  this: TOSBase,\n  input: PutBucketNotificationInput\n) {\n  const { bucket, ...otherProps } = input;\n\n  const body = convertNormalCamelCase2Upper(otherProps);\n  return this.fetchBucket<PutBucketNotificationOutput>(\n    bucket,\n    'PUT',\n    { [CommonQueryKey]: '' },\n    {},\n    {\n      ...body,\n    }\n  );\n}\n\nexport interface GetBucketNotificationInput {\n  bucket: string;\n}\n\nexport interface GetBucketNotificationOutput {\n  Rules: NotificationRule[];\n  Version?: string;\n}\n\nexport async function getBucketNotificationType2(\n  this: TOSBase,\n  input: GetBucketNotificationInput\n) {\n  const { bucket } = input;\n  try {\n    return await this.fetchBucket<GetBucketNotificationOutput>(\n      bucket,\n      'GET',\n      { [CommonQueryKey]: '' },\n      {}\n    );\n  } catch (error) {\n    return handleEmptyServerError<GetBucketNotificationOutput>(error, {\n      enableCatchEmptyServerError: this.opts.enableOptimizeMethodBehavior,\n      methodKey: 'getBucketNotificationType2',\n      defaultResponse: {\n        Rules: [],\n      },\n    });\n  }\n}\n", "import TOSBase, { TosResponse } from '../base';\nimport { fillRequestHeaders, normalizeHeadersKey } from '../../utils';\nimport { Acl } from '../../interface';\nimport { StorageClassType } from '../../TosExportEnum';\n\nexport interface PutSymInput {\n  bucket?: string;\n  key: string;\n  symLinkTargetKey: string;\n  symLinkTargetBucket?: string;\n  forbidOverwrite?: boolean;\n  acl?: Acl;\n  meta?: Record<string, string>;\n  storageClass?: StorageClassType;\n\n  headers?: {\n    'x-tos-symlink-target': string;\n    'x-tos-symlink-bucket'?: string;\n    'x-tos-forbid-overwrite'?: string;\n    'x-tos-acl'?: Acl;\n    'x-tos-storage-class'?: string;\n    [key: string]: string | undefined;\n  };\n}\n\nexport interface PutSymOutput {\n  VersionID?: string;\n}\n/**\n * @private unstable method\n */\nexport async function putSymlink(this: TOSBase, input: PutSymInput) {\n  return _putSymlink.call(this, input);\n}\n\nexport async function _putSymlink(\n  this: TOSBase,\n  input: PutSymInput\n): Promise<TosResponse<PutSymOutput>> {\n  const headers = (input.headers = normalizeHeadersKey(input.headers));\n  fillRequestHeaders(input, [\n    'symLinkTargetKey',\n    'symLinkTargetBucket',\n    'forbidOverwrite',\n    'acl',\n    'storageClass',\n    'meta',\n  ]);\n  return this._fetchObject<PutSymOutput>(\n    input,\n    'PUT',\n    { symlink: '' },\n    headers,\n    undefined,\n    {\n      handleResponse(response) {\n        const { headers } = response;\n        return {\n          VersionID: headers['x-tos-version-id'],\n        };\n      },\n    }\n  );\n}\n\nexport default putSymlink;\n", "import TOSBase, { TosResponse } from '../base';\n\nexport interface GetSymInput {\n  bucket?: string;\n  key: string;\n  versionId?: string;\n}\n\nexport interface PutSymOutput {\n  VersionID?: string;\n  SymlinkTargetKey: string;\n  SymlinkTargetBucket: string;\n  LastModified: string;\n}\n\n/**\n * @private unstable method\n */\nexport async function getSymlink(this: TOSBase, input: GetSymInput) {\n  return _getSymlink.call(this, input);\n}\n\nexport async function _getSymlink(\n  this: TOSBase,\n  input: GetSymInput\n): Promise<TosResponse<PutSymOutput>> {\n  const query: Record<string, any> = { symlink: '' };\n  if (input.versionId) {\n    query.versionId = input.versionId;\n  }\n  return this._fetchObject<PutSymOutput>(input, 'GET', query, {}, undefined, {\n    handleResponse: (res) => {\n      const { headers } = res;\n      return {\n        VersionID: headers['x-tos-version-id'],\n        SymlinkTargetKey: headers['x-tos-symlink-target'],\n        SymlinkTargetBucket: headers['x-tos-symlink-bucket'],\n        LastModified: headers['last-modified'],\n      };\n    },\n  });\n}\n\nexport default getSymlink;\n", "import { handleEmptyServerError } from '../../handleEmptyServerError';\nimport { TransferAccelerationStatusType } from '../../TosExportEnum';\nimport { convertNormalCamelCase2Upper } from '../../utils';\nimport { Headers } from '../../interface';\nimport TOSBase from '../base';\n\nconst CommonQueryKey = 'transferAcceleration';\n\nexport interface PutBucketTransferAccelerationInput {\n  bucket?: string;\n  transferAccelerationConfiguration: {\n    Enabled: 'true' | 'false';\n  };\n}\n\nexport interface PutBucketTransferAccelerationOutput {}\n\n/**\n * @private unstable\n */\nexport async function putBucketTransferAcceleration(\n  this: TOSBase,\n  input: PutBucketTransferAccelerationInput\n) {\n  const { bucket, ...otherProps } = input;\n\n  const body = convertNormalCamelCase2Upper(otherProps);\n  return this.fetchBucket<PutBucketTransferAccelerationOutput>(\n    bucket,\n    'PUT',\n    { [CommonQueryKey]: '' },\n    {},\n    {\n      ...body,\n    }\n  );\n}\n\nexport interface GetBucketTransferAccelerationInput {\n  bucket?: string;\n  getStatus?: boolean;\n}\n\nexport interface GetBucketTransferAccelerationOutput {\n  TransferAccelerationConfiguration: {\n    Enabled: 'true' | 'false';\n    Status: TransferAccelerationStatusType;\n  };\n}\n\n/**\n * @private unstable\n */\nexport async function getBucketTransferAcceleration(\n  this: TOSBase,\n  input: GetBucketTransferAccelerationInput\n) {\n  try {\n    const { bucket } = input;\n    const headers: Headers = {};\n    if (input.getStatus) {\n      headers['x-tos-get-bucket-acceleration-status'] = 'true';\n    }\n    const res = await this.fetchBucket<GetBucketTransferAccelerationOutput>(\n      bucket,\n      'GET',\n      { [CommonQueryKey]: '' },\n      headers\n    );\n    return res;\n  } catch (error) {\n    return handleEmptyServerError<GetBucketTransferAccelerationOutput>(error, {\n      enableCatchEmptyServerError: this.opts.enableOptimizeMethodBehavior,\n      methodKey: 'getBucketTransferAcceleration',\n      defaultResponse: {\n        TransferAccelerationConfiguration: {\n          Enabled: 'false',\n          Status: TransferAccelerationStatusType.Terminated,\n        },\n      },\n    });\n  }\n}\n", "import TOSBase from '../base';\nimport { GetBucketLifecycleInput } from './lifecycle';\nimport { handleEmptyServerError } from '../../handleEmptyServerError';\n\nexport interface PutBucketAccessMonitorInput {\n  bucket: string;\n  status: 'Enabled' | 'Disabled';\n}\n\nexport interface PutBucketAccessMonitorOutput {}\n\n/**\n * @private unstable method\n */\nexport async function putBucketAccessMonitor(\n  this: TOSBase,\n  input: PutBucketAccessMonitorInput\n) {\n  const { bucket, status } = input;\n\n  return this.fetchBucket<PutBucketAccessMonitorOutput>(\n    bucket,\n    'PUT',\n    { accessmonitor: '' },\n    {},\n    {\n      Status: status,\n    }\n  );\n}\n\nexport interface GetBucketAccessMonitorInput {\n  bucket: string;\n}\n\nexport interface GetBucketAccessMonitorOutput {\n  Status?: 'Enabled' | 'Disabled';\n}\n\n/**\n * @private unstable method\n */\nexport async function getBucketAccessMonitor(\n  this: TOSBase,\n  input: GetBucketLifecycleInput\n) {\n  try {\n    const { bucket } = input;\n\n    return await this.fetchBucket<GetBucketAccessMonitorOutput>(\n      bucket,\n      'GET',\n      { accessmonitor: '' },\n      {}\n    );\n  } catch (error) {\n    return handleEmptyServerError<GetBucketAccessMonitorOutput>(error, {\n      enableCatchEmptyServerError: this.opts.enableOptimizeMethodBehavior,\n      methodKey: 'getBucketAccessMonitor',\n      defaultResponse: {},\n    });\n  }\n}\n", "import TOSBase from '../base';\nimport { AccessPointStatusType } from '../../TosExportEnum';\n\nexport interface AccessPoint {\n  Name: string; //规则名\n  Alias?: string; //别名\n  Status: AccessPointStatusType; //状态;\n  CreatedAt: number;\n  Regions: Array<{\n    Bucket: string;\n    BucketAccountId: number;\n    Region: string;\n  }>;\n}\n\nexport interface CreateMultiRegionAccessPointInput {\n  name: string;\n  regions: Array<{\n    Bucket: string;\n    BucketAccountId: string;\n  }>;\n  accountId: string;\n}\n\nexport interface CreateMultiRegionAccessPointOutput {}\n\n/**\n * @private unstable method\n */\nexport async function createMultiRegionAccessPoint(\n  this: TOSBase,\n  input: CreateMultiRegionAccessPointInput\n) {\n  const { accountId, name, regions } = input;\n\n  const res = await this.fetch<CreateMultiRegionAccessPointOutput>(\n    'POST',\n    '/mrap',\n    {\n      name,\n    },\n    {\n      'x-tos-account-id': accountId,\n    },\n    {\n      Name: name,\n      Regions: regions,\n    },\n    {}\n  );\n\n  return res;\n}\n\nexport interface GetMultiRegionAccessPointInput {\n  name: string;\n  accountId: string;\n}\n\nexport interface GetMultiRegionAccessPointOutput extends AccessPoint {}\n\n/**\n * @private unstable method\n */\nexport async function getMultiRegionAccessPoint(\n  this: TOSBase,\n  input: GetMultiRegionAccessPointInput\n) {\n  const { name, accountId } = input;\n  const res = await this.fetch<GetMultiRegionAccessPointOutput>(\n    'GET',\n    '/mrap',\n    {\n      name,\n    },\n    {\n      'x-tos-account-id': accountId,\n    },\n    {},\n    {}\n  );\n\n  return res;\n}\n\nexport interface ListMultiRegionAccessPointsInput {\n  accountId: string;\n  maxResults?: number;\n  nextToken?: string;\n}\n\nexport interface ListMultiRegionAccessPointsOutput {\n  AccessPoints: Array<AccessPoint>;\n  NextToken?: string;\n}\n\n/**\n * @private unstable method\n */\nexport async function listMultiRegionAccessPoints(\n  this: TOSBase,\n  input: ListMultiRegionAccessPointsInput\n) {\n  const { accountId, ...nextQuery } = input;\n  const res = await this.fetch<ListMultiRegionAccessPointsOutput>(\n    'GET',\n    '/mrap',\n    { ...nextQuery },\n    {\n      'x-tos-account-id': accountId,\n    },\n    {},\n    {}\n  );\n\n  return res;\n}\n\nexport interface GetMultiRegionAccessPointRoutesInput {\n  accountId: string;\n  alias: string;\n}\n\nexport interface AccessPointRoute {\n  Bucket: string;\n  Region: string;\n  TrafficDialPercentage: number;\n}\n\nexport interface GetMultiRegionAccessPointRoutesOutput {\n  Routes?: AccessPointRoute[];\n  Alias?: string;\n}\n\n/**\n * @private unstable method\n */\nexport async function getMultiRegionAccessPointRoutes(\n  this: TOSBase,\n  input: GetMultiRegionAccessPointRoutesInput\n) {\n  const { accountId, alias } = input;\n  const res = await this.fetch<GetMultiRegionAccessPointRoutesOutput>(\n    'GET',\n    '/mrap/routes',\n    {\n      alias,\n    },\n    {\n      'x-tos-account-id': accountId,\n    }\n  );\n  return res;\n}\n\nexport interface DeleteMultiRegionAccessPointInput {\n  accountId: string;\n  name: string;\n}\n\nexport interface DeleteMultiRegionAccessPointOutput {}\n\nexport async function deleteMultiRegionAccessPoint(\n  this: TOSBase,\n  input: DeleteMultiRegionAccessPointInput\n) {\n  const { name, accountId } = input;\n  const res = await this.fetch<DeleteMultiRegionAccessPointOutput>(\n    'DELETE',\n    '/mrap',\n    {\n      name,\n    },\n    {\n      'x-tos-account-id': accountId,\n    }\n  );\n  return res;\n}\n\nexport interface SubmitMultiRegionAccessPointRoutesInput {\n  accountId: string;\n  alias: string;\n  routes: AccessPointRoute[];\n}\n\nexport interface SubmitMultiRegionAccessPointRoutesOutput {}\n\nexport async function submitMultiRegionAccessPointRoutes(\n  this: TOSBase,\n  input: SubmitMultiRegionAccessPointRoutesInput\n) {\n  const { routes, accountId, alias } = input;\n  const res = await this.fetch<SubmitMultiRegionAccessPointRoutesOutput>(\n    'PATCH',\n    '/mrap/routes',\n    {\n      alias,\n    },\n    {\n      'x-tos-account-id': accountId,\n    },\n    {\n      Routes: routes,\n    }\n  );\n  return res;\n}\n", "import TOSBase from '../base';\nimport { MRAPMirrorBackRedirectPolicyType } from '../../TosExportEnum';\nimport { makeArrayProp } from '../../utils';\nimport { handleEmptyServerError } from '../../handleEmptyServerError';\n\nexport interface MirrorBackRule {\n  ID?: string;\n  Status: 'Enabled' | 'Disabled';\n  Condition?: {\n    HttpCode?: number[];\n    KeyPrefix?: string[];\n  };\n  Redirect?: {\n    RedirectPolicy: MRAPMirrorBackRedirectPolicyType;\n  };\n}\n\nexport interface PutMultiRegionAccessPointMirrorBackInput {\n  accountId: string;\n  alias: string;\n  rules: MirrorBackRule[];\n}\n\nexport interface PutMultiRegionAccessPointMirrorBackOutput {}\n\n/**\n * @private unstable method\n */\nexport const putMultiRegionAccessPointMirrorBack = async function (\n  this: TOSBase,\n  input: PutMultiRegionAccessPointMirrorBackInput\n) {\n  const { accountId, alias, rules } = input;\n\n  if (this.opts.enableOptimizeMethodBehavior && !rules.length) {\n    return deleteMultiRegionAccessPointMirrorBack.call(this, {\n      accountId,\n      alias,\n    });\n  }\n\n  const res = await this.fetch<PutMultiRegionAccessPointMirrorBackOutput>(\n    'PUT',\n    '/mrap/mirror',\n    {\n      alias,\n    },\n    {\n      'x-tos-account-id': accountId,\n    },\n    {\n      Rules: rules,\n    },\n    {\n      handleResponse() {\n        return {};\n      },\n    }\n  );\n  return res;\n};\n\nexport interface GetMultiRegionAccessPointMirrorBackInput {\n  accountId: string;\n  alias: string;\n}\n\nexport interface GetMultiRegionAccessPointMirrorBackOutput {\n  Rules: MirrorBackRule[];\n}\n\n/**\n * @private unstable method\n */\nexport const getMultiRegionAccessPointMirrorBack = async function (\n  this: TOSBase,\n  input: GetMultiRegionAccessPointMirrorBackInput\n) {\n  const { accountId, alias } = input;\n  try {\n    const res = await this.fetch<GetMultiRegionAccessPointMirrorBackOutput>(\n      'GET',\n      '/mrap/mirror',\n      {\n        alias,\n      },\n      {\n        'x-tos-account-id': accountId,\n      },\n      {},\n      {}\n    );\n    const arrayProp = makeArrayProp(res.data);\n    arrayProp('Rules');\n    return res;\n  } catch (error) {\n    return handleEmptyServerError<GetMultiRegionAccessPointMirrorBackOutput>(\n      error,\n      {\n        enableCatchEmptyServerError: this.opts.enableOptimizeMethodBehavior,\n        methodKey: 'getMultiRegionAccessPointMirrorBack',\n        defaultResponse: {\n          Rules: [],\n        },\n      }\n    );\n  }\n};\n\nexport interface DeleteMultiRegionAccessPointMirrorBackInput {\n  accountId: string;\n  alias: string;\n}\nexport interface DeleteMultiRegionAccessPointMirrorBackOutput {}\n\n/**\n * @private unstable method\n */\nexport const deleteMultiRegionAccessPointMirrorBack = async function (\n  this: TOSBase,\n  input: DeleteMultiRegionAccessPointMirrorBackInput\n) {\n  const { accountId, alias } = input;\n  const res = await this.fetch<DeleteMultiRegionAccessPointMirrorBackOutput>(\n    'DELETE',\n    '/mrap/mirror',\n    {\n      alias,\n    },\n    {\n      'x-tos-account-id': accountId,\n    },\n    {},\n    {\n      handleResponse() {\n        return {};\n      },\n    }\n  );\n  return res;\n};\n", "import TOSBase from '../base';\nimport { handleEmptyServerError } from '../../handleEmptyServerError';\n\nexport interface PutBucketPrivateM3U8Input {\n  bucket: string;\n  enable: boolean;\n}\n\nexport interface PutPrivateM3U8Output {}\n\n/**\n * @private unstable\n */\nexport async function putBucketPrivateM3U8(\n  this: TOSBase,\n  input: PutBucketPrivateM3U8Input\n) {\n  const { bucket, enable } = input;\n  return await this.fetchBucket(\n    bucket,\n    'PUT',\n    {\n      privateM3U8: '',\n    },\n    {},\n    {\n      Enable: enable,\n    }\n  );\n}\n\nexport interface GetBucketPrivateM3U8Input {\n  bucket: string;\n}\n\nexport interface GetBucketPrivateM3U8Output {\n  Enable: boolean;\n}\n\n/**\n * @private unstable\n */\nexport async function getBucketPrivateM3U8(\n  this: TOSBase,\n  input: GetBucketPrivateM3U8Input\n) {\n  const { bucket } = input;\n  try {\n    return await this.fetchBucket<GetBucketPrivateM3U8Output>(\n      bucket,\n      'GET',\n      {\n        privateM3U8: '',\n      },\n      {}\n    );\n  } catch (error) {\n    return handleEmptyServerError(error, {\n      enableCatchEmptyServerError: this.opts.enableOptimizeMethodBehavior,\n      methodKey: 'getBucketPrivateM3U8',\n      defaultResponse: {\n        Enable: false,\n      },\n    });\n  }\n}\n", "import { convertNormalCamelCase2Upper } from '../../utils';\nimport TOSBase from '../base';\n\nconst CommonQueryKey = 'trash';\n\nexport interface PutBucketTrashInput {\n  bucket?: string;\n  Trash: {\n    TrashPath: string;\n    CleanInterval: number;\n    Status: 'Enabled' | 'Disabled';\n  };\n}\n\nexport interface PutBucketTrashOutput {}\n\nexport async function putBucketTrash(\n  this: TOSBase,\n  input: PutBucketTrashInput\n) {\n  const { bucket, ...otherProps } = input;\n\n  const body = convertNormalCamelCase2Upper(otherProps);\n  return this.fetchBucket<PutBucketTrashOutput>(\n    bucket,\n    'PUT',\n    { [CommonQueryKey]: '' },\n    {},\n    {\n      ...body,\n    }\n  );\n}\n\nexport interface GetBucketTrashInput {\n  bucket?: string;\n}\n\nexport interface GetBucketTrashOutput {\n  Trash: {\n    TrashPath: string;\n    CleanInterval: number;\n    Status: 'Enabled' | 'Disabled';\n  };\n}\n\nexport async function getBucketTrash(\n  this: TOSBase,\n  input: GetBucketTrashInput\n) {\n  const { bucket } = input;\n  return await this.fetchBucket<GetBucketTrashOutput>(\n    bucket,\n    'GET',\n    { [CommonQueryKey]: '' },\n    {}\n  );\n}\n", "import TOSBase from './methods/base';\nimport {\n  listBuckets,\n  createBucket,\n  deleteBucket,\n  headBucket,\n  putBucketStorageClass,\n} from './methods/bucket/base';\nimport { getBucketAcl, putBucketAcl } from './methods/bucket/acl';\nimport {\n  getObject,\n  getObjectV2,\n  getObjectToFile,\n} from './methods/object/getObject';\nimport putObject, { putObjectFromFile } from './methods/object/putObject';\nimport { fetchObject, putFetchTask } from './methods/object/fetch';\nimport { listObjectVersions, listObjects } from './methods/object/listObjects';\nimport getPreSignedUrl from './methods/object/getPreSignedUrl';\nimport headObject from './methods/object/headObject';\nimport deleteObject from './methods/object/deleteObject';\nimport renameObject from './methods/object/renameObject';\nimport deleteMultiObjects from './methods/object/deleteMultiObjects';\nimport copyObject from './methods/object/copyObject';\nimport { getObjectAcl, putObjectAcl } from './methods/object/acl';\nimport {\n  abortMultipartUpload,\n  completeMultipartUpload,\n  createMultipartUpload,\n  listParts,\n  uploadPart,\n  listMultipartUploads,\n  uploadPartFromFile,\n} from './methods/object/multipart';\nimport appendObject from './methods/object/appendObject';\nimport setObjectMeta from './methods/object/setObjectMeta';\nimport { uploadPartCopy } from './methods/object/multipart/uploadPartCopy';\nimport uploadFile from './methods/object/multipart/uploadFile';\nimport { calculatePostSignature } from './methods/object/calculatePostSignature';\n\nimport { resumableCopyObject } from './methods/object/multipart/resumableCopyObject';\nimport {\n  deleteBucketPolicy,\n  getBucketPolicy,\n  putBucketPolicy,\n} from './methods/bucket/policy';\nimport {\n  getBucketVersioning,\n  putBucketVersioning,\n} from './methods/bucket/versioning';\nimport { preSignedPolicyURL } from './methods/object/preSignedPolicyURL';\nimport downloadFile from './methods/object/downloadFile';\nimport { getBucketLocation } from './methods/bucket/getBucketLocation';\nimport {\n  deleteBucketCORS,\n  getBucketCORS,\n  putBucketCORS,\n} from './methods/bucket/cors';\nimport { listObjectsType2 } from './methods/object/listObjectsType2';\nimport {\n  deleteBucketLifecycle,\n  getBucketLifecycle,\n  putBucketLifecycle,\n} from './methods/bucket/lifecycle';\nimport {\n  putBucketEncryption,\n  getBucketEncryption,\n  deleteBucketEncryption,\n} from './methods/bucket/encryption';\nimport {\n  deleteBucketMirrorBack,\n  getBucketMirrorBack,\n  putBucketMirrorBack,\n} from './methods/bucket/mirrorback';\nimport {\n  deleteObjectTagging,\n  getObjectTagging,\n  putObjectTagging,\n} from './methods/object/tagging';\nimport {\n  deleteBucketReplication,\n  getBucketReplication,\n  putBucketReplication,\n} from './methods/bucket/replication';\nimport {\n  deleteBucketWebsite,\n  getBucketWebsite,\n  putBucketWebsite,\n} from './methods/bucket/website';\nimport {\n  getBucketNotification,\n  putBucketNotification,\n} from './methods/bucket/notification';\nimport {\n  deleteBucketCustomDomain,\n  getBucketCustomDomain,\n  putBucketCustomDomain,\n} from './methods/bucket/customDomain';\nimport {\n  deleteBucketRealTimeLog,\n  getBucketRealTimeLog,\n  putBucketRealTimeLog,\n} from './methods/bucket/realTimeLog';\nimport {\n  deleteBucketInventory,\n  getBucketInventory,\n  listBucketInventory,\n  putBucketInventory,\n} from './methods/bucket/inventory';\nimport {\n  createJob,\n  deleteJob,\n  describeJob,\n  updateJobStatus,\n  updateJobPriority,\n  listJobs,\n} from './methods/batch';\nimport {\n  deleteBucketTagging,\n  getBucketTagging,\n  putBucketTagging,\n} from './methods/bucket/tag';\nimport {\n  getBucketPayByTraffic,\n  putBucketPayByTraffic,\n} from './methods/bucket/payByTraffic';\nimport {\n  getBucketImageStyle,\n  getBucketImageStyleList,\n  getBucketImageStyleListByName,\n  getImageStyleBriefInfo,\n  deleteBucketImageStyle,\n  putBucketImageStyle,\n  putBucketImageStyleSeparator,\n  putBucketImageProtect,\n  getBucketImageProtect,\n  getBucketImageStyleSeparator,\n} from './methods/bucket/img';\nimport { getBucketIntelligenttiering } from './methods/bucket/intelligenttiering';\nimport {\n  putBucketRename,\n  getBucketRename,\n  deleteBucketRename,\n} from './methods/bucket/rename';\nimport restoreObject from './methods/object/restoreObject';\nimport {\n  deleteStorageLens,\n  getStorageLens,\n  listStorageLens,\n  putStorageLens,\n} from './methods/storageLens';\nimport {\n  putBucketNotificationType2,\n  getBucketNotificationType2,\n} from './methods/bucket/notificationType2';\n\nimport putSymlink from './methods/object/putSymlink';\nimport getSymlink from './methods/object/getSymlink';\nimport {\n  getBucketTransferAcceleration,\n  putBucketTransferAcceleration,\n} from './methods/bucket/acceleration';\nimport {\n  getBucketAccessMonitor,\n  putBucketAccessMonitor,\n} from './methods/bucket/accessMonitor';\nimport {\n  getQosPolicy,\n  putQosPolicy,\n  deleteQosPolicy,\n} from './methods/qosPolicy';\nimport {\n  createMultiRegionAccessPoint,\n  getMultiRegionAccessPoint,\n  listMultiRegionAccessPoints,\n  getMultiRegionAccessPointRoutes,\n  deleteMultiRegionAccessPoint,\n  submitMultiRegionAccessPointRoutes,\n} from './methods/mrap';\nimport {\n  putMultiRegionAccessPointMirrorBack,\n  getMultiRegionAccessPointMirrorBack,\n  deleteMultiRegionAccessPointMirrorBack,\n} from './methods/mrap/mirror';\nimport {\n  putBucketPrivateM3U8,\n  getBucketPrivateM3U8,\n} from './methods/bucket/media';\nimport { getBucketTrash, putBucketTrash } from './methods/bucket/trash';\n\n// refer https://stackoverflow.com/questions/23876782/how-do-i-split-a-typescript-class-into-multiple-files\nexport class InnerClient extends TOSBase {\n  // bucket base\n  createBucket = createBucket;\n  headBucket = headBucket;\n  deleteBucket = deleteBucket;\n  listBuckets = listBuckets;\n  getBucketLocation = getBucketLocation;\n  putBucketStorageClass = putBucketStorageClass;\n\n  // bucket acl\n  getBucketAcl = getBucketAcl;\n  putBucketAcl = putBucketAcl;\n\n  // bucket policy\n  getBucketPolicy = getBucketPolicy;\n  putBucketPolicy = putBucketPolicy;\n  deleteBucketPolicy = deleteBucketPolicy;\n\n  // bucket versioning\n  getBucketVersioning = getBucketVersioning;\n  putBucketVersioning = putBucketVersioning;\n\n  // bucket cors\n  getBucketCORS = getBucketCORS;\n  putBucketCORS = putBucketCORS;\n  deleteBucketCORS = deleteBucketCORS;\n\n  // bucket lifecycle\n  putBucketLifecycle = putBucketLifecycle;\n  getBucketLifecycle = getBucketLifecycle;\n  deleteBucketLifecycle = deleteBucketLifecycle;\n\n  //bucket encryption\n  putBucketEncryption = putBucketEncryption;\n  getBucketEncryption = getBucketEncryption;\n  deleteBucketEncryption = deleteBucketEncryption;\n\n  // bucket mirror back\n  putBucketMirrorBack = putBucketMirrorBack;\n  getBucketMirrorBack = getBucketMirrorBack;\n  deleteBucketMirrorBack = deleteBucketMirrorBack;\n\n  // bucket replication\n  putBucketReplication = putBucketReplication;\n  getBucketReplication = getBucketReplication;\n  deleteBucketReplication = deleteBucketReplication;\n\n  // bucket website\n  putBucketWebsite = putBucketWebsite;\n  getBucketWebsite = getBucketWebsite;\n  deleteBucketWebsite = deleteBucketWebsite;\n\n  // bucket notification\n  putBucketNotification = putBucketNotification;\n  getBucketNotification = getBucketNotification;\n\n  // bucket customdomain\n  putBucketCustomDomain = putBucketCustomDomain;\n  getBucketCustomDomain = getBucketCustomDomain;\n  deleteBucketCustomDomain = deleteBucketCustomDomain;\n\n  // bucket timelog\n  putBucketRealTimeLog = putBucketRealTimeLog;\n  getBucketRealTimeLog = getBucketRealTimeLog;\n  deleteBucketRealTimeLog = deleteBucketRealTimeLog;\n\n  // bucket Inventory\n  getBucketInventory = getBucketInventory;\n  listBucketInventory = listBucketInventory;\n  putBucketInventory = putBucketInventory;\n  deleteBucketInventory = deleteBucketInventory;\n\n  // bucket tag\n  putBucketTagging = putBucketTagging;\n  getBucketTagging = getBucketTagging;\n  deleteBucketTagging = deleteBucketTagging;\n\n  // bucket pay by traffic\n  putBucketPayByTraffic = putBucketPayByTraffic;\n  getBucketPayByTraffic = getBucketPayByTraffic;\n\n  // bucket imgStyle\n  getBucketImageStyle = getBucketImageStyle;\n  getBucketImageStyleList = getBucketImageStyleList;\n  getBucketImageStyleListByName = getBucketImageStyleListByName;\n  getImageStyleBriefInfo = getImageStyleBriefInfo;\n  deleteBucketImageStyle = deleteBucketImageStyle;\n  putBucketImageStyle = putBucketImageStyle;\n  putBucketImageStyleSeparator = putBucketImageStyleSeparator;\n  putBucketImageProtect = putBucketImageProtect;\n  getBucketImageProtect = getBucketImageProtect;\n  getBucketImageStyleSeparator = getBucketImageStyleSeparator;\n\n  // bucket tag\n  putBucketRename = putBucketRename;\n  getBucketRename = getBucketRename;\n  deleteBucketRename = deleteBucketRename;\n\n  // bucket acceleration\n  putBucketTransferAcceleration = putBucketTransferAcceleration;\n  getBucketTransferAcceleration = getBucketTransferAcceleration;\n\n  // object base\n  copyObject = copyObject;\n  resumableCopyObject = resumableCopyObject;\n  deleteObject = deleteObject;\n  deleteMultiObjects = deleteMultiObjects;\n  getObject = getObject;\n  getObjectV2 = getObjectV2;\n  getObjectToFile = getObjectToFile;\n  getObjectAcl = getObjectAcl;\n  headObject = headObject;\n  appendObject = appendObject;\n  listObjects = listObjects;\n  renameObject = renameObject;\n  fetchObject = fetchObject;\n  putFetchTask = putFetchTask;\n\n  listObjectsType2 = listObjectsType2;\n\n  listObjectVersions = listObjectVersions;\n  putObject = putObject;\n  putObjectFromFile = putObjectFromFile;\n  putObjectAcl = putObjectAcl;\n  setObjectMeta = setObjectMeta;\n\n  // object multipart\n  createMultipartUpload = createMultipartUpload;\n  uploadPart = uploadPart;\n  uploadPartFromFile = uploadPartFromFile;\n  completeMultipartUpload = completeMultipartUpload;\n  abortMultipartUpload = abortMultipartUpload;\n  uploadPartCopy = uploadPartCopy;\n  listMultipartUploads = listMultipartUploads;\n  listParts = listParts;\n  downloadFile = downloadFile;\n\n  // object tagging\n  putObjectTagging = putObjectTagging;\n  getObjectTagging = getObjectTagging;\n  deleteObjectTagging = deleteObjectTagging;\n\n  // batch job\n  listJobs = listJobs;\n  createJob = createJob;\n  deleteJob = deleteJob;\n  describeJob = describeJob;\n  updateJobStatus = updateJobStatus;\n  updateJobPriority = updateJobPriority;\n\n  // restore object\n  restoreObject = restoreObject;\n  // object others\n  uploadFile = uploadFile;\n  getPreSignedUrl = getPreSignedUrl;\n  /**\n   * alias to preSignedPostSignature\n   */\n  calculatePostSignature = calculatePostSignature;\n  preSignedPostSignature = calculatePostSignature;\n  preSignedPolicyURL = preSignedPolicyURL;\n  // object intelligenttiering\n  getBucketIntelligenttiering = getBucketIntelligenttiering;\n\n  // storageLens\n  listStorageLens = listStorageLens;\n  deleteStorageLens = deleteStorageLens;\n  getStorageLens = getStorageLens;\n  putStorageLens = putStorageLens;\n\n  // bucket notificationV2\n  putBucketNotificationType2 = putBucketNotificationType2;\n  getBucketNotificationType2 = getBucketNotificationType2;\n\n  // symlink\n  putSymlink = putSymlink;\n  getSymlink = getSymlink;\n\n  // bucket accessMonitor\n  putBucketAccessMonitor = putBucketAccessMonitor;\n  getBucketAccessMonitor = getBucketAccessMonitor;\n\n  // qospolicy\n  putQosPolicy = putQosPolicy;\n  getQosPolicy = getQosPolicy;\n  deleteQosPolicy = deleteQosPolicy;\n\n  // mrap\n  createMultiRegionAccessPoint = createMultiRegionAccessPoint;\n  getMultiRegionAccessPoint = getMultiRegionAccessPoint;\n  listMultiRegionAccessPoints = listMultiRegionAccessPoints;\n  getMultiRegionAccessPointRoutes = getMultiRegionAccessPointRoutes;\n  deleteMultiRegionAccessPoint = deleteMultiRegionAccessPoint;\n  submitMultiRegionAccessPointRoutes = submitMultiRegionAccessPointRoutes;\n\n  // mrap mirror back\n  putMultiRegionAccessPointMirrorBack = putMultiRegionAccessPointMirrorBack;\n  getMultiRegionAccessPointMirrorBack = getMultiRegionAccessPointMirrorBack;\n  deleteMultiRegionAccessPointMirrorBack =\n    deleteMultiRegionAccessPointMirrorBack;\n\n  // pm3u8\n  putBucketPrivateM3U8 = putBucketPrivateM3U8;\n  getBucketPrivateM3U8 = getBucketPrivateM3U8;\n  // hns trash\n  putBucketTrash = putBucketTrash;\n  getBucketTrash = getBucketTrash;\n}\n", "import axios from 'axios';\nimport { TosServerError, TosServerCode } from './TosServerError';\nimport { TosClientError } from './TosClientError';\nimport { isCancelError as isCancel } from './utils';\nimport { UploadEventType } from './methods/object/multipart/uploadFile';\nimport {\n  ACLType,\n  StorageClassType,\n  MetadataDirectiveType,\n  AzRedundancyType,\n  PermissionType,\n  GranteeType,\n  CannedType,\n  HttpMethodType,\n  LifecycleStatusType,\n  StatusType,\n  RedirectType,\n  StorageClassInheritDirectiveType,\n  TierType,\n  VersioningStatusType,\n  ReplicationStatusType,\n  AccessPointStatusType,\n  TransferAccelerationStatusType,\n  MRAPMirrorBackRedirectPolicyType\n} from './TosExportEnum';\nimport { CancelError } from './CancelError';\nimport { ResumableCopyEventType } from './methods/object/multipart/resumableCopyObject';\nimport { DownloadEventType } from './methods/object/downloadFile';\nimport { DataTransferType } from './interface';\nimport { ShareLinkClient } from './ShareLinkClient';\nimport { InnerClient } from './InnerClient';\nimport { createDefaultRateLimiter } from './universal/rate-limiter';\n\nconst CancelToken = axios.CancelToken;\n// for export\nclass TosClient extends InnerClient {\n  // for umd bundle\n  static TosServerError = TosServerError;\n  static isCancel = isCancel;\n  static CancelError = CancelError;\n  static TosServerCode = TosServerCode;\n  static TosClientError = TosClientError;\n  static CancelToken = CancelToken;\n  static ACLType = ACLType;\n  static StorageClassType = StorageClassType;\n  static MetadataDirectiveType = MetadataDirectiveType;\n  static AzRedundancyType = AzRedundancyType;\n  static PermissionType = PermissionType;\n  static GranteeType = GranteeType;\n  static CannedType = CannedType;\n  static HttpMethodType = HttpMethodType;\n  static LifecycleStatusType = LifecycleStatusType;\n  static StatusType = StatusType;\n  static RedirectType = RedirectType;\n  static StorageClassInheritDirectiveType = StorageClassInheritDirectiveType;\n  static TierType = TierType;\n  static VersioningStatusType = VersioningStatusType;\n  static createDefaultRateLimiter = createDefaultRateLimiter;\n  static DataTransferType = DataTransferType;\n  static UploadEventType = UploadEventType;\n  static DownloadEventType = DownloadEventType;\n  static ResumableCopyEventType = ResumableCopyEventType;\n  static ReplicationStatusType = ReplicationStatusType;\n  /** @private unstable */\n  static AccessPointStatusType = AccessPointStatusType;\n  /** @private unstable */\n  static TransferAccelerationStatusType = TransferAccelerationStatusType;\n  /** @private unstable */\n  static MRAPMirrorBackRedirectPolicyType = MRAPMirrorBackRedirectPolicyType;\n  /** @private unstable */\n  static ShareLinkClient = ShareLinkClient;\n}\n\nexport default TosClient;\n\nexport { TosClient as TOS, TosClient };\nexport {\n  TosServerError,\n  TosClientError,\n  isCancel,\n  CancelError,\n  TosServerCode,\n  CancelToken,\n  ACLType,\n  StorageClassType,\n  MetadataDirectiveType,\n  AzRedundancyType,\n  PermissionType,\n  GranteeType,\n  CannedType,\n  HttpMethodType,\n  LifecycleStatusType,\n  RedirectType,\n  StatusType,\n  StorageClassInheritDirectiveType,\n  TierType,\n  VersioningStatusType,\n  createDefaultRateLimiter,\n  DataTransferType,\n  UploadEventType,\n  DownloadEventType,\n  ResumableCopyEventType,\n  ReplicationStatusType,\n  AccessPointStatusType,\n  TransferAccelerationStatusType,\n  ShareLinkClient,\n  MRAPMirrorBackRedirectPolicyType\n};\n\n// TODO: hack for umd\nif (\n  process.env.TARGET_ENVIRONMENT === 'browser' &&\n  process.env.BUILD_FORMAT === 'umd'\n) {\n  // @ts-ignore\n  if (typeof window !== 'undefined') {\n    // @ts-ignore\n    window.TOS = TosClient;\n    // @ts-ignore\n    window.TosClient = TosClient;\n  }\n  if (typeof global !== 'undefined') {\n    // @ts-ignore\n    global.TOS = TosClient;\n    // @ts-ignore\n    global.TosClient = TosClient;\n  }\n  if (typeof globalThis !== 'undefined') {\n    // @ts-ignore\n    globalThis.TOS = TosClient;\n    // @ts-ignore\n    globalThis.TosClient = TosClient;\n  }\n}\n"], "names": ["TosServerCode", "TosServerError", "Error", "constructor", "response", "data", "super", "Message", "code", "statusCode", "headers", "requestId", "id2", "Object", "setPrototypeOf", "this", "prototype", "Code", "status", "TosClientError", "message", "CancelError", "makeArrayProp", "obj", "key", "value", "get", "Array", "isArray", "set", "makeConvertProp", "convertMethod", "final<PERSON><PERSON><PERSON>", "target", "map", "it", "keys", "reduce", "acc", "covertCamelCase2Kebab", "camelCase", "replace", "toLowerCase", "convertNormalCamelCase2Upper", "normalCamelCase", "toUpperCase", "slice", "getSortedQueryString", "query", "searchParts", "sort", "for<PERSON>ach", "push", "encodeURIComponent", "join", "normalizeHeadersKey", "headers1", "headers2", "headers3", "new<PERSON>ey", "normalizeProxy", "proxy", "url", "needProxyParams", "async", "safeAwait", "p", "err", "isBlob", "Blob", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "obj2QueryStr", "v", "vStr", "isCancelError", "gmtDateOrStr", "toUTCString", "requestHeadersMap", "projectName", "encodingType", "cacheControl", "contentDisposition", "contentLength", "contentMD5", "contentSHA256", "contentEncoding", "contentLanguage", "contentType", "expires", "range", "ifMatch", "ifModifiedSince", "ifNoneMatch", "ifUnmodifiedSince", "acl", "grantFullControl", "grantRead", "grantReadAcp", "grantWrite", "grantWriteAcp", "serverSideEncryption", "serverSideDataEncryption", "ssecAlgorithm", "ssecKey", "ssecKeyMD5", "copySourceRange", "copySourceIfMatch", "copySourceIfModifiedSince", "copySourceIfNoneMatch", "copySourceIfUnmodifiedSince", "copySourceSSECAlgorithm", "copySourceSSECKey", "copySourceSSECKeyMD5", "metadataDirective", "meta", "prev", "websiteRedirectLocation", "storageClass", "azRedundancy", "trafficLimit", "callback", "callback<PERSON><PERSON>", "allowSameActionOverlap", "String", "symLinkTargetKey", "symLinkTargetBucket", "forbidOverwrite", "bucketType", "recursiveMkdir", "requestQueryMap", "versionId", "process", "saveBucket", "saveObject", "responseCacheControl", "responseContentDisposition", "responseContentEncoding", "responseContentLanguage", "responseContentType", "responseExpires", "fillRequestHeaders", "length", "set<PERSON>neHeader", "k", "confV", "oriValue", "entries", "paramsSerializer", "params", "qs", "stringify", "getNormalDataFromError", "checkCRC64WithHeaders", "crc", "serverCRC64", "console", "warn", "crcStr", "getCrc64", "HttpHeader", "makeSerialAsyncTask", "makeTask", "lastTask", "Promise", "resolve", "then", "try<PERSON><PERSON><PERSON>", "stream", "destroy", "destroyed", "createMultipartUpload", "input", "normalizeObjectInput", "setObjectContentTypeHeader", "_fetchObject", "uploads", "calculateSafePartSize", "totalSize", "expectPartSize", "showWarning", "partSize", "minSize", "Math", "ceil", "listParts", "uploadId", "<PERSON><PERSON><PERSON><PERSON>", "ret", "arrayProp", "mimeTypes", "3gp", "7z", "abw", "ai", "aif", "aifc", "aiff", "alc", "amr", "anx", "apk", "appcache", "art", "asc", "asf", "aso", "asx", "atom", "atomcat", "atomsrv", "au", "avi", "awb", "axa", "axv", "b", "bak", "bat", "bcpio", "bib", "bin", "bmp", "boo", "book", "brf", "bsd", "c", "c++", "c3d", "cab", "cac", "cache", "cap", "cascii", "cat", "cbin", "cbr", "cbz", "cc", "cda", "cdf", "cdr", "cdt", "cdx", "cdy", "cef", "cer", "chm", "chrt", "cif", "class", "cls", "cmdf", "cml", "cod", "com", "cpa", "cpio", "cpp", "cpt", "cr2", "crl", "crt", "crw", "csd", "csf", "csh", "csm", "csml", "css", "csv", "ctab", "ctx", "cu", "cub", "cxf", "cxx", "d", "dav<PERSON>", "dcm", "dcr", "ddeb", "dif", "diff", "dir", "djv", "djvu", "dl", "dll", "dmg", "dms", "doc", "docm", "docx", "dot", "dotm", "dotx", "dv", "dvi", "dx", "dxr", "emb", "embl", "eml", "eot", "eps", "eps2", "eps3", "epsf", "epsi", "erf", "es", "etx", "exe", "ez", "fb", "fbdoc", "fch", "fchk", "fig", "flac", "fli", "flv", "fm", "frame", "frm", "gal", "gam", "gamin", "gan", "gau", "gcd", "gcf", "gcg", "gen", "gf", "gif", "gjc", "gjf", "gl", "gnumeric", "gpt", "gsf", "gsm", "gtar", "gz", "h", "h++", "hdf", "hh", "hin", "hpp", "hqx", "hs", "hta", "htc", "htm", "html", "hwp", "hxx", "ica", "ice", "ico", "ics", "icz", "ief", "iges", "igs", "iii", "info", "inp", "ins", "iso", "isp", "ist", "istr", "jad", "jam", "jar", "java", "jdx", "jmz", "jng", "jnlp", "jp2", "jpe", "jpeg", "jpf", "jpg", "jpg2", "jpm", "jpx", "js", "json", "kar", "kil", "kin", "kml", "kmz", "kpr", "kpt", "ksp", "kwd", "kwt", "latex", "lha", "lhs", "lin", "lsf", "lsx", "ltx", "ly", "lyx", "lzh", "lzx", "m3g", "m3u", "m3u8", "m4a", "maker", "man", "mbox", "mcif", "mcm", "mdb", "me", "mesh", "mid", "midi", "mif", "mkv", "mm", "mmd", "mmf", "mml", "mmod", "mng", "moc", "mol", "mol2", "moo", "mop", "mopcrt", "mov", "movie", "mp2", "mp3", "mp4", "mpc", "mpe", "mpeg", "mpega", "mpg", "mpga", "mph", "mpv", "ms", "msh", "msi", "mvb", "mxf", "mxu", "nb", "nbp", "nc", "nef", "nwc", "o", "oda", "odb", "odc", "odf", "odg", "odi", "odm", "odp", "ods", "odt", "oga", "ogg", "ogv", "ogx", "old", "one", "onepkg", "onetmp", "onetoc2", "opf", "opus", "orc", "orf", "otf", "otg", "oth", "otp", "ots", "ott", "oza", "p7r", "pac", "pas", "pat", "patch", "pbm", "pcap", "pcf", "pcf.Z", "pcx", "pdb", "pdf", "pfa", "pfb", "pfr", "pgm", "pgn", "pgp", "php", "php3", "php3p", "php4", "php5", "phps", "pht", "phtml", "pk", "pl", "pls", "pm", "png", "pnm", "pot", "potm", "potx", "ppam", "ppm", "pps", "ppsm", "ppsx", "ppt", "pptm", "pptx", "prf", "prt", "ps", "psd", "py", "pyc", "pyo", "qgs", "qt", "qtl", "ra", "ram", "rar", "ras", "rb", "rd", "rdf", "rdp", "rgb", "rhtml", "rm", "roff", "ros", "rpm", "rss", "rtf", "rtx", "rxn", "scala", "sce", "sci", "sco", "scr", "sct", "sd", "sd2", "sda", "sdc", "sdd", "sds", "sdw", "ser", "sfd", "sfv", "sgf", "sgl", "sh", "shar", "shp", "shtml", "shx", "sid", "sig", "sik", "silo", "sis", "sisx", "sit", "sitx", "skd", "skm", "skp", "skt", "sldm", "sldx", "smi", "smil", "snd", "spc", "spl", "spx", "sql", "src", "srt", "stc", "std", "sti", "stw", "sty", "sv4cpio", "sv4crc", "svg", "svgz", "sw", "swf", "swfl", "sxc", "sxd", "sxg", "sxi", "sxm", "sxw", "t", "tar", "taz", "tcl", "tex", "texi", "texinfo", "text", "tgf", "tgz", "thmx", "tif", "tiff", "tk", "tm", "torrent", "tr", "ts", "tsp", "tsv", "ttf", "ttl", "txt", "uls", "ustar", "val", "vcard", "vcd", "vcf", "vcs", "vmd", "vms", "vrm", "vrml", "vsd", "vss", "vst", "vsw", "wad", "wasm", "wav", "wax", "wbmp", "wbxml", "webm", "wk", "wm", "wma", "wmd", "wml", "wmlc", "wmls", "wmlsc", "wmv", "wmx", "wmz", "woff", "wp5", "wpd", "wrl", "wsc", "wvx", "wz", "x3d", "x3db", "x3dv", "xbm", "xcf", "xcos", "xht", "xhtml", "xlam", "xlb", "xls", "xlsb", "xlsm", "xlsx", "xlt", "xltm", "xltx", "xml", "xpi", "xpm", "xsd", "xsl", "xslt", "xspf", "xtel", "xul", "xwd", "xyz", "xz", "zip", "crcModule", "reset", "[object Object]", "update", "_value", "combineCrc64", "rateLimiter", "_capacity", "_rate", "_rateLimiter", "createDefaultRateLimiter", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "validateObjectName", "getSize", "body", "size", "getNewBodyConfig", "config1", "makeRetryStream", "undefined", "getEmitReadBodyConfig", "beforeRetry", "getCRCBodyConfig", "getCopySourceHeaderValue", "srcBucket", "srcKey", "getRestoreInfoFromHeaders", "headerStoreValue", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ExpiryDate", "split", "_split$", "_split$$split", "OngoingRequest", "trim", "restoreInfo", "RestoreStatus", "RestoreParam", "ExpiryDays", "HeaderRestoreExpiryDays", "Number", "RequestDate", "HeaderRestoreRequestDate", "Tier", "HeaderRestoreTier", "DataTransferType", "TOS", "createDebug", "BROWSER_NEED_DELETE_HEADERS", "getEnc", "coding", "cryptoEncUtf8", "cryptoEncBase64", "cryptoEncHex", "decode", "decoding", "toString", "hashMd5", "cryptoHashMd5", "crypto", "cryptoHmacSha256", "cryptoHashSha256", "str", "encoding", "parse", "hmacSha256", "hashSha256", "_uploadPart", "partNumber", "enableContentMD5", "toFixed", "totalSizeValid", "dataTransferStatusChange", "progress", "consumedBytes", "triggerDataTransfer", "type", "rwOnceBytes", "totalBytes", "progressValue", "Succeed", "bodyConfig", "dataTransferCallback", "n", "Rw", "enableCRC", "opts", "Started", "res", "handleResponse", "ETag", "etag", "serverSideEncryptionKeyId", "hashCrc64ecma", "axiosOpts", "__retryConfig__", "onUploadProgress", "event", "loaded", "task", "Failed", "uploadPart", "call", "uploadPartFromFile", "completeMultipartUpload", "result", "VersionID", "Bucket", "bucket", "Location", "HashCrc64ecma", "Key", "Callback<PERSON><PERSON><PERSON>", "JSON", "completeAll", "parts", "x-tos-complete-all", "Parts", "eTag", "PartNumber", "UploadEventType", "ABORT_ERROR_STATUS_CODE", "uploadFile", "cancelToken", "isCancel", "reason", "fileStats", "fileSize", "file", "checkpointRichInfo", "checkpoint", "record", "_checkpointRichInfo$r", "file_info", "last_modified", "file_size", "_checkpointRichInfo$r2", "mtimeMs", "Date", "_checkpointRichInfo$r3", "part_size", "tasks", "allTasks", "i", "offset", "currPartSize", "min", "getAllTasks", "initConsumedBytes", "parts_info", "filter", "is_completed", "consumedBytesForProgress", "recordedTasks", "recordedTaskMap", "Map", "part_number", "getCheckpointContent", "checkpointContent", "upload_id", "triggerUploadEvent", "e", "uploadEventChange", "filePath", "checkpointFile", "TriggerProgressEventType", "triggerProgressEvent", "uploadPartSucceed", "start", "writeCheckpointFile", "updateAfterUploadPart", "uploadPartRes", "existRecordTask", "hash_crc64ecma", "uploadPartInfo", "UploadPartFailed", "includes", "UploadPartAborted", "UploadPartSucceed", "uploadedPartSet", "Set", "has", "multipartRes", "UploadId", "filePathIsPlaceholder", "_checkpointRichInfo$f", "getDefaultCheckpointFilePath", "CreateMultipartUploadSucceed", "_err", "CreateMultipartUploadFailed", "firstErr", "index", "all", "from", "taskNum", "currentIndex", "curTask", "consumedBytesThisTask", "getBody", "end", "make", "content-length", "x-tos-server-side-encryption-customer-algorithm", "x-tos-server-side-encryption-customer-key", "x-tos-server-side-encryption-customer-key-md5", "getLastStream", "CompleteMultipartUploadFailed", "CompleteMultipartUploadSucceed", "completeMultipartUploadSucceed", "rmCheckpointFile", "cp", "sortedPartsInfo", "_cp$parts_info", "a", "part", "combineCRCInParts", "handleTasks", "ACLType", "StorageClassType", "MetadataDirectiveType", "AzRedundancyType", "PermissionType", "GranteeType", "CannedType", "HttpMethodType", "StorageClassInheritDirectiveType", "ReplicationStatusType", "LifecycleStatusType", "RedirectType", "StatusType", "TierType", "VersioningStatusType", "AccessPointStatusType", "TransferAccelerationStatusType", "MRAPMirrorBackRedirectPolicyType", "ResumableCopyEventType", "headObject", "normalizedInput", "ReplicationStatus", "HeaderReplicationStatus", "RestoreInfo", "uploadPartCopy", "copySource", "srcVersionID", "copySourceRangeStart", "copySourceRangeEnd", "copyRange", "SSECAlgorithm", "SSECKeyMD5", "copyObject", "resumableCopyObject", "objectStats", "srcVersionId", "objectSize", "copy_source_object_info", "object_size", "copy_source_range_end", "copy_source_range_start", "copyEventListener", "rangeStart", "rangeEnd", "copyPartInfo", "UploadPartCopyFailed", "UploadPartCopyAborted", "UploadPartCopySucceed", "cloneDeep", "Boolean", "x-tos-copy-source", "x-tos-copy-source-if-match", "secure", "endpoint", "handleEmptyObj", "x-tos-copy-source-range", "sourceCRC64", "actualCrc64", "getObject", "responseType", "BROWSER_DATATYPE", "getObjectV2", "dataType", "environment", "supportDataTypes", "checkSupportDataType", "setOneKey", "fill<PERSON>e<PERSON><PERSON><PERSON><PERSON>", "onDownloadProgress", "total", "resHeaders", "newData", "actualRes", "content", "lastModified", "getObjectToFile", "DownloadEventType", "downloadFile", "isDefaultPort", "port", "v4Identifier", "SignersV4", "opt", "credentials", "options", "signature", "expiredAt", "credString", "credentialString", "datetime", "algorithm", "GetAccessKey", "signedHeaders", "authorization", "signature<PERSON><PERSON>er", "getDateTime", "header", "host", "endpoints", "hexEncodedBodyHash", "securityToken", "startsWith", "path", "getEncodePath", "sign", "gnrCopySig", "getSignature", "getSignatureQuery", "X-Tos-Algorithm", "X-Tos-Content-Sha256", "X-Tos-Credential", "X-Tos-Date", "X-Tos-Expires", "X-Tos-SignedHeaders", "getSignaturePolicyQuery", "X-Tos-Policy", "policy", "<PERSON><PERSON><PERSON>", "getSigningKey", "substr", "stringToSign", "toISOString", "createScope", "region", "serviceName", "date", "kDate", "GetSecretKey", "kRegion", "kService", "canonicalString", "canonicalStringPolicy", "hexEncodedHash", "string", "method", "canonicalHeaders", "needSignHeaders", "getNeedSignedHeaders", "canonicalHeader<PERSON><PERSON>ues", "values", "encodeAll", "tmpPath", "ISigV4Credentials", "secretAccessKey", "accessKeyId", "platFormName", "mpAdapter", "config", "transformRequestOption", "requestOption", "request", "wx", "bind", "swan", "dd", "httpRequest", "my", "getRequest", "reject", "requestTask", "requestData", "requestHeaders", "mpRequestOption", "buildURL", "buildFullPath", "baseURL", "timeout", "success", "mpResponse", "statusText", "transformResponse", "settle", "fail", "error", "errMsg", "indexOf", "createError", "transformError", "complete", "auth", "username", "password", "Authorization", "block", "charCode", "idx", "output", "char<PERSON>t", "charCodeAt", "encode", "utils", "_val", "_header", "promise", "cancel", "abort", "isJSONstr", "transformConfig", "TOSBase", "_opts", "axiosInst", "userAgent", "httpAgent", "httpsAgent", "getObjectPath", "actualBucket", "<PERSON><PERSON>ey", "mimeType", "getObjectInputKey", "autoRecognizeContentType", "lastDotIndex", "lastIndexOf", "extName", "lookupMimeType", "normalizeOpts", "getUserAgent", "maxRetryCount", "axios", "create", "defaults", "withCredentials", "max<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON><PERSON><PERSON>", "maxRedirects", "validateStatus", "decompress", "transitional", "silentJSONParsing", "forcedJSONParsing", "clarifyTimeoutError", "interceptors", "use", "ensureHeaders", "_v$response", "handleResponseHeader", "decodedValue", "func", "decodeURI", "safeSync", "sArr", "valueArr", "match", "decodedValueArr", "j", "ch", "encodedCh", "isAxiosError", "_error$response", "retryConfig", "retryCount", "_error$response$heade", "isNetworkError", "isCanRetryStatusCode", "retrySignature", "signOpt", "sigInst", "log", "nextConfig", "makeAxiosInst", "mustKeysErrorStr", "_default", "defaultValue", "enableVerifySSL", "requestTimeout", "connectionTimeout", "maxConnections", "idleConnectionTime", "requestAdapter", "getAdapter", "needMd5", "md5String", "newPath", "subdomainBucket", "forcePathStyle", "isCustomDomain", "test", "header2", "encodeHeadersValue", "signv4", "stsToken", "accessKeySecret", "signatureHeaders", "reqHeaders", "reqOpts", "normalizedProxy", "proxyHost", "proxyPort", "protocol", "Infinity", "logReqOpts", "adapter", "__retrySignature__", "_err$response", "_err$response$headers", "fetch", "subdomain", "normalizeBucketInput", "window", "location", "uni", "uniappAdapter", "listObjects", "fetchBucket", "listObjectVersions", "versions", "listObjectsType2", "listOnlyOnce", "max<PERSON>eys", "listObjectsType2Once", "KeyCount", "IsTruncated", "NextContinuationToken", "Contents", "concat", "CommonPrefixes", "continuationToken", "list-type", "ShareLinkClient", "modifyAxiosInst", "parsedPolicyUrlVal", "origin", "addQueryStr", "search", "shareLinkClientOpts", "initParsedPolicyUrlVal", "matched", "policyUrl", "listBuckets", "createBucket", "deleteBucket", "headBucket", "ProjectName", "HeaderProjectName", "putBucketStorageClass", "x-tos-storage-class", "putBucketAcl", "aclBody", "getBucketAcl", "putObject", "_putObject", "putObjectFromFile", "fetchObject", "URL", "IgnoreSameKey", "ignore<PERSON><PERSON><PERSON><PERSON>", "ContentMD5", "putFetchTask", "fetchTask", "getPreSignedUrl", "alternativeEndpoint", "newHost", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "objectKeyPath", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_key", "kebab<PERSON>ey", "deleteObject", "skipTrash", "recursive", "renameObject", "rename", "name", "deleteMultiObjects", "Quiet", "quiet", "Objects", "objects", "VersionId", "delete", "getObjectAcl", "putObjectAcl", "abortMultipartUpload", "listMultipartUploads", "appendObject", "preHashCrc64ecma", "append", "nextAppendOffset", "setObjectMeta", "metadata", "calculatePostSignature", "expiresIn", "fields", "conditions", "expirationDateStr", "getDateTimeStr", "valueOf", "dateStr", "date8Str", "substring", "service", "requestStr", "addedInForm", "x-tos-algorithm", "x-tos-date", "x-tos-credential", "policyStr", "expiration", "policyBase64", "defaultEmptyMethodMap", "getBucketCustomDomain", "getBucketIntelligenttiering", "getBucketInventory", "listBucketInventory", "getBucketMirrorBack", "getBucketNotification", "getBucketPolicy", "getBucketRealTimeLog", "getBucketReplication", "getBucketTagging", "getBucketWebsite", "handleEmptyServerError", "enableCatchEmptyServerError", "<PERSON><PERSON><PERSON>", "defaultResponse", "putBucketPolicy", "enableOptimizeMethodBehavior", "Statement", "deleteBucketPolicy", "Condition", "key2", "Version", "getBucketVersioning", "versioning", "putBucketVersioning", "Status", "preSignedPolicyURL", "normalizeInput", "validateConditions", "queryStr", "getSignedURLForList", "additionalQuery", "str2", "q", "getSignedURLForGetOrHead", "keyP<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "normalizedConditions", "operator", "getBucketLocation", "getBucketCORS", "cors", "CORSRules", "putBucketCORS", "deleteBucketCORS", "putBucketLifecycle", "rules", "deleteBucketLifecycle", "lifecycle", "Rules", "getBucketLifecycle", "AllowSameActionOverlap", "putBucketEncryption", "rule", "encryption", "Content-MD5", "Rule", "getBucketEncryption", "deleteBucketEncryption", "putBucketMirrorBack", "deleteBucketMirrorBack", "mirror", "putObjectTagging", "tagSet", "tagging", "TagSet", "getObjectTagging", "deleteObjectTagging", "putBucketReplication", "role", "deleteBucketReplication", "replication", "Role", "ruleId", "putBucketWebsite", "otherProps", "website", "RoutingRules", "deleteBucketWebsite", "putBucketNotification", "notification", "CloudFunctionConfigurations", "RocketMQConfigurations", "putBucketCustomDomain", "customdomain", "CustomDomainRules", "deleteBucketCustomDomain", "customDomain", "putBucketRealTimeLog", "realtimeLog", "deleteBucketRealTimeLog", "ScheduleFrequency", "IncludedObjectVersions", "InventoryOptionalFields", "StringOp", "DateOp", "IpOp", "QuotaType", "req", "inventory", "id", "continuation-token", "InventoryConfigurations", "deleteBucketInventory", "putBucketInventory", "inventoryConfiguration", "Id", "createJob", "accountId", "x-tos-account-id", "listJobs", "maxResults", "others", "updateJobPriority", "jobId", "JobId", "priority", "updateJobStatus", "requestedJobStatus", "statusUpdateReason", "deleteJob", "<PERSON><PERSON><PERSON>", "putBucketTagging", "Tags", "deleteBucketTagging", "putBucketPayByTraffic", "payByTraffic", "getBucketPayByTraffic", "getImageStyleBriefInfo", "imageStyleBriefInfo", "BucketName", "ImageStyleBriefInfo", "getBucketImageStyleList", "imageStyle", "ImageStyles", "getBucketImageStyleListByName", "styleName", "imageStyleContent", "getBucketImageStyle", "putBucketImageStyle", "styleObjectPrefix", "Content", "deleteBucketImageStyle", "putBucketImageProtect", "originalImageProtect", "getBucketImageProtect", "putBucketImageStyleSeparator", "Separator", "SeparatorSuffix", "imageStyleSeparator", "getBucketImageStyleSeparator", "intelligenttiering", "putBucketRename", "getBucketRename", "deleteBucketRename", "restoreObject", "restore", "listStorageLens", "deleteStorageLens", "getStorageLens", "putStorageLens", "rest", "putBucketNotificationType2", "notification_v2", "getBucketNotificationType2", "putSymlink", "_putSymlink", "symlink", "getSymlink", "_getSymlink", "SymlinkTargetKey", "SymlinkTargetBucket", "LastModified", "putBucketTransferAcceleration", "transferAcceleration", "getBucketTransferAcceleration", "getStatus", "TransferAccelerationConfiguration", "Enabled", "Terminated", "putBucketAccessMonitor", "accessmonitor", "getBucketAccessMonitor", "getQosPolicy", "putQosPolicy", "restParams", "deleteQosPolicy", "createMultiRegionAccessPoint", "regions", "Name", "Regions", "getMultiRegionAccessPoint", "listMultiRegionAccessPoints", "getMultiRegionAccessPointRoutes", "alias", "deleteMultiRegionAccessPoint", "submitMultiRegionAccessPointRoutes", "routes", "Routes", "putMultiRegionAccessPointMirrorBack", "deleteMultiRegionAccessPointMirrorBack", "getMultiRegionAccessPointMirrorBack", "putBucketPrivateM3U8", "enable", "privateM3U8", "Enable", "getBucketPrivateM3U8", "putBucketTrash", "trash", "getBucketTrash", "InnerClient", "preSignedPostSignature", "CancelToken", "TosClient"], "mappings": "8IA4DYA,2lBAjDCC,UAAuBC,MA+BlCC,YAAYC,GACV,MAAMC,KAAEA,GAASD,EACjBE,MAAMD,EAAKE,cA7BNC,iBAKAH,iBAIAI,uBAIAC,oBAMAC,sBAMAC,WAOLC,OAAOC,eAAeC,KAAMd,EAAee,WAE3CD,KAAKV,KAAOA,EACZU,KAAKP,KAAOH,EAAKY,KACjBF,KAAKN,WAAaL,EAASc,OAC3BH,KAAKL,QAAUN,EAASM,QACxBK,KAAKJ,UAAYP,EAASM,QAAQ,oBAClCK,KAAKH,IAAMR,EAASM,QAAQ,gBAMpBV,EAAAA,wBAAAA,uDAEVA,wBACAA,8BACAA,mCACAA,wCACAA,kCACAA,gDACAA,oCACAA,kCACAA,wCACAA,kCACAA,kCACAA,4CACAA,gCACAA,8DACAA,2EACAA,wDACAA,wEACAA,8CACAA,gDACAA,iEACAA,0CACAA,wBACAA,gCACAA,kCACAA,gCACAA,sCACAA,0CACAA,8CACAA,gDACAA,kCACAA,oCACAA,oDACAA,wDACAA,0DACAA,+CACAA,oDACAA,0CACAA,kCACAA,oCACAA,0BACAA,8BACAA,oBACAA,sCACAA,gCACAA,oCACAA,gCACAA,oCACAA,oBACAA,8DACAA,oCACAA,oCACAA,sCACAA,8CACAA,sDACAA,sCACAA,8BACAA,oCACAA,gCACAA,gCACAA,kDACAA,4BACAA,wCACAA,kCACAA,8CACAA,8DACAA,gBACAA,0DChIWmB,UAAuBjB,MAClCC,YAAYiB,GACVd,MAAMc,GAGNP,OAAOC,eAAeC,KAAMI,EAAeH,kBCLlCK,UAAoBnB,MAC/BC,YAAYiB,GACVd,MAAMc,GAGNP,OAAOC,eAAeC,KAAMM,EAAYL,YCcrC,MAAMM,EAAiBC,GAAkBC,IAC9C,GAAW,MAAPD,GAA8B,iBAARA,EACxB,OAGF,MAAME,EAAQC,EAAIH,EAAKC,GAClBG,MAAMC,QAAQH,IACjBI,EAAIN,EAAKC,EAAc,MAATC,EAAgB,GAAK,CAACA,KAIlCK,EAAmBC,IACvB,MAAMC,EAA4BC,GAC5BN,MAAMC,QAAQK,GACTA,EAAOC,IAAKC,GAAOH,EAAYG,IAGlB,iBAAXF,EACFF,EAAcE,GAGD,iBAAXA,GAAiC,MAAVA,EAEpBpB,OAAOuB,KAAKH,GAAQI,OAAO,CAACC,EAAUd,KAEhDc,EADgBN,EAAYR,IACZS,EAAeT,GACxBc,GACN,IAIEL,EAGT,OAAOD,GAGIO,EAAwBT,EAAiBU,GAC7CA,EAAUC,QAAQ,SAAU,OAAOC,eAS/BC,EAA+Bb,EACzCc,GACQA,EAAgB,GAAGC,cAAgBD,EAAgBE,MAAM,IAIvDC,EAAwBC,IACnC,MAAMC,EAAwB,GAQ9B,OAPApC,OAAOuB,KAAKY,GACTE,OACAC,QAAS3B,IACRyB,EAAYG,QACPC,mBAAmB7B,MAAQ6B,mBAAmBL,EAAMxB,SAGtDyB,EAAYK,KAAK,MAGbC,EACX7C,IAEA,MAAM8C,EAAoB9C,GAAW,GAC/B+C,EAAoB,GAC1B5C,OAAOuB,KAAKoB,GAAUL,QAAS3B,IACR,MAAjBgC,EAAShC,KACXiC,EAASjC,GAAOgC,EAAShC,MAI7B,MAAMkC,EAAoB,GAM1B,OALA7C,OAAOuB,KAAKqB,GAAUN,QAAS3B,IAC7B,MAAMmC,EAASnC,EAAIkB,cACnBgB,EAASC,GAAUF,EAASjC,KAGvBkC,GAkCIE,EAAkBC,UAe7B,MAdqB,iBAAVA,IACTA,EAAQ,CACNC,IAAKD,IAKPA,GAC0B,gBAA1BA,YAAOE,mBAGPF,EAAME,iBAAkB,GAGnBF,GAGFG,eAAeC,EACpBC,GAEA,IAEE,MAAO,CAAC,WADQA,GAEhB,MAAOC,GACP,MAAO,CAACA,EAAK,gBAaDC,EAAO7C,GACrB,MAAuB,oBAAT8C,MAAwB9C,aAAe8C,cAGvCC,EAAS/C,GACvB,MAAyB,oBAAXgD,QAA0BhD,aAAegD,gBAezCC,EAAaC,GAC3B,OAAKA,EAGE5D,OAAOuB,KAAKqC,GAChBvC,IAAKV,IACJ,MAAMkD,KAAUD,EAAEjD,GAClB,SAAU6B,mBAAmB7B,MAAQ6B,mBAAmBqB,OAEzDpB,KAAK,KAPC,YAUKqB,EAAcR,GAC5B,OAAOA,aAAe9C,EAGxB,MAKMuD,EAAgBH,GACH,iBAANA,EACFA,EAEFA,EAAEI,cAGEC,EAGT,CACFC,YAAa,qBACbC,aAAc,gBACdC,aAAc,gBACdC,mBAAoB,sBACpBC,cAAe,iBACfC,WAAY,cACZC,cAAe,uBACfC,gBAAiB,mBACjBC,gBAAiB,mBACjBC,YAAa,eACbC,QAAS,CAAC,UAxBkBhB,GACrBA,EAAEI,eAwBTa,MAAO,QAEPC,QAAS,WACTC,gBAAiB,CAAC,oBAAqBhB,GACvCiB,YAAa,gBACbC,kBAAmB,CAAC,sBAAuBlB,GAE3CmB,IAAK,YACLC,iBAAkB,2BAClBC,UAAW,mBACXC,aAAc,uBACdC,WAAY,oBACZC,cAAe,wBAEfC,qBAAsB,+BACtBC,yBAA0B,oCAC1BC,cAAe,kDACfC,QAAS,4CACTC,WAAY,gDAEZC,gBAAiB,0BACjBC,kBAAmB,6BACnBC,0BAA2B,CACzB,sCACAhC,GAEFiC,sBAAuB,kCACvBC,4BAA6B,wCAC7BC,wBACE,8DACFC,kBAAmB,wDACnBC,qBACE,4DAEFC,kBAAmB,2BACnBC,KAAO1C,GACE5D,OAAOuB,KAAKqC,GAAGpC,OAAO,CAAC+E,EAAM5F,KAClC4F,gBAAmB5F,MAAYiD,EAAEjD,GAC1B4F,GACN,IAELC,wBAAyB,kCACzBC,aAAc,sBACdC,aAAc,sBACdC,aAAc,sBACdC,SAAU,iBACVC,YAAa,qBACbC,uBAAwB,CAAC,kCAAoClD,GAAMmD,OAAOnD,IAC1EoD,iBAAkB,uBAClBC,oBAAqB,uBACrBC,gBAAiB,yBACjBC,WAAY,oBACZC,eAAgB,yBAILC,EAGT,CACFC,UAAW,YACXC,QAAS,gBACTC,WAAY,oBACZC,WAAY,oBAEZC,qBAAsB,yBACtBC,2BAA4B,+BAC5BC,wBAAyB,4BACzBC,wBAAyB,4BACzBC,oBAAqB,wBACrBC,gBAAiB,CAAC,mBAAqBnE,GAAYA,EAAEI,gBAGvD,SAAgBgE,EACdpE,EAEArC,GAEA,IAAKA,EAAK0G,OACR,OAGF,MAAMpI,EAAU+D,EAAE/D,SAAW,GAG7B,SAASqI,EAAaC,EAAWvE,GACb,MAAd/D,EAAQsI,KACVtI,EAAQsI,GAAKvE,GAJjBA,EAAE/D,QAAUA,EAQZ0B,EAAKe,QAAS6F,IACZ,MAAMC,EAAQnE,EAAkBkE,GAChC,IAAKC,EAEH,MAAM,IAAI9H,OACH6H,8CAIT,MAAME,EAAWzE,EAAEuE,GACnB,GAAgB,MAAZE,EACF,OAIF,GAAqB,iBAAVD,EACT,OAAOF,EAAaE,KAFCC,GAKvB,GAAIvH,MAAMC,QAAQqH,GAGhB,OAAOF,EAFQE,EAAM,GACJA,EAAM,GAAGC,IAI5B,MAAM3H,EAAM0H,EAAMC,GAClBrI,OAAOsI,QAAQ5H,GAAK4B,QAAQ,EAAE6F,EAAGvE,MAC/BsE,EAAaC,EAAGvE,OAkDf,MAAM2E,EAAoBC,GACxBC,EAAGC,UAAUF,YAGNG,EACdnJ,EACA8D,GAEA,MAAO,CACL9D,KAAAA,EACAI,WAAY0D,EAAI1D,WAChBC,QAASyD,EAAIzD,QACbC,UAAWwD,EAAIxD,UACfC,IAAKuD,EAAIvD,cAoBG6I,EAAsBC,EAAsBhJ,GAC1D,MAAMiJ,EAAcjJ,EAAQ,wBAC5B,GAAmB,MAAfiJ,EAOF,YALEC,QAAQC,KACN,gKAON,MAAMC,EAAwB,iBAARJ,EAAmBA,EAAMA,EAAIK,WACnD,GAAID,IAAWH,EACb,MAAM,IAAIxI,8CACoCwI,mBAA6BG,wBAK/E,IAIYE,GAAZ,SAAYA,GACVA,+BACAA,iCACAA,mCACAA,qCACAA,2BACAA,iDACAA,6BACAA,0CACAA,0CACAA,yDACAA,mBACAA,kCAZF,CAAYA,IAAAA,OAoBL,MAAMC,EAAuBC,IAClC,IAAIC,EAAWC,QAAQC,UACvB,OAAOrG,UACLmG,EAAWA,EAASG,KAAK,IAAMJ,KACxBC,IAmCEI,EAAa,CACxBC,EAOArG,KAEIqG,GAAU,YAAaA,GAAoC,mBAAnBA,EAAOC,SAC7C,cAAeD,IAAWA,EAAOE,WACnCF,EAAOC,QAAQtG,ICrddH,eAAe2G,EAEpBC,GAEAA,EAAQ7J,KAAK8J,qBAAqBD,GAClC,MAAMlK,EAAU6C,EAAoBqH,EAAMlK,SA+B1C,OA9BAkK,EAAMlK,QAAUA,EAChBmI,EAAmB+B,EAAO,CACxB,eACA,eACA,qBACA,kBACA,kBACA,cACA,UAEA,MACA,mBACA,YACA,eACA,gBAEA,gBACA,UACA,aACA,uBACA,2BAEA,OACA,0BACA,eACA,oBAGF7J,KAAK+J,2BAA2BF,EAAOlK,GAEhCK,KAAKgK,aACVH,EACA,OACA,CAAEI,QAAS,IACXtK,EACA,ICxEJ,MAGauK,EAAwB,CACnCC,EACAC,EACAC,GAAc,KAEd,IAAIC,EAAWF,EACXA,EATuC,UAUzCE,EAVyC,QAWrCD,GACFxB,QAAQC,iCACsBwB,oFAIlC,MAAMC,EAAUC,KAAKC,KAAKN,EAhBG,KA0B7B,OATIC,EAAiBG,IACnBD,EAAWC,EACPF,GACFxB,QAAQC,iCACsBwB,mFAK3BA,GAGFrH,eAAeyH,EAAyBb,GAC7C,MAAMc,SAAeA,KAAaC,GAAcf,EAC1CgB,QAAY7K,KAAKgK,aACrBH,EACA,MACA,CACEc,SAAAA,KACGnJ,EAAsBoJ,IAE3B,IAKF,OAHkBrK,EAAcsK,EAAIvL,KACpCwL,CAAU,SAEHD,EC1ET,MAAaE,EAAgD,CAC3DC,MAAO,aACPC,KAAM,8BACNC,IAAK,wBACLC,GAAI,yBACJC,IAAK,eACLC,KAAM,eACNC,KAAM,eACNC,IAAK,qBACLC,IAAK,YACLC,IAAK,sBACLC,IAAK,0CACLC,SAAU,sBACVC,IAAK,aACLC,IAAK,aACLC,IAAK,iBACLC,IAAK,8BACLC,IAAK,iBACLC,KAAM,uBACNC,QAAS,0BACTC,QAAS,2BACTC,GAAI,cACJC,IAAK,kBACLC,IAAK,eACLC,IAAK,gBACLC,IAAK,gBACLC,EAAG,uBACHC,IAAK,sBACLC,IAAK,8BACLC,MAAO,sBACPC,IAAK,gBACLC,IAAK,2BACLC,IAAK,iBACLC,IAAK,aACLC,KAAM,sBACNC,IAAK,aACLC,IAAK,uBACLC,EAAG,cACHC,MAAO,gBACPC,IAAK,oBACLC,IAAK,oBACLC,IAAK,mBACLC,MAAO,mBACPC,IAAK,+BACLC,OAAQ,2BACRC,IAAK,gCACLC,KAAM,2BACNC,IAAK,oBACLC,IAAK,oBACLC,GAAI,gBACJC,IAAK,oBACLC,IAAK,oBACLC,IAAK,oBACLC,IAAK,4BACLC,IAAK,iBACLC,IAAK,6BACLC,IAAK,iBACLC,IAAK,oBACLC,IAAK,sBACLC,KAAM,uBACNC,IAAK,iBACLC,MAAO,sBACPC,IAAK,aACLC,KAAM,kBACNC,IAAK,iBACLC,IAAK,0BACLC,IAAK,8BACLC,IAAK,qBACLC,KAAM,qBACNC,IAAK,gBACLC,IAAK,6BACLC,IAAK,oBACLC,IAAK,0BACLC,IAAK,6BACLC,IAAK,oBACLC,IAAK,eACLC,IAAK,uBACLC,IAAK,oBACLC,IAAK,kBACLC,KAAM,kBACNC,IAAK,WACLC,IAAK,WACLC,KAAM,2BACNC,IAAK,iBACLC,GAAI,uBACJC,IAAK,2BACLC,IAAK,iBACLC,IAAK,gBACLC,EAAG,cACHC,SAAU,2BACVC,IAAK,oBACLC,IAAK,yBACLC,KAAM,wCACNC,IAAK,WACLC,KAAM,cACNC,IAAK,yBACLC,IAAK,iBACLC,KAAM,iBACNC,GAAI,WACJC,IAAK,8BACLC,IAAK,gCACLC,IAAK,oBACLC,IAAK,qBACLC,KAAM,mDACNC,KAAM,0EACNC,IAAK,qBACLC,KAAM,mDACNC,KAAM,0EACNC,GAAI,WACJC,IAAK,oBACLC,GAAI,sBACJC,IAAK,yBACLC,IAAK,gCACLC,KAAM,gCACNC,IAAK,iBACLC,IAAK,gCACLC,IAAK,yBACLC,KAAM,yBACNC,KAAM,yBACNC,KAAM,yBACNC,KAAM,yBACNC,IAAK,oBACLC,GAAI,yBACJC,IAAK,gBACLC,IAAK,8BACLC,GAAI,2BACJC,GAAI,sBACJC,MAAO,sBACPC,IAAK,iCACLC,KAAM,iCACNC,IAAK,qBACLC,KAAM,aACNC,IAAK,YACLC,IAAK,cACLC,GAAI,sBACJC,MAAO,sBACPC,IAAK,sBACLC,IAAK,0BACLC,IAAK,0BACLC,MAAO,0BACPC,IAAK,6BACLC,IAAK,4BACLC,IAAK,iBACLC,IAAK,oCACLC,IAAK,2BACLC,IAAK,qBACLC,GAAI,uBACJC,IAAK,YACLC,IAAK,4BACLC,IAAK,4BACLC,GAAI,WACJC,SAAU,yBACVC,IAAK,yBACLC,IAAK,qBACLC,IAAK,cACLC,KAAM,qBACNC,GAAI,mBACJC,EAAG,cACHC,MAAO,gBACPC,IAAK,oBACLC,GAAI,gBACJC,IAAK,iBACLC,IAAK,gBACLC,IAAK,2BACLC,GAAI,iBACJC,IAAK,kBACLC,IAAK,mBACLC,IAAK,YACLC,KAAM,YACNC,IAAK,oBACLC,IAAK,gBACLC,IAAK,oBACLC,IAAK,0BACLC,IAAK,2BACLC,IAAK,gBACLC,IAAK,gBACLC,IAAK,YACLC,KAAM,aACNC,IAAK,aACLC,IAAK,uBACLC,KAAM,qBACNC,IAAK,0BACLC,IAAK,gCACLC,IAAK,8BACLC,IAAK,gCACLC,IAAK,qBACLC,KAAM,qBACNC,IAAK,mCACLC,IAAK,oBACLC,IAAK,2BACLC,KAAM,cACNC,IAAK,sBACLC,IAAK,qBACLC,IAAK,cACLC,KAAM,+BACNC,IAAK,YACLC,IAAK,aACLC,KAAM,aACNC,IAAK,YACLC,IAAK,aACLC,KAAM,YACNC,IAAK,YACLC,IAAK,YACLC,GAAI,yBACJC,KAAM,mBACNC,IAAK,aACLnX,IAAK,uBACLoX,IAAK,6BACLC,IAAK,sBACLC,IAAK,uCACLC,IAAK,mCACLC,IAAK,2BACLC,IAAK,2BACLC,IAAK,wBACLC,IAAK,sBACLC,IAAK,sBACLC,MAAO,sBACPC,IAAK,oBACLC,IAAK,0BACLC,IAAK,qBACLC,IAAK,iBACLC,IAAK,iBACLC,IAAK,aACLC,GAAI,kBACJC,IAAK,oBACLC,IAAK,oBACLC,IAAK,oBACLC,IAAK,kBACLC,IAAK,kBACLC,KAAM,wBACNC,IAAK,aACLC,MAAO,sBACPC,IAAK,0BACLC,KAAM,mBACNC,KAAM,mBACNC,IAAK,yBACLC,IAAK,uBACLC,GAAI,yBACJC,KAAM,aACNC,IAAK,aACLC,KAAM,aACNC,IAAK,oBACLC,IAAK,mBACLC,GAAI,yBACJC,IAAK,8BACLC,IAAK,uBACLC,IAAK,cACLC,KAAM,8BACNC,IAAK,cACLC,IAAK,aACLC,IAAK,yBACLC,KAAM,kBACNC,IAAK,uBACLC,IAAK,yBACLC,OAAQ,yBACRC,IAAK,kBACLC,MAAO,oBACPC,IAAK,aACLC,IAAK,aACLC,IAAK,YACLC,IAAK,yBACLC,IAAK,aACLC,KAAM,aACNC,MAAO,aACPC,IAAK,aACLC,KAAM,aACNC,IAAK,uBACLC,IAAK,mBACLC,GAAI,yBACJC,IAAK,aACLC,IAAK,oBACLC,IAAK,uBACLC,IAAK,kBACLC,IAAK,oBACLC,GAAI,0BACJC,IAAK,0BACLC,GAAI,uBACJC,IAAK,oBACLC,IAAK,oBACLC,EAAG,uBACHC,IAAK,kBACLC,IAAK,8CACLC,IAAK,2CACLC,IAAK,6CACLC,IAAK,8CACLC,IAAK,2CACLC,IAAK,iDACLC,IAAK,kDACLC,IAAK,iDACLC,IAAK,0CACLC,IAAK,YACLC,IAAK,YACLC,IAAK,YACLC,IAAK,kBACLC,IAAK,sBACLC,IAAK,sBACLC,OAAQ,sBACRC,OAAQ,sBACRC,QAAS,sBACTC,IAAK,gCACLC,KAAM,YACNC,IAAK,eACLC,IAAK,sBACLC,IAAK,wBACLC,IAAK,uDACLC,IAAK,8CACLC,IAAK,2DACLC,IAAK,0DACLC,IAAK,mDACLC,IAAK,+BACLhb,EAAG,gBACHib,IAAK,kCACLC,IAAK,oCACLC,IAAK,gBACLC,IAAK,2BACLC,MAAO,cACPC,IAAK,0BACLC,KAAM,+BACNC,IAAK,yBACLC,QAAS,yBACTC,IAAK,YACLC,IAAK,iBACLC,IAAK,kBACLC,IAAK,qBACLC,IAAK,qBACLC,IAAK,yBACLC,IAAK,2BACLC,IAAK,0BACLC,IAAK,4BACLC,IAAK,2BACLC,KAAM,4BACNC,MAAO,yCACPC,KAAM,4BACNC,KAAM,4BACNC,KAAM,kCACNC,IAAK,2BACLC,MAAO,2BACPC,GAAI,uBACJC,GAAI,cACJC,IAAK,gBACLC,GAAI,cACJC,IAAK,YACLC,IAAK,0BACLC,IAAK,aACLC,KAAM,yDACNC,KAAM,wEACNC,KAAM,sDACNC,IAAK,0BACLC,IAAK,gCACLC,KAAM,0DACNC,KAAM,yEACNC,IAAK,gCACLC,KAAM,6DACNC,KAAM,4EACNC,IAAK,yBACLC,IAAK,6BACLC,GAAI,yBACJC,IAAK,oBACLC,GAAI,gBACJC,IAAK,4BACLC,IAAK,4BACLC,IAAK,qBACLC,GAAI,kBACJC,IAAK,gCACLC,GAAI,uBACJC,IAAK,uBACLC,IAAK,kBACLC,IAAK,qBACLC,GAAI,qBACJC,GAAI,wBACJC,IAAK,sBACLC,IAAK,oBACLC,IAAK,cACLC,MAAO,6BACPC,GAAI,uBACJC,KAAM,sBACNC,IAAK,oBACLC,IAAK,uCACLC,IAAK,wBACLC,IAAK,kBACLC,IAAK,gBACLC,IAAK,yBACLC,MAAO,eACPC,IAAK,uBACLC,IAAK,uBACLC,IAAK,eACLC,IAAK,4BACLC,IAAK,iBACLC,GAAI,wBACJC,IAAK,cACLC,IAAK,oCACLC,IAAK,oCACLC,IAAK,uCACLC,IAAK,qCACLC,IAAK,sCACLC,IAAK,qCACLC,IAAK,qCACLC,IAAK,aACLC,IAAK,uBACLC,IAAK,6CACLC,GAAI,mBACJC,KAAM,qBACNC,IAAK,qBACLC,MAAO,YACPC,IAAK,qBACLC,IAAK,gBACLC,IAAK,4BACLC,IAAK,sBACLC,KAAM,aACNC,IAAK,kCACLC,KAAM,oBACNC,IAAK,wBACLC,KAAM,wBACNC,IAAK,qBACLC,IAAK,qBACLC,IAAK,qBACLC,IAAK,qBACLC,KAAM,sDACNC,KAAM,qEACNC,IAAK,uBACLC,KAAM,uBACNC,IAAK,cACLC,IAAK,0BACLC,IAAK,6BACLC,IAAK,YACLC,IAAK,oBACLC,IAAK,4BACLC,IAAK,aACLC,IAAK,wCACLC,IAAK,wCACLC,IAAK,2CACLC,IAAK,0CACLC,IAAK,aACLC,QAAS,wBACTC,OAAQ,uBACRC,IAAK,gBACLC,KAAM,gBACNC,GAAI,uBACJC,IAAK,gCACLC,KAAM,gCACNC,IAAK,+BACLC,IAAK,+BACLC,IAAK,wCACLC,IAAK,kCACLC,IAAK,+BACLC,IAAK,iCACLC,EAAG,sBACHC,IAAK,oBACLC,IAAK,gCACLC,IAAK,oBACLC,IAAK,aACLC,KAAM,wBACNC,QAAS,wBACTC,KAAM,aACNC,IAAK,qBACLC,IAAK,gCACLC,KAAM,iCACNC,IAAK,aACLC,KAAM,aACNC,GAAI,aACJC,GAAI,eACJC,QAAS,2BACTC,GAAI,sBACJC,GAAI,aACJC,IAAK,sBACLC,IAAK,4BACLC,IAAK,wBACLC,IAAK,cACLC,IAAK,aACLC,IAAK,YACLC,MAAO,sBACPC,IAAK,8BACLC,MAAO,aACPC,IAAK,uBACLC,IAAK,aACLC,IAAK,mBACLC,IAAK,iBACLC,IAAK,4BACLC,IAAK,iBACLC,KAAM,aACNC,IAAK,wBACLC,IAAK,wBACLC,IAAK,wBACLC,IAAK,wBACLC,IAAK,qBACLC,KAAM,mBACNC,IAAK,YACLC,IAAK,iBACLC,KAAM,qBACNC,MAAO,4BACPC,KAAM,aACNC,GAAI,oBACJC,GAAI,gBACJC,IAAK,iBACLC,IAAK,uBACLC,IAAK,mBACLC,KAAM,2BACNC,KAAM,yBACNC,MAAO,iCACPC,IAAK,iBACLC,IAAK,iBACLC,IAAK,uBACLC,KAAM,wBACNC,IAAK,iCACLC,IAAK,8BACLC,IAAK,aACLC,IAAK,iBACLC,IAAK,iBACLC,GAAI,sBACJC,IAAK,gBACLC,KAAM,mBACNC,KAAM,iBACNC,IAAK,kBACLC,IAAK,oBACLC,KAAM,4BACNC,IAAK,wBACLC,MAAO,wBACPC,KAAM,iDACNC,IAAK,2BACLC,IAAK,2BACLC,KAAM,wDACNC,KAAM,iDACNC,KAAM,oEACNC,IAAK,2BACLC,KAAM,oDACNC,KAAM,uEACNC,IAAK,kBACLC,IAAK,0BACLC,IAAK,kBACLC,IAAK,kBACLC,IAAK,uBACLC,KAAM,uBACNC,KAAM,uBACNC,KAAM,kBACNC,IAAK,kCACLC,IAAK,sBACLC,IAAK,iBACLC,GAAI,mBACJC,IAAK,mBCvhBP,IAAIC,EAAY,KAKdA,sBCLF,MACEC,SAEgBC,mBACd,MAAM,IAAItsB,EAAe,gDAG3BusB,OAAOC,GACL,MAAM,IAAIxsB,EAAe,mDDA7B,MAAMysB,aAAOA,GAAiBL,EED9B,IAAIM,EAAc,KAIhBA,oDCbAC,EACAC,GAEA,MAAM7tB,MAAM,4EAGZ8tB,GAEA,MAAM9tB,MAAM,2EDQN+tB,GAAsDJ,EED9D,IAAYK,WCoBIC,GAAmBvjB,GAEjC,IAD6B,iBAAVA,EAAqBA,EAAQA,EAAMpJ,KAC9CsH,OAAS,EACf,MAAM,IAAI3H,EACR,mEAKUitB,GAAQC,EAAe3tB,GACrC,GAAI4D,EAAS+pB,GACX,OAAOA,EAAKvlB,OAEd,GAAI1E,EAAOiqB,GACT,OAAOA,EAAKC,KAEd,GAAI5tB,GAAWA,EAAQ,kBAAmB,CACxC,MAAM+D,GAAK/D,EAAQ,kBACnB,GAAI+D,GAAK,EACP,OAAOA,EAGX,OAAO,KA0HFT,eAAeuqB,GACpB3jB,GAEA,MAAM4jB,EAhGR,UAAmEH,KACjEA,IAaE,OALAA,KAHuCA,EAIvCI,qBAAiBC,GAsFHC,CAAsB/jB,GAGtC,OA/CF5G,gBAAoEqqB,KAClEA,EADkEO,YAElEA,EAFkEH,gBAGlEA,IAIE,MAAO,CACLJ,KAAAA,EACAO,YAAAA,EACAH,gBAAAA,GAoCYI,CADhBjkB,EAAQ,IAAKA,KAAU4jB,aAKTM,GAAyBC,EAAmBC,GAC1D,UAAWD,KAAa1rB,mBAAmB2rB,MD9K7C,SAAYd,GACVA,gCACAA,sDACAA,wDACAA,yCACAA,yCACAA,qDANF,CAAYA,IAAAA,OCiML,MAAMe,GAA6BvuB,IACxC,IAAKA,EAAS,OACd,MAAMwuB,QAAmBxuB,SAAAA,EAAUwtB,EAAUiB,eAE7C,GAAID,EAAkB,CAAA,UAKpB,MAAME,2BACHF,EAAAA,EAAoB,IAAIG,MAAM,MAAM,WAArCC,EAAyCD,gBAAzCC,EAAyCD,MAAQ,aAAjDE,EAAwD,MAAM,GAC1DC,EDnMkC,kCCoMtCN,SAAAA,EAAkBO,QACdC,EAA2B,CAC/BC,cAAe,CACbH,eAAAA,EACAJ,WAAAA,IAGgB,MASpB,OATII,IACFE,EAAYE,aAAe,CACzBC,WAAYnvB,EAAQwtB,EAAU4B,yBAC1BC,OAAOrvB,EAAQwtB,EAAU4B,0BACzB,EACJE,qBAAatvB,EAAQwtB,EAAU+B,6BAA6B,GAC5DC,KAAMxvB,EAAQwtB,EAAUiC,qBAGrBT,QCxLCU,IAAAA,GAAAA,2BAAAA,sDAEVA,iBACAA,2BACAA,yBCzDK,MAAMC,GAAMC,EAAY,OCkDzBC,GAA8B,CAAC,iBAAkB,aAAc,QC3CrE,SAASC,GAAOC,GACd,OAAQA,GACN,IAAK,QACH,OAAOC,EACT,IAAK,SACH,OAAOC,EACT,IAAK,MACH,OAAOC,EACT,QACE,MAAM,IAAIzvB,EAAe,gCAI/B,SAAS0vB,GAAOpsB,EAAQqsB,GACtB,OAAKA,EAIErsB,EAAEssB,SAASP,GAAOM,IAHhBrsB,EAMX,MAeausB,GAAU,SACrB5vB,EACA0vB,GAEA,GAAIxsB,EAASlD,GACX,MAAM,IAAID,EAAe,6CAG3B,OAAO0vB,GAAOI,EAAc7vB,GAAU0vB,ICtCxC,IAAII,GAAS,KAIXA,8BDWwB,SACxB1vB,EACAJ,EACA0vB,GAEA,OAAOD,GAAOM,EAAiB/vB,EAASI,GAAMsvB,eAGtB,SACxB1vB,EACA0vB,GAEA,OAAOD,GAAOO,EAAiBhwB,GAAU0vB,qBActB,SACnBO,EACAC,GAEA,OAAOd,GAAOc,GAAUC,MAAMF,cAGP,SACvBA,EACAP,GAEA,OAAON,GAAOM,GAAUvnB,UAAU8nB,KC7CpC,iBAAQG,cAAYC,WAAYT,SAASO,aAAOhoB,IAAc2nB,GCyDvDltB,eAAe0tB,GAA2B9mB,GAC/C,MAAMc,SAAEA,EAAFimB,WAAYA,EAAZtD,KAAwBA,EAAxBuD,iBAA8BA,GAAmB,GAAUhnB,EAC3DlK,EAAU6C,EAAoBqH,EAAMlK,SAC1CkK,EAAMlK,QAAUA,EAChBmI,EAAmB+B,EAAO,CACxB,eACA,gBACA,UACA,eAGF,MAAM0jB,EAAOF,GAAQC,GACjBC,GAAqC,MAA7B5tB,EAAQ,oBAClBA,EAAQ,kBAAoB4tB,EAAKuD,QAAQ,IAEvCD,GAA8C,MAA1BlxB,EAAQ,gBAoB5BkJ,QAAQC,8CAIZ,MAAMqB,EAAYkjB,GAAQxjB,EAAMyjB,KAAM3tB,GAChCoxB,EAA8B,MAAb5mB,EAClB4mB,IAAmBlnB,EAAMmnB,2BAA4BnnB,EAAMonB,UAC9DpoB,QAAQC,qJAKV,IAAIooB,EAAgB,EACpB,MAAMF,yBAAEA,EAAFC,SAA4BA,GAAapnB,EACzCsnB,EAAsB,CAC1BC,EACAC,EAAsB,KAGtB,IAAKN,GAAkBM,EAAc,EACnC,OAEF,IAAKL,IAA6BC,EAChC,OAEFC,GAAiBG,QAEjBL,GAAAA,EAA2B,CACzBI,KAAAA,EACAC,YAAAA,EACAH,cAAAA,EACAI,WAAYnnB,IAGd,MAAMonB,EACc,IAAdpnB,EACEinB,IAAS/B,yBAAiBmC,QACrB,EAEF,EAEFN,EAAgB/mB,EAEH,IAAlBonB,EACEH,IAAS/B,yBAAiBmC,gBAC5BP,GAAAA,EAAWM,UAKbN,GAAAA,EAAWM,IAGTE,QAAmBjE,GAAiB,CACxCF,KAAMzjB,EAAMyjB,KACZoE,qBAAuBC,GAAMR,EAAoB9B,yBAAiBuC,GAAID,GACtE9D,YAAahkB,EAAMgkB,YACnBH,gBAAiB7jB,EAAM6jB,gBACvBmE,UAAW7xB,KAAK8xB,KAAKD,UACrB/E,YAAajjB,EAAMijB,cAGrBqE,EAAoB9B,yBAAiB0C,SACrC,MA4CO3uB,EAAK4uB,SAAa9uB,EA5CZD,WACX,MAAM+uB,QAAYhyB,KAAKgK,aACrBH,EACA,MACA,CAAE+mB,WAAAA,EAAYjmB,SAAAA,GACdhL,EACA8xB,EAAWnE,KACX,CACE2E,eAAiBD,KACfpB,WAAAA,EACAsB,KAAMF,EAAIryB,QAAQwyB,KAClB7sB,qBAAsB0sB,EAAIryB,QAAQ,gCAClC4F,yBACEysB,EAAIryB,QAAQ,qCACdyyB,0BACEJ,EAAIryB,QAAQ,2CACd6F,cACEwsB,EAAIryB,QAAQ,mDACd+F,WACEssB,EAAIryB,QAAQ,iDACd0yB,cAAeL,EAAIryB,QAAQ,0BAE7B2yB,UAAW,CACTC,gBAAkB,CAChB1E,YAAa,KACXqD,EAAgB,QAChBO,EAAW5D,aAAX4D,EAAW5D,eAEbH,gBAAiB+D,EAAW/D,iBAE9B8E,iBAAmBC,IACjBtB,EACE9B,yBAAiBuC,GACjBa,EAAMC,OAASxB,OASzB,OAHIlxB,KAAK8xB,KAAKD,WAAaJ,EAAW9oB,KACpCD,EAAsB+oB,EAAW9oB,IAAKqpB,EAAIryB,SAErCqyB,GAE0BW,IAIjC,GAAIX,IAAQA,EAAI1yB,KAAK4yB,KACnB,MAAM,IAAI9xB,EACR,wHAKN,GAAIgD,IAAQ4uB,EAEV,MADAb,EAAoB9B,yBAAiBuD,QAC/BxvB,EAIR,OADA+tB,EAAoB9B,yBAAiBmC,SAC9BQ,EAGF/uB,eAAe4vB,GAA0BhpB,GAC9C,OAAO8mB,GAAYmC,KAAK9yB,KAAM6J,GAezB5G,eAAe8vB,GAEpBlpB,GAGE,MAAM,IAAIzJ,EACR,6DC1NC6C,eAAe+vB,GAEpBnpB,SAEAA,EAAMlK,iBAAUkK,EAAMlK,WAAW,GACjCmI,EAAmB+B,EAAO,CAAC,WAAY,cAAe,oBAEtD,MAAMooB,EAAkB5yB,IAItB,MACMM,EAAUN,EAASM,QACnBszB,EAAwC,CAE1CC,UAAWvzB,EAAQ,oBACnBuyB,KAAMvyB,EAAO,KACbwzB,OANWtpB,EAAMupB,QAAUpzB,KAAK8xB,KAAKsB,QAAU,GAO/CC,SAAU1zB,EAAO,SACjB2zB,cAAe3zB,EAAQ,wBACvB4zB,IAAK1pB,EAAMpJ,OAEVpB,EAASC,MAKd,OAHIuK,EAAMnD,WACRusB,EAAOO,kBAAoBC,KAAKjrB,UAAUnJ,EAASC,OAE9C2zB,GAET,GAAIppB,EAAM6pB,YAAa,CAAA,MACrB,aAAI7pB,EAAM8pB,gBAAO5rB,QAAS,EACxB,MAAM,IAAI3H,+DAIZ,OAAOJ,KAAKgK,aACVH,EACA,OACA,CACEc,SAAUd,EAAMc,UAElB,IACKd,EAAMlK,QACTi0B,qBAAsB,YAExBjG,EACA,CACEsE,eAAAA,IAKN,OAAOjyB,KAAKgK,aACVH,EACA,OACA,CACEc,SAAUd,EAAMc,UAElB,IACKd,EAAMlK,SAEX,CACEk0B,MAAOhqB,EAAM8pB,MAAMxyB,IAAKC,KACtB8wB,KAAM9wB,EAAG0yB,KACTC,WAAY3yB,EAAGwvB,eAGnB,CACEqB,eAAAA,QCLM+B,IAAAA,GAAAA,0BAAAA,+FAEVA,mEACAA,+CACAA,6CACAA,+CACAA,yEACAA,uEAuEF,MAEMC,GAA0B,CAAC,IAAK,IAAK,KAEpChxB,eAAeixB,GAEpBrqB,aAEA,MAAMsqB,YAAEA,EAAFtD,iBAAeA,GAAmB,GAAUhnB,EAC5ClK,EAAU6C,EAAoBqH,EAAMlK,SAC1CkK,EAAMlK,QAAUA,EAChBmI,EAAmB+B,EAAO,CACxB,eACA,eACA,qBACA,kBACA,kBACA,cACA,UAEA,MACA,mBACA,YACA,eACA,gBAEA,gBACA,UACA,aACA,uBACA,2BACA,OACA,0BACA,iBAGF,MAAMuqB,EAAW,IAAMD,KAAiBA,EAAYE,OAG9CC,OAAgC,UAO7B,KAP6B,GAUhCC,OAAiB,WACrB,MAAMC,KAAEA,GAAS3qB,EACjB,GAAIyqB,EACF,OAAOA,EAAU/G,KAEnB,GAAIhqB,EAASixB,GACX,OAAOA,EAAKzsB,OAEd,GAAI1E,EAAOmxB,GACT,OAAOA,EAAKjH,KAEd,MAAM,IAAIntB,EA3De,gDAgDJ,GAcjBq0B,OAA2B,UA8DC,iBAArB5qB,EAAM6qB,WACR,CACLC,OAAQ9qB,EAAM6qB,YAIX,GApEwB,QAwE3B,iBACJ,GAAIJ,YAAaG,EAAmBE,SAAnBC,EAA2BC,UAAW,CAAA,MACrD,MAAMC,cAAEA,EAAFC,UAAiBA,YAAcN,EAAmBE,eAAnBK,EAA2BH,UAC5DP,EAAUW,UAAYH,GAAiBR,EAAU/G,OAASwH,IAC5DlsB,QAAQC,yCAC8B,IAAIosB,KACtCJ,4FAGGL,EAAmBE,UAT1B,GAcN,MAAMrqB,EAAWJ,EACfqqB,EACA1qB,EAAMS,oBAAYmqB,EAAmBE,eAAnBQ,EAA2BC,YjBhIhB,UiBiI7B,GAKAX,EAAmBE,QACnBF,EAAmBE,OAAOS,YAAc9qB,IAExCzB,QAAQC,KACN,2JAGK2rB,EAAmBE,QAG5B,IAAIvB,EAASvpB,EAAMupB,QAAUpzB,KAAK8xB,KAAKsB,QAAU,GACjD,MAAM3yB,EAAMoJ,EAAMpJ,IAClB,IAAIkK,EAAW,GACX0qB,EAAgB,GACpB,MAAMC,EAwYR,SAAqBnrB,EAAmBG,GACtC,MAAM+qB,EAAgB,GACtB,IAAK,IAAIE,EAAI,KAAOA,EAAG,CACrB,MAAMC,EAASD,EAAIjrB,EACbmrB,EAAejrB,KAAKkrB,IAAIprB,EAAUH,EAAYqrB,GAQpD,GANAH,EAAMhzB,KAAK,CACTmzB,OAAAA,EACAlrB,SAAUmrB,EACV7E,WAAY2E,EAAI,KAGbA,EAAI,GAAKjrB,GAAYH,EACxB,MAIJ,OAAOkrB,EAzZkBM,CAAYpB,EAAUjqB,GACzCsrB,aAAqBnB,EAAmBE,iBAAQkB,aAAc,IACjEC,OAAQ10B,GAAOA,EAAG20B,cAClBz0B,OAAO,CAAC+E,EAAMjF,IAAOiF,EAAOjF,EAAGg0B,UAAW,GAC7C,IAAIY,EAA2BJ,EAG/B,MAAMK,YAAgBxB,EAAmBE,iBAAQkB,aAAc,GACzDK,EAAqD,IAAIC,IAC/DF,EAAc7zB,QAAShB,GAAO80B,EAAgBp1B,IAAIM,EAAGg1B,YAAah1B,IAElE,MAAMi1B,EAAuB,KAC3B,MAAMC,EAAsC,CAC1ClD,OAAAA,EACA3yB,IAAAA,EACA20B,UAAW9qB,EACXisB,UAAW5rB,EACXkrB,WAAYI,GAQd,OANI3B,IACFgC,EAAkBzB,UAAY,CAC5BC,cAAeR,EAAUW,QACzBF,UAAWT,EAAU/G,OAGlB+I,GAEHE,EACJC,IAEA,IAAK5sB,EAAM6sB,kBACT,OAGF,MAAMjE,EAAqB,CACzBW,OAAAA,EACAzoB,SAAAA,EACAlK,IAAAA,KACGg2B,GAEDhC,EAAmBkC,WACrBlE,EAAMmE,eAAiBnC,EAAmBkC,UAG5C9sB,EAAM6sB,kBAAkBjE,IAE1B,IAAKoE,GAAL,SAAKA,GACHA,qBACAA,6CACAA,uEAHF,CAAKA,IAAAA,OAKL,MAAMC,EAAwB1F,IACvBvnB,EAAMonB,WAYT+E,IAA6BzB,GAC7BnD,IAASyF,EAAyBE,mBAIlCltB,EAAMonB,SAZFG,IAASyF,EAAyBG,OAAsB,IAAbzC,EACtC,EAEDA,EAAeyB,EAA2BzB,EAA/B,EASK8B,OAG5B,IAAInF,EAAgB0E,EACpB,MAAM5E,yBAAEA,GAA6BnnB,EAC/BsnB,EAAsB,CAC1BC,EACAC,EAAsB,KAEjBL,IAGLE,GAAiBG,QAEjBL,GAAAA,EAA2B,CACzBI,KAAAA,EACAC,YAAAA,EACAH,cAAAA,EACAI,WAAYiD,MAGV0C,EAAsB/tB,EAAoBjG,aAgC1Ci0B,EAAwBj0B,MAC5B0vB,EACAwE,KASA,IAAIC,EAAkBlB,EAAgBv1B,IAAIgyB,EAAK/B,YAC1CwG,IACHA,EAAkB,CAChBhB,YAAazD,EAAK/B,WAClB4E,OAAQ7C,EAAK6C,OACbJ,UAAWzC,EAAKroB,SAChByrB,cAAc,EACd5D,KAAM,GACNkF,eAAgB,IAElBpB,EAAc5zB,KAAK+0B,GACnBlB,EAAgBp1B,IAAIs2B,EAAgBhB,YAAagB,IAG9CD,EAAc/zB,MACjBg0B,EAAgBrB,cAAe,EAC/BqB,EAAgBjF,KAAOgF,EAAcnF,IAAIE,KACzCkF,EAAgBC,eAAiBF,EAAcnF,IAAIK,qBAG/C4E,IACN,MAAMK,EAAiC,CACrC1G,WAAYwG,EAAgBhB,YAC5B9rB,SAAU8sB,EAAgBhC,UAC1BI,OAAQ4B,EAAgB5B,QAG1B,GAAI2B,EAAc/zB,IAAK,CACrB,MAAMA,EAAM+zB,EAAc/zB,IAC1B,IAAIguB,EAAwB4C,wBAAgBuD,iBAa5C,OAXIn0B,aAAelE,GACb+0B,GAAwBuD,SAASp0B,EAAI1D,cACvC0xB,EAAO4C,wBAAgByD,wBAI3BjB,EAAmB,CACjBpF,KAAAA,EACAhuB,IAAAA,EACAk0B,eAAAA,IAKJA,EAAenF,KAAOgF,EAAcnF,IAAIE,KACxC8D,GAA4BsB,EAAehtB,SAE3CksB,EAAmB,CACjBpF,KAAM4C,wBAAgB0D,kBACtBJ,eAAAA,IAEFR,EAAqBD,EAAyBE,oBAGhD,GAAItC,EAAmBE,OAAQ,CAC7BvB,EAASqB,EAAmBE,OAAOvB,OACnCzoB,EAAW8pB,EAAmBE,OAAO4B,UAGrC,MAAMoB,EAA+B,IAAIC,KACtCnD,EAAmBE,OAAOkB,YAAc,IACtCC,OAAQ10B,GAAOA,EAAG20B,cAClB50B,IAAKC,GAAOA,EAAGg1B,cAEpBf,EAAQC,EAASQ,OAAQ10B,IAAQu2B,EAAgBE,IAAIz2B,EAAGwvB,iBACnD,CAEL,IACE,MAAQtxB,KAAMw4B,SAAuBluB,EAAsBkpB,KACzD9yB,KACA6J,GAEF,GAAIuqB,IACF,MAAM,IAAI9zB,EAAY,qBAKsB,MAF9C8yB,EAAS0E,EAAa3E,OACtBxoB,EAAWmtB,EAAaC,SACpBtD,EAAmBuD,wBACrBvD,EAAmBkC,kBAAWlC,EAAmBkC,iBAAnBsB,EAA6Bv2B,0CA0NnE,SAAsC0xB,EAAgB3yB,GAGpD,SAFsBA,KAAOwvB,MAAWmD,KAAU3yB,IAAO,gBACxBiB,QAAQ,SAAU,IA1N3Cw2B,CAA6B9E,EAAQ3yB,KAIzC+1B,EAAmB,CACjBpF,KAAM4C,wBAAgBmE,+BAExB,MAAOC,GACP,MAAMh1B,EAAMg1B,EAKZ,MAJA5B,EAAmB,CACjBpF,KAAM4C,wBAAgBqE,4BACtBj1B,IAAAA,IAEIA,EAGRiyB,EAAQC,EAGVwB,EAAqBD,EAAyBG,OA+I9C7F,EAAoB9B,yBAAiB0C,SACrC,MAAO3uB,EAAK4uB,SAAa9uB,EA/ILD,WAClB,IAAIq1B,EAAyB,KACzBC,EAAQ,EAgGZ,SA7FMlvB,QAAQmvB,IACZ53B,MAAM63B,KAAK,CAAE1wB,OAAQ8B,EAAM6uB,SAAW,IAAKv3B,IAAI8B,UAC7C,OAAa,CACX,MAAM01B,EAAeJ,IACrB,GAAII,GAAgBtD,EAAMttB,OACxB,OAGF,MAAM6wB,EAAUvD,EAAMsD,GACtB,IAAIE,EAAwB,EAC5B,MAAMnL,OAkLPC,EAjLC,IACE,SAASmL,EAAQtE,EAA+B7B,GAC9C,MAAQ6C,OAAQwB,EAAV1sB,SAAiBA,GAAaqoB,EAC9BoG,EAAM/B,EAAQ1sB,EAEpB,GAAIojB,EACF,OAAOA,EAAgBsL,OAGzB,GAAI31B,EAAOmxB,GACT,OAAOA,EAAKzyB,MAAMi1B,EAAO+B,GAE3B,GAAIx1B,EAASixB,GACX,OAAOA,EAAKzyB,MAAMi1B,EAAO+B,GAE3B,MAAM,IAAI34B,EApbK,+CAubjB,MAAQd,KAAM63B,SAAwBxG,GAAYmC,KAAK9yB,KAAM,CAC3DozB,OAAAA,EACA3yB,IAAAA,EACAkK,SAAAA,EACA2iB,KAAMwL,EAAQjvB,EAAM2qB,KAAMoE,GAC1B/H,iBAAAA,EACAnD,sBAAiBA,SAAAA,EAAiBsL,KAClCnL,YAAa,KACXqD,GAAiB2H,EACjBA,EAAwB,GAE1BjI,WAAYgI,EAAQhI,WACpBjxB,QAAS,CACPs5B,oBAAuBL,EAAQtuB,SAC/B4uB,kDACEv5B,EAAQ,mDACVw5B,4CACEx5B,EAAQ,6CACVy5B,gDACEz5B,EAAQ,kDAEZqxB,yBAAyB7wB,GACnBA,EAAOixB,OAAS/B,yBAAiBuC,KAGjCwC,MAGJyE,GAAyB14B,EAAOkxB,YAChCF,EAAoBhxB,EAAOixB,KAAMjxB,EAAOkxB,gBAE1C5qB,aAAcoD,EAAMpD,aACpBqmB,YAAajjB,EAAMijB,cAGrB,GAAIsH,IACF,MAAM,IAAI9zB,EAAY,2BAGlB42B,EAAsB0B,EAAS,CAAE5G,IAAKmF,IAC5C,MAAOiB,GACP5uB,QAAWkkB,SAAAA,EAAiB2L,gBAAiBjB,GAE7C,MAAMh1B,EAAMg1B,EAIZ,GAHAlH,GAAiB2H,EACjBA,EAAwB,EAEpBj1B,EAAcR,GAChB,MAAMA,EAGR,GAAIgxB,IACF,MAAM,IAAI9zB,EAAY,qBAGnBg4B,IACHA,EAAWl1B,SAEP8zB,EAAsB0B,EAAS,CAAEx1B,IAAAA,SAM3Ck1B,EACF,MAAMA,EAGR,MAAM3E,GAAS0C,IAAuBR,YAAc,IAAI10B,IAAKC,KAC3D0yB,KAAM1yB,EAAG+wB,KACTvB,WAAYxvB,EAAGg1B,gBAGVhzB,EAAK4uB,SAAa9uB,EACvB8vB,GAAwBF,KAAK9yB,KAAM,CACjCozB,OAAAA,EACA3yB,IAAAA,EACAkK,SAAAA,EACAgpB,MAAAA,KAIJ,GAAIvwB,IAAQ4uB,EAIV,MAHAwE,EAAmB,CACjBpF,KAAM4C,wBAAgBsF,gCAElBl2B,EAWR,GARAozB,EAAmB,CACjBpF,KAAM4C,wBAAgBuF,iCAExBzC,EACED,EAAyB2C,qCArQJv2B,aAuQjBw2B,GAGJz5B,KAAK8xB,KAAKD,WACVG,EAAI1yB,KAAKg0B,eAqEf,SAA2BoG,aACzB,MAAMnM,YAAOmM,EAAG7E,oBAAWE,YAAa,EACxC,IAAI/C,EAAM,IACV,MAAM2H,oBACJD,EAAG7D,mBAAH+D,EAAez3B,YAAfy3B,EAAez3B,KAAO,CAAC03B,EAAGptB,IAAMotB,EAAEzD,YAAc3pB,EAAE2pB,gBAAgB,GACpE,IAAK,MAAM0D,KAAQH,EACjB3H,EAAMnF,EACJmF,EACA8H,EAAKzC,eACL7sB,KAAKkrB,IAAIoE,EAAK1E,UAAW7H,EAAOuM,EAAKtE,SAGzC,OAAOxD,EAhFH+H,CAAkB1D,OAA4BrE,EAAI1yB,KAAKg0B,cAEvD,MAAM,IAAIlzB,EAAe,gCAG3B,OAAO4xB,GAI0BgI,IACnC,GAAI52B,IAAQ4uB,EAEV,MADAb,EAAoB9B,yBAAiBuD,QAC/BxvB,EAGR,OADA+tB,EAAoB9B,yBAAiBmC,SAC9BQ,MCruBGiI,GAeAC,GAUAC,GAKAC,GAKAC,GAYAC,GAKAC,GAKAC,GAQAC,GAKAC,GAOAC,GAKAC,GAKAC,GAKAC,GAMAC,GAkBAC,GAUAC,GASAC,GClDAC,GC/BLl4B,eAAem4B,GAEpBvxB,GAEA,MAAMwxB,EAAmC,iBAAVxxB,EAAqB,CAAEpJ,IAAKoJ,GAAUA,EAC/DlK,EAAU6C,EAAoB64B,EAAgB17B,SACpD07B,EAAgB17B,QAAUA,EAE1B,MAAMsC,EAA6B,GAenC,OAdIo5B,EAAgBj0B,YAClBnF,EAAMmF,UAAYi0B,EAAgBj0B,WAGpCU,EAAmBuzB,EAAiB,CAClC,UACA,kBACA,cACA,oBACA,gBACA,UACA,eAGKr7B,KAAKgK,aACVH,EACA,OACA5H,SACAo5B,SAAAA,EAAiB17B,UAAW,QAC5BguB,EACA,CACEsE,eAAiBD,IACf,MAAMiB,EAAS,IACVjB,EAAIryB,QACP27B,kBAAmBtJ,EAAIryB,QAAQwtB,EAAUoO,0BAErCplB,EAAO+X,GAA0B8D,EAAIryB,SAK3C,OAHIwW,IACF8c,EAAOuI,YAAcrlB,GAEhB8c,KCzCRhwB,eAAew4B,GAEpB5xB,GAEA,MAAMc,SAAEA,EAAFimB,WAAYA,GAAe/mB,EAC3BlK,EAAU6C,EAAoBqH,EAAMlK,SAY1C,GAXAkK,EAAMlK,QAAUA,EAChBmI,EAAmB+B,EAAO,CACxB,kBACA,0BACA,oBACA,uBACA,gBACA,UACA,aACA,iBAEEA,EAAMmkB,WAAankB,EAAMokB,OAAQ,CAAA,MACnC,IAAIyN,EAAa3N,GAAyBlkB,EAAMmkB,UAAWnkB,EAAMokB,QAC7DpkB,EAAM8xB,eACRD,iBAA4B7xB,EAAM8xB,cAEpCh8B,EAAQ,8BAAuBA,EAAQ,wBAAwB+7B,EAGjE,GAC2B,MAAzB7xB,EAAMlE,kBACyB,MAA9BkE,EAAM+xB,sBAA4D,MAA5B/xB,EAAMgyB,oBAC7C,CAAA,MACA,MAIMC,WAH0B,MAA9BjyB,EAAM+xB,wBAAkC/xB,EAAM+xB,qBAAyB,MAE3C,MAA5B/xB,EAAMgyB,sBAAgChyB,EAAMgyB,mBAAuB,KAErEl8B,EAAQ,oCACNA,EAAQ,8BAA8Bm8B,EAG1C,MAAO14B,EAAK4uB,SAAa9uB,EACvBlD,KAAKgK,aACHH,EACA,MACA,CAAE+mB,WAAAA,EAAYjmB,SAAAA,GACdhL,OACAguB,EACA,CACEsE,eAAe5yB,IACN,IACFA,EAASC,KACZy8B,cACE18B,EAASM,QAAQoE,EAAiB,eACpCi4B,WACE38B,EAASM,QAAQoE,EAAiB,iBAO9C,GAAIX,IAAQ4uB,IAAQA,EAAI1yB,KAAK4yB,KAE3B,MAAM9uB,EAGR,OAAO4uB,ECjCF/uB,eAAeg5B,GAEpBpyB,GAEA,MAAMlK,EAAU6C,EAAoBqH,EAAMlK,SAqC1C,GApCAkK,EAAMlK,QAAUA,EAChBmI,EAAmB+B,EAAO,CACxB,eACA,qBACA,kBACA,kBACA,cACA,UAEA,oBACA,4BACA,wBACA,8BACA,0BACA,oBACA,uBAEA,MACA,mBACA,YACA,eACA,gBAEA,gBACA,UACA,aACA,uBAEA,oBACA,OACA,0BACA,eACA,eACA,kBACA,YAEEA,EAAMmkB,WAAankB,EAAMokB,OAAQ,CAAA,MACnC,IAAIyN,EAAa3N,GAAyBlkB,EAAMmkB,UAAWnkB,EAAMokB,QAC7DpkB,EAAM8xB,eACRD,iBAA4B7xB,EAAM8xB,cAEpCh8B,EAAQ,8BAAuBA,EAAQ,wBAAwB+7B,EAGjE,MAAOt4B,EAAK4uB,SAAa9uB,EACvBlD,KAAKgK,aAA6BH,EAAO,MAAO,GAAIlK,IAGtD,GAAIyD,IAAQ4uB,IAAQA,EAAI1yB,KAAK4yB,KAE3B,MAAM9uB,EAER,OAAO4uB,GJ7IGiI,GAAAA,kBAAAA,0CAEVA,+BACAA,0CACAA,6CACAA,0CACAA,yDAEAA,oDAIAA,yBAGUC,GAAAA,2BAAAA,8DAGVA,uBACAA,sCACAA,0CACAA,wDACAA,kCAGUC,GAAAA,gCAAAA,gEAEVA,uCAGUC,GAAAA,2BAAAA,+DAEVA,mCAGUC,GAAAA,yBAAAA,kDAEVA,2BACAA,gCACAA,kCACAA,wCAIAA,0CAGUC,GAAAA,sBAAAA,8CAEVA,gCAGUC,GAAAA,qBAAAA,kDAEVA,kDAGUC,GAAAA,yBAAAA,gDAEVA,uBACAA,yBACAA,6BACAA,0BAGUC,GAAAA,2CAAAA,iHAEVA,6DAGUC,GAAAA,gCAAAA,uDAEVA,qBACAA,mBACAA,sBAGUC,GAAAA,8BAAAA,mDAEVA,wBAGUC,GAAAA,uBAAAA,0CAEVA,kBAGUC,GAAAA,qBAAAA,0CAEVA,wBAGUC,GAAAA,mBAAAA,8CAEVA,6BACAA,oBAGUC,GAAAA,+BAAAA,oDAEVA,yBACAA,aAKAA,oBAIAA,eAMUC,GAAAA,gCAAAA,iDAEVA,uBACAA,qBACAA,wBAMUC,GAAAA,yCAAAA,gFAEVA,qCACAA,wCAMUC,GAAAA,2CAAAA,2EAEVA,+BCpDUC,GAAAA,iCAAAA,sGAEVA,mEACAA,uDACAA,qDACAA,uDACAA,yEACAA,uEAiEF,MACMlH,GAA0B,CAAC,IAAK,IAAK,KAGpChxB,eAAei5B,GAEpBryB,aAEA,MAAMsqB,YAAEA,GAAgBtqB,EAClBuqB,EAAW,IAAMD,KAAiBA,EAAYE,QAG5C/0B,KAAM68B,SAAsBf,GAAWtI,KAAK9yB,KAAM,CACxDozB,OAAQvpB,EAAMmkB,UACdvtB,IAAKoJ,EAAMokB,OACX7mB,UAAWyC,EAAMuyB,eAEbjK,EAAOgK,EAAW,KAClBE,GAAcF,EAAY,kBAE1B1H,OAA2B,UAoDC,iBAArB5qB,EAAM6qB,WACR,CACLC,OAAQ9qB,EAAM6qB,YAIX,GA1DwB,QA8D3B,iBACJ,YAAID,EAAmBE,SAAnBC,EAA2B0H,wBAAyB,CAAA,MACtD,MAAMxH,cAAEA,EAAFyH,YAAiBA,YACrB9H,EAAmBE,eAAnBK,EAA2BsH,wBAG3BH,EAAY,mBAAqBrH,IAChCqH,EAAY,oBAAsBI,IAEnC1zB,QAAQC,yCAC8B,IAAIosB,KACtCJ,4FAGGL,EAAmBE,UAd1B,GAmBN,MAAMrqB,EAAWJ,EACfmyB,EACAxyB,EAAMS,oBAAYmqB,EAAmBE,eAAnBQ,EAA2BC,YArGhB,UAsG7B,GAKAX,EAAmBE,QACnBF,EAAmBE,OAAOS,YAAc9qB,IAExCzB,QAAQC,KACN,2JAGK2rB,EAAmBE,QAG5B,IAAIvB,EAASvpB,EAAMupB,QAAUpzB,KAAK8xB,KAAKsB,QAAU,GACjD,MAAM3yB,EAAMoJ,EAAMpJ,IAClB,IAAIkK,EAAW,GACX0qB,EAAgB,GACpB,MAAMC,EAmaR,SAAqBnrB,EAAmBG,GACtC,MAAM+qB,EAAgB,GACtB,IAAK,IAAIE,EAAI,KAAOA,EAAG,CACrB,MAAMC,EAASD,EAAIjrB,EACbmrB,EAAejrB,KAAKkrB,IAAIprB,EAAUH,EAAYqrB,GAQpD,GANAH,EAAMhzB,KAAK,CACTmzB,OAAAA,EACAlrB,SAAUmrB,EACV7E,WAAY2E,EAAI,KAGbA,EAAI,GAAKjrB,GAAYH,EACxB,MAIJ,OAAOkrB,EApbkBM,CAAY0G,EAAY/xB,GAQjD,IAAI0rB,aAPuBvB,EAAmBE,iBAAQkB,aAAc,IACjEC,OAAQ10B,GAAOA,EAAG20B,cAClBz0B,OACC,CAAC+E,EAAMjF,IACLiF,EAAOjF,EAAGo7B,sBAAwBp7B,EAAGq7B,wBAA0B,EACjE,GAKJ,MAAMxG,YAAgBxB,EAAmBE,iBAAQkB,aAAc,GACzDK,EACJ,IAAIC,IACNF,EAAc7zB,QAAShB,GAAO80B,EAAgBp1B,IAAIM,EAAGg1B,YAAah1B,IAElE,MAAMi1B,EAAuB,KAC8B,CACvDjD,OAAAA,EACA3yB,IAAAA,EACA20B,UAAW9qB,EACXisB,UAAW5rB,EACXkrB,WAAYI,EACZqG,wBAAyB,CACvBxH,cAAeqH,EAAY,iBAC3BhK,KAAMgK,EAAYhK,KAClBkF,eAAgB8E,EAAY,yBAA2B,GACvDI,aAAcJ,EAAY,qBAK1B3F,EACJC,IAKA,IAAK5sB,EAAM6yB,kBACT,OAGF,MAAMjK,EAA4B,CAChCW,OAAAA,EACAzoB,SAAAA,EACAlK,IAAAA,KACGg2B,GAEDhC,EAAmBkC,WACrBlE,EAAMmE,eAAiBnC,EAAmBkC,UAG5C9sB,EAAM6yB,kBAAkBjK,IAE1B,IAAKoE,GAAL,SAAKA,GACHA,qBACAA,6CACAA,uEAHF,CAAKA,IAAAA,OAKL,MAAMC,EAAwB1F,IACvBvnB,EAAMonB,WAYT+E,IAA6BqG,GAC7BjL,IAASyF,EAAyBE,mBAIlCltB,EAAMonB,SAZFG,IAASyF,EAAyBG,OAAwB,IAAfqF,EACtC,EAEDA,EAAiBrG,EAA2BqG,EAA/B,EASGhG,OAItBY,EAAsB/tB,EAAoBjG,aAgC1Ci0B,EAAwBj0B,MAC5B0vB,EACAwE,KASA,IAAIC,EAAkBlB,EAAgBv1B,IAAIgyB,EAAK/B,YAC/C,MAAM+L,EAAahK,EAAK6C,OAClBoH,EAAWpyB,KAAKkrB,IAAI/C,EAAK6C,OAASlrB,EAAW,EAAG+xB,EAAa,GAC9DjF,IACHA,EAAkB,CAChBhB,YAAazD,EAAK/B,WAClB6L,wBAAyBE,EACzBH,sBAAuBI,EACvB7G,cAAc,EACd5D,KAAM,IAER8D,EAAc5zB,KAAK+0B,GACnBlB,EAAgBp1B,IAAIs2B,EAAgBhB,YAAagB,IAG9CD,EAAc/zB,MACjBg0B,EAAgBrB,cAAe,EAC/BqB,EAAgBjF,KAAOgF,EAAcnF,IAAIE,YAGrC+E,IACN,MAAM4F,EAA6B,CACjCjM,WAAYwG,EAAgBhB,YAC5ByF,mBAAoBzE,EAAgBoF,sBACpCZ,qBAAsBxE,EAAgBqF,yBAGxC,GAAItF,EAAc/zB,IAAK,CACrB,MAAMA,EAAM+zB,EAAc/zB,IAC1B,IAAIguB,EACF+J,+BAAuB2B,qBAazB,OAXI15B,aAAelE,GACb+0B,GAAwBuD,SAASp0B,EAAI1D,cACvC0xB,EAAO+J,+BAAuB4B,4BAIlCvG,EAAmB,CACjBpF,KAAAA,EACAhuB,IAAAA,EACAy5B,aAAAA,IAKJA,EAAa1K,KAAOgF,EAAcnF,IAAIE,KACtC8D,GACE6G,EAAahB,mBAAqBgB,EAAajB,qBAAuB,EAExEpF,EAAmB,CACjBpF,KAAM+J,+BAAuB6B,sBAC7BH,aAAAA,IAEF/F,EAAqBD,EAAyBE,oBAGhD,GAAItC,EAAmBE,OAAQ,CAC7BvB,EAASqB,EAAmBE,OAAOvB,OACnCzoB,EAAW8pB,EAAmBE,OAAO4B,UAGrC,MAAMoB,EAA+B,IAAIC,KACtCnD,EAAmBE,OAAOkB,YAAc,IACtCC,OAAQ10B,GAAOA,EAAG20B,cAClB50B,IAAKC,GAAOA,EAAGg1B,cAEpBf,EAAQC,EAASQ,OAAQ10B,IAAQu2B,EAAgBE,IAAIz2B,EAAGwvB,iBACnD,CAEL,IACE,MAAQtxB,KAAMw4B,SAAuBluB,EAAsBkpB,KACzD9yB,KACAi9B,EAAUpzB,IAEZ,GAAIuqB,IACF,MAAM,IAAI9zB,EAAY,qBAKsB,MAF9C8yB,EAAS0E,EAAa3E,OACtBxoB,EAAWmtB,EAAaC,SACpBtD,EAAmBuD,wBACrBvD,EAAmBkC,kBAAWlC,EAAmBkC,iBAAnBsB,EAA6Bv2B,0CAiP9C,EAPnBowB,EAxOqC,IACxBjoB,EACHupB,OAAAA,IA8OHpF,UACL8D,EAAK7D,OACL6D,EAAKsK,aACLtK,EAAKsB,OACLtB,EAAKrxB,IACL,QAECq1B,OAAOoH,SACP36B,KAAK,KAEyBb,QAAQ,SAAU,MAnP/C80B,EAAmB,CACjBpF,KAAM+J,+BAAuBhD,+BAE/B,MAAOC,GACP,MAAMh1B,EAAMg1B,EAKZ,MAJA5B,EAAmB,CACjBpF,KAAM+J,+BAAuB9C,4BAC7Bj1B,IAAAA,IAEIA,EAGRiyB,EAAQC,EAoNZ,IACExD,EAjCA,OADAgF,EAAqBD,EAAyBG,OACxB,IAAfqF,EAzDgBp5B,WACrB,IAAIy4B,EAAa3N,GAAyBlkB,EAAMmkB,UAAWnkB,EAAMokB,QAC7DpkB,EAAMuyB,eACRV,iBAA4B7xB,EAAMuyB,cAEpC,MAAMz8B,EAAmB,IACpBkK,EAAMlK,QACTw9B,oBAAuBzB,EACvB0B,6BAAgCjL,IAG3B/uB,EAAK4uB,SAAa9uB,EACvB+4B,GAAWnJ,KAAK9yB,KAAM,CACpBozB,OAAQvpB,EAAMupB,OACd3yB,IAAKoJ,EAAMpJ,IACXd,QAAAA,EACA8G,aAAcoD,EAAMpD,gBAGxB,GAAIrD,IAAQ4uB,EAIV,MAHAwE,EAAmB,CACjBpF,KAAM+J,+BAAuB2B,uBAEzB15B,EAkBR,OAfA0zB,EACED,EAAyB2C,gCAE3BhD,EAAmB,CACjBpF,KAAM+J,+BAAuB6B,sBAC7BH,aAAc,CACZjM,WAAY,EACZgL,qBAAsB,EACtBC,mBAAoB,KAGxBrF,EAAmB,CACjBpF,KAAM+J,+BAAuB5B,iCAGxB,IACFvH,EACH1yB,KAAM,CACJ4yB,KAAMF,EAAIryB,QAAJ,MAAuB,GAC7BwzB,OAAQC,EACRG,IAAK9yB,EACL4yB,gBAAiBrzB,KAAK8xB,KAAKuL,OAAS,IAAM,QAAQjK,KAChDpzB,KAAK8xB,KAAKwL,YACR78B,IACJyyB,UAAWlB,EAAIryB,QAAQ,oBACvB2zB,cAAetB,EAAIryB,QAAQ,2BAMP49B,GAjLNt6B,WAClB,IAAIq1B,EAAyB,KACzBC,EAAQ,EAmEZ,SAhEMlvB,QAAQmvB,IACZ53B,MAAM63B,KAAK,CAAE1wB,OAAQ8B,EAAM6uB,SAAW,IAAKv3B,IAAI8B,UAC7C,OAAa,CACX,MAAM01B,EAAeJ,IACrB,GAAII,GAAgBtD,EAAMttB,OACxB,OAGF,MAAM6wB,EAAUvD,EAAMsD,GACtB,IACE,IAAI+C,EAAa3N,GACflkB,EAAMmkB,UACNnkB,EAAMokB,QAEJpkB,EAAMuyB,eACRV,iBAA4B7xB,EAAMuyB,cAEpC,MAAMN,WAAqBlD,EAAQpD,UACjCoD,EAAQpD,OAASoD,EAAQtuB,SAAW,IAEhC3K,EAAmB,IACpBkK,EAAMlK,QACTw9B,oBAAuBzB,EACvB0B,6BAAgCjL,EAChCqL,0BAA6B1B,GAG1BlD,EAAQtuB,iBACJ3K,EAAQ,2BAEjB,MAAQL,KAAM63B,SAAwBsE,GAAe3I,KAAK9yB,KAAM,CAC9DozB,OAAAA,EACA3yB,IAAAA,EACAkK,SAAAA,EACAimB,WAAYgI,EAAQhI,WACpBjxB,QAAAA,EACA8G,aAAcoD,EAAMpD,eAGtB,GAAI2tB,IACF,MAAM,IAAI9zB,EAAY,oCAGlB42B,EAAsB0B,EAAS,CAAE5G,IAAKmF,IAC5C,MAAOiB,GACP,MAAMh1B,EAAMg1B,EAEZ,GAAIx0B,GAAcR,GAChB,MAAMA,EAGR,GAAIgxB,IACF,MAAM,IAAI9zB,EAAY,8BAGnBg4B,IACHA,EAAWl1B,SAEP8zB,EAAsB0B,EAAS,CAAEx1B,IAAAA,SAM3Ck1B,EACF,MAAMA,EAGR,MAAM3E,GAAS0C,IAAuBR,YAAc,IAAI10B,IAAKC,KAC3D0yB,KAAM1yB,EAAG+wB,KACTvB,WAAYxvB,EAAGg1B,gBAGVhzB,EAAK4uB,SAAa9uB,EACvB8vB,GAAwBF,KAAK9yB,KAAM,CACjCozB,OAAAA,EACA3yB,IAAAA,EACAkK,SAAAA,EACAgpB,MAAAA,KAIJ,GAAIvwB,IAAQ4uB,EAIV,MAHAwE,EAAmB,CACjBpF,KAAM+J,+BAAuB7B,gCAEzBl2B,EAGRozB,EAAmB,CACjBpF,KAAM+J,+BAAuB5B,iCAE/BzC,EACED,EAAyB2C,gCAG3B,MAAMiE,EACJpH,IAAuBiG,wBAAwBjF,eAC3CqG,EAAc1L,EAAI1yB,KAAKg0B,cAC7B,GACEtzB,KAAK8xB,KAAKD,WACV4L,GACAC,GACAD,IAAgBC,EAEhB,MAAM,IAAIt9B,8CACoCq9B,mBAA6BC,wBAM7E,YA/PuBz6B,aA6PjBw2B,GAECzH,GA4DoCgI,YAG/Bp2B,GAAcR,GAC5B,OAAOA,aAAe9C,EIhoBjB2C,eAAe06B,GAAyB9zB,GAC7C,MAAMwxB,EAAmC,iBAAVxxB,EAAqB,CAAEpJ,IAAKoJ,GAAUA,EAC/D5H,EAA6B,GAC/Bo5B,EAAgBj0B,YAClBnF,EAAMmF,UAAYi0B,EAAgBj0B,WAEpC,MAAMzH,EAAmB6C,QAAoB64B,SAAAA,EAAiB17B,SACxDN,SAA6Bg8B,SAAAA,EAAiBh8B,WAAY,GAShE,OARAS,OAAOuB,KAAKhC,GAAU+C,QAAS3B,IAC7B,MAAMiD,EAAIrE,EAASoB,GACV,MAALiD,IACFzB,cAAkBxB,GAASiD,KAKxB1D,KAAKgK,aAAqBH,EAAO,MAAO5H,EAAOtC,OAASguB,EAAW,CACxE2E,UAAW,CAAEsL,aAAc,iBAqH/B,MAAMC,GAA+B,CAAC,QAoCtC56B,eAAe66B,GAEbj0B,GAEA,MAAMwxB,EAAmC,iBAAVxxB,EAAqB,CAAEpJ,IAAKoJ,GAAUA,EAC/DlK,EAAU6C,EAAoB64B,EAAgB17B,SACpD07B,EAAgB17B,QAAUA,EAC1B,MAAMo+B,EAAW1C,EAAgB0C,UAAY,SAC7C1C,EAAgB0C,SAAWA,EA1C7B,SAA8BA,GAC5B,IAAIC,EAAkC,OAClCC,EAA+B,GAQnC,GAHED,EAAc,UACdC,EAAmBJ,IAEhBI,EAAiBzG,SAASuG,GAC7B,MAAM,IAAI39B,+CACqC69B,EAAiB17B,KAC5D,mCA+BN27B,CAAqBH,GAErB,MAAM97B,EAAiC,GACjC5C,SAA6Bg8B,SAAAA,EAAiBh8B,WAAY,GAkChE,GAjCAS,OAAOuB,KAAKhC,GAAU+C,QAAS3B,IAC7B,MAAMiD,EAAIrE,EAASoB,GACV,MAALiD,IACFzB,cAAkBxB,GAASiD,KvByHjC,SACEA,EACAzB,EACAZ,GAMA,SAAS88B,EAAUl2B,EAAWvE,GACZ,MAAZzB,EAAMgG,KACRhG,EAAMgG,GAAKvE,GANVrC,EAAK0G,QAUV1G,EAAKe,QAAS6F,IACZ,MAAMC,EAAQf,EAAgBc,GAC9B,IAAKC,EAEH,MAAM,IAAI9H,OAAoB6H,4CAGhC,MAAME,EAAWzE,EAAEuE,GACnB,GAAgB,MAAZE,EACF,OAIF,GAAqB,iBAAVD,EACT,OAAOi2B,EAAUj2B,KAFIC,GAKvB,GAAIvH,MAAMC,QAAQqH,GAGhB,OAAOi2B,EAFQj2B,EAAM,GACJA,EAAM,GAAGC,IAI5B,MAAM3H,EAAM0H,EAAMC,GAClBrI,OAAOsI,QAAQ5H,GAAK4B,QAAQ,EAAE6F,EAAGvE,MAC/By6B,EAAUl2B,EAAGvE,OuB9JjB06B,CAAiB/C,EAAiBp5B,EAAO,CACvC,YACA,UACA,aACA,aACA,uBACA,6BACA,0BACA,0BACA,sBACA,oBAGF6F,EAAmBuzB,EAAiB,CAClC,UACA,kBACA,cACA,oBAEA,gBACA,UACA,aAEA,QACA,iBAIyB,MAAzBA,EAAgB12B,QACe,MAA9B02B,EAAgBsB,YAAkD,MAA5BtB,EAAgBuB,UACvD,CAAA,MACA,MAIMj4B,WAH0B,MAA9B02B,EAAgBsB,cAAwBtB,EAAgBsB,WAAe,MAE3C,MAA5BtB,EAAgBuB,YAAsBvB,EAAgBuB,SAAa,KAErEj9B,EAAO,eAAYA,EAAO,SAAagF,EAUzC,IAAIusB,EAAgB,EAEhB/mB,GAAa,EACjB,MAAM6mB,yBAAEA,EAAFC,SAA4BA,GAAaoK,EACzClK,EAAsB,CAC1BC,EACAC,EAAsB,KAGtB,GAAIA,EAAc,EAChB,OAEF,IAAKL,IAA6BC,EAChC,OAEFC,GAAiBG,QACjBL,GAAAA,EAA2B,CACzBI,KAAAA,EACAC,YAAAA,EACAH,cAAAA,EACAI,WAAYnnB,IAEd,MAAMonB,EAEApnB,EAAY,EACP,EAGS,IAAdA,EACEinB,IAAS/B,yBAAiBmC,QACrB,EAEF,EAEFN,EAAgB/mB,EAEH,IAAlBonB,EACEH,IAAS/B,yBAAiBmC,gBAC5BP,GAAAA,EAAWM,UAKbN,GAAAA,EAAWM,IAIfJ,EAAoB9B,yBAAiB0C,SACrC,MAAO3uB,EAAK4uB,SAAa9uB,EACvBlD,KAAKgK,aAAkBH,EAAO,MAAO5H,EAAOtC,OAASguB,EAAW,CAC9D2E,UAAW,CACTsL,aAtDG,cAuDHS,mBAAqB5L,IACnBtoB,EAAYsoB,EAAM6L,MAClBnN,EACE9B,yBAAiBuC,GACjBa,EAAMC,OAASxB,QAMzB,GAAI9tB,IAAQ4uB,EAEV,MADAb,EAAoB9B,yBAAiBuD,QAC/BxvB,EAGR,IAAIm7B,EAAavM,EAAIryB,QACjB6+B,EAAiDxM,EAAI1yB,KACzD6K,IAAco0B,EAAW,mBAAqB,GA6B3B,SAAbR,IACFS,EAAU,IAAIl7B,KAAK,CAAC0uB,EAAI1yB,MAAO,CAC7B8xB,KAAMmN,EAAW,mBAGrBpN,EAAoB9B,yBAAiBmC,SAGvC,MAAMiN,EAA4C,IAC7CzM,EACH1yB,KAAM,CACJo/B,QAASF,EACTrM,KAAMoM,EAAU,MAAY,GAC5BI,aAAcJ,EAAW,kBAAoB,GAC7ClM,cAAekM,EAAW,yBAA2B,GACrDjD,kBAAmBiD,EAAWpR,EAAUoO,2BAMtCplB,EAAO+X,GAA0BqQ,GAIvC,OAHIpoB,IACFsoB,EAAUn/B,KAAKk8B,YAAcrlB,GAExBsoB,EAQFx7B,eAAe27B,GAEpB/0B,GAGE,MAAM,IAAIzJ,EACR,0DC5RN,IAAYy+B,GA2BL57B,eAAe67B,GAEpBj1B,GAGE,MAAM,IAAIzJ,EACR,mEC9FU2+B,GAAcC,GAC5B,OAAIA,GAAiB,KAATA,GAAwB,MAATA,GD4DjBH,GAAAA,4BAAAA,mFAEVA,qDACAA,mDACAA,iDACAA,mDACAA,uDACAA,qDC1DF,MAAMI,GAAe,UAYrB,MAAaC,GAGX9/B,YAAY+/B,EAAiBC,QAFrBC,oBACAD,wBASDE,UAAY,CACjBH,EACAI,EACAH,KAEKA,IACHA,EAAcp/B,KAAKo/B,aAErB,MAAMzL,EAAkB,GAElB6L,EAAax/B,KAAKy/B,iBADPN,EAAIO,UAerB,OAbA/L,EAAMtxB,KACJrC,KAAKq/B,QAAQM,UACX,eACAP,EAAYQ,eACZ,IACAJ,GAMJ7L,EAAMtxB,KAAK,iBAAmBrC,KAAK6/B,cAAcV,IACjDxL,EAAMtxB,KAAK,aAAerC,KAAK8/B,cAAcX,EAAKC,EAAa,IACxDzL,EAAMpxB,KAAK,YAGbw9B,gBAAkB,CACvBZ,EACAI,EACAH,KAGAD,EAAIO,SAAW1/B,KAAKggC,cACpB,MAAMC,EAAS,IAAI9J,IAEdgJ,EAAIx/B,UAEPw/B,EAAIx/B,QADiC,IAIvCw/B,EAAIx/B,QAAQugC,QAAUf,EAAIe,KAErBnB,GAAcI,EAAIH,QACrBG,EAAIx/B,QAAQugC,MAAQ,IAAMf,EAAIH,MAG5BG,EAAIgB,YACNhB,EAAIx/B,QAAQugC,QAAUlgC,KAAKq/B,QAAQjM,UAAU+L,EAAIgB,aAGnDF,EAAOn/B,IAAI,OAAQq+B,EAAIx/B,QAAQugC,MAC/BD,EAAOn/B,IAAI,aAAcq+B,EAAIO,UAc7BO,EAAOn/B,IAAI,uBAAwBd,KAAKogC,sBACpCpgC,KAAKq/B,QAAQgB,eACfJ,EAAOn/B,IAAI,uBAAwBd,KAAKq/B,QAAQgB,eAGlDJ,EAAO79B,QAAQ,CAAC1B,EAAOD,KACjBA,EAAI6/B,WAAW,WACjBnB,EAAIx/B,QAAQc,GAAOC,KAGvBy+B,EAAIoB,KAAOvgC,KAAKwgC,cAAcrB,EAAIoB,MAClC,MAAME,EAAOzgC,KAAKs/B,UAAUH,EAAK,EAAGC,GAGpC,OAFAa,EAAOn/B,IAAI,gBAAiB2/B,GAErBR,QAGFS,WAAa,CAClBvB,EACAC,KAEO,CAAE3+B,IAAK,GAAIC,MAAO,UAGpBigC,aAAe,CACpBxB,EACAI,KAEO,CAAE9+B,IAAK,GAAIC,MAAO,UAGpBkgC,kBAAoB,CACzBzB,EACAI,KAEAJ,EAAIO,SAAW1/B,KAAKggC,cACfb,EAAIx/B,UAEPw/B,EAAIx/B,QADiC,IAIvCw/B,EAAIx/B,QAAQugC,QAAUf,EAAIe,KACrBnB,GAAcI,EAAIH,QACrBG,EAAIx/B,QAAQugC,MAAQ,IAAMf,EAAIH,MAGhCG,EAAIoB,KAAOvgC,KAAKwgC,cAAcrB,EAAIoB,MAC9BpB,EAAIgB,YACNhB,EAAIx/B,QAAQugC,QAAUlgC,KAAKq/B,QAAQjM,UAAU+L,EAAIgB,aAInDhB,EAAIx/B,QAlKG,cAkK0Bw/B,EAAIO,SACrC,MAAMF,EAAax/B,KAAKy/B,iBAAiBN,EAAIO,UACvC1N,EAAM,IACNmN,EAAIl9B,OAAS,GACjB4+B,kBAA0B7gC,KAAKq/B,QAAQM,UACvCmB,uBAA4B9gC,KAAKogC,qBACjCW,mBACE/gC,KAAKo/B,YAAYQ,eAAiB,IAAMJ,EAC1CwB,aAAqB7B,EAAIO,SACzBuB,gBAAwB,GAAK1B,EAC7B2B,sBAA8BlhC,KAAK6/B,cAAcV,IAYnD,OAVIn/B,KAAKq/B,QAAQgB,gBACfrO,EA5Ke,wBA4KoBhyB,KAAKq/B,QAAQgB,eAElDlB,EAAIl9B,MAAQD,EAAqBgwB,GAEjCA,EA/KY,mBA+KkBhyB,KAAK8/B,cACjCX,EACAn/B,KAAKo/B,YACLG,GAEKvN,QAGFmP,wBAA0B,CAC/BhC,EACAI,KAEAJ,EAAIO,SAAW1/B,KAAKggC,cAEpB,MAAMR,EAAax/B,KAAKy/B,iBAAiBN,EAAIO,UACvC1N,EAAM,CACV6O,kBAA0B7gC,KAAKq/B,QAAQM,UACvCoB,mBACE/gC,KAAKo/B,YAAYQ,eAAiB,IAAMJ,EAC1CwB,aAAqB7B,EAAIO,SACzBuB,gBAAwB,GAAK1B,EAC7B6B,eAAuB54B,GACrBgoB,GAAMiD,KAAKjrB,UAAU22B,EAAIkC,QAAS,SAClC,WAaJ,OAVIrhC,KAAKq/B,QAAQgB,gBACfrO,EA3Me,wBA2MoBhyB,KAAKq/B,QAAQgB,eAElDlB,EAAIl9B,MAAQD,EAAqBgwB,GAEjCA,EA9MY,mBA8MkBhyB,KAAK8/B,cACjCX,EACAn/B,KAAKo/B,YACLG,GAEKvN,QAGDoO,mBAAqB,IACpB,wBAIDN,cAAgB,CACtBX,EACAC,EACAG,KAGA,IAAKJ,EAAIO,SACP,MAAO,GAGT,MAAM4B,EAAathC,KAAKuhC,cACtBnC,EACAD,EAAIO,SAAS8B,OAAO,EAAG,IAQzB,OAAO/Q,GAAW6Q,EAAYthC,KAAKyhC,aAAatC,EAAIO,SAAUP,GAAM,aAG9Da,YAAc,IACP,IAAI9K,MAAK,IAAIA,MAAOpxB,eAG5B49B,cACAhgC,QAAQ,OAAQ,IAChBA,QAAQ,KAAM,IACdA,QAAQ,KAAM,IAAM,SAGnB+9B,iBAAoBC,GACnB1/B,KAAK2hC,YACVjC,EAAS8B,OAAO,EAAG,GACnBxhC,KAAKq/B,QAAQuC,OACb5hC,KAAKq/B,QAAQwC,kBAITF,YAAc,CAACG,EAAMF,EAAQC,IAC5B,CAACC,EAAKN,OAAO,EAAG,GAAII,EAAQC,EAAa5C,IAAc18B,KAAK,UAG7Dg/B,cAAgB,CAACnC,EAA8B0C,KACrD,MAAMC,EAAQtR,GAAW2O,EAAY4C,eAAgBF,GAC/CG,EAAUxR,GAAWsR,EAAO/hC,KAAKq/B,QAAQuC,QACzCM,EAAWzR,GAAWwR,EAASjiC,KAAKq/B,QAAQwC,aAGlD,OAFmBpR,GAAWyR,EAAUjD,UAKlCwC,aAAe,CAAC/B,EAAkBP,KAExC,IAAKn/B,KAAKq/B,QAAQM,UAChB,MAAO,GAGT,MAAMhM,EAAkB,GACxBA,EAAMtxB,KAAKrC,KAAKq/B,QAAQM,WACxBhM,EAAMtxB,KAAKq9B,GACX/L,EAAMtxB,KAAKrC,KAAKy/B,iBAAiBC,IACjC,MAAMyC,EACJ,WAAYhD,EACRn/B,KAAKoiC,sBAAsBjD,GAC3Bn/B,KAAKmiC,gBAAgBhD,GAG3B,OADAxL,EAAMtxB,KAAKrC,KAAKqiC,eAAeF,IACxBxO,EAAMpxB,KAAK,YAGZ8/B,eAAiBC,GAChB5R,GAAW4R,EAAQ,YAGpBH,gBAAmBhD,IACzB,MAAMxL,EAAe,GAOrB,OANAA,EAAMtxB,KAAK88B,EAAIoD,QACf5O,EAAMtxB,KAAK88B,EAAIoB,MACf5M,EAAMtxB,KAAKrC,KAAKwgC,cAAcrB,EAAIl9B,OAAiB,IACnD0xB,EAAMtxB,KAAKrC,KAAKwiC,iBAAiBrD,GAAO,MACxCxL,EAAMtxB,KAAKrC,KAAK6/B,cAAcV,IAC9BxL,EAAMtxB,KAAKrC,KAAKogC,sBACTzM,EAAMpxB,KAAK,YAGZ6/B,sBAAyBjD,IAC/B,MAAMxL,EAAe,GAGrB,OAFAA,EAAMtxB,KAAKrC,KAAKwgC,cAAcrB,EAAIl9B,OAAiB,IACnD0xB,EAAMtxB,KAAKrC,KAAKogC,sBACTzM,EAAMpxB,KAAK,YAGZigC,iBAAoBrD,IAC1B,MAAMxL,EAAkB,GAClB8O,EAAkBC,GAAqBvD,EAAIx/B,SAEjD,IAAK,IAAIc,KAAOgiC,EAAiB,CAC/B,MAAM/hC,EAAQy+B,EAAIx/B,QAAQc,GAC1BA,EAAMA,EAAIkB,cACVgyB,EAAMtxB,KAAK5B,EAAM,IAAMT,KAAK2iC,sBAAsBjiC,EAAMsvB,aAG1D,OAAO2D,EAAMpxB,KAAK,YAGZogC,sBAAyBC,GACxBA,EAAOlhC,QAAQ,OAAQ,KAAKA,QAAQ,aAAc,SAGnDm+B,cAAiBV,IACvB,MAAM99B,EAAiB,GACjBohC,EAAkBC,GAAqBvD,EAAIx/B,SAEjD,IAAK,IAAIc,KAAOgiC,EACdhiC,EAAMA,EAAIkB,cACVN,EAAKgB,KAAK5B,GAGZ,OAAOY,EAAKc,OAAOI,KAAK,MAnTxBvC,KAAKq/B,QAAUF,EACfn/B,KAAKo/B,YAAcA,EAwTboB,cAAcD,EAAcsC,GAAqB,GACvD,IAAKtC,EACH,MAAO,GAGT,IAAIuC,EAAUvC,EASd,OARIsC,IACFC,EAAUvC,EAAK7+B,QAAQ,OAAQ,MAEjCohC,EAAUA,EAAQphC,QAAQ,MAAO,OACjCohC,EAAUA,EAAQphC,QAAQ,MAAO,OACjCohC,EAAUA,EAAQphC,QAAQ,KAAM,OAChCohC,EAAUA,EAAQphC,QAAQ,MAAO,OACjCohC,EAAUA,EAAQphC,QAAQ,MAAO,OAC1BohC,GAIX,MAAaC,GAKX3jC,YACEihC,EACA2C,EACAC,QAPK5C,0BACA2C,4BACAC,mBAOLjjC,KAAKijC,YAAcA,EACnBjjC,KAAKgjC,gBAAkBA,EACvBhjC,KAAKqgC,cAAgBA,EAGhBT,eACL,OAAO5/B,KAAKijC,YAGPjB,eACL,OAAOhiC,KAAKgjC,iBAIhB,SAASN,GAAqB/iC,GAC5B,MAAM8iC,EAA4B,GAQlC,OAPA3iC,OAAOuB,KAAK1B,GAAW,IAAIyC,QAAS3B,KACtB,SAARA,GAAkBA,EAAI6/B,WAAW,YACf,MAAhB3gC,EAAQc,IACVgiC,EAAgBpgC,KAAK5B,KAIpBgiC,EAAgBtgC,OCjdzB,IAqBI+gC,oBCQoBC,GACtBC,GACAC,uBACEA,EAA0BC,CAAAA,GAAkBA,IAG1C,IAEJ,MAAMC,EDXR,WAGE,QAAQ,GACN,IAAmB,iBAAPC,GAEV,OADAN,WACOM,GAAGD,QAAQE,KAAKD,IACzB,IAAqB,iBAATE,KAEV,OADAR,WACOQ,KAAKH,QAAQE,KAAKC,MAC3B,IAAmB,iBAAPC,GAGV,OAFAT,QAEOS,GAAGC,YAAYH,KAAKE,IAC7B,IAAmB,iBAAPE,GASV,OADAX,aACQW,GAAGN,SAAWM,GAAGD,aAAaH,KAAKI,IAC7C,QACE,OAAOL,GAAGD,QAAQE,KAAKD,KCdXM,GAChB,OAAO,IAAIz6B,QAAQ,CAACC,EAASy6B,KAC3B,IAAIC,EACAC,EAAcb,EAAO9jC,KACrB4kC,EAAiBd,EAAOzjC,QAI5B,MAAMwkC,EAAmD,CACvD5B,OAHmBa,EAAOb,QAAUa,EAAOb,OAAOzgC,eAAkB,MAYpEiB,IAAKqhC,EACHC,EAAcjB,EAAOkB,QAASlB,EAAOrgC,KACrCqgC,EAAO96B,OACP86B,EAAO/6B,kBAETk8B,QAASnB,EAAOmB,QAEhBC,QAAUC,IACR,MAAMplC,EDFd,SACEolC,EACArB,EACAe,GAEA,MACMhkC,EAASskC,EAAW/kC,YAAc+kC,EAAWtkC,OAEnD,IAAIukC,EAAa,GAejB,OAde,MAAXvkC,EACFukC,EAAa,KACO,MAAXvkC,IACTukC,EAAa,eAGiB,CAC9BplC,KAAMmlC,EAAWnlC,KACjBa,OAAAA,EACAukC,WAAAA,EACA/kC,QAdc8kC,EAAWxE,QAAUwE,EAAW9kC,QAe9CyjC,OAAAA,EACAG,QAASY,GCnBYQ,CAAkBF,EAAYrB,EAAQe,GACvDS,EAAOt7B,EAASy6B,EAAQ1kC,IAG1BwlC,KAAOC,KD0Bb,SAA+BA,EAAYf,EAAaX,GACtD,OAAQF,IACN,aACsD,IAAhD4B,EAAMC,OAAOC,QAAQ,sBAEvBjB,EAAOkB,EAAY,kBAAmB7B,EAAQ,eAAgB,MAChB,IAArC0B,EAAMC,OAAOC,QAAQ,WAE9BjB,EACEkB,EACE,cAAgB7B,EAAOmB,QAAU,cACjCnB,EACA,eACA,KAKJW,EAAOkB,EAAY,gBAAiB7B,EAAQ,KAAM,KAEpD,MACF,SACA,aAEM,CAAC,GAAI,IAAI5L,SAASsN,EAAMA,OAC1Bf,EACEkB,EAAY,kBAAmB7B,EAAQ,eAAgB,GAAI0B,IAEpD,CAAC,IAAItN,SAASsN,EAAMA,OAE7Bf,EACEkB,EACE,cAAgB7B,EAAOmB,QAAU,cACjCnB,EACA,eACA,GACA0B,IAKJf,EAAOkB,EAAY,gBAAiB7B,EAAQ,KAAM,GAAI0B,IAExD,MACF,YAEEf,EAAOkB,EAAY,gBAAiB7B,EAAQ,KAAM,MCvEhD8B,CAAeJ,EAAOf,EAAQX,IAEhC+B,WACEnB,OAAcrW,IAKlB,GAAIyV,EAAOgC,KAAM,CACf,MAAOC,EAAUC,GAAY,CAC3BlC,EAAOgC,KAAKC,UAAY,GACxBjC,EAAOgC,KAAKE,UAAY,IAE1BpB,EAAeqB,cACb,SC9ER,SAAiB17B,GACf,MAAMymB,EAAMzpB,OAAOgD,GAEnB,IAAI27B,EACAC,EACAC,EAAM,EACNvkC,EARJ,oEASIwkC,EAAS,GACb,KAKErV,EAAIsV,OAAa,EAANF,KAAcvkC,EAAM,IAAMukC,EAAM,GAE3CC,GAAUxkC,EAAIykC,OAAO,GAAMJ,GAAU,EAAKE,EAAM,EAAK,GACrD,CAEA,GADAD,EAAWnV,EAAIuV,WAAYH,GAAO,EAAI,GAClCD,EAAW,IACb,MAAM,IAAItmC,MACR,4FAIJqmC,EAASA,GAAS,EAAKC,EAEzB,OAAOE,EDoDUG,CAAOT,EAAW,IAAMC,GAIvCS,EAAM3jC,QACJ8hC,GACA,SAA0B8B,EAAWvlC,GACnC,MAAMwlC,EAAUxlC,EAAIkB,oBAEM,IAAhBsiC,GAA2C,iBAAZgC,GAC3B,YAAZA,WAIO/B,EAAezjC,MAI5B0jC,EAAgBlE,OAASiE,EAGrBd,EAAOxF,eACTuG,EAAgBvG,aAAewF,EAAOxF,cAKpCwF,EAAOjP,aAETiP,EAAOjP,YAAY+R,QAAQ38B,MAAK,SAAoB48B,GAC7CnC,IAGLA,EAAYoC,QACZrC,EAAOoC,GAEPnC,OAAcrW,MArGH2C,CAAAA,IACjB,IACE,MACiB,iBAARA,GACPA,EAAIvoB,SACHuoB,EAAMmD,KAAKjD,MAAMF,KACsB,oBAAxCxwB,OAAOG,UAAU+vB,SAAS8C,KAAKxC,GAEjC,MAAOwU,GACP,OAAO,IAgGHuB,CAAUpC,KACZA,EAAcxQ,KAAKjD,MAAMyT,SAEPtW,IAAhBsW,IACFE,EAAgB7kC,KAAO2kC,GAEzBD,EAAcT,EACZF,WDmB0BD,GAC6C,MAa3E,MAbI,gBAA+C5L,SAAS0L,MAC1DE,EAAOzjC,QAAUyjC,EAAOnD,cACjBmD,EAAOnD,OAEZ,OAA0BiD,IACR,QAAlBE,EAAOb,QAC8B,+BAArCa,EAAOzjC,kBAAU,kBAC+B,oBAAhDG,OAAOG,UAAU+vB,SAAS8C,KAAKsQ,EAAO9jC,QAGtC8jC,EAAO9jC,KAAOm0B,KAAKjrB,UAAU46B,EAAO9jC,QAGjC8jC,ECjCoBkD,CAAgBnC,aE6EhCoC,GAUXnnC,YAAYonC,QATZ1U,iBAEA2U,sBAEAC,sBAEQC,sBACAC,uBAmYEC,cACR/U,IAEA,MAAMgV,EACa,iBAAThV,GAAqBA,EAAKsB,QAAWpzB,KAAK8xB,KAAKsB,OACnD2T,EAA4B,iBAATjV,EAAoBA,EAAOA,EAAKrxB,IACzD,IAAKqmC,EACH,MAAM,IAAI1mC,EAAe,6BAE3B,UAAW0mC,KAAgBxkC,mBAAmBykC,WActCh9B,2BAA6B,CACrCF,EACAlK,KAEA,GAA+B,MAA3BA,EAAQ,gBACV,OAGF,IAAIqnC,EpBrmB4B,2BoBsmBhC,MAAMvmC,EpB1mBwBoJ,CAAAA,GACR,iBAAVA,EAAqBA,EAAQA,EAAMpJ,IoBymBnCwmC,CAAkBp9B,GAE1B7J,KAAK8xB,KAAKoV,2BACZF,WpBvmByBvmC,GAC7B,MAAM0mC,EAAe1mC,EAAI2mC,YAAY,KAErC,GAAID,GAAgB,EAClB,OAGF,MAAME,EAAU5mC,EAAIsB,MAAMolC,EAAe,GAAGxlC,cAE5C,OAAOoJ,EAAUs8B,GoB8lBFC,CAAe7mC,IAAQumC,GAGhCA,IACFrnC,EAAQ,gBAAkBqnC,SAIpBv+B,uBAAyBA,EA3ajCzI,KAAK8xB,KAAO9xB,KAAKunC,cAAcf,GAW/BxmC,KAAK0mC,UAAY1mC,KAAKwnC,eACtBxnC,KAAKymC,UjBnLqBgB,CAAAA,IAC5B,MAAMhB,EAAYiB,EAAMC,SAExBlB,EAAUmB,SAASxC,UAAOzX,EAC1B8Y,EAAUmB,SAAShK,aAAe,OAClC6I,EAAUmB,SAASt/B,YAASqlB,EAC5B8Y,EAAUmB,SAASjoC,QAAU,GAC7B8mC,EAAUmB,SAASC,iBAAkB,EACrCpB,EAAUmB,SAASE,kBAAoB,EACvCrB,EAAUmB,SAASG,eAAiB,EACpCtB,EAAUmB,SAASI,aAAe,EAClCvB,EAAUmB,SAASK,eAAiB,SAAU9nC,GAC5C,OAAOA,GAAU,KAAOA,EAAS,KAEnCsmC,EAAUmB,SAASM,YAAa,EAChCzB,EAAUmB,SAASO,aAAe,CAChCC,mBAAmB,EACnBC,mBAAmB,EACnBC,qBAAqB,GAKrB7B,EAAU8B,aAAahF,QAAQiF,IAAKpF,GAC7BA,EAAOzjC,SAIZG,OAAOuB,KAAK+hC,EAAOzjC,SAASyC,QAAS3B,IAC/B+uB,GAA4BgI,SAAS/2B,EAAIkB,uBACpCyhC,EAAOzjC,QAAQc,KAInB2iC,GATEA,GAcb,MAAMqF,EAAiB/kC,UAErB,OADAA,EAAE/D,QAAU+D,EAAE/D,SAAW+D,EAAEu8B,eAAUv8B,YAAAA,EAAGrE,iBAAHqpC,EAAa/oC,UAAW,GACtD+D,GAQT,SAASilC,EAAqBhpC,GAC5BG,OAAOsI,QAAQzI,GAASyC,QAAQ,EAAE3B,EAAKC,MACrC,MAAO0C,EAAKwlC,YZ2DUC,GAC1B,IAEE,MAAO,CAAC,KY9DqCC,UAAUpoC,IZ+DvD,MAAO0C,GACP,MAAO,CAACA,EAAK,OYhEiB2lC,GAC5B,GAAI3lC,GAAuB,MAAhBwlC,GAAwBA,IAAiBloC,EAClD,OAEF,IAAIsoC,EAAO,GACX,MAAMC,MAAcvoC,GAAQwoC,MAAM,OAC5BC,EAAkBP,EAAaM,MAAM,OAC3C,IAAK,IAAI3T,EAAI,EAAG6T,EAAI,EAAG7T,EAAI4T,EAAgBphC,QAAU,CACnD,MAAMshC,EAAKF,EAAgB5T,GAC3B,GAAI8T,IAAOJ,EAASG,GAAI,CACtBJ,EAAK3mC,KAAKgnC,KACR9T,IACA6T,EACF,SAGF,MAAME,EAAYhnC,mBAAmB+mC,GACjCA,EAAGthC,OAAS,GAAKshC,EAAGxD,WAAW,IAAM,IACvCmD,EAAK3mC,KAAKgnC,GAEVL,EAAK3mC,KAAKinC,KAEV/T,EACF6T,GAAKE,EAAUvhC,OAEjBpI,EAAQc,GAAOuoC,EAAKzmC,KAAK,MAqF7B,OAtHAkkC,EAAU8B,aAAalpC,SAASmpC,IAAIC,EAAgB3D,IAClD2D,EAAc3D,GACPz7B,QAAQ06B,OAAOe,KAkCxB2B,EAAU8B,aAAalpC,SAASmpC,IAC7BxW,GACMA,EAAIryB,SAGTgpC,EAAqB3W,EAAIryB,SAClBqyB,GAHEA,EAKX/uB,MAAAA,UACE,IAAKykC,EAAM6B,aAAazE,GACtB,OAAOz7B,QAAQ06B,OAAOe,GAGxB,MAAMnlC,WAAUmlC,EAAMzlC,iBAANmqC,EAAgB7pC,QAChC,OAAKA,GAGLgpC,EAAqBhpC,GACd0J,QAAQ06B,OAAOe,IAHbz7B,QAAQ06B,OAAOe,KAQ5B2B,EAAU8B,aAAalpC,SAASmpC,SAAI7a,EAAW1qB,MAAAA,UAC7C,MAAMmgC,OAAEA,GAAW0B,EACnB,IAAK1B,EACH,OAAO/5B,QAAQ06B,OAAOe,GAGnB1B,EAAM,kBACTA,EAAM,gBAAmB,IAE3B,MAAMqG,EAAgCrG,EAAM,gBACtCsG,WAAaD,EAAYC,cAAc,EAE7C,IAAIlL,EAAU4E,EAAO9jC,KAmBrB,MA3JJ,SAAwBwlC,SAEtB,OACIA,EAAMzlC,UAAY69B,QAAQ4H,EAAMrlC,OACjCqlC,EAAMzlC,qBAAaylC,EAAMzlC,SAASM,UAAfgqC,EAAyB,qBAmJ1CC,CAAe9E,IA/ItB,SAA8BA,GAC5B,IAAKA,EAAMzlC,SACT,OAAO,EAGT,MAAMc,OAAEA,GAAW2kC,EAAMzlC,SACzB,OAAe,MAAXc,GAAkBA,GAAU,IAyIF0pC,CAAqB/E,KAC/C4E,EAAajC,GAIb,OAAOp+B,QAAQ06B,OAAOe,GAGxB,MAAMgF,EAAiB1G,EAAM,mBAC7B,GAAI0G,EAAgB,CAClB,MAAMC,QAAEA,EAAFC,QAAWA,GAAYF,EACJE,EAAQjK,gBAAgBgK,GAChC3nC,QAAQ,CAAC1B,EAAOD,KAC/B2iC,EAAOzjC,QAAQc,GAAOC,IAK1BupC,GAAQ,gBAAiB7G,GACzB,MAAM8G,EAAa,IACd9G,EACH9jC,KAAMk/B,EACNjM,gBAAkB,IACbkX,EACHC,WAAYA,EAAa,IAK7B,aADAD,EAAY5b,aAAZ4b,EAAY5b,cACL4Y,EAAUyD,KAGZzD,GiBkBY0D,CAAcnqC,KAAK8xB,KAAK2V,eAGnCF,cAAcf,WAEH,CACf,cACA,kBACA,WACA,SACA,YAEOpkC,QAAS3B,IAChB,MAAMC,EAAQ8lC,EAAM/lC,GACC,iBAAVC,IAET8lC,EAAM/lC,GAAOC,EAAMguB,UAIvB,MACM0b,EADW,CAAC,cAAe,kBAAmB,UAEjDtU,OAAQr1B,IAAU+lC,EAAc/lC,IAChC8B,KAAK,MAER,GAAI6nC,EACF,MAAM,IAAIhqC,kBAA+BgqC,MAG3C,MAAM9M,EAAWkJ,EAAMlJ,iBAAwBkJ,EAAM5E,oBACrD,IAAKtE,EACH,MAAM,IAAIl9B,0FAKZ,GAAIk9B,EAAS9F,SAAS,MACpB,MAAM,IAAIp3B,0DAKZ,MAAMi9B,EAAyB,MAAhBmJ,EAAMnJ,UAA0BmJ,EAAMnJ,OAC/CgN,EAAW,CACf3mC,EACA4mC,IACS,MAAL5mC,EAAY4mC,EAAe5mC,EAGjC,YADkB8iC,EAAM3U,cAEtB,MAAM,IAAIzxB,EAAe,0CAG3B,MAAO,IACFomC,EACHlJ,SAAAA,EACAD,OAAAA,EACAkN,gBAAiBF,EAAS7D,EAAM+D,iBAAiB,GACjDrD,yBAA0BmD,EAAS7D,EAAMU,0BAA0B,GACnEsD,eAAgBH,EAAS7D,EAAMgE,eAAgB,MAC/CC,kBAAmBJ,EAAS7D,EAAMiE,kBAAmB,KACrDC,eAAgBL,EAAS7D,EAAMkE,eAAgB,MAC/CC,mBAAoBN,EAAS7D,EAAMmE,mBAAoB,KACvDlD,cAAe4C,EAAS7D,EAAMiB,cAAe,GAC7C5V,mBAAW2U,EAAM3U,cACjB+Y,eAAgBC,MAIZrD,eAMJ,oCAmDiB9a,YACnB6V,EACAhC,EACAt+B,EACAtC,EACA2tB,EACAwE,GAEA,MAAMG,SAAiBH,SAAAA,EAAMG,kBAAoBD,GAAQA,EAAI1yB,MAG7D,GAAIguB,UAFYwE,SAAAA,EAAMgZ,SAED,CACnB,MAAMC,EAAY9a,GAAQwD,KAAKjrB,UAAU8kB,GAAO,UAChD3tB,EAAQ,eAAiBorC,EAG3B,MAAOzN,EAAU0N,GAAW,WACtBlZ,GAAAA,EAAMmZ,iBAAmBjrC,KAAK8xB,KAAKoZ,eAC9B,CAAClrC,KAAK8xB,KAAKwL,aAAcxL,EAAKmZ,kBAAkB1K,WAGrDzO,GAAAA,EAAMmZ,kBAAoBjrC,KAAK8xB,KAAKqZ,eAElC,UAAUC,KAAKprC,KAAK8xB,KAAKwL,UACpB,CAACt9B,KAAK8xB,KAAKwL,aAAcxL,EAAKmZ,kBAAkB1K,KAElD,UAAIzO,SAAAA,EAAMmZ,mBAAmBjrC,KAAK8xB,KAAKwL,WAAYiD,GAErD,CAACvgC,KAAK8xB,KAAKwL,SAAUiD,GAZF,GAc5BA,EAAOyK,EAEPrrC,E7B/R+BA,CAAAA,IACjC,MAAM0rC,EAAmB,GAczB,OAbAvrC,OAAOsI,QAAQzI,GAASyC,QAAQ,EAAE3B,EAAKC,MACrC2qC,EAAQ5qC,OAAUC,GAGfwoC,MAAM,OACN/nC,IAAKkoC,GACAA,EAAGthC,OAAS,GAAKshC,EAAGxD,WAAW,IAAM,IAChCvjC,mBAAmB+mC,GAErBA,GAER9mC,KAAK,MAEH8oC,G6BgRKC,CAAmB3rC,GAE7B,MAAMoqC,EAAU,CAEd5J,eAAWxS,EACXyF,OAAQ,GAERmP,OAAAA,EACA5iC,QAAS,IAAKA,GACd4gC,KAAAA,EACAt+B,MAAOD,EAAqBC,GAC5Bi+B,KAAM5C,GAGFiO,EAAS,IAAIxI,GACjB/iC,KAAK8xB,KAAK0Z,SACVxrC,KAAK8xB,KAAK2Z,gBACVzrC,KAAK8xB,KAAKmR,aAGN9e,EAAM,IAAI+a,GACd,CACES,UAAW,mBACXiC,OAAQ5hC,KAAK8xB,KAAK8P,OAClBC,YAAa,MACbzO,OAAQ,GACRiN,cAAergC,KAAK8xB,KAAK0Z,UAE3BD,GAGIG,EAAmBvnB,EAAI4b,gBAAgBgK,GACvC4B,EAAa,IAAKhsC,GAElBisC,EAA8B,CAClCrJ,OAAAA,EACA+B,eAAgBtkC,KAAK8xB,KAAKuL,OAAS,IAAM,QAAQC,IACjDv6B,IAAKw9B,EACLj4B,OAAQrG,EACRtC,QAASgsC,EACTrsC,KAAMguB,GAGRoe,EAAiBtpC,QAAQ,CAAC1B,EAAOD,KAC/BmrC,EAAQjsC,QAAQc,GAAOC,IAGzB,MAAMmrC,EAAkBhpC,EAAe7C,KAAK8xB,KAAKhvB,OACjD,SAAI+oC,GAAAA,EAAiB9oC,MAAQ/C,KAAK8xB,KAAKga,UAErCF,EAAQtH,QAAUuH,EAAgB9oC,UAC9B8oC,GAAAA,EAAiB7oC,kBACnB4oC,EAAQtjC,OAAO,oBAAsBg1B,SAC9BqO,EAAU,WAEd,GAAI3rC,KAAK8xB,KAAKga,UAAW,CAC9B,IAAK9rC,KAAK8xB,KAAKia,UACb,MAAM,IAAI3rC,EACR,wDAKJwrC,EAAQ9oC,MAAQ,CACdo9B,KAAMlgC,KAAK8xB,KAAKga,UAChB9M,KAAMh/B,KAAK8xB,KAAKia,UAChBC,SAAU,QAIdL,EAAW,cAAgB3rC,KAAK0mC,UAC5B1mC,KAAK8xB,KAAK0Y,eAAiB,GAAkCyB,WAA7BjsC,KAAK8xB,KAAK0Y,iBAC5CoB,EAAQrH,QAAUvkC,KAAK8xB,KAAK0Y,gBAQ9B,IACE,MAAM0B,EAAa,IAAKN,UACjBM,EAAWvF,iBACXuF,EAAWtF,WAClBqD,GAAQ,YAAaiC,GACrB,MAAMla,QAAYhyB,KAAKymC,UAAU,CAE7BsB,cAAekE,SACfnE,iBAAkBmE,SAClBE,QAASnsC,KAAK8xB,KAAK8Y,kBAElBgB,YACC9Z,SAAAA,EAAMQ,YAAa,GACvB8Z,mBAA2B,CACzBrC,QAAAA,EACAC,QAAS7lB,KAKb,MAAO,CACL7kB,KAFW2yB,EAAeD,GAG1BtyB,WAAYsyB,EAAI7xB,OAChBR,QAASqyB,EAAIryB,QACbC,UAAWoyB,EAAIryB,QAAQ,oBACvBE,IAAKmyB,EAAIryB,QAAQ,eAEnB,MAAOyD,GAAK,QACZ,GACEskC,EAAM6B,aAAanmC,aACnBA,EAAI/D,oBAAJgtC,EAAc1sC,UAAd2sC,EAAwB,oBACxB,CAEA,MAAMjtC,EAA8C+D,EAAI/D,SAGxD,MAFA4qC,GAAQ,4BAA6B5qC,GACxB,IAAIH,EAAeG,GAMlC,MADA4qC,GAAQ,QAAS7mC,GACXA,GAIiBspB,kBACzB0G,EACAmP,EACAtgC,EACAtC,EACA2tB,EACAwE,GAEA,MAAMgV,EAAe1T,GAAUpzB,KAAK8xB,KAAKsB,OACzC,IAAK0T,EACH,MAAM,IAAI1mC,EAAe,6BAE3B,OAAOJ,KAAKusC,MAAMhK,EAAQ,IAAKtgC,EAAOtC,EAAS2tB,EAAM,IAChDwE,EACHmZ,gBAAiBnE,IAIOpa,mBAC1B7iB,EACA04B,EACAtgC,EACAtC,EACA2tB,EACAwE,GAEA,MAAMgV,EACc,iBAAVj9B,GAAsBA,EAAMupB,QAAWpzB,KAAK8xB,KAAKsB,OACrD2T,EAA6B,iBAAVl9B,EAAqBA,EAAQA,EAAMpJ,IAC5D,IAAKqmC,EACH,MAAM,IAAI1mC,EAAe,6BAI3B,OAFAgtB,GAAmB2Z,GAEZ/mC,KAAKusC,MACVhK,MACIjgC,mBAAmBykC,GACvB9kC,EACAtC,EACA2tB,EACA,IACKwE,EACHmZ,gBAAiBnE,IAKblG,kBACR/2B,GAEA,MAAM0hC,EAAS,IAAIxI,GACjB/iC,KAAK8xB,KAAK0Z,SACVxrC,KAAK8xB,KAAK2Z,gBACVzrC,KAAK8xB,KAAKmR,aAGN9e,EAAM,IAAI+a,GACd,CACES,UAAW,mBACXiC,OAAQ5hC,KAAK8xB,KAAKwL,SAClBuE,YAAa,MAEbzO,OAAQvpB,EAAMupB,OACdiN,cAAergC,KAAK8xB,KAAK0Z,UAE3BD,GAGF,MAAI,WAAY1hC,EACPsa,EAAIgd,wBACT,CACEE,OAAQx3B,EAAMw3B,QAEhBx3B,EAAMnF,SAGDyf,EAAIyc,kBACT,CACE2B,OAAQ14B,EAAM04B,OACdhC,KAAM12B,EAAM02B,KACZJ,UAAWt2B,EAAM2iC,UAAY3iC,EAAMyzB,cAAW3P,EAC9CuS,KAAMr2B,EAAMyzB,SACZr7B,MAAO4H,EAAM5H,OAEf4H,EAAMnF,SAiBF+nC,qBACR5iC,GAEA,MAAyB,iBAAVA,EAAqB,CAAEupB,OAAQvpB,GAAUA,EAEhDC,qBACRD,GAEA,MAAyB,iBAAVA,EAAqB,CAAEpJ,IAAKoJ,GAAUA,GA4BzD,SAASghC,KAKP,GAAsB,oBAAX6B,aAAqD,IAApBA,OAAOC,SAKnD,QAAQ,GACN,IAAmB,oBAAPnJ,GACZ,IAAqB,oBAATE,KACZ,IAAmB,oBAAPC,GACZ,IAAmB,oBAAPE,GACV,OAAOV,GACT,IAAoB,oBAARyJ,IACV,OAAOC,EACT,QACE,QCjkBC5pC,eAAe6pC,GAEpBjjC,EAA0B,IAE1B,SAAmBe,GAAcf,EAC3BgB,QAAY7K,KAAK+sC,YACrBljC,EAAMupB,OACN,MACA5xB,EAAsBoJ,GACtB,IAEIE,EAAYvK,EAAcsK,EAAIvL,MAKpC,OAJAwL,EAAU,kBACVA,EAAU,YACVA,EAAU,YACVA,EAAU,iBACHD,EA8BF5H,eAAe+pC,GAEpBnjC,EAAiC,IAEjC,SAAmBe,GAAcf,EAC3BgB,QAAY7K,KAAK+sC,YACrBljC,EAAMupB,OACN,MACA5xB,EAAsB,CAAEyrC,SAAU,MAAOriC,IACzC,IAEIE,EAAYvK,EAAcsK,EAAIvL,MAIpC,OAHAwL,EAAU,kBACVA,EAAU,YACVA,EAAU,iBACHD,EC7EF5H,eAAeiqC,GAEpBrjC,EAA+B,IAE/B,MAAMsjC,aAAEA,GAAe,GAAUtjC,EAEjC,IAAI87B,EAKJ,GAJK97B,EAAMujC,UACTvjC,EAAMujC,QAViB,KAarBD,EACFxH,QAAe0H,GAAqBva,KAAK9yB,KAAM6J,OAC1C,CACL,MAAMujC,EAAUvjC,EAAMujC,QACtB,IAAI9kC,EAAS,IACRuB,EACHujC,QAAAA,GAEF,OAAa,CACX,MAAMpb,QAAYqb,GAAqBva,KAAK9yB,KAAMsI,GAiBlD,GAhBc,MAAVq9B,EACFA,EAAS3T,GAET2T,EAAS,IACJ3T,EACH1yB,KAAMqmC,EAAOrmC,MAEfqmC,EAAOrmC,KAAKguC,UAAYtb,EAAI1yB,KAAKguC,SACjC3H,EAAOrmC,KAAKiuC,YAAcvb,EAAI1yB,KAAKiuC,YACnC5H,EAAOrmC,KAAKkuC,sBAAwBxb,EAAI1yB,KAAKkuC,sBAC7C7H,EAAOrmC,KAAKmuC,SAAW9H,EAAOrmC,KAAKmuC,SAASC,OAAO1b,EAAI1yB,KAAKmuC,UAC5D9H,EAAOrmC,KAAKquC,eAAiBhI,EAAOrmC,KAAKquC,eAAeD,OACtD1b,EAAI1yB,KAAKquC,kBAIR3b,EAAI1yB,KAAKiuC,aAAe5H,EAAOrmC,KAAKguC,UAAYF,EACnD,MAGF9kC,EAAOslC,kBAAoB5b,EAAI1yB,KAAKkuC,sBACpCllC,EAAO8kC,QAAU9kC,EAAO8kC,QAAUpb,EAAI1yB,KAAKguC,UAI/C,OAAO3H,EAET1iC,eAAeoqC,GAEbxjC,GAEA,SAAmBe,GAAcf,EAE3BgB,QAAY7K,KAAK+sC,YACrBljC,EAAMupB,OACN,MACA,CACEya,YAAa,KACVrsC,EAAsBoJ,IAE3B,IAEIE,EAAYvK,EAAcsK,EAAIvL,MAGpC,OAFAwL,EAAU,kBACVA,EAAU,YACHD,QCzHIijC,WAAwBvH,GAKnCwH,kBACoB/tC,KAAKymC,UAEb8B,aAAahF,QAAQiF,IAAKpF,IAClC,MAAMzjC,EAAUyjC,EAAOzjC,SAAW,GAUlC,cATOA,EAAO,cACdA,EAAO,KAAWK,KAAKguC,mBAAmB9N,KAC1CkD,EAAOkB,QAAUtkC,KAAKguC,mBAAmBC,OACzC7K,EAAO/6B,iBAAoBC,IACzB,MAAM4lC,EAAc7lC,EAAiBC,GACrC,MAAO,CAACtI,KAAKguC,mBAAmBG,OAAQD,GACrCpY,OAAQ10B,GAAOA,EAAGstB,QAClBnsB,KAAK,MAEH6gC,IAIXhkC,YAAYonC,GACVjnC,MAAM,IACDinC,EAEHpT,OAAQ,cACRwO,OAAQ,cACRqB,YAAa,mBACbwI,gBAAiB,uBACjBnO,SAAU,2BA9Bd8Q,gCAEQJ,+BAiDR5S,WAAaA,QACb0C,YAAcA,QACdgP,YAAcA,QACdI,iBAAmBA,QACnBF,mBAAqBA,QACrBlO,aAAeA,GAvBb9+B,KAAKouC,oBAAsB5H,EAC3BxmC,KAAKguC,mBAAqBhuC,KAAKquC,yBAC/BruC,KAAK+tC,kBAGCM,yBACN,MACMC,EAAUtuC,KAAKouC,oBAAoBG,UAAUrF,MADvC,+CAEZ,IAAKoF,EACH,MAAM,IAAIluC,EAAe,oCAE3B,MAAO,CACL6tC,OAAQK,EAAQ,GAChBpO,KAAMoO,EAAQ,GACdH,OAAQG,EAAQ,KCpBfrrC,eAAeurC,GAA2B3kC,EAAyB,IACxE,MAAMlK,EAAU,UAIhBkK,SAAAA,EAAO7F,cACL8D,EAAmB,IAAK+B,EAAOlK,QAAAA,GAAW,CAAC,gBAC7C,MAAMqyB,QAAYhyB,KAAKusC,MAAwB,MAAO,IAAK,GAAI5sC,GAI/D,OAHkBY,EAAcyxB,EAAI1yB,KACpCwL,CAAU,WAEHknB,EAGF/uB,eAAewrC,GAA4B5kC,GAChD,MAAMi9B,EAAej9B,EAAMupB,QAAUpzB,KAAK8xB,KAAKsB,OAE/C,GAAI0T,EAAc,CAChB,GAAIA,EAAa/+B,OAAS,GAAK++B,EAAa/+B,OAAS,GACnD,MAAM,IAAI3H,EACR,mDAGJ,IAAK,kBAAkBgrC,KAAKtE,GAC1B,MAAM,IAAI1mC,EACR,qDAGJ,GAAI,KAAKgrC,KAAKtE,IAAiB,KAAKsE,KAAKtE,GACvC,MAAM,IAAI1mC,+FAKd,MAAMT,EAAWkK,EAAMlK,QAAU6C,EAAoBqH,EAAMlK,SAoB3D,OAlBAmI,EAAmB+B,EAAO,CACxB,MACA,mBACA,YACA,eACA,aACA,gBACA,eACA,eACA,sBAMFA,SAAAA,EAAO7F,cAAe8D,EAAmB+B,EAAO,CAAC,sBAE/B7J,KAAK+sC,YAAYljC,EAAMupB,OAAQ,MAAO,GAAIzzB,GAIvDsD,eAAeyrC,GAA4Btb,GAChD,OAAOpzB,KAAK+sC,YAAY3Z,EAAQ,SAAU,GAAI,IAUzCnwB,eAAe0rC,GAA0Bvb,GAC9C,OAAOpzB,KAAK+sC,YAA8B3Z,EAAQ,OAAQ,GAAI,QAAIzF,EAAW,CAC3EsE,eAAiBD,IACR,IACFA,EAAIryB,QACPivC,YAAa5c,EAAIryB,QAAQwtB,EAAU0hB,uBAapC5rC,eAAe6rC,GAEpBjlC,GAEA,MAAMupB,OAAEA,EAAF7sB,aAAUA,GAAiBsD,EAEjC,OAAO7J,KAAK+sC,YACV3Z,EACA,MACA,CAAE7sB,aAAc,IAChB,CACEwoC,sBAAuBxoC,ICzItBtD,eAAe+rC,GAA4BnlC,GAChD,MAAMlK,EAAmB,GAWzB,OAVIkK,EAAM7E,MAAKrF,EAAQ,aAAekK,EAAM7E,WAE1BhF,KAAK+sC,YACrBljC,EAAMupB,OACN,MACA,CAAEpuB,IAAK,IACPrF,EACAkK,EAAMolC,QACN,CAAEnE,SAAS,IAKR7nC,eAAeisC,GAA4B9b,GAChD,MAAMpB,QAAYhyB,KAAK+sC,YACrB3Z,EACA,MACA,CACEpuB,IAAK,IAEP,IAIF,OAFkBzE,EAAcyxB,EAAI1yB,KACpCwL,CAAU,UACHknB,ECsFF/uB,eAAeksC,GAAyBtlC,GAC7C,OAAOulC,GAAWtc,KAAK9yB,KAAM6J,GAGxB5G,eAAemsC,GAEpBvlC,GAGA,MAAMlK,GADNkK,EAAQ7J,KAAK8J,qBAAqBD,IACXlK,QAAU6C,EAAoBqH,EAAMlK,SAC3DmI,EAAmB+B,EAAO,CACxB,gBACA,aACA,gBACA,eACA,qBACA,kBACA,kBACA,cACA,UACA,MACA,mBACA,YACA,eACA,aACA,gBACA,gBACA,UACA,aACA,uBACA,2BACA,OACA,0BACA,eACA,eACA,WACA,cACA,kBACA,YAEF7J,KAAK+J,2BAA2BF,EAAOlK,GAEvC,MAAMwK,EAAYkjB,GAAQxjB,EAAMyjB,KAAM3tB,GAChCoxB,EAA8B,MAAb5mB,EAElB4mB,IAAmBlnB,EAAMmnB,2BAA4BnnB,EAAMonB,UAC9DpoB,QAAQC,kKAKV,IAAIooB,EAAgB,EACpB,MAAMF,yBAAEA,EAAFC,SAA4BA,GAAapnB,EACzCsnB,EAAsB,CAC1BC,EACAC,EAAsB,KAGtB,IAAKN,GAAkBM,EAAc,EACnC,OAEF,IAAKL,IAA6BC,EAChC,OAEFC,GAAiBG,QAEjBL,GAAAA,EAA2B,CACzBI,KAAAA,EACAC,YAAAA,EACAH,cAAAA,EACAI,WAAYnnB,IAEd,MAAMonB,EACc,IAAdpnB,EACEinB,IAAS/B,yBAAiBmC,QACrB,EAEF,EAEFN,EAAgB/mB,EAEH,IAAlBonB,EACEH,IAAS/B,yBAAiBmC,gBAC5BP,GAAAA,EAAWM,UAKbN,GAAAA,EAAWM,IAITE,QAAmBjE,GAAiB,CACxCF,KAAMzjB,EAAMyjB,KACZoE,qBAAuBC,GAAMR,EAAoB9B,yBAAiBuC,GAAID,GACtEjE,gBAAiB7jB,EAAM6jB,gBACvBmE,UAAW7xB,KAAK8xB,KAAKD,UACrB/E,YAAajjB,EAAMijB,cAGrBqE,EAAoB9B,yBAAiB0C,SAErC,MAqCO3uB,EAAK4uB,SAAa9uB,EArCZD,WACX,MAAM+uB,QAAYhyB,KAAKgK,aACrBH,EACA,MACA,GACAlK,EACA8xB,EAAWnE,MAAQ,GACnB,CACE2E,eAAiBD,UACf,MAAMiB,EAAS,IAAKjB,EAAIryB,SAIxB,gBAHKkK,MAA+BnD,UAAYsrB,EAAI1yB,OAClD2zB,EAAOO,kBAAoBC,KAAKjrB,UAAUwpB,EAAI1yB,OAEzC2zB,GAETX,UAAW,CACTC,gBAAkB,CAChB1E,YAAa,KACXqD,EAAgB,QAChBO,EAAW5D,aAAX4D,EAAW5D,eAEbH,gBAAiB+D,EAAW/D,iBAE9B8E,iBAAmBC,IACjBtB,EACE9B,yBAAiBuC,GACjBa,EAAMC,OAASxB,OASzB,OAHIlxB,KAAK8xB,KAAKD,WAAaJ,EAAW9oB,KACpCD,EAAsB+oB,EAAW9oB,IAAKqpB,EAAIryB,SAErCqyB,GAE0BW,IAEnC,GAAIvvB,IAAQ4uB,EAEV,MADAb,EAAoB9B,yBAAiBuD,QAC/BxvB,EAIR,OADA+tB,EAAoB9B,yBAAiBmC,SAC9BQ,EAOF/uB,eAAeosC,GAEpBxlC,GAIE,MAFwBrH,EAAoBqH,EAAMlK,SAE5C,IAAIS,EACR,4DCvPC6C,eAAeqsC,GAA2BzlC,GAC/C,MAAMlK,EAAWkK,EAAMlK,QAAU6C,EAAoBqH,EAAMlK,SA6B3D,OA5BAmI,EAAmB+B,EAAO,CACxB,MACA,mBACA,YACA,eACA,gBACA,gBACA,UACA,aACA,OACA,uBAEgB7J,KAAKgK,aACrBH,EACA,OACA,CACE0iC,MAAO,IAET5sC,EACA,CACE4vC,IAAK1lC,EAAM9G,IACXysC,cAAe3lC,EAAM4lC,cACrBC,WAAY7lC,EAAMxF,YAEpB,CACEymC,SAAS,IAoCR7nC,eAAe0sC,GAA4B9lC,GAChD,MAAMlK,EAAWkK,EAAMlK,QAAU6C,EAAoBqH,EAAMlK,SA+B3D,OA9BAmI,EAAmB+B,EAAO,CACxB,MACA,mBACA,YACA,eACA,gBACA,gBACA,UACA,aACA,OACA,uBAGgB7J,KAAKgK,aACrBH,EACA,OACA,CACE+lC,UAAW,IAEbjwC,EACA,CACE4vC,IAAK1lC,EAAM9G,IACXysC,cAAe3lC,EAAM4lC,cACrBC,WAAY7lC,EAAMxF,WAClBvE,OAAQ+J,EAAMpJ,KAEhB,CACEqqC,SAAS,aCpGC+E,GAEdhmC,GAEAujB,GAAmBvjB,GACnB,MAAMwxB,EAAmC,iBAAVxxB,EAAqB,CAAEpJ,IAAKoJ,GAAUA,EAC/DyzB,EAAWjC,EAAgByU,qBAAuB9vC,KAAK8xB,KAAKwL,SAC5DkP,GACJnR,EAAgByU,sBAAuBzU,EAAgB8P,eAGnD/X,EAASiI,EAAgBjI,QAAUpzB,KAAK8xB,KAAKsB,QAAU,GAC7D,GAAIoZ,IAAcpZ,EAChB,MAAM,IAAIhzB,EAAe,6BAG3B,MAAO2vC,EAAS/E,EAASgF,GAAe,MACtC,MAAMC,EAAa3tC,mBAAmB+4B,EAAgB56B,KAChDyvC,EAAgB7U,EAAgB56B,IACnC6tB,MAAM,KACNntB,IAAKC,GAAOkB,mBAAmBlB,IAC/BmB,KAAK,KAER,OAAIiqC,EACK,IAAIpZ,KAAUkK,QAAgB4S,MAAqBD,GAErD,CAAC3S,MAAc4S,MAAqBD,IAVL,GAalCrlC,EAAiCywB,EAAgBp5B,OAAS,GAC1DkuC,EAAc,CAACloC,EAAWvE,KACV,MAAhBkH,EAAU3C,IAAmB,MAALvE,IAC1BkH,EAAU3C,GAAKvE,IAGbrE,EAAWg8B,EAAgBh8B,UAAY,GAC7CS,OAAOuB,KAAKhC,GAAU+C,QAASguC,IAC7B,MAAM3vC,EAAM2vC,EACNC,EAAW7uC,EAAsBf,GACvC0vC,cAAwBE,EAAYhxC,EAASoB,MAE3C46B,EAAgBj0B,WAClB+oC,EAAY,YAAa9U,EAAgBj0B,WAG3C,MAAMnF,EAAQjC,KAAK4gC,kBAAkB,CACnCxN,OAAAA,EACAmP,OAAQlH,EAAgBkH,QAAU,MAClChC,KAAMyP,EACN1S,SAAAA,EACAkP,UAAAA,EACA9nC,QAAS22B,EAAgB32B,SAAW,KACpCzC,MAAO2I,IAGHihC,EAAkBhpC,EAAe7C,KAAK8xB,KAAKhvB,OACjD,IAAIwhC,SAAiBtkC,KAAK8xB,KAAKuL,OAAS,IAAM,QAAQ0S,IAgBtD,aAfIlE,GAAAA,EAAiB9oC,MAGnBuhC,EAAUuH,EAAgB9oC,IAAIrB,QAAQ,QAAS,UAC3CmqC,GAAAA,EAAiB7oC,kBACnBf,EAAM,oBAAsB8tC,OAUtBzL,IAAU0G,KANHlrC,OAAOuB,KAAKY,GAC1Bd,IAAKV,MACM6B,mBAAmB7B,MAAQ6B,mBAAmBL,EAAMxB,OAE/D8B,KAAK,OClFHU,eAAeqtC,GAEpBzmC,GAEA,MAAMwxB,EAAmC,iBAAVxxB,EAAqB,CAAEpJ,IAAKoJ,GAAUA,EAC/D5H,EAA6B,GAkBnC,OAjBIo5B,EAAgBj0B,YAClBnF,EAAMmF,UAAYi0B,EAAgBj0B,WAEhCi0B,EAAgBkV,YAClBtuC,EAAMsuC,UAAYlV,EAAgBkV,WAEhClV,EAAgBmV,YAClBvuC,EAAMuuC,UAAYnV,EAAgBmV,iBAElBxwC,KAAKgK,aACrBH,EACA,SACA5H,EACA,GACA,GACA,CAAEgwB,eAAiBD,GAAQA,EAAIryB,UCzB5BsD,eAAewtC,GAA4B5mC,GAGhD,OAFAA,EAAMlK,QAAUkK,EAAMlK,SAAW,GACjCmI,EAAmB+B,EAAO,CAAC,iBAAkB,oBACtC7J,KAAKgK,aACVH,EACA,MACA,CAAE6mC,OAAQ,GAAIC,KAAM9mC,EAAMjH,QAC1BiH,EAAMlK,QACN,ICaGsD,eAAe2tC,GAEpB/mC,GAEA,MAAMyjB,EAAO,CACXujB,MAAOhnC,EAAMinC,MACbC,QAASlnC,EAAMmnC,QAAQ7vC,IAAKC,KAC1BmyB,IAAKnyB,EAAGX,IACRwwC,UAAW7vC,EAAGgG,cAIZnF,EAAgC,CACpCivC,OAAQ,IAGNrnC,EAAM0mC,YACRtuC,EAAMsuC,UAAY1mC,EAAM0mC,WAGtB1mC,EAAM2mC,YACRvuC,EAAMuuC,UAAY3mC,EAAM2mC,WAG1B,MAAMxe,QAAYhyB,KAAK+sC,YACrBljC,EAAMupB,OACN,OACAnxB,EACA,GACAqrB,GAGIxiB,EAAYvK,EAAcyxB,EAAI1yB,MAIpC,OAHAwL,EAAU,WACVA,EAAU,SAEHknB,ECtDF/uB,eAAekuC,GAEpBtnC,GAEA,MAAMwxB,EAAmC,iBAAVxxB,EAAqB,CAAEpJ,IAAKoJ,GAAUA,EAC/D5H,EAA6B,CAAE+C,IAAK,IACtCq2B,EAAgBj0B,YAClBnF,EAAMmF,UAAYi0B,EAAgBj0B,WAGpC,MAAM4qB,QAAYhyB,KAAKgK,aAAiCH,EAAO,MAAO5H,EAAO,IAK7E,OAHkB1B,EAAcyxB,EAAI1yB,KACpCwL,CAAU,UAEHknB,EAeF/uB,eAAemuC,GAA4BvnC,GAChD,MAAMlK,EAAWkK,EAAMlK,QAAU6C,EAAoBqH,EAAMlK,SACrDsC,EAA6B,CAAE+C,IAAK,IAM1C,OALI6E,EAAMzC,YACRnF,EAAMmF,UAAYyC,EAAMzC,WAE1BU,EAAmB+B,EAAO,CAAC,QAEpB7J,KAAKgK,aACVH,EACA,MACA5H,EACAtC,EACAkK,EAAMolC,SCpDHhsC,eAAeouC,GAEpBxnC,GAEA,OAAO7J,KAAKgK,aACVH,EACA,SACA,CACEc,SAAUd,EAAMc,UAElB,ICeG1H,eAAequC,GAEpBznC,EAAmC,IAEnC,SAAmBe,GAAcf,EAC3BgB,QAAY7K,KAAK+sC,YACrBljC,EAAMupB,OACN,MACA,CACEnpB,QAAS,MACNzI,EAAsBoJ,IAE3B,IAGIE,EAAYvK,EAAcsK,EAAIvL,MAIpC,OAHAwL,EAAU,WACVA,EAAU,kBAEHD,ECkCF5H,eAAesuC,GAEpB1nC,GAEA,MAAMwxB,EAAmBxxB,EAAQ7J,KAAK8J,qBAAqBD,GACrDlK,EAAWkK,EAAMlK,QAAU6C,EAAoBqH,EAAMlK,SAC3DmI,EAAmB+B,EAAO,CACxB,gBACA,eACA,qBACA,kBACA,kBACA,cACA,UACA,MACA,mBACA,YACA,eACA,gBACA,OACA,0BACA,eACA,iBAEF7J,KAAK+J,2BAA2BF,EAAOlK,GAEvC,MAAMwK,EAAYkjB,GAAQxjB,EAAMyjB,KAAM3tB,GAChCoxB,EAA8B,MAAb5mB,EACvB,IAAK4mB,EACH,MAAM,IAAI3wB,8DAMZ,GAFAT,EAAQ,kBAAoBA,EAAQ,sBAAwBwK,EAExDnK,KAAK8xB,KAAKD,WAA8B,IAAjBhoB,EAAM2rB,SAAiB3rB,EAAM2nC,iBACtD,MAAM,IAAIpxC,EACR,6EAIJ,IAAI8wB,EAAgB,EACpB,MAAMF,yBAAEA,EAAFC,SAA4BA,GAAapnB,EACzCsnB,EAAsB,CAC1BC,EACAC,EAAsB,KAGtB,IAAKN,GAAkBM,EAAc,EACnC,OAEF,IAAKL,IAA6BC,EAChC,OAEFC,GAAiBG,QAEjBL,GAAAA,EAA2B,CACzBI,KAAAA,EACAC,YAAAA,EACAH,cAAAA,EACAI,WAAYnnB,IAEd,MAAMonB,EACc,IAAdpnB,EACEinB,IAAS/B,yBAAiBmC,QACrB,EAEF,EAEFN,EAAgB/mB,EAEH,IAAlBonB,EACEH,IAAS/B,yBAAiBmC,gBAC5BP,GAAAA,EAAWM,UAKbN,GAAAA,EAAWM,IAITE,QAAmBjE,GAAiB,CACxCF,KAAMzjB,EAAMyjB,KACZoE,qBAAuBC,GAAMR,EAAoB9B,yBAAiBuC,GAAID,GACtEjE,qBAAiBC,EACjBkE,UAAW7xB,KAAK8xB,KAAKD,UACrB/E,YAAajjB,EAAMijB,cAGrBqE,EAAoB9B,yBAAiB0C,SACrC,MAwCO3uB,EAAK4uB,SAAa9uB,EAxCZD,WACX,MAAM+uB,QAAYhyB,KAAKgK,aACrBH,EACA,OACA,CAAE4nC,OAAQ,GAAIjc,OAAQ6F,EAAgB7F,QACtC71B,EACA8xB,EAAWnE,MAAQ,GACnB,CACE2E,eAAiBD,QACZA,EAAIryB,QACP+xC,kBAAmB1f,EAAIryB,QAAQ,4BAC/B0yB,cAAeL,EAAIryB,QAAQ,0BAE7B2yB,UAAW,CACTC,gBAAkB,CAChB1E,YAAa,KACXqD,EAAgB,QAChBO,EAAW5D,aAAX4D,EAAW5D,eAEbH,gBAAiB+D,EAAW/D,iBAE9B8E,iBAAmBC,IACjBtB,EACE9B,yBAAiBuC,GACjBa,EAAMC,OAASxB,OAczB,OARIlxB,KAAK8xB,KAAKD,WAAaJ,EAAW9oB,KAMpCD,EALwBmkB,EACtBwO,EAAgBmW,kBAAoB,IACpC/f,EAAW9oB,IAAIK,WACfmB,GAEqC6nB,EAAIryB,SAEtCqyB,GAE0BW,IAEnC,GAAIvvB,IAAQ4uB,EAEV,MADAb,EAAoB9B,yBAAiBuD,QAC/BxvB,EAIR,OADA+tB,EAAoB9B,yBAAiBmC,SAC9BQ,ECtMF/uB,eAAe0uC,GAEpB9nC,GAEA,MAAMwxB,EAAmC,iBAAVxxB,EAAqB,CAAEpJ,IAAKoJ,GAAUA,EAC/DlK,EAAW07B,EAAgB17B,QAAU6C,EACzC64B,EAAgB17B,SAElBmI,EAAmBuzB,EAAiB,CAClC,eACA,qBACA,kBACA,kBACA,cACA,UACA,SAEF,MAAMp5B,EAA6B,CAAE2vC,SAAU,IAK/C,OAJIvW,EAAgBj0B,YAClBnF,EAAMmF,UAAYi0B,EAAgBj0B,WAG7BpH,KAAKgK,aAAwBH,EAAO,OAAQ5H,EAAOtC,GC3BrDsD,eAAe4uC,GAEpBhoC,GAEAujB,GAAmBvjB,GACnBA,EAAQ7J,KAAK8J,qBAAqBD,GAClC,MAAMioC,UAAEA,EAAY,KAAdrxC,IAAoBA,GAAQoJ,EAC5BupB,EAASvpB,EAAMupB,QAAUpzB,KAAK8xB,KAAKsB,OACnC2e,EAAS,IAAKloC,EAAMkoC,QACpBC,EAAa,IAAKnoC,EAAMmoC,YAAc,IAE5C,IAAK5e,EACH,MAAM,IAAIhzB,EAAe,6BAG3B,MAAMqrC,EAAkBzrC,KAAK8xB,KAAK2Z,gBAC5B3J,EAAO,IAAI5M,KACX+c,EAAoBC,GAAe,CACvCpQ,KAAM,IAAI5M,KAAK4M,EAAKqQ,UAAwB,IAAZL,GAChC1gB,KAAM,QAEFghB,EAAUF,KACVG,EAAWD,EAAQE,UAAU,EAAG,GAChCC,EAAU,MACVC,EAAa,UAEbzQ,EAAQtR,GAAWgb,EAAiB4G,GACpCpQ,EAAUxR,GAAWsR,EAAO/hC,KAAK8xB,KAAK8P,QACtCM,EAAWzR,GAAWwR,EAASsQ,GAC/BjR,EAAa7Q,GAAWyR,EAAUsQ,GAUlCC,EAAsC,CAC1ChyC,IAAAA,EACAiyC,kBAAmB,mBACnBC,aAAcP,EACdQ,mBAZiB,CACjB5yC,KAAK8xB,KAAKmR,YACVoP,EACAryC,KAAK8xB,KAAK8P,OACV2Q,EACAC,GACAjwC,KAAK,MAQHvC,KAAK8xB,KAAK0Z,WACZiH,EAAY,wBAA0BzyC,KAAK8xB,KAAK0Z,UAGlDwG,EAAW3vC,KAAK,CAAE+wB,OAAAA,IAClBtzB,OAAOsI,QAAQqqC,GAAarwC,QAAQ,EAAE3B,EAAKC,MACzCqxC,EAAOtxC,GAAOC,IAEhBZ,OAAOsI,QAAQ2pC,GAAQ3vC,QAAQ,EAAE3B,EAAKC,MACpCsxC,EAAW3vC,KAAK,CAAEqqB,CAACjsB,MAASC,MAG9B,MAIMmyC,EAAYpf,KAAKjrB,UAJR,CACbsqC,WAAYb,EACZD,WAAAA,IAGIe,EAAevqC,GAAUgoB,GAAMqiB,EAAW,SAAU,UACpDvT,EAAY7O,GAAW6Q,EAAYyR,EAAc,OAKvD,OAHAhB,EAAO1Q,OAAS0R,EAChBhB,EAAO,mBAAqBzS,EAErByS,EAUT,SAASG,GAAe/S,GACtB,MAAM2C,KAAEA,EAAO,IAAI5M,KAAb9D,KAAqBA,EAAO,KAAQ+N,GAAO,GACjD,MAAa,QAAT/N,EACK0Q,EAAKJ,cAIZI,EAAKJ,cAAchgC,QAAQ,OAAQ,IAAIA,QAAQ,KAAM,IAAIA,QAAQ,KAAM,IACvE,ICzGJ,MAAMsxC,GAAiD,CACrDC,uBAAuB,EACvBC,6BAA6B,EAC7BC,oBAAoB,EACpBC,qBAAqB,EACrBC,qBAAqB,EACrBC,uBAAuB,EACvBC,iBAAiB,EACjBC,sBAAsB,EACtBC,sBAAsB,EACtBC,kBAAkB,EAClBC,kBAAkB,YAGJC,GACdxwC,EACA0uB,GAMA,MAAM+hB,4BAAEA,EAAFC,UAA+BA,EAA/BC,gBAA0CA,GAAoBjiB,EACpE,GAAI1uB,aAAelE,EACjB,GAAI20C,GACF,GAAuB,MAAnBzwC,EAAI1D,WACN,OAAO+I,EAAuBsrC,EAAiB3wC,QAI9C,QAAoCuqB,IAAhCkmB,GACgB,MAAnBzwC,EAAI1D,YAAsBszC,GAAsBc,GAClD,OAAOrrC,EAAuBsrC,EAAiB3wC,GAIrD,MAAMA,ECJDH,eAAe+wC,GAEpBnqC,GAEA,OACG7J,KAAK8xB,KAAKmiB,mCACkCtmB,IAA3C3tB,KAAK8xB,KAAKmiB,8BACXpqC,EAAMw3B,OAAO6S,UAAUnsC,aAKR/H,KAAK+sC,YACrBljC,EAAMupB,OACN,MACA,CAAEiO,OAAQ,IACV,GACAx3B,EAAMw3B,OACN,CAAEyJ,SAAS,IATJqJ,GAAmBrhB,KAAK9yB,KAAM6J,EAAMupB,QAcxCnwB,eAAeswC,GAEpBngB,GAEA,IACE,MAAMpB,QAAYhyB,KAAK+sC,YACrB3Z,EACA,MACA,CACEiO,OAAQ,IAEV,IAWF,OATArP,EAAI1yB,KAAK40C,UAAU9xC,QAAShB,IAC1B,MAAM0J,EAAYvK,EAAca,GAEhCtB,OAAOuB,KAAKD,EAAGgzC,WAAa,IAAIhyC,QAAS3B,IACvCX,OAAOuB,KAAKD,EAAGgzC,UAAU3zC,IAAM2B,QAASiyC,IACtCvpC,gBAAwBrK,QAAU4zC,aAIjCriB,EACP,MAAO8S,GACP,OAAO8O,GAA8C9O,EAAO,CAC1D+O,4BAA6B7zC,KAAK8xB,KAAKmiB,6BACvCH,UAAW,kBACXC,gBAAiB,CACfG,UAAW,GACXI,QAAS,iBAMVrxC,eAAekxC,GAAkC/gB,GACtD,OAAOpzB,KAAK+sC,YAAY3Z,EAAQ,SAAU,CAAEiO,OAAQ,IAAM,IC1ErDp+B,eAAesxC,GAAmCnhB,GACvD,OAAOpzB,KAAK+sC,YACV3Z,EACA,MACA,CAAEohB,WAAY,IACd,IAIGvxC,eAAewxC,GAEpB5qC,GAEA,OAAO7J,KAAK+sC,YACVljC,EAAMupB,OACN,MACA,CAAEohB,WAAY,IACd,GACA,CACEE,OAAQ7qC,EAAM1J,kBCsBJw0C,GAEd9qC,GAEA,MAAMwxB,EAAkBuZ,GAAe9hB,KAAK9yB,KAAM6J,GAElDgrC,GAAmBhrC,EAAMmoC,YAEzB,MAMM1N,SAAiBtkC,KAAK8xB,KAAKuL,OAAS,IAAM,QAL9CxzB,EAAMimC,sBACLjmC,EAAMshC,eACHnrC,KAAK8xB,KAAKwL,YACPjC,EAAgBjI,UAAUpzB,KAAK8xB,KAAKwL,cAYvCwX,EAAWrxC,EARHzD,KAAK4gC,kBAAkB,CACnCxN,OAAQiI,EAAgBjI,OACxB1uB,QAAS22B,EAAgB32B,QACzB28B,OAAQ,CACN2Q,WAAY3W,EAAgB2W,eAwBhC,MAAO,CACL+C,oBAlBAC,IAEA,MAAMC,EAAOxxC,EAAauxC,GACpBE,EAAI,CAACJ,EAAUG,GAAMnf,OAAOoH,SAAS36B,KAAK,KAChD,SAAU+hC,KAAW4Q,KAerBC,yBAZA,CAAC10C,EAAKu0C,KACJ,MAAMC,EAAOxxC,EAAauxC,GACpBE,EAAI,CAACJ,EAAUG,GAAMnf,OAAOoH,SAAS36B,KAAK,KAE1C6yC,EAAU30C,EACb6tB,MAAM,KACNntB,IAAKC,GAAOkB,mBAAmBlB,IAC/BmB,KAAK,KACR,SAAU+hC,KAAW8Q,KAAWF,KAKlCG,YAAaP,GAIjB,SAASF,GAEP/qC,GAEA,MAAMi9B,EAAej9B,EAAMupB,QAAUpzB,KAAK8xB,KAAKsB,OAG/C,IAAK0T,EACH,MAAM,IAAI1mC,EAAe,6BAG3By0C,GAAmBhrC,EAAMmoC,YACzB,MAAMsD,EACJzrC,EAAMmoC,WAAW7wC,IAAKC,GAAO,CAACA,EAAGm0C,UAAY,KAAM,OAAQn0C,EAAGV,QAGhE,OAFA40C,EAAqBjzC,KAAK,CAAC,KAAM,UAAWykC,IAErC,CACL1T,OAAQ0T,EACRpiC,QAASmF,EAAMnF,SAbM,KAcrBstC,WAAYsD,GAIhB,SAAST,GAAmB7C,GAC1B,GAAIA,EAAWjqC,OAAS,EACtB,MAAM,IAAI3H,EACR,kFAIJ,IAAK,MAAMgB,KAAM4wC,EAAY,CAC3B,GAAe,QAAX5wC,EAAGX,IACL,MAAM,IAAIL,EACR,iEAIJ,GAAIgB,EAAGm0C,UAA4B,OAAhBn0C,EAAGm0C,UAAqC,gBAAhBn0C,EAAGm0C,SAC5C,MAAM,IAAIn1C,EACR,yFC3ID6C,eAAeuyC,GAEpB3rC,GAEA,MAAMupB,OAAEA,GAAWvpB,EAEnB,OAAO7J,KAAK+sC,YACV3Z,EACA,MACA,CAAEuZ,SAAU,IACZ,ICDG1pC,eAAewyC,GAA6B5rC,GACjD,IACE,MAAMupB,OAAEA,GAAWvpB,EAEnB,aAAa7J,KAAK+sC,YAChB3Z,EACA,MACA,CAAEsiB,KAAM,IACR,IAEF,MAAO5Q,GACP,OAAO8O,GAA4C9O,EAAO,CACxDiP,gBAAiB,CAAE4B,UAAW,IAC9B9B,4BAA6B7zC,KAAK8xB,KAAKmiB,6BACvCH,UAAW,mBAYV7wC,eAAe2yC,GAA6B/rC,GACjD,MAAMupB,OAAEA,EAAFuiB,UAAUA,GAAc9rC,EAC9B,OAAI7J,KAAK8xB,KAAKmiB,+BAAiC0B,EAAU5tC,OAChD8tC,GAAiB/iB,KAAK9yB,KAAM,CAAEozB,OAAAA,IAEhCpzB,KAAK+sC,YACV3Z,EACA,MACA,CAAEsiB,KAAM,IACR,GACA,CAAEC,UAAAA,IAUC1yC,eAAe4yC,GAEpBhsC,GAEA,MAAMupB,OAAEA,GAAWvpB,EAEnB,OAAO7J,KAAK+sC,YACV3Z,EACA,SACA,CAAEsiB,KAAM,IACR,ICbGzyC,eAAe6yC,GAEpBjsC,GAEA,MAAMupB,OAAEA,EAAF2iB,MAAUA,GAAUlsC,EAC1B,GAAI7J,KAAK8xB,KAAKmiB,+BAAiC8B,EAAMhuC,OACnD,OAAOiuC,GAAsBljB,KAAK9yB,KAAM,CAAEozB,OAAAA,IAG5C,MAAMzzB,EAAU,GAGhB,OAFAmI,EAAmB,IAAK+B,EAAOlK,QAAAA,GAAW,CAAC,2BAEpCK,KAAK+sC,YACV3Z,EACA,MACA,CAAE6iB,UAAW,IACbt2C,EACA,CACEu2C,MAAOH,IAcN9yC,eAAekzC,GAEpBtsC,GAEA,IACE,MAAMupB,OAAEA,GAAWvpB,EAEnB,aAAa7J,KAAK+sC,YAChB3Z,EACA,MACA,CAAE6iB,UAAW,IACb,GACA,GACA,CACEhkB,eAAiBD,IACR,CACLokB,uBACEpkB,EAAIryB,QAAQ,mCACdu2C,MAAOlkB,EAAI1yB,KAAK42C,UAKxB,MAAOpR,GACP,OAAO8O,GAAiD9O,EAAO,CAC7D+O,4BAA6B7zC,KAAK8xB,KAAKmiB,6BACvCH,UAAW,qBACXC,gBAAiB,CACfmC,MAAO,OAYRjzC,eAAe+yC,GAEpBnsC,GAEA,MAAMupB,OAAEA,GAAWvpB,EAEnB,OAAO7J,KAAK+sC,YACV3Z,EACA,SACA,CAAE6iB,UAAW,IACb,IClIGhzC,eAAeozC,GAEpBxsC,GAEA,MAAMupB,OAAEA,EAAFkjB,KAAUA,GAASzsC,EAEzB,OAAO7J,KAAK+sC,YACV3Z,EACA,MACA,CAAEmjB,WAAY,IACd,CACEC,cAAevmB,GACbwD,KAAKjrB,UAAU,CACbiuC,KAAMH,IAER,WAGJ,CACEG,KAAMH,IAKLrzC,eAAeyzC,GAEpB7sC,GAEA,MAAMupB,OAAEA,GAAWvpB,EAEnB,OAAO7J,KAAK+sC,YACV3Z,EACA,MACA,CAAEmjB,WAAY,IACd,IAIGtzC,eAAe0zC,GAEpB9sC,GAEA,MAAMupB,OAAEA,GAAWvpB,EAEnB,OAAO7J,KAAK+sC,YAAY3Z,EAAQ,SAAU,CAAEmjB,WAAY,IAAM,ICMzDtzC,eAAe2zC,GAEpB/sC,GAEA,MAAMupB,OAAEA,EAAF2iB,MAAUA,GAAUlsC,EAC1B,OAAI7J,KAAK8xB,KAAKmiB,+BAAiC8B,EAAMhuC,OAC5C8uC,GAAuB/jB,KAAK9yB,KAAM,CAAEozB,OAAAA,IAGtCpzB,KAAK+sC,YACV3Z,EACA,MACA,CAAE0jB,OAAkB,IACpB,GACA,CACEZ,MAAOH,IAaN9yC,eAAeowC,GAEpBxpC,GAEA,MAAMupB,OAAEA,GAAWvpB,EAEnB,IACE,aAAa7J,KAAK+sC,YAChB3Z,EACA,MACA,CAAE0jB,OAAkB,IACpB,IAEF,MAAOhS,GACP,OAAO8O,GAAkD9O,EAAO,CAC9D+O,4BAA6B7zC,KAAK8xB,KAAKmiB,6BACvCH,UAAW,sBACXC,gBAAiB,CACfmC,MAAO,OAYRjzC,eAAe4zC,GAEpBhtC,GAEA,MAAMupB,OAAEA,GAAWvpB,EAEnB,OAAO7J,KAAK+sC,YACV3Z,EACA,SACA,CAAE0jB,OAAkB,IACpB,ICjHG7zC,eAAe8zC,GAEpBltC,GAEA,MAAMmtC,OAAEA,EAAF5vC,UAAUA,GAAcyC,EACxBlK,EAAU6C,EAAoB,CAClC4E,UAAAA,IAGF,OAAOpH,KAAKgK,aACVH,EACA,MACA,CAAEotC,QAAkB,MAAOt3C,GAC3B,GACA,CACEu3C,OAAQF,IAeP/zC,eAAek0C,GAEpBttC,GAEA,MAAMzC,UAAEA,GAAcyC,EAChBlK,EAAU6C,EAAoB,CAClC4E,UAAAA,IAEI4qB,QAAYhyB,KAAKgK,aACrBH,EAEA,MACA,CAAEotC,QAAkB,MAAOt3C,GAC3B,IAGF,OADAY,EAAcyxB,EAAI1yB,KAAK43C,OAAvB32C,CAA+B,QACxByxB,EAWF/uB,eAAem0C,GAEpBvtC,GAEA,MAAMzC,UAAEA,GAAcyC,EAChBlK,EAAU6C,EAAoB,CAClC4E,UAAAA,IAGF,OAAOpH,KAAKgK,aACVH,EACA,SACA,CAAEotC,QAAkB,MAAOt3C,GAC3B,IC9CGsD,eAAeo0C,GAEpBxtC,GAEA,MAAMupB,OAAEA,EAAF2iB,MAAUA,EAAVuB,KAAiBA,GAASztC,EAChC,OAAI7J,KAAK8xB,KAAKmiB,+BAAiC8B,EAAMhuC,OAC5CwvC,GAAwBzkB,KAAK9yB,KAAM,CAAEozB,OAAAA,IAGvCpzB,KAAK+sC,YACV3Z,EACA,MACA,CAAEokB,YAAkB,IACpB,GACA,CACEC,KAAMH,EACNpB,MAAOH,IAgBN9yC,eAAewwC,GAEpB5pC,GAEA,MAAMupB,OAAEA,EAAFnC,SAAUA,EAAVymB,OAAoBA,GAAW7tC,EAC/B5H,EAAgC,CACpCu1C,YAAkB,GAClBvmB,SAAUA,GAAY,IAEV,MAAVymB,IACFz1C,EAAM,cAAgBy1C,GAGxB,IACE,aAAa13C,KAAK+sC,YAChB3Z,EACA,MACAnxB,EACA,IAEF,MAAOmB,GACP,OAAOwwC,GAAmDxwC,EAAK,CAC7DywC,4BAA6B7zC,KAAK8xB,KAAKmiB,6BACvCH,UAAW,uBACXC,gBAAiB,CACfmC,MAAO,GACPuB,KAAM,OAYPx0C,eAAes0C,GAEpB1tC,GAEA,MAAMupB,OAAEA,GAAWvpB,EAEnB,OAAO7J,KAAK+sC,YACV3Z,EACA,SACA,CAAEokB,YAAkB,IACpB,ICjFGv0C,eAAe00C,GAEpB9tC,GAEA,MAAMupB,OAAEA,KAAWwkB,GAAe/tC,EAE5ByjB,EAAO1rB,EAA6Bg2C,GAC1C,OAAO53C,KAAK+sC,YACV3Z,EACA,MACA,CAAEykB,QAAkB,IACpB,GACA,IACKvqB,IAgBFrqB,eAAe0wC,GAEpB9pC,GAEA,MAAMupB,OAAEA,GAAWvpB,EAEnB,IACE,OAAO7J,KAAK+sC,YACV3Z,EACA,MACA,CAAEykB,QAAkB,IACpB,IAEF,MAAO/S,GACP,OAAO8O,GAA+C9O,EAAO,CAC3D+O,4BAA6B7zC,KAAK8xB,KAAKmiB,6BACvCH,UAAW,mBACXC,gBAAiB,CACf+D,aAAc,OAYf70C,eAAe80C,GAEpBluC,GAEA,MAAMupB,OAAEA,GAAWvpB,EAEnB,OAAO7J,KAAK+sC,YACV3Z,EACA,SACA,CAAEykB,QAAkB,IACpB,ICpEG50C,eAAe+0C,GAEpBnuC,GAEA,MAAMupB,OAAEA,KAAWwkB,GAAe/tC,EAE5ByjB,EAAO1rB,EAA6Bg2C,GAC1C,OAAO53C,KAAK+sC,YACV3Z,EACA,MACA,CAAE6kB,aAAkB,IACpB,GACA,IACK3qB,IAiBFrqB,eAAeqwC,GAEpBzpC,GAEA,MAAMupB,OAAEA,GAAWvpB,EACnB,IACE,aAAa7J,KAAK+sC,YAChB3Z,EACA,MACA,CAAE6kB,aAAkB,IACpB,IAEF,MAAOnT,GACP,OAAO8O,GAAoD9O,EAAO,CAChE+O,4BAA6B7zC,KAAK8xB,KAAKmiB,6BACvCH,UAAW,wBACXC,gBAAiB,CACfmE,4BAA6B,GAC7BC,uBAAwB,OC5DzBl1C,eAAem1C,GAEpBvuC,GAEA,MAAMupB,OAAEA,KAAWwkB,GAAe/tC,EAE5ByjB,EAAO1rB,EAA6Bg2C,GAC1C,OAAO53C,KAAK+sC,YACV3Z,EACA,MACA,CAAEilB,aAAkB,IACpB,GACA,IACK/qB,IAaFrqB,eAAegwC,GAEpBppC,GAEA,IACE,MAAMupB,OAAEA,GAAWvpB,EACnB,aAAa7J,KAAK+sC,YAChB3Z,EACA,MACA,CAAEilB,aAAkB,IACpB,IAEF,MAAOvT,GACP,OAAO8O,GAAoD9O,EAAO,CAChEiP,gBAAiB,CAAEuE,kBAAmB,IACtCzE,4BAA6B7zC,KAAK8xB,KAAKmiB,6BACvCH,UAAW,2BAYV7wC,eAAes1C,GAEpB1uC,GAEA,MAAMupB,OAAEA,EAAFolB,aAAUA,GAAiB3uC,EAEjC,OAAO7J,KAAK+sC,YACV3Z,EACA,SACA,CAAEilB,aAAcG,GAChB,ICzEGv1C,eAAew1C,GAEpB5uC,GAEA,MAAMupB,OAAEA,KAAWwkB,GAAe/tC,EAE5ByjB,EAAO1rB,EAA6Bg2C,GAC1C,OAAO53C,KAAK+sC,YACV3Z,EACA,MACA,CAAEslB,YAAkB,IACpB,GACA,IACKprB,IAaFrqB,eAAeuwC,GAEpB3pC,GAEA,MAAMupB,OAAEA,GAAWvpB,EAEnB,IACE,aAAa7J,KAAK+sC,YAChB3Z,EACA,MACA,CAAEslB,YAAkB,IACpB,IAEF,MAAO5T,GACP,OAAO8O,GAAmD9O,EAAO,CAC/D+O,4BAA6B7zC,KAAK8xB,KAAKmiB,6BACvCH,UAAW,uBACXC,gBAAiB,MAWhB9wC,eAAe01C,GAEpB9uC,GAEA,MAAMupB,OAAEA,GAAWvpB,EAEnB,OAAO7J,KAAK+sC,YACV3Z,EACA,SACA,CAAEslB,YAAkB,IACpB,IClFJ,IAAYE,GAUAC,GAUAC,GCtBAC,GAQAC,GAQAC,GAgBAC,GDoFLj2C,eAAekwC,GAEpBgG,GAEA,IAWE,aAVkBn5C,KAAK+sC,YACrBoM,EAAI/lB,OACJ,MACA,CACEgmB,UAAW,GACXC,GAAIF,EAAIE,IAEV,IAIF,MAAOvU,GACP,OAAO8O,GAAiD9O,EAAO,CAC7D+O,4BAA6B7zC,KAAK8xB,KAAKmiB,6BACvCH,UAAW,qBACXC,qBAAiBpmB,KAQhB1qB,eAAemwC,GAEpB+F,GAEA,MAAM7wC,EAAS,CACb8wC,UAAW,MACPD,EAAIvL,kBACJ,CAAE0L,qBAAsBH,EAAIvL,mBAC5B,MAEN,IAOE,aANkB5tC,KAAK+sC,YACrBoM,EAAI/lB,OACJ,MACA9qB,EACA,IAGF,MAAOw8B,GACP,OAAO8O,GAAkD9O,EAAO,CAC9D+O,4BAA6B7zC,KAAK8xB,KAAKmiB,6BACvCH,UAAW,sBACXC,gBAAiB,CACfwF,wBAAyB,OAS1Bt2C,eAAeu2C,GAEpBL,GAEA,OAAOn5C,KAAK+sC,YACVoM,EAAI/lB,OACJ,SACA,CAAEgmB,UAAW,GAAIC,GAAIF,EAAIE,IACzB,aAOYI,GAEdN,GAEA,OAAOn5C,KAAK+sC,YACVoM,EAAI/lB,OACJ,MACA,CAAEgmB,UAAW,GAAIC,GAAIF,EAAIO,uBAAuBC,IAChD,GACAR,EAAIO,wBEmBDz2C,eAAe22C,GAAyBtxC,GAC7C,MAAMuxC,UAAEA,KAAcptB,GAAUnkB,EAC1BhJ,EAAOsC,EAA6B6qB,GAY1C,aAXkBzsB,KAAKusC,MACrB,OACA,QACA,GACA,CACEuN,mBAAoBD,GAEtB,IACKv6C,IAYF2D,eAAe82C,GAAwBzxC,GAC5C,MAAMuxC,UAAEA,EAAFG,WAAaA,EAAa,OAASC,GAAW3xC,EAkBpD,aAjBkBtI,KAAKusC,MACrB,MACA,QACA,CACEyN,WAAAA,KACGC,GAEL,CACEH,mBAAoBD,GAEtB,GACA,CACEvnB,UAAW,CACTjqB,iBAAAA,KAcDpF,eAAei3C,GAEpB5xC,GAEA,MAAMuxC,UAAEA,EAAWM,MAAOC,EAApBC,SAA2BA,GAAa/xC,EAe9C,aAdkBtI,KAAKusC,MACrB,gBACS6N,aACT,CACEC,SAAAA,GAEF,CACEP,mBAAoBD,GAEtB,GACA,CACE/O,SAAS,IAaR7nC,eAAeq3C,GAEpBhyC,GAEA,MAAMuxC,UACJA,EACAM,MAAOC,EAFHG,mBAGJA,EAHIC,mBAIJA,GACElyC,EAgBJ,aAfkBtI,KAAKusC,MACrB,gBACS6N,WACT,CACEG,mBAAAA,EACAC,mBAAAA,GAEF,CACEV,mBAAoBD,GAEtB,GACA,CACE/O,SAAS,IAaR7nC,eAAew3C,GAAyBnyC,GAC7C,MAAMuxC,UAAEA,EAAFO,MAAaA,GAAU9xC,EAU7B,aATkBtI,KAAKusC,MACrB,kBACS6N,EACT,GACA,CACEN,mBAAoBD,GAEtB,IAYG52C,eAAey3C,GAA2BpyC,GAC/C,MAAMuxC,UAAEA,EAAFO,MAAaA,GAAU9xC,EAU7B,aATkBtI,KAAKusC,MACrB,eACS6N,EACT,GACA,CACEN,mBAAoBD,GAEtB,ICtVG52C,eAAe03C,GAEpB9wC,GAYA,aAVkB7J,KAAK+sC,YACrBljC,EAAMupB,OACN,MACA,CAAE6jB,QAAS,IACX,GACAptC,EAAMotC,QACN,CACEnM,SAAS,IASf7nC,eAAsBywC,IAEpBtgB,OAAEA,IAEF,IASE,aARkBpzB,KAAK+sC,YACrB3Z,EACA,MACA,CACE6jB,QAAS,IAEX,IAGF,MAAOnS,GACP,OAAO8O,GAA+C9O,EAAO,CAC3D+O,4BAA6B7zC,KAAK8xB,KAAKmiB,6BACvCH,UAAW,mBACXC,gBAAiB,CACfmD,OAAQ,CACN0D,KAAM,QAchB33C,eAAsB43C,IAEpBznB,OAAEA,IAEF,OAAOpzB,KAAK+sC,YAAY3Z,EAAQ,SAAU,CAAE6jB,QAAS,IAAM,IClEtDh0C,eAAe63C,GAEpBjxC,GASA,aAPkB7J,KAAK+sC,YACrBljC,EAAMupB,OACN,MACA,CAAE2nB,aAAc,IAChB,GACAlxC,EAAMkxC,cAWV93C,eAAsB+3C,IAEpB5nB,OAAEA,IAUF,aARkBpzB,KAAK+sC,YACrB3Z,EACA,MACA,CACE2nB,aAAc,IAEhB,ICjBG93C,eAAeg4C,GAEpB9B,GAEA,MAAM/lB,OAAEA,GAAW+lB,EACnB,IASE,aARkBn5C,KAAK+sC,YACrB3Z,EACA,MACA,CACE8nB,oBAAqB,IAEvB,IAGF,MAAO93C,GACP,GAAIA,aAAelE,GACM,MAAnBkE,EAAI1D,WACN,OAAOM,KAAKyI,uBACV,CACE0yC,WAAY/nB,EACZgoB,oBAAqB,IAEvBh4C,GAKN,MAAMA,GAOHH,eAAeo4C,GAEpBjoB,GAEA,IASE,aARkBpzB,KAAK+sC,YACrB3Z,EACA,MACA,CACEkoB,WAAY,IAEd,IAGF,MAAOl4C,GACP,GAAIA,aAAelE,GACM,MAAnBkE,EAAI1D,WACN,OAAOM,KAAKyI,uBACV,CACE8yC,YAAa,IAEfn4C,GAKN,MAAMA,GAYHH,eAAeu4C,GAEpBrC,GAEA,IACE,MAAM/lB,OAAEA,EAAFqoB,UAAUA,GAActC,EAU9B,aATkBn5C,KAAK+sC,YACrB3Z,EACA,MACA,CACEsoB,kBAAmB,GACnBD,UAAAA,GAEF,IAGF,MAAOr4C,GACP,GAAIA,aAAelE,GACM,MAAnBkE,EAAI1D,WACN,OAAOM,KAAKyI,uBACV,CACE8yC,YAAa,IAEfn4C,GAKN,MAAMA,GAOHH,eAAe04C,GAEpBvoB,EACAqoB,GAEA,IAUE,aATkBz7C,KAAK+sC,YACrB3Z,EACA,MACA,CACEkoB,WAAY,GACZG,UAAAA,GAEF,IAGF,MAAOr4C,GACP,GAAIA,aAAelE,GACM,MAAnBkE,EAAI1D,WACN,OAAOM,KAAKyI,uBAAuB,KAAMrF,GAI7C,MAAMA,GAcHH,eAAe24C,GAEpBzC,GAEA,MAAM/lB,OAAEA,EAAFqoB,UAAUA,EAAV/c,QAAqBA,EAArBmd,kBAA8BA,GAAsB1C,EAC1D,IAYE,aAXkBn5C,KAAK+sC,YACrB3Z,EACA,MACA,CACEkoB,WAAY,GACZG,UAAAA,EACAI,kBAAAA,GAEF,GACA,CAAEC,QAASpd,IAGb,MAAOt7B,GACP,GAAIA,aAAelE,GACM,MAAnBkE,EAAI1D,WACN,OAAOM,KAAKyI,uBAAuB,KAAMrF,GAI7C,MAAMA,GAaHH,eAAe84C,GAEpB5C,GAEA,MAAMsC,UAAEA,EAAFI,kBAAaA,EAAbzoB,OAAgCA,GAAW+lB,EACjD,IAWE,aAVkBn5C,KAAK+sC,YACrB3Z,EACA,SACA,CACEkoB,WAAY,GACZG,UAAAA,EACAI,kBAAAA,GAEF,IAGF,MAAOz4C,GACP,GAAIA,aAAelE,GACM,MAAnBkE,EAAI1D,WACN,OAAOM,KAAKyI,uBAAuB,KAAMrF,GAG7C,MAAMA,GAgBHH,eAAe+4C,GAEpB5oB,EACA9zB,GAEA,IAUE,aATkBU,KAAK+sC,YACrB3Z,EACA,MACA,CACE6oB,qBAAsB,IAExB,GACA38C,GAGF,MAAO8D,GACP,GAAIA,aAAelE,GACM,MAAnBkE,EAAI1D,WACN,OAAOM,KAAKyI,uBAAuB,KAAMrF,GAG7C,MAAMA,GAOHH,eAAei5C,GAEpB9oB,GAEA,IASE,aARkBpzB,KAAK+sC,YACrB3Z,EACA,MACA,CACE6oB,qBAAsB,IAExB,IAGF,MAAO74C,GACP,GAAIA,aAAelE,GACM,MAAnBkE,EAAI1D,WACN,OAAOM,KAAKyI,uBAAuB,KAAMrF,GAG7C,MAAMA,GAkBHH,eAAek5C,GAEpBhD,GAEA,MAAM/lB,OAAEA,EAAFgpB,UAAUA,EAAVC,gBAAqBA,GAAoBlD,EAC/C,IAUE,aATkBn5C,KAAK+sC,YACrB3Z,EACA,MACA,CACEkpB,oBAAqB,IAEvB,GACA,CAAEF,UAAAA,EAAWC,gBAAAA,IAGf,MAAOj5C,GACP,GAAIA,aAAelE,GACM,MAAnBkE,EAAI1D,WACN,OAAOM,KAAKyI,uBAAuB,KAAMrF,GAG7C,MAAMA,GAOHH,eAAes5C,GAEpBnpB,GAEA,IASE,aARkBpzB,KAAK+sC,YACrB3Z,EACA,MACA,CACEkpB,oBAAqB,IAEvB,IAGF,MAAOl5C,GACP,GAAIA,aAAelE,GACM,MAAnBkE,EAAI1D,WACN,OAAOM,KAAKyI,uBAAuB,KAAMrF,GAG7C,MAAMA,GCvWHH,eAAeiwC,GAEpB9f,GAEA,IAOE,aANkBpzB,KAAK+sC,YACrB3Z,EACA,MACA,CAAEopB,mBAAoB,IACtB,IAGF,MAAO1X,GACP,OAAO8O,GAAuD9O,EAAO,CACnE+O,4BAA6B7zC,KAAK8xB,KAAKmiB,6BACvCH,UAAW,8BACXC,gBAAiB,MCfhB9wC,eAAew5C,GAEpB5yC,GAEA,MAAMupB,OAAEA,KAAWwkB,GAAe/tC,EAE5ByjB,EAAO1rB,EAA6Bg2C,GAC1C,OAAO53C,KAAK+sC,YACV3Z,EACA,MACA,CAAEsd,OAAkB,IACpB,GACA,IACKpjB,IAaFrqB,eAAey5C,GAEpB7yC,GAEA,MAAMupB,OAAEA,GAAWvpB,EACnB,aAAa7J,KAAK+sC,YAChB3Z,EACA,MACA,CAAEsd,OAAkB,IACpB,IAUGztC,eAAe05C,GAEpB9yC,GAEA,MAAMupB,OAAEA,GAAWvpB,EAEnB,OAAO7J,KAAK+sC,YACV3Z,EACA,SACA,CAAEsd,OAAkB,IACpB,ICrDGztC,eAAe25C,GAA6B/yC,GACjD,MAAMzC,UAAeA,KAAcwwC,GAAe/tC,EAC5C5H,EAA6B,CAAE46C,QAAS,IAC1Cz1C,IACFnF,EAAMmF,UAAYA,GAEpB,MAAMkmB,EAAO1rB,EAA6Bg2C,GAE1C,OAAO53C,KAAKgK,aAAwBH,EAAO,OAAQ5H,EAAO,GAAIqrB,GC4CzDrqB,eAAe65C,GAA+Bx0C,GACnD,MAAMuxC,UAAEA,GAAcvxC,EAetB,aAdkBtI,KAAKusC,MACrB,MACA,eACA,GACA,CACEuN,mBAAoBD,GAEtB,GACA,CACEvnB,UAAW,CACTjqB,iBAAAA,KAaDpF,eAAe85C,GAEpBz0C,GAEA,MAAMuxC,UAAEA,EAAFF,GAAaA,GAAOrxC,EAe1B,aAdkBtI,KAAKusC,MACrB,wBAEA,CACE8M,GAAIM,GAEN,CACEG,mBAAoBD,GAEtB,GACA,CACE/O,SAAS,IAYR7nC,eAAe+5C,GAA8B10C,GAClD,MAAMuxC,UAAEA,EAAFF,GAAaA,GAAOrxC,EAe1B,aAdkBtI,KAAKusC,MACrB,qBAEA,CACE8M,GAAIM,GAEN,CACEG,mBAAoBD,GAEtB,GACA,CACE/O,SAAS,IAWR7nC,eAAeg6C,GAEpB30C,GAEA,MAAMuxC,UAAEA,EAAFF,GAAaA,KAAOuD,GAAS50C,EAmBnC,aAjBkBtI,KAAKusC,MACrB,qBAEA,CACE8M,GAAIM,GAEN,CACEG,mBAAoBD,GAEtB,IACKqD,EACHvD,GAAAA,GAEF,CACE7O,SAAS,ICzHR7nC,eAAek6C,GAEpBtzC,GAEA,MAAMupB,OAAEA,KAAWwkB,GAAe/tC,EAE5ByjB,EAAO1rB,EAA6Bg2C,GAC1C,OAAO53C,KAAK+sC,YACV3Z,EACA,MACA,CAAEgqB,gBAAkB,IACpB,GACA,IACK9vB,IAcFrqB,eAAeo6C,GAEpBxzC,GAEA,MAAMupB,OAAEA,GAAWvpB,EACnB,IACE,aAAa7J,KAAK+sC,YAChB3Z,EACA,MACA,CAAEgqB,gBAAkB,IACpB,IAEF,MAAOtY,GACP,OAAO8O,GAAoD9O,EAAO,CAChE+O,4BAA6B7zC,KAAK8xB,KAAKmiB,6BACvCH,UAAW,6BACXC,gBAAiB,CACfmC,MAAO,OCvDRjzC,eAAeq6C,GAA0BzzC,GAC9C,OAAO0zC,GAAYzqB,KAAK9yB,KAAM6J,GAGzB5G,eAAes6C,GAEpB1zC,GAEA,MAAMlK,EAAWkK,EAAMlK,QAAU6C,EAAoBqH,EAAMlK,SAS3D,OARAmI,EAAmB+B,EAAO,CACxB,mBACA,sBACA,kBACA,MACA,eACA,SAEK7J,KAAKgK,aACVH,EACA,MACA,CAAE2zC,QAAS,IACX79C,OACAguB,EACA,CACEsE,eAAe5yB,GACb,MAAMM,QAAEA,GAAYN,EACpB,MAAO,CACL6zB,UAAWvzB,EAAQ,wBCxCtBsD,eAAew6C,GAA0B5zC,GAC9C,OAAO6zC,GAAY5qB,KAAK9yB,KAAM6J,GAGzB5G,eAAey6C,GAEpB7zC,GAEA,MAAM5H,EAA6B,CAAEu7C,QAAS,IAI9C,OAHI3zC,EAAMzC,YACRnF,EAAMmF,UAAYyC,EAAMzC,WAEnBpH,KAAKgK,aAA2BH,EAAO,MAAO5H,EAAO,QAAI0rB,EAAW,CACzEsE,eAAiBD,IACf,MAAMryB,QAAEA,GAAYqyB,EACpB,MAAO,CACLkB,UAAWvzB,EAAQ,oBACnBg+C,iBAAkBh+C,EAAQ,wBAC1Bi+C,oBAAqBj+C,EAAQ,wBAC7Bk+C,aAAcl+C,EAAQ,qBCjBvBsD,eAAe66C,GAEpBj0C,GAEA,MAAMupB,OAAEA,KAAWwkB,GAAe/tC,EAE5ByjB,EAAO1rB,EAA6Bg2C,GAC1C,OAAO53C,KAAK+sC,YACV3Z,EACA,MACA,CAAE2qB,qBAAkB,IACpB,GACA,IACKzwB,IAoBFrqB,eAAe+6C,GAEpBn0C,GAEA,IACE,MAAMupB,OAAEA,GAAWvpB,EACblK,EAAmB,GAUzB,OATIkK,EAAMo0C,YACRt+C,EAAQ,wCAA0C,cAElCK,KAAK+sC,YACrB3Z,EACA,MACA,CAAE2qB,qBAAkB,IACpBp+C,GAGF,MAAOmlC,GACP,OAAO8O,GAA4D9O,EAAO,CACxE+O,4BAA6B7zC,KAAK8xB,KAAKmiB,6BACvCH,UAAW,gCACXC,gBAAiB,CACfmK,kCAAmC,CACjCC,QAAS,QACTzJ,OAAQzZ,uCAA+BmjB,gBC/D1Cn7C,eAAeo7C,GAEpBx0C,GAEA,MAAMupB,OAAEA,EAAFjzB,OAAUA,GAAW0J,EAE3B,OAAO7J,KAAK+sC,YACV3Z,EACA,MACA,CAAEkrB,cAAe,IACjB,GACA,CACE5J,OAAQv0C,IAgBP8C,eAAes7C,GAEpB10C,GAEA,IACE,MAAMupB,OAAEA,GAAWvpB,EAEnB,aAAa7J,KAAK+sC,YAChB3Z,EACA,MACA,CAAEkrB,cAAe,IACjB,IAEF,MAAOxZ,GACP,OAAO8O,GAAqD9O,EAAO,CACjE+O,4BAA6B7zC,KAAK8xB,KAAKmiB,6BACvCH,UAAW,yBACXC,gBAAiB,MbkDhB9wC,eAAeu7C,GAA4Bl2C,GAChD,MAAMuxC,UAAEA,GAAcvxC,EAYtB,aAXkBtI,KAAKusC,MACrB,MACA,aACA,GACA,CACEuN,mBAAoBD,GAEtB,GACA,IAWG52C,eAAew7C,GAA4Bn2C,GAChD,MAAMuxC,UAAEA,KAAc6E,GAAep2C,EAcrC,aAbkBtI,KAAKusC,MACrB,MACA,aACA,GACA,CACEuN,mBAAoBD,GAEtB,IACK6E,GAEL,IAWGz7C,eAAe07C,GAEpBr2C,GAEA,MAAMuxC,UAAEA,GAAcvxC,EAYtB,aAXkBtI,KAAKusC,MACrB,SACA,aACA,GACA,CACEuN,mBAAoBD,GAEtB,GACA,IczIG52C,eAAe27C,GAEpB/0C,GAEA,MAAMgwC,UAAEA,EAAFlJ,KAAaA,EAAbkO,QAAmBA,GAAYh1C,EAkBrC,aAhBkB7J,KAAKusC,MACrB,OACA,QACA,CACEoE,KAAAA,GAEF,CACEmJ,mBAAoBD,GAEtB,CACEiF,KAAMnO,EACNoO,QAASF,GAEX,IAgBG57C,eAAe+7C,GAEpBn1C,GAEA,MAAM8mC,KAAEA,EAAFkJ,UAAQA,GAAchwC,EAc5B,aAbkB7J,KAAKusC,MACrB,MACA,QACA,CACEoE,KAAAA,GAEF,CACEmJ,mBAAoBD,GAEtB,GACA,IAoBG52C,eAAeg8C,GAEpBp1C,GAEA,MAAMgwC,UAAEA,KAAcjvC,GAAcf,EAYpC,aAXkB7J,KAAKusC,MACrB,MACA,QACA,IAAK3hC,GACL,CACEkvC,mBAAoBD,GAEtB,GACA,IAyBG52C,eAAei8C,GAEpBr1C,GAEA,MAAMgwC,UAAEA,EAAFsF,MAAaA,GAAUt1C,EAW7B,aAVkB7J,KAAKusC,MACrB,MACA,eACA,CACE4S,MAAAA,GAEF,CACErF,mBAAoBD,IAanB52C,eAAem8C,GAEpBv1C,GAEA,MAAM8mC,KAAEA,EAAFkJ,UAAQA,GAAchwC,EAW5B,aAVkB7J,KAAKusC,MACrB,SACA,QACA,CACEoE,KAAAA,GAEF,CACEmJ,mBAAoBD,IAcnB52C,eAAeo8C,GAEpBx1C,GAEA,MAAMy1C,OAAEA,EAAFzF,UAAUA,EAAVsF,MAAqBA,GAAUt1C,EAcrC,aAbkB7J,KAAKusC,MACrB,QACA,eACA,CACE4S,MAAAA,GAEF,CACErF,mBAAoBD,GAEtB,CACE0F,OAAQD,KfrMd,SAAY1G,GAEVA,gBAEAA,kBAJF,CAAYA,KAAAA,QAUZ,SAAYC,GAEVA,YAEAA,oBAJF,CAAYA,KAAAA,QAUZ,SAAYC,GAEVA,cAEAA,qCAEAA,cAEAA,8BAEAA,4CAEAA,sCACAA,gBAEAA,wCAfF,CAAYA,KAAAA,QCtBZ,SAAYC,GACVA,8BACAA,oCACAA,kDACAA,wDACAA,0BACAA,gCANF,CAAYA,KAAAA,QAQZ,SAAYC,GACVA,0BACAA,gCACAA,8BACAA,0CACAA,oCACAA,gDANF,CAAYA,KAAAA,QAQZ,SAAYC,GACVA,wBACAA,8BAFF,CAAYA,KAAAA,QAgBZ,SAAYC,GAEVA,wBAEAA,sBAEAA,oBAEAA,0BAEAA,wBAVF,CAAYA,KAAAA,QeRL,MAAMsG,GAAsCv8C,eAEjD4G,GAEA,MAAMgwC,UAAEA,EAAFsF,MAAaA,EAAbpJ,MAAoBA,GAAUlsC,EAEpC,OAAI7J,KAAK8xB,KAAKmiB,+BAAiC8B,EAAMhuC,OAC5C03C,GAAuC3sB,KAAK9yB,KAAM,CACvD65C,UAAAA,EACAsF,MAAAA,UAIcn/C,KAAKusC,MACrB,MACA,eACA,CACE4S,MAAAA,GAEF,CACErF,mBAAoBD,GAEtB,CACE3D,MAAOH,GAET,CACE9jB,eAAc,KACL,OAmBFytB,GAAsCz8C,eAEjD4G,GAEA,MAAMgwC,UAAEA,EAAFsF,MAAaA,GAAUt1C,EAC7B,IACE,MAAMmoB,QAAYhyB,KAAKusC,MACrB,MACA,eACA,CACE4S,MAAAA,GAEF,CACErF,mBAAoBD,GAEtB,GACA,IAIF,OAFkBt5C,EAAcyxB,EAAI1yB,KACpCwL,CAAU,SACHknB,EACP,MAAO8S,GACP,OAAO8O,GACL9O,EACA,CACE+O,4BAA6B7zC,KAAK8xB,KAAKmiB,6BACvCH,UAAW,sCACXC,gBAAiB,CACfmC,MAAO,QAgBJuJ,GAAyCx8C,eAEpD4G,GAEA,MAAMgwC,UAAEA,EAAFsF,MAAaA,GAAUt1C,EAiB7B,aAhBkB7J,KAAKusC,MACrB,SACA,eACA,CACE4S,MAAAA,GAEF,CACErF,mBAAoBD,GAEtB,GACA,CACE5nB,eAAc,KACL,OC1HRhvB,eAAe08C,GAEpB91C,GAEA,MAAMupB,OAAEA,EAAFwsB,OAAUA,GAAW/1C,EAC3B,aAAa7J,KAAK+sC,YAChB3Z,EACA,MACA,CACEysB,YAAa,IAEf,GACA,CACEC,OAAQF,IAgBP38C,eAAe88C,GAEpBl2C,GAEA,MAAMupB,OAAEA,GAAWvpB,EACnB,IACE,aAAa7J,KAAK+sC,YAChB3Z,EACA,MACA,CACEysB,YAAa,IAEf,IAEF,MAAO/a,GACP,OAAO8O,GAAuB9O,EAAO,CACnC+O,4BAA6B7zC,KAAK8xB,KAAKmiB,6BACvCH,UAAW,uBACXC,gBAAiB,CACf+L,QAAQ,MC7CT78C,eAAe+8C,GAEpBn2C,GAEA,MAAMupB,OAAEA,KAAWwkB,GAAe/tC,EAE5ByjB,EAAO1rB,EAA6Bg2C,GAC1C,OAAO53C,KAAK+sC,YACV3Z,EACA,MACA,CAAE6sB,MAAkB,IACpB,GACA,IACK3yB,IAiBFrqB,eAAei9C,GAEpBr2C,GAEA,MAAMupB,OAAEA,GAAWvpB,EACnB,aAAa7J,KAAK+sC,YAChB3Z,EACA,MACA,CAAE6sB,MAAkB,IACpB,UCuISE,WAAoB5Z,sCAE/BkI,aAAeA,QACfE,WAAaA,QACbD,aAAeA,QACfF,YAAcA,QACdgH,kBAAoBA,QACpB1G,sBAAwBA,QAGxBI,aAAeA,QACfF,aAAeA,QAGfuE,gBAAkBA,QAClBS,gBAAkBA,QAClBG,mBAAqBA,QAGrBI,oBAAsBA,QACtBE,oBAAsBA,QAGtBgB,cAAgBA,QAChBG,cAAgBA,QAChBC,iBAAmBA,QAGnBC,mBAAqBA,QACrBK,mBAAqBA,QACrBH,sBAAwBA,QAGxBK,oBAAsBA,QACtBK,oBAAsBA,QACtBC,uBAAyBA,QAGzBC,oBAAsBA,QACtBvD,oBAAsBA,QACtBwD,uBAAyBA,QAGzBQ,qBAAuBA,QACvB5D,qBAAuBA,QACvB8D,wBAA0BA,QAG1BI,iBAAmBA,QACnBhE,iBAAmBA,QACnBoE,oBAAsBA,QAGtBC,sBAAwBA,QACxB1E,sBAAwBA,QAGxB8E,sBAAwBA,QACxBnF,sBAAwBA,QACxBsF,yBAA2BA,QAG3BE,qBAAuBA,QACvBjF,qBAAuBA,QACvBmF,wBAA0BA,QAG1BxF,mBAAqBA,QACrBC,oBAAsBA,QACtBqG,mBAAqBA,QACrBD,sBAAwBA,QAGxBmB,iBAAmBA,QACnBjH,iBAAmBA,QACnBmH,oBAAsBA,QAGtBC,sBAAwBA,QACxBE,sBAAwBA,QAGxBW,oBAAsBA,QACtBN,wBAA0BA,QAC1BG,8BAAgCA,QAChCP,uBAAyBA,QACzBc,uBAAyBA,QACzBH,oBAAsBA,QACtBO,6BAA+BA,QAC/BH,sBAAwBA,QACxBE,sBAAwBA,QACxBK,6BAA+BA,QAG/BE,gBAAkBA,QAClBC,gBAAkBA,QAClBC,mBAAqBA,QAGrBmB,8BAAgCA,QAChCE,8BAAgCA,QAGhC/hB,WAAaA,QACbC,oBAAsBA,QACtBoU,aAAeA,QACfM,mBAAqBA,QACrBjT,UAAYA,QACZG,YAAcA,QACdc,gBAAkBA,QAClBuS,aAAeA,QACf/V,WAAaA,QACbmW,aAAeA,QACfzE,YAAcA,QACd2D,aAAeA,QACfnB,YAAcA,QACdK,aAAeA,QAEfzC,iBAAmBA,QAEnBF,mBAAqBA,QACrBmC,UAAYA,QACZE,kBAAoBA,QACpB+B,aAAeA,QACfO,cAAgBA,QAGhB/nC,sBAAwBA,OACxBipB,WAAaA,QACbE,mBAAqBA,QACrBC,wBAA0BA,QAC1Bqe,qBAAuBA,QACvB5V,eAAiBA,QACjB6V,qBAAuBA,QACvB5mC,UAAYA,OACZo0B,aAAeA,QAGfiY,iBAAmBA,QACnBI,iBAAmBA,QACnBC,oBAAsBA,QAGtB2C,SAAWA,QACXH,UAAYA,QACZa,UAAYA,QACZC,YAAcA,QACdJ,gBAAkBA,QAClBJ,kBAAoBA,QAGpB0C,cAAgBA,QAEhB1oB,WAAaA,QACb2b,gBAAkBA,QAIlBgC,uBAAyBA,QACzBuO,uBAAyBvO,QACzB8C,mBAAqBA,QAErBzB,4BAA8BA,QAG9B4J,gBAAkBA,QAClBC,kBAAoBA,QACpBC,eAAiBA,QACjBC,eAAiBA,QAGjBE,2BAA6BA,QAC7BE,2BAA6BA,QAG7BC,WAAaA,QACbG,WAAaA,QAGbY,uBAAyBA,QACzBE,uBAAyBA,QAGzBE,aAAeA,QACfD,aAAeA,QACfG,gBAAkBA,QAGlBC,6BAA+BA,QAC/BI,0BAA4BA,QAC5BC,4BAA8BA,QAC9BC,gCAAkCA,QAClCE,6BAA+BA,QAC/BC,mCAAqCA,QAGrCG,oCAAsCA,QACtCE,oCAAsCA,QACtCD,uCACEA,QAGFE,qBAAuBA,QACvBI,qBAAuBA,QAEvBC,eAAiBA,QACjBE,eAAiBA,UC3WbG,GAAc3Y,EAAM2Y,YAE1B,MAAMC,WAAkBH,IAAlBG,GAEGphD,eAAiBA,EAFpBohD,GAGGlsB,SAAWA,EAHdksB,GAIGhgD,YAAcA,EAJjBggD,GAKGrhD,cAAgBA,sBALnBqhD,GAMGlgD,eAAiBA,EANpBkgD,GAOGD,YAAcA,GAPjBC,GAQGrmB,QAAUA,gBARbqmB,GASGpmB,iBAAmBA,yBATtBomB,GAUGnmB,sBAAwBA,8BAV3BmmB,GAWGlmB,iBAAmBA,yBAXtBkmB,GAYGjmB,eAAiBA,uBAZpBimB,GAaGhmB,YAAcA,oBAbjBgmB,GAcG/lB,WAAaA,mBAdhB+lB,GAeG9lB,eAAiBA,uBAfpB8lB,GAgBG3lB,oBAAsBA,4BAhBzB2lB,GAiBGzlB,WAAaA,mBAjBhBylB,GAkBG1lB,aAAeA,qBAlBlB0lB,GAmBG7lB,iCAAmCA,yCAnBtC6lB,GAoBGxlB,SAAWA,iBApBdwlB,GAqBGvlB,qBAAuBA,6BArB1BulB,GAsBGpzB,yBAA2BA,EAtB9BozB,GAuBGjxB,iBAAmBA,yBAvBtBixB,GAwBGtsB,gBAAkBA,wBAxBrBssB,GAyBGzhB,kBAAoBA,0BAzBvByhB,GA0BGnlB,uBAAyBA,+BA1B5BmlB,GA2BG5lB,sBAAwBA,8BA3B3B4lB,GA6BGtlB,sBAAwBA,8BA7B3BslB,GA+BGrlB,+BAAiCA,uCA/BpCqlB,GAiCGplB,iCAAmCA,yCAjCtColB,GAmCGxS,gBAAkBA"}