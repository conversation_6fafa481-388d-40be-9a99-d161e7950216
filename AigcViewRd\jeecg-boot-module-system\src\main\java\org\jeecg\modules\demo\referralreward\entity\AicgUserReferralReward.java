package org.jeecg.modules.demo.referralreward.entity;

import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 推荐奖励记录表
 * @Author: jeecg-boot
 * @Date:   2025-06-18
 * @Version: V1.0
 */
@Data
@TableName("aicg_user_referral_reward")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="aicg_user_referral_reward对象", description="推荐奖励记录表")
public class AicgUserReferralReward implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String id;
    
	/**推荐关系ID*/
	@Excel(name = "推荐关系ID", width = 15)
    @ApiModelProperty(value = "推荐关系ID，关联aicg_user_referral.id")
    private String referralId;
    
	/**推荐人ID*/
	@Excel(name = "推荐人ID", width = 15)
    @ApiModelProperty(value = "推荐人ID，关联sys_user.id")
    private String referrerId;
    
	/**被推荐人ID*/
	@Excel(name = "被推荐人ID", width = 15)
    @ApiModelProperty(value = "被推荐人ID，关联sys_user.id")
    private String refereeId;
    
	/**奖励类型*/
	@Excel(name = "奖励类型", width = 15)
    @ApiModelProperty(value = "奖励类型：1=注册奖励,2=首充奖励,3=升级奖励")
    private Integer rewardType;
    
	/**奖励金额*/
	@Excel(name = "奖励金额", width = 15)
    @ApiModelProperty(value = "奖励金额")
    private BigDecimal rewardAmount;
    
	/**关联交易记录ID*/
	@Excel(name = "关联交易记录ID", width = 15)
    @ApiModelProperty(value = "关联交易记录ID，关联aicg_user_transaction.id")
    private String transactionId;
    
	/**触发事件描述*/
	@Excel(name = "触发事件描述", width = 30)
    @ApiModelProperty(value = "触发事件描述")
    private String triggerEvent;
    
	/**状态*/
	@Excel(name = "状态", width = 15)
    @ApiModelProperty(value = "状态：1=待发放,2=已发放,3=已取消")
    private Integer status;
    
	/**奖励发放时间*/
	@Excel(name = "奖励发放时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "奖励发放时间")
    private Date rewardTime;
    
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;
    
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private Date createTime;
    
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private String updateBy;
    
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private Date updateTime;
    
	/**所属部门*/
    @ApiModelProperty(value = "所属部门")
    private String sysOrgCode;
}
