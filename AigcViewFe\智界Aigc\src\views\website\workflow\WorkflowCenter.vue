<template>
  <WebsitePage>
    <div class="workflow-center">
      <!-- 简洁页面标题 -->
      <div class="simple-header">
        <h1 class="simple-title">AI工作流中心</h1>
        <p class="simple-subtitle">发现和使用优质AI智能体，提升您的创作效率，让每个想法都能完美实现</p>
      </div>

    <!-- Tab导航 -->
    <div class="workflow-tabs">
      <div class="tabs-container">
        <div class="tab-nav">
          <div
            class="tab-item"
            :class="{ active: activeTab === 'market' }"
            @click="switchTab('market')"
          >
            <a-icon type="shop" />
            <span>智能体市场</span>
          </div>
          <div
            class="tab-item"
            :class="{ active: activeTab === 'creator' }"
            @click="switchTab('creator')"
          >
            <a-icon type="build" />
            <span>创作者中心</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Tab内容 -->
    <div class="workflow-content">
      <div class="content-container">
        <!-- 智能体市场 -->
        <div v-show="activeTab === 'market'" class="tab-content">
          <AgentMarket />
        </div>

        <!-- 创作者中心 -->
        <div v-show="activeTab === 'creator'" class="tab-content">
          <CreatorCenter ref="creatorCenter" />
        </div>
      </div>
    </div>
    </div>
  </WebsitePage>
</template>

<script>
import WebsitePage from '@/components/website/WebsitePage.vue'
import AgentMarket from './components/AgentMarket.vue'
import CreatorCenter from './components/CreatorCenter.vue'

export default {
  name: 'WorkflowCenter',
  components: {
    WebsitePage,
    AgentMarket,
    CreatorCenter
  },
  data() {
    return {
      activeTab: 'market'
    }
  },
  mounted() {
    // 根据URL参数设置默认tab
    const tab = this.$route.query.tab
    if (tab === 'creator') {
      this.activeTab = 'creator'
      document.title = '创作者中心 - 智界AIGC'
    } else {
      this.activeTab = 'market'
      document.title = 'AI工作流中心 - 智界AIGC'
    }
  },
  watch: {
    // 监听路由变化
    '$route.query.tab'(newTab) {
      if (newTab === 'creator') {
        this.activeTab = 'creator'
        document.title = '创作者中心 - 智界AIGC'
        // 路由变化切换到创作者中心时也刷新数据
        this.$nextTick(() => {
          this.refreshCreatorCenter()
        })
      } else {
        this.activeTab = 'market'
        document.title = 'AI工作流中心 - 智界AIGC'
      }
    }
  },
  methods: {
    switchTab(tab) {
      this.activeTab = tab

      // 更新URL参数
      this.$router.push({
        path: '/workflow-center',
        query: { tab }
      }).catch(() => {}) // 忽略重复导航错误

      // 更新页面标题
      if (tab === 'creator') {
        document.title = '创作者中心 - 智界AIGC'
        // 切换到创作者中心时，通知子组件刷新数据
        this.$nextTick(() => {
          this.refreshCreatorCenter()
        })
      } else {
        document.title = 'AI工作流中心 - 智界AIGC'
      }
    },

    // 刷新创作者中心数据
    refreshCreatorCenter() {
      // 通过ref调用CreatorCenter组件的刷新方法
      if (this.$refs.creatorCenter) {
        this.$refs.creatorCenter.refreshAllData()
      }
    }
  }
}
</script>

<style scoped>
.workflow-center {
  min-height: 100vh;
  padding: 2rem 0;
}

/* 简洁页面标题 */
.simple-header {
  text-align: center;
  padding: 2rem 0 3rem; /* 恢复原来的padding */
  max-width: 1200px;
  margin: 0 auto;
}

.simple-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0 0 0.5rem 0;
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.simple-subtitle {
  font-size: 1.1rem;
  color: #64748b;
  margin: 0;
}

.workflow-tabs {
  background: white;
  border-bottom: 1px solid #e2e8f0;
  position: sticky;
  top: 101px; /* 顶部导航栏实际高度是100px */
  z-index: 200; /* 提高层级，确保在搜索筛选区域之上 */
}

.tabs-container {
  max-width: 1600px;
  margin: 0 auto;
  padding: 0 2rem;
}

.tab-nav {
  display: flex;
  gap: 0;
  border-bottom: 1px solid #e2e8f0;
}

.tab-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem 2rem;
  cursor: pointer;
  border-bottom: 3px solid transparent;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-weight: 600;
  font-size: 1rem;
  color: #64748b;
  background: transparent;
  position: relative;
  border-radius: 8px 8px 0 0;
}

.tab-item:hover {
  color: #3b82f6;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.tab-item.active {
  color: #ffffff;
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  border-bottom-color: transparent;
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: -3px;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  border-radius: 2px;
}

.tab-item .anticon {
  font-size: 1.2rem;
  transition: transform 0.3s ease;
}

.tab-item.active .anticon {
  transform: scale(1.1);
}

.workflow-content {
  flex: 1;
}

.content-container {
  max-width: 1600px;
  margin: 0 auto;
  padding: 2rem;
}

.tab-content {
  min-height: 600px;
}

@media (max-width: 768px) {
  .simple-title {
    font-size: 2rem;
  }

  .simple-subtitle {
    font-size: 1rem;
  }

  .tabs-container {
    padding: 0 1rem;
  }

  .tab-item {
    padding: 0.75rem 1rem;
    font-size: 0.875rem;
  }

  .content-container {
    padding: 1rem;
  }
}

@media (max-width: 480px) {
  .simple-title {
    font-size: 1.8rem;
  }

  .simple-subtitle {
    font-size: 1rem;
  }

  .tab-nav {
    justify-content: center;
  }

  .tab-item {
    flex: 1;
    justify-content: center;
  }
}
</style>