import TOSBase, { TosResponse } from '../base';
export interface GetSymInput {
    bucket?: string;
    key: string;
    versionId?: string;
}
export interface PutSymOutput {
    VersionID?: string;
    SymlinkTargetKey: string;
    SymlinkTargetBucket: string;
    LastModified: string;
}
/**
 * @private unstable method
 */
export declare function getSymlink(this: TOSBase, input: GetSymInput): Promise<TosResponse<PutSymOutput>>;
export declare function _getSymlink(this: TOSBase, input: GetSymInput): Promise<TosResponse<PutSymOutput>>;
export default getSymlink;
