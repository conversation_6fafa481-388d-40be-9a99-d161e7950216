package org.jeecg.modules.jianyingpro.dto.response;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 超级剪映小助手基础响应类
 * 
 * <AUTHOR>
 * @since 2025-01-01
 */
@Data
public class BaseJianyingProResponse {
    
    @ApiModelProperty(value = "操作是否成功")
    @JsonProperty("success")
    private Boolean success;
    
    @ApiModelProperty(value = "操作名称")
    @JsonProperty("operation")
    private String operation;
    
    @ApiModelProperty(value = "响应时间戳")
    @JsonProperty("timestamp")
    private Long timestamp;
    
    @ApiModelProperty(value = "请求ID")
    @JsonProperty("request_id")
    private String requestId;
    
    @ApiModelProperty(value = "错误码（失败时）")
    @JsonProperty("error_code")
    private String errorCode;
    
    @ApiModelProperty(value = "错误消息（失败时）")
    @JsonProperty("error_message")
    private String errorMessage;
    
    @ApiModelProperty(value = "错误详情（失败时）")
    @JsonProperty("error_details")
    private String errorDetails;
    
    @ApiModelProperty(value = "HTTP状态码")
    @JsonProperty("http_status")
    private Integer httpStatus;
    
    /**
     * 创建成功响应
     */
    public static BaseJianyingProResponse success(String operation) {
        BaseJianyingProResponse response = new BaseJianyingProResponse();
        response.setSuccess(true);
        response.setOperation(operation);
        response.setTimestamp(System.currentTimeMillis());
        response.setRequestId(generateRequestId());
        response.setHttpStatus(200);
        return response;
    }
    
    /**
     * 创建错误响应
     */
    public static BaseJianyingProResponse error(String operation, String errorCode, 
                                               String errorMessage, String errorDetails) {
        BaseJianyingProResponse response = new BaseJianyingProResponse();
        response.setSuccess(false);
        response.setOperation(operation);
        response.setErrorCode(errorCode);
        response.setErrorMessage(errorMessage);
        response.setErrorDetails(errorDetails);
        response.setTimestamp(System.currentTimeMillis());
        response.setRequestId(generateRequestId());
        
        // 根据错误码设置HTTP状态码
        if (errorCode.startsWith("PARAM_")) {
            response.setHttpStatus(400);
        } else if (errorCode.startsWith("BUSINESS_")) {
            response.setHttpStatus(422);
        } else {
            response.setHttpStatus(500);
        }
        
        return response;
    }
    
    /**
     * 从JSONObject转换
     */
    public static BaseJianyingProResponse fromJson(JSONObject json, String operation) {
        BaseJianyingProResponse response = new BaseJianyingProResponse();
        
        if (json.containsKey("error_code")) {
            // 错误响应
            response.setSuccess(false);
            response.setErrorCode(json.getString("error_code"));
            response.setErrorMessage(json.getString("error_message"));
            response.setErrorDetails(json.getString("error_details"));
            response.setHttpStatus(json.getInteger("http_status"));
        } else {
            // 成功响应
            response.setSuccess(true);
            response.setHttpStatus(200);
        }
        
        response.setOperation(operation);
        response.setTimestamp(json.getLong("timestamp"));
        response.setRequestId(json.getString("request_id"));
        
        return response;
    }
    
    /**
     * 转换为JSONObject
     */
    public JSONObject toJson() {
        JSONObject json = new JSONObject();
        json.put("success", success);
        json.put("operation", operation);
        json.put("timestamp", timestamp);
        json.put("request_id", requestId);
        json.put("http_status", httpStatus);
        
        if (!success) {
            json.put("error_code", errorCode);
            json.put("error_message", errorMessage);
            json.put("error_details", errorDetails);
        }
        
        return json;
    }
    
    /**
     * 生成请求ID
     */
    private static String generateRequestId() {
        return "jianyingpro_" + System.currentTimeMillis() + "_" + 
               Integer.toHexString((int)(Math.random() * 0xFFFF));
    }
}
