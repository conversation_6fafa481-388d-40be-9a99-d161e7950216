{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\workflow\\components\\AgentMarket.vue?vue&type=style&index=0&id=dbf24c8c&scoped=true&lang=css&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\workflow\\components\\AgentMarket.vue", "mtime": 1754513071550}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\css-loader\\index.js", "mtime": 1749979994548}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n.agent-market {\n  padding: 0;\n}\n\n/* 固定的搜索和筛选区域 */\n.sticky-filters {\n  position: sticky;\n  top: 156px; /* 顶部导航栏100px + tab栏60px */\n  z-index: 100;\n  background: white;\n  padding: 1rem 0;\n  margin-bottom: 1rem;\n  border-bottom: 1px solid #f1f5f9;\n}\n\n.market-filters {\n  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);\n  padding: 2rem;\n  border-radius: 20px;\n  box-shadow: 0 8px 32px rgba(59, 130, 246, 0.12), 0 2px 8px rgba(0, 0, 0, 0.04);\n  max-width: 1600px;\n  margin: 0 auto;\n  border: 1px solid rgba(59, 130, 246, 0.1);\n}\n\n.filter-row {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  gap: 2rem;\n}\n\n.search-box {\n  flex: 1;\n  max-width: 600px;\n}\n\n.search-wrapper {\n  position: relative;\n  display: flex;\n  align-items: center;\n  background: white;\n  border-radius: 12px;\n  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);\n  border: 2px solid #cbd5e1;\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n}\n\n.search-wrapper:hover {\n  border-color: #3b82f6;\n  box-shadow: 0 6px 20px rgba(59, 130, 246, 0.2);\n  transform: translateY(-1px);\n}\n\n.search-wrapper:focus-within {\n  border-color: #3b82f6;\n  box-shadow: 0 8px 24px rgba(59, 130, 246, 0.3);\n  transform: translateY(-1px);\n}\n\n.search-icon {\n  position: absolute;\n  left: 16px;\n  z-index: 2;\n  color: #64748b;\n  font-size: 18px;\n  transition: color 0.3s ease;\n}\n\n.search-wrapper:focus-within .search-icon {\n  color: #3b82f6;\n}\n\n.clear-icon {\n  position: absolute;\n  right: 16px;\n  z-index: 2;\n  color: #94a3b8;\n  font-size: 16px;\n  cursor: pointer;\n  transition: color 0.3s ease;\n}\n\n.clear-icon:hover {\n  color: #64748b;\n}\n\n.search-input {\n  flex: 1;\n  border: none !important;\n  box-shadow: none !important;\n  padding-left: 48px !important;\n  padding-right: 48px !important;\n  font-size: 16px;\n  height: 48px;\n  background: transparent;\n}\n\n.search-input:focus {\n  border: none !important;\n  box-shadow: none !important;\n}\n\n.filter-controls {\n  display: flex;\n  align-items: flex-start;\n  gap: 2rem;\n  flex-wrap: wrap;\n}\n\n/* 筛选组 */\n.filter-group {\n  display: flex;\n  align-items: center;\n}\n\n/* 筛选按钮组 */\n.filter-buttons {\n  display: flex;\n  gap: 8px;\n  flex-wrap: wrap;\n}\n\n/* 筛选按钮 */\n.filter-btn {\n  display: flex;\n  align-items: center;\n  gap: 6px;\n  padding: 8px 16px;\n  border: 2px solid #e2e8f0;\n  border-radius: 12px;\n  background: white;\n  color: #64748b;\n  font-size: 14px;\n  font-weight: 500;\n  cursor: pointer;\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n  position: relative;\n  overflow: hidden;\n}\n\n.filter-btn::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: -100%;\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(90deg, transparent, rgba(79, 70, 229, 0.1), transparent);\n  transition: left 0.5s ease;\n}\n\n.filter-btn:hover::before {\n  left: 100%;\n}\n\n.filter-btn:hover {\n  border-color: #4f46e5;\n  color: #4f46e5;\n  transform: translateY(-1px);\n  box-shadow: 0 4px 12px rgba(79, 70, 229, 0.15);\n}\n\n/* 激活状态 */\n.filter-btn.active {\n  background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);\n  border-color: #4f46e5;\n  color: white;\n  transform: translateY(-1px);\n  box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3);\n}\n\n.filter-btn.active:hover {\n  background: linear-gradient(135deg, #4338ca 0%, #6d28d9 100%);\n  border-color: #4338ca;\n  box-shadow: 0 6px 16px rgba(79, 70, 229, 0.4);\n}\n\n/* 特殊按钮样式 */\n.filter-btn.official.active {\n  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);\n  border-color: #f59e0b;\n}\n\n.filter-btn.official.active:hover {\n  background: linear-gradient(135deg, #d97706 0%, #b45309 100%);\n  border-color: #d97706;\n}\n\n.filter-btn.creator.active {\n  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);\n  border-color: #3b82f6;\n}\n\n.filter-btn.creator.active:hover {\n  background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);\n  border-color: #2563eb;\n}\n\n.filter-btn.purchased.active {\n  background: linear-gradient(135deg, #10b981 0%, #059669 100%);\n  border-color: #10b981;\n}\n\n.filter-btn.purchased.active:hover {\n  background: linear-gradient(135deg, #059669 0%, #047857 100%);\n  border-color: #059669;\n}\n\n.filter-btn.unpurchased.active {\n  background: linear-gradient(135deg, #f97316 0%, #ea580c 100%);\n  border-color: #f97316;\n}\n\n.filter-btn.unpurchased.active:hover {\n  background: linear-gradient(135deg, #ea580c 0%, #c2410c 100%);\n  border-color: #ea580c;\n}\n\n/* 按钮图标 */\n.filter-btn .anticon {\n  font-size: 16px;\n}\n\n/* 重置按钮特殊样式 */\n.filter-btn.reset-btn {\n  border-color: #f97316;\n  color: #f97316;\n}\n\n.filter-btn.reset-btn:hover:not(:disabled) {\n  border-color: #ea580c;\n  color: #ea580c;\n  background: linear-gradient(135deg, #fff7ed 0%, #fed7aa 100%);\n}\n\n.filter-btn.reset-btn:disabled {\n  border-color: #e2e8f0;\n  color: #94a3b8;\n  background: #f8fafc;\n  cursor: not-allowed;\n  transform: none;\n  box-shadow: none;\n}\n\n.filter-btn.reset-btn:disabled:hover {\n  border-color: #e2e8f0;\n  color: #94a3b8;\n  background: #f8fafc;\n  transform: none;\n  box-shadow: none;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .filter-controls {\n    gap: 1rem;\n  }\n\n  .filter-buttons {\n    gap: 6px;\n  }\n\n  .filter-btn {\n    padding: 6px 12px;\n    font-size: 13px;\n  }\n\n  .filter-btn .anticon {\n    font-size: 14px;\n  }\n\n  .filter-btn.reset-btn {\n    padding: 6px 10px;\n  }\n}\n\n/* 智能体列表 */\n.agent-list {\n  background: white;\n  border-radius: 16px;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\n  overflow: hidden;\n}\n\n.list-header {\n  padding: 2rem 2rem 1rem 2rem;\n  border-bottom: 1px solid #f1f5f9;\n}\n\n.list-title {\n  font-size: 1.5rem;\n  font-weight: 600;\n  color: #1e293b;\n  margin: 0;\n}\n\n.list-count {\n  color: #64748b;\n  font-weight: 400;\n  font-size: 1rem;\n}\n\n/* 智能体网格 */\n.agent-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));\n  gap: 1.5rem;\n  padding: 2rem;\n  max-width: 1600px;\n  margin: 0 auto;\n}\n\n/* 空状态 */\n.empty-state {\n  padding: 4rem 2rem;\n  text-align: center;\n}\n\n/* 懒加载相关样式 */\n.load-more-wrapper {\n  padding: 2rem;\n  border-top: 1px solid #f1f5f9;\n  display: flex;\n  justify-content: center;\n  max-width: 1600px;\n  margin: 0 auto;\n}\n\n.loading-more {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  color: #64748b;\n  font-size: 0.875rem;\n}\n\n.load-more-trigger {\n  height: 20px;\n  width: 100%;\n}\n\n.no-more-data {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  color: #94a3b8;\n  font-size: 0.875rem;\n}\n\n.no-more-data .anticon {\n  color: #10b981;\n}\n\n/* 加载状态 */\n.loading-state {\n  padding: 4rem 2rem;\n  text-align: center;\n}\n\n.loading-placeholder {\n  height: 400px;\n  background: transparent;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .filter-row {\n    flex-direction: column;\n    gap: 1rem;\n  }\n\n  .search-box {\n    width: 100%;\n  }\n\n  .filter-controls {\n    width: 100%;\n    justify-content: space-between;\n  }\n\n  .agent-grid {\n    grid-template-columns: 1fr;\n    gap: 1rem;\n    padding: 1rem;\n  }\n\n  .market-filters,\n  .list-header,\n  .load-more-wrapper {\n    padding: 1rem;\n  }\n}\n", {"version": 3, "sources": ["AgentMarket.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+lBA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "AgentMarket.vue", "sourceRoot": "src/views/website/workflow/components", "sourcesContent": ["<template>\n  <div class=\"agent-market\">\n    <!-- 固定的搜索和筛选区域 -->\n    <div class=\"sticky-filters\">\n      <div class=\"market-filters\">\n        <div class=\"filter-row\">\n          <!-- 搜索框 -->\n          <div class=\"search-box\">\n            <div class=\"search-wrapper\">\n              <a-icon type=\"search\" class=\"search-icon\" />\n              <a-input\n                v-model=\"searchQuery\"\n                placeholder=\"搜索智能体名称、描述或标签...\"\n                size=\"large\"\n                @pressEnter=\"handleSearch\"\n                @input=\"handleSearch\"\n                class=\"search-input\"\n              />\n              <a-icon\n                v-if=\"searchQuery\"\n                type=\"close-circle\"\n                class=\"clear-icon\"\n                @click=\"clearSearch\"\n              />\n            </div>\n          </div>\n\n          <!-- 筛选器 -->\n          <div class=\"filter-controls\">\n            <!-- 作者类型筛选 -->\n            <div class=\"filter-group\">\n              <div class=\"filter-buttons\">\n                <button\n                  class=\"filter-btn\"\n                  :class=\"{ 'active': authorTypeFilter === '' }\"\n                  @click=\"setAuthorTypeFilter('')\"\n                >\n                  <a-icon type=\"appstore\" />\n                  全部\n                </button>\n                <button\n                  class=\"filter-btn official\"\n                  :class=\"{ 'active': authorTypeFilter === '1' }\"\n                  @click=\"setAuthorTypeFilter('1')\"\n                >\n                  <a-icon type=\"crown\" />\n                  官方\n                </button>\n                <button\n                  class=\"filter-btn creator\"\n                  :class=\"{ 'active': authorTypeFilter === '2' }\"\n                  @click=\"setAuthorTypeFilter('2')\"\n                >\n                  <a-icon type=\"user\" />\n                  创作者\n                </button>\n              </div>\n            </div>\n\n            <!-- 状态筛选 -->\n            <div class=\"filter-group\">\n              <div class=\"filter-buttons\">\n                <button\n                  class=\"filter-btn\"\n                  :class=\"{ 'active': purchaseStatusFilter === '' }\"\n                  @click=\"setPurchaseStatusFilter('')\"\n                >\n                  <a-icon type=\"appstore\" />\n                  全部\n                </button>\n                <button\n                  class=\"filter-btn purchased\"\n                  :class=\"{ 'active': purchaseStatusFilter === 'purchased' }\"\n                  @click=\"setPurchaseStatusFilter('purchased')\"\n                >\n                  <a-icon type=\"check-circle\" />\n                  {{ getPurchaseFilterText() }}\n                </button>\n                <button\n                  class=\"filter-btn unpurchased\"\n                  :class=\"{ 'active': purchaseStatusFilter === 'unpurchased' }\"\n                  @click=\"setPurchaseStatusFilter('unpurchased')\"\n                >\n                  <a-icon type=\"shopping\" />\n                  未购\n                </button>\n              </div>\n            </div>\n\n            <!-- 重置按钮 -->\n            <div class=\"filter-group\">\n              <div class=\"filter-buttons\">\n                <button\n                  class=\"filter-btn reset-btn\"\n                  @click=\"resetAllFilters\"\n                  :disabled=\"!hasActiveFilters\"\n                >\n                  <a-icon type=\"reload\" />\n                  重置\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 智能体列表 -->\n    <div class=\"agent-list\" v-if=\"!loading\">\n      <div class=\"list-header\">\n        <h3 class=\"list-title\">\n          智能体列表\n          <span class=\"list-count\">({{ totalCount }}个)</span>\n        </h3>\n      </div>\n\n      <!-- 智能体卡片网格 -->\n      <div class=\"agent-grid\" v-if=\"agentList.length > 0\">\n        <AgentCard\n          v-for=\"agent in agentList\"\n          :key=\"agent.id\"\n          :agent=\"agent\"\n          @view-detail=\"handleViewDetail\"\n        />\n      </div>\n\n      <!-- 空状态 -->\n      <div v-else class=\"empty-state\">\n        <a-empty\n          description=\"暂无智能体数据\"\n        >\n          <a-button type=\"primary\" @click=\"handleRefresh\">\n            <a-icon type=\"reload\" />\n            刷新数据\n          </a-button>\n        </a-empty>\n      </div>\n\n      <!-- 加载更多提示 -->\n      <div class=\"load-more-wrapper\" v-if=\"agentList.length > 0\">\n        <div v-if=\"loadingMore\" class=\"loading-more\">\n          <a-spin size=\"small\" />\n          <span>正在加载更多...</span>\n        </div>\n        <div v-else-if=\"hasMore\" class=\"load-more-trigger\" ref=\"loadMoreTrigger\">\n          <!-- 滚动到这里触发加载更多 -->\n        </div>\n        <div v-else class=\"no-more-data\">\n          <a-icon type=\"check-circle\" />\n          <span>已加载全部数据 (共{{ totalCount }}个)</span>\n        </div>\n      </div>\n    </div>\n\n    <!-- 加载状态 -->\n    <div v-else class=\"loading-state\">\n      <a-spin size=\"large\" tip=\"正在加载智能体数据...\">\n        <div class=\"loading-placeholder\"></div>\n      </a-spin>\n    </div>\n\n    <!-- 智能体详情弹窗 -->\n    <AgentDetailModal\n      :visible=\"detailModalVisible\"\n      :agentId=\"selectedAgentId\"\n      :isPurchased=\"isSelectedAgentPurchased\"\n      @close=\"handleCloseDetailModal\"\n      @update:visible=\"handleUpdateVisible\"\n      @purchase=\"handlePurchaseFromModal\"\n      @purchase-success=\"handlePurchaseSuccess\"\n    />\n  </div>\n</template>\n\n<script>\nimport AgentCard from './AgentCard.vue'\nimport AgentDetailModal from './AgentDetailModal.vue'\nimport { getUserRole } from '@/api/usercenter'\n\nexport default {\n  name: 'AgentMarket',\n  components: {\n    AgentCard,\n    AgentDetailModal\n  },\n  data() {\n    return {\n      loading: false,\n      loadingMore: false,\n      searchQuery: '',\n      authorTypeFilter: '',\n      purchaseStatusFilter: '', // 购买状态筛选：'' | 'purchased' | 'free'\n      agentList: [],\n      userRole: 'user', // 用户角色\n      currentPage: 1,\n      pageSize: 16,\n      totalCount: 0,\n      hasMore: true,\n      // 详情弹窗相关\n      detailModalVisible: false, // 详情弹窗显示状态\n      selectedAgentId: '', // 选中的智能体ID\n      selectedAgent: null, // 选中的智能体数据\n      purchasedAgents: [], // 已购买的智能体ID列表\n      debounceSearch: null // 防抖搜索函数\n    }\n  },\n  computed: {\n    // 检查选中的智能体是否已购买（使用智能体对象的购买状态）\n    isSelectedAgentPurchased() {\n      if (!this.selectedAgent) {\n        return false\n      }\n      return this.selectedAgent.isPurchased || false\n    },\n\n    // 检查是否有激活的筛选条件\n    hasActiveFilters() {\n      return this.searchQuery !== '' ||\n             this.authorTypeFilter !== '' ||\n             this.purchaseStatusFilter !== ''\n    }\n  },\n  async mounted() {\n    await this.loadUserRole()\n    // 🔥 不再需要单独加载购买状态，后端列表API已包含购买状态\n    await this.loadAgentList()\n    this.setupIntersectionObserver()\n    this.initDebounceSearch()\n  },\n\n  beforeDestroy() {\n    if (this.observer) {\n      this.observer.disconnect()\n    }\n  },\n  methods: {\n    // 加载用户角色（实时API获取，与Membership页面保持一致）\n    async loadUserRole() {\n      try {\n        const response = await getUserRole()\n        if (response && response.success) {\n          this.userRole = response.result.role_code || 'user'\n        } else {\n          this.userRole = 'user'\n        }\n      } catch (error) {\n        console.error('🔍 AgentMarket: 获取用户角色异常:', error)\n        this.userRole = 'user'\n      }\n    },\n\n    // 加载智能体列表（首次加载或搜索时重置）\n    async loadAgentList(reset = true) {\n      if (reset) {\n        this.loading = true\n        this.currentPage = 1\n        this.agentList = []\n        this.hasMore = true\n      } else {\n        this.loadingMore = true\n      }\n\n      try {\n        const params = {\n          pageNo: this.currentPage,\n          pageSize: this.pageSize,\n          agentName: this.searchQuery || undefined,\n          authorType: this.authorTypeFilter || undefined,\n          auditStatus: '2', // 只显示审核通过的\n          purchaseStatus: this.purchaseStatusFilter || undefined // 添加购买状态筛选\n        }\n\n        // 调用后端API\n        const response = await this.$http.get('/api/agent/market/list', { params })\n\n        // 兼容不同的响应格式\n        const data = response.data || response\n        if (data && data.success) {\n          const newRecords = data.result.records || []\n\n          // 直接使用后端返回的价格信息，不再前端计算\n          const processedRecords = newRecords.map(agent => {\n            // 后端已经计算好了所有价格信息，直接使用\n            return {\n              ...agent\n              // 后端返回的字段：discountPrice, originalPrice, showDiscountPrice, isFree, isPurchased 等\n            }\n          })\n\n          if (reset) {\n            this.agentList = processedRecords\n          } else {\n            this.agentList.push(...processedRecords)\n          }\n\n          // 使用后端返回的总数（后端已经根据筛选条件返回正确的总数）\n          this.totalCount = data.result.total || 0\n\n          // 判断是否还有更多数据\n          this.hasMore = this.agentList.length < this.totalCount\n\n          // 如果有数据，准备下一页\n          if (newRecords.length > 0) {\n            this.currentPage++\n          }\n        } else {\n          this.$message.error((data && data.message) || '获取智能体列表失败')\n        }\n      } catch (error) {\n        console.error('加载智能体列表失败:', error)\n        this.$message.error('加载智能体列表失败，请稍后重试')\n      } finally {\n        this.loading = false\n        this.loadingMore = false\n\n        // 重新设置IntersectionObserver，确保监听新的DOM元素\n        if (reset) {\n          this.$nextTick(() => {\n            this.setupIntersectionObserver()\n          })\n        }\n      }\n    },\n\n    // 临时模拟数据\n    async loadMockData() {\n      // 模拟API延迟\n      await new Promise(resolve => setTimeout(resolve, 500))\n\n      this.agentList = []\n      this.totalCount = 0\n      this.hasMore = false\n    },\n\n    // 获取购买筛选的显示文本\n    getPurchaseFilterText() {\n      if (this.userRole === 'SVIP') {\n        return '已购/SVIP免费'\n      } else if (this.userRole === 'VIP') {\n        return '已购买'\n      } else {\n        return '已购买'\n      }\n    },\n\n    // 加载更多数据\n    async loadMore() {\n      if (!this.hasMore || this.loadingMore) {\n        return\n      }\n      await this.loadAgentList(false)\n    },\n\n    // 计算价格和推广标签显示\n    calculatePrice(agent) {\n      let showSvipPromo = false\n      let showDiscountPrice = false\n      let discountRate = 1 // 默认无折扣\n      let isFree = false // 是否免费\n\n      // 🔥 已购买的智能体不显示任何推广标签（使用后端返回的购买状态）\n      if (agent.isPurchased) {\n        return {\n          showSvipPromo: false,\n          showDiscountPrice: false,\n          discountRate: 1,\n          isFree: false,\n          isPurchased: true\n        }\n      }\n\n      // 根据用户角色计算价格和推广显示\n      if (this.userRole === null || this.userRole === 'user' || this.userRole === 'admin') {\n        // 未登录、普通用户或管理员：显示SVIP推广标签\n        showSvipPromo = true\n        showDiscountPrice = false\n      } else if (this.userRole === 'VIP') {\n        // VIP用户：显示7折价格，不显示推广标签\n        showSvipPromo = false\n        showDiscountPrice = true\n        discountRate = 0.7 // VIP 7折\n      } else if (this.userRole === 'SVIP') {\n        // SVIP用户：根据作者类型计算价格\n        showSvipPromo = false\n\n        if (agent && (agent.authorType === 1 || agent.authorType === '1')) {\n          // 官方智能体：免费\n          isFree = true\n          discountRate = 0\n          showDiscountPrice = false // 免费时不显示折扣价格\n        } else if (agent && (agent.authorType === 2 || agent.authorType === '2')) {\n          // 创作者智能体：5折\n          isFree = false\n          showDiscountPrice = true\n          discountRate = 0.5\n        }\n      }\n\n\n\n      return {\n        showSvipPromo,\n        showDiscountPrice,\n        discountRate,\n        isFree,\n        isPurchased: false\n      }\n    },\n\n    // 搜索处理\n    handleSearch() {\n      this.scrollToTop()\n      this.loadAgentList(true)\n    },\n\n    // 筛选变化处理\n    handleFilterChange() {\n      this.scrollToTop()\n      this.loadAgentList(true)\n    },\n\n    // 设置作者类型筛选\n    setAuthorTypeFilter(value) {\n      this.authorTypeFilter = value\n      this.handleFilterChange()\n    },\n\n    // 设置购买状态筛选\n    setPurchaseStatusFilter(value) {\n      this.purchaseStatusFilter = value\n      this.handleFilterChange()\n    },\n\n    // 重置所有筛选条件\n    resetAllFilters() {\n      this.searchQuery = ''\n      this.authorTypeFilter = ''\n      this.purchaseStatusFilter = ''\n      this.handleFilterChange()\n    },\n\n    // 滚动到顶部\n    scrollToTop() {\n      // 滚动到页面顶部\n      window.scrollTo({\n        top: 0,\n        behavior: 'smooth'\n      })\n    },\n\n    // 设置滚动监听\n    setupIntersectionObserver() {\n      // 先断开旧的observer\n      if (this.observer) {\n        this.observer.disconnect()\n        this.observer = null\n      }\n\n      this.$nextTick(() => {\n        const target = this.$refs.loadMoreTrigger\n        if (!target) return\n\n        this.observer = new IntersectionObserver((entries) => {\n          entries.forEach(entry => {\n            if (entry.isIntersecting && this.hasMore && !this.loadingMore) {\n              this.loadMore()\n            }\n          })\n        }, {\n          rootMargin: '100px' // 提前100px开始加载\n        })\n\n        this.observer.observe(target)\n      })\n    },\n\n    // 查看详情\n    async handleViewDetail(agent) {\n      // 🔒 服务端验证购买状态，防止前端缓存篡改\n      try {\n        const response = await this.$http.get(`/api/agent/market/purchase/check/${agent.id}`)\n\n        if (response && response.success) {\n          // 更新智能体的真实购买状态，但保持SVIP免费的显示逻辑\n          const backendPurchased = response.result.isPurchased\n\n          // 如果是SVIP用户对官方智能体，保持免费状态显示，不改为已购买\n          if (this.userRole === 'SVIP' && agent.authorType === '1' && agent.isFree) {\n            // 保持SVIP免费状态，不更新为已购买\n            agent.isPurchased = backendPurchased // 内部状态更新（用于权限判断）\n          } else {\n            // 其他情况正常更新购买状态\n            agent.isPurchased = backendPurchased\n          }\n        }\n      } catch (error) {\n        console.error('❌ 购买状态验证出错:', error)\n        // 验证失败时，为了安全起见，假设未购买\n        agent.isPurchased = false\n      }\n\n      // 🎯 显示详情弹窗（无论是否购买）\n      this.selectedAgent = agent\n      this.selectedAgentId = agent.id\n      this.detailModalVisible = true\n    },\n\n    // 关闭详情弹窗\n    handleCloseDetailModal() {\n      this.detailModalVisible = false\n      this.selectedAgent = null\n      this.selectedAgentId = ''\n    },\n\n    // 处理弹窗visible状态更新\n    handleUpdateVisible(visible) {\n      this.detailModalVisible = visible\n      if (!visible) {\n        this.selectedAgent = null\n        this.selectedAgentId = ''\n      }\n    },\n\n    // 从弹窗发起购买\n    handlePurchaseFromModal(agent) {\n      // 购买逻辑已在AgentDetailModal中实现\n    },\n\n    // 购买成功回调\n    handlePurchaseSuccess(agentId) {\n      this.onPurchaseSuccess(agentId)\n      this.$message.success('购买成功！您现在可以下载该智能体的所有工作流了')\n    },\n\n    // 🔥 已废弃：加载已购买的智能体列表（现在由后端列表API直接返回购买状态）\n    async loadPurchasedAgents() {\n      // 此方法已不再使用，购买状态现在由后端列表API直接返回\n    },\n\n    // 检查智能体是否已购买（现在直接使用后端返回的状态）\n    isAgentPurchased(agentId) {\n      // 在智能体列表中查找对应的智能体，使用后端返回的购买状态\n      const agent = this.agentList.find(a => a.id === agentId)\n      return agent ? agent.isPurchased : false\n    },\n\n    // 购买成功后更新状态\n    async onPurchaseSuccess(agentId) {\n      console.log('✅ 购买成功回调:', agentId)\n\n      // 🔄 重新加载列表以获取最新的购买状态（后端会返回更新后的购买状态）\n      console.log('🔄 购买成功，重新加载智能体列表...')\n      this.loadAgentList(true) // 重置并重新加载\n\n      // 🔄 刷新用户角色（防止购买会员后角色状态不更新）\n      console.log('🔄 购买成功，刷新用户角色状态...')\n      await this.loadUserRole()\n    },\n\n    // 初始化防抖搜索\n    initDebounceSearch() {\n      this.debounceSearch = this.debounce(() => {\n        this.handleSearch()\n      }, 300)\n    },\n\n    // 防抖函数\n    debounce(func, wait) {\n      let timeout\n      return function executedFunction(...args) {\n        const later = () => {\n          clearTimeout(timeout)\n          func(...args)\n        }\n        clearTimeout(timeout)\n        timeout = setTimeout(later, wait)\n      }\n    },\n\n    // 刷新数据\n    handleRefresh() {\n      this.loadAgentList()\n    },\n\n    // 获取创作者数量\n    getCreatorCount() {\n      return this.agentList.filter(agent => agent.authorType === '2').length\n    },\n\n    // 清空搜索\n    clearSearch() {\n      this.searchQuery = ''\n      this.handleSearch()\n    },\n\n    // 清除所有筛选\n    clearAllFilters() {\n      this.searchQuery = ''\n      this.authorTypeFilter = ''\n      this.purchaseStatusFilter = ''\n      this.handleFilterChange()\n    }\n  }\n}\n</script>\n\n<style scoped>\n.agent-market {\n  padding: 0;\n}\n\n/* 固定的搜索和筛选区域 */\n.sticky-filters {\n  position: sticky;\n  top: 156px; /* 顶部导航栏100px + tab栏60px */\n  z-index: 100;\n  background: white;\n  padding: 1rem 0;\n  margin-bottom: 1rem;\n  border-bottom: 1px solid #f1f5f9;\n}\n\n.market-filters {\n  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);\n  padding: 2rem;\n  border-radius: 20px;\n  box-shadow: 0 8px 32px rgba(59, 130, 246, 0.12), 0 2px 8px rgba(0, 0, 0, 0.04);\n  max-width: 1600px;\n  margin: 0 auto;\n  border: 1px solid rgba(59, 130, 246, 0.1);\n}\n\n.filter-row {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  gap: 2rem;\n}\n\n.search-box {\n  flex: 1;\n  max-width: 600px;\n}\n\n.search-wrapper {\n  position: relative;\n  display: flex;\n  align-items: center;\n  background: white;\n  border-radius: 12px;\n  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);\n  border: 2px solid #cbd5e1;\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n}\n\n.search-wrapper:hover {\n  border-color: #3b82f6;\n  box-shadow: 0 6px 20px rgba(59, 130, 246, 0.2);\n  transform: translateY(-1px);\n}\n\n.search-wrapper:focus-within {\n  border-color: #3b82f6;\n  box-shadow: 0 8px 24px rgba(59, 130, 246, 0.3);\n  transform: translateY(-1px);\n}\n\n.search-icon {\n  position: absolute;\n  left: 16px;\n  z-index: 2;\n  color: #64748b;\n  font-size: 18px;\n  transition: color 0.3s ease;\n}\n\n.search-wrapper:focus-within .search-icon {\n  color: #3b82f6;\n}\n\n.clear-icon {\n  position: absolute;\n  right: 16px;\n  z-index: 2;\n  color: #94a3b8;\n  font-size: 16px;\n  cursor: pointer;\n  transition: color 0.3s ease;\n}\n\n.clear-icon:hover {\n  color: #64748b;\n}\n\n.search-input {\n  flex: 1;\n  border: none !important;\n  box-shadow: none !important;\n  padding-left: 48px !important;\n  padding-right: 48px !important;\n  font-size: 16px;\n  height: 48px;\n  background: transparent;\n}\n\n.search-input:focus {\n  border: none !important;\n  box-shadow: none !important;\n}\n\n.filter-controls {\n  display: flex;\n  align-items: flex-start;\n  gap: 2rem;\n  flex-wrap: wrap;\n}\n\n/* 筛选组 */\n.filter-group {\n  display: flex;\n  align-items: center;\n}\n\n/* 筛选按钮组 */\n.filter-buttons {\n  display: flex;\n  gap: 8px;\n  flex-wrap: wrap;\n}\n\n/* 筛选按钮 */\n.filter-btn {\n  display: flex;\n  align-items: center;\n  gap: 6px;\n  padding: 8px 16px;\n  border: 2px solid #e2e8f0;\n  border-radius: 12px;\n  background: white;\n  color: #64748b;\n  font-size: 14px;\n  font-weight: 500;\n  cursor: pointer;\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n  position: relative;\n  overflow: hidden;\n}\n\n.filter-btn::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: -100%;\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(90deg, transparent, rgba(79, 70, 229, 0.1), transparent);\n  transition: left 0.5s ease;\n}\n\n.filter-btn:hover::before {\n  left: 100%;\n}\n\n.filter-btn:hover {\n  border-color: #4f46e5;\n  color: #4f46e5;\n  transform: translateY(-1px);\n  box-shadow: 0 4px 12px rgba(79, 70, 229, 0.15);\n}\n\n/* 激活状态 */\n.filter-btn.active {\n  background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);\n  border-color: #4f46e5;\n  color: white;\n  transform: translateY(-1px);\n  box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3);\n}\n\n.filter-btn.active:hover {\n  background: linear-gradient(135deg, #4338ca 0%, #6d28d9 100%);\n  border-color: #4338ca;\n  box-shadow: 0 6px 16px rgba(79, 70, 229, 0.4);\n}\n\n/* 特殊按钮样式 */\n.filter-btn.official.active {\n  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);\n  border-color: #f59e0b;\n}\n\n.filter-btn.official.active:hover {\n  background: linear-gradient(135deg, #d97706 0%, #b45309 100%);\n  border-color: #d97706;\n}\n\n.filter-btn.creator.active {\n  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);\n  border-color: #3b82f6;\n}\n\n.filter-btn.creator.active:hover {\n  background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);\n  border-color: #2563eb;\n}\n\n.filter-btn.purchased.active {\n  background: linear-gradient(135deg, #10b981 0%, #059669 100%);\n  border-color: #10b981;\n}\n\n.filter-btn.purchased.active:hover {\n  background: linear-gradient(135deg, #059669 0%, #047857 100%);\n  border-color: #059669;\n}\n\n.filter-btn.unpurchased.active {\n  background: linear-gradient(135deg, #f97316 0%, #ea580c 100%);\n  border-color: #f97316;\n}\n\n.filter-btn.unpurchased.active:hover {\n  background: linear-gradient(135deg, #ea580c 0%, #c2410c 100%);\n  border-color: #ea580c;\n}\n\n/* 按钮图标 */\n.filter-btn .anticon {\n  font-size: 16px;\n}\n\n/* 重置按钮特殊样式 */\n.filter-btn.reset-btn {\n  border-color: #f97316;\n  color: #f97316;\n}\n\n.filter-btn.reset-btn:hover:not(:disabled) {\n  border-color: #ea580c;\n  color: #ea580c;\n  background: linear-gradient(135deg, #fff7ed 0%, #fed7aa 100%);\n}\n\n.filter-btn.reset-btn:disabled {\n  border-color: #e2e8f0;\n  color: #94a3b8;\n  background: #f8fafc;\n  cursor: not-allowed;\n  transform: none;\n  box-shadow: none;\n}\n\n.filter-btn.reset-btn:disabled:hover {\n  border-color: #e2e8f0;\n  color: #94a3b8;\n  background: #f8fafc;\n  transform: none;\n  box-shadow: none;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .filter-controls {\n    gap: 1rem;\n  }\n\n  .filter-buttons {\n    gap: 6px;\n  }\n\n  .filter-btn {\n    padding: 6px 12px;\n    font-size: 13px;\n  }\n\n  .filter-btn .anticon {\n    font-size: 14px;\n  }\n\n  .filter-btn.reset-btn {\n    padding: 6px 10px;\n  }\n}\n\n/* 智能体列表 */\n.agent-list {\n  background: white;\n  border-radius: 16px;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\n  overflow: hidden;\n}\n\n.list-header {\n  padding: 2rem 2rem 1rem 2rem;\n  border-bottom: 1px solid #f1f5f9;\n}\n\n.list-title {\n  font-size: 1.5rem;\n  font-weight: 600;\n  color: #1e293b;\n  margin: 0;\n}\n\n.list-count {\n  color: #64748b;\n  font-weight: 400;\n  font-size: 1rem;\n}\n\n/* 智能体网格 */\n.agent-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));\n  gap: 1.5rem;\n  padding: 2rem;\n  max-width: 1600px;\n  margin: 0 auto;\n}\n\n/* 空状态 */\n.empty-state {\n  padding: 4rem 2rem;\n  text-align: center;\n}\n\n/* 懒加载相关样式 */\n.load-more-wrapper {\n  padding: 2rem;\n  border-top: 1px solid #f1f5f9;\n  display: flex;\n  justify-content: center;\n  max-width: 1600px;\n  margin: 0 auto;\n}\n\n.loading-more {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  color: #64748b;\n  font-size: 0.875rem;\n}\n\n.load-more-trigger {\n  height: 20px;\n  width: 100%;\n}\n\n.no-more-data {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  color: #94a3b8;\n  font-size: 0.875rem;\n}\n\n.no-more-data .anticon {\n  color: #10b981;\n}\n\n/* 加载状态 */\n.loading-state {\n  padding: 4rem 2rem;\n  text-align: center;\n}\n\n.loading-placeholder {\n  height: 400px;\n  background: transparent;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .filter-row {\n    flex-direction: column;\n    gap: 1rem;\n  }\n\n  .search-box {\n    width: 100%;\n  }\n\n  .filter-controls {\n    width: 100%;\n    justify-content: space-between;\n  }\n\n  .agent-grid {\n    grid-template-columns: 1fr;\n    gap: 1rem;\n    padding: 1rem;\n  }\n\n  .market-filters,\n  .list-header,\n  .load-more-wrapper {\n    padding: 1rem;\n  }\n}\n</style>\n"]}]}