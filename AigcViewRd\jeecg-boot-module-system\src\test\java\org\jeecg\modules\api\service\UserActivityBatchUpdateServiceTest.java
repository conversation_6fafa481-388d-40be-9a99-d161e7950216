package org.jeecg.modules.api.service;

import org.jeecg.modules.api.config.UserActivityConfig;
import org.jeecg.modules.api.dto.UserActivityUpdateDTO;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.Map;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;

/**
 * @Description: 用户活跃状态批量更新服务测试
 * @Author: AigcView
 * @Date: 2025-01-30
 * @Version: V1.0
 */
@SpringBootTest
@ActiveProfiles("dev")
public class UserActivityBatchUpdateServiceTest {

    @Autowired
    private UserActivityBatchUpdateService batchUpdateService;

    @Autowired
    private UserActivityConfig config;

    @Test
    public void testServiceInjection() {
        // 验证服务是否正确注入
        assertNotNull(batchUpdateService, "批量更新服务应该被正确注入");
        assertNotNull(config, "配置类应该被正确注入");
    }

    @Test
    public void testAddToUpdateQueue() {
        // 清空队列
        batchUpdateService.clearQueue();
        
        // 创建测试数据
        UserActivityUpdateDTO dto = UserActivityUpdateDTO.createActivityUpdate(
            "test_user_001", 
            "session_001", 
            "/api/test", 
            "*************", 
            "Mozilla/5.0 Test Browser"
        );

        // 添加到队列
        boolean result = batchUpdateService.addToUpdateQueue(dto);
        assertTrue(result, "应该成功添加到队列");

        // 验证队列大小
        assertEquals(1, batchUpdateService.getQueueSize(), "队列大小应该是1");
    }

    @Test
    public void testAddLoginUpdate() {
        // 清空队列
        batchUpdateService.clearQueue();
        
        // 添加登录更新
        boolean result = batchUpdateService.addLoginUpdate(
            "test_user_002", 
            "session_002", 
            "192.168.1.101", 
            "Mozilla/5.0 Test Browser"
        );
        
        assertTrue(result, "应该成功添加登录更新");
        assertEquals(1, batchUpdateService.getQueueSize(), "队列大小应该是1");
    }

    @Test
    public void testAddLogoutUpdate() {
        // 清空队列
        batchUpdateService.clearQueue();
        
        // 添加登出更新
        boolean result = batchUpdateService.addLogoutUpdate("test_user_003", "session_003");
        
        assertTrue(result, "应该成功添加登出更新");
        assertEquals(1, batchUpdateService.getQueueSize(), "队列大小应该是1");
    }

    @Test
    public void testAddActivityUpdate() {
        // 清空队列
        batchUpdateService.clearQueue();
        
        // 添加活跃状态更新
        boolean result = batchUpdateService.addActivityUpdate(
            "test_user_004", 
            "session_004", 
            "/api/aigc/generate", 
            "192.168.1.102", 
            "Mozilla/5.0 Test Browser"
        );
        
        assertTrue(result, "应该成功添加活跃状态更新");
        assertEquals(1, batchUpdateService.getQueueSize(), "队列大小应该是1");
    }

    @Test
    public void testBatchProcessing() throws InterruptedException {
        // 清空队列
        batchUpdateService.clearQueue();
        
        // 添加多个更新
        for (int i = 0; i < 5; i++) {
            UserActivityUpdateDTO dto = UserActivityUpdateDTO.createActivityUpdate(
                "test_user_" + String.format("%03d", i), 
                "session_" + String.format("%03d", i), 
                "/api/test/" + i, 
                "192.168.1." + (100 + i), 
                "Mozilla/5.0 Test Browser " + i
            );
            batchUpdateService.addToUpdateQueue(dto);
        }

        assertEquals(5, batchUpdateService.getQueueSize(), "队列大小应该是5");

        // 执行同步批量处理
        batchUpdateService.processBatchSync();

        // 等待处理完成
        TimeUnit.SECONDS.sleep(1);

        // 验证队列是否被处理
        assertTrue(batchUpdateService.getQueueSize() < 5, "队列应该被部分或全部处理");
    }

    @Test
    public void testForceBatchUpdate() {
        // 清空队列
        batchUpdateService.clearQueue();
        
        // 添加测试数据
        for (int i = 0; i < 3; i++) {
            UserActivityUpdateDTO dto = UserActivityUpdateDTO.createActivityUpdate(
                "force_test_user_" + i, 
                "force_session_" + i, 
                "/api/force/test", 
                "192.168.2." + (100 + i), 
                "Force Test Browser"
            );
            batchUpdateService.addToUpdateQueue(dto);
        }

        int originalSize = batchUpdateService.getQueueSize();
        assertTrue(originalSize > 0, "队列应该有数据");

        // 强制执行批量更新
        int processedCount = batchUpdateService.forceBatchUpdate();
        
        assertTrue(processedCount >= 0, "处理数量应该大于等于0");
        assertTrue(batchUpdateService.getQueueSize() <= originalSize, "队列大小应该减少或保持不变");
    }

    @Test
    public void testPerformanceStats() {
        // 获取性能统计
        Map<String, Object> stats = batchUpdateService.getPerformanceStats();
        
        assertNotNull(stats, "性能统计不应该为空");
        assertTrue(stats.containsKey("totalProcessed"), "应该包含总处理数");
        assertTrue(stats.containsKey("totalSuccess"), "应该包含成功数");
        assertTrue(stats.containsKey("totalFailed"), "应该包含失败数");
        assertTrue(stats.containsKey("currentQueueSize"), "应该包含当前队列大小");
        assertTrue(stats.containsKey("isProcessing"), "应该包含处理状态");
        assertTrue(stats.containsKey("isDegraded"), "应该包含降级状态");
        assertTrue(stats.containsKey("successRate"), "应该包含成功率");
        
        // 验证数据类型
        assertTrue(stats.get("totalProcessed") instanceof Long, "总处理数应该是Long类型");
        assertTrue(stats.get("successRate") instanceof Double, "成功率应该是Double类型");
    }

    @Test
    public void testClearQueue() {
        // 添加测试数据
        for (int i = 0; i < 3; i++) {
            UserActivityUpdateDTO dto = UserActivityUpdateDTO.createActivityUpdate(
                "clear_test_user_" + i, 
                "clear_session_" + i, 
                "/api/clear/test", 
                "192.168.3." + (100 + i), 
                "Clear Test Browser"
            );
            batchUpdateService.addToUpdateQueue(dto);
        }

        int originalSize = batchUpdateService.getQueueSize();
        assertTrue(originalSize > 0, "队列应该有数据");

        // 清空队列
        int clearedCount = batchUpdateService.clearQueue();
        
        assertEquals(originalSize, clearedCount, "清空数量应该等于原始大小");
        assertEquals(0, batchUpdateService.getQueueSize(), "队列大小应该是0");
    }

    @Test
    public void testInvalidData() {
        // 清空队列
        batchUpdateService.clearQueue();
        
        // 测试空数据
        boolean result1 = batchUpdateService.addToUpdateQueue(null);
        assertFalse(result1, "空数据应该添加失败");

        // 测试无效数据
        UserActivityUpdateDTO invalidDto = new UserActivityUpdateDTO();
        boolean result2 = batchUpdateService.addToUpdateQueue(invalidDto);
        assertFalse(result2, "无效数据应该添加失败");

        // 验证队列大小没有变化
        assertEquals(0, batchUpdateService.getQueueSize(), "队列大小应该保持为0");
    }

    @Test
    public void testCriticalApiDetection() {
        // 清空队列
        batchUpdateService.clearQueue();
        
        // 测试核心API
        UserActivityUpdateDTO criticalDto = UserActivityUpdateDTO.createActivityUpdate(
            "critical_user", 
            "critical_session", 
            "/payment/create", 
            "192.168.4.100", 
            "Critical Test Browser"
        );
        
        boolean result = batchUpdateService.addToUpdateQueue(criticalDto);
        assertTrue(result, "核心API更新应该成功添加");
        
        // 测试非核心API
        UserActivityUpdateDTO normalDto = UserActivityUpdateDTO.createActivityUpdate(
            "normal_user", 
            "normal_session", 
            "/api/user/profile", 
            "192.168.4.101", 
            "Normal Test Browser"
        );
        
        boolean result2 = batchUpdateService.addToUpdateQueue(normalDto);
        assertTrue(result2, "普通API更新应该成功添加");
        
        assertEquals(2, batchUpdateService.getQueueSize(), "队列大小应该是2");
    }

    @Test
    public void testQueueCapacityLimit() {
        // 清空队列
        batchUpdateService.clearQueue();
        
        // 获取队列容量
        int queueCapacity = config.getQueueCapacity();
        
        // 尝试添加超过容量的数据
        int addCount = 0;
        for (int i = 0; i < queueCapacity + 10; i++) {
            UserActivityUpdateDTO dto = UserActivityUpdateDTO.createActivityUpdate(
                "capacity_test_user_" + i, 
                "capacity_session_" + i, 
                "/api/capacity/test", 
                "192.168.5." + (i % 255), 
                "Capacity Test Browser"
            );
            
            boolean result = batchUpdateService.addToUpdateQueue(dto);
            if (result) {
                addCount++;
            }
        }
        
        // 验证添加的数量不超过队列容量
        assertTrue(addCount <= queueCapacity, "添加的数量不应该超过队列容量");
        assertTrue(batchUpdateService.getQueueSize() <= queueCapacity, "队列大小不应该超过容量限制");
    }
}
