# 剪映小助手测试配置文件
server:
  port: 8080
  servlet:
    context-path: /jeecg-boot

spring:
  datasource:
    dynamic:
      primary: master
      strict: false
      datasource:
        master:
          url: ***********************************************************************************************************************************************************************
          username: root
          password: root
          driver-class-name: com.mysql.cj.jdbc.Driver
  redis:
    database: 0
    host: 127.0.0.1
    lettuce:
      pool:
        max-active: 8
        max-wait: -1ms
        max-idle: 8
        min-idle: 0
    password: ''
    port: 6379
    timeout: 5000ms

# 日志配置
logging:
  level:
    org.jeecg.modules.jianying: DEBUG
    org.springframework.web: DEBUG
    org.springframework.security: DEBUG

# JeecG配置
jeecg:
  shiro:
    excludeUrls: /api/jianying/**,/api/jianyingpro/**
  # 签名密钥串
  signatureSecret: dd05f1c54d63749eda95f9fa6d49v442a

# 剪映小助手测试配置
jianying:
  test:
    access-key: JianyingAPI_2025_SecureAccess_AigcView
    mock-service: true
    enable-validation: true
