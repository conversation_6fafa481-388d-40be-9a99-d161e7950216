package org.jeecg.modules.system.service;

import org.jeecg.modules.system.entity.AicgVerifyCode;
import org.jeecg.modules.system.entity.VerifyCodeResult;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * @Description: 验证码记录表
 * @Author: jeecg-boot
 * @Date: 2025-06-19
 * @Version: V1.0
 */
public interface IAicgVerifyCodeService extends IService<AicgVerifyCode> {

    /**
     * 发送短信验证码
     * @param phone 手机号
     * @param scene 使用场景
     * @param ipAddress IP地址
     * @return 验证码发送结果
     */
    VerifyCodeResult sendSmsCode(String phone, String scene, String ipAddress);

    /**
     * 发送邮箱验证码
     * @param email 邮箱
     * @param scene 使用场景
     * @param ipAddress IP地址
     * @return 验证码发送结果
     */
    VerifyCodeResult sendEmailCode(String email, String scene, String ipAddress);

    /**
     * 验证验证码
     * @param target 目标（手机号/邮箱）
     * @param code 验证码
     * @param codeType 验证码类型
     * @param scene 使用场景
     * @return 是否验证成功
     */
    boolean verifyCode(String target, String code, String codeType, String scene);

    /**
     * 检查发送频率限制
     * @param target 目标（手机号/邮箱）
     * @param codeType 验证码类型
     * @param scene 使用场景
     * @return 是否可以发送
     */
    boolean canSendCode(String target, String codeType, String scene);

    /**
     * 检查IP发送频率限制
     * @param ipAddress IP地址
     * @param codeType 验证码类型
     * @param scene 使用场景
     * @return 是否可以发送
     */
    boolean canSendCodeByIp(String ipAddress, String codeType, String scene);

    /**
     * 生成验证码
     * @param length 长度
     * @return 验证码
     */
    String generateCode(int length);

    /**
     * 清理过期验证码
     * @return 清理的记录数
     */
    int cleanExpiredCodes();

    /**
     * 解除用户验证码限制
     * @param phone 手机号
     * @param email 邮箱
     * @param resetType 重置类型：sms-短信，email-邮箱，all-全部
     * @param ipAddress IP地址（可选，用于解除IP限制）
     * @return 是否成功
     */
    boolean resetUserVerifyCodeLimit(String phone, String email, String resetType, String ipAddress);

    /**
     * 获取今日验证码发送次数
     * @param target 目标（手机号/邮箱）
     * @param codeType 验证码类型
     * @return 今日发送次数
     */
    int getTodayCodeCount(String target, String codeType);

    /**
     * 获取IP地址近期验证码发送次数
     * @param ipAddress IP地址
     * @param codeType 验证码类型
     * @param hours 小时数（查询多少小时内的记录）
     * @return 近期发送次数
     */
    int getIpRecentCodeCount(String ipAddress, String codeType, int hours);

    /**
     * 获取用户相关IP地址近期验证码发送次数
     * @param target 目标（手机号/邮箱）
     * @param codeType 验证码类型
     * @param hours 小时数（查询多少小时内的记录）
     * @return 近期发送次数
     */
    int getUserRelatedIpRecentCodeCount(String target, String codeType, int hours);
}
