package org.jeecg.modules.api.service;

import java.util.Map;

/**
 * @Description: 用户活跃状态性能监控服务接口
 * @Author: AigcView
 * @Date: 2025-01-30
 * @Version: V1.0
 */
public interface IUserActivityPerformanceMonitor {

    // ==================== 性能监控 ====================

    /**
     * 记录API调用性能数据
     * @param apiPath API路径
     * @param responseTime 响应时间（毫秒）
     * @param isCriticalApi 是否为关键API
     * @param success 是否成功
     */
    void recordApiPerformance(String apiPath, long responseTime, boolean isCriticalApi, boolean success);

    /**
     * 获取API性能统计
     * @param apiPath API路径，为null时返回全局统计
     * @return 性能统计信息
     */
    Map<String, Object> getApiPerformanceStats(String apiPath);

    /**
     * 获取系统整体性能统计
     * @return 系统性能统计
     */
    Map<String, Object> getSystemPerformanceStats();

    /**
     * 获取最近N分钟的性能统计
     * @param minutes 时间窗口（分钟）
     * @return 性能统计信息
     */
    Map<String, Object> getRecentPerformanceStats(int minutes);

    /**
     * 检查API性能是否超过阈值
     * @param apiPath API路径
     * @param responseTime 响应时间
     * @return 是否超过阈值
     */
    boolean isPerformanceThresholdExceeded(String apiPath, long responseTime);

    /**
     * 获取性能告警信息
     * @return 告警信息列表
     */
    java.util.List<Map<String, Object>> getPerformanceAlerts();

    // ==================== 降级机制 ====================

    /**
     * 检查系统是否需要降级
     * @return 是否需要降级
     */
    boolean shouldDegrade();

    /**
     * 检查特定API是否需要降级
     * @param apiPath API路径
     * @param isCriticalApi 是否为关键API
     * @return 是否需要降级
     */
    boolean shouldDegradeApi(String apiPath, boolean isCriticalApi);

    /**
     * 手动触发系统降级
     * @param reason 降级原因
     * @param durationMinutes 降级持续时间（分钟）
     * @return 是否成功
     */
    boolean triggerDegradation(String reason, int durationMinutes);

    /**
     * 手动恢复系统
     * @param reason 恢复原因
     * @return 是否成功
     */
    boolean recoverFromDegradation(String reason);

    /**
     * 获取当前降级状态
     * @return 降级状态信息
     */
    Map<String, Object> getDegradationStatus();

    /**
     * 检查是否处于降级状态
     * @return 是否降级中
     */
    boolean isDegraded();

    // ==================== 系统负载监控 ====================

    /**
     * 获取当前系统负载
     * @return 系统负载信息
     */
    Map<String, Object> getSystemLoad();

    /**
     * 获取JVM内存使用情况
     * @return JVM内存信息
     */
    Map<String, Object> getJvmMemoryInfo();

    /**
     * 获取线程池状态
     * @return 线程池状态信息
     */
    Map<String, Object> getThreadPoolStatus();

    /**
     * 获取数据库连接池状态
     * @return 数据库连接池状态
     */
    Map<String, Object> getDatabaseConnectionPoolStatus();

    /**
     * 获取Redis连接状态
     * @return Redis连接状态
     */
    Map<String, Object> getRedisConnectionStatus();

    // ==================== 性能优化建议 ====================

    /**
     * 分析性能瓶颈
     * @return 性能瓶颈分析结果
     */
    Map<String, Object> analyzePerformanceBottlenecks();

    /**
     * 获取性能优化建议
     * @return 优化建议列表
     */
    java.util.List<Map<String, Object>> getPerformanceOptimizationSuggestions();

    /**
     * 生成性能报告
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 性能报告
     */
    Map<String, Object> generatePerformanceReport(long startTime, long endTime);

    // ==================== 配置管理 ====================

    /**
     * 更新性能阈值配置
     * @param thresholds 阈值配置
     * @return 是否成功
     */
    boolean updatePerformanceThresholds(Map<String, Object> thresholds);

    /**
     * 更新降级策略配置
     * @param degradationConfig 降级配置
     * @return 是否成功
     */
    boolean updateDegradationConfig(Map<String, Object> degradationConfig);

    /**
     * 获取当前配置
     * @return 当前配置信息
     */
    Map<String, Object> getCurrentConfig();

    // ==================== 监控数据清理 ====================

    /**
     * 清理过期的性能监控数据
     * @param retentionHours 保留时间（小时）
     * @return 清理的数据量
     */
    int cleanExpiredPerformanceData(int retentionHours);

    /**
     * 重置性能统计数据
     * @return 是否成功
     */
    boolean resetPerformanceStats();

    /**
     * 导出性能监控数据
     * @param format 导出格式（json, csv等）
     * @return 导出的数据
     */
    String exportPerformanceData(String format);

    // ==================== 实时监控 ====================

    /**
     * 获取实时性能指标
     * @return 实时性能指标
     */
    Map<String, Object> getRealTimeMetrics();

    /**
     * 获取API调用频率统计
     * @param timeWindowMinutes 时间窗口（分钟）
     * @return API调用频率统计
     */
    Map<String, Object> getApiCallFrequencyStats(int timeWindowMinutes);

    /**
     * 获取错误率统计
     * @param timeWindowMinutes 时间窗口（分钟）
     * @return 错误率统计
     */
    Map<String, Object> getErrorRateStats(int timeWindowMinutes);

    /**
     * 检查系统健康状态
     * @return 健康状态信息
     */
    Map<String, Object> checkSystemHealth();
}
