package org.jeecg.modules.demo.websitestats.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.demo.websitestats.entity.AigcWebsiteStats;
import org.jeecg.modules.demo.websitestats.service.IAigcWebsiteStatsService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;

/**
 * @Description: 网站统计数据
 * @Author: jeecg-boot
 * @Date:   2025-06-17
 * @Version: V1.0
 */
@Api(tags="网站统计数据")
@RestController
@RequestMapping("/aigc/websiteStats")
@Slf4j
public class AigcWebsiteStatsController extends JeecgController<AigcWebsiteStats, IAigcWebsiteStatsService> {
	@Autowired
	private IAigcWebsiteStatsService aigcWebsiteStatsService;
	
	/**
	 * 分页列表查询
	 *
	 * @param aigcWebsiteStats
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	@AutoLog(value = "网站统计数据-分页列表查询")
	@ApiOperation(value="网站统计数据-分页列表查询", notes="网站统计数据-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(AigcWebsiteStats aigcWebsiteStats,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<AigcWebsiteStats> queryWrapper = QueryGenerator.initQueryWrapper(aigcWebsiteStats, req.getParameterMap());
		Page<AigcWebsiteStats> page = new Page<AigcWebsiteStats>(pageNo, pageSize);
		IPage<AigcWebsiteStats> pageList = aigcWebsiteStatsService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param aigcWebsiteStats
	 * @return
	 */
	@AutoLog(value = "网站统计数据-添加")
	@ApiOperation(value="网站统计数据-添加", notes="网站统计数据-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody AigcWebsiteStats aigcWebsiteStats) {
		aigcWebsiteStatsService.save(aigcWebsiteStats);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param aigcWebsiteStats
	 * @return
	 */
	@AutoLog(value = "网站统计数据-编辑")
	@ApiOperation(value="网站统计数据-编辑", notes="网站统计数据-编辑")
	@PutMapping(value = "/edit")
	public Result<?> edit(@RequestBody AigcWebsiteStats aigcWebsiteStats) {
		aigcWebsiteStatsService.updateById(aigcWebsiteStats);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "网站统计数据-通过id删除")
	@ApiOperation(value="网站统计数据-通过id删除", notes="网站统计数据-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		aigcWebsiteStatsService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "网站统计数据-批量删除")
	@ApiOperation(value="网站统计数据-批量删除", notes="网站统计数据-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.aigcWebsiteStatsService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "网站统计数据-通过id查询")
	@ApiOperation(value="网站统计数据-通过id查询", notes="网站统计数据-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		AigcWebsiteStats aigcWebsiteStats = aigcWebsiteStatsService.getById(id);
		if(aigcWebsiteStats==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(aigcWebsiteStats);
	}
}
