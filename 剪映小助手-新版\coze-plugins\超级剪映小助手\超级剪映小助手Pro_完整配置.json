{"openapi": "3.0.0", "info": {"title": "超级剪映小助手Pro - 一体化API集合", "description": "提供8个一体化接口，支持外部URL直接下载模式，大幅提升响应速度和降低成本。每个接口都集成了智能参数识别和错误处理机制。", "version": "2.0.0", "contact": {"name": "智界Aigc", "url": "https://www.aigcview.com", "email": "<EMAIL>"}}, "servers": [{"url": "https://www.aigcview.com", "description": "生产环境"}], "security": [{"AccessKeyAuth": []}], "components": {"securitySchemes": {"AccessKeyAuth": {"type": "<PERSON><PERSON><PERSON><PERSON>", "in": "header", "name": "access_key", "description": "API访问密钥"}}, "schemas": {"BaseRequest": {"type": "object", "required": ["access_key"], "properties": {"access_key": {"type": "string", "description": "访问密钥（必填）", "example": "JianyingAPI_2025_SecureAccess_AigcView", "default": "JianyingAPI_2025_SecureAccess_AigcView"}}}, "SuccessResponse": {"type": "object", "properties": {"success": {"type": "boolean", "description": "操作是否成功", "example": true}, "operation": {"type": "string", "description": "操作名称", "example": "一体化音频添加"}, "timestamp": {"type": "integer", "description": "响应时间戳", "example": 1705123456789}, "request_id": {"type": "string", "description": "请求ID", "example": "jianyingpro_1705123456789_abc123"}}}, "ErrorResponse": {"type": "object", "properties": {"success": {"type": "boolean", "description": "操作是否成功", "example": false}, "error": {"type": "string", "description": "错误信息", "example": "参数验证失败"}, "error_code": {"type": "string", "description": "错误代码", "example": "VALIDATION_ERROR"}, "timestamp": {"type": "integer", "description": "错误时间戳", "example": 1705123456789}}}}}, "paths": {"/jeecg-boot/api/jianyingpro/add_audios": {"post": {"summary": "一体化音频添加", "description": "智能音频添加接口，支持多种音频格式和自动时间线生成。使用TOS上传模式确保稳定性。", "operationId": "add_audios_pro", "tags": ["音频处理"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/BaseRequest"}], "type": "object", "properties": {"draft_url": {"type": "string", "description": "草稿文件URL（必填）", "example": "https://tos-s3-cn-beijing.volces.com/example/draft.json"}, "mp3_urls": {"type": "array", "items": {"type": "string"}, "description": "音频文件URL列表（必填）", "example": ["https://example.com/audio1.mp3", "https://example.com/audio2.mp3"]}, "volume": {"type": "number", "description": "音量大小（可选，0.0-1.0）", "example": 0.8, "minimum": 0.0, "maximum": 1.0}, "fade_in": {"type": "integer", "description": "淡入时长（微秒，可选）", "example": 500000}, "fade_out": {"type": "integer", "description": "淡出时长（微秒，可选）", "example": 500000}}, "required": ["access_key", "draft_url", "mp3_urls"]}}}}, "responses": {"200": {"description": "音频添加成功", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/SuccessResponse"}], "type": "object", "properties": {"data": {"type": "object", "properties": {"draft_url": {"type": "string", "description": "更新后的草稿URL"}, "audio_count": {"type": "integer", "description": "添加的音频数量"}, "total_duration": {"type": "integer", "description": "音频总时长（微秒）"}}}}}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}}}