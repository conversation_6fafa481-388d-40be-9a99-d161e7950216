package org.jeecg.modules.jianying.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

/**
 * 添加特效请求参数
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AddEffectsRequest extends BaseJianyingRequest {
    
    @ApiModelProperty(value = "草稿地址，使用create_draft输出的draft_url即可（必填）", required = true,
                     example = "https://example.com/draft/123")
    @NotBlank(message = "draft_url不能为空")
    @JsonProperty("draft_url")
    private String zjDraftUrl;

    @ApiModelProperty(value = "特效数组内容（必填）", required = true,
                     example = "[{\"effect_title\":\"金粉闪闪\",\"end\":5000000,\"start\":0}]")
    @NotBlank(message = "effect_infos不能为空")
    @JsonProperty("effect_infos")
    private String zjEffectInfos;
    
    @Override
    public String getSummary() {
        return "AddEffectsRequest{" +
               "accessKey=" + (getAccessKey() != null && getAccessKey().length() > 10 ? 
                              getAccessKey().substring(0, 10) + "***" : "null") +
               ", draftUrl=" + (zjDraftUrl != null && zjDraftUrl.length() > 30 ? 
                               zjDraftUrl.substring(0, 30) + "***" : zjDraftUrl) +
               "}";
    }
}
