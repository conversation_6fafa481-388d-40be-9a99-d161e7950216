-- 插件作者表结构修改
-- 添加职位和专业领域字段

-- 1. 添加作者职位字段（单选，关联字典）
ALTER TABLE aigc_plub_author 
ADD COLUMN title VARCHAR(50) DEFAULT NULL COMMENT '作者职位头衔' AFTER authorname;

-- 2. 添加专业领域字段（多选，存储逗号分隔的字典值）
ALTER TABLE aigc_plub_author 
ADD COLUMN expertise TEXT DEFAULT NULL COMMENT '专业领域（多选，逗号分隔）' AFTER title;

-- 查看表结构确认
-- DESC aigc_plub_author;

-- 可选：为现有数据设置默认值
-- UPDATE aigc_plub_author SET title = 'senior_ai_developer' WHERE title IS NULL;
-- UPDATE aigc_plub_author SET expertise = 'ai_technology,machine_learning' WHERE expertise IS NULL;
