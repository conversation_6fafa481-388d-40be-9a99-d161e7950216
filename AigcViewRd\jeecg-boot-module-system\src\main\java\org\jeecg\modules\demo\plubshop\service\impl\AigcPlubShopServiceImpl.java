package org.jeecg.modules.demo.plubshop.service.impl;

import org.jeecg.modules.demo.plubshop.entity.AigcPlubShop;
import org.jeecg.modules.demo.plubshop.mapper.AigcPlubShopMapper;
import org.jeecg.modules.demo.plubshop.service.IAigcPlubShopService;
import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;
import java.math.BigDecimal;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: 插件商城
 * @Author: jeecg-boot
 * @Date:   2025-06-14
 * @Version: V1.0
 */
@Slf4j
@Service
public class AigcPlubShopServiceImpl extends ServiceImpl<AigcPlubShopMapper, AigcPlubShop> implements IAigcPlubShopService {

    @Override
    public AigcPlubShop getByPluginName(String pluginName) {
        try {
            return baseMapper.getByPluginName(pluginName);
        } catch (Exception e) {
            log.error("根据插件名称查询插件信息异常: {}", e.getMessage(), e);
            return null;
        }
    }

    @Override
    public AigcPlubShop getByPluginKey(String pluginKey) {
        try {
            return baseMapper.getByPluginKey(pluginKey);
        } catch (Exception e) {
            log.error("根据插件唯一标识查询插件信息异常: {}", e.getMessage(), e);
            return null;
        }
    }

    @Override
    public boolean updatePluginStats(String pluginId, BigDecimal amount) {
        try {
            int result = baseMapper.updatePluginStats(pluginId, amount);
            if (result > 0) {
                log.info("更新插件统计信息成功，插件ID: {}, 收益金额: {}", pluginId, amount);
                return true;
            }
            return false;
        } catch (Exception e) {
            log.error("更新插件统计信息异常: {}", e.getMessage(), e);
            return false;
        }
    }

}
