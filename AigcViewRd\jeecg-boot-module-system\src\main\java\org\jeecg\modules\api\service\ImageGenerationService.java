package org.jeecg.modules.api.service;

import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.demo.userprofile.entity.AicgUserProfile;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * 图片生成业务服务
 * 
 * <AUTHOR>
 * @since 2025-01-16
 */
@Service
@Slf4j
public class ImageGenerationService {

    @Autowired
    private DouBaoImageApiService douBaoImageApiService;

    /**
     * 生成图片
     */
    public Map<String, Object> generateImage(Map<String, Object> params, AicgUserProfile userProfile) {
        try {
            log.info("开始生成图片，用户: {}", userProfile.getUserId());

            // 1. 参数预处理
            Map<String, Object> processedParams = preprocessParams(params);
            
            // 2. 调用豆包API生成图片
            Map<String, Object> apiResult = douBaoImageApiService.generateImage(processedParams);
            
            // 3. 结果后处理
            Map<String, Object> finalResult = postprocessResult(apiResult, userProfile);
            
            log.info("图片生成成功: imageUrl={}, 用户={}", 
                    finalResult.get("image_url"), userProfile.getUserId());
            
            return finalResult;

        } catch (Exception e) {
            log.error("图片生成失败，用户: {}, 错误: {}", userProfile.getUserId(), e.getMessage(), e);
            throw new RuntimeException("图片生成失败: " + e.getMessage());
        }
    }

    /**
     * 获取图片生成状态（同步生成，直接返回完成状态）
     */
    public Map<String, Object> getImageStatus(String imageUrl, AicgUserProfile userProfile) {
        try {
            log.info("查询图片状态，用户: {}, 图片URL: {}", userProfile.getUserId(), imageUrl);

            Map<String, Object> result = new HashMap<>();
            
            if (StringUtils.hasText(imageUrl)) {
                result.put("status", "completed");
                result.put("image_url", imageUrl);
                result.put("message", "图片已生成完成");
                result.put("progress", 100);
            } else {
                result.put("status", "failed");
                result.put("message", "图片URL无效");
                result.put("progress", 0);
            }
            
            log.info("图片状态查询完成: status={}, 用户={}", 
                    result.get("status"), userProfile.getUserId());
            
            return result;

        } catch (Exception e) {
            log.error("查询图片状态失败，用户: {}, 错误: {}", userProfile.getUserId(), e.getMessage(), e);
            throw new RuntimeException("查询图片状态失败: " + e.getMessage());
        }
    }

    /**
     * 验证图片生成参数
     */
    public void validateImageParams(Map<String, Object> params) {
        try {
            log.info("开始验证图片生成参数: {}", params);

            // 验证必填参数
            String prompt = (String) params.get("prompt");
            if (!StringUtils.hasText(prompt)) {
                throw new IllegalArgumentException("prompt不能为空");
            }
            if (prompt.length() > 1000) {
                throw new IllegalArgumentException("prompt长度不能超过1000字符");
            }

            // 验证可选参数
            String size = (String) params.get("size");
            if (size != null) {
                if (!douBaoImageApiService.getSupportedSizes().contains(size)) {
                    throw new IllegalArgumentException("不支持的图片尺寸: " + size);
                }
            }

            Integer seed = (Integer) params.get("seed");
            if (seed != null && (seed < -1 || seed > 2147483647)) {
                throw new IllegalArgumentException("seed取值范围为[-1, 2147483647]");
            }

            Float guidanceScale = (Float) params.get("guidance_scale");
            if (guidanceScale != null && (guidanceScale < 1.0f || guidanceScale > 10.0f)) {
                throw new IllegalArgumentException("guidance_scale取值范围为[1.0, 10.0]");
            }

            log.info("图片生成参数验证通过");

        } catch (Exception e) {
            log.error("图片生成参数验证失败: {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 参数预处理
     */
    private Map<String, Object> preprocessParams(Map<String, Object> params) {
        Map<String, Object> processed = new HashMap<>(params);
        
        // 应用默认值
        Map<String, Object> defaults = douBaoImageApiService.getDefaultParams();
        for (Map.Entry<String, Object> entry : defaults.entrySet()) {
            if (!processed.containsKey(entry.getKey()) || processed.get(entry.getKey()) == null) {
                processed.put(entry.getKey(), entry.getValue());
            }
        }
        
        // 移除不需要传递给API的参数
        processed.remove("apiKey");
        
        // 类型转换和标准化
        if (processed.containsKey("seed") && processed.get("seed") instanceof String) {
            try {
                processed.put("seed", Integer.parseInt((String) processed.get("seed")));
            } catch (NumberFormatException e) {
                log.warn("seed参数格式错误，使用默认值: {}", processed.get("seed"));
                processed.put("seed", -1);
            }
        }
        
        if (processed.containsKey("guidance_scale") && processed.get("guidance_scale") instanceof String) {
            try {
                processed.put("guidance_scale", Float.parseFloat((String) processed.get("guidance_scale")));
            } catch (NumberFormatException e) {
                log.warn("guidance_scale参数格式错误，使用默认值: {}", processed.get("guidance_scale"));
                processed.put("guidance_scale", 2.5f);
            }
        }
        
        if (processed.containsKey("watermark") && processed.get("watermark") instanceof String) {
            processed.put("watermark", Boolean.parseBoolean((String) processed.get("watermark")));
        }
        
        log.info("参数预处理完成: {}", processed);
        return processed;
    }

    /**
     * 结果后处理
     */
    private Map<String, Object> postprocessResult(Map<String, Object> apiResult, AicgUserProfile userProfile) {
        Map<String, Object> result = new HashMap<>(apiResult);
        
        // 添加用户相关信息
        result.put("user_id", userProfile.getUserId());
        result.put("generated_at", System.currentTimeMillis());
        
        // 添加状态信息
        result.put("status", "completed");
        result.put("progress", 100);
        
        // 确保必要字段存在
        if (!result.containsKey("generated_images")) {
            result.put("generated_images", 1);
        }
        
        if (!result.containsKey("message")) {
            result.put("message", "图片生成成功");
        }
        
        log.info("结果后处理完成: {}", result);
        return result;
    }

    /**
     * 获取支持的图片尺寸
     */
    public Map<String, Object> getSupportedSizes() {
        Map<String, Object> result = new HashMap<>();
        result.put("sizes", douBaoImageApiService.getSupportedSizes());
        result.put("default_size", "1024x1024");
        Map<String, String> sizeDescriptions = new HashMap<>();
        sizeDescriptions.put("1024x1024", "正方形 (1:1) - 适合头像、图标、社交媒体");
        sizeDescriptions.put("864x1152", "竖屏 (3:4) - 适合手机壁纸、海报");
        sizeDescriptions.put("1152x864", "横屏 (4:3) - 适合电脑壁纸、展示图");
        sizeDescriptions.put("1280x720", "宽屏 (16:9) - 适合视频封面、横幅");
        sizeDescriptions.put("720x1280", "手机竖屏 (9:16) - 适合短视频、手机壁纸");
        sizeDescriptions.put("832x1248", "海报比例 (2:3) - 适合海报、宣传图");
        sizeDescriptions.put("1248x832", "风景比例 (3:2) - 适合风景照、横版海报");
        sizeDescriptions.put("1512x648", "超宽屏 (21:9) - 适合横幅广告、全景图");
        result.put("size_descriptions", sizeDescriptions);
        return result;
    }

    /**
     * 获取默认参数
     */
    public Map<String, Object> getDefaultParams() {
        return douBaoImageApiService.getDefaultParams();
    }
}
