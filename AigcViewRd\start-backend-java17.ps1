Write-Host "Starting 智界Aigc Backend with Java 17 compatibility..." -ForegroundColor Green
Write-Host ""
Write-Host "重要提示：此脚本解决Java 17模块系统限制问题" -ForegroundColor Yellow
Write-Host "Important: This script resolves Java 17 module system restrictions" -ForegroundColor Yellow
Write-Host ""

Set-Location "jeecg-boot-module-system"

Write-Host "正在启动后端服务..." -ForegroundColor Cyan
Write-Host "Starting backend service..." -ForegroundColor Cyan

# 方法1: 使用Maven启动（推荐）
$mavenArgs = @(
    "org.springframework.boot:spring-boot-maven-plugin:run",
    "-Dspring-boot.run.jvmArguments=--add-opens java.base/java.lang.invoke=ALL-UNNAMED --add-opens java.base/java.lang.reflect=ALL-UNNAMED --add-opens java.base/java.io=ALL-UNNAMED"
)

try {
    & mvn $mavenArgs
} catch {
    Write-Host "Maven启动失败，尝试其他方法..." -ForegroundColor Red
    Write-Host "Maven startup failed, trying alternative method..." -ForegroundColor Red

    # 方法2: 直接Java启动（需要先编译）
    $javaArgs = @(
        "--add-opens", "java.base/java.lang.invoke=ALL-UNNAMED",
        "--add-opens", "java.base/java.lang.reflect=ALL-UNNAMED",
        "--add-opens", "java.base/java.io=ALL-UNNAMED",
        "-jar", "target/jeecg-boot-module-system-2.4.6.jar"
    )

    & java $javaArgs
}

Write-Host ""
Write-Host "如果启动成功，请访问: http://localhost:8080/jeecg-boot/" -ForegroundColor Green
Write-Host "If startup successful, visit: http://localhost:8080/jeecg-boot/" -ForegroundColor Green
Read-Host "Press Enter to continue..."
