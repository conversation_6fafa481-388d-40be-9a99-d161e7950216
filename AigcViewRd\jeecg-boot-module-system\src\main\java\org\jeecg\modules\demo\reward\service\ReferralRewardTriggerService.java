package org.jeecg.modules.demo.reward.service;

import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.demo.referral.entity.AicgUserReferral;
import org.jeecg.modules.demo.referral.service.IAicgUserReferralService;
import org.jeecg.modules.demo.referralreward.entity.AicgUserReferralReward;
import org.jeecg.modules.demo.referralreward.service.IAicgUserReferralRewardService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * 邀请奖励触发服务
 * 统一处理各种业务场景下的邀请奖励触发逻辑
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
@Slf4j
@Service
public class ReferralRewardTriggerService {

    @Autowired
    private IAicgUserReferralService userReferralService;
    
    @Autowired
    private IAicgUserReferralRewardService userReferralRewardService;
    
    @Autowired
    private JdbcTemplate jdbcTemplate;

    /**
     * 会员订阅成功触发奖励
     * 
     * @param subscriberUserId 订阅用户ID
     * @param subscriptionAmount 订阅金额
     * @param membershipLevel 会员等级
     * @param orderId 订单ID
     * @return 是否成功触发奖励
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean triggerMembershipSubscriptionReward(String subscriberUserId, BigDecimal subscriptionAmount, 
                                                      Integer membershipLevel, String orderId) {
        try {
            log.info("🎯 开始处理会员订阅奖励触发 - 用户: {}, 金额: {}, 等级: {}", 
                    subscriberUserId, subscriptionAmount, membershipLevel);

            // 1. 查找邀请关系
            AicgUserReferral referral = userReferralService.getByRefereeId(subscriberUserId);
            if (referral == null) {
                log.info("📝 用户 {} 没有邀请关系，无需发放奖励", subscriberUserId);
                return true; // 没有邀请关系不算错误
            }

            String referrerId = referral.getReferrerId();
            log.info("🔗 找到邀请关系 - 邀请人: {}, 被邀请人: {}", referrerId, subscriberUserId);

            // 2. 计算邀请人的佣金比例
            BigDecimal commissionRate = calculateCommissionRate(referrerId);
            if (commissionRate.compareTo(BigDecimal.ZERO) <= 0) {
                log.warn("⚠️ 邀请人 {} 的佣金比例为0，跳过奖励发放", referrerId);
                return true;
            }

            // 3. 计算奖励金额
            BigDecimal rewardAmount = subscriptionAmount.multiply(commissionRate)
                    .setScale(2, RoundingMode.HALF_UP);

            log.info("💰 计算奖励 - 订阅金额: {}, 佣金比例: {}%, 奖励金额: {}", 
                    subscriptionAmount, commissionRate.multiply(new BigDecimal("100")), rewardAmount);

            // 4. 创建奖励记录
            String triggerEvent = String.format("会员订阅成功 - 等级%d - 订单:%s", membershipLevel, orderId);
            AicgUserReferralReward reward = userReferralRewardService.createReward(
                    referral.getId(),
                    referrerId,
                    subscriberUserId,
                    1, // 奖励类型：1-佣金奖励
                    rewardAmount,
                    triggerEvent,
                    "system"
            );

            // 5. 更新邀请人的佣金余额
            updateReferrerCommissionBalance(referrerId, rewardAmount);

            // 6. 更新邀请关系状态（如果是首次奖励）
            if (referral.getStatus() == 1) {
                referral.setFirstRechargeTime(new Date());
                referral.setFirstRechargeAmount(subscriptionAmount);
                referral.setStatus(2); // 已确认
                userReferralService.updateById(referral);
            }

            log.info("✅ 会员订阅奖励处理完成 - 奖励ID: {}, 金额: {}", reward.getId(), rewardAmount);
            return true;

        } catch (Exception e) {
            log.error("❌ 处理会员订阅奖励失败 - 用户: {}, 金额: {}", subscriberUserId, subscriptionAmount, e);
            throw e; // 重新抛出异常，触发事务回滚
        }
    }

    /**
     * 计算邀请人的佣金比例
     * 根据邀请人的会员等级和邀请人数确定佣金比例
     * 
     * @param referrerId 邀请人ID
     * @return 佣金比例（小数形式，如0.30表示30%）
     */
    private BigDecimal calculateCommissionRate(String referrerId) {
        try {
            // 1. 获取邀请人的会员类型
            String memberTypeSql = "SELECT r.role_code FROM sys_user_role ur " +
                    "JOIN sys_role r ON ur.role_id = r.id " +
                    "WHERE ur.user_id = ? AND r.role_code IN ('VIP', 'SVIP')";
            
            List<Map<String, Object>> memberTypeResult = jdbcTemplate.queryForList(memberTypeSql, referrerId);
            String memberType = "NORMAL"; // 默认普通用户
            
            if (!memberTypeResult.isEmpty()) {
                String roleCode = (String) memberTypeResult.get(0).get("role_code");
                if ("VIP".equals(roleCode) || "SVIP".equals(roleCode)) {
                    memberType = roleCode;
                }
            }

            // 2. 获取邀请人的有效邀请数量
            String inviteCountSql = "SELECT COUNT(*) as invite_count FROM aicg_user_referral WHERE referrer_id = ?";
            List<Map<String, Object>> countResult = jdbcTemplate.queryForList(inviteCountSql, referrerId);
            int inviteCount = 0;
            if (!countResult.isEmpty()) {
                inviteCount = ((Number) countResult.get(0).get("invite_count")).intValue();
            }

            // 3. 根据会员类型和邀请数量查询佣金比例
            String rateSql = "SELECT base_rate FROM aicg_commission_level_config " +
                    "WHERE member_type = ? AND invite_requirement <= ? " +
                    "ORDER BY invite_requirement DESC LIMIT 1";
            
            List<Map<String, Object>> rateResult = jdbcTemplate.queryForList(rateSql, memberType, inviteCount);
            
            if (rateResult.isEmpty()) {
                log.warn("⚠️ 未找到邀请人 {} 的佣金配置 - 类型: {}, 邀请数: {}", referrerId, memberType, inviteCount);
                return BigDecimal.ZERO;
            }

            BigDecimal rate = (BigDecimal) rateResult.get(0).get("base_rate");
            log.info("📊 邀请人 {} 佣金配置 - 类型: {}, 邀请数: {}, 佣金比例: {}%", 
                    referrerId, memberType, inviteCount, rate.multiply(new BigDecimal("100")));
            
            return rate;

        } catch (Exception e) {
            log.error("❌ 计算邀请人 {} 佣金比例失败", referrerId, e);
            return BigDecimal.ZERO;
        }
    }

    /**
     * 更新邀请人的佣金余额
     * 
     * @param referrerId 邀请人ID
     * @param rewardAmount 奖励金额
     */
    private void updateReferrerCommissionBalance(String referrerId, BigDecimal rewardAmount) {
        try {
            String updateSql = "UPDATE aicg_user_profile SET " +
                    "total_commission = COALESCE(total_commission, 0) + ?, " +
                    "available_commission = COALESCE(available_commission, 0) + ?, " +
                    "update_time = ? " +
                    "WHERE user_id = ?";
            
            int updated = jdbcTemplate.update(updateSql, rewardAmount, rewardAmount, new Date(), referrerId);
            
            if (updated > 0) {
                log.info("💰 更新邀请人 {} 佣金余额成功 - 增加金额: {}", referrerId, rewardAmount);
            } else {
                log.warn("⚠️ 更新邀请人 {} 佣金余额失败 - 用户不存在", referrerId);
            }
            
        } catch (Exception e) {
            log.error("❌ 更新邀请人 {} 佣金余额失败", referrerId, e);
            throw e;
        }
    }

    /**
     * 充值成功触发奖励（预留接口）
     * 
     * @param rechargerUserId 充值用户ID
     * @param rechargeAmount 充值金额
     * @param orderId 订单ID
     * @return 是否成功触发奖励
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean triggerRechargeReward(String rechargerUserId, BigDecimal rechargeAmount, String orderId) {
        // TODO: 如果后续需要充值奖励，可以在这里实现
        log.info("📝 充值奖励功能暂未开启 - 用户: {}, 金额: {}", rechargerUserId, rechargeAmount);
        return true;
    }

    /**
     * 插件购买成功触发奖励（预留接口）
     * 
     * @param buyerUserId 购买用户ID
     * @param purchaseAmount 购买金额
     * @param pluginId 插件ID
     * @param orderId 订单ID
     * @return 是否成功触发奖励
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean triggerPluginPurchaseReward(String buyerUserId, BigDecimal purchaseAmount, 
                                             String pluginId, String orderId) {
        // TODO: 如果后续需要插件购买奖励，可以在这里实现
        log.info("📝 插件购买奖励功能暂未开启 - 用户: {}, 金额: {}, 插件: {}", 
                buyerUserId, purchaseAmount, pluginId);
        return true;
    }
}
