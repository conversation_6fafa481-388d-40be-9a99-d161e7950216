{"openapi": "3.0.0", "info": {"title": "剪映小助手_图生视频箱 - 查询视频生成任务", "description": "查询智界AIGC图生视频任务的状态和结果", "version": "1.0.0"}, "servers": [{"url": "https://www.aigcview.com"}], "paths": {"/jeecg-boot/api/coze/video/query-task": {"post": {"summary": "查询视频生成任务", "description": "根据任务ID查询视频生成任务的状态、进度和结果", "operationId": "query_video_task", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"task_id": {"type": "string", "description": "视频生成任务ID（必填）", "example": "task_abc123def456"}, "wait_cnt": {"type": "integer", "description": "轮询等待次数（可选），范围1-10。当任务未完成时，会间隔3秒重复查询，直到任务完成或达到指定次数", "example": 3, "default": 1, "minimum": 1, "maximum": 10}}, "required": ["task_id"], "additionalProperties": false}}}}, "responses": {"200": {"description": "成功查询视频生成任务", "content": {"application/json": {"schema": {"type": "object", "properties": {"task_id": {"type": "string", "description": "视频生成任务ID", "example": "cgt-20250716135931-jv2ts"}, "status": {"type": "string", "description": "任务状态：pending(等待中)、running(执行中)、completed(已完成)、failed(失败)", "example": "completed", "enum": ["pending", "running", "completed", "failed", "cancelled"]}, "message": {"type": "string", "description": "状态描述信息", "example": "视频生成完成"}, "video_url": {"type": "string", "description": "生成的视频下载链接（仅当status为completed时返回）", "example": "https://ark-content-generation-cn-beijing.tos-cn-beijing.volces.com/video.mp4"}, "failure_reason": {"type": "string", "description": "失败原因（仅当status为failed时返回）", "example": "视频生成失败：提示词包含不当内容"}, "created_at": {"type": "string", "description": "任务创建时间（ISO 8601格式）", "example": "2025-01-15T10:30:00Z"}, "updated_at": {"type": "string", "description": "任务最后更新时间（ISO 8601格式）", "example": "2025-01-15T10:32:30Z"}}, "required": ["task_id", "status", "message"]}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "description": "错误信息", "example": "查询视频生成任务失败: 任务ID格式无效"}}, "required": ["error"]}}}}, "404": {"description": "任务不存在", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "description": "错误信息", "example": "查询视频生成任务失败: 任务不存在或已被删除"}}, "required": ["error"]}}}}, "500": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "description": "错误信息", "example": "查询视频生成任务失败: 服务器内部错误"}}, "required": ["error"]}}}}}}}}}