# 剪映小助手_智界音频库

> 扣子平台插件 - 智界音频库

## 📖 插件简介

剪映小助手_智界音频库是专为音频内容创作者设计的音频资源搜索工具集合，提供背景音乐和音效的智能搜索功能，帮助用户快速找到合适的音频素材。

## 🛠️ 工具列表

本插件包含以下工具，每个工具都有对应的JSON定义文件：

### 核心工具
- **背景音乐搜索** - 搜索剪映背景音乐库，支持关键词搜索和类型筛选
- **背景音效搜索** - 搜索剪映背景音效库，支持关键词搜索

## 📁 文件结构

```
智界音频库/
├── README.md           # 插件说明文档
├── bgm_search.json     # 背景音乐搜索工具定义
└── sound_search.json   # 背景音效搜索工具定义
```

## 🔧 使用说明

1. 将JSON文件导入到扣子平台
2. 配置访问密钥（使用固定值：JianyingAPI_2025_SecureAccess_AigcView）
3. 在工作流中调用相应工具
4. 获取音频搜索结果和下载链接

## 🔑 API配置

- **访问密钥**: JianyingAPI_2025_SecureAccess_AigcView（固定值）
- **Base URL**: https://www.aigcview.com
- **费用说明**: 音频搜索功能免费使用

## 📝 开发规范

- 每个工具都有独立的JSON定义文件
- 遵循扣子平台的工具定义规范
- 输入输出参数清晰明确
- 提供详细的工具描述和使用说明
- 操作ID使用下划线命名格式
- 参数名不使用前缀，保持简洁

## 🎯 功能特点

- **智能搜索**: 支持关键词搜索，快速定位目标音频
- **类型筛选**: 背景音乐支持VIP/免费类型筛选
- **丰富结果**: 返回音频标题、URL、时长等完整信息
- **高性能**: 优化的API调用，快速响应
- **标准化**: 遵循OpenAPI 3.0.0规范
- **免费使用**: 无需付费，开箱即用

## 🎵 支持的音频类型

### 背景音乐
- **全部音乐**: 包含VIP和免费音乐
- **VIP音乐**: 需要会员权限的高质量音乐
- **免费音乐**: 无需会员即可使用的音乐

### 背景音效
- **环境音效**: 自然环境、城市环境等
- **动作音效**: 点击、打字、脚步声等
- **情感音效**: 欢快、悲伤、紧张等氛围音效

## 📋 参数说明

### 背景音乐搜索参数
- **access_key** (必填): 访问密钥，固定值：JianyingAPI_2025_SecureAccess_AigcView
- **keyword** (必填): 搜索关键词，如"我的天空"
- **type** (可选): 音乐类型筛选，可选值：0-默认所有，1-VIP，2-免费，默认0

### 背景音效搜索参数
- **access_key** (必填): 访问密钥，固定值：JianyingAPI_2025_SecureAccess_AigcView
- **keyword** (必填): 搜索关键词，如"打字声"

## 📊 返回数据格式

### 背景音乐搜索返回
```json
{
  "data": [
    {
      "title": "音乐标题",
      "bgm_url": "音乐下载链接",
      "bgm_urls": ["音乐链接列表"],
      "duration": 120000000,
      "timelines": [
        {
          "start": 0,
          "end": 120000000
        }
      ]
    }
  ],
  "message": "成功搜索背景音乐: 关键词",
  "success": true
}
```

### 背景音效搜索返回
```json
{
  "data": [
    {
      "title": "音效标题",
      "url": "音效下载链接"
    }
  ],
  "message": "成功搜索音效: 关键词",
  "success": true
}
```

## 🚀 使用示例

### 搜索背景音乐
```json
{
  "access_key": "JianyingAPI_2025_SecureAccess_AigcView",
  "keyword": "我的天空",
  "type": "0"
}
```

### 搜索背景音效
```json
{
  "access_key": "JianyingAPI_2025_SecureAccess_AigcView",
  "keyword": "打字声"
}
```

## ⚠️ 注意事项

1. **访问密钥**: 必须使用固定的访问密钥，不可修改
2. **搜索关键词**: 建议使用中文关键词，搜索效果更佳
3. **音频格式**: 返回的音频链接通常为MP3格式
4. **使用限制**: 音频搜索功能免费，但请合理使用避免频繁调用
5. **版权说明**: 使用音频素材时请注意版权问题

## 🔄 更新日志

- **v1.0.0** (2025-01-16)
  - 初始版本发布
  - 支持背景音乐搜索
  - 支持背景音效搜索
  - 遵循最新的扣子插件开发规范

## 📞 技术支持

- **官方网站**: https://www.aigcview.com
- **API文档**: https://www.aigcview.com/jeecg-boot/doc.html
- **技术支持**: 智界Aigc开发团队

---

*智界音频库 - 让音频创作更简单*
