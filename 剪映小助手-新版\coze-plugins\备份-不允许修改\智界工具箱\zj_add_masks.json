{"openapi": "3.0.0", "info": {"title": "剪映小助手_智界工具箱 - 增加蒙版", "description": "增加蒙版", "version": "1.0.0"}, "servers": [{"url": "https://www.aigcview.com"}], "paths": {"/jeecg-boot/api/jianying/add_masks": {"post": {"summary": "增加蒙版", "description": "增加蒙版", "operationId": "addMasks_zj", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"access_key": {"type": "string", "description": "访问密钥（必填）", "example": "JianyingAPI_2025_SecureAccess_AigcView", "default": "JianyingAPI_2025_SecureAccess_AigcView"}, "zj_height": {"type": "integer", "description": "对应剪映的高度", "example": 1080}, "zj_name": {"type": "string", "description": "蒙板类型，'线性'，'镜面'，'圆形'，'矩形'，'爱心'，'星形'（必填）", "enum": ["线性", "镜面", "圆形", "矩形", "爱心", "星形"], "example": "圆形"}, "zj_roundCorner": {"type": "integer", "description": "矩形的圆角", "example": 10}, "zj_segment_ids": {"type": "array", "description": "addVideos_zj或者addImages_zj的返回值（必填）", "items": {"type": "string"}, "example": ["segment_123", "segment_456"]}, "zj_X": {"type": "integer", "description": "对应剪映的X位置", "example": 100}, "zj_Y": {"type": "integer", "description": "对应剪映的Y位置", "example": 200}, "zj_draft_url": {"type": "string", "description": "草稿地址，使用createDraft_zj输出的draft_url即可（必填）", "example": "https://example.com/draft/123"}, "zj_feather": {"type": "integer", "description": "羽化，范围0-100", "minimum": 0, "maximum": 100, "example": 20}, "zj_invert": {"type": "boolean", "description": "是否反转", "example": false}, "zj_rotation": {"type": "integer", "description": "旋转角度，范围0-360", "minimum": 0, "maximum": 360, "example": 45}, "zj_width": {"type": "integer", "description": "对应剪映的宽度", "example": 1920}}, "required": ["access_key", "zj_name", "zj_segment_ids", "zj_draft_url"], "additionalProperties": false}}}}, "responses": {"200": {"description": "成功添加蒙版", "content": {"application/json": {"schema": {"type": "object", "properties": {"draft_url": {"type": "string", "description": "更新后的草稿地址"}, "message": {"type": "string", "description": "导入提示信息"}}, "required": ["draft_url", "message"]}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "description": "错误信息"}}, "required": ["error"]}}}}, "500": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "description": "错误信息"}}, "required": ["error"]}}}}}}}}}