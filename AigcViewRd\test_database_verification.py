#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库验证脚本
验证插件调用后数据库中的统计信息是否正确更新
"""

import mysql.connector
import requests
import json
import time

# 数据库配置
DB_CONFIG = {
    'host': '127.0.0.1',
    'user': 'root',
    'password': 'root',
    'database': 'AigcView',
    'charset': 'utf8mb4'
}

# API配置
BASE_URL = "http://localhost:8080"
API_KEY = "ak_afc0fd5b3fbf4265af92280630b37b91"

def get_db_connection():
    """获取数据库连接"""
    try:
        conn = mysql.connector.connect(**DB_CONFIG)
        return conn
    except Exception as e:
        print(f"数据库连接失败: {e}")
        return None

def get_current_stats():
    """获取当前的统计数据"""
    conn = get_db_connection()
    if not conn:
        return None
    
    try:
        cursor = conn.cursor(dictionary=True)
        
        # 查询用户余额、插件统计、创作者统计
        query = """
        SELECT
            u.nickname, u.account_balance, u.total_consumption,
            p.plubname, p.income, p.usernum, p.neednum,
            a.authorname, a.plubusenum, a.total_income
        FROM aicg_user_profile u, aigc_plub_shop p, aigc_plub_author a
        WHERE u.api_key = %s
        AND p.plubname = %s
        AND p.plubwrite = a.id
        """
        
        cursor.execute(query, (API_KEY, "小红书发布"))
        result = cursor.fetchone()
        
        cursor.close()
        conn.close()
        
        return result
        
    except Exception as e:
        print(f"查询数据库异常: {e}")
        if conn:
            conn.close()
        return None

def call_plugin_api():
    """调用插件API"""
    url = f"{BASE_URL}/api/aigc/verify-apikey"
    data = {
        "apiKey": API_KEY,
        "pluginKey": "xiaohongshufabu"
    }
    
    try:
        response = requests.post(url, data=data)
        result = response.json()
        return result
    except Exception as e:
        print(f"API调用异常: {e}")
        return None

def main():
    """主测试函数"""
    print("=== 插件调用数据库验证测试 ===")
    
    # 1. 获取调用前的数据
    print("\n1. 获取调用前的统计数据...")
    stats_before = get_current_stats()
    if not stats_before:
        print("❌ 无法获取调用前的数据")
        return
    
    print("调用前数据:")
    print(f"  用户余额: {stats_before['account_balance']}")
    print(f"  用户累计消费: {stats_before['total_consumption']}")
    print(f"  插件收益: {stats_before['income']}")
    print(f"  插件调用次数: {stats_before['usernum']}")
    print(f"  创作者使用总数: {stats_before['plubusenum']}")
    print(f"  创作者累计收益: {stats_before['total_income']}")
    
    # 2. 调用插件API
    print("\n2. 调用插件API...")
    api_result = call_plugin_api()
    if not api_result:
        print("❌ API调用失败")
        return
    
    print(f"API响应: {json.dumps(api_result, indent=2, ensure_ascii=False)}")
    
    if not api_result.get("success"):
        print("❌ API调用不成功")
        return
    
    # 3. 等待数据库更新
    print("\n3. 等待数据库更新...")
    time.sleep(2)
    
    # 4. 获取调用后的数据
    print("\n4. 获取调用后的统计数据...")
    stats_after = get_current_stats()
    if not stats_after:
        print("❌ 无法获取调用后的数据")
        return
    
    print("调用后数据:")
    print(f"  用户余额: {stats_after['account_balance']}")
    print(f"  用户累计消费: {stats_after['total_consumption']}")
    print(f"  插件收益: {stats_after['income']}")
    print(f"  插件调用次数: {stats_after['usernum']}")
    print(f"  创作者使用总数: {stats_after['plubusenum']}")
    print(f"  创作者累计收益: {stats_after['total_income']}")
    
    # 5. 验证数据变化
    print("\n5. 验证数据变化...")
    
    # 获取插件需要的金额
    need_amount = float(stats_before['neednum'])
    
    # 验证用户余额
    expected_balance = float(stats_before['account_balance']) - need_amount
    actual_balance = float(stats_after['account_balance'])
    if abs(actual_balance - expected_balance) < 0.01:
        print("✅ 用户余额扣减正确")
    else:
        print(f"❌ 用户余额扣减错误，期望: {expected_balance}, 实际: {actual_balance}")

    # 验证用户累计消费
    expected_consumption = float(stats_before['total_consumption']) + need_amount
    actual_consumption = float(stats_after['total_consumption'])
    if abs(actual_consumption - expected_consumption) < 0.01:
        print("✅ 用户累计消费更新正确")
    else:
        print(f"❌ 用户累计消费更新错误，期望: {expected_consumption}, 实际: {actual_consumption}")

    # 验证插件收益
    expected_income = float(stats_before['income']) + need_amount
    actual_income = float(stats_after['income'])
    if abs(actual_income - expected_income) < 0.01:
        print("✅ 插件收益更新正确")
    else:
        print(f"❌ 插件收益更新错误，期望: {expected_income}, 实际: {actual_income}")
    
    # 验证插件调用次数
    expected_usernum = stats_before['usernum'] + 1
    actual_usernum = stats_after['usernum']
    if actual_usernum == expected_usernum:
        print("✅ 插件调用次数更新正确")
    else:
        print(f"❌ 插件调用次数更新错误，期望: {expected_usernum}, 实际: {actual_usernum}")
    
    # 验证创作者使用总数
    expected_author_usage = stats_before['plubusenum'] + 1
    actual_author_usage = stats_after['plubusenum']
    if actual_author_usage == expected_author_usage:
        print("✅ 创作者使用总数更新正确")
    else:
        print(f"❌ 创作者使用总数更新错误，期望: {expected_author_usage}, 实际: {actual_author_usage}")

    # 验证创作者累计收益
    expected_author_income = float(stats_before['total_income']) + need_amount
    actual_author_income = float(stats_after['total_income'])
    if abs(actual_author_income - expected_author_income) < 0.01:
        print("✅ 创作者累计收益更新正确")
    else:
        print(f"❌ 创作者累计收益更新错误，期望: {expected_author_income}, 实际: {actual_author_income}")

    print("\n=== 测试完成 ===")

if __name__ == "__main__":
    main()
