/// <reference types="node" />
import TOSBase, { TosResponse } from '../../base';
import { CreateMultipartUploadInput } from './createMultipartUpload';
import { CompleteMultipartUploadOutput } from './completeMultipartUpload';
import { CancelToken } from 'axios';
import { DataTransferStatus } from '../../../interface';
import { IRateLimiter } from '../../../interface';
export interface UploadFileInput extends CreateMultipartUploadInput {
    /**
     * if the type of `file` is string,
     * `file` represents the file path that will be uploaded
     */
    file: string | File | Blob | Buffer;
    /**
     * default is 20 MB
     *
     * unit: B
     */
    partSize?: number;
    /**
     * the number of request to parallel upload part，default value is 1
     */
    taskNum?: number;
    /**
     * if checkpoint is a string and point to a exist file,
     * the checkpoint record will recover from this file.
     *
     * if checkpoint is a string and point to a directory,
     * the checkpoint will be auto generated,
     * and its name is `{bucketName}_{objectName}.{uploadId}`.
     */
    checkpoint?: string | CheckpointRecord;
    dataTransferStatusChange?: (status: DataTransferStatus) => void;
    /**
     * the feature of pause and continue uploading
     */
    uploadEventChange?: (event: UploadEvent) => void;
    /**
     * the simple progress feature
     * percent is [0, 1]
     */
    progress?: (percent: number, checkpoint: CheckpointRecord) => void;
    /**
     * cancel this upload progress
     */
    cancelToken?: CancelToken;
    /**
     * enable md5 checksum to uploadPart method
     *
     * default: false
     */
    enableContentMD5?: boolean;
    /**
     * unit: bit/s
     * server side traffic limit
     **/
    trafficLimit?: number;
    /**
     * only works for nodejs environment
     */
    rateLimiter?: IRateLimiter;
}
export interface UploadFileOutput extends CompleteMultipartUploadOutput {
}
export declare enum UploadEventType {
    CreateMultipartUploadSucceed = 1,
    CreateMultipartUploadFailed = 2,
    UploadPartSucceed = 3,
    UploadPartFailed = 4,
    UploadPartAborted = 5,
    CompleteMultipartUploadSucceed = 6,
    CompleteMultipartUploadFailed = 7
}
export interface UploadPartInfo {
    partNumber: number;
    partSize: number;
    offset: number;
    etag?: string;
}
export interface UploadEvent {
    type: UploadEventType;
    /**
     * has value when event is failed or aborted
     */
    err?: Error;
    bucket: string;
    key: string;
    uploadId: string;
    checkpointFile?: string;
    uploadPartInfo?: UploadPartInfo;
}
export interface CheckpointRecord {
    bucket: string;
    key: string;
    part_size: number;
    upload_id: string;
    parts_info?: CheckpointRecordPart[];
    file_info?: {
        last_modified: number;
        file_size: number;
    };
}
interface CheckpointRecordPart {
    part_number: number;
    part_size: number;
    offset: number;
    etag: string;
    hash_crc64ecma: string;
    is_completed: boolean;
}
export declare function uploadFile(this: TOSBase, input: UploadFileInput): Promise<TosResponse<UploadFileOutput>>;
export default uploadFile;
