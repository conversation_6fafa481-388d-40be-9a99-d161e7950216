package org.jeecg.modules.jianying.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 剪映API基础请求类
 * 所有剪映API的Request类都应该继承此类
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
@Data
public abstract class BaseJianyingRequest {
    
    /**
     * 访问密钥（必填）
     * 用于验证API调用的合法性
     */
    @ApiModelProperty(value = "访问密钥（必填）", required = true, example = "JianyingAPI_2025_SecureAccess_AigcView")
    @NotBlank(message = "access_key不能为空")
    @JsonProperty("access_key")
    private String accessKey;
    
    /**
     * 获取访问密钥
     * 
     * @return 访问密钥
     */
    public String getAccessKey() {
        return accessKey;
    }
    
    /**
     * 设置访问密钥
     * 
     * @param accessKey 访问密钥
     */
    public void setAccessKey(String accessKey) {
        this.accessKey = accessKey;
    }
    
    /**
     * 验证基础参数
     * 子类可以重写此方法添加额外的验证逻辑
     * 
     * @return true表示验证通过，false表示验证失败
     */
    public boolean validateBase() {
        return accessKey != null && !accessKey.trim().isEmpty();
    }
    
    /**
     * 获取请求摘要信息（用于日志记录）
     * 子类可以重写此方法提供更详细的信息
     * 
     * @return 请求摘要字符串
     */
    public String getSummary() {
        return this.getClass().getSimpleName() + "{accessKey=" + 
               (accessKey != null && accessKey.length() > 10 ? 
                accessKey.substring(0, 10) + "***" : "null") + "}";
    }
    
    /**
     * 清理敏感信息（用于日志输出）
     * 
     * @return 清理后的对象字符串表示
     */
    @Override
    public String toString() {
        return getSummary();
    }
}
