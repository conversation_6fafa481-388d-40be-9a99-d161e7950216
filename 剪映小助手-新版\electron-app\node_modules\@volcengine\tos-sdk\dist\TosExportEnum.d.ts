export declare enum ACLType {
    ACLPrivate = "private",
    ACLPublicRead = "public-read",
    ACLPublicReadWrite = "public-read-write",
    ACLAuthenticatedRead = "authenticated-read",
    ACLBucketOwnerRead = "bucket-owner-read",
    ACLBucketOwnerFullControl = "bucket-owner-full-control",
    ACLBucketOwnerEntrusted = "bucket-owner-entrusted",
    /**
     * @private unstable value for object ACL
     */
    ACLDefault = "default"
}
export declare enum StorageClassType {
    StorageClassStandard = "STANDARD",
    StorageClassIa = "IA",
    StorageClassArchiveFr = "ARCHIVE_FR",
    StorageClassColdArchive = "COLD_ARCHIVE",
    StorageClassIntelligentTiering = "INTELLIGENT_TIERING",
    StorageClassArchive = "ARCHIVE"
}
export declare enum MetadataDirectiveType {
    MetadataDirectiveCopy = "COPY",
    MetadataDirectiveReplace = "REPLACE"
}
export declare enum AzRedundancyType {
    AzRedundancySingleAz = "single-az",
    AzRedundancyMultiAz = "multi-az"
}
export declare enum PermissionType {
    PermissionRead = "READ",
    PermissionWrite = "WRITE",
    PermissionReadAcp = "READ_ACP",
    PermissionWriteAcp = "WRITE_ACP",
    PermissionFullControl = "FULL_CONTROL",
    /**
     * @private unstable value for ACL
     */
    PermissionReadNONLIST = "READ_NON_LIST"
}
export declare enum GranteeType {
    GranteeGroup = "Group",
    GranteeUser = "CanonicalUser"
}
export declare enum CannedType {
    CannedAllUsers = "AllUsers",
    CannedAuthenticatedUsers = "AuthenticatedUsers"
}
export declare enum HttpMethodType {
    HttpMethodGet = "GET",
    HttpMethodPut = "PUT",
    HttpMethodPost = "POST",
    HttpMethodDelete = "DELETE",
    HttpMethodHead = "HEAD"
}
export declare enum StorageClassInheritDirectiveType {
    StorageClassInheritDirectiveDestinationBucket = "DESTINATION_BUCKET",
    StorageClassInheritDirectiveSourceObject = "SOURCE_OBJECT"
}
export declare enum ReplicationStatusType {
    Complete = "COMPLETE",
    Pending = "PENDING",
    Failed = "FAILED",
    Replica = "REPLICA"
}
export declare enum LifecycleStatusType {
    Enabled = "Enabled",
    Disabled = "Disabled"
}
export declare enum RedirectType {
    Mirror = "Mirror",
    Async = "Async"
}
export declare enum StatusType {
    Enabled = "Enabled",
    Disabled = "Disabled"
}
export declare enum TierType {
    TierStandard = "Standard",
    TierExpedited = "Expedited",
    TierBulk = "Bulk"
}
export declare enum VersioningStatusType {
    Enabled = "Enabled",
    Suspended = "Suspended",
    NotSet = "",
    /**
     * @deprecated use `Enabled` instead
     */
    Enable = "Enabled",
    /**
     * @deprecated use `NotSet` instead
     */
    Disable = ""
}
/**
 * @private unstable
 */
export declare enum AccessPointStatusType {
    Ready = "READY",
    Creating = "CREATING",
    Created = "CREATED",
    Deleting = "DELETING"
}
/**
 * @private unstable
 */
export declare enum TransferAccelerationStatusType {
    Activating = "AccelerationActivating",
    Activated = "AccelerationActivated",
    Terminated = "AccelerationTerminated"
}
/**
 * @private unstable
 */
export declare enum MRAPMirrorBackRedirectPolicyType {
    ClosestFirst = "Closest-First",
    LatestFirst = "Latest-First"
}
