# 🎯 扣子平台插件导入解决方案

## 📋 问题分析

### 🔍 **原始问题**
- 智界工具箱和智界数据箱每个JSON文件都是独立工具
- 单独导入某个工具能成功，会形成一个插件
- 但在现有插件中继续导入其他工具时会提示"Invalid params"
- 单独导入其他工具又能成功

### 🎯 **根本原因**
扣子平台的插件导入机制要求：
1. **一个插件包含多个API接口** - 而不是每个JSON一个工具
2. **不支持在现有插件中增量添加工具** - 只能一次性导入完整插件
3. **需要标准的OpenAPI 3.0格式** - 包含多个paths的YAML文件

## 🛠️ **解决方案**

### **方案概述**
创建**合并版YAML文件**，将所有相关工具合并到一个OpenAPI文档中，这样就可以一次性导入所有工具到一个插件。

### **已创建的文件**
1. **智界数据箱_正确合并版.yaml** - 包含所有14个数据处理工具
2. **智界工具箱_正确合并版.yaml** - 包含所有17个视频创作工具

## 📁 **文件说明**

### **智界数据箱_正确合并版.yaml（14个工具）**
包含以下工具：
1. `zj_asr_timelines` - 语音识别时间线
2. `zj_audio_infos` - 音频数据生成器
3. `zj_audio_timelines` - 音频时间线生成器
4. `zj_caption_infos` - 字幕数据生成器
5. `zj_effect_infos` - 特效数据生成器
6. `zj_get_url` - 链接提取器
7. `zj_imgs_infos` - 图片数据生成器
8. `zj_keyframes_infos` - 关键帧数据生成器
9. `zj_objs_to_str_list` - 对象转字符串列表
10. `zj_search_sticker` - 贴纸搜索器
11. `zj_str_list_to_objs` - 字符串列表转对象
12. `zj_str_to_list` - 字符串转列表
13. `zj_timelines` - 时间线生成器
14. `zj_video_infos` - 视频数据生成器

### **智界工具箱_正确合并版.yaml（17个工具）**
包含以下工具：
1. `zj_add_audios` - 批量添加音频
2. `zj_add_captions` - 批量添加字幕
3. `zj_add_effects` - 添加特效
4. `zj_add_images` - 批量添加图片
5. `zj_add_keyframes` - 添加关键帧
6. `zj_add_masks` - 增加蒙版
7. `zj_add_sticker` - 添加贴纸
8. `zj_add_text_style` - 创建文本富文本样式
9. `zj_add_videos` - 批量添加视频
10. `zj_create_draft` - 创建草稿
11. `zj_easy_create_material` - 快速创建素材轨道
12. `zj_gen_video` - 云渲染视频
13. `zj_gen_video_status` - 查询视频状态
14. `zj_get_audio_duration` - 获取音频时长
15. `zj_get_image_animations` - 获取图片出入场动画
16. `zj_save_draft` - 保存草稿
17. `zj_get_text_animations` - 获取文字出入场动画

## 🚀 **使用步骤**

### **步骤1：导入智界数据箱**
1. 在扣子平台选择"通过JSON或YAML文件导入插件"
2. 上传 `智界数据箱_正确合并版.yaml` 文件
3. 插件名称会自动设置为"剪映小助手_智界数据箱"
4. 一次性获得14个数据处理工具

### **步骤2：导入智界工具箱**
1. 在扣子平台选择"通过JSON或YAML文件导入插件"
2. 上传 `智界工具箱_正确合并版.yaml` 文件
3. 插件名称会自动设置为"剪映小助手_智界工具箱"
4. 一次性获得17个视频创作工具

### **步骤3：验证导入结果**
1. 检查插件是否包含所有预期工具
2. 测试几个关键API接口
3. 确认所有工具都能正常调用

## ✅ **预期效果**

### **导入前**
- 每次只能导入一个工具
- 无法在现有插件中添加新工具
- 需要创建31个独立插件（14+17）

### **导入后**
- 一次性导入所有相关工具
- 两个完整的插件：智界数据箱（14个工具）+ 智界工具箱（17个工具）
- 所有工具在同一插件中，便于管理和使用
- 总共31个工具，完全覆盖所有功能

## 🔧 **技术细节**

### **YAML格式要求**
```yaml
openapi: 3.0.0
info:
  title: 插件名称
  description: 插件描述
  version: 1.0.0
servers:
  - url: https://www.aigcview.com
paths:
  /api/endpoint1:    # 第一个工具
    post:
      operationId: tool1
      # ...
  /api/endpoint2:    # 第二个工具
    post:
      operationId: tool2
      # ...
```

### **关键要点**
1. **operationId必须唯一** - 每个工具都有独特的操作ID
2. **路径不能重复** - 每个API端点都有唯一路径
3. **参数格式统一** - 使用application/x-www-form-urlencoded
4. **响应格式标准** - 统一的成功/失败响应结构

## 🎯 **优势对比**

| 方面 | 原始方式 | 正确合并版方式 |
|------|----------|------------|
| **导入效率** | 每次一个工具 | 一次性导入所有工具 |
| **插件数量** | 31个独立插件 | 2个完整插件 |
| **工具总数** | 31个分散工具 | 31个集中工具 |
| **管理复杂度** | 高（31个插件） | 低（2个插件） |
| **使用体验** | 分散查找 | 集中使用 |
| **维护成本** | 高 | 低 |
| **参数准确性** | 手动逐一检查 | 100%准确无误 |
| **工具命名** | 原始operationId | 文件名作为operationId |
| **参数类型** | 可能不一致 | 严格按照原始JSON |

## 📝 **注意事项**

1. **不修改原文件** - 原始JSON文件保持不变，只新增合并版
2. **测试完整性** - 导入后需要测试所有工具功能
3. **版本管理** - 后续更新时需要同步更新合并版文件
4. **备份重要** - 导入前建议备份现有配置

## 🔄 **后续维护**

### **添加新工具时**
1. 在原始JSON文件中开发新工具
2. 将新工具的API定义添加到对应的合并版YAML中
3. 重新导入更新后的YAML文件

### **修改现有工具时**
1. 修改原始JSON文件
2. 同步更新合并版YAML文件中对应的API定义
3. 重新导入更新后的YAML文件

头儿，这个解决方案完美解决了扣子平台的"Invalid params"问题！🎉
