package org.jeecg.modules.demo.websitefeatures.entity;

import java.io.Serializable;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 网站功能特性
 * @Author: jeecg-boot
 * @Date:   2025-06-17
 * @Version: V1.0
 */
@Data
@TableName("aigc_website_features")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="aigc_website_features对象", description="网站功能特性")
public class AigcWebsiteFeatures implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String id;
	/**功能标题*/
	@Excel(name = "功能标题", width = 15)
    @ApiModelProperty(value = "功能标题")
    private String title;
	/**功能描述*/
	@Excel(name = "功能描述", width = 15)
    @ApiModelProperty(value = "功能描述")
    private String description;
	/**图标名称*/
	@Excel(name = "图标名称", width = 15)
    @ApiModelProperty(value = "图标名称")
    private String icon;
	/**功能详情链接*/
	@Excel(name = "功能详情链接", width = 15)
    @ApiModelProperty(value = "功能详情链接")
    private String linkUrl;
	/**排序序号*/
	@Excel(name = "排序序号", width = 15)
    @ApiModelProperty(value = "排序序号")
    private Integer sortOrder;
	/**状态*/
	@Excel(name = "状态", width = 15)
    @ApiModelProperty(value = "状态")
    private String status;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private Date updateTime;
}
