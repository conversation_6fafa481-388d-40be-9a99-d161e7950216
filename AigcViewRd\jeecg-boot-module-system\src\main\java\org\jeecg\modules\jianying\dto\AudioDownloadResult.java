package org.jeecg.modules.jianying.dto;

import lombok.Data;

/**
 * 音频下载结果类
 * 用于封装音频文件下载的结果信息，支持容错处理
 * 
 * <AUTHOR>
 * @date 2025-07-08
 */
@Data
public class AudioDownloadResult {
    
    /** 下载是否成功 */
    private boolean success;
    
    /** 文件名 */
    private String fileName;
    
    /** 下载URL */
    private String downloadUrl;
    
    /** 错误信息（失败时） */
    private String errorMessage;
    
    /** 文件大小（字节） */
    private long fileSize;
    
    /** 下载耗时（毫秒） */
    private long downloadTime;
    
    /** 是否为占位符文件 */
    private boolean isPlaceholder;
    
    /**
     * 创建成功的下载结果
     */
    public static AudioDownloadResult success(String fileName, String downloadUrl, long fileSize, long downloadTime) {
        AudioDownloadResult result = new AudioDownloadResult();
        result.success = true;
        result.fileName = fileName;
        result.downloadUrl = downloadUrl;
        result.fileSize = fileSize;
        result.downloadTime = downloadTime;
        result.isPlaceholder = false;
        return result;
    }
    
    /**
     * 创建占位符的下载结果（用于下载失败时的降级处理）
     */
    public static AudioDownloadResult placeholder(String fileName, String downloadUrl, String errorMessage) {
        AudioDownloadResult result = new AudioDownloadResult();
        result.success = false;
        result.fileName = fileName;
        result.downloadUrl = downloadUrl;
        result.errorMessage = errorMessage;
        result.isPlaceholder = true;
        result.fileSize = 0L;
        result.downloadTime = 0L;
        return result;
    }
    
    /**
     * 创建失败的下载结果
     */
    public static AudioDownloadResult failure(String errorMessage) {
        AudioDownloadResult result = new AudioDownloadResult();
        result.success = false;
        result.errorMessage = errorMessage;
        result.isPlaceholder = false;
        result.fileSize = 0L;
        result.downloadTime = 0L;
        return result;
    }
}
