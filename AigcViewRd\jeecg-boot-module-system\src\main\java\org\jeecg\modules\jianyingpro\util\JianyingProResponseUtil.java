package org.jeecg.modules.jianyingpro.util;

import com.alibaba.fastjson.JSONObject;
import org.jeecg.modules.jianyingpro.enums.JianyingProErrorCode;

/**
 * 超级剪映小助手响应工具类
 * 提供统一的响应格式和错误处理
 * 
 * <AUTHOR>
 * @since 2025-01-01
 */
public class JianyingProResponseUtil {
    
    /**
     * 创建成功响应
     * 
     * @param data 响应数据
     * @param operation 操作名称
     * @return 成功响应
     */
    public static JSONObject success(JSONObject data, String operation) {
        JSONObject response = new JSONObject();
        
        // 如果原始数据已经包含错误信息，直接返回
        if (data.containsKey("error") || data.containsKey("error_code")) {
            return data;
        }
        
        // 添加元数据
        data.put("operation", operation);
        data.put("timestamp", System.currentTimeMillis());
        data.put("request_id", generateRequestId());
        data.put("success", true);
        
        return data;
    }
    
    /**
     * 创建成功响应（简单数据）
     * 
     * @param message 成功消息
     * @param operation 操作名称
     * @return 成功响应
     */
    public static JSONObject success(String message, String operation) {
        JSONObject response = new JSONObject();
        response.put("success", true);
        response.put("message", message);
        response.put("operation", operation);
        response.put("timestamp", System.currentTimeMillis());
        response.put("request_id", generateRequestId());
        
        return response;
    }
    
    /**
     * 创建错误响应
     * 
     * @param errorCode 错误码枚举
     * @return 错误响应
     */
    public static JSONObject error(JianyingProErrorCode errorCode) {
        return error(errorCode, null, null);
    }
    
    /**
     * 创建错误响应
     * 
     * @param errorCode 错误码枚举
     * @param customMessage 自定义错误消息（可选）
     * @return 错误响应
     */
    public static JSONObject error(JianyingProErrorCode errorCode, String customMessage) {
        return error(errorCode, customMessage, null);
    }
    
    /**
     * 创建错误响应
     * 
     * @param errorCode 错误码枚举
     * @param customMessage 自定义错误消息（可选）
     * @param customSolution 自定义解决方案（可选）
     * @return 错误响应
     */
    public static JSONObject error(JianyingProErrorCode errorCode, String customMessage, String customSolution) {
        JSONObject response = new JSONObject();
        response.put("success", false);
        response.put("error_code", errorCode.getCode());
        response.put("error_message", customMessage != null ? customMessage : errorCode.getMessage());
        response.put("error_details", customSolution != null ? customSolution : errorCode.getSolution());
        response.put("http_status", errorCode.getHttpStatus());
        response.put("timestamp", System.currentTimeMillis());
        response.put("request_id", generateRequestId());

        // 为了与基础版保持一致，也添加error字段
        response.put("error", customMessage != null ? customMessage : errorCode.getMessage());

        return response;
    }
    
    /**
     * 创建错误响应（使用错误码字符串）
     * 
     * @param errorCodeStr 错误码字符串
     * @param errorMessage 错误消息
     * @param errorDetails 错误详情
     * @return 错误响应
     */
    public static JSONObject error(String errorCodeStr, String errorMessage, String errorDetails) {
        JianyingProErrorCode errorCode = JianyingProErrorCode.fromCode(errorCodeStr);
        
        JSONObject response = new JSONObject();
        response.put("success", false);
        response.put("error_code", errorCodeStr);
        response.put("error_message", errorMessage);
        response.put("error_details", errorDetails);
        response.put("http_status", errorCode.getHttpStatus());
        response.put("timestamp", System.currentTimeMillis());
        response.put("request_id", generateRequestId());

        // 为了与基础版保持一致，也添加error字段
        response.put("error", errorMessage);

        return response;
    }
    
    /**
     * 转换原始响应为标准格式
     *
     * @param originalResult 原始响应
     * @param operation 操作名称
     * @return 标准格式响应
     */
    public static JSONObject formatResponse(JSONObject originalResult, String operation) {
        if (originalResult == null) {
            return error(JianyingProErrorCode.SYSTEM_ERROR_500, "响应数据为空");
        }

        // 检查是否为错误响应
        if (originalResult.containsKey("error")) {
            // 转换为标准错误格式
            String errorMessage = originalResult.getString("error");
            return error(JianyingProErrorCode.BUSINESS_DRAFT_001,
                operation + "失败: " + errorMessage,
                "请检查输入参数并重试");
        }

        if (originalResult.containsKey("error_code")) {
            // 已经是标准错误格式，直接返回
            return originalResult;
        }

        // 成功响应，直接返回原始数据（不添加额外包装）
        return originalResult;
    }
    
    /**
     * 检查响应是否为错误
     * 
     * @param response 响应对象
     * @return 是否为错误响应
     */
    public static boolean isError(JSONObject response) {
        return response.containsKey("error") || 
               response.containsKey("error_code") || 
               (response.containsKey("success") && !response.getBooleanValue("success"));
    }
    
    /**
     * 获取响应的HTTP状态码
     * 
     * @param response 响应对象
     * @return HTTP状态码
     */
    public static int getHttpStatus(JSONObject response) {
        if (response.containsKey("http_status")) {
            return response.getIntValue("http_status");
        }
        
        if (isError(response)) {
            String errorCode = response.getString("error_code");
            if (errorCode != null) {
                return JianyingProErrorCode.fromCode(errorCode).getHttpStatus();
            }
            return 500; // 默认500
        }
        
        return 200; // 成功默认200
    }
    
    /**
     * 创建参数验证错误响应
     * 
     * @param paramName 参数名
     * @param reason 错误原因
     * @return 参数错误响应
     */
    public static JSONObject paramError(String paramName, String reason) {
        return error(JianyingProErrorCode.PARAM_MISSING_001, 
            "参数 " + paramName + " " + reason, 
            "请检查并提供正确的 " + paramName + " 参数");
    }
    
    /**
     * 创建业务逻辑错误响应
     * 
     * @param operation 操作名称
     * @param reason 错误原因
     * @return 业务错误响应
     */
    public static JSONObject businessError(String operation, String reason) {
        return error(JianyingProErrorCode.BUSINESS_DRAFT_001, 
            operation + "失败: " + reason, 
            "请检查输入参数和系统状态后重试");
    }
    
    /**
     * 创建系统错误响应
     *
     * @param operation 操作名称
     * @param exception 异常信息
     * @return 系统错误响应
     */
    public static JSONObject systemError(String operation, Exception exception) {
        return error(JianyingProErrorCode.SYSTEM_ERROR_500,
            operation + "系统错误: " + exception.getMessage(),
            "系统内部错误，请稍后重试或联系管理员");
    }
    
    /**
     * 创建功能未实现错误响应
     * 
     * @param feature 功能名称
     * @return 未实现错误响应
     */
    public static JSONObject notImplemented(String feature) {
        return error(JianyingProErrorCode.SYSTEM_ERROR_501, 
            feature + "功能暂未实现", 
            "该功能正在开发中，敬请期待");
    }
    
    /**
     * 生成请求ID
     * 
     * @return 请求ID
     */
    private static String generateRequestId() {
        return "jianyingpro_" + System.currentTimeMillis() + "_" + 
               Integer.toHexString((int)(Math.random() * 0xFFFF));
    }
    
    /**
     * 添加调试信息到响应
     * 
     * @param response 响应对象
     * @param debugInfo 调试信息
     * @return 包含调试信息的响应
     */
    public static JSONObject addDebugInfo(JSONObject response, String debugInfo) {
        if (response == null) {
            response = new JSONObject();
        }
        
        // 只在开发环境添加调试信息
        String env = System.getProperty("spring.profiles.active", "dev");
        if ("dev".equals(env) || "test".equals(env)) {
            response.put("debug_info", debugInfo);
            response.put("debug_timestamp", System.currentTimeMillis());
        }
        
        return response;
    }
}
