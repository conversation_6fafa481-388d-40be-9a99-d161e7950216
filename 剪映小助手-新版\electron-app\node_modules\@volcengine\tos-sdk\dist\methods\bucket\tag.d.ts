import TOSBase, { TosResponse } from '../base';
interface TagSet {
    Tags: {
        Key: string;
        Value: string;
    }[];
}
export interface PutBucketTaggingInput {
    bucket?: string;
    tagging: {
        TagSet: TagSet;
    };
}
export interface GetBucketTaggingInput {
    bucket: string;
}
export interface GetBucketTaggingOutput {
    TagSet: TagSet;
}
/**
 * @private unstable method
 */
export declare function putBucketTagging(this: TOSBase, input: PutBucketTaggingInput): Promise<TosResponse<unknown>>;
/**
 * @private unstable method
 */
export declare function getBucketTagging(this: TOSBase, { bucket }: GetBucketTaggingInput): Promise<TosResponse<GetBucketTaggingOutput>>;
export interface DeleteBucketTaggingInput {
    bucket: string;
}
/**
 * @private unstable method
 */
export declare function deleteBucketTagging(this: TOSBase, { bucket }: DeleteBucketTaggingInput): Promise<TosResponse<unknown>>;
export {};
