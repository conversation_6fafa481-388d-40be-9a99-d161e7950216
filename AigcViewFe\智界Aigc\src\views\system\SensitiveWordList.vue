<template>
  <a-card :bordered="false">
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline" @keyup.enter.native="searchQuery">
        <a-row :gutter="24">
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="敏感词">
              <a-input placeholder="请输入敏感词" v-model="queryParam.word"></a-input>
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="分类">
              <j-dict-select-tag placeholder="请选择分类" v-model="queryParam.category" dictCode="sensitive_word_category"/>
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="级别">
              <j-dict-select-tag placeholder="请选择级别" v-model="queryParam.level" dictCode="sensitive_word_level"/>
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="状态">
              <j-dict-select-tag placeholder="请选择状态" v-model="queryParam.status" dictCode="valid_status"/>
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <span class="table-page-search-submitButtons" style="display: flex;white-space: nowrap; overflow: hidden;">
              <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
              <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <!-- 查询区域-END -->

    <!-- 操作按钮区域 -->
    <div class="table-operator">
      <a-button @click="handleAdd" type="primary" icon="plus" v-has="'sensitive:word:add'">新增</a-button>
      <a-button type="primary" icon="download" @click="handleExportXls('敏感词管理')" v-has="'sensitive:word:export'">导出</a-button>
      <a-button type="primary" icon="import" @click="handleImportExcel" v-has="'sensitive:word:import'">导入</a-button>
      <a-button type="primary" icon="cloud-download" @click="handleImportFromHoubb" v-has="'sensitive:word:import'">从houbb库导入</a-button>
      <a-button type="primary" icon="experiment" @click="handleSensitiveTest">敏感词测试</a-button>
      <a-button type="primary" icon="bar-chart" @click="handleStatistics" v-has="'sensitive:word:statistics'">统计分析</a-button>
      <a-button type="default" icon="reload" @click="handleRefreshCache">刷新缓存</a-button>
      <a-dropdown v-if="selectedRowKeys.length > 0">
        <a-menu slot="overlay">
          <a-menu-item key="1" @click="batchDel" v-has="'sensitive:word:deleteBatch'">
            <a-icon type="delete"/>删除
          </a-menu-item>
        </a-menu>
        <a-button style="margin-left: 8px">
          批量操作 <a-icon type="down" />
        </a-button>
      </a-dropdown>
    </div>

    <!-- table区域-begin -->
    <div>
      <div class="ant-alert ant-alert-info" style="margin-bottom: 16px;">
        <i class="anticon anticon-info-circle ant-alert-icon"></i> 已选择 <a style="font-weight: 600">{{ selectedRowKeys.length }}</a> 项
        <a style="margin-left: 24px" @click="onClearSelected">清空</a>
      </div>

      <a-table
        ref="table"
        size="middle"
        :scroll="{x:true}"
        bordered
        rowKey="id"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="ipagination"
        :loading="loading"
        :rowSelection="rowSelection"
        class="j-table-force-nowrap"
        @change="handleTableChange">

        <template slot="htmlSlot" slot-scope="text">
          <div v-html="text"></div>
        </template>
        <template slot="imgSlot" slot-scope="text">
          <span v-if="!text" style="font-size: 12px;font-style: italic;">无图片</span>
          <img v-else :src="getImgView(text)" height="25px" alt="" style="max-width:80px;font-size: 12px;font-style: italic;"/>
        </template>
        <template slot="fileSlot" slot-scope="text">
          <span v-if="!text" style="font-size: 12px;font-style: italic;">无文件</span>
          <a-button
            v-else
            :ghost="true"
            type="primary"
            icon="download"
            size="small"
            @click="downloadFile(text)">
            下载
          </a-button>
        </template>

        <span slot="action" slot-scope="text, record">
          <a @click="handleEdit(record)" v-has="'sensitive:word:edit'">编辑</a>
          <a-divider type="vertical" />
          <a-dropdown>
            <a class="ant-dropdown-link">更多 <a-icon type="down" /></a>
            <a-menu slot="overlay">
              <a-menu-item>
                <a @click="handleDetail(record)">详情</a>
              </a-menu-item>
              <a-menu-item>
                <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)" v-has="'sensitive:word:delete'">
                  <a>删除</a>
                </a-popconfirm>
              </a-menu-item>
            </a-menu>
          </a-dropdown>
        </span>

      </a-table>
    </div>

    <!-- 添加/修改弹窗 -->
    <sensitive-word-modal ref="modalForm" @ok="modalFormOk"></sensitive-word-modal>
    <!-- 敏感词测试弹窗 -->
    <sensitive-word-test-modal ref="testModal"></sensitive-word-test-modal>
    <!-- 统计分析弹窗 -->
    <sensitive-word-statistics-modal ref="statisticsModal"></sensitive-word-statistics-modal>
    <!-- 导入弹窗 -->
    <j-import-modal ref="importModal" :url="url.importExcelUrl" @ok="importModalOk"></j-import-modal>
  </a-card>
</template>

<script>

  import '@/assets/less/TableExpand.less'
  import { mixinDevice } from '@/utils/mixin'
  import { JeecgListMixin } from '@/mixins/JeecgListMixin'
  import SensitiveWordModal from './modules/SensitiveWordModal'
  import SensitiveWordTestModal from './modules/SensitiveWordTestModal'
  import SensitiveWordStatisticsModal from './modules/SensitiveWordStatisticsModal'
  import {filterMultiDictText} from '@/components/dict/JDictSelectUtil'
  import { getSensitiveWordList, deleteSensitiveWord, batchDeleteSensitiveWord, getExportUrl, getImportUrl, importFromHoubb, refreshSensitiveWordCache } from '@/api/system/sensitiveWord'
  import { filterObj } from '@/utils/util'
  import pick from 'lodash.pick'

  export default {
    name: 'SensitiveWordList',
    mixins:[JeecgListMixin, mixinDevice],
    components: {
      SensitiveWordModal,
      SensitiveWordTestModal,
      SensitiveWordStatisticsModal
    },
    data () {
      return {
        description: '敏感词管理页面',
        // 表头
        columns: [
          {
            title: '#',
            dataIndex: '',
            key:'rowIndex',
            width:60,
            align:"center",
            customRender:function (t,r,index) {
              return parseInt(index)+1;
            }
          },
          {
            title:'敏感词',
            align:"center",
            dataIndex: 'word'
          },
          {
            title:'分类',
            align:"center",
            dataIndex: 'category',
            customRender: (text) => {
              return filterMultiDictText(this.dictOptions['sensitive_word_category'], text)
            }
          },
          {
            title:'级别',
            align:"center",
            dataIndex: 'level',
            customRender: (text) => {
              return filterMultiDictText(this.dictOptions['sensitive_word_level'], text)
            }
          },
          {
            title:'状态',
            align:"center",
            dataIndex: 'status',
            customRender: (text) => {
              return this.$filterMultiDictText(this.dictOptions['valid_status'], text)
            }
          },
          {
            title:'命中次数',
            align:"center",
            dataIndex: 'hitCount',
            sorter: true
          },
          {
            title:'来源',
            align:"center",
            dataIndex: 'source'
          },
          {
            title: '创建时间',
            align:"center",
            dataIndex: 'createTime',
            sorter: true
          },
          {
            title: '操作',
            dataIndex: 'action',
            align:"center",
            fixed:"right",
            width:147,
            scopedSlots: { customRender: 'action' }
          }
        ],
        url: {
          list: "/sys/sensitiveWord/list",
          delete: "/sys/sensitiveWord/delete",
          deleteBatch: "/sys/sensitiveWord/deleteBatch",
          exportXlsUrl: "/sys/sensitiveWord/exportXls",
          importExcelUrl: "/sys/sensitiveWord/importExcel",
        },
        dictOptions:{},
        superFieldList:[],
      }
    },
    created() {
      this.getSuperFieldList();
      this.initDictConfig();
    },
    computed: {
      importExcelUrl: function(){
        return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`;
      },
    },
    methods: {
      initDictConfig(){
        // 初始化字典配置
        this.$initDictOptions('sensitive_word_category').then((res) => {
          if (res.success) {
            this.$set(this.dictOptions, 'sensitive_word_category', res.result)
          }
        })

        this.$initDictOptions('sensitive_word_level').then((res) => {
          if (res.success) {
            this.$set(this.dictOptions, 'sensitive_word_level', res.result)
          }
        })

        this.$initDictOptions('valid_status').then((res) => {
          if (res.success) {
            this.$set(this.dictOptions, 'valid_status', res.result)
          }
        })
      },
      getSuperFieldList(){
        let fieldList=[];
        fieldList.push({type:'string',value:'word',text:'敏感词',dictCode:''})
        fieldList.push({type:'string',value:'category',text:'分类',dictCode:'sensitive_word_category'})
        fieldList.push({type:'int',value:'level',text:'级别',dictCode:'sensitive_word_level'})
        fieldList.push({type:'int',value:'status',text:'状态',dictCode:'valid_status'})
        fieldList.push({type:'int',value:'hitCount',text:'命中次数',dictCode:''})
        fieldList.push({type:'string',value:'source',text:'来源',dictCode:''})
        fieldList.push({type:'string',value:'remark',text:'备注',dictCode:''})
        this.superFieldList = fieldList
      },
      handleAdd: function () {
        this.$refs.modalForm.add();
        this.$refs.modalForm.title = "新增敏感词";
      },
      handleEdit: function (record) {
        this.$refs.modalForm.edit(record);
        this.$refs.modalForm.title = "编辑敏感词";
      },
      handleDetail: function (record) {
        this.$refs.modalForm.detail(record);
        this.$refs.modalForm.title = "敏感词详情";
      },
      handleSensitiveTest: function () {
        this.$refs.testModal.show();
      },
      handleStatistics: function () {
        this.$refs.statisticsModal.show();
      },
      handleImportFromHoubb: function () {
        let that = this;
        this.$confirm({
          title: "确认导入",
          content: "确定要从houbb库导入敏感词吗？这可能需要一些时间。",
          onOk: function () {
            that.confirmImportFromHoubb();
          }
        });
      },
      confirmImportFromHoubb: function () {
        let that = this;
        this.$message.loading('正在导入，请稍候...', 0);
        importFromHoubb().then((res) => {
          this.$message.destroy();
          if (res.success) {
            this.$message.success(res.message);
            that.loadData();
          } else {
            this.$message.error(res.message);
          }
        }).catch((error) => {
          this.$message.destroy();
          this.$message.error('导入失败：' + error.message);
        });
      },
      handleRefreshCache: function () {
        let that = this;
        this.$confirm({
          title: "确认刷新",
          content: "确定要刷新敏感词缓存吗？",
          onOk: function () {
            that.confirmRefreshCache();
          }
        });
      },
      confirmRefreshCache: function () {
        let that = this;
        refreshSensitiveWordCache().then((res) => {
          if (res.success) {
            this.$message.success(res.message);
          } else {
            this.$message.error(res.message);
          }
        }).catch((error) => {
          this.$message.error('刷新缓存失败：' + error.message);
        });
      },
      getQueryParams() {
        //获取查询条件
        let sqp = {}
        if(this.superQueryParams){
          sqp['superQueryParams']=encodeURI(this.superQueryParams)
          sqp['superQueryMatchType'] = this.superQueryMatchType
        }
        var param = Object.assign(sqp, this.queryParam, this.isorter ,this.filters);
        param.field = this.getQueryField();
        param.pageNo = this.ipagination.current;
        param.pageSize = this.ipagination.pageSize;
        return filterObj(param);
      },
      getQueryField() {
        //TODO 字段权限控制
        var str = "id,";
        this.columns.forEach(function (value) {
          str += "," + value.dataIndex;
        });
        return str;
      },

    }
  }
</script>
<style scoped>
  @import '~@assets/less/common.less';
</style>
