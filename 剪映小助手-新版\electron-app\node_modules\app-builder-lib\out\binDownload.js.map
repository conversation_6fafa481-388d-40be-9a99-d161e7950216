{"version": 3, "file": "binDownload.js", "sourceRoot": "", "sources": ["../src/binDownload.ts"], "names": [], "mappings": ";;;AAAA,+CAAgD;AAEhD,MAAM,gBAAgB,GAAG,IAAI,GAAG,EAA2B,CAAA;AAE3D,SAAgB,QAAQ,CAAC,GAAW,EAAE,MAAc,EAAE,QAAwB;IAC5E,MAAM,IAAI,GAAG,CAAC,UAAU,EAAE,OAAO,EAAE,GAAG,EAAE,UAAU,EAAE,MAAM,CAAC,CAAA;IAC3D,IAAI,QAAQ,IAAI,IAAI,EAAE,CAAC;QACrB,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAA;IACjC,CAAC;IACD,OAAO,IAAA,gCAAiB,EAAC,IAAI,CAAiB,CAAA;AAChD,CAAC;AAND,4BAMC;AAED,SAAgB,mBAAmB,CAAC,IAAY,EAAE,OAAe,EAAE,cAAsB,EAAE,QAAgB;IACzG,MAAM,OAAO,GAAG,GAAG,IAAI,IAAI,OAAO,EAAE,CAAA;IACpC,OAAO,MAAM,CAAC,OAAO,EAAE,cAAc,EAAE,QAAQ,CAAC,CAAA;AAClD,CAAC;AAHD,kDAGC;AAED,SAAgB,aAAa,CAAC,IAAY,EAAE,OAAe,EAAE,QAAgB;IAC3E,MAAM,OAAO,GAAG,GAAG,IAAI,IAAI,OAAO,EAAE,CAAA;IACpC,IAAI,GAAW,CAAA;IACf,IAAI,OAAO,CAAC,GAAG,CAAC,+CAA+C,EAAE,CAAC;QAChE,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC,+CAA+C,GAAG,GAAG,GAAG,OAAO,GAAG,KAAK,CAAA;IAC3F,CAAC;SAAM,CAAC;QACN,MAAM,OAAO,GACX,OAAO,CAAC,GAAG,CAAC,2CAA2C;YACvD,OAAO,CAAC,GAAG,CAAC,2CAA2C;YACvD,OAAO,CAAC,GAAG,CAAC,mDAAmD;YAC/D,OAAO,CAAC,GAAG,CAAC,gCAAgC;YAC5C,mFAAmF,CAAA;QACrF,MAAM,SAAS,GACb,OAAO,CAAC,GAAG,CAAC,+CAA+C;YAC3D,OAAO,CAAC,GAAG,CAAC,+CAA+C;YAC3D,OAAO,CAAC,GAAG,CAAC,uDAAuD;YACnE,OAAO,CAAC,GAAG,CAAC,oCAAoC;YAChD,OAAO,CAAA;QACT,MAAM,SAAS,GAAG,OAAO,GAAG,KAAK,CAAA;QACjC,GAAG,GAAG,GAAG,OAAO,GAAG,SAAS,IAAI,SAAS,EAAE,CAAA;IAC7C,CAAC;IAED,OAAO,MAAM,CAAC,OAAO,EAAE,GAAG,EAAE,QAAQ,CAAC,CAAA;AACvC,CAAC;AAvBD,sCAuBC;AAED,SAAgB,MAAM,CAAC,IAAY,EAAE,GAAmB,EAAE,QAAwB;IAChF,6DAA6D;IAC7D,MAAM,SAAS,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC,sBAAsB,GAAG,IAAI,EAAE,CAAA;IAChE,IAAI,OAAO,GAAG,gBAAgB,CAAC,GAAG,CAAC,SAAS,CAAC,CAAA,CAAC,6CAA6C;IAE3F,IAAI,OAAO,IAAI,IAAI,EAAE,CAAC;QACpB,OAAO,OAAO,CAAA;IAChB,CAAC;IAED,OAAO,GAAG,QAAQ,CAAC,IAAI,EAAE,GAAG,EAAE,QAAQ,CAAC,CAAA;IACvC,gBAAgB,CAAC,GAAG,CAAC,SAAS,EAAE,OAAO,CAAC,CAAA;IACxC,OAAO,OAAO,CAAA;AAChB,CAAC;AAZD,wBAYC;AAED,SAAS,QAAQ,CAAC,IAAY,EAAE,GAA8B,EAAE,QAAmC;IACjG,MAAM,IAAI,GAAG,CAAC,mBAAmB,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAA;IAClD,IAAI,GAAG,IAAI,IAAI,EAAE,CAAC;QAChB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,CAAC,CAAA;IACzB,CAAC;IACD,IAAI,QAAQ,IAAI,IAAI,EAAE,CAAC;QACrB,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAA;IACjC,CAAC;IACD,OAAO,IAAA,gCAAiB,EAAC,IAAI,CAAC,CAAA;AAChC,CAAC", "sourcesContent": ["import { executeAppBuilder } from \"builder-util\"\n\nconst versionToPromise = new Map<string, Promise<string>>()\n\nexport function download(url: string, output: string, checksum?: string | null): Promise<void> {\n  const args = [\"download\", \"--url\", url, \"--output\", output]\n  if (checksum != null) {\n    args.push(\"--sha512\", checksum)\n  }\n  return executeAppBuilder(args) as Promise<any>\n}\n\nexport function getBinFromCustomLoc(name: string, version: string, binariesLocUrl: string, checksum: string): Promise<string> {\n  const dirName = `${name}-${version}`\n  return getBin(dirName, binariesLocUrl, checksum)\n}\n\nexport function getBinFromUrl(name: string, version: string, checksum: string): Promise<string> {\n  const dirName = `${name}-${version}`\n  let url: string\n  if (process.env.ELECTRON_BUILDER_BINARIES_DOWNLOAD_OVERRIDE_URL) {\n    url = process.env.ELECTRON_BUILDER_BINARIES_DOWNLOAD_OVERRIDE_URL + \"/\" + dirName + \".7z\"\n  } else {\n    const baseUrl =\n      process.env.NPM_CONFIG_ELECTRON_BUILDER_BINARIES_MIRROR ||\n      process.env.npm_config_electron_builder_binaries_mirror ||\n      process.env.npm_package_config_electron_builder_binaries_mirror ||\n      process.env.ELECTRON_BUILDER_BINARIES_MIRROR ||\n      \"https://github.com/electron-userland/electron-builder-binaries/releases/download/\"\n    const middleUrl =\n      process.env.NPM_CONFIG_ELECTRON_BUILDER_BINARIES_CUSTOM_DIR ||\n      process.env.npm_config_electron_builder_binaries_custom_dir ||\n      process.env.npm_package_config_electron_builder_binaries_custom_dir ||\n      process.env.ELECTRON_BUILDER_BINARIES_CUSTOM_DIR ||\n      dirName\n    const urlSuffix = dirName + \".7z\"\n    url = `${baseUrl}${middleUrl}/${urlSuffix}`\n  }\n\n  return getBin(dirName, url, checksum)\n}\n\nexport function getBin(name: string, url?: string | null, checksum?: string | null): Promise<string> {\n  // Old cache is ignored if cache environment variable changes\n  const cacheName = `${process.env.ELECTRON_BUILDER_CACHE}${name}`\n  let promise = versionToPromise.get(cacheName) // if rejected, we will try to download again\n\n  if (promise != null) {\n    return promise\n  }\n\n  promise = doGetBin(name, url, checksum)\n  versionToPromise.set(cacheName, promise)\n  return promise\n}\n\nfunction doGetBin(name: string, url: string | undefined | null, checksum: string | null | undefined): Promise<string> {\n  const args = [\"download-artifact\", \"--name\", name]\n  if (url != null) {\n    args.push(\"--url\", url)\n  }\n  if (checksum != null) {\n    args.push(\"--sha512\", checksum)\n  }\n  return executeAppBuilder(args)\n}\n"]}