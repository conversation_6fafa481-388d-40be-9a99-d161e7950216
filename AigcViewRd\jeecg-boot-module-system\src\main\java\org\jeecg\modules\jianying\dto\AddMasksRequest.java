package org.jeecg.modules.jianying.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 添加蒙版请求参数
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AddMasksRequest extends BaseJianyingRequest {
    
    @ApiModelProperty(value = "草稿地址，使用create_draft输出的draft_url即可（必填）", required = true,
                     example = "https://example.com/draft/123")
    @NotBlank(message = "draft_url不能为空")
    @JsonProperty("draft_url")
    private String zjDraftUrl;

    @ApiModelProperty(value = "蒙版名称（必填）", required = true, example = "圆形蒙版")
    @NotBlank(message = "name不能为空")
    @JsonProperty("name")
    private String zjName;

    @ApiModelProperty(value = "add_videos或者add_images的返回值（必填）", required = true,
                     example = "[\"segment_123\", \"segment_456\"]")
    @NotEmpty(message = "segment_ids不能为空")
    @JsonProperty("segment_ids")
    private List<String> zjSegmentIds;

    @ApiModelProperty(value = "蒙版高度", example = "1080")
    @JsonProperty("height")
    private Integer zjHeight;

    @ApiModelProperty(value = "蒙版宽度", example = "1920")
    @JsonProperty("width")
    private Integer zjWidth;

    @ApiModelProperty(value = "X轴位置", example = "100")
    @JsonProperty("X")
    private Integer zjX;

    @ApiModelProperty(value = "Y轴位置", example = "200")
    @JsonProperty("Y")
    private Integer zjY;

    @ApiModelProperty(value = "旋转角度，范围0-360", example = "45")
    @JsonProperty("rotation")
    private Integer zjRotation;

    @ApiModelProperty(value = "矩形的圆角", example = "10")
    @JsonProperty("roundCorner")
    private Integer zjRoundCorner;

    @ApiModelProperty(value = "羽化，范围0-100", example = "20")
    @JsonProperty("feather")
    private Integer zjFeather;

    @ApiModelProperty(value = "是否反转", example = "false")
    @JsonProperty("invert")
    private Boolean zjInvert;
    
    @Override
    public String getSummary() {
        return "AddMasksRequest{" +
               "accessKey=" + (getAccessKey() != null && getAccessKey().length() > 10 ?
                              getAccessKey().substring(0, 10) + "***" : "null") +
               ", name=" + zjName +
               ", segmentCount=" + (zjSegmentIds != null ? zjSegmentIds.size() : 0) +
               ", position=(" + zjX + "," + zjY + ")" +
               ", size=(" + zjWidth + "x" + zjHeight + ")" +
               "}";
    }
}
