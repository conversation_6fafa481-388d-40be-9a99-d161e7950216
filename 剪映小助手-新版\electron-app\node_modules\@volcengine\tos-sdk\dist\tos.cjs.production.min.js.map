{"version": 3, "file": "tos.cjs.production.min.js", "sources": ["../src/TosServerError.ts", "../src/TosClientError.ts", "../src/CancelError.ts", "../src/nodejs/fs-promises.ts", "../src/utils.ts", "../src/methods/object/multipart/createMultipartUpload.ts", "../src/methods/object/multipart/listParts.ts", "../src/mime-types.ts", "../src/nodejs/EmitReadStream.ts", "../src/universal/crc.ts", "../src/nodejs/crc.node.ts", "../src/nodejs/rate-limiter.ts", "../src/universal/rate-limiter.ts", "../src/nodejs/buffer2Stream.ts", "../src/nodejs/CrcReadStream.ts", "../src/methods/object/sharedTypes.ts", "../src/methods/object/utils.ts", "../src/interface.ts", "../src/log.ts", "../src/universal/crypto.browser.ts", "../src/nodejs/crypto.nodejs.ts", "../src/universal/crypto.ts", "../src/methods/object/multipart/uploadPart.ts", "../src/methods/object/multipart/completeMultipartUpload.ts", "../src/nodejs/EmptyReadStream.ts", "../src/methods/object/multipart/uploadFile.ts", "../src/TosExportEnum.ts", "../src/methods/object/multipart/resumableCopyObject.ts", "../src/methods/object/headObject.ts", "../src/methods/object/multipart/uploadPartCopy.ts", "../src/methods/object/copyObject.ts", "../src/methods/object/getObject.ts", "../src/methods/object/downloadFile.ts", "../src/signatureV4.ts", "../src/nodejs/TosAgent.ts", "../src/methods/base.ts", "../src/axios.ts", "../src/methods/object/listObjects.ts", "../src/methods/object/listObjectsType2.ts", "../src/ShareLinkClient.ts", "../src/methods/bucket/base.ts", "../src/methods/bucket/acl.ts", "../src/methods/object/putObject.ts", "../src/methods/object/fetch.ts", "../src/methods/object/getPreSignedUrl.ts", "../src/methods/object/deleteObject.ts", "../src/methods/object/renameObject.ts", "../src/methods/object/deleteMultiObjects.ts", "../src/methods/object/acl/index.ts", "../src/methods/object/multipart/abortMultipartUpload.ts", "../src/methods/object/multipart/listMultipartUploads.ts", "../src/methods/object/appendObject.ts", "../src/methods/object/setObjectMeta.ts", "../src/methods/object/calculatePostSignature.ts", "../src/handleEmptyServerError.ts", "../src/methods/bucket/policy.ts", "../src/methods/bucket/versioning.ts", "../src/methods/object/preSignedPolicyURL.ts", "../src/methods/bucket/getBucketLocation.ts", "../src/methods/bucket/cors.ts", "../src/methods/bucket/lifecycle.ts", "../src/methods/bucket/encryption.ts", "../src/methods/bucket/mirrorback.ts", "../src/methods/object/tagging.ts", "../src/methods/bucket/replication.ts", "../src/methods/bucket/website.ts", "../src/methods/bucket/notification.ts", "../src/methods/bucket/customDomain.ts", "../src/methods/bucket/realTimeLog.ts", "../src/methods/bucket/inventory.ts", "../src/methods/qosPolicy/index.ts", "../src/methods/batch/index.ts", "../src/methods/bucket/tag.ts", "../src/methods/bucket/payByTraffic.ts", "../src/methods/bucket/img.ts", "../src/methods/bucket/intelligenttiering.ts", "../src/methods/bucket/rename.ts", "../src/methods/object/restoreObject.ts", "../src/methods/storageLens/index.ts", "../src/methods/bucket/notificationType2.ts", "../src/methods/object/putSymlink.ts", "../src/methods/object/getSymlink.ts", "../src/methods/bucket/acceleration.ts", "../src/methods/bucket/accessMonitor.ts", "../src/methods/mrap/index.tsx", "../src/methods/mrap/mirror.tsx", "../src/methods/bucket/media.ts", "../src/methods/bucket/trash.ts", "../src/InnerClient.ts", "../src/browser-index.ts"], "sourcesContent": ["import { AxiosResponse } from 'axios';\nimport { Headers } from './interface';\n\nexport interface TosServerErrorData {\n  Code: string;\n  HostId: string;\n  Message: string;\n  RequestId: string;\n  EC?: string;\n}\n\nexport class TosServerError extends Error {\n  /**\n   * is original from backend, equals `data.Code`\n   */\n  public code: string;\n\n  /**\n   * the body when backend errors\n   */\n  public data: TosServerErrorData;\n  /**\n   * status code\n   */\n  public statusCode: number;\n  /**\n   * response headers\n   */\n  public headers: Headers;\n\n  /**\n   * identifies the errored request, equals to headers['x-tos-request-id'].\n   * If you has any question about the request, please send the requestId and id2 to TOS worker.\n   */\n  public requestId: string;\n\n  /**\n   * identifies the errored request, equals to headers['x-tos-id-2'].\n   * If you has any question about the request, please send the requestId and id2 to TOS worker.\n   */\n  public id2: string;\n\n  constructor(response: AxiosResponse<TosServerErrorData>) {\n    const { data } = response;\n    super(data.Message);\n\n    // https://www.dannyguo.com/blog/how-to-fix-instanceof-not-working-for-custom-errors-in-typescript/\n    Object.setPrototypeOf(this, TosServerError.prototype);\n\n    this.data = data;\n    this.code = data.Code;\n    this.statusCode = response.status;\n    this.headers = response.headers;\n    this.requestId = response.headers['x-tos-request-id'];\n    this.id2 = response.headers['x-tos-id-2'];\n  }\n}\n\nexport default TosServerError;\n\nexport enum TosServerCode {\n  NoSuchBucket = 'NoSuchBucket',\n  NoSuchKey = 'NoSuchKey',\n  AccessDenied = 'AccessDenied',\n  MalformedAcl = 'MalformedAclError',\n  UnexpectedContent = 'UnexpectedContent',\n  InvalidRequest = 'InvalidRequest',\n  MissingSecurityHeader = 'MissingSecurityHeader',\n  InvalidArgument = 'InvalidArgument',\n  EntityTooSmall = 'EntityTooSmall',\n  InvalidBucketName = 'InvalidBucketName',\n  BucketNotEmpty = 'BucketNotEmpty',\n  TooManyBuckets = 'TooManyBuckets',\n  BucketAlreadyExists = 'BucketAlreadyExists',\n  MalformedBody = 'MalformedBody',\n  NoSuchLifecycleConfiguration = 'NoSuchLifecycleConfiguration',\n  ReplicationConfigurationNotFound = 'ReplicationConfigurationNotFoundError',\n  InvalidLocationConstraint = 'InvalidLocationConstraint',\n  AuthorizationQueryParametersError = 'AuthorizationQueryParametersError',\n  RequestTimeTooSkewed = 'RequestTimeTooSkewed',\n  SignatureDoesNotMatch = 'SignatureDoesNotMatch',\n  RequestedRangeNotSatisfiable = 'Requested Range Not Satisfiable',\n  PreconditionFailed = 'PreconditionFailed',\n  BadDigest = 'BadDigest',\n  InvalidDigest = 'InvalidDigest',\n  EntityTooLarge = 'EntityTooLarge',\n  UnImplemented = 'UnImplemented',\n  MethodNotAllowed = 'MethodNotAllowed',\n  InvalidAccessKeyId = 'InvalidAccessKeyId',\n  InvalidSecurityToken = 'InvalidSecurityToken',\n  ContentSHA256Mismatch = 'ContentSHA256Mismatch',\n  ExceedQPSLimit = 'ExceedQPSLimit',\n  ExceedRateLimit = 'ExceedRateLimit',\n  NoSuchCORSConfiguration = 'NoSuchCORSConfiguration',\n  NoSuchMirrorConfiguration = 'NoSuchMirrorConfiguration',\n  NoSuchWebsiteConfiguration = 'NoSuchWebsiteConfiguration',\n  MissingRequestBody = 'MissingRequestBodyError',\n  BucketAlreadyOwnedByYou = 'BucketAlreadyOwnedByYou',\n  NoSuchBucketPolicy = 'NoSuchBucketPolicy',\n  PolicyTooLarge = 'PolicyTooLarge',\n  MalformedPolicy = 'MalformedPolicy',\n  InvalidKey = 'InvalidKey',\n  MirrorFailed = 'MirrorFailed',\n  Timeout = 'Timeout',\n  OffsetNotMatched = 'OffsetNotMatched',\n  NotAppendable = 'NotAppendable',\n  ContextCanceled = 'ContextCanceled',\n  InternalError = 'InternalError',\n  TooManyRequests = 'TooManyRequests',\n  TimeOut = 'TimeOut',\n  ConcurrencyUpdateObjectLimit = 'ConcurrencyUpdateObjectLimit',\n  DuplicateUpload = 'DuplicateUpload',\n  DuplicateObject = 'DuplicateObject',\n  InvalidVersionId = 'InvalidVersionId',\n  StorageClassNotMatch = 'StorageClassNotMatch',\n  UploadStatusNotUploading = 'UploadStatusNotUploading',\n  PartSizeNotMatch = 'PartSizeNotMatch',\n  NoUploadPart = 'NoUploadPart',\n  PartsLenInvalid = 'PartsLenInvalid',\n  PartsIdxSmall = 'PartsIdxSmall',\n  PartSizeSmall = 'PartSizeSmall',\n  PrefixNotNextKeyPrefix = 'PrefixNotNextKeyPrefix',\n  InvalidPart = 'InvalidPart',\n  InvalidPartOffset = 'InvalidPartOffset',\n  MismatchObject = 'MismatchObject',\n  UploadStatusMismatch = 'UploadStatusMismatch',\n  CompletingStatusNoExpiration = 'CompletingStatusNoExpiration',\n  Found = 'Found',\n  InvalidRedirectLocation = 'InvalidRedirectLocation',\n}\n", "export class TosClientError extends Error {\n  constructor(message: string) {\n    super(message);\n\n    // https://www.dannyguo.com/blog/how-to-fix-instanceof-not-working-for-custom-errors-in-typescript/\n    Object.setPrototypeOf(this, TosClientError.prototype);\n  }\n}\n\nexport default TosClientError;\n", "export class CancelError extends Error {\n  constructor(message: string) {\n    super(message);\n\n    // https://www.dannyguo.com/blog/how-to-fix-instanceof-not-working-for-custom-errors-in-typescript/\n    Object.setPrototypeOf(this, CancelError.prototype);\n  }\n}\n", "/**\n * since fs/promises exist after nodejs@14, so we make own fs/promises\n */\nimport fs from 'fs';\nimport fsPromises from 'fs/promises';\nimport { promisify } from 'util';\n\nexport const createWriteStream = fs.createWriteStream;\nexport const createReadStream = fs.createReadStream;\nexport const open = fsPromises.open;\nexport const close = promisify(fs.close);\nexport const rename = promisify(fs.rename);\nexport const stat = promisify(fs.stat);\nexport const mkdir = promisify(fs.mkdir);\nexport const writeFile = promisify(fs.writeFile);\nexport const write = promisify(fs.write);\nexport const appendFile = promisify(fs.appendFile);\n// fs.rm was added v14.14.0, so use fs.unlink\nexport const rm = promisify(fs.unlink);\nexport const readFile = promisify(fs.readFile);\n\nexport const safeMkdirRecursive = async (dirName: fs.PathLike) => {\n  try {\n    await fsPromises.access(dirName);\n  } catch (e) {\n    await fsPromises.mkdir(dirName, { recursive: true });\n  }\n};\n", "import { Readable } from 'stream';\nimport {\n  CamelCasedPropertiesDeep,\n  KebabCasedPropertiesDeep,\n  PascalCasedPropertiesDeep,\n} from 'type-fest';\nimport get from 'lodash/get';\nimport set from 'lodash/set';\nimport { CancelError } from './CancelError';\nimport TosClientError from './TosClientError';\nimport { Headers } from './interface';\nimport { TOSConstructorOptions, TosResponse } from './methods/base';\nimport qs from 'qs';\nimport TosServerError from './TosServerError';\nimport { CRCCls } from './universal/crc';\nimport * as fsp from './nodejs/fs-promises';\nimport { ReadStream, WriteStream } from 'fs';\n\n// obj[key] must be a array\nexport const makeArrayProp = (obj: unknown) => (key: string) => {\n  if (obj == null || typeof obj !== 'object') {\n    return;\n  }\n\n  const value = get(obj, key);\n  if (!Array.isArray(value)) {\n    set(obj, key, value == null ? [] : [value]);\n  }\n};\n\nconst makeConvertProp = (convertMethod: (prop: string) => string) => {\n  const finalMethod = <T = unknown>(target: T): T => {\n    if (Array.isArray(target)) {\n      return target.map((it) => finalMethod(it)) as unknown as T;\n    }\n\n    if (typeof target === 'string') {\n      return convertMethod(target) as unknown as T;\n    }\n\n    if (typeof target === 'object' && target != null) {\n      type Obj = Record<string, unknown>;\n      const ret = Object.keys(target).reduce((acc: Obj, key: string) => {\n        const nextKey = finalMethod(key);\n        acc[nextKey] = (target as Obj)[key];\n        return acc;\n      }, {});\n      return ret as unknown as T;\n    }\n\n    return target;\n  };\n\n  return finalMethod;\n};\n\nexport const covertCamelCase2Kebab = makeConvertProp((camelCase: string) => {\n  return camelCase.replace(/[A-Z]/g, '-$&').toLowerCase();\n}) as <T = unknown>(target: T) => KebabCasedPropertiesDeep<T>;\n\nexport const convertUpperCamelCase2Normal = makeConvertProp(\n  (upperCamelCase: string) => {\n    return upperCamelCase[0].toLocaleLowerCase() + upperCamelCase.slice(1);\n  }\n) as <T = unknown>(target: T) => CamelCasedPropertiesDeep<T>;\n\nexport const convertNormalCamelCase2Upper = makeConvertProp(\n  (normalCamelCase: string) => {\n    return normalCamelCase[0].toUpperCase() + normalCamelCase.slice(1);\n  }\n) as <T = unknown>(target: T) => PascalCasedPropertiesDeep<T>;\n\nexport const getSortedQueryString = (query: Record<string, any>) => {\n  const searchParts: string[] = [];\n  Object.keys(query)\n    .sort()\n    .forEach((key) => {\n      searchParts.push(\n        `${encodeURIComponent(key)}=${encodeURIComponent(query[key])}`\n      );\n    });\n  return searchParts.join('&');\n};\n\nexport const normalizeHeadersKey = <T extends Headers>(\n  headers: T | undefined\n): T => {\n  const headers1: Headers = headers || {};\n  const headers2: Headers = {};\n  Object.keys(headers1).forEach((key: string) => {\n    if (headers1[key] != null) {\n      headers2[key] = headers1[key];\n    }\n  });\n\n  const headers3: Headers = {};\n  Object.keys(headers2).forEach((key: string) => {\n    const newKey = key.toLowerCase();\n    headers3[newKey] = headers2[key];\n  });\n\n  return headers3 as T;\n};\n\nexport const encodeHeadersValue = (headers: Headers) => {\n  const header2: Headers = {};\n  Object.entries(headers).forEach(([key, value]) => {\n    header2[key] = `${value}`\n      // reference:\n      //  https://stackoverflow.com/questions/38345372/why-is-length-2\n      .match(/./gu)!\n      .map((ch: string) => {\n        if (ch.length > 1 || ch.charCodeAt(0) >= 128) {\n          return encodeURIComponent(ch);\n        }\n        return ch;\n      })\n      .join('');\n  });\n  return header2;\n};\n\n// TODO: getRegion from endpoint, maybe user passes it is better.\nexport const getRegion = (endpoint: string) => {\n  const region = endpoint.match(/-(\\w+).volces.com/);\n  if (!region) {\n    return 'cn-beijing';\n  }\n  return `cn-${region[1]}`;\n};\n\nexport const getEndpoint = (region: string) => {\n  return `tos-${region}.volces.com`;\n};\n\nexport const normalizeProxy = (proxy: TOSConstructorOptions['proxy']) => {\n  if (typeof proxy === 'string') {\n    proxy = {\n      url: proxy,\n    };\n  }\n\n  if (\n    proxy &&\n    proxy?.needProxyParams == null &&\n    process.env.TARGET_ENVIRONMENT === 'browser'\n  ) {\n    proxy.needProxyParams = true;\n  }\n\n  return proxy;\n};\n\nexport async function safeAwait<T>(\n  p: T\n): Promise<[null, Awaited<T>] | [any, null]> {\n  try {\n    const v = await p;\n    return [null, v];\n  } catch (err) {\n    return [err, null];\n  }\n}\n\nexport function safeSync<T>(func: () => T): [any, null] | [null, T] {\n  try {\n    const ret = func();\n    return [null, ret];\n  } catch (err) {\n    return [err, null];\n  }\n}\n\nexport function isBlob(obj: unknown): obj is Blob {\n  return typeof Blob !== 'undefined' && obj instanceof Blob;\n}\n\nexport function isBuffer(obj: unknown): obj is Buffer {\n  return typeof Buffer !== 'undefined' && obj instanceof Buffer;\n}\n\nexport function isReadable(obj: unknown): obj is NodeJS.ReadableStream {\n  if (process.env.TARGET_ENVIRONMENT !== 'node') {\n    return false;\n  }\n\n  return obj instanceof Readable;\n}\n\nexport function isValidNumber(v: number): v is number {\n  return !!v || v == 0;\n}\n\nexport function obj2QueryStr(v?: Record<string, unknown>) {\n  if (!v) {\n    return '';\n  }\n  return Object.keys(v)\n    .map((key) => {\n      const vStr = `${v[key]}`;\n      return `${encodeURIComponent(key)}=${encodeURIComponent(vStr)}`;\n    })\n    .join('&');\n}\n\nexport function isCancelError(err: any) {\n  return err instanceof CancelError;\n}\n\nexport const DEFAULT_PART_SIZE = 20 * 1024 * 1024; // 20 MB\n\nexport const getGMTDateStr = (v: Date) => {\n  return v.toUTCString();\n};\nconst gmtDateOrStr = (v: Date | string) => {\n  if (typeof v === 'string') {\n    return v;\n  }\n  return v.toUTCString();\n};\n\nexport const requestHeadersMap: Record<\n  string,\n  string | [string, (v: any) => string] | ((v: any) => Record<string, string>)\n> = {\n  projectName: 'x-tos-project-name',\n  encodingType: 'encoding-type',\n  cacheControl: 'cache-control',\n  contentDisposition: 'content-disposition',\n  contentLength: 'content-length',\n  contentMD5: 'content-md5',\n  contentSHA256: 'x-tos-content-sha256',\n  contentEncoding: 'content-encoding',\n  contentLanguage: 'content-language',\n  contentType: 'content-type',\n  expires: ['expires', getGMTDateStr],\n  range: 'range',\n\n  ifMatch: 'if-match',\n  ifModifiedSince: ['if-modified-since', gmtDateOrStr],\n  ifNoneMatch: 'if-none-match',\n  ifUnmodifiedSince: ['if-unmodified-since', gmtDateOrStr],\n\n  acl: 'x-tos-acl',\n  grantFullControl: 'x-tos-grant-full-control',\n  grantRead: 'x-tos-grant-read',\n  grantReadAcp: 'x-tos-grant-read-acp',\n  grantWrite: 'x-tos-grant-write',\n  grantWriteAcp: 'x-tos-grant-write-acp',\n\n  serverSideEncryption: 'x-tos-server-side-encryption',\n  serverSideDataEncryption: 'x-tos-server-side-data-encryption',\n  ssecAlgorithm: 'x-tos-server-side-encryption-customer-algorithm',\n  ssecKey: 'x-tos-server-side-encryption-customer-key',\n  ssecKeyMD5: 'x-tos-server-side-encryption-customer-key-md5',\n\n  copySourceRange: 'x-tos-copy-source-range',\n  copySourceIfMatch: 'x-tos-copy-source-if-match',\n  copySourceIfModifiedSince: [\n    'x-tos-copy-source-if-modified-since',\n    gmtDateOrStr,\n  ],\n  copySourceIfNoneMatch: 'x-tos-copy-source-if-none-match',\n  copySourceIfUnmodifiedSince: 'x-tos-copy-source-if-unmodified-since',\n  copySourceSSECAlgorithm:\n    'x-tos-copy-source-server-side-encryption-customer-algorithm',\n  copySourceSSECKey: 'x-tos-copy-source-server-side-encryption-customer-key',\n  copySourceSSECKeyMD5:\n    'x-tos-copy-source-server-side-encryption-customer-key-MD5',\n\n  metadataDirective: 'x-tos-metadata-directive',\n  meta: (v: any) => {\n    return Object.keys(v).reduce((prev, key) => {\n      prev[`x-tos-meta-${key}`] = `${v[key]}`;\n      return prev;\n    }, {} as Record<string, string>);\n  },\n  websiteRedirectLocation: 'x-tos-website-redirect-location',\n  storageClass: 'x-tos-storage-class',\n  azRedundancy: 'x-tos-az-redundancy',\n  trafficLimit: 'x-tos-traffic-limit',\n  callback: 'x-tos-callback',\n  callbackVar: 'x-tos-callback-var',\n  allowSameActionOverlap: ['x-tos-allow-same-action-overlap', (v) => String(v)],\n  symLinkTargetKey: 'x-tos-symlink-target',\n  symLinkTargetBucket: 'x-tos-symlink-bucket',\n  forbidOverwrite: 'x-tos-forbid-overwrite',\n  bucketType: 'x-tos-bucket-type',\n  recursiveMkdir: 'x-tos-recursive-mkdir',\n};\n// type RequestHeadersMapKeys = keyof typeof requestHeadersMap;\n\nexport const requestQueryMap: Record<\n  string,\n  string | [string, (v: any) => string] | ((v: any) => Record<string, string>)\n> = {\n  versionId: 'versionId',\n  process: 'x-tos-process',\n  saveBucket: 'x-tos-save-bucket',\n  saveObject: 'x-tos-save-object',\n\n  responseCacheControl: 'response-cache-control',\n  responseContentDisposition: 'response-content-disposition',\n  responseContentEncoding: 'response-content-encoding',\n  responseContentLanguage: 'response-content-language',\n  responseContentType: 'response-content-type',\n  responseExpires: ['response-expires', (v: Date) => v.toUTCString()],\n};\n\nexport function fillRequestHeaders<T extends { headers?: Headers }>(\n  v: T,\n  // keys: (keyof T & RequestHeadersMapKeys)[]\n  keys: (keyof T & string)[]\n) {\n  if (!keys.length) {\n    return;\n  }\n\n  const headers = v.headers || {};\n  v.headers = headers;\n\n  function setOneHeader(k: string, v: string) {\n    if (headers[k] == null) {\n      headers[k] = v;\n    }\n  }\n\n  keys.forEach((k) => {\n    const confV = requestHeadersMap[k];\n    if (!confV) {\n      // maybe warning\n      throw new TosClientError(\n        `\\`${k}\\` isn't in keys of \\`requestHeadersMap\\``\n      );\n    }\n\n    const oriValue = v[k];\n    if (oriValue == null) {\n      return;\n    }\n\n    const oriValueStr = `${oriValue}`;\n    if (typeof confV === 'string') {\n      return setOneHeader(confV, oriValueStr);\n    }\n\n    if (Array.isArray(confV)) {\n      const newKey = confV[0];\n      const newValue = confV[1](oriValue);\n      return setOneHeader(newKey, newValue);\n    }\n\n    const obj = confV(oriValue);\n    Object.entries(obj).forEach(([k, v]) => {\n      setOneHeader(k, v);\n    });\n  });\n}\n\nexport function fillRequestQuery<T>(\n  v: T,\n  query: Record<string, unknown>,\n  keys: (keyof T & string)[]\n) {\n  if (!keys.length) {\n    return;\n  }\n\n  function setOneKey(k: string, v: string) {\n    if (query[k] == null) {\n      query[k] = v;\n    }\n  }\n\n  keys.forEach((k) => {\n    const confV = requestQueryMap[k];\n    if (!confV) {\n      // maybe warning\n      throw new TosClientError(`\\`${k}\\` isn't in keys of \\`requestQueryMap\\``);\n    }\n\n    const oriValue = v[k];\n    if (oriValue == null) {\n      return;\n    }\n\n    const oriValueStr = `${oriValue}`;\n    if (typeof confV === 'string') {\n      return setOneKey(confV, oriValueStr);\n    }\n\n    if (Array.isArray(confV)) {\n      const newKey = confV[0];\n      const newValue = confV[1](oriValue);\n      return setOneKey(newKey, newValue);\n    }\n\n    const obj = confV(oriValue);\n    Object.entries(obj).forEach(([k, v]) => {\n      setOneKey(k, v);\n    });\n  });\n}\n\nexport const paramsSerializer = (params: Record<string, string>) => {\n  return qs.stringify(params);\n};\n\nexport function getNormalDataFromError<T>(\n  data: T,\n  err: TosServerError\n): TosResponse<T> {\n  return {\n    data,\n    statusCode: err.statusCode,\n    headers: err.headers,\n    requestId: err.requestId,\n    id2: err.id2,\n  };\n}\nexport const streamToBuf = async (\n  stream: NodeJS.ReadableStream\n): Promise<Buffer> => {\n  let buf = Buffer.from([]);\n  return new Promise((resolve, reject) => {\n    stream.on('data', (data) => {\n      buf = Buffer.concat([buf, data]);\n    });\n    stream.on('end', () => {\n      resolve(buf);\n    });\n    stream.on('error', (err) => {\n      reject(err);\n    });\n  });\n};\n\nexport function checkCRC64WithHeaders(crc: CRCCls | string, headers: Headers) {\n  const serverCRC64 = headers['x-tos-hash-crc64ecma'];\n  if (serverCRC64 == null) {\n    if (process.env.TARGET_ENVIRONMENT === 'browser') {\n      console.warn(\n        \"No x-tos-hash-crc64ecma in response's headers, please see https://www.volcengine.com/docs/6349/127737 to add `x-tos-hash-crc64ecma` to Expose-Headers field.\"\n      );\n    } else {\n    }\n    return;\n  }\n\n  const crcStr = typeof crc === 'string' ? crc : crc.getCrc64();\n  if (crcStr !== serverCRC64) {\n    throw new TosClientError(\n      `validate file crc64 failed. Expect crc64 ${serverCRC64}, actual crc64 ${crcStr}. Please try again.`\n    );\n  }\n}\n\nexport const makeStreamErrorHandler = (prefix?: string) => (err: any) => {\n  console.log(`${prefix || ''} stream error:`, err);\n};\n\nexport enum HttpHeader {\n  LastModified = 'last-modified',\n  ContentLength = 'content-length',\n  AcceptEncoding = 'accept-encoding',\n  ContentEncoding = 'content-encoding',\n  ContentMD5 = 'content-md5',\n  TosRawContentLength = 'x-tos-raw-content-length',\n  TosTrailer = 'x-tos-trailer',\n  TosHashCrc64ecma = 'x-tos-hash-crc64ecma',\n  TosContentSha256 = 'x-tos-content-sha256',\n  TosDecodedContentLength = 'x-tos-decoded-content-length',\n  TosEc = 'x-tos-ec',\n  TosRequestId = 'x-tos-request-id',\n}\n\n/**\n * make async tasks serial\n * @param makeTask\n * @returns\n */\nexport const makeSerialAsyncTask = (makeTask: () => Promise<void>) => {\n  let lastTask = Promise.resolve();\n  return async () => {\n    lastTask = lastTask.then(() => makeTask());\n    return lastTask;\n  };\n};\n\nexport const safeParseCheckpointFile = async (filePath: string) => {\n  try {\n    return JSON.parse(await fsp.readFile(filePath, 'utf-8'));\n  } catch (err) {\n    console.warn(\"checkpoint's content is not a valid JSON\");\n    return undefined;\n  }\n};\n\nexport const makeRetryStreamAutoClose = (\n  makeStream: () => NodeJS.ReadableStream | ReadStream\n) => {\n  let lastStream: ReadStream | NodeJS.ReadableStream | null = null;\n  const makeRetryStream = () => {\n    if (lastStream) {\n      tryDestroy(\n        lastStream,\n        new Error('retry new stream by makeRetryStreamAutoClose')\n      );\n    }\n\n    lastStream = makeStream();\n    return lastStream;\n  };\n\n  return {\n    getLastStream: () => lastStream,\n    make: makeRetryStream,\n  };\n};\n\nexport const tryDestroy = (\n  stream:\n    | NodeJS.ReadableStream\n    | ReadStream\n    | NodeJS.WritableStream\n    | WriteStream\n    | null\n    | undefined,\n  err: any\n) => {\n  if (stream && 'destroy' in stream && typeof stream.destroy === 'function') {\n    if ('destroyed' in stream && !stream.destroyed) {\n      stream.destroy(err);\n    }\n  }\n};\nexport const pipeStreamWithErrorHandle = <\n  Src extends NodeJS.ReadableStream | ReadStream,\n  Dest extends NodeJS.WritableStream | WriteStream\n>(\n  src: Src,\n  dest: Dest,\n  label: string\n): Dest => {\n  dest.on('error', makeStreamErrorHandler(label));\n  src.on('error', (err) => tryDestroy(dest, err));\n  dest.on('error', (err) => tryDestroy(src, err));\n  return src.pipe(dest) as Dest;\n};\n", "import TOSBase from '../../base';\nimport { fillRequestHeaders, normalizeHeadersKey } from '../../../utils';\nimport { Acl } from '../../../interface';\nimport { StorageClassType } from '../../../TosExportEnum';\n\nexport interface CreateMultipartUploadInput {\n  bucket?: string;\n  key: string;\n\n  encodingType?: string;\n  cacheControl?: string;\n  contentDisposition?: string;\n  contentEncoding?: string;\n  contentLanguage?: string;\n  contentType?: string;\n  expires?: Date;\n\n  acl?: Acl;\n  grantFullControl?: string;\n  grantRead?: string;\n  grantReadAcp?: string;\n  grantWriteAcp?: string;\n\n  ssecAlgorithm?: string;\n  ssecKey?: string;\n  ssecKeyMD5?: string;\n\n  serverSideEncryption?: string;\n  /** @private unstable */\n  serverSideDataEncryption?: string;\n\n  meta?: Record<string, string>;\n  websiteRedirectLocation?: string;\n  storageClass?: StorageClassType;\n  forbidOverwrite?: boolean;\n\n  headers?: {\n    [key: string]: string | undefined;\n    'encoding-type'?: string;\n    'Content-Disposition'?: string;\n    'x-tos-acl'?: Acl;\n    'content-type'?: string;\n    'x-tos-grant-full-control'?: string;\n    'x-tos-grant-read'?: string;\n    'x-tos-grant-read-acp'?: string;\n    'x-tos-grant-write-acp'?: string;\n    'x-tos-server-side-encryption-customer-algorithm'?: string;\n    'x-tos-server-side-encryption-customer-key'?: string;\n    'x-tos-server-side-encryption-customer-key-md5'?: string;\n    'x-tos-website-redirect-location'?: string;\n    'x-tos-storage-class'?: string;\n    'x-tos-server-side-encryption'?: string;\n    ['x-tos-forbid-overwrite']?: string;\n  };\n}\n\nexport interface CreateMultipartUploadOutput {\n  UploadId: string;\n  Bucket: string;\n  Key: string;\n  EncodingType?: string;\n}\n\nexport async function createMultipartUpload(\n  this: TOSBase,\n  input: CreateMultipartUploadInput | string\n) {\n  input = this.normalizeObjectInput(input);\n  const headers = normalizeHeadersKey(input.headers);\n  input.headers = headers;\n  fillRequestHeaders(input, [\n    'encodingType',\n    'cacheControl',\n    'contentDisposition',\n    'contentEncoding',\n    'contentLanguage',\n    'contentType',\n    'expires',\n\n    'acl',\n    'grantFullControl',\n    'grantRead',\n    'grantReadAcp',\n    'grantWriteAcp',\n\n    'ssecAlgorithm',\n    'ssecKey',\n    'ssecKeyMD5',\n    'serverSideEncryption',\n    'serverSideDataEncryption',\n\n    'meta',\n    'websiteRedirectLocation',\n    'storageClass',\n    'forbidOverwrite',\n  ]);\n\n  this.setObjectContentTypeHeader(input, headers);\n\n  return this._fetchObject<CreateMultipartUploadOutput>(\n    input,\n    'POST',\n    { uploads: '' },\n    headers,\n    ''\n  );\n}\n", "import { covertCamelCase2Kebab, makeArrayProp } from '../../../utils';\nimport TOSBase from '../../base';\n\ninterface ListPartInput {\n  bucket?: string;\n  key: string;\n  uploadId: string;\n  maxParts?: number;\n  partNumberMarker?: number;\n  encodingType?: string;\n}\n\ninterface ListPartOutput {\n  Bucket: string;\n  Key: string;\n  UploadId: string;\n  PartNumberMarker: number;\n  NextPartNumberMarker: number;\n  MaxParts: number;\n  IsTruncated: boolean;\n  StorageClass: string;\n  Owner: { ID: string; DisplayName: string };\n  Parts: {\n    PartNumber: number;\n    LastModified: string;\n    ETag: string;\n    Size: number;\n  }[];\n}\n\n// the part except last one must be >= 5 MB\n// the last part is no size limit\nexport const MIN_PART_SIZE_EXCEPT_LAST_ONE = 5 * 1024 * 1024;\nexport const MAX_PART_NUMBER = 10000;\n\nexport const calculateSafePartSize = (\n  totalSize: number,\n  expectPartSize: number,\n  showWarning = false\n) => {\n  let partSize = expectPartSize;\n  if (expectPartSize < MIN_PART_SIZE_EXCEPT_LAST_ONE) {\n    partSize = MIN_PART_SIZE_EXCEPT_LAST_ONE;\n    if (showWarning) {\n      console.warn(\n        `partSize has been set to ${partSize}, because the partSize you provided is less than the minimal size of multipart`\n      );\n    }\n  }\n  const minSize = Math.ceil(totalSize / MAX_PART_NUMBER);\n  if (expectPartSize < minSize) {\n    partSize = minSize;\n    if (showWarning) {\n      console.warn(\n        `partSize has been set to ${partSize}, because the partSize you provided causes the number of part excesses 10,000`\n      );\n    }\n  }\n\n  return partSize;\n};\n\nexport async function listParts(this: TOSBase, input: ListPartInput) {\n  const { bucket, key, uploadId, ...nextQuery } = input;\n  const ret = await this._fetchObject<ListPartOutput>(\n    input,\n    'GET',\n    {\n      uploadId,\n      ...covertCamelCase2Kebab(nextQuery),\n    },\n    {}\n  );\n  const arrayProp = makeArrayProp(ret.data);\n  arrayProp('Parts');\n\n  return ret;\n}\n", "// alias with GoSDK\n// refer https://github.com/volcengine/ve-tos-golang-sdk/blob/main/tos/mime.go\nexport const mimeTypes: Record<string, string | undefined> = {\n  '3gp': 'video/3gpp',\n  '7z': 'application/x-7z-compressed',\n  abw: 'application/x-abiword',\n  ai: 'application/postscript',\n  aif: 'audio/x-aiff',\n  aifc: 'audio/x-aiff',\n  aiff: 'audio/x-aiff',\n  alc: 'chemical/x-alchemy',\n  amr: 'audio/amr',\n  anx: 'application/annodex',\n  apk: 'application/vnd.android.package-archive',\n  appcache: 'text/cache-manifest',\n  art: 'image/x-jg',\n  asc: 'text/plain',\n  asf: 'video/x-ms-asf',\n  aso: 'chemical/x-ncbi-asn1-binary',\n  asx: 'video/x-ms-asf',\n  atom: 'application/atom+xml',\n  atomcat: 'application/atomcat+xml',\n  atomsrv: 'application/atomserv+xml',\n  au: 'audio/basic',\n  avi: 'video/x-msvideo',\n  awb: 'audio/amr-wb',\n  axa: 'audio/annodex',\n  axv: 'video/annodex',\n  b: 'chemical/x-molconn-Z',\n  bak: 'application/x-trash',\n  bat: 'application/x-msdos-program',\n  bcpio: 'application/x-bcpio',\n  bib: 'text/x-bibtex',\n  bin: 'application/octet-stream',\n  bmp: 'image/x-ms-bmp',\n  boo: 'text/x-boo',\n  book: 'application/x-maker',\n  brf: 'text/plain',\n  bsd: 'chemical/x-crossfire',\n  c: 'text/x-csrc',\n  'c++': 'text/x-c++src',\n  c3d: 'chemical/x-chem3d',\n  cab: 'application/x-cab',\n  cac: 'chemical/x-cache',\n  cache: 'chemical/x-cache',\n  cap: 'application/vnd.tcpdump.pcap',\n  cascii: 'chemical/x-cactvs-binary',\n  cat: 'application/vnd.ms-pki.seccat',\n  cbin: 'chemical/x-cactvs-binary',\n  cbr: 'application/x-cbr',\n  cbz: 'application/x-cbz',\n  cc: 'text/x-c++src',\n  cda: 'application/x-cdf',\n  cdf: 'application/x-cdf',\n  cdr: 'image/x-coreldraw',\n  cdt: 'image/x-coreldrawtemplate',\n  cdx: 'chemical/x-cdx',\n  cdy: 'application/vnd.cinderella',\n  cef: 'chemical/x-cxf',\n  cer: 'chemical/x-cerius',\n  chm: 'chemical/x-chemdraw',\n  chrt: 'application/x-kchart',\n  cif: 'chemical/x-cif',\n  class: 'application/java-vm',\n  cls: 'text/x-tex',\n  cmdf: 'chemical/x-cmdf',\n  cml: 'chemical/x-cml',\n  cod: 'application/vnd.rim.cod',\n  com: 'application/x-msdos-program',\n  cpa: 'chemical/x-compass',\n  cpio: 'application/x-cpio',\n  cpp: 'text/x-c++src',\n  cpt: 'application/mac-compactpro',\n  cr2: 'image/x-canon-cr2',\n  crl: 'application/x-pkcs7-crl',\n  crt: 'application/x-x509-ca-cert',\n  crw: 'image/x-canon-crw',\n  csd: 'audio/csound',\n  csf: 'chemical/x-cache-csf',\n  csh: 'application/x-csh',\n  csm: 'chemical/x-csml',\n  csml: 'chemical/x-csml',\n  css: 'text/css',\n  csv: 'text/csv',\n  ctab: 'chemical/x-cactvs-binary',\n  ctx: 'chemical/x-ctx',\n  cu: 'application/cu-seeme',\n  cub: 'chemical/x-gaussian-cube',\n  cxf: 'chemical/x-cxf',\n  cxx: 'text/x-c++src',\n  d: 'text/x-dsrc',\n  davmount: 'application/davmount+xml',\n  dcm: 'application/dicom',\n  dcr: 'application/x-director',\n  ddeb: 'application/vnd.debian.binary-package',\n  dif: 'video/dv',\n  diff: 'text/x-diff',\n  dir: 'application/x-director',\n  djv: 'image/vnd.djvu',\n  djvu: 'image/vnd.djvu',\n  dl: 'video/dl',\n  dll: 'application/x-msdos-program',\n  dmg: 'application/x-apple-diskimage',\n  dms: 'application/x-dms',\n  doc: 'application/msword',\n  docm: 'application/vnd.ms-word.document.macroEnabled.12',\n  docx: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',\n  dot: 'application/msword',\n  dotm: 'application/vnd.ms-word.template.macroEnabled.12',\n  dotx: 'application/vnd.openxmlformats-officedocument.wordprocessingml.template',\n  dv: 'video/dv',\n  dvi: 'application/x-dvi',\n  dx: 'chemical/x-jcamp-dx',\n  dxr: 'application/x-director',\n  emb: 'chemical/x-embl-dl-nucleotide',\n  embl: 'chemical/x-embl-dl-nucleotide',\n  eml: 'message/rfc822',\n  eot: 'application/vnd.ms-fontobject',\n  eps: 'application/postscript',\n  eps2: 'application/postscript',\n  eps3: 'application/postscript',\n  epsf: 'application/postscript',\n  epsi: 'application/postscript',\n  erf: 'image/x-epson-erf',\n  es: 'application/ecmascript',\n  etx: 'text/x-setext',\n  exe: 'application/x-msdos-program',\n  ez: 'application/andrew-inset',\n  fb: 'application/x-maker',\n  fbdoc: 'application/x-maker',\n  fch: 'chemical/x-gaussian-checkpoint',\n  fchk: 'chemical/x-gaussian-checkpoint',\n  fig: 'application/x-xfig',\n  flac: 'audio/flac',\n  fli: 'video/fli',\n  flv: 'video/x-flv',\n  fm: 'application/x-maker',\n  frame: 'application/x-maker',\n  frm: 'application/x-maker',\n  gal: 'chemical/x-gaussian-log',\n  gam: 'chemical/x-gamess-input',\n  gamin: 'chemical/x-gamess-input',\n  gan: 'application/x-ganttproject',\n  gau: 'chemical/x-gaussian-input',\n  gcd: 'text/x-pcs-gcd',\n  gcf: 'application/x-graphing-calculator',\n  gcg: 'chemical/x-gcg8-sequence',\n  gen: 'chemical/x-genbank',\n  gf: 'application/x-tex-gf',\n  gif: 'image/gif',\n  gjc: 'chemical/x-gaussian-input',\n  gjf: 'chemical/x-gaussian-input',\n  gl: 'video/gl',\n  gnumeric: 'application/x-gnumeric',\n  gpt: 'chemical/x-mopac-graph',\n  gsf: 'application/x-font',\n  gsm: 'audio/x-gsm',\n  gtar: 'application/x-gtar',\n  gz: 'application/gzip',\n  h: 'text/x-chdr',\n  'h++': 'text/x-c++hdr',\n  hdf: 'application/x-hdf',\n  hh: 'text/x-c++hdr',\n  hin: 'chemical/x-hin',\n  hpp: 'text/x-c++hdr',\n  hqx: 'application/mac-binhex40',\n  hs: 'text/x-haskell',\n  hta: 'application/hta',\n  htc: 'text/x-component',\n  htm: 'text/html',\n  html: 'text/html',\n  hwp: 'application/x-hwp',\n  hxx: 'text/x-c++hdr',\n  ica: 'application/x-ica',\n  ice: 'x-conference/x-cooltalk',\n  ico: 'image/vnd.microsoft.icon',\n  ics: 'text/calendar',\n  icz: 'text/calendar',\n  ief: 'image/ief',\n  iges: 'model/iges',\n  igs: 'model/iges',\n  iii: 'application/x-iphone',\n  info: 'application/x-info',\n  inp: 'chemical/x-gamess-input',\n  ins: 'application/x-internet-signup',\n  iso: 'application/x-iso9660-image',\n  isp: 'application/x-internet-signup',\n  ist: 'chemical/x-isostar',\n  istr: 'chemical/x-isostar',\n  jad: 'text/vnd.sun.j2me.app-descriptor',\n  jam: 'application/x-jam',\n  jar: 'application/java-archive',\n  java: 'text/x-java',\n  jdx: 'chemical/x-jcamp-dx',\n  jmz: 'application/x-jmol',\n  jng: 'image/x-jng',\n  jnlp: 'application/x-java-jnlp-file',\n  jp2: 'image/jp2',\n  jpe: 'image/jpeg',\n  jpeg: 'image/jpeg',\n  jpf: 'image/jpx',\n  jpg: 'image/jpeg',\n  jpg2: 'image/jp2',\n  jpm: 'image/jpm',\n  jpx: 'image/jpx',\n  js: 'application/javascript',\n  json: 'application/json',\n  kar: 'audio/midi',\n  key: 'application/pgp-keys',\n  kil: 'application/x-killustrator',\n  kin: 'chemical/x-kinemage',\n  kml: 'application/vnd.google-earth.kml+xml',\n  kmz: 'application/vnd.google-earth.kmz',\n  kpr: 'application/x-kpresenter',\n  kpt: 'application/x-kpresenter',\n  ksp: 'application/x-kspread',\n  kwd: 'application/x-kword',\n  kwt: 'application/x-kword',\n  latex: 'application/x-latex',\n  lha: 'application/x-lha',\n  lhs: 'text/x-literate-haskell',\n  lin: 'application/bbolin',\n  lsf: 'video/x-la-asf',\n  lsx: 'video/x-la-asf',\n  ltx: 'text/x-tex',\n  ly: 'text/x-lilypond',\n  lyx: 'application/x-lyx',\n  lzh: 'application/x-lzh',\n  lzx: 'application/x-lzx',\n  m3g: 'application/m3g',\n  m3u: 'audio/x-mpegurl',\n  m3u8: 'application/x-mpegURL',\n  m4a: 'audio/mpeg',\n  maker: 'application/x-maker',\n  man: 'application/x-troff-man',\n  mbox: 'application/mbox',\n  mcif: 'chemical/x-mmcif',\n  mcm: 'chemical/x-macmolecule',\n  mdb: 'application/msaccess',\n  me: 'application/x-troff-me',\n  mesh: 'model/mesh',\n  mid: 'audio/midi',\n  midi: 'audio/midi',\n  mif: 'application/x-mif',\n  mkv: 'video/x-matroska',\n  mm: 'application/x-freemind',\n  mmd: 'chemical/x-macromodel-input',\n  mmf: 'application/vnd.smaf',\n  mml: 'text/mathml',\n  mmod: 'chemical/x-macromodel-input',\n  mng: 'video/x-mng',\n  moc: 'text/x-moc',\n  mol: 'chemical/x-mdl-molfile',\n  mol2: 'chemical/x-mol2',\n  moo: 'chemical/x-mopac-out',\n  mop: 'chemical/x-mopac-input',\n  mopcrt: 'chemical/x-mopac-input',\n  mov: 'video/quicktime',\n  movie: 'video/x-sgi-movie',\n  mp2: 'audio/mpeg',\n  mp3: 'audio/mpeg',\n  mp4: 'video/mp4',\n  mpc: 'chemical/x-mopac-input',\n  mpe: 'video/mpeg',\n  mpeg: 'video/mpeg',\n  mpega: 'audio/mpeg',\n  mpg: 'video/mpeg',\n  mpga: 'audio/mpeg',\n  mph: 'application/x-comsol',\n  mpv: 'video/x-matroska',\n  ms: 'application/x-troff-ms',\n  msh: 'model/mesh',\n  msi: 'application/x-msi',\n  mvb: 'chemical/x-mopac-vib',\n  mxf: 'application/mxf',\n  mxu: 'video/vnd.mpegurl',\n  nb: 'application/mathematica',\n  nbp: 'application/mathematica',\n  nc: 'application/x-netcdf',\n  nef: 'image/x-nikon-nef',\n  nwc: 'application/x-nwc',\n  o: 'application/x-object',\n  oda: 'application/oda',\n  odb: 'application/vnd.oasis.opendocument.database',\n  odc: 'application/vnd.oasis.opendocument.chart',\n  odf: 'application/vnd.oasis.opendocument.formula',\n  odg: 'application/vnd.oasis.opendocument.graphics',\n  odi: 'application/vnd.oasis.opendocument.image',\n  odm: 'application/vnd.oasis.opendocument.text-master',\n  odp: 'application/vnd.oasis.opendocument.presentation',\n  ods: 'application/vnd.oasis.opendocument.spreadsheet',\n  odt: 'application/vnd.oasis.opendocument.text',\n  oga: 'audio/ogg',\n  ogg: 'audio/ogg',\n  ogv: 'video/ogg',\n  ogx: 'application/ogg',\n  old: 'application/x-trash',\n  one: 'application/onenote',\n  onepkg: 'application/onenote',\n  onetmp: 'application/onenote',\n  onetoc2: 'application/onenote',\n  opf: 'application/oebps-package+xml',\n  opus: 'audio/ogg',\n  orc: 'audio/csound',\n  orf: 'image/x-olympus-orf',\n  otf: 'application/font-sfnt',\n  otg: 'application/vnd.oasis.opendocument.graphics-template',\n  oth: 'application/vnd.oasis.opendocument.text-web',\n  otp: 'application/vnd.oasis.opendocument.presentation-template',\n  ots: 'application/vnd.oasis.opendocument.spreadsheet-template',\n  ott: 'application/vnd.oasis.opendocument.text-template',\n  oza: 'application/x-oz-application',\n  p: 'text/x-pascal',\n  p7r: 'application/x-pkcs7-certreqresp',\n  pac: 'application/x-ns-proxy-autoconfig',\n  pas: 'text/x-pascal',\n  pat: 'image/x-coreldrawpattern',\n  patch: 'text/x-diff',\n  pbm: 'image/x-portable-bitmap',\n  pcap: 'application/vnd.tcpdump.pcap',\n  pcf: 'application/x-font-pcf',\n  'pcf.Z': 'application/x-font-pcf',\n  pcx: 'image/pcx',\n  pdb: 'chemical/x-pdb',\n  pdf: 'application/pdf',\n  pfa: 'application/x-font',\n  pfb: 'application/x-font',\n  pfr: 'application/font-tdpfr',\n  pgm: 'image/x-portable-graymap',\n  pgn: 'application/x-chess-pgn',\n  pgp: 'application/pgp-encrypted',\n  php: '#application/x-httpd-php',\n  php3: '#application/x-httpd-php3',\n  php3p: '#application/x-httpd-php3-preprocessed',\n  php4: '#application/x-httpd-php4',\n  php5: '#application/x-httpd-php5',\n  phps: '#application/x-httpd-php-source',\n  pht: '#application/x-httpd-php',\n  phtml: '#application/x-httpd-php',\n  pk: 'application/x-tex-pk',\n  pl: 'text/x-perl',\n  pls: 'audio/x-scpls',\n  pm: 'text/x-perl',\n  png: 'image/png',\n  pnm: 'image/x-portable-anymap',\n  pot: 'text/plain',\n  potm: 'application/vnd.ms-powerpoint.template.macroEnabled.12',\n  potx: 'application/vnd.openxmlformats-officedocument.presentationml.template',\n  ppam: 'application/vnd.ms-powerpoint.addin.macroEnabled.12',\n  ppm: 'image/x-portable-pixmap',\n  pps: 'application/vnd.ms-powerpoint',\n  ppsm: 'application/vnd.ms-powerpoint.slideshow.macroEnabled.12',\n  ppsx: 'application/vnd.openxmlformats-officedocument.presentationml.slideshow',\n  ppt: 'application/vnd.ms-powerpoint',\n  pptm: 'application/vnd.ms-powerpoint.presentation.macroEnabled.12',\n  pptx: 'application/vnd.openxmlformats-officedocument.presentationml.presentation',\n  prf: 'application/pics-rules',\n  prt: 'chemical/x-ncbi-asn1-ascii',\n  ps: 'application/postscript',\n  psd: 'image/x-photoshop',\n  py: 'text/x-python',\n  pyc: 'application/x-python-code',\n  pyo: 'application/x-python-code',\n  qgs: 'application/x-qgis',\n  qt: 'video/quicktime',\n  qtl: 'application/x-quicktimeplayer',\n  ra: 'audio/x-pn-realaudio',\n  ram: 'audio/x-pn-realaudio',\n  rar: 'application/rar',\n  ras: 'image/x-cmu-raster',\n  rb: 'application/x-ruby',\n  rd: 'chemical/x-mdl-rdfile',\n  rdf: 'application/rdf+xml',\n  rdp: 'application/x-rdp',\n  rgb: 'image/x-rgb',\n  rhtml: '#application/x-httpd-eruby',\n  rm: 'audio/x-pn-realaudio',\n  roff: 'application/x-troff',\n  ros: 'chemical/x-rosdal',\n  rpm: 'application/x-redhat-package-manager',\n  rss: 'application/x-rss+xml',\n  rtf: 'application/rtf',\n  rtx: 'text/richtext',\n  rxn: 'chemical/x-mdl-rxnfile',\n  scala: 'text/x-scala',\n  sce: 'application/x-scilab',\n  sci: 'application/x-scilab',\n  sco: 'audio/csound',\n  scr: 'application/x-silverlight',\n  sct: 'text/scriptlet',\n  sd: 'chemical/x-mdl-sdfile',\n  sd2: 'audio/x-sd2',\n  sda: 'application/vnd.stardivision.draw',\n  sdc: 'application/vnd.stardivision.calc',\n  sdd: 'application/vnd.stardivision.impress',\n  sds: 'application/vnd.stardivision.chart',\n  sdw: 'application/vnd.stardivision.writer',\n  ser: 'application/java-serialized-object',\n  sfd: 'application/vnd.font-fontforge-sfd',\n  sfv: 'text/x-sfv',\n  sgf: 'application/x-go-sgf',\n  sgl: 'application/vnd.stardivision.writer-global',\n  sh: 'application/x-sh',\n  shar: 'application/x-shar',\n  shp: 'application/x-qgis',\n  shtml: 'text/html',\n  shx: 'application/x-qgis',\n  sid: 'audio/prs.sid',\n  sig: 'application/pgp-signature',\n  sik: 'application/x-trash',\n  silo: 'model/mesh',\n  sis: 'application/vnd.symbian.install',\n  sisx: 'x-epoc/x-sisx-app',\n  sit: 'application/x-stuffit',\n  sitx: 'application/x-stuffit',\n  skd: 'application/x-koan',\n  skm: 'application/x-koan',\n  skp: 'application/x-koan',\n  skt: 'application/x-koan',\n  sldm: 'application/vnd.ms-powerpoint.slide.macroEnabled.12',\n  sldx: 'application/vnd.openxmlformats-officedocument.presentationml.slide',\n  smi: 'application/smil+xml',\n  smil: 'application/smil+xml',\n  snd: 'audio/basic',\n  spc: 'chemical/x-galactic-spc',\n  spl: 'application/x-futuresplash',\n  spx: 'audio/ogg',\n  sql: 'application/x-sql',\n  src: 'application/x-wais-source',\n  srt: 'text/plain',\n  stc: 'application/vnd.sun.xml.calc.template',\n  std: 'application/vnd.sun.xml.draw.template',\n  sti: 'application/vnd.sun.xml.impress.template',\n  stw: 'application/vnd.sun.xml.writer.template',\n  sty: 'text/x-tex',\n  sv4cpio: 'application/x-sv4cpio',\n  sv4crc: 'application/x-sv4crc',\n  svg: 'image/svg+xml',\n  svgz: 'image/svg+xml',\n  sw: 'chemical/x-swissprot',\n  swf: 'application/x-shockwave-flash',\n  swfl: 'application/x-shockwave-flash',\n  sxc: 'application/vnd.sun.xml.calc',\n  sxd: 'application/vnd.sun.xml.draw',\n  sxg: 'application/vnd.sun.xml.writer.global',\n  sxi: 'application/vnd.sun.xml.impress',\n  sxm: 'application/vnd.sun.xml.math',\n  sxw: 'application/vnd.sun.xml.writer',\n  t: 'application/x-troff',\n  tar: 'application/x-tar',\n  taz: 'application/x-gtar-compressed',\n  tcl: 'application/x-tcl',\n  tex: 'text/x-tex',\n  texi: 'application/x-texinfo',\n  texinfo: 'application/x-texinfo',\n  text: 'text/plain',\n  tgf: 'chemical/x-mdl-tgf',\n  tgz: 'application/x-gtar-compressed',\n  thmx: 'application/vnd.ms-officetheme',\n  tif: 'image/tiff',\n  tiff: 'image/tiff',\n  tk: 'text/x-tcl',\n  tm: 'text/texmacs',\n  torrent: 'application/x-bittorrent',\n  tr: 'application/x-troff',\n  ts: 'video/MP2T',\n  tsp: 'application/dsptype',\n  tsv: 'text/tab-separated-values',\n  ttf: 'application/font-sfnt',\n  ttl: 'text/turtle',\n  txt: 'text/plain',\n  uls: 'text/iuls',\n  ustar: 'application/x-ustar',\n  val: 'chemical/x-ncbi-asn1-binary',\n  vcard: 'text/vcard',\n  vcd: 'application/x-cdlink',\n  vcf: 'text/vcard',\n  vcs: 'text/x-vcalendar',\n  vmd: 'chemical/x-vmd',\n  vms: 'chemical/x-vamas-iso14976',\n  vrm: 'x-world/x-vrml',\n  vrml: 'model/vrml',\n  vsd: 'application/vnd.visio',\n  vss: 'application/vnd.visio',\n  vst: 'application/vnd.visio',\n  vsw: 'application/vnd.visio',\n  wad: 'application/x-doom',\n  wasm: 'application/wasm',\n  wav: 'audio/wav',\n  wax: 'audio/x-ms-wax',\n  wbmp: 'image/vnd.wap.wbmp',\n  wbxml: 'application/vnd.wap.wbxml',\n  webm: 'video/webm',\n  wk: 'application/x-123',\n  wm: 'video/x-ms-wm',\n  wma: 'audio/x-ms-wma',\n  wmd: 'application/x-ms-wmd',\n  wml: 'text/vnd.wap.wml',\n  wmlc: 'application/vnd.wap.wmlc',\n  wmls: 'text/vnd.wap.wmlscript',\n  wmlsc: 'application/vnd.wap.wmlscriptc',\n  wmv: 'video/x-ms-wmv',\n  wmx: 'video/x-ms-wmx',\n  wmz: 'application/x-ms-wmz',\n  woff: 'application/font-woff',\n  wp5: 'application/vnd.wordperfect5.1',\n  wpd: 'application/vnd.wordperfect',\n  wrl: 'model/vrml',\n  wsc: 'text/scriptlet',\n  wvx: 'video/x-ms-wvx',\n  wz: 'application/x-wingz',\n  x3d: 'model/x3d+xml',\n  x3db: 'model/x3d+binary',\n  x3dv: 'model/x3d+vrml',\n  xbm: 'image/x-xbitmap',\n  xcf: 'application/x-xcf',\n  xcos: 'application/x-scilab-xcos',\n  xht: 'application/xhtml+xml',\n  xhtml: 'application/xhtml+xml',\n  xlam: 'application/vnd.ms-excel.addin.macroEnabled.12',\n  xlb: 'application/vnd.ms-excel',\n  xls: 'application/vnd.ms-excel',\n  xlsb: 'application/vnd.ms-excel.sheet.binary.macroEnabled.12',\n  xlsm: 'application/vnd.ms-excel.sheet.macroEnabled.12',\n  xlsx: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',\n  xlt: 'application/vnd.ms-excel',\n  xltm: 'application/vnd.ms-excel.template.macroEnabled.12',\n  xltx: 'application/vnd.openxmlformats-officedocument.spreadsheetml.template',\n  xml: 'application/xml',\n  xpi: 'application/x-xpinstall',\n  xpm: 'image/x-xpixmap',\n  xsd: 'application/xml',\n  xsl: 'application/xslt+xml',\n  xslt: 'application/xslt+xml',\n  xspf: 'application/xspf+xml',\n  xtel: 'chemical/x-xtel',\n  xul: 'application/vnd.mozilla.xul+xml',\n  xwd: 'image/x-xwindowdump',\n  xyz: 'chemical/x-xyz',\n  xz: 'application/x-xz',\n  zip: 'application/zip',\n};\n\nexport default mimeTypes;\n", "import { Transform } from 'stream';\nimport { makeStreamError<PERSON><PERSON><PERSON>, pipeStreamWithErrorHandle } from '../utils';\n\nfunction createReadNCbTransformer(readCb: (n: number) => void) {\n  return new Transform({\n    async transform(chunk, _encoding, callback) {\n      const chunkSize = chunk.length;\n      readCb(chunkSize);\n      this.push(chunk);\n      callback();\n    },\n  });\n}\n\nexport function createReadNReadStream(\n  stream: NodeJS.ReadableStream,\n  readCb: (n: number) => void\n) {\n  const readCbTransformer = createReadNCbTransformer(readCb);\n  return pipeStreamWithErrorHandle(\n    stream,\n    readCbTransformer,\n    'createReadNReadStream'\n  );\n\n  /**\n   * Don't use the below code.\n   *\n   * 1. The readable stream will be flowing mode after adding a 'data' event listener to it.\n   * 2. The stream will be paused after calling `pause()` method.\n   * 3. The stream will not change to flowing mode when adding a 'data' event listener to it.\n   */\n  // stream.on('data', (d) => {\n  //   readCb(d.length);\n  // });\n  // stream.pause();\n}\n", "import * as browserCRC from './crc.browser';\nimport * as nodejsCRC from '../nodejs/crc.node';\n\ntype CRCModule = typeof nodejsCRC;\nexport type CRCCls = nodejsCRC.CRC;\nlet crcModule = null as unknown as CRCModule;\n\nif (process.env.TARGET_ENVIRONMENT === 'node') {\n  crcModule = nodejsCRC;\n} else {\n  crcModule = browserCRC as unknown as CRCModule;\n}\n\nconst { CRC, combineCrc64 } = crcModule;\nexport { CRC, combineCrc64 };\n", "import TosClientError from '../TosClientError';\nimport { crc64 } from 'tos-crc64-js';\nexport { combineCrc64 } from 'tos-crc64-js';\n\nexport class CRC {\n  private value = '0';\n\n  reset() {\n    this.value = '0';\n  }\n\n  async updateBlob(): Promise<string> {\n    throw new TosClientError('Not implemented in node.js environment.');\n  }\n\n  update(value: Buffer): string {\n    this.value = crc64(value, this.value);\n    return this.value;\n  }\n\n  getCrc64(): string {\n    return this.value;\n  }\n}\n", "import { Readable, Transform } from 'stream';\nimport { makeStreamErrorHandler, pipeStreamWithErrorHandle } from '../utils';\nimport { IRateLimiter } from '../interface';\n\nexport interface DefaultRateLimiter {\n  rate: number;\n  capacity: number;\n  currentAmount: number;\n  lastConsumeTime: number;\n}\n\nconst minRate = 1024;\nconst minCapacity = 10 * 1024;\n\n/**\n *\n * @param capacity  minValue 10KB. unit byte\n * @param rate   minValue 1KB. unit byte/s\n * @returns\n */\nexport function createDefaultRateLimiter(\n  capacity: number,\n  rate: number\n): IRateLimiter {\n  const realCapacity = Math.max(minCapacity, capacity);\n  const realRate = Math.max(minRate, rate);\n  const d: DefaultRateLimiter = {\n    rate: realRate,\n    capacity: realCapacity,\n    currentAmount: realCapacity,\n    lastConsumeTime: Date.now(),\n  };\n\n  return {\n    Acquire: async (want) => {\n      if (want > d.capacity) {\n        want = d.capacity;\n      }\n\n      const now = Date.now();\n      const increment = Math.floor(((now - d.lastConsumeTime) / 1000) * d.rate);\n\n      if (increment + d.currentAmount > d.capacity) {\n        d.currentAmount = d.capacity;\n      } else {\n        d.currentAmount += increment;\n      }\n\n      if (want > d.currentAmount) {\n        const timeToWaitSec = (want - d.currentAmount) / d.rate;\n\n        return { ok: false, timeToWait: Math.ceil(timeToWaitSec * 1000) };\n      }\n\n      d.lastConsumeTime = now;\n      d.currentAmount = d.currentAmount - want;\n\n      return {\n        ok: true,\n        timeToWait: 0,\n      };\n    },\n  };\n}\n\nfunction createRateLimiterTransform(rateLimiter: IRateLimiter) {\n  return new Transform({\n    async transform(chunk, _encoding, callback) {\n      try {\n        const chunkSize = chunk.length;\n        let finished = false;\n        while (!finished) {\n          const { ok, timeToWait } = await rateLimiter.Acquire(chunkSize);\n\n          if (!ok) {\n            await wait(timeToWait);\n          }\n          finished = ok;\n        }\n\n        this.push(chunk);\n        callback();\n      } catch (error: any) {\n        callback(error);\n      }\n    },\n  });\n}\n\nexport function createRateLimiterStream(\n  stream: NodeJS.ReadableStream | Readable,\n  rateLimiter: IRateLimiter\n) {\n  const pipeRateLimit = createRateLimiterTransform(rateLimiter);\n\n  return pipeStreamWithErrorHandle(\n    stream,\n    pipeRateLimit,\n    'createRateLimiterStream'\n  );\n}\n\nexport function wait(milliseconds: number) {\n  return new Promise((r) => {\n    setTimeout(() => r(''), milliseconds);\n  });\n}\n", "import * as moduleBrowser from './rate-limiter.browser';\nimport * as moduleNode from '../nodejs/rate-limiter';\nimport { IRateLimiter } from '../interface';\n\ninterface RateLimiterModule {\n  createDefaultRateLimiter(capacity: number, rate: number): IRateLimiter;\n  createRateLimiterStream(\n    stream: NodeJS.ReadableStream,\n    rateLimiter: IRateLimiter\n  ): NodeJS.ReadableStream;\n}\n\nlet rateLimiter = null as unknown as RateLimiterModule;\nif (process.env.TARGET_ENVIRONMENT === 'node') {\n  rateLimiter = moduleNode as unknown as RateLimiterModule;\n} else {\n  rateLimiter = moduleBrowser as unknown as RateLimiterModule;\n}\n\nconst { createDefaultRateLimiter, createRateLimiterStream } = rateLimiter;\n\nexport { createDefaultRateLimiter, createRateLimiterStream };\nexport type { IRateLimiter };\n", "import { Readable } from 'stream';\n\nexport class Buffer2Stream extends Readable {\n  lastPos = 0;\n\n  constructor(private buf: Buffer) {\n    super();\n  }\n\n  _read(n: number) {\n    const totalSize = this.buf.length;\n    let actualN = Math.min(n, totalSize - this.lastPos);\n\n    if (this.lastPos >= totalSize) {\n      this.push(null);\n      return;\n    }\n\n    this.push(this.buf.slice(this.lastPos, this.lastPos + actualN));\n    this.lastPos += actualN;\n  }\n}\n", "import { Transform } from 'stream';\nimport { CRCCls } from '../universal/crc';\nimport { makeStreamError<PERSON><PERSON><PERSON>, pipeStreamWithErrorHandle } from '../utils';\n\nfunction createReadCbTransformer(readCb: (chunk: Buffer) => void) {\n  return new Transform({\n    async transform(chunk, _encoding, callback) {\n      readCb(chunk);\n      this.push(chunk);\n      callback();\n    },\n  });\n}\n\nexport function createCrcReadStream(\n  stream: NodeJS.ReadableStream,\n  crc: CRCCls\n) {\n  const readCbTransformer = createReadCbTransformer((chunk: Buffer) =>\n    crc.update(chunk)\n  );\n\n  return pipeStreamWithErrorHandle(\n    stream,\n    readCbTransformer,\n    'createCrcReadStream'\n  );\n}\n", "import { TierType } from '../../TosExportEnum';\n\nexport interface RestoreInfo {\n  RestoreStatus: RestoreStatus;\n  RestoreParam?: RestoreParam;\n}\n\nexport type RestoreStatus = {\n  OngoingRequest: boolean;\n  ExpiryDate?: string;\n};\n\nexport type RestoreParam = {\n  RequestDate: string;\n  ExpiryDays: number;\n  Tier: TierType;\n};\n\nexport enum TosHeader {\n  HeaderRestore = 'x-tos-restore',\n  HeaderRestoreExpiryDays = 'x-tos-restore-expiry-days',\n  HeaderRestoreRequestDate = 'x-tos-restore-request-date',\n  HeaderRestoreTier = 'x-tos-restore-tier',\n  HeaderProjectName = 'x-tos-project-name',\n  HeaderReplicationStatus = 'x-tos-replication-status',\n}\n\nexport const RestoreOngoingRequestTrueStr = 'ongoing-request=\"true\"';\nexport const RestoreOngoingRequestFalseReg = 'ongoing-request=\"false\"';\n", "import TosClientError from '../../TosClientError';\nimport mimeTypes from '../../mime-types';\nimport { Headers, SupportObjectBody } from '../../interface';\nimport { createReadNReadStream } from '../../nodejs/EmitReadStream';\nimport { isBuffer, isBlob, isReadable } from '../../utils';\nimport { CRC, CRCCls } from '../../universal/crc';\nimport {\n  IRateLimiter,\n  createRateLimiterStream,\n} from '../../universal/rate-limiter';\nimport { Buffer2Stream } from '../../nodejs/buffer2Stream';\nimport { createCrcReadStream } from '../../nodejs/CrcReadStream';\nimport {\n  RestoreInfo,\n  RestoreOngoingRequestTrueStr,\n  TosHeader,\n} from './sharedTypes';\nimport { TierType } from '../../TosExportEnum';\n\nexport const getObjectInputKey = (input: string | { key: string }): string => {\n  return typeof input === 'string' ? input : input.key;\n};\n\nexport const DEFAULT_CONTENT_TYPE = 'application/octet-stream';\n\nexport function lookupMimeType(key: string) {\n  const lastDotIndex = key.lastIndexOf('.');\n\n  if (lastDotIndex <= 0) {\n    return undefined;\n  }\n\n  const extName = key.slice(lastDotIndex + 1).toLowerCase();\n\n  return mimeTypes[extName];\n}\n\n// for all object methods\nexport function validateObjectName(input: { key: string } | string) {\n  const key = typeof input === 'string' ? input : input.key;\n  if (key.length < 1) {\n    throw new TosClientError(\n      'invalid object name, the length must be greater than 1'\n    );\n  }\n}\n\nexport function getSize(body: unknown, headers?: Headers) {\n  if (isBuffer(body)) {\n    return body.length;\n  }\n  if (isBlob(body)) {\n    return body.size;\n  }\n  if (headers && headers['content-length']) {\n    const v = +headers['content-length'];\n    if (v >= 0) {\n      return v;\n    }\n  }\n  return null;\n}\n\ninterface GetNewBodyConfigIn<T> {\n  body: T;\n  dataTransferCallback: (n: number) => void;\n  beforeRetry?: () => void;\n  makeRetryStream?: () => NodeJS.ReadableStream | undefined;\n  enableCRC: boolean;\n  rateLimiter?: IRateLimiter;\n}\ninterface GetNewBodyConfigOut<T> {\n  body: T | NodeJS.ReadableStream;\n  beforeRetry?: () => void;\n  makeRetryStream?: () => NodeJS.ReadableStream | undefined;\n  crc?: CRCCls;\n}\n\ninterface GetEmitReadBodyConfigIn<T> {\n  body: T;\n  dataTransferCallback: (n: number) => void;\n  makeRetryStream?: () => NodeJS.ReadableStream | undefined;\n  rateLimiter?: IRateLimiter;\n}\ninterface GetEmitReadBodyConfigOut<T> {\n  body: T | NodeJS.ReadableStream;\n  makeRetryStream?: () => NodeJS.ReadableStream | undefined;\n}\n\nexport function getEmitReadBodyConfig<T extends SupportObjectBody>({\n  body,\n  dataTransferCallback,\n  makeRetryStream,\n  rateLimiter,\n}: GetEmitReadBodyConfigIn<T>): GetEmitReadBodyConfigOut<T> {\n  let newBody: T | NodeJS.ReadableStream = body;\n\n  const getDefaultRet = () => ({\n    body: newBody,\n    makeRetryStream: undefined,\n  });\n\n  if (process.env.TARGET_ENVIRONMENT !== 'node') {\n    return getDefaultRet();\n  }\n\n  if (isBuffer(newBody)) {\n    const bodyBuf = newBody;\n    makeRetryStream = () => new Buffer2Stream(bodyBuf);\n    newBody = new Buffer2Stream(bodyBuf);\n  }\n\n  if (isReadable(newBody)) {\n    if (rateLimiter && isValidRateLimiter(rateLimiter)) {\n      newBody = createRateLimiterStream(newBody, rateLimiter);\n    }\n    newBody = createReadNReadStream(newBody, dataTransferCallback);\n\n    if (makeRetryStream) {\n      const oriMakeRetryStream = makeRetryStream;\n      return {\n        body: newBody,\n        makeRetryStream: () => {\n          let stream = oriMakeRetryStream();\n          if (!stream) {\n            return stream;\n          }\n\n          if (rateLimiter && isValidRateLimiter(rateLimiter)) {\n            stream = createRateLimiterStream(stream, rateLimiter);\n          }\n          stream = createReadNReadStream(stream, dataTransferCallback);\n          return stream;\n        },\n      };\n    }\n  }\n\n  return getDefaultRet();\n}\n\nexport async function getCRCBodyConfig<T extends SupportObjectBody>({\n  body,\n  beforeRetry,\n  makeRetryStream,\n  enableCRC,\n}: GetNewBodyConfigIn<T>): Promise<GetNewBodyConfigOut<T>> {\n  if (process.env.TARGET_ENVIRONMENT === 'browser' || !enableCRC) {\n    return {\n      body,\n      beforeRetry,\n      makeRetryStream,\n    };\n  }\n\n  let newBody: T | NodeJS.ReadableStream = body;\n  const crc = new CRC();\n  if (isReadable(body)) {\n    newBody = createCrcReadStream(body, crc);\n    if (makeRetryStream) {\n      const oriMakeRetryStream = makeRetryStream;\n      makeRetryStream = () => {\n        const stream = oriMakeRetryStream();\n        if (!stream) {\n          return stream;\n        }\n        return createCrcReadStream(stream, crc);\n      };\n    }\n  }\n\n  return {\n    body: newBody,\n    beforeRetry: () => {\n      crc.reset();\n      beforeRetry?.();\n    },\n    makeRetryStream,\n    crc,\n  };\n}\n\nexport async function getNewBodyConfig<T extends SupportObjectBody>(\n  input: GetNewBodyConfigIn<T>\n): Promise<GetNewBodyConfigOut<T>> {\n  const config1 = getEmitReadBodyConfig(input);\n  input = { ...input, ...config1 } as GetNewBodyConfigIn<T>;\n  const config2 = getCRCBodyConfig(input);\n  return config2;\n}\n\nexport function getCopySourceHeaderValue(srcBucket: string, srcKey: string) {\n  return `/${srcBucket}/${encodeURIComponent(srcKey)}`;\n}\n\nexport function isValidRateLimiter(rateLimiter?: IRateLimiter) {\n  if (!rateLimiter?.Acquire || !(rateLimiter?.Acquire instanceof Function)) {\n    throw new TosClientError(`The rateLimiter is not valid function`);\n  }\n  return true;\n}\n\nexport function validateCheckpoint(cp: undefined | string | Object) {\n  if (process.env.TARGET_ENVIRONMENT === 'node' && typeof cp === 'object') {\n    console.warn(\n      `The \\`checkpoint\\` parameter should be passed as a string in node.js environment, representing a file or directory.` +\n        `Passing a checkpoint object to it will be removed in the future.`\n    );\n  }\n}\n\nexport const getRestoreInfoFromHeaders = (headers: Headers) => {\n  if (!headers) return;\n  const headerStoreValue = headers?.[TosHeader.HeaderRestore];\n\n  if (headerStoreValue) {\n    /**\n     * value example:\n     * X-Tos-Restore: ongoing-request=\"false\", expiry-date=\"Fri, 19 Apr 2024 00:00:00 GMT\"\n     */\n    const ExpiryDate =\n      (headerStoreValue ?? '').split('\",')[1]?.split?.('=')?.[1] ?? '';\n    const OngoingRequest =\n      headerStoreValue?.trim() === RestoreOngoingRequestTrueStr ? true : false;\n    const restoreInfo: RestoreInfo = {\n      RestoreStatus: {\n        OngoingRequest,\n        ExpiryDate,\n      },\n    };\n    if (OngoingRequest) {\n      restoreInfo.RestoreParam = {\n        ExpiryDays: headers[TosHeader.HeaderRestoreExpiryDays]\n          ? Number(headers[TosHeader.HeaderRestoreExpiryDays])\n          : 0,\n        RequestDate: headers[TosHeader.HeaderRestoreRequestDate] ?? '',\n        Tier: headers[TosHeader.HeaderRestoreTier] as TierType,\n      };\n    }\n    return restoreInfo;\n  }\n  return;\n};\n", "import {\n  ACLType,\n  CannedType,\n  PermissionType,\n  StorageClassType,\n} from './TosExportEnum';\n\nexport type Headers = { [key: string]: string | undefined };\n\nexport interface AclInterface {\n  Owner: { ID: string };\n  Grants: {\n    Grantee: {\n      ID?: string;\n      DisplayName?: string;\n      Type: string;\n      Canned?: CannedType;\n    };\n    Permission: PermissionType;\n  }[];\n  /**\n   * @private unstable property only for bucket ACL\n   */\n  BucketAclDelivered?: boolean;\n  /**\n   * @private unstable property only for object ACL\n   */\n  IsDefault?: boolean;\n}\n\nexport type Acl = ACLType;\nexport type StorageClass = StorageClassType;\n\nexport type ServerSideEncryption = 'AES256';\n\nexport interface DataTransferStatus {\n  /**\n   * has read or wrote bytes\n   */\n  consumedBytes: number;\n\n  /**\n   * totalBytes maybe 0 or -1.\n   * `-1` means unkown totalBytes, for example when starting to download an object\n   */\n  totalBytes: number;\n\n  /**\n   * transferred bytes in this transfer\n   */\n  rwOnceBytes: number;\n\n  type: DataTransferType;\n}\n\nexport enum DataTransferType {\n  Started = 1, // data transfer start\n  Rw = 2, // one transfer\n  Succeed = 3, // data transfer succeed\n  Failed = 4, // data transfer failed\n}\n\nexport type SupportObjectBody =\n  | File\n  | Blob\n  | Buffer\n  | NodeJS.ReadableStream\n  | undefined;\n\nexport type StringKeys<T> = Extract<\n  { [K in keyof T]: T[K] extends string | undefined ? K : never }[keyof T],\n  string\n>;\n\nexport interface IRateLimiter {\n  Acquire: (want: number) => Promise<{\n    ok: boolean;\n    /**\n     * unit: milliseconds\n     */\n    timeToWait: number;\n  }>;\n}\n", "import createDebug from 'debug';\n\nexport const TOS = createDebug('TOS');\n", "import cryptoHmacSha256 from 'crypto-js/hmac-sha256';\nimport cryptoHashSha256 from 'crypto-js/sha256';\nimport cryptoHashMd5 from 'crypto-js/md5';\nimport cryptoEncBase64 from 'crypto-js/enc-base64';\nimport cryptoEncHex from 'crypto-js/enc-hex';\nimport cryptoEncUtf8 from 'crypto-js/enc-utf8';\nimport TosClientError from '../TosClientError';\nimport { isBuffer } from '../utils';\n\nfunction getEnc(coding: 'utf-8' | 'base64' | 'hex') {\n  switch (coding) {\n    case 'utf-8':\n      return cryptoEncUtf8;\n    case 'base64':\n      return cryptoEncBase64;\n    case 'hex':\n      return cryptoEncHex;\n    default:\n      throw new TosClientError('The coding is not supported');\n  }\n}\n\nfunction decode(v: any, decoding?: 'base64' | 'hex'): string {\n  if (!decoding) {\n    return v;\n  }\n\n  return v.toString(getEnc(decoding));\n}\n\nexport const hmacSha256 = function hmacSha256(\n  key: string,\n  message: string,\n  decoding?: 'base64' | 'hex'\n) {\n  return decode(cryptoHmacSha256(message, key), decoding);\n};\n\nexport const hashSha256 = function hashSha256(\n  message: string,\n  decoding?: 'base64' | 'hex'\n) {\n  return decode(cryptoHashSha256(message), decoding);\n};\n\nexport const hashMd5 = function hashMd5(\n  message: string | Buffer,\n  decoding?: 'base64' | 'hex'\n) {\n  if (isBuffer(message)) {\n    throw new TosClientError('not support buffer in browser environment');\n  }\n\n  return decode(cryptoHashMd5(message), decoding);\n};\n\nexport const parse = function parse(\n  str: string,\n  encoding: 'utf-8' | 'base64' | 'hex'\n) {\n  return getEnc(encoding).parse(str);\n};\n\nexport const stringify = function stringify(\n  str: CryptoJS.lib.WordArray,\n  decoding: 'utf-8' | 'base64' | 'hex'\n) {\n  return getEnc(decoding).stringify(str);\n};\n", "import crypto from 'crypto';\n\nfunction digest(v: any, decoding?: 'base64' | 'hex'): string {\n  if (!decoding) {\n    return v.digest();\n  }\n  return v.digest(decoding);\n}\n\nexport const hmacSha256 = function hmacSha256(\n  key: string,\n  message: string,\n  decoding?: 'base64' | 'hex'\n) {\n  return digest(crypto.createHmac('sha256', key).update(message), decoding);\n};\n\nexport const hashSha256 = function hashSha256(\n  message: string,\n  decoding?: 'base64' | 'hex'\n) {\n  return digest(crypto.createHash('sha256').update(message), decoding);\n};\n\nexport const hashMd5 = function hashMd5(\n  message: string | Buffer,\n  decoding?: 'base64' | 'hex'\n) {\n  return digest(crypto.createHash('md5').update(message), decoding);\n};\n\nexport const parse = function parse(\n  str: string,\n  encoding: 'utf-8' | 'base64' | 'hex'\n) {\n  return Buffer.from(str, encoding);\n};\n\nexport const stringify = function stringify(\n  str: Buffer,\n  decoding: 'utf-8' | 'base64' | 'hex'\n) {\n  return str.toString(decoding);\n};\n", "import * as cryptoBrowser from './crypto.browser';\nimport * as cryptoNode from '../nodejs/crypto.nodejs';\n\ninterface CryptoModule {\n  hmacSha256: (\n    key: string,\n    message: string,\n    decoding?: 'base64' | 'hex'\n  ) => string;\n  hashSha256: (message: string, decoding?: 'base64' | 'hex') => string;\n  hashMd5: (message: string | Buffer, decoding?: 'base64' | 'hex') => string;\n  parse: (str: string, encoding: 'utf-8' | 'base64' | 'hex') => string;\n  stringify: (str: string, decoding: 'utf-8' | 'base64' | 'hex') => string;\n}\n\nlet crypto = null as unknown as CryptoModule;\nif (process.env.TARGET_ENVIRONMENT === 'node') {\n  crypto = cryptoNode as unknown as CryptoModule;\n} else {\n  crypto = cryptoBrowser as unknown as CryptoModule;\n}\n\nconst { hmacSha256, hashSha256, hashMd5, parse, stringify } = crypto;\n\nexport { hmacSha256, hashSha256, hashMd5, parse, stringify };\n", "import { getNewBodyConfig, getSize } from '../utils';\nimport TOSBase from '../../base';\nimport TosClientError from '../../../TosClientError';\nimport { ReadStream, Stats } from 'fs';\nimport * as fsp from '../../../nodejs/fs-promises';\nimport { DataTransferStatus, DataTransferType } from '../../../interface';\nimport {\n  checkCRC64WithHeaders,\n  fillRequestHeaders,\n  isReadable,\n  makeRetryStreamAutoClose,\n  normalizeHeadersKey,\n  safeAwait,\n  tryDestroy,\n} from '../../../utils';\nimport { retryNamespace } from '../../../axios';\nimport { hashMd5 } from '../../../universal/crypto';\nimport { IRateLimiter } from '../../../universal/rate-limiter';\n\nexport interface UploadPartInput {\n  body: Blob | Buffer | NodeJS.ReadableStream;\n  bucket?: string;\n  key: string;\n  partNumber: number;\n  uploadId: string;\n  dataTransferStatusChange?: (status: DataTransferStatus) => void;\n  /**\n   * the simple progress feature\n   * percent is [0, 1].\n   *\n   * since uploadPart is stateless, so if `uploadPart` fail and you retry it,\n   * `percent` will start from 0 again rather than from the previous value.\n   */\n  progress?: (percent: number) => void;\n  /**\n   * unit: bit/s\n   * server side traffic limit\n   **/\n  trafficLimit?: number;\n  /**\n   * only works for nodejs environment\n   */\n  rateLimiter?: IRateLimiter;\n\n  ssecAlgorithm?: string;\n  ssecKey?: string;\n  ssecKeyMD5?: string;\n\n  headers?: {\n    [key: string]: string | undefined;\n    'content-length'?: string;\n    'content-md5'?: string;\n    'x-tos-server-side-encryption-customer-algorithm'?: string;\n    'x-tos-server-side-encryption-customer-key'?: string;\n    'x-tos-server-side-encryption-customer-key-MD5'?: string;\n  };\n}\n\nexport interface UploadPartInputInner extends UploadPartInput {\n  makeRetryStream?: () => NodeJS.ReadableStream | undefined;\n  beforeRetry?: () => void;\n  /**\n   * default: false\n   */\n  enableContentMD5?: boolean;\n}\n\nexport interface UploadPartOutput {\n  partNumber: number;\n  ETag: string;\n  ssecAlgorithm?: string;\n  ssecKeyMD5?: string;\n  hashCrc64ecma: string;\n  serverSideEncryption?: string;\n  serverSideEncryptionKeyId?: string;\n  /** @private unstable */\n  serverSideDataEncryption?: string;\n}\n\nexport async function _uploadPart(this: TOSBase, input: UploadPartInputInner) {\n  const { uploadId, partNumber, body, enableContentMD5 = false } = input;\n  const headers = normalizeHeadersKey(input.headers);\n  input.headers = headers;\n  fillRequestHeaders(input, [\n    'trafficLimit',\n    'ssecAlgorithm',\n    'ssecKey',\n    'ssecKeyMD5',\n  ]);\n\n  const size = getSize(body);\n  if (size && headers['content-length'] == null) {\n    headers['content-length'] = size.toFixed(0);\n  }\n  if (enableContentMD5 && headers['content-md5'] == null) {\n    // current only support in nodejs\n    if (\n      process.env.TARGET_ENVIRONMENT === 'node' &&\n      isReadable(body) &&\n      input.makeRetryStream\n    ) {\n      const newStream = input.makeRetryStream();\n      if (newStream) {\n        let allContent = Buffer.from([]);\n        for await (const chunk of newStream) {\n          allContent = Buffer.concat([\n            allContent,\n            typeof chunk === 'string' ? Buffer.from(chunk) : chunk,\n          ]);\n        }\n        const md5 = hashMd5(allContent, 'base64');\n        headers['content-md5'] = md5;\n      }\n    } else {\n      console.warn(`current not support enableMD5Checksum`);\n    }\n  }\n\n  const totalSize = getSize(input.body, headers);\n  const totalSizeValid = totalSize != null;\n  if (!totalSizeValid && (input.dataTransferStatusChange || input.progress)) {\n    console.warn(\n      `Don't get totalSize of uploadPart's body, the \\`dataTransferStatusChange\\` callback will not trigger. You can use \\`uploadPartFromFile\\` instead`\n    );\n  }\n\n  let consumedBytes = 0;\n  const { dataTransferStatusChange, progress } = input;\n  const triggerDataTransfer = (\n    type: DataTransferType,\n    rwOnceBytes: number = 0\n  ) => {\n    // request cancel will make rwOnceBytes < 0 in browser\n    if (!totalSizeValid || rwOnceBytes < 0) {\n      return;\n    }\n    if (!dataTransferStatusChange && !progress) {\n      return;\n    }\n    consumedBytes += rwOnceBytes;\n\n    dataTransferStatusChange?.({\n      type,\n      rwOnceBytes,\n      consumedBytes,\n      totalBytes: totalSize,\n    });\n\n    const progressValue = (() => {\n      if (totalSize === 0) {\n        if (type === DataTransferType.Succeed) {\n          return 1;\n        }\n        return 0;\n      }\n      return consumedBytes / totalSize;\n    })();\n    if (progressValue === 1) {\n      if (type === DataTransferType.Succeed) {\n        progress?.(progressValue);\n      } else {\n        // not exec progress\n      }\n    } else {\n      progress?.(progressValue);\n    }\n  };\n  const bodyConfig = await getNewBodyConfig({\n    body: input.body,\n    dataTransferCallback: (n) => triggerDataTransfer(DataTransferType.Rw, n),\n    beforeRetry: input.beforeRetry,\n    makeRetryStream: input.makeRetryStream,\n    enableCRC: this.opts.enableCRC,\n    rateLimiter: input.rateLimiter,\n  });\n\n  triggerDataTransfer(DataTransferType.Started);\n  const task = async () => {\n    const res = await this._fetchObject<UploadPartOutput>(\n      input,\n      'PUT',\n      { partNumber, uploadId },\n      headers,\n      bodyConfig.body,\n      {\n        handleResponse: (res) => ({\n          partNumber,\n          ETag: res.headers.etag,\n          serverSideEncryption: res.headers['x-tos-server-side-encryption'],\n          serverSideDataEncryption:\n            res.headers['x-tos-server-side-data-encryption'],\n          serverSideEncryptionKeyId:\n            res.headers['x-tos-server-side-encryption-kms-key-id'],\n          ssecAlgorithm:\n            res.headers['x-tos-server-side-encryption-customer-algorithm'],\n          ssecKeyMD5:\n            res.headers['x-tos-server-side-encryption-customer-key-MD5'],\n          hashCrc64ecma: res.headers['x-tos-hash-crc64ecma'],\n        }),\n        axiosOpts: {\n          [retryNamespace]: {\n            beforeRetry: () => {\n              consumedBytes = 0;\n              bodyConfig.beforeRetry?.();\n            },\n            makeRetryStream: bodyConfig.makeRetryStream,\n          },\n          onUploadProgress: (event) => {\n            triggerDataTransfer(\n              DataTransferType.Rw,\n              event.loaded - consumedBytes\n            );\n          },\n        },\n      }\n    );\n    if (this.opts.enableCRC && bodyConfig.crc) {\n      checkCRC64WithHeaders(bodyConfig.crc, res.headers);\n    }\n    return res;\n  };\n  const [err, res] = await safeAwait(task());\n\n  // FAQ: no etag\n  if (process.env.TARGET_ENVIRONMENT === 'browser') {\n    if (res && !res.data.ETag) {\n      throw new TosClientError(\n        \"No ETag in uploadPart's response headers, please see https://www.volcengine.com/docs/6349/127737 to fix CORS problem\"\n      );\n    }\n  }\n\n  if (err || !res) {\n    triggerDataTransfer(DataTransferType.Failed);\n    throw err;\n  }\n\n  triggerDataTransfer(DataTransferType.Succeed);\n  return res;\n}\n\nexport async function uploadPart(this: TOSBase, input: UploadPartInput) {\n  return _uploadPart.call(this, input);\n}\n\ninterface UploadPartFromFileInput extends Omit<UploadPartInput, 'body'> {\n  filePath: string;\n  /**\n   * default: 0\n   */\n  offset?: number;\n\n  /**\n   * default: file size\n   */\n  partSize?: number;\n}\nexport async function uploadPartFromFile(\n  this: TOSBase,\n  input: UploadPartFromFileInput\n) {\n  if (process.env.TARGET_ENVIRONMENT !== 'node') {\n    throw new TosClientError(\n      \"uploadPartFromFile doesn't support in browser environment\"\n    );\n  }\n\n  const stats: Stats = await fsp.stat(input.filePath);\n  const start = input.offset ?? 0;\n  const end = Math.min(stats.size, start + (input.partSize ?? stats.size));\n  const makeRetryStream = makeRetryStreamAutoClose(() =>\n    fsp.createReadStream(input.filePath, {\n      start,\n      end: end - 1,\n    })\n  );\n\n  try {\n    return await _uploadPart.call(this, {\n      ...input,\n      body: makeRetryStream.make(),\n      headers: {\n        ...(input.headers || {}),\n        ['content-length']: `${end - start}`,\n      },\n      makeRetryStream: makeRetryStream.make,\n    });\n  } catch (err) {\n    tryDestroy(makeRetryStream.getLastStream(), err);\n    throw err;\n  }\n}\n", "import TosClientError from '../../../TosClientError';\nimport { fillRequestHeaders } from '../../../utils';\nimport TOSBase from '../../base';\n\nexport interface CompleteMultipartUploadInput {\n  bucket?: string;\n  key: string;\n  uploadId: string;\n  parts: {\n    eTag: string;\n    partNumber: number;\n  }[];\n  /**\n   * when true `parts` param need to be empty array\n   */\n  completeAll?: boolean;\n\n  callback?: string;\n  callbackVar?: string;\n  forbidOverwrite?: boolean;\n\n  headers?: {\n    ['x-tos-forbid-overwrite']?: string;\n  };\n}\n\nexport type UploadedPart = {\n  PartNumber: number;\n  ETag: string;\n};\n\nexport interface CompleteMultipartUploadOutput {\n  Bucket: string;\n  Key: string;\n  ETag: string;\n  Location: string;\n  VersionID?: string;\n  HashCrc64ecma?: string;\n  /** the field has a value when completeAll is true\n   * when specify callback, the field will not has a value\n   */\n  CompletedParts?: UploadedPart[];\n  CallbackResult?: string;\n}\n\nexport async function completeMultipartUpload(\n  this: TOSBase,\n  input: CompleteMultipartUploadInput\n) {\n  input.headers = input.headers ?? {};\n  fillRequestHeaders(input, ['callback', 'callbackVar', 'forbidOverwrite']);\n\n  const handleResponse = (response: {\n    headers: { [x: string]: any };\n    data: CompleteMultipartUploadOutput;\n  }) => {\n    const bucket = input.bucket || this.opts.bucket || '';\n    const headers = response.headers;\n    const result: CompleteMultipartUploadOutput = {\n      ...{\n        VersionID: headers['x-tos-version-id'],\n        ETag: headers['etag'],\n        Bucket: bucket,\n        Location: headers['location'],\n        HashCrc64ecma: headers['x-tos-hash-crc64ecma'],\n        Key: input.key,\n      },\n      ...response.data,\n    };\n    if (input.callback) {\n      result.CallbackResult = `${JSON.stringify(response.data)}`;\n    }\n    return result;\n  };\n  if (input.completeAll) {\n    if (input.parts?.length > 0) {\n      throw new TosClientError(\n        `Should not specify both 'completeAll' and 'parts' params.`\n      );\n    }\n    return this._fetchObject<CompleteMultipartUploadOutput>(\n      input,\n      'POST',\n      {\n        uploadId: input.uploadId,\n      },\n      {\n        ...input.headers,\n        'x-tos-complete-all': 'yes',\n      },\n      undefined,\n      {\n        handleResponse,\n      }\n    );\n  }\n\n  return this._fetchObject<CompleteMultipartUploadOutput>(\n    input,\n    'POST',\n    {\n      uploadId: input.uploadId,\n    },\n    {\n      ...input.headers,\n    },\n    {\n      Parts: input.parts.map((it) => ({\n        ETag: it.eTag,\n        PartNumber: it.partNumber,\n      })),\n    },\n    {\n      handleResponse,\n    }\n  );\n}\n", "import { Readable } from 'stream';\n\nexport class EmptyReadStream extends Readable {\n  _read(): void {\n    this.push(null);\n  }\n}\n", "import TOSBase, { TosResponse } from '../../base';\nimport {\n  createMultipartUpload,\n  CreateMultipartUploadInput,\n} from './createMultipartUpload';\n\nimport { calculateSafePartSize } from './listParts';\nimport { Stats } from 'fs';\nimport { UploadPartOutput, _uploadPart } from './uploadPart';\nimport TosServerError from '../../../TosServerError';\nimport {\n  completeMultipartUpload,\n  CompleteMultipartUploadOutput,\n} from './completeMultipartUpload';\nimport { CancelToken } from 'axios';\nimport * as fsp from '../../../nodejs/fs-promises';\nimport path from 'path';\nimport TosClientError from '../../../TosClientError';\nimport { DataTransferStatus, DataTransferType } from '../../../interface';\nimport {\n  safeAwait,\n  isBlob,\n  isBuffer,\n  isCancel<PERSON>rror,\n  DEFAULT_PART_SIZE,\n  normalizeHeadersKey,\n  fillRequestHeaders,\n  makeSerialAsyncTask,\n  safeParseCheckpointFile,\n  makeRetryStreamAutoClose,\n  tryDestroy,\n} from '../../../utils';\nimport { EmptyReadStream } from '../../../nodejs/EmptyReadStream';\nimport { CancelError } from '../../../CancelError';\nimport { hashMd5 } from '../../../universal/crypto';\nimport { IRateLimiter } from '../../../interface';\nimport { validateCheckpoint } from '../utils';\nimport { combineCrc64 } from '../../../universal/crc';\n\nexport interface UploadFileInput extends CreateMultipartUploadInput {\n  /**\n   * if the type of `file` is string,\n   * `file` represents the file path that will be uploaded\n   */\n  file: string | File | Blob | Buffer;\n\n  /**\n   * default is 20 MB\n   *\n   * unit: B\n   */\n  partSize?: number;\n\n  /**\n   * the number of request to parallel upload part，default value is 1\n   */\n  taskNum?: number;\n\n  // TODO: default file name is not aligned.\n  /**\n   * if checkpoint is a string and point to a exist file,\n   * the checkpoint record will recover from this file.\n   *\n   * if checkpoint is a string and point to a directory,\n   * the checkpoint will be auto generated,\n   * and its name is `{bucketName}_{objectName}.{uploadId}`.\n   */\n  checkpoint?: string | CheckpointRecord;\n\n  dataTransferStatusChange?: (status: DataTransferStatus) => void;\n\n  /**\n   * the feature of pause and continue uploading\n   */\n  uploadEventChange?: (event: UploadEvent) => void;\n\n  /**\n   * the simple progress feature\n   * percent is [0, 1]\n   */\n  progress?: (percent: number, checkpoint: CheckpointRecord) => void;\n\n  /**\n   * cancel this upload progress\n   */\n  cancelToken?: CancelToken;\n\n  /**\n   * enable md5 checksum to uploadPart method\n   *\n   * default: false\n   */\n  enableContentMD5?: boolean;\n\n  /**\n   * unit: bit/s\n   * server side traffic limit\n   **/\n  trafficLimit?: number;\n\n  /**\n   * only works for nodejs environment\n   */\n  rateLimiter?: IRateLimiter;\n}\n\nexport interface UploadFileOutput extends CompleteMultipartUploadOutput {}\n\nexport enum UploadEventType {\n  CreateMultipartUploadSucceed = 1,\n  CreateMultipartUploadFailed = 2,\n  UploadPartSucceed = 3,\n  UploadPartFailed = 4,\n  UploadPartAborted = 5,\n  CompleteMultipartUploadSucceed = 6,\n  CompleteMultipartUploadFailed = 7,\n}\n\nexport interface UploadPartInfo {\n  partNumber: number;\n  partSize: number;\n  offset: number;\n\n  // has value when upload part succeed\n  etag?: string;\n\n  // not support\n  // hashCrc64ecma?: number;\n}\n\nexport interface UploadEvent {\n  type: UploadEventType;\n\n  /**\n   * has value when event is failed or aborted\n   */\n  err?: Error;\n\n  bucket: string;\n  key: string;\n  uploadId: string;\n  checkpointFile?: string;\n  uploadPartInfo?: UploadPartInfo;\n}\n\nexport interface CheckpointRecord {\n  bucket: string;\n  key: string;\n  part_size: number;\n  upload_id: string;\n  parts_info?: CheckpointRecordPart[];\n  // Information about the file to be uploaded\n  file_info?: {\n    last_modified: number;\n    file_size: number;\n  };\n\n  // TODO: Not support the fields below\n  // ssec_algorithm?: string;\n  // ssec_key_md5?: string;\n  // encoding_type?: string;\n}\n\ninterface CheckpointRecordPart {\n  part_number: number;\n  part_size: number;\n  offset: number;\n  etag: string;\n  hash_crc64ecma: string;\n  is_completed: boolean;\n}\n\ninterface CheckpointRichInfo {\n  filePath?: string | undefined;\n\n  filePathIsPlaceholder?: boolean;\n\n  record?: CheckpointRecord;\n}\n\ninterface Task {\n  partSize: number;\n  offset: number;\n  partNumber: number;\n}\n\nconst CHECKPOINT_FILE_NAME_PLACEHOLDER = '@@checkpoint-file-placeholder@@';\nconst FILE_PARAM_CHECK_MSG = '`file` must be string, Buffer, File or Blob';\nconst ABORT_ERROR_STATUS_CODE = [403, 404, 405];\n\nexport async function uploadFile(\n  this: TOSBase,\n  input: UploadFileInput\n): Promise<TosResponse<UploadFileOutput>> {\n  const { cancelToken, enableContentMD5 = false } = input;\n  const headers = normalizeHeadersKey(input.headers);\n  input.headers = headers;\n  fillRequestHeaders(input, [\n    'encodingType',\n    'cacheControl',\n    'contentDisposition',\n    'contentEncoding',\n    'contentLanguage',\n    'contentType',\n    'expires',\n\n    'acl',\n    'grantFullControl',\n    'grantRead',\n    'grantReadAcp',\n    'grantWriteAcp',\n\n    'ssecAlgorithm',\n    'ssecKey',\n    'ssecKeyMD5',\n    'serverSideEncryption',\n    'serverSideDataEncryption',\n    'meta',\n    'websiteRedirectLocation',\n    'storageClass',\n  ]);\n\n  const isCancel = () => cancelToken && !!cancelToken.reason;\n  validateCheckpoint(input.checkpoint);\n\n  const fileStats: Stats | null = await (async () => {\n    if (\n      process.env.TARGET_ENVIRONMENT === 'node' &&\n      typeof input.file === 'string'\n    ) {\n      return fsp.stat(input.file);\n    }\n    return null;\n  })();\n\n  const fileSize = await (async () => {\n    const { file } = input;\n    if (fileStats) {\n      return fileStats.size;\n    }\n    if (isBuffer(file)) {\n      return file.length;\n    }\n    if (isBlob(file)) {\n      return file.size;\n    }\n    throw new TosClientError(FILE_PARAM_CHECK_MSG);\n  })();\n\n  const checkpointRichInfo = await (async (): Promise<CheckpointRichInfo> => {\n    if (process.env.TARGET_ENVIRONMENT === 'node') {\n      if (typeof input.checkpoint === 'string') {\n        const { checkpoint } = input;\n        // file doesn't exist when stat is null\n        let checkpointStat: Stats | null = null;\n        try {\n          checkpointStat = await fsp.stat(checkpoint);\n        } catch (_err) {\n          // TODO: remove any\n          const err = _err as any;\n          if (err.code === 'ENOENT') {\n            // file doesn't exist\n          } else {\n            throw err;\n          }\n        }\n\n        const isDirectory = (() => {\n          if (checkpointStat) {\n            return checkpointStat.isDirectory();\n          }\n          return checkpoint.endsWith('/');\n        })();\n\n        // TODO: this is not a right decision\n        // filePath will generated by uploadId, use placeholder temporarily\n        const filePath = isDirectory\n          ? path.resolve(checkpoint, CHECKPOINT_FILE_NAME_PLACEHOLDER)\n          : // ensure relative path require\n            path.resolve(checkpoint);\n        const dirPath = path.dirname(filePath);\n        // ensure directory exist\n        await fsp.safeMkdirRecursive(dirPath);\n\n        if (isDirectory) {\n          return {\n            filePath,\n            filePathIsPlaceholder: true,\n          };\n        }\n\n        try {\n          const record = checkpointStat\n            ? await safeParseCheckpointFile(filePath)\n            : undefined;\n          return {\n            filePath,\n            filePathIsPlaceholder: false,\n            // filePath is json file\n            // TODO: validate json schema\n            record,\n          };\n        } catch (error) {\n          console.warn(\n            'the checkpoint file is invalid JSON format. please check checkpoint file'\n          );\n          throw error;\n        }\n      }\n    }\n\n    if (typeof input.checkpoint === 'object') {\n      return {\n        record: input.checkpoint,\n      };\n    }\n\n    return {};\n  })();\n\n  // check if file info is matched\n  await (async () => {\n    if (fileStats && checkpointRichInfo.record?.file_info) {\n      const { last_modified, file_size } = checkpointRichInfo.record?.file_info;\n      if (fileStats.mtimeMs !== last_modified || fileStats.size !== file_size) {\n        console.warn(\n          `The file has been modified since ${new Date(\n            last_modified\n          )}, so the checkpoint file is invalid, and specified file will be uploaded again.`\n        );\n        delete checkpointRichInfo.record;\n      }\n    }\n  })();\n\n  const partSize = calculateSafePartSize(\n    fileSize,\n    input.partSize || checkpointRichInfo.record?.part_size || DEFAULT_PART_SIZE,\n    true\n  );\n\n  // check partSize is matched\n  if (\n    checkpointRichInfo.record &&\n    checkpointRichInfo.record.part_size !== partSize\n  ) {\n    console.warn(\n      'The partSize param does not equal the partSize in checkpoint file, ' +\n        'so the checkpoint file is invalid, and specified file will be uploaded again.'\n    );\n    delete checkpointRichInfo.record;\n  }\n\n  let bucket = input.bucket || this.opts.bucket || '';\n  const key = input.key;\n  let uploadId = '';\n  let tasks: Task[] = [];\n  const allTasks: Task[] = getAllTasks(fileSize, partSize);\n  const initConsumedBytes = (checkpointRichInfo.record?.parts_info || [])\n    .filter((it) => it.is_completed)\n    .reduce((prev, it) => prev + it.part_size, 0);\n  let consumedBytesForProgress = initConsumedBytes;\n\n  // recorded tasks\n  const recordedTasks = checkpointRichInfo.record?.parts_info || [];\n  const recordedTaskMap: Map<number, CheckpointRecordPart> = new Map();\n  recordedTasks.forEach((it) => recordedTaskMap.set(it.part_number, it));\n\n  const getCheckpointContent = () => {\n    const checkpointContent: CheckpointRecord = {\n      bucket,\n      key,\n      part_size: partSize,\n      upload_id: uploadId,\n      parts_info: recordedTasks,\n    };\n    if (fileStats) {\n      checkpointContent.file_info = {\n        last_modified: fileStats.mtimeMs,\n        file_size: fileStats.size,\n      };\n    }\n    return checkpointContent;\n  };\n  const triggerUploadEvent = (\n    e: Omit<UploadEvent, 'bucket' | 'uploadId' | 'key' | 'checkpointFile'>\n  ) => {\n    if (!input.uploadEventChange) {\n      return;\n    }\n\n    const event: UploadEvent = {\n      bucket,\n      uploadId,\n      key,\n      ...e,\n    };\n    if (checkpointRichInfo.filePath) {\n      event.checkpointFile = checkpointRichInfo.filePath;\n    }\n\n    input.uploadEventChange(event);\n  };\n  enum TriggerProgressEventType {\n    start = 1,\n    uploadPartSucceed = 2,\n    completeMultipartUploadSucceed = 3,\n  }\n  const triggerProgressEvent = (type: TriggerProgressEventType) => {\n    if (!input.progress) {\n      return;\n    }\n\n    const percent = (() => {\n      if (type === TriggerProgressEventType.start && fileSize === 0) {\n        return 0;\n      }\n      return !fileSize ? 1 : consumedBytesForProgress / fileSize;\n    })();\n\n    if (\n      consumedBytesForProgress === fileSize &&\n      type === TriggerProgressEventType.uploadPartSucceed\n    ) {\n      // 100% 仅在 complete 后处理，以便 100% 可以拉取到新对象\n    } else {\n      input.progress(percent, getCheckpointContent());\n    }\n  };\n  let consumedBytes = initConsumedBytes;\n  const { dataTransferStatusChange } = input;\n  const triggerDataTransfer = (\n    type: DataTransferType,\n    rwOnceBytes: number = 0\n  ) => {\n    if (!dataTransferStatusChange) {\n      return;\n    }\n    consumedBytes += rwOnceBytes;\n\n    dataTransferStatusChange?.({\n      type,\n      rwOnceBytes,\n      consumedBytes,\n      totalBytes: fileSize,\n    });\n  };\n  const writeCheckpointFile = makeSerialAsyncTask(async () => {\n    if (\n      process.env.TARGET_ENVIRONMENT === 'node' &&\n      checkpointRichInfo.filePath\n    ) {\n      const content = JSON.stringify(getCheckpointContent(), null, 2);\n      const dirPath = path.dirname(checkpointRichInfo.filePath); // ensure directory exist\n      await fsp.safeMkdirRecursive(dirPath);\n      await fsp.writeFile(checkpointRichInfo.filePath, content, 'utf-8');\n    }\n  });\n  const rmCheckpointFile = async () => {\n    if (\n      process.env.TARGET_ENVIRONMENT === 'node' &&\n      checkpointRichInfo.filePath\n    ) {\n      await fsp.rm(checkpointRichInfo.filePath).catch((err: any) => {\n        // eat err\n        console.warn(\n          'remove checkpoint file failure, you can remove it by hand.\\n',\n          `checkpoint file path: ${checkpointRichInfo.filePath}\\n`,\n          err.message\n        );\n      });\n    }\n  };\n\n  /**\n   *\n   * @param task one part task\n   * @param uploadPartRes upload part failed if `uploadPartRes` is Error\n   */\n  const updateAfterUploadPart = async (\n    task: Task,\n    uploadPartRes:\n      | {\n          res: UploadPartOutput;\n          err?: null;\n        }\n      | {\n          err: Error;\n        }\n  ) => {\n    let existRecordTask = recordedTaskMap.get(task.partNumber);\n    if (!existRecordTask) {\n      existRecordTask = {\n        part_number: task.partNumber,\n        offset: task.offset,\n        part_size: task.partSize,\n        is_completed: false,\n        etag: '',\n        hash_crc64ecma: '',\n      };\n      recordedTasks.push(existRecordTask);\n      recordedTaskMap.set(existRecordTask.part_number, existRecordTask);\n    }\n\n    if (!uploadPartRes.err) {\n      existRecordTask.is_completed = true;\n      existRecordTask.etag = uploadPartRes.res.ETag;\n      existRecordTask.hash_crc64ecma = uploadPartRes.res.hashCrc64ecma;\n    }\n\n    await writeCheckpointFile();\n    const uploadPartInfo: UploadPartInfo = {\n      partNumber: existRecordTask.part_number,\n      partSize: existRecordTask.part_size,\n      offset: existRecordTask.offset,\n    };\n\n    if (uploadPartRes.err) {\n      const err = uploadPartRes.err;\n      let type: UploadEventType = UploadEventType.UploadPartFailed;\n\n      if (err instanceof TosServerError) {\n        if (ABORT_ERROR_STATUS_CODE.includes(err.statusCode)) {\n          type = UploadEventType.UploadPartAborted;\n        }\n      }\n\n      triggerUploadEvent({\n        type,\n        err,\n        uploadPartInfo,\n      });\n      return;\n    }\n\n    uploadPartInfo.etag = uploadPartRes.res.ETag;\n    consumedBytesForProgress += uploadPartInfo.partSize;\n\n    triggerUploadEvent({\n      type: UploadEventType.UploadPartSucceed,\n      uploadPartInfo,\n    });\n    triggerProgressEvent(TriggerProgressEventType.uploadPartSucceed);\n  };\n\n  if (checkpointRichInfo.record) {\n    bucket = checkpointRichInfo.record.bucket;\n    uploadId = checkpointRichInfo.record.upload_id;\n\n    // checkpoint info exists, so need to calculate remain tasks\n    const uploadedPartSet: Set<number> = new Set(\n      (checkpointRichInfo.record.parts_info || [])\n        .filter((it) => it.is_completed)\n        .map((it) => it.part_number)\n    );\n    tasks = allTasks.filter((it) => !uploadedPartSet.has(it.partNumber));\n  } else {\n    // createMultipartUpload will check bucket\n    try {\n      const { data: multipartRes } = await createMultipartUpload.call(\n        this,\n        input\n      );\n      if (isCancel()) {\n        throw new CancelError('cancel uploadFile');\n      }\n\n      bucket = multipartRes.Bucket;\n      uploadId = multipartRes.UploadId;\n      if (checkpointRichInfo.filePathIsPlaceholder) {\n        checkpointRichInfo.filePath = checkpointRichInfo.filePath?.replace(\n          `${CHECKPOINT_FILE_NAME_PLACEHOLDER}`,\n          getDefaultCheckpointFilePath(bucket, key)\n        );\n      }\n\n      triggerUploadEvent({\n        type: UploadEventType.CreateMultipartUploadSucceed,\n      });\n    } catch (_err) {\n      const err = _err as Error;\n      triggerUploadEvent({\n        type: UploadEventType.CreateMultipartUploadFailed,\n        err,\n      });\n      throw err;\n    }\n\n    tasks = allTasks;\n  }\n\n  triggerProgressEvent(TriggerProgressEventType.start);\n  const handleTasks = async () => {\n    let firstErr: Error | null = null;\n    let index = 0;\n\n    // TODO: how to test parallel does work, measure time is not right\n    await Promise.all(\n      Array.from({ length: input.taskNum || 1 }).map(async () => {\n        while (true) {\n          const currentIndex = index++;\n          if (currentIndex >= tasks.length) {\n            return;\n          }\n\n          const curTask = tasks[currentIndex];\n          let consumedBytesThisTask = 0;\n          const makeRetryStream = getMakeRetryStream(input.file, curTask);\n          try {\n            function getBody(file: UploadFileInput['file'], task: Task) {\n              const { offset: start, partSize } = task;\n              const end = start + partSize;\n\n              if (makeRetryStream) {\n                return makeRetryStream.make();\n              }\n\n              if (isBlob(file)) {\n                return file.slice(start, end);\n              }\n              if (isBuffer(file)) {\n                return file.slice(start, end);\n              }\n              throw new TosClientError(FILE_PARAM_CHECK_MSG);\n            }\n\n            const { data: uploadPartRes } = await _uploadPart.call(this, {\n              bucket,\n              key,\n              uploadId,\n              body: getBody(input.file, curTask),\n              enableContentMD5,\n              makeRetryStream: makeRetryStream?.make,\n              beforeRetry: () => {\n                consumedBytes -= consumedBytesThisTask;\n                consumedBytesThisTask = 0;\n              },\n              partNumber: curTask.partNumber,\n              headers: {\n                ['content-length']: `${curTask.partSize}`,\n                ['x-tos-server-side-encryption-customer-algorithm']:\n                  headers['x-tos-server-side-encryption-customer-algorithm'],\n                ['x-tos-server-side-encryption-customer-key']:\n                  headers['x-tos-server-side-encryption-customer-key'],\n                ['x-tos-server-side-encryption-customer-key-md5']:\n                  headers['x-tos-server-side-encryption-customer-key-md5'],\n              },\n              dataTransferStatusChange(status) {\n                if (status.type !== DataTransferType.Rw) {\n                  return;\n                }\n                if (isCancel()) {\n                  return;\n                }\n                consumedBytesThisTask += status.rwOnceBytes;\n                triggerDataTransfer(status.type, status.rwOnceBytes);\n              },\n              trafficLimit: input.trafficLimit,\n              rateLimiter: input.rateLimiter,\n            });\n\n            if (isCancel()) {\n              throw new CancelError('cancel uploadFile');\n            }\n\n            await updateAfterUploadPart(curTask, { res: uploadPartRes });\n          } catch (_err) {\n            tryDestroy(makeRetryStream?.getLastStream(), _err);\n\n            const err = _err as any;\n            consumedBytes -= consumedBytesThisTask;\n            consumedBytesThisTask = 0;\n\n            if (isCancelError(err)) {\n              throw err;\n            }\n\n            if (isCancel()) {\n              throw new CancelError('cancel uploadFile');\n            }\n\n            if (!firstErr) {\n              firstErr = err;\n            }\n            await updateAfterUploadPart(curTask, { err });\n          }\n        }\n      })\n    );\n\n    if (firstErr) {\n      throw firstErr;\n    }\n\n    const parts = (getCheckpointContent().parts_info || []).map((it) => ({\n      eTag: it.etag,\n      partNumber: it.part_number,\n    }));\n\n    const [err, res] = await safeAwait(\n      completeMultipartUpload.call(this, {\n        bucket,\n        key,\n        uploadId,\n        parts,\n      })\n    );\n\n    if (err || !res) {\n      triggerUploadEvent({\n        type: UploadEventType.CompleteMultipartUploadFailed,\n      });\n      throw err;\n    }\n\n    triggerUploadEvent({\n      type: UploadEventType.CompleteMultipartUploadSucceed,\n    });\n    triggerProgressEvent(\n      TriggerProgressEventType.completeMultipartUploadSucceed\n    );\n    await rmCheckpointFile();\n\n    if (\n      this.opts.enableCRC &&\n      res.data.HashCrc64ecma &&\n      combineCRCInParts(getCheckpointContent()) !== res.data.HashCrc64ecma\n    ) {\n      throw new TosClientError('crc of entire file mismatch.');\n    }\n\n    return res;\n  };\n\n  triggerDataTransfer(DataTransferType.Started);\n  const [err, res] = await safeAwait(handleTasks());\n  if (err || !res) {\n    triggerDataTransfer(DataTransferType.Failed);\n    throw err;\n  }\n  triggerDataTransfer(DataTransferType.Succeed);\n  return res;\n}\n\nexport default uploadFile;\n\n/**\n * 即使 totalSize 是 0，也需要一个 Part，否则 Server 端会报错 read request body failed\n */\nfunction getAllTasks(totalSize: number, partSize: number) {\n  const tasks: Task[] = [];\n  for (let i = 0; ; ++i) {\n    const offset = i * partSize;\n    const currPartSize = Math.min(partSize, totalSize - offset);\n\n    tasks.push({\n      offset,\n      partSize: currPartSize,\n      partNumber: i + 1,\n    });\n\n    if ((i + 1) * partSize >= totalSize) {\n      break;\n    }\n  }\n\n  return tasks;\n}\n\nfunction getMakeRetryStream(file: UploadFileInput['file'], task: Task) {\n  const { offset: start, partSize } = task;\n  const end = start + partSize;\n\n  if (process.env.TARGET_ENVIRONMENT === 'node' && typeof file === 'string') {\n    return makeRetryStreamAutoClose(() => {\n      if (!partSize) {\n        return new EmptyReadStream();\n      }\n      return fsp.createReadStream(file, {\n        start,\n        end: end - 1,\n      });\n    });\n  }\n\n  return undefined;\n}\n\nfunction getDefaultCheckpointFilePath(bucket: string, key: string) {\n  const originPath = `${key}.${hashMd5(`${bucket}.${key}`, 'hex')}.upload`;\n  const normalizePath = originPath.replace(/[\\\\/]/g, '');\n  return normalizePath;\n}\n\nfunction combineCRCInParts(cp: CheckpointRecord) {\n  const size = cp.file_info?.file_size || 0;\n  let res = '0';\n  const sortedPartsInfo =\n    cp.parts_info?.sort?.((a, b) => a.part_number - b.part_number) ?? [];\n  for (const part of sortedPartsInfo) {\n    res = combineCrc64(\n      res,\n      part.hash_crc64ecma,\n      Math.min(part.part_size, size - part.offset)\n    );\n  }\n  return res;\n}\n", "export enum ACLType {\n  ACLPrivate = 'private',\n  ACLPublicRead = 'public-read',\n  ACLPublicReadWrite = 'public-read-write',\n  ACLAuthenticatedRead = 'authenticated-read',\n  ACLBucketOwnerRead = 'bucket-owner-read',\n  ACLBucketOwnerFullControl = 'bucket-owner-full-control',\n  // only works for object ACL\n  ACLBucketOwnerEntrusted = 'bucket-owner-entrusted',\n  /**\n   * @private unstable value for object ACL\n   */\n  ACLDefault = 'default',\n}\n\nexport enum StorageClassType {\n  // storage-class will inherit from bucket if uploading object without `x-tos-storage-class` header\n  StorageClassStandard = 'STANDARD',\n  StorageClassIa = 'IA',\n  StorageClassArchiveFr = 'ARCHIVE_FR',\n  StorageClassColdArchive = 'COLD_ARCHIVE',\n  StorageClassIntelligentTiering = 'INTELLIGENT_TIERING',\n  StorageClassArchive = 'ARCHIVE',\n}\n\nexport enum MetadataDirectiveType {\n  MetadataDirectiveCopy = 'COPY',\n  MetadataDirectiveReplace = 'REPLACE',\n}\n\nexport enum AzRedundancyType {\n  AzRedundancySingleAz = 'single-az',\n  AzRedundancyMultiAz = 'multi-az',\n}\n\nexport enum PermissionType {\n  PermissionRead = 'READ',\n  PermissionWrite = 'WRITE',\n  PermissionReadAcp = 'READ_ACP',\n  PermissionWriteAcp = 'WRITE_ACP',\n  PermissionFullControl = 'FULL_CONTROL',\n  /**\n   * @private unstable value for ACL\n   */\n  PermissionReadNONLIST = 'READ_NON_LIST',\n}\n\nexport enum GranteeType {\n  GranteeGroup = 'Group',\n  GranteeUser = 'CanonicalUser',\n}\n\nexport enum CannedType {\n  CannedAllUsers = 'AllUsers',\n  CannedAuthenticatedUsers = 'AuthenticatedUsers',\n}\n\nexport enum HttpMethodType {\n  HttpMethodGet = 'GET',\n  HttpMethodPut = 'PUT',\n  HttpMethodPost = 'POST',\n  HttpMethodDelete = 'DELETE',\n  HttpMethodHead = 'HEAD',\n}\n\nexport enum StorageClassInheritDirectiveType {\n  StorageClassInheritDirectiveDestinationBucket = 'DESTINATION_BUCKET',\n  StorageClassInheritDirectiveSourceObject = 'SOURCE_OBJECT',\n}\n\nexport enum ReplicationStatusType {\n  Complete = 'COMPLETE',\n  Pending = 'PENDING',\n  Failed = 'FAILED',\n  Replica = 'REPLICA',\n}\n\nexport enum LifecycleStatusType {\n  Enabled = 'Enabled',\n  Disabled = 'Disabled',\n}\n\nexport enum RedirectType {\n  Mirror = 'Mirror',\n  Async = 'Async',\n}\n\nexport enum StatusType {\n  Enabled = 'Enabled',\n  Disabled = 'Disabled',\n}\n\nexport enum TierType {\n  TierStandard = 'Standard',\n  TierExpedited = 'Expedited',\n  TierBulk = 'Bulk',\n}\n\nexport enum VersioningStatusType {\n  Enabled = 'Enabled',\n  Suspended = 'Suspended',\n  NotSet = '',\n\n  /**\n   * @deprecated use `Enabled` instead\n   */\n  Enable = 'Enabled',\n  /**\n   * @deprecated use `NotSet` instead\n   */\n  Disable = '',\n}\n\n/**\n * @private unstable\n */\nexport enum AccessPointStatusType {\n  Ready = 'READY',\n  Creating = 'CREATING',\n  Created = 'CREATED',\n  Deleting = 'DELETING',\n}\n\n/**\n * @private unstable\n */\nexport enum TransferAccelerationStatusType {\n  Activating = 'AccelerationActivating',\n  Activated = 'AccelerationActivated',\n  Terminated = 'AccelerationTerminated',\n}\n\n/**\n * @private unstable\n */\nexport enum MRAPMirrorBackRedirectPolicyType {\n  ClosestFirst = 'Closest-First',\n  LatestFirst = 'Latest-First',\n}\n", "import TOSBase, { TosResponse } from '../../base';\nimport {\n  createMultipartUpload,\n  CreateMultipartUploadInput,\n} from './createMultipartUpload';\n\nimport { calculateSafePartSize } from './listParts';\nimport { Stats } from 'fs';\nimport { UploadPartOutput, _uploadPart } from './uploadPart';\nimport TosServerError from '../../../TosServerError';\nimport {\n  completeMultipartUpload,\n  CompleteMultipartUploadOutput,\n} from './completeMultipartUpload';\nimport { CancelToken } from 'axios';\nimport * as fsp from '../../../nodejs/fs-promises';\nimport path from 'path';\nimport {\n  makeSerialAsyncTask,\n  safeAwait,\n  safeParseCheckpointFile,\n} from '../../../utils';\nimport { CancelError } from '../../../CancelError';\nimport headObject from '../headObject';\nimport { uploadPartCopy, UploadPartCopyOutput } from './uploadPartCopy';\nimport { Headers } from '../../../interface';\nimport copyObject from '../copyObject';\nimport { getCopySourceHeaderValue, validateCheckpoint } from '../utils';\nimport cloneDeep from 'lodash/cloneDeep';\nimport TosClientError from '../../../TosClientError';\n\nexport interface ResumableCopyObjectInput extends CreateMultipartUploadInput {\n  srcBucket: string;\n  srcKey: string;\n  srcVersionId?: string;\n\n  /**\n   * default is 20 MB\n   */\n  partSize?: number;\n\n  /**\n   * the number of request to parallel upload part，default value is 1\n   */\n  taskNum?: number;\n\n  /**\n   * if checkpoint is a string and point to a exist file,\n   * the checkpoint record will recover from this file.\n   *\n   * if checkpoint is a string and point to a directory,\n   * the checkpoint will be auto generated,\n   * and its name is\n   * `{srcBucketName}.{srcObjectName}.{srcVersionId}.{bucketName}.{objectName}.copy`.\n   */\n  checkpoint?: string | ResumableCopyCheckpointRecord;\n\n  /**\n   * the callback of copy event\n   */\n  copyEventListener?: (event: ResumableCopyEvent) => void;\n\n  /**\n   * the simple progress feature\n   * percent is [0, 1]\n   */\n  progress?: (\n    percent: number,\n    checkpoint: ResumableCopyCheckpointRecord\n  ) => void;\n\n  /**\n   * is axios CancelToken\n   */\n  cancelToken?: CancelToken;\n\n  /**\n   * unit: bit/s\n   * server side traffic limit\n   **/\n  trafficLimit?: number;\n}\n\nexport interface UploadFileOutput extends CompleteMultipartUploadOutput {}\n\nexport enum ResumableCopyEventType {\n  CreateMultipartUploadSucceed = 1,\n  CreateMultipartUploadFailed = 2,\n  UploadPartCopySucceed = 3,\n  UploadPartCopyFailed = 4,\n  UploadPartCopyAborted = 5,\n  CompleteMultipartUploadSucceed = 6,\n  CompleteMultipartUploadFailed = 7,\n}\n\nexport interface CopyPartInfo {\n  partNumber: number;\n  copySourceRangeStart: number;\n  copySourceRangeEnd: number;\n\n  // has value when upload part succeed\n  etag?: string;\n}\n\nexport interface ResumableCopyEvent {\n  type: ResumableCopyEventType;\n\n  /**\n   * has value when event is failed or aborted\n   */\n  err?: Error;\n\n  bucket: string;\n  key: string;\n  uploadId?: string;\n  checkpointFile?: string;\n  copyPartInfo?: CopyPartInfo;\n}\n\nexport interface ResumableCopyCheckpointRecord {\n  bucket: string;\n  key: string;\n  part_size: number;\n  upload_id: string;\n  parts_info?: ResumableCopyCheckpointRecordPart[];\n  // Information about the file to be uploaded\n  copy_source_object_info: {\n    etag: string;\n    hash_crc64ecma: string;\n    last_modified: string;\n    object_size: number;\n  };\n  // TODO: more information\n}\n\ninterface ResumableCopyCheckpointRecordPart {\n  part_number: number;\n  copy_source_range_start: number;\n  copy_source_range_end: number;\n  etag: string;\n  is_completed: boolean;\n}\n\ninterface CheckpointRichInfo {\n  filePath?: string | undefined;\n\n  filePathIsPlaceholder?: boolean;\n\n  record?: ResumableCopyCheckpointRecord;\n}\n\ninterface Task {\n  partSize: number;\n  offset: number;\n  partNumber: number;\n}\n\nconst CHECKPOINT_FILE_NAME_PLACEHOLDER = '@@checkpoint-file-placeholder@@';\nconst ABORT_ERROR_STATUS_CODE = [403, 404, 405];\nexport const DEFAULT_PART_SIZE = 20 * 1024 * 1024; // 20 MB\n\nexport async function resumableCopyObject(\n  this: TOSBase,\n  input: ResumableCopyObjectInput\n): Promise<TosResponse<UploadFileOutput>> {\n  const { cancelToken } = input;\n  const isCancel = () => cancelToken && !!cancelToken.reason;\n  validateCheckpoint(input.checkpoint);\n\n  const { data: objectStats } = await headObject.call(this, {\n    bucket: input.srcBucket,\n    key: input.srcKey,\n    versionId: input.srcVersionId,\n  });\n  const etag = objectStats['etag'];\n  const objectSize = +objectStats['content-length'];\n\n  const checkpointRichInfo = await (async (): Promise<CheckpointRichInfo> => {\n    if (process.env.TARGET_ENVIRONMENT === 'node') {\n      if (typeof input.checkpoint === 'string') {\n        const { checkpoint } = input;\n        // file doesn't exist when stat is null\n        let checkpointStat: Stats | null = null;\n        try {\n          checkpointStat = await fsp.stat(checkpoint);\n        } catch (_err) {\n          // TODO: remove any\n          const err = _err as any;\n          if (err.code === 'ENOENT') {\n            // file doesn't exist\n          } else {\n            throw err;\n          }\n        }\n\n        const isDirectory = (() => {\n          if (checkpointStat) {\n            return checkpointStat.isDirectory();\n          }\n          return checkpoint.endsWith('/');\n        })();\n\n        // filePath will generated by uploadId, use placeholder temporarily\n        const filePath = isDirectory\n          ? path.resolve(checkpoint, CHECKPOINT_FILE_NAME_PLACEHOLDER)\n          : path.resolve(checkpoint);\n        const dirPath = path.dirname(filePath);\n        // ensure directory exist\n        await fsp.safeMkdirRecursive(dirPath);\n\n        if (isDirectory) {\n          return {\n            filePath,\n            filePathIsPlaceholder: true,\n          };\n        }\n\n        return {\n          filePath,\n          filePathIsPlaceholder: false,\n          // filePath is json file\n          // TODO: validate json schema\n          record: checkpointStat\n            ? await safeParseCheckpointFile(filePath)\n            : undefined,\n        };\n      }\n    }\n\n    if (typeof input.checkpoint === 'object') {\n      return {\n        record: input.checkpoint,\n      };\n    }\n\n    return {};\n  })();\n\n  // check if file info is matched\n  await (async () => {\n    if (checkpointRichInfo.record?.copy_source_object_info) {\n      const { last_modified, object_size } =\n        checkpointRichInfo.record?.copy_source_object_info;\n      if (\n        // TODO: `last-modified` aligns to number\n        objectStats['last-modified'] !== last_modified ||\n        +objectStats['content-length'] !== object_size\n      ) {\n        console.warn(\n          `The file has been modified since ${new Date(\n            last_modified\n          )}, so the checkpoint file is invalid, and specified file will be uploaded again.`\n        );\n        delete checkpointRichInfo.record;\n      }\n    }\n  })();\n\n  const partSize = calculateSafePartSize(\n    objectSize,\n    input.partSize || checkpointRichInfo.record?.part_size || DEFAULT_PART_SIZE,\n    true\n  );\n\n  // check partSize is matched\n  if (\n    checkpointRichInfo.record &&\n    checkpointRichInfo.record.part_size !== partSize\n  ) {\n    console.warn(\n      'The partSize param does not equal the partSize in checkpoint file, ' +\n        'so the checkpoint file is invalid, and specified file will be uploaded again.'\n    );\n    delete checkpointRichInfo.record;\n  }\n\n  let bucket = input.bucket || this.opts.bucket || '';\n  const key = input.key;\n  let uploadId = '';\n  let tasks: Task[] = [];\n  const allTasks: Task[] = getAllTasks(objectSize, partSize);\n  const initConsumedBytes = (checkpointRichInfo.record?.parts_info || [])\n    .filter((it) => it.is_completed)\n    .reduce(\n      (prev, it) =>\n        prev + it.copy_source_range_end - it.copy_source_range_start + 1,\n      0\n    );\n  let consumedBytesForProgress = initConsumedBytes;\n\n  // recorded tasks\n  const recordedTasks = checkpointRichInfo.record?.parts_info || [];\n  const recordedTaskMap: Map<number, ResumableCopyCheckpointRecordPart> =\n    new Map();\n  recordedTasks.forEach((it) => recordedTaskMap.set(it.part_number, it));\n\n  const getCheckpointContent = () => {\n    const checkpointContent: ResumableCopyCheckpointRecord = {\n      bucket,\n      key,\n      part_size: partSize,\n      upload_id: uploadId,\n      parts_info: recordedTasks,\n      copy_source_object_info: {\n        last_modified: objectStats['last-modified'],\n        etag: objectStats.etag,\n        hash_crc64ecma: objectStats['x-tos-hash-crc64ecma'] || '',\n        object_size: +objectStats['content-length'],\n      },\n    };\n    return checkpointContent;\n  };\n  const triggerUploadEvent = (\n    e: Omit<\n      ResumableCopyEvent,\n      'bucket' | 'uploadId' | 'key' | 'checkpointFile'\n    >\n  ) => {\n    if (!input.copyEventListener) {\n      return;\n    }\n\n    const event: ResumableCopyEvent = {\n      bucket,\n      uploadId,\n      key,\n      ...e,\n    };\n    if (checkpointRichInfo.filePath) {\n      event.checkpointFile = checkpointRichInfo.filePath;\n    }\n\n    input.copyEventListener(event);\n  };\n  enum TriggerProgressEventType {\n    start = 1,\n    uploadPartSucceed = 2,\n    completeMultipartUploadSucceed = 3,\n  }\n  const triggerProgressEvent = (type: TriggerProgressEventType) => {\n    if (!input.progress) {\n      return;\n    }\n\n    const percent = (() => {\n      if (type === TriggerProgressEventType.start && objectSize === 0) {\n        return 0;\n      }\n      return !objectSize ? 1 : consumedBytesForProgress / objectSize;\n    })();\n\n    if (\n      consumedBytesForProgress === objectSize &&\n      type === TriggerProgressEventType.uploadPartSucceed\n    ) {\n      // 100% 仅在 complete 后处理，以便 100% 可以拉取到新对象\n    } else {\n      input.progress(percent, getCheckpointContent());\n    }\n  };\n\n  const writeCheckpointFile = makeSerialAsyncTask(async () => {\n    if (\n      process.env.TARGET_ENVIRONMENT === 'node' &&\n      checkpointRichInfo.filePath\n    ) {\n      const content = JSON.stringify(getCheckpointContent(), null, 2);\n      const dirPath = path.dirname(checkpointRichInfo.filePath); // ensure directory exist\n      await fsp.safeMkdirRecursive(dirPath);\n      await fsp.writeFile(checkpointRichInfo.filePath, content, 'utf-8');\n    }\n  });\n  const rmCheckpointFile = async () => {\n    if (\n      process.env.TARGET_ENVIRONMENT === 'node' &&\n      checkpointRichInfo.filePath\n    ) {\n      await fsp.rm(checkpointRichInfo.filePath).catch((err: any) => {\n        // eat err\n        console.warn(\n          'remove checkpoint file failure, you can remove it by hand.\\n',\n          `checkpoint file path: ${checkpointRichInfo.filePath}\\n`,\n          err.message\n        );\n      });\n    }\n  };\n\n  /**\n   *\n   * @param task one part task\n   * @param uploadPartRes upload part failed if `uploadPartRes` is Error\n   */\n  const updateAfterUploadPart = async (\n    task: Task,\n    uploadPartRes:\n      | {\n          res: UploadPartCopyOutput;\n          err?: null;\n        }\n      | {\n          err: Error;\n        }\n  ) => {\n    let existRecordTask = recordedTaskMap.get(task.partNumber);\n    const rangeStart = task.offset;\n    const rangeEnd = Math.min(task.offset + partSize - 1, objectSize - 1);\n    if (!existRecordTask) {\n      existRecordTask = {\n        part_number: task.partNumber,\n        copy_source_range_start: rangeStart,\n        copy_source_range_end: rangeEnd,\n        is_completed: false,\n        etag: '',\n      };\n      recordedTasks.push(existRecordTask);\n      recordedTaskMap.set(existRecordTask.part_number, existRecordTask);\n    }\n\n    if (!uploadPartRes.err) {\n      existRecordTask.is_completed = true;\n      existRecordTask.etag = uploadPartRes.res.ETag;\n    }\n\n    await writeCheckpointFile();\n    const copyPartInfo: CopyPartInfo = {\n      partNumber: existRecordTask.part_number,\n      copySourceRangeEnd: existRecordTask.copy_source_range_end,\n      copySourceRangeStart: existRecordTask.copy_source_range_start,\n    };\n\n    if (uploadPartRes.err) {\n      const err = uploadPartRes.err;\n      let type: ResumableCopyEventType =\n        ResumableCopyEventType.UploadPartCopyFailed;\n\n      if (err instanceof TosServerError) {\n        if (ABORT_ERROR_STATUS_CODE.includes(err.statusCode)) {\n          type = ResumableCopyEventType.UploadPartCopyAborted;\n        }\n      }\n\n      triggerUploadEvent({\n        type,\n        err,\n        copyPartInfo,\n      });\n      return;\n    }\n\n    copyPartInfo.etag = uploadPartRes.res.ETag;\n    consumedBytesForProgress +=\n      copyPartInfo.copySourceRangeEnd - copyPartInfo.copySourceRangeStart + 1;\n\n    triggerUploadEvent({\n      type: ResumableCopyEventType.UploadPartCopySucceed,\n      copyPartInfo,\n    });\n    triggerProgressEvent(TriggerProgressEventType.uploadPartSucceed);\n  };\n\n  if (checkpointRichInfo.record) {\n    bucket = checkpointRichInfo.record.bucket;\n    uploadId = checkpointRichInfo.record.upload_id;\n\n    // checkpoint info exists, so need to calculate remain tasks\n    const uploadedPartSet: Set<number> = new Set(\n      (checkpointRichInfo.record.parts_info || [])\n        .filter((it) => it.is_completed)\n        .map((it) => it.part_number)\n    );\n    tasks = allTasks.filter((it) => !uploadedPartSet.has(it.partNumber));\n  } else {\n    // createMultipartUpload will check bucket\n    try {\n      const { data: multipartRes } = await createMultipartUpload.call(\n        this,\n        cloneDeep(input)\n      );\n      if (isCancel()) {\n        throw new CancelError('cancel uploadFile');\n      }\n\n      bucket = multipartRes.Bucket;\n      uploadId = multipartRes.UploadId;\n      if (checkpointRichInfo.filePathIsPlaceholder) {\n        checkpointRichInfo.filePath = checkpointRichInfo.filePath?.replace(\n          `${CHECKPOINT_FILE_NAME_PLACEHOLDER}`,\n          getDefaultCheckpointFilePath({\n            ...input,\n            bucket,\n          })\n        );\n      }\n\n      triggerUploadEvent({\n        type: ResumableCopyEventType.CreateMultipartUploadSucceed,\n      });\n    } catch (_err) {\n      const err = _err as Error;\n      triggerUploadEvent({\n        type: ResumableCopyEventType.CreateMultipartUploadFailed,\n        err,\n      });\n      throw err;\n    }\n\n    tasks = allTasks;\n  }\n\n  const handleTasks = async () => {\n    let firstErr: Error | null = null;\n    let index = 0;\n\n    // TODO: how to test parallel does work, measure time is not right\n    await Promise.all(\n      Array.from({ length: input.taskNum || 1 }).map(async () => {\n        while (true) {\n          const currentIndex = index++;\n          if (currentIndex >= tasks.length) {\n            return;\n          }\n\n          const curTask = tasks[currentIndex];\n          try {\n            let copySource = getCopySourceHeaderValue(\n              input.srcBucket,\n              input.srcKey\n            );\n            if (input.srcVersionId) {\n              copySource += `?versionId=${input.srcVersionId}`;\n            }\n            const copyRange = `bytes=${curTask.offset}-${\n              curTask.offset + curTask.partSize - 1\n            }`;\n            const headers: Headers = {\n              ...input.headers,\n              ['x-tos-copy-source']: copySource,\n              ['x-tos-copy-source-if-match']: etag,\n              ['x-tos-copy-source-range']: copyRange,\n            };\n\n            if (!curTask.partSize) {\n              delete headers['x-tos-copy-source-range'];\n            }\n            const { data: uploadPartRes } = await uploadPartCopy.call(this, {\n              bucket,\n              key,\n              uploadId,\n              partNumber: curTask.partNumber,\n              headers,\n              trafficLimit: input.trafficLimit,\n            });\n\n            if (isCancel()) {\n              throw new CancelError('cancel resumableCopyObject');\n            }\n\n            await updateAfterUploadPart(curTask, { res: uploadPartRes });\n          } catch (_err) {\n            const err = _err as any;\n\n            if (isCancelError(err)) {\n              throw err;\n            }\n\n            if (isCancel()) {\n              throw new CancelError('cancel resumableCopyObject');\n            }\n\n            if (!firstErr) {\n              firstErr = err;\n            }\n            await updateAfterUploadPart(curTask, { err });\n          }\n        }\n      })\n    );\n\n    if (firstErr) {\n      throw firstErr;\n    }\n\n    const parts = (getCheckpointContent().parts_info || []).map((it) => ({\n      eTag: it.etag,\n      partNumber: it.part_number,\n    }));\n\n    const [err, res] = await safeAwait(\n      completeMultipartUpload.call(this, {\n        bucket,\n        key,\n        uploadId,\n        parts,\n      })\n    );\n\n    if (err || !res) {\n      triggerUploadEvent({\n        type: ResumableCopyEventType.CompleteMultipartUploadFailed,\n      });\n      throw err;\n    }\n\n    triggerUploadEvent({\n      type: ResumableCopyEventType.CompleteMultipartUploadSucceed,\n    });\n    triggerProgressEvent(\n      TriggerProgressEventType.completeMultipartUploadSucceed\n    );\n\n    const sourceCRC64 =\n      getCheckpointContent().copy_source_object_info.hash_crc64ecma;\n    const actualCrc64 = res.data.HashCrc64ecma;\n    if (\n      this.opts.enableCRC &&\n      sourceCRC64 &&\n      actualCrc64 &&\n      sourceCRC64 !== actualCrc64\n    ) {\n      throw new TosClientError(\n        `validate file crc64 failed. Expect crc64 ${sourceCRC64}, actual crc64 ${actualCrc64}. Please try again.`\n      );\n    }\n\n    await rmCheckpointFile();\n\n    return res;\n  };\n\n  const handleEmptyObj = async (): Promise<TosResponse<UploadFileOutput>> => {\n    let copySource = getCopySourceHeaderValue(input.srcBucket, input.srcKey);\n    if (input.srcVersionId) {\n      copySource += `?versionId=${input.srcVersionId}`;\n    }\n    const headers: Headers = {\n      ...input.headers,\n      ['x-tos-copy-source']: copySource,\n      ['x-tos-copy-source-if-match']: etag,\n    };\n\n    const [err, res] = await safeAwait(\n      copyObject.call(this, {\n        bucket: input.bucket,\n        key: input.key,\n        headers,\n        trafficLimit: input.trafficLimit,\n      })\n    );\n    if (err || !res) {\n      triggerUploadEvent({\n        type: ResumableCopyEventType.UploadPartCopyFailed,\n      });\n      throw err;\n    }\n\n    triggerProgressEvent(\n      TriggerProgressEventType.completeMultipartUploadSucceed\n    );\n    triggerUploadEvent({\n      type: ResumableCopyEventType.UploadPartCopySucceed,\n      copyPartInfo: {\n        partNumber: 0,\n        copySourceRangeStart: 0,\n        copySourceRangeEnd: 0,\n      },\n    });\n    triggerUploadEvent({\n      type: ResumableCopyEventType.CompleteMultipartUploadSucceed,\n    });\n\n    return {\n      ...res,\n      data: {\n        ETag: res.headers['etag'] || '',\n        Bucket: bucket,\n        Key: key,\n        Location: `http${this.opts.secure ? 's' : ''}://${bucket}.${\n          this.opts.endpoint\n        }/${key}`,\n        VersionID: res.headers['x-tos-version-id'],\n        HashCrc64ecma: res.headers['x-tos-hash-crc64ecma'],\n      },\n    };\n  };\n\n  triggerProgressEvent(TriggerProgressEventType.start);\n  return objectSize === 0 ? handleEmptyObj() : handleTasks();\n}\n\nexport function isCancelError(err: any) {\n  return err instanceof CancelError;\n}\n\nexport default resumableCopyObject;\n\n/**\n * 即使 totalSize 是 0，也需要一个 Part，否则 Server 端会报错 read request body failed\n */\nfunction getAllTasks(totalSize: number, partSize: number) {\n  const tasks: Task[] = [];\n  for (let i = 0; ; ++i) {\n    const offset = i * partSize;\n    const currPartSize = Math.min(partSize, totalSize - offset);\n\n    tasks.push({\n      offset,\n      partSize: currPartSize,\n      partNumber: i + 1,\n    });\n\n    if ((i + 1) * partSize >= totalSize) {\n      break;\n    }\n  }\n\n  return tasks;\n}\n\nfunction getDefaultCheckpointFilePath(\n  opts: Pick<\n    ResumableCopyObjectInput,\n    'srcBucket' | 'srcKey' | 'srcVersionId' | 'key'\n  > & {\n    bucket: string;\n  }\n) {\n  const originPath = [\n    opts.srcBucket,\n    opts.srcKey,\n    opts.srcVersionId,\n    opts.bucket,\n    opts.key,\n    'copy',\n  ]\n    .filter(Boolean)\n    .join('.');\n\n  const normalizePath = originPath.replace(/[\\\\/]/g, '');\n  return normalizePath;\n}\n", "import { StorageClass } from '../../interface';\nimport { fillRequestHeaders, normalizeHeadersKey } from '../../utils';\nimport TOSBase from '../base';\nimport { ReplicationStatusType } from '../../TosExportEnum';\nimport { RestoreInfo, TosHeader } from './sharedTypes';\nimport { getRestoreInfoFromHeaders } from './utils';\n\nexport interface HeadObjectInput {\n  bucket?: string;\n  key: string;\n  versionId?: string;\n\n  ifMatch?: string;\n  ifModifiedSince?: string;\n  ifNoneMatch?: string;\n  ifUnmodifiedSince?: string;\n\n  ssecAlgorithm?: string;\n  ssecKey?: string;\n  ssecKeyMD5?: string;\n\n  headers?: {\n    [key: string]: string | undefined;\n    'If-Match'?: string;\n    'If-Modified-Since'?: string;\n    'If-None-Match'?: string;\n    'If-Unmodified-Since'?: string;\n    'x-tos-server-side-encryption-customer-algorithm'?: string;\n    'x-tos-server-side-encryption-customer-key'?: string;\n    'x-tos-server-side-encryption-customer-key-md5'?: string;\n  };\n}\n\nexport interface HeadObjectOutput {\n  [key: string]: string | undefined | object;\n  'content-length': string;\n  'last-modified': string;\n  'content-md5': string;\n  etag: string;\n  'x-tos-object-type'?: 'Appendable' | 'Symlink';\n  'x-tos-delete-marker'?: string;\n  'x-tos-server-side-encryption-customer-algorithm'?: string;\n  'x-tos-server-side-encryption-customer-key-md5'?: string;\n  'x-tos-version-id'?: string;\n  'x-tos-website-redirect-location'?: string;\n  'x-tos-hash-crc64ecma'?: string;\n  'x-tos-storage-class': StorageClass;\n  'x-tos-server-side-encryption'?: string;\n  'x-tos-replication-status'?: ReplicationStatusType;\n  'x-tos-symlink-target-size'?: string;\n  RestoreInfo?: RestoreInfo;\n  ReplicationStatus?: ReplicationStatusType;\n}\n\nexport async function headObject(\n  this: TOSBase,\n  input: HeadObjectInput | string\n) {\n  const normalizedInput = typeof input === 'string' ? { key: input } : input;\n  const headers = normalizeHeadersKey(normalizedInput.headers);\n  normalizedInput.headers = headers;\n\n  const query: Record<string, any> = {};\n  if (normalizedInput.versionId) {\n    query.versionId = normalizedInput.versionId;\n  }\n\n  fillRequestHeaders(normalizedInput, [\n    'ifMatch',\n    'ifModifiedSince',\n    'ifNoneMatch',\n    'ifUnmodifiedSince',\n    'ssecAlgorithm',\n    'ssecKey',\n    'ssecKeyMD5',\n  ]);\n\n  return this._fetchObject<HeadObjectOutput>(\n    input,\n    'HEAD',\n    query,\n    normalizedInput?.headers || {},\n    undefined,\n    {\n      handleResponse: (res) => {\n        const result = {\n          ...res.headers,\n          ReplicationStatus: res.headers[TosHeader.HeaderReplicationStatus],\n        };\n        const info = getRestoreInfoFromHeaders(res.headers);\n\n        if (info) {\n          result.RestoreInfo = info;\n        }\n        return result;\n      },\n    }\n  );\n}\n\nexport default headObject;\n", "import {\n  fillRequestHeaders,\n  safeAwait,\n  normalizeHeadersKey,\n  requestHeadersMap,\n} from '../../../utils';\nimport TOSBase from '../../base';\nimport { getCopySourceHeaderValue } from '../utils';\n\nexport interface UploadPartCopyInput {\n  bucket?: string;\n  key: string;\n  partNumber: number;\n  uploadId: string;\n\n  srcBucket?: string;\n  srcKey?: string;\n  srcVersionID?: string;\n  copySourceRange?: string;\n  copySourceRangeStart?: number;\n  copySourceRangeEnd?: number;\n  copySourceSSECAlgorithm?: string;\n  copySourceSSECKey?: string;\n  copySourceSSECKeyMD5?: string;\n  ssecAlgorithm?: string;\n  ssecKey?: string;\n  ssecKeyMD5?: string;\n  /**\n   * unit: bit/s\n   * server side traffic limit\n   **/\n  trafficLimit?: number;\n  headers?: {\n    [key: string]: string | undefined;\n    'x-tos-copy-source'?: string;\n    'x-tos-copy-source-range'?: string;\n    'x-tos-copy-source-if-match'?: string;\n    'x-tos-copy-source-if-modified-since'?: string;\n    'x-tos-copy-source-if-none-match'?: string;\n    'x-tos-copy-source-if-unmodified-since'?: string;\n    'x-tos-copy-source-server-side-encryption-customer-algorithm'?: string;\n    'x-tos-copy-source-server-side-encryption-customer-key'?: string;\n    'x-tos-copy-source-server-side-encryption-customer-key-MD5'?: string;\n  };\n}\n\nexport interface UploadPartCopyOutput {\n  ETag: string;\n  LastModified: string;\n  SSECAlgorithm: string;\n  SSECKeyMD5: string;\n}\n\nexport async function uploadPartCopy(\n  this: TOSBase,\n  input: UploadPartCopyInput\n) {\n  const { uploadId, partNumber } = input;\n  const headers = normalizeHeadersKey(input.headers);\n  input.headers = headers;\n  fillRequestHeaders(input, [\n    'copySourceRange',\n    'copySourceSSECAlgorithm',\n    'copySourceSSECKey',\n    'copySourceSSECKeyMD5',\n    'ssecAlgorithm',\n    'ssecKey',\n    'ssecKeyMD5',\n    'trafficLimit',\n  ]);\n  if (input.srcBucket && input.srcKey) {\n    let copySource = getCopySourceHeaderValue(input.srcBucket, input.srcKey);\n    if (input.srcVersionID) {\n      copySource += `?versionId=${input.srcVersionID}`;\n    }\n    headers['x-tos-copy-source'] = headers['x-tos-copy-source'] ?? copySource;\n  }\n\n  if (\n    input.copySourceRange == null &&\n    (input.copySourceRangeStart != null || input.copySourceRangeEnd != null)\n  ) {\n    const start =\n      input.copySourceRangeStart != null ? `${input.copySourceRangeStart}` : '';\n    const end =\n      input.copySourceRangeEnd != null ? `${input.copySourceRangeEnd}` : '';\n    const copyRange = `bytes=${start}-${end}`;\n    headers['x-tos-copy-source-range'] =\n      headers['x-tos-copy-source-range'] ?? copyRange;\n  }\n\n  const [err, res] = await safeAwait(\n    this._fetchObject<UploadPartCopyOutput>(\n      input,\n      'PUT',\n      { partNumber, uploadId },\n      headers,\n      undefined,\n      {\n        handleResponse(response) {\n          return {\n            ...response.data,\n            SSECAlgorithm:\n              response.headers[requestHeadersMap['ssecAlgorithm'] as string],\n            SSECKeyMD5:\n              response.headers[requestHeadersMap['ssecKeyMD5'] as string],\n          };\n        },\n      }\n    )\n  );\n\n  if (err || !res || !res.data.ETag) {\n    // TODO: throw TosServerErr\n    throw err;\n  }\n\n  return res;\n}\n", "import {\n  safeAwait,\n  normalizeHeadersKey,\n  fillRequestHeaders,\n} from '../../utils';\nimport { StorageClass, ServerSideEncryption, Acl } from '../../interface';\nimport TOSBase, { TosResponse } from '../base';\nimport { StorageClassType } from '../../TosExportEnum';\nimport { getCopySourceHeaderValue } from './utils';\n\nexport interface CopyObjectInput {\n  bucket?: string;\n  key: string;\n\n  srcBucket?: string;\n  srcKey?: string;\n  srcVersionID?: string;\n  cacheControl?: string;\n  contentDisposition?: string;\n  contentEncoding?: string;\n  contentLanguage?: string;\n  contentType?: string;\n  expires?: Date;\n\n  copySourceIfMatch?: string;\n  copySourceIfModifiedSince?: string | Date;\n  copySourceIfNoneMatch?: string;\n  copySourceIfUnmodifiedSince?: string;\n  copySourceSSECAlgorithm?: string;\n  copySourceSSECKey?: string;\n  copySourceSSECKeyMD5?: string;\n\n  ssecAlgorithm?: string;\n  ssecKey?: string;\n  ssecKeyMD5?: string;\n  serverSideEncryption?: string;\n  /**\n   * unit: bit/s\n   * server side traffic limit\n   **/\n  trafficLimit?: number;\n\n  acl?: Acl;\n  grantFullControl?: string;\n  grantRead?: string;\n  grantReadAcp?: string;\n  grantWriteAcp?: string;\n\n  metadataDirective?: string;\n  meta?: Record<string, string>;\n  websiteRedirectLocation?: string;\n  storageClass?: StorageClassType;\n  ifMatch?: string;\n  forbidOverwrite?: boolean;\n\n  headers?: {\n    [key: string]: string | undefined;\n    ['x-tos-copy-source']?: string;\n    ['x-tos-acl']?: string;\n    ['x-tos-copy-source-if-match']?: string;\n    ['x-tos-copy-source-if-modified-since']?: string;\n    ['x-tos-copy-source-if-none-match']?: string;\n    ['x-tos-copy-source-if-unmodified-since']?: string;\n    ['x-tos-copy-source-server-side-encryption-customer-algorithm']?: string;\n    ['x-tos-copy-source-server-side-encryption-customer-key']?: string;\n    ['x-tos-copy-source-server-side-encryption-customer-key-MD5']?: string;\n    ['x-tos-grant-full-control']?: string;\n    ['x-tos-grant-read']?: string;\n    ['x-tos-grant-read-acp']?: string;\n    ['x-tos-metadata-directive']?: string;\n    ['x-tos-website-redirect-location']?: string;\n    ['x-tos-storage-class']?: StorageClass;\n    ['x-tos-server-side-encryption']?: ServerSideEncryption;\n    ['x-tos-forbid-overwrite']?: string;\n    'If-Match'?: string;\n  };\n}\n\ninterface CopyObjectBody {\n  ETag: string;\n}\n\nexport interface CopyObjectOutput extends CopyObjectBody {}\n\nexport async function copyObject(\n  this: TOSBase,\n  input: CopyObjectInput\n): Promise<TosResponse<CopyObjectOutput>> {\n  const headers = normalizeHeadersKey(input.headers);\n  input.headers = headers;\n  fillRequestHeaders(input, [\n    'cacheControl',\n    'contentDisposition',\n    'contentEncoding',\n    'contentLanguage',\n    'contentType',\n    'expires',\n\n    'copySourceIfMatch',\n    'copySourceIfModifiedSince',\n    'copySourceIfNoneMatch',\n    'copySourceIfUnmodifiedSince',\n    'copySourceSSECAlgorithm',\n    'copySourceSSECKey',\n    'copySourceSSECKeyMD5',\n\n    'acl',\n    'grantFullControl',\n    'grantRead',\n    'grantReadAcp',\n    'grantWriteAcp',\n\n    'ssecAlgorithm',\n    'ssecKey',\n    'ssecKeyMD5',\n    'serverSideEncryption',\n\n    'metadataDirective',\n    'meta',\n    'websiteRedirectLocation',\n    'storageClass',\n    'trafficLimit',\n    'forbidOverwrite',\n    'ifMatch',\n  ]);\n  if (input.srcBucket && input.srcKey) {\n    let copySource = getCopySourceHeaderValue(input.srcBucket, input.srcKey);\n    if (input.srcVersionID) {\n      copySource += `?versionId=${input.srcVersionID}`;\n    }\n    headers['x-tos-copy-source'] = headers['x-tos-copy-source'] ?? copySource;\n  }\n\n  const [err, res] = await safeAwait(\n    this._fetchObject<CopyObjectBody>(input, 'PUT', {}, headers)\n  );\n\n  if (err || !res || !res.data.ETag) {\n    // TODO: throw TosServerErr\n    throw err;\n  }\n  return res;\n}\n\nexport default copyObject;\n", "import { createWriteStream } from '../../nodejs/fs-promises';\nimport TosClientError from '../../TosClientError';\nimport { DataTransferStatus, DataTransferType, Headers } from '../../interface';\nimport {\n  fillRequestHeaders,\n  fillRequestQ<PERSON>y,\n  streamToBuf,\n  isReadable,\n  normalize<PERSON><PERSON><PERSON><PERSON>ey,\n  safeAwait,\n} from '../../utils';\nimport TOSBase, { TosResponse } from '../base';\nimport {\n  IRateLimiter,\n  createRateLimiterStream,\n} from '../../universal/rate-limiter';\nimport { getRestoreInfoFromHeaders, isValidRateLimiter } from './utils';\nimport { createReadNReadStream } from '../../nodejs/EmitReadStream';\nimport { RestoreInfo, TosHeader } from './sharedTypes';\nimport { ReplicationStatusType } from '../../TosExportEnum';\n\nexport interface GetObjectInput {\n  bucket?: string;\n  key: string;\n  versionId?: string;\n\n  headers?: {\n    [key: string]: string | undefined;\n    'If-Modified-Since'?: string;\n    'If-Unmodified-Since'?: string;\n    'If-Match'?: string;\n    'If-None-Match'?: string;\n    'x-tos-server-side-encryption-customer-key'?: string;\n    'x-tos-server-side-encryption-customer-key-md5'?: string;\n    'x-tos-server-side-encryption-customer-algorithm'?: string;\n    Range?: string;\n  };\n  response?: Headers & {\n    'cache-control'?: string;\n    'content-disposition'?: string;\n    'content-encoding'?: string;\n    'content-language'?: string;\n    'content-type'?: string;\n    expires?: string;\n  };\n}\n\n/**\n * @deprecated use getObjectV2 instead\n * @returns arraybuffer\n */\nexport async function getObject(this: TOSBase, input: GetObjectInput | string) {\n  const normalizedInput = typeof input === 'string' ? { key: input } : input;\n  const query: Record<string, any> = {};\n  if (normalizedInput.versionId) {\n    query.versionId = normalizedInput.versionId;\n  }\n  const headers: Headers = normalizeHeadersKey(normalizedInput?.headers);\n  const response: Partial<Headers> = normalizedInput?.response || {};\n  Object.keys(response).forEach((key: string) => {\n    const v = response[key];\n    if (v != null) {\n      query[`response-${key}`] = v;\n    }\n  });\n\n  // TODO: maybe need to return response's headers\n  return this._fetchObject<Buffer>(input, 'GET', query, headers, undefined, {\n    axiosOpts: { responseType: 'arraybuffer' },\n  });\n}\n\ntype DataType = 'stream' | 'buffer' | 'blob';\nexport interface GetObjectV2Input {\n  bucket?: string;\n  key: string;\n  versionId?: string;\n\n  /**\n   * The type of return value, 'stream' | 'blob'\n   * default: 'stream'\n   *\n   * nodejs environment can use 'stream' and 'buffer'\n   * browser environment can use 'blob'\n   */\n  dataType?: DataType;\n\n  ifMatch?: string;\n  ifModifiedSince?: string | Date;\n  ifNoneMatch?: string;\n  ifUnmodifiedSince?: string | Date;\n\n  ssecAlgorithm?: string;\n  ssecKey?: string;\n  ssecKeyMD5?: string;\n  /**\n   * unit: bit/s\n   * server side traffic limit\n   **/\n  trafficLimit?: number;\n  /**\n   * only works for nodejs environment\n   */\n  rateLimiter?: IRateLimiter;\n\n  range?: string;\n  rangeStart?: number;\n  rangeEnd?: number;\n\n  process?: string;\n  // need base64url encode\n  saveBucket?: string;\n  // need base64url encode\n  saveObject?: string;\n\n  responseCacheControl?: string;\n  responseContentDisposition?: string;\n  responseContentEncoding?: string;\n  responseContentLanguage?: string;\n  responseContentType?: string;\n  responseExpires?: Date;\n\n  dataTransferStatusChange?: (status: DataTransferStatus) => void;\n  /**\n   * the simple progress feature\n   * percent is [0, 1].\n   */\n  progress?: (percent: number) => void;\n\n  headers?: {\n    [key: string]: string | undefined;\n    'If-Modified-Since'?: string;\n    'If-Unmodified-Since'?: string;\n    'If-Match'?: string;\n    'If-None-Match'?: string;\n    'x-tos-server-side-encryption-customer-key'?: string;\n    'x-tos-server-side-encryption-customer-key-md5'?: string;\n    'x-tos-server-side-encryption-customer-algorithm'?: string;\n    range?: string;\n  };\n  /**\n   * @deprecated use responseXxx options instead\n   */\n  response?: Headers & {\n    'cache-control'?: string;\n    'content-disposition'?: string;\n    'content-encoding'?: string;\n    'content-language'?: string;\n    'content-type'?: string;\n    expires?: string;\n  };\n}\nexport interface GetObjectV2Output {\n  content: NodeJS.ReadableStream | Buffer | Blob;\n  etag: string;\n  lastModified: string;\n\n  // object created before tos server supports crc, hashCrc64ecma will be empty string\n  hashCrc64ecma: string;\n  RestoreInfo?: RestoreInfo;\n  ReplicationStatus?: ReplicationStatusType;\n}\n\nexport interface GetObjectV2OutputStream\n  extends Omit<GetObjectV2Output, 'content'> {\n  content: NodeJS.ReadableStream;\n}\nexport interface GetObjectV2InputBuffer\n  extends Omit<GetObjectV2Input, 'dataType'> {\n  dataType: 'buffer';\n}\nexport interface GetObjectV2OutputBuffer\n  extends Omit<GetObjectV2Output, 'content'> {\n  content: Buffer;\n}\nexport interface GetObjectV2InputBlob\n  extends Omit<GetObjectV2Input, 'dataType'> {\n  dataType: 'blob';\n}\nexport interface GetObjectV2OutputBlob\n  extends Omit<GetObjectV2Output, 'content'> {\n  content: Blob;\n}\n\nconst NODEJS_DATATYPE: DataType[] = ['stream', 'buffer'];\nconst BROWSER_DATATYPE: DataType[] = ['blob'];\n\nfunction checkSupportDataType(dataType: DataType) {\n  let environment: 'node' | 'browser' = 'node';\n  let supportDataTypes: DataType[] = [];\n  if (process.env.TARGET_ENVIRONMENT === 'node') {\n    environment = 'node';\n    supportDataTypes = NODEJS_DATATYPE;\n  } else {\n    environment = 'browser';\n    supportDataTypes = BROWSER_DATATYPE;\n  }\n  if (!supportDataTypes.includes(dataType)) {\n    throw new TosClientError(\n      `The value of \\`dataType\\` only supports \\`${supportDataTypes.join(\n        ' | '\n      )}\\` in ${environment} environment`\n    );\n  }\n}\n\n/**\n * `getObjectV2` default returns stream, using `dataType` param to return other type(eg: buffer, blob)\n */\nasync function getObjectV2(\n  this: TOSBase,\n  input: GetObjectV2InputBlob\n): Promise<TosResponse<GetObjectV2OutputBlob>>;\nasync function getObjectV2(\n  this: TOSBase,\n  input: GetObjectV2InputBuffer\n): Promise<TosResponse<GetObjectV2OutputBuffer>>;\nasync function getObjectV2(\n  this: TOSBase,\n  input: GetObjectV2Input | string\n): Promise<TosResponse<GetObjectV2OutputStream>>;\nasync function getObjectV2(\n  this: TOSBase,\n  input: GetObjectV2Input | string\n): Promise<TosResponse<GetObjectV2Output>> {\n  const normalizedInput = typeof input === 'string' ? { key: input } : input;\n  const headers = normalizeHeadersKey(normalizedInput.headers);\n  normalizedInput.headers = headers;\n  const dataType = normalizedInput.dataType || 'stream';\n  normalizedInput.dataType = dataType;\n\n  checkSupportDataType(dataType);\n\n  const query: Record<string, unknown> = {};\n  const response: Partial<Headers> = normalizedInput?.response || {};\n  Object.keys(response).forEach((key: string) => {\n    const v = response[key];\n    if (v != null) {\n      query[`response-${key}`] = v;\n    }\n  });\n  fillRequestQuery(normalizedInput, query, [\n    'versionId',\n    'process',\n    'saveBucket',\n    'saveObject',\n    'responseCacheControl',\n    'responseContentDisposition',\n    'responseContentEncoding',\n    'responseContentLanguage',\n    'responseContentType',\n    'responseExpires',\n  ]);\n\n  fillRequestHeaders(normalizedInput, [\n    'ifMatch',\n    'ifModifiedSince',\n    'ifNoneMatch',\n    'ifUnmodifiedSince',\n\n    'ssecAlgorithm',\n    'ssecKey',\n    'ssecKeyMD5',\n\n    'range',\n    'trafficLimit',\n  ]);\n\n  if (\n    normalizedInput.range == null &&\n    (normalizedInput.rangeStart != null || normalizedInput.rangeEnd != null)\n  ) {\n    const start =\n      normalizedInput.rangeStart != null ? `${normalizedInput.rangeStart}` : '';\n    const end =\n      normalizedInput.rangeEnd != null ? `${normalizedInput.rangeEnd}` : '';\n    const range = `bytes=${start}-${end}`;\n    headers['range'] = headers['range'] ?? range;\n  }\n\n  const responseType = (() => {\n    if (process.env.TARGET_ENVIRONMENT === 'node') {\n      return 'stream';\n    }\n    return 'arraybuffer';\n  })();\n\n  let consumedBytes = 0;\n  // totalSize is unknown when start download\n  let totalSize = -1;\n  const { dataTransferStatusChange, progress } = normalizedInput;\n  const triggerDataTransfer = (\n    type: DataTransferType,\n    rwOnceBytes: number = 0\n  ) => {\n    // request cancel will make rwOnceBytes < 0 in browser\n    if (rwOnceBytes < 0) {\n      return;\n    }\n    if (!dataTransferStatusChange && !progress) {\n      return;\n    }\n    consumedBytes += rwOnceBytes;\n    dataTransferStatusChange?.({\n      type,\n      rwOnceBytes,\n      consumedBytes,\n      totalBytes: totalSize,\n    });\n    const progressValue = (() => {\n      // `totalSize` is unknown if it's in start or fail\n      if (totalSize < 0) {\n        return 0;\n      }\n\n      if (totalSize === 0) {\n        if (type === DataTransferType.Succeed) {\n          return 1;\n        }\n        return 0;\n      }\n      return consumedBytes / totalSize;\n    })();\n    if (progressValue === 1) {\n      if (type === DataTransferType.Succeed) {\n        progress?.(progressValue);\n      } else {\n        // not exec progress\n      }\n    } else {\n      progress?.(progressValue);\n    }\n  };\n\n  triggerDataTransfer(DataTransferType.Started);\n  const [err, res] = await safeAwait(\n    this._fetchObject<any>(input, 'GET', query, headers, undefined, {\n      axiosOpts: {\n        responseType,\n        onDownloadProgress: (event) => {\n          totalSize = event.total;\n          triggerDataTransfer(\n            DataTransferType.Rw,\n            event.loaded - consumedBytes\n          );\n        },\n      },\n    })\n  );\n  if (err || !res) {\n    triggerDataTransfer(DataTransferType.Failed);\n    throw err;\n  }\n\n  let resHeaders = res.headers;\n  let newData: NodeJS.ReadableStream | Blob | Buffer = res.data;\n  totalSize = +(resHeaders['content-length'] || 0);\n\n  if (process.env.TARGET_ENVIRONMENT === 'node') {\n    // res.data must be a stream in nodejs environment\n    if (isReadable(newData)) {\n      if (\n        normalizedInput.rateLimiter &&\n        isValidRateLimiter(normalizedInput.rateLimiter)\n      ) {\n        newData = createRateLimiterStream(\n          newData as NodeJS.ReadableStream,\n          normalizedInput.rateLimiter\n        );\n      }\n\n      newData = createReadNReadStream(newData, (n) =>\n        triggerDataTransfer(DataTransferType.Rw, n)\n      );\n      newData.on('end', () => triggerDataTransfer(DataTransferType.Succeed));\n\n      if (dataType === 'buffer') {\n        // consume stream after `createRateLimiterStream`\n        newData = await streamToBuf(newData);\n      }\n    } else {\n      // should not enter this branch\n    }\n  } else {\n    // 浏览器环境\n    if (dataType === 'blob') {\n      newData = new Blob([res.data], {\n        type: resHeaders['content-type'],\n      });\n    }\n    triggerDataTransfer(DataTransferType.Succeed);\n  }\n\n  const actualRes: TosResponse<GetObjectV2Output> = {\n    ...res,\n    data: {\n      content: newData,\n      etag: resHeaders['etag'] || '',\n      lastModified: resHeaders['last-modified'] || '',\n      hashCrc64ecma: resHeaders['x-tos-hash-crc64ecma'] || '',\n      ReplicationStatus: resHeaders[TosHeader.HeaderReplicationStatus] as\n        | ReplicationStatusType\n        | undefined,\n    },\n  };\n\n  const info = getRestoreInfoFromHeaders(resHeaders);\n  if (info) {\n    actualRes.data.RestoreInfo = info;\n  }\n  return actualRes;\n}\n\ninterface GetObjectToFileInput extends Omit<GetObjectV2Input, 'dataType'> {\n  filePath: string;\n}\ninterface GetObjectToFileOutput extends Omit<GetObjectV2Output, 'content'> {}\n\nexport async function getObjectToFile(\n  this: TOSBase,\n  input: GetObjectToFileInput\n): Promise<TosResponse<GetObjectToFileOutput>> {\n  if (process.env.TARGET_ENVIRONMENT !== 'node') {\n    throw new TosClientError(\n      \"getObjectToFile doesn't support in browser environment\"\n    );\n  }\n\n  return new Promise(async (resolve, reject) => {\n    const getObjectRes = await getObjectV2.call(this, input);\n    const stream = getObjectRes.data.content;\n\n    const fsWriteStream = createWriteStream(input.filePath);\n    stream.pipe(fsWriteStream);\n    stream.on('error', (err) => fsWriteStream.destroy(err));\n    fsWriteStream.on('error', (err) => reject(err));\n    fsWriteStream.on('finish', () => {\n      const newData: any = { ...getObjectRes.data };\n      delete newData.content;\n      resolve({ ...getObjectRes, data: { ...newData } });\n    });\n  });\n}\n\nexport { getObjectV2 };\n", "import TOSBase, { TosResponse } from '../base';\nimport {\n  DEFAULT_PART_SIZE,\n  isCancelError,\n  makeSerialAsyncTask,\n  safeParseCheckpointFile,\n} from '../../utils';\nimport * as fsp from '../../nodejs/fs-promises';\nimport { DataTransferStatus, DataTransferType } from '../../interface';\nimport headObject, { HeadObjectInput, HeadObjectOutput } from './headObject';\nimport { CancelToken } from 'axios';\nimport { Stats } from 'fs';\nimport path from 'path';\nimport TosClientError from '../../TosClientError';\nimport { getObjectV2, GetObjectV2Output } from './getObject';\nimport TosServerError from '../../TosServerError';\nimport { CancelError } from '../../CancelError';\nimport { IRateLimiter } from '../../universal/rate-limiter';\nimport { validateCheckpoint } from './utils';\nimport { createCrcReadStream } from '../../nodejs/CrcReadStream';\nimport { CRC } from '../../universal/crc';\nimport { combineCrc64 } from '../../universal/crc';\n\nexport interface DownloadFileCheckpointRecord {\n  bucket: string;\n  key: string;\n  version_id?: string;\n  part_size: number;\n\n  object_info: {\n    etag: string;\n    hash_crc64ecma: string;\n    object_size: number;\n    last_modified: string;\n  };\n\n  file_info: {\n    file_path: string;\n    temp_file_path: string;\n  };\n\n  parts_info: DownloadFileCheckpointRecordPartInfo[];\n}\n\nexport interface DownloadFileCheckpointRecordPartInfo {\n  part_number: number;\n  range_start: number;\n  range_end: number;\n  hash_crc64ecma: string;\n  is_completed: boolean;\n}\n\nexport interface DownloadFileInput extends HeadObjectInput {\n  filePath: string;\n  /**\n   * @private unstable tempFilePath\n   */\n  tempFilePath?: string;\n\n  /**\n   * default is 20 MB\n   *\n   * unit: B\n   */\n  partSize?: number;\n\n  /**\n   * the number of request to parallel upload part，default value is 1\n   */\n  taskNum?: number;\n\n  /**\n   * if checkpoint is a string and point to a exist file,\n   * the checkpoint record will recover from this file.\n   *\n   * if checkpoint is a string and point to a directory,\n   * the checkpoint will be auto generated,\n   * and its name is `{bucketName}_{objectName}.{uploadId}`.\n   */\n  checkpoint?: string | DownloadFileCheckpointRecord;\n\n  dataTransferStatusChange?: (status: DataTransferStatus) => void;\n\n  /**\n   * the simple progress feature\n   * percent is [0, 1]\n   */\n  progress?: (\n    percent: number,\n    checkpoint: DownloadFileCheckpointRecord\n  ) => void;\n\n  /**\n   * the feature of pause and continue downloading\n   */\n  downloadEventChange?: (event: DownloadEvent) => void;\n\n  /**\n   * cancel this upload progress\n   */\n  cancelToken?: CancelToken;\n  /**\n   * unit: bit/s\n   * server side traffic limit\n   **/\n  trafficLimit?: number;\n  /**\n   * only works for nodejs environment\n   */\n  rateLimiter?: IRateLimiter;\n\n  /**\n   * @private unstable\n   * custom rename file to support not overwrite file\n   */\n  customRenameFileAfterDownloadCompleted?: (\n    tempFilePath: string,\n    filePath: string\n  ) => void;\n}\nexport interface DownloadFileOutput extends HeadObjectOutput {}\n\nexport interface DownloadEvent {\n  type: DownloadEventType;\n  err?: Error;\n\n  bucket: string;\n  key: string;\n  versionId?: string;\n  filePath: string;\n  checkpointFile?: string;\n  downloadPartInfo?: DownloadPartInfo;\n}\n\nexport interface DownloadPartInfo {\n  partNumber: number;\n  rangeStart: number;\n  rangeEnd: number;\n}\n\nexport enum DownloadEventType {\n  CreateTempFileSucceed = 1,\n  CreateTempFileFailed,\n  DownloadPartSucceed,\n  DownloadPartFailed,\n  DownloadPartAborted,\n  RenameTempFileSucceed,\n  RenameTempFileFailed,\n}\n\ninterface CheckpointRichInfo {\n  filePath?: string | undefined;\n\n  filePathIsPlaceholder?: boolean;\n\n  record?: DownloadFileCheckpointRecord;\n}\n\ninterface Task {\n  partSize: number;\n  offset: number;\n  partNumber: number;\n}\n\nconst CHECKPOINT_FILE_NAME_PLACEHOLDER = '@@checkpoint-file-placeholder@@';\nconst ABORT_ERROR_STATUS_CODE = [403, 404, 405];\n\nexport async function downloadFile(\n  this: TOSBase,\n  input: DownloadFileInput\n): Promise<TosResponse<DownloadFileOutput>> {\n  if (process.env.TARGET_ENVIRONMENT === 'browser') {\n    throw new TosClientError(\n      '`downloadFile` is not supported in browser environment'\n    );\n  }\n  const { cancelToken, versionId } = input;\n  const isCancel = () => cancelToken && !!cancelToken.reason;\n  validateCheckpoint(input.checkpoint);\n\n  const headObjectRes = await headObject.call(this, {\n    bucket: input.bucket,\n    key: input.key,\n    versionId,\n  });\n  const { data: objectStats } = headObjectRes;\n  const etag = objectStats['etag'];\n  const symlinkTargetSize = objectStats['x-tos-symlink-target-size'] ?? 0;\n  const objectSize =\n    objectStats['x-tos-object-type'] === 'Symlink'\n      ? +symlinkTargetSize\n      : +objectStats['content-length'];\n\n  const checkpointRichInfo = await (async (): Promise<CheckpointRichInfo> => {\n    if (process.env.TARGET_ENVIRONMENT === 'node') {\n      if (typeof input.checkpoint === 'string') {\n        const { checkpoint } = input;\n        // file doesn't exist when stat is null\n        let checkpointStat: Stats | null = null;\n        try {\n          checkpointStat = await fsp.stat(checkpoint);\n        } catch (_err) {\n          // TODO: remove any\n          const err = _err as any;\n          if (err.code === 'ENOENT') {\n            // file doesn't exist\n          } else {\n            throw err;\n          }\n        }\n\n        const isDirectory = (() => {\n          if (checkpointStat) {\n            return checkpointStat.isDirectory();\n          }\n          return checkpoint.endsWith('/');\n        })();\n\n        // filePath will generated by uploadId, use placeholder temporarily\n        const filePath = isDirectory\n          ? path.resolve(checkpoint, CHECKPOINT_FILE_NAME_PLACEHOLDER)\n          : checkpoint;\n        const dirPath = path.dirname(filePath);\n        // ensure directory exist\n        await fsp.safeMkdirRecursive(dirPath);\n\n        if (isDirectory) {\n          return {\n            filePath,\n            filePathIsPlaceholder: true,\n          };\n        }\n\n        return {\n          filePath,\n          filePathIsPlaceholder: false,\n          // filePath is json file\n          // TODO: validate json schema\n          record: checkpointStat\n            ? await safeParseCheckpointFile(filePath)\n            : undefined,\n        };\n      }\n    }\n\n    if (typeof input.checkpoint === 'object') {\n      return {\n        record: input.checkpoint,\n      };\n    }\n\n    return {};\n  })();\n\n  // check if file info is matched\n  await (async () => {\n    if (checkpointRichInfo.record?.object_info) {\n      const { last_modified, object_size } =\n        checkpointRichInfo.record?.object_info;\n      if (\n        // TODO: `last-modified` aligns to number\n        objectStats['last-modified'] !== last_modified ||\n        objectSize !== object_size\n      ) {\n        console.warn(\n          `The file has been modified since ${new Date(\n            last_modified\n          )}, so the checkpoint file is invalid, and specified object will be downloaded again.`\n        );\n        delete checkpointRichInfo.record;\n      }\n    }\n  })();\n\n  const partSize =\n    input.partSize || checkpointRichInfo.record?.part_size || DEFAULT_PART_SIZE;\n\n  // check partSize is matched\n  if (\n    checkpointRichInfo.record &&\n    checkpointRichInfo.record.part_size !== partSize\n  ) {\n    console.warn(\n      'The partSize param does not equal the partSize in checkpoint file, ' +\n        'so the checkpoint file is invalid, and specified object will be downloaded again.'\n    );\n    delete checkpointRichInfo.record;\n  }\n\n  let bucket = input.bucket || this.opts.bucket || '';\n  const key = input.key;\n  const filePath = await (async () => {\n    let filePathStats: Stats | null = null;\n    try {\n      filePathStats = await fsp.stat(input.filePath);\n    } catch (_err) {\n      const err = _err as any;\n      if (err.code === 'ENOENT') {\n        // file doesn't exist\n      } else {\n        throw err;\n      }\n    }\n\n    const isDirectory = (() => {\n      if (filePathStats) {\n        return filePathStats.isDirectory();\n      }\n      return input.filePath.endsWith('/');\n    })();\n    const filePath = isDirectory\n      ? path.resolve(input.filePath, key)\n      : input.filePath;\n\n    const dirPath = path.dirname(filePath);\n    await fsp.safeMkdirRecursive(dirPath);\n\n    return filePath;\n  })();\n  const [tempFilePath, isExist] = await (async () => {\n    const tempFilePath = input.tempFilePath\n      ? input.tempFilePath\n      : filePath + '.temp';\n    let isExist = true;\n    try {\n      await fsp.stat(tempFilePath);\n    } catch (_err) {\n      const err = _err as any;\n      if (err.code === 'ENOENT') {\n        isExist = false;\n        // file doesn't exist\n      } else {\n        throw err;\n      }\n    }\n    return [tempFilePath, isExist];\n  })();\n  if (checkpointRichInfo.record) {\n    if (!isExist) {\n      console.warn(\n        \"The temp file doesn't not exist \" +\n          'so the checkpoint file is invalid, and specified object will be downloaded again.'\n      );\n      delete checkpointRichInfo.record;\n    }\n  }\n\n  let tasks: Task[] = [];\n  const allTasks: Task[] = getAllTasks(objectSize, partSize);\n  const initConsumedBytes = (checkpointRichInfo.record?.parts_info || [])\n    .filter((it) => it.is_completed)\n    .reduce((prev, it) => prev + (it.range_end - it.range_start + 1), 0);\n\n  // recorded tasks\n  const recordedTasks = checkpointRichInfo.record?.parts_info || [];\n  const recordedTaskMap: Map<number, DownloadFileCheckpointRecordPartInfo> =\n    new Map();\n  recordedTasks.forEach((it) => recordedTaskMap.set(it.part_number, it));\n\n  const nextEnsureCloseFd = async () => {\n    const getCheckpointContent = () => {\n      const checkpointContent: DownloadFileCheckpointRecord = {\n        bucket,\n        key,\n        version_id: versionId,\n        part_size: partSize,\n        parts_info: recordedTasks,\n        file_info: {\n          file_path: filePath,\n          temp_file_path: tempFilePath,\n        },\n        object_info: {\n          last_modified: objectStats['last-modified'],\n          etag: etag,\n          hash_crc64ecma: objectStats['x-tos-hash-crc64ecma'] || '',\n          object_size: objectSize,\n        },\n      };\n      return checkpointContent;\n    };\n    const triggerDownloadEvent = (\n      e: Omit<\n        DownloadEvent,\n        'bucket' | 'versionId' | 'key' | 'checkpointFile' | 'filePath'\n      >\n    ) => {\n      if (!input.downloadEventChange) {\n        return;\n      }\n\n      const event: DownloadEvent = {\n        bucket,\n        versionId,\n        key,\n        filePath,\n        ...e,\n      };\n      if (checkpointRichInfo.filePath) {\n        event.checkpointFile = checkpointRichInfo.filePath;\n      }\n\n      input.downloadEventChange(event);\n    };\n\n    let consumedBytesForProgress = initConsumedBytes;\n    enum TriggerProgressEventType {\n      start = 0,\n      downloadPartSucceed = 1,\n      renameTempFileSucceed = 2,\n    }\n    const triggerProgressEvent = (type: TriggerProgressEventType) => {\n      if (!input.progress) {\n        return;\n      }\n\n      const percent = (() => {\n        if (type === TriggerProgressEventType.start && objectSize === 0) {\n          return 0;\n        }\n        return !objectSize ? 1 : consumedBytesForProgress / objectSize;\n      })();\n\n      if (\n        consumedBytesForProgress === objectSize &&\n        type === TriggerProgressEventType.downloadPartSucceed\n      ) {\n        // 100% 仅在 complete 后处理，以便 100% 可以拉取到新对象\n      } else {\n        input.progress(percent, getCheckpointContent());\n      }\n    };\n    let consumedBytes = initConsumedBytes;\n    const { dataTransferStatusChange } = input;\n    const triggerDataTransfer = (\n      type: DataTransferType,\n      rwOnceBytes: number = 0\n    ) => {\n      if (!dataTransferStatusChange) {\n        return;\n      }\n      consumedBytes += rwOnceBytes;\n\n      dataTransferStatusChange?.({\n        type,\n        rwOnceBytes,\n        consumedBytes,\n        totalBytes: objectSize,\n      });\n    };\n    const writeCheckpointFile = makeSerialAsyncTask(async () => {\n      if (\n        process.env.TARGET_ENVIRONMENT === 'node' &&\n        checkpointRichInfo.filePath\n      ) {\n        const content = JSON.stringify(getCheckpointContent(), null, 2);\n        const dirPath = path.dirname(checkpointRichInfo.filePath); // ensure directory exist\n\n        await fsp.safeMkdirRecursive(dirPath);\n        await fsp.writeFile(checkpointRichInfo.filePath, content, 'utf-8');\n      }\n    });\n    const rmCheckpointFile = async () => {\n      if (\n        process.env.TARGET_ENVIRONMENT === 'node' &&\n        checkpointRichInfo.filePath\n      ) {\n        await fsp.rm(checkpointRichInfo.filePath).catch((err: any) => {\n          // eat err\n          console.warn(\n            'remove checkpoint file failure, you can remove it by hand.\\n',\n            `checkpoint file path: ${checkpointRichInfo.filePath}\\n`,\n            err.message\n          );\n        });\n      }\n    };\n\n    /**\n     *\n     * @param task one part task\n     * @param downloadPartRes upload part failed if `downloadPartRes` is Error\n     */\n    const updateAfterDownloadPart = async (\n      task: Task,\n      downloadPartRes:\n        | {\n            res: GetObjectV2Output & { rangeHashCrc64ecma: string };\n            err?: null;\n          }\n        | {\n            err: Error;\n          }\n    ) => {\n      let existRecordTask = recordedTaskMap.get(task.partNumber);\n      const rangeStart = task.offset;\n      const rangeEnd = Math.min(task.offset + partSize - 1, objectSize - 1);\n      if (!existRecordTask) {\n        existRecordTask = {\n          part_number: task.partNumber,\n          range_start: rangeStart,\n          range_end: rangeEnd,\n          hash_crc64ecma: '',\n          is_completed: false,\n        };\n        recordedTasks.push(existRecordTask);\n        recordedTaskMap.set(existRecordTask.part_number, existRecordTask);\n      }\n\n      if (!downloadPartRes.err) {\n        existRecordTask.is_completed = true;\n        existRecordTask.hash_crc64ecma = downloadPartRes.res.rangeHashCrc64ecma;\n      }\n\n      await writeCheckpointFile();\n      const downloadPartInfo: DownloadPartInfo = {\n        partNumber: existRecordTask.part_number,\n        rangeStart,\n        rangeEnd,\n      };\n\n      if (downloadPartRes.err) {\n        const err = downloadPartRes.err;\n        let type: DownloadEventType = DownloadEventType.DownloadPartFailed;\n\n        if (err instanceof TosServerError) {\n          if (ABORT_ERROR_STATUS_CODE.includes(err.statusCode)) {\n            type = DownloadEventType.DownloadPartAborted;\n          }\n        }\n\n        triggerDownloadEvent({\n          type,\n          err,\n          downloadPartInfo: downloadPartInfo,\n        });\n        return;\n      }\n\n      consumedBytesForProgress +=\n        downloadPartInfo.rangeEnd - downloadPartInfo.rangeStart + 1;\n\n      triggerDownloadEvent({\n        type: DownloadEventType.DownloadPartSucceed,\n        downloadPartInfo: downloadPartInfo,\n      });\n      triggerProgressEvent(TriggerProgressEventType.downloadPartSucceed);\n    };\n\n    if (checkpointRichInfo.record) {\n      bucket = checkpointRichInfo.record.bucket;\n\n      // checkpoint info exists, so need to calculate remain tasks\n      const uploadedPartSet: Set<number> = new Set(\n        (checkpointRichInfo.record.parts_info || [])\n          .filter((it) => it.is_completed)\n          .map((it) => it.part_number)\n      );\n      tasks = allTasks.filter((it) => !uploadedPartSet.has(it.partNumber));\n    } else {\n      try {\n        // create temp file\n        await fsp.writeFile(tempFilePath, '', {\n          flag: 'w+',\n        });\n      } catch (_err) {\n        const err = _err as any;\n        triggerDownloadEvent({\n          type: DownloadEventType.CreateTempFileFailed,\n          err,\n        });\n        throw err;\n      }\n\n      if (checkpointRichInfo.filePathIsPlaceholder) {\n        checkpointRichInfo.filePath = checkpointRichInfo.filePath?.replace(\n          `${CHECKPOINT_FILE_NAME_PLACEHOLDER}`,\n          getDefaultCheckpointFilePath(bucket, key, versionId)\n        );\n      }\n\n      triggerDownloadEvent({\n        type: DownloadEventType.CreateTempFileSucceed,\n      });\n      triggerDataTransfer(DataTransferType.Started);\n      tasks = allTasks;\n    }\n\n    const handleTasks = async () => {\n      let firstErr: Error | null = null;\n      let index = 0;\n\n      // TODO: how to test parallel does work, measure time is not right\n      await Promise.all(\n        Array.from({ length: input.taskNum || 1 }).map(async () => {\n          while (true) {\n            const currentIndex = index++;\n            if (currentIndex >= tasks.length) {\n              return;\n            }\n\n            const curTask = tasks[currentIndex];\n            let consumedBytesThisTask = 0;\n            try {\n              const res = await getObjectV2.call(this, {\n                bucket,\n                key,\n                versionId,\n                headers: {\n                  'if-match': etag,\n                  range: `bytes=${curTask.offset}-${Math.min(\n                    curTask.offset + curTask.partSize - 1,\n                    objectSize - 1\n                  )}`,\n                },\n                trafficLimit: input.trafficLimit,\n                rateLimiter: input.rateLimiter,\n                dataTransferStatusChange(status) {\n                  if (status.type !== DataTransferType.Rw) {\n                    return;\n                  }\n                  if (isCancel()) {\n                    return;\n                  }\n                  consumedBytesThisTask += status.rwOnceBytes;\n                  triggerDataTransfer(DataTransferType.Rw, status.rwOnceBytes);\n                },\n              });\n\n              // need to handle stream's error event before throw a error\n              // if (isCancel()) {\n              //   throw new CancelError('cancel downloadFile');\n              // }\n\n              let dataStream = res.data.content;\n              const crcInst = new CRC();\n              if (\n                process.env.TARGET_ENVIRONMENT === 'node' &&\n                this.opts.enableCRC\n              ) {\n                dataStream = createCrcReadStream(dataStream, crcInst);\n              }\n              await new Promise((resolve, reject) => {\n                const writeStream = fsp.createWriteStream(tempFilePath, {\n                  start: curTask.offset,\n                  flags: 'r+',\n                });\n\n                let isErr = false;\n                let err: any = null;\n                writeStream.on('close', () => {\n                  if (isErr) {\n                    reject(err);\n                  } else {\n                    resolve(undefined);\n                  }\n                });\n\n                writeStream.on('error', (_err) => {\n                  isErr = true;\n                  err = _err;\n                });\n\n                dataStream.pipe(writeStream);\n                dataStream.on('error', (err) => writeStream.destroy(err));\n                function handleOnceCancel() {\n                  if (isCancel()) {\n                    reject(new CancelError('cancel downloadFile'));\n                    // fix windows\n                    writeStream.end();\n                    dataStream.unpipe(writeStream);\n                    dataStream.off('data', handleOnceCancel);\n                  }\n                }\n                dataStream.on('data', handleOnceCancel);\n              });\n\n              if (isCancel()) {\n                throw new CancelError('cancel downloadFile');\n              }\n\n              await updateAfterDownloadPart(curTask, {\n                res: { ...res.data, rangeHashCrc64ecma: crcInst.getCrc64() },\n              });\n            } catch (_err) {\n              const err = _err as any;\n              consumedBytes -= consumedBytesThisTask;\n              consumedBytesThisTask = 0;\n\n              if (isCancelError(err)) {\n                throw err;\n              }\n\n              if (isCancel()) {\n                throw new CancelError('cancel downloadFile');\n              }\n\n              if (!firstErr) {\n                firstErr = err;\n              }\n              await updateAfterDownloadPart(curTask, { err });\n            }\n          }\n        })\n      );\n\n      if (firstErr) {\n        throw firstErr;\n      }\n\n      const serverCRC64 = headObjectRes.data['x-tos-hash-crc64ecma'];\n      if (this.opts.enableCRC && serverCRC64) {\n        const actualCrc64 = combineCRCInParts(getCheckpointContent());\n        if (actualCrc64 !== serverCRC64) {\n          throw new TosClientError(\n            `validate file crc64 failed. Expect crc64 ${serverCRC64}, actual crc64 ${actualCrc64}. Please try again.`\n          );\n        }\n      }\n    };\n\n    const handleEmptyObj = async () => {};\n\n    triggerProgressEvent(TriggerProgressEventType.start);\n    objectSize === 0 ? await handleEmptyObj() : await handleTasks();\n\n    try {\n      if (typeof input.customRenameFileAfterDownloadCompleted === 'function') {\n        await input.customRenameFileAfterDownloadCompleted(\n          tempFilePath,\n          filePath\n        );\n      } else {\n        await fsp.rename(tempFilePath, filePath);\n      }\n    } catch (_err) {\n      const err = _err as any;\n      triggerDownloadEvent({\n        type: DownloadEventType.RenameTempFileFailed,\n        err,\n      });\n      triggerDataTransfer(DataTransferType.Failed);\n      throw err;\n    }\n\n    triggerDownloadEvent({\n      type: DownloadEventType.RenameTempFileSucceed,\n    });\n    triggerProgressEvent(TriggerProgressEventType.renameTempFileSucceed);\n    triggerDataTransfer(DataTransferType.Succeed);\n    await rmCheckpointFile();\n\n    return headObjectRes;\n  };\n\n  try {\n    return await nextEnsureCloseFd();\n  } finally {\n    // there is no global fd, don't need to close fd\n  }\n}\n\nexport default downloadFile;\n\n/**\n * 即使 totalSize 是 0，也需要一个 Part，否则 Server 端会报错 read request body failed\n */\nfunction getAllTasks(totalSize: number, partSize: number) {\n  const tasks: Task[] = [];\n  for (let i = 0; ; ++i) {\n    const offset = i * partSize;\n    const currPartSize = Math.min(partSize, totalSize - offset);\n\n    tasks.push({\n      offset,\n      partSize: currPartSize,\n      partNumber: i + 1,\n    });\n\n    if ((i + 1) * partSize >= totalSize) {\n      break;\n    }\n  }\n\n  return tasks;\n}\n\nfunction getDefaultCheckpointFilePath(\n  bucket: string,\n  key: string,\n  versionId?: string\n) {\n  const originPath = `${bucket}_${key}.${versionId}.json`;\n  const normalizePath = originPath.replace(/[\\\\/]/g, '');\n  return normalizePath;\n}\n\nfunction combineCRCInParts(cp: DownloadFileCheckpointRecord) {\n  let res = '0';\n  const sortedPartsInfo =\n    cp.parts_info?.sort?.((a, b) => a.part_number - b.part_number) ?? [];\n  for (const part of sortedPartsInfo) {\n    res = combineCrc64(\n      res,\n      part.hash_crc64ecma,\n      part.range_end - part.range_start + 1\n    );\n  }\n  return res;\n}\n", "// @ts-nocheck\nimport { hashSha256, hmacSha256, stringify, parse } from './universal/crypto';\nimport { getSortedQueryString } from './utils';\n\nexport interface ISign {\n  signature(\n    opt: ISigOptions,\n    expiredAt: number,\n    credentials?: ISigCredentials\n  ): string;\n  signatureHeader(\n    opt: ISigOptions,\n    expiredAt?: number,\n    credentials?: ISigCredentials\n  ): Map<string, string>;\n  gnrCopySig(\n    opt: ISigOptions,\n    credentials: ISigCredentials\n  ): { key: string; value: string };\n  getSignatureQuery(\n    opt: ISigOptions,\n    expiredAt: number\n  ): { [key: string]: any };\n  getSignature(\n    reqOpts: ISigOptions,\n    expiredAt: number\n  ): { key: string; value: string };\n}\n\nexport interface ISigCredentials {\n  GetSecretKey(): string;\n  GetAccessKey(): string;\n}\n\nexport interface ISigPolicyQuery {\n  policy: {\n    conditions: (string[] | { bucket: string } | { key: string })[];\n  };\n}\n\nexport interface ISigOptions {\n  sigName?: string;\n  endpoints?: string;\n  bucket?: string;\n  headers?: { [key: string]: string | undefined };\n  region?: string;\n  serviceName?: string;\n  algorithm?: string;\n  path: string;\n  method: string;\n  query?: string;\n  datetime?: string;\n  host?: string;\n  port?: number;\n}\n\nexport interface ISigQueryOptions extends Omit<ISigOptions, 'query'> {\n  query?: Record<string, any>;\n}\n\nexport const SIG_QUERY = {\n  algorithm: 'tos-algorithm',\n  expiration: 'tos-expiration',\n  signame: 'tos-signame',\n  signature: 'tos-signature',\n\n  v4_algorithm: 'X-Tos-Algorithm',\n  v4_credential: 'X-Tos-Credential',\n  v4_date: 'X-Tos-Date',\n  v4_expires: 'X-Tos-Expires',\n  v4_signedHeaders: 'X-Tos-SignedHeaders',\n  v4_security_token: 'X-Tos-Security-Token',\n  v4_signature: 'X-Tos-Signature',\n  v4_content_sha: 'X-Tos-Content-Sha256',\n  v4_policy: 'X-Tos-Policy',\n\n\n};\n\nexport function isDefaultPort(port?: number) {\n  if (port && port !== 80 && port !== 443) {\n    return false;\n  }\n  return true;\n}\n\n/**\n * @api private\n */\nconst v4Identifier = 'request';\n\ninterface ISignV4Opt {\n  algorithm?: string;\n  region?: string;\n  serviceName?: string;\n  securityToken?: string;\n  bucket: string;\n}\n/**\n * @api private\n */\nexport class SignersV4 implements ISign {\n  private options: ISignV4Opt;\n  private credentials: ISigCredentials;\n  constructor(opt: ISignV4Opt, credentials: ISigCredentials) {\n    this.options = opt;\n    this.credentials = credentials;\n  }\n\n  /*\n   * normal v4 signature\n   * */\n  public signature = (\n    opt: ISigOptions,\n    expiredAt: number,\n    credentials?: ISigCredentials\n  ) => {\n    if (!credentials) {\n      credentials = this.credentials;\n    }\n    const parts: string[] = [];\n    const datatime = opt.datetime as string;\n    const credString = this.credentialString(datatime);\n    parts.push(\n      this.options.algorithm +\n        ' Credential=' +\n        credentials.GetAccessKey() +\n        '/' +\n        credString\n    );\n\n    // console.log(this.algorithm + ' Credential=' +\n    //   credentials.accessKeyId + '/' + credString)\n\n    parts.push('SignedHeaders=' + this.signedHeaders(opt));\n    parts.push('Signature=' + this.authorization(opt, credentials, 0));\n    return parts.join(', ');\n  };\n\n  public signatureHeader = (\n    opt: ISigOptions,\n    expiredAt?: number,\n    credentials?: ISigCredentials\n  ): Map<string, string> => {\n    // const datetime = (new Date(new Date().toUTCString())).Format(\"yyyyMMddTHHmmssZ\")\n    opt.datetime = this.getDateTime();\n    const header = new Map<string, string>();\n    /* istanbul ignore if */\n    if (!opt.headers) {\n      const h: { [key: string]: string } = {};\n      opt.headers = h;\n    }\n\n    opt.headers.host = `${opt.host}`;\n    /* istanbul ignore if */\n    if (!isDefaultPort(opt.port)) {\n      opt.headers.host += ':' + opt.port;\n    }\n    /* istanbul ignore if */\n    if (opt.endpoints) {\n      opt.headers.host = `${this.options.bucket}.${opt.endpoints}`;\n    }\n\n    header.set('host', opt.headers.host); // opt.endpoints as string)\n    header.set('x-tos-date', opt.datetime); // opt.datetime)\n    /* istanbul ignore if\n      if (opt.endpoints) {\n          let bucket = this.options.bucket;\n          if (opt.bucket) {\n              bucket = opt.bucket;\n          }\n          if (!opt.path || opt.path === '/' || opt.path === `/${bucket}`) {\n              opt.path = '/' + bucket;\n          } else {\n              opt.path = '/' + bucket + opt.path;\n          }\n      }\n      */\n    header.set('x-tos-content-sha256', this.hexEncodedBodyHash());\n    if (this.options.securityToken) {\n      header.set('x-tos-security-token', this.options.securityToken);\n    }\n    // x-tos- must to be signatured\n    header.forEach((value, key) => {\n      if (key.startsWith('x-tos')) {\n        opt.headers[key] = value;\n      }\n    });\n    opt.path = this.getEncodePath(opt.path);\n    const sign = this.signature(opt, 0, credentials);\n    header.set('authorization', sign);\n\n    return header;\n  };\n\n  public gnrCopySig = (\n    opt: ISigOptions,\n    credentials: ISigCredentials\n  ): { key: string; value: string } => {\n    return { key: '', value: '' };\n  };\n\n  public getSignature = (\n    opt: ISigOptions,\n    expiredAt: number\n  ): { key: ''; value: '' } => {\n    return { key: '', value: '' };\n  };\n\n  public getSignatureQuery = (\n    opt: ISigQueryOptions,\n    expiredAt: number\n  ): { [key: string]: any } => {\n    opt.datetime = this.getDateTime();\n    if (!opt.headers) {\n      const h: { [key: string]: string } = {};\n      opt.headers = h;\n    }\n\n    opt.headers.host = `${opt.host}`;\n    if (!isDefaultPort(opt.port)) {\n      opt.headers.host += ':' + opt.port;\n    }\n\n    opt.path = this.getEncodePath(opt.path);\n    if (opt.endpoints) {\n      opt.headers.host = `${this.options.bucket}.${opt.endpoints}`;\n      // opt.path = `${opt.path}`;\n    }\n\n    opt.headers[SIG_QUERY.v4_date] = opt.datetime;\n    const credString = this.credentialString(opt.datetime as string);\n    const res = {\n      ...(opt.query || {}),\n      [SIG_QUERY.v4_algorithm]: this.options.algorithm,\n      [SIG_QUERY.v4_content_sha]: this.hexEncodedBodyHash(),\n      [SIG_QUERY.v4_credential]:\n        this.credentials.GetAccessKey() + '/' + credString,\n      [SIG_QUERY.v4_date]: opt.datetime,\n      [SIG_QUERY.v4_expires]: '' + expiredAt,\n      [SIG_QUERY.v4_signedHeaders]: this.signedHeaders(opt),\n    };\n    if (this.options.securityToken) {\n      res[SIG_QUERY.v4_security_token] = this.options.securityToken;\n    }\n    opt.query = getSortedQueryString(res);\n\n    res[SIG_QUERY.v4_signature] = this.authorization(\n      opt,\n      this.credentials,\n      expiredAt\n    );\n    return res;\n  };\n\n  public getSignaturePolicyQuery = (\n    opt: ISigPolicyQuery,\n    expiredAt: number\n  ): { [key: string]: any } => {\n    opt.datetime = this.getDateTime();\n\n    const credString = this.credentialString(opt.datetime as string);\n    const res = {\n      [SIG_QUERY.v4_algorithm]: this.options.algorithm,\n      [SIG_QUERY.v4_credential]:\n        this.credentials.GetAccessKey() + '/' + credString,\n      [SIG_QUERY.v4_date]: opt.datetime,\n      [SIG_QUERY.v4_expires]: '' + expiredAt,\n      [SIG_QUERY.v4_policy]: stringify(\n        parse(JSON.stringify(opt.policy), 'utf-8'),\n        'base64'\n      ),\n    };\n    if (this.options.securityToken) {\n      res[SIG_QUERY.v4_security_token] = this.options.securityToken;\n    }\n    opt.query = getSortedQueryString(res);\n\n    res[SIG_QUERY.v4_signature] = this.authorization(\n      opt,\n      this.credentials,\n      expiredAt\n    );\n    return res;\n  };\n\n  private hexEncodedBodyHash = () => {\n    return 'UNSIGNED-PAYLOAD';\n    // return this.hexEncodedHash('');\n  };\n\n  private authorization = (\n    opt: ISigOptions,\n    credentials: ISigCredentials,\n    expiredAt: number\n  ) => {\n    /* istanbul ignore if */\n    if (!opt.datetime) {\n      return '';\n    }\n\n    const signingKey = this.getSigningKey(\n      credentials,\n      opt.datetime.substr(0, 8)\n    );\n    // console.log(\n    // 'signingKey:',\n    //  signingKey,\n    //  'sign:',\n    //  this.stringToSign(opt.datetime, opt)\n    //  );\n    return hmacSha256(signingKey, this.stringToSign(opt.datetime, opt), 'hex');\n  };\n\n  private getDateTime = () => {\n    const date = new Date(new Date().toUTCString());\n    const datetime =\n      date\n        .toISOString()\n        .replace(/\\..+/, '')\n        .replace(/-/g, '')\n        .replace(/:/g, '') + 'Z';\n    return datetime;\n  };\n  private credentialString = (datetime: string) => {\n    return this.createScope(\n      datetime.substr(0, 8),\n      this.options.region,\n      this.options.serviceName\n    );\n  };\n\n  private createScope = (date, region, serviceName) => {\n    return [date.substr(0, 8), region, serviceName, v4Identifier].join('/');\n  };\n\n  private getSigningKey = (credentials: ISigCredentials, date) => {\n    const kDate = hmacSha256(credentials.GetSecretKey(), date);\n    const kRegion = hmacSha256(kDate, this.options.region as string);\n    const kService = hmacSha256(kRegion, this.options.serviceName as string);\n    const signingKey = hmacSha256(kService, v4Identifier);\n\n    return signingKey;\n  };\n\n  private stringToSign = (datetime: string, opt: ISigOptions) => {\n    /* istanbul ignore if */\n    if (!this.options.algorithm) {\n      return '';\n    }\n\n    const parts: string[] = [];\n    parts.push(this.options.algorithm);\n    parts.push(datetime);\n    parts.push(this.credentialString(datetime));\n    const canonicalString =\n      'policy' in opt\n        ? this.canonicalStringPolicy(opt)\n        : this.canonicalString(opt);\n    // console.log('canonicalString',this.canonicalString(opt),' code:',this.hexEncodedHash(this.canonicalString(opt)));\n    parts.push(this.hexEncodedHash(canonicalString));\n    return parts.join('\\n');\n  };\n\n  private hexEncodedHash = string => {\n    return hashSha256(string, 'hex');\n  };\n\n  private canonicalString = (opt: ISigOptions) => {\n    const parts: any[] = [];\n    parts.push(opt.method);\n    parts.push(opt.path);\n    parts.push(this.getEncodePath(opt.query as string, false));\n    parts.push(this.canonicalHeaders(opt) + '\\n');\n    parts.push(this.signedHeaders(opt));\n    parts.push(this.hexEncodedBodyHash());\n    return parts.join('\\n');\n  };\n\n  private canonicalStringPolicy = (opt: ISigOptions) => {\n    const parts: any[] = [];\n    parts.push(this.getEncodePath(opt.query as string, false));\n    parts.push(this.hexEncodedBodyHash());\n    return parts.join('\\n');\n  };\n\n  private canonicalHeaders = (opt: ISigOptions) => {\n    const parts: string[] = [];\n    const needSignHeaders = getNeedSignedHeaders(opt.headers);\n\n    for (let key of needSignHeaders) {\n      const value = opt.headers[key];\n      key = key.toLowerCase();\n      parts.push(key + ':' + this.canonicalHeaderValues(value.toString()));\n    }\n\n    return parts.join('\\n');\n  };\n\n  private canonicalHeaderValues = (values: string) => {\n    return values.replace(/\\s+/g, ' ').replace(/^\\s+|\\s+$/g, '');\n  };\n\n  private signedHeaders = (opt: ISigOptions) => {\n    const keys: string[] = [];\n    const needSignHeaders = getNeedSignedHeaders(opt.headers);\n\n    for (let key of needSignHeaders) {\n      key = key.toLowerCase();\n      keys.push(key);\n    }\n\n    return keys.sort().join(';');\n  };\n\n  /**\n   * ! * ' () aren't transformed by encodeUrl, so they need be handled\n   */\n  private getEncodePath(path: string, encodeAll: boolean = true): string {\n    if (!path) {\n      return '';\n    }\n\n    let tmpPath = path;\n    if (encodeAll) {\n      tmpPath = path.replace(/%2F/g, '/');\n    }\n    tmpPath = tmpPath.replace(/\\(/g, '%28');\n    tmpPath = tmpPath.replace(/\\)/g, '%29');\n    tmpPath = tmpPath.replace(/!/g, '%21');\n    tmpPath = tmpPath.replace(/\\*/g, '%2A');\n    tmpPath = tmpPath.replace(/\\'/g, '%27');\n    return tmpPath;\n  }\n}\n\nexport class ISigV4Credentials implements ISigCredentials {\n  public securityToken: string;\n  public secretAccessKey: string;\n  public accessKeyId: string;\n\n  constructor(\n    securityToken?: string,\n    secretAccessKey?: string,\n    accessKeyId?: string\n  ) {\n    this.accessKeyId = accessKeyId as string;\n    this.secretAccessKey = secretAccessKey as string;\n    this.securityToken = securityToken as string;\n  }\n\n  public GetAccessKey(): string {\n    return this.accessKeyId;\n  }\n\n  public GetSecretKey(): string {\n    return this.secretAccessKey;\n  }\n}\n\nfunction getNeedSignedHeaders(headers: Record<string, unknown> | undefined) {\n  const needSignHeaders: string[] = [];\n  Object.keys(headers || {}).forEach((key: string) => {\n    if (key === 'host' || key.startsWith('x-tos-')) {\n      if (headers[key] != null) {\n        needSignHeaders.push(key);\n      }\n    }\n  });\n  return needSignHeaders.sort();\n}\n", "import http from 'http';\nimport https from 'https';\nimport { Socket } from 'net';\n\n// add missing type\ndeclare module 'http' {\n  interface AgentOptions {\n    // reference:\n    // https://stackoverflow.com/questions/51363855/how-to-configure-axios-to-use-ssl-certificate\n    rejectUnauthorized?: boolean;\n  }\n\n  interface Agent {\n    createConnection(...opts: unknown[]): Socket;\n  }\n}\n\ninterface TosAgentOptions extends http.AgentOptions {\n  tosOpts: {\n    enableVerifySSL: boolean;\n    connectionTimeout: number;\n    maxConnections: number;\n    idleConnectionTime: number;\n    isHttps: boolean;\n  };\n}\n\n// not use class grammar, because Agent is dynamic\nexport function TosAgent(opts: TosAgentOptions) {\n  const { tosOpts, ...agentOpts } = opts;\n  const Agent = tosOpts.isHttps ? https.Agent : http.Agent;\n  const agent = new Agent({\n    ...agentOpts,\n    keepAlive: true,\n    rejectUnauthorized: tosOpts.enableVerifySSL,\n    timeout: tosOpts.idleConnectionTime,\n  });\n\n  agent.maxFreeSockets = Infinity;\n  agent.maxTotalSockets = tosOpts.maxConnections;\n\n  const oriCreateConnection = agent.createConnection;\n  agent.createConnection = function (...args) {\n    const socket = oriCreateConnection.call(this, ...args);\n    let isTimeout = false;\n    let isConnected = false;\n    let connectTimer: NodeJS.Timeout | null = null;\n\n    // Place `setTimeout` in `process.nextTick` to avoid to\n    // trigger \"Connect timeout\" when debug\n    process.nextTick(() => {\n      if (isConnected) {\n        return;\n      }\n\n      connectTimer = setTimeout(() => {\n        isTimeout = true;\n      }, tosOpts.connectionTimeout);\n    });\n\n    socket.on('connect', () => {\n      isConnected = true;\n      if (connectTimer) {\n        clearTimeout(connectTimer);\n      }\n\n      if (isTimeout) {\n        socket.destroy(new Error('Connect timeout'));\n      }\n    });\n\n    return socket;\n  };\n\n  return agent;\n}\n", "import { hashMd5 } from '../universal/crypto';\nimport axios, {\n  AxiosAdapter,\n  AxiosInstance,\n  AxiosRequestConfig,\n  AxiosResponse,\n  Method,\n} from 'axios';\nimport { ISigV4Credentials, SignersV4 } from '../signatureV4';\nimport { Headers, StringKeys } from '../interface';\nimport TosServerError, { TosServerErrorData } from '../TosServerError';\nimport {\n  encodeHeadersValue,\n  getEndpoint,\n  getNormalDataFromError,\n  getSortedQueryString,\n  normalizeProxy,\n} from '../utils';\nimport version from '../version';\nimport { TosAgent } from '../nodejs/TosAgent';\nimport TosClientError from '../TosClientError';\nimport {\n  DEFAULT_CONTENT_TYPE,\n  getObjectInputKey,\n  lookupMimeType,\n  validateObjectName,\n} from './object/utils';\nimport { makeAxiosInst } from '../axios';\nimport type { CRCCls } from '../universal/crc';\nimport * as log from '../log';\nimport mpAdapter from '../axios-miniprogram-adapter';\nimport uniappAdapter from 'axios-adapter-uniapp';\nimport os from 'os';\nimport { retrySignatureNamespace } from '../axios';\n\nexport interface TOSConstructorOptions {\n  accessKeyId: string;\n  accessKeySecret: string;\n  stsToken?: string;\n  bucket?: string;\n  endpoint?: string;\n  /**\n   * default value: true\n   * when using proxyHost&proxyPort, it needs to be set to false\n   */\n  secure?: boolean;\n  region: string;\n\n  /**\n   * proxy for web, use it with the middleware of `./proxy`\n   */\n  proxy?:\n    | string\n    | {\n        url: string;\n        needProxyParams?: boolean;\n      };\n  /**\n   * proxy to general http proxy server, this feature doesn't work in browser environment.\n   * only support http proxy server.\n   * proxyHost and proxyPort are required if the proxy function works.\n   * HINT: need set `secure` field false\n   */\n  proxyHost?: string;\n  proxyPort?: number;\n  // username and password don't be supported currently\n  // proxyUsername?: string;\n  // proxyPassword?: string;\n\n  /**\n   * default value: true\n   */\n  enableVerifySSL?: boolean;\n\n  /**\n   * default value: true\n   */\n  autoRecognizeContentType?: boolean;\n\n  /**\n   * unit: ms\n   * default value: 120s\n   * disable if value <= 0\n   */\n  requestTimeout?: number;\n\n  /**\n   * unit: ms\n   * default value: 10s\n   * disable if value <= 0\n   */\n  connectionTimeout?: number;\n\n  /**\n   * default value: 1024\n   */\n  maxConnections?: number;\n\n  /**\n   * unit: ms\n   * default value: 30s\n   */\n  idleConnectionTime?: number;\n\n  /**\n   * default value: 3\n   *\n   * disable if value <= 0\n   */\n  maxRetryCount?: number;\n\n  // TODO: need more efficient way, 1min for 10M currently\n  /**\n   * default value: false\n   *\n   * CRC executed by js is slow currently, it's default value will be true if it is fast enough.\n   */\n  enableCRC?: boolean;\n\n  /**\n   * set request adapter to send request.\n   */\n  requestAdapter?: AxiosAdapter;\n\n  /**\n   * default value: false  ${bucket}.${endpoint}\n   * if true request will not combine `${bucket}.${endpoint}`\n   */\n  isCustomDomain?: boolean;\n\n  /**\n   * @private unstable option: false | true | undefined\n   * default value: undefined\n   * true:\n   * Allow SDK to internally catch server errors for 404 and return default values\n   * Allow SDK to internally change some put methods to delete methods when pass empty value\n   */\n  enableOptimizeMethodBehavior?: boolean;\n  /**\n   * @private unstable option\n   */\n  forcePathStyle?: boolean;\n\n  userAgentProductName?: string;\n  userAgentSoftName?: string;\n  userAgentSoftVersion?: string;\n  userAgentCustomizedKeyValues?: Record<string, string>;\n}\n\ninterface NormalizedTOSConstructorOptions extends TOSConstructorOptions {\n  secure: boolean;\n  endpoint: string;\n  enableVerifySSL: boolean;\n  autoRecognizeContentType: boolean;\n  requestTimeout: number;\n  connectionTimeout: number;\n  maxConnections: number;\n  idleConnectionTime: number;\n  maxRetryCount: number;\n  enableCRC: boolean;\n}\n\ninterface GetSignatureQueryUrlInput {\n  bucket: string;\n  method: Method;\n  path: string;\n  subdomain: boolean;\n  endpoint: string;\n  // unit: second\n  expires: number;\n  query?: Record<string, any>;\n}\n\ninterface GetSignaturePolicyQueryInput {\n  bucket: string;\n  expires: number;\n  policy: {\n    conditions: (string[] | { bucket: string } | { key: string })[];\n  };\n}\n\ntype GetSignatureQueryInput =\n  | GetSignatureQueryUrlInput\n  | GetSignaturePolicyQueryInput;\n\ninterface FetchOpts<T> {\n  needMd5?: boolean;\n  handleResponse?: (response: AxiosResponse<T>) => T;\n  subdomainBucket?: string;\n  axiosOpts?: AxiosRequestConfig;\n}\n\nexport interface TosResponse<T> {\n  data: T;\n\n  statusCode: number;\n  headers: Headers;\n  /**\n   * identifies the errored request, equals to headers['x-tos-request-id'].\n   * If you has any question about the request, please send the requestId and id2 to TOS worker.\n   */\n  requestId: string;\n\n  /**\n   * identifies the errored request, equals to headers['x-tos-id-2'].\n   * If you has any question about the request, please send the requestId and id2 to TOS worker.\n   */\n  id2: string;\n}\n\nexport class TOSBase {\n  opts: NormalizedTOSConstructorOptions;\n\n  axiosInst: AxiosInstance;\n\n  userAgent: string;\n\n  private httpAgent: unknown;\n  private httpsAgent: unknown;\n\n  constructor(_opts: TOSConstructorOptions) {\n    this.opts = this.normalizeOpts(_opts);\n\n    if (process.env.TARGET_ENVIRONMENT === 'node') {\n      this.httpAgent = TosAgent({ tosOpts: { ...this.opts, isHttps: false } });\n      // fix axios issue, it uses `httpsAgent` although http proxy is enabled.\n      const isProxy = !!this.opts.proxyHost;\n      this.httpsAgent = TosAgent({\n        tosOpts: { ...this.opts, isHttps: !isProxy },\n      });\n    }\n\n    this.userAgent = this.getUserAgent();\n    this.axiosInst = makeAxiosInst(this.opts.maxRetryCount);\n  }\n\n  private normalizeOpts(_opts: TOSConstructorOptions) {\n    // 对字符串参数做 trim 操作\n    const trimKeys = [\n      'accessKeyId',\n      'accessKeySecret',\n      'stsToken',\n      'region',\n      'endpoint',\n    ] as const;\n    trimKeys.forEach((key) => {\n      const value = _opts[key];\n      if (typeof value === 'string') {\n        // maybe undefined\n        _opts[key] = value.trim();\n      }\n    });\n\n    const mustKeys = ['accessKeyId', 'accessKeySecret', 'region'];\n    const mustKeysErrorStr = mustKeys\n      .filter((key) => !(_opts as any)[key])\n      .join(', ');\n\n    if (mustKeysErrorStr) {\n      throw new TosClientError(`lack params: ${mustKeysErrorStr}.`);\n    }\n\n    const endpoint = _opts.endpoint || getEndpoint(_opts.region);\n    if (!endpoint) {\n      throw new TosClientError(\n        `the value of param region is invalid, correct values are cn-beijing, cn-nantong etc.`\n      );\n    }\n\n    if (endpoint.includes('s3')) {\n      throw new TosClientError(\n        `do not support s3 endpoint, please use tos endpoint.`\n      );\n    }\n\n    const secure = _opts.secure == null ? true : !!_opts.secure;\n    const _default = <T extends unknown>(\n      v: T | undefined | null,\n      defaultValue: T\n    ) => (v == null ? defaultValue : v);\n\n    const enableCRC = _opts.enableCRC ?? false;\n    if (enableCRC && process.env.TARGET_ENVIRONMENT === 'browser') {\n      throw new TosClientError('not support crc in browser environment');\n    }\n\n    return {\n      ..._opts,\n      endpoint,\n      secure,\n      enableVerifySSL: _default(_opts.enableVerifySSL, true),\n      autoRecognizeContentType: _default(_opts.autoRecognizeContentType, true),\n      requestTimeout: _default(_opts.requestTimeout, 120_000),\n      connectionTimeout: _default(_opts.connectionTimeout, 10_000),\n      maxConnections: _default(_opts.maxConnections, 1024),\n      idleConnectionTime: _default(_opts.idleConnectionTime, 30_000),\n      maxRetryCount: _default(_opts.maxRetryCount, 3),\n      enableCRC: _opts.enableCRC ?? false,\n      requestAdapter: getAdapter(),\n    };\n  }\n\n  private getUserAgent() {\n    // ve-tos-go-sdk/v2.0.0 (linux/amd64;go1.17.0)\n    const language =\n      process.env.TARGET_ENVIRONMENT === 'browser' ? 'browserjs' : 'nodejs';\n    const sdkVersion = `ve-tos-${language}-sdk/v${version}`;\n    if (process.env.TARGET_ENVIRONMENT === 'browser') {\n      return sdkVersion;\n    }\n\n    const osType = (() => {\n      const oriType = os.type();\n      const aliasType: Record<string, string> = {\n        Linux: 'linux',\n        Darwin: 'darwin',\n        Windows_NT: 'windows',\n      };\n      return aliasType[oriType] || oriType;\n    })();\n    const nodeVersion = (() => {\n      return process.version.replaceAll('v', '');\n    })();\n    const stdStr = `${sdkVersion} (${osType}/${process.arch};nodejs${nodeVersion})`;\n    const moreStr = (() => {\n      const { userAgentProductName, userAgentSoftName, userAgentSoftVersion } =\n        this.opts;\n      let customStr = Object.entries(\n        this.opts.userAgentCustomizedKeyValues || {}\n      )\n        .map(([k, v]) => {\n          return `${k}/${v}`;\n        })\n        .join(';');\n      customStr = customStr ? `(${customStr})` : '';\n\n      if (\n        !userAgentProductName &&\n        !userAgentSoftName &&\n        !userAgentSoftVersion &&\n        !customStr\n      ) {\n        return '';\n      }\n      const defaultValue = 'undefined';\n      const productSoftStr = [\n        userAgentProductName,\n        userAgentSoftName,\n        userAgentSoftVersion,\n      ]\n        .map((it) => it || defaultValue)\n        .join('/');\n\n      return [productSoftStr, customStr].filter(Boolean).join(' ');\n    })();\n\n    return [stdStr, moreStr].filter(Boolean).join(' -- ');\n  }\n\n  protected async fetch<Data>(\n    method: Method,\n    path: string,\n    query: Record<string, any>,\n    headers: Headers,\n    body?: Object | File | Blob | NodeJS.ReadableStream,\n    opts?: FetchOpts<Data>\n  ): Promise<TosResponse<Data>> {\n    const handleResponse = opts?.handleResponse || ((res) => res.data);\n    const needMd5 = opts?.needMd5 || false;\n\n    if (body && needMd5) {\n      const md5String = hashMd5(JSON.stringify(body), 'base64');\n      headers['content-md5'] = md5String;\n    }\n\n    const [endpoint, newPath] = (() => {\n      if (opts?.subdomainBucket && this.opts.forcePathStyle) {\n        return [this.opts.endpoint, `/${opts.subdomainBucket}${path}`];\n      }\n      // if isCustomDomain true, not add subdomainBucket\n      if (opts?.subdomainBucket && !this.opts.isCustomDomain) {\n        // endpoint is ip address\n        if (/^(\\d|:)/.test(this.opts.endpoint)) {\n          return [this.opts.endpoint, `/${opts.subdomainBucket}${path}`];\n        }\n        return [`${opts?.subdomainBucket}.${this.opts.endpoint}`, path];\n      }\n      return [this.opts.endpoint, path];\n    })();\n    path = newPath;\n\n    headers = encodeHeadersValue(headers);\n\n    const signOpt = {\n      // TODO: delete endpoints and buckets\n      endpoints: undefined,\n      bucket: '',\n\n      method,\n      headers: { ...headers },\n      path,\n      query: getSortedQueryString(query),\n      host: endpoint,\n    };\n\n    const signv4 = new ISigV4Credentials(\n      this.opts.stsToken,\n      this.opts.accessKeySecret,\n      this.opts.accessKeyId\n    );\n\n    const sig = new SignersV4(\n      {\n        algorithm: 'TOS4-HMAC-SHA256',\n        region: this.opts.region,\n        serviceName: 'tos',\n        bucket: '',\n        securityToken: this.opts.stsToken,\n      },\n      signv4\n    );\n\n    const signatureHeaders = sig.signatureHeader(signOpt);\n    const reqHeaders = { ...headers };\n\n    const reqOpts: AxiosRequestConfig = {\n      method,\n      baseURL: `http${this.opts.secure ? 's' : ''}://${endpoint}`,\n      url: path,\n      params: query,\n      headers: reqHeaders,\n      data: body,\n    };\n\n    signatureHeaders.forEach((value, key) => {\n      reqOpts.headers[key] = value;\n    });\n\n    const normalizedProxy = normalizeProxy(this.opts.proxy);\n    if (normalizedProxy?.url && !this.opts.proxyHost) {\n      // proxy for nodejs middleware server\n      reqOpts.baseURL = normalizedProxy.url;\n      if (normalizedProxy?.needProxyParams) {\n        reqOpts.params['x-proxy-tos-host'] = endpoint;\n        delete reqHeaders['host'];\n      }\n    } else if (this.opts.proxyHost) {\n      if (!this.opts.proxyPort) {\n        throw new TosClientError(\n          'The `proxyPort` is required if `proxyHost` is truly.'\n        );\n      }\n\n      // proxy for general proxy server\n      reqOpts.proxy = {\n        host: this.opts.proxyHost,\n        port: this.opts.proxyPort,\n        protocol: 'http',\n      };\n    }\n\n    reqHeaders['user-agent'] = this.userAgent;\n    if (this.opts.requestTimeout > 0 && this.opts.requestTimeout !== Infinity) {\n      reqOpts.timeout = this.opts.requestTimeout;\n    }\n\n    if (process.env.TARGET_ENVIRONMENT === 'node') {\n      reqOpts.httpAgent = this.httpAgent;\n      reqOpts.httpsAgent = this.httpsAgent;\n    }\n\n    try {\n      const logReqOpts = { ...reqOpts };\n      delete logReqOpts.httpAgent;\n      delete logReqOpts.httpsAgent;\n      log.TOS('reqOpts: ', logReqOpts);\n      const res = await this.axiosInst({\n        ...{\n          maxBodyLength: Infinity,\n          maxContentLength: Infinity,\n          adapter: this.opts.requestAdapter,\n        },\n        ...reqOpts,\n        ...(opts?.axiosOpts || {}),\n        [retrySignatureNamespace]: {\n          signOpt,\n          sigInst: sig,\n        },\n      });\n\n      const data = handleResponse(res);\n      return {\n        data,\n        statusCode: res.status,\n        headers: res.headers,\n        requestId: res.headers['x-tos-request-id'],\n        id2: res.headers['x-tos-id-2'],\n      };\n    } catch (err) {\n      if (\n        axios.isAxiosError(err) &&\n        err.response?.headers?.['x-tos-request-id']\n      ) {\n        // it's ServerError only if `RequestId` exists\n        const response: AxiosResponse<TosServerErrorData> = err.response;\n        log.TOS('TosServerError response: ', response);\n        const err2 = new TosServerError(response);\n        throw err2;\n      }\n\n      // it is neither ServerError nor ClientError, it's other error\n      log.TOS('err: ', err);\n      throw err;\n    }\n  }\n\n  protected async fetchBucket<Data>(\n    bucket: string | undefined,\n    method: Method,\n    query: any,\n    headers: Headers,\n    body?: Object | File | Blob | NodeJS.ReadableStream,\n    opts?: FetchOpts<Data>\n  ): Promise<TosResponse<Data>> {\n    const actualBucket = bucket || this.opts.bucket;\n    if (!actualBucket) {\n      throw new TosClientError('Must provide bucket param');\n    }\n    return this.fetch(method, '/', query, headers, body, {\n      ...opts,\n      subdomainBucket: actualBucket,\n    });\n  }\n\n  protected async _fetchObject<Data>(\n    input: { bucket?: string; key: string } | string,\n    method: Method,\n    query: any,\n    headers: Headers,\n    body?: Object | File | Blob | NodeJS.ReadableStream,\n    opts?: FetchOpts<Data>\n  ): Promise<TosResponse<Data>> {\n    const actualBucket =\n      (typeof input !== 'string' && input.bucket) || this.opts.bucket;\n    const actualKey = typeof input === 'string' ? input : input.key;\n    if (!actualBucket) {\n      throw new TosClientError('Must provide bucket param');\n    }\n    validateObjectName(actualKey);\n\n    return this.fetch(\n      method,\n      `/${encodeURIComponent(actualKey)}`,\n      query,\n      headers,\n      body,\n      {\n        ...opts,\n        subdomainBucket: actualBucket,\n      }\n    );\n  }\n\n  protected getSignatureQuery(\n    input: GetSignatureQueryInput\n  ): Record<string, string> {\n    const signv4 = new ISigV4Credentials(\n      this.opts.stsToken,\n      this.opts.accessKeySecret,\n      this.opts.accessKeyId\n    );\n\n    const sig = new SignersV4(\n      {\n        algorithm: 'TOS4-HMAC-SHA256',\n        region: this.opts.endpoint,\n        serviceName: 'tos',\n        // SignV4 uses this.options.bucket, so set it here\n        bucket: input.bucket,\n        securityToken: this.opts.stsToken,\n      },\n      signv4\n    );\n\n    if ('policy' in input) {\n      return sig.getSignaturePolicyQuery(\n        {\n          policy: input.policy,\n        },\n        input.expires\n      );\n    } else {\n      return sig.getSignatureQuery(\n        {\n          method: input.method,\n          path: input.path,\n          endpoints: input.subdomain ? input.endpoint : undefined,\n          host: input.endpoint,\n          query: input.query,\n        },\n        input.expires\n      );\n    }\n  }\n\n  protected getObjectPath = (\n    opts: { bucket?: string; key: string } | string\n  ) => {\n    const actualBucket =\n      (typeof opts !== 'string' && opts.bucket) || this.opts.bucket;\n    const actualKey = typeof opts === 'string' ? opts : opts.key;\n    if (!actualBucket) {\n      throw new TosClientError('Must provide bucket param');\n    }\n    return `/${actualBucket}/${encodeURIComponent(actualKey)}`;\n  };\n\n  protected normalizeBucketInput<T extends { bucket: string }>(\n    input: T | string\n  ): T {\n    return (typeof input === 'string' ? { bucket: input } : input) as T;\n  }\n  protected normalizeObjectInput<T extends { key: string }>(\n    input: T | string\n  ): T {\n    return (typeof input === 'string' ? { key: input } : input) as T;\n  }\n\n  protected setObjectContentTypeHeader = (\n    input: string | { key: string },\n    headers: Headers\n  ): void => {\n    if (headers['content-type'] != null) {\n      return;\n    }\n\n    let mimeType = DEFAULT_CONTENT_TYPE;\n    const key = getObjectInputKey(input);\n\n    if (this.opts.autoRecognizeContentType) {\n      mimeType = lookupMimeType(key) || mimeType;\n    }\n\n    if (mimeType) {\n      headers['content-type'] = mimeType;\n    }\n  };\n\n  protected getNormalDataFromError = getNormalDataFromError;\n}\n\nexport default TOSBase;\n\nfunction getAdapter(): AxiosAdapter | undefined {\n  if (process.env.TARGET_ENVIRONMENT === 'node') {\n    // nodejs env\n    return undefined;\n  }\n  if (typeof window !== 'undefined' && typeof window.location !== 'undefined') {\n    // browser env\n    return undefined;\n  }\n\n  switch (true) {\n    case typeof wx !== 'undefined':\n    case typeof swan !== 'undefined':\n    case typeof dd !== 'undefined':\n    case typeof my !== 'undefined':\n      return mpAdapter as AxiosAdapter;\n    case typeof uni !== 'undefined':\n      return uniappAdapter as AxiosAdapter;\n    default:\n      return undefined;\n  }\n}\n", "import axios from 'axios';\nimport { Readable } from 'stream';\nimport { getSortedQueryString, safeSync } from './utils';\nimport { ISigV4Credentials, SignersV4 } from './signatureV4';\nimport * as log from './log';\n\nexport const retryNamespace = '__retryConfig__';\nexport const retrySignatureNamespace = '__retrySignature__';\n\nexport interface RetryConfig {\n  // 对于文件流重试应该重新生成新的文件流\n  makeRetryStream?: () => NodeJS.ReadableStream | undefined;\n\n  beforeRetry?: () => void;\n}\n\ninterface InnerRetryConfig extends RetryConfig {\n  retryCount?: number;\n}\n\ninterface RetrySignature {\n  signOpt: any;\n  sigInst: SignersV4;\n}\n\ndeclare module 'axios' {\n  interface AxiosRequestConfig {\n    __retryConfig__?: RetryConfig;\n    __retrySignature__?: RetrySignature;\n  }\n}\n\nfunction isNetworkError(error: any) {\n  // no response or no requestId, ignore no network(error.code is undefined)\n  return (\n    (!error.response && Boolean(error.code)) ||\n    (error.response && !error.response.headers?.['x-tos-request-id'])\n  );\n}\n\nfunction isCanRetryStatusCode(error: any) {\n  if (!error.response) {\n    return false;\n  }\n\n  const { status } = error.response;\n  if (status === 429 || status >= 500) {\n    return true;\n  }\n  return false;\n}\n\nconst BROWSER_NEED_DELETE_HEADERS = ['content-length', 'user-agent', 'host'];\n\nexport const makeAxiosInst = (maxRetryCount: number) => {\n  const axiosInst = axios.create();\n  // set `axiosInst` default values to avoid being affected by the global default values of axios\n  axiosInst.defaults.auth = undefined;\n  axiosInst.defaults.responseType = 'json';\n  axiosInst.defaults.params = undefined;\n  axiosInst.defaults.headers = {};\n  axiosInst.defaults.withCredentials = false;\n  axiosInst.defaults.maxContentLength = -1;\n  axiosInst.defaults.maxBodyLength = -1;\n  axiosInst.defaults.maxRedirects = 0;\n  axiosInst.defaults.validateStatus = function (status) {\n    return status >= 200 && status < 300; // default\n  };\n  axiosInst.defaults.decompress = false;\n  axiosInst.defaults.transitional = {\n    silentJSONParsing: true,\n    forcedJSONParsing: true,\n    clarifyTimeoutError: false,\n  };\n\n  // delete browser headers\n  if (process.env.TARGET_ENVIRONMENT === 'browser') {\n    axiosInst.interceptors.request.use((config) => {\n      if (!config.headers) {\n        return config;\n      }\n\n      Object.keys(config.headers).forEach((key) => {\n        if (BROWSER_NEED_DELETE_HEADERS.includes(key.toLowerCase())) {\n          delete config.headers[key];\n        }\n      });\n\n      return config;\n    });\n  }\n\n  // headers\n  const ensureHeaders = (v: any) => {\n    v.headers = v.headers || v.header || v?.response?.headers || {};\n    return v;\n  };\n  axiosInst.interceptors.response.use(ensureHeaders, (error) => {\n    ensureHeaders(error);\n    return Promise.reject(error);\n  });\n\n  // decode header. Encode headers' value by encodeHeadersValue method before calling axios\n  function handleResponseHeader(headers: Record<string, string>) {\n    Object.entries(headers).forEach(([key, value]) => {\n      const [err, decodedValue] = safeSync(() => decodeURI(value));\n      if (err || decodedValue == null || decodedValue === value) {\n        return;\n      }\n      let sArr = [];\n      const valueArr = `${value}`.match(/./gu)!;\n      const decodedValueArr = decodedValue.match(/./gu)!;\n      for (let i = 0, j = 0; i < decodedValueArr.length; ) {\n        const ch = decodedValueArr[i];\n        if (ch === valueArr[j]) {\n          sArr.push(ch);\n          ++i;\n          ++j;\n          continue;\n        }\n\n        const encodedCh = encodeURIComponent(ch);\n        if (ch.length > 1 || ch.charCodeAt(0) >= 128) {\n          sArr.push(ch);\n        } else {\n          sArr.push(encodedCh);\n        }\n        ++i;\n        j += encodedCh.length;\n      }\n      headers[key] = sArr.join('');\n    });\n  }\n  axiosInst.interceptors.response.use(\n    (res) => {\n      if (!res.headers) {\n        return res;\n      }\n      handleResponseHeader(res.headers);\n      return res;\n    },\n    async (error) => {\n      if (!axios.isAxiosError(error)) {\n        return Promise.reject(error);\n      }\n\n      const headers = error.response?.headers;\n      if (!headers) {\n        return Promise.reject(error);\n      }\n      handleResponseHeader(headers);\n      return Promise.reject(error);\n    }\n  );\n\n  // retry\n  axiosInst.interceptors.response.use(undefined, async (error) => {\n    const { config } = error;\n    if (!config) {\n      return Promise.reject(error);\n    }\n\n    if (!config[retryNamespace]) {\n      config[retryNamespace] = {};\n    }\n    const retryConfig: InnerRetryConfig = config[retryNamespace];\n    const retryCount = retryConfig.retryCount ?? 0;\n\n    let newData = config.data;\n    const canRetryData = (() => {\n      if (process.env.TARGET_ENVIRONMENT === 'node') {\n        if (config.data && config.data instanceof Readable) {\n          const v = retryConfig.makeRetryStream?.();\n          if (!v) {\n            return false;\n          }\n          newData = v;\n        }\n      }\n      return true;\n    })();\n\n    const canRetry =\n      (isNetworkError(error) || isCanRetryStatusCode(error)) &&\n      retryCount < maxRetryCount &&\n      canRetryData;\n\n    if (!canRetry) {\n      return Promise.reject(error);\n    }\n\n    const retrySignature = config[retrySignatureNamespace] as RetrySignature;\n    if (retrySignature) {\n      const { signOpt, sigInst } = retrySignature;\n      const signatureHeaders = sigInst.signatureHeader(signOpt);\n      signatureHeaders.forEach((value, key) => {\n        config.headers[key] = value;\n      });\n    }\n\n    //console.log('config: ', config)\n    log.TOS('retryConfig: ', config);\n    const nextConfig = {\n      ...config,\n      data: newData,\n      [retryNamespace]: {\n        ...retryConfig,\n        retryCount: retryCount + 1,\n      },\n    };\n\n    retryConfig.beforeRetry?.();\n    return axiosInst(nextConfig);\n  });\n\n  return axiosInst;\n};\n", "import { covertCamelCase2Kebab, makeArrayProp } from '../../utils';\nimport TOSBase from '../base';\n\nexport interface ListObjectsInput {\n  bucket?: string;\n  continuationToken?: string;\n  delimiter?: string;\n  encodingType?: string;\n  fetchOwner?: string;\n  maxKeys?: string | number;\n  prefix?: string;\n  marker?: string;\n\n  /**\n   * use `marker` instead of `startAfter`\n   */\n  startAfter?: string;\n  /**\n   * equal to listObjectVersions when input\n   */\n  versions?: string;\n  listType?: string;\n  versionIdMarker?: string;\n  /**\n   * only works when pass versions field\n   */\n  keyMarker?: string;\n}\n\nexport interface ListObjectsContentItem {\n  ETag: string;\n  Key: string;\n  // \"2021-08-02T09:53:27.000Z\"\n  LastModified: string;\n  Owner: { ID: string; DisplayName: string };\n  Size: number;\n  StorageClass: string;\n}\n\nexport interface ListObjectsVersionItem {\n  ETag: string;\n  IsLatest: boolean;\n  Key: string;\n  LastModified: string;\n  Owner: { ID: string; DisplayName: string };\n  Size: number;\n  StorageClass: string;\n  VersionId: string;\n}\n\nexport interface ListObjectDeleteMarkerItem {\n  ETag: string;\n  IsLatest: boolean;\n  Key: string;\n  LastModified: string;\n  Owner: { ID: string; DisplayName: string };\n  Size: number;\n  StorageClass: string;\n  VersionId: string;\n}\n\nexport interface ListedCommonPrefix {\n  Prefix: string;\n}\nexport interface ListObjectsOutput {\n  CommonPrefixes: ListedCommonPrefix[];\n  Contents: ListObjectsContentItem[];\n  IsTruncated: boolean;\n  Marker: string;\n  MaxKeys: number;\n  KeyMarker?: string;\n  Name: string;\n  Prefix: string;\n  ContinuationToken?: string;\n  NextContinuationToken?: string;\n  Delimiter?: string;\n  EncodingType?: string;\n  NextMarker?: string;\n  VersionIdMarker?: string;\n  Versions: ListObjectsVersionItem[];\n  NextKeyMarker?: string;\n  DeleteMarkers: ListObjectDeleteMarkerItem[];\n  NextVersionIdMarker?: string;\n}\n\nclass TOSListObjects extends TOSBase {\n  listObjects = listObjects;\n  listObjectVersions = listObjectVersions;\n}\n\n/**\n *\n * @deprecated use listObjectsType2 instead\n * @returns\n */\nexport async function listObjects(\n  this: TOSListObjects,\n  input: ListObjectsInput = {}\n) {\n  const { bucket, ...nextQuery } = input;\n  const ret = await this.fetchBucket<ListObjectsOutput>(\n    input.bucket,\n    'GET',\n    covertCamelCase2Kebab(nextQuery),\n    {}\n  );\n  const arrayProp = makeArrayProp(ret.data);\n  arrayProp('CommonPrefixes');\n  arrayProp('Contents');\n  arrayProp('Versions');\n  arrayProp('DeleteMarkers');\n  return ret;\n}\n\nexport type ListObjectVersionsInput = Pick<\n  ListObjectsInput,\n  | 'bucket'\n  | 'prefix'\n  | 'delimiter'\n  | 'keyMarker'\n  | 'versionIdMarker'\n  | 'maxKeys'\n  | 'encodingType'\n>;\n\nexport interface listObjectVersionsOutput {\n  Name: string;\n  Prefix: string;\n  KeyMarker?: string;\n  VersionIdMarker?: string;\n  MaxKeys: number;\n  Delimiter?: string;\n  IsTruncated: boolean;\n  EncodingType?: string;\n  NextKeyMarker?: string;\n  NextVersionIdMarker?: string;\n  CommonPrefixes: ListedCommonPrefix[];\n  Versions: ListObjectsVersionItem[];\n  DeleteMarkers: ListObjectDeleteMarkerItem[];\n}\n\nexport async function listObjectVersions(\n  this: TOSListObjects,\n  input: ListObjectVersionsInput = {}\n) {\n  const { bucket, ...nextQuery } = input;\n  const ret = await this.fetchBucket<listObjectVersionsOutput>(\n    input.bucket,\n    'GET',\n    covertCamelCase2Kebab({ versions: '', ...nextQuery }),\n    {}\n  );\n  const arrayProp = makeArrayProp(ret.data);\n  arrayProp('CommonPrefixes');\n  arrayProp('Versions');\n  arrayProp('DeleteMarkers');\n  return ret;\n}\n", "import { covertCamelCase2Kebab, makeArrayProp } from '../../utils';\nimport TOSBase, { TosResponse } from '../base';\n\nexport interface ListObjectsType2Input {\n  bucket?: string;\n  prefix?: string;\n  delimiter?: string;\n  encodingType?: string;\n  /**\n   * if not specify `maxKeys` field, default maxKeys value is 1000.\n   */\n  maxKeys?: number;\n  continuationToken?: string;\n  startAfter?: string;\n  /**\n   * default value: false\n   * if set false, the method will keep fetch objects until get `maxKeys` objects.\n   * if set true,  the method will fetch objects once\n   */\n  listOnlyOnce?: boolean;\n}\n\nexport interface ListObjectsType2ContentItem {\n  ETag: string;\n  Key: string;\n  // \"2021-08-02T09:53:27.000Z\"\n  LastModified: string;\n  Owner?: { ID: string; DisplayName: string };\n  Size: number;\n  StorageClass: string;\n  HashCrc64ecma?: string;\n}\n\nexport interface ListObjectsType2VersionItem {\n  ETag: string;\n  IsLatest: boolean;\n  Key: string;\n  LastModified: string;\n  Owner: { ID: string; DisplayName: string };\n  Size: number;\n  StorageClass: string;\n  VersionId: string;\n}\n\nexport interface ListObjectDeleteMarkerItem {\n  ETag: string;\n  IsLatest: boolean;\n  Key: string;\n  LastModified: string;\n  Owner: { ID: string; DisplayName: string };\n  Size: number;\n  StorageClass: string;\n  VersionId: string;\n}\n\nexport interface ListedCommonPrefix {\n  Prefix: string;\n}\n\nexport interface ListObjectsType2Output {\n  Name: string;\n  Prefix: string;\n  MaxKeys: number;\n  Delimiter?: string;\n  EncodingType?: string;\n  IsTruncated: boolean;\n  KeyCount: number;\n  StartAfter?: string;\n  ContinuationToken?: string;\n  NextContinuationToken?: string;\n  CommonPrefixes: ListedCommonPrefix[];\n  Contents: ListObjectsType2ContentItem[];\n}\n\nclass TOSListObjectsType2 extends TOSBase {\n  listObjectsType2 = listObjectsType2;\n}\nconst DefaultListMaxKeys = 1000;\n\nexport async function listObjectsType2(\n  this: TOSListObjectsType2,\n  input: ListObjectsType2Input = {}\n): Promise<TosResponse<ListObjectsType2Output>> {\n  const { listOnlyOnce = false } = input;\n\n  let output;\n  if (!input.maxKeys) {\n    input.maxKeys = DefaultListMaxKeys;\n  }\n\n  if (listOnlyOnce) {\n    output = await listObjectsType2Once.call(this, input);\n  } else {\n    const maxKeys = input.maxKeys;\n    let params = {\n      ...input,\n      maxKeys,\n    };\n    while (true) {\n      const res = await listObjectsType2Once.call(this, params);\n      if (output == null) {\n        output = res;\n      } else {\n        output = {\n          ...res,\n          data: output.data,\n        };\n        output.data.KeyCount += res.data.KeyCount;\n        output.data.IsTruncated = res.data.IsTruncated;\n        output.data.NextContinuationToken = res.data.NextContinuationToken;\n        output.data.Contents = output.data.Contents.concat(res.data.Contents);\n        output.data.CommonPrefixes = output.data.CommonPrefixes.concat(\n          res.data.CommonPrefixes\n        );\n      }\n\n      if (!res.data.IsTruncated || output.data.KeyCount >= maxKeys) {\n        break;\n      }\n\n      params.continuationToken = res.data.NextContinuationToken;\n      params.maxKeys = params.maxKeys - res.data.KeyCount;\n    }\n  }\n\n  return output;\n}\nasync function listObjectsType2Once(\n  this: TOSListObjectsType2,\n  input: ListObjectsType2Input\n) {\n  const { bucket, ...nextQuery } = input;\n\n  const ret = await this.fetchBucket<ListObjectsType2Output>(\n    input.bucket,\n    'GET',\n    {\n      'list-type': 2,\n      ...covertCamelCase2Kebab(nextQuery),\n    },\n    {}\n  );\n  const arrayProp = makeArrayProp(ret.data);\n  arrayProp('CommonPrefixes');\n  arrayProp('Contents');\n  return ret;\n}\n", "import { TosClientError } from './browser-index';\nimport { TOSBase, type TOSConstructorOptions } from './methods/base';\nimport { paramsSerializer } from './utils';\nimport { getObjectV2 } from './methods/object/getObject';\nimport headObject from './methods/object/headObject';\nimport { listObjectVersions, listObjects } from './methods/object/listObjects';\nimport downloadFile from './methods/object/downloadFile';\nimport { listObjectsType2 } from './methods/object/listObjectsType2';\n\nexport interface ShareLinkClientOptions\n  extends Omit<\n    TOSConstructorOptions,\n    'region' | 'accessKeyId' | 'accessKeySecret' | 'endpoint' | 'bucket'\n  > {\n  policyUrl: string;\n}\n\ninterface ParsedPolicyUrlVal {\n  origin: string;\n  host: string;\n  search: string;\n}\n\n/** @private unstable */\nexport class ShareLinkClient extends TOSBase {\n  shareLinkClientOpts: ShareLinkClientOptions;\n\n  private parsedPolicyUrlVal: ParsedPolicyUrlVal;\n\n  modifyAxiosInst() {\n    const axiosInst = this.axiosInst;\n\n    axiosInst.interceptors.request.use((config) => {\n      const headers = config.headers || {};\n      delete headers['authorization'];\n      headers['host'] = this.parsedPolicyUrlVal.host;\n      config.baseURL = this.parsedPolicyUrlVal.origin;\n      config.paramsSerializer = (params) => {\n        const addQueryStr = paramsSerializer(params);\n        return [this.parsedPolicyUrlVal.search, addQueryStr]\n          .filter((it) => it.trim())\n          .join('&');\n      };\n      return config;\n    });\n  }\n\n  constructor(_opts: ShareLinkClientOptions) {\n    super({\n      ..._opts,\n\n      bucket: 'fake-bucket',\n      region: 'fake-region',\n      accessKeyId: 'fake-accessKeyId',\n      accessKeySecret: 'fake-accessKeySecret',\n      endpoint: 'fake-endpoint.com',\n    });\n\n    this.shareLinkClientOpts = _opts;\n    this.parsedPolicyUrlVal = this.initParsedPolicyUrlVal();\n    this.modifyAxiosInst();\n  }\n\n  private initParsedPolicyUrlVal(): ParsedPolicyUrlVal {\n    const reg = /(https?:\\/\\/(?:[^@]+@)?([^/?]+))[^?]*\\?(.+)/;\n    const matched = this.shareLinkClientOpts.policyUrl.match(reg);\n    if (!matched) {\n      throw new TosClientError('the `policyUrl` param is invalid');\n    }\n    return {\n      origin: matched[1],\n      host: matched[2],\n      search: matched[3],\n    };\n  }\n\n  headObject = headObject;\n  getObjectV2 = getObjectV2;\n  listObjects = listObjects;\n  listObjectsType2 = listObjectsType2;\n  listObjectVersions = listObjectVersions;\n  downloadFile = downloadFile;\n}\n", "import TOSBase from '../base';\nimport { Acl, Headers, StorageClass } from '../../interface';\nimport {\n  fillRequestHeaders,\n  makeArrayProp,\n  normalizeHeadersKey,\n} from '../../utils';\nimport TosClientError from '../../TosClientError';\nimport { AzRedundancyType, StorageClassType } from '../../TosExportEnum';\nimport { TosHeader } from '../object/sharedTypes';\n\nexport interface Bucket {\n  // '2021-07-20T09:22:05.000Z'\n  CreationDate: string;\n  ExtranetEndpoint: string;\n  IntranetEndpoint: string;\n  Location: string;\n  Name: string;\n  Owner: { ID: string };\n  BucketType?: string;\n}\n\nexport interface ListBucketOutput {\n  Buckets: Bucket[];\n}\n\nexport interface PutBucketInput {\n  bucket?: string;\n  acl?: Acl;\n  grantFullControl?: string;\n  grantRead?: string;\n  grantReadAcp?: string;\n  grantWrite?: string;\n  grantWriteAcp?: string;\n  storageClass?: StorageClassType;\n  azRedundancy?: AzRedundancyType;\n  projectName?: string;\n  bucketType?: string;\n  headers?: {\n    [key: string]: string | undefined;\n    ['x-tos-acl']?: Acl;\n    ['x-tos-grant-full-control']?: string;\n    ['x-tos-grant-read']?: string;\n    ['x-tos-grant-read-acp']?: string;\n    ['x-tos-grant-write']?: string;\n    ['x-tos-grant-write-acp']?: string;\n    ['x-tos-storage-class']?: StorageClass;\n  };\n}\nexport interface ListBucketInput {\n  projectName?: string;\n}\nexport async function listBuckets(this: TOSBase, input: ListBucketInput = {}) {\n  const headers = {};\n  /**\n   * empty string is invalid value\n   */\n  input?.projectName &&\n    fillRequestHeaders({ ...input, headers }, ['projectName']);\n  const res = await this.fetch<ListBucketOutput>('GET', '/', {}, headers);\n  const arrayProp = makeArrayProp(res.data);\n  arrayProp('Buckets');\n\n  return res;\n}\n\nexport async function createBucket(this: TOSBase, input: PutBucketInput) {\n  const actualBucket = input.bucket || this.opts.bucket;\n  // these errors are only for creating bucket\n  if (actualBucket) {\n    if (actualBucket.length < 3 || actualBucket.length > 63) {\n      throw new TosClientError(\n        'invalid bucket name, the length must be [3, 63]'\n      );\n    }\n    if (!/^([a-z]|-|\\d)+$/.test(actualBucket)) {\n      throw new TosClientError(\n        'invalid bucket name, the character set is illegal'\n      );\n    }\n    if (/^-/.test(actualBucket) || /-$/.test(actualBucket)) {\n      throw new TosClientError(\n        `invalid bucket name, the bucket name can be neither starting with '-' nor ending with '-'`\n      );\n    }\n  }\n  const headers = (input.headers = normalizeHeadersKey(input.headers));\n\n  fillRequestHeaders(input, [\n    'acl',\n    'grantFullControl',\n    'grantRead',\n    'grantReadAcp',\n    'grantWrite',\n    'grantWriteAcp',\n    'storageClass',\n    'azRedundancy',\n    'bucketType',\n  ]);\n\n  /**\n   * empty string is invalid value\n   */\n  input?.projectName && fillRequestHeaders(input, ['projectName']);\n\n  const res = await this.fetchBucket(input.bucket, 'PUT', {}, headers);\n  return res;\n}\n\nexport async function deleteBucket(this: TOSBase, bucket?: string) {\n  return this.fetchBucket(bucket, 'DELETE', {}, {});\n}\n\nexport interface HeadBucketOutput {\n  ['x-tos-bucket-region']: string;\n  ['x-tos-storage-class']: StorageClass;\n  ['x-tos-bucket-type']?: string;\n  ProjectName?: string;\n}\n\nexport async function headBucket(this: TOSBase, bucket?: string) {\n  return this.fetchBucket<HeadBucketOutput>(bucket, 'HEAD', {}, {}, undefined, {\n    handleResponse: (res) => {\n      return {\n        ...res.headers,\n        ProjectName: res.headers[TosHeader.HeaderProjectName],\n      };\n    },\n  });\n}\n\nexport interface PutBucketStorageClassInput {\n  bucket: string;\n  storageClass: StorageClassType;\n}\n\nexport interface PutBucketStorageClassOutput {}\n\nexport async function putBucketStorageClass(\n  this: TOSBase,\n  input: PutBucketStorageClassInput\n) {\n  const { bucket, storageClass } = input;\n\n  return this.fetchBucket<PutBucketStorageClassOutput>(\n    bucket,\n    'PUT',\n    { storageClass: '' },\n    {\n      'x-tos-storage-class': storageClass,\n    }\n  );\n}\n", "import TOSBase from '../base';\nimport { Headers, AclInterface, Acl } from '../../interface';\nimport { makeArrayProp } from '../../utils';\n\nexport type GetBucketAclOutput = AclInterface;\n\nexport interface PutBucketAclInput {\n  bucket?: string;\n  acl?: Acl;\n  aclBody?: AclInterface;\n}\n\nexport async function putBucketAcl(this: TOSBase, input: PutBucketAclInput) {\n  const headers: Headers = {};\n  if (input.acl) headers['x-tos-acl'] = input.acl;\n\n  const res = await this.fetchBucket(\n    input.bucket,\n    'PUT',\n    { acl: '' },\n    headers,\n    input.aclBody,\n    { needMd5: true }\n  );\n  return res;\n}\n\nexport async function getBucketAcl(this: TOSBase, bucket?: string) {\n  const res = await this.fetchBucket<GetBucketAclOutput>(\n    bucket,\n    'GET',\n    {\n      acl: '',\n    },\n    {}\n  );\n  const arrayProp = makeArrayProp(res.data);\n  arrayProp('Grants');\n  return res;\n}\n", "import TOSBase, { TosResponse } from '../base';\nimport {\n  checkCRC64WithHeaders,\n  fillRequestHeaders,\n  makeRetryStreamAutoClose,\n  normalizeHeaders<PERSON>ey,\n  safeAwait,\n  tryDestroy,\n} from '../../utils';\nimport {\n  Acl,\n  DataTransferStatus,\n  DataTransferType,\n  SupportObjectBody,\n} from '../../interface';\nimport TosClientError from '../../TosClientError';\nimport * as fsp from '../../nodejs/fs-promises';\nimport { Stats, ReadStream } from 'fs';\nimport { getSize, getNewBodyConfig } from './utils';\nimport { retryNamespace } from '../../axios';\nimport { IRateLimiter } from '../../universal/rate-limiter';\nimport { StorageClassType } from '../../TosExportEnum';\n\nexport interface PutObjectInput {\n  bucket?: string;\n  key: string;\n  /**\n   * body is empty buffer if it's falsy.\n   */\n  body?: SupportObjectBody;\n\n  contentLength?: number;\n  contentMD5?: string;\n  contentSHA256?: string;\n  cacheControl?: string;\n  contentDisposition?: string;\n  contentEncoding?: string;\n  contentLanguage?: string;\n  contentType?: string;\n  expires?: Date;\n\n  acl?: Acl;\n  grantFullControl?: string;\n  grantRead?: string;\n  grantReadAcp?: string;\n  grantWrite?: string;\n  grantWriteAcp?: string;\n\n  ssecAlgorithm?: string;\n  ssecKey?: string;\n  ssecKeyMD5?: string;\n  serverSideEncryption?: string;\n  /**\n   * @private unstable\n   */\n  serverSideDataEncryption?: string;\n\n  meta?: Record<string, string>;\n  websiteRedirectLocation?: string;\n  storageClass?: StorageClassType;\n  ifMatch?: string;\n\n  dataTransferStatusChange?: (status: DataTransferStatus) => void;\n\n  /**\n   * the simple progress feature\n   * percent is [0, 1].\n   *\n   * since putObject is stateless, so if `putObject` fail and you retry it,\n   * `percent` will start from 0 again rather than from the previous value.\n   * if you need `percent` start from the previous value, you can use `uploadFile` instead.\n   */\n  progress?: (percent: number) => void;\n  /**\n   * unit: bit/s\n   * server side traffic limit\n   **/\n  trafficLimit?: number;\n  /**\n   * only works for nodejs environment\n   **/\n  rateLimiter?: IRateLimiter;\n\n  forbidOverwrite?: boolean;\n\n  callback?: string;\n  callbackVar?: string;\n\n  headers?: {\n    [key: string]: string | undefined;\n    'content-length'?: string;\n    'content-type'?: string;\n    'content-md5'?: string;\n    'cache-control'?: string;\n    expires?: string;\n    'x-tos-acl'?: Acl;\n    'x-tos-grant-full-control'?: string;\n    'x-tos-grant-read'?: string;\n    'x-tos-grant-read-acp'?: string;\n    'x-tos-grant-write-acp'?: string;\n    'x-tos-server-side-encryption-customer-algorithm'?: string;\n    'x-tos-server-side-encryption-customer-key'?: string;\n    'x-tos-server-side-encryption-customer-key-md5'?: string;\n    'x-tos-website-redirect-location'?: string;\n    'x-tos-storage-class'?: string;\n    'x-tos-server-side-encryption'?: string;\n    'x-tos-forbid-overwrite'?: string;\n    'If-Match'?: string;\n  };\n}\n\ninterface PutObjectInputInner extends PutObjectInput {\n  makeRetryStream?: () => NodeJS.ReadableStream | undefined;\n}\n\nexport interface PutObjectOutput {\n  'x-tos-server-side-encryption-customer-algorithm'?: string;\n  'x-tos-server-side-encryption-customer-key-md5'?: string;\n  'x-tos-version-id'?: string;\n  'x-tos-hash-crc64ecma'?: string;\n  'x-tos-server-side-encryption'?: string;\n  CallbackResult?: string;\n}\n\nexport async function putObject(this: TOSBase, input: PutObjectInput | string) {\n  return _putObject.call(this, input);\n}\n\nexport async function _putObject(\n  this: TOSBase,\n  input: PutObjectInputInner | string\n): Promise<TosResponse<PutObjectOutput>> {\n  input = this.normalizeObjectInput(input);\n  const headers = (input.headers = normalizeHeadersKey(input.headers));\n  fillRequestHeaders(input, [\n    'contentLength',\n    'contentMD5',\n    'contentSHA256',\n    'cacheControl',\n    'contentDisposition',\n    'contentEncoding',\n    'contentLanguage',\n    'contentType',\n    'expires',\n    'acl',\n    'grantFullControl',\n    'grantRead',\n    'grantReadAcp',\n    'grantWrite',\n    'grantWriteAcp',\n    'ssecAlgorithm',\n    'ssecKey',\n    'ssecKeyMD5',\n    'serverSideEncryption',\n    'serverSideDataEncryption',\n    'meta',\n    'websiteRedirectLocation',\n    'storageClass',\n    'trafficLimit',\n    'callback',\n    'callbackVar',\n    'forbidOverwrite',\n    'ifMatch',\n  ]);\n  this.setObjectContentTypeHeader(input, headers);\n\n  const totalSize = getSize(input.body, headers);\n  const totalSizeValid = totalSize != null;\n\n  if (!totalSizeValid && (input.dataTransferStatusChange || input.progress)) {\n    console.warn(\n      `Don't get totalSize of putObject's body, the \\`dataTransferStatusChange\\` and \\`progress\\` callback will not trigger. You can use \\`putObjectFromFile\\` instead`\n    );\n  }\n\n  let consumedBytes = 0;\n  const { dataTransferStatusChange, progress } = input;\n  const triggerDataTransfer = (\n    type: DataTransferType,\n    rwOnceBytes: number = 0\n  ) => {\n    // request cancel will make rwOnceBytes < 0 in browser\n    if (!totalSizeValid || rwOnceBytes < 0) {\n      return;\n    }\n    if (!dataTransferStatusChange && !progress) {\n      return;\n    }\n    consumedBytes += rwOnceBytes;\n\n    dataTransferStatusChange?.({\n      type,\n      rwOnceBytes,\n      consumedBytes,\n      totalBytes: totalSize,\n    });\n    const progressValue = (() => {\n      if (totalSize === 0) {\n        if (type === DataTransferType.Succeed) {\n          return 1;\n        }\n        return 0;\n      }\n      return consumedBytes / totalSize;\n    })();\n    if (progressValue === 1) {\n      if (type === DataTransferType.Succeed) {\n        progress?.(progressValue);\n      } else {\n        // not exec progress\n      }\n    } else {\n      progress?.(progressValue);\n    }\n  };\n\n  const bodyConfig = await getNewBodyConfig({\n    body: input.body,\n    dataTransferCallback: (n) => triggerDataTransfer(DataTransferType.Rw, n),\n    makeRetryStream: input.makeRetryStream,\n    enableCRC: this.opts.enableCRC,\n    rateLimiter: input.rateLimiter,\n  });\n\n  triggerDataTransfer(DataTransferType.Started);\n\n  const task = async () => {\n    const res = await this._fetchObject<PutObjectOutput>(\n      input,\n      'PUT',\n      {},\n      headers,\n      bodyConfig.body || '',\n      {\n        handleResponse: (res) => {\n          const result = { ...res.headers };\n          if ((input as PutObjectInputInner)?.callback && res.data) {\n            result.CallbackResult = `${JSON.stringify(res.data)}`;\n          }\n          return result;\n        },\n        axiosOpts: {\n          [retryNamespace]: {\n            beforeRetry: () => {\n              consumedBytes = 0;\n              bodyConfig.beforeRetry?.();\n            },\n            makeRetryStream: bodyConfig.makeRetryStream,\n          },\n          onUploadProgress: (event) => {\n            triggerDataTransfer(\n              DataTransferType.Rw,\n              event.loaded - consumedBytes\n            );\n          },\n        },\n      }\n    );\n    if (this.opts.enableCRC && bodyConfig.crc) {\n      checkCRC64WithHeaders(bodyConfig.crc, res.headers);\n    }\n    return res;\n  };\n  const [err, res] = await safeAwait(task());\n\n  if (err || !res) {\n    triggerDataTransfer(DataTransferType.Failed);\n    throw err;\n  }\n\n  triggerDataTransfer(DataTransferType.Succeed);\n  return res;\n}\n\ninterface PutObjectFromFileInput extends Omit<PutObjectInput, 'body'> {\n  filePath: string;\n}\n\nexport async function putObjectFromFile(\n  this: TOSBase,\n  input: PutObjectFromFileInput\n): Promise<TosResponse<PutObjectOutput>> {\n  const normalizedHeaders = normalizeHeadersKey(input.headers);\n  if (process.env.TARGET_ENVIRONMENT !== 'node') {\n    throw new TosClientError(\n      \"putObjectFromFile doesn't support in browser environment\"\n    );\n  }\n\n  if (!normalizedHeaders['content-length']) {\n    const stats: Stats = await fsp.stat(input.filePath);\n    normalizedHeaders['content-length'] = `${stats.size}`;\n  }\n\n  const makeRetryStream = makeRetryStreamAutoClose(() =>\n    fsp.createReadStream(input.filePath)\n  );\n\n  try {\n    return await _putObject.call(this, {\n      ...input,\n      body: makeRetryStream.make(),\n      headers: normalizedHeaders,\n      makeRetryStream: makeRetryStream.make,\n    });\n  } catch (err) {\n    tryDestroy(makeRetryStream.getLastStream(), err);\n    throw err;\n  }\n}\n\nexport default putObject;\n", "import { StorageClassType } from '../../TosExportEnum';\nimport { Acl } from '../../interface';\nimport { fillRequestHeaders, normalizeHeadersKey } from '../../utils';\nimport TOSBase from '../base';\n\nexport interface FetchObjectInput {\n  bucket?: string;\n  key: string;\n  url: string;\n  ignoreSameKey?: boolean;\n\n  acl?: Acl;\n  grantFullControl?: string;\n  grantRead?: string;\n  grantReadAcp?: string;\n  grantWriteAcp?: string;\n  storageClass?: StorageClassType;\n\n  ssecAlgorithm?: string;\n  ssecKey?: string;\n  ssecKeyMD5?: string;\n  meta?: Record<string, string>;\n\n  // contentMD5 is the base64 encoded of object's md5\n  contentMD5?: string;\n\n  headers?: {\n    [key: string]: string | undefined;\n  };\n}\n\nexport interface FetchObjectOutput {\n  VersionID?: string;\n  Etag: string;\n  SSECAlgorithm?: string;\n  SSECKeyMD5?: string;\n}\n\nexport async function fetchObject(this: TOSBase, input: FetchObjectInput) {\n  const headers = (input.headers = normalizeHeadersKey(input.headers));\n  fillRequestHeaders(input, [\n    'acl',\n    'grantFullControl',\n    'grantRead',\n    'grantReadAcp',\n    'grantWriteAcp',\n    'ssecAlgorithm',\n    'ssecKey',\n    'ssecKeyMD5',\n    'meta',\n    'storageClass',\n  ]);\n  const res = await this._fetchObject<FetchObjectOutput>(\n    input,\n    'POST',\n    {\n      fetch: '',\n    },\n    headers,\n    {\n      URL: input.url,\n      IgnoreSameKey: input.ignoreSameKey,\n      ContentMD5: input.contentMD5,\n    },\n    {\n      needMd5: true,\n    }\n  );\n  return res;\n}\n\nexport interface PutFetchTaskInput {\n  bucket?: string;\n  key: string;\n  url: string;\n  ignoreSameKey?: boolean;\n\n  acl?: Acl;\n  grantFullControl?: string;\n  grantRead?: string;\n  grantReadAcp?: string;\n  grantWriteAcp?: string;\n  storageClass?: StorageClassType;\n\n  ssecAlgorithm?: string;\n  ssecKey?: string;\n  ssecKeyMD5?: string;\n  meta?: Record<string, string>;\n\n  // contentMD5 is the base64 encoded of object's md5\n  contentMD5?: string;\n\n  headers?: {\n    [key: string]: string | undefined;\n  };\n}\n\nexport interface PutFetchTaskOutput {\n  TaskId: string;\n}\n\nexport async function putFetchTask(this: TOSBase, input: PutFetchTaskInput) {\n  const headers = (input.headers = normalizeHeadersKey(input.headers));\n  fillRequestHeaders(input, [\n    'acl',\n    'grantFullControl',\n    'grantRead',\n    'grantReadAcp',\n    'grantWriteAcp',\n    'ssecAlgorithm',\n    'ssecKey',\n    'ssecKeyMD5',\n    'meta',\n    'storageClass',\n  ]);\n\n  const res = await this._fetchObject<PutFetchTaskOutput>(\n    input,\n    'POST',\n    {\n      fetchTask: '',\n    },\n    headers,\n    {\n      URL: input.url,\n      IgnoreSameKey: input.ignoreSameKey,\n      ContentMD5: input.contentMD5,\n      Object: input.key,\n    },\n    {\n      needMd5: true,\n    }\n  );\n  return res;\n}\n", "import TosClientError from '../../TosClientError';\nimport { covertCamelCase2Kebab, normalizeProxy } from '../../utils';\nimport TOSBase from '../base';\nimport { validateObjectName } from './utils';\n\nexport interface GetPreSignedUrlInput {\n  bucket?: string;\n  key: string;\n  /**\n   * default: 'GET'\n   */\n  method?: 'GET' | 'PUT';\n  /**\n   * unit: second, default: 1800\n   */\n  expires?: number;\n  alternativeEndpoint?: string;\n  response?: {\n    contentType?: string;\n    contentDisposition?: string;\n  };\n  versionId?: string;\n  query?: Record<string, string>;\n  /**\n   * default: false\n   * if set true. generate domain will direct use `endpoint` or `alternativeEndpoint`.\n   */\n  isCustomDomain?: boolean;\n}\n\nexport function getPreSignedUrl(\n  this: TOSBase,\n  input: GetPreSignedUrlInput | string\n) {\n  validateObjectName(input);\n  const normalizedInput = typeof input === 'string' ? { key: input } : input;\n  const endpoint = normalizedInput.alternativeEndpoint || this.opts.endpoint;\n  const subdomain =\n    normalizedInput.alternativeEndpoint || normalizedInput.isCustomDomain\n      ? false\n      : true;\n  const bucket = normalizedInput.bucket || this.opts.bucket || '';\n  if (subdomain && !bucket) {\n    throw new TosClientError('Must provide bucket param');\n  }\n\n  const [newHost, newPath, signingPath] = (() => {\n    const encodedKey = encodeURIComponent(normalizedInput.key);\n    const objectKeyPath = normalizedInput.key\n      .split('/')\n      .map((it) => encodeURIComponent(it))\n      .join('/');\n\n    if (subdomain) {\n      return [`${bucket}.${endpoint}`, `/${objectKeyPath}`, `/${encodedKey}`];\n    }\n    return [endpoint, `/${objectKeyPath}`, `/${encodedKey}`];\n  })();\n\n  const nextQuery: Record<string, any> = normalizedInput.query || {};\n  const setOneQuery = (k: string, v?: string) => {\n    if (nextQuery[k] == null && v != null) {\n      nextQuery[k] = v;\n    }\n  };\n  const response = normalizedInput.response || {};\n  Object.keys(response).forEach((_key) => {\n    const key = _key as keyof typeof response;\n    const kebabKey = covertCamelCase2Kebab(key);\n    setOneQuery(`response-${kebabKey}`, response[key]);\n  });\n  if (normalizedInput.versionId) {\n    setOneQuery('versionId', normalizedInput.versionId);\n  }\n\n  const query = this.getSignatureQuery({\n    bucket,\n    method: normalizedInput.method || 'GET',\n    path: signingPath,\n    endpoint,\n    subdomain,\n    expires: normalizedInput.expires || 1800,\n    query: nextQuery,\n  });\n\n  const normalizedProxy = normalizeProxy(this.opts.proxy);\n  let baseURL = `http${this.opts.secure ? 's' : ''}://${newHost}`;\n  if (normalizedProxy?.url) {\n    // if `baseURL` ends with '/'，we filter it.\n    // because `newPath` starts with '/'\n    baseURL = normalizedProxy.url.replace(/\\/+$/g, '');\n    if (normalizedProxy?.needProxyParams) {\n      query['x-proxy-tos-host'] = newHost;\n    }\n  }\n\n  const queryStr = Object.keys(query)\n    .map((key) => {\n      return `${encodeURIComponent(key)}=${encodeURIComponent(query[key])}`;\n    })\n    .join('&');\n\n  return `${baseURL}${newPath}?${queryStr}`;\n}\n\nexport default getPreSignedUrl;\n", "import TOSBase from '../base';\n\nexport interface DeleteObjectInput {\n  bucket?: string;\n  key: string;\n  versionId?: string;\n  /**@private unstable */\n  skipTrash?: string;\n  /**@private unstable */\n  recursive?: string;\n}\n\nexport interface DeleteObjectOutput {\n  [key: string]: string | undefined;\n  ['x-tos-delete-marker']: string;\n  ['x-tos-version-id']: string;\n}\n\nexport async function deleteObject(\n  this: TOSBase,\n  input: DeleteObjectInput | string\n) {\n  const normalizedInput = typeof input === 'string' ? { key: input } : input;\n  const query: Record<string, any> = {};\n  if (normalizedInput.versionId) {\n    query.versionId = normalizedInput.versionId;\n  }\n  if (normalizedInput.skipTrash) {\n    query.skipTrash = normalizedInput.skipTrash;\n  }\n  if (normalizedInput.recursive) {\n    query.recursive = normalizedInput.recursive;\n  }\n  const res = await this._fetchObject<DeleteObjectOutput>(\n    input,\n    'DELETE',\n    query,\n    {},\n    {},\n    { handleResponse: (res) => res.headers }\n  );\n  return res;\n}\n\nexport default deleteObject;\n", "import { fillRequestHeaders, normalizeHeadersKey } from '../../utils';\nimport TOSBase from '../base';\n\nexport interface RenameObjectInput {\n  bucket?: string;\n  key: string;\n  newKey: string;\n  recursiveMkdir?: boolean;\n  forbidOverwrite?: boolean;\n  headers?: {\n    [key: string]: string | undefined;\n  };\n}\n\nexport async function renameObject(this: TOSBase, input: RenameObjectInput) {\n  input.headers = input.headers || {};\n  fillRequestHeaders(input, ['recursiveMkdir', 'forbidOverwrite']);\n  return this._fetchObject<undefined>(\n    input,\n    'PUT',\n    { rename: '', name: input.newKey },\n    input.headers,\n    ''\n  );\n}\n\nexport default renameObject;\n", "import { makeArrayProp } from '../../utils';\nimport TOSBase from '../base';\n\nexport interface DeleteMultiObjectsInput {\n  bucket?: string;\n  /**\n   * default: false\n   */\n  quiet?: boolean;\n  objects: {\n    key: string;\n    versionId?: string;\n  }[];\n  /**@private unstable */\n  skipTrash?: string;\n  /**@private unstable */\n  recursive?: string;\n}\n\nexport interface DeleteMultiObjectsOutput {\n  Deleted: {\n    Key: string;\n    VersionId: string;\n    DeleteMarker?: boolean;\n    DeleteMarkerVersionId?: string;\n  }[];\n\n  Error: {\n    Code: string;\n    Message: string;\n    Key: string;\n    VersionId: string;\n  }[];\n}\n\nexport async function deleteMultiObjects(\n  this: TOSBase,\n  input: DeleteMultiObjectsInput\n) {\n  const body = {\n    Quiet: input.quiet,\n    Objects: input.objects.map((it) => ({\n      Key: it.key,\n      VersionId: it.versionId,\n    })),\n  };\n\n  const query: Record<string, string> = {\n    delete: '',\n  };\n\n  if (input.skipTrash) {\n    query.skipTrash = input.skipTrash;\n  }\n\n  if (input.recursive) {\n    query.recursive = input.recursive;\n  }\n\n  const res = await this.fetchBucket<DeleteMultiObjectsOutput>(\n    input.bucket,\n    'POST',\n    query,\n    {},\n    body\n  );\n\n  const arrayProp = makeArrayProp(res.data);\n  arrayProp('Deleted');\n  arrayProp('Error');\n\n  return res;\n}\n\nexport default deleteMultiObjects;\n", "import { Acl, AclInterface } from '../../../interface';\nimport { fillRequestHeaders, makeArrayProp, normalizeHeadersKey } from '../../../utils';\nimport TOSBase from '../../base';\n\nexport interface GetObjectAclInput {\n  bucket?: string;\n  key: string;\n  versionId?: string;\n}\n\nexport type ObjectAclBody = AclInterface & {\n  BucketOwnerEntrusted?: boolean;\n  IsDefault?: boolean;\n};\n\nexport type GetObjectAclOutput = ObjectAclBody;\n\nexport async function getObjectAcl(\n  this: TOSBase,\n  input: GetObjectAclInput | string\n) {\n  const normalizedInput = typeof input === 'string' ? { key: input } : input;\n  const query: Record<string, any> = { acl: '' };\n  if (normalizedInput.versionId) {\n    query.versionId = normalizedInput.versionId;\n  }\n\n  const res = await this._fetchObject<GetObjectAclOutput>(input, 'GET', query, {});\n\n  const arrayProp = makeArrayProp(res.data);\n  arrayProp('Grants');\n\n  return res;\n}\n\nexport interface PutObjectAclInput {\n  bucket?: string;\n  key: string;\n  versionId?: string;\n  acl?: Acl;\n  aclBody?: ObjectAclBody;\n  headers?: {\n    [key: string]: string | undefined;\n    'x-tos-acl'?: Acl;\n  };\n}\n\nexport async function putObjectAcl(this: TOSBase, input: PutObjectAclInput) {\n  const headers = (input.headers = normalizeHeadersKey(input.headers));\n  const query: Record<string, any> = { acl: '' };\n  if (input.versionId) {\n    query.versionId = input.versionId;\n  }\n  fillRequestHeaders(input, ['acl']);\n\n  return this._fetchObject<undefined>(\n    input,\n    'PUT',\n    query,\n    headers,\n    input.aclBody\n  );\n}\n", "import TOSBase from '../../base';\n\nexport interface AbortMultipartUploadInput {\n  bucket?: string;\n  key: string;\n  uploadId: string;\n}\n\nexport async function abortMultipartUpload(\n  this: TOSBase,\n  input: AbortMultipartUploadInput\n) {\n  return this._fetchObject<undefined>(\n    input,\n    'DELETE',\n    {\n      uploadId: input.uploadId,\n    },\n    {}\n  );\n}\n", "import { covertCamelCase2Kebab, makeArrayProp } from '../../../utils';\nimport TOSBase from '../../base';\n\nexport interface ListMultipartUploadsInput {\n  bucket?: string;\n  maxUploads?: number;\n  keyMarker?: string;\n  uploadIdMarker?: string;\n  delimiter?: string;\n  encodingType?: string;\n  prefix?: string;\n}\n\nexport interface ListMultipartUploadsOutput {\n  Uploads: {\n    Key: string;\n    UploadId: string;\n    StorageClass: string;\n    Initiated: string;\n  }[];\n  CommonPrefixes: string[];\n  Delimiter?: string;\n  EncodingType?: string;\n  KeyMarker?: string;\n  NextKeyMarker: string;\n  MaxUploads?: string;\n  UploadIdMarker?: string;\n  NextUploadIdMarker: string;\n  Prefix?: string;\n  IsTruncated: boolean;\n  Bucket: string;\n}\n\nexport async function listMultipartUploads(\n  this: TOSBase,\n  input: ListMultipartUploadsInput = {}\n) {\n  const { bucket, ...nextQuery } = input;\n  const ret = await this.fetchBucket<ListMultipartUploadsOutput>(\n    input.bucket,\n    'GET',\n    {\n      uploads: '',\n      ...covertCamelCase2Kebab(nextQuery),\n    },\n    {}\n  );\n\n  const arrayProp = makeArrayProp(ret.data);\n  arrayProp('Uploads');\n  arrayProp('CommonPrefixes');\n\n  return ret;\n}\n", "import TOSBase from '../base';\nimport {\n  checkCRC64WithHeaders,\n  fillRequestHeaders,\n  normalizeHead<PERSON><PERSON>ey,\n  safeAwait,\n} from '../../utils';\nimport { Acl, DataTransferStatus, DataTransferType } from '../../interface';\nimport { IRateLimiter } from '../../universal/rate-limiter';\nimport { getNewBodyConfig, getSize } from './utils';\nimport { StorageClassType } from '../../TosExportEnum';\nimport { retryNamespace } from '../../axios';\nimport TosClientError from '../../TosClientError';\nimport { combineCrc64 } from '../../universal/crc';\n\nexport interface AppendObjectInput {\n  bucket?: string;\n  key: string;\n  offset: number;\n  // body is empty buffer if it's falsy\n  body?: File | Blob | Buffer | NodeJS.ReadableStream;\n\n  // must provide preHashCrc64ecma if enableCRC is true and offset is non-zero\n  preHashCrc64ecma?: string;\n\n  /**\n   * unit: bit/s\n   * server side traffic limit\n   **/\n  trafficLimit?: number;\n  /**\n   * only works for nodejs environment\n   */\n  rateLimiter?: IRateLimiter;\n\n  contentLength?: number;\n  cacheControl?: string;\n  contentDisposition?: string;\n  contentEncoding?: string;\n  contentLanguage?: string;\n  contentType?: string;\n  expires?: Date;\n\n  acl?: Acl;\n  grantFullControl?: string;\n  grantRead?: string;\n  grantReadAcp?: string;\n  grantWriteAcp?: string;\n\n  meta?: Record<string, string>;\n  websiteRedirectLocation?: string;\n  storageClass?: StorageClassType;\n\n  dataTransferStatusChange?: (status: DataTransferStatus) => void;\n\n  /**\n   * the simple progress feature\n   * percent is [0, 1].\n   *\n   * since appendObject is stateless, so if `appendObject` fail and you retry it,\n   * `percent` will start from 0 again rather than from the previous value.\n   * if you need `percent` start from the previous value, you can use `uploadFile` instead.\n   */\n  progress?: (percent: number) => void;\n\n  headers?: {\n    [key: string]: string | undefined;\n    'Cache-Control'?: string;\n    'x-tos-acl'?: Acl;\n    'x-tos-grant-full-control'?: string;\n    'x-tos-grant-read'?: string;\n    'x-tos-grant-read-acp'?: string;\n    'x-tos-grant-write-acp'?: string;\n    'x-tos-website-redirect-location'?: string;\n    'x-tos-storage-class'?: string;\n  };\n}\n\nexport interface AppendObjectOutput {\n  nextAppendOffset: number;\n  hashCrc64ecma: string;\n  'x-tos-version-id'?: string;\n  'x-tos-hash-crc64ecma'?: string;\n  'x-tos-next-append-offset'?: string;\n}\n\nexport async function appendObject(\n  this: TOSBase,\n  input: AppendObjectInput | string\n) {\n  const normalizedInput = (input = this.normalizeObjectInput(input));\n  const headers = (input.headers = normalizeHeadersKey(input.headers));\n  fillRequestHeaders(input, [\n    'contentLength',\n    'cacheControl',\n    'contentDisposition',\n    'contentEncoding',\n    'contentLanguage',\n    'contentType',\n    'expires',\n    'acl',\n    'grantFullControl',\n    'grantRead',\n    'grantReadAcp',\n    'grantWriteAcp',\n    'meta',\n    'websiteRedirectLocation',\n    'storageClass',\n    'trafficLimit',\n  ]);\n  this.setObjectContentTypeHeader(input, headers);\n\n  const totalSize = getSize(input.body, headers);\n  const totalSizeValid = totalSize != null;\n  if (!totalSizeValid) {\n    throw new TosClientError(\n      `appendObject needs to know the content length in advance`\n    );\n  }\n  headers['content-length'] = headers['content-length'] || `${totalSize}`;\n\n  if (this.opts.enableCRC && input.offset !== 0 && !input.preHashCrc64ecma) {\n    throw new TosClientError(\n      'must provide preHashCrc64ecma if enableCRC is true and offset is non-zero'\n    );\n  }\n\n  let consumedBytes = 0;\n  const { dataTransferStatusChange, progress } = input;\n  const triggerDataTransfer = (\n    type: DataTransferType,\n    rwOnceBytes: number = 0\n  ) => {\n    // request cancel will make rwOnceBytes < 0 in browser\n    if (!totalSizeValid || rwOnceBytes < 0) {\n      return;\n    }\n    if (!dataTransferStatusChange && !progress) {\n      return;\n    }\n    consumedBytes += rwOnceBytes;\n\n    dataTransferStatusChange?.({\n      type,\n      rwOnceBytes,\n      consumedBytes,\n      totalBytes: totalSize,\n    });\n    const progressValue = (() => {\n      if (totalSize === 0) {\n        if (type === DataTransferType.Succeed) {\n          return 1;\n        }\n        return 0;\n      }\n      return consumedBytes / totalSize;\n    })();\n    if (progressValue === 1) {\n      if (type === DataTransferType.Succeed) {\n        progress?.(progressValue);\n      } else {\n        // not exec progress\n      }\n    } else {\n      progress?.(progressValue);\n    }\n  };\n\n  const bodyConfig = await getNewBodyConfig({\n    body: input.body,\n    dataTransferCallback: (n) => triggerDataTransfer(DataTransferType.Rw, n),\n    makeRetryStream: undefined,\n    enableCRC: this.opts.enableCRC,\n    rateLimiter: input.rateLimiter,\n  });\n\n  triggerDataTransfer(DataTransferType.Started);\n  const task = async () => {\n    const res = await this._fetchObject<AppendObjectOutput>(\n      input,\n      'POST',\n      { append: '', offset: normalizedInput.offset },\n      headers,\n      bodyConfig.body || '',\n      {\n        handleResponse: (res) => ({\n          ...res.headers,\n          nextAppendOffset: +res.headers['x-tos-next-append-offset'],\n          hashCrc64ecma: res.headers['x-tos-hash-crc64ecma'],\n        }),\n        axiosOpts: {\n          [retryNamespace]: {\n            beforeRetry: () => {\n              consumedBytes = 0;\n              bodyConfig.beforeRetry?.();\n            },\n            makeRetryStream: bodyConfig.makeRetryStream,\n          },\n          onUploadProgress: (event) => {\n            triggerDataTransfer(\n              DataTransferType.Rw,\n              event.loaded - consumedBytes\n            );\n          },\n        },\n      }\n    );\n    if (this.opts.enableCRC && bodyConfig.crc) {\n      const appendObjectCrc = combineCrc64(\n        normalizedInput.preHashCrc64ecma || '0',\n        bodyConfig.crc.getCrc64(),\n        totalSize\n      );\n      checkCRC64WithHeaders(appendObjectCrc, res.headers);\n    }\n    return res;\n  };\n  const [err, res] = await safeAwait(task());\n\n  if (err || !res) {\n    triggerDataTransfer(DataTransferType.Failed);\n    throw err;\n  }\n\n  triggerDataTransfer(DataTransferType.Succeed);\n  return res;\n}\n\nexport default appendObject;\n", "import { fillRequestHeaders, normalizeHeadersKey } from '../../utils';\nimport TOSBase from '../base';\n\nexport interface SetObjectMetaInput {\n  bucket?: string;\n  key: string;\n  versionId?: string;\n\n  // object meta data\n  cacheControl?: string;\n  contentDisposition?: string;\n  contentEncoding?: string;\n  contentLanguage?: string;\n  contentType?: string;\n  expires?: Date;\n  meta?: Record<string, string>;\n\n  headers?: {\n    [key: string]: string | undefined;\n    'Cache-Control'?: string;\n    'Content-Disposition'?: string;\n    Expires?: string;\n    'Content-Type'?: string;\n    'Content-Language'?: string;\n  };\n}\n\nexport async function setObjectMeta(\n  this: TOSBase,\n  input: SetObjectMetaInput | string\n) {\n  const normalizedInput = typeof input === 'string' ? { key: input } : input;\n  const headers = (normalizedInput.headers = normalizeHeadersKey(\n    normalizedInput.headers\n  ));\n  fillRequestHeaders(normalizedInput, [\n    'cacheControl',\n    'contentDisposition',\n    'contentEncoding',\n    'contentLanguage',\n    'contentType',\n    'expires',\n    'meta',\n  ]);\n  const query: Record<string, any> = { metadata: '' };\n  if (normalizedInput.versionId) {\n    query.versionId = normalizedInput.versionId;\n  }\n\n  return this._fetchObject<undefined>(input, 'POST', query, headers);\n}\n\nexport default setObjectMeta;\n", "import TOSBase from '../base';\nimport { parse, stringify, hmacSha256 } from '../../universal/crypto';\nimport TosClientError from '../../TosClientError';\nimport { validateObjectName } from './utils';\n\nexport type PostSignatureCondition =\n  | {\n      [key: string]: string;\n    }\n  | ['eq', string, string]\n  | ['starts-with', string, string]\n  | ['content-length-range', number, number];\n\nexport interface CalculatePostSignatureInput {\n  bucket?: string;\n  key: string;\n  // unit: seconds, default: 3600(1 hour)\n  expiresIn?: number;\n  fields?: Record<string, unknown>;\n  conditions?: PostSignatureCondition[];\n}\n\nexport async function calculatePostSignature(\n  this: TOSBase,\n  input: CalculatePostSignatureInput | string\n) {\n  validateObjectName(input);\n  input = this.normalizeObjectInput(input);\n  const { expiresIn = 3600, key } = input;\n  const bucket = input.bucket || this.opts.bucket;\n  const fields = { ...input.fields };\n  const conditions = [...(input.conditions || [])];\n\n  if (!bucket) {\n    throw new TosClientError('Must provide bucket param');\n  }\n\n  const accessKeySecret = this.opts.accessKeySecret;\n  const date = new Date();\n  const expirationDateStr = getDateTimeStr({\n    date: new Date(date.valueOf() + expiresIn * 1000),\n    type: 'ISO',\n  });\n  const dateStr = getDateTimeStr();\n  const date8Str = dateStr.substring(0, 8);\n  const service = 'tos';\n  const requestStr = 'request';\n\n  const kDate = hmacSha256(accessKeySecret, date8Str);\n  const kRegion = hmacSha256(kDate, this.opts.region);\n  const kService = hmacSha256(kRegion, service);\n  const signingKey = hmacSha256(kService, requestStr);\n\n  const credential = [\n    this.opts.accessKeyId,\n    date8Str,\n    this.opts.region,\n    service,\n    requestStr,\n  ].join('/');\n\n  const addedInForm: Record<string, string> = {\n    key,\n    'x-tos-algorithm': 'TOS4-HMAC-SHA256',\n    'x-tos-date': dateStr,\n    'x-tos-credential': credential,\n  };\n  if (this.opts.stsToken) {\n    addedInForm['x-tos-security-token'] = this.opts.stsToken;\n  }\n\n  conditions.push({ bucket });\n  Object.entries(addedInForm).forEach(([key, value]) => {\n    fields[key] = value;\n  });\n  Object.entries(fields).forEach(([key, value]) => {\n    conditions.push({ [key]: `${value}` });\n  });\n\n  const policy = {\n    expiration: expirationDateStr,\n    conditions,\n  };\n  const policyStr = JSON.stringify(policy);\n  const policyBase64 = stringify(parse(policyStr, 'utf-8'), 'base64');\n  const signature = hmacSha256(signingKey, policyBase64, 'hex');\n\n  fields.policy = policyBase64;\n  fields['x-tos-signature'] = signature;\n\n  return fields;\n}\n\n/**\n *\n * Z for 20130728T000000Z\n * ISO for 2007-12-01T12:00:00.000Z\n * @param opt\n * @returns\n */\nfunction getDateTimeStr(opt?: { date?: Date; type?: 'Z' | 'ISO' }) {\n  const { date = new Date(), type = 'Z' } = opt || {};\n  if (type === 'ISO') {\n    return date.toISOString();\n  }\n\n  const dateTime =\n    date.toISOString().replace(/\\..+/, '').replace(/-/g, '').replace(/:/g, '') +\n    'Z';\n\n  return dateTime;\n}\n\nexport default calculatePostSignature;\n", "import TosServerError from './TosServerError';\nimport { getNormalDataFromError } from './utils';\n\nconst defaultEmptyMethodMap: Record<string, boolean> = {\n  getBucketCustomDomain: true,\n  getBucketIntelligenttiering: true,\n  getBucketInventory: true,\n  listBucketInventory: true,\n  getBucketMirrorBack: true,\n  getBucketNotification: true,\n  getBucketPolicy: true,\n  getBucketRealTimeLog: true,\n  getBucketReplication: true,\n  getBucketTagging: true,\n  getBucketWebsite: true,\n};\n\nexport function handleEmptyServerError<T>(\n  err: Error | TosServerError | unknown,\n  opts: {\n    defaultResponse: T;\n    enableCatchEmptyServerError?: boolean;\n    methodKey: string;\n  }\n) {\n  const { enableCatchEmptyServerError, methodKey, defaultResponse } = opts;\n  if (err instanceof TosServerError) {\n    if (enableCatchEmptyServerError) {\n      if (err.statusCode === 404) {\n        return getNormalDataFromError(defaultResponse, err);\n      }\n    }\n    // 在本次更改前已经有一些接口对404做了catch处理，在不显式声明enableCatchEmptyServerError的情况下，保持原样，不做break change\n    else if (enableCatchEmptyServerError === undefined) {\n      if (err.statusCode === 404 && defaultEmptyMethodMap[methodKey]) {\n        return getNormalDataFromError(defaultResponse, err);\n      }\n    }\n  }\n  throw err;\n}\n", "import { makeArrayProp } from '../../utils';\nimport { handleEmptyServerError } from '../../handleEmptyServerError';\nimport TOSBase, { TosResponse } from '../base';\n\nexport interface BucketPolicyStatement {\n  Sid: string;\n  Effect: 'Allow' | 'Deny';\n  Action?: string | string[];\n  NotAction?: string | string[];\n  Condition?: {\n    [key in string]: {\n      [key in string]: string[];\n    };\n  };\n  Principal?: string[];\n  NotPrincipal?: string[];\n  Resource?: string | string[];\n  NotResource?: string | string[];\n}\n\nexport interface GetBucketPolicyOutput {\n  Statement: BucketPolicyStatement[];\n  Version: string;\n}\n\ninterface PutBucketPolicyInputPolicy\n  extends Omit<GetBucketPolicyOutput, 'Version'> {\n  Version?: string;\n}\n\nexport interface PutBucketPolicyInput {\n  bucket?: string;\n  policy: PutBucketPolicyInputPolicy;\n}\n\nexport async function putBucketPolicy(\n  this: TOSBase,\n  input: PutBucketPolicyInput\n) {\n  if (\n    (this.opts.enableOptimizeMethodBehavior ||\n      this.opts.enableOptimizeMethodBehavior === undefined) &&\n    !input.policy.Statement.length\n  ) {\n    return deleteBucketPolicy.call(this, input.bucket);\n  }\n\n  const res = await this.fetchBucket(\n    input.bucket,\n    'PUT',\n    { policy: '' },\n    {},\n    input.policy,\n    { needMd5: true }\n  );\n  return res;\n}\n\nexport async function getBucketPolicy(\n  this: TOSBase,\n  bucket?: string\n): Promise<TosResponse<GetBucketPolicyOutput>> {\n  try {\n    const res = await this.fetchBucket<GetBucketPolicyOutput>(\n      bucket,\n      'GET',\n      {\n        policy: '',\n      },\n      {}\n    );\n    res.data.Statement.forEach((it: any) => {\n      const arrayProp = makeArrayProp(it);\n\n      Object.keys(it.Condition || {}).forEach((key) => {\n        Object.keys(it.Condition[key]).forEach((key2) => {\n          arrayProp(`Condition[\"${key}\"][\"${key2}\"]`);\n        });\n      });\n    });\n    return res;\n  } catch (error) {\n    return handleEmptyServerError<GetBucketPolicyOutput>(error, {\n      enableCatchEmptyServerError: this.opts.enableOptimizeMethodBehavior,\n      methodKey: 'getBucketPolicy',\n      defaultResponse: {\n        Statement: [],\n        Version: '2012-10-17',\n      },\n    });\n  }\n}\n\nexport async function deleteBucketPolicy(this: TOSBase, bucket?: string) {\n  return this.fetchBucket(bucket, 'DELETE', { policy: '' }, {});\n}\n", "import { VersioningStatusType } from '../../TosExportEnum';\nimport TOSBase from '../base';\n\n// for backward compatibility\nexport { VersioningStatusType as BucketVersioningStatus };\n\nexport type PutBucketVersioningInputStatus =\n  | VersioningStatusType.Enable\n  | VersioningStatusType.Enabled\n  | VersioningStatusType.Suspended;\n\nexport interface GetBucketVersioningOutput {\n  Status: VersioningStatusType;\n}\n\nexport interface PutBucketVersioningInput {\n  bucket?: string;\n  status: PutBucketVersioningInputStatus;\n}\n\nexport async function getBucketVersioning(this: TOSBase, bucket?: string) {\n  return this.fetchBucket<GetBucketVersioningOutput>(\n    bucket,\n    'GET',\n    { versioning: '' },\n    {}\n  );\n}\n\nexport async function putBucketVersioning(\n  this: TOSBase,\n  input: PutBucketVersioningInput\n) {\n  return this.fetchBucket(\n    input.bucket,\n    'PUT',\n    { versioning: '' },\n    {},\n    {\n      Status: input.status,\n    }\n  );\n}\n", "import TosClientError from '../../TosClientError';\nimport { obj2QueryStr } from '../../utils';\nimport TOSBase from '../base';\n\nexport interface PreSignedPolicyURLInput {\n  bucket?: string;\n  /**\n   * unit: s\n   * default value: 3600\n   * range is: [1, 604800]\n   */\n  expires?: number;\n\n  conditions: PolicySignatureCondition[];\n\n  alternativeEndpoint?: string;\n  /**\n   * default: false\n   * if set true. generate domain will direct use `endpoint` or `alternativeEndpoint`.\n   */\n  isCustomDomain?: boolean;\n}\n\nexport interface PreSignedPolicyURLOutput {\n  getSignedURLForList(additionalQuery?: Record<string, string>): string;\n\n  // since conditions maybe includes multi exact key, so key isn't predictable\n  getSignedURLForGetOrHead(\n    key: string,\n    additionalQuery?: Record<string, string>\n  ): string;\n\n  signedQuery: string;\n}\n\nexport interface PolicySignatureCondition {\n  key: 'key';\n  value: string;\n  operator?: 'eq' | 'starts-with';\n}\n\ninterface NormalizedInput {\n  bucket: string;\n  /**\n   * unit: s\n   * default value: 3600\n   * range is: [1, 604800]\n   */\n  expires: number;\n\n  conditions: NormalizedPolicySignatureCondition[];\n\n  alternativeEndpoint?: string;\n}\n\ntype NormalizedPolicySignatureCondition = [\n  'eq' | 'starts-with',\n  '$key' | '$bucket',\n  string\n];\n\nexport function preSignedPolicyURL(\n  this: TOSBase,\n  input: PreSignedPolicyURLInput\n): PreSignedPolicyURLOutput {\n  const normalizedInput = normalizeInput.call(this, input);\n\n  validateConditions(input.conditions);\n\n  const endpoint =\n    input.alternativeEndpoint ||\n    (input.isCustomDomain\n      ? this.opts.endpoint\n      : `${normalizedInput.bucket}.${this.opts.endpoint}`);\n\n  const baseURL = `http${this.opts.secure ? 's' : ''}://${endpoint}`;\n\n  const query = this.getSignatureQuery({\n    bucket: normalizedInput.bucket,\n    expires: normalizedInput.expires,\n    policy: {\n      conditions: normalizedInput.conditions,\n    },\n  });\n\n  const queryStr = obj2QueryStr(query);\n\n  const getSignedURLForList: PreSignedPolicyURLOutput['getSignedURLForList'] = (\n    additionalQuery\n  ) => {\n    const str2 = obj2QueryStr(additionalQuery);\n    const q = [queryStr, str2].filter(Boolean).join('&');\n    return `${baseURL}?${q}`;\n  };\n  const getSignedURLForGetOrHead: PreSignedPolicyURLOutput['getSignedURLForGetOrHead'] =\n    (key, additionalQuery) => {\n      const str2 = obj2QueryStr(additionalQuery);\n      const q = [queryStr, str2].filter(Boolean).join('&');\n      // keep   '/'\n      const keyPath = key\n        .split('/')\n        .map((it) => encodeURIComponent(it))\n        .join('/');\n      return `${baseURL}/${keyPath}?${q}`;\n    };\n  return {\n    getSignedURLForList,\n    getSignedURLForGetOrHead,\n    signedQuery: queryStr,\n  };\n}\n\nfunction normalizeInput(\n  this: TOSBase,\n  input: PreSignedPolicyURLInput\n): NormalizedInput {\n  const actualBucket = input.bucket || this.opts.bucket;\n  const defaultExpires = 3600;\n\n  if (!actualBucket) {\n    throw new TosClientError('Must provide bucket param');\n  }\n\n  validateConditions(input.conditions);\n  const normalizedConditions: NormalizedPolicySignatureCondition[] =\n    input.conditions.map((it) => [it.operator || 'eq', '$key', it.value]);\n  normalizedConditions.push(['eq', '$bucket', actualBucket]);\n\n  return {\n    bucket: actualBucket,\n    expires: input.expires || defaultExpires,\n    conditions: normalizedConditions,\n  };\n}\n\nfunction validateConditions(conditions: PolicySignatureCondition[]) {\n  if (conditions.length < 1) {\n    throw new TosClientError(\n      'The `conditions` field of `PreSignedPolicyURLInput` must has one item at least'\n    );\n  }\n\n  for (const it of conditions) {\n    if (it.key !== 'key') {\n      throw new TosClientError(\n        \"The `key` field of `PolicySignatureCondition` must be `'key'`\"\n      );\n    }\n\n    if (it.operator && it.operator !== 'eq' && it.operator !== 'starts-with') {\n      throw new TosClientError(\n        \"The `operator` field of `PolicySignatureCondition` must be `'eq'` or `'starts-with'`\"\n      );\n    }\n  }\n}\n", "import TOSBase from '../base';\n\nexport interface GetBucketLocationInput {\n  bucket: string;\n}\n\nexport interface GetBucketLocationOutput {\n  ExtranetEndpoint: string;\n  IntranetEndpoint: string;\n  Region: string;\n}\n\nexport async function getBucketLocation(\n  this: TOSBase,\n  input: GetBucketLocationInput\n) {\n  const { bucket } = input;\n\n  return this.fetchBucket<GetBucketLocationOutput>(\n    bucket,\n    'GET',\n    { location: '' },\n    {}\n  );\n}\n", "import { HttpMethodType } from '../../TosExportEnum';\nimport { handleEmptyServerError } from '../../handleEmptyServerError';\nimport TOSBase from '../base';\n\nexport interface CORSRule {\n  AllowedOrigins: string[];\n  AllowedMethods: HttpMethodType[];\n  AllowedHeaders: string[];\n  ExposeHeaders: string[];\n  MaxAgeSeconds: number;\n  ResponseVary?: boolean;\n}\n\nexport interface GetBucketCORSInput {\n  bucket: string;\n}\n\nexport interface GetBucketCORSOutput {\n  CORSRules: CORSRule[];\n}\n\nexport async function getBucketCORS(this: TOSBase, input: GetBucketCORSInput) {\n  try {\n    const { bucket } = input;\n\n    return await this.fetchBucket<GetBucketCORSOutput>(\n      bucket,\n      'GET',\n      { cors: '' },\n      {}\n    );\n  } catch (error) {\n    return handleEmptyServerError<GetBucketCORSOutput>(error, {\n      defaultResponse: { CORSRules: [] },\n      enableCatchEmptyServerError: this.opts.enableOptimizeMethodBehavior,\n      methodKey: 'getBucketCORS',\n    });\n  }\n}\n\nexport interface PutBucketCORSInput {\n  bucket: string;\n  CORSRules: CORSRule[];\n}\n\nexport interface PutBucketCORSOutput {}\n\nexport async function putBucketCORS(this: TOSBase, input: PutBucketCORSInput) {\n  const { bucket, CORSRules } = input;\n  if (this.opts.enableOptimizeMethodBehavior && !CORSRules.length) {\n    return deleteBucketCORS.call(this, { bucket });\n  }\n  return this.fetchBucket<PutBucketCORSOutput>(\n    bucket,\n    'PUT',\n    { cors: '' },\n    {},\n    { CORSRules }\n  );\n}\n\nexport interface DeleteBucketCORSInput {\n  bucket: string;\n}\n\nexport interface DeleteBucketCORSOutput {}\n\nexport async function deleteBucketCORS(\n  this: TOSBase,\n  input: DeleteBucketCORSInput\n) {\n  const { bucket } = input;\n\n  return this.fetchBucket<DeleteBucketCORSOutput>(\n    bucket,\n    'DELETE',\n    { cors: '' },\n    {}\n  );\n}\n", "import { StorageClassType } from '../../TosExportEnum';\nimport TOSBase from '../base';\nimport { fillRequestHeaders, normalizeHeadersKey } from '../../utils';\nimport { handleEmptyServerError } from '../../handleEmptyServerError';\n\ninterface LifecycleRule {\n  ID?: string;\n  Prefix?: string;\n  Status: 'Enabled' | 'Disabled';\n  Filter?: {\n    GreaterThanIncludeEqual?: 'Enabled' | 'Disabled';\n    LessThanIncludeEqual?: 'Enabled' | 'Disabled';\n    /** unit bit */\n    ObjectSizeGreaterThan?: number;\n    /** unit bit */\n    ObjectSizeLessThan?: number;\n  };\n  Expiration?: { Date?: string; Days?: number };\n  Days?: number;\n  Date?: string;\n  NoncurrentVersionExpiration?: {\n    NoncurrentDays?: number;\n    NoncurrentDate?: string;\n  };\n  AbortIncompleteMultipartUpload?: { DaysAfterInitiation?: number };\n  DaysAfterInitiation?: number;\n  Transitions?: {\n    StorageClass: StorageClassType;\n    Days?: number;\n    Date?: string;\n  }[];\n  /**\n   * @private unstable\n   */\n  AccessTimeTransitions?: {\n    StorageClass: StorageClassType;\n    Days?: number;\n  }[];\n  /**\n   * @private unstable\n   */\n  NoncurrentVersionAccessTimeTransitions?: {\n    StorageClass: StorageClassType;\n    NoncurrentDays?: number;\n  }[];\n  NoncurrentVersionTransitions?: {\n    StorageClass?: StorageClassType;\n    NoncurrentDays?: number;\n    NoncurrentDate?: string;\n  }[];\n  Tags?: {\n    Key?: string;\n    Value?: string;\n  }[];\n}\n\nexport interface PutBucketLifecycleInput {\n  bucket: string;\n  rules: LifecycleRule[];\n  allowSameActionOverlap?: boolean;\n}\n\nexport interface PutBucketLifecycleOutput {}\n\nexport async function putBucketLifecycle(\n  this: TOSBase,\n  input: PutBucketLifecycleInput\n) {\n  const { bucket, rules } = input;\n  if (this.opts.enableOptimizeMethodBehavior && !rules.length) {\n    return deleteBucketLifecycle.call(this, { bucket });\n  }\n\n  const headers = {};\n  fillRequestHeaders({ ...input, headers }, ['allowSameActionOverlap']);\n\n  return this.fetchBucket<PutBucketLifecycleOutput>(\n    bucket,\n    'PUT',\n    { lifecycle: '' },\n    headers,\n    {\n      Rules: rules,\n    }\n  );\n}\n\nexport interface GetBucketLifecycleInput {\n  bucket: string;\n}\n\nexport interface GetBucketLifecycleOutput {\n  Rules: LifecycleRule[];\n  AllowSameActionOverlap?: boolean;\n}\n\nexport async function getBucketLifecycle(\n  this: TOSBase,\n  input: GetBucketLifecycleInput\n) {\n  try {\n    const { bucket } = input;\n\n    return await this.fetchBucket<GetBucketLifecycleOutput>(\n      bucket,\n      'GET',\n      { lifecycle: '' },\n      {},\n      {},\n      {\n        handleResponse: (res) => {\n          return {\n            AllowSameActionOverlap:\n              res.headers['x-tos-allow-same-action-overlap'],\n            Rules: res.data.Rules,\n          };\n        },\n      }\n    );\n  } catch (error) {\n    return handleEmptyServerError<GetBucketLifecycleOutput>(error, {\n      enableCatchEmptyServerError: this.opts.enableOptimizeMethodBehavior,\n      methodKey: 'getBucketLifecycle',\n      defaultResponse: {\n        Rules: [],\n      },\n    });\n  }\n}\n\nexport interface DeleteBucketLifecycleInput {\n  bucket: string;\n}\n\nexport interface DeleteBucketLifecycleOutput {}\n\nexport async function deleteBucketLifecycle(\n  this: TOSBase,\n  input: DeleteBucketLifecycleInput\n) {\n  const { bucket } = input;\n\n  return this.fetchBucket<DeleteBucketLifecycleOutput>(\n    bucket,\n    'DELETE',\n    { lifecycle: '' },\n    {}\n  );\n}\n", "import TOSBase from '../base';\nimport { hashMd5 } from '../../universal/crypto.browser';\n\nexport interface EncryptionData {\n  Rule: EncryptionDataRule;\n}\nexport interface EncryptionDataRule {\n  ApplyServerSideEncryptionByDefault: {\n    // SSEAlgorithm support 'kms' and 'AES256' and 'sm4'\n    SSEAlgorithm: string;\n    KMSMasterKeyID?: string;\n    /** @private unstable */\n    KMSDataEncryption?: string;\n  };\n}\n\nexport async function putBucketEncryption(\n  this: TOSBase,\n  input: { rule: EncryptionDataRule } & { bucket?: string }\n) {\n  const { bucket, rule } = input;\n\n  return this.fetchBucket(\n    bucket,\n    'PUT',\n    { encryption: '' },\n    {\n      'Content-MD5': hashMd5(\n        JSON.stringify({\n          Rule: rule,\n        }),\n        'base64'\n      ),\n    },\n    {\n      Rule: rule,\n    }\n  );\n}\n\nexport async function getBucketEncryption(\n  this: TOSBase,\n  input: { bucket?: string }\n) {\n  const { bucket } = input;\n\n  return this.fetchBucket<EncryptionData>(\n    bucket,\n    'GET',\n    { encryption: '' },\n    {}\n  );\n}\n\nexport async function deleteBucketEncryption(\n  this: TOSBase,\n  input: { bucket?: string }\n) {\n  const { bucket } = input;\n\n  return this.fetchBucket(bucket, 'DELETE', { encryption: '' }, {});\n}\n", "import { handleEmptyServerError } from '../../handleEmptyServerError';\nimport TOSBase from '../base';\n\nconst CommonQueryKey = 'mirror';\n\nexport interface MirrorBackRule {\n  ID: string;\n  Condition: {\n    HttpCode: number;\n    KeyPrefix?: string;\n    KeySuffix?: string;\n    /** private unstable */\n    AllowHost?: string[];\n    /** private unstable */\n    HttpMethod?: string[];\n  };\n  Redirect: {\n    RedirectType?: 'Mirror' | 'Async';\n    FetchSourceOnRedirect?: boolean;\n    /** @private unstable */\n    FetchSourceOnRedirectWithQuery?: boolean;\n    PublicSource?: {\n      SourceEndpoint: {\n        Primary: string[];\n        Follower?: string[];\n      };\n      FixedEndpoint?: boolean;\n    };\n    /** @private unstable */\n    PrivateSource?: {\n      SourceEndpoint: {\n        Primary: {\n          Endpoint: string;\n          BucketName: string;\n          CredentialProvider: { Role: string };\n        }[];\n      };\n    };\n    PassQuery?: boolean;\n    FollowRedirect?: boolean;\n    MirrorHeader?: {\n      PassAll?: boolean;\n      Pass?: string[];\n      Remove?: string[];\n      /** private unstable */\n      Set?: { Key: string; Value: string }[];\n    };\n\n    Transform?: {\n      WithKeyPrefix?: string;\n      WithKeySuffix?: string;\n      ReplaceKeyPrefix?: {\n        KeyPrefix?: string;\n        ReplaceWith?: string;\n      };\n    };\n  };\n}\n\nexport interface PutBucketMirrorBackInput {\n  bucket: string;\n  rules: MirrorBackRule[];\n}\n\nexport interface PutBucketMirrorBackOutput {}\n\nexport async function putBucketMirrorBack(\n  this: TOSBase,\n  input: PutBucketMirrorBackInput\n) {\n  const { bucket, rules } = input;\n  if (this.opts.enableOptimizeMethodBehavior && !rules.length) {\n    return deleteBucketMirrorBack.call(this, { bucket });\n  }\n\n  return this.fetchBucket<PutBucketMirrorBackOutput>(\n    bucket,\n    'PUT',\n    { [CommonQueryKey]: '' },\n    {},\n    {\n      Rules: rules,\n    }\n  );\n}\n\nexport interface GetBucketMirrorBackInput {\n  bucket: string;\n}\n\nexport interface GetBucketMirrorBackOutput {\n  Rules: MirrorBackRule[];\n}\n\nexport async function getBucketMirrorBack(\n  this: TOSBase,\n  input: GetBucketMirrorBackInput\n) {\n  const { bucket } = input;\n\n  try {\n    return await this.fetchBucket<GetBucketMirrorBackOutput>(\n      bucket,\n      'GET',\n      { [CommonQueryKey]: '' },\n      {}\n    );\n  } catch (error) {\n    return handleEmptyServerError<GetBucketMirrorBackOutput>(error, {\n      enableCatchEmptyServerError: this.opts.enableOptimizeMethodBehavior,\n      methodKey: 'getBucketMirrorBack',\n      defaultResponse: {\n        Rules: [],\n      },\n    });\n  }\n}\n\nexport interface DeleteBucketMirrorBackInput {\n  bucket: string;\n}\n\nexport interface DeleteBucketMirrorBackOutput {}\n\nexport async function deleteBucketMirrorBack(\n  this: TOSBase,\n  input: DeleteBucketMirrorBackInput\n) {\n  const { bucket } = input;\n\n  return this.fetchBucket<DeleteBucketMirrorBackOutput>(\n    bucket,\n    'DELETE',\n    { [CommonQueryKey]: '' },\n    {}\n  );\n}\n", "import { makeArrayProp, normalizeHead<PERSON><PERSON>ey } from '../../utils';\nimport TOSBase from '../base';\n\nconst CommonQueryKey = 'tagging';\n\ninterface TagSet {\n  Tags: {\n    Key: string;\n    Value: string;\n  }[];\n}\n\nexport interface PutObjectTaggingInput {\n  bucket: string;\n  key: string;\n  versionId?: string;\n  tagSet: TagSet;\n}\n\nexport interface PutObjectTaggingOutput {}\n\nexport async function putObjectTagging(\n  this: TOSBase,\n  input: PutObjectTaggingInput\n) {\n  const { tagSet, versionId } = input;\n  const headers = normalizeHeadersKey({\n    versionId,\n  });\n\n  return this._fetchObject<PutObjectTaggingOutput>(\n    input,\n    'PUT',\n    { [CommonQueryKey]: '', ...headers },\n    {},\n    {\n      TagSet: tagSet,\n    }\n  );\n}\n\nexport interface GetObjectTaggingInput {\n  bucket: string;\n  key: string;\n  versionId?: string;\n}\n\nexport interface GetObjectTaggingOutput {\n  TagSet: TagSet;\n}\n\nexport async function getObjectTagging(\n  this: TOSBase,\n  input: GetObjectTaggingInput\n) {\n  const { versionId } = input;\n  const headers = normalizeHeadersKey({\n    versionId,\n  });\n  const res = await this._fetchObject<GetObjectTaggingOutput>(\n    input,\n\n    'GET',\n    { [CommonQueryKey]: '', ...headers },\n    {}\n  );\n  makeArrayProp(res.data.TagSet)('Tags');\n  return res;\n}\n\nexport interface DeleteObjectTaggingInput {\n  bucket: string;\n  key: string;\n  versionId?: string;\n}\n\nexport interface DeleteObjectTaggingOutput {}\n\nexport async function deleteObjectTagging(\n  this: TOSBase,\n  input: DeleteObjectTaggingInput\n) {\n  const { versionId } = input;\n  const headers = normalizeHeadersKey({\n    versionId,\n  });\n\n  return this._fetchObject<DeleteObjectTaggingOutput>(\n    input,\n    'DELETE',\n    { [CommonQueryKey]: '', ...headers },\n    {}\n  );\n}\n", "import {\n  ACLType,\n  StorageClassInheritDirectiveType,\n  StorageClassType,\n} from '../../TosExportEnum';\nimport { handleEmptyServerError } from '../../handleEmptyServerError';\nimport TOSBase from '../base';\n\nconst CommonQueryKey = 'replication';\n\nexport interface ReplicationTag {\n  Key: string;\n  Value: string;\n}\n\nexport interface ReplicationRule {\n  ID: string;\n  Status: string;\n  PrefixSet?: string[];\n  Destination: {\n    Bucket: string;\n    Location: string;\n    StorageClass?: StorageClassType;\n    StorageClassInheritDirective: StorageClassInheritDirectiveType;\n  };\n  /**\n   *  @private unstable: internal(default) |tos_acc\n   */\n  TransferType?: string;\n  HistoricalObjectReplication: 'Enabled' | 'Disabled';\n  /** @private unstable */\n  Tags?: ReplicationTag[];\n  AccessControlTranslation?: {\n    Owner: string; //\"BucketOwnerEntrusted\"\n  };\n}\n\nexport interface PutBucketReplicationInput {\n  bucket: string;\n  role: string;\n  rules: ReplicationRule[];\n}\n\nexport interface PutBucketReplicationOutput {}\n\nexport async function putBucketReplication(\n  this: TOSBase,\n  input: PutBucketReplicationInput\n) {\n  const { bucket, rules, role } = input;\n  if (this.opts.enableOptimizeMethodBehavior && !rules.length) {\n    return deleteBucketReplication.call(this, { bucket });\n  }\n\n  return this.fetchBucket<PutBucketReplicationOutput>(\n    bucket,\n    'PUT',\n    { [CommonQueryKey]: '' },\n    {},\n    {\n      Role: role,\n      Rules: rules,\n    }\n  );\n}\n\nexport interface GetBucketReplicationInput {\n  bucket: string;\n  progress?: string;\n  ruleId?: string;\n}\n\nexport interface GetBucketReplicationOutput {\n  Role: string;\n  Rules: ReplicationRule[];\n}\n\nexport async function getBucketReplication(\n  this: TOSBase,\n  input: GetBucketReplicationInput\n) {\n  const { bucket, progress, ruleId } = input;\n  const query: Record<string, string> = {\n    [CommonQueryKey]: '',\n    progress: progress || '',\n  };\n  if (ruleId != null) {\n    query['rule-id'] = `${ruleId}`;\n  }\n\n  try {\n    return await this.fetchBucket<GetBucketReplicationOutput>(\n      bucket,\n      'GET',\n      query,\n      {}\n    );\n  } catch (err) {\n    return handleEmptyServerError<GetBucketReplicationOutput>(err, {\n      enableCatchEmptyServerError: this.opts.enableOptimizeMethodBehavior,\n      methodKey: 'getBucketReplication',\n      defaultResponse: {\n        Rules: [],\n        Role: '',\n      },\n    });\n  }\n}\n\nexport interface DeleteBucketReplicationInput {\n  bucket: string;\n}\n\nexport interface DeleteBucketReplicationOutput {}\n\nexport async function deleteBucketReplication(\n  this: TOSBase,\n  input: DeleteBucketReplicationInput\n) {\n  const { bucket } = input;\n\n  return this.fetchBucket<DeleteBucketReplicationOutput>(\n    bucket,\n    'DELETE',\n    { [CommonQueryKey]: '' },\n    {}\n  );\n}\n", "import { convertNormalCamelCase2Upper } from '../../utils';\nimport { handleEmptyServerError } from '../../handleEmptyServerError';\nimport TOSBase from '../base';\n\nconst CommonQueryKey = 'website';\n\ntype Protocol = 'http' | 'https';\n\ninterface RedirectAllRequestsTo {\n  HostName: string;\n  Protocol?: Protocol;\n}\n\ninterface IndexDocument {\n  Suffix: string;\n  ForbiddenSubDir?: boolean;\n}\ninterface ErrorDocument {\n  Key?: string;\n}\ninterface RoutingRule {\n  Condition: {\n    HttpErrorCodeReturnedEquals?: number;\n    KeyPrefixEquals?: string;\n  };\n  Redirect: {\n    HostName?: string;\n    HttpRedirectCode?: number;\n    Protocol?: Protocol;\n    ReplaceKeyPrefixWith?: string;\n    ReplaceKeyWith?: string;\n  };\n}\n\nexport interface PutBucketWebsiteInput {\n  bucket: string;\n  redirectAllRequestsTo?: RedirectAllRequestsTo;\n  indexDocument?: IndexDocument;\n  errorDocument?: ErrorDocument;\n  routingRules?: RoutingRule[];\n}\n\nexport interface PutBucketWebsiteOutput {}\n\nexport async function putBucketWebsite(\n  this: TOSBase,\n  input: PutBucketWebsiteInput\n) {\n  const { bucket, ...otherProps } = input;\n\n  const body = convertNormalCamelCase2Upper(otherProps);\n  return this.fetchBucket<PutBucketWebsiteOutput>(\n    bucket,\n    'PUT',\n    { [CommonQueryKey]: '' },\n    {},\n    {\n      ...body,\n    }\n  );\n}\n\nexport interface GetBucketWebsiteInput {\n  bucket: string;\n}\n\nexport interface GetBucketWebsiteOutput {\n  RedirectAllRequestsTo?: RedirectAllRequestsTo;\n  IndexDocument?: IndexDocument;\n  ErrorDocument?: ErrorDocument;\n  RoutingRules?: RoutingRule[];\n}\n\nexport async function getBucketWebsite(\n  this: TOSBase,\n  input: GetBucketWebsiteInput\n) {\n  const { bucket } = input;\n\n  try {\n    return this.fetchBucket<GetBucketWebsiteOutput>(\n      bucket,\n      'GET',\n      { [CommonQueryKey]: '' },\n      {}\n    );\n  } catch (error) {\n    return handleEmptyServerError<GetBucketWebsiteOutput>(error, {\n      enableCatchEmptyServerError: this.opts.enableOptimizeMethodBehavior,\n      methodKey: 'getBucketWebsite',\n      defaultResponse: {\n        RoutingRules: [],\n      },\n    });\n  }\n}\n\nexport interface DeleteBucketWebsiteInput {\n  bucket: string;\n}\n\nexport interface DeleteBucketWebsiteOutput {}\n\nexport async function deleteBucketWebsite(\n  this: TOSBase,\n  input: DeleteBucketWebsiteInput\n) {\n  const { bucket } = input;\n\n  return this.fetchBucket<DeleteBucketWebsiteOutput>(\n    bucket,\n    'DELETE',\n    { [CommonQueryKey]: '' },\n    {}\n  );\n}\n", "import { convertNormalCamelCase2Upper } from '../../utils';\nimport { handleEmptyServerError } from '../../handleEmptyServerError';\nimport TOSBase from '../base';\n\nconst CommonQueryKey = 'notification';\n\nexport interface Filter {\n  TOSKey?: {\n    FilterRules: {\n      Name: string;\n      Value: string;\n    }[];\n  };\n}\ninterface CloudFunctionConfiguration {\n  Events: string[];\n  Filter?: Filter;\n  RuleId?: string;\n  CloudFunction: string;\n}\n\nexport interface RocketMQConf {\n  InstanceId: string;\n  Topic: string;\n  AccessKeyId: string;\n}\nexport interface RocketMQConfiguration {\n  RuleId: string;\n  Role: string;\n  Events: string[]; // 支持的值在不断增加，不定义成枚举\n  Filter?: Filter;\n  RocketMQ: RocketMQConf;\n}\n\nexport interface PutBucketNotificationInput {\n  bucket: string;\n  cloudFunctionConfigurations?: CloudFunctionConfiguration[];\n  rocketMQConfigurations?: RocketMQConfiguration[];\n}\n\nexport interface PutBucketNotificationOutput {}\n\n/**\n * @deprecated use PutBucketNotificationType2 instead\n */\nexport async function putBucketNotification(\n  this: TOSBase,\n  input: PutBucketNotificationInput\n) {\n  const { bucket, ...otherProps } = input;\n\n  const body = convertNormalCamelCase2Upper(otherProps);\n  return this.fetchBucket<PutBucketNotificationOutput>(\n    bucket,\n    'PUT',\n    { [CommonQueryKey]: '' },\n    {},\n    {\n      ...body,\n    }\n  );\n}\n\nexport interface GetBucketNotificationInput {\n  bucket: string;\n}\n\nexport interface GetBucketNotificationOutput {\n  CloudFunctionConfigurations: CloudFunctionConfiguration[];\n  RocketMQConfigurations: RocketMQConfiguration[];\n}\n\n/**\n * @deprecated use GetBucketNotificationType2 instead\n */\nexport async function getBucketNotification(\n  this: TOSBase,\n  input: GetBucketNotificationInput\n) {\n  const { bucket } = input;\n  try {\n    return await this.fetchBucket<GetBucketNotificationOutput>(\n      bucket,\n      'GET',\n      { [CommonQueryKey]: '' },\n      {}\n    );\n  } catch (error) {\n    return handleEmptyServerError<GetBucketNotificationOutput>(error, {\n      enableCatchEmptyServerError: this.opts.enableOptimizeMethodBehavior,\n      methodKey: 'getBucketNotification',\n      defaultResponse: {\n        CloudFunctionConfigurations: [],\n        RocketMQConfigurations: [],\n      },\n    });\n  }\n}\n", "import { convertNormalCamelCase2Upper } from '../../utils';\nimport { handleEmptyServerError } from '../../handleEmptyServerError';\nimport TOSBase from '../base';\n\nconst CommonQueryKey = 'customdomain';\n\nexport interface CustomDomainRule {\n  Domain: string;\n  Cname: string;\n  Forbidden?: boolean;\n  ForbiddenReason?: string;\n  CertId?: string;\n  CertStatus?: string;\n  /**@private unstable\n   * value tos|s3\n   * */\n  Protocol?: string;\n}\n\nexport interface PutBucketCustomDomainInput {\n  bucket: string;\n  customDomainRule: {\n    Domain: string;\n    CertId?: string;\n    /**@private unstable\n     * value tos|s3\n     * */\n    Protocol?: string;\n  };\n}\n\nexport interface PutBucketCustomDomainOutput {}\n\nexport async function putBucketCustomDomain(\n  this: TOSBase,\n  input: PutBucketCustomDomainInput\n) {\n  const { bucket, ...otherProps } = input;\n\n  const body = convertNormalCamelCase2Upper(otherProps);\n  return this.fetchBucket<PutBucketCustomDomainOutput>(\n    bucket,\n    'PUT',\n    { [CommonQueryKey]: '' },\n    {},\n    {\n      ...body,\n    }\n  );\n}\n\nexport interface GetBucketCustomDomainInput {\n  bucket: string;\n}\n\nexport interface GetBucketCustomDomainOutput {\n  CustomDomainRules: CustomDomainRule[];\n}\n\nexport async function getBucketCustomDomain(\n  this: TOSBase,\n  input: GetBucketCustomDomainInput\n) {\n  try {\n    const { bucket } = input;\n    return await this.fetchBucket<GetBucketCustomDomainOutput>(\n      bucket,\n      'GET',\n      { [CommonQueryKey]: '' },\n      {}\n    );\n  } catch (error) {\n    return handleEmptyServerError<GetBucketCustomDomainOutput>(error, {\n      defaultResponse: { CustomDomainRules: [] },\n      enableCatchEmptyServerError: this.opts.enableOptimizeMethodBehavior,\n      methodKey: 'getBucketCustomDomain',\n    });\n  }\n}\n\nexport interface DeleteBucketCustomDomainInput {\n  bucket: string;\n  customDomain: string;\n}\n\nexport interface DeleteBucketCustomDomainOutput {}\n\nexport async function deleteBucketCustomDomain(\n  this: TOSBase,\n  input: DeleteBucketCustomDomainInput\n) {\n  const { bucket, customDomain } = input;\n\n  return this.fetchBucket<DeleteBucketCustomDomainOutput>(\n    bucket,\n    'DELETE',\n    { customdomain: customDomain },\n    {}\n  );\n}\n", "import { convertNormalCamelCase2Upper } from '../../utils';\nimport { handleEmptyServerError } from '../../handleEmptyServerError';\nimport TOSBase from '../base';\n\nconst CommonQueryKey = 'realtimeLog';\n\ninterface AccessLogConfiguration {\n  UseServiceTopic: boolean;\n  TLSProjectID?: string;\n  TLSTopicID?: string;\n}\n\ninterface RealTimeLogConfiguration {\n  Role: string;\n  AccessLogConfiguration: AccessLogConfiguration;\n}\n\nexport interface PutBucketRealTimeLogInput {\n  bucket: string;\n  realTimeLogConfiguration: RealTimeLogConfiguration;\n}\n\nexport interface PutBucketRealTimeLogOutput {}\n\nexport async function putBucketRealTimeLog(\n  this: TOSBase,\n  input: PutBucketRealTimeLogInput\n) {\n  const { bucket, ...otherProps } = input;\n\n  const body = convertNormalCamelCase2Upper(otherProps);\n  return this.fetchBucket<PutBucketRealTimeLogOutput>(\n    bucket,\n    'PUT',\n    { [CommonQueryKey]: '' },\n    {},\n    {\n      ...body,\n    }\n  );\n}\n\nexport interface GetBucketRealTimeLogInput {\n  bucket: string;\n}\n\nexport interface GetBucketRealTimeLogOutput {\n  RealTimeLogConfiguration?: RealTimeLogConfiguration;\n}\n\nexport async function getBucketRealTimeLog(\n  this: TOSBase,\n  input: GetBucketRealTimeLogInput\n) {\n  const { bucket } = input;\n\n  try {\n    return await this.fetchBucket<GetBucketRealTimeLogOutput>(\n      bucket,\n      'GET',\n      { [CommonQueryKey]: '' },\n      {}\n    );\n  } catch (error) {\n    return handleEmptyServerError<GetBucketRealTimeLogOutput>(error, {\n      enableCatchEmptyServerError: this.opts.enableOptimizeMethodBehavior,\n      methodKey: 'getBucketRealTimeLog',\n      defaultResponse: {},\n    });\n  }\n}\n\nexport interface DeleteBucketRealTimeLogInput {\n  bucket: string;\n}\n\nexport interface DeleteBucketRealTimeLogOutput {}\n\nexport async function deleteBucketRealTimeLog(\n  this: TOSBase,\n  input: DeleteBucketRealTimeLogInput\n) {\n  const { bucket } = input;\n\n  return this.fetchBucket<DeleteBucketRealTimeLogOutput>(\n    bucket,\n    'DELETE',\n    { [CommonQueryKey]: '' },\n    {}\n  );\n}\n", "import { handleEmptyServerError } from '../../handleEmptyServerError';\nimport TOSBase, { TosResponse } from '../base';\n\n/**\n * 清单文件导出周期\n */\nexport enum ScheduleFrequency {\n  /** 按天 */\n  Daily = 'Daily',\n  /** 按周 */\n  Weekly = 'Weekly',\n}\n\n/**\n * 清单包含Object版本信息值\n */\nexport enum IncludedObjectVersions {\n  /** 全部 */\n  All = 'All',\n  /** 当前版本 */\n  Current = 'Current',\n}\n\n/**\n * 清单配置项\n */\nexport enum InventoryOptionalFields {\n  /** Object的大小 */\n  Size = 'Size',\n  /** Object的最后修改时间 */\n  LastModifiedDat = 'LastModifiedDate',\n  /** 标识Object的内容 */\n  ETag = 'ETag',\n  /** Object的存储类型 */\n  StorageClass = 'StorageClass',\n  /** 是否为通过分片上传的Object */\n  IsMultipartUploaded = 'IsMultipartUploaded',\n  /** Object是否加密 */\n  EncryptionStatus = 'EncryptionStatus',\n  CRC64 = 'CRC64',\n  /** crr复制状态 */\n  ReplicationStatus = 'ReplicationStatus',\n}\n\n/**\n * 桶清单\n */\nexport interface BucketInventoryItem {\n  /** 清单名称 */\n  Id: string;\n  /** 清单功能是否启用 */\n  IsEnabled: boolean;\n  /** 清单筛选的前缀 */\n  Filter?: {\n    /** 筛选规则的匹配前缀 */\n    Prefix?: string;\n  };\n  /** 存放清单结果 */\n  Destination: {\n    /** Bucket 信息 */\n    TOSBucketDestination: {\n      /** 清单文件的文件格式 */\n      Format: string;\n      /** Bucket 所有者授予的账户ID */\n      AccountId: string;\n      /** 角色名称 */\n      Role: string;\n      /** 存放导出的清单文件的 Bucket */\n      Bucket: string;\n      /** 清单文件的存储路径前缀 */\n      Prefix?: string;\n    };\n  };\n  /** 存放清单导出周期信息 */\n  Schedule: {\n    /** 导出的周期 */\n    Frequency: ScheduleFrequency;\n  };\n  /** 是否在清单中包含 Object 版本信息 */\n  IncludedObjectVersions: string;\n  /** 配置项 */\n  OptionalFields?: {\n    Field: InventoryOptionalFields[];\n  };\n}\n\nexport interface PutBucketInventoryInput {\n  bucket: string;\n  inventoryConfiguration: BucketInventoryItem;\n}\n\nexport interface PutBucketInventoryOutput {}\n\nexport interface GetBucketInventoryInput {\n  bucket: string;\n  id: string;\n}\n\nexport type GetBucketInventoryOutput = BucketInventoryItem | undefined;\nexport interface ListBucketInventoryInput {\n  bucket: string;\n  continuationToken?: string;\n}\n\nexport interface ListBucketInventoryOutput {\n  InventoryConfigurations: BucketInventoryItem[];\n  IsTruncated?: boolean;\n  NextContinuationToken?: string;\n}\n\nexport interface DeleteBucketInventoryInput {\n  bucket: string;\n  id: string;\n}\n\nexport interface DeleteBucketInventoryOutput {}\n\n/**\n * 获取桶清单详情信息\n */\nexport async function getBucketInventory(\n  this: TOSBase,\n  req: GetBucketInventoryInput\n): Promise<TosResponse<GetBucketInventoryOutput>> {\n  try {\n    const res = await this.fetchBucket<GetBucketInventoryOutput>(\n      req.bucket,\n      'GET',\n      {\n        inventory: '',\n        id: req.id,\n      },\n      {}\n    );\n\n    return res;\n  } catch (error) {\n    return handleEmptyServerError<GetBucketInventoryOutput>(error, {\n      enableCatchEmptyServerError: this.opts.enableOptimizeMethodBehavior,\n      methodKey: 'getBucketInventory',\n      defaultResponse: undefined,\n    });\n  }\n}\n\n/**\n * 分页获取桶清单信息\n */\nexport async function listBucketInventory(\n  this: TOSBase,\n  req: ListBucketInventoryInput\n): Promise<TosResponse<ListBucketInventoryOutput>> {\n  const params = {\n    inventory: '',\n    ...(req.continuationToken\n      ? { 'continuation-token': req.continuationToken }\n      : null),\n  };\n  try {\n    const res = await this.fetchBucket<ListBucketInventoryOutput>(\n      req.bucket,\n      'GET',\n      params,\n      {}\n    );\n    return res;\n  } catch (error) {\n    return handleEmptyServerError<ListBucketInventoryOutput>(error, {\n      enableCatchEmptyServerError: this.opts.enableOptimizeMethodBehavior,\n      methodKey: 'listBucketInventory',\n      defaultResponse: {\n        InventoryConfigurations: [],\n      },\n    });\n  }\n}\n\n/**\n * 删除桶清单\n */\nexport async function deleteBucketInventory(\n  this: TOSBase,\n  req: DeleteBucketInventoryInput\n): Promise<TosResponse<DeleteBucketInventoryOutput>> {\n  return this.fetchBucket(\n    req.bucket,\n    'DELETE',\n    { inventory: '', id: req.id },\n    {}\n  );\n}\n\n/**\n * 更新桶清单\n */\nexport function putBucketInventory(\n  this: TOSBase,\n  req: PutBucketInventoryInput\n): Promise<TosResponse<PutBucketInventoryOutput>> {\n  return this.fetchBucket(\n    req.bucket,\n    'PUT',\n    { inventory: '', id: req.inventoryConfiguration.Id },\n    {},\n    req.inventoryConfiguration\n  );\n}\n", "/** @file TOS 支持 QoSPolicy(流控策略管理) 相关接口  */\nimport { MergeExclusive } from 'type-fest';\nimport TOSBase from '../base';\n\nexport enum StringOp {\n  StringEquals = 'StringEquals',\n  StringNotEquals = 'StringNotEquals',\n  StringEqualsIgnoreCase = 'StringEqualsIgnoreCase',\n  StringNotEqualsIgnoreCase = 'StringNotEqualsIgnoreCase',\n  StringLike = 'StringLike',\n  StringNotLike = 'StringNotLike',\n}\nexport enum DateOp {\n  DateEquals = 'DateEquals',\n  DateNotEquals = 'DateNotEquals',\n  DateLessThan = 'DateLessThan',\n  DateLessThanEquals = 'DateLessThanEquals',\n  DateGreaterThan = 'DateGreaterThan',\n  DateGreaterThanEquals = 'DateGreaterThanEquals',\n}\nexport enum IpOp {\n  IpAddress = 'IpAddress',\n  NotIpAddress = 'NotIpAddress',\n}\n\n/** (共三种)条件的运算符 */\nexport type QosOp = StringOp | DateOp | IpOp;\n\n/** 服务端模型 - 条件键值对 */\nexport type QosConditions = {\n  [operator in string]?: {\n    [key in string]: string[];\n  };\n};\n\n/** 流控类别 */\nexport enum QuotaType {\n  /** 写Qps */\n  WritesQps = 'WritesQps',\n  /** 读Qps */\n  ReadsQps = 'ReadsQps',\n  /** list类Qps */\n  ListQps = 'ListQps',\n  /** 写带宽 */\n  WritesRate = 'WritesRate',\n  /** 读带宽 */\n  ReadsRate = 'ReadsRate',\n}\nexport type QuotaTypes = {\n  /** 写类 action Qps，取值为正整数 */\n  [QuotaType.WritesQps]?: string;\n  /** 读类 action Qps，取值为正整数 */\n  [QuotaType.ReadsQps]?: string;\n  /** list 类 action Qps，取值为正整数 */\n  [QuotaType.ListQps]?: string;\n  /** 写类 action 带宽，单位为 Mbps，取值为正整数 */\n  [QuotaType.WritesRate]?: string;\n  /** 读类 action 带宽，单位为 Mbps，取值为正整数 */\n  [QuotaType.ReadsRate]?: string;\n};\n\nexport type QosStatement = MergeExclusive<\n  /** 适用的资源列表，不支持两个属性同时使用 */\n  { Resource: string | string[] },\n  { NotResource: string | string[] }\n> &\n  MergeExclusive<\n    /** 适用的账户、用户或者角色，不支持两个属性同时使用 */\n    { Principal: string[] },\n    { NotPrincipal: string[] }\n  > & {\n    /** 策略名称，以区分不同的策略 */\n    Sid: string;\n    /** 流控策略的配额 */\n    Quota: QuotaTypes;\n    /** 指定策略在哪些情况下适用 quota */\n    Condition?: QosConditions;\n  };\n\n/** 服务端预期接收的数据模型 */\nexport interface QosPolicy {\n  /** 策略列表 */\n  Statement: QosStatement[];\n  /** API 接口版本号 */\n  Version?: string;\n  /** Cas 版本号 */\n  CasVersion?: string;\n}\n\nexport interface QosPolicyBaseInput {\n  accountId: string;\n}\n\nexport interface GetQosPolicyInput extends QosPolicyBaseInput {}\nexport interface GetQosPolicyOutput extends QosPolicy {\n  Version: string;\n  CasVersion: string;\n}\n\nexport interface PutQosPolicyInput extends QosPolicy, QosPolicyBaseInput {}\n\nexport interface DeleteQosPolicyInput extends QosPolicyBaseInput {}\n\n/**\n * @private unstable method\n * @description 拉取流控策略列表\n * @param {GetQosPolicyInput}\n * @returns {GetQosPolicyOutput}\n */\nexport async function getQosPolicy(this: TOSBase, params: GetQosPolicyInput) {\n  const { accountId } = params;\n  const res = await this.fetch<GetQosPolicyOutput>(\n    'GET',\n    '/qospolicy',\n    {},\n    {\n      'x-tos-account-id': accountId,\n    },\n    {},\n    {}\n  );\n\n  return res;\n}\n\n/**\n * @private unstable method\n * @description 更新流控策略列表 覆盖全部 QosPolicy\n * @param {PutQosPolicyInput}\n */\nexport async function putQosPolicy(this: TOSBase, params: PutQosPolicyInput) {\n  const { accountId, ...restParams } = params;\n  const res = await this.fetch(\n    'PUT',\n    '/qospolicy',\n    {},\n    {\n      'x-tos-account-id': accountId,\n    },\n    {\n      ...restParams,\n    },\n    {}\n  );\n\n  return res;\n}\n\n/**\n * @private unstable method\n * @description 拉取流控策略列表\n * @param {DeleteQosPolicyInput}\n */\nexport async function deleteQosPolicy(\n  this: TOSBase,\n  params: DeleteQosPolicyInput\n) {\n  const { accountId } = params;\n  const res = await this.fetch(\n    'DELETE',\n    '/qospolicy',\n    {},\n    {\n      'x-tos-account-id': accountId,\n    },\n    {},\n    {}\n  );\n\n  return res;\n}\n", "import TOSBase from '../base';\nimport { convertNormalCamelCase2Upper, paramsSerializer } from '../../utils';\nimport { StorageClassType, TierType } from '../../TosExportEnum';\nexport type JobStatusType =\n  | 'New'\n  | 'Preparing'\n  | 'Suspended'\n  | 'Ready'\n  | 'Active'\n  | 'Pausing'\n  | 'Paused'\n  | 'Complete'\n  | 'Cancelling'\n  | 'Cancelled'\n  | 'Failing'\n  | 'Failed';\n\nexport type DirectiveType = 'COPY' | 'REPLACE' | 'ADD';\nexport type CannedAccessControlListType = 'default' | 'private' | 'public-read';\nexport type PermissionType = 'READ' | 'READ_ACP' | 'WRITE_ACP' | 'FULL_CONTROL';\nexport type PrefixReplaceType = 'true' | 'false';\nexport type ConfirmationRequiredType = '0' | '1';\n\nexport interface Tag {\n  Key: string;\n  Value: string;\n}\n\ninterface Manifest {\n  Location: {\n    ETag: string;\n    ObjectTrn: string;\n    ObjectVersionId?: string;\n  };\n  Spec: {\n    Format: 'TOSInventoryReport_CSV_V1';\n  };\n}\n\nexport interface NewObjectMetadataType {\n  SSEAlgorithm?: 'AES256';\n  UserMetadata?: {\n    member: { Key: string; Value: string }[];\n  };\n  'content-type'?: string;\n  'content-encoding'?: string;\n  'content-language'?: string;\n  'cache-control'?: string;\n  'content-disposition'?: string;\n  expires?: string;\n}\nexport interface NewObjectTaggingType {\n  TOSTag?: Tag[];\n}\nexport interface Report {\n  Bucket: string;\n  Enabled: PrefixReplaceType;\n  Format: 'Report_CSV_V1';\n  Prefix: string;\n  ReportScope: 'AllTasks' | 'FailedTasksOnly';\n}\n\nexport interface ProgressSummary {\n  TotalNumberOfTasks: number;\n  NumberOfTasksSucceeded: number;\n  NumberOfTasksFailed: number;\n}\n\nexport interface ListBatchInput {\n  accountId: string;\n  jobStatuses?: string[];\n  nextToken?: string;\n  maxResults?: number;\n}\n\nexport interface UpdateJobPriorityInput {\n  jobId: string;\n  priority: number;\n  accountId: string;\n}\nexport interface UpdateJobStatusInput {\n  jobId: string;\n  accountId: string;\n  requestedJobStatus: 'Ready' | 'Cancelled';\n  statusUpdateReason?: string;\n}\n\nexport interface JobInput {\n  JobId: string;\n  accountId: string;\n}\n\nexport type DeleteJob = JobInput;\nexport type DescribeJob = JobInput;\n\nexport interface AccessControlList {\n  TOSGrant: {\n    Grantee: {\n      Identifier: string;\n      TypeIdentifier: 'id';\n    };\n    Permission: PermissionType;\n  }[];\n}\nexport interface TOSPutObjectCopy {\n  TOSPutObjectCopy: {\n    PrefixReplace: PrefixReplaceType;\n    ResourcesPrefix: string;\n    TargetKeyPrefix: string;\n    StorageClass: StorageClassType;\n    AccessControlDirective: DirectiveType;\n    CannedAccessControlList?: CannedAccessControlListType;\n    AccessControlGrants?: AccessControlList;\n    TargetResource: string;\n    MetadataDirective: DirectiveType;\n    NewObjectMetadata: NewObjectMetadataType;\n    TaggingDirective: DirectiveType;\n    NewObjectTagging: NewObjectTaggingType;\n  };\n}\n\nexport interface TOSPutObjectAcl {\n  TOSPutObjectAcl: {\n    AccessControlPolicy: {\n      CannedAccessControlList: CannedAccessControlListType;\n      AccessControlList: AccessControlList;\n    };\n  };\n}\n\nexport interface TOSPutObjectTagging {\n  TOSPutObjectTagging: {\n    TOSTag: Tag[];\n  };\n}\n\nexport interface TOSRestoreObject {\n  TOSRestoreObject: {\n    Days: number;\n    Tier: TierType;\n  };\n}\n\nexport interface TOSDeleteObjectTagging {\n  TOSDeleteObjectTagging: {};\n}\n\nexport type PutJobInput = {\n  accountId: string;\n  clientRequestToken: string;\n  confirmationRequired: '0' | '1';\n  description?: string;\n  manifest: Manifest;\n  priority: string;\n  roleTrn: string;\n  report?: Report;\n  operation?:\n    | TOSPutObjectCopy\n    | TOSPutObjectAcl\n    | TOSPutObjectTagging\n    | TOSRestoreObject\n    | TOSDeleteObjectTagging;\n};\n\nexport interface DescribeJobRes {\n  Job: {\n    JobId: string;\n    ConfirmationRequired: ConfirmationRequiredType;\n    Description?: string;\n    FailureReasons?: {\n      JobFailure: {\n        FailureCode: string;\n        FailureReason: string;\n      };\n    };\n    Manifest: Manifest;\n    Priority: number;\n    ProgressSummary: ProgressSummary;\n    Report: Report;\n    RoleArn: string;\n    Status: JobStatusType;\n    StatusUpdateReason: string;\n    SuspendedDate: string;\n    TerminationDate: string;\n    CreationTime: string;\n    Operation:\n      | TOSPutObjectCopy\n      | TOSPutObjectAcl\n      | TOSPutObjectTagging\n      | TOSRestoreObject\n      | TOSDeleteObjectTagging;\n  };\n}\n\nexport interface JobList {\n  JobId: string;\n  CreationTime: string;\n  Operation:\n    | 'TOSPutObjectCopy'\n    | 'TOSPutObjectAcl'\n    | 'TOSPutObjectTagging'\n    | 'TOSRestoreObject'\n    | 'TOSDeleteObjectTagging';\n  Priority: number;\n  ProgressSummary: ProgressSummary;\n  Status: JobStatusType;\n  TerminationDate: string;\n  Description: string;\n}\nexport interface JobListRes {\n  Jobs: {\n    member: JobList[];\n  };\n  NextToken: string;\n}\n\n/**\n *\n * @private unstable method\n * @description 创建批量任务\n * @param params\n * @returns\n */\nexport async function createJob(this: TOSBase, params: PutJobInput) {\n  const { accountId, ...reset } = params;\n  const data = convertNormalCamelCase2Upper(reset);\n  const res = await this.fetch(\n    'POST',\n    '/jobs',\n    {},\n    {\n      'x-tos-account-id': accountId,\n    },\n    {\n      ...data,\n    }\n  );\n  return res;\n}\n\n/**\n * @private unstable method\n * @description  获取批量任务列表\n * @param params\n * @returns\n */\nexport async function listJobs(this: TOSBase, params: ListBatchInput) {\n  const { accountId, maxResults = 1000, ...others } = params;\n  const res = await this.fetch<JobListRes>(\n    'GET',\n    '/jobs',\n    {\n      maxResults,\n      ...others,\n    },\n    {\n      'x-tos-account-id': accountId,\n    },\n    {},\n    {\n      axiosOpts: {\n        paramsSerializer,\n      },\n    }\n  );\n  return res;\n}\n\n/**\n *\n * @private unstable method\n * @description 更新批量任务优先级\n * @param params\n * @returns\n */\nexport async function updateJobPriority(\n  this: TOSBase,\n  params: UpdateJobPriorityInput\n) {\n  const { accountId, jobId: JobId, priority } = params;\n  const res = await this.fetch(\n    'POST',\n    `/jobs/${JobId}/priority`,\n    {\n      priority,\n    },\n    {\n      'x-tos-account-id': accountId,\n    },\n    {},\n    {\n      needMd5: true,\n    }\n  );\n  return res;\n}\n\n/**\n *\n * @private unstable method\n * @description 更新批量任务优先级\n * @param params\n * @returns\n */\nexport async function updateJobStatus(\n  this: TOSBase,\n  params: UpdateJobStatusInput\n) {\n  const {\n    accountId,\n    jobId: JobId,\n    requestedJobStatus,\n    statusUpdateReason,\n  } = params;\n  const res = await this.fetch(\n    'POST',\n    `/jobs/${JobId}/status`,\n    {\n      requestedJobStatus,\n      statusUpdateReason,\n    },\n    {\n      'x-tos-account-id': accountId,\n    },\n    {},\n    {\n      needMd5: true,\n    }\n  );\n  return res;\n}\n\n/**\n *\n * @private unstable method\n * @description 删除批量任务\n * @param params\n * @returns\n */\nexport async function deleteJob(this: TOSBase, params: DeleteJob) {\n  const { accountId, JobId } = params;\n  const res = await this.fetch(\n    'DELETE',\n    `/jobs/${JobId}`,\n    {},\n    {\n      'x-tos-account-id': accountId,\n    },\n    {}\n  );\n  return res;\n}\n\n/**\n *\n * @private unstable method\n * @description 获取批量任务详情\n * @param params\n * @returns\n */\nexport async function describeJob(this: TOSBase, params: DescribeJob) {\n  const { accountId, JobId } = params;\n  const res = await this.fetch<DescribeJobRes>(\n    'GET',\n    `/jobs/${JobId}`,\n    {},\n    {\n      'x-tos-account-id': accountId,\n    },\n    {}\n  );\n  return res;\n}\n", "import { handleEmptyServerError } from '../../handleEmptyServerError';\nimport TOSBase, { TosResponse } from '../base';\n\ninterface TagSet {\n  Tags: {\n    Key: string;\n    Value: string;\n  }[];\n}\n\nexport interface PutBucketTaggingInput {\n  bucket?: string;\n  tagging: {\n    TagSet: TagSet;\n  };\n}\n\nexport interface GetBucketTaggingInput {\n  bucket: string;\n}\nexport interface GetBucketTaggingOutput {\n  TagSet: TagSet;\n}\n\n/**\n * @private unstable method\n */\nexport async function putBucketTagging(\n  this: TOSBase,\n  input: PutBucketTaggingInput\n) {\n  const res = await this.fetchBucket(\n    input.bucket,\n    'PUT',\n    { tagging: '' },\n    {},\n    input.tagging,\n    {\n      needMd5: true,\n    }\n  );\n  return res;\n}\n\n/**\n * @private unstable method\n */\nexport async function getBucketTagging(\n  this: TOSBase,\n  { bucket }: GetBucketTaggingInput\n): Promise<TosResponse<GetBucketTaggingOutput>> {\n  try {\n    const res = await this.fetchBucket<GetBucketTaggingOutput>(\n      bucket,\n      'GET',\n      {\n        tagging: '',\n      },\n      {}\n    );\n    return res;\n  } catch (error) {\n    return handleEmptyServerError<GetBucketTaggingOutput>(error, {\n      enableCatchEmptyServerError: this.opts.enableOptimizeMethodBehavior,\n      methodKey: 'getBucketTagging',\n      defaultResponse: {\n        TagSet: {\n          Tags: [],\n        },\n      },\n    });\n  }\n}\n\nexport interface DeleteBucketTaggingInput {\n  bucket: string;\n}\n\n/**\n * @private unstable method\n */\nexport async function deleteBucketTagging(\n  this: TOSBase,\n  { bucket }: DeleteBucketTaggingInput\n) {\n  return this.fetchBucket(bucket, 'DELETE', { tagging: '' }, {});\n}\n", "import TOSBase, { TosResponse } from '../base';\n\nexport interface BucketPayByTraffic {\n  ChargeType: 'FlowOut' | 'Bandwidth';\n  ActiveType: 'NextDay' | 'NextMonth';\n}\n\nexport interface PutBucketPayByTrafficInput {\n  bucket?: string;\n  payByTraffic: BucketPayByTraffic;\n}\n\nexport type GetBucketPayByTrafficOutput = BucketPayByTraffic & {\n  ActiveTime: string;\n};\n\n/**\n * @private unstable method\n */\nexport async function putBucketPayByTraffic(\n  this: TOSBase,\n  input: PutBucketPayByTrafficInput\n) {\n  const res = await this.fetchBucket(\n    input.bucket,\n    'PUT',\n    { payByTraffic: '' },\n    {},\n    input.payByTraffic\n  );\n  return res;\n}\n\ninterface GetBucketPayByTrafficInput {\n  bucket: string;\n}\n/**\n * @private unstable method\n */\nexport async function getBucketPayByTraffic(\n  this: TOSBase,\n  { bucket }: GetBucketPayByTrafficInput\n): Promise<TosResponse<GetBucketPayByTrafficOutput>> {\n  const res = await this.fetchBucket<GetBucketPayByTrafficOutput>(\n    bucket,\n    'GET',\n    {\n      payByTraffic: '',\n    },\n    {}\n  );\n  return res;\n}\n", "import TOSBase, { TosResponse } from '../base';\nimport TosServerError from '../../TosServerError';\nexport interface ImageStyle {\n  Name: string;\n  Content: string;\n  CreateTime: string;\n  LastModifyTime: string;\n}\n\nexport interface BucketImgStyle {\n  bucket?: string;\n  imageStyles: { ImageStyles: ImageStyle[] };\n}\n\nexport interface ImageBriefInfo {\n  Name: string;\n  BucketLevelContent: string;\n  PrefixCount: number;\n}\n\nexport interface BucketImageBriefInfo {\n  BucketName: string;\n  ImageStyleBriefInfo: ImageBriefInfo[];\n}\n\nexport interface GetImageStyleBriefInfoInput {\n  bucket: string;\n}\n\n/**\n * @private unstable method\n */\nexport async function getImageStyleBriefInfo(\n  this: TOSBase,\n  req: GetImageStyleBriefInfoInput\n): Promise<TosResponse<BucketImageBriefInfo>> {\n  const { bucket } = req;\n  try {\n    const res = await this.fetchBucket<BucketImageBriefInfo>(\n      bucket,\n      'GET',\n      {\n        imageStyleBriefInfo: '',\n      },\n      {}\n    );\n    return res;\n  } catch (err) {\n    if (err instanceof TosServerError) {\n      if (err.statusCode === 404) {\n        return this.getNormalDataFromError(\n          {\n            BucketName: bucket,\n            ImageStyleBriefInfo: [],\n          },\n          err\n        );\n      }\n    }\n\n    throw err;\n  }\n}\n\n/**\n * @private unstable method\n */\nexport async function getBucketImageStyleList(\n  this: TOSBase,\n  bucket: string\n): Promise<TosResponse<BucketImgStyle['imageStyles']>> {\n  try {\n    const res = await this.fetchBucket<BucketImgStyle['imageStyles']>(\n      bucket,\n      'GET',\n      {\n        imageStyle: '',\n      },\n      {}\n    );\n    return res;\n  } catch (err) {\n    if (err instanceof TosServerError) {\n      if (err.statusCode === 404) {\n        return this.getNormalDataFromError(\n          {\n            ImageStyles: [],\n          },\n          err\n        );\n      }\n    }\n\n    throw err;\n  }\n}\n\nexport interface GetBucketImageStyleListByNameInput {\n  bucket: string;\n  styleName: string;\n}\n\n/**\n * @private unstable method\n */\nexport async function getBucketImageStyleListByName(\n  this: TOSBase,\n  req: GetBucketImageStyleListByNameInput\n): Promise<TosResponse<BucketImgStyle['imageStyles']>> {\n  try {\n    const { bucket, styleName } = req;\n    const res = await this.fetchBucket<BucketImgStyle['imageStyles']>(\n      bucket,\n      'GET',\n      {\n        imageStyleContent: '',\n        styleName,\n      },\n      {}\n    );\n    return res;\n  } catch (err) {\n    if (err instanceof TosServerError) {\n      if (err.statusCode === 404) {\n        return this.getNormalDataFromError(\n          {\n            ImageStyles: [],\n          },\n          err\n        );\n      }\n    }\n\n    throw err;\n  }\n}\n\n/**\n * @private unstable method\n */\nexport async function getBucketImageStyle(\n  this: TOSBase,\n  bucket: string,\n  styleName: string\n): Promise<TosResponse<ImageStyle | null>> {\n  try {\n    const res = await this.fetchBucket<ImageStyle>(\n      bucket,\n      'GET',\n      {\n        imageStyle: '',\n        styleName,\n      },\n      {}\n    );\n    return res;\n  } catch (err) {\n    if (err instanceof TosServerError) {\n      if (err.statusCode === 404) {\n        return this.getNormalDataFromError(null, err);\n      }\n    }\n\n    throw err;\n  }\n}\n\nexport interface PutBucketImageStyleInput {\n  bucket: string;\n  styleName: string;\n  content: string;\n  styleObjectPrefix?: string;\n}\n\n/**\n * @private unstable method\n */\nexport async function putBucketImageStyle(\n  this: TOSBase,\n  req: PutBucketImageStyleInput\n): Promise<TosResponse<any>> {\n  const { bucket, styleName, content, styleObjectPrefix } = req;\n  try {\n    const res = await this.fetchBucket<any>(\n      bucket,\n      'PUT',\n      {\n        imageStyle: '',\n        styleName,\n        styleObjectPrefix,\n      },\n      {},\n      { Content: content }\n    );\n    return res;\n  } catch (err) {\n    if (err instanceof TosServerError) {\n      if (err.statusCode === 404) {\n        return this.getNormalDataFromError(null, err);\n      }\n    }\n\n    throw err;\n  }\n}\n\nexport interface DeleteBucketImageStyleInput {\n  bucket: string;\n  styleName: string;\n  styleObjectPrefix?: string;\n}\n\n/**\n * @private unstable method\n */\nexport async function deleteBucketImageStyle(\n  this: TOSBase,\n  req: DeleteBucketImageStyleInput\n): Promise<TosResponse<any>> {\n  const { styleName, styleObjectPrefix, bucket } = req;\n  try {\n    const res = await this.fetchBucket<any>(\n      bucket,\n      'DELETE',\n      {\n        imageStyle: '',\n        styleName,\n        styleObjectPrefix,\n      },\n      {}\n    );\n    return res;\n  } catch (err) {\n    if (err instanceof TosServerError) {\n      if (err.statusCode === 404) {\n        return this.getNormalDataFromError(null, err);\n      }\n    }\n    throw err;\n  }\n}\n\nexport interface BucketImgProtect {\n  Enable: boolean;\n  Suffixes?: string[];\n  //原图保护规则（OSS支持10条），一期暂时不做，预留接口\n  OIPRules?: any[];\n  Prefix?: string;\n  Suffix?: string;\n}\n\n/**\n * @private unstable method\n */\nexport async function putBucketImageProtect(\n  this: TOSBase,\n  bucket: string,\n  data: BucketImgProtect\n): Promise<TosResponse<any>> {\n  try {\n    const res = await this.fetchBucket<any>(\n      bucket,\n      'PUT',\n      {\n        originalImageProtect: '',\n      },\n      {},\n      data\n    );\n    return res;\n  } catch (err) {\n    if (err instanceof TosServerError) {\n      if (err.statusCode === 404) {\n        return this.getNormalDataFromError(null, err);\n      }\n    }\n    throw err;\n  }\n}\n\n/**\n * @private unstable method\n */\nexport async function getBucketImageProtect(\n  this: TOSBase,\n  bucket: string\n): Promise<TosResponse<any>> {\n  try {\n    const res = await this.fetchBucket<any>(\n      bucket,\n      'GET',\n      {\n        originalImageProtect: '',\n      },\n      {}\n    );\n    return res;\n  } catch (err) {\n    if (err instanceof TosServerError) {\n      if (err.statusCode === 404) {\n        return this.getNormalDataFromError(null, err);\n      }\n    }\n    throw err;\n  }\n}\nexport type BucketImgProtectStyleSeparator = '-' | '_' | '!' | '\\\\';\n\nexport type BucketImgStyleSeparatorAffixes = Partial<\n  Record<BucketImgProtectStyleSeparator, string>\n>;\n\nexport interface PutBucketImageStyleSeparatorInput {\n  bucket: string;\n  Separator: BucketImgProtectStyleSeparator[];\n  SeparatorSuffix?: BucketImgStyleSeparatorAffixes;\n}\n\n/**\n * @private unstable method\n */\nexport async function putBucketImageStyleSeparator(\n  this: TOSBase,\n  req: PutBucketImageStyleSeparatorInput\n): Promise<TosResponse<any>> {\n  const { bucket, Separator, SeparatorSuffix } = req;\n  try {\n    const res = await this.fetchBucket<any>(\n      bucket,\n      'PUT',\n      {\n        imageStyleSeparator: '',\n      },\n      {},\n      { Separator, SeparatorSuffix }\n    );\n    return res;\n  } catch (err) {\n    if (err instanceof TosServerError) {\n      if (err.statusCode === 404) {\n        return this.getNormalDataFromError(null, err);\n      }\n    }\n    throw err;\n  }\n}\n\n/**\n * @private unstable method\n */\nexport async function getBucketImageStyleSeparator(\n  this: TOSBase,\n  bucket: string\n): Promise<TosResponse<any>> {\n  try {\n    const res = await this.fetchBucket<any>(\n      bucket,\n      'GET',\n      {\n        imageStyleSeparator: '',\n      },\n      {}\n    );\n    return res;\n  } catch (err) {\n    if (err instanceof TosServerError) {\n      if (err.statusCode === 404) {\n        return this.getNormalDataFromError(null, err);\n      }\n    }\n    throw err;\n  }\n}\n", "import { handleEmptyServerError } from '../../handleEmptyServerError';\nimport TOSBase, { TosResponse } from '../base';\n\nexport interface BucketIntelligenttieringOutput {\n  Status?: 'Enabled' | 'Disabled';\n  Transitions?: {\n    Days: number;\n    AccessTier: 'INFREQUENT' | 'ARCHIVEFR';\n  }[];\n}\n\nexport async function getBucketIntelligenttiering(\n  this: TOSBase,\n  bucket?: string\n): Promise<TosResponse<BucketIntelligenttieringOutput>> {\n  try {\n    const res = await this.fetchBucket<BucketIntelligenttieringOutput>(\n      bucket,\n      'GET',\n      { intelligenttiering: '' },\n      {}\n    );\n    return res;\n  } catch (error) {\n    return handleEmptyServerError<BucketIntelligenttieringOutput>(error, {\n      enableCatchEmptyServerError: this.opts.enableOptimizeMethodBehavior,\n      methodKey: 'getBucketIntelligenttiering',\n      defaultResponse: {},\n    });\n  }\n}\n", "import { convertNormalCamelCase2Upper } from '../../utils';\nimport TOSBase from '../base';\n\nconst CommonQueryKey = 'rename';\n\nexport interface PutBucketRenameInput {\n  bucket?: string;\n  renameEnable: boolean;\n}\n\nexport interface PutBucketRenameOutput {}\n\nexport async function putBucketRename(\n  this: TOSBase,\n  input: PutBucketRenameInput\n) {\n  const { bucket, ...otherProps } = input;\n\n  const body = convertNormalCamelCase2Upper(otherProps);\n  return this.fetchBucket<PutBucketRenameOutput>(\n    bucket,\n    'PUT',\n    { [CommonQueryKey]: '' },\n    {},\n    {\n      ...body,\n    }\n  );\n}\n\nexport interface GetBucketRenameInput {\n  bucket?: string;\n}\n\nexport interface GetBucketRenameOutput {\n  RenameEnable: boolean;\n}\n\nexport async function getBucketRename(\n  this: TOSBase,\n  input: GetBucketRenameInput\n) {\n  const { bucket } = input;\n  return await this.fetchBucket<GetBucketRenameOutput>(\n    bucket,\n    'GET',\n    { [CommonQueryKey]: '' },\n    {}\n  );\n}\n\nexport interface DeleteBucketRenameInput {\n  bucket?: string;\n}\n\nexport interface DeleteBucketRenameOutput {}\n\nexport async function deleteBucketRename(\n  this: TOSBase,\n  input: DeleteBucketRenameInput\n) {\n  const { bucket } = input;\n\n  return this.fetchBucket<DeleteBucketRenameOutput>(\n    bucket,\n    'DELETE',\n    { [CommonQueryKey]: '' },\n    {}\n  );\n}\n", "import { TierType } from '../../TosExportEnum';\nimport { convertNormalCamelCase2Upper } from '../../utils';\nimport TOSBase from '../base';\n\nexport interface RestoreObjectInput {\n  bucket?: string;\n  key: string;\n  versionId?: string;\n  days: number;\n  restoreJobParameters?: {\n    Tier: TierType;\n  };\n}\n\nexport async function restoreObject(this: TOSBase, input: RestoreObjectInput) {\n  const { bucket, key, versionId, ...otherProps } = input;\n  const query: Record<string, any> = { restore: '' };\n  if (versionId) {\n    query.versionId = versionId;\n  }\n  const body = convertNormalCamelCase2Upper(otherProps);\n\n  return this._fetchObject<undefined>(input, 'POST', query, {}, body);\n}\n\nexport default restoreObject;\n", "import TOSBase from '../base';\nimport { paramsSerializer } from '../../utils';\n\nexport interface IMetaData {\n  accountId: string;\n}\n\nexport interface StorageLensInput extends IMetaData {\n  Id: string;\n}\ninterface IStrategy {\n  Buckets: {\n    Bucket: string[];\n  };\n  Regions: {\n    Region: string[];\n  };\n}\n\ninterface IPrefixLevel {\n  StorageMetrics: {\n    IsEnabled: boolean;\n    SelectionCriteria: {\n      MaxDepth?: number;\n      MinStorageBytesPercentage?: number;\n      Delimiter: string;\n      Prefixes?: string[];\n    };\n  };\n}\nexport interface StorageLensConfigurationInput extends StorageLensInput {\n  Region: string;\n  IsEnabled: boolean;\n  AccountLevel: {\n    BucketLevel: {\n      /** @deprecated will be removed soon */\n      ActivityMetrics?: {\n        IsEnabled: boolean;\n      };\n      HotStatsMetrics?: {\n        IsEnabled: boolean;\n        Actions: string[]; //当前默认前端传这个，方便后期扩action\n      };\n      PrefixLevel?: IPrefixLevel;\n    };\n  };\n  DataExport?: {\n    BucketDestination?: {\n      Bucket: string;\n      Prefix?: string;\n      OutputSchemaVersion: string;\n      Format: string;\n      Role: string;\n    };\n  };\n  Include?: IStrategy;\n  Exclude?: IStrategy;\n}\nexport type StorageLensConfigurationOutput = StorageLensConfigurationInput;\n\n/**\n * @private unstable method\n * @description 获取数据透视列表\n * @param params\n * @returns\n */\nexport async function listStorageLens(this: TOSBase, params: IMetaData) {\n  const { accountId } = params;\n  const res = await this.fetch<StorageLensConfigurationOutput[]>(\n    'GET',\n    '/storagelens',\n    {},\n    {\n      'x-tos-account-id': accountId,\n    },\n    {},\n    {\n      axiosOpts: {\n        paramsSerializer,\n      },\n    }\n  );\n  return res;\n}\n\n/**\n * @private unstable method\n * @description 删除数据透视记录\n * @param params\n * @returns\n */\nexport async function deleteStorageLens(\n  this: TOSBase,\n  params: StorageLensInput\n) {\n  const { accountId, Id } = params;\n  const res = await this.fetch(\n    'DELETE',\n    `/storagelens`,\n    {\n      id: Id,\n    },\n    {\n      'x-tos-account-id': accountId,\n    },\n    {},\n    {\n      needMd5: true,\n    }\n  );\n  return res;\n}\n\n/**\n * @private unstable method\n * @description 获取数据透视详情\n * @param params\n * @returns\n */\nexport async function getStorageLens(this: TOSBase, params: StorageLensInput) {\n  const { accountId, Id } = params;\n  const res = await this.fetch<StorageLensConfigurationOutput>(\n    'GET',\n    `/storagelens`,\n    {\n      id: Id,\n    },\n    {\n      'x-tos-account-id': accountId,\n    },\n    {},\n    {\n      needMd5: true,\n    }\n  );\n  return res;\n}\n/**\n * @private unstable method\n * @description 提交数据透视记录\n * @param params\n * @returns\n */\nexport async function putStorageLens(\n  this: TOSBase,\n  params: StorageLensConfigurationInput\n) {\n  const { accountId, Id, ...rest } = params;\n\n  const res = await this.fetch(\n    'PUT',\n    `/storagelens`,\n    {\n      id: Id,\n    },\n    {\n      'x-tos-account-id': accountId,\n    },\n    {\n      ...rest,\n      Id,\n    },\n    {\n      needMd5: true,\n    }\n  );\n  return res;\n}\n", "import { convertNormalCamelCase2Upper } from '../../utils';\nimport { handleEmptyServerError } from '../../handleEmptyServerError';\nimport TOSBase from '../base';\n\nconst CommonQueryKey = 'notification_v2';\n\nexport interface NotificationFilter {\n  TOSKey?: {\n    FilterRules: {\n      Name: string;\n      Value: string;\n    }[];\n  };\n}\n\nexport interface DestinationRocketMQ {\n  Role: string;\n  InstanceId: string;\n  Topic: string;\n  AccessKeyId: string;\n}\n\nexport interface NotificationDestination {\n  RocketMQ?: DestinationRocketMQ[];\n  VeFaaS?: { FunctionId: string }[];\n}\n\nexport interface NotificationRule {\n  RuleId: string;\n  Events: string[];\n  Filter?: NotificationFilter;\n  Destination: NotificationDestination;\n}\n\nexport interface PutBucketNotificationInput {\n  bucket: string;\n  Rules: NotificationRule[];\n  Version?: string;\n}\n\nexport interface PutBucketNotificationOutput {}\n\nexport async function putBucketNotificationType2(\n  this: TOSBase,\n  input: PutBucketNotificationInput\n) {\n  const { bucket, ...otherProps } = input;\n\n  const body = convertNormalCamelCase2Upper(otherProps);\n  return this.fetchBucket<PutBucketNotificationOutput>(\n    bucket,\n    'PUT',\n    { [CommonQueryKey]: '' },\n    {},\n    {\n      ...body,\n    }\n  );\n}\n\nexport interface GetBucketNotificationInput {\n  bucket: string;\n}\n\nexport interface GetBucketNotificationOutput {\n  Rules: NotificationRule[];\n  Version?: string;\n}\n\nexport async function getBucketNotificationType2(\n  this: TOSBase,\n  input: GetBucketNotificationInput\n) {\n  const { bucket } = input;\n  try {\n    return await this.fetchBucket<GetBucketNotificationOutput>(\n      bucket,\n      'GET',\n      { [CommonQueryKey]: '' },\n      {}\n    );\n  } catch (error) {\n    return handleEmptyServerError<GetBucketNotificationOutput>(error, {\n      enableCatchEmptyServerError: this.opts.enableOptimizeMethodBehavior,\n      methodKey: 'getBucketNotificationType2',\n      defaultResponse: {\n        Rules: [],\n      },\n    });\n  }\n}\n", "import TOSBase, { TosResponse } from '../base';\nimport { fillRequestHeaders, normalizeHeadersKey } from '../../utils';\nimport { Acl } from '../../interface';\nimport { StorageClassType } from '../../TosExportEnum';\n\nexport interface PutSymInput {\n  bucket?: string;\n  key: string;\n  symLinkTargetKey: string;\n  symLinkTargetBucket?: string;\n  forbidOverwrite?: boolean;\n  acl?: Acl;\n  meta?: Record<string, string>;\n  storageClass?: StorageClassType;\n\n  headers?: {\n    'x-tos-symlink-target': string;\n    'x-tos-symlink-bucket'?: string;\n    'x-tos-forbid-overwrite'?: string;\n    'x-tos-acl'?: Acl;\n    'x-tos-storage-class'?: string;\n    [key: string]: string | undefined;\n  };\n}\n\nexport interface PutSymOutput {\n  VersionID?: string;\n}\n/**\n * @private unstable method\n */\nexport async function putSymlink(this: TOSBase, input: PutSymInput) {\n  return _putSymlink.call(this, input);\n}\n\nexport async function _putSymlink(\n  this: TOSBase,\n  input: PutSymInput\n): Promise<TosResponse<PutSymOutput>> {\n  const headers = (input.headers = normalizeHeadersKey(input.headers));\n  fillRequestHeaders(input, [\n    'symLinkTargetKey',\n    'symLinkTargetBucket',\n    'forbidOverwrite',\n    'acl',\n    'storageClass',\n    'meta',\n  ]);\n  return this._fetchObject<PutSymOutput>(\n    input,\n    'PUT',\n    { symlink: '' },\n    headers,\n    undefined,\n    {\n      handleResponse(response) {\n        const { headers } = response;\n        return {\n          VersionID: headers['x-tos-version-id'],\n        };\n      },\n    }\n  );\n}\n\nexport default putSymlink;\n", "import TOSBase, { TosResponse } from '../base';\n\nexport interface GetSymInput {\n  bucket?: string;\n  key: string;\n  versionId?: string;\n}\n\nexport interface PutSymOutput {\n  VersionID?: string;\n  SymlinkTargetKey: string;\n  SymlinkTargetBucket: string;\n  LastModified: string;\n}\n\n/**\n * @private unstable method\n */\nexport async function getSymlink(this: TOSBase, input: GetSymInput) {\n  return _getSymlink.call(this, input);\n}\n\nexport async function _getSymlink(\n  this: TOSBase,\n  input: GetSymInput\n): Promise<TosResponse<PutSymOutput>> {\n  const query: Record<string, any> = { symlink: '' };\n  if (input.versionId) {\n    query.versionId = input.versionId;\n  }\n  return this._fetchObject<PutSymOutput>(input, 'GET', query, {}, undefined, {\n    handleResponse: (res) => {\n      const { headers } = res;\n      return {\n        VersionID: headers['x-tos-version-id'],\n        SymlinkTargetKey: headers['x-tos-symlink-target'],\n        SymlinkTargetBucket: headers['x-tos-symlink-bucket'],\n        LastModified: headers['last-modified'],\n      };\n    },\n  });\n}\n\nexport default getSymlink;\n", "import { handleEmptyServerError } from '../../handleEmptyServerError';\nimport { TransferAccelerationStatusType } from '../../TosExportEnum';\nimport { convertNormalCamelCase2Upper } from '../../utils';\nimport { Headers } from '../../interface';\nimport TOSBase from '../base';\n\nconst CommonQueryKey = 'transferAcceleration';\n\nexport interface PutBucketTransferAccelerationInput {\n  bucket?: string;\n  transferAccelerationConfiguration: {\n    Enabled: 'true' | 'false';\n  };\n}\n\nexport interface PutBucketTransferAccelerationOutput {}\n\n/**\n * @private unstable\n */\nexport async function putBucketTransferAcceleration(\n  this: TOSBase,\n  input: PutBucketTransferAccelerationInput\n) {\n  const { bucket, ...otherProps } = input;\n\n  const body = convertNormalCamelCase2Upper(otherProps);\n  return this.fetchBucket<PutBucketTransferAccelerationOutput>(\n    bucket,\n    'PUT',\n    { [CommonQueryKey]: '' },\n    {},\n    {\n      ...body,\n    }\n  );\n}\n\nexport interface GetBucketTransferAccelerationInput {\n  bucket?: string;\n  getStatus?: boolean;\n}\n\nexport interface GetBucketTransferAccelerationOutput {\n  TransferAccelerationConfiguration: {\n    Enabled: 'true' | 'false';\n    Status: TransferAccelerationStatusType;\n  };\n}\n\n/**\n * @private unstable\n */\nexport async function getBucketTransferAcceleration(\n  this: TOSBase,\n  input: GetBucketTransferAccelerationInput\n) {\n  try {\n    const { bucket } = input;\n    const headers: Headers = {};\n    if (input.getStatus) {\n      headers['x-tos-get-bucket-acceleration-status'] = 'true';\n    }\n    const res = await this.fetchBucket<GetBucketTransferAccelerationOutput>(\n      bucket,\n      'GET',\n      { [CommonQueryKey]: '' },\n      headers\n    );\n    return res;\n  } catch (error) {\n    return handleEmptyServerError<GetBucketTransferAccelerationOutput>(error, {\n      enableCatchEmptyServerError: this.opts.enableOptimizeMethodBehavior,\n      methodKey: 'getBucketTransferAcceleration',\n      defaultResponse: {\n        TransferAccelerationConfiguration: {\n          Enabled: 'false',\n          Status: TransferAccelerationStatusType.Terminated,\n        },\n      },\n    });\n  }\n}\n", "import TOSBase from '../base';\nimport { GetBucketLifecycleInput } from './lifecycle';\nimport { handleEmptyServerError } from '../../handleEmptyServerError';\n\nexport interface PutBucketAccessMonitorInput {\n  bucket: string;\n  status: 'Enabled' | 'Disabled';\n}\n\nexport interface PutBucketAccessMonitorOutput {}\n\n/**\n * @private unstable method\n */\nexport async function putBucketAccessMonitor(\n  this: TOSBase,\n  input: PutBucketAccessMonitorInput\n) {\n  const { bucket, status } = input;\n\n  return this.fetchBucket<PutBucketAccessMonitorOutput>(\n    bucket,\n    'PUT',\n    { accessmonitor: '' },\n    {},\n    {\n      Status: status,\n    }\n  );\n}\n\nexport interface GetBucketAccessMonitorInput {\n  bucket: string;\n}\n\nexport interface GetBucketAccessMonitorOutput {\n  Status?: 'Enabled' | 'Disabled';\n}\n\n/**\n * @private unstable method\n */\nexport async function getBucketAccessMonitor(\n  this: TOSBase,\n  input: GetBucketLifecycleInput\n) {\n  try {\n    const { bucket } = input;\n\n    return await this.fetchBucket<GetBucketAccessMonitorOutput>(\n      bucket,\n      'GET',\n      { accessmonitor: '' },\n      {}\n    );\n  } catch (error) {\n    return handleEmptyServerError<GetBucketAccessMonitorOutput>(error, {\n      enableCatchEmptyServerError: this.opts.enableOptimizeMethodBehavior,\n      methodKey: 'getBucketAccessMonitor',\n      defaultResponse: {},\n    });\n  }\n}\n", "import TOSBase from '../base';\nimport { AccessPointStatusType } from '../../TosExportEnum';\n\nexport interface AccessPoint {\n  Name: string; //规则名\n  Alias?: string; //别名\n  Status: AccessPointStatusType; //状态;\n  CreatedAt: number;\n  Regions: Array<{\n    Bucket: string;\n    BucketAccountId: number;\n    Region: string;\n  }>;\n}\n\nexport interface CreateMultiRegionAccessPointInput {\n  name: string;\n  regions: Array<{\n    Bucket: string;\n    BucketAccountId: string;\n  }>;\n  accountId: string;\n}\n\nexport interface CreateMultiRegionAccessPointOutput {}\n\n/**\n * @private unstable method\n */\nexport async function createMultiRegionAccessPoint(\n  this: TOSBase,\n  input: CreateMultiRegionAccessPointInput\n) {\n  const { accountId, name, regions } = input;\n\n  const res = await this.fetch<CreateMultiRegionAccessPointOutput>(\n    'POST',\n    '/mrap',\n    {\n      name,\n    },\n    {\n      'x-tos-account-id': accountId,\n    },\n    {\n      Name: name,\n      Regions: regions,\n    },\n    {}\n  );\n\n  return res;\n}\n\nexport interface GetMultiRegionAccessPointInput {\n  name: string;\n  accountId: string;\n}\n\nexport interface GetMultiRegionAccessPointOutput extends AccessPoint {}\n\n/**\n * @private unstable method\n */\nexport async function getMultiRegionAccessPoint(\n  this: TOSBase,\n  input: GetMultiRegionAccessPointInput\n) {\n  const { name, accountId } = input;\n  const res = await this.fetch<GetMultiRegionAccessPointOutput>(\n    'GET',\n    '/mrap',\n    {\n      name,\n    },\n    {\n      'x-tos-account-id': accountId,\n    },\n    {},\n    {}\n  );\n\n  return res;\n}\n\nexport interface ListMultiRegionAccessPointsInput {\n  accountId: string;\n  maxResults?: number;\n  nextToken?: string;\n}\n\nexport interface ListMultiRegionAccessPointsOutput {\n  AccessPoints: Array<AccessPoint>;\n  NextToken?: string;\n}\n\n/**\n * @private unstable method\n */\nexport async function listMultiRegionAccessPoints(\n  this: TOSBase,\n  input: ListMultiRegionAccessPointsInput\n) {\n  const { accountId, ...nextQuery } = input;\n  const res = await this.fetch<ListMultiRegionAccessPointsOutput>(\n    'GET',\n    '/mrap',\n    { ...nextQuery },\n    {\n      'x-tos-account-id': accountId,\n    },\n    {},\n    {}\n  );\n\n  return res;\n}\n\nexport interface GetMultiRegionAccessPointRoutesInput {\n  accountId: string;\n  alias: string;\n}\n\nexport interface AccessPointRoute {\n  Bucket: string;\n  Region: string;\n  TrafficDialPercentage: number;\n}\n\nexport interface GetMultiRegionAccessPointRoutesOutput {\n  Routes?: AccessPointRoute[];\n  Alias?: string;\n}\n\n/**\n * @private unstable method\n */\nexport async function getMultiRegionAccessPointRoutes(\n  this: TOSBase,\n  input: GetMultiRegionAccessPointRoutesInput\n) {\n  const { accountId, alias } = input;\n  const res = await this.fetch<GetMultiRegionAccessPointRoutesOutput>(\n    'GET',\n    '/mrap/routes',\n    {\n      alias,\n    },\n    {\n      'x-tos-account-id': accountId,\n    }\n  );\n  return res;\n}\n\nexport interface DeleteMultiRegionAccessPointInput {\n  accountId: string;\n  name: string;\n}\n\nexport interface DeleteMultiRegionAccessPointOutput {}\n\nexport async function deleteMultiRegionAccessPoint(\n  this: TOSBase,\n  input: DeleteMultiRegionAccessPointInput\n) {\n  const { name, accountId } = input;\n  const res = await this.fetch<DeleteMultiRegionAccessPointOutput>(\n    'DELETE',\n    '/mrap',\n    {\n      name,\n    },\n    {\n      'x-tos-account-id': accountId,\n    }\n  );\n  return res;\n}\n\nexport interface SubmitMultiRegionAccessPointRoutesInput {\n  accountId: string;\n  alias: string;\n  routes: AccessPointRoute[];\n}\n\nexport interface SubmitMultiRegionAccessPointRoutesOutput {}\n\nexport async function submitMultiRegionAccessPointRoutes(\n  this: TOSBase,\n  input: SubmitMultiRegionAccessPointRoutesInput\n) {\n  const { routes, accountId, alias } = input;\n  const res = await this.fetch<SubmitMultiRegionAccessPointRoutesOutput>(\n    'PATCH',\n    '/mrap/routes',\n    {\n      alias,\n    },\n    {\n      'x-tos-account-id': accountId,\n    },\n    {\n      Routes: routes,\n    }\n  );\n  return res;\n}\n", "import TOSBase from '../base';\nimport { MRAPMirrorBackRedirectPolicyType } from '../../TosExportEnum';\nimport { makeArrayProp } from '../../utils';\nimport { handleEmptyServerError } from '../../handleEmptyServerError';\n\nexport interface MirrorBackRule {\n  ID?: string;\n  Status: 'Enabled' | 'Disabled';\n  Condition?: {\n    HttpCode?: number[];\n    KeyPrefix?: string[];\n  };\n  Redirect?: {\n    RedirectPolicy: MRAPMirrorBackRedirectPolicyType;\n  };\n}\n\nexport interface PutMultiRegionAccessPointMirrorBackInput {\n  accountId: string;\n  alias: string;\n  rules: MirrorBackRule[];\n}\n\nexport interface PutMultiRegionAccessPointMirrorBackOutput {}\n\n/**\n * @private unstable method\n */\nexport const putMultiRegionAccessPointMirrorBack = async function (\n  this: TOSBase,\n  input: PutMultiRegionAccessPointMirrorBackInput\n) {\n  const { accountId, alias, rules } = input;\n\n  if (this.opts.enableOptimizeMethodBehavior && !rules.length) {\n    return deleteMultiRegionAccessPointMirrorBack.call(this, {\n      accountId,\n      alias,\n    });\n  }\n\n  const res = await this.fetch<PutMultiRegionAccessPointMirrorBackOutput>(\n    'PUT',\n    '/mrap/mirror',\n    {\n      alias,\n    },\n    {\n      'x-tos-account-id': accountId,\n    },\n    {\n      Rules: rules,\n    },\n    {\n      handleResponse() {\n        return {};\n      },\n    }\n  );\n  return res;\n};\n\nexport interface GetMultiRegionAccessPointMirrorBackInput {\n  accountId: string;\n  alias: string;\n}\n\nexport interface GetMultiRegionAccessPointMirrorBackOutput {\n  Rules: MirrorBackRule[];\n}\n\n/**\n * @private unstable method\n */\nexport const getMultiRegionAccessPointMirrorBack = async function (\n  this: TOSBase,\n  input: GetMultiRegionAccessPointMirrorBackInput\n) {\n  const { accountId, alias } = input;\n  try {\n    const res = await this.fetch<GetMultiRegionAccessPointMirrorBackOutput>(\n      'GET',\n      '/mrap/mirror',\n      {\n        alias,\n      },\n      {\n        'x-tos-account-id': accountId,\n      },\n      {},\n      {}\n    );\n    const arrayProp = makeArrayProp(res.data);\n    arrayProp('Rules');\n    return res;\n  } catch (error) {\n    return handleEmptyServerError<GetMultiRegionAccessPointMirrorBackOutput>(\n      error,\n      {\n        enableCatchEmptyServerError: this.opts.enableOptimizeMethodBehavior,\n        methodKey: 'getMultiRegionAccessPointMirrorBack',\n        defaultResponse: {\n          Rules: [],\n        },\n      }\n    );\n  }\n};\n\nexport interface DeleteMultiRegionAccessPointMirrorBackInput {\n  accountId: string;\n  alias: string;\n}\nexport interface DeleteMultiRegionAccessPointMirrorBackOutput {}\n\n/**\n * @private unstable method\n */\nexport const deleteMultiRegionAccessPointMirrorBack = async function (\n  this: TOSBase,\n  input: DeleteMultiRegionAccessPointMirrorBackInput\n) {\n  const { accountId, alias } = input;\n  const res = await this.fetch<DeleteMultiRegionAccessPointMirrorBackOutput>(\n    'DELETE',\n    '/mrap/mirror',\n    {\n      alias,\n    },\n    {\n      'x-tos-account-id': accountId,\n    },\n    {},\n    {\n      handleResponse() {\n        return {};\n      },\n    }\n  );\n  return res;\n};\n", "import TOSBase from '../base';\nimport { handleEmptyServerError } from '../../handleEmptyServerError';\n\nexport interface PutBucketPrivateM3U8Input {\n  bucket: string;\n  enable: boolean;\n}\n\nexport interface PutPrivateM3U8Output {}\n\n/**\n * @private unstable\n */\nexport async function putBucketPrivateM3U8(\n  this: TOSBase,\n  input: PutBucketPrivateM3U8Input\n) {\n  const { bucket, enable } = input;\n  return await this.fetchBucket(\n    bucket,\n    'PUT',\n    {\n      privateM3U8: '',\n    },\n    {},\n    {\n      Enable: enable,\n    }\n  );\n}\n\nexport interface GetBucketPrivateM3U8Input {\n  bucket: string;\n}\n\nexport interface GetBucketPrivateM3U8Output {\n  Enable: boolean;\n}\n\n/**\n * @private unstable\n */\nexport async function getBucketPrivateM3U8(\n  this: TOSBase,\n  input: GetBucketPrivateM3U8Input\n) {\n  const { bucket } = input;\n  try {\n    return await this.fetchBucket<GetBucketPrivateM3U8Output>(\n      bucket,\n      'GET',\n      {\n        privateM3U8: '',\n      },\n      {}\n    );\n  } catch (error) {\n    return handleEmptyServerError(error, {\n      enableCatchEmptyServerError: this.opts.enableOptimizeMethodBehavior,\n      methodKey: 'getBucketPrivateM3U8',\n      defaultResponse: {\n        Enable: false,\n      },\n    });\n  }\n}\n", "import { convertNormalCamelCase2Upper } from '../../utils';\nimport TOSBase from '../base';\n\nconst CommonQueryKey = 'trash';\n\nexport interface PutBucketTrashInput {\n  bucket?: string;\n  Trash: {\n    TrashPath: string;\n    CleanInterval: number;\n    Status: 'Enabled' | 'Disabled';\n  };\n}\n\nexport interface PutBucketTrashOutput {}\n\nexport async function putBucketTrash(\n  this: TOSBase,\n  input: PutBucketTrashInput\n) {\n  const { bucket, ...otherProps } = input;\n\n  const body = convertNormalCamelCase2Upper(otherProps);\n  return this.fetchBucket<PutBucketTrashOutput>(\n    bucket,\n    'PUT',\n    { [CommonQueryKey]: '' },\n    {},\n    {\n      ...body,\n    }\n  );\n}\n\nexport interface GetBucketTrashInput {\n  bucket?: string;\n}\n\nexport interface GetBucketTrashOutput {\n  Trash: {\n    TrashPath: string;\n    CleanInterval: number;\n    Status: 'Enabled' | 'Disabled';\n  };\n}\n\nexport async function getBucketTrash(\n  this: TOSBase,\n  input: GetBucketTrashInput\n) {\n  const { bucket } = input;\n  return await this.fetchBucket<GetBucketTrashOutput>(\n    bucket,\n    'GET',\n    { [CommonQueryKey]: '' },\n    {}\n  );\n}\n", "import TOSBase from './methods/base';\nimport {\n  listBuckets,\n  createBucket,\n  deleteBucket,\n  headBucket,\n  putBucketStorageClass,\n} from './methods/bucket/base';\nimport { getBucketAcl, putBucketAcl } from './methods/bucket/acl';\nimport {\n  getObject,\n  getObjectV2,\n  getObjectToFile,\n} from './methods/object/getObject';\nimport putObject, { putObjectFromFile } from './methods/object/putObject';\nimport { fetchObject, putFetchTask } from './methods/object/fetch';\nimport { listObjectVersions, listObjects } from './methods/object/listObjects';\nimport getPreSignedUrl from './methods/object/getPreSignedUrl';\nimport headObject from './methods/object/headObject';\nimport deleteObject from './methods/object/deleteObject';\nimport renameObject from './methods/object/renameObject';\nimport deleteMultiObjects from './methods/object/deleteMultiObjects';\nimport copyObject from './methods/object/copyObject';\nimport { getObjectAcl, putObjectAcl } from './methods/object/acl';\nimport {\n  abortMultipartUpload,\n  completeMultipartUpload,\n  createMultipartUpload,\n  listParts,\n  uploadPart,\n  listMultipartUploads,\n  uploadPartFromFile,\n} from './methods/object/multipart';\nimport appendObject from './methods/object/appendObject';\nimport setObjectMeta from './methods/object/setObjectMeta';\nimport { uploadPartCopy } from './methods/object/multipart/uploadPartCopy';\nimport uploadFile from './methods/object/multipart/uploadFile';\nimport { calculatePostSignature } from './methods/object/calculatePostSignature';\n\nimport { resumableCopyObject } from './methods/object/multipart/resumableCopyObject';\nimport {\n  deleteBucketPolicy,\n  getBucketPolicy,\n  putBucketPolicy,\n} from './methods/bucket/policy';\nimport {\n  getBucketVersioning,\n  putBucketVersioning,\n} from './methods/bucket/versioning';\nimport { preSignedPolicyURL } from './methods/object/preSignedPolicyURL';\nimport downloadFile from './methods/object/downloadFile';\nimport { getBucketLocation } from './methods/bucket/getBucketLocation';\nimport {\n  deleteBucketCORS,\n  getBucketCORS,\n  putBucketCORS,\n} from './methods/bucket/cors';\nimport { listObjectsType2 } from './methods/object/listObjectsType2';\nimport {\n  deleteBucketLifecycle,\n  getBucketLifecycle,\n  putBucketLifecycle,\n} from './methods/bucket/lifecycle';\nimport {\n  putBucketEncryption,\n  getBucketEncryption,\n  deleteBucketEncryption,\n} from './methods/bucket/encryption';\nimport {\n  deleteBucketMirrorBack,\n  getBucketMirrorBack,\n  putBucketMirrorBack,\n} from './methods/bucket/mirrorback';\nimport {\n  deleteObjectTagging,\n  getObjectTagging,\n  putObjectTagging,\n} from './methods/object/tagging';\nimport {\n  deleteBucketReplication,\n  getBucketReplication,\n  putBucketReplication,\n} from './methods/bucket/replication';\nimport {\n  deleteBucketWebsite,\n  getBucketWebsite,\n  putBucketWebsite,\n} from './methods/bucket/website';\nimport {\n  getBucketNotification,\n  putBucketNotification,\n} from './methods/bucket/notification';\nimport {\n  deleteBucketCustomDomain,\n  getBucketCustomDomain,\n  putBucketCustomDomain,\n} from './methods/bucket/customDomain';\nimport {\n  deleteBucketRealTimeLog,\n  getBucketRealTimeLog,\n  putBucketRealTimeLog,\n} from './methods/bucket/realTimeLog';\nimport {\n  deleteBucketInventory,\n  getBucketInventory,\n  listBucketInventory,\n  putBucketInventory,\n} from './methods/bucket/inventory';\nimport {\n  createJob,\n  deleteJob,\n  describeJob,\n  updateJobStatus,\n  updateJobPriority,\n  listJobs,\n} from './methods/batch';\nimport {\n  deleteBucketTagging,\n  getBucketTagging,\n  putBucketTagging,\n} from './methods/bucket/tag';\nimport {\n  getBucketPayByTraffic,\n  putBucketPayByTraffic,\n} from './methods/bucket/payByTraffic';\nimport {\n  getBucketImageStyle,\n  getBucketImageStyleList,\n  getBucketImageStyleListByName,\n  getImageStyleBriefInfo,\n  deleteBucketImageStyle,\n  putBucketImageStyle,\n  putBucketImageStyleSeparator,\n  putBucketImageProtect,\n  getBucketImageProtect,\n  getBucketImageStyleSeparator,\n} from './methods/bucket/img';\nimport { getBucketIntelligenttiering } from './methods/bucket/intelligenttiering';\nimport {\n  putBucketRename,\n  getBucketRename,\n  deleteBucketRename,\n} from './methods/bucket/rename';\nimport restoreObject from './methods/object/restoreObject';\nimport {\n  deleteStorageLens,\n  getStorageLens,\n  listStorageLens,\n  putStorageLens,\n} from './methods/storageLens';\nimport {\n  putBucketNotificationType2,\n  getBucketNotificationType2,\n} from './methods/bucket/notificationType2';\n\nimport putSymlink from './methods/object/putSymlink';\nimport getSymlink from './methods/object/getSymlink';\nimport {\n  getBucketTransferAcceleration,\n  putBucketTransferAcceleration,\n} from './methods/bucket/acceleration';\nimport {\n  getBucketAccessMonitor,\n  putBucketAccessMonitor,\n} from './methods/bucket/accessMonitor';\nimport {\n  getQosPolicy,\n  putQosPolicy,\n  deleteQosPolicy,\n} from './methods/qosPolicy';\nimport {\n  createMultiRegionAccessPoint,\n  getMultiRegionAccessPoint,\n  listMultiRegionAccessPoints,\n  getMultiRegionAccessPointRoutes,\n  deleteMultiRegionAccessPoint,\n  submitMultiRegionAccessPointRoutes,\n} from './methods/mrap';\nimport {\n  putMultiRegionAccessPointMirrorBack,\n  getMultiRegionAccessPointMirrorBack,\n  deleteMultiRegionAccessPointMirrorBack,\n} from './methods/mrap/mirror';\nimport {\n  putBucketPrivateM3U8,\n  getBucketPrivateM3U8,\n} from './methods/bucket/media';\nimport { getBucketTrash, putBucketTrash } from './methods/bucket/trash';\n\n// refer https://stackoverflow.com/questions/23876782/how-do-i-split-a-typescript-class-into-multiple-files\nexport class InnerClient extends TOSBase {\n  // bucket base\n  createBucket = createBucket;\n  headBucket = headBucket;\n  deleteBucket = deleteBucket;\n  listBuckets = listBuckets;\n  getBucketLocation = getBucketLocation;\n  putBucketStorageClass = putBucketStorageClass;\n\n  // bucket acl\n  getBucketAcl = getBucketAcl;\n  putBucketAcl = putBucketAcl;\n\n  // bucket policy\n  getBucketPolicy = getBucketPolicy;\n  putBucketPolicy = putBucketPolicy;\n  deleteBucketPolicy = deleteBucketPolicy;\n\n  // bucket versioning\n  getBucketVersioning = getBucketVersioning;\n  putBucketVersioning = putBucketVersioning;\n\n  // bucket cors\n  getBucketCORS = getBucketCORS;\n  putBucketCORS = putBucketCORS;\n  deleteBucketCORS = deleteBucketCORS;\n\n  // bucket lifecycle\n  putBucketLifecycle = putBucketLifecycle;\n  getBucketLifecycle = getBucketLifecycle;\n  deleteBucketLifecycle = deleteBucketLifecycle;\n\n  //bucket encryption\n  putBucketEncryption = putBucketEncryption;\n  getBucketEncryption = getBucketEncryption;\n  deleteBucketEncryption = deleteBucketEncryption;\n\n  // bucket mirror back\n  putBucketMirrorBack = putBucketMirrorBack;\n  getBucketMirrorBack = getBucketMirrorBack;\n  deleteBucketMirrorBack = deleteBucketMirrorBack;\n\n  // bucket replication\n  putBucketReplication = putBucketReplication;\n  getBucketReplication = getBucketReplication;\n  deleteBucketReplication = deleteBucketReplication;\n\n  // bucket website\n  putBucketWebsite = putBucketWebsite;\n  getBucketWebsite = getBucketWebsite;\n  deleteBucketWebsite = deleteBucketWebsite;\n\n  // bucket notification\n  putBucketNotification = putBucketNotification;\n  getBucketNotification = getBucketNotification;\n\n  // bucket customdomain\n  putBucketCustomDomain = putBucketCustomDomain;\n  getBucketCustomDomain = getBucketCustomDomain;\n  deleteBucketCustomDomain = deleteBucketCustomDomain;\n\n  // bucket timelog\n  putBucketRealTimeLog = putBucketRealTimeLog;\n  getBucketRealTimeLog = getBucketRealTimeLog;\n  deleteBucketRealTimeLog = deleteBucketRealTimeLog;\n\n  // bucket Inventory\n  getBucketInventory = getBucketInventory;\n  listBucketInventory = listBucketInventory;\n  putBucketInventory = putBucketInventory;\n  deleteBucketInventory = deleteBucketInventory;\n\n  // bucket tag\n  putBucketTagging = putBucketTagging;\n  getBucketTagging = getBucketTagging;\n  deleteBucketTagging = deleteBucketTagging;\n\n  // bucket pay by traffic\n  putBucketPayByTraffic = putBucketPayByTraffic;\n  getBucketPayByTraffic = getBucketPayByTraffic;\n\n  // bucket imgStyle\n  getBucketImageStyle = getBucketImageStyle;\n  getBucketImageStyleList = getBucketImageStyleList;\n  getBucketImageStyleListByName = getBucketImageStyleListByName;\n  getImageStyleBriefInfo = getImageStyleBriefInfo;\n  deleteBucketImageStyle = deleteBucketImageStyle;\n  putBucketImageStyle = putBucketImageStyle;\n  putBucketImageStyleSeparator = putBucketImageStyleSeparator;\n  putBucketImageProtect = putBucketImageProtect;\n  getBucketImageProtect = getBucketImageProtect;\n  getBucketImageStyleSeparator = getBucketImageStyleSeparator;\n\n  // bucket tag\n  putBucketRename = putBucketRename;\n  getBucketRename = getBucketRename;\n  deleteBucketRename = deleteBucketRename;\n\n  // bucket acceleration\n  putBucketTransferAcceleration = putBucketTransferAcceleration;\n  getBucketTransferAcceleration = getBucketTransferAcceleration;\n\n  // object base\n  copyObject = copyObject;\n  resumableCopyObject = resumableCopyObject;\n  deleteObject = deleteObject;\n  deleteMultiObjects = deleteMultiObjects;\n  getObject = getObject;\n  getObjectV2 = getObjectV2;\n  getObjectToFile = getObjectToFile;\n  getObjectAcl = getObjectAcl;\n  headObject = headObject;\n  appendObject = appendObject;\n  listObjects = listObjects;\n  renameObject = renameObject;\n  fetchObject = fetchObject;\n  putFetchTask = putFetchTask;\n\n  listObjectsType2 = listObjectsType2;\n\n  listObjectVersions = listObjectVersions;\n  putObject = putObject;\n  putObjectFromFile = putObjectFromFile;\n  putObjectAcl = putObjectAcl;\n  setObjectMeta = setObjectMeta;\n\n  // object multipart\n  createMultipartUpload = createMultipartUpload;\n  uploadPart = uploadPart;\n  uploadPartFromFile = uploadPartFromFile;\n  completeMultipartUpload = completeMultipartUpload;\n  abortMultipartUpload = abortMultipartUpload;\n  uploadPartCopy = uploadPartCopy;\n  listMultipartUploads = listMultipartUploads;\n  listParts = listParts;\n  downloadFile = downloadFile;\n\n  // object tagging\n  putObjectTagging = putObjectTagging;\n  getObjectTagging = getObjectTagging;\n  deleteObjectTagging = deleteObjectTagging;\n\n  // batch job\n  listJobs = listJobs;\n  createJob = createJob;\n  deleteJob = deleteJob;\n  describeJob = describeJob;\n  updateJobStatus = updateJobStatus;\n  updateJobPriority = updateJobPriority;\n\n  // restore object\n  restoreObject = restoreObject;\n  // object others\n  uploadFile = uploadFile;\n  getPreSignedUrl = getPreSignedUrl;\n  /**\n   * alias to preSignedPostSignature\n   */\n  calculatePostSignature = calculatePostSignature;\n  preSignedPostSignature = calculatePostSignature;\n  preSignedPolicyURL = preSignedPolicyURL;\n  // object intelligenttiering\n  getBucketIntelligenttiering = getBucketIntelligenttiering;\n\n  // storageLens\n  listStorageLens = listStorageLens;\n  deleteStorageLens = deleteStorageLens;\n  getStorageLens = getStorageLens;\n  putStorageLens = putStorageLens;\n\n  // bucket notificationV2\n  putBucketNotificationType2 = putBucketNotificationType2;\n  getBucketNotificationType2 = getBucketNotificationType2;\n\n  // symlink\n  putSymlink = putSymlink;\n  getSymlink = getSymlink;\n\n  // bucket accessMonitor\n  putBucketAccessMonitor = putBucketAccessMonitor;\n  getBucketAccessMonitor = getBucketAccessMonitor;\n\n  // qospolicy\n  putQosPolicy = putQosPolicy;\n  getQosPolicy = getQosPolicy;\n  deleteQosPolicy = deleteQosPolicy;\n\n  // mrap\n  createMultiRegionAccessPoint = createMultiRegionAccessPoint;\n  getMultiRegionAccessPoint = getMultiRegionAccessPoint;\n  listMultiRegionAccessPoints = listMultiRegionAccessPoints;\n  getMultiRegionAccessPointRoutes = getMultiRegionAccessPointRoutes;\n  deleteMultiRegionAccessPoint = deleteMultiRegionAccessPoint;\n  submitMultiRegionAccessPointRoutes = submitMultiRegionAccessPointRoutes;\n\n  // mrap mirror back\n  putMultiRegionAccessPointMirrorBack = putMultiRegionAccessPointMirrorBack;\n  getMultiRegionAccessPointMirrorBack = getMultiRegionAccessPointMirrorBack;\n  deleteMultiRegionAccessPointMirrorBack =\n    deleteMultiRegionAccessPointMirrorBack;\n\n  // pm3u8\n  putBucketPrivateM3U8 = putBucketPrivateM3U8;\n  getBucketPrivateM3U8 = getBucketPrivateM3U8;\n  // hns trash\n  putBucketTrash = putBucketTrash;\n  getBucketTrash = getBucketTrash;\n}\n", "import axios from 'axios';\nimport { TosServerError, TosServerCode } from './TosServerError';\nimport { TosClientError } from './TosClientError';\nimport { isCancelError as isCancel } from './utils';\nimport { UploadEventType } from './methods/object/multipart/uploadFile';\nimport {\n  ACLType,\n  StorageClassType,\n  MetadataDirectiveType,\n  AzRedundancyType,\n  PermissionType,\n  GranteeType,\n  CannedType,\n  HttpMethodType,\n  LifecycleStatusType,\n  StatusType,\n  RedirectType,\n  StorageClassInheritDirectiveType,\n  TierType,\n  VersioningStatusType,\n  ReplicationStatusType,\n  AccessPointStatusType,\n  TransferAccelerationStatusType,\n  MRAPMirrorBackRedirectPolicyType\n} from './TosExportEnum';\nimport { CancelError } from './CancelError';\nimport { ResumableCopyEventType } from './methods/object/multipart/resumableCopyObject';\nimport { DownloadEventType } from './methods/object/downloadFile';\nimport { DataTransferType } from './interface';\nimport { ShareLinkClient } from './ShareLinkClient';\nimport { InnerClient } from './InnerClient';\nimport { createDefaultRateLimiter } from './universal/rate-limiter';\n\nconst CancelToken = axios.CancelToken;\n// for export\nclass TosClient extends InnerClient {\n  // for umd bundle\n  static TosServerError = TosServerError;\n  static isCancel = isCancel;\n  static CancelError = CancelError;\n  static TosServerCode = TosServerCode;\n  static TosClientError = TosClientError;\n  static CancelToken = CancelToken;\n  static ACLType = ACLType;\n  static StorageClassType = StorageClassType;\n  static MetadataDirectiveType = MetadataDirectiveType;\n  static AzRedundancyType = AzRedundancyType;\n  static PermissionType = PermissionType;\n  static GranteeType = GranteeType;\n  static CannedType = CannedType;\n  static HttpMethodType = HttpMethodType;\n  static LifecycleStatusType = LifecycleStatusType;\n  static StatusType = StatusType;\n  static RedirectType = RedirectType;\n  static StorageClassInheritDirectiveType = StorageClassInheritDirectiveType;\n  static TierType = TierType;\n  static VersioningStatusType = VersioningStatusType;\n  static createDefaultRateLimiter = createDefaultRateLimiter;\n  static DataTransferType = DataTransferType;\n  static UploadEventType = UploadEventType;\n  static DownloadEventType = DownloadEventType;\n  static ResumableCopyEventType = ResumableCopyEventType;\n  static ReplicationStatusType = ReplicationStatusType;\n  /** @private unstable */\n  static AccessPointStatusType = AccessPointStatusType;\n  /** @private unstable */\n  static TransferAccelerationStatusType = TransferAccelerationStatusType;\n  /** @private unstable */\n  static MRAPMirrorBackRedirectPolicyType = MRAPMirrorBackRedirectPolicyType;\n  /** @private unstable */\n  static ShareLinkClient = ShareLinkClient;\n}\n\nexport default TosClient;\n\nexport { TosClient as TOS, TosClient };\nexport {\n  TosServerError,\n  TosClientError,\n  isCancel,\n  CancelError,\n  TosServerCode,\n  CancelToken,\n  ACLType,\n  StorageClassType,\n  MetadataDirectiveType,\n  AzRedundancyType,\n  PermissionType,\n  GranteeType,\n  CannedType,\n  HttpMethodType,\n  LifecycleStatusType,\n  RedirectType,\n  StatusType,\n  StorageClassInheritDirectiveType,\n  TierType,\n  VersioningStatusType,\n  createDefaultRateLimiter,\n  DataTransferType,\n  UploadEventType,\n  DownloadEventType,\n  ResumableCopyEventType,\n  ReplicationStatusType,\n  AccessPointStatusType,\n  TransferAccelerationStatusType,\n  ShareLinkClient,\n  MRAPMirrorBackRedirectPolicyType\n};\n\n// TODO: hack for umd\nif (\n  process.env.TARGET_ENVIRONMENT === 'browser' &&\n  process.env.BUILD_FORMAT === 'umd'\n) {\n  // @ts-ignore\n  if (typeof window !== 'undefined') {\n    // @ts-ignore\n    window.TOS = TosClient;\n    // @ts-ignore\n    window.TosClient = TosClient;\n  }\n  if (typeof global !== 'undefined') {\n    // @ts-ignore\n    global.TOS = TosClient;\n    // @ts-ignore\n    global.TosClient = TosClient;\n  }\n  if (typeof globalThis !== 'undefined') {\n    // @ts-ignore\n    globalThis.TOS = TosClient;\n    // @ts-ignore\n    globalThis.TosClient = TosClient;\n  }\n}\n"], "names": ["TosServerCode", "TosServerError", "Error", "constructor", "response", "data", "super", "Message", "code", "statusCode", "headers", "requestId", "id2", "Object", "setPrototypeOf", "this", "prototype", "Code", "status", "TosClientError", "message", "CancelError", "createWriteStream", "fs", "createReadStream", "rename", "promisify", "stat", "writeFile", "rm", "unlink", "readFile", "safeMkdirRecursive", "async", "fsPromises", "access", "<PERSON><PERSON><PERSON>", "e", "mkdir", "recursive", "makeArrayProp", "obj", "key", "value", "get", "Array", "isArray", "set", "makeConvertProp", "convertMethod", "final<PERSON><PERSON><PERSON>", "target", "map", "it", "keys", "reduce", "acc", "covertCamelCase2Kebab", "camelCase", "replace", "toLowerCase", "convertNormalCamelCase2Upper", "normalCamelCase", "toUpperCase", "slice", "getSortedQueryString", "query", "searchParts", "sort", "for<PERSON>ach", "push", "encodeURIComponent", "join", "normalizeHeadersKey", "headers1", "headers2", "headers3", "new<PERSON>ey", "normalizeProxy", "proxy", "url", "safeAwait", "p", "err", "isBlob", "Blob", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "isReadable", "Readable", "obj2QueryStr", "v", "vStr", "isCancelError", "gmtDateOrStr", "toUTCString", "requestHeadersMap", "projectName", "encodingType", "cacheControl", "contentDisposition", "contentLength", "contentMD5", "contentSHA256", "contentEncoding", "contentLanguage", "contentType", "expires", "range", "ifMatch", "ifModifiedSince", "ifNoneMatch", "ifUnmodifiedSince", "acl", "grantFullControl", "grantRead", "grantReadAcp", "grantWrite", "grantWriteAcp", "serverSideEncryption", "serverSideDataEncryption", "ssecAlgorithm", "ssecKey", "ssecKeyMD5", "copySourceRange", "copySourceIfMatch", "copySourceIfModifiedSince", "copySourceIfNoneMatch", "copySourceIfUnmodifiedSince", "copySourceSSECAlgorithm", "copySourceSSECKey", "copySourceSSECKeyMD5", "metadataDirective", "meta", "prev", "websiteRedirectLocation", "storageClass", "azRedundancy", "trafficLimit", "callback", "callback<PERSON><PERSON>", "allowSameActionOverlap", "String", "symLinkTargetKey", "symLinkTargetBucket", "forbidOverwrite", "bucketType", "recursiveMkdir", "requestQueryMap", "versionId", "process", "saveBucket", "saveObject", "responseCacheControl", "responseContentDisposition", "responseContentEncoding", "responseContentLanguage", "responseContentType", "responseExpires", "fillRequestHeaders", "length", "set<PERSON>neHeader", "k", "confV", "oriValue", "entries", "paramsSerializer", "params", "qs", "stringify", "getNormalDataFromError", "checkCRC64WithHeaders", "crc", "serverCRC64", "crcStr", "getCrc64", "HttpHeader", "makeSerialAsyncTask", "makeTask", "lastTask", "Promise", "resolve", "then", "safeParseCheckpointFile", "JSON", "parse", "fsp", "filePath", "console", "warn", "makeRetryStreamAutoClose", "makeStream", "lastStream", "getLastStream", "make", "try<PERSON><PERSON><PERSON>", "stream", "destroy", "destroyed", "pipeStreamWithErrorHandle", "src", "dest", "label", "prefix", "on", "log", "pipe", "createMultipartUpload", "input", "normalizeObjectInput", "setObjectContentTypeHeader", "_fetchObject", "uploads", "calculateSafePartSize", "totalSize", "expectPartSize", "showWarning", "partSize", "minSize", "Math", "ceil", "listParts", "uploadId", "<PERSON><PERSON><PERSON><PERSON>", "ret", "arrayProp", "mimeTypes", "3gp", "7z", "abw", "ai", "aif", "aifc", "aiff", "alc", "amr", "anx", "apk", "appcache", "art", "asc", "asf", "aso", "asx", "atom", "atomcat", "atomsrv", "au", "avi", "awb", "axa", "axv", "b", "bak", "bat", "bcpio", "bib", "bin", "bmp", "boo", "book", "brf", "bsd", "c", "c++", "c3d", "cab", "cac", "cache", "cap", "cascii", "cat", "cbin", "cbr", "cbz", "cc", "cda", "cdf", "cdr", "cdt", "cdx", "cdy", "cef", "cer", "chm", "chrt", "cif", "class", "cls", "cmdf", "cml", "cod", "com", "cpa", "cpio", "cpp", "cpt", "cr2", "crl", "crt", "crw", "csd", "csf", "csh", "csm", "csml", "css", "csv", "ctab", "ctx", "cu", "cub", "cxf", "cxx", "d", "dav<PERSON>", "dcm", "dcr", "ddeb", "dif", "diff", "dir", "djv", "djvu", "dl", "dll", "dmg", "dms", "doc", "docm", "docx", "dot", "dotm", "dotx", "dv", "dvi", "dx", "dxr", "emb", "embl", "eml", "eot", "eps", "eps2", "eps3", "epsf", "epsi", "erf", "es", "etx", "exe", "ez", "fb", "fbdoc", "fch", "fchk", "fig", "flac", "fli", "flv", "fm", "frame", "frm", "gal", "gam", "gamin", "gan", "gau", "gcd", "gcf", "gcg", "gen", "gf", "gif", "gjc", "gjf", "gl", "gnumeric", "gpt", "gsf", "gsm", "gtar", "gz", "h", "h++", "hdf", "hh", "hin", "hpp", "hqx", "hs", "hta", "htc", "htm", "html", "hwp", "hxx", "ica", "ice", "ico", "ics", "icz", "ief", "iges", "igs", "iii", "info", "inp", "ins", "iso", "isp", "ist", "istr", "jad", "jam", "jar", "java", "jdx", "jmz", "jng", "jnlp", "jp2", "jpe", "jpeg", "jpf", "jpg", "jpg2", "jpm", "jpx", "js", "json", "kar", "kil", "kin", "kml", "kmz", "kpr", "kpt", "ksp", "kwd", "kwt", "latex", "lha", "lhs", "lin", "lsf", "lsx", "ltx", "ly", "lyx", "lzh", "lzx", "m3g", "m3u", "m3u8", "m4a", "maker", "man", "mbox", "mcif", "mcm", "mdb", "me", "mesh", "mid", "midi", "mif", "mkv", "mm", "mmd", "mmf", "mml", "mmod", "mng", "moc", "mol", "mol2", "moo", "mop", "mopcrt", "mov", "movie", "mp2", "mp3", "mp4", "mpc", "mpe", "mpeg", "mpega", "mpg", "mpga", "mph", "mpv", "ms", "msh", "msi", "mvb", "mxf", "mxu", "nb", "nbp", "nc", "nef", "nwc", "o", "oda", "odb", "odc", "odf", "odg", "odi", "odm", "odp", "ods", "odt", "oga", "ogg", "ogv", "ogx", "old", "one", "onepkg", "onetmp", "onetoc2", "opf", "opus", "orc", "orf", "otf", "otg", "oth", "otp", "ots", "ott", "oza", "p7r", "pac", "pas", "pat", "patch", "pbm", "pcap", "pcf", "pcf.Z", "pcx", "pdb", "pdf", "pfa", "pfb", "pfr", "pgm", "pgn", "pgp", "php", "php3", "php3p", "php4", "php5", "phps", "pht", "phtml", "pk", "pl", "pls", "pm", "png", "pnm", "pot", "potm", "potx", "ppam", "ppm", "pps", "ppsm", "ppsx", "ppt", "pptm", "pptx", "prf", "prt", "ps", "psd", "py", "pyc", "pyo", "qgs", "qt", "qtl", "ra", "ram", "rar", "ras", "rb", "rd", "rdf", "rdp", "rgb", "rhtml", "roff", "ros", "rpm", "rss", "rtf", "rtx", "rxn", "scala", "sce", "sci", "sco", "scr", "sct", "sd", "sd2", "sda", "sdc", "sdd", "sds", "sdw", "ser", "sfd", "sfv", "sgf", "sgl", "sh", "shar", "shp", "shtml", "shx", "sid", "sig", "sik", "silo", "sis", "sisx", "sit", "sitx", "skd", "skm", "skp", "skt", "sldm", "sldx", "smi", "smil", "snd", "spc", "spl", "spx", "sql", "srt", "stc", "std", "sti", "stw", "sty", "sv4cpio", "sv4crc", "svg", "svgz", "sw", "swf", "swfl", "sxc", "sxd", "sxg", "sxi", "sxm", "sxw", "t", "tar", "taz", "tcl", "tex", "texi", "texinfo", "text", "tgf", "tgz", "thmx", "tif", "tiff", "tk", "tm", "torrent", "tr", "ts", "tsp", "tsv", "ttf", "ttl", "txt", "uls", "ustar", "val", "vcard", "vcd", "vcf", "vcs", "vmd", "vms", "vrm", "vrml", "vsd", "vss", "vst", "vsw", "wad", "wasm", "wav", "wax", "wbmp", "wbxml", "webm", "wk", "wm", "wma", "wmd", "wml", "wmlc", "wmls", "wmlsc", "wmv", "wmx", "wmz", "woff", "wp5", "wpd", "wrl", "wsc", "wvx", "wz", "x3d", "x3db", "x3dv", "xbm", "xcf", "xcos", "xht", "xhtml", "xlam", "xlb", "xls", "xlsb", "xlsm", "xlsx", "xlt", "xltm", "xltx", "xml", "xpi", "xpm", "xsd", "xsl", "xslt", "xspf", "xtel", "xul", "xwd", "xyz", "xz", "zip", "createReadNCbTransformer", "readCb", "Transform", "[object Object]", "chunk", "_encoding", "createReadNReadStream", "readCbTransformer", "crcModule", "reset", "update", "crc64", "CRC", "combineCrc64", "createRateLimiterTransform", "rateLimiter", "chunkSize", "finished", "ok", "timeToWait", "Acquire", "wait", "error", "milliseconds", "r", "setTimeout", "capacity", "rate", "realCapacity", "max", "currentAmount", "lastConsumeTime", "Date", "now", "want", "increment", "floor", "pipeRateLimit", "moduleNode", "createDefaultRateLimiter", "createRateLimiterStream", "Buffer2Stream", "buf", "lastPos", "_read", "n", "actualN", "min", "createReadCbTransformer", "createCrcReadStream", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "validateObjectName", "getSize", "body", "size", "getNewBodyConfig", "config1", "dataTransferCallback", "makeRetryStream", "newBody", "bodyBuf", "isValidRateLimiter", "oriMakeRetryStream", "undefined", "getEmitReadBodyConfig", "beforeRetry", "enableCRC", "getCRCBodyConfig", "getCopySourceHeaderValue", "srcBucket", "srcKey", "Function", "validateCheckpoint", "cp", "getRestoreInfoFromHeaders", "headerStoreValue", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ExpiryDate", "split", "_split$", "_split$$split", "OngoingRequest", "trim", "restoreInfo", "RestoreStatus", "RestoreParam", "ExpiryDays", "HeaderRestoreExpiryDays", "Number", "RequestDate", "HeaderRestoreRequestDate", "Tier", "HeaderRestoreTier", "DataTransferType", "TOS", "createDebug", "hashMd5", "decoding", "toString", "coding", "cryptoEncUtf8", "cryptoEncBase64", "cryptoEncHex", "getEnc", "decode", "cryptoHashMd5", "digest", "crypto", "createHmac", "createHash", "str", "encoding", "from", "hmacSha256", "hashSha256", "_uploadPart", "partNumber", "enableContentMD5", "toFixed", "newStream", "allContent", "concat", "md5", "totalSizeValid", "dataTransferStatusChange", "progress", "consumedBytes", "triggerDataTransfer", "type", "rwOnceBytes", "totalBytes", "progressValue", "Succeed", "bodyConfig", "Rw", "opts", "Started", "res", "handleResponse", "ETag", "etag", "serverSideEncryptionKeyId", "hashCrc64ecma", "axiosOpts", "__retryConfig__", "onUploadProgress", "event", "loaded", "task", "Failed", "uploadPart", "call", "uploadPartFromFile", "stats", "start", "offset", "end", "content-length", "completeMultipartUpload", "result", "VersionID", "Bucket", "bucket", "Location", "HashCrc64ecma", "Key", "Callback<PERSON><PERSON><PERSON>", "completeAll", "parts", "x-tos-complete-all", "Parts", "eTag", "PartNumber", "EmptyReadStream", "UploadEventType", "ABORT_ERROR_STATUS_CODE", "uploadFile", "cancelToken", "isCancel", "reason", "checkpoint", "fileStats", "file", "fileSize", "checkpointRichInfo", "checkpointStat", "_err", "isDirectory", "endsWith", "path", "<PERSON><PERSON><PERSON>", "dirname", "filePathIsPlaceholder", "record", "_checkpointRichInfo$r", "file_info", "last_modified", "file_size", "_checkpointRichInfo$r2", "mtimeMs", "_checkpointRichInfo$r3", "part_size", "tasks", "allTasks", "i", "currPartSize", "getAllTasks", "initConsumedBytes", "parts_info", "filter", "is_completed", "consumedBytesForProgress", "recordedTasks", "recordedTaskMap", "Map", "part_number", "getCheckpointContent", "checkpointContent", "upload_id", "triggerUploadEvent", "uploadEventChange", "checkpointFile", "TriggerProgressEventType", "triggerProgressEvent", "uploadPartSucceed", "writeCheckpointFile", "content", "updateAfterUploadPart", "uploadPartRes", "existRecordTask", "hash_crc64ecma", "uploadPartInfo", "UploadPartFailed", "includes", "UploadPartAborted", "UploadPartSucceed", "uploadedPartSet", "Set", "has", "multipartRes", "UploadId", "_checkpointRichInfo$f", "getDefaultCheckpointFilePath", "CreateMultipartUploadSucceed", "CreateMultipartUploadFailed", "firstErr", "index", "all", "taskNum", "currentIndex", "curTask", "consumedBytesThisTask", "getMakeRetryStream", "getBody", "x-tos-server-side-encryption-customer-algorithm", "x-tos-server-side-encryption-customer-key", "x-tos-server-side-encryption-customer-key-md5", "CompleteMultipartUploadFailed", "CompleteMultipartUploadSucceed", "completeMultipartUploadSucceed", "catch", "rmCheckpointFile", "sortedPartsInfo", "_cp$parts_info", "a", "part", "combineCRCInParts", "handleTasks", "ACLType", "StorageClassType", "MetadataDirectiveType", "AzRedundancyType", "PermissionType", "GranteeType", "CannedType", "HttpMethodType", "StorageClassInheritDirectiveType", "ReplicationStatusType", "LifecycleStatusType", "RedirectType", "StatusType", "TierType", "VersioningStatusType", "AccessPointStatusType", "TransferAccelerationStatusType", "MRAPMirrorBackRedirectPolicyType", "ResumableCopyEventType", "headObject", "normalizedInput", "ReplicationStatus", "HeaderReplicationStatus", "RestoreInfo", "uploadPartCopy", "copySource", "srcVersionID", "copySourceRangeStart", "copySourceRangeEnd", "copyRange", "SSECAlgorithm", "SSECKeyMD5", "copyObject", "resumableCopyObject", "objectStats", "srcVersionId", "objectSize", "copy_source_object_info", "object_size", "copy_source_range_end", "copy_source_range_start", "copyEventListener", "rangeStart", "rangeEnd", "copyPartInfo", "UploadPartCopyFailed", "UploadPartCopyAborted", "UploadPartCopySucceed", "cloneDeep", "Boolean", "x-tos-copy-source", "x-tos-copy-source-if-match", "secure", "endpoint", "handleEmptyObj", "x-tos-copy-source-range", "sourceCRC64", "actualCrc64", "getObject", "responseType", "NODEJS_DATATYPE", "getObjectV2", "dataType", "environment", "supportDataTypes", "checkSupportDataType", "setOneKey", "fill<PERSON>e<PERSON><PERSON><PERSON><PERSON>", "onDownloadProgress", "total", "resHeaders", "newData", "reject", "streamToBuf", "actualRes", "lastModified", "getObjectToFile", "getObjectRes", "fsWriteStream", "DownloadEventType", "downloadFile", "headObjectRes", "symlinkTargetSize", "object_info", "filePathStats", "tempFile<PERSON>ath", "isExist", "range_end", "range_start", "nextEnsureCloseFd", "version_id", "file_path", "temp_file_path", "triggerDownloadEvent", "downloadEventChange", "downloadPartSucceed", "updateAfterDownloadPart", "downloadPartRes", "rangeHashCrc64ecma", "downloadPartInfo", "DownloadPartFailed", "DownloadPartAborted", "DownloadPartSucceed", "flag", "CreateTempFileFailed", "CreateTempFileSucceed", "if-match", "dataStream", "crcInst", "writeStream", "flags", "isErr", "handleOnceCancel", "unpipe", "off", "customRenameFileAfterDownloadCompleted", "RenameTempFileFailed", "RenameTempFileSucceed", "renameTempFileSucceed", "isDefaultPort", "port", "v4Identifier", "SignersV4", "opt", "credentials", "options", "signature", "expiredAt", "credString", "credentialString", "datetime", "algorithm", "GetAccessKey", "signedHeaders", "authorization", "signature<PERSON><PERSON>er", "getDateTime", "header", "host", "endpoints", "hexEncodedBodyHash", "securityToken", "startsWith", "getEncodePath", "sign", "gnrCopySig", "getSignature", "getSignatureQuery", "X-Tos-Algorithm", "X-Tos-Content-Sha256", "X-Tos-Credential", "X-Tos-Date", "X-Tos-Expires", "X-Tos-SignedHeaders", "getSignaturePolicyQuery", "X-Tos-Policy", "policy", "<PERSON><PERSON><PERSON>", "getSigningKey", "substr", "stringToSign", "toISOString", "createScope", "region", "serviceName", "date", "kDate", "GetSecretKey", "kRegion", "kService", "canonicalString", "canonicalStringPolicy", "hexEncodedHash", "string", "method", "canonicalHeaders", "needSignHeaders", "getNeedSignedHeaders", "canonicalHeader<PERSON><PERSON>ues", "values", "encodeAll", "tmpPath", "ISigV4Credentials", "secretAccessKey", "accessKeyId", "TosAgent", "tosOpts", "agentOpts", "agent", "isHttps", "https", "Agent", "http", "keepAlive", "rejectUnauthorized", "enableVerifySSL", "timeout", "idleConnectionTime", "maxFreeSockets", "Infinity", "maxTotalSockets", "maxConnections", "oriCreateConnection", "createConnection", "args", "socket", "isTimeout", "isConnected", "connectTimer", "nextTick", "connectionTimeout", "clearTimeout", "TOSBase", "_opts", "axiosInst", "userAgent", "httpAgent", "httpsAgent", "getObjectPath", "actualBucket", "<PERSON><PERSON>ey", "mimeType", "getObjectInputKey", "autoRecognizeContentType", "lastDotIndex", "lastIndexOf", "extName", "lookupMimeType", "normalizeOpts", "proxyHost", "getUserAgent", "maxRetryCount", "axios", "create", "defaults", "auth", "withCredentials", "max<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON><PERSON><PERSON>", "maxRedirects", "validateStatus", "decompress", "transitional", "silentJSONParsing", "forcedJSONParsing", "clarifyTimeoutError", "ensureHeaders", "_v$response", "handleResponseHeader", "decodedValue", "func", "decodeURI", "safeSync", "sArr", "valueArr", "match", "decodedValueArr", "j", "ch", "encodedCh", "charCodeAt", "interceptors", "use", "isAxiosError", "_error$response", "config", "retryConfig", "retryCount", "canRetryData", "_error$response$heade", "isNetworkError", "isCanRetryStatusCode", "retrySignature", "signOpt", "sigInst", "nextConfig", "makeAxiosInst", "mustKeysErrorStr", "_default", "defaultValue", "requestTimeout", "requestAdapter", "getAdapter", "osType", "oriType", "os", "Linux", "<PERSON>", "Windows_NT", "nodeVersion", "version", "replaceAll", "arch", "userAgentProductName", "userAgentSoftName", "userAgentSoftVersion", "customStr", "userAgentCustomizedKeyValues", "needMd5", "md5String", "newPath", "subdomainBucket", "forcePathStyle", "isCustomDomain", "test", "header2", "encodeHeadersValue", "signv4", "stsToken", "accessKeySecret", "signatureHeaders", "reqHeaders", "reqOpts", "baseURL", "normalizedProxy", "needProxyParams", "proxyPort", "protocol", "logReqOpts", "adapter", "__retrySignature__", "_err$response", "_err$response$headers", "fetch", "subdomain", "normalizeBucketInput", "listObjects", "fetchBucket", "listObjectVersions", "versions", "listObjectsType2", "listOnlyOnce", "output", "max<PERSON>eys", "listObjectsType2Once", "KeyCount", "IsTruncated", "NextContinuationToken", "Contents", "CommonPrefixes", "continuationToken", "list-type", "ShareLinkClient", "modifyAxiosInst", "request", "parsedPolicyUrlVal", "origin", "addQueryStr", "search", "shareLinkClientOpts", "initParsedPolicyUrlVal", "matched", "policyUrl", "listBuckets", "createBucket", "deleteBucket", "headBucket", "ProjectName", "HeaderProjectName", "putBucketStorageClass", "x-tos-storage-class", "putBucketAcl", "aclBody", "getBucketAcl", "putObject", "_putObject", "putObjectFromFile", "normalizedHeaders", "fetchObject", "URL", "IgnoreSameKey", "ignore<PERSON><PERSON><PERSON><PERSON>", "ContentMD5", "putFetchTask", "fetchTask", "getPreSignedUrl", "alternativeEndpoint", "newHost", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "objectKeyPath", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_key", "kebab<PERSON>ey", "deleteObject", "skipTrash", "renameObject", "name", "deleteMultiObjects", "Quiet", "quiet", "Objects", "objects", "VersionId", "delete", "getObjectAcl", "putObjectAcl", "abortMultipartUpload", "listMultipartUploads", "appendObject", "preHashCrc64ecma", "append", "nextAppendOffset", "setObjectMeta", "metadata", "calculatePostSignature", "expiresIn", "fields", "conditions", "expirationDateStr", "getDateTimeStr", "valueOf", "dateStr", "date8Str", "substring", "service", "requestStr", "addedInForm", "x-tos-algorithm", "x-tos-date", "x-tos-credential", "policyStr", "expiration", "policyBase64", "defaultEmptyMethodMap", "getBucketCustomDomain", "getBucketIntelligenttiering", "getBucketInventory", "listBucketInventory", "getBucketMirrorBack", "getBucketNotification", "getBucketPolicy", "getBucketRealTimeLog", "getBucketReplication", "getBucketTagging", "getBucketWebsite", "handleEmptyServerError", "enableCatchEmptyServerError", "<PERSON><PERSON><PERSON>", "defaultResponse", "putBucketPolicy", "enableOptimizeMethodBehavior", "Statement", "deleteBucketPolicy", "Condition", "key2", "Version", "getBucketVersioning", "versioning", "putBucketVersioning", "Status", "preSignedPolicyURL", "normalizeInput", "validateConditions", "queryStr", "getSignedURLForList", "additionalQuery", "str2", "q", "getSignedURLForGetOrHead", "keyP<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "normalizedConditions", "operator", "getBucketLocation", "location", "getBucketCORS", "cors", "CORSRules", "putBucketCORS", "deleteBucketCORS", "putBucketLifecycle", "rules", "deleteBucketLifecycle", "lifecycle", "Rules", "getBucketLifecycle", "AllowSameActionOverlap", "putBucketEncryption", "rule", "encryption", "Content-MD5", "Rule", "getBucketEncryption", "deleteBucketEncryption", "putBucketMirrorBack", "deleteBucketMirrorBack", "mirror", "putObjectTagging", "tagSet", "tagging", "TagSet", "getObjectTagging", "deleteObjectTagging", "putBucketReplication", "role", "deleteBucketReplication", "replication", "Role", "ruleId", "putBucketWebsite", "otherProps", "website", "RoutingRules", "deleteBucketWebsite", "putBucketNotification", "notification", "CloudFunctionConfigurations", "RocketMQConfigurations", "putBucketCustomDomain", "customdomain", "CustomDomainRules", "deleteBucketCustomDomain", "customDomain", "putBucketRealTimeLog", "realtimeLog", "deleteBucketRealTimeLog", "ScheduleFrequency", "IncludedObjectVersions", "InventoryOptionalFields", "StringOp", "DateOp", "IpOp", "QuotaType", "req", "inventory", "id", "continuation-token", "InventoryConfigurations", "deleteBucketInventory", "putBucketInventory", "inventoryConfiguration", "Id", "createJob", "accountId", "x-tos-account-id", "listJobs", "maxResults", "others", "updateJobPriority", "jobId", "JobId", "priority", "updateJobStatus", "requestedJobStatus", "statusUpdateReason", "deleteJob", "<PERSON><PERSON><PERSON>", "putBucketTagging", "Tags", "deleteBucketTagging", "putBucketPayByTraffic", "payByTraffic", "getBucketPayByTraffic", "getImageStyleBriefInfo", "imageStyleBriefInfo", "BucketName", "ImageStyleBriefInfo", "getBucketImageStyleList", "imageStyle", "ImageStyles", "getBucketImageStyleListByName", "styleName", "imageStyleContent", "getBucketImageStyle", "putBucketImageStyle", "styleObjectPrefix", "Content", "deleteBucketImageStyle", "putBucketImageProtect", "originalImageProtect", "getBucketImageProtect", "putBucketImageStyleSeparator", "Separator", "SeparatorSuffix", "imageStyleSeparator", "getBucketImageStyleSeparator", "intelligenttiering", "putBucketRename", "getBucketRename", "deleteBucketRename", "restoreObject", "restore", "listStorageLens", "deleteStorageLens", "getStorageLens", "putStorageLens", "rest", "putBucketNotificationType2", "notification_v2", "getBucketNotificationType2", "putSymlink", "_putSymlink", "symlink", "getSymlink", "_getSymlink", "SymlinkTargetKey", "SymlinkTargetBucket", "LastModified", "putBucketTransferAcceleration", "transferAcceleration", "getBucketTransferAcceleration", "getStatus", "TransferAccelerationConfiguration", "Enabled", "Terminated", "putBucketAccessMonitor", "accessmonitor", "getBucketAccessMonitor", "getQosPolicy", "putQosPolicy", "restParams", "deleteQosPolicy", "createMultiRegionAccessPoint", "regions", "Name", "Regions", "getMultiRegionAccessPoint", "listMultiRegionAccessPoints", "getMultiRegionAccessPointRoutes", "alias", "deleteMultiRegionAccessPoint", "submitMultiRegionAccessPointRoutes", "routes", "Routes", "putMultiRegionAccessPointMirrorBack", "deleteMultiRegionAccessPointMirrorBack", "getMultiRegionAccessPointMirrorBack", "putBucketPrivateM3U8", "enable", "privateM3U8", "Enable", "getBucketPrivateM3U8", "putBucketTrash", "trash", "getBucketTrash", "InnerClient", "preSignedPostSignature", "CancelToken", "TosClient"], "mappings": "8IA4DYA,+fAjDCC,UAAuBC,MA+BlCC,YAAYC,GACV,MAAMC,KAAEA,GAASD,EACjBE,MAAMD,EAAKE,cA7BNC,iBAKAH,iBAIAI,uBAIAC,oBAMAC,sBAMAC,WAOLC,OAAOC,eAAeC,KAAMd,EAAee,WAE3CD,KAAKV,KAAOA,EACZU,KAAKP,KAAOH,EAAKY,KACjBF,KAAKN,WAAaL,EAASc,OAC3BH,KAAKL,QAAUN,EAASM,QACxBK,KAAKJ,UAAYP,EAASM,QAAQ,oBAClCK,KAAKH,IAAMR,EAASM,QAAQ,gBAMpBV,EAAAA,wBAAAA,uDAEVA,wBACAA,8BACAA,mCACAA,wCACAA,kCACAA,gDACAA,oCACAA,kCACAA,wCACAA,kCACAA,kCACAA,4CACAA,gCACAA,8DACAA,2EACAA,wDACAA,wEACAA,8CACAA,gDACAA,iEACAA,0CACAA,wBACAA,gCACAA,kCACAA,gCACAA,sCACAA,0CACAA,8CACAA,gDACAA,kCACAA,oCACAA,oDACAA,wDACAA,0DACAA,+CACAA,oDACAA,0CACAA,kCACAA,oCACAA,0BACAA,8BACAA,oBACAA,sCACAA,gCACAA,oCACAA,gCACAA,oCACAA,oBACAA,8DACAA,oCACAA,oCACAA,sCACAA,8CACAA,sDACAA,sCACAA,8BACAA,oCACAA,gCACAA,gCACAA,kDACAA,4BACAA,wCACAA,kCACAA,8CACAA,8DACAA,gBACAA,0DChIWmB,UAAuBjB,MAClCC,YAAYiB,GACVd,MAAMc,GAGNP,OAAOC,eAAeC,KAAMI,EAAeH,kBCLlCK,UAAoBnB,MAC/BC,YAAYiB,GACVd,MAAMc,GAGNP,OAAOC,eAAeC,KAAMM,EAAYL,YCErC,MAAMM,EAAoBC,EAAGD,kBACvBE,EAAmBD,EAAGC,iBAGtBC,EAASC,YAAUH,EAAGE,QACtBE,EAAOD,YAAUH,EAAGI,MAEpBC,EAAYF,YAAUH,EAAGK,WAIzBC,EAAKH,YAAUH,EAAGO,QAClBC,EAAWL,YAAUH,EAAGQ,UAExBC,EAAqBC,MAAAA,IAChC,UACQC,EAAWC,OAAOC,GACxB,MAAOC,SACDH,EAAWI,MAAMF,EAAS,CAAEG,WAAW,MCNpCC,EAAiBC,GAAkBC,IAC9C,GAAW,MAAPD,GAA8B,iBAARA,EACxB,OAGF,MAAME,EAAQC,EAAIH,EAAKC,GAClBG,MAAMC,QAAQH,IACjBI,EAAIN,EAAKC,EAAc,MAATC,EAAgB,GAAK,CAACA,KAIlCK,EAAmBC,IACvB,MAAMC,EAA4BC,GAC5BN,MAAMC,QAAQK,GACTA,EAAOC,IAAKC,GAAOH,EAAYG,IAGlB,iBAAXF,EACFF,EAAcE,GAGD,iBAAXA,GAAiC,MAAVA,EAEpBtC,OAAOyC,KAAKH,GAAQI,OAAO,CAACC,EAAUd,KAEhDc,EADgBN,EAAYR,IACZS,EAAeT,GACxBc,GACN,IAIEL,EAGT,OAAOD,GAGIO,EAAwBT,EAAiBU,GAC7CA,EAAUC,QAAQ,SAAU,OAAOC,eAS/BC,EAA+Bb,EACzCc,GACQA,EAAgB,GAAGC,cAAgBD,EAAgBE,MAAM,IAIvDC,EAAwBC,IACnC,MAAMC,EAAwB,GAQ9B,OAPAtD,OAAOyC,KAAKY,GACTE,OACAC,QAAS3B,IACRyB,EAAYG,QACPC,mBAAmB7B,MAAQ6B,mBAAmBL,EAAMxB,SAGtDyB,EAAYK,KAAK,MAGbC,EACX/D,IAEA,MAAMgE,EAAoBhE,GAAW,GAC/BiE,EAAoB,GAC1B9D,OAAOyC,KAAKoB,GAAUL,QAAS3B,IACR,MAAjBgC,EAAShC,KACXiC,EAASjC,GAAOgC,EAAShC,MAI7B,MAAMkC,EAAoB,GAM1B,OALA/D,OAAOyC,KAAKqB,GAAUN,QAAS3B,IAC7B,MAAMmC,EAASnC,EAAIkB,cACnBgB,EAASC,GAAUF,EAASjC,KAGvBkC,GAkCIE,EAAkBC,IACR,iBAAVA,IACTA,EAAQ,CACNC,IAAKD,IAYFA,GAGF9C,eAAegD,EACpBC,GAEA,IAEE,MAAO,CAAC,WADQA,GAEhB,MAAOC,GACP,MAAO,CAACA,EAAK,gBAaDC,EAAO3C,GACrB,MAAuB,oBAAT4C,MAAwB5C,aAAe4C,cAGvCC,EAAS7C,GACvB,MAAyB,oBAAX8C,QAA0B9C,aAAe8C,gBAGzCC,EAAW/C,GAKzB,OAAOA,aAAegD,oBAORC,EAAaC,GAC3B,OAAKA,EAGE9E,OAAOyC,KAAKqC,GAChBvC,IAAKV,IACJ,MAAMkD,KAAUD,EAAEjD,GAClB,SAAU6B,mBAAmB7B,MAAQ6B,mBAAmBqB,OAEzDpB,KAAK,KAPC,YAUKqB,EAAcV,GAC5B,OAAOA,aAAe9D,EAGxB,MAKMyE,EAAgBH,GACH,iBAANA,EACFA,EAEFA,EAAEI,cAGEC,EAGT,CACFC,YAAa,qBACbC,aAAc,gBACdC,aAAc,gBACdC,mBAAoB,sBACpBC,cAAe,iBACfC,WAAY,cACZC,cAAe,uBACfC,gBAAiB,mBACjBC,gBAAiB,mBACjBC,YAAa,eACbC,QAAS,CAAC,UAxBkBhB,GACrBA,EAAEI,eAwBTa,MAAO,QAEPC,QAAS,WACTC,gBAAiB,CAAC,oBAAqBhB,GACvCiB,YAAa,gBACbC,kBAAmB,CAAC,sBAAuBlB,GAE3CmB,IAAK,YACLC,iBAAkB,2BAClBC,UAAW,mBACXC,aAAc,uBACdC,WAAY,oBACZC,cAAe,wBAEfC,qBAAsB,+BACtBC,yBAA0B,oCAC1BC,cAAe,kDACfC,QAAS,4CACTC,WAAY,gDAEZC,gBAAiB,0BACjBC,kBAAmB,6BACnBC,0BAA2B,CACzB,sCACAhC,GAEFiC,sBAAuB,kCACvBC,4BAA6B,wCAC7BC,wBACE,8DACFC,kBAAmB,wDACnBC,qBACE,4DAEFC,kBAAmB,2BACnBC,KAAO1C,GACE9E,OAAOyC,KAAKqC,GAAGpC,OAAO,CAAC+E,EAAM5F,KAClC4F,gBAAmB5F,MAAYiD,EAAEjD,GAC1B4F,GACN,IAELC,wBAAyB,kCACzBC,aAAc,sBACdC,aAAc,sBACdC,aAAc,sBACdC,SAAU,iBACVC,YAAa,qBACbC,uBAAwB,CAAC,kCAAoClD,GAAMmD,OAAOnD,IAC1EoD,iBAAkB,uBAClBC,oBAAqB,uBACrBC,gBAAiB,yBACjBC,WAAY,oBACZC,eAAgB,yBAILC,EAGT,CACFC,UAAW,YACXC,QAAS,gBACTC,WAAY,oBACZC,WAAY,oBAEZC,qBAAsB,yBACtBC,2BAA4B,+BAC5BC,wBAAyB,4BACzBC,wBAAyB,4BACzBC,oBAAqB,wBACrBC,gBAAiB,CAAC,mBAAqBnE,GAAYA,EAAEI,gBAGvD,SAAgBgE,EACdpE,EAEArC,GAEA,IAAKA,EAAK0G,OACR,OAGF,MAAMtJ,EAAUiF,EAAEjF,SAAW,GAG7B,SAASuJ,EAAaC,EAAWvE,GACb,MAAdjF,EAAQwJ,KACVxJ,EAAQwJ,GAAKvE,GAJjBA,EAAEjF,QAAUA,EAQZ4C,EAAKe,QAAS6F,IACZ,MAAMC,EAAQnE,EAAkBkE,GAChC,IAAKC,EAEH,MAAM,IAAIhJ,OACH+I,8CAIT,MAAME,EAAWzE,EAAEuE,GACnB,GAAgB,MAAZE,EACF,OAIF,GAAqB,iBAAVD,EACT,OAAOF,EAAaE,KAFCC,GAKvB,GAAIvH,MAAMC,QAAQqH,GAGhB,OAAOF,EAFQE,EAAM,GACJA,EAAM,GAAGC,IAI5B,MAAM3H,EAAM0H,EAAMC,GAClBvJ,OAAOwJ,QAAQ5H,GAAK4B,QAAQ,EAAE6F,EAAGvE,MAC/BsE,EAAaC,EAAGvE,OAkDf,MAAM2E,EAAoBC,GACxBC,EAAGC,UAAUF,YAGNG,EACdrK,EACA8E,GAEA,MAAO,CACL9E,KAAAA,EACAI,WAAY0E,EAAI1E,WAChBC,QAASyE,EAAIzE,QACbC,UAAWwE,EAAIxE,UACfC,IAAKuE,EAAIvE,cAoBG+J,EAAsBC,EAAsBlK,GAC1D,MAAMmK,EAAcnK,EAAQ,wBAC5B,GAAmB,MAAfmK,EAOF,OAGF,MAAMC,EAAwB,iBAARF,EAAmBA,EAAMA,EAAIG,WACnD,GAAID,IAAWD,EACb,MAAM,IAAI1J,8CACoC0J,mBAA6BC,wBAS/E,IAAYE,GAAZ,SAAYA,GACVA,+BACAA,iCACAA,mCACAA,qCACAA,2BACAA,iDACAA,6BACAA,0CACAA,0CACAA,yDACAA,mBACAA,kCAZF,CAAYA,IAAAA,OAoBL,MAAMC,GAAuBC,IAClC,IAAIC,EAAWC,QAAQC,UACvB,OAAOpJ,UACLkJ,EAAWA,EAASG,KAAK,IAAMJ,KACxBC,IAIEI,GAA0BtJ,MAAAA,IACrC,IACE,OAAOuJ,KAAKC,YAAYC,EAAaC,EAAU,UAC/C,MAAOxG,GAEP,YADAyG,QAAQC,KAAK,8CAKJC,GACXC,IAEA,IAAIC,EAAwD,KAa5D,MAAO,CACLC,cAAe,IAAMD,EACrBE,KAdsB,KAClBF,GACFG,GACEH,EACA,IAAI9L,MAAM,iDAId8L,EAAaD,IACNC,KASEG,GAAa,CACxBC,EAOAjH,KAEIiH,GAAU,YAAaA,GAAoC,mBAAnBA,EAAOC,SAC7C,cAAeD,IAAWA,EAAOE,WACnCF,EAAOC,QAAQlH,IAIRoH,GAA4B,CAIvCC,EACAC,EACAC,KArFqCC,IAAAA,EA0FrC,OAHAF,EAAKG,GAAG,SAvF6BD,EAuFGD,EAvFkBvH,IAC1DyG,QAAQiB,KAAOF,GAAU,qBAAoBxH,MAuF7CqH,EAAII,GAAG,QAAUzH,GAAQgH,GAAWM,EAAMtH,IAC1CsH,EAAKG,GAAG,QAAUzH,GAAQgH,GAAWK,EAAKrH,IACnCqH,EAAIM,KAAKL,ICpeXxK,eAAe8K,GAEpBC,GAEAA,EAAQjM,KAAKkM,qBAAqBD,GAClC,MAAMtM,EAAU+D,EAAoBuI,EAAMtM,SA+B1C,OA9BAsM,EAAMtM,QAAUA,EAChBqJ,EAAmBiD,EAAO,CACxB,eACA,eACA,qBACA,kBACA,kBACA,cACA,UAEA,MACA,mBACA,YACA,eACA,gBAEA,gBACA,UACA,aACA,uBACA,2BAEA,OACA,0BACA,eACA,oBAGFjM,KAAKmM,2BAA2BF,EAAOtM,GAEhCK,KAAKoM,aACVH,EACA,OACA,CAAEI,QAAS,IACX1M,EACA,ICxEJ,MAGa2M,GAAwB,CACnCC,EACAC,EACAC,GAAc,KAEd,IAAIC,EAAWF,EACXA,EATuC,UAUzCE,EAVyC,QAWrCD,GACF5B,QAAQC,iCACsB4B,oFAIlC,MAAMC,EAAUC,KAAKC,KAAKN,EAhBG,KA0B7B,OATIC,EAAiBG,IACnBD,EAAWC,EACPF,GACF5B,QAAQC,iCACsB4B,mFAK3BA,GAGFxL,eAAe4L,GAAyBb,GAC7C,MAAMc,SAAeA,KAAaC,GAAcf,EAC1CgB,QAAYjN,KAAKoM,aACrBH,EACA,MACA,CACEc,SAAAA,KACGrK,EAAsBsK,IAE3B,IAKF,OAHkBvL,EAAcwL,EAAI3N,KACpC4N,CAAU,SAEHD,EC1ET,MAAaE,GAAgD,CAC3DC,MAAO,aACPC,KAAM,8BACNC,IAAK,wBACLC,GAAI,yBACJC,IAAK,eACLC,KAAM,eACNC,KAAM,eACNC,IAAK,qBACLC,IAAK,YACLC,IAAK,sBACLC,IAAK,0CACLC,SAAU,sBACVC,IAAK,aACLC,IAAK,aACLC,IAAK,iBACLC,IAAK,8BACLC,IAAK,iBACLC,KAAM,uBACNC,QAAS,0BACTC,QAAS,2BACTC,GAAI,cACJC,IAAK,kBACLC,IAAK,eACLC,IAAK,gBACLC,IAAK,gBACLC,EAAG,uBACHC,IAAK,sBACLC,IAAK,8BACLC,MAAO,sBACPC,IAAK,gBACLC,IAAK,2BACLC,IAAK,iBACLC,IAAK,aACLC,KAAM,sBACNC,IAAK,aACLC,IAAK,uBACLC,EAAG,cACHC,MAAO,gBACPC,IAAK,oBACLC,IAAK,oBACLC,IAAK,mBACLC,MAAO,mBACPC,IAAK,+BACLC,OAAQ,2BACRC,IAAK,gCACLC,KAAM,2BACNC,IAAK,oBACLC,IAAK,oBACLC,GAAI,gBACJC,IAAK,oBACLC,IAAK,oBACLC,IAAK,oBACLC,IAAK,4BACLC,IAAK,iBACLC,IAAK,6BACLC,IAAK,iBACLC,IAAK,oBACLC,IAAK,sBACLC,KAAM,uBACNC,IAAK,iBACLC,MAAO,sBACPC,IAAK,aACLC,KAAM,kBACNC,IAAK,iBACLC,IAAK,0BACLC,IAAK,8BACLC,IAAK,qBACLC,KAAM,qBACNC,IAAK,gBACLC,IAAK,6BACLC,IAAK,oBACLC,IAAK,0BACLC,IAAK,6BACLC,IAAK,oBACLC,IAAK,eACLC,IAAK,uBACLC,IAAK,oBACLC,IAAK,kBACLC,KAAM,kBACNC,IAAK,WACLC,IAAK,WACLC,KAAM,2BACNC,IAAK,iBACLC,GAAI,uBACJC,IAAK,2BACLC,IAAK,iBACLC,IAAK,gBACLC,EAAG,cACHC,SAAU,2BACVC,IAAK,oBACLC,IAAK,yBACLC,KAAM,wCACNC,IAAK,WACLC,KAAM,cACNC,IAAK,yBACLC,IAAK,iBACLC,KAAM,iBACNC,GAAI,WACJC,IAAK,8BACLC,IAAK,gCACLC,IAAK,oBACLC,IAAK,qBACLC,KAAM,mDACNC,KAAM,0EACNC,IAAK,qBACLC,KAAM,mDACNC,KAAM,0EACNC,GAAI,WACJC,IAAK,oBACLC,GAAI,sBACJC,IAAK,yBACLC,IAAK,gCACLC,KAAM,gCACNC,IAAK,iBACLC,IAAK,gCACLC,IAAK,yBACLC,KAAM,yBACNC,KAAM,yBACNC,KAAM,yBACNC,KAAM,yBACNC,IAAK,oBACLC,GAAI,yBACJC,IAAK,gBACLC,IAAK,8BACLC,GAAI,2BACJC,GAAI,sBACJC,MAAO,sBACPC,IAAK,iCACLC,KAAM,iCACNC,IAAK,qBACLC,KAAM,aACNC,IAAK,YACLC,IAAK,cACLC,GAAI,sBACJC,MAAO,sBACPC,IAAK,sBACLC,IAAK,0BACLC,IAAK,0BACLC,MAAO,0BACPC,IAAK,6BACLC,IAAK,4BACLC,IAAK,iBACLC,IAAK,oCACLC,IAAK,2BACLC,IAAK,qBACLC,GAAI,uBACJC,IAAK,YACLC,IAAK,4BACLC,IAAK,4BACLC,GAAI,WACJC,SAAU,yBACVC,IAAK,yBACLC,IAAK,qBACLC,IAAK,cACLC,KAAM,qBACNC,GAAI,mBACJC,EAAG,cACHC,MAAO,gBACPC,IAAK,oBACLC,GAAI,gBACJC,IAAK,iBACLC,IAAK,gBACLC,IAAK,2BACLC,GAAI,iBACJC,IAAK,kBACLC,IAAK,mBACLC,IAAK,YACLC,KAAM,YACNC,IAAK,oBACLC,IAAK,gBACLC,IAAK,oBACLC,IAAK,0BACLC,IAAK,2BACLC,IAAK,gBACLC,IAAK,gBACLC,IAAK,YACLC,KAAM,aACNC,IAAK,aACLC,IAAK,uBACLC,KAAM,qBACNC,IAAK,0BACLC,IAAK,gCACLC,IAAK,8BACLC,IAAK,gCACLC,IAAK,qBACLC,KAAM,qBACNC,IAAK,mCACLC,IAAK,oBACLC,IAAK,2BACLC,KAAM,cACNC,IAAK,sBACLC,IAAK,qBACLC,IAAK,cACLC,KAAM,+BACNC,IAAK,YACLC,IAAK,aACLC,KAAM,aACNC,IAAK,YACLC,IAAK,aACLC,KAAM,YACNC,IAAK,YACLC,IAAK,YACLC,GAAI,yBACJC,KAAM,mBACNC,IAAK,aACLrY,IAAK,uBACLsY,IAAK,6BACLC,IAAK,sBACLC,IAAK,uCACLC,IAAK,mCACLC,IAAK,2BACLC,IAAK,2BACLC,IAAK,wBACLC,IAAK,sBACLC,IAAK,sBACLC,MAAO,sBACPC,IAAK,oBACLC,IAAK,0BACLC,IAAK,qBACLC,IAAK,iBACLC,IAAK,iBACLC,IAAK,aACLC,GAAI,kBACJC,IAAK,oBACLC,IAAK,oBACLC,IAAK,oBACLC,IAAK,kBACLC,IAAK,kBACLC,KAAM,wBACNC,IAAK,aACLC,MAAO,sBACPC,IAAK,0BACLC,KAAM,mBACNC,KAAM,mBACNC,IAAK,yBACLC,IAAK,uBACLC,GAAI,yBACJC,KAAM,aACNC,IAAK,aACLC,KAAM,aACNC,IAAK,oBACLC,IAAK,mBACLC,GAAI,yBACJC,IAAK,8BACLC,IAAK,uBACLC,IAAK,cACLC,KAAM,8BACNC,IAAK,cACLC,IAAK,aACLC,IAAK,yBACLC,KAAM,kBACNC,IAAK,uBACLC,IAAK,yBACLC,OAAQ,yBACRC,IAAK,kBACLC,MAAO,oBACPC,IAAK,aACLC,IAAK,aACLC,IAAK,YACLC,IAAK,yBACLC,IAAK,aACLC,KAAM,aACNC,MAAO,aACPC,IAAK,aACLC,KAAM,aACNC,IAAK,uBACLC,IAAK,mBACLC,GAAI,yBACJC,IAAK,aACLC,IAAK,oBACLC,IAAK,uBACLC,IAAK,kBACLC,IAAK,oBACLC,GAAI,0BACJC,IAAK,0BACLC,GAAI,uBACJC,IAAK,oBACLC,IAAK,oBACLC,EAAG,uBACHC,IAAK,kBACLC,IAAK,8CACLC,IAAK,2CACLC,IAAK,6CACLC,IAAK,8CACLC,IAAK,2CACLC,IAAK,iDACLC,IAAK,kDACLC,IAAK,iDACLC,IAAK,0CACLC,IAAK,YACLC,IAAK,YACLC,IAAK,YACLC,IAAK,kBACLC,IAAK,sBACLC,IAAK,sBACLC,OAAQ,sBACRC,OAAQ,sBACRC,QAAS,sBACTC,IAAK,gCACLC,KAAM,YACNC,IAAK,eACLC,IAAK,sBACLC,IAAK,wBACLC,IAAK,uDACLC,IAAK,8CACLC,IAAK,2DACLC,IAAK,0DACLC,IAAK,mDACLC,IAAK,+BACLpc,EAAG,gBACHqc,IAAK,kCACLC,IAAK,oCACLC,IAAK,gBACLC,IAAK,2BACLC,MAAO,cACPC,IAAK,0BACLC,KAAM,+BACNC,IAAK,yBACLC,QAAS,yBACTC,IAAK,YACLC,IAAK,iBACLC,IAAK,kBACLC,IAAK,qBACLC,IAAK,qBACLC,IAAK,yBACLC,IAAK,2BACLC,IAAK,0BACLC,IAAK,4BACLC,IAAK,2BACLC,KAAM,4BACNC,MAAO,yCACPC,KAAM,4BACNC,KAAM,4BACNC,KAAM,kCACNC,IAAK,2BACLC,MAAO,2BACPC,GAAI,uBACJC,GAAI,cACJC,IAAK,gBACLC,GAAI,cACJC,IAAK,YACLC,IAAK,0BACLC,IAAK,aACLC,KAAM,yDACNC,KAAM,wEACNC,KAAM,sDACNC,IAAK,0BACLC,IAAK,gCACLC,KAAM,0DACNC,KAAM,yEACNC,IAAK,gCACLC,KAAM,6DACNC,KAAM,4EACNC,IAAK,yBACLC,IAAK,6BACLC,GAAI,yBACJC,IAAK,oBACLC,GAAI,gBACJC,IAAK,4BACLC,IAAK,4BACLC,IAAK,qBACLC,GAAI,kBACJC,IAAK,gCACLC,GAAI,uBACJC,IAAK,uBACLC,IAAK,kBACLC,IAAK,qBACLC,GAAI,qBACJC,GAAI,wBACJC,IAAK,sBACLC,IAAK,oBACLC,IAAK,cACLC,MAAO,6BACPxjB,GAAI,uBACJyjB,KAAM,sBACNC,IAAK,oBACLC,IAAK,uCACLC,IAAK,wBACLC,IAAK,kBACLC,IAAK,gBACLC,IAAK,yBACLC,MAAO,eACPC,IAAK,uBACLC,IAAK,uBACLC,IAAK,eACLC,IAAK,4BACLC,IAAK,iBACLC,GAAI,wBACJC,IAAK,cACLC,IAAK,oCACLC,IAAK,oCACLC,IAAK,uCACLC,IAAK,qCACLC,IAAK,sCACLC,IAAK,qCACLC,IAAK,qCACLC,IAAK,aACLC,IAAK,uBACLC,IAAK,6CACLC,GAAI,mBACJC,KAAM,qBACNC,IAAK,qBACLC,MAAO,YACPC,IAAK,qBACLC,IAAK,gBACLC,IAAK,4BACLC,IAAK,sBACLC,KAAM,aACNC,IAAK,kCACLC,KAAM,oBACNC,IAAK,wBACLC,KAAM,wBACNC,IAAK,qBACLC,IAAK,qBACLC,IAAK,qBACLC,IAAK,qBACLC,KAAM,sDACNC,KAAM,qEACNC,IAAK,uBACLC,KAAM,uBACNC,IAAK,cACLC,IAAK,0BACLC,IAAK,6BACLC,IAAK,YACLC,IAAK,oBACLhc,IAAK,4BACLic,IAAK,aACLC,IAAK,wCACLC,IAAK,wCACLC,IAAK,2CACLC,IAAK,0CACLC,IAAK,aACLC,QAAS,wBACTC,OAAQ,uBACRC,IAAK,gBACLC,KAAM,gBACNC,GAAI,uBACJC,IAAK,gCACLC,KAAM,gCACNC,IAAK,+BACLC,IAAK,+BACLC,IAAK,wCACLC,IAAK,kCACLC,IAAK,+BACLC,IAAK,iCACLC,EAAG,sBACHC,IAAK,oBACLC,IAAK,gCACLC,IAAK,oBACLC,IAAK,aACLC,KAAM,wBACNC,QAAS,wBACTC,KAAM,aACNC,IAAK,qBACLC,IAAK,gCACLC,KAAM,iCACNC,IAAK,aACLC,KAAM,aACNC,GAAI,aACJC,GAAI,eACJC,QAAS,2BACTC,GAAI,sBACJC,GAAI,aACJC,IAAK,sBACLC,IAAK,4BACLC,IAAK,wBACLC,IAAK,cACLC,IAAK,aACLC,IAAK,YACLC,MAAO,sBACPC,IAAK,8BACLC,MAAO,aACPC,IAAK,uBACLC,IAAK,aACLC,IAAK,mBACLC,IAAK,iBACLC,IAAK,4BACLC,IAAK,iBACLC,KAAM,aACNC,IAAK,wBACLC,IAAK,wBACLC,IAAK,wBACLC,IAAK,wBACLC,IAAK,qBACLC,KAAM,mBACNC,IAAK,YACLC,IAAK,iBACLC,KAAM,qBACNC,MAAO,4BACPC,KAAM,aACNC,GAAI,oBACJC,GAAI,gBACJC,IAAK,iBACLC,IAAK,uBACLC,IAAK,mBACLC,KAAM,2BACNC,KAAM,yBACNC,MAAO,iCACPC,IAAK,iBACLC,IAAK,iBACLC,IAAK,uBACLC,KAAM,wBACNC,IAAK,iCACLC,IAAK,8BACLC,IAAK,aACLC,IAAK,iBACLC,IAAK,iBACLC,GAAI,sBACJC,IAAK,gBACLC,KAAM,mBACNC,KAAM,iBACNC,IAAK,kBACLC,IAAK,oBACLC,KAAM,4BACNC,IAAK,wBACLC,MAAO,wBACPC,KAAM,iDACNC,IAAK,2BACLC,IAAK,2BACLC,KAAM,wDACNC,KAAM,iDACNC,KAAM,oEACNC,IAAK,2BACLC,KAAM,oDACNC,KAAM,uEACNC,IAAK,kBACLC,IAAK,0BACLC,IAAK,kBACLC,IAAK,kBACLC,IAAK,uBACLC,KAAM,uBACNC,KAAM,uBACNC,KAAM,kBACNC,IAAK,kCACLC,IAAK,sBACLC,IAAK,iBACLC,GAAI,mBACJC,IAAK,mBCzhBP,SAASC,GAAyBC,GAChC,OAAO,IAAIC,YAAU,CACnBC,gBAAgBC,EAAOC,EAAWnnB,GAEhC+mB,EADkBG,EAAM7lB,QAExBjJ,KAAKuD,KAAKurB,GACVlnB,gBAKUonB,GACd3jB,EACAsjB,GAEA,MAAMM,EAAoBP,GAAyBC,GACnD,OAAOnjB,GACLH,EACA4jB,EACA,yBCjBJ,IAAIC,GAAY,KAGdA,gDCHQttB,MAAQ,IAEhButB,QACEnvB,KAAK4B,MAAQ,IAGCitB,mBACd,MAAM,IAAIzuB,EAAe,2CAG3BgvB,OAAOxtB,GAEL,OADA5B,KAAK4B,MAAQytB,QAAMztB,EAAO5B,KAAK4B,OACxB5B,KAAK4B,MAGdoI,WACE,OAAOhK,KAAK4B,qCDRhB,UAAQ0tB,GAAFC,aAAOA,IAAiBL,GEoD9B,SAASM,GAA2BC,GAClC,OAAO,IAAIb,YAAU,CACnBC,gBAAgBC,EAAOC,EAAWnnB,GAChC,IACE,MAAM8nB,EAAYZ,EAAM7lB,OACxB,IAAI0mB,GAAW,EACf,MAAQA,GAAU,CAChB,MAAMC,GAAEA,EAAFC,WAAMA,SAAqBJ,EAAYK,QAAQJ,GAEhDE,SACGG,GAAKF,GAEbF,EAAWC,EAGb5vB,KAAKuD,KAAKurB,GACVlnB,IACA,MAAOooB,GACPpoB,EAASooB,gBAmBDD,GAAKE,GACnB,OAAO,IAAI5lB,QAAS6lB,IAClBC,WAAW,IAAMD,EAAE,IAAKD,8DAnF1BG,EACAC,GAEA,MAAMC,EAAe1jB,KAAK2jB,IAZR,MAYyBH,GAErCzd,EAAwB,CAC5B0d,KAFezjB,KAAK2jB,IAdR,KAcqBF,GAGjCD,SAAUE,EACVE,cAAeF,EACfG,gBAAiBC,KAAKC,OAGxB,MAAO,CACLb,QAAS5uB,MAAAA,IACH0vB,EAAOje,EAAEyd,WACXQ,EAAOje,EAAEyd,UAGX,MAAMO,EAAMD,KAAKC,MACXE,EAAYjkB,KAAKkkB,OAAQH,EAAMhe,EAAE8d,iBAAmB,IAAQ9d,EAAE0d,MAQpE,OANIQ,EAAYle,EAAE6d,cAAgB7d,EAAEyd,SAClCzd,EAAE6d,cAAgB7d,EAAEyd,SAEpBzd,EAAE6d,eAAiBK,EAGjBD,EAAOje,EAAE6d,cAGJ,CAAEZ,IAAI,EAAOC,WAAYjjB,KAAKC,MAFd+jB,EAAOje,EAAE6d,eAAiB7d,EAAE0d,KAEO,OAG5D1d,EAAE8d,gBAAkBE,EACpBhe,EAAE6d,cAAgB7d,EAAE6d,cAAgBI,EAE7B,CACLhB,IAAI,EACJC,WAAY,wCA+BlBxkB,EACAokB,GAEA,MAAMsB,EAAgBvB,GAA2BC,GAEjD,OAAOjkB,GACLH,EACA0lB,EACA,qCCtFJ,IAAItB,GAAc,KAEhBA,GAAcuB,kCAKRC,2BAA0BC,IAA4BzB,SCjBjD0B,WAAsBzsB,WAGjCtF,YAAoBgyB,GAClB7xB,aADkB6xB,gBAFpBC,QAAU,EAEUrxB,SAAAoxB,EAIpBE,MAAMC,GACJ,MAAMhlB,EAAYvM,KAAKoxB,IAAInoB,OAC3B,IAAIuoB,EAAU5kB,KAAK6kB,IAAIF,EAAGhlB,EAAYvM,KAAKqxB,SAEvCrxB,KAAKqxB,SAAW9kB,EAClBvM,KAAKuD,KAAK,OAIZvD,KAAKuD,KAAKvD,KAAKoxB,IAAInuB,MAAMjD,KAAKqxB,QAASrxB,KAAKqxB,QAAUG,IACtDxxB,KAAKqxB,SAAWG,ICfpB,SAASE,GAAwB/C,GAC/B,OAAO,IAAIC,YAAU,CACnBC,gBAAgBC,EAAOC,EAAWnnB,GAChC+mB,EAAOG,GACP9uB,KAAKuD,KAAKurB,GACVlnB,gBAKU+pB,GACdtmB,EACAxB,GAEA,MAAMolB,EAAoByC,GAAyB5C,GACjDjlB,EAAIulB,OAAON,IAGb,OAAOtjB,GACLH,EACA4jB,EACA,2BCPQ2C,YCoBIC,GAAmB5lB,GAEjC,IAD6B,iBAAVA,EAAqBA,EAAQA,EAAMtK,KAC9CsH,OAAS,EACf,MAAM,IAAI7I,EACR,mEAKU0xB,GAAQC,EAAepyB,GACrC,GAAI4E,EAASwtB,GACX,OAAOA,EAAK9oB,OAEd,GAAI5E,EAAO0tB,GACT,OAAOA,EAAKC,KAEd,GAAIryB,GAAWA,EAAQ,kBAAmB,CACxC,MAAMiF,GAAKjF,EAAQ,kBACnB,GAAIiF,GAAK,EACP,OAAOA,EAGX,OAAO,KA0HF1D,eAAe+wB,GACpBhmB,GAEA,MAAMimB,EAhGR,UAAmEH,KACjEA,EADiEI,qBAEjEA,EAFiEC,gBAGjEA,EAHiE3C,YAIjEA,IAEA,IAAI4C,EAAqCN,EAWzC,GAAIxtB,EAAS8tB,GAAU,CACrB,MAAMC,EAAUD,EAChBD,EAAkB,IAAM,IAAIjB,GAAcmB,GAC1CD,EAAU,IAAIlB,GAAcmB,GAG9B,GAAI7tB,EAAW4tB,KACT5C,GAAe8C,GAAmB9C,KACpC4C,EAAUnB,GAAwBmB,EAAS5C,IAE7C4C,EAAUrD,GAAsBqD,EAASF,GAErCC,GAAiB,CACnB,MAAMI,EAAqBJ,EAC3B,MAAO,CACLL,KAAMM,EACND,gBAAiB,KACf,IAAI/mB,EAASmnB,IACb,OAAKnnB,GAIDokB,GAAe8C,GAAmB9C,KACpCpkB,EAAS6lB,GAAwB7lB,EAAQokB,IAE3CpkB,EAAS2jB,GAAsB3jB,EAAQ8mB,GAChC9mB,GAPEA,IAajB,OAxCE0mB,KAAMM,EACND,qBAAiBK,GAsFHC,CAAsBzmB,GAGtC,OA/CF/K,gBAAoE6wB,KAClEA,EADkEY,YAElEA,EAFkEP,gBAGlEA,EAHkEQ,UAIlEA,IAEA,IAAqDA,EACnD,MAAO,CACLb,KAAAA,EACAY,YAAAA,EACAP,gBAAAA,GAIJ,IAAIC,EAAqCN,EACzC,MAAMloB,EAAM,IAAIylB,GAChB,GAAI7qB,EAAWstB,KACbM,EAAUV,GAAoBI,EAAMloB,GAChCuoB,GAAiB,CACnB,MAAMI,EAAqBJ,EAC3BA,EAAkB,KAChB,MAAM/mB,EAASmnB,IACf,OAAKnnB,EAGEsmB,GAAoBtmB,EAAQxB,GAF1BwB,GAOf,MAAO,CACL0mB,KAAMM,EACNM,YAAa,KACX9oB,EAAIslB,cACJwD,GAAAA,KAEFP,gBAAAA,EACAvoB,IAAAA,GAScgpB,CADhB5mB,EAAQ,IAAKA,KAAUimB,aAKTY,GAAyBC,EAAmBC,GAC1D,UAAWD,KAAavvB,mBAAmBwvB,cAG7BT,GAAmB9C,GACjC,WAAKA,GAAAA,EAAaK,gBAAaL,SAAAA,EAAaK,mBAAmBmD,UAC7D,MAAM,IAAI7yB,2CAEZ,OAAO,WAGO8yB,GAAmBC,GAC8B,iBAAPA,GACtDtoB,QAAQC,2LD1LZ,SAAY8mB,GACVA,gCACAA,sDACAA,wDACAA,yCACAA,yCACAA,qDANF,CAAYA,KAAAA,QCiML,MAAMwB,GAA6BzzB,IACxC,IAAKA,EAAS,OACd,MAAM0zB,QAAmB1zB,SAAAA,EAAUiyB,GAAU0B,eAE7C,GAAID,EAAkB,CAAA,UAKpB,MAAME,2BACHF,EAAAA,EAAoB,IAAIG,MAAM,MAAM,WAArCC,EAAyCD,gBAAzCC,EAAyCD,MAAQ,aAAjDE,EAAwD,MAAM,GAC1DC,EDnMkC,kCCoMtCN,SAAAA,EAAkBO,QACdC,EAA2B,CAC/BC,cAAe,CACbH,eAAAA,EACAJ,WAAAA,IAGgB,MASpB,OATII,IACFE,EAAYE,aAAe,CACzBC,WAAYr0B,EAAQiyB,GAAUqC,yBAC1BC,OAAOv0B,EAAQiyB,GAAUqC,0BACzB,EACJE,qBAAax0B,EAAQiyB,GAAUwC,6BAA6B,GAC5DC,KAAM10B,EAAQiyB,GAAU0C,qBAGrBT,QCxLCU,IAAAA,GAAAA,2BAAAA,sDAEVA,iBACAA,2BACAA,yBCzDK,MAAMC,GAAMC,EAAY,OC2ClBC,GAAU,SACrBr0B,EACAs0B,GAEA,GAAIpwB,EAASlE,GACX,MAAM,IAAID,EAAe,6CAG3B,OA/BF,SAAgBwE,EAAQ+vB,GACtB,OAAKA,EAIE/vB,EAAEgwB,SAlBX,SAAgBC,GACd,OAAQA,GACN,IAAK,QACH,OAAOC,EACT,IAAK,SACH,OAAOC,EACT,IAAK,MACH,OAAOC,EACT,QACE,MAAM,IAAI50B,EAAe,gCASX60B,CAAON,IAHhB/vB,EA6BFswB,CAAOC,EAAc90B,GAAUs0B,ICnDxC,SAASS,GAAOxwB,EAAQ+vB,GACtB,OAAKA,EAGE/vB,EAAEwwB,OAAOT,GAFP/vB,EAAEwwB,SCWb,IAAIC,GAAS,KAEXA,8BDRwB,SACxB1zB,EACAtB,EACAs0B,GAEA,OAAOS,GAAOC,EAAOC,WAAW,SAAU3zB,GAAKytB,OAAO/uB,GAAUs0B,eAGxC,SACxBt0B,EACAs0B,GAEA,OAAOS,GAAOC,EAAOE,WAAW,UAAUnG,OAAO/uB,GAAUs0B,YAGtC,SACrBt0B,EACAs0B,GAEA,OAAOS,GAAOC,EAAOE,WAAW,OAAOnG,OAAO/uB,GAAUs0B,UAGrC,SACnBa,EACAC,GAEA,OAAOjxB,OAAOkxB,KAAKF,EAAKC,cAGD,SACvBD,EACAb,GAEA,OAAOa,EAAIZ,SAASD,KCpBtB,iBAAQgB,cAAYC,WAAYlB,SAAShqB,aAAOhB,IAAc2rB,GCyDvDn0B,eAAe20B,GAA2B5pB,GAC/C,MAAMc,SAAEA,EAAF+oB,WAAYA,EAAZ/D,KAAwBA,EAAxBgE,iBAA8BA,GAAmB,GAAU9pB,EAC3DtM,EAAU+D,EAAoBuI,EAAMtM,SAC1CsM,EAAMtM,QAAUA,EAChBqJ,EAAmBiD,EAAO,CACxB,eACA,gBACA,UACA,eAGF,MAAM+lB,EAAOF,GAAQC,GAIrB,GAHIC,GAAqC,MAA7BryB,EAAQ,oBAClBA,EAAQ,kBAAoBqyB,EAAKgE,QAAQ,IAEvCD,GAA8C,MAA1Bp2B,EAAQ,eAE9B,GAEE8E,EAAWstB,IACX9lB,EAAMmmB,gBACN,CACA,MAAM6D,EAAYhqB,EAAMmmB,kBACxB,GAAI6D,EAAW,CACb,IAAIC,EAAa1xB,OAAOkxB,KAAK,IAC7B,UAAW,MAAM5G,KAASmH,EACxBC,EAAa1xB,OAAO2xB,OAAO,CACzBD,EACiB,iBAAVpH,EAAqBtqB,OAAOkxB,KAAK5G,GAASA,IAGrD,MAAMsH,EAAM1B,GAAQwB,EAAY,UAChCv2B,EAAQ,eAAiBy2B,QAG3BvrB,QAAQC,8CAIZ,MAAMyB,EAAYulB,GAAQ7lB,EAAM8lB,KAAMpyB,GAChC02B,EAA8B,MAAb9pB,EAClB8pB,IAAmBpqB,EAAMqqB,2BAA4BrqB,EAAMsqB,UAC9D1rB,QAAQC,qJAKV,IAAI0rB,EAAgB,EACpB,MAAMF,yBAAEA,EAAFC,SAA4BA,GAAatqB,EACzCwqB,EAAsB,CAC1BC,EACAC,EAAsB,KAGtB,IAAKN,GAAkBM,EAAc,EACnC,OAEF,IAAKL,IAA6BC,EAChC,OAEFC,GAAiBG,QAEjBL,GAAAA,EAA2B,CACzBI,KAAAA,EACAC,YAAAA,EACAH,cAAAA,EACAI,WAAYrqB,IAGd,MAAMsqB,EACc,IAAdtqB,EACEmqB,IAASnC,yBAAiBuC,QACrB,EAEF,EAEFN,EAAgBjqB,EAEH,IAAlBsqB,EACEH,IAASnC,yBAAiBuC,gBAC5BP,GAAAA,EAAWM,UAKbN,GAAAA,EAAWM,IAGTE,QAAmB9E,GAAiB,CACxCF,KAAM9lB,EAAM8lB,KACZI,qBAAuBZ,GAAMkF,EAAoBlC,yBAAiByC,GAAIzF,GACtEoB,YAAa1mB,EAAM0mB,YACnBP,gBAAiBnmB,EAAMmmB,gBACvBQ,UAAW5yB,KAAKi3B,KAAKrE,UACrBnD,YAAaxjB,EAAMwjB,cAGrBgH,EAAoBlC,yBAAiB2C,SACrC,MA4CO9yB,EAAK+yB,SAAajzB,EA5CZhD,WACX,MAAMi2B,QAAYn3B,KAAKoM,aACrBH,EACA,MACA,CAAE6pB,WAAAA,EAAY/oB,SAAAA,GACdpN,EACAo3B,EAAWhF,KACX,CACEqF,eAAiBD,KACfrB,WAAAA,EACAuB,KAAMF,EAAIx3B,QAAQ23B,KAClB9wB,qBAAsB2wB,EAAIx3B,QAAQ,gCAClC8G,yBACE0wB,EAAIx3B,QAAQ,qCACd43B,0BACEJ,EAAIx3B,QAAQ,2CACd+G,cACEywB,EAAIx3B,QAAQ,mDACdiH,WACEuwB,EAAIx3B,QAAQ,iDACd63B,cAAeL,EAAIx3B,QAAQ,0BAE7B83B,UAAW,CACTC,gBAAkB,CAChB/E,YAAa,KACX6D,EAAgB,QAChBO,EAAWpE,aAAXoE,EAAWpE,eAEbP,gBAAiB2E,EAAW3E,iBAE9BuF,iBAAmBC,IACjBnB,EACElC,yBAAiByC,GACjBY,EAAMC,OAASrB,OASzB,OAHIx2B,KAAKi3B,KAAKrE,WAAamE,EAAWltB,KACpCD,EAAsBmtB,EAAWltB,IAAKstB,EAAIx3B,SAErCw3B,GAE0BW,IAWnC,GAAI1zB,IAAQ+yB,EAEV,MADAV,EAAoBlC,yBAAiBwD,QAC/B3zB,EAIR,OADAqyB,EAAoBlC,yBAAiBuC,SAC9BK,EAGFj2B,eAAe82B,GAA0B/rB,GAC9C,OAAO4pB,GAAYoC,KAAKj4B,KAAMiM,GAezB/K,eAAeg3B,GAEpBjsB,WAQA,MAAMksB,QAAqBxtB,EAASsB,EAAMrB,UACpCwtB,WAAQnsB,EAAMosB,UAAU,EACxBC,EAAM1rB,KAAK6kB,IAAI0G,EAAMnG,KAAMoG,YAASnsB,EAAMS,YAAYyrB,EAAMnG,OAC5DI,EAAkBrnB,GAAyB,IAC/CJ,EAAqBsB,EAAMrB,SAAU,CACnCwtB,MAAAA,EACAE,IAAKA,EAAM,KAIf,IACE,aAAazC,GAAYoC,KAAKj4B,KAAM,IAC/BiM,EACH8lB,KAAMK,EAAgBjnB,OACtBxL,QAAS,IACHsM,EAAMtM,SAAW,GACrB44B,qBAAuBD,EAAMF,IAE/BhG,gBAAiBA,EAAgBjnB,OAEnC,MAAO/G,GAEP,MADAgH,GAAWgnB,EAAgBlnB,gBAAiB9G,GACtCA,GCpPHlD,eAAes3B,GAEpBvsB,SAEAA,EAAMtM,iBAAUsM,EAAMtM,WAAW,GACjCqJ,EAAmBiD,EAAO,CAAC,WAAY,cAAe,oBAEtD,MAAMmrB,EAAkB/3B,IAItB,MACMM,EAAUN,EAASM,QACnB84B,EAAwC,CAE1CC,UAAW/4B,EAAQ,oBACnB03B,KAAM13B,EAAO,KACbg5B,OANW1sB,EAAM2sB,QAAU54B,KAAKi3B,KAAK2B,QAAU,GAO/CC,SAAUl5B,EAAO,SACjBm5B,cAAen5B,EAAQ,wBACvBo5B,IAAK9sB,EAAMtK,OAEVtC,EAASC,MAKd,OAHI2M,EAAMrE,WACR6wB,EAAOO,kBAAoBvuB,KAAKf,UAAUrK,EAASC,OAE9Cm5B,GAET,GAAIxsB,EAAMgtB,YAAa,CAAA,MACrB,aAAIhtB,EAAMitB,gBAAOjwB,QAAS,EACxB,MAAM,IAAI7I,+DAIZ,OAAOJ,KAAKoM,aACVH,EACA,OACA,CACEc,SAAUd,EAAMc,UAElB,IACKd,EAAMtM,QACTw5B,qBAAsB,YAExB1G,EACA,CACE2E,eAAAA,IAKN,OAAOp3B,KAAKoM,aACVH,EACA,OACA,CACEc,SAAUd,EAAMc,UAElB,IACKd,EAAMtM,SAEX,CACEy5B,MAAOntB,EAAMitB,MAAM72B,IAAKC,KACtB+0B,KAAM/0B,EAAG+2B,KACTC,WAAYh3B,EAAGwzB,eAGnB,CACEsB,eAAAA,UC/GOmC,WAAwB70B,WACnC4sB,QACEtxB,KAAKuD,KAAK,WCwGFi2B,IAAAA,GAAAA,0BAAAA,+FAEVA,mEACAA,+CACAA,6CACAA,+CACAA,yEACAA,uEAuEF,MAEMC,GAA0B,CAAC,IAAK,IAAK,KAEpCv4B,eAAew4B,GAEpBztB,aAEA,MAAM0tB,YAAEA,EAAF5D,iBAAeA,GAAmB,GAAU9pB,EAC5CtM,EAAU+D,EAAoBuI,EAAMtM,SAC1CsM,EAAMtM,QAAUA,EAChBqJ,EAAmBiD,EAAO,CACxB,eACA,eACA,qBACA,kBACA,kBACA,cACA,UAEA,MACA,mBACA,YACA,eACA,gBAEA,gBACA,UACA,aACA,uBACA,2BACA,OACA,0BACA,iBAGF,MAAM2tB,EAAW,IAAMD,KAAiBA,EAAYE,OACpD3G,GAAmBjnB,EAAM6tB,YAEzB,MAAMC,OAAgC,UAGZ,iBAAf9tB,EAAM+tB,KAENrvB,EAASsB,EAAM+tB,MAEjB,KAP6B,GAUhCC,OAAiB,WACrB,MAAMD,KAAEA,GAAS/tB,EACjB,GAAI8tB,EACF,OAAOA,EAAU/H,KAEnB,GAAIztB,EAASy1B,GACX,OAAOA,EAAK/wB,OAEd,GAAI5E,EAAO21B,GACT,OAAOA,EAAKhI,KAEd,MAAM,IAAI5xB,EA3De,gDAgDJ,GAcjB85B,OAA2B,WAE7B,GAAgC,iBAArBjuB,EAAM6tB,WAAyB,CACxC,MAAMA,WAAEA,GAAe7tB,EAEvB,IAAIkuB,EAA+B,KACnC,IACEA,QAAuBxvB,EAASmvB,GAChC,MAAOM,GAEP,MAAMh2B,EAAMg2B,EACZ,GAAiB,WAAbh2B,EAAI3E,KAGN,MAAM2E,EAIV,MAAMi2B,EACAF,EACKA,EAAeE,cAEjBP,EAAWQ,SAAS,KAKvB1vB,EAAWyvB,EACbE,EAAKjwB,QAAQwvB,EA3FgB,mCA6F7BS,EAAKjwB,QAAQwvB,GACXU,EAAUD,EAAKE,QAAQ7vB,GAI7B,SAFMD,EAAuB6vB,GAEzBH,EACF,MAAO,CACLzvB,SAAAA,EACA8vB,uBAAuB,GAI3B,IAIE,MAAO,CACL9vB,SAAAA,EACA8vB,uBAAuB,EAGvBC,OARaR,QACL3vB,GAAwBI,QAC9B6nB,GAQJ,MAAOzC,GAIP,MAHAnlB,QAAQC,KACN,4EAEIklB,GAKZ,MAAgC,iBAArB/jB,EAAM6tB,WACR,CACLa,OAAQ1uB,EAAM6tB,YAIX,IApEwB,QAwE3B,iBACJ,GAAIC,YAAaG,EAAmBS,SAAnBC,EAA2BC,UAAW,CAAA,MACrD,MAAMC,cAAEA,EAAFC,UAAiBA,YAAcb,EAAmBS,eAAnBK,EAA2BH,UAC5Dd,EAAUkB,UAAYH,GAAiBf,EAAU/H,OAAS+I,IAC5DlwB,QAAQC,yCAC8B,IAAI4lB,KACtCoK,4FAGGZ,EAAmBS,UAT1B,GAcN,MAAMjuB,EAAWJ,GACf2tB,EACAhuB,EAAMS,oBAAYwtB,EAAmBS,eAAnBO,EAA2BC,YrBhIhB,UqBiI7B,GAKAjB,EAAmBS,QACnBT,EAAmBS,OAAOQ,YAAczuB,IAExC7B,QAAQC,KACN,2JAGKovB,EAAmBS,QAG5B,IAAI/B,EAAS3sB,EAAM2sB,QAAU54B,KAAKi3B,KAAK2B,QAAU,GACjD,MAAMj3B,EAAMsK,EAAMtK,IAClB,IAAIoL,EAAW,GACXquB,EAAgB,GACpB,MAAMC,EAwYR,SAAqB9uB,EAAmBG,GACtC,MAAM0uB,EAAgB,GACtB,IAAK,IAAIE,EAAI,KAAOA,EAAG,CACrB,MAAMjD,EAASiD,EAAI5uB,EACb6uB,EAAe3uB,KAAK6kB,IAAI/kB,EAAUH,EAAY8rB,GAQpD,GANA+C,EAAM73B,KAAK,CACT80B,OAAAA,EACA3rB,SAAU6uB,EACVzF,WAAYwF,EAAI,KAGbA,EAAI,GAAK5uB,GAAYH,EACxB,MAIJ,OAAO6uB,EAzZkBI,CAAYvB,EAAUvtB,GACzC+uB,aAAqBvB,EAAmBS,iBAAQe,aAAc,IACjEC,OAAQr5B,GAAOA,EAAGs5B,cAClBp5B,OAAO,CAAC+E,EAAMjF,IAAOiF,EAAOjF,EAAG64B,UAAW,GAC7C,IAAIU,EAA2BJ,EAG/B,MAAMK,YAAgB5B,EAAmBS,iBAAQe,aAAc,GACzDK,EAAqD,IAAIC,IAC/DF,EAAcx4B,QAAShB,GAAOy5B,EAAgB/5B,IAAIM,EAAG25B,YAAa35B,IAElE,MAAM45B,EAAuB,KAC3B,MAAMC,EAAsC,CAC1CvD,OAAAA,EACAj3B,IAAAA,EACAw5B,UAAWzuB,EACX0vB,UAAWrvB,EACX2uB,WAAYI,GAQd,OANI/B,IACFoC,EAAkBtB,UAAY,CAC5BC,cAAef,EAAUkB,QACzBF,UAAWhB,EAAU/H,OAGlBmK,GAEHE,EACJ/6B,IAEA,IAAK2K,EAAMqwB,kBACT,OAGF,MAAM1E,EAAqB,CACzBgB,OAAAA,EACA7rB,SAAAA,EACApL,IAAAA,KACGL,GAED44B,EAAmBtvB,WACrBgtB,EAAM2E,eAAiBrC,EAAmBtvB,UAG5CqB,EAAMqwB,kBAAkB1E,IAE1B,IAAK4E,GAAL,SAAKA,GACHA,qBACAA,6CACAA,uEAHF,CAAKA,IAAAA,OAKL,MAAMC,EAAwB/F,IACvBzqB,EAAMsqB,WAYTsF,IAA6B5B,GAC7BvD,IAAS8F,EAAyBE,mBAIlCzwB,EAAMsqB,SAZFG,IAAS8F,EAAyBpE,OAAsB,IAAb6B,EACtC,EAEDA,EAAe4B,EAA2B5B,EAA/B,EASKiC,OAG5B,IAAI1F,EAAgBiF,EACpB,MAAMnF,yBAAEA,GAA6BrqB,EAC/BwqB,EAAsB,CAC1BC,EACAC,EAAsB,KAEjBL,IAGLE,GAAiBG,QAEjBL,GAAAA,EAA2B,CACzBI,KAAAA,EACAC,YAAAA,EACAH,cAAAA,EACAI,WAAYqD,MAGV0C,EAAsBzyB,GAAoBhJ,UAC9C,GAEEg5B,EAAmBtvB,SACnB,CACA,MAAMgyB,EAAUnyB,KAAKf,UAAUwyB,IAAwB,KAAM,GACvD1B,EAAUD,EAAKE,QAAQP,EAAmBtvB,gBAC1CD,EAAuB6vB,SACvB7vB,EAAcuvB,EAAmBtvB,SAAUgyB,EAAS,YAwBxDC,EAAwB37B,MAC5B42B,EACAgF,KASA,IAAIC,EAAkBhB,EAAgBl6B,IAAIi2B,EAAKhC,YAC1CiH,IACHA,EAAkB,CAChBd,YAAanE,EAAKhC,WAClBuC,OAAQP,EAAKO,OACb8C,UAAWrD,EAAKprB,SAChBkvB,cAAc,EACdtE,KAAM,GACN0F,eAAgB,IAElBlB,EAAcv4B,KAAKw5B,GACnBhB,EAAgB/5B,IAAI+6B,EAAgBd,YAAac,IAG9CD,EAAc14B,MACjB24B,EAAgBnB,cAAe,EAC/BmB,EAAgBzF,KAAOwF,EAAc3F,IAAIE,KACzC0F,EAAgBC,eAAiBF,EAAc3F,IAAIK,qBAG/CmF,IACN,MAAMM,EAAiC,CACrCnH,WAAYiH,EAAgBd,YAC5BvvB,SAAUqwB,EAAgB5B,UAC1B9C,OAAQ0E,EAAgB1E,QAG1B,GAAIyE,EAAc14B,IAAK,CACrB,MAAMA,EAAM04B,EAAc14B,IAC1B,IAAIsyB,EAAwB8C,wBAAgB0D,iBAa5C,OAXI94B,aAAelF,GACbu6B,GAAwB0D,SAAS/4B,EAAI1E,cACvCg3B,EAAO8C,wBAAgB4D,wBAI3Bf,EAAmB,CACjB3F,KAAAA,EACAtyB,IAAAA,EACA64B,eAAAA,IAKJA,EAAe3F,KAAOwF,EAAc3F,IAAIE,KACxCwE,GAA4BoB,EAAevwB,SAE3C2vB,EAAmB,CACjB3F,KAAM8C,wBAAgB6D,kBACtBJ,eAAAA,IAEFR,EAAqBD,EAAyBE,oBAGhD,GAAIxC,EAAmBS,OAAQ,CAC7B/B,EAASsB,EAAmBS,OAAO/B,OACnC7rB,EAAWmtB,EAAmBS,OAAOyB,UAGrC,MAAMkB,EAA+B,IAAIC,KACtCrD,EAAmBS,OAAOe,YAAc,IACtCC,OAAQr5B,GAAOA,EAAGs5B,cAClBv5B,IAAKC,GAAOA,EAAG25B,cAEpBb,EAAQC,EAASM,OAAQr5B,IAAQg7B,EAAgBE,IAAIl7B,EAAGwzB,iBACnD,CAEL,IACE,MAAQx2B,KAAMm+B,SAAuBzxB,GAAsBisB,KACzDj4B,KACAiM,GAEF,GAAI2tB,IACF,MAAM,IAAIt5B,EAAY,qBAKsB,MAF9Cs4B,EAAS6E,EAAa9E,OACtB5rB,EAAW0wB,EAAaC,SACpBxD,EAAmBQ,wBACrBR,EAAmBtvB,kBAAWsvB,EAAmBtvB,iBAAnB+yB,EAA6B/6B,0CA0NnE,SAAsCg2B,EAAgBj3B,GAGpD,SAFsBA,KAAO+yB,MAAWkE,KAAUj3B,IAAO,gBACxBiB,QAAQ,SAAU,IA1N3Cg7B,CAA6BhF,EAAQj3B,KAIzC06B,EAAmB,CACjB3F,KAAM8C,wBAAgBqE,+BAExB,MAAOzD,GACP,MAAMh2B,EAAMg2B,EAKZ,MAJAiC,EAAmB,CACjB3F,KAAM8C,wBAAgBsE,4BACtB15B,IAAAA,IAEIA,EAGRg3B,EAAQC,EAGVoB,EAAqBD,EAAyBpE,OA+I9C3B,EAAoBlC,yBAAiB2C,SACrC,MAAO9yB,EAAK+yB,SAAajzB,EA/ILhD,WAClB,IAAI68B,EAAyB,KACzBC,EAAQ,EAgGZ,SA7FM3zB,QAAQ4zB,IACZn8B,MAAM4zB,KAAK,CAAEzsB,OAAQgD,EAAMiyB,SAAW,IAAK77B,IAAInB,UAC7C,OAAa,CACX,MAAMi9B,EAAeH,IACrB,GAAIG,GAAgB/C,EAAMnyB,OACxB,OAGF,MAAMm1B,EAAUhD,EAAM+C,GACtB,IAAIE,EAAwB,EAC5B,MAAMjM,EAAkBkM,GAAmBryB,EAAM+tB,KAAMoE,GACvD,IACE,SAASG,EAAQvE,EAA+BlC,GAC9C,MAAQO,OAAQD,EAAV1rB,SAAiBA,GAAaorB,EAC9BQ,EAAMF,EAAQ1rB,EAEpB,GAAI0lB,EACF,OAAOA,EAAgBjnB,OAGzB,GAAI9G,EAAO21B,GACT,OAAOA,EAAK/2B,MAAMm1B,EAAOE,GAE3B,GAAI/zB,EAASy1B,GACX,OAAOA,EAAK/2B,MAAMm1B,EAAOE,GAE3B,MAAM,IAAIl4B,EApbK,+CAubjB,MAAQd,KAAMw9B,SAAwBjH,GAAYoC,KAAKj4B,KAAM,CAC3D44B,OAAAA,EACAj3B,IAAAA,EACAoL,SAAAA,EACAglB,KAAMwM,EAAQtyB,EAAM+tB,KAAMoE,GAC1BrI,iBAAAA,EACA3D,sBAAiBA,SAAAA,EAAiBjnB,KAClCwnB,YAAa,KACX6D,GAAiB6H,EACjBA,EAAwB,GAE1BvI,WAAYsI,EAAQtI,WACpBn2B,QAAS,CACP44B,oBAAuB6F,EAAQ1xB,SAC/B8xB,kDACE7+B,EAAQ,mDACV8+B,4CACE9+B,EAAQ,6CACV++B,gDACE/+B,EAAQ,kDAEZ22B,yBAAyBn2B,GACnBA,EAAOu2B,OAASnC,yBAAiByC,KAGjC4C,MAGJyE,GAAyBl+B,EAAOw2B,YAChCF,EAAoBt2B,EAAOu2B,KAAMv2B,EAAOw2B,gBAE1ChvB,aAAcsE,EAAMtE,aACpB8nB,YAAaxjB,EAAMwjB,cAGrB,GAAImK,IACF,MAAM,IAAIt5B,EAAY,2BAGlBu8B,EAAsBuB,EAAS,CAAEjH,IAAK2F,IAC5C,MAAO1C,GACPhvB,SAAWgnB,SAAAA,EAAiBlnB,gBAAiBkvB,GAE7C,MAAMh2B,EAAMg2B,EAIZ,GAHA5D,GAAiB6H,EACjBA,EAAwB,EAEpBv5B,EAAcV,GAChB,MAAMA,EAGR,GAAIw1B,IACF,MAAM,IAAIt5B,EAAY,qBAGnBy9B,IACHA,EAAW35B,SAEPy4B,EAAsBuB,EAAS,CAAEh6B,IAAAA,SAM3C25B,EACF,MAAMA,EAGR,MAAM7E,GAASgD,IAAuBR,YAAc,IAAIr5B,IAAKC,KAC3D+2B,KAAM/2B,EAAGg1B,KACTxB,WAAYxzB,EAAG25B,gBAGV73B,EAAK+yB,SAAajzB,EACvBs0B,GAAwBP,KAAKj4B,KAAM,CACjC44B,OAAAA,EACAj3B,IAAAA,EACAoL,SAAAA,EACAmsB,MAAAA,KAIJ,GAAI90B,IAAQ+yB,EAIV,MAHAkF,EAAmB,CACjB3F,KAAM8C,wBAAgBmF,gCAElBv6B,EAWR,GARAi4B,EAAmB,CACjB3F,KAAM8C,wBAAgBoF,iCAExBnC,EACED,EAAyBqC,qCArQJ39B,WAGrBg5B,EAAmBtvB,gBAEbD,EAAOuvB,EAAmBtvB,UAAUk0B,MAAO16B,IAE/CyG,QAAQC,KACN,wFACyBovB,EAAmBtvB,aAC5CxG,EAAI/D,YA6PJ0+B,GAGJ/+B,KAAKi3B,KAAKrE,WACVuE,EAAI73B,KAAKw5B,eAqEf,SAA2B3F,aACzB,MAAMnB,YAAOmB,EAAG0H,oBAAWE,YAAa,EACxC,IAAI5D,EAAM,IACV,MAAM6H,oBACJ7L,EAAGuI,mBAAHuD,EAAe57B,YAAf47B,EAAe57B,KAAO,CAAC67B,EAAGrwB,IAAMqwB,EAAEjD,YAAcptB,EAAEotB,gBAAgB,GACpE,IAAK,MAAMkD,KAAQH,EACjB7H,EAAM5H,GACJ4H,EACAgI,EAAKnC,eACLpwB,KAAK6kB,IAAI0N,EAAKhE,UAAWnJ,EAAOmN,EAAK9G,SAGzC,OAAOlB,EAhFHiI,CAAkBlD,OAA4B/E,EAAI73B,KAAKw5B,cAEvD,MAAM,IAAI14B,EAAe,gCAG3B,OAAO+2B,GAI0BkI,IACnC,GAAIj7B,IAAQ+yB,EAEV,MADAV,EAAoBlC,yBAAiBwD,QAC/B3zB,EAGR,OADAqyB,EAAoBlC,yBAAiBuC,SAC9BK,EA4BT,SAASmH,GAAmBtE,EAA+BlC,GACzD,MAAQO,OAAQD,EAAV1rB,SAAiBA,GAAaorB,EAC9BQ,EAAMF,EAAQ1rB,EAEpB,GAAiE,iBAATstB,EACtD,OAAOjvB,GAAyB,IACzB2B,EAGE/B,EAAqBqvB,EAAM,CAChC5B,MAAAA,EACAE,IAAKA,EAAM,IAJJ,IAAIiB,QCxwBP+F,GAeAC,GAUAC,GAKAC,GAKAC,GAYAC,GAKAC,GAKAC,GAQAC,GAKAC,GAOAC,GAKAC,GAKAC,GAKAC,GAMAC,GAkBAC,GAUAC,GASAC,GClDAC,GC/BLt/B,eAAeu/B,GAEpBx0B,GAEA,MAAMy0B,EAAmC,iBAAVz0B,EAAqB,CAAEtK,IAAKsK,GAAUA,EAC/DtM,EAAU+D,EAAoBg9B,EAAgB/gC,SACpD+gC,EAAgB/gC,QAAUA,EAE1B,MAAMwD,EAA6B,GAenC,OAdIu9B,EAAgBp4B,YAClBnF,EAAMmF,UAAYo4B,EAAgBp4B,WAGpCU,EAAmB03B,EAAiB,CAClC,UACA,kBACA,cACA,oBACA,gBACA,UACA,eAGK1gC,KAAKoM,aACVH,EACA,OACA9I,SACAu9B,SAAAA,EAAiB/gC,UAAW,QAC5B8yB,EACA,CACE2E,eAAiBD,IACf,MAAMsB,EAAS,IACVtB,EAAIx3B,QACPghC,kBAAmBxJ,EAAIx3B,QAAQiyB,GAAUgP,0BAErCroB,EAAO6a,GAA0B+D,EAAIx3B,SAK3C,OAHI4Y,IACFkgB,EAAOoI,YAActoB,GAEhBkgB,KCzCRv3B,eAAe4/B,GAEpB70B,GAEA,MAAMc,SAAEA,EAAF+oB,WAAYA,GAAe7pB,EAC3BtM,EAAU+D,EAAoBuI,EAAMtM,SAY1C,GAXAsM,EAAMtM,QAAUA,EAChBqJ,EAAmBiD,EAAO,CACxB,kBACA,0BACA,oBACA,uBACA,gBACA,UACA,aACA,iBAEEA,EAAM8mB,WAAa9mB,EAAM+mB,OAAQ,CAAA,MACnC,IAAI+N,EAAajO,GAAyB7mB,EAAM8mB,UAAW9mB,EAAM+mB,QAC7D/mB,EAAM+0B,eACRD,iBAA4B90B,EAAM+0B,cAEpCrhC,EAAQ,8BAAuBA,EAAQ,wBAAwBohC,EAGjE,GAC2B,MAAzB90B,EAAMpF,kBACyB,MAA9BoF,EAAMg1B,sBAA4D,MAA5Bh1B,EAAMi1B,oBAC7C,CAAA,MACA,MAIMC,WAH0B,MAA9Bl1B,EAAMg1B,wBAAkCh1B,EAAMg1B,qBAAyB,MAE3C,MAA5Bh1B,EAAMi1B,sBAAgCj1B,EAAMi1B,mBAAuB,KAErEvhC,EAAQ,oCACNA,EAAQ,8BAA8BwhC,EAG1C,MAAO/8B,EAAK+yB,SAAajzB,EACvBlE,KAAKoM,aACHH,EACA,MACA,CAAE6pB,WAAAA,EAAY/oB,SAAAA,GACdpN,OACA8yB,EACA,CACE2E,eAAe/3B,IACN,IACFA,EAASC,KACZ8hC,cACE/hC,EAASM,QAAQsF,EAAiB,eACpCo8B,WACEhiC,EAASM,QAAQsF,EAAiB,iBAO9C,GAAIb,IAAQ+yB,IAAQA,EAAI73B,KAAK+3B,KAE3B,MAAMjzB,EAGR,OAAO+yB,ECjCFj2B,eAAeogC,GAEpBr1B,GAEA,MAAMtM,EAAU+D,EAAoBuI,EAAMtM,SAqC1C,GApCAsM,EAAMtM,QAAUA,EAChBqJ,EAAmBiD,EAAO,CACxB,eACA,qBACA,kBACA,kBACA,cACA,UAEA,oBACA,4BACA,wBACA,8BACA,0BACA,oBACA,uBAEA,MACA,mBACA,YACA,eACA,gBAEA,gBACA,UACA,aACA,uBAEA,oBACA,OACA,0BACA,eACA,eACA,kBACA,YAEEA,EAAM8mB,WAAa9mB,EAAM+mB,OAAQ,CAAA,MACnC,IAAI+N,EAAajO,GAAyB7mB,EAAM8mB,UAAW9mB,EAAM+mB,QAC7D/mB,EAAM+0B,eACRD,iBAA4B90B,EAAM+0B,cAEpCrhC,EAAQ,8BAAuBA,EAAQ,wBAAwBohC,EAGjE,MAAO38B,EAAK+yB,SAAajzB,EACvBlE,KAAKoM,aAA6BH,EAAO,MAAO,GAAItM,IAGtD,GAAIyE,IAAQ+yB,IAAQA,EAAI73B,KAAK+3B,KAE3B,MAAMjzB,EAER,OAAO+yB,GJ7IGmI,GAAAA,kBAAAA,0CAEVA,+BACAA,0CACAA,6CACAA,0CACAA,yDAEAA,oDAIAA,yBAGUC,GAAAA,2BAAAA,8DAGVA,uBACAA,sCACAA,0CACAA,wDACAA,kCAGUC,GAAAA,gCAAAA,gEAEVA,uCAGUC,GAAAA,2BAAAA,+DAEVA,mCAGUC,GAAAA,yBAAAA,kDAEVA,2BACAA,gCACAA,kCACAA,wCAIAA,0CAGUC,GAAAA,sBAAAA,8CAEVA,gCAGUC,GAAAA,qBAAAA,kDAEVA,kDAGUC,GAAAA,yBAAAA,gDAEVA,uBACAA,yBACAA,6BACAA,0BAGUC,GAAAA,2CAAAA,iHAEVA,6DAGUC,GAAAA,gCAAAA,uDAEVA,qBACAA,mBACAA,sBAGUC,GAAAA,8BAAAA,mDAEVA,wBAGUC,GAAAA,uBAAAA,0CAEVA,kBAGUC,GAAAA,qBAAAA,0CAEVA,wBAGUC,GAAAA,mBAAAA,8CAEVA,6BACAA,oBAGUC,GAAAA,+BAAAA,oDAEVA,yBACAA,aAKAA,oBAIAA,eAMUC,GAAAA,gCAAAA,iDAEVA,uBACAA,qBACAA,wBAMUC,GAAAA,yCAAAA,gFAEVA,qCACAA,wCAMUC,GAAAA,2CAAAA,2EAEVA,+BCpDUC,GAAAA,iCAAAA,sGAEVA,mEACAA,uDACAA,qDACAA,uDACAA,yEACAA,uEAiEF,MACM/G,GAA0B,CAAC,IAAK,IAAK,KAGpCv4B,eAAeqgC,GAEpBt1B,aAEA,MAAM0tB,YAAEA,GAAgB1tB,EAClB2tB,EAAW,IAAMD,KAAiBA,EAAYE,OACpD3G,GAAmBjnB,EAAM6tB,YAEzB,MAAQx6B,KAAMkiC,SAAsBf,GAAWxI,KAAKj4B,KAAM,CACxD44B,OAAQ3sB,EAAM8mB,UACdpxB,IAAKsK,EAAM+mB,OACX1qB,UAAW2D,EAAMw1B,eAEbnK,EAAOkK,EAAW,KAClBE,GAAcF,EAAY,kBAE1BtH,OAA2B,WAE7B,GAAgC,iBAArBjuB,EAAM6tB,WAAyB,CACxC,MAAMA,WAAEA,GAAe7tB,EAEvB,IAAIkuB,EAA+B,KACnC,IACEA,QAAuBxvB,EAASmvB,GAChC,MAAOM,GAEP,MAAMh2B,EAAMg2B,EACZ,GAAiB,WAAbh2B,EAAI3E,KAGN,MAAM2E,EAIV,MAAMi2B,EACAF,EACKA,EAAeE,cAEjBP,EAAWQ,SAAS,KAIvB1vB,EAAWyvB,EACbE,EAAKjwB,QAAQwvB,EA/CgB,mCAgD7BS,EAAKjwB,QAAQwvB,GACXU,EAAUD,EAAKE,QAAQ7vB,GAI7B,aAFMD,EAAuB6vB,GAEzBH,EACK,CACLzvB,SAAAA,EACA8vB,uBAAuB,GAIpB,CACL9vB,SAAAA,EACA8vB,uBAAuB,EAGvBC,OAAQR,QACE3vB,GAAwBI,QAC9B6nB,GAKV,MAAgC,iBAArBxmB,EAAM6tB,WACR,CACLa,OAAQ1uB,EAAM6tB,YAIX,IA1DwB,QA8D3B,iBACJ,YAAII,EAAmBS,SAAnBC,EAA2B+G,wBAAyB,CAAA,MACtD,MAAM7G,cAAEA,EAAF8G,YAAiBA,YACrB1H,EAAmBS,eAAnBK,EAA2B2G,wBAG3BH,EAAY,mBAAqB1G,IAChC0G,EAAY,oBAAsBI,IAEnC/2B,QAAQC,yCAC8B,IAAI4lB,KACtCoK,4FAGGZ,EAAmBS,UAd1B,GAmBN,MAAMjuB,EAAWJ,GACfo1B,EACAz1B,EAAMS,oBAAYwtB,EAAmBS,eAAnBO,EAA2BC,YArGhB,UAsG7B,GAKAjB,EAAmBS,QACnBT,EAAmBS,OAAOQ,YAAczuB,IAExC7B,QAAQC,KACN,2JAGKovB,EAAmBS,QAG5B,IAAI/B,EAAS3sB,EAAM2sB,QAAU54B,KAAKi3B,KAAK2B,QAAU,GACjD,MAAMj3B,EAAMsK,EAAMtK,IAClB,IAAIoL,EAAW,GACXquB,EAAgB,GACpB,MAAMC,EAmaR,SAAqB9uB,EAAmBG,GACtC,MAAM0uB,EAAgB,GACtB,IAAK,IAAIE,EAAI,KAAOA,EAAG,CACrB,MAAMjD,EAASiD,EAAI5uB,EACb6uB,EAAe3uB,KAAK6kB,IAAI/kB,EAAUH,EAAY8rB,GAQpD,GANA+C,EAAM73B,KAAK,CACT80B,OAAAA,EACA3rB,SAAU6uB,EACVzF,WAAYwF,EAAI,KAGbA,EAAI,GAAK5uB,GAAYH,EACxB,MAIJ,OAAO6uB,EApbkBI,CAAYkG,EAAYh1B,GAQjD,IAAImvB,aAPuB3B,EAAmBS,iBAAQe,aAAc,IACjEC,OAAQr5B,GAAOA,EAAGs5B,cAClBp5B,OACC,CAAC+E,EAAMjF,IACLiF,EAAOjF,EAAGu/B,sBAAwBv/B,EAAGw/B,wBAA0B,EACjE,GAKJ,MAAMhG,YAAgB5B,EAAmBS,iBAAQe,aAAc,GACzDK,EACJ,IAAIC,IACNF,EAAcx4B,QAAShB,GAAOy5B,EAAgB/5B,IAAIM,EAAG25B,YAAa35B,IAElE,MAAM45B,EAAuB,KAC8B,CACvDtD,OAAAA,EACAj3B,IAAAA,EACAw5B,UAAWzuB,EACX0vB,UAAWrvB,EACX2uB,WAAYI,EACZ6F,wBAAyB,CACvB7G,cAAe0G,EAAY,iBAC3BlK,KAAMkK,EAAYlK,KAClB0F,eAAgBwE,EAAY,yBAA2B,GACvDI,aAAcJ,EAAY,qBAK1BnF,EACJ/6B,IAKA,IAAK2K,EAAM81B,kBACT,OAGF,MAAMnK,EAA4B,CAChCgB,OAAAA,EACA7rB,SAAAA,EACApL,IAAAA,KACGL,GAED44B,EAAmBtvB,WACrBgtB,EAAM2E,eAAiBrC,EAAmBtvB,UAG5CqB,EAAM81B,kBAAkBnK,IAE1B,IAAK4E,GAAL,SAAKA,GACHA,qBACAA,6CACAA,uEAHF,CAAKA,IAAAA,OAKL,MAAMC,EAAwB/F,IACvBzqB,EAAMsqB,WAYTsF,IAA6B6F,GAC7BhL,IAAS8F,EAAyBE,mBAIlCzwB,EAAMsqB,SAZFG,IAAS8F,EAAyBpE,OAAwB,IAAfsJ,EACtC,EAEDA,EAAiB7F,EAA2B6F,EAA/B,EASGxF,OAItBS,EAAsBzyB,GAAoBhJ,UAC9C,GAEEg5B,EAAmBtvB,SACnB,CACA,MAAMgyB,EAAUnyB,KAAKf,UAAUwyB,IAAwB,KAAM,GACvD1B,EAAUD,EAAKE,QAAQP,EAAmBtvB,gBAC1CD,EAAuB6vB,SACvB7vB,EAAcuvB,EAAmBtvB,SAAUgyB,EAAS,YAwBxDC,EAAwB37B,MAC5B42B,EACAgF,KASA,IAAIC,EAAkBhB,EAAgBl6B,IAAIi2B,EAAKhC,YAC/C,MAAMkM,EAAalK,EAAKO,OAClB4J,EAAWr1B,KAAK6kB,IAAIqG,EAAKO,OAAS3rB,EAAW,EAAGg1B,EAAa,GAC9D3E,IACHA,EAAkB,CAChBd,YAAanE,EAAKhC,WAClBgM,wBAAyBE,EACzBH,sBAAuBI,EACvBrG,cAAc,EACdtE,KAAM,IAERwE,EAAcv4B,KAAKw5B,GACnBhB,EAAgB/5B,IAAI+6B,EAAgBd,YAAac,IAG9CD,EAAc14B,MACjB24B,EAAgBnB,cAAe,EAC/BmB,EAAgBzF,KAAOwF,EAAc3F,IAAIE,YAGrCsF,IACN,MAAMuF,EAA6B,CACjCpM,WAAYiH,EAAgBd,YAC5BiF,mBAAoBnE,EAAgB8E,sBACpCZ,qBAAsBlE,EAAgB+E,yBAGxC,GAAIhF,EAAc14B,IAAK,CACrB,MAAMA,EAAM04B,EAAc14B,IAC1B,IAAIsyB,EACF8J,+BAAuB2B,qBAazB,OAXI/9B,aAAelF,GACbu6B,GAAwB0D,SAAS/4B,EAAI1E,cACvCg3B,EAAO8J,+BAAuB4B,4BAIlC/F,EAAmB,CACjB3F,KAAAA,EACAtyB,IAAAA,EACA89B,aAAAA,IAKJA,EAAa5K,KAAOwF,EAAc3F,IAAIE,KACtCwE,GACEqG,EAAahB,mBAAqBgB,EAAajB,qBAAuB,EAExE5E,EAAmB,CACjB3F,KAAM8J,+BAAuB6B,sBAC7BH,aAAAA,IAEFzF,EAAqBD,EAAyBE,oBAGhD,GAAIxC,EAAmBS,OAAQ,CAC7B/B,EAASsB,EAAmBS,OAAO/B,OACnC7rB,EAAWmtB,EAAmBS,OAAOyB,UAGrC,MAAMkB,EAA+B,IAAIC,KACtCrD,EAAmBS,OAAOe,YAAc,IACtCC,OAAQr5B,GAAOA,EAAGs5B,cAClBv5B,IAAKC,GAAOA,EAAG25B,cAEpBb,EAAQC,EAASM,OAAQr5B,IAAQg7B,EAAgBE,IAAIl7B,EAAGwzB,iBACnD,CAEL,IACE,MAAQx2B,KAAMm+B,SAAuBzxB,GAAsBisB,KACzDj4B,KACAsiC,EAAUr2B,IAEZ,GAAI2tB,IACF,MAAM,IAAIt5B,EAAY,qBAKsB,MAF9Cs4B,EAAS6E,EAAa9E,OACtB5rB,EAAW0wB,EAAaC,SACpBxD,EAAmBQ,wBACrBR,EAAmBtvB,kBAAWsvB,EAAmBtvB,iBAAnB+yB,EAA6B/6B,0CAiP9C,EAPnBq0B,EAxOqC,IACxBhrB,EACH2sB,OAAAA,IA8OH7F,UACLkE,EAAKjE,OACLiE,EAAKwK,aACLxK,EAAK2B,OACL3B,EAAKt1B,IACL,QAECg6B,OAAO4G,SACP9+B,KAAK,KAEyBb,QAAQ,SAAU,MAnP/Cy5B,EAAmB,CACjB3F,KAAM8J,+BAAuB3C,+BAE/B,MAAOzD,GACP,MAAMh2B,EAAMg2B,EAKZ,MAJAiC,EAAmB,CACjB3F,KAAM8J,+BAAuB1C,4BAC7B15B,IAAAA,IAEIA,EAGRg3B,EAAQC,EAoNZ,IACEpE,EAjCA,OADAwF,EAAqBD,EAAyBpE,OACxB,IAAfsJ,EAzDgBxgC,WACrB,IAAI6/B,EAAajO,GAAyB7mB,EAAM8mB,UAAW9mB,EAAM+mB,QAC7D/mB,EAAMw1B,eACRV,iBAA4B90B,EAAMw1B,cAEpC,MAAM9hC,EAAmB,IACpBsM,EAAMtM,QACT6iC,oBAAuBzB,EACvB0B,6BAAgCnL,IAG3BlzB,EAAK+yB,SAAajzB,EACvBo9B,GAAWrJ,KAAKj4B,KAAM,CACpB44B,OAAQ3sB,EAAM2sB,OACdj3B,IAAKsK,EAAMtK,IACXhC,QAAAA,EACAgI,aAAcsE,EAAMtE,gBAGxB,GAAIvD,IAAQ+yB,EAIV,MAHAkF,EAAmB,CACjB3F,KAAM8J,+BAAuB2B,uBAEzB/9B,EAkBR,OAfAq4B,EACED,EAAyBqC,gCAE3BxC,EAAmB,CACjB3F,KAAM8J,+BAAuB6B,sBAC7BH,aAAc,CACZpM,WAAY,EACZmL,qBAAsB,EACtBC,mBAAoB,KAGxB7E,EAAmB,CACjB3F,KAAM8J,+BAAuB5B,iCAGxB,IACFzH,EACH73B,KAAM,CACJ+3B,KAAMF,EAAIx3B,QAAJ,MAAuB,GAC7Bg5B,OAAQC,EACRG,IAAKp3B,EACLk3B,gBAAiB74B,KAAKi3B,KAAKyL,OAAS,IAAM,QAAQ9J,KAChD54B,KAAKi3B,KAAK0L,YACRhhC,IACJ+2B,UAAWvB,EAAIx3B,QAAQ,oBACvBm5B,cAAe3B,EAAIx3B,QAAQ,2BAMPijC,GAjLN1hC,WAClB,IAAI68B,EAAyB,KACzBC,EAAQ,EAmEZ,SAhEM3zB,QAAQ4zB,IACZn8B,MAAM4zB,KAAK,CAAEzsB,OAAQgD,EAAMiyB,SAAW,IAAK77B,IAAInB,UAC7C,OAAa,CACX,MAAMi9B,EAAeH,IACrB,GAAIG,GAAgB/C,EAAMnyB,OACxB,OAGF,MAAMm1B,EAAUhD,EAAM+C,GACtB,IACE,IAAI4C,EAAajO,GACf7mB,EAAM8mB,UACN9mB,EAAM+mB,QAEJ/mB,EAAMw1B,eACRV,iBAA4B90B,EAAMw1B,cAEpC,MAAMN,WAAqB/C,EAAQ/F,UACjC+F,EAAQ/F,OAAS+F,EAAQ1xB,SAAW,IAEhC/M,EAAmB,IACpBsM,EAAMtM,QACT6iC,oBAAuBzB,EACvB0B,6BAAgCnL,EAChCuL,0BAA6B1B,GAG1B/C,EAAQ1xB,iBACJ/M,EAAQ,2BAEjB,MAAQL,KAAMw9B,SAAwBgE,GAAe7I,KAAKj4B,KAAM,CAC9D44B,OAAAA,EACAj3B,IAAAA,EACAoL,SAAAA,EACA+oB,WAAYsI,EAAQtI,WACpBn2B,QAAAA,EACAgI,aAAcsE,EAAMtE,eAGtB,GAAIiyB,IACF,MAAM,IAAIt5B,EAAY,oCAGlBu8B,EAAsBuB,EAAS,CAAEjH,IAAK2F,IAC5C,MAAO1C,GACP,MAAMh2B,EAAMg2B,EAEZ,GAAIt1B,GAAcV,GAChB,MAAMA,EAGR,GAAIw1B,IACF,MAAM,IAAIt5B,EAAY,8BAGnBy9B,IACHA,EAAW35B,SAEPy4B,EAAsBuB,EAAS,CAAEh6B,IAAAA,SAM3C25B,EACF,MAAMA,EAGR,MAAM7E,GAASgD,IAAuBR,YAAc,IAAIr5B,IAAKC,KAC3D+2B,KAAM/2B,EAAGg1B,KACTxB,WAAYxzB,EAAG25B,gBAGV73B,EAAK+yB,SAAajzB,EACvBs0B,GAAwBP,KAAKj4B,KAAM,CACjC44B,OAAAA,EACAj3B,IAAAA,EACAoL,SAAAA,EACAmsB,MAAAA,KAIJ,GAAI90B,IAAQ+yB,EAIV,MAHAkF,EAAmB,CACjB3F,KAAM8J,+BAAuB7B,gCAEzBv6B,EAGRi4B,EAAmB,CACjB3F,KAAM8J,+BAAuB5B,iCAE/BnC,EACED,EAAyBqC,gCAG3B,MAAMiE,EACJ5G,IAAuByF,wBAAwB3E,eAC3C+F,EAAc5L,EAAI73B,KAAKw5B,cAC7B,GACE94B,KAAKi3B,KAAKrE,WACVkQ,GACAC,GACAD,IAAgBC,EAEhB,MAAM,IAAI3iC,8CACoC0iC,mBAA6BC,wBAM7E,YA/PuB7hC,WAGrBg5B,EAAmBtvB,gBAEbD,EAAOuvB,EAAmBtvB,UAAUk0B,MAAO16B,IAE/CyG,QAAQC,KACN,wFACyBovB,EAAmBtvB,aAC5CxG,EAAI/D,YAmPJ0+B,GAEC5H,GA4DoCkI,YAG/Bv6B,GAAcV,GAC5B,OAAOA,aAAe9D,EIhoBjBY,eAAe8hC,GAAyB/2B,GAC7C,MAAMy0B,EAAmC,iBAAVz0B,EAAqB,CAAEtK,IAAKsK,GAAUA,EAC/D9I,EAA6B,GAC/Bu9B,EAAgBp4B,YAClBnF,EAAMmF,UAAYo4B,EAAgBp4B,WAEpC,MAAM3I,EAAmB+D,QAAoBg9B,SAAAA,EAAiB/gC,SACxDN,SAA6BqhC,SAAAA,EAAiBrhC,WAAY,GAShE,OARAS,OAAOyC,KAAKlD,GAAUiE,QAAS3B,IAC7B,MAAMiD,EAAIvF,EAASsC,GACV,MAALiD,IACFzB,cAAkBxB,GAASiD,KAKxB5E,KAAKoM,aAAqBH,EAAO,MAAO9I,EAAOxD,OAAS8yB,EAAW,CACxEgF,UAAW,CAAEwL,aAAc,iBAoH/B,MAAMC,GAA8B,CAAC,SAAU,UAqC/ChiC,eAAeiiC,GAEbl3B,GAEA,MAAMy0B,EAAmC,iBAAVz0B,EAAqB,CAAEtK,IAAKsK,GAAUA,EAC/DtM,EAAU+D,EAAoBg9B,EAAgB/gC,SACpD+gC,EAAgB/gC,QAAUA,EAC1B,MAAMyjC,EAAW1C,EAAgB0C,UAAY,SAC7C1C,EAAgB0C,SAAWA,EA1C7B,SAA8BA,GAC5B,IAAIC,EAAkC,OAClCC,EAA+B,GAQnC,GANED,EAAc,OACdC,EAAmBJ,IAKhBI,EAAiBnG,SAASiG,GAC7B,MAAM,IAAIhjC,+CACqCkjC,EAAiB7/B,KAC5D,gCA+BN8/B,CAAqBH,GAErB,MAAMjgC,EAAiC,GACjC9D,SAA6BqhC,SAAAA,EAAiBrhC,WAAY,GAkChE,GAjCAS,OAAOyC,KAAKlD,GAAUiE,QAAS3B,IAC7B,MAAMiD,EAAIvF,EAASsC,GACV,MAALiD,IACFzB,cAAkBxB,GAASiD,K3ByHjC,SACEA,EACAzB,EACAZ,GAMA,SAASihC,EAAUr6B,EAAWvE,GACZ,MAAZzB,EAAMgG,KACRhG,EAAMgG,GAAKvE,GANVrC,EAAK0G,QAUV1G,EAAKe,QAAS6F,IACZ,MAAMC,EAAQf,EAAgBc,GAC9B,IAAKC,EAEH,MAAM,IAAIhJ,OAAoB+I,4CAGhC,MAAME,EAAWzE,EAAEuE,GACnB,GAAgB,MAAZE,EACF,OAIF,GAAqB,iBAAVD,EACT,OAAOo6B,EAAUp6B,KAFIC,GAKvB,GAAIvH,MAAMC,QAAQqH,GAGhB,OAAOo6B,EAFQp6B,EAAM,GACJA,EAAM,GAAGC,IAI5B,MAAM3H,EAAM0H,EAAMC,GAClBvJ,OAAOwJ,QAAQ5H,GAAK4B,QAAQ,EAAE6F,EAAGvE,MAC/B4+B,EAAUr6B,EAAGvE,O2B9JjB6+B,CAAiB/C,EAAiBv9B,EAAO,CACvC,YACA,UACA,aACA,aACA,uBACA,6BACA,0BACA,0BACA,sBACA,oBAGF6F,EAAmB03B,EAAiB,CAClC,UACA,kBACA,cACA,oBAEA,gBACA,UACA,aAEA,QACA,iBAIyB,MAAzBA,EAAgB76B,QACe,MAA9B66B,EAAgBsB,YAAkD,MAA5BtB,EAAgBuB,UACvD,CAAA,MACA,MAIMp8B,WAH0B,MAA9B66B,EAAgBsB,cAAwBtB,EAAgBsB,WAAe,MAE3C,MAA5BtB,EAAgBuB,YAAsBvB,EAAgBuB,SAAa,KAErEtiC,EAAO,eAAYA,EAAO,SAAakG,EAUzC,IAAI2wB,EAAgB,EAEhBjqB,GAAa,EACjB,MAAM+pB,yBAAEA,EAAFC,SAA4BA,GAAamK,EACzCjK,EAAsB,CAC1BC,EACAC,EAAsB,KAGtB,GAAIA,EAAc,EAChB,OAEF,IAAKL,IAA6BC,EAChC,OAEFC,GAAiBG,QACjBL,GAAAA,EAA2B,CACzBI,KAAAA,EACAC,YAAAA,EACAH,cAAAA,EACAI,WAAYrqB,IAEd,MAAMsqB,EAEAtqB,EAAY,EACP,EAGS,IAAdA,EACEmqB,IAASnC,yBAAiBuC,QACrB,EAEF,EAEFN,EAAgBjqB,EAEH,IAAlBsqB,EACEH,IAASnC,yBAAiBuC,gBAC5BP,GAAAA,EAAWM,UAKbN,GAAAA,EAAWM,IAIfJ,EAAoBlC,yBAAiB2C,SACrC,MAAO9yB,EAAK+yB,SAAajzB,EACvBlE,KAAKoM,aAAkBH,EAAO,MAAO9I,EAAOxD,OAAS8yB,EAAW,CAC9DgF,UAAW,CACTwL,aAxDK,SAyDLS,mBAAqB9L,IACnBrrB,EAAYqrB,EAAM+L,MAClBlN,EACElC,yBAAiByC,GACjBY,EAAMC,OAASrB,QAMzB,GAAIpyB,IAAQ+yB,EAEV,MADAV,EAAoBlC,yBAAiBwD,QAC/B3zB,EAGR,IAAIw/B,EAAazM,EAAIx3B,QACjBkkC,EAAiD1M,EAAI73B,KACzDiN,IAAcq3B,EAAW,mBAAqB,GAIxCn/B,EAAWo/B,KAEXnD,EAAgBjR,aAChB8C,GAAmBmO,EAAgBjR,eAEnCoU,EAAU3S,GACR2S,EACAnD,EAAgBjR,cAIpBoU,EAAU7U,GAAsB6U,EAAUtS,GACxCkF,EAAoBlC,yBAAiByC,GAAIzF,IAE3CsS,EAAQh4B,GAAG,MAAO,IAAM4qB,EAAoBlC,yBAAiBuC,UAE5C,WAAbsM,IAEFS,O3B0CmB3iC,OAAAA,IAGzB,IAAIkwB,EAAM5sB,OAAOkxB,KAAK,IACtB,OAAO,IAAIrrB,QAAQ,CAACC,EAASw5B,KAC3Bz4B,EAAOQ,GAAG,OAASvM,IACjB8xB,EAAM5sB,OAAO2xB,OAAO,CAAC/E,EAAK9xB,MAE5B+L,EAAOQ,GAAG,MAAO,KACfvB,EAAQ8mB,KAEV/lB,EAAOQ,GAAG,QAAUzH,IAClB0/B,EAAO1/B,Q2BtDW2/B,CAAYF,KAelC,MAAMG,EAA4C,IAC7C7M,EACH73B,KAAM,CACJs9B,QAASiH,EACTvM,KAAMsM,EAAU,MAAY,GAC5BK,aAAcL,EAAW,kBAAoB,GAC7CpM,cAAeoM,EAAW,yBAA2B,GACrDjD,kBAAmBiD,EAAWhS,GAAUgP,2BAMtCroB,EAAO6a,GAA0BwQ,GAIvC,OAHIrrB,IACFyrB,EAAU1kC,KAAKuhC,YAActoB,GAExByrB,EAQF9iC,eAAegjC,GAEpBj4B,GAQA,OAAO,IAAI5B,QAAQnJ,MAAOoJ,EAASw5B,KACjC,MAAMK,QAAqBhB,GAAYlL,KAAKj4B,KAAMiM,GAC5CZ,EAAS84B,EAAa7kC,KAAKs9B,QAE3BwH,EAAgB7jC,EAAkB0L,EAAMrB,UAC9CS,EAAOU,KAAKq4B,GACZ/4B,EAAOQ,GAAG,QAAUzH,GAAQggC,EAAc94B,QAAQlH,IAClDggC,EAAcv4B,GAAG,QAAUzH,GAAQ0/B,EAAO1/B,IAC1CggC,EAAcv4B,GAAG,SAAU,KACzB,MAAMg4B,EAAe,IAAKM,EAAa7kC,aAChCukC,EAAQjH,QACftyB,EAAQ,IAAK65B,EAAc7kC,KAAM,IAAKukC,SC3S5C,IAAYQ,IAAAA,GAAAA,4BAAAA,mFAEVA,qDACAA,mDACAA,iDACAA,mDACAA,uDACAA,qDAiBF,MACM5K,GAA0B,CAAC,IAAK,IAAK,KAEpCv4B,eAAeojC,GAEpBr4B,eAOA,MAAM0tB,YAAEA,EAAFrxB,UAAeA,GAAc2D,EAC7B2tB,EAAW,IAAMD,KAAiBA,EAAYE,OACpD3G,GAAmBjnB,EAAM6tB,YAEzB,MAAMyK,QAAsB9D,GAAWxI,KAAKj4B,KAAM,CAChD44B,OAAQ3sB,EAAM2sB,OACdj3B,IAAKsK,EAAMtK,IACX2G,UAAAA,KAEMhJ,KAAMkiC,GAAgB+C,EACxBjN,EAAOkK,EAAW,KAClBgD,WAAoBhD,EAAY,gCAAgC,EAChEE,EACiC,YAArCF,EAAY,sBACPgD,GACAhD,EAAY,kBAEbtH,OAA2B,WAE7B,GAAgC,iBAArBjuB,EAAM6tB,WAAyB,CACxC,MAAMA,WAAEA,GAAe7tB,EAEvB,IAAIkuB,EAA+B,KACnC,IACEA,QAAuBxvB,EAASmvB,GAChC,MAAOM,GAEP,MAAMh2B,EAAMg2B,EACZ,GAAiB,WAAbh2B,EAAI3E,KAGN,MAAM2E,EAIV,MAAMi2B,EACAF,EACKA,EAAeE,cAEjBP,EAAWQ,SAAS,KAIvB1vB,EAAWyvB,EACbE,EAAKjwB,QAAQwvB,EAxDgB,mCAyD7BA,EACEU,EAAUD,EAAKE,QAAQ7vB,GAI7B,aAFMD,EAAuB6vB,GAEzBH,EACK,CACLzvB,SAAAA,EACA8vB,uBAAuB,GAIpB,CACL9vB,SAAAA,EACA8vB,uBAAuB,EAGvBC,OAAQR,QACE3vB,GAAwBI,QAC9B6nB,GAKV,MAAgC,iBAArBxmB,EAAM6tB,WACR,CACLa,OAAQ1uB,EAAM6tB,YAIX,IA1DwB,QA8D3B,iBACJ,YAAII,EAAmBS,SAAnBC,EAA2B6J,YAAa,CAAA,MAC1C,MAAM3J,cAAEA,EAAF8G,YAAiBA,YACrB1H,EAAmBS,eAAnBK,EAA2ByJ,YAG3BjD,EAAY,mBAAqB1G,GACjC4G,IAAeE,IAEf/2B,QAAQC,yCAC8B,IAAI4lB,KACtCoK,gGAGGZ,EAAmBS,UAd1B,GAmBN,MAAMjuB,EACJT,EAAMS,oBAAYwtB,EAAmBS,eAAnBO,EAA2BC,Y5BlEhB,S4BsE7BjB,EAAmBS,QACnBT,EAAmBS,OAAOQ,YAAczuB,IAExC7B,QAAQC,KACN,+JAGKovB,EAAmBS,QAG5B,IAAI/B,EAAS3sB,EAAM2sB,QAAU54B,KAAKi3B,KAAK2B,QAAU,GACjD,MAAMj3B,EAAMsK,EAAMtK,IACZiJ,OAAiB,WACrB,IAAI85B,EAA8B,KAClC,IACEA,QAAsB/5B,EAASsB,EAAMrB,UACrC,MAAOwvB,GACP,MAAMh2B,EAAMg2B,EACZ,GAAiB,WAAbh2B,EAAI3E,KAGN,MAAM2E,EAIV,MAMMwG,GALA85B,EACKA,EAAcrK,cAEhBpuB,EAAMrB,SAAS0vB,SAAS,MAG7BC,EAAKjwB,QAAQ2B,EAAMrB,SAAUjJ,GAC7BsK,EAAMrB,SAEJ4vB,EAAUD,EAAKE,QAAQ7vB,GAG7B,aAFMD,EAAuB6vB,GAEtB5vB,GA1Bc,IA4BhB+5B,EAAcC,QAAiB,WACpC,MAAMD,EAAe14B,EAAM04B,aACvB14B,EAAM04B,aACN/5B,EAAW,QACf,IAAIg6B,GAAU,EACd,UACQj6B,EAASg6B,GACf,MAAOvK,GACP,MAAMh2B,EAAMg2B,EACZ,GAAiB,WAAbh2B,EAAI3E,KAIN,MAAM2E,EAHNwgC,GAAU,EAMd,MAAO,CAACD,EAAcC,IAhBc,GAkBlC1K,EAAmBS,SAChBiK,IACH/5B,QAAQC,KACN,4HAGKovB,EAAmBS,SAI9B,IAAIS,EAAgB,GACpB,MAAMC,EAkaR,SAAqB9uB,EAAmBG,GACtC,MAAM0uB,EAAgB,GACtB,IAAK,IAAIE,EAAI,KAAOA,EAAG,CACrB,MAAMjD,EAASiD,EAAI5uB,EACb6uB,EAAe3uB,KAAK6kB,IAAI/kB,EAAUH,EAAY8rB,GAQpD,GANA+C,EAAM73B,KAAK,CACT80B,OAAAA,EACA3rB,SAAU6uB,EACVzF,WAAYwF,EAAI,KAGbA,EAAI,GAAK5uB,GAAYH,EACxB,MAIJ,OAAO6uB,EAnbkBI,CAAYkG,EAAYh1B,GAC3C+uB,aAAqBvB,EAAmBS,iBAAQe,aAAc,IACjEC,OAAQr5B,GAAOA,EAAGs5B,cAClBp5B,OAAO,CAAC+E,EAAMjF,IAAOiF,GAAQjF,EAAGuiC,UAAYviC,EAAGwiC,YAAc,GAAI,GAG9DhJ,YAAgB5B,EAAmBS,iBAAQe,aAAc,GACzDK,EACJ,IAAIC,IACNF,EAAcx4B,QAAShB,GAAOy5B,EAAgB/5B,IAAIM,EAAG25B,YAAa35B,IAElE,MAAMyiC,EAAoB7jC,UACxB,MAAMg7B,EAAuB,KAC6B,CACtDtD,OAAAA,EACAj3B,IAAAA,EACAqjC,WAAY18B,EACZ6yB,UAAWzuB,EACXgvB,WAAYI,EACZjB,UAAW,CACToK,UAAWr6B,EACXs6B,eAAgBP,GAElBF,YAAa,CACX3J,cAAe0G,EAAY,iBAC3BlK,KAAMA,EACN0F,eAAgBwE,EAAY,yBAA2B,GACvDI,YAAaF,KAKbyD,EACJ7jC,IAKA,IAAK2K,EAAMm5B,oBACT,OAGF,MAAMxN,EAAuB,CAC3BgB,OAAAA,EACAtwB,UAAAA,EACA3G,IAAAA,EACAiJ,SAAAA,KACGtJ,GAED44B,EAAmBtvB,WACrBgtB,EAAM2E,eAAiBrC,EAAmBtvB,UAG5CqB,EAAMm5B,oBAAoBxN,IAG5B,IACK4E,EADDX,EAA2BJ,GAC/B,SAAKe,GACHA,qBACAA,iDACAA,qDAHF,CAAKA,IAAAA,OAKL,MAAMC,EAAwB/F,IACvBzqB,EAAMsqB,WAYTsF,IAA6B6F,GAC7BhL,IAAS8F,EAAyB6I,qBAIlCp5B,EAAMsqB,SAZFG,IAAS8F,EAAyBpE,OAAwB,IAAfsJ,EACtC,EAEDA,EAAiB7F,EAA2B6F,EAA/B,EASGxF,OAG5B,IAAI1F,EAAgBiF,EACpB,MAAMnF,yBAAEA,GAA6BrqB,EAC/BwqB,EAAsB,CAC1BC,EACAC,EAAsB,KAEjBL,IAGLE,GAAiBG,QAEjBL,GAAAA,EAA2B,CACzBI,KAAAA,EACAC,YAAAA,EACAH,cAAAA,EACAI,WAAY8K,MAGV/E,EAAsBzyB,GAAoBhJ,UAC9C,GAEEg5B,EAAmBtvB,SACnB,CACA,MAAMgyB,EAAUnyB,KAAKf,UAAUwyB,IAAwB,KAAM,GACvD1B,EAAUD,EAAKE,QAAQP,EAAmBtvB,gBAE1CD,EAAuB6vB,SACvB7vB,EAAcuvB,EAAmBtvB,SAAUgyB,EAAS,YAwBxD0I,EAA0BpkC,MAC9B42B,EACAyN,KASA,IAAIxI,EAAkBhB,EAAgBl6B,IAAIi2B,EAAKhC,YAC/C,MAAMkM,EAAalK,EAAKO,OAClB4J,EAAWr1B,KAAK6kB,IAAIqG,EAAKO,OAAS3rB,EAAW,EAAGg1B,EAAa,GAC9D3E,IACHA,EAAkB,CAChBd,YAAanE,EAAKhC,WAClBgP,YAAa9C,EACb6C,UAAW5C,EACXjF,eAAgB,GAChBpB,cAAc,GAEhBE,EAAcv4B,KAAKw5B,GACnBhB,EAAgB/5B,IAAI+6B,EAAgBd,YAAac,IAG9CwI,EAAgBnhC,MACnB24B,EAAgBnB,cAAe,EAC/BmB,EAAgBC,eAAiBuI,EAAgBpO,IAAIqO,0BAGjD7I,IACN,MAAM8I,EAAqC,CACzC3P,WAAYiH,EAAgBd,YAC5B+F,WAAAA,EACAC,SAAAA,GAGF,GAAIsD,EAAgBnhC,IAAK,CACvB,MAAMA,EAAMmhC,EAAgBnhC,IAC5B,IAAIsyB,EAA0B2N,0BAAkBqB,mBAahD,OAXIthC,aAAelF,GACbu6B,GAAwB0D,SAAS/4B,EAAI1E,cACvCg3B,EAAO2N,0BAAkBsB,0BAI7BR,EAAqB,CACnBzO,KAAAA,EACAtyB,IAAAA,EACAqhC,iBAAkBA,IAKtB5J,GACE4J,EAAiBxD,SAAWwD,EAAiBzD,WAAa,EAE5DmD,EAAqB,CACnBzO,KAAM2N,0BAAkBuB,oBACxBH,iBAAkBA,IAEpBhJ,EAAqBD,EAAyB6I,sBAGhD,GAAInL,EAAmBS,OAAQ,CAC7B/B,EAASsB,EAAmBS,OAAO/B,OAGnC,MAAM0E,EAA+B,IAAIC,KACtCrD,EAAmBS,OAAOe,YAAc,IACtCC,OAAQr5B,GAAOA,EAAGs5B,cAClBv5B,IAAKC,GAAOA,EAAG25B,cAEpBb,EAAQC,EAASM,OAAQr5B,IAAQg7B,EAAgBE,IAAIl7B,EAAGwzB,iBACnD,CACL,UAEQnrB,EAAcg6B,EAAc,GAAI,CACpCkB,KAAM,OAER,MAAOzL,GACP,MAAMh2B,EAAMg2B,EAKZ,MAJA+K,EAAqB,CACnBzO,KAAM2N,0BAAkByB,qBACxB1hC,IAAAA,IAEIA,EAGsC,MAA1C81B,EAAmBQ,wBACrBR,EAAmBtvB,kBAAWsvB,EAAmBtvB,iBAAnB+yB,EAA6B/6B,0CAoNnE,SACEg2B,EACAj3B,EACA2G,GAIA,SAFsBswB,KAAUj3B,KAAO2G,SACN1F,QAAQ,SAAU,IAxN3Cg7B,CAA6BhF,EAAQj3B,EAAK2G,KAI9C68B,EAAqB,CACnBzO,KAAM2N,0BAAkB0B,wBAE1BtP,EAAoBlC,yBAAiB2C,SACrCkE,EAAQC,EA0IVoB,EAAqBD,EAAyBpE,OAC/B,IAAfsJ,OAHuBxgC,aAGE0hC,QAxIL1hC,WAClB,IAAI68B,EAAyB,KACzBC,EAAQ,EAoHZ,SAjHM3zB,QAAQ4zB,IACZn8B,MAAM4zB,KAAK,CAAEzsB,OAAQgD,EAAMiyB,SAAW,IAAK77B,IAAInB,UAC7C,OAAa,CACX,MAAMi9B,EAAeH,IACrB,GAAIG,GAAgB/C,EAAMnyB,OACxB,OAGF,MAAMm1B,EAAUhD,EAAM+C,GACtB,IAAIE,EAAwB,EAC5B,IACE,MAAMlH,QAAYgM,GAAYlL,KAAKj4B,KAAM,CACvC44B,OAAAA,EACAj3B,IAAAA,EACA2G,UAAAA,EACA3I,QAAS,CACPqmC,WAAY1O,EACZzxB,eAAgBu4B,EAAQ/F,UAAUzrB,KAAK6kB,IACrC2M,EAAQ/F,OAAS+F,EAAQ1xB,SAAW,EACpCg1B,EAAa,MAGjB/5B,aAAcsE,EAAMtE,aACpB8nB,YAAaxjB,EAAMwjB,YACnB6G,yBAAyBn2B,GACnBA,EAAOu2B,OAASnC,yBAAiByC,KAGjC4C,MAGJyE,GAAyBl+B,EAAOw2B,YAChCF,EAAoBlC,yBAAiByC,GAAI72B,EAAOw2B,kBASpD,IAAIsP,EAAa9O,EAAI73B,KAAKs9B,QAC1B,MAAMsJ,EAAU,IAAI5W,GA0CpB,GAvCEtvB,KAAKi3B,KAAKrE,YAEVqT,EAAatU,GAAoBsU,EAAYC,UAEzC,IAAI77B,QAAQ,CAACC,EAASw5B,KAC1B,MAAMqC,EAAcx7B,EAAsBg6B,EAAc,CACtDvM,MAAOgG,EAAQ/F,OACf+N,MAAO,OAGT,IAAIC,GAAQ,EACRjiC,EAAW,KACf+hC,EAAYt6B,GAAG,QAAS,KAClBw6B,EACFvC,EAAO1/B,GAEPkG,OAAQmoB,KAIZ0T,EAAYt6B,GAAG,QAAUuuB,IACvBiM,GAAQ,EACRjiC,EAAMg2B,IAGR6L,EAAWl6B,KAAKo6B,GAChBF,EAAWp6B,GAAG,QAAUzH,GAAQ+hC,EAAY76B,QAAQlH,IAUpD6hC,EAAWp6B,GAAG,QATd,SAASy6B,IACH1M,MACFkK,EAAO,IAAIxjC,EAAY,wBAEvB6lC,EAAY7N,MACZ2N,EAAWM,OAAOJ,GAClBF,EAAWO,IAAI,OAAQF,SAMzB1M,IACF,MAAM,IAAIt5B,EAAY,6BAGlBglC,EAAwBlH,EAAS,CACrCjH,IAAK,IAAKA,EAAI73B,KAAMkmC,mBAAoBU,EAAQl8B,cAElD,MAAOowB,GACP,MAAMh2B,EAAMg2B,EAIZ,GAHA5D,GAAiB6H,EACjBA,EAAwB,EAEpBv5B,EAAcV,GAChB,MAAMA,EAGR,GAAIw1B,IACF,MAAM,IAAIt5B,EAAY,uBAGnBy9B,IACHA,EAAW35B,SAEPkhC,EAAwBlH,EAAS,CAAEh6B,IAAAA,SAM7C25B,EACF,MAAMA,EAGR,MAAMj0B,EAAcy6B,EAAcjlC,KAAK,wBACvC,GAAIU,KAAKi3B,KAAKrE,WAAa9oB,EAAa,CACtC,MAAMi5B,EAqFd,SAA2B5P,WACzB,IAAIgE,EAAM,IACV,MAAM6H,oBACJ7L,EAAGuI,mBAAHuD,EAAe57B,YAAf47B,EAAe57B,KAAO,CAAC67B,EAAGrwB,IAAMqwB,EAAEjD,YAAcptB,EAAEotB,gBAAgB,GACpE,IAAK,MAAMkD,KAAQH,EACjB7H,EAAM5H,GACJ4H,EACAgI,EAAKnC,eACLmC,EAAK0F,UAAY1F,EAAK2F,YAAc,GAGxC,OAAO3N,EAhGmBiI,CAAkBlD,KACtC,GAAI6G,IAAgBj5B,EAClB,MAAM,IAAI1J,8CACoC0J,mBAA6Bi5B,0BAS/B1D,GAElD,IAC8D,mBAAjDpzB,EAAMw6B,6CACTx6B,EAAMw6B,uCACV9B,EACA/5B,SAGID,EAAWg6B,EAAc/5B,GAEjC,MAAOwvB,GACP,MAAMh2B,EAAMg2B,EAMZ,MALA+K,EAAqB,CACnBzO,KAAM2N,0BAAkBqC,qBACxBtiC,IAAAA,IAEFqyB,EAAoBlC,yBAAiBwD,QAC/B3zB,EAUR,OAPA+gC,EAAqB,CACnBzO,KAAM2N,0BAAkBsC,wBAE1BlK,EAAqBD,EAAyBoK,uBAC9CnQ,EAAoBlC,yBAAiBuC,cA/RZ51B,WAGrBg5B,EAAmBtvB,gBAEbD,EAAOuvB,EAAmBtvB,UAAUk0B,MAAO16B,IAE/CyG,QAAQC,KACN,wFACyBovB,EAAmBtvB,aAC5CxG,EAAI/D,YAsRN0+B,GAECwF,GAGT,IACE,aAAaQ,uBCpqBD8B,GAAcC,GAC5B,OAAIA,GAAiB,KAATA,GAAwB,MAATA,EAS7B,MAAMC,GAAe,UAYrB,MAAaC,GAGX5nC,YAAY6nC,EAAiBC,QAFrBC,oBACAD,wBASDE,UAAY,CACjBH,EACAI,EACAH,KAEKA,IACHA,EAAclnC,KAAKknC,aAErB,MAAMhO,EAAkB,GAElBoO,EAAatnC,KAAKunC,iBADPN,EAAIO,UAerB,OAbAtO,EAAM31B,KACJvD,KAAKmnC,QAAQM,UACX,eACAP,EAAYQ,eACZ,IACAJ,GAMJpO,EAAM31B,KAAK,iBAAmBvD,KAAK2nC,cAAcV,IACjD/N,EAAM31B,KAAK,aAAevD,KAAK4nC,cAAcX,EAAKC,EAAa,IACxDhO,EAAMz1B,KAAK,YAGbokC,gBAAkB,CACvBZ,EACAI,EACAH,KAGAD,EAAIO,SAAWxnC,KAAK8nC,cACpB,MAAMC,EAAS,IAAI/L,IAEdiL,EAAItnC,UAEPsnC,EAAItnC,QADiC,IAIvCsnC,EAAItnC,QAAQqoC,QAAUf,EAAIe,KAErBnB,GAAcI,EAAIH,QACrBG,EAAItnC,QAAQqoC,MAAQ,IAAMf,EAAIH,MAG5BG,EAAIgB,YACNhB,EAAItnC,QAAQqoC,QAAUhoC,KAAKmnC,QAAQvO,UAAUqO,EAAIgB,aAGnDF,EAAO/lC,IAAI,OAAQilC,EAAItnC,QAAQqoC,MAC/BD,EAAO/lC,IAAI,aAAcilC,EAAIO,UAc7BO,EAAO/lC,IAAI,uBAAwBhC,KAAKkoC,sBACpCloC,KAAKmnC,QAAQgB,eACfJ,EAAO/lC,IAAI,uBAAwBhC,KAAKmnC,QAAQgB,eAGlDJ,EAAOzkC,QAAQ,CAAC1B,EAAOD,KACjBA,EAAIymC,WAAW,WACjBnB,EAAItnC,QAAQgC,GAAOC,KAGvBqlC,EAAI1M,KAAOv6B,KAAKqoC,cAAcpB,EAAI1M,MAClC,MAAM+N,EAAOtoC,KAAKonC,UAAUH,EAAK,EAAGC,GAGpC,OAFAa,EAAO/lC,IAAI,gBAAiBsmC,GAErBP,QAGFQ,WAAa,CAClBtB,EACAC,KAEO,CAAEvlC,IAAK,GAAIC,MAAO,UAGpB4mC,aAAe,CACpBvB,EACAI,KAEO,CAAE1lC,IAAK,GAAIC,MAAO,UAGpB6mC,kBAAoB,CACzBxB,EACAI,KAEAJ,EAAIO,SAAWxnC,KAAK8nC,cACfb,EAAItnC,UAEPsnC,EAAItnC,QADiC,IAIvCsnC,EAAItnC,QAAQqoC,QAAUf,EAAIe,KACrBnB,GAAcI,EAAIH,QACrBG,EAAItnC,QAAQqoC,MAAQ,IAAMf,EAAIH,MAGhCG,EAAI1M,KAAOv6B,KAAKqoC,cAAcpB,EAAI1M,MAC9B0M,EAAIgB,YACNhB,EAAItnC,QAAQqoC,QAAUhoC,KAAKmnC,QAAQvO,UAAUqO,EAAIgB,aAInDhB,EAAItnC,QAlKG,cAkK0BsnC,EAAIO,SACrC,MAAMF,EAAatnC,KAAKunC,iBAAiBN,EAAIO,UACvCrQ,EAAM,IACN8P,EAAI9jC,OAAS,GACjBulC,kBAA0B1oC,KAAKmnC,QAAQM,UACvCkB,uBAA4B3oC,KAAKkoC,qBACjCU,mBACE5oC,KAAKknC,YAAYQ,eAAiB,IAAMJ,EAC1CuB,aAAqB5B,EAAIO,SACzBsB,gBAAwB,GAAKzB,EAC7B0B,sBAA8B/oC,KAAK2nC,cAAcV,IAYnD,OAVIjnC,KAAKmnC,QAAQgB,gBACfhR,EA5Ke,wBA4KoBn3B,KAAKmnC,QAAQgB,eAElDlB,EAAI9jC,MAAQD,EAAqBi0B,GAEjCA,EA/KY,mBA+KkBn3B,KAAK4nC,cACjCX,EACAjnC,KAAKknC,YACLG,GAEKlQ,QAGF6R,wBAA0B,CAC/B/B,EACAI,KAEAJ,EAAIO,SAAWxnC,KAAK8nC,cAEpB,MAAMR,EAAatnC,KAAKunC,iBAAiBN,EAAIO,UACvCrQ,EAAM,CACVuR,kBAA0B1oC,KAAKmnC,QAAQM,UACvCmB,mBACE5oC,KAAKknC,YAAYQ,eAAiB,IAAMJ,EAC1CuB,aAAqB5B,EAAIO,SACzBsB,gBAAwB,GAAKzB,EAC7B4B,eAAuBv/B,GACrBgB,GAAMD,KAAKf,UAAUu9B,EAAIiC,QAAS,SAClC,WAaJ,OAVIlpC,KAAKmnC,QAAQgB,gBACfhR,EA3Me,wBA2MoBn3B,KAAKmnC,QAAQgB,eAElDlB,EAAI9jC,MAAQD,EAAqBi0B,GAEjCA,EA9MY,mBA8MkBn3B,KAAK4nC,cACjCX,EACAjnC,KAAKknC,YACLG,GAEKlQ,QAGD+Q,mBAAqB,IACpB,wBAIDN,cAAgB,CACtBX,EACAC,EACAG,KAGA,IAAKJ,EAAIO,SACP,MAAO,GAGT,MAAM2B,EAAanpC,KAAKopC,cACtBlC,EACAD,EAAIO,SAAS6B,OAAO,EAAG,IAQzB,OAAO1T,GAAWwT,EAAYnpC,KAAKspC,aAAarC,EAAIO,SAAUP,GAAM,aAG9Da,YAAc,IACP,IAAIpX,MAAK,IAAIA,MAAO1rB,eAG5BukC,cACA3mC,QAAQ,OAAQ,IAChBA,QAAQ,KAAM,IACdA,QAAQ,KAAM,IAAM,SAGnB2kC,iBAAoBC,GACnBxnC,KAAKwpC,YACVhC,EAAS6B,OAAO,EAAG,GACnBrpC,KAAKmnC,QAAQsC,OACbzpC,KAAKmnC,QAAQuC,kBAITF,YAAc,CAACG,EAAMF,EAAQC,IAC5B,CAACC,EAAKN,OAAO,EAAG,GAAII,EAAQC,EAAa3C,IAActjC,KAAK,UAG7D2lC,cAAgB,CAAClC,EAA8ByC,KACrD,MAAMC,EAAQjU,GAAWuR,EAAY2C,eAAgBF,GAC/CG,EAAUnU,GAAWiU,EAAO5pC,KAAKmnC,QAAQsC,QACzCM,EAAWpU,GAAWmU,EAAS9pC,KAAKmnC,QAAQuC,aAGlD,OAFmB/T,GAAWoU,EAAUhD,UAKlCuC,aAAe,CAAC9B,EAAkBP,KAExC,IAAKjnC,KAAKmnC,QAAQM,UAChB,MAAO,GAGT,MAAMvO,EAAkB,GACxBA,EAAM31B,KAAKvD,KAAKmnC,QAAQM,WACxBvO,EAAM31B,KAAKikC,GACXtO,EAAM31B,KAAKvD,KAAKunC,iBAAiBC,IACjC,MAAMwC,EACJ,WAAY/C,EACRjnC,KAAKiqC,sBAAsBhD,GAC3BjnC,KAAKgqC,gBAAgB/C,GAG3B,OADA/N,EAAM31B,KAAKvD,KAAKkqC,eAAeF,IACxB9Q,EAAMz1B,KAAK,YAGZymC,eAAiBC,GAChBvU,GAAWuU,EAAQ,YAGpBH,gBAAmB/C,IACzB,MAAM/N,EAAe,GAOrB,OANAA,EAAM31B,KAAK0jC,EAAImD,QACflR,EAAM31B,KAAK0jC,EAAI1M,MACfrB,EAAM31B,KAAKvD,KAAKqoC,cAAcpB,EAAI9jC,OAAiB,IACnD+1B,EAAM31B,KAAKvD,KAAKqqC,iBAAiBpD,GAAO,MACxC/N,EAAM31B,KAAKvD,KAAK2nC,cAAcV,IAC9B/N,EAAM31B,KAAKvD,KAAKkoC,sBACThP,EAAMz1B,KAAK,YAGZwmC,sBAAyBhD,IAC/B,MAAM/N,EAAe,GAGrB,OAFAA,EAAM31B,KAAKvD,KAAKqoC,cAAcpB,EAAI9jC,OAAiB,IACnD+1B,EAAM31B,KAAKvD,KAAKkoC,sBACThP,EAAMz1B,KAAK,YAGZ4mC,iBAAoBpD,IAC1B,MAAM/N,EAAkB,GAClBoR,EAAkBC,GAAqBtD,EAAItnC,SAEjD,IAAK,IAAIgC,KAAO2oC,EAAiB,CAC/B,MAAM1oC,EAAQqlC,EAAItnC,QAAQgC,GAC1BA,EAAMA,EAAIkB,cACVq2B,EAAM31B,KAAK5B,EAAM,IAAM3B,KAAKwqC,sBAAsB5oC,EAAMgzB,aAG1D,OAAOsE,EAAMz1B,KAAK,YAGZ+mC,sBAAyBC,GACxBA,EAAO7nC,QAAQ,OAAQ,KAAKA,QAAQ,aAAc,SAGnD+kC,cAAiBV,IACvB,MAAM1kC,EAAiB,GACjB+nC,EAAkBC,GAAqBtD,EAAItnC,SAEjD,IAAK,IAAIgC,KAAO2oC,EACd3oC,EAAMA,EAAIkB,cACVN,EAAKgB,KAAK5B,GAGZ,OAAOY,EAAKc,OAAOI,KAAK,MAnTxBzD,KAAKmnC,QAAUF,EACfjnC,KAAKknC,YAAcA,EAwTbmB,cAAc9N,EAAcmQ,GAAqB,GACvD,IAAKnQ,EACH,MAAO,GAGT,IAAIoQ,EAAUpQ,EASd,OARImQ,IACFC,EAAUpQ,EAAK33B,QAAQ,OAAQ,MAEjC+nC,EAAUA,EAAQ/nC,QAAQ,MAAO,OACjC+nC,EAAUA,EAAQ/nC,QAAQ,MAAO,OACjC+nC,EAAUA,EAAQ/nC,QAAQ,KAAM,OAChC+nC,EAAUA,EAAQ/nC,QAAQ,MAAO,OACjC+nC,EAAUA,EAAQ/nC,QAAQ,MAAO,OAC1B+nC,GAIX,MAAaC,GAKXxrC,YACE+oC,EACA0C,EACAC,QAPK3C,0BACA0C,4BACAC,mBAOL9qC,KAAK8qC,YAAcA,EACnB9qC,KAAK6qC,gBAAkBA,EACvB7qC,KAAKmoC,cAAgBA,EAGhBT,eACL,OAAO1nC,KAAK8qC,YAGPjB,eACL,OAAO7pC,KAAK6qC,iBAIhB,SAASN,GAAqB5qC,GAC5B,MAAM2qC,EAA4B,GAQlC,OAPAxqC,OAAOyC,KAAK5C,GAAW,IAAI2D,QAAS3B,KACtB,SAARA,GAAkBA,EAAIymC,WAAW,YACf,MAAhBzoC,EAAQgC,IACV2oC,EAAgB/mC,KAAK5B,KAIpB2oC,EAAgBjnC,gBCzbT0nC,GAAS9T,GACvB,MAAM+T,QAAEA,KAAYC,GAAchU,EAE5BiU,EAAQ,IADAF,EAAQG,QAAUC,EAAMC,MAAQC,EAAKD,OAC3B,IACnBJ,EACHM,WAAW,EACXC,mBAAoBR,EAAQS,gBAC5BC,QAASV,EAAQW,qBAGnBT,EAAMU,eAAiBC,SACvBX,EAAMY,gBAAkBd,EAAQe,eAEhC,MAAMC,EAAsBd,EAAMe,iBAiClC,OAhCAf,EAAMe,iBAAmB,YAAaC,GACpC,MAAMC,EAASH,EAAoB/T,KAAKj4B,QAASksC,GACjD,IAAIE,GAAY,EACZC,GAAc,EACdC,EAAsC,KAyB1C,OArBA/jC,QAAQgkC,SAAS,KACXF,IAIJC,EAAenc,WAAW,KACxBic,GAAY,GACXpB,EAAQwB,sBAGbL,EAAOtgC,GAAG,UAAW,KACnBwgC,GAAc,EACVC,GACFG,aAAaH,GAGXF,GACFD,EAAO7gC,QAAQ,IAAInM,MAAM,sBAItBgtC,GAGFjB,QCwIIwB,GAUXttC,YAAYutC,QATZ1V,iBAEA2V,sBAEAC,sBAEQC,sBACAC,uBAmYEC,cACR/V,IAEA,MAAMgW,EACa,iBAAThW,GAAqBA,EAAK2B,QAAW54B,KAAKi3B,KAAK2B,OACnDsU,EAA4B,iBAATjW,EAAoBA,EAAOA,EAAKt1B,IACzD,IAAKsrC,EACH,MAAM,IAAI7sC,EAAe,6BAE3B,UAAW6sC,KAAgBzpC,mBAAmB0pC,WActC/gC,2BAA6B,CACrCF,EACAtM,KAEA,GAA+B,MAA3BA,EAAQ,gBACV,OAGF,IAAIwtC,EnBrmB4B,2BmBsmBhC,MAAMxrC,EnB1mBwBsK,CAAAA,GACR,iBAAVA,EAAqBA,EAAQA,EAAMtK,ImBymBnCyrC,CAAkBnhC,GAE1BjM,KAAKi3B,KAAKoW,2BACZF,WnBvmByBxrC,GAC7B,MAAM2rC,EAAe3rC,EAAI4rC,YAAY,KAErC,GAAID,GAAgB,EAClB,OAGF,MAAME,EAAU7rC,EAAIsB,MAAMqqC,EAAe,GAAGzqC,cAE5C,OAAOsK,GAAUqgC,GmB8lBFC,CAAe9rC,IAAQwrC,GAGhCA,IACFxtC,EAAQ,gBAAkBwtC,SAIpBxjC,uBAAyBA,EA3ajC3J,KAAKi3B,KAAOj3B,KAAK0tC,cAAcf,GAG7B3sC,KAAK8sC,UAAY/B,GAAS,CAAEC,QAAS,IAAKhrC,KAAKi3B,KAAMkU,SAAS,KAG9DnrC,KAAK+sC,WAAahC,GAAS,CACzBC,QAAS,IAAKhrC,KAAKi3B,KAAMkU,SAFTnrC,KAAKi3B,KAAK0W,aAM9B3tC,KAAK6sC,UAAY7sC,KAAK4tC,eACtB5tC,KAAK4sC,UCnLqBiB,CAAAA,IAC5B,MAAMjB,EAAYkB,EAAMC,SAExBnB,EAAUoB,SAASC,UAAOxb,EAC1Bma,EAAUoB,SAAS/K,aAAe,OAClC2J,EAAUoB,SAASxkC,YAASipB,EAC5Bma,EAAUoB,SAASruC,QAAU,GAC7BitC,EAAUoB,SAASE,iBAAkB,EACrCtB,EAAUoB,SAASG,kBAAoB,EACvCvB,EAAUoB,SAASI,eAAiB,EACpCxB,EAAUoB,SAASK,aAAe,EAClCzB,EAAUoB,SAASM,eAAiB,SAAUnuC,GAC5C,OAAOA,GAAU,KAAOA,EAAS,KAEnCysC,EAAUoB,SAASO,YAAa,EAChC3B,EAAUoB,SAASQ,aAAe,CAChCC,mBAAmB,EACnBC,mBAAmB,EACnBC,qBAAqB,GAqBvB,MAAMC,EAAiBhqC,UAErB,OADAA,EAAEjF,QAAUiF,EAAEjF,SAAWiF,EAAEmjC,eAAUnjC,YAAAA,EAAGvF,iBAAHwvC,EAAalvC,UAAW,GACtDiF,GAQT,SAASkqC,EAAqBnvC,GAC5BG,OAAOwJ,QAAQ3J,GAAS2D,QAAQ,EAAE3B,EAAKC,MACrC,MAAOwC,EAAK2qC,YhC2DUC,GAC1B,IAEE,MAAO,CAAC,KgC9DqCC,UAAUrtC,IhC+DvD,MAAOwC,GACP,MAAO,CAACA,EAAK,OgChEiB8qC,GAC5B,GAAI9qC,GAAuB,MAAhB2qC,GAAwBA,IAAiBntC,EAClD,OAEF,IAAIutC,EAAO,GACX,MAAMC,MAAcxtC,GAAQytC,MAAM,OAC5BC,EAAkBP,EAAaM,MAAM,OAC3C,IAAK,IAAI/T,EAAI,EAAGiU,EAAI,EAAGjU,EAAIgU,EAAgBrmC,QAAU,CACnD,MAAMumC,EAAKF,EAAgBhU,GAC3B,GAAIkU,IAAOJ,EAASG,GAAI,CACtBJ,EAAK5rC,KAAKisC,KACRlU,IACAiU,EACF,SAGF,MAAME,EAAYjsC,mBAAmBgsC,GACjCA,EAAGvmC,OAAS,GAAKumC,EAAGE,WAAW,IAAM,IACvCP,EAAK5rC,KAAKisC,GAEVL,EAAK5rC,KAAKksC,KAEVnU,EACFiU,GAAKE,EAAUxmC,OAEjBtJ,EAAQgC,GAAOwtC,EAAK1rC,KAAK,MAqF7B,OAtHAmpC,EAAU+C,aAAatwC,SAASuwC,IAAIhB,EAAgB5e,IAClD4e,EAAc5e,GACP3lB,QAAQy5B,OAAO9T,KAkCxB4c,EAAU+C,aAAatwC,SAASuwC,IAC7BzY,GACMA,EAAIx3B,SAGTmvC,EAAqB3X,EAAIx3B,SAClBw3B,GAHEA,EAKXj2B,MAAAA,UACE,IAAK4sC,EAAM+B,aAAa7f,GACtB,OAAO3lB,QAAQy5B,OAAO9T,GAGxB,MAAMrwB,WAAUqwB,EAAM3wB,iBAANywC,EAAgBnwC,QAChC,OAAKA,GAGLmvC,EAAqBnvC,GACd0K,QAAQy5B,OAAO9T,IAHb3lB,QAAQy5B,OAAO9T,KAQ5B4c,EAAU+C,aAAatwC,SAASuwC,SAAInd,EAAWvxB,MAAAA,UAC7C,MAAM6uC,OAAEA,GAAW/f,EACnB,IAAK+f,EACH,OAAO1lC,QAAQy5B,OAAO9T,GAGnB+f,EAAM,kBACTA,EAAM,gBAAmB,IAE3B,MAAMC,EAAgCD,EAAM,gBACtCE,WAAaD,EAAYC,cAAc,EAE7C,IAAIpM,EAAUkM,EAAOzwC,KACrB,MAAM4wC,EAAe,MAEjB,GAAIH,EAAOzwC,MAAQywC,EAAOzwC,gBAAgBoF,WAAU,CAClD,MAAME,QAAIorC,EAAY5d,uBAAZ4d,EAAY5d,kBACtB,IAAKxtB,EACH,OAAO,EAETi/B,EAAUj/B,EAGd,OAAO,GAVY,GAkBrB,MA3JJ,SAAwBorB,SAEtB,OACIA,EAAM3wB,UAAYkjC,QAAQvS,EAAMvwB,OACjCuwB,EAAM3wB,qBAAa2wB,EAAM3wB,SAASM,UAAfwwC,EAAyB,qBAmJ1CC,CAAepgB,IA/ItB,SAA8BA,GAC5B,IAAKA,EAAM3wB,SACT,OAAO,EAGT,MAAMc,OAAEA,GAAW6vB,EAAM3wB,SACzB,OAAe,MAAXc,GAAkBA,GAAU,IAyIFkwC,CAAqBrgB,KAC/CigB,EAAapC,GACbqC,GAGA,OAAO7lC,QAAQy5B,OAAO9T,GAGxB,MAAMsgB,EAAiBP,EAAM,mBAC7B,GAAIO,EAAgB,CAClB,MAAMC,QAAEA,EAAFC,QAAWA,GAAYF,EACJE,EAAQ3I,gBAAgB0I,GAChCjtC,QAAQ,CAAC1B,EAAOD,KAC/BouC,EAAOpwC,QAAQgC,GAAOC,IAK1BkK,GAAQ,gBAAiBikC,GACzB,MAAMU,EAAa,IACdV,EACHzwC,KAAMukC,EACNnM,gBAAkB,IACbsY,EACHC,WAAYA,EAAa,IAK7B,aADAD,EAAYrd,aAAZqd,EAAYrd,cACLia,EAAU6D,KAGZ7D,GDkBY8D,CAAc1wC,KAAKi3B,KAAK4W,eAGnCH,cAAcf,SAEH,CACf,cACA,kBACA,WACA,SACA,YAEOrpC,QAAS3B,IAChB,MAAMC,EAAQ+qC,EAAMhrC,GACC,iBAAVC,IAET+qC,EAAMhrC,GAAOC,EAAMgyB,UAIvB,MACM+c,EADW,CAAC,cAAe,kBAAmB,UAEjDhV,OAAQh6B,IAAUgrC,EAAchrC,IAChC8B,KAAK,MAER,GAAIktC,EACF,MAAM,IAAIvwC,kBAA+BuwC,MAG3C,MAAMhO,EAAWgK,EAAMhK,iBAAwBgK,EAAMlD,oBACrD,IAAK9G,EACH,MAAM,IAAIviC,0FAKZ,GAAIuiC,EAASxF,SAAS,MACpB,MAAM,IAAI/8B,0DAKZ,MAAMsiC,EAAyB,MAAhBiK,EAAMjK,UAA0BiK,EAAMjK,OAC/CkO,EAAW,CACfhsC,EACAisC,IACS,MAALjsC,EAAYisC,EAAejsC,EAOjC,MAAO,IACF+nC,EACHhK,SAAAA,EACAD,OAAAA,EACA+I,gBAAiBmF,EAASjE,EAAMlB,iBAAiB,GACjD4B,yBAA0BuD,EAASjE,EAAMU,0BAA0B,GACnEyD,eAAgBF,EAASjE,EAAMmE,eAAgB,MAC/CtE,kBAAmBoE,EAASjE,EAAMH,kBAAmB,KACrDT,eAAgB6E,EAASjE,EAAMZ,eAAgB,MAC/CJ,mBAAoBiF,EAASjE,EAAMhB,mBAAoB,KACvDkC,cAAe+C,EAASjE,EAAMkB,cAAe,GAC7Cjb,mBAAW+Z,EAAM/Z,cACjBme,oBAAgBC,GAIZpD,eAEN,MAOMqD,EAAS,MACb,MAAMC,EAAUC,EAAGza,OAMnB,MAL0C,CACxC0a,MAAO,QACPC,OAAQ,SACRC,WAAY,WAEGJ,IAAYA,GAPhB,GASTK,EACGhpC,QAAQipC,QAAQC,WAAW,IAAK,IAmCzC,MAAO,8BAjC0BR,KAAU1oC,QAAQmpC,cAAcH,KACjD,MACd,MAAMI,qBAAEA,EAAFC,kBAAwBA,EAAxBC,qBAA2CA,GAC/C7xC,KAAKi3B,KACP,IAAI6a,EAAYhyC,OAAOwJ,QACrBtJ,KAAKi3B,KAAK8a,8BAAgC,IAEzC1vC,IAAI,EAAE8G,EAAGvE,QACEuE,KAAKvE,KAEhBnB,KAAK,KAGR,OAFAquC,EAAYA,MAAgBA,KAAe,GAGxCH,GACAC,GACAC,GACAC,EAaI,CARgB,CACrBH,EACAC,EACAC,GAECxvC,IAAKC,GAAOA,GANM,aAOlBmB,KAAK,KAEgBquC,GAAWnW,OAAO4G,SAAS9+B,KAAK,KAX/C,IAlBK,IAgCSk4B,OAAO4G,SAAS9+B,KAAK,QAG3BorB,YACnBub,EACA7P,EACAp3B,EACAxD,EACAoyB,EACAkF,GAEA,MAAMG,SAAiBH,SAAAA,EAAMG,kBAAoBD,GAAQA,EAAI73B,MAG7D,GAAIyyB,UAFYkF,SAAAA,EAAM+a,SAED,CACnB,MAAMC,EAAYvd,GAAQjqB,KAAKf,UAAUqoB,GAAO,UAChDpyB,EAAQ,eAAiBsyC,EAG3B,MAAOtP,EAAUuP,GAAW,WACtBjb,GAAAA,EAAMkb,iBAAmBnyC,KAAKi3B,KAAKmb,eAC9B,CAACpyC,KAAKi3B,KAAK0L,aAAc1L,EAAKkb,kBAAkB5X,WAGrDtD,GAAAA,EAAMkb,kBAAoBnyC,KAAKi3B,KAAKob,eAElC,UAAUC,KAAKtyC,KAAKi3B,KAAK0L,UACpB,CAAC3iC,KAAKi3B,KAAK0L,aAAc1L,EAAKkb,kBAAkB5X,KAElD,UAAItD,SAAAA,EAAMkb,mBAAmBnyC,KAAKi3B,KAAK0L,WAAYpI,GAErD,CAACv6B,KAAKi3B,KAAK0L,SAAUpI,GAZF,GAc5BA,EAAO2X,EAEPvyC,E/B/R+BA,CAAAA,IACjC,MAAM4yC,EAAmB,GAczB,OAbAzyC,OAAOwJ,QAAQ3J,GAAS2D,QAAQ,EAAE3B,EAAKC,MACrC2wC,EAAQ5wC,OAAUC,GAGfytC,MAAM,OACNhtC,IAAKmtC,GACAA,EAAGvmC,OAAS,GAAKumC,EAAGE,WAAW,IAAM,IAChClsC,mBAAmBgsC,GAErBA,GAER/rC,KAAK,MAEH8uC,G+BgRKC,CAAmB7yC,GAE7B,MAAM4wC,EAAU,CAEdtI,eAAWxV,EACXmG,OAAQ,GAERwR,OAAAA,EACAzqC,QAAS,IAAKA,GACd46B,KAAAA,EACAp3B,MAAOD,EAAqBC,GAC5B6kC,KAAMrF,GAGF8P,EAAS,IAAI7H,GACjB5qC,KAAKi3B,KAAKyb,SACV1yC,KAAKi3B,KAAK0b,gBACV3yC,KAAKi3B,KAAK6T,aAGNxkB,EAAM,IAAI0gB,GACd,CACES,UAAW,mBACXgC,OAAQzpC,KAAKi3B,KAAKwS,OAClBC,YAAa,MACb9Q,OAAQ,GACRuP,cAAenoC,KAAKi3B,KAAKyb,UAE3BD,GAGIG,EAAmBtsB,EAAIuhB,gBAAgB0I,GACvCsC,EAAa,IAAKlzC,GAElBmzC,EAA8B,CAClC1I,OAAAA,EACA2I,eAAgB/yC,KAAKi3B,KAAKyL,OAAS,IAAM,QAAQC,IACjD1+B,IAAKs2B,EACL/wB,OAAQrG,EACRxD,QAASkzC,EACTvzC,KAAMyyB,GAGR6gB,EAAiBtvC,QAAQ,CAAC1B,EAAOD,KAC/BmxC,EAAQnzC,QAAQgC,GAAOC,IAGzB,MAAMoxC,EAAkBjvC,EAAe/D,KAAKi3B,KAAKjzB,OACjD,SAAIgvC,GAAAA,EAAiB/uC,MAAQjE,KAAKi3B,KAAK0W,UAErCmF,EAAQC,QAAUC,EAAgB/uC,UAC9B+uC,GAAAA,EAAiBC,kBACnBH,EAAQtpC,OAAO,oBAAsBm5B,SAC9BkQ,EAAU,WAEd,GAAI7yC,KAAKi3B,KAAK0W,UAAW,CAC9B,IAAK3tC,KAAKi3B,KAAKic,UACb,MAAM,IAAI9yC,EACR,wDAKJ0yC,EAAQ9uC,MAAQ,CACdgkC,KAAMhoC,KAAKi3B,KAAK0W,UAChB7G,KAAM9mC,KAAKi3B,KAAKic,UAChBC,SAAU,QAIdN,EAAW,cAAgB7yC,KAAK6sC,UAC5B7sC,KAAKi3B,KAAK6Z,eAAiB,GAAkCjF,WAA7B7rC,KAAKi3B,KAAK6Z,iBAC5CgC,EAAQpH,QAAU1rC,KAAKi3B,KAAK6Z,gBAI5BgC,EAAQhG,UAAY9sC,KAAK8sC,UACzBgG,EAAQ/F,WAAa/sC,KAAK+sC,WAG5B,IACE,MAAMqG,EAAa,IAAKN,UACjBM,EAAWtG,iBACXsG,EAAWrG,WAClBjhC,GAAQ,YAAasnC,GACrB,MAAMjc,QAAYn3B,KAAK4sC,UAAU,CAE7BwB,cAAevC,SACfsC,iBAAkBtC,SAClBwH,QAASrzC,KAAKi3B,KAAK8Z,kBAElB+B,YACC7b,SAAAA,EAAMQ,YAAa,GACvB6b,mBAA2B,CACzB/C,QAAAA,EACAC,QAASlqB,KAKb,MAAO,CACLhnB,KAFW83B,EAAeD,GAG1Bz3B,WAAYy3B,EAAIh3B,OAChBR,QAASw3B,EAAIx3B,QACbC,UAAWu3B,EAAIx3B,QAAQ,oBACvBE,IAAKs3B,EAAIx3B,QAAQ,eAEnB,MAAOyE,GAAK,QACZ,GACE0pC,EAAM+B,aAAazrC,aACnBA,EAAI/E,oBAAJk0C,EAAc5zC,UAAd6zC,EAAwB,oBACxB,CAEA,MAAMn0C,EAA8C+E,EAAI/E,SAGxD,MAFAyM,GAAQ,4BAA6BzM,GACxB,IAAIH,EAAeG,GAMlC,MADAyM,GAAQ,QAAS1H,GACXA,GAIiByqB,kBACzB+J,EACAwR,EACAjnC,EACAxD,EACAoyB,EACAkF,GAEA,MAAMgW,EAAerU,GAAU54B,KAAKi3B,KAAK2B,OACzC,IAAKqU,EACH,MAAM,IAAI7sC,EAAe,6BAE3B,OAAOJ,KAAKyzC,MAAMrJ,EAAQ,IAAKjnC,EAAOxD,EAASoyB,EAAM,IAChDkF,EACHkb,gBAAiBlF,IAIOpe,mBAC1B5iB,EACAm+B,EACAjnC,EACAxD,EACAoyB,EACAkF,GAEA,MAAMgW,EACc,iBAAVhhC,GAAsBA,EAAM2sB,QAAW54B,KAAKi3B,KAAK2B,OACrDsU,EAA6B,iBAAVjhC,EAAqBA,EAAQA,EAAMtK,IAC5D,IAAKsrC,EACH,MAAM,IAAI7sC,EAAe,6BAI3B,OAFAyxB,GAAmBqb,GAEZltC,KAAKyzC,MACVrJ,MACI5mC,mBAAmB0pC,GACvB/pC,EACAxD,EACAoyB,EACA,IACKkF,EACHkb,gBAAiBlF,IAKbxE,kBACRx8B,GAEA,MAAMwmC,EAAS,IAAI7H,GACjB5qC,KAAKi3B,KAAKyb,SACV1yC,KAAKi3B,KAAK0b,gBACV3yC,KAAKi3B,KAAK6T,aAGNxkB,EAAM,IAAI0gB,GACd,CACES,UAAW,mBACXgC,OAAQzpC,KAAKi3B,KAAK0L,SAClB+G,YAAa,MAEb9Q,OAAQ3sB,EAAM2sB,OACduP,cAAenoC,KAAKi3B,KAAKyb,UAE3BD,GAGF,MAAI,WAAYxmC,EACPqa,EAAI0iB,wBACT,CACEE,OAAQj9B,EAAMi9B,QAEhBj9B,EAAMrG,SAGD0gB,EAAImiB,kBACT,CACE2B,OAAQn+B,EAAMm+B,OACd7P,KAAMtuB,EAAMsuB,KACZ0N,UAAWh8B,EAAMynC,UAAYznC,EAAM02B,cAAWlQ,EAC9CuV,KAAM/7B,EAAM02B,SACZx/B,MAAO8I,EAAM9I,OAEf8I,EAAMrG,SAiBF+tC,qBACR1nC,GAEA,MAAyB,iBAAVA,EAAqB,CAAE2sB,OAAQ3sB,GAAUA,EAEhDC,qBACRD,GAEA,MAAyB,iBAAVA,EAAqB,CAAEtK,IAAKsK,GAAUA,GElhBlD/K,eAAe0yC,GAEpB3nC,EAA0B,IAE1B,SAAmBe,GAAcf,EAC3BgB,QAAYjN,KAAK6zC,YACrB5nC,EAAM2sB,OACN,MACAl2B,EAAsBsK,GACtB,IAEIE,EAAYzL,EAAcwL,EAAI3N,MAKpC,OAJA4N,EAAU,kBACVA,EAAU,YACVA,EAAU,YACVA,EAAU,iBACHD,EA8BF/L,eAAe4yC,GAEpB7nC,EAAiC,IAEjC,SAAmBe,GAAcf,EAC3BgB,QAAYjN,KAAK6zC,YACrB5nC,EAAM2sB,OACN,MACAl2B,EAAsB,CAAEqxC,SAAU,MAAO/mC,IACzC,IAEIE,EAAYzL,EAAcwL,EAAI3N,MAIpC,OAHA4N,EAAU,kBACVA,EAAU,YACVA,EAAU,iBACHD,EC7EF/L,eAAe8yC,GAEpB/nC,EAA+B,IAE/B,MAAMgoC,aAAEA,GAAe,GAAUhoC,EAEjC,IAAIioC,EAKJ,GAJKjoC,EAAMkoC,UACTloC,EAAMkoC,QAViB,KAarBF,EACFC,QAAeE,GAAqBnc,KAAKj4B,KAAMiM,OAC1C,CACL,MAAMkoC,EAAUloC,EAAMkoC,QACtB,IAAI3qC,EAAS,IACRyC,EACHkoC,QAAAA,GAEF,OAAa,CACX,MAAMhd,QAAYid,GAAqBnc,KAAKj4B,KAAMwJ,GAiBlD,GAhBc,MAAV0qC,EACFA,EAAS/c,GAET+c,EAAS,IACJ/c,EACH73B,KAAM40C,EAAO50C,MAEf40C,EAAO50C,KAAK+0C,UAAYld,EAAI73B,KAAK+0C,SACjCH,EAAO50C,KAAKg1C,YAAcnd,EAAI73B,KAAKg1C,YACnCJ,EAAO50C,KAAKi1C,sBAAwBpd,EAAI73B,KAAKi1C,sBAC7CL,EAAO50C,KAAKk1C,SAAWN,EAAO50C,KAAKk1C,SAASre,OAAOgB,EAAI73B,KAAKk1C,UAC5DN,EAAO50C,KAAKm1C,eAAiBP,EAAO50C,KAAKm1C,eAAete,OACtDgB,EAAI73B,KAAKm1C,kBAIRtd,EAAI73B,KAAKg1C,aAAeJ,EAAO50C,KAAK+0C,UAAYF,EACnD,MAGF3qC,EAAOkrC,kBAAoBvd,EAAI73B,KAAKi1C,sBACpC/qC,EAAO2qC,QAAU3qC,EAAO2qC,QAAUhd,EAAI73B,KAAK+0C,UAI/C,OAAOH,EAEThzC,eAAekzC,GAEbnoC,GAEA,SAAmBe,GAAcf,EAE3BgB,QAAYjN,KAAK6zC,YACrB5nC,EAAM2sB,OACN,MACA,CACE+b,YAAa,KACVjyC,EAAsBsK,IAE3B,IAEIE,EAAYzL,EAAcwL,EAAI3N,MAGpC,OAFA4N,EAAU,kBACVA,EAAU,YACHD,QCzHI2nC,WAAwBlI,GAKnCmI,kBACoB70C,KAAK4sC,UAEb+C,aAAamF,QAAQlF,IAAKG,IAClC,MAAMpwC,EAAUowC,EAAOpwC,SAAW,GAUlC,cATOA,EAAO,cACdA,EAAO,KAAWK,KAAK+0C,mBAAmB/M,KAC1C+H,EAAOgD,QAAU/yC,KAAK+0C,mBAAmBC,OACzCjF,EAAOxmC,iBAAoBC,IACzB,MAAMyrC,EAAc1rC,EAAiBC,GACrC,MAAO,CAACxJ,KAAK+0C,mBAAmBG,OAAQD,GACrCtZ,OAAQr5B,GAAOA,EAAGsxB,QAClBnwB,KAAK,MAEHssC,IAIX3wC,YAAYutC,GACVptC,MAAM,IACDotC,EAEH/T,OAAQ,cACR6Q,OAAQ,cACRqB,YAAa,mBACb6H,gBAAiB,uBACjBhQ,SAAU,2BA9BdwS,gCAEQJ,+BAiDRtU,WAAaA,QACb0C,YAAcA,QACdyQ,YAAcA,QACdI,iBAAmBA,QACnBF,mBAAqBA,QACrBxP,aAAeA,GAvBbtkC,KAAKm1C,oBAAsBxI,EAC3B3sC,KAAK+0C,mBAAqB/0C,KAAKo1C,yBAC/Bp1C,KAAK60C,kBAGCO,yBACN,MACMC,EAAUr1C,KAAKm1C,oBAAoBG,UAAUjG,MADvC,+CAEZ,IAAKgG,EACH,MAAM,IAAIj1C,EAAe,oCAE3B,MAAO,CACL40C,OAAQK,EAAQ,GAChBrN,KAAMqN,EAAQ,GACdH,OAAQG,EAAQ,KCpBfn0C,eAAeq0C,GAA2BtpC,EAAyB,IACxE,MAAMtM,EAAU,UAIhBsM,SAAAA,EAAO/G,cACL8D,EAAmB,IAAKiD,EAAOtM,QAAAA,GAAW,CAAC,gBAC7C,MAAMw3B,QAAYn3B,KAAKyzC,MAAwB,MAAO,IAAK,GAAI9zC,GAI/D,OAHkB8B,EAAc01B,EAAI73B,KACpC4N,CAAU,WAEHiqB,EAGFj2B,eAAes0C,GAA4BvpC,GAChD,MAAMghC,EAAehhC,EAAM2sB,QAAU54B,KAAKi3B,KAAK2B,OAE/C,GAAIqU,EAAc,CAChB,GAAIA,EAAahkC,OAAS,GAAKgkC,EAAahkC,OAAS,GACnD,MAAM,IAAI7I,EACR,mDAGJ,IAAK,kBAAkBkyC,KAAKrF,GAC1B,MAAM,IAAI7sC,EACR,qDAGJ,GAAI,KAAKkyC,KAAKrF,IAAiB,KAAKqF,KAAKrF,GACvC,MAAM,IAAI7sC,+FAKd,MAAMT,EAAWsM,EAAMtM,QAAU+D,EAAoBuI,EAAMtM,SAoB3D,OAlBAqJ,EAAmBiD,EAAO,CACxB,MACA,mBACA,YACA,eACA,aACA,gBACA,eACA,eACA,sBAMFA,SAAAA,EAAO/G,cAAe8D,EAAmBiD,EAAO,CAAC,sBAE/BjM,KAAK6zC,YAAY5nC,EAAM2sB,OAAQ,MAAO,GAAIj5B,GAIvDuB,eAAeu0C,GAA4B7c,GAChD,OAAO54B,KAAK6zC,YAAYjb,EAAQ,SAAU,GAAI,IAUzC13B,eAAew0C,GAA0B9c,GAC9C,OAAO54B,KAAK6zC,YAA8Bjb,EAAQ,OAAQ,GAAI,QAAInG,EAAW,CAC3E2E,eAAiBD,IACR,IACFA,EAAIx3B,QACPg2C,YAAaxe,EAAIx3B,QAAQiyB,GAAUgkB,uBAapC10C,eAAe20C,GAEpB5pC,GAEA,MAAM2sB,OAAEA,EAAFnxB,aAAUA,GAAiBwE,EAEjC,OAAOjM,KAAK6zC,YACVjb,EACA,MACA,CAAEnxB,aAAc,IAChB,CACEquC,sBAAuBruC,ICzItBvG,eAAe60C,GAA4B9pC,GAChD,MAAMtM,EAAmB,GAWzB,OAVIsM,EAAM/F,MAAKvG,EAAQ,aAAesM,EAAM/F,WAE1BlG,KAAK6zC,YACrB5nC,EAAM2sB,OACN,MACA,CAAE1yB,IAAK,IACPvG,EACAsM,EAAM+pC,QACN,CAAEhE,SAAS,IAKR9wC,eAAe+0C,GAA4Brd,GAChD,MAAMzB,QAAYn3B,KAAK6zC,YACrBjb,EACA,MACA,CACE1yB,IAAK,IAEP,IAIF,OAFkBzE,EAAc01B,EAAI73B,KACpC4N,CAAU,UACHiqB,ECsFFj2B,eAAeg1C,GAAyBjqC,GAC7C,OAAOkqC,GAAWle,KAAKj4B,KAAMiM,GAGxB/K,eAAei1C,GAEpBlqC,GAGA,MAAMtM,GADNsM,EAAQjM,KAAKkM,qBAAqBD,IACXtM,QAAU+D,EAAoBuI,EAAMtM,SAC3DqJ,EAAmBiD,EAAO,CACxB,gBACA,aACA,gBACA,eACA,qBACA,kBACA,kBACA,cACA,UACA,MACA,mBACA,YACA,eACA,aACA,gBACA,gBACA,UACA,aACA,uBACA,2BACA,OACA,0BACA,eACA,eACA,WACA,cACA,kBACA,YAEFjM,KAAKmM,2BAA2BF,EAAOtM,GAEvC,MAAM4M,EAAYulB,GAAQ7lB,EAAM8lB,KAAMpyB,GAChC02B,EAA8B,MAAb9pB,EAElB8pB,IAAmBpqB,EAAMqqB,2BAA4BrqB,EAAMsqB,UAC9D1rB,QAAQC,kKAKV,IAAI0rB,EAAgB,EACpB,MAAMF,yBAAEA,EAAFC,SAA4BA,GAAatqB,EACzCwqB,EAAsB,CAC1BC,EACAC,EAAsB,KAGtB,IAAKN,GAAkBM,EAAc,EACnC,OAEF,IAAKL,IAA6BC,EAChC,OAEFC,GAAiBG,QAEjBL,GAAAA,EAA2B,CACzBI,KAAAA,EACAC,YAAAA,EACAH,cAAAA,EACAI,WAAYrqB,IAEd,MAAMsqB,EACc,IAAdtqB,EACEmqB,IAASnC,yBAAiBuC,QACrB,EAEF,EAEFN,EAAgBjqB,EAEH,IAAlBsqB,EACEH,IAASnC,yBAAiBuC,gBAC5BP,GAAAA,EAAWM,UAKbN,GAAAA,EAAWM,IAITE,QAAmB9E,GAAiB,CACxCF,KAAM9lB,EAAM8lB,KACZI,qBAAuBZ,GAAMkF,EAAoBlC,yBAAiByC,GAAIzF,GACtEa,gBAAiBnmB,EAAMmmB,gBACvBQ,UAAW5yB,KAAKi3B,KAAKrE,UACrBnD,YAAaxjB,EAAMwjB,cAGrBgH,EAAoBlC,yBAAiB2C,SAErC,MAqCO9yB,EAAK+yB,SAAajzB,EArCZhD,WACX,MAAMi2B,QAAYn3B,KAAKoM,aACrBH,EACA,MACA,GACAtM,EACAo3B,EAAWhF,MAAQ,GACnB,CACEqF,eAAiBD,UACf,MAAMsB,EAAS,IAAKtB,EAAIx3B,SAIxB,gBAHKsM,MAA+BrE,UAAYuvB,EAAI73B,OAClDm5B,EAAOO,kBAAoBvuB,KAAKf,UAAUytB,EAAI73B,OAEzCm5B,GAEThB,UAAW,CACTC,gBAAkB,CAChB/E,YAAa,KACX6D,EAAgB,QAChBO,EAAWpE,aAAXoE,EAAWpE,eAEbP,gBAAiB2E,EAAW3E,iBAE9BuF,iBAAmBC,IACjBnB,EACElC,yBAAiByC,GACjBY,EAAMC,OAASrB,OASzB,OAHIx2B,KAAKi3B,KAAKrE,WAAamE,EAAWltB,KACpCD,EAAsBmtB,EAAWltB,IAAKstB,EAAIx3B,SAErCw3B,GAE0BW,IAEnC,GAAI1zB,IAAQ+yB,EAEV,MADAV,EAAoBlC,yBAAiBwD,QAC/B3zB,EAIR,OADAqyB,EAAoBlC,yBAAiBuC,SAC9BK,EAOFj2B,eAAek1C,GAEpBnqC,GAEA,MAAMoqC,EAAoB3yC,EAAoBuI,EAAMtM,SAOpD,IAAK02C,EAAkB,kBAAmB,CACxC,MAAMle,QAAqBxtB,EAASsB,EAAMrB,UAC1CyrC,EAAkB,qBAAuBle,EAAMnG,KAGjD,MAAMI,EAAkBrnB,GAAyB,IAC/CJ,EAAqBsB,EAAMrB,WAG7B,IACE,aAAaurC,GAAWle,KAAKj4B,KAAM,IAC9BiM,EACH8lB,KAAMK,EAAgBjnB,OACtBxL,QAAS02C,EACTjkB,gBAAiBA,EAAgBjnB,OAEnC,MAAO/G,GAEP,MADAgH,GAAWgnB,EAAgBlnB,gBAAiB9G,GACtCA,GC7QHlD,eAAeo1C,GAA2BrqC,GAC/C,MAAMtM,EAAWsM,EAAMtM,QAAU+D,EAAoBuI,EAAMtM,SA6B3D,OA5BAqJ,EAAmBiD,EAAO,CACxB,MACA,mBACA,YACA,eACA,gBACA,gBACA,UACA,aACA,OACA,uBAEgBjM,KAAKoM,aACrBH,EACA,OACA,CACEwnC,MAAO,IAET9zC,EACA,CACE42C,IAAKtqC,EAAMhI,IACXuyC,cAAevqC,EAAMwqC,cACrBC,WAAYzqC,EAAM1G,YAEpB,CACEysC,SAAS,IAoCR9wC,eAAey1C,GAA4B1qC,GAChD,MAAMtM,EAAWsM,EAAMtM,QAAU+D,EAAoBuI,EAAMtM,SA+B3D,OA9BAqJ,EAAmBiD,EAAO,CACxB,MACA,mBACA,YACA,eACA,gBACA,gBACA,UACA,aACA,OACA,uBAGgBjM,KAAKoM,aACrBH,EACA,OACA,CACE2qC,UAAW,IAEbj3C,EACA,CACE42C,IAAKtqC,EAAMhI,IACXuyC,cAAevqC,EAAMwqC,cACrBC,WAAYzqC,EAAM1G,WAClBzF,OAAQmM,EAAMtK,KAEhB,CACEqwC,SAAS,aCpGC6E,GAEd5qC,GAEA4lB,GAAmB5lB,GACnB,MAAMy0B,EAAmC,iBAAVz0B,EAAqB,CAAEtK,IAAKsK,GAAUA,EAC/D02B,EAAWjC,EAAgBoW,qBAAuB92C,KAAKi3B,KAAK0L,SAC5D+Q,GACJhT,EAAgBoW,sBAAuBpW,EAAgB2R,eAGnDzZ,EAAS8H,EAAgB9H,QAAU54B,KAAKi3B,KAAK2B,QAAU,GAC7D,GAAI8a,IAAc9a,EAChB,MAAM,IAAIx4B,EAAe,6BAG3B,MAAO22C,EAAS7E,EAAS8E,GAAe,MACtC,MAAMC,EAAazzC,mBAAmBk9B,EAAgB/+B,KAChDu1C,EAAgBxW,EAAgB/+B,IACnC6xB,MAAM,KACNnxB,IAAKC,GAAOkB,mBAAmBlB,IAC/BmB,KAAK,KAER,OAAIiwC,EACK,IAAI9a,KAAU+J,QAAgBuU,MAAqBD,GAErD,CAACtU,MAAcuU,MAAqBD,IAVL,GAalCjqC,EAAiC0zB,EAAgBv9B,OAAS,GAC1Dg0C,EAAc,CAAChuC,EAAWvE,KACV,MAAhBoI,EAAU7D,IAAmB,MAALvE,IAC1BoI,EAAU7D,GAAKvE,IAGbvF,EAAWqhC,EAAgBrhC,UAAY,GAC7CS,OAAOyC,KAAKlD,GAAUiE,QAAS8zC,IAC7B,MAAMz1C,EAAMy1C,EACNC,EAAW30C,EAAsBf,GACvCw1C,cAAwBE,EAAYh4C,EAASsC,MAE3C++B,EAAgBp4B,WAClB6uC,EAAY,YAAazW,EAAgBp4B,WAG3C,MAAMnF,EAAQnD,KAAKyoC,kBAAkB,CACnC7P,OAAAA,EACAwR,OAAQ1J,EAAgB0J,QAAU,MAClC7P,KAAMyc,EACNrU,SAAAA,EACA+Q,UAAAA,EACA9tC,QAAS86B,EAAgB96B,SAAW,KACpCzC,MAAO6J,IAGHgmC,EAAkBjvC,EAAe/D,KAAKi3B,KAAKjzB,OACjD,IAAI+uC,SAAiB/yC,KAAKi3B,KAAKyL,OAAS,IAAM,QAAQqU,IAgBtD,aAfI/D,GAAAA,EAAiB/uC,MAGnB8uC,EAAUC,EAAgB/uC,IAAIrB,QAAQ,QAAS,UAC3CowC,GAAAA,EAAiBC,kBACnB9vC,EAAM,oBAAsB4zC,OAUtBhE,IAAUb,KANHpyC,OAAOyC,KAAKY,GAC1Bd,IAAKV,MACM6B,mBAAmB7B,MAAQ6B,mBAAmBL,EAAMxB,OAE/D8B,KAAK,OClFHvC,eAAeo2C,GAEpBrrC,GAEA,MAAMy0B,EAAmC,iBAAVz0B,EAAqB,CAAEtK,IAAKsK,GAAUA,EAC/D9I,EAA6B,GAkBnC,OAjBIu9B,EAAgBp4B,YAClBnF,EAAMmF,UAAYo4B,EAAgBp4B,WAEhCo4B,EAAgB6W,YAClBp0C,EAAMo0C,UAAY7W,EAAgB6W,WAEhC7W,EAAgBl/B,YAClB2B,EAAM3B,UAAYk/B,EAAgBl/B,iBAElBxB,KAAKoM,aACrBH,EACA,SACA9I,EACA,GACA,GACA,CAAEi0B,eAAiBD,GAAQA,EAAIx3B,UCzB5BuB,eAAes2C,GAA4BvrC,GAGhD,OAFAA,EAAMtM,QAAUsM,EAAMtM,SAAW,GACjCqJ,EAAmBiD,EAAO,CAAC,iBAAkB,oBACtCjM,KAAKoM,aACVH,EACA,MACA,CAAEvL,OAAQ,GAAI+2C,KAAMxrC,EAAMnI,QAC1BmI,EAAMtM,QACN,ICaGuB,eAAew2C,GAEpBzrC,GAEA,MAAM8lB,EAAO,CACX4lB,MAAO1rC,EAAM2rC,MACbC,QAAS5rC,EAAM6rC,QAAQz1C,IAAKC,KAC1By2B,IAAKz2B,EAAGX,IACRo2C,UAAWz1C,EAAGgG,cAIZnF,EAAgC,CACpC60C,OAAQ,IAGN/rC,EAAMsrC,YACRp0C,EAAMo0C,UAAYtrC,EAAMsrC,WAGtBtrC,EAAMzK,YACR2B,EAAM3B,UAAYyK,EAAMzK,WAG1B,MAAM21B,QAAYn3B,KAAK6zC,YACrB5nC,EAAM2sB,OACN,OACAz1B,EACA,GACA4uB,GAGI7kB,EAAYzL,EAAc01B,EAAI73B,MAIpC,OAHA4N,EAAU,WACVA,EAAU,SAEHiqB,ECtDFj2B,eAAe+2C,GAEpBhsC,GAEA,MAAMy0B,EAAmC,iBAAVz0B,EAAqB,CAAEtK,IAAKsK,GAAUA,EAC/D9I,EAA6B,CAAE+C,IAAK,IACtCw6B,EAAgBp4B,YAClBnF,EAAMmF,UAAYo4B,EAAgBp4B,WAGpC,MAAM6uB,QAAYn3B,KAAKoM,aAAiCH,EAAO,MAAO9I,EAAO,IAK7E,OAHkB1B,EAAc01B,EAAI73B,KACpC4N,CAAU,UAEHiqB,EAeFj2B,eAAeg3C,GAA4BjsC,GAChD,MAAMtM,EAAWsM,EAAMtM,QAAU+D,EAAoBuI,EAAMtM,SACrDwD,EAA6B,CAAE+C,IAAK,IAM1C,OALI+F,EAAM3D,YACRnF,EAAMmF,UAAY2D,EAAM3D,WAE1BU,EAAmBiD,EAAO,CAAC,QAEpBjM,KAAKoM,aACVH,EACA,MACA9I,EACAxD,EACAsM,EAAM+pC,SCpDH90C,eAAei3C,GAEpBlsC,GAEA,OAAOjM,KAAKoM,aACVH,EACA,SACA,CACEc,SAAUd,EAAMc,UAElB,ICeG7L,eAAek3C,GAEpBnsC,EAAmC,IAEnC,SAAmBe,GAAcf,EAC3BgB,QAAYjN,KAAK6zC,YACrB5nC,EAAM2sB,OACN,MACA,CACEvsB,QAAS,MACN3J,EAAsBsK,IAE3B,IAGIE,EAAYzL,EAAcwL,EAAI3N,MAIpC,OAHA4N,EAAU,WACVA,EAAU,kBAEHD,ECkCF/L,eAAem3C,GAEpBpsC,GAEA,MAAMy0B,EAAmBz0B,EAAQjM,KAAKkM,qBAAqBD,GACrDtM,EAAWsM,EAAMtM,QAAU+D,EAAoBuI,EAAMtM,SAC3DqJ,EAAmBiD,EAAO,CACxB,gBACA,eACA,qBACA,kBACA,kBACA,cACA,UACA,MACA,mBACA,YACA,eACA,gBACA,OACA,0BACA,eACA,iBAEFjM,KAAKmM,2BAA2BF,EAAOtM,GAEvC,MAAM4M,EAAYulB,GAAQ7lB,EAAM8lB,KAAMpyB,GAChC02B,EAA8B,MAAb9pB,EACvB,IAAK8pB,EACH,MAAM,IAAIj2B,8DAMZ,GAFAT,EAAQ,kBAAoBA,EAAQ,sBAAwB4M,EAExDvM,KAAKi3B,KAAKrE,WAA8B,IAAjB3mB,EAAMosB,SAAiBpsB,EAAMqsC,iBACtD,MAAM,IAAIl4C,EACR,6EAIJ,IAAIo2B,EAAgB,EACpB,MAAMF,yBAAEA,EAAFC,SAA4BA,GAAatqB,EACzCwqB,EAAsB,CAC1BC,EACAC,EAAsB,KAGtB,IAAKN,GAAkBM,EAAc,EACnC,OAEF,IAAKL,IAA6BC,EAChC,OAEFC,GAAiBG,QAEjBL,GAAAA,EAA2B,CACzBI,KAAAA,EACAC,YAAAA,EACAH,cAAAA,EACAI,WAAYrqB,IAEd,MAAMsqB,EACc,IAAdtqB,EACEmqB,IAASnC,yBAAiBuC,QACrB,EAEF,EAEFN,EAAgBjqB,EAEH,IAAlBsqB,EACEH,IAASnC,yBAAiBuC,gBAC5BP,GAAAA,EAAWM,UAKbN,GAAAA,EAAWM,IAITE,QAAmB9E,GAAiB,CACxCF,KAAM9lB,EAAM8lB,KACZI,qBAAuBZ,GAAMkF,EAAoBlC,yBAAiByC,GAAIzF,GACtEa,qBAAiBK,EACjBG,UAAW5yB,KAAKi3B,KAAKrE,UACrBnD,YAAaxjB,EAAMwjB,cAGrBgH,EAAoBlC,yBAAiB2C,SACrC,MAwCO9yB,EAAK+yB,SAAajzB,EAxCZhD,WACX,MAAMi2B,QAAYn3B,KAAKoM,aACrBH,EACA,OACA,CAAEssC,OAAQ,GAAIlgB,OAAQqI,EAAgBrI,QACtC14B,EACAo3B,EAAWhF,MAAQ,GACnB,CACEqF,eAAiBD,QACZA,EAAIx3B,QACP64C,kBAAmBrhB,EAAIx3B,QAAQ,4BAC/B63B,cAAeL,EAAIx3B,QAAQ,0BAE7B83B,UAAW,CACTC,gBAAkB,CAChB/E,YAAa,KACX6D,EAAgB,QAChBO,EAAWpE,aAAXoE,EAAWpE,eAEbP,gBAAiB2E,EAAW3E,iBAE9BuF,iBAAmBC,IACjBnB,EACElC,yBAAiByC,GACjBY,EAAMC,OAASrB,OAczB,OARIx2B,KAAKi3B,KAAKrE,WAAamE,EAAWltB,KAMpCD,EALwB2lB,GACtBmR,EAAgB4X,kBAAoB,IACpCvhB,EAAWltB,IAAIG,WACfuC,GAEqC4qB,EAAIx3B,SAEtCw3B,GAE0BW,IAEnC,GAAI1zB,IAAQ+yB,EAEV,MADAV,EAAoBlC,yBAAiBwD,QAC/B3zB,EAIR,OADAqyB,EAAoBlC,yBAAiBuC,SAC9BK,ECtMFj2B,eAAeu3C,GAEpBxsC,GAEA,MAAMy0B,EAAmC,iBAAVz0B,EAAqB,CAAEtK,IAAKsK,GAAUA,EAC/DtM,EAAW+gC,EAAgB/gC,QAAU+D,EACzCg9B,EAAgB/gC,SAElBqJ,EAAmB03B,EAAiB,CAClC,eACA,qBACA,kBACA,kBACA,cACA,UACA,SAEF,MAAMv9B,EAA6B,CAAEu1C,SAAU,IAK/C,OAJIhY,EAAgBp4B,YAClBnF,EAAMmF,UAAYo4B,EAAgBp4B,WAG7BtI,KAAKoM,aAAwBH,EAAO,OAAQ9I,EAAOxD,GC3BrDuB,eAAey3C,GAEpB1sC,GAEA4lB,GAAmB5lB,GACnBA,EAAQjM,KAAKkM,qBAAqBD,GAClC,MAAM2sC,UAAEA,EAAY,KAAdj3C,IAAoBA,GAAQsK,EAC5B2sB,EAAS3sB,EAAM2sB,QAAU54B,KAAKi3B,KAAK2B,OACnCigB,EAAS,IAAK5sC,EAAM4sC,QACpBC,EAAa,IAAK7sC,EAAM6sC,YAAc,IAE5C,IAAKlgB,EACH,MAAM,IAAIx4B,EAAe,6BAG3B,MAAMuyC,EAAkB3yC,KAAKi3B,KAAK0b,gBAC5BhJ,EAAO,IAAIjZ,KACXqoB,EAAoBC,GAAe,CACvCrP,KAAM,IAAIjZ,KAAKiZ,EAAKsP,UAAwB,IAAZL,GAChCliB,KAAM,QAEFwiB,EAAUF,KACVG,EAAWD,EAAQE,UAAU,EAAG,GAChCC,EAAU,MACVC,EAAa,UAEb1P,EAAQjU,GAAWgd,EAAiBwG,GACpCrP,EAAUnU,GAAWiU,EAAO5pC,KAAKi3B,KAAKwS,QACtCM,EAAWpU,GAAWmU,EAASuP,GAC/BlQ,EAAaxT,GAAWoU,EAAUuP,GAUlCC,EAAsC,CAC1C53C,IAAAA,EACA63C,kBAAmB,mBACnBC,aAAcP,EACdQ,mBAZiB,CACjB15C,KAAKi3B,KAAK6T,YACVqO,EACAn5C,KAAKi3B,KAAKwS,OACV4P,EACAC,GACA71C,KAAK,MAQHzD,KAAKi3B,KAAKyb,WACZ6G,EAAY,wBAA0Bv5C,KAAKi3B,KAAKyb,UAGlDoG,EAAWv1C,KAAK,CAAEq1B,OAAAA,IAClB94B,OAAOwJ,QAAQiwC,GAAaj2C,QAAQ,EAAE3B,EAAKC,MACzCi3C,EAAOl3C,GAAOC,IAEhB9B,OAAOwJ,QAAQuvC,GAAQv1C,QAAQ,EAAE3B,EAAKC,MACpCk3C,EAAWv1C,KAAK,CAAEsrB,CAACltB,MAASC,MAG9B,MAIM+3C,EAAYlvC,KAAKf,UAJR,CACbkwC,WAAYb,EACZD,WAAAA,IAGIe,EAAenwC,GAAUgB,GAAMivC,EAAW,SAAU,UACpDvS,EAAYzR,GAAWwT,EAAY0Q,EAAc,OAKvD,OAHAhB,EAAO3P,OAAS2Q,EAChBhB,EAAO,mBAAqBzR,EAErByR,EAUT,SAASG,GAAe/R,GACtB,MAAM0C,KAAEA,EAAO,IAAIjZ,KAAbgG,KAAqBA,EAAO,KAAQuQ,GAAO,GACjD,MAAa,QAATvQ,EACKiT,EAAKJ,cAIZI,EAAKJ,cAAc3mC,QAAQ,OAAQ,IAAIA,QAAQ,KAAM,IAAIA,QAAQ,KAAM,IACvE,ICzGJ,MAAMk3C,GAAiD,CACrDC,uBAAuB,EACvBC,6BAA6B,EAC7BC,oBAAoB,EACpBC,qBAAqB,EACrBC,qBAAqB,EACrBC,uBAAuB,EACvBC,iBAAiB,EACjBC,sBAAsB,EACtBC,sBAAsB,EACtBC,kBAAkB,EAClBC,kBAAkB,YAGJC,GACdt2C,EACA6yB,GAMA,MAAM0jB,4BAAEA,EAAFC,UAA+BA,EAA/BC,gBAA0CA,GAAoB5jB,EACpE,GAAI7yB,aAAelF,EACjB,GAAIy7C,GACF,GAAuB,MAAnBv2C,EAAI1E,WACN,OAAOiK,EAAuBkxC,EAAiBz2C,QAI9C,QAAoCquB,IAAhCkoB,GACgB,MAAnBv2C,EAAI1E,YAAsBo6C,GAAsBc,GAClD,OAAOjxC,EAAuBkxC,EAAiBz2C,GAIrD,MAAMA,ECJDlD,eAAe45C,GAEpB7uC,GAEA,OACGjM,KAAKi3B,KAAK8jB,mCACkCtoB,IAA3CzyB,KAAKi3B,KAAK8jB,8BACX9uC,EAAMi9B,OAAO8R,UAAU/xC,aAKRjJ,KAAK6zC,YACrB5nC,EAAM2sB,OACN,MACA,CAAEsQ,OAAQ,IACV,GACAj9B,EAAMi9B,OACN,CAAE8I,SAAS,IATJiJ,GAAmBhjB,KAAKj4B,KAAMiM,EAAM2sB,QAcxC13B,eAAem5C,GAEpBzhB,GAEA,IACE,MAAMzB,QAAYn3B,KAAK6zC,YACrBjb,EACA,MACA,CACEsQ,OAAQ,IAEV,IAWF,OATA/R,EAAI73B,KAAK07C,UAAU13C,QAAShB,IAC1B,MAAM4K,EAAYzL,EAAca,GAEhCxC,OAAOyC,KAAKD,EAAG44C,WAAa,IAAI53C,QAAS3B,IACvC7B,OAAOyC,KAAKD,EAAG44C,UAAUv5C,IAAM2B,QAAS63C,IACtCjuC,gBAAwBvL,QAAUw5C,aAIjChkB,EACP,MAAOnH,GACP,OAAO0qB,GAA8C1qB,EAAO,CAC1D2qB,4BAA6B36C,KAAKi3B,KAAK8jB,6BACvCH,UAAW,kBACXC,gBAAiB,CACfG,UAAW,GACXI,QAAS,iBAMVl6C,eAAe+5C,GAAkCriB,GACtD,OAAO54B,KAAK6zC,YAAYjb,EAAQ,SAAU,CAAEsQ,OAAQ,IAAM,IC1ErDhoC,eAAem6C,GAAmCziB,GACvD,OAAO54B,KAAK6zC,YACVjb,EACA,MACA,CAAE0iB,WAAY,IACd,IAIGp6C,eAAeq6C,GAEpBtvC,GAEA,OAAOjM,KAAK6zC,YACV5nC,EAAM2sB,OACN,MACA,CAAE0iB,WAAY,IACd,GACA,CACEE,OAAQvvC,EAAM9L,kBCsBJs7C,GAEdxvC,GAEA,MAAMy0B,EAAkBgb,GAAezjB,KAAKj4B,KAAMiM,GAElD0vC,GAAmB1vC,EAAM6sC,YAEzB,MAMM/F,SAAiB/yC,KAAKi3B,KAAKyL,OAAS,IAAM,QAL9Cz2B,EAAM6qC,sBACL7qC,EAAMomC,eACHryC,KAAKi3B,KAAK0L,YACPjC,EAAgB9H,UAAU54B,KAAKi3B,KAAK0L,cAYvCiZ,EAAWj3C,EARH3E,KAAKyoC,kBAAkB,CACnC7P,OAAQ8H,EAAgB9H,OACxBhzB,QAAS86B,EAAgB96B,QACzBsjC,OAAQ,CACN4P,WAAYpY,EAAgBoY,eAwBhC,MAAO,CACL+C,oBAlBAC,IAEA,MAAMC,EAAOp3C,EAAam3C,GACpBE,EAAI,CAACJ,EAAUG,GAAMpgB,OAAO4G,SAAS9+B,KAAK,KAChD,SAAUsvC,KAAWiJ,KAerBC,yBAZA,CAACt6C,EAAKm6C,KACJ,MAAMC,EAAOp3C,EAAam3C,GACpBE,EAAI,CAACJ,EAAUG,GAAMpgB,OAAO4G,SAAS9+B,KAAK,KAE1Cy4C,EAAUv6C,EACb6xB,MAAM,KACNnxB,IAAKC,GAAOkB,mBAAmBlB,IAC/BmB,KAAK,KACR,SAAUsvC,KAAWmJ,KAAWF,KAKlCG,YAAaP,GAIjB,SAASF,GAEPzvC,GAEA,MAAMghC,EAAehhC,EAAM2sB,QAAU54B,KAAKi3B,KAAK2B,OAG/C,IAAKqU,EACH,MAAM,IAAI7sC,EAAe,6BAG3Bu7C,GAAmB1vC,EAAM6sC,YACzB,MAAMsD,EACJnwC,EAAM6sC,WAAWz2C,IAAKC,GAAO,CAACA,EAAG+5C,UAAY,KAAM,OAAQ/5C,EAAGV,QAGhE,OAFAw6C,EAAqB74C,KAAK,CAAC,KAAM,UAAW0pC,IAErC,CACLrU,OAAQqU,EACRrnC,QAASqG,EAAMrG,SAbM,KAcrBkzC,WAAYsD,GAIhB,SAAST,GAAmB7C,GAC1B,GAAIA,EAAW7vC,OAAS,EACtB,MAAM,IAAI7I,EACR,kFAIJ,IAAK,MAAMkC,KAAMw2C,EAAY,CAC3B,GAAe,QAAXx2C,EAAGX,IACL,MAAM,IAAIvB,EACR,iEAIJ,GAAIkC,EAAG+5C,UAA4B,OAAhB/5C,EAAG+5C,UAAqC,gBAAhB/5C,EAAG+5C,SAC5C,MAAM,IAAIj8C,EACR,yFC3IDc,eAAeo7C,GAEpBrwC,GAEA,MAAM2sB,OAAEA,GAAW3sB,EAEnB,OAAOjM,KAAK6zC,YACVjb,EACA,MACA,CAAE2jB,SAAU,IACZ,ICDGr7C,eAAes7C,GAA6BvwC,GACjD,IACE,MAAM2sB,OAAEA,GAAW3sB,EAEnB,aAAajM,KAAK6zC,YAChBjb,EACA,MACA,CAAE6jB,KAAM,IACR,IAEF,MAAOzsB,GACP,OAAO0qB,GAA4C1qB,EAAO,CACxD6qB,gBAAiB,CAAE6B,UAAW,IAC9B/B,4BAA6B36C,KAAKi3B,KAAK8jB,6BACvCH,UAAW,mBAYV15C,eAAey7C,GAA6B1wC,GACjD,MAAM2sB,OAAEA,EAAF8jB,UAAUA,GAAczwC,EAC9B,OAAIjM,KAAKi3B,KAAK8jB,+BAAiC2B,EAAUzzC,OAChD2zC,GAAiB3kB,KAAKj4B,KAAM,CAAE44B,OAAAA,IAEhC54B,KAAK6zC,YACVjb,EACA,MACA,CAAE6jB,KAAM,IACR,GACA,CAAEC,UAAAA,IAUCx7C,eAAe07C,GAEpB3wC,GAEA,MAAM2sB,OAAEA,GAAW3sB,EAEnB,OAAOjM,KAAK6zC,YACVjb,EACA,SACA,CAAE6jB,KAAM,IACR,ICbGv7C,eAAe27C,GAEpB5wC,GAEA,MAAM2sB,OAAEA,EAAFkkB,MAAUA,GAAU7wC,EAC1B,GAAIjM,KAAKi3B,KAAK8jB,+BAAiC+B,EAAM7zC,OACnD,OAAO8zC,GAAsB9kB,KAAKj4B,KAAM,CAAE44B,OAAAA,IAG5C,MAAMj5B,EAAU,GAGhB,OAFAqJ,EAAmB,IAAKiD,EAAOtM,QAAAA,GAAW,CAAC,2BAEpCK,KAAK6zC,YACVjb,EACA,MACA,CAAEokB,UAAW,IACbr9C,EACA,CACEs9C,MAAOH,IAcN57C,eAAeg8C,GAEpBjxC,GAEA,IACE,MAAM2sB,OAAEA,GAAW3sB,EAEnB,aAAajM,KAAK6zC,YAChBjb,EACA,MACA,CAAEokB,UAAW,IACb,GACA,GACA,CACE5lB,eAAiBD,IACR,CACLgmB,uBACEhmB,EAAIx3B,QAAQ,mCACds9C,MAAO9lB,EAAI73B,KAAK29C,UAKxB,MAAOjtB,GACP,OAAO0qB,GAAiD1qB,EAAO,CAC7D2qB,4BAA6B36C,KAAKi3B,KAAK8jB,6BACvCH,UAAW,qBACXC,gBAAiB,CACfoC,MAAO,OAYR/7C,eAAe67C,GAEpB9wC,GAEA,MAAM2sB,OAAEA,GAAW3sB,EAEnB,OAAOjM,KAAK6zC,YACVjb,EACA,SACA,CAAEokB,UAAW,IACb,IClIG97C,eAAek8C,GAEpBnxC,GAEA,MAAM2sB,OAAEA,EAAFykB,KAAUA,GAASpxC,EAEzB,OAAOjM,KAAK6zC,YACVjb,EACA,MACA,CAAE0kB,WAAY,IACd,CACEC,cAAe7oB,GACbjqB,KAAKf,UAAU,CACb8zC,KAAMH,IAER,WAGJ,CACEG,KAAMH,IAKLn8C,eAAeu8C,GAEpBxxC,GAEA,MAAM2sB,OAAEA,GAAW3sB,EAEnB,OAAOjM,KAAK6zC,YACVjb,EACA,MACA,CAAE0kB,WAAY,IACd,IAIGp8C,eAAew8C,GAEpBzxC,GAEA,MAAM2sB,OAAEA,GAAW3sB,EAEnB,OAAOjM,KAAK6zC,YAAYjb,EAAQ,SAAU,CAAE0kB,WAAY,IAAM,ICMzDp8C,eAAey8C,GAEpB1xC,GAEA,MAAM2sB,OAAEA,EAAFkkB,MAAUA,GAAU7wC,EAC1B,OAAIjM,KAAKi3B,KAAK8jB,+BAAiC+B,EAAM7zC,OAC5C20C,GAAuB3lB,KAAKj4B,KAAM,CAAE44B,OAAAA,IAGtC54B,KAAK6zC,YACVjb,EACA,MACA,CAAEilB,OAAkB,IACpB,GACA,CACEZ,MAAOH,IAaN57C,eAAei5C,GAEpBluC,GAEA,MAAM2sB,OAAEA,GAAW3sB,EAEnB,IACE,aAAajM,KAAK6zC,YAChBjb,EACA,MACA,CAAEilB,OAAkB,IACpB,IAEF,MAAO7tB,GACP,OAAO0qB,GAAkD1qB,EAAO,CAC9D2qB,4BAA6B36C,KAAKi3B,KAAK8jB,6BACvCH,UAAW,sBACXC,gBAAiB,CACfoC,MAAO,OAYR/7C,eAAe08C,GAEpB3xC,GAEA,MAAM2sB,OAAEA,GAAW3sB,EAEnB,OAAOjM,KAAK6zC,YACVjb,EACA,SACA,CAAEilB,OAAkB,IACpB,ICjHG38C,eAAe48C,GAEpB7xC,GAEA,MAAM8xC,OAAEA,EAAFz1C,UAAUA,GAAc2D,EACxBtM,EAAU+D,EAAoB,CAClC4E,UAAAA,IAGF,OAAOtI,KAAKoM,aACVH,EACA,MACA,CAAE+xC,QAAkB,MAAOr+C,GAC3B,GACA,CACEs+C,OAAQF,IAeP78C,eAAeg9C,GAEpBjyC,GAEA,MAAM3D,UAAEA,GAAc2D,EAChBtM,EAAU+D,EAAoB,CAClC4E,UAAAA,IAEI6uB,QAAYn3B,KAAKoM,aACrBH,EAEA,MACA,CAAE+xC,QAAkB,MAAOr+C,GAC3B,IAGF,OADA8B,EAAc01B,EAAI73B,KAAK2+C,OAAvBx8C,CAA+B,QACxB01B,EAWFj2B,eAAei9C,GAEpBlyC,GAEA,MAAM3D,UAAEA,GAAc2D,EAChBtM,EAAU+D,EAAoB,CAClC4E,UAAAA,IAGF,OAAOtI,KAAKoM,aACVH,EACA,SACA,CAAE+xC,QAAkB,MAAOr+C,GAC3B,IC9CGuB,eAAek9C,GAEpBnyC,GAEA,MAAM2sB,OAAEA,EAAFkkB,MAAUA,EAAVuB,KAAiBA,GAASpyC,EAChC,OAAIjM,KAAKi3B,KAAK8jB,+BAAiC+B,EAAM7zC,OAC5Cq1C,GAAwBrmB,KAAKj4B,KAAM,CAAE44B,OAAAA,IAGvC54B,KAAK6zC,YACVjb,EACA,MACA,CAAE2lB,YAAkB,IACpB,GACA,CACEC,KAAMH,EACNpB,MAAOH,IAgBN57C,eAAeq5C,GAEpBtuC,GAEA,MAAM2sB,OAAEA,EAAFrC,SAAUA,EAAVkoB,OAAoBA,GAAWxyC,EAC/B9I,EAAgC,CACpCo7C,YAAkB,GAClBhoB,SAAUA,GAAY,IAEV,MAAVkoB,IACFt7C,EAAM,cAAgBs7C,GAGxB,IACE,aAAaz+C,KAAK6zC,YAChBjb,EACA,MACAz1B,EACA,IAEF,MAAOiB,GACP,OAAOs2C,GAAmDt2C,EAAK,CAC7Du2C,4BAA6B36C,KAAKi3B,KAAK8jB,6BACvCH,UAAW,uBACXC,gBAAiB,CACfoC,MAAO,GACPuB,KAAM,OAYPt9C,eAAeo9C,GAEpBryC,GAEA,MAAM2sB,OAAEA,GAAW3sB,EAEnB,OAAOjM,KAAK6zC,YACVjb,EACA,SACA,CAAE2lB,YAAkB,IACpB,ICjFGr9C,eAAew9C,GAEpBzyC,GAEA,MAAM2sB,OAAEA,KAAW+lB,GAAe1yC,EAE5B8lB,EAAOjvB,EAA6B67C,GAC1C,OAAO3+C,KAAK6zC,YACVjb,EACA,MACA,CAAEgmB,QAAkB,IACpB,GACA,IACK7sB,IAgBF7wB,eAAeu5C,GAEpBxuC,GAEA,MAAM2sB,OAAEA,GAAW3sB,EAEnB,IACE,OAAOjM,KAAK6zC,YACVjb,EACA,MACA,CAAEgmB,QAAkB,IACpB,IAEF,MAAO5uB,GACP,OAAO0qB,GAA+C1qB,EAAO,CAC3D2qB,4BAA6B36C,KAAKi3B,KAAK8jB,6BACvCH,UAAW,mBACXC,gBAAiB,CACfgE,aAAc,OAYf39C,eAAe49C,GAEpB7yC,GAEA,MAAM2sB,OAAEA,GAAW3sB,EAEnB,OAAOjM,KAAK6zC,YACVjb,EACA,SACA,CAAEgmB,QAAkB,IACpB,ICpEG19C,eAAe69C,GAEpB9yC,GAEA,MAAM2sB,OAAEA,KAAW+lB,GAAe1yC,EAE5B8lB,EAAOjvB,EAA6B67C,GAC1C,OAAO3+C,KAAK6zC,YACVjb,EACA,MACA,CAAEomB,aAAkB,IACpB,GACA,IACKjtB,IAiBF7wB,eAAek5C,GAEpBnuC,GAEA,MAAM2sB,OAAEA,GAAW3sB,EACnB,IACE,aAAajM,KAAK6zC,YAChBjb,EACA,MACA,CAAEomB,aAAkB,IACpB,IAEF,MAAOhvB,GACP,OAAO0qB,GAAoD1qB,EAAO,CAChE2qB,4BAA6B36C,KAAKi3B,KAAK8jB,6BACvCH,UAAW,wBACXC,gBAAiB,CACfoE,4BAA6B,GAC7BC,uBAAwB,OC5DzBh+C,eAAei+C,GAEpBlzC,GAEA,MAAM2sB,OAAEA,KAAW+lB,GAAe1yC,EAE5B8lB,EAAOjvB,EAA6B67C,GAC1C,OAAO3+C,KAAK6zC,YACVjb,EACA,MACA,CAAEwmB,aAAkB,IACpB,GACA,IACKrtB,IAaF7wB,eAAe64C,GAEpB9tC,GAEA,IACE,MAAM2sB,OAAEA,GAAW3sB,EACnB,aAAajM,KAAK6zC,YAChBjb,EACA,MACA,CAAEwmB,aAAkB,IACpB,IAEF,MAAOpvB,GACP,OAAO0qB,GAAoD1qB,EAAO,CAChE6qB,gBAAiB,CAAEwE,kBAAmB,IACtC1E,4BAA6B36C,KAAKi3B,KAAK8jB,6BACvCH,UAAW,2BAYV15C,eAAeo+C,GAEpBrzC,GAEA,MAAM2sB,OAAEA,EAAF2mB,aAAUA,GAAiBtzC,EAEjC,OAAOjM,KAAK6zC,YACVjb,EACA,SACA,CAAEwmB,aAAcG,GAChB,ICzEGr+C,eAAes+C,GAEpBvzC,GAEA,MAAM2sB,OAAEA,KAAW+lB,GAAe1yC,EAE5B8lB,EAAOjvB,EAA6B67C,GAC1C,OAAO3+C,KAAK6zC,YACVjb,EACA,MACA,CAAE6mB,YAAkB,IACpB,GACA,IACK1tB,IAaF7wB,eAAeo5C,GAEpBruC,GAEA,MAAM2sB,OAAEA,GAAW3sB,EAEnB,IACE,aAAajM,KAAK6zC,YAChBjb,EACA,MACA,CAAE6mB,YAAkB,IACpB,IAEF,MAAOzvB,GACP,OAAO0qB,GAAmD1qB,EAAO,CAC/D2qB,4BAA6B36C,KAAKi3B,KAAK8jB,6BACvCH,UAAW,uBACXC,gBAAiB,MAWhB35C,eAAew+C,GAEpBzzC,GAEA,MAAM2sB,OAAEA,GAAW3sB,EAEnB,OAAOjM,KAAK6zC,YACVjb,EACA,SACA,CAAE6mB,YAAkB,IACpB,IClFJ,IAAYE,GAUAC,GAUAC,GCtBAC,GAQAC,GAQAC,GAgBAC,GDoFL/+C,eAAe+4C,GAEpBiG,GAEA,IAWE,aAVkBlgD,KAAK6zC,YACrBqM,EAAItnB,OACJ,MACA,CACEunB,UAAW,GACXC,GAAIF,EAAIE,IAEV,IAIF,MAAOpwB,GACP,OAAO0qB,GAAiD1qB,EAAO,CAC7D2qB,4BAA6B36C,KAAKi3B,KAAK8jB,6BACvCH,UAAW,qBACXC,qBAAiBpoB,KAQhBvxB,eAAeg5C,GAEpBgG,GAEA,MAAM12C,EAAS,CACb22C,UAAW,MACPD,EAAIxL,kBACJ,CAAE2L,qBAAsBH,EAAIxL,mBAC5B,MAEN,IAOE,aANkB10C,KAAK6zC,YACrBqM,EAAItnB,OACJ,MACApvB,EACA,IAGF,MAAOwmB,GACP,OAAO0qB,GAAkD1qB,EAAO,CAC9D2qB,4BAA6B36C,KAAKi3B,KAAK8jB,6BACvCH,UAAW,sBACXC,gBAAiB,CACfyF,wBAAyB,OAS1Bp/C,eAAeq/C,GAEpBL,GAEA,OAAOlgD,KAAK6zC,YACVqM,EAAItnB,OACJ,SACA,CAAEunB,UAAW,GAAIC,GAAIF,EAAIE,IACzB,aAOYI,GAEdN,GAEA,OAAOlgD,KAAK6zC,YACVqM,EAAItnB,OACJ,MACA,CAAEunB,UAAW,GAAIC,GAAIF,EAAIO,uBAAuBC,IAChD,GACAR,EAAIO,wBEmBDv/C,eAAey/C,GAAyBn3C,GAC7C,MAAMo3C,UAAEA,KAAczxB,GAAU3lB,EAC1BlK,EAAOwD,EAA6BqsB,GAY1C,aAXkBnvB,KAAKyzC,MACrB,OACA,QACA,GACA,CACEoN,mBAAoBD,GAEtB,IACKthD,IAYF4B,eAAe4/C,GAAwBt3C,GAC5C,MAAMo3C,UAAEA,EAAFG,WAAaA,EAAa,OAASC,GAAWx3C,EAkBpD,aAjBkBxJ,KAAKyzC,MACrB,MACA,QACA,CACEsN,WAAAA,KACGC,GAEL,CACEH,mBAAoBD,GAEtB,GACA,CACEnpB,UAAW,CACTluB,iBAAAA,KAcDrI,eAAe+/C,GAEpBz3C,GAEA,MAAMo3C,UAAEA,EAAWM,MAAOC,EAApBC,SAA2BA,GAAa53C,EAe9C,aAdkBxJ,KAAKyzC,MACrB,gBACS0N,aACT,CACEC,SAAAA,GAEF,CACEP,mBAAoBD,GAEtB,GACA,CACE5O,SAAS,IAaR9wC,eAAemgD,GAEpB73C,GAEA,MAAMo3C,UACJA,EACAM,MAAOC,EAFHG,mBAGJA,EAHIC,mBAIJA,GACE/3C,EAgBJ,aAfkBxJ,KAAKyzC,MACrB,gBACS0N,WACT,CACEG,mBAAAA,EACAC,mBAAAA,GAEF,CACEV,mBAAoBD,GAEtB,GACA,CACE5O,SAAS,IAaR9wC,eAAesgD,GAAyBh4C,GAC7C,MAAMo3C,UAAEA,EAAFO,MAAaA,GAAU33C,EAU7B,aATkBxJ,KAAKyzC,MACrB,kBACS0N,EACT,GACA,CACEN,mBAAoBD,GAEtB,IAYG1/C,eAAeugD,GAA2Bj4C,GAC/C,MAAMo3C,UAAEA,EAAFO,MAAaA,GAAU33C,EAU7B,aATkBxJ,KAAKyzC,MACrB,eACS0N,EACT,GACA,CACEN,mBAAoBD,GAEtB,ICtVG1/C,eAAewgD,GAEpBz1C,GAYA,aAVkBjM,KAAK6zC,YACrB5nC,EAAM2sB,OACN,MACA,CAAEolB,QAAS,IACX,GACA/xC,EAAM+xC,QACN,CACEhM,SAAS,IASf9wC,eAAsBs5C,IAEpB5hB,OAAEA,IAEF,IASE,aARkB54B,KAAK6zC,YACrBjb,EACA,MACA,CACEolB,QAAS,IAEX,IAGF,MAAOhuB,GACP,OAAO0qB,GAA+C1qB,EAAO,CAC3D2qB,4BAA6B36C,KAAKi3B,KAAK8jB,6BACvCH,UAAW,mBACXC,gBAAiB,CACfoD,OAAQ,CACN0D,KAAM,QAchBzgD,eAAsB0gD,IAEpBhpB,OAAEA,IAEF,OAAO54B,KAAK6zC,YAAYjb,EAAQ,SAAU,CAAEolB,QAAS,IAAM,IClEtD98C,eAAe2gD,GAEpB51C,GASA,aAPkBjM,KAAK6zC,YACrB5nC,EAAM2sB,OACN,MACA,CAAEkpB,aAAc,IAChB,GACA71C,EAAM61C,cAWV5gD,eAAsB6gD,IAEpBnpB,OAAEA,IAUF,aARkB54B,KAAK6zC,YACrBjb,EACA,MACA,CACEkpB,aAAc,IAEhB,ICjBG5gD,eAAe8gD,GAEpB9B,GAEA,MAAMtnB,OAAEA,GAAWsnB,EACnB,IASE,aARkBlgD,KAAK6zC,YACrBjb,EACA,MACA,CACEqpB,oBAAqB,IAEvB,IAGF,MAAO79C,GACP,GAAIA,aAAelF,GACM,MAAnBkF,EAAI1E,WACN,OAAOM,KAAK2J,uBACV,CACEu4C,WAAYtpB,EACZupB,oBAAqB,IAEvB/9C,GAKN,MAAMA,GAOHlD,eAAekhD,GAEpBxpB,GAEA,IASE,aARkB54B,KAAK6zC,YACrBjb,EACA,MACA,CACEypB,WAAY,IAEd,IAGF,MAAOj+C,GACP,GAAIA,aAAelF,GACM,MAAnBkF,EAAI1E,WACN,OAAOM,KAAK2J,uBACV,CACE24C,YAAa,IAEfl+C,GAKN,MAAMA,GAYHlD,eAAeqhD,GAEpBrC,GAEA,IACE,MAAMtnB,OAAEA,EAAF4pB,UAAUA,GAActC,EAU9B,aATkBlgD,KAAK6zC,YACrBjb,EACA,MACA,CACE6pB,kBAAmB,GACnBD,UAAAA,GAEF,IAGF,MAAOp+C,GACP,GAAIA,aAAelF,GACM,MAAnBkF,EAAI1E,WACN,OAAOM,KAAK2J,uBACV,CACE24C,YAAa,IAEfl+C,GAKN,MAAMA,GAOHlD,eAAewhD,GAEpB9pB,EACA4pB,GAEA,IAUE,aATkBxiD,KAAK6zC,YACrBjb,EACA,MACA,CACEypB,WAAY,GACZG,UAAAA,GAEF,IAGF,MAAOp+C,GACP,GAAIA,aAAelF,GACM,MAAnBkF,EAAI1E,WACN,OAAOM,KAAK2J,uBAAuB,KAAMvF,GAI7C,MAAMA,GAcHlD,eAAeyhD,GAEpBzC,GAEA,MAAMtnB,OAAEA,EAAF4pB,UAAUA,EAAV5lB,QAAqBA,EAArBgmB,kBAA8BA,GAAsB1C,EAC1D,IAYE,aAXkBlgD,KAAK6zC,YACrBjb,EACA,MACA,CACEypB,WAAY,GACZG,UAAAA,EACAI,kBAAAA,GAEF,GACA,CAAEC,QAASjmB,IAGb,MAAOx4B,GACP,GAAIA,aAAelF,GACM,MAAnBkF,EAAI1E,WACN,OAAOM,KAAK2J,uBAAuB,KAAMvF,GAI7C,MAAMA,GAaHlD,eAAe4hD,GAEpB5C,GAEA,MAAMsC,UAAEA,EAAFI,kBAAaA,EAAbhqB,OAAgCA,GAAWsnB,EACjD,IAWE,aAVkBlgD,KAAK6zC,YACrBjb,EACA,SACA,CACEypB,WAAY,GACZG,UAAAA,EACAI,kBAAAA,GAEF,IAGF,MAAOx+C,GACP,GAAIA,aAAelF,GACM,MAAnBkF,EAAI1E,WACN,OAAOM,KAAK2J,uBAAuB,KAAMvF,GAG7C,MAAMA,GAgBHlD,eAAe6hD,GAEpBnqB,EACAt5B,GAEA,IAUE,aATkBU,KAAK6zC,YACrBjb,EACA,MACA,CACEoqB,qBAAsB,IAExB,GACA1jD,GAGF,MAAO8E,GACP,GAAIA,aAAelF,GACM,MAAnBkF,EAAI1E,WACN,OAAOM,KAAK2J,uBAAuB,KAAMvF,GAG7C,MAAMA,GAOHlD,eAAe+hD,GAEpBrqB,GAEA,IASE,aARkB54B,KAAK6zC,YACrBjb,EACA,MACA,CACEoqB,qBAAsB,IAExB,IAGF,MAAO5+C,GACP,GAAIA,aAAelF,GACM,MAAnBkF,EAAI1E,WACN,OAAOM,KAAK2J,uBAAuB,KAAMvF,GAG7C,MAAMA,GAkBHlD,eAAegiD,GAEpBhD,GAEA,MAAMtnB,OAAEA,EAAFuqB,UAAUA,EAAVC,gBAAqBA,GAAoBlD,EAC/C,IAUE,aATkBlgD,KAAK6zC,YACrBjb,EACA,MACA,CACEyqB,oBAAqB,IAEvB,GACA,CAAEF,UAAAA,EAAWC,gBAAAA,IAGf,MAAOh/C,GACP,GAAIA,aAAelF,GACM,MAAnBkF,EAAI1E,WACN,OAAOM,KAAK2J,uBAAuB,KAAMvF,GAG7C,MAAMA,GAOHlD,eAAeoiD,GAEpB1qB,GAEA,IASE,aARkB54B,KAAK6zC,YACrBjb,EACA,MACA,CACEyqB,oBAAqB,IAEvB,IAGF,MAAOj/C,GACP,GAAIA,aAAelF,GACM,MAAnBkF,EAAI1E,WACN,OAAOM,KAAK2J,uBAAuB,KAAMvF,GAG7C,MAAMA,GCvWHlD,eAAe84C,GAEpBphB,GAEA,IAOE,aANkB54B,KAAK6zC,YACrBjb,EACA,MACA,CAAE2qB,mBAAoB,IACtB,IAGF,MAAOvzB,GACP,OAAO0qB,GAAuD1qB,EAAO,CACnE2qB,4BAA6B36C,KAAKi3B,KAAK8jB,6BACvCH,UAAW,8BACXC,gBAAiB,MCfhB35C,eAAesiD,GAEpBv3C,GAEA,MAAM2sB,OAAEA,KAAW+lB,GAAe1yC,EAE5B8lB,EAAOjvB,EAA6B67C,GAC1C,OAAO3+C,KAAK6zC,YACVjb,EACA,MACA,CAAEl4B,OAAkB,IACpB,GACA,IACKqxB,IAaF7wB,eAAeuiD,GAEpBx3C,GAEA,MAAM2sB,OAAEA,GAAW3sB,EACnB,aAAajM,KAAK6zC,YAChBjb,EACA,MACA,CAAEl4B,OAAkB,IACpB,IAUGQ,eAAewiD,GAEpBz3C,GAEA,MAAM2sB,OAAEA,GAAW3sB,EAEnB,OAAOjM,KAAK6zC,YACVjb,EACA,SACA,CAAEl4B,OAAkB,IACpB,ICrDGQ,eAAeyiD,GAA6B13C,GACjD,MAAM3D,UAAeA,KAAcq2C,GAAe1yC,EAC5C9I,EAA6B,CAAEygD,QAAS,IAC1Ct7C,IACFnF,EAAMmF,UAAYA,GAEpB,MAAMypB,EAAOjvB,EAA6B67C,GAE1C,OAAO3+C,KAAKoM,aAAwBH,EAAO,OAAQ9I,EAAO,GAAI4uB,GC4CzD7wB,eAAe2iD,GAA+Br6C,GACnD,MAAMo3C,UAAEA,GAAcp3C,EAetB,aAdkBxJ,KAAKyzC,MACrB,MACA,eACA,GACA,CACEoN,mBAAoBD,GAEtB,GACA,CACEnpB,UAAW,CACTluB,iBAAAA,KAaDrI,eAAe4iD,GAEpBt6C,GAEA,MAAMo3C,UAAEA,EAAFF,GAAaA,GAAOl3C,EAe1B,aAdkBxJ,KAAKyzC,MACrB,wBAEA,CACE2M,GAAIM,GAEN,CACEG,mBAAoBD,GAEtB,GACA,CACE5O,SAAS,IAYR9wC,eAAe6iD,GAA8Bv6C,GAClD,MAAMo3C,UAAEA,EAAFF,GAAaA,GAAOl3C,EAe1B,aAdkBxJ,KAAKyzC,MACrB,qBAEA,CACE2M,GAAIM,GAEN,CACEG,mBAAoBD,GAEtB,GACA,CACE5O,SAAS,IAWR9wC,eAAe8iD,GAEpBx6C,GAEA,MAAMo3C,UAAEA,EAAFF,GAAaA,KAAOuD,GAASz6C,EAmBnC,aAjBkBxJ,KAAKyzC,MACrB,qBAEA,CACE2M,GAAIM,GAEN,CACEG,mBAAoBD,GAEtB,IACKqD,EACHvD,GAAAA,GAEF,CACE1O,SAAS,ICzHR9wC,eAAegjD,GAEpBj4C,GAEA,MAAM2sB,OAAEA,KAAW+lB,GAAe1yC,EAE5B8lB,EAAOjvB,EAA6B67C,GAC1C,OAAO3+C,KAAK6zC,YACVjb,EACA,MACA,CAAEurB,gBAAkB,IACpB,GACA,IACKpyB,IAcF7wB,eAAekjD,GAEpBn4C,GAEA,MAAM2sB,OAAEA,GAAW3sB,EACnB,IACE,aAAajM,KAAK6zC,YAChBjb,EACA,MACA,CAAEurB,gBAAkB,IACpB,IAEF,MAAOn0B,GACP,OAAO0qB,GAAoD1qB,EAAO,CAChE2qB,4BAA6B36C,KAAKi3B,KAAK8jB,6BACvCH,UAAW,6BACXC,gBAAiB,CACfoC,MAAO,OCvDR/7C,eAAemjD,GAA0Bp4C,GAC9C,OAAOq4C,GAAYrsB,KAAKj4B,KAAMiM,GAGzB/K,eAAeojD,GAEpBr4C,GAEA,MAAMtM,EAAWsM,EAAMtM,QAAU+D,EAAoBuI,EAAMtM,SAS3D,OARAqJ,EAAmBiD,EAAO,CACxB,mBACA,sBACA,kBACA,MACA,eACA,SAEKjM,KAAKoM,aACVH,EACA,MACA,CAAEs4C,QAAS,IACX5kD,OACA8yB,EACA,CACE2E,eAAe/3B,GACb,MAAMM,QAAEA,GAAYN,EACpB,MAAO,CACLq5B,UAAW/4B,EAAQ,wBCxCtBuB,eAAesjD,GAA0Bv4C,GAC9C,OAAOw4C,GAAYxsB,KAAKj4B,KAAMiM,GAGzB/K,eAAeujD,GAEpBx4C,GAEA,MAAM9I,EAA6B,CAAEohD,QAAS,IAI9C,OAHIt4C,EAAM3D,YACRnF,EAAMmF,UAAY2D,EAAM3D,WAEnBtI,KAAKoM,aAA2BH,EAAO,MAAO9I,EAAO,QAAIsvB,EAAW,CACzE2E,eAAiBD,IACf,MAAMx3B,QAAEA,GAAYw3B,EACpB,MAAO,CACLuB,UAAW/4B,EAAQ,oBACnB+kD,iBAAkB/kD,EAAQ,wBAC1BglD,oBAAqBhlD,EAAQ,wBAC7BilD,aAAcjlD,EAAQ,qBCjBvBuB,eAAe2jD,GAEpB54C,GAEA,MAAM2sB,OAAEA,KAAW+lB,GAAe1yC,EAE5B8lB,EAAOjvB,EAA6B67C,GAC1C,OAAO3+C,KAAK6zC,YACVjb,EACA,MACA,CAAEksB,qBAAkB,IACpB,GACA,IACK/yB,IAoBF7wB,eAAe6jD,GAEpB94C,GAEA,IACE,MAAM2sB,OAAEA,GAAW3sB,EACbtM,EAAmB,GAUzB,OATIsM,EAAM+4C,YACRrlD,EAAQ,wCAA0C,cAElCK,KAAK6zC,YACrBjb,EACA,MACA,CAAEksB,qBAAkB,IACpBnlD,GAGF,MAAOqwB,GACP,OAAO0qB,GAA4D1qB,EAAO,CACxE2qB,4BAA6B36C,KAAKi3B,KAAK8jB,6BACvCH,UAAW,gCACXC,gBAAiB,CACfoK,kCAAmC,CACjCC,QAAS,QACT1J,OAAQlb,uCAA+B6kB,gBC/D1CjkD,eAAekkD,GAEpBn5C,GAEA,MAAM2sB,OAAEA,EAAFz4B,OAAUA,GAAW8L,EAE3B,OAAOjM,KAAK6zC,YACVjb,EACA,MACA,CAAEysB,cAAe,IACjB,GACA,CACE7J,OAAQr7C,IAgBPe,eAAeokD,GAEpBr5C,GAEA,IACE,MAAM2sB,OAAEA,GAAW3sB,EAEnB,aAAajM,KAAK6zC,YAChBjb,EACA,MACA,CAAEysB,cAAe,IACjB,IAEF,MAAOr1B,GACP,OAAO0qB,GAAqD1qB,EAAO,CACjE2qB,4BAA6B36C,KAAKi3B,KAAK8jB,6BACvCH,UAAW,yBACXC,gBAAiB,MbkDhB35C,eAAeqkD,GAA4B/7C,GAChD,MAAMo3C,UAAEA,GAAcp3C,EAYtB,aAXkBxJ,KAAKyzC,MACrB,MACA,aACA,GACA,CACEoN,mBAAoBD,GAEtB,GACA,IAWG1/C,eAAeskD,GAA4Bh8C,GAChD,MAAMo3C,UAAEA,KAAc6E,GAAej8C,EAcrC,aAbkBxJ,KAAKyzC,MACrB,MACA,aACA,GACA,CACEoN,mBAAoBD,GAEtB,IACK6E,GAEL,IAWGvkD,eAAewkD,GAEpBl8C,GAEA,MAAMo3C,UAAEA,GAAcp3C,EAYtB,aAXkBxJ,KAAKyzC,MACrB,SACA,aACA,GACA,CACEoN,mBAAoBD,GAEtB,GACA,IczIG1/C,eAAeykD,GAEpB15C,GAEA,MAAM20C,UAAEA,EAAFnJ,KAAaA,EAAbmO,QAAmBA,GAAY35C,EAkBrC,aAhBkBjM,KAAKyzC,MACrB,OACA,QACA,CACEgE,KAAAA,GAEF,CACEoJ,mBAAoBD,GAEtB,CACEiF,KAAMpO,EACNqO,QAASF,GAEX,IAgBG1kD,eAAe6kD,GAEpB95C,GAEA,MAAMwrC,KAAEA,EAAFmJ,UAAQA,GAAc30C,EAc5B,aAbkBjM,KAAKyzC,MACrB,MACA,QACA,CACEgE,KAAAA,GAEF,CACEoJ,mBAAoBD,GAEtB,GACA,IAoBG1/C,eAAe8kD,GAEpB/5C,GAEA,MAAM20C,UAAEA,KAAc5zC,GAAcf,EAYpC,aAXkBjM,KAAKyzC,MACrB,MACA,QACA,IAAKzmC,GACL,CACE6zC,mBAAoBD,GAEtB,GACA,IAyBG1/C,eAAe+kD,GAEpBh6C,GAEA,MAAM20C,UAAEA,EAAFsF,MAAaA,GAAUj6C,EAW7B,aAVkBjM,KAAKyzC,MACrB,MACA,eACA,CACEyS,MAAAA,GAEF,CACErF,mBAAoBD,IAanB1/C,eAAeilD,GAEpBl6C,GAEA,MAAMwrC,KAAEA,EAAFmJ,UAAQA,GAAc30C,EAW5B,aAVkBjM,KAAKyzC,MACrB,SACA,QACA,CACEgE,KAAAA,GAEF,CACEoJ,mBAAoBD,IAcnB1/C,eAAeklD,GAEpBn6C,GAEA,MAAMo6C,OAAEA,EAAFzF,UAAUA,EAAVsF,MAAqBA,GAAUj6C,EAcrC,aAbkBjM,KAAKyzC,MACrB,QACA,eACA,CACEyS,MAAAA,GAEF,CACErF,mBAAoBD,GAEtB,CACE0F,OAAQD,KfrMd,SAAY1G,GAEVA,gBAEAA,kBAJF,CAAYA,KAAAA,QAUZ,SAAYC,GAEVA,YAEAA,oBAJF,CAAYA,KAAAA,QAUZ,SAAYC,GAEVA,cAEAA,qCAEAA,cAEAA,8BAEAA,4CAEAA,sCACAA,gBAEAA,wCAfF,CAAYA,KAAAA,QCtBZ,SAAYC,GACVA,8BACAA,oCACAA,kDACAA,wDACAA,0BACAA,gCANF,CAAYA,KAAAA,QAQZ,SAAYC,GACVA,0BACAA,gCACAA,8BACAA,0CACAA,oCACAA,gDANF,CAAYA,KAAAA,QAQZ,SAAYC,GACVA,wBACAA,8BAFF,CAAYA,KAAAA,QAgBZ,SAAYC,GAEVA,wBAEAA,sBAEAA,oBAEAA,0BAEAA,wBAVF,CAAYA,KAAAA,QeRL,MAAMsG,GAAsCrlD,eAEjD+K,GAEA,MAAM20C,UAAEA,EAAFsF,MAAaA,EAAbpJ,MAAoBA,GAAU7wC,EAEpC,OAAIjM,KAAKi3B,KAAK8jB,+BAAiC+B,EAAM7zC,OAC5Cu9C,GAAuCvuB,KAAKj4B,KAAM,CACvD4gD,UAAAA,EACAsF,MAAAA,UAIclmD,KAAKyzC,MACrB,MACA,eACA,CACEyS,MAAAA,GAEF,CACErF,mBAAoBD,GAEtB,CACE3D,MAAOH,GAET,CACE1lB,eAAc,KACL,OAmBFqvB,GAAsCvlD,eAEjD+K,GAEA,MAAM20C,UAAEA,EAAFsF,MAAaA,GAAUj6C,EAC7B,IACE,MAAMkrB,QAAYn3B,KAAKyzC,MACrB,MACA,eACA,CACEyS,MAAAA,GAEF,CACErF,mBAAoBD,GAEtB,GACA,IAIF,OAFkBn/C,EAAc01B,EAAI73B,KACpC4N,CAAU,SACHiqB,EACP,MAAOnH,GACP,OAAO0qB,GACL1qB,EACA,CACE2qB,4BAA6B36C,KAAKi3B,KAAK8jB,6BACvCH,UAAW,sCACXC,gBAAiB,CACfoC,MAAO,QAgBJuJ,GAAyCtlD,eAEpD+K,GAEA,MAAM20C,UAAEA,EAAFsF,MAAaA,GAAUj6C,EAiB7B,aAhBkBjM,KAAKyzC,MACrB,SACA,eACA,CACEyS,MAAAA,GAEF,CACErF,mBAAoBD,GAEtB,GACA,CACExpB,eAAc,KACL,OC1HRl2B,eAAewlD,GAEpBz6C,GAEA,MAAM2sB,OAAEA,EAAF+tB,OAAUA,GAAW16C,EAC3B,aAAajM,KAAK6zC,YAChBjb,EACA,MACA,CACEguB,YAAa,IAEf,GACA,CACEC,OAAQF,IAgBPzlD,eAAe4lD,GAEpB76C,GAEA,MAAM2sB,OAAEA,GAAW3sB,EACnB,IACE,aAAajM,KAAK6zC,YAChBjb,EACA,MACA,CACEguB,YAAa,IAEf,IAEF,MAAO52B,GACP,OAAO0qB,GAAuB1qB,EAAO,CACnC2qB,4BAA6B36C,KAAKi3B,KAAK8jB,6BACvCH,UAAW,uBACXC,gBAAiB,CACfgM,QAAQ,MC7CT3lD,eAAe6lD,GAEpB96C,GAEA,MAAM2sB,OAAEA,KAAW+lB,GAAe1yC,EAE5B8lB,EAAOjvB,EAA6B67C,GAC1C,OAAO3+C,KAAK6zC,YACVjb,EACA,MACA,CAAEouB,MAAkB,IACpB,GACA,IACKj1B,IAiBF7wB,eAAe+lD,GAEpBh7C,GAEA,MAAM2sB,OAAEA,GAAW3sB,EACnB,aAAajM,KAAK6zC,YAChBjb,EACA,MACA,CAAEouB,MAAkB,IACpB,UCuISE,WAAoBxa,sCAE/B8I,aAAeA,QACfE,WAAaA,QACbD,aAAeA,QACfF,YAAcA,QACd+G,kBAAoBA,QACpBzG,sBAAwBA,QAGxBI,aAAeA,QACfF,aAAeA,QAGfsE,gBAAkBA,QAClBS,gBAAkBA,QAClBG,mBAAqBA,QAGrBI,oBAAsBA,QACtBE,oBAAsBA,QAGtBiB,cAAgBA,QAChBG,cAAgBA,QAChBC,iBAAmBA,QAGnBC,mBAAqBA,QACrBK,mBAAqBA,QACrBH,sBAAwBA,QAGxBK,oBAAsBA,QACtBK,oBAAsBA,QACtBC,uBAAyBA,QAGzBC,oBAAsBA,QACtBxD,oBAAsBA,QACtByD,uBAAyBA,QAGzBQ,qBAAuBA,QACvB7D,qBAAuBA,QACvB+D,wBAA0BA,QAG1BI,iBAAmBA,QACnBjE,iBAAmBA,QACnBqE,oBAAsBA,QAGtBC,sBAAwBA,QACxB3E,sBAAwBA,QAGxB+E,sBAAwBA,QACxBpF,sBAAwBA,QACxBuF,yBAA2BA,QAG3BE,qBAAuBA,QACvBlF,qBAAuBA,QACvBoF,wBAA0BA,QAG1BzF,mBAAqBA,QACrBC,oBAAsBA,QACtBsG,mBAAqBA,QACrBD,sBAAwBA,QAGxBmB,iBAAmBA,QACnBlH,iBAAmBA,QACnBoH,oBAAsBA,QAGtBC,sBAAwBA,QACxBE,sBAAwBA,QAGxBW,oBAAsBA,QACtBN,wBAA0BA,QAC1BG,8BAAgCA,QAChCP,uBAAyBA,QACzBc,uBAAyBA,QACzBH,oBAAsBA,QACtBO,6BAA+BA,QAC/BH,sBAAwBA,QACxBE,sBAAwBA,QACxBK,6BAA+BA,QAG/BE,gBAAkBA,QAClBC,gBAAkBA,QAClBC,mBAAqBA,QAGrBmB,8BAAgCA,QAChCE,8BAAgCA,QAGhCzjB,WAAaA,QACbC,oBAAsBA,QACtB+V,aAAeA,QACfI,mBAAqBA,QACrB1U,UAAYA,QACZG,YAAcA,QACde,gBAAkBA,QAClB+T,aAAeA,QACfxX,WAAaA,QACb4X,aAAeA,QACfzE,YAAcA,QACd4D,aAAeA,QACflB,YAAcA,QACdK,aAAeA,QAEf3C,iBAAmBA,QAEnBF,mBAAqBA,QACrBoC,UAAYA,QACZE,kBAAoBA,QACpB8B,aAAeA,QACfO,cAAgBA,QAGhBzsC,sBAAwBA,QACxBgsB,WAAaA,QACbE,mBAAqBA,QACrBM,wBAA0BA,QAC1B2f,qBAAuBA,QACvBrX,eAAiBA,QACjBsX,qBAAuBA,QACvBtrC,UAAYA,QACZw3B,aAAeA,QAGfwZ,iBAAmBA,QACnBI,iBAAmBA,QACnBC,oBAAsBA,QAGtB2C,SAAWA,QACXH,UAAYA,QACZa,UAAYA,QACZC,YAAcA,QACdJ,gBAAkBA,QAClBJ,kBAAoBA,QAGpB0C,cAAgBA,QAEhBjqB,WAAaA,QACbmd,gBAAkBA,QAIlB8B,uBAAyBA,QACzBwO,uBAAyBxO,QACzB8C,mBAAqBA,QAErBzB,4BAA8BA,QAG9B6J,gBAAkBA,QAClBC,kBAAoBA,QACpBC,eAAiBA,QACjBC,eAAiBA,QAGjBE,2BAA6BA,QAC7BE,2BAA6BA,QAG7BC,WAAaA,QACbG,WAAaA,QAGbY,uBAAyBA,QACzBE,uBAAyBA,QAGzBE,aAAeA,QACfD,aAAeA,QACfG,gBAAkBA,QAGlBC,6BAA+BA,QAC/BI,0BAA4BA,QAC5BC,4BAA8BA,QAC9BC,gCAAkCA,QAClCE,6BAA+BA,QAC/BC,mCAAqCA,QAGrCG,oCAAsCA,QACtCE,oCAAsCA,QACtCD,uCACEA,QAGFE,qBAAuBA,QACvBI,qBAAuBA,QAEvBC,eAAiBA,QACjBE,eAAiBA,UC3WbG,GAActZ,EAAMsZ,YAE1B,MAAMC,WAAkBH,IAAlBG,GAEGnoD,eAAiBA,EAFpBmoD,GAGGztB,SAAWA,EAHdytB,GAIG/mD,YAAcA,EAJjB+mD,GAKGpoD,cAAgBA,sBALnBooD,GAMGjnD,eAAiBA,EANpBinD,GAOGD,YAAcA,GAPjBC,GAQG/nB,QAAUA,gBARb+nB,GASG9nB,iBAAmBA,yBATtB8nB,GAUG7nB,sBAAwBA,8BAV3B6nB,GAWG5nB,iBAAmBA,yBAXtB4nB,GAYG3nB,eAAiBA,uBAZpB2nB,GAaG1nB,YAAcA,oBAbjB0nB,GAcGznB,WAAaA,mBAdhBynB,GAeGxnB,eAAiBA,uBAfpBwnB,GAgBGrnB,oBAAsBA,4BAhBzBqnB,GAiBGnnB,WAAaA,mBAjBhBmnB,GAkBGpnB,aAAeA,qBAlBlBonB,GAmBGvnB,iCAAmCA,yCAnBtCunB,GAoBGlnB,SAAWA,iBApBdknB,GAqBGjnB,qBAAuBA,6BArB1BinB,GAsBGp2B,yBAA2BA,GAtB9Bo2B,GAuBG9yB,iBAAmBA,yBAvBtB8yB,GAwBG7tB,gBAAkBA,wBAxBrB6tB,GAyBGhjB,kBAAoBA,0BAzBvBgjB,GA0BG7mB,uBAAyBA,+BA1B5B6mB,GA2BGtnB,sBAAwBA,8BA3B3BsnB,GA6BGhnB,sBAAwBA,8BA7B3BgnB,GA+BG/mB,+BAAiCA,uCA/BpC+mB,GAiCG9mB,iCAAmCA,yCAjCtC8mB,GAmCGzS,gBAAkBA"}