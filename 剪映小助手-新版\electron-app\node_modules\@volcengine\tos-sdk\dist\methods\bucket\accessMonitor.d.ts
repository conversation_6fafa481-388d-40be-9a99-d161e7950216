import TOSBase from '../base';
import { GetBucketLifecycleInput } from './lifecycle';
export interface PutBucketAccessMonitorInput {
    bucket: string;
    status: 'Enabled' | 'Disabled';
}
export interface PutBucketAccessMonitorOutput {
}
/**
 * @private unstable method
 */
export declare function putBucketAccessMonitor(this: TOSBase, input: PutBucketAccessMonitorInput): Promise<import("../base").TosResponse<PutBucketAccessMonitorOutput>>;
export interface GetBucketAccessMonitorInput {
    bucket: string;
}
export interface GetBucketAccessMonitorOutput {
    Status?: 'Enabled' | 'Disabled';
}
/**
 * @private unstable method
 */
export declare function getBucketAccessMonitor(this: TOSBase, input: GetBucketLifecycleInput): Promise<import("../base").TosResponse<GetBucketAccessMonitorOutput>>;
