import TOSBase, { TosResponse } from '../base';
export interface BucketPolicyStatement {
    Sid: string;
    Effect: 'Allow' | 'Deny';
    Action?: string | string[];
    NotAction?: string | string[];
    Condition?: {
        [key in string]: {
            [key in string]: string[];
        };
    };
    Principal?: string[];
    NotPrincipal?: string[];
    Resource?: string | string[];
    NotResource?: string | string[];
}
export interface GetBucketPolicyOutput {
    Statement: BucketPolicyStatement[];
    Version: string;
}
interface PutBucketPolicyInputPolicy extends Omit<GetBucketPolicyOutput, 'Version'> {
    Version?: string;
}
export interface PutBucketPolicyInput {
    bucket?: string;
    policy: PutBucketPolicyInputPolicy;
}
export declare function putBucketPolicy(this: TOSBase, input: PutBucketPolicyInput): Promise<TosResponse<unknown>>;
export declare function getBucketPolicy(this: TOSBase, bucket?: string): Promise<TosResponse<GetBucketPolicyOutput>>;
export declare function deleteBucketPolicy(this: TOSBase, bucket?: string): Promise<TosResponse<unknown>>;
export {};
