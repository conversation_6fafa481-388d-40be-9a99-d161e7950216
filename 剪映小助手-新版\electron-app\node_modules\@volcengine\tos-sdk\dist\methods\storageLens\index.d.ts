import TOSBase from '../base';
export interface IMetaData {
    accountId: string;
}
export interface StorageLensInput extends IMetaData {
    Id: string;
}
interface IStrategy {
    Buckets: {
        Bucket: string[];
    };
    Regions: {
        Region: string[];
    };
}
interface IPrefixLevel {
    StorageMetrics: {
        IsEnabled: boolean;
        SelectionCriteria: {
            MaxDepth?: number;
            MinStorageBytesPercentage?: number;
            Delimiter: string;
            Prefixes?: string[];
        };
    };
}
export interface StorageLensConfigurationInput extends StorageLensInput {
    Region: string;
    IsEnabled: boolean;
    AccountLevel: {
        BucketLevel: {
            /** @deprecated will be removed soon */
            ActivityMetrics?: {
                IsEnabled: boolean;
            };
            HotStatsMetrics?: {
                IsEnabled: boolean;
                Actions: string[];
            };
            PrefixLevel?: IPrefixLevel;
        };
    };
    DataExport?: {
        BucketDestination?: {
            Bucket: string;
            Prefix?: string;
            OutputSchemaVersion: string;
            Format: string;
            Role: string;
        };
    };
    Include?: IStrategy;
    Exclude?: IStrategy;
}
export declare type StorageLensConfigurationOutput = StorageLensConfigurationInput;
/**
 * @private unstable method
 * @description 获取数据透视列表
 * @param params
 * @returns
 */
export declare function listStorageLens(this: TOSBase, params: IMetaData): Promise<import("../base").TosResponse<StorageLensConfigurationInput[]>>;
/**
 * @private unstable method
 * @description 删除数据透视记录
 * @param params
 * @returns
 */
export declare function deleteStorageLens(this: TOSBase, params: StorageLensInput): Promise<import("../base").TosResponse<unknown>>;
/**
 * @private unstable method
 * @description 获取数据透视详情
 * @param params
 * @returns
 */
export declare function getStorageLens(this: TOSBase, params: StorageLensInput): Promise<import("../base").TosResponse<StorageLensConfigurationInput>>;
/**
 * @private unstable method
 * @description 提交数据透视记录
 * @param params
 * @returns
 */
export declare function putStorageLens(this: TOSBase, params: StorageLensConfigurationInput): Promise<import("../base").TosResponse<unknown>>;
export {};
