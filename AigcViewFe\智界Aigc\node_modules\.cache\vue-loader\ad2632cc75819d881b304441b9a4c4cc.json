{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\agent\\AgentDetailPage.vue", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\agent\\AgentDetailPage.vue", "mtime": 1754511159792}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["import { render, staticRenderFns } from \"./AgentDetailPage.vue?vue&type=template&id=2f9872c6&scoped=true&\"\nimport script from \"./AgentDetailPage.vue?vue&type=script&lang=js&\"\nexport * from \"./AgentDetailPage.vue?vue&type=script&lang=js&\"\nimport style0 from \"./AgentDetailPage.vue?vue&type=style&index=0&id=2f9872c6&lang=less&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"2f9872c6\",\n  null\n  \n)\n\n/* hot reload */\nif (module.hot) {\n  var api = require(\"D:\\\\AigcView_zj\\\\AigcViewFe\\\\智界Aigc\\\\node_modules\\\\vue-hot-reload-api\\\\dist\\\\index.js\")\n  api.install(require('vue'))\n  if (api.compatible) {\n    module.hot.accept()\n    if (!api.isRecorded('2f9872c6')) {\n      api.createRecord('2f9872c6', component.options)\n    } else {\n      api.reload('2f9872c6', component.options)\n    }\n    module.hot.accept(\"./AgentDetailPage.vue?vue&type=template&id=2f9872c6&scoped=true&\", function () {\n      api.rerender('2f9872c6', {\n        render: render,\n        staticRenderFns: staticRenderFns\n      })\n    })\n  }\n}\ncomponent.options.__file = \"src/views/website/agent/AgentDetailPage.vue\"\nexport default component.exports"]}