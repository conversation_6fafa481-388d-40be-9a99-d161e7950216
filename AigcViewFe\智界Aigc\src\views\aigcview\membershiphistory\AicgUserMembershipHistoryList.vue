<template>
  <a-card :bordered="false">
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline" @keyup.enter.native="searchQuery">
        <a-row :gutter="24">
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="用户ID">
              <a-input placeholder="请输入用户ID" v-model="queryParam.userId"></a-input>
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="会员等级">
              <a-select placeholder="请选择会员等级" v-model="queryParam.memberLevel" allowClear>
                <a-select-option :value="1">普通用户</a-select-option>
                <a-select-option :value="2">VIP会员</a-select-option>
                <a-select-option :value="3">SVIP会员</a-select-option>
                <a-select-option :value="4">至尊会员</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="状态">
              <a-select placeholder="请选择状态" v-model="queryParam.status" allowClear>
                <a-select-option :value="1">生效中</a-select-option>
                <a-select-option :value="2">已过期</a-select-option>
                <a-select-option :value="3">已取消</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <span class="table-page-search-submitButtons">
              <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
              <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    
    <!-- 操作按钮区域 -->
    <div class="table-operator">
      <a-button @click="handleAdd" type="primary" icon="plus">新增</a-button>
      <a-button type="primary" icon="download" @click="handleExportXls('会员订阅历史')">导出</a-button>
      <a-upload name="file" :showUploadList="false" :multiple="false" :headers="tokenHeader" :action="importExcelUrl" @change="handleImportExcel">
        <a-button type="primary" icon="import">导入</a-button>
      </a-upload>
      <a-dropdown v-if="selectedRowKeys.length > 0">
        <a-menu slot="overlay">
          <a-menu-item key="1" @click="batchDel"><a-icon type="delete"/>删除</a-menu-item>
        </a-menu>
        <a-button style="margin-left: 8px"> 批量操作 <a-icon type="down" /></a-button>
      </a-dropdown>
    </div>

    <!-- table区域-begin -->
    <div>
      <div class="ant-alert ant-alert-info" style="margin-bottom: 16px;">
        <i class="anticon anticon-info-circle ant-alert-icon"></i> 已选择 <a style="font-weight: 600">{{ selectedRowKeys.length }}</a>项
        <a style="margin-left: 24px" @click="onClearSelected">清空</a>
      </div>

      <a-table
        ref="table"
        size="middle"
        :scroll="{x:true}"
        bordered
        rowKey="id"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="ipagination"
        :loading="loading"
        :rowSelection="{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}"
        class="j-table-force-nowrap"
        @change="handleTableChange">

        <template slot="status" slot-scope="text">
          <a-tag :color="text === 1 ? 'green' : text === 2 ? 'red' : 'orange'">
            {{ text === 1 ? '生效中' : text === 2 ? '已过期' : text === 3 ? '已取消' : text }}
          </a-tag>
        </template>

        <template slot="imgSlot" slot-scope="text">
          <span v-if="!text" style="font-size: 12px;font-style: italic;">无图片</span>
          <img v-else :src="getImgView(text)" height="25px" alt="" style="max-width:80px;font-size: 12px;font-style: italic;"/>
        </template>
        <template slot="fileSlot" slot-scope="text">
          <span v-if="!text" style="font-size: 12px;font-style: italic;">无文件</span>
          <a-button
            v-else
            :ghost="true"
            type="primary"
            icon="download"
            size="small"
            @click="downloadFile(text)">
            下载
          </a-button>
        </template>

        <span slot="action" slot-scope="text, record">
          <a @click="handleEdit(record)">编辑</a>
          <a-divider type="vertical" />
          <a-dropdown>
            <a class="ant-dropdown-link">更多 <a-icon type="down" /></a>
            <a-menu slot="overlay">
              <a-menu-item>
                <a @click="handleDetail(record)">详情</a>
              </a-menu-item>
              <a-menu-item v-if="record.status === 1">
                <a @click="handleCancel(record)">取消订阅</a>
              </a-menu-item>
              <a-menu-item>
                <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
                  <a>删除</a>
                </a-popconfirm>
              </a-menu-item>
            </a-menu>
          </a-dropdown>
        </span>

      </a-table>
    </div>

    <aicg-user-membership-history-modal ref="modalForm" @ok="modalFormOk"></aicg-user-membership-history-modal>
  </a-card>
</template>

<script>

  import '@/assets/less/TableExpand.less'
  import { mixinDevice } from '@/utils/mixin'
  import { JeecgListMixin } from '@/mixins/JeecgListMixin'
  import AicgUserMembershipHistoryModal from './modules/AicgUserMembershipHistoryModal.vue'

  export default {
    name: 'AicgUserMembershipHistoryList',
    mixins:[JeecgListMixin, mixinDevice],
    components: {
      AicgUserMembershipHistoryModal
    },
    data () {
      return {
        description: '用户会员订阅历史管理页面',
        // 表头
        columns: [
          {
            title: '#',
            dataIndex: '',
            key:'rowIndex',
            width:60,
            align:"center",
            customRender:function (t,r,index) {
              return parseInt(index)+1;
            }
          },
          {
            title:'用户ID',
            align:"center",
            dataIndex: 'userId'
          },
          {
            title:'订单ID',
            align:"center",
            dataIndex: 'orderId'
          },
          {
            title:'会员等级',
            align:"center",
            dataIndex: 'memberLevel',
            customRender: function (text) {
              const levelMap = {1: '普通用户', 2: 'VIP会员', 3: 'SVIP会员', 4: '至尊会员'}
              return levelMap[text] || text
            }
          },
          {
            title:'订阅时长(月)',
            align:"center",
            dataIndex: 'durationMonths'
          },
          {
            title:'订阅金额',
            align:"center",
            dataIndex: 'amount',
            customRender: function (text) {
              return text ? `¥${text}` : '-'
            }
          },
          {
            title:'开始时间',
            align:"center",
            dataIndex: 'startTime'
          },
          {
            title:'结束时间',
            align:"center",
            dataIndex: 'endTime'
          },
          {
            title:'状态',
            align:"center",
            dataIndex: 'status',
            scopedSlots: { customRender: 'status' }
          },
          {
            title: '操作',
            dataIndex: 'action',
            align:"center",
            fixed:"right",
            width:147,
            scopedSlots: { customRender: 'action' }
          }
        ],
        url: {
          list: "/demo/membershiphistory/list",
          delete: "/demo/membershiphistory/delete",
          deleteBatch: "/demo/membershiphistory/deleteBatch",
          exportXlsUrl: "/demo/membershiphistory/exportXls",
          importExcelUrl: "demo/membershiphistory/importExcel",
        },
        dictOptions:{},
        superFieldList:[],
      }
    },
    created() {
      this.getSuperFieldList();
    },
    computed: {
      importExcelUrl: function(){
        return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`;
      },
    },
    methods: {
      initDictConfig(){
      },
      getSuperFieldList(){
        let fieldList=[];
        fieldList.push({type:'string',value:'userId',text:'用户ID'})
        fieldList.push({type:'string',value:'orderId',text:'订单ID'})
        fieldList.push({type:'int',value:'memberLevel',text:'会员等级'})
        fieldList.push({type:'int',value:'durationMonths',text:'订阅时长(月)'})
        fieldList.push({type:'BigDecimal',value:'amount',text:'订阅金额'})
        fieldList.push({type:'Date',value:'startTime',text:'开始时间'})
        fieldList.push({type:'Date',value:'endTime',text:'结束时间'})
        fieldList.push({type:'int',value:'status',text:'状态'})
        this.superFieldList = fieldList
      },
      handleCancel(record) {
        this.$confirm({
          title: '确认取消',
          content: '确定要取消该会员订阅吗？',
          onOk: () => {
            this.$http.post(`/demo/membershiphistory/cancel?id=${record.id}`).then((res) => {
              if (res.success) {
                this.$message.success('取消成功')
                this.loadData()
              } else {
                this.$message.error(res.message)
              }
            })
          }
        })
      }
    }
  }
</script>
<style scoped>
  @import '~@assets/less/common.less';
</style>
