import TOSBase from '../base';
export interface ListObjectsInput {
    bucket?: string;
    continuationToken?: string;
    delimiter?: string;
    encodingType?: string;
    fetchOwner?: string;
    maxKeys?: string | number;
    prefix?: string;
    marker?: string;
    /**
     * use `marker` instead of `startAfter`
     */
    startAfter?: string;
    /**
     * equal to listObjectVersions when input
     */
    versions?: string;
    listType?: string;
    versionIdMarker?: string;
    /**
     * only works when pass versions field
     */
    keyMarker?: string;
}
export interface ListObjectsContentItem {
    ETag: string;
    Key: string;
    LastModified: string;
    Owner: {
        ID: string;
        DisplayName: string;
    };
    Size: number;
    StorageClass: string;
}
export interface ListObjectsVersionItem {
    ETag: string;
    IsLatest: boolean;
    Key: string;
    LastModified: string;
    Owner: {
        ID: string;
        DisplayName: string;
    };
    Size: number;
    StorageClass: string;
    VersionId: string;
}
export interface ListObjectDeleteMarkerItem {
    ETag: string;
    IsLatest: boolean;
    Key: string;
    LastModified: string;
    Owner: {
        ID: string;
        DisplayName: string;
    };
    Size: number;
    StorageClass: string;
    VersionId: string;
}
export interface ListedCommonPrefix {
    Prefix: string;
}
export interface ListObjectsOutput {
    CommonPrefixes: ListedCommonPrefix[];
    Contents: ListObjectsContentItem[];
    IsTruncated: boolean;
    Marker: string;
    MaxKeys: number;
    KeyMarker?: string;
    Name: string;
    Prefix: string;
    ContinuationToken?: string;
    NextContinuationToken?: string;
    Delimiter?: string;
    EncodingType?: string;
    NextMarker?: string;
    VersionIdMarker?: string;
    Versions: ListObjectsVersionItem[];
    NextKeyMarker?: string;
    DeleteMarkers: ListObjectDeleteMarkerItem[];
    NextVersionIdMarker?: string;
}
declare class TOSListObjects extends TOSBase {
    listObjects: typeof listObjects;
    listObjectVersions: typeof listObjectVersions;
}
/**
 *
 * @deprecated use listObjectsType2 instead
 * @returns
 */
export declare function listObjects(this: TOSListObjects, input?: ListObjectsInput): Promise<import("../base").TosResponse<ListObjectsOutput>>;
export declare type ListObjectVersionsInput = Pick<ListObjectsInput, 'bucket' | 'prefix' | 'delimiter' | 'keyMarker' | 'versionIdMarker' | 'maxKeys' | 'encodingType'>;
export interface listObjectVersionsOutput {
    Name: string;
    Prefix: string;
    KeyMarker?: string;
    VersionIdMarker?: string;
    MaxKeys: number;
    Delimiter?: string;
    IsTruncated: boolean;
    EncodingType?: string;
    NextKeyMarker?: string;
    NextVersionIdMarker?: string;
    CommonPrefixes: ListedCommonPrefix[];
    Versions: ListObjectsVersionItem[];
    DeleteMarkers: ListObjectDeleteMarkerItem[];
}
export declare function listObjectVersions(this: TOSListObjects, input?: ListObjectVersionsInput): Promise<import("../base").TosResponse<listObjectVersionsOutput>>;
export {};
