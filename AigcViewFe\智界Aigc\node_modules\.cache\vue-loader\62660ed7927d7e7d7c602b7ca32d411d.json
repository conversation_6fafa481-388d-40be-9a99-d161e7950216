{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\workflow\\components\\CreatorCenter.vue?vue&type=template&id=bfaacd0c&scoped=true&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\workflow\\components\\CreatorCenter.vue", "mtime": 1754512989758}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["var render = function() {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  return _c(\n    \"div\",\n    { staticClass: \"creator-center\" },\n    [\n      _c(\"div\", { staticClass: \"creator-header\" }, [\n        _c(\"div\", { staticClass: \"header-content\" }, [\n          _c(\"div\", { staticClass: \"header-left\" }, [\n            _c(\n              \"h1\",\n              { staticClass: \"page-title\" },\n              [\n                _c(\"a-icon\", { attrs: { type: \"user\" } }),\n                _vm._v(\"\\n          创作者中心\\n        \")\n              ],\n              1\n            ),\n            _c(\"p\", { staticClass: \"page-subtitle\" }, [\n              _vm._v(\n                \"管理您的智能体，查看收益统计（全部收益归创作者，但VIP和SVIP用户分别是价格的7折和5折，如您选择发布默认同意此方案）\"\n              )\n            ])\n          ]),\n          _c(\n            \"div\",\n            { staticClass: \"header-right\" },\n            [\n              _c(\n                \"a-button\",\n                {\n                  attrs: {\n                    type: \"primary\",\n                    size: \"large\",\n                    loading: _vm.creating\n                  },\n                  on: { click: _vm.handleCreateAgent }\n                },\n                [\n                  _c(\"a-icon\", { attrs: { type: \"plus\" } }),\n                  _vm._v(\"\\n          新增智能体\\n        \")\n                ],\n                1\n              )\n            ],\n            1\n          )\n        ])\n      ]),\n      _c(\n        \"div\",\n        { staticClass: \"stats-cards-section\" },\n        [\n          _c(\"div\", { staticClass: \"section-header\" }, [\n            _c(\"h2\", { staticClass: \"section-title\" }, [_vm._v(\"收益统计\")]),\n            _c(\n              \"div\",\n              { staticClass: \"section-actions\" },\n              [\n                _c(\n                  \"a-button\",\n                  {\n                    staticClass: \"records-btn\",\n                    attrs: { type: \"default\" },\n                    on: { click: _vm.openWithdrawRecordsModal }\n                  },\n                  [\n                    _c(\"a-icon\", { attrs: { type: \"history\" } }),\n                    _vm._v(\"\\n          查看提现记录\\n        \")\n                  ],\n                  1\n                )\n              ],\n              1\n            )\n          ]),\n          _c(\"RevenueStatsCards\", {\n            attrs: { loading: _vm.statsLoading, data: _vm.revenueStats },\n            on: { refresh: _vm.loadRevenueStats, withdraw: _vm.handleWithdraw }\n          })\n        ],\n        1\n      ),\n      _c(\n        \"div\",\n        { staticClass: \"agents-section\" },\n        [\n          _vm._m(0),\n          _c(\"AgentManagement\", {\n            attrs: {\n              loading: _vm.agentsLoading,\n              agents: _vm.agentList,\n              pagination: _vm.pagination\n            },\n            on: {\n              refresh: _vm.loadAgentList,\n              edit: _vm.handleEditAgent,\n              delete: _vm.handleDeleteAgent,\n              \"page-change\": _vm.handlePageChange,\n              search: _vm.handleAgentSearch,\n              \"filter-change\": _vm.handleAgentFilter,\n              \"sort-change\": _vm.handleSortChange\n            }\n          })\n        ],\n        1\n      ),\n      _c(\n        \"div\",\n        { staticClass: \"ranking-section\" },\n        [\n          _c(\"RevenueRanking\", {\n            attrs: { loading: _vm.statsLoading, data: _vm.revenueStats }\n          })\n        ],\n        1\n      ),\n      _c(\"CreatorAgentForm\", {\n        ref: \"agentForm\",\n        attrs: {\n          visible: _vm.formVisible,\n          loading: _vm.formLoading,\n          agent: _vm.currentAgent,\n          mode: _vm.formMode\n        },\n        on: {\n          close: _vm.handleCloseForm,\n          submit: _vm.handleSubmitForm,\n          complete: _vm.handleAgentComplete,\n          \"delete-workflow\": _vm.handleDeleteWorkflowFromForm\n        }\n      }),\n      _c(\"WithdrawModal\", {\n        attrs: {\n          visible: _vm.showWithdrawModal,\n          \"available-amount\": _vm.withdrawAvailableAmount,\n          \"revenue-type\": _vm.withdrawRevenueType,\n          loading: _vm.withdrawLoading\n        },\n        on: {\n          submit: _vm.handleWithdrawSubmit,\n          cancel: _vm.handleWithdrawCancel\n        }\n      }),\n      _c(\"WithdrawRecordsModal\", {\n        attrs: {\n          visible: _vm.showWithdrawRecordsModal,\n          \"revenue-type\": \"agent\"\n        },\n        on: {\n          cancel: _vm.handleWithdrawRecordsCancel,\n          refresh: _vm.loadRevenueStats\n        }\n      })\n    ],\n    1\n  )\n}\nvar staticRenderFns = [\n  function() {\n    var _vm = this\n    var _h = _vm.$createElement\n    var _c = _vm._self._c || _h\n    return _c(\"div\", { staticClass: \"section-header\" }, [\n      _c(\"h2\", { staticClass: \"section-title\" }, [_vm._v(\"我的智能体\")])\n    ])\n  }\n]\nrender._withStripped = true\n\nexport { render, staticRenderFns }"]}