package org.jeecg.modules.demo.websitefeatures.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.demo.websitefeatures.entity.AigcWebsiteFeatures;
import org.jeecg.modules.demo.websitefeatures.service.IAigcWebsiteFeaturesService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;

/**
 * @Description: 网站功能特性
 * @Author: jeecg-boot
 * @Date:   2025-06-17
 * @Version: V1.0
 */
@Api(tags="网站功能特性")
@RestController
@RequestMapping("/aigc/websiteFeatures")
@Slf4j
public class AigcWebsiteFeaturesController extends JeecgController<AigcWebsiteFeatures, IAigcWebsiteFeaturesService> {
	@Autowired
	private IAigcWebsiteFeaturesService aigcWebsiteFeaturesService;
	
	/**
	 * 分页列表查询
	 *
	 * @param aigcWebsiteFeatures
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	@AutoLog(value = "网站功能特性-分页列表查询")
	@ApiOperation(value="网站功能特性-分页列表查询", notes="网站功能特性-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(AigcWebsiteFeatures aigcWebsiteFeatures,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<AigcWebsiteFeatures> queryWrapper = QueryGenerator.initQueryWrapper(aigcWebsiteFeatures, req.getParameterMap());
		Page<AigcWebsiteFeatures> page = new Page<AigcWebsiteFeatures>(pageNo, pageSize);
		IPage<AigcWebsiteFeatures> pageList = aigcWebsiteFeaturesService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param aigcWebsiteFeatures
	 * @return
	 */
	@AutoLog(value = "网站功能特性-添加")
	@ApiOperation(value="网站功能特性-添加", notes="网站功能特性-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody AigcWebsiteFeatures aigcWebsiteFeatures) {
		aigcWebsiteFeaturesService.save(aigcWebsiteFeatures);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param aigcWebsiteFeatures
	 * @return
	 */
	@AutoLog(value = "网站功能特性-编辑")
	@ApiOperation(value="网站功能特性-编辑", notes="网站功能特性-编辑")
	@PutMapping(value = "/edit")
	public Result<?> edit(@RequestBody AigcWebsiteFeatures aigcWebsiteFeatures) {
		aigcWebsiteFeaturesService.updateById(aigcWebsiteFeatures);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "网站功能特性-通过id删除")
	@ApiOperation(value="网站功能特性-通过id删除", notes="网站功能特性-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		aigcWebsiteFeaturesService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "网站功能特性-批量删除")
	@ApiOperation(value="网站功能特性-批量删除", notes="网站功能特性-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.aigcWebsiteFeaturesService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "网站功能特性-通过id查询")
	@ApiOperation(value="网站功能特性-通过id查询", notes="网站功能特性-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		AigcWebsiteFeatures aigcWebsiteFeatures = aigcWebsiteFeaturesService.getById(id);
		if(aigcWebsiteFeatures==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(aigcWebsiteFeatures);
	}
}
