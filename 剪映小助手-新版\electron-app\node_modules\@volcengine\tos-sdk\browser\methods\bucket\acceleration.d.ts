import { TransferAccelerationStatusType } from '../../TosExportEnum';
import TOSBase from '../base';
export interface PutBucketTransferAccelerationInput {
    bucket?: string;
    transferAccelerationConfiguration: {
        Enabled: 'true' | 'false';
    };
}
export interface PutBucketTransferAccelerationOutput {
}
/**
 * @private unstable
 */
export declare function putBucketTransferAcceleration(this: TOSBase, input: PutBucketTransferAccelerationInput): Promise<import("../base").TosResponse<PutBucketTransferAccelerationOutput>>;
export interface GetBucketTransferAccelerationInput {
    bucket?: string;
    getStatus?: boolean;
}
export interface GetBucketTransferAccelerationOutput {
    TransferAccelerationConfiguration: {
        Enabled: 'true' | 'false';
        Status: TransferAccelerationStatusType;
    };
}
/**
 * @private unstable
 */
export declare function getBucketTransferAcceleration(this: TOSBase, input: GetBucketTransferAccelerationInput): Promise<import("../base").TosResponse<GetBucketTransferAccelerationOutput>>;
