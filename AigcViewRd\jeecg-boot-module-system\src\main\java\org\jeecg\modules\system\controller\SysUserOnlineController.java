package org.jeecg.modules.system.controller;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;

import javax.annotation.Resource;
import org.apache.commons.lang.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.constant.CacheConstant;
import org.jeecg.common.constant.CommonConstant;
import org.jeecg.common.system.api.ISysBaseAPI;
import org.jeecg.common.system.util.JwtUtil;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.RedisUtil;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.base.service.BaseCommonService;
import org.jeecg.modules.system.service.ISysUserService;
import org.jeecg.modules.system.service.UserCacheCleanupService;
import org.jeecg.modules.system.vo.SysUserOnlineVO;
import org.jeecg.modules.api.service.IUserActivityBatchUpdateService;
import org.jeecg.modules.api.service.IUserActivityCacheService;
import org.jeecg.modules.api.config.UserActivityConfig;
import org.jeecg.common.util.IPUtils;
import javax.servlet.http.HttpServletRequest;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.*;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import lombok.extern.slf4j.Slf4j;

/**
 * @Description: 在线用户
 * @Author: chenli
 * @Date: 2020-06-07
 * @Version: V1.0
 */
@RestController
@RequestMapping("/sys/online")
@Slf4j
public class SysUserOnlineController {

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    public RedisTemplate redisTemplate;

    @Autowired
    public ISysUserService userService;

    @Autowired
    private ISysBaseAPI sysBaseAPI;

    @Autowired
    private UserCacheCleanupService userCacheCleanupService;

    @Autowired
    private IUserActivityBatchUpdateService userActivityBatchUpdateService;

    @Autowired
    private IUserActivityCacheService userActivityCacheService;

    @Autowired
    private UserActivityConfig userActivityConfig;

    @Resource
    private BaseCommonService baseCommonService;

    @RequestMapping(value = "/list", method = RequestMethod.GET)
    public Result<Page<SysUserOnlineVO>> list(@RequestParam(name="username", required=false) String username, @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
                                              @RequestParam(name="pageSize", defaultValue="10") Integer pageSize) {
        Collection<String> keys = redisTemplate.keys(CommonConstant.PREFIX_USER_TOKEN + "*");
        SysUserOnlineVO online;
        List<SysUserOnlineVO> onlineList = new ArrayList<SysUserOnlineVO>();
        for (String key : keys) {
            online = new SysUserOnlineVO();
            String token = (String) redisUtil.get(key);
            if (!StringUtils.isEmpty(token)){
                online.setToken(token);
                LoginUser loginUser = sysBaseAPI.getUserByName(JwtUtil.getUsername(token));
                BeanUtils.copyProperties(loginUser, online);
                if (StringUtils.isNotEmpty(username)) {
                    if (StringUtils.equals(username, online.getUsername())) {
                        onlineList.add(online);
                    }
                } else {
                    onlineList.add(online);
                }
            }
        }

        Page<SysUserOnlineVO> page = new Page<SysUserOnlineVO>(pageNo, pageSize);
        int count = onlineList.size();
        List<SysUserOnlineVO> pages = new ArrayList<>();
        //计算当前页第一条数据的下标
        int currId = pageNo>1 ? (pageNo-1)*pageSize:0;
        for (int i=0; i<pageSize && i<count - currId;i++){
            pages.add(onlineList.get(currId+i));
        }
        page.setSize(pageSize);
        page.setCurrent(pageNo);
        page.setTotal(count);
        //计算分页总页数
        page.setPages(count %10 == 0 ? count/10 :count/10+1);
        page.setRecords(pages);

        Collections.reverse(onlineList);
        onlineList.removeAll(Collections.singleton(null));
        Result<Page<SysUserOnlineVO>> result = new Result<Page<SysUserOnlineVO>>();
        result.setSuccess(true);
        result.setResult(page);
        return result;
    }

    /**
     * 强退用户（增强版）
     */
    @RequestMapping(value = "/forceLogout",method = RequestMethod.POST)
    public Result<Object> forceLogout(@RequestBody SysUserOnlineVO online, HttpServletRequest request) {
        //用户退出逻辑
        if(oConvertUtils.isEmpty(online.getToken())) {
            return Result.error("退出登录失败！");
        }
        String username = JwtUtil.getUsername(online.getToken());
        LoginUser sysUser = sysBaseAPI.getUserByName(username);
        if(sysUser!=null) {
            baseCommonService.addLog("强制: "+sysUser.getRealname()+"退出成功！", CommonConstant.LOG_TYPE_1, null,sysUser);
            log.info(" 强制  "+sysUser.getRealname()+"退出成功！ ");

            // 🆕 记录用户强制登出状态（集成新的用户活跃状态追踪系统）
            recordUserForceLogout(sysUser.getId(), online.getToken(), request, "ADMIN_FORCE_LOGOUT");

            // 🆕 使用统一的缓存清理服务，确保完整清理所有相关缓存
            userCacheCleanupService.cleanupUserLoginCache(sysUser.getId(), online.getToken(), sysUser.getUsername());

            // 调用shiro的logout
            SecurityUtils.getSubject().logout();
            return Result.ok("退出登录成功！");
        }else {
            return Result.error("Token无效!");
        }
    }

    /**
     * 记录用户强制登出状态
     * @param userId 用户ID
     * @param sessionId 会话ID
     * @param request HTTP请求对象
     * @param logoutReason 登出原因
     */
    private void recordUserForceLogout(String userId, String sessionId, HttpServletRequest request, String logoutReason) {
        try {
            log.info("开始记录用户强制登出状态 - 用户ID: {}, 会话ID: {}, 原因: {}", userId, sessionId, logoutReason);

            // 检查是否启用用户活跃状态追踪
            if (!userActivityConfig.getEnabled()) {
                log.debug("用户活跃状态追踪已禁用，跳过强制登出集成");
                return;
            }

            // 获取详细的登出信息
            String ipAddress = IPUtils.getIpAddr(request);
            String userAgent = request.getHeader("User-Agent");

            // 🆕 添加到批量更新队列
            boolean addedToQueue = userActivityBatchUpdateService.addLogoutUpdate(userId, sessionId);

            if (addedToQueue) {
                log.info("用户强制登出状态已添加到批量更新队列 - 用户ID: {}", userId);
            } else {
                log.warn("用户强制登出状态添加到批量更新队列失败 - 用户ID: {}", userId);
            }

            // 🆕 清理用户活跃状态缓存
            int cacheCleared = userActivityCacheService.clearUserCache(userId);
            if (cacheCleared > 0) {
                log.debug("用户活跃状态缓存已清理 - 用户ID: {}, 清理数量: {}", userId, cacheCleared);
            } else {
                log.warn("用户活跃状态缓存清理失败 - 用户ID: {}", userId);
            }

            // 🆕 清理会话信息缓存
            boolean sessionCleared = userActivityCacheService.removeSessionCache(sessionId);
            if (sessionCleared) {
                log.debug("会话信息缓存已清理 - 会话ID: {}", sessionId);
            } else {
                log.warn("会话信息缓存清理失败 - 会话ID: {}", sessionId);
            }

            // 🆕 记录强制登出详细信息到缓存（用于统计分析）
            java.util.Map<String, Object> logoutInfo = new java.util.HashMap<>();
            logoutInfo.put("logoutTime", new java.util.Date());
            logoutInfo.put("ipAddress", ipAddress);
            logoutInfo.put("userAgent", userAgent);
            logoutInfo.put("logoutReason", logoutReason);
            logoutInfo.put("logoutType", "FORCE_LOGOUT");

            // 使用统计信息缓存方法记录强制登出信息
            String logoutStatsKey = "force_logout_info_" + userId + "_" + System.currentTimeMillis();
            boolean logoutInfoCached = userActivityCacheService.cacheOnlineStats(logoutStatsKey, logoutInfo, 3600); // 缓存1小时
            if (logoutInfoCached) {
                log.debug("强制登出信息已缓存用于统计分析 - 用户ID: {}, 缓存键: {}", userId, logoutStatsKey);
            } else {
                log.warn("强制登出信息缓存失败 - 用户ID: {}", userId);
            }

        } catch (Exception e) {
            log.error("记录用户强制登出状态失败 - 用户ID: {}, 错误信息: {}", userId, e.getMessage(), e);
        }
    }
}
