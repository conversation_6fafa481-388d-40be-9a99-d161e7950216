package org.jeecg.modules.jianying.interceptor;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.jianying.service.ApiKeyVerificationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.PrintWriter;

/**
 * 剪映小助手API拦截器
 * 统一处理API Key验证
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
@Slf4j
@Component
public class JianyingApiInterceptor implements HandlerInterceptor {
    
    @Autowired
    private ApiKeyVerificationService apiKeyVerificationService;
    
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        try {
            // 获取API Token（剪映小助手使用zj_api_token参数）
            String apiToken = request.getParameter("zj_api_token");
            if (apiToken == null || apiToken.isEmpty()) {
                writeErrorResponse(response, "API Token不能为空");
                return false;
            }

            // 验证API Token（实际上就是API Key）
            JSONObject verifyResult = apiKeyVerificationService.verifyApiKey(apiToken);
            if (!verifyResult.getBoolean("success")) {
                writeErrorResponse(response, verifyResult.getString("error"));
                return false;
            }

            // 将用户信息存储到请求属性中，供后续使用
            JSONObject userData = verifyResult.getJSONObject("data");
            request.setAttribute("userId", userData.getString("userId"));
            request.setAttribute("userNickname", userData.getString("nickname"));
            request.setAttribute("userBalance", userData.getBigDecimal("balance"));

            log.info("API Token验证成功，用户: {}", userData.getString("nickname"));
            return true;

        } catch (Exception e) {
            log.error("API拦截器异常", e);
            writeErrorResponse(response, "系统异常");
            return false;
        }
    }
    
    /**
     * 写入错误响应
     */
    private void writeErrorResponse(HttpServletResponse response, String message) throws Exception {
        response.setContentType("application/json;charset=UTF-8");
        response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
        
        JSONObject errorResult = new JSONObject();
        errorResult.put("success", false);
        errorResult.put("message", message);
        errorResult.put("code", 401);
        errorResult.put("timestamp", System.currentTimeMillis());
        
        PrintWriter writer = response.getWriter();
        writer.write(errorResult.toJSONString());
        writer.flush();
        writer.close();
    }
}
