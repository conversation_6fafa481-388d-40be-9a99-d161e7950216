package org.jeecg.modules.api.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.api.dto.CreatorWorkflowDTO;
import org.jeecg.modules.demo.aigc_agent.entity.AigcAgent;
import org.jeecg.modules.demo.aigc_agent.entity.AigcWorkflow;
import org.jeecg.modules.demo.aigc_agent.service.IAigcAgentService;
import org.jeecg.modules.demo.aigc_agent.service.IAigcWorkflowService;
import org.jeecg.modules.jianying.service.TosService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * @Description: 创作者工作流管理控制器
 * @Author: 智界AIGC
 * @Date: 2025-08-04
 * @Version: V1.0
 */
@Api(tags = "创作者中心-工作流管理")
@RestController
@RequestMapping("/api/creator/workflow")
@Slf4j
public class CreatorWorkflowController {

    @Autowired
    private IAigcWorkflowService aigcWorkflowService;

    @Autowired
    private IAigcAgentService aigcAgentService;

    @Autowired
    private TosService tosService;

    /**
     * 获取智能体的工作流列表
     */
    @AutoLog(value = "创作者工作流-列表查询")
    @ApiOperation(value = "创作者工作流-列表查询", notes = "获取指定智能体下的工作流列表")
    @GetMapping(value = "/list/{agentId}")
    public Result<?> queryList(@PathVariable String agentId, HttpServletRequest request) {
        
        try {
            // 获取当前登录用户
            LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            if (loginUser == null) {
                return Result.error("用户未登录");
            }
            
            log.info("🔍 创作者工作流列表查询 - 用户: {}, 智能体ID: {}", loginUser.getUsername(), agentId);

            // 验证智能体是否存在且属于当前用户
            AigcAgent agent = aigcAgentService.getById(agentId);
            if (agent == null) {
                return Result.error("智能体不存在");
            }
            
            if (!loginUser.getUsername().equals(agent.getCreateBy())) {
                return Result.error("无权限访问此智能体的工作流");
            }

            // 查询工作流列表
            QueryWrapper<AigcWorkflow> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("agent_id", agentId);
            queryWrapper.orderByDesc("create_time");
            
            List<AigcWorkflow> workflowList = aigcWorkflowService.list(queryWrapper);

            log.info("✅ 创作者工作流列表查询成功 - 用户: {}, 智能体ID: {}, 工作流数量: {}", 
                loginUser.getUsername(), agentId, workflowList.size());
            
            return Result.OK(workflowList);

        } catch (Exception e) {
            log.error("❌ 创作者工作流列表查询失败", e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 新增工作流
     */
    @AutoLog(value = "创作者工作流-新增")
    @ApiOperation(value = "创作者工作流-新增", notes = "为智能体新增工作流")
    @PostMapping(value = "/add")
    public Result<?> add(@Valid @RequestBody CreatorWorkflowDTO dto, HttpServletRequest request) {
        
        try {
            // 获取当前登录用户
            LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            if (loginUser == null) {
                return Result.error("用户未登录");
            }
            
            log.info("🔍 创作者新增工作流 - 用户: {}, 智能体ID: {}, 工作流名称: {}", 
                loginUser.getUsername(), dto.getAgentId(), dto.getWorkflowName());

            // 验证智能体是否存在且属于当前用户
            AigcAgent agent = aigcAgentService.getById(dto.getAgentId());
            if (agent == null) {
                return Result.error("智能体不存在");
            }
            
            if (!loginUser.getUsername().equals(agent.getCreateBy())) {
                return Result.error("无权限为此智能体添加工作流");
            }

            // 创建工作流实体
            AigcWorkflow workflow = new AigcWorkflow();
            BeanUtils.copyProperties(dto, workflow);
            
            // 🔧 自动设置系统字段
            workflow.setWorkflowId(generateUniqueWorkflowId()); // 自动生成工作流ID
            workflow.setCreateBy(loginUser.getUsername());
            workflow.setCreateTime(new Date());
            workflow.setUpdateTime(new Date());

            // 保存到数据库
            boolean success = aigcWorkflowService.save(workflow);
            
            if (success) {
                log.info("✅ 创作者工作流新增成功 - 用户: {}, 工作流ID: {}, 工作流名称: {}", 
                    loginUser.getUsername(), workflow.getWorkflowId(), workflow.getWorkflowName());
                return Result.OK(workflow);
            } else {
                return Result.error("工作流创建失败");
            }

        } catch (Exception e) {
            log.error("❌ 创作者工作流新增失败", e);
            return Result.error("创建失败: " + e.getMessage());
        }
    }

    /**
     * 编辑工作流
     */
    @AutoLog(value = "创作者工作流-编辑")
    @ApiOperation(value = "创作者工作流-编辑", notes = "编辑工作流信息")
    @PutMapping(value = "/edit/{id}")
    public Result<?> edit(@PathVariable String id, @Valid @RequestBody CreatorWorkflowDTO dto, HttpServletRequest request) {
        
        try {
            // 获取当前登录用户
            LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            if (loginUser == null) {
                return Result.error("用户未登录");
            }
            
            log.info("🔍 创作者编辑工作流 - 用户: {}, 工作流ID: {}", loginUser.getUsername(), id);

            // 查询工作流
            AigcWorkflow workflow = aigcWorkflowService.getById(id);
            if (workflow == null) {
                return Result.error("工作流不存在");
            }
            
            // 验证智能体权限
            AigcAgent agent = aigcAgentService.getById(workflow.getAgentId());
            if (agent == null || !loginUser.getUsername().equals(agent.getCreateBy())) {
                return Result.error("无权限编辑此工作流");
            }

            // 更新工作流信息
            workflow.setWorkflowName(dto.getWorkflowName());
            workflow.setWorkflowDescription(dto.getWorkflowDescription());
            workflow.setInputParamsDesc(dto.getInputParamsDesc()); // 🔥 添加输入参数说明的更新
            if (oConvertUtils.isNotEmpty(dto.getWorkflowPackage())) {
                workflow.setWorkflowPackage(dto.getWorkflowPackage());
            }
            workflow.setUpdateTime(new Date());

            // 保存到数据库
            boolean success = aigcWorkflowService.updateById(workflow);
            
            if (success) {
                log.info("✅ 创作者工作流编辑成功 - 用户: {}, 工作流ID: {}", loginUser.getUsername(), id);
                return Result.OK(workflow);
            } else {
                return Result.error("工作流更新失败");
            }

        } catch (Exception e) {
            log.error("❌ 创作者工作流编辑失败", e);
            return Result.error("编辑失败: " + e.getMessage());
        }
    }

    /**
     * 删除工作流
     */
    @AutoLog(value = "创作者工作流-删除")
    @ApiOperation(value = "创作者工作流-删除", notes = "删除工作流")
    @DeleteMapping(value = "/delete/{id}")
    public Result<?> delete(@PathVariable String id, HttpServletRequest request) {
        
        try {
            // 获取当前登录用户
            LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            if (loginUser == null) {
                return Result.error("用户未登录");
            }
            
            log.info("🔍 创作者删除工作流 - 用户: {}, 工作流ID: {}", loginUser.getUsername(), id);

            // 查询工作流
            AigcWorkflow workflow = aigcWorkflowService.getById(id);
            if (workflow == null) {
                return Result.error("工作流不存在");
            }
            
            // 验证智能体权限
            AigcAgent agent = aigcAgentService.getById(workflow.getAgentId());
            if (agent == null || !loginUser.getUsername().equals(agent.getCreateBy())) {
                return Result.error("无权限删除此工作流");
            }

            // 删除工作流文件（如果存在）
            if (oConvertUtils.isNotEmpty(workflow.getWorkflowPackage())) {
                try {
                    tosService.deleteFile(workflow.getWorkflowPackage());
                    log.info("✅ 工作流文件删除成功 - 文件: {}", workflow.getWorkflowPackage());
                } catch (Exception e) {
                    log.warn("⚠️ 工作流文件删除失败 - 文件: {}, 错误: {}", workflow.getWorkflowPackage(), e.getMessage());
                }
            }

            // 删除工作流记录
            boolean success = aigcWorkflowService.removeById(id);
            
            if (success) {
                log.info("✅ 创作者工作流删除成功 - 用户: {}, 工作流ID: {}", loginUser.getUsername(), id);
                return Result.OK("工作流删除成功！");
            } else {
                return Result.error("工作流删除失败");
            }

        } catch (Exception e) {
            log.error("❌ 创作者工作流删除失败", e);
            return Result.error("删除失败: " + e.getMessage());
        }
    }

    /**
     * 上传工作流文件
     */
    @AutoLog(value = "创作者工作流-文件上传")
    @ApiOperation(value = "创作者工作流-文件上传", notes = "上传工作流压缩包文件")
    @PostMapping(value = "/upload")
    public Result<?> uploadFile(
            @ApiParam(value = "工作流文件", required = true) @RequestParam("file") MultipartFile file,
            @ApiParam(value = "智能体ID", required = true) @RequestParam("agentId") String agentId,
            HttpServletRequest request) {

        try {
            // 获取当前登录用户
            LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            if (loginUser == null) {
                return Result.error("用户未登录");
            }

            log.info("🔍 创作者工作流文件上传 - 用户: {}, 智能体ID: {}, 文件名: {}",
                loginUser.getUsername(), agentId, file.getOriginalFilename());

            // 验证智能体是否存在且属于当前用户
            AigcAgent agent = aigcAgentService.getById(agentId);
            if (agent == null) {
                return Result.error("智能体不存在");
            }

            if (!loginUser.getUsername().equals(agent.getCreateBy())) {
                return Result.error("无权限为此智能体上传工作流文件");
            }

            // 验证文件
            if (file.isEmpty()) {
                return Result.error("请选择要上传的文件");
            }

            // 验证文件类型（只允许压缩包）
            String originalFilename = file.getOriginalFilename();
            if (originalFilename == null ||
                (!originalFilename.toLowerCase().endsWith(".zip") &&
                 !originalFilename.toLowerCase().endsWith(".rar") &&
                 !originalFilename.toLowerCase().endsWith(".7z"))) {
                return Result.error("只支持上传 .zip、.rar、.7z 格式的压缩包文件");
            }

            // 验证文件大小（限制50MB）
            long maxSize = 50 * 1024 * 1024; // 50MB
            if (file.getSize() > maxSize) {
                return Result.error("文件大小不能超过50MB");
            }

            // 上传文件到TOS
            String fileName = tosService.uploadGeneralFile(file, "workflow");

            if (oConvertUtils.isNotEmpty(fileName)) {
                log.info("✅ 创作者工作流文件上传成功 - 用户: {}, 文件名: {}", loginUser.getUsername(), fileName);

                // 返回文件信息
                return Result.OK(fileName, "文件上传成功！");
            } else {
                return Result.error("文件上传失败");
            }

        } catch (Exception e) {
            log.error("❌ 创作者工作流文件上传失败", e);
            return Result.error("上传失败: " + e.getMessage());
        }
    }

    /**
     * 获取工作流详情
     */
    @AutoLog(value = "创作者工作流-详情查询")
    @ApiOperation(value = "创作者工作流-详情查询", notes = "获取工作流详细信息")
    @GetMapping(value = "/detail/{id}")
    public Result<?> queryById(@PathVariable String id, HttpServletRequest request) {

        try {
            // 获取当前登录用户
            LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            if (loginUser == null) {
                return Result.error("用户未登录");
            }

            log.info("🔍 创作者工作流详情查询 - 用户: {}, 工作流ID: {}", loginUser.getUsername(), id);

            // 查询工作流
            AigcWorkflow workflow = aigcWorkflowService.getById(id);
            if (workflow == null) {
                return Result.error("工作流不存在");
            }

            // 验证智能体权限
            AigcAgent agent = aigcAgentService.getById(workflow.getAgentId());
            if (agent == null || !loginUser.getUsername().equals(agent.getCreateBy())) {
                return Result.error("无权限查看此工作流");
            }

            log.info("✅ 创作者工作流详情查询成功 - 用户: {}, 工作流ID: {}", loginUser.getUsername(), id);
            return Result.OK(workflow);

        } catch (Exception e) {
            log.error("❌ 创作者工作流详情查询失败", e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 生成唯一的工作流ID
     * 格式：WF_ + UUID前8位
     */
    private String generateUniqueWorkflowId() {
        String prefix = "WF_";
        String uuid = UUID.randomUUID().toString().replace("-", "").substring(0, 8).toUpperCase();
        String workflowId = prefix + uuid;

        // 检查是否已存在，如果存在则重新生成
        while (aigcWorkflowService.lambdaQuery().eq(AigcWorkflow::getWorkflowId, workflowId).exists()) {
            uuid = UUID.randomUUID().toString().replace("-", "").substring(0, 8).toUpperCase();
            workflowId = prefix + uuid;
        }

        return workflowId;
    }
}
