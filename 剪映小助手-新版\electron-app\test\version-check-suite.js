/**
 * 版本检查功能测试套件
 * 包含单元测试和集成测试
 */

const VersionChecker = require('../src/utils/VersionChecker');
const UpdateManager = require('../src/utils/UpdateManager');

class VersionCheckTestSuite {
  constructor() {
    this.testResults = [];
    this.passedTests = 0;
    this.failedTests = 0;
  }

  /**
   * 运行测试
   * @param {string} testName 测试名称
   * @param {Function} testFunction 测试函数
   */
  async runTest(testName, testFunction) {
    try {
      console.log(`\n🧪 运行测试: ${testName}`);
      await testFunction();
      console.log(`✅ 测试通过: ${testName}`);
      this.testResults.push({ name: testName, status: 'PASS' });
      this.passedTests++;
    } catch (error) {
      console.error(`❌ 测试失败: ${testName}`);
      console.error('错误详情:', error.message);
      this.testResults.push({ name: testName, status: 'FAIL', error: error.message });
      this.failedTests++;
    }
  }

  /**
   * 断言函数
   */
  assert(condition, message) {
    if (!condition) {
      throw new Error(message || '断言失败');
    }
  }

  assertEqual(actual, expected, message) {
    if (actual !== expected) {
      throw new Error(message || `期望 ${expected}，实际 ${actual}`);
    }
  }

  assertNotNull(value, message) {
    if (value === null || value === undefined) {
      throw new Error(message || '值不应为null或undefined');
    }
  }

  /**
   * 版本比较算法测试
   */
  async testVersionComparison() {
    const versionChecker = new VersionChecker();
    
    // 测试用例
    const testCases = [
      ['1.0.0', '1.0.1', -1],
      ['1.0.1', '1.0.0', 1],
      ['1.0.0', '1.0.0', 0],
      ['1.0.0', '1.1.0', -1],
      ['2.0.0', '1.9.9', 1],
      ['1.0', '1.0.0', 0],
      ['1.2.3', '1.2.3', 0],
      ['0.9.9', '1.0.0', -1],
      ['10.0.0', '2.0.0', 1]
    ];

    testCases.forEach(([v1, v2, expected]) => {
      const result = versionChecker.compareVersions(v1, v2);
      this.assertEqual(result, expected, `版本比较 ${v1} vs ${v2} 应该返回 ${expected}`);
    });
  }

  /**
   * 强制更新判断测试
   */
  async testForceUpdateLogic() {
    const versionChecker = new VersionChecker();
    
    // 测试包含强制更新关键词的情况
    const forceUpdateCases = [
      { updateContent: '安全更新：修复重要漏洞', expected: true },
      { updateContent: '紧急修复：解决崩溃问题', expected: true },
      { updateContent: '重要更新：性能优化', expected: true },
      { updateContent: '修复严重bug', expected: true },
      { updateContent: '常规功能更新', expected: false },
      { updateContent: '界面优化', expected: false }
    ];

    forceUpdateCases.forEach(({ updateContent, expected }) => {
      const result = versionChecker.shouldForceUpdate({ updateContent });
      this.assertEqual(result, expected, `更新内容"${updateContent}"的强制更新判断错误`);
    });
  }

  /**
   * 缓存机制测试
   */
  async testCacheMechanism() {
    const versionChecker = new VersionChecker();
    
    // 清除现有缓存
    versionChecker.clearCache();
    
    // 测试缓存无效时的状态
    this.assertEqual(versionChecker.isCacheValid(), false, '清除缓存后应该无效');
    
    // 模拟设置缓存
    versionChecker.store.set('cachedVersionInfo', {
      versionNumber: '1.0.1',
      updateContent: '测试更新'
    });
    versionChecker.store.set('lastCheckTime', Date.now());
    
    // 测试缓存有效时的状态
    this.assertEqual(versionChecker.isCacheValid(), true, '设置缓存后应该有效');
  }

  /**
   * 用户偏好设置测试
   */
  async testUserPreferences() {
    const versionChecker = new VersionChecker();
    
    // 获取默认偏好设置
    const defaultPrefs = versionChecker.getUserPreferences();
    this.assertNotNull(defaultPrefs, '默认偏好设置不应为空');
    
    // 更新偏好设置
    const newPrefs = {
      autoCheck: false,
      reminderDelay: 7200000,
      skippedVersions: ['1.0.1']
    };
    
    versionChecker.updateUserPreferences(newPrefs);
    const updatedPrefs = versionChecker.getUserPreferences();
    
    this.assertEqual(updatedPrefs.autoCheck, false, '自动检查设置应该更新');
    this.assertEqual(updatedPrefs.reminderDelay, 7200000, '提醒延迟设置应该更新');
    this.assert(updatedPrefs.skippedVersions.includes('1.0.1'), '跳过版本列表应该更新');
  }

  /**
   * 日志功能测试
   */
  async testLoggingFunctionality() {
    const versionChecker = new VersionChecker({ enableLogging: true });
    
    // 清除现有日志
    versionChecker.clearLogs();
    
    // 记录不同级别的日志
    versionChecker.log('信息日志', { test: true }, 'info');
    versionChecker.log('警告日志', { test: true }, 'warn');
    versionChecker.log('错误日志', { test: true }, 'error');
    
    // 获取日志
    const allLogs = versionChecker.getLogs(10);
    this.assertEqual(allLogs.length, 4, '应该有4条日志（包括清除日志的记录）'); // 包括clearLogs的日志
    
    // 测试按级别过滤
    const errorLogs = versionChecker.getLogs(10, 'error');
    this.assert(errorLogs.length >= 1, '应该至少有1条错误日志');
    
    // 测试日志导出
    const exportedLogs = versionChecker.exportLogs();
    this.assert(exportedLogs.length > 0, '导出的日志不应为空');
    this.assert(exportedLogs.startsWith('['), '导出的日志应该是JSON格式');
  }

  /**
   * 错误处理测试
   */
  async testErrorHandling() {
    const versionChecker = new VersionChecker({
      apiBaseUrl: 'http://invalid-url-for-testing.com',
      maxRetries: 1,
      requestTimeout: 1000
    });
    
    // 测试网络错误处理
    const result = await versionChecker.checkForUpdates(true);
    
    this.assertEqual(result.networkError, true, '应该检测到网络错误');
    this.assertNotNull(result.errorType, '应该有错误类型');
    this.assertNotNull(result.errorMessage, '应该有错误消息');
  }

  /**
   * UpdateManager集成测试
   */
  async testUpdateManagerIntegration() {
    const updateManager = new UpdateManager({
      apiBaseUrl: 'https://aigcview.com/jeecg-boot',
      programType: 'desktop',
      enableBackgroundCheck: false // 测试时禁用后台检查
    });
    
    // 测试状态获取
    const status = updateManager.getUpdateStatus();
    this.assertNotNull(status.currentVersion, '应该有当前版本信息');
    this.assertNotNull(status.userPreferences, '应该有用户偏好设置');
    
    // 测试清理功能
    updateManager.cleanup();
    this.assert(true, '清理功能应该正常执行');
  }

  /**
   * 运行所有测试
   */
  async runAllTests() {
    console.log('🚀 开始运行版本检查功能测试套件');
    console.log('=' .repeat(50));
    
    await this.runTest('版本比较算法测试', () => this.testVersionComparison());
    await this.runTest('强制更新判断测试', () => this.testForceUpdateLogic());
    await this.runTest('缓存机制测试', () => this.testCacheMechanism());
    await this.runTest('用户偏好设置测试', () => this.testUserPreferences());
    await this.runTest('日志功能测试', () => this.testLoggingFunctionality());
    await this.runTest('错误处理测试', () => this.testErrorHandling());
    await this.runTest('UpdateManager集成测试', () => this.testUpdateManagerIntegration());
    
    this.printTestResults();
  }

  /**
   * 打印测试结果
   */
  printTestResults() {
    console.log('\n' + '=' .repeat(50));
    console.log('📊 测试结果汇总');
    console.log('=' .repeat(50));
    
    this.testResults.forEach(result => {
      const status = result.status === 'PASS' ? '✅' : '❌';
      console.log(`${status} ${result.name}`);
      if (result.error) {
        console.log(`   错误: ${result.error}`);
      }
    });
    
    console.log('\n📈 统计信息:');
    console.log(`总测试数: ${this.testResults.length}`);
    console.log(`通过: ${this.passedTests}`);
    console.log(`失败: ${this.failedTests}`);
    console.log(`成功率: ${((this.passedTests / this.testResults.length) * 100).toFixed(1)}%`);
    
    if (this.failedTests === 0) {
      console.log('\n🎉 所有测试都通过了！');
    } else {
      console.log('\n⚠️  有测试失败，请检查相关功能');
    }
  }
}

// 运行测试
if (require.main === module) {
  const testSuite = new VersionCheckTestSuite();
  testSuite.runAllTests().catch(error => {
    console.error('测试套件运行失败:', error);
    process.exit(1);
  });
}

module.exports = VersionCheckTestSuite;
