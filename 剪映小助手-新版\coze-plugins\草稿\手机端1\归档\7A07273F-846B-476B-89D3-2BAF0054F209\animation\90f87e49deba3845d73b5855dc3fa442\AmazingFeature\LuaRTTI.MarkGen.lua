
Util = Util or {} ---@type Util
Util.__typename = "Util"
Util.__supername = ""
Util.__scriptname = ""
Util.__scriptpath = ""


AETools = AETools or {} ---@type AETools
AETools.__typename = "AETools"
AETools.__supername = ""
AETools.__scriptname = ""
AETools.__scriptpath = ""


SeekModeScript = SeekModeScript or {} ---@type SeekModeScript
SeekModeScript.__typename = "SeekModeScript"
SeekModeScript.__supername = "ScriptComponent"
SeekModeScript.__scriptname = ""
SeekModeScript.__scriptpath = ""

