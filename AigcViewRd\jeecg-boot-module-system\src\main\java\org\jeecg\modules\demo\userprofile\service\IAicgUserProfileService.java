package org.jeecg.modules.demo.userprofile.service;

import java.math.BigDecimal;
import org.jeecg.modules.demo.userprofile.entity.AicgUserProfile;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * @Description: 用户扩展信息表
 * @Author: jeecg-boot
 * @Date:   2025-06-14
 * @Version: V1.0
 */
public interface IAicgUserProfileService extends IService<AicgUserProfile> {

    /**
     * 根据用户ID查询用户扩展信息
     * @param userId 用户ID
     * @return 用户扩展信息
     */
    AicgUserProfile getByUserId(String userId);

    /**
     * 预扣费（冻结余额）
     * @param userId 用户ID
     * @param amount 冻结金额
     * @return 是否成功
     */
    boolean preDeductBalance(String userId, java.math.BigDecimal amount);

    /**
     * 确认扣费（从冻结余额转为实际扣费）
     * @param userId 用户ID
     * @param amount 扣费金额
     * @return 是否成功
     */
    boolean confirmDeductBalance(String userId, java.math.BigDecimal amount);

    /**
     * 退还冻结余额
     * @param userId 用户ID
     * @param amount 退还金额
     * @return 是否成功
     */
    boolean refundFrozenBalance(String userId, java.math.BigDecimal amount);

    /**
     * 获取用户可用余额（账户余额 - 冻结余额）
     * @param userId 用户ID
     * @return 可用余额
     */
    java.math.BigDecimal getAvailableBalance(String userId);

    /**
     * 根据用户名查询用户扩展信息
     * @param username 用户名
     * @return 用户扩展信息
     */
    AicgUserProfile getByUsername(String username);

    /**
     * 获取用户完整信息（联合查询sys_user表）
     * @param username 用户名
     * @return 用户完整信息
     */
    java.util.Map<String, Object> getUserFullInfo(String username);
    
    /**
     * 初始化用户扩展信息
     * @param userId 用户ID
     * @param nickname 昵称
     * @return 用户扩展信息
     */
    AicgUserProfile initUserProfile(String userId, String nickname);
    
    /**
     * 充值
     * @param userId 用户ID
     * @param amount 充值金额
     * @param description 描述
     * @param operatorId 操作人ID
     * @return 是否成功
     */
    boolean recharge(String userId, BigDecimal amount, String description, String operatorId);
    
    /**
     * 消费
     * @param userId 用户ID
     * @param amount 消费金额
     * @param description 描述
     * @param operatorId 操作人ID
     * @return 是否成功
     */
    boolean consume(String userId, BigDecimal amount, String description, String operatorId);
    
    /**
     * 重新生成API密钥
     * @param userId 用户ID
     * @param operatorId 操作人ID
     * @return 新的API密钥
     */
    String regenerateApiKey(String userId, String operatorId);
    
    /**
     * 更新昵称
     * @param userId 用户ID
     * @param nickname 新昵称
     * @param operatorId 操作人ID
     * @return 是否成功
     */
    boolean updateNickname(String userId, String nickname, String operatorId);
    
    /**
     * 获取用户余额
     * @param userId 用户ID
     * @return 用户余额
     */
    BigDecimal getUserBalance(String userId);
    
    /**
     * 检查用户余额是否足够
     * @param userId 用户ID
     * @param amount 需要的金额
     * @return 是否足够
     */
    boolean checkBalance(String userId, BigDecimal amount);

    /**
     * 根据邀请码查询用户扩展信息
     * @param inviteCode 邀请码
     * @return 用户扩展信息
     */
    AicgUserProfile getByInviteCode(String inviteCode);
}
