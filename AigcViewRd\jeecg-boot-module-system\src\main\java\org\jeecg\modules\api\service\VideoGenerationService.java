package org.jeecg.modules.api.service;

import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.demo.userprofile.entity.AicgUserProfile;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * 视频生成业务服务
 * 
 * <AUTHOR>
 * @since 2025-01-16
 */
@Service
@Slf4j
public class VideoGenerationService {

    @Autowired
    private DouBaoVideoApiService douBaoVideoApiService;

    /**
     * 创建视频生成任务
     */
    public Map<String, Object> createVideoTask(Map<String, Object> params, AicgUserProfile userProfile) {
        try {
            log.info("开始创建视频生成任务，用户: {}", userProfile.getUserId());

            // 1. 参数预处理
            Map<String, Object> processedParams = preprocessParams(params);
            
            // 2. 调用豆包API创建任务
            Map<String, Object> apiResult = douBaoVideoApiService.createVideoTask(processedParams);
            
            // 3. 记录任务信息（可选：保存到数据库）
            String taskId = (String) apiResult.get("task_id");
            log.info("视频生成任务创建成功: taskId={}, 用户={}", taskId, userProfile.getUserId());
            
            // 4. 构建返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("task_id", taskId);
            result.put("status", apiResult.get("status"));
            result.put("message", apiResult.get("message"));
            result.put("estimated_time", apiResult.get("estimated_time"));
            
            return result;

        } catch (Exception e) {
            log.error("创建视频生成任务失败", e);
            throw new RuntimeException("创建视频任务失败: " + e.getMessage());
        }
    }

    /**
     * 查询视频生成任务状态
     */
    public Map<String, Object> queryTaskStatus(String taskId) {
        try {
            log.info("查询视频生成任务状态: {}", taskId);

            // 调用豆包API查询任务状态
            Map<String, Object> apiResult = douBaoVideoApiService.queryTaskStatus(taskId);

            log.info("视频任务状态查询成功: taskId={}, status={}", taskId, apiResult.get("status"));

            return apiResult;

        } catch (Exception e) {
            log.error("查询视频任务状态失败", e);
            throw new RuntimeException("查询任务状态失败: " + e.getMessage());
        }
    }

    /**
     * 查询视频生成任务状态（支持轮询等待）
     */
    public Map<String, Object> queryTaskStatusWithWait(String taskId, Integer waitCnt) {
        try {
            log.info("查询视频生成任务状态（轮询模式） - 任务ID: {}, 等待次数: {}", taskId, waitCnt);

            Map<String, Object> lastResult = null;

            for (int i = 1; i <= waitCnt; i++) {
                log.debug("第{}次查询任务状态: {}", i, taskId);

                // 调用豆包API查询任务状态
                Map<String, Object> apiResult = douBaoVideoApiService.queryTaskStatus(taskId);
                lastResult = apiResult;

                String status = (String) apiResult.get("status");
                log.debug("第{}次查询结果 - 状态: {}", i, status);

                // 如果任务已完成或失败，直接返回
                if ("completed".equals(status) || "failed".equals(status) || "cancelled".equals(status)) {
                    log.info("任务已完成，第{}次查询获得最终状态: {}", i, status);
                    return apiResult;
                }

                // 如果不是最后一次查询，等待3秒后继续
                if (i < waitCnt) {
                    try {
                        log.debug("等待3秒后进行第{}次查询...", i + 1);
                        Thread.sleep(3000);
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        log.warn("等待被中断，返回当前查询结果");
                        break;
                    }
                }
            }

            log.info("完成{}次查询，最终状态: {}", waitCnt, lastResult.get("status"));
            return lastResult;

        } catch (Exception e) {
            log.error("轮询查询视频任务状态失败", e);
            throw new RuntimeException("查询任务状态失败: " + e.getMessage());
        }
    }

    /**
     * 取消视频生成任务
     */
    public Map<String, Object> cancelVideoTask(String taskId) {
        try {
            log.info("取消视频生成任务: {}", taskId);

            // 调用豆包API取消任务
            Map<String, Object> apiResult = douBaoVideoApiService.cancelVideoTask(taskId);
            
            log.info("视频任务取消成功: {}", taskId);
            
            return apiResult;

        } catch (Exception e) {
            log.error("取消视频任务失败", e);
            throw new RuntimeException("取消任务失败: " + e.getMessage());
        }
    }

    /**
     * 参数预处理
     */
    private Map<String, Object> preprocessParams(Map<String, Object> params) {
        Map<String, Object> processed = new HashMap<>(params);
        
        // 确保必要参数存在
        if (!processed.containsKey("model")) {
            processed.put("model", "doubao-seedance-1-0-lite-i2v-250428");
        }
        
        if (!processed.containsKey("resolution")) {
            processed.put("resolution", "480p");
        }
        
        if (!processed.containsKey("duration")) {
            processed.put("duration", 5);
        }
        
        // 移除不需要传递给API的参数
        processed.remove("apiKey");
        processed.remove("asyn");
        
        log.info("参数预处理完成: {}", processed);
        return processed;
    }
}
