import TOSBase from '../base';
export declare type PostSignatureCondition = {
    [key: string]: string;
} | ['eq', string, string] | ['starts-with', string, string] | ['content-length-range', number, number];
export interface CalculatePostSignatureInput {
    bucket?: string;
    key: string;
    expiresIn?: number;
    fields?: Record<string, unknown>;
    conditions?: PostSignatureCondition[];
}
export declare function calculatePostSignature(this: TOSBase, input: CalculatePostSignatureInput | string): Promise<{
    [x: string]: unknown;
}>;
export default calculatePostSignature;
