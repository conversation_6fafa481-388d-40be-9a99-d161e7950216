/// <reference types="node" />
import { DataTransferStatus, <PERSON><PERSON> } from '../../interface';
import TOSBase, { TosResponse } from '../base';
import { IRateLimiter } from '../../universal/rate-limiter';
import { RestoreInfo } from './sharedTypes';
import { ReplicationStatusType } from '../../TosExportEnum';
export interface GetObjectInput {
    bucket?: string;
    key: string;
    versionId?: string;
    headers?: {
        [key: string]: string | undefined;
        'If-Modified-Since'?: string;
        'If-Unmodified-Since'?: string;
        'If-Match'?: string;
        'If-None-Match'?: string;
        'x-tos-server-side-encryption-customer-key'?: string;
        'x-tos-server-side-encryption-customer-key-md5'?: string;
        'x-tos-server-side-encryption-customer-algorithm'?: string;
        Range?: string;
    };
    response?: Headers & {
        'cache-control'?: string;
        'content-disposition'?: string;
        'content-encoding'?: string;
        'content-language'?: string;
        'content-type'?: string;
        expires?: string;
    };
}
/**
 * @deprecated use getObjectV2 instead
 * @returns arraybuffer
 */
export declare function getObject(this: TOSBase, input: GetObjectInput | string): Promise<TosResponse<Buffer>>;
declare type DataType = 'stream' | 'buffer' | 'blob';
export interface GetObjectV2Input {
    bucket?: string;
    key: string;
    versionId?: string;
    /**
     * The type of return value, 'stream' | 'blob'
     * default: 'stream'
     *
     * nodejs environment can use 'stream' and 'buffer'
     * browser environment can use 'blob'
     */
    dataType?: DataType;
    ifMatch?: string;
    ifModifiedSince?: string | Date;
    ifNoneMatch?: string;
    ifUnmodifiedSince?: string | Date;
    ssecAlgorithm?: string;
    ssecKey?: string;
    ssecKeyMD5?: string;
    /**
     * unit: bit/s
     * server side traffic limit
     **/
    trafficLimit?: number;
    /**
     * only works for nodejs environment
     */
    rateLimiter?: IRateLimiter;
    range?: string;
    rangeStart?: number;
    rangeEnd?: number;
    process?: string;
    saveBucket?: string;
    saveObject?: string;
    responseCacheControl?: string;
    responseContentDisposition?: string;
    responseContentEncoding?: string;
    responseContentLanguage?: string;
    responseContentType?: string;
    responseExpires?: Date;
    dataTransferStatusChange?: (status: DataTransferStatus) => void;
    /**
     * the simple progress feature
     * percent is [0, 1].
     */
    progress?: (percent: number) => void;
    headers?: {
        [key: string]: string | undefined;
        'If-Modified-Since'?: string;
        'If-Unmodified-Since'?: string;
        'If-Match'?: string;
        'If-None-Match'?: string;
        'x-tos-server-side-encryption-customer-key'?: string;
        'x-tos-server-side-encryption-customer-key-md5'?: string;
        'x-tos-server-side-encryption-customer-algorithm'?: string;
        range?: string;
    };
    /**
     * @deprecated use responseXxx options instead
     */
    response?: Headers & {
        'cache-control'?: string;
        'content-disposition'?: string;
        'content-encoding'?: string;
        'content-language'?: string;
        'content-type'?: string;
        expires?: string;
    };
}
export interface GetObjectV2Output {
    content: NodeJS.ReadableStream | Buffer | Blob;
    etag: string;
    lastModified: string;
    hashCrc64ecma: string;
    RestoreInfo?: RestoreInfo;
    ReplicationStatus?: ReplicationStatusType;
}
export interface GetObjectV2OutputStream extends Omit<GetObjectV2Output, 'content'> {
    content: NodeJS.ReadableStream;
}
export interface GetObjectV2InputBuffer extends Omit<GetObjectV2Input, 'dataType'> {
    dataType: 'buffer';
}
export interface GetObjectV2OutputBuffer extends Omit<GetObjectV2Output, 'content'> {
    content: Buffer;
}
export interface GetObjectV2InputBlob extends Omit<GetObjectV2Input, 'dataType'> {
    dataType: 'blob';
}
export interface GetObjectV2OutputBlob extends Omit<GetObjectV2Output, 'content'> {
    content: Blob;
}
/**
 * `getObjectV2` default returns stream, using `dataType` param to return other type(eg: buffer, blob)
 */
declare function getObjectV2(this: TOSBase, input: GetObjectV2InputBlob): Promise<TosResponse<GetObjectV2OutputBlob>>;
declare function getObjectV2(this: TOSBase, input: GetObjectV2InputBuffer): Promise<TosResponse<GetObjectV2OutputBuffer>>;
declare function getObjectV2(this: TOSBase, input: GetObjectV2Input | string): Promise<TosResponse<GetObjectV2OutputStream>>;
interface GetObjectToFileInput extends Omit<GetObjectV2Input, 'dataType'> {
    filePath: string;
}
interface GetObjectToFileOutput extends Omit<GetObjectV2Output, 'content'> {
}
export declare function getObjectToFile(this: TOSBase, input: GetObjectToFileInput): Promise<TosResponse<GetObjectToFileOutput>>;
export { getObjectV2 };
