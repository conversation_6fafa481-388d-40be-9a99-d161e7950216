# 智界Aigc 对外API接口使用说明

## 📋 接口概述

智界Aigc提供两个对外API接口：
1. **API-Key验证接口** - 验证API密钥的有效性
2. **HTML文件生成接口** - 将HTML代码生成文件并返回访问地址和二维码

## 🔐 安全机制

### 多层安全防护
1. **API-Key身份验证** - 确保调用者身份合法
2. **请求签名验证** - 防止请求被篡改和重放攻击
3. **HTML内容安全检查** - 防止恶意代码注入
4. **访问频率限制** - 防止恶意刷量攻击
5. **时间戳验证** - 防止重放攻击（5分钟有效期）

### 签名算法
```
signature = SHA256(apiKey + htmlContent + timestamp)
```

## 🚀 接口详情

### 1. API-Key验证接口（支持插件调用验证）

#### 功能说明
此接口支持两种模式：
1. **基础验证模式**：仅验证API-Key的有效性，不进行任何扣费操作
2. **插件调用模式**：验证API-Key后，检查用户余额并扣除插件所需点数，同时更新插件统计信息

#### 插件调用逻辑
当提供`pluginName`参数时，系统将执行以下步骤：
1. 验证API-Key有效性
2. 根据插件名称查询插件信息
3. 检查用户余额是否足够支付插件所需点数
4. 扣除用户余额（点数 = 金额）
5. 更新插件的收益信息和调用次数
6. **更新插件创作者的使用总数**
7. 记录用户消费记录

#### 统计信息更新
每次成功调用插件后，系统会自动更新以下统计信息：
- **插件统计**：收益金额增加、调用次数+1
- **创作者统计**：插件使用总数+1、累计收益增加
- **用户记录**：生成消费交易记录

#### 收益分配机制
- **插件收益**：插件商城中每个插件的收益统计（金额）
- **创作者收益**：插件创作者的累计收益，等于其所有插件收益的总和
- **收益计算**：每次插件调用成功后，插件收益和创作者累计收益都会增加相应的金额

#### 接口信息
- **URL**: `POST /api/aigc/verify-apikey`
- **Content-Type**: `application/x-www-form-urlencoded`

#### 请求参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| apiKey | String | 是 | API密钥，格式：ak_xxxxxxxx |
| pluginName | String | 否 | 插件名称，如果提供则进行插件调用验证和扣费 |

#### 请求示例

##### 1. 仅验证API-Key
```bash
curl -X POST "http://localhost:8080/api/aigc/verify-apikey" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "apiKey=ak_test123456789abcdef"
```

##### 2. 验证API-Key并调用插件（扣费）
```bash
curl -X POST "http://localhost:8080/api/aigc/verify-apikey" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "apiKey=ak_test123456789abcdef&pluginName=小红书发布"
```

#### 响应示例

##### 1. 仅验证API-Key的响应
```json
{
  "success": true,
  "message": "验证成功",
  "code": 200,
  "result": {
    "valid": true,
    "userId": "1400726588175749122",
    "nickname": "测试用户123",
    "memberLevel": 1,
    "balance": 100.00,
    "pluginVerified": false
  },
  "timestamp": 1718352000000
}
```

##### 2. 插件验证成功的响应
```json
{
  "success": true,
  "message": "验证成功",
  "code": 200,
  "result": {
    "valid": true,
    "userId": "1400726588175749122",
    "nickname": "测试用户123",
    "memberLevel": 1,
    "balance": 100.00,
    "pluginVerified": true,
    "pluginName": "小红书发布",
    "deductedAmount": 10.00,
    "balanceAfter": 90.00,
    "pluginId": "1933809340810715137"
  },
  "timestamp": 1718352000000
}
```

##### 3. 余额不足的错误响应
```json
{
  "success": false,
  "message": "余额不足，插件 小红书发布 需要 10.00 元，当前余额 5.00 元",
  "code": 500,
  "result": null,
  "timestamp": 1718352000000
}
```

##### 4. 插件不存在的错误响应
```json
{
  "success": false,
  "message": "插件不存在: 不存在的插件",
  "code": 500,
  "result": null,
  "timestamp": 1718352000000
}
```

### 2. HTML文件生成接口

#### 接口信息
- **URL**: `POST /api/aigc/generate-html`
- **Content-Type**: `application/json`

#### 请求参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| apiKey | String | 是 | API密钥 |
| htmlContent | String | 是 | HTML代码内容 |
| timestamp | String | 是 | 时间戳（毫秒） |
| signature | String | 是 | 请求签名 |
| filename | String | 否 | 文件名（可选） |

#### 签名生成示例（Java）
```java
public static String generateSignature(String apiKey, String htmlContent, String timestamp) {
    String signString = apiKey + htmlContent + timestamp;
    MessageDigest digest = MessageDigest.getInstance("SHA-256");
    byte[] hash = digest.digest(signString.getBytes("UTF-8"));
    
    StringBuilder hexString = new StringBuilder();
    for (byte b : hash) {
        String hex = Integer.toHexString(0xff & b);
        if (hex.length() == 1) {
            hexString.append('0');
        }
        hexString.append(hex);
    }
    return hexString.toString();
}
```

#### 请求示例
```bash
curl -X POST "http://localhost:8080/api/aigc/generate-html" \
  -H "Content-Type: application/json" \
  -d '{
    "apiKey": "ak_test123456789abcdef",
    "htmlContent": "<html><head><title>Test</title></head><body><h1>Hello World</h1></body></html>",
    "timestamp": "1718352000000",
    "signature": "a1b2c3d4e5f6...",
    "filename": "test.html"
  }'
```

#### 响应示例
```json
{
  "success": true,
  "message": "HTML文件生成成功",
  "code": 200,
  "result": {
    "htmlUrl": "http://localhost:8080/api/aigc/html/aigc_1718352000000_abc12345.html",
    "qrcodeUrl": "http://localhost:8080/api/aigc/qrcode/qr_1718352000000_def67890.png",
    "filename": "aigc_1718352000000_abc12345.html",
    "generateTime": "2025-06-14T10:00:00.000+00:00"
  },
  "timestamp": 1718352000000
}
```

## 🛡️ 安全限制

### HTML内容安全检查
以下内容将被拒绝：

#### 危险标签
- `<script>` - JavaScript脚本
- `<iframe>` - 内嵌框架
- `<object>` - 对象嵌入
- `<embed>` - 嵌入内容
- `<form>` - 表单
- `<input>` - 输入框
- `<meta>` - 元数据
- `<link>` - 外部链接
- `<style>` - 样式

#### 危险属性
- `onclick`, `onload` 等事件处理器
- `javascript:` 协议
- `vbscript:` 协议
- `data:text/html` 数据URI

#### 其他限制
- 文件大小限制：2MB
- 不允许引用外部资源
- 不允许包含SQL注入代码

### 频率限制

#### 基础限制（非峰值时间）

##### 普通用户
- 每分钟最多：**60次**
- 每小时最多：**3,000次**
- 每天最多：**50,000次**

##### VIP用户（会员等级2）
- 每分钟最多：**120次**
- 每小时最多：**6,000次**
- 每天最多：**100,000次**

##### SVIP用户（会员等级3）
- 每分钟最多：**300次**
- 每小时最多：**15,000次**
- 每天最多：**300,000次**

#### 峰值时间段限制（9:00-18:00）

##### 普通用户
- 每分钟最多：**100次** (60 + 40)
- 每小时最多：**5,000次** (3,000 + 2,000)

##### VIP用户（会员等级2）
- 每分钟最多：**200次** (120 + 80)
- 每小时最多：**10,000次** (6,000 + 4,000)

##### SVIP用户（会员等级3）
- 每分钟最多：**500次** (300 + 200)
- 每小时最多：**25,000次** (15,000 + 10,000)

#### 峰值时间段说明
- **时间范围**：工作日 9:00 - 18:00
- **计算公式**：峰值限制 = 基础限制 × 2 + 额外配额
- **周末**：不启用峰值时间段
- **节假日**：启用峰值时间段

**注意**：超过任何一个限制都将返回403错误

### 时间戳验证
- 请求时间戳与服务器时间差不能超过5分钟
- 防止重放攻击

## 📝 错误码说明

| 错误码 | 说明 |
|--------|------|
| 400 | 参数错误 |
| 401 | API-Key无效 |
| 403 | 权限不足或频率限制 |
| 422 | HTML内容安全检查失败 |
| 500 | 服务器内部错误 |

## 🔧 客户端示例

### Java示例
```java
public class AigcApiClient {
    private String baseUrl = "http://localhost:8080";
    private String apiKey = "ak_your_api_key_here";
    
    public boolean verifyApiKey() {
        // 实现API-Key验证
        return true;
    }
    
    public String generateHtml(String htmlContent, String filename) {
        String timestamp = String.valueOf(System.currentTimeMillis());
        String signature = generateSignature(apiKey, htmlContent, timestamp);
        
        // 构建请求并发送
        // 返回生成的HTML文件URL
        return null;
    }
}
```

### Python示例
```python
import hashlib
import time
import requests

class AigcApiClient:
    def __init__(self, base_url, api_key):
        self.base_url = base_url
        self.api_key = api_key
    
    def generate_signature(self, html_content, timestamp):
        sign_string = self.api_key + html_content + timestamp
        return hashlib.sha256(sign_string.encode('utf-8')).hexdigest()
    
    def verify_api_key(self):
        url = f"{self.base_url}/api/aigc/verify-apikey"
        data = {"apiKey": self.api_key}
        response = requests.post(url, data=data)
        return response.json()
    
    def generate_html(self, html_content, filename=None):
        timestamp = str(int(time.time() * 1000))
        signature = self.generate_signature(html_content, timestamp)
        
        url = f"{self.base_url}/api/aigc/generate-html"
        data = {
            "apiKey": self.api_key,
            "htmlContent": html_content,
            "timestamp": timestamp,
            "signature": signature,
            "filename": filename
        }
        response = requests.post(url, json=data)
        return response.json()
```

## 📞 技术支持

如有问题，请联系：
- 技术支持邮箱：<EMAIL>
- 开发文档：http://localhost:8080/jeecg-boot/doc.html
- API测试工具：Swagger UI

---

**版本**: V1.0  
**更新时间**: 2025-06-14  
**维护团队**: 智界Aigc开发组
