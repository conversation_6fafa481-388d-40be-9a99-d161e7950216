{"openapi": "3.0.0", "info": {"title": "剪映小助手_智界工具箱 - 批量添加字幕", "description": "批量添加字幕", "version": "1.0.0"}, "servers": [{"url": "https://www.aigcview.com"}], "paths": {"/jeecg-boot/api/jianying/add_captions": {"post": {"summary": "批量添加字幕", "description": "批量添加字幕", "operationId": "addCaptions_zj", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"access_key": {"type": "string", "description": "访问密钥（必填）", "example": "JianyingAPI_2025_SecureAccess_AigcView", "default": "JianyingAPI_2025_SecureAccess_AigcView"}, "zj_font_size": {"type": "integer", "description": "字体大小，默认：15", "example": 15}, "zj_scale_y": {"type": "number", "description": "Y轴缩放比例，1.0为原始大小", "example": 1.0}, "zj_text_color": {"type": "string", "description": "文字颜色，十六进制格式：#ff1837", "example": "#ff1837"}, "zj_border_color": {"type": "string", "description": "边框颜色，十六进制格式，eg：#fe8a80", "example": "#fe8a80"}, "zj_draft_url": {"type": "string", "description": "草稿地址，使用createDraft_zj输出的draft_url即可（必填）", "example": "https://example.com/draft/123"}, "zj_scale_x": {"type": "number", "description": "X轴缩放比例，1.0为原始大小", "example": 1.0}, "zj_transform_x": {"type": "number", "description": "X轴位置坐标，相对于画布中心", "example": 0.5}, "zj_transform_y": {"type": "number", "description": "Y轴位置坐标，相对于画布中心", "example": 0.8}, "zj_alpha": {"type": "number", "description": "字体透明度，范围0-1，默认1.0", "minimum": 0, "maximum": 1, "example": 1.0}, "zj_letter_spacing": {"type": "number", "description": "字符间距，默认0", "example": 0}, "zj_alignment": {"type": "integer", "description": "字幕对齐方式，0左对齐，1居中对齐，2右对齐，3竖排居顶，4竖排居中，5竖排居底", "enum": [0, 1, 2, 3, 4, 5], "example": 1}, "zj_captions": {"type": "string", "description": "字幕内容JSON字符串（必填），查看说明：https://krxc4izye0.feishu.cn/wiki/HhQrw3BFhi1XGOkkJCBcfkqGnwf?from=from_copylink", "example": "[{\"text\":\"这是字幕内容\",\"start\":0,\"end\":5000000}]"}, "zj_font": {"type": "string", "description": "字体名称，字体列表：https://krxc4izye0.feishu.cn/wiki/SmnrwabXriG7JckEzyGcChk4nDd", "example": "默认字体"}, "zj_line_spacing": {"type": "number", "description": "行间距，默认0", "example": 0}, "zj_style_text": {"type": "integer", "description": "文本样式，0默认，1富文本样式", "enum": [0, 1], "example": 0}}, "required": ["access_key", "zj_draft_url", "zj_captions"], "additionalProperties": false}}}}, "responses": {"200": {"description": "成功添加字幕", "content": {"application/json": {"schema": {"type": "object", "properties": {"text_ids": {"type": "array", "description": "生成的字幕材料ID数组", "items": {"type": "string"}, "example": ["text-id-1", "text-id-2"]}, "track_id": {"type": "string", "description": "创建的文字轨道ID", "example": "track-id-123"}, "draft_url": {"type": "string", "description": "更新后的草稿文件URL", "example": "https://example.com/draft/123"}, "segment_ids": {"type": "array", "description": "创建的片段ID数组", "items": {"type": "string"}, "example": ["segment-id-1", "segment-id-2"]}, "segment_infos": {"type": "array", "description": "片段详细信息数组", "items": {"type": "object", "properties": {"id": {"type": "string", "description": "片段ID"}}}, "example": [{"id": "segment-id-1"}, {"id": "segment-id-2"}]}}, "required": ["text_ids", "track_id", "draft_url", "segment_ids", "segment_infos"]}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "description": "错误信息", "example": "批量添加字幕失败: 字幕信息不能为空"}}, "required": ["error"]}}}}, "500": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "description": "错误信息", "example": "批量添加字幕失败: 服务器内部错误"}}, "required": ["error"]}}}}}}}}}