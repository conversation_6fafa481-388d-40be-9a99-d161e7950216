package org.jeecg.modules.jianying.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 添加贴纸请求参数
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AddStickerRequest extends BaseJianyingRequest {
    
    @ApiModelProperty(value = "草稿地址（必填）", required = true,
                     example = "https://example.com/draft/123")
    @NotBlank(message = "draft_url不能为空")
    @JsonProperty("draft_url")
    private String zjDraftUrl;

    @ApiModelProperty(value = "贴纸ID（必填）", required = true, example = "sticker_123")
    @NotBlank(message = "sticker_id不能为空")
    @JsonProperty("sticker_id")
    private String zjStickerId;

    @ApiModelProperty(value = "开始时间（必填）", required = true, example = "0")
    @NotNull(message = "start不能为空")
    @JsonProperty("start")
    private Long zjStart;

    @ApiModelProperty(value = "结束时间（必填）", required = true, example = "5000000")
    @NotNull(message = "end不能为空")
    @JsonProperty("end")
    private Long zjEnd;

    @ApiModelProperty(value = "X轴位置", example = "100")
    @JsonProperty("transform_x")
    private Integer zjTransformX;

    @ApiModelProperty(value = "Y轴位置", example = "200")
    @JsonProperty("transform_y")
    private Integer zjTransformY;

    @ApiModelProperty(value = "缩放，0-5", example = "1.0")
    @JsonProperty("scale")
    private Double zjScale;
    
    @Override
    public String getSummary() {
        return "AddStickerRequest{" +
               "accessKey=" + (getAccessKey() != null && getAccessKey().length() > 10 ? 
                              getAccessKey().substring(0, 10) + "***" : "null") +
               ", draftUrl=" + (zjDraftUrl != null && zjDraftUrl.length() > 30 ? 
                               zjDraftUrl.substring(0, 30) + "***" : zjDraftUrl) +
               ", stickerId=" + zjStickerId +
               ", start=" + zjStart +
               ", end=" + zjEnd +
               "}";
    }
}
