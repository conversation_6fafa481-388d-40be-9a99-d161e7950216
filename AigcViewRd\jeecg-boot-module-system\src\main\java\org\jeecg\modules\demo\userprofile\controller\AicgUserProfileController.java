package org.jeecg.modules.demo.userprofile.controller;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.io.IOException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.demo.userprofile.entity.AicgUserProfile;
import org.jeecg.modules.demo.userprofile.service.IAicgUserProfileService;
import org.jeecg.modules.demo.userrecord.entity.AicgUserRecord;
import org.jeecg.modules.demo.userrecord.service.IAicgUserRecordService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.vo.LoginUser;
import org.apache.shiro.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;

/**
 * @Description: 用户扩展信息表
 * @Author: jeecg-boot
 * @Date:   2025-06-14
 * @Version: V1.0
 */
@Api(tags="用户扩展信息表")
@RestController
@RequestMapping("/demo/userprofile")
@Slf4j
public class AicgUserProfileController extends JeecgController<AicgUserProfile, IAicgUserProfileService> {
	@Autowired
	private IAicgUserProfileService aicgUserProfileService;
	@Autowired
	private IAicgUserRecordService aicgUserRecordService;
	
	/**
	 * 分页列表查询
	 *
	 * @param aicgUserProfile
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	@AutoLog(value = "用户扩展信息表-分页列表查询")
	@ApiOperation(value="用户扩展信息表-分页列表查询", notes="用户扩展信息表-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(AicgUserProfile aicgUserProfile,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<AicgUserProfile> queryWrapper = QueryGenerator.initQueryWrapper(aicgUserProfile, req.getParameterMap());
		Page<AicgUserProfile> page = new Page<AicgUserProfile>(pageNo, pageSize);
		IPage<AicgUserProfile> pageList = aicgUserProfileService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 * 获取当前用户的扩展信息
	 *
	 * @return
	 */
	@AutoLog(value = "获取当前用户的扩展信息")
	@ApiOperation(value="获取当前用户的扩展信息", notes="获取当前用户的扩展信息")
	@GetMapping(value = "/current")
	public Result<?> getCurrentUserProfile() {
		LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
		AicgUserProfile userProfile = aicgUserProfileService.getByUserId(sysUser.getId());
		if (userProfile == null) {
			// 初始化用户扩展信息
			userProfile = aicgUserProfileService.initUserProfile(sysUser.getId(), sysUser.getRealname());
		}
		return Result.OK(userProfile);
	}
	
	/**
	 * 获取用户交易记录
	 *
	 * @param pageNo
	 * @param pageSize
	 * @return
	 */
	@AutoLog(value = "获取用户交易记录")
	@ApiOperation(value="获取用户交易记录", notes="获取用户交易记录")
	@GetMapping(value = "/transactions")
	public Result<?> getUserTransactions(@RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
										@RequestParam(name="pageSize", defaultValue="10") Integer pageSize) {
		LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
		QueryWrapper<AicgUserRecord> queryWrapper = new QueryWrapper<>();
		queryWrapper.eq("user_id", sysUser.getId());
		queryWrapper.orderByDesc("transaction_time");
		
		Page<AicgUserRecord> page = new Page<AicgUserRecord>(pageNo, pageSize);
		IPage<AicgUserRecord> pageList = aicgUserRecordService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 * 重新生成API密钥
	 *
	 * @return
	 */
	@AutoLog(value = "重新生成API密钥")
	@ApiOperation(value="重新生成API密钥", notes="重新生成API密钥")
	@PostMapping(value = "/regenerateApiKey")
	public Result<?> regenerateApiKey() {
		LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
		String newApiKey = aicgUserProfileService.regenerateApiKey(sysUser.getId(), sysUser.getId());
		if (newApiKey != null) {
			return Result.OK("API密钥重新生成成功", newApiKey);
		} else {
			return Result.error("API密钥重新生成失败");
		}
	}
	
	/**
	 * 更新昵称
	 *
	 * @param nickname
	 * @return
	 */
	@AutoLog(value = "更新昵称")
	@ApiOperation(value="更新昵称", notes="更新昵称")
	@PostMapping(value = "/updateNickname")
	public Result<?> updateNickname(@RequestParam String nickname) {
		LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
		boolean success = aicgUserProfileService.updateNickname(sysUser.getId(), nickname, sysUser.getId());
		if (success) {
			return Result.OK("昵称更新成功");
		} else {
			return Result.error("昵称更新失败");
		}
	}
	
	/**
	 * 管理员充值接口
	 *
	 * @param userId
	 * @param amount
	 * @param description
	 * @return
	 */
	@AutoLog(value = "管理员充值")
	@ApiOperation(value="管理员充值", notes="管理员充值")
	@PostMapping(value = "/adminRecharge")
	public Result<?> adminRecharge(@RequestParam String userId, 
								  @RequestParam BigDecimal amount, 
								  @RequestParam(required = false) String description) {
		LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
		boolean success = aicgUserProfileService.recharge(userId, amount, 
			oConvertUtils.isEmpty(description) ? "管理员充值" : description, sysUser.getId());
		if (success) {
			return Result.OK("充值成功");
		} else {
			return Result.error("充值失败");
		}
	}
	
	/**
	 * 添加
	 *
	 * @param aicgUserProfile
	 * @return
	 */
	@AutoLog(value = "用户扩展信息表-添加")
	@ApiOperation(value="用户扩展信息表-添加", notes="用户扩展信息表-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody AicgUserProfile aicgUserProfile) {
		aicgUserProfileService.save(aicgUserProfile);
		return Result.OK("添加成功！");
	}
	
	/**
	 * 编辑
	 *
	 * @param aicgUserProfile
	 * @return
	 */
	@AutoLog(value = "用户扩展信息表-编辑")
	@ApiOperation(value="用户扩展信息表-编辑", notes="用户扩展信息表-编辑")
	@PutMapping(value = "/edit")
	public Result<?> edit(@RequestBody AicgUserProfile aicgUserProfile) {
		aicgUserProfileService.updateById(aicgUserProfile);
		return Result.OK("编辑成功!");
	}
	
	/**
	 * 通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "用户扩展信息表-通过id删除")
	@ApiOperation(value="用户扩展信息表-通过id删除", notes="用户扩展信息表-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		aicgUserProfileService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 * 批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "用户扩展信息表-批量删除")
	@ApiOperation(value="用户扩展信息表-批量删除", notes="用户扩展信息表-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.aicgUserProfileService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "用户扩展信息表-通过id查询")
	@ApiOperation(value="用户扩展信息表-通过id查询", notes="用户扩展信息表-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		AicgUserProfile aicgUserProfile = aicgUserProfileService.getById(id);
		if(aicgUserProfile==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(aicgUserProfile);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param aicgUserProfile
    */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, AicgUserProfile aicgUserProfile) {
        return super.exportXls(request, aicgUserProfile, AicgUserProfile.class, "用户扩展信息表");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, AicgUserProfile.class);
    }

}
