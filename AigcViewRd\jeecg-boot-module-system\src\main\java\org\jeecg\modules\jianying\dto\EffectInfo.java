package org.jeecg.modules.jianying.dto;

import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

/**
 * 剪映特效信息
 * 
 * <AUTHOR>
 * @date 2025-07-06
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class EffectInfo {
    
    /**
     * 特效ID
     */
    private String effectId;
    
    /**
     * 资源ID
     */
    private String resourceId;
    
    /**
     * 特效名称
     */
    private String name;

    /**
     * 特效唯一标识符
     */
    private String id;
    
    /**
     * 文件URL信息
     */
    private FileUrlInfo fileUrl;
    
    /**
     * 图标URL信息
     */
    private FileUrlInfo iconUrl;
    
    /**
     * SDK版本
     */
    private String sdkVersion;
    
    /**
     * 设备平台
     */
    private String devicePlatform;
    
    /**
     * 特效类型
     */
    private Integer effectType;
    
    /**
     * 扩展信息
     */
    private String extra;
    
    /**
     * SDK扩展信息
     */
    private String sdkExtra;
    
    /**
     * 创建时间
     */
    private Long createdAt;
    
    /**
     * 文件URL信息
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class FileUrlInfo {
        /**
         * URI
         */
        private String uri;
        
        /**
         * URL列表
         */
        private String[] urlList;
        
        /**
         * MD5
         */
        private String md5;
    }
}
