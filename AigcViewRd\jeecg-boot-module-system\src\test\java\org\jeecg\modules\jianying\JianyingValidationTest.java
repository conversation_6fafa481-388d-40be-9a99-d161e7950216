package org.jeecg.modules.jianying;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.JeecgSystemApplication;
import org.jeecg.modules.jianying.aspect.JianyingAccessKeyAspect;
import org.jeecg.modules.jianying.dto.*;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

import java.util.Arrays;
import java.util.Collections;

import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;

/**
 * 剪映小助手参数验证测试类
 * 测试各种Request类的参数验证功能
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
@Slf4j
@SpringBootTest(classes = JeecgSystemApplication.class)
@AutoConfigureWebMvc
@ActiveProfiles("test")
public class JianyingValidationTest {
    
    @Autowired
    private MockMvc mockMvc;
    
    private String getValidAccessKey() {
        return JianyingAccessKeyAspect.getExpectedAccessKey();
    }
    
    /**
     * 测试AddStickerRequest参数验证
     */
    @Test
    public void testAddStickerRequestValidation() throws Exception {
        // 测试缺少必填参数
        AddStickerRequest request = new AddStickerRequest();
        request.setAccessKey(getValidAccessKey());
        // 不设置必填的zjStickerId和zjDraftUrl
        
        String requestJson = JSONObject.toJSONString(request);
        log.info("测试AddStickerRequest缺少必填参数: {}", requestJson);
        
        mockMvc.perform(MockMvcRequestBuilders.post("/api/jianying/add_sticker")
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestJson))
                .andDo(print())
                .andExpect(MockMvcResultMatchers.status().isBadRequest())
                .andExpect(MockMvcResultMatchers.jsonPath("$.success").value(false))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(400));
    }
    
    /**
     * 测试AddStickerRequest有效参数
     */
    @Test
    public void testAddStickerRequestValid() throws Exception {
        AddStickerRequest request = new AddStickerRequest();
        request.setAccessKey(getValidAccessKey());
        request.setZjStickerId("sticker_123");
        request.setZjDraftUrl("https://example.com/draft/123");
        request.setZjStart(0L);
        request.setZjEnd(5000000L);
        
        String requestJson = JSONObject.toJSONString(request);
        log.info("测试AddStickerRequest有效参数: {}", requestJson);
        
        mockMvc.perform(MockMvcRequestBuilders.post("/api/jianying/add_sticker")
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestJson))
                .andDo(print())
                .andExpect(MockMvcResultMatchers.status().isOk());
    }
    
    /**
     * 测试GetAudioDurationRequest参数验证
     */
    @Test
    public void testGetAudioDurationRequestValidation() throws Exception {
        // 测试缺少必填参数
        GetAudioDurationRequest request = new GetAudioDurationRequest();
        request.setAccessKey(getValidAccessKey());
        // 不设置必填的zjMp3Url
        
        String requestJson = JSONObject.toJSONString(request);
        log.info("测试GetAudioDurationRequest缺少必填参数: {}", requestJson);
        
        mockMvc.perform(MockMvcRequestBuilders.post("/api/jianying/get_audio_duration")
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestJson))
                .andDo(print())
                .andExpect(MockMvcResultMatchers.status().isBadRequest())
                .andExpect(MockMvcResultMatchers.jsonPath("$.success").value(false))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(400));
    }
    
    /**
     * 测试GetAudioDurationRequest有效参数
     */
    @Test
    public void testGetAudioDurationRequestValid() throws Exception {
        GetAudioDurationRequest request = new GetAudioDurationRequest();
        request.setAccessKey(getValidAccessKey());
        request.setZjMp3Url("https://example.com/audio.mp3");
        
        String requestJson = JSONObject.toJSONString(request);
        log.info("测试GetAudioDurationRequest有效参数: {}", requestJson);
        
        mockMvc.perform(MockMvcRequestBuilders.post("/api/jianying/get_audio_duration")
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestJson))
                .andDo(print())
                .andExpect(MockMvcResultMatchers.status().isOk());
    }
    
    /**
     * 测试AddAudiosRequest参数验证
     */
    @Test
    public void testAddAudiosRequestValidation() throws Exception {
        // 测试空字符串参数
        AddAudiosRequest request = new AddAudiosRequest();
        request.setAccessKey(getValidAccessKey());
        request.setZjAudioInfos(""); // 空字符串
        request.setZjDraftUrl("https://example.com/draft/123");

        String requestJson = JSONObject.toJSONString(request);
        log.info("测试AddAudiosRequest空字符串参数: {}", requestJson);
        
        mockMvc.perform(MockMvcRequestBuilders.post("/api/jianying/add_audios")
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestJson))
                .andDo(print())
                .andExpect(MockMvcResultMatchers.status().isBadRequest())
                .andExpect(MockMvcResultMatchers.jsonPath("$.success").value(false))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(400));
    }
    
    /**
     * 测试AddAudiosRequest有效参数
     */
    @Test
    public void testAddAudiosRequestValid() throws Exception {
        AddAudiosRequest request = new AddAudiosRequest();
        request.setAccessKey(getValidAccessKey());
        request.setZjAudioInfos("[{\"audio_url\": \"http://example.com/audio1.mp3\",\"duration\":120,\"start\":0,\"end\":12000000,\"audio_effect\":\"教堂\"}]");
        request.setZjDraftUrl("https://example.com/draft/123");

        String requestJson = JSONObject.toJSONString(request);
        log.info("测试AddAudiosRequest有效参数: {}", requestJson);
        
        mockMvc.perform(MockMvcRequestBuilders.post("/api/jianying/add_audios")
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestJson))
                .andDo(print())
                .andExpect(MockMvcResultMatchers.status().isOk());
    }
    
    /**
     * 测试SearchStickerRequest参数验证
     */
    @Test
    public void testSearchStickerRequestValidation() throws Exception {
        // 测试缺少必填参数
        SearchStickerRequest request = new SearchStickerRequest();
        request.setAccessKey(getValidAccessKey());
        // 不设置必填的zjKeyword
        
        String requestJson = JSONObject.toJSONString(request);
        log.info("测试SearchStickerRequest缺少必填参数: {}", requestJson);
        
        mockMvc.perform(MockMvcRequestBuilders.post("/api/jianying/search_sticker")
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestJson))
                .andDo(print())
                .andExpect(MockMvcResultMatchers.status().isBadRequest())
                .andExpect(MockMvcResultMatchers.jsonPath("$.success").value(false))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(400));
    }
    
    /**
     * 测试SearchStickerRequest有效参数
     */
    @Test
    public void testSearchStickerRequestValid() throws Exception {
        SearchStickerRequest request = new SearchStickerRequest();
        request.setAccessKey(getValidAccessKey());
        request.setZjKeyword("爱心");
        
        String requestJson = JSONObject.toJSONString(request);
        log.info("测试SearchStickerRequest有效参数: {}", requestJson);
        
        mockMvc.perform(MockMvcRequestBuilders.post("/api/jianying/search_sticker")
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestJson))
                .andDo(print())
                .andExpect(MockMvcResultMatchers.status().isOk());
    }
    
    /**
     * 测试TimelinesRequest参数验证
     */
    @Test
    public void testTimelinesRequestValidation() throws Exception {
        // 测试缺少必填参数
        TimelinesRequest request = new TimelinesRequest();
        request.setAccessKey(getValidAccessKey());
        // 不设置必填的zjDuration和zjNum
        
        String requestJson = JSONObject.toJSONString(request);
        log.info("测试TimelinesRequest缺少必填参数: {}", requestJson);
        
        mockMvc.perform(MockMvcRequestBuilders.post("/api/jianying/timelines")
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestJson))
                .andDo(print())
                .andExpect(MockMvcResultMatchers.status().isBadRequest())
                .andExpect(MockMvcResultMatchers.jsonPath("$.success").value(false))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(400));
    }
    
    /**
     * 测试TimelinesRequest有效参数
     */
    @Test
    public void testTimelinesRequestValid() throws Exception {
        TimelinesRequest request = new TimelinesRequest();
        request.setAccessKey(getValidAccessKey());
        request.setZjDuration(10000000L);
        request.setZjNum(5);
        
        String requestJson = JSONObject.toJSONString(request);
        log.info("测试TimelinesRequest有效参数: {}", requestJson);
        
        mockMvc.perform(MockMvcRequestBuilders.post("/api/jianying/timelines")
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestJson))
                .andDo(print())
                .andExpect(MockMvcResultMatchers.status().isOk());
    }

    /**
     * 测试AddVideosRequest参数验证 - 缺少必填参数
     */
    @Test
    public void testAddVideosRequestMissingRequired() throws Exception {
        AddVideosRequest request = new AddVideosRequest();
        request.setAccessKey(getValidAccessKey());
        // 不设置必填的zjVideoInfos和zjDraftUrl

        String requestJson = JSONObject.toJSONString(request);
        log.info("测试AddVideosRequest缺少必填参数: {}", requestJson);

        mockMvc.perform(MockMvcRequestBuilders.post("/api/jianying/add_videos")
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestJson))
                .andDo(print())
                .andExpect(MockMvcResultMatchers.status().isBadRequest())
                .andExpect(MockMvcResultMatchers.jsonPath("$.success").value(false))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(400));
    }

    /**
     * 测试AddVideosRequest参数验证 - 空字符串参数
     */
    @Test
    public void testAddVideosRequestEmptyString() throws Exception {
        AddVideosRequest request = new AddVideosRequest();
        request.setAccessKey(getValidAccessKey());
        request.setZjVideoInfos(""); // 空字符串
        request.setZjDraftUrl("https://example.com/draft/123");

        String requestJson = JSONObject.toJSONString(request);
        log.info("测试AddVideosRequest空字符串参数: {}", requestJson);

        mockMvc.perform(MockMvcRequestBuilders.post("/api/jianying/add_videos")
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestJson))
                .andDo(print())
                .andExpect(MockMvcResultMatchers.status().isBadRequest())
                .andExpect(MockMvcResultMatchers.jsonPath("$.success").value(false))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(400));
    }

    /**
     * 测试AddVideosRequest参数验证 - 样式参数边界值
     */
    @Test
    public void testAddVideosRequestStyleParameterBounds() throws Exception {
        // 测试透明度超出范围
        AddVideosRequest request = new AddVideosRequest();
        request.setAccessKey(getValidAccessKey());
        request.setZjVideoInfos("[{\"video_url\": \"http://example.com/video1.mp4\",\"duration\":10000000,\"start\":0,\"end\":10000000}]");
        request.setZjDraftUrl("https://example.com/draft/123");
        request.setZjAlpha(1.5); // 超出0-1范围

        String requestJson = JSONObject.toJSONString(request);
        log.info("测试AddVideosRequest透明度超出范围: {}", requestJson);

        mockMvc.perform(MockMvcRequestBuilders.post("/api/jianying/add_videos")
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestJson))
                .andDo(print())
                .andExpect(MockMvcResultMatchers.status().isBadRequest())
                .andExpect(MockMvcResultMatchers.jsonPath("$.success").value(false))
                .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(400));
    }

    /**
     * 测试AddVideosRequest有效参数
     */
    @Test
    public void testAddVideosRequestValid() throws Exception {
        AddVideosRequest request = new AddVideosRequest();
        request.setAccessKey(getValidAccessKey());
        request.setZjVideoInfos("[{\"video_url\": \"http://example.com/video1.mp4\",\"duration\":10000000,\"start\":0,\"end\":10000000}]");
        request.setZjDraftUrl("https://example.com/draft/123");
        request.setZjAlpha(0.8);
        request.setZjScaleX(1.2);
        request.setZjScaleY(1.2);
        request.setZjTransformX(100.0);
        request.setZjTransformY(-50.0);

        String requestJson = JSONObject.toJSONString(request);
        log.info("测试AddVideosRequest有效参数: {}", requestJson);

        mockMvc.perform(MockMvcRequestBuilders.post("/api/jianying/add_videos")
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestJson))
                .andDo(print())
                .andExpect(MockMvcResultMatchers.status().isOk());
    }

    /**
     * 测试AddVideosRequest JSON格式错误
     */
    @Test
    public void testAddVideosRequestInvalidJson() throws Exception {
        AddVideosRequest request = new AddVideosRequest();
        request.setAccessKey(getValidAccessKey());
        request.setZjVideoInfos("invalid json format"); // 无效JSON格式
        request.setZjDraftUrl("https://example.com/draft/123");

        String requestJson = JSONObject.toJSONString(request);
        log.info("测试AddVideosRequest无效JSON格式: {}", requestJson);

        mockMvc.perform(MockMvcRequestBuilders.post("/api/jianying/add_videos")
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestJson))
                .andDo(print())
                .andExpect(MockMvcResultMatchers.status().isInternalServerError());
    }
}
