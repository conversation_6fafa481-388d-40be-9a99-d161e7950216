{"openapi": "3.0.0", "info": {"title": "剪映小助手_超级剪映小助手 - 批量添加特效", "description": "批量添加特效", "version": "1.0.0"}, "servers": [{"url": "https://www.aigcview.com"}], "paths": {"/jeecg-boot/api/jianyingpro/add_effects": {"post": {"summary": "批量添加特效", "description": "批量添加特效", "operationId": "add_effects_pro", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"access_key": {"type": "string", "description": "访问密钥（必填）", "example": "JianyingAPI_2025_SecureAccess_AigcView", "default": "JianyingAPI_2025_SecureAccess_AigcView"}, "draft_url": {"type": "string", "description": "草稿地址，使用create_draft输出的draft_url即可（必填）", "example": "https://example.com/draft/123"}, "effects": {"type": "array", "description": "特效名字（必填，例如：金粉飘落）", "items": {"type": "string"}, "example": ["金粉飘落"]}, "timelines": {"type": "array", "description": "时间节点，只接收结构：[{\"start\":0,\"end\":4612}]，一般从audio_timeline节点的输出获取（必填）", "items": {"type": "object", "properties": {"start": {"type": "integer"}, "end": {"type": "integer"}}}, "example": [{"start": 0, "end": 4612000}]}}, "required": ["access_key", "draft_url", "effects", "timelines"], "additionalProperties": false}}}}, "responses": {"200": {"description": "成功添加特效", "content": {"application/json": {"schema": {"type": "object", "properties": {"draft_url": {"type": "string", "description": "更新后的草稿地址"}, "message": {"type": "string", "description": "导入指南信息", "example": "不知道导入的请看：https://www.aigcview.com/JianYingDraft?draft=https://example.com/draft.json"}}, "required": ["draft_url", "message"]}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "description": "错误信息", "example": "参数不完整: 草稿地址不能为空"}, "error_code": {"type": "string", "description": "错误码", "example": "PARAM_INCOMPLETE_003"}, "error_message": {"type": "string", "description": "详细错误消息", "example": "参数不完整"}, "error_details": {"type": "string", "description": "错误解决方案", "example": "请提供有效的draft_url参数"}}, "required": ["error", "error_code"]}}}}, "500": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "description": "错误信息", "example": "批量添加特效失败: 服务器内部错误"}}, "required": ["error"]}}}}}}}}}