package org.jeecg.modules.api.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import lombok.Data;

/**
 * @Description: 智界Aigc API配置类
 * @Author: jeecg-boot
 * @Date: 2025-06-14
 * @Version: V1.0
 */
@Data
@Component
@ConfigurationProperties(prefix = "aigc.api")
public class AigcApiConfig {


    
    /**
     * HTML安全配置
     */
    private HtmlSecurity htmlSecurity = new HtmlSecurity();
    
    /**
     * 文件存储配置
     */
    private FileStorage fileStorage = new FileStorage();



    @Data
    public static class HtmlSecurity {
        /**
         * HTML内容最大大小（字节）
         */
        private long maxContentSize = 2 * 1024 * 1024; // 2MB
        
        /**
         * 是否允许外部资源
         */
        private boolean allowExternalResources = false;
        
        /**
         * 是否启用安全检查
         */
        private boolean enabled = true;
        
        /**
         * 允许的HTML标签（白名单）
         */
        private String[] allowedTags = {
            "html", "head", "title", "body", "div", "span", "p", "h1", "h2", "h3", "h4", "h5", "h6",
            "ul", "ol", "li", "table", "tr", "td", "th", "thead", "tbody", "tfoot",
            "img", "a", "strong", "em", "b", "i", "u", "br", "hr", "pre", "code",
            "blockquote", "cite", "small", "sub", "sup", "mark", "del", "ins"
        };
        
        /**
         * 允许的HTML属性（白名单）
         */
        private String[] allowedAttributes = {
            "id", "class", "style", "title", "alt", "src", "href", "target",
            "width", "height", "border", "cellpadding", "cellspacing",
            "colspan", "rowspan", "align", "valign"
        };
    }

    @Data
    public static class FileStorage {
        /**
         * HTML文件存储路径
         */
        private String htmlPath = "C:/aigcview/html/";

        /**
         * 二维码文件存储路径
         */
        private String qrcodePath = "C:/aigcview/qrcode/";
        
        /**
         * 文件保留天数
         */
        private int retentionDays = 30;
        
        /**
         * 是否启用文件清理
         */
        private boolean enableCleanup = true;
        
        /**
         * 最大文件数量
         */
        private int maxFileCount = 100000;
    }

}
