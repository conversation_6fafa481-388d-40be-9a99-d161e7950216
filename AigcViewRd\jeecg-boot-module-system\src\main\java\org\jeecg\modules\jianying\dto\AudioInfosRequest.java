package org.jeecg.modules.jianying.dto;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 音频数据生成器请求参数
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AudioInfosRequest extends BaseJianyingRequest {
    
    @ApiModelProperty(value = "音频列表（必填）", required = true,
                     example = "[\"https://example.com/audio1.mp3\", \"https://example.com/audio2.mp3\"]")
    @NotEmpty(message = "mp3_urls不能为空")
    @JsonProperty("mp3_urls")
    private List<String> zjMp3Urls;

    @ApiModelProperty(value = "时间线（必填）", required = true,
                     example = "[{\"start\": 0, \"end\": 4612000}]")
    @NotEmpty(message = "timelines不能为空")
    @JsonProperty("timelines")
    private List<JSONObject> zjTimelines;

    @ApiModelProperty(value = "特效音，eg：教堂，默认无", example = "教堂")
    @JsonProperty("audio_effect")
    private String zjAudioEffect;

    @ApiModelProperty(value = "音量大小，0-10，默认1", example = "1.0")
    @JsonProperty("volume")
    private Double zjVolume;
    
    @Override
    public String getSummary() {
        return "AudioInfosRequest{" +
               "accessKey=" + (getAccessKey() != null && getAccessKey().length() > 10 ? 
                              getAccessKey().substring(0, 10) + "***" : "null") +
               ", mp3Count=" + (zjMp3Urls != null ? zjMp3Urls.size() : 0) +
               ", timelinesCount=" + (zjTimelines != null ? zjTimelines.size() : 0) +
               ", effect=" + zjAudioEffect +
               ", volume=" + zjVolume +
               "}";
    }
}
