package org.jeecg.modules.api.service;

import org.jeecg.modules.api.config.UserActivityConfig;
import org.jeecg.modules.api.service.impl.UserActivityPerformanceMonitorImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Map;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * @Description: 用户活跃状态性能监控服务测试
 * @Author: AigcView
 * @Date: 2025-01-30
 * @Version: V1.0
 */
@ExtendWith(MockitoExtension.class)
public class UserActivityPerformanceMonitorTest {

    @Mock
    private UserActivityConfig userActivityConfig;

    @InjectMocks
    private UserActivityPerformanceMonitorImpl performanceMonitor;

    @BeforeEach
    void setUp() {
        // 设置默认配置
        when(userActivityConfig.getPerformanceThreshold()).thenReturn(1000L);
        when(userActivityConfig.getEnablePerformanceMonitoring()).thenReturn(true);
        when(userActivityConfig.isCriticalApi(anyString())).thenReturn(false);
        
        // 初始化监控服务
        performanceMonitor.init();
    }

    @Test
    void testRecordApiPerformance() {
        // 测试记录API性能数据
        String apiPath = "/api/test";
        long responseTime = 500L;
        boolean isCriticalApi = false;
        boolean success = true;

        // 记录性能数据
        performanceMonitor.recordApiPerformance(apiPath, responseTime, isCriticalApi, success);

        // 获取API性能统计
        Map<String, Object> stats = performanceMonitor.getApiPerformanceStats(apiPath);
        
        assertNotNull(stats);
        assertEquals(1L, stats.get("totalCalls"));
        assertEquals(1L, stats.get("successCalls"));
        assertEquals(0L, stats.get("errorCalls"));
        assertEquals(100.0, stats.get("successRate"));
        assertEquals(500.0, stats.get("avgResponseTime"));
        assertEquals(500L, stats.get("maxResponseTime"));
        assertEquals(500L, stats.get("minResponseTime"));
    }

    @Test
    void testMultipleApiCalls() {
        // 测试多次API调用的统计
        String apiPath = "/api/test";
        
        // 记录多次调用
        performanceMonitor.recordApiPerformance(apiPath, 300L, false, true);
        performanceMonitor.recordApiPerformance(apiPath, 700L, false, true);
        performanceMonitor.recordApiPerformance(apiPath, 500L, false, false); // 失败的调用

        // 获取统计数据
        Map<String, Object> stats = performanceMonitor.getApiPerformanceStats(apiPath);
        
        assertEquals(3L, stats.get("totalCalls"));
        assertEquals(2L, stats.get("successCalls"));
        assertEquals(1L, stats.get("errorCalls"));
        assertEquals(66.67, stats.get("successRate"));
        assertEquals(33.33, stats.get("errorRate"));
        assertEquals(500.0, stats.get("avgResponseTime"));
        assertEquals(700L, stats.get("maxResponseTime"));
        assertEquals(300L, stats.get("minResponseTime"));
    }

    @Test
    void testSystemPerformanceStats() {
        // 测试系统整体性能统计
        performanceMonitor.recordApiPerformance("/api/test1", 400L, false, true);
        performanceMonitor.recordApiPerformance("/api/test2", 600L, true, true);
        performanceMonitor.recordApiPerformance("/api/test3", 800L, false, false);

        Map<String, Object> systemStats = performanceMonitor.getSystemPerformanceStats();
        
        assertNotNull(systemStats);
        assertEquals(3L, systemStats.get("totalCalls"));
        assertEquals(2L, systemStats.get("successCalls"));
        assertEquals(1L, systemStats.get("errorCalls"));
        assertEquals(1L, systemStats.get("criticalCalls"));
        assertEquals(66.67, systemStats.get("successRate"));
        assertEquals(33.33, systemStats.get("criticalRate"));
        assertEquals(600.0, systemStats.get("avgResponseTime"));
    }

    @Test
    void testPerformanceThresholdExceeded() {
        // 测试性能阈值检查
        String apiPath = "/api/slow";
        long slowResponseTime = 1500L; // 超过阈值1000ms
        
        boolean exceeded = performanceMonitor.isPerformanceThresholdExceeded(apiPath, slowResponseTime);
        assertTrue(exceeded);
        
        long fastResponseTime = 500L; // 未超过阈值
        boolean notExceeded = performanceMonitor.isPerformanceThresholdExceeded(apiPath, fastResponseTime);
        assertFalse(notExceeded);
    }

    @Test
    void testCriticalApiThreshold() {
        // 测试关键API的特殊阈值
        String criticalApiPath = "/api/critical";
        when(userActivityConfig.isCriticalApi(criticalApiPath)).thenReturn(true);
        
        long responseTime = 600L; // 超过关键API阈值(500ms)但未超过普通阈值(1000ms)
        boolean exceeded = performanceMonitor.isPerformanceThresholdExceeded(criticalApiPath, responseTime);
        assertTrue(exceeded); // 关键API阈值更严格
    }

    @Test
    void testManualDegradation() {
        // 测试手动降级
        assertFalse(performanceMonitor.isDegraded());
        
        String reason = "系统维护";
        int durationMinutes = 10;
        
        boolean triggered = performanceMonitor.triggerDegradation(reason, durationMinutes);
        assertTrue(triggered);
        assertTrue(performanceMonitor.isDegraded());
        
        Map<String, Object> status = performanceMonitor.getDegradationStatus();
        assertEquals(true, status.get("degraded"));
        assertEquals(reason, status.get("degradationReason"));
    }

    @Test
    void testManualRecovery() {
        // 测试手动恢复
        performanceMonitor.triggerDegradation("测试降级", 10);
        assertTrue(performanceMonitor.isDegraded());
        
        String recoveryReason = "维护完成";
        boolean recovered = performanceMonitor.recoverFromDegradation(recoveryReason);
        assertTrue(recovered);
        assertFalse(performanceMonitor.isDegraded());
    }

    @Test
    void testShouldDegradeApi() {
        // 测试API降级判断
        String normalApi = "/api/normal";
        String criticalApi = "/api/critical";
        
        when(userActivityConfig.isCriticalApi(criticalApi)).thenReturn(true);
        
        // 正常情况下不降级
        assertFalse(performanceMonitor.shouldDegradeApi(normalApi, false));
        assertFalse(performanceMonitor.shouldDegradeApi(criticalApi, true));
        
        // 手动降级后
        performanceMonitor.triggerDegradation("测试", 10);
        
        // 普通API降级，关键API不降级
        assertTrue(performanceMonitor.shouldDegradeApi(normalApi, false));
        assertFalse(performanceMonitor.shouldDegradeApi(criticalApi, true));
    }

    @Test
    void testSystemLoadMonitoring() {
        // 测试系统负载监控
        Map<String, Object> systemLoad = performanceMonitor.getSystemLoad();
        
        assertNotNull(systemLoad);
        assertTrue(systemLoad.containsKey("cpuUsage"));
        assertTrue(systemLoad.containsKey("availableProcessors"));
        assertTrue(systemLoad.containsKey("timestamp"));
    }

    @Test
    void testJvmMemoryMonitoring() {
        // 测试JVM内存监控
        Map<String, Object> memoryInfo = performanceMonitor.getJvmMemoryInfo();
        
        assertNotNull(memoryInfo);
        assertTrue(memoryInfo.containsKey("heapUsed"));
        assertTrue(memoryInfo.containsKey("heapMax"));
        assertTrue(memoryInfo.containsKey("heapUsagePercentage"));
        assertTrue(memoryInfo.containsKey("memoryLevel"));
    }

    @Test
    void testThreadPoolMonitoring() {
        // 测试线程池监控
        Map<String, Object> threadPool = performanceMonitor.getThreadPoolStatus();
        
        assertNotNull(threadPool);
        assertTrue(threadPool.containsKey("threadCount"));
        assertTrue(threadPool.containsKey("peakThreadCount"));
        assertTrue(threadPool.containsKey("threadLevel"));
    }

    @Test
    void testPerformanceAlerts() {
        // 测试性能告警
        String apiPath = "/api/slow";
        long slowResponseTime = 1500L;
        
        // 记录超过阈值的API调用
        performanceMonitor.recordApiPerformance(apiPath, slowResponseTime, false, true);
        
        // 获取告警列表
        List<Map<String, Object>> alerts = performanceMonitor.getPerformanceAlerts();
        
        assertNotNull(alerts);
        assertFalse(alerts.isEmpty());
        
        Map<String, Object> alert = alerts.get(0);
        assertEquals("PERFORMANCE_THRESHOLD_EXCEEDED", alert.get("type"));
        assertEquals(apiPath, alert.get("apiPath"));
        assertEquals(slowResponseTime, alert.get("responseTime"));
    }

    @Test
    void testResetPerformanceStats() {
        // 测试重置性能统计
        performanceMonitor.recordApiPerformance("/api/test", 500L, false, true);
        
        Map<String, Object> statsBefore = performanceMonitor.getSystemPerformanceStats();
        assertEquals(1L, statsBefore.get("totalCalls"));
        
        boolean reset = performanceMonitor.resetPerformanceStats();
        assertTrue(reset);
        
        Map<String, Object> statsAfter = performanceMonitor.getSystemPerformanceStats();
        assertEquals(0L, statsAfter.get("totalCalls"));
    }

    @Test
    void testSystemHealthCheck() {
        // 测试系统健康检查
        Map<String, Object> health = performanceMonitor.checkSystemHealth();
        
        assertNotNull(health);
        assertTrue(health.containsKey("systemLoad"));
        assertTrue(health.containsKey("jvmMemory"));
        assertTrue(health.containsKey("threadPool"));
        assertTrue(health.containsKey("degradationStatus"));
    }
}
