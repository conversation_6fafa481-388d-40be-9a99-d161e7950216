# 剪映小助手Pro版 - 快速上手指南

## 🚀 5分钟快速上手

### ✨ Pro版核心优势
- **8个合并接口**：将原来16个接口合并为8个智能接口
- **一体化操作**：从两步操作简化为一步操作
- **智能参数识别**：系统自动识别参数类型并选择处理模式
- **操作效率提升100%**：大幅简化用户操作流程

### 1. 启动后端服务

```bash
# 进入项目目录
cd AigcViewRd

# 启动开发服务
mvn spring-boot:run -Dspring-boot.run.profiles=dev
```

### 2. 验证服务启动

访问：http://localhost:8080/jeecg-boot/doc.html

### 3. 测试API接口

**8个核心接口路径**：
- `POST /api/jianyingpro/add_audios` - 一体化音频添加
- `POST /api/jianyingpro/add_videos` - 一体化视频添加
- `POST /api/jianyingpro/add_images` - 一体化图片添加
- `POST /api/jianyingpro/add_captions` - 一体化字幕添加
- `POST /api/jianyingpro/add_effects` - 一体化特效添加
- `POST /api/jianyingpro/add_keyframes` - 一体化关键帧添加
- `POST /api/jianyingpro/generate_timelines` - 智能时间线生成
- `POST /api/jianyingpro/data_conversion` - 智能数据转换

**最简单的测试（智能数据转换）**：
```json
{
  "access_key": "JianyingAPI_2025_SecureAccess_AigcView",
  "input_string": "苹果,香蕉,橙子"
}
```

**预期返回**：
```json
{
  "success": true,
  "message": "智能数据转换完成，共执行 1 种转换",
  "conversion_count": 1,
  "data": {
    "string_to_list": {
      "list": ["苹果", "香蕉", "橙子"],
      "count": 3,
      "delimiter_used": ","
    }
  }
}
```

## 📝 8个接口测试用例

### 用例1：一体化音频添加
```json
{
  "access_key": "JianyingAPI_2025_SecureAccess_AigcView",
  "draft_url": "草稿地址",
  "mp3_urls": ["音频URL1", "音频URL2"]
}
```

### 用例2：一体化视频添加
```json
{
  "access_key": "JianyingAPI_2025_SecureAccess_AigcView",
  "draft_url": "草稿地址",
  "mp4_urls": ["视频URL1", "视频URL2"]
}
```

### 用例3：一体化图片添加
```json
{
  "access_key": "JianyingAPI_2025_SecureAccess_AigcView",
  "draft_url": "草稿地址",
  "image_urls": ["图片URL1", "图片URL2"]
}
```

### 用例4：智能时间线生成（音频模式）
```json
{
  "access_key": "JianyingAPI_2025_SecureAccess_AigcView",
  "audio_urls": ["音频URL1", "音频URL2"]
}
```

### 用例5：智能时间线生成（自定义模式）
```json
{
  "access_key": "JianyingAPI_2025_SecureAccess_AigcView",
  "duration": 10000000,
  "num": 5
}
```

### 用例6：智能数据转换（多重转换）
```json
{
  "access_key": "JianyingAPI_2025_SecureAccess_AigcView",
  "input_string": "苹果,香蕉",
  "input_string_list": ["橙子", "葡萄"],
  "input_object_list": [{"output": "西瓜"}]
}
```

## 🔧 Coze插件配置

### 1. 上传配置文件

文件位置：`剪映小助手-新版/coze-plugins/超级剪映小助手/data_conversion_pro.json`

### 2. 配置服务器

- **开发环境**：`https://www.aigcview.com`
- **本地测试**：`http://localhost:8080`

### 3. 测试插件

在Coze平台中测试插件功能，确保能正常调用API。

## ⚡ 快速调试

### 查看日志
```bash
# 查看实时日志
tail -f logs/jeecg-boot.log

# 搜索特定日志
grep "智能数据转换" logs/jeecg-boot.log
```

### 常见错误

1. **端口被占用**：
   ```bash
   # 查找占用8080端口的进程
   netstat -ano | findstr :8080
   
   # 杀掉进程
   taskkill /PID [进程ID] /F
   ```

2. **参数错误**：
   - 检查`access_key`是否正确
   - 确保至少提供一种输入数据

3. **返回空对象**：
   - 检查输入数据格式
   - 查看后端日志排查问题

## 📋 开发检查清单

### 后端开发
- [ ] 服务正常启动
- [ ] API接口可访问
- [ ] 参数验证正常
- [ ] 转换逻辑正确
- [ ] 错误处理完善
- [ ] 日志记录详细

### 前端集成
- [ ] Coze插件配置正确
- [ ] 参数映射一致
- [ ] 返回结果解析正常
- [ ] 错误处理友好

### 测试验证
- [ ] 单一转换测试通过
- [ ] 多重转换测试通过
- [ ] 边界情况测试通过
- [ ] 性能测试满足要求

## 🎯 下一步

1. **阅读详细文档**：`README_新版开发文档.md`
2. **了解架构设计**：查看代码结构和设计模式
3. **扩展功能**：根据需求添加新的转换类型
4. **性能优化**：针对大数据量场景进行优化

## 💡 小贴士

- **开发环境**：建议使用IntelliJ IDEA或Eclipse
- **API测试**：推荐使用Postman或curl
- **日志查看**：使用tail命令实时查看日志
- **代码格式**：保持代码风格一致，添加必要注释

---

*快速上手指南 - 让你5分钟内掌握剪映小助手Pro版*
