# 邀请奖励触发机制使用说明

## 📋 概述

`ReferralRewardTriggerService` 是一个通用的邀请奖励触发服务，专门处理各种业务场景下的邀请奖励计算和发放。

## 🎯 核心功能

### 1. 会员订阅奖励触发
当用户订阅会员成功时，自动检查邀请关系并发放佣金奖励。

### 2. 自动佣金计算
根据邀请人的会员等级和邀请人数，自动计算正确的佣金比例。

### 3. 完整的奖励流程
- 创建奖励记录
- 更新邀请人佣金余额
- 更新邀请关系状态
- 完整的日志记录

## 🔧 使用方法

### 在支付宝支付回调中使用

```java
@Autowired
private ReferralRewardTriggerService rewardTriggerService;

// 在支付成功处理逻辑中添加
if ("membership".equals(orderType)) {
    // 会员订阅支付成功，触发邀请奖励
    try {
        boolean success = rewardTriggerService.triggerMembershipSubscriptionReward(
            userId,           // 订阅用户ID
            orderAmount,      // 订阅金额
            membershipLevel,  // 会员等级
            outTradeNo        // 订单ID
        );
        
        if (success) {
            log.info("✅ 邀请奖励触发成功 - 用户: {}, 金额: {}", userId, orderAmount);
        }
    } catch (Exception e) {
        log.error("❌ 邀请奖励触发失败 - 用户: {}, 金额: {}", userId, orderAmount, e);
        // 注意：奖励失败不应该影响主业务流程
    }
}
```

### 在会员升级接口中使用

```java
@Autowired
private ReferralRewardTriggerService rewardTriggerService;

@PostMapping("/upgradeMembership")
public Result<?> upgradeMembership(@RequestBody Map<String, Object> params) {
    try {
        // ... 现有的会员升级逻辑 ...
        
        // 升级成功后触发邀请奖励
        String userId = getCurrentUserId();
        BigDecimal amount = new BigDecimal(params.get("amount").toString());
        Integer targetLevel = (Integer) params.get("targetLevel");
        String orderId = generateOrderId();
        
        // 触发邀请奖励
        rewardTriggerService.triggerMembershipSubscriptionReward(
            userId, amount, targetLevel, orderId
        );
        
        return Result.OK("会员升级成功");
    } catch (Exception e) {
        log.error("会员升级失败", e);
        return Result.error("升级失败");
    }
}
```

## 📊 奖励计算逻辑

### 佣金比例计算规则

根据 `aicg_commission_level_config` 表的配置：

| 用户等级 | 邀请人数要求 | 奖励比例 | 说明 |
|---------|-------------|---------|------|
| 普通用户 | 0-9人 | 30% | 新手邀请员 |
| 普通用户 | 10-29人 | 40% | 高级邀请员 |
| 普通用户 | 30人以上 | 50% | 顶级邀请员 |
| VIP用户 | 0-9人 | 35% | VIP新手邀请员 |
| VIP用户 | 10-29人 | 45% | VIP高级邀请员 |
| VIP用户 | 30人以上 | 50% | VIP顶级邀请员 |
| SVIP用户 | 无要求 | 50% | SVIP邀请员 |

### 奖励计算公式

```
奖励金额 = 订阅金额 × 佣金比例
```

**示例：**
- VIP用户（邀请了15人）的被邀请人订阅99元会员
- 佣金比例：45%
- 奖励金额：99 × 0.45 = 44.55元

## 🔄 完整流程

### 1. 触发检查
```
用户订阅会员成功 → 检查是否有邀请关系
```

### 2. 佣金计算
```
查询邀请人会员等级 → 查询邀请人数 → 确定佣金比例
```

### 3. 奖励发放
```
计算奖励金额 → 创建奖励记录 → 更新邀请人余额
```

### 4. 状态更新
```
更新邀请关系状态 → 记录首次消费信息
```

## 🛡️ 安全特性

### 1. 事务保护
所有奖励操作都在事务中执行，确保数据一致性。

### 2. 异常处理
奖励失败不会影响主业务流程，只记录错误日志。

### 3. 重复检查
避免重复发放奖励，确保数据准确性。

### 4. 详细日志
完整的操作日志，便于问题排查和数据审计。

## 📝 日志示例

```
🎯 开始处理会员订阅奖励触发 - 用户: user123, 金额: 99.00, 等级: 2
🔗 找到邀请关系 - 邀请人: referrer456, 被邀请人: user123
📊 邀请人 referrer456 佣金配置 - 类型: VIP, 邀请数: 15, 佣金比例: 45.00%
💰 计算奖励 - 订阅金额: 99.00, 佣金比例: 45%, 奖励金额: 44.55
💰 更新邀请人 referrer456 佣金余额成功 - 增加金额: 44.55
✅ 会员订阅奖励处理完成 - 奖励ID: reward789, 金额: 44.55
```

## 🚀 扩展性

### 预留接口
服务中预留了其他业务场景的奖励接口：

- `triggerRechargeReward()` - 充值奖励
- `triggerPluginPurchaseReward()` - 插件购买奖励

### 配置化
所有佣金比例都通过数据库配置，可以灵活调整。

### 模块化
独立的服务类，可以轻松集成到任何业务场景中。

## ⚠️ 注意事项

1. **确保数据库字段存在**：需要先执行佣金功能的数据库升级脚本
2. **异常处理**：奖励触发失败不应该影响主业务流程
3. **性能考虑**：大量订阅时可以考虑异步处理奖励
4. **数据一致性**：所有奖励操作都在事务中执行

## 🔧 集成步骤

1. **注入服务**
```java
@Autowired
private ReferralRewardTriggerService rewardTriggerService;
```

2. **在支付成功回调中调用**
```java
rewardTriggerService.triggerMembershipSubscriptionReward(userId, amount, level, orderId);
```

3. **处理异常**
```java
try {
    // 触发奖励
} catch (Exception e) {
    log.error("奖励触发失败", e);
    // 不影响主流程
}
```

这样设计的好处是：
- **解耦**：奖励逻辑独立，不影响主业务
- **复用**：一个服务处理所有奖励场景
- **可维护**：集中管理奖励规则和计算逻辑
- **可扩展**：轻松添加新的奖励类型
