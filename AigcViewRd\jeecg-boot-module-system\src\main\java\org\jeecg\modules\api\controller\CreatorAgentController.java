package org.jeecg.modules.api.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.api.dto.CreatorAgentDTO;
import org.jeecg.modules.api.vo.CreatorAgentVO;
import org.jeecg.modules.api.vo.CreatorRevenueStatsVO;
import org.jeecg.modules.demo.aigc_agent.entity.AigcAgent;
import org.jeecg.modules.demo.aigc_agent.entity.AigcWorkflow;
import org.jeecg.modules.demo.aigc_agent.service.IAigcAgentService;
import org.jeecg.modules.demo.aigc_agent.service.IAigcWorkflowService;
import org.jeecg.modules.system.service.ISysDictService;
import org.jeecg.modules.jianyingpro.service.internal.JianyingProTosService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.math.BigDecimal;
import java.security.SecureRandom;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 创作者智能体管理控制器
 * @Author: 智界AIGC
 * @Date: 2025-08-04
 * @Version: V1.0
 */
@Api(tags = "创作者中心-智能体管理")
@RestController
@RequestMapping("/api/creator/agent")
@Slf4j
public class CreatorAgentController {

    @Autowired
    private IAigcAgentService aigcAgentService;

    @Autowired
    private IAigcWorkflowService aigcWorkflowService;

    @Autowired
    private ISysDictService sysDictService;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private JianyingProTosService tosService;

    /**
     * 获取创作者智能体列表
     */
    @AutoLog(value = "创作者智能体-列表查询")
    @ApiOperation(value = "创作者智能体-列表查询", notes = "获取当前创作者的智能体列表")
    @GetMapping(value = "/list")
    public Result<?> queryPageList(
            @ApiParam(value = "页码", defaultValue = "1") @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
            @ApiParam(value = "页大小", defaultValue = "10") @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
            @ApiParam(value = "智能体名称") @RequestParam(name = "agentName", required = false) String agentName,
            @ApiParam(value = "审核状态") @RequestParam(name = "auditStatus", required = false) String auditStatus,
            @ApiParam(value = "排序字段", allowableValues = "totalRevenue,salesCount,createTime") @RequestParam(name = "sortField", required = false, defaultValue = "totalRevenue") String sortField,
            @ApiParam(value = "排序方向", allowableValues = "asc,desc") @RequestParam(name = "sortOrder", required = false, defaultValue = "desc") String sortOrder,
            HttpServletRequest request) {
        
        try {
            // 获取当前登录用户
            LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            if (loginUser == null) {
                return Result.error("用户未登录");
            }
            
            log.info("🔍 创作者智能体列表查询 - 用户: {}, 页码: {}, 页大小: {}, 排序: {}({})",
                loginUser.getUsername(), pageNo, pageSize, sortField, sortOrder);

            // 构建查询条件
            QueryWrapper<AigcAgent> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("create_by", loginUser.getUsername()); // 只查询当前用户创建的智能体
            // 注释掉author_type限制，允许查询所有类型的智能体
            // queryWrapper.eq("author_type", "2"); // 只查询创作者类型的智能体
            
            // 按智能体名称模糊查询
            if (oConvertUtils.isNotEmpty(agentName)) {
                queryWrapper.like("agent_name", agentName);
            }
            
            // 按审核状态查询
            if (oConvertUtils.isNotEmpty(auditStatus)) {
                queryWrapper.eq("audit_status", auditStatus);
            }
            
            // 🔥 根据排序字段设置排序规则
            if ("createTime".equals(sortField)) {
                // 按创建时间排序
                if ("asc".equals(sortOrder)) {
                    queryWrapper.orderByAsc("create_time");
                } else {
                    queryWrapper.orderByDesc("create_time");
                }
            } else {
                // 对于收益和销售次数排序，先按创建时间排序，后续在内存中重新排序
                queryWrapper.orderByDesc("create_time");
            }

            // 分页查询
            Page<AigcAgent> page = new Page<>(pageNo, pageSize);
            IPage<AigcAgent> pageList = aigcAgentService.page(page, queryWrapper);

            // 转换为VO对象
            List<CreatorAgentVO> voList = pageList.getRecords().stream().map(agent -> {
                CreatorAgentVO vo = new CreatorAgentVO();
                BeanUtils.copyProperties(agent, vo);

                // 处理头像URL，添加CDN前缀
                if (agent.getAgentAvatar() != null && !agent.getAgentAvatar().isEmpty()) {
                    vo.setAgentAvatar(tosService.generateFileUrl(agent.getAgentAvatar()));
                }

                // 设置字典文本
                vo.setAuthorTypeText(getDictText("author_type", agent.getAuthorType()));
                vo.setAuditStatusText(getDictText("audit_status", agent.getAuditStatus()));
                
                // 设置操作权限（待审核和审核拒绝状态可编辑和删除）
                boolean isPending = "1".equals(agent.getAuditStatus());
                boolean isRejected = "3".equals(agent.getAuditStatus());
                boolean canEdit = isPending || isRejected;
                vo.setEditable(canEdit);
                vo.setDeletable(canEdit);
                
                // 查询工作流数量
                QueryWrapper<AigcWorkflow> workflowQuery = new QueryWrapper<>();
                workflowQuery.eq("agent_id", agent.getId());
                int workflowCount = (int) aigcWorkflowService.count(workflowQuery);
                vo.setWorkflowCount(workflowCount);
                
                // 如果是已通过状态，查询收益信息
                if ("2".equals(agent.getAuditStatus())) {
                    // 查询真实的收益信息
                    Map<String, Object> revenueData = queryAgentRevenueData(agent.getId());
                    vo.setTotalRevenue((BigDecimal) revenueData.get("totalRevenue"));
                    vo.setMonthRevenue((BigDecimal) revenueData.get("monthRevenue"));
                    vo.setSalesCount((Integer) revenueData.get("salesCount"));
                }
                
                return vo;
            }).collect(Collectors.toList());

            // 🔥 对收益和销售次数进行内存排序
            if ("totalRevenue".equals(sortField) || "salesCount".equals(sortField)) {
                voList.sort((a, b) -> {
                    int compareResult;

                    if ("totalRevenue".equals(sortField)) {
                        BigDecimal valueA = a.getTotalRevenue() != null ? a.getTotalRevenue() : BigDecimal.ZERO;
                        BigDecimal valueB = b.getTotalRevenue() != null ? b.getTotalRevenue() : BigDecimal.ZERO;
                        compareResult = valueA.compareTo(valueB);
                    } else {
                        Integer valueA = a.getSalesCount() != null ? a.getSalesCount() : 0;
                        Integer valueB = b.getSalesCount() != null ? b.getSalesCount() : 0;
                        compareResult = valueA.compareTo(valueB);
                    }

                    return "asc".equals(sortOrder) ? compareResult : -compareResult;
                });

                log.info("🔄 已按{}进行{}排序", sortField, sortOrder);
            }

            // 构建分页结果
            Map<String, Object> result = new HashMap<>();
            result.put("records", voList);
            result.put("total", pageList.getTotal());
            result.put("size", pageList.getSize());
            result.put("current", pageList.getCurrent());
            result.put("pages", pageList.getPages());

            log.info("✅ 创作者智能体列表查询成功 - 用户: {}, 总数: {}", loginUser.getUsername(), pageList.getTotal());
            return Result.OK(result);

        } catch (Exception e) {
            log.error("❌ 创作者智能体列表查询失败", e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 新增智能体
     */
    @AutoLog(value = "创作者智能体-新增")
    @ApiOperation(value = "创作者智能体-新增", notes = "创作者新增智能体")
    @PostMapping(value = "/add")
    public Result<?> add(@Valid @RequestBody CreatorAgentDTO dto, HttpServletRequest request) {
        
        try {
            // 获取当前登录用户
            LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            if (loginUser == null) {
                return Result.error("用户未登录");
            }
            
            log.info("🔍 创作者新增智能体 - 用户: {}, 智能体名称: {}", loginUser.getUsername(), dto.getAgentName());

            // 创建智能体实体
            AigcAgent agent = new AigcAgent();
            BeanUtils.copyProperties(dto, agent);
            
            // 🔧 自动设置系统字段
            agent.setAgentId(generateUniqueAgentId()); // 自动生成智能体ID
            agent.setAuthorType("2"); // 默认创作者类型
            agent.setAuditStatus("1"); // 默认待审核状态
            agent.setCreateBy(loginUser.getUsername()); // 设置创建者
            agent.setCreateTime(new Date());
            agent.setUpdateTime(new Date());
            // demoVideo字段保持为空，创作者中心不支持视频上传

            // 保存到数据库
            boolean success = aigcAgentService.save(agent);
            
            if (success) {
                log.info("✅ 创作者智能体新增成功 - 用户: {}, 智能体ID: {}, 智能体名称: {}", 
                    loginUser.getUsername(), agent.getAgentId(), agent.getAgentName());
                
                // 返回创建的智能体信息
                CreatorAgentVO vo = new CreatorAgentVO();
                BeanUtils.copyProperties(agent, vo);
                vo.setAuthorTypeText(getDictText("author_type", agent.getAuthorType()));
                vo.setAuditStatusText(getDictText("audit_status", agent.getAuditStatus()));
                vo.setEditable(true);
                vo.setDeletable(true);
                vo.setWorkflowCount(0);
                
                return Result.OK(vo);
            } else {
                return Result.error("智能体创建失败");
            }

        } catch (Exception e) {
            log.error("❌ 创作者智能体新增失败", e);
            return Result.error("创建失败: " + e.getMessage());
        }
    }

    /**
     * 生成唯一的智能体ID
     * 格式：AGENT_ + 8位随机字符串
     */
    private String generateUniqueAgentId() {
        String prefix = "AGENT_";
        String characters = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
        SecureRandom random = new SecureRandom();
        StringBuilder sb = new StringBuilder(prefix);
        
        // 生成8位随机字符串
        for (int i = 0; i < 8; i++) {
            sb.append(characters.charAt(random.nextInt(characters.length())));
        }
        
        String agentId = sb.toString();
        
        // 检查是否已存在，如果存在则重新生成
        while (aigcAgentService.lambdaQuery().eq(AigcAgent::getAgentId, agentId).exists()) {
            sb = new StringBuilder(prefix);
            for (int i = 0; i < 8; i++) {
                sb.append(characters.charAt(random.nextInt(characters.length())));
            }
            agentId = sb.toString();
        }
        
        return agentId;
    }

    /**
     * 编辑智能体
     */
    @AutoLog(value = "创作者智能体-编辑")
    @ApiOperation(value = "创作者智能体-编辑", notes = "创作者编辑智能体（仅待审核状态可编辑）")
    @PutMapping(value = "/edit/{id}")
    public Result<?> edit(@PathVariable String id, @Valid @RequestBody CreatorAgentDTO dto, HttpServletRequest request) {

        try {
            // 获取当前登录用户
            LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            if (loginUser == null) {
                return Result.error("用户未登录");
            }

            log.info("🔍 创作者编辑智能体 - 用户: {}, 智能体ID: {}", loginUser.getUsername(), id);

            // 查询智能体
            AigcAgent agent = aigcAgentService.getById(id);
            if (agent == null) {
                return Result.error("智能体不存在");
            }

            // 验证权限：只能编辑自己创建的智能体
            if (!loginUser.getUsername().equals(agent.getCreateBy())) {
                return Result.error("无权限编辑此智能体");
            }

            // 验证状态：待审核和审核拒绝状态可编辑
            if (!"1".equals(agent.getAuditStatus()) && !"3".equals(agent.getAuditStatus())) {
                return Result.error("只有待审核或审核拒绝状态的智能体可以编辑");
            }

            // 更新智能体信息
            BeanUtils.copyProperties(dto, agent);
            agent.setUpdateTime(new Date());

            // 🔥 编辑后重新设置为待审核状态
            agent.setAuditStatus("1"); // 1=待审核
            log.info("🔄 智能体编辑后状态重置为待审核 - 智能体ID: {}", id);

            // 保持系统字段不变：agentId、authorType、createBy等

            // 保存到数据库
            boolean success = aigcAgentService.updateById(agent);

            if (success) {
                log.info("✅ 创作者智能体编辑成功 - 用户: {}, 智能体ID: {}", loginUser.getUsername(), id);

                // 返回更新后的智能体信息
                CreatorAgentVO vo = new CreatorAgentVO();
                BeanUtils.copyProperties(agent, vo);
                vo.setAuthorTypeText(getDictText("author_type", agent.getAuthorType()));
                vo.setAuditStatusText(getDictText("audit_status", agent.getAuditStatus()));

                // 🔥 重新计算操作权限（编辑后状态为待审核，仍可编辑和删除）
                boolean isPending = "1".equals(agent.getAuditStatus());
                boolean isRejected = "3".equals(agent.getAuditStatus());
                boolean canEdit = isPending || isRejected;
                vo.setEditable(canEdit);
                vo.setDeletable(canEdit);

                // 查询工作流数量
                QueryWrapper<AigcWorkflow> workflowQuery = new QueryWrapper<>();
                workflowQuery.eq("agent_id", agent.getId());
                int workflowCount = (int) aigcWorkflowService.count(workflowQuery);
                vo.setWorkflowCount(workflowCount);

                return Result.OK(vo);
            } else {
                return Result.error("智能体更新失败");
            }

        } catch (Exception e) {
            log.error("❌ 创作者智能体编辑失败", e);
            return Result.error("编辑失败: " + e.getMessage());
        }
    }

    /**
     * 删除智能体
     */
    @AutoLog(value = "创作者智能体-删除")
    @ApiOperation(value = "创作者智能体-删除", notes = "创作者删除智能体（待审核或审核拒绝状态可删除）")
    @DeleteMapping(value = "/delete/{id}")
    public Result<?> delete(@PathVariable String id, HttpServletRequest request) {

        try {
            // 获取当前登录用户
            LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            if (loginUser == null) {
                return Result.error("用户未登录");
            }

            log.info("🔍 创作者删除智能体 - 用户: {}, 智能体ID: {}", loginUser.getUsername(), id);

            // 查询智能体
            AigcAgent agent = aigcAgentService.getById(id);
            if (agent == null) {
                return Result.error("智能体不存在");
            }

            // 验证权限：只能删除自己创建的智能体
            if (!loginUser.getUsername().equals(agent.getCreateBy())) {
                return Result.error("无权限删除此智能体");
            }

            // 验证状态：待审核和审核拒绝状态可删除
            if (!"1".equals(agent.getAuditStatus()) && !"3".equals(agent.getAuditStatus())) {
                return Result.error("只有待审核或审核拒绝状态的智能体可以删除");
            }

            // 检查是否有关联的工作流
            QueryWrapper<AigcWorkflow> workflowQuery = new QueryWrapper<>();
            workflowQuery.eq("agent_id", id);
            long workflowCount = aigcWorkflowService.count(workflowQuery);

            if (workflowCount > 0) {
                return Result.error("该智能体下还有工作流，请先删除所有工作流后再删除智能体");
            }

            // 删除智能体
            boolean success = aigcAgentService.removeById(id);

            if (success) {
                log.info("✅ 创作者智能体删除成功 - 用户: {}, 智能体ID: {}", loginUser.getUsername(), id);
                return Result.OK("智能体删除成功！");
            } else {
                return Result.error("智能体删除失败");
            }

        } catch (Exception e) {
            log.error("❌ 创作者智能体删除失败", e);
            return Result.error("删除失败: " + e.getMessage());
        }
    }

    /**
     * 获取智能体详情
     */
    @AutoLog(value = "创作者智能体-详情查询")
    @ApiOperation(value = "创作者智能体-详情查询", notes = "获取智能体详细信息")
    @GetMapping(value = "/detail/{id}")
    public Result<?> queryById(@PathVariable String id, HttpServletRequest request) {

        try {
            // 获取当前登录用户
            LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            if (loginUser == null) {
                return Result.error("用户未登录");
            }

            log.info("🔍 创作者智能体详情查询 - 用户: {}, 智能体ID: {}", loginUser.getUsername(), id);

            // 查询智能体
            AigcAgent agent = aigcAgentService.getById(id);
            if (agent == null) {
                return Result.error("智能体不存在");
            }

            // 验证权限：只能查看自己创建的智能体
            if (!loginUser.getUsername().equals(agent.getCreateBy())) {
                return Result.error("无权限查看此智能体");
            }

            // 转换为VO对象
            CreatorAgentVO vo = new CreatorAgentVO();
            BeanUtils.copyProperties(agent, vo);

            // 处理头像URL，添加CDN前缀
            if (agent.getAgentAvatar() != null && !agent.getAgentAvatar().isEmpty()) {
                vo.setAgentAvatar(tosService.generateFileUrl(agent.getAgentAvatar()));
            }

            // 设置字典文本
            vo.setAuthorTypeText(getDictText("author_type", agent.getAuthorType()));
            vo.setAuditStatusText(getDictText("audit_status", agent.getAuditStatus()));

            // 设置操作权限（待审核和审核拒绝状态可编辑和删除）
            boolean isPending = "1".equals(agent.getAuditStatus());
            boolean isRejected = "3".equals(agent.getAuditStatus());
            boolean canEdit = isPending || isRejected;
            vo.setEditable(canEdit);
            vo.setDeletable(canEdit);

            // 查询工作流数量
            QueryWrapper<AigcWorkflow> workflowQuery = new QueryWrapper<>();
            workflowQuery.eq("agent_id", agent.getId());
            int workflowCount = (int) aigcWorkflowService.count(workflowQuery);
            vo.setWorkflowCount(workflowCount);

            // 如果是已通过状态，查询收益信息
            if ("2".equals(agent.getAuditStatus())) {
                // 查询真实的收益信息
                Map<String, Object> revenueData = queryAgentRevenueData(agent.getId());
                vo.setTotalRevenue((BigDecimal) revenueData.get("totalRevenue"));
                vo.setMonthRevenue((BigDecimal) revenueData.get("monthRevenue"));
                vo.setSalesCount((Integer) revenueData.get("salesCount"));
            }

            log.info("✅ 创作者智能体详情查询成功 - 用户: {}, 智能体ID: {}", loginUser.getUsername(), id);
            return Result.OK(vo);

        } catch (Exception e) {
            log.error("❌ 创作者智能体详情查询失败", e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 获取收益统计
     */
    @AutoLog(value = "创作者收益统计-查询")
    @ApiOperation(value = "创作者收益统计-查询", notes = "获取创作者的收益统计数据")
    @GetMapping(value = "/revenue/stats")
    public Result<?> getRevenueStats(HttpServletRequest request) {

        try {
            // 获取当前登录用户
            LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            if (loginUser == null) {
                return Result.error("用户未登录");
            }

            log.info("🔍 创作者收益统计查询 - 用户: {}", loginUser.getUsername());

            // 创建收益统计VO
            CreatorRevenueStatsVO statsVO = new CreatorRevenueStatsVO();

            // 1. 查询智能体统计
            QueryWrapper<AigcAgent> agentQuery = new QueryWrapper<>();
            agentQuery.eq("create_by", loginUser.getUsername());
            // 注释掉author_type限制，允许统计所有类型的智能体
            // agentQuery.eq("author_type", "2"); // 创作者类型

            List<AigcAgent> allAgents = aigcAgentService.list(agentQuery);
            statsVO.setAgentCount(allAgents.size());

            // 按审核状态分组统计
            Map<String, Long> statusCount = allAgents.stream()
                .collect(Collectors.groupingBy(AigcAgent::getAuditStatus, Collectors.counting()));

            statsVO.setApprovedAgentCount(statusCount.getOrDefault("2", 0L).intValue()); // 已通过
            statsVO.setPendingAgentCount(statusCount.getOrDefault("1", 0L).intValue());   // 待审核
            statsVO.setRejectedAgentCount(statusCount.getOrDefault("3", 0L).intValue());  // 已拒绝

            // 2. 查询工作流总数
            int totalWorkflowCount = 0;
            for (AigcAgent agent : allAgents) {
                QueryWrapper<AigcWorkflow> workflowQuery = new QueryWrapper<>();
                workflowQuery.eq("agent_id", agent.getId());
                totalWorkflowCount += (int) aigcWorkflowService.count(workflowQuery);
            }
            statsVO.setWorkflowCount(totalWorkflowCount);

            // 3. 查询收益数据（只统计已审核通过的智能体）
            List<AigcAgent> approvedAgents = allAgents.stream()
                .filter(agent -> "2".equals(agent.getAuditStatus()))
                .collect(Collectors.toList());

            if (!approvedAgents.isEmpty()) {
                // 获取智能体ID列表
                List<String> agentIds = approvedAgents.stream()
                    .map(AigcAgent::getId)
                    .collect(Collectors.toList());

                // 查询收益数据
                Map<String, Object> revenueData = queryRevenueData(loginUser.getUsername(), agentIds);

                // 设置收益统计
                statsVO.setTotalRevenue((BigDecimal) revenueData.getOrDefault("totalRevenue", BigDecimal.ZERO));
                statsVO.setMonthRevenue((BigDecimal) revenueData.getOrDefault("monthRevenue", BigDecimal.ZERO));
                statsVO.setYesterdayRevenue((BigDecimal) revenueData.getOrDefault("yesterdayRevenue", BigDecimal.ZERO));
                statsVO.setTotalSales((Integer) revenueData.getOrDefault("totalSales", 0));
                statsVO.setMonthSales((Integer) revenueData.getOrDefault("monthSales", 0));
                statsVO.setYesterdaySales((Integer) revenueData.getOrDefault("yesterdaySales", 0));

                // 4. 构建智能体收益明细
                List<CreatorRevenueStatsVO.AgentRevenueItem> agentRevenueList = new ArrayList<>();
                for (AigcAgent agent : approvedAgents) {
                    CreatorRevenueStatsVO.AgentRevenueItem item = new CreatorRevenueStatsVO.AgentRevenueItem();
                    item.setAgentId(agent.getAgentId());
                    item.setAgentName(agent.getAgentName());
                    // 处理头像URL，添加CDN前缀
                    if (agent.getAgentAvatar() != null && !agent.getAgentAvatar().isEmpty()) {
                        item.setAgentAvatar(tosService.generateFileUrl(agent.getAgentAvatar()));
                    } else {
                        item.setAgentAvatar(agent.getAgentAvatar());
                    }
                    item.setPrice(agent.getPrice());
                    item.setAuditStatus(agent.getAuditStatus());
                    item.setAuditStatusText(getDictText("audit_status", agent.getAuditStatus()));

                    // 查询该智能体的收益数据
                    Map<String, Object> agentRevenue = queryAgentRevenueData(agent.getId());
                    item.setTotalRevenue((BigDecimal) agentRevenue.getOrDefault("totalRevenue", BigDecimal.ZERO));
                    item.setMonthRevenue((BigDecimal) agentRevenue.getOrDefault("monthRevenue", BigDecimal.ZERO));
                    item.setSalesCount((Integer) agentRevenue.getOrDefault("salesCount", 0));
                    item.setMonthSalesCount((Integer) agentRevenue.getOrDefault("monthSalesCount", 0));

                    // 查询工作流数量
                    QueryWrapper<AigcWorkflow> workflowQuery = new QueryWrapper<>();
                    workflowQuery.eq("agent_id", agent.getId());
                    int workflowCount = (int) aigcWorkflowService.count(workflowQuery);
                    item.setWorkflowCount(workflowCount);

                    agentRevenueList.add(item);
                }

                // 按总收益降序排列
                agentRevenueList.sort((a, b) -> b.getTotalRevenue().compareTo(a.getTotalRevenue()));
                statsVO.setAgentRevenueList(agentRevenueList);
            } else {
                // 没有已审核通过的智能体，设置默认值
                statsVO.setTotalRevenue(BigDecimal.ZERO);
                statsVO.setMonthRevenue(BigDecimal.ZERO);
                statsVO.setYesterdayRevenue(BigDecimal.ZERO);
                statsVO.setTotalSales(0);
                statsVO.setMonthSales(0);
                statsVO.setYesterdaySales(0);
                statsVO.setAgentRevenueList(new ArrayList<>());
            }

            log.info("✅ 创作者收益统计查询成功 - 用户: {}, 智能体总数: {}, 已通过: {}",
                loginUser.getUsername(), statsVO.getAgentCount(), statsVO.getApprovedAgentCount());

            return Result.OK(statsVO);

        } catch (Exception e) {
            log.error("❌ 创作者收益统计查询失败", e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 查询创作者的收益数据
     * 从用户扩展表获取总收益，从智能体表获取销售次数
     */
    private Map<String, Object> queryRevenueData(String username, List<String> agentIds) {
        Map<String, Object> result = new HashMap<>();

        try {
            // 1. 从用户扩展表查询总收益
            String userRevenueSql = "SELECT " +
                "COALESCE(p.agent_earnings, 0) as total_revenue " +
                "FROM sys_user u " +
                "LEFT JOIN aicg_user_profile p ON u.id = p.user_id " +
                "WHERE u.username = ?";

            List<Map<String, Object>> userRevenueResult = jdbcTemplate.queryForList(userRevenueSql, username);
            BigDecimal totalRevenue = BigDecimal.ZERO;
            if (!userRevenueResult.isEmpty()) {
                Object revenueObj = userRevenueResult.get(0).get("total_revenue");
                if (revenueObj != null) {
                    totalRevenue = new BigDecimal(revenueObj.toString());
                }
            }

            // 2. 从智能体表查询销售次数
            int totalSales = 0;
            if (!agentIds.isEmpty()) {
                String agentIdPlaceholders = agentIds.stream().map(id -> "?").collect(Collectors.joining(","));
                String salesSql = "SELECT COALESCE(SUM(sales_count), 0) as total_sales " +
                    "FROM aigc_agent WHERE id IN (" + agentIdPlaceholders + ")";

                List<Map<String, Object>> salesResult = jdbcTemplate.queryForList(salesSql, agentIds.toArray());
                if (!salesResult.isEmpty()) {
                    Object salesObj = salesResult.get(0).get("total_sales");
                    if (salesObj != null) {
                        totalSales = Integer.parseInt(salesObj.toString());
                    }
                }
            }

            // 3. 设置返回数据（只返回能查询到的真实数据）
            result.put("totalRevenue", totalRevenue);
            result.put("totalSales", totalSales);
            // 本月收益和昨日收益无法准确查询，暂不提供
            result.put("monthRevenue", BigDecimal.ZERO);
            result.put("yesterdayRevenue", BigDecimal.ZERO);
            result.put("monthSales", 0);
            result.put("yesterdaySales", 0);

            log.info("查询收益数据成功 - 用户: {}, 总收益: {}, 总销售: {}", username, totalRevenue, totalSales);

        } catch (Exception e) {
            log.error("查询收益数据失败 - 用户: {}", username, e);
            // 返回默认值
            result.put("totalRevenue", BigDecimal.ZERO);
            result.put("monthRevenue", BigDecimal.ZERO);
            result.put("yesterdayRevenue", BigDecimal.ZERO);
            result.put("totalSales", 0);
            result.put("monthSales", 0);
            result.put("yesterdaySales", 0);
        }

        return result;
    }

    /**
     * 查询单个智能体的收益数据
     * 直接从智能体表的收益字段获取真实数据
     */
    private Map<String, Object> queryAgentRevenueData(String agentId) {
        Map<String, Object> result = new HashMap<>();

        try {
            // 从智能体表查询真实的收益数据
            String sql = "SELECT " +
                "COALESCE(sales_count, 0) as sales_count, " +
                "COALESCE(agent_revenue, 0) as agent_revenue, " +
                "COALESCE(month_revenue, 0) as month_revenue, " +
                "last_sale_time " +
                "FROM aigc_agent WHERE id = ?";

            List<Map<String, Object>> queryResult = jdbcTemplate.queryForList(sql, agentId);
            if (!queryResult.isEmpty()) {
                Map<String, Object> data = queryResult.get(0);
                int salesCount = Integer.parseInt(data.get("sales_count").toString());
                BigDecimal agentRevenue = new BigDecimal(data.get("agent_revenue").toString());
                BigDecimal monthRevenue = new BigDecimal(data.get("month_revenue").toString());

                result.put("totalRevenue", agentRevenue);
                result.put("salesCount", salesCount);
                result.put("monthRevenue", monthRevenue);
                result.put("monthSalesCount", 0); // 暂时不统计本月销售次数

                log.debug("查询智能体收益 - ID: {}, 销售次数: {}, 总收益: {}, 本月收益: {}",
                    agentId, salesCount, agentRevenue, monthRevenue);
            } else {
                result.put("totalRevenue", BigDecimal.ZERO);
                result.put("salesCount", 0);
                result.put("monthRevenue", BigDecimal.ZERO);
                result.put("monthSalesCount", 0);
            }

        } catch (Exception e) {
            log.error("查询智能体收益数据失败 - 智能体ID: {}", agentId, e);
            result.put("totalRevenue", BigDecimal.ZERO);
            result.put("salesCount", 0);
            result.put("monthRevenue", BigDecimal.ZERO);
            result.put("monthSalesCount", 0);
        }

        return result;
    }

    /**
     * 获取字典文本
     */
    private String getDictText(String dictCode, String dictValue) {
        try {
            if (oConvertUtils.isEmpty(dictValue)) {
                return "";
            }
            return sysDictService.queryDictTextByKey(dictCode, dictValue);
        } catch (Exception e) {
            log.warn("获取字典文本失败 - dictCode: {}, dictValue: {}", dictCode, dictValue);
            return dictValue;
        }
    }
}
