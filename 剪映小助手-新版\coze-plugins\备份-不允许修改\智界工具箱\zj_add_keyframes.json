{"openapi": "3.0.0", "info": {"title": "剪映小助手_智界工具箱 - 添加关键帧", "description": "添加关键帧", "version": "1.0.0"}, "servers": [{"url": "https://www.aigcview.com"}], "paths": {"/jeecg-boot/api/jianying/add_keyframes": {"post": {"summary": "添加关键帧", "description": "添加关键帧", "operationId": "addKeyframes_zj", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"access_key": {"type": "string", "description": "访问密钥（必填）", "example": "JianyingAPI_2025_SecureAccess_AigcView", "default": "JianyingAPI_2025_SecureAccess_AigcView"}, "zj_draft_url": {"type": "string", "description": "草稿地址，使用createDraft_zj输出的draft_url即可（必填）", "example": "https://example.com/draft/123"}, "zj_keyframes": {"type": "string", "description": "关键帧数据（必填）", "example": "[{\"offset\": 5000000, \"property\": \"KFTypePositionX\", \"segment_id\": \"acc5b516-046b-4eae-a179-f686f35e70a8\", \"value\": 0}]"}}, "required": ["access_key", "zj_draft_url", "zj_keyframes"], "additionalProperties": false}}}}, "responses": {"200": {"description": "成功添加关键帧", "content": {"application/json": {"schema": {"type": "object", "properties": {"draft_url": {"type": "string", "description": "更新后的草稿地址", "example": "https://example.com/draft/123"}}, "required": ["draft_url"]}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "description": "错误信息", "example": "关键帧数据不能为空"}}, "required": ["error"]}}}}, "500": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "description": "错误信息", "example": "添加关键帧失败: 系统内部错误"}}, "required": ["error"]}}}}}}}}}