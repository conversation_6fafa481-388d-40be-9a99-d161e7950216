{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": ";;;AAAA,6CAAkE;AAAzD,6GAAA,aAAa,OAAA;AAAE,oGAAA,IAAI,OAAA;AAAE,8GAAA,cAAc,OAAA;AAC5C,qCAA4D;AAAnD,gGAAA,KAAK,OAAA;AAAc,wGAAA,aAAa,OAAA;AACzC,mDA+DwB;AA7DtB,2GAAA,QAAQ,OAAA;AACR,yGAAA,MAAM,OAAA;AACN,6GAAA,UAAU,OAAA;AAKV,iHAAA,cAAc,OAAA;AA8Bd,2GAAA,QAAQ,OAAA;AAmBR,0GAAA,OAAO,OAAA;AAEP,iHAAA,cAAc,OAAA;AAIhB,mDAA0D;AAAjD,6GAAA,UAAU,OAAA;AACnB,6DAAwD;AAA/C,yHAAA,iBAAiB,OAAA", "sourcesContent": ["export { getArchSuffix, Arch, archFromString } from \"builder-util\"\nexport { build, CliOptions, createTargets } from \"./builder\"\nexport {\n  TargetConfiguration,\n  Platform,\n  Target,\n  DIR_TARGET,\n  BeforeBuildContext,\n  SourceRepositoryInfo,\n  TargetSpecificOptions,\n  TargetConfigType,\n  DEFAULT_TARGET,\n  CompressionLevel,\n  MacConfiguration,\n  DmgOptions,\n  MasConfiguration,\n  MacOsTargetName,\n  PkgOptions,\n  DmgContent,\n  DmgWindow,\n  PlatformSpecificBuildOptions,\n  AsarOptions,\n  FileSet,\n  LinuxConfiguration,\n  DebOptions,\n  CommonLinuxOptions,\n  LinuxTargetSpecificOptions,\n  AppImageOptions,\n  Configuration,\n  AfterPackContext,\n  MetadataDirectories,\n  Protocol,\n  ReleaseInfo,\n  ElectronBrandingOptions,\n  ElectronDownloadOptions,\n  SnapOptions,\n  CommonWindowsInstallerConfiguration,\n  FileAssociation,\n  MsiOptions,\n  AppXOptions,\n  WindowsConfiguration,\n  Packager,\n  BuildResult,\n  PackagerOptions,\n  ArtifactCreated,\n  ArtifactBuildStarted,\n  NsisOptions,\n  NsisWebOptions,\n  PortableOptions,\n  CommonNsisOptions,\n  SquirrelWindowsOptions,\n  WindowsSignOptions,\n  CustomWindowsSignTaskConfiguration,\n  WindowsSignTaskConfiguration,\n  CustomWindowsSign,\n  FileCodeSigningInfo,\n  CertificateFromStoreInfo,\n  Metadata,\n  AuthorMetadata,\n  RepositoryInfo,\n  AppInfo,\n  UploadTask,\n  PublishManager,\n  PublishOptions,\n  ProgressInfo,\n} from \"app-builder-lib\"\nexport { buildForge, ForgeOptions } from \"app-builder-lib\"\nexport { CancellationToken } from \"builder-util-runtime\"\n"]}