import { VersioningStatusType } from '../../TosExportEnum';
import TOSBase from '../base';
export { VersioningStatusType as BucketVersioningStatus };
export declare type PutBucketVersioningInputStatus = VersioningStatusType.Enable | VersioningStatusType.Enabled | VersioningStatusType.Suspended;
export interface GetBucketVersioningOutput {
    Status: VersioningStatusType;
}
export interface PutBucketVersioningInput {
    bucket?: string;
    status: PutBucketVersioningInputStatus;
}
export declare function getBucketVersioning(this: TOSBase, bucket?: string): Promise<import("../base").TosResponse<GetBucketVersioningOutput>>;
export declare function putBucketVersioning(this: TOSBase, input: PutBucketVersioningInput): Promise<import("../base").TosResponse<unknown>>;
