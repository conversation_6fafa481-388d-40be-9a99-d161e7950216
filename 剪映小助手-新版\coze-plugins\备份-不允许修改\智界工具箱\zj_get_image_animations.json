{"openapi": "3.0.0", "info": {"title": "剪映小助手_智界工具箱 - 获取图片出入场动画", "description": "获取图片出入场动画", "version": "1.0.0"}, "servers": [{"url": "https://www.aigcview.com"}], "paths": {"/jeecg-boot/api/jianying/get_image_animations": {"post": {"summary": "获取图片出入场动画", "description": "获取图片出入场动画", "operationId": "getImageAnimations_zj", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"access_key": {"type": "string", "description": "访问密钥（必填）", "example": "JianyingAPI_2025_SecureAccess_AigcView", "default": "JianyingAPI_2025_SecureAccess_AigcView"}, "zj_mode": {"type": "integer", "description": "默认0 全部 1 vip 2 免费", "enum": [0, 1, 2], "example": 0}, "zj_type": {"type": "string", "description": "三个值，默认in：in 入场 out 出场 group 组合", "enum": ["in", "out", "group"], "example": "in"}}, "required": ["access_key"], "additionalProperties": false}}}}, "responses": {"200": {"description": "成功获取图片动画", "content": {"application/json": {"schema": {"type": "object", "properties": {"effects": {"type": "array", "description": "图片动画效果列表", "items": {"type": "object", "properties": {"resource_id": {"type": "string", "description": "资源ID"}, "type": {"type": "string", "description": "动画类型", "enum": ["in", "out", "group"]}, "category_id": {"type": "string", "description": "分类ID"}, "duration": {"type": "integer", "description": "动画时长（微秒）"}, "name": {"type": "string", "description": "动画名称"}, "request_id": {"type": "string", "description": "请求ID"}, "panel": {"type": "string", "description": "面板类型"}, "path": {"type": "string", "description": "路径"}, "platform": {"type": "string", "description": "平台"}, "start": {"type": "integer", "description": "开始时间"}, "category_name": {"type": "string", "description": "分类名称"}, "icon_url": {"type": "string", "description": "图标URL"}, "id": {"type": "string", "description": "动画ID"}, "material_type": {"type": "string", "description": "素材类型"}}, "required": ["resource_id", "type", "category_id", "duration", "name", "id", "material_type"]}}}, "required": ["effects"]}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "description": "错误信息"}}, "required": ["error"]}}}}, "500": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "description": "错误信息"}}, "required": ["error"]}}}}}}}}}