package org.jeecg.modules.demo.notification.entity;

import java.io.Serializable;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 用户通知消息表
 * @Author: jeecg-boot
 * @Date:   2025-06-18
 * @Version: V1.0
 */
@Data
@TableName("aicg_user_notification")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="aicg_user_notification对象", description="用户通知消息表")
public class AicgUserNotification implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String id;
    
	/**用户ID*/
	@Excel(name = "用户ID", width = 15)
    @ApiModelProperty(value = "用户ID，关联sys_user.id")
    private String userId;
    
	/**通知标题*/
	@Excel(name = "通知标题", width = 30)
    @ApiModelProperty(value = "通知标题")
    private String title;
    
	/**通知内容*/
    @ApiModelProperty(value = "通知内容")
    private String content;
    
	/**通知类型*/
	@Excel(name = "通知类型", width = 15)
    @ApiModelProperty(value = "通知类型：1=系统通知,2=交易通知,3=安全提醒,4=营销推送")
    private Integer type;
    
	/**优先级*/
	@Excel(name = "优先级", width = 15)
    @ApiModelProperty(value = "优先级：1=普通,2=重要,3=紧急")
    private Integer priority;
    
	/**是否已读*/
	@Excel(name = "是否已读", width = 15)
    @ApiModelProperty(value = "是否已读：0=未读,1=已读")
    private Integer isRead;
    
	/**阅读时间*/
	@Excel(name = "阅读时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "阅读时间")
    private Date readTime;
    
	/**关联业务ID*/
	@Excel(name = "关联业务ID", width = 15)
    @ApiModelProperty(value = "关联业务ID(订单ID、交易ID等)")
    private String relatedId;
    
	/**关联业务类型*/
	@Excel(name = "关联业务类型", width = 20)
    @ApiModelProperty(value = "关联业务类型")
    private String relatedType;
    
	/**操作链接*/
    @ApiModelProperty(value = "操作链接")
    private String actionUrl;
    
	/**过期时间*/
	@Excel(name = "过期时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "过期时间")
    private Date expireTime;
    
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;
    
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private Date createTime;
    
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private String updateBy;
    
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private Date updateTime;
    
	/**所属部门*/
    @ApiModelProperty(value = "所属部门")
    private String sysOrgCode;
}
