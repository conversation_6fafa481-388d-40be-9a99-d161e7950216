package org.jeecg.modules.jianying.service;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.jeecg.common.util.oConvertUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.PostConstruct;
import java.nio.charset.StandardCharsets;
import java.util.concurrent.TimeUnit;

/**
 * Coze平台API服务
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
@Slf4j
@Service
public class CozeApiService {

    @Value("${coze.api.base-url:https://api.coze.cn}")
    private String cozeBaseUrl;

    @Value("${coze.api.token:}")
    private String cozeApiToken;

    // 🆕 CDN配置
    @Value("${volcengine.tos.cdn.enabled:false}")
    private Boolean cdnEnabled;

    @Value("${volcengine.tos.cdn.domain:}")
    private String cdnDomain;

    private final RestTemplate restTemplate = new RestTemplate();

    @Autowired
    private TosService tosService;

    @Autowired
    private JianyingDataboxService jianyingDataboxService;

    /**
     * 初始化RestTemplate，设置UTF-8编码
     */
    @PostConstruct
    public void initRestTemplate() {
        // 设置UTF-8编码的StringHttpMessageConverter
        restTemplate.getMessageConverters()
                .stream()
                .filter(converter -> converter instanceof StringHttpMessageConverter)
                .forEach(converter -> ((StringHttpMessageConverter) converter).setDefaultCharset(StandardCharsets.UTF_8));

        // 设置更长的超时时间，避免TOS访问超时
        restTemplate.getRequestFactory();
        log.info("RestTemplate初始化完成，支持TOS文件下载");
    }
    
    /**
     * 调用Coze平台创建草稿API（本地处理）
     */
    public JSONObject callCreateDraft(Integer height, Integer width, Integer userId) {
        try {
            log.info("本地处理创建草稿请求 - height: {}, width: {}, userId: {}", height, width, userId);

            // 本地处理创建草稿（不需要调用外部API）
            Integer finalHeight = height != null ? height : 1024;
            Integer finalWidth = width != null ? width : 1024;

            // 生成草稿ID
            int hashValue = finalHeight + finalWidth + (userId != null ? userId : 0);
            String draftId = "draft_" + System.currentTimeMillis() + "_" + Math.abs(hashValue);

            // 生成基础的剪映草稿JSON结构
            JSONObject draftData = new JSONObject();
            draftData.put("draft_id", draftId);
            draftData.put("height", finalHeight);
            draftData.put("width", finalWidth);
            draftData.put("user_id", userId);
            draftData.put("created_time", System.currentTimeMillis());

            // 基础剪映草稿结构
            JSONObject draftContent = new JSONObject();
            draftContent.put("version", "13.0.0");
            draftContent.put("platform", "android");
            draftContent.put("draft_id", draftId);

            // 画布信息
            JSONObject canvasConfig = new JSONObject();
            canvasConfig.put("height", finalHeight);
            canvasConfig.put("width", finalWidth);
            canvasConfig.put("ratio", "16:9");
            draftContent.put("canvas_config", canvasConfig);

            // 轨道信息
            JSONObject tracks = new JSONObject();
            tracks.put("video", new java.util.ArrayList<>());
            tracks.put("audio", new java.util.ArrayList<>());
            tracks.put("sticker", new java.util.ArrayList<>());
            tracks.put("text", new java.util.ArrayList<>());
            tracks.put("effect", new java.util.ArrayList<>());
            draftContent.put("tracks", tracks);

            // 素材信息
            JSONObject materials = new JSONObject();
            materials.put("videos", new java.util.ArrayList<>());
            materials.put("audios", new java.util.ArrayList<>());
            materials.put("images", new java.util.ArrayList<>());
            materials.put("texts", new java.util.ArrayList<>());
            draftContent.put("materials", materials);

            draftData.put("draft_content", draftContent);

            // 返回结果
            JSONObject result = new JSONObject();
            result.put("success", true);
            result.put("draft_id", draftId);
            result.put("draft_content", draftContent.toJSONString());
            result.put("message", "草稿创建成功");

            log.info("本地创建草稿成功 - draftId: {}", draftId);
            return result;

        } catch (Exception e) {
            log.error("本地创建草稿失败", e);
            throw new RuntimeException("创建草稿失败", e);
        }
    }
    
    /**
     * 调用Coze平台快速创建素材轨道API（本地处理）
     */
    public JSONObject callEasyCreateMaterial(String videoUrl, String text, Double textTransformX, Double textTransformY,
                                           Integer fontSize, String imgUrl, String textColor,
                                           String audioUrl, String draftUrl) {
        try {
            log.info("本地处理快速创建素材轨道请求 - audioUrl: {}, draftUrl: {}", audioUrl, draftUrl);

            // 参数验证
            if (audioUrl == null || audioUrl.trim().isEmpty()) {
                throw new RuntimeException("音频链接不能为空");
            }
            if (draftUrl == null || draftUrl.trim().isEmpty()) {
                throw new RuntimeException("草稿地址不能为空");
            }

            // 第1步：下载并解析原草稿
            JSONObject originalDraft = downloadAndParseDraft(draftUrl);
            log.info("成功下载原草稿，ID: {}", originalDraft.getString("id"));

            // 第2步：在原始草稿基础上添加所有素材
            JSONObject updatedDraft = addAllMaterialsToDraft(originalDraft, audioUrl, videoUrl, text, textTransformX, textTransformY, fontSize, imgUrl, textColor);

            // 3. 覆盖保存到原地址（就地修改，匹配竞争对手逻辑）
            overwriteDraftFile(draftUrl, updatedDraft);

            // 计算添加的素材数量
            int materialCount = 1; // 音频必有
            if (videoUrl != null && !videoUrl.trim().isEmpty()) materialCount++;
            if (imgUrl != null && !imgUrl.trim().isEmpty()) materialCount++;
            if (text != null && !text.trim().isEmpty()) materialCount++;

            // 返回结果（匹配竞争对手格式）
            JSONObject result = new JSONObject();
            result.put("message", "不知道导入的请看：https://www.aigcview.com/JianYingDraft?draft=" + draftUrl);
            result.put("draft_url", draftUrl); // 直接返回火山引擎TOS的下载地址
            result.put("material_count", materialCount);

            log.info("本地快速创建素材轨道成功 - 素材数量: {}, 文件已覆盖: {}", materialCount, draftUrl);
            return result;

        } catch (Exception e) {
            log.error("本地快速创建素材轨道失败", e);
            throw new RuntimeException("快速创建素材轨道失败", e);
        }
    }
    
    /**
     * 通用Coze API调用方法
     */
    public JSONObject callCozeApi(String endpoint, JSONObject parameters) {
        try {
            String url = cozeBaseUrl + endpoint;
            
            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(new MediaType("application", "json", StandardCharsets.UTF_8));
            headers.setAcceptCharset(java.util.Collections.singletonList(StandardCharsets.UTF_8));
            if (cozeApiToken != null && !cozeApiToken.isEmpty()) {
                headers.setBearerAuth(cozeApiToken);
            }
            
            HttpEntity<String> entity = new HttpEntity<>(parameters.toJSONString(), headers);
            
            // 发送请求
            ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.POST, entity, String.class);

            if (response.getStatusCode() == HttpStatus.OK) {
                String responseBody = response.getBody();
                log.debug("Coze API响应: {}", responseBody);

                // 验证响应是否为有效JSON
                if (responseBody == null || responseBody.trim().isEmpty()) {
                    throw new RuntimeException("Coze API返回空响应");
                }

                // 检查是否包含非JSON内容（如HTML或纯文本）
                if (!responseBody.trim().startsWith("{") && !responseBody.trim().startsWith("[")) {
                    log.error("Coze API返回非JSON格式响应: {}", responseBody);
                    throw new RuntimeException("Coze API返回格式错误: " + responseBody);
                }

                try {
                    return JSONObject.parseObject(responseBody);
                } catch (Exception e) {
                    log.error("解析Coze API响应失败: {}", responseBody, e);
                    throw new RuntimeException("解析API响应失败: " + e.getMessage());
                }
            } else {
                String errorBody = response.getBody();
                log.error("Coze API调用失败 - 状态码: {}, 响应: {}", response.getStatusCode(), errorBody);
                throw new RuntimeException("Coze API调用失败: " + response.getStatusCode() + ", 响应: " + errorBody);
            }
            
        } catch (Exception e) {
            log.error("调用Coze API失败: {}", endpoint, e);
            throw new RuntimeException("Coze API调用失败", e);
        }
    }

    /**
     * 下载草稿JSON文件（使用TOS SDK）
     */
    public JSONObject downloadDraftJson(String draftUrl) {
        try {
            log.info("开始使用TOS SDK下载草稿JSON: {}", draftUrl);

            // 从URL中提取文件路径
            String filePath = extractFilePathFromUrl(draftUrl);

            // 使用TosService的SDK方法下载
            String jsonContent = tosService.downloadDraftFile(filePath);

            if (jsonContent == null || jsonContent.trim().isEmpty()) {
                throw new RuntimeException("下载的JSON内容为空");
            }

            log.info("下载的JSON内容前200字符: {}", jsonContent.length() > 200 ? jsonContent.substring(0, 200) : jsonContent);

            // 解析JSON
            JSONObject draftJson = JSONObject.parseObject(jsonContent);
            log.info("TOS SDK下载草稿JSON成功，大小: {} 字符", jsonContent.length());

            return draftJson;

        } catch (Exception e) {
            log.error("TOS SDK下载草稿JSON失败: {}", draftUrl, e);
            throw new RuntimeException("下载草稿JSON失败: " + e.getMessage(), e);
        }
    }

    /**
     * 在原始草稿基础上添加所有素材（音频、视频、图片、文本）
     */
    private JSONObject addAllMaterialsToDraft(JSONObject originalDraft, String audioUrl, String videoUrl,
                                             String text, Double textTransformX, Double textTransformY, Integer fontSize,
                                             String imgUrl, String textColor) {
        try {
            log.info("开始在原始草稿基础上添加所有素材 - 音频:{}, 视频:{}, 图片:{}, 文本:{}",
                    audioUrl != null, videoUrl != null, imgUrl != null, text != null);

            // 使用originalDraft作为基础，而不是硬编码模板
            JSONObject updatedDraft = JSONObject.parseObject(originalDraft.toJSONString());

            // 确保materials对象存在且有正确的结构
            JSONObject materials = updatedDraft.getJSONObject("materials");
            if (materials == null) {
                materials = new JSONObject(new java.util.LinkedHashMap<>());
                updatedDraft.put("materials", materials);
            }

            // 确保tracks数组存在且有正确的4个tracks结构
            com.alibaba.fastjson.JSONArray tracks = updatedDraft.getJSONArray("tracks");
            if (tracks == null) {
                tracks = new com.alibaba.fastjson.JSONArray();
                updatedDraft.put("tracks", tracks);
            }

            // 确保有4个tracks（和竞争对手一致）
            if (tracks.size() < 4) {
                // 保留现有tracks，只补充缺失的tracks
                // 不能删除原有的tracks！

                // 1. video track (flag:0)
                JSONObject videoTrack1 = new JSONObject(new java.util.LinkedHashMap<>());
                videoTrack1.put("attribute", 0);
                videoTrack1.put("flag", 0);
                videoTrack1.put("id", java.util.UUID.randomUUID().toString());
                videoTrack1.put("segments", new com.alibaba.fastjson.JSONArray());
                videoTrack1.put("type", "video");
                tracks.add(videoTrack1);

                // 2. audio track (flag:3) - 稍后会添加segments
                JSONObject audioTrack = new JSONObject(new java.util.LinkedHashMap<>());
                audioTrack.put("attribute", 0);
                audioTrack.put("flag", 3);
                audioTrack.put("id", java.util.UUID.randomUUID().toString());
                audioTrack.put("segments", new com.alibaba.fastjson.JSONArray());
                audioTrack.put("type", "audio");
                tracks.add(audioTrack);

                // 3. video track (flag:3)
                JSONObject videoTrack2 = new JSONObject(new java.util.LinkedHashMap<>());
                videoTrack2.put("attribute", 0);
                videoTrack2.put("flag", 3);
                videoTrack2.put("id", java.util.UUID.randomUUID().toString());
                videoTrack2.put("segments", new com.alibaba.fastjson.JSONArray());
                videoTrack2.put("type", "video");
                tracks.add(videoTrack2);

                // 4. text track (flag:3)
                JSONObject textTrack = new JSONObject(new java.util.LinkedHashMap<>());
                textTrack.put("attribute", 0);
                textTrack.put("flag", 3);
                textTrack.put("id", java.util.UUID.randomUUID().toString());
                textTrack.put("segments", new com.alibaba.fastjson.JSONArray());
                textTrack.put("type", "text");
                tracks.add(textTrack);

                log.info("重新创建了4个tracks结构");
            }

            // 第1步：确定统一的文件夹ID（关键修复！）
            String unifiedFolderId = extractOrCreateUnifiedFolderId(updatedDraft);
            log.info("使用统一文件夹ID: {}", unifiedFolderId);

            // 生成新的音频素材ID
            String audioMaterialId = java.util.UUID.randomUUID().toString(); // 音频素材ID（用于material_id引用）
            String audioSegmentId = java.util.UUID.randomUUID().toString();

            // 1. 获取音频真实时长
            log.info("开始获取音频真实时长: {}", audioUrl);
            long realAudioDuration = jianyingDataboxService.getRealAudioDurationOptimized(audioUrl);
            log.info("音频真实时长: {} 微秒 (约 {} 秒)", realAudioDuration, realAudioDuration / 1000000.0);

            // 2. 下载音频文件并上传到TOS（使用统一文件夹ID）
            String[] audioInfo = downloadAndUploadAudio(audioUrl, unifiedFolderId);
            String audioFileName = audioInfo[0];
            String audioDownloadUrl = audioInfo[1];

            // 3. 添加音频素材到materials.audios
            com.alibaba.fastjson.JSONArray audios = updatedDraft.getJSONObject("materials").getJSONArray("audios");
            if (audios == null) {
                audios = new com.alibaba.fastjson.JSONArray();
                updatedDraft.getJSONObject("materials").put("audios", audios);
            }

            // 按照竞争对手的完全一致的字段顺序创建audioMaterial
            JSONObject audioMaterial = new JSONObject(new java.util.LinkedHashMap<>());
            audioMaterial.put("app_id", 0);
            audioMaterial.put("category_id", "");
            audioMaterial.put("category_name", "local");
            audioMaterial.put("check_flag", 1);
            audioMaterial.put("copyright_limit_type", "none");
            audioMaterial.put("duration", realAudioDuration);
            audioMaterial.put("effect_id", "");
            audioMaterial.put("formula_id", "");
            audioMaterial.put("id", audioMaterialId);
            audioMaterial.put("intensifies_path", "");
            audioMaterial.put("is_ai_clone_tone", false);
            audioMaterial.put("is_text_edit_overdub", false);
            audioMaterial.put("is_ugc", false);
            audioMaterial.put("local_material_id", "");
            audioMaterial.put("music_id", "");
            audioMaterial.put("name", java.util.UUID.randomUUID().toString());
            // 使用固定的占位符ID（与竞争对手一致）
            audioMaterial.put("path", "##_draftpath_placeholder_0E685133-18CE-45ED-8CB8-2904A212EC80_##\\" + unifiedFolderId + "\\" + audioFileName);
            audioMaterial.put("query", "");
            audioMaterial.put("request_id", "");
            audioMaterial.put("resource_id", "");
            audioMaterial.put("search_id", "");
            audioMaterial.put("source_from", "");
            audioMaterial.put("source_platform", 0);
            audioMaterial.put("team_id", "");
            audioMaterial.put("text_id", "");
            audioMaterial.put("tone_category_id", "");
            audioMaterial.put("tone_category_name", "");
            audioMaterial.put("tone_effect_id", "");
            audioMaterial.put("tone_effect_name", "");
            audioMaterial.put("tone_platform", "");
            audioMaterial.put("tone_second_category_id", "");
            audioMaterial.put("tone_second_category_name", "");
            audioMaterial.put("tone_speaker", "");
            audioMaterial.put("tone_type", "");
            audioMaterial.put("type", "extract_music");
            audioMaterial.put("video_id", "");
            audioMaterial.put("wave_points", new com.alibaba.fastjson.JSONArray());
            // 添加客户端下载需要的字段（客户端会在保存时移除）
            audioMaterial.put("download_url", audioDownloadUrl);
            audioMaterial.put("file_name", audioFileName);
            audios.add(audioMaterial);

            // 2. 添加音频轨道到tracks（tracks已在上面定义）

            // 音频轨道已在模板中创建

            // 创建音频段（按照竞争对手的完全一致的字段顺序）
            JSONObject audioSegment = new JSONObject(new java.util.LinkedHashMap<>());

            // 按照竞争对手的确切字段顺序
            audioSegment.put("caption_info", null);
            audioSegment.put("cartoon", false);
            audioSegment.put("clip", new JSONObject(new java.util.LinkedHashMap<>()) {{
                put("alpha", 1);
                put("flip", new JSONObject(new java.util.LinkedHashMap<>()) {{
                    put("horizontal", false);
                    put("vertical", false);
                }});
                put("rotation", 0);
                put("scale", new JSONObject(new java.util.LinkedHashMap<>()) {{
                    put("x", 1);
                    put("y", 1);
                }});
                put("transform", new JSONObject(new java.util.LinkedHashMap<>()) {{
                    put("x", 0);
                    put("y", 0);
                }});
            }});
            audioSegment.put("common_keyframes", new com.alibaba.fastjson.JSONArray());
            audioSegment.put("enable_adjust", true);
            audioSegment.put("enable_color_correct_adjust", false);
            audioSegment.put("enable_color_curves", true);
            audioSegment.put("enable_color_match_adjust", false);
            audioSegment.put("enable_color_wheels", true);
            audioSegment.put("enable_lut", true);
            audioSegment.put("enable_smart_color_adjust", false);
            audioSegment.put("extra_material_refs", new com.alibaba.fastjson.JSONArray());
            audioSegment.put("group_id", "");
            audioSegment.put("hdr_settings", new JSONObject(new java.util.LinkedHashMap<>()) {{
                put("intensity", 1);
                put("mode", 1);
                put("nits", 1000);
            }});
            audioSegment.put("id", audioSegmentId);
            audioSegment.put("intensifies_audio", false);
            audioSegment.put("is_placeholder", false);
            audioSegment.put("is_tone_modify", false);
            audioSegment.put("keyframe_refs", new com.alibaba.fastjson.JSONArray());
            audioSegment.put("last_nonzero_volume", 1);
            audioSegment.put("material_id", audioMaterialId);
            audioSegment.put("render_index", 1);
            audioSegment.put("responsive_layout", new JSONObject(new java.util.LinkedHashMap<>()) {{
                put("enable", false);
                put("horizontal_pos_layout", 0);
                put("size_layout", 0);
                put("target_follow", "");
                put("vertical_pos_layout", 0);
            }});
            audioSegment.put("reverse", false);
            audioSegment.put("source_timerange", new JSONObject(new java.util.LinkedHashMap<>()) {{
                put("duration", realAudioDuration);
                put("start", 0);
            }});
            audioSegment.put("speed", 1);
            audioSegment.put("target_timerange", new JSONObject(new java.util.LinkedHashMap<>()) {{
                put("duration", realAudioDuration);
                put("start", 0);
            }});
            audioSegment.put("template_id", "");
            audioSegment.put("template_scene", "default");
            audioSegment.put("track_attribute", 0);
            audioSegment.put("track_render_index", 3);
            audioSegment.put("uniform_scale", new JSONObject(new java.util.LinkedHashMap<>()) {{
                put("on", true);
                put("value", 1);
            }});
            audioSegment.put("visible", true);
            audioSegment.put("volume", 1);


            // 找到音频轨道并追加segments（修改为追加模式，与add_audios接口一致）
            JSONObject audioTrack = null;
            for (int i = 0; i < tracks.size(); i++) {
                JSONObject track = tracks.getJSONObject(i);
                if ("audio".equals(track.getString("type"))) {
                    audioTrack = track;
                    break;
                }
            }

            if (audioTrack != null) {
                // 获取现有segments数组（追加模式，与视频、图片处理逻辑一致）
                com.alibaba.fastjson.JSONArray existingSegments = audioTrack.getJSONArray("segments");
                if (existingSegments == null) {
                    existingSegments = new com.alibaba.fastjson.JSONArray();
                    audioTrack.put("segments", existingSegments);
                }
                existingSegments.add(audioSegment); // 追加到现有数组，而不是替换
                log.info("找到audio track并追加segment（追加模式）");
            } else {
                log.error("未找到audio track，这不应该发生");
            }

            // 3. 确保keyframes.audios为空（匹配竞争对手格式）
            JSONObject keyframes = updatedDraft.getJSONObject("keyframes");
            if (keyframes == null) {
                keyframes = new JSONObject();
                updatedDraft.put("keyframes", keyframes);
            }

            // keyframes.audios已在模板中设置为空数组

            log.info("成功添加音频素材到草稿，素材ID: {}", audioMaterialId);

            // 第3步：只有当音频时长更长时才更新草稿总时长
            long currentDuration = updatedDraft.getLongValue("duration");
            if (realAudioDuration > currentDuration) {
                updatedDraft.put("duration", realAudioDuration);
                log.info("音频时长更长，更新草稿总时长为: {} 微秒", realAudioDuration);
            } else {
                log.info("保持原有草稿时长: {} 微秒", currentDuration);
            }

            // 第4步：处理视频素材（如果有）
            if (videoUrl != null && !videoUrl.trim().isEmpty()) {
                log.info("开始处理视频素材: {}", videoUrl);
                addVideoMaterial(updatedDraft, videoUrl, realAudioDuration, unifiedFolderId);
                log.info("视频素材处理成功");
            }

            // 第5步：处理图片素材（如果有）
            if (imgUrl != null && !imgUrl.trim().isEmpty()) {
                log.info("开始处理图片素材: {}", imgUrl);
                addImageMaterial(updatedDraft, imgUrl, realAudioDuration, unifiedFolderId);
                log.info("图片素材处理成功");
            }

            // 第6步：处理文本素材（如果有）
            if (text != null && !text.trim().isEmpty()) {
                log.info("开始处理文本素材: {}", text);
                addTextMaterial(updatedDraft, text, textColor, fontSize, textTransformX, textTransformY, realAudioDuration);
                log.info("文本素材处理成功");
            }

            log.info("所有素材处理完成");

            // 调试：检查最终的materials内容
            JSONObject finalMaterials = updatedDraft.getJSONObject("materials");
            if (finalMaterials != null) {
                com.alibaba.fastjson.JSONArray finalVideos = finalMaterials.getJSONArray("videos");
                com.alibaba.fastjson.JSONArray finalTexts = finalMaterials.getJSONArray("texts");
                log.info("最终materials检查 - videos数量: {}, texts数量: {}",
                    finalVideos != null ? finalVideos.size() : 0,
                    finalTexts != null ? finalTexts.size() : 0);
            }

            return updatedDraft;

        } catch (Exception e) {
            log.error("添加音频素材到草稿失败", e);
            throw new RuntimeException("添加音频素材失败: " + e.getMessage(), e);
        }
    }

    /**
     * 覆盖保存草稿文件到原地址（就地修改）
     */
    public void overwriteDraftFile(String draftUrl, JSONObject updatedDraft) {
        try {
            log.info("开始覆盖保存草稿文件到原地址: {}", draftUrl);

            // 调试：安全地检查要保存的内容
            Object materialsObj = updatedDraft.get("materials");
            if (materialsObj instanceof JSONObject) {
                JSONObject materials = (JSONObject) materialsObj;
                com.alibaba.fastjson.JSONArray videos = materials.getJSONArray("videos");
                com.alibaba.fastjson.JSONArray texts = materials.getJSONArray("texts");
                log.info("准备保存的materials - videos数量: {}, texts数量: {}",
                    videos != null ? videos.size() : 0,
                    texts != null ? texts.size() : 0);
            } else if (materialsObj instanceof com.alibaba.fastjson.JSONArray) {
                com.alibaba.fastjson.JSONArray materialsArray = (com.alibaba.fastjson.JSONArray) materialsObj;
                log.info("准备保存的materials是数组格式，数量: {}", materialsArray.size());
            } else {
                log.info("准备保存的materials类型: {}",
                        materialsObj != null ? materialsObj.getClass().getSimpleName() : "null");
            }

            // 从URL中提取文件路径
            String filePath = extractFilePathFromUrl(draftUrl);

            // 将更新后的JSON内容上传到TOS，覆盖原文件
            // 使用特殊的序列化方式保持字段顺序
            String jsonContent = com.alibaba.fastjson.JSON.toJSONString(updatedDraft,
                com.alibaba.fastjson.serializer.SerializerFeature.WriteMapNullValue,
                com.alibaba.fastjson.serializer.SerializerFeature.DisableCircularReferenceDetect);

            // 调用TOS服务覆盖文件
            tosService.overwriteDraftFile(filePath, jsonContent);

            log.info("草稿文件覆盖保存成功: {}", draftUrl);

        } catch (Exception e) {
            log.error("覆盖保存草稿文件失败: {}", draftUrl, e);
            throw new RuntimeException("覆盖保存草稿文件失败: " + e.getMessage(), e);
        }
    }

    /**
     * 从URL中提取文件路径
     */
    private String extractFilePathFromUrl(String url) {
        try {
            // 从TOS URL中提取文件路径
            // 例如：https://aigcview-plub.tos-cn-guangzhou.volces.com//jianying-assistant/drafts/2025/07/04/file.json
            // 提取：/jianying-assistant/drafts/2025/07/04/file.json

            int pathStart = url.indexOf("/jianying-assistant/");
            if (pathStart == -1) {
                throw new RuntimeException("无法从URL中提取文件路径: " + url);
            }

            return url.substring(pathStart);

        } catch (Exception e) {
            log.error("提取文件路径失败: {}", url, e);
            throw new RuntimeException("提取文件路径失败: " + e.getMessage(), e);
        }
    }

    /**
     * 下载音频文件并上传到TOS（公共方法）
     * @return String[] {fileName, downloadUrl}
     */
    public String[] downloadAndUploadAudioPublic(String audioUrl, String audioMaterialId) {
        return downloadAndUploadAudio(audioUrl, audioMaterialId);
    }

    /**
     * 下载音频文件并上传到TOS（使用统一文件夹ID，供add_audios接口使用）
     * @return String[] {fileName, downloadUrl}
     */
    public String[] downloadAndUploadAudioWithUnifiedFolder(String audioUrl, String unifiedFolderId) {
        return downloadAndUploadAudio(audioUrl, unifiedFolderId);
    }

    /**
     * 下载视频文件并上传到TOS（使用统一文件夹ID，供add_videos接口使用）
     * @return String[] {fileName, downloadUrl}
     */
    public String[] downloadAndUploadVideoWithUnifiedFolder(String videoUrl, String unifiedFolderId) {
        return downloadAndUploadVideo(videoUrl, unifiedFolderId);
    }

    /**
     * 下载音频文件并上传到TOS（指定子文件夹）
     * @return String[] {fileName, downloadUrl}
     */
    public String[] downloadAndUploadAudioWithSubFolder(String audioUrl, String audioMaterialId, String subFolderId) {
        return downloadAndUploadAudio(audioUrl, audioMaterialId, subFolderId);
    }

    /**
     * 带容错处理的音频下载方法
     * 支持重试机制、404处理、静音占位符生成等容错功能
     *
     * @param audioUrl 音频文件URL
     * @param audioMaterialId 音频材料ID
     * @return AudioDownloadResult 下载结果（包含成功/失败信息）
     */
    public org.jeecg.modules.jianying.dto.AudioDownloadResult downloadAndUploadAudioWithFallback(String audioUrl, String audioMaterialId) {
        long startTime = System.currentTimeMillis();

        log.info("开始容错下载音频文件: {}", audioUrl);

        // 重试机制：最多3次
        for (int attempt = 1; attempt <= 3; attempt++) {
            try {
                log.info("尝试下载音频文件 (第{}次): {}", attempt, audioUrl);

                // 调用原有的下载方法
                String[] result = downloadAndUploadAudio(audioUrl, audioMaterialId);
                long downloadTime = System.currentTimeMillis() - startTime;

                log.info("音频文件下载成功: {}, 耗时: {}ms", audioUrl, downloadTime);
                return org.jeecg.modules.jianying.dto.AudioDownloadResult.success(result[0], result[1], 0L, downloadTime);

            } catch (Exception e) {
                log.warn("音频文件下载失败 (第{}次): {}, 错误: {}", attempt, audioUrl, e.getMessage());

                // 如果是不可恢复的错误（如404）或最后一次重试，使用占位符
                if (attempt == 3 || isUnrecoverableError(e)) {
                    log.info("使用静音占位符替代失败的音频文件: {}", audioUrl);
                    return createSilentAudioPlaceholder(audioMaterialId, e.getMessage());
                }

                // 指数退避等待
                try {
                    Thread.sleep(attempt * 1000);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }
        }

        // 兜底处理
        log.error("音频文件下载重试失败，使用占位符: {}", audioUrl);
        return createSilentAudioPlaceholder(audioMaterialId, "下载重试失败");
    }

    /**
     * 判断是否为不可恢复的错误
     */
    private boolean isUnrecoverableError(Exception e) {
        String message = e.getMessage().toLowerCase();
        return message.contains("404") ||
               message.contains("not found") ||
               message.contains("forbidden") ||
               message.contains("unauthorized") ||
               message.contains("403");
    }

    /**
     * 创建静音音频占位符
     */
    private org.jeecg.modules.jianying.dto.AudioDownloadResult createSilentAudioPlaceholder(String audioMaterialId, String errorMessage) {
        try {
            log.info("创建静音音频占位符，材料ID: {}, 错误: {}", audioMaterialId, errorMessage);

            // 使用预定义的5秒静音音频（需要预先上传到TOS）
            String silentAudioUrl = tosService.generateFileUrl("silent-audio/silent_5s.mp3");
            String fileName = "silent_placeholder_" + System.currentTimeMillis() + ".mp3";

            log.info("静音占位符创建成功: {}", fileName);
            return org.jeecg.modules.jianying.dto.AudioDownloadResult.placeholder(fileName, silentAudioUrl, errorMessage);

        } catch (Exception e) {
            log.error("创建静音占位符失败", e);
            // 如果连占位符都创建失败，返回一个基本的失败结果
            return org.jeecg.modules.jianying.dto.AudioDownloadResult.failure("创建静音占位符失败: " + e.getMessage());
        }
    }

    /**
     * 下载音频文件并上传到TOS
     * @return String[] {fileName, downloadUrl}
     */
    private String[] downloadAndUploadAudio(String audioUrl, String audioMaterialId) {
        return downloadAndUploadAudio(audioUrl, audioMaterialId, null);
    }

    /**
     * 下载并上传音频文件（支持指定子文件夹ID）
     */
    private String[] downloadAndUploadAudio(String audioUrl, String audioMaterialId, String subFolderId) {
        try {
            log.info("开始下载音频文件: {}", audioUrl);

            // 1. 尝试下载音频文件到临时位置
            byte[] audioData;
            try {
                audioData = downloadAudioFile(audioUrl);
                log.info("音频文件下载成功: {}", audioUrl);
            } catch (Exception e) {
                log.warn("音频文件下载失败，创建空音频文件: {}, 错误: {}", audioUrl, e.getMessage());
                // 创建一个空的音频文件（最小的MP3文件）
                audioData = createEmptyAudioFile();
            }

            // 2. 生成音频文件名（使用UUID确保唯一性）
            String audioFileName = java.util.UUID.randomUUID().toString() + ".mp3";

            // 3. 构建TOS中的音频文件路径（与剪映path格式匹配）
            // 如果没有指定子文件夹ID，则生成一个
            if (subFolderId == null) {
                subFolderId = java.util.UUID.randomUUID().toString();
            }
            // 使用与剪映path匹配的路径结构：0E685133-18CE-45ED-8CB8-2904A212EC80/subFolderId/audioFileName
            String audioFilePath = "/jianying-assistant/drafts/" +
                                 java.time.LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyy/MM/dd/")) +
                                 "0E685133-18CE-45ED-8CB8-2904A212EC80/" + subFolderId + "/" + audioFileName;

            // 4. 上传音频文件到TOS
            tosService.uploadAudioFile(audioFilePath, audioData);

            // 5. 生成音频文件的下载URL（返回系统URL，避免内网TOS签名失败）
            String audioDownloadUrl = tosService.generateSystemFileUrl(audioFilePath);

            log.info("音频文件处理完成: {}, 下载URL: {}", audioFilePath, audioDownloadUrl);
            return new String[]{audioFileName, audioDownloadUrl};

        } catch (Exception e) {
            log.error("音频文件处理失败: {}", audioUrl, e);
            throw new RuntimeException("音频文件处理失败: " + e.getMessage(), e);
        }
    }

    /**
     * 创建空的音频文件（用于下载失败时的占位符）
     */
    private byte[] createEmptyAudioFile() {
        // 创建一个最小的MP3文件头（约1秒的静音）
        // 这是一个有效的MP3文件，但内容为静音
        return new byte[]{
            (byte)0xFF, (byte)0xFB, (byte)0x90, (byte)0x00,
            (byte)0x00, (byte)0x00, (byte)0x00, (byte)0x00,
            (byte)0x00, (byte)0x00, (byte)0x00, (byte)0x00,
            (byte)0x00, (byte)0x00, (byte)0x00, (byte)0x00
        };
    }

    /**
     * 下载音频文件
     */
    private byte[] downloadAudioFile(String audioUrl) {
        try {
            log.info("开始下载音频文件: {}", audioUrl);

            ResponseEntity<byte[]> response = restTemplate.getForEntity(audioUrl, byte[].class);

            if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
                log.info("音频文件下载成功，大小: {} bytes", response.getBody().length);
                return response.getBody();
            } else {
                throw new RuntimeException("音频文件下载失败，状态码: " + response.getStatusCode());
            }

        } catch (Exception e) {
            log.error("下载音频文件失败: {}", audioUrl, e);
            throw new RuntimeException("下载音频文件失败: " + e.getMessage(), e);
        }
    }

    /**
     * 🔒 下载并解析草稿JSON文件（剪映草稿文件优先内网TOS）
     */
    public JSONObject downloadAndParseDraft(String draftUrl) {
        try {
            log.info("开始下载草稿JSON: {}", draftUrl);

            // 🔒 剪映草稿文件必须使用内网TOS（免费流量）
            if (draftUrl.contains("jianying-assistant/drafts/")) {
                log.info("检测到剪映草稿文件，使用内网TOS下载: {}", draftUrl);

                String objectKey;
                if (draftUrl.contains("aigcview.cn") && draftUrl.contains("/sys/common/")) {
                    // 系统URL：提取文件路径
                    objectKey = extractFilePathFromUrl(draftUrl);
                } else {
                    // TOS URL：提取对象键
                    objectKey = extractObjectKeyFromUrl(draftUrl);
                }

                // 直接使用内网TOS SDK下载（不尝试CDN）
                return downloadDraftViaTosSDK(objectKey);
            }

            // 🌐 非剪映草稿文件：优先CDN下载
            if (draftUrl.contains("cdn.aigcview.com")) {
                // CDN URL：使用HTTP下载
                return downloadDraftViaHttp(draftUrl);
            } else if (draftUrl.contains("aigcview.cn") && draftUrl.contains("/sys/common/")) {
                // 系统URL：优先转换为CDN下载
                String filePath = extractFilePathFromUrl(draftUrl);
                if (Boolean.TRUE.equals(cdnEnabled) && oConvertUtils.isNotEmpty(cdnDomain)) {
                    String cdnUrl = cdnDomain + filePath;
                    try {
                        return downloadDraftViaHttp(cdnUrl);
                    } catch (Exception e) {
                        log.warn("CDN下载失败，降级到TOS SDK: {}", e.getMessage());
                    }
                }
                // 降级到TOS SDK下载
                return downloadDraftViaTosSDK(filePath);
            } else {
                // TOS URL：优先转换为CDN下载
                String objectKey = extractObjectKeyFromUrl(draftUrl);
                if (Boolean.TRUE.equals(cdnEnabled) && oConvertUtils.isNotEmpty(cdnDomain)) {
                    String cdnUrl = cdnDomain + "/" + objectKey;
                    try {
                        return downloadDraftViaHttp(cdnUrl);
                    } catch (Exception e) {
                        log.warn("CDN下载失败，降级到TOS SDK: {}", e.getMessage());
                    }
                }
                // 降级到TOS SDK下载
                return downloadDraftViaTosSDK(objectKey);
            }

        } catch (Exception e) {
            log.error("下载并解析草稿失败: {}", draftUrl, e);
            throw new RuntimeException("下载并解析草稿失败: " + e.getMessage(), e);
        }
    }

    /**
     * 添加视频素材到草稿（外链直接引用模式，与add_videos接口一致）
     */
    private void addVideoMaterial(JSONObject draft, String videoUrl, long audioDuration, String unifiedFolderId) {
        try {
            log.info("开始添加视频素材（外链模式）: {}", videoUrl);

            // 生成视频素材ID和段ID（使用统一文件夹ID）
            String videoMaterialId = java.util.UUID.randomUUID().toString();
            String videoSegmentId = java.util.UUID.randomUUID().toString();

            // 🔄 外链优化：直接使用原始URL，不下载不上传
            String originalUrl = "";
            boolean urlValid = false;

            if (videoUrl != null && !videoUrl.trim().isEmpty()) {
                // URL格式验证（与add_videos接口一致）
                if (isValidVideoURL(videoUrl)) {
                    originalUrl = videoUrl;
                    urlValid = true;
                    log.info("视频URL格式验证通过: {}", videoUrl);

                    // 可选的连通性检查（不阻塞处理）
                    if (!checkURLAccessible(videoUrl)) {
                        log.warn("视频URL连通性检查失败: {}", videoUrl);
                    }
                } else {
                    throw new RuntimeException("视频URL格式不正确: " + videoUrl);
                }
            } else {
                log.info("视频URL为空，创建占位符材料");
            }

            // 创建视频材料对象（使用外部URL直接引用模式，与add_videos接口一致）
            JSONObject videoMaterial = createVideoMaterialWithOriginalURL(videoMaterialId, originalUrl, urlValid, unifiedFolderId);

            // 添加到materials.videos
            JSONObject materials = draft.getJSONObject("materials");
            com.alibaba.fastjson.JSONArray videos = materials.getJSONArray("videos");
            if (videos == null) {
                videos = new com.alibaba.fastjson.JSONArray();
                materials.put("videos", videos);
            }

            videos.add(videoMaterial);

            // 添加视频段到第一个video轨道
            com.alibaba.fastjson.JSONArray tracks = draft.getJSONArray("tracks");
            JSONObject videoTrack = tracks.getJSONObject(0); // 第一个轨道是video轨道
            com.alibaba.fastjson.JSONArray segments = videoTrack.getJSONArray("segments");
            if (segments == null) {
                segments = new com.alibaba.fastjson.JSONArray();
                videoTrack.put("segments", segments);
            }

            // 创建视频段
            JSONObject videoSegment = createSegment(videoSegmentId, videoMaterialId, audioDuration);
            segments.add(videoSegment);

            log.info("成功添加视频素材，ID: {}", videoMaterialId);

        } catch (Exception e) {
            log.error("添加视频素材失败", e);
            throw new RuntimeException("添加视频素材失败: " + e.getMessage(), e);
        }
    }

    /**
     * 下载视频文件并上传到TOS
     */
    private String[] downloadAndUploadVideo(String videoUrl, String folderId) {
        try {
            log.info("开始下载视频文件: {}", videoUrl);

            // 下载视频文件
            byte[] videoData = downloadFile(videoUrl);

            // 生成视频文件名
            String videoFileName = java.util.UUID.randomUUID().toString() + ".mp4";

            // 上传到TOS（使用现有的uploadAudioFile方法，它可以上传任何文件）
            String videoPath = "/jianying-assistant/drafts/" +
                java.time.LocalDate.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyy/MM/dd")) +
                "/" + folderId + "/" + videoFileName;

            tosService.uploadAudioFile(videoPath, videoData);

            // 返回系统内部路径，不暴露TOS签名URL
            String internalPath = videoPath;

            log.info("视频文件上传成功，内部路径: {}", internalPath);
            return new String[]{videoFileName, internalPath};

        } catch (Exception e) {
            log.error("下载并上传视频文件失败: {}", videoUrl, e);
            throw new RuntimeException("下载并上传视频文件失败: " + e.getMessage(), e);
        }
    }

    /**
     * 下载文件的通用方法（TOS文件使用SDK，非TOS文件使用HTTP）
     */
    private byte[] downloadFile(String fileUrl) {
        try {
            log.info("开始下载文件: {}", fileUrl);

            // 判断是否为TOS文件（支持新旧端点）
            if (fileUrl.contains("tos-cn-guangzhou.volces.com") ||
                fileUrl.contains("tos-cn-shanghai.volces.com")) {
                return downloadTosFile(fileUrl);
            } else {
                return downloadFileWithHttp(fileUrl);
            }

        } catch (Exception e) {
            log.error("下载文件失败: {}", fileUrl, e);
            throw new RuntimeException("文件下载失败: " + e.getMessage(), e);
        }
    }

    /**
     * 🔒 使用TOS SDK下载剪映文件（仅限剪映文件）
     */
    private byte[] downloadTosFile(String fileUrl) {
        try {
            log.info("使用TOS SDK下载剪映文件: {}", fileUrl);

            // 从URL中提取文件路径
            String filePath = extractFilePathFromUrl(fileUrl);
            String cleanPath = filePath.startsWith("/") ? filePath.substring(1) : filePath;

            // 🔒 安全检查：确保只下载剪映文件
            if (!cleanPath.startsWith("jianying-assistant/")) {
                log.error("🚨 安全拦截：此方法只能下载剪映文件: {}", cleanPath);
                throw new RuntimeException("安全拦截：此方法只能下载剪映文件");
            }

            // 使用TOS SDK下载剪映文件内容
            String content = tosService.downloadDraftFile(cleanPath);
            byte[] fileData = content.getBytes(StandardCharsets.UTF_8);

            log.info("TOS SDK下载剪映文件成功，大小: {} bytes", fileData.length);
            return fileData;

        } catch (Exception e) {
            log.error("TOS SDK下载剪映文件失败: {}", fileUrl, e);
            throw new RuntimeException("TOS SDK下载失败: " + e.getMessage(), e);
        }
    }

    /**
     * 使用HTTP方式下载文件（保留原有逻辑作为降级方案）
     */
    private byte[] downloadFileWithHttp(String fileUrl) {
        try {
            log.info("使用HTTP方式下载文件: {}", fileUrl);

            // 使用原生URLConnection，完全模拟浏览器行为
            java.net.URL url = new java.net.URL(fileUrl);
            java.net.HttpURLConnection connection = (java.net.HttpURLConnection) url.openConnection();

            // 设置完整的浏览器请求头
            connection.setRequestMethod("GET");
            connection.setRequestProperty("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36");
            connection.setRequestProperty("Accept", "*/*");
            connection.setRequestProperty("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8");
            connection.setRequestProperty("Accept-Encoding", "gzip, deflate, br");
            connection.setRequestProperty("Connection", "keep-alive");
            connection.setRequestProperty("Upgrade-Insecure-Requests", "1");
            connection.setRequestProperty("Sec-Fetch-Dest", "document");
            connection.setRequestProperty("Sec-Fetch-Mode", "navigate");
            connection.setRequestProperty("Sec-Fetch-Site", "none");
            connection.setRequestProperty("Sec-Fetch-User", "?1");
            connection.setRequestProperty("Cache-Control", "max-age=0");

            // 设置超时
            connection.setConnectTimeout(30000); // 30秒连接超时
            connection.setReadTimeout(60000);    // 60秒读取超时

            // 发送请求
            int responseCode = connection.getResponseCode();
            log.info("响应状态码: {}", responseCode);

            if (responseCode == 200) {
                // 读取响应数据
                try (java.io.InputStream inputStream = connection.getInputStream();
                     java.io.ByteArrayOutputStream outputStream = new java.io.ByteArrayOutputStream()) {

                    byte[] buffer = new byte[8192];
                    int bytesRead;
                    while ((bytesRead = inputStream.read(buffer)) != -1) {
                        outputStream.write(buffer, 0, bytesRead);
                    }

                    byte[] fileData = outputStream.toByteArray();
                    log.info("文件下载成功，大小: {} bytes", fileData.length);
                    return fileData;
                }
            } else {
                // 读取错误响应（兼容Java 8）
                String errorResponse = "";
                try (java.io.InputStream errorStream = connection.getErrorStream()) {
                    if (errorStream != null) {
                        java.io.ByteArrayOutputStream errorOutput = new java.io.ByteArrayOutputStream();
                        byte[] buffer = new byte[1024];
                        int bytesRead;
                        while ((bytesRead = errorStream.read(buffer)) != -1) {
                            errorOutput.write(buffer, 0, bytesRead);
                        }
                        errorResponse = new String(errorOutput.toByteArray(), "UTF-8");
                    }
                } catch (Exception e) {
                    log.warn("读取错误响应失败: {}", e.getMessage());
                }

                throw new RuntimeException("文件下载失败，状态码: " + responseCode + ", 错误信息: " + errorResponse);
            }

        } catch (Exception e) {
            log.error("HTTP下载文件失败: {}", fileUrl, e);
            throw new RuntimeException("HTTP下载失败: " + e.getMessage(), e);
        }
    }



    /**
     * 创建轨道段的通用方法
     */
    private JSONObject createSegment(String segmentId, String materialId, long duration) {
        JSONObject segment = new JSONObject(new java.util.LinkedHashMap<>());

        segment.put("caption_info", null);
        segment.put("cartoon", false);
        segment.put("clip", new JSONObject(new java.util.LinkedHashMap<>()) {{
            put("alpha", 1);
            put("flip", new JSONObject(new java.util.LinkedHashMap<>()) {{
                put("horizontal", false);
                put("vertical", false);
            }});
            put("rotation", 0);
            put("scale", new JSONObject(new java.util.LinkedHashMap<>()) {{
                put("x", 1);
                put("y", 1);
            }});
            put("transform", new JSONObject(new java.util.LinkedHashMap<>()) {{
                put("x", 0);
                put("y", 0);
            }});
        }});
        segment.put("common_keyframes", new com.alibaba.fastjson.JSONArray());
        segment.put("enable_adjust", true);
        segment.put("enable_color_correct_adjust", false);
        segment.put("enable_color_curves", true);
        segment.put("enable_color_match_adjust", false);
        segment.put("enable_color_wheels", true);
        segment.put("enable_lut", true);
        segment.put("enable_smart_color_adjust", false);
        segment.put("extra_material_refs", new com.alibaba.fastjson.JSONArray());
        segment.put("group_id", "");
        segment.put("hdr_settings", new JSONObject(new java.util.LinkedHashMap<>()) {{
            put("intensity", 1);
            put("mode", 1);
            put("nits", 1000);
        }});
        segment.put("id", segmentId);
        segment.put("intensifies_audio", false);
        segment.put("is_placeholder", false);
        segment.put("is_tone_modify", false);
        segment.put("keyframe_refs", new com.alibaba.fastjson.JSONArray());
        segment.put("last_nonzero_volume", 1);
        segment.put("material_id", materialId);
        segment.put("render_index", 1);
        segment.put("responsive_layout", new JSONObject(new java.util.LinkedHashMap<>()) {{
            put("enable", false);
            put("horizontal_pos_layout", 0);
            put("size_layout", 0);
            put("target_follow", "");
            put("vertical_pos_layout", 0);
        }});
        segment.put("reverse", false);
        segment.put("source_timerange", new JSONObject(new java.util.LinkedHashMap<>()) {{
            put("duration", duration);
            put("start", 0);
        }});
        segment.put("speed", 1);
        segment.put("target_timerange", new JSONObject(new java.util.LinkedHashMap<>()) {{
            put("duration", duration);
            put("start", 0);
        }});
        segment.put("template_id", "");
        segment.put("template_scene", "default");
        segment.put("track_attribute", 0);
        segment.put("track_render_index", 3);
        segment.put("uniform_scale", new JSONObject(new java.util.LinkedHashMap<>()) {{
            put("on", true);
            put("value", 1);
        }});
        segment.put("visible", true);
        segment.put("volume", 1);

        return segment;
    }

    /**
     * 创建文本段（专门为easy_create_material接口设计，完全模仿add_captions接口）
     */
    private JSONObject createTextSegmentForEasyCreate(String segmentId, String textMaterialId, long duration, Double transformX, Double transformY) {
        log.info("🔍 createTextSegmentForEasyCreate调试 - 接收到的坐标参数: transformX={}, transformY={}", transformX, transformY);

        JSONObject segment = new JSONObject(new java.util.LinkedHashMap<>());

        // 基础字段（完全复制自add_captions接口）
        segment.put("caption_info", null);
        segment.put("cartoon", false);

        // 片段剪辑信息（完全复制自add_captions接口的clip结构）
        segment.put("clip", new JSONObject(new java.util.LinkedHashMap<>()) {{
            put("alpha", 1);
            put("flip", new JSONObject(new java.util.LinkedHashMap<>()) {{
                put("horizontal", false);
                put("vertical", false);
            }});
            put("rotation", 0);
            put("scale", new JSONObject(new java.util.LinkedHashMap<>()) {{
                put("x", 1);
                put("y", 1);
            }});
            put("transform", new JSONObject(new java.util.LinkedHashMap<>()) {{
                // 设置位置 - 使用剪映原生默认值（完全模仿add_captions接口）
                put("x", 0);  // 剪映原生默认值
                put("y", 0);  // 剪映原生默认值

                log.info("🔍 clip.transform设置前检查: transformX={}, transformY={}", transformX, transformY);

                // 只在用户明确提供X和Y坐标时才覆盖默认值（与add_captions接口完全一致）
                if (transformX != null && transformY != null) {
                    put("x", transformX);  // 使用用户指定的归一化坐标
                    put("y", transformY);  // 使用用户指定的归一化坐标
                    log.info("✅ clip.transform已设置用户坐标: x={}, y={}", transformX, transformY);
                } else {
                    log.warn("❌ clip.transform使用默认坐标(0,0): transformX={}, transformY={}", transformX, transformY);
                }
            }});
        }});

        // 其他标准字段（完全复制自add_captions接口）
        segment.put("common_keyframes", new com.alibaba.fastjson.JSONArray());
        segment.put("enable_adjust", true);
        segment.put("enable_color_correct_adjust", false);
        segment.put("enable_color_curves", true);
        segment.put("enable_color_match_adjust", false);
        segment.put("enable_color_wheels", true);
        segment.put("enable_lut", true);
        segment.put("enable_smart_color_adjust", false);

        // 关联动画材料（暂时为空，后续可扩展）
        segment.put("extra_material_refs", new com.alibaba.fastjson.JSONArray());

        segment.put("group_id", "");

        // HDR设置（完全复制自add_captions接口）
        segment.put("hdr_settings", new JSONObject(new java.util.LinkedHashMap<>()) {{
            put("intensity", 1);
            put("mode", 1);
            put("nits", 1000);
        }});

        segment.put("id", segmentId);
        segment.put("intensifies_audio", false);
        segment.put("is_placeholder", false);
        segment.put("is_tone_modify", false);
        segment.put("keyframe_refs", new com.alibaba.fastjson.JSONArray());
        segment.put("last_nonzero_volume", 1);
        segment.put("material_id", textMaterialId);
        segment.put("render_index", 1); // 字幕使用render_index=1（与add_captions一致）
        segment.put("reverse", false);
        segment.put("source_timerange", new JSONObject(new java.util.LinkedHashMap<>()) {{
            put("duration", duration);
            put("start", 0L);
        }});
        segment.put("speed", 1.0);
        segment.put("target_timerange", new JSONObject(new java.util.LinkedHashMap<>()) {{
            put("duration", duration);
            put("start", 0L);  // 文本从0开始显示
        }});
        segment.put("template_id", "");
        segment.put("template_scene", "default");
        segment.put("track_attribute", 0);
        segment.put("track_render_index", 3); // 文本轨道使用3（与add_captions一致）

        // uniform_scale字段（完全复制自add_captions接口）
        segment.put("uniform_scale", new JSONObject(new java.util.LinkedHashMap<>()) {{
            put("on", true);
            put("value", 1);
        }});

        // 响应式布局（完全复制自add_captions接口）
        segment.put("responsive_layout", new JSONObject(new java.util.LinkedHashMap<>()) {{
            put("enable", false);
            put("horizontal_pos_layout", 0);
            put("size_layout", 0);
            put("target_follow", "");
            put("vertical_pos_layout", 0);
        }});

        segment.put("visible", true);
        segment.put("volume", 1.0);

        return segment;
    }

    /**
     * 添加图片素材到草稿
     */
    private void addImageMaterial(JSONObject draft, String imgUrl, long audioDuration, String unifiedFolderId) {
        try {
            log.info("开始添加图片素材: {}", imgUrl);

            // 生成图片素材ID和段ID（使用统一文件夹ID）
            String imageMaterialId = java.util.UUID.randomUUID().toString();
            String imageSegmentId = java.util.UUID.randomUUID().toString();

            // 使用外部URL直接引用模式（与add_images接口一致）
            String originalUrl = imgUrl;
            boolean urlValid = false;

            if (originalUrl != null && !originalUrl.trim().isEmpty()) {
                // 验证URL格式
                urlValid = isValidImageURL(originalUrl);
                if (urlValid) {
                    // 可选：检查URL连通性（5秒超时）
                    boolean accessible = checkURLAccessible(originalUrl);
                    if (!accessible) {
                        log.warn("图片URL无法访问，但仍将使用: {}", originalUrl);
                    }
                } else {
                    log.warn("图片URL格式无效: {}", originalUrl);
                }
            } else {
                log.info("图片URL为空，创建占位符材料");
            }

            // 创建图片材料对象（使用外部URL直接引用模式，与add_images接口一致）
            JSONObject imageMaterial = createImageMaterialWithOriginalURL(imageMaterialId, originalUrl, urlValid, unifiedFolderId);

            // 添加到materials.videos（图片也放在videos数组中）
            JSONObject materials = draft.getJSONObject("materials");
            com.alibaba.fastjson.JSONArray videos = materials.getJSONArray("videos");
            if (videos == null) {
                videos = new com.alibaba.fastjson.JSONArray();
                materials.put("videos", videos);
            }

            videos.add(imageMaterial);

            // 添加图片段到第三个轨道（根据竞争对手的结构）
            com.alibaba.fastjson.JSONArray tracks = draft.getJSONArray("tracks");

            // 确保有足够的轨道（按竞争对手格式）
            while (tracks.size() < 3) {
                JSONObject newTrack = new JSONObject(new java.util.LinkedHashMap<>());
                newTrack.put("attribute", 0);

                // 根据轨道位置设置flag和type
                if (tracks.size() == 0) {
                    newTrack.put("flag", 0); // 第一个video轨道flag=0
                    newTrack.put("type", "video");
                } else if (tracks.size() == 1) {
                    newTrack.put("flag", 3); // audio轨道flag=3
                    newTrack.put("type", "audio");
                } else {
                    newTrack.put("flag", 3); // 其他轨道flag=3
                    newTrack.put("type", "video");
                }

                newTrack.put("id", java.util.UUID.randomUUID().toString());
                newTrack.put("segments", new com.alibaba.fastjson.JSONArray());
                tracks.add(newTrack);
            }

            JSONObject imageTrack = tracks.getJSONObject(2); // 第三个轨道用于图片
            com.alibaba.fastjson.JSONArray segments = imageTrack.getJSONArray("segments");
            if (segments == null) {
                segments = new com.alibaba.fastjson.JSONArray();
                imageTrack.put("segments", segments);
            }

            // 创建图片段
            JSONObject imageSegment = createSegment(imageSegmentId, imageMaterialId, audioDuration);
            segments.add(imageSegment);

            log.info("成功添加图片素材，ID: {}", imageMaterialId);

        } catch (Exception e) {
            log.error("添加图片素材失败", e);
            throw new RuntimeException("添加图片素材失败: " + e.getMessage(), e);
        }
    }

    /**
     * 下载图片文件并上传到TOS
     */
    public String[] downloadAndUploadImage(String imgUrl, String folderId) {
        try {
            log.info("开始下载图片文件: {}", imgUrl);

            // 下载图片文件
            byte[] imageData = downloadFile(imgUrl);

            // 生成图片文件名（根据URL判断扩展名）
            String extension = ".png"; // 默认png
            if (imgUrl.toLowerCase().contains(".jpg") || imgUrl.toLowerCase().contains(".jpeg")) {
                extension = ".jpg";
            } else if (imgUrl.toLowerCase().contains(".gif")) {
                extension = ".gif";
            } else if (imgUrl.toLowerCase().contains(".webp")) {
                extension = ".webp";
            }

            String imageFileName = java.util.UUID.randomUUID().toString() + extension;

            // 上传到TOS
            String imagePath = "/jianying-assistant/drafts/" +
                java.time.LocalDate.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyy/MM/dd")) +
                "/" + folderId + "/" + imageFileName;

            tosService.uploadAudioFile(imagePath, imageData);

            // 生成下载URL（返回系统URL，避免内网TOS签名失败）
            String imageDownloadUrl = tosService.generateSystemFileUrl(imagePath);

            log.info("图片文件上传成功: {}", imageDownloadUrl);
            return new String[]{imageFileName, imageDownloadUrl};

        } catch (Exception e) {
            log.error("下载并上传图片文件失败: {}", imgUrl, e);
            throw new RuntimeException("下载并上传图片文件失败: " + e.getMessage(), e);
        }
    }

    /**
     * 添加文本素材到草稿
     */
    private void addTextMaterial(JSONObject draft, String text, String textColor, Integer fontSize,
                                Double textTransformX, Double textTransformY, long audioDuration) {
        try {
            log.info("开始添加文本素材: {}", text);

            // 生成文本素材ID和段ID
            String textMaterialId = java.util.UUID.randomUUID().toString();
            String textSegmentId = java.util.UUID.randomUUID().toString();

            // 添加到materials.texts
            JSONObject materials = draft.getJSONObject("materials");
            com.alibaba.fastjson.JSONArray texts = materials.getJSONArray("texts");
            if (texts == null) {
                texts = new com.alibaba.fastjson.JSONArray();
                materials.put("texts", texts);
            }

            // 处理文本颜色（转换为RGB数组）
            double[] colorRgb = parseTextColor(textColor);

            // 处理字体大小
            int finalFontSize = fontSize != null ? fontSize : 15; // 默认15

            // 创建文本内容JSON（参考竞争对手格式）
            JSONObject contentJson = new JSONObject(new java.util.LinkedHashMap<>());
            contentJson.put("text", text);

            com.alibaba.fastjson.JSONArray styles = new com.alibaba.fastjson.JSONArray();
            JSONObject style = new JSONObject(new java.util.LinkedHashMap<>());
            style.put("range", new int[]{0, text.length()});
            style.put("size", finalFontSize);

            JSONObject font = new JSONObject(new java.util.LinkedHashMap<>());
            font.put("id", "");
            font.put("path", "");
            style.put("font", font);

            JSONObject fill = new JSONObject(new java.util.LinkedHashMap<>());
            JSONObject content = new JSONObject(new java.util.LinkedHashMap<>());
            JSONObject solid = new JSONObject(new java.util.LinkedHashMap<>());
            solid.put("color", colorRgb);
            content.put("solid", solid);
            fill.put("content", content);
            style.put("fill", fill);

            styles.add(style);
            contentJson.put("styles", styles);

            // 创建文本素材（参考竞争对手格式）
            JSONObject textMaterial = new JSONObject(new java.util.LinkedHashMap<>());
            textMaterial.put("add_type", 0);
            textMaterial.put("alignment", 1);
            textMaterial.put("background_alpha", 1);
            textMaterial.put("background_color", "#000000");
            textMaterial.put("background_height", 1);
            textMaterial.put("background_horizontal_offset", 0);
            textMaterial.put("background_round_radius", 0);
            textMaterial.put("background_style", 0);
            textMaterial.put("background_vertical_offset", 0);
            textMaterial.put("background_width", 1);
            textMaterial.put("bold_width", 0);
            textMaterial.put("border_alpha", 1);
            textMaterial.put("border_color", "#000000");
            textMaterial.put("border_width", 0.04);
            textMaterial.put("check_flag", 7);
            textMaterial.put("combo_info", new JSONObject(new java.util.LinkedHashMap<>()) {{
                put("text_templates", new com.alibaba.fastjson.JSONArray());
            }});
            textMaterial.put("content", contentJson.toJSONString());
            textMaterial.put("fixed_height", -1);
            textMaterial.put("fixed_width", -1);
            textMaterial.put("force_apply_line_max_width", false);
            textMaterial.put("global_alpha", 1);
            textMaterial.put("group_id", "");
            textMaterial.put("has_shadow", false);
            textMaterial.put("id", textMaterialId);
            textMaterial.put("initial_scale", 1);
            textMaterial.put("is_rich_text", false);
            textMaterial.put("italic_degree", 0);
            textMaterial.put("ktv_color", "#FFFFFF");
            textMaterial.put("language", "");
            textMaterial.put("layer_weight", 1);
            textMaterial.put("letter_spacing", 0);
            textMaterial.put("line_spacing", 0.02);
            textMaterial.put("multi_language_current", "none");
            textMaterial.put("preset_category", "");
            textMaterial.put("preset_category_id", "");
            textMaterial.put("preset_has_set_alignment", false);
            textMaterial.put("preset_id", "");
            textMaterial.put("preset_index", 0);
            textMaterial.put("preset_name", "");
            textMaterial.put("recognize_task_id", "");
            textMaterial.put("recognize_type", 0);
            textMaterial.put("relevance_segment", new com.alibaba.fastjson.JSONArray());
            textMaterial.put("shadow_alpha", 0.8);
            textMaterial.put("shadow_angle", 315);
            textMaterial.put("shadow_color", "#000000");
            textMaterial.put("shadow_distance", 5);
            textMaterial.put("shadow_point", new JSONObject(new java.util.LinkedHashMap<>()) {{
                put("x", 1);
                put("y", 1);
            }});
            textMaterial.put("shadow_smoothing", 1);
            textMaterial.put("shape_clip_x", false);
            textMaterial.put("shape_clip_y", false);
            textMaterial.put("style_name", "");
            textMaterial.put("sub_type", 0);
            textMaterial.put("text_alpha", 1);
            textMaterial.put("text_color", textColor != null ? textColor : "red");
            textMaterial.put("text_curve", null);
            textMaterial.put("text_preset_resource_id", "");
            textMaterial.put("text_size", finalFontSize);
            textMaterial.put("text_to_audio_ids", new com.alibaba.fastjson.JSONArray());
            textMaterial.put("tts_auto_update", false);
            textMaterial.put("type", "subtitle");
            textMaterial.put("typesetting", 0);
            textMaterial.put("underline", false);
            textMaterial.put("underline_offset", 0.22);
            textMaterial.put("underline_width", 0.05);
            textMaterial.put("use_effect_default_color", true);
            textMaterial.put("words", new com.alibaba.fastjson.JSONArray());

            texts.add(textMaterial);

            // 创建专门的文本轨道（与add_captions接口保持一致）
            com.alibaba.fastjson.JSONArray tracks = draft.getJSONArray("tracks");

            // 创建新的文本轨道（完全模仿add_captions接口的createTextTrack方法）
            String textTrackId = java.util.UUID.randomUUID().toString();
            JSONObject textTrack = new JSONObject(new java.util.LinkedHashMap<>());
            textTrack.put("attribute", 0);
            textTrack.put("flag", 3);  // 文字轨道标志（与add_captions一致）
            textTrack.put("id", textTrackId);
            textTrack.put("segments", new com.alibaba.fastjson.JSONArray());
            textTrack.put("type", "text");

            // 添加到tracks数组末尾
            tracks.add(textTrack);
            com.alibaba.fastjson.JSONArray segments = textTrack.getJSONArray("segments");
            if (segments == null) {
                segments = new com.alibaba.fastjson.JSONArray();
                textTrack.put("segments", segments);
            }

            // 获取画布尺寸作为坐标转换基准（与add_captions接口一致）
            JSONObject canvasConfig = draft.getJSONObject("canvas_config");
            int canvasWidth = canvasConfig != null ? canvasConfig.getIntValue("width") : 1080;
            int canvasHeight = canvasConfig != null ? canvasConfig.getIntValue("height") : 1920;

            // 确保画布尺寸有效（修正默认值为竖屏格式）
            if (canvasWidth <= 0) {
                log.warn("画布宽度无效: {}, 使用默认值1080", canvasWidth);
                canvasWidth = 1080;
            }
            if (canvasHeight <= 0) {
                log.warn("画布高度无效: {}, 使用默认值1920", canvasHeight);
                canvasHeight = 1920;
            }

            // 坐标转换逻辑（完全模仿add_captions接口的条件判断）
            Double relativeX = null;
            Double relativeY = null;

            log.info("🔍 调试信息 - 文本坐标参数检查: textTransformX={}, textTransformY={}, 画布尺寸: {}x{}",
                     textTransformX, textTransformY, canvasWidth, canvasHeight);

            // 只在用户明确提供X和Y坐标时才进行坐标转换（与add_captions接口完全一致）
            if (textTransformX != null && textTransformY != null) {
                // 转换用户输入的像素坐标为剪映归一化坐标
                relativeX = textTransformX / (double)canvasWidth;
                relativeY = textTransformY / (double)canvasHeight;
                log.info("✅ 文本坐标转换: 用户输入X({})像素->归一化({}), Y({})像素->归一化({}), 画布尺寸: {}x{}",
                         textTransformX, relativeX, textTransformY, relativeY, canvasWidth, canvasHeight);
            } else {
                log.warn("❌ 文本坐标参数不完整，使用剪映原生默认坐标: (0.0, 0.0) - textTransformX={}, textTransformY={}",
                         textTransformX, textTransformY);
            }

            // 创建文本段（完全模仿add_captions接口的createTextSegmentObject方法）
            JSONObject textSegment = createTextSegmentForEasyCreate(textSegmentId, textMaterialId, audioDuration, relativeX, relativeY);

            segments.add(textSegment);

            log.info("成功添加文本素材，ID: {}, 内容: {}", textMaterialId, text);

        } catch (Exception e) {
            log.error("添加文本素材失败", e);
            throw new RuntimeException("添加文本素材失败: " + e.getMessage(), e);
        }
    }

    /**
     * 解析文本颜色为RGB数组（通用颜色转换器）
     */
    private double[] parseTextColor(String textColor) {
        if (textColor == null || textColor.trim().isEmpty()) {
            return new double[]{1, 1, 1}; // 默认白色
        }

        String originalColor = textColor;
        textColor = textColor.toLowerCase().trim();

        // 1. 尝试解析RGB格式（支持多种格式）
        double[] rgbResult = parseRgbFormat(textColor, originalColor);
        if (rgbResult != null) {
            return rgbResult;
        }

        // 2. 尝试解析十六进制颜色（带#前缀）
        if (textColor.startsWith("#") && textColor.length() == 7) {
            try {
                int r = Integer.parseInt(textColor.substring(1, 3), 16);
                int g = Integer.parseInt(textColor.substring(3, 5), 16);
                int b = Integer.parseInt(textColor.substring(5, 7), 16);
                log.debug("解析十六进制颜色成功: {} -> ({},{},{})", originalColor, r, g, b);
                return new double[]{r / 255.0, g / 255.0, b / 255.0};
            } catch (NumberFormatException e) {
                log.warn("无法解析十六进制颜色: {}", originalColor);
            }
        }

        // 3. 尝试解析十六进制颜色（无#前缀）
        if (textColor.length() == 6 && textColor.matches("[0-9a-f]{6}")) {
            try {
                int r = Integer.parseInt(textColor.substring(0, 2), 16);
                int g = Integer.parseInt(textColor.substring(2, 4), 16);
                int b = Integer.parseInt(textColor.substring(4, 6), 16);
                log.debug("解析十六进制颜色成功（无#）: {} -> ({},{},{})", originalColor, r, g, b);
                return new double[]{r / 255.0, g / 255.0, b / 255.0};
            } catch (NumberFormatException e) {
                log.warn("无法解析十六进制颜色: {}", originalColor);
            }
        }

        // 4. 常用颜色名称映射（扩展更多英文颜色）
        double[] colorResult = parseColorName(textColor);
        if (colorResult != null) {
            log.debug("解析颜色名称成功: {} -> RGB", originalColor);
            return colorResult;
        }

        // 5. 如果都无法解析，使用默认白色并记录警告
        log.warn("无法识别的颜色格式: {}，支持格式：RGB(255,0,0)或255,0,0、十六进制#FF0000或FF0000、常用颜色名称", originalColor);
        return new double[]{1, 1, 1}; // 默认白色
    }

    /**
     * 解析RGB格式（支持多种格式）
     */
    private double[] parseRgbFormat(String textColor, String originalColor) {
        String rgbValues = null;

        // 格式1: (255,0,0) 或 (255，0，0)
        if (textColor.startsWith("(") && textColor.endsWith(")")) {
            rgbValues = textColor.substring(1, textColor.length() - 1);
        }
        // 格式2: 255,0,0 或 255，0，0
        else if (textColor.contains(",") || textColor.contains("，")) {
            rgbValues = textColor;
        }

        if (rgbValues != null) {
            try {
                // 支持中文逗号和英文逗号
                String[] parts = rgbValues.replace("，", ",").split(",");
                if (parts.length == 3) {
                    int r = Integer.parseInt(parts[0].trim());
                    int g = Integer.parseInt(parts[1].trim());
                    int b = Integer.parseInt(parts[2].trim());

                    // 验证RGB值范围
                    if (r >= 0 && r <= 255 && g >= 0 && g <= 255 && b >= 0 && b <= 255) {
                        log.debug("解析RGB颜色成功: {} -> ({},{},{})", originalColor, r, g, b);
                        return new double[]{r / 255.0, g / 255.0, b / 255.0};
                    }
                }
            } catch (NumberFormatException e) {
                log.warn("无法解析RGB颜色: {}", originalColor);
            }
        }

        return null;
    }

    /**
     * 解析常用颜色名称（扩展版本）
     */
    private double[] parseColorName(String colorName) {
        switch (colorName) {
            // 基础颜色
            case "red": return new double[]{1, 0, 0};
            case "green": return new double[]{0, 1, 0};
            case "blue": return new double[]{0, 0, 1};
            case "white": return new double[]{1, 1, 1};
            case "black": return new double[]{0, 0, 0};
            case "yellow": return new double[]{1, 1, 0};
            case "cyan": return new double[]{0, 1, 1};
            case "magenta": return new double[]{1, 0, 1};

            // 扩展颜色
            case "orange": return new double[]{1, 0.647, 0}; // #FFA500
            case "purple": return new double[]{0.502, 0, 0.502}; // #800080
            case "pink": return new double[]{1, 0.753, 0.796}; // #FFC0CB
            case "brown": return new double[]{0.647, 0.165, 0.165}; // #A52A2A
            case "gray": case "grey": return new double[]{0.502, 0.502, 0.502}; // #808080
            case "silver": return new double[]{0.753, 0.753, 0.753}; // #C0C0C0
            case "gold": return new double[]{1, 0.843, 0}; // #FFD700
            case "lime": return new double[]{0, 1, 0}; // #00FF00
            case "navy": return new double[]{0, 0, 0.502}; // #000080
            case "teal": return new double[]{0, 0.502, 0.502}; // #008080
            case "olive": return new double[]{0.502, 0.502, 0}; // #808000
            case "maroon": return new double[]{0.502, 0, 0}; // #800000
            case "aqua": return new double[]{0, 1, 1}; // #00FFFF
            case "fuchsia": return new double[]{1, 0, 1}; // #FF00FF
            case "violet": return new double[]{0.933, 0.510, 0.933}; // #EE82EE
            case "indigo": return new double[]{0.294, 0, 0.510}; // #4B0082
            case "turquoise": return new double[]{0.251, 0.878, 0.816}; // #40E0D0
            case "coral": return new double[]{1, 0.498, 0.314}; // #FF7F50
            case "salmon": return new double[]{0.980, 0.502, 0.447}; // #FA8072
            case "khaki": return new double[]{0.941, 0.902, 0.549}; // #F0E68C
            case "crimson": return new double[]{0.863, 0.078, 0.235}; // #DC143C
            case "chocolate": return new double[]{0.824, 0.412, 0.118}; // #D2691E
            case "tan": return new double[]{0.824, 0.706, 0.549}; // #D2B48C
            case "beige": return new double[]{0.961, 0.961, 0.863}; // #F5F5DC
            case "ivory": return new double[]{1, 1, 0.941}; // #FFFFF0
            case "lavender": return new double[]{0.902, 0.902, 0.980}; // #E6E6FA
            case "plum": return new double[]{0.867, 0.627, 0.867}; // #DDA0DD
            case "orchid": return new double[]{0.855, 0.439, 0.839}; // #DA70D6
            case "thistle": return new double[]{0.847, 0.749, 0.847}; // #D8BFD8
            case "snow": return new double[]{1, 0.980, 0.980}; // #FFFAFA
            case "linen": return new double[]{0.980, 0.941, 0.902}; // #FAF0E6
            case "azure": return new double[]{0.941, 1, 1}; // #F0FFFF
            case "mint": return new double[]{0.596, 1, 0.596}; // #98FB98

            default: return null;
        }
    }

    /**
     * 提取或创建统一的文件夹ID（关键修复方法）
     */
    public String extractOrCreateUnifiedFolderId(JSONObject draft) {
        try {
            // 第1步：尝试从现有音频素材中提取文件夹ID
            JSONObject materials = draft.getJSONObject("materials");
            if (materials != null) {
                com.alibaba.fastjson.JSONArray audios = materials.getJSONArray("audios");
                if (audios != null && audios.size() > 0) {
                    JSONObject firstAudio = audios.getJSONObject(0);
                    String path = firstAudio.getString("path");
                    if (path != null && path.contains("##_draftpath_placeholder_")) {
                        // 提取文件夹ID：##_draftpath_placeholder_xxx_##\folderId\filename
                        String[] parts = path.split("\\\\");
                        if (parts.length >= 2) {
                            String folderId = parts[1]; // 第二部分是文件夹ID
                            log.info("从现有音频素材中提取文件夹ID: {}", folderId);
                            return folderId;
                        }
                    }
                }

                // 第2步：尝试从现有视频素材中提取文件夹ID
                com.alibaba.fastjson.JSONArray videos = materials.getJSONArray("videos");
                if (videos != null && videos.size() > 0) {
                    JSONObject firstVideo = videos.getJSONObject(0);
                    String path = firstVideo.getString("path");
                    if (path != null && path.contains("##_draftpath_placeholder_")) {
                        String[] parts = path.split("\\\\");
                        if (parts.length >= 2) {
                            String folderId = parts[1];
                            log.info("从现有视频素材中提取文件夹ID: {}", folderId);
                            return folderId;
                        }
                    }
                }
            }

            // 第3步：如果没有现有素材，创建新的文件夹ID
            String newFolderId = java.util.UUID.randomUUID().toString();
            log.info("创建新的统一文件夹ID: {}", newFolderId);
            return newFolderId;

        } catch (Exception e) {
            log.warn("提取文件夹ID失败，创建新的: {}", e.getMessage());
            return java.util.UUID.randomUUID().toString();
        }
    }

    /**
     * 🆕 通过HTTP下载草稿（CDN方式）- 🔒 禁止访问剪映草稿文件
     */
    private JSONObject downloadDraftViaHttp(String url) {
        try {
            // 🔒 安全检查：禁止通过CDN访问剪映草稿文件
            if (url.contains("jianying-assistant/drafts/")) {
                log.error("🚨 安全拦截：禁止通过CDN访问剪映草稿文件: {}", url);
                throw new RuntimeException("安全拦截：剪映草稿文件必须使用内网TOS访问，禁止CDN访问");
            }

            log.info("使用HTTP下载草稿: {}", url);

            // 使用HTTP客户端下载
            OkHttpClient client = new OkHttpClient.Builder()
                    .connectTimeout(30, TimeUnit.SECONDS)
                    .readTimeout(60, TimeUnit.SECONDS)
                    .build();

            Request request = new Request.Builder()
                    .url(url)
                    .build();

            try (Response response = client.newCall(request).execute()) {
                if (!response.isSuccessful()) {
                    throw new RuntimeException("HTTP下载失败: " + response.code());
                }

                String jsonContent = response.body().string();
                JSONObject draftJson = JSONObject.parseObject(jsonContent);
                log.info("HTTP下载草稿成功，ID: {}", draftJson.getString("id"));
                return draftJson;
            }

        } catch (Exception e) {
            log.error("HTTP下载草稿失败: {}", url, e);
            throw new RuntimeException("HTTP下载草稿失败: " + e.getMessage(), e);
        }
    }

    /**
     * 🔄 通过TOS SDK下载草稿（降级方式）
     */
    private JSONObject downloadDraftViaTosSDK(String objectKey) {
        try {
            log.info("使用TOS SDK下载草稿: {}", objectKey);

            // 🔧 直接使用TOS SDK下载，不构建URL
            String cleanKey = objectKey.startsWith("/") ? objectKey.substring(1) : objectKey;
            String jsonContent = tosService.downloadDraftFile(cleanKey);

            if (jsonContent == null || jsonContent.trim().isEmpty()) {
                throw new RuntimeException("下载的JSON内容为空");
            }

            JSONObject draftJson = JSONObject.parseObject(jsonContent);
            log.info("TOS SDK下载草稿成功，ID: {}", draftJson.getString("id"));
            return draftJson;

        } catch (Exception e) {
            log.error("TOS SDK下载草稿失败: {}", objectKey, e);
            throw new RuntimeException("TOS SDK下载草稿失败: " + e.getMessage(), e);
        }
    }

    /**
     * 🆕 从URL中提取对象键（支持多种URL格式）
     */
    private String extractObjectKeyFromUrl(String url) {
        try {
            log.debug("开始提取对象键，URL: {}", url);

            // 🔧 支持系统URL格式
            // 例如：https://aigcview.cn/jeecg-boot/sys/common/static/jianying-assistant/drafts/...
            if (url.contains("aigcview.cn") && url.contains("/sys/common/static/")) {
                int index = url.indexOf("/sys/common/static/");
                if (index != -1) {
                    String objectKey = url.substring(index + "/sys/common/static/".length());
                    log.debug("从系统URL提取对象键: {}", objectKey);
                    return objectKey;
                }
            }

            // 🔧 支持华东上海TOS URL格式
            // 例如：https://aigcview-tos.tos-cn-shanghai.volces.com/jianying-assistant/drafts/...
            if (url.contains("tos-cn-shanghai.volces.com/")) {
                int index = url.indexOf("tos-cn-shanghai.volces.com/");
                if (index != -1) {
                    String objectKey = url.substring(index + "tos-cn-shanghai.volces.com/".length());
                    // 移除开头的斜杠（如果有）
                    objectKey = objectKey.startsWith("/") ? objectKey.substring(1) : objectKey;
                    log.debug("从华东TOS URL提取对象键: {}", objectKey);
                    return objectKey;
                }
            }

            // 🔧 兼容旧的华南广州TOS URL格式
            if (url.contains("tos-cn-guangzhou.volces.com/")) {
                int index = url.indexOf("tos-cn-guangzhou.volces.com/");
                if (index != -1) {
                    String objectKey = url.substring(index + "tos-cn-guangzhou.volces.com/".length());
                    // 移除开头的斜杠（如果有）
                    objectKey = objectKey.startsWith("/") ? objectKey.substring(1) : objectKey;
                    log.debug("从华南TOS URL提取对象键: {}", objectKey);
                    return objectKey;
                }
            }

            throw new RuntimeException("无法从URL中提取对象键，不支持的URL格式: " + url);

        } catch (Exception e) {
            log.error("提取对象键失败: {}", url, e);
            throw new RuntimeException("提取对象键失败: " + e.getMessage(), e);
        }
    }

    /**
     * 验证视频URL格式（与add_videos接口一致）
     */
    private boolean isValidVideoURL(String url) {
        if (url == null || url.trim().isEmpty()) {
            return false;
        }
        // 基础格式检查：支持http/https协议和常见视频格式
        return url.matches("^https?://.*\\.(mp4|avi|mov|wmv|flv|webm|mkv|m4v).*$");
    }

    /**
     * 检查URL连通性（可选，5秒超时，支持重定向）
     */
    private boolean checkURLAccessible(String url) {
        try {
            java.net.HttpURLConnection conn = (java.net.HttpURLConnection) new java.net.URL(url).openConnection();
            conn.setRequestMethod("HEAD");
            conn.setConnectTimeout(5000);
            conn.setReadTimeout(5000);
            conn.setInstanceFollowRedirects(true); // 支持重定向

            int responseCode = conn.getResponseCode();
            boolean accessible = responseCode >= 200 && responseCode < 400;

            log.debug("URL连通性检查: {} -> {}", url, accessible ? "可访问" : "不可访问(" + responseCode + ")");
            return accessible;

        } catch (Exception e) {
            log.debug("URL连通性检查失败: {} -> {}", url, e.getMessage());
            return false; // 检查失败不阻塞处理
        }
    }

    /**
     * 验证图片URL格式（宽松模式，支持各种图片链接，与add_images接口完全一致）
     */
    private boolean isValidImageURL(String url) {
        if (url == null || url.trim().isEmpty()) {
            return false;
        }

        // 基础格式检查：只要是http/https协议即可
        if (!url.matches("^https?://.*")) {
            return false;
        }

        // 排除明显的非图片URL（如视频、音频、文档等）
        String lowerUrl = url.toLowerCase();

        // 排除视频格式
        if (lowerUrl.matches(".*\\.(mp4|avi|mov|wmv|flv|webm|mkv|m4v)($|\\?.*)")) {
            return false;
        }

        // 排除音频格式
        if (lowerUrl.matches(".*\\.(mp3|wav|flac|aac|ogg|wma)($|\\?.*)")) {
            return false;
        }

        // 排除文档格式
        if (lowerUrl.matches(".*\\.(pdf|doc|docx|xls|xlsx|ppt|pptx|txt)($|\\?.*)")) {
            return false;
        }

        // 其他HTTP/HTTPS链接都认为可能是图片（包括coze.cn、imgur、cloudinary等图片服务）
        return true;
    }

    /**
     * 生成统一文件夹的Windows路径格式（与add_videos接口一致）
     */
    private String generateUnifiedFolderPath(String materialId, String originalUrl, String unifiedFolderId) {
        try {
            // 从URL中提取文件名
            String baseFileName = extractFileNameFromUrl(originalUrl);

            // 生成带素材ID前缀的文件名，确保唯一性
            String uniqueFileName = materialId + "_" + baseFileName;

            // 生成Electron期望的统一文件夹路径格式
            String draftPlaceholder = "##_draftpath_placeholder_0E685133-18CE-45ED-8CB8-2904A212EC80_##";
            return draftPlaceholder + "\\" + unifiedFolderId + "\\" + uniqueFileName;

        } catch (Exception e) {
            log.warn("生成统一文件夹路径失败，使用默认格式: {}", e.getMessage());
            // 如果提取失败，使用默认文件名
            String draftPlaceholder = "##_draftpath_placeholder_0E685133-18CE-45ED-8CB8-2904A212EC80_##";
            return draftPlaceholder + "\\" + unifiedFolderId + "\\" + materialId + "_video.mp4";
        }
    }

    /**
     * 从URL中提取文件名
     */
    private String extractFileNameFromUrl(String url) {
        try {
            String path = new java.net.URL(url).getPath();
            String fileName = path.substring(path.lastIndexOf('/') + 1);
            return fileName.isEmpty() ? "video.mp4" : fileName;
        } catch (Exception e) {
            return "video.mp4";
        }
    }

    /**
     * 创建视频材料对象（外部URL直接引用模式，与add_videos接口一致）
     */
    private JSONObject createVideoMaterialWithOriginalURL(String videoMaterialId, String originalUrl, boolean urlValid, String unifiedFolderId) {
        // 生成随机的material_name（UUID格式）
        String materialName = java.util.UUID.randomUUID().toString();

        JSONObject videoMaterial = new JSONObject(new java.util.LinkedHashMap<>());

        // 基础信息
        videoMaterial.put("id", videoMaterialId);
        videoMaterial.put("material_id", videoMaterialId);
        videoMaterial.put("material_name", materialName);
        videoMaterial.put("material_url", originalUrl); // 直接使用原始URL

        // 修复：path字段使用Electron期望的Windows路径格式（统一文件夹模式）
        String electronPath = urlValid ?
            generateUnifiedFolderPath(videoMaterialId, originalUrl, unifiedFolderId) :
            "";
        videoMaterial.put("path", electronPath);

        videoMaterial.put("type", "video");
        videoMaterial.put("source_platform", urlValid ? "external" : "local"); // 标识外部URL

        // 视频属性（使用默认值）
        videoMaterial.put("duration", 5000000L); // 5秒
        videoMaterial.put("width", 1920);
        videoMaterial.put("height", 1080);
        videoMaterial.put("has_audio", true);

        // 视频特有字段（完整的剪映格式）
        videoMaterial.put("aigc_type", "none");
        videoMaterial.put("audio_fade", null);
        videoMaterial.put("cartoon_path", "");
        videoMaterial.put("category_id", "");
        videoMaterial.put("category_name", "");
        videoMaterial.put("check_flag", 63487);
        videoMaterial.put("crop", new JSONObject(new java.util.LinkedHashMap<>()) {{
            put("lower_left_x", 0);
            put("lower_left_y", 1);
            put("lower_right_x", 1);
            put("lower_right_y", 1);
            put("upper_left_x", 0);
            put("upper_left_y", 0);
            put("upper_right_x", 1);
            put("upper_right_y", 0);
        }});
        videoMaterial.put("crop_ratio", "free");
        videoMaterial.put("crop_scale", 1);
        videoMaterial.put("extra_type_option", 0);
        videoMaterial.put("formula_id", "");
        videoMaterial.put("freeze", null);
        videoMaterial.put("intensifies_audio_path", "");
        videoMaterial.put("intensifies_path", "");
        videoMaterial.put("is_ai_generate_content", false);
        videoMaterial.put("is_copyright", false);
        videoMaterial.put("is_text_edit_overdub", false);
        videoMaterial.put("is_unified_beauty_mode", false);
        videoMaterial.put("local_id", "");
        videoMaterial.put("local_material_id", "");
        videoMaterial.put("matting", new JSONObject(new java.util.LinkedHashMap<>()) {{
            put("flag", 0);
            put("has_use_quick_brush", false);
            put("has_use_quick_eraser", false);
            put("interactiveTime", new com.alibaba.fastjson.JSONArray());
            put("path", "");
            put("strokes", new com.alibaba.fastjson.JSONArray());
        }});
        videoMaterial.put("media_path", "");
        videoMaterial.put("object_locked", null);
        videoMaterial.put("origin_material_id", "");
        videoMaterial.put("picture_from", "none");
        videoMaterial.put("picture_set_category_id", "");
        videoMaterial.put("picture_set_category_name", "");
        videoMaterial.put("request_id", "");
        videoMaterial.put("reverse_intensifies_path", "");
        videoMaterial.put("reverse_path", "");
        videoMaterial.put("smart_motion", null);
        videoMaterial.put("source", 0);
        videoMaterial.put("stable", new JSONObject(new java.util.LinkedHashMap<>()) {{
            put("matrix_path", "");
            put("stable_level", 0);
            put("time_range", new JSONObject(new java.util.LinkedHashMap<>()) {{
                put("duration", 5000000L);
                put("start", 0);
            }});
        }});
        videoMaterial.put("team_id", "");
        videoMaterial.put("video_algorithm", new JSONObject(new java.util.LinkedHashMap<>()) {{
            put("algorithms", new com.alibaba.fastjson.JSONArray());
            put("complement_frame_config", null);
            put("deflicker", null);
            put("gameplay_configs", new com.alibaba.fastjson.JSONArray());
            put("motion_blur_config", null);
            put("noise_reduction", null);
            put("path", "");
            put("quality_enhance", null);
            put("time_range", null);
        }});

        // 客户端下载需要的字段
        videoMaterial.put("download_url", originalUrl);
        videoMaterial.put("file_name", extractFileNameFromUrl(originalUrl));

        return videoMaterial;
    }

    /**
     * 创建图片材料对象（外部URL直接引用模式，与add_images接口一致）
     */
    private JSONObject createImageMaterialWithOriginalURL(String imageMaterialId, String originalUrl, boolean urlValid, String unifiedFolderId) {
        // 生成随机的material_name（UUID格式）
        String materialName = java.util.UUID.randomUUID().toString();

        JSONObject imageMaterial = new JSONObject(new java.util.LinkedHashMap<>());

        // 基础信息
        imageMaterial.put("id", imageMaterialId);
        imageMaterial.put("material_id", imageMaterialId);
        imageMaterial.put("material_name", materialName);
        imageMaterial.put("material_url", originalUrl); // 直接使用原始URL

        // 修复：path字段使用Electron期望的Windows路径格式（统一文件夹模式）
        String electronPath = urlValid ?
            generateImageUnifiedFolderPath(imageMaterialId, originalUrl, unifiedFolderId) :
            "";
        imageMaterial.put("path", electronPath);

        imageMaterial.put("type", "photo"); // 图片类型
        imageMaterial.put("source_platform", urlValid ? "external" : "local"); // 标识外部URL

        // 图片属性（使用默认值）
        imageMaterial.put("duration", 5000000L); // 5秒
        imageMaterial.put("width", 1920);
        imageMaterial.put("height", 1080);
        imageMaterial.put("has_audio", false);

        // 图片特有字段（完整的剪映格式）
        imageMaterial.put("aigc_type", "none");
        imageMaterial.put("audio_fade", null);
        imageMaterial.put("cartoon_path", "");
        imageMaterial.put("category_id", "");
        imageMaterial.put("category_name", "");
        imageMaterial.put("check_flag", 63487);
        imageMaterial.put("crop", new JSONObject(new java.util.LinkedHashMap<>()) {{
            put("lower_left_x", 0);
            put("lower_left_y", 1);
            put("lower_right_x", 1);
            put("lower_right_y", 1);
            put("upper_left_x", 0);
            put("upper_left_y", 0);
            put("upper_right_x", 1);
            put("upper_right_y", 0);
        }});
        imageMaterial.put("crop_ratio", "free");
        imageMaterial.put("crop_scale", 1);
        imageMaterial.put("extra_type_option", 0);
        imageMaterial.put("formula_id", "");
        imageMaterial.put("freeze", null);
        imageMaterial.put("intensifies_audio_path", "");
        imageMaterial.put("intensifies_path", "");
        imageMaterial.put("is_ai_generate_content", false);
        imageMaterial.put("is_copyright", false);
        imageMaterial.put("is_text_edit_overdub", false);
        imageMaterial.put("is_unified_beauty_mode", false);
        imageMaterial.put("local_id", "");
        imageMaterial.put("local_material_id", "");
        imageMaterial.put("matting", new JSONObject(new java.util.LinkedHashMap<>()) {{
            put("flag", 0);
            put("has_use_quick_brush", false);
            put("has_use_quick_eraser", false);
            put("interactiveTime", new com.alibaba.fastjson.JSONArray());
            put("path", "");
            put("strokes", new com.alibaba.fastjson.JSONArray());
        }});
        imageMaterial.put("media_path", "");
        imageMaterial.put("object_locked", null);
        imageMaterial.put("origin_material_id", "");
        imageMaterial.put("picture_from", "none");
        imageMaterial.put("picture_set_category_id", "");
        imageMaterial.put("picture_set_category_name", "");
        imageMaterial.put("request_id", "");
        imageMaterial.put("reverse_intensifies_path", "");
        imageMaterial.put("reverse_path", "");
        imageMaterial.put("smart_motion", null);
        imageMaterial.put("source", 0);
        imageMaterial.put("stable", new JSONObject(new java.util.LinkedHashMap<>()) {{
            put("matrix_path", "");
            put("stable_level", 0);
            put("time_range", new JSONObject(new java.util.LinkedHashMap<>()) {{
                put("duration", 5000000L);
                put("start", 0);
            }});
        }});
        imageMaterial.put("team_id", "");
        imageMaterial.put("video_algorithm", new JSONObject(new java.util.LinkedHashMap<>()) {{
            put("algorithms", new com.alibaba.fastjson.JSONArray());
            put("complement_frame_config", null);
            put("deflicker", null);
            put("gameplay_configs", new com.alibaba.fastjson.JSONArray());
            put("motion_blur_config", null);
            put("noise_reduction", null);
            put("path", "");
            put("quality_enhance", null);
            put("time_range", null);
        }});

        // 客户端下载需要的字段
        imageMaterial.put("download_url", originalUrl);
        imageMaterial.put("file_name", extractImageFileNameFromUrl(originalUrl));

        return imageMaterial;
    }

    /**
     * 生成图片统一文件夹的Windows路径格式
     */
    private String generateImageUnifiedFolderPath(String materialId, String originalUrl, String unifiedFolderId) {
        try {
            // 从URL中提取文件名
            String baseFileName = extractImageFileNameFromUrl(originalUrl);

            // 生成带素材ID前缀的文件名，确保唯一性
            String uniqueFileName = materialId + "_" + baseFileName;

            // 生成Electron期望的统一文件夹路径格式
            String draftPlaceholder = "##_draftpath_placeholder_0E685133-18CE-45ED-8CB8-2904A212EC80_##";
            return draftPlaceholder + "\\" + unifiedFolderId + "\\" + uniqueFileName;

        } catch (Exception e) {
            log.warn("生成图片统一文件夹路径失败，使用默认格式: {}", e.getMessage());
            // 如果提取失败，使用默认文件名
            String draftPlaceholder = "##_draftpath_placeholder_0E685133-18CE-45ED-8CB8-2904A212EC80_##";
            return draftPlaceholder + "\\" + unifiedFolderId + "\\" + materialId + "_image.jpg";
        }
    }

    /**
     * 从图片URL中提取文件名（智能处理各种URL格式，与add_images接口一致）
     */
    private String extractImageFileNameFromUrl(String url) {
        try {
            // 移除查询参数
            String cleanUrl = url.split("\\?")[0];

            // 提取最后一个斜杠后的部分
            String[] parts = cleanUrl.split("/");
            String fileName = parts[parts.length - 1];

            // 如果文件名为空或者是特殊标识符，生成一个基于URL的唯一文件名
            if (fileName.isEmpty() || fileName.length() < 3) {
                // 使用URL的hash值生成文件名
                fileName = "img_" + Math.abs(url.hashCode());
            }

            // 检查是否已有图片扩展名
            String lowerFileName = fileName.toLowerCase();
            boolean hasImageExt = lowerFileName.matches(".*\\.(jpg|jpeg|png|gif|bmp|webp|svg)$");

            // 如果没有图片扩展名，添加默认扩展名
            if (!hasImageExt) {
                // 根据URL特征判断可能的格式
                if (url.toLowerCase().contains("webp")) {
                    fileName += ".webp";
                } else if (url.toLowerCase().contains("png")) {
                    fileName += ".png";
                } else if (url.toLowerCase().contains("gif")) {
                    fileName += ".gif";
                } else {
                    fileName += ".jpg"; // 默认jpg格式
                }
            }

            return fileName;

        } catch (Exception e) {
            log.warn("从图片URL提取文件名失败: {}", url);
            // 生成基于时间戳的唯一文件名
            return "image_" + System.currentTimeMillis() + ".jpg";
        }
    }
}
