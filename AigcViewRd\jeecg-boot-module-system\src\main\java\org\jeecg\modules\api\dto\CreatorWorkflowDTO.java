package org.jeecg.modules.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * @Description: 创作者工作流数据传输对象
 * @Author: 智界AIGC
 * @Date: 2025-08-04
 * @Version: V1.0
 */
@Data
@ApiModel(value = "CreatorWorkflowDTO", description = "创作者工作流数据传输对象")
public class CreatorWorkflowDTO implements Serializable {
    
    private static final long serialVersionUID = 1L;

    /**智能体ID*/
    @ApiModelProperty(value = "智能体ID", required = true)
    @NotBlank(message = "智能体ID不能为空")
    private String agentId;

    /**工作流名称*/
    @ApiModelProperty(value = "工作流名称", required = true)
    @NotBlank(message = "工作流名称不能为空")
    @Length(max = 100, message = "工作流名称长度不能超过100个字符")
    private String workflowName;

    /**工作流描述*/
    @ApiModelProperty(value = "工作流描述")
    @Length(max = 500, message = "工作流描述长度不能超过500个字符")
    private String workflowDescription;

    /**输入参数说明*/
    @ApiModelProperty(value = "输入参数说明", required = true)
    @NotBlank(message = "输入参数说明不能为空")
    @Length(max = 10000, message = "输入参数说明长度不能超过10000个字符")
    private String inputParamsDesc;

    /**工作流压缩包文件名*/
    @ApiModelProperty(value = "工作流压缩包文件名")
    @Length(max = 200, message = "工作流压缩包文件名长度不能超过200个字符")
    private String workflowPackage;

    // 注意：以下字段不在DTO中，由后端自动处理
    // - workflowId: 系统自动生成
    // - createBy: 当前登录用户
    // - createTime: 系统自动设置
}
