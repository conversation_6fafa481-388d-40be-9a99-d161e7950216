<template>
  <WebsitePage>
    <div class="jianying-draft-container">
      <!-- 简洁页面标题 -->
      <div class="simple-header">
        <h1 class="simple-title">剪映小助手</h1>
        <p class="simple-subtitle">专业的剪映草稿管理工具</p>
      </div>

      <!-- 主要功能区域 -->
      <div class="main-functions">
        <div class="functions-container">
          <!-- 左侧：软件下载 -->
          <div class="function-card download-card">
            <div class="card-header">
              <div class="card-icon">
                <a-icon type="download" />
              </div>
              <h3 class="card-title">下载小助手</h3>
              <p class="card-description">选择适合您操作系统的版本进行下载</p>
            </div>

            <div class="download-options">
              <div class="download-option" @click="handleDownload('windows')" :class="{ 'downloading': isPlatformDownloading('windows'), 'recommended': currentOS === 'windows', 'disabled': isDownloadDisabled('windows') }">
                <div class="option-icon">
                  <a-icon type="windows" />
                </div>
                <div class="option-content">
                  <h4 class="option-title">
                    Windows 版本
                    <span v-if="currentOS === 'windows'" class="recommended-badge">推荐</span>
                  </h4>
                  <p class="option-desc">适用于 Windows 8/10/11</p>
                  <p v-if="isPlatformDownloading('windows')" class="download-status">正在获取下载链接...</p>
                  <p v-if="getCooldownStatus('windows').inCooldown" class="cooldown-status">
                    请稍后再试 ({{ getCooldownStatus('windows').remainingTime }}s)
                  </p>
                </div>
                <div class="option-arrow">
                  <a-icon :type="isPlatformDownloading('windows') ? 'loading' : 'download'" :spin="isPlatformDownloading('windows')" />
                </div>
              </div>

              <div class="download-option" @click="handleDownload('mac')" :class="{ 'downloading': isPlatformDownloading('mac'), 'recommended': currentOS === 'mac', 'disabled': isDownloadDisabled('mac') }">
                <div class="option-icon">
                  <a-icon type="apple" />
                </div>
                <div class="option-content">
                  <h4 class="option-title">
                    Mac 版本
                    <span v-if="currentOS === 'mac'" class="recommended-badge">推荐</span>
                  </h4>
                  <p class="option-desc">适用于 macOS 系统</p>
                  <p v-if="isPlatformDownloading('mac')" class="download-status">正在获取下载链接...</p>
                  <p v-if="getCooldownStatus('mac').inCooldown" class="cooldown-status">
                    请稍后再试 ({{ getCooldownStatus('mac').remainingTime }}s)
                  </p>
                </div>
                <div class="option-arrow">
                  <a-icon :type="isPlatformDownloading('mac') ? 'loading' : 'download'" :spin="isPlatformDownloading('mac')" />
                </div>
              </div>
            </div>

            <!-- 下载说明 -->
            <div class="download-notice">
              <div class="notice-item">
                <a-icon type="info-circle" />
                <span>下载完成后，请按照安装向导完成安装</span>
              </div>
            </div>
          </div>

          <!-- 右侧：草稿导入 -->
          <div class="function-card draft-card">
            <div class="card-header">
              <div class="card-icon">
                <a-icon type="file-text" />
              </div>
              <h3 class="card-title">剪映草稿导出</h3>
              <p class="card-description">复制草稿链接到剪映小助手，一键下载并创建本地草稿文件<br/>支持链接换行，批量处理多个草稿</p>
            </div>

            <div class="draft-input-area">
              <a-textarea
                v-model="draftUrl"
                placeholder="请粘贴剪映草稿链接..."
                :rows="6"
                class="draft-textarea"
              />
              <a-button
                type="primary"
                size="large"
                @click="handleCopyUrl"
                :disabled="!draftUrl"
                class="copy-btn"
              >
                <a-icon type="copy" />
                复制草稿链接
              </a-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 产品介绍 -->
      <div class="product-intro">
        <div class="intro-container">
          <div class="intro-header">
            <h2 class="intro-title">产品介绍</h2>
          </div>

          <div class="intro-content">
            <div class="intro-card">
              <div class="intro-icon">
                <a-icon type="tool" />
              </div>
              <div class="intro-text">
                <h3>剪映小助手</h3>
                <p>专为剪映用户打造的桌面工具，支持草稿文件的快速导入和管理。通过简单的链接分享，让您的创作更加便捷高效。支持Windows和Mac系统，本地处理保障数据安全。</p>
              </div>
            </div>
          </div>

          <!-- 功能特色 -->
          <div class="features-grid">
            <div v-for="feature in features" :key="feature.id" class="feature-item">
              <div class="feature-icon">
                <a-icon :type="feature.icon" />
              </div>
              <div class="feature-content">
                <h4 class="feature-title">{{ feature.title }}</h4>
                <p class="feature-description">{{ feature.description }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 系统不匹配确认模态框 -->
    <a-modal
      v-model="confirmModalVisible"
      title=""
      :footer="null"
      :width="480"
      :centered="true"
      :maskClosable="false"
    >
      <div class="confirm-modal-content">
        <div class="modal-icon">
          <a-icon type="exclamation-circle" />
        </div>
        <div class="modal-title">系统不匹配提醒</div>
        <div class="modal-message">
          检测到您当前使用的是 <strong>{{ confirmModalData.currentOS }}</strong> 系统，
          但您选择下载 <strong>{{ confirmModalData.targetOS }}</strong> 版本。
          <br><br>
          确定要继续下载吗？
        </div>
        <div class="modal-actions">
          <a-button @click="cancelDownload" class="cancel-btn">
            取消
          </a-button>
          <a-button type="primary" @click="confirmDownload" class="confirm-btn">
            继续下载
          </a-button>
        </div>
      </div>
    </a-modal>
  </WebsitePage>
</template>

<script>
import WebsitePage from '@/components/website/WebsitePage.vue'

export default {
  name: 'JianYingDraft',
  components: {
    WebsitePage
  },
  data() {
    return {
      draftUrl: '',
      downloadingPlatforms: {}, // 各平台下载状态 {platform: boolean}
      currentOS: null, // 当前操作系统
      confirmModalVisible: false, // 确认模态框显示状态
      confirmModalData: {}, // 确认模态框数据
      downloadCooldown: {}, // 下载冷却时间管理 {platform: timestamp}
      downloadHistory: {}, // 下载历史记录 {platform: {timestamp, fileName}}
      cooldownTimer: null, // 冷却时间更新定时器
      features: [
        {
          id: 1,
          icon: 'cloud-download',
          title: '草稿文件导入',
          description: '支持从云端下载剪映草稿文件到本地，快速同步您的创作项目'
        },
        {
          id: 2,
          icon: 'folder',
          title: '文件管理',
          description: '智能管理下载的草稿文件，自动分类整理，方便查找和使用'
        },
        {
          id: 3,
          icon: 'setting',
          title: '路径配置',
          description: '灵活设置剪映软件安装路径，确保草稿文件正确导入到剪映编辑器'
        },
        {
          id: 4,
          icon: 'safety',
          title: '本地安全',
          description: '所有操作在本地进行，保护您的创作内容安全，不会泄露到第三方'
        },
        {
          id: 5,
          icon: 'thunderbolt',
          title: '批量处理',
          description: '支持批量下载和导入多个草稿文件，大幅提升工作效率'
        },
        {
          id: 6,
          icon: 'global',
          title: '跨平台支持',
          description: '同时支持Windows和Mac系统，满足不同用户的使用需求'
        }
      ]
    }
  },

  computed: {
    // 检查下载按钮是否应该禁用
    isDownloadDisabled() {
      return (platform) => {
        // 当前平台正在下载
        if (this.downloadingPlatforms[platform]) {
          return true
        }

        // 冷却时间检查（仅针对当前平台）
        const cooldownCheck = this.checkDownloadCooldown(platform)
        if (cooldownCheck.inCooldown) {
          return true
        }

        return false
      }
    },

    // 获取冷却状态（用于实时显示）
    getCooldownStatus() {
      return (platform) => {
        return this.checkDownloadCooldown(platform)
      }
    }
  },

  mounted() {
    // 检查URL参数中是否有draft参数，自动回填
    this.checkDraftParam()
    // 检测当前操作系统
    this.detectCurrentOS()
    // 从本地存储初始化数据
    this.initializeFromLocalStorage()
    // 启动冷却时间更新定时器
    this.startCooldownTimer()
  },

  beforeDestroy() {
    // 清理定时器
    if (this.cooldownTimer) {
      clearInterval(this.cooldownTimer)
    }

    // 最后一次清理过期数据
    this.cleanupExpiredData()
  },
  methods: {
    // 检测当前操作系统
    detectCurrentOS() {
      const userAgent = navigator.userAgent.toLowerCase()

      if (userAgent.includes('mac')) {
        this.currentOS = 'mac'
      } else if (userAgent.includes('win')) {
        this.currentOS = 'windows'
      } else if (userAgent.includes('linux')) {
        this.currentOS = 'linux'
      } else {
        this.currentOS = 'unknown'
      }

      console.log('检测到当前系统:', this.currentOS)
    },

    // 检查下载冷却时间
    checkDownloadCooldown(platform) {
      const now = Date.now()
      const cooldownTime = 45000 // 45秒冷却时间
      const lastDownload = this.downloadCooldown[platform]

      if (lastDownload && (now - lastDownload) < cooldownTime) {
        const remainingTime = Math.ceil((cooldownTime - (now - lastDownload)) / 1000)
        return {
          inCooldown: true,
          remainingTime: remainingTime
        }
      }

      return {
        inCooldown: false,
        remainingTime: 0
      }
    },

    // 检查下载历史
    checkDownloadHistory(platform) {
      const history = this.downloadHistory[platform]
      if (history) {
        const timeDiff = Date.now() - history.timestamp
        const minutesAgo = Math.floor(timeDiff / (1000 * 60))

        if (minutesAgo < 10) { // 10分钟内下载过
          return {
            hasRecent: true,
            minutesAgo: minutesAgo,
            fileName: history.fileName
          }
        }
      }

      return {
        hasRecent: false,
        minutesAgo: 0,
        fileName: ''
      }
    },

    // 设置下载冷却
    setDownloadCooldown(platform) {
      const timestamp = Date.now()
      this.$set(this.downloadCooldown, platform, timestamp)

      // 保存到本地存储
      this.saveToLocalStorage('download_cooldown', this.downloadCooldown)
    },

    // 记录下载历史
    recordDownloadHistory(platform, fileName) {
      const historyData = {
        timestamp: Date.now(),
        fileName: fileName
      }
      this.$set(this.downloadHistory, platform, historyData)

      // 保存到本地存储
      this.saveToLocalStorage('download_history', this.downloadHistory)
    },

    // 启动冷却时间更新定时器
    startCooldownTimer() {
      this.cooldownTimer = setInterval(() => {
        // 强制更新计算属性，让冷却时间实时显示
        this.$forceUpdate()

        // 每10秒清理一次过期数据
        if (Date.now() % 10000 < 1000) {
          this.cleanupExpiredData()
        }
      }, 1000) // 每秒更新一次
    },

    // 清理过期数据
    cleanupExpiredData() {
      const now = Date.now()
      const cooldownTime = 45000 // 45秒
      const historyExpireTime = 24 * 60 * 60 * 1000 // 24小时

      // 清理过期的冷却时间
      let cooldownUpdated = false
      Object.keys(this.downloadCooldown).forEach(platform => {
        const lastDownload = this.downloadCooldown[platform]
        if (lastDownload && (now - lastDownload) >= cooldownTime) {
          this.$delete(this.downloadCooldown, platform)
          cooldownUpdated = true
        }
      })

      // 清理过期的历史记录
      let historyUpdated = false
      Object.keys(this.downloadHistory).forEach(platform => {
        const history = this.downloadHistory[platform]
        if (history && history.timestamp && (now - history.timestamp) >= historyExpireTime) {
          this.$delete(this.downloadHistory, platform)
          historyUpdated = true
        }
      })

      // 更新本地存储
      if (cooldownUpdated) {
        this.saveToLocalStorage('download_cooldown', this.downloadCooldown)
      }
      if (historyUpdated) {
        this.saveToLocalStorage('download_history', this.downloadHistory)
      }
    },

    // 本地存储管理
    saveToLocalStorage(key, data) {
      try {
        localStorage.setItem(`jianying_assistant_${key}`, JSON.stringify(data))
      } catch (error) {
        console.warn('保存到本地存储失败:', error)
      }
    },

    loadFromLocalStorage(key) {
      try {
        const data = localStorage.getItem(`jianying_assistant_${key}`)
        return data ? JSON.parse(data) : null
      } catch (error) {
        console.warn('从本地存储读取失败:', error)
        return null
      }
    },

    clearLocalStorage(key) {
      try {
        localStorage.removeItem(`jianying_assistant_${key}`)
      } catch (error) {
        console.warn('清除本地存储失败:', error)
      }
    },

    // 开发者工具：手动清理所有下载相关数据
    clearAllDownloadData() {
      this.downloadCooldown = {}
      this.downloadHistory = {}
      this.downloadingPlatforms = {}

      this.clearLocalStorage('download_cooldown')
      this.clearLocalStorage('download_history')

      console.log('已清理所有下载相关数据')
      this.$message.success('已清理所有下载数据')
    },

    // 开发者工具：查看当前下载状态
    debugDownloadStatus() {
      const status = {
        downloadingPlatforms: this.downloadingPlatforms,
        downloadCooldown: this.downloadCooldown,
        downloadHistory: this.downloadHistory,
        localStorageCooldown: this.loadFromLocalStorage('download_cooldown'),
        localStorageHistory: this.loadFromLocalStorage('download_history')
      }
      console.log('当前下载状态:', status)
      return status
    },

    // 设置平台下载状态
    setPlatformDownloading(platform, isDownloading) {
      this.$set(this.downloadingPlatforms, platform, isDownloading)
    },

    // 检查平台是否正在下载
    isPlatformDownloading(platform) {
      return this.downloadingPlatforms[platform] || false
    },

    // 初始化本地存储数据
    initializeFromLocalStorage() {
      // 加载冷却时间数据
      const savedCooldown = this.loadFromLocalStorage('download_cooldown')
      if (savedCooldown) {
        // 清理过期的冷却时间
        const now = Date.now()
        const cooldownTime = 45000 // 45秒

        Object.keys(savedCooldown).forEach(platform => {
          const lastDownload = savedCooldown[platform]
          if (lastDownload && (now - lastDownload) < cooldownTime) {
            // 冷却时间还未过期，保留
            this.$set(this.downloadCooldown, platform, lastDownload)
          }
        })
      }

      // 加载下载历史数据
      const savedHistory = this.loadFromLocalStorage('download_history')
      if (savedHistory) {
        // 清理过期的历史记录（超过24小时）
        const now = Date.now()
        const historyExpireTime = 24 * 60 * 60 * 1000 // 24小时

        Object.keys(savedHistory).forEach(platform => {
          const history = savedHistory[platform]
          if (history && history.timestamp && (now - history.timestamp) < historyExpireTime) {
            // 历史记录还未过期，保留
            this.$set(this.downloadHistory, platform, history)
          }
        })
      }
    },

    // 检查URL参数中的draft值
    checkDraftParam() {
      const draftParam = this.$route.query.draft
      if (draftParam) {
        this.draftUrl = draftParam
        console.log('自动回填草稿链接:', draftParam)
      }
    },

    // 处理下载
    async handleDownload(platform) {
      // 1. 检查当前平台是否正在下载
      if (this.isPlatformDownloading(platform)) {
        return
      }

      // 2. 检查当前平台的下载冷却时间
      const cooldownCheck = this.checkDownloadCooldown(platform)
      if (cooldownCheck.inCooldown) {
        const platformName = platform === 'windows' ? 'Windows' : 'Mac'
        this.$message.warning(`${platformName} 版本请稍后再试，还需等待 ${cooldownCheck.remainingTime} 秒`)
        return
      }

      // 3. 检查下载历史，给出友好提示
      const historyCheck = this.checkDownloadHistory(platform)
      if (historyCheck.hasRecent) {
        const platformName = platform === 'windows' ? 'Windows' : 'Mac'
        if (historyCheck.minutesAgo === 0) {
          this.$message.info(`检测到您刚刚下载过 ${platformName} 版本`)
        } else {
          this.$message.info(`检测到您 ${historyCheck.minutesAgo} 分钟前下载过 ${platformName} 版本`)
        }
      }

      // 4. 检查是否与当前系统匹配，给出提示
      if (this.currentOS && this.currentOS !== platform && this.currentOS !== 'unknown') {
        const currentOSName = this.currentOS === 'mac' ? 'Mac' : 'Windows'
        const targetOSName = platform === 'mac' ? 'Mac' : 'Windows'

        // 显示自定义确认模态框
        this.confirmModalVisible = true
        this.confirmModalData = {
          currentOS: currentOSName,
          targetOS: targetOSName,
          platform: platform
        }
        return
      }

      // 5. 如果系统匹配，直接下载
      await this.executeDownload(platform)
    },

    // 确认下载不匹配的系统版本
    async confirmDownload() {
      this.confirmModalVisible = false
      const platform = this.confirmModalData.platform

      // 继续执行下载逻辑
      await this.executeDownload(platform)
    },

    // 取消下载
    cancelDownload() {
      this.confirmModalVisible = false
      this.confirmModalData = {}
    },

    // 执行实际的下载逻辑
    async executeDownload(platform) {
      try {
        // 设置当前平台的下载状态
        this.setPlatformDownloading(platform, true)

        // 设置下载冷却时间
        this.setDownloadCooldown(platform)

        // 定义文件名映射
        const fileNames = {
          windows: '剪映小助手-智界-Windows.zip',
          mac: '剪映小助手-智界-Mac.zip'
        }

        const fileName = fileNames[platform]
        if (!fileName) {
          this.$message.error('不支持的平台类型')
          return
        }

        // 调用后端API获取桌面应用下载链接
        const response = await this.axios.get(`${this.API_BASE_URL}/sys/common/desktop-app-download`, {
          params: {
            platform: platform
          }
        })

        if (response.success && response.result) {
          // 创建隐藏的下载链接并触发下载
          const downloadLink = document.createElement('a')
          downloadLink.href = response.result
          downloadLink.download = fileName
          downloadLink.style.display = 'none'
          document.body.appendChild(downloadLink)
          downloadLink.click()
          document.body.removeChild(downloadLink)

          // 记录下载历史
          this.recordDownloadHistory(platform, fileName)

          this.$message.success(`开始下载 ${platform === 'windows' ? 'Windows' : 'Mac'} 版本`)
        } else {
          this.$message.error(response.message || '获取下载链接失败')
        }
      } catch (error) {
        console.error('下载失败:', error)

        // 详细的错误处理
        if (error.response) {
          const status = error.response.status
          const message = (error.response.data && error.response.data.message) || error.response.statusText

          switch (status) {
            case 404:
              this.$message.error('安装包文件不存在，请联系管理员')
              break
            case 403:
              this.$message.error('没有权限访问该文件')
              break
            case 500:
              this.$message.error('服务器内部错误，请稍后重试')
              break
            default:
              this.$message.error(`下载失败: ${message}`)
          }
        } else if (error.code === 'NETWORK_ERROR' || error.message.includes('Network Error')) {
          this.$message.error('网络连接失败，请检查网络后重试')
        } else if (error.code === 'TIMEOUT') {
          this.$message.error('请求超时，请稍后重试')
        } else {
          this.$message.error('下载失败，请稍后重试')
        }
      } finally {
        // 清除当前平台的下载状态
        this.setPlatformDownloading(platform, false)
      }
    },

    // 复制草稿链接（供桌面工具使用）
    handleCopyUrl() {
      if (!this.draftUrl) return

      // 使用现代浏览器的Clipboard API
      if (navigator.clipboard) {
        navigator.clipboard.writeText(this.draftUrl).then(() => {
          this.$message.success('草稿链接已复制，请粘贴到剪映小助手中')
        }).catch(() => {
          this.fallbackCopyText(this.draftUrl)
        })
      } else {
        this.fallbackCopyText(this.draftUrl)
      }
    },

    // 备用复制方法
    fallbackCopyText(text) {
      const textArea = document.createElement('textarea')
      textArea.value = text
      document.body.appendChild(textArea)
      textArea.select()
      try {
        document.execCommand('copy')
        this.$message.success('链接已复制到剪贴板')
      } catch (err) {
        this.$message.error('复制失败，请手动复制')
      }
      document.body.removeChild(textArea)
    }
  }
}
</script>

<style scoped>
.jianying-draft-container {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #cbd5e1 100%);
  min-height: 100vh;
  padding: 2rem 0;
}

/* 简洁页面标题 */
.simple-header {
  text-align: center;
  padding: 2rem 0 3rem;
  max-width: 1200px;
  margin: 0 auto;
}

.simple-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0 0 0.5rem 0;
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.simple-subtitle {
  font-size: 1.1rem;
  color: #64748b;
  margin: 0;
}

/* 主要功能区域 */
.main-functions {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem 3rem;
}

.functions-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin-bottom: 4rem;
}

.function-card {
  background: white;
  border-radius: 20px;
  padding: 2.5rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.function-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  border-color: #3b82f6;
}

/* 功能卡片头部 */
.card-header {
  text-align: center;
  margin-bottom: 2rem;
}

.card-icon {
  font-size: 2.5rem;
  color: #3b82f6;
  margin-bottom: 1rem;
}

.card-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 0.5rem 0;
}

.card-description {
  color: #64748b;
  line-height: 1.6;
  margin: 0;
}

/* 下载选项区域 */
.download-options {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.download-option {
  display: flex;
  align-items: center;
  padding: 1.5rem;
  background: #f8fafc;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.download-option:hover {
  background: #e2e8f0;
  border-color: #3b82f6;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.15);
}

.download-option:active {
  transform: translateY(0);
}

.option-icon {
  font-size: 2.5rem;
  color: #3b82f6;
  margin-right: 1.5rem;
  flex-shrink: 0;
}

.option-content {
  flex: 1;
}

.option-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 0.3rem 0;
}

.option-desc {
  color: #64748b;
  margin: 0;
  font-size: 0.9rem;
}

.option-arrow {
  font-size: 1.5rem;
  color: #3b82f6;
  margin-left: 1rem;
  transition: transform 0.3s ease;
}

.download-option:hover .option-arrow {
  transform: translateX(5px);
}

.download-option.downloading {
  background: #e6f7ff;
  border-color: #1890ff;
  cursor: not-allowed;
}

.download-option.downloading:hover {
  transform: none;
  background: #e6f7ff;
}

.download-option.disabled {
  opacity: 0.6;
  cursor: not-allowed;
  background: #f5f5f5;
  border-color: #d9d9d9;
}

.download-option.disabled:hover {
  transform: none;
  background: #f5f5f5;
  border-color: #d9d9d9;
}

.download-status {
  color: #1890ff;
  font-size: 0.8rem;
  margin: 0.2rem 0 0 0;
  font-weight: 500;
}

.cooldown-status {
  color: #ff7875;
  font-size: 0.8rem;
  margin: 0.2rem 0 0 0;
  font-weight: 500;
}

.download-notice {
  margin-top: 1.5rem;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #1890ff;
}

.notice-item {
  display: flex;
  align-items: center;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
  color: #666;
}

.notice-item:last-child {
  margin-bottom: 0;
}

.notice-item .anticon {
  margin-right: 0.5rem;
  color: #1890ff;
}

.download-option.recommended {
  border-color: #1890ff;
  background: linear-gradient(135deg, #f0f9ff 0%, #e6f7ff 100%);
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.1);
}

.download-option.recommended:hover {
  border-color: #40a9ff;
  background: linear-gradient(135deg, #e6f7ff 0%, #d6f7ff 100%);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);
}

.recommended-badge {
  display: inline-block;
  background: #1890ff;
  color: white;
  font-size: 0.7rem;
  padding: 0.1rem 0.4rem;
  border-radius: 10px;
  margin-left: 0.5rem;
  font-weight: 500;
}

/* 自定义确认模态框样式 */
.confirm-modal-content {
  text-align: center;
  padding: 1rem 0;
}

.modal-icon {
  font-size: 3rem;
  color: #faad14;
  margin-bottom: 1rem;
}

.modal-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: #262626;
  margin-bottom: 1rem;
}

.modal-message {
  font-size: 1rem;
  color: #595959;
  line-height: 1.6;
  margin-bottom: 2rem;
}

.modal-message strong {
  color: #1890ff;
  font-weight: 600;
}

.modal-actions {
  display: flex;
  justify-content: center;
  gap: 1rem;
}

.cancel-btn {
  min-width: 80px;
  height: 36px;
  border-radius: 6px;
}

.confirm-btn {
  min-width: 100px;
  height: 36px;
  border-radius: 6px;
  background: #1890ff;
  border-color: #1890ff;
}

/* 使用提示 */
.usage-tips {
  margin-top: 1rem;
  padding: 1rem;
  background: #f0f9ff;
  border-radius: 8px;
  border-left: 4px solid #3b82f6;
}

.tip-item {
  margin: 0.3rem 0;
  font-size: 0.9rem;
  color: #1e40af;
  line-height: 1.4;
}

/* 草稿输入区域 */
.draft-input-area {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.draft-textarea {
  resize: vertical;
  min-height: 120px;
  font-size: 14px;
  line-height: 1.6;
}

.copy-btn {
  width: 100%;
  height: 50px;
  font-size: 1rem;
  font-weight: 500;
}

/* 产品介绍区域 */
.product-intro {
  background: white;
  margin: 0 2rem;
  border-radius: 20px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.intro-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 3rem 2rem;
}

.intro-header {
  text-align: center;
  margin-bottom: 3rem;
}

.intro-title {
  font-size: 2rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
}

.intro-content {
  margin-bottom: 3rem;
}

.intro-card {
  display: flex;
  align-items: center;
  gap: 2rem;
  background: #f8fafc;
  border-radius: 15px;
  padding: 2rem;
}

.intro-icon {
  font-size: 3rem;
  color: #3b82f6;
  flex-shrink: 0;
}

.intro-text h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 1rem 0;
}

.intro-text p {
  color: #64748b;
  line-height: 1.8;
  margin: 0;
  font-size: 1rem;
}

/* 功能特色网格 */
.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 1.5rem;
}

.feature-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1.5rem;
  background: #f8fafc;
  border-radius: 12px;
  transition: all 0.3s ease;
}

.feature-item:hover {
  background: #e2e8f0;
  transform: translateY(-2px);
}

.feature-item .feature-icon {
  font-size: 1.8rem;
  color: #3b82f6;
  flex-shrink: 0;
  margin-top: 0.2rem;
}

.feature-content {
  flex: 1;
}

.feature-item .feature-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 0.5rem 0;
}

.feature-item .feature-description {
  color: #64748b;
  line-height: 1.6;
  margin: 0;
  font-size: 0.9rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .simple-title {
    font-size: 2rem;
  }

  .functions-container {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .function-card {
    padding: 2rem;
  }

  .download-option {
    padding: 1.2rem;
  }

  .option-icon {
    font-size: 2rem;
    margin-right: 1rem;
  }

  .option-title {
    font-size: 1.1rem;
  }

  .intro-card {
    flex-direction: column;
    text-align: center;
    gap: 1.5rem;
  }

  .features-grid {
    grid-template-columns: 1fr;
  }

  .intro-container {
    padding: 2rem 1rem;
  }

  .main-functions {
    padding: 0 1rem 2rem;
  }
}

@media (max-width: 480px) {
  .simple-title {
    font-size: 1.8rem;
  }

  .simple-subtitle {
    font-size: 1rem;
  }

  .function-card {
    padding: 1.5rem;
  }

  .card-title {
    font-size: 1.3rem;
  }

  .download-option {
    padding: 1rem;
    flex-direction: column;
    text-align: center;
    gap: 1rem;
  }

  .option-icon {
    font-size: 2.5rem;
    margin-right: 0;
    margin-bottom: 0.5rem;
  }

  .option-content {
    text-align: center;
  }

  .option-arrow {
    margin-left: 0;
  }

  .intro-title {
    font-size: 1.5rem;
  }

  .intro-text h3 {
    font-size: 1.3rem;
  }

  .intro-text p {
    font-size: 0.9rem;
  }

  .feature-item {
    padding: 1rem;
  }

  .features-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}
</style>
