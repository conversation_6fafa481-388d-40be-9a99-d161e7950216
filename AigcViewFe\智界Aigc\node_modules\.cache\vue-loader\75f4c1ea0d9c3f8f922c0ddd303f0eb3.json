{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\workflow\\components\\AgentDetailModal.vue?vue&type=template&id=f8301f64&scoped=true&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\workflow\\components\\AgentDetailModal.vue", "mtime": 1754510290845}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\n<div class=\"agent-detail-modal-wrapper\">\n  <a-modal\n    :visible=\"visible\"\n    :width=\"800\"\n    :footer=\"null\"\n    :closable=\"false\"\n    :maskClosable=\"true\"\n    @cancel=\"handleClose\"\n    class=\"agent-detail-modal custom-modal\"\n    :bodyStyle=\"{ padding: 0, borderRadius: '16px', overflow: 'hidden' }\"\n    :centered=\"true\"\n    :destroyOnClose=\"true\"\n  >\n  <!-- 加载状态 -->\n  <div v-if=\"loading\" class=\"loading-container\">\n    <a-spin size=\"large\" tip=\"加载中...\">\n      <div class=\"loading-placeholder\"></div>\n    </a-spin>\n  </div>\n\n  <!-- 主要内容 -->\n  <div v-else class=\"modal-content\">\n    <!-- 自定义关闭按钮 -->\n    <div class=\"custom-close-button\" @click=\"handleClose\">\n      <a-icon type=\"close\" />\n    </div>\n\n    <!-- 背景装饰 -->\n    <div class=\"modal-background\">\n      <div class=\"bg-pattern\"></div>\n      <div class=\"bg-gradient\"></div>\n    </div>\n    <!-- 1. 智能体基本信息区域 -->\n    <div class=\"agent-info-section\">\n      <div class=\"agent-header\">\n        <!-- 智能体头像 -->\n        <div class=\"agent-avatar\">\n          <img\n            v-if=\"agentDetail.agentAvatar\"\n            :src=\"agentDetail.agentAvatar\"\n            :alt=\"agentDetail.agentName\"\n            @error=\"handleImageError\"\n          />\n          <div v-else class=\"avatar-placeholder\">\n            <a-icon type=\"robot\" />\n          </div>\n        </div>\n\n        <!-- 智能体基本信息和价格 -->\n        <div class=\"agent-info-and-price\">\n          <!-- 智能体基本信息 -->\n          <div class=\"agent-basic-info\">\n            <h2 class=\"agent-name\">{{ agentDetail.agentName }}</h2>\n            <p class=\"agent-description\">{{ agentDetail.agentDescription }}</p>\n\n            <!-- 创作者信息 -->\n            <div class=\"creator-info\">\n              <div class=\"creator-avatar\">\n                <img\n                  v-if=\"agentDetail.creatorInfo && agentDetail.creatorInfo.avatar\"\n                  :src=\"agentDetail.creatorInfo.avatar\"\n                  :alt=\"agentDetail.creatorInfo.name\"\n                  @error=\"handleCreatorAvatarError\"\n                />\n                <a-icon v-else type=\"user\" />\n              </div>\n              <div class=\"creator-details\">\n                <div class=\"creator-name-line\">\n                  <span class=\"creator-name\">{{ creatorName }}</span>\n                  <span class=\"creator-type\">{{ authorTypeText }}</span>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- 价格信息 -->\n          <div class=\"price-section\">\n              <!-- SVIP免费优先显示（即使已购买也显示免费） -->\n              <div v-if=\"agentDetail.isFree\" class=\"price-container\">\n                <span class=\"free-price\">免费</span>\n              </div>\n              <div v-else-if=\"isPurchased\" class=\"price-container\">\n                <span class=\"purchased-price\">已购买</span>\n              </div>\n              <div v-else-if=\"agentDetail.showDiscountPrice\" class=\"price-container\">\n                <span class=\"discount-price\">¥{{ agentDetail.discountPrice || 0 }}</span>\n                <span class=\"original-price\">¥{{ agentDetail.originalPrice || agentDetail.price || 0 }}</span>\n              </div>\n              <div v-else class=\"price-container\">\n                <span class=\"current-price\">¥{{ agentDetail.discountPrice || agentDetail.price || agentDetail.originalPrice || 0 }}</span>\n              </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 2. 演示视频区域 -->\n    <div v-if=\"agentDetail.demoVideo\" class=\"demo-video-section\">\n      <h3 class=\"section-title\">\n        <a-icon type=\"play-circle\" />\n        演示视频\n      </h3>\n      <div class=\"video-container\">\n        <div class=\"video-wrapper\" v-if=\"!videoError\">\n          <video\n            ref=\"demoVideo\"\n            :src=\"agentDetail.demoVideo\"\n            class=\"demo-video\"\n            :muted=\"videoMuted\"\n            :autoplay=\"videoAutoplay\"\n            preload=\"metadata\"\n            @loadstart=\"handleVideoLoadStart\"\n            @loadeddata=\"handleVideoLoaded\"\n            @error=\"handleVideoError\"\n            @play=\"handleVideoPlay\"\n            @pause=\"handleVideoPause\"\n            @timeupdate=\"handleVideoTimeUpdate\"\n            @volumechange=\"handleVolumeChange\"\n            @click=\"toggleVideoPlay\"\n          >\n            您的浏览器不支持视频播放\n          </video>\n\n          <!-- 自定义视频控制栏 -->\n          <div class=\"video-controls\" v-show=\"showControls\">\n            <div class=\"controls-left\">\n              <!-- 播放/暂停按钮 -->\n              <a-button\n                type=\"link\"\n                :icon=\"videoPlaying ? 'pause' : 'caret-right'\"\n                @click=\"toggleVideoPlay\"\n                class=\"control-btn play-btn\"\n              />\n\n              <!-- 时间显示 -->\n              <span class=\"time-display\">\n                {{ formatTime(currentTime) }} / {{ formatTime(duration) }}\n              </span>\n            </div>\n\n            <div class=\"controls-center\">\n              <!-- 进度条 -->\n              <div class=\"progress-container\" @click=\"handleProgressClick\">\n                <div class=\"progress-bar\">\n                  <div\n                    class=\"progress-filled\"\n                    :style=\"{ width: progressPercent + '%' }\"\n                  ></div>\n                  <div\n                    class=\"progress-thumb\"\n                    :style=\"{ left: progressPercent + '%' }\"\n                  ></div>\n                </div>\n              </div>\n            </div>\n\n            <div class=\"controls-right\">\n              <!-- 音量控制 -->\n              <div class=\"volume-control\" @mouseenter=\"showVolumeSlider = true\" @mouseleave=\"showVolumeSlider = false\">\n                <a-button\n                  type=\"link\"\n                  :icon=\"videoMuted ? 'sound' : 'sound-filled'\"\n                  @click=\"toggleMute\"\n                  class=\"control-btn volume-btn\"\n                />\n                <div class=\"volume-slider\" v-show=\"showVolumeSlider\">\n                  <a-slider\n                    v-model=\"videoVolume\"\n                    :min=\"0\"\n                    :max=\"100\"\n                    :step=\"1\"\n                    vertical\n                    :tip-formatter=\"null\"\n                    @change=\"handleVolumeSliderChange\"\n                    class=\"volume-range\"\n                  />\n                </div>\n              </div>\n\n              <!-- 全屏按钮 -->\n              <a-button\n                type=\"link\"\n                icon=\"fullscreen\"\n                @click=\"toggleFullscreen\"\n                class=\"control-btn fullscreen-btn\"\n              />\n            </div>\n          </div>\n\n          <!-- 视频加载状态 -->\n          <div class=\"video-loading\" v-show=\"videoLoading\">\n            <a-spin size=\"large\">\n              <a-icon slot=\"indicator\" type=\"loading\" style=\"font-size: 24px;\" spin />\n            </a-spin>\n            <p>视频加载中...</p>\n          </div>\n        </div>\n\n        <!-- 视频加载失败占位符 -->\n        <div class=\"video-error-placeholder\" v-if=\"videoError\">\n          <div class=\"error-content\">\n            <a-icon type=\"exclamation-circle\" class=\"error-icon\" />\n            <h4>视频加载失败</h4>\n            <p>抱歉，演示视频暂时无法播放</p>\n            <a-button @click=\"retryVideoLoad\" type=\"primary\" ghost>\n              <a-icon type=\"reload\" />\n              重新加载\n            </a-button>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 3. 工作流列表区域 -->\n    <div class=\"workflow-section\">\n      <h3 class=\"section-title\">\n        工作流列表\n        <span class=\"workflow-count\">({{ workflowList.length }}个)</span>\n      </h3>\n      \n      <div v-if=\"workflowLoading\" class=\"workflow-loading\">\n        <a-spin tip=\"加载工作流中...\" />\n      </div>\n      \n      <div v-else-if=\"workflowList.length > 0\" class=\"workflow-list\">\n        <div\n          v-for=\"(workflow, index) in workflowList\"\n          :key=\"workflow.id\"\n          class=\"workflow-item\"\n        >\n          <div class=\"workflow-info\">\n            <div class=\"workflow-sequence\">{{ index + 1 }}</div>\n            <div class=\"workflow-avatar\">\n              <img\n                v-if=\"workflow.agentAvatar || agentDetail.agentAvatar\"\n                :src=\"workflow.agentAvatar || agentDetail.agentAvatar\"\n                :alt=\"workflow.workflowName\"\n                @error=\"handleWorkflowImageError\"\n              />\n              <a-icon v-else type=\"setting\" />\n            </div>\n            <div class=\"workflow-details\">\n              <h4 class=\"workflow-name\">{{ workflow.workflowName }}</h4>\n              <p class=\"workflow-description\">{{ workflow.workflowDescription }}</p>\n\n              <!-- 🔥 新增：输入参数说明 -->\n              <div class=\"workflow-params\">\n                <div class=\"params-label\">\n                  <a-icon type=\"setting\" />\n                  <span>输入参数说明</span>\n                </div>\n                <div class=\"params-content\">\n                  {{ workflow.inputParamsDesc || '暂无输入参数说明' }}\n                </div>\n              </div>\n            </div>\n          </div>\n          \n          <!-- 下载按钮 -->\n          <div class=\"workflow-actions\">\n              <!-- 未购买且非免费时显示购买提示 -->\n              <a-button\n                v-if=\"!isPurchased && !agentDetail.isFree\"\n                type=\"default\"\n                disabled\n                @click=\"handleDownloadTip\"\n              >\n                <a-icon type=\"download\" />\n                请先购买\n              </a-button>\n              <!-- 已购买或免费时可以下载 -->\n              <a-button\n                v-else\n                type=\"primary\"\n                @click=\"handleWorkflowDownload(workflow)\"\n                :loading=\"downloadLoading[workflow.id]\"\n              >\n                <a-icon type=\"download\" />\n                下载\n              </a-button>\n          </div>\n        </div>\n      </div>\n      \n      <div v-else class=\"workflow-empty\">\n        <a-empty description=\"暂无工作流\" />\n      </div>\n    </div>\n\n    <!-- 4. 底部操作按钮区域 -->\n    <div class=\"action-buttons modern-actions\">\n      <a-button @click=\"handleClose\" class=\"close-btn modern-btn-secondary\">\n        <a-icon type=\"close\" />\n        关闭\n      </a-button>\n\n      <div class=\"primary-actions\">\n          <!-- 查看详情按钮：已购买或免费时可点击 -->\n          <a-button\n            v-if=\"isPurchased || agentDetail.isFree\"\n            type=\"default\"\n            @click=\"handleViewDetail\"\n            class=\"detail-btn modern-btn-outline\"\n          >\n            <a-icon type=\"eye\" />\n            查看详情\n          </a-button>\n          <a-button\n            v-else\n            type=\"default\"\n            disabled\n            class=\"detail-btn modern-btn-outline disabled\"\n          >\n            <a-icon type=\"eye\" />\n            查看详情\n          </a-button>\n\n          <!-- 购买按钮：只有在未购买且非免费时才显示 -->\n          <a-button\n            v-if=\"!isPurchased && !agentDetail.isFree\"\n            type=\"primary\"\n            @click=\"handlePurchase\"\n            :loading=\"purchaseLoading\"\n            class=\"purchase-btn modern-btn-primary\"\n          >\n            <a-icon type=\"shopping-cart\" />\n            立即购买\n          </a-button>\n\n        <a-button\n          v-if=\"agentDetail.experienceLink\"\n          type=\"default\"\n          @click=\"handleExperience\"\n          class=\"experience-btn modern-btn-outline\"\n        >\n          <a-icon type=\"robot\" />\n          体验智能体\n        </a-button>\n        <a-button\n          v-else\n          type=\"default\"\n          disabled\n          class=\"experience-btn modern-btn-outline disabled\"\n        >\n          <a-icon type=\"robot\" />\n          暂无体验\n        </a-button>\n      </div>\n    </div>\n  </div>\n</a-modal>\n\n<!-- 支付方式选择弹窗 -->\n<a-modal\n  v-model=\"showPaymentModal\"\n  title=\"选择支付方式\"\n  :width=\"520\"\n  :footer=\"null\"\n  :maskClosable=\"false\"\n  :centered=\"true\"\n  class=\"payment-modal\"\n>\n  <div class=\"payment-content\">\n    <!-- 订单信息卡片 -->\n    <div class=\"order-info-card\">\n      <div class=\"order-header\">\n        <div class=\"order-icon\">\n          <a-icon type=\"shopping-cart\" />\n        </div>\n        <div class=\"order-title\">\n          <h3>订单详情</h3>\n          <p>请确认您的购买信息</p>\n        </div>\n      </div>\n      <div class=\"order-details\">\n        <div class=\"order-item\">\n          <span class=\"label\">智能体名称</span>\n          <span class=\"value\">{{ orderInfo && orderInfo.agentName }}</span>\n        </div>\n        <div class=\"order-item total\">\n          <span class=\"label\">支付金额</span>\n          <span class=\"price\">¥{{ orderInfo && orderInfo.purchasePrice }}</span>\n        </div>\n      </div>\n    </div>\n\n    <!-- 支付方式选择 -->\n    <div class=\"payment-methods\">\n      <div class=\"payment-header\">\n        <h3>选择支付方式</h3>\n        <p>请选择您偏好的支付方式完成购买</p>\n      </div>\n\n      <!-- 余额支付 -->\n      <div class=\"payment-option\" @click=\"selectPaymentMethod('balance')\" :class=\"{\n        'insufficient': userBalance < (orderInfo && orderInfo.purchasePrice),\n        'selected': selectedPaymentMethod === 'balance'\n      }\">\n        <div class=\"payment-icon balance\">\n          <a-icon type=\"wallet\" />\n        </div>\n        <div class=\"payment-info\">\n          <div class=\"payment-title\">\n            账户余额支付\n            <span v-if=\"userBalance < (orderInfo && orderInfo.purchasePrice)\" class=\"insufficient-tag\">余额不足</span>\n          </div>\n          <div class=\"payment-desc\">当前余额：¥{{ userBalance }}</div>\n        </div>\n        <div class=\"payment-status\">\n          <a-icon v-if=\"selectedPaymentMethod === 'balance'\" type=\"check-circle\" class=\"selected-icon\" />\n          <a-icon v-else-if=\"userBalance < (orderInfo && orderInfo.purchasePrice)\" type=\"exclamation-circle\" class=\"insufficient\" />\n          <a-icon v-else type=\"wallet\" class=\"available\" />\n        </div>\n      </div>\n\n      <!-- 支付宝支付 -->\n      <div class=\"payment-option\" @click=\"selectPaymentMethod('alipay')\" :class=\"{ 'selected': selectedPaymentMethod === 'alipay' }\">\n        <div class=\"payment-icon alipay\">\n          <a-icon type=\"alipay\" />\n        </div>\n        <div class=\"payment-info\">\n          <div class=\"payment-title\">\n            支付宝支付\n          </div>\n          <div class=\"payment-desc\">安全便捷的在线支付</div>\n        </div>\n        <div class=\"payment-status\">\n          <a-icon v-if=\"selectedPaymentMethod === 'alipay'\" type=\"check-circle\" class=\"selected-icon\" />\n          <a-icon v-else type=\"right\" />\n        </div>\n      </div>\n    </div>\n\n    <div class=\"payment-actions\">\n      <a-button @click=\"showPaymentModal = false\" class=\"cancel-btn\">\n        <a-icon type=\"close\" />\n        取消支付\n      </a-button>\n\n      <a-button\n        type=\"primary\"\n        @click=\"confirmPayment\"\n        :disabled=\"!selectedPaymentMethod || (selectedPaymentMethod === 'balance' && userBalance < (orderInfo && orderInfo.purchasePrice))\"\n        :loading=\"paymentLoading\"\n        class=\"confirm-payment-btn\"\n      >\n        <a-icon type=\"credit-card\" />\n        确认支付 ¥{{ orderInfo && orderInfo.purchasePrice }}\n      </a-button>\n    </div>\n  </div>\n\n  <div v-if=\"paymentLoading\" class=\"payment-loading\">\n    <a-spin size=\"large\" tip=\"处理支付中...\" />\n  </div>\n</a-modal>\n</div>\n", null]}