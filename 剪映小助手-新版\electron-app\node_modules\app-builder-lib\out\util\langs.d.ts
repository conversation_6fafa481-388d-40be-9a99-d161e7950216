export declare const bundledLanguages: string[];
export declare function toLangWithRegion(lang: string): string;
export declare const lcid: any;
export declare const langIdToName: {
    ab: string;
    aa: string;
    af: string;
    ak: string;
    sq: string;
    am: string;
    ar: string;
    an: string;
    hy: string;
    as: string;
    av: string;
    ae: string;
    ay: string;
    az: string;
    bm: string;
    ba: string;
    eu: string;
    be: string;
    bn: string;
    bh: string;
    bi: string;
    bs: string;
    br: string;
    bg: string;
    my: string;
    ca: string;
    ch: string;
    ce: string;
    ny: string;
    zh: string;
    cv: string;
    kw: string;
    co: string;
    cr: string;
    hr: string;
    cs: string;
    da: string;
    dv: string;
    nl: string;
    dz: string;
    en: string;
    eo: string;
    et: string;
    ee: string;
    fo: string;
    fj: string;
    fi: string;
    fr: string;
    ff: string;
    gl: string;
    ka: string;
    de: string;
    el: string;
    gn: string;
    gu: string;
    ht: string;
    ha: string;
    he: string;
    hz: string;
    hi: string;
    ho: string;
    hu: string;
    ia: string;
    id: string;
    ie: string;
    ga: string;
    ig: string;
    ik: string;
    io: string;
    is: string;
    it: string;
    iu: string;
    ja: string;
    jv: string;
    kl: string;
    kn: string;
    kr: string;
    ks: string;
    kk: string;
    km: string;
    ki: string;
    rw: string;
    ky: string;
    kv: string;
    kg: string;
    ko: string;
    ku: string;
    kj: string;
    la: string;
    lb: string;
    lg: string;
    li: string;
    ln: string;
    lo: string;
    lt: string;
    lu: string;
    lv: string;
    gv: string;
    mk: string;
    mg: string;
    ms: string;
    ml: string;
    mt: string;
    mi: string;
    mr: string;
    mh: string;
    mn: string;
    na: string;
    nv: string;
    nd: string;
    ne: string;
    ng: string;
    nb: string;
    nn: string;
    no: string;
    ii: string;
    nr: string;
    oc: string;
    oj: string;
    cu: string;
    om: string;
    or: string;
    os: string;
    pa: string;
    pi: string;
    fa: string;
    pl: string;
    ps: string;
    pt: string;
    qu: string;
    rm: string;
    rn: string;
    ro: string;
    ru: string;
    sa: string;
    sc: string;
    sd: string;
    se: string;
    sm: string;
    sg: string;
    sr: string;
    gd: string;
    sn: string;
    si: string;
    sk: string;
    sl: string;
    so: string;
    st: string;
    es: string;
    su: string;
    sw: string;
    ss: string;
    sv: string;
    ta: string;
    te: string;
    tg: string;
    th: string;
    ti: string;
    bo: string;
    tk: string;
    tl: string;
    tn: string;
    to: string;
    tr: string;
    ts: string;
    tt: string;
    tw: string;
    ty: string;
    ug: string;
    uk: string;
    ur: string;
    uz: string;
    ve: string;
    vi: string;
    vo: string;
    wa: string;
    cy: string;
    wo: string;
    fy: string;
    xh: string;
    yi: string;
    yo: string;
    za: string;
    zu: string;
};
