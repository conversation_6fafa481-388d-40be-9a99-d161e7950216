
/*列表上方操作按钮区域*/
.ant-card-body .table-operator {
  margin-bottom: 8px;
}
/** Button按钮间距 */
.table-operator .ant-btn {
  margin: 0 8px 8px 0;
}
.table-operator .ant-btn-group .ant-btn {
  margin: 0;
}

.table-operator .ant-btn-group .ant-btn:last-child {
  margin: 0 8px 8px 0;
}
/*列表td的padding设置 可以控制列表大小*/
.ant-table-tbody .ant-table-row td {
  padding-top: 15px;
  padding-bottom: 15px;
}

/*列表页面弹出modal*/
.ant-modal-cust-warp {
  height: 100%
}

/*弹出modal Y轴滚动条*/
.ant-modal-cust-warp .ant-modal-body {
  height: calc(100% - 110px) !important;
  overflow-y: auto
}

/*弹出modal 先有content后有body 故滚动条控制在body上*/
.ant-modal-cust-warp .ant-modal-content {
  height: 90% !important;
  overflow-y: hidden
}
/*列表中有图片的加这个样式 参考用户管理*/
.anty-img-wrap {
  height: 25px;
  position: relative;
}
.anty-img-wrap > img {
  max-height: 100%;
}
/*列表中范围查询样式*/
.query-group-cust{width: calc(50% - 10px)}
.query-group-split-cust:before{content:"~";width: 20px;display: inline-block;text-align: center}


/*erp风格子表外框padding设置*/
.ant-card-wider-padding.cust-erp-sub-tab>.ant-card-body{padding:5px 12px}

/* 内嵌子表背景颜色 */
.j-inner-table-wrapper /deep/ .ant-table-expanded-row .ant-table-wrapper .ant-table-tbody .ant-table-row {
  background-color: #FFFFFF;
}

/**隐藏样式-modal确定按钮 */
.jee-hidden{display: none}

/* 🔥 悬浮式剪映小助手推广组件样式 */
.jianying-float-container {
  position: fixed;
  bottom: 12rem;
  right: 1rem;
  z-index: 999;
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 剪映小助手悬浮按钮 */
.jianying-float-btn {
  position: relative;
  width: 180px;
  height: 65px;
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 30%, #d63031 70%, #c0392b 100%);
  border: 2px solid rgba(255, 107, 107, 0.4);
  border-radius: 32px;
  color: white;
  cursor: pointer;
  overflow: hidden;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.6rem;
  box-shadow:
    0 6px 25px rgba(0, 0, 0, 0.4),
    0 0 25px rgba(255, 107, 107, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1),
    inset 0 -1px 0 rgba(0, 0, 0, 0.2);
}

.jianying-float-btn:hover {
  transform: translateY(-4px);
  border-color: rgba(255, 107, 107, 0.8);
  box-shadow:
    0 12px 40px rgba(0, 0, 0, 0.5),
    0 0 40px rgba(255, 107, 107, 0.5),
    inset 0 1px 0 rgba(255, 255, 255, 0.2),
    inset 0 -1px 0 rgba(0, 0, 0, 0.3);
}

/* 关闭按钮 */
.float-close-btn {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 10px;
  z-index: 10;
}

.float-close-btn:hover {
  background: rgba(255, 255, 255, 0.4);
  transform: scale(1.1);
}

/* 按钮图标 */
.btn-icon {
  font-size: 1.3rem;
  color: #ffeaa7;
  transition: all 0.3s ease;
  z-index: 2;
}

.jianying-float-btn:hover .btn-icon {
  color: #fdcb6e;
  transform: translateY(-3px);
  animation: jianyingDownload 0.8s ease-in-out;
}

@keyframes jianyingDownload {
  0% { transform: translateY(0) rotate(0deg); }
  25% { transform: translateY(-6px) rotate(-10deg); }
  50% { transform: translateY(-8px) rotate(0deg); }
  75% { transform: translateY(-4px) rotate(8deg); }
  100% { transform: translateY(-3px) rotate(0deg); }
}

/* 按钮文字 */
.btn-text {
  font-size: 0.9rem;
  font-weight: 700;
  color: rgba(255, 255, 255, 0.95);
  transition: all 0.3s ease;
  z-index: 2;
  white-space: nowrap;
  letter-spacing: 0.5px;
}

.jianying-float-btn:hover .btn-text {
  color: white;
  text-shadow: 0 0 15px rgba(255, 107, 107, 0.8);
}

/* 发光效果 */
.btn-glow {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(255, 107, 107, 0.15) 0%, rgba(238, 90, 36, 0.15) 100%);
  border-radius: 32px;
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 1;
}

.jianying-float-btn:hover .btn-glow {
  opacity: 1;
  animation: jianyingPulse 2.5s ease-in-out infinite;
}

@keyframes jianyingPulse {
  0%, 100% { opacity: 0.4; transform: scale(1); }
  50% { opacity: 0.8; transform: scale(1.08); }
}

/* 波纹效果 */
.jianying-waves {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 100%;
  height: 100%;
  transform: translate(-50%, -50%);
  border-radius: 32px;
  z-index: 0;
}

.jianying-waves::before,
.jianying-waves::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 2px solid rgba(255, 107, 107, 0.3);
  border-radius: 32px;
  opacity: 0;
}

.jianying-float-btn:hover .jianying-waves::before {
  animation: jianyingWave1 2s ease-out infinite;
}

.jianying-float-btn:hover .jianying-waves::after {
  animation: jianyingWave2 2s ease-out infinite 0.5s;
}

@keyframes jianyingWave1 {
  0% { transform: scale(1); opacity: 0.6; }
  100% { transform: scale(1.5); opacity: 0; }
}

@keyframes jianyingWave2 {
  0% { transform: scale(1); opacity: 0.4; }
  100% { transform: scale(1.8); opacity: 0; }
}

/* 粒子容器 */
.btn-particles {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 3;
}

.jianying-particle {
  position: absolute;
  border-radius: 50%;
  pointer-events: none;
}

/* 光束扫过效果 */
.jianying-float-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    rgba(255, 107, 107, 0.3),
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.6s ease;
  z-index: 1;
  border-radius: 32px;
}

.jianying-float-btn:hover::before {
  left: 100%;
}







/* 动画效果 */

/* 响应式设计 */
@media (max-width: 1200px) {
  .jianying-float-container {
    right: 0.8rem;
  }
}

@media (max-width: 768px) {
  .jianying-float-container {
    bottom: 10rem;
    right: 0.5rem;
  }

  .jianying-float-btn {
    width: 160px;
    height: 55px;
    border-radius: 28px;
  }

  .btn-icon {
    font-size: 1.1rem;
  }

  .btn-text {
    font-size: 0.8rem;
  }
}

@media (max-width: 480px) {
  .jianying-float-container {
    bottom: 8rem;
    right: 0.3rem;
  }

  .jianying-float-btn {
    width: 140px;
    height: 50px;
    border-radius: 25px;
  }

  .btn-icon {
    font-size: 1rem;
  }

  .btn-text {
    font-size: 0.75rem;
  }
}