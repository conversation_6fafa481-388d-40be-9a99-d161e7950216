<template>
  <div class="tencent-carousel" ref="carousel" @mouseenter="showControls" @mouseleave="hideControls" :class="{ 'loaded': isLoaded }">
    <div class="carousel-container">
      <!-- 轮播图主体 -->
      <div class="carousel-wrapper" ref="wrapper">
        <div
          v-for="(slide, index) in slides"
          :key="index"
          class="carousel-slide"
          :class="{ 'active': index === currentSlide }"
        >
          <!-- 背景图片 -->
          <div class="slide-background">
            <img :src="slide.image" :alt="slide.title" />
            <div class="slide-overlay"></div>
            <div class="slide-gradient"></div>
          </div>

          <!-- 内容区域 - 简洁左对齐 -->
          <div class="slide-content">
            <div class="content-wrapper">
              <div class="slide-badge" v-if="slide.badge">
                {{ slide.badge }}
              </div>
              <h2 class="slide-title">{{ slide.title }}</h2>
              <p class="slide-description">{{ slide.description }}</p>
              <div class="slide-actions" v-if="slide.primaryAction">
                <button class="btn-primary" @click="handleSlideAction(slide)">
                  {{ slide.primaryAction.text }}
                  <a-icon type="arrow-right" />
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 现代化控制面板 -->
      <div class="carousel-controls">
        <!-- 指示器和切换按钮组合 -->
        <div class="controls-panel" :class="{ 'visible': controlsVisible }">
          <!-- 上一张按钮 -->
          <button class="control-btn prev-btn" @click="prevSlideHandler" :disabled="isTransitioning">
            <a-icon type="left" />
          </button>

          <!-- 指示器区域 -->
          <div class="modern-indicators">
            <div
              v-for="(slide, index) in slides"
              :key="index"
              class="modern-indicator"
              :class="{ 'active': index === currentSlide }"
              @click="goToSlide(index)"
            >
              <div class="indicator-progress" v-if="index === currentSlide"></div>
            </div>
          </div>

          <!-- 下一张按钮 -->
          <button class="control-btn next-btn" @click="nextSlideHandler" :disabled="isTransitioning">
            <a-icon type="right" />
          </button>
        </div>

        <!-- 播放/暂停按钮 -->
        <div class="play-control" :class="{ 'visible': controlsVisible }">
          <button class="play-pause-btn" @click="toggleAutoPlay">
            <a-icon :type="isPlaying ? 'pause' : 'play-circle'" />
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'TencentCarousel',
  props: {
    slides: {
      type: Array,
      required: true
    },
    autoPlay: {
      type: Boolean,
      default: true
    },
    interval: {
      type: Number,
      default: 5000
    },
    transitionDuration: {
      type: Number,
      default: 600 // 从800ms减少到600ms，让轮播图切换更快
    }
  },
  data() {
    return {
      currentSlide: 0,
      isTransitioning: false,
      isPlaying: this.autoPlay,
      autoPlayTimer: null,
      controlsVisible: false,
      progressTimer: null,
      progressStartTime: null,
      isLoaded: false
    }
  },
  computed: {
    prevSlide() {
      return this.currentSlide === 0 ? this.slides.length - 1 : this.currentSlide - 1
    },
    nextSlide() {
      return this.currentSlide === this.slides.length - 1 ? 0 : this.currentSlide + 1
    }
  },
  mounted() {
    this.initCarousel()

    // 验证slides数据
    if (!this.slides || this.slides.length === 0) {
      console.warn('AdvancedCarousel: No slides data provided')
      return
    }

    // 延迟显示轮播图，创建平滑的入场效果
    setTimeout(() => {
      this.isLoaded = true

      // 再延迟一点启动自动播放，确保动画完成
      setTimeout(() => {
        if (this.isPlaying) {
          this.startAutoPlay()
        }

        // 初始提示动画，让用户知道有控制器
        this.showInitialHint()
      }, 500)
    }, 300)

    console.log('AdvancedCarousel mounted with', this.slides.length, 'slides')
  },
  beforeDestroy() {
    this.stopAutoPlay()
    this.stopProgress()
    this.clearAllTimers()
  },
  methods: {
    initCarousel() {
      // 简单初始化，CSS处理过渡效果
    },

    goToSlide(index) {
      if (index === this.currentSlide || this.isTransitioning) return

      this.isTransitioning = true
      this.stopProgress() // 停止当前进度条
      this.currentSlide = index

      // 重新开始进度条动画
      if (this.isPlaying) {
        this.startProgress()
      }

      setTimeout(() => {
        this.isTransitioning = false
      }, 600) // 与CSS transition时间一致
    },
    
    nextSlideHandler() {
      console.log('Next slide clicked, current:', this.currentSlide)
      const nextIndex = this.currentSlide === this.slides.length - 1 ? 0 : this.currentSlide + 1
      this.goToSlide(nextIndex)
    },

    prevSlideHandler() {
      console.log('Previous slide clicked, current:', this.currentSlide)
      const prevIndex = this.currentSlide === 0 ? this.slides.length - 1 : this.currentSlide - 1
      this.goToSlide(prevIndex)
    },
    
    startAutoPlay() {
      this.stopAutoPlay() // 确保清理之前的定时器
      this.autoPlayTimer = setInterval(() => {
        this.nextSlideHandler()
      }, this.interval)
      this.startProgress() // 开始进度条动画
    },

    stopAutoPlay() {
      if (this.autoPlayTimer) {
        clearInterval(this.autoPlayTimer)
        this.autoPlayTimer = null
      }
      this.stopProgress() // 同时停止进度条
    },

    // 控制面板显示/隐藏
    showControls() {
      this.controlsVisible = true
    },

    hideControls() {
      this.controlsVisible = false
    },

    // 播放/暂停切换
    toggleAutoPlay() {
      if (this.isPlaying) {
        this.stopAutoPlay()
        this.isPlaying = false
      } else {
        this.startAutoPlay()
        this.isPlaying = true
      }
    },

    // 初始提示动画
    showInitialHint() {
      // 2秒后显示控制器提示用户
      setTimeout(() => {
        this.controlsVisible = true
        // 3秒后自动隐藏
        setTimeout(() => {
          this.controlsVisible = false
        }, 3000)
      }, 2000)
    },

    // 开始进度条动画
    startProgress() {
      this.stopProgress() // 先停止之前的进度条
      this.progressStartTime = Date.now()

      // 重新触发CSS动画
      this.$nextTick(() => {
        const progressBar = this.$el.querySelector('.indicator-progress')
        if (progressBar) {
          progressBar.style.animation = 'none'
          progressBar.offsetHeight // 触发重排
          progressBar.style.animation = `progressBar ${this.interval}ms linear`
        }
      })
    },

    // 停止进度条动画
    stopProgress() {
      if (this.progressTimer) {
        clearTimeout(this.progressTimer)
        this.progressTimer = null
      }

      // 停止CSS动画
      if (this.$el) {
        const progressBar = this.$el.querySelector('.indicator-progress')
        if (progressBar) {
          progressBar.style.animation = 'none'
        }
      }
    },

    // 清理所有定时器
    clearAllTimers() {
      if (this.autoPlayTimer) {
        clearInterval(this.autoPlayTimer)
        this.autoPlayTimer = null
      }
      if (this.progressTimer) {
        clearTimeout(this.progressTimer)
        this.progressTimer = null
      }
    },

    // 处理轮播图按钮点击
    handleSlideAction(slide) {
      console.log('轮播图按钮被点击:', slide.title)

      // 发射事件给父组件处理
      this.$emit('slide-action', {
        slide: slide,
        action: slide.primaryAction
      })

      // 如果有链接，可以直接跳转
      if (slide.primaryAction && slide.primaryAction.link) {
        // 内部路由跳转
        if (slide.primaryAction.link.startsWith('/')) {
          this.$router.push(slide.primaryAction.link)
        } else {
          // 外部链接
          window.open(slide.primaryAction.link, '_blank')
        }
      }
    }
  }
}
</script>

<style scoped>
/* 腾讯风格轮播图 */
.tencent-carousel {
  position: relative;
  width: 100%;
  height: 720px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.8s ease-out;
}

.tencent-carousel.loaded {
  opacity: 1;
  transform: translateY(0);
}

.carousel-container {
  position: relative;
  width: 100%;
  height: 100%;
}

.carousel-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
}

.carousel-slide {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  transition: opacity 0.6s ease-in-out;
}

.carousel-slide.active {
  opacity: 1;
}

/* 背景图片 */
.slide-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.slide-background img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 灰色透明遮罩层 - 确保页头可读性 */
.slide-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    to bottom,
    rgba(0, 0, 0, 0.4) 0%,
    rgba(0, 0, 0, 0.3) 20%,
    rgba(0, 0, 0, 0.2) 50%,
    rgba(0, 0, 0, 0.1) 100%
  );
  z-index: 2;
}

.slide-gradient {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.6) 0%, rgba(0, 0, 0, 0.2) 50%, rgba(0, 0, 0, 0.1) 100%);
  z-index: 3;
}

/* 内容区域 */
.slide-content {
  position: relative;
  z-index: 4;
  height: 100%;
  display: flex;
  align-items: center;
  padding: 0 4rem;
}

.content-wrapper {
  max-width: 550px;
  color: white;
  margin-left: 150px;
  opacity: 0;
  transform: translateX(-30px);
  transition: all 0.6s ease-out 0.3s;
}

.tencent-carousel.loaded .content-wrapper {
  opacity: 1;
  transform: translateX(0);
}

/* 现代简洁左侧布局 */
.slide-badge {
  display: inline-block;
  padding: 0.6rem 1.5rem;
  background: rgba(59, 130, 246, 0.9);
  color: white;
  border-radius: 25px;
  font-size: 0.9rem;
  font-weight: 600;
  margin-bottom: 2rem;
  backdrop-filter: blur(10px);
  text-transform: uppercase;
  letter-spacing: 1px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.slide-title {
  font-size: 3.8rem;
  font-weight: 900;
  line-height: 1.1;
  color: white;
  margin-bottom: 1.5rem;
  text-shadow: 0 4px 30px rgba(0, 0, 0, 0.7);
  letter-spacing: -1px;
}

.slide-description {
  font-size: 1.3rem;
  line-height: 1.6;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 3rem;
  text-shadow: 0 2px 15px rgba(0, 0, 0, 0.5);
  max-width: 480px;
}

.slide-actions {
  display: flex;
  gap: 1.5rem;
  align-items: center;
}

.slide-actions {
  display: flex;
  gap: 1rem;
}

.btn-primary {
  padding: 1.2rem 3rem;
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  border: 2px solid rgba(255, 255, 255, 0.3);
  color: white;
  border-radius: 8px;
  font-size: 1.1rem;
  font-weight: 700;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.8rem;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 1px;
  backdrop-filter: blur(15px);
  box-shadow: 0 10px 40px rgba(59, 130, 246, 0.4);
  transform: skew(-10deg);
  position: relative;
  overflow: hidden;
}

.btn-primary::after {
  content: '';
  position: absolute;
  top: 20%;
  right: 30%;
  width: 3px;
  height: 3px;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 50%;
  transform: scale(0);
  transition: all 0.4s ease 0.1s;
  box-shadow:
    8px 12px 0 -1px rgba(255, 255, 255, 0.4),
    -12px 8px 0 -1px rgba(255, 255, 255, 0.4),
    15px -5px 0 -2px rgba(255, 255, 255, 0.3),
    -8px -12px 0 -1px rgba(255, 255, 255, 0.3);
}

.btn-primary::before {
  content: '';
  position: absolute;
  top: 30%;
  left: -20px;
  width: 4px;
  height: 4px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  opacity: 0;
  transition: all 0.8s ease;
  box-shadow:
    8px 5px 0 -1px rgba(255, 255, 255, 0.8),
    16px -3px 0 -1px rgba(255, 255, 255, 0.7),
    24px 8px 0 -2px rgba(255, 255, 255, 0.6),
    32px 2px 0 -2px rgba(255, 255, 255, 0.5),
    40px -5px 0 -3px rgba(255, 255, 255, 0.4),
    48px 6px 0 -3px rgba(255, 255, 255, 0.3),
    56px -2px 0 -4px rgba(255, 255, 255, 0.2),
    64px 4px 0 -4px rgba(255, 255, 255, 0.1);
  animation: particleTrail 1.5s ease-in-out infinite;
}

.btn-primary:hover {
  transform: skew(-10deg) translateY(-4px) scale(1.05);
  box-shadow: 0 15px 50px rgba(59, 130, 246, 0.6);
  border-color: rgba(255, 255, 255, 0.5);
}

.btn-primary:hover::after {
  transform: scale(1);
  box-shadow:
    16px 24px 8px 0px rgba(255, 255, 255, 0.5),
    -24px 16px 8px 0px rgba(255, 255, 255, 0.5),
    30px -10px 12px -5px rgba(255, 255, 255, 0.4),
    -16px -24px 8px 0px rgba(255, 255, 255, 0.4),
    20px 8px 6px 0px rgba(255, 255, 255, 0.3),
    -8px 20px 6px 0px rgba(255, 255, 255, 0.3);
}

.btn-primary:hover::before {
  left: calc(100% + 20px);
  opacity: 1;
  animation: particleTrailHover 0.8s ease-out forwards;
}

/* 现代化控制面板 */
.carousel-controls {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 120px;
  z-index: 10;
  pointer-events: none;
}

.controls-panel {
  position: absolute;
  bottom: 2rem;
  left: 50%;
  transform: translateX(-50%) translateY(20px);
  display: flex;
  align-items: center;
  gap: 1.5rem;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(20px);
  border-radius: 50px;
  padding: 0.75rem 1.5rem;
  opacity: 0;
  transition: all 0.6s ease 0.5s;
  pointer-events: auto;
}

.tencent-carousel.loaded .controls-panel {
  opacity: 0.3;
  transform: translateX(-50%) translateY(0);
}

.controls-panel.visible {
  opacity: 1;
  transform: translateX(-50%) translateY(0);
}

/* 轮播图悬停时控制面板和指示器亮度提升 */
.tencent-carousel:hover .controls-panel {
  opacity: 1;
}

.tencent-carousel:hover .modern-indicator {
  background: rgba(255, 255, 255, 0.6);
}

.tencent-carousel:hover .modern-indicator.active {
  background: rgba(255, 255, 255, 1);
}

.tencent-carousel:hover .play-control {
  opacity: 1;
}

/* 控制按钮 */
.control-btn {
  width: 36px;
  height: 36px;
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  color: white;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.control-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
  transform: scale(1.1);
}

.control-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 现代化指示器 */
.modern-indicators {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.modern-indicator {
  position: relative;
  width: 32px;
  height: 6px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
  cursor: pointer;
  transition: all 0.3s ease;
  overflow: hidden;
}

.modern-indicator:hover {
  background: rgba(255, 255, 255, 0.8);
  transform: scaleY(1.3);
  box-shadow: 0 2px 8px rgba(255, 255, 255, 0.3);
}

.modern-indicator.active {
  background: rgba(255, 255, 255, 0.9);
  box-shadow: 0 2px 12px rgba(255, 255, 255, 0.4);
}

.modern-indicator.active:hover {
  background: rgba(255, 255, 255, 1);
  transform: scaleY(1.4);
  box-shadow: 0 3px 15px rgba(255, 255, 255, 0.5);
}

.indicator-progress {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 0%;
  background: white;
  border-radius: 3px;
  transition: width 0.1s ease;
}

@keyframes progressBar {
  from { width: 0%; }
  to { width: 100%; }
}

/* 颗粒拖尾动画 */
@keyframes particleTrail {
  0% {
    transform: translateY(-50%) translateX(0px);
    box-shadow:
      8px 5px 0 -1px rgba(255, 255, 255, 0.8),
      16px -3px 0 -1px rgba(255, 255, 255, 0.7),
      24px 8px 0 -2px rgba(255, 255, 255, 0.6),
      32px 2px 0 -2px rgba(255, 255, 255, 0.5),
      40px -5px 0 -3px rgba(255, 255, 255, 0.4),
      48px 6px 0 -3px rgba(255, 255, 255, 0.3);
  }
  50% {
    transform: translateY(-50%) translateX(5px);
    box-shadow:
      12px 8px 0 -1px rgba(255, 255, 255, 0.9),
      20px -1px 0 -1px rgba(255, 255, 255, 0.8),
      28px 12px 0 -2px rgba(255, 255, 255, 0.7),
      36px 4px 0 -2px rgba(255, 255, 255, 0.6),
      44px -3px 0 -3px rgba(255, 255, 255, 0.5),
      52px 9px 0 -3px rgba(255, 255, 255, 0.4);
  }
  100% {
    transform: translateY(-50%) translateX(-2px);
    box-shadow:
      6px 3px 0 -1px rgba(255, 255, 255, 0.7),
      14px -5px 0 -1px rgba(255, 255, 255, 0.6),
      22px 6px 0 -2px rgba(255, 255, 255, 0.5),
      30px 0px 0 -2px rgba(255, 255, 255, 0.4),
      38px -7px 0 -3px rgba(255, 255, 255, 0.3),
      46px 4px 0 -3px rgba(255, 255, 255, 0.2);
  }
}

@keyframes particleTrailHover {
  0% {
    box-shadow:
      8px 5px 0 -1px rgba(255, 255, 255, 0.8),
      16px -3px 0 -1px rgba(255, 255, 255, 0.7),
      24px 8px 0 -2px rgba(255, 255, 255, 0.6),
      32px 2px 0 -2px rgba(255, 255, 255, 0.5),
      40px -5px 0 -3px rgba(255, 255, 255, 0.4),
      48px 6px 0 -3px rgba(255, 255, 255, 0.3);
  }
  100% {
    box-shadow:
      -8px 5px 8px 2px rgba(255, 255, 255, 0.6),
      -16px -3px 6px 1px rgba(255, 255, 255, 0.5),
      -24px 8px 10px 0px rgba(255, 255, 255, 0.4),
      -32px 2px 8px -1px rgba(255, 255, 255, 0.3),
      -40px -5px 12px -2px rgba(255, 255, 255, 0.2),
      -48px 6px 6px -2px rgba(255, 255, 255, 0.1);
  }
}

/* 播放控制 */
.play-control {
  position: absolute;
  top: 2rem;
  right: 2rem;
  opacity: 0;
  transform: translateY(-20px);
  transition: all 0.6s ease 0.7s;
  pointer-events: auto;
}

.tencent-carousel.loaded .play-control {
  opacity: 0.2;
  transform: translateY(0);
}

.play-control.visible {
  opacity: 1;
}

.play-pause-btn {
  width: 44px;
  height: 44px;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  color: white;
  font-size: 1.2rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.play-pause-btn:hover {
  background: rgba(0, 0, 0, 0.8);
  border-color: rgba(255, 255, 255, 0.4);
  transform: scale(1.1);
}



/* 响应式设计 */
@media (max-width: 768px) {
  .tencent-carousel {
    height: 450px;
  }

  .slide-content {
    padding: 0 2rem;
  }

  .content-wrapper {
    margin-left: 0;
    text-align: center;
  }

  .slide-title {
    font-size: 2.5rem;
  }

  .slide-description {
    font-size: 1.1rem;
  }

  .slide-description {
    font-size: 1rem;
  }

  .slide-actions {
    flex-direction: column;
  }

  /* 移动端控制面板优化 */
  .controls-panel {
    bottom: 1rem;
    padding: 0.5rem 1rem;
    gap: 1rem;
  }

  .control-btn {
    width: 32px;
    height: 32px;
    font-size: 0.8rem;
  }

  .modern-indicator {
    width: 24px;
    height: 4px;
  }

  .play-control {
    top: 1rem;
    right: 1rem;
  }

  .play-pause-btn {
    width: 36px;
    height: 36px;
    font-size: 1rem;
  }
}
</style>
