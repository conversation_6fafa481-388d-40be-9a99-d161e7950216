/**
 * 个人中心页面GSAP动画
 * 遵循开发规范，使用GSAP实现所有动画效果
 */
import { gsap } from 'gsap'
import { ScrollTrigger } from 'gsap/ScrollTrigger'
import { responsiveConfig } from '../managers/ResponsiveConfig'

gsap.registerPlugin(ScrollTrigger)

export const usercenterAnimations = {
  /**
   * 初始化个人中心所有动画
   * @param {AnimationManager} animManager - 动画管理器
   */
  init(animManager) {
    const animations = []

    // 1. 页面入场动画
    const enterAnim = this.initPageEnterAnimation()
    if (enterAnim) animations.push(enterAnim)

    // 2. 侧边栏动画
    const sidebarAnim = this.initSidebarAnimation()
    if (sidebarAnim) animations.push(sidebarAnim)

    // 3. 内容区域动画
    const contentAnim = this.initContentAnimation()
    if (contentAnim) animations.push(contentAnim)

    // 4. 卡片悬停效果
    const cardHoverEffects = this.initCardHoverEffects()
    if (cardHoverEffects) animations.push(...cardHoverEffects)

    // 5. 数据加载动画
    const loadingAnim = this.initLoadingAnimations()
    if (loadingAnim) animations.push(loadingAnim)

    // 将所有动画添加到管理器
    animations.forEach(anim => {
      if (anim.scrollTrigger) {
        animManager.addScrollTrigger(anim.scrollTrigger)
      } else if (anim.timeline) {
        animManager.addTimeline(anim.timeline)
      } else {
        animManager.add(anim)
      }
    })

    return animations
  },

  /**
   * 页面入场动画
   */
  initPageEnterAnimation() {
    const tl = gsap.timeline()

    // 侧边栏从左滑入
    tl.from('.usercenter-sidebar', {
      duration: 0.6,
      x: -100,
      opacity: 0,
      ease: 'power2.out'
    })

    // 内容区域从右滑入
    .from('.usercenter-content', {
      duration: 0.6,
      x: 50,
      opacity: 0,
      ease: 'power2.out'
    }, '-=0.4')

    return { timeline: tl }
  },

  /**
   * 侧边栏动画
   */
  initSidebarAnimation() {
    const menuItems = document.querySelectorAll('.sidebar-menu-item')
    if (!menuItems.length) return null

    // 菜单项依次出现
    const menuAnim = gsap.from(menuItems, {
      duration: 0.4,
      y: 20,
      opacity: 0,
      stagger: 0.08,
      ease: 'power2.out',
      delay: 0.3
    })

    // 菜单项悬停效果
    menuItems.forEach(item => {
      item.addEventListener('mouseenter', () => {
        gsap.to(item, {
          duration: 0.3,
          x: 8,
          backgroundColor: 'rgba(124, 138, 237, 0.1)',
          ease: 'power2.out'
        })
      })

      item.addEventListener('mouseleave', () => {
        gsap.to(item, {
          duration: 0.3,
          x: 0,
          backgroundColor: 'transparent',
          ease: 'power2.out'
        })
      })
    })

    return menuAnim
  },

  /**
   * 内容区域动画
   */
  initContentAnimation() {
    const contentSections = document.querySelectorAll('.content-section')
    if (!contentSections.length) return null

    return gsap.from(contentSections, {
      duration: 0.6,
      y: 30,
      opacity: 0,
      stagger: 0.1,
      ease: 'power2.out',
      delay: 0.5
    })
  },

  /**
   * 卡片悬停效果
   */
  initCardHoverEffects() {
    const cards = document.querySelectorAll('.stats-card, .info-card, .action-card')
    const animations = []

    cards.forEach(card => {
      const hoverTl = gsap.timeline({ paused: true })

      hoverTl.to(card, {
        duration: 0.3,
        y: -8,
        scale: 1.02,
        boxShadow: '0 12px 40px rgba(124, 138, 237, 0.15)',
        ease: 'power2.out'
      })

      card.addEventListener('mouseenter', () => hoverTl.play())
      card.addEventListener('mouseleave', () => hoverTl.reverse())

      animations.push({ timeline: hoverTl })
    })

    return animations
  },

  /**
   * 数据加载动画
   */
  initLoadingAnimations() {
    // 骨架屏动画
    const skeletons = document.querySelectorAll('.skeleton-loading')
    if (!skeletons.length) return null

    return gsap.to(skeletons, {
      duration: 1.5,
      opacity: 0.3,
      ease: 'power2.inOut',
      repeat: -1,
      yoyo: true
    })
  },

  /**
   * 页面切换动画
   * @param {string} fromPage - 当前页面选择器
   * @param {string} toPage - 目标页面选择器
   */
  switchPage(fromPage, toPage) {
    const tl = gsap.timeline()

    // 当前页面淡出并向左移动
    tl.to(fromPage, {
      duration: 0.3,
      x: -50,
      opacity: 0,
      ease: 'power2.in'
    })

    // 目标页面从右侧滑入
    .fromTo(toPage, 
      { x: 50, opacity: 0, display: 'block' },
      {
        duration: 0.4,
        x: 0,
        opacity: 1,
        ease: 'power2.out'
      }
    )

    // 隐藏当前页面
    .set(fromPage, { display: 'none' })

    return tl
  },

  /**
   * 数字递增动画
   * @param {Element} element - 目标元素
   * @param {number} target - 目标数值
   * @param {Object} options - 动画选项
   */
  animateCounter(element, target, options = {}) {
    const params = responsiveConfig.getAnimationParams({
      duration: 2,
      ease: 'power2.out',
      ...options
    })

    return gsap.to(element, {
      ...params,
      innerHTML: target,
      snap: { innerHTML: 1 },
      onUpdate: function() {
        // 格式化数字显示
        const value = Math.round(this.targets()[0].innerHTML)
        if (value >= 10000) {
          this.targets()[0].innerHTML = (value / 10000).toFixed(1) + '万'
        } else if (value >= 1000) {
          this.targets()[0].innerHTML = (value / 1000).toFixed(1) + 'k'
        }
      }
    })
  },

  /**
   * 表格行动画
   * @param {string} tableSelector - 表格选择器
   */
  animateTableRows(tableSelector) {
    const rows = document.querySelectorAll(`${tableSelector} tbody tr`)
    if (!rows.length) return null

    return gsap.from(rows, {
      duration: 0.4,
      y: 20,
      opacity: 0,
      stagger: 0.05,
      ease: 'power2.out'
    })
  },

  /**
   * 进度条动画
   * @param {Element} progressBar - 进度条元素
   * @param {number} percentage - 目标百分比
   */
  animateProgressBar(progressBar, percentage) {
    return gsap.to(progressBar, {
      duration: 1.5,
      width: `${percentage}%`,
      ease: 'power2.out'
    })
  },

  /**
   * 图表入场动画
   * @param {string} chartSelector - 图表容器选择器
   */
  animateChart(chartSelector) {
    const chart = document.querySelector(chartSelector)
    if (!chart) return null

    return gsap.from(chart, {
      duration: 0.8,
      scale: 0.8,
      opacity: 0,
      ease: 'back.out(1.7)'
    })
  },

  /**
   * 通知消息动画
   * @param {Element} notification - 通知元素
   * @param {string} type - 通知类型：success、error、warning、info
   */
  showNotification(notification, type = 'info') {
    const tl = gsap.timeline()

    // 设置初始状态
    gsap.set(notification, {
      x: 300,
      opacity: 0,
      display: 'block'
    })

    // 滑入动画
    tl.to(notification, {
      duration: 0.5,
      x: 0,
      opacity: 1,
      ease: 'back.out(1.7)'
    })

    // 自动隐藏（3秒后）
    .to(notification, {
      duration: 0.3,
      x: 300,
      opacity: 0,
      ease: 'power2.in',
      delay: 3
    })

    // 隐藏元素
    .set(notification, { display: 'none' })

    return tl
  },

  /**
   * 模态框动画
   * @param {Element} modal - 模态框元素
   * @param {boolean} show - 显示或隐藏
   */
  animateModal(modal, show = true) {
    const overlay = modal.querySelector('.modal-overlay')
    const content = modal.querySelector('.modal-content')

    if (show) {
      const tl = gsap.timeline()

      gsap.set(modal, { display: 'flex' })

      tl.fromTo(overlay, 
        { opacity: 0 },
        { duration: 0.3, opacity: 1, ease: 'power2.out' }
      )
      .fromTo(content,
        { scale: 0.8, opacity: 0 },
        { duration: 0.4, scale: 1, opacity: 1, ease: 'back.out(1.7)' },
        '-=0.2'
      )

      return tl
    } else {
      const tl = gsap.timeline()

      tl.to(content, {
        duration: 0.3,
        scale: 0.8,
        opacity: 0,
        ease: 'power2.in'
      })
      .to(overlay, {
        duration: 0.2,
        opacity: 0,
        ease: 'power2.in'
      }, '-=0.1')
      .set(modal, { display: 'none' })

      return tl
    }
  }
}
