import TOSBase from '../base';
export interface SetObjectMetaInput {
    bucket?: string;
    key: string;
    versionId?: string;
    cacheControl?: string;
    contentDisposition?: string;
    contentEncoding?: string;
    contentLanguage?: string;
    contentType?: string;
    expires?: Date;
    meta?: Record<string, string>;
    headers?: {
        [key: string]: string | undefined;
        'Cache-Control'?: string;
        'Content-Disposition'?: string;
        Expires?: string;
        'Content-Type'?: string;
        'Content-Language'?: string;
    };
}
export declare function setObjectMeta(this: TOSBase, input: SetObjectMetaInput | string): Promise<import("../base").TosResponse<undefined>>;
export default setObjectMeta;
