import { TierType } from '../../TosExportEnum';
import TOSBase from '../base';
export interface RestoreObjectInput {
    bucket?: string;
    key: string;
    versionId?: string;
    days: number;
    restoreJobParameters?: {
        Tier: TierType;
    };
}
export declare function restoreObject(this: TOSBase, input: RestoreObjectInput): Promise<import("../base").TosResponse<undefined>>;
export default restoreObject;
