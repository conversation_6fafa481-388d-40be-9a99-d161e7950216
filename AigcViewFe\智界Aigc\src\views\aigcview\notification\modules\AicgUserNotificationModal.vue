<template>
  <a-modal
    :title="title"
    :width="800"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel"
    cancelText="关闭">
    
    <a-spin :spinning="confirmLoading">
      <a-form-model ref="form" :model="model" :rules="validatorRules" v-bind="layout">
        <a-row>
          <a-col :span="12">
            <a-form-model-item label="用户ID" prop="userId">
              <a-input v-model="model.userId" placeholder="请输入用户ID"></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="通知类型" prop="type">
              <a-select v-model="model.type" placeholder="请选择通知类型">
                <a-select-option :value="1">系统通知</a-select-option>
                <a-select-option :value="2">交易通知</a-select-option>
                <a-select-option :value="3">安全提醒</a-select-option>
                <a-select-option :value="4">营销推送</a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="通知标题" prop="title">
              <a-input v-model="model.title" placeholder="请输入通知标题"></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="通知内容" prop="content">
              <a-textarea v-model="model.content" placeholder="请输入通知内容" :rows="4"></a-textarea>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="优先级" prop="priority">
              <a-select v-model="model.priority" placeholder="请选择优先级">
                <a-select-option :value="1">普通</a-select-option>
                <a-select-option :value="2">重要</a-select-option>
                <a-select-option :value="3">紧急</a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="是否已读" prop="isRead">
              <a-select v-model="model.isRead" placeholder="请选择是否已读">
                <a-select-option :value="0">未读</a-select-option>
                <a-select-option :value="1">已读</a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="跳转链接" prop="actionUrl">
              <a-input v-model="model.actionUrl" placeholder="请输入跳转链接"></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="阅读时间" prop="readTime">
              <a-date-picker 
                v-model="model.readTime" 
                show-time 
                format="YYYY-MM-DD HH:mm:ss"
                placeholder="请选择阅读时间"
                style="width: 100%">
              </a-date-picker>
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </a-spin>
  </a-modal>
</template>

<script>
  import { httpAction } from '@/api/manage'
  import moment from 'moment'

  export default {
    name: 'AicgUserNotificationModal',
    components: {
    },
    data () {
      return {
        layout: {
          labelCol: {
            xs: { span: 24 },
            sm: { span: 5 },
          },
          wrapperCol: {
            xs: { span: 24 },
            sm: { span: 16 },
          },
        },
        title:"操作",
        visible: false,
        model: {},
        confirmLoading: false,
        form: this.$form.createForm(this),
        validatorRules: {
          userId: [
            { required: true, message: '请输入用户ID!' },
          ],
          type: [
            { required: true, message: '请选择通知类型!' },
          ],
          title: [
            { required: true, message: '请输入通知标题!' },
          ],
          content: [
            { required: true, message: '请输入通知内容!' },
          ],
          priority: [
            { required: true, message: '请选择优先级!' },
          ],
        },
        url: {
          add: "/demo/notification/add",
          edit: "/demo/notification/edit",
          queryById: "/demo/notification/queryById"
        }
      }
    },
    methods: {
      add () {
        this.edit({});
      },
      edit (record) {
        this.form.resetFields();
        this.model = Object.assign({}, record);
        this.visible = true;
        this.$nextTick(() => {
          const formData = {
            userId: this.model.userId,
            type: this.model.type,
            title: this.model.title,
            content: this.model.content,
            priority: this.model.priority,
            isRead: this.model.isRead,
            actionUrl: this.model.actionUrl
          }
          this.form.setFieldsValue(formData)
          if(this.model.readTime) {
            this.model.readTime = moment(this.model.readTime)
          }
        })
        if(record.id){
          this.title = "编辑";
        }else{
          this.title = "新增";
          this.model.priority = 1
          this.model.isRead = 0
        }
      },
      close () {
        this.$emit('close');
        this.visible = false;
      },
      handleOk () {
        const that = this;
        // 触发表单验证
        this.$refs.form.validate(valid => {
          if(valid) {
            that.confirmLoading = true;
            let httpurl = '';
            let method = '';
            if(!this.model.id){
              httpurl+=this.url.add;
              method = 'post';
            }else{
              httpurl+=this.url.edit;
              method = 'put';
            }
            let formData = Object.assign(this.model);
            if(formData.readTime) {
              formData.readTime = formData.readTime.format('YYYY-MM-DD HH:mm:ss')
            }
            httpAction(httpurl,formData,method).then((res)=>{
              if(res.success){
                that.$message.success(res.message);
                that.$emit('ok');
              }else{
                that.$message.warning(res.message);
              }
            }).finally(() => {
              that.confirmLoading = false;
            })
          }
        })
      },
      handleCancel () {
        this.close()
      }
    }
  }
</script>
