<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form-model ref="form" :model="model" :rules="validatorRules" slot="detail">
        <a-row>
          <a-col :span="12">
            <a-form-model-item label="是否系列视频" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="isseries">
              <j-dict-select-tag type="list" v-model="model.isseries" dictCode="isTrue" placeholder="请选择是否系列视频" />
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="系列名称" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="seriesname">
              <a-input v-model="model.seriesname" placeholder="请输入系列名称"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="视频文件" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="videofile">
              <j-upload v-model="model.videofile"   ></j-upload>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="讲师" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="teacher">
              <j-dict-select-tag type="list" v-model="model.teacher" dictCode="aigc_video_teacher,teachername,id" placeholder="请选择讲师" />
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="点击量" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="clicknum">
              <a-input-number v-model="model.clicknum" placeholder="请输入点击量" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="标题" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="titile">
              <a-input v-model="model.titile" placeholder="请输入标题"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="设置等级" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="setlevel">
              <j-dict-select-tag type="list" v-model="model.setlevel" dictCode="setLevel" placeholder="请选择设置等级" />
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="课程介绍" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="intro">
              <a-textarea v-model="model.intro" rows="4" placeholder="请输入课程介绍" />
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="课程标签" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="tag">
              <j-multi-select-tag type="checkbox" v-model="model.tag" dictCode="tag" placeholder="请选择课程标签" />
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="上传日期" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="uptime">
              <j-date placeholder="请选择上传日期" v-model="model.uptime"  style="width: 100%" />
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </j-form-container>
  </a-spin>
</template>

<script>

  import { httpAction, getAction } from '@/api/manage'
  import { validateDuplicateValue } from '@/utils/util'

  export default {
    name: 'AigcVideoTutorialForm',
    components: {
    },
    props: {
      //表单禁用
      disabled: {
        type: Boolean,
        default: false,
        required: false
      }
    },
    data () {
      return {
        model:{
         },
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 },
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 },
        },
        confirmLoading: false,
        validatorRules: {
        },
        url: {
          add: "/videotutorial/aigcVideoTutorial/add",
          edit: "/videotutorial/aigcVideoTutorial/edit",
          queryById: "/videotutorial/aigcVideoTutorial/queryById"
        }
      }
    },
    computed: {
      formDisabled(){
        return this.disabled
      },
    },
    created () {
       //备份model原始值
      this.modelDefault = JSON.parse(JSON.stringify(this.model));
    },
    methods: {
      add () {
        this.edit(this.modelDefault);
      },
      edit (record) {
        this.model = Object.assign({}, record);
        this.visible = true;
      },
      submitForm () {
        const that = this;
        // 触发表单验证
        this.$refs.form.validate(valid => {
          if (valid) {
            that.confirmLoading = true;
            let httpurl = '';
            let method = '';
            if(!this.model.id){
              httpurl+=this.url.add;
              method = 'post';
            }else{
              httpurl+=this.url.edit;
               method = 'put';
            }
            httpAction(httpurl,this.model,method).then((res)=>{
              if(res.success){
                that.$message.success(res.message);
                that.$emit('ok');
              }else{
                that.$message.warning(res.message);
              }
            }).finally(() => {
              that.confirmLoading = false;
            })
          }
         
        })
      },
    }
  }
</script>