{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\workflow\\components\\CreatorAgentForm.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\workflow\\components\\CreatorAgentForm.vue", "mtime": 1754512791552}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["import _regeneratorRuntime from \"D:/AigcView_zj/AigcViewFe/\\u667A\\u754CAigc/node_modules/@babel/runtime/regenerator\";\n\nfunction _createForOfIteratorHelper(o, allowArrayLike) { var it; if (typeof Symbol === \"undefined\" || o[Symbol.iterator] == null) { if (Array.isArray(o) || (it = _unsupportedIterableToArray(o)) || allowArrayLike && o && typeof o.length === \"number\") { if (it) o = it; var i = 0; var F = function F() {}; return { s: F, n: function n() { if (i >= o.length) return { done: true }; return { done: false, value: o[i++] }; }, e: function e(_e) { throw _e; }, f: F }; } throw new TypeError(\"Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); } var normalCompletion = true, didErr = false, err; return { s: function s() { it = o[Symbol.iterator](); }, n: function n() { var step = it.next(); normalCompletion = step.done; return step; }, e: function e(_e2) { didErr = true; err = _e2; }, f: function f() { try { if (!normalCompletion && it.return != null) it.return(); } finally { if (didErr) throw err; } } }; }\n\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\n\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) { arr2[i] = arr[i]; } return arr2; }\n\nfunction _typeof(obj) { \"@babel/helpers - typeof\"; if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") { _typeof = function _typeof(obj) { return typeof obj; }; } else { _typeof = function _typeof(obj) { return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }; } return _typeof(obj); }\n\nfunction asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }\n\nfunction _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"next\", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"throw\", err); } _next(undefined); }); }; }\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(Object(source), true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nimport JImageUploadDeferred from '@/components/jeecg/JImageUploadDeferred';\nimport { createAgent, updateAgent } from '@/api/creator-agent';\nimport { createWorkflow, updateWorkflow, getWorkflowList } from '@/api/creator-workflow';\nexport default {\n  name: 'CreatorAgentForm',\n  components: {\n    JImageUploadDeferred: JImageUploadDeferred\n  },\n  props: {\n    visible: {\n      type: Boolean,\n      default: false\n    },\n    loading: {\n      type: Boolean,\n      default: false\n    },\n    agent: {\n      type: Object,\n      default: null\n    },\n    mode: {\n      type: String,\n      default: 'create' // 'create' | 'edit'\n\n    }\n  },\n  data: function data() {\n    return {\n      currentStep: 0,\n      // 当前步骤 0=第一步，1=第二步\n      stepLoading: false,\n      // 步骤操作加载状态\n      createdAgent: null,\n      // 第一步创建的智能体信息\n      formData: {\n        agentId: '',\n        agentName: '',\n        agentDescription: '',\n        agentAvatar: '',\n        experienceLink: '',\n        price: 0\n      },\n      // 🔥 工作流表单数据\n      workflowFormData: {\n        workflowName: '',\n        workflowDescription: '',\n        inputParamsDesc: '',\n        workflowPackage: ''\n      },\n      // 🔥 工作流前端暂存管理（新的数据管理机制）\n      tempWorkflowList: [],\n      // 前端暂存的工作流列表\n      currentWorkflowIndex: -1,\n      // 当前正在编辑的工作流索引 (-1表示新建)\n      // 🔥 工作流验证错误状态管理\n      workflowValidationErrors: {},\n      // 格式：{ workflowId: { errors: ['缺少工作流描述', '缺少压缩包文件'], isValid: false } }\n      workflowFileList: [],\n      // 当前工作流的文件列表（File对象，未上传）\n      // 🔥 工作流文件上传相关（延迟上传机制）\n      workflowFileInfo: null,\n      // 当前选择的文件信息（用于显示）\n      workflowUploading: false,\n      // 上传状态\n      workflowList: [],\n      // 已保存的工作流列表（从后端加载）\n      workflowLoading: false,\n      rules: {\n        agentName: [{\n          required: true,\n          message: '请输入智能体名称',\n          trigger: 'blur'\n        }, {\n          min: 2,\n          max: 100,\n          message: '智能体名称长度在 2 到 100 个字符',\n          trigger: 'blur'\n        }],\n        agentDescription: [{\n          required: true,\n          message: '请输入智能体描述',\n          trigger: 'blur'\n        }, {\n          min: 2,\n          max: 1000,\n          message: '智能体描述长度在 2 到 1000 个字符',\n          trigger: 'blur'\n        }],\n        agentAvatar: [{\n          required: true,\n          message: '请上传智能体头像',\n          trigger: 'change'\n        }],\n        experienceLink: [{\n          type: 'url',\n          message: '请输入正确的URL格式',\n          trigger: 'blur'\n        }],\n        price: [{\n          required: true,\n          message: '请输入价格',\n          trigger: 'blur'\n        }, {\n          type: 'number',\n          min: 0,\n          max: 99999,\n          message: '价格范围在 0 到 99999 元',\n          trigger: 'blur'\n        }]\n      },\n      // 🔥 工作流表单验证规则（所有字段必填）\n      workflowRules: {\n        workflowName: [{\n          required: true,\n          message: '工作流名称为必填项',\n          trigger: 'blur'\n        }, {\n          min: 2,\n          max: 30,\n          message: '工作流名称长度在 2 到 30 个字符',\n          trigger: 'blur'\n        }, {\n          pattern: /^[a-zA-Z0-9\\u4e00-\\u9fa5\\s\\-_]+$/,\n          message: '工作流名称只能包含中英文、数字、空格、横线和下划线',\n          trigger: 'blur'\n        }],\n        workflowDescription: [{\n          required: true,\n          message: '工作流描述为必填项',\n          trigger: 'blur'\n        }, {\n          min: 2,\n          max: 200,\n          message: '工作流描述长度在 2 到 200 个字符',\n          trigger: 'blur'\n        }],\n        inputParamsDesc: [{\n          required: true,\n          message: '请输入参数说明',\n          trigger: 'blur'\n        }, {\n          min: 2,\n          message: '参数说明至少需要2个字符',\n          trigger: 'blur'\n        }, {\n          max: 10000,\n          message: '参数说明长度不能超过10000个字符',\n          trigger: 'blur'\n        }, {\n          pattern: /^[^:：]+[:：](?:\"[^\"]*\"|'[^']*'|[^:：,，]+)(?:[,，][^:：]+[:：](?:\"[^\"]*\"|'[^']*'|[^:：,，]+))*$/,\n          message: '请按照格式填写：参数:值 或 参数:\"值\" 或 参数:\\'值\\'',\n          trigger: 'blur'\n        }],\n        workflowPackage: [// 🔥 移除自动验证，改为手动验证，避免文件选择后仍提示必填\n        ]\n      }\n    };\n  },\n  computed: {\n    modalTitle: function modalTitle() {\n      return this.mode === 'create' ? '新增智能体' : '编辑智能体';\n    },\n    // 🔥 文件上传配置（与后台管理系统一致）\n    uploadAction: function uploadAction() {\n      return \"\".concat(window._CONFIG['domianURL'], \"/sys/common/upload\");\n    },\n    uploadHeaders: function uploadHeaders() {\n      return {\n        'X-Access-Token': this.$ls.get('Access-Token')\n      };\n    },\n    uploadData: function uploadData() {\n      return {\n        'isup': 1,\n        'biz': '' // 工作流文件使用空的biz，后台会自动设置为workflow路径\n\n      };\n    }\n  },\n  watch: {\n    visible: function visible(val) {\n      var _this = this;\n\n      if (val) {\n        this.initForm(); // 🔥 新增和编辑模式都从第一步开始，让用户可以修改基本信息\n\n        this.currentStep = 0; // 🔥 弹窗打开时立即滚动到顶部\n\n        this.scrollToTop();\n\n        if (this.mode === 'edit' && this.agent) {\n          this.createdAgent = _objectSpread({}, this.agent); // 🔥 编辑模式下立即加载工作流数据，确保数据回填\n\n          this.$nextTick(function () {\n            _this.loadWorkflowList(_this.agent.id);\n          });\n        }\n      } else {\n        // 🔥 弹窗关闭时清空所有数据，避免数据污染\n        this.resetForm();\n        this.clearAllWorkflowData();\n      }\n    },\n    agent: {\n      handler: function handler(val) {\n        var _this2 = this;\n\n        if (val && this.visible) {\n          this.initForm();\n\n          if (this.mode === 'edit') {\n            this.createdAgent = _objectSpread({}, val); // 🔥 编辑模式下立即加载工作流数据，确保数据回填\n\n            this.$nextTick(function () {\n              _this2.loadWorkflowList(val.id);\n            });\n          }\n        }\n      },\n      deep: true\n    }\n  },\n  methods: {\n    // 初始化表单\n    initForm: function initForm() {\n      var _this3 = this;\n\n      if (this.mode === 'edit' && this.agent) {\n        this.formData = {\n          agentName: this.agent.agentName || '',\n          agentDescription: this.agent.agentDescription || '',\n          agentAvatar: this.agent.agentAvatar || '',\n          experienceLink: this.agent.experienceLink || '',\n          price: this.agent.price || 0\n        };\n      } else {\n        this.formData = {\n          agentName: '',\n          agentDescription: '',\n          agentAvatar: '',\n          experienceLink: '',\n          price: 0\n        }; // 🔥 新增智能体时清空所有暂存工作流数据，确保每个智能体的创建流程是独立的\n\n        this.clearAllWorkflowData();\n      } // 清除验证状态\n\n\n      this.$nextTick(function () {\n        if (_this3.$refs.form) {\n          _this3.$refs.form.clearValidate();\n        }\n      });\n    },\n    // 重置表单\n    resetForm: function resetForm() {\n      this.currentStep = 0;\n      this.stepLoading = false;\n      this.createdAgent = null;\n      this.formData = {\n        agentId: '',\n        agentName: '',\n        agentDescription: '',\n        agentAvatar: '',\n        experienceLink: '',\n        price: 0\n      };\n      this.workflowList = [];\n\n      if (this.$refs.form) {\n        this.$refs.form.clearValidate();\n      }\n    },\n    // 🔥 第一步：下一步按钮\n    handleNext: function () {\n      var _handleNext = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee2() {\n        var _this4 = this;\n\n        var originalAvatar, hasPendingAvatar;\n        return _regeneratorRuntime.wrap(function _callee2$(_context2) {\n          while (1) {\n            switch (_context2.prev = _context2.next) {\n              case 0:\n                // 🔥 先进行表单验证\n                originalAvatar = this.formData.agentAvatar;\n                hasPendingAvatar = this.$refs.avatarUpload && this.$refs.avatarUpload.hasPendingFiles();\n\n                if (!this.formData.agentAvatar && hasPendingAvatar) {\n                  this.formData.agentAvatar = 'pending_upload';\n                }\n\n                this.$refs.form.validate( /*#__PURE__*/function () {\n                  var _ref = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee(valid) {\n                    var agentData, resultAgent;\n                    return _regeneratorRuntime.wrap(function _callee$(_context) {\n                      while (1) {\n                        switch (_context.prev = _context.next) {\n                          case 0:\n                            _this4.formData.agentAvatar = originalAvatar;\n\n                            if (!valid) {\n                              _context.next = 33;\n                              break;\n                            }\n\n                            _this4.stepLoading = true;\n                            _context.prev = 3;\n                            _context.next = 6;\n                            return _this4.uploadPendingImages();\n\n                          case 6:\n                            // 🔥 提交智能体信息\n                            agentData = {\n                              agentName: _this4.formData.agentName.trim(),\n                              agentDescription: _this4.formData.agentDescription.trim(),\n                              agentAvatar: _this4.processAvatarValue(_this4.formData.agentAvatar),\n                              experienceLink: _this4.formData.experienceLink.trim(),\n                              price: _this4.formData.price\n                            }; // 🔥 根据模式调用不同的API\n\n                            if (!(_this4.mode === 'create')) {\n                              _context.next = 14;\n                              break;\n                            }\n\n                            _context.next = 10;\n                            return _this4.createAgentStep(agentData);\n\n                          case 10:\n                            resultAgent = _context.sent;\n\n                            _this4.$message.success('智能体创建成功，请配置工作流');\n\n                            _context.next = 18;\n                            break;\n\n                          case 14:\n                            _context.next = 16;\n                            return _this4.updateAgentStep(agentData);\n\n                          case 16:\n                            resultAgent = _context.sent;\n\n                            _this4.$message.success('智能体更新成功，请配置工作流');\n\n                          case 18:\n                            _this4.createdAgent = resultAgent; // 进入第二步\n\n                            _this4.currentStep = 1;\n\n                            _this4.loadWorkflowList(resultAgent.id); // 🔥 使用主键ID\n                            // 🔥 滚动到顶部，确保用户看到完整的第二步内容\n\n\n                            _this4.scrollToTop();\n\n                            _context.next = 28;\n                            break;\n\n                          case 24:\n                            _context.prev = 24;\n                            _context.t0 = _context[\"catch\"](3);\n                            console.error('🎯 CreatorAgentForm: 第一步提交失败:', _context.t0);\n\n                            _this4.$message.error('智能体创建失败: ' + (_context.t0.message || '未知错误'));\n\n                          case 28:\n                            _context.prev = 28;\n                            _this4.stepLoading = false;\n                            return _context.finish(28);\n\n                          case 31:\n                            _context.next = 34;\n                            break;\n\n                          case 33:\n                            _this4.$message.error('请检查表单信息');\n\n                          case 34:\n                          case \"end\":\n                            return _context.stop();\n                        }\n                      }\n                    }, _callee, null, [[3, 24, 28, 31]]);\n                  }));\n\n                  return function (_x) {\n                    return _ref.apply(this, arguments);\n                  };\n                }());\n\n              case 4:\n              case \"end\":\n                return _context2.stop();\n            }\n          }\n        }, _callee2, this);\n      }));\n\n      function handleNext() {\n        return _handleNext.apply(this, arguments);\n      }\n\n      return handleNext;\n    }(),\n    // 🔥 第二步：上一步按钮\n    handlePrev: function handlePrev() {\n      console.log('🎯 CreatorAgentForm: 返回第一步');\n      this.currentStep = 0; // 🔥 滚动到顶部，确保用户看到完整的第一步内容\n\n      this.scrollToTop();\n    },\n    // 🔥 第二步：完成按钮（批量保存所有工作流）\n    handleComplete: function () {\n      var _handleComplete = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee3() {\n        var saveResult, validationResult, savedWorkflows;\n        return _regeneratorRuntime.wrap(function _callee3$(_context3) {\n          while (1) {\n            switch (_context3.prev = _context3.next) {\n              case 0:\n                _context3.prev = 0;\n                console.log('🎯 CreatorAgentForm: 完成创建，开始批量处理工作流');\n                this.stepLoading = true; // 🔥 自动暂存当前表单数据（统一处理，避免重复保存）\n\n                console.log('🎯 CreatorAgentForm: 第一步 - 自动暂存当前表单数据');\n                console.log('🎯 CreatorAgentForm: 当前暂存列表长度:', this.tempWorkflowList.length);\n                console.log('🎯 CreatorAgentForm: 当前编辑索引:', this.currentWorkflowIndex);\n                saveResult = this.autoSaveCurrentWorkflow();\n                console.log('🎯 CreatorAgentForm: 自动暂存结果:', saveResult);\n                console.log('🎯 CreatorAgentForm: 暂存后列表长度:', this.tempWorkflowList.length); // 检查是否有暂存的工作流需要保存\n\n                if (!(this.tempWorkflowList.length === 0)) {\n                  _context3.next = 15;\n                  break;\n                }\n\n                console.log('🎯 CreatorAgentForm: 无工作流数据，直接完成');\n                this.$message.success('智能体创建完成！');\n                this.$emit('complete', this.createdAgent);\n                this.handleCancel();\n                return _context3.abrupt(\"return\");\n\n              case 15:\n                console.log(\"\\uD83C\\uDFAF CreatorAgentForm: \\u7B2C\\u4E8C\\u6B65 - \\u5F00\\u59CB\\u9A8C\\u8BC1\\u6240\\u6709 \".concat(this.tempWorkflowList.length, \" \\u4E2A\\u5DE5\\u4F5C\\u6D41\")); // 🔥 第二步：验证所有工作流的完整性（包括刚暂存的）\n\n                validationResult = this.validateAllWorkflows();\n\n                if (validationResult.isValid) {\n                  _context3.next = 22;\n                  break;\n                }\n\n                console.error('🎯 CreatorAgentForm: 工作流验证失败:', validationResult.errors); // 🔥 第三步：智能错误处理 - 回填第一个有错误的工作流到表单\n\n                this.handleValidationErrors(validationResult);\n                this.stepLoading = false;\n                return _context3.abrupt(\"return\");\n\n              case 22:\n                console.log(\"\\uD83C\\uDFAF CreatorAgentForm: \\u7B2C\\u4E09\\u6B65 - \\u9A8C\\u8BC1\\u901A\\u8FC7\\uFF0C\\u5F00\\u59CB\\u6279\\u91CF\\u4FDD\\u5B58 \".concat(this.tempWorkflowList.length, \" \\u4E2A\\u5DE5\\u4F5C\\u6D41\")); // 批量上传文件和保存工作流\n\n                _context3.next = 25;\n                return this.batchSaveWorkflows();\n\n              case 25:\n                savedWorkflows = _context3.sent;\n                this.$message.success(\"\\u667A\\u80FD\\u4F53\\u548C \".concat(savedWorkflows.length, \" \\u4E2A\\u5DE5\\u4F5C\\u6D41\\u521B\\u5EFA\\u5B8C\\u6210\\uFF01\")); // 🔥 进入第三步成功页面，而不是直接关闭弹窗\n\n                this.currentStep = 2;\n                this.scrollToTop();\n                console.log('🎯 CreatorAgentForm: 进入第三步成功页面');\n                _context3.next = 36;\n                break;\n\n              case 32:\n                _context3.prev = 32;\n                _context3.t0 = _context3[\"catch\"](0);\n                console.error('🎯 CreatorAgentForm: 完成创建失败:', _context3.t0);\n                this.$message.error('工作流保存失败: ' + (_context3.t0.message || '未知错误'));\n\n              case 36:\n                _context3.prev = 36;\n                this.stepLoading = false;\n                return _context3.finish(36);\n\n              case 39:\n              case \"end\":\n                return _context3.stop();\n            }\n          }\n        }, _callee3, this, [[0, 32, 36, 39]]);\n      }));\n\n      function handleComplete() {\n        return _handleComplete.apply(this, arguments);\n      }\n\n      return handleComplete;\n    }(),\n    // 🔥 关闭弹窗（第三步完成后）\n    handleCloseModal: function handleCloseModal() {\n      console.log('🎯 CreatorAgentForm: 用户点击关闭按钮');\n      this.$emit('complete', this.createdAgent);\n      this.handleCancel();\n    },\n    // 🔥 批量保存所有暂存的工作流\n    batchSaveWorkflows: function () {\n      var _batchSaveWorkflows = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee4() {\n        var savedWorkflows, totalCount, i, workflow, originalWorkflow, hasTextChanges, hasFileChanges, hasChanges, fileUrl, updateData, response, updatedWorkflow, _fileUrl, workflowData, _response;\n\n        return _regeneratorRuntime.wrap(function _callee4$(_context4) {\n          while (1) {\n            switch (_context4.prev = _context4.next) {\n              case 0:\n                savedWorkflows = [];\n                totalCount = this.tempWorkflowList.length;\n                i = 0;\n\n              case 3:\n                if (!(i < this.tempWorkflowList.length)) {\n                  _context4.next = 69;\n                  break;\n                }\n\n                workflow = this.tempWorkflowList[i];\n                console.log(\"\\uD83C\\uDFAF CreatorAgentForm: \\u4FDD\\u5B58\\u5DE5\\u4F5C\\u6D41 \".concat(i + 1, \"/\").concat(totalCount, \": \").concat(workflow.workflowName));\n                _context4.prev = 6;\n\n                if (!(workflow.status === 'saved')) {\n                  _context4.next = 44;\n                  break;\n                }\n\n                // 已保存的工作流：检查是否有实际修改（包括文件修改）\n                originalWorkflow = workflow.originalWorkflow;\n                hasTextChanges = originalWorkflow && (originalWorkflow.workflowName !== workflow.workflowName || originalWorkflow.workflowDescription !== workflow.workflowDescription || originalWorkflow.inputParamsDesc !== workflow.inputParamsDesc); // 🔥 检查是否重新选择了文件\n\n                hasFileChanges = workflow.workflowFile !== null;\n                hasChanges = hasTextChanges || hasFileChanges;\n\n                if (!hasChanges) {\n                  _context4.next = 40;\n                  break;\n                }\n\n                console.log(\"\\uD83C\\uDFAF CreatorAgentForm: \\u5DF2\\u4FDD\\u5B58\\u5DE5\\u4F5C\\u6D41\\u6709\\u4FEE\\u6539\\uFF0C\\u5F00\\u59CB\\u66F4\\u65B0: \".concat(workflow.workflowName));\n                console.log(\"\\uD83C\\uDFAF CreatorAgentForm: \\u6587\\u672C\\u4FEE\\u6539: \".concat(hasTextChanges, \", \\u6587\\u4EF6\\u4FEE\\u6539: \").concat(hasFileChanges));\n                fileUrl = originalWorkflow.workflowPackage; // 默认使用原有文件路径\n                // 🔥 如果用户重新选择了文件，先上传新文件\n\n                if (!hasFileChanges) {\n                  _context4.next = 22;\n                  break;\n                }\n\n                console.log(\"\\uD83C\\uDFAF CreatorAgentForm: \\u68C0\\u6D4B\\u5230\\u6587\\u4EF6\\u53D8\\u66F4\\uFF0C\\u4E0A\\u4F20\\u65B0\\u6587\\u4EF6: \".concat(workflow.workflowFile.name));\n                _context4.next = 20;\n                return this.uploadWorkflowFile(workflow.workflowFile, workflow.workflowName);\n\n              case 20:\n                fileUrl = _context4.sent;\n                console.log(\"\\uD83C\\uDFAF CreatorAgentForm: \\u65B0\\u6587\\u4EF6\\u4E0A\\u4F20\\u6210\\u529F: \".concat(fileUrl));\n\n              case 22:\n                // 🔥 调用更新工作流API\n                updateData = {\n                  agentId: this.createdAgent.id,\n                  // 🔥 后端必填字段\n                  workflowName: workflow.workflowName,\n                  workflowDescription: workflow.workflowDescription,\n                  inputParamsDesc: workflow.inputParamsDesc,\n                  workflowPackage: fileUrl // 🔥 使用新文件路径或原有路径\n\n                };\n                console.log('🎯 CreatorAgentForm: 调用工作流更新API:', updateData);\n                console.log('🎯 CreatorAgentForm: 更新前的原始数据:', originalWorkflow);\n                _context4.next = 27;\n                return updateWorkflow(originalWorkflow.id, updateData);\n\n              case 27:\n                response = _context4.sent;\n                console.log('🎯 CreatorAgentForm: 更新API响应:', response);\n\n                if (!response.success) {\n                  _context4.next = 37;\n                  break;\n                }\n\n                updatedWorkflow = response.result || _objectSpread(_objectSpread({}, originalWorkflow), updateData);\n                savedWorkflows.push(updatedWorkflow); // 🔥 同步更新暂存列表中的数据，确保界面显示最新状态\n\n                this.tempWorkflowList[i] = _objectSpread(_objectSpread({}, workflow), {}, {\n                  // 🔥 确保所有字段都同步到最新状态\n                  workflowName: updatedWorkflow.workflowName || workflow.workflowName,\n                  workflowDescription: updatedWorkflow.workflowDescription || workflow.workflowDescription,\n                  inputParamsDesc: updatedWorkflow.inputParamsDesc || workflow.inputParamsDesc,\n                  originalWorkflow: updatedWorkflow // 更新原始数据引用\n\n                });\n                console.log('🎯 CreatorAgentForm: 暂存列表已同步更新:', this.tempWorkflowList[i]);\n                console.log(\"\\uD83C\\uDFAF CreatorAgentForm: \\u5DE5\\u4F5C\\u6D41 \".concat(workflow.workflowName, \" \\u66F4\\u65B0\\u6210\\u529F\"));\n                _context4.next = 38;\n                break;\n\n              case 37:\n                throw new Error(response.message || '工作流更新API调用失败');\n\n              case 38:\n                _context4.next = 42;\n                break;\n\n              case 40:\n                console.log(\"\\uD83C\\uDFAF CreatorAgentForm: \\u5DF2\\u4FDD\\u5B58\\u5DE5\\u4F5C\\u6D41\\u65E0\\u4FEE\\u6539\\uFF0C\\u8DF3\\u8FC7\\u4FDD\\u5B58: \".concat(workflow.workflowName));\n                savedWorkflows.push(originalWorkflow); // 使用原始数据\n\n              case 42:\n                _context4.next = 59;\n                break;\n\n              case 44:\n                // 新增的工作流：需要上传文件并创建\n                console.log(\"\\uD83C\\uDFAF CreatorAgentForm: \\u65B0\\u589E\\u5DE5\\u4F5C\\u6D41\\uFF0C\\u5F00\\u59CB\\u4E0A\\u4F20\\u6587\\u4EF6: \".concat(workflow.workflowFile ? workflow.workflowFile.name : 'null'));\n                _context4.next = 47;\n                return this.uploadWorkflowFile(workflow.workflowFile, workflow.workflowName);\n\n              case 47:\n                _fileUrl = _context4.sent;\n                // 保存工作流数据\n                workflowData = {\n                  agentId: this.createdAgent.id,\n                  workflowName: workflow.workflowName,\n                  workflowDescription: workflow.workflowDescription,\n                  inputParamsDesc: workflow.inputParamsDesc,\n                  // 🔥 前端验证确保不为空\n                  workflowPackage: _fileUrl\n                }; // 🔥 调用工作流创建API\n\n                console.log('🎯 CreatorAgentForm: 调用工作流创建API:', workflowData);\n                _context4.next = 52;\n                return createWorkflow(workflowData);\n\n              case 52:\n                _response = _context4.sent;\n\n                if (!_response.success) {\n                  _context4.next = 58;\n                  break;\n                }\n\n                savedWorkflows.push(_response.result);\n                console.log(\"\\uD83C\\uDFAF CreatorAgentForm: \\u5DE5\\u4F5C\\u6D41 \".concat(workflow.workflowName, \" API\\u8C03\\u7528\\u6210\\u529F\"));\n                _context4.next = 59;\n                break;\n\n              case 58:\n                throw new Error(_response.message || '工作流创建API调用失败');\n\n              case 59:\n                console.log(\"\\uD83C\\uDFAF CreatorAgentForm: \\u5DE5\\u4F5C\\u6D41 \".concat(workflow.workflowName, \" \\u4FDD\\u5B58\\u6210\\u529F\"));\n                _context4.next = 66;\n                break;\n\n              case 62:\n                _context4.prev = 62;\n                _context4.t0 = _context4[\"catch\"](6);\n                console.error(\"\\uD83C\\uDFAF CreatorAgentForm: \\u5DE5\\u4F5C\\u6D41 \".concat(workflow.workflowName, \" \\u4FDD\\u5B58\\u5931\\u8D25:\"), _context4.t0);\n                throw new Error(\"\\u5DE5\\u4F5C\\u6D41\\\"\".concat(workflow.workflowName, \"\\\"\\u4FDD\\u5B58\\u5931\\u8D25: \").concat(_context4.t0.message));\n\n              case 66:\n                i++;\n                _context4.next = 3;\n                break;\n\n              case 69:\n                return _context4.abrupt(\"return\", savedWorkflows);\n\n              case 70:\n              case \"end\":\n                return _context4.stop();\n            }\n          }\n        }, _callee4, this, [[6, 62]]);\n      }));\n\n      function batchSaveWorkflows() {\n        return _batchSaveWorkflows.apply(this, arguments);\n      }\n\n      return batchSaveWorkflows;\n    }(),\n    // 🔥 上传单个工作流文件\n    uploadWorkflowFile: function () {\n      var _uploadWorkflowFile = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee5(file, workflowName) {\n        var _this5 = this;\n\n        return _regeneratorRuntime.wrap(function _callee5$(_context5) {\n          while (1) {\n            switch (_context5.prev = _context5.next) {\n              case 0:\n                return _context5.abrupt(\"return\", new Promise(function (resolve, reject) {\n                  var formData = new FormData();\n                  formData.append('file', file);\n                  formData.append('isup', '1');\n                  formData.append('biz', ''); // 工作流文件使用空的biz\n                  // 生成重命名后的文件名\n\n                  var renamedFileName = _this5.generateWorkflowFileName(file.name, workflowName); // 使用fetch进行文件上传\n\n\n                  fetch(_this5.uploadAction, {\n                    method: 'POST',\n                    headers: _this5.uploadHeaders,\n                    body: formData\n                  }).then(function (response) {\n                    return response.json();\n                  }).then(function (result) {\n                    if (result.success) {\n                      console.log(\"\\uD83C\\uDFAF CreatorAgentForm: \\u6587\\u4EF6\\u4E0A\\u4F20\\u6210\\u529F: \".concat(file.name, \" -> \").concat(result.message));\n                      resolve(result.message);\n                    } else {\n                      reject(new Error(result.message || '文件上传失败'));\n                    }\n                  }).catch(function (error) {\n                    console.error(\"\\uD83C\\uDFAF CreatorAgentForm: \\u6587\\u4EF6\\u4E0A\\u4F20\\u5931\\u8D25:\", error);\n                    reject(error);\n                  });\n                }));\n\n              case 1:\n              case \"end\":\n                return _context5.stop();\n            }\n          }\n        }, _callee5);\n      }));\n\n      function uploadWorkflowFile(_x2, _x3) {\n        return _uploadWorkflowFile.apply(this, arguments);\n      }\n\n      return uploadWorkflowFile;\n    }(),\n    // 🔥 创建智能体（第一步）\n    createAgentStep: function () {\n      var _createAgentStep = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee6(agentData) {\n        var response;\n        return _regeneratorRuntime.wrap(function _callee6$(_context6) {\n          while (1) {\n            switch (_context6.prev = _context6.next) {\n              case 0:\n                _context6.prev = 0;\n                console.log('🎯 CreatorAgentForm: 调用创建智能体API...', agentData);\n                _context6.next = 4;\n                return createAgent(agentData);\n\n              case 4:\n                response = _context6.sent;\n\n                if (!response.success) {\n                  _context6.next = 11;\n                  break;\n                }\n\n                console.log('🎯 CreatorAgentForm: 智能体创建成功', response.result); // 🔥 确认删除被替换的原始头像文件\n\n                this.confirmDeleteOriginalFiles();\n                return _context6.abrupt(\"return\", response.result);\n\n              case 11:\n                throw new Error(response.message || '创建智能体失败');\n\n              case 12:\n                _context6.next = 18;\n                break;\n\n              case 14:\n                _context6.prev = 14;\n                _context6.t0 = _context6[\"catch\"](0);\n                console.error('🎯 CreatorAgentForm: 创建智能体失败:', _context6.t0);\n                throw _context6.t0;\n\n              case 18:\n              case \"end\":\n                return _context6.stop();\n            }\n          }\n        }, _callee6, this, [[0, 14]]);\n      }));\n\n      function createAgentStep(_x4) {\n        return _createAgentStep.apply(this, arguments);\n      }\n\n      return createAgentStep;\n    }(),\n    // 🔥 更新智能体（编辑模式第一步）\n    updateAgentStep: function () {\n      var _updateAgentStep = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee7(agentData) {\n        var agentId, response;\n        return _regeneratorRuntime.wrap(function _callee7$(_context7) {\n          while (1) {\n            switch (_context7.prev = _context7.next) {\n              case 0:\n                _context7.prev = 0;\n                console.log('🎯 CreatorAgentForm: 调用更新智能体API...', agentData); // 获取智能体ID\n\n                agentId = this.agent.id || this.agent.agentId;\n\n                if (agentId) {\n                  _context7.next = 5;\n                  break;\n                }\n\n                throw new Error('智能体ID不存在');\n\n              case 5:\n                _context7.next = 7;\n                return updateAgent(agentId, agentData);\n\n              case 7:\n                response = _context7.sent;\n\n                if (!response.success) {\n                  _context7.next = 14;\n                  break;\n                }\n\n                console.log('🎯 CreatorAgentForm: 智能体更新成功', response.result); // 🔥 确认删除被替换的原始头像文件\n\n                this.confirmDeleteOriginalFiles();\n                return _context7.abrupt(\"return\", response.result);\n\n              case 14:\n                throw new Error(response.message || '更新智能体失败');\n\n              case 15:\n                _context7.next = 21;\n                break;\n\n              case 17:\n                _context7.prev = 17;\n                _context7.t0 = _context7[\"catch\"](0);\n                console.error('🎯 CreatorAgentForm: 更新智能体失败:', _context7.t0);\n                throw _context7.t0;\n\n              case 21:\n              case \"end\":\n                return _context7.stop();\n            }\n          }\n        }, _callee7, this, [[0, 17]]);\n      }));\n\n      function updateAgentStep(_x5) {\n        return _updateAgentStep.apply(this, arguments);\n      }\n\n      return updateAgentStep;\n    }(),\n    // 🔥 处理头像值，确保返回字符串格式\n    processAvatarValue: function processAvatarValue(avatarValue) {\n      console.log('🎯 CreatorAgentForm: 处理头像值 - 原始值:', avatarValue, _typeof(avatarValue)); // 如果是空值，返回空字符串\n\n      if (!avatarValue) {\n        return '';\n      } // 如果是数组，取第一个元素\n\n\n      if (Array.isArray(avatarValue)) {\n        console.log('🎯 CreatorAgentForm: 头像值是数组，取第一个元素:', avatarValue[0]);\n        return this.processAvatarValue(avatarValue[0]); // 递归处理\n      } // 如果是对象，尝试获取url字段\n\n\n      if (_typeof(avatarValue) === 'object' && avatarValue.url) {\n        console.log('🎯 CreatorAgentForm: 头像值是对象，取url字段:', avatarValue.url);\n        return this.processAvatarValue(avatarValue.url); // 递归处理\n      } // 如果是字符串，处理URL\n\n\n      if (typeof avatarValue === 'string') {\n        return this.extractRelativePath(avatarValue);\n      } // 其他情况，转换为字符串\n\n\n      console.log('🎯 CreatorAgentForm: 头像值转换为字符串:', String(avatarValue));\n      return String(avatarValue);\n    },\n    // 🔥 提取相对路径，避免TOS URL双重包装\n    extractRelativePath: function extractRelativePath(url) {\n      console.log('🎯 CreatorAgentForm: 提取相对路径 - 输入URL:', url); // 如果是完整的TOS URL，提取相对路径\n\n      if (url.includes('aigcview-tos.tos-cn-shanghai.volces.com/')) {\n        // 提取 uploads/ 开头的路径\n        var match = url.match(/uploads\\/[^?]+/);\n\n        if (match) {\n          var relativePath = match[0];\n          console.log('🎯 CreatorAgentForm: 从TOS URL提取相对路径:', relativePath);\n          return relativePath;\n        }\n      } // 如果是CDN URL，提取相对路径\n\n\n      if (url.includes('cdn.aigcview.com/')) {\n        var _match = url.match(/uploads\\/[^?]+/);\n\n        if (_match) {\n          var _relativePath = _match[0];\n          console.log('🎯 CreatorAgentForm: 从CDN URL提取相对路径:', _relativePath);\n          return _relativePath;\n        }\n      } // 如果已经是相对路径（以uploads/开头），直接返回\n\n\n      if (url.startsWith('uploads/')) {\n        console.log('🎯 CreatorAgentForm: 已经是相对路径:', url);\n        return url;\n      } // 其他情况，直接返回原值\n\n\n      console.log('🎯 CreatorAgentForm: 无法识别的URL格式，返回原值:', url);\n      return url;\n    },\n    // 🔥 获取完整的头像URL（用于显示）\n    getFullAvatarUrl: function getFullAvatarUrl(avatarPath) {\n      if (!avatarPath) {\n        return '';\n      } // 如果已经是完整URL，直接返回\n\n\n      if (avatarPath.startsWith('http')) {\n        return avatarPath;\n      } // 如果是相对路径，转换为CDN URL\n\n\n      if (avatarPath.startsWith('uploads/')) {\n        var _cdnUrl = \"https://cdn.aigcview.com/\".concat(avatarPath);\n\n        console.log('🎯 CreatorAgentForm: 转换头像URL:', avatarPath, '->', _cdnUrl);\n        return _cdnUrl;\n      } // 其他情况，尝试拼接CDN前缀\n\n\n      var cdnUrl = \"https://cdn.aigcview.com/\".concat(avatarPath);\n      console.log('🎯 CreatorAgentForm: 拼接头像URL:', avatarPath, '->', cdnUrl);\n      return cdnUrl;\n    },\n    // 提交表单（保留原方法，但在分步模式下不使用）\n    handleSubmit: function () {\n      var _handleSubmit = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee9() {\n        var _this6 = this;\n\n        var originalAvatar, hasPendingAvatar;\n        return _regeneratorRuntime.wrap(function _callee9$(_context9) {\n          while (1) {\n            switch (_context9.prev = _context9.next) {\n              case 0:\n                console.log('🎯 CreatorAgentForm: 开始提交智能体表单...'); // 🔥 先进行表单验证，验证通过后再上传头像\n                // 🔥 验证前检查头像：如果当前值为空但有待上传文件，则临时设置一个值通过验证\n\n                originalAvatar = this.formData.agentAvatar;\n                hasPendingAvatar = this.$refs.avatarUpload && this.$refs.avatarUpload.hasPendingFiles();\n\n                if (!this.formData.agentAvatar && hasPendingAvatar) {\n                  console.log('🎯 CreatorAgentForm: 检测到待上传头像，临时设置头像值以通过验证');\n                  this.formData.agentAvatar = 'pending_upload'; // 临时值\n                } // 表单验证\n\n\n                this.$refs.form.validate( /*#__PURE__*/function () {\n                  var _ref2 = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee8(valid) {\n                    var submitData;\n                    return _regeneratorRuntime.wrap(function _callee8$(_context8) {\n                      while (1) {\n                        switch (_context8.prev = _context8.next) {\n                          case 0:\n                            // 恢复原始值\n                            _this6.formData.agentAvatar = originalAvatar;\n\n                            if (!valid) {\n                              _context8.next = 17;\n                              break;\n                            }\n\n                            console.log('🎯 CreatorAgentForm: 表单验证通过，开始上传头像...');\n                            _context8.prev = 3;\n                            _context8.next = 6;\n                            return _this6.uploadPendingImages();\n\n                          case 6:\n                            // 创建提交数据，只包含需要的字段\n                            submitData = {\n                              agentName: _this6.formData.agentName.trim(),\n                              agentDescription: _this6.formData.agentDescription.trim(),\n                              agentAvatar: _this6.processAvatarValue(_this6.formData.agentAvatar),\n                              experienceLink: _this6.formData.experienceLink.trim(),\n                              price: _this6.formData.price\n                            };\n                            console.log('🎯 CreatorAgentForm: 头像上传成功，提交数据:', submitData);\n\n                            _this6.$emit('submit', submitData);\n\n                            _context8.next = 15;\n                            break;\n\n                          case 11:\n                            _context8.prev = 11;\n                            _context8.t0 = _context8[\"catch\"](3);\n                            console.error('🎯 CreatorAgentForm: 头像上传失败:', _context8.t0);\n\n                            _this6.$message.error('头像上传失败: ' + (_context8.t0.message || '未知错误'));\n\n                          case 15:\n                            _context8.next = 19;\n                            break;\n\n                          case 17:\n                            console.log('🎯 CreatorAgentForm: 表单验证失败，不上传头像');\n\n                            _this6.$message.error('请检查表单信息');\n\n                          case 19:\n                          case \"end\":\n                            return _context8.stop();\n                        }\n                      }\n                    }, _callee8, null, [[3, 11]]);\n                  }));\n\n                  return function (_x6) {\n                    return _ref2.apply(this, arguments);\n                  };\n                }());\n\n              case 5:\n              case \"end\":\n                return _context9.stop();\n            }\n          }\n        }, _callee9, this);\n      }));\n\n      function handleSubmit() {\n        return _handleSubmit.apply(this, arguments);\n      }\n\n      return handleSubmit;\n    }(),\n    // 取消\n    handleCancel: function handleCancel() {\n      console.log('🎯 CreatorAgentForm: 用户取消，执行清理操作...'); // 🔥 回滚头像变更\n\n      this.rollbackChanges(); // 🔥 清空所有工作流数据，避免数据污染\n\n      this.clearAllWorkflowData(); // 🔥 重置表单数据\n\n      this.resetForm();\n      console.log('🎯 CreatorAgentForm: 所有数据已清理完成');\n      this.$emit('close');\n    },\n    // 🔥 上传待处理的图片（与后台管理系统逻辑一致）\n    uploadPendingImages: function () {\n      var _uploadPendingImages = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee10() {\n        var finalUrl;\n        return _regeneratorRuntime.wrap(function _callee10$(_context10) {\n          while (1) {\n            switch (_context10.prev = _context10.next) {\n              case 0:\n                console.log('🎯 CreatorAgentForm: 开始上传待处理的头像...');\n\n                if (!(this.$refs.avatarUpload && this.$refs.avatarUpload.hasPendingFiles())) {\n                  _context10.next = 18;\n                  break;\n                }\n\n                console.log('🎯 CreatorAgentForm: 检测到待上传头像，开始上传...');\n                _context10.prev = 3;\n                _context10.next = 6;\n                return this.$refs.avatarUpload.performUpload();\n\n              case 6:\n                finalUrl = _context10.sent;\n                console.log('🎯 CreatorAgentForm: 头像上传成功，URL:', finalUrl); // 更新formData中的头像URL\n\n                this.formData.agentAvatar = finalUrl;\n                return _context10.abrupt(\"return\", finalUrl);\n\n              case 12:\n                _context10.prev = 12;\n                _context10.t0 = _context10[\"catch\"](3);\n                console.error('🎯 CreatorAgentForm: 头像上传失败:', _context10.t0);\n                throw new Error('头像上传失败: ' + (_context10.t0.message || '未知错误'));\n\n              case 16:\n                _context10.next = 20;\n                break;\n\n              case 18:\n                console.log('🎯 CreatorAgentForm: 没有待上传的头像');\n                return _context10.abrupt(\"return\", this.formData.agentAvatar);\n\n              case 20:\n              case \"end\":\n                return _context10.stop();\n            }\n          }\n        }, _callee10, this, [[3, 12]]);\n      }));\n\n      function uploadPendingImages() {\n        return _uploadPendingImages.apply(this, arguments);\n      }\n\n      return uploadPendingImages;\n    }(),\n    // 🔥 确认删除原始文件（与后台管理系统逻辑一致）\n    confirmDeleteOriginalFiles: function confirmDeleteOriginalFiles() {\n      console.log('🎯 CreatorAgentForm: 确认删除被替换的原始头像文件...');\n\n      if (this.$refs.avatarUpload && this.$refs.avatarUpload.confirmDeleteOriginalFiles) {\n        this.$refs.avatarUpload.confirmDeleteOriginalFiles();\n        console.log('🎯 CreatorAgentForm: 原始头像文件删除确认完成');\n      }\n    },\n    // 🔥 回滚变更（与后台管理系统逻辑一致）\n    rollbackChanges: function rollbackChanges() {\n      console.log('🎯 CreatorAgentForm: 回滚头像变更...');\n\n      if (this.$refs.avatarUpload && this.$refs.avatarUpload.rollbackChanges) {\n        this.$refs.avatarUpload.rollbackChanges();\n        console.log('🎯 CreatorAgentForm: 头像变更回滚完成');\n      }\n    },\n    // 🔥 加载工作流列表\n    loadWorkflowList: function () {\n      var _loadWorkflowList = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee11(agentId) {\n        var response;\n        return _regeneratorRuntime.wrap(function _callee11$(_context11) {\n          while (1) {\n            switch (_context11.prev = _context11.next) {\n              case 0:\n                if (agentId) {\n                  _context11.next = 2;\n                  break;\n                }\n\n                return _context11.abrupt(\"return\");\n\n              case 2:\n                this.workflowLoading = true;\n                _context11.prev = 3;\n                console.log('🎯 CreatorAgentForm: 加载工作流列表...', agentId); // 🔥 调用获取工作流列表的API\n\n                _context11.next = 7;\n                return getWorkflowList(agentId);\n\n              case 7:\n                response = _context11.sent;\n\n                if (response.success) {\n                  this.workflowList = response.result || [];\n                  console.log('🎯 CreatorAgentForm: API返回工作流数据:', this.workflowList); // 🔥 编辑模式下：将现有工作流数据回填到暂存区域\n\n                  if (this.mode === 'edit' && this.workflowList.length > 0) {\n                    this.convertWorkflowsToTempList(this.workflowList);\n                    console.log('🎯 CreatorAgentForm: 编辑模式 - 工作流数据已回填到暂存区域');\n                  } else if (this.mode === 'edit') {\n                    console.log('🎯 CreatorAgentForm: 编辑模式 - 该智能体暂无工作流数据');\n                  }\n                } else {\n                  console.error('🎯 CreatorAgentForm: API返回失败:', response.message);\n                  this.$message.error(response.message || '获取工作流列表失败');\n                  this.workflowList = [];\n                }\n\n                console.log('🎯 CreatorAgentForm: 工作流列表加载完成，共', this.workflowList.length, '个工作流');\n                _context11.next = 17;\n                break;\n\n              case 12:\n                _context11.prev = 12;\n                _context11.t0 = _context11[\"catch\"](3);\n                console.error('🎯 CreatorAgentForm: 加载工作流列表失败:', _context11.t0);\n                this.$message.error('加载工作流列表失败: ' + (_context11.t0.message || '网络错误'));\n                this.workflowList = [];\n\n              case 17:\n                _context11.prev = 17;\n                this.workflowLoading = false;\n                return _context11.finish(17);\n\n              case 20:\n              case \"end\":\n                return _context11.stop();\n            }\n          }\n        }, _callee11, this, [[3, 12, 17, 20]]);\n      }));\n\n      function loadWorkflowList(_x7) {\n        return _loadWorkflowList.apply(this, arguments);\n      }\n\n      return loadWorkflowList;\n    }(),\n    // 🔥 将现有工作流数据转换为暂存格式（编辑模式回填）\n    convertWorkflowsToTempList: function convertWorkflowsToTempList(workflows) {\n      var _this7 = this;\n\n      console.log('🎯 CreatorAgentForm: 开始转换工作流数据到暂存区域');\n      console.log('🎯 CreatorAgentForm: 原始工作流数据:', workflows); // 清空现有暂存数据\n\n      this.tempWorkflowList = []; // 转换每个工作流为暂存格式\n\n      workflows.forEach(function (workflow, index) {\n        console.log(\"\\uD83C\\uDFAF CreatorAgentForm: \\u5904\\u7406\\u5DE5\\u4F5C\\u6D41 \".concat(index + 1, \":\"), {\n          id: workflow.id,\n          workflowName: workflow.workflowName,\n          workflowDescription: workflow.workflowDescription,\n          inputParamsDesc: workflow.inputParamsDesc,\n          workflowPackage: workflow.workflowPackage,\n          createTime: workflow.createTime\n        }); // 🔥 处理已保存工作流的文件信息\n\n        var packageFileName = workflow.workflowPackage || '';\n        var displayFileName = packageFileName ? packageFileName.includes('/') ? packageFileName.split('/').pop() : packageFileName : '已上传文件';\n        var tempWorkflow = {\n          id: workflow.id || Date.now() + index,\n          workflowName: workflow.workflowName || '',\n          workflowDescription: workflow.workflowDescription || '',\n          inputParamsDesc: workflow.inputParamsDesc || '',\n          // 🔥 确保包含输入参数说明\n          workflowFile: null,\n          // 已保存的工作流没有文件对象\n          fileName: displayFileName,\n          // 🔥 显示文件名而不是完整路径\n          fileSize: 0,\n          // 已保存的工作流无法获取文件大小\n          workflowPackage: workflow.workflowPackage || '',\n          // 🔥 保存完整的包路径\n          status: 'saved',\n          // 标记为已保存状态\n          createTime: new Date(workflow.createTime || Date.now()),\n          originalWorkflow: workflow // 保存原始工作流数据引用\n\n        };\n\n        _this7.tempWorkflowList.push(tempWorkflow);\n\n        console.log(\"\\uD83C\\uDFAF CreatorAgentForm: \\u5DE5\\u4F5C\\u6D41 \\\"\".concat(workflow.workflowName, \"\\\" \\u5DF2\\u8F6C\\u6362\\u4E3A\\u6682\\u5B58\\u683C\\u5F0F:\"), tempWorkflow);\n      });\n      console.log(\"\\uD83C\\uDFAF CreatorAgentForm: \\u5171\\u8F6C\\u6362 \".concat(workflows.length, \" \\u4E2A\\u5DE5\\u4F5C\\u6D41\\u5230\\u6682\\u5B58\\u533A\\u57DF\"));\n      console.log('🎯 CreatorAgentForm: 最终暂存列表:', this.tempWorkflowList);\n    },\n    // 🔥 工作流文件上传前安全检查\n    beforeWorkflowUpload: function beforeWorkflowUpload(file) {\n      console.log('🎯 CreatorAgentForm: 工作流文件安全检查开始:', file.name); // 🛡️ 1. 文件名安全检查\n\n      if (!this.isSecureFileName(file.name)) {\n        this.$message.error('文件名包含不安全字符，请重命名后重试!');\n        return false;\n      } // 🛡️ 2. 严格检查文件扩展名（只允许.zip）\n\n\n      var fileName = file.name.toLowerCase();\n\n      if (!fileName.endsWith('.zip')) {\n        this.$message.error('只能上传 .zip 格式的文件!');\n        return false;\n      } // 🛡️ 3. 严格检查MIME类型\n\n\n      var allowedMimeTypes = ['application/zip', 'application/x-zip-compressed', 'application/x-zip'];\n\n      if (!allowedMimeTypes.includes(file.type)) {\n        this.$message.error('文件类型不正确，只允许上传ZIP压缩包!');\n        return false;\n      } // 🛡️ 4. 检查文件大小 (5MB)\n\n\n      var maxSize = 5 * 1024 * 1024; // 5MB\n\n      if (file.size > maxSize) {\n        this.$message.error('文件大小不能超过 5MB!');\n        return false;\n      } // 🛡️ 5. 检查文件大小不能为0\n\n\n      if (file.size === 0) {\n        this.$message.error('文件不能为空!');\n        return false;\n      }\n\n      console.log('🎯 CreatorAgentForm: 工作流文件安全检查通过');\n      return false; // 阻止默认上传，使用延迟上传机制\n    },\n    // 🛡️ 文件名安全检查\n    isSecureFileName: function isSecureFileName(fileName) {\n      // 检查危险字符\n      var dangerousChars = /[<>:\"|?*\\x00-\\x1f]/;\n\n      if (dangerousChars.test(fileName)) {\n        return false;\n      } // 检查路径遍历\n\n\n      if (fileName.includes('..') || fileName.includes('./') || fileName.includes('.\\\\')) {\n        return false;\n      } // 检查保留名称（Windows）\n\n\n      var reservedNames = /^(CON|PRN|AUX|NUL|COM[1-9]|LPT[1-9])(\\.|$)/i;\n\n      if (reservedNames.test(fileName)) {\n        return false;\n      } // 检查文件名长度\n\n\n      if (fileName.length > 255) {\n        return false;\n      }\n\n      return true;\n    },\n    // 🛡️ 验证工作流数据安全性\n    validateWorkflowData: function validateWorkflowData() {\n      var _this$workflowFormDat = this.workflowFormData,\n          workflowName = _this$workflowFormDat.workflowName,\n          workflowDescription = _this$workflowFormDat.workflowDescription,\n          inputParamsDesc = _this$workflowFormDat.inputParamsDesc; // 检查工作流名称\n\n      if (!workflowName || workflowName.trim().length === 0) {\n        this.$message.error('工作流名称不能为空');\n        return false;\n      } // 检查名称中的危险字符\n\n\n      var namePattern = /^[a-zA-Z0-9\\u4e00-\\u9fa5\\s\\-_]+$/;\n\n      if (!namePattern.test(workflowName)) {\n        this.$message.error('工作流名称包含不允许的字符');\n        return false;\n      } // 检查工作流描述\n\n\n      if (!workflowDescription || workflowDescription.trim().length === 0) {\n        this.$message.error('工作流描述不能为空');\n        return false;\n      } // 检查描述长度\n\n\n      if (workflowDescription.length < 10 || workflowDescription.length > 200) {\n        this.$message.error('工作流描述长度必须在10-200字符之间');\n        return false;\n      } // 检查是否包含潜在的脚本注入\n\n\n      var scriptPattern = /<script|javascript:|on\\w+\\s*=/i;\n\n      if (scriptPattern.test(workflowName) || scriptPattern.test(workflowDescription) || scriptPattern.test(inputParamsDesc)) {\n        this.$message.error('输入内容包含不安全的脚本代码');\n        return false;\n      }\n\n      return true;\n    },\n    // 🔥 工作流文件选择处理（延迟上传机制）\n    handleWorkflowFileSelect: function handleWorkflowFileSelect(info) {\n      var _this8 = this;\n\n      console.log('🎯 CreatorAgentForm: 工作流文件选择:', info.file ? info.file.name : 'no file', 'status:', info.file ? info.file.status : 'no status'); // 处理文件选择（不进行实际上传）\n\n      if (info.fileList && info.fileList.length > 0) {\n        var file = info.fileList[0].originFileObj || info.fileList[0]; // 🛡️ 文件大小验证（5MB限制）\n\n        var maxSize = 5 * 1024 * 1024; // 5MB\n\n        if (file.size > maxSize) {\n          console.error('🎯 CreatorAgentForm: 文件大小超过限制:', file.size, '字节');\n          this.$message.error('文件大小不能超过 5MB!'); // 🔥 立即清理文件状态，不保存任何信息\n\n          this.workflowFileList = [];\n          this.workflowFileInfo = null; // 强制清空上传组件的文件列表\n\n          this.$nextTick(function () {\n            // 通过ref清空上传组件（如果有ref的话）\n            var uploadComponent = _this8.$refs.workflowUpload;\n\n            if (uploadComponent) {\n              uploadComponent.fileList = [];\n            }\n          });\n          console.log('🎯 CreatorAgentForm: 文件已清理，不保存超大文件');\n          return; // 直接返回，不执行后续逻辑\n        } // 🛡️ 文件大小不能为0\n\n\n        if (file.size === 0) {\n          console.error('🎯 CreatorAgentForm: 文件大小为0');\n          this.$message.error('文件不能为空!'); // 清理文件状态\n\n          this.workflowFileList = [];\n          this.workflowFileInfo = null;\n          return;\n        } // 🛡️ 文件名安全检查\n\n\n        if (!this.isSecureFileName(file.name)) {\n          console.error('🎯 CreatorAgentForm: 文件名不安全:', file.name);\n          this.$message.error('文件名包含不安全字符，请重命名后重试!'); // 清理文件状态\n\n          this.workflowFileList = [];\n          this.workflowFileInfo = null;\n          return;\n        } // 🛡️ 文件类型检查（扩展名）\n\n\n        var fileName = file.name.toLowerCase();\n\n        if (!fileName.endsWith('.zip')) {\n          console.error('🎯 CreatorAgentForm: 文件扩展名不正确:', file.name);\n          this.$message.error('只能上传 .zip 格式的文件!'); // 清理文件状态\n\n          this.workflowFileList = [];\n          this.workflowFileInfo = null;\n          return;\n        } // 🛡️ 文件类型检查（MIME类型）\n\n\n        var allowedMimeTypes = ['application/zip', 'application/x-zip-compressed', 'application/x-zip'];\n\n        if (!allowedMimeTypes.includes(file.type)) {\n          console.error('🎯 CreatorAgentForm: 文件MIME类型不正确:', file.type);\n          this.$message.error('文件类型不正确，只允许上传ZIP压缩包!'); // 清理文件状态\n\n          this.workflowFileList = [];\n          this.workflowFileInfo = null;\n          return;\n        } // ✅ 所有验证通过，保存文件信息\n\n\n        this.workflowFileList = [file];\n        this.workflowFileInfo = {\n          name: file.name,\n          originalName: file.name,\n          size: file.size,\n          file: file,\n          // 保存原始File对象\n          isSaved: false // 🔥 新选择的文件标记为未保存状态\n\n        };\n        console.log('🎯 CreatorAgentForm: 工作流文件已选择，等待最终上传:', file.name);\n        this.$message.success('工作流文件已选择，将在完成创建时上传');\n      } else if (info.fileList && info.fileList.length === 0) {\n        // 文件被移除\n        this.workflowFileList = [];\n        this.workflowFileInfo = null;\n        console.log('🎯 CreatorAgentForm: 工作流文件已移除');\n      }\n    },\n    // 🔥 生成工作流文件名（与后台管理系统一致的重命名逻辑）\n    generateWorkflowFileName: function generateWorkflowFileName(originalName) {\n      var customWorkflowName = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : null;\n      // 获取文件扩展名\n      var lastDotIndex = originalName.lastIndexOf('.');\n      var extension = lastDotIndex > -1 ? originalName.substring(lastDotIndex) : ''; // 生成时间戳\n\n      var timestamp = new Date().getTime(); // 生成随机数\n\n      var random = Math.floor(Math.random() * 10000).toString().padStart(4, '0'); // 智能体名称（清理特殊字符）\n\n      var agentName = this.formData.agentName ? this.formData.agentName.replace(/[^a-zA-Z0-9\\u4e00-\\u9fa5]/g, '_').substring(0, 20) : 'workflow'; // 工作流名称（清理特殊字符）\n\n      var workflowName = customWorkflowName || this.workflowFormData.workflowName ? (customWorkflowName || this.workflowFormData.workflowName).replace(/[^a-zA-Z0-9\\u4e00-\\u9fa5]/g, '_').substring(0, 20) : 'default'; // 生成新文件名：智能体名称_工作流名称_时间戳_随机数.扩展名\n\n      var newFileName = \"\".concat(agentName, \"_\").concat(workflowName, \"_\").concat(timestamp, \"_\").concat(random).concat(extension);\n      console.log('🎯 CreatorAgentForm: 文件重命名:', originalName, '->', newFileName);\n      return newFileName;\n    },\n    // 🔥 移除工作流文件\n    handleWorkflowRemove: function handleWorkflowRemove(file) {\n      console.log('🎯 CreatorAgentForm: 移除工作流文件:', file.name);\n      this.workflowFileList = [];\n      this.workflowFormData.workflowPackage = '';\n      this.workflowFileInfo = null;\n    },\n    // 🔥 移除工作流文件（用于文件信息显示区域）\n    handleRemoveWorkflowFile: function handleRemoveWorkflowFile() {\n      console.log('🎯 CreatorAgentForm: 移除工作流文件');\n      this.workflowFileList = [];\n      this.workflowFormData.workflowPackage = '';\n      this.workflowFileInfo = null;\n      this.$message.success('工作流文件已移除');\n    },\n    // 🔥 格式化文件大小\n    formatFileSize: function formatFileSize(bytes) {\n      if (!bytes) return '0 B';\n      var k = 1024;\n      var sizes = ['B', 'KB', 'MB', 'GB'];\n      var i = Math.floor(Math.log(bytes) / Math.log(k));\n      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n    },\n    // 🔥 提交工作流表单\n    handleWorkflowSubmit: function handleWorkflowSubmit() {\n      var _this9 = this;\n\n      return new Promise(function (resolve, reject) {\n        // 🛡️ 1. 验证表单（Vue 2 + Ant Design Vue 方式）\n        _this9.$refs.workflowFormRef.validate(function (valid) {\n          if (!valid) {\n            _this9.$message.error('请完善表单信息');\n\n            reject(new Error('表单验证失败'));\n            return;\n          }\n\n          _this9.performWorkflowSubmit().then(resolve).catch(reject);\n        });\n      });\n    },\n    // 🔥 执行工作流提交\n    performWorkflowSubmit: function () {\n      var _performWorkflowSubmit = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee12() {\n        var file;\n        return _regeneratorRuntime.wrap(function _callee12$(_context12) {\n          while (1) {\n            switch (_context12.prev = _context12.next) {\n              case 0:\n                _context12.prev = 0;\n\n                if (!(this.workflowFileList.length === 0)) {\n                  _context12.next = 4;\n                  break;\n                }\n\n                this.$message.error('请选择工作流文件');\n                throw new Error('未选择工作流文件');\n\n              case 4:\n                file = this.workflowFileList[0]; // 🛡️ 3. 提交前再次安全检查\n\n                if (this.isSecureFileName(file.name)) {\n                  _context12.next = 8;\n                  break;\n                }\n\n                this.$message.error('文件名不安全，请重新选择文件');\n                throw new Error('文件名不安全');\n\n              case 8:\n                if (file.name.toLowerCase().endsWith('.zip')) {\n                  _context12.next = 11;\n                  break;\n                }\n\n                this.$message.error('只能提交ZIP格式的文件');\n                throw new Error('文件格式不正确');\n\n              case 11:\n                if (!(file.size > 5 * 1024 * 1024)) {\n                  _context12.next = 14;\n                  break;\n                }\n\n                this.$message.error('文件大小超过5MB限制');\n                throw new Error('文件大小超限');\n\n              case 14:\n                if (this.validateWorkflowData()) {\n                  _context12.next = 16;\n                  break;\n                }\n\n                throw new Error('表单数据验证失败');\n\n              case 16:\n                this.stepLoading = true;\n                console.log('🎯 CreatorAgentForm: 提交工作流表单:', this.workflowFormData); // TODO: 调用工作流创建API\n                // const workflowData = {\n                //   agentId: this.createdAgent.id,\n                //   workflowName: this.workflowFormData.workflowName,\n                //   workflowDescription: this.workflowFormData.workflowDescription,\n                //   workflowFile: this.workflowFileList[0]\n                // }\n                // const response = await createWorkflow(workflowData)\n                // 模拟成功\n\n                _context12.next = 20;\n                return new Promise(function (resolve) {\n                  return setTimeout(resolve, 1000);\n                });\n\n              case 20:\n                this.$message.success('工作流创建成功!'); // 重新加载工作流列表\n\n                this.loadWorkflowList(this.createdAgent.id); // 重置表单\n\n                this.resetWorkflowForm();\n                return _context12.abrupt(\"return\", true);\n\n              case 26:\n                _context12.prev = 26;\n                _context12.t0 = _context12[\"catch\"](0);\n                console.error('🎯 CreatorAgentForm: 工作流创建失败:', _context12.t0);\n                this.$message.error('工作流创建失败: ' + (_context12.t0.message || '未知错误'));\n                throw _context12.t0;\n\n              case 31:\n                _context12.prev = 31;\n                this.stepLoading = false;\n                return _context12.finish(31);\n\n              case 34:\n              case \"end\":\n                return _context12.stop();\n            }\n          }\n        }, _callee12, this, [[0, 26, 31, 34]]);\n      }));\n\n      function performWorkflowSubmit() {\n        return _performWorkflowSubmit.apply(this, arguments);\n      }\n\n      return performWorkflowSubmit;\n    }(),\n    // 🔥 重置工作流表单\n    resetWorkflowForm: function resetWorkflowForm() {\n      var _this10 = this;\n\n      this.workflowFormData = {\n        workflowName: '',\n        workflowDescription: '',\n        inputParamsDesc: '',\n        workflowPackage: ''\n      };\n      this.workflowFileList = [];\n      this.workflowFileInfo = null;\n      this.currentWorkflowIndex = -1; // Vue 2 + Ant Design Vue 方式重置表单\n\n      this.$nextTick(function () {\n        if (_this10.$refs.workflowFormRef) {\n          _this10.$refs.workflowFormRef.resetFields();\n        }\n      });\n      console.log('🎯 CreatorAgentForm: 工作流表单已重置');\n    },\n    // 🔥 清空所有工作流数据（解决数据污染问题）\n    clearAllWorkflowData: function clearAllWorkflowData() {\n      // 清空暂存工作流列表\n      this.tempWorkflowList = []; // 重置工作流表单\n\n      this.resetWorkflowForm(); // 重置工作流相关状态\n\n      this.workflowFileList = [];\n      this.workflowFileInfo = null;\n      this.currentWorkflowIndex = -1; // 🔥 清空工作流验证错误状态\n\n      this.workflowValidationErrors = {}; // 🔥 重置工作流上传状态\n\n      this.workflowUploading = false; // 🔥 清空已保存的工作流列表\n\n      this.workflowList = [];\n      this.workflowLoading = false;\n      console.log('🎯 CreatorAgentForm: 所有工作流数据已清空');\n    },\n    // 🔥 处理输入参数说明失焦事件，手动触发验证\n    handleInputParamsBlur: function handleInputParamsBlur() {\n      var _this11 = this;\n\n      console.log('🎯 CreatorAgentForm: 输入参数说明失焦，触发验证');\n      this.$nextTick(function () {\n        if (_this11.$refs.workflowFormRef) {\n          _this11.$refs.workflowFormRef.validateField('inputParamsDesc', function (errorMessage) {\n            if (errorMessage) {\n              console.log('🎯 CreatorAgentForm: 输入参数说明验证失败:', errorMessage);\n            } else {\n              console.log('🎯 CreatorAgentForm: 输入参数说明验证通过');\n            }\n          });\n        }\n      });\n    },\n    // 🔥 滚动弹窗到顶部（提升用户体验）\n    scrollToTop: function scrollToTop() {\n      this.$nextTick(function () {\n        console.log('🎯 CreatorAgentForm: 开始查找滚动容器...'); // 🔥 优先查找步骤内容容器（真正的滚动容器）\n\n        var stepContent = document.querySelector('.step-content');\n\n        if (stepContent) {\n          console.log('🎯 CreatorAgentForm: 找到步骤内容容器:', {\n            scrollTop: stepContent.scrollTop,\n            scrollHeight: stepContent.scrollHeight,\n            clientHeight: stepContent.clientHeight\n          });\n          var beforeScrollTop = stepContent.scrollTop;\n          stepContent.scrollTop = 0;\n          var afterScrollTop = stepContent.scrollTop;\n          console.log(\"\\uD83C\\uDFAF CreatorAgentForm: \\u6B65\\u9AA4\\u5185\\u5BB9\\u6EDA\\u52A8 - \\u6EDA\\u52A8\\u524D: \".concat(beforeScrollTop, \", \\u6EDA\\u52A8\\u540E: \").concat(afterScrollTop));\n\n          if (beforeScrollTop !== afterScrollTop || beforeScrollTop === 0) {\n            console.log('🎯 CreatorAgentForm: 步骤内容滚动成功');\n            return;\n          }\n        } // 🔥 备用方案：尝试其他可能的滚动容器\n\n\n        var selectors = ['.creator-agent-modal .ant-modal-body', '.ant-modal-body', '.creator-agent-modal .ant-modal-content', '.ant-modal-content', '.creator-agent-modal', '.ant-modal-wrap'];\n        var scrollContainer = null;\n        var usedSelector = '';\n\n        for (var _i = 0, _selectors = selectors; _i < _selectors.length; _i++) {\n          var selector = _selectors[_i];\n          var element = document.querySelector(selector);\n\n          if (element) {\n            console.log(\"\\uD83C\\uDFAF CreatorAgentForm: \\u627E\\u5230\\u5143\\u7D20 \".concat(selector, \":\"), element);\n            console.log(\"\\uD83C\\uDFAF CreatorAgentForm: \\u5143\\u7D20\\u5F53\\u524D scrollTop: \".concat(element.scrollTop, \", scrollHeight: \").concat(element.scrollHeight, \", clientHeight: \").concat(element.clientHeight)); // 检查元素是否可滚动\n\n            if (element.scrollHeight > element.clientHeight || element.scrollTop > 0) {\n              scrollContainer = element;\n              usedSelector = selector;\n              break;\n            }\n          }\n        }\n\n        if (scrollContainer) {\n          var _beforeScrollTop = scrollContainer.scrollTop;\n          scrollContainer.scrollTop = 0;\n          var _afterScrollTop = scrollContainer.scrollTop;\n          console.log(\"\\uD83C\\uDFAF CreatorAgentForm: \\u4F7F\\u7528\\u9009\\u62E9\\u5668 \".concat(usedSelector, \" \\u6EDA\\u52A8\\u5230\\u9876\\u90E8\"));\n          console.log(\"\\uD83C\\uDFAF CreatorAgentForm: \\u6EDA\\u52A8\\u524D: \".concat(_beforeScrollTop, \", \\u6EDA\\u52A8\\u540E: \").concat(_afterScrollTop));\n\n          if (_beforeScrollTop === _afterScrollTop && _beforeScrollTop > 0) {\n            console.warn('🎯 CreatorAgentForm: 滚动可能没有生效，尝试其他方法'); // 尝试平滑滚动\n\n            scrollContainer.scrollTo({\n              top: 0,\n              behavior: 'smooth'\n            });\n          }\n        } else {\n          console.warn('🎯 CreatorAgentForm: 未找到可滚动的容器，延迟重试'); // 🔥 延迟重试，并尝试更多选择器\n\n          setTimeout(function () {\n            var allModalElements = document.querySelectorAll('[class*=\"modal\"]');\n            console.log('🎯 CreatorAgentForm: 所有modal相关元素:', allModalElements);\n\n            var _iterator = _createForOfIteratorHelper(allModalElements),\n                _step;\n\n            try {\n              for (_iterator.s(); !(_step = _iterator.n()).done;) {\n                var _element = _step.value;\n\n                if (_element.scrollHeight > _element.clientHeight || _element.scrollTop > 0) {\n                  _element.scrollTop = 0;\n                  console.log('🎯 CreatorAgentForm: 延迟重试成功，使用元素:', _element);\n                  return;\n                }\n              }\n            } catch (err) {\n              _iterator.e(err);\n            } finally {\n              _iterator.f();\n            }\n\n            console.error('🎯 CreatorAgentForm: 延迟重试仍未找到可滚动容器');\n          }, 200);\n        }\n      });\n    },\n    // 🔥 新增下一个工作流（前端暂存机制）\n    addNextWorkflow: function addNextWorkflow() {\n      console.log('🎯 CreatorAgentForm: 新增下一个工作流'); // 🔥 自动暂存当前数据（智能保护机制）\n\n      this.autoSaveCurrentWorkflow(); // 重置表单，准备添加新工作流\n\n      this.resetWorkflowForm();\n      this.currentWorkflowIndex = -1; // 设置为新建状态\n      // 🔥 滚动到顶部，确保用户看到新工作流表单的开始\n\n      this.scrollToTop();\n      this.$message.success('可以继续添加新工作流');\n    },\n    // 🔥 验证当前工作流数据\n    validateCurrentWorkflowData: function validateCurrentWorkflowData() {\n      // 检查工作流名称\n      if (!this.workflowFormData.workflowName.trim()) {\n        this.$message.error('请输入工作流名称');\n        return false;\n      } // 🔥 严格检查工作流名称长度（防止数据库字段溢出）\n\n\n      if (this.workflowFormData.workflowName.trim().length > 30) {\n        this.$message.error('工作流名称不能超过30个字符');\n        return false;\n      } // 检查工作流描述\n\n\n      if (!this.workflowFormData.workflowDescription.trim()) {\n        this.$message.error('请输入工作流描述');\n        return false;\n      } // 🔥 严格检查工作流描述长度\n\n\n      if (this.workflowFormData.workflowDescription.trim().length > 200) {\n        this.$message.error('工作流描述不能超过200个字符');\n        return false;\n      } // 🔥 检查输入参数说明（后端必填字段）\n\n\n      if (!this.workflowFormData.inputParamsDesc.trim()) {\n        this.$message.error('请输入参数说明');\n        return false;\n      } // 🔥 检查输入参数说明格式（与后台管理一致的正则验证）\n\n\n      var inputParamsPattern = /^[^:：]+[:：](?:\"[^\"]*\"|'[^']*'|[^:：,，]+)(?:[,，][^:：]+[:：](?:\"[^\"]*\"|'[^']*'|[^:：,，]+))*$/;\n\n      if (!inputParamsPattern.test(this.workflowFormData.inputParamsDesc.trim())) {\n        this.$message.error('请按照格式填写：参数:值 或 参数:\"值\" 或 参数:\\'值\\'');\n        return false;\n      } // 🔥 检查工作流文件（区分新增和已保存工作流）\n\n\n      console.log('🎯 CreatorAgentForm: 文件验证 - workflowFileList.length:', this.workflowFileList.length);\n      console.log('🎯 CreatorAgentForm: 文件验证 - workflowFileInfo:', this.workflowFileInfo);\n      console.log('🎯 CreatorAgentForm: 文件验证 - workflowFileInfo.isSaved:', this.workflowFileInfo && this.workflowFileInfo.isSaved); // 🔥 文件验证逻辑：\n      // 1. 如果有新选择的文件（workflowFileList.length > 0），验证通过\n      // 2. 如果没有新文件，但有已保存的文件信息（workflowFileInfo.isSaved），验证通过\n      // 3. 其他情况验证失败\n\n      var hasNewFile = this.workflowFileList.length > 0;\n      var hasSavedFile = this.workflowFileInfo && this.workflowFileInfo.isSaved;\n\n      if (!hasNewFile && !hasSavedFile) {\n        this.$message.error('工作流文件为必填项，请上传ZIP压缩包');\n        return false;\n      }\n\n      return true;\n    },\n    // 🔥 保存当前工作流到暂存列表（支持任意字段的暂存）\n    saveCurrentWorkflowToTemp: function saveCurrentWorkflowToTemp() {\n      // 🔥 生成默认工作流名称（如果用户没有输入）\n      var workflowName = this.workflowFormData.workflowName.trim();\n\n      if (!workflowName) {\n        // 如果没有名称，生成一个默认名称\n        var timestamp = new Date().toLocaleString('zh-CN');\n        workflowName = \"\\u5DE5\\u4F5C\\u6D41_\".concat(timestamp);\n      } // 🔥 智能处理工作流状态和文件信息\n\n\n      var isEditingExisting = this.currentWorkflowIndex >= 0;\n      var existingWorkflow = isEditingExisting ? this.tempWorkflowList[this.currentWorkflowIndex] : null;\n      console.log('🎯 CreatorAgentForm: saveCurrentWorkflowToTemp - isEditingExisting:', isEditingExisting);\n      console.log('🎯 CreatorAgentForm: saveCurrentWorkflowToTemp - existingWorkflow:', existingWorkflow);\n      console.log('🎯 CreatorAgentForm: saveCurrentWorkflowToTemp - existingWorkflow.status:', existingWorkflow ? existingWorkflow.status : 'null'); // 🔥 保持已保存工作流的状态和文件信息\n\n      var workflowData = {\n        id: isEditingExisting ? existingWorkflow.id : Date.now(),\n        workflowName: workflowName,\n        workflowDescription: this.workflowFormData.workflowDescription.trim(),\n        inputParamsDesc: this.workflowFormData.inputParamsDesc.trim(),\n        workflowFile: this.workflowFileList.length > 0 ? this.workflowFileList[0] : existingWorkflow ? existingWorkflow.workflowFile : null,\n        fileName: this.workflowFileInfo ? this.workflowFileInfo.name : existingWorkflow ? existingWorkflow.fileName : '',\n        fileSize: this.workflowFileInfo ? this.workflowFileInfo.size : existingWorkflow ? existingWorkflow.fileSize : 0,\n        // 🔥 保持已保存工作流的状态，新工作流设为draft\n        status: existingWorkflow && existingWorkflow.status === 'saved' ? 'saved' : 'draft',\n        createTime: isEditingExisting ? existingWorkflow.createTime : new Date(),\n        // 🔥 保持已保存工作流的包路径信息\n        workflowPackage: existingWorkflow ? existingWorkflow.workflowPackage : '',\n        originalWorkflow: existingWorkflow ? existingWorkflow.originalWorkflow : null\n      };\n      console.log('🎯 CreatorAgentForm: saveCurrentWorkflowToTemp - 创建的workflowData:', workflowData);\n      console.log('🎯 CreatorAgentForm: saveCurrentWorkflowToTemp - workflowData.status:', workflowData.status);\n      console.log('🎯 CreatorAgentForm: saveCurrentWorkflowToTemp - workflowData.workflowPackage:', workflowData.workflowPackage);\n\n      if (this.currentWorkflowIndex >= 0) {\n        // 更新现有工作流\n        console.log('🎯 CreatorAgentForm: 更新现有工作流，索引:', this.currentWorkflowIndex);\n        this.tempWorkflowList.splice(this.currentWorkflowIndex, 1, workflowData);\n        console.log('🎯 CreatorAgentForm: 更新暂存工作流:', workflowData);\n      } else {\n        // 添加新工作流\n        console.log('🎯 CreatorAgentForm: 添加新工作流，当前列表长度:', this.tempWorkflowList.length);\n        this.tempWorkflowList.push(workflowData);\n        console.log('🎯 CreatorAgentForm: 添加暂存工作流:', workflowData);\n        console.log('🎯 CreatorAgentForm: 添加后列表长度:', this.tempWorkflowList.length);\n      } // 🔥 实时更新验证状态\n\n\n      this.updateWorkflowValidation(workflowData);\n    },\n    // 🔥 从暂存列表加载工作流数据到表单（带自动暂存保护）\n    loadWorkflowFromTemp: function loadWorkflowFromTemp(index) {\n      var skipAutoSave = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n\n      if (index < 0 || index >= this.tempWorkflowList.length) {\n        console.error('🎯 CreatorAgentForm: 无效的工作流索引:', index);\n        return;\n      } // 🔥 自动暂存当前正在编辑的数据（数据保护机制）\n      // 在验证错误处理时跳过自动暂存，避免循环\n\n\n      if (!skipAutoSave) {\n        this.autoSaveCurrentWorkflow();\n      }\n\n      var workflow = this.tempWorkflowList[index];\n      console.log('🎯 CreatorAgentForm: 加载暂存工作流:', workflow); // 加载表单数据\n\n      this.workflowFormData = {\n        workflowName: workflow.workflowName,\n        workflowDescription: workflow.workflowDescription,\n        inputParamsDesc: workflow.inputParamsDesc || '',\n        workflowPackage: ''\n      }; // 🔥 加载文件数据（区分新增和已保存工作流）\n\n      if (workflow.status === 'saved' && workflow.fileName) {\n        // 已保存的工作流：显示文件信息但没有File对象\n        this.workflowFileList = []; // 已保存的工作流没有File对象\n\n        this.workflowFileInfo = {\n          name: workflow.fileName,\n          originalName: workflow.fileName,\n          size: workflow.fileSize || 0,\n          file: null,\n          // 已保存的工作流没有File对象\n          isSaved: true,\n          // 标记为已保存状态\n          packagePath: workflow.workflowPackage || '' // 保存包路径\n\n        };\n        console.log('🎯 CreatorAgentForm: 加载已保存工作流的文件信息:', this.workflowFileInfo);\n      } else if (workflow.workflowFile && workflow.fileName) {\n        // 新增的工作流：有完整的File对象\n        this.workflowFileList = [workflow.workflowFile];\n        this.workflowFileInfo = {\n          name: workflow.fileName,\n          originalName: workflow.fileName,\n          size: workflow.fileSize || 0,\n          file: workflow.workflowFile,\n          isSaved: false\n        };\n        console.log('🎯 CreatorAgentForm: 加载新增工作流的文件信息:', this.workflowFileInfo);\n      } else {\n        // 没有文件信息\n        this.workflowFileList = [];\n        this.workflowFileInfo = null;\n        console.log('🎯 CreatorAgentForm: 工作流没有文件信息');\n      } // 设置当前编辑索引\n\n\n      this.currentWorkflowIndex = index; // 在验证错误处理时不显示加载提示\n\n      if (!skipAutoSave) {\n        this.$message.info(\"\\u5DF2\\u52A0\\u8F7D\\u5DE5\\u4F5C\\u6D41: \".concat(workflow.workflowName)); // 🔥 滚动到第二步顶部，确保用户看到完整的工作流表单\n\n        this.scrollToTop();\n      }\n    },\n    // 🔥 自动暂存当前工作流数据（智能数据保护）\n    autoSaveCurrentWorkflow: function autoSaveCurrentWorkflow() {\n      // 🔥 检查当前是否有任意工作流数据需要保存（包括已保存的工作流）\n      var hasCurrentData = this.workflowFormData.workflowName.trim() || this.workflowFormData.workflowDescription.trim() || this.workflowFileList.length > 0 || this.workflowFileInfo && this.workflowFileInfo.isSaved;\n\n      if (!hasCurrentData) {\n        console.log('🎯 CreatorAgentForm: 当前无数据，跳过自动暂存');\n        return false; // 返回false表示没有暂存任何数据\n      } // 如果当前正在编辑已存在的工作流，直接更新\n\n\n      if (this.currentWorkflowIndex >= 0) {\n        console.log('🎯 CreatorAgentForm: 自动更新当前编辑的工作流');\n        this.saveCurrentWorkflowToTemp();\n        return true; // 返回true表示已暂存数据\n      } // 🔥 如果是新工作流，只要有任意数据就自动暂存\n\n\n      console.log('🎯 CreatorAgentForm: 自动暂存新工作流数据（有任意输入）');\n      this.saveCurrentWorkflowToTemp(); // 在完成创建流程中不显示暂存提示，避免干扰用户\n\n      if (!this.stepLoading) {\n        this.$message.info('当前工作流数据已自动暂存');\n      }\n\n      return true; // 返回true表示已暂存数据\n    },\n    // 🔥 验证所有工作流的完整性\n    validateAllWorkflows: function validateAllWorkflows() {\n      console.log('🎯 CreatorAgentForm: 开始验证所有工作流的完整性'); // 清空之前的验证错误\n\n      this.workflowValidationErrors = {};\n      var isAllValid = true;\n      var invalidCount = 0;\n      var allErrors = []; // 遍历所有暂存的工作流\n\n      for (var i = 0; i < this.tempWorkflowList.length; i++) {\n        var workflow = this.tempWorkflowList[i];\n        var errors = []; // 验证工作流名称\n\n        if (!workflow.workflowName || !workflow.workflowName.trim()) {\n          errors.push('缺少工作流名称');\n        } // 验证工作流描述\n\n\n        if (!workflow.workflowDescription || !workflow.workflowDescription.trim()) {\n          errors.push('缺少工作流描述');\n        } // 🔥 验证输入参数说明（后端必填字段）\n\n\n        if (!workflow.inputParamsDesc || !workflow.inputParamsDesc.trim()) {\n          errors.push('缺少参数说明');\n        } else {\n          // 🔥 验证输入参数说明格式（与后台管理一致的正则验证）\n          var inputParamsPattern = /^[^:：]+[:：](?:\"[^\"]*\"|'[^']*'|[^:：,，]+)(?:[,，][^:：]+[:：](?:\"[^\"]*\"|'[^']*'|[^:：,，]+))*$/;\n\n          if (!inputParamsPattern.test(workflow.inputParamsDesc.trim())) {\n            errors.push('参数说明格式不正确');\n          }\n        } // 🔥 验证工作流文件（区分新增和已保存工作流）\n\n\n        if (workflow.status === 'saved') {\n          // 已保存的工作流：只需要有文件名或包路径\n          if (!workflow.fileName && !workflow.workflowPackage) {\n            errors.push('缺少压缩包文件');\n          }\n        } else {\n          // 新增的工作流：需要有File对象和文件名\n          if (!workflow.workflowFile || !workflow.fileName) {\n            errors.push('缺少压缩包文件');\n          }\n        } // 记录验证结果\n\n\n        var isValid = errors.length === 0;\n        this.workflowValidationErrors[workflow.id] = {\n          errors: errors,\n          isValid: isValid,\n          workflowName: workflow.workflowName || \"\\u5DE5\\u4F5C\\u6D41\".concat(i + 1)\n        };\n\n        if (!isValid) {\n          isAllValid = false;\n          invalidCount++;\n          allErrors.push(\"\".concat(workflow.workflowName || \"\\u5DE5\\u4F5C\\u6D41\".concat(i + 1), \": \").concat(errors.join('、')));\n        }\n      }\n\n      var result = {\n        isValid: isAllValid,\n        invalidCount: invalidCount,\n        errors: allErrors,\n        validationDetails: this.workflowValidationErrors\n      };\n      console.log('🎯 CreatorAgentForm: 工作流验证结果:', result);\n      return result;\n    },\n    // 🔥 实时更新单个工作流的验证状态\n    updateWorkflowValidation: function updateWorkflowValidation(workflow) {\n      var errors = []; // 验证工作流名称\n\n      if (!workflow.workflowName || !workflow.workflowName.trim()) {\n        errors.push('缺少工作流名称');\n      } // 验证工作流描述\n\n\n      if (!workflow.workflowDescription || !workflow.workflowDescription.trim()) {\n        errors.push('缺少工作流描述');\n      } // 🔥 验证输入参数说明（与批量验证保持一致）\n\n\n      if (!workflow.inputParamsDesc || !workflow.inputParamsDesc.trim()) {\n        errors.push('缺少参数说明');\n      } else {\n        // 🔥 验证输入参数说明格式（与后台管理一致的正则验证）\n        var inputParamsPattern = /^[^:：]+[:：](?:\"[^\"]*\"|'[^']*'|[^:：,，]+)(?:[,，][^:：]+[:：](?:\"[^\"]*\"|'[^']*'|[^:：,，]+))*$/;\n\n        if (!inputParamsPattern.test(workflow.inputParamsDesc.trim())) {\n          errors.push('参数说明格式不正确');\n        }\n      } // 🔥 验证工作流文件（区分新增和已保存工作流）\n\n\n      if (workflow.status === 'saved') {\n        // 已保存的工作流：只需要有文件名或包路径\n        if (!workflow.fileName && !workflow.workflowPackage) {\n          errors.push('缺少压缩包文件');\n        }\n      } else {\n        // 新增的工作流：需要有File对象和文件名\n        if (!workflow.workflowFile || !workflow.fileName) {\n          errors.push('缺少压缩包文件');\n        }\n      } // 更新验证状态\n\n\n      var isValid = errors.length === 0;\n      this.$set(this.workflowValidationErrors, workflow.id, {\n        errors: errors,\n        isValid: isValid,\n        workflowName: workflow.workflowName || '未命名工作流'\n      });\n      console.log(\"\\uD83C\\uDFAF CreatorAgentForm: \\u66F4\\u65B0\\u5DE5\\u4F5C\\u6D41\\u9A8C\\u8BC1\\u72B6\\u6001 - \".concat(workflow.workflowName, \": \").concat(isValid ? '有效' : '无效'), errors);\n    },\n    // 🔥 智能错误处理 - 回填第一个有错误的工作流到表单\n    handleValidationErrors: function handleValidationErrors(validationResult) {\n      var _this12 = this;\n\n      console.log('🎯 CreatorAgentForm: 开始智能错误处理'); // 找到第一个有错误的工作流\n\n      var firstErrorWorkflowIndex = -1;\n      var firstErrorWorkflow = null;\n\n      for (var i = 0; i < this.tempWorkflowList.length; i++) {\n        var workflow = this.tempWorkflowList[i];\n        var validationInfo = validationResult.validationDetails[workflow.id];\n\n        if (validationInfo && !validationInfo.isValid) {\n          firstErrorWorkflowIndex = i;\n          firstErrorWorkflow = workflow;\n          break;\n        }\n      }\n\n      if (firstErrorWorkflowIndex >= 0 && firstErrorWorkflow) {\n        console.log(\"\\uD83C\\uDFAF CreatorAgentForm: \\u627E\\u5230\\u7B2C\\u4E00\\u4E2A\\u9519\\u8BEF\\u5DE5\\u4F5C\\u6D41: \".concat(firstErrorWorkflow.workflowName, \" (\\u7D22\\u5F15: \").concat(firstErrorWorkflowIndex, \")\")); // 回填错误工作流到表单中（跳过自动暂存避免循环）\n\n        this.loadWorkflowFromTemp(firstErrorWorkflowIndex, true); // 显示详细的错误信息\n\n        var errorInfo = validationResult.validationDetails[firstErrorWorkflow.id];\n        var errorMessage = \"\\u5DE5\\u4F5C\\u6D41\\\"\".concat(firstErrorWorkflow.workflowName, \"\\\"\\u7F3A\\u5C11\\u5FC5\\u586B\\u4FE1\\u606F\\uFF1A\").concat(errorInfo.errors.join('、'));\n        this.$message.error(errorMessage); // 显示总体错误统计\n\n        setTimeout(function () {\n          _this12.$message.warning(\"\\u5171\\u6709 \".concat(validationResult.invalidCount, \" \\u4E2A\\u5DE5\\u4F5C\\u6D41\\u5B58\\u5728\\u95EE\\u9898\\uFF0C\\u8BF7\\u9010\\u4E00\\u5B8C\\u5584\\u540E\\u518D\\u63D0\\u4EA4\"));\n        }, 1000);\n        console.log('🎯 CreatorAgentForm: 错误工作流已回填到表单，用户可以直接修改');\n      } else {\n        // 如果没找到错误工作流（理论上不应该发生）\n        this.$message.error(\"\\u8BF7\\u5B8C\\u5584\\u5DE5\\u4F5C\\u6D41\\u4FE1\\u606F\\u540E\\u518D\\u63D0\\u4EA4\\uFF0C\\u5171\\u6709 \".concat(validationResult.invalidCount, \" \\u4E2A\\u5DE5\\u4F5C\\u6D41\\u5B58\\u5728\\u95EE\\u9898\"));\n      }\n    },\n    // 🔥 从暂存列表删除工作流\n    deleteWorkflowFromTemp: function deleteWorkflowFromTemp(index) {\n      var _this13 = this;\n\n      if (index < 0 || index >= this.tempWorkflowList.length) {\n        console.error('🎯 CreatorAgentForm: 无效的工作流索引:', index);\n        return;\n      }\n\n      var workflow = this.tempWorkflowList[index];\n      this.$confirm({\n        title: '删除工作流',\n        content: \"\\u786E\\u5B9A\\u8981\\u5220\\u9664\\u5DE5\\u4F5C\\u6D41\\\"\".concat(workflow.workflowName, \"\\\"\\u5417\\uFF1F\"),\n        onOk: function onOk() {\n          _this13.tempWorkflowList.splice(index, 1);\n\n          console.log('🎯 CreatorAgentForm: 删除暂存工作流:', workflow); // 如果删除的是当前正在编辑的工作流，重置表单\n\n          if (_this13.currentWorkflowIndex === index) {\n            _this13.resetWorkflowForm();\n\n            _this13.currentWorkflowIndex = -1; // 🔥 删除当前编辑的工作流后滚动到顶部\n\n            _this13.scrollToTop();\n          } else if (_this13.currentWorkflowIndex > index) {\n            // 调整当前编辑索引\n            _this13.currentWorkflowIndex--;\n          }\n\n          _this13.$message.success('工作流已删除');\n        }\n      });\n    },\n    // 格式化日期\n    formatDate: function formatDate(date) {\n      if (!date) return '';\n      return new Date(date).toLocaleString('zh-CN');\n    }\n  }\n};", {"version": 3, "sources": ["CreatorAgentForm.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2YA,OAAA,oBAAA,MAAA,yCAAA;AACA,SAAA,WAAA,EAAA,WAAA,QAAA,qBAAA;AACA,SAAA,cAAA,EAAA,cAAA,EAAA,eAAA,QAAA,wBAAA;AAEA,eAAA;AACA,EAAA,IAAA,EAAA,kBADA;AAEA,EAAA,UAAA,EAAA;AACA,IAAA,oBAAA,EAAA;AADA,GAFA;AAKA,EAAA,KAAA,EAAA;AACA,IAAA,OAAA,EAAA;AACA,MAAA,IAAA,EAAA,OADA;AAEA,MAAA,OAAA,EAAA;AAFA,KADA;AAKA,IAAA,OAAA,EAAA;AACA,MAAA,IAAA,EAAA,OADA;AAEA,MAAA,OAAA,EAAA;AAFA,KALA;AASA,IAAA,KAAA,EAAA;AACA,MAAA,IAAA,EAAA,MADA;AAEA,MAAA,OAAA,EAAA;AAFA,KATA;AAaA,IAAA,IAAA,EAAA;AACA,MAAA,IAAA,EAAA,MADA;AAEA,MAAA,OAAA,EAAA,QAFA,CAEA;;AAFA;AAbA,GALA;AAwBA,EAAA,IAxBA,kBAwBA;AACA,WAAA;AACA,MAAA,WAAA,EAAA,CADA;AACA;AACA,MAAA,WAAA,EAAA,KAFA;AAEA;AACA,MAAA,YAAA,EAAA,IAHA;AAGA;AAEA,MAAA,QAAA,EAAA;AACA,QAAA,OAAA,EAAA,EADA;AAEA,QAAA,SAAA,EAAA,EAFA;AAGA,QAAA,gBAAA,EAAA,EAHA;AAIA,QAAA,WAAA,EAAA,EAJA;AAKA,QAAA,cAAA,EAAA,EALA;AAMA,QAAA,KAAA,EAAA;AANA,OALA;AAcA;AACA,MAAA,gBAAA,EAAA;AACA,QAAA,YAAA,EAAA,EADA;AAEA,QAAA,mBAAA,EAAA,EAFA;AAGA,QAAA,eAAA,EAAA,EAHA;AAIA,QAAA,eAAA,EAAA;AAJA,OAfA;AAsBA;AACA,MAAA,gBAAA,EAAA,EAvBA;AAuBA;AACA,MAAA,oBAAA,EAAA,CAAA,CAxBA;AAwBA;AAEA;AACA,MAAA,wBAAA,EAAA,EA3BA;AA2BA;AACA,MAAA,gBAAA,EAAA,EA5BA;AA4BA;AAEA;AACA,MAAA,gBAAA,EAAA,IA/BA;AA+BA;AACA,MAAA,iBAAA,EAAA,KAhCA;AAgCA;AACA,MAAA,YAAA,EAAA,EAjCA;AAiCA;AACA,MAAA,eAAA,EAAA,KAlCA;AAoCA,MAAA,KAAA,EAAA;AACA,QAAA,SAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,EAEA;AAAA,UAAA,GAAA,EAAA,CAAA;AAAA,UAAA,GAAA,EAAA,GAAA;AAAA,UAAA,OAAA,EAAA,sBAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAFA,CADA;AAKA,QAAA,gBAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,EAEA;AAAA,UAAA,GAAA,EAAA,CAAA;AAAA,UAAA,GAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,uBAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAFA,CALA;AASA,QAAA,WAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,UAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CATA;AAYA,QAAA,cAAA,EAAA,CACA;AAAA,UAAA,IAAA,EAAA,KAAA;AAAA,UAAA,OAAA,EAAA,aAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,CAZA;AAeA,QAAA,KAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,OAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,EAEA;AAAA,UAAA,IAAA,EAAA,QAAA;AAAA,UAAA,GAAA,EAAA,CAAA;AAAA,UAAA,GAAA,EAAA,KAAA;AAAA,UAAA,OAAA,EAAA,mBAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAFA;AAfA,OApCA;AAyDA;AACA,MAAA,aAAA,EAAA;AACA,QAAA,YAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,WAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,EAEA;AAAA,UAAA,GAAA,EAAA,CAAA;AAAA,UAAA,GAAA,EAAA,EAAA;AAAA,UAAA,OAAA,EAAA,qBAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAFA,EAGA;AAAA,UAAA,OAAA,EAAA,kCAAA;AAAA,UAAA,OAAA,EAAA,2BAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAHA,CADA;AAMA,QAAA,mBAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,WAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,EAEA;AAAA,UAAA,GAAA,EAAA,CAAA;AAAA,UAAA,GAAA,EAAA,GAAA;AAAA,UAAA,OAAA,EAAA,sBAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAFA,CANA;AAUA,QAAA,eAAA,EAAA,CACA;AAAA,UAAA,QAAA,EAAA,IAAA;AAAA,UAAA,OAAA,EAAA,SAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SADA,EAEA;AAAA,UAAA,GAAA,EAAA,CAAA;AAAA,UAAA,OAAA,EAAA,cAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAFA,EAGA;AAAA,UAAA,GAAA,EAAA,KAAA;AAAA,UAAA,OAAA,EAAA,oBAAA;AAAA,UAAA,OAAA,EAAA;AAAA,SAHA,EAIA;AACA,UAAA,OAAA,EAAA,yFADA;AAEA,UAAA,OAAA,EAAA,kCAFA;AAGA,UAAA,OAAA,EAAA;AAHA,SAJA,CAVA;AAoBA,QAAA,eAAA,EAAA,CACA;AADA;AApBA;AA1DA,KAAA;AAmFA,GA5GA;AA8GA,EAAA,QAAA,EAAA;AACA,IAAA,UADA,wBACA;AACA,aAAA,KAAA,IAAA,KAAA,QAAA,GAAA,OAAA,GAAA,OAAA;AACA,KAHA;AAKA;AACA,IAAA,YANA,0BAMA;AACA,uBAAA,MAAA,CAAA,OAAA,CAAA,WAAA,CAAA;AACA,KARA;AAUA,IAAA,aAVA,2BAUA;AACA,aAAA;AACA,0BAAA,KAAA,GAAA,CAAA,GAAA,CAAA,cAAA;AADA,OAAA;AAGA,KAdA;AAgBA,IAAA,UAhBA,wBAgBA;AACA,aAAA;AACA,gBAAA,CADA;AAEA,eAAA,EAFA,CAEA;;AAFA,OAAA;AAIA;AArBA,GA9GA;AAsIA,EAAA,KAAA,EAAA;AACA,IAAA,OADA,mBACA,GADA,EACA;AAAA;;AACA,UAAA,GAAA,EAAA;AACA,aAAA,QAAA,GADA,CAEA;;AACA,aAAA,WAAA,GAAA,CAAA,CAHA,CAKA;;AACA,aAAA,WAAA;;AAEA,YAAA,KAAA,IAAA,KAAA,MAAA,IAAA,KAAA,KAAA,EAAA;AACA,eAAA,YAAA,qBAAA,KAAA,KAAA,EADA,CAEA;;AACA,eAAA,SAAA,CAAA,YAAA;AACA,YAAA,KAAA,CAAA,gBAAA,CAAA,KAAA,CAAA,KAAA,CAAA,EAAA;AACA,WAFA;AAGA;AACA,OAfA,MAeA;AACA;AACA,aAAA,SAAA;AACA,aAAA,oBAAA;AACA;AACA,KAtBA;AAwBA,IAAA,KAAA,EAAA;AACA,MAAA,OADA,mBACA,GADA,EACA;AAAA;;AACA,YAAA,GAAA,IAAA,KAAA,OAAA,EAAA;AACA,eAAA,QAAA;;AACA,cAAA,KAAA,IAAA,KAAA,MAAA,EAAA;AACA,iBAAA,YAAA,qBAAA,GAAA,EADA,CAEA;;AACA,iBAAA,SAAA,CAAA,YAAA;AACA,cAAA,MAAA,CAAA,gBAAA,CAAA,GAAA,CAAA,EAAA;AACA,aAFA;AAGA;AACA;AACA,OAZA;AAaA,MAAA,IAAA,EAAA;AAbA;AAxBA,GAtIA;AA+KA,EAAA,OAAA,EAAA;AACA;AACA,IAAA,QAFA,sBAEA;AAAA;;AACA,UAAA,KAAA,IAAA,KAAA,MAAA,IAAA,KAAA,KAAA,EAAA;AACA,aAAA,QAAA,GAAA;AACA,UAAA,SAAA,EAAA,KAAA,KAAA,CAAA,SAAA,IAAA,EADA;AAEA,UAAA,gBAAA,EAAA,KAAA,KAAA,CAAA,gBAAA,IAAA,EAFA;AAGA,UAAA,WAAA,EAAA,KAAA,KAAA,CAAA,WAAA,IAAA,EAHA;AAIA,UAAA,cAAA,EAAA,KAAA,KAAA,CAAA,cAAA,IAAA,EAJA;AAKA,UAAA,KAAA,EAAA,KAAA,KAAA,CAAA,KAAA,IAAA;AALA,SAAA;AAOA,OARA,MAQA;AACA,aAAA,QAAA,GAAA;AACA,UAAA,SAAA,EAAA,EADA;AAEA,UAAA,gBAAA,EAAA,EAFA;AAGA,UAAA,WAAA,EAAA,EAHA;AAIA,UAAA,cAAA,EAAA,EAJA;AAKA,UAAA,KAAA,EAAA;AALA,SAAA,CADA,CASA;;AACA,aAAA,oBAAA;AACA,OApBA,CAsBA;;;AACA,WAAA,SAAA,CAAA,YAAA;AACA,YAAA,MAAA,CAAA,KAAA,CAAA,IAAA,EAAA;AACA,UAAA,MAAA,CAAA,KAAA,CAAA,IAAA,CAAA,aAAA;AACA;AACA,OAJA;AAKA,KA9BA;AAgCA;AACA,IAAA,SAjCA,uBAiCA;AACA,WAAA,WAAA,GAAA,CAAA;AACA,WAAA,WAAA,GAAA,KAAA;AACA,WAAA,YAAA,GAAA,IAAA;AAEA,WAAA,QAAA,GAAA;AACA,QAAA,OAAA,EAAA,EADA;AAEA,QAAA,SAAA,EAAA,EAFA;AAGA,QAAA,gBAAA,EAAA,EAHA;AAIA,QAAA,WAAA,EAAA,EAJA;AAKA,QAAA,cAAA,EAAA,EALA;AAMA,QAAA,KAAA,EAAA;AANA,OAAA;AAQA,WAAA,YAAA,GAAA,EAAA;;AAEA,UAAA,KAAA,KAAA,CAAA,IAAA,EAAA;AACA,aAAA,KAAA,CAAA,IAAA,CAAA,aAAA;AACA;AACA,KAnDA;AAqDA;AACA,IAAA,UAtDA;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAuDA;AACA,gBAAA,cAxDA,GAwDA,KAAA,QAAA,CAAA,WAxDA;AAyDA,gBAAA,gBAzDA,GAyDA,KAAA,KAAA,CAAA,YAAA,IAAA,KAAA,KAAA,CAAA,YAAA,CAAA,eAAA,EAzDA;;AA2DA,oBAAA,CAAA,KAAA,QAAA,CAAA,WAAA,IAAA,gBAAA,EAAA;AACA,uBAAA,QAAA,CAAA,WAAA,GAAA,gBAAA;AACA;;AAEA,qBAAA,KAAA,CAAA,IAAA,CAAA,QAAA;AAAA,sFAAA,iBAAA,KAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,4BAAA,MAAA,CAAA,QAAA,CAAA,WAAA,GAAA,cAAA;;AADA,iCAGA,KAHA;AAAA;AAAA;AAAA;;AAIA,4BAAA,MAAA,CAAA,WAAA,GAAA,IAAA;AAJA;AAAA;AAAA,mCAOA,MAAA,CAAA,mBAAA,EAPA;;AAAA;AASA;AACA,4BAAA,SAVA,GAUA;AACA,8BAAA,SAAA,EAAA,MAAA,CAAA,QAAA,CAAA,SAAA,CAAA,IAAA,EADA;AAEA,8BAAA,gBAAA,EAAA,MAAA,CAAA,QAAA,CAAA,gBAAA,CAAA,IAAA,EAFA;AAGA,8BAAA,WAAA,EAAA,MAAA,CAAA,kBAAA,CAAA,MAAA,CAAA,QAAA,CAAA,WAAA,CAHA;AAIA,8BAAA,cAAA,EAAA,MAAA,CAAA,QAAA,CAAA,cAAA,CAAA,IAAA,EAJA;AAKA,8BAAA,KAAA,EAAA,MAAA,CAAA,QAAA,CAAA;AALA,6BAVA,EAoBA;;AApBA,kCAsBA,MAAA,CAAA,IAAA,KAAA,QAtBA;AAAA;AAAA;AAAA;;AAAA;AAAA,mCAwBA,MAAA,CAAA,eAAA,CAAA,SAAA,CAxBA;;AAAA;AAwBA,4BAAA,WAxBA;;AAyBA,4BAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,gBAAA;;AAzBA;AAAA;;AAAA;AAAA;AAAA,mCA4BA,MAAA,CAAA,eAAA,CAAA,SAAA,CA5BA;;AAAA;AA4BA,4BAAA,WA5BA;;AA6BA,4BAAA,MAAA,CAAA,QAAA,CAAA,OAAA,CAAA,gBAAA;;AA7BA;AAgCA,4BAAA,MAAA,CAAA,YAAA,GAAA,WAAA,CAhCA,CAkCA;;AACA,4BAAA,MAAA,CAAA,WAAA,GAAA,CAAA;;AACA,4BAAA,MAAA,CAAA,gBAAA,CAAA,WAAA,CAAA,EAAA,EApCA,CAoCA;AAEA;;;AACA,4BAAA,MAAA,CAAA,WAAA;;AAvCA;AAAA;;AAAA;AAAA;AAAA;AA0CA,4BAAA,OAAA,CAAA,KAAA,CAAA,+BAAA;;AACA,4BAAA,MAAA,CAAA,QAAA,CAAA,KAAA,CAAA,eAAA,YAAA,OAAA,IAAA,MAAA,CAAA;;AA3CA;AAAA;AA6CA,4BAAA,MAAA,CAAA,WAAA,GAAA,KAAA;AA7CA;;AAAA;AAAA;AAAA;;AAAA;AAgDA,4BAAA,MAAA,CAAA,QAAA,CAAA,KAAA,CAAA,SAAA;;AAhDA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,mBAAA;;AAAA;AAAA;AAAA;AAAA;;AA/DA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAoHA;AACA,IAAA,UArHA,wBAqHA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,4BAAA;AACA,WAAA,WAAA,GAAA,CAAA,CAFA,CAIA;;AACA,WAAA,WAAA;AACA,KA3HA;AA6HA;AACA,IAAA,cA9HA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAgIA,gBAAA,OAAA,CAAA,GAAA,CAAA,qCAAA;AACA,qBAAA,WAAA,GAAA,IAAA,CAjIA,CAmIA;;AACA,gBAAA,OAAA,CAAA,GAAA,CAAA,uCAAA;AACA,gBAAA,OAAA,CAAA,GAAA,CAAA,gCAAA,EAAA,KAAA,gBAAA,CAAA,MAAA;AACA,gBAAA,OAAA,CAAA,GAAA,CAAA,8BAAA,EAAA,KAAA,oBAAA;AACA,gBAAA,UAvIA,GAuIA,KAAA,uBAAA,EAvIA;AAwIA,gBAAA,OAAA,CAAA,GAAA,CAAA,8BAAA,EAAA,UAAA;AACA,gBAAA,OAAA,CAAA,GAAA,CAAA,+BAAA,EAAA,KAAA,gBAAA,CAAA,MAAA,EAzIA,CA2IA;;AA3IA,sBA4IA,KAAA,gBAAA,CAAA,MAAA,KAAA,CA5IA;AAAA;AAAA;AAAA;;AA6IA,gBAAA,OAAA,CAAA,GAAA,CAAA,kCAAA;AACA,qBAAA,QAAA,CAAA,OAAA,CAAA,UAAA;AACA,qBAAA,KAAA,CAAA,UAAA,EAAA,KAAA,YAAA;AACA,qBAAA,YAAA;AAhJA;;AAAA;AAoJA,gBAAA,OAAA,CAAA,GAAA,oGAAA,KAAA,gBAAA,CAAA,MAAA,gCApJA,CAsJA;;AACA,gBAAA,gBAvJA,GAuJA,KAAA,oBAAA,EAvJA;;AAAA,oBAwJA,gBAAA,CAAA,OAxJA;AAAA;AAAA;AAAA;;AAyJA,gBAAA,OAAA,CAAA,KAAA,CAAA,+BAAA,EAAA,gBAAA,CAAA,MAAA,EAzJA,CA2JA;;AACA,qBAAA,sBAAA,CAAA,gBAAA;AACA,qBAAA,WAAA,GAAA,KAAA;AA7JA;;AAAA;AAiKA,gBAAA,OAAA,CAAA,GAAA,kIAAA,KAAA,gBAAA,CAAA,MAAA,gCAjKA,CAmKA;;AAnKA;AAAA,uBAoKA,KAAA,kBAAA,EApKA;;AAAA;AAoKA,gBAAA,cApKA;AAsKA,qBAAA,QAAA,CAAA,OAAA,oCAAA,cAAA,CAAA,MAAA,8DAtKA,CAwKA;;AACA,qBAAA,WAAA,GAAA,CAAA;AACA,qBAAA,WAAA;AAEA,gBAAA,OAAA,CAAA,GAAA,CAAA,gCAAA;AA5KA;AAAA;;AAAA;AAAA;AAAA;AA+KA,gBAAA,OAAA,CAAA,KAAA,CAAA,8BAAA;AACA,qBAAA,QAAA,CAAA,KAAA,CAAA,eAAA,aAAA,OAAA,IAAA,MAAA,CAAA;;AAhLA;AAAA;AAkLA,qBAAA,WAAA,GAAA,KAAA;AAlLA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAsLA;AACA,IAAA,gBAvLA,8BAuLA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,+BAAA;AACA,WAAA,KAAA,CAAA,UAAA,EAAA,KAAA,YAAA;AACA,WAAA,YAAA;AACA,KA3LA;AA6LA;AACA,IAAA,kBA9LA;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AA+LA,gBAAA,cA/LA,GA+LA,EA/LA;AAgMA,gBAAA,UAhMA,GAgMA,KAAA,gBAAA,CAAA,MAhMA;AAkMA,gBAAA,CAlMA,GAkMA,CAlMA;;AAAA;AAAA,sBAkMA,CAAA,GAAA,KAAA,gBAAA,CAAA,MAlMA;AAAA;AAAA;AAAA;;AAmMA,gBAAA,QAnMA,GAmMA,KAAA,gBAAA,CAAA,CAAA,CAnMA;AAoMA,gBAAA,OAAA,CAAA,GAAA,yEAAA,CAAA,GAAA,CAAA,cAAA,UAAA,eAAA,QAAA,CAAA,YAAA;AApMA;;AAAA,sBAwMA,QAAA,CAAA,MAAA,KAAA,OAxMA;AAAA;AAAA;AAAA;;AAyMA;AACA,gBAAA,gBA1MA,GA0MA,QAAA,CAAA,gBA1MA;AA2MA,gBAAA,cA3MA,GA2MA,gBAAA,KACA,gBAAA,CAAA,YAAA,KAAA,QAAA,CAAA,YAAA,IACA,gBAAA,CAAA,mBAAA,KAAA,QAAA,CAAA,mBADA,IAEA,gBAAA,CAAA,eAAA,KAAA,QAAA,CAAA,eAHA,CA3MA,EAiNA;;AACA,gBAAA,cAlNA,GAkNA,QAAA,CAAA,YAAA,KAAA,IAlNA;AAmNA,gBAAA,UAnNA,GAmNA,cAAA,IAAA,cAnNA;;AAAA,qBAqNA,UArNA;AAAA;AAAA;AAAA;;AAsNA,gBAAA,OAAA,CAAA,GAAA,gIAAA,QAAA,CAAA,YAAA;AACA,gBAAA,OAAA,CAAA,GAAA,oEAAA,cAAA,yCAAA,cAAA;AAEA,gBAAA,OAzNA,GAyNA,gBAAA,CAAA,eAzNA,EAyNA;AAEA;;AA3NA,qBA4NA,cA5NA;AAAA;AAAA;AAAA;;AA6NA,gBAAA,OAAA,CAAA,GAAA,0HAAA,QAAA,CAAA,YAAA,CAAA,IAAA;AA7NA;AAAA,uBA8NA,KAAA,kBAAA,CAAA,QAAA,CAAA,YAAA,EAAA,QAAA,CAAA,YAAA,CA9NA;;AAAA;AA8NA,gBAAA,OA9NA;AA+NA,gBAAA,OAAA,CAAA,GAAA,sFAAA,OAAA;;AA/NA;AAkOA;AACA,gBAAA,UAnOA,GAmOA;AACA,kBAAA,OAAA,EAAA,KAAA,YAAA,CAAA,EADA;AACA;AACA,kBAAA,YAAA,EAAA,QAAA,CAAA,YAFA;AAGA,kBAAA,mBAAA,EAAA,QAAA,CAAA,mBAHA;AAIA,kBAAA,eAAA,EAAA,QAAA,CAAA,eAJA;AAKA,kBAAA,eAAA,EAAA,OALA,CAKA;;AALA,iBAnOA;AA2OA,gBAAA,OAAA,CAAA,GAAA,CAAA,kCAAA,EAAA,UAAA;AACA,gBAAA,OAAA,CAAA,GAAA,CAAA,gCAAA,EAAA,gBAAA;AA5OA;AAAA,uBA6OA,cAAA,CAAA,gBAAA,CAAA,EAAA,EAAA,UAAA,CA7OA;;AAAA;AA6OA,gBAAA,QA7OA;AA8OA,gBAAA,OAAA,CAAA,GAAA,CAAA,+BAAA,EAAA,QAAA;;AA9OA,qBAgPA,QAAA,CAAA,OAhPA;AAAA;AAAA;AAAA;;AAiPA,gBAAA,eAjPA,GAiPA,QAAA,CAAA,MAAA,oCAAA,gBAAA,GAAA,UAAA,CAjPA;AAkPA,gBAAA,cAAA,CAAA,IAAA,CAAA,eAAA,EAlPA,CAoPA;;AACA,qBAAA,gBAAA,CAAA,CAAA,oCACA,QADA;AAEA;AACA,kBAAA,YAAA,EAAA,eAAA,CAAA,YAAA,IAAA,QAAA,CAAA,YAHA;AAIA,kBAAA,mBAAA,EAAA,eAAA,CAAA,mBAAA,IAAA,QAAA,CAAA,mBAJA;AAKA,kBAAA,eAAA,EAAA,eAAA,CAAA,eAAA,IAAA,QAAA,CAAA,eALA;AAMA,kBAAA,gBAAA,EAAA,eANA,CAMA;;AANA;AASA,gBAAA,OAAA,CAAA,GAAA,CAAA,iCAAA,EAAA,KAAA,gBAAA,CAAA,CAAA,CAAA;AAEA,gBAAA,OAAA,CAAA,GAAA,6DAAA,QAAA,CAAA,YAAA;AAhQA;AAAA;;AAAA;AAAA,sBAkQA,IAAA,KAAA,CAAA,QAAA,CAAA,OAAA,IAAA,cAAA,CAlQA;;AAAA;AAAA;AAAA;;AAAA;AAqQA,gBAAA,OAAA,CAAA,GAAA,gIAAA,QAAA,CAAA,YAAA;AACA,gBAAA,cAAA,CAAA,IAAA,CAAA,gBAAA,EAtQA,CAsQA;;AAtQA;AAAA;AAAA;;AAAA;AAyQA;AACA,gBAAA,OAAA,CAAA,GAAA,oHAAA,QAAA,CAAA,YAAA,GAAA,QAAA,CAAA,YAAA,CAAA,IAAA,GAAA,MAAA;AA1QA;AAAA,uBA2QA,KAAA,kBAAA,CAAA,QAAA,CAAA,YAAA,EAAA,QAAA,CAAA,YAAA,CA3QA;;AAAA;AA2QA,gBAAA,QA3QA;AA6QA;AACA,gBAAA,YA9QA,GA8QA;AACA,kBAAA,OAAA,EAAA,KAAA,YAAA,CAAA,EADA;AAEA,kBAAA,YAAA,EAAA,QAAA,CAAA,YAFA;AAGA,kBAAA,mBAAA,EAAA,QAAA,CAAA,mBAHA;AAIA,kBAAA,eAAA,EAAA,QAAA,CAAA,eAJA;AAIA;AACA,kBAAA,eAAA,EAAA;AALA,iBA9QA,EAsRA;;AACA,gBAAA,OAAA,CAAA,GAAA,CAAA,kCAAA,EAAA,YAAA;AAvRA;AAAA,uBAwRA,cAAA,CAAA,YAAA,CAxRA;;AAAA;AAwRA,gBAAA,SAxRA;;AAAA,qBA0RA,SAAA,CAAA,OA1RA;AAAA;AAAA;AAAA;;AA2RA,gBAAA,cAAA,CAAA,IAAA,CAAA,SAAA,CAAA,MAAA;AACA,gBAAA,OAAA,CAAA,GAAA,6DAAA,QAAA,CAAA,YAAA;AA5RA;AAAA;;AAAA;AAAA,sBA8RA,IAAA,KAAA,CAAA,SAAA,CAAA,OAAA,IAAA,cAAA,CA9RA;;AAAA;AAkSA,gBAAA,OAAA,CAAA,GAAA,6DAAA,QAAA,CAAA,YAAA;AAlSA;AAAA;;AAAA;AAAA;AAAA;AAqSA,gBAAA,OAAA,CAAA,KAAA,6DAAA,QAAA,CAAA,YAAA;AArSA,sBAsSA,IAAA,KAAA,+BAAA,QAAA,CAAA,YAAA,yCAAA,aAAA,OAAA,EAtSA;;AAAA;AAkMA,gBAAA,CAAA,EAlMA;AAAA;AAAA;;AAAA;AAAA,kDA0SA,cA1SA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AA6SA;AACA,IAAA,kBA9SA;AAAA,2GA8SA,IA9SA,EA8SA,YA9SA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA,kDA+SA,IAAA,OAAA,CAAA,UAAA,OAAA,EAAA,MAAA,EAAA;AACA,sBAAA,QAAA,GAAA,IAAA,QAAA,EAAA;AACA,kBAAA,QAAA,CAAA,MAAA,CAAA,MAAA,EAAA,IAAA;AACA,kBAAA,QAAA,CAAA,MAAA,CAAA,MAAA,EAAA,GAAA;AACA,kBAAA,QAAA,CAAA,MAAA,CAAA,KAAA,EAAA,EAAA,EAJA,CAIA;AAEA;;AACA,sBAAA,eAAA,GAAA,MAAA,CAAA,wBAAA,CAAA,IAAA,CAAA,IAAA,EAAA,YAAA,CAAA,CAPA,CASA;;;AACA,kBAAA,KAAA,CAAA,MAAA,CAAA,YAAA,EAAA;AACA,oBAAA,MAAA,EAAA,MADA;AAEA,oBAAA,OAAA,EAAA,MAAA,CAAA,aAFA;AAGA,oBAAA,IAAA,EAAA;AAHA,mBAAA,CAAA,CAKA,IALA,CAKA,UAAA,QAAA;AAAA,2BAAA,QAAA,CAAA,IAAA,EAAA;AAAA,mBALA,EAMA,IANA,CAMA,UAAA,MAAA,EAAA;AACA,wBAAA,MAAA,CAAA,OAAA,EAAA;AACA,sBAAA,OAAA,CAAA,GAAA,gFAAA,IAAA,CAAA,IAAA,iBAAA,MAAA,CAAA,OAAA;AACA,sBAAA,OAAA,CAAA,MAAA,CAAA,OAAA,CAAA;AACA,qBAHA,MAGA;AACA,sBAAA,MAAA,CAAA,IAAA,KAAA,CAAA,MAAA,CAAA,OAAA,IAAA,QAAA,CAAA,CAAA;AACA;AACA,mBAbA,EAcA,KAdA,CAcA,UAAA,KAAA,EAAA;AACA,oBAAA,OAAA,CAAA,KAAA,yEAAA,KAAA;AACA,oBAAA,MAAA,CAAA,KAAA,CAAA;AACA,mBAjBA;AAkBA,iBA5BA,CA/SA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AA8UA;AACA,IAAA,eA/UA;AAAA,wGA+UA,SA/UA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAiVA,gBAAA,OAAA,CAAA,GAAA,CAAA,oCAAA,EAAA,SAAA;AAjVA;AAAA,uBAkVA,WAAA,CAAA,SAAA,CAlVA;;AAAA;AAkVA,gBAAA,QAlVA;;AAAA,qBAoVA,QAAA,CAAA,OApVA;AAAA;AAAA;AAAA;;AAqVA,gBAAA,OAAA,CAAA,GAAA,CAAA,8BAAA,EAAA,QAAA,CAAA,MAAA,EArVA,CAuVA;;AACA,qBAAA,0BAAA;AAxVA,kDA0VA,QAAA,CAAA,MA1VA;;AAAA;AAAA,sBA4VA,IAAA,KAAA,CAAA,QAAA,CAAA,OAAA,IAAA,SAAA,CA5VA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;AA+VA,gBAAA,OAAA,CAAA,KAAA,CAAA,+BAAA;AA/VA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAoWA;AACA,IAAA,eArWA;AAAA,wGAqWA,SArWA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAuWA,gBAAA,OAAA,CAAA,GAAA,CAAA,oCAAA,EAAA,SAAA,EAvWA,CAyWA;;AACA,gBAAA,OA1WA,GA0WA,KAAA,KAAA,CAAA,EAAA,IAAA,KAAA,KAAA,CAAA,OA1WA;;AAAA,oBA2WA,OA3WA;AAAA;AAAA;AAAA;;AAAA,sBA4WA,IAAA,KAAA,CAAA,UAAA,CA5WA;;AAAA;AAAA;AAAA,uBA+WA,WAAA,CAAA,OAAA,EAAA,SAAA,CA/WA;;AAAA;AA+WA,gBAAA,QA/WA;;AAAA,qBAiXA,QAAA,CAAA,OAjXA;AAAA;AAAA;AAAA;;AAkXA,gBAAA,OAAA,CAAA,GAAA,CAAA,8BAAA,EAAA,QAAA,CAAA,MAAA,EAlXA,CAoXA;;AACA,qBAAA,0BAAA;AArXA,kDAuXA,QAAA,CAAA,MAvXA;;AAAA;AAAA,sBAyXA,IAAA,KAAA,CAAA,QAAA,CAAA,OAAA,IAAA,SAAA,CAzXA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;AA4XA,gBAAA,OAAA,CAAA,KAAA,CAAA,+BAAA;AA5XA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAiYA;AACA,IAAA,kBAlYA,8BAkYA,WAlYA,EAkYA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,mCAAA,EAAA,WAAA,UAAA,WAAA,GADA,CAGA;;AACA,UAAA,CAAA,WAAA,EAAA;AACA,eAAA,EAAA;AACA,OANA,CAQA;;;AACA,UAAA,KAAA,CAAA,OAAA,CAAA,WAAA,CAAA,EAAA;AACA,QAAA,OAAA,CAAA,GAAA,CAAA,qCAAA,EAAA,WAAA,CAAA,CAAA,CAAA;AACA,eAAA,KAAA,kBAAA,CAAA,WAAA,CAAA,CAAA,CAAA,CAAA,CAFA,CAEA;AACA,OAZA,CAcA;;;AACA,UAAA,QAAA,WAAA,MAAA,QAAA,IAAA,WAAA,CAAA,GAAA,EAAA;AACA,QAAA,OAAA,CAAA,GAAA,CAAA,qCAAA,EAAA,WAAA,CAAA,GAAA;AACA,eAAA,KAAA,kBAAA,CAAA,WAAA,CAAA,GAAA,CAAA,CAFA,CAEA;AACA,OAlBA,CAoBA;;;AACA,UAAA,OAAA,WAAA,KAAA,QAAA,EAAA;AACA,eAAA,KAAA,mBAAA,CAAA,WAAA,CAAA;AACA,OAvBA,CAyBA;;;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,iCAAA,EAAA,MAAA,CAAA,WAAA,CAAA;AACA,aAAA,MAAA,CAAA,WAAA,CAAA;AACA,KA9ZA;AAgaA;AACA,IAAA,mBAjaA,+BAiaA,GAjaA,EAiaA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,sCAAA,EAAA,GAAA,EADA,CAGA;;AACA,UAAA,GAAA,CAAA,QAAA,CAAA,0CAAA,CAAA,EAAA;AACA;AACA,YAAA,KAAA,GAAA,GAAA,CAAA,KAAA,CAAA,gBAAA,CAAA;;AACA,YAAA,KAAA,EAAA;AACA,cAAA,YAAA,GAAA,KAAA,CAAA,CAAA,CAAA;AACA,UAAA,OAAA,CAAA,GAAA,CAAA,sCAAA,EAAA,YAAA;AACA,iBAAA,YAAA;AACA;AACA,OAZA,CAcA;;;AACA,UAAA,GAAA,CAAA,QAAA,CAAA,mBAAA,CAAA,EAAA;AACA,YAAA,MAAA,GAAA,GAAA,CAAA,KAAA,CAAA,gBAAA,CAAA;;AACA,YAAA,MAAA,EAAA;AACA,cAAA,aAAA,GAAA,MAAA,CAAA,CAAA,CAAA;AACA,UAAA,OAAA,CAAA,GAAA,CAAA,sCAAA,EAAA,aAAA;AACA,iBAAA,aAAA;AACA;AACA,OAtBA,CAwBA;;;AACA,UAAA,GAAA,CAAA,UAAA,CAAA,UAAA,CAAA,EAAA;AACA,QAAA,OAAA,CAAA,GAAA,CAAA,+BAAA,EAAA,GAAA;AACA,eAAA,GAAA;AACA,OA5BA,CA8BA;;;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,uCAAA,EAAA,GAAA;AACA,aAAA,GAAA;AACA,KAlcA;AAocA;AACA,IAAA,gBArcA,4BAqcA,UArcA,EAqcA;AACA,UAAA,CAAA,UAAA,EAAA;AACA,eAAA,EAAA;AACA,OAHA,CAKA;;;AACA,UAAA,UAAA,CAAA,UAAA,CAAA,MAAA,CAAA,EAAA;AACA,eAAA,UAAA;AACA,OARA,CAUA;;;AACA,UAAA,UAAA,CAAA,UAAA,CAAA,UAAA,CAAA,EAAA;AACA,YAAA,OAAA,sCAAA,UAAA,CAAA;;AACA,QAAA,OAAA,CAAA,GAAA,CAAA,+BAAA,EAAA,UAAA,EAAA,IAAA,EAAA,OAAA;AACA,eAAA,OAAA;AACA,OAfA,CAiBA;;;AACA,UAAA,MAAA,sCAAA,UAAA,CAAA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,+BAAA,EAAA,UAAA,EAAA,IAAA,EAAA,MAAA;AACA,aAAA,MAAA;AACA,KA1dA;AA4dA;AACA,IAAA,YA7dA;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AA8dA,gBAAA,OAAA,CAAA,GAAA,CAAA,mCAAA,EA9dA,CAgeA;AACA;;AACA,gBAAA,cAleA,GAkeA,KAAA,QAAA,CAAA,WAleA;AAmeA,gBAAA,gBAneA,GAmeA,KAAA,KAAA,CAAA,YAAA,IAAA,KAAA,KAAA,CAAA,YAAA,CAAA,eAAA,EAneA;;AAqeA,oBAAA,CAAA,KAAA,QAAA,CAAA,WAAA,IAAA,gBAAA,EAAA;AACA,kBAAA,OAAA,CAAA,GAAA,CAAA,4CAAA;AACA,uBAAA,QAAA,CAAA,WAAA,GAAA,gBAAA,CAFA,CAEA;AACA,iBAxeA,CA0eA;;;AACA,qBAAA,KAAA,CAAA,IAAA,CAAA,QAAA;AAAA,uFAAA,kBAAA,KAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA,4BAAA,MAAA,CAAA,QAAA,CAAA,WAAA,GAAA,cAAA;;AAFA,iCAIA,KAJA;AAAA;AAAA;AAAA;;AAKA,4BAAA,OAAA,CAAA,GAAA,CAAA,uCAAA;AALA;AAAA;AAAA,mCASA,MAAA,CAAA,mBAAA,EATA;;AAAA;AAWA;AACA,4BAAA,UAZA,GAYA;AACA,8BAAA,SAAA,EAAA,MAAA,CAAA,QAAA,CAAA,SAAA,CAAA,IAAA,EADA;AAEA,8BAAA,gBAAA,EAAA,MAAA,CAAA,QAAA,CAAA,gBAAA,CAAA,IAAA,EAFA;AAGA,8BAAA,WAAA,EAAA,MAAA,CAAA,kBAAA,CAAA,MAAA,CAAA,QAAA,CAAA,WAAA,CAHA;AAIA,8BAAA,cAAA,EAAA,MAAA,CAAA,QAAA,CAAA,cAAA,CAAA,IAAA,EAJA;AAKA,8BAAA,KAAA,EAAA,MAAA,CAAA,QAAA,CAAA;AALA,6BAZA;AAoBA,4BAAA,OAAA,CAAA,GAAA,CAAA,mCAAA,EAAA,UAAA;;AACA,4BAAA,MAAA,CAAA,KAAA,CAAA,QAAA,EAAA,UAAA;;AArBA;AAAA;;AAAA;AAAA;AAAA;AAwBA,4BAAA,OAAA,CAAA,KAAA,CAAA,8BAAA;;AACA,4BAAA,MAAA,CAAA,QAAA,CAAA,KAAA,CAAA,cAAA,aAAA,OAAA,IAAA,MAAA,CAAA;;AAzBA;AAAA;AAAA;;AAAA;AA4BA,4BAAA,OAAA,CAAA,GAAA,CAAA,mCAAA;;AACA,4BAAA,MAAA,CAAA,QAAA,CAAA,KAAA,CAAA,SAAA;;AA7BA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,mBAAA;;AAAA;AAAA;AAAA;AAAA;;AA3eA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AA6gBA;AACA,IAAA,YA9gBA,0BA8gBA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,qCAAA,EADA,CAGA;;AACA,WAAA,eAAA,GAJA,CAMA;;AACA,WAAA,oBAAA,GAPA,CASA;;AACA,WAAA,SAAA;AAEA,MAAA,OAAA,CAAA,GAAA,CAAA,gCAAA;AACA,WAAA,KAAA,CAAA,OAAA;AACA,KA5hBA;AA8hBA;AACA,IAAA,mBA/hBA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAgiBA,gBAAA,OAAA,CAAA,GAAA,CAAA,oCAAA;;AAhiBA,sBAkiBA,KAAA,KAAA,CAAA,YAAA,IAAA,KAAA,KAAA,CAAA,YAAA,CAAA,eAAA,EAliBA;AAAA;AAAA;AAAA;;AAmiBA,gBAAA,OAAA,CAAA,GAAA,CAAA,uCAAA;AAniBA;AAAA;AAAA,uBAuiBA,KAAA,KAAA,CAAA,YAAA,CAAA,aAAA,EAviBA;;AAAA;AAuiBA,gBAAA,QAviBA;AAwiBA,gBAAA,OAAA,CAAA,GAAA,CAAA,kCAAA,EAAA,QAAA,EAxiBA,CA0iBA;;AACA,qBAAA,QAAA,CAAA,WAAA,GAAA,QAAA;AA3iBA,mDA6iBA,QA7iBA;;AAAA;AAAA;AAAA;AA+iBA,gBAAA,OAAA,CAAA,KAAA,CAAA,8BAAA;AA/iBA,sBAgjBA,IAAA,KAAA,CAAA,cAAA,cAAA,OAAA,IAAA,MAAA,CAAA,CAhjBA;;AAAA;AAAA;AAAA;;AAAA;AAmjBA,gBAAA,OAAA,CAAA,GAAA,CAAA,+BAAA;AAnjBA,mDAojBA,KAAA,QAAA,CAAA,WApjBA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAwjBA;AACA,IAAA,0BAzjBA,wCAyjBA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,wCAAA;;AAEA,UAAA,KAAA,KAAA,CAAA,YAAA,IAAA,KAAA,KAAA,CAAA,YAAA,CAAA,0BAAA,EAAA;AACA,aAAA,KAAA,CAAA,YAAA,CAAA,0BAAA;AACA,QAAA,OAAA,CAAA,GAAA,CAAA,mCAAA;AACA;AACA,KAhkBA;AAkkBA;AACA,IAAA,eAnkBA,6BAmkBA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,gCAAA;;AAEA,UAAA,KAAA,KAAA,CAAA,YAAA,IAAA,KAAA,KAAA,CAAA,YAAA,CAAA,eAAA,EAAA;AACA,aAAA,KAAA,CAAA,YAAA,CAAA,eAAA;AACA,QAAA,OAAA,CAAA,GAAA,CAAA,+BAAA;AACA;AACA,KA1kBA;AA4kBA;AACA,IAAA,gBA7kBA;AAAA,0GA6kBA,OA7kBA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,oBA8kBA,OA9kBA;AAAA;AAAA;AAAA;;AAAA;;AAAA;AAglBA,qBAAA,eAAA,GAAA,IAAA;AAhlBA;AAklBA,gBAAA,OAAA,CAAA,GAAA,CAAA,iCAAA,EAAA,OAAA,EAllBA,CAolBA;;AAplBA;AAAA,uBAqlBA,eAAA,CAAA,OAAA,CArlBA;;AAAA;AAqlBA,gBAAA,QArlBA;;AAulBA,oBAAA,QAAA,CAAA,OAAA,EAAA;AACA,uBAAA,YAAA,GAAA,QAAA,CAAA,MAAA,IAAA,EAAA;AACA,kBAAA,OAAA,CAAA,GAAA,CAAA,kCAAA,EAAA,KAAA,YAAA,EAFA,CAIA;;AACA,sBAAA,KAAA,IAAA,KAAA,MAAA,IAAA,KAAA,YAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,yBAAA,0BAAA,CAAA,KAAA,YAAA;AACA,oBAAA,OAAA,CAAA,GAAA,CAAA,2CAAA;AACA,mBAHA,MAGA,IAAA,KAAA,IAAA,KAAA,MAAA,EAAA;AACA,oBAAA,OAAA,CAAA,GAAA,CAAA,yCAAA;AACA;AACA,iBAXA,MAWA;AACA,kBAAA,OAAA,CAAA,KAAA,CAAA,+BAAA,EAAA,QAAA,CAAA,OAAA;AACA,uBAAA,QAAA,CAAA,KAAA,CAAA,QAAA,CAAA,OAAA,IAAA,WAAA;AACA,uBAAA,YAAA,GAAA,EAAA;AACA;;AAEA,gBAAA,OAAA,CAAA,GAAA,CAAA,kCAAA,EAAA,KAAA,YAAA,CAAA,MAAA,EAAA,MAAA;AAxmBA;AAAA;;AAAA;AAAA;AAAA;AA0mBA,gBAAA,OAAA,CAAA,KAAA,CAAA,iCAAA;AACA,qBAAA,QAAA,CAAA,KAAA,CAAA,iBAAA,cAAA,OAAA,IAAA,MAAA,CAAA;AACA,qBAAA,YAAA,GAAA,EAAA;;AA5mBA;AAAA;AA8mBA,qBAAA,eAAA,GAAA,KAAA;AA9mBA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAknBA;AACA,IAAA,0BAnnBA,sCAmnBA,SAnnBA,EAmnBA;AAAA;;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,qCAAA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,+BAAA,EAAA,SAAA,EAFA,CAIA;;AACA,WAAA,gBAAA,GAAA,EAAA,CALA,CAOA;;AACA,MAAA,SAAA,CAAA,OAAA,CAAA,UAAA,QAAA,EAAA,KAAA,EAAA;AACA,QAAA,OAAA,CAAA,GAAA,yEAAA,KAAA,GAAA,CAAA,QAAA;AACA,UAAA,EAAA,EAAA,QAAA,CAAA,EADA;AAEA,UAAA,YAAA,EAAA,QAAA,CAAA,YAFA;AAGA,UAAA,mBAAA,EAAA,QAAA,CAAA,mBAHA;AAIA,UAAA,eAAA,EAAA,QAAA,CAAA,eAJA;AAKA,UAAA,eAAA,EAAA,QAAA,CAAA,eALA;AAMA,UAAA,UAAA,EAAA,QAAA,CAAA;AANA,SAAA,EADA,CAUA;;AACA,YAAA,eAAA,GAAA,QAAA,CAAA,eAAA,IAAA,EAAA;AACA,YAAA,eAAA,GAAA,eAAA,GACA,eAAA,CAAA,QAAA,CAAA,GAAA,IAAA,eAAA,CAAA,KAAA,CAAA,GAAA,EAAA,GAAA,EAAA,GAAA,eADA,GAEA,OAFA;AAIA,YAAA,YAAA,GAAA;AACA,UAAA,EAAA,EAAA,QAAA,CAAA,EAAA,IAAA,IAAA,CAAA,GAAA,KAAA,KADA;AAEA,UAAA,YAAA,EAAA,QAAA,CAAA,YAAA,IAAA,EAFA;AAGA,UAAA,mBAAA,EAAA,QAAA,CAAA,mBAAA,IAAA,EAHA;AAIA,UAAA,eAAA,EAAA,QAAA,CAAA,eAAA,IAAA,EAJA;AAIA;AACA,UAAA,YAAA,EAAA,IALA;AAKA;AACA,UAAA,QAAA,EAAA,eANA;AAMA;AACA,UAAA,QAAA,EAAA,CAPA;AAOA;AACA,UAAA,eAAA,EAAA,QAAA,CAAA,eAAA,IAAA,EARA;AAQA;AACA,UAAA,MAAA,EAAA,OATA;AASA;AACA,UAAA,UAAA,EAAA,IAAA,IAAA,CAAA,QAAA,CAAA,UAAA,IAAA,IAAA,CAAA,GAAA,EAAA,CAVA;AAWA,UAAA,gBAAA,EAAA,QAXA,CAWA;;AAXA,SAAA;;AAcA,QAAA,MAAA,CAAA,gBAAA,CAAA,IAAA,CAAA,YAAA;;AACA,QAAA,OAAA,CAAA,GAAA,+DAAA,QAAA,CAAA,YAAA,2DAAA,YAAA;AACA,OAhCA;AAkCA,MAAA,OAAA,CAAA,GAAA,6DAAA,SAAA,CAAA,MAAA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,8BAAA,EAAA,KAAA,gBAAA;AACA,KA/pBA;AAiqBA;AACA,IAAA,oBAlqBA,gCAkqBA,IAlqBA,EAkqBA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,mCAAA,EAAA,IAAA,CAAA,IAAA,EADA,CAGA;;AACA,UAAA,CAAA,KAAA,gBAAA,CAAA,IAAA,CAAA,IAAA,CAAA,EAAA;AACA,aAAA,QAAA,CAAA,KAAA,CAAA,qBAAA;AACA,eAAA,KAAA;AACA,OAPA,CASA;;;AACA,UAAA,QAAA,GAAA,IAAA,CAAA,IAAA,CAAA,WAAA,EAAA;;AACA,UAAA,CAAA,QAAA,CAAA,QAAA,CAAA,MAAA,CAAA,EAAA;AACA,aAAA,QAAA,CAAA,KAAA,CAAA,kBAAA;AACA,eAAA,KAAA;AACA,OAdA,CAgBA;;;AACA,UAAA,gBAAA,GAAA,CACA,iBADA,EAEA,8BAFA,EAGA,mBAHA,CAAA;;AAKA,UAAA,CAAA,gBAAA,CAAA,QAAA,CAAA,IAAA,CAAA,IAAA,CAAA,EAAA;AACA,aAAA,QAAA,CAAA,KAAA,CAAA,sBAAA;AACA,eAAA,KAAA;AACA,OAzBA,CA2BA;;;AACA,UAAA,OAAA,GAAA,IAAA,IAAA,GAAA,IAAA,CA5BA,CA4BA;;AACA,UAAA,IAAA,CAAA,IAAA,GAAA,OAAA,EAAA;AACA,aAAA,QAAA,CAAA,KAAA,CAAA,eAAA;AACA,eAAA,KAAA;AACA,OAhCA,CAkCA;;;AACA,UAAA,IAAA,CAAA,IAAA,KAAA,CAAA,EAAA;AACA,aAAA,QAAA,CAAA,KAAA,CAAA,SAAA;AACA,eAAA,KAAA;AACA;;AAEA,MAAA,OAAA,CAAA,GAAA,CAAA,kCAAA;AACA,aAAA,KAAA,CAzCA,CAyCA;AACA,KA5sBA;AA8sBA;AACA,IAAA,gBA/sBA,4BA+sBA,QA/sBA,EA+sBA;AACA;AACA,UAAA,cAAA,GAAA,oBAAA;;AACA,UAAA,cAAA,CAAA,IAAA,CAAA,QAAA,CAAA,EAAA;AACA,eAAA,KAAA;AACA,OALA,CAOA;;;AACA,UAAA,QAAA,CAAA,QAAA,CAAA,IAAA,KAAA,QAAA,CAAA,QAAA,CAAA,IAAA,CAAA,IAAA,QAAA,CAAA,QAAA,CAAA,KAAA,CAAA,EAAA;AACA,eAAA,KAAA;AACA,OAVA,CAYA;;;AACA,UAAA,aAAA,GAAA,6CAAA;;AACA,UAAA,aAAA,CAAA,IAAA,CAAA,QAAA,CAAA,EAAA;AACA,eAAA,KAAA;AACA,OAhBA,CAkBA;;;AACA,UAAA,QAAA,CAAA,MAAA,GAAA,GAAA,EAAA;AACA,eAAA,KAAA;AACA;;AAEA,aAAA,IAAA;AACA,KAvuBA;AAyuBA;AACA,IAAA,oBA1uBA,kCA0uBA;AAAA,kCACA,KAAA,gBADA;AAAA,UACA,YADA,yBACA,YADA;AAAA,UACA,mBADA,yBACA,mBADA;AAAA,UACA,eADA,yBACA,eADA,EAGA;;AACA,UAAA,CAAA,YAAA,IAAA,YAAA,CAAA,IAAA,GAAA,MAAA,KAAA,CAAA,EAAA;AACA,aAAA,QAAA,CAAA,KAAA,CAAA,WAAA;AACA,eAAA,KAAA;AACA,OAPA,CASA;;;AACA,UAAA,WAAA,GAAA,kCAAA;;AACA,UAAA,CAAA,WAAA,CAAA,IAAA,CAAA,YAAA,CAAA,EAAA;AACA,aAAA,QAAA,CAAA,KAAA,CAAA,eAAA;AACA,eAAA,KAAA;AACA,OAdA,CAgBA;;;AACA,UAAA,CAAA,mBAAA,IAAA,mBAAA,CAAA,IAAA,GAAA,MAAA,KAAA,CAAA,EAAA;AACA,aAAA,QAAA,CAAA,KAAA,CAAA,WAAA;AACA,eAAA,KAAA;AACA,OApBA,CAsBA;;;AACA,UAAA,mBAAA,CAAA,MAAA,GAAA,EAAA,IAAA,mBAAA,CAAA,MAAA,GAAA,GAAA,EAAA;AACA,aAAA,QAAA,CAAA,KAAA,CAAA,sBAAA;AACA,eAAA,KAAA;AACA,OA1BA,CA4BA;;;AACA,UAAA,aAAA,GAAA,gCAAA;;AACA,UAAA,aAAA,CAAA,IAAA,CAAA,YAAA,KAAA,aAAA,CAAA,IAAA,CAAA,mBAAA,CAAA,IAAA,aAAA,CAAA,IAAA,CAAA,eAAA,CAAA,EAAA;AACA,aAAA,QAAA,CAAA,KAAA,CAAA,gBAAA;AACA,eAAA,KAAA;AACA;;AAEA,aAAA,IAAA;AACA,KA9wBA;AAgxBA;AACA,IAAA,wBAjxBA,oCAixBA,IAjxBA,EAixBA;AAAA;;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,+BAAA,EAAA,IAAA,CAAA,IAAA,GAAA,IAAA,CAAA,IAAA,CAAA,IAAA,GAAA,SAAA,EAAA,SAAA,EAAA,IAAA,CAAA,IAAA,GAAA,IAAA,CAAA,IAAA,CAAA,MAAA,GAAA,WAAA,EADA,CAGA;;AACA,UAAA,IAAA,CAAA,QAAA,IAAA,IAAA,CAAA,QAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,YAAA,IAAA,GAAA,IAAA,CAAA,QAAA,CAAA,CAAA,EAAA,aAAA,IAAA,IAAA,CAAA,QAAA,CAAA,CAAA,CAAA,CADA,CAGA;;AACA,YAAA,OAAA,GAAA,IAAA,IAAA,GAAA,IAAA,CAJA,CAIA;;AACA,YAAA,IAAA,CAAA,IAAA,GAAA,OAAA,EAAA;AACA,UAAA,OAAA,CAAA,KAAA,CAAA,gCAAA,EAAA,IAAA,CAAA,IAAA,EAAA,IAAA;AACA,eAAA,QAAA,CAAA,KAAA,CAAA,eAAA,EAFA,CAIA;;AACA,eAAA,gBAAA,GAAA,EAAA;AACA,eAAA,gBAAA,GAAA,IAAA,CANA,CAQA;;AACA,eAAA,SAAA,CAAA,YAAA;AACA;AACA,gBAAA,eAAA,GAAA,MAAA,CAAA,KAAA,CAAA,cAAA;;AACA,gBAAA,eAAA,EAAA;AACA,cAAA,eAAA,CAAA,QAAA,GAAA,EAAA;AACA;AACA,WANA;AAQA,UAAA,OAAA,CAAA,GAAA,CAAA,oCAAA;AACA,iBAlBA,CAkBA;AACA,SAxBA,CA0BA;;;AACA,YAAA,IAAA,CAAA,IAAA,KAAA,CAAA,EAAA;AACA,UAAA,OAAA,CAAA,KAAA,CAAA,6BAAA;AACA,eAAA,QAAA,CAAA,KAAA,CAAA,SAAA,EAFA,CAIA;;AACA,eAAA,gBAAA,GAAA,EAAA;AACA,eAAA,gBAAA,GAAA,IAAA;AACA;AACA,SAnCA,CAqCA;;;AACA,YAAA,CAAA,KAAA,gBAAA,CAAA,IAAA,CAAA,IAAA,CAAA,EAAA;AACA,UAAA,OAAA,CAAA,KAAA,CAAA,8BAAA,EAAA,IAAA,CAAA,IAAA;AACA,eAAA,QAAA,CAAA,KAAA,CAAA,qBAAA,EAFA,CAIA;;AACA,eAAA,gBAAA,GAAA,EAAA;AACA,eAAA,gBAAA,GAAA,IAAA;AACA;AACA,SA9CA,CAgDA;;;AACA,YAAA,QAAA,GAAA,IAAA,CAAA,IAAA,CAAA,WAAA,EAAA;;AACA,YAAA,CAAA,QAAA,CAAA,QAAA,CAAA,MAAA,CAAA,EAAA;AACA,UAAA,OAAA,CAAA,KAAA,CAAA,gCAAA,EAAA,IAAA,CAAA,IAAA;AACA,eAAA,QAAA,CAAA,KAAA,CAAA,kBAAA,EAFA,CAIA;;AACA,eAAA,gBAAA,GAAA,EAAA;AACA,eAAA,gBAAA,GAAA,IAAA;AACA;AACA,SA1DA,CA4DA;;;AACA,YAAA,gBAAA,GAAA,CACA,iBADA,EAEA,8BAFA,EAGA,mBAHA,CAAA;;AAKA,YAAA,CAAA,gBAAA,CAAA,QAAA,CAAA,IAAA,CAAA,IAAA,CAAA,EAAA;AACA,UAAA,OAAA,CAAA,KAAA,CAAA,mCAAA,EAAA,IAAA,CAAA,IAAA;AACA,eAAA,QAAA,CAAA,KAAA,CAAA,sBAAA,EAFA,CAIA;;AACA,eAAA,gBAAA,GAAA,EAAA;AACA,eAAA,gBAAA,GAAA,IAAA;AACA;AACA,SA1EA,CA4EA;;;AACA,aAAA,gBAAA,GAAA,CAAA,IAAA,CAAA;AACA,aAAA,gBAAA,GAAA;AACA,UAAA,IAAA,EAAA,IAAA,CAAA,IADA;AAEA,UAAA,YAAA,EAAA,IAAA,CAAA,IAFA;AAGA,UAAA,IAAA,EAAA,IAAA,CAAA,IAHA;AAIA,UAAA,IAAA,EAAA,IAJA;AAIA;AACA,UAAA,OAAA,EAAA,KALA,CAKA;;AALA,SAAA;AAQA,QAAA,OAAA,CAAA,GAAA,CAAA,uCAAA,EAAA,IAAA,CAAA,IAAA;AACA,aAAA,QAAA,CAAA,OAAA,CAAA,oBAAA;AAEA,OAzFA,MAyFA,IAAA,IAAA,CAAA,QAAA,IAAA,IAAA,CAAA,QAAA,CAAA,MAAA,KAAA,CAAA,EAAA;AACA;AACA,aAAA,gBAAA,GAAA,EAAA;AACA,aAAA,gBAAA,GAAA,IAAA;AACA,QAAA,OAAA,CAAA,GAAA,CAAA,+BAAA;AACA;AACA,KAp3BA;AAs3BA;AACA,IAAA,wBAv3BA,oCAu3BA,YAv3BA,EAu3BA;AAAA,UAAA,kBAAA,uEAAA,IAAA;AACA;AACA,UAAA,YAAA,GAAA,YAAA,CAAA,WAAA,CAAA,GAAA,CAAA;AACA,UAAA,SAAA,GAAA,YAAA,GAAA,CAAA,CAAA,GAAA,YAAA,CAAA,SAAA,CAAA,YAAA,CAAA,GAAA,EAAA,CAHA,CAKA;;AACA,UAAA,SAAA,GAAA,IAAA,IAAA,GAAA,OAAA,EAAA,CANA,CAQA;;AACA,UAAA,MAAA,GAAA,IAAA,CAAA,KAAA,CAAA,IAAA,CAAA,MAAA,KAAA,KAAA,EAAA,QAAA,GAAA,QAAA,CAAA,CAAA,EAAA,GAAA,CAAA,CATA,CAWA;;AACA,UAAA,SAAA,GAAA,KAAA,QAAA,CAAA,SAAA,GACA,KAAA,QAAA,CAAA,SAAA,CAAA,OAAA,CAAA,4BAAA,EAAA,GAAA,EAAA,SAAA,CAAA,CAAA,EAAA,EAAA,CADA,GAEA,UAFA,CAZA,CAgBA;;AACA,UAAA,YAAA,GAAA,kBAAA,IAAA,KAAA,gBAAA,CAAA,YAAA,GACA,CAAA,kBAAA,IAAA,KAAA,gBAAA,CAAA,YAAA,EAAA,OAAA,CAAA,4BAAA,EAAA,GAAA,EAAA,SAAA,CAAA,CAAA,EAAA,EAAA,CADA,GAEA,SAFA,CAjBA,CAqBA;;AACA,UAAA,WAAA,aAAA,SAAA,cAAA,YAAA,cAAA,SAAA,cAAA,MAAA,SAAA,SAAA,CAAA;AAEA,MAAA,OAAA,CAAA,GAAA,CAAA,6BAAA,EAAA,YAAA,EAAA,IAAA,EAAA,WAAA;AACA,aAAA,WAAA;AACA,KAj5BA;AAm5BA;AACA,IAAA,oBAp5BA,gCAo5BA,IAp5BA,EAo5BA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,+BAAA,EAAA,IAAA,CAAA,IAAA;AACA,WAAA,gBAAA,GAAA,EAAA;AACA,WAAA,gBAAA,CAAA,eAAA,GAAA,EAAA;AACA,WAAA,gBAAA,GAAA,IAAA;AACA,KAz5BA;AA25BA;AACA,IAAA,wBA55BA,sCA45BA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,8BAAA;AACA,WAAA,gBAAA,GAAA,EAAA;AACA,WAAA,gBAAA,CAAA,eAAA,GAAA,EAAA;AACA,WAAA,gBAAA,GAAA,IAAA;AACA,WAAA,QAAA,CAAA,OAAA,CAAA,UAAA;AACA,KAl6BA;AAo6BA;AACA,IAAA,cAr6BA,0BAq6BA,KAr6BA,EAq6BA;AACA,UAAA,CAAA,KAAA,EAAA,OAAA,KAAA;AACA,UAAA,CAAA,GAAA,IAAA;AACA,UAAA,KAAA,GAAA,CAAA,GAAA,EAAA,IAAA,EAAA,IAAA,EAAA,IAAA,CAAA;AACA,UAAA,CAAA,GAAA,IAAA,CAAA,KAAA,CAAA,IAAA,CAAA,GAAA,CAAA,KAAA,IAAA,IAAA,CAAA,GAAA,CAAA,CAAA,CAAA,CAAA;AACA,aAAA,UAAA,CAAA,CAAA,KAAA,GAAA,IAAA,CAAA,GAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,OAAA,CAAA,CAAA,CAAA,CAAA,GAAA,GAAA,GAAA,KAAA,CAAA,CAAA,CAAA;AACA,KA36BA;AA66BA;AACA,IAAA,oBA96BA,kCA86BA;AAAA;;AACA,aAAA,IAAA,OAAA,CAAA,UAAA,OAAA,EAAA,MAAA,EAAA;AACA;AACA,QAAA,MAAA,CAAA,KAAA,CAAA,eAAA,CAAA,QAAA,CAAA,UAAA,KAAA,EAAA;AACA,cAAA,CAAA,KAAA,EAAA;AACA,YAAA,MAAA,CAAA,QAAA,CAAA,KAAA,CAAA,SAAA;;AACA,YAAA,MAAA,CAAA,IAAA,KAAA,CAAA,QAAA,CAAA,CAAA;AACA;AACA;;AAEA,UAAA,MAAA,CAAA,qBAAA,GAAA,IAAA,CAAA,OAAA,EAAA,KAAA,CAAA,MAAA;AACA,SARA;AASA,OAXA,CAAA;AAYA,KA37BA;AA67BA;AACA,IAAA,qBA97BA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA,sBAi8BA,KAAA,gBAAA,CAAA,MAAA,KAAA,CAj8BA;AAAA;AAAA;AAAA;;AAk8BA,qBAAA,QAAA,CAAA,KAAA,CAAA,UAAA;AAl8BA,sBAm8BA,IAAA,KAAA,CAAA,UAAA,CAn8BA;;AAAA;AAs8BA,gBAAA,IAt8BA,GAs8BA,KAAA,gBAAA,CAAA,CAAA,CAt8BA,EAw8BA;;AAx8BA,oBAy8BA,KAAA,gBAAA,CAAA,IAAA,CAAA,IAAA,CAz8BA;AAAA;AAAA;AAAA;;AA08BA,qBAAA,QAAA,CAAA,KAAA,CAAA,gBAAA;AA18BA,sBA28BA,IAAA,KAAA,CAAA,QAAA,CA38BA;;AAAA;AAAA,oBA88BA,IAAA,CAAA,IAAA,CAAA,WAAA,GAAA,QAAA,CAAA,MAAA,CA98BA;AAAA;AAAA;AAAA;;AA+8BA,qBAAA,QAAA,CAAA,KAAA,CAAA,cAAA;AA/8BA,sBAg9BA,IAAA,KAAA,CAAA,SAAA,CAh9BA;;AAAA;AAAA,sBAm9BA,IAAA,CAAA,IAAA,GAAA,IAAA,IAAA,GAAA,IAn9BA;AAAA;AAAA;AAAA;;AAo9BA,qBAAA,QAAA,CAAA,KAAA,CAAA,aAAA;AAp9BA,sBAq9BA,IAAA,KAAA,CAAA,QAAA,CAr9BA;;AAAA;AAAA,oBAy9BA,KAAA,oBAAA,EAz9BA;AAAA;AAAA;AAAA;;AAAA,sBA09BA,IAAA,KAAA,CAAA,UAAA,CA19BA;;AAAA;AA69BA,qBAAA,WAAA,GAAA,IAAA;AACA,gBAAA,OAAA,CAAA,GAAA,CAAA,+BAAA,EAAA,KAAA,gBAAA,EA99BA,CAg+BA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;;AAz+BA;AAAA,uBA0+BA,IAAA,OAAA,CAAA,UAAA,OAAA;AAAA,yBAAA,UAAA,CAAA,OAAA,EAAA,IAAA,CAAA;AAAA,iBAAA,CA1+BA;;AAAA;AA4+BA,qBAAA,QAAA,CAAA,OAAA,CAAA,UAAA,EA5+BA,CA8+BA;;AACA,qBAAA,gBAAA,CAAA,KAAA,YAAA,CAAA,EAAA,EA/+BA,CAi/BA;;AACA,qBAAA,iBAAA;AAl/BA,mDAo/BA,IAp/BA;;AAAA;AAAA;AAAA;AAs/BA,gBAAA,OAAA,CAAA,KAAA,CAAA,+BAAA;AACA,qBAAA,QAAA,CAAA,KAAA,CAAA,eAAA,cAAA,OAAA,IAAA,MAAA,CAAA;AAv/BA;;AAAA;AAAA;AA0/BA,qBAAA,WAAA,GAAA,KAAA;AA1/BA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AA8/BA;AACA,IAAA,iBA//BA,+BA+/BA;AAAA;;AACA,WAAA,gBAAA,GAAA;AACA,QAAA,YAAA,EAAA,EADA;AAEA,QAAA,mBAAA,EAAA,EAFA;AAGA,QAAA,eAAA,EAAA,EAHA;AAIA,QAAA,eAAA,EAAA;AAJA,OAAA;AAMA,WAAA,gBAAA,GAAA,EAAA;AACA,WAAA,gBAAA,GAAA,IAAA;AACA,WAAA,oBAAA,GAAA,CAAA,CAAA,CATA,CAWA;;AACA,WAAA,SAAA,CAAA,YAAA;AACA,YAAA,OAAA,CAAA,KAAA,CAAA,eAAA,EAAA;AACA,UAAA,OAAA,CAAA,KAAA,CAAA,eAAA,CAAA,WAAA;AACA;AACA,OAJA;AAMA,MAAA,OAAA,CAAA,GAAA,CAAA,+BAAA;AACA,KAlhCA;AAohCA;AACA,IAAA,oBArhCA,kCAqhCA;AACA;AACA,WAAA,gBAAA,GAAA,EAAA,CAFA,CAIA;;AACA,WAAA,iBAAA,GALA,CAOA;;AACA,WAAA,gBAAA,GAAA,EAAA;AACA,WAAA,gBAAA,GAAA,IAAA;AACA,WAAA,oBAAA,GAAA,CAAA,CAAA,CAVA,CAYA;;AACA,WAAA,wBAAA,GAAA,EAAA,CAbA,CAeA;;AACA,WAAA,iBAAA,GAAA,KAAA,CAhBA,CAkBA;;AACA,WAAA,YAAA,GAAA,EAAA;AACA,WAAA,eAAA,GAAA,KAAA;AAEA,MAAA,OAAA,CAAA,GAAA,CAAA,iCAAA;AACA,KA5iCA;AA8iCA;AACA,IAAA,qBA/iCA,mCA+iCA;AAAA;;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,oCAAA;AACA,WAAA,SAAA,CAAA,YAAA;AACA,YAAA,OAAA,CAAA,KAAA,CAAA,eAAA,EAAA;AACA,UAAA,OAAA,CAAA,KAAA,CAAA,eAAA,CAAA,aAAA,CAAA,iBAAA,EAAA,UAAA,YAAA,EAAA;AACA,gBAAA,YAAA,EAAA;AACA,cAAA,OAAA,CAAA,GAAA,CAAA,kCAAA,EAAA,YAAA;AACA,aAFA,MAEA;AACA,cAAA,OAAA,CAAA,GAAA,CAAA,iCAAA;AACA;AACA,WANA;AAOA;AACA,OAVA;AAWA,KA5jCA;AA8jCA;AACA,IAAA,WA/jCA,yBA+jCA;AACA,WAAA,SAAA,CAAA,YAAA;AACA,QAAA,OAAA,CAAA,GAAA,CAAA,kCAAA,EADA,CAGA;;AACA,YAAA,WAAA,GAAA,QAAA,CAAA,aAAA,CAAA,eAAA,CAAA;;AAEA,YAAA,WAAA,EAAA;AACA,UAAA,OAAA,CAAA,GAAA,CAAA,gCAAA,EAAA;AACA,YAAA,SAAA,EAAA,WAAA,CAAA,SADA;AAEA,YAAA,YAAA,EAAA,WAAA,CAAA,YAFA;AAGA,YAAA,YAAA,EAAA,WAAA,CAAA;AAHA,WAAA;AAMA,cAAA,eAAA,GAAA,WAAA,CAAA,SAAA;AACA,UAAA,WAAA,CAAA,SAAA,GAAA,CAAA;AACA,cAAA,cAAA,GAAA,WAAA,CAAA,SAAA;AAEA,UAAA,OAAA,CAAA,GAAA,qGAAA,eAAA,mCAAA,cAAA;;AAEA,cAAA,eAAA,KAAA,cAAA,IAAA,eAAA,KAAA,CAAA,EAAA;AACA,YAAA,OAAA,CAAA,GAAA,CAAA,+BAAA;AACA;AACA;AACA,SAvBA,CAyBA;;;AACA,YAAA,SAAA,GAAA,CACA,sCADA,EAEA,iBAFA,EAGA,yCAHA,EAIA,oBAJA,EAKA,sBALA,EAMA,iBANA,CAAA;AASA,YAAA,eAAA,GAAA,IAAA;AACA,YAAA,YAAA,GAAA,EAAA;;AAEA,sCAAA,SAAA,gCAAA;AAAA,cAAA,QAAA,iBAAA;AACA,cAAA,OAAA,GAAA,QAAA,CAAA,aAAA,CAAA,QAAA,CAAA;;AACA,cAAA,OAAA,EAAA;AACA,YAAA,OAAA,CAAA,GAAA,mEAAA,QAAA,QAAA,OAAA;AACA,YAAA,OAAA,CAAA,GAAA,8EAAA,OAAA,CAAA,SAAA,6BAAA,OAAA,CAAA,YAAA,6BAAA,OAAA,CAAA,YAAA,GAFA,CAIA;;AACA,gBAAA,OAAA,CAAA,YAAA,GAAA,OAAA,CAAA,YAAA,IAAA,OAAA,CAAA,SAAA,GAAA,CAAA,EAAA;AACA,cAAA,eAAA,GAAA,OAAA;AACA,cAAA,YAAA,GAAA,QAAA;AACA;AACA;AACA;AACA;;AAEA,YAAA,eAAA,EAAA;AACA,cAAA,gBAAA,GAAA,eAAA,CAAA,SAAA;AACA,UAAA,eAAA,CAAA,SAAA,GAAA,CAAA;AACA,cAAA,eAAA,GAAA,eAAA,CAAA,SAAA;AAEA,UAAA,OAAA,CAAA,GAAA,yEAAA,YAAA;AACA,UAAA,OAAA,CAAA,GAAA,8DAAA,gBAAA,mCAAA,eAAA;;AAEA,cAAA,gBAAA,KAAA,eAAA,IAAA,gBAAA,GAAA,CAAA,EAAA;AACA,YAAA,OAAA,CAAA,IAAA,CAAA,sCAAA,EADA,CAEA;;AACA,YAAA,eAAA,CAAA,QAAA,CAAA;AAAA,cAAA,GAAA,EAAA,CAAA;AAAA,cAAA,QAAA,EAAA;AAAA,aAAA;AACA;AACA,SAbA,MAaA;AACA,UAAA,OAAA,CAAA,IAAA,CAAA,qCAAA,EADA,CAEA;;AACA,UAAA,UAAA,CAAA,YAAA;AACA,gBAAA,gBAAA,GAAA,QAAA,CAAA,gBAAA,CAAA,kBAAA,CAAA;AACA,YAAA,OAAA,CAAA,GAAA,CAAA,mCAAA,EAAA,gBAAA;;AAFA,uDAIA,gBAJA;AAAA;;AAAA;AAIA,kEAAA;AAAA,oBAAA,QAAA;;AACA,oBAAA,QAAA,CAAA,YAAA,GAAA,QAAA,CAAA,YAAA,IAAA,QAAA,CAAA,SAAA,GAAA,CAAA,EAAA;AACA,kBAAA,QAAA,CAAA,SAAA,GAAA,CAAA;AACA,kBAAA,OAAA,CAAA,GAAA,CAAA,mCAAA,EAAA,QAAA;AACA;AACA;AACA;AAVA;AAAA;AAAA;AAAA;AAAA;;AAYA,YAAA,OAAA,CAAA,KAAA,CAAA,oCAAA;AACA,WAbA,EAaA,GAbA,CAAA;AAcA;AACA,OApFA;AAqFA,KArpCA;AAupCA;AACA,IAAA,eAxpCA,6BAwpCA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,+BAAA,EADA,CAGA;;AACA,WAAA,uBAAA,GAJA,CAMA;;AACA,WAAA,iBAAA;AACA,WAAA,oBAAA,GAAA,CAAA,CAAA,CARA,CAQA;AAEA;;AACA,WAAA,WAAA;AAEA,WAAA,QAAA,CAAA,OAAA,CAAA,YAAA;AACA,KAtqCA;AAwqCA;AACA,IAAA,2BAzqCA,yCAyqCA;AACA;AACA,UAAA,CAAA,KAAA,gBAAA,CAAA,YAAA,CAAA,IAAA,EAAA,EAAA;AACA,aAAA,QAAA,CAAA,KAAA,CAAA,UAAA;AACA,eAAA,KAAA;AACA,OALA,CAOA;;;AACA,UAAA,KAAA,gBAAA,CAAA,YAAA,CAAA,IAAA,GAAA,MAAA,GAAA,EAAA,EAAA;AACA,aAAA,QAAA,CAAA,KAAA,CAAA,gBAAA;AACA,eAAA,KAAA;AACA,OAXA,CAaA;;;AACA,UAAA,CAAA,KAAA,gBAAA,CAAA,mBAAA,CAAA,IAAA,EAAA,EAAA;AACA,aAAA,QAAA,CAAA,KAAA,CAAA,UAAA;AACA,eAAA,KAAA;AACA,OAjBA,CAmBA;;;AACA,UAAA,KAAA,gBAAA,CAAA,mBAAA,CAAA,IAAA,GAAA,MAAA,GAAA,GAAA,EAAA;AACA,aAAA,QAAA,CAAA,KAAA,CAAA,iBAAA;AACA,eAAA,KAAA;AACA,OAvBA,CAyBA;;;AACA,UAAA,CAAA,KAAA,gBAAA,CAAA,eAAA,CAAA,IAAA,EAAA,EAAA;AACA,aAAA,QAAA,CAAA,KAAA,CAAA,SAAA;AACA,eAAA,KAAA;AACA,OA7BA,CA+BA;;;AACA,UAAA,kBAAA,GAAA,yFAAA;;AACA,UAAA,CAAA,kBAAA,CAAA,IAAA,CAAA,KAAA,gBAAA,CAAA,eAAA,CAAA,IAAA,EAAA,CAAA,EAAA;AACA,aAAA,QAAA,CAAA,KAAA,CAAA,kCAAA;AACA,eAAA,KAAA;AACA,OApCA,CAsCA;;;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,sDAAA,EAAA,KAAA,gBAAA,CAAA,MAAA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,+CAAA,EAAA,KAAA,gBAAA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,uDAAA,EAAA,KAAA,gBAAA,IAAA,KAAA,gBAAA,CAAA,OAAA,EAzCA,CA2CA;AACA;AACA;AACA;;AACA,UAAA,UAAA,GAAA,KAAA,gBAAA,CAAA,MAAA,GAAA,CAAA;AACA,UAAA,YAAA,GAAA,KAAA,gBAAA,IAAA,KAAA,gBAAA,CAAA,OAAA;;AAEA,UAAA,CAAA,UAAA,IAAA,CAAA,YAAA,EAAA;AACA,aAAA,QAAA,CAAA,KAAA,CAAA,qBAAA;AACA,eAAA,KAAA;AACA;;AAEA,aAAA,IAAA;AACA,KAjuCA;AAmuCA;AACA,IAAA,yBApuCA,uCAouCA;AACA;AACA,UAAA,YAAA,GAAA,KAAA,gBAAA,CAAA,YAAA,CAAA,IAAA,EAAA;;AACA,UAAA,CAAA,YAAA,EAAA;AACA;AACA,YAAA,SAAA,GAAA,IAAA,IAAA,GAAA,cAAA,CAAA,OAAA,CAAA;AACA,QAAA,YAAA,gCAAA,SAAA,CAAA;AACA,OAPA,CASA;;;AACA,UAAA,iBAAA,GAAA,KAAA,oBAAA,IAAA,CAAA;AACA,UAAA,gBAAA,GAAA,iBAAA,GAAA,KAAA,gBAAA,CAAA,KAAA,oBAAA,CAAA,GAAA,IAAA;AAEA,MAAA,OAAA,CAAA,GAAA,CAAA,qEAAA,EAAA,iBAAA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,oEAAA,EAAA,gBAAA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,2EAAA,EAAA,gBAAA,GAAA,gBAAA,CAAA,MAAA,GAAA,MAAA,EAfA,CAiBA;;AACA,UAAA,YAAA,GAAA;AACA,QAAA,EAAA,EAAA,iBAAA,GAAA,gBAAA,CAAA,EAAA,GAAA,IAAA,CAAA,GAAA,EADA;AAEA,QAAA,YAAA,EAAA,YAFA;AAGA,QAAA,mBAAA,EAAA,KAAA,gBAAA,CAAA,mBAAA,CAAA,IAAA,EAHA;AAIA,QAAA,eAAA,EAAA,KAAA,gBAAA,CAAA,eAAA,CAAA,IAAA,EAJA;AAKA,QAAA,YAAA,EAAA,KAAA,gBAAA,CAAA,MAAA,GAAA,CAAA,GAAA,KAAA,gBAAA,CAAA,CAAA,CAAA,GAAA,gBAAA,GAAA,gBAAA,CAAA,YAAA,GAAA,IALA;AAMA,QAAA,QAAA,EAAA,KAAA,gBAAA,GAAA,KAAA,gBAAA,CAAA,IAAA,GAAA,gBAAA,GAAA,gBAAA,CAAA,QAAA,GAAA,EANA;AAOA,QAAA,QAAA,EAAA,KAAA,gBAAA,GAAA,KAAA,gBAAA,CAAA,IAAA,GAAA,gBAAA,GAAA,gBAAA,CAAA,QAAA,GAAA,CAPA;AAQA;AACA,QAAA,MAAA,EAAA,gBAAA,IAAA,gBAAA,CAAA,MAAA,KAAA,OAAA,GAAA,OAAA,GAAA,OATA;AAUA,QAAA,UAAA,EAAA,iBAAA,GAAA,gBAAA,CAAA,UAAA,GAAA,IAAA,IAAA,EAVA;AAWA;AACA,QAAA,eAAA,EAAA,gBAAA,GAAA,gBAAA,CAAA,eAAA,GAAA,EAZA;AAaA,QAAA,gBAAA,EAAA,gBAAA,GAAA,gBAAA,CAAA,gBAAA,GAAA;AAbA,OAAA;AAgBA,MAAA,OAAA,CAAA,GAAA,CAAA,mEAAA,EAAA,YAAA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,uEAAA,EAAA,YAAA,CAAA,MAAA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,gFAAA,EAAA,YAAA,CAAA,eAAA;;AAEA,UAAA,KAAA,oBAAA,IAAA,CAAA,EAAA;AACA;AACA,QAAA,OAAA,CAAA,GAAA,CAAA,kCAAA,EAAA,KAAA,oBAAA;AACA,aAAA,gBAAA,CAAA,MAAA,CAAA,KAAA,oBAAA,EAAA,CAAA,EAAA,YAAA;AACA,QAAA,OAAA,CAAA,GAAA,CAAA,+BAAA,EAAA,YAAA;AACA,OALA,MAKA;AACA;AACA,QAAA,OAAA,CAAA,GAAA,CAAA,qCAAA,EAAA,KAAA,gBAAA,CAAA,MAAA;AACA,aAAA,gBAAA,CAAA,IAAA,CAAA,YAAA;AACA,QAAA,OAAA,CAAA,GAAA,CAAA,+BAAA,EAAA,YAAA;AACA,QAAA,OAAA,CAAA,GAAA,CAAA,+BAAA,EAAA,KAAA,gBAAA,CAAA,MAAA;AACA,OAjDA,CAmDA;;;AACA,WAAA,wBAAA,CAAA,YAAA;AACA,KAzxCA;AA2xCA;AACA,IAAA,oBA5xCA,gCA4xCA,KA5xCA,EA4xCA;AAAA,UAAA,YAAA,uEAAA,KAAA;;AACA,UAAA,KAAA,GAAA,CAAA,IAAA,KAAA,IAAA,KAAA,gBAAA,CAAA,MAAA,EAAA;AACA,QAAA,OAAA,CAAA,KAAA,CAAA,gCAAA,EAAA,KAAA;AACA;AACA,OAJA,CAMA;AACA;;;AACA,UAAA,CAAA,YAAA,EAAA;AACA,aAAA,uBAAA;AACA;;AAEA,UAAA,QAAA,GAAA,KAAA,gBAAA,CAAA,KAAA,CAAA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,+BAAA,EAAA,QAAA,EAbA,CAeA;;AACA,WAAA,gBAAA,GAAA;AACA,QAAA,YAAA,EAAA,QAAA,CAAA,YADA;AAEA,QAAA,mBAAA,EAAA,QAAA,CAAA,mBAFA;AAGA,QAAA,eAAA,EAAA,QAAA,CAAA,eAAA,IAAA,EAHA;AAIA,QAAA,eAAA,EAAA;AAJA,OAAA,CAhBA,CAuBA;;AACA,UAAA,QAAA,CAAA,MAAA,KAAA,OAAA,IAAA,QAAA,CAAA,QAAA,EAAA;AACA;AACA,aAAA,gBAAA,GAAA,EAAA,CAFA,CAEA;;AACA,aAAA,gBAAA,GAAA;AACA,UAAA,IAAA,EAAA,QAAA,CAAA,QADA;AAEA,UAAA,YAAA,EAAA,QAAA,CAAA,QAFA;AAGA,UAAA,IAAA,EAAA,QAAA,CAAA,QAAA,IAAA,CAHA;AAIA,UAAA,IAAA,EAAA,IAJA;AAIA;AACA,UAAA,OAAA,EAAA,IALA;AAKA;AACA,UAAA,WAAA,EAAA,QAAA,CAAA,eAAA,IAAA,EANA,CAMA;;AANA,SAAA;AAQA,QAAA,OAAA,CAAA,GAAA,CAAA,qCAAA,EAAA,KAAA,gBAAA;AACA,OAZA,MAYA,IAAA,QAAA,CAAA,YAAA,IAAA,QAAA,CAAA,QAAA,EAAA;AACA;AACA,aAAA,gBAAA,GAAA,CAAA,QAAA,CAAA,YAAA,CAAA;AACA,aAAA,gBAAA,GAAA;AACA,UAAA,IAAA,EAAA,QAAA,CAAA,QADA;AAEA,UAAA,YAAA,EAAA,QAAA,CAAA,QAFA;AAGA,UAAA,IAAA,EAAA,QAAA,CAAA,QAAA,IAAA,CAHA;AAIA,UAAA,IAAA,EAAA,QAAA,CAAA,YAJA;AAKA,UAAA,OAAA,EAAA;AALA,SAAA;AAOA,QAAA,OAAA,CAAA,GAAA,CAAA,oCAAA,EAAA,KAAA,gBAAA;AACA,OAXA,MAWA;AACA;AACA,aAAA,gBAAA,GAAA,EAAA;AACA,aAAA,gBAAA,GAAA,IAAA;AACA,QAAA,OAAA,CAAA,GAAA,CAAA,gCAAA;AACA,OApDA,CAsDA;;;AACA,WAAA,oBAAA,GAAA,KAAA,CAvDA,CAyDA;;AACA,UAAA,CAAA,YAAA,EAAA;AACA,aAAA,QAAA,CAAA,IAAA,iDAAA,QAAA,CAAA,YAAA,GADA,CAGA;;AACA,aAAA,WAAA;AACA;AACA,KA51CA;AA81CA;AACA,IAAA,uBA/1CA,qCA+1CA;AACA;AACA,UAAA,cAAA,GAAA,KAAA,gBAAA,CAAA,YAAA,CAAA,IAAA,MACA,KAAA,gBAAA,CAAA,mBAAA,CAAA,IAAA,EADA,IAEA,KAAA,gBAAA,CAAA,MAAA,GAAA,CAFA,IAGA,KAAA,gBAAA,IAAA,KAAA,gBAAA,CAAA,OAHA;;AAKA,UAAA,CAAA,cAAA,EAAA;AACA,QAAA,OAAA,CAAA,GAAA,CAAA,mCAAA;AACA,eAAA,KAAA,CAFA,CAEA;AACA,OAVA,CAYA;;;AACA,UAAA,KAAA,oBAAA,IAAA,CAAA,EAAA;AACA,QAAA,OAAA,CAAA,GAAA,CAAA,mCAAA;AACA,aAAA,yBAAA;AACA,eAAA,IAAA,CAHA,CAGA;AACA,OAjBA,CAmBA;;;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,wCAAA;AACA,WAAA,yBAAA,GArBA,CAuBA;;AACA,UAAA,CAAA,KAAA,WAAA,EAAA;AACA,aAAA,QAAA,CAAA,IAAA,CAAA,cAAA;AACA;;AAEA,aAAA,IAAA,CA5BA,CA4BA;AACA,KA53CA;AA83CA;AACA,IAAA,oBA/3CA,kCA+3CA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,oCAAA,EADA,CAGA;;AACA,WAAA,wBAAA,GAAA,EAAA;AAEA,UAAA,UAAA,GAAA,IAAA;AACA,UAAA,YAAA,GAAA,CAAA;AACA,UAAA,SAAA,GAAA,EAAA,CARA,CAUA;;AACA,WAAA,IAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,KAAA,gBAAA,CAAA,MAAA,EAAA,CAAA,EAAA,EAAA;AACA,YAAA,QAAA,GAAA,KAAA,gBAAA,CAAA,CAAA,CAAA;AACA,YAAA,MAAA,GAAA,EAAA,CAFA,CAIA;;AACA,YAAA,CAAA,QAAA,CAAA,YAAA,IAAA,CAAA,QAAA,CAAA,YAAA,CAAA,IAAA,EAAA,EAAA;AACA,UAAA,MAAA,CAAA,IAAA,CAAA,SAAA;AACA,SAPA,CASA;;;AACA,YAAA,CAAA,QAAA,CAAA,mBAAA,IAAA,CAAA,QAAA,CAAA,mBAAA,CAAA,IAAA,EAAA,EAAA;AACA,UAAA,MAAA,CAAA,IAAA,CAAA,SAAA;AACA,SAZA,CAcA;;;AACA,YAAA,CAAA,QAAA,CAAA,eAAA,IAAA,CAAA,QAAA,CAAA,eAAA,CAAA,IAAA,EAAA,EAAA;AACA,UAAA,MAAA,CAAA,IAAA,CAAA,QAAA;AACA,SAFA,MAEA;AACA;AACA,cAAA,kBAAA,GAAA,yFAAA;;AACA,cAAA,CAAA,kBAAA,CAAA,IAAA,CAAA,QAAA,CAAA,eAAA,CAAA,IAAA,EAAA,CAAA,EAAA;AACA,YAAA,MAAA,CAAA,IAAA,CAAA,WAAA;AACA;AACA,SAvBA,CAyBA;;;AACA,YAAA,QAAA,CAAA,MAAA,KAAA,OAAA,EAAA;AACA;AACA,cAAA,CAAA,QAAA,CAAA,QAAA,IAAA,CAAA,QAAA,CAAA,eAAA,EAAA;AACA,YAAA,MAAA,CAAA,IAAA,CAAA,SAAA;AACA;AACA,SALA,MAKA;AACA;AACA,cAAA,CAAA,QAAA,CAAA,YAAA,IAAA,CAAA,QAAA,CAAA,QAAA,EAAA;AACA,YAAA,MAAA,CAAA,IAAA,CAAA,SAAA;AACA;AACA,SApCA,CAsCA;;;AACA,YAAA,OAAA,GAAA,MAAA,CAAA,MAAA,KAAA,CAAA;AACA,aAAA,wBAAA,CAAA,QAAA,CAAA,EAAA,IAAA;AACA,UAAA,MAAA,EAAA,MADA;AAEA,UAAA,OAAA,EAAA,OAFA;AAGA,UAAA,YAAA,EAAA,QAAA,CAAA,YAAA,gCAAA,CAAA,GAAA,CAAA;AAHA,SAAA;;AAMA,YAAA,CAAA,OAAA,EAAA;AACA,UAAA,UAAA,GAAA,KAAA;AACA,UAAA,YAAA;AACA,UAAA,SAAA,CAAA,IAAA,WAAA,QAAA,CAAA,YAAA,gCAAA,CAAA,GAAA,CAAA,CAAA,eAAA,MAAA,CAAA,IAAA,CAAA,GAAA,CAAA;AACA;AACA;;AAEA,UAAA,MAAA,GAAA;AACA,QAAA,OAAA,EAAA,UADA;AAEA,QAAA,YAAA,EAAA,YAFA;AAGA,QAAA,MAAA,EAAA,SAHA;AAIA,QAAA,iBAAA,EAAA,KAAA;AAJA,OAAA;AAOA,MAAA,OAAA,CAAA,GAAA,CAAA,+BAAA,EAAA,MAAA;AACA,aAAA,MAAA;AACA,KAx8CA;AA08CA;AACA,IAAA,wBA38CA,oCA28CA,QA38CA,EA28CA;AACA,UAAA,MAAA,GAAA,EAAA,CADA,CAGA;;AACA,UAAA,CAAA,QAAA,CAAA,YAAA,IAAA,CAAA,QAAA,CAAA,YAAA,CAAA,IAAA,EAAA,EAAA;AACA,QAAA,MAAA,CAAA,IAAA,CAAA,SAAA;AACA,OANA,CAQA;;;AACA,UAAA,CAAA,QAAA,CAAA,mBAAA,IAAA,CAAA,QAAA,CAAA,mBAAA,CAAA,IAAA,EAAA,EAAA;AACA,QAAA,MAAA,CAAA,IAAA,CAAA,SAAA;AACA,OAXA,CAaA;;;AACA,UAAA,CAAA,QAAA,CAAA,eAAA,IAAA,CAAA,QAAA,CAAA,eAAA,CAAA,IAAA,EAAA,EAAA;AACA,QAAA,MAAA,CAAA,IAAA,CAAA,QAAA;AACA,OAFA,MAEA;AACA;AACA,YAAA,kBAAA,GAAA,yFAAA;;AACA,YAAA,CAAA,kBAAA,CAAA,IAAA,CAAA,QAAA,CAAA,eAAA,CAAA,IAAA,EAAA,CAAA,EAAA;AACA,UAAA,MAAA,CAAA,IAAA,CAAA,WAAA;AACA;AACA,OAtBA,CAwBA;;;AACA,UAAA,QAAA,CAAA,MAAA,KAAA,OAAA,EAAA;AACA;AACA,YAAA,CAAA,QAAA,CAAA,QAAA,IAAA,CAAA,QAAA,CAAA,eAAA,EAAA;AACA,UAAA,MAAA,CAAA,IAAA,CAAA,SAAA;AACA;AACA,OALA,MAKA;AACA;AACA,YAAA,CAAA,QAAA,CAAA,YAAA,IAAA,CAAA,QAAA,CAAA,QAAA,EAAA;AACA,UAAA,MAAA,CAAA,IAAA,CAAA,SAAA;AACA;AACA,OAnCA,CAqCA;;;AACA,UAAA,OAAA,GAAA,MAAA,CAAA,MAAA,KAAA,CAAA;AACA,WAAA,IAAA,CAAA,KAAA,wBAAA,EAAA,QAAA,CAAA,EAAA,EAAA;AACA,QAAA,MAAA,EAAA,MADA;AAEA,QAAA,OAAA,EAAA,OAFA;AAGA,QAAA,YAAA,EAAA,QAAA,CAAA,YAAA,IAAA;AAHA,OAAA;AAMA,MAAA,OAAA,CAAA,GAAA,mGAAA,QAAA,CAAA,YAAA,eAAA,OAAA,GAAA,IAAA,GAAA,IAAA,GAAA,MAAA;AACA,KAz/CA;AA2/CA;AACA,IAAA,sBA5/CA,kCA4/CA,gBA5/CA,EA4/CA;AAAA;;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,+BAAA,EADA,CAGA;;AACA,UAAA,uBAAA,GAAA,CAAA,CAAA;AACA,UAAA,kBAAA,GAAA,IAAA;;AAEA,WAAA,IAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,KAAA,gBAAA,CAAA,MAAA,EAAA,CAAA,EAAA,EAAA;AACA,YAAA,QAAA,GAAA,KAAA,gBAAA,CAAA,CAAA,CAAA;AACA,YAAA,cAAA,GAAA,gBAAA,CAAA,iBAAA,CAAA,QAAA,CAAA,EAAA,CAAA;;AAEA,YAAA,cAAA,IAAA,CAAA,cAAA,CAAA,OAAA,EAAA;AACA,UAAA,uBAAA,GAAA,CAAA;AACA,UAAA,kBAAA,GAAA,QAAA;AACA;AACA;AACA;;AAEA,UAAA,uBAAA,IAAA,CAAA,IAAA,kBAAA,EAAA;AACA,QAAA,OAAA,CAAA,GAAA,wGAAA,kBAAA,CAAA,YAAA,6BAAA,uBAAA,QADA,CAGA;;AACA,aAAA,oBAAA,CAAA,uBAAA,EAAA,IAAA,EAJA,CAMA;;AACA,YAAA,SAAA,GAAA,gBAAA,CAAA,iBAAA,CAAA,kBAAA,CAAA,EAAA,CAAA;AACA,YAAA,YAAA,iCAAA,kBAAA,CAAA,YAAA,yDAAA,SAAA,CAAA,MAAA,CAAA,IAAA,CAAA,GAAA,CAAA,CAAA;AAEA,aAAA,QAAA,CAAA,KAAA,CAAA,YAAA,EAVA,CAYA;;AACA,QAAA,UAAA,CAAA,YAAA;AACA,UAAA,OAAA,CAAA,QAAA,CAAA,OAAA,wBAAA,gBAAA,CAAA,YAAA;AACA,SAFA,EAEA,IAFA,CAAA;AAIA,QAAA,OAAA,CAAA,GAAA,CAAA,2CAAA;AACA,OAlBA,MAkBA;AACA;AACA,aAAA,QAAA,CAAA,KAAA,sGAAA,gBAAA,CAAA,YAAA;AACA;AACA,KApiDA;AAsiDA;AACA,IAAA,sBAviDA,kCAuiDA,KAviDA,EAuiDA;AAAA;;AACA,UAAA,KAAA,GAAA,CAAA,IAAA,KAAA,IAAA,KAAA,gBAAA,CAAA,MAAA,EAAA;AACA,QAAA,OAAA,CAAA,KAAA,CAAA,gCAAA,EAAA,KAAA;AACA;AACA;;AAEA,UAAA,QAAA,GAAA,KAAA,gBAAA,CAAA,KAAA,CAAA;AACA,WAAA,QAAA,CAAA;AACA,QAAA,KAAA,EAAA,OADA;AAEA,QAAA,OAAA,8DAAA,QAAA,CAAA,YAAA,mBAFA;AAGA,QAAA,IAAA,EAAA,gBAAA;AACA,UAAA,OAAA,CAAA,gBAAA,CAAA,MAAA,CAAA,KAAA,EAAA,CAAA;;AACA,UAAA,OAAA,CAAA,GAAA,CAAA,+BAAA,EAAA,QAAA,EAFA,CAIA;;AACA,cAAA,OAAA,CAAA,oBAAA,KAAA,KAAA,EAAA;AACA,YAAA,OAAA,CAAA,iBAAA;;AACA,YAAA,OAAA,CAAA,oBAAA,GAAA,CAAA,CAAA,CAFA,CAGA;;AACA,YAAA,OAAA,CAAA,WAAA;AACA,WALA,MAKA,IAAA,OAAA,CAAA,oBAAA,GAAA,KAAA,EAAA;AACA;AACA,YAAA,OAAA,CAAA,oBAAA;AACA;;AAEA,UAAA,OAAA,CAAA,QAAA,CAAA,OAAA,CAAA,QAAA;AACA;AAnBA,OAAA;AAqBA,KAnkDA;AAqkDA;AACA,IAAA,UAtkDA,sBAskDA,IAtkDA,EAskDA;AACA,UAAA,CAAA,IAAA,EAAA,OAAA,EAAA;AACA,aAAA,IAAA,IAAA,CAAA,IAAA,EAAA,cAAA,CAAA,OAAA,CAAA;AACA;AAzkDA;AA/KA,CAAA", "sourcesContent": ["<template>\n  <a-modal\n    :title=\"modalTitle\"\n    :visible=\"visible\"\n    :width=\"900\"\n    :confirm-loading=\"stepLoading\"\n    :mask-closable=\"false\"\n    :centered=\"true\"\n    :body-style=\"{ padding: '0' }\"\n    :footer=\"null\"\n    class=\"creator-agent-modal\"\n    @cancel=\"handleCancel\"\n  >\n    <!-- 步骤条 -->\n    <div class=\"step-header\">\n      <a-steps :current=\"currentStep\" class=\"custom-steps\">\n        <a-step title=\"智能体信息\" description=\"填写基本信息\" />\n        <a-step title=\"工作流配置\" description=\"配置工作流\" />\n        <a-step title=\"完成创建\" description=\"创建完成\" />\n      </a-steps>\n    </div>\n\n    <!-- 步骤内容 -->\n    <div class=\"step-content\">\n      <!-- 第一步：智能体基本信息 -->\n      <div v-show=\"currentStep === 0\" class=\"step-panel\">\n        <div class=\"panel-header\">\n          <h3 class=\"panel-title\">\n            <a-icon type=\"robot\" />\n            智能体基本信息\n          </h3>\n          <p class=\"panel-desc\">请填写智能体的基本信息，这些信息将展示给用户</p>\n        </div>\n\n        <a-form-model\n          ref=\"form\"\n          :model=\"formData\"\n          :rules=\"rules\"\n          class=\"modern-form\"\n        >\n          <!-- 智能体名称 -->\n          <div class=\"form-card\">\n            <a-form-model-item prop=\"agentName\">\n              <div class=\"field-label\">\n                <a-icon type=\"robot\" />\n                智能体名称\n                <span class=\"required-star\">*</span>\n              </div>\n              <a-input\n                v-model=\"formData.agentName\"\n                placeholder=\"请输入智能体名称\"\n                size=\"large\"\n                class=\"modern-input\"\n                :max-length=\"100\"\n                show-count\n              />\n              <div class=\"field-tips\">\n                为您的智能体起一个吸引人的名称\n              </div>\n            </a-form-model-item>\n          </div>\n\n          <!-- 智能体描述 -->\n          <div class=\"form-card\">\n            <a-form-model-item prop=\"agentDescription\">\n              <div class=\"field-label\">\n                <a-icon type=\"file-text\" />\n                智能体描述\n                <span class=\"required-star\">*</span>\n              </div>\n              <a-textarea\n                v-model=\"formData.agentDescription\"\n                placeholder=\"请详细描述您的智能体功能和特点\"\n                :rows=\"4\"\n                :max-length=\"1000\"\n                show-count\n                class=\"modern-textarea\"\n              />\n              <div class=\"field-tips\">\n                详细描述有助于用户了解您的智能体功能\n              </div>\n            </a-form-model-item>\n          </div>\n\n          <!-- 智能体头像 -->\n          <div class=\"form-card\">\n            <a-form-model-item prop=\"agentAvatar\">\n              <div class=\"field-label\">\n                <a-icon type=\"picture\" />\n                智能体头像\n                <span class=\"required-star\">*</span>\n              </div>\n              <j-image-upload-deferred\n                ref=\"avatarUpload\"\n                v-model=\"formData.agentAvatar\"\n                :isMultiple=\"false\"\n                bizPath=\"agent-avatar\"\n                text=\"上传头像\">\n              </j-image-upload-deferred>\n              <div class=\"field-tips\">\n                支持 JPG、PNG 格式，文件大小不超过 5MB\n              </div>\n            </a-form-model-item>\n          </div>\n\n          <!-- 体验链接 -->\n          <div class=\"form-card\">\n            <a-form-model-item prop=\"experienceLink\">\n              <div class=\"field-label\">\n                <a-icon type=\"link\" />\n                体验链接\n                <span class=\"optional\">（可选）</span>\n              </div>\n              <a-input\n                v-model=\"formData.experienceLink\"\n                placeholder=\"请输入体验链接\"\n                size=\"large\"\n                class=\"modern-input\"\n                :max-length=\"500\"\n              />\n              <div class=\"field-tips\">\n                用户可以通过此链接体验您的智能体功能\n              </div>\n            </a-form-model-item>\n          </div>\n\n          <!-- 价格 -->\n          <div class=\"form-card\">\n            <a-form-model-item prop=\"price\">\n              <div class=\"field-label\">\n                <a-icon type=\"dollar\" />\n                价格设置\n                <span class=\"required-star\">*</span>\n              </div>\n              <a-input-number\n                v-model=\"formData.price\"\n                placeholder=\"请输入价格\"\n                :min=\"0\"\n                :max=\"99999\"\n                :precision=\"2\"\n                :step=\"0.01\"\n                size=\"large\"\n                class=\"modern-input-number\"\n              >\n                <template slot=\"addonBefore\">¥</template>\n              </a-input-number>\n              <div class=\"field-tips\">\n                设置智能体的使用价格，用户购买后可以使用您的智能体\n              </div>\n            </a-form-model-item>\n          </div>\n        </a-form-model>\n\n        <!-- 第一步底部按钮 -->\n        <div class=\"step-footer\">\n          <a-button @click=\"handleCancel\" :disabled=\"stepLoading\">\n            取消\n          </a-button>\n          <a-button type=\"primary\" @click=\"handleNext\" :loading=\"stepLoading\" class=\"next-btn\">\n            下一步：配置工作流\n            <a-icon type=\"arrow-right\" />\n          </a-button>\n        </div>\n      </div>\n\n      <!-- 第二步：工作流配置 -->\n      <div v-show=\"currentStep === 1\" class=\"step-panel\">\n        <div class=\"panel-header\">\n          <h3 class=\"panel-title\">\n            <a-icon type=\"apartment\" />\n            工作流配置\n          </h3>\n          <p class=\"panel-desc\">为您的智能体配置工作流，提升智能体的功能和效率</p>\n        </div>\n\n        <!-- 已创建的智能体信息 -->\n        <div class=\"created-agent-info\" v-if=\"createdAgent\">\n          <div class=\"agent-summary\">\n            <div class=\"agent-avatar\">\n              <img :src=\"getFullAvatarUrl(createdAgent.agentAvatar)\" :alt=\"createdAgent.agentName\" v-if=\"createdAgent.agentAvatar\" />\n              <a-icon type=\"robot\" v-else />\n            </div>\n            <div class=\"agent-details\">\n              <h4>{{ createdAgent.agentName }}</h4>\n              <p>{{ createdAgent.agentDescription }}</p>\n              <span class=\"agent-price\">¥{{ createdAgent.price }}</span>\n            </div>\n          </div>\n        </div>\n\n        <!-- 工作流配置区域 -->\n        <div class=\"workflow-section\">\n          <!-- 工作流表单 -->\n          <div class=\"workflow-form\">\n            <div class=\"form-header\">\n              <h3>\n                <a-icon type=\"apartment\" />\n                新增工作流\n              </h3>\n              <p>为您的智能体添加工作流，让它更加智能和实用</p>\n            </div>\n\n            <a-form-model ref=\"workflowFormRef\" :model=\"workflowFormData\" :rules=\"workflowRules\" layout=\"vertical\">\n              <a-form-model-item label=\"工作流名称\" prop=\"workflowName\">\n                <a-input\n                  v-model=\"workflowFormData.workflowName\"\n                  placeholder=\"请输入工作流名称，如：文档生成助手\"\n                  :maxLength=\"30\"\n                  show-count\n                />\n              </a-form-model-item>\n\n              <a-form-model-item label=\"工作流描述\" prop=\"workflowDescription\">\n                <a-textarea\n                  v-model=\"workflowFormData.workflowDescription\"\n                  placeholder=\"请描述工作流的功能和用途，帮助用户了解其作用\"\n                  :rows=\"3\"\n                  :maxLength=\"200\"\n                  show-count\n                />\n              </a-form-model-item>\n\n              <a-form-model-item label=\"输入参数说明\" prop=\"inputParamsDesc\">\n                <a-textarea\n                  v-model=\"workflowFormData.inputParamsDesc\"\n                  placeholder=\"格式：参数:值 或 参数:&quot;值&quot; 或 参数:'值'（例如：name:&quot;张三&quot;,age:25,city:'北京'）\"\n                  :rows=\"4\"\n                  :maxLength=\"10000\"\n                  @blur=\"handleInputParamsBlur\"\n                />\n                <div style=\"color: #666; font-size: 12px; margin-top: 4px;\">\n                  * 必填项，例如：name:&quot;张三&quot;,age:25,city:'北京' 支持中英文冒号逗号\n                </div>\n              </a-form-model-item>\n\n              <a-form-model-item label=\"工作流文件\" prop=\"workflowPackage\">\n                <!-- 文件上传区域 -->\n                <div class=\"workflow-file-upload\">\n                  <!-- 已上传文件显示 -->\n                  <div v-if=\"workflowFileInfo\" class=\"uploaded-file-info\">\n                    <div class=\"file-item\" :class=\"{ 'saved-file': workflowFileInfo.isSaved }\">\n                      <a-icon type=\"file-zip\" class=\"file-icon\" />\n                      <span class=\"file-name\">{{ workflowFileInfo.originalName || workflowFileInfo.name }}</span>\n                      <!-- 🔥 已保存的文件显示状态标签 -->\n                      <a-tag v-if=\"workflowFileInfo.isSaved\" color=\"green\" size=\"small\" style=\"margin-left: 8px;\">\n                        已保存\n                      </a-tag>\n                      <a-button\n                        type=\"link\"\n                        size=\"small\"\n                        @click=\"handleRemoveWorkflowFile\"\n                        class=\"remove-btn\"\n                        :title=\"workflowFileInfo.isSaved ? '重新选择文件' : '删除文件'\"\n                      >\n                        <a-icon :type=\"workflowFileInfo.isSaved ? 'edit' : 'delete'\" />\n                        {{ workflowFileInfo.isSaved ? '重选' : '删除' }}\n                      </a-button>\n                    </div>\n                  </div>\n\n                  <!-- 上传按钮 -->\n                  <div v-else class=\"upload-area\">\n                    <a-upload\n                      ref=\"workflowUpload\"\n                      name=\"file\"\n                      :multiple=\"false\"\n                      :before-upload=\"beforeWorkflowUpload\"\n                      :show-upload-list=\"false\"\n                      @change=\"handleWorkflowFileSelect\"\n                      accept=\".zip\"\n                      :customRequest=\"() => {}\"\n                    >\n                      <a-button :loading=\"workflowUploading\">\n                        <a-icon type=\"upload\" />\n                        选择工作流压缩包\n                      </a-button>\n                    </a-upload>\n                  </div>\n                </div>\n\n                <div class=\"upload-tip\">\n                  <a-icon type=\"exclamation-circle\" style=\"color: #faad14;\" />\n                  <strong>温馨提示：</strong>只支持 .zip 格式，文件大小不超过 5MB\n                </div>\n              </a-form-model-item>\n            </a-form-model>\n\n            <!-- 新增下一个工作流按钮 -->\n            <div class=\"add-next-workflow\" v-show=\"currentStep === 1\">\n              <a-button type=\"dashed\" block @click=\"addNextWorkflow\" class=\"add-next-btn\">\n                <a-icon type=\"plus\" />\n                新增下一个工作流\n              </a-button>\n            </div>\n          </div>\n\n          <!-- 工作流列表 -->\n          <div class=\"temp-workflows\" v-if=\"tempWorkflowList.length > 0\">\n            <a-divider>工作流列表 ({{ tempWorkflowList.length }})</a-divider>\n            <div class=\"workflow-list\">\n              <div\n                v-for=\"(workflow, index) in tempWorkflowList\"\n                :key=\"workflow.id\"\n                class=\"workflow-item\"\n                :class=\"{\n                  'editing': currentWorkflowIndex === index,\n                  'has-error': workflowValidationErrors[workflow.id] && !workflowValidationErrors[workflow.id].isValid\n                }\"\n              >\n                <div class=\"workflow-info\">\n                  <h4 class=\"workflow-name\">\n                    {{ workflow.workflowName }}\n                    <!-- 状态标签 -->\n                    <a-tag v-if=\"workflow.status === 'saved'\" color=\"green\" size=\"small\">已保存</a-tag>\n                    <a-tag v-else-if=\"workflow.status === 'draft'\" color=\"orange\" size=\"small\">新增</a-tag>\n                    <a-tag v-if=\"currentWorkflowIndex === index\" color=\"blue\" size=\"small\">编辑中</a-tag>\n                    <a-tag v-if=\"workflowValidationErrors[workflow.id] && !workflowValidationErrors[workflow.id].isValid\" color=\"red\" size=\"small\">\n                      <a-icon type=\"exclamation-circle\" />\n                      有错误\n                    </a-tag>\n                  </h4>\n                  <p class=\"workflow-desc\">{{ workflow.workflowDescription || '暂无描述' }}</p>\n\n                  <!-- 🔥 验证错误提示 -->\n                  <div v-if=\"workflowValidationErrors[workflow.id] && !workflowValidationErrors[workflow.id].isValid\" class=\"workflow-errors\">\n                    <a-alert\n                      type=\"error\"\n                      size=\"small\"\n                      show-icon\n                      :message=\"`请补充：${workflowValidationErrors[workflow.id].errors.join('、')}`\"\n                    />\n                  </div>\n\n                </div>\n                <div class=\"workflow-actions\">\n                  <a-button size=\"small\" @click=\"loadWorkflowFromTemp(index)\" :disabled=\"currentWorkflowIndex === index\">\n                    <a-icon type=\"edit\" />\n                    编辑\n                  </a-button>\n                  <a-button size=\"small\" type=\"danger\" @click=\"deleteWorkflowFromTemp(index)\">\n                    <a-icon type=\"delete\" />\n                    删除\n                  </a-button>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- 第二步底部按钮 -->\n        <div class=\"step-footer\">\n          <a-button @click=\"handlePrev\" :disabled=\"stepLoading\">\n            <a-icon type=\"arrow-left\" />\n            上一步\n          </a-button>\n          <a-button type=\"primary\" @click=\"handleComplete\" :loading=\"stepLoading\" class=\"complete-btn\">\n            完成创建\n            <a-icon type=\"check\" />\n          </a-button>\n        </div>\n      </div>\n\n      <!-- 第三步：创建完成 -->\n      <div v-if=\"currentStep === 2\" class=\"step-content\">\n        <div class=\"step-header\">\n          <h3>\n            <a-icon type=\"check-circle\" style=\"color: #52c41a; margin-right: 8px;\" />\n            创建完成\n          </h3>\n          <p>智能体创建流程已完成</p>\n        </div>\n\n        <div class=\"success-content\">\n          <div class=\"success-icon\">\n            <a-icon type=\"check-circle\" style=\"font-size: 64px; color: #52c41a;\" />\n          </div>\n          <div class=\"success-message\">\n            <h2>恭喜您，已成功提交智能体，请耐心等待审核！</h2>\n            <p>您的智能体信息已提交至平台，我们将在1-3个工作日内完成审核。</p>\n            <p>审核结果将通过站内消息通知您，请注意查收。</p>\n          </div>\n        </div>\n\n        <!-- 第三步底部按钮 -->\n        <div class=\"step-footer\">\n          <a-button type=\"primary\" @click=\"handleCloseModal\" size=\"large\">\n            关闭\n          </a-button>\n        </div>\n      </div>\n    </div>\n  </a-modal>\n</template>\n\n<script>\nimport JImageUploadDeferred from '@/components/jeecg/JImageUploadDeferred'\nimport { createAgent, updateAgent } from '@/api/creator-agent'\nimport { createWorkflow, updateWorkflow, getWorkflowList } from '@/api/creator-workflow'\n\nexport default {\n  name: 'CreatorAgentForm',\n  components: {\n    JImageUploadDeferred\n  },\n  props: {\n    visible: {\n      type: Boolean,\n      default: false\n    },\n    loading: {\n      type: Boolean,\n      default: false\n    },\n    agent: {\n      type: Object,\n      default: null\n    },\n    mode: {\n      type: String,\n      default: 'create' // 'create' | 'edit'\n    }\n  },\n\n  data() {\n    return {\n      currentStep: 0, // 当前步骤 0=第一步，1=第二步\n      stepLoading: false, // 步骤操作加载状态\n      createdAgent: null, // 第一步创建的智能体信息\n\n      formData: {\n        agentId: '',\n        agentName: '',\n        agentDescription: '',\n        agentAvatar: '',\n        experienceLink: '',\n        price: 0\n      },\n\n      // 🔥 工作流表单数据\n      workflowFormData: {\n        workflowName: '',\n        workflowDescription: '',\n        inputParamsDesc: '',\n        workflowPackage: ''\n      },\n\n      // 🔥 工作流前端暂存管理（新的数据管理机制）\n      tempWorkflowList: [], // 前端暂存的工作流列表\n      currentWorkflowIndex: -1, // 当前正在编辑的工作流索引 (-1表示新建)\n\n      // 🔥 工作流验证错误状态管理\n      workflowValidationErrors: {}, // 格式：{ workflowId: { errors: ['缺少工作流描述', '缺少压缩包文件'], isValid: false } }\n      workflowFileList: [], // 当前工作流的文件列表（File对象，未上传）\n\n      // 🔥 工作流文件上传相关（延迟上传机制）\n      workflowFileInfo: null, // 当前选择的文件信息（用于显示）\n      workflowUploading: false, // 上传状态\n      workflowList: [], // 已保存的工作流列表（从后端加载）\n      workflowLoading: false,\n\n      rules: {\n        agentName: [\n          { required: true, message: '请输入智能体名称', trigger: 'blur' },\n          { min: 2, max: 100, message: '智能体名称长度在 2 到 100 个字符', trigger: 'blur' }\n        ],\n        agentDescription: [\n          { required: true, message: '请输入智能体描述', trigger: 'blur' },\n          { min: 2, max: 1000, message: '智能体描述长度在 2 到 1000 个字符', trigger: 'blur' }\n        ],\n        agentAvatar: [\n          { required: true, message: '请上传智能体头像', trigger: 'change' }\n        ],\n        experienceLink: [\n          { type: 'url', message: '请输入正确的URL格式', trigger: 'blur' }\n        ],\n        price: [\n          { required: true, message: '请输入价格', trigger: 'blur' },\n          { type: 'number', min: 0, max: 99999, message: '价格范围在 0 到 99999 元', trigger: 'blur' }\n        ]\n      },\n\n      // 🔥 工作流表单验证规则（所有字段必填）\n      workflowRules: {\n        workflowName: [\n          { required: true, message: '工作流名称为必填项', trigger: 'blur' },\n          { min: 2, max: 30, message: '工作流名称长度在 2 到 30 个字符', trigger: 'blur' },\n          { pattern: /^[a-zA-Z0-9\\u4e00-\\u9fa5\\s\\-_]+$/, message: '工作流名称只能包含中英文、数字、空格、横线和下划线', trigger: 'blur' }\n        ],\n        workflowDescription: [\n          { required: true, message: '工作流描述为必填项', trigger: 'blur' },\n          { min: 2, max: 200, message: '工作流描述长度在 2 到 200 个字符', trigger: 'blur' }\n        ],\n        inputParamsDesc: [\n          { required: true, message: '请输入参数说明', trigger: 'blur' },\n          { min: 2, message: '参数说明至少需要2个字符', trigger: 'blur' },\n          { max: 10000, message: '参数说明长度不能超过10000个字符', trigger: 'blur' },\n          {\n            pattern: /^[^:：]+[:：](?:\"[^\"]*\"|'[^']*'|[^:：,，]+)(?:[,，][^:：]+[:：](?:\"[^\"]*\"|'[^']*'|[^:：,，]+))*$/,\n            message: '请按照格式填写：参数:值 或 参数:\"值\" 或 参数:\\'值\\'',\n            trigger: 'blur'\n          }\n        ],\n        workflowPackage: [\n          // 🔥 移除自动验证，改为手动验证，避免文件选择后仍提示必填\n        ]\n      }\n    }\n  },\n\n  computed: {\n    modalTitle() {\n      return this.mode === 'create' ? '新增智能体' : '编辑智能体'\n    },\n\n    // 🔥 文件上传配置（与后台管理系统一致）\n    uploadAction() {\n      return `${window._CONFIG['domianURL']}/sys/common/upload`\n    },\n\n    uploadHeaders() {\n      return {\n        'X-Access-Token': this.$ls.get('Access-Token')\n      }\n    },\n\n    uploadData() {\n      return {\n        'isup': 1,\n        'biz': '' // 工作流文件使用空的biz，后台会自动设置为workflow路径\n      }\n    }\n  },\n\n  watch: {\n    visible(val) {\n      if (val) {\n        this.initForm()\n        // 🔥 新增和编辑模式都从第一步开始，让用户可以修改基本信息\n        this.currentStep = 0\n\n        // 🔥 弹窗打开时立即滚动到顶部\n        this.scrollToTop()\n\n        if (this.mode === 'edit' && this.agent) {\n          this.createdAgent = { ...this.agent }\n          // 🔥 编辑模式下立即加载工作流数据，确保数据回填\n          this.$nextTick(() => {\n            this.loadWorkflowList(this.agent.id)\n          })\n        }\n      } else {\n        // 🔥 弹窗关闭时清空所有数据，避免数据污染\n        this.resetForm()\n        this.clearAllWorkflowData()\n      }\n    },\n\n    agent: {\n      handler(val) {\n        if (val && this.visible) {\n          this.initForm()\n          if (this.mode === 'edit') {\n            this.createdAgent = { ...val }\n            // 🔥 编辑模式下立即加载工作流数据，确保数据回填\n            this.$nextTick(() => {\n              this.loadWorkflowList(val.id)\n            })\n          }\n        }\n      },\n      deep: true\n    }\n  },\n\n  methods: {\n    // 初始化表单\n    initForm() {\n      if (this.mode === 'edit' && this.agent) {\n        this.formData = {\n          agentName: this.agent.agentName || '',\n          agentDescription: this.agent.agentDescription || '',\n          agentAvatar: this.agent.agentAvatar || '',\n          experienceLink: this.agent.experienceLink || '',\n          price: this.agent.price || 0\n        }\n      } else {\n        this.formData = {\n          agentName: '',\n          agentDescription: '',\n          agentAvatar: '',\n          experienceLink: '',\n          price: 0\n        }\n\n        // 🔥 新增智能体时清空所有暂存工作流数据，确保每个智能体的创建流程是独立的\n        this.clearAllWorkflowData()\n      }\n\n      // 清除验证状态\n      this.$nextTick(() => {\n        if (this.$refs.form) {\n          this.$refs.form.clearValidate()\n        }\n      })\n    },\n\n    // 重置表单\n    resetForm() {\n      this.currentStep = 0\n      this.stepLoading = false\n      this.createdAgent = null\n\n      this.formData = {\n        agentId: '',\n        agentName: '',\n        agentDescription: '',\n        agentAvatar: '',\n        experienceLink: '',\n        price: 0\n      }\n      this.workflowList = []\n\n      if (this.$refs.form) {\n        this.$refs.form.clearValidate()\n      }\n    },\n\n    // 🔥 第一步：下一步按钮\n    async handleNext() {\n      // 🔥 先进行表单验证\n      const originalAvatar = this.formData.agentAvatar\n      const hasPendingAvatar = this.$refs.avatarUpload && this.$refs.avatarUpload.hasPendingFiles()\n\n      if (!this.formData.agentAvatar && hasPendingAvatar) {\n        this.formData.agentAvatar = 'pending_upload'\n      }\n\n      this.$refs.form.validate(async (valid) => {\n        this.formData.agentAvatar = originalAvatar\n\n        if (valid) {\n          this.stepLoading = true\n          try {\n            // 🔥 上传头像\n            await this.uploadPendingImages()\n\n            // 🔥 提交智能体信息\n            const agentData = {\n              agentName: this.formData.agentName.trim(),\n              agentDescription: this.formData.agentDescription.trim(),\n              agentAvatar: this.processAvatarValue(this.formData.agentAvatar),\n              experienceLink: this.formData.experienceLink.trim(),\n              price: this.formData.price\n            }\n\n\n\n            // 🔥 根据模式调用不同的API\n            let resultAgent\n            if (this.mode === 'create') {\n              // 新增模式：创建新智能体\n              resultAgent = await this.createAgentStep(agentData)\n              this.$message.success('智能体创建成功，请配置工作流')\n            } else {\n              // 编辑模式：更新现有智能体\n              resultAgent = await this.updateAgentStep(agentData)\n              this.$message.success('智能体更新成功，请配置工作流')\n            }\n\n            this.createdAgent = resultAgent\n\n            // 进入第二步\n            this.currentStep = 1\n            this.loadWorkflowList(resultAgent.id) // 🔥 使用主键ID\n\n            // 🔥 滚动到顶部，确保用户看到完整的第二步内容\n            this.scrollToTop()\n\n          } catch (error) {\n            console.error('🎯 CreatorAgentForm: 第一步提交失败:', error)\n            this.$message.error('智能体创建失败: ' + (error.message || '未知错误'))\n          } finally {\n            this.stepLoading = false\n          }\n        } else {\n          this.$message.error('请检查表单信息')\n        }\n      })\n    },\n\n    // 🔥 第二步：上一步按钮\n    handlePrev() {\n      console.log('🎯 CreatorAgentForm: 返回第一步')\n      this.currentStep = 0\n\n      // 🔥 滚动到顶部，确保用户看到完整的第一步内容\n      this.scrollToTop()\n    },\n\n    // 🔥 第二步：完成按钮（批量保存所有工作流）\n    async handleComplete() {\n      try {\n        console.log('🎯 CreatorAgentForm: 完成创建，开始批量处理工作流')\n        this.stepLoading = true\n\n        // 🔥 自动暂存当前表单数据（统一处理，避免重复保存）\n        console.log('🎯 CreatorAgentForm: 第一步 - 自动暂存当前表单数据')\n        console.log('🎯 CreatorAgentForm: 当前暂存列表长度:', this.tempWorkflowList.length)\n        console.log('🎯 CreatorAgentForm: 当前编辑索引:', this.currentWorkflowIndex)\n        const saveResult = this.autoSaveCurrentWorkflow()\n        console.log('🎯 CreatorAgentForm: 自动暂存结果:', saveResult)\n        console.log('🎯 CreatorAgentForm: 暂存后列表长度:', this.tempWorkflowList.length)\n\n        // 检查是否有暂存的工作流需要保存\n        if (this.tempWorkflowList.length === 0) {\n          console.log('🎯 CreatorAgentForm: 无工作流数据，直接完成')\n          this.$message.success('智能体创建完成！')\n          this.$emit('complete', this.createdAgent)\n          this.handleCancel()\n          return\n        }\n\n        console.log(`🎯 CreatorAgentForm: 第二步 - 开始验证所有 ${this.tempWorkflowList.length} 个工作流`)\n\n        // 🔥 第二步：验证所有工作流的完整性（包括刚暂存的）\n        const validationResult = this.validateAllWorkflows()\n        if (!validationResult.isValid) {\n          console.error('🎯 CreatorAgentForm: 工作流验证失败:', validationResult.errors)\n\n          // 🔥 第三步：智能错误处理 - 回填第一个有错误的工作流到表单\n          this.handleValidationErrors(validationResult)\n          this.stepLoading = false\n          return\n        }\n\n        console.log(`🎯 CreatorAgentForm: 第三步 - 验证通过，开始批量保存 ${this.tempWorkflowList.length} 个工作流`)\n\n        // 批量上传文件和保存工作流\n        const savedWorkflows = await this.batchSaveWorkflows()\n\n        this.$message.success(`智能体和 ${savedWorkflows.length} 个工作流创建完成！`)\n\n        // 🔥 进入第三步成功页面，而不是直接关闭弹窗\n        this.currentStep = 2\n        this.scrollToTop()\n\n        console.log('🎯 CreatorAgentForm: 进入第三步成功页面')\n\n      } catch (error) {\n        console.error('🎯 CreatorAgentForm: 完成创建失败:', error)\n        this.$message.error('工作流保存失败: ' + (error.message || '未知错误'))\n      } finally {\n        this.stepLoading = false\n      }\n    },\n\n    // 🔥 关闭弹窗（第三步完成后）\n    handleCloseModal() {\n      console.log('🎯 CreatorAgentForm: 用户点击关闭按钮')\n      this.$emit('complete', this.createdAgent)\n      this.handleCancel()\n    },\n\n    // 🔥 批量保存所有暂存的工作流\n    async batchSaveWorkflows() {\n      const savedWorkflows = []\n      const totalCount = this.tempWorkflowList.length\n\n      for (let i = 0; i < this.tempWorkflowList.length; i++) {\n        const workflow = this.tempWorkflowList[i]\n        console.log(`🎯 CreatorAgentForm: 保存工作流 ${i + 1}/${totalCount}: ${workflow.workflowName}`)\n\n        try {\n          // 🔥 区分已保存和新增工作流的处理逻辑\n          if (workflow.status === 'saved') {\n            // 已保存的工作流：检查是否有实际修改（包括文件修改）\n            const originalWorkflow = workflow.originalWorkflow\n            const hasTextChanges = originalWorkflow && (\n              originalWorkflow.workflowName !== workflow.workflowName ||\n              originalWorkflow.workflowDescription !== workflow.workflowDescription ||\n              originalWorkflow.inputParamsDesc !== workflow.inputParamsDesc\n            )\n\n            // 🔥 检查是否重新选择了文件\n            const hasFileChanges = workflow.workflowFile !== null\n            const hasChanges = hasTextChanges || hasFileChanges\n\n            if (hasChanges) {\n              console.log(`🎯 CreatorAgentForm: 已保存工作流有修改，开始更新: ${workflow.workflowName}`)\n              console.log(`🎯 CreatorAgentForm: 文本修改: ${hasTextChanges}, 文件修改: ${hasFileChanges}`)\n\n              let fileUrl = originalWorkflow.workflowPackage // 默认使用原有文件路径\n\n              // 🔥 如果用户重新选择了文件，先上传新文件\n              if (hasFileChanges) {\n                console.log(`🎯 CreatorAgentForm: 检测到文件变更，上传新文件: ${workflow.workflowFile.name}`)\n                fileUrl = await this.uploadWorkflowFile(workflow.workflowFile, workflow.workflowName)\n                console.log(`🎯 CreatorAgentForm: 新文件上传成功: ${fileUrl}`)\n              }\n\n              // 🔥 调用更新工作流API\n              const updateData = {\n                agentId: this.createdAgent.id, // 🔥 后端必填字段\n                workflowName: workflow.workflowName,\n                workflowDescription: workflow.workflowDescription,\n                inputParamsDesc: workflow.inputParamsDesc,\n                workflowPackage: fileUrl // 🔥 使用新文件路径或原有路径\n              }\n\n              console.log('🎯 CreatorAgentForm: 调用工作流更新API:', updateData)\n              console.log('🎯 CreatorAgentForm: 更新前的原始数据:', originalWorkflow)\n              const response = await updateWorkflow(originalWorkflow.id, updateData)\n              console.log('🎯 CreatorAgentForm: 更新API响应:', response)\n\n              if (response.success) {\n                const updatedWorkflow = response.result || { ...originalWorkflow, ...updateData }\n                savedWorkflows.push(updatedWorkflow)\n\n                // 🔥 同步更新暂存列表中的数据，确保界面显示最新状态\n                this.tempWorkflowList[i] = {\n                  ...workflow,\n                  // 🔥 确保所有字段都同步到最新状态\n                  workflowName: updatedWorkflow.workflowName || workflow.workflowName,\n                  workflowDescription: updatedWorkflow.workflowDescription || workflow.workflowDescription,\n                  inputParamsDesc: updatedWorkflow.inputParamsDesc || workflow.inputParamsDesc,\n                  originalWorkflow: updatedWorkflow // 更新原始数据引用\n                }\n\n                console.log('🎯 CreatorAgentForm: 暂存列表已同步更新:', this.tempWorkflowList[i])\n\n                console.log(`🎯 CreatorAgentForm: 工作流 ${workflow.workflowName} 更新成功`)\n              } else {\n                throw new Error(response.message || '工作流更新API调用失败')\n              }\n            } else {\n              console.log(`🎯 CreatorAgentForm: 已保存工作流无修改，跳过保存: ${workflow.workflowName}`)\n              savedWorkflows.push(originalWorkflow) // 使用原始数据\n            }\n          } else {\n            // 新增的工作流：需要上传文件并创建\n            console.log(`🎯 CreatorAgentForm: 新增工作流，开始上传文件: ${workflow.workflowFile ? workflow.workflowFile.name : 'null'}`)\n            const fileUrl = await this.uploadWorkflowFile(workflow.workflowFile, workflow.workflowName)\n\n            // 保存工作流数据\n            const workflowData = {\n              agentId: this.createdAgent.id,\n              workflowName: workflow.workflowName,\n              workflowDescription: workflow.workflowDescription,\n              inputParamsDesc: workflow.inputParamsDesc, // 🔥 前端验证确保不为空\n              workflowPackage: fileUrl\n            }\n\n            // 🔥 调用工作流创建API\n            console.log('🎯 CreatorAgentForm: 调用工作流创建API:', workflowData)\n            const response = await createWorkflow(workflowData)\n\n            if (response.success) {\n              savedWorkflows.push(response.result)\n              console.log(`🎯 CreatorAgentForm: 工作流 ${workflow.workflowName} API调用成功`)\n            } else {\n              throw new Error(response.message || '工作流创建API调用失败')\n            }\n          }\n\n          console.log(`🎯 CreatorAgentForm: 工作流 ${workflow.workflowName} 保存成功`)\n\n        } catch (error) {\n          console.error(`🎯 CreatorAgentForm: 工作流 ${workflow.workflowName} 保存失败:`, error)\n          throw new Error(`工作流\"${workflow.workflowName}\"保存失败: ${error.message}`)\n        }\n      }\n\n      return savedWorkflows\n    },\n\n    // 🔥 上传单个工作流文件\n    async uploadWorkflowFile(file, workflowName) {\n      return new Promise((resolve, reject) => {\n        const formData = new FormData()\n        formData.append('file', file)\n        formData.append('isup', '1')\n        formData.append('biz', '') // 工作流文件使用空的biz\n\n        // 生成重命名后的文件名\n        const renamedFileName = this.generateWorkflowFileName(file.name, workflowName)\n\n        // 使用fetch进行文件上传\n        fetch(this.uploadAction, {\n          method: 'POST',\n          headers: this.uploadHeaders,\n          body: formData\n        })\n        .then(response => response.json())\n        .then(result => {\n          if (result.success) {\n            console.log(`🎯 CreatorAgentForm: 文件上传成功: ${file.name} -> ${result.message}`)\n            resolve(result.message)\n          } else {\n            reject(new Error(result.message || '文件上传失败'))\n          }\n        })\n        .catch(error => {\n          console.error(`🎯 CreatorAgentForm: 文件上传失败:`, error)\n          reject(error)\n        })\n      })\n    },\n\n    // 🔥 创建智能体（第一步）\n    async createAgentStep(agentData) {\n      try {\n        console.log('🎯 CreatorAgentForm: 调用创建智能体API...', agentData)\n        const response = await createAgent(agentData)\n\n        if (response.success) {\n          console.log('🎯 CreatorAgentForm: 智能体创建成功', response.result)\n\n          // 🔥 确认删除被替换的原始头像文件\n          this.confirmDeleteOriginalFiles()\n\n          return response.result\n        } else {\n          throw new Error(response.message || '创建智能体失败')\n        }\n      } catch (error) {\n        console.error('🎯 CreatorAgentForm: 创建智能体失败:', error)\n        throw error\n      }\n    },\n\n    // 🔥 更新智能体（编辑模式第一步）\n    async updateAgentStep(agentData) {\n      try {\n        console.log('🎯 CreatorAgentForm: 调用更新智能体API...', agentData)\n\n        // 获取智能体ID\n        const agentId = this.agent.id || this.agent.agentId\n        if (!agentId) {\n          throw new Error('智能体ID不存在')\n        }\n\n        const response = await updateAgent(agentId, agentData)\n\n        if (response.success) {\n          console.log('🎯 CreatorAgentForm: 智能体更新成功', response.result)\n\n          // 🔥 确认删除被替换的原始头像文件\n          this.confirmDeleteOriginalFiles()\n\n          return response.result\n        } else {\n          throw new Error(response.message || '更新智能体失败')\n        }\n      } catch (error) {\n        console.error('🎯 CreatorAgentForm: 更新智能体失败:', error)\n        throw error\n      }\n    },\n\n    // 🔥 处理头像值，确保返回字符串格式\n    processAvatarValue(avatarValue) {\n      console.log('🎯 CreatorAgentForm: 处理头像值 - 原始值:', avatarValue, typeof avatarValue)\n\n      // 如果是空值，返回空字符串\n      if (!avatarValue) {\n        return ''\n      }\n\n      // 如果是数组，取第一个元素\n      if (Array.isArray(avatarValue)) {\n        console.log('🎯 CreatorAgentForm: 头像值是数组，取第一个元素:', avatarValue[0])\n        return this.processAvatarValue(avatarValue[0]) // 递归处理\n      }\n\n      // 如果是对象，尝试获取url字段\n      if (typeof avatarValue === 'object' && avatarValue.url) {\n        console.log('🎯 CreatorAgentForm: 头像值是对象，取url字段:', avatarValue.url)\n        return this.processAvatarValue(avatarValue.url) // 递归处理\n      }\n\n      // 如果是字符串，处理URL\n      if (typeof avatarValue === 'string') {\n        return this.extractRelativePath(avatarValue)\n      }\n\n      // 其他情况，转换为字符串\n      console.log('🎯 CreatorAgentForm: 头像值转换为字符串:', String(avatarValue))\n      return String(avatarValue)\n    },\n\n    // 🔥 提取相对路径，避免TOS URL双重包装\n    extractRelativePath(url) {\n      console.log('🎯 CreatorAgentForm: 提取相对路径 - 输入URL:', url)\n\n      // 如果是完整的TOS URL，提取相对路径\n      if (url.includes('aigcview-tos.tos-cn-shanghai.volces.com/')) {\n        // 提取 uploads/ 开头的路径\n        const match = url.match(/uploads\\/[^?]+/)\n        if (match) {\n          const relativePath = match[0]\n          console.log('🎯 CreatorAgentForm: 从TOS URL提取相对路径:', relativePath)\n          return relativePath\n        }\n      }\n\n      // 如果是CDN URL，提取相对路径\n      if (url.includes('cdn.aigcview.com/')) {\n        const match = url.match(/uploads\\/[^?]+/)\n        if (match) {\n          const relativePath = match[0]\n          console.log('🎯 CreatorAgentForm: 从CDN URL提取相对路径:', relativePath)\n          return relativePath\n        }\n      }\n\n      // 如果已经是相对路径（以uploads/开头），直接返回\n      if (url.startsWith('uploads/')) {\n        console.log('🎯 CreatorAgentForm: 已经是相对路径:', url)\n        return url\n      }\n\n      // 其他情况，直接返回原值\n      console.log('🎯 CreatorAgentForm: 无法识别的URL格式，返回原值:', url)\n      return url\n    },\n\n    // 🔥 获取完整的头像URL（用于显示）\n    getFullAvatarUrl(avatarPath) {\n      if (!avatarPath) {\n        return ''\n      }\n\n      // 如果已经是完整URL，直接返回\n      if (avatarPath.startsWith('http')) {\n        return avatarPath\n      }\n\n      // 如果是相对路径，转换为CDN URL\n      if (avatarPath.startsWith('uploads/')) {\n        const cdnUrl = `https://cdn.aigcview.com/${avatarPath}`\n        console.log('🎯 CreatorAgentForm: 转换头像URL:', avatarPath, '->', cdnUrl)\n        return cdnUrl\n      }\n\n      // 其他情况，尝试拼接CDN前缀\n      const cdnUrl = `https://cdn.aigcview.com/${avatarPath}`\n      console.log('🎯 CreatorAgentForm: 拼接头像URL:', avatarPath, '->', cdnUrl)\n      return cdnUrl\n    },\n\n    // 提交表单（保留原方法，但在分步模式下不使用）\n    async handleSubmit() {\n      console.log('🎯 CreatorAgentForm: 开始提交智能体表单...')\n\n      // 🔥 先进行表单验证，验证通过后再上传头像\n      // 🔥 验证前检查头像：如果当前值为空但有待上传文件，则临时设置一个值通过验证\n      const originalAvatar = this.formData.agentAvatar\n      const hasPendingAvatar = this.$refs.avatarUpload && this.$refs.avatarUpload.hasPendingFiles()\n\n      if (!this.formData.agentAvatar && hasPendingAvatar) {\n        console.log('🎯 CreatorAgentForm: 检测到待上传头像，临时设置头像值以通过验证')\n        this.formData.agentAvatar = 'pending_upload' // 临时值\n      }\n\n      // 表单验证\n      this.$refs.form.validate(async (valid) => {\n        // 恢复原始值\n        this.formData.agentAvatar = originalAvatar\n\n        if (valid) {\n          console.log('🎯 CreatorAgentForm: 表单验证通过，开始上传头像...')\n\n          try {\n            // 🔥 表单验证通过后才上传头像\n            await this.uploadPendingImages()\n\n            // 创建提交数据，只包含需要的字段\n            const submitData = {\n              agentName: this.formData.agentName.trim(),\n              agentDescription: this.formData.agentDescription.trim(),\n              agentAvatar: this.processAvatarValue(this.formData.agentAvatar),\n              experienceLink: this.formData.experienceLink.trim(),\n              price: this.formData.price\n            }\n\n            console.log('🎯 CreatorAgentForm: 头像上传成功，提交数据:', submitData)\n            this.$emit('submit', submitData)\n\n          } catch (error) {\n            console.error('🎯 CreatorAgentForm: 头像上传失败:', error)\n            this.$message.error('头像上传失败: ' + (error.message || '未知错误'))\n          }\n        } else {\n          console.log('🎯 CreatorAgentForm: 表单验证失败，不上传头像')\n          this.$message.error('请检查表单信息')\n        }\n      })\n    },\n\n    // 取消\n    handleCancel() {\n      console.log('🎯 CreatorAgentForm: 用户取消，执行清理操作...')\n\n      // 🔥 回滚头像变更\n      this.rollbackChanges()\n\n      // 🔥 清空所有工作流数据，避免数据污染\n      this.clearAllWorkflowData()\n\n      // 🔥 重置表单数据\n      this.resetForm()\n\n      console.log('🎯 CreatorAgentForm: 所有数据已清理完成')\n      this.$emit('close')\n    },\n\n    // 🔥 上传待处理的图片（与后台管理系统逻辑一致）\n    async uploadPendingImages() {\n      console.log('🎯 CreatorAgentForm: 开始上传待处理的头像...')\n\n      if (this.$refs.avatarUpload && this.$refs.avatarUpload.hasPendingFiles()) {\n        console.log('🎯 CreatorAgentForm: 检测到待上传头像，开始上传...')\n\n        try {\n          // 执行上传并获取最终的URL\n          const finalUrl = await this.$refs.avatarUpload.performUpload()\n          console.log('🎯 CreatorAgentForm: 头像上传成功，URL:', finalUrl)\n\n          // 更新formData中的头像URL\n          this.formData.agentAvatar = finalUrl\n\n          return finalUrl\n        } catch (error) {\n          console.error('🎯 CreatorAgentForm: 头像上传失败:', error)\n          throw new Error('头像上传失败: ' + (error.message || '未知错误'))\n        }\n      } else {\n        console.log('🎯 CreatorAgentForm: 没有待上传的头像')\n        return this.formData.agentAvatar\n      }\n    },\n\n    // 🔥 确认删除原始文件（与后台管理系统逻辑一致）\n    confirmDeleteOriginalFiles() {\n      console.log('🎯 CreatorAgentForm: 确认删除被替换的原始头像文件...')\n\n      if (this.$refs.avatarUpload && this.$refs.avatarUpload.confirmDeleteOriginalFiles) {\n        this.$refs.avatarUpload.confirmDeleteOriginalFiles()\n        console.log('🎯 CreatorAgentForm: 原始头像文件删除确认完成')\n      }\n    },\n\n    // 🔥 回滚变更（与后台管理系统逻辑一致）\n    rollbackChanges() {\n      console.log('🎯 CreatorAgentForm: 回滚头像变更...')\n\n      if (this.$refs.avatarUpload && this.$refs.avatarUpload.rollbackChanges) {\n        this.$refs.avatarUpload.rollbackChanges()\n        console.log('🎯 CreatorAgentForm: 头像变更回滚完成')\n      }\n    },\n\n    // 🔥 加载工作流列表\n    async loadWorkflowList(agentId) {\n      if (!agentId) return\n\n      this.workflowLoading = true\n      try {\n        console.log('🎯 CreatorAgentForm: 加载工作流列表...', agentId)\n\n        // 🔥 调用获取工作流列表的API\n        const response = await getWorkflowList(agentId)\n\n        if (response.success) {\n          this.workflowList = response.result || []\n          console.log('🎯 CreatorAgentForm: API返回工作流数据:', this.workflowList)\n\n          // 🔥 编辑模式下：将现有工作流数据回填到暂存区域\n          if (this.mode === 'edit' && this.workflowList.length > 0) {\n            this.convertWorkflowsToTempList(this.workflowList)\n            console.log('🎯 CreatorAgentForm: 编辑模式 - 工作流数据已回填到暂存区域')\n          } else if (this.mode === 'edit') {\n            console.log('🎯 CreatorAgentForm: 编辑模式 - 该智能体暂无工作流数据')\n          }\n        } else {\n          console.error('🎯 CreatorAgentForm: API返回失败:', response.message)\n          this.$message.error(response.message || '获取工作流列表失败')\n          this.workflowList = []\n        }\n\n        console.log('🎯 CreatorAgentForm: 工作流列表加载完成，共', this.workflowList.length, '个工作流')\n      } catch (error) {\n        console.error('🎯 CreatorAgentForm: 加载工作流列表失败:', error)\n        this.$message.error('加载工作流列表失败: ' + (error.message || '网络错误'))\n        this.workflowList = []\n      } finally {\n        this.workflowLoading = false\n      }\n    },\n\n    // 🔥 将现有工作流数据转换为暂存格式（编辑模式回填）\n    convertWorkflowsToTempList(workflows) {\n      console.log('🎯 CreatorAgentForm: 开始转换工作流数据到暂存区域')\n      console.log('🎯 CreatorAgentForm: 原始工作流数据:', workflows)\n\n      // 清空现有暂存数据\n      this.tempWorkflowList = []\n\n      // 转换每个工作流为暂存格式\n      workflows.forEach((workflow, index) => {\n        console.log(`🎯 CreatorAgentForm: 处理工作流 ${index + 1}:`, {\n          id: workflow.id,\n          workflowName: workflow.workflowName,\n          workflowDescription: workflow.workflowDescription,\n          inputParamsDesc: workflow.inputParamsDesc,\n          workflowPackage: workflow.workflowPackage,\n          createTime: workflow.createTime\n        })\n\n        // 🔥 处理已保存工作流的文件信息\n        const packageFileName = workflow.workflowPackage || ''\n        const displayFileName = packageFileName ?\n          (packageFileName.includes('/') ? packageFileName.split('/').pop() : packageFileName) :\n          '已上传文件'\n\n        const tempWorkflow = {\n          id: workflow.id || Date.now() + index,\n          workflowName: workflow.workflowName || '',\n          workflowDescription: workflow.workflowDescription || '',\n          inputParamsDesc: workflow.inputParamsDesc || '', // 🔥 确保包含输入参数说明\n          workflowFile: null, // 已保存的工作流没有文件对象\n          fileName: displayFileName, // 🔥 显示文件名而不是完整路径\n          fileSize: 0, // 已保存的工作流无法获取文件大小\n          workflowPackage: workflow.workflowPackage || '', // 🔥 保存完整的包路径\n          status: 'saved', // 标记为已保存状态\n          createTime: new Date(workflow.createTime || Date.now()),\n          originalWorkflow: workflow // 保存原始工作流数据引用\n        }\n\n        this.tempWorkflowList.push(tempWorkflow)\n        console.log(`🎯 CreatorAgentForm: 工作流 \"${workflow.workflowName}\" 已转换为暂存格式:`, tempWorkflow)\n      })\n\n      console.log(`🎯 CreatorAgentForm: 共转换 ${workflows.length} 个工作流到暂存区域`)\n      console.log('🎯 CreatorAgentForm: 最终暂存列表:', this.tempWorkflowList)\n    },\n\n    // 🔥 工作流文件上传前安全检查\n    beforeWorkflowUpload(file) {\n      console.log('🎯 CreatorAgentForm: 工作流文件安全检查开始:', file.name)\n\n      // 🛡️ 1. 文件名安全检查\n      if (!this.isSecureFileName(file.name)) {\n        this.$message.error('文件名包含不安全字符，请重命名后重试!')\n        return false\n      }\n\n      // 🛡️ 2. 严格检查文件扩展名（只允许.zip）\n      const fileName = file.name.toLowerCase()\n      if (!fileName.endsWith('.zip')) {\n        this.$message.error('只能上传 .zip 格式的文件!')\n        return false\n      }\n\n      // 🛡️ 3. 严格检查MIME类型\n      const allowedMimeTypes = [\n        'application/zip',\n        'application/x-zip-compressed',\n        'application/x-zip'\n      ]\n      if (!allowedMimeTypes.includes(file.type)) {\n        this.$message.error('文件类型不正确，只允许上传ZIP压缩包!')\n        return false\n      }\n\n      // 🛡️ 4. 检查文件大小 (5MB)\n      const maxSize = 5 * 1024 * 1024 // 5MB\n      if (file.size > maxSize) {\n        this.$message.error('文件大小不能超过 5MB!')\n        return false\n      }\n\n      // 🛡️ 5. 检查文件大小不能为0\n      if (file.size === 0) {\n        this.$message.error('文件不能为空!')\n        return false\n      }\n\n      console.log('🎯 CreatorAgentForm: 工作流文件安全检查通过')\n      return false // 阻止默认上传，使用延迟上传机制\n    },\n\n    // 🛡️ 文件名安全检查\n    isSecureFileName(fileName) {\n      // 检查危险字符\n      const dangerousChars = /[<>:\"|?*\\x00-\\x1f]/\n      if (dangerousChars.test(fileName)) {\n        return false\n      }\n\n      // 检查路径遍历\n      if (fileName.includes('..') || fileName.includes('./') || fileName.includes('.\\\\')) {\n        return false\n      }\n\n      // 检查保留名称（Windows）\n      const reservedNames = /^(CON|PRN|AUX|NUL|COM[1-9]|LPT[1-9])(\\.|$)/i\n      if (reservedNames.test(fileName)) {\n        return false\n      }\n\n      // 检查文件名长度\n      if (fileName.length > 255) {\n        return false\n      }\n\n      return true\n    },\n\n    // 🛡️ 验证工作流数据安全性\n    validateWorkflowData() {\n      const { workflowName, workflowDescription, inputParamsDesc } = this.workflowFormData\n\n      // 检查工作流名称\n      if (!workflowName || workflowName.trim().length === 0) {\n        this.$message.error('工作流名称不能为空')\n        return false\n      }\n\n      // 检查名称中的危险字符\n      const namePattern = /^[a-zA-Z0-9\\u4e00-\\u9fa5\\s\\-_]+$/\n      if (!namePattern.test(workflowName)) {\n        this.$message.error('工作流名称包含不允许的字符')\n        return false\n      }\n\n      // 检查工作流描述\n      if (!workflowDescription || workflowDescription.trim().length === 0) {\n        this.$message.error('工作流描述不能为空')\n        return false\n      }\n\n      // 检查描述长度\n      if (workflowDescription.length < 10 || workflowDescription.length > 200) {\n        this.$message.error('工作流描述长度必须在10-200字符之间')\n        return false\n      }\n\n      // 检查是否包含潜在的脚本注入\n      const scriptPattern = /<script|javascript:|on\\w+\\s*=/i\n      if (scriptPattern.test(workflowName) || scriptPattern.test(workflowDescription) || scriptPattern.test(inputParamsDesc)) {\n        this.$message.error('输入内容包含不安全的脚本代码')\n        return false\n      }\n\n      return true\n    },\n\n    // 🔥 工作流文件选择处理（延迟上传机制）\n    handleWorkflowFileSelect(info) {\n      console.log('🎯 CreatorAgentForm: 工作流文件选择:', info.file ? info.file.name : 'no file', 'status:', info.file ? info.file.status : 'no status')\n\n      // 处理文件选择（不进行实际上传）\n      if (info.fileList && info.fileList.length > 0) {\n        const file = info.fileList[0].originFileObj || info.fileList[0]\n\n        // 🛡️ 文件大小验证（5MB限制）\n        const maxSize = 5 * 1024 * 1024 // 5MB\n        if (file.size > maxSize) {\n          console.error('🎯 CreatorAgentForm: 文件大小超过限制:', file.size, '字节')\n          this.$message.error('文件大小不能超过 5MB!')\n\n          // 🔥 立即清理文件状态，不保存任何信息\n          this.workflowFileList = []\n          this.workflowFileInfo = null\n\n          // 强制清空上传组件的文件列表\n          this.$nextTick(() => {\n            // 通过ref清空上传组件（如果有ref的话）\n            const uploadComponent = this.$refs.workflowUpload\n            if (uploadComponent) {\n              uploadComponent.fileList = []\n            }\n          })\n\n          console.log('🎯 CreatorAgentForm: 文件已清理，不保存超大文件')\n          return // 直接返回，不执行后续逻辑\n        }\n\n        // 🛡️ 文件大小不能为0\n        if (file.size === 0) {\n          console.error('🎯 CreatorAgentForm: 文件大小为0')\n          this.$message.error('文件不能为空!')\n\n          // 清理文件状态\n          this.workflowFileList = []\n          this.workflowFileInfo = null\n          return\n        }\n\n        // 🛡️ 文件名安全检查\n        if (!this.isSecureFileName(file.name)) {\n          console.error('🎯 CreatorAgentForm: 文件名不安全:', file.name)\n          this.$message.error('文件名包含不安全字符，请重命名后重试!')\n\n          // 清理文件状态\n          this.workflowFileList = []\n          this.workflowFileInfo = null\n          return\n        }\n\n        // 🛡️ 文件类型检查（扩展名）\n        const fileName = file.name.toLowerCase()\n        if (!fileName.endsWith('.zip')) {\n          console.error('🎯 CreatorAgentForm: 文件扩展名不正确:', file.name)\n          this.$message.error('只能上传 .zip 格式的文件!')\n\n          // 清理文件状态\n          this.workflowFileList = []\n          this.workflowFileInfo = null\n          return\n        }\n\n        // 🛡️ 文件类型检查（MIME类型）\n        const allowedMimeTypes = [\n          'application/zip',\n          'application/x-zip-compressed',\n          'application/x-zip'\n        ]\n        if (!allowedMimeTypes.includes(file.type)) {\n          console.error('🎯 CreatorAgentForm: 文件MIME类型不正确:', file.type)\n          this.$message.error('文件类型不正确，只允许上传ZIP压缩包!')\n\n          // 清理文件状态\n          this.workflowFileList = []\n          this.workflowFileInfo = null\n          return\n        }\n\n        // ✅ 所有验证通过，保存文件信息\n        this.workflowFileList = [file]\n        this.workflowFileInfo = {\n          name: file.name,\n          originalName: file.name,\n          size: file.size,\n          file: file, // 保存原始File对象\n          isSaved: false // 🔥 新选择的文件标记为未保存状态\n        }\n\n        console.log('🎯 CreatorAgentForm: 工作流文件已选择，等待最终上传:', file.name)\n        this.$message.success('工作流文件已选择，将在完成创建时上传')\n\n      } else if (info.fileList && info.fileList.length === 0) {\n        // 文件被移除\n        this.workflowFileList = []\n        this.workflowFileInfo = null\n        console.log('🎯 CreatorAgentForm: 工作流文件已移除')\n      }\n    },\n\n    // 🔥 生成工作流文件名（与后台管理系统一致的重命名逻辑）\n    generateWorkflowFileName(originalName, customWorkflowName = null) {\n      // 获取文件扩展名\n      const lastDotIndex = originalName.lastIndexOf('.')\n      const extension = lastDotIndex > -1 ? originalName.substring(lastDotIndex) : ''\n\n      // 生成时间戳\n      const timestamp = new Date().getTime()\n\n      // 生成随机数\n      const random = Math.floor(Math.random() * 10000).toString().padStart(4, '0')\n\n      // 智能体名称（清理特殊字符）\n      const agentName = this.formData.agentName\n        ? this.formData.agentName.replace(/[^a-zA-Z0-9\\u4e00-\\u9fa5]/g, '_').substring(0, 20)\n        : 'workflow'\n\n      // 工作流名称（清理特殊字符）\n      const workflowName = (customWorkflowName || this.workflowFormData.workflowName)\n        ? (customWorkflowName || this.workflowFormData.workflowName).replace(/[^a-zA-Z0-9\\u4e00-\\u9fa5]/g, '_').substring(0, 20)\n        : 'default'\n\n      // 生成新文件名：智能体名称_工作流名称_时间戳_随机数.扩展名\n      const newFileName = `${agentName}_${workflowName}_${timestamp}_${random}${extension}`\n\n      console.log('🎯 CreatorAgentForm: 文件重命名:', originalName, '->', newFileName)\n      return newFileName\n    },\n\n    // 🔥 移除工作流文件\n    handleWorkflowRemove(file) {\n      console.log('🎯 CreatorAgentForm: 移除工作流文件:', file.name)\n      this.workflowFileList = []\n      this.workflowFormData.workflowPackage = ''\n      this.workflowFileInfo = null\n    },\n\n    // 🔥 移除工作流文件（用于文件信息显示区域）\n    handleRemoveWorkflowFile() {\n      console.log('🎯 CreatorAgentForm: 移除工作流文件')\n      this.workflowFileList = []\n      this.workflowFormData.workflowPackage = ''\n      this.workflowFileInfo = null\n      this.$message.success('工作流文件已移除')\n    },\n\n    // 🔥 格式化文件大小\n    formatFileSize(bytes) {\n      if (!bytes) return '0 B'\n      const k = 1024\n      const sizes = ['B', 'KB', 'MB', 'GB']\n      const i = Math.floor(Math.log(bytes) / Math.log(k))\n      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]\n    },\n\n    // 🔥 提交工作流表单\n    handleWorkflowSubmit() {\n      return new Promise((resolve, reject) => {\n        // 🛡️ 1. 验证表单（Vue 2 + Ant Design Vue 方式）\n        this.$refs.workflowFormRef.validate((valid) => {\n          if (!valid) {\n            this.$message.error('请完善表单信息')\n            reject(new Error('表单验证失败'))\n            return\n          }\n\n          this.performWorkflowSubmit().then(resolve).catch(reject)\n        })\n      })\n    },\n\n    // 🔥 执行工作流提交\n    async performWorkflowSubmit() {\n      try {\n        // 🛡️ 2. 二次验证文件\n        if (this.workflowFileList.length === 0) {\n          this.$message.error('请选择工作流文件')\n          throw new Error('未选择工作流文件')\n        }\n\n        const file = this.workflowFileList[0]\n\n        // 🛡️ 3. 提交前再次安全检查\n        if (!this.isSecureFileName(file.name)) {\n          this.$message.error('文件名不安全，请重新选择文件')\n          throw new Error('文件名不安全')\n        }\n\n        if (!file.name.toLowerCase().endsWith('.zip')) {\n          this.$message.error('只能提交ZIP格式的文件')\n          throw new Error('文件格式不正确')\n        }\n\n        if (file.size > 5 * 1024 * 1024) {\n          this.$message.error('文件大小超过5MB限制')\n          throw new Error('文件大小超限')\n        }\n\n        // 🛡️ 4. 验证表单数据安全性\n        if (!this.validateWorkflowData()) {\n          throw new Error('表单数据验证失败')\n        }\n\n        this.stepLoading = true\n        console.log('🎯 CreatorAgentForm: 提交工作流表单:', this.workflowFormData)\n\n        // TODO: 调用工作流创建API\n        // const workflowData = {\n        //   agentId: this.createdAgent.id,\n        //   workflowName: this.workflowFormData.workflowName,\n        //   workflowDescription: this.workflowFormData.workflowDescription,\n        //   workflowFile: this.workflowFileList[0]\n        // }\n        // const response = await createWorkflow(workflowData)\n\n        // 模拟成功\n        await new Promise(resolve => setTimeout(resolve, 1000))\n\n        this.$message.success('工作流创建成功!')\n\n        // 重新加载工作流列表\n        this.loadWorkflowList(this.createdAgent.id)\n\n        // 重置表单\n        this.resetWorkflowForm()\n\n        return true\n      } catch (error) {\n        console.error('🎯 CreatorAgentForm: 工作流创建失败:', error)\n        this.$message.error('工作流创建失败: ' + (error.message || '未知错误'))\n        throw error\n      } finally {\n        this.stepLoading = false\n      }\n    },\n\n    // 🔥 重置工作流表单\n    resetWorkflowForm() {\n      this.workflowFormData = {\n        workflowName: '',\n        workflowDescription: '',\n        inputParamsDesc: '',\n        workflowPackage: ''\n      }\n      this.workflowFileList = []\n      this.workflowFileInfo = null\n      this.currentWorkflowIndex = -1\n\n      // Vue 2 + Ant Design Vue 方式重置表单\n      this.$nextTick(() => {\n        if (this.$refs.workflowFormRef) {\n          this.$refs.workflowFormRef.resetFields()\n        }\n      })\n\n      console.log('🎯 CreatorAgentForm: 工作流表单已重置')\n    },\n\n    // 🔥 清空所有工作流数据（解决数据污染问题）\n    clearAllWorkflowData() {\n      // 清空暂存工作流列表\n      this.tempWorkflowList = []\n\n      // 重置工作流表单\n      this.resetWorkflowForm()\n\n      // 重置工作流相关状态\n      this.workflowFileList = []\n      this.workflowFileInfo = null\n      this.currentWorkflowIndex = -1\n\n      // 🔥 清空工作流验证错误状态\n      this.workflowValidationErrors = {}\n\n      // 🔥 重置工作流上传状态\n      this.workflowUploading = false\n\n      // 🔥 清空已保存的工作流列表\n      this.workflowList = []\n      this.workflowLoading = false\n\n      console.log('🎯 CreatorAgentForm: 所有工作流数据已清空')\n    },\n\n    // 🔥 处理输入参数说明失焦事件，手动触发验证\n    handleInputParamsBlur() {\n      console.log('🎯 CreatorAgentForm: 输入参数说明失焦，触发验证')\n      this.$nextTick(() => {\n        if (this.$refs.workflowFormRef) {\n          this.$refs.workflowFormRef.validateField('inputParamsDesc', (errorMessage) => {\n            if (errorMessage) {\n              console.log('🎯 CreatorAgentForm: 输入参数说明验证失败:', errorMessage)\n            } else {\n              console.log('🎯 CreatorAgentForm: 输入参数说明验证通过')\n            }\n          })\n        }\n      })\n    },\n\n    // 🔥 滚动弹窗到顶部（提升用户体验）\n    scrollToTop() {\n      this.$nextTick(() => {\n        console.log('🎯 CreatorAgentForm: 开始查找滚动容器...')\n\n        // 🔥 优先查找步骤内容容器（真正的滚动容器）\n        const stepContent = document.querySelector('.step-content')\n\n        if (stepContent) {\n          console.log('🎯 CreatorAgentForm: 找到步骤内容容器:', {\n            scrollTop: stepContent.scrollTop,\n            scrollHeight: stepContent.scrollHeight,\n            clientHeight: stepContent.clientHeight\n          })\n\n          const beforeScrollTop = stepContent.scrollTop\n          stepContent.scrollTop = 0\n          const afterScrollTop = stepContent.scrollTop\n\n          console.log(`🎯 CreatorAgentForm: 步骤内容滚动 - 滚动前: ${beforeScrollTop}, 滚动后: ${afterScrollTop}`)\n\n          if (beforeScrollTop !== afterScrollTop || beforeScrollTop === 0) {\n            console.log('🎯 CreatorAgentForm: 步骤内容滚动成功')\n            return\n          }\n        }\n\n        // 🔥 备用方案：尝试其他可能的滚动容器\n        const selectors = [\n          '.creator-agent-modal .ant-modal-body',\n          '.ant-modal-body',\n          '.creator-agent-modal .ant-modal-content',\n          '.ant-modal-content',\n          '.creator-agent-modal',\n          '.ant-modal-wrap'\n        ]\n\n        let scrollContainer = null\n        let usedSelector = ''\n\n        for (const selector of selectors) {\n          const element = document.querySelector(selector)\n          if (element) {\n            console.log(`🎯 CreatorAgentForm: 找到元素 ${selector}:`, element)\n            console.log(`🎯 CreatorAgentForm: 元素当前 scrollTop: ${element.scrollTop}, scrollHeight: ${element.scrollHeight}, clientHeight: ${element.clientHeight}`)\n\n            // 检查元素是否可滚动\n            if (element.scrollHeight > element.clientHeight || element.scrollTop > 0) {\n              scrollContainer = element\n              usedSelector = selector\n              break\n            }\n          }\n        }\n\n        if (scrollContainer) {\n          const beforeScrollTop = scrollContainer.scrollTop\n          scrollContainer.scrollTop = 0\n          const afterScrollTop = scrollContainer.scrollTop\n\n          console.log(`🎯 CreatorAgentForm: 使用选择器 ${usedSelector} 滚动到顶部`)\n          console.log(`🎯 CreatorAgentForm: 滚动前: ${beforeScrollTop}, 滚动后: ${afterScrollTop}`)\n\n          if (beforeScrollTop === afterScrollTop && beforeScrollTop > 0) {\n            console.warn('🎯 CreatorAgentForm: 滚动可能没有生效，尝试其他方法')\n            // 尝试平滑滚动\n            scrollContainer.scrollTo({ top: 0, behavior: 'smooth' })\n          }\n        } else {\n          console.warn('🎯 CreatorAgentForm: 未找到可滚动的容器，延迟重试')\n          // 🔥 延迟重试，并尝试更多选择器\n          setTimeout(() => {\n            const allModalElements = document.querySelectorAll('[class*=\"modal\"]')\n            console.log('🎯 CreatorAgentForm: 所有modal相关元素:', allModalElements)\n\n            for (const element of allModalElements) {\n              if (element.scrollHeight > element.clientHeight || element.scrollTop > 0) {\n                element.scrollTop = 0\n                console.log('🎯 CreatorAgentForm: 延迟重试成功，使用元素:', element)\n                return\n              }\n            }\n\n            console.error('🎯 CreatorAgentForm: 延迟重试仍未找到可滚动容器')\n          }, 200)\n        }\n      })\n    },\n\n    // 🔥 新增下一个工作流（前端暂存机制）\n    addNextWorkflow() {\n      console.log('🎯 CreatorAgentForm: 新增下一个工作流')\n\n      // 🔥 自动暂存当前数据（智能保护机制）\n      this.autoSaveCurrentWorkflow()\n\n      // 重置表单，准备添加新工作流\n      this.resetWorkflowForm()\n      this.currentWorkflowIndex = -1 // 设置为新建状态\n\n      // 🔥 滚动到顶部，确保用户看到新工作流表单的开始\n      this.scrollToTop()\n\n      this.$message.success('可以继续添加新工作流')\n    },\n\n    // 🔥 验证当前工作流数据\n    validateCurrentWorkflowData() {\n      // 检查工作流名称\n      if (!this.workflowFormData.workflowName.trim()) {\n        this.$message.error('请输入工作流名称')\n        return false\n      }\n\n      // 🔥 严格检查工作流名称长度（防止数据库字段溢出）\n      if (this.workflowFormData.workflowName.trim().length > 30) {\n        this.$message.error('工作流名称不能超过30个字符')\n        return false\n      }\n\n      // 检查工作流描述\n      if (!this.workflowFormData.workflowDescription.trim()) {\n        this.$message.error('请输入工作流描述')\n        return false\n      }\n\n      // 🔥 严格检查工作流描述长度\n      if (this.workflowFormData.workflowDescription.trim().length > 200) {\n        this.$message.error('工作流描述不能超过200个字符')\n        return false\n      }\n\n      // 🔥 检查输入参数说明（后端必填字段）\n      if (!this.workflowFormData.inputParamsDesc.trim()) {\n        this.$message.error('请输入参数说明')\n        return false\n      }\n\n      // 🔥 检查输入参数说明格式（与后台管理一致的正则验证）\n      const inputParamsPattern = /^[^:：]+[:：](?:\"[^\"]*\"|'[^']*'|[^:：,，]+)(?:[,，][^:：]+[:：](?:\"[^\"]*\"|'[^']*'|[^:：,，]+))*$/\n      if (!inputParamsPattern.test(this.workflowFormData.inputParamsDesc.trim())) {\n        this.$message.error('请按照格式填写：参数:值 或 参数:\"值\" 或 参数:\\'值\\'')\n        return false\n      }\n\n      // 🔥 检查工作流文件（区分新增和已保存工作流）\n      console.log('🎯 CreatorAgentForm: 文件验证 - workflowFileList.length:', this.workflowFileList.length)\n      console.log('🎯 CreatorAgentForm: 文件验证 - workflowFileInfo:', this.workflowFileInfo)\n      console.log('🎯 CreatorAgentForm: 文件验证 - workflowFileInfo.isSaved:', this.workflowFileInfo && this.workflowFileInfo.isSaved)\n\n      // 🔥 文件验证逻辑：\n      // 1. 如果有新选择的文件（workflowFileList.length > 0），验证通过\n      // 2. 如果没有新文件，但有已保存的文件信息（workflowFileInfo.isSaved），验证通过\n      // 3. 其他情况验证失败\n      const hasNewFile = this.workflowFileList.length > 0\n      const hasSavedFile = this.workflowFileInfo && this.workflowFileInfo.isSaved\n\n      if (!hasNewFile && !hasSavedFile) {\n        this.$message.error('工作流文件为必填项，请上传ZIP压缩包')\n        return false\n      }\n\n      return true\n    },\n\n    // 🔥 保存当前工作流到暂存列表（支持任意字段的暂存）\n    saveCurrentWorkflowToTemp() {\n      // 🔥 生成默认工作流名称（如果用户没有输入）\n      let workflowName = this.workflowFormData.workflowName.trim()\n      if (!workflowName) {\n        // 如果没有名称，生成一个默认名称\n        const timestamp = new Date().toLocaleString('zh-CN')\n        workflowName = `工作流_${timestamp}`\n      }\n\n      // 🔥 智能处理工作流状态和文件信息\n      const isEditingExisting = this.currentWorkflowIndex >= 0\n      const existingWorkflow = isEditingExisting ? this.tempWorkflowList[this.currentWorkflowIndex] : null\n\n      console.log('🎯 CreatorAgentForm: saveCurrentWorkflowToTemp - isEditingExisting:', isEditingExisting)\n      console.log('🎯 CreatorAgentForm: saveCurrentWorkflowToTemp - existingWorkflow:', existingWorkflow)\n      console.log('🎯 CreatorAgentForm: saveCurrentWorkflowToTemp - existingWorkflow.status:', existingWorkflow ? existingWorkflow.status : 'null')\n\n      // 🔥 保持已保存工作流的状态和文件信息\n      const workflowData = {\n        id: isEditingExisting ? existingWorkflow.id : Date.now(),\n        workflowName: workflowName,\n        workflowDescription: this.workflowFormData.workflowDescription.trim(),\n        inputParamsDesc: this.workflowFormData.inputParamsDesc.trim(),\n        workflowFile: this.workflowFileList.length > 0 ? this.workflowFileList[0] : (existingWorkflow ? existingWorkflow.workflowFile : null),\n        fileName: this.workflowFileInfo ? this.workflowFileInfo.name : (existingWorkflow ? existingWorkflow.fileName : ''),\n        fileSize: this.workflowFileInfo ? this.workflowFileInfo.size : (existingWorkflow ? existingWorkflow.fileSize : 0),\n        // 🔥 保持已保存工作流的状态，新工作流设为draft\n        status: (existingWorkflow && existingWorkflow.status === 'saved') ? 'saved' : 'draft',\n        createTime: isEditingExisting ? existingWorkflow.createTime : new Date(),\n        // 🔥 保持已保存工作流的包路径信息\n        workflowPackage: existingWorkflow ? existingWorkflow.workflowPackage : '',\n        originalWorkflow: existingWorkflow ? existingWorkflow.originalWorkflow : null\n      }\n\n      console.log('🎯 CreatorAgentForm: saveCurrentWorkflowToTemp - 创建的workflowData:', workflowData)\n      console.log('🎯 CreatorAgentForm: saveCurrentWorkflowToTemp - workflowData.status:', workflowData.status)\n      console.log('🎯 CreatorAgentForm: saveCurrentWorkflowToTemp - workflowData.workflowPackage:', workflowData.workflowPackage)\n\n      if (this.currentWorkflowIndex >= 0) {\n        // 更新现有工作流\n        console.log('🎯 CreatorAgentForm: 更新现有工作流，索引:', this.currentWorkflowIndex)\n        this.tempWorkflowList.splice(this.currentWorkflowIndex, 1, workflowData)\n        console.log('🎯 CreatorAgentForm: 更新暂存工作流:', workflowData)\n      } else {\n        // 添加新工作流\n        console.log('🎯 CreatorAgentForm: 添加新工作流，当前列表长度:', this.tempWorkflowList.length)\n        this.tempWorkflowList.push(workflowData)\n        console.log('🎯 CreatorAgentForm: 添加暂存工作流:', workflowData)\n        console.log('🎯 CreatorAgentForm: 添加后列表长度:', this.tempWorkflowList.length)\n      }\n\n      // 🔥 实时更新验证状态\n      this.updateWorkflowValidation(workflowData)\n    },\n\n    // 🔥 从暂存列表加载工作流数据到表单（带自动暂存保护）\n    loadWorkflowFromTemp(index, skipAutoSave = false) {\n      if (index < 0 || index >= this.tempWorkflowList.length) {\n        console.error('🎯 CreatorAgentForm: 无效的工作流索引:', index)\n        return\n      }\n\n      // 🔥 自动暂存当前正在编辑的数据（数据保护机制）\n      // 在验证错误处理时跳过自动暂存，避免循环\n      if (!skipAutoSave) {\n        this.autoSaveCurrentWorkflow()\n      }\n\n      const workflow = this.tempWorkflowList[index]\n      console.log('🎯 CreatorAgentForm: 加载暂存工作流:', workflow)\n\n      // 加载表单数据\n      this.workflowFormData = {\n        workflowName: workflow.workflowName,\n        workflowDescription: workflow.workflowDescription,\n        inputParamsDesc: workflow.inputParamsDesc || '',\n        workflowPackage: ''\n      }\n\n      // 🔥 加载文件数据（区分新增和已保存工作流）\n      if (workflow.status === 'saved' && workflow.fileName) {\n        // 已保存的工作流：显示文件信息但没有File对象\n        this.workflowFileList = [] // 已保存的工作流没有File对象\n        this.workflowFileInfo = {\n          name: workflow.fileName,\n          originalName: workflow.fileName,\n          size: workflow.fileSize || 0,\n          file: null, // 已保存的工作流没有File对象\n          isSaved: true, // 标记为已保存状态\n          packagePath: workflow.workflowPackage || '' // 保存包路径\n        }\n        console.log('🎯 CreatorAgentForm: 加载已保存工作流的文件信息:', this.workflowFileInfo)\n      } else if (workflow.workflowFile && workflow.fileName) {\n        // 新增的工作流：有完整的File对象\n        this.workflowFileList = [workflow.workflowFile]\n        this.workflowFileInfo = {\n          name: workflow.fileName,\n          originalName: workflow.fileName,\n          size: workflow.fileSize || 0,\n          file: workflow.workflowFile,\n          isSaved: false\n        }\n        console.log('🎯 CreatorAgentForm: 加载新增工作流的文件信息:', this.workflowFileInfo)\n      } else {\n        // 没有文件信息\n        this.workflowFileList = []\n        this.workflowFileInfo = null\n        console.log('🎯 CreatorAgentForm: 工作流没有文件信息')\n      }\n\n      // 设置当前编辑索引\n      this.currentWorkflowIndex = index\n\n      // 在验证错误处理时不显示加载提示\n      if (!skipAutoSave) {\n        this.$message.info(`已加载工作流: ${workflow.workflowName}`)\n\n        // 🔥 滚动到第二步顶部，确保用户看到完整的工作流表单\n        this.scrollToTop()\n      }\n    },\n\n    // 🔥 自动暂存当前工作流数据（智能数据保护）\n    autoSaveCurrentWorkflow() {\n      // 🔥 检查当前是否有任意工作流数据需要保存（包括已保存的工作流）\n      const hasCurrentData = this.workflowFormData.workflowName.trim() ||\n                             this.workflowFormData.workflowDescription.trim() ||\n                             this.workflowFileList.length > 0 ||\n                             (this.workflowFileInfo && this.workflowFileInfo.isSaved)\n\n      if (!hasCurrentData) {\n        console.log('🎯 CreatorAgentForm: 当前无数据，跳过自动暂存')\n        return false // 返回false表示没有暂存任何数据\n      }\n\n      // 如果当前正在编辑已存在的工作流，直接更新\n      if (this.currentWorkflowIndex >= 0) {\n        console.log('🎯 CreatorAgentForm: 自动更新当前编辑的工作流')\n        this.saveCurrentWorkflowToTemp()\n        return true // 返回true表示已暂存数据\n      }\n\n      // 🔥 如果是新工作流，只要有任意数据就自动暂存\n      console.log('🎯 CreatorAgentForm: 自动暂存新工作流数据（有任意输入）')\n      this.saveCurrentWorkflowToTemp()\n\n      // 在完成创建流程中不显示暂存提示，避免干扰用户\n      if (!this.stepLoading) {\n        this.$message.info('当前工作流数据已自动暂存')\n      }\n\n      return true // 返回true表示已暂存数据\n    },\n\n    // 🔥 验证所有工作流的完整性\n    validateAllWorkflows() {\n      console.log('🎯 CreatorAgentForm: 开始验证所有工作流的完整性')\n\n      // 清空之前的验证错误\n      this.workflowValidationErrors = {}\n\n      let isAllValid = true\n      let invalidCount = 0\n      const allErrors = []\n\n      // 遍历所有暂存的工作流\n      for (let i = 0; i < this.tempWorkflowList.length; i++) {\n        const workflow = this.tempWorkflowList[i]\n        const errors = []\n\n        // 验证工作流名称\n        if (!workflow.workflowName || !workflow.workflowName.trim()) {\n          errors.push('缺少工作流名称')\n        }\n\n        // 验证工作流描述\n        if (!workflow.workflowDescription || !workflow.workflowDescription.trim()) {\n          errors.push('缺少工作流描述')\n        }\n\n        // 🔥 验证输入参数说明（后端必填字段）\n        if (!workflow.inputParamsDesc || !workflow.inputParamsDesc.trim()) {\n          errors.push('缺少参数说明')\n        } else {\n          // 🔥 验证输入参数说明格式（与后台管理一致的正则验证）\n          const inputParamsPattern = /^[^:：]+[:：](?:\"[^\"]*\"|'[^']*'|[^:：,，]+)(?:[,，][^:：]+[:：](?:\"[^\"]*\"|'[^']*'|[^:：,，]+))*$/\n          if (!inputParamsPattern.test(workflow.inputParamsDesc.trim())) {\n            errors.push('参数说明格式不正确')\n          }\n        }\n\n        // 🔥 验证工作流文件（区分新增和已保存工作流）\n        if (workflow.status === 'saved') {\n          // 已保存的工作流：只需要有文件名或包路径\n          if (!workflow.fileName && !workflow.workflowPackage) {\n            errors.push('缺少压缩包文件')\n          }\n        } else {\n          // 新增的工作流：需要有File对象和文件名\n          if (!workflow.workflowFile || !workflow.fileName) {\n            errors.push('缺少压缩包文件')\n          }\n        }\n\n        // 记录验证结果\n        const isValid = errors.length === 0\n        this.workflowValidationErrors[workflow.id] = {\n          errors: errors,\n          isValid: isValid,\n          workflowName: workflow.workflowName || `工作流${i + 1}`\n        }\n\n        if (!isValid) {\n          isAllValid = false\n          invalidCount++\n          allErrors.push(`${workflow.workflowName || `工作流${i + 1}`}: ${errors.join('、')}`)\n        }\n      }\n\n      const result = {\n        isValid: isAllValid,\n        invalidCount: invalidCount,\n        errors: allErrors,\n        validationDetails: this.workflowValidationErrors\n      }\n\n      console.log('🎯 CreatorAgentForm: 工作流验证结果:', result)\n      return result\n    },\n\n    // 🔥 实时更新单个工作流的验证状态\n    updateWorkflowValidation(workflow) {\n      const errors = []\n\n      // 验证工作流名称\n      if (!workflow.workflowName || !workflow.workflowName.trim()) {\n        errors.push('缺少工作流名称')\n      }\n\n      // 验证工作流描述\n      if (!workflow.workflowDescription || !workflow.workflowDescription.trim()) {\n        errors.push('缺少工作流描述')\n      }\n\n      // 🔥 验证输入参数说明（与批量验证保持一致）\n      if (!workflow.inputParamsDesc || !workflow.inputParamsDesc.trim()) {\n        errors.push('缺少参数说明')\n      } else {\n        // 🔥 验证输入参数说明格式（与后台管理一致的正则验证）\n        const inputParamsPattern = /^[^:：]+[:：](?:\"[^\"]*\"|'[^']*'|[^:：,，]+)(?:[,，][^:：]+[:：](?:\"[^\"]*\"|'[^']*'|[^:：,，]+))*$/\n        if (!inputParamsPattern.test(workflow.inputParamsDesc.trim())) {\n          errors.push('参数说明格式不正确')\n        }\n      }\n\n      // 🔥 验证工作流文件（区分新增和已保存工作流）\n      if (workflow.status === 'saved') {\n        // 已保存的工作流：只需要有文件名或包路径\n        if (!workflow.fileName && !workflow.workflowPackage) {\n          errors.push('缺少压缩包文件')\n        }\n      } else {\n        // 新增的工作流：需要有File对象和文件名\n        if (!workflow.workflowFile || !workflow.fileName) {\n          errors.push('缺少压缩包文件')\n        }\n      }\n\n      // 更新验证状态\n      const isValid = errors.length === 0\n      this.$set(this.workflowValidationErrors, workflow.id, {\n        errors: errors,\n        isValid: isValid,\n        workflowName: workflow.workflowName || '未命名工作流'\n      })\n\n      console.log(`🎯 CreatorAgentForm: 更新工作流验证状态 - ${workflow.workflowName}: ${isValid ? '有效' : '无效'}`, errors)\n    },\n\n    // 🔥 智能错误处理 - 回填第一个有错误的工作流到表单\n    handleValidationErrors(validationResult) {\n      console.log('🎯 CreatorAgentForm: 开始智能错误处理')\n\n      // 找到第一个有错误的工作流\n      let firstErrorWorkflowIndex = -1\n      let firstErrorWorkflow = null\n\n      for (let i = 0; i < this.tempWorkflowList.length; i++) {\n        const workflow = this.tempWorkflowList[i]\n        const validationInfo = validationResult.validationDetails[workflow.id]\n\n        if (validationInfo && !validationInfo.isValid) {\n          firstErrorWorkflowIndex = i\n          firstErrorWorkflow = workflow\n          break\n        }\n      }\n\n      if (firstErrorWorkflowIndex >= 0 && firstErrorWorkflow) {\n        console.log(`🎯 CreatorAgentForm: 找到第一个错误工作流: ${firstErrorWorkflow.workflowName} (索引: ${firstErrorWorkflowIndex})`)\n\n        // 回填错误工作流到表单中（跳过自动暂存避免循环）\n        this.loadWorkflowFromTemp(firstErrorWorkflowIndex, true)\n\n        // 显示详细的错误信息\n        const errorInfo = validationResult.validationDetails[firstErrorWorkflow.id]\n        const errorMessage = `工作流\"${firstErrorWorkflow.workflowName}\"缺少必填信息：${errorInfo.errors.join('、')}`\n\n        this.$message.error(errorMessage)\n\n        // 显示总体错误统计\n        setTimeout(() => {\n          this.$message.warning(`共有 ${validationResult.invalidCount} 个工作流存在问题，请逐一完善后再提交`)\n        }, 1000)\n\n        console.log('🎯 CreatorAgentForm: 错误工作流已回填到表单，用户可以直接修改')\n      } else {\n        // 如果没找到错误工作流（理论上不应该发生）\n        this.$message.error(`请完善工作流信息后再提交，共有 ${validationResult.invalidCount} 个工作流存在问题`)\n      }\n    },\n\n    // 🔥 从暂存列表删除工作流\n    deleteWorkflowFromTemp(index) {\n      if (index < 0 || index >= this.tempWorkflowList.length) {\n        console.error('🎯 CreatorAgentForm: 无效的工作流索引:', index)\n        return\n      }\n\n      const workflow = this.tempWorkflowList[index]\n      this.$confirm({\n        title: '删除工作流',\n        content: `确定要删除工作流\"${workflow.workflowName}\"吗？`,\n        onOk: () => {\n          this.tempWorkflowList.splice(index, 1)\n          console.log('🎯 CreatorAgentForm: 删除暂存工作流:', workflow)\n\n          // 如果删除的是当前正在编辑的工作流，重置表单\n          if (this.currentWorkflowIndex === index) {\n            this.resetWorkflowForm()\n            this.currentWorkflowIndex = -1\n            // 🔥 删除当前编辑的工作流后滚动到顶部\n            this.scrollToTop()\n          } else if (this.currentWorkflowIndex > index) {\n            // 调整当前编辑索引\n            this.currentWorkflowIndex--\n          }\n\n          this.$message.success('工作流已删除')\n        }\n      })\n    },\n\n    // 格式化日期\n    formatDate(date) {\n      if (!date) return ''\n      return new Date(date).toLocaleString('zh-CN')\n    }\n  }\n}\n</script>\n\n<style lang=\"less\" scoped>\n// 上传提示\n.upload-tips {\n  margin-top: 8px;\n\n  p {\n    margin: 0;\n    font-size: 12px;\n    color: #666;\n    line-height: 1.4;\n  }\n\n  .tip-note {\n    color: #1890ff;\n    font-weight: 500;\n    margin-top: 4px;\n  }\n}\n\n// 字段提示\n.field-tips {\n  margin-top: 4px;\n  font-size: 12px;\n  color: #999;\n  line-height: 1.4;\n}\n\n// 表单通知\n.form-notice {\n  margin-top: 24px;\n\n  .ant-alert {\n    border-radius: 6px;\n\n    .ant-alert-message {\n      font-weight: 600;\n    }\n\n    .ant-alert-description {\n      margin-top: 4px;\n      line-height: 1.5;\n    }\n  }\n}\n\n// 表单样式优化\n.ant-form-model {\n  .ant-form-model-item {\n    margin-bottom: 24px;\n\n    .ant-form-model-item-label {\n      label {\n        font-weight: 600;\n        color: #1f2937;\n\n        &::after {\n          content: '';\n        }\n      }\n    }\n\n    .ant-input,\n    .ant-input-number,\n    .ant-select-selection,\n    .ant-input-number-input {\n      border-radius: 6px;\n      border-color: #d1d5db;\n\n      &:focus,\n      &:hover {\n        border-color: #1890ff;\n      }\n    }\n\n    .ant-textarea {\n      border-radius: 6px;\n      border-color: #d1d5db;\n\n      &:focus,\n      &:hover {\n        border-color: #1890ff;\n      }\n    }\n\n    .ant-input-number {\n      width: 100%;\n\n      .ant-input-number-input {\n        border: none;\n      }\n    }\n  }\n}\n\n// 模态框样式\n.ant-modal {\n  .ant-modal-header {\n    border-radius: 8px 8px 0 0;\n\n    .ant-modal-title {\n      font-weight: 600;\n      color: #1f2937;\n    }\n  }\n\n  .ant-modal-body {\n    padding: 24px;\n  }\n\n  .ant-modal-footer {\n    border-radius: 0 0 8px 8px;\n\n    .ant-btn {\n      border-radius: 6px;\n\n      &.ant-btn-primary {\n        background: #1890ff;\n        border-color: #1890ff;\n\n        &:hover {\n          background: #40a9ff;\n          border-color: #40a9ff;\n        }\n      }\n    }\n  }\n}\n\n// 响应式设计\n@media (max-width: 768px) {\n  .ant-modal {\n    margin: 0;\n    max-width: 100vw;\n\n    .ant-modal-content {\n      border-radius: 0;\n    }\n  }\n\n  .ant-form-model {\n    .ant-form-model-item {\n      .ant-form-model-item-label {\n        text-align: left;\n      }\n    }\n  }\n}\n\n// 弹窗样式\n:global(.creator-agent-modal) {\n  .ant-modal-header {\n    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n    border-bottom: none;\n    border-radius: 12px 12px 0 0;\n\n    .ant-modal-title {\n      color: white;\n      font-weight: 600;\n      font-size: 20px;\n    }\n  }\n\n  .ant-modal-close {\n    color: white;\n\n    &:hover {\n      color: rgba(255, 255, 255, 0.8);\n    }\n  }\n\n  .ant-modal-body {\n    padding: 0;\n    max-height: 80vh;\n    overflow: hidden;\n  }\n\n  .ant-modal-content {\n    border-radius: 12px;\n    overflow: hidden;\n    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);\n  }\n}\n\n// 步骤条样式\n.step-header {\n  padding: 24px 32px;\n  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);\n  border-bottom: 1px solid #e2e8f0;\n\n  .custom-steps {\n    :global(.ant-steps-item-title) {\n      font-weight: 600;\n      font-size: 16px;\n    }\n\n    :global(.ant-steps-item-description) {\n      color: #64748b;\n    }\n\n    :global(.ant-steps-item-process .ant-steps-item-icon) {\n      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n      border-color: #667eea;\n    }\n\n    :global(.ant-steps-item-finish .ant-steps-item-icon) {\n      background: #10b981;\n      border-color: #10b981;\n    }\n  }\n}\n\n// 步骤内容\n.step-content {\n  min-height: 500px;\n  max-height: 60vh;\n  overflow-y: auto;\n}\n\n.step-panel {\n  padding: 32px;\n\n  .panel-header {\n    text-align: center;\n    margin-bottom: 32px;\n\n    .panel-title {\n      font-size: 24px;\n      font-weight: 700;\n      color: #1e293b;\n      margin: 0 0 8px 0;\n\n      .anticon {\n        margin-right: 12px;\n        color: #667eea;\n      }\n    }\n\n    .panel-desc {\n      font-size: 16px;\n      color: #64748b;\n      margin: 0;\n    }\n  }\n}\n\n// 现代化表单样式\n.modern-form {\n  .form-card {\n    background: white;\n    border-radius: 12px;\n    padding: 24px;\n    margin-bottom: 20px;\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\n    border: 1px solid #e2e8f0;\n    transition: all 0.3s ease;\n\n    &:hover {\n      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);\n      border-color: #cbd5e1;\n    }\n\n    .field-label {\n      font-size: 16px;\n      font-weight: 600;\n      color: #1e293b;\n      margin-bottom: 12px;\n      display: flex;\n      align-items: center;\n\n      .anticon {\n        margin-right: 8px;\n        color: #667eea;\n        font-size: 18px;\n      }\n\n      .optional {\n        font-size: 14px;\n        font-weight: 400;\n        color: #94a3b8;\n        margin-left: 8px;\n      }\n\n      // 🔥 必填星号样式\n      .required-star {\n        color: #ff4d4f;\n        font-size: 16px;\n        font-weight: 600;\n        margin-left: 4px;\n      }\n    }\n\n    .modern-input,\n    .modern-textarea,\n    .modern-input-number {\n      border-radius: 8px;\n      border: 2px solid #e2e8f0;\n      transition: all 0.3s ease;\n\n      &:hover {\n        border-color: #cbd5e1;\n      }\n\n      &:focus,\n      &.ant-input-focused {\n        border-color: #667eea;\n        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);\n      }\n    }\n\n    .modern-input-number {\n      width: 100%;\n\n      :global(.ant-input-number-input) {\n        border: none;\n        border-radius: 6px;\n      }\n    }\n\n    .field-tips {\n      margin-top: 8px;\n      font-size: 14px;\n      color: #64748b;\n      line-height: 1.5;\n    }\n  }\n}\n\n// 步骤底部按钮\n.step-footer {\n  padding: 24px 32px;\n  border-top: 1px solid #e2e8f0;\n  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n\n  .ant-btn {\n    height: 44px;\n    padding: 0 24px;\n    font-size: 16px;\n    font-weight: 500;\n    border-radius: 8px;\n    transition: all 0.3s ease;\n\n    &:not(.ant-btn-primary) {\n      border: 2px solid #e2e8f0;\n      color: #64748b;\n\n      &:hover {\n        border-color: #cbd5e1;\n        color: #475569;\n      }\n    }\n  }\n\n  .step-actions {\n    display: flex;\n    gap: 12px;\n    align-items: center;\n  }\n\n  .next-btn,\n  .complete-btn {\n    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n    border: none;\n    color: white;\n\n    &:hover {\n      background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);\n      transform: translateY(-1px);\n      box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);\n    }\n\n    .anticon {\n      margin-left: 8px;\n    }\n  }\n}\n\n// 已创建智能体信息\n.created-agent-info {\n  margin-bottom: 24px;\n\n  .agent-summary {\n    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);\n    border: 2px solid #0ea5e9;\n    border-radius: 12px;\n    padding: 20px;\n    display: flex;\n    align-items: center;\n    gap: 16px;\n\n    .agent-avatar {\n      width: 60px;\n      height: 60px;\n      border-radius: 12px;\n      overflow: hidden;\n      background: white;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      border: 2px solid #0ea5e9;\n\n      img {\n        width: 100%;\n        height: 100%;\n        object-fit: cover;\n      }\n\n      .anticon {\n        font-size: 24px;\n        color: #0ea5e9;\n      }\n    }\n\n    .agent-details {\n      flex: 1;\n\n      h4 {\n        margin: 0 0 4px 0;\n        font-size: 18px;\n        font-weight: 700;\n        color: #0c4a6e;\n      }\n\n      p {\n        margin: 0 0 8px 0;\n        font-size: 14px;\n        color: #0369a1;\n        line-height: 1.4;\n      }\n\n      .agent-price {\n        font-size: 16px;\n        font-weight: 600;\n        color: #059669;\n        background: rgba(5, 150, 105, 0.1);\n        padding: 4px 8px;\n        border-radius: 6px;\n      }\n    }\n  }\n}\n\n// 工作流管理区域\n.workflow-section {\n  border: 1px solid #e8eaec;\n  border-radius: 8px;\n  padding: 16px;\n  background: #fafbfc;\n\n  .workflow-notice {\n    margin-bottom: 16px;\n  }\n\n  .workflow-form {\n    .form-header {\n      margin-bottom: 24px;\n      text-align: center;\n\n      h3 {\n        margin: 0 0 8px 0;\n        font-size: 18px;\n        font-weight: 600;\n        color: #333;\n\n        .anticon {\n          margin-right: 8px;\n          color: #1890ff;\n        }\n      }\n\n      p {\n        margin: 0;\n        font-size: 14px;\n        color: #666;\n      }\n    }\n\n    .ant-form-item {\n      margin-bottom: 20px;\n    }\n\n    .upload-tip {\n      margin-top: 8px;\n      padding: 8px 12px;\n      font-size: 12px;\n      color: #666;\n      background: #fff7e6;\n      border: 1px solid #ffd591;\n      border-radius: 4px;\n      line-height: 1.4;\n\n      .anticon {\n        margin-right: 4px;\n      }\n\n      strong {\n        color: #fa8c16;\n      }\n    }\n  }\n\n  .add-next-workflow {\n    margin-top: 20px;\n    padding-top: 16px;\n    border-top: 1px dashed #e8eaec;\n\n    .add-next-btn {\n      height: 48px;\n      border: 2px dashed #52c41a;\n      color: #52c41a;\n      font-size: 14px;\n      font-weight: 500;\n      transition: all 0.3s;\n\n      &:hover {\n        border-color: #389e0d;\n        color: #389e0d;\n        background: #f6ffed;\n      }\n\n      .anticon {\n        margin-right: 8px;\n      }\n    }\n  }\n\n  .workflow-header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    margin-bottom: 16px;\n\n    .workflow-title {\n      font-size: 14px;\n      font-weight: 600;\n      color: #333;\n\n      .anticon {\n        margin-right: 6px;\n        color: #1890ff;\n      }\n    }\n  }\n\n  .workflow-list {\n    .workflow-item {\n      background: white;\n      border: 1px solid #e8eaec;\n      border-radius: 6px;\n      padding: 12px;\n      margin-bottom: 8px;\n      display: flex;\n      justify-content: space-between;\n      align-items: flex-start;\n      transition: all 0.3s ease;\n\n      &:hover {\n        border-color: #1890ff;\n        box-shadow: 0 2px 8px rgba(24, 144, 255, 0.1);\n      }\n\n      &:last-child {\n        margin-bottom: 0;\n      }\n\n      &.pending {\n        border-color: #faad14;\n        background: #fffbe6;\n\n        &:hover {\n          border-color: #faad14;\n          box-shadow: 0 2px 8px rgba(250, 173, 20, 0.2);\n        }\n      }\n\n      .workflow-info {\n        flex: 1;\n\n        .workflow-name {\n          margin: 0 0 4px 0;\n          font-size: 14px;\n          font-weight: 600;\n          color: #333;\n        }\n\n        .workflow-desc {\n          margin: 0 0 4px 0;\n          font-size: 12px;\n          color: #666;\n          line-height: 1.4;\n        }\n\n        .workflow-time {\n          font-size: 11px;\n          color: #999;\n        }\n\n        .workflow-status {\n          font-size: 11px;\n          color: #faad14;\n          background: #fff7e6;\n          padding: 2px 6px;\n          border-radius: 4px;\n          border: 1px solid #ffd591;\n        }\n      }\n\n      .workflow-actions {\n        display: flex;\n        gap: 8px;\n\n        .ant-btn {\n          padding: 4px 8px;\n          height: auto;\n          font-size: 12px;\n        }\n      }\n    }\n  }\n\n  // 🔥 暂存工作流列表特殊样式\n  .temp-workflows {\n    .workflow-item {\n      &.editing {\n        border-color: #1890ff;\n        background: #f0f8ff;\n        box-shadow: 0 2px 8px rgba(24, 144, 255, 0.15);\n      }\n\n      // 🔥 验证错误状态样式\n      &.has-error {\n        border-color: #ff4d4f;\n        background: #fff2f0;\n        box-shadow: 0 2px 8px rgba(255, 77, 79, 0.15);\n\n        .workflow-name {\n          color: #ff4d4f;\n        }\n      }\n\n      .workflow-meta {\n        display: flex;\n        gap: 16px;\n        font-size: 12px;\n        color: #999;\n        margin-top: 8px;\n\n        .workflow-file, .workflow-size {\n          display: flex;\n          align-items: center;\n        }\n      }\n    }\n  }\n\n  // 🔥 工作流文件上传样式\n  .workflow-file-upload {\n    .uploaded-file-info {\n      .file-item {\n        display: flex;\n        align-items: center;\n        padding: 8px 12px;\n        background: #f5f5f5;\n        border: 1px solid #d9d9d9;\n        border-radius: 6px;\n        margin-bottom: 8px;\n\n        .file-icon {\n          color: #1890ff;\n          margin-right: 8px;\n          font-size: 16px;\n        }\n\n        .file-name {\n          flex: 1;\n          font-size: 14px;\n          color: #333;\n          margin-right: 8px;\n        }\n\n        .remove-btn {\n          padding: 4px 8px;\n          font-size: 12px;\n        }\n\n        // 🔥 已保存文件的特殊样式\n        &.saved-file {\n          background: #f6ffed;\n          border-color: #b7eb8f;\n\n          .file-icon {\n            color: #52c41a;\n          }\n\n          .file-name {\n            color: #389e0d;\n            font-weight: 500;\n          }\n        }\n      }\n\n      // 🔥 验证错误提示样式\n      .workflow-errors {\n        margin-top: 12px;\n        margin-bottom: 8px;\n\n        .ant-alert {\n          border-radius: 4px;\n\n          .ant-alert-message {\n            font-size: 12px;\n            line-height: 1.4;\n          }\n        }\n      }\n    }\n  }\n\n  .workflow-empty {\n    text-align: center;\n    padding: 20px;\n\n    .ant-empty {\n      margin: 0;\n    }\n  }\n\n  .workflow-add-item {\n    margin-top: 16px;\n\n    .add-workflow-btn {\n      height: 48px;\n      border: 2px dashed #d9d9d9;\n      color: #666;\n      font-size: 14px;\n      transition: all 0.3s;\n\n      &:hover {\n        border-color: #1890ff;\n        color: #1890ff;\n      }\n\n      .anticon {\n        margin-right: 8px;\n      }\n    }\n  }\n}\n\n// 响应式设计\n@media (max-width: 768px) {\n  :global(.creator-agent-modal) {\n    .ant-modal {\n      width: 95% !important;\n      margin: 10px auto;\n    }\n\n    .ant-modal-body {\n      padding: 16px;\n    }\n  }\n\n  .modal-footer {\n    padding: 12px 16px;\n\n    .ant-btn {\n      width: 48%;\n      margin-left: 4%;\n\n      &:first-child {\n        margin-left: 0;\n      }\n    }\n  }\n\n  .workflow-section {\n    .workflow-item {\n      flex-direction: column;\n      gap: 12px;\n\n      .workflow-actions {\n        align-self: stretch;\n        justify-content: flex-end;\n      }\n    }\n  }\n}\n\n// 第三步成功页面样式\n.success-content {\n  text-align: center;\n  padding: 40px 20px;\n\n  .success-icon {\n    margin-bottom: 24px;\n  }\n\n  .success-message {\n    h2 {\n      color: #52c41a;\n      font-size: 24px;\n      font-weight: 600;\n      margin-bottom: 16px;\n    }\n\n    p {\n      color: #666;\n      font-size: 14px;\n      line-height: 1.6;\n      margin-bottom: 8px;\n\n      &:last-child {\n        margin-bottom: 0;\n      }\n    }\n  }\n}\n</style>\n"], "sourceRoot": "src/views/website/workflow/components"}]}