-- =============================================
-- 智界Aigc 订单倒计时功能数据库脚本
-- 创建时间：2025-06-20
-- 版本：V1.0
-- 说明：为aicg_user_transaction表添加expire_time字段支持15分钟倒计时
-- =============================================

-- 1. 添加订单过期时间字段
ALTER TABLE `aicg_user_transaction` 
ADD COLUMN `expire_time` DATETIME NULL 
COMMENT '订单过期时间（用于15分钟倒计时）' 
AFTER `transaction_time`;

-- 2. 为过期时间字段添加索引（用于定时任务查询）
ALTER TABLE `aicg_user_transaction` 
ADD INDEX `idx_expire_time` (`expire_time`);

-- 3. 为订单状态和过期时间组合添加索引（优化定时任务查询）
ALTER TABLE `aicg_user_transaction` 
ADD INDEX `idx_order_status_expire` (`order_status`, `expire_time`);

-- 4. 更新现有待支付订单的过期时间（设置为创建时间后15分钟）
UPDATE `aicg_user_transaction` 
SET `expire_time` = DATE_ADD(`create_time`, INTERVAL 15 MINUTE)
WHERE `order_status` = 1 AND `expire_time` IS NULL;

-- 5. 创建自动取消超时订单的存储过程
DELIMITER $$
CREATE PROCEDURE `CancelExpiredOrders`()
BEGIN
    DECLARE affected_rows INT DEFAULT 0;
    
    -- 更新超时的待支付订单状态为已取消
    UPDATE `aicg_user_transaction` 
    SET `order_status` = 4, 
        `update_time` = NOW() 
    WHERE `order_status` = 1 
    AND `expire_time` < NOW();
    
    -- 获取影响的行数
    SET affected_rows = ROW_COUNT();
    
    -- 记录日志（可选）
    INSERT INTO `sys_log` (`log_type`, `log_content`, `operate_type`, `create_time`)
    VALUES (1, CONCAT('自动取消超时订单，影响订单数：', affected_rows), 1, NOW());
    
    -- 返回影响的行数
    SELECT affected_rows as cancelled_orders;
END$$
DELIMITER ;

-- 6. 查询验证数据
SELECT 
    id,
    related_order_id,
    order_status,
    create_time,
    expire_time,
    CASE 
        WHEN expire_time IS NULL THEN '无过期时间'
        WHEN expire_time < NOW() THEN '已超时'
        ELSE CONCAT('剩余', TIMESTAMPDIFF(MINUTE, NOW(), expire_time), '分钟')
    END as status_desc
FROM aicg_user_transaction 
WHERE order_status = 1 
ORDER BY create_time DESC 
LIMIT 10;

-- 7. 测试存储过程
-- CALL CancelExpiredOrders();

-- 执行完成提示
SELECT '订单倒计时功能数据库脚本执行完成！' as message;
SELECT '注意：需要重启后端服务以启用定时任务功能' as reminder;
