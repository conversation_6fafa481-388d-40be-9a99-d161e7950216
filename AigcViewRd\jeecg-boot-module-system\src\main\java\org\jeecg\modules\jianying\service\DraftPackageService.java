package org.jeecg.modules.jianying.service;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.jianying.dto.CreateDraftRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.UUID;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * 草稿打包服务 - 将所有草稿文件打包成ZIP压缩包
 * 
 * <AUTHOR>
 * @date 2025-01-04
 */
@Slf4j
@Service
public class DraftPackageService {

    @Autowired
    private DraftContentGenerator draftContentGenerator;
    
    @Autowired
    private DraftConfigGenerator draftConfigGenerator;

    /**
     * 创建完整的草稿包
     * 
     * @param request 创建草稿请求
     * @return 草稿包信息 {zipFilePath, folderName, draftId}
     */
    public JSONObject createDraftPackage(CreateDraftRequest request) {
        String folderName = UUID.randomUUID().toString();
        String draftId = UUID.randomUUID().toString().toUpperCase();
        String templateId = UUID.randomUUID().toString();
        
        // 创建临时目录
        Path tempDir = null;
        Path zipFilePath = null;
        
        try {
            // 创建临时文件夹
            tempDir = Files.createTempDirectory("jianying_draft_");
            Path draftFolder = tempDir.resolve(folderName);
            Files.createDirectories(draftFolder);
            
            // 创建素材文件夹
            Path materialFolder = draftFolder.resolve(folderName);
            Files.createDirectories(materialFolder);
            
            // 生成所有文件内容
            JSONObject draftContent = draftContentGenerator.generateDraftContent(request, draftId);
            JSONObject metaInfo = draftConfigGenerator.generateDraftMetaInfo(
                folderName, 
                draftId, 
                "C:/Users/<USER>/AppData/Local/JianyingPro/User Data/Projects/com.lveditor.draft/" + folderName
            );
            JSONObject agencyConfig = draftConfigGenerator.generateDraftAgencyConfig();
            JSONObject attachmentConfig = draftConfigGenerator.generateAttachmentPcCommon();
            JSONObject template = draftConfigGenerator.generateTemplate(templateId);
            
            // 写入文件
            writeJsonFile(draftFolder.resolve("draft_info.json"), draftContent);
            writeJsonFile(draftFolder.resolve("draft_content.json"), draftContent);
            writeJsonFile(draftFolder.resolve(folderName + ".json"), draftContent);
            writeJsonFile(draftFolder.resolve("draft_meta_info.json"), metaInfo);
            writeJsonFile(draftFolder.resolve("draft_agency_config.json"), agencyConfig);
            writeJsonFile(draftFolder.resolve("attachment_pc_common.json"), attachmentConfig);
            writeJsonFile(draftFolder.resolve("template.tmp"), template);
            
            // 创建使用说明文件
            Files.write(draftFolder.resolve("把当前文件夹放到剪映草稿目录下"), 
                       "".getBytes(StandardCharsets.UTF_8));
            
            // 打包成ZIP
            zipFilePath = tempDir.resolve("draft_package_" + System.currentTimeMillis() + ".zip");
            createZipFile(draftFolder, zipFilePath);
            
            // 返回结果
            JSONObject result = new JSONObject();
            result.put("zipFilePath", zipFilePath.toString());
            result.put("folderName", folderName);
            result.put("draftId", draftId);
            result.put("tempDir", tempDir.toString());
            
            log.info("草稿包创建成功: {}", result);
            return result;
            
        } catch (Exception e) {
            log.error("创建草稿包失败", e);
            // 清理临时文件
            if (tempDir != null) {
                cleanupTempDir(tempDir);
            }
            throw new RuntimeException("创建草稿包失败: " + e.getMessage(), e);
        }
    }

    /**
     * 写入JSON文件
     */
    private void writeJsonFile(Path filePath, JSONObject content) throws IOException {
        String jsonString = content.toJSONString();
        Files.write(filePath, jsonString.getBytes(StandardCharsets.UTF_8));
        log.debug("写入文件: {}, 大小: {} bytes", filePath.getFileName(), jsonString.length());
    }

    /**
     * 创建ZIP文件
     */
    private void createZipFile(Path sourceFolder, Path zipFilePath) throws IOException {
        try (FileOutputStream fos = new FileOutputStream(zipFilePath.toFile());
             ZipOutputStream zos = new ZipOutputStream(fos)) {
            
            // 递归添加文件夹内容到ZIP
            addFolderToZip(sourceFolder, sourceFolder.getFileName().toString(), zos);
            
            log.info("ZIP文件创建成功: {}", zipFilePath);
        }
    }

    /**
     * 递归添加文件夹到ZIP
     */
    private void addFolderToZip(Path sourceFolder, String baseName, ZipOutputStream zos) throws IOException {
        File folder = sourceFolder.toFile();
        File[] files = folder.listFiles();
        
        if (files != null) {
            for (File file : files) {
                if (file.isDirectory()) {
                    // 添加文件夹
                    String entryName = baseName + "/" + file.getName() + "/";
                    ZipEntry zipEntry = new ZipEntry(entryName);
                    zos.putNextEntry(zipEntry);
                    zos.closeEntry();
                    
                    // 递归添加子文件夹
                    addFolderToZip(file.toPath(), baseName + "/" + file.getName(), zos);
                } else {
                    // 添加文件
                    String entryName = baseName + "/" + file.getName();
                    ZipEntry zipEntry = new ZipEntry(entryName);
                    zos.putNextEntry(zipEntry);
                    
                    try (FileInputStream fis = new FileInputStream(file)) {
                        byte[] buffer = new byte[1024];
                        int length;
                        while ((length = fis.read(buffer)) > 0) {
                            zos.write(buffer, 0, length);
                        }
                    }
                    
                    zos.closeEntry();
                    log.debug("添加文件到ZIP: {}", entryName);
                }
            }
        }
    }

    /**
     * 清理临时目录
     */
    public void cleanupTempDir(Path tempDir) {
        try {
            if (Files.exists(tempDir)) {
                Files.walk(tempDir)
                    .sorted((a, b) -> b.compareTo(a)) // 先删除文件，再删除文件夹
                    .forEach(path -> {
                        try {
                            Files.delete(path);
                        } catch (IOException e) {
                            log.warn("删除临时文件失败: {}", path, e);
                        }
                    });
                log.info("临时目录清理完成: {}", tempDir);
            }
        } catch (Exception e) {
            log.error("清理临时目录失败: {}", tempDir, e);
        }
    }

    /**
     * 清理临时目录 - 字符串路径版本
     */
    public void cleanupTempDir(String tempDirPath) {
        if (tempDirPath != null && !tempDirPath.isEmpty()) {
            cleanupTempDir(Paths.get(tempDirPath));
        }
    }
}
