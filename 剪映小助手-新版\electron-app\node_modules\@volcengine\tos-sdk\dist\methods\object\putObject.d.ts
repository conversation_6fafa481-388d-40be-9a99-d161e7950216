/// <reference types="node" />
import TOSBase, { TosResponse } from '../base';
import { Acl, DataTransferStatus, SupportObjectBody } from '../../interface';
import { IRateLimiter } from '../../universal/rate-limiter';
import { StorageClassType } from '../../TosExportEnum';
export interface PutObjectInput {
    bucket?: string;
    key: string;
    /**
     * body is empty buffer if it's falsy.
     */
    body?: SupportObjectBody;
    contentLength?: number;
    contentMD5?: string;
    contentSHA256?: string;
    cacheControl?: string;
    contentDisposition?: string;
    contentEncoding?: string;
    contentLanguage?: string;
    contentType?: string;
    expires?: Date;
    acl?: Acl;
    grantFullControl?: string;
    grantRead?: string;
    grantReadAcp?: string;
    grantWrite?: string;
    grantWriteAcp?: string;
    ssecAlgorithm?: string;
    ssecKey?: string;
    ssecKeyMD5?: string;
    serverSideEncryption?: string;
    /**
     * @private unstable
     */
    serverSideDataEncryption?: string;
    meta?: Record<string, string>;
    websiteRedirectLocation?: string;
    storageClass?: StorageClassType;
    ifMatch?: string;
    dataTransferStatusChange?: (status: DataTransferStatus) => void;
    /**
     * the simple progress feature
     * percent is [0, 1].
     *
     * since putObject is stateless, so if `putObject` fail and you retry it,
     * `percent` will start from 0 again rather than from the previous value.
     * if you need `percent` start from the previous value, you can use `uploadFile` instead.
     */
    progress?: (percent: number) => void;
    /**
     * unit: bit/s
     * server side traffic limit
     **/
    trafficLimit?: number;
    /**
     * only works for nodejs environment
     **/
    rateLimiter?: IRateLimiter;
    forbidOverwrite?: boolean;
    callback?: string;
    callbackVar?: string;
    headers?: {
        [key: string]: string | undefined;
        'content-length'?: string;
        'content-type'?: string;
        'content-md5'?: string;
        'cache-control'?: string;
        expires?: string;
        'x-tos-acl'?: Acl;
        'x-tos-grant-full-control'?: string;
        'x-tos-grant-read'?: string;
        'x-tos-grant-read-acp'?: string;
        'x-tos-grant-write-acp'?: string;
        'x-tos-server-side-encryption-customer-algorithm'?: string;
        'x-tos-server-side-encryption-customer-key'?: string;
        'x-tos-server-side-encryption-customer-key-md5'?: string;
        'x-tos-website-redirect-location'?: string;
        'x-tos-storage-class'?: string;
        'x-tos-server-side-encryption'?: string;
        'x-tos-forbid-overwrite'?: string;
        'If-Match'?: string;
    };
}
interface PutObjectInputInner extends PutObjectInput {
    makeRetryStream?: () => NodeJS.ReadableStream | undefined;
}
export interface PutObjectOutput {
    'x-tos-server-side-encryption-customer-algorithm'?: string;
    'x-tos-server-side-encryption-customer-key-md5'?: string;
    'x-tos-version-id'?: string;
    'x-tos-hash-crc64ecma'?: string;
    'x-tos-server-side-encryption'?: string;
    CallbackResult?: string;
}
export declare function putObject(this: TOSBase, input: PutObjectInput | string): Promise<TosResponse<PutObjectOutput>>;
export declare function _putObject(this: TOSBase, input: PutObjectInputInner | string): Promise<TosResponse<PutObjectOutput>>;
interface PutObjectFromFileInput extends Omit<PutObjectInput, 'body'> {
    filePath: string;
}
export declare function putObjectFromFile(this: TOSBase, input: PutObjectFromFileInput): Promise<TosResponse<PutObjectOutput>>;
export default putObject;
