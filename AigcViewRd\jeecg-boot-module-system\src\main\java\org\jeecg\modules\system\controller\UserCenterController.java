package org.jeecg.modules.system.controller;

import org.jeecg.modules.system.service.IAicgVerifyCodeService;
import org.jeecg.modules.system.service.ISensitiveWordService;
import org.jeecg.modules.system.entity.VerifyCodeResult;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.util.IPUtils;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.common.system.util.JwtUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;

import javax.servlet.http.HttpServletRequest;

/**
 * @Description: 用户中心控制器
 * @Author: jeecg-boot
 * @Date: 2025-06-22
 * @Version: V1.0
 */
@Api(tags = "用户中心")
@RestController
@RequestMapping("/api/usercenter")
@Slf4j
public class UserCenterController {

    @Autowired
    private IAicgVerifyCodeService verifyCodeService;

    @Autowired
    private ISensitiveWordService sensitiveWordService;

    /**
     * 发送短信验证码（用户中心专用，需要token）
     */
    @ApiOperation(value = "发送短信验证码", notes = "用户中心修改手机号专用验证码")
    @PostMapping("/sendSmsCode")
    public Result<?> sendSmsCode(
            @ApiParam(value = "手机号", required = true) @RequestParam String phone,
            @ApiParam(value = "使用场景", required = true) @RequestParam(defaultValue = "changePhone") String scene,
            HttpServletRequest request) {
        try {
            // 验证token
            String token = request.getHeader("X-Access-Token");
            if (oConvertUtils.isEmpty(token)) {
                return Result.error("请先登录");
            }

            // 验证token有效性
            try {
                String username = JwtUtil.getUsername(token);
                if (oConvertUtils.isEmpty(username)) {
                    return Result.error("登录状态已过期，请重新登录");
                }
            } catch (Exception e) {
                log.error("Token验证失败：{}", e.getMessage());
                return Result.error("登录状态无效，请重新登录");
            }

            if (oConvertUtils.isEmpty(phone)) {
                return Result.error("手机号不能为空");
            }

            // 验证手机号格式
            if (!phone.matches("^1[3-9]\\d{9}$")) {
                return Result.error("手机号格式不正确");
            }

            String ipAddress = IPUtils.getIpAddr(request);
            VerifyCodeResult result = verifyCodeService.sendSmsCode(phone, scene, ipAddress);

            if (result.isSuccess()) {
                return Result.OK(result.getMessage());
            } else {
                return Result.error(result.getMessage());
            }
        } catch (Exception e) {
            log.error("发送短信验证码异常：{}", e.getMessage(), e);
            return Result.error("发送失败，请稍后重试");
        }
    }

    /**
     * 发送邮箱验证码（用户中心专用，需要token）
     */
    @ApiOperation(value = "发送邮箱验证码", notes = "用户中心修改邮箱专用验证码")
    @PostMapping("/sendEmailCode")
    public Result<?> sendEmailCode(
            @ApiParam(value = "邮箱", required = true) @RequestParam String email,
            @ApiParam(value = "使用场景", required = true) @RequestParam(defaultValue = "changeEmail") String scene,
            HttpServletRequest request) {
        try {
            // 验证token
            String token = request.getHeader("X-Access-Token");
            if (oConvertUtils.isEmpty(token)) {
                return Result.error("请先登录");
            }

            // 验证token有效性
            try {
                String username = JwtUtil.getUsername(token);
                if (oConvertUtils.isEmpty(username)) {
                    return Result.error("登录状态已过期，请重新登录");
                }
            } catch (Exception e) {
                log.error("Token验证失败：{}", e.getMessage());
                return Result.error("登录状态无效，请重新登录");
            }

            if (oConvertUtils.isEmpty(email)) {
                return Result.error("邮箱不能为空");
            }

            // 验证邮箱格式
            if (!email.matches("^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$")) {
                return Result.error("邮箱格式不正确");
            }

            String ipAddress = IPUtils.getIpAddr(request);
            VerifyCodeResult result = verifyCodeService.sendEmailCode(email, scene, ipAddress);

            if (result.isSuccess()) {
                return Result.OK(result.getMessage());
            } else {
                return Result.error(result.getMessage());
            }
        } catch (Exception e) {
            log.error("发送邮箱验证码异常：{}", e.getMessage(), e);
            return Result.error("发送失败，请稍后重试");
        }
    }

    /**
     * 验证验证码（用户中心专用，需要token）
     */
    @ApiOperation(value = "验证验证码", notes = "验证短信或邮箱验证码")
    @PostMapping("/verifyCode")
    public Result<?> verifyCode(
            @ApiParam(value = "目标（手机号/邮箱）", required = true) @RequestParam String target,
            @ApiParam(value = "验证码", required = true) @RequestParam String code,
            @ApiParam(value = "验证码类型：sms-短信，email-邮箱", required = true) @RequestParam String codeType,
            @ApiParam(value = "使用场景", required = true) @RequestParam(defaultValue = "changePhone") String scene,
            HttpServletRequest request) {
        try {
            // 验证token
            String token = request.getHeader("X-Access-Token");
            if (oConvertUtils.isEmpty(token)) {
                return Result.error("请先登录");
            }

            // 验证token有效性
            try {
                String username = JwtUtil.getUsername(token);
                if (oConvertUtils.isEmpty(username)) {
                    return Result.error("登录状态已过期，请重新登录");
                }
            } catch (Exception e) {
                log.error("Token验证失败：{}", e.getMessage());
                return Result.error("登录状态无效，请重新登录");
            }

            if (oConvertUtils.isEmpty(target) || oConvertUtils.isEmpty(code) || oConvertUtils.isEmpty(codeType)) {
                return Result.error("参数不能为空");
            }

            boolean valid = verifyCodeService.verifyCode(target, code, codeType, scene);
            
            if (valid) {
                return Result.OK("验证码验证成功");
            } else {
                return Result.error("验证码错误或已过期");
            }
        } catch (Exception e) {
            log.error("验证验证码异常：{}", e.getMessage(), e);
            return Result.error("验证失败，请稍后重试");
        }
    }

    /**
     * 验证昵称是否可用（需要token）
     */
    @ApiOperation(value = "验证昵称", notes = "检查昵称是否包含敏感词和是否重复")
    @PostMapping("/validateNickname")
    public Result<?> validateNickname(
            @ApiParam(value = "昵称", required = true) @RequestParam String nickname,
            HttpServletRequest request) {
        try {
            // 🛡️ 安全检查：验证token
            String token = request.getHeader("X-Access-Token");
            if (oConvertUtils.isEmpty(token)) {
                return Result.error("请先登录");
            }

            String currentUserId = null;
            try {
                String username = JwtUtil.getUsername(token);
                if (oConvertUtils.isEmpty(username)) {
                    return Result.error("登录状态已过期，请重新登录");
                }
                // 这里可以根据username获取userId，暂时使用username作为标识
                currentUserId = username;
            } catch (Exception e) {
                log.error("Token验证失败：{}", e.getMessage());
                return Result.error("登录状态无效，请重新登录");
            }

            // 🛡️ 安全检查：IP频率限制
            String ipAddress = IPUtils.getIpAddr(request);
            if (!sensitiveWordService.checkRateLimit(ipAddress)) {
                log.warn("🚨 IP请求频率过高，拒绝请求：{}", ipAddress);
                return Result.error("请求过于频繁，请稍后再试");
            }

            // 🛡️ 安全检查：参数基础验证
            if (oConvertUtils.isEmpty(nickname)) {
                return Result.error("昵称不能为空");
            }

            if (nickname.length() > 50) { // 防止超长参数攻击
                return Result.error("昵称长度过长");
            }

            // 执行昵称验证
            ISensitiveWordService.NicknameValidationResult result =
                sensitiveWordService.validateNickname(nickname, currentUserId);

            if (result.isValid()) {
                return Result.OK("昵称可用");
            } else {
                // 记录验证失败的日志（用于安全监控）
                log.info("昵称验证失败 - 用户：{}，昵称：{}，原因：{}，类型：{}",
                    currentUserId, nickname, result.getMessage(), result.getErrorType());

                return Result.error(result.getMessage());
            }

        } catch (Exception e) {
            log.error("昵称验证异常：{}", e.getMessage(), e);
            return Result.error("验证失败，请稍后重试");
        }
    }
}
