package org.jeecg.modules.api.exception;

import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

/**
 * 扣子图生视频接口专用异常处理器
 * 优先级高于全局异常处理器，确保返回纯JSON格式
 * 
 * <AUTHOR>
 * @since 2025-01-16
 */
@Slf4j
@Order(-1)  // 🔥 设置最高优先级，确保优先于所有其他异常处理器
@RestControllerAdvice(basePackages = "org.jeecg.modules.api.controller")
public class CozeVideoExceptionHandler {

    /**
     * 处理视频生成业务异常
     */
    @ExceptionHandler(VideoGenerationException.class)
    public ResponseEntity<Map<String, Object>> handleVideoGenerationException(VideoGenerationException e, HttpServletRequest request) {
        String apiPath = request.getRequestURI();
        String clientIp = getClientIp(request);

        log.error("图生视频业务异常 - Path: {}, IP: {}, 错误代码: {}, 错误信息: {}",
                apiPath, clientIp, e.getErrorCode(), e.getErrorMessage(), e);

        Map<String, Object> response = new HashMap<>();
        response.put("error", e.getErrorMessage());

        // 🔥 根据错误类型返回对应的HTTP状态码
        HttpStatus status = getHttpStatusForError(e.getErrorCode());

        log.info("返回图生视频错误响应: {}, HTTP状态码: {}", response, status.value());
        return ResponseEntity.status(status).body(response);
    }

    /**
     * 处理参数验证异常
     */
    @ExceptionHandler(IllegalArgumentException.class)
    public ResponseEntity<Map<String, Object>> handleIllegalArgumentException(IllegalArgumentException e, HttpServletRequest request) {
        String apiPath = request.getRequestURI();
        String clientIp = getClientIp(request);

        log.warn("图生视频参数异常 - Path: {}, IP: {}, Error: {}",
                apiPath, clientIp, e.getMessage());

        Map<String, Object> response = new HashMap<>();
        response.put("error", "参数错误：" + e.getMessage());

        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
    }

    /**
     * 处理空指针异常
     */
    @ExceptionHandler(NullPointerException.class)
    public Map<String, Object> handleNullPointerException(NullPointerException e, HttpServletRequest request) {
        String apiPath = request.getRequestURI();
        String clientIp = getClientIp(request);
        
        log.error("图生视频空指针异常 - Path: {}, IP: {}", apiPath, clientIp, e);
        
        Map<String, Object> response = new HashMap<>();
        response.put("error", "系统内部错误：空指针异常");
        
        return response;
    }

    /**
     * 处理运行时异常
     */
    @ExceptionHandler(RuntimeException.class)
    public Map<String, Object> handleRuntimeException(RuntimeException e, HttpServletRequest request) {
        String apiPath = request.getRequestURI();
        String clientIp = getClientIp(request);
        
        // 如果是VideoGenerationException，让专门的处理器处理
        if (e instanceof VideoGenerationException) {
            throw e;
        }
        
        log.error("图生视频运行时异常 - Path: {}, IP: {}, Error: {}", 
                apiPath, clientIp, e.getMessage(), e);
        
        Map<String, Object> response = new HashMap<>();
        response.put("error", "系统内部错误：" + e.getMessage());
        
        return response;
    }

    /**
     * 处理所有其他异常（图生视频接口专用）
     */
    @ExceptionHandler(Exception.class)
    public Map<String, Object> handleException(Exception e, HttpServletRequest request) {
        String apiPath = request.getRequestURI();
        String clientIp = getClientIp(request);
        
        // 如果是VideoGenerationException，让专门的处理器处理
        if (e instanceof VideoGenerationException) {
            throw (VideoGenerationException) e;
        }
        
        log.error("图生视频未知异常 - Path: {}, IP: {}, Type: {}, Error: {}", 
                apiPath, clientIp, e.getClass().getSimpleName(), e.getMessage(), e);
        
        Map<String, Object> response = new HashMap<>();
        response.put("error", "系统异常，请稍后重试：" + e.getMessage());
        
        return response;
    }

    /**
     * 获取客户端IP地址
     */
    private String getClientIp(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        
        // 处理多个IP的情况，取第一个
        if (ip != null && ip.contains(",")) {
            ip = ip.split(",")[0].trim();
        }
        
        return ip;
    }

    /**
     * 🔥 根据错误代码映射HTTP状态码
     */
    private HttpStatus getHttpStatusForError(String errorCode) {
        if (errorCode == null) {
            return HttpStatus.INTERNAL_SERVER_ERROR;
        }

        switch (errorCode) {
            case "INVALID_PARAMETER":
            case "MODEL_NOT_SUPPORTED":
            case "RESOLUTION_NOT_SUPPORTED":
            case "DURATION_NOT_SUPPORTED":
                return HttpStatus.BAD_REQUEST; // 400

            case "API_KEY_INVALID":
                return HttpStatus.UNAUTHORIZED; // 401

            case "INSUFFICIENT_BALANCE":
                return HttpStatus.PAYMENT_REQUIRED; // 402

            case "TASK_NOT_FOUND":
                return HttpStatus.NOT_FOUND; // 404

            case "DOUBAO_API_ERROR":
            case "NETWORK_ERROR":
            case "SYSTEM_ERROR":
            case "DEDUCT_BALANCE_FAILED":
            default:
                return HttpStatus.INTERNAL_SERVER_ERROR; // 500
        }
    }
}
