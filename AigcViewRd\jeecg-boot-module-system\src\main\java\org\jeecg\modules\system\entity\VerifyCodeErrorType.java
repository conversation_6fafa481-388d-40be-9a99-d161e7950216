package org.jeecg.modules.system.entity;

/**
 * 验证码错误类型常量
 * 
 * <AUTHOR>
 * @date 2025-06-23
 */
public class VerifyCodeErrorType {
    
    /**
     * 发送频率限制（60秒内重复发送）
     */
    public static final String FREQUENCY_LIMIT = "FREQUENCY_LIMIT";
    
    /**
     * 每日发送次数限制
     */
    public static final String DAILY_LIMIT = "DAILY_LIMIT";
    
    /**
     * IP发送频率限制
     */
    public static final String IP_LIMIT = "IP_LIMIT";
    
    /**
     * 网络或服务异常
     */
    public static final String NETWORK_ERROR = "NETWORK_ERROR";
    
    /**
     * 短信模板或邮件模板错误
     */
    public static final String TEMPLATE_ERROR = "TEMPLATE_ERROR";
    
    /**
     * 配置错误（如短信服务配置）
     */
    public static final String CONFIG_ERROR = "CONFIG_ERROR";
    
    /**
     * 流控限制错误（阿里云短信平台限制）
     */
    public static final String FLOW_CONTROL_ERROR = "FLOW_CONTROL_ERROR";

    /**
     * 短信平台错误（如签名、模板等问题）
     */
    public static final String SMS_PLATFORM_ERROR = "SMS_PLATFORM_ERROR";

    /**
     * 未知错误
     */
    public static final String UNKNOWN = "UNKNOWN";
    
    /**
     * 获取用户友好的错误消息
     * 
     * @param errorType 错误类型
     * @return 用户友好的错误消息
     */
    public static String getFriendlyMessage(String errorType) {
        switch (errorType) {
            case FREQUENCY_LIMIT:
                return "发送过于频繁，请60秒后重试";
            case DAILY_LIMIT:
                return "今日发送次数已达上限，请明天再试";
            case IP_LIMIT:
                return "该网络发送过于频繁，请稍后重试";
            case FLOW_CONTROL_ERROR:
                return "发送过于频繁，已触发平台限制，请1小时后重试或使用邮箱验证";
            case SMS_PLATFORM_ERROR:
                return "短信平台异常，请稍后重试或使用邮箱验证";
            case NETWORK_ERROR:
                return "网络异常，请检查网络连接后重试";
            case TEMPLATE_ERROR:
                return "服务配置异常，请联系管理员";
            case CONFIG_ERROR:
                return "服务暂时不可用，请稍后重试";
            default:
                return "验证码发送失败，请稍后重试";
        }
    }
}
