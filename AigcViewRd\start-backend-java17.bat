@echo off
echo Starting 智界Aigc Backend with Java 17 compatibility...
echo.
echo 重要提示：此脚本解决Java 17模块系统限制问题
echo Important: This script resolves Java 17 module system restrictions
echo.

cd jeecg-boot-module-system

echo 正在启动后端服务...
echo Starting backend service...

REM 方法1: 使用Maven启动（推荐）
mvn org.springframework.boot:spring-boot-maven-plugin:run ^
    -Dspring-boot.run.jvmArguments="--add-opens java.base/java.lang.invoke=ALL-UNNAMED --add-opens java.base/java.lang.reflect=ALL-UNNAMED --add-opens java.base/java.io=ALL-UNNAMED"

REM 如果Maven方法失败，可以尝试直接Java启动（需要先编译）
REM java --add-opens java.base/java.lang.invoke=ALL-UNNAMED ^
REM      --add-opens java.base/java.lang.reflect=ALL-UNNAMED ^
REM      --add-opens java.base/java.io=ALL-UNNAMED ^
REM      -jar target/jeecg-boot-module-system-2.4.6.jar

echo.
echo 如果启动成功，请访问: http://localhost:8080/jeecg-boot/
echo If startup successful, visit: http://localhost:8080/jeecg-boot/
pause
