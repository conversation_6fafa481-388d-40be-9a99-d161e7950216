-- 敏感词管理表
CREATE TABLE `sys_sensitive_word` (
  `id` varchar(32) NOT NULL COMMENT '主键ID',
  `word` varchar(100) NOT NULL COMMENT '敏感词',
  `category` varchar(50) DEFAULT NULL COMMENT '分类(政治/色情/暴力/广告/违法/赌博/毒品/其他)',
  `level` int(1) DEFAULT '1' COMMENT '敏感级别(1-低危 2-中危 3-高危)',
  `status` int(1) DEFAULT '1' COMMENT '状态(0-禁用 1-启用)',
  `hit_count` int(11) DEFAULT '0' COMMENT '命中次数',
  `source` varchar(50) DEFAULT 'CUSTOM' COMMENT '来源(HOUBB-houbb库/CUSTOM-自定义)',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_word` (`word`) COMMENT '敏感词唯一索引',
  KEY `idx_category` (`category`) COMMENT '分类索引',
  KEY `idx_level` (`level`) COMMENT '级别索引',
  KEY `idx_status` (`status`) COMMENT '状态索引',
  KEY `idx_hit_count` (`hit_count`) COMMENT '命中次数索引',
  KEY `idx_create_time` (`create_time`) COMMENT '创建时间索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='敏感词管理表';

-- 敏感词命中记录表（用于详细统计）
CREATE TABLE `sys_sensitive_word_hit_log` (
  `id` varchar(32) NOT NULL COMMENT '主键ID',
  `word_id` varchar(32) NOT NULL COMMENT '敏感词ID',
  `word` varchar(100) NOT NULL COMMENT '敏感词内容',
  `hit_text` text COMMENT '命中的原文本',
  `user_id` varchar(50) DEFAULT NULL COMMENT '触发用户ID',
  `ip_address` varchar(50) DEFAULT NULL COMMENT 'IP地址',
  `module` varchar(50) DEFAULT NULL COMMENT '触发模块(昵称校验/内容审核等)',
  `hit_time` datetime DEFAULT NULL COMMENT '命中时间',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_word_id` (`word_id`) COMMENT '敏感词ID索引',
  KEY `idx_user_id` (`user_id`) COMMENT '用户ID索引',
  KEY `idx_hit_time` (`hit_time`) COMMENT '命中时间索引',
  KEY `idx_module` (`module`) COMMENT '模块索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='敏感词命中记录表';

-- 初始化敏感词分类字典
INSERT INTO `sys_dict` (`id`, `dict_name`, `dict_code`, `description`, `del_flag`, `create_by`, `create_time`, `type`) 
VALUES ('sensitive_word_category_dict_id', '敏感词分类', 'sensitive_word_category', '敏感词分类字典', 0, 'admin', NOW(), 0);

INSERT INTO `sys_dict_item` (`id`, `dict_id`, `item_text`, `item_value`, `description`, `sort_order`, `status`, `create_by`, `create_time`) VALUES
('swc_001', 'sensitive_word_category_dict_id', '政治', 'political', '政治敏感词', 1, 1, 'admin', NOW()),
('swc_002', 'sensitive_word_category_dict_id', '色情', 'pornographic', '色情敏感词', 2, 1, 'admin', NOW()),
('swc_003', 'sensitive_word_category_dict_id', '暴力', 'violent', '暴力敏感词', 3, 1, 'admin', NOW()),
('swc_004', 'sensitive_word_category_dict_id', '广告', 'advertisement', '广告敏感词', 4, 1, 'admin', NOW()),
('swc_005', 'sensitive_word_category_dict_id', '违法', 'illegal', '违法敏感词', 5, 1, 'admin', NOW()),
('swc_006', 'sensitive_word_category_dict_id', '赌博', 'gambling', '赌博敏感词', 6, 1, 'admin', NOW()),
('swc_007', 'sensitive_word_category_dict_id', '毒品', 'drugs', '毒品敏感词', 7, 1, 'admin', NOW()),
('swc_008', 'sensitive_word_category_dict_id', '其他', 'other', '其他敏感词', 8, 1, 'admin', NOW());

-- 初始化敏感词级别字典
INSERT INTO `sys_dict` (`id`, `dict_name`, `dict_code`, `description`, `del_flag`, `create_by`, `create_time`, `type`) 
VALUES ('sensitive_word_level_dict_id', '敏感词级别', 'sensitive_word_level', '敏感词级别字典', 0, 'admin', NOW(), 0);

INSERT INTO `sys_dict_item` (`id`, `dict_id`, `item_text`, `item_value`, `description`, `sort_order`, `status`, `create_by`, `create_time`) VALUES
('swl_001', 'sensitive_word_level_dict_id', '低危', '1', '低危敏感词', 1, 1, 'admin', NOW()),
('swl_002', 'sensitive_word_level_dict_id', '中危', '2', '中危敏感词', 2, 1, 'admin', NOW()),
('swl_003', 'sensitive_word_level_dict_id', '高危', '3', '高危敏感词', 3, 1, 'admin', NOW());
