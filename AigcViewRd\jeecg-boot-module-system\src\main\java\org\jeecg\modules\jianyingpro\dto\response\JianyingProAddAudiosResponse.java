package org.jeecg.modules.jianyingpro.dto.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 一体化音频添加响应
 * 
 * <AUTHOR>
 * @since 2025-01-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class JianyingProAddAudiosResponse extends BaseJianyingProResponse {
    
    @ApiModelProperty(value = "更新后的草稿地址")
    @JsonProperty("draft_url")
    private String draftUrl;
    
    @ApiModelProperty(value = "操作提示信息")
    @JsonProperty("message")
    private String message;
    
    @ApiModelProperty(value = "处理的音频数量")
    @JsonProperty("audio_count")
    private Integer audioCount;
    
    @ApiModelProperty(value = "成功处理的音频数量")
    @JsonProperty("success_count")
    private Integer successCount;
    
    @ApiModelProperty(value = "失败的音频数量")
    @JsonProperty("failed_count")
    private Integer failedCount;
    
    @ApiModelProperty(value = "使用占位符的音频数量")
    @JsonProperty("placeholder_count")
    private Integer placeholderCount;
    
    @ApiModelProperty(value = "处理耗时（毫秒）")
    @JsonProperty("processing_time")
    private Long processingTime;
    
    @ApiModelProperty(value = "生成的轨道ID")
    @JsonProperty("track_id")
    private String trackId;
    
    @ApiModelProperty(value = "音频材料ID列表")
    @JsonProperty("audio_material_ids")
    private java.util.List<String> audioMaterialIds;
    
    @ApiModelProperty(value = "音频段ID列表")
    @JsonProperty("audio_segment_ids")
    private java.util.List<String> audioSegmentIds;
    
    @ApiModelProperty(value = "调试信息（开发环境）")
    @JsonProperty("debug_info")
    private String debugInfo;
}
