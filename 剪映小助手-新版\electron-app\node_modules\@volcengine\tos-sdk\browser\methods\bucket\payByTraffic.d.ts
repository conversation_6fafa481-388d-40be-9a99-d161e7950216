import TOSBase, { TosResponse } from '../base';
export interface BucketPayByTraffic {
    ChargeType: 'FlowOut' | 'Bandwidth';
    ActiveType: 'NextDay' | 'NextMonth';
}
export interface PutBucketPayByTrafficInput {
    bucket?: string;
    payByTraffic: BucketPayByTraffic;
}
export declare type GetBucketPayByTrafficOutput = BucketPayByTraffic & {
    ActiveTime: string;
};
/**
 * @private unstable method
 */
export declare function putBucketPayByTraffic(this: TOSBase, input: PutBucketPayByTrafficInput): Promise<TosResponse<unknown>>;
interface GetBucketPayByTrafficInput {
    bucket: string;
}
/**
 * @private unstable method
 */
export declare function getBucketPayByTraffic(this: TOSBase, { bucket }: GetBucketPayByTrafficInput): Promise<TosResponse<GetBucketPayByTrafficOutput>>;
export {};
