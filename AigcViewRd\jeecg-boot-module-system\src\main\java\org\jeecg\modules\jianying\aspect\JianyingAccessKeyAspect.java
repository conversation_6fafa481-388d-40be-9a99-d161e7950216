package org.jeecg.modules.jianying.aspect;

import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.jianying.dto.BaseJianyingRequest;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;

/**
 * 剪映小助手Access Key验证切面
 * 自动验证所有剪映API的access_key参数
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
@Slf4j
@Aspect
@Component
public class JianyingAccessKeyAspect {
    
    /**
     * 预期的access_key值
     */
    private static final String EXPECTED_ACCESS_KEY = "JianyingAPI_2025_SecureAccess_AigcView";
    
    /**
     * 定义切点：拦截剪映Controller包下的所有公共方法
     */
    @Pointcut("execution(public * org.jeecg.modules.jianying.controller..*(..))")
    public void jianyingControllerMethods() {}
    
    /**
     * 环绕通知：验证access_key
     */
    @Around("jianyingControllerMethods()")
    public Object validateAccessKey(ProceedingJoinPoint joinPoint) throws Throwable {
        String methodName = joinPoint.getSignature().getName();
        String className = joinPoint.getTarget().getClass().getSimpleName();
        
        try {
            // 获取方法参数
            Object[] args = joinPoint.getArgs();
            
            // 查找BaseJianyingRequest类型的参数
            BaseJianyingRequest request = null;
            HttpServletRequest httpRequest = null;
            
            for (Object arg : args) {
                if (arg instanceof BaseJianyingRequest) {
                    request = (BaseJianyingRequest) arg;
                } else if (arg instanceof HttpServletRequest) {
                    httpRequest = (HttpServletRequest) arg;
                }
            }
            
            // 如果没有找到BaseJianyingRequest参数，直接执行方法
            if (request == null) {
                log.debug("方法 {}.{} 没有BaseJianyingRequest参数，跳过access_key验证", 
                         className, methodName);
                return joinPoint.proceed();
            }
            
            // 获取客户端IP（用于日志记录）
            String clientIp = getClientIp(httpRequest);
            
            // 验证access_key
            String accessKey = request.getAccessKey();
            if (accessKey == null || accessKey.trim().isEmpty()) {
                log.warn("剪映API访问被拒绝 - 缺少access_key - Method: {}.{}, IP: {}", 
                        className, methodName, clientIp);
                return Result.error(401, "访问被拒绝：缺少access_key参数");
            }
            
            if (!EXPECTED_ACCESS_KEY.equals(accessKey)) {
                log.warn("剪映API访问被拒绝 - access_key无效 - Method: {}.{}, IP: {}, AccessKey: {}", 
                        className, methodName, clientIp, 
                        accessKey.length() > 10 ? accessKey.substring(0, 10) + "***" : accessKey);
                return Result.error(401, "访问被拒绝：access_key无效");
            }
            
            // access_key验证通过，记录访问日志
            log.info("剪映API访问验证通过 - Method: {}.{}, IP: {}, Request: {}", 
                    className, methodName, clientIp, request.getSummary());
            
            // 执行原方法
            Object result = joinPoint.proceed();
            
            // 记录执行成功日志
            log.debug("剪映API执行成功 - Method: {}.{}, IP: {}", 
                     className, methodName, clientIp);
            
            return result;
            
        } catch (Exception e) {
            // 记录执行失败日志
            log.error("剪映API执行失败 - Method: {}.{}, Error: {}", 
                     className, methodName, e.getMessage(), e);
            throw e;
        }
    }
    
    /**
     * 获取客户端真实IP地址
     */
    private String getClientIp(HttpServletRequest request) {
        if (request == null) {
            return "unknown";
        }
        
        String ip = request.getHeader("X-Forwarded-For");
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        
        // 处理多个IP的情况，取第一个
        if (ip != null && ip.contains(",")) {
            ip = ip.split(",")[0].trim();
        }
        
        return ip != null ? ip : "unknown";
    }
    
    /**
     * 检查access_key是否有效
     */
    public static boolean isValidAccessKey(String accessKey) {
        return EXPECTED_ACCESS_KEY.equals(accessKey);
    }
    
    /**
     * 获取预期的access_key值（用于测试或配置）
     */
    public static String getExpectedAccessKey() {
        return EXPECTED_ACCESS_KEY;
    }
}
