package org.jeecg.modules.demo.videotutorial.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.demo.videotutorial.entity.AigcVideoTutorial;
import org.jeecg.modules.demo.videotutorial.service.IAigcVideoTutorialService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;

 /**
 * @Description: 视频教程
 * @Author: jeecg-boot
 * @Date:   2025-06-14
 * @Version: V1.0
 */
@Api(tags="视频教程")
@RestController
@RequestMapping("/videotutorial/aigcVideoTutorial")
@Slf4j
public class AigcVideoTutorialController extends JeecgController<AigcVideoTutorial, IAigcVideoTutorialService> {
	@Autowired
	private IAigcVideoTutorialService aigcVideoTutorialService;
	
	/**
	 * 分页列表查询
	 *
	 * @param aigcVideoTutorial
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	@AutoLog(value = "视频教程-分页列表查询")
	@ApiOperation(value="视频教程-分页列表查询", notes="视频教程-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(AigcVideoTutorial aigcVideoTutorial,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<AigcVideoTutorial> queryWrapper = QueryGenerator.initQueryWrapper(aigcVideoTutorial, req.getParameterMap());
		Page<AigcVideoTutorial> page = new Page<AigcVideoTutorial>(pageNo, pageSize);
		IPage<AigcVideoTutorial> pageList = aigcVideoTutorialService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param aigcVideoTutorial
	 * @return
	 */
	@AutoLog(value = "视频教程-添加")
	@ApiOperation(value="视频教程-添加", notes="视频教程-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody AigcVideoTutorial aigcVideoTutorial) {
		aigcVideoTutorialService.save(aigcVideoTutorial);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param aigcVideoTutorial
	 * @return
	 */
	@AutoLog(value = "视频教程-编辑")
	@ApiOperation(value="视频教程-编辑", notes="视频教程-编辑")
	@PutMapping(value = "/edit")
	public Result<?> edit(@RequestBody AigcVideoTutorial aigcVideoTutorial) {
		aigcVideoTutorialService.updateById(aigcVideoTutorial);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "视频教程-通过id删除")
	@ApiOperation(value="视频教程-通过id删除", notes="视频教程-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		aigcVideoTutorialService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "视频教程-批量删除")
	@ApiOperation(value="视频教程-批量删除", notes="视频教程-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.aigcVideoTutorialService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "视频教程-通过id查询")
	@ApiOperation(value="视频教程-通过id查询", notes="视频教程-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		AigcVideoTutorial aigcVideoTutorial = aigcVideoTutorialService.getById(id);
		if(aigcVideoTutorial==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(aigcVideoTutorial);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param aigcVideoTutorial
    */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, AigcVideoTutorial aigcVideoTutorial) {
        return super.exportXls(request, aigcVideoTutorial, AigcVideoTutorial.class, "视频教程");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, AigcVideoTutorial.class);
    }

}
