package org.jeecg.modules.jianying.exception;

import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.springframework.http.HttpStatus;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.servlet.http.HttpServletRequest;
import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import java.util.Set;

/**
 * 剪映小助手异常处理器
 * 统一处理剪映API的异常和错误响应
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
@Slf4j
@RestControllerAdvice(basePackages = "org.jeecg.modules.jianying.controller")
public class JianyingExceptionHandler {
    
    /**
     * 处理@Valid注解的参数验证异常（@RequestBody）
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public Result<?> handleMethodArgumentNotValidException(MethodArgumentNotValidException e, HttpServletRequest request) {
        String apiPath = request.getRequestURI();
        String clientIp = getClientIp(request);
        
        // 获取第一个验证错误信息
        FieldError fieldError = e.getBindingResult().getFieldError();
        String errorMessage = fieldError != null ? fieldError.getDefaultMessage() : "参数验证失败";
        String fieldName = fieldError != null ? fieldError.getField() : "unknown";
        
        log.warn("剪映API参数验证失败 - Path: {}, IP: {}, Field: {}, Error: {}", 
                apiPath, clientIp, fieldName, errorMessage);
        
        return Result.error(400, "参数验证失败：" + errorMessage);
    }
    
    /**
     * 处理@Valid注解的参数验证异常（@RequestParam）
     */
    @ExceptionHandler(BindException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public Result<?> handleBindException(BindException e, HttpServletRequest request) {
        String apiPath = request.getRequestURI();
        String clientIp = getClientIp(request);
        
        // 获取第一个验证错误信息
        FieldError fieldError = e.getBindingResult().getFieldError();
        String errorMessage = fieldError != null ? fieldError.getDefaultMessage() : "参数绑定失败";
        String fieldName = fieldError != null ? fieldError.getField() : "unknown";
        
        log.warn("剪映API参数绑定失败 - Path: {}, IP: {}, Field: {}, Error: {}", 
                apiPath, clientIp, fieldName, errorMessage);
        
        return Result.error(400, "参数绑定失败：" + errorMessage);
    }
    
    /**
     * 处理@Validated注解的参数验证异常
     */
    @ExceptionHandler(ConstraintViolationException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public Result<?> handleConstraintViolationException(ConstraintViolationException e, HttpServletRequest request) {
        String apiPath = request.getRequestURI();
        String clientIp = getClientIp(request);
        
        // 获取第一个约束违反信息
        Set<ConstraintViolation<?>> violations = e.getConstraintViolations();
        String errorMessage = "约束验证失败";
        if (!violations.isEmpty()) {
            ConstraintViolation<?> violation = violations.iterator().next();
            errorMessage = violation.getMessage();
        }
        
        log.warn("剪映API约束验证失败 - Path: {}, IP: {}, Error: {}", 
                apiPath, clientIp, errorMessage);
        
        return Result.error(400, "约束验证失败：" + errorMessage);
    }
    
    /**
     * 处理IllegalArgumentException异常
     */
    @ExceptionHandler(IllegalArgumentException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public Result<?> handleIllegalArgumentException(IllegalArgumentException e, HttpServletRequest request) {
        String apiPath = request.getRequestURI();
        String clientIp = getClientIp(request);
        
        log.warn("剪映API参数错误 - Path: {}, IP: {}, Error: {}", 
                apiPath, clientIp, e.getMessage());
        
        return Result.error(400, "参数错误：" + e.getMessage());
    }
    
    /**
     * 处理NullPointerException异常
     */
    @ExceptionHandler(NullPointerException.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public Result<?> handleNullPointerException(NullPointerException e, HttpServletRequest request) {
        String apiPath = request.getRequestURI();
        String clientIp = getClientIp(request);
        
        log.error("剪映API空指针异常 - Path: {}, IP: {}", apiPath, clientIp, e);
        
        return Result.error(500, "系统内部错误：空指针异常");
    }
    
    /**
     * 处理RuntimeException异常
     */
    @ExceptionHandler(RuntimeException.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public Result<?> handleRuntimeException(RuntimeException e, HttpServletRequest request) {
        String apiPath = request.getRequestURI();
        String clientIp = getClientIp(request);
        
        log.error("剪映API运行时异常 - Path: {}, IP: {}, Error: {}", 
                apiPath, clientIp, e.getMessage(), e);
        
        return Result.error(500, "系统内部错误：" + e.getMessage());
    }
    
    /**
     * 处理所有其他异常
     */
    @ExceptionHandler(Exception.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public Result<?> handleException(Exception e, HttpServletRequest request) {
        String apiPath = request.getRequestURI();
        String clientIp = getClientIp(request);
        
        log.error("剪映API未知异常 - Path: {}, IP: {}, Type: {}, Error: {}", 
                apiPath, clientIp, e.getClass().getSimpleName(), e.getMessage(), e);
        
        return Result.error(500, "系统内部错误：" + e.getMessage());
    }
    
    /**
     * 获取客户端真实IP地址
     */
    private String getClientIp(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        
        // 处理多个IP的情况，取第一个
        if (ip != null && ip.contains(",")) {
            ip = ip.split(",")[0].trim();
        }
        
        return ip;
    }
}
