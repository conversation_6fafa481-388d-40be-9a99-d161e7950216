{"version": 3, "file": "ponyfill.mjs", "sources": ["../src/stub/symbol.ts", "../node_modules/tslib/tslib.es6.js", "../src/utils.ts", "../src/lib/helpers/miscellaneous.ts", "../src/lib/helpers/webidl.ts", "../src/lib/simple-queue.ts", "../src/lib/abstract-ops/internal-methods.ts", "../src/lib/readable-stream/generic-reader.ts", "../src/stub/number-isfinite.ts", "../src/stub/math-trunc.ts", "../src/lib/validators/basic.ts", "../src/lib/validators/readable-stream.ts", "../src/lib/readable-stream/default-reader.ts", "../src/lib/abstract-ops/ecmascript.ts", "../src/target/es5/stub/async-iterator-prototype.ts", "../src/lib/readable-stream/async-iterator.ts", "../src/stub/number-isnan.ts", "../src/lib/abstract-ops/miscellaneous.ts", "../src/lib/abstract-ops/queue-with-sizes.ts", "../src/lib/helpers/array-buffer-view.ts", "../src/lib/readable-stream/byte-stream-controller.ts", "../src/lib/validators/reader-options.ts", "../src/lib/readable-stream/byob-reader.ts", "../src/lib/abstract-ops/queuing-strategy.ts", "../src/lib/validators/queuing-strategy.ts", "../src/lib/validators/underlying-sink.ts", "../src/lib/validators/writable-stream.ts", "../src/lib/abort-signal.ts", "../src/lib/writable-stream.ts", "../src/globals.ts", "../src/stub/dom-exception.ts", "../src/lib/readable-stream/pipe.ts", "../src/lib/readable-stream/default-controller.ts", "../src/lib/readable-stream/tee.ts", "../src/lib/readable-stream/readable-stream-like.ts", "../src/lib/readable-stream/from.ts", "../src/lib/validators/underlying-source.ts", "../src/lib/validators/iterator-options.ts", "../src/lib/validators/pipe-options.ts", "../src/lib/validators/readable-writable-pair.ts", "../src/lib/readable-stream.ts", "../src/lib/validators/queuing-strategy-init.ts", "../src/lib/byte-length-queuing-strategy.ts", "../src/lib/count-queuing-strategy.ts", "../src/lib/validators/transformer.ts", "../src/lib/transform-stream.ts"], "sourcesContent": ["/// <reference lib=\"es2015.symbol\" />\n\nconst SymbolPolyfill: (description?: string) => symbol =\n  typeof Symbol === 'function' && typeof Symbol.iterator === 'symbol' ?\n    Symbol :\n    description => `Symbol(${description})` as any as symbol;\n\nexport default SymbolPolyfill;\n", "/******************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global Reflect, Promise, SuppressedError, Symbol */\r\n\r\nvar extendStatics = function(d, b) {\r\n    extendStatics = Object.setPrototypeOf ||\r\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n        function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\r\n    return extendStatics(d, b);\r\n};\r\n\r\nexport function __extends(d, b) {\r\n    if (typeof b !== \"function\" && b !== null)\r\n        throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nexport var __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    }\r\n    return __assign.apply(this, arguments);\r\n}\r\n\r\nexport function __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\r\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\r\n                t[p[i]] = s[p[i]];\r\n        }\r\n    return t;\r\n}\r\n\r\nexport function __decorate(decorators, target, key, desc) {\r\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\r\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\r\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\r\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\r\n}\r\n\r\nexport function __param(paramIndex, decorator) {\r\n    return function (target, key) { decorator(target, key, paramIndex); }\r\n}\r\n\r\nexport function __esDecorate(ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {\r\n    function accept(f) { if (f !== void 0 && typeof f !== \"function\") throw new TypeError(\"Function expected\"); return f; }\r\n    var kind = contextIn.kind, key = kind === \"getter\" ? \"get\" : kind === \"setter\" ? \"set\" : \"value\";\r\n    var target = !descriptorIn && ctor ? contextIn[\"static\"] ? ctor : ctor.prototype : null;\r\n    var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});\r\n    var _, done = false;\r\n    for (var i = decorators.length - 1; i >= 0; i--) {\r\n        var context = {};\r\n        for (var p in contextIn) context[p] = p === \"access\" ? {} : contextIn[p];\r\n        for (var p in contextIn.access) context.access[p] = contextIn.access[p];\r\n        context.addInitializer = function (f) { if (done) throw new TypeError(\"Cannot add initializers after decoration has completed\"); extraInitializers.push(accept(f || null)); };\r\n        var result = (0, decorators[i])(kind === \"accessor\" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);\r\n        if (kind === \"accessor\") {\r\n            if (result === void 0) continue;\r\n            if (result === null || typeof result !== \"object\") throw new TypeError(\"Object expected\");\r\n            if (_ = accept(result.get)) descriptor.get = _;\r\n            if (_ = accept(result.set)) descriptor.set = _;\r\n            if (_ = accept(result.init)) initializers.unshift(_);\r\n        }\r\n        else if (_ = accept(result)) {\r\n            if (kind === \"field\") initializers.unshift(_);\r\n            else descriptor[key] = _;\r\n        }\r\n    }\r\n    if (target) Object.defineProperty(target, contextIn.name, descriptor);\r\n    done = true;\r\n};\r\n\r\nexport function __runInitializers(thisArg, initializers, value) {\r\n    var useValue = arguments.length > 2;\r\n    for (var i = 0; i < initializers.length; i++) {\r\n        value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);\r\n    }\r\n    return useValue ? value : void 0;\r\n};\r\n\r\nexport function __propKey(x) {\r\n    return typeof x === \"symbol\" ? x : \"\".concat(x);\r\n};\r\n\r\nexport function __setFunctionName(f, name, prefix) {\r\n    if (typeof name === \"symbol\") name = name.description ? \"[\".concat(name.description, \"]\") : \"\";\r\n    return Object.defineProperty(f, \"name\", { configurable: true, value: prefix ? \"\".concat(prefix, \" \", name) : name });\r\n};\r\n\r\nexport function __metadata(metadataKey, metadataValue) {\r\n    if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\r\n}\r\n\r\nexport function __awaiter(thisArg, _arguments, P, generator) {\r\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\nexport function __generator(thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (g && (g = 0, op[0] && (_ = 0)), _) try {\r\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [op[0] & 2, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n}\r\n\r\nexport var __createBinding = Object.create ? (function(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    var desc = Object.getOwnPropertyDescriptor(m, k);\r\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\r\n        desc = { enumerable: true, get: function() { return m[k]; } };\r\n    }\r\n    Object.defineProperty(o, k2, desc);\r\n}) : (function(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    o[k2] = m[k];\r\n});\r\n\r\nexport function __exportStar(m, o) {\r\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(o, p)) __createBinding(o, m, p);\r\n}\r\n\r\nexport function __values(o) {\r\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\r\n    if (m) return m.call(o);\r\n    if (o && typeof o.length === \"number\") return {\r\n        next: function () {\r\n            if (o && i >= o.length) o = void 0;\r\n            return { value: o && o[i++], done: !o };\r\n        }\r\n    };\r\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\r\n}\r\n\r\nexport function __read(o, n) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\r\n    if (!m) return o;\r\n    var i = m.call(o), r, ar = [], e;\r\n    try {\r\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\r\n    }\r\n    catch (error) { e = { error: error }; }\r\n    finally {\r\n        try {\r\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\r\n        }\r\n        finally { if (e) throw e.error; }\r\n    }\r\n    return ar;\r\n}\r\n\r\n/** @deprecated */\r\nexport function __spread() {\r\n    for (var ar = [], i = 0; i < arguments.length; i++)\r\n        ar = ar.concat(__read(arguments[i]));\r\n    return ar;\r\n}\r\n\r\n/** @deprecated */\r\nexport function __spreadArrays() {\r\n    for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\r\n    for (var r = Array(s), k = 0, i = 0; i < il; i++)\r\n        for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\r\n            r[k] = a[j];\r\n    return r;\r\n}\r\n\r\nexport function __spreadArray(to, from, pack) {\r\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\r\n        if (ar || !(i in from)) {\r\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\r\n            ar[i] = from[i];\r\n        }\r\n    }\r\n    return to.concat(ar || Array.prototype.slice.call(from));\r\n}\r\n\r\nexport function __await(v) {\r\n    return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n}\r\n\r\nexport function __asyncGenerator(thisArg, _arguments, generator) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n    function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\r\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\r\n    function fulfill(value) { resume(\"next\", value); }\r\n    function reject(value) { resume(\"throw\", value); }\r\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n}\r\n\r\nexport function __asyncDelegator(o) {\r\n    var i, p;\r\n    return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\r\n    function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: false } : f ? f(v) : v; } : f; }\r\n}\r\n\r\nexport function __asyncValues(o) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var m = o[Symbol.asyncIterator], i;\r\n    return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\r\n    function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\r\n    function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\r\n}\r\n\r\nexport function __makeTemplateObject(cooked, raw) {\r\n    if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\r\n    return cooked;\r\n};\r\n\r\nvar __setModuleDefault = Object.create ? (function(o, v) {\r\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\r\n}) : function(o, v) {\r\n    o[\"default\"] = v;\r\n};\r\n\r\nexport function __importStar(mod) {\r\n    if (mod && mod.__esModule) return mod;\r\n    var result = {};\r\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\r\n    __setModuleDefault(result, mod);\r\n    return result;\r\n}\r\n\r\nexport function __importDefault(mod) {\r\n    return (mod && mod.__esModule) ? mod : { default: mod };\r\n}\r\n\r\nexport function __classPrivateFieldGet(receiver, state, kind, f) {\r\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\r\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\r\n    return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\r\n}\r\n\r\nexport function __classPrivateFieldSet(receiver, state, value, kind, f) {\r\n    if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\r\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\r\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\r\n    return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\r\n}\r\n\r\nexport function __classPrivateFieldIn(state, receiver) {\r\n    if (receiver === null || (typeof receiver !== \"object\" && typeof receiver !== \"function\")) throw new TypeError(\"Cannot use 'in' operator on non-object\");\r\n    return typeof state === \"function\" ? receiver === state : state.has(receiver);\r\n}\r\n\r\nexport function __addDisposableResource(env, value, async) {\r\n    if (value !== null && value !== void 0) {\r\n        if (typeof value !== \"object\" && typeof value !== \"function\") throw new TypeError(\"Object expected.\");\r\n        var dispose;\r\n        if (async) {\r\n            if (!Symbol.asyncDispose) throw new TypeError(\"Symbol.asyncDispose is not defined.\");\r\n            dispose = value[Symbol.asyncDispose];\r\n        }\r\n        if (dispose === void 0) {\r\n            if (!Symbol.dispose) throw new TypeError(\"Symbol.dispose is not defined.\");\r\n            dispose = value[Symbol.dispose];\r\n        }\r\n        if (typeof dispose !== \"function\") throw new TypeError(\"Object not disposable.\");\r\n        env.stack.push({ value: value, dispose: dispose, async: async });\r\n    }\r\n    else if (async) {\r\n        env.stack.push({ async: true });\r\n    }\r\n    return value;\r\n}\r\n\r\nvar _SuppressedError = typeof SuppressedError === \"function\" ? SuppressedError : function (error, suppressed, message) {\r\n    var e = new Error(message);\r\n    return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\r\n};\r\n\r\nexport function __disposeResources(env) {\r\n    function fail(e) {\r\n        env.error = env.hasError ? new _SuppressedError(e, env.error, \"An error was suppressed during disposal.\") : e;\r\n        env.hasError = true;\r\n    }\r\n    function next() {\r\n        while (env.stack.length) {\r\n            var rec = env.stack.pop();\r\n            try {\r\n                var result = rec.dispose && rec.dispose.call(rec.value);\r\n                if (rec.async) return Promise.resolve(result).then(next, function(e) { fail(e); return next(); });\r\n            }\r\n            catch (e) {\r\n                fail(e);\r\n            }\r\n        }\r\n        if (env.hasError) throw env.error;\r\n    }\r\n    return next();\r\n}\r\n\r\nexport default {\r\n    __extends: __extends,\r\n    __assign: __assign,\r\n    __rest: __rest,\r\n    __decorate: __decorate,\r\n    __param: __param,\r\n    __metadata: __metadata,\r\n    __awaiter: __awaiter,\r\n    __generator: __generator,\r\n    __createBinding: __createBinding,\r\n    __exportStar: __exportStar,\r\n    __values: __values,\r\n    __read: __read,\r\n    __spread: __spread,\r\n    __spreadArrays: __spreadArrays,\r\n    __spreadArray: __spreadArray,\r\n    __await: __await,\r\n    __asyncGenerator: __asyncGenerator,\r\n    __asyncDelegator: __asyncDelegator,\r\n    __asyncValues: __asyncValues,\r\n    __makeTemplateObject: __makeTemplateObject,\r\n    __importStar: __importStar,\r\n    __importDefault: __importDefault,\r\n    __classPrivateFieldGet: __classPrivateFieldGet,\r\n    __classPrivateFieldSet: __classPrivateFieldSet,\r\n    __classPrivateFieldIn: __classPrivateFieldIn,\r\n    __addDisposableResource: __addDisposableResource,\r\n    __disposeResources: __disposeResources,\r\n};\r\n", "export function noop(): undefined {\n  return undefined;\n}\n", "import { noop } from '../../utils';\nimport { AssertionError } from '../../stub/assert';\n\nexport function typeIsObject(x: any): x is object {\n  return (typeof x === 'object' && x !== null) || typeof x === 'function';\n}\n\nexport const rethrowAssertionErrorRejection: (e: any) => void =\n  DEBUG ? e => {\n    // Used throughout the reference implementation, as `.catch(rethrowAssertionErrorRejection)`, to ensure any errors\n    // get shown. There are places in the spec where we do promise transformations and purposefully ignore or don't\n    // expect any errors, but assertion errors are always problematic.\n    if (e && e instanceof AssertionError) {\n      setTimeout(() => {\n        throw e;\n      }, 0);\n    }\n  } : noop;\n\nexport function setFunctionName(fn: Function, name: string): void {\n  try {\n    Object.defineProperty(fn, 'name', {\n      value: name,\n      configurable: true\n    });\n  } catch {\n    // This property is non-configurable in older browsers, so ignore if this throws.\n    // https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Function/name#browser_compatibility\n  }\n}\n", "import { rethrowAssertionErrorRejection } from './miscellaneous';\nimport assert from '../../stub/assert';\n\nconst originalPromise = Promise;\nconst originalPromiseThen = Promise.prototype.then;\nconst originalPromiseReject = Promise.reject.bind(originalPromise);\n\n// https://webidl.spec.whatwg.org/#a-new-promise\nexport function newPromise<T>(executor: (\n  resolve: (value: T | PromiseLike<T>) => void,\n  reject: (reason?: any) => void\n) => void): Promise<T> {\n  return new originalPromise(executor);\n}\n\n// https://webidl.spec.whatwg.org/#a-promise-resolved-with\nexport function promiseResolvedWith<T>(value: T | PromiseLike<T>): Promise<T> {\n  return newPromise(resolve => resolve(value));\n}\n\n// https://webidl.spec.whatwg.org/#a-promise-rejected-with\nexport function promiseRejectedWith<T = never>(reason: any): Promise<T> {\n  return originalPromiseReject(reason);\n}\n\nexport function PerformPromiseThen<T, TResult1 = T, TResult2 = never>(\n  promise: Promise<T>,\n  onFulfilled?: (value: T) => TResult1 | PromiseLike<TResult1>,\n  onRejected?: (reason: any) => TResult2 | PromiseLike<TResult2>): Promise<TResult1 | TResult2> {\n  // There doesn't appear to be any way to correctly emulate the behaviour from JavaScript, so this is just an\n  // approximation.\n  return originalPromiseThen.call(promise, onFulfilled, onRejected) as Promise<TResult1 | TResult2>;\n}\n\n// Bluebird logs a warning when a promise is created within a fulfillment handler, but then isn't returned\n// from that handler. To prevent this, return null instead of void from all handlers.\n// http://bluebirdjs.com/docs/warning-explanations.html#warning-a-promise-was-created-in-a-handler-but-was-not-returned-from-it\nexport function uponPromise<T>(\n  promise: Promise<T>,\n  onFulfilled?: (value: T) => null | PromiseLike<null>,\n  onRejected?: (reason: any) => null | PromiseLike<null>): void {\n  PerformPromiseThen(\n    PerformPromiseThen(promise, onFulfilled, onRejected),\n    undefined,\n    rethrowAssertionErrorRejection\n  );\n}\n\nexport function uponFulfillment<T>(promise: Promise<T>, onFulfilled: (value: T) => null | PromiseLike<null>): void {\n  uponPromise(promise, onFulfilled);\n}\n\nexport function uponRejection(promise: Promise<unknown>, onRejected: (reason: any) => null | PromiseLike<null>): void {\n  uponPromise(promise, undefined, onRejected);\n}\n\nexport function transformPromiseWith<T, TResult1 = T, TResult2 = never>(\n  promise: Promise<T>,\n  fulfillmentHandler?: (value: T) => TResult1 | PromiseLike<TResult1>,\n  rejectionHandler?: (reason: any) => TResult2 | PromiseLike<TResult2>): Promise<TResult1 | TResult2> {\n  return PerformPromiseThen(promise, fulfillmentHandler, rejectionHandler);\n}\n\nexport function setPromiseIsHandledToTrue(promise: Promise<unknown>): void {\n  PerformPromiseThen(promise, undefined, rethrowAssertionErrorRejection);\n}\n\nlet _queueMicrotask: (callback: () => void) => void = callback => {\n  if (typeof queueMicrotask === 'function') {\n    _queueMicrotask = queueMicrotask;\n  } else {\n    const resolvedPromise = promiseResolvedWith(undefined);\n    _queueMicrotask = cb => PerformPromiseThen(resolvedPromise, cb);\n  }\n  return _queueMicrotask(callback);\n};\n\nexport { _queueMicrotask as queueMicrotask };\n\nexport function reflectCall<T, A extends any[], R>(F: (this: T, ...fnArgs: A) => R, V: T, args: A): R {\n  if (typeof F !== 'function') {\n    throw new TypeError('Argument is not a function');\n  }\n  return Function.prototype.apply.call(F, V, args);\n}\n\nexport function promiseCall<T, A extends any[], R>(F: (this: T, ...fnArgs: A) => R | PromiseLike<R>,\n                                                   V: T,\n                                                   args: A): Promise<R> {\n  assert(typeof F === 'function');\n  assert(V !== undefined);\n  assert(Array.isArray(args));\n  try {\n    return promiseResolvedWith(reflectCall(F, V, args));\n  } catch (value) {\n    return promiseRejectedWith(value);\n  }\n}\n", "import assert from '../stub/assert';\n\n// Original from Chromium\n// https://chromium.googlesource.com/chromium/src/+/0aee4434a4dba42a42abaea9bfbc0cd196a63bc1/third_party/blink/renderer/core/streams/SimpleQueue.js\n\nconst QUEUE_MAX_ARRAY_SIZE = 16384;\n\ninterface Node<T> {\n  _elements: T[];\n  _next: Node<T> | undefined;\n}\n\n/**\n * Simple queue structure.\n *\n * Avoids scalability issues with using a packed array directly by using\n * multiple arrays in a linked list and keeping the array size bounded.\n */\nexport class SimpleQueue<T> {\n  private _front: Node<T>;\n  private _back: Node<T>;\n  private _cursor = 0;\n  private _size = 0;\n\n  constructor() {\n    // _front and _back are always defined.\n    this._front = {\n      _elements: [],\n      _next: undefined\n    };\n    this._back = this._front;\n    // The cursor is used to avoid calling Array.shift().\n    // It contains the index of the front element of the array inside the\n    // front-most node. It is always in the range [0, QUEUE_MAX_ARRAY_SIZE).\n    this._cursor = 0;\n    // When there is only one node, size === elements.length - cursor.\n    this._size = 0;\n  }\n\n  get length(): number {\n    return this._size;\n  }\n\n  // For exception safety, this method is structured in order:\n  // 1. Read state\n  // 2. Calculate required state mutations\n  // 3. Perform state mutations\n  push(element: T): void {\n    const oldBack = this._back;\n    let newBack = oldBack;\n    assert(oldBack._next === undefined);\n    if (oldBack._elements.length === QUEUE_MAX_ARRAY_SIZE - 1) {\n      newBack = {\n        _elements: [],\n        _next: undefined\n      };\n    }\n\n    // push() is the mutation most likely to throw an exception, so it\n    // goes first.\n    oldBack._elements.push(element);\n    if (newBack !== oldBack) {\n      this._back = newBack;\n      oldBack._next = newBack;\n    }\n    ++this._size;\n  }\n\n  // Like push(), shift() follows the read -> calculate -> mutate pattern for\n  // exception safety.\n  shift(): T {\n    assert(this._size > 0); // must not be called on an empty queue\n\n    const oldFront = this._front;\n    let newFront = oldFront;\n    const oldCursor = this._cursor;\n    let newCursor = oldCursor + 1;\n\n    const elements = oldFront._elements;\n    const element = elements[oldCursor];\n\n    if (newCursor === QUEUE_MAX_ARRAY_SIZE) {\n      assert(elements.length === QUEUE_MAX_ARRAY_SIZE);\n      assert(oldFront._next !== undefined);\n      newFront = oldFront._next!;\n      newCursor = 0;\n    }\n\n    // No mutations before this point.\n    --this._size;\n    this._cursor = newCursor;\n    if (oldFront !== newFront) {\n      this._front = newFront;\n    }\n\n    // Permit shifted element to be garbage collected.\n    elements[oldCursor] = undefined!;\n\n    return element;\n  }\n\n  // The tricky thing about forEach() is that it can be called\n  // re-entrantly. The queue may be mutated inside the callback. It is easy to\n  // see that push() within the callback has no negative effects since the end\n  // of the queue is checked for on every iteration. If shift() is called\n  // repeatedly within the callback then the next iteration may return an\n  // element that has been removed. In this case the callback will be called\n  // with undefined values until we either \"catch up\" with elements that still\n  // exist or reach the back of the queue.\n  forEach(callback: (element: T) => void): void {\n    let i = this._cursor;\n    let node = this._front;\n    let elements = node._elements;\n    while (i !== elements.length || node._next !== undefined) {\n      if (i === elements.length) {\n        assert(node._next !== undefined);\n        assert(i === QUEUE_MAX_ARRAY_SIZE);\n        node = node._next!;\n        elements = node._elements;\n        i = 0;\n        if (elements.length === 0) {\n          break;\n        }\n      }\n      callback(elements[i]);\n      ++i;\n    }\n  }\n\n  // Return the element that would be returned if shift() was called now,\n  // without modifying the queue.\n  peek(): T {\n    assert(this._size > 0); // must not be called on an empty queue\n\n    const front = this._front;\n    const cursor = this._cursor;\n    return front._elements[cursor];\n  }\n}\n", "export const AbortSteps = Symbol('[[AbortSteps]]');\nexport const ErrorSteps = Symbol('[[ErrorSteps]]');\nexport const CancelSteps = Symbol('[[CancelSteps]]');\nexport const PullSteps = Symbol('[[PullSteps]]');\nexport const ReleaseSteps = Symbol('[[ReleaseSteps]]');\n", "import assert from '../../stub/assert';\nimport { ReadableStream, ReadableStreamCancel, type ReadableStreamReader } from '../readable-stream';\nimport { newPromise, setPromiseIsHandledToTrue } from '../helpers/webidl';\nimport { ReleaseSteps } from '../abstract-ops/internal-methods';\n\nexport function ReadableStreamReaderGenericInitialize<R>(reader: ReadableStreamReader<R>, stream: ReadableStream<R>) {\n  reader._ownerReadableStream = stream;\n  stream._reader = reader;\n\n  if (stream._state === 'readable') {\n    defaultReaderClosedPromiseInitialize(reader);\n  } else if (stream._state === 'closed') {\n    defaultReaderClosedPromiseInitializeAsResolved(reader);\n  } else {\n    assert(stream._state === 'errored');\n\n    defaultReaderClosedPromiseInitializeAsRejected(reader, stream._storedError);\n  }\n}\n\n// A client of ReadableStreamDefaultReader and ReadableStreamBYO<PERSON>eader may use these functions directly to bypass state\n// check.\n\nexport function ReadableStreamReaderGenericCancel(reader: ReadableStreamReader<any>, reason: any): Promise<undefined> {\n  const stream = reader._ownerReadableStream;\n  assert(stream !== undefined);\n  return ReadableStreamCancel(stream, reason);\n}\n\nexport function ReadableStreamReaderGenericRelease(reader: ReadableStreamReader<any>) {\n  const stream = reader._ownerReadableStream;\n  assert(stream !== undefined);\n  assert(stream._reader === reader);\n\n  if (stream._state === 'readable') {\n    defaultReaderClosedPromiseReject(\n      reader,\n      new TypeError(`Reader was released and can no longer be used to monitor the stream's closedness`));\n  } else {\n    defaultReaderClosedPromiseResetToRejected(\n      reader,\n      new TypeError(`Reader was released and can no longer be used to monitor the stream's closedness`));\n  }\n\n  stream._readableStreamController[ReleaseSteps]();\n\n  stream._reader = undefined;\n  reader._ownerReadableStream = undefined!;\n}\n\n// Helper functions for the readers.\n\nexport function readerLockException(name: string): TypeError {\n  return new TypeError('Cannot ' + name + ' a stream using a released reader');\n}\n\n// Helper functions for the ReadableStreamDefaultReader.\n\nexport function defaultReaderClosedPromiseInitialize(reader: ReadableStreamReader<any>) {\n  reader._closedPromise = newPromise((resolve, reject) => {\n    reader._closedPromise_resolve = resolve;\n    reader._closedPromise_reject = reject;\n  });\n}\n\nexport function defaultReaderClosedPromiseInitializeAsRejected(reader: ReadableStreamReader<any>, reason: any) {\n  defaultReaderClosedPromiseInitialize(reader);\n  defaultReaderClosedPromiseReject(reader, reason);\n}\n\nexport function defaultReaderClosedPromiseInitializeAsResolved(reader: ReadableStreamReader<any>) {\n  defaultReaderClosedPromiseInitialize(reader);\n  defaultReaderClosedPromiseResolve(reader);\n}\n\nexport function defaultReaderClosedPromiseReject(reader: ReadableStreamReader<any>, reason: any) {\n  if (reader._closedPromise_reject === undefined) {\n    return;\n  }\n\n  setPromiseIsHandledToTrue(reader._closedPromise);\n  reader._closedPromise_reject(reason);\n  reader._closedPromise_resolve = undefined;\n  reader._closedPromise_reject = undefined;\n}\n\nexport function defaultReaderClosedPromiseResetToRejected(reader: ReadableStreamReader<any>, reason: any) {\n  assert(reader._closedPromise_resolve === undefined);\n  assert(reader._closedPromise_reject === undefined);\n\n  defaultReaderClosedPromiseInitializeAsRejected(reader, reason);\n}\n\nexport function defaultReaderClosedPromiseResolve(reader: ReadableStreamReader<any>) {\n  if (reader._closedPromise_resolve === undefined) {\n    return;\n  }\n\n  reader._closedPromise_resolve(undefined);\n  reader._closedPromise_resolve = undefined;\n  reader._closedPromise_reject = undefined;\n}\n", "/// <reference lib=\"es2015.core\" />\n\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number/isFinite#Polyfill\nconst NumberIsFinite: typeof Number.isFinite = Number.isFinite || function (x) {\n  return typeof x === 'number' && isFinite(x);\n};\n\nexport default NumberIsFinite;\n", "/// <reference lib=\"es2015.core\" />\n\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Math/trunc#Polyfill\nconst MathTrunc: typeof Math.trunc = Math.trunc || function (v) {\n  return v < 0 ? Math.ceil(v) : Math.floor(v);\n};\n\nexport default MathTrunc;\n", "import NumberIsFinite from '../../stub/number-isfinite';\nimport MathTrunc from '../../stub/math-trunc';\n\n// https://heycam.github.io/webidl/#idl-dictionaries\nexport function isDictionary(x: any): x is object | null {\n  return typeof x === 'object' || typeof x === 'function';\n}\n\nexport function assertDictionary(obj: unknown,\n                                 context: string): asserts obj is object | null | undefined {\n  if (obj !== undefined && !isDictionary(obj)) {\n    throw new TypeError(`${context} is not an object.`);\n  }\n}\n\nexport type AnyFunction = (...args: any[]) => any;\n\n// https://heycam.github.io/webidl/#idl-callback-functions\nexport function assertFunction(x: unknown, context: string): asserts x is AnyFunction {\n  if (typeof x !== 'function') {\n    throw new TypeError(`${context} is not a function.`);\n  }\n}\n\n// https://heycam.github.io/webidl/#idl-object\nexport function isObject(x: any): x is object {\n  return (typeof x === 'object' && x !== null) || typeof x === 'function';\n}\n\nexport function assertObject(x: unknown,\n                             context: string): asserts x is object {\n  if (!isObject(x)) {\n    throw new TypeError(`${context} is not an object.`);\n  }\n}\n\nexport function assertRequiredArgument<T>(x: T | undefined,\n                                          position: number,\n                                          context: string): asserts x is T {\n  if (x === undefined) {\n    throw new TypeError(`Parameter ${position} is required in '${context}'.`);\n  }\n}\n\nexport function assertRequiredField<T>(x: T | undefined,\n                                       field: string,\n                                       context: string): asserts x is T {\n  if (x === undefined) {\n    throw new TypeError(`${field} is required in '${context}'.`);\n  }\n}\n\n// https://heycam.github.io/webidl/#idl-unrestricted-double\nexport function convertUnrestrictedDouble(value: unknown): number {\n  return Number(value);\n}\n\nfunction censorNegativeZero(x: number): number {\n  return x === 0 ? 0 : x;\n}\n\nfunction integerPart(x: number): number {\n  return censorNegativeZero(MathTrunc(x));\n}\n\n// https://heycam.github.io/webidl/#idl-unsigned-long-long\nexport function convertUnsignedLongLongWithEnforceRange(value: unknown, context: string): number {\n  const lowerBound = 0;\n  const upperBound = Number.MAX_SAFE_INTEGER;\n\n  let x = Number(value);\n  x = censorNegativeZero(x);\n\n  if (!NumberIsFinite(x)) {\n    throw new TypeError(`${context} is not a finite number`);\n  }\n\n  x = integerPart(x);\n\n  if (x < lowerBound || x > upperBound) {\n    throw new TypeError(`${context} is outside the accepted range of ${lowerBound} to ${upperBound}, inclusive`);\n  }\n\n  if (!NumberIsFinite(x) || x === 0) {\n    return 0;\n  }\n\n  // TODO Use BigInt if supported?\n  // let xBigInt = BigInt(integerPart(x));\n  // xBigInt = BigInt.asUintN(64, xBigInt);\n  // return Number(xBigInt);\n\n  return x;\n}\n", "import { IsReadableStream, ReadableStream } from '../readable-stream';\n\nexport function assertReadableStream(x: unknown, context: string): asserts x is ReadableStream {\n  if (!IsReadableStream(x)) {\n    throw new TypeError(`${context} is not a ReadableStream.`);\n  }\n}\n", "import assert from '../../stub/assert';\nimport { SimpleQueue } from '../simple-queue';\nimport {\n  ReadableStreamReaderGenericCancel,\n  ReadableStreamReaderGenericInitialize,\n  ReadableStreamReaderGenericRelease,\n  readerLockException\n} from './generic-reader';\nimport { IsReadableStreamLocked, ReadableStream } from '../readable-stream';\nimport { setFunctionName, typeIsObject } from '../helpers/miscellaneous';\nimport { PullSteps } from '../abstract-ops/internal-methods';\nimport { newPromise, promiseRejectedWith } from '../helpers/webidl';\nimport { assertRequiredArgument } from '../validators/basic';\nimport { assertReadableStream } from '../validators/readable-stream';\n\n/**\n * A result returned by {@link ReadableStreamDefaultReader.read}.\n *\n * @public\n */\nexport type ReadableStreamDefaultReadResult<T> = {\n  done: false;\n  value: T;\n} | {\n  done: true;\n  value?: undefined;\n}\n\n// Abstract operations for the ReadableStream.\n\nexport function AcquireReadableStreamDefaultReader<R>(stream: ReadableStream): ReadableStreamDefaultReader<R> {\n  return new ReadableStreamDefaultReader(stream);\n}\n\n// ReadableStream API exposed for controllers.\n\nexport function ReadableStreamAddReadRequest<R>(stream: ReadableStream<R>,\n                                                readRequest: ReadRequest<R>): void {\n  assert(IsReadableStreamDefaultReader(stream._reader));\n  assert(stream._state === 'readable');\n\n  (stream._reader! as ReadableStreamDefaultReader<R>)._readRequests.push(readRequest);\n}\n\nexport function ReadableStreamFulfillReadRequest<R>(stream: ReadableStream<R>, chunk: R | undefined, done: boolean) {\n  const reader = stream._reader as ReadableStreamDefaultReader<R>;\n\n  assert(reader._readRequests.length > 0);\n\n  const readRequest = reader._readRequests.shift()!;\n  if (done) {\n    readRequest._closeSteps();\n  } else {\n    readRequest._chunkSteps(chunk!);\n  }\n}\n\nexport function ReadableStreamGetNumReadRequests<R>(stream: ReadableStream<R>): number {\n  return (stream._reader as ReadableStreamDefaultReader<R>)._readRequests.length;\n}\n\nexport function ReadableStreamHasDefaultReader(stream: ReadableStream): boolean {\n  const reader = stream._reader;\n\n  if (reader === undefined) {\n    return false;\n  }\n\n  if (!IsReadableStreamDefaultReader(reader)) {\n    return false;\n  }\n\n  return true;\n}\n\n// Readers\n\nexport interface ReadRequest<R> {\n  _chunkSteps(chunk: R): void;\n\n  _closeSteps(): void;\n\n  _errorSteps(e: any): void;\n}\n\n/**\n * A default reader vended by a {@link ReadableStream}.\n *\n * @public\n */\nexport class ReadableStreamDefaultReader<R = any> {\n  /** @internal */\n  _ownerReadableStream!: ReadableStream<R>;\n  /** @internal */\n  _closedPromise!: Promise<undefined>;\n  /** @internal */\n  _closedPromise_resolve?: (value?: undefined) => void;\n  /** @internal */\n  _closedPromise_reject?: (reason: any) => void;\n  /** @internal */\n  _readRequests: SimpleQueue<ReadRequest<R>>;\n\n  constructor(stream: ReadableStream<R>) {\n    assertRequiredArgument(stream, 1, 'ReadableStreamDefaultReader');\n    assertReadableStream(stream, 'First parameter');\n\n    if (IsReadableStreamLocked(stream)) {\n      throw new TypeError('This stream has already been locked for exclusive reading by another reader');\n    }\n\n    ReadableStreamReaderGenericInitialize(this, stream);\n\n    this._readRequests = new SimpleQueue();\n  }\n\n  /**\n   * Returns a promise that will be fulfilled when the stream becomes closed,\n   * or rejected if the stream ever errors or the reader's lock is released before the stream finishes closing.\n   */\n  get closed(): Promise<undefined> {\n    if (!IsReadableStreamDefaultReader(this)) {\n      return promiseRejectedWith(defaultReaderBrandCheckException('closed'));\n    }\n\n    return this._closedPromise;\n  }\n\n  /**\n   * If the reader is active, behaves the same as {@link ReadableStream.cancel | stream.cancel(reason)}.\n   */\n  cancel(reason: any = undefined): Promise<void> {\n    if (!IsReadableStreamDefaultReader(this)) {\n      return promiseRejectedWith(defaultReaderBrandCheckException('cancel'));\n    }\n\n    if (this._ownerReadableStream === undefined) {\n      return promiseRejectedWith(readerLockException('cancel'));\n    }\n\n    return ReadableStreamReaderGenericCancel(this, reason);\n  }\n\n  /**\n   * Returns a promise that allows access to the next chunk from the stream's internal queue, if available.\n   *\n   * If reading a chunk causes the queue to become empty, more data will be pulled from the underlying source.\n   */\n  read(): Promise<ReadableStreamDefaultReadResult<R>> {\n    if (!IsReadableStreamDefaultReader(this)) {\n      return promiseRejectedWith(defaultReaderBrandCheckException('read'));\n    }\n\n    if (this._ownerReadableStream === undefined) {\n      return promiseRejectedWith(readerLockException('read from'));\n    }\n\n    let resolvePromise!: (result: ReadableStreamDefaultReadResult<R>) => void;\n    let rejectPromise!: (reason: any) => void;\n    const promise = newPromise<ReadableStreamDefaultReadResult<R>>((resolve, reject) => {\n      resolvePromise = resolve;\n      rejectPromise = reject;\n    });\n    const readRequest: ReadRequest<R> = {\n      _chunkSteps: chunk => resolvePromise({ value: chunk, done: false }),\n      _closeSteps: () => resolvePromise({ value: undefined, done: true }),\n      _errorSteps: e => rejectPromise(e)\n    };\n    ReadableStreamDefaultReaderRead(this, readRequest);\n    return promise;\n  }\n\n  /**\n   * Releases the reader's lock on the corresponding stream. After the lock is released, the reader is no longer active.\n   * If the associated stream is errored when the lock is released, the reader will appear errored in the same way\n   * from now on; otherwise, the reader will appear closed.\n   *\n   * A reader's lock cannot be released while it still has a pending read request, i.e., if a promise returned by\n   * the reader's {@link ReadableStreamDefaultReader.read | read()} method has not yet been settled. Attempting to\n   * do so will throw a `TypeError` and leave the reader locked to the stream.\n   */\n  releaseLock(): void {\n    if (!IsReadableStreamDefaultReader(this)) {\n      throw defaultReaderBrandCheckException('releaseLock');\n    }\n\n    if (this._ownerReadableStream === undefined) {\n      return;\n    }\n\n    ReadableStreamDefaultReaderRelease(this);\n  }\n}\n\nObject.defineProperties(ReadableStreamDefaultReader.prototype, {\n  cancel: { enumerable: true },\n  read: { enumerable: true },\n  releaseLock: { enumerable: true },\n  closed: { enumerable: true }\n});\nsetFunctionName(ReadableStreamDefaultReader.prototype.cancel, 'cancel');\nsetFunctionName(ReadableStreamDefaultReader.prototype.read, 'read');\nsetFunctionName(ReadableStreamDefaultReader.prototype.releaseLock, 'releaseLock');\nif (typeof Symbol.toStringTag === 'symbol') {\n  Object.defineProperty(ReadableStreamDefaultReader.prototype, Symbol.toStringTag, {\n    value: 'ReadableStreamDefaultReader',\n    configurable: true\n  });\n}\n\n// Abstract operations for the readers.\n\nexport function IsReadableStreamDefaultReader<R = any>(x: any): x is ReadableStreamDefaultReader<R> {\n  if (!typeIsObject(x)) {\n    return false;\n  }\n\n  if (!Object.prototype.hasOwnProperty.call(x, '_readRequests')) {\n    return false;\n  }\n\n  return x instanceof ReadableStreamDefaultReader;\n}\n\nexport function ReadableStreamDefaultReaderRead<R>(reader: ReadableStreamDefaultReader<R>,\n                                                   readRequest: ReadRequest<R>): void {\n  const stream = reader._ownerReadableStream;\n\n  assert(stream !== undefined);\n\n  stream._disturbed = true;\n\n  if (stream._state === 'closed') {\n    readRequest._closeSteps();\n  } else if (stream._state === 'errored') {\n    readRequest._errorSteps(stream._storedError);\n  } else {\n    assert(stream._state === 'readable');\n    stream._readableStreamController[PullSteps](readRequest as ReadRequest<any>);\n  }\n}\n\nexport function ReadableStreamDefaultReaderRelease(reader: ReadableStreamDefaultReader) {\n  ReadableStreamReaderGenericRelease(reader);\n  const e = new TypeError('Reader was released');\n  ReadableStreamDefaultReaderErrorReadRequests(reader, e);\n}\n\nexport function ReadableStreamDefaultReaderErrorReadRequests(reader: ReadableStreamDefaultReader, e: any) {\n  const readRequests = reader._readRequests;\n  reader._readRequests = new SimpleQueue();\n  readRequests.forEach(readRequest => {\n    readRequest._errorSteps(e);\n  });\n}\n\n// Helper functions for the ReadableStreamDefaultReader.\n\nfunction defaultReaderBrandCheckException(name: string): TypeError {\n  return new TypeError(\n    `ReadableStreamDefaultReader.prototype.${name} can only be used on a ReadableStreamDefaultReader`);\n}\n", "import { reflectCall } from 'lib/helpers/webidl';\nimport { typeIsObject } from '../helpers/miscellaneous';\nimport assert from '../../stub/assert';\n\ndeclare global {\n  interface ArrayBuffer {\n    readonly detached: boolean;\n\n    transfer(): ArrayBuffer;\n  }\n\n  function structuredClone<T>(value: T, options: { transfer: ArrayBuffer[] }): T;\n}\n\nexport function CreateArrayFromList<T extends any[]>(elements: T): T {\n  // We use arrays to represent lists, so this is basically a no-op.\n  // Do a slice though just in case we happen to depend on the unique-ness.\n  return elements.slice() as T;\n}\n\nexport function CopyDataBlockBytes(dest: ArrayBuffer,\n                                   destOffset: number,\n                                   src: ArrayBuffer,\n                                   srcOffset: number,\n                                   n: number) {\n  new Uint8Array(dest).set(new Uint8Array(src, srcOffset, n), destOffset);\n}\n\nexport let TransferArrayBuffer = (O: ArrayBuffer): ArrayBuffer => {\n  if (typeof O.transfer === 'function') {\n    TransferArrayBuffer = buffer => buffer.transfer();\n  } else if (typeof structuredClone === 'function') {\n    TransferArrayBuffer = buffer => structuredClone(buffer, { transfer: [buffer] });\n  } else {\n    // Not implemented correctly\n    TransferArrayBuffer = buffer => buffer;\n  }\n  return TransferArrayBuffer(O);\n};\n\nexport function CanTransferArrayBuffer(O: ArrayBuffer): boolean {\n  return !IsDetachedBuffer(O);\n}\n\nexport let IsDetachedBuffer = (O: ArrayBuffer): boolean => {\n  if (typeof O.detached === 'boolean') {\n    IsDetachedBuffer = buffer => buffer.detached;\n  } else {\n    // Not implemented correctly\n    IsDetachedBuffer = buffer => buffer.byteLength === 0;\n  }\n  return IsDetachedBuffer(O);\n};\n\nexport function ArrayBufferSlice(buffer: ArrayBuffer, begin: number, end: number): ArrayBuffer {\n  // ArrayBuffer.prototype.slice is not available on IE10\n  // https://www.caniuse.com/mdn-javascript_builtins_arraybuffer_slice\n  if (buffer.slice) {\n    return buffer.slice(begin, end);\n  }\n  const length = end - begin;\n  const slice = new ArrayBuffer(length);\n  CopyDataBlockBytes(slice, 0, buffer, begin, length);\n  return slice;\n}\n\nexport type MethodName<T> = {\n  [P in keyof T]: T[P] extends Function | undefined ? P : never;\n}[keyof T];\n\nexport function GetMethod<T, K extends MethodName<T>>(receiver: T, prop: K): T[K] | undefined {\n  const func = receiver[prop];\n  if (func === undefined || func === null) {\n    return undefined;\n  }\n  if (typeof func !== 'function') {\n    throw new TypeError(`${String(prop)} is not a function`);\n  }\n  return func;\n}\n\nexport interface SyncIteratorRecord<T> {\n  iterator: Iterator<T>,\n  nextMethod: Iterator<T>['next'],\n  done: boolean;\n}\n\nexport interface AsyncIteratorRecord<T> {\n  iterator: AsyncIterator<T>,\n  nextMethod: AsyncIterator<T>['next'],\n  done: boolean;\n}\n\nexport type SyncOrAsyncIteratorRecord<T> = SyncIteratorRecord<T> | AsyncIteratorRecord<T>;\n\nexport function CreateAsyncFromSyncIterator<T>(syncIteratorRecord: SyncIteratorRecord<T>): AsyncIteratorRecord<T> {\n  // Instead of re-implementing CreateAsyncFromSyncIterator and %AsyncFromSyncIteratorPrototype%,\n  // we use yield* inside an async generator function to achieve the same result.\n\n  // Wrap the sync iterator inside a sync iterable, so we can use it with yield*.\n  const syncIterable = {\n    [Symbol.iterator]: () => syncIteratorRecord.iterator\n  };\n  // Create an async generator function and immediately invoke it.\n  const asyncIterator = (async function* () {\n    return yield* syncIterable;\n  }());\n  // Return as an async iterator record.\n  const nextMethod = asyncIterator.next;\n  return { iterator: asyncIterator, nextMethod, done: false };\n}\n\n// Aligns with core-js/modules/es.symbol.async-iterator.js\nexport const SymbolAsyncIterator: (typeof Symbol)['asyncIterator'] =\n  Symbol.asyncIterator ??\n  Symbol.for?.('Symbol.asyncIterator') ??\n  '@@asyncIterator';\n\nexport type SyncOrAsyncIterable<T> = Iterable<T> | AsyncIterable<T>;\nexport type SyncOrAsyncIteratorMethod<T> = () => (Iterator<T> | AsyncIterator<T>);\n\nfunction GetIterator<T>(\n  obj: SyncOrAsyncIterable<T>,\n  hint: 'async',\n  method?: SyncOrAsyncIteratorMethod<T>\n): AsyncIteratorRecord<T>;\nfunction GetIterator<T>(\n  obj: Iterable<T>,\n  hint: 'sync',\n  method?: SyncOrAsyncIteratorMethod<T>\n): SyncIteratorRecord<T>;\nfunction GetIterator<T>(\n  obj: SyncOrAsyncIterable<T>,\n  hint = 'sync',\n  method?: SyncOrAsyncIteratorMethod<T>\n): SyncOrAsyncIteratorRecord<T> {\n  assert(hint === 'sync' || hint === 'async');\n  if (method === undefined) {\n    if (hint === 'async') {\n      method = GetMethod(obj as AsyncIterable<T>, SymbolAsyncIterator);\n      if (method === undefined) {\n        const syncMethod = GetMethod(obj as Iterable<T>, Symbol.iterator);\n        const syncIteratorRecord = GetIterator(obj as Iterable<T>, 'sync', syncMethod);\n        return CreateAsyncFromSyncIterator(syncIteratorRecord);\n      }\n    } else {\n      method = GetMethod(obj as Iterable<T>, Symbol.iterator);\n    }\n  }\n  if (method === undefined) {\n    throw new TypeError('The object is not iterable');\n  }\n  const iterator = reflectCall(method, obj, []);\n  if (!typeIsObject(iterator)) {\n    throw new TypeError('The iterator method must return an object');\n  }\n  const nextMethod = iterator.next;\n  return { iterator, nextMethod, done: false } as SyncOrAsyncIteratorRecord<T>;\n}\n\nexport { GetIterator };\n\nexport function IteratorNext<T>(iteratorRecord: AsyncIteratorRecord<T>): Promise<IteratorResult<T>> {\n  const result = reflectCall(iteratorRecord.nextMethod, iteratorRecord.iterator, []);\n  if (!typeIsObject(result)) {\n    throw new TypeError('The iterator.next() method must return an object');\n  }\n  return result;\n}\n\nexport function IteratorComplete<TReturn>(\n  iterResult: IteratorResult<unknown, TReturn>\n): iterResult is IteratorReturnResult<TReturn> {\n  assert(typeIsObject(iterResult));\n  return Boolean(iterResult.done);\n}\n\nexport function IteratorValue<T>(iterResult: IteratorYieldResult<T>): T {\n  assert(typeIsObject(iterResult));\n  return iterResult.value;\n}\n", "/// <reference lib=\"es2018.asynciterable\" />\n\nimport { SymbolAsyncIterator } from '../../../lib/abstract-ops/ecmascript';\n\n// We cannot access %AsyncIteratorPrototype% without non-ES2018 syntax, but we can re-create it.\nexport const AsyncIteratorPrototype: AsyncIterable<any> = {\n  // ******** %AsyncIteratorPrototype% [ @@asyncIterator ] ( )\n  // https://tc39.github.io/ecma262/#sec-asynciteratorprototype-asynciterator\n  [SymbolAsyncIterator](this: AsyncIterator<any>) {\n    return this;\n  }\n};\nObject.defineProperty(AsyncIteratorPrototype, SymbolAsyncIterator, { enumerable: false });\n", "/// <reference lib=\"es2018.asynciterable\" />\n\nimport { ReadableStream } from '../readable-stream';\nimport {\n  AcquireReadableStreamDefaultReader,\n  ReadableStreamDefaultReader,\n  ReadableStreamDefaultReaderRead,\n  type ReadableStreamDefaultReadResult,\n  type ReadRequest\n} from './default-reader';\nimport { ReadableStreamReaderGenericCancel, ReadableStreamReaderGenericRelease } from './generic-reader';\nimport assert from '../../stub/assert';\nimport { AsyncIteratorPrototype } from '@@target/stub/async-iterator-prototype';\nimport { typeIsObject } from '../helpers/miscellaneous';\nimport {\n  newPromise,\n  promiseRejectedWith,\n  promiseResolvedWith,\n  queueMicrotask,\n  transformPromiseWith\n} from '../helpers/webidl';\n\n/**\n * An async iterator returned by {@link ReadableStream.values}.\n *\n * @public\n */\nexport interface ReadableStreamAsyncIterator<R> extends AsyncIterableIterator<R> {\n  next(): Promise<IteratorResult<R, undefined>>;\n\n  return(value?: any): Promise<IteratorResult<any>>;\n}\n\nexport class ReadableStreamAsyncIteratorImpl<R> {\n  private readonly _reader: ReadableStreamDefaultReader<R>;\n  private readonly _preventCancel: boolean;\n  private _ongoingPromise: Promise<ReadableStreamDefaultReadResult<R>> | undefined = undefined;\n  private _isFinished = false;\n\n  constructor(reader: ReadableStreamDefaultReader<R>, preventCancel: boolean) {\n    this._reader = reader;\n    this._preventCancel = preventCancel;\n  }\n\n  next(): Promise<ReadableStreamDefaultReadResult<R>> {\n    const nextSteps = () => this._nextSteps();\n    this._ongoingPromise = this._ongoingPromise ?\n      transformPromiseWith(this._ongoingPromise, nextSteps, nextSteps) :\n      nextSteps();\n    return this._ongoingPromise;\n  }\n\n  return(value: any): Promise<ReadableStreamDefaultReadResult<any>> {\n    const returnSteps = () => this._returnSteps(value);\n    return this._ongoingPromise ?\n      transformPromiseWith(this._ongoingPromise, returnSteps, returnSteps) :\n      returnSteps();\n  }\n\n  private _nextSteps(): Promise<ReadableStreamDefaultReadResult<R>> {\n    if (this._isFinished) {\n      return Promise.resolve({ value: undefined, done: true });\n    }\n\n    const reader = this._reader;\n    assert(reader._ownerReadableStream !== undefined);\n\n    let resolvePromise!: (result: ReadableStreamDefaultReadResult<R>) => void;\n    let rejectPromise!: (reason: any) => void;\n    const promise = newPromise<ReadableStreamDefaultReadResult<R>>((resolve, reject) => {\n      resolvePromise = resolve;\n      rejectPromise = reject;\n    });\n    const readRequest: ReadRequest<R> = {\n      _chunkSteps: chunk => {\n        this._ongoingPromise = undefined;\n        // This needs to be delayed by one microtask, otherwise we stop pulling too early which breaks a test.\n        // FIXME Is this a bug in the specification, or in the test?\n        queueMicrotask(() => resolvePromise({ value: chunk, done: false }));\n      },\n      _closeSteps: () => {\n        this._ongoingPromise = undefined;\n        this._isFinished = true;\n        ReadableStreamReaderGenericRelease(reader);\n        resolvePromise({ value: undefined, done: true });\n      },\n      _errorSteps: reason => {\n        this._ongoingPromise = undefined;\n        this._isFinished = true;\n        ReadableStreamReaderGenericRelease(reader);\n        rejectPromise(reason);\n      }\n    };\n    ReadableStreamDefaultReaderRead(reader, readRequest);\n    return promise;\n  }\n\n  private _returnSteps(value: any): Promise<ReadableStreamDefaultReadResult<any>> {\n    if (this._isFinished) {\n      return Promise.resolve({ value, done: true });\n    }\n    this._isFinished = true;\n\n    const reader = this._reader;\n    assert(reader._ownerReadableStream !== undefined);\n    assert(reader._readRequests.length === 0);\n\n    if (!this._preventCancel) {\n      const result = ReadableStreamReaderGenericCancel(reader, value);\n      ReadableStreamReaderGenericRelease(reader);\n      return transformPromiseWith(result, () => ({ value, done: true }));\n    }\n\n    ReadableStreamReaderGenericRelease(reader);\n    return promiseResolvedWith({ value, done: true });\n  }\n}\n\ninterface ReadableStreamAsyncIteratorInstance<R> extends ReadableStreamAsyncIterator<R> {\n  /** @interal */\n  _asyncIteratorImpl: ReadableStreamAsyncIteratorImpl<R>;\n\n  next(): Promise<IteratorResult<R, undefined>>;\n\n  return(value?: any): Promise<IteratorResult<any>>;\n}\n\nconst ReadableStreamAsyncIteratorPrototype: ReadableStreamAsyncIteratorInstance<any> = {\n  next(this: ReadableStreamAsyncIteratorInstance<any>): Promise<ReadableStreamDefaultReadResult<any>> {\n    if (!IsReadableStreamAsyncIterator(this)) {\n      return promiseRejectedWith(streamAsyncIteratorBrandCheckException('next'));\n    }\n    return this._asyncIteratorImpl.next();\n  },\n\n  return(this: ReadableStreamAsyncIteratorInstance<any>, value: any): Promise<ReadableStreamDefaultReadResult<any>> {\n    if (!IsReadableStreamAsyncIterator(this)) {\n      return promiseRejectedWith(streamAsyncIteratorBrandCheckException('return'));\n    }\n    return this._asyncIteratorImpl.return(value);\n  }\n} as any;\nObject.setPrototypeOf(ReadableStreamAsyncIteratorPrototype, AsyncIteratorPrototype);\n\n// Abstract operations for the ReadableStream.\n\nexport function AcquireReadableStreamAsyncIterator<R>(stream: ReadableStream<R>,\n                                                      preventCancel: boolean): ReadableStreamAsyncIterator<R> {\n  const reader = AcquireReadableStreamDefaultReader<R>(stream);\n  const impl = new ReadableStreamAsyncIteratorImpl(reader, preventCancel);\n  const iterator: ReadableStreamAsyncIteratorInstance<R> = Object.create(ReadableStreamAsyncIteratorPrototype);\n  iterator._asyncIteratorImpl = impl;\n  return iterator;\n}\n\nfunction IsReadableStreamAsyncIterator<R = any>(x: any): x is ReadableStreamAsyncIterator<R> {\n  if (!typeIsObject(x)) {\n    return false;\n  }\n\n  if (!Object.prototype.hasOwnProperty.call(x, '_asyncIteratorImpl')) {\n    return false;\n  }\n\n  try {\n    // noinspection SuspiciousTypeOfGuard\n    return (x as ReadableStreamAsyncIteratorInstance<any>)._asyncIteratorImpl instanceof\n      ReadableStreamAsyncIteratorImpl;\n  } catch {\n    return false;\n  }\n}\n\n// Helper functions for the ReadableStream.\n\nfunction streamAsyncIteratorBrandCheckException(name: string): TypeError {\n  return new TypeError(`ReadableStreamAsyncIterator.${name} can only be used on a ReadableSteamAsyncIterator`);\n}\n", "/// <reference lib=\"es2015.core\" />\n\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number/isNaN#Polyfill\nconst NumberIsNaN: typeof Number.isNaN = Number.isNaN || function (x) {\n  // eslint-disable-next-line no-self-compare\n  return x !== x;\n};\n\nexport default NumberIsNaN;\n", "import NumberIsNaN from '../../stub/number-isnan';\nimport { ArrayBufferSlice } from './ecmascript';\nimport type { NonShared } from '../helpers/array-buffer-view';\n\nexport function IsNonNegativeNumber(v: number): boolean {\n  if (typeof v !== 'number') {\n    return false;\n  }\n\n  if (NumberIsNaN(v)) {\n    return false;\n  }\n\n  if (v < 0) {\n    return false;\n  }\n\n  return true;\n}\n\nexport function CloneAsUint8Array(O: NonShared<ArrayBufferView>): NonShared<Uint8Array> {\n  const buffer = ArrayBufferSlice(O.buffer, O.byteOffset, O.byteOffset + O.byteLength);\n  return new Uint8Array(buffer) as NonShared<Uint8Array>;\n}\n", "import assert from '../../stub/assert';\nimport { SimpleQueue } from '../simple-queue';\nimport { IsNonNegativeNumber } from './miscellaneous';\n\nexport interface QueueContainer<T> {\n  _queue: SimpleQueue<T>;\n  _queueTotalSize: number;\n}\n\nexport interface QueuePair<T> {\n  value: T;\n  size: number;\n}\n\nexport function DequeueValue<T>(container: QueueContainer<QueuePair<T>>): T {\n  assert('_queue' in container && '_queueTotalSize' in container);\n  assert(container._queue.length > 0);\n\n  const pair = container._queue.shift()!;\n  container._queueTotalSize -= pair.size;\n  if (container._queueTotalSize < 0) {\n    container._queueTotalSize = 0;\n  }\n\n  return pair.value;\n}\n\nexport function EnqueueValueWithSize<T>(container: QueueContainer<QueuePair<T>>, value: T, size: number) {\n  assert('_queue' in container && '_queueTotalSize' in container);\n\n  if (!IsNonNegativeNumber(size) || size === Infinity) {\n    throw new RangeError('Size must be a finite, non-NaN, non-negative number.');\n  }\n\n  container._queue.push({ value, size });\n  container._queueTotalSize += size;\n}\n\nexport function PeekQueueValue<T>(container: QueueContainer<QueuePair<T>>): T {\n  assert('_queue' in container && '_queueTotalSize' in container);\n  assert(container._queue.length > 0);\n\n  const pair = container._queue.peek();\n  return pair.value;\n}\n\nexport function ResetQueue<T>(container: QueueContainer<T>) {\n  assert('_queue' in container && '_queueTotalSize' in container);\n\n  container._queue = new SimpleQueue<T>();\n  container._queueTotalSize = 0;\n}\n", "export type TypedArray =\n  | Int8Array\n  | Uint8Array\n  | Uint8ClampedArray\n  | Int16Array\n  | Uint16Array\n  | Int32Array\n  | Uint32Array\n  | Float32Array\n  | Float64Array;\n\nexport type NonShared<T extends ArrayBufferView> = T & {\n  buffer: ArrayBuffer;\n}\n\nexport interface ArrayBufferViewConstructor<T extends ArrayBufferView = ArrayBufferView> {\n  new(buffer: ArrayBuffer, byteOffset: number, length?: number): T;\n\n  readonly prototype: T;\n}\n\nexport interface TypedArrayConstructor<T extends TypedArray = TypedArray> extends ArrayBufferViewConstructor<T> {\n  readonly BYTES_PER_ELEMENT: number;\n}\n\nexport type DataViewConstructor = ArrayBufferViewConstructor<DataView>;\n\nfunction isDataViewConstructor(ctor: Function): ctor is DataViewConstructor {\n  return ctor === DataView;\n}\n\nexport function isDataView(view: ArrayBufferView): view is DataView {\n  return isDataViewConstructor(view.constructor);\n}\n\nexport function arrayBufferViewElementSize<T extends ArrayBufferView>(ctor: ArrayBufferViewConstructor<T>): number {\n  if (isDataViewConstructor(ctor)) {\n    return 1;\n  }\n  return (ctor as unknown as TypedArrayConstructor).BYTES_PER_ELEMENT;\n}\n", "import assert from '../../stub/assert';\nimport { SimpleQueue } from '../simple-queue';\nimport { ResetQueue } from '../abstract-ops/queue-with-sizes';\nimport {\n  IsReadableStreamDefaultReader,\n  ReadableStreamAddReadRequest,\n  ReadableStreamFulfillReadRequest,\n  ReadableStreamGetNumReadRequests,\n  ReadableStreamHasDefaultReader,\n  type ReadRequest\n} from './default-reader';\nimport {\n  ReadableStreamAddReadIntoRequest,\n  ReadableStreamFulfillReadIntoRequest,\n  ReadableStreamGetNumReadIntoRequests,\n  ReadableStreamHasBYOBReader,\n  type ReadIntoRequest\n} from './byob-reader';\nimport NumberIsInteger from '../../stub/number-isinteger';\nimport {\n  IsReadableStreamLocked,\n  type ReadableByteStream,\n  ReadableStreamClose,\n  ReadableStreamError\n} from '../readable-stream';\nimport type { ValidatedUnderlyingByteSource } from './underlying-source';\nimport { setFunctionName, typeIsObject } from '../helpers/miscellaneous';\nimport {\n  ArrayBufferSlice,\n  CanTransferArrayBuffer,\n  CopyDataBlockBytes,\n  IsDetachedBuffer,\n  TransferArrayBuffer\n} from '../abstract-ops/ecmascript';\nimport { CancelSteps, PullSteps, ReleaseSteps } from '../abstract-ops/internal-methods';\nimport { promiseResolvedWith, uponPromise } from '../helpers/webidl';\nimport { assertRequiredArgument, convertUnsignedLongLongWithEnforceRange } from '../validators/basic';\nimport {\n  type ArrayBufferViewConstructor,\n  arrayBufferViewElementSize,\n  type NonShared,\n  type TypedArrayConstructor\n} from '../helpers/array-buffer-view';\n\n/**\n * A pull-into request in a {@link ReadableByteStreamController}.\n *\n * @public\n */\nexport class ReadableStreamBYOBRequest {\n  /** @internal */\n  _associatedReadableByteStreamController!: ReadableByteStreamController;\n  /** @internal */\n  _view!: NonShared<ArrayBufferView> | null;\n\n  private constructor() {\n    throw new TypeError('Illegal constructor');\n  }\n\n  /**\n   * Returns the view for writing in to, or `null` if the BYOB request has already been responded to.\n   */\n  get view(): ArrayBufferView | null {\n    if (!IsReadableStreamBYOBRequest(this)) {\n      throw byobRequestBrandCheckException('view');\n    }\n\n    return this._view;\n  }\n\n  /**\n   * Indicates to the associated readable byte stream that `bytesWritten` bytes were written into\n   * {@link ReadableStreamBYOBRequest.view | view}, causing the result be surfaced to the consumer.\n   *\n   * After this method is called, {@link ReadableStreamBYOBRequest.view | view} will be transferred and no longer\n   * modifiable.\n   */\n  respond(bytesWritten: number): void;\n  respond(bytesWritten: number | undefined): void {\n    if (!IsReadableStreamBYOBRequest(this)) {\n      throw byobRequestBrandCheckException('respond');\n    }\n    assertRequiredArgument(bytesWritten, 1, 'respond');\n    bytesWritten = convertUnsignedLongLongWithEnforceRange(bytesWritten, 'First parameter');\n\n    if (this._associatedReadableByteStreamController === undefined) {\n      throw new TypeError('This BYOB request has been invalidated');\n    }\n\n    if (IsDetachedBuffer(this._view!.buffer)) {\n      throw new TypeError(`The BYOB request's buffer has been detached and so cannot be used as a response`);\n    }\n\n    assert(this._view!.byteLength > 0);\n    assert(this._view!.buffer.byteLength > 0);\n\n    ReadableByteStreamControllerRespond(this._associatedReadableByteStreamController, bytesWritten);\n  }\n\n  /**\n   * Indicates to the associated readable byte stream that instead of writing into\n   * {@link ReadableStreamBYOBRequest.view | view}, the underlying byte source is providing a new `ArrayBufferView`,\n   * which will be given to the consumer of the readable byte stream.\n   *\n   * After this method is called, `view` will be transferred and no longer modifiable.\n   */\n  respondWithNewView(view: ArrayBufferView): void;\n  respondWithNewView(view: NonShared<ArrayBufferView>): void {\n    if (!IsReadableStreamBYOBRequest(this)) {\n      throw byobRequestBrandCheckException('respondWithNewView');\n    }\n    assertRequiredArgument(view, 1, 'respondWithNewView');\n\n    if (!ArrayBuffer.isView(view)) {\n      throw new TypeError('You can only respond with array buffer views');\n    }\n\n    if (this._associatedReadableByteStreamController === undefined) {\n      throw new TypeError('This BYOB request has been invalidated');\n    }\n\n    if (IsDetachedBuffer(view.buffer)) {\n      throw new TypeError('The given view\\'s buffer has been detached and so cannot be used as a response');\n    }\n\n    ReadableByteStreamControllerRespondWithNewView(this._associatedReadableByteStreamController, view);\n  }\n}\n\nObject.defineProperties(ReadableStreamBYOBRequest.prototype, {\n  respond: { enumerable: true },\n  respondWithNewView: { enumerable: true },\n  view: { enumerable: true }\n});\nsetFunctionName(ReadableStreamBYOBRequest.prototype.respond, 'respond');\nsetFunctionName(ReadableStreamBYOBRequest.prototype.respondWithNewView, 'respondWithNewView');\nif (typeof Symbol.toStringTag === 'symbol') {\n  Object.defineProperty(ReadableStreamBYOBRequest.prototype, Symbol.toStringTag, {\n    value: 'ReadableStreamBYOBRequest',\n    configurable: true\n  });\n}\n\ninterface ByteQueueElement {\n  buffer: ArrayBuffer;\n  byteOffset: number;\n  byteLength: number;\n}\n\ntype PullIntoDescriptor<T extends NonShared<ArrayBufferView> = NonShared<ArrayBufferView>> =\n  DefaultPullIntoDescriptor\n  | BYOBPullIntoDescriptor<T>;\n\ninterface DefaultPullIntoDescriptor {\n  buffer: ArrayBuffer;\n  bufferByteLength: number;\n  byteOffset: number;\n  byteLength: number;\n  bytesFilled: number;\n  minimumFill: number;\n  elementSize: number;\n  viewConstructor: TypedArrayConstructor<Uint8Array>;\n  readerType: 'default' | 'none';\n}\n\ninterface BYOBPullIntoDescriptor<T extends NonShared<ArrayBufferView> = NonShared<ArrayBufferView>> {\n  buffer: ArrayBuffer;\n  bufferByteLength: number;\n  byteOffset: number;\n  byteLength: number;\n  bytesFilled: number;\n  minimumFill: number;\n  elementSize: number;\n  viewConstructor: ArrayBufferViewConstructor<T>;\n  readerType: 'byob' | 'none';\n}\n\n/**\n * Allows control of a {@link ReadableStream | readable byte stream}'s state and internal queue.\n *\n * @public\n */\nexport class ReadableByteStreamController {\n  /** @internal */\n  _controlledReadableByteStream!: ReadableByteStream;\n  /** @internal */\n  _queue!: SimpleQueue<ByteQueueElement>;\n  /** @internal */\n  _queueTotalSize!: number;\n  /** @internal */\n  _started!: boolean;\n  /** @internal */\n  _closeRequested!: boolean;\n  /** @internal */\n  _pullAgain!: boolean;\n  /** @internal */\n  _pulling !: boolean;\n  /** @internal */\n  _strategyHWM!: number;\n  /** @internal */\n  _pullAlgorithm!: () => Promise<void>;\n  /** @internal */\n  _cancelAlgorithm!: (reason: any) => Promise<void>;\n  /** @internal */\n  _autoAllocateChunkSize: number | undefined;\n  /** @internal */\n  _byobRequest: ReadableStreamBYOBRequest | null;\n  /** @internal */\n  _pendingPullIntos!: SimpleQueue<PullIntoDescriptor>;\n\n  private constructor() {\n    throw new TypeError('Illegal constructor');\n  }\n\n  /**\n   * Returns the current BYOB pull request, or `null` if there isn't one.\n   */\n  get byobRequest(): ReadableStreamBYOBRequest | null {\n    if (!IsReadableByteStreamController(this)) {\n      throw byteStreamControllerBrandCheckException('byobRequest');\n    }\n\n    return ReadableByteStreamControllerGetBYOBRequest(this);\n  }\n\n  /**\n   * Returns the desired size to fill the controlled stream's internal queue. It can be negative, if the queue is\n   * over-full. An underlying byte source ought to use this information to determine when and how to apply backpressure.\n   */\n  get desiredSize(): number | null {\n    if (!IsReadableByteStreamController(this)) {\n      throw byteStreamControllerBrandCheckException('desiredSize');\n    }\n\n    return ReadableByteStreamControllerGetDesiredSize(this);\n  }\n\n  /**\n   * Closes the controlled readable stream. Consumers will still be able to read any previously-enqueued chunks from\n   * the stream, but once those are read, the stream will become closed.\n   */\n  close(): void {\n    if (!IsReadableByteStreamController(this)) {\n      throw byteStreamControllerBrandCheckException('close');\n    }\n\n    if (this._closeRequested) {\n      throw new TypeError('The stream has already been closed; do not close it again!');\n    }\n\n    const state = this._controlledReadableByteStream._state;\n    if (state !== 'readable') {\n      throw new TypeError(`The stream (in ${state} state) is not in the readable state and cannot be closed`);\n    }\n\n    ReadableByteStreamControllerClose(this);\n  }\n\n  /**\n   * Enqueues the given chunk chunk in the controlled readable stream.\n   * The chunk has to be an `ArrayBufferView` instance, or else a `TypeError` will be thrown.\n   */\n  enqueue(chunk: ArrayBufferView): void;\n  enqueue(chunk: NonShared<ArrayBufferView>): void {\n    if (!IsReadableByteStreamController(this)) {\n      throw byteStreamControllerBrandCheckException('enqueue');\n    }\n\n    assertRequiredArgument(chunk, 1, 'enqueue');\n    if (!ArrayBuffer.isView(chunk)) {\n      throw new TypeError('chunk must be an array buffer view');\n    }\n    if (chunk.byteLength === 0) {\n      throw new TypeError('chunk must have non-zero byteLength');\n    }\n    if (chunk.buffer.byteLength === 0) {\n      throw new TypeError(`chunk's buffer must have non-zero byteLength`);\n    }\n\n    if (this._closeRequested) {\n      throw new TypeError('stream is closed or draining');\n    }\n\n    const state = this._controlledReadableByteStream._state;\n    if (state !== 'readable') {\n      throw new TypeError(`The stream (in ${state} state) is not in the readable state and cannot be enqueued to`);\n    }\n\n    ReadableByteStreamControllerEnqueue(this, chunk);\n  }\n\n  /**\n   * Errors the controlled readable stream, making all future interactions with it fail with the given error `e`.\n   */\n  error(e: any = undefined): void {\n    if (!IsReadableByteStreamController(this)) {\n      throw byteStreamControllerBrandCheckException('error');\n    }\n\n    ReadableByteStreamControllerError(this, e);\n  }\n\n  /** @internal */\n  [CancelSteps](reason: any): Promise<void> {\n    ReadableByteStreamControllerClearPendingPullIntos(this);\n\n    ResetQueue(this);\n\n    const result = this._cancelAlgorithm(reason);\n    ReadableByteStreamControllerClearAlgorithms(this);\n    return result;\n  }\n\n  /** @internal */\n  [PullSteps](readRequest: ReadRequest<NonShared<Uint8Array>>): void {\n    const stream = this._controlledReadableByteStream;\n    assert(ReadableStreamHasDefaultReader(stream));\n\n    if (this._queueTotalSize > 0) {\n      assert(ReadableStreamGetNumReadRequests(stream) === 0);\n\n      ReadableByteStreamControllerFillReadRequestFromQueue(this, readRequest);\n      return;\n    }\n\n    const autoAllocateChunkSize = this._autoAllocateChunkSize;\n    if (autoAllocateChunkSize !== undefined) {\n      let buffer: ArrayBuffer;\n      try {\n        buffer = new ArrayBuffer(autoAllocateChunkSize);\n      } catch (bufferE) {\n        readRequest._errorSteps(bufferE);\n        return;\n      }\n\n      const pullIntoDescriptor: DefaultPullIntoDescriptor = {\n        buffer,\n        bufferByteLength: autoAllocateChunkSize,\n        byteOffset: 0,\n        byteLength: autoAllocateChunkSize,\n        bytesFilled: 0,\n        minimumFill: 1,\n        elementSize: 1,\n        viewConstructor: Uint8Array,\n        readerType: 'default'\n      };\n\n      this._pendingPullIntos.push(pullIntoDescriptor);\n    }\n\n    ReadableStreamAddReadRequest(stream, readRequest);\n    ReadableByteStreamControllerCallPullIfNeeded(this);\n  }\n\n  /** @internal */\n  [ReleaseSteps](): void {\n    if (this._pendingPullIntos.length > 0) {\n      const firstPullInto = this._pendingPullIntos.peek();\n      firstPullInto.readerType = 'none';\n\n      this._pendingPullIntos = new SimpleQueue();\n      this._pendingPullIntos.push(firstPullInto);\n    }\n  }\n}\n\nObject.defineProperties(ReadableByteStreamController.prototype, {\n  close: { enumerable: true },\n  enqueue: { enumerable: true },\n  error: { enumerable: true },\n  byobRequest: { enumerable: true },\n  desiredSize: { enumerable: true }\n});\nsetFunctionName(ReadableByteStreamController.prototype.close, 'close');\nsetFunctionName(ReadableByteStreamController.prototype.enqueue, 'enqueue');\nsetFunctionName(ReadableByteStreamController.prototype.error, 'error');\nif (typeof Symbol.toStringTag === 'symbol') {\n  Object.defineProperty(ReadableByteStreamController.prototype, Symbol.toStringTag, {\n    value: 'ReadableByteStreamController',\n    configurable: true\n  });\n}\n\n// Abstract operations for the ReadableByteStreamController.\n\nexport function IsReadableByteStreamController(x: any): x is ReadableByteStreamController {\n  if (!typeIsObject(x)) {\n    return false;\n  }\n\n  if (!Object.prototype.hasOwnProperty.call(x, '_controlledReadableByteStream')) {\n    return false;\n  }\n\n  return x instanceof ReadableByteStreamController;\n}\n\nfunction IsReadableStreamBYOBRequest(x: any): x is ReadableStreamBYOBRequest {\n  if (!typeIsObject(x)) {\n    return false;\n  }\n\n  if (!Object.prototype.hasOwnProperty.call(x, '_associatedReadableByteStreamController')) {\n    return false;\n  }\n\n  return x instanceof ReadableStreamBYOBRequest;\n}\n\nfunction ReadableByteStreamControllerCallPullIfNeeded(controller: ReadableByteStreamController): void {\n  const shouldPull = ReadableByteStreamControllerShouldCallPull(controller);\n  if (!shouldPull) {\n    return;\n  }\n\n  if (controller._pulling) {\n    controller._pullAgain = true;\n    return;\n  }\n\n  assert(!controller._pullAgain);\n\n  controller._pulling = true;\n\n  // TODO: Test controller argument\n  const pullPromise = controller._pullAlgorithm();\n  uponPromise(\n    pullPromise,\n    () => {\n      controller._pulling = false;\n\n      if (controller._pullAgain) {\n        controller._pullAgain = false;\n        ReadableByteStreamControllerCallPullIfNeeded(controller);\n      }\n\n      return null;\n    },\n    e => {\n      ReadableByteStreamControllerError(controller, e);\n      return null;\n    }\n  );\n}\n\nfunction ReadableByteStreamControllerClearPendingPullIntos(controller: ReadableByteStreamController) {\n  ReadableByteStreamControllerInvalidateBYOBRequest(controller);\n  controller._pendingPullIntos = new SimpleQueue();\n}\n\nfunction ReadableByteStreamControllerCommitPullIntoDescriptor<T extends NonShared<ArrayBufferView>>(\n  stream: ReadableByteStream,\n  pullIntoDescriptor: PullIntoDescriptor<T>\n) {\n  assert(stream._state !== 'errored');\n  assert(pullIntoDescriptor.readerType !== 'none');\n\n  let done = false;\n  if (stream._state === 'closed') {\n    assert(pullIntoDescriptor.bytesFilled % pullIntoDescriptor.elementSize === 0);\n    done = true;\n  }\n\n  const filledView = ReadableByteStreamControllerConvertPullIntoDescriptor<T>(pullIntoDescriptor);\n  if (pullIntoDescriptor.readerType === 'default') {\n    ReadableStreamFulfillReadRequest(stream, filledView as unknown as NonShared<Uint8Array>, done);\n  } else {\n    assert(pullIntoDescriptor.readerType === 'byob');\n    ReadableStreamFulfillReadIntoRequest(stream, filledView, done);\n  }\n}\n\nfunction ReadableByteStreamControllerConvertPullIntoDescriptor<T extends NonShared<ArrayBufferView>>(\n  pullIntoDescriptor: PullIntoDescriptor<T>\n): T {\n  const bytesFilled = pullIntoDescriptor.bytesFilled;\n  const elementSize = pullIntoDescriptor.elementSize;\n\n  assert(bytesFilled <= pullIntoDescriptor.byteLength);\n  assert(bytesFilled % elementSize === 0);\n\n  return new pullIntoDescriptor.viewConstructor(\n    pullIntoDescriptor.buffer, pullIntoDescriptor.byteOffset, bytesFilled / elementSize) as T;\n}\n\nfunction ReadableByteStreamControllerEnqueueChunkToQueue(controller: ReadableByteStreamController,\n                                                         buffer: ArrayBuffer,\n                                                         byteOffset: number,\n                                                         byteLength: number) {\n  controller._queue.push({ buffer, byteOffset, byteLength });\n  controller._queueTotalSize += byteLength;\n}\n\nfunction ReadableByteStreamControllerEnqueueClonedChunkToQueue(controller: ReadableByteStreamController,\n                                                               buffer: ArrayBuffer,\n                                                               byteOffset: number,\n                                                               byteLength: number) {\n  let clonedChunk;\n  try {\n    clonedChunk = ArrayBufferSlice(buffer, byteOffset, byteOffset + byteLength);\n  } catch (cloneE) {\n    ReadableByteStreamControllerError(controller, cloneE);\n    throw cloneE;\n  }\n  ReadableByteStreamControllerEnqueueChunkToQueue(controller, clonedChunk, 0, byteLength);\n}\n\nfunction ReadableByteStreamControllerEnqueueDetachedPullIntoToQueue(controller: ReadableByteStreamController,\n                                                                    firstDescriptor: PullIntoDescriptor) {\n  assert(firstDescriptor.readerType === 'none');\n  if (firstDescriptor.bytesFilled > 0) {\n    ReadableByteStreamControllerEnqueueClonedChunkToQueue(\n      controller,\n      firstDescriptor.buffer,\n      firstDescriptor.byteOffset,\n      firstDescriptor.bytesFilled\n    );\n  }\n  ReadableByteStreamControllerShiftPendingPullInto(controller);\n}\n\nfunction ReadableByteStreamControllerFillPullIntoDescriptorFromQueue(controller: ReadableByteStreamController,\n                                                                     pullIntoDescriptor: PullIntoDescriptor) {\n  const maxBytesToCopy = Math.min(controller._queueTotalSize,\n                                  pullIntoDescriptor.byteLength - pullIntoDescriptor.bytesFilled);\n  const maxBytesFilled = pullIntoDescriptor.bytesFilled + maxBytesToCopy;\n\n  let totalBytesToCopyRemaining = maxBytesToCopy;\n  let ready = false;\n  assert(pullIntoDescriptor.bytesFilled < pullIntoDescriptor.minimumFill);\n  const remainderBytes = maxBytesFilled % pullIntoDescriptor.elementSize;\n  const maxAlignedBytes = maxBytesFilled - remainderBytes;\n  // A descriptor for a read() request that is not yet filled up to its minimum length will stay at the head\n  // of the queue, so the underlying source can keep filling it.\n  if (maxAlignedBytes >= pullIntoDescriptor.minimumFill) {\n    totalBytesToCopyRemaining = maxAlignedBytes - pullIntoDescriptor.bytesFilled;\n    ready = true;\n  }\n\n  const queue = controller._queue;\n\n  while (totalBytesToCopyRemaining > 0) {\n    const headOfQueue = queue.peek();\n\n    const bytesToCopy = Math.min(totalBytesToCopyRemaining, headOfQueue.byteLength);\n\n    const destStart = pullIntoDescriptor.byteOffset + pullIntoDescriptor.bytesFilled;\n    CopyDataBlockBytes(pullIntoDescriptor.buffer, destStart, headOfQueue.buffer, headOfQueue.byteOffset, bytesToCopy);\n\n    if (headOfQueue.byteLength === bytesToCopy) {\n      queue.shift();\n    } else {\n      headOfQueue.byteOffset += bytesToCopy;\n      headOfQueue.byteLength -= bytesToCopy;\n    }\n    controller._queueTotalSize -= bytesToCopy;\n\n    ReadableByteStreamControllerFillHeadPullIntoDescriptor(controller, bytesToCopy, pullIntoDescriptor);\n\n    totalBytesToCopyRemaining -= bytesToCopy;\n  }\n\n  if (!ready) {\n    assert(controller._queueTotalSize === 0);\n    assert(pullIntoDescriptor.bytesFilled > 0);\n    assert(pullIntoDescriptor.bytesFilled < pullIntoDescriptor.minimumFill);\n  }\n\n  return ready;\n}\n\nfunction ReadableByteStreamControllerFillHeadPullIntoDescriptor(controller: ReadableByteStreamController,\n                                                                size: number,\n                                                                pullIntoDescriptor: PullIntoDescriptor) {\n  assert(controller._pendingPullIntos.length === 0 || controller._pendingPullIntos.peek() === pullIntoDescriptor);\n  assert(controller._byobRequest === null);\n  pullIntoDescriptor.bytesFilled += size;\n}\n\nfunction ReadableByteStreamControllerHandleQueueDrain(controller: ReadableByteStreamController) {\n  assert(controller._controlledReadableByteStream._state === 'readable');\n\n  if (controller._queueTotalSize === 0 && controller._closeRequested) {\n    ReadableByteStreamControllerClearAlgorithms(controller);\n    ReadableStreamClose(controller._controlledReadableByteStream);\n  } else {\n    ReadableByteStreamControllerCallPullIfNeeded(controller);\n  }\n}\n\nfunction ReadableByteStreamControllerInvalidateBYOBRequest(controller: ReadableByteStreamController) {\n  if (controller._byobRequest === null) {\n    return;\n  }\n\n  controller._byobRequest._associatedReadableByteStreamController = undefined!;\n  controller._byobRequest._view = null!;\n  controller._byobRequest = null;\n}\n\nfunction ReadableByteStreamControllerProcessPullIntoDescriptorsUsingQueue(controller: ReadableByteStreamController) {\n  assert(!controller._closeRequested);\n\n  while (controller._pendingPullIntos.length > 0) {\n    if (controller._queueTotalSize === 0) {\n      return;\n    }\n\n    const pullIntoDescriptor = controller._pendingPullIntos.peek();\n    assert(pullIntoDescriptor.readerType !== 'none');\n\n    if (ReadableByteStreamControllerFillPullIntoDescriptorFromQueue(controller, pullIntoDescriptor)) {\n      ReadableByteStreamControllerShiftPendingPullInto(controller);\n\n      ReadableByteStreamControllerCommitPullIntoDescriptor(\n        controller._controlledReadableByteStream,\n        pullIntoDescriptor\n      );\n    }\n  }\n}\n\nfunction ReadableByteStreamControllerProcessReadRequestsUsingQueue(controller: ReadableByteStreamController) {\n  const reader = controller._controlledReadableByteStream._reader;\n  assert(IsReadableStreamDefaultReader(reader));\n  while (reader._readRequests.length > 0) {\n    if (controller._queueTotalSize === 0) {\n      return;\n    }\n    const readRequest = reader._readRequests.shift();\n    ReadableByteStreamControllerFillReadRequestFromQueue(controller, readRequest);\n  }\n}\n\nexport function ReadableByteStreamControllerPullInto<T extends NonShared<ArrayBufferView>>(\n  controller: ReadableByteStreamController,\n  view: T,\n  min: number,\n  readIntoRequest: ReadIntoRequest<T>\n): void {\n  const stream = controller._controlledReadableByteStream;\n\n  const ctor = view.constructor as ArrayBufferViewConstructor<T>;\n  const elementSize = arrayBufferViewElementSize(ctor);\n\n  const { byteOffset, byteLength } = view;\n\n  const minimumFill = min * elementSize;\n  assert(minimumFill >= elementSize && minimumFill <= byteLength);\n  assert(minimumFill % elementSize === 0);\n\n  let buffer: ArrayBuffer;\n  try {\n    buffer = TransferArrayBuffer(view.buffer);\n  } catch (e) {\n    readIntoRequest._errorSteps(e);\n    return;\n  }\n\n  const pullIntoDescriptor: BYOBPullIntoDescriptor<T> = {\n    buffer,\n    bufferByteLength: buffer.byteLength,\n    byteOffset,\n    byteLength,\n    bytesFilled: 0,\n    minimumFill,\n    elementSize,\n    viewConstructor: ctor,\n    readerType: 'byob'\n  };\n\n  if (controller._pendingPullIntos.length > 0) {\n    controller._pendingPullIntos.push(pullIntoDescriptor);\n\n    // No ReadableByteStreamControllerCallPullIfNeeded() call since:\n    // - No change happens on desiredSize\n    // - The source has already been notified of that there's at least 1 pending read(view)\n\n    ReadableStreamAddReadIntoRequest(stream, readIntoRequest);\n    return;\n  }\n\n  if (stream._state === 'closed') {\n    const emptyView = new ctor(pullIntoDescriptor.buffer, pullIntoDescriptor.byteOffset, 0);\n    readIntoRequest._closeSteps(emptyView);\n    return;\n  }\n\n  if (controller._queueTotalSize > 0) {\n    if (ReadableByteStreamControllerFillPullIntoDescriptorFromQueue(controller, pullIntoDescriptor)) {\n      const filledView = ReadableByteStreamControllerConvertPullIntoDescriptor<T>(pullIntoDescriptor);\n\n      ReadableByteStreamControllerHandleQueueDrain(controller);\n\n      readIntoRequest._chunkSteps(filledView);\n      return;\n    }\n\n    if (controller._closeRequested) {\n      const e = new TypeError('Insufficient bytes to fill elements in the given buffer');\n      ReadableByteStreamControllerError(controller, e);\n\n      readIntoRequest._errorSteps(e);\n      return;\n    }\n  }\n\n  controller._pendingPullIntos.push(pullIntoDescriptor);\n\n  ReadableStreamAddReadIntoRequest<T>(stream, readIntoRequest);\n  ReadableByteStreamControllerCallPullIfNeeded(controller);\n}\n\nfunction ReadableByteStreamControllerRespondInClosedState(controller: ReadableByteStreamController,\n                                                          firstDescriptor: PullIntoDescriptor) {\n  assert(firstDescriptor.bytesFilled % firstDescriptor.elementSize === 0);\n\n  if (firstDescriptor.readerType === 'none') {\n    ReadableByteStreamControllerShiftPendingPullInto(controller);\n  }\n\n  const stream = controller._controlledReadableByteStream;\n  if (ReadableStreamHasBYOBReader(stream)) {\n    while (ReadableStreamGetNumReadIntoRequests(stream) > 0) {\n      const pullIntoDescriptor = ReadableByteStreamControllerShiftPendingPullInto(controller);\n      ReadableByteStreamControllerCommitPullIntoDescriptor(stream, pullIntoDescriptor);\n    }\n  }\n}\n\nfunction ReadableByteStreamControllerRespondInReadableState(controller: ReadableByteStreamController,\n                                                            bytesWritten: number,\n                                                            pullIntoDescriptor: PullIntoDescriptor) {\n  assert(pullIntoDescriptor.bytesFilled + bytesWritten <= pullIntoDescriptor.byteLength);\n\n  ReadableByteStreamControllerFillHeadPullIntoDescriptor(controller, bytesWritten, pullIntoDescriptor);\n\n  if (pullIntoDescriptor.readerType === 'none') {\n    ReadableByteStreamControllerEnqueueDetachedPullIntoToQueue(controller, pullIntoDescriptor);\n    ReadableByteStreamControllerProcessPullIntoDescriptorsUsingQueue(controller);\n    return;\n  }\n\n  if (pullIntoDescriptor.bytesFilled < pullIntoDescriptor.minimumFill) {\n    // A descriptor for a read() request that is not yet filled up to its minimum length will stay at the head\n    // of the queue, so the underlying source can keep filling it.\n    return;\n  }\n\n  ReadableByteStreamControllerShiftPendingPullInto(controller);\n\n  const remainderSize = pullIntoDescriptor.bytesFilled % pullIntoDescriptor.elementSize;\n  if (remainderSize > 0) {\n    const end = pullIntoDescriptor.byteOffset + pullIntoDescriptor.bytesFilled;\n    ReadableByteStreamControllerEnqueueClonedChunkToQueue(\n      controller,\n      pullIntoDescriptor.buffer,\n      end - remainderSize,\n      remainderSize\n    );\n  }\n\n  pullIntoDescriptor.bytesFilled -= remainderSize;\n  ReadableByteStreamControllerCommitPullIntoDescriptor(controller._controlledReadableByteStream, pullIntoDescriptor);\n\n  ReadableByteStreamControllerProcessPullIntoDescriptorsUsingQueue(controller);\n}\n\nfunction ReadableByteStreamControllerRespondInternal(controller: ReadableByteStreamController, bytesWritten: number) {\n  const firstDescriptor = controller._pendingPullIntos.peek();\n  assert(CanTransferArrayBuffer(firstDescriptor.buffer));\n\n  ReadableByteStreamControllerInvalidateBYOBRequest(controller);\n\n  const state = controller._controlledReadableByteStream._state;\n  if (state === 'closed') {\n    assert(bytesWritten === 0);\n    ReadableByteStreamControllerRespondInClosedState(controller, firstDescriptor);\n  } else {\n    assert(state === 'readable');\n    assert(bytesWritten > 0);\n    ReadableByteStreamControllerRespondInReadableState(controller, bytesWritten, firstDescriptor);\n  }\n\n  ReadableByteStreamControllerCallPullIfNeeded(controller);\n}\n\nfunction ReadableByteStreamControllerShiftPendingPullInto(\n  controller: ReadableByteStreamController\n): PullIntoDescriptor {\n  assert(controller._byobRequest === null);\n  const descriptor = controller._pendingPullIntos.shift()!;\n  return descriptor;\n}\n\nfunction ReadableByteStreamControllerShouldCallPull(controller: ReadableByteStreamController): boolean {\n  const stream = controller._controlledReadableByteStream;\n\n  if (stream._state !== 'readable') {\n    return false;\n  }\n\n  if (controller._closeRequested) {\n    return false;\n  }\n\n  if (!controller._started) {\n    return false;\n  }\n\n  if (ReadableStreamHasDefaultReader(stream) && ReadableStreamGetNumReadRequests(stream) > 0) {\n    return true;\n  }\n\n  if (ReadableStreamHasBYOBReader(stream) && ReadableStreamGetNumReadIntoRequests(stream) > 0) {\n    return true;\n  }\n\n  const desiredSize = ReadableByteStreamControllerGetDesiredSize(controller);\n  assert(desiredSize !== null);\n  if (desiredSize! > 0) {\n    return true;\n  }\n\n  return false;\n}\n\nfunction ReadableByteStreamControllerClearAlgorithms(controller: ReadableByteStreamController) {\n  controller._pullAlgorithm = undefined!;\n  controller._cancelAlgorithm = undefined!;\n}\n\n// A client of ReadableByteStreamController may use these functions directly to bypass state check.\n\nexport function ReadableByteStreamControllerClose(controller: ReadableByteStreamController) {\n  const stream = controller._controlledReadableByteStream;\n\n  if (controller._closeRequested || stream._state !== 'readable') {\n    return;\n  }\n\n  if (controller._queueTotalSize > 0) {\n    controller._closeRequested = true;\n\n    return;\n  }\n\n  if (controller._pendingPullIntos.length > 0) {\n    const firstPendingPullInto = controller._pendingPullIntos.peek();\n    if (firstPendingPullInto.bytesFilled % firstPendingPullInto.elementSize !== 0) {\n      const e = new TypeError('Insufficient bytes to fill elements in the given buffer');\n      ReadableByteStreamControllerError(controller, e);\n\n      throw e;\n    }\n  }\n\n  ReadableByteStreamControllerClearAlgorithms(controller);\n  ReadableStreamClose(stream);\n}\n\nexport function ReadableByteStreamControllerEnqueue(\n  controller: ReadableByteStreamController,\n  chunk: NonShared<ArrayBufferView>\n) {\n  const stream = controller._controlledReadableByteStream;\n\n  if (controller._closeRequested || stream._state !== 'readable') {\n    return;\n  }\n\n  const { buffer, byteOffset, byteLength } = chunk;\n  if (IsDetachedBuffer(buffer)) {\n    throw new TypeError('chunk\\'s buffer is detached and so cannot be enqueued');\n  }\n  const transferredBuffer = TransferArrayBuffer(buffer);\n\n  if (controller._pendingPullIntos.length > 0) {\n    const firstPendingPullInto = controller._pendingPullIntos.peek();\n    if (IsDetachedBuffer(firstPendingPullInto.buffer)) {\n      throw new TypeError(\n        'The BYOB request\\'s buffer has been detached and so cannot be filled with an enqueued chunk'\n      );\n    }\n    ReadableByteStreamControllerInvalidateBYOBRequest(controller);\n    firstPendingPullInto.buffer = TransferArrayBuffer(firstPendingPullInto.buffer);\n    if (firstPendingPullInto.readerType === 'none') {\n      ReadableByteStreamControllerEnqueueDetachedPullIntoToQueue(controller, firstPendingPullInto);\n    }\n  }\n\n  if (ReadableStreamHasDefaultReader(stream)) {\n    ReadableByteStreamControllerProcessReadRequestsUsingQueue(controller);\n    if (ReadableStreamGetNumReadRequests(stream) === 0) {\n      assert(controller._pendingPullIntos.length === 0);\n      ReadableByteStreamControllerEnqueueChunkToQueue(controller, transferredBuffer, byteOffset, byteLength);\n    } else {\n      assert(controller._queue.length === 0);\n      if (controller._pendingPullIntos.length > 0) {\n        assert(controller._pendingPullIntos.peek().readerType === 'default');\n        ReadableByteStreamControllerShiftPendingPullInto(controller);\n      }\n      const transferredView = new Uint8Array(transferredBuffer, byteOffset, byteLength);\n      ReadableStreamFulfillReadRequest(stream, transferredView as NonShared<Uint8Array>, false);\n    }\n  } else if (ReadableStreamHasBYOBReader(stream)) {\n    // TODO: Ideally in this branch detaching should happen only if the buffer is not consumed fully.\n    ReadableByteStreamControllerEnqueueChunkToQueue(controller, transferredBuffer, byteOffset, byteLength);\n    ReadableByteStreamControllerProcessPullIntoDescriptorsUsingQueue(controller);\n  } else {\n    assert(!IsReadableStreamLocked(stream));\n    ReadableByteStreamControllerEnqueueChunkToQueue(controller, transferredBuffer, byteOffset, byteLength);\n  }\n\n  ReadableByteStreamControllerCallPullIfNeeded(controller);\n}\n\nexport function ReadableByteStreamControllerError(controller: ReadableByteStreamController, e: any) {\n  const stream = controller._controlledReadableByteStream;\n\n  if (stream._state !== 'readable') {\n    return;\n  }\n\n  ReadableByteStreamControllerClearPendingPullIntos(controller);\n\n  ResetQueue(controller);\n  ReadableByteStreamControllerClearAlgorithms(controller);\n  ReadableStreamError(stream, e);\n}\n\nexport function ReadableByteStreamControllerFillReadRequestFromQueue(\n  controller: ReadableByteStreamController,\n  readRequest: ReadRequest<NonShared<Uint8Array>>\n) {\n  assert(controller._queueTotalSize > 0);\n\n  const entry = controller._queue.shift();\n  controller._queueTotalSize -= entry.byteLength;\n\n  ReadableByteStreamControllerHandleQueueDrain(controller);\n\n  const view = new Uint8Array(entry.buffer, entry.byteOffset, entry.byteLength);\n  readRequest._chunkSteps(view as NonShared<Uint8Array>);\n}\n\nexport function ReadableByteStreamControllerGetBYOBRequest(\n  controller: ReadableByteStreamController\n): ReadableStreamBYOBRequest | null {\n  if (controller._byobRequest === null && controller._pendingPullIntos.length > 0) {\n    const firstDescriptor = controller._pendingPullIntos.peek();\n    const view = new Uint8Array(firstDescriptor.buffer,\n                                firstDescriptor.byteOffset + firstDescriptor.bytesFilled,\n                                firstDescriptor.byteLength - firstDescriptor.bytesFilled);\n\n    const byobRequest: ReadableStreamBYOBRequest = Object.create(ReadableStreamBYOBRequest.prototype);\n    SetUpReadableStreamBYOBRequest(byobRequest, controller, view as NonShared<Uint8Array>);\n    controller._byobRequest = byobRequest;\n  }\n  return controller._byobRequest;\n}\n\nfunction ReadableByteStreamControllerGetDesiredSize(controller: ReadableByteStreamController): number | null {\n  const state = controller._controlledReadableByteStream._state;\n\n  if (state === 'errored') {\n    return null;\n  }\n  if (state === 'closed') {\n    return 0;\n  }\n\n  return controller._strategyHWM - controller._queueTotalSize;\n}\n\nexport function ReadableByteStreamControllerRespond(controller: ReadableByteStreamController, bytesWritten: number) {\n  assert(controller._pendingPullIntos.length > 0);\n\n  const firstDescriptor = controller._pendingPullIntos.peek();\n  const state = controller._controlledReadableByteStream._state;\n\n  if (state === 'closed') {\n    if (bytesWritten !== 0) {\n      throw new TypeError('bytesWritten must be 0 when calling respond() on a closed stream');\n    }\n  } else {\n    assert(state === 'readable');\n    if (bytesWritten === 0) {\n      throw new TypeError('bytesWritten must be greater than 0 when calling respond() on a readable stream');\n    }\n    if (firstDescriptor.bytesFilled + bytesWritten > firstDescriptor.byteLength) {\n      throw new RangeError('bytesWritten out of range');\n    }\n  }\n\n  firstDescriptor.buffer = TransferArrayBuffer(firstDescriptor.buffer);\n\n  ReadableByteStreamControllerRespondInternal(controller, bytesWritten);\n}\n\nexport function ReadableByteStreamControllerRespondWithNewView(controller: ReadableByteStreamController,\n                                                               view: NonShared<ArrayBufferView>) {\n  assert(controller._pendingPullIntos.length > 0);\n  assert(!IsDetachedBuffer(view.buffer));\n\n  const firstDescriptor = controller._pendingPullIntos.peek();\n  const state = controller._controlledReadableByteStream._state;\n\n  if (state === 'closed') {\n    if (view.byteLength !== 0) {\n      throw new TypeError('The view\\'s length must be 0 when calling respondWithNewView() on a closed stream');\n    }\n  } else {\n    assert(state === 'readable');\n    if (view.byteLength === 0) {\n      throw new TypeError(\n        'The view\\'s length must be greater than 0 when calling respondWithNewView() on a readable stream'\n      );\n    }\n  }\n\n  if (firstDescriptor.byteOffset + firstDescriptor.bytesFilled !== view.byteOffset) {\n    throw new RangeError('The region specified by view does not match byobRequest');\n  }\n  if (firstDescriptor.bufferByteLength !== view.buffer.byteLength) {\n    throw new RangeError('The buffer of view has different capacity than byobRequest');\n  }\n  if (firstDescriptor.bytesFilled + view.byteLength > firstDescriptor.byteLength) {\n    throw new RangeError('The region specified by view is larger than byobRequest');\n  }\n\n  const viewByteLength = view.byteLength;\n  firstDescriptor.buffer = TransferArrayBuffer(view.buffer);\n  ReadableByteStreamControllerRespondInternal(controller, viewByteLength);\n}\n\nexport function SetUpReadableByteStreamController(stream: ReadableByteStream,\n                                                  controller: ReadableByteStreamController,\n                                                  startAlgorithm: () => void | PromiseLike<void>,\n                                                  pullAlgorithm: () => Promise<void>,\n                                                  cancelAlgorithm: (reason: any) => Promise<void>,\n                                                  highWaterMark: number,\n                                                  autoAllocateChunkSize: number | undefined) {\n  assert(stream._readableStreamController === undefined);\n  if (autoAllocateChunkSize !== undefined) {\n    assert(NumberIsInteger(autoAllocateChunkSize));\n    assert(autoAllocateChunkSize > 0);\n  }\n\n  controller._controlledReadableByteStream = stream;\n\n  controller._pullAgain = false;\n  controller._pulling = false;\n\n  controller._byobRequest = null;\n\n  // Need to set the slots so that the assert doesn't fire. In the spec the slots already exist implicitly.\n  controller._queue = controller._queueTotalSize = undefined!;\n  ResetQueue(controller);\n\n  controller._closeRequested = false;\n  controller._started = false;\n\n  controller._strategyHWM = highWaterMark;\n\n  controller._pullAlgorithm = pullAlgorithm;\n  controller._cancelAlgorithm = cancelAlgorithm;\n\n  controller._autoAllocateChunkSize = autoAllocateChunkSize;\n\n  controller._pendingPullIntos = new SimpleQueue();\n\n  stream._readableStreamController = controller;\n\n  const startResult = startAlgorithm();\n  uponPromise(\n    promiseResolvedWith(startResult),\n    () => {\n      controller._started = true;\n\n      assert(!controller._pulling);\n      assert(!controller._pullAgain);\n\n      ReadableByteStreamControllerCallPullIfNeeded(controller);\n      return null;\n    },\n    r => {\n      ReadableByteStreamControllerError(controller, r);\n      return null;\n    }\n  );\n}\n\nexport function SetUpReadableByteStreamControllerFromUnderlyingSource(\n  stream: ReadableByteStream,\n  underlyingByteSource: ValidatedUnderlyingByteSource,\n  highWaterMark: number\n) {\n  const controller: ReadableByteStreamController = Object.create(ReadableByteStreamController.prototype);\n\n  let startAlgorithm: () => void | PromiseLike<void>;\n  let pullAlgorithm: () => Promise<void>;\n  let cancelAlgorithm: (reason: any) => Promise<void>;\n\n  if (underlyingByteSource.start !== undefined) {\n    startAlgorithm = () => underlyingByteSource.start!(controller);\n  } else {\n    startAlgorithm = () => undefined;\n  }\n  if (underlyingByteSource.pull !== undefined) {\n    pullAlgorithm = () => underlyingByteSource.pull!(controller);\n  } else {\n    pullAlgorithm = () => promiseResolvedWith(undefined);\n  }\n  if (underlyingByteSource.cancel !== undefined) {\n    cancelAlgorithm = reason => underlyingByteSource.cancel!(reason);\n  } else {\n    cancelAlgorithm = () => promiseResolvedWith(undefined);\n  }\n\n  const autoAllocateChunkSize = underlyingByteSource.autoAllocateChunkSize;\n  if (autoAllocateChunkSize === 0) {\n    throw new TypeError('autoAllocateChunkSize must be greater than 0');\n  }\n\n  SetUpReadableByteStreamController(\n    stream, controller, startAlgorithm, pullAlgorithm, cancelAlgorithm, highWaterMark, autoAllocateChunkSize\n  );\n}\n\nfunction SetUpReadableStreamBYOBRequest(request: ReadableStreamBYOBRequest,\n                                        controller: ReadableByteStreamController,\n                                        view: NonShared<ArrayBufferView>) {\n  assert(IsReadableByteStreamController(controller));\n  assert(typeof view === 'object');\n  assert(ArrayBuffer.isView(view));\n  assert(!IsDetachedBuffer(view.buffer));\n  request._associatedReadableByteStreamController = controller;\n  request._view = view;\n}\n\n// Helper functions for the ReadableStreamBYOBRequest.\n\nfunction byobRequestBrandCheckException(name: string): TypeError {\n  return new TypeError(\n    `ReadableStreamBYOBRequest.prototype.${name} can only be used on a ReadableStreamBYOBRequest`);\n}\n\n// Helper functions for the ReadableByteStreamController.\n\nfunction byteStreamControllerBrandCheckException(name: string): TypeError {\n  return new TypeError(\n    `ReadableByteStreamController.prototype.${name} can only be used on a ReadableByteStreamController`);\n}\n", "import { assertDictionary, convertUnsignedLongLongWithEnforceRange } from './basic';\nimport type {\n  ReadableStreamBYOBReaderReadOptions,\n  ReadableStreamGetReaderOptions,\n  ValidatedReadableStreamBYOBReaderReadOptions\n} from '../readable-stream/reader-options';\n\nexport function convertReaderOptions(options: ReadableStreamGetReaderOptions | null | undefined,\n                                     context: string): ReadableStreamGetReaderOptions {\n  assertDictionary(options, context);\n  const mode = options?.mode;\n  return {\n    mode: mode === undefined ? undefined : convertReadableStreamReaderMode(mode, `${context} has member 'mode' that`)\n  };\n}\n\nfunction convertReadableStreamReaderMode(mode: string, context: string): 'byob' {\n  mode = `${mode}`;\n  if (mode !== 'byob') {\n    throw new TypeError(`${context} '${mode}' is not a valid enumeration value for ReadableStreamReaderMode`);\n  }\n  return mode;\n}\n\nexport function convertByobReadOptions(\n  options: ReadableStreamBYOBReaderReadOptions | null | undefined,\n  context: string\n): ValidatedReadableStreamBYOBReaderReadOptions {\n  assertDictionary(options, context);\n  const min = options?.min ?? 1;\n  return {\n    min: convertUnsignedLongLongWithEnforceRange(\n      min,\n      `${context} has member 'min' that`\n    )\n  };\n}\n", "import assert from '../../stub/assert';\nimport { SimpleQueue } from '../simple-queue';\nimport {\n  ReadableStreamReaderGenericCancel,\n  ReadableStreamReaderGenericInitialize,\n  ReadableStreamReaderGenericRelease,\n  readerLockException\n} from './generic-reader';\nimport { IsReadableStreamLocked, type ReadableByteStream, type ReadableStream } from '../readable-stream';\nimport {\n  IsReadableByteStreamController,\n  ReadableByteStreamController,\n  ReadableByteStreamControllerPullInto\n} from './byte-stream-controller';\nimport { setFunctionName, typeIsObject } from '../helpers/miscellaneous';\nimport { newPromise, promiseRejectedWith } from '../helpers/webidl';\nimport { assertRequiredArgument } from '../validators/basic';\nimport { assertReadableStream } from '../validators/readable-stream';\nimport { IsDetachedBuffer } from '../abstract-ops/ecmascript';\nimport type {\n  ReadableStreamBYOBReaderReadOptions,\n  ValidatedReadableStreamBYOBReaderReadOptions\n} from './reader-options';\nimport { convertByobReadOptions } from '../validators/reader-options';\nimport { isDataView, type NonShared, type TypedArray } from '../helpers/array-buffer-view';\n\n/**\n * A result returned by {@link ReadableStreamBYOBReader.read}.\n *\n * @public\n */\nexport type ReadableStreamBYOBReadResult<T extends ArrayBufferView> = {\n  done: false;\n  value: T;\n} | {\n  done: true;\n  value: T | undefined;\n};\n\n// Abstract operations for the ReadableStream.\n\nexport function AcquireReadableStreamBYOBReader(stream: ReadableByteStream): ReadableStreamBYOBReader {\n  return new ReadableStreamBYOBReader(stream as ReadableStream<Uint8Array>);\n}\n\n// ReadableStream API exposed for controllers.\n\nexport function ReadableStreamAddReadIntoRequest<T extends NonShared<ArrayBufferView>>(\n  stream: ReadableByteStream,\n  readIntoRequest: ReadIntoRequest<T>\n): void {\n  assert(IsReadableStreamBYOBReader(stream._reader));\n  assert(stream._state === 'readable' || stream._state === 'closed');\n\n  (stream._reader! as ReadableStreamBYOBReader)._readIntoRequests.push(readIntoRequest);\n}\n\nexport function ReadableStreamFulfillReadIntoRequest(stream: ReadableByteStream,\n                                                     chunk: ArrayBufferView,\n                                                     done: boolean) {\n  const reader = stream._reader as ReadableStreamBYOBReader;\n\n  assert(reader._readIntoRequests.length > 0);\n\n  const readIntoRequest = reader._readIntoRequests.shift()!;\n  if (done) {\n    readIntoRequest._closeSteps(chunk);\n  } else {\n    readIntoRequest._chunkSteps(chunk);\n  }\n}\n\nexport function ReadableStreamGetNumReadIntoRequests(stream: ReadableByteStream): number {\n  return (stream._reader as ReadableStreamBYOBReader)._readIntoRequests.length;\n}\n\nexport function ReadableStreamHasBYOBReader(stream: ReadableByteStream): boolean {\n  const reader = stream._reader;\n\n  if (reader === undefined) {\n    return false;\n  }\n\n  if (!IsReadableStreamBYOBReader(reader)) {\n    return false;\n  }\n\n  return true;\n}\n\n// Readers\n\nexport interface ReadIntoRequest<T extends NonShared<ArrayBufferView>> {\n  _chunkSteps(chunk: T): void;\n\n  _closeSteps(chunk: T | undefined): void;\n\n  _errorSteps(e: any): void;\n}\n\n/**\n * A BYOB reader vended by a {@link ReadableStream}.\n *\n * @public\n */\nexport class ReadableStreamBYOBReader {\n  /** @internal */\n  _ownerReadableStream!: ReadableByteStream;\n  /** @internal */\n  _closedPromise!: Promise<undefined>;\n  /** @internal */\n  _closedPromise_resolve?: (value?: undefined) => void;\n  /** @internal */\n  _closedPromise_reject?: (reason: any) => void;\n  /** @internal */\n  _readIntoRequests: SimpleQueue<ReadIntoRequest<any>>;\n\n  constructor(stream: ReadableStream<Uint8Array>) {\n    assertRequiredArgument(stream, 1, 'ReadableStreamBYOBReader');\n    assertReadableStream(stream, 'First parameter');\n\n    if (IsReadableStreamLocked(stream)) {\n      throw new TypeError('This stream has already been locked for exclusive reading by another reader');\n    }\n\n    if (!IsReadableByteStreamController(stream._readableStreamController)) {\n      throw new TypeError('Cannot construct a ReadableStreamBYOBReader for a stream not constructed with a byte ' +\n        'source');\n    }\n\n    ReadableStreamReaderGenericInitialize(this, stream);\n\n    this._readIntoRequests = new SimpleQueue();\n  }\n\n  /**\n   * Returns a promise that will be fulfilled when the stream becomes closed, or rejected if the stream ever errors or\n   * the reader's lock is released before the stream finishes closing.\n   */\n  get closed(): Promise<undefined> {\n    if (!IsReadableStreamBYOBReader(this)) {\n      return promiseRejectedWith(byobReaderBrandCheckException('closed'));\n    }\n\n    return this._closedPromise;\n  }\n\n  /**\n   * If the reader is active, behaves the same as {@link ReadableStream.cancel | stream.cancel(reason)}.\n   */\n  cancel(reason: any = undefined): Promise<void> {\n    if (!IsReadableStreamBYOBReader(this)) {\n      return promiseRejectedWith(byobReaderBrandCheckException('cancel'));\n    }\n\n    if (this._ownerReadableStream === undefined) {\n      return promiseRejectedWith(readerLockException('cancel'));\n    }\n\n    return ReadableStreamReaderGenericCancel(this, reason);\n  }\n\n  /**\n   * Attempts to reads bytes into view, and returns a promise resolved with the result.\n   *\n   * If reading a chunk causes the queue to become empty, more data will be pulled from the underlying source.\n   */\n  read<T extends ArrayBufferView>(\n    view: T,\n    options?: ReadableStreamBYOBReaderReadOptions\n  ): Promise<ReadableStreamBYOBReadResult<T>>;\n  read<T extends NonShared<ArrayBufferView>>(\n    view: T,\n    rawOptions: ReadableStreamBYOBReaderReadOptions | null | undefined = {}\n  ): Promise<ReadableStreamBYOBReadResult<T>> {\n    if (!IsReadableStreamBYOBReader(this)) {\n      return promiseRejectedWith(byobReaderBrandCheckException('read'));\n    }\n\n    if (!ArrayBuffer.isView(view)) {\n      return promiseRejectedWith(new TypeError('view must be an array buffer view'));\n    }\n    if (view.byteLength === 0) {\n      return promiseRejectedWith(new TypeError('view must have non-zero byteLength'));\n    }\n    if (view.buffer.byteLength === 0) {\n      return promiseRejectedWith(new TypeError(`view's buffer must have non-zero byteLength`));\n    }\n    if (IsDetachedBuffer(view.buffer)) {\n      return promiseRejectedWith(new TypeError('view\\'s buffer has been detached'));\n    }\n\n    let options: ValidatedReadableStreamBYOBReaderReadOptions;\n    try {\n      options = convertByobReadOptions(rawOptions, 'options');\n    } catch (e) {\n      return promiseRejectedWith(e);\n    }\n    const min = options.min;\n    if (min === 0) {\n      return promiseRejectedWith(new TypeError('options.min must be greater than 0'));\n    }\n    if (!isDataView(view)) {\n      if (min > (view as unknown as TypedArray).length) {\n        return promiseRejectedWith(new RangeError('options.min must be less than or equal to view\\'s length'));\n      }\n    } else if (min > view.byteLength) {\n      return promiseRejectedWith(new RangeError('options.min must be less than or equal to view\\'s byteLength'));\n    }\n\n    if (this._ownerReadableStream === undefined) {\n      return promiseRejectedWith(readerLockException('read from'));\n    }\n\n    let resolvePromise!: (result: ReadableStreamBYOBReadResult<T>) => void;\n    let rejectPromise!: (reason: any) => void;\n    const promise = newPromise<ReadableStreamBYOBReadResult<T>>((resolve, reject) => {\n      resolvePromise = resolve;\n      rejectPromise = reject;\n    });\n    const readIntoRequest: ReadIntoRequest<T> = {\n      _chunkSteps: chunk => resolvePromise({ value: chunk, done: false }),\n      _closeSteps: chunk => resolvePromise({ value: chunk, done: true }),\n      _errorSteps: e => rejectPromise(e)\n    };\n    ReadableStreamBYOBReaderRead(this, view, min, readIntoRequest);\n    return promise;\n  }\n\n  /**\n   * Releases the reader's lock on the corresponding stream. After the lock is released, the reader is no longer active.\n   * If the associated stream is errored when the lock is released, the reader will appear errored in the same way\n   * from now on; otherwise, the reader will appear closed.\n   *\n   * A reader's lock cannot be released while it still has a pending read request, i.e., if a promise returned by\n   * the reader's {@link ReadableStreamBYOBReader.read | read()} method has not yet been settled. Attempting to\n   * do so will throw a `TypeError` and leave the reader locked to the stream.\n   */\n  releaseLock(): void {\n    if (!IsReadableStreamBYOBReader(this)) {\n      throw byobReaderBrandCheckException('releaseLock');\n    }\n\n    if (this._ownerReadableStream === undefined) {\n      return;\n    }\n\n    ReadableStreamBYOBReaderRelease(this);\n  }\n}\n\nObject.defineProperties(ReadableStreamBYOBReader.prototype, {\n  cancel: { enumerable: true },\n  read: { enumerable: true },\n  releaseLock: { enumerable: true },\n  closed: { enumerable: true }\n});\nsetFunctionName(ReadableStreamBYOBReader.prototype.cancel, 'cancel');\nsetFunctionName(ReadableStreamBYOBReader.prototype.read, 'read');\nsetFunctionName(ReadableStreamBYOBReader.prototype.releaseLock, 'releaseLock');\nif (typeof Symbol.toStringTag === 'symbol') {\n  Object.defineProperty(ReadableStreamBYOBReader.prototype, Symbol.toStringTag, {\n    value: 'ReadableStreamBYOBReader',\n    configurable: true\n  });\n}\n\n// Abstract operations for the readers.\n\nexport function IsReadableStreamBYOBReader(x: any): x is ReadableStreamBYOBReader {\n  if (!typeIsObject(x)) {\n    return false;\n  }\n\n  if (!Object.prototype.hasOwnProperty.call(x, '_readIntoRequests')) {\n    return false;\n  }\n\n  return x instanceof ReadableStreamBYOBReader;\n}\n\nexport function ReadableStreamBYOBReaderRead<T extends NonShared<ArrayBufferView>>(\n  reader: ReadableStreamBYOBReader,\n  view: T,\n  min: number,\n  readIntoRequest: ReadIntoRequest<T>\n): void {\n  const stream = reader._ownerReadableStream;\n\n  assert(stream !== undefined);\n\n  stream._disturbed = true;\n\n  if (stream._state === 'errored') {\n    readIntoRequest._errorSteps(stream._storedError);\n  } else {\n    ReadableByteStreamControllerPullInto(\n      stream._readableStreamController as ReadableByteStreamController,\n      view,\n      min,\n      readIntoRequest\n    );\n  }\n}\n\nexport function ReadableStreamBYOBReaderRelease(reader: ReadableStreamBYOBReader) {\n  ReadableStreamReaderGenericRelease(reader);\n  const e = new TypeError('Reader was released');\n  ReadableStreamBYOBReaderErrorReadIntoRequests(reader, e);\n}\n\nexport function ReadableStreamBYOBReaderErrorReadIntoRequests(reader: ReadableStreamBYOBReader, e: any) {\n  const readIntoRequests = reader._readIntoRequests;\n  reader._readIntoRequests = new SimpleQueue();\n  readIntoRequests.forEach(readIntoRequest => {\n    readIntoRequest._errorSteps(e);\n  });\n}\n\n// Helper functions for the ReadableStreamBYOBReader.\n\nfunction byobReaderBrandCheckException(name: string): TypeError {\n  return new TypeError(\n    `ReadableStreamBYOBReader.prototype.${name} can only be used on a ReadableStreamBYOBReader`);\n}\n", "import type { QueuingStrategy, QueuingStrategySizeCallback } from '../queuing-strategy';\nimport NumberIsNaN from '../../stub/number-isnan';\n\nexport function ExtractHighWaterMark(strategy: QueuingStrategy, defaultHWM: number): number {\n  const { highWaterMark } = strategy;\n\n  if (highWaterMark === undefined) {\n    return defaultHWM;\n  }\n\n  if (NumberIsNaN(highWaterMark) || highWaterMark < 0) {\n    throw new RangeError('Invalid highWaterMark');\n  }\n\n  return highWaterMark;\n}\n\nexport function ExtractSizeAlgorithm<T>(strategy: QueuingStrategy<T>): QueuingStrategySizeCallback<T> {\n  const { size } = strategy;\n\n  if (!size) {\n    return () => 1;\n  }\n\n  return size;\n}\n", "import type { QueuingStrategy, QueuingStrategySizeCallback } from '../queuing-strategy';\nimport { assertDictionary, assertFunction, convertUnrestrictedDouble } from './basic';\n\nexport function convertQueuingStrategy<T>(init: QueuingStrategy<T> | null | undefined,\n                                          context: string): QueuingStrategy<T> {\n  assertDictionary(init, context);\n  const highWaterMark = init?.highWaterMark;\n  const size = init?.size;\n  return {\n    highWaterMark: highWaterMark === undefined ? undefined : convertUnrestrictedDouble(highWaterMark),\n    size: size === undefined ? undefined : convertQueuingStrategySize(size, `${context} has member 'size' that`)\n  };\n}\n\nfunction convertQueuingStrategySize<T>(fn: QueuingStrategySizeCallback<T>,\n                                       context: string): QueuingStrategySizeCallback<T> {\n  assertFunction(fn, context);\n  return chunk => convertUnrestrictedDouble(fn(chunk));\n}\n", "import { assertDictionary, assertFunction } from './basic';\nimport { promiseCall, reflectCall } from '../helpers/webidl';\nimport type {\n  UnderlyingSink,\n  UnderlyingSinkAbortCallback,\n  UnderlyingSinkCloseCallback,\n  UnderlyingSinkStartCallback,\n  UnderlyingSinkWriteCallback,\n  ValidatedUnderlyingSink\n} from '../writable-stream/underlying-sink';\nimport { WritableStreamDefaultController } from '../writable-stream';\n\nexport function convertUnderlyingSink<W>(original: UnderlyingSink<W> | null,\n                                         context: string): ValidatedUnderlyingSink<W> {\n  assertDictionary(original, context);\n  const abort = original?.abort;\n  const close = original?.close;\n  const start = original?.start;\n  const type = original?.type;\n  const write = original?.write;\n  return {\n    abort: abort === undefined ?\n      undefined :\n      convertUnderlyingSinkAbortCallback(abort, original!, `${context} has member 'abort' that`),\n    close: close === undefined ?\n      undefined :\n      convertUnderlyingSinkCloseCallback(close, original!, `${context} has member 'close' that`),\n    start: start === undefined ?\n      undefined :\n      convertUnderlyingSinkStartCallback(start, original!, `${context} has member 'start' that`),\n    write: write === undefined ?\n      undefined :\n      convertUnderlyingSinkWriteCallback(write, original!, `${context} has member 'write' that`),\n    type\n  };\n}\n\nfunction convertUnderlyingSinkAbortCallback(\n  fn: UnderlyingSinkAbortCallback,\n  original: UnderlyingSink,\n  context: string\n): (reason: any) => Promise<void> {\n  assertFunction(fn, context);\n  return (reason: any) => promiseCall(fn, original, [reason]);\n}\n\nfunction convertUnderlyingSinkCloseCallback(\n  fn: UnderlyingSinkCloseCallback,\n  original: UnderlyingSink,\n  context: string\n): () => Promise<void> {\n  assertFunction(fn, context);\n  return () => promiseCall(fn, original, []);\n}\n\nfunction convertUnderlyingSinkStartCallback(\n  fn: UnderlyingSinkStartCallback,\n  original: UnderlyingSink,\n  context: string\n): UnderlyingSinkStartCallback {\n  assertFunction(fn, context);\n  return (controller: WritableStreamDefaultController) => reflectCall(fn, original, [controller]);\n}\n\nfunction convertUnderlyingSinkWriteCallback<W>(\n  fn: UnderlyingSinkWriteCallback<W>,\n  original: UnderlyingSink<W>,\n  context: string\n): (chunk: W, controller: WritableStreamDefaultController) => Promise<void> {\n  assertFunction(fn, context);\n  return (chunk: W, controller: WritableStreamDefaultController) => promiseCall(fn, original, [chunk, controller]);\n}\n", "import { IsWritableStream, WritableStream } from '../writable-stream';\n\nexport function assertWritableStream(x: unknown, context: string): asserts x is WritableStream {\n  if (!IsWritableStream(x)) {\n    throw new TypeError(`${context} is not a WritableStream.`);\n  }\n}\n", "/**\n * A signal object that allows you to communicate with a request and abort it if required\n * via its associated `AbortController` object.\n *\n * @remarks\n *   This interface is compatible with the `AbortSignal` interface defined in TypeScript's DOM types.\n *   It is redefined here, so it can be polyfilled without a DOM, for example with\n *   {@link https://www.npmjs.com/package/abortcontroller-polyfill | abortcontroller-polyfill} in a Node environment.\n *\n * @public\n */\nexport interface AbortSignal {\n  /**\n   * Whether the request is aborted.\n   */\n  readonly aborted: boolean;\n\n  /**\n   * If aborted, returns the reason for aborting.\n   */\n  readonly reason?: any;\n\n  /**\n   * Add an event listener to be triggered when this signal becomes aborted.\n   */\n  addEventListener(type: 'abort', listener: () => void): void;\n\n  /**\n   * Remove an event listener that was previously added with {@link AbortSignal.addEventListener}.\n   */\n  removeEventListener(type: 'abort', listener: () => void): void;\n}\n\nexport function isAbortSignal(value: unknown): value is AbortSignal {\n  if (typeof value !== 'object' || value === null) {\n    return false;\n  }\n  try {\n    return typeof (value as AbortSignal).aborted === 'boolean';\n  } catch {\n    // AbortSignal.prototype.aborted throws if its brand check fails\n    return false;\n  }\n}\n\n/**\n * A controller object that allows you to abort an `AbortSignal` when desired.\n *\n * @remarks\n *   This interface is compatible with the `AbortController` interface defined in TypeScript's DOM types.\n *   It is redefined here, so it can be polyfilled without a DOM, for example with\n *   {@link https://www.npmjs.com/package/abortcontroller-polyfill | abortcontroller-polyfill} in a Node environment.\n *\n * @internal\n */\nexport interface AbortController {\n  readonly signal: AbortSignal;\n\n  abort(reason?: any): void;\n}\n\ninterface AbortControllerConstructor {\n  new(): AbortController;\n}\n\nconst supportsAbortController = typeof (AbortController as any) === 'function';\n\n/**\n * Construct a new AbortController, if supported by the platform.\n *\n * @internal\n */\nexport function createAbortController(): AbortController | undefined {\n  if (supportsAbortController) {\n    return new (AbortController as AbortControllerConstructor)();\n  }\n  return undefined;\n}\n", "import assert from '../stub/assert';\nimport {\n  newPromise,\n  promiseRejectedWith,\n  promiseResolvedWith,\n  setPromiseIsHandledToTrue,\n  uponPromise\n} from './helpers/webidl';\nimport {\n  DequeueValue,\n  EnqueueValueWithSize,\n  PeekQueueValue,\n  type QueuePair,\n  ResetQueue\n} from './abstract-ops/queue-with-sizes';\nimport type { QueuingStrategy, QueuingStrategySizeCallback } from './queuing-strategy';\nimport { SimpleQueue } from './simple-queue';\nimport { setFunctionName, typeIsObject } from './helpers/miscellaneous';\nimport { AbortSteps, ErrorSteps } from './abstract-ops/internal-methods';\nimport { IsNonNegativeNumber } from './abstract-ops/miscellaneous';\nimport { ExtractHighWaterMark, ExtractSizeAlgorithm } from './abstract-ops/queuing-strategy';\nimport { convertQueuingStrategy } from './validators/queuing-strategy';\nimport type {\n  UnderlyingSink,\n  UnderlyingSinkAbortCallback,\n  UnderlyingSinkCloseCallback,\n  UnderlyingSinkStartCallback,\n  UnderlyingSinkWriteCallback,\n  ValidatedUnderlyingSink\n} from './writable-stream/underlying-sink';\nimport { assertObject, assertRequiredArgument } from './validators/basic';\nimport { convertUnderlyingSink } from './validators/underlying-sink';\nimport { assertWritableStream } from './validators/writable-stream';\nimport { type AbortController, type AbortSignal, createAbortController } from './abort-signal';\n\ntype WritableStreamState = 'writable' | 'closed' | 'erroring' | 'errored';\n\ninterface WriteOrCloseRequest {\n  _resolve: (value?: undefined) => void;\n  _reject: (reason: any) => void;\n}\n\ntype WriteRequest = WriteOrCloseRequest;\ntype CloseRequest = WriteOrCloseRequest;\n\ninterface PendingAbortRequest {\n  _promise: Promise<undefined>;\n  _resolve: (value?: undefined) => void;\n  _reject: (reason: any) => void;\n  _reason: any;\n  _wasAlreadyErroring: boolean;\n}\n\n/**\n * A writable stream represents a destination for data, into which you can write.\n *\n * @public\n */\nclass WritableStream<W = any> {\n  /** @internal */\n  _state!: WritableStreamState;\n  /** @internal */\n  _storedError: any;\n  /** @internal */\n  _writer: WritableStreamDefaultWriter<W> | undefined;\n  /** @internal */\n  _writableStreamController!: WritableStreamDefaultController<W>;\n  /** @internal */\n  _writeRequests!: SimpleQueue<WriteRequest>;\n  /** @internal */\n  _inFlightWriteRequest: WriteRequest | undefined;\n  /** @internal */\n  _closeRequest: CloseRequest | undefined;\n  /** @internal */\n  _inFlightCloseRequest: CloseRequest | undefined;\n  /** @internal */\n  _pendingAbortRequest: PendingAbortRequest | undefined;\n  /** @internal */\n  _backpressure!: boolean;\n\n  constructor(underlyingSink?: UnderlyingSink<W>, strategy?: QueuingStrategy<W>);\n  constructor(rawUnderlyingSink: UnderlyingSink<W> | null | undefined = {},\n              rawStrategy: QueuingStrategy<W> | null | undefined = {}) {\n    if (rawUnderlyingSink === undefined) {\n      rawUnderlyingSink = null;\n    } else {\n      assertObject(rawUnderlyingSink, 'First parameter');\n    }\n\n    const strategy = convertQueuingStrategy(rawStrategy, 'Second parameter');\n    const underlyingSink = convertUnderlyingSink(rawUnderlyingSink, 'First parameter');\n\n    InitializeWritableStream(this);\n\n    const type = underlyingSink.type;\n    if (type !== undefined) {\n      throw new RangeError('Invalid type is specified');\n    }\n\n    const sizeAlgorithm = ExtractSizeAlgorithm(strategy);\n    const highWaterMark = ExtractHighWaterMark(strategy, 1);\n\n    SetUpWritableStreamDefaultControllerFromUnderlyingSink(this, underlyingSink, highWaterMark, sizeAlgorithm);\n  }\n\n  /**\n   * Returns whether or not the writable stream is locked to a writer.\n   */\n  get locked(): boolean {\n    if (!IsWritableStream(this)) {\n      throw streamBrandCheckException('locked');\n    }\n\n    return IsWritableStreamLocked(this);\n  }\n\n  /**\n   * Aborts the stream, signaling that the producer can no longer successfully write to the stream and it is to be\n   * immediately moved to an errored state, with any queued-up writes discarded. This will also execute any abort\n   * mechanism of the underlying sink.\n   *\n   * The returned promise will fulfill if the stream shuts down successfully, or reject if the underlying sink signaled\n   * that there was an error doing so. Additionally, it will reject with a `TypeError` (without attempting to cancel\n   * the stream) if the stream is currently locked.\n   */\n  abort(reason: any = undefined): Promise<void> {\n    if (!IsWritableStream(this)) {\n      return promiseRejectedWith(streamBrandCheckException('abort'));\n    }\n\n    if (IsWritableStreamLocked(this)) {\n      return promiseRejectedWith(new TypeError('Cannot abort a stream that already has a writer'));\n    }\n\n    return WritableStreamAbort(this, reason);\n  }\n\n  /**\n   * Closes the stream. The underlying sink will finish processing any previously-written chunks, before invoking its\n   * close behavior. During this time any further attempts to write will fail (without erroring the stream).\n   *\n   * The method returns a promise that will fulfill if all remaining chunks are successfully written and the stream\n   * successfully closes, or rejects if an error is encountered during this process. Additionally, it will reject with\n   * a `TypeError` (without attempting to cancel the stream) if the stream is currently locked.\n   */\n  close() {\n    if (!IsWritableStream(this)) {\n      return promiseRejectedWith(streamBrandCheckException('close'));\n    }\n\n    if (IsWritableStreamLocked(this)) {\n      return promiseRejectedWith(new TypeError('Cannot close a stream that already has a writer'));\n    }\n\n    if (WritableStreamCloseQueuedOrInFlight(this)) {\n      return promiseRejectedWith(new TypeError('Cannot close an already-closing stream'));\n    }\n\n    return WritableStreamClose(this);\n  }\n\n  /**\n   * Creates a {@link WritableStreamDefaultWriter | writer} and locks the stream to the new writer. While the stream\n   * is locked, no other writer can be acquired until this one is released.\n   *\n   * This functionality is especially useful for creating abstractions that desire the ability to write to a stream\n   * without interruption or interleaving. By getting a writer for the stream, you can ensure nobody else can write at\n   * the same time, which would cause the resulting written data to be unpredictable and probably useless.\n   */\n  getWriter(): WritableStreamDefaultWriter<W> {\n    if (!IsWritableStream(this)) {\n      throw streamBrandCheckException('getWriter');\n    }\n\n    return AcquireWritableStreamDefaultWriter(this);\n  }\n}\n\nObject.defineProperties(WritableStream.prototype, {\n  abort: { enumerable: true },\n  close: { enumerable: true },\n  getWriter: { enumerable: true },\n  locked: { enumerable: true }\n});\nsetFunctionName(WritableStream.prototype.abort, 'abort');\nsetFunctionName(WritableStream.prototype.close, 'close');\nsetFunctionName(WritableStream.prototype.getWriter, 'getWriter');\nif (typeof Symbol.toStringTag === 'symbol') {\n  Object.defineProperty(WritableStream.prototype, Symbol.toStringTag, {\n    value: 'WritableStream',\n    configurable: true\n  });\n}\n\nexport {\n  AcquireWritableStreamDefaultWriter,\n  CreateWritableStream,\n  IsWritableStream,\n  IsWritableStreamLocked,\n  WritableStream,\n  WritableStreamAbort,\n  WritableStreamDefaultControllerErrorIfNeeded,\n  WritableStreamDefaultWriterCloseWithErrorPropagation,\n  WritableStreamDefaultWriterRelease,\n  WritableStreamDefaultWriterWrite,\n  WritableStreamCloseQueuedOrInFlight\n};\n\nexport type {\n  UnderlyingSink,\n  UnderlyingSinkStartCallback,\n  UnderlyingSinkWriteCallback,\n  UnderlyingSinkCloseCallback,\n  UnderlyingSinkAbortCallback\n};\n\n// Abstract operations for the WritableStream.\n\nfunction AcquireWritableStreamDefaultWriter<W>(stream: WritableStream<W>): WritableStreamDefaultWriter<W> {\n  return new WritableStreamDefaultWriter(stream);\n}\n\n// Throws if and only if startAlgorithm throws.\nfunction CreateWritableStream<W>(startAlgorithm: () => void | PromiseLike<void>,\n                                 writeAlgorithm: (chunk: W) => Promise<void>,\n                                 closeAlgorithm: () => Promise<void>,\n                                 abortAlgorithm: (reason: any) => Promise<void>,\n                                 highWaterMark = 1,\n                                 sizeAlgorithm: QueuingStrategySizeCallback<W> = () => 1) {\n  assert(IsNonNegativeNumber(highWaterMark));\n\n  const stream: WritableStream<W> = Object.create(WritableStream.prototype);\n  InitializeWritableStream(stream);\n\n  const controller: WritableStreamDefaultController<W> = Object.create(WritableStreamDefaultController.prototype);\n\n  SetUpWritableStreamDefaultController(stream, controller, startAlgorithm, writeAlgorithm, closeAlgorithm,\n                                       abortAlgorithm, highWaterMark, sizeAlgorithm);\n  return stream;\n}\n\nfunction InitializeWritableStream<W>(stream: WritableStream<W>) {\n  stream._state = 'writable';\n\n  // The error that will be reported by new method calls once the state becomes errored. Only set when [[state]] is\n  // 'erroring' or 'errored'. May be set to an undefined value.\n  stream._storedError = undefined;\n\n  stream._writer = undefined;\n\n  // Initialize to undefined first because the constructor of the controller checks this\n  // variable to validate the caller.\n  stream._writableStreamController = undefined!;\n\n  // This queue is placed here instead of the writer class in order to allow for passing a writer to the next data\n  // producer without waiting for the queued writes to finish.\n  stream._writeRequests = new SimpleQueue();\n\n  // Write requests are removed from _writeRequests when write() is called on the underlying sink. This prevents\n  // them from being erroneously rejected on error. If a write() call is in-flight, the request is stored here.\n  stream._inFlightWriteRequest = undefined;\n\n  // The promise that was returned from writer.close(). Stored here because it may be fulfilled after the writer\n  // has been detached.\n  stream._closeRequest = undefined;\n\n  // Close request is removed from _closeRequest when close() is called on the underlying sink. This prevents it\n  // from being erroneously rejected on error. If a close() call is in-flight, the request is stored here.\n  stream._inFlightCloseRequest = undefined;\n\n  // The promise that was returned from writer.abort(). This may also be fulfilled after the writer has detached.\n  stream._pendingAbortRequest = undefined;\n\n  // The backpressure signal set by the controller.\n  stream._backpressure = false;\n}\n\nfunction IsWritableStream(x: unknown): x is WritableStream {\n  if (!typeIsObject(x)) {\n    return false;\n  }\n\n  if (!Object.prototype.hasOwnProperty.call(x, '_writableStreamController')) {\n    return false;\n  }\n\n  return x instanceof WritableStream;\n}\n\nfunction IsWritableStreamLocked(stream: WritableStream): boolean {\n  assert(IsWritableStream(stream));\n\n  if (stream._writer === undefined) {\n    return false;\n  }\n\n  return true;\n}\n\nfunction WritableStreamAbort(stream: WritableStream, reason: any): Promise<undefined> {\n  if (stream._state === 'closed' || stream._state === 'errored') {\n    return promiseResolvedWith(undefined);\n  }\n  stream._writableStreamController._abortReason = reason;\n  stream._writableStreamController._abortController?.abort(reason);\n\n  // TypeScript narrows the type of `stream._state` down to 'writable' | 'erroring',\n  // but it doesn't know that signaling abort runs author code that might have changed the state.\n  // Widen the type again by casting to WritableStreamState.\n  const state = stream._state as WritableStreamState;\n\n  if (state === 'closed' || state === 'errored') {\n    return promiseResolvedWith(undefined);\n  }\n  if (stream._pendingAbortRequest !== undefined) {\n    return stream._pendingAbortRequest._promise;\n  }\n\n  assert(state === 'writable' || state === 'erroring');\n\n  let wasAlreadyErroring = false;\n  if (state === 'erroring') {\n    wasAlreadyErroring = true;\n    // reason will not be used, so don't keep a reference to it.\n    reason = undefined;\n  }\n\n  const promise = newPromise<undefined>((resolve, reject) => {\n    stream._pendingAbortRequest = {\n      _promise: undefined!,\n      _resolve: resolve,\n      _reject: reject,\n      _reason: reason,\n      _wasAlreadyErroring: wasAlreadyErroring\n    };\n  });\n  stream._pendingAbortRequest!._promise = promise;\n\n  if (!wasAlreadyErroring) {\n    WritableStreamStartErroring(stream, reason);\n  }\n\n  return promise;\n}\n\nfunction WritableStreamClose(stream: WritableStream<any>): Promise<undefined> {\n  const state = stream._state;\n  if (state === 'closed' || state === 'errored') {\n    return promiseRejectedWith(new TypeError(\n      `The stream (in ${state} state) is not in the writable state and cannot be closed`));\n  }\n\n  assert(state === 'writable' || state === 'erroring');\n  assert(!WritableStreamCloseQueuedOrInFlight(stream));\n\n  const promise = newPromise<undefined>((resolve, reject) => {\n    const closeRequest: CloseRequest = {\n      _resolve: resolve,\n      _reject: reject\n    };\n\n    stream._closeRequest = closeRequest;\n  });\n\n  const writer = stream._writer;\n  if (writer !== undefined && stream._backpressure && state === 'writable') {\n    defaultWriterReadyPromiseResolve(writer);\n  }\n\n  WritableStreamDefaultControllerClose(stream._writableStreamController);\n\n  return promise;\n}\n\n// WritableStream API exposed for controllers.\n\nfunction WritableStreamAddWriteRequest(stream: WritableStream): Promise<undefined> {\n  assert(IsWritableStreamLocked(stream));\n  assert(stream._state === 'writable');\n\n  const promise = newPromise<undefined>((resolve, reject) => {\n    const writeRequest: WriteRequest = {\n      _resolve: resolve,\n      _reject: reject\n    };\n\n    stream._writeRequests.push(writeRequest);\n  });\n\n  return promise;\n}\n\nfunction WritableStreamDealWithRejection(stream: WritableStream, error: any) {\n  const state = stream._state;\n\n  if (state === 'writable') {\n    WritableStreamStartErroring(stream, error);\n    return;\n  }\n\n  assert(state === 'erroring');\n  WritableStreamFinishErroring(stream);\n}\n\nfunction WritableStreamStartErroring(stream: WritableStream, reason: any) {\n  assert(stream._storedError === undefined);\n  assert(stream._state === 'writable');\n\n  const controller = stream._writableStreamController;\n  assert(controller !== undefined);\n\n  stream._state = 'erroring';\n  stream._storedError = reason;\n  const writer = stream._writer;\n  if (writer !== undefined) {\n    WritableStreamDefaultWriterEnsureReadyPromiseRejected(writer, reason);\n  }\n\n  if (!WritableStreamHasOperationMarkedInFlight(stream) && controller._started) {\n    WritableStreamFinishErroring(stream);\n  }\n}\n\nfunction WritableStreamFinishErroring(stream: WritableStream) {\n  assert(stream._state === 'erroring');\n  assert(!WritableStreamHasOperationMarkedInFlight(stream));\n  stream._state = 'errored';\n  stream._writableStreamController[ErrorSteps]();\n\n  const storedError = stream._storedError;\n  stream._writeRequests.forEach(writeRequest => {\n    writeRequest._reject(storedError);\n  });\n  stream._writeRequests = new SimpleQueue();\n\n  if (stream._pendingAbortRequest === undefined) {\n    WritableStreamRejectCloseAndClosedPromiseIfNeeded(stream);\n    return;\n  }\n\n  const abortRequest = stream._pendingAbortRequest;\n  stream._pendingAbortRequest = undefined;\n\n  if (abortRequest._wasAlreadyErroring) {\n    abortRequest._reject(storedError);\n    WritableStreamRejectCloseAndClosedPromiseIfNeeded(stream);\n    return;\n  }\n\n  const promise = stream._writableStreamController[AbortSteps](abortRequest._reason);\n  uponPromise(\n    promise,\n    () => {\n      abortRequest._resolve();\n      WritableStreamRejectCloseAndClosedPromiseIfNeeded(stream);\n      return null;\n    },\n    (reason: any) => {\n      abortRequest._reject(reason);\n      WritableStreamRejectCloseAndClosedPromiseIfNeeded(stream);\n      return null;\n    });\n}\n\nfunction WritableStreamFinishInFlightWrite(stream: WritableStream) {\n  assert(stream._inFlightWriteRequest !== undefined);\n  stream._inFlightWriteRequest!._resolve(undefined);\n  stream._inFlightWriteRequest = undefined;\n}\n\nfunction WritableStreamFinishInFlightWriteWithError(stream: WritableStream, error: any) {\n  assert(stream._inFlightWriteRequest !== undefined);\n  stream._inFlightWriteRequest!._reject(error);\n  stream._inFlightWriteRequest = undefined;\n\n  assert(stream._state === 'writable' || stream._state === 'erroring');\n\n  WritableStreamDealWithRejection(stream, error);\n}\n\nfunction WritableStreamFinishInFlightClose(stream: WritableStream) {\n  assert(stream._inFlightCloseRequest !== undefined);\n  stream._inFlightCloseRequest!._resolve(undefined);\n  stream._inFlightCloseRequest = undefined;\n\n  const state = stream._state;\n\n  assert(state === 'writable' || state === 'erroring');\n\n  if (state === 'erroring') {\n    // The error was too late to do anything, so it is ignored.\n    stream._storedError = undefined;\n    if (stream._pendingAbortRequest !== undefined) {\n      stream._pendingAbortRequest._resolve();\n      stream._pendingAbortRequest = undefined;\n    }\n  }\n\n  stream._state = 'closed';\n\n  const writer = stream._writer;\n  if (writer !== undefined) {\n    defaultWriterClosedPromiseResolve(writer);\n  }\n\n  assert(stream._pendingAbortRequest === undefined);\n  assert(stream._storedError === undefined);\n}\n\nfunction WritableStreamFinishInFlightCloseWithError(stream: WritableStream, error: any) {\n  assert(stream._inFlightCloseRequest !== undefined);\n  stream._inFlightCloseRequest!._reject(error);\n  stream._inFlightCloseRequest = undefined;\n\n  assert(stream._state === 'writable' || stream._state === 'erroring');\n\n  // Never execute sink abort() after sink close().\n  if (stream._pendingAbortRequest !== undefined) {\n    stream._pendingAbortRequest._reject(error);\n    stream._pendingAbortRequest = undefined;\n  }\n  WritableStreamDealWithRejection(stream, error);\n}\n\n// TODO(ricea): Fix alphabetical order.\nfunction WritableStreamCloseQueuedOrInFlight(stream: WritableStream): boolean {\n  if (stream._closeRequest === undefined && stream._inFlightCloseRequest === undefined) {\n    return false;\n  }\n\n  return true;\n}\n\nfunction WritableStreamHasOperationMarkedInFlight(stream: WritableStream): boolean {\n  if (stream._inFlightWriteRequest === undefined && stream._inFlightCloseRequest === undefined) {\n    return false;\n  }\n\n  return true;\n}\n\nfunction WritableStreamMarkCloseRequestInFlight(stream: WritableStream) {\n  assert(stream._inFlightCloseRequest === undefined);\n  assert(stream._closeRequest !== undefined);\n  stream._inFlightCloseRequest = stream._closeRequest;\n  stream._closeRequest = undefined;\n}\n\nfunction WritableStreamMarkFirstWriteRequestInFlight(stream: WritableStream) {\n  assert(stream._inFlightWriteRequest === undefined);\n  assert(stream._writeRequests.length !== 0);\n  stream._inFlightWriteRequest = stream._writeRequests.shift();\n}\n\nfunction WritableStreamRejectCloseAndClosedPromiseIfNeeded(stream: WritableStream) {\n  assert(stream._state === 'errored');\n  if (stream._closeRequest !== undefined) {\n    assert(stream._inFlightCloseRequest === undefined);\n\n    stream._closeRequest._reject(stream._storedError);\n    stream._closeRequest = undefined;\n  }\n  const writer = stream._writer;\n  if (writer !== undefined) {\n    defaultWriterClosedPromiseReject(writer, stream._storedError);\n  }\n}\n\nfunction WritableStreamUpdateBackpressure(stream: WritableStream, backpressure: boolean) {\n  assert(stream._state === 'writable');\n  assert(!WritableStreamCloseQueuedOrInFlight(stream));\n\n  const writer = stream._writer;\n  if (writer !== undefined && backpressure !== stream._backpressure) {\n    if (backpressure) {\n      defaultWriterReadyPromiseReset(writer);\n    } else {\n      assert(!backpressure);\n\n      defaultWriterReadyPromiseResolve(writer);\n    }\n  }\n\n  stream._backpressure = backpressure;\n}\n\n/**\n * A default writer vended by a {@link WritableStream}.\n *\n * @public\n */\nexport class WritableStreamDefaultWriter<W = any> {\n  /** @internal */\n  _ownerWritableStream: WritableStream<W>;\n  /** @internal */\n  _closedPromise!: Promise<undefined>;\n  /** @internal */\n  _closedPromise_resolve?: (value?: undefined) => void;\n  /** @internal */\n  _closedPromise_reject?: (reason: any) => void;\n  /** @internal */\n  _closedPromiseState!: 'pending' | 'resolved' | 'rejected';\n  /** @internal */\n  _readyPromise!: Promise<undefined>;\n  /** @internal */\n  _readyPromise_resolve?: (value?: undefined) => void;\n  /** @internal */\n  _readyPromise_reject?: (reason: any) => void;\n  /** @internal */\n  _readyPromiseState!: 'pending' | 'fulfilled' | 'rejected';\n\n  constructor(stream: WritableStream<W>) {\n    assertRequiredArgument(stream, 1, 'WritableStreamDefaultWriter');\n    assertWritableStream(stream, 'First parameter');\n\n    if (IsWritableStreamLocked(stream)) {\n      throw new TypeError('This stream has already been locked for exclusive writing by another writer');\n    }\n\n    this._ownerWritableStream = stream;\n    stream._writer = this;\n\n    const state = stream._state;\n\n    if (state === 'writable') {\n      if (!WritableStreamCloseQueuedOrInFlight(stream) && stream._backpressure) {\n        defaultWriterReadyPromiseInitialize(this);\n      } else {\n        defaultWriterReadyPromiseInitializeAsResolved(this);\n      }\n\n      defaultWriterClosedPromiseInitialize(this);\n    } else if (state === 'erroring') {\n      defaultWriterReadyPromiseInitializeAsRejected(this, stream._storedError);\n      defaultWriterClosedPromiseInitialize(this);\n    } else if (state === 'closed') {\n      defaultWriterReadyPromiseInitializeAsResolved(this);\n      defaultWriterClosedPromiseInitializeAsResolved(this);\n    } else {\n      assert(state === 'errored');\n\n      const storedError = stream._storedError;\n      defaultWriterReadyPromiseInitializeAsRejected(this, storedError);\n      defaultWriterClosedPromiseInitializeAsRejected(this, storedError);\n    }\n  }\n\n  /**\n   * Returns a promise that will be fulfilled when the stream becomes closed, or rejected if the stream ever errors or\n   * the writer’s lock is released before the stream finishes closing.\n   */\n  get closed(): Promise<undefined> {\n    if (!IsWritableStreamDefaultWriter(this)) {\n      return promiseRejectedWith(defaultWriterBrandCheckException('closed'));\n    }\n\n    return this._closedPromise;\n  }\n\n  /**\n   * Returns the desired size to fill the stream’s internal queue. It can be negative, if the queue is over-full.\n   * A producer can use this information to determine the right amount of data to write.\n   *\n   * It will be `null` if the stream cannot be successfully written to (due to either being errored, or having an abort\n   * queued up). It will return zero if the stream is closed. And the getter will throw an exception if invoked when\n   * the writer’s lock is released.\n   */\n  get desiredSize(): number | null {\n    if (!IsWritableStreamDefaultWriter(this)) {\n      throw defaultWriterBrandCheckException('desiredSize');\n    }\n\n    if (this._ownerWritableStream === undefined) {\n      throw defaultWriterLockException('desiredSize');\n    }\n\n    return WritableStreamDefaultWriterGetDesiredSize(this);\n  }\n\n  /**\n   * Returns a promise that will be fulfilled when the desired size to fill the stream’s internal queue transitions\n   * from non-positive to positive, signaling that it is no longer applying backpressure. Once the desired size dips\n   * back to zero or below, the getter will return a new promise that stays pending until the next transition.\n   *\n   * If the stream becomes errored or aborted, or the writer’s lock is released, the returned promise will become\n   * rejected.\n   */\n  get ready(): Promise<undefined> {\n    if (!IsWritableStreamDefaultWriter(this)) {\n      return promiseRejectedWith(defaultWriterBrandCheckException('ready'));\n    }\n\n    return this._readyPromise;\n  }\n\n  /**\n   * If the reader is active, behaves the same as {@link WritableStream.abort | stream.abort(reason)}.\n   */\n  abort(reason: any = undefined): Promise<void> {\n    if (!IsWritableStreamDefaultWriter(this)) {\n      return promiseRejectedWith(defaultWriterBrandCheckException('abort'));\n    }\n\n    if (this._ownerWritableStream === undefined) {\n      return promiseRejectedWith(defaultWriterLockException('abort'));\n    }\n\n    return WritableStreamDefaultWriterAbort(this, reason);\n  }\n\n  /**\n   * If the reader is active, behaves the same as {@link WritableStream.close | stream.close()}.\n   */\n  close(): Promise<void> {\n    if (!IsWritableStreamDefaultWriter(this)) {\n      return promiseRejectedWith(defaultWriterBrandCheckException('close'));\n    }\n\n    const stream = this._ownerWritableStream;\n\n    if (stream === undefined) {\n      return promiseRejectedWith(defaultWriterLockException('close'));\n    }\n\n    if (WritableStreamCloseQueuedOrInFlight(stream)) {\n      return promiseRejectedWith(new TypeError('Cannot close an already-closing stream'));\n    }\n\n    return WritableStreamDefaultWriterClose(this);\n  }\n\n  /**\n   * Releases the writer’s lock on the corresponding stream. After the lock is released, the writer is no longer active.\n   * If the associated stream is errored when the lock is released, the writer will appear errored in the same way from\n   * now on; otherwise, the writer will appear closed.\n   *\n   * Note that the lock can still be released even if some ongoing writes have not yet finished (i.e. even if the\n   * promises returned from previous calls to {@link WritableStreamDefaultWriter.write | write()} have not yet settled).\n   * It’s not necessary to hold the lock on the writer for the duration of the write; the lock instead simply prevents\n   * other producers from writing in an interleaved manner.\n   */\n  releaseLock(): void {\n    if (!IsWritableStreamDefaultWriter(this)) {\n      throw defaultWriterBrandCheckException('releaseLock');\n    }\n\n    const stream = this._ownerWritableStream;\n\n    if (stream === undefined) {\n      return;\n    }\n\n    assert(stream._writer !== undefined);\n\n    WritableStreamDefaultWriterRelease(this);\n  }\n\n  /**\n   * Writes the given chunk to the writable stream, by waiting until any previous writes have finished successfully,\n   * and then sending the chunk to the underlying sink's {@link UnderlyingSink.write | write()} method. It will return\n   * a promise that fulfills with undefined upon a successful write, or rejects if the write fails or stream becomes\n   * errored before the writing process is initiated.\n   *\n   * Note that what \"success\" means is up to the underlying sink; it might indicate simply that the chunk has been\n   * accepted, and not necessarily that it is safely saved to its ultimate destination.\n   */\n  write(chunk: W): Promise<void>;\n  write(chunk: W = undefined!): Promise<void> {\n    if (!IsWritableStreamDefaultWriter(this)) {\n      return promiseRejectedWith(defaultWriterBrandCheckException('write'));\n    }\n\n    if (this._ownerWritableStream === undefined) {\n      return promiseRejectedWith(defaultWriterLockException('write to'));\n    }\n\n    return WritableStreamDefaultWriterWrite(this, chunk);\n  }\n}\n\nObject.defineProperties(WritableStreamDefaultWriter.prototype, {\n  abort: { enumerable: true },\n  close: { enumerable: true },\n  releaseLock: { enumerable: true },\n  write: { enumerable: true },\n  closed: { enumerable: true },\n  desiredSize: { enumerable: true },\n  ready: { enumerable: true }\n});\nsetFunctionName(WritableStreamDefaultWriter.prototype.abort, 'abort');\nsetFunctionName(WritableStreamDefaultWriter.prototype.close, 'close');\nsetFunctionName(WritableStreamDefaultWriter.prototype.releaseLock, 'releaseLock');\nsetFunctionName(WritableStreamDefaultWriter.prototype.write, 'write');\nif (typeof Symbol.toStringTag === 'symbol') {\n  Object.defineProperty(WritableStreamDefaultWriter.prototype, Symbol.toStringTag, {\n    value: 'WritableStreamDefaultWriter',\n    configurable: true\n  });\n}\n\n// Abstract operations for the WritableStreamDefaultWriter.\n\nfunction IsWritableStreamDefaultWriter<W = any>(x: any): x is WritableStreamDefaultWriter<W> {\n  if (!typeIsObject(x)) {\n    return false;\n  }\n\n  if (!Object.prototype.hasOwnProperty.call(x, '_ownerWritableStream')) {\n    return false;\n  }\n\n  return x instanceof WritableStreamDefaultWriter;\n}\n\n// A client of WritableStreamDefaultWriter may use these functions directly to bypass state check.\n\nfunction WritableStreamDefaultWriterAbort(writer: WritableStreamDefaultWriter, reason: any) {\n  const stream = writer._ownerWritableStream;\n\n  assert(stream !== undefined);\n\n  return WritableStreamAbort(stream, reason);\n}\n\nfunction WritableStreamDefaultWriterClose(writer: WritableStreamDefaultWriter): Promise<undefined> {\n  const stream = writer._ownerWritableStream;\n\n  assert(stream !== undefined);\n\n  return WritableStreamClose(stream);\n}\n\nfunction WritableStreamDefaultWriterCloseWithErrorPropagation(writer: WritableStreamDefaultWriter): Promise<undefined> {\n  const stream = writer._ownerWritableStream;\n\n  assert(stream !== undefined);\n\n  const state = stream._state;\n  if (WritableStreamCloseQueuedOrInFlight(stream) || state === 'closed') {\n    return promiseResolvedWith(undefined);\n  }\n\n  if (state === 'errored') {\n    return promiseRejectedWith(stream._storedError);\n  }\n\n  assert(state === 'writable' || state === 'erroring');\n\n  return WritableStreamDefaultWriterClose(writer);\n}\n\nfunction WritableStreamDefaultWriterEnsureClosedPromiseRejected(writer: WritableStreamDefaultWriter, error: any) {\n  if (writer._closedPromiseState === 'pending') {\n    defaultWriterClosedPromiseReject(writer, error);\n  } else {\n    defaultWriterClosedPromiseResetToRejected(writer, error);\n  }\n}\n\nfunction WritableStreamDefaultWriterEnsureReadyPromiseRejected(writer: WritableStreamDefaultWriter, error: any) {\n  if (writer._readyPromiseState === 'pending') {\n    defaultWriterReadyPromiseReject(writer, error);\n  } else {\n    defaultWriterReadyPromiseResetToRejected(writer, error);\n  }\n}\n\nfunction WritableStreamDefaultWriterGetDesiredSize(writer: WritableStreamDefaultWriter): number | null {\n  const stream = writer._ownerWritableStream;\n  const state = stream._state;\n\n  if (state === 'errored' || state === 'erroring') {\n    return null;\n  }\n\n  if (state === 'closed') {\n    return 0;\n  }\n\n  return WritableStreamDefaultControllerGetDesiredSize(stream._writableStreamController);\n}\n\nfunction WritableStreamDefaultWriterRelease(writer: WritableStreamDefaultWriter) {\n  const stream = writer._ownerWritableStream;\n  assert(stream !== undefined);\n  assert(stream._writer === writer);\n\n  const releasedError = new TypeError(\n    `Writer was released and can no longer be used to monitor the stream's closedness`);\n\n  WritableStreamDefaultWriterEnsureReadyPromiseRejected(writer, releasedError);\n\n  // The state transitions to \"errored\" before the sink abort() method runs, but the writer.closed promise is not\n  // rejected until afterwards. This means that simply testing state will not work.\n  WritableStreamDefaultWriterEnsureClosedPromiseRejected(writer, releasedError);\n\n  stream._writer = undefined;\n  writer._ownerWritableStream = undefined!;\n}\n\nfunction WritableStreamDefaultWriterWrite<W>(writer: WritableStreamDefaultWriter<W>, chunk: W): Promise<undefined> {\n  const stream = writer._ownerWritableStream;\n\n  assert(stream !== undefined);\n\n  const controller = stream._writableStreamController;\n\n  const chunkSize = WritableStreamDefaultControllerGetChunkSize(controller, chunk);\n\n  if (stream !== writer._ownerWritableStream) {\n    return promiseRejectedWith(defaultWriterLockException('write to'));\n  }\n\n  const state = stream._state;\n  if (state === 'errored') {\n    return promiseRejectedWith(stream._storedError);\n  }\n  if (WritableStreamCloseQueuedOrInFlight(stream) || state === 'closed') {\n    return promiseRejectedWith(new TypeError('The stream is closing or closed and cannot be written to'));\n  }\n  if (state === 'erroring') {\n    return promiseRejectedWith(stream._storedError);\n  }\n\n  assert(state === 'writable');\n\n  const promise = WritableStreamAddWriteRequest(stream);\n\n  WritableStreamDefaultControllerWrite(controller, chunk, chunkSize);\n\n  return promise;\n}\n\nconst closeSentinel: unique symbol = {} as any;\n\ntype QueueRecord<W> = W | typeof closeSentinel;\n\n/**\n * Allows control of a {@link WritableStream | writable stream}'s state and internal queue.\n *\n * @public\n */\nexport class WritableStreamDefaultController<W = any> {\n  /** @internal */\n  _controlledWritableStream!: WritableStream<W>;\n  /** @internal */\n  _queue!: SimpleQueue<QueuePair<QueueRecord<W>>>;\n  /** @internal */\n  _queueTotalSize!: number;\n  /** @internal */\n  _abortReason: any;\n  /** @internal */\n  _abortController: AbortController | undefined;\n  /** @internal */\n  _started!: boolean;\n  /** @internal */\n  _strategySizeAlgorithm!: QueuingStrategySizeCallback<W>;\n  /** @internal */\n  _strategyHWM!: number;\n  /** @internal */\n  _writeAlgorithm!: (chunk: W) => Promise<void>;\n  /** @internal */\n  _closeAlgorithm!: () => Promise<void>;\n  /** @internal */\n  _abortAlgorithm!: (reason: any) => Promise<void>;\n\n  private constructor() {\n    throw new TypeError('Illegal constructor');\n  }\n\n  /**\n   * The reason which was passed to `WritableStream.abort(reason)` when the stream was aborted.\n   *\n   * @deprecated\n   *  This property has been removed from the specification, see https://github.com/whatwg/streams/pull/1177.\n   *  Use {@link WritableStreamDefaultController.signal}'s `reason` instead.\n   */\n  get abortReason(): any {\n    if (!IsWritableStreamDefaultController(this)) {\n      throw defaultControllerBrandCheckException('abortReason');\n    }\n    return this._abortReason;\n  }\n\n  /**\n   * An `AbortSignal` that can be used to abort the pending write or close operation when the stream is aborted.\n   */\n  get signal(): AbortSignal {\n    if (!IsWritableStreamDefaultController(this)) {\n      throw defaultControllerBrandCheckException('signal');\n    }\n    if (this._abortController === undefined) {\n      // Older browsers or older Node versions may not support `AbortController` or `AbortSignal`.\n      // We don't want to bundle and ship an `AbortController` polyfill together with our polyfill,\n      // so instead we only implement support for `signal` if we find a global `AbortController` constructor.\n      throw new TypeError('WritableStreamDefaultController.prototype.signal is not supported');\n    }\n    return this._abortController.signal;\n  }\n\n  /**\n   * Closes the controlled writable stream, making all future interactions with it fail with the given error `e`.\n   *\n   * This method is rarely used, since usually it suffices to return a rejected promise from one of the underlying\n   * sink's methods. However, it can be useful for suddenly shutting down a stream in response to an event outside the\n   * normal lifecycle of interactions with the underlying sink.\n   */\n  error(e: any = undefined): void {\n    if (!IsWritableStreamDefaultController(this)) {\n      throw defaultControllerBrandCheckException('error');\n    }\n    const state = this._controlledWritableStream._state;\n    if (state !== 'writable') {\n      // The stream is closed, errored or will be soon. The sink can't do anything useful if it gets an error here, so\n      // just treat it as a no-op.\n      return;\n    }\n\n    WritableStreamDefaultControllerError(this, e);\n  }\n\n  /** @internal */\n  [AbortSteps](reason: any): Promise<void> {\n    const result = this._abortAlgorithm(reason);\n    WritableStreamDefaultControllerClearAlgorithms(this);\n    return result;\n  }\n\n  /** @internal */\n  [ErrorSteps]() {\n    ResetQueue(this);\n  }\n}\n\nObject.defineProperties(WritableStreamDefaultController.prototype, {\n  abortReason: { enumerable: true },\n  signal: { enumerable: true },\n  error: { enumerable: true }\n});\nif (typeof Symbol.toStringTag === 'symbol') {\n  Object.defineProperty(WritableStreamDefaultController.prototype, Symbol.toStringTag, {\n    value: 'WritableStreamDefaultController',\n    configurable: true\n  });\n}\n\n// Abstract operations implementing interface required by the WritableStream.\n\nfunction IsWritableStreamDefaultController(x: any): x is WritableStreamDefaultController<any> {\n  if (!typeIsObject(x)) {\n    return false;\n  }\n\n  if (!Object.prototype.hasOwnProperty.call(x, '_controlledWritableStream')) {\n    return false;\n  }\n\n  return x instanceof WritableStreamDefaultController;\n}\n\nfunction SetUpWritableStreamDefaultController<W>(stream: WritableStream<W>,\n                                                 controller: WritableStreamDefaultController<W>,\n                                                 startAlgorithm: () => void | PromiseLike<void>,\n                                                 writeAlgorithm: (chunk: W) => Promise<void>,\n                                                 closeAlgorithm: () => Promise<void>,\n                                                 abortAlgorithm: (reason: any) => Promise<void>,\n                                                 highWaterMark: number,\n                                                 sizeAlgorithm: QueuingStrategySizeCallback<W>) {\n  assert(IsWritableStream(stream));\n  assert(stream._writableStreamController === undefined);\n\n  controller._controlledWritableStream = stream;\n  stream._writableStreamController = controller;\n\n  // Need to set the slots so that the assert doesn't fire. In the spec the slots already exist implicitly.\n  controller._queue = undefined!;\n  controller._queueTotalSize = undefined!;\n  ResetQueue(controller);\n\n  controller._abortReason = undefined;\n  controller._abortController = createAbortController();\n  controller._started = false;\n\n  controller._strategySizeAlgorithm = sizeAlgorithm;\n  controller._strategyHWM = highWaterMark;\n\n  controller._writeAlgorithm = writeAlgorithm;\n  controller._closeAlgorithm = closeAlgorithm;\n  controller._abortAlgorithm = abortAlgorithm;\n\n  const backpressure = WritableStreamDefaultControllerGetBackpressure(controller);\n  WritableStreamUpdateBackpressure(stream, backpressure);\n\n  const startResult = startAlgorithm();\n  const startPromise = promiseResolvedWith(startResult);\n  uponPromise(\n    startPromise,\n    () => {\n      assert(stream._state === 'writable' || stream._state === 'erroring');\n      controller._started = true;\n      WritableStreamDefaultControllerAdvanceQueueIfNeeded(controller);\n      return null;\n    },\n    r => {\n      assert(stream._state === 'writable' || stream._state === 'erroring');\n      controller._started = true;\n      WritableStreamDealWithRejection(stream, r);\n      return null;\n    }\n  );\n}\n\nfunction SetUpWritableStreamDefaultControllerFromUnderlyingSink<W>(stream: WritableStream<W>,\n                                                                   underlyingSink: ValidatedUnderlyingSink<W>,\n                                                                   highWaterMark: number,\n                                                                   sizeAlgorithm: QueuingStrategySizeCallback<W>) {\n  const controller = Object.create(WritableStreamDefaultController.prototype);\n\n  let startAlgorithm: () => void | PromiseLike<void>;\n  let writeAlgorithm: (chunk: W) => Promise<void>;\n  let closeAlgorithm: () => Promise<void>;\n  let abortAlgorithm: (reason: any) => Promise<void>;\n\n  if (underlyingSink.start !== undefined) {\n    startAlgorithm = () => underlyingSink.start!(controller);\n  } else {\n    startAlgorithm = () => undefined;\n  }\n  if (underlyingSink.write !== undefined) {\n    writeAlgorithm = chunk => underlyingSink.write!(chunk, controller);\n  } else {\n    writeAlgorithm = () => promiseResolvedWith(undefined);\n  }\n  if (underlyingSink.close !== undefined) {\n    closeAlgorithm = () => underlyingSink.close!();\n  } else {\n    closeAlgorithm = () => promiseResolvedWith(undefined);\n  }\n  if (underlyingSink.abort !== undefined) {\n    abortAlgorithm = reason => underlyingSink.abort!(reason);\n  } else {\n    abortAlgorithm = () => promiseResolvedWith(undefined);\n  }\n\n  SetUpWritableStreamDefaultController(\n    stream, controller, startAlgorithm, writeAlgorithm, closeAlgorithm, abortAlgorithm, highWaterMark, sizeAlgorithm\n  );\n}\n\n// ClearAlgorithms may be called twice. Erroring the same stream in multiple ways will often result in redundant calls.\nfunction WritableStreamDefaultControllerClearAlgorithms(controller: WritableStreamDefaultController<any>) {\n  controller._writeAlgorithm = undefined!;\n  controller._closeAlgorithm = undefined!;\n  controller._abortAlgorithm = undefined!;\n  controller._strategySizeAlgorithm = undefined!;\n}\n\nfunction WritableStreamDefaultControllerClose<W>(controller: WritableStreamDefaultController<W>) {\n  EnqueueValueWithSize(controller, closeSentinel, 0);\n  WritableStreamDefaultControllerAdvanceQueueIfNeeded(controller);\n}\n\nfunction WritableStreamDefaultControllerGetChunkSize<W>(controller: WritableStreamDefaultController<W>,\n                                                        chunk: W): number {\n  try {\n    return controller._strategySizeAlgorithm(chunk);\n  } catch (chunkSizeE) {\n    WritableStreamDefaultControllerErrorIfNeeded(controller, chunkSizeE);\n    return 1;\n  }\n}\n\nfunction WritableStreamDefaultControllerGetDesiredSize(controller: WritableStreamDefaultController<any>): number {\n  return controller._strategyHWM - controller._queueTotalSize;\n}\n\nfunction WritableStreamDefaultControllerWrite<W>(controller: WritableStreamDefaultController<W>,\n                                                 chunk: W,\n                                                 chunkSize: number) {\n  try {\n    EnqueueValueWithSize(controller, chunk, chunkSize);\n  } catch (enqueueE) {\n    WritableStreamDefaultControllerErrorIfNeeded(controller, enqueueE);\n    return;\n  }\n\n  const stream = controller._controlledWritableStream;\n  if (!WritableStreamCloseQueuedOrInFlight(stream) && stream._state === 'writable') {\n    const backpressure = WritableStreamDefaultControllerGetBackpressure(controller);\n    WritableStreamUpdateBackpressure(stream, backpressure);\n  }\n\n  WritableStreamDefaultControllerAdvanceQueueIfNeeded(controller);\n}\n\n// Abstract operations for the WritableStreamDefaultController.\n\nfunction WritableStreamDefaultControllerAdvanceQueueIfNeeded<W>(controller: WritableStreamDefaultController<W>) {\n  const stream = controller._controlledWritableStream;\n\n  if (!controller._started) {\n    return;\n  }\n\n  if (stream._inFlightWriteRequest !== undefined) {\n    return;\n  }\n\n  const state = stream._state;\n  assert(state !== 'closed' && state !== 'errored');\n  if (state === 'erroring') {\n    WritableStreamFinishErroring(stream);\n    return;\n  }\n\n  if (controller._queue.length === 0) {\n    return;\n  }\n\n  const value = PeekQueueValue(controller);\n  if (value === closeSentinel) {\n    WritableStreamDefaultControllerProcessClose(controller);\n  } else {\n    WritableStreamDefaultControllerProcessWrite(controller, value);\n  }\n}\n\nfunction WritableStreamDefaultControllerErrorIfNeeded(controller: WritableStreamDefaultController<any>, error: any) {\n  if (controller._controlledWritableStream._state === 'writable') {\n    WritableStreamDefaultControllerError(controller, error);\n  }\n}\n\nfunction WritableStreamDefaultControllerProcessClose(controller: WritableStreamDefaultController<any>) {\n  const stream = controller._controlledWritableStream;\n\n  WritableStreamMarkCloseRequestInFlight(stream);\n\n  DequeueValue(controller);\n  assert(controller._queue.length === 0);\n\n  const sinkClosePromise = controller._closeAlgorithm();\n  WritableStreamDefaultControllerClearAlgorithms(controller);\n  uponPromise(\n    sinkClosePromise,\n    () => {\n      WritableStreamFinishInFlightClose(stream);\n      return null;\n    },\n    reason => {\n      WritableStreamFinishInFlightCloseWithError(stream, reason);\n      return null;\n    }\n  );\n}\n\nfunction WritableStreamDefaultControllerProcessWrite<W>(controller: WritableStreamDefaultController<W>, chunk: W) {\n  const stream = controller._controlledWritableStream;\n\n  WritableStreamMarkFirstWriteRequestInFlight(stream);\n\n  const sinkWritePromise = controller._writeAlgorithm(chunk);\n  uponPromise(\n    sinkWritePromise,\n    () => {\n      WritableStreamFinishInFlightWrite(stream);\n\n      const state = stream._state;\n      assert(state === 'writable' || state === 'erroring');\n\n      DequeueValue(controller);\n\n      if (!WritableStreamCloseQueuedOrInFlight(stream) && state === 'writable') {\n        const backpressure = WritableStreamDefaultControllerGetBackpressure(controller);\n        WritableStreamUpdateBackpressure(stream, backpressure);\n      }\n\n      WritableStreamDefaultControllerAdvanceQueueIfNeeded(controller);\n      return null;\n    },\n    reason => {\n      if (stream._state === 'writable') {\n        WritableStreamDefaultControllerClearAlgorithms(controller);\n      }\n      WritableStreamFinishInFlightWriteWithError(stream, reason);\n      return null;\n    }\n  );\n}\n\nfunction WritableStreamDefaultControllerGetBackpressure(controller: WritableStreamDefaultController<any>): boolean {\n  const desiredSize = WritableStreamDefaultControllerGetDesiredSize(controller);\n  return desiredSize <= 0;\n}\n\n// A client of WritableStreamDefaultController may use these functions directly to bypass state check.\n\nfunction WritableStreamDefaultControllerError(controller: WritableStreamDefaultController<any>, error: any) {\n  const stream = controller._controlledWritableStream;\n\n  assert(stream._state === 'writable');\n\n  WritableStreamDefaultControllerClearAlgorithms(controller);\n  WritableStreamStartErroring(stream, error);\n}\n\n// Helper functions for the WritableStream.\n\nfunction streamBrandCheckException(name: string): TypeError {\n  return new TypeError(`WritableStream.prototype.${name} can only be used on a WritableStream`);\n}\n\n// Helper functions for the WritableStreamDefaultController.\n\nfunction defaultControllerBrandCheckException(name: string): TypeError {\n  return new TypeError(\n    `WritableStreamDefaultController.prototype.${name} can only be used on a WritableStreamDefaultController`);\n}\n\n\n// Helper functions for the WritableStreamDefaultWriter.\n\nfunction defaultWriterBrandCheckException(name: string): TypeError {\n  return new TypeError(\n    `WritableStreamDefaultWriter.prototype.${name} can only be used on a WritableStreamDefaultWriter`);\n}\n\nfunction defaultWriterLockException(name: string): TypeError {\n  return new TypeError('Cannot ' + name + ' a stream using a released writer');\n}\n\nfunction defaultWriterClosedPromiseInitialize(writer: WritableStreamDefaultWriter) {\n  writer._closedPromise = newPromise((resolve, reject) => {\n    writer._closedPromise_resolve = resolve;\n    writer._closedPromise_reject = reject;\n    writer._closedPromiseState = 'pending';\n  });\n}\n\nfunction defaultWriterClosedPromiseInitializeAsRejected(writer: WritableStreamDefaultWriter, reason: any) {\n  defaultWriterClosedPromiseInitialize(writer);\n  defaultWriterClosedPromiseReject(writer, reason);\n}\n\nfunction defaultWriterClosedPromiseInitializeAsResolved(writer: WritableStreamDefaultWriter) {\n  defaultWriterClosedPromiseInitialize(writer);\n  defaultWriterClosedPromiseResolve(writer);\n}\n\nfunction defaultWriterClosedPromiseReject(writer: WritableStreamDefaultWriter, reason: any) {\n  if (writer._closedPromise_reject === undefined) {\n    return;\n  }\n  assert(writer._closedPromiseState === 'pending');\n\n  setPromiseIsHandledToTrue(writer._closedPromise);\n  writer._closedPromise_reject(reason);\n  writer._closedPromise_resolve = undefined;\n  writer._closedPromise_reject = undefined;\n  writer._closedPromiseState = 'rejected';\n}\n\nfunction defaultWriterClosedPromiseResetToRejected(writer: WritableStreamDefaultWriter, reason: any) {\n  assert(writer._closedPromise_resolve === undefined);\n  assert(writer._closedPromise_reject === undefined);\n  assert(writer._closedPromiseState !== 'pending');\n\n  defaultWriterClosedPromiseInitializeAsRejected(writer, reason);\n}\n\nfunction defaultWriterClosedPromiseResolve(writer: WritableStreamDefaultWriter) {\n  if (writer._closedPromise_resolve === undefined) {\n    return;\n  }\n  assert(writer._closedPromiseState === 'pending');\n\n  writer._closedPromise_resolve(undefined);\n  writer._closedPromise_resolve = undefined;\n  writer._closedPromise_reject = undefined;\n  writer._closedPromiseState = 'resolved';\n}\n\nfunction defaultWriterReadyPromiseInitialize(writer: WritableStreamDefaultWriter) {\n  writer._readyPromise = newPromise((resolve, reject) => {\n    writer._readyPromise_resolve = resolve;\n    writer._readyPromise_reject = reject;\n  });\n  writer._readyPromiseState = 'pending';\n}\n\nfunction defaultWriterReadyPromiseInitializeAsRejected(writer: WritableStreamDefaultWriter, reason: any) {\n  defaultWriterReadyPromiseInitialize(writer);\n  defaultWriterReadyPromiseReject(writer, reason);\n}\n\nfunction defaultWriterReadyPromiseInitializeAsResolved(writer: WritableStreamDefaultWriter) {\n  defaultWriterReadyPromiseInitialize(writer);\n  defaultWriterReadyPromiseResolve(writer);\n}\n\nfunction defaultWriterReadyPromiseReject(writer: WritableStreamDefaultWriter, reason: any) {\n  if (writer._readyPromise_reject === undefined) {\n    return;\n  }\n\n  setPromiseIsHandledToTrue(writer._readyPromise);\n  writer._readyPromise_reject(reason);\n  writer._readyPromise_resolve = undefined;\n  writer._readyPromise_reject = undefined;\n  writer._readyPromiseState = 'rejected';\n}\n\nfunction defaultWriterReadyPromiseReset(writer: WritableStreamDefaultWriter) {\n  assert(writer._readyPromise_resolve === undefined);\n  assert(writer._readyPromise_reject === undefined);\n\n  defaultWriterReadyPromiseInitialize(writer);\n}\n\nfunction defaultWriterReadyPromiseResetToRejected(writer: WritableStreamDefaultWriter, reason: any) {\n  assert(writer._readyPromise_resolve === undefined);\n  assert(writer._readyPromise_reject === undefined);\n\n  defaultWriterReadyPromiseInitializeAsRejected(writer, reason);\n}\n\nfunction defaultWriterReadyPromiseResolve(writer: WritableStreamDefaultWriter) {\n  if (writer._readyPromise_resolve === undefined) {\n    return;\n  }\n\n  writer._readyPromise_resolve(undefined);\n  writer._readyPromise_resolve = undefined;\n  writer._readyPromise_reject = undefined;\n  writer._readyPromiseState = 'fulfilled';\n}\n", "/// <reference lib=\"dom\" />\n\nfunction getGlobals(): typeof globalThis | undefined {\n  if (typeof globalThis !== 'undefined') {\n    return globalThis;\n  } else if (typeof self !== 'undefined') {\n    return self;\n  } else if (typeof global !== 'undefined') {\n    return global;\n  }\n  return undefined;\n}\n\nexport const globals = getGlobals();\n", "/// <reference types=\"node\" />\nimport { globals } from '../globals';\nimport { setFunctionName } from '../lib/helpers/miscellaneous';\n\ninterface DOMException extends Error {\n  name: string;\n  message: string;\n}\n\ntype DOMExceptionConstructor = new (message?: string, name?: string) => DOMException;\n\nfunction isDOMExceptionConstructor(ctor: unknown): ctor is DOMExceptionConstructor {\n  if (!(typeof ctor === 'function' || typeof ctor === 'object')) {\n    return false;\n  }\n  if ((ctor as DOMExceptionConstructor).name !== 'DOMException') {\n    return false;\n  }\n  try {\n    new (ctor as DOMExceptionConstructor)();\n    return true;\n  } catch {\n    return false;\n  }\n}\n\n/**\n * Support:\n * - Web browsers\n * - Node 18 and higher (https://github.com/nodejs/node/commit/e4b1fb5e6422c1ff151234bb9de792d45dd88d87)\n */\nfunction getFromGlobal(): DOMExceptionConstructor | undefined {\n  const ctor = globals?.DOMException;\n  return isDOMExceptionConstructor(ctor) ? ctor : undefined;\n}\n\n/**\n * Support:\n * - All platforms\n */\nfunction createPolyfill(): DOMExceptionConstructor {\n  // eslint-disable-next-line @typescript-eslint/no-shadow\n  const ctor = function DOMException(this: DOMException, message?: string, name?: string) {\n    this.message = message || '';\n    this.name = name || 'Error';\n    if (Error.captureStackTrace) {\n      Error.captureStackTrace(this, this.constructor);\n    }\n  } as any;\n  setFunctionName(ctor, 'DOMException');\n  ctor.prototype = Object.create(Error.prototype);\n  Object.defineProperty(ctor.prototype, 'constructor', { value: ctor, writable: true, configurable: true });\n  return ctor;\n}\n\n// eslint-disable-next-line @typescript-eslint/no-redeclare\nconst DOMException: DOMExceptionConstructor = getFromGlobal() || createPolyfill();\n\nexport { DOMException };\n", "import { IsReadableStream, IsReadableStreamLocked, ReadableStream, ReadableStreamCancel } from '../readable-stream';\nimport { AcquireReadableStreamDefaultReader, ReadableStreamDefaultReaderRead } from './default-reader';\nimport { ReadableStreamReaderGenericRelease } from './generic-reader';\nimport {\n  AcquireWritableStreamDefaultWriter,\n  IsWritableStream,\n  IsWritableStreamLocked,\n  WritableStream,\n  WritableStreamAbort,\n  WritableStreamCloseQueuedOrInFlight,\n  WritableStreamDefaultWriterCloseWithErrorPropagation,\n  WritableStreamDefaultWriterRelease,\n  WritableStreamDefaultWriterWrite\n} from '../writable-stream';\nimport assert from '../../stub/assert';\nimport {\n  newPromise,\n  PerformPromiseThen,\n  promiseResolvedWith,\n  setPromiseIsHandledToTrue,\n  uponFulfillment,\n  uponPromise,\n  uponRejection\n} from '../helpers/webidl';\nimport { noop } from '../../utils';\nimport { type AbortSignal, isAbortSignal } from '../abort-signal';\nimport { DOMException } from '../../stub/dom-exception';\n\nexport function ReadableStreamPipeTo<T>(source: ReadableStream<T>,\n                                        dest: WritableStream<T>,\n                                        preventClose: boolean,\n                                        preventAbort: boolean,\n                                        preventCancel: boolean,\n                                        signal: AbortSignal | undefined): Promise<undefined> {\n  assert(IsReadableStream(source));\n  assert(IsWritableStream(dest));\n  assert(typeof preventClose === 'boolean');\n  assert(typeof preventAbort === 'boolean');\n  assert(typeof preventCancel === 'boolean');\n  assert(signal === undefined || isAbortSignal(signal));\n  assert(!IsReadableStreamLocked(source));\n  assert(!IsWritableStreamLocked(dest));\n\n  const reader = AcquireReadableStreamDefaultReader<T>(source);\n  const writer = AcquireWritableStreamDefaultWriter<T>(dest);\n\n  source._disturbed = true;\n\n  let shuttingDown = false;\n\n  // This is used to keep track of the spec's requirement that we wait for ongoing writes during shutdown.\n  let currentWrite = promiseResolvedWith<void>(undefined);\n\n  return newPromise((resolve, reject) => {\n    let abortAlgorithm: () => void;\n    if (signal !== undefined) {\n      abortAlgorithm = () => {\n        const error = signal.reason !== undefined ? signal.reason : new DOMException('Aborted', 'AbortError');\n        const actions: Array<() => Promise<void>> = [];\n        if (!preventAbort) {\n          actions.push(() => {\n            if (dest._state === 'writable') {\n              return WritableStreamAbort(dest, error);\n            }\n            return promiseResolvedWith(undefined);\n          });\n        }\n        if (!preventCancel) {\n          actions.push(() => {\n            if (source._state === 'readable') {\n              return ReadableStreamCancel(source, error);\n            }\n            return promiseResolvedWith(undefined);\n          });\n        }\n        shutdownWithAction(() => Promise.all(actions.map(action => action())), true, error);\n      };\n\n      if (signal.aborted) {\n        abortAlgorithm();\n        return;\n      }\n\n      signal.addEventListener('abort', abortAlgorithm);\n    }\n\n    // Using reader and writer, read all chunks from this and write them to dest\n    // - Backpressure must be enforced\n    // - Shutdown must stop all activity\n    function pipeLoop() {\n      return newPromise<void>((resolveLoop, rejectLoop) => {\n        function next(done: boolean) {\n          if (done) {\n            resolveLoop();\n          } else {\n            // Use `PerformPromiseThen` instead of `uponPromise` to avoid\n            // adding unnecessary `.catch(rethrowAssertionErrorRejection)` handlers\n            PerformPromiseThen(pipeStep(), next, rejectLoop);\n          }\n        }\n\n        next(false);\n      });\n    }\n\n    function pipeStep(): Promise<boolean> {\n      if (shuttingDown) {\n        return promiseResolvedWith(true);\n      }\n\n      return PerformPromiseThen(writer._readyPromise, () => {\n        return newPromise<boolean>((resolveRead, rejectRead) => {\n          ReadableStreamDefaultReaderRead(\n            reader,\n            {\n              _chunkSteps: chunk => {\n                currentWrite = PerformPromiseThen(WritableStreamDefaultWriterWrite(writer, chunk), undefined, noop);\n                resolveRead(false);\n              },\n              _closeSteps: () => resolveRead(true),\n              _errorSteps: rejectRead\n            }\n          );\n        });\n      });\n    }\n\n    // Errors must be propagated forward\n    isOrBecomesErrored(source, reader._closedPromise, storedError => {\n      if (!preventAbort) {\n        shutdownWithAction(() => WritableStreamAbort(dest, storedError), true, storedError);\n      } else {\n        shutdown(true, storedError);\n      }\n      return null;\n    });\n\n    // Errors must be propagated backward\n    isOrBecomesErrored(dest, writer._closedPromise, storedError => {\n      if (!preventCancel) {\n        shutdownWithAction(() => ReadableStreamCancel(source, storedError), true, storedError);\n      } else {\n        shutdown(true, storedError);\n      }\n      return null;\n    });\n\n    // Closing must be propagated forward\n    isOrBecomesClosed(source, reader._closedPromise, () => {\n      if (!preventClose) {\n        shutdownWithAction(() => WritableStreamDefaultWriterCloseWithErrorPropagation(writer));\n      } else {\n        shutdown();\n      }\n      return null;\n    });\n\n    // Closing must be propagated backward\n    if (WritableStreamCloseQueuedOrInFlight(dest) || dest._state === 'closed') {\n      const destClosed = new TypeError('the destination writable stream closed before all data could be piped to it');\n\n      if (!preventCancel) {\n        shutdownWithAction(() => ReadableStreamCancel(source, destClosed), true, destClosed);\n      } else {\n        shutdown(true, destClosed);\n      }\n    }\n\n    setPromiseIsHandledToTrue(pipeLoop());\n\n    function waitForWritesToFinish(): Promise<void> {\n      // Another write may have started while we were waiting on this currentWrite, so we have to be sure to wait\n      // for that too.\n      const oldCurrentWrite = currentWrite;\n      return PerformPromiseThen(\n        currentWrite,\n        () => oldCurrentWrite !== currentWrite ? waitForWritesToFinish() : undefined\n      );\n    }\n\n    function isOrBecomesErrored(stream: ReadableStream | WritableStream,\n                                promise: Promise<void>,\n                                action: (reason: any) => null) {\n      if (stream._state === 'errored') {\n        action(stream._storedError);\n      } else {\n        uponRejection(promise, action);\n      }\n    }\n\n    function isOrBecomesClosed(stream: ReadableStream | WritableStream, promise: Promise<void>, action: () => null) {\n      if (stream._state === 'closed') {\n        action();\n      } else {\n        uponFulfillment(promise, action);\n      }\n    }\n\n    function shutdownWithAction(action: () => Promise<unknown>, originalIsError?: boolean, originalError?: any) {\n      if (shuttingDown) {\n        return;\n      }\n      shuttingDown = true;\n\n      if (dest._state === 'writable' && !WritableStreamCloseQueuedOrInFlight(dest)) {\n        uponFulfillment(waitForWritesToFinish(), doTheRest);\n      } else {\n        doTheRest();\n      }\n\n      function doTheRest(): null {\n        uponPromise(\n          action(),\n          () => finalize(originalIsError, originalError),\n          newError => finalize(true, newError)\n        );\n        return null;\n      }\n    }\n\n    function shutdown(isError?: boolean, error?: any) {\n      if (shuttingDown) {\n        return;\n      }\n      shuttingDown = true;\n\n      if (dest._state === 'writable' && !WritableStreamCloseQueuedOrInFlight(dest)) {\n        uponFulfillment(waitForWritesToFinish(), () => finalize(isError, error));\n      } else {\n        finalize(isError, error);\n      }\n    }\n\n    function finalize(isError?: boolean, error?: any): null {\n      WritableStreamDefaultWriterRelease(writer);\n      ReadableStreamReaderGenericRelease(reader);\n\n      if (signal !== undefined) {\n        signal.removeEventListener('abort', abortAlgorithm);\n      }\n      if (isError) {\n        reject(error);\n      } else {\n        resolve(undefined);\n      }\n\n      return null;\n    }\n  });\n}\n", "import type { QueuingStrategySizeCallback } from '../queuing-strategy';\nimport assert from '../../stub/assert';\nimport { DequeueValue, EnqueueValueWithSize, type QueuePair, ResetQueue } from '../abstract-ops/queue-with-sizes';\nimport {\n  ReadableStreamAddReadRequest,\n  ReadableStreamFulfillReadRequest,\n  ReadableStreamGetNumReadRequests,\n  type ReadRequest\n} from './default-reader';\nimport { SimpleQueue } from '../simple-queue';\nimport { IsReadableStreamLocked, ReadableStream, ReadableStreamClose, ReadableStreamError } from '../readable-stream';\nimport type { ValidatedUnderlyingSource } from './underlying-source';\nimport { setFunctionName, typeIsObject } from '../helpers/miscellaneous';\nimport { CancelSteps, PullSteps, ReleaseSteps } from '../abstract-ops/internal-methods';\nimport { promiseResolvedWith, uponPromise } from '../helpers/webidl';\n\n/**\n * Allows control of a {@link ReadableStream | readable stream}'s state and internal queue.\n *\n * @public\n */\nexport class ReadableStreamDefaultController<R> {\n  /** @internal */\n  _controlledReadableStream!: ReadableStream<R>;\n  /** @internal */\n  _queue!: SimpleQueue<QueuePair<R>>;\n  /** @internal */\n  _queueTotalSize!: number;\n  /** @internal */\n  _started!: boolean;\n  /** @internal */\n  _closeRequested!: boolean;\n  /** @internal */\n  _pullAgain!: boolean;\n  /** @internal */\n  _pulling !: boolean;\n  /** @internal */\n  _strategySizeAlgorithm!: QueuingStrategySizeCallback<R>;\n  /** @internal */\n  _strategyHWM!: number;\n  /** @internal */\n  _pullAlgorithm!: () => Promise<void>;\n  /** @internal */\n  _cancelAlgorithm!: (reason: any) => Promise<void>;\n\n  private constructor() {\n    throw new TypeError('Illegal constructor');\n  }\n\n  /**\n   * Returns the desired size to fill the controlled stream's internal queue. It can be negative, if the queue is\n   * over-full. An underlying source ought to use this information to determine when and how to apply backpressure.\n   */\n  get desiredSize(): number | null {\n    if (!IsReadableStreamDefaultController(this)) {\n      throw defaultControllerBrandCheckException('desiredSize');\n    }\n\n    return ReadableStreamDefaultControllerGetDesiredSize(this);\n  }\n\n  /**\n   * Closes the controlled readable stream. Consumers will still be able to read any previously-enqueued chunks from\n   * the stream, but once those are read, the stream will become closed.\n   */\n  close(): void {\n    if (!IsReadableStreamDefaultController(this)) {\n      throw defaultControllerBrandCheckException('close');\n    }\n\n    if (!ReadableStreamDefaultControllerCanCloseOrEnqueue(this)) {\n      throw new TypeError('The stream is not in a state that permits close');\n    }\n\n    ReadableStreamDefaultControllerClose(this);\n  }\n\n  /**\n   * Enqueues the given chunk `chunk` in the controlled readable stream.\n   */\n  enqueue(chunk: R): void;\n  enqueue(chunk: R = undefined!): void {\n    if (!IsReadableStreamDefaultController(this)) {\n      throw defaultControllerBrandCheckException('enqueue');\n    }\n\n    if (!ReadableStreamDefaultControllerCanCloseOrEnqueue(this)) {\n      throw new TypeError('The stream is not in a state that permits enqueue');\n    }\n\n    return ReadableStreamDefaultControllerEnqueue(this, chunk);\n  }\n\n  /**\n   * Errors the controlled readable stream, making all future interactions with it fail with the given error `e`.\n   */\n  error(e: any = undefined): void {\n    if (!IsReadableStreamDefaultController(this)) {\n      throw defaultControllerBrandCheckException('error');\n    }\n\n    ReadableStreamDefaultControllerError(this, e);\n  }\n\n  /** @internal */\n  [CancelSteps](reason: any): Promise<void> {\n    ResetQueue(this);\n    const result = this._cancelAlgorithm(reason);\n    ReadableStreamDefaultControllerClearAlgorithms(this);\n    return result;\n  }\n\n  /** @internal */\n  [PullSteps](readRequest: ReadRequest<R>): void {\n    const stream = this._controlledReadableStream;\n\n    if (this._queue.length > 0) {\n      const chunk = DequeueValue(this);\n\n      if (this._closeRequested && this._queue.length === 0) {\n        ReadableStreamDefaultControllerClearAlgorithms(this);\n        ReadableStreamClose(stream);\n      } else {\n        ReadableStreamDefaultControllerCallPullIfNeeded(this);\n      }\n\n      readRequest._chunkSteps(chunk);\n    } else {\n      ReadableStreamAddReadRequest(stream, readRequest);\n      ReadableStreamDefaultControllerCallPullIfNeeded(this);\n    }\n  }\n\n  /** @internal */\n  [ReleaseSteps](): void {\n    // Do nothing.\n  }\n}\n\nObject.defineProperties(ReadableStreamDefaultController.prototype, {\n  close: { enumerable: true },\n  enqueue: { enumerable: true },\n  error: { enumerable: true },\n  desiredSize: { enumerable: true }\n});\nsetFunctionName(ReadableStreamDefaultController.prototype.close, 'close');\nsetFunctionName(ReadableStreamDefaultController.prototype.enqueue, 'enqueue');\nsetFunctionName(ReadableStreamDefaultController.prototype.error, 'error');\nif (typeof Symbol.toStringTag === 'symbol') {\n  Object.defineProperty(ReadableStreamDefaultController.prototype, Symbol.toStringTag, {\n    value: 'ReadableStreamDefaultController',\n    configurable: true\n  });\n}\n\n// Abstract operations for the ReadableStreamDefaultController.\n\nfunction IsReadableStreamDefaultController<R = any>(x: any): x is ReadableStreamDefaultController<R> {\n  if (!typeIsObject(x)) {\n    return false;\n  }\n\n  if (!Object.prototype.hasOwnProperty.call(x, '_controlledReadableStream')) {\n    return false;\n  }\n\n  return x instanceof ReadableStreamDefaultController;\n}\n\nfunction ReadableStreamDefaultControllerCallPullIfNeeded(controller: ReadableStreamDefaultController<any>): void {\n  const shouldPull = ReadableStreamDefaultControllerShouldCallPull(controller);\n  if (!shouldPull) {\n    return;\n  }\n\n  if (controller._pulling) {\n    controller._pullAgain = true;\n    return;\n  }\n\n  assert(!controller._pullAgain);\n\n  controller._pulling = true;\n\n  const pullPromise = controller._pullAlgorithm();\n  uponPromise(\n    pullPromise,\n    () => {\n      controller._pulling = false;\n\n      if (controller._pullAgain) {\n        controller._pullAgain = false;\n        ReadableStreamDefaultControllerCallPullIfNeeded(controller);\n      }\n\n      return null;\n    },\n    e => {\n      ReadableStreamDefaultControllerError(controller, e);\n      return null;\n    }\n  );\n}\n\nfunction ReadableStreamDefaultControllerShouldCallPull(controller: ReadableStreamDefaultController<any>): boolean {\n  const stream = controller._controlledReadableStream;\n\n  if (!ReadableStreamDefaultControllerCanCloseOrEnqueue(controller)) {\n    return false;\n  }\n\n  if (!controller._started) {\n    return false;\n  }\n\n  if (IsReadableStreamLocked(stream) && ReadableStreamGetNumReadRequests(stream) > 0) {\n    return true;\n  }\n\n  const desiredSize = ReadableStreamDefaultControllerGetDesiredSize(controller);\n  assert(desiredSize !== null);\n  if (desiredSize! > 0) {\n    return true;\n  }\n\n  return false;\n}\n\nfunction ReadableStreamDefaultControllerClearAlgorithms(controller: ReadableStreamDefaultController<any>) {\n  controller._pullAlgorithm = undefined!;\n  controller._cancelAlgorithm = undefined!;\n  controller._strategySizeAlgorithm = undefined!;\n}\n\n// A client of ReadableStreamDefaultController may use these functions directly to bypass state check.\n\nexport function ReadableStreamDefaultControllerClose(controller: ReadableStreamDefaultController<any>) {\n  if (!ReadableStreamDefaultControllerCanCloseOrEnqueue(controller)) {\n    return;\n  }\n\n  const stream = controller._controlledReadableStream;\n\n  controller._closeRequested = true;\n\n  if (controller._queue.length === 0) {\n    ReadableStreamDefaultControllerClearAlgorithms(controller);\n    ReadableStreamClose(stream);\n  }\n}\n\nexport function ReadableStreamDefaultControllerEnqueue<R>(\n  controller: ReadableStreamDefaultController<R>,\n  chunk: R\n): void {\n  if (!ReadableStreamDefaultControllerCanCloseOrEnqueue(controller)) {\n    return;\n  }\n\n  const stream = controller._controlledReadableStream;\n\n  if (IsReadableStreamLocked(stream) && ReadableStreamGetNumReadRequests(stream) > 0) {\n    ReadableStreamFulfillReadRequest(stream, chunk, false);\n  } else {\n    let chunkSize;\n    try {\n      chunkSize = controller._strategySizeAlgorithm(chunk);\n    } catch (chunkSizeE) {\n      ReadableStreamDefaultControllerError(controller, chunkSizeE);\n      throw chunkSizeE;\n    }\n\n    try {\n      EnqueueValueWithSize(controller, chunk, chunkSize);\n    } catch (enqueueE) {\n      ReadableStreamDefaultControllerError(controller, enqueueE);\n      throw enqueueE;\n    }\n  }\n\n  ReadableStreamDefaultControllerCallPullIfNeeded(controller);\n}\n\nexport function ReadableStreamDefaultControllerError(controller: ReadableStreamDefaultController<any>, e: any) {\n  const stream = controller._controlledReadableStream;\n\n  if (stream._state !== 'readable') {\n    return;\n  }\n\n  ResetQueue(controller);\n\n  ReadableStreamDefaultControllerClearAlgorithms(controller);\n  ReadableStreamError(stream, e);\n}\n\nexport function ReadableStreamDefaultControllerGetDesiredSize(\n  controller: ReadableStreamDefaultController<any>\n): number | null {\n  const state = controller._controlledReadableStream._state;\n\n  if (state === 'errored') {\n    return null;\n  }\n  if (state === 'closed') {\n    return 0;\n  }\n\n  return controller._strategyHWM - controller._queueTotalSize;\n}\n\n// This is used in the implementation of TransformStream.\nexport function ReadableStreamDefaultControllerHasBackpressure(\n  controller: ReadableStreamDefaultController<any>\n): boolean {\n  if (ReadableStreamDefaultControllerShouldCallPull(controller)) {\n    return false;\n  }\n\n  return true;\n}\n\nexport function ReadableStreamDefaultControllerCanCloseOrEnqueue(\n  controller: ReadableStreamDefaultController<any>\n): boolean {\n  const state = controller._controlledReadableStream._state;\n\n  if (!controller._closeRequested && state === 'readable') {\n    return true;\n  }\n\n  return false;\n}\n\nexport function SetUpReadableStreamDefaultController<R>(stream: ReadableStream<R>,\n                                                        controller: ReadableStreamDefaultController<R>,\n                                                        startAlgorithm: () => void | PromiseLike<void>,\n                                                        pullAlgorithm: () => Promise<void>,\n                                                        cancelAlgorithm: (reason: any) => Promise<void>,\n                                                        highWaterMark: number,\n                                                        sizeAlgorithm: QueuingStrategySizeCallback<R>) {\n  assert(stream._readableStreamController === undefined);\n\n  controller._controlledReadableStream = stream;\n\n  controller._queue = undefined!;\n  controller._queueTotalSize = undefined!;\n  ResetQueue(controller);\n\n  controller._started = false;\n  controller._closeRequested = false;\n  controller._pullAgain = false;\n  controller._pulling = false;\n\n  controller._strategySizeAlgorithm = sizeAlgorithm;\n  controller._strategyHWM = highWaterMark;\n\n  controller._pullAlgorithm = pullAlgorithm;\n  controller._cancelAlgorithm = cancelAlgorithm;\n\n  stream._readableStreamController = controller;\n\n  const startResult = startAlgorithm();\n  uponPromise(\n    promiseResolvedWith(startResult),\n    () => {\n      controller._started = true;\n\n      assert(!controller._pulling);\n      assert(!controller._pullAgain);\n\n      ReadableStreamDefaultControllerCallPullIfNeeded(controller);\n      return null;\n    },\n    r => {\n      ReadableStreamDefaultControllerError(controller, r);\n      return null;\n    }\n  );\n}\n\nexport function SetUpReadableStreamDefaultControllerFromUnderlyingSource<R>(\n  stream: ReadableStream<R>,\n  underlyingSource: ValidatedUnderlyingSource<R>,\n  highWaterMark: number,\n  sizeAlgorithm: QueuingStrategySizeCallback<R>\n) {\n  const controller: ReadableStreamDefaultController<R> = Object.create(ReadableStreamDefaultController.prototype);\n\n  let startAlgorithm: () => void | PromiseLike<void>;\n  let pullAlgorithm: () => Promise<void>;\n  let cancelAlgorithm: (reason: any) => Promise<void>;\n\n  if (underlyingSource.start !== undefined) {\n    startAlgorithm = () => underlyingSource.start!(controller);\n  } else {\n    startAlgorithm = () => undefined;\n  }\n  if (underlyingSource.pull !== undefined) {\n    pullAlgorithm = () => underlyingSource.pull!(controller);\n  } else {\n    pullAlgorithm = () => promiseResolvedWith(undefined);\n  }\n  if (underlyingSource.cancel !== undefined) {\n    cancelAlgorithm = reason => underlyingSource.cancel!(reason);\n  } else {\n    cancelAlgorithm = () => promiseResolvedWith(undefined);\n  }\n\n  SetUpReadableStreamDefaultController(\n    stream, controller, startAlgorithm, pullAlgorithm, cancelAlgorithm, highWaterMark, sizeAlgorithm\n  );\n}\n\n// Helper functions for the ReadableStreamDefaultController.\n\nfunction defaultControllerBrandCheckException(name: string): TypeError {\n  return new TypeError(\n    `ReadableStreamDefaultController.prototype.${name} can only be used on a ReadableStreamDefaultController`);\n}\n", "import {\n  CreateReadableByteStream,\n  CreateReadableStream,\n  type DefaultReadableStream,\n  IsReadableStream,\n  type ReadableByteStream,\n  ReadableStream,\n  ReadableStreamCancel,\n  type ReadableStreamReader\n} from '../readable-stream';\nimport { ReadableStreamReaderGenericRelease } from './generic-reader';\nimport {\n  AcquireReadableStreamDefaultReader,\n  IsReadableStreamDefaultReader,\n  ReadableStreamDefaultReaderRead,\n  type ReadRequest\n} from './default-reader';\nimport {\n  AcquireReadableStreamBYOBReader,\n  IsReadableStreamBYOBReader,\n  ReadableStreamBYOBReaderRead,\n  type ReadIntoRequest\n} from './byob-reader';\nimport assert from '../../stub/assert';\nimport { newPromise, promiseResolvedWith, queueMicrotask, uponRejection } from '../helpers/webidl';\nimport {\n  ReadableStreamDefaultControllerClose,\n  ReadableStreamDefaultControllerEnqueue,\n  ReadableStreamDefaultControllerError\n} from './default-controller';\nimport {\n  IsReadableByteStreamController,\n  ReadableByteStreamControllerClose,\n  ReadableByteStreamControllerEnqueue,\n  ReadableByteStreamControllerError,\n  ReadableByteStreamControllerGetBYOBRequest,\n  ReadableByteStreamControllerRespond,\n  ReadableByteStreamControllerRespondWithNewView\n} from './byte-stream-controller';\nimport { CreateArrayFromList } from '../abstract-ops/ecmascript';\nimport { CloneAsUint8Array } from '../abstract-ops/miscellaneous';\nimport type { NonShared } from '../helpers/array-buffer-view';\n\nexport function ReadableStreamTee<R>(stream: ReadableStream<R>,\n                                     cloneForBranch2: boolean): [ReadableStream<R>, ReadableStream<R>] {\n  assert(IsReadableStream(stream));\n  assert(typeof cloneForBranch2 === 'boolean');\n  if (IsReadableByteStreamController(stream._readableStreamController)) {\n    return ReadableByteStreamTee(stream as unknown as ReadableByteStream) as\n      unknown as [ReadableStream<R>, ReadableStream<R>];\n  }\n  return ReadableStreamDefaultTee(stream, cloneForBranch2);\n}\n\nexport function ReadableStreamDefaultTee<R>(\n  stream: ReadableStream<R>,\n  cloneForBranch2: boolean\n): [DefaultReadableStream<R>, DefaultReadableStream<R>] {\n  assert(IsReadableStream(stream));\n  assert(typeof cloneForBranch2 === 'boolean');\n\n  const reader = AcquireReadableStreamDefaultReader<R>(stream);\n\n  let reading = false;\n  let readAgain = false;\n  let canceled1 = false;\n  let canceled2 = false;\n  let reason1: any;\n  let reason2: any;\n  let branch1: DefaultReadableStream<R>;\n  let branch2: DefaultReadableStream<R>;\n\n  let resolveCancelPromise: (value: undefined | Promise<undefined>) => void;\n  const cancelPromise = newPromise<undefined>(resolve => {\n    resolveCancelPromise = resolve;\n  });\n\n  function pullAlgorithm(): Promise<void> {\n    if (reading) {\n      readAgain = true;\n      return promiseResolvedWith(undefined);\n    }\n\n    reading = true;\n\n    const readRequest: ReadRequest<R> = {\n      _chunkSteps: chunk => {\n        // This needs to be delayed a microtask because it takes at least a microtask to detect errors (using\n        // reader._closedPromise below), and we want errors in stream to error both branches immediately. We cannot let\n        // successful synchronously-available reads get ahead of asynchronously-available errors.\n        queueMicrotask(() => {\n          readAgain = false;\n          const chunk1 = chunk;\n          const chunk2 = chunk;\n\n          // There is no way to access the cloning code right now in the reference implementation.\n          // If we add one then we'll need an implementation for serializable objects.\n          // if (!canceled2 && cloneForBranch2) {\n          //   chunk2 = StructuredDeserialize(StructuredSerialize(chunk2));\n          // }\n\n          if (!canceled1) {\n            ReadableStreamDefaultControllerEnqueue(branch1._readableStreamController, chunk1);\n          }\n          if (!canceled2) {\n            ReadableStreamDefaultControllerEnqueue(branch2._readableStreamController, chunk2);\n          }\n\n          reading = false;\n          if (readAgain) {\n            pullAlgorithm();\n          }\n        });\n      },\n      _closeSteps: () => {\n        reading = false;\n        if (!canceled1) {\n          ReadableStreamDefaultControllerClose(branch1._readableStreamController);\n        }\n        if (!canceled2) {\n          ReadableStreamDefaultControllerClose(branch2._readableStreamController);\n        }\n\n        if (!canceled1 || !canceled2) {\n          resolveCancelPromise(undefined);\n        }\n      },\n      _errorSteps: () => {\n        reading = false;\n      }\n    };\n    ReadableStreamDefaultReaderRead(reader, readRequest);\n\n    return promiseResolvedWith(undefined);\n  }\n\n  function cancel1Algorithm(reason: any): Promise<void> {\n    canceled1 = true;\n    reason1 = reason;\n    if (canceled2) {\n      const compositeReason = CreateArrayFromList([reason1, reason2]);\n      const cancelResult = ReadableStreamCancel(stream, compositeReason);\n      resolveCancelPromise(cancelResult);\n    }\n    return cancelPromise;\n  }\n\n  function cancel2Algorithm(reason: any): Promise<void> {\n    canceled2 = true;\n    reason2 = reason;\n    if (canceled1) {\n      const compositeReason = CreateArrayFromList([reason1, reason2]);\n      const cancelResult = ReadableStreamCancel(stream, compositeReason);\n      resolveCancelPromise(cancelResult);\n    }\n    return cancelPromise;\n  }\n\n  function startAlgorithm() {\n    // do nothing\n  }\n\n  branch1 = CreateReadableStream(startAlgorithm, pullAlgorithm, cancel1Algorithm);\n  branch2 = CreateReadableStream(startAlgorithm, pullAlgorithm, cancel2Algorithm);\n\n  uponRejection(reader._closedPromise, (r: any) => {\n    ReadableStreamDefaultControllerError(branch1._readableStreamController, r);\n    ReadableStreamDefaultControllerError(branch2._readableStreamController, r);\n    if (!canceled1 || !canceled2) {\n      resolveCancelPromise(undefined);\n    }\n    return null;\n  });\n\n  return [branch1, branch2];\n}\n\nexport function ReadableByteStreamTee(stream: ReadableByteStream): [ReadableByteStream, ReadableByteStream] {\n  assert(IsReadableStream(stream));\n  assert(IsReadableByteStreamController(stream._readableStreamController));\n\n  let reader: ReadableStreamReader<NonShared<Uint8Array>> = AcquireReadableStreamDefaultReader(stream);\n  let reading = false;\n  let readAgainForBranch1 = false;\n  let readAgainForBranch2 = false;\n  let canceled1 = false;\n  let canceled2 = false;\n  let reason1: any;\n  let reason2: any;\n  let branch1: ReadableByteStream;\n  let branch2: ReadableByteStream;\n\n  let resolveCancelPromise: (value: undefined | Promise<undefined>) => void;\n  const cancelPromise = newPromise<void>(resolve => {\n    resolveCancelPromise = resolve;\n  });\n\n  function forwardReaderError(thisReader: ReadableStreamReader<NonShared<Uint8Array>>) {\n    uponRejection(thisReader._closedPromise, r => {\n      if (thisReader !== reader) {\n        return null;\n      }\n      ReadableByteStreamControllerError(branch1._readableStreamController, r);\n      ReadableByteStreamControllerError(branch2._readableStreamController, r);\n      if (!canceled1 || !canceled2) {\n        resolveCancelPromise(undefined);\n      }\n      return null;\n    });\n  }\n\n  function pullWithDefaultReader() {\n    if (IsReadableStreamBYOBReader(reader)) {\n      assert(reader._readIntoRequests.length === 0);\n      ReadableStreamReaderGenericRelease(reader);\n\n      reader = AcquireReadableStreamDefaultReader(stream);\n      forwardReaderError(reader);\n    }\n\n    const readRequest: ReadRequest<NonShared<Uint8Array>> = {\n      _chunkSteps: chunk => {\n        // This needs to be delayed a microtask because it takes at least a microtask to detect errors (using\n        // reader._closedPromise below), and we want errors in stream to error both branches immediately. We cannot let\n        // successful synchronously-available reads get ahead of asynchronously-available errors.\n        queueMicrotask(() => {\n          readAgainForBranch1 = false;\n          readAgainForBranch2 = false;\n\n          const chunk1 = chunk;\n          let chunk2 = chunk;\n          if (!canceled1 && !canceled2) {\n            try {\n              chunk2 = CloneAsUint8Array(chunk);\n            } catch (cloneE) {\n              ReadableByteStreamControllerError(branch1._readableStreamController, cloneE);\n              ReadableByteStreamControllerError(branch2._readableStreamController, cloneE);\n              resolveCancelPromise(ReadableStreamCancel(stream, cloneE));\n              return;\n            }\n          }\n\n          if (!canceled1) {\n            ReadableByteStreamControllerEnqueue(branch1._readableStreamController, chunk1);\n          }\n          if (!canceled2) {\n            ReadableByteStreamControllerEnqueue(branch2._readableStreamController, chunk2);\n          }\n\n          reading = false;\n          if (readAgainForBranch1) {\n            pull1Algorithm();\n          } else if (readAgainForBranch2) {\n            pull2Algorithm();\n          }\n        });\n      },\n      _closeSteps: () => {\n        reading = false;\n        if (!canceled1) {\n          ReadableByteStreamControllerClose(branch1._readableStreamController);\n        }\n        if (!canceled2) {\n          ReadableByteStreamControllerClose(branch2._readableStreamController);\n        }\n        if (branch1._readableStreamController._pendingPullIntos.length > 0) {\n          ReadableByteStreamControllerRespond(branch1._readableStreamController, 0);\n        }\n        if (branch2._readableStreamController._pendingPullIntos.length > 0) {\n          ReadableByteStreamControllerRespond(branch2._readableStreamController, 0);\n        }\n        if (!canceled1 || !canceled2) {\n          resolveCancelPromise(undefined);\n        }\n      },\n      _errorSteps: () => {\n        reading = false;\n      }\n    };\n    ReadableStreamDefaultReaderRead(reader, readRequest);\n  }\n\n  function pullWithBYOBReader(view: NonShared<ArrayBufferView>, forBranch2: boolean) {\n    if (IsReadableStreamDefaultReader<NonShared<Uint8Array>>(reader)) {\n      assert(reader._readRequests.length === 0);\n      ReadableStreamReaderGenericRelease(reader);\n\n      reader = AcquireReadableStreamBYOBReader(stream);\n      forwardReaderError(reader);\n    }\n\n    const byobBranch = forBranch2 ? branch2 : branch1;\n    const otherBranch = forBranch2 ? branch1 : branch2;\n\n    const readIntoRequest: ReadIntoRequest<NonShared<ArrayBufferView>> = {\n      _chunkSteps: chunk => {\n        // This needs to be delayed a microtask because it takes at least a microtask to detect errors (using\n        // reader._closedPromise below), and we want errors in stream to error both branches immediately. We cannot let\n        // successful synchronously-available reads get ahead of asynchronously-available errors.\n        queueMicrotask(() => {\n          readAgainForBranch1 = false;\n          readAgainForBranch2 = false;\n\n          const byobCanceled = forBranch2 ? canceled2 : canceled1;\n          const otherCanceled = forBranch2 ? canceled1 : canceled2;\n\n          if (!otherCanceled) {\n            let clonedChunk;\n            try {\n              clonedChunk = CloneAsUint8Array(chunk);\n            } catch (cloneE) {\n              ReadableByteStreamControllerError(byobBranch._readableStreamController, cloneE);\n              ReadableByteStreamControllerError(otherBranch._readableStreamController, cloneE);\n              resolveCancelPromise(ReadableStreamCancel(stream, cloneE));\n              return;\n            }\n            if (!byobCanceled) {\n              ReadableByteStreamControllerRespondWithNewView(byobBranch._readableStreamController, chunk);\n            }\n            ReadableByteStreamControllerEnqueue(otherBranch._readableStreamController, clonedChunk);\n          } else if (!byobCanceled) {\n            ReadableByteStreamControllerRespondWithNewView(byobBranch._readableStreamController, chunk);\n          }\n\n          reading = false;\n          if (readAgainForBranch1) {\n            pull1Algorithm();\n          } else if (readAgainForBranch2) {\n            pull2Algorithm();\n          }\n        });\n      },\n      _closeSteps: chunk => {\n        reading = false;\n\n        const byobCanceled = forBranch2 ? canceled2 : canceled1;\n        const otherCanceled = forBranch2 ? canceled1 : canceled2;\n\n        if (!byobCanceled) {\n          ReadableByteStreamControllerClose(byobBranch._readableStreamController);\n        }\n        if (!otherCanceled) {\n          ReadableByteStreamControllerClose(otherBranch._readableStreamController);\n        }\n\n        if (chunk !== undefined) {\n          assert(chunk.byteLength === 0);\n\n          if (!byobCanceled) {\n            ReadableByteStreamControllerRespondWithNewView(byobBranch._readableStreamController, chunk);\n          }\n          if (!otherCanceled && otherBranch._readableStreamController._pendingPullIntos.length > 0) {\n            ReadableByteStreamControllerRespond(otherBranch._readableStreamController, 0);\n          }\n        }\n\n        if (!byobCanceled || !otherCanceled) {\n          resolveCancelPromise(undefined);\n        }\n      },\n      _errorSteps: () => {\n        reading = false;\n      }\n    };\n    ReadableStreamBYOBReaderRead(reader, view, 1, readIntoRequest);\n  }\n\n  function pull1Algorithm(): Promise<void> {\n    if (reading) {\n      readAgainForBranch1 = true;\n      return promiseResolvedWith(undefined);\n    }\n\n    reading = true;\n\n    const byobRequest = ReadableByteStreamControllerGetBYOBRequest(branch1._readableStreamController);\n    if (byobRequest === null) {\n      pullWithDefaultReader();\n    } else {\n      pullWithBYOBReader(byobRequest._view!, false);\n    }\n\n    return promiseResolvedWith(undefined);\n  }\n\n  function pull2Algorithm(): Promise<void> {\n    if (reading) {\n      readAgainForBranch2 = true;\n      return promiseResolvedWith(undefined);\n    }\n\n    reading = true;\n\n    const byobRequest = ReadableByteStreamControllerGetBYOBRequest(branch2._readableStreamController);\n    if (byobRequest === null) {\n      pullWithDefaultReader();\n    } else {\n      pullWithBYOBReader(byobRequest._view!, true);\n    }\n\n    return promiseResolvedWith(undefined);\n  }\n\n  function cancel1Algorithm(reason: any): Promise<void> {\n    canceled1 = true;\n    reason1 = reason;\n    if (canceled2) {\n      const compositeReason = CreateArrayFromList([reason1, reason2]);\n      const cancelResult = ReadableStreamCancel(stream, compositeReason);\n      resolveCancelPromise(cancelResult);\n    }\n    return cancelPromise;\n  }\n\n  function cancel2Algorithm(reason: any): Promise<void> {\n    canceled2 = true;\n    reason2 = reason;\n    if (canceled1) {\n      const compositeReason = CreateArrayFromList([reason1, reason2]);\n      const cancelResult = ReadableStreamCancel(stream, compositeReason);\n      resolveCancelPromise(cancelResult);\n    }\n    return cancelPromise;\n  }\n\n  function startAlgorithm(): void {\n    return;\n  }\n\n  branch1 = CreateReadableByteStream(startAlgorithm, pull1Algorithm, cancel1Algorithm);\n  branch2 = CreateReadableByteStream(startAlgorithm, pull2Algorithm, cancel2Algorithm);\n\n  forwardReaderError(reader);\n\n  return [branch1, branch2];\n}\n", "import { typeIsObject } from '../helpers/miscellaneous';\nimport type { ReadableStreamDefaultReadResult } from './default-reader';\n\n/**\n * A common interface for a `ReadadableStream` implementation.\n *\n * @public\n */\nexport interface ReadableStreamLike<R = any> {\n  readonly locked: boolean;\n\n  getReader(): ReadableStreamDefaultReaderLike<R>;\n}\n\n/**\n * A common interface for a `ReadableStreamDefaultReader` implementation.\n *\n * @public\n */\nexport interface ReadableStreamDefaultReaderLike<R = any> {\n  readonly closed: Promise<undefined>;\n\n  cancel(reason?: any): Promise<void>;\n\n  read(): Promise<ReadableStreamDefaultReadResult<R>>;\n\n  releaseLock(): void;\n}\n\nexport function isReadableStreamLike<R>(stream: unknown): stream is ReadableStreamLike<R> {\n  return typeIsObject(stream) && typeof (stream as ReadableStreamLike<R>).getReader !== 'undefined';\n}\n", "import { CreateReadableStream, type DefaultReadableStream } from '../readable-stream';\nimport {\n  isReadableStreamLike,\n  type ReadableStreamDefaultReaderLike,\n  type ReadableStreamLike\n} from './readable-stream-like';\nimport { ReadableStreamDefaultControllerClose, ReadableStreamDefaultControllerEnqueue } from './default-controller';\nimport { GetIterator, GetMethod, IteratorComplete, IteratorNext, IteratorValue } from '../abstract-ops/ecmascript';\nimport { promiseRejectedWith, promiseResolvedWith, reflectCall, transformPromiseWith } from '../helpers/webidl';\nimport { typeIsObject } from '../helpers/miscellaneous';\nimport { noop } from '../../utils';\n\nexport function ReadableStreamFrom<R>(\n  source: Iterable<R> | AsyncIterable<R> | ReadableStreamLike<R>\n): DefaultReadableStream<R> {\n  if (isReadableStreamLike(source)) {\n    return ReadableStreamFromDefaultReader(source.getReader());\n  }\n  return ReadableStreamFromIterable(source);\n}\n\nexport function ReadableStreamFromIterable<R>(asyncIterable: Iterable<R> | AsyncIterable<R>): DefaultReadableStream<R> {\n  let stream: DefaultReadableStream<R>;\n  const iteratorRecord = GetIterator(asyncIterable, 'async');\n\n  const startAlgorithm = noop;\n\n  function pullAlgorithm(): Promise<void> {\n    let nextResult;\n    try {\n      nextResult = IteratorNext(iteratorRecord);\n    } catch (e) {\n      return promiseRejectedWith(e);\n    }\n    const nextPromise = promiseResolvedWith(nextResult);\n    return transformPromiseWith(nextPromise, iterResult => {\n      if (!typeIsObject(iterResult)) {\n        throw new TypeError('The promise returned by the iterator.next() method must fulfill with an object');\n      }\n      const done = IteratorComplete(iterResult);\n      if (done) {\n        ReadableStreamDefaultControllerClose(stream._readableStreamController);\n      } else {\n        const value = IteratorValue(iterResult);\n        ReadableStreamDefaultControllerEnqueue(stream._readableStreamController, value);\n      }\n    });\n  }\n\n  function cancelAlgorithm(reason: any): Promise<void> {\n    const iterator = iteratorRecord.iterator;\n    let returnMethod: (typeof iterator)['return'] | undefined;\n    try {\n      returnMethod = GetMethod(iterator, 'return');\n    } catch (e) {\n      return promiseRejectedWith(e);\n    }\n    if (returnMethod === undefined) {\n      return promiseResolvedWith(undefined);\n    }\n    let returnResult: IteratorResult<R> | Promise<IteratorResult<R>>;\n    try {\n      returnResult = reflectCall(returnMethod, iterator, [reason]);\n    } catch (e) {\n      return promiseRejectedWith(e);\n    }\n    const returnPromise = promiseResolvedWith(returnResult);\n    return transformPromiseWith(returnPromise, iterResult => {\n      if (!typeIsObject(iterResult)) {\n        throw new TypeError('The promise returned by the iterator.return() method must fulfill with an object');\n      }\n      return undefined;\n    });\n  }\n\n  stream = CreateReadableStream(startAlgorithm, pullAlgorithm, cancelAlgorithm, 0);\n  return stream;\n}\n\nexport function ReadableStreamFromDefaultReader<R>(\n  reader: ReadableStreamDefaultReaderLike<R>\n): DefaultReadableStream<R> {\n  let stream: DefaultReadableStream<R>;\n\n  const startAlgorithm = noop;\n\n  function pullAlgorithm(): Promise<void> {\n    let readPromise;\n    try {\n      readPromise = reader.read();\n    } catch (e) {\n      return promiseRejectedWith(e);\n    }\n    return transformPromiseWith(readPromise, readResult => {\n      if (!typeIsObject(readResult)) {\n        throw new TypeError('The promise returned by the reader.read() method must fulfill with an object');\n      }\n      if (readResult.done) {\n        ReadableStreamDefaultControllerClose(stream._readableStreamController);\n      } else {\n        const value = readResult.value;\n        ReadableStreamDefaultControllerEnqueue(stream._readableStreamController, value);\n      }\n    });\n  }\n\n  function cancelAlgorithm(reason: any): Promise<void> {\n    try {\n      return promiseResolvedWith(reader.cancel(reason));\n    } catch (e) {\n      return promiseRejectedWith(e);\n    }\n  }\n\n  stream = CreateReadableStream(startAlgorithm, pullAlgorithm, cancelAlgorithm, 0);\n  return stream;\n}\n", "import { assertDictionary, assertFunction, convertUnsignedLongLongWithEnforceRange } from './basic';\nimport type {\n  ReadableStreamController,\n  UnderlyingByteSource,\n  UnderlyingDefaultOrByteSource,\n  UnderlyingDefaultOrByteSourcePullCallback,\n  UnderlyingDefaultOrByteSourceStartCallback,\n  UnderlyingSource,\n  UnderlyingSourceCancelCallback,\n  ValidatedUnderlyingDefaultOrByteSource\n} from '../readable-stream/underlying-source';\nimport { promiseCall, reflectCall } from '../helpers/webidl';\n\nexport function convertUnderlyingDefaultOrByteSource<R>(\n  source: UnderlyingSource<R> | UnderlyingByteSource | null,\n  context: string\n): ValidatedUnderlyingDefaultOrByteSource<R> {\n  assertDictionary(source, context);\n  const original = source as (UnderlyingDefaultOrByteSource<R> | null);\n  const autoAllocateChunkSize = original?.autoAllocateChunkSize;\n  const cancel = original?.cancel;\n  const pull = original?.pull;\n  const start = original?.start;\n  const type = original?.type;\n  return {\n    autoAllocateChunkSize: autoAllocateChunkSize === undefined ?\n      undefined :\n      convertUnsignedLongLongWithEnforceRange(\n        autoAllocateChunkSize,\n        `${context} has member 'autoAllocateChunkSize' that`\n      ),\n    cancel: cancel === undefined ?\n      undefined :\n      convertUnderlyingSourceCancelCallback(cancel, original!, `${context} has member 'cancel' that`),\n    pull: pull === undefined ?\n      undefined :\n      convertUnderlyingSourcePullCallback(pull, original!, `${context} has member 'pull' that`),\n    start: start === undefined ?\n      undefined :\n      convertUnderlyingSourceStartCallback(start, original!, `${context} has member 'start' that`),\n    type: type === undefined ? undefined : convertReadableStreamType(type, `${context} has member 'type' that`)\n  };\n}\n\nfunction convertUnderlyingSourceCancelCallback(\n  fn: UnderlyingSourceCancelCallback,\n  original: UnderlyingDefaultOrByteSource,\n  context: string\n): (reason: any) => Promise<void> {\n  assertFunction(fn, context);\n  return (reason: any) => promiseCall(fn, original, [reason]);\n}\n\nfunction convertUnderlyingSourcePullCallback<R>(\n  fn: UnderlyingDefaultOrByteSourcePullCallback<R>,\n  original: UnderlyingDefaultOrByteSource<R>,\n  context: string\n): (controller: ReadableStreamController<R>) => Promise<void> {\n  assertFunction(fn, context);\n  return (controller: ReadableStreamController<R>) => promiseCall(fn, original, [controller]);\n}\n\nfunction convertUnderlyingSourceStartCallback<R>(\n  fn: UnderlyingDefaultOrByteSourceStartCallback<R>,\n  original: UnderlyingDefaultOrByteSource<R>,\n  context: string\n): UnderlyingDefaultOrByteSourceStartCallback<R> {\n  assertFunction(fn, context);\n  return (controller: ReadableStreamController<R>) => reflectCall(fn, original, [controller]);\n}\n\nfunction convertReadableStreamType(type: string, context: string): 'bytes' {\n  type = `${type}`;\n  if (type !== 'bytes') {\n    throw new TypeError(`${context} '${type}' is not a valid enumeration value for ReadableStreamType`);\n  }\n  return type;\n}\n", "import { assertDictionary } from './basic';\nimport type {\n  ReadableStreamIteratorOptions,\n  ValidatedReadableStreamIteratorOptions\n} from '../readable-stream/iterator-options';\n\nexport function convertIteratorOptions(options: ReadableStreamIteratorOptions | null | undefined,\n                                       context: string): ValidatedReadableStreamIteratorOptions {\n  assertDictionary(options, context);\n  const preventCancel = options?.preventCancel;\n  return { preventCancel: Boolean(preventCancel) };\n}\n", "import { assertDictionary } from './basic';\nimport type { StreamPipeOptions, ValidatedStreamPipeOptions } from '../readable-stream/pipe-options';\nimport { type AbortSignal, isAbortSignal } from '../abort-signal';\n\nexport function convertPipeOptions(options: StreamPipeOptions | null | undefined,\n                                   context: string): ValidatedStreamPipeOptions {\n  assertDictionary(options, context);\n  const preventAbort = options?.preventAbort;\n  const preventCancel = options?.preventCancel;\n  const preventClose = options?.preventClose;\n  const signal = options?.signal;\n  if (signal !== undefined) {\n    assertAbortSignal(signal, `${context} has member 'signal' that`);\n  }\n  return {\n    preventAbort: <PERSON><PERSON><PERSON>(preventAbort),\n    preventCancel: <PERSON><PERSON>an(preventCancel),\n    preventClose: Boolean(preventClose),\n    signal\n  };\n}\n\nfunction assertAbortSignal(signal: unknown, context: string): asserts signal is AbortSignal {\n  if (!isAbortSignal(signal)) {\n    throw new TypeError(`${context} is not an AbortSignal.`);\n  }\n}\n", "import { assertDictionary, assertRequiredField } from './basic';\nimport { ReadableStream } from '../readable-stream';\nimport { WritableStream } from '../writable-stream';\nimport { assertReadableStream } from './readable-stream';\nimport { assertWritableStream } from './writable-stream';\n\nexport function convertReadableWritablePair<RS extends ReadableStream, WS extends WritableStream>(\n  pair: { readable: RS; writable: WS } | null | undefined,\n  context: string\n): { readable: RS; writable: WS } {\n  assertDictionary(pair, context);\n\n  const readable = pair?.readable;\n  assertRequiredField(readable, 'readable', 'ReadableWritablePair');\n  assertReadableStream(readable, `${context} has member 'readable' that`);\n\n  const writable = pair?.writable;\n  assertRequiredField(writable, 'writable', 'ReadableWritablePair');\n  assertWritableStream(writable, `${context} has member 'writable' that`);\n\n  return { readable, writable };\n}\n", "import assert from '../stub/assert';\nimport {\n  promiseRejectedWith,\n  promiseResolvedWith,\n  setPromiseIsHandledToTrue,\n  transformPromiseWith\n} from './helpers/webidl';\nimport type { QueuingStrategy, QueuingStrategySizeCallback } from './queuing-strategy';\nimport { AcquireReadableStreamAsyncIterator, type ReadableStreamAsyncIterator } from './readable-stream/async-iterator';\nimport { defaultReaderClosedPromiseReject, defaultReaderClosedPromiseResolve } from './readable-stream/generic-reader';\nimport {\n  AcquireReadableStreamDefaultReader,\n  IsReadableStreamDefaultReader,\n  ReadableStreamDefaultReader,\n  ReadableStreamDefaultReaderErrorReadRequests,\n  type ReadableStreamDefaultReadResult\n} from './readable-stream/default-reader';\nimport {\n  AcquireReadableStreamBYOBReader,\n  IsReadableStreamBYOBReader,\n  ReadableStreamBYOBReader,\n  ReadableStreamBYOBReaderErrorReadIntoRequests,\n  type ReadableStreamBYOBReadResult\n} from './readable-stream/byob-reader';\nimport { ReadableStreamPipeTo } from './readable-stream/pipe';\nimport { ReadableStreamTee } from './readable-stream/tee';\nimport { ReadableStreamFrom } from './readable-stream/from';\nimport { IsWritableStream, IsWritableStreamLocked, WritableStream } from './writable-stream';\nimport { SimpleQueue } from './simple-queue';\nimport {\n  ReadableByteStreamController,\n  ReadableStreamBYOBRequest,\n  SetUpReadableByteStreamController,\n  SetUpReadableByteStreamControllerFromUnderlyingSource\n} from './readable-stream/byte-stream-controller';\nimport {\n  ReadableStreamDefaultController,\n  SetUpReadableStreamDefaultController,\n  SetUpReadableStreamDefaultControllerFromUnderlyingSource\n} from './readable-stream/default-controller';\nimport type {\n  UnderlyingByteSource,\n  UnderlyingByteSourcePullCallback,\n  UnderlyingByteSourceStartCallback,\n  UnderlyingSource,\n  UnderlyingSourceCancelCallback,\n  UnderlyingSourcePullCallback,\n  UnderlyingSourceStartCallback\n} from './readable-stream/underlying-source';\nimport { noop } from '../utils';\nimport { setFunctionName, typeIsObject } from './helpers/miscellaneous';\nimport { CreateArrayFromList, SymbolAsyncIterator } from './abstract-ops/ecmascript';\nimport { CancelSteps } from './abstract-ops/internal-methods';\nimport { IsNonNegativeNumber } from './abstract-ops/miscellaneous';\nimport { assertObject, assertRequiredArgument } from './validators/basic';\nimport { convertQueuingStrategy } from './validators/queuing-strategy';\nimport { ExtractHighWaterMark, ExtractSizeAlgorithm } from './abstract-ops/queuing-strategy';\nimport { convertUnderlyingDefaultOrByteSource } from './validators/underlying-source';\nimport type {\n  ReadableStreamBYOBReaderReadOptions,\n  ReadableStreamGetReaderOptions\n} from './readable-stream/reader-options';\nimport { convertReaderOptions } from './validators/reader-options';\nimport type { StreamPipeOptions, ValidatedStreamPipeOptions } from './readable-stream/pipe-options';\nimport type { ReadableStreamIteratorOptions } from './readable-stream/iterator-options';\nimport { convertIteratorOptions } from './validators/iterator-options';\nimport { convertPipeOptions } from './validators/pipe-options';\nimport type { ReadableWritablePair } from './readable-stream/readable-writable-pair';\nimport { convertReadableWritablePair } from './validators/readable-writable-pair';\nimport type { ReadableStreamDefaultReaderLike, ReadableStreamLike } from './readable-stream/readable-stream-like';\nimport type { NonShared } from './helpers/array-buffer-view';\n\nexport type DefaultReadableStream<R = any> = ReadableStream<R> & {\n  _readableStreamController: ReadableStreamDefaultController<R>\n};\n\nexport type ReadableByteStream = ReadableStream<NonShared<Uint8Array>> & {\n  _readableStreamController: ReadableByteStreamController\n};\n\ntype ReadableStreamState = 'readable' | 'closed' | 'errored';\n\n/**\n * A readable stream represents a source of data, from which you can read.\n *\n * @public\n */\nexport class ReadableStream<R = any> implements AsyncIterable<R> {\n  /** @internal */\n  _state!: ReadableStreamState;\n  /** @internal */\n  _reader: ReadableStreamReader<R> | undefined;\n  /** @internal */\n  _storedError: any;\n  /** @internal */\n  _disturbed!: boolean;\n  /** @internal */\n  _readableStreamController!: ReadableStreamDefaultController<R> | ReadableByteStreamController;\n\n  constructor(underlyingSource: UnderlyingByteSource, strategy?: { highWaterMark?: number; size?: undefined });\n  constructor(underlyingSource?: UnderlyingSource<R>, strategy?: QueuingStrategy<R>);\n  constructor(rawUnderlyingSource: UnderlyingSource<R> | UnderlyingByteSource | null | undefined = {},\n              rawStrategy: QueuingStrategy<R> | null | undefined = {}) {\n    if (rawUnderlyingSource === undefined) {\n      rawUnderlyingSource = null;\n    } else {\n      assertObject(rawUnderlyingSource, 'First parameter');\n    }\n\n    const strategy = convertQueuingStrategy(rawStrategy, 'Second parameter');\n    const underlyingSource = convertUnderlyingDefaultOrByteSource(rawUnderlyingSource, 'First parameter');\n\n    InitializeReadableStream(this);\n\n    if (underlyingSource.type === 'bytes') {\n      if (strategy.size !== undefined) {\n        throw new RangeError('The strategy for a byte stream cannot have a size function');\n      }\n      const highWaterMark = ExtractHighWaterMark(strategy, 0);\n      SetUpReadableByteStreamControllerFromUnderlyingSource(\n        this as unknown as ReadableByteStream,\n        underlyingSource,\n        highWaterMark\n      );\n    } else {\n      assert(underlyingSource.type === undefined);\n      const sizeAlgorithm = ExtractSizeAlgorithm(strategy);\n      const highWaterMark = ExtractHighWaterMark(strategy, 1);\n      SetUpReadableStreamDefaultControllerFromUnderlyingSource(\n        this,\n        underlyingSource,\n        highWaterMark,\n        sizeAlgorithm\n      );\n    }\n  }\n\n  /**\n   * Whether or not the readable stream is locked to a {@link ReadableStreamDefaultReader | reader}.\n   */\n  get locked(): boolean {\n    if (!IsReadableStream(this)) {\n      throw streamBrandCheckException('locked');\n    }\n\n    return IsReadableStreamLocked(this);\n  }\n\n  /**\n   * Cancels the stream, signaling a loss of interest in the stream by a consumer.\n   *\n   * The supplied `reason` argument will be given to the underlying source's {@link UnderlyingSource.cancel | cancel()}\n   * method, which might or might not use it.\n   */\n  cancel(reason: any = undefined): Promise<void> {\n    if (!IsReadableStream(this)) {\n      return promiseRejectedWith(streamBrandCheckException('cancel'));\n    }\n\n    if (IsReadableStreamLocked(this)) {\n      return promiseRejectedWith(new TypeError('Cannot cancel a stream that already has a reader'));\n    }\n\n    return ReadableStreamCancel(this, reason);\n  }\n\n  /**\n   * Creates a {@link ReadableStreamBYOBReader} and locks the stream to the new reader.\n   *\n   * This call behaves the same way as the no-argument variant, except that it only works on readable byte streams,\n   * i.e. streams which were constructed specifically with the ability to handle \"bring your own buffer\" reading.\n   * The returned BYOB reader provides the ability to directly read individual chunks from the stream via its\n   * {@link ReadableStreamBYOBReader.read | read()} method, into developer-supplied buffers, allowing more precise\n   * control over allocation.\n   */\n  getReader({ mode }: { mode: 'byob' }): ReadableStreamBYOBReader;\n  /**\n   * Creates a {@link ReadableStreamDefaultReader} and locks the stream to the new reader.\n   * While the stream is locked, no other reader can be acquired until this one is released.\n   *\n   * This functionality is especially useful for creating abstractions that desire the ability to consume a stream\n   * in its entirety. By getting a reader for the stream, you can ensure nobody else can interleave reads with yours\n   * or cancel the stream, which would interfere with your abstraction.\n   */\n  getReader(): ReadableStreamDefaultReader<R>;\n  getReader(\n    rawOptions: ReadableStreamGetReaderOptions | null | undefined = undefined\n  ): ReadableStreamDefaultReader<R> | ReadableStreamBYOBReader {\n    if (!IsReadableStream(this)) {\n      throw streamBrandCheckException('getReader');\n    }\n\n    const options = convertReaderOptions(rawOptions, 'First parameter');\n\n    if (options.mode === undefined) {\n      return AcquireReadableStreamDefaultReader(this);\n    }\n\n    assert(options.mode === 'byob');\n    return AcquireReadableStreamBYOBReader(this as unknown as ReadableByteStream);\n  }\n\n  /**\n   * Provides a convenient, chainable way of piping this readable stream through a transform stream\n   * (or any other `{ writable, readable }` pair). It simply {@link ReadableStream.pipeTo | pipes} the stream\n   * into the writable side of the supplied pair, and returns the readable side for further use.\n   *\n   * Piping a stream will lock it for the duration of the pipe, preventing any other consumer from acquiring a reader.\n   */\n  pipeThrough<RS extends ReadableStream>(\n    transform: { readable: RS; writable: WritableStream<R> },\n    options?: StreamPipeOptions\n  ): RS;\n  pipeThrough<RS extends ReadableStream>(\n    rawTransform: { readable: RS; writable: WritableStream<R> } | null | undefined,\n    rawOptions: StreamPipeOptions | null | undefined = {}\n  ): RS {\n    if (!IsReadableStream(this)) {\n      throw streamBrandCheckException('pipeThrough');\n    }\n    assertRequiredArgument(rawTransform, 1, 'pipeThrough');\n\n    const transform = convertReadableWritablePair(rawTransform, 'First parameter');\n    const options = convertPipeOptions(rawOptions, 'Second parameter');\n\n    if (IsReadableStreamLocked(this)) {\n      throw new TypeError('ReadableStream.prototype.pipeThrough cannot be used on a locked ReadableStream');\n    }\n    if (IsWritableStreamLocked(transform.writable)) {\n      throw new TypeError('ReadableStream.prototype.pipeThrough cannot be used on a locked WritableStream');\n    }\n\n    const promise = ReadableStreamPipeTo(\n      this, transform.writable, options.preventClose, options.preventAbort, options.preventCancel, options.signal\n    );\n\n    setPromiseIsHandledToTrue(promise);\n\n    return transform.readable;\n  }\n\n  /**\n   * Pipes this readable stream to a given writable stream. The way in which the piping process behaves under\n   * various error conditions can be customized with a number of passed options. It returns a promise that fulfills\n   * when the piping process completes successfully, or rejects if any errors were encountered.\n   *\n   * Piping a stream will lock it for the duration of the pipe, preventing any other consumer from acquiring a reader.\n   */\n  pipeTo(destination: WritableStream<R>, options?: StreamPipeOptions): Promise<void>;\n  pipeTo(destination: WritableStream<R> | null | undefined,\n         rawOptions: StreamPipeOptions | null | undefined = {}): Promise<void> {\n    if (!IsReadableStream(this)) {\n      return promiseRejectedWith(streamBrandCheckException('pipeTo'));\n    }\n\n    if (destination === undefined) {\n      return promiseRejectedWith(`Parameter 1 is required in 'pipeTo'.`);\n    }\n    if (!IsWritableStream(destination)) {\n      return promiseRejectedWith(\n        new TypeError(`ReadableStream.prototype.pipeTo's first argument must be a WritableStream`)\n      );\n    }\n\n    let options: ValidatedStreamPipeOptions;\n    try {\n      options = convertPipeOptions(rawOptions, 'Second parameter');\n    } catch (e) {\n      return promiseRejectedWith(e);\n    }\n\n    if (IsReadableStreamLocked(this)) {\n      return promiseRejectedWith(\n        new TypeError('ReadableStream.prototype.pipeTo cannot be used on a locked ReadableStream')\n      );\n    }\n    if (IsWritableStreamLocked(destination)) {\n      return promiseRejectedWith(\n        new TypeError('ReadableStream.prototype.pipeTo cannot be used on a locked WritableStream')\n      );\n    }\n\n    return ReadableStreamPipeTo<R>(\n      this, destination, options.preventClose, options.preventAbort, options.preventCancel, options.signal\n    );\n  }\n\n  /**\n   * Tees this readable stream, returning a two-element array containing the two resulting branches as\n   * new {@link ReadableStream} instances.\n   *\n   * Teeing a stream will lock it, preventing any other consumer from acquiring a reader.\n   * To cancel the stream, cancel both of the resulting branches; a composite cancellation reason will then be\n   * propagated to the stream's underlying source.\n   *\n   * Note that the chunks seen in each branch will be the same object. If the chunks are not immutable,\n   * this could allow interference between the two branches.\n   */\n  tee(): [ReadableStream<R>, ReadableStream<R>] {\n    if (!IsReadableStream(this)) {\n      throw streamBrandCheckException('tee');\n    }\n\n    const branches = ReadableStreamTee(this, false);\n    return CreateArrayFromList(branches);\n  }\n\n  /**\n   * Asynchronously iterates over the chunks in the stream's internal queue.\n   *\n   * Asynchronously iterating over the stream will lock it, preventing any other consumer from acquiring a reader.\n   * The lock will be released if the async iterator's {@link ReadableStreamAsyncIterator.return | return()} method\n   * is called, e.g. by breaking out of the loop.\n   *\n   * By default, calling the async iterator's {@link ReadableStreamAsyncIterator.return | return()} method will also\n   * cancel the stream. To prevent this, use the stream's {@link ReadableStream.values | values()} method, passing\n   * `true` for the `preventCancel` option.\n   */\n  values(options?: ReadableStreamIteratorOptions): ReadableStreamAsyncIterator<R>;\n  values(rawOptions: ReadableStreamIteratorOptions | null | undefined = undefined): ReadableStreamAsyncIterator<R> {\n    if (!IsReadableStream(this)) {\n      throw streamBrandCheckException('values');\n    }\n\n    const options = convertIteratorOptions(rawOptions, 'First parameter');\n    return AcquireReadableStreamAsyncIterator<R>(this, options.preventCancel);\n  }\n\n  /**\n   * {@inheritDoc ReadableStream.values}\n   */\n  [Symbol.asyncIterator](options?: ReadableStreamIteratorOptions): ReadableStreamAsyncIterator<R>;\n\n  [SymbolAsyncIterator](options?: ReadableStreamIteratorOptions): ReadableStreamAsyncIterator<R> {\n    // Stub implementation, overridden below\n    return this.values(options);\n  }\n\n  /**\n   * Creates a new ReadableStream wrapping the provided iterable or async iterable.\n   *\n   * This can be used to adapt various kinds of objects into a readable stream,\n   * such as an array, an async generator, or a Node.js readable stream.\n   */\n  static from<R>(asyncIterable: Iterable<R> | AsyncIterable<R> | ReadableStreamLike<R>): ReadableStream<R> {\n    return ReadableStreamFrom(asyncIterable);\n  }\n}\n\nObject.defineProperties(ReadableStream, {\n  from: { enumerable: true }\n});\nObject.defineProperties(ReadableStream.prototype, {\n  cancel: { enumerable: true },\n  getReader: { enumerable: true },\n  pipeThrough: { enumerable: true },\n  pipeTo: { enumerable: true },\n  tee: { enumerable: true },\n  values: { enumerable: true },\n  locked: { enumerable: true }\n});\nsetFunctionName(ReadableStream.from, 'from');\nsetFunctionName(ReadableStream.prototype.cancel, 'cancel');\nsetFunctionName(ReadableStream.prototype.getReader, 'getReader');\nsetFunctionName(ReadableStream.prototype.pipeThrough, 'pipeThrough');\nsetFunctionName(ReadableStream.prototype.pipeTo, 'pipeTo');\nsetFunctionName(ReadableStream.prototype.tee, 'tee');\nsetFunctionName(ReadableStream.prototype.values, 'values');\nif (typeof Symbol.toStringTag === 'symbol') {\n  Object.defineProperty(ReadableStream.prototype, Symbol.toStringTag, {\n    value: 'ReadableStream',\n    configurable: true\n  });\n}\nObject.defineProperty(ReadableStream.prototype, SymbolAsyncIterator, {\n  value: ReadableStream.prototype.values,\n  writable: true,\n  configurable: true\n});\n\nexport type {\n  ReadableStreamAsyncIterator,\n  ReadableStreamDefaultReadResult,\n  ReadableStreamBYOBReadResult,\n  ReadableStreamBYOBReaderReadOptions,\n  UnderlyingByteSource,\n  UnderlyingSource,\n  UnderlyingSourceStartCallback,\n  UnderlyingSourcePullCallback,\n  UnderlyingSourceCancelCallback,\n  UnderlyingByteSourceStartCallback,\n  UnderlyingByteSourcePullCallback,\n  StreamPipeOptions,\n  ReadableWritablePair,\n  ReadableStreamIteratorOptions,\n  ReadableStreamLike,\n  ReadableStreamDefaultReaderLike\n};\n\n// Abstract operations for the ReadableStream.\n\n// Throws if and only if startAlgorithm throws.\nexport function CreateReadableStream<R>(\n  startAlgorithm: () => void | PromiseLike<void>,\n  pullAlgorithm: () => Promise<void>,\n  cancelAlgorithm: (reason: any) => Promise<void>,\n  highWaterMark = 1,\n  sizeAlgorithm: QueuingStrategySizeCallback<R> = () => 1\n): DefaultReadableStream<R> {\n  assert(IsNonNegativeNumber(highWaterMark));\n\n  const stream: DefaultReadableStream<R> = Object.create(ReadableStream.prototype);\n  InitializeReadableStream(stream);\n\n  const controller: ReadableStreamDefaultController<R> = Object.create(ReadableStreamDefaultController.prototype);\n  SetUpReadableStreamDefaultController(\n    stream, controller, startAlgorithm, pullAlgorithm, cancelAlgorithm, highWaterMark, sizeAlgorithm\n  );\n\n  return stream;\n}\n\n// Throws if and only if startAlgorithm throws.\nexport function CreateReadableByteStream(\n  startAlgorithm: () => void | PromiseLike<void>,\n  pullAlgorithm: () => Promise<void>,\n  cancelAlgorithm: (reason: any) => Promise<void>\n): ReadableByteStream {\n  const stream: ReadableByteStream = Object.create(ReadableStream.prototype);\n  InitializeReadableStream(stream);\n\n  const controller: ReadableByteStreamController = Object.create(ReadableByteStreamController.prototype);\n  SetUpReadableByteStreamController(stream, controller, startAlgorithm, pullAlgorithm, cancelAlgorithm, 0, undefined);\n\n  return stream;\n}\n\nfunction InitializeReadableStream(stream: ReadableStream) {\n  stream._state = 'readable';\n  stream._reader = undefined;\n  stream._storedError = undefined;\n  stream._disturbed = false;\n}\n\nexport function IsReadableStream(x: unknown): x is ReadableStream {\n  if (!typeIsObject(x)) {\n    return false;\n  }\n\n  if (!Object.prototype.hasOwnProperty.call(x, '_readableStreamController')) {\n    return false;\n  }\n\n  return x instanceof ReadableStream;\n}\n\nexport function IsReadableStreamDisturbed(stream: ReadableStream): boolean {\n  assert(IsReadableStream(stream));\n\n  return stream._disturbed;\n}\n\nexport function IsReadableStreamLocked(stream: ReadableStream): boolean {\n  assert(IsReadableStream(stream));\n\n  if (stream._reader === undefined) {\n    return false;\n  }\n\n  return true;\n}\n\n// ReadableStream API exposed for controllers.\n\nexport function ReadableStreamCancel<R>(stream: ReadableStream<R>, reason: any): Promise<undefined> {\n  stream._disturbed = true;\n\n  if (stream._state === 'closed') {\n    return promiseResolvedWith(undefined);\n  }\n  if (stream._state === 'errored') {\n    return promiseRejectedWith(stream._storedError);\n  }\n\n  ReadableStreamClose(stream);\n\n  const reader = stream._reader;\n  if (reader !== undefined && IsReadableStreamBYOBReader(reader)) {\n    const readIntoRequests = reader._readIntoRequests;\n    reader._readIntoRequests = new SimpleQueue();\n    readIntoRequests.forEach(readIntoRequest => {\n      readIntoRequest._closeSteps(undefined);\n    });\n  }\n\n  const sourceCancelPromise = stream._readableStreamController[CancelSteps](reason);\n  return transformPromiseWith(sourceCancelPromise, noop);\n}\n\nexport function ReadableStreamClose<R>(stream: ReadableStream<R>): void {\n  assert(stream._state === 'readable');\n\n  stream._state = 'closed';\n\n  const reader = stream._reader;\n\n  if (reader === undefined) {\n    return;\n  }\n\n  defaultReaderClosedPromiseResolve(reader);\n\n  if (IsReadableStreamDefaultReader<R>(reader)) {\n    const readRequests = reader._readRequests;\n    reader._readRequests = new SimpleQueue();\n    readRequests.forEach(readRequest => {\n      readRequest._closeSteps();\n    });\n  }\n}\n\nexport function ReadableStreamError<R>(stream: ReadableStream<R>, e: any): void {\n  assert(IsReadableStream(stream));\n  assert(stream._state === 'readable');\n\n  stream._state = 'errored';\n  stream._storedError = e;\n\n  const reader = stream._reader;\n\n  if (reader === undefined) {\n    return;\n  }\n\n  defaultReaderClosedPromiseReject(reader, e);\n\n  if (IsReadableStreamDefaultReader<R>(reader)) {\n    ReadableStreamDefaultReaderErrorReadRequests(reader, e);\n  } else {\n    assert(IsReadableStreamBYOBReader(reader));\n    ReadableStreamBYOBReaderErrorReadIntoRequests(reader, e);\n  }\n}\n\n// Readers\n\nexport type ReadableStreamReader<R> = ReadableStreamDefaultReader<R> | ReadableStreamBYOBReader;\n\nexport {\n  ReadableStreamDefaultReader,\n  ReadableStreamBYOBReader\n};\n\n// Controllers\n\nexport {\n  ReadableStreamDefaultController,\n  ReadableStreamBYOBRequest,\n  ReadableByteStreamController\n};\n\n// Helper functions for the ReadableStream.\n\nfunction streamBrandCheckException(name: string): TypeError {\n  return new TypeError(`ReadableStream.prototype.${name} can only be used on a ReadableStream`);\n}\n", "import type { QueuingStrategyInit } from '../queuing-strategy';\nimport { assertDictionary, assertRequiredField, convertUnrestrictedDouble } from './basic';\n\nexport function convertQueuingStrategyInit(init: QueuingStrategyInit | null | undefined,\n                                           context: string): QueuingStrategyInit {\n  assertDictionary(init, context);\n  const highWaterMark = init?.highWaterMark;\n  assertRequiredField(highWaterMark, 'highWaterMark', 'QueuingStrategyInit');\n  return {\n    highWaterMark: convertUnrestrictedDouble(highWaterMark)\n  };\n}\n", "import type { QueuingStrategy, QueuingStrategyInit } from './queuing-strategy';\nimport { setFunctionName, typeIsObject } from './helpers/miscellaneous';\nimport { assertRequiredArgument } from './validators/basic';\nimport { convertQueuingStrategyInit } from './validators/queuing-strategy-init';\n\n// The size function must not have a prototype property nor be a constructor\nconst byteLengthSizeFunction = (chunk: ArrayBufferView): number => {\n  return chunk.byteLength;\n};\nsetFunctionName(byteLengthSizeFunction, 'size');\n\n/**\n * A queuing strategy that counts the number of bytes in each chunk.\n *\n * @public\n */\nexport default class ByteLengthQueuingStrategy implements QueuingStrategy<ArrayBufferView> {\n  /** @internal */\n  readonly _byteLengthQueuingStrategyHighWaterMark: number;\n\n  constructor(options: QueuingStrategyInit) {\n    assertRequiredArgument(options, 1, 'ByteLengthQueuingStrategy');\n    options = convertQueuingStrategyInit(options, 'First parameter');\n    this._byteLengthQueuingStrategyHighWaterMark = options.highWaterMark;\n  }\n\n  /**\n   * Returns the high water mark provided to the constructor.\n   */\n  get highWaterMark(): number {\n    if (!IsByteLengthQueuingStrategy(this)) {\n      throw byteLengthBrandCheckException('highWaterMark');\n    }\n    return this._byteLengthQueuingStrategyHighWaterMark;\n  }\n\n  /**\n   * Measures the size of `chunk` by returning the value of its `byteLength` property.\n   */\n  get size(): (chunk: ArrayBufferView) => number {\n    if (!IsByteLengthQueuingStrategy(this)) {\n      throw byteLengthBrandCheckException('size');\n    }\n    return byteLengthSizeFunction;\n  }\n}\n\nObject.defineProperties(ByteLengthQueuingStrategy.prototype, {\n  highWaterMark: { enumerable: true },\n  size: { enumerable: true }\n});\nif (typeof Symbol.toStringTag === 'symbol') {\n  Object.defineProperty(ByteLengthQueuingStrategy.prototype, Symbol.toStringTag, {\n    value: 'ByteLengthQueuingStrategy',\n    configurable: true\n  });\n}\n\n// Helper functions for the ByteLengthQueuingStrategy.\n\nfunction byteLengthBrandCheckException(name: string): TypeError {\n  return new TypeError(`ByteLengthQueuingStrategy.prototype.${name} can only be used on a ByteLengthQueuingStrategy`);\n}\n\nexport function IsByteLengthQueuingStrategy(x: any): x is ByteLengthQueuingStrategy {\n  if (!typeIsObject(x)) {\n    return false;\n  }\n\n  if (!Object.prototype.hasOwnProperty.call(x, '_byteLengthQueuingStrategyHighWaterMark')) {\n    return false;\n  }\n\n  return x instanceof ByteLengthQueuingStrategy;\n}\n", "import type { QueuingStrategy, QueuingStrategyInit } from './queuing-strategy';\nimport { setFunctionName, typeIsObject } from './helpers/miscellaneous';\nimport { assertRequiredArgument } from './validators/basic';\nimport { convertQueuingStrategyInit } from './validators/queuing-strategy-init';\n\n// The size function must not have a prototype property nor be a constructor\nconst countSizeFunction = (): 1 => {\n  return 1;\n};\nsetFunctionName(countSizeFunction, 'size');\n\n/**\n * A queuing strategy that counts the number of chunks.\n *\n * @public\n */\nexport default class CountQueuingStrategy implements QueuingStrategy<any> {\n  /** @internal */\n  readonly _countQueuingStrategyHighWaterMark!: number;\n\n  constructor(options: QueuingStrategyInit) {\n    assertRequiredArgument(options, 1, 'CountQueuingStrategy');\n    options = convertQueuingStrategyInit(options, 'First parameter');\n    this._countQueuingStrategyHighWaterMark = options.highWaterMark;\n  }\n\n  /**\n   * Returns the high water mark provided to the constructor.\n   */\n  get highWaterMark(): number {\n    if (!IsCountQueuingStrategy(this)) {\n      throw countBrandCheckException('highWaterMark');\n    }\n    return this._countQueuingStrategyHighWaterMark;\n  }\n\n  /**\n   * Measures the size of `chunk` by always returning 1.\n   * This ensures that the total queue size is a count of the number of chunks in the queue.\n   */\n  get size(): (chunk: any) => 1 {\n    if (!IsCountQueuingStrategy(this)) {\n      throw countBrandCheckException('size');\n    }\n    return countSizeFunction;\n  }\n}\n\nObject.defineProperties(CountQueuingStrategy.prototype, {\n  highWaterMark: { enumerable: true },\n  size: { enumerable: true }\n});\nif (typeof Symbol.toStringTag === 'symbol') {\n  Object.defineProperty(CountQueuingStrategy.prototype, Symbol.toStringTag, {\n    value: 'CountQueuingStrategy',\n    configurable: true\n  });\n}\n\n// Helper functions for the CountQueuingStrategy.\n\nfunction countBrandCheckException(name: string): TypeError {\n  return new TypeError(`CountQueuingStrategy.prototype.${name} can only be used on a CountQueuingStrategy`);\n}\n\nexport function IsCountQueuingStrategy(x: any): x is CountQueuingStrategy {\n  if (!typeIsObject(x)) {\n    return false;\n  }\n\n  if (!Object.prototype.hasOwnProperty.call(x, '_countQueuingStrategyHighWaterMark')) {\n    return false;\n  }\n\n  return x instanceof CountQueuingStrategy;\n}\n", "import { assertDictionary, assertFunction } from './basic';\nimport { promiseCall, reflectCall } from '../helpers/webidl';\nimport type {\n  Transformer,\n  TransformerCancelCallback,\n  TransformerFlushCallback,\n  TransformerStartCallback,\n  TransformerTransformCallback,\n  ValidatedTransformer\n} from '../transform-stream/transformer';\nimport { TransformStreamDefaultController } from '../transform-stream';\n\nexport function convertTransformer<I, O>(original: Transformer<I, O> | null,\n                                         context: string): ValidatedTransformer<I, O> {\n  assertDictionary(original, context);\n  const cancel = original?.cancel;\n  const flush = original?.flush;\n  const readableType = original?.readableType;\n  const start = original?.start;\n  const transform = original?.transform;\n  const writableType = original?.writableType;\n  return {\n    cancel: cancel === undefined ?\n      undefined :\n      convertTransformerCancelCallback(cancel, original!, `${context} has member 'cancel' that`),\n    flush: flush === undefined ?\n      undefined :\n      convertTransformerFlushCallback(flush, original!, `${context} has member 'flush' that`),\n    readableType,\n    start: start === undefined ?\n      undefined :\n      convertTransformerStartCallback(start, original!, `${context} has member 'start' that`),\n    transform: transform === undefined ?\n      undefined :\n      convertTransformerTransformCallback(transform, original!, `${context} has member 'transform' that`),\n    writableType\n  };\n}\n\nfunction convertTransformerFlushCallback<I, O>(\n  fn: TransformerFlushCallback<O>,\n  original: Transformer<I, O>,\n  context: string\n): (controller: TransformStreamDefaultController<O>) => Promise<void> {\n  assertFunction(fn, context);\n  return (controller: TransformStreamDefaultController<O>) => promiseCall(fn, original, [controller]);\n}\n\nfunction convertTransformerStartCallback<I, O>(\n  fn: TransformerStartCallback<O>,\n  original: Transformer<I, O>,\n  context: string\n): TransformerStartCallback<O> {\n  assertFunction(fn, context);\n  return (controller: TransformStreamDefaultController<O>) => reflectCall(fn, original, [controller]);\n}\n\nfunction convertTransformerTransformCallback<I, O>(\n  fn: TransformerTransformCallback<I, O>,\n  original: Transformer<I, O>,\n  context: string\n): (chunk: I, controller: TransformStreamDefaultController<O>) => Promise<void> {\n  assertFunction(fn, context);\n  return (chunk: I, controller: TransformStreamDefaultController<O>) => promiseCall(fn, original, [chunk, controller]);\n}\n\nfunction convertTransformerCancelCallback<I, O>(\n  fn: TransformerCancelCallback,\n  original: Transformer<I, O>,\n  context: string\n): (reason: any) => Promise<void> {\n  assertFunction(fn, context);\n  return (reason: any) => promiseCall(fn, original, [reason]);\n}\n", "import assert from '../stub/assert';\nimport {\n  newPromise,\n  promiseRejectedWith,\n  promiseResolvedWith,\n  setPromiseIsHandledToTrue,\n  transformPromiseWith,\n  uponPromise\n} from './helpers/webidl';\nimport { CreateReadableStream, type DefaultReadableStream, ReadableStream } from './readable-stream';\nimport {\n  ReadableStreamDefaultControllerCanCloseOrEnqueue,\n  ReadableStreamDefaultControllerClose,\n  ReadableStreamDefaultControllerEnqueue,\n  ReadableStreamDefaultControllerError,\n  ReadableStreamDefaultControllerGetDesiredSize,\n  ReadableStreamDefaultControllerHasBackpressure\n} from './readable-stream/default-controller';\nimport type { QueuingStrategy, QueuingStrategySizeCallback } from './queuing-strategy';\nimport { CreateWritableStream, WritableStream, WritableStreamDefaultControllerErrorIfNeeded } from './writable-stream';\nimport { setFunctionName, typeIsObject } from './helpers/miscellaneous';\nimport { IsNonNegativeNumber } from './abstract-ops/miscellaneous';\nimport { convertQueuingStrategy } from './validators/queuing-strategy';\nimport { ExtractHighWaterMark, ExtractSizeAlgorithm } from './abstract-ops/queuing-strategy';\nimport type {\n  Transformer,\n  TransformerCancelCallback,\n  TransformerFlushCallback,\n  TransformerStartCallback,\n  TransformerTransformCallback,\n  ValidatedTransformer\n} from './transform-stream/transformer';\nimport { convertTransformer } from './validators/transformer';\n\n// Class TransformStream\n\n/**\n * A transform stream consists of a pair of streams: a {@link WritableStream | writable stream},\n * known as its writable side, and a {@link ReadableStream | readable stream}, known as its readable side.\n * In a manner specific to the transform stream in question, writes to the writable side result in new data being\n * made available for reading from the readable side.\n *\n * @public\n */\nexport class TransformStream<I = any, O = any> {\n  /** @internal */\n  _writable!: WritableStream<I>;\n  /** @internal */\n  _readable!: DefaultReadableStream<O>;\n  /** @internal */\n  _backpressure!: boolean;\n  /** @internal */\n  _backpressureChangePromise!: Promise<void>;\n  /** @internal */\n  _backpressureChangePromise_resolve!: () => void;\n  /** @internal */\n  _transformStreamController!: TransformStreamDefaultController<O>;\n\n  constructor(\n    transformer?: Transformer<I, O>,\n    writableStrategy?: QueuingStrategy<I>,\n    readableStrategy?: QueuingStrategy<O>\n  );\n  constructor(rawTransformer: Transformer<I, O> | null | undefined = {},\n              rawWritableStrategy: QueuingStrategy<I> | null | undefined = {},\n              rawReadableStrategy: QueuingStrategy<O> | null | undefined = {}) {\n    if (rawTransformer === undefined) {\n      rawTransformer = null;\n    }\n\n    const writableStrategy = convertQueuingStrategy(rawWritableStrategy, 'Second parameter');\n    const readableStrategy = convertQueuingStrategy(rawReadableStrategy, 'Third parameter');\n\n    const transformer = convertTransformer(rawTransformer, 'First parameter');\n    if (transformer.readableType !== undefined) {\n      throw new RangeError('Invalid readableType specified');\n    }\n    if (transformer.writableType !== undefined) {\n      throw new RangeError('Invalid writableType specified');\n    }\n\n    const readableHighWaterMark = ExtractHighWaterMark(readableStrategy, 0);\n    const readableSizeAlgorithm = ExtractSizeAlgorithm(readableStrategy);\n    const writableHighWaterMark = ExtractHighWaterMark(writableStrategy, 1);\n    const writableSizeAlgorithm = ExtractSizeAlgorithm(writableStrategy);\n\n    let startPromise_resolve!: (value: void | PromiseLike<void>) => void;\n    const startPromise = newPromise<void>(resolve => {\n      startPromise_resolve = resolve;\n    });\n\n    InitializeTransformStream(\n      this, startPromise, writableHighWaterMark, writableSizeAlgorithm, readableHighWaterMark, readableSizeAlgorithm\n    );\n    SetUpTransformStreamDefaultControllerFromTransformer(this, transformer);\n\n    if (transformer.start !== undefined) {\n      startPromise_resolve(transformer.start(this._transformStreamController));\n    } else {\n      startPromise_resolve(undefined);\n    }\n  }\n\n  /**\n   * The readable side of the transform stream.\n   */\n  get readable(): ReadableStream<O> {\n    if (!IsTransformStream(this)) {\n      throw streamBrandCheckException('readable');\n    }\n\n    return this._readable;\n  }\n\n  /**\n   * The writable side of the transform stream.\n   */\n  get writable(): WritableStream<I> {\n    if (!IsTransformStream(this)) {\n      throw streamBrandCheckException('writable');\n    }\n\n    return this._writable;\n  }\n}\n\nObject.defineProperties(TransformStream.prototype, {\n  readable: { enumerable: true },\n  writable: { enumerable: true }\n});\nif (typeof Symbol.toStringTag === 'symbol') {\n  Object.defineProperty(TransformStream.prototype, Symbol.toStringTag, {\n    value: 'TransformStream',\n    configurable: true\n  });\n}\n\nexport type {\n  Transformer,\n  TransformerCancelCallback,\n  TransformerStartCallback,\n  TransformerFlushCallback,\n  TransformerTransformCallback\n};\n\n// Transform Stream Abstract Operations\n\nexport function CreateTransformStream<I, O>(startAlgorithm: () => void | PromiseLike<void>,\n                                            transformAlgorithm: (chunk: I) => Promise<void>,\n                                            flushAlgorithm: () => Promise<void>,\n                                            cancelAlgorithm: (reason: any) => Promise<void>,\n                                            writableHighWaterMark = 1,\n                                            writableSizeAlgorithm: QueuingStrategySizeCallback<I> = () => 1,\n                                            readableHighWaterMark = 0,\n                                            readableSizeAlgorithm: QueuingStrategySizeCallback<O> = () => 1) {\n  assert(IsNonNegativeNumber(writableHighWaterMark));\n  assert(IsNonNegativeNumber(readableHighWaterMark));\n\n  const stream: TransformStream<I, O> = Object.create(TransformStream.prototype);\n\n  let startPromise_resolve!: (value: void | PromiseLike<void>) => void;\n  const startPromise = newPromise<void>(resolve => {\n    startPromise_resolve = resolve;\n  });\n\n  InitializeTransformStream(stream, startPromise, writableHighWaterMark, writableSizeAlgorithm, readableHighWaterMark,\n                            readableSizeAlgorithm);\n\n  const controller: TransformStreamDefaultController<O> = Object.create(TransformStreamDefaultController.prototype);\n\n  SetUpTransformStreamDefaultController(stream, controller, transformAlgorithm, flushAlgorithm, cancelAlgorithm);\n\n  const startResult = startAlgorithm();\n  startPromise_resolve(startResult);\n  return stream;\n}\n\nfunction InitializeTransformStream<I, O>(stream: TransformStream<I, O>,\n                                         startPromise: Promise<void>,\n                                         writableHighWaterMark: number,\n                                         writableSizeAlgorithm: QueuingStrategySizeCallback<I>,\n                                         readableHighWaterMark: number,\n                                         readableSizeAlgorithm: QueuingStrategySizeCallback<O>) {\n  function startAlgorithm(): Promise<void> {\n    return startPromise;\n  }\n\n  function writeAlgorithm(chunk: I): Promise<void> {\n    return TransformStreamDefaultSinkWriteAlgorithm(stream, chunk);\n  }\n\n  function abortAlgorithm(reason: any): Promise<void> {\n    return TransformStreamDefaultSinkAbortAlgorithm(stream, reason);\n  }\n\n  function closeAlgorithm(): Promise<void> {\n    return TransformStreamDefaultSinkCloseAlgorithm(stream);\n  }\n\n  stream._writable = CreateWritableStream(startAlgorithm, writeAlgorithm, closeAlgorithm, abortAlgorithm,\n                                          writableHighWaterMark, writableSizeAlgorithm);\n\n  function pullAlgorithm(): Promise<void> {\n    return TransformStreamDefaultSourcePullAlgorithm(stream);\n  }\n\n  function cancelAlgorithm(reason: any): Promise<void> {\n    return TransformStreamDefaultSourceCancelAlgorithm(stream, reason);\n  }\n\n  stream._readable = CreateReadableStream(startAlgorithm, pullAlgorithm, cancelAlgorithm, readableHighWaterMark,\n                                          readableSizeAlgorithm);\n\n  // The [[backpressure]] slot is set to undefined so that it can be initialised by TransformStreamSetBackpressure.\n  stream._backpressure = undefined!;\n  stream._backpressureChangePromise = undefined!;\n  stream._backpressureChangePromise_resolve = undefined!;\n  TransformStreamSetBackpressure(stream, true);\n\n  stream._transformStreamController = undefined!;\n}\n\nfunction IsTransformStream(x: unknown): x is TransformStream {\n  if (!typeIsObject(x)) {\n    return false;\n  }\n\n  if (!Object.prototype.hasOwnProperty.call(x, '_transformStreamController')) {\n    return false;\n  }\n\n  return x instanceof TransformStream;\n}\n\n// This is a no-op if both sides are already errored.\nfunction TransformStreamError(stream: TransformStream, e: any) {\n  ReadableStreamDefaultControllerError(stream._readable._readableStreamController, e);\n  TransformStreamErrorWritableAndUnblockWrite(stream, e);\n}\n\nfunction TransformStreamErrorWritableAndUnblockWrite(stream: TransformStream, e: any) {\n  TransformStreamDefaultControllerClearAlgorithms(stream._transformStreamController);\n  WritableStreamDefaultControllerErrorIfNeeded(stream._writable._writableStreamController, e);\n  TransformStreamUnblockWrite(stream);\n}\n\nfunction TransformStreamUnblockWrite(stream: TransformStream) {\n  if (stream._backpressure) {\n    // Pretend that pull() was called to permit any pending write() calls to complete. TransformStreamSetBackpressure()\n    // cannot be called from enqueue() or pull() once the ReadableStream is errored, so this will will be the final time\n    // _backpressure is set.\n    TransformStreamSetBackpressure(stream, false);\n  }\n}\n\nfunction TransformStreamSetBackpressure(stream: TransformStream, backpressure: boolean) {\n  // Passes also when called during construction.\n  assert(stream._backpressure !== backpressure);\n\n  if (stream._backpressureChangePromise !== undefined) {\n    stream._backpressureChangePromise_resolve();\n  }\n\n  stream._backpressureChangePromise = newPromise(resolve => {\n    stream._backpressureChangePromise_resolve = resolve;\n  });\n\n  stream._backpressure = backpressure;\n}\n\n// Class TransformStreamDefaultController\n\n/**\n * Allows control of the {@link ReadableStream} and {@link WritableStream} of the associated {@link TransformStream}.\n *\n * @public\n */\nexport class TransformStreamDefaultController<O> {\n  /** @internal */\n  _controlledTransformStream: TransformStream<any, O>;\n  /** @internal */\n  _finishPromise: Promise<undefined> | undefined;\n  /** @internal */\n  _finishPromise_resolve?: (value?: undefined) => void;\n  /** @internal */\n  _finishPromise_reject?: (reason: any) => void;\n  /** @internal */\n  _transformAlgorithm: (chunk: any) => Promise<void>;\n  /** @internal */\n  _flushAlgorithm: () => Promise<void>;\n  /** @internal */\n  _cancelAlgorithm: (reason: any) => Promise<void>;\n\n  private constructor() {\n    throw new TypeError('Illegal constructor');\n  }\n\n  /**\n   * Returns the desired size to fill the readable side’s internal queue. It can be negative, if the queue is over-full.\n   */\n  get desiredSize(): number | null {\n    if (!IsTransformStreamDefaultController(this)) {\n      throw defaultControllerBrandCheckException('desiredSize');\n    }\n\n    const readableController = this._controlledTransformStream._readable._readableStreamController;\n    return ReadableStreamDefaultControllerGetDesiredSize(readableController);\n  }\n\n  /**\n   * Enqueues the given chunk `chunk` in the readable side of the controlled transform stream.\n   */\n  enqueue(chunk: O): void;\n  enqueue(chunk: O = undefined!): void {\n    if (!IsTransformStreamDefaultController(this)) {\n      throw defaultControllerBrandCheckException('enqueue');\n    }\n\n    TransformStreamDefaultControllerEnqueue(this, chunk);\n  }\n\n  /**\n   * Errors both the readable side and the writable side of the controlled transform stream, making all future\n   * interactions with it fail with the given error `e`. Any chunks queued for transformation will be discarded.\n   */\n  error(reason: any = undefined): void {\n    if (!IsTransformStreamDefaultController(this)) {\n      throw defaultControllerBrandCheckException('error');\n    }\n\n    TransformStreamDefaultControllerError(this, reason);\n  }\n\n  /**\n   * Closes the readable side and errors the writable side of the controlled transform stream. This is useful when the\n   * transformer only needs to consume a portion of the chunks written to the writable side.\n   */\n  terminate(): void {\n    if (!IsTransformStreamDefaultController(this)) {\n      throw defaultControllerBrandCheckException('terminate');\n    }\n\n    TransformStreamDefaultControllerTerminate(this);\n  }\n}\n\nObject.defineProperties(TransformStreamDefaultController.prototype, {\n  enqueue: { enumerable: true },\n  error: { enumerable: true },\n  terminate: { enumerable: true },\n  desiredSize: { enumerable: true }\n});\nsetFunctionName(TransformStreamDefaultController.prototype.enqueue, 'enqueue');\nsetFunctionName(TransformStreamDefaultController.prototype.error, 'error');\nsetFunctionName(TransformStreamDefaultController.prototype.terminate, 'terminate');\nif (typeof Symbol.toStringTag === 'symbol') {\n  Object.defineProperty(TransformStreamDefaultController.prototype, Symbol.toStringTag, {\n    value: 'TransformStreamDefaultController',\n    configurable: true\n  });\n}\n\n// Transform Stream Default Controller Abstract Operations\n\nfunction IsTransformStreamDefaultController<O = any>(x: any): x is TransformStreamDefaultController<O> {\n  if (!typeIsObject(x)) {\n    return false;\n  }\n\n  if (!Object.prototype.hasOwnProperty.call(x, '_controlledTransformStream')) {\n    return false;\n  }\n\n  return x instanceof TransformStreamDefaultController;\n}\n\nfunction SetUpTransformStreamDefaultController<I, O>(stream: TransformStream<I, O>,\n                                                     controller: TransformStreamDefaultController<O>,\n                                                     transformAlgorithm: (chunk: I) => Promise<void>,\n                                                     flushAlgorithm: () => Promise<void>,\n                                                     cancelAlgorithm: (reason: any) => Promise<void>) {\n  assert(IsTransformStream(stream));\n  assert(stream._transformStreamController === undefined);\n\n  controller._controlledTransformStream = stream;\n  stream._transformStreamController = controller;\n\n  controller._transformAlgorithm = transformAlgorithm;\n  controller._flushAlgorithm = flushAlgorithm;\n  controller._cancelAlgorithm = cancelAlgorithm;\n\n  controller._finishPromise = undefined;\n  controller._finishPromise_resolve = undefined;\n  controller._finishPromise_reject = undefined;\n}\n\nfunction SetUpTransformStreamDefaultControllerFromTransformer<I, O>(stream: TransformStream<I, O>,\n                                                                    transformer: ValidatedTransformer<I, O>) {\n  const controller: TransformStreamDefaultController<O> = Object.create(TransformStreamDefaultController.prototype);\n\n  let transformAlgorithm: (chunk: I) => Promise<void>;\n  let flushAlgorithm: () => Promise<void>;\n  let cancelAlgorithm: (reason: any) => Promise<void>;\n\n  if (transformer.transform !== undefined) {\n    transformAlgorithm = chunk => transformer.transform!(chunk, controller);\n  } else {\n    transformAlgorithm = chunk => {\n      try {\n        TransformStreamDefaultControllerEnqueue(controller, chunk as unknown as O);\n        return promiseResolvedWith(undefined);\n      } catch (transformResultE) {\n        return promiseRejectedWith(transformResultE);\n      }\n    };\n  }\n\n  if (transformer.flush !== undefined) {\n    flushAlgorithm = () => transformer.flush!(controller);\n  } else {\n    flushAlgorithm = () => promiseResolvedWith(undefined);\n  }\n\n  if (transformer.cancel !== undefined) {\n    cancelAlgorithm = reason => transformer.cancel!(reason);\n  } else {\n    cancelAlgorithm = () => promiseResolvedWith(undefined);\n  }\n\n  SetUpTransformStreamDefaultController(stream, controller, transformAlgorithm, flushAlgorithm, cancelAlgorithm);\n}\n\nfunction TransformStreamDefaultControllerClearAlgorithms(controller: TransformStreamDefaultController<any>) {\n  controller._transformAlgorithm = undefined!;\n  controller._flushAlgorithm = undefined!;\n  controller._cancelAlgorithm = undefined!;\n}\n\nfunction TransformStreamDefaultControllerEnqueue<O>(controller: TransformStreamDefaultController<O>, chunk: O) {\n  const stream = controller._controlledTransformStream;\n  const readableController = stream._readable._readableStreamController;\n  if (!ReadableStreamDefaultControllerCanCloseOrEnqueue(readableController)) {\n    throw new TypeError('Readable side is not in a state that permits enqueue');\n  }\n\n  // We throttle transform invocations based on the backpressure of the ReadableStream, but we still\n  // accept TransformStreamDefaultControllerEnqueue() calls.\n\n  try {\n    ReadableStreamDefaultControllerEnqueue(readableController, chunk);\n  } catch (e) {\n    // This happens when readableStrategy.size() throws.\n    TransformStreamErrorWritableAndUnblockWrite(stream, e);\n\n    throw stream._readable._storedError;\n  }\n\n  const backpressure = ReadableStreamDefaultControllerHasBackpressure(readableController);\n  if (backpressure !== stream._backpressure) {\n    assert(backpressure);\n    TransformStreamSetBackpressure(stream, true);\n  }\n}\n\nfunction TransformStreamDefaultControllerError(controller: TransformStreamDefaultController<any>, e: any) {\n  TransformStreamError(controller._controlledTransformStream, e);\n}\n\nfunction TransformStreamDefaultControllerPerformTransform<I, O>(controller: TransformStreamDefaultController<O>,\n                                                                chunk: I) {\n  const transformPromise = controller._transformAlgorithm(chunk);\n  return transformPromiseWith(transformPromise, undefined, r => {\n    TransformStreamError(controller._controlledTransformStream, r);\n    throw r;\n  });\n}\n\nfunction TransformStreamDefaultControllerTerminate<O>(controller: TransformStreamDefaultController<O>) {\n  const stream = controller._controlledTransformStream;\n  const readableController = stream._readable._readableStreamController;\n\n  ReadableStreamDefaultControllerClose(readableController);\n\n  const error = new TypeError('TransformStream terminated');\n  TransformStreamErrorWritableAndUnblockWrite(stream, error);\n}\n\n// TransformStreamDefaultSink Algorithms\n\nfunction TransformStreamDefaultSinkWriteAlgorithm<I, O>(stream: TransformStream<I, O>, chunk: I): Promise<void> {\n  assert(stream._writable._state === 'writable');\n\n  const controller = stream._transformStreamController;\n\n  if (stream._backpressure) {\n    const backpressureChangePromise = stream._backpressureChangePromise;\n    assert(backpressureChangePromise !== undefined);\n    return transformPromiseWith(backpressureChangePromise, () => {\n      const writable = stream._writable;\n      const state = writable._state;\n      if (state === 'erroring') {\n        throw writable._storedError;\n      }\n      assert(state === 'writable');\n      return TransformStreamDefaultControllerPerformTransform<I, O>(controller, chunk);\n    });\n  }\n\n  return TransformStreamDefaultControllerPerformTransform<I, O>(controller, chunk);\n}\n\nfunction TransformStreamDefaultSinkAbortAlgorithm<I, O>(stream: TransformStream<I, O>, reason: any): Promise<void> {\n  const controller = stream._transformStreamController;\n  if (controller._finishPromise !== undefined) {\n    return controller._finishPromise;\n  }\n\n  // stream._readable cannot change after construction, so caching it across a call to user code is safe.\n  const readable = stream._readable;\n\n  // Assign the _finishPromise now so that if _cancelAlgorithm calls readable.cancel() internally,\n  // we don't run the _cancelAlgorithm again.\n  controller._finishPromise = newPromise((resolve, reject) => {\n    controller._finishPromise_resolve = resolve;\n    controller._finishPromise_reject = reject;\n  });\n\n  const cancelPromise = controller._cancelAlgorithm(reason);\n  TransformStreamDefaultControllerClearAlgorithms(controller);\n\n  uponPromise(cancelPromise, () => {\n    if (readable._state === 'errored') {\n      defaultControllerFinishPromiseReject(controller, readable._storedError);\n    } else {\n      ReadableStreamDefaultControllerError(readable._readableStreamController, reason);\n      defaultControllerFinishPromiseResolve(controller);\n    }\n    return null;\n  }, r => {\n    ReadableStreamDefaultControllerError(readable._readableStreamController, r);\n    defaultControllerFinishPromiseReject(controller, r);\n    return null;\n  });\n\n  return controller._finishPromise;\n}\n\nfunction TransformStreamDefaultSinkCloseAlgorithm<I, O>(stream: TransformStream<I, O>): Promise<void> {\n  const controller = stream._transformStreamController;\n  if (controller._finishPromise !== undefined) {\n    return controller._finishPromise;\n  }\n\n  // stream._readable cannot change after construction, so caching it across a call to user code is safe.\n  const readable = stream._readable;\n\n  // Assign the _finishPromise now so that if _flushAlgorithm calls readable.cancel() internally,\n  // we don't also run the _cancelAlgorithm.\n  controller._finishPromise = newPromise((resolve, reject) => {\n    controller._finishPromise_resolve = resolve;\n    controller._finishPromise_reject = reject;\n  });\n\n  const flushPromise = controller._flushAlgorithm();\n  TransformStreamDefaultControllerClearAlgorithms(controller);\n\n  uponPromise(flushPromise, () => {\n    if (readable._state === 'errored') {\n      defaultControllerFinishPromiseReject(controller, readable._storedError);\n    } else {\n      ReadableStreamDefaultControllerClose(readable._readableStreamController);\n      defaultControllerFinishPromiseResolve(controller);\n    }\n    return null;\n  }, r => {\n    ReadableStreamDefaultControllerError(readable._readableStreamController, r);\n    defaultControllerFinishPromiseReject(controller, r);\n    return null;\n  });\n\n  return controller._finishPromise;\n}\n\n// TransformStreamDefaultSource Algorithms\n\nfunction TransformStreamDefaultSourcePullAlgorithm(stream: TransformStream): Promise<void> {\n  // Invariant. Enforced by the promises returned by start() and pull().\n  assert(stream._backpressure);\n\n  assert(stream._backpressureChangePromise !== undefined);\n\n  TransformStreamSetBackpressure(stream, false);\n\n  // Prevent the next pull() call until there is backpressure.\n  return stream._backpressureChangePromise;\n}\n\nfunction TransformStreamDefaultSourceCancelAlgorithm<I, O>(stream: TransformStream<I, O>, reason: any): Promise<void> {\n  const controller = stream._transformStreamController;\n  if (controller._finishPromise !== undefined) {\n    return controller._finishPromise;\n  }\n\n  // stream._writable cannot change after construction, so caching it across a call to user code is safe.\n  const writable = stream._writable;\n\n  // Assign the _finishPromise now so that if _flushAlgorithm calls writable.abort() or\n  // writable.cancel() internally, we don't run the _cancelAlgorithm again, or also run the\n  // _flushAlgorithm.\n  controller._finishPromise = newPromise((resolve, reject) => {\n    controller._finishPromise_resolve = resolve;\n    controller._finishPromise_reject = reject;\n  });\n\n  const cancelPromise = controller._cancelAlgorithm(reason);\n  TransformStreamDefaultControllerClearAlgorithms(controller);\n\n  uponPromise(cancelPromise, () => {\n    if (writable._state === 'errored') {\n      defaultControllerFinishPromiseReject(controller, writable._storedError);\n    } else {\n      WritableStreamDefaultControllerErrorIfNeeded(writable._writableStreamController, reason);\n      TransformStreamUnblockWrite(stream);\n      defaultControllerFinishPromiseResolve(controller);\n    }\n    return null;\n  }, r => {\n    WritableStreamDefaultControllerErrorIfNeeded(writable._writableStreamController, r);\n    TransformStreamUnblockWrite(stream);\n    defaultControllerFinishPromiseReject(controller, r);\n    return null;\n  });\n\n  return controller._finishPromise;\n}\n\n// Helper functions for the TransformStreamDefaultController.\n\nfunction defaultControllerBrandCheckException(name: string): TypeError {\n  return new TypeError(\n    `TransformStreamDefaultController.prototype.${name} can only be used on a TransformStreamDefaultController`);\n}\n\nexport function defaultControllerFinishPromiseResolve(controller: TransformStreamDefaultController<any>) {\n  if (controller._finishPromise_resolve === undefined) {\n    return;\n  }\n\n  controller._finishPromise_resolve();\n  controller._finishPromise_resolve = undefined;\n  controller._finishPromise_reject = undefined;\n}\n\nexport function defaultControllerFinishPromiseReject(controller: TransformStreamDefaultController<any>, reason: any) {\n  if (controller._finishPromise_reject === undefined) {\n    return;\n  }\n\n  setPromiseIsHandledToTrue(controller._finishPromise!);\n  controller._finishPromise_reject(reason);\n  controller._finishPromise_resolve = undefined;\n  controller._finishPromise_reject = undefined;\n}\n\n// Helper functions for the TransformStream.\n\nfunction streamBrandCheckException(name: string): TypeError {\n  return new TypeError(\n    `TransformStream.prototype.${name} can only be used on a TransformStream`);\n}\n"], "names": ["Symbol", "_a", "queueMicrotask", "streamBrandCheckException", "defaultControllerBrandCheckException"], "mappings": ";;;;;;;AAAA;AAEA,IAAM,cAAc,GAClB,OAAO,MAAM,KAAK,UAAU,IAAI,OAAO,MAAM,CAAC,QAAQ,KAAK,QAAQ;AACjE,IAAA,MAAM;IACN,UAAA,WAAW,IAAI,OAAA,SAAA,CAAA,MAAA,CAAU,WAAW,EAAoB,GAAA,CAAA,CAAA,EAAA;;ACL5D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AA4GA;AACO,SAAS,WAAW,CAAC,OAAO,EAAE,IAAI,EAAE;AAC3C,IAAI,IAAI,CAAC,GAAG,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,WAAW,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AACrH,IAAI,OAAO,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,OAAO,MAAM,KAAK,UAAU,KAAK,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,WAAW,EAAE,OAAO,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AAC7J,IAAI,SAAS,IAAI,CAAC,CAAC,EAAE,EAAE,OAAO,UAAU,CAAC,EAAE,EAAE,OAAO,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;AACtE,IAAI,SAAS,IAAI,CAAC,EAAE,EAAE;AACtB,QAAQ,IAAI,CAAC,EAAE,MAAM,IAAI,SAAS,CAAC,iCAAiC,CAAC,CAAC;AACtE,QAAQ,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI;AACtD,YAAY,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;AACzK,YAAY,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC;AACpD,YAAY,QAAQ,EAAE,CAAC,CAAC,CAAC;AACzB,gBAAgB,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,MAAM;AAC9C,gBAAgB,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,OAAO,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC;AACxE,gBAAgB,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS;AACjE,gBAAgB,KAAK,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,SAAS;AACjE,gBAAgB;AAChB,oBAAoB,IAAI,EAAE,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,SAAS,EAAE;AAChI,oBAAoB,IAAI,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,KAAK,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE;AAC1G,oBAAoB,IAAI,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,MAAM,EAAE;AACzF,oBAAoB,IAAI,CAAC,IAAI,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,EAAE;AACvF,oBAAoB,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC;AAC1C,oBAAoB,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,SAAS;AAC3C,aAAa;AACb,YAAY,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;AACvC,SAAS,CAAC,OAAO,CAAC,EAAE,EAAE,EAAE,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,SAAS,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE;AAClE,QAAQ,IAAI,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;AACzF,KAAK;AACL,CAAC;AAiBD;AACO,SAAS,QAAQ,CAAC,CAAC,EAAE;AAC5B,IAAI,IAAI,CAAC,GAAG,OAAO,MAAM,KAAK,UAAU,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;AAClF,IAAI,IAAI,CAAC,EAAE,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAC5B,IAAI,IAAI,CAAC,IAAI,OAAO,CAAC,CAAC,MAAM,KAAK,QAAQ,EAAE,OAAO;AAClD,QAAQ,IAAI,EAAE,YAAY;AAC1B,YAAY,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,KAAK,CAAC,CAAC;AAC/C,YAAY,OAAO,EAAE,KAAK,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC;AACpD,SAAS;AACT,KAAK,CAAC;AACN,IAAI,MAAM,IAAI,SAAS,CAAC,CAAC,GAAG,yBAAyB,GAAG,iCAAiC,CAAC,CAAC;AAC3F,CAAC;AA4CD;AACO,SAAS,OAAO,CAAC,CAAC,EAAE;AAC3B,IAAI,OAAO,IAAI,YAAY,OAAO,IAAI,IAAI,CAAC,CAAC,GAAG,CAAC,EAAE,IAAI,IAAI,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC;AACzE,CAAC;AACD;AACO,SAAS,gBAAgB,CAAC,OAAO,EAAE,UAAU,EAAE,SAAS,EAAE;AACjE,IAAI,IAAI,CAAC,MAAM,CAAC,aAAa,EAAE,MAAM,IAAI,SAAS,CAAC,sCAAsC,CAAC,CAAC;AAC3F,IAAI,IAAI,CAAC,GAAG,SAAS,CAAC,KAAK,CAAC,OAAO,EAAE,UAAU,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC;AAClE,IAAI,OAAO,CAAC,GAAG,EAAE,EAAE,IAAI,CAAC,MAAM,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,aAAa,CAAC,GAAG,YAAY,EAAE,OAAO,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC;AAC1H,IAAI,SAAS,IAAI,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,EAAE,EAAE,OAAO,IAAI,OAAO,CAAC,UAAU,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE;AAC9I,IAAI,SAAS,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE;AACtF,IAAI,SAAS,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,KAAK,YAAY,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,EAAE,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;AAC5H,IAAI,SAAS,OAAO,CAAC,KAAK,EAAE,EAAE,MAAM,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,EAAE;AACtD,IAAI,SAAS,MAAM,CAAC,KAAK,EAAE,EAAE,MAAM,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC,EAAE;AACtD,IAAI,SAAS,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;AACtF,CAAC;AACD;AACO,SAAS,gBAAgB,CAAC,CAAC,EAAE;AACpC,IAAI,IAAI,CAAC,EAAE,CAAC,CAAC;AACb,IAAI,OAAO,CAAC,GAAG,EAAE,EAAE,IAAI,CAAC,MAAM,CAAC,EAAE,IAAI,CAAC,OAAO,EAAE,UAAU,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,YAAY,EAAE,OAAO,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC;AAChJ,IAAI,SAAS,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;AAC1I,CAAC;AACD;AACO,SAAS,aAAa,CAAC,CAAC,EAAE;AACjC,IAAI,IAAI,CAAC,MAAM,CAAC,aAAa,EAAE,MAAM,IAAI,SAAS,CAAC,sCAAsC,CAAC,CAAC;AAC3F,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,aAAa,CAAC,EAAE,CAAC,CAAC;AACvC,IAAI,OAAO,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,OAAO,QAAQ,KAAK,UAAU,GAAG,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC,GAAG,EAAE,EAAE,IAAI,CAAC,MAAM,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,aAAa,CAAC,GAAG,YAAY,EAAE,OAAO,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;AACrN,IAAI,SAAS,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,UAAU,CAAC,EAAE,EAAE,OAAO,IAAI,OAAO,CAAC,UAAU,OAAO,EAAE,MAAM,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,OAAO,EAAE,MAAM,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE;AACpK,IAAI,SAAS,MAAM,CAAC,OAAO,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,EAAE,OAAO,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC,EAAE;AAChI,CAAC;AA+DD;AACuB,OAAO,eAAe,KAAK,UAAU,GAAG,eAAe,GAAG,UAAU,KAAK,EAAE,UAAU,EAAE,OAAO,EAAE;AACvH,IAAI,IAAI,CAAC,GAAG,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC;AAC/B,IAAI,OAAO,CAAC,CAAC,IAAI,GAAG,iBAAiB,EAAE,CAAC,CAAC,KAAK,GAAG,KAAK,EAAE,CAAC,CAAC,UAAU,GAAG,UAAU,EAAE,CAAC,CAAC;AACrF;;SC9TgB,IAAI,GAAA;AAClB,IAAA,OAAO,SAAS,CAAC;AACnB;;ACCM,SAAU,YAAY,CAAC,CAAM,EAAA;AACjC,IAAA,OAAO,CAAC,OAAO,CAAC,KAAK,QAAQ,IAAI,CAAC,KAAK,IAAI,KAAK,OAAO,CAAC,KAAK,UAAU,CAAC;AAC1E,CAAC;AAEM,IAAM,8BAA8B,GAUrC,IAAI,CAAC;AAEK,SAAA,eAAe,CAAC,EAAY,EAAE,IAAY,EAAA;AACxD,IAAA,IAAI;AACF,QAAA,MAAM,CAAC,cAAc,CAAC,EAAE,EAAE,MAAM,EAAE;AAChC,YAAA,KAAK,EAAE,IAAI;AACX,YAAA,YAAY,EAAE,IAAI;AACnB,SAAA,CAAC,CAAC;KACJ;AAAC,IAAA,OAAA,EAAA,EAAM;;;KAGP;AACH;;AC1BA,IAAM,eAAe,GAAG,OAAO,CAAC;AAChC,IAAM,mBAAmB,GAAG,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC;AACnD,IAAM,qBAAqB,GAAG,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;AAEnE;AACM,SAAU,UAAU,CAAI,QAGrB,EAAA;AACP,IAAA,OAAO,IAAI,eAAe,CAAC,QAAQ,CAAC,CAAC;AACvC,CAAC;AAED;AACM,SAAU,mBAAmB,CAAI,KAAyB,EAAA;AAC9D,IAAA,OAAO,UAAU,CAAC,UAAA,OAAO,EAAI,EAAA,OAAA,OAAO,CAAC,KAAK,CAAC,CAAd,EAAc,CAAC,CAAC;AAC/C,CAAC;AAED;AACM,SAAU,mBAAmB,CAAY,MAAW,EAAA;AACxD,IAAA,OAAO,qBAAqB,CAAC,MAAM,CAAC,CAAC;AACvC,CAAC;SAEe,kBAAkB,CAChC,OAAmB,EACnB,WAA4D,EAC5D,UAA8D,EAAA;;;IAG9D,OAAO,mBAAmB,CAAC,IAAI,CAAC,OAAO,EAAE,WAAW,EAAE,UAAU,CAAiC,CAAC;AACpG,CAAC;AAED;AACA;AACA;SACgB,WAAW,CACzB,OAAmB,EACnB,WAAoD,EACpD,UAAsD,EAAA;AACtD,IAAA,kBAAkB,CAChB,kBAAkB,CAAC,OAAO,EAAE,WAAW,EAAE,UAAU,CAAC,EACpD,SAAS,EACT,8BAA8B,CAC/B,CAAC;AACJ,CAAC;AAEe,SAAA,eAAe,CAAI,OAAmB,EAAE,WAAmD,EAAA;AACzG,IAAA,WAAW,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC;AACpC,CAAC;AAEe,SAAA,aAAa,CAAC,OAAyB,EAAE,UAAqD,EAAA;AAC5G,IAAA,WAAW,CAAC,OAAO,EAAE,SAAS,EAAE,UAAU,CAAC,CAAC;AAC9C,CAAC;SAEe,oBAAoB,CAClC,OAAmB,EACnB,kBAAmE,EACnE,gBAAoE,EAAA;IACpE,OAAO,kBAAkB,CAAC,OAAO,EAAE,kBAAkB,EAAE,gBAAgB,CAAC,CAAC;AAC3E,CAAC;AAEK,SAAU,yBAAyB,CAAC,OAAyB,EAAA;AACjE,IAAA,kBAAkB,CAAC,OAAO,EAAE,SAAS,EAAE,8BAA8B,CAAC,CAAC;AACzE,CAAC;AAED,IAAI,eAAe,GAAmC,UAAA,QAAQ,EAAA;AAC5D,IAAA,IAAI,OAAO,cAAc,KAAK,UAAU,EAAE;QACxC,eAAe,GAAG,cAAc,CAAC;KAClC;SAAM;AACL,QAAA,IAAM,iBAAe,GAAG,mBAAmB,CAAC,SAAS,CAAC,CAAC;AACvD,QAAA,eAAe,GAAG,UAAA,EAAE,EAAA,EAAI,OAAA,kBAAkB,CAAC,iBAAe,EAAE,EAAE,CAAC,CAAA,EAAA,CAAC;KACjE;AACD,IAAA,OAAO,eAAe,CAAC,QAAQ,CAAC,CAAC;AACnC,CAAC,CAAC;SAIc,WAAW,CAAwB,CAA+B,EAAE,CAAI,EAAE,IAAO,EAAA;AAC/F,IAAA,IAAI,OAAO,CAAC,KAAK,UAAU,EAAE;AAC3B,QAAA,MAAM,IAAI,SAAS,CAAC,4BAA4B,CAAC,CAAC;KACnD;AACD,IAAA,OAAO,QAAQ,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC;AACnD,CAAC;SAEe,WAAW,CAAwB,CAAgD,EAChD,CAAI,EACJ,IAAO,EAAA;AAIxD,IAAA,IAAI;QACF,OAAO,mBAAmB,CAAC,WAAW,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;KACrD;IAAC,OAAO,KAAK,EAAE;AACd,QAAA,OAAO,mBAAmB,CAAC,KAAK,CAAC,CAAC;KACnC;AACH;;AC/FA;AACA;AAEA,IAAM,oBAAoB,GAAG,KAAK,CAAC;AAOnC;;;;;AAKG;AACH,IAAA,WAAA,kBAAA,YAAA;AAME,IAAA,SAAA,WAAA,GAAA;QAHQ,IAAO,CAAA,OAAA,GAAG,CAAC,CAAC;QACZ,IAAK,CAAA,KAAA,GAAG,CAAC,CAAC;;QAIhB,IAAI,CAAC,MAAM,GAAG;AACZ,YAAA,SAAS,EAAE,EAAE;AACb,YAAA,KAAK,EAAE,SAAS;SACjB,CAAC;AACF,QAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC;;;;AAIzB,QAAA,IAAI,CAAC,OAAO,GAAG,CAAC,CAAC;;AAEjB,QAAA,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;KAChB;AAED,IAAA,MAAA,CAAA,cAAA,CAAI,WAAM,CAAA,SAAA,EAAA,QAAA,EAAA;AAAV,QAAA,GAAA,EAAA,YAAA;YACE,OAAO,IAAI,CAAC,KAAK,CAAC;SACnB;;;AAAA,KAAA,CAAA,CAAA;;;;;IAMD,WAAI,CAAA,SAAA,CAAA,IAAA,GAAJ,UAAK,OAAU,EAAA;AACb,QAAA,IAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC;QAC3B,IAAI,OAAO,GAAG,OAAO,CACe;QACpC,IAAI,OAAO,CAAC,SAAS,CAAC,MAAM,KAAK,oBAAoB,GAAG,CAAC,EAAE;AACzD,YAAA,OAAO,GAAG;AACR,gBAAA,SAAS,EAAE,EAAE;AACb,gBAAA,KAAK,EAAE,SAAS;aACjB,CAAC;SACH;;;AAID,QAAA,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AAChC,QAAA,IAAI,OAAO,KAAK,OAAO,EAAE;AACvB,YAAA,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC;AACrB,YAAA,OAAO,CAAC,KAAK,GAAG,OAAO,CAAC;SACzB;QACD,EAAE,IAAI,CAAC,KAAK,CAAC;KACd,CAAA;;;AAID,IAAA,WAAA,CAAA,SAAA,CAAA,KAAK,GAAL,YAAA;AAGE,QAAA,IAAM,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC;QAC7B,IAAI,QAAQ,GAAG,QAAQ,CAAC;AACxB,QAAA,IAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC;AAC/B,QAAA,IAAI,SAAS,GAAG,SAAS,GAAG,CAAC,CAAC;AAE9B,QAAA,IAAM,QAAQ,GAAG,QAAQ,CAAC,SAAS,CAAC;AACpC,QAAA,IAAM,OAAO,GAAG,QAAQ,CAAC,SAAS,CAAC,CAAC;AAEpC,QAAA,IAAI,SAAS,KAAK,oBAAoB,EAAE;AAGtC,YAAA,QAAQ,GAAG,QAAQ,CAAC,KAAM,CAAC;YAC3B,SAAS,GAAG,CAAC,CAAC;SACf;;QAGD,EAAE,IAAI,CAAC,KAAK,CAAC;AACb,QAAA,IAAI,CAAC,OAAO,GAAG,SAAS,CAAC;AACzB,QAAA,IAAI,QAAQ,KAAK,QAAQ,EAAE;AACzB,YAAA,IAAI,CAAC,MAAM,GAAG,QAAQ,CAAC;SACxB;;AAGD,QAAA,QAAQ,CAAC,SAAS,CAAC,GAAG,SAAU,CAAC;AAEjC,QAAA,OAAO,OAAO,CAAC;KAChB,CAAA;;;;;;;;;IAUD,WAAO,CAAA,SAAA,CAAA,OAAA,GAAP,UAAQ,QAA8B,EAAA;AACpC,QAAA,IAAI,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACrB,QAAA,IAAI,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC;AACvB,QAAA,IAAI,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC;AAC9B,QAAA,OAAO,CAAC,KAAK,QAAQ,CAAC,MAAM,IAAI,IAAI,CAAC,KAAK,KAAK,SAAS,EAAE;AACxD,YAAA,IAAI,CAAC,KAAK,QAAQ,CAAC,MAAM,EAAE;AAGzB,gBAAA,IAAI,GAAG,IAAI,CAAC,KAAM,CAAC;AACnB,gBAAA,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC;gBAC1B,CAAC,GAAG,CAAC,CAAC;AACN,gBAAA,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE;oBACzB,MAAM;iBACP;aACF;AACD,YAAA,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;AACtB,YAAA,EAAE,CAAC,CAAC;SACL;KACF,CAAA;;;AAID,IAAA,WAAA,CAAA,SAAA,CAAA,IAAI,GAAJ,YAAA;AAGE,QAAA,IAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC;AAC1B,QAAA,IAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC;AAC5B,QAAA,OAAO,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;KAChC,CAAA;IACH,OAAC,WAAA,CAAA;AAAD,CAAC,EAAA,CAAA;;AC1IM,IAAM,UAAU,GAAGA,cAAM,CAAC,gBAAgB,CAAC,CAAC;AAC5C,IAAM,UAAU,GAAGA,cAAM,CAAC,gBAAgB,CAAC,CAAC;AAC5C,IAAM,WAAW,GAAGA,cAAM,CAAC,iBAAiB,CAAC,CAAC;AAC9C,IAAM,SAAS,GAAGA,cAAM,CAAC,eAAe,CAAC,CAAC;AAC1C,IAAM,YAAY,GAAGA,cAAM,CAAC,kBAAkB,CAAC;;ACCtC,SAAA,qCAAqC,CAAI,MAA+B,EAAE,MAAyB,EAAA;AACjH,IAAA,MAAM,CAAC,oBAAoB,GAAG,MAAM,CAAC;AACrC,IAAA,MAAM,CAAC,OAAO,GAAG,MAAM,CAAC;AAExB,IAAA,IAAI,MAAM,CAAC,MAAM,KAAK,UAAU,EAAE;QAChC,oCAAoC,CAAC,MAAM,CAAC,CAAC;KAC9C;AAAM,SAAA,IAAI,MAAM,CAAC,MAAM,KAAK,QAAQ,EAAE;QACrC,8CAA8C,CAAC,MAAM,CAAC,CAAC;KACxD;SAAM;AAGL,QAAA,8CAA8C,CAAC,MAAM,EAAE,MAAM,CAAC,YAAY,CAAC,CAAC;KAC7E;AACH,CAAC;AAED;AACA;AAEgB,SAAA,iCAAiC,CAAC,MAAiC,EAAE,MAAW,EAAA;AAC9F,IAAA,IAAM,MAAM,GAAG,MAAM,CAAC,oBAAoB,CACb;AAC7B,IAAA,OAAO,oBAAoB,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;AAC9C,CAAC;AAEK,SAAU,kCAAkC,CAAC,MAAiC,EAAA;AAClF,IAAA,IAAM,MAAM,GAAG,MAAM,CAAC,oBAAoB,CAER;AAElC,IAAA,IAAI,MAAM,CAAC,MAAM,KAAK,UAAU,EAAE;QAChC,gCAAgC,CAC9B,MAAM,EACN,IAAI,SAAS,CAAC,kFAAkF,CAAC,CAAC,CAAC;KACtG;SAAM;QACL,yCAAyC,CACvC,MAAM,EACN,IAAI,SAAS,CAAC,kFAAkF,CAAC,CAAC,CAAC;KACtG;AAED,IAAA,MAAM,CAAC,yBAAyB,CAAC,YAAY,CAAC,EAAE,CAAC;AAEjD,IAAA,MAAM,CAAC,OAAO,GAAG,SAAS,CAAC;AAC3B,IAAA,MAAM,CAAC,oBAAoB,GAAG,SAAU,CAAC;AAC3C,CAAC;AAED;AAEM,SAAU,mBAAmB,CAAC,IAAY,EAAA;IAC9C,OAAO,IAAI,SAAS,CAAC,SAAS,GAAG,IAAI,GAAG,mCAAmC,CAAC,CAAC;AAC/E,CAAC;AAED;AAEM,SAAU,oCAAoC,CAAC,MAAiC,EAAA;IACpF,MAAM,CAAC,cAAc,GAAG,UAAU,CAAC,UAAC,OAAO,EAAE,MAAM,EAAA;AACjD,QAAA,MAAM,CAAC,sBAAsB,GAAG,OAAO,CAAC;AACxC,QAAA,MAAM,CAAC,qBAAqB,GAAG,MAAM,CAAC;AACxC,KAAC,CAAC,CAAC;AACL,CAAC;AAEe,SAAA,8CAA8C,CAAC,MAAiC,EAAE,MAAW,EAAA;IAC3G,oCAAoC,CAAC,MAAM,CAAC,CAAC;AAC7C,IAAA,gCAAgC,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;AACnD,CAAC;AAEK,SAAU,8CAA8C,CAAC,MAAiC,EAAA;IAC9F,oCAAoC,CAAC,MAAM,CAAC,CAAC;IAC7C,iCAAiC,CAAC,MAAM,CAAC,CAAC;AAC5C,CAAC;AAEe,SAAA,gCAAgC,CAAC,MAAiC,EAAE,MAAW,EAAA;AAC7F,IAAA,IAAI,MAAM,CAAC,qBAAqB,KAAK,SAAS,EAAE;QAC9C,OAAO;KACR;AAED,IAAA,yBAAyB,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;AACjD,IAAA,MAAM,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC;AACrC,IAAA,MAAM,CAAC,sBAAsB,GAAG,SAAS,CAAC;AAC1C,IAAA,MAAM,CAAC,qBAAqB,GAAG,SAAS,CAAC;AAC3C,CAAC;AAEe,SAAA,yCAAyC,CAAC,MAAiC,EAAE,MAAW,EAAA;AAItG,IAAA,8CAA8C,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;AACjE,CAAC;AAEK,SAAU,iCAAiC,CAAC,MAAiC,EAAA;AACjF,IAAA,IAAI,MAAM,CAAC,sBAAsB,KAAK,SAAS,EAAE;QAC/C,OAAO;KACR;AAED,IAAA,MAAM,CAAC,sBAAsB,CAAC,SAAS,CAAC,CAAC;AACzC,IAAA,MAAM,CAAC,sBAAsB,GAAG,SAAS,CAAC;AAC1C,IAAA,MAAM,CAAC,qBAAqB,GAAG,SAAS,CAAC;AAC3C;;ACrGA;AAEA;AACA,IAAM,cAAc,GAA2B,MAAM,CAAC,QAAQ,IAAI,UAAU,CAAC,EAAA;IAC3E,OAAO,OAAO,CAAC,KAAK,QAAQ,IAAI,QAAQ,CAAC,CAAC,CAAC,CAAC;AAC9C,CAAC;;ACLD;AAEA;AACA,IAAM,SAAS,GAAsB,IAAI,CAAC,KAAK,IAAI,UAAU,CAAC,EAAA;IAC5D,OAAO,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AAC9C,CAAC;;ACFD;AACM,SAAU,YAAY,CAAC,CAAM,EAAA;IACjC,OAAO,OAAO,CAAC,KAAK,QAAQ,IAAI,OAAO,CAAC,KAAK,UAAU,CAAC;AAC1D,CAAC;AAEe,SAAA,gBAAgB,CAAC,GAAY,EACZ,OAAe,EAAA;IAC9C,IAAI,GAAG,KAAK,SAAS,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,EAAE;AAC3C,QAAA,MAAM,IAAI,SAAS,CAAC,UAAG,OAAO,EAAA,oBAAA,CAAoB,CAAC,CAAC;KACrD;AACH,CAAC;AAID;AACgB,SAAA,cAAc,CAAC,CAAU,EAAE,OAAe,EAAA;AACxD,IAAA,IAAI,OAAO,CAAC,KAAK,UAAU,EAAE;AAC3B,QAAA,MAAM,IAAI,SAAS,CAAC,UAAG,OAAO,EAAA,qBAAA,CAAqB,CAAC,CAAC;KACtD;AACH,CAAC;AAED;AACM,SAAU,QAAQ,CAAC,CAAM,EAAA;AAC7B,IAAA,OAAO,CAAC,OAAO,CAAC,KAAK,QAAQ,IAAI,CAAC,KAAK,IAAI,KAAK,OAAO,CAAC,KAAK,UAAU,CAAC;AAC1E,CAAC;AAEe,SAAA,YAAY,CAAC,CAAU,EACV,OAAe,EAAA;AAC1C,IAAA,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE;AAChB,QAAA,MAAM,IAAI,SAAS,CAAC,UAAG,OAAO,EAAA,oBAAA,CAAoB,CAAC,CAAC;KACrD;AACH,CAAC;SAEe,sBAAsB,CAAI,CAAgB,EAChB,QAAgB,EAChB,OAAe,EAAA;AACvD,IAAA,IAAI,CAAC,KAAK,SAAS,EAAE;QACnB,MAAM,IAAI,SAAS,CAAC,YAAA,CAAA,MAAA,CAAa,QAAQ,EAAoB,mBAAA,CAAA,CAAA,MAAA,CAAA,OAAO,EAAI,IAAA,CAAA,CAAC,CAAC;KAC3E;AACH,CAAC;SAEe,mBAAmB,CAAI,CAAgB,EAChB,KAAa,EACb,OAAe,EAAA;AACpD,IAAA,IAAI,CAAC,KAAK,SAAS,EAAE;QACnB,MAAM,IAAI,SAAS,CAAC,EAAA,CAAA,MAAA,CAAG,KAAK,EAAoB,mBAAA,CAAA,CAAA,MAAA,CAAA,OAAO,EAAI,IAAA,CAAA,CAAC,CAAC;KAC9D;AACH,CAAC;AAED;AACM,SAAU,yBAAyB,CAAC,KAAc,EAAA;AACtD,IAAA,OAAO,MAAM,CAAC,KAAK,CAAC,CAAC;AACvB,CAAC;AAED,SAAS,kBAAkB,CAAC,CAAS,EAAA;IACnC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACzB,CAAC;AAED,SAAS,WAAW,CAAC,CAAS,EAAA;AAC5B,IAAA,OAAO,kBAAkB,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1C,CAAC;AAED;AACgB,SAAA,uCAAuC,CAAC,KAAc,EAAE,OAAe,EAAA;IACrF,IAAM,UAAU,GAAG,CAAC,CAAC;AACrB,IAAA,IAAM,UAAU,GAAG,MAAM,CAAC,gBAAgB,CAAC;AAE3C,IAAA,IAAI,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;AACtB,IAAA,CAAC,GAAG,kBAAkB,CAAC,CAAC,CAAC,CAAC;AAE1B,IAAA,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE;AACtB,QAAA,MAAM,IAAI,SAAS,CAAC,UAAG,OAAO,EAAA,yBAAA,CAAyB,CAAC,CAAC;KAC1D;AAED,IAAA,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;IAEnB,IAAI,CAAC,GAAG,UAAU,IAAI,CAAC,GAAG,UAAU,EAAE;QACpC,MAAM,IAAI,SAAS,CAAC,EAAG,CAAA,MAAA,CAAA,OAAO,EAAqC,oCAAA,CAAA,CAAA,MAAA,CAAA,UAAU,EAAO,MAAA,CAAA,CAAA,MAAA,CAAA,UAAU,EAAa,aAAA,CAAA,CAAC,CAAC;KAC9G;IAED,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;AACjC,QAAA,OAAO,CAAC,CAAC;KACV;;;;;AAOD,IAAA,OAAO,CAAC,CAAC;AACX;;AC3FgB,SAAA,oBAAoB,CAAC,CAAU,EAAE,OAAe,EAAA;AAC9D,IAAA,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,EAAE;AACxB,QAAA,MAAM,IAAI,SAAS,CAAC,UAAG,OAAO,EAAA,2BAAA,CAA2B,CAAC,CAAC;KAC5D;AACH;;ACsBA;AAEM,SAAU,kCAAkC,CAAI,MAAsB,EAAA;AAC1E,IAAA,OAAO,IAAI,2BAA2B,CAAC,MAAM,CAAC,CAAC;AACjD,CAAC;AAED;AAEgB,SAAA,4BAA4B,CAAI,MAAyB,EACzB,WAA2B,EAAA;IAIxE,MAAM,CAAC,OAA2C,CAAC,aAAa,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;AACtF,CAAC;SAEe,gCAAgC,CAAI,MAAyB,EAAE,KAAoB,EAAE,IAAa,EAAA;AAChH,IAAA,IAAM,MAAM,GAAG,MAAM,CAAC,OAAyC,CAEvB;IAExC,IAAM,WAAW,GAAG,MAAM,CAAC,aAAa,CAAC,KAAK,EAAG,CAAC;IAClD,IAAI,IAAI,EAAE;QACR,WAAW,CAAC,WAAW,EAAE,CAAC;KAC3B;SAAM;AACL,QAAA,WAAW,CAAC,WAAW,CAAC,KAAM,CAAC,CAAC;KACjC;AACH,CAAC;AAEK,SAAU,gCAAgC,CAAI,MAAyB,EAAA;AAC3E,IAAA,OAAQ,MAAM,CAAC,OAA0C,CAAC,aAAa,CAAC,MAAM,CAAC;AACjF,CAAC;AAEK,SAAU,8BAA8B,CAAC,MAAsB,EAAA;AACnE,IAAA,IAAM,MAAM,GAAG,MAAM,CAAC,OAAO,CAAC;AAE9B,IAAA,IAAI,MAAM,KAAK,SAAS,EAAE;AACxB,QAAA,OAAO,KAAK,CAAC;KACd;AAED,IAAA,IAAI,CAAC,6BAA6B,CAAC,MAAM,CAAC,EAAE;AAC1C,QAAA,OAAO,KAAK,CAAC;KACd;AAED,IAAA,OAAO,IAAI,CAAC;AACd,CAAC;AAYD;;;;AAIG;AACH,IAAA,2BAAA,kBAAA,YAAA;AAYE,IAAA,SAAA,2BAAA,CAAY,MAAyB,EAAA;AACnC,QAAA,sBAAsB,CAAC,MAAM,EAAE,CAAC,EAAE,6BAA6B,CAAC,CAAC;AACjE,QAAA,oBAAoB,CAAC,MAAM,EAAE,iBAAiB,CAAC,CAAC;AAEhD,QAAA,IAAI,sBAAsB,CAAC,MAAM,CAAC,EAAE;AAClC,YAAA,MAAM,IAAI,SAAS,CAAC,6EAA6E,CAAC,CAAC;SACpG;AAED,QAAA,qCAAqC,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;AAEpD,QAAA,IAAI,CAAC,aAAa,GAAG,IAAI,WAAW,EAAE,CAAC;KACxC;AAMD,IAAA,MAAA,CAAA,cAAA,CAAI,2BAAM,CAAA,SAAA,EAAA,QAAA,EAAA;AAJV;;;AAGG;AACH,QAAA,GAAA,EAAA,YAAA;AACE,YAAA,IAAI,CAAC,6BAA6B,CAAC,IAAI,CAAC,EAAE;AACxC,gBAAA,OAAO,mBAAmB,CAAC,gCAAgC,CAAC,QAAQ,CAAC,CAAC,CAAC;aACxE;YAED,OAAO,IAAI,CAAC,cAAc,CAAC;SAC5B;;;AAAA,KAAA,CAAA,CAAA;AAED;;AAEG;IACH,2BAAM,CAAA,SAAA,CAAA,MAAA,GAAN,UAAO,MAAuB,EAAA;AAAvB,QAAA,IAAA,MAAA,KAAA,KAAA,CAAA,EAAA,EAAA,MAAuB,GAAA,SAAA,CAAA,EAAA;AAC5B,QAAA,IAAI,CAAC,6BAA6B,CAAC,IAAI,CAAC,EAAE;AACxC,YAAA,OAAO,mBAAmB,CAAC,gCAAgC,CAAC,QAAQ,CAAC,CAAC,CAAC;SACxE;AAED,QAAA,IAAI,IAAI,CAAC,oBAAoB,KAAK,SAAS,EAAE;AAC3C,YAAA,OAAO,mBAAmB,CAAC,mBAAmB,CAAC,QAAQ,CAAC,CAAC,CAAC;SAC3D;AAED,QAAA,OAAO,iCAAiC,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;KACxD,CAAA;AAED;;;;AAIG;AACH,IAAA,2BAAA,CAAA,SAAA,CAAA,IAAI,GAAJ,YAAA;AACE,QAAA,IAAI,CAAC,6BAA6B,CAAC,IAAI,CAAC,EAAE;AACxC,YAAA,OAAO,mBAAmB,CAAC,gCAAgC,CAAC,MAAM,CAAC,CAAC,CAAC;SACtE;AAED,QAAA,IAAI,IAAI,CAAC,oBAAoB,KAAK,SAAS,EAAE;AAC3C,YAAA,OAAO,mBAAmB,CAAC,mBAAmB,CAAC,WAAW,CAAC,CAAC,CAAC;SAC9D;AAED,QAAA,IAAI,cAAqE,CAAC;AAC1E,QAAA,IAAI,aAAqC,CAAC;AAC1C,QAAA,IAAM,OAAO,GAAG,UAAU,CAAqC,UAAC,OAAO,EAAE,MAAM,EAAA;YAC7E,cAAc,GAAG,OAAO,CAAC;YACzB,aAAa,GAAG,MAAM,CAAC;AACzB,SAAC,CAAC,CAAC;AACH,QAAA,IAAM,WAAW,GAAmB;AAClC,YAAA,WAAW,EAAE,UAAA,KAAK,IAAI,OAAA,cAAc,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,GAAA;AACnE,YAAA,WAAW,EAAE,YAAM,EAAA,OAAA,cAAc,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,GAAA;YACnE,WAAW,EAAE,UAAA,CAAC,EAAI,EAAA,OAAA,aAAa,CAAC,CAAC,CAAC,CAAA,EAAA;SACnC,CAAC;AACF,QAAA,+BAA+B,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;AACnD,QAAA,OAAO,OAAO,CAAC;KAChB,CAAA;AAED;;;;;;;;AAQG;AACH,IAAA,2BAAA,CAAA,SAAA,CAAA,WAAW,GAAX,YAAA;AACE,QAAA,IAAI,CAAC,6BAA6B,CAAC,IAAI,CAAC,EAAE;AACxC,YAAA,MAAM,gCAAgC,CAAC,aAAa,CAAC,CAAC;SACvD;AAED,QAAA,IAAI,IAAI,CAAC,oBAAoB,KAAK,SAAS,EAAE;YAC3C,OAAO;SACR;QAED,kCAAkC,CAAC,IAAI,CAAC,CAAC;KAC1C,CAAA;IACH,OAAC,2BAAA,CAAA;AAAD,CAAC,EAAA,EAAA;AAED,MAAM,CAAC,gBAAgB,CAAC,2BAA2B,CAAC,SAAS,EAAE;AAC7D,IAAA,MAAM,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE;AAC5B,IAAA,IAAI,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE;AAC1B,IAAA,WAAW,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE;AACjC,IAAA,MAAM,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE;AAC7B,CAAA,CAAC,CAAC;AACH,eAAe,CAAC,2BAA2B,CAAC,SAAS,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;AACxE,eAAe,CAAC,2BAA2B,CAAC,SAAS,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;AACpE,eAAe,CAAC,2BAA2B,CAAC,SAAS,CAAC,WAAW,EAAE,aAAa,CAAC,CAAC;AAClF,IAAI,OAAOA,cAAM,CAAC,WAAW,KAAK,QAAQ,EAAE;IAC1C,MAAM,CAAC,cAAc,CAAC,2BAA2B,CAAC,SAAS,EAAEA,cAAM,CAAC,WAAW,EAAE;AAC/E,QAAA,KAAK,EAAE,6BAA6B;AACpC,QAAA,YAAY,EAAE,IAAI;AACnB,KAAA,CAAC,CAAC;AACL,CAAC;AAED;AAEM,SAAU,6BAA6B,CAAU,CAAM,EAAA;AAC3D,IAAA,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE;AACpB,QAAA,OAAO,KAAK,CAAC;KACd;AAED,IAAA,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,eAAe,CAAC,EAAE;AAC7D,QAAA,OAAO,KAAK,CAAC;KACd;IAED,OAAO,CAAC,YAAY,2BAA2B,CAAC;AAClD,CAAC;AAEe,SAAA,+BAA+B,CAAI,MAAsC,EACtC,WAA2B,EAAA;AAC5E,IAAA,IAAM,MAAM,GAAG,MAAM,CAAC,oBAAoB,CAEb;AAE7B,IAAA,MAAM,CAAC,UAAU,GAAG,IAAI,CAAC;AAEzB,IAAA,IAAI,MAAM,CAAC,MAAM,KAAK,QAAQ,EAAE;QAC9B,WAAW,CAAC,WAAW,EAAE,CAAC;KAC3B;AAAM,SAAA,IAAI,MAAM,CAAC,MAAM,KAAK,SAAS,EAAE;AACtC,QAAA,WAAW,CAAC,WAAW,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;KAC9C;SAAM;QAEL,MAAM,CAAC,yBAAyB,CAAC,SAAS,CAAC,CAAC,WAA+B,CAAC,CAAC;KAC9E;AACH,CAAC;AAEK,SAAU,kCAAkC,CAAC,MAAmC,EAAA;IACpF,kCAAkC,CAAC,MAAM,CAAC,CAAC;AAC3C,IAAA,IAAM,CAAC,GAAG,IAAI,SAAS,CAAC,qBAAqB,CAAC,CAAC;AAC/C,IAAA,4CAA4C,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;AAC1D,CAAC;AAEe,SAAA,4CAA4C,CAAC,MAAmC,EAAE,CAAM,EAAA;AACtG,IAAA,IAAM,YAAY,GAAG,MAAM,CAAC,aAAa,CAAC;AAC1C,IAAA,MAAM,CAAC,aAAa,GAAG,IAAI,WAAW,EAAE,CAAC;AACzC,IAAA,YAAY,CAAC,OAAO,CAAC,UAAA,WAAW,EAAA;AAC9B,QAAA,WAAW,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;AAC7B,KAAC,CAAC,CAAC;AACL,CAAC;AAED;AAEA,SAAS,gCAAgC,CAAC,IAAY,EAAA;AACpD,IAAA,OAAO,IAAI,SAAS,CAClB,gDAAyC,IAAI,EAAA,oDAAA,CAAoD,CAAC,CAAC;AACvG;;;ACtPM,SAAU,mBAAmB,CAAkB,QAAW,EAAA;;;AAG9D,IAAA,OAAO,QAAQ,CAAC,KAAK,EAAO,CAAC;AAC/B,CAAC;AAEK,SAAU,kBAAkB,CAAC,IAAiB,EACjB,UAAkB,EAClB,GAAgB,EAChB,SAAiB,EACjB,CAAS,EAAA;AAC1C,IAAA,IAAI,UAAU,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,UAAU,CAAC,GAAG,EAAE,SAAS,EAAE,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC;AAC1E,CAAC;AAEM,IAAI,mBAAmB,GAAG,UAAC,CAAc,EAAA;AAC9C,IAAA,IAAI,OAAO,CAAC,CAAC,QAAQ,KAAK,UAAU,EAAE;QACpC,mBAAmB,GAAG,UAAA,MAAM,EAAI,EAAA,OAAA,MAAM,CAAC,QAAQ,EAAE,CAAjB,EAAiB,CAAC;KACnD;AAAM,SAAA,IAAI,OAAO,eAAe,KAAK,UAAU,EAAE;AAChD,QAAA,mBAAmB,GAAG,UAAA,MAAM,IAAI,OAAA,eAAe,CAAC,MAAM,EAAE,EAAE,QAAQ,EAAE,CAAC,MAAM,CAAC,EAAE,CAAC,CAAA,EAAA,CAAC;KACjF;SAAM;;QAEL,mBAAmB,GAAG,UAAA,MAAM,EAAA,EAAI,OAAA,MAAM,CAAA,EAAA,CAAC;KACxC;AACD,IAAA,OAAO,mBAAmB,CAAC,CAAC,CAAC,CAAC;AAChC,CAAC,CAAC;AAMK,IAAI,gBAAgB,GAAG,UAAC,CAAc,EAAA;AAC3C,IAAA,IAAI,OAAO,CAAC,CAAC,QAAQ,KAAK,SAAS,EAAE;QACnC,gBAAgB,GAAG,UAAA,MAAM,EAAI,EAAA,OAAA,MAAM,CAAC,QAAQ,CAAf,EAAe,CAAC;KAC9C;SAAM;;AAEL,QAAA,gBAAgB,GAAG,UAAA,MAAM,EAAA,EAAI,OAAA,MAAM,CAAC,UAAU,KAAK,CAAC,CAAvB,EAAuB,CAAC;KACtD;AACD,IAAA,OAAO,gBAAgB,CAAC,CAAC,CAAC,CAAC;AAC7B,CAAC,CAAC;SAEc,gBAAgB,CAAC,MAAmB,EAAE,KAAa,EAAE,GAAW,EAAA;;;AAG9E,IAAA,IAAI,MAAM,CAAC,KAAK,EAAE;QAChB,OAAO,MAAM,CAAC,KAAK,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;KACjC;AACD,IAAA,IAAM,MAAM,GAAG,GAAG,GAAG,KAAK,CAAC;AAC3B,IAAA,IAAM,KAAK,GAAG,IAAI,WAAW,CAAC,MAAM,CAAC,CAAC;IACtC,kBAAkB,CAAC,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;AACpD,IAAA,OAAO,KAAK,CAAC;AACf,CAAC;AAMe,SAAA,SAAS,CAA6B,QAAW,EAAE,IAAO,EAAA;AACxE,IAAA,IAAM,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC;IAC5B,IAAI,IAAI,KAAK,SAAS,IAAI,IAAI,KAAK,IAAI,EAAE;AACvC,QAAA,OAAO,SAAS,CAAC;KAClB;AACD,IAAA,IAAI,OAAO,IAAI,KAAK,UAAU,EAAE;QAC9B,MAAM,IAAI,SAAS,CAAC,EAAG,CAAA,MAAA,CAAA,MAAM,CAAC,IAAI,CAAC,EAAoB,oBAAA,CAAA,CAAC,CAAC;KAC1D;AACD,IAAA,OAAO,IAAI,CAAC;AACd,CAAC;AAgBK,SAAU,2BAA2B,CAAI,kBAAyC,EAAA;;;;;AAKtF,IAAA,IAAM,YAAY,IAAA,EAAA,GAAA,EAAA;QAChB,EAAC,CAAAA,cAAM,CAAC,QAAQ,CAAG,GAAA,YAAA,EAAM,OAAA,kBAAkB,CAAC,QAAQ,CAAA,EAAA;WACrD,CAAC;;IAEF,IAAM,aAAa,IAAI,YAAA;;;;AACd,oBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,aAAA,SAAO,gBAAA,CAAA,aAAA,CAAA,YAAY,CAAA,CAAA,CAAA,CAAA,CAAA;AAAnB,oBAAA,KAAA,CAAA,EAAA,OAAA,CAAA,CAAA,YAAA,OAAA,CAAA,KAAA,CAAA,KAAA,CAAA,EAAA,CAAA,SAAmB,CAAA,CAAA,CAAA,CAAA;wEAAnB,EAAmB,CAAA,IAAA,EAAA,CAAA,CAAA,CAAA,CAAA;4BAA1B,OAA2B,CAAA,CAAA,aAAA,EAAA,CAAA,IAAA,EAAA,CAAA,CAAA;;;;AAC5B,KAAA,EAAE,CAAC,CAAC;;AAEL,IAAA,IAAM,UAAU,GAAG,aAAa,CAAC,IAAI,CAAC;AACtC,IAAA,OAAO,EAAE,QAAQ,EAAE,aAAa,EAAE,UAAU,EAAA,UAAA,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC;AAC9D,CAAC;AAED;AACO,IAAM,mBAAmB,GAC9B,CAAA,EAAA,GAAA,CAAAC,IAAA,GAAAD,cAAM,CAAC,aAAa,uCACpB,CAAA,EAAA,GAAAA,cAAM,CAAC,GAAG,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAAA,cAAA,EAAG,sBAAsB,CAAC,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GACpC,iBAAiB,CAAC;AAepB,SAAS,WAAW,CAClB,GAA2B,EAC3B,IAAa,EACb,MAAqC,EAAA;AADrC,IAAA,IAAA,IAAA,KAAA,KAAA,CAAA,EAAA,EAAA,IAAa,GAAA,MAAA,CAAA,EAG+B;AAC5C,IAAA,IAAI,MAAM,KAAK,SAAS,EAAE;AACxB,QAAA,IAAI,IAAI,KAAK,OAAO,EAAE;AACpB,YAAA,MAAM,GAAG,SAAS,CAAC,GAAuB,EAAE,mBAAmB,CAAC,CAAC;AACjE,YAAA,IAAI,MAAM,KAAK,SAAS,EAAE;gBACxB,IAAM,UAAU,GAAG,SAAS,CAAC,GAAkB,EAAEA,cAAM,CAAC,QAAQ,CAAC,CAAC;gBAClE,IAAM,kBAAkB,GAAG,WAAW,CAAC,GAAkB,EAAE,MAAM,EAAE,UAAU,CAAC,CAAC;AAC/E,gBAAA,OAAO,2BAA2B,CAAC,kBAAkB,CAAC,CAAC;aACxD;SACF;aAAM;YACL,MAAM,GAAG,SAAS,CAAC,GAAkB,EAAEA,cAAM,CAAC,QAAQ,CAAC,CAAC;SACzD;KACF;AACD,IAAA,IAAI,MAAM,KAAK,SAAS,EAAE;AACxB,QAAA,MAAM,IAAI,SAAS,CAAC,4BAA4B,CAAC,CAAC;KACnD;IACD,IAAM,QAAQ,GAAG,WAAW,CAAC,MAAM,EAAE,GAAG,EAAE,EAAE,CAAC,CAAC;AAC9C,IAAA,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,EAAE;AAC3B,QAAA,MAAM,IAAI,SAAS,CAAC,2CAA2C,CAAC,CAAC;KAClE;AACD,IAAA,IAAM,UAAU,GAAG,QAAQ,CAAC,IAAI,CAAC;IACjC,OAAO,EAAE,QAAQ,EAAA,QAAA,EAAE,UAAU,EAAA,UAAA,EAAE,IAAI,EAAE,KAAK,EAAkC,CAAC;AAC/E,CAAC;AAIK,SAAU,YAAY,CAAI,cAAsC,EAAA;AACpE,IAAA,IAAM,MAAM,GAAG,WAAW,CAAC,cAAc,CAAC,UAAU,EAAE,cAAc,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;AACnF,IAAA,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,EAAE;AACzB,QAAA,MAAM,IAAI,SAAS,CAAC,kDAAkD,CAAC,CAAC;KACzE;AACD,IAAA,OAAO,MAAM,CAAC;AAChB,CAAC;AAEK,SAAU,gBAAgB,CAC9B,UAA4C,EAAA;AAG5C,IAAA,OAAO,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;AAClC,CAAC;AAEK,SAAU,aAAa,CAAI,UAAkC,EAAA;IAEjE,OAAO,UAAU,CAAC,KAAK,CAAC;AAC1B;;ACpLA;;AAIA;AACO,IAAM,sBAAsB,IAAA,EAAA,GAAA,EAAA;;;AAGjC,IAAA,EAAA,CAAC,mBAAmB,CAApB,GAAA,YAAA;AACE,QAAA,OAAO,IAAI,CAAC;KACb;OACF,CAAC;AACF,MAAM,CAAC,cAAc,CAAC,sBAAsB,EAAE,mBAAmB,EAAE,EAAE,UAAU,EAAE,KAAK,EAAE,CAAC;;ACZzF;AAiCA,IAAA,+BAAA,kBAAA,YAAA;IAME,SAAY,+BAAA,CAAA,MAAsC,EAAE,aAAsB,EAAA;QAHlE,IAAe,CAAA,eAAA,GAA4D,SAAS,CAAC;QACrF,IAAW,CAAA,WAAA,GAAG,KAAK,CAAC;AAG1B,QAAA,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;AACtB,QAAA,IAAI,CAAC,cAAc,GAAG,aAAa,CAAC;KACrC;AAED,IAAA,+BAAA,CAAA,SAAA,CAAA,IAAI,GAAJ,YAAA;QAAA,IAMC,KAAA,GAAA,IAAA,CAAA;QALC,IAAM,SAAS,GAAG,YAAA,EAAM,OAAA,KAAI,CAAC,UAAU,EAAE,CAAjB,EAAiB,CAAC;AAC1C,QAAA,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe;YACzC,oBAAoB,CAAC,IAAI,CAAC,eAAe,EAAE,SAAS,EAAE,SAAS,CAAC;AAChE,YAAA,SAAS,EAAE,CAAC;QACd,OAAO,IAAI,CAAC,eAAe,CAAC;KAC7B,CAAA;IAED,+BAAM,CAAA,SAAA,CAAA,MAAA,GAAN,UAAO,KAAU,EAAA;QAAjB,IAKC,KAAA,GAAA,IAAA,CAAA;AAJC,QAAA,IAAM,WAAW,GAAG,YAAM,EAAA,OAAA,KAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAxB,EAAwB,CAAC;AACnD,QAAA,OAAO,IAAI,CAAC,eAAe;YACzB,oBAAoB,CAAC,IAAI,CAAC,eAAe,EAAE,WAAW,EAAE,WAAW,CAAC;AACpE,YAAA,WAAW,EAAE,CAAC;KACjB,CAAA;AAEO,IAAA,+BAAA,CAAA,SAAA,CAAA,UAAU,GAAlB,YAAA;QAAA,IAoCC,KAAA,GAAA,IAAA,CAAA;AAnCC,QAAA,IAAI,IAAI,CAAC,WAAW,EAAE;AACpB,YAAA,OAAO,OAAO,CAAC,OAAO,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC;SAC1D;AAED,QAAA,IAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CACuB;AAElD,QAAA,IAAI,cAAqE,CAAC;AAC1E,QAAA,IAAI,aAAqC,CAAC;AAC1C,QAAA,IAAM,OAAO,GAAG,UAAU,CAAqC,UAAC,OAAO,EAAE,MAAM,EAAA;YAC7E,cAAc,GAAG,OAAO,CAAC;YACzB,aAAa,GAAG,MAAM,CAAC;AACzB,SAAC,CAAC,CAAC;AACH,QAAA,IAAM,WAAW,GAAmB;YAClC,WAAW,EAAE,UAAA,KAAK,EAAA;AAChB,gBAAA,KAAI,CAAC,eAAe,GAAG,SAAS,CAAC;;;AAGjC,gBAAAE,eAAc,CAAC,YAAM,EAAA,OAAA,cAAc,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,CAA7C,EAA6C,CAAC,CAAC;aACrE;AACD,YAAA,WAAW,EAAE,YAAA;AACX,gBAAA,KAAI,CAAC,eAAe,GAAG,SAAS,CAAC;AACjC,gBAAA,KAAI,CAAC,WAAW,GAAG,IAAI,CAAC;gBACxB,kCAAkC,CAAC,MAAM,CAAC,CAAC;gBAC3C,cAAc,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC;aAClD;YACD,WAAW,EAAE,UAAA,MAAM,EAAA;AACjB,gBAAA,KAAI,CAAC,eAAe,GAAG,SAAS,CAAC;AACjC,gBAAA,KAAI,CAAC,WAAW,GAAG,IAAI,CAAC;gBACxB,kCAAkC,CAAC,MAAM,CAAC,CAAC;gBAC3C,aAAa,CAAC,MAAM,CAAC,CAAC;aACvB;SACF,CAAC;AACF,QAAA,+BAA+B,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;AACrD,QAAA,OAAO,OAAO,CAAC;KAChB,CAAA;IAEO,+BAAY,CAAA,SAAA,CAAA,YAAA,GAApB,UAAqB,KAAU,EAAA;AAC7B,QAAA,IAAI,IAAI,CAAC,WAAW,EAAE;AACpB,YAAA,OAAO,OAAO,CAAC,OAAO,CAAC,EAAE,KAAK,EAAA,KAAA,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC;SAC/C;AACD,QAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;AAExB,QAAA,IAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAEe;AAE1C,QAAA,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YACxB,IAAM,MAAM,GAAG,iCAAiC,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;YAChE,kCAAkC,CAAC,MAAM,CAAC,CAAC;AAC3C,YAAA,OAAO,oBAAoB,CAAC,MAAM,EAAE,YAAM,EAAA,QAAC,EAAE,KAAK,OAAA,EAAE,IAAI,EAAE,IAAI,EAAE,EAAtB,EAAuB,CAAC,CAAC;SACpE;QAED,kCAAkC,CAAC,MAAM,CAAC,CAAC;QAC3C,OAAO,mBAAmB,CAAC,EAAE,KAAK,EAAA,KAAA,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC;KACnD,CAAA;IACH,OAAC,+BAAA,CAAA;AAAD,CAAC,EAAA,CAAA,CAAA;AAWD,IAAM,oCAAoC,GAA6C;IACrF,IAAI,EAAA,YAAA;AACF,QAAA,IAAI,CAAC,6BAA6B,CAAC,IAAI,CAAC,EAAE;AACxC,YAAA,OAAO,mBAAmB,CAAC,sCAAsC,CAAC,MAAM,CAAC,CAAC,CAAC;SAC5E;AACD,QAAA,OAAO,IAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE,CAAC;KACvC;AAED,IAAA,MAAM,YAAiD,KAAU,EAAA;AAC/D,QAAA,IAAI,CAAC,6BAA6B,CAAC,IAAI,CAAC,EAAE;AACxC,YAAA,OAAO,mBAAmB,CAAC,sCAAsC,CAAC,QAAQ,CAAC,CAAC,CAAC;SAC9E;QACD,OAAO,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;KAC9C;CACK,CAAC;AACT,MAAM,CAAC,cAAc,CAAC,oCAAoC,EAAE,sBAAsB,CAAC,CAAC;AAEpF;AAEgB,SAAA,kCAAkC,CAAI,MAAyB,EACzB,aAAsB,EAAA;AAC1E,IAAA,IAAM,MAAM,GAAG,kCAAkC,CAAI,MAAM,CAAC,CAAC;IAC7D,IAAM,IAAI,GAAG,IAAI,+BAA+B,CAAC,MAAM,EAAE,aAAa,CAAC,CAAC;IACxE,IAAM,QAAQ,GAA2C,MAAM,CAAC,MAAM,CAAC,oCAAoC,CAAC,CAAC;AAC7G,IAAA,QAAQ,CAAC,kBAAkB,GAAG,IAAI,CAAC;AACnC,IAAA,OAAO,QAAQ,CAAC;AAClB,CAAC;AAED,SAAS,6BAA6B,CAAU,CAAM,EAAA;AACpD,IAAA,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE;AACpB,QAAA,OAAO,KAAK,CAAC;KACd;AAED,IAAA,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,oBAAoB,CAAC,EAAE;AAClE,QAAA,OAAO,KAAK,CAAC;KACd;AAED,IAAA,IAAI;;QAEF,OAAQ,CAA8C,CAAC,kBAAkB;AACvE,YAAA,+BAA+B,CAAC;KACnC;AAAC,IAAA,OAAA,EAAA,EAAM;AACN,QAAA,OAAO,KAAK,CAAC;KACd;AACH,CAAC;AAED;AAEA,SAAS,sCAAsC,CAAC,IAAY,EAAA;AAC1D,IAAA,OAAO,IAAI,SAAS,CAAC,sCAA+B,IAAI,EAAA,mDAAA,CAAmD,CAAC,CAAC;AAC/G;;ACjLA;AAEA;AACA,IAAM,WAAW,GAAwB,MAAM,CAAC,KAAK,IAAI,UAAU,CAAC,EAAA;;IAElE,OAAO,CAAC,KAAK,CAAC,CAAC;AACjB,CAAC;;ACFK,SAAU,mBAAmB,CAAC,CAAS,EAAA;AAC3C,IAAA,IAAI,OAAO,CAAC,KAAK,QAAQ,EAAE;AACzB,QAAA,OAAO,KAAK,CAAC;KACd;AAED,IAAA,IAAI,WAAW,CAAC,CAAC,CAAC,EAAE;AAClB,QAAA,OAAO,KAAK,CAAC;KACd;AAED,IAAA,IAAI,CAAC,GAAG,CAAC,EAAE;AACT,QAAA,OAAO,KAAK,CAAC;KACd;AAED,IAAA,OAAO,IAAI,CAAC;AACd,CAAC;AAEK,SAAU,iBAAiB,CAAC,CAA6B,EAAA;IAC7D,IAAM,MAAM,GAAG,gBAAgB,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,UAAU,EAAE,CAAC,CAAC,UAAU,GAAG,CAAC,CAAC,UAAU,CAAC,CAAC;AACrF,IAAA,OAAO,IAAI,UAAU,CAAC,MAAM,CAA0B,CAAC;AACzD;;ACTM,SAAU,YAAY,CAAI,SAAuC,EAAA;IAIrE,IAAM,IAAI,GAAG,SAAS,CAAC,MAAM,CAAC,KAAK,EAAG,CAAC;AACvC,IAAA,SAAS,CAAC,eAAe,IAAI,IAAI,CAAC,IAAI,CAAC;AACvC,IAAA,IAAI,SAAS,CAAC,eAAe,GAAG,CAAC,EAAE;AACjC,QAAA,SAAS,CAAC,eAAe,GAAG,CAAC,CAAC;KAC/B;IAED,OAAO,IAAI,CAAC,KAAK,CAAC;AACpB,CAAC;SAEe,oBAAoB,CAAI,SAAuC,EAAE,KAAQ,EAAE,IAAY,EAAA;IAGrG,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI,IAAI,KAAK,QAAQ,EAAE;AACnD,QAAA,MAAM,IAAI,UAAU,CAAC,sDAAsD,CAAC,CAAC;KAC9E;AAED,IAAA,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,KAAK,EAAA,KAAA,EAAE,IAAI,EAAA,IAAA,EAAE,CAAC,CAAC;AACvC,IAAA,SAAS,CAAC,eAAe,IAAI,IAAI,CAAC;AACpC,CAAC;AAEK,SAAU,cAAc,CAAI,SAAuC,EAAA;IAIvE,IAAM,IAAI,GAAG,SAAS,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;IACrC,OAAO,IAAI,CAAC,KAAK,CAAC;AACpB,CAAC;AAEK,SAAU,UAAU,CAAI,SAA4B,EAAA;AAGxD,IAAA,SAAS,CAAC,MAAM,GAAG,IAAI,WAAW,EAAK,CAAC;AACxC,IAAA,SAAS,CAAC,eAAe,GAAG,CAAC,CAAC;AAChC;;ACxBA,SAAS,qBAAqB,CAAC,IAAc,EAAA;IAC3C,OAAO,IAAI,KAAK,QAAQ,CAAC;AAC3B,CAAC;AAEK,SAAU,UAAU,CAAC,IAAqB,EAAA;AAC9C,IAAA,OAAO,qBAAqB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;AACjD,CAAC;AAEK,SAAU,0BAA0B,CAA4B,IAAmC,EAAA;AACvG,IAAA,IAAI,qBAAqB,CAAC,IAAI,CAAC,EAAE;AAC/B,QAAA,OAAO,CAAC,CAAC;KACV;IACD,OAAQ,IAAyC,CAAC,iBAAiB,CAAC;AACtE;;ACIA;;;;AAIG;AACH,IAAA,yBAAA,kBAAA,YAAA;AAME,IAAA,SAAA,yBAAA,GAAA;AACE,QAAA,MAAM,IAAI,SAAS,CAAC,qBAAqB,CAAC,CAAC;KAC5C;AAKD,IAAA,MAAA,CAAA,cAAA,CAAI,yBAAI,CAAA,SAAA,EAAA,MAAA,EAAA;AAHR;;AAEG;AACH,QAAA,GAAA,EAAA,YAAA;AACE,YAAA,IAAI,CAAC,2BAA2B,CAAC,IAAI,CAAC,EAAE;AACtC,gBAAA,MAAM,8BAA8B,CAAC,MAAM,CAAC,CAAC;aAC9C;YAED,OAAO,IAAI,CAAC,KAAK,CAAC;SACnB;;;AAAA,KAAA,CAAA,CAAA;IAUD,yBAAO,CAAA,SAAA,CAAA,OAAA,GAAP,UAAQ,YAAgC,EAAA;AACtC,QAAA,IAAI,CAAC,2BAA2B,CAAC,IAAI,CAAC,EAAE;AACtC,YAAA,MAAM,8BAA8B,CAAC,SAAS,CAAC,CAAC;SACjD;AACD,QAAA,sBAAsB,CAAC,YAAY,EAAE,CAAC,EAAE,SAAS,CAAC,CAAC;AACnD,QAAA,YAAY,GAAG,uCAAuC,CAAC,YAAY,EAAE,iBAAiB,CAAC,CAAC;AAExF,QAAA,IAAI,IAAI,CAAC,uCAAuC,KAAK,SAAS,EAAE;AAC9D,YAAA,MAAM,IAAI,SAAS,CAAC,wCAAwC,CAAC,CAAC;SAC/D;QAED,IAAI,gBAAgB,CAAC,IAAI,CAAC,KAAM,CAAC,MAAM,CAAC,EAAE;AACxC,YAAA,MAAM,IAAI,SAAS,CAAC,iFAAiF,CAAC,CAAC;SAI/D;AAE1C,QAAA,mCAAmC,CAAC,IAAI,CAAC,uCAAuC,EAAE,YAAY,CAAC,CAAC;KACjG,CAAA;IAUD,yBAAkB,CAAA,SAAA,CAAA,kBAAA,GAAlB,UAAmB,IAAgC,EAAA;AACjD,QAAA,IAAI,CAAC,2BAA2B,CAAC,IAAI,CAAC,EAAE;AACtC,YAAA,MAAM,8BAA8B,CAAC,oBAAoB,CAAC,CAAC;SAC5D;AACD,QAAA,sBAAsB,CAAC,IAAI,EAAE,CAAC,EAAE,oBAAoB,CAAC,CAAC;QAEtD,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE;AAC7B,YAAA,MAAM,IAAI,SAAS,CAAC,8CAA8C,CAAC,CAAC;SACrE;AAED,QAAA,IAAI,IAAI,CAAC,uCAAuC,KAAK,SAAS,EAAE;AAC9D,YAAA,MAAM,IAAI,SAAS,CAAC,wCAAwC,CAAC,CAAC;SAC/D;AAED,QAAA,IAAI,gBAAgB,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;AACjC,YAAA,MAAM,IAAI,SAAS,CAAC,gFAAgF,CAAC,CAAC;SACvG;AAED,QAAA,8CAA8C,CAAC,IAAI,CAAC,uCAAuC,EAAE,IAAI,CAAC,CAAC;KACpG,CAAA;IACH,OAAC,yBAAA,CAAA;AAAD,CAAC,EAAA,EAAA;AAED,MAAM,CAAC,gBAAgB,CAAC,yBAAyB,CAAC,SAAS,EAAE;AAC3D,IAAA,OAAO,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE;AAC7B,IAAA,kBAAkB,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE;AACxC,IAAA,IAAI,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE;AAC3B,CAAA,CAAC,CAAC;AACH,eAAe,CAAC,yBAAyB,CAAC,SAAS,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;AACxE,eAAe,CAAC,yBAAyB,CAAC,SAAS,CAAC,kBAAkB,EAAE,oBAAoB,CAAC,CAAC;AAC9F,IAAI,OAAOF,cAAM,CAAC,WAAW,KAAK,QAAQ,EAAE;IAC1C,MAAM,CAAC,cAAc,CAAC,yBAAyB,CAAC,SAAS,EAAEA,cAAM,CAAC,WAAW,EAAE;AAC7E,QAAA,KAAK,EAAE,2BAA2B;AAClC,QAAA,YAAY,EAAE,IAAI;AACnB,KAAA,CAAC,CAAC;AACL,CAAC;AAoCD;;;;AAIG;AACH,IAAA,4BAAA,kBAAA,YAAA;AA4BE,IAAA,SAAA,4BAAA,GAAA;AACE,QAAA,MAAM,IAAI,SAAS,CAAC,qBAAqB,CAAC,CAAC;KAC5C;AAKD,IAAA,MAAA,CAAA,cAAA,CAAI,4BAAW,CAAA,SAAA,EAAA,aAAA,EAAA;AAHf;;AAEG;AACH,QAAA,GAAA,EAAA,YAAA;AACE,YAAA,IAAI,CAAC,8BAA8B,CAAC,IAAI,CAAC,EAAE;AACzC,gBAAA,MAAM,uCAAuC,CAAC,aAAa,CAAC,CAAC;aAC9D;AAED,YAAA,OAAO,0CAA0C,CAAC,IAAI,CAAC,CAAC;SACzD;;;AAAA,KAAA,CAAA,CAAA;AAMD,IAAA,MAAA,CAAA,cAAA,CAAI,4BAAW,CAAA,SAAA,EAAA,aAAA,EAAA;AAJf;;;AAGG;AACH,QAAA,GAAA,EAAA,YAAA;AACE,YAAA,IAAI,CAAC,8BAA8B,CAAC,IAAI,CAAC,EAAE;AACzC,gBAAA,MAAM,uCAAuC,CAAC,aAAa,CAAC,CAAC;aAC9D;AAED,YAAA,OAAO,0CAA0C,CAAC,IAAI,CAAC,CAAC;SACzD;;;AAAA,KAAA,CAAA,CAAA;AAED;;;AAGG;AACH,IAAA,4BAAA,CAAA,SAAA,CAAA,KAAK,GAAL,YAAA;AACE,QAAA,IAAI,CAAC,8BAA8B,CAAC,IAAI,CAAC,EAAE;AACzC,YAAA,MAAM,uCAAuC,CAAC,OAAO,CAAC,CAAC;SACxD;AAED,QAAA,IAAI,IAAI,CAAC,eAAe,EAAE;AACxB,YAAA,MAAM,IAAI,SAAS,CAAC,4DAA4D,CAAC,CAAC;SACnF;AAED,QAAA,IAAM,KAAK,GAAG,IAAI,CAAC,6BAA6B,CAAC,MAAM,CAAC;AACxD,QAAA,IAAI,KAAK,KAAK,UAAU,EAAE;AACxB,YAAA,MAAM,IAAI,SAAS,CAAC,yBAAkB,KAAK,EAAA,2DAAA,CAA2D,CAAC,CAAC;SACzG;QAED,iCAAiC,CAAC,IAAI,CAAC,CAAC;KACzC,CAAA;IAOD,4BAAO,CAAA,SAAA,CAAA,OAAA,GAAP,UAAQ,KAAiC,EAAA;AACvC,QAAA,IAAI,CAAC,8BAA8B,CAAC,IAAI,CAAC,EAAE;AACzC,YAAA,MAAM,uCAAuC,CAAC,SAAS,CAAC,CAAC;SAC1D;AAED,QAAA,sBAAsB,CAAC,KAAK,EAAE,CAAC,EAAE,SAAS,CAAC,CAAC;QAC5C,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;AAC9B,YAAA,MAAM,IAAI,SAAS,CAAC,oCAAoC,CAAC,CAAC;SAC3D;AACD,QAAA,IAAI,KAAK,CAAC,UAAU,KAAK,CAAC,EAAE;AAC1B,YAAA,MAAM,IAAI,SAAS,CAAC,qCAAqC,CAAC,CAAC;SAC5D;QACD,IAAI,KAAK,CAAC,MAAM,CAAC,UAAU,KAAK,CAAC,EAAE;AACjC,YAAA,MAAM,IAAI,SAAS,CAAC,8CAA8C,CAAC,CAAC;SACrE;AAED,QAAA,IAAI,IAAI,CAAC,eAAe,EAAE;AACxB,YAAA,MAAM,IAAI,SAAS,CAAC,8BAA8B,CAAC,CAAC;SACrD;AAED,QAAA,IAAM,KAAK,GAAG,IAAI,CAAC,6BAA6B,CAAC,MAAM,CAAC;AACxD,QAAA,IAAI,KAAK,KAAK,UAAU,EAAE;AACxB,YAAA,MAAM,IAAI,SAAS,CAAC,yBAAkB,KAAK,EAAA,gEAAA,CAAgE,CAAC,CAAC;SAC9G;AAED,QAAA,mCAAmC,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;KAClD,CAAA;AAED;;AAEG;IACH,4BAAK,CAAA,SAAA,CAAA,KAAA,GAAL,UAAM,CAAkB,EAAA;AAAlB,QAAA,IAAA,CAAA,KAAA,KAAA,CAAA,EAAA,EAAA,CAAkB,GAAA,SAAA,CAAA,EAAA;AACtB,QAAA,IAAI,CAAC,8BAA8B,CAAC,IAAI,CAAC,EAAE;AACzC,YAAA,MAAM,uCAAuC,CAAC,OAAO,CAAC,CAAC;SACxD;AAED,QAAA,iCAAiC,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;KAC5C,CAAA;;AAGD,IAAA,4BAAA,CAAA,SAAA,CAAC,WAAW,CAAC,GAAb,UAAc,MAAW,EAAA;QACvB,iDAAiD,CAAC,IAAI,CAAC,CAAC;QAExD,UAAU,CAAC,IAAI,CAAC,CAAC;QAEjB,IAAM,MAAM,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;QAC7C,2CAA2C,CAAC,IAAI,CAAC,CAAC;AAClD,QAAA,OAAO,MAAM,CAAC;KACf,CAAA;;AAGD,IAAA,4BAAA,CAAA,SAAA,CAAC,SAAS,CAAC,GAAX,UAAY,WAA+C,EAAA;AACzD,QAAA,IAAM,MAAM,GAAG,IAAI,CAAC,6BAA6B,CACF;AAE/C,QAAA,IAAI,IAAI,CAAC,eAAe,GAAG,CAAC,EAAE;AAG5B,YAAA,oDAAoD,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;YACxE,OAAO;SACR;AAED,QAAA,IAAM,qBAAqB,GAAG,IAAI,CAAC,sBAAsB,CAAC;AAC1D,QAAA,IAAI,qBAAqB,KAAK,SAAS,EAAE;YACvC,IAAI,MAAM,SAAa,CAAC;AACxB,YAAA,IAAI;AACF,gBAAA,MAAM,GAAG,IAAI,WAAW,CAAC,qBAAqB,CAAC,CAAC;aACjD;YAAC,OAAO,OAAO,EAAE;AAChB,gBAAA,WAAW,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;gBACjC,OAAO;aACR;AAED,YAAA,IAAM,kBAAkB,GAA8B;AACpD,gBAAA,MAAM,EAAA,MAAA;AACN,gBAAA,gBAAgB,EAAE,qBAAqB;AACvC,gBAAA,UAAU,EAAE,CAAC;AACb,gBAAA,UAAU,EAAE,qBAAqB;AACjC,gBAAA,WAAW,EAAE,CAAC;AACd,gBAAA,WAAW,EAAE,CAAC;AACd,gBAAA,WAAW,EAAE,CAAC;AACd,gBAAA,eAAe,EAAE,UAAU;AAC3B,gBAAA,UAAU,EAAE,SAAS;aACtB,CAAC;AAEF,YAAA,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;SACjD;AAED,QAAA,4BAA4B,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;QAClD,4CAA4C,CAAC,IAAI,CAAC,CAAC;KACpD,CAAA;;IAGD,4BAAC,CAAA,SAAA,CAAA,YAAY,CAAC,GAAd,YAAA;QACE,IAAI,IAAI,CAAC,iBAAiB,CAAC,MAAM,GAAG,CAAC,EAAE;YACrC,IAAM,aAAa,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,CAAC;AACpD,YAAA,aAAa,CAAC,UAAU,GAAG,MAAM,CAAC;AAElC,YAAA,IAAI,CAAC,iBAAiB,GAAG,IAAI,WAAW,EAAE,CAAC;AAC3C,YAAA,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;SAC5C;KACF,CAAA;IACH,OAAC,4BAAA,CAAA;AAAD,CAAC,EAAA,EAAA;AAED,MAAM,CAAC,gBAAgB,CAAC,4BAA4B,CAAC,SAAS,EAAE;AAC9D,IAAA,KAAK,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE;AAC3B,IAAA,OAAO,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE;AAC7B,IAAA,KAAK,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE;AAC3B,IAAA,WAAW,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE;AACjC,IAAA,WAAW,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE;AAClC,CAAA,CAAC,CAAC;AACH,eAAe,CAAC,4BAA4B,CAAC,SAAS,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;AACvE,eAAe,CAAC,4BAA4B,CAAC,SAAS,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;AAC3E,eAAe,CAAC,4BAA4B,CAAC,SAAS,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;AACvE,IAAI,OAAOA,cAAM,CAAC,WAAW,KAAK,QAAQ,EAAE;IAC1C,MAAM,CAAC,cAAc,CAAC,4BAA4B,CAAC,SAAS,EAAEA,cAAM,CAAC,WAAW,EAAE;AAChF,QAAA,KAAK,EAAE,8BAA8B;AACrC,QAAA,YAAY,EAAE,IAAI;AACnB,KAAA,CAAC,CAAC;AACL,CAAC;AAED;AAEM,SAAU,8BAA8B,CAAC,CAAM,EAAA;AACnD,IAAA,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE;AACpB,QAAA,OAAO,KAAK,CAAC;KACd;AAED,IAAA,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,+BAA+B,CAAC,EAAE;AAC7E,QAAA,OAAO,KAAK,CAAC;KACd;IAED,OAAO,CAAC,YAAY,4BAA4B,CAAC;AACnD,CAAC;AAED,SAAS,2BAA2B,CAAC,CAAM,EAAA;AACzC,IAAA,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE;AACpB,QAAA,OAAO,KAAK,CAAC;KACd;AAED,IAAA,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,yCAAyC,CAAC,EAAE;AACvF,QAAA,OAAO,KAAK,CAAC;KACd;IAED,OAAO,CAAC,YAAY,yBAAyB,CAAC;AAChD,CAAC;AAED,SAAS,4CAA4C,CAAC,UAAwC,EAAA;AAC5F,IAAA,IAAM,UAAU,GAAG,0CAA0C,CAAC,UAAU,CAAC,CAAC;IAC1E,IAAI,CAAC,UAAU,EAAE;QACf,OAAO;KACR;AAED,IAAA,IAAI,UAAU,CAAC,QAAQ,EAAE;AACvB,QAAA,UAAU,CAAC,UAAU,GAAG,IAAI,CAAC;QAC7B,OAAO;KAGsB;AAE/B,IAAA,UAAU,CAAC,QAAQ,GAAG,IAAI,CAAC;;AAG3B,IAAA,IAAM,WAAW,GAAG,UAAU,CAAC,cAAc,EAAE,CAAC;IAChD,WAAW,CACT,WAAW,EACX,YAAA;AACE,QAAA,UAAU,CAAC,QAAQ,GAAG,KAAK,CAAC;AAE5B,QAAA,IAAI,UAAU,CAAC,UAAU,EAAE;AACzB,YAAA,UAAU,CAAC,UAAU,GAAG,KAAK,CAAC;YAC9B,4CAA4C,CAAC,UAAU,CAAC,CAAC;SAC1D;AAED,QAAA,OAAO,IAAI,CAAC;KACb,EACD,UAAA,CAAC,EAAA;AACC,QAAA,iCAAiC,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC;AACjD,QAAA,OAAO,IAAI,CAAC;AACd,KAAC,CACF,CAAC;AACJ,CAAC;AAED,SAAS,iDAAiD,CAAC,UAAwC,EAAA;IACjG,iDAAiD,CAAC,UAAU,CAAC,CAAC;AAC9D,IAAA,UAAU,CAAC,iBAAiB,GAAG,IAAI,WAAW,EAAE,CAAC;AACnD,CAAC;AAED,SAAS,oDAAoD,CAC3D,MAA0B,EAC1B,kBAAyC,EAAA;IAKzC,IAAI,IAAI,GAAG,KAAK,CAAC;AACjB,IAAA,IAAI,MAAM,CAAC,MAAM,KAAK,QAAQ,EAAE;QAE9B,IAAI,GAAG,IAAI,CAAC;KACb;AAED,IAAA,IAAM,UAAU,GAAG,qDAAqD,CAAI,kBAAkB,CAAC,CAAC;AAChG,IAAA,IAAI,kBAAkB,CAAC,UAAU,KAAK,SAAS,EAAE;AAC/C,QAAA,gCAAgC,CAAC,MAAM,EAAE,UAA8C,EAAE,IAAI,CAAC,CAAC;KAChG;SAAM;AAEL,QAAA,oCAAoC,CAAC,MAAM,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC;KAChE;AACH,CAAC;AAED,SAAS,qDAAqD,CAC5D,kBAAyC,EAAA;AAEzC,IAAA,IAAM,WAAW,GAAG,kBAAkB,CAAC,WAAW,CAAC;AACnD,IAAA,IAAM,WAAW,GAAG,kBAAkB,CAAC,WAAW,CAGV;AAExC,IAAA,OAAO,IAAI,kBAAkB,CAAC,eAAe,CAC3C,kBAAkB,CAAC,MAAM,EAAE,kBAAkB,CAAC,UAAU,EAAE,WAAW,GAAG,WAAW,CAAM,CAAC;AAC9F,CAAC;AAED,SAAS,+CAA+C,CAAC,UAAwC,EACxC,MAAmB,EACnB,UAAkB,EAClB,UAAkB,EAAA;AACzE,IAAA,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,MAAM,EAAA,MAAA,EAAE,UAAU,YAAA,EAAE,UAAU,EAAA,UAAA,EAAE,CAAC,CAAC;AAC3D,IAAA,UAAU,CAAC,eAAe,IAAI,UAAU,CAAC;AAC3C,CAAC;AAED,SAAS,qDAAqD,CAAC,UAAwC,EACxC,MAAmB,EACnB,UAAkB,EAClB,UAAkB,EAAA;AAC/E,IAAA,IAAI,WAAW,CAAC;AAChB,IAAA,IAAI;QACF,WAAW,GAAG,gBAAgB,CAAC,MAAM,EAAE,UAAU,EAAE,UAAU,GAAG,UAAU,CAAC,CAAC;KAC7E;IAAC,OAAO,MAAM,EAAE;AACf,QAAA,iCAAiC,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;AACtD,QAAA,MAAM,MAAM,CAAC;KACd;IACD,+CAA+C,CAAC,UAAU,EAAE,WAAW,EAAE,CAAC,EAAE,UAAU,CAAC,CAAC;AAC1F,CAAC;AAED,SAAS,0DAA0D,CAAC,UAAwC,EACxC,eAAmC,EAAA;AAErG,IAAA,IAAI,eAAe,CAAC,WAAW,GAAG,CAAC,EAAE;AACnC,QAAA,qDAAqD,CACnD,UAAU,EACV,eAAe,CAAC,MAAM,EACtB,eAAe,CAAC,UAAU,EAC1B,eAAe,CAAC,WAAW,CAC5B,CAAC;KACH;IACD,gDAAgD,CAAC,UAAU,CAAC,CAAC;AAC/D,CAAC;AAED,SAAS,2DAA2D,CAAC,UAAwC,EACxC,kBAAsC,EAAA;AACzG,IAAA,IAAM,cAAc,GAAG,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,eAAe,EAC1B,kBAAkB,CAAC,UAAU,GAAG,kBAAkB,CAAC,WAAW,CAAC,CAAC;AAChG,IAAA,IAAM,cAAc,GAAG,kBAAkB,CAAC,WAAW,GAAG,cAAc,CAAC;IAEvE,IAAI,yBAAyB,GAAG,cAAc,CAAC;IAC/C,IAAI,KAAK,GAAG,KAAK,CACuD;AACxE,IAAA,IAAM,cAAc,GAAG,cAAc,GAAG,kBAAkB,CAAC,WAAW,CAAC;AACvE,IAAA,IAAM,eAAe,GAAG,cAAc,GAAG,cAAc,CAAC;;;AAGxD,IAAA,IAAI,eAAe,IAAI,kBAAkB,CAAC,WAAW,EAAE;AACrD,QAAA,yBAAyB,GAAG,eAAe,GAAG,kBAAkB,CAAC,WAAW,CAAC;QAC7E,KAAK,GAAG,IAAI,CAAC;KACd;AAED,IAAA,IAAM,KAAK,GAAG,UAAU,CAAC,MAAM,CAAC;AAEhC,IAAA,OAAO,yBAAyB,GAAG,CAAC,EAAE;AACpC,QAAA,IAAM,WAAW,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC;AAEjC,QAAA,IAAM,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,yBAAyB,EAAE,WAAW,CAAC,UAAU,CAAC,CAAC;QAEhF,IAAM,SAAS,GAAG,kBAAkB,CAAC,UAAU,GAAG,kBAAkB,CAAC,WAAW,CAAC;AACjF,QAAA,kBAAkB,CAAC,kBAAkB,CAAC,MAAM,EAAE,SAAS,EAAE,WAAW,CAAC,MAAM,EAAE,WAAW,CAAC,UAAU,EAAE,WAAW,CAAC,CAAC;AAElH,QAAA,IAAI,WAAW,CAAC,UAAU,KAAK,WAAW,EAAE;YAC1C,KAAK,CAAC,KAAK,EAAE,CAAC;SACf;aAAM;AACL,YAAA,WAAW,CAAC,UAAU,IAAI,WAAW,CAAC;AACtC,YAAA,WAAW,CAAC,UAAU,IAAI,WAAW,CAAC;SACvC;AACD,QAAA,UAAU,CAAC,eAAe,IAAI,WAAW,CAAC;AAE1C,QAAA,sDAAsD,CAAC,UAAU,EAAE,WAAW,EAAE,kBAAkB,CAAC,CAAC;QAEpG,yBAAyB,IAAI,WAAW,CAAC;KAC1C;AAQD,IAAA,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAS,sDAAsD,CAAC,UAAwC,EACxC,IAAY,EACZ,kBAAsC,EAAA;AAGpG,IAAA,kBAAkB,CAAC,WAAW,IAAI,IAAI,CAAC;AACzC,CAAC;AAED,SAAS,4CAA4C,CAAC,UAAwC,EAAA;IAG5F,IAAI,UAAU,CAAC,eAAe,KAAK,CAAC,IAAI,UAAU,CAAC,eAAe,EAAE;QAClE,2CAA2C,CAAC,UAAU,CAAC,CAAC;AACxD,QAAA,mBAAmB,CAAC,UAAU,CAAC,6BAA6B,CAAC,CAAC;KAC/D;SAAM;QACL,4CAA4C,CAAC,UAAU,CAAC,CAAC;KAC1D;AACH,CAAC;AAED,SAAS,iDAAiD,CAAC,UAAwC,EAAA;AACjG,IAAA,IAAI,UAAU,CAAC,YAAY,KAAK,IAAI,EAAE;QACpC,OAAO;KACR;AAED,IAAA,UAAU,CAAC,YAAY,CAAC,uCAAuC,GAAG,SAAU,CAAC;AAC7E,IAAA,UAAU,CAAC,YAAY,CAAC,KAAK,GAAG,IAAK,CAAC;AACtC,IAAA,UAAU,CAAC,YAAY,GAAG,IAAI,CAAC;AACjC,CAAC;AAED,SAAS,gEAAgE,CAAC,UAAwC,EAAA;IAGhH,OAAO,UAAU,CAAC,iBAAiB,CAAC,MAAM,GAAG,CAAC,EAAE;AAC9C,QAAA,IAAI,UAAU,CAAC,eAAe,KAAK,CAAC,EAAE;YACpC,OAAO;SACR;QAED,IAAM,kBAAkB,GAAG,UAAU,CAAC,iBAAiB,CAAC,IAAI,EAAE,CACb;AAEjD,QAAA,IAAI,2DAA2D,CAAC,UAAU,EAAE,kBAAkB,CAAC,EAAE;YAC/F,gDAAgD,CAAC,UAAU,CAAC,CAAC;AAE7D,YAAA,oDAAoD,CAClD,UAAU,CAAC,6BAA6B,EACxC,kBAAkB,CACnB,CAAC;SACH;KACF;AACH,CAAC;AAED,SAAS,yDAAyD,CAAC,UAAwC,EAAA;AACzG,IAAA,IAAM,MAAM,GAAG,UAAU,CAAC,6BAA6B,CAAC,OAAO,CACjB;IAC9C,OAAO,MAAM,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE;AACtC,QAAA,IAAI,UAAU,CAAC,eAAe,KAAK,CAAC,EAAE;YACpC,OAAO;SACR;QACD,IAAM,WAAW,GAAG,MAAM,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;AACjD,QAAA,oDAAoD,CAAC,UAAU,EAAE,WAAW,CAAC,CAAC;KAC/E;AACH,CAAC;AAEK,SAAU,oCAAoC,CAClD,UAAwC,EACxC,IAAO,EACP,GAAW,EACX,eAAmC,EAAA;AAEnC,IAAA,IAAM,MAAM,GAAG,UAAU,CAAC,6BAA6B,CAAC;AAExD,IAAA,IAAM,IAAI,GAAG,IAAI,CAAC,WAA4C,CAAC;AAC/D,IAAA,IAAM,WAAW,GAAG,0BAA0B,CAAC,IAAI,CAAC,CAAC;IAE7C,IAAA,UAAU,GAAiB,IAAI,CAAA,UAArB,EAAE,UAAU,GAAK,IAAI,CAAA,UAAT,CAAU;AAExC,IAAA,IAAM,WAAW,GAAG,GAAG,GAAG,WAAW,CAEG;AAExC,IAAA,IAAI,MAAmB,CAAC;AACxB,IAAA,IAAI;AACF,QAAA,MAAM,GAAG,mBAAmB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;KAC3C;IAAC,OAAO,CAAC,EAAE;AACV,QAAA,eAAe,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;QAC/B,OAAO;KACR;AAED,IAAA,IAAM,kBAAkB,GAA8B;AACpD,QAAA,MAAM,EAAA,MAAA;QACN,gBAAgB,EAAE,MAAM,CAAC,UAAU;AACnC,QAAA,UAAU,EAAA,UAAA;AACV,QAAA,UAAU,EAAA,UAAA;AACV,QAAA,WAAW,EAAE,CAAC;AACd,QAAA,WAAW,EAAA,WAAA;AACX,QAAA,WAAW,EAAA,WAAA;AACX,QAAA,eAAe,EAAE,IAAI;AACrB,QAAA,UAAU,EAAE,MAAM;KACnB,CAAC;IAEF,IAAI,UAAU,CAAC,iBAAiB,CAAC,MAAM,GAAG,CAAC,EAAE;AAC3C,QAAA,UAAU,CAAC,iBAAiB,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;;;;AAMtD,QAAA,gCAAgC,CAAC,MAAM,EAAE,eAAe,CAAC,CAAC;QAC1D,OAAO;KACR;AAED,IAAA,IAAI,MAAM,CAAC,MAAM,KAAK,QAAQ,EAAE;AAC9B,QAAA,IAAM,SAAS,GAAG,IAAI,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,kBAAkB,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC;AACxF,QAAA,eAAe,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;QACvC,OAAO;KACR;AAED,IAAA,IAAI,UAAU,CAAC,eAAe,GAAG,CAAC,EAAE;AAClC,QAAA,IAAI,2DAA2D,CAAC,UAAU,EAAE,kBAAkB,CAAC,EAAE;AAC/F,YAAA,IAAM,UAAU,GAAG,qDAAqD,CAAI,kBAAkB,CAAC,CAAC;YAEhG,4CAA4C,CAAC,UAAU,CAAC,CAAC;AAEzD,YAAA,eAAe,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;YACxC,OAAO;SACR;AAED,QAAA,IAAI,UAAU,CAAC,eAAe,EAAE;AAC9B,YAAA,IAAM,CAAC,GAAG,IAAI,SAAS,CAAC,yDAAyD,CAAC,CAAC;AACnF,YAAA,iCAAiC,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC;AAEjD,YAAA,eAAe,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;YAC/B,OAAO;SACR;KACF;AAED,IAAA,UAAU,CAAC,iBAAiB,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;AAEtD,IAAA,gCAAgC,CAAI,MAAM,EAAE,eAAe,CAAC,CAAC;IAC7D,4CAA4C,CAAC,UAAU,CAAC,CAAC;AAC3D,CAAC;AAED,SAAS,gDAAgD,CAAC,UAAwC,EACxC,eAAmC,EAAA;AAG3F,IAAA,IAAI,eAAe,CAAC,UAAU,KAAK,MAAM,EAAE;QACzC,gDAAgD,CAAC,UAAU,CAAC,CAAC;KAC9D;AAED,IAAA,IAAM,MAAM,GAAG,UAAU,CAAC,6BAA6B,CAAC;AACxD,IAAA,IAAI,2BAA2B,CAAC,MAAM,CAAC,EAAE;AACvC,QAAA,OAAO,oCAAoC,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE;AACvD,YAAA,IAAM,kBAAkB,GAAG,gDAAgD,CAAC,UAAU,CAAC,CAAC;AACxF,YAAA,oDAAoD,CAAC,MAAM,EAAE,kBAAkB,CAAC,CAAC;SAClF;KACF;AACH,CAAC;AAED,SAAS,kDAAkD,CAAC,UAAwC,EACxC,YAAoB,EACpB,kBAAsC,EAAA;AAGhG,IAAA,sDAAsD,CAAC,UAAU,EAAE,YAAY,EAAE,kBAAkB,CAAC,CAAC;AAErG,IAAA,IAAI,kBAAkB,CAAC,UAAU,KAAK,MAAM,EAAE;AAC5C,QAAA,0DAA0D,CAAC,UAAU,EAAE,kBAAkB,CAAC,CAAC;QAC3F,gEAAgE,CAAC,UAAU,CAAC,CAAC;QAC7E,OAAO;KACR;IAED,IAAI,kBAAkB,CAAC,WAAW,GAAG,kBAAkB,CAAC,WAAW,EAAE;;;QAGnE,OAAO;KACR;IAED,gDAAgD,CAAC,UAAU,CAAC,CAAC;IAE7D,IAAM,aAAa,GAAG,kBAAkB,CAAC,WAAW,GAAG,kBAAkB,CAAC,WAAW,CAAC;AACtF,IAAA,IAAI,aAAa,GAAG,CAAC,EAAE;QACrB,IAAM,GAAG,GAAG,kBAAkB,CAAC,UAAU,GAAG,kBAAkB,CAAC,WAAW,CAAC;AAC3E,QAAA,qDAAqD,CACnD,UAAU,EACV,kBAAkB,CAAC,MAAM,EACzB,GAAG,GAAG,aAAa,EACnB,aAAa,CACd,CAAC;KACH;AAED,IAAA,kBAAkB,CAAC,WAAW,IAAI,aAAa,CAAC;AAChD,IAAA,oDAAoD,CAAC,UAAU,CAAC,6BAA6B,EAAE,kBAAkB,CAAC,CAAC;IAEnH,gEAAgE,CAAC,UAAU,CAAC,CAAC;AAC/E,CAAC;AAED,SAAS,2CAA2C,CAAC,UAAwC,EAAE,YAAoB,EAAA;IACjH,IAAM,eAAe,GAAG,UAAU,CAAC,iBAAiB,CAAC,IAAI,EAAE,CACJ;IAEvD,iDAAiD,CAAC,UAAU,CAAC,CAAC;AAE9D,IAAA,IAAM,KAAK,GAAG,UAAU,CAAC,6BAA6B,CAAC,MAAM,CAAC;AAC9D,IAAA,IAAI,KAAK,KAAK,QAAQ,EAAE;AAEtB,QAAA,gDAAgD,CAAC,UAAU,EAAE,eAAe,CAAC,CAAC;KAC/E;SAAM;AAGL,QAAA,kDAAkD,CAAC,UAAU,EAAE,YAAY,EAAE,eAAe,CAAC,CAAC;KAC/F;IAED,4CAA4C,CAAC,UAAU,CAAC,CAAC;AAC3D,CAAC;AAED,SAAS,gDAAgD,CACvD,UAAwC,EAAA;IAGxC,IAAM,UAAU,GAAG,UAAU,CAAC,iBAAiB,CAAC,KAAK,EAAG,CAAC;AACzD,IAAA,OAAO,UAAU,CAAC;AACpB,CAAC;AAED,SAAS,0CAA0C,CAAC,UAAwC,EAAA;AAC1F,IAAA,IAAM,MAAM,GAAG,UAAU,CAAC,6BAA6B,CAAC;AAExD,IAAA,IAAI,MAAM,CAAC,MAAM,KAAK,UAAU,EAAE;AAChC,QAAA,OAAO,KAAK,CAAC;KACd;AAED,IAAA,IAAI,UAAU,CAAC,eAAe,EAAE;AAC9B,QAAA,OAAO,KAAK,CAAC;KACd;AAED,IAAA,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE;AACxB,QAAA,OAAO,KAAK,CAAC;KACd;AAED,IAAA,IAAI,8BAA8B,CAAC,MAAM,CAAC,IAAI,gCAAgC,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE;AAC1F,QAAA,OAAO,IAAI,CAAC;KACb;AAED,IAAA,IAAI,2BAA2B,CAAC,MAAM,CAAC,IAAI,oCAAoC,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE;AAC3F,QAAA,OAAO,IAAI,CAAC;KACb;AAED,IAAA,IAAM,WAAW,GAAG,0CAA0C,CAAC,UAAU,CAAC,CAC7C;AAC7B,IAAA,IAAI,WAAY,GAAG,CAAC,EAAE;AACpB,QAAA,OAAO,IAAI,CAAC;KACb;AAED,IAAA,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAS,2CAA2C,CAAC,UAAwC,EAAA;AAC3F,IAAA,UAAU,CAAC,cAAc,GAAG,SAAU,CAAC;AACvC,IAAA,UAAU,CAAC,gBAAgB,GAAG,SAAU,CAAC;AAC3C,CAAC;AAED;AAEM,SAAU,iCAAiC,CAAC,UAAwC,EAAA;AACxF,IAAA,IAAM,MAAM,GAAG,UAAU,CAAC,6BAA6B,CAAC;IAExD,IAAI,UAAU,CAAC,eAAe,IAAI,MAAM,CAAC,MAAM,KAAK,UAAU,EAAE;QAC9D,OAAO;KACR;AAED,IAAA,IAAI,UAAU,CAAC,eAAe,GAAG,CAAC,EAAE;AAClC,QAAA,UAAU,CAAC,eAAe,GAAG,IAAI,CAAC;QAElC,OAAO;KACR;IAED,IAAI,UAAU,CAAC,iBAAiB,CAAC,MAAM,GAAG,CAAC,EAAE;QAC3C,IAAM,oBAAoB,GAAG,UAAU,CAAC,iBAAiB,CAAC,IAAI,EAAE,CAAC;QACjE,IAAI,oBAAoB,CAAC,WAAW,GAAG,oBAAoB,CAAC,WAAW,KAAK,CAAC,EAAE;AAC7E,YAAA,IAAM,CAAC,GAAG,IAAI,SAAS,CAAC,yDAAyD,CAAC,CAAC;AACnF,YAAA,iCAAiC,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC;AAEjD,YAAA,MAAM,CAAC,CAAC;SACT;KACF;IAED,2CAA2C,CAAC,UAAU,CAAC,CAAC;IACxD,mBAAmB,CAAC,MAAM,CAAC,CAAC;AAC9B,CAAC;AAEe,SAAA,mCAAmC,CACjD,UAAwC,EACxC,KAAiC,EAAA;AAEjC,IAAA,IAAM,MAAM,GAAG,UAAU,CAAC,6BAA6B,CAAC;IAExD,IAAI,UAAU,CAAC,eAAe,IAAI,MAAM,CAAC,MAAM,KAAK,UAAU,EAAE;QAC9D,OAAO;KACR;AAEO,IAAA,IAAA,MAAM,GAA6B,KAAK,CAAA,MAAlC,EAAE,UAAU,GAAiB,KAAK,CAAA,UAAtB,EAAE,UAAU,GAAK,KAAK,WAAV,CAAW;AACjD,IAAA,IAAI,gBAAgB,CAAC,MAAM,CAAC,EAAE;AAC5B,QAAA,MAAM,IAAI,SAAS,CAAC,uDAAuD,CAAC,CAAC;KAC9E;AACD,IAAA,IAAM,iBAAiB,GAAG,mBAAmB,CAAC,MAAM,CAAC,CAAC;IAEtD,IAAI,UAAU,CAAC,iBAAiB,CAAC,MAAM,GAAG,CAAC,EAAE;QAC3C,IAAM,oBAAoB,GAAG,UAAU,CAAC,iBAAiB,CAAC,IAAI,EAAE,CAAC;AACjE,QAAA,IAAI,gBAAgB,CAAC,oBAAoB,CAAC,MAAM,CAAC,EAAE;AACjD,YAAA,MAAM,IAAI,SAAS,CACjB,6FAA6F,CAC9F,CAAC;SACH;QACD,iDAAiD,CAAC,UAAU,CAAC,CAAC;QAC9D,oBAAoB,CAAC,MAAM,GAAG,mBAAmB,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC;AAC/E,QAAA,IAAI,oBAAoB,CAAC,UAAU,KAAK,MAAM,EAAE;AAC9C,YAAA,0DAA0D,CAAC,UAAU,EAAE,oBAAoB,CAAC,CAAC;SAC9F;KACF;AAED,IAAA,IAAI,8BAA8B,CAAC,MAAM,CAAC,EAAE;QAC1C,yDAAyD,CAAC,UAAU,CAAC,CAAC;AACtE,QAAA,IAAI,gCAAgC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;YAElD,+CAA+C,CAAC,UAAU,EAAE,iBAAiB,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC;SACxG;aAAM;YAEL,IAAI,UAAU,CAAC,iBAAiB,CAAC,MAAM,GAAG,CAAC,EAAE;gBAE3C,gDAAgD,CAAC,UAAU,CAAC,CAAC;aAC9D;YACD,IAAM,eAAe,GAAG,IAAI,UAAU,CAAC,iBAAiB,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC;AAClF,YAAA,gCAAgC,CAAC,MAAM,EAAE,eAAwC,EAAE,KAAK,CAAC,CAAC;SAC3F;KACF;AAAM,SAAA,IAAI,2BAA2B,CAAC,MAAM,CAAC,EAAE;;QAE9C,+CAA+C,CAAC,UAAU,EAAE,iBAAiB,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC;QACvG,gEAAgE,CAAC,UAAU,CAAC,CAAC;KAC9E;SAAM;QAEL,+CAA+C,CAAC,UAAU,EAAE,iBAAiB,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC;KACxG;IAED,4CAA4C,CAAC,UAAU,CAAC,CAAC;AAC3D,CAAC;AAEe,SAAA,iCAAiC,CAAC,UAAwC,EAAE,CAAM,EAAA;AAChG,IAAA,IAAM,MAAM,GAAG,UAAU,CAAC,6BAA6B,CAAC;AAExD,IAAA,IAAI,MAAM,CAAC,MAAM,KAAK,UAAU,EAAE;QAChC,OAAO;KACR;IAED,iDAAiD,CAAC,UAAU,CAAC,CAAC;IAE9D,UAAU,CAAC,UAAU,CAAC,CAAC;IACvB,2CAA2C,CAAC,UAAU,CAAC,CAAC;AACxD,IAAA,mBAAmB,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;AACjC,CAAC;AAEe,SAAA,oDAAoD,CAClE,UAAwC,EACxC,WAA+C,EAAA;IAI/C,IAAM,KAAK,GAAG,UAAU,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;AACxC,IAAA,UAAU,CAAC,eAAe,IAAI,KAAK,CAAC,UAAU,CAAC;IAE/C,4CAA4C,CAAC,UAAU,CAAC,CAAC;AAEzD,IAAA,IAAM,IAAI,GAAG,IAAI,UAAU,CAAC,KAAK,CAAC,MAAM,EAAE,KAAK,CAAC,UAAU,EAAE,KAAK,CAAC,UAAU,CAAC,CAAC;AAC9E,IAAA,WAAW,CAAC,WAAW,CAAC,IAA6B,CAAC,CAAC;AACzD,CAAC;AAEK,SAAU,0CAA0C,CACxD,UAAwC,EAAA;AAExC,IAAA,IAAI,UAAU,CAAC,YAAY,KAAK,IAAI,IAAI,UAAU,CAAC,iBAAiB,CAAC,MAAM,GAAG,CAAC,EAAE;QAC/E,IAAM,eAAe,GAAG,UAAU,CAAC,iBAAiB,CAAC,IAAI,EAAE,CAAC;QAC5D,IAAM,IAAI,GAAG,IAAI,UAAU,CAAC,eAAe,CAAC,MAAM,EACtB,eAAe,CAAC,UAAU,GAAG,eAAe,CAAC,WAAW,EACxD,eAAe,CAAC,UAAU,GAAG,eAAe,CAAC,WAAW,CAAC,CAAC;QAEtF,IAAM,WAAW,GAA8B,MAAM,CAAC,MAAM,CAAC,yBAAyB,CAAC,SAAS,CAAC,CAAC;AAClG,QAAA,8BAA8B,CAAC,WAAW,EAAE,UAAU,EAAE,IAA6B,CAAC,CAAC;AACvF,QAAA,UAAU,CAAC,YAAY,GAAG,WAAW,CAAC;KACvC;IACD,OAAO,UAAU,CAAC,YAAY,CAAC;AACjC,CAAC;AAED,SAAS,0CAA0C,CAAC,UAAwC,EAAA;AAC1F,IAAA,IAAM,KAAK,GAAG,UAAU,CAAC,6BAA6B,CAAC,MAAM,CAAC;AAE9D,IAAA,IAAI,KAAK,KAAK,SAAS,EAAE;AACvB,QAAA,OAAO,IAAI,CAAC;KACb;AACD,IAAA,IAAI,KAAK,KAAK,QAAQ,EAAE;AACtB,QAAA,OAAO,CAAC,CAAC;KACV;AAED,IAAA,OAAO,UAAU,CAAC,YAAY,GAAG,UAAU,CAAC,eAAe,CAAC;AAC9D,CAAC;AAEe,SAAA,mCAAmC,CAAC,UAAwC,EAAE,YAAoB,EAAA;IAGhH,IAAM,eAAe,GAAG,UAAU,CAAC,iBAAiB,CAAC,IAAI,EAAE,CAAC;AAC5D,IAAA,IAAM,KAAK,GAAG,UAAU,CAAC,6BAA6B,CAAC,MAAM,CAAC;AAE9D,IAAA,IAAI,KAAK,KAAK,QAAQ,EAAE;AACtB,QAAA,IAAI,YAAY,KAAK,CAAC,EAAE;AACtB,YAAA,MAAM,IAAI,SAAS,CAAC,kEAAkE,CAAC,CAAC;SACzF;KACF;SAAM;AAEL,QAAA,IAAI,YAAY,KAAK,CAAC,EAAE;AACtB,YAAA,MAAM,IAAI,SAAS,CAAC,iFAAiF,CAAC,CAAC;SACxG;QACD,IAAI,eAAe,CAAC,WAAW,GAAG,YAAY,GAAG,eAAe,CAAC,UAAU,EAAE;AAC3E,YAAA,MAAM,IAAI,UAAU,CAAC,2BAA2B,CAAC,CAAC;SACnD;KACF;IAED,eAAe,CAAC,MAAM,GAAG,mBAAmB,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;AAErE,IAAA,2CAA2C,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC;AACxE,CAAC;AAEe,SAAA,8CAA8C,CAAC,UAAwC,EACxC,IAAgC,EAAA;IAI7F,IAAM,eAAe,GAAG,UAAU,CAAC,iBAAiB,CAAC,IAAI,EAAE,CAAC;AAC5D,IAAA,IAAM,KAAK,GAAG,UAAU,CAAC,6BAA6B,CAAC,MAAM,CAAC;AAE9D,IAAA,IAAI,KAAK,KAAK,QAAQ,EAAE;AACtB,QAAA,IAAI,IAAI,CAAC,UAAU,KAAK,CAAC,EAAE;AACzB,YAAA,MAAM,IAAI,SAAS,CAAC,mFAAmF,CAAC,CAAC;SAC1G;KACF;SAAM;AAEL,QAAA,IAAI,IAAI,CAAC,UAAU,KAAK,CAAC,EAAE;AACzB,YAAA,MAAM,IAAI,SAAS,CACjB,kGAAkG,CACnG,CAAC;SACH;KACF;AAED,IAAA,IAAI,eAAe,CAAC,UAAU,GAAG,eAAe,CAAC,WAAW,KAAK,IAAI,CAAC,UAAU,EAAE;AAChF,QAAA,MAAM,IAAI,UAAU,CAAC,yDAAyD,CAAC,CAAC;KACjF;IACD,IAAI,eAAe,CAAC,gBAAgB,KAAK,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE;AAC/D,QAAA,MAAM,IAAI,UAAU,CAAC,4DAA4D,CAAC,CAAC;KACpF;AACD,IAAA,IAAI,eAAe,CAAC,WAAW,GAAG,IAAI,CAAC,UAAU,GAAG,eAAe,CAAC,UAAU,EAAE;AAC9E,QAAA,MAAM,IAAI,UAAU,CAAC,yDAAyD,CAAC,CAAC;KACjF;AAED,IAAA,IAAM,cAAc,GAAG,IAAI,CAAC,UAAU,CAAC;IACvC,eAAe,CAAC,MAAM,GAAG,mBAAmB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AAC1D,IAAA,2CAA2C,CAAC,UAAU,EAAE,cAAc,CAAC,CAAC;AAC1E,CAAC;AAEe,SAAA,iCAAiC,CAAC,MAA0B,EAC1B,UAAwC,EACxC,cAA8C,EAC9C,aAAkC,EAClC,eAA+C,EAC/C,aAAqB,EACrB,qBAAyC,EAAA;AAOzF,IAAA,UAAU,CAAC,6BAA6B,GAAG,MAAM,CAAC;AAElD,IAAA,UAAU,CAAC,UAAU,GAAG,KAAK,CAAC;AAC9B,IAAA,UAAU,CAAC,QAAQ,GAAG,KAAK,CAAC;AAE5B,IAAA,UAAU,CAAC,YAAY,GAAG,IAAI,CAAC;;IAG/B,UAAU,CAAC,MAAM,GAAG,UAAU,CAAC,eAAe,GAAG,SAAU,CAAC;IAC5D,UAAU,CAAC,UAAU,CAAC,CAAC;AAEvB,IAAA,UAAU,CAAC,eAAe,GAAG,KAAK,CAAC;AACnC,IAAA,UAAU,CAAC,QAAQ,GAAG,KAAK,CAAC;AAE5B,IAAA,UAAU,CAAC,YAAY,GAAG,aAAa,CAAC;AAExC,IAAA,UAAU,CAAC,cAAc,GAAG,aAAa,CAAC;AAC1C,IAAA,UAAU,CAAC,gBAAgB,GAAG,eAAe,CAAC;AAE9C,IAAA,UAAU,CAAC,sBAAsB,GAAG,qBAAqB,CAAC;AAE1D,IAAA,UAAU,CAAC,iBAAiB,GAAG,IAAI,WAAW,EAAE,CAAC;AAEjD,IAAA,MAAM,CAAC,yBAAyB,GAAG,UAAU,CAAC;AAE9C,IAAA,IAAM,WAAW,GAAG,cAAc,EAAE,CAAC;AACrC,IAAA,WAAW,CACT,mBAAmB,CAAC,WAAW,CAAC,EAChC,YAAA;AACE,QAAA,UAAU,CAAC,QAAQ,GAAG,IAAI,CAGK;QAE/B,4CAA4C,CAAC,UAAU,CAAC,CAAC;AACzD,QAAA,OAAO,IAAI,CAAC;KACb,EACD,UAAA,CAAC,EAAA;AACC,QAAA,iCAAiC,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC;AACjD,QAAA,OAAO,IAAI,CAAC;AACd,KAAC,CACF,CAAC;AACJ,CAAC;SAEe,qDAAqD,CACnE,MAA0B,EAC1B,oBAAmD,EACnD,aAAqB,EAAA;IAErB,IAAM,UAAU,GAAiC,MAAM,CAAC,MAAM,CAAC,4BAA4B,CAAC,SAAS,CAAC,CAAC;AAEvG,IAAA,IAAI,cAA8C,CAAC;AACnD,IAAA,IAAI,aAAkC,CAAC;AACvC,IAAA,IAAI,eAA+C,CAAC;AAEpD,IAAA,IAAI,oBAAoB,CAAC,KAAK,KAAK,SAAS,EAAE;QAC5C,cAAc,GAAG,YAAM,EAAA,OAAA,oBAAoB,CAAC,KAAM,CAAC,UAAU,CAAC,CAAvC,EAAuC,CAAC;KAChE;SAAM;AACL,QAAA,cAAc,GAAG,YAAM,EAAA,OAAA,SAAS,CAAA,EAAA,CAAC;KAClC;AACD,IAAA,IAAI,oBAAoB,CAAC,IAAI,KAAK,SAAS,EAAE;QAC3C,aAAa,GAAG,YAAM,EAAA,OAAA,oBAAoB,CAAC,IAAK,CAAC,UAAU,CAAC,CAAtC,EAAsC,CAAC;KAC9D;SAAM;QACL,aAAa,GAAG,cAAM,OAAA,mBAAmB,CAAC,SAAS,CAAC,CAA9B,EAA8B,CAAC;KACtD;AACD,IAAA,IAAI,oBAAoB,CAAC,MAAM,KAAK,SAAS,EAAE;AAC7C,QAAA,eAAe,GAAG,UAAA,MAAM,EAAA,EAAI,OAAA,oBAAoB,CAAC,MAAO,CAAC,MAAM,CAAC,CAAA,EAAA,CAAC;KAClE;SAAM;QACL,eAAe,GAAG,cAAM,OAAA,mBAAmB,CAAC,SAAS,CAAC,CAA9B,EAA8B,CAAC;KACxD;AAED,IAAA,IAAM,qBAAqB,GAAG,oBAAoB,CAAC,qBAAqB,CAAC;AACzE,IAAA,IAAI,qBAAqB,KAAK,CAAC,EAAE;AAC/B,QAAA,MAAM,IAAI,SAAS,CAAC,8CAA8C,CAAC,CAAC;KACrE;AAED,IAAA,iCAAiC,CAC/B,MAAM,EAAE,UAAU,EAAE,cAAc,EAAE,aAAa,EAAE,eAAe,EAAE,aAAa,EAAE,qBAAqB,CACzG,CAAC;AACJ,CAAC;AAED,SAAS,8BAA8B,CAAC,OAAkC,EAClC,UAAwC,EACxC,IAAgC,EAAA;AAKtE,IAAA,OAAO,CAAC,uCAAuC,GAAG,UAAU,CAAC;AAC7D,IAAA,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC;AACvB,CAAC;AAED;AAEA,SAAS,8BAA8B,CAAC,IAAY,EAAA;AAClD,IAAA,OAAO,IAAI,SAAS,CAClB,8CAAuC,IAAI,EAAA,kDAAA,CAAkD,CAAC,CAAC;AACnG,CAAC;AAED;AAEA,SAAS,uCAAuC,CAAC,IAAY,EAAA;AAC3D,IAAA,OAAO,IAAI,SAAS,CAClB,iDAA0C,IAAI,EAAA,qDAAA,CAAqD,CAAC,CAAC;AACzG;;AC1nCgB,SAAA,oBAAoB,CAAC,OAA0D,EAC1D,OAAe,EAAA;AAClD,IAAA,gBAAgB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;IACnC,IAAM,IAAI,GAAG,OAAO,KAAA,IAAA,IAAP,OAAO,KAAP,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,OAAO,CAAE,IAAI,CAAC;IAC3B,OAAO;AACL,QAAA,IAAI,EAAE,IAAI,KAAK,SAAS,GAAG,SAAS,GAAG,+BAA+B,CAAC,IAAI,EAAE,EAAG,CAAA,MAAA,CAAA,OAAO,4BAAyB,CAAC;KAClH,CAAC;AACJ,CAAC;AAED,SAAS,+BAA+B,CAAC,IAAY,EAAE,OAAe,EAAA;AACpE,IAAA,IAAI,GAAG,EAAA,CAAA,MAAA,CAAG,IAAI,CAAE,CAAC;AACjB,IAAA,IAAI,IAAI,KAAK,MAAM,EAAE;QACnB,MAAM,IAAI,SAAS,CAAC,EAAA,CAAA,MAAA,CAAG,OAAO,EAAK,IAAA,CAAA,CAAA,MAAA,CAAA,IAAI,EAAiE,iEAAA,CAAA,CAAC,CAAC;KAC3G;AACD,IAAA,OAAO,IAAI,CAAC;AACd,CAAC;AAEe,SAAA,sBAAsB,CACpC,OAA+D,EAC/D,OAAe,EAAA;;AAEf,IAAA,gBAAgB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;AACnC,IAAA,IAAM,GAAG,GAAG,CAAA,EAAA,GAAA,OAAO,KAAP,IAAA,IAAA,OAAO,KAAP,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,OAAO,CAAE,GAAG,MAAI,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAA,CAAC,CAAC;IAC9B,OAAO;QACL,GAAG,EAAE,uCAAuC,CAC1C,GAAG,EACH,EAAG,CAAA,MAAA,CAAA,OAAO,2BAAwB,CACnC;KACF,CAAC;AACJ;;ACGA;AAEM,SAAU,+BAA+B,CAAC,MAA0B,EAAA;AACxE,IAAA,OAAO,IAAI,wBAAwB,CAAC,MAAoC,CAAC,CAAC;AAC5E,CAAC;AAED;AAEgB,SAAA,gCAAgC,CAC9C,MAA0B,EAC1B,eAAmC,EAAA;IAKlC,MAAM,CAAC,OAAqC,CAAC,iBAAiB,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;AACxF,CAAC;SAEe,oCAAoC,CAAC,MAA0B,EAC1B,KAAsB,EACtB,IAAa,EAAA;AAChE,IAAA,IAAM,MAAM,GAAG,MAAM,CAAC,OAAmC,CAEb;IAE5C,IAAM,eAAe,GAAG,MAAM,CAAC,iBAAiB,CAAC,KAAK,EAAG,CAAC;IAC1D,IAAI,IAAI,EAAE;AACR,QAAA,eAAe,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;KACpC;SAAM;AACL,QAAA,eAAe,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;KACpC;AACH,CAAC;AAEK,SAAU,oCAAoC,CAAC,MAA0B,EAAA;AAC7E,IAAA,OAAQ,MAAM,CAAC,OAAoC,CAAC,iBAAiB,CAAC,MAAM,CAAC;AAC/E,CAAC;AAEK,SAAU,2BAA2B,CAAC,MAA0B,EAAA;AACpE,IAAA,IAAM,MAAM,GAAG,MAAM,CAAC,OAAO,CAAC;AAE9B,IAAA,IAAI,MAAM,KAAK,SAAS,EAAE;AACxB,QAAA,OAAO,KAAK,CAAC;KACd;AAED,IAAA,IAAI,CAAC,0BAA0B,CAAC,MAAM,CAAC,EAAE;AACvC,QAAA,OAAO,KAAK,CAAC;KACd;AAED,IAAA,OAAO,IAAI,CAAC;AACd,CAAC;AAYD;;;;AAIG;AACH,IAAA,wBAAA,kBAAA,YAAA;AAYE,IAAA,SAAA,wBAAA,CAAY,MAAkC,EAAA;AAC5C,QAAA,sBAAsB,CAAC,MAAM,EAAE,CAAC,EAAE,0BAA0B,CAAC,CAAC;AAC9D,QAAA,oBAAoB,CAAC,MAAM,EAAE,iBAAiB,CAAC,CAAC;AAEhD,QAAA,IAAI,sBAAsB,CAAC,MAAM,CAAC,EAAE;AAClC,YAAA,MAAM,IAAI,SAAS,CAAC,6EAA6E,CAAC,CAAC;SACpG;QAED,IAAI,CAAC,8BAA8B,CAAC,MAAM,CAAC,yBAAyB,CAAC,EAAE;YACrE,MAAM,IAAI,SAAS,CAAC,uFAAuF;AACzG,gBAAA,QAAQ,CAAC,CAAC;SACb;AAED,QAAA,qCAAqC,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;AAEpD,QAAA,IAAI,CAAC,iBAAiB,GAAG,IAAI,WAAW,EAAE,CAAC;KAC5C;AAMD,IAAA,MAAA,CAAA,cAAA,CAAI,wBAAM,CAAA,SAAA,EAAA,QAAA,EAAA;AAJV;;;AAGG;AACH,QAAA,GAAA,EAAA,YAAA;AACE,YAAA,IAAI,CAAC,0BAA0B,CAAC,IAAI,CAAC,EAAE;AACrC,gBAAA,OAAO,mBAAmB,CAAC,6BAA6B,CAAC,QAAQ,CAAC,CAAC,CAAC;aACrE;YAED,OAAO,IAAI,CAAC,cAAc,CAAC;SAC5B;;;AAAA,KAAA,CAAA,CAAA;AAED;;AAEG;IACH,wBAAM,CAAA,SAAA,CAAA,MAAA,GAAN,UAAO,MAAuB,EAAA;AAAvB,QAAA,IAAA,MAAA,KAAA,KAAA,CAAA,EAAA,EAAA,MAAuB,GAAA,SAAA,CAAA,EAAA;AAC5B,QAAA,IAAI,CAAC,0BAA0B,CAAC,IAAI,CAAC,EAAE;AACrC,YAAA,OAAO,mBAAmB,CAAC,6BAA6B,CAAC,QAAQ,CAAC,CAAC,CAAC;SACrE;AAED,QAAA,IAAI,IAAI,CAAC,oBAAoB,KAAK,SAAS,EAAE;AAC3C,YAAA,OAAO,mBAAmB,CAAC,mBAAmB,CAAC,QAAQ,CAAC,CAAC,CAAC;SAC3D;AAED,QAAA,OAAO,iCAAiC,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;KACxD,CAAA;AAWD,IAAA,wBAAA,CAAA,SAAA,CAAA,IAAI,GAAJ,UACE,IAAO,EACP,UAAuE,EAAA;AAAvE,QAAA,IAAA,UAAA,KAAA,KAAA,CAAA,EAAA,EAAA,UAAuE,GAAA,EAAA,CAAA,EAAA;AAEvE,QAAA,IAAI,CAAC,0BAA0B,CAAC,IAAI,CAAC,EAAE;AACrC,YAAA,OAAO,mBAAmB,CAAC,6BAA6B,CAAC,MAAM,CAAC,CAAC,CAAC;SACnE;QAED,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE;YAC7B,OAAO,mBAAmB,CAAC,IAAI,SAAS,CAAC,mCAAmC,CAAC,CAAC,CAAC;SAChF;AACD,QAAA,IAAI,IAAI,CAAC,UAAU,KAAK,CAAC,EAAE;YACzB,OAAO,mBAAmB,CAAC,IAAI,SAAS,CAAC,oCAAoC,CAAC,CAAC,CAAC;SACjF;QACD,IAAI,IAAI,CAAC,MAAM,CAAC,UAAU,KAAK,CAAC,EAAE;YAChC,OAAO,mBAAmB,CAAC,IAAI,SAAS,CAAC,6CAA6C,CAAC,CAAC,CAAC;SAC1F;AACD,QAAA,IAAI,gBAAgB,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;YACjC,OAAO,mBAAmB,CAAC,IAAI,SAAS,CAAC,kCAAkC,CAAC,CAAC,CAAC;SAC/E;AAED,QAAA,IAAI,OAAqD,CAAC;AAC1D,QAAA,IAAI;AACF,YAAA,OAAO,GAAG,sBAAsB,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC;SACzD;QAAC,OAAO,CAAC,EAAE;AACV,YAAA,OAAO,mBAAmB,CAAC,CAAC,CAAC,CAAC;SAC/B;AACD,QAAA,IAAM,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC;AACxB,QAAA,IAAI,GAAG,KAAK,CAAC,EAAE;YACb,OAAO,mBAAmB,CAAC,IAAI,SAAS,CAAC,oCAAoC,CAAC,CAAC,CAAC;SACjF;AACD,QAAA,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE;AACrB,YAAA,IAAI,GAAG,GAAI,IAA8B,CAAC,MAAM,EAAE;gBAChD,OAAO,mBAAmB,CAAC,IAAI,UAAU,CAAC,0DAA0D,CAAC,CAAC,CAAC;aACxG;SACF;AAAM,aAAA,IAAI,GAAG,GAAG,IAAI,CAAC,UAAU,EAAE;YAChC,OAAO,mBAAmB,CAAC,IAAI,UAAU,CAAC,8DAA8D,CAAC,CAAC,CAAC;SAC5G;AAED,QAAA,IAAI,IAAI,CAAC,oBAAoB,KAAK,SAAS,EAAE;AAC3C,YAAA,OAAO,mBAAmB,CAAC,mBAAmB,CAAC,WAAW,CAAC,CAAC,CAAC;SAC9D;AAED,QAAA,IAAI,cAAkE,CAAC;AACvE,QAAA,IAAI,aAAqC,CAAC;AAC1C,QAAA,IAAM,OAAO,GAAG,UAAU,CAAkC,UAAC,OAAO,EAAE,MAAM,EAAA;YAC1E,cAAc,GAAG,OAAO,CAAC;YACzB,aAAa,GAAG,MAAM,CAAC;AACzB,SAAC,CAAC,CAAC;AACH,QAAA,IAAM,eAAe,GAAuB;AAC1C,YAAA,WAAW,EAAE,UAAA,KAAK,IAAI,OAAA,cAAc,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,GAAA;AACnE,YAAA,WAAW,EAAE,UAAA,KAAK,IAAI,OAAA,cAAc,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,GAAA;YAClE,WAAW,EAAE,UAAA,CAAC,EAAI,EAAA,OAAA,aAAa,CAAC,CAAC,CAAC,CAAA,EAAA;SACnC,CAAC;QACF,4BAA4B,CAAC,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,eAAe,CAAC,CAAC;AAC/D,QAAA,OAAO,OAAO,CAAC;KAChB,CAAA;AAED;;;;;;;;AAQG;AACH,IAAA,wBAAA,CAAA,SAAA,CAAA,WAAW,GAAX,YAAA;AACE,QAAA,IAAI,CAAC,0BAA0B,CAAC,IAAI,CAAC,EAAE;AACrC,YAAA,MAAM,6BAA6B,CAAC,aAAa,CAAC,CAAC;SACpD;AAED,QAAA,IAAI,IAAI,CAAC,oBAAoB,KAAK,SAAS,EAAE;YAC3C,OAAO;SACR;QAED,+BAA+B,CAAC,IAAI,CAAC,CAAC;KACvC,CAAA;IACH,OAAC,wBAAA,CAAA;AAAD,CAAC,EAAA,EAAA;AAED,MAAM,CAAC,gBAAgB,CAAC,wBAAwB,CAAC,SAAS,EAAE;AAC1D,IAAA,MAAM,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE;AAC5B,IAAA,IAAI,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE;AAC1B,IAAA,WAAW,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE;AACjC,IAAA,MAAM,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE;AAC7B,CAAA,CAAC,CAAC;AACH,eAAe,CAAC,wBAAwB,CAAC,SAAS,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;AACrE,eAAe,CAAC,wBAAwB,CAAC,SAAS,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;AACjE,eAAe,CAAC,wBAAwB,CAAC,SAAS,CAAC,WAAW,EAAE,aAAa,CAAC,CAAC;AAC/E,IAAI,OAAOA,cAAM,CAAC,WAAW,KAAK,QAAQ,EAAE;IAC1C,MAAM,CAAC,cAAc,CAAC,wBAAwB,CAAC,SAAS,EAAEA,cAAM,CAAC,WAAW,EAAE;AAC5E,QAAA,KAAK,EAAE,0BAA0B;AACjC,QAAA,YAAY,EAAE,IAAI;AACnB,KAAA,CAAC,CAAC;AACL,CAAC;AAED;AAEM,SAAU,0BAA0B,CAAC,CAAM,EAAA;AAC/C,IAAA,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE;AACpB,QAAA,OAAO,KAAK,CAAC;KACd;AAED,IAAA,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,mBAAmB,CAAC,EAAE;AACjE,QAAA,OAAO,KAAK,CAAC;KACd;IAED,OAAO,CAAC,YAAY,wBAAwB,CAAC;AAC/C,CAAC;AAEK,SAAU,4BAA4B,CAC1C,MAAgC,EAChC,IAAO,EACP,GAAW,EACX,eAAmC,EAAA;AAEnC,IAAA,IAAM,MAAM,GAAG,MAAM,CAAC,oBAAoB,CAEb;AAE7B,IAAA,MAAM,CAAC,UAAU,GAAG,IAAI,CAAC;AAEzB,IAAA,IAAI,MAAM,CAAC,MAAM,KAAK,SAAS,EAAE;AAC/B,QAAA,eAAe,CAAC,WAAW,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;KAClD;SAAM;QACL,oCAAoC,CAClC,MAAM,CAAC,yBAAyD,EAChE,IAAI,EACJ,GAAG,EACH,eAAe,CAChB,CAAC;KACH;AACH,CAAC;AAEK,SAAU,+BAA+B,CAAC,MAAgC,EAAA;IAC9E,kCAAkC,CAAC,MAAM,CAAC,CAAC;AAC3C,IAAA,IAAM,CAAC,GAAG,IAAI,SAAS,CAAC,qBAAqB,CAAC,CAAC;AAC/C,IAAA,6CAA6C,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;AAC3D,CAAC;AAEe,SAAA,6CAA6C,CAAC,MAAgC,EAAE,CAAM,EAAA;AACpG,IAAA,IAAM,gBAAgB,GAAG,MAAM,CAAC,iBAAiB,CAAC;AAClD,IAAA,MAAM,CAAC,iBAAiB,GAAG,IAAI,WAAW,EAAE,CAAC;AAC7C,IAAA,gBAAgB,CAAC,OAAO,CAAC,UAAA,eAAe,EAAA;AACtC,QAAA,eAAe,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;AACjC,KAAC,CAAC,CAAC;AACL,CAAC;AAED;AAEA,SAAS,6BAA6B,CAAC,IAAY,EAAA;AACjD,IAAA,OAAO,IAAI,SAAS,CAClB,6CAAsC,IAAI,EAAA,iDAAA,CAAiD,CAAC,CAAC;AACjG;;ACjUgB,SAAA,oBAAoB,CAAC,QAAyB,EAAE,UAAkB,EAAA;AACxE,IAAA,IAAA,aAAa,GAAK,QAAQ,CAAA,aAAb,CAAc;AAEnC,IAAA,IAAI,aAAa,KAAK,SAAS,EAAE;AAC/B,QAAA,OAAO,UAAU,CAAC;KACnB;IAED,IAAI,WAAW,CAAC,aAAa,CAAC,IAAI,aAAa,GAAG,CAAC,EAAE;AACnD,QAAA,MAAM,IAAI,UAAU,CAAC,uBAAuB,CAAC,CAAC;KAC/C;AAED,IAAA,OAAO,aAAa,CAAC;AACvB,CAAC;AAEK,SAAU,oBAAoB,CAAI,QAA4B,EAAA;AAC1D,IAAA,IAAA,IAAI,GAAK,QAAQ,CAAA,IAAb,CAAc;IAE1B,IAAI,CAAC,IAAI,EAAE;AACT,QAAA,OAAO,YAAM,EAAA,OAAA,CAAC,CAAA,EAAA,CAAC;KAChB;AAED,IAAA,OAAO,IAAI,CAAC;AACd;;ACtBgB,SAAA,sBAAsB,CAAI,IAA2C,EAC3C,OAAe,EAAA;AACvD,IAAA,gBAAgB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;IAChC,IAAM,aAAa,GAAG,IAAI,KAAA,IAAA,IAAJ,IAAI,KAAJ,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,IAAI,CAAE,aAAa,CAAC;IAC1C,IAAM,IAAI,GAAG,IAAI,KAAA,IAAA,IAAJ,IAAI,KAAJ,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,IAAI,CAAE,IAAI,CAAC;IACxB,OAAO;AACL,QAAA,aAAa,EAAE,aAAa,KAAK,SAAS,GAAG,SAAS,GAAG,yBAAyB,CAAC,aAAa,CAAC;AACjG,QAAA,IAAI,EAAE,IAAI,KAAK,SAAS,GAAG,SAAS,GAAG,0BAA0B,CAAC,IAAI,EAAE,EAAG,CAAA,MAAA,CAAA,OAAO,4BAAyB,CAAC;KAC7G,CAAC;AACJ,CAAC;AAED,SAAS,0BAA0B,CAAI,EAAkC,EAClC,OAAe,EAAA;AACpD,IAAA,cAAc,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;AAC5B,IAAA,OAAO,UAAA,KAAK,EAAI,EAAA,OAAA,yBAAyB,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,CAAA,EAAA,CAAC;AACvD;;ACNgB,SAAA,qBAAqB,CAAI,QAAkC,EAClC,OAAe,EAAA;AACtD,IAAA,gBAAgB,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;IACpC,IAAM,KAAK,GAAG,QAAQ,KAAA,IAAA,IAAR,QAAQ,KAAR,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,QAAQ,CAAE,KAAK,CAAC;IAC9B,IAAM,KAAK,GAAG,QAAQ,KAAA,IAAA,IAAR,QAAQ,KAAR,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,QAAQ,CAAE,KAAK,CAAC;IAC9B,IAAM,KAAK,GAAG,QAAQ,KAAA,IAAA,IAAR,QAAQ,KAAR,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,QAAQ,CAAE,KAAK,CAAC;IAC9B,IAAM,IAAI,GAAG,QAAQ,KAAA,IAAA,IAAR,QAAQ,KAAR,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,QAAQ,CAAE,IAAI,CAAC;IAC5B,IAAM,KAAK,GAAG,QAAQ,KAAA,IAAA,IAAR,QAAQ,KAAR,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,QAAQ,CAAE,KAAK,CAAC;IAC9B,OAAO;AACL,QAAA,KAAK,EAAE,KAAK,KAAK,SAAS;AACxB,YAAA,SAAS;YACT,kCAAkC,CAAC,KAAK,EAAE,QAAS,EAAE,EAAG,CAAA,MAAA,CAAA,OAAO,6BAA0B,CAAC;AAC5F,QAAA,KAAK,EAAE,KAAK,KAAK,SAAS;AACxB,YAAA,SAAS;YACT,kCAAkC,CAAC,KAAK,EAAE,QAAS,EAAE,EAAG,CAAA,MAAA,CAAA,OAAO,6BAA0B,CAAC;AAC5F,QAAA,KAAK,EAAE,KAAK,KAAK,SAAS;AACxB,YAAA,SAAS;YACT,kCAAkC,CAAC,KAAK,EAAE,QAAS,EAAE,EAAG,CAAA,MAAA,CAAA,OAAO,6BAA0B,CAAC;AAC5F,QAAA,KAAK,EAAE,KAAK,KAAK,SAAS;AACxB,YAAA,SAAS;YACT,kCAAkC,CAAC,KAAK,EAAE,QAAS,EAAE,EAAG,CAAA,MAAA,CAAA,OAAO,6BAA0B,CAAC;AAC5F,QAAA,IAAI,EAAA,IAAA;KACL,CAAC;AACJ,CAAC;AAED,SAAS,kCAAkC,CACzC,EAA+B,EAC/B,QAAwB,EACxB,OAAe,EAAA;AAEf,IAAA,cAAc,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;AAC5B,IAAA,OAAO,UAAC,MAAW,EAAA,EAAK,OAAA,WAAW,CAAC,EAAE,EAAE,QAAQ,EAAE,CAAC,MAAM,CAAC,CAAC,CAAA,EAAA,CAAC;AAC9D,CAAC;AAED,SAAS,kCAAkC,CACzC,EAA+B,EAC/B,QAAwB,EACxB,OAAe,EAAA;AAEf,IAAA,cAAc,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;AAC5B,IAAA,OAAO,YAAM,EAAA,OAAA,WAAW,CAAC,EAAE,EAAE,QAAQ,EAAE,EAAE,CAAC,CAA7B,EAA6B,CAAC;AAC7C,CAAC;AAED,SAAS,kCAAkC,CACzC,EAA+B,EAC/B,QAAwB,EACxB,OAAe,EAAA;AAEf,IAAA,cAAc,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;AAC5B,IAAA,OAAO,UAAC,UAA2C,EAAA,EAAK,OAAA,WAAW,CAAC,EAAE,EAAE,QAAQ,EAAE,CAAC,UAAU,CAAC,CAAC,CAAA,EAAA,CAAC;AAClG,CAAC;AAED,SAAS,kCAAkC,CACzC,EAAkC,EAClC,QAA2B,EAC3B,OAAe,EAAA;AAEf,IAAA,cAAc,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;IAC5B,OAAO,UAAC,KAAQ,EAAE,UAA2C,IAAK,OAAA,WAAW,CAAC,EAAE,EAAE,QAAQ,EAAE,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC,CAAA,EAAA,CAAC;AACnH;;ACrEgB,SAAA,oBAAoB,CAAC,CAAU,EAAE,OAAe,EAAA;AAC9D,IAAA,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,EAAE;AACxB,QAAA,MAAM,IAAI,SAAS,CAAC,UAAG,OAAO,EAAA,2BAAA,CAA2B,CAAC,CAAC;KAC5D;AACH;;AC2BM,SAAU,aAAa,CAAC,KAAc,EAAA;IAC1C,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,KAAK,IAAI,EAAE;AAC/C,QAAA,OAAO,KAAK,CAAC;KACd;AACD,IAAA,IAAI;AACF,QAAA,OAAO,OAAQ,KAAqB,CAAC,OAAO,KAAK,SAAS,CAAC;KAC5D;AAAC,IAAA,OAAA,EAAA,EAAM;;AAEN,QAAA,OAAO,KAAK,CAAC;KACd;AACH,CAAC;AAsBD,IAAM,uBAAuB,GAAG,OAAQ,eAAuB,KAAK,UAAU,CAAC;AAE/E;;;;AAIG;SACa,qBAAqB,GAAA;IACnC,IAAI,uBAAuB,EAAE;QAC3B,OAAO,IAAK,eAA8C,EAAE,CAAC;KAC9D;AACD,IAAA,OAAO,SAAS,CAAC;AACnB;;ACxBA;;;;AAIG;AACH,IAAA,cAAA,kBAAA,YAAA;IAuBE,SAAY,cAAA,CAAA,iBAA4D,EAC5D,WAAuD,EAAA;AADvD,QAAA,IAAA,iBAAA,KAAA,KAAA,CAAA,EAAA,EAAA,iBAA4D,GAAA,EAAA,CAAA,EAAA;AAC5D,QAAA,IAAA,WAAA,KAAA,KAAA,CAAA,EAAA,EAAA,WAAuD,GAAA,EAAA,CAAA,EAAA;AACjE,QAAA,IAAI,iBAAiB,KAAK,SAAS,EAAE;YACnC,iBAAiB,GAAG,IAAI,CAAC;SAC1B;aAAM;AACL,YAAA,YAAY,CAAC,iBAAiB,EAAE,iBAAiB,CAAC,CAAC;SACpD;QAED,IAAM,QAAQ,GAAG,sBAAsB,CAAC,WAAW,EAAE,kBAAkB,CAAC,CAAC;QACzE,IAAM,cAAc,GAAG,qBAAqB,CAAC,iBAAiB,EAAE,iBAAiB,CAAC,CAAC;QAEnF,wBAAwB,CAAC,IAAI,CAAC,CAAC;AAE/B,QAAA,IAAM,IAAI,GAAG,cAAc,CAAC,IAAI,CAAC;AACjC,QAAA,IAAI,IAAI,KAAK,SAAS,EAAE;AACtB,YAAA,MAAM,IAAI,UAAU,CAAC,2BAA2B,CAAC,CAAC;SACnD;AAED,QAAA,IAAM,aAAa,GAAG,oBAAoB,CAAC,QAAQ,CAAC,CAAC;QACrD,IAAM,aAAa,GAAG,oBAAoB,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;QAExD,sDAAsD,CAAC,IAAI,EAAE,cAAc,EAAE,aAAa,EAAE,aAAa,CAAC,CAAC;KAC5G;AAKD,IAAA,MAAA,CAAA,cAAA,CAAI,cAAM,CAAA,SAAA,EAAA,QAAA,EAAA;AAHV;;AAEG;AACH,QAAA,GAAA,EAAA,YAAA;AACE,YAAA,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,EAAE;AAC3B,gBAAA,MAAMG,2BAAyB,CAAC,QAAQ,CAAC,CAAC;aAC3C;AAED,YAAA,OAAO,sBAAsB,CAAC,IAAI,CAAC,CAAC;SACrC;;;AAAA,KAAA,CAAA,CAAA;AAED;;;;;;;;AAQG;IACH,cAAK,CAAA,SAAA,CAAA,KAAA,GAAL,UAAM,MAAuB,EAAA;AAAvB,QAAA,IAAA,MAAA,KAAA,KAAA,CAAA,EAAA,EAAA,MAAuB,GAAA,SAAA,CAAA,EAAA;AAC3B,QAAA,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,EAAE;AAC3B,YAAA,OAAO,mBAAmB,CAACA,2BAAyB,CAAC,OAAO,CAAC,CAAC,CAAC;SAChE;AAED,QAAA,IAAI,sBAAsB,CAAC,IAAI,CAAC,EAAE;YAChC,OAAO,mBAAmB,CAAC,IAAI,SAAS,CAAC,iDAAiD,CAAC,CAAC,CAAC;SAC9F;AAED,QAAA,OAAO,mBAAmB,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;KAC1C,CAAA;AAED;;;;;;;AAOG;AACH,IAAA,cAAA,CAAA,SAAA,CAAA,KAAK,GAAL,YAAA;AACE,QAAA,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,EAAE;AAC3B,YAAA,OAAO,mBAAmB,CAACA,2BAAyB,CAAC,OAAO,CAAC,CAAC,CAAC;SAChE;AAED,QAAA,IAAI,sBAAsB,CAAC,IAAI,CAAC,EAAE;YAChC,OAAO,mBAAmB,CAAC,IAAI,SAAS,CAAC,iDAAiD,CAAC,CAAC,CAAC;SAC9F;AAED,QAAA,IAAI,mCAAmC,CAAC,IAAI,CAAC,EAAE;YAC7C,OAAO,mBAAmB,CAAC,IAAI,SAAS,CAAC,wCAAwC,CAAC,CAAC,CAAC;SACrF;AAED,QAAA,OAAO,mBAAmB,CAAC,IAAI,CAAC,CAAC;KAClC,CAAA;AAED;;;;;;;AAOG;AACH,IAAA,cAAA,CAAA,SAAA,CAAA,SAAS,GAAT,YAAA;AACE,QAAA,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,EAAE;AAC3B,YAAA,MAAMA,2BAAyB,CAAC,WAAW,CAAC,CAAC;SAC9C;AAED,QAAA,OAAO,kCAAkC,CAAC,IAAI,CAAC,CAAC;KACjD,CAAA;IACH,OAAC,cAAA,CAAA;AAAD,CAAC,EAAA,EAAA;AAED,MAAM,CAAC,gBAAgB,CAAC,cAAc,CAAC,SAAS,EAAE;AAChD,IAAA,KAAK,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE;AAC3B,IAAA,KAAK,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE;AAC3B,IAAA,SAAS,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE;AAC/B,IAAA,MAAM,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE;AAC7B,CAAA,CAAC,CAAC;AACH,eAAe,CAAC,cAAc,CAAC,SAAS,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;AACzD,eAAe,CAAC,cAAc,CAAC,SAAS,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;AACzD,eAAe,CAAC,cAAc,CAAC,SAAS,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC;AACjE,IAAI,OAAOH,cAAM,CAAC,WAAW,KAAK,QAAQ,EAAE;IAC1C,MAAM,CAAC,cAAc,CAAC,cAAc,CAAC,SAAS,EAAEA,cAAM,CAAC,WAAW,EAAE;AAClE,QAAA,KAAK,EAAE,gBAAgB;AACvB,QAAA,YAAY,EAAE,IAAI;AACnB,KAAA,CAAC,CAAC;AACL,CAAC;AAwBD;AAEA,SAAS,kCAAkC,CAAI,MAAyB,EAAA;AACtE,IAAA,OAAO,IAAI,2BAA2B,CAAC,MAAM,CAAC,CAAC;AACjD,CAAC;AAED;AACA,SAAS,oBAAoB,CAAI,cAA8C,EAC9C,cAA2C,EAC3C,cAAmC,EACnC,cAA8C,EAC9C,aAAiB,EACjB,aAAuD,EAAA;AADvD,IAAA,IAAA,aAAA,KAAA,KAAA,CAAA,EAAA,EAAA,aAAiB,GAAA,CAAA,CAAA,EAAA;AACjB,IAAA,IAAA,aAAA,KAAA,KAAA,CAAA,EAAA,EAAA,aAAA,GAAA,YAAA,EAAsD,OAAA,CAAC,GAAA,CAAA,EAC3C;IAE3C,IAAM,MAAM,GAAsB,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;IAC1E,wBAAwB,CAAC,MAAM,CAAC,CAAC;IAEjC,IAAM,UAAU,GAAuC,MAAM,CAAC,MAAM,CAAC,+BAA+B,CAAC,SAAS,CAAC,CAAC;AAEhH,IAAA,oCAAoC,CAAC,MAAM,EAAE,UAAU,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc,EAClE,cAAc,EAAE,aAAa,EAAE,aAAa,CAAC,CAAC;AACnF,IAAA,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,SAAS,wBAAwB,CAAI,MAAyB,EAAA;AAC5D,IAAA,MAAM,CAAC,MAAM,GAAG,UAAU,CAAC;;;AAI3B,IAAA,MAAM,CAAC,YAAY,GAAG,SAAS,CAAC;AAEhC,IAAA,MAAM,CAAC,OAAO,GAAG,SAAS,CAAC;;;AAI3B,IAAA,MAAM,CAAC,yBAAyB,GAAG,SAAU,CAAC;;;AAI9C,IAAA,MAAM,CAAC,cAAc,GAAG,IAAI,WAAW,EAAE,CAAC;;;AAI1C,IAAA,MAAM,CAAC,qBAAqB,GAAG,SAAS,CAAC;;;AAIzC,IAAA,MAAM,CAAC,aAAa,GAAG,SAAS,CAAC;;;AAIjC,IAAA,MAAM,CAAC,qBAAqB,GAAG,SAAS,CAAC;;AAGzC,IAAA,MAAM,CAAC,oBAAoB,GAAG,SAAS,CAAC;;AAGxC,IAAA,MAAM,CAAC,aAAa,GAAG,KAAK,CAAC;AAC/B,CAAC;AAED,SAAS,gBAAgB,CAAC,CAAU,EAAA;AAClC,IAAA,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE;AACpB,QAAA,OAAO,KAAK,CAAC;KACd;AAED,IAAA,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,2BAA2B,CAAC,EAAE;AACzE,QAAA,OAAO,KAAK,CAAC;KACd;IAED,OAAO,CAAC,YAAY,cAAc,CAAC;AACrC,CAAC;AAED,SAAS,sBAAsB,CAAC,MAAsB,EAAA;AAGpD,IAAA,IAAI,MAAM,CAAC,OAAO,KAAK,SAAS,EAAE;AAChC,QAAA,OAAO,KAAK,CAAC;KACd;AAED,IAAA,OAAO,IAAI,CAAC;AACd,CAAC;AAED,SAAS,mBAAmB,CAAC,MAAsB,EAAE,MAAW,EAAA;;AAC9D,IAAA,IAAI,MAAM,CAAC,MAAM,KAAK,QAAQ,IAAI,MAAM,CAAC,MAAM,KAAK,SAAS,EAAE;AAC7D,QAAA,OAAO,mBAAmB,CAAC,SAAS,CAAC,CAAC;KACvC;AACD,IAAA,MAAM,CAAC,yBAAyB,CAAC,YAAY,GAAG,MAAM,CAAC;IACvD,CAAA,EAAA,GAAA,MAAM,CAAC,yBAAyB,CAAC,gBAAgB,0CAAE,KAAK,CAAC,MAAM,CAAC,CAAC;;;;AAKjE,IAAA,IAAM,KAAK,GAAG,MAAM,CAAC,MAA6B,CAAC;IAEnD,IAAI,KAAK,KAAK,QAAQ,IAAI,KAAK,KAAK,SAAS,EAAE;AAC7C,QAAA,OAAO,mBAAmB,CAAC,SAAS,CAAC,CAAC;KACvC;AACD,IAAA,IAAI,MAAM,CAAC,oBAAoB,KAAK,SAAS,EAAE;AAC7C,QAAA,OAAO,MAAM,CAAC,oBAAoB,CAAC,QAAQ,CAAC;KAGO;IAErD,IAAI,kBAAkB,GAAG,KAAK,CAAC;AAC/B,IAAA,IAAI,KAAK,KAAK,UAAU,EAAE;QACxB,kBAAkB,GAAG,IAAI,CAAC;;QAE1B,MAAM,GAAG,SAAS,CAAC;KACpB;AAED,IAAA,IAAM,OAAO,GAAG,UAAU,CAAY,UAAC,OAAO,EAAE,MAAM,EAAA;QACpD,MAAM,CAAC,oBAAoB,GAAG;AAC5B,YAAA,QAAQ,EAAE,SAAU;AACpB,YAAA,QAAQ,EAAE,OAAO;AACjB,YAAA,OAAO,EAAE,MAAM;AACf,YAAA,OAAO,EAAE,MAAM;AACf,YAAA,mBAAmB,EAAE,kBAAkB;SACxC,CAAC;AACJ,KAAC,CAAC,CAAC;AACH,IAAA,MAAM,CAAC,oBAAqB,CAAC,QAAQ,GAAG,OAAO,CAAC;IAEhD,IAAI,CAAC,kBAAkB,EAAE;AACvB,QAAA,2BAA2B,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;KAC7C;AAED,IAAA,OAAO,OAAO,CAAC;AACjB,CAAC;AAED,SAAS,mBAAmB,CAAC,MAA2B,EAAA;AACtD,IAAA,IAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC;IAC5B,IAAI,KAAK,KAAK,QAAQ,IAAI,KAAK,KAAK,SAAS,EAAE;QAC7C,OAAO,mBAAmB,CAAC,IAAI,SAAS,CACtC,yBAAkB,KAAK,EAAA,2DAAA,CAA2D,CAAC,CAAC,CAAC;KAIpC;AAErD,IAAA,IAAM,OAAO,GAAG,UAAU,CAAY,UAAC,OAAO,EAAE,MAAM,EAAA;AACpD,QAAA,IAAM,YAAY,GAAiB;AACjC,YAAA,QAAQ,EAAE,OAAO;AACjB,YAAA,OAAO,EAAE,MAAM;SAChB,CAAC;AAEF,QAAA,MAAM,CAAC,aAAa,GAAG,YAAY,CAAC;AACtC,KAAC,CAAC,CAAC;AAEH,IAAA,IAAM,MAAM,GAAG,MAAM,CAAC,OAAO,CAAC;AAC9B,IAAA,IAAI,MAAM,KAAK,SAAS,IAAI,MAAM,CAAC,aAAa,IAAI,KAAK,KAAK,UAAU,EAAE;QACxE,gCAAgC,CAAC,MAAM,CAAC,CAAC;KAC1C;AAED,IAAA,oCAAoC,CAAC,MAAM,CAAC,yBAAyB,CAAC,CAAC;AAEvE,IAAA,OAAO,OAAO,CAAC;AACjB,CAAC;AAED;AAEA,SAAS,6BAA6B,CAAC,MAAsB,EAAA;AAI3D,IAAA,IAAM,OAAO,GAAG,UAAU,CAAY,UAAC,OAAO,EAAE,MAAM,EAAA;AACpD,QAAA,IAAM,YAAY,GAAiB;AACjC,YAAA,QAAQ,EAAE,OAAO;AACjB,YAAA,OAAO,EAAE,MAAM;SAChB,CAAC;AAEF,QAAA,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;AAC3C,KAAC,CAAC,CAAC;AAEH,IAAA,OAAO,OAAO,CAAC;AACjB,CAAC;AAED,SAAS,+BAA+B,CAAC,MAAsB,EAAE,KAAU,EAAA;AACzE,IAAA,IAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC;AAE5B,IAAA,IAAI,KAAK,KAAK,UAAU,EAAE;AACxB,QAAA,2BAA2B,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;QAC3C,OAAO;KAGoB;IAC7B,4BAA4B,CAAC,MAAM,CAAC,CAAC;AACvC,CAAC;AAED,SAAS,2BAA2B,CAAC,MAAsB,EAAE,MAAW,EAAA;AAItE,IAAA,IAAM,UAAU,GAAG,MAAM,CAAC,yBAAyB,CAClB;AAEjC,IAAA,MAAM,CAAC,MAAM,GAAG,UAAU,CAAC;AAC3B,IAAA,MAAM,CAAC,YAAY,GAAG,MAAM,CAAC;AAC7B,IAAA,IAAM,MAAM,GAAG,MAAM,CAAC,OAAO,CAAC;AAC9B,IAAA,IAAI,MAAM,KAAK,SAAS,EAAE;AACxB,QAAA,qDAAqD,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;KACvE;IAED,IAAI,CAAC,wCAAwC,CAAC,MAAM,CAAC,IAAI,UAAU,CAAC,QAAQ,EAAE;QAC5E,4BAA4B,CAAC,MAAM,CAAC,CAAC;KACtC;AACH,CAAC;AAED,SAAS,4BAA4B,CAAC,MAAsB,EAAA;AAG1D,IAAA,MAAM,CAAC,MAAM,GAAG,SAAS,CAAC;AAC1B,IAAA,MAAM,CAAC,yBAAyB,CAAC,UAAU,CAAC,EAAE,CAAC;AAE/C,IAAA,IAAM,WAAW,GAAG,MAAM,CAAC,YAAY,CAAC;AACxC,IAAA,MAAM,CAAC,cAAc,CAAC,OAAO,CAAC,UAAA,YAAY,EAAA;AACxC,QAAA,YAAY,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;AACpC,KAAC,CAAC,CAAC;AACH,IAAA,MAAM,CAAC,cAAc,GAAG,IAAI,WAAW,EAAE,CAAC;AAE1C,IAAA,IAAI,MAAM,CAAC,oBAAoB,KAAK,SAAS,EAAE;QAC7C,iDAAiD,CAAC,MAAM,CAAC,CAAC;QAC1D,OAAO;KACR;AAED,IAAA,IAAM,YAAY,GAAG,MAAM,CAAC,oBAAoB,CAAC;AACjD,IAAA,MAAM,CAAC,oBAAoB,GAAG,SAAS,CAAC;AAExC,IAAA,IAAI,YAAY,CAAC,mBAAmB,EAAE;AACpC,QAAA,YAAY,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;QAClC,iDAAiD,CAAC,MAAM,CAAC,CAAC;QAC1D,OAAO;KACR;AAED,IAAA,IAAM,OAAO,GAAG,MAAM,CAAC,yBAAyB,CAAC,UAAU,CAAC,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;IACnF,WAAW,CACT,OAAO,EACP,YAAA;QACE,YAAY,CAAC,QAAQ,EAAE,CAAC;QACxB,iDAAiD,CAAC,MAAM,CAAC,CAAC;AAC1D,QAAA,OAAO,IAAI,CAAC;KACb,EACD,UAAC,MAAW,EAAA;AACV,QAAA,YAAY,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QAC7B,iDAAiD,CAAC,MAAM,CAAC,CAAC;AAC1D,QAAA,OAAO,IAAI,CAAC;AACd,KAAC,CAAC,CAAC;AACP,CAAC;AAED,SAAS,iCAAiC,CAAC,MAAsB,EAAA;AAE/D,IAAA,MAAM,CAAC,qBAAsB,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;AAClD,IAAA,MAAM,CAAC,qBAAqB,GAAG,SAAS,CAAC;AAC3C,CAAC;AAED,SAAS,0CAA0C,CAAC,MAAsB,EAAE,KAAU,EAAA;AAEpF,IAAA,MAAM,CAAC,qBAAsB,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AAC7C,IAAA,MAAM,CAAC,qBAAqB,GAAG,SAAS,CAE6B;AAErE,IAAA,+BAA+B,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;AACjD,CAAC;AAED,SAAS,iCAAiC,CAAC,MAAsB,EAAA;AAE/D,IAAA,MAAM,CAAC,qBAAsB,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;AAClD,IAAA,MAAM,CAAC,qBAAqB,GAAG,SAAS,CAAC;AAEzC,IAAA,IAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CAE0B;AAErD,IAAA,IAAI,KAAK,KAAK,UAAU,EAAE;;AAExB,QAAA,MAAM,CAAC,YAAY,GAAG,SAAS,CAAC;AAChC,QAAA,IAAI,MAAM,CAAC,oBAAoB,KAAK,SAAS,EAAE;AAC7C,YAAA,MAAM,CAAC,oBAAoB,CAAC,QAAQ,EAAE,CAAC;AACvC,YAAA,MAAM,CAAC,oBAAoB,GAAG,SAAS,CAAC;SACzC;KACF;AAED,IAAA,MAAM,CAAC,MAAM,GAAG,QAAQ,CAAC;AAEzB,IAAA,IAAM,MAAM,GAAG,MAAM,CAAC,OAAO,CAAC;AAC9B,IAAA,IAAI,MAAM,KAAK,SAAS,EAAE;QACxB,iCAAiC,CAAC,MAAM,CAAC,CAAC;KAIF;AAC5C,CAAC;AAED,SAAS,0CAA0C,CAAC,MAAsB,EAAE,KAAU,EAAA;AAEpF,IAAA,MAAM,CAAC,qBAAsB,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AAC7C,IAAA,MAAM,CAAC,qBAAqB,GAAG,SAAS,CAE6B;;AAGrE,IAAA,IAAI,MAAM,CAAC,oBAAoB,KAAK,SAAS,EAAE;AAC7C,QAAA,MAAM,CAAC,oBAAoB,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AAC3C,QAAA,MAAM,CAAC,oBAAoB,GAAG,SAAS,CAAC;KACzC;AACD,IAAA,+BAA+B,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;AACjD,CAAC;AAED;AACA,SAAS,mCAAmC,CAAC,MAAsB,EAAA;AACjE,IAAA,IAAI,MAAM,CAAC,aAAa,KAAK,SAAS,IAAI,MAAM,CAAC,qBAAqB,KAAK,SAAS,EAAE;AACpF,QAAA,OAAO,KAAK,CAAC;KACd;AAED,IAAA,OAAO,IAAI,CAAC;AACd,CAAC;AAED,SAAS,wCAAwC,CAAC,MAAsB,EAAA;AACtE,IAAA,IAAI,MAAM,CAAC,qBAAqB,KAAK,SAAS,IAAI,MAAM,CAAC,qBAAqB,KAAK,SAAS,EAAE;AAC5F,QAAA,OAAO,KAAK,CAAC;KACd;AAED,IAAA,OAAO,IAAI,CAAC;AACd,CAAC;AAED,SAAS,sCAAsC,CAAC,MAAsB,EAAA;AAGpE,IAAA,MAAM,CAAC,qBAAqB,GAAG,MAAM,CAAC,aAAa,CAAC;AACpD,IAAA,MAAM,CAAC,aAAa,GAAG,SAAS,CAAC;AACnC,CAAC;AAED,SAAS,2CAA2C,CAAC,MAAsB,EAAA;IAGzE,MAAM,CAAC,qBAAqB,GAAG,MAAM,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC;AAC/D,CAAC;AAED,SAAS,iDAAiD,CAAC,MAAsB,EAAA;AAE/E,IAAA,IAAI,MAAM,CAAC,aAAa,KAAK,SAAS,EAAE;QAGtC,MAAM,CAAC,aAAa,CAAC,OAAO,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;AAClD,QAAA,MAAM,CAAC,aAAa,GAAG,SAAS,CAAC;KAClC;AACD,IAAA,IAAM,MAAM,GAAG,MAAM,CAAC,OAAO,CAAC;AAC9B,IAAA,IAAI,MAAM,KAAK,SAAS,EAAE;AACxB,QAAA,gCAAgC,CAAC,MAAM,EAAE,MAAM,CAAC,YAAY,CAAC,CAAC;KAC/D;AACH,CAAC;AAED,SAAS,gCAAgC,CAAC,MAAsB,EAAE,YAAqB,EAAA;AAIrF,IAAA,IAAM,MAAM,GAAG,MAAM,CAAC,OAAO,CAAC;IAC9B,IAAI,MAAM,KAAK,SAAS,IAAI,YAAY,KAAK,MAAM,CAAC,aAAa,EAAE;QACjE,IAAI,YAAY,EAAE;YAChB,8BAA8B,CAAC,MAAM,CAAC,CAAC;SACxC;aAAM;YAGL,gCAAgC,CAAC,MAAM,CAAC,CAAC;SAC1C;KACF;AAED,IAAA,MAAM,CAAC,aAAa,GAAG,YAAY,CAAC;AACtC,CAAC;AAED;;;;AAIG;AACH,IAAA,2BAAA,kBAAA,YAAA;AAoBE,IAAA,SAAA,2BAAA,CAAY,MAAyB,EAAA;AACnC,QAAA,sBAAsB,CAAC,MAAM,EAAE,CAAC,EAAE,6BAA6B,CAAC,CAAC;AACjE,QAAA,oBAAoB,CAAC,MAAM,EAAE,iBAAiB,CAAC,CAAC;AAEhD,QAAA,IAAI,sBAAsB,CAAC,MAAM,CAAC,EAAE;AAClC,YAAA,MAAM,IAAI,SAAS,CAAC,6EAA6E,CAAC,CAAC;SACpG;AAED,QAAA,IAAI,CAAC,oBAAoB,GAAG,MAAM,CAAC;AACnC,QAAA,MAAM,CAAC,OAAO,GAAG,IAAI,CAAC;AAEtB,QAAA,IAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC;AAE5B,QAAA,IAAI,KAAK,KAAK,UAAU,EAAE;YACxB,IAAI,CAAC,mCAAmC,CAAC,MAAM,CAAC,IAAI,MAAM,CAAC,aAAa,EAAE;gBACxE,mCAAmC,CAAC,IAAI,CAAC,CAAC;aAC3C;iBAAM;gBACL,6CAA6C,CAAC,IAAI,CAAC,CAAC;aACrD;YAED,oCAAoC,CAAC,IAAI,CAAC,CAAC;SAC5C;AAAM,aAAA,IAAI,KAAK,KAAK,UAAU,EAAE;AAC/B,YAAA,6CAA6C,CAAC,IAAI,EAAE,MAAM,CAAC,YAAY,CAAC,CAAC;YACzE,oCAAoC,CAAC,IAAI,CAAC,CAAC;SAC5C;AAAM,aAAA,IAAI,KAAK,KAAK,QAAQ,EAAE;YAC7B,6CAA6C,CAAC,IAAI,CAAC,CAAC;YACpD,8CAA8C,CAAC,IAAI,CAAC,CAAC;SACtD;aAAM;AAGL,YAAA,IAAM,WAAW,GAAG,MAAM,CAAC,YAAY,CAAC;AACxC,YAAA,6CAA6C,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;AACjE,YAAA,8CAA8C,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;SACnE;KACF;AAMD,IAAA,MAAA,CAAA,cAAA,CAAI,2BAAM,CAAA,SAAA,EAAA,QAAA,EAAA;AAJV;;;AAGG;AACH,QAAA,GAAA,EAAA,YAAA;AACE,YAAA,IAAI,CAAC,6BAA6B,CAAC,IAAI,CAAC,EAAE;AACxC,gBAAA,OAAO,mBAAmB,CAAC,gCAAgC,CAAC,QAAQ,CAAC,CAAC,CAAC;aACxE;YAED,OAAO,IAAI,CAAC,cAAc,CAAC;SAC5B;;;AAAA,KAAA,CAAA,CAAA;AAUD,IAAA,MAAA,CAAA,cAAA,CAAI,2BAAW,CAAA,SAAA,EAAA,aAAA,EAAA;AARf;;;;;;;AAOG;AACH,QAAA,GAAA,EAAA,YAAA;AACE,YAAA,IAAI,CAAC,6BAA6B,CAAC,IAAI,CAAC,EAAE;AACxC,gBAAA,MAAM,gCAAgC,CAAC,aAAa,CAAC,CAAC;aACvD;AAED,YAAA,IAAI,IAAI,CAAC,oBAAoB,KAAK,SAAS,EAAE;AAC3C,gBAAA,MAAM,0BAA0B,CAAC,aAAa,CAAC,CAAC;aACjD;AAED,YAAA,OAAO,yCAAyC,CAAC,IAAI,CAAC,CAAC;SACxD;;;AAAA,KAAA,CAAA,CAAA;AAUD,IAAA,MAAA,CAAA,cAAA,CAAI,2BAAK,CAAA,SAAA,EAAA,OAAA,EAAA;AART;;;;;;;AAOG;AACH,QAAA,GAAA,EAAA,YAAA;AACE,YAAA,IAAI,CAAC,6BAA6B,CAAC,IAAI,CAAC,EAAE;AACxC,gBAAA,OAAO,mBAAmB,CAAC,gCAAgC,CAAC,OAAO,CAAC,CAAC,CAAC;aACvE;YAED,OAAO,IAAI,CAAC,aAAa,CAAC;SAC3B;;;AAAA,KAAA,CAAA,CAAA;AAED;;AAEG;IACH,2BAAK,CAAA,SAAA,CAAA,KAAA,GAAL,UAAM,MAAuB,EAAA;AAAvB,QAAA,IAAA,MAAA,KAAA,KAAA,CAAA,EAAA,EAAA,MAAuB,GAAA,SAAA,CAAA,EAAA;AAC3B,QAAA,IAAI,CAAC,6BAA6B,CAAC,IAAI,CAAC,EAAE;AACxC,YAAA,OAAO,mBAAmB,CAAC,gCAAgC,CAAC,OAAO,CAAC,CAAC,CAAC;SACvE;AAED,QAAA,IAAI,IAAI,CAAC,oBAAoB,KAAK,SAAS,EAAE;AAC3C,YAAA,OAAO,mBAAmB,CAAC,0BAA0B,CAAC,OAAO,CAAC,CAAC,CAAC;SACjE;AAED,QAAA,OAAO,gCAAgC,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;KACvD,CAAA;AAED;;AAEG;AACH,IAAA,2BAAA,CAAA,SAAA,CAAA,KAAK,GAAL,YAAA;AACE,QAAA,IAAI,CAAC,6BAA6B,CAAC,IAAI,CAAC,EAAE;AACxC,YAAA,OAAO,mBAAmB,CAAC,gCAAgC,CAAC,OAAO,CAAC,CAAC,CAAC;SACvE;AAED,QAAA,IAAM,MAAM,GAAG,IAAI,CAAC,oBAAoB,CAAC;AAEzC,QAAA,IAAI,MAAM,KAAK,SAAS,EAAE;AACxB,YAAA,OAAO,mBAAmB,CAAC,0BAA0B,CAAC,OAAO,CAAC,CAAC,CAAC;SACjE;AAED,QAAA,IAAI,mCAAmC,CAAC,MAAM,CAAC,EAAE;YAC/C,OAAO,mBAAmB,CAAC,IAAI,SAAS,CAAC,wCAAwC,CAAC,CAAC,CAAC;SACrF;AAED,QAAA,OAAO,gCAAgC,CAAC,IAAI,CAAC,CAAC;KAC/C,CAAA;AAED;;;;;;;;;AASG;AACH,IAAA,2BAAA,CAAA,SAAA,CAAA,WAAW,GAAX,YAAA;AACE,QAAA,IAAI,CAAC,6BAA6B,CAAC,IAAI,CAAC,EAAE;AACxC,YAAA,MAAM,gCAAgC,CAAC,aAAa,CAAC,CAAC;SACvD;AAED,QAAA,IAAM,MAAM,GAAG,IAAI,CAAC,oBAAoB,CAAC;AAEzC,QAAA,IAAI,MAAM,KAAK,SAAS,EAAE;YACxB,OAAO;SAG4B;QAErC,kCAAkC,CAAC,IAAI,CAAC,CAAC;KAC1C,CAAA;IAYD,2BAAK,CAAA,SAAA,CAAA,KAAA,GAAL,UAAM,KAAqB,EAAA;QAArB,IAAA,KAAA,KAAA,KAAA,CAAA,EAAA,EAAA,QAAW,SAAU,CAAA,EAAA;AACzB,QAAA,IAAI,CAAC,6BAA6B,CAAC,IAAI,CAAC,EAAE;AACxC,YAAA,OAAO,mBAAmB,CAAC,gCAAgC,CAAC,OAAO,CAAC,CAAC,CAAC;SACvE;AAED,QAAA,IAAI,IAAI,CAAC,oBAAoB,KAAK,SAAS,EAAE;AAC3C,YAAA,OAAO,mBAAmB,CAAC,0BAA0B,CAAC,UAAU,CAAC,CAAC,CAAC;SACpE;AAED,QAAA,OAAO,gCAAgC,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;KACtD,CAAA;IACH,OAAC,2BAAA,CAAA;AAAD,CAAC,EAAA,EAAA;AAED,MAAM,CAAC,gBAAgB,CAAC,2BAA2B,CAAC,SAAS,EAAE;AAC7D,IAAA,KAAK,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE;AAC3B,IAAA,KAAK,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE;AAC3B,IAAA,WAAW,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE;AACjC,IAAA,KAAK,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE;AAC3B,IAAA,MAAM,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE;AAC5B,IAAA,WAAW,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE;AACjC,IAAA,KAAK,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE;AAC5B,CAAA,CAAC,CAAC;AACH,eAAe,CAAC,2BAA2B,CAAC,SAAS,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;AACtE,eAAe,CAAC,2BAA2B,CAAC,SAAS,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;AACtE,eAAe,CAAC,2BAA2B,CAAC,SAAS,CAAC,WAAW,EAAE,aAAa,CAAC,CAAC;AAClF,eAAe,CAAC,2BAA2B,CAAC,SAAS,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;AACtE,IAAI,OAAOA,cAAM,CAAC,WAAW,KAAK,QAAQ,EAAE;IAC1C,MAAM,CAAC,cAAc,CAAC,2BAA2B,CAAC,SAAS,EAAEA,cAAM,CAAC,WAAW,EAAE;AAC/E,QAAA,KAAK,EAAE,6BAA6B;AACpC,QAAA,YAAY,EAAE,IAAI;AACnB,KAAA,CAAC,CAAC;AACL,CAAC;AAED;AAEA,SAAS,6BAA6B,CAAU,CAAM,EAAA;AACpD,IAAA,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE;AACpB,QAAA,OAAO,KAAK,CAAC;KACd;AAED,IAAA,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,sBAAsB,CAAC,EAAE;AACpE,QAAA,OAAO,KAAK,CAAC;KACd;IAED,OAAO,CAAC,YAAY,2BAA2B,CAAC;AAClD,CAAC;AAED;AAEA,SAAS,gCAAgC,CAAC,MAAmC,EAAE,MAAW,EAAA;AACxF,IAAA,IAAM,MAAM,GAAG,MAAM,CAAC,oBAAoB,CAEb;AAE7B,IAAA,OAAO,mBAAmB,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;AAC7C,CAAC;AAED,SAAS,gCAAgC,CAAC,MAAmC,EAAA;AAC3E,IAAA,IAAM,MAAM,GAAG,MAAM,CAAC,oBAAoB,CAEb;AAE7B,IAAA,OAAO,mBAAmB,CAAC,MAAM,CAAC,CAAC;AACrC,CAAC;AAED,SAAS,oDAAoD,CAAC,MAAmC,EAAA;AAC/F,IAAA,IAAM,MAAM,GAAG,MAAM,CAAC,oBAAoB,CAEb;AAE7B,IAAA,IAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC;IAC5B,IAAI,mCAAmC,CAAC,MAAM,CAAC,IAAI,KAAK,KAAK,QAAQ,EAAE;AACrE,QAAA,OAAO,mBAAmB,CAAC,SAAS,CAAC,CAAC;KACvC;AAED,IAAA,IAAI,KAAK,KAAK,SAAS,EAAE;AACvB,QAAA,OAAO,mBAAmB,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;KAGG;AAErD,IAAA,OAAO,gCAAgC,CAAC,MAAM,CAAC,CAAC;AAClD,CAAC;AAED,SAAS,sDAAsD,CAAC,MAAmC,EAAE,KAAU,EAAA;AAC7G,IAAA,IAAI,MAAM,CAAC,mBAAmB,KAAK,SAAS,EAAE;AAC5C,QAAA,gCAAgC,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;KACjD;SAAM;AACL,QAAA,yCAAyC,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;KAC1D;AACH,CAAC;AAED,SAAS,qDAAqD,CAAC,MAAmC,EAAE,KAAU,EAAA;AAC5G,IAAA,IAAI,MAAM,CAAC,kBAAkB,KAAK,SAAS,EAAE;AAC3C,QAAA,+BAA+B,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;KAChD;SAAM;AACL,QAAA,wCAAwC,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;KACzD;AACH,CAAC;AAED,SAAS,yCAAyC,CAAC,MAAmC,EAAA;AACpF,IAAA,IAAM,MAAM,GAAG,MAAM,CAAC,oBAAoB,CAAC;AAC3C,IAAA,IAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC;IAE5B,IAAI,KAAK,KAAK,SAAS,IAAI,KAAK,KAAK,UAAU,EAAE;AAC/C,QAAA,OAAO,IAAI,CAAC;KACb;AAED,IAAA,IAAI,KAAK,KAAK,QAAQ,EAAE;AACtB,QAAA,OAAO,CAAC,CAAC;KACV;AAED,IAAA,OAAO,6CAA6C,CAAC,MAAM,CAAC,yBAAyB,CAAC,CAAC;AACzF,CAAC;AAED,SAAS,kCAAkC,CAAC,MAAmC,EAAA;AAC7E,IAAA,IAAM,MAAM,GAAG,MAAM,CAAC,oBAAoB,CAER;AAElC,IAAA,IAAM,aAAa,GAAG,IAAI,SAAS,CACjC,kFAAkF,CAAC,CAAC;AAEtF,IAAA,qDAAqD,CAAC,MAAM,EAAE,aAAa,CAAC,CAAC;;;AAI7E,IAAA,sDAAsD,CAAC,MAAM,EAAE,aAAa,CAAC,CAAC;AAE9E,IAAA,MAAM,CAAC,OAAO,GAAG,SAAS,CAAC;AAC3B,IAAA,MAAM,CAAC,oBAAoB,GAAG,SAAU,CAAC;AAC3C,CAAC;AAED,SAAS,gCAAgC,CAAI,MAAsC,EAAE,KAAQ,EAAA;AAC3F,IAAA,IAAM,MAAM,GAAG,MAAM,CAAC,oBAAoB,CAEb;AAE7B,IAAA,IAAM,UAAU,GAAG,MAAM,CAAC,yBAAyB,CAAC;IAEpD,IAAM,SAAS,GAAG,2CAA2C,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;AAEjF,IAAA,IAAI,MAAM,KAAK,MAAM,CAAC,oBAAoB,EAAE;AAC1C,QAAA,OAAO,mBAAmB,CAAC,0BAA0B,CAAC,UAAU,CAAC,CAAC,CAAC;KACpE;AAED,IAAA,IAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC;AAC5B,IAAA,IAAI,KAAK,KAAK,SAAS,EAAE;AACvB,QAAA,OAAO,mBAAmB,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;KACjD;IACD,IAAI,mCAAmC,CAAC,MAAM,CAAC,IAAI,KAAK,KAAK,QAAQ,EAAE;QACrE,OAAO,mBAAmB,CAAC,IAAI,SAAS,CAAC,0DAA0D,CAAC,CAAC,CAAC;KACvG;AACD,IAAA,IAAI,KAAK,KAAK,UAAU,EAAE;AACxB,QAAA,OAAO,mBAAmB,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;KAGrB;AAE7B,IAAA,IAAM,OAAO,GAAG,6BAA6B,CAAC,MAAM,CAAC,CAAC;AAEtD,IAAA,oCAAoC,CAAC,UAAU,EAAE,KAAK,EAAE,SAAS,CAAC,CAAC;AAEnE,IAAA,OAAO,OAAO,CAAC;AACjB,CAAC;AAED,IAAM,aAAa,GAAkB,EAAS,CAAC;AAI/C;;;;AAIG;AACH,IAAA,+BAAA,kBAAA,YAAA;AAwBE,IAAA,SAAA,+BAAA,GAAA;AACE,QAAA,MAAM,IAAI,SAAS,CAAC,qBAAqB,CAAC,CAAC;KAC5C;AASD,IAAA,MAAA,CAAA,cAAA,CAAI,+BAAW,CAAA,SAAA,EAAA,aAAA,EAAA;AAPf;;;;;;AAMG;AACH,QAAA,GAAA,EAAA,YAAA;AACE,YAAA,IAAI,CAAC,iCAAiC,CAAC,IAAI,CAAC,EAAE;AAC5C,gBAAA,MAAMI,sCAAoC,CAAC,aAAa,CAAC,CAAC;aAC3D;YACD,OAAO,IAAI,CAAC,YAAY,CAAC;SAC1B;;;AAAA,KAAA,CAAA,CAAA;AAKD,IAAA,MAAA,CAAA,cAAA,CAAI,+BAAM,CAAA,SAAA,EAAA,QAAA,EAAA;AAHV;;AAEG;AACH,QAAA,GAAA,EAAA,YAAA;AACE,YAAA,IAAI,CAAC,iCAAiC,CAAC,IAAI,CAAC,EAAE;AAC5C,gBAAA,MAAMA,sCAAoC,CAAC,QAAQ,CAAC,CAAC;aACtD;AACD,YAAA,IAAI,IAAI,CAAC,gBAAgB,KAAK,SAAS,EAAE;;;;AAIvC,gBAAA,MAAM,IAAI,SAAS,CAAC,mEAAmE,CAAC,CAAC;aAC1F;AACD,YAAA,OAAO,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC;SACrC;;;AAAA,KAAA,CAAA,CAAA;AAED;;;;;;AAMG;IACH,+BAAK,CAAA,SAAA,CAAA,KAAA,GAAL,UAAM,CAAkB,EAAA;AAAlB,QAAA,IAAA,CAAA,KAAA,KAAA,CAAA,EAAA,EAAA,CAAkB,GAAA,SAAA,CAAA,EAAA;AACtB,QAAA,IAAI,CAAC,iCAAiC,CAAC,IAAI,CAAC,EAAE;AAC5C,YAAA,MAAMA,sCAAoC,CAAC,OAAO,CAAC,CAAC;SACrD;AACD,QAAA,IAAM,KAAK,GAAG,IAAI,CAAC,yBAAyB,CAAC,MAAM,CAAC;AACpD,QAAA,IAAI,KAAK,KAAK,UAAU,EAAE;;;YAGxB,OAAO;SACR;AAED,QAAA,oCAAoC,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;KAC/C,CAAA;;AAGD,IAAA,+BAAA,CAAA,SAAA,CAAC,UAAU,CAAC,GAAZ,UAAa,MAAW,EAAA;QACtB,IAAM,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;QAC5C,8CAA8C,CAAC,IAAI,CAAC,CAAC;AACrD,QAAA,OAAO,MAAM,CAAC;KACf,CAAA;;IAGD,+BAAC,CAAA,SAAA,CAAA,UAAU,CAAC,GAAZ,YAAA;QACE,UAAU,CAAC,IAAI,CAAC,CAAC;KAClB,CAAA;IACH,OAAC,+BAAA,CAAA;AAAD,CAAC,EAAA,EAAA;AAED,MAAM,CAAC,gBAAgB,CAAC,+BAA+B,CAAC,SAAS,EAAE;AACjE,IAAA,WAAW,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE;AACjC,IAAA,MAAM,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE;AAC5B,IAAA,KAAK,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE;AAC5B,CAAA,CAAC,CAAC;AACH,IAAI,OAAOJ,cAAM,CAAC,WAAW,KAAK,QAAQ,EAAE;IAC1C,MAAM,CAAC,cAAc,CAAC,+BAA+B,CAAC,SAAS,EAAEA,cAAM,CAAC,WAAW,EAAE;AACnF,QAAA,KAAK,EAAE,iCAAiC;AACxC,QAAA,YAAY,EAAE,IAAI;AACnB,KAAA,CAAC,CAAC;AACL,CAAC;AAED;AAEA,SAAS,iCAAiC,CAAC,CAAM,EAAA;AAC/C,IAAA,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE;AACpB,QAAA,OAAO,KAAK,CAAC;KACd;AAED,IAAA,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,2BAA2B,CAAC,EAAE;AACzE,QAAA,OAAO,KAAK,CAAC;KACd;IAED,OAAO,CAAC,YAAY,+BAA+B,CAAC;AACtD,CAAC;AAED,SAAS,oCAAoC,CAAI,MAAyB,EACzB,UAA8C,EAC9C,cAA8C,EAC9C,cAA2C,EAC3C,cAAmC,EACnC,cAA8C,EAC9C,aAAqB,EACrB,aAA6C,EAAA;AAI5F,IAAA,UAAU,CAAC,yBAAyB,GAAG,MAAM,CAAC;AAC9C,IAAA,MAAM,CAAC,yBAAyB,GAAG,UAAU,CAAC;;AAG9C,IAAA,UAAU,CAAC,MAAM,GAAG,SAAU,CAAC;AAC/B,IAAA,UAAU,CAAC,eAAe,GAAG,SAAU,CAAC;IACxC,UAAU,CAAC,UAAU,CAAC,CAAC;AAEvB,IAAA,UAAU,CAAC,YAAY,GAAG,SAAS,CAAC;AACpC,IAAA,UAAU,CAAC,gBAAgB,GAAG,qBAAqB,EAAE,CAAC;AACtD,IAAA,UAAU,CAAC,QAAQ,GAAG,KAAK,CAAC;AAE5B,IAAA,UAAU,CAAC,sBAAsB,GAAG,aAAa,CAAC;AAClD,IAAA,UAAU,CAAC,YAAY,GAAG,aAAa,CAAC;AAExC,IAAA,UAAU,CAAC,eAAe,GAAG,cAAc,CAAC;AAC5C,IAAA,UAAU,CAAC,eAAe,GAAG,cAAc,CAAC;AAC5C,IAAA,UAAU,CAAC,eAAe,GAAG,cAAc,CAAC;AAE5C,IAAA,IAAM,YAAY,GAAG,8CAA8C,CAAC,UAAU,CAAC,CAAC;AAChF,IAAA,gCAAgC,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC;AAEvD,IAAA,IAAM,WAAW,GAAG,cAAc,EAAE,CAAC;AACrC,IAAA,IAAM,YAAY,GAAG,mBAAmB,CAAC,WAAW,CAAC,CAAC;IACtD,WAAW,CACT,YAAY,EACZ,YAAA;AAEE,QAAA,UAAU,CAAC,QAAQ,GAAG,IAAI,CAAC;QAC3B,mDAAmD,CAAC,UAAU,CAAC,CAAC;AAChE,QAAA,OAAO,IAAI,CAAC;KACb,EACD,UAAA,CAAC,EAAA;AAEC,QAAA,UAAU,CAAC,QAAQ,GAAG,IAAI,CAAC;AAC3B,QAAA,+BAA+B,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;AAC3C,QAAA,OAAO,IAAI,CAAC;AACd,KAAC,CACF,CAAC;AACJ,CAAC;AAED,SAAS,sDAAsD,CAAI,MAAyB,EACzB,cAA0C,EAC1C,aAAqB,EACrB,aAA6C,EAAA;IAC9G,IAAM,UAAU,GAAG,MAAM,CAAC,MAAM,CAAC,+BAA+B,CAAC,SAAS,CAAC,CAAC;AAE5E,IAAA,IAAI,cAA8C,CAAC;AACnD,IAAA,IAAI,cAA2C,CAAC;AAChD,IAAA,IAAI,cAAmC,CAAC;AACxC,IAAA,IAAI,cAA8C,CAAC;AAEnD,IAAA,IAAI,cAAc,CAAC,KAAK,KAAK,SAAS,EAAE;QACtC,cAAc,GAAG,YAAM,EAAA,OAAA,cAAc,CAAC,KAAM,CAAC,UAAU,CAAC,CAAjC,EAAiC,CAAC;KAC1D;SAAM;AACL,QAAA,cAAc,GAAG,YAAM,EAAA,OAAA,SAAS,CAAA,EAAA,CAAC;KAClC;AACD,IAAA,IAAI,cAAc,CAAC,KAAK,KAAK,SAAS,EAAE;AACtC,QAAA,cAAc,GAAG,UAAA,KAAK,EAAA,EAAI,OAAA,cAAc,CAAC,KAAM,CAAC,KAAK,EAAE,UAAU,CAAC,CAAA,EAAA,CAAC;KACpE;SAAM;QACL,cAAc,GAAG,cAAM,OAAA,mBAAmB,CAAC,SAAS,CAAC,CAA9B,EAA8B,CAAC;KACvD;AACD,IAAA,IAAI,cAAc,CAAC,KAAK,KAAK,SAAS,EAAE;QACtC,cAAc,GAAG,cAAM,OAAA,cAAc,CAAC,KAAM,EAAE,CAAvB,EAAuB,CAAC;KAChD;SAAM;QACL,cAAc,GAAG,cAAM,OAAA,mBAAmB,CAAC,SAAS,CAAC,CAA9B,EAA8B,CAAC;KACvD;AACD,IAAA,IAAI,cAAc,CAAC,KAAK,KAAK,SAAS,EAAE;AACtC,QAAA,cAAc,GAAG,UAAA,MAAM,EAAA,EAAI,OAAA,cAAc,CAAC,KAAM,CAAC,MAAM,CAAC,CAAA,EAAA,CAAC;KAC1D;SAAM;QACL,cAAc,GAAG,cAAM,OAAA,mBAAmB,CAAC,SAAS,CAAC,CAA9B,EAA8B,CAAC;KACvD;AAED,IAAA,oCAAoC,CAClC,MAAM,EAAE,UAAU,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc,EAAE,aAAa,EAAE,aAAa,CACjH,CAAC;AACJ,CAAC;AAED;AACA,SAAS,8CAA8C,CAAC,UAAgD,EAAA;AACtG,IAAA,UAAU,CAAC,eAAe,GAAG,SAAU,CAAC;AACxC,IAAA,UAAU,CAAC,eAAe,GAAG,SAAU,CAAC;AACxC,IAAA,UAAU,CAAC,eAAe,GAAG,SAAU,CAAC;AACxC,IAAA,UAAU,CAAC,sBAAsB,GAAG,SAAU,CAAC;AACjD,CAAC;AAED,SAAS,oCAAoC,CAAI,UAA8C,EAAA;AAC7F,IAAA,oBAAoB,CAAC,UAAU,EAAE,aAAa,EAAE,CAAC,CAAC,CAAC;IACnD,mDAAmD,CAAC,UAAU,CAAC,CAAC;AAClE,CAAC;AAED,SAAS,2CAA2C,CAAI,UAA8C,EAC9C,KAAQ,EAAA;AAC9D,IAAA,IAAI;AACF,QAAA,OAAO,UAAU,CAAC,sBAAsB,CAAC,KAAK,CAAC,CAAC;KACjD;IAAC,OAAO,UAAU,EAAE;AACnB,QAAA,4CAA4C,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;AACrE,QAAA,OAAO,CAAC,CAAC;KACV;AACH,CAAC;AAED,SAAS,6CAA6C,CAAC,UAAgD,EAAA;AACrG,IAAA,OAAO,UAAU,CAAC,YAAY,GAAG,UAAU,CAAC,eAAe,CAAC;AAC9D,CAAC;AAED,SAAS,oCAAoC,CAAI,UAA8C,EAC9C,KAAQ,EACR,SAAiB,EAAA;AAChE,IAAA,IAAI;AACF,QAAA,oBAAoB,CAAC,UAAU,EAAE,KAAK,EAAE,SAAS,CAAC,CAAC;KACpD;IAAC,OAAO,QAAQ,EAAE;AACjB,QAAA,4CAA4C,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;QACnE,OAAO;KACR;AAED,IAAA,IAAM,MAAM,GAAG,UAAU,CAAC,yBAAyB,CAAC;AACpD,IAAA,IAAI,CAAC,mCAAmC,CAAC,MAAM,CAAC,IAAI,MAAM,CAAC,MAAM,KAAK,UAAU,EAAE;AAChF,QAAA,IAAM,YAAY,GAAG,8CAA8C,CAAC,UAAU,CAAC,CAAC;AAChF,QAAA,gCAAgC,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC;KACxD;IAED,mDAAmD,CAAC,UAAU,CAAC,CAAC;AAClE,CAAC;AAED;AAEA,SAAS,mDAAmD,CAAI,UAA8C,EAAA;AAC5G,IAAA,IAAM,MAAM,GAAG,UAAU,CAAC,yBAAyB,CAAC;AAEpD,IAAA,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE;QACxB,OAAO;KACR;AAED,IAAA,IAAI,MAAM,CAAC,qBAAqB,KAAK,SAAS,EAAE;QAC9C,OAAO;KACR;AAED,IAAA,IAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CACuB;AAClD,IAAA,IAAI,KAAK,KAAK,UAAU,EAAE;QACxB,4BAA4B,CAAC,MAAM,CAAC,CAAC;QACrC,OAAO;KACR;IAED,IAAI,UAAU,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;QAClC,OAAO;KACR;AAED,IAAA,IAAM,KAAK,GAAG,cAAc,CAAC,UAAU,CAAC,CAAC;AACzC,IAAA,IAAI,KAAK,KAAK,aAAa,EAAE;QAC3B,2CAA2C,CAAC,UAAU,CAAC,CAAC;KACzD;SAAM;AACL,QAAA,2CAA2C,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;KAChE;AACH,CAAC;AAED,SAAS,4CAA4C,CAAC,UAAgD,EAAE,KAAU,EAAA;IAChH,IAAI,UAAU,CAAC,yBAAyB,CAAC,MAAM,KAAK,UAAU,EAAE;AAC9D,QAAA,oCAAoC,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;KACzD;AACH,CAAC;AAED,SAAS,2CAA2C,CAAC,UAAgD,EAAA;AACnG,IAAA,IAAM,MAAM,GAAG,UAAU,CAAC,yBAAyB,CAAC;IAEpD,sCAAsC,CAAC,MAAM,CAAC,CAAC;IAE/C,YAAY,CAAC,UAAU,CAAC,CACe;AAEvC,IAAA,IAAM,gBAAgB,GAAG,UAAU,CAAC,eAAe,EAAE,CAAC;IACtD,8CAA8C,CAAC,UAAU,CAAC,CAAC;IAC3D,WAAW,CACT,gBAAgB,EAChB,YAAA;QACE,iCAAiC,CAAC,MAAM,CAAC,CAAC;AAC1C,QAAA,OAAO,IAAI,CAAC;KACb,EACD,UAAA,MAAM,EAAA;AACJ,QAAA,0CAA0C,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;AAC3D,QAAA,OAAO,IAAI,CAAC;AACd,KAAC,CACF,CAAC;AACJ,CAAC;AAED,SAAS,2CAA2C,CAAI,UAA8C,EAAE,KAAQ,EAAA;AAC9G,IAAA,IAAM,MAAM,GAAG,UAAU,CAAC,yBAAyB,CAAC;IAEpD,2CAA2C,CAAC,MAAM,CAAC,CAAC;IAEpD,IAAM,gBAAgB,GAAG,UAAU,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;IAC3D,WAAW,CACT,gBAAgB,EAChB,YAAA;QACE,iCAAiC,CAAC,MAAM,CAAC,CAAC;AAE1C,QAAA,IAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CAC0B;QAErD,YAAY,CAAC,UAAU,CAAC,CAAC;QAEzB,IAAI,CAAC,mCAAmC,CAAC,MAAM,CAAC,IAAI,KAAK,KAAK,UAAU,EAAE;AACxE,YAAA,IAAM,YAAY,GAAG,8CAA8C,CAAC,UAAU,CAAC,CAAC;AAChF,YAAA,gCAAgC,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC;SACxD;QAED,mDAAmD,CAAC,UAAU,CAAC,CAAC;AAChE,QAAA,OAAO,IAAI,CAAC;KACb,EACD,UAAA,MAAM,EAAA;AACJ,QAAA,IAAI,MAAM,CAAC,MAAM,KAAK,UAAU,EAAE;YAChC,8CAA8C,CAAC,UAAU,CAAC,CAAC;SAC5D;AACD,QAAA,0CAA0C,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;AAC3D,QAAA,OAAO,IAAI,CAAC;AACd,KAAC,CACF,CAAC;AACJ,CAAC;AAED,SAAS,8CAA8C,CAAC,UAAgD,EAAA;AACtG,IAAA,IAAM,WAAW,GAAG,6CAA6C,CAAC,UAAU,CAAC,CAAC;IAC9E,OAAO,WAAW,IAAI,CAAC,CAAC;AAC1B,CAAC;AAED;AAEA,SAAS,oCAAoC,CAAC,UAAgD,EAAE,KAAU,EAAA;AACxG,IAAA,IAAM,MAAM,GAAG,UAAU,CAAC,yBAAyB,CAEd;IAErC,8CAA8C,CAAC,UAAU,CAAC,CAAC;AAC3D,IAAA,2BAA2B,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;AAC7C,CAAC;AAED;AAEA,SAASG,2BAAyB,CAAC,IAAY,EAAA;AAC7C,IAAA,OAAO,IAAI,SAAS,CAAC,mCAA4B,IAAI,EAAA,uCAAA,CAAuC,CAAC,CAAC;AAChG,CAAC;AAED;AAEA,SAASC,sCAAoC,CAAC,IAAY,EAAA;AACxD,IAAA,OAAO,IAAI,SAAS,CAClB,oDAA6C,IAAI,EAAA,wDAAA,CAAwD,CAAC,CAAC;AAC/G,CAAC;AAGD;AAEA,SAAS,gCAAgC,CAAC,IAAY,EAAA;AACpD,IAAA,OAAO,IAAI,SAAS,CAClB,gDAAyC,IAAI,EAAA,oDAAA,CAAoD,CAAC,CAAC;AACvG,CAAC;AAED,SAAS,0BAA0B,CAAC,IAAY,EAAA;IAC9C,OAAO,IAAI,SAAS,CAAC,SAAS,GAAG,IAAI,GAAG,mCAAmC,CAAC,CAAC;AAC/E,CAAC;AAED,SAAS,oCAAoC,CAAC,MAAmC,EAAA;IAC/E,MAAM,CAAC,cAAc,GAAG,UAAU,CAAC,UAAC,OAAO,EAAE,MAAM,EAAA;AACjD,QAAA,MAAM,CAAC,sBAAsB,GAAG,OAAO,CAAC;AACxC,QAAA,MAAM,CAAC,qBAAqB,GAAG,MAAM,CAAC;AACtC,QAAA,MAAM,CAAC,mBAAmB,GAAG,SAAS,CAAC;AACzC,KAAC,CAAC,CAAC;AACL,CAAC;AAED,SAAS,8CAA8C,CAAC,MAAmC,EAAE,MAAW,EAAA;IACtG,oCAAoC,CAAC,MAAM,CAAC,CAAC;AAC7C,IAAA,gCAAgC,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;AACnD,CAAC;AAED,SAAS,8CAA8C,CAAC,MAAmC,EAAA;IACzF,oCAAoC,CAAC,MAAM,CAAC,CAAC;IAC7C,iCAAiC,CAAC,MAAM,CAAC,CAAC;AAC5C,CAAC;AAED,SAAS,gCAAgC,CAAC,MAAmC,EAAE,MAAW,EAAA;AACxF,IAAA,IAAI,MAAM,CAAC,qBAAqB,KAAK,SAAS,EAAE;QAC9C,OAAO;KAEwC;AAEjD,IAAA,yBAAyB,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;AACjD,IAAA,MAAM,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC;AACrC,IAAA,MAAM,CAAC,sBAAsB,GAAG,SAAS,CAAC;AAC1C,IAAA,MAAM,CAAC,qBAAqB,GAAG,SAAS,CAAC;AACzC,IAAA,MAAM,CAAC,mBAAmB,GAAG,UAAU,CAAC;AAC1C,CAAC;AAED,SAAS,yCAAyC,CAAC,MAAmC,EAAE,MAAW,EAAA;AAKjG,IAAA,8CAA8C,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;AACjE,CAAC;AAED,SAAS,iCAAiC,CAAC,MAAmC,EAAA;AAC5E,IAAA,IAAI,MAAM,CAAC,sBAAsB,KAAK,SAAS,EAAE;QAC/C,OAAO;KAEwC;AAEjD,IAAA,MAAM,CAAC,sBAAsB,CAAC,SAAS,CAAC,CAAC;AACzC,IAAA,MAAM,CAAC,sBAAsB,GAAG,SAAS,CAAC;AAC1C,IAAA,MAAM,CAAC,qBAAqB,GAAG,SAAS,CAAC;AACzC,IAAA,MAAM,CAAC,mBAAmB,GAAG,UAAU,CAAC;AAC1C,CAAC;AAED,SAAS,mCAAmC,CAAC,MAAmC,EAAA;IAC9E,MAAM,CAAC,aAAa,GAAG,UAAU,CAAC,UAAC,OAAO,EAAE,MAAM,EAAA;AAChD,QAAA,MAAM,CAAC,qBAAqB,GAAG,OAAO,CAAC;AACvC,QAAA,MAAM,CAAC,oBAAoB,GAAG,MAAM,CAAC;AACvC,KAAC,CAAC,CAAC;AACH,IAAA,MAAM,CAAC,kBAAkB,GAAG,SAAS,CAAC;AACxC,CAAC;AAED,SAAS,6CAA6C,CAAC,MAAmC,EAAE,MAAW,EAAA;IACrG,mCAAmC,CAAC,MAAM,CAAC,CAAC;AAC5C,IAAA,+BAA+B,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;AAClD,CAAC;AAED,SAAS,6CAA6C,CAAC,MAAmC,EAAA;IACxF,mCAAmC,CAAC,MAAM,CAAC,CAAC;IAC5C,gCAAgC,CAAC,MAAM,CAAC,CAAC;AAC3C,CAAC;AAED,SAAS,+BAA+B,CAAC,MAAmC,EAAE,MAAW,EAAA;AACvF,IAAA,IAAI,MAAM,CAAC,oBAAoB,KAAK,SAAS,EAAE;QAC7C,OAAO;KACR;AAED,IAAA,yBAAyB,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;AAChD,IAAA,MAAM,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC;AACpC,IAAA,MAAM,CAAC,qBAAqB,GAAG,SAAS,CAAC;AACzC,IAAA,MAAM,CAAC,oBAAoB,GAAG,SAAS,CAAC;AACxC,IAAA,MAAM,CAAC,kBAAkB,GAAG,UAAU,CAAC;AACzC,CAAC;AAED,SAAS,8BAA8B,CAAC,MAAmC,EAAA;IAIzE,mCAAmC,CAAC,MAAM,CAAC,CAAC;AAC9C,CAAC;AAED,SAAS,wCAAwC,CAAC,MAAmC,EAAE,MAAW,EAAA;AAIhG,IAAA,6CAA6C,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;AAChE,CAAC;AAED,SAAS,gCAAgC,CAAC,MAAmC,EAAA;AAC3E,IAAA,IAAI,MAAM,CAAC,qBAAqB,KAAK,SAAS,EAAE;QAC9C,OAAO;KACR;AAED,IAAA,MAAM,CAAC,qBAAqB,CAAC,SAAS,CAAC,CAAC;AACxC,IAAA,MAAM,CAAC,qBAAqB,GAAG,SAAS,CAAC;AACzC,IAAA,MAAM,CAAC,oBAAoB,GAAG,SAAS,CAAC;AACxC,IAAA,MAAM,CAAC,kBAAkB,GAAG,WAAW,CAAC;AAC1C;;AC35CA;AAEA,SAAS,UAAU,GAAA;AACjB,IAAA,IAAI,OAAO,UAAU,KAAK,WAAW,EAAE;AACrC,QAAA,OAAO,UAAU,CAAC;KACnB;AAAM,SAAA,IAAI,OAAO,IAAI,KAAK,WAAW,EAAE;AACtC,QAAA,OAAO,IAAI,CAAC;KACb;AAAM,SAAA,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;AACxC,QAAA,OAAO,MAAM,CAAC;KACf;AACD,IAAA,OAAO,SAAS,CAAC;AACnB,CAAC;AAEM,IAAM,OAAO,GAAG,UAAU,EAAE;;ACbnC;AAWA,SAAS,yBAAyB,CAAC,IAAa,EAAA;AAC9C,IAAA,IAAI,EAAE,OAAO,IAAI,KAAK,UAAU,IAAI,OAAO,IAAI,KAAK,QAAQ,CAAC,EAAE;AAC7D,QAAA,OAAO,KAAK,CAAC;KACd;AACD,IAAA,IAAK,IAAgC,CAAC,IAAI,KAAK,cAAc,EAAE;AAC7D,QAAA,OAAO,KAAK,CAAC;KACd;AACD,IAAA,IAAI;QACF,IAAK,IAAgC,EAAE,CAAC;AACxC,QAAA,OAAO,IAAI,CAAC;KACb;AAAC,IAAA,OAAA,EAAA,EAAM;AACN,QAAA,OAAO,KAAK,CAAC;KACd;AACH,CAAC;AAED;;;;AAIG;AACH,SAAS,aAAa,GAAA;IACpB,IAAM,IAAI,GAAG,OAAO,KAAA,IAAA,IAAP,OAAO,KAAP,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,OAAO,CAAE,YAAY,CAAC;AACnC,IAAA,OAAO,yBAAyB,CAAC,IAAI,CAAC,GAAG,IAAI,GAAG,SAAS,CAAC;AAC5D,CAAC;AAED;;;AAGG;AACH,SAAS,cAAc,GAAA;;AAErB,IAAA,IAAM,IAAI,GAAG,SAAS,YAAY,CAAqB,OAAgB,EAAE,IAAa,EAAA;AACpF,QAAA,IAAI,CAAC,OAAO,GAAG,OAAO,IAAI,EAAE,CAAC;AAC7B,QAAA,IAAI,CAAC,IAAI,GAAG,IAAI,IAAI,OAAO,CAAC;AAC5B,QAAA,IAAI,KAAK,CAAC,iBAAiB,EAAE;YAC3B,KAAK,CAAC,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;SACjD;AACH,KAAQ,CAAC;AACT,IAAA,eAAe,CAAC,IAAI,EAAE,cAAc,CAAC,CAAC;IACtC,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;IAChD,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,SAAS,EAAE,aAAa,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,CAAC,CAAC;AAC1G,IAAA,OAAO,IAAI,CAAC;AACd,CAAC;AAED;AACA,IAAM,YAAY,GAA4B,aAAa,EAAE,IAAI,cAAc,EAAE;;AC5BjE,SAAA,oBAAoB,CAAI,MAAyB,EACzB,IAAuB,EACvB,YAAqB,EACrB,YAAqB,EACrB,aAAsB,EACtB,MAA+B,EAAA;AAUrE,IAAA,IAAM,MAAM,GAAG,kCAAkC,CAAI,MAAM,CAAC,CAAC;AAC7D,IAAA,IAAM,MAAM,GAAG,kCAAkC,CAAI,IAAI,CAAC,CAAC;AAE3D,IAAA,MAAM,CAAC,UAAU,GAAG,IAAI,CAAC;IAEzB,IAAI,YAAY,GAAG,KAAK,CAAC;;AAGzB,IAAA,IAAI,YAAY,GAAG,mBAAmB,CAAO,SAAS,CAAC,CAAC;AAExD,IAAA,OAAO,UAAU,CAAC,UAAC,OAAO,EAAE,MAAM,EAAA;AAChC,QAAA,IAAI,cAA0B,CAAC;AAC/B,QAAA,IAAI,MAAM,KAAK,SAAS,EAAE;AACxB,YAAA,cAAc,GAAG,YAAA;gBACf,IAAM,KAAK,GAAG,MAAM,CAAC,MAAM,KAAK,SAAS,GAAG,MAAM,CAAC,MAAM,GAAG,IAAI,YAAY,CAAC,SAAS,EAAE,YAAY,CAAC,CAAC;gBACtG,IAAM,OAAO,GAA+B,EAAE,CAAC;gBAC/C,IAAI,CAAC,YAAY,EAAE;oBACjB,OAAO,CAAC,IAAI,CAAC,YAAA;AACX,wBAAA,IAAI,IAAI,CAAC,MAAM,KAAK,UAAU,EAAE;AAC9B,4BAAA,OAAO,mBAAmB,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;yBACzC;AACD,wBAAA,OAAO,mBAAmB,CAAC,SAAS,CAAC,CAAC;AACxC,qBAAC,CAAC,CAAC;iBACJ;gBACD,IAAI,CAAC,aAAa,EAAE;oBAClB,OAAO,CAAC,IAAI,CAAC,YAAA;AACX,wBAAA,IAAI,MAAM,CAAC,MAAM,KAAK,UAAU,EAAE;AAChC,4BAAA,OAAO,oBAAoB,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;yBAC5C;AACD,wBAAA,OAAO,mBAAmB,CAAC,SAAS,CAAC,CAAC;AACxC,qBAAC,CAAC,CAAC;iBACJ;AACD,gBAAA,kBAAkB,CAAC,YAAA,EAAM,OAAA,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,UAAA,MAAM,EAAA,EAAI,OAAA,MAAM,EAAE,CAAA,EAAA,CAAC,CAAC,CAAA,EAAA,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;AACtF,aAAC,CAAC;AAEF,YAAA,IAAI,MAAM,CAAC,OAAO,EAAE;AAClB,gBAAA,cAAc,EAAE,CAAC;gBACjB,OAAO;aACR;AAED,YAAA,MAAM,CAAC,gBAAgB,CAAC,OAAO,EAAE,cAAc,CAAC,CAAC;SAClD;;;;AAKD,QAAA,SAAS,QAAQ,GAAA;AACf,YAAA,OAAO,UAAU,CAAO,UAAC,WAAW,EAAE,UAAU,EAAA;gBAC9C,SAAS,IAAI,CAAC,IAAa,EAAA;oBACzB,IAAI,IAAI,EAAE;AACR,wBAAA,WAAW,EAAE,CAAC;qBACf;yBAAM;;;wBAGL,kBAAkB,CAAC,QAAQ,EAAE,EAAE,IAAI,EAAE,UAAU,CAAC,CAAC;qBAClD;iBACF;gBAED,IAAI,CAAC,KAAK,CAAC,CAAC;AACd,aAAC,CAAC,CAAC;SACJ;AAED,QAAA,SAAS,QAAQ,GAAA;YACf,IAAI,YAAY,EAAE;AAChB,gBAAA,OAAO,mBAAmB,CAAC,IAAI,CAAC,CAAC;aAClC;AAED,YAAA,OAAO,kBAAkB,CAAC,MAAM,CAAC,aAAa,EAAE,YAAA;AAC9C,gBAAA,OAAO,UAAU,CAAU,UAAC,WAAW,EAAE,UAAU,EAAA;oBACjD,+BAA+B,CAC7B,MAAM,EACN;wBACE,WAAW,EAAE,UAAA,KAAK,EAAA;AAChB,4BAAA,YAAY,GAAG,kBAAkB,CAAC,gCAAgC,CAAC,MAAM,EAAE,KAAK,CAAC,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;4BACpG,WAAW,CAAC,KAAK,CAAC,CAAC;yBACpB;wBACD,WAAW,EAAE,cAAM,OAAA,WAAW,CAAC,IAAI,CAAC,GAAA;AACpC,wBAAA,WAAW,EAAE,UAAU;AACxB,qBAAA,CACF,CAAC;AACJ,iBAAC,CAAC,CAAC;AACL,aAAC,CAAC,CAAC;SACJ;;QAGD,kBAAkB,CAAC,MAAM,EAAE,MAAM,CAAC,cAAc,EAAE,UAAA,WAAW,EAAA;YAC3D,IAAI,CAAC,YAAY,EAAE;AACjB,gBAAA,kBAAkB,CAAC,YAAM,EAAA,OAAA,mBAAmB,CAAC,IAAI,EAAE,WAAW,CAAC,CAAA,EAAA,EAAE,IAAI,EAAE,WAAW,CAAC,CAAC;aACrF;iBAAM;AACL,gBAAA,QAAQ,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;aAC7B;AACD,YAAA,OAAO,IAAI,CAAC;AACd,SAAC,CAAC,CAAC;;QAGH,kBAAkB,CAAC,IAAI,EAAE,MAAM,CAAC,cAAc,EAAE,UAAA,WAAW,EAAA;YACzD,IAAI,CAAC,aAAa,EAAE;AAClB,gBAAA,kBAAkB,CAAC,YAAM,EAAA,OAAA,oBAAoB,CAAC,MAAM,EAAE,WAAW,CAAC,CAAA,EAAA,EAAE,IAAI,EAAE,WAAW,CAAC,CAAC;aACxF;iBAAM;AACL,gBAAA,QAAQ,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;aAC7B;AACD,YAAA,OAAO,IAAI,CAAC;AACd,SAAC,CAAC,CAAC;;AAGH,QAAA,iBAAiB,CAAC,MAAM,EAAE,MAAM,CAAC,cAAc,EAAE,YAAA;YAC/C,IAAI,CAAC,YAAY,EAAE;gBACjB,kBAAkB,CAAC,YAAM,EAAA,OAAA,oDAAoD,CAAC,MAAM,CAAC,CAAA,EAAA,CAAC,CAAC;aACxF;iBAAM;AACL,gBAAA,QAAQ,EAAE,CAAC;aACZ;AACD,YAAA,OAAO,IAAI,CAAC;AACd,SAAC,CAAC,CAAC;;QAGH,IAAI,mCAAmC,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,MAAM,KAAK,QAAQ,EAAE;AACzE,YAAA,IAAM,YAAU,GAAG,IAAI,SAAS,CAAC,6EAA6E,CAAC,CAAC;YAEhH,IAAI,CAAC,aAAa,EAAE;AAClB,gBAAA,kBAAkB,CAAC,YAAM,EAAA,OAAA,oBAAoB,CAAC,MAAM,EAAE,YAAU,CAAC,CAAA,EAAA,EAAE,IAAI,EAAE,YAAU,CAAC,CAAC;aACtF;iBAAM;AACL,gBAAA,QAAQ,CAAC,IAAI,EAAE,YAAU,CAAC,CAAC;aAC5B;SACF;AAED,QAAA,yBAAyB,CAAC,QAAQ,EAAE,CAAC,CAAC;AAEtC,QAAA,SAAS,qBAAqB,GAAA;;;YAG5B,IAAM,eAAe,GAAG,YAAY,CAAC;YACrC,OAAO,kBAAkB,CACvB,YAAY,EACZ,cAAM,OAAA,eAAe,KAAK,YAAY,GAAG,qBAAqB,EAAE,GAAG,SAAS,CAAA,EAAA,CAC7E,CAAC;SACH;AAED,QAAA,SAAS,kBAAkB,CAAC,MAAuC,EACvC,OAAsB,EACtB,MAA6B,EAAA;AACvD,YAAA,IAAI,MAAM,CAAC,MAAM,KAAK,SAAS,EAAE;AAC/B,gBAAA,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;aAC7B;iBAAM;AACL,gBAAA,aAAa,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;aAChC;SACF;AAED,QAAA,SAAS,iBAAiB,CAAC,MAAuC,EAAE,OAAsB,EAAE,MAAkB,EAAA;AAC5G,YAAA,IAAI,MAAM,CAAC,MAAM,KAAK,QAAQ,EAAE;AAC9B,gBAAA,MAAM,EAAE,CAAC;aACV;iBAAM;AACL,gBAAA,eAAe,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;aAClC;SACF;AAED,QAAA,SAAS,kBAAkB,CAAC,MAA8B,EAAE,eAAyB,EAAE,aAAmB,EAAA;YACxG,IAAI,YAAY,EAAE;gBAChB,OAAO;aACR;YACD,YAAY,GAAG,IAAI,CAAC;AAEpB,YAAA,IAAI,IAAI,CAAC,MAAM,KAAK,UAAU,IAAI,CAAC,mCAAmC,CAAC,IAAI,CAAC,EAAE;AAC5E,gBAAA,eAAe,CAAC,qBAAqB,EAAE,EAAE,SAAS,CAAC,CAAC;aACrD;iBAAM;AACL,gBAAA,SAAS,EAAE,CAAC;aACb;AAED,YAAA,SAAS,SAAS,GAAA;AAChB,gBAAA,WAAW,CACT,MAAM,EAAE,EACR,YAAM,EAAA,OAAA,QAAQ,CAAC,eAAe,EAAE,aAAa,CAAC,CAAxC,EAAwC,EAC9C,UAAA,QAAQ,EAAA,EAAI,OAAA,QAAQ,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAA,EAAA,CACrC,CAAC;AACF,gBAAA,OAAO,IAAI,CAAC;aACb;SACF;AAED,QAAA,SAAS,QAAQ,CAAC,OAAiB,EAAE,KAAW,EAAA;YAC9C,IAAI,YAAY,EAAE;gBAChB,OAAO;aACR;YACD,YAAY,GAAG,IAAI,CAAC;AAEpB,YAAA,IAAI,IAAI,CAAC,MAAM,KAAK,UAAU,IAAI,CAAC,mCAAmC,CAAC,IAAI,CAAC,EAAE;AAC5E,gBAAA,eAAe,CAAC,qBAAqB,EAAE,EAAE,cAAM,OAAA,QAAQ,CAAC,OAAO,EAAE,KAAK,CAAC,CAAxB,EAAwB,CAAC,CAAC;aAC1E;iBAAM;AACL,gBAAA,QAAQ,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;aAC1B;SACF;AAED,QAAA,SAAS,QAAQ,CAAC,OAAiB,EAAE,KAAW,EAAA;YAC9C,kCAAkC,CAAC,MAAM,CAAC,CAAC;YAC3C,kCAAkC,CAAC,MAAM,CAAC,CAAC;AAE3C,YAAA,IAAI,MAAM,KAAK,SAAS,EAAE;AACxB,gBAAA,MAAM,CAAC,mBAAmB,CAAC,OAAO,EAAE,cAAc,CAAC,CAAC;aACrD;YACD,IAAI,OAAO,EAAE;gBACX,MAAM,CAAC,KAAK,CAAC,CAAC;aACf;iBAAM;gBACL,OAAO,CAAC,SAAS,CAAC,CAAC;aACpB;AAED,YAAA,OAAO,IAAI,CAAC;SACb;AACH,KAAC,CAAC,CAAC;AACL;;ACzOA;;;;AAIG;AACH,IAAA,+BAAA,kBAAA,YAAA;AAwBE,IAAA,SAAA,+BAAA,GAAA;AACE,QAAA,MAAM,IAAI,SAAS,CAAC,qBAAqB,CAAC,CAAC;KAC5C;AAMD,IAAA,MAAA,CAAA,cAAA,CAAI,+BAAW,CAAA,SAAA,EAAA,aAAA,EAAA;AAJf;;;AAGG;AACH,QAAA,GAAA,EAAA,YAAA;AACE,YAAA,IAAI,CAAC,iCAAiC,CAAC,IAAI,CAAC,EAAE;AAC5C,gBAAA,MAAMA,sCAAoC,CAAC,aAAa,CAAC,CAAC;aAC3D;AAED,YAAA,OAAO,6CAA6C,CAAC,IAAI,CAAC,CAAC;SAC5D;;;AAAA,KAAA,CAAA,CAAA;AAED;;;AAGG;AACH,IAAA,+BAAA,CAAA,SAAA,CAAA,KAAK,GAAL,YAAA;AACE,QAAA,IAAI,CAAC,iCAAiC,CAAC,IAAI,CAAC,EAAE;AAC5C,YAAA,MAAMA,sCAAoC,CAAC,OAAO,CAAC,CAAC;SACrD;AAED,QAAA,IAAI,CAAC,gDAAgD,CAAC,IAAI,CAAC,EAAE;AAC3D,YAAA,MAAM,IAAI,SAAS,CAAC,iDAAiD,CAAC,CAAC;SACxE;QAED,oCAAoC,CAAC,IAAI,CAAC,CAAC;KAC5C,CAAA;IAMD,+BAAO,CAAA,SAAA,CAAA,OAAA,GAAP,UAAQ,KAAqB,EAAA;QAArB,IAAA,KAAA,KAAA,KAAA,CAAA,EAAA,EAAA,QAAW,SAAU,CAAA,EAAA;AAC3B,QAAA,IAAI,CAAC,iCAAiC,CAAC,IAAI,CAAC,EAAE;AAC5C,YAAA,MAAMA,sCAAoC,CAAC,SAAS,CAAC,CAAC;SACvD;AAED,QAAA,IAAI,CAAC,gDAAgD,CAAC,IAAI,CAAC,EAAE;AAC3D,YAAA,MAAM,IAAI,SAAS,CAAC,mDAAmD,CAAC,CAAC;SAC1E;AAED,QAAA,OAAO,sCAAsC,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;KAC5D,CAAA;AAED;;AAEG;IACH,+BAAK,CAAA,SAAA,CAAA,KAAA,GAAL,UAAM,CAAkB,EAAA;AAAlB,QAAA,IAAA,CAAA,KAAA,KAAA,CAAA,EAAA,EAAA,CAAkB,GAAA,SAAA,CAAA,EAAA;AACtB,QAAA,IAAI,CAAC,iCAAiC,CAAC,IAAI,CAAC,EAAE;AAC5C,YAAA,MAAMA,sCAAoC,CAAC,OAAO,CAAC,CAAC;SACrD;AAED,QAAA,oCAAoC,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;KAC/C,CAAA;;AAGD,IAAA,+BAAA,CAAA,SAAA,CAAC,WAAW,CAAC,GAAb,UAAc,MAAW,EAAA;QACvB,UAAU,CAAC,IAAI,CAAC,CAAC;QACjB,IAAM,MAAM,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;QAC7C,8CAA8C,CAAC,IAAI,CAAC,CAAC;AACrD,QAAA,OAAO,MAAM,CAAC;KACf,CAAA;;AAGD,IAAA,+BAAA,CAAA,SAAA,CAAC,SAAS,CAAC,GAAX,UAAY,WAA2B,EAAA;AACrC,QAAA,IAAM,MAAM,GAAG,IAAI,CAAC,yBAAyB,CAAC;QAE9C,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;AAC1B,YAAA,IAAM,KAAK,GAAG,YAAY,CAAC,IAAI,CAAC,CAAC;AAEjC,YAAA,IAAI,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;gBACpD,8CAA8C,CAAC,IAAI,CAAC,CAAC;gBACrD,mBAAmB,CAAC,MAAM,CAAC,CAAC;aAC7B;iBAAM;gBACL,+CAA+C,CAAC,IAAI,CAAC,CAAC;aACvD;AAED,YAAA,WAAW,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;SAChC;aAAM;AACL,YAAA,4BAA4B,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;YAClD,+CAA+C,CAAC,IAAI,CAAC,CAAC;SACvD;KACF,CAAA;;IAGD,+BAAC,CAAA,SAAA,CAAA,YAAY,CAAC,GAAd,YAAA;;KAEC,CAAA;IACH,OAAC,+BAAA,CAAA;AAAD,CAAC,EAAA,EAAA;AAED,MAAM,CAAC,gBAAgB,CAAC,+BAA+B,CAAC,SAAS,EAAE;AACjE,IAAA,KAAK,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE;AAC3B,IAAA,OAAO,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE;AAC7B,IAAA,KAAK,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE;AAC3B,IAAA,WAAW,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE;AAClC,CAAA,CAAC,CAAC;AACH,eAAe,CAAC,+BAA+B,CAAC,SAAS,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;AAC1E,eAAe,CAAC,+BAA+B,CAAC,SAAS,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;AAC9E,eAAe,CAAC,+BAA+B,CAAC,SAAS,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;AAC1E,IAAI,OAAOJ,cAAM,CAAC,WAAW,KAAK,QAAQ,EAAE;IAC1C,MAAM,CAAC,cAAc,CAAC,+BAA+B,CAAC,SAAS,EAAEA,cAAM,CAAC,WAAW,EAAE;AACnF,QAAA,KAAK,EAAE,iCAAiC;AACxC,QAAA,YAAY,EAAE,IAAI;AACnB,KAAA,CAAC,CAAC;AACL,CAAC;AAED;AAEA,SAAS,iCAAiC,CAAU,CAAM,EAAA;AACxD,IAAA,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE;AACpB,QAAA,OAAO,KAAK,CAAC;KACd;AAED,IAAA,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,2BAA2B,CAAC,EAAE;AACzE,QAAA,OAAO,KAAK,CAAC;KACd;IAED,OAAO,CAAC,YAAY,+BAA+B,CAAC;AACtD,CAAC;AAED,SAAS,+CAA+C,CAAC,UAAgD,EAAA;AACvG,IAAA,IAAM,UAAU,GAAG,6CAA6C,CAAC,UAAU,CAAC,CAAC;IAC7E,IAAI,CAAC,UAAU,EAAE;QACf,OAAO;KACR;AAED,IAAA,IAAI,UAAU,CAAC,QAAQ,EAAE;AACvB,QAAA,UAAU,CAAC,UAAU,GAAG,IAAI,CAAC;QAC7B,OAAO;KAGsB;AAE/B,IAAA,UAAU,CAAC,QAAQ,GAAG,IAAI,CAAC;AAE3B,IAAA,IAAM,WAAW,GAAG,UAAU,CAAC,cAAc,EAAE,CAAC;IAChD,WAAW,CACT,WAAW,EACX,YAAA;AACE,QAAA,UAAU,CAAC,QAAQ,GAAG,KAAK,CAAC;AAE5B,QAAA,IAAI,UAAU,CAAC,UAAU,EAAE;AACzB,YAAA,UAAU,CAAC,UAAU,GAAG,KAAK,CAAC;YAC9B,+CAA+C,CAAC,UAAU,CAAC,CAAC;SAC7D;AAED,QAAA,OAAO,IAAI,CAAC;KACb,EACD,UAAA,CAAC,EAAA;AACC,QAAA,oCAAoC,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC;AACpD,QAAA,OAAO,IAAI,CAAC;AACd,KAAC,CACF,CAAC;AACJ,CAAC;AAED,SAAS,6CAA6C,CAAC,UAAgD,EAAA;AACrG,IAAA,IAAM,MAAM,GAAG,UAAU,CAAC,yBAAyB,CAAC;AAEpD,IAAA,IAAI,CAAC,gDAAgD,CAAC,UAAU,CAAC,EAAE;AACjE,QAAA,OAAO,KAAK,CAAC;KACd;AAED,IAAA,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE;AACxB,QAAA,OAAO,KAAK,CAAC;KACd;AAED,IAAA,IAAI,sBAAsB,CAAC,MAAM,CAAC,IAAI,gCAAgC,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE;AAClF,QAAA,OAAO,IAAI,CAAC;KACb;AAED,IAAA,IAAM,WAAW,GAAG,6CAA6C,CAAC,UAAU,CAAC,CAChD;AAC7B,IAAA,IAAI,WAAY,GAAG,CAAC,EAAE;AACpB,QAAA,OAAO,IAAI,CAAC;KACb;AAED,IAAA,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAS,8CAA8C,CAAC,UAAgD,EAAA;AACtG,IAAA,UAAU,CAAC,cAAc,GAAG,SAAU,CAAC;AACvC,IAAA,UAAU,CAAC,gBAAgB,GAAG,SAAU,CAAC;AACzC,IAAA,UAAU,CAAC,sBAAsB,GAAG,SAAU,CAAC;AACjD,CAAC;AAED;AAEM,SAAU,oCAAoC,CAAC,UAAgD,EAAA;AACnG,IAAA,IAAI,CAAC,gDAAgD,CAAC,UAAU,CAAC,EAAE;QACjE,OAAO;KACR;AAED,IAAA,IAAM,MAAM,GAAG,UAAU,CAAC,yBAAyB,CAAC;AAEpD,IAAA,UAAU,CAAC,eAAe,GAAG,IAAI,CAAC;IAElC,IAAI,UAAU,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;QAClC,8CAA8C,CAAC,UAAU,CAAC,CAAC;QAC3D,mBAAmB,CAAC,MAAM,CAAC,CAAC;KAC7B;AACH,CAAC;AAEe,SAAA,sCAAsC,CACpD,UAA8C,EAC9C,KAAQ,EAAA;AAER,IAAA,IAAI,CAAC,gDAAgD,CAAC,UAAU,CAAC,EAAE;QACjE,OAAO;KACR;AAED,IAAA,IAAM,MAAM,GAAG,UAAU,CAAC,yBAAyB,CAAC;AAEpD,IAAA,IAAI,sBAAsB,CAAC,MAAM,CAAC,IAAI,gCAAgC,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE;AAClF,QAAA,gCAAgC,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;KACxD;SAAM;QACL,IAAI,SAAS,SAAA,CAAC;AACd,QAAA,IAAI;AACF,YAAA,SAAS,GAAG,UAAU,CAAC,sBAAsB,CAAC,KAAK,CAAC,CAAC;SACtD;QAAC,OAAO,UAAU,EAAE;AACnB,YAAA,oCAAoC,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;AAC7D,YAAA,MAAM,UAAU,CAAC;SAClB;AAED,QAAA,IAAI;AACF,YAAA,oBAAoB,CAAC,UAAU,EAAE,KAAK,EAAE,SAAS,CAAC,CAAC;SACpD;QAAC,OAAO,QAAQ,EAAE;AACjB,YAAA,oCAAoC,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;AAC3D,YAAA,MAAM,QAAQ,CAAC;SAChB;KACF;IAED,+CAA+C,CAAC,UAAU,CAAC,CAAC;AAC9D,CAAC;AAEe,SAAA,oCAAoC,CAAC,UAAgD,EAAE,CAAM,EAAA;AAC3G,IAAA,IAAM,MAAM,GAAG,UAAU,CAAC,yBAAyB,CAAC;AAEpD,IAAA,IAAI,MAAM,CAAC,MAAM,KAAK,UAAU,EAAE;QAChC,OAAO;KACR;IAED,UAAU,CAAC,UAAU,CAAC,CAAC;IAEvB,8CAA8C,CAAC,UAAU,CAAC,CAAC;AAC3D,IAAA,mBAAmB,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;AACjC,CAAC;AAEK,SAAU,6CAA6C,CAC3D,UAAgD,EAAA;AAEhD,IAAA,IAAM,KAAK,GAAG,UAAU,CAAC,yBAAyB,CAAC,MAAM,CAAC;AAE1D,IAAA,IAAI,KAAK,KAAK,SAAS,EAAE;AACvB,QAAA,OAAO,IAAI,CAAC;KACb;AACD,IAAA,IAAI,KAAK,KAAK,QAAQ,EAAE;AACtB,QAAA,OAAO,CAAC,CAAC;KACV;AAED,IAAA,OAAO,UAAU,CAAC,YAAY,GAAG,UAAU,CAAC,eAAe,CAAC;AAC9D,CAAC;AAED;AACM,SAAU,8CAA8C,CAC5D,UAAgD,EAAA;AAEhD,IAAA,IAAI,6CAA6C,CAAC,UAAU,CAAC,EAAE;AAC7D,QAAA,OAAO,KAAK,CAAC;KACd;AAED,IAAA,OAAO,IAAI,CAAC;AACd,CAAC;AAEK,SAAU,gDAAgD,CAC9D,UAAgD,EAAA;AAEhD,IAAA,IAAM,KAAK,GAAG,UAAU,CAAC,yBAAyB,CAAC,MAAM,CAAC;IAE1D,IAAI,CAAC,UAAU,CAAC,eAAe,IAAI,KAAK,KAAK,UAAU,EAAE;AACvD,QAAA,OAAO,IAAI,CAAC;KACb;AAED,IAAA,OAAO,KAAK,CAAC;AACf,CAAC;AAEe,SAAA,oCAAoC,CAAI,MAAyB,EACzB,UAA8C,EAC9C,cAA8C,EAC9C,aAAkC,EAClC,eAA+C,EAC/C,aAAqB,EACrB,aAA6C,EAAA;AAGnG,IAAA,UAAU,CAAC,yBAAyB,GAAG,MAAM,CAAC;AAE9C,IAAA,UAAU,CAAC,MAAM,GAAG,SAAU,CAAC;AAC/B,IAAA,UAAU,CAAC,eAAe,GAAG,SAAU,CAAC;IACxC,UAAU,CAAC,UAAU,CAAC,CAAC;AAEvB,IAAA,UAAU,CAAC,QAAQ,GAAG,KAAK,CAAC;AAC5B,IAAA,UAAU,CAAC,eAAe,GAAG,KAAK,CAAC;AACnC,IAAA,UAAU,CAAC,UAAU,GAAG,KAAK,CAAC;AAC9B,IAAA,UAAU,CAAC,QAAQ,GAAG,KAAK,CAAC;AAE5B,IAAA,UAAU,CAAC,sBAAsB,GAAG,aAAa,CAAC;AAClD,IAAA,UAAU,CAAC,YAAY,GAAG,aAAa,CAAC;AAExC,IAAA,UAAU,CAAC,cAAc,GAAG,aAAa,CAAC;AAC1C,IAAA,UAAU,CAAC,gBAAgB,GAAG,eAAe,CAAC;AAE9C,IAAA,MAAM,CAAC,yBAAyB,GAAG,UAAU,CAAC;AAE9C,IAAA,IAAM,WAAW,GAAG,cAAc,EAAE,CAAC;AACrC,IAAA,WAAW,CACT,mBAAmB,CAAC,WAAW,CAAC,EAChC,YAAA;AACE,QAAA,UAAU,CAAC,QAAQ,GAAG,IAAI,CAGK;QAE/B,+CAA+C,CAAC,UAAU,CAAC,CAAC;AAC5D,QAAA,OAAO,IAAI,CAAC;KACb,EACD,UAAA,CAAC,EAAA;AACC,QAAA,oCAAoC,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC;AACpD,QAAA,OAAO,IAAI,CAAC;AACd,KAAC,CACF,CAAC;AACJ,CAAC;AAEK,SAAU,wDAAwD,CACtE,MAAyB,EACzB,gBAA8C,EAC9C,aAAqB,EACrB,aAA6C,EAAA;IAE7C,IAAM,UAAU,GAAuC,MAAM,CAAC,MAAM,CAAC,+BAA+B,CAAC,SAAS,CAAC,CAAC;AAEhH,IAAA,IAAI,cAA8C,CAAC;AACnD,IAAA,IAAI,aAAkC,CAAC;AACvC,IAAA,IAAI,eAA+C,CAAC;AAEpD,IAAA,IAAI,gBAAgB,CAAC,KAAK,KAAK,SAAS,EAAE;QACxC,cAAc,GAAG,YAAM,EAAA,OAAA,gBAAgB,CAAC,KAAM,CAAC,UAAU,CAAC,CAAnC,EAAmC,CAAC;KAC5D;SAAM;AACL,QAAA,cAAc,GAAG,YAAM,EAAA,OAAA,SAAS,CAAA,EAAA,CAAC;KAClC;AACD,IAAA,IAAI,gBAAgB,CAAC,IAAI,KAAK,SAAS,EAAE;QACvC,aAAa,GAAG,YAAM,EAAA,OAAA,gBAAgB,CAAC,IAAK,CAAC,UAAU,CAAC,CAAlC,EAAkC,CAAC;KAC1D;SAAM;QACL,aAAa,GAAG,cAAM,OAAA,mBAAmB,CAAC,SAAS,CAAC,CAA9B,EAA8B,CAAC;KACtD;AACD,IAAA,IAAI,gBAAgB,CAAC,MAAM,KAAK,SAAS,EAAE;AACzC,QAAA,eAAe,GAAG,UAAA,MAAM,EAAA,EAAI,OAAA,gBAAgB,CAAC,MAAO,CAAC,MAAM,CAAC,CAAA,EAAA,CAAC;KAC9D;SAAM;QACL,eAAe,GAAG,cAAM,OAAA,mBAAmB,CAAC,SAAS,CAAC,CAA9B,EAA8B,CAAC;KACxD;AAED,IAAA,oCAAoC,CAClC,MAAM,EAAE,UAAU,EAAE,cAAc,EAAE,aAAa,EAAE,eAAe,EAAE,aAAa,EAAE,aAAa,CACjG,CAAC;AACJ,CAAC;AAED;AAEA,SAASI,sCAAoC,CAAC,IAAY,EAAA;AACxD,IAAA,OAAO,IAAI,SAAS,CAClB,oDAA6C,IAAI,EAAA,wDAAA,CAAwD,CAAC,CAAC;AAC/G;;ACxXgB,SAAA,iBAAiB,CAAI,MAAyB,EACzB,eAAwB,EAAA;AAG3D,IAAA,IAAI,8BAA8B,CAAC,MAAM,CAAC,yBAAyB,CAAC,EAAE;AACpE,QAAA,OAAO,qBAAqB,CAAC,MAAuC,CACjB,CAAC;KACrD;AACD,IAAA,OAAO,wBAAwB,CAAC,MAAuB,CAAC,CAAC;AAC3D,CAAC;AAEe,SAAA,wBAAwB,CACtC,MAAyB,EACzB,eAAwB,EAAA;AAKxB,IAAA,IAAM,MAAM,GAAG,kCAAkC,CAAI,MAAM,CAAC,CAAC;IAE7D,IAAI,OAAO,GAAG,KAAK,CAAC;IACpB,IAAI,SAAS,GAAG,KAAK,CAAC;IACtB,IAAI,SAAS,GAAG,KAAK,CAAC;IACtB,IAAI,SAAS,GAAG,KAAK,CAAC;AACtB,IAAA,IAAI,OAAY,CAAC;AACjB,IAAA,IAAI,OAAY,CAAC;AACjB,IAAA,IAAI,OAAiC,CAAC;AACtC,IAAA,IAAI,OAAiC,CAAC;AAEtC,IAAA,IAAI,oBAAqE,CAAC;AAC1E,IAAA,IAAM,aAAa,GAAG,UAAU,CAAY,UAAA,OAAO,EAAA;QACjD,oBAAoB,GAAG,OAAO,CAAC;AACjC,KAAC,CAAC,CAAC;AAEH,IAAA,SAAS,aAAa,GAAA;QACpB,IAAI,OAAO,EAAE;YACX,SAAS,GAAG,IAAI,CAAC;AACjB,YAAA,OAAO,mBAAmB,CAAC,SAAS,CAAC,CAAC;SACvC;QAED,OAAO,GAAG,IAAI,CAAC;AAEf,QAAA,IAAM,WAAW,GAAmB;YAClC,WAAW,EAAE,UAAA,KAAK,EAAA;;;;AAIhB,gBAAAF,eAAc,CAAC,YAAA;oBACb,SAAS,GAAG,KAAK,CAAC;oBAClB,IAAM,MAAM,GAAG,KAAK,CAAC;oBACrB,IAAM,MAAM,GAAG,KAAK,CAAC;;;;;;oBAQrB,IAAI,CAAC,SAAS,EAAE;AACd,wBAAA,sCAAsC,CAAC,OAAO,CAAC,yBAAyB,EAAE,MAAM,CAAC,CAAC;qBACnF;oBACD,IAAI,CAAC,SAAS,EAAE;AACd,wBAAA,sCAAsC,CAAC,OAAO,CAAC,yBAAyB,EAAE,MAAM,CAAC,CAAC;qBACnF;oBAED,OAAO,GAAG,KAAK,CAAC;oBAChB,IAAI,SAAS,EAAE;AACb,wBAAA,aAAa,EAAE,CAAC;qBACjB;AACH,iBAAC,CAAC,CAAC;aACJ;AACD,YAAA,WAAW,EAAE,YAAA;gBACX,OAAO,GAAG,KAAK,CAAC;gBAChB,IAAI,CAAC,SAAS,EAAE;AACd,oBAAA,oCAAoC,CAAC,OAAO,CAAC,yBAAyB,CAAC,CAAC;iBACzE;gBACD,IAAI,CAAC,SAAS,EAAE;AACd,oBAAA,oCAAoC,CAAC,OAAO,CAAC,yBAAyB,CAAC,CAAC;iBACzE;AAED,gBAAA,IAAI,CAAC,SAAS,IAAI,CAAC,SAAS,EAAE;oBAC5B,oBAAoB,CAAC,SAAS,CAAC,CAAC;iBACjC;aACF;AACD,YAAA,WAAW,EAAE,YAAA;gBACX,OAAO,GAAG,KAAK,CAAC;aACjB;SACF,CAAC;AACF,QAAA,+BAA+B,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;AAErD,QAAA,OAAO,mBAAmB,CAAC,SAAS,CAAC,CAAC;KACvC;IAED,SAAS,gBAAgB,CAAC,MAAW,EAAA;QACnC,SAAS,GAAG,IAAI,CAAC;QACjB,OAAO,GAAG,MAAM,CAAC;QACjB,IAAI,SAAS,EAAE;YACb,IAAM,eAAe,GAAG,mBAAmB,CAAC,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC;YAChE,IAAM,YAAY,GAAG,oBAAoB,CAAC,MAAM,EAAE,eAAe,CAAC,CAAC;YACnE,oBAAoB,CAAC,YAAY,CAAC,CAAC;SACpC;AACD,QAAA,OAAO,aAAa,CAAC;KACtB;IAED,SAAS,gBAAgB,CAAC,MAAW,EAAA;QACnC,SAAS,GAAG,IAAI,CAAC;QACjB,OAAO,GAAG,MAAM,CAAC;QACjB,IAAI,SAAS,EAAE;YACb,IAAM,eAAe,GAAG,mBAAmB,CAAC,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC;YAChE,IAAM,YAAY,GAAG,oBAAoB,CAAC,MAAM,EAAE,eAAe,CAAC,CAAC;YACnE,oBAAoB,CAAC,YAAY,CAAC,CAAC;SACpC;AACD,QAAA,OAAO,aAAa,CAAC;KACtB;AAED,IAAA,SAAS,cAAc,GAAA;;KAEtB;IAED,OAAO,GAAG,oBAAoB,CAAC,cAAc,EAAE,aAAa,EAAE,gBAAgB,CAAC,CAAC;IAChF,OAAO,GAAG,oBAAoB,CAAC,cAAc,EAAE,aAAa,EAAE,gBAAgB,CAAC,CAAC;AAEhF,IAAA,aAAa,CAAC,MAAM,CAAC,cAAc,EAAE,UAAC,CAAM,EAAA;AAC1C,QAAA,oCAAoC,CAAC,OAAO,CAAC,yBAAyB,EAAE,CAAC,CAAC,CAAC;AAC3E,QAAA,oCAAoC,CAAC,OAAO,CAAC,yBAAyB,EAAE,CAAC,CAAC,CAAC;AAC3E,QAAA,IAAI,CAAC,SAAS,IAAI,CAAC,SAAS,EAAE;YAC5B,oBAAoB,CAAC,SAAS,CAAC,CAAC;SACjC;AACD,QAAA,OAAO,IAAI,CAAC;AACd,KAAC,CAAC,CAAC;AAEH,IAAA,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;AAC5B,CAAC;AAEK,SAAU,qBAAqB,CAAC,MAA0B,EAAA;AAI9D,IAAA,IAAI,MAAM,GAAgD,kCAAkC,CAAC,MAAM,CAAC,CAAC;IACrG,IAAI,OAAO,GAAG,KAAK,CAAC;IACpB,IAAI,mBAAmB,GAAG,KAAK,CAAC;IAChC,IAAI,mBAAmB,GAAG,KAAK,CAAC;IAChC,IAAI,SAAS,GAAG,KAAK,CAAC;IACtB,IAAI,SAAS,GAAG,KAAK,CAAC;AACtB,IAAA,IAAI,OAAY,CAAC;AACjB,IAAA,IAAI,OAAY,CAAC;AACjB,IAAA,IAAI,OAA2B,CAAC;AAChC,IAAA,IAAI,OAA2B,CAAC;AAEhC,IAAA,IAAI,oBAAqE,CAAC;AAC1E,IAAA,IAAM,aAAa,GAAG,UAAU,CAAO,UAAA,OAAO,EAAA;QAC5C,oBAAoB,GAAG,OAAO,CAAC;AACjC,KAAC,CAAC,CAAC;IAEH,SAAS,kBAAkB,CAAC,UAAuD,EAAA;AACjF,QAAA,aAAa,CAAC,UAAU,CAAC,cAAc,EAAE,UAAA,CAAC,EAAA;AACxC,YAAA,IAAI,UAAU,KAAK,MAAM,EAAE;AACzB,gBAAA,OAAO,IAAI,CAAC;aACb;AACD,YAAA,iCAAiC,CAAC,OAAO,CAAC,yBAAyB,EAAE,CAAC,CAAC,CAAC;AACxE,YAAA,iCAAiC,CAAC,OAAO,CAAC,yBAAyB,EAAE,CAAC,CAAC,CAAC;AACxE,YAAA,IAAI,CAAC,SAAS,IAAI,CAAC,SAAS,EAAE;gBAC5B,oBAAoB,CAAC,SAAS,CAAC,CAAC;aACjC;AACD,YAAA,OAAO,IAAI,CAAC;AACd,SAAC,CAAC,CAAC;KACJ;AAED,IAAA,SAAS,qBAAqB,GAAA;AAC5B,QAAA,IAAI,0BAA0B,CAAC,MAAM,CAAC,EAAE;YAEtC,kCAAkC,CAAC,MAAM,CAAC,CAAC;AAE3C,YAAA,MAAM,GAAG,kCAAkC,CAAC,MAAM,CAAC,CAAC;YACpD,kBAAkB,CAAC,MAAM,CAAC,CAAC;SAC5B;AAED,QAAA,IAAM,WAAW,GAAuC;YACtD,WAAW,EAAE,UAAA,KAAK,EAAA;;;;AAIhB,gBAAAA,eAAc,CAAC,YAAA;oBACb,mBAAmB,GAAG,KAAK,CAAC;oBAC5B,mBAAmB,GAAG,KAAK,CAAC;oBAE5B,IAAM,MAAM,GAAG,KAAK,CAAC;oBACrB,IAAI,MAAM,GAAG,KAAK,CAAC;AACnB,oBAAA,IAAI,CAAC,SAAS,IAAI,CAAC,SAAS,EAAE;AAC5B,wBAAA,IAAI;AACF,4BAAA,MAAM,GAAG,iBAAiB,CAAC,KAAK,CAAC,CAAC;yBACnC;wBAAC,OAAO,MAAM,EAAE;AACf,4BAAA,iCAAiC,CAAC,OAAO,CAAC,yBAAyB,EAAE,MAAM,CAAC,CAAC;AAC7E,4BAAA,iCAAiC,CAAC,OAAO,CAAC,yBAAyB,EAAE,MAAM,CAAC,CAAC;4BAC7E,oBAAoB,CAAC,oBAAoB,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC;4BAC3D,OAAO;yBACR;qBACF;oBAED,IAAI,CAAC,SAAS,EAAE;AACd,wBAAA,mCAAmC,CAAC,OAAO,CAAC,yBAAyB,EAAE,MAAM,CAAC,CAAC;qBAChF;oBACD,IAAI,CAAC,SAAS,EAAE;AACd,wBAAA,mCAAmC,CAAC,OAAO,CAAC,yBAAyB,EAAE,MAAM,CAAC,CAAC;qBAChF;oBAED,OAAO,GAAG,KAAK,CAAC;oBAChB,IAAI,mBAAmB,EAAE;AACvB,wBAAA,cAAc,EAAE,CAAC;qBAClB;yBAAM,IAAI,mBAAmB,EAAE;AAC9B,wBAAA,cAAc,EAAE,CAAC;qBAClB;AACH,iBAAC,CAAC,CAAC;aACJ;AACD,YAAA,WAAW,EAAE,YAAA;gBACX,OAAO,GAAG,KAAK,CAAC;gBAChB,IAAI,CAAC,SAAS,EAAE;AACd,oBAAA,iCAAiC,CAAC,OAAO,CAAC,yBAAyB,CAAC,CAAC;iBACtE;gBACD,IAAI,CAAC,SAAS,EAAE;AACd,oBAAA,iCAAiC,CAAC,OAAO,CAAC,yBAAyB,CAAC,CAAC;iBACtE;gBACD,IAAI,OAAO,CAAC,yBAAyB,CAAC,iBAAiB,CAAC,MAAM,GAAG,CAAC,EAAE;AAClE,oBAAA,mCAAmC,CAAC,OAAO,CAAC,yBAAyB,EAAE,CAAC,CAAC,CAAC;iBAC3E;gBACD,IAAI,OAAO,CAAC,yBAAyB,CAAC,iBAAiB,CAAC,MAAM,GAAG,CAAC,EAAE;AAClE,oBAAA,mCAAmC,CAAC,OAAO,CAAC,yBAAyB,EAAE,CAAC,CAAC,CAAC;iBAC3E;AACD,gBAAA,IAAI,CAAC,SAAS,IAAI,CAAC,SAAS,EAAE;oBAC5B,oBAAoB,CAAC,SAAS,CAAC,CAAC;iBACjC;aACF;AACD,YAAA,WAAW,EAAE,YAAA;gBACX,OAAO,GAAG,KAAK,CAAC;aACjB;SACF,CAAC;AACF,QAAA,+BAA+B,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;KACtD;AAED,IAAA,SAAS,kBAAkB,CAAC,IAAgC,EAAE,UAAmB,EAAA;AAC/E,QAAA,IAAI,6BAA6B,CAAwB,MAAM,CAAC,EAAE;YAEhE,kCAAkC,CAAC,MAAM,CAAC,CAAC;AAE3C,YAAA,MAAM,GAAG,+BAA+B,CAAC,MAAM,CAAC,CAAC;YACjD,kBAAkB,CAAC,MAAM,CAAC,CAAC;SAC5B;QAED,IAAM,UAAU,GAAG,UAAU,GAAG,OAAO,GAAG,OAAO,CAAC;QAClD,IAAM,WAAW,GAAG,UAAU,GAAG,OAAO,GAAG,OAAO,CAAC;AAEnD,QAAA,IAAM,eAAe,GAAgD;YACnE,WAAW,EAAE,UAAA,KAAK,EAAA;;;;AAIhB,gBAAAA,eAAc,CAAC,YAAA;oBACb,mBAAmB,GAAG,KAAK,CAAC;oBAC5B,mBAAmB,GAAG,KAAK,CAAC;oBAE5B,IAAM,YAAY,GAAG,UAAU,GAAG,SAAS,GAAG,SAAS,CAAC;oBACxD,IAAM,aAAa,GAAG,UAAU,GAAG,SAAS,GAAG,SAAS,CAAC;oBAEzD,IAAI,CAAC,aAAa,EAAE;wBAClB,IAAI,WAAW,SAAA,CAAC;AAChB,wBAAA,IAAI;AACF,4BAAA,WAAW,GAAG,iBAAiB,CAAC,KAAK,CAAC,CAAC;yBACxC;wBAAC,OAAO,MAAM,EAAE;AACf,4BAAA,iCAAiC,CAAC,UAAU,CAAC,yBAAyB,EAAE,MAAM,CAAC,CAAC;AAChF,4BAAA,iCAAiC,CAAC,WAAW,CAAC,yBAAyB,EAAE,MAAM,CAAC,CAAC;4BACjF,oBAAoB,CAAC,oBAAoB,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC;4BAC3D,OAAO;yBACR;wBACD,IAAI,CAAC,YAAY,EAAE;AACjB,4BAAA,8CAA8C,CAAC,UAAU,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAC;yBAC7F;AACD,wBAAA,mCAAmC,CAAC,WAAW,CAAC,yBAAyB,EAAE,WAAW,CAAC,CAAC;qBACzF;yBAAM,IAAI,CAAC,YAAY,EAAE;AACxB,wBAAA,8CAA8C,CAAC,UAAU,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAC;qBAC7F;oBAED,OAAO,GAAG,KAAK,CAAC;oBAChB,IAAI,mBAAmB,EAAE;AACvB,wBAAA,cAAc,EAAE,CAAC;qBAClB;yBAAM,IAAI,mBAAmB,EAAE;AAC9B,wBAAA,cAAc,EAAE,CAAC;qBAClB;AACH,iBAAC,CAAC,CAAC;aACJ;YACD,WAAW,EAAE,UAAA,KAAK,EAAA;gBAChB,OAAO,GAAG,KAAK,CAAC;gBAEhB,IAAM,YAAY,GAAG,UAAU,GAAG,SAAS,GAAG,SAAS,CAAC;gBACxD,IAAM,aAAa,GAAG,UAAU,GAAG,SAAS,GAAG,SAAS,CAAC;gBAEzD,IAAI,CAAC,YAAY,EAAE;AACjB,oBAAA,iCAAiC,CAAC,UAAU,CAAC,yBAAyB,CAAC,CAAC;iBACzE;gBACD,IAAI,CAAC,aAAa,EAAE;AAClB,oBAAA,iCAAiC,CAAC,WAAW,CAAC,yBAAyB,CAAC,CAAC;iBAC1E;AAED,gBAAA,IAAI,KAAK,KAAK,SAAS,EAAE;oBAGvB,IAAI,CAAC,YAAY,EAAE;AACjB,wBAAA,8CAA8C,CAAC,UAAU,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAC;qBAC7F;AACD,oBAAA,IAAI,CAAC,aAAa,IAAI,WAAW,CAAC,yBAAyB,CAAC,iBAAiB,CAAC,MAAM,GAAG,CAAC,EAAE;AACxF,wBAAA,mCAAmC,CAAC,WAAW,CAAC,yBAAyB,EAAE,CAAC,CAAC,CAAC;qBAC/E;iBACF;AAED,gBAAA,IAAI,CAAC,YAAY,IAAI,CAAC,aAAa,EAAE;oBACnC,oBAAoB,CAAC,SAAS,CAAC,CAAC;iBACjC;aACF;AACD,YAAA,WAAW,EAAE,YAAA;gBACX,OAAO,GAAG,KAAK,CAAC;aACjB;SACF,CAAC;QACF,4BAA4B,CAAC,MAAM,EAAE,IAAI,EAAE,CAAC,EAAE,eAAe,CAAC,CAAC;KAChE;AAED,IAAA,SAAS,cAAc,GAAA;QACrB,IAAI,OAAO,EAAE;YACX,mBAAmB,GAAG,IAAI,CAAC;AAC3B,YAAA,OAAO,mBAAmB,CAAC,SAAS,CAAC,CAAC;SACvC;QAED,OAAO,GAAG,IAAI,CAAC;QAEf,IAAM,WAAW,GAAG,0CAA0C,CAAC,OAAO,CAAC,yBAAyB,CAAC,CAAC;AAClG,QAAA,IAAI,WAAW,KAAK,IAAI,EAAE;AACxB,YAAA,qBAAqB,EAAE,CAAC;SACzB;aAAM;AACL,YAAA,kBAAkB,CAAC,WAAW,CAAC,KAAM,EAAE,KAAK,CAAC,CAAC;SAC/C;AAED,QAAA,OAAO,mBAAmB,CAAC,SAAS,CAAC,CAAC;KACvC;AAED,IAAA,SAAS,cAAc,GAAA;QACrB,IAAI,OAAO,EAAE;YACX,mBAAmB,GAAG,IAAI,CAAC;AAC3B,YAAA,OAAO,mBAAmB,CAAC,SAAS,CAAC,CAAC;SACvC;QAED,OAAO,GAAG,IAAI,CAAC;QAEf,IAAM,WAAW,GAAG,0CAA0C,CAAC,OAAO,CAAC,yBAAyB,CAAC,CAAC;AAClG,QAAA,IAAI,WAAW,KAAK,IAAI,EAAE;AACxB,YAAA,qBAAqB,EAAE,CAAC;SACzB;aAAM;AACL,YAAA,kBAAkB,CAAC,WAAW,CAAC,KAAM,EAAE,IAAI,CAAC,CAAC;SAC9C;AAED,QAAA,OAAO,mBAAmB,CAAC,SAAS,CAAC,CAAC;KACvC;IAED,SAAS,gBAAgB,CAAC,MAAW,EAAA;QACnC,SAAS,GAAG,IAAI,CAAC;QACjB,OAAO,GAAG,MAAM,CAAC;QACjB,IAAI,SAAS,EAAE;YACb,IAAM,eAAe,GAAG,mBAAmB,CAAC,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC;YAChE,IAAM,YAAY,GAAG,oBAAoB,CAAC,MAAM,EAAE,eAAe,CAAC,CAAC;YACnE,oBAAoB,CAAC,YAAY,CAAC,CAAC;SACpC;AACD,QAAA,OAAO,aAAa,CAAC;KACtB;IAED,SAAS,gBAAgB,CAAC,MAAW,EAAA;QACnC,SAAS,GAAG,IAAI,CAAC;QACjB,OAAO,GAAG,MAAM,CAAC;QACjB,IAAI,SAAS,EAAE;YACb,IAAM,eAAe,GAAG,mBAAmB,CAAC,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC;YAChE,IAAM,YAAY,GAAG,oBAAoB,CAAC,MAAM,EAAE,eAAe,CAAC,CAAC;YACnE,oBAAoB,CAAC,YAAY,CAAC,CAAC;SACpC;AACD,QAAA,OAAO,aAAa,CAAC;KACtB;AAED,IAAA,SAAS,cAAc,GAAA;QACrB,OAAO;KACR;IAED,OAAO,GAAG,wBAAwB,CAAC,cAAc,EAAE,cAAc,EAAE,gBAAgB,CAAC,CAAC;IACrF,OAAO,GAAG,wBAAwB,CAAC,cAAc,EAAE,cAAc,EAAE,gBAAgB,CAAC,CAAC;IAErF,kBAAkB,CAAC,MAAM,CAAC,CAAC;AAE3B,IAAA,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;AAC5B;;ACtZM,SAAU,oBAAoB,CAAI,MAAe,EAAA;IACrD,OAAO,YAAY,CAAC,MAAM,CAAC,IAAI,OAAQ,MAAgC,CAAC,SAAS,KAAK,WAAW,CAAC;AACpG;;ACnBM,SAAU,kBAAkB,CAChC,MAA8D,EAAA;AAE9D,IAAA,IAAI,oBAAoB,CAAC,MAAM,CAAC,EAAE;AAChC,QAAA,OAAO,+BAA+B,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC;KAC5D;AACD,IAAA,OAAO,0BAA0B,CAAC,MAAM,CAAC,CAAC;AAC5C,CAAC;AAEK,SAAU,0BAA0B,CAAI,aAA6C,EAAA;AACzF,IAAA,IAAI,MAAgC,CAAC;IACrC,IAAM,cAAc,GAAG,WAAW,CAAC,aAAa,EAAE,OAAO,CAAC,CAAC;IAE3D,IAAM,cAAc,GAAG,IAAI,CAAC;AAE5B,IAAA,SAAS,aAAa,GAAA;AACpB,QAAA,IAAI,UAAU,CAAC;AACf,QAAA,IAAI;AACF,YAAA,UAAU,GAAG,YAAY,CAAC,cAAc,CAAC,CAAC;SAC3C;QAAC,OAAO,CAAC,EAAE;AACV,YAAA,OAAO,mBAAmB,CAAC,CAAC,CAAC,CAAC;SAC/B;AACD,QAAA,IAAM,WAAW,GAAG,mBAAmB,CAAC,UAAU,CAAC,CAAC;AACpD,QAAA,OAAO,oBAAoB,CAAC,WAAW,EAAE,UAAA,UAAU,EAAA;AACjD,YAAA,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,EAAE;AAC7B,gBAAA,MAAM,IAAI,SAAS,CAAC,gFAAgF,CAAC,CAAC;aACvG;AACD,YAAA,IAAM,IAAI,GAAG,gBAAgB,CAAC,UAAU,CAAC,CAAC;YAC1C,IAAI,IAAI,EAAE;AACR,gBAAA,oCAAoC,CAAC,MAAM,CAAC,yBAAyB,CAAC,CAAC;aACxE;iBAAM;AACL,gBAAA,IAAM,KAAK,GAAG,aAAa,CAAC,UAAU,CAAC,CAAC;AACxC,gBAAA,sCAAsC,CAAC,MAAM,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAC;aACjF;AACH,SAAC,CAAC,CAAC;KACJ;IAED,SAAS,eAAe,CAAC,MAAW,EAAA;AAClC,QAAA,IAAM,QAAQ,GAAG,cAAc,CAAC,QAAQ,CAAC;AACzC,QAAA,IAAI,YAAqD,CAAC;AAC1D,QAAA,IAAI;AACF,YAAA,YAAY,GAAG,SAAS,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;SAC9C;QAAC,OAAO,CAAC,EAAE;AACV,YAAA,OAAO,mBAAmB,CAAC,CAAC,CAAC,CAAC;SAC/B;AACD,QAAA,IAAI,YAAY,KAAK,SAAS,EAAE;AAC9B,YAAA,OAAO,mBAAmB,CAAC,SAAS,CAAC,CAAC;SACvC;AACD,QAAA,IAAI,YAA4D,CAAC;AACjE,QAAA,IAAI;YACF,YAAY,GAAG,WAAW,CAAC,YAAY,EAAE,QAAQ,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC;SAC9D;QAAC,OAAO,CAAC,EAAE;AACV,YAAA,OAAO,mBAAmB,CAAC,CAAC,CAAC,CAAC;SAC/B;AACD,QAAA,IAAM,aAAa,GAAG,mBAAmB,CAAC,YAAY,CAAC,CAAC;AACxD,QAAA,OAAO,oBAAoB,CAAC,aAAa,EAAE,UAAA,UAAU,EAAA;AACnD,YAAA,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,EAAE;AAC7B,gBAAA,MAAM,IAAI,SAAS,CAAC,kFAAkF,CAAC,CAAC;aACzG;AACD,YAAA,OAAO,SAAS,CAAC;AACnB,SAAC,CAAC,CAAC;KACJ;IAED,MAAM,GAAG,oBAAoB,CAAC,cAAc,EAAE,aAAa,EAAE,eAAe,EAAE,CAAC,CAAC,CAAC;AACjF,IAAA,OAAO,MAAM,CAAC;AAChB,CAAC;AAEK,SAAU,+BAA+B,CAC7C,MAA0C,EAAA;AAE1C,IAAA,IAAI,MAAgC,CAAC;IAErC,IAAM,cAAc,GAAG,IAAI,CAAC;AAE5B,IAAA,SAAS,aAAa,GAAA;AACpB,QAAA,IAAI,WAAW,CAAC;AAChB,QAAA,IAAI;AACF,YAAA,WAAW,GAAG,MAAM,CAAC,IAAI,EAAE,CAAC;SAC7B;QAAC,OAAO,CAAC,EAAE;AACV,YAAA,OAAO,mBAAmB,CAAC,CAAC,CAAC,CAAC;SAC/B;AACD,QAAA,OAAO,oBAAoB,CAAC,WAAW,EAAE,UAAA,UAAU,EAAA;AACjD,YAAA,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,EAAE;AAC7B,gBAAA,MAAM,IAAI,SAAS,CAAC,8EAA8E,CAAC,CAAC;aACrG;AACD,YAAA,IAAI,UAAU,CAAC,IAAI,EAAE;AACnB,gBAAA,oCAAoC,CAAC,MAAM,CAAC,yBAAyB,CAAC,CAAC;aACxE;iBAAM;AACL,gBAAA,IAAM,KAAK,GAAG,UAAU,CAAC,KAAK,CAAC;AAC/B,gBAAA,sCAAsC,CAAC,MAAM,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAC;aACjF;AACH,SAAC,CAAC,CAAC;KACJ;IAED,SAAS,eAAe,CAAC,MAAW,EAAA;AAClC,QAAA,IAAI;YACF,OAAO,mBAAmB,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC;SACnD;QAAC,OAAO,CAAC,EAAE;AACV,YAAA,OAAO,mBAAmB,CAAC,CAAC,CAAC,CAAC;SAC/B;KACF;IAED,MAAM,GAAG,oBAAoB,CAAC,cAAc,EAAE,aAAa,EAAE,eAAe,EAAE,CAAC,CAAC,CAAC;AACjF,IAAA,OAAO,MAAM,CAAC;AAChB;;ACvGgB,SAAA,oCAAoC,CAClD,MAAyD,EACzD,OAAe,EAAA;AAEf,IAAA,gBAAgB,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;IAClC,IAAM,QAAQ,GAAG,MAAmD,CAAC;IACrE,IAAM,qBAAqB,GAAG,QAAQ,KAAA,IAAA,IAAR,QAAQ,KAAR,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,QAAQ,CAAE,qBAAqB,CAAC;IAC9D,IAAM,MAAM,GAAG,QAAQ,KAAA,IAAA,IAAR,QAAQ,KAAR,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,QAAQ,CAAE,MAAM,CAAC;IAChC,IAAM,IAAI,GAAG,QAAQ,KAAA,IAAA,IAAR,QAAQ,KAAR,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,QAAQ,CAAE,IAAI,CAAC;IAC5B,IAAM,KAAK,GAAG,QAAQ,KAAA,IAAA,IAAR,QAAQ,KAAR,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,QAAQ,CAAE,KAAK,CAAC;IAC9B,IAAM,IAAI,GAAG,QAAQ,KAAA,IAAA,IAAR,QAAQ,KAAR,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,QAAQ,CAAE,IAAI,CAAC;IAC5B,OAAO;AACL,QAAA,qBAAqB,EAAE,qBAAqB,KAAK,SAAS;AACxD,YAAA,SAAS;AACT,YAAA,uCAAuC,CACrC,qBAAqB,EACrB,EAAG,CAAA,MAAA,CAAA,OAAO,6CAA0C,CACrD;AACH,QAAA,MAAM,EAAE,MAAM,KAAK,SAAS;AAC1B,YAAA,SAAS;YACT,qCAAqC,CAAC,MAAM,EAAE,QAAS,EAAE,EAAG,CAAA,MAAA,CAAA,OAAO,8BAA2B,CAAC;AACjG,QAAA,IAAI,EAAE,IAAI,KAAK,SAAS;AACtB,YAAA,SAAS;YACT,mCAAmC,CAAC,IAAI,EAAE,QAAS,EAAE,EAAG,CAAA,MAAA,CAAA,OAAO,4BAAyB,CAAC;AAC3F,QAAA,KAAK,EAAE,KAAK,KAAK,SAAS;AACxB,YAAA,SAAS;YACT,oCAAoC,CAAC,KAAK,EAAE,QAAS,EAAE,EAAG,CAAA,MAAA,CAAA,OAAO,6BAA0B,CAAC;AAC9F,QAAA,IAAI,EAAE,IAAI,KAAK,SAAS,GAAG,SAAS,GAAG,yBAAyB,CAAC,IAAI,EAAE,EAAG,CAAA,MAAA,CAAA,OAAO,4BAAyB,CAAC;KAC5G,CAAC;AACJ,CAAC;AAED,SAAS,qCAAqC,CAC5C,EAAkC,EAClC,QAAuC,EACvC,OAAe,EAAA;AAEf,IAAA,cAAc,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;AAC5B,IAAA,OAAO,UAAC,MAAW,EAAA,EAAK,OAAA,WAAW,CAAC,EAAE,EAAE,QAAQ,EAAE,CAAC,MAAM,CAAC,CAAC,CAAA,EAAA,CAAC;AAC9D,CAAC;AAED,SAAS,mCAAmC,CAC1C,EAAgD,EAChD,QAA0C,EAC1C,OAAe,EAAA;AAEf,IAAA,cAAc,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;AAC5B,IAAA,OAAO,UAAC,UAAuC,EAAA,EAAK,OAAA,WAAW,CAAC,EAAE,EAAE,QAAQ,EAAE,CAAC,UAAU,CAAC,CAAC,CAAA,EAAA,CAAC;AAC9F,CAAC;AAED,SAAS,oCAAoC,CAC3C,EAAiD,EACjD,QAA0C,EAC1C,OAAe,EAAA;AAEf,IAAA,cAAc,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;AAC5B,IAAA,OAAO,UAAC,UAAuC,EAAA,EAAK,OAAA,WAAW,CAAC,EAAE,EAAE,QAAQ,EAAE,CAAC,UAAU,CAAC,CAAC,CAAA,EAAA,CAAC;AAC9F,CAAC;AAED,SAAS,yBAAyB,CAAC,IAAY,EAAE,OAAe,EAAA;AAC9D,IAAA,IAAI,GAAG,EAAA,CAAA,MAAA,CAAG,IAAI,CAAE,CAAC;AACjB,IAAA,IAAI,IAAI,KAAK,OAAO,EAAE;QACpB,MAAM,IAAI,SAAS,CAAC,EAAA,CAAA,MAAA,CAAG,OAAO,EAAK,IAAA,CAAA,CAAA,MAAA,CAAA,IAAI,EAA2D,2DAAA,CAAA,CAAC,CAAC;KACrG;AACD,IAAA,OAAO,IAAI,CAAC;AACd;;ACvEgB,SAAA,sBAAsB,CAAC,OAAyD,EACzD,OAAe,EAAA;AACpD,IAAA,gBAAgB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;IACnC,IAAM,aAAa,GAAG,OAAO,KAAA,IAAA,IAAP,OAAO,KAAP,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,OAAO,CAAE,aAAa,CAAC;IAC7C,OAAO,EAAE,aAAa,EAAE,OAAO,CAAC,aAAa,CAAC,EAAE,CAAC;AACnD;;ACPgB,SAAA,kBAAkB,CAAC,OAA6C,EAC7C,OAAe,EAAA;AAChD,IAAA,gBAAgB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;IACnC,IAAM,YAAY,GAAG,OAAO,KAAA,IAAA,IAAP,OAAO,KAAP,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,OAAO,CAAE,YAAY,CAAC;IAC3C,IAAM,aAAa,GAAG,OAAO,KAAA,IAAA,IAAP,OAAO,KAAP,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,OAAO,CAAE,aAAa,CAAC;IAC7C,IAAM,YAAY,GAAG,OAAO,KAAA,IAAA,IAAP,OAAO,KAAP,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,OAAO,CAAE,YAAY,CAAC;IAC3C,IAAM,MAAM,GAAG,OAAO,KAAA,IAAA,IAAP,OAAO,KAAP,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,OAAO,CAAE,MAAM,CAAC;AAC/B,IAAA,IAAI,MAAM,KAAK,SAAS,EAAE;AACxB,QAAA,iBAAiB,CAAC,MAAM,EAAE,UAAG,OAAO,EAAA,2BAAA,CAA2B,CAAC,CAAC;KAClE;IACD,OAAO;AACL,QAAA,YAAY,EAAE,OAAO,CAAC,YAAY,CAAC;AACnC,QAAA,aAAa,EAAE,OAAO,CAAC,aAAa,CAAC;AACrC,QAAA,YAAY,EAAE,OAAO,CAAC,YAAY,CAAC;AACnC,QAAA,MAAM,EAAA,MAAA;KACP,CAAC;AACJ,CAAC;AAED,SAAS,iBAAiB,CAAC,MAAe,EAAE,OAAe,EAAA;AACzD,IAAA,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,EAAE;AAC1B,QAAA,MAAM,IAAI,SAAS,CAAC,UAAG,OAAO,EAAA,yBAAA,CAAyB,CAAC,CAAC;KAC1D;AACH;;ACpBgB,SAAA,2BAA2B,CACzC,IAAuD,EACvD,OAAe,EAAA;AAEf,IAAA,gBAAgB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;IAEhC,IAAM,QAAQ,GAAG,IAAI,KAAA,IAAA,IAAJ,IAAI,KAAJ,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,IAAI,CAAE,QAAQ,CAAC;AAChC,IAAA,mBAAmB,CAAC,QAAQ,EAAE,UAAU,EAAE,sBAAsB,CAAC,CAAC;AAClE,IAAA,oBAAoB,CAAC,QAAQ,EAAE,UAAG,OAAO,EAAA,6BAAA,CAA6B,CAAC,CAAC;IAExE,IAAM,QAAQ,GAAG,IAAI,KAAA,IAAA,IAAJ,IAAI,KAAJ,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,IAAI,CAAE,QAAQ,CAAC;AAChC,IAAA,mBAAmB,CAAC,QAAQ,EAAE,UAAU,EAAE,sBAAsB,CAAC,CAAC;AAClE,IAAA,oBAAoB,CAAC,QAAQ,EAAE,UAAG,OAAO,EAAA,6BAAA,CAA6B,CAAC,CAAC;AAExE,IAAA,OAAO,EAAE,QAAQ,EAAA,QAAA,EAAE,QAAQ,EAAA,QAAA,EAAE,CAAC;AAChC;;AC6DA;;;;AAIG;AACH,IAAA,cAAA,kBAAA,YAAA;IAcE,SAAY,cAAA,CAAA,mBAAuF,EACvF,WAAuD,EAAA;AADvD,QAAA,IAAA,mBAAA,KAAA,KAAA,CAAA,EAAA,EAAA,mBAAuF,GAAA,EAAA,CAAA,EAAA;AACvF,QAAA,IAAA,WAAA,KAAA,KAAA,CAAA,EAAA,EAAA,WAAuD,GAAA,EAAA,CAAA,EAAA;AACjE,QAAA,IAAI,mBAAmB,KAAK,SAAS,EAAE;YACrC,mBAAmB,GAAG,IAAI,CAAC;SAC5B;aAAM;AACL,YAAA,YAAY,CAAC,mBAAmB,EAAE,iBAAiB,CAAC,CAAC;SACtD;QAED,IAAM,QAAQ,GAAG,sBAAsB,CAAC,WAAW,EAAE,kBAAkB,CAAC,CAAC;QACzE,IAAM,gBAAgB,GAAG,oCAAoC,CAAC,mBAAmB,EAAE,iBAAiB,CAAC,CAAC;QAEtG,wBAAwB,CAAC,IAAI,CAAC,CAAC;AAE/B,QAAA,IAAI,gBAAgB,CAAC,IAAI,KAAK,OAAO,EAAE;AACrC,YAAA,IAAI,QAAQ,CAAC,IAAI,KAAK,SAAS,EAAE;AAC/B,gBAAA,MAAM,IAAI,UAAU,CAAC,4DAA4D,CAAC,CAAC;aACpF;YACD,IAAM,aAAa,GAAG,oBAAoB,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;AACxD,YAAA,qDAAqD,CACnD,IAAqC,EACrC,gBAAgB,EAChB,aAAa,CACd,CAAC;SACH;aAAM;AAEL,YAAA,IAAM,aAAa,GAAG,oBAAoB,CAAC,QAAQ,CAAC,CAAC;YACrD,IAAM,aAAa,GAAG,oBAAoB,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;YACxD,wDAAwD,CACtD,IAAI,EACJ,gBAAgB,EAChB,aAAa,EACb,aAAa,CACd,CAAC;SACH;KACF;AAKD,IAAA,MAAA,CAAA,cAAA,CAAI,cAAM,CAAA,SAAA,EAAA,QAAA,EAAA;AAHV;;AAEG;AACH,QAAA,GAAA,EAAA,YAAA;AACE,YAAA,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,EAAE;AAC3B,gBAAA,MAAMC,2BAAyB,CAAC,QAAQ,CAAC,CAAC;aAC3C;AAED,YAAA,OAAO,sBAAsB,CAAC,IAAI,CAAC,CAAC;SACrC;;;AAAA,KAAA,CAAA,CAAA;AAED;;;;;AAKG;IACH,cAAM,CAAA,SAAA,CAAA,MAAA,GAAN,UAAO,MAAuB,EAAA;AAAvB,QAAA,IAAA,MAAA,KAAA,KAAA,CAAA,EAAA,EAAA,MAAuB,GAAA,SAAA,CAAA,EAAA;AAC5B,QAAA,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,EAAE;AAC3B,YAAA,OAAO,mBAAmB,CAACA,2BAAyB,CAAC,QAAQ,CAAC,CAAC,CAAC;SACjE;AAED,QAAA,IAAI,sBAAsB,CAAC,IAAI,CAAC,EAAE;YAChC,OAAO,mBAAmB,CAAC,IAAI,SAAS,CAAC,kDAAkD,CAAC,CAAC,CAAC;SAC/F;AAED,QAAA,OAAO,oBAAoB,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;KAC3C,CAAA;IAqBD,cAAS,CAAA,SAAA,CAAA,SAAA,GAAT,UACE,UAAyE,EAAA;AAAzE,QAAA,IAAA,UAAA,KAAA,KAAA,CAAA,EAAA,EAAA,UAAyE,GAAA,SAAA,CAAA,EAAA;AAEzE,QAAA,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,EAAE;AAC3B,YAAA,MAAMA,2BAAyB,CAAC,WAAW,CAAC,CAAC;SAC9C;QAED,IAAM,OAAO,GAAG,oBAAoB,CAAC,UAAU,EAAE,iBAAiB,CAAC,CAAC;AAEpE,QAAA,IAAI,OAAO,CAAC,IAAI,KAAK,SAAS,EAAE;AAC9B,YAAA,OAAO,kCAAkC,CAAC,IAAI,CAAC,CAAC;SAGlB;AAChC,QAAA,OAAO,+BAA+B,CAAC,IAAqC,CAAC,CAAC;KAC/E,CAAA;AAaD,IAAA,cAAA,CAAA,SAAA,CAAA,WAAW,GAAX,UACE,YAA8E,EAC9E,UAAqD,EAAA;AAArD,QAAA,IAAA,UAAA,KAAA,KAAA,CAAA,EAAA,EAAA,UAAqD,GAAA,EAAA,CAAA,EAAA;AAErD,QAAA,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,EAAE;AAC3B,YAAA,MAAMA,2BAAyB,CAAC,aAAa,CAAC,CAAC;SAChD;AACD,QAAA,sBAAsB,CAAC,YAAY,EAAE,CAAC,EAAE,aAAa,CAAC,CAAC;QAEvD,IAAM,SAAS,GAAG,2BAA2B,CAAC,YAAY,EAAE,iBAAiB,CAAC,CAAC;QAC/E,IAAM,OAAO,GAAG,kBAAkB,CAAC,UAAU,EAAE,kBAAkB,CAAC,CAAC;AAEnE,QAAA,IAAI,sBAAsB,CAAC,IAAI,CAAC,EAAE;AAChC,YAAA,MAAM,IAAI,SAAS,CAAC,gFAAgF,CAAC,CAAC;SACvG;AACD,QAAA,IAAI,sBAAsB,CAAC,SAAS,CAAC,QAAQ,CAAC,EAAE;AAC9C,YAAA,MAAM,IAAI,SAAS,CAAC,gFAAgF,CAAC,CAAC;SACvG;QAED,IAAM,OAAO,GAAG,oBAAoB,CAClC,IAAI,EAAE,SAAS,CAAC,QAAQ,EAAE,OAAO,CAAC,YAAY,EAAE,OAAO,CAAC,YAAY,EAAE,OAAO,CAAC,aAAa,EAAE,OAAO,CAAC,MAAM,CAC5G,CAAC;QAEF,yBAAyB,CAAC,OAAO,CAAC,CAAC;QAEnC,OAAO,SAAS,CAAC,QAAQ,CAAC;KAC3B,CAAA;AAUD,IAAA,cAAA,CAAA,SAAA,CAAA,MAAM,GAAN,UAAO,WAAiD,EACjD,UAAqD,EAAA;AAArD,QAAA,IAAA,UAAA,KAAA,KAAA,CAAA,EAAA,EAAA,UAAqD,GAAA,EAAA,CAAA,EAAA;AAC1D,QAAA,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,EAAE;AAC3B,YAAA,OAAO,mBAAmB,CAACA,2BAAyB,CAAC,QAAQ,CAAC,CAAC,CAAC;SACjE;AAED,QAAA,IAAI,WAAW,KAAK,SAAS,EAAE;AAC7B,YAAA,OAAO,mBAAmB,CAAC,sCAAsC,CAAC,CAAC;SACpE;AACD,QAAA,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,EAAE;YAClC,OAAO,mBAAmB,CACxB,IAAI,SAAS,CAAC,2EAA2E,CAAC,CAC3F,CAAC;SACH;AAED,QAAA,IAAI,OAAmC,CAAC;AACxC,QAAA,IAAI;AACF,YAAA,OAAO,GAAG,kBAAkB,CAAC,UAAU,EAAE,kBAAkB,CAAC,CAAC;SAC9D;QAAC,OAAO,CAAC,EAAE;AACV,YAAA,OAAO,mBAAmB,CAAC,CAAC,CAAC,CAAC;SAC/B;AAED,QAAA,IAAI,sBAAsB,CAAC,IAAI,CAAC,EAAE;YAChC,OAAO,mBAAmB,CACxB,IAAI,SAAS,CAAC,2EAA2E,CAAC,CAC3F,CAAC;SACH;AACD,QAAA,IAAI,sBAAsB,CAAC,WAAW,CAAC,EAAE;YACvC,OAAO,mBAAmB,CACxB,IAAI,SAAS,CAAC,2EAA2E,CAAC,CAC3F,CAAC;SACH;QAED,OAAO,oBAAoB,CACzB,IAAI,EAAE,WAAW,EAAE,OAAO,CAAC,YAAY,EAAE,OAAO,CAAC,YAAY,EAAE,OAAO,CAAC,aAAa,EAAE,OAAO,CAAC,MAAM,CACrG,CAAC;KACH,CAAA;AAED;;;;;;;;;;AAUG;AACH,IAAA,cAAA,CAAA,SAAA,CAAA,GAAG,GAAH,YAAA;AACE,QAAA,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,EAAE;AAC3B,YAAA,MAAMA,2BAAyB,CAAC,KAAK,CAAC,CAAC;SACxC;QAED,IAAM,QAAQ,GAAG,iBAAiB,CAAC,IAAW,CAAC,CAAC;AAChD,QAAA,OAAO,mBAAmB,CAAC,QAAQ,CAAC,CAAC;KACtC,CAAA;IAcD,cAAM,CAAA,SAAA,CAAA,MAAA,GAAN,UAAO,UAAwE,EAAA;AAAxE,QAAA,IAAA,UAAA,KAAA,KAAA,CAAA,EAAA,EAAA,UAAwE,GAAA,SAAA,CAAA,EAAA;AAC7E,QAAA,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,EAAE;AAC3B,YAAA,MAAMA,2BAAyB,CAAC,QAAQ,CAAC,CAAC;SAC3C;QAED,IAAM,OAAO,GAAG,sBAAsB,CAAC,UAAU,EAAE,iBAAiB,CAAC,CAAC;QACtE,OAAO,kCAAkC,CAAI,IAAI,EAAE,OAAO,CAAC,aAAa,CAAC,CAAC;KAC3E,CAAA;AAOD,IAAA,cAAA,CAAA,SAAA,CAAC,mBAAmB,CAAC,GAArB,UAAsB,OAAuC,EAAA;;AAE3D,QAAA,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;KAC7B,CAAA;AAED;;;;;AAKG;IACI,cAAI,CAAA,IAAA,GAAX,UAAe,aAAqE,EAAA;AAClF,QAAA,OAAO,kBAAkB,CAAC,aAAa,CAAC,CAAC;KAC1C,CAAA;IACH,OAAC,cAAA,CAAA;AAAD,CAAC,EAAA,EAAA;AAED,MAAM,CAAC,gBAAgB,CAAC,cAAc,EAAE;AACtC,IAAA,IAAI,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE;AAC3B,CAAA,CAAC,CAAC;AACH,MAAM,CAAC,gBAAgB,CAAC,cAAc,CAAC,SAAS,EAAE;AAChD,IAAA,MAAM,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE;AAC5B,IAAA,SAAS,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE;AAC/B,IAAA,WAAW,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE;AACjC,IAAA,MAAM,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE;AAC5B,IAAA,GAAG,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE;AACzB,IAAA,MAAM,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE;AAC5B,IAAA,MAAM,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE;AAC7B,CAAA,CAAC,CAAC;AACH,eAAe,CAAC,cAAc,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;AAC7C,eAAe,CAAC,cAAc,CAAC,SAAS,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;AAC3D,eAAe,CAAC,cAAc,CAAC,SAAS,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC;AACjE,eAAe,CAAC,cAAc,CAAC,SAAS,CAAC,WAAW,EAAE,aAAa,CAAC,CAAC;AACrE,eAAe,CAAC,cAAc,CAAC,SAAS,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;AAC3D,eAAe,CAAC,cAAc,CAAC,SAAS,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;AACrD,eAAe,CAAC,cAAc,CAAC,SAAS,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;AAC3D,IAAI,OAAOH,cAAM,CAAC,WAAW,KAAK,QAAQ,EAAE;IAC1C,MAAM,CAAC,cAAc,CAAC,cAAc,CAAC,SAAS,EAAEA,cAAM,CAAC,WAAW,EAAE;AAClE,QAAA,KAAK,EAAE,gBAAgB;AACvB,QAAA,YAAY,EAAE,IAAI;AACnB,KAAA,CAAC,CAAC;AACL,CAAC;AACD,MAAM,CAAC,cAAc,CAAC,cAAc,CAAC,SAAS,EAAE,mBAAmB,EAAE;AACnE,IAAA,KAAK,EAAE,cAAc,CAAC,SAAS,CAAC,MAAM;AACtC,IAAA,QAAQ,EAAE,IAAI;AACd,IAAA,YAAY,EAAE,IAAI;AACnB,CAAA,CAAC,CAAC;AAqBH;AAEA;AACM,SAAU,oBAAoB,CAClC,cAA8C,EAC9C,aAAkC,EAClC,eAA+C,EAC/C,aAAiB,EACjB,aAAuD,EAAA;AADvD,IAAA,IAAA,aAAA,KAAA,KAAA,CAAA,EAAA,EAAA,aAAiB,GAAA,CAAA,CAAA,EAAA;AACjB,IAAA,IAAA,aAAA,KAAA,KAAA,CAAA,EAAA,EAAA,aAAA,GAAA,YAAA,EAAsD,OAAA,CAAC,GAAA,CAAA,EAEZ;IAE3C,IAAM,MAAM,GAA6B,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;IACjF,wBAAwB,CAAC,MAAM,CAAC,CAAC;IAEjC,IAAM,UAAU,GAAuC,MAAM,CAAC,MAAM,CAAC,+BAA+B,CAAC,SAAS,CAAC,CAAC;AAChH,IAAA,oCAAoC,CAClC,MAAM,EAAE,UAAU,EAAE,cAAc,EAAE,aAAa,EAAE,eAAe,EAAE,aAAa,EAAE,aAAa,CACjG,CAAC;AAEF,IAAA,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;SACgB,wBAAwB,CACtC,cAA8C,EAC9C,aAAkC,EAClC,eAA+C,EAAA;IAE/C,IAAM,MAAM,GAAuB,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;IAC3E,wBAAwB,CAAC,MAAM,CAAC,CAAC;IAEjC,IAAM,UAAU,GAAiC,MAAM,CAAC,MAAM,CAAC,4BAA4B,CAAC,SAAS,CAAC,CAAC;AACvG,IAAA,iCAAiC,CAAC,MAAM,EAAE,UAAU,EAAE,cAAc,EAAE,aAAa,EAAE,eAAe,EAAE,CAAC,EAAE,SAAS,CAAC,CAAC;AAEpH,IAAA,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,SAAS,wBAAwB,CAAC,MAAsB,EAAA;AACtD,IAAA,MAAM,CAAC,MAAM,GAAG,UAAU,CAAC;AAC3B,IAAA,MAAM,CAAC,OAAO,GAAG,SAAS,CAAC;AAC3B,IAAA,MAAM,CAAC,YAAY,GAAG,SAAS,CAAC;AAChC,IAAA,MAAM,CAAC,UAAU,GAAG,KAAK,CAAC;AAC5B,CAAC;AAEK,SAAU,gBAAgB,CAAC,CAAU,EAAA;AACzC,IAAA,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE;AACpB,QAAA,OAAO,KAAK,CAAC;KACd;AAED,IAAA,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,2BAA2B,CAAC,EAAE;AACzE,QAAA,OAAO,KAAK,CAAC;KACd;IAED,OAAO,CAAC,YAAY,cAAc,CAAC;AACrC,CAAC;AAQK,SAAU,sBAAsB,CAAC,MAAsB,EAAA;AAG3D,IAAA,IAAI,MAAM,CAAC,OAAO,KAAK,SAAS,EAAE;AAChC,QAAA,OAAO,KAAK,CAAC;KACd;AAED,IAAA,OAAO,IAAI,CAAC;AACd,CAAC;AAED;AAEgB,SAAA,oBAAoB,CAAI,MAAyB,EAAE,MAAW,EAAA;AAC5E,IAAA,MAAM,CAAC,UAAU,GAAG,IAAI,CAAC;AAEzB,IAAA,IAAI,MAAM,CAAC,MAAM,KAAK,QAAQ,EAAE;AAC9B,QAAA,OAAO,mBAAmB,CAAC,SAAS,CAAC,CAAC;KACvC;AACD,IAAA,IAAI,MAAM,CAAC,MAAM,KAAK,SAAS,EAAE;AAC/B,QAAA,OAAO,mBAAmB,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;KACjD;IAED,mBAAmB,CAAC,MAAM,CAAC,CAAC;AAE5B,IAAA,IAAM,MAAM,GAAG,MAAM,CAAC,OAAO,CAAC;IAC9B,IAAI,MAAM,KAAK,SAAS,IAAI,0BAA0B,CAAC,MAAM,CAAC,EAAE;AAC9D,QAAA,IAAM,gBAAgB,GAAG,MAAM,CAAC,iBAAiB,CAAC;AAClD,QAAA,MAAM,CAAC,iBAAiB,GAAG,IAAI,WAAW,EAAE,CAAC;AAC7C,QAAA,gBAAgB,CAAC,OAAO,CAAC,UAAA,eAAe,EAAA;AACtC,YAAA,eAAe,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;AACzC,SAAC,CAAC,CAAC;KACJ;IAED,IAAM,mBAAmB,GAAG,MAAM,CAAC,yBAAyB,CAAC,WAAW,CAAC,CAAC,MAAM,CAAC,CAAC;AAClF,IAAA,OAAO,oBAAoB,CAAC,mBAAmB,EAAE,IAAI,CAAC,CAAC;AACzD,CAAC;AAEK,SAAU,mBAAmB,CAAI,MAAyB,EAAA;AAG9D,IAAA,MAAM,CAAC,MAAM,GAAG,QAAQ,CAAC;AAEzB,IAAA,IAAM,MAAM,GAAG,MAAM,CAAC,OAAO,CAAC;AAE9B,IAAA,IAAI,MAAM,KAAK,SAAS,EAAE;QACxB,OAAO;KACR;IAED,iCAAiC,CAAC,MAAM,CAAC,CAAC;AAE1C,IAAA,IAAI,6BAA6B,CAAI,MAAM,CAAC,EAAE;AAC5C,QAAA,IAAM,YAAY,GAAG,MAAM,CAAC,aAAa,CAAC;AAC1C,QAAA,MAAM,CAAC,aAAa,GAAG,IAAI,WAAW,EAAE,CAAC;AACzC,QAAA,YAAY,CAAC,OAAO,CAAC,UAAA,WAAW,EAAA;YAC9B,WAAW,CAAC,WAAW,EAAE,CAAC;AAC5B,SAAC,CAAC,CAAC;KACJ;AACH,CAAC;AAEe,SAAA,mBAAmB,CAAI,MAAyB,EAAE,CAAM,EAAA;AAItE,IAAA,MAAM,CAAC,MAAM,GAAG,SAAS,CAAC;AAC1B,IAAA,MAAM,CAAC,YAAY,GAAG,CAAC,CAAC;AAExB,IAAA,IAAM,MAAM,GAAG,MAAM,CAAC,OAAO,CAAC;AAE9B,IAAA,IAAI,MAAM,KAAK,SAAS,EAAE;QACxB,OAAO;KACR;AAED,IAAA,gCAAgC,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;AAE5C,IAAA,IAAI,6BAA6B,CAAI,MAAM,CAAC,EAAE;AAC5C,QAAA,4CAA4C,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;KACzD;SAAM;AAEL,QAAA,6CAA6C,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;KAC1D;AACH,CAAC;AAmBD;AAEA,SAASG,2BAAyB,CAAC,IAAY,EAAA;AAC7C,IAAA,OAAO,IAAI,SAAS,CAAC,mCAA4B,IAAI,EAAA,uCAAA,CAAuC,CAAC,CAAC;AAChG;;ACljBgB,SAAA,0BAA0B,CAAC,IAA4C,EAC5C,OAAe,EAAA;AACxD,IAAA,gBAAgB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;IAChC,IAAM,aAAa,GAAG,IAAI,KAAA,IAAA,IAAJ,IAAI,KAAJ,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,IAAI,CAAE,aAAa,CAAC;AAC1C,IAAA,mBAAmB,CAAC,aAAa,EAAE,eAAe,EAAE,qBAAqB,CAAC,CAAC;IAC3E,OAAO;AACL,QAAA,aAAa,EAAE,yBAAyB,CAAC,aAAa,CAAC;KACxD,CAAC;AACJ;;ACNA;AACA,IAAM,sBAAsB,GAAG,UAAC,KAAsB,EAAA;IACpD,OAAO,KAAK,CAAC,UAAU,CAAC;AAC1B,CAAC,CAAC;AACF,eAAe,CAAC,sBAAsB,EAAE,MAAM,CAAC,CAAC;AAEhD;;;;AAIG;AACH,IAAA,yBAAA,kBAAA,YAAA;AAIE,IAAA,SAAA,yBAAA,CAAY,OAA4B,EAAA;AACtC,QAAA,sBAAsB,CAAC,OAAO,EAAE,CAAC,EAAE,2BAA2B,CAAC,CAAC;AAChE,QAAA,OAAO,GAAG,0BAA0B,CAAC,OAAO,EAAE,iBAAiB,CAAC,CAAC;AACjE,QAAA,IAAI,CAAC,uCAAuC,GAAG,OAAO,CAAC,aAAa,CAAC;KACtE;AAKD,IAAA,MAAA,CAAA,cAAA,CAAI,yBAAa,CAAA,SAAA,EAAA,eAAA,EAAA;AAHjB;;AAEG;AACH,QAAA,GAAA,EAAA,YAAA;AACE,YAAA,IAAI,CAAC,2BAA2B,CAAC,IAAI,CAAC,EAAE;AACtC,gBAAA,MAAM,6BAA6B,CAAC,eAAe,CAAC,CAAC;aACtD;YACD,OAAO,IAAI,CAAC,uCAAuC,CAAC;SACrD;;;AAAA,KAAA,CAAA,CAAA;AAKD,IAAA,MAAA,CAAA,cAAA,CAAI,yBAAI,CAAA,SAAA,EAAA,MAAA,EAAA;AAHR;;AAEG;AACH,QAAA,GAAA,EAAA,YAAA;AACE,YAAA,IAAI,CAAC,2BAA2B,CAAC,IAAI,CAAC,EAAE;AACtC,gBAAA,MAAM,6BAA6B,CAAC,MAAM,CAAC,CAAC;aAC7C;AACD,YAAA,OAAO,sBAAsB,CAAC;SAC/B;;;AAAA,KAAA,CAAA,CAAA;IACH,OAAC,yBAAA,CAAA;AAAD,CAAC,EAAA,EAAA;AAED,MAAM,CAAC,gBAAgB,CAAC,yBAAyB,CAAC,SAAS,EAAE;AAC3D,IAAA,aAAa,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE;AACnC,IAAA,IAAI,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE;AAC3B,CAAA,CAAC,CAAC;AACH,IAAI,OAAOH,cAAM,CAAC,WAAW,KAAK,QAAQ,EAAE;IAC1C,MAAM,CAAC,cAAc,CAAC,yBAAyB,CAAC,SAAS,EAAEA,cAAM,CAAC,WAAW,EAAE;AAC7E,QAAA,KAAK,EAAE,2BAA2B;AAClC,QAAA,YAAY,EAAE,IAAI;AACnB,KAAA,CAAC,CAAC;AACL,CAAC;AAED;AAEA,SAAS,6BAA6B,CAAC,IAAY,EAAA;AACjD,IAAA,OAAO,IAAI,SAAS,CAAC,8CAAuC,IAAI,EAAA,kDAAA,CAAkD,CAAC,CAAC;AACtH,CAAC;AAEK,SAAU,2BAA2B,CAAC,CAAM,EAAA;AAChD,IAAA,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE;AACpB,QAAA,OAAO,KAAK,CAAC;KACd;AAED,IAAA,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,yCAAyC,CAAC,EAAE;AACvF,QAAA,OAAO,KAAK,CAAC;KACd;IAED,OAAO,CAAC,YAAY,yBAAyB,CAAC;AAChD;;ACrEA;AACA,IAAM,iBAAiB,GAAG,YAAA;AACxB,IAAA,OAAO,CAAC,CAAC;AACX,CAAC,CAAC;AACF,eAAe,CAAC,iBAAiB,EAAE,MAAM,CAAC,CAAC;AAE3C;;;;AAIG;AACH,IAAA,oBAAA,kBAAA,YAAA;AAIE,IAAA,SAAA,oBAAA,CAAY,OAA4B,EAAA;AACtC,QAAA,sBAAsB,CAAC,OAAO,EAAE,CAAC,EAAE,sBAAsB,CAAC,CAAC;AAC3D,QAAA,OAAO,GAAG,0BAA0B,CAAC,OAAO,EAAE,iBAAiB,CAAC,CAAC;AACjE,QAAA,IAAI,CAAC,kCAAkC,GAAG,OAAO,CAAC,aAAa,CAAC;KACjE;AAKD,IAAA,MAAA,CAAA,cAAA,CAAI,oBAAa,CAAA,SAAA,EAAA,eAAA,EAAA;AAHjB;;AAEG;AACH,QAAA,GAAA,EAAA,YAAA;AACE,YAAA,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,EAAE;AACjC,gBAAA,MAAM,wBAAwB,CAAC,eAAe,CAAC,CAAC;aACjD;YACD,OAAO,IAAI,CAAC,kCAAkC,CAAC;SAChD;;;AAAA,KAAA,CAAA,CAAA;AAMD,IAAA,MAAA,CAAA,cAAA,CAAI,oBAAI,CAAA,SAAA,EAAA,MAAA,EAAA;AAJR;;;AAGG;AACH,QAAA,GAAA,EAAA,YAAA;AACE,YAAA,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,EAAE;AACjC,gBAAA,MAAM,wBAAwB,CAAC,MAAM,CAAC,CAAC;aACxC;AACD,YAAA,OAAO,iBAAiB,CAAC;SAC1B;;;AAAA,KAAA,CAAA,CAAA;IACH,OAAC,oBAAA,CAAA;AAAD,CAAC,EAAA,EAAA;AAED,MAAM,CAAC,gBAAgB,CAAC,oBAAoB,CAAC,SAAS,EAAE;AACtD,IAAA,aAAa,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE;AACnC,IAAA,IAAI,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE;AAC3B,CAAA,CAAC,CAAC;AACH,IAAI,OAAOA,cAAM,CAAC,WAAW,KAAK,QAAQ,EAAE;IAC1C,MAAM,CAAC,cAAc,CAAC,oBAAoB,CAAC,SAAS,EAAEA,cAAM,CAAC,WAAW,EAAE;AACxE,QAAA,KAAK,EAAE,sBAAsB;AAC7B,QAAA,YAAY,EAAE,IAAI;AACnB,KAAA,CAAC,CAAC;AACL,CAAC;AAED;AAEA,SAAS,wBAAwB,CAAC,IAAY,EAAA;AAC5C,IAAA,OAAO,IAAI,SAAS,CAAC,yCAAkC,IAAI,EAAA,6CAAA,CAA6C,CAAC,CAAC;AAC5G,CAAC;AAEK,SAAU,sBAAsB,CAAC,CAAM,EAAA;AAC3C,IAAA,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE;AACpB,QAAA,OAAO,KAAK,CAAC;KACd;AAED,IAAA,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,oCAAoC,CAAC,EAAE;AAClF,QAAA,OAAO,KAAK,CAAC;KACd;IAED,OAAO,CAAC,YAAY,oBAAoB,CAAC;AAC3C;;AC/DgB,SAAA,kBAAkB,CAAO,QAAkC,EAClC,OAAe,EAAA;AACtD,IAAA,gBAAgB,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;IACpC,IAAM,MAAM,GAAG,QAAQ,KAAA,IAAA,IAAR,QAAQ,KAAR,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,QAAQ,CAAE,MAAM,CAAC;IAChC,IAAM,KAAK,GAAG,QAAQ,KAAA,IAAA,IAAR,QAAQ,KAAR,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,QAAQ,CAAE,KAAK,CAAC;IAC9B,IAAM,YAAY,GAAG,QAAQ,KAAA,IAAA,IAAR,QAAQ,KAAR,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,QAAQ,CAAE,YAAY,CAAC;IAC5C,IAAM,KAAK,GAAG,QAAQ,KAAA,IAAA,IAAR,QAAQ,KAAR,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,QAAQ,CAAE,KAAK,CAAC;IAC9B,IAAM,SAAS,GAAG,QAAQ,KAAA,IAAA,IAAR,QAAQ,KAAR,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,QAAQ,CAAE,SAAS,CAAC;IACtC,IAAM,YAAY,GAAG,QAAQ,KAAA,IAAA,IAAR,QAAQ,KAAR,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,QAAQ,CAAE,YAAY,CAAC;IAC5C,OAAO;AACL,QAAA,MAAM,EAAE,MAAM,KAAK,SAAS;AAC1B,YAAA,SAAS;YACT,gCAAgC,CAAC,MAAM,EAAE,QAAS,EAAE,EAAG,CAAA,MAAA,CAAA,OAAO,8BAA2B,CAAC;AAC5F,QAAA,KAAK,EAAE,KAAK,KAAK,SAAS;AACxB,YAAA,SAAS;YACT,+BAA+B,CAAC,KAAK,EAAE,QAAS,EAAE,EAAG,CAAA,MAAA,CAAA,OAAO,6BAA0B,CAAC;AACzF,QAAA,YAAY,EAAA,YAAA;AACZ,QAAA,KAAK,EAAE,KAAK,KAAK,SAAS;AACxB,YAAA,SAAS;YACT,+BAA+B,CAAC,KAAK,EAAE,QAAS,EAAE,EAAG,CAAA,MAAA,CAAA,OAAO,6BAA0B,CAAC;AACzF,QAAA,SAAS,EAAE,SAAS,KAAK,SAAS;AAChC,YAAA,SAAS;YACT,mCAAmC,CAAC,SAAS,EAAE,QAAS,EAAE,EAAG,CAAA,MAAA,CAAA,OAAO,iCAA8B,CAAC;AACrG,QAAA,YAAY,EAAA,YAAA;KACb,CAAC;AACJ,CAAC;AAED,SAAS,+BAA+B,CACtC,EAA+B,EAC/B,QAA2B,EAC3B,OAAe,EAAA;AAEf,IAAA,cAAc,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;AAC5B,IAAA,OAAO,UAAC,UAA+C,EAAA,EAAK,OAAA,WAAW,CAAC,EAAE,EAAE,QAAQ,EAAE,CAAC,UAAU,CAAC,CAAC,CAAA,EAAA,CAAC;AACtG,CAAC;AAED,SAAS,+BAA+B,CACtC,EAA+B,EAC/B,QAA2B,EAC3B,OAAe,EAAA;AAEf,IAAA,cAAc,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;AAC5B,IAAA,OAAO,UAAC,UAA+C,EAAA,EAAK,OAAA,WAAW,CAAC,EAAE,EAAE,QAAQ,EAAE,CAAC,UAAU,CAAC,CAAC,CAAA,EAAA,CAAC;AACtG,CAAC;AAED,SAAS,mCAAmC,CAC1C,EAAsC,EACtC,QAA2B,EAC3B,OAAe,EAAA;AAEf,IAAA,cAAc,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;IAC5B,OAAO,UAAC,KAAQ,EAAE,UAA+C,IAAK,OAAA,WAAW,CAAC,EAAE,EAAE,QAAQ,EAAE,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC,CAAA,EAAA,CAAC;AACvH,CAAC;AAED,SAAS,gCAAgC,CACvC,EAA6B,EAC7B,QAA2B,EAC3B,OAAe,EAAA;AAEf,IAAA,cAAc,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;AAC5B,IAAA,OAAO,UAAC,MAAW,EAAA,EAAK,OAAA,WAAW,CAAC,EAAE,EAAE,QAAQ,EAAE,CAAC,MAAM,CAAC,CAAC,CAAA,EAAA,CAAC;AAC9D;;ACvCA;AAEA;;;;;;;AAOG;AACH,IAAA,eAAA,kBAAA,YAAA;AAmBE,IAAA,SAAA,eAAA,CAAY,cAAyD,EACzD,mBAA+D,EAC/D,mBAA+D,EAAA;AAF/D,QAAA,IAAA,cAAA,KAAA,KAAA,CAAA,EAAA,EAAA,cAAyD,GAAA,EAAA,CAAA,EAAA;AACzD,QAAA,IAAA,mBAAA,KAAA,KAAA,CAAA,EAAA,EAAA,mBAA+D,GAAA,EAAA,CAAA,EAAA;AAC/D,QAAA,IAAA,mBAAA,KAAA,KAAA,CAAA,EAAA,EAAA,mBAA+D,GAAA,EAAA,CAAA,EAAA;AACzE,QAAA,IAAI,cAAc,KAAK,SAAS,EAAE;YAChC,cAAc,GAAG,IAAI,CAAC;SACvB;QAED,IAAM,gBAAgB,GAAG,sBAAsB,CAAC,mBAAmB,EAAE,kBAAkB,CAAC,CAAC;QACzF,IAAM,gBAAgB,GAAG,sBAAsB,CAAC,mBAAmB,EAAE,iBAAiB,CAAC,CAAC;QAExF,IAAM,WAAW,GAAG,kBAAkB,CAAC,cAAc,EAAE,iBAAiB,CAAC,CAAC;AAC1E,QAAA,IAAI,WAAW,CAAC,YAAY,KAAK,SAAS,EAAE;AAC1C,YAAA,MAAM,IAAI,UAAU,CAAC,gCAAgC,CAAC,CAAC;SACxD;AACD,QAAA,IAAI,WAAW,CAAC,YAAY,KAAK,SAAS,EAAE;AAC1C,YAAA,MAAM,IAAI,UAAU,CAAC,gCAAgC,CAAC,CAAC;SACxD;QAED,IAAM,qBAAqB,GAAG,oBAAoB,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC;AACxE,QAAA,IAAM,qBAAqB,GAAG,oBAAoB,CAAC,gBAAgB,CAAC,CAAC;QACrE,IAAM,qBAAqB,GAAG,oBAAoB,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC;AACxE,QAAA,IAAM,qBAAqB,GAAG,oBAAoB,CAAC,gBAAgB,CAAC,CAAC;AAErE,QAAA,IAAI,oBAAgE,CAAC;AACrE,QAAA,IAAM,YAAY,GAAG,UAAU,CAAO,UAAA,OAAO,EAAA;YAC3C,oBAAoB,GAAG,OAAO,CAAC;AACjC,SAAC,CAAC,CAAC;AAEH,QAAA,yBAAyB,CACvB,IAAI,EAAE,YAAY,EAAE,qBAAqB,EAAE,qBAAqB,EAAE,qBAAqB,EAAE,qBAAqB,CAC/G,CAAC;AACF,QAAA,oDAAoD,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;AAExE,QAAA,IAAI,WAAW,CAAC,KAAK,KAAK,SAAS,EAAE;YACnC,oBAAoB,CAAC,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC,CAAC;SAC1E;aAAM;YACL,oBAAoB,CAAC,SAAS,CAAC,CAAC;SACjC;KACF;AAKD,IAAA,MAAA,CAAA,cAAA,CAAI,eAAQ,CAAA,SAAA,EAAA,UAAA,EAAA;AAHZ;;AAEG;AACH,QAAA,GAAA,EAAA,YAAA;AACE,YAAA,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,EAAE;AAC5B,gBAAA,MAAM,yBAAyB,CAAC,UAAU,CAAC,CAAC;aAC7C;YAED,OAAO,IAAI,CAAC,SAAS,CAAC;SACvB;;;AAAA,KAAA,CAAA,CAAA;AAKD,IAAA,MAAA,CAAA,cAAA,CAAI,eAAQ,CAAA,SAAA,EAAA,UAAA,EAAA;AAHZ;;AAEG;AACH,QAAA,GAAA,EAAA,YAAA;AACE,YAAA,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,EAAE;AAC5B,gBAAA,MAAM,yBAAyB,CAAC,UAAU,CAAC,CAAC;aAC7C;YAED,OAAO,IAAI,CAAC,SAAS,CAAC;SACvB;;;AAAA,KAAA,CAAA,CAAA;IACH,OAAC,eAAA,CAAA;AAAD,CAAC,EAAA,EAAA;AAED,MAAM,CAAC,gBAAgB,CAAC,eAAe,CAAC,SAAS,EAAE;AACjD,IAAA,QAAQ,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE;AAC9B,IAAA,QAAQ,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE;AAC/B,CAAA,CAAC,CAAC;AACH,IAAI,OAAOA,cAAM,CAAC,WAAW,KAAK,QAAQ,EAAE;IAC1C,MAAM,CAAC,cAAc,CAAC,eAAe,CAAC,SAAS,EAAEA,cAAM,CAAC,WAAW,EAAE;AACnE,QAAA,KAAK,EAAE,iBAAiB;AACxB,QAAA,YAAY,EAAE,IAAI;AACnB,KAAA,CAAC,CAAC;AACL,CAAC;AA0CD,SAAS,yBAAyB,CAAO,MAA6B,EAC7B,YAA2B,EAC3B,qBAA6B,EAC7B,qBAAqD,EACrD,qBAA6B,EAC7B,qBAAqD,EAAA;AAC5F,IAAA,SAAS,cAAc,GAAA;AACrB,QAAA,OAAO,YAAY,CAAC;KACrB;IAED,SAAS,cAAc,CAAC,KAAQ,EAAA;AAC9B,QAAA,OAAO,wCAAwC,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;KAChE;IAED,SAAS,cAAc,CAAC,MAAW,EAAA;AACjC,QAAA,OAAO,wCAAwC,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;KACjE;AAED,IAAA,SAAS,cAAc,GAAA;AACrB,QAAA,OAAO,wCAAwC,CAAC,MAAM,CAAC,CAAC;KACzD;AAED,IAAA,MAAM,CAAC,SAAS,GAAG,oBAAoB,CAAC,cAAc,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc,EAC9D,qBAAqB,EAAE,qBAAqB,CAAC,CAAC;AAEtF,IAAA,SAAS,aAAa,GAAA;AACpB,QAAA,OAAO,yCAAyC,CAAC,MAAM,CAAC,CAAC;KAC1D;IAED,SAAS,eAAe,CAAC,MAAW,EAAA;AAClC,QAAA,OAAO,2CAA2C,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;KACpE;AAED,IAAA,MAAM,CAAC,SAAS,GAAG,oBAAoB,CAAC,cAAc,EAAE,aAAa,EAAE,eAAe,EAAE,qBAAqB,EACrE,qBAAqB,CAAC,CAAC;;AAG/D,IAAA,MAAM,CAAC,aAAa,GAAG,SAAU,CAAC;AAClC,IAAA,MAAM,CAAC,0BAA0B,GAAG,SAAU,CAAC;AAC/C,IAAA,MAAM,CAAC,kCAAkC,GAAG,SAAU,CAAC;AACvD,IAAA,8BAA8B,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;AAE7C,IAAA,MAAM,CAAC,0BAA0B,GAAG,SAAU,CAAC;AACjD,CAAC;AAED,SAAS,iBAAiB,CAAC,CAAU,EAAA;AACnC,IAAA,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE;AACpB,QAAA,OAAO,KAAK,CAAC;KACd;AAED,IAAA,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,4BAA4B,CAAC,EAAE;AAC1E,QAAA,OAAO,KAAK,CAAC;KACd;IAED,OAAO,CAAC,YAAY,eAAe,CAAC;AACtC,CAAC;AAED;AACA,SAAS,oBAAoB,CAAC,MAAuB,EAAE,CAAM,EAAA;IAC3D,oCAAoC,CAAC,MAAM,CAAC,SAAS,CAAC,yBAAyB,EAAE,CAAC,CAAC,CAAC;AACpF,IAAA,2CAA2C,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;AACzD,CAAC;AAED,SAAS,2CAA2C,CAAC,MAAuB,EAAE,CAAM,EAAA;AAClF,IAAA,+CAA+C,CAAC,MAAM,CAAC,0BAA0B,CAAC,CAAC;IACnF,4CAA4C,CAAC,MAAM,CAAC,SAAS,CAAC,yBAAyB,EAAE,CAAC,CAAC,CAAC;IAC5F,2BAA2B,CAAC,MAAM,CAAC,CAAC;AACtC,CAAC;AAED,SAAS,2BAA2B,CAAC,MAAuB,EAAA;AAC1D,IAAA,IAAI,MAAM,CAAC,aAAa,EAAE;;;;AAIxB,QAAA,8BAA8B,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;KAC/C;AACH,CAAC;AAED,SAAS,8BAA8B,CAAC,MAAuB,EAAE,YAAqB,EAAA;;AAIpF,IAAA,IAAI,MAAM,CAAC,0BAA0B,KAAK,SAAS,EAAE;QACnD,MAAM,CAAC,kCAAkC,EAAE,CAAC;KAC7C;AAED,IAAA,MAAM,CAAC,0BAA0B,GAAG,UAAU,CAAC,UAAA,OAAO,EAAA;AACpD,QAAA,MAAM,CAAC,kCAAkC,GAAG,OAAO,CAAC;AACtD,KAAC,CAAC,CAAC;AAEH,IAAA,MAAM,CAAC,aAAa,GAAG,YAAY,CAAC;AACtC,CAAC;AAED;AAEA;;;;AAIG;AACH,IAAA,gCAAA,kBAAA,YAAA;AAgBE,IAAA,SAAA,gCAAA,GAAA;AACE,QAAA,MAAM,IAAI,SAAS,CAAC,qBAAqB,CAAC,CAAC;KAC5C;AAKD,IAAA,MAAA,CAAA,cAAA,CAAI,gCAAW,CAAA,SAAA,EAAA,aAAA,EAAA;AAHf;;AAEG;AACH,QAAA,GAAA,EAAA,YAAA;AACE,YAAA,IAAI,CAAC,kCAAkC,CAAC,IAAI,CAAC,EAAE;AAC7C,gBAAA,MAAM,oCAAoC,CAAC,aAAa,CAAC,CAAC;aAC3D;YAED,IAAM,kBAAkB,GAAG,IAAI,CAAC,0BAA0B,CAAC,SAAS,CAAC,yBAAyB,CAAC;AAC/F,YAAA,OAAO,6CAA6C,CAAC,kBAAkB,CAAC,CAAC;SAC1E;;;AAAA,KAAA,CAAA,CAAA;IAMD,gCAAO,CAAA,SAAA,CAAA,OAAA,GAAP,UAAQ,KAAqB,EAAA;QAArB,IAAA,KAAA,KAAA,KAAA,CAAA,EAAA,EAAA,QAAW,SAAU,CAAA,EAAA;AAC3B,QAAA,IAAI,CAAC,kCAAkC,CAAC,IAAI,CAAC,EAAE;AAC7C,YAAA,MAAM,oCAAoC,CAAC,SAAS,CAAC,CAAC;SACvD;AAED,QAAA,uCAAuC,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;KACtD,CAAA;AAED;;;AAGG;IACH,gCAAK,CAAA,SAAA,CAAA,KAAA,GAAL,UAAM,MAAuB,EAAA;AAAvB,QAAA,IAAA,MAAA,KAAA,KAAA,CAAA,EAAA,EAAA,MAAuB,GAAA,SAAA,CAAA,EAAA;AAC3B,QAAA,IAAI,CAAC,kCAAkC,CAAC,IAAI,CAAC,EAAE;AAC7C,YAAA,MAAM,oCAAoC,CAAC,OAAO,CAAC,CAAC;SACrD;AAED,QAAA,qCAAqC,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;KACrD,CAAA;AAED;;;AAGG;AACH,IAAA,gCAAA,CAAA,SAAA,CAAA,SAAS,GAAT,YAAA;AACE,QAAA,IAAI,CAAC,kCAAkC,CAAC,IAAI,CAAC,EAAE;AAC7C,YAAA,MAAM,oCAAoC,CAAC,WAAW,CAAC,CAAC;SACzD;QAED,yCAAyC,CAAC,IAAI,CAAC,CAAC;KACjD,CAAA;IACH,OAAC,gCAAA,CAAA;AAAD,CAAC,EAAA,EAAA;AAED,MAAM,CAAC,gBAAgB,CAAC,gCAAgC,CAAC,SAAS,EAAE;AAClE,IAAA,OAAO,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE;AAC7B,IAAA,KAAK,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE;AAC3B,IAAA,SAAS,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE;AAC/B,IAAA,WAAW,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE;AAClC,CAAA,CAAC,CAAC;AACH,eAAe,CAAC,gCAAgC,CAAC,SAAS,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;AAC/E,eAAe,CAAC,gCAAgC,CAAC,SAAS,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;AAC3E,eAAe,CAAC,gCAAgC,CAAC,SAAS,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC;AACnF,IAAI,OAAOA,cAAM,CAAC,WAAW,KAAK,QAAQ,EAAE;IAC1C,MAAM,CAAC,cAAc,CAAC,gCAAgC,CAAC,SAAS,EAAEA,cAAM,CAAC,WAAW,EAAE;AACpF,QAAA,KAAK,EAAE,kCAAkC;AACzC,QAAA,YAAY,EAAE,IAAI;AACnB,KAAA,CAAC,CAAC;AACL,CAAC;AAED;AAEA,SAAS,kCAAkC,CAAU,CAAM,EAAA;AACzD,IAAA,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE;AACpB,QAAA,OAAO,KAAK,CAAC;KACd;AAED,IAAA,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,4BAA4B,CAAC,EAAE;AAC1E,QAAA,OAAO,KAAK,CAAC;KACd;IAED,OAAO,CAAC,YAAY,gCAAgC,CAAC;AACvD,CAAC;AAED,SAAS,qCAAqC,CAAO,MAA6B,EAC7B,UAA+C,EAC/C,kBAA+C,EAC/C,cAAmC,EACnC,eAA+C,EAAA;AAIlG,IAAA,UAAU,CAAC,0BAA0B,GAAG,MAAM,CAAC;AAC/C,IAAA,MAAM,CAAC,0BAA0B,GAAG,UAAU,CAAC;AAE/C,IAAA,UAAU,CAAC,mBAAmB,GAAG,kBAAkB,CAAC;AACpD,IAAA,UAAU,CAAC,eAAe,GAAG,cAAc,CAAC;AAC5C,IAAA,UAAU,CAAC,gBAAgB,GAAG,eAAe,CAAC;AAE9C,IAAA,UAAU,CAAC,cAAc,GAAG,SAAS,CAAC;AACtC,IAAA,UAAU,CAAC,sBAAsB,GAAG,SAAS,CAAC;AAC9C,IAAA,UAAU,CAAC,qBAAqB,GAAG,SAAS,CAAC;AAC/C,CAAC;AAED,SAAS,oDAAoD,CAAO,MAA6B,EAC7B,WAAuC,EAAA;IACzG,IAAM,UAAU,GAAwC,MAAM,CAAC,MAAM,CAAC,gCAAgC,CAAC,SAAS,CAAC,CAAC;AAElH,IAAA,IAAI,kBAA+C,CAAC;AACpD,IAAA,IAAI,cAAmC,CAAC;AACxC,IAAA,IAAI,eAA+C,CAAC;AAEpD,IAAA,IAAI,WAAW,CAAC,SAAS,KAAK,SAAS,EAAE;AACvC,QAAA,kBAAkB,GAAG,UAAA,KAAK,EAAA,EAAI,OAAA,WAAW,CAAC,SAAU,CAAC,KAAK,EAAE,UAAU,CAAC,CAAA,EAAA,CAAC;KACzE;SAAM;QACL,kBAAkB,GAAG,UAAA,KAAK,EAAA;AACxB,YAAA,IAAI;AACF,gBAAA,uCAAuC,CAAC,UAAU,EAAE,KAAqB,CAAC,CAAC;AAC3E,gBAAA,OAAO,mBAAmB,CAAC,SAAS,CAAC,CAAC;aACvC;YAAC,OAAO,gBAAgB,EAAE;AACzB,gBAAA,OAAO,mBAAmB,CAAC,gBAAgB,CAAC,CAAC;aAC9C;AACH,SAAC,CAAC;KACH;AAED,IAAA,IAAI,WAAW,CAAC,KAAK,KAAK,SAAS,EAAE;QACnC,cAAc,GAAG,YAAM,EAAA,OAAA,WAAW,CAAC,KAAM,CAAC,UAAU,CAAC,CAA9B,EAA8B,CAAC;KACvD;SAAM;QACL,cAAc,GAAG,cAAM,OAAA,mBAAmB,CAAC,SAAS,CAAC,CAA9B,EAA8B,CAAC;KACvD;AAED,IAAA,IAAI,WAAW,CAAC,MAAM,KAAK,SAAS,EAAE;AACpC,QAAA,eAAe,GAAG,UAAA,MAAM,EAAA,EAAI,OAAA,WAAW,CAAC,MAAO,CAAC,MAAM,CAAC,CAAA,EAAA,CAAC;KACzD;SAAM;QACL,eAAe,GAAG,cAAM,OAAA,mBAAmB,CAAC,SAAS,CAAC,CAA9B,EAA8B,CAAC;KACxD;IAED,qCAAqC,CAAC,MAAM,EAAE,UAAU,EAAE,kBAAkB,EAAE,cAAc,EAAE,eAAe,CAAC,CAAC;AACjH,CAAC;AAED,SAAS,+CAA+C,CAAC,UAAiD,EAAA;AACxG,IAAA,UAAU,CAAC,mBAAmB,GAAG,SAAU,CAAC;AAC5C,IAAA,UAAU,CAAC,eAAe,GAAG,SAAU,CAAC;AACxC,IAAA,UAAU,CAAC,gBAAgB,GAAG,SAAU,CAAC;AAC3C,CAAC;AAED,SAAS,uCAAuC,CAAI,UAA+C,EAAE,KAAQ,EAAA;AAC3G,IAAA,IAAM,MAAM,GAAG,UAAU,CAAC,0BAA0B,CAAC;AACrD,IAAA,IAAM,kBAAkB,GAAG,MAAM,CAAC,SAAS,CAAC,yBAAyB,CAAC;AACtE,IAAA,IAAI,CAAC,gDAAgD,CAAC,kBAAkB,CAAC,EAAE;AACzE,QAAA,MAAM,IAAI,SAAS,CAAC,sDAAsD,CAAC,CAAC;KAC7E;;;AAKD,IAAA,IAAI;AACF,QAAA,sCAAsC,CAAC,kBAAkB,EAAE,KAAK,CAAC,CAAC;KACnE;IAAC,OAAO,CAAC,EAAE;;AAEV,QAAA,2CAA2C,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;AAEvD,QAAA,MAAM,MAAM,CAAC,SAAS,CAAC,YAAY,CAAC;KACrC;AAED,IAAA,IAAM,YAAY,GAAG,8CAA8C,CAAC,kBAAkB,CAAC,CAAC;AACxF,IAAA,IAAI,YAAY,KAAK,MAAM,CAAC,aAAa,EAAE;AAEzC,QAAA,8BAA8B,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;KAC9C;AACH,CAAC;AAED,SAAS,qCAAqC,CAAC,UAAiD,EAAE,CAAM,EAAA;AACtG,IAAA,oBAAoB,CAAC,UAAU,CAAC,0BAA0B,EAAE,CAAC,CAAC,CAAC;AACjE,CAAC;AAED,SAAS,gDAAgD,CAAO,UAA+C,EAC/C,KAAQ,EAAA;IACtE,IAAM,gBAAgB,GAAG,UAAU,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;AAC/D,IAAA,OAAO,oBAAoB,CAAC,gBAAgB,EAAE,SAAS,EAAE,UAAA,CAAC,EAAA;AACxD,QAAA,oBAAoB,CAAC,UAAU,CAAC,0BAA0B,EAAE,CAAC,CAAC,CAAC;AAC/D,QAAA,MAAM,CAAC,CAAC;AACV,KAAC,CAAC,CAAC;AACL,CAAC;AAED,SAAS,yCAAyC,CAAI,UAA+C,EAAA;AACnG,IAAA,IAAM,MAAM,GAAG,UAAU,CAAC,0BAA0B,CAAC;AACrD,IAAA,IAAM,kBAAkB,GAAG,MAAM,CAAC,SAAS,CAAC,yBAAyB,CAAC;IAEtE,oCAAoC,CAAC,kBAAkB,CAAC,CAAC;AAEzD,IAAA,IAAM,KAAK,GAAG,IAAI,SAAS,CAAC,4BAA4B,CAAC,CAAC;AAC1D,IAAA,2CAA2C,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;AAC7D,CAAC;AAED;AAEA,SAAS,wCAAwC,CAAO,MAA6B,EAAE,KAAQ,EAAA;AAG7F,IAAA,IAAM,UAAU,GAAG,MAAM,CAAC,0BAA0B,CAAC;AAErD,IAAA,IAAI,MAAM,CAAC,aAAa,EAAE;AACxB,QAAA,IAAM,yBAAyB,GAAG,MAAM,CAAC,0BAA0B,CACnB;QAChD,OAAO,oBAAoB,CAAC,yBAAyB,EAAE,YAAA;AACrD,YAAA,IAAM,QAAQ,GAAG,MAAM,CAAC,SAAS,CAAC;AAClC,YAAA,IAAM,KAAK,GAAG,QAAQ,CAAC,MAAM,CAAC;AAC9B,YAAA,IAAI,KAAK,KAAK,UAAU,EAAE;gBACxB,MAAM,QAAQ,CAAC,YAAY,CAAC;aAED;AAC7B,YAAA,OAAO,gDAAgD,CAAO,UAAU,EAAE,KAAK,CAAC,CAAC;AACnF,SAAC,CAAC,CAAC;KACJ;AAED,IAAA,OAAO,gDAAgD,CAAO,UAAU,EAAE,KAAK,CAAC,CAAC;AACnF,CAAC;AAED,SAAS,wCAAwC,CAAO,MAA6B,EAAE,MAAW,EAAA;AAChG,IAAA,IAAM,UAAU,GAAG,MAAM,CAAC,0BAA0B,CAAC;AACrD,IAAA,IAAI,UAAU,CAAC,cAAc,KAAK,SAAS,EAAE;QAC3C,OAAO,UAAU,CAAC,cAAc,CAAC;KAClC;;AAGD,IAAA,IAAM,QAAQ,GAAG,MAAM,CAAC,SAAS,CAAC;;;IAIlC,UAAU,CAAC,cAAc,GAAG,UAAU,CAAC,UAAC,OAAO,EAAE,MAAM,EAAA;AACrD,QAAA,UAAU,CAAC,sBAAsB,GAAG,OAAO,CAAC;AAC5C,QAAA,UAAU,CAAC,qBAAqB,GAAG,MAAM,CAAC;AAC5C,KAAC,CAAC,CAAC;IAEH,IAAM,aAAa,GAAG,UAAU,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;IAC1D,+CAA+C,CAAC,UAAU,CAAC,CAAC;IAE5D,WAAW,CAAC,aAAa,EAAE,YAAA;AACzB,QAAA,IAAI,QAAQ,CAAC,MAAM,KAAK,SAAS,EAAE;AACjC,YAAA,oCAAoC,CAAC,UAAU,EAAE,QAAQ,CAAC,YAAY,CAAC,CAAC;SACzE;aAAM;AACL,YAAA,oCAAoC,CAAC,QAAQ,CAAC,yBAAyB,EAAE,MAAM,CAAC,CAAC;YACjF,qCAAqC,CAAC,UAAU,CAAC,CAAC;SACnD;AACD,QAAA,OAAO,IAAI,CAAC;KACb,EAAE,UAAA,CAAC,EAAA;AACF,QAAA,oCAAoC,CAAC,QAAQ,CAAC,yBAAyB,EAAE,CAAC,CAAC,CAAC;AAC5E,QAAA,oCAAoC,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC;AACpD,QAAA,OAAO,IAAI,CAAC;AACd,KAAC,CAAC,CAAC;IAEH,OAAO,UAAU,CAAC,cAAc,CAAC;AACnC,CAAC;AAED,SAAS,wCAAwC,CAAO,MAA6B,EAAA;AACnF,IAAA,IAAM,UAAU,GAAG,MAAM,CAAC,0BAA0B,CAAC;AACrD,IAAA,IAAI,UAAU,CAAC,cAAc,KAAK,SAAS,EAAE;QAC3C,OAAO,UAAU,CAAC,cAAc,CAAC;KAClC;;AAGD,IAAA,IAAM,QAAQ,GAAG,MAAM,CAAC,SAAS,CAAC;;;IAIlC,UAAU,CAAC,cAAc,GAAG,UAAU,CAAC,UAAC,OAAO,EAAE,MAAM,EAAA;AACrD,QAAA,UAAU,CAAC,sBAAsB,GAAG,OAAO,CAAC;AAC5C,QAAA,UAAU,CAAC,qBAAqB,GAAG,MAAM,CAAC;AAC5C,KAAC,CAAC,CAAC;AAEH,IAAA,IAAM,YAAY,GAAG,UAAU,CAAC,eAAe,EAAE,CAAC;IAClD,+CAA+C,CAAC,UAAU,CAAC,CAAC;IAE5D,WAAW,CAAC,YAAY,EAAE,YAAA;AACxB,QAAA,IAAI,QAAQ,CAAC,MAAM,KAAK,SAAS,EAAE;AACjC,YAAA,oCAAoC,CAAC,UAAU,EAAE,QAAQ,CAAC,YAAY,CAAC,CAAC;SACzE;aAAM;AACL,YAAA,oCAAoC,CAAC,QAAQ,CAAC,yBAAyB,CAAC,CAAC;YACzE,qCAAqC,CAAC,UAAU,CAAC,CAAC;SACnD;AACD,QAAA,OAAO,IAAI,CAAC;KACb,EAAE,UAAA,CAAC,EAAA;AACF,QAAA,oCAAoC,CAAC,QAAQ,CAAC,yBAAyB,EAAE,CAAC,CAAC,CAAC;AAC5E,QAAA,oCAAoC,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC;AACpD,QAAA,OAAO,IAAI,CAAC;AACd,KAAC,CAAC,CAAC;IAEH,OAAO,UAAU,CAAC,cAAc,CAAC;AACnC,CAAC;AAED;AAEA,SAAS,yCAAyC,CAAC,MAAuB,EAAA;;AAMxE,IAAA,8BAA8B,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;;IAG9C,OAAO,MAAM,CAAC,0BAA0B,CAAC;AAC3C,CAAC;AAED,SAAS,2CAA2C,CAAO,MAA6B,EAAE,MAAW,EAAA;AACnG,IAAA,IAAM,UAAU,GAAG,MAAM,CAAC,0BAA0B,CAAC;AACrD,IAAA,IAAI,UAAU,CAAC,cAAc,KAAK,SAAS,EAAE;QAC3C,OAAO,UAAU,CAAC,cAAc,CAAC;KAClC;;AAGD,IAAA,IAAM,QAAQ,GAAG,MAAM,CAAC,SAAS,CAAC;;;;IAKlC,UAAU,CAAC,cAAc,GAAG,UAAU,CAAC,UAAC,OAAO,EAAE,MAAM,EAAA;AACrD,QAAA,UAAU,CAAC,sBAAsB,GAAG,OAAO,CAAC;AAC5C,QAAA,UAAU,CAAC,qBAAqB,GAAG,MAAM,CAAC;AAC5C,KAAC,CAAC,CAAC;IAEH,IAAM,aAAa,GAAG,UAAU,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;IAC1D,+CAA+C,CAAC,UAAU,CAAC,CAAC;IAE5D,WAAW,CAAC,aAAa,EAAE,YAAA;AACzB,QAAA,IAAI,QAAQ,CAAC,MAAM,KAAK,SAAS,EAAE;AACjC,YAAA,oCAAoC,CAAC,UAAU,EAAE,QAAQ,CAAC,YAAY,CAAC,CAAC;SACzE;aAAM;AACL,YAAA,4CAA4C,CAAC,QAAQ,CAAC,yBAAyB,EAAE,MAAM,CAAC,CAAC;YACzF,2BAA2B,CAAC,MAAM,CAAC,CAAC;YACpC,qCAAqC,CAAC,UAAU,CAAC,CAAC;SACnD;AACD,QAAA,OAAO,IAAI,CAAC;KACb,EAAE,UAAA,CAAC,EAAA;AACF,QAAA,4CAA4C,CAAC,QAAQ,CAAC,yBAAyB,EAAE,CAAC,CAAC,CAAC;QACpF,2BAA2B,CAAC,MAAM,CAAC,CAAC;AACpC,QAAA,oCAAoC,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC;AACpD,QAAA,OAAO,IAAI,CAAC;AACd,KAAC,CAAC,CAAC;IAEH,OAAO,UAAU,CAAC,cAAc,CAAC;AACnC,CAAC;AAED;AAEA,SAAS,oCAAoC,CAAC,IAAY,EAAA;AACxD,IAAA,OAAO,IAAI,SAAS,CAClB,qDAA8C,IAAI,EAAA,yDAAA,CAAyD,CAAC,CAAC;AACjH,CAAC;AAEK,SAAU,qCAAqC,CAAC,UAAiD,EAAA;AACrG,IAAA,IAAI,UAAU,CAAC,sBAAsB,KAAK,SAAS,EAAE;QACnD,OAAO;KACR;IAED,UAAU,CAAC,sBAAsB,EAAE,CAAC;AACpC,IAAA,UAAU,CAAC,sBAAsB,GAAG,SAAS,CAAC;AAC9C,IAAA,UAAU,CAAC,qBAAqB,GAAG,SAAS,CAAC;AAC/C,CAAC;AAEe,SAAA,oCAAoC,CAAC,UAAiD,EAAE,MAAW,EAAA;AACjH,IAAA,IAAI,UAAU,CAAC,qBAAqB,KAAK,SAAS,EAAE;QAClD,OAAO;KACR;AAED,IAAA,yBAAyB,CAAC,UAAU,CAAC,cAAe,CAAC,CAAC;AACtD,IAAA,UAAU,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC;AACzC,IAAA,UAAU,CAAC,sBAAsB,GAAG,SAAS,CAAC;AAC9C,IAAA,UAAU,CAAC,qBAAqB,GAAG,SAAS,CAAC;AAC/C,CAAC;AAED;AAEA,SAAS,yBAAyB,CAAC,IAAY,EAAA;AAC7C,IAAA,OAAO,IAAI,SAAS,CAClB,oCAA6B,IAAI,EAAA,wCAAA,CAAwC,CAAC,CAAC;AAC/E;;;;", "x_google_ignoreList": [1]}