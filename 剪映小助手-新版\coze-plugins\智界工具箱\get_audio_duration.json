{"openapi": "3.0.0", "info": {"title": "剪映小助手_智界工具箱 - 获取音频时长", "description": "获取音频时长", "version": "1.0.0"}, "servers": [{"url": "https://www.aigcview.com"}], "paths": {"/jeecg-boot/api/jianying/get_audio_duration": {"post": {"summary": "获取音频时长", "description": "获取音频时长", "operationId": "get_audio_duration", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"access_key": {"type": "string", "description": "访问密钥（必填）", "example": "JianyingAPI_2025_SecureAccess_AigcView", "default": "JianyingAPI_2025_SecureAccess_AigcView"}, "mp3_url": {"type": "string", "description": "音频链接（必填）", "example": "https://example.com/audio.mp3"}}, "required": ["access_key", "mp3_url"], "additionalProperties": false}}}}, "responses": {"200": {"description": "成功获取音频时长", "content": {"application/json": {"schema": {"type": "object", "properties": {"duration": {"type": "integer", "description": "音频时长（微秒），通过真实音频文件解析获得", "example": 14328000}, "message": {"type": "string", "description": "响应消息", "example": "ok"}}, "required": ["duration", "message"]}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "description": "错误信息", "example": "音频链接不能为空"}}, "required": ["error"]}}}}, "500": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "description": "错误信息", "example": "获取音频时长失败: 网络连接超时"}}, "required": ["error"]}}}}}}}}}