# 版本号管理指南

## 📋 概述

本项目已实现版本号的统一管理，所有版本号都从 `package.json` 动态获取，避免硬编码导致的不一致问题。

## 🎯 版本号显示位置

### 1. 关于对话框
- **位置**: 点击菜单栏"关于剪映小助手"
- **实现**: 通过 IPC 从主进程获取 `app.getVersion()`
- **文件**: `src/renderer/scripts/main.js`

### 2. 设置页面
- **位置**: 设置页面底部"应用信息"区域
- **实现**: 页面加载时通过 IPC 动态获取版本号
- **文件**: `src/renderer/index.html`, `src/renderer/scripts/settings.js`

### 3. 版本更新弹窗
- **位置**: 检查更新时显示的当前版本
- **实现**: 通过 IPC 动态获取版本号
- **文件**: `src/renderer/index.html`, `src/renderer/scripts/main.js`

### 4. 主进程版本检查
- **位置**: 版本检查和更新管理
- **实现**: 直接使用 `app.getVersion()`
- **文件**: `src/main/main.js`, `src/utils/VersionChecker.js`

## 🔧 版本号更新流程

### 方法1: 使用自动化脚本（推荐）

```bash
# 更新版本号到 1.2.0
npm run version:update 1.2.0

# 检查版本号一致性
npm run version:check
```

### 方法2: 手动更新

1. **修改 package.json**
   ```json
   {
     "version": "1.2.0"
   }
   ```

2. **运行一致性检查**
   ```bash
   npm run version:check
   ```

3. **测试版本号显示**
   - 启动应用
   - 检查关于对话框
   - 检查设置页面
   - 测试版本更新功能

## 📁 相关文件

### 核心文件
- `package.json` - 版本号数据源
- `src/main/main.js` - 主进程版本号获取
- `src/renderer/scripts/main.js` - 关于对话框
- `src/renderer/scripts/settings.js` - 设置页面版本显示
- `src/renderer/index.html` - 版本号显示元素

### 工具脚本
- `scripts/update-version.js` - 版本号更新工具
- `scripts/check-version-consistency.js` - 一致性检查工具

### 测试文件
- `test-update-data.sql` - 版本更新测试数据

## 🚀 技术实现

### 主进程获取版本号
```javascript
// 使用 Electron 的 app.getVersion()
const currentVersion = app.getVersion();

// IPC 处理器
safeRegisterIpcHandler('get-version-info', async () => {
  return {
    success: true,
    data: {
      currentVersion: app.getVersion(),
      // 其他信息...
    }
  };
});
```

### 渲染进程获取版本号
```javascript
// 通过 IPC 获取版本信息
const { ipcRenderer } = require('electron');
const versionInfo = await ipcRenderer.invoke('get-version-info');
const currentVersion = versionInfo.data.currentVersion;

// 更新 UI 显示
document.getElementById('app-version').textContent = `v${currentVersion}`;
```

## ✅ 最佳实践

### 1. 版本号格式
- 使用语义化版本号: `主版本.次版本.修订版本`
- 示例: `1.0.0`, `1.2.3`, `2.0.0`

### 2. 更新流程
1. 修改 `package.json` 中的版本号
2. 运行 `npm run version:check` 检查一致性
3. 测试所有版本号显示位置
4. 提交代码并创建版本标签
5. 构建和发布新版本

### 3. 避免硬编码
- ❌ 不要在代码中直接写版本号字符串
- ✅ 始终通过 `app.getVersion()` 或 IPC 获取
- ✅ 使用动态模板字符串显示版本号

### 4. 测试检查清单
- [ ] 关于对话框显示正确版本号
- [ ] 设置页面显示正确版本号
- [ ] 版本更新弹窗显示正确当前版本
- [ ] 版本检查功能正常工作
- [ ] 运行 `npm run version:check` 无错误

## 🔍 故障排除

### 问题1: 版本号显示为 undefined
**原因**: IPC 调用失败或主进程未正确返回版本号
**解决**: 检查主进程的 IPC 处理器和 `app.getVersion()` 调用

### 问题2: 版本号不一致
**原因**: 存在硬编码的版本号
**解决**: 运行 `npm run version:check` 找出不一致的位置并修复

### 问题3: 更新检查失败
**原因**: 版本比较逻辑错误
**解决**: 确保版本号格式正确，检查版本比较算法

## 📝 发布检查清单

发布新版本前请确认：

- [ ] `package.json` 版本号已更新
- [ ] 运行 `npm run version:check` 通过
- [ ] 所有版本号显示位置测试正常
- [ ] 版本更新功能测试正常
- [ ] 构建测试通过
- [ ] 创建对应的 Git 标签
- [ ] 更新发布说明

## 🎉 总结

通过统一的版本号管理系统，我们实现了：
- ✅ 单一数据源（package.json）
- ✅ 动态版本号获取
- ✅ 自动化更新工具
- ✅ 一致性检查机制
- ✅ 完整的测试流程

这确保了版本号在整个应用中的一致性和可维护性。
