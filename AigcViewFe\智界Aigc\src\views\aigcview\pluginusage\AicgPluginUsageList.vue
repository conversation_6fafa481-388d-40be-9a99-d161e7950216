<template>
  <a-card :bordered="false">
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline" @keyup.enter.native="searchQuery">
        <a-row :gutter="24">
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="用户ID">
              <a-input placeholder="请输入用户ID" v-model="queryParam.userId"></a-input>
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="用户昵称">
              <a-input placeholder="请输入用户昵称" v-model="queryParam.userNickname"></a-input>
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="插件名称">
              <a-input placeholder="请输入插件名称" v-model="queryParam.pluginName"></a-input>
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="插件标识">
              <a-input placeholder="请输入插件Key" v-model="queryParam.pluginKey"></a-input>
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="调用状态">
              <a-select placeholder="请选择调用状态" v-model="queryParam.responseStatus" allowClear>
                <a-select-option :value="200">成功</a-select-option>
                <a-select-option :value="400">请求错误</a-select-option>
                <a-select-option :value="401">未授权</a-select-option>
                <a-select-option :value="500">服务器错误</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="调用时间">
              <a-range-picker 
                v-model="queryParam.callTimeRange" 
                format="YYYY-MM-DD"
                placeholder="['开始时间', '结束时间']"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <span class="table-page-search-submitButtons">
              <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
              <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    
    <!-- 操作按钮区域 -->
    <div class="table-operator">
      <a-button type="primary" icon="download" @click="handleExportXls('插件使用记录')">导出</a-button>
      <a-button type="primary" icon="bar-chart" @click="showStats">统计分析</a-button>
      <a-button type="danger" icon="delete" @click="batchDelete" v-if="selectedRowKeys.length > 0">批量删除</a-button>
    </div>

    <!-- table区域-begin -->
    <div>
      <a-table
        ref="table"
        size="middle"
        :scroll="{x:true}"
        bordered
        rowKey="id"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="ipagination"
        :loading="loading"
        :rowSelection="rowSelection"
        class="j-table-force-nowrap"
        @change="handleTableChange">

        <template slot="responseStatus" slot-scope="text">
          <a-tag :color="text === 200 ? 'green' : 'red'">
            {{ text === 200 ? '成功' : `失败(${text})` }}
          </a-tag>
        </template>

        <span slot="action" slot-scope="text, record">
          <a @click="handleDetail(record)">详情</a>
          <a-divider type="vertical" />
          <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
            <a>删除</a>
          </a-popconfirm>
        </span>

      </a-table>
    </div>

    <!-- 统计分析模态框 -->
    <a-modal
      title="插件使用统计分析"
      :width="1200"
      :visible="statsVisible"
      @cancel="statsVisible = false"
      :footer="null">
      <div v-if="statsData">
        <a-row :gutter="16" style="margin-bottom: 16px">
          <a-col :span="6">
            <a-statistic title="总调用次数" :value="statsData.totalCalls" />
          </a-col>
          <a-col :span="6">
            <a-statistic title="成功调用" :value="statsData.successCalls" />
          </a-col>
          <a-col :span="6">
            <a-statistic title="失败调用" :value="statsData.errorCalls" />
          </a-col>
          <a-col :span="6">
            <a-statistic title="成功率" :value="statsData.successRate" suffix="%" :precision="2" />
          </a-col>
        </a-row>
        <a-row :gutter="16" style="margin-bottom: 16px">
          <a-col :span="6">
            <a-statistic title="总消耗Token" :value="statsData.totalTokens" />
          </a-col>
          <a-col :span="6">
            <a-statistic title="总消耗金额" :value="statsData.totalCost" prefix="¥" :precision="2" />
          </a-col>
          <a-col :span="6">
            <a-statistic title="平均响应时间" :value="statsData.avgResponseTime" suffix="ms" />
          </a-col>
          <a-col :span="6">
            <a-statistic title="活跃用户数" :value="statsData.activeUsers" />
          </a-col>
        </a-row>
        <a-divider />
        <a-row :gutter="16">
          <a-col :span="12">
            <h4>热门插件排行</h4>
            <a-table 
              :columns="hotPluginColumns" 
              :dataSource="statsData.hotPlugins" 
              :pagination="false"
              size="small"
            />
          </a-col>
          <a-col :span="12">
            <h4>活跃用户排行</h4>
            <a-table 
              :columns="activeUserColumns" 
              :dataSource="statsData.activeUserList" 
              :pagination="false"
              size="small"
            />
          </a-col>
        </a-row>
      </div>
    </a-modal>

    <!-- 详情模态框 -->
    <a-modal
      title="插件使用详情"
      :width="800"
      :visible="detailVisible"
      @cancel="detailVisible = false"
      :footer="null">
      <div v-if="detailData">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="用户ID">{{ detailData.user_id }}</a-descriptions-item>
          <a-descriptions-item label="用户昵称">{{ detailData.userNickname || '-' }}</a-descriptions-item>
          <a-descriptions-item label="API密钥">{{ detailData.api_key }}</a-descriptions-item>
          <a-descriptions-item label="插件名称">{{ detailData.plugin_name }}</a-descriptions-item>
          <a-descriptions-item label="插件标识">{{ detailData.plugin_key }}</a-descriptions-item>
          <a-descriptions-item label="API接口">{{ detailData.api_endpoint }}</a-descriptions-item>
          <a-descriptions-item label="请求方法">{{ detailData.api_method }}</a-descriptions-item>
          <a-descriptions-item label="响应状态">
            <a-tag :color="detailData.response_status === 200 ? 'green' : 'red'">
              {{ detailData.response_status }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="响应时间">{{ detailData.response_time }}ms</a-descriptions-item>
          <a-descriptions-item label="消耗Token">{{ detailData.tokens_used || '-' }}</a-descriptions-item>
          <a-descriptions-item label="消耗金额">¥{{ detailData.cost_amount || '0.00' }}</a-descriptions-item>
          <a-descriptions-item label="IP地址">{{ detailData.ip_address }}</a-descriptions-item>
          <a-descriptions-item label="调用时间">{{ formatDateTime(detailData.call_time) }}</a-descriptions-item>
        </a-descriptions>
        
        <a-divider />
        
        <h4>请求参数</h4>
        <pre style="background: #f5f5f5; padding: 10px; border-radius: 4px; max-height: 200px; overflow-y: auto;">{{ detailData.request_params }}</pre>

        <div v-if="detailData.error_message">
          <h4>错误信息</h4>
          <a-alert :message="detailData.error_message" type="error" />
        </div>
      </div>
    </a-modal>
  </a-card>
</template>

<script>

  import '@/assets/less/TableExpand.less'
  import { mixinDevice } from '@/utils/mixin'
  import { JeecgListMixin } from '@/mixins/JeecgListMixin'

  export default {
    name: 'AicgUserPluginUsageList',
    mixins:[JeecgListMixin, mixinDevice],
    components: {
    },
    data () {
      return {
        description: '用户插件使用记录管理页面',
        statsVisible: false,
        statsData: null,
        detailVisible: false,
        detailData: null,
        // 表头
        columns: [
          {
            title: '#',
            dataIndex: '',
            key:'rowIndex',
            width:60,
            align:"center",
            customRender:function (t,r,index) {
              return parseInt(index)+1;
            }
          },
          {
            title:'用户ID',
            align:"center",
            dataIndex: 'user_id',
            width: 120,
            ellipsis: true
          },
          {
            title:'用户昵称',
            align:"center",
            dataIndex: 'userNickname',
            width: 120,
            ellipsis: true
          },
          {
            title:'插件名称',
            align:"center",
            dataIndex: 'plugin_name',
            width: 150,
            ellipsis: true
          },
          {
            title:'插件标识',
            align:"center",
            dataIndex: 'plugin_key',
            width: 120,
            ellipsis: true
          },
          {
            title:'API接口',
            align:"center",
            dataIndex: 'api_endpoint',
            width: 200,
            ellipsis: true
          },
          {
            title:'调用状态',
            align:"center",
            dataIndex: 'response_status',
            width: 100,
            scopedSlots: { customRender: 'responseStatus' }
          },
          {
            title:'响应时间',
            align:"center",
            dataIndex: 'response_time',
            width: 100,
            customRender: function (text) {
              return text ? `${text}ms` : '-'
            }
          },
          {
            title:'消耗Token',
            align:"center",
            dataIndex: 'tokens_used',
            width: 100,
            customRender: function (text) {
              return text || '-'
            }
          },
          {
            title:'消耗金额',
            align:"center",
            dataIndex: 'cost_amount',
            width: 100,
            customRender: function (text) {
              return text ? `¥${text}` : '-'
            }
          },
          {
            title:'调用时间',
            align:"center",
            dataIndex: 'call_time',
            width: 150,
            customRender: function (text) {
              if (text) {
                return new Date(text).toLocaleString('zh-CN', {
                  year: 'numeric',
                  month: '2-digit',
                  day: '2-digit',
                  hour: '2-digit',
                  minute: '2-digit',
                  second: '2-digit'
                })
              }
              return '-'
            }
          },
          {
            title: '操作',
            dataIndex: 'action',
            align:"center",
            fixed:"right",
            width:120,
            scopedSlots: { customRender: 'action' }
          }
        ],
        url: {
          list: "/demo/apiusage/pluginUsageList",
          delete: "/demo/apiusage/delete",
          deleteBatch: "/demo/apiusage/deleteBatch",
          exportXlsUrl: "/demo/apiusage/exportXls",
          importExcelUrl: "demo/apiusage/importExcel",
          stats: "/demo/apiusage/getPluginStats"
        },
        dictOptions:{},
        superFieldList:[],
        // 热门插件表头
        hotPluginColumns: [
          { title: '排名', dataIndex: 'rank', width: 60, align: 'center' },
          { title: '插件名称', dataIndex: 'pluginName', ellipsis: true },
          { title: '调用次数', dataIndex: 'callCount', width: 100, align: 'center' }
        ],
        // 活跃用户表头
        activeUserColumns: [
          { title: '排名', dataIndex: 'rank', width: 60, align: 'center' },
          { title: '用户ID', dataIndex: 'userId', ellipsis: true },
          { title: '调用次数', dataIndex: 'callCount', width: 100, align: 'center' }
        ]
      }
    },
    created() {
      this.getSuperFieldList();
    },
    computed: {
      importExcelUrl: function(){
        return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`;
      },
    },
    methods: {
      initDictConfig(){
      },
      getSuperFieldList(){
        let fieldList=[];
        fieldList.push({type:'string',value:'user_id',text:'用户ID'})
        fieldList.push({type:'string',value:'userNickname',text:'用户昵称'})
        fieldList.push({type:'string',value:'plugin_name',text:'插件名称'})
        fieldList.push({type:'string',value:'plugin_key',text:'插件标识'})
        fieldList.push({type:'string',value:'api_endpoint',text:'API接口地址'})
        fieldList.push({type:'string',value:'api_method',text:'请求方法'})
        fieldList.push({type:'int',value:'response_status',text:'响应状态码'})
        fieldList.push({type:'int',value:'response_time',text:'响应时间'})
        fieldList.push({type:'int',value:'tokens_used',text:'消耗Token数量'})
        fieldList.push({type:'BigDecimal',value:'cost_amount',text:'消耗金额'})
        fieldList.push({type:'Date',value:'call_time',text:'调用时间'})
        this.superFieldList = fieldList
      },
      // 显示统计分析
      showStats() {
        this.loading = true
        this.$http.get(this.url.stats).then(res => {
          this.loading = false
          if (res.success) {
            this.statsData = res.result
            this.statsVisible = true
          } else {
            this.$message.error(res.message || '获取统计数据失败')
          }
        }).catch(err => {
          this.loading = false
          this.$message.error('获取统计数据失败')
        })
      },
      // 显示详情
      handleDetail(record) {
        this.detailData = record
        this.detailVisible = true
      },
      // 批量删除
      batchDelete() {
        if (this.selectedRowKeys.length === 0) {
          this.$message.warning('请选择要删除的记录')
          return
        }
        
        this.$confirm({
          title: '确认删除',
          content: `确定要删除选中的 ${this.selectedRowKeys.length} 条记录吗？`,
          onOk: () => {
            this.loading = true
            this.$http.delete(this.url.deleteBatch, {
              data: this.selectedRowKeys
            }).then(res => {
              this.loading = false
              if (res.success) {
                this.$message.success('删除成功')
                this.loadData()
                this.onClearSelected()
              } else {
                this.$message.error(res.message || '删除失败')
              }
            }).catch(err => {
              this.loading = false
              this.$message.error('删除失败')
            })
          }
        })
      },
      // 格式化时间显示
      formatDateTime(dateTime) {
        if (!dateTime) return '-'
        return new Date(dateTime).toLocaleString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit'
        })
      },
      // 重写查询方法，添加插件相关筛选
      loadData(arg) {
        if (!this.url.list) {
          this.$message.error("请设置url.list属性!")
          return
        }
        
        // 处理时间范围查询
        let params = Object.assign({}, this.queryParam, this.isorter, this.filters)
        if (this.queryParam.callTimeRange && this.queryParam.callTimeRange.length === 2) {
          params.callTimeStart = this.queryParam.callTimeRange[0].format('YYYY-MM-DD')
          params.callTimeEnd = this.queryParam.callTimeRange[1].format('YYYY-MM-DD')
          delete params.callTimeRange
        }
        
        // 只查询有插件信息的记录
        params.hasPluginInfo = true
        
        params.pageNo = this.ipagination.current
        params.pageSize = this.ipagination.pageSize
        
        this.loading = true
        this.$http.get(this.url.list, {params: params}).then(res => {
          this.loading = false
          if (res.success) {
            this.dataSource = res.result.records || res.result
            this.ipagination.total = res.result.total
          } else {
            this.$message.error(res.message || '查询失败')
          }
        }).catch(err => {
          this.loading = false
          this.$message.error('查询失败')
        })
      }
    }
  }
</script>
<style scoped>
  @import '~@assets/less/common.less';
</style>
