!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((e=e||self).TOS={})}(this,(function(e){"use strict";var t=function(e,t){return function(){for(var n=new Array(arguments.length),r=0;r<n.length;r++)n[r]=arguments[r];return e.apply(t,n)}},n=Object.prototype.toString;function r(e){return"[object Array]"===n.call(e)}function o(e){return void 0===e}function a(e){return null!==e&&"object"==typeof e}function i(e){if("[object Object]"!==n.call(e))return!1;var t=Object.getPrototypeOf(e);return null===t||t===Object.prototype}function s(e){return"[object Function]"===n.call(e)}function c(e,t){if(null!=e)if("object"!=typeof e&&(e=[e]),r(e))for(var n=0,o=e.length;n<o;n++)t.call(null,e[n],n,e);else for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.call(null,e[a],a,e)}var u={isArray:r,isArrayBuffer:function(e){return"[object ArrayBuffer]"===n.call(e)},isBuffer:function(e){return null!==e&&!o(e)&&null!==e.constructor&&!o(e.constructor)&&"function"==typeof e.constructor.isBuffer&&e.constructor.isBuffer(e)},isFormData:function(e){return"undefined"!=typeof FormData&&e instanceof FormData},isArrayBufferView:function(e){return"undefined"!=typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(e):e&&e.buffer&&e.buffer instanceof ArrayBuffer},isString:function(e){return"string"==typeof e},isNumber:function(e){return"number"==typeof e},isObject:a,isPlainObject:i,isUndefined:o,isDate:function(e){return"[object Date]"===n.call(e)},isFile:function(e){return"[object File]"===n.call(e)},isBlob:function(e){return"[object Blob]"===n.call(e)},isFunction:s,isStream:function(e){return a(e)&&s(e.pipe)},isURLSearchParams:function(e){return"undefined"!=typeof URLSearchParams&&e instanceof URLSearchParams},isStandardBrowserEnv:function(){return("undefined"==typeof navigator||"ReactNative"!==navigator.product&&"NativeScript"!==navigator.product&&"NS"!==navigator.product)&&"undefined"!=typeof window&&"undefined"!=typeof document},forEach:c,merge:function e(){var t={};function n(n,o){t[o]=i(t[o])&&i(n)?e(t[o],n):i(n)?e({},n):r(n)?n.slice():n}for(var o=0,a=arguments.length;o<a;o++)c(arguments[o],n);return t},extend:function(e,n,r){return c(n,(function(n,o){e[o]=r&&"function"==typeof n?t(n,r):n})),e},trim:function(e){return e.trim?e.trim():e.replace(/^\s+|\s+$/g,"")},stripBOM:function(e){return 65279===e.charCodeAt(0)&&(e=e.slice(1)),e}};function p(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}var l=function(e,t,n){if(!t)return e;var r;if(n)r=n(t);else if(u.isURLSearchParams(t))r=t.toString();else{var o=[];u.forEach(t,(function(e,t){null!=e&&(u.isArray(e)?t+="[]":e=[e],u.forEach(e,(function(e){u.isDate(e)?e=e.toISOString():u.isObject(e)&&(e=JSON.stringify(e)),o.push(p(t)+"="+p(e))})))})),r=o.join("&")}if(r){var a=e.indexOf("#");-1!==a&&(e=e.slice(0,a)),e+=(-1===e.indexOf("?")?"?":"&")+r}return e};function d(){this.handlers=[]}d.prototype.use=function(e,t,n){return this.handlers.push({fulfilled:e,rejected:t,synchronous:!!n&&n.synchronous,runWhen:n?n.runWhen:null}),this.handlers.length-1},d.prototype.eject=function(e){this.handlers[e]&&(this.handlers[e]=null)},d.prototype.forEach=function(e){u.forEach(this.handlers,(function(t){null!==t&&e(t)}))};var f=d,h=function(e,t){u.forEach(e,(function(n,r){r!==t&&r.toUpperCase()===t.toUpperCase()&&(e[t]=n,delete e[r])}))},y=function(e,t,n,r,o){return e.config=t,n&&(e.code=n),e.request=r,e.response=o,e.isAxiosError=!0,e.toJSON=function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:this.config,code:this.code}},e},m=function(e,t,n,r,o){var a=new Error(e);return y(a,t,n,r,o)},g=function(e,t,n){var r=n.config.validateStatus;n.status&&r&&!r(n.status)?t(m("Request failed with status code "+n.status,n.config,null,n.request,n)):e(n)},b=u.isStandardBrowserEnv()?{write:function(e,t,n,r,o,a){var i=[];i.push(e+"="+encodeURIComponent(t)),u.isNumber(n)&&i.push("expires="+new Date(n).toGMTString()),u.isString(r)&&i.push("path="+r),u.isString(o)&&i.push("domain="+o),!0===a&&i.push("secure"),document.cookie=i.join("; ")},read:function(e){var t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove:function(e){this.write(e,"",Date.now()-864e5)}}:{write:function(){},read:function(){return null},remove:function(){}},v=function(e,t){return e&&!/^([a-z][a-z\d\+\-\.]*:)?\/\//i.test(t)?function(e,t){return t?e.replace(/\/+$/,"")+"/"+t.replace(/^\/+/,""):e}(e,t):t},x=["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"],w=u.isStandardBrowserEnv()?function(){var e,t=/(msie|trident)/i.test(navigator.userAgent),n=document.createElement("a");function r(e){var r=e;return t&&(n.setAttribute("href",r),r=n.href),n.setAttribute("href",r),{href:n.href,protocol:n.protocol?n.protocol.replace(/:$/,""):"",host:n.host,search:n.search?n.search.replace(/^\?/,""):"",hash:n.hash?n.hash.replace(/^#/,""):"",hostname:n.hostname,port:n.port,pathname:"/"===n.pathname.charAt(0)?n.pathname:"/"+n.pathname}}return e=r(window.location.href),function(t){var n=u.isString(t)?r(t):t;return n.protocol===e.protocol&&n.host===e.host}}():function(){return!0},S={"Content-Type":"application/x-www-form-urlencoded"};function k(e,t){!u.isUndefined(e)&&u.isUndefined(e["Content-Type"])&&(e["Content-Type"]=t)}var C,T={transitional:{silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},adapter:(("undefined"!=typeof XMLHttpRequest||"undefined"!=typeof process&&"[object process]"===Object.prototype.toString.call(process))&&(C=function(e){return new Promise((function(t,n){var r=e.data,o=e.headers,a=e.responseType;u.isFormData(r)&&delete o["Content-Type"];var i=new XMLHttpRequest;if(e.auth){var s=e.auth.username||"",c=e.auth.password?unescape(encodeURIComponent(e.auth.password)):"";o.Authorization="Basic "+btoa(s+":"+c)}var p=v(e.baseURL,e.url);function d(){if(i){var r,o,s,c,p,l="getAllResponseHeaders"in i?(r=i.getAllResponseHeaders(),p={},r?(u.forEach(r.split("\n"),(function(e){if(c=e.indexOf(":"),o=u.trim(e.substr(0,c)).toLowerCase(),s=u.trim(e.substr(c+1)),o){if(p[o]&&x.indexOf(o)>=0)return;p[o]="set-cookie"===o?(p[o]?p[o]:[]).concat([s]):p[o]?p[o]+", "+s:s}})),p):p):null;g(t,n,{data:a&&"text"!==a&&"json"!==a?i.response:i.responseText,status:i.status,statusText:i.statusText,headers:l,config:e,request:i}),i=null}}if(i.open(e.method.toUpperCase(),l(p,e.params,e.paramsSerializer),!0),i.timeout=e.timeout,"onloadend"in i?i.onloadend=d:i.onreadystatechange=function(){i&&4===i.readyState&&(0!==i.status||i.responseURL&&0===i.responseURL.indexOf("file:"))&&setTimeout(d)},i.onabort=function(){i&&(n(m("Request aborted",e,"ECONNABORTED",i)),i=null)},i.onerror=function(){n(m("Network Error",e,null,i)),i=null},i.ontimeout=function(){var t="timeout of "+e.timeout+"ms exceeded";e.timeoutErrorMessage&&(t=e.timeoutErrorMessage),n(m(t,e,e.transitional&&e.transitional.clarifyTimeoutError?"ETIMEDOUT":"ECONNABORTED",i)),i=null},u.isStandardBrowserEnv()){var f=(e.withCredentials||w(p))&&e.xsrfCookieName?b.read(e.xsrfCookieName):void 0;f&&(o[e.xsrfHeaderName]=f)}"setRequestHeader"in i&&u.forEach(o,(function(e,t){void 0===r&&"content-type"===t.toLowerCase()?delete o[t]:i.setRequestHeader(t,e)})),u.isUndefined(e.withCredentials)||(i.withCredentials=!!e.withCredentials),a&&"json"!==a&&(i.responseType=e.responseType),"function"==typeof e.onDownloadProgress&&i.addEventListener("progress",e.onDownloadProgress),"function"==typeof e.onUploadProgress&&i.upload&&i.upload.addEventListener("progress",e.onUploadProgress),e.cancelToken&&e.cancelToken.promise.then((function(e){i&&(i.abort(),n(e),i=null)})),r||(r=null),i.send(r)}))}),C),transformRequest:[function(e,t){return h(t,"Accept"),h(t,"Content-Type"),u.isFormData(e)||u.isArrayBuffer(e)||u.isBuffer(e)||u.isStream(e)||u.isFile(e)||u.isBlob(e)?e:u.isArrayBufferView(e)?e.buffer:u.isURLSearchParams(e)?(k(t,"application/x-www-form-urlencoded;charset=utf-8"),e.toString()):u.isObject(e)||t&&"application/json"===t["Content-Type"]?(k(t,"application/json"),function(e,t,n){if(u.isString(e))try{return(0,JSON.parse)(e),u.trim(e)}catch(e){if("SyntaxError"!==e.name)throw e}return(0,JSON.stringify)(e)}(e)):e}],transformResponse:[function(e){var t=this.transitional,n=!(t&&t.silentJSONParsing)&&"json"===this.responseType;if(n||t&&t.forcedJSONParsing&&u.isString(e)&&e.length)try{return JSON.parse(e)}catch(e){if(n){if("SyntaxError"===e.name)throw y(e,this,"E_JSON_PARSE");throw e}}return e}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,validateStatus:function(e){return e>=200&&e<300},headers:{common:{Accept:"application/json, text/plain, */*"}}};u.forEach(["delete","get","head"],(function(e){T.headers[e]={}})),u.forEach(["post","put","patch"],(function(e){T.headers[e]=u.merge(S)}));var E=T,j=function(e,t,n){var r=this||E;return u.forEach(n,(function(n){e=n.call(r,e,t)})),e},O=function(e){return!(!e||!e.__CANCEL__)};function R(e){e.cancelToken&&e.cancelToken.throwIfRequested()}var A=function(e){return R(e),e.headers=e.headers||{},e.data=j.call(e,e.data,e.headers,e.transformRequest),e.headers=u.merge(e.headers.common||{},e.headers[e.method]||{},e.headers),u.forEach(["delete","get","head","post","put","patch","common"],(function(t){delete e.headers[t]})),(e.adapter||E.adapter)(e).then((function(t){return R(e),t.data=j.call(e,t.data,t.headers,e.transformResponse),t}),(function(t){return O(t)||(R(e),t&&t.response&&(t.response.data=j.call(e,t.response.data,t.response.headers,e.transformResponse))),Promise.reject(t)}))},P=function(e,t){t=t||{};var n={},r=["url","method","data"],o=["headers","auth","proxy","params"],a=["baseURL","transformRequest","transformResponse","paramsSerializer","timeout","timeoutMessage","withCredentials","adapter","responseType","xsrfCookieName","xsrfHeaderName","onUploadProgress","onDownloadProgress","decompress","maxContentLength","maxBodyLength","maxRedirects","transport","httpAgent","httpsAgent","cancelToken","socketPath","responseEncoding"],i=["validateStatus"];function s(e,t){return u.isPlainObject(e)&&u.isPlainObject(t)?u.merge(e,t):u.isPlainObject(t)?u.merge({},t):u.isArray(t)?t.slice():t}function c(r){u.isUndefined(t[r])?u.isUndefined(e[r])||(n[r]=s(void 0,e[r])):n[r]=s(e[r],t[r])}u.forEach(r,(function(e){u.isUndefined(t[e])||(n[e]=s(void 0,t[e]))})),u.forEach(o,c),u.forEach(a,(function(r){u.isUndefined(t[r])?u.isUndefined(e[r])||(n[r]=s(void 0,e[r])):n[r]=s(void 0,t[r])})),u.forEach(i,(function(r){r in t?n[r]=s(e[r],t[r]):r in e&&(n[r]=s(void 0,e[r]))}));var p=r.concat(o).concat(a).concat(i),l=Object.keys(e).concat(Object.keys(t)).filter((function(e){return-1===p.indexOf(e)}));return u.forEach(l,c),n},B="Promise based HTTP client for the browser and node.js",_={test:"grunt test",start:"node ./sandbox/server.js",build:"NODE_ENV=production grunt build",preversion:"npm test",version:"npm run build && grunt version && git add -A dist && git add CHANGELOG.md bower.json package.json",postversion:"git push && git push --tags",examples:"node ./examples/server.js",coveralls:"cat coverage/lcov.info | ./node_modules/coveralls/bin/coveralls.js",fix:"eslint --fix lib/**/*.js"},I={type:"git",url:"https://github.com/axios/axios.git"},M=["xhr","http","ajax","promise","node"],D={url:"https://github.com/axios/axios/issues"},U={coveralls:"^3.0.0","es6-promise":"^4.2.4",grunt:"^1.3.0","grunt-banner":"^0.6.0","grunt-cli":"^1.2.0","grunt-contrib-clean":"^1.1.0","grunt-contrib-watch":"^1.0.0","grunt-eslint":"^23.0.0","grunt-karma":"^4.0.0","grunt-mocha-test":"^0.13.3","grunt-ts":"^6.0.0-beta.19","grunt-webpack":"^4.0.2","istanbul-instrumenter-loader":"^1.0.0","jasmine-core":"^2.4.1",karma:"^6.3.2","karma-chrome-launcher":"^3.1.0","karma-firefox-launcher":"^2.1.0","karma-jasmine":"^1.1.1","karma-jasmine-ajax":"^0.1.13","karma-safari-launcher":"^1.0.0","karma-sauce-launcher":"^4.3.6","karma-sinon":"^1.0.5","karma-sourcemap-loader":"^0.3.8","karma-webpack":"^4.0.2","load-grunt-tasks":"^3.5.2",minimist:"^1.2.0",mocha:"^8.2.1",sinon:"^4.5.0","terser-webpack-plugin":"^4.2.3",typescript:"^4.0.5","url-search-params":"^0.10.0",webpack:"^4.44.2","webpack-dev-server":"^3.11.0"},F={"./lib/adapters/http.js":"./lib/adapters/xhr.js"},N="dist/axios.min.js",L={"follow-redirects":"^1.14.0"},z=[{path:"./dist/axios.min.js",threshold:"5kB"}],q="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function K(e,t){return e(t={exports:{}},t.exports),t.exports}function H(e){return e&&e.default||e}var $=H({__proto__:null,name:"axios",version:"0.21.4",description:B,main:"index.js",scripts:_,repository:I,keywords:M,author:"Matt Zabriskie",license:"MIT",bugs:D,homepage:"https://axios-http.com",devDependencies:U,browser:F,jsdelivr:"dist/axios.min.js",unpkg:N,typings:"./index.d.ts",dependencies:L,bundlesize:z,default:{name:"axios",version:"0.21.4",description:B,main:"index.js",scripts:_,repository:I,keywords:M,author:"Matt Zabriskie",license:"MIT",bugs:D,homepage:"https://axios-http.com",devDependencies:U,browser:F,jsdelivr:"dist/axios.min.js",unpkg:N,typings:"./index.d.ts",dependencies:L,bundlesize:z}}),G={};["object","boolean","number","function","string","symbol"].forEach((function(e,t){G[e]=function(n){return typeof n===e||"a"+(t<1?"n ":" ")+e}}));var W={},V=$.version.split(".");function J(e,t){for(var n=t?t.split("."):V,r=e.split("."),o=0;o<3;o++){if(n[o]>r[o])return!0;if(n[o]<r[o])return!1}return!1}G.transitional=function(e,t,n){var r=t&&J(t);function o(e,t){return"[Axios v"+$.version+"] Transitional option '"+e+"'"+t+(n?". "+n:"")}return function(n,a,i){if(!1===e)throw new Error(o(a," has been removed in "+t));return r&&!W[a]&&(W[a]=!0,console.warn(o(a," has been deprecated since v"+t+" and will be removed in the near future"))),!e||e(n,a,i)}};var Q={isOlderVersion:J,assertOptions:function(e,t,n){if("object"!=typeof e)throw new TypeError("options must be an object");for(var r=Object.keys(e),o=r.length;o-- >0;){var a=r[o],i=t[a];if(i){var s=e[a],c=void 0===s||i(s,a,e);if(!0!==c)throw new TypeError("option "+a+" must be "+c)}else if(!0!==n)throw Error("Unknown option "+a)}},validators:G},X=Q.validators;function Z(e){this.defaults=e,this.interceptors={request:new f,response:new f}}Z.prototype.request=function(e){"string"==typeof e?(e=arguments[1]||{}).url=arguments[0]:e=e||{},(e=P(this.defaults,e)).method=e.method?e.method.toLowerCase():this.defaults.method?this.defaults.method.toLowerCase():"get";var t=e.transitional;void 0!==t&&Q.assertOptions(t,{silentJSONParsing:X.transitional(X.boolean,"1.0.0"),forcedJSONParsing:X.transitional(X.boolean,"1.0.0"),clarifyTimeoutError:X.transitional(X.boolean,"1.0.0")},!1);var n=[],r=!0;this.interceptors.request.forEach((function(t){"function"==typeof t.runWhen&&!1===t.runWhen(e)||(r=r&&t.synchronous,n.unshift(t.fulfilled,t.rejected))}));var o,a=[];if(this.interceptors.response.forEach((function(e){a.push(e.fulfilled,e.rejected)})),!r){var i=[A,void 0];for(Array.prototype.unshift.apply(i,n),i=i.concat(a),o=Promise.resolve(e);i.length;)o=o.then(i.shift(),i.shift());return o}for(var s=e;n.length;){var c=n.shift(),u=n.shift();try{s=c(s)}catch(e){u(e);break}}try{o=A(s)}catch(e){return Promise.reject(e)}for(;a.length;)o=o.then(a.shift(),a.shift());return o},Z.prototype.getUri=function(e){return e=P(this.defaults,e),l(e.url,e.params,e.paramsSerializer).replace(/^\?/,"")},u.forEach(["delete","get","head","options"],(function(e){Z.prototype[e]=function(t,n){return this.request(P(n||{},{method:e,url:t,data:(n||{}).data}))}})),u.forEach(["post","put","patch"],(function(e){Z.prototype[e]=function(t,n,r){return this.request(P(r||{},{method:e,url:t,data:n}))}}));var Y=Z;function ee(e){this.message=e}ee.prototype.toString=function(){return"Cancel"+(this.message?": "+this.message:"")},ee.prototype.__CANCEL__=!0;var te=ee;function ne(e){if("function"!=typeof e)throw new TypeError("executor must be a function.");var t;this.promise=new Promise((function(e){t=e}));var n=this;e((function(e){n.reason||(n.reason=new te(e),t(n.reason))}))}ne.prototype.throwIfRequested=function(){if(this.reason)throw this.reason},ne.source=function(){var e;return{token:new ne((function(t){e=t})),cancel:e}};var re=ne;function oe(e){var n=new Y(e),r=t(Y.prototype.request,n);return u.extend(r,Y.prototype,n),u.extend(r,n),r}var ae=oe(E);ae.Axios=Y,ae.create=function(e){return oe(P(ae.defaults,e))},ae.Cancel=te,ae.CancelToken=re,ae.isCancel=O,ae.all=function(e){return Promise.all(e)},ae.spread=function(e){return function(t){return e.apply(null,t)}},ae.isAxiosError=function(e){return"object"==typeof e&&!0===e.isAxiosError};var ie=ae;ie.default=ae;var se,ce=ie;class ue extends Error{constructor(e){const{data:t}=e;super(t.Message),this.code=void 0,this.data=void 0,this.statusCode=void 0,this.headers=void 0,this.requestId=void 0,this.id2=void 0,Object.setPrototypeOf(this,ue.prototype),this.data=t,this.code=t.Code,this.statusCode=e.status,this.headers=e.headers,this.requestId=e.headers["x-tos-request-id"],this.id2=e.headers["x-tos-id-2"]}}(se=e.TosServerCode||(e.TosServerCode={})).NoSuchBucket="NoSuchBucket",se.NoSuchKey="NoSuchKey",se.AccessDenied="AccessDenied",se.MalformedAcl="MalformedAclError",se.UnexpectedContent="UnexpectedContent",se.InvalidRequest="InvalidRequest",se.MissingSecurityHeader="MissingSecurityHeader",se.InvalidArgument="InvalidArgument",se.EntityTooSmall="EntityTooSmall",se.InvalidBucketName="InvalidBucketName",se.BucketNotEmpty="BucketNotEmpty",se.TooManyBuckets="TooManyBuckets",se.BucketAlreadyExists="BucketAlreadyExists",se.MalformedBody="MalformedBody",se.NoSuchLifecycleConfiguration="NoSuchLifecycleConfiguration",se.ReplicationConfigurationNotFound="ReplicationConfigurationNotFoundError",se.InvalidLocationConstraint="InvalidLocationConstraint",se.AuthorizationQueryParametersError="AuthorizationQueryParametersError",se.RequestTimeTooSkewed="RequestTimeTooSkewed",se.SignatureDoesNotMatch="SignatureDoesNotMatch",se.RequestedRangeNotSatisfiable="Requested Range Not Satisfiable",se.PreconditionFailed="PreconditionFailed",se.BadDigest="BadDigest",se.InvalidDigest="InvalidDigest",se.EntityTooLarge="EntityTooLarge",se.UnImplemented="UnImplemented",se.MethodNotAllowed="MethodNotAllowed",se.InvalidAccessKeyId="InvalidAccessKeyId",se.InvalidSecurityToken="InvalidSecurityToken",se.ContentSHA256Mismatch="ContentSHA256Mismatch",se.ExceedQPSLimit="ExceedQPSLimit",se.ExceedRateLimit="ExceedRateLimit",se.NoSuchCORSConfiguration="NoSuchCORSConfiguration",se.NoSuchMirrorConfiguration="NoSuchMirrorConfiguration",se.NoSuchWebsiteConfiguration="NoSuchWebsiteConfiguration",se.MissingRequestBody="MissingRequestBodyError",se.BucketAlreadyOwnedByYou="BucketAlreadyOwnedByYou",se.NoSuchBucketPolicy="NoSuchBucketPolicy",se.PolicyTooLarge="PolicyTooLarge",se.MalformedPolicy="MalformedPolicy",se.InvalidKey="InvalidKey",se.MirrorFailed="MirrorFailed",se.Timeout="Timeout",se.OffsetNotMatched="OffsetNotMatched",se.NotAppendable="NotAppendable",se.ContextCanceled="ContextCanceled",se.InternalError="InternalError",se.TooManyRequests="TooManyRequests",se.TimeOut="TimeOut",se.ConcurrencyUpdateObjectLimit="ConcurrencyUpdateObjectLimit",se.DuplicateUpload="DuplicateUpload",se.DuplicateObject="DuplicateObject",se.InvalidVersionId="InvalidVersionId",se.StorageClassNotMatch="StorageClassNotMatch",se.UploadStatusNotUploading="UploadStatusNotUploading",se.PartSizeNotMatch="PartSizeNotMatch",se.NoUploadPart="NoUploadPart",se.PartsLenInvalid="PartsLenInvalid",se.PartsIdxSmall="PartsIdxSmall",se.PartSizeSmall="PartSizeSmall",se.PrefixNotNextKeyPrefix="PrefixNotNextKeyPrefix",se.InvalidPart="InvalidPart",se.InvalidPartOffset="InvalidPartOffset",se.MismatchObject="MismatchObject",se.UploadStatusMismatch="UploadStatusMismatch",se.CompletingStatusNoExpiration="CompletingStatusNoExpiration",se.Found="Found",se.InvalidRedirectLocation="InvalidRedirectLocation";class pe extends Error{constructor(e){super(e),Object.setPrototypeOf(this,pe.prototype)}}var le,de=Array.isArray,fe="object"==typeof q&&q&&q.Object===Object&&q,he="object"==typeof self&&self&&self.Object===Object&&self,ye=fe||he||Function("return this")(),me=ye.Symbol,ge=Object.prototype,be=ge.hasOwnProperty,ve=ge.toString,xe=me?me.toStringTag:void 0,we=Object.prototype.toString,Se=me?me.toStringTag:void 0,ke=function(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":Se&&Se in Object(e)?function(e){var t=be.call(e,xe),n=e[xe];try{e[xe]=void 0;var r=!0}catch(e){}var o=ve.call(e);return r&&(t?e[xe]=n:delete e[xe]),o}(e):function(e){return we.call(e)}(e)},Ce=function(e){return null!=e&&"object"==typeof e},Te=function(e){return"symbol"==typeof e||Ce(e)&&"[object Symbol]"==ke(e)},Ee=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,je=/^\w*$/,Oe=function(e){var t=typeof e;return null!=e&&("object"==t||"function"==t)},Re=function(e){if(!Oe(e))return!1;var t=ke(e);return"[object Function]"==t||"[object GeneratorFunction]"==t||"[object AsyncFunction]"==t||"[object Proxy]"==t},Ae=ye["__core-js_shared__"],Pe=(le=/[^.]+$/.exec(Ae&&Ae.keys&&Ae.keys.IE_PROTO||""))?"Symbol(src)_1."+le:"",Be=Function.prototype.toString,_e=function(e){if(null!=e){try{return Be.call(e)}catch(e){}try{return e+""}catch(e){}}return""},Ie=/^\[object .+?Constructor\]$/,Me=RegExp("^"+Function.prototype.toString.call(Object.prototype.hasOwnProperty).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),De=function(e,t){var n=function(e,t){return null==e?void 0:e[t]}(e,t);return function(e){return!(!Oe(e)||(t=e,Pe&&Pe in t))&&(Re(e)?Me:Ie).test(_e(e));var t}(n)?n:void 0},Ue=De(Object,"create"),Fe=Object.prototype.hasOwnProperty,Ne=Object.prototype.hasOwnProperty;function Le(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}Le.prototype.clear=function(){this.__data__=Ue?Ue(null):{},this.size=0},Le.prototype.delete=function(e){var t=this.has(e)&&delete this.__data__[e];return this.size-=t?1:0,t},Le.prototype.get=function(e){var t=this.__data__;if(Ue){var n=t[e];return"__lodash_hash_undefined__"===n?void 0:n}return Fe.call(t,e)?t[e]:void 0},Le.prototype.has=function(e){var t=this.__data__;return Ue?void 0!==t[e]:Ne.call(t,e)},Le.prototype.set=function(e,t){var n=this.__data__;return this.size+=this.has(e)?0:1,n[e]=Ue&&void 0===t?"__lodash_hash_undefined__":t,this};var ze=Le,qe=function(e,t){return e===t||e!=e&&t!=t},Ke=function(e,t){for(var n=e.length;n--;)if(qe(e[n][0],t))return n;return-1},He=Array.prototype.splice;function $e(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}$e.prototype.clear=function(){this.__data__=[],this.size=0},$e.prototype.delete=function(e){var t=this.__data__,n=Ke(t,e);return!(n<0||(n==t.length-1?t.pop():He.call(t,n,1),--this.size,0))},$e.prototype.get=function(e){var t=this.__data__,n=Ke(t,e);return n<0?void 0:t[n][1]},$e.prototype.has=function(e){return Ke(this.__data__,e)>-1},$e.prototype.set=function(e,t){var n=this.__data__,r=Ke(n,e);return r<0?(++this.size,n.push([e,t])):n[r][1]=t,this};var Ge=$e,We=De(ye,"Map"),Ve=function(e,t){var n,r,o=e.__data__;return("string"==(r=typeof(n=t))||"number"==r||"symbol"==r||"boolean"==r?"__proto__"!==n:null===n)?o["string"==typeof t?"string":"hash"]:o.map};function Je(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}Je.prototype.clear=function(){this.size=0,this.__data__={hash:new ze,map:new(We||Ge),string:new ze}},Je.prototype.delete=function(e){var t=Ve(this,e).delete(e);return this.size-=t?1:0,t},Je.prototype.get=function(e){return Ve(this,e).get(e)},Je.prototype.has=function(e){return Ve(this,e).has(e)},Je.prototype.set=function(e,t){var n=Ve(this,e),r=n.size;return n.set(e,t),this.size+=n.size==r?0:1,this};var Qe=Je;function Xe(e,t){if("function"!=typeof e||null!=t&&"function"!=typeof t)throw new TypeError("Expected a function");var n=function(){var r=arguments,o=t?t.apply(this,r):r[0],a=n.cache;if(a.has(o))return a.get(o);var i=e.apply(this,r);return n.cache=a.set(o,i)||a,i};return n.cache=new(Xe.Cache||Qe),n}Xe.Cache=Qe;var Ze=Xe,Ye=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,et=/\\(\\)?/g,tt=function(e){var t=Ze((function(e){var t=[];return 46===e.charCodeAt(0)&&t.push(""),e.replace(Ye,(function(e,n,r,o){t.push(r?o.replace(et,"$1"):n||e)})),t}),(function(e){return 500===n.size&&n.clear(),e})),n=t.cache;return t}(),nt=me?me.prototype:void 0,rt=nt?nt.toString:void 0,ot=function(e,t){return de(e)?e:function(e,t){if(de(e))return!1;var n=typeof e;return!("number"!=n&&"symbol"!=n&&"boolean"!=n&&null!=e&&!Te(e))||je.test(e)||!Ee.test(e)||null!=t&&e in Object(t)}(e,t)?[e]:tt(function(e){return null==e?"":function e(t){if("string"==typeof t)return t;if(de(t))return function(e,t){for(var n=-1,r=null==e?0:e.length,o=Array(r);++n<r;)o[n]=t(e[n],n,e);return o}(t,e)+"";if(Te(t))return rt?rt.call(t):"";var n=t+"";return"0"==n&&1/t==-1/0?"-0":n}(e)}(e))},at=function(e){if("string"==typeof e||Te(e))return e;var t=e+"";return"0"==t&&1/e==-1/0?"-0":t},it=function(){try{var e=De(Object,"defineProperty");return e({},"",{}),e}catch(e){}}(),st=function(e,t,n){"__proto__"==t&&it?it(e,t,{configurable:!0,enumerable:!0,value:n,writable:!0}):e[t]=n},ct=Object.prototype.hasOwnProperty,ut=function(e,t,n){var r=e[t];ct.call(e,t)&&qe(r,n)&&(void 0!==n||t in e)||st(e,t,n)},pt=/^(?:0|[1-9]\d*)$/,lt=function(e,t){var n=typeof e;return!!(t=null==t?9007199254740991:t)&&("number"==n||"symbol"!=n&&pt.test(e))&&e>-1&&e%1==0&&e<t};class dt extends Error{constructor(e){super(e),Object.setPrototypeOf(this,dt.prototype)}}var ft="undefined"!=typeof Symbol&&Symbol,ht="Function.prototype.bind called on incompatible ",yt=Array.prototype.slice,mt=Object.prototype.toString,gt=Function.prototype.bind||function(e){var t=this;if("function"!=typeof t||"[object Function]"!==mt.call(t))throw new TypeError(ht+t);for(var n,r=yt.call(arguments,1),o=function(){if(this instanceof n){var o=t.apply(this,r.concat(yt.call(arguments)));return Object(o)===o?o:this}return t.apply(e,r.concat(yt.call(arguments)))},a=Math.max(0,t.length-r.length),i=[],s=0;s<a;s++)i.push("$"+s);if(n=Function("binder","return function ("+i.join(",")+"){ return binder.apply(this,arguments); }")(o),t.prototype){var c=function(){};c.prototype=t.prototype,n.prototype=new c,c.prototype=null}return n},bt=gt.call(Function.call,Object.prototype.hasOwnProperty),vt=SyntaxError,xt=Function,wt=TypeError,St=function(e){try{return xt('"use strict"; return ('+e+").constructor;")()}catch(e){}},kt=Object.getOwnPropertyDescriptor;if(kt)try{kt({},"")}catch(e){kt=null}var Ct=function(){throw new wt},Tt=kt?function(){try{return Ct}catch(e){try{return kt(arguments,"callee").get}catch(e){return Ct}}}():Ct,Et="function"==typeof ft&&"function"==typeof Symbol&&"symbol"==typeof ft("foo")&&"symbol"==typeof Symbol("bar")&&function(){if("function"!=typeof Symbol||"function"!=typeof Object.getOwnPropertySymbols)return!1;if("symbol"==typeof Symbol.iterator)return!0;var e={},t=Symbol("test"),n=Object(t);if("string"==typeof t)return!1;if("[object Symbol]"!==Object.prototype.toString.call(t))return!1;if("[object Symbol]"!==Object.prototype.toString.call(n))return!1;for(t in e[t]=42,e)return!1;if("function"==typeof Object.keys&&0!==Object.keys(e).length)return!1;if("function"==typeof Object.getOwnPropertyNames&&0!==Object.getOwnPropertyNames(e).length)return!1;var r=Object.getOwnPropertySymbols(e);if(1!==r.length||r[0]!==t)return!1;if(!Object.prototype.propertyIsEnumerable.call(e,t))return!1;if("function"==typeof Object.getOwnPropertyDescriptor){var o=Object.getOwnPropertyDescriptor(e,t);if(42!==o.value||!0!==o.enumerable)return!1}return!0}(),jt=Object.getPrototypeOf||function(e){return e.__proto__},Ot={},Rt="undefined"==typeof Uint8Array?void 0:jt(Uint8Array),At={"%AggregateError%":"undefined"==typeof AggregateError?void 0:AggregateError,"%Array%":Array,"%ArrayBuffer%":"undefined"==typeof ArrayBuffer?void 0:ArrayBuffer,"%ArrayIteratorPrototype%":Et?jt([][Symbol.iterator]()):void 0,"%AsyncFromSyncIteratorPrototype%":void 0,"%AsyncFunction%":Ot,"%AsyncGenerator%":Ot,"%AsyncGeneratorFunction%":Ot,"%AsyncIteratorPrototype%":Ot,"%Atomics%":"undefined"==typeof Atomics?void 0:Atomics,"%BigInt%":"undefined"==typeof BigInt?void 0:BigInt,"%Boolean%":Boolean,"%DataView%":"undefined"==typeof DataView?void 0:DataView,"%Date%":Date,"%decodeURI%":decodeURI,"%decodeURIComponent%":decodeURIComponent,"%encodeURI%":encodeURI,"%encodeURIComponent%":encodeURIComponent,"%Error%":Error,"%eval%":eval,"%EvalError%":EvalError,"%Float32Array%":"undefined"==typeof Float32Array?void 0:Float32Array,"%Float64Array%":"undefined"==typeof Float64Array?void 0:Float64Array,"%FinalizationRegistry%":"undefined"==typeof FinalizationRegistry?void 0:FinalizationRegistry,"%Function%":xt,"%GeneratorFunction%":Ot,"%Int8Array%":"undefined"==typeof Int8Array?void 0:Int8Array,"%Int16Array%":"undefined"==typeof Int16Array?void 0:Int16Array,"%Int32Array%":"undefined"==typeof Int32Array?void 0:Int32Array,"%isFinite%":isFinite,"%isNaN%":isNaN,"%IteratorPrototype%":Et?jt(jt([][Symbol.iterator]())):void 0,"%JSON%":"object"==typeof JSON?JSON:void 0,"%Map%":"undefined"==typeof Map?void 0:Map,"%MapIteratorPrototype%":"undefined"!=typeof Map&&Et?jt((new Map)[Symbol.iterator]()):void 0,"%Math%":Math,"%Number%":Number,"%Object%":Object,"%parseFloat%":parseFloat,"%parseInt%":parseInt,"%Promise%":"undefined"==typeof Promise?void 0:Promise,"%Proxy%":"undefined"==typeof Proxy?void 0:Proxy,"%RangeError%":RangeError,"%ReferenceError%":ReferenceError,"%Reflect%":"undefined"==typeof Reflect?void 0:Reflect,"%RegExp%":RegExp,"%Set%":"undefined"==typeof Set?void 0:Set,"%SetIteratorPrototype%":"undefined"!=typeof Set&&Et?jt((new Set)[Symbol.iterator]()):void 0,"%SharedArrayBuffer%":"undefined"==typeof SharedArrayBuffer?void 0:SharedArrayBuffer,"%String%":String,"%StringIteratorPrototype%":Et?jt(""[Symbol.iterator]()):void 0,"%Symbol%":Et?Symbol:void 0,"%SyntaxError%":vt,"%ThrowTypeError%":Tt,"%TypedArray%":Rt,"%TypeError%":wt,"%Uint8Array%":"undefined"==typeof Uint8Array?void 0:Uint8Array,"%Uint8ClampedArray%":"undefined"==typeof Uint8ClampedArray?void 0:Uint8ClampedArray,"%Uint16Array%":"undefined"==typeof Uint16Array?void 0:Uint16Array,"%Uint32Array%":"undefined"==typeof Uint32Array?void 0:Uint32Array,"%URIError%":URIError,"%WeakMap%":"undefined"==typeof WeakMap?void 0:WeakMap,"%WeakRef%":"undefined"==typeof WeakRef?void 0:WeakRef,"%WeakSet%":"undefined"==typeof WeakSet?void 0:WeakSet},Pt={"%ArrayBufferPrototype%":["ArrayBuffer","prototype"],"%ArrayPrototype%":["Array","prototype"],"%ArrayProto_entries%":["Array","prototype","entries"],"%ArrayProto_forEach%":["Array","prototype","forEach"],"%ArrayProto_keys%":["Array","prototype","keys"],"%ArrayProto_values%":["Array","prototype","values"],"%AsyncFunctionPrototype%":["AsyncFunction","prototype"],"%AsyncGenerator%":["AsyncGeneratorFunction","prototype"],"%AsyncGeneratorPrototype%":["AsyncGeneratorFunction","prototype","prototype"],"%BooleanPrototype%":["Boolean","prototype"],"%DataViewPrototype%":["DataView","prototype"],"%DatePrototype%":["Date","prototype"],"%ErrorPrototype%":["Error","prototype"],"%EvalErrorPrototype%":["EvalError","prototype"],"%Float32ArrayPrototype%":["Float32Array","prototype"],"%Float64ArrayPrototype%":["Float64Array","prototype"],"%FunctionPrototype%":["Function","prototype"],"%Generator%":["GeneratorFunction","prototype"],"%GeneratorPrototype%":["GeneratorFunction","prototype","prototype"],"%Int8ArrayPrototype%":["Int8Array","prototype"],"%Int16ArrayPrototype%":["Int16Array","prototype"],"%Int32ArrayPrototype%":["Int32Array","prototype"],"%JSONParse%":["JSON","parse"],"%JSONStringify%":["JSON","stringify"],"%MapPrototype%":["Map","prototype"],"%NumberPrototype%":["Number","prototype"],"%ObjectPrototype%":["Object","prototype"],"%ObjProto_toString%":["Object","prototype","toString"],"%ObjProto_valueOf%":["Object","prototype","valueOf"],"%PromisePrototype%":["Promise","prototype"],"%PromiseProto_then%":["Promise","prototype","then"],"%Promise_all%":["Promise","all"],"%Promise_reject%":["Promise","reject"],"%Promise_resolve%":["Promise","resolve"],"%RangeErrorPrototype%":["RangeError","prototype"],"%ReferenceErrorPrototype%":["ReferenceError","prototype"],"%RegExpPrototype%":["RegExp","prototype"],"%SetPrototype%":["Set","prototype"],"%SharedArrayBufferPrototype%":["SharedArrayBuffer","prototype"],"%StringPrototype%":["String","prototype"],"%SymbolPrototype%":["Symbol","prototype"],"%SyntaxErrorPrototype%":["SyntaxError","prototype"],"%TypedArrayPrototype%":["TypedArray","prototype"],"%TypeErrorPrototype%":["TypeError","prototype"],"%Uint8ArrayPrototype%":["Uint8Array","prototype"],"%Uint8ClampedArrayPrototype%":["Uint8ClampedArray","prototype"],"%Uint16ArrayPrototype%":["Uint16Array","prototype"],"%Uint32ArrayPrototype%":["Uint32Array","prototype"],"%URIErrorPrototype%":["URIError","prototype"],"%WeakMapPrototype%":["WeakMap","prototype"],"%WeakSetPrototype%":["WeakSet","prototype"]},Bt=gt.call(Function.call,Array.prototype.concat),_t=gt.call(Function.apply,Array.prototype.splice),It=gt.call(Function.call,String.prototype.replace),Mt=gt.call(Function.call,String.prototype.slice),Dt=/[^%.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|%$))/g,Ut=/\\(\\)?/g,Ft=function(e){var t=Mt(e,0,1),n=Mt(e,-1);if("%"===t&&"%"!==n)throw new vt("invalid intrinsic syntax, expected closing `%`");if("%"===n&&"%"!==t)throw new vt("invalid intrinsic syntax, expected opening `%`");var r=[];return It(e,Dt,(function(e,t,n,o){r[r.length]=n?It(o,Ut,"$1"):t||e})),r},Nt=function(e,t){var n,r=e;if(bt(Pt,r)&&(r="%"+(n=Pt[r])[0]+"%"),bt(At,r)){var o=At[r];if(o===Ot&&(o=function e(t){var n;if("%AsyncFunction%"===t)n=St("async function () {}");else if("%GeneratorFunction%"===t)n=St("function* () {}");else if("%AsyncGeneratorFunction%"===t)n=St("async function* () {}");else if("%AsyncGenerator%"===t){var r=e("%AsyncGeneratorFunction%");r&&(n=r.prototype)}else if("%AsyncIteratorPrototype%"===t){var o=e("%AsyncGenerator%");o&&(n=jt(o.prototype))}return At[t]=n,n}(r)),void 0===o&&!t)throw new wt("intrinsic "+e+" exists, but is not available. Please file an issue!");return{alias:n,name:r,value:o}}throw new vt("intrinsic "+e+" does not exist!")},Lt=function(e,t){if("string"!=typeof e||0===e.length)throw new wt("intrinsic name must be a non-empty string");if(arguments.length>1&&"boolean"!=typeof t)throw new wt('"allowMissing" argument must be a boolean');var n=Ft(e),r=n.length>0?n[0]:"",o=Nt("%"+r+"%",t),a=o.name,i=o.value,s=!1,c=o.alias;c&&(r=c[0],_t(n,Bt([0,1],c)));for(var u=1,p=!0;u<n.length;u+=1){var l=n[u],d=Mt(l,0,1),f=Mt(l,-1);if(('"'===d||"'"===d||"`"===d||'"'===f||"'"===f||"`"===f)&&d!==f)throw new vt("property names with quotes must have matching quotes");if("constructor"!==l&&p||(s=!0),bt(At,a="%"+(r+="."+l)+"%"))i=At[a];else if(null!=i){if(!(l in i)){if(!t)throw new wt("base intrinsic for "+e+" exists, but the property is not available.");return}if(kt&&u+1>=n.length){var h=kt(i,l);i=(p=!!h)&&"get"in h&&!("originalValue"in h.get)?h.get:i[l]}else p=bt(i,l),i=i[l];p&&!s&&(At[a]=i)}}return i},zt=K((function(e){var t=Lt("%Function.prototype.apply%"),n=Lt("%Function.prototype.call%"),r=Lt("%Reflect.apply%",!0)||gt.call(n,t),o=Lt("%Object.getOwnPropertyDescriptor%",!0),a=Lt("%Object.defineProperty%",!0),i=Lt("%Math.max%");if(a)try{a({},"a",{value:1})}catch(e){a=null}e.exports=function(e){var t=r(gt,n,arguments);if(o&&a){var s=o(t,"length");s.configurable&&a(t,"length",{value:1+i(0,e.length-(arguments.length-1))})}return t};var s=function(){return r(gt,t,arguments)};a?a(e.exports,"apply",{value:s}):e.exports.apply=s})),qt=zt(Lt("String.prototype.indexOf")),Kt=function(e,t){var n=Lt(e,!!t);return"function"==typeof n&&qt(e,".prototype.")>-1?zt(n):n},Ht=H({__proto__:null,default:{}}),$t="function"==typeof Map&&Map.prototype,Gt=Object.getOwnPropertyDescriptor&&$t?Object.getOwnPropertyDescriptor(Map.prototype,"size"):null,Wt=$t&&Gt&&"function"==typeof Gt.get?Gt.get:null,Vt=$t&&Map.prototype.forEach,Jt="function"==typeof Set&&Set.prototype,Qt=Object.getOwnPropertyDescriptor&&Jt?Object.getOwnPropertyDescriptor(Set.prototype,"size"):null,Xt=Jt&&Qt&&"function"==typeof Qt.get?Qt.get:null,Zt=Jt&&Set.prototype.forEach,Yt="function"==typeof WeakMap&&WeakMap.prototype?WeakMap.prototype.has:null,en="function"==typeof WeakSet&&WeakSet.prototype?WeakSet.prototype.has:null,tn="function"==typeof WeakRef&&WeakRef.prototype?WeakRef.prototype.deref:null,nn=Boolean.prototype.valueOf,rn=Object.prototype.toString,on=Function.prototype.toString,an=String.prototype.match,sn=String.prototype.slice,cn=String.prototype.replace,un=String.prototype.toUpperCase,pn=String.prototype.toLowerCase,ln=RegExp.prototype.test,dn=Array.prototype.concat,fn=Array.prototype.join,hn=Array.prototype.slice,yn=Math.floor,mn="function"==typeof BigInt?BigInt.prototype.valueOf:null,gn=Object.getOwnPropertySymbols,bn="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?Symbol.prototype.toString:null,vn="function"==typeof Symbol&&"object"==typeof Symbol.iterator,xn="function"==typeof Symbol&&Symbol.toStringTag&&(Symbol,1)?Symbol.toStringTag:null,wn=Object.prototype.propertyIsEnumerable,Sn=("function"==typeof Reflect?Reflect.getPrototypeOf:Object.getPrototypeOf)||([].__proto__===Array.prototype?function(e){return e.__proto__}:null);function kn(e,t){if(Infinity===e||-Infinity===e||e!=e||e&&e>-1e3&&e<1e3||ln.call(/e/,t))return t;var n=/[0-9](?=(?:[0-9]{3})+(?![0-9]))/g;if("number"==typeof e){var r=e<0?-yn(-e):yn(e);if(r!==e){var o=String(r),a=sn.call(t,o.length+1);return cn.call(o,n,"$&_")+"."+cn.call(cn.call(a,/([0-9]{3})/g,"$&_"),/_$/,"")}}return cn.call(t,n,"$&_")}var Cn=Ht.custom,Tn=Cn&&Rn(Cn)?Cn:null;function En(e,t,n){var r="double"===(n.quoteStyle||t)?'"':"'";return r+e+r}function jn(e){return cn.call(String(e),/"/g,"&quot;")}function On(e){return!("[object Array]"!==Bn(e)||xn&&"object"==typeof e&&xn in e)}function Rn(e){if(vn)return e&&"object"==typeof e&&e instanceof Symbol;if("symbol"==typeof e)return!0;if(!e||"object"!=typeof e||!bn)return!1;try{return bn.call(e),!0}catch(e){}return!1}var An=Object.prototype.hasOwnProperty||function(e){return e in this};function Pn(e,t){return An.call(e,t)}function Bn(e){return rn.call(e)}function _n(e,t){if(e.indexOf)return e.indexOf(t);for(var n=0,r=e.length;n<r;n++)if(e[n]===t)return n;return-1}function In(e){var t=e.charCodeAt(0),n={8:"b",9:"t",10:"n",12:"f",13:"r"}[t];return n?"\\"+n:"\\x"+(t<16?"0":"")+un.call(t.toString(16))}function Mn(e){return"Object("+e+")"}function Dn(e){return e+" { ? }"}function Un(e,t,n,r){return e+" ("+t+") {"+(r?Fn(n,r):fn.call(n,", "))+"}"}function Fn(e,t){if(0===e.length)return"";var n="\n"+t.prev+t.base;return n+fn.call(e,","+n)+"\n"+t.prev}function Nn(e,t){var n=On(e),r=[];if(n){r.length=e.length;for(var o=0;o<e.length;o++)r[o]=Pn(e,o)?t(e[o],e):""}var a,i="function"==typeof gn?gn(e):[];if(vn){a={};for(var s=0;s<i.length;s++)a["$"+i[s]]=i[s]}for(var c in e)Pn(e,c)&&(n&&String(Number(c))===c&&c<e.length||vn&&a["$"+c]instanceof Symbol||(ln.call(/[^\w$]/,c)?r.push(t(c,e)+": "+t(e[c],e)):r.push(c+": "+t(e[c],e))));if("function"==typeof gn)for(var u=0;u<i.length;u++)wn.call(e,i[u])&&r.push("["+t(i[u])+"]: "+t(e[i[u]],e));return r}var Ln=Lt("%TypeError%"),zn=Lt("%WeakMap%",!0),qn=Lt("%Map%",!0),Kn=Kt("WeakMap.prototype.get",!0),Hn=Kt("WeakMap.prototype.set",!0),$n=Kt("WeakMap.prototype.has",!0),Gn=Kt("Map.prototype.get",!0),Wn=Kt("Map.prototype.set",!0),Vn=Kt("Map.prototype.has",!0),Jn=function(e,t){for(var n,r=e;null!==(n=r.next);r=n)if(n.key===t)return r.next=n.next,n.next=e.next,e.next=n,n},Qn=function(){var e,t,n,r={assert:function(e){if(!r.has(e))throw new Ln("Side channel does not contain "+function e(t,n,r,o){var a=n||{};if(Pn(a,"quoteStyle")&&"single"!==a.quoteStyle&&"double"!==a.quoteStyle)throw new TypeError('option "quoteStyle" must be "single" or "double"');if(Pn(a,"maxStringLength")&&("number"==typeof a.maxStringLength?a.maxStringLength<0&&Infinity!==a.maxStringLength:null!==a.maxStringLength))throw new TypeError('option "maxStringLength", if provided, must be a positive integer, Infinity, or `null`');var i=!Pn(a,"customInspect")||a.customInspect;if("boolean"!=typeof i&&"symbol"!==i)throw new TypeError("option \"customInspect\", if provided, must be `true`, `false`, or `'symbol'`");if(Pn(a,"indent")&&null!==a.indent&&"\t"!==a.indent&&!(parseInt(a.indent,10)===a.indent&&a.indent>0))throw new TypeError('option "indent" must be "\\t", an integer > 0, or `null`');if(Pn(a,"numericSeparator")&&"boolean"!=typeof a.numericSeparator)throw new TypeError('option "numericSeparator", if provided, must be `true` or `false`');var s=a.numericSeparator;if(void 0===t)return"undefined";if(null===t)return"null";if("boolean"==typeof t)return t?"true":"false";if("string"==typeof t)return function e(t,n){if(t.length>n.maxStringLength){var r=t.length-n.maxStringLength,o="... "+r+" more character"+(r>1?"s":"");return e(sn.call(t,0,n.maxStringLength),n)+o}return En(cn.call(cn.call(t,/(['\\])/g,"\\$1"),/[\x00-\x1f]/g,In),"single",n)}(t,a);if("number"==typeof t){if(0===t)return Infinity/t>0?"0":"-0";var c=String(t);return s?kn(t,c):c}if("bigint"==typeof t){var u=String(t)+"n";return s?kn(t,u):u}var p=void 0===a.depth?5:a.depth;if(void 0===r&&(r=0),r>=p&&p>0&&"object"==typeof t)return On(t)?"[Array]":"[Object]";var l,d=function(e,t){var n;if("\t"===e.indent)n="\t";else{if(!("number"==typeof e.indent&&e.indent>0))return null;n=fn.call(Array(e.indent+1)," ")}return{base:n,prev:fn.call(Array(t+1),n)}}(a,r);if(void 0===o)o=[];else if(_n(o,t)>=0)return"[Circular]";function f(t,n,i){if(n&&(o=hn.call(o)).push(n),i){var s={depth:a.depth};return Pn(a,"quoteStyle")&&(s.quoteStyle=a.quoteStyle),e(t,s,r+1,o)}return e(t,a,r+1,o)}if("function"==typeof t){var h=function(e){if(e.name)return e.name;var t=an.call(on.call(e),/^function\s*([\w$]+)/);return t?t[1]:null}(t),y=Nn(t,f);return"[Function"+(h?": "+h:" (anonymous)")+"]"+(y.length>0?" { "+fn.call(y,", ")+" }":"")}if(Rn(t)){var m=vn?cn.call(String(t),/^(Symbol\(.*\))_[^)]*$/,"$1"):bn.call(t);return"object"!=typeof t||vn?m:Mn(m)}if((l=t)&&"object"==typeof l&&("undefined"!=typeof HTMLElement&&l instanceof HTMLElement||"string"==typeof l.nodeName&&"function"==typeof l.getAttribute)){for(var g="<"+pn.call(String(t.nodeName)),b=t.attributes||[],v=0;v<b.length;v++)g+=" "+b[v].name+"="+En(jn(b[v].value),"double",a);return g+=">",t.childNodes&&t.childNodes.length&&(g+="..."),g+"</"+pn.call(String(t.nodeName))+">"}if(On(t)){if(0===t.length)return"[]";var x=Nn(t,f);return d&&!function(e){for(var t=0;t<e.length;t++)if(_n(e[t],"\n")>=0)return!1;return!0}(x)?"["+Fn(x,d)+"]":"[ "+fn.call(x,", ")+" ]"}if(function(e){return!("[object Error]"!==Bn(e)||xn&&"object"==typeof e&&xn in e)}(t)){var w=Nn(t,f);return"cause"in t&&!wn.call(t,"cause")?"{ ["+String(t)+"] "+fn.call(dn.call("[cause]: "+f(t.cause),w),", ")+" }":0===w.length?"["+String(t)+"]":"{ ["+String(t)+"] "+fn.call(w,", ")+" }"}if("object"==typeof t&&i){if(Tn&&"function"==typeof t[Tn])return t[Tn]();if("symbol"!==i&&"function"==typeof t.inspect)return t.inspect()}if(function(e){if(!Wt||!e||"object"!=typeof e)return!1;try{Wt.call(e);try{Xt.call(e)}catch(e){return!0}return e instanceof Map}catch(e){}return!1}(t)){var S=[];return Vt.call(t,(function(e,n){S.push(f(n,t,!0)+" => "+f(e,t))})),Un("Map",Wt.call(t),S,d)}if(function(e){if(!Xt||!e||"object"!=typeof e)return!1;try{Xt.call(e);try{Wt.call(e)}catch(e){return!0}return e instanceof Set}catch(e){}return!1}(t)){var k=[];return Zt.call(t,(function(e){k.push(f(e,t))})),Un("Set",Xt.call(t),k,d)}if(function(e){if(!Yt||!e||"object"!=typeof e)return!1;try{Yt.call(e,Yt);try{en.call(e,en)}catch(e){return!0}return e instanceof WeakMap}catch(e){}return!1}(t))return Dn("WeakMap");if(function(e){if(!en||!e||"object"!=typeof e)return!1;try{en.call(e,en);try{Yt.call(e,Yt)}catch(e){return!0}return e instanceof WeakSet}catch(e){}return!1}(t))return Dn("WeakSet");if(function(e){if(!tn||!e||"object"!=typeof e)return!1;try{return tn.call(e),!0}catch(e){}return!1}(t))return Dn("WeakRef");if(function(e){return!("[object Number]"!==Bn(e)||xn&&"object"==typeof e&&xn in e)}(t))return Mn(f(Number(t)));if(function(e){if(!e||"object"!=typeof e||!mn)return!1;try{return mn.call(e),!0}catch(e){}return!1}(t))return Mn(f(mn.call(t)));if(function(e){return!("[object Boolean]"!==Bn(e)||xn&&"object"==typeof e&&xn in e)}(t))return Mn(nn.call(t));if(function(e){return!("[object String]"!==Bn(e)||xn&&"object"==typeof e&&xn in e)}(t))return Mn(f(String(t)));if(!function(e){return!("[object Date]"!==Bn(e)||xn&&"object"==typeof e&&xn in e)}(t)&&!function(e){return!("[object RegExp]"!==Bn(e)||xn&&"object"==typeof e&&xn in e)}(t)){var C=Nn(t,f),T=Sn?Sn(t)===Object.prototype:t instanceof Object||t.constructor===Object,E=t instanceof Object?"":"null prototype",j=!T&&xn&&Object(t)===t&&xn in t?sn.call(Bn(t),8,-1):E?"Object":"",O=(T||"function"!=typeof t.constructor?"":t.constructor.name?t.constructor.name+" ":"")+(j||E?"["+fn.call(dn.call([],j||[],E||[]),": ")+"] ":"");return 0===C.length?O+"{}":d?O+"{"+Fn(C,d)+"}":O+"{ "+fn.call(C,", ")+" }"}return String(t)}(e))},get:function(r){if(zn&&r&&("object"==typeof r||"function"==typeof r)){if(e)return Kn(e,r)}else if(qn){if(t)return Gn(t,r)}else if(n)return function(e,t){var n=Jn(e,t);return n&&n.value}(n,r)},has:function(r){if(zn&&r&&("object"==typeof r||"function"==typeof r)){if(e)return $n(e,r)}else if(qn){if(t)return Vn(t,r)}else if(n)return function(e,t){return!!Jn(e,t)}(n,r);return!1},set:function(r,o){zn&&r&&("object"==typeof r||"function"==typeof r)?(e||(e=new zn),Hn(e,r,o)):qn?(t||(t=new qn),Wn(t,r,o)):(n||(n={key:{},next:null}),function(e,t,n){var r=Jn(e,t);r?r.value=n:e.next={key:t,next:e.next,value:n}}(n,r,o))}};return r},Xn=String.prototype.replace,Zn=/%20/g,Yn={default:"RFC3986",formatters:{RFC1738:function(e){return Xn.call(e,Zn,"+")},RFC3986:function(e){return String(e)}},RFC1738:"RFC1738",RFC3986:"RFC3986"},er=Object.prototype.hasOwnProperty,tr=Array.isArray,nr=function(){for(var e=[],t=0;t<256;++t)e.push("%"+((t<16?"0":"")+t.toString(16)).toUpperCase());return e}(),rr=function(e,t){for(var n=t&&t.plainObjects?Object.create(null):{},r=0;r<e.length;++r)void 0!==e[r]&&(n[r]=e[r]);return n},or={arrayToObject:rr,assign:function(e,t){return Object.keys(t).reduce((function(e,n){return e[n]=t[n],e}),e)},combine:function(e,t){return[].concat(e,t)},compact:function(e){for(var t=[{obj:{o:e},prop:"o"}],n=[],r=0;r<t.length;++r)for(var o=t[r],a=o.obj[o.prop],i=Object.keys(a),s=0;s<i.length;++s){var c=i[s],u=a[c];"object"==typeof u&&null!==u&&-1===n.indexOf(u)&&(t.push({obj:a,prop:c}),n.push(u))}return function(e){for(;e.length>1;){var t=e.pop(),n=t.obj[t.prop];if(tr(n)){for(var r=[],o=0;o<n.length;++o)void 0!==n[o]&&r.push(n[o]);t.obj[t.prop]=r}}}(t),e},decode:function(e,t,n){var r=e.replace(/\+/g," ");if("iso-8859-1"===n)return r.replace(/%[0-9a-f]{2}/gi,unescape);try{return decodeURIComponent(r)}catch(e){return r}},encode:function(e,t,n,r,o){if(0===e.length)return e;var a=e;if("symbol"==typeof e?a=Symbol.prototype.toString.call(e):"string"!=typeof e&&(a=String(e)),"iso-8859-1"===n)return escape(a).replace(/%u[0-9a-f]{4}/gi,(function(e){return"%26%23"+parseInt(e.slice(2),16)+"%3B"}));for(var i="",s=0;s<a.length;++s){var c=a.charCodeAt(s);45===c||46===c||95===c||126===c||c>=48&&c<=57||c>=65&&c<=90||c>=97&&c<=122||o===Yn.RFC1738&&(40===c||41===c)?i+=a.charAt(s):c<128?i+=nr[c]:c<2048?i+=nr[192|c>>6]+nr[128|63&c]:c<55296||c>=57344?i+=nr[224|c>>12]+nr[128|c>>6&63]+nr[128|63&c]:(c=65536+((1023&c)<<10|1023&a.charCodeAt(s+=1)),i+=nr[240|c>>18]+nr[128|c>>12&63]+nr[128|c>>6&63]+nr[128|63&c])}return i},isBuffer:function(e){return!(!e||"object"!=typeof e||!(e.constructor&&e.constructor.isBuffer&&e.constructor.isBuffer(e)))},isRegExp:function(e){return"[object RegExp]"===Object.prototype.toString.call(e)},maybeMap:function(e,t){if(tr(e)){for(var n=[],r=0;r<e.length;r+=1)n.push(t(e[r]));return n}return t(e)},merge:function e(t,n,r){if(!n)return t;if("object"!=typeof n){if(tr(t))t.push(n);else{if(!t||"object"!=typeof t)return[t,n];(r&&(r.plainObjects||r.allowPrototypes)||!er.call(Object.prototype,n))&&(t[n]=!0)}return t}if(!t||"object"!=typeof t)return[t].concat(n);var o=t;return tr(t)&&!tr(n)&&(o=rr(t,r)),tr(t)&&tr(n)?(n.forEach((function(n,o){if(er.call(t,o)){var a=t[o];a&&"object"==typeof a&&n&&"object"==typeof n?t[o]=e(a,n,r):t.push(n)}else t[o]=n})),t):Object.keys(n).reduce((function(t,o){var a=n[o];return t[o]=er.call(t,o)?e(t[o],a,r):a,t}),o)}},ar=function(e,t){return e+"["+t+"]"},ir=Array.isArray,sr=Array.prototype.push,cr=function(e,t){sr.apply(e,ir(t)?t:[t])},ur=Date.prototype.toISOString,pr=Yn.default,lr={addQueryPrefix:!1,allowDots:!1,charset:"utf-8",charsetSentinel:!1,delimiter:"&",encode:!0,encoder:or.encode,encodeValuesOnly:!1,format:pr,formatter:Yn.formatters[pr],indices:!1,serializeDate:function(e){return ur.call(e)},skipNulls:!1,strictNullHandling:!1},dr={},fr=function e(t,n,r,o,a,i,s,c,u,p,l,d,f,h,y,m){for(var g,b=t,v=m,x=0,w=!1;void 0!==(v=v.get(dr))&&!w;){var S=v.get(t);if(x+=1,void 0!==S){if(S===x)throw new RangeError("Cyclic object value");w=!0}void 0===v.get(dr)&&(x=0)}if("function"==typeof c?b=c(n,b):b instanceof Date?b=l(b):"comma"===r&&ir(b)&&(b=or.maybeMap(b,(function(e){return e instanceof Date?l(e):e}))),null===b){if(a)return s&&!h?s(n,lr.encoder,y,"key",d):n;b=""}if("string"==typeof(g=b)||"number"==typeof g||"boolean"==typeof g||"symbol"==typeof g||"bigint"==typeof g||or.isBuffer(b))return s?[f(h?n:s(n,lr.encoder,y,"key",d))+"="+f(s(b,lr.encoder,y,"value",d))]:[f(n)+"="+f(String(b))];var k,C=[];if(void 0===b)return C;if("comma"===r&&ir(b))h&&s&&(b=or.maybeMap(b,s)),k=[{value:b.length>0?b.join(",")||null:void 0}];else if(ir(c))k=c;else{var T=Object.keys(b);k=u?T.sort(u):T}for(var E=o&&ir(b)&&1===b.length?n+"[]":n,j=0;j<k.length;++j){var O=k[j],R="object"==typeof O&&void 0!==O.value?O.value:b[O];if(!i||null!==R){var A=ir(b)?"function"==typeof r?r(E,O):E:E+(p?"."+O:"["+O+"]");m.set(t,x);var P=Qn();P.set(dr,m),cr(C,e(R,A,r,o,a,i,"comma"===r&&h&&ir(b)?null:s,c,u,p,l,d,f,h,y,P))}}return C};const hr=e=>t=>{if(null==e||"object"!=typeof e)return;const n=void 0===(o=null==(r=e)?void 0:function(e,t){for(var n=0,r=(t=ot(t,e)).length;null!=e&&n<r;)e=e[at(t[n++])];return n&&n==r?e:void 0}(r,t))?void 0:o;var r,o;Array.isArray(n)||function(e,t,n){null==e||function(e,t,n,r){if(!Oe(e))return e;for(var o=-1,a=(t=ot(t,e)).length,i=a-1,s=e;null!=s&&++o<a;){var c=at(t[o]),u=n;if("__proto__"===c||"constructor"===c||"prototype"===c)return e;if(o!=i){var p=s[c];void 0===(u=void 0)&&(u=Oe(p)?p:lt(t[o+1])?[]:{})}ut(s,c,u),s=s[c]}}(e,t,n)}(e,t,null==n?[]:[n])},yr=e=>{const t=n=>Array.isArray(n)?n.map(e=>t(e)):"string"==typeof n?e(n):"object"==typeof n&&null!=n?Object.keys(n).reduce((e,r)=>(e[t(r)]=n[r],e),{}):n;return t},mr=yr(e=>e.replace(/[A-Z]/g,"-$&").toLowerCase()),gr=yr(e=>e[0].toUpperCase()+e.slice(1)),br=e=>{const t=[];return Object.keys(e).sort().forEach(n=>{t.push(`${encodeURIComponent(n)}=${encodeURIComponent(e[n])}`)}),t.join("&")},vr=e=>{const t=e||{},n={};Object.keys(t).forEach(e=>{null!=t[e]&&(n[e]=t[e])});const r={};return Object.keys(n).forEach(e=>{const t=e.toLowerCase();r[t]=n[e]}),r},xr=e=>{var t;return"string"==typeof e&&(e={url:e}),e&&null==(null==(t=e)?void 0:t.needProxyParams)&&(e.needProxyParams=!0),e};async function wr(e){try{return[null,await e]}catch(e){return[e,null]}}function Sr(e){return"undefined"!=typeof Blob&&e instanceof Blob}function kr(e){return"undefined"!=typeof Buffer&&e instanceof Buffer}function Cr(e){return e?Object.keys(e).map(t=>{const n=""+e[t];return`${encodeURIComponent(t)}=${encodeURIComponent(n)}`}).join("&"):""}function Tr(e){return e instanceof dt}const Er=e=>"string"==typeof e?e:e.toUTCString(),jr={projectName:"x-tos-project-name",encodingType:"encoding-type",cacheControl:"cache-control",contentDisposition:"content-disposition",contentLength:"content-length",contentMD5:"content-md5",contentSHA256:"x-tos-content-sha256",contentEncoding:"content-encoding",contentLanguage:"content-language",contentType:"content-type",expires:["expires",e=>e.toUTCString()],range:"range",ifMatch:"if-match",ifModifiedSince:["if-modified-since",Er],ifNoneMatch:"if-none-match",ifUnmodifiedSince:["if-unmodified-since",Er],acl:"x-tos-acl",grantFullControl:"x-tos-grant-full-control",grantRead:"x-tos-grant-read",grantReadAcp:"x-tos-grant-read-acp",grantWrite:"x-tos-grant-write",grantWriteAcp:"x-tos-grant-write-acp",serverSideEncryption:"x-tos-server-side-encryption",serverSideDataEncryption:"x-tos-server-side-data-encryption",ssecAlgorithm:"x-tos-server-side-encryption-customer-algorithm",ssecKey:"x-tos-server-side-encryption-customer-key",ssecKeyMD5:"x-tos-server-side-encryption-customer-key-md5",copySourceRange:"x-tos-copy-source-range",copySourceIfMatch:"x-tos-copy-source-if-match",copySourceIfModifiedSince:["x-tos-copy-source-if-modified-since",Er],copySourceIfNoneMatch:"x-tos-copy-source-if-none-match",copySourceIfUnmodifiedSince:"x-tos-copy-source-if-unmodified-since",copySourceSSECAlgorithm:"x-tos-copy-source-server-side-encryption-customer-algorithm",copySourceSSECKey:"x-tos-copy-source-server-side-encryption-customer-key",copySourceSSECKeyMD5:"x-tos-copy-source-server-side-encryption-customer-key-MD5",metadataDirective:"x-tos-metadata-directive",meta:e=>Object.keys(e).reduce((t,n)=>(t["x-tos-meta-"+n]=""+e[n],t),{}),websiteRedirectLocation:"x-tos-website-redirect-location",storageClass:"x-tos-storage-class",azRedundancy:"x-tos-az-redundancy",trafficLimit:"x-tos-traffic-limit",callback:"x-tos-callback",callbackVar:"x-tos-callback-var",allowSameActionOverlap:["x-tos-allow-same-action-overlap",e=>String(e)],symLinkTargetKey:"x-tos-symlink-target",symLinkTargetBucket:"x-tos-symlink-bucket",forbidOverwrite:"x-tos-forbid-overwrite",bucketType:"x-tos-bucket-type",recursiveMkdir:"x-tos-recursive-mkdir"},Or={versionId:"versionId",process:"x-tos-process",saveBucket:"x-tos-save-bucket",saveObject:"x-tos-save-object",responseCacheControl:"response-cache-control",responseContentDisposition:"response-content-disposition",responseContentEncoding:"response-content-encoding",responseContentLanguage:"response-content-language",responseContentType:"response-content-type",responseExpires:["response-expires",e=>e.toUTCString()]};function Rr(e,t){if(!t.length)return;const n=e.headers||{};function r(e,t){null==n[e]&&(n[e]=t)}e.headers=n,t.forEach(t=>{const n=jr[t];if(!n)throw new pe(`\`${t}\` isn't in keys of \`requestHeadersMap\``);const o=e[t];if(null==o)return;if("string"==typeof n)return r(n,""+o);if(Array.isArray(n))return r(n[0],n[1](o));const a=n(o);Object.entries(a).forEach(([e,t])=>{r(e,t)})})}const Ar=e=>function(e,t){var n,r=e,o=lr;"function"==typeof o.filter?r=(0,o.filter)("",r):ir(o.filter)&&(n=o.filter);var a=[];if("object"!=typeof r||null===r)return"";var i=ar,s="comma"===i&&void 0;n||(n=Object.keys(r)),o.sort&&n.sort(o.sort);for(var c=Qn(),u=0;u<n.length;++u){var p=n[u];o.skipNulls&&null===r[p]||cr(a,fr(r[p],p,i,s,o.strictNullHandling,o.skipNulls,o.encode?o.encoder:null,o.filter,o.sort,o.allowDots,o.serializeDate,o.format,o.formatter,o.encodeValuesOnly,o.charset,c))}var l=a.join(o.delimiter),d=!0===o.addQueryPrefix?"?":"";return o.charsetSentinel&&(d+="iso-8859-1"===o.charset?"utf8=%26%2310003%3B&":"utf8=%E2%9C%93&"),l.length>0?d+l:""}(e);function Pr(e,t){return{data:e,statusCode:t.statusCode,headers:t.headers,requestId:t.requestId,id2:t.id2}}function Br(e,t){const n=t["x-tos-hash-crc64ecma"];if(null==n)return void console.warn("No x-tos-hash-crc64ecma in response's headers, please see https://www.volcengine.com/docs/6349/127737 to add `x-tos-hash-crc64ecma` to Expose-Headers field.");const r="string"==typeof e?e:e.getCrc64();if(r!==n)throw new pe(`validate file crc64 failed. Expect crc64 ${n}, actual crc64 ${r}. Please try again.`)}var _r;!function(e){e.LastModified="last-modified",e.ContentLength="content-length",e.AcceptEncoding="accept-encoding",e.ContentEncoding="content-encoding",e.ContentMD5="content-md5",e.TosRawContentLength="x-tos-raw-content-length",e.TosTrailer="x-tos-trailer",e.TosHashCrc64ecma="x-tos-hash-crc64ecma",e.TosContentSha256="x-tos-content-sha256",e.TosDecodedContentLength="x-tos-decoded-content-length",e.TosEc="x-tos-ec",e.TosRequestId="x-tos-request-id"}(_r||(_r={}));const Ir=e=>{let t=Promise.resolve();return async()=>(t=t.then(()=>e()),t)},Mr=(e,t)=>{e&&"destroy"in e&&"function"==typeof e.destroy&&"destroyed"in e&&!e.destroyed&&e.destroy(t)};async function Dr(e){e=this.normalizeObjectInput(e);const t=vr(e.headers);return e.headers=t,Rr(e,["encodingType","cacheControl","contentDisposition","contentEncoding","contentLanguage","contentType","expires","acl","grantFullControl","grantRead","grantReadAcp","grantWriteAcp","ssecAlgorithm","ssecKey","ssecKeyMD5","serverSideEncryption","serverSideDataEncryption","meta","websiteRedirectLocation","storageClass","forbidOverwrite"]),this.setObjectContentTypeHeader(e,t),this._fetchObject(e,"POST",{uploads:""},t,"")}const Ur=(e,t,n=!1)=>{let r=t;t<5242880&&(r=5242880,n&&console.warn(`partSize has been set to ${r}, because the partSize you provided is less than the minimal size of multipart`));const o=Math.ceil(e/1e4);return t<o&&(r=o,n&&console.warn(`partSize has been set to ${r}, because the partSize you provided causes the number of part excesses 10,000`)),r};async function Fr(e){const{uploadId:t,...n}=e,r=await this._fetchObject(e,"GET",{uploadId:t,...mr(n)},{});return hr(r.data)("Parts"),r}const Nr={"3gp":"video/3gpp","7z":"application/x-7z-compressed",abw:"application/x-abiword",ai:"application/postscript",aif:"audio/x-aiff",aifc:"audio/x-aiff",aiff:"audio/x-aiff",alc:"chemical/x-alchemy",amr:"audio/amr",anx:"application/annodex",apk:"application/vnd.android.package-archive",appcache:"text/cache-manifest",art:"image/x-jg",asc:"text/plain",asf:"video/x-ms-asf",aso:"chemical/x-ncbi-asn1-binary",asx:"video/x-ms-asf",atom:"application/atom+xml",atomcat:"application/atomcat+xml",atomsrv:"application/atomserv+xml",au:"audio/basic",avi:"video/x-msvideo",awb:"audio/amr-wb",axa:"audio/annodex",axv:"video/annodex",b:"chemical/x-molconn-Z",bak:"application/x-trash",bat:"application/x-msdos-program",bcpio:"application/x-bcpio",bib:"text/x-bibtex",bin:"application/octet-stream",bmp:"image/x-ms-bmp",boo:"text/x-boo",book:"application/x-maker",brf:"text/plain",bsd:"chemical/x-crossfire",c:"text/x-csrc","c++":"text/x-c++src",c3d:"chemical/x-chem3d",cab:"application/x-cab",cac:"chemical/x-cache",cache:"chemical/x-cache",cap:"application/vnd.tcpdump.pcap",cascii:"chemical/x-cactvs-binary",cat:"application/vnd.ms-pki.seccat",cbin:"chemical/x-cactvs-binary",cbr:"application/x-cbr",cbz:"application/x-cbz",cc:"text/x-c++src",cda:"application/x-cdf",cdf:"application/x-cdf",cdr:"image/x-coreldraw",cdt:"image/x-coreldrawtemplate",cdx:"chemical/x-cdx",cdy:"application/vnd.cinderella",cef:"chemical/x-cxf",cer:"chemical/x-cerius",chm:"chemical/x-chemdraw",chrt:"application/x-kchart",cif:"chemical/x-cif",class:"application/java-vm",cls:"text/x-tex",cmdf:"chemical/x-cmdf",cml:"chemical/x-cml",cod:"application/vnd.rim.cod",com:"application/x-msdos-program",cpa:"chemical/x-compass",cpio:"application/x-cpio",cpp:"text/x-c++src",cpt:"application/mac-compactpro",cr2:"image/x-canon-cr2",crl:"application/x-pkcs7-crl",crt:"application/x-x509-ca-cert",crw:"image/x-canon-crw",csd:"audio/csound",csf:"chemical/x-cache-csf",csh:"application/x-csh",csm:"chemical/x-csml",csml:"chemical/x-csml",css:"text/css",csv:"text/csv",ctab:"chemical/x-cactvs-binary",ctx:"chemical/x-ctx",cu:"application/cu-seeme",cub:"chemical/x-gaussian-cube",cxf:"chemical/x-cxf",cxx:"text/x-c++src",d:"text/x-dsrc",davmount:"application/davmount+xml",dcm:"application/dicom",dcr:"application/x-director",ddeb:"application/vnd.debian.binary-package",dif:"video/dv",diff:"text/x-diff",dir:"application/x-director",djv:"image/vnd.djvu",djvu:"image/vnd.djvu",dl:"video/dl",dll:"application/x-msdos-program",dmg:"application/x-apple-diskimage",dms:"application/x-dms",doc:"application/msword",docm:"application/vnd.ms-word.document.macroEnabled.12",docx:"application/vnd.openxmlformats-officedocument.wordprocessingml.document",dot:"application/msword",dotm:"application/vnd.ms-word.template.macroEnabled.12",dotx:"application/vnd.openxmlformats-officedocument.wordprocessingml.template",dv:"video/dv",dvi:"application/x-dvi",dx:"chemical/x-jcamp-dx",dxr:"application/x-director",emb:"chemical/x-embl-dl-nucleotide",embl:"chemical/x-embl-dl-nucleotide",eml:"message/rfc822",eot:"application/vnd.ms-fontobject",eps:"application/postscript",eps2:"application/postscript",eps3:"application/postscript",epsf:"application/postscript",epsi:"application/postscript",erf:"image/x-epson-erf",es:"application/ecmascript",etx:"text/x-setext",exe:"application/x-msdos-program",ez:"application/andrew-inset",fb:"application/x-maker",fbdoc:"application/x-maker",fch:"chemical/x-gaussian-checkpoint",fchk:"chemical/x-gaussian-checkpoint",fig:"application/x-xfig",flac:"audio/flac",fli:"video/fli",flv:"video/x-flv",fm:"application/x-maker",frame:"application/x-maker",frm:"application/x-maker",gal:"chemical/x-gaussian-log",gam:"chemical/x-gamess-input",gamin:"chemical/x-gamess-input",gan:"application/x-ganttproject",gau:"chemical/x-gaussian-input",gcd:"text/x-pcs-gcd",gcf:"application/x-graphing-calculator",gcg:"chemical/x-gcg8-sequence",gen:"chemical/x-genbank",gf:"application/x-tex-gf",gif:"image/gif",gjc:"chemical/x-gaussian-input",gjf:"chemical/x-gaussian-input",gl:"video/gl",gnumeric:"application/x-gnumeric",gpt:"chemical/x-mopac-graph",gsf:"application/x-font",gsm:"audio/x-gsm",gtar:"application/x-gtar",gz:"application/gzip",h:"text/x-chdr","h++":"text/x-c++hdr",hdf:"application/x-hdf",hh:"text/x-c++hdr",hin:"chemical/x-hin",hpp:"text/x-c++hdr",hqx:"application/mac-binhex40",hs:"text/x-haskell",hta:"application/hta",htc:"text/x-component",htm:"text/html",html:"text/html",hwp:"application/x-hwp",hxx:"text/x-c++hdr",ica:"application/x-ica",ice:"x-conference/x-cooltalk",ico:"image/vnd.microsoft.icon",ics:"text/calendar",icz:"text/calendar",ief:"image/ief",iges:"model/iges",igs:"model/iges",iii:"application/x-iphone",info:"application/x-info",inp:"chemical/x-gamess-input",ins:"application/x-internet-signup",iso:"application/x-iso9660-image",isp:"application/x-internet-signup",ist:"chemical/x-isostar",istr:"chemical/x-isostar",jad:"text/vnd.sun.j2me.app-descriptor",jam:"application/x-jam",jar:"application/java-archive",java:"text/x-java",jdx:"chemical/x-jcamp-dx",jmz:"application/x-jmol",jng:"image/x-jng",jnlp:"application/x-java-jnlp-file",jp2:"image/jp2",jpe:"image/jpeg",jpeg:"image/jpeg",jpf:"image/jpx",jpg:"image/jpeg",jpg2:"image/jp2",jpm:"image/jpm",jpx:"image/jpx",js:"application/javascript",json:"application/json",kar:"audio/midi",key:"application/pgp-keys",kil:"application/x-killustrator",kin:"chemical/x-kinemage",kml:"application/vnd.google-earth.kml+xml",kmz:"application/vnd.google-earth.kmz",kpr:"application/x-kpresenter",kpt:"application/x-kpresenter",ksp:"application/x-kspread",kwd:"application/x-kword",kwt:"application/x-kword",latex:"application/x-latex",lha:"application/x-lha",lhs:"text/x-literate-haskell",lin:"application/bbolin",lsf:"video/x-la-asf",lsx:"video/x-la-asf",ltx:"text/x-tex",ly:"text/x-lilypond",lyx:"application/x-lyx",lzh:"application/x-lzh",lzx:"application/x-lzx",m3g:"application/m3g",m3u:"audio/x-mpegurl",m3u8:"application/x-mpegURL",m4a:"audio/mpeg",maker:"application/x-maker",man:"application/x-troff-man",mbox:"application/mbox",mcif:"chemical/x-mmcif",mcm:"chemical/x-macmolecule",mdb:"application/msaccess",me:"application/x-troff-me",mesh:"model/mesh",mid:"audio/midi",midi:"audio/midi",mif:"application/x-mif",mkv:"video/x-matroska",mm:"application/x-freemind",mmd:"chemical/x-macromodel-input",mmf:"application/vnd.smaf",mml:"text/mathml",mmod:"chemical/x-macromodel-input",mng:"video/x-mng",moc:"text/x-moc",mol:"chemical/x-mdl-molfile",mol2:"chemical/x-mol2",moo:"chemical/x-mopac-out",mop:"chemical/x-mopac-input",mopcrt:"chemical/x-mopac-input",mov:"video/quicktime",movie:"video/x-sgi-movie",mp2:"audio/mpeg",mp3:"audio/mpeg",mp4:"video/mp4",mpc:"chemical/x-mopac-input",mpe:"video/mpeg",mpeg:"video/mpeg",mpega:"audio/mpeg",mpg:"video/mpeg",mpga:"audio/mpeg",mph:"application/x-comsol",mpv:"video/x-matroska",ms:"application/x-troff-ms",msh:"model/mesh",msi:"application/x-msi",mvb:"chemical/x-mopac-vib",mxf:"application/mxf",mxu:"video/vnd.mpegurl",nb:"application/mathematica",nbp:"application/mathematica",nc:"application/x-netcdf",nef:"image/x-nikon-nef",nwc:"application/x-nwc",o:"application/x-object",oda:"application/oda",odb:"application/vnd.oasis.opendocument.database",odc:"application/vnd.oasis.opendocument.chart",odf:"application/vnd.oasis.opendocument.formula",odg:"application/vnd.oasis.opendocument.graphics",odi:"application/vnd.oasis.opendocument.image",odm:"application/vnd.oasis.opendocument.text-master",odp:"application/vnd.oasis.opendocument.presentation",ods:"application/vnd.oasis.opendocument.spreadsheet",odt:"application/vnd.oasis.opendocument.text",oga:"audio/ogg",ogg:"audio/ogg",ogv:"video/ogg",ogx:"application/ogg",old:"application/x-trash",one:"application/onenote",onepkg:"application/onenote",onetmp:"application/onenote",onetoc2:"application/onenote",opf:"application/oebps-package+xml",opus:"audio/ogg",orc:"audio/csound",orf:"image/x-olympus-orf",otf:"application/font-sfnt",otg:"application/vnd.oasis.opendocument.graphics-template",oth:"application/vnd.oasis.opendocument.text-web",otp:"application/vnd.oasis.opendocument.presentation-template",ots:"application/vnd.oasis.opendocument.spreadsheet-template",ott:"application/vnd.oasis.opendocument.text-template",oza:"application/x-oz-application",p:"text/x-pascal",p7r:"application/x-pkcs7-certreqresp",pac:"application/x-ns-proxy-autoconfig",pas:"text/x-pascal",pat:"image/x-coreldrawpattern",patch:"text/x-diff",pbm:"image/x-portable-bitmap",pcap:"application/vnd.tcpdump.pcap",pcf:"application/x-font-pcf","pcf.Z":"application/x-font-pcf",pcx:"image/pcx",pdb:"chemical/x-pdb",pdf:"application/pdf",pfa:"application/x-font",pfb:"application/x-font",pfr:"application/font-tdpfr",pgm:"image/x-portable-graymap",pgn:"application/x-chess-pgn",pgp:"application/pgp-encrypted",php:"#application/x-httpd-php",php3:"#application/x-httpd-php3",php3p:"#application/x-httpd-php3-preprocessed",php4:"#application/x-httpd-php4",php5:"#application/x-httpd-php5",phps:"#application/x-httpd-php-source",pht:"#application/x-httpd-php",phtml:"#application/x-httpd-php",pk:"application/x-tex-pk",pl:"text/x-perl",pls:"audio/x-scpls",pm:"text/x-perl",png:"image/png",pnm:"image/x-portable-anymap",pot:"text/plain",potm:"application/vnd.ms-powerpoint.template.macroEnabled.12",potx:"application/vnd.openxmlformats-officedocument.presentationml.template",ppam:"application/vnd.ms-powerpoint.addin.macroEnabled.12",ppm:"image/x-portable-pixmap",pps:"application/vnd.ms-powerpoint",ppsm:"application/vnd.ms-powerpoint.slideshow.macroEnabled.12",ppsx:"application/vnd.openxmlformats-officedocument.presentationml.slideshow",ppt:"application/vnd.ms-powerpoint",pptm:"application/vnd.ms-powerpoint.presentation.macroEnabled.12",pptx:"application/vnd.openxmlformats-officedocument.presentationml.presentation",prf:"application/pics-rules",prt:"chemical/x-ncbi-asn1-ascii",ps:"application/postscript",psd:"image/x-photoshop",py:"text/x-python",pyc:"application/x-python-code",pyo:"application/x-python-code",qgs:"application/x-qgis",qt:"video/quicktime",qtl:"application/x-quicktimeplayer",ra:"audio/x-pn-realaudio",ram:"audio/x-pn-realaudio",rar:"application/rar",ras:"image/x-cmu-raster",rb:"application/x-ruby",rd:"chemical/x-mdl-rdfile",rdf:"application/rdf+xml",rdp:"application/x-rdp",rgb:"image/x-rgb",rhtml:"#application/x-httpd-eruby",rm:"audio/x-pn-realaudio",roff:"application/x-troff",ros:"chemical/x-rosdal",rpm:"application/x-redhat-package-manager",rss:"application/x-rss+xml",rtf:"application/rtf",rtx:"text/richtext",rxn:"chemical/x-mdl-rxnfile",scala:"text/x-scala",sce:"application/x-scilab",sci:"application/x-scilab",sco:"audio/csound",scr:"application/x-silverlight",sct:"text/scriptlet",sd:"chemical/x-mdl-sdfile",sd2:"audio/x-sd2",sda:"application/vnd.stardivision.draw",sdc:"application/vnd.stardivision.calc",sdd:"application/vnd.stardivision.impress",sds:"application/vnd.stardivision.chart",sdw:"application/vnd.stardivision.writer",ser:"application/java-serialized-object",sfd:"application/vnd.font-fontforge-sfd",sfv:"text/x-sfv",sgf:"application/x-go-sgf",sgl:"application/vnd.stardivision.writer-global",sh:"application/x-sh",shar:"application/x-shar",shp:"application/x-qgis",shtml:"text/html",shx:"application/x-qgis",sid:"audio/prs.sid",sig:"application/pgp-signature",sik:"application/x-trash",silo:"model/mesh",sis:"application/vnd.symbian.install",sisx:"x-epoc/x-sisx-app",sit:"application/x-stuffit",sitx:"application/x-stuffit",skd:"application/x-koan",skm:"application/x-koan",skp:"application/x-koan",skt:"application/x-koan",sldm:"application/vnd.ms-powerpoint.slide.macroEnabled.12",sldx:"application/vnd.openxmlformats-officedocument.presentationml.slide",smi:"application/smil+xml",smil:"application/smil+xml",snd:"audio/basic",spc:"chemical/x-galactic-spc",spl:"application/x-futuresplash",spx:"audio/ogg",sql:"application/x-sql",src:"application/x-wais-source",srt:"text/plain",stc:"application/vnd.sun.xml.calc.template",std:"application/vnd.sun.xml.draw.template",sti:"application/vnd.sun.xml.impress.template",stw:"application/vnd.sun.xml.writer.template",sty:"text/x-tex",sv4cpio:"application/x-sv4cpio",sv4crc:"application/x-sv4crc",svg:"image/svg+xml",svgz:"image/svg+xml",sw:"chemical/x-swissprot",swf:"application/x-shockwave-flash",swfl:"application/x-shockwave-flash",sxc:"application/vnd.sun.xml.calc",sxd:"application/vnd.sun.xml.draw",sxg:"application/vnd.sun.xml.writer.global",sxi:"application/vnd.sun.xml.impress",sxm:"application/vnd.sun.xml.math",sxw:"application/vnd.sun.xml.writer",t:"application/x-troff",tar:"application/x-tar",taz:"application/x-gtar-compressed",tcl:"application/x-tcl",tex:"text/x-tex",texi:"application/x-texinfo",texinfo:"application/x-texinfo",text:"text/plain",tgf:"chemical/x-mdl-tgf",tgz:"application/x-gtar-compressed",thmx:"application/vnd.ms-officetheme",tif:"image/tiff",tiff:"image/tiff",tk:"text/x-tcl",tm:"text/texmacs",torrent:"application/x-bittorrent",tr:"application/x-troff",ts:"video/MP2T",tsp:"application/dsptype",tsv:"text/tab-separated-values",ttf:"application/font-sfnt",ttl:"text/turtle",txt:"text/plain",uls:"text/iuls",ustar:"application/x-ustar",val:"chemical/x-ncbi-asn1-binary",vcard:"text/vcard",vcd:"application/x-cdlink",vcf:"text/vcard",vcs:"text/x-vcalendar",vmd:"chemical/x-vmd",vms:"chemical/x-vamas-iso14976",vrm:"x-world/x-vrml",vrml:"model/vrml",vsd:"application/vnd.visio",vss:"application/vnd.visio",vst:"application/vnd.visio",vsw:"application/vnd.visio",wad:"application/x-doom",wasm:"application/wasm",wav:"audio/wav",wax:"audio/x-ms-wax",wbmp:"image/vnd.wap.wbmp",wbxml:"application/vnd.wap.wbxml",webm:"video/webm",wk:"application/x-123",wm:"video/x-ms-wm",wma:"audio/x-ms-wma",wmd:"application/x-ms-wmd",wml:"text/vnd.wap.wml",wmlc:"application/vnd.wap.wmlc",wmls:"text/vnd.wap.wmlscript",wmlsc:"application/vnd.wap.wmlscriptc",wmv:"video/x-ms-wmv",wmx:"video/x-ms-wmx",wmz:"application/x-ms-wmz",woff:"application/font-woff",wp5:"application/vnd.wordperfect5.1",wpd:"application/vnd.wordperfect",wrl:"model/vrml",wsc:"text/scriptlet",wvx:"video/x-ms-wvx",wz:"application/x-wingz",x3d:"model/x3d+xml",x3db:"model/x3d+binary",x3dv:"model/x3d+vrml",xbm:"image/x-xbitmap",xcf:"application/x-xcf",xcos:"application/x-scilab-xcos",xht:"application/xhtml+xml",xhtml:"application/xhtml+xml",xlam:"application/vnd.ms-excel.addin.macroEnabled.12",xlb:"application/vnd.ms-excel",xls:"application/vnd.ms-excel",xlsb:"application/vnd.ms-excel.sheet.binary.macroEnabled.12",xlsm:"application/vnd.ms-excel.sheet.macroEnabled.12",xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",xlt:"application/vnd.ms-excel",xltm:"application/vnd.ms-excel.template.macroEnabled.12",xltx:"application/vnd.openxmlformats-officedocument.spreadsheetml.template",xml:"application/xml",xpi:"application/x-xpinstall",xpm:"image/x-xpixmap",xsd:"application/xml",xsl:"application/xslt+xml",xslt:"application/xslt+xml",xspf:"application/xspf+xml",xtel:"chemical/x-xtel",xul:"application/vnd.mozilla.xul+xml",xwd:"image/x-xwindowdump",xyz:"chemical/x-xyz",xz:"application/x-xz",zip:"application/zip"};let Lr=null;Lr={__proto__:null,CRC:class{reset(){}async updateBlob(){throw new pe("Not implemented.(CRC may cause browser lag.)")}update(e){throw new pe("Not implemented.(CRC may cause browser lag.)")}}};const{combineCrc64:zr}=Lr;let qr=null;qr={__proto__:null,createDefaultRateLimiter:function(e,t){throw Error("no implemention in browser environment")},createRateLimiterStream:function(e){throw Error("no implemention in browser environment")}};const{createDefaultRateLimiter:Kr}=qr;var Hr;function $r(e){if(("string"==typeof e?e:e.key).length<1)throw new pe("invalid object name, the length must be greater than 1")}function Gr(e,t){if(kr(e))return e.length;if(Sr(e))return e.size;if(t&&t["content-length"]){const e=+t["content-length"];if(e>=0)return e}return null}async function Wr(e){const t=function({body:e}){return{body:e,makeRetryStream:void 0}}(e);return async function({body:e,beforeRetry:t,makeRetryStream:n}){return{body:e,beforeRetry:t,makeRetryStream:n}}(e={...e,...t})}function Vr(e,t){return`/${e}/${encodeURIComponent(t)}`}!function(e){e.HeaderRestore="x-tos-restore",e.HeaderRestoreExpiryDays="x-tos-restore-expiry-days",e.HeaderRestoreRequestDate="x-tos-restore-request-date",e.HeaderRestoreTier="x-tos-restore-tier",e.HeaderProjectName="x-tos-project-name",e.HeaderReplicationStatus="x-tos-replication-status"}(Hr||(Hr={}));const Jr=e=>{if(!e)return;const t=null==e?void 0:e[Hr.HeaderRestore];if(t){var n,r,o;const i=null!=(n=null==(r=(null!=t?t:"").split('",')[1])||null==r.split||null==(o=r.split("="))?void 0:o[1])?n:"",s='ongoing-request="true"'===(null==t?void 0:t.trim()),c={RestoreStatus:{OngoingRequest:s,ExpiryDate:i}};var a;return s&&(c.RestoreParam={ExpiryDays:e[Hr.HeaderRestoreExpiryDays]?Number(e[Hr.HeaderRestoreExpiryDays]):0,RequestDate:null!=(a=e[Hr.HeaderRestoreRequestDate])?a:"",Tier:e[Hr.HeaderRestoreTier]}),c}};var Qr;(Qr=e.DataTransferType||(e.DataTransferType={}))[Qr.Started=1]="Started",Qr[Qr.Rw=2]="Rw",Qr[Qr.Succeed=3]="Succeed",Qr[Qr.Failed=4]="Failed";var Xr=1e3,Zr=6e4,Yr=60*Zr,eo=24*Yr,to=function(e,t){t=t||{};var n=typeof e;if("string"===n&&e.length>0)return function(e){if(!((e=String(e)).length>100)){var t=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(e);if(t){var n=parseFloat(t[1]);switch((t[2]||"ms").toLowerCase()){case"years":case"year":case"yrs":case"yr":case"y":return 315576e5*n;case"weeks":case"week":case"w":return 6048e5*n;case"days":case"day":case"d":return n*eo;case"hours":case"hour":case"hrs":case"hr":case"h":return n*Yr;case"minutes":case"minute":case"mins":case"min":case"m":return n*Zr;case"seconds":case"second":case"secs":case"sec":case"s":return n*Xr;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return n;default:return}}}}(e);if("number"===n&&isFinite(e))return t.long?function(e){var t=Math.abs(e);return t>=eo?no(e,t,eo,"day"):t>=Yr?no(e,t,Yr,"hour"):t>=Zr?no(e,t,Zr,"minute"):t>=Xr?no(e,t,Xr,"second"):e+" ms"}(e):function(e){var t=Math.abs(e);return t>=eo?Math.round(e/eo)+"d":t>=Yr?Math.round(e/Yr)+"h":t>=Zr?Math.round(e/Zr)+"m":t>=Xr?Math.round(e/Xr)+"s":e+"ms"}(e);throw new Error("val is not a non-empty string or a valid number. val="+JSON.stringify(e))};function no(e,t,n,r){var o=t>=1.5*n;return Math.round(e/n)+" "+r+(o?"s":"")}const ro=K((function(e,t){t.formatArgs=function(t){if(t[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+t[0]+(this.useColors?"%c ":" ")+"+"+e.exports.humanize(this.diff),!this.useColors)return;const n="color: "+this.color;t.splice(1,0,n,"color: inherit");let r=0,o=0;t[0].replace(/%[a-zA-Z%]/g,e=>{"%%"!==e&&(r++,"%c"===e&&(o=r))}),t.splice(o,0,n)},t.save=function(e){try{e?t.storage.setItem("debug",e):t.storage.removeItem("debug")}catch(e){}},t.load=function(){let e;try{e=t.storage.getItem("debug")}catch(e){}return!e&&"undefined"!=typeof process&&"env"in process&&(e=process.env.DEBUG),e},t.useColors=function(){return!("undefined"==typeof window||!window.process||"renderer"!==window.process.type&&!window.process.__nwjs)||("undefined"==typeof navigator||!navigator.userAgent||!navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))&&("undefined"!=typeof document&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||"undefined"!=typeof window&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/)&&parseInt(RegExp.$1,10)>=31||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/))},t.storage=function(){try{return localStorage}catch(e){}}(),t.destroy=(()=>{let e=!1;return()=>{e||(e=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}})(),t.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"],t.log=console.debug||console.log||(()=>{}),e.exports=function(e){function t(e){let r,o,a,i=null;function s(...e){if(!s.enabled)return;const n=s,o=Number(new Date);n.diff=o-(r||o),n.prev=r,n.curr=o,r=o,e[0]=t.coerce(e[0]),"string"!=typeof e[0]&&e.unshift("%O");let a=0;e[0]=e[0].replace(/%([a-zA-Z%])/g,(r,o)=>{if("%%"===r)return"%";a++;const i=t.formatters[o];return"function"==typeof i&&(r=i.call(n,e[a]),e.splice(a,1),a--),r}),t.formatArgs.call(n,e),(n.log||t.log).apply(n,e)}return s.namespace=e,s.useColors=t.useColors(),s.color=t.selectColor(e),s.extend=n,s.destroy=t.destroy,Object.defineProperty(s,"enabled",{enumerable:!0,configurable:!1,get:()=>null!==i?i:(o!==t.namespaces&&(o=t.namespaces,a=t.enabled(e)),a),set:e=>{i=e}}),"function"==typeof t.init&&t.init(s),s}function n(e,n){const r=t(this.namespace+(void 0===n?":":n)+e);return r.log=this.log,r}function r(e){return e.toString().substring(2,e.toString().length-2).replace(/\.\*\?$/,"*")}return t.debug=t,t.default=t,t.coerce=function(e){return e instanceof Error?e.stack||e.message:e},t.disable=function(){const e=[...t.names.map(r),...t.skips.map(r).map(e=>"-"+e)].join(",");return t.enable(""),e},t.enable=function(e){let n;t.save(e),t.namespaces=e,t.names=[],t.skips=[];const r=("string"==typeof e?e:"").split(/[\s,]+/),o=r.length;for(n=0;n<o;n++)r[n]&&("-"===(e=r[n].replace(/\*/g,".*?"))[0]?t.skips.push(new RegExp("^"+e.slice(1)+"$")):t.names.push(new RegExp("^"+e+"$")))},t.enabled=function(e){if("*"===e[e.length-1])return!0;let n,r;for(n=0,r=t.skips.length;n<r;n++)if(t.skips[n].test(e))return!1;for(n=0,r=t.names.length;n<r;n++)if(t.names[n].test(e))return!0;return!1},t.humanize=to,t.destroy=function(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")},Object.keys(e).forEach(n=>{t[n]=e[n]}),t.names=[],t.skips=[],t.formatters={},t.selectColor=function(e){let n=0;for(let t=0;t<e.length;t++)n=(n<<5)-n+e.charCodeAt(t),n|=0;return t.colors[Math.abs(n)%t.colors.length]},t.enable(t.load()),t}(t);const{formatters:n}=e.exports;n.j=function(e){try{return JSON.stringify(e)}catch(e){return"[UnexpectedJSONParseError]: "+e.message}}}))("TOS"),oo=["content-length","user-agent","host"];var ao=K((function(e,t){var n;e.exports=n=n||function(e,t){var n;if("undefined"!=typeof window&&window.crypto&&(n=window.crypto),"undefined"!=typeof self&&self.crypto&&(n=self.crypto),"undefined"!=typeof globalThis&&globalThis.crypto&&(n=globalThis.crypto),!n&&"undefined"!=typeof window&&window.msCrypto&&(n=window.msCrypto),!n&&void 0!==q&&q.crypto&&(n=q.crypto),!n)try{n=Ht}catch(e){}var r=function(){if(n){if("function"==typeof n.getRandomValues)try{return n.getRandomValues(new Uint32Array(1))[0]}catch(e){}if("function"==typeof n.randomBytes)try{return n.randomBytes(4).readInt32LE()}catch(e){}}throw new Error("Native crypto module could not be used to get secure random number.")},o=Object.create||function(){function e(){}return function(t){var n;return e.prototype=t,n=new e,e.prototype=null,n}}(),a={},i=a.lib={},s=i.Base={extend:function(e){var t=o(this);return e&&t.mixIn(e),t.hasOwnProperty("init")&&this.init!==t.init||(t.init=function(){t.$super.init.apply(this,arguments)}),t.init.prototype=t,t.$super=this,t},create:function(){var e=this.extend();return e.init.apply(e,arguments),e},init:function(){},mixIn:function(e){for(var t in e)e.hasOwnProperty(t)&&(this[t]=e[t]);e.hasOwnProperty("toString")&&(this.toString=e.toString)},clone:function(){return this.init.prototype.extend(this)}},c=i.WordArray=s.extend({init:function(e,t){e=this.words=e||[],this.sigBytes=null!=t?t:4*e.length},toString:function(e){return(e||p).stringify(this)},concat:function(e){var t=this.words,n=e.words,r=this.sigBytes,o=e.sigBytes;if(this.clamp(),r%4)for(var a=0;a<o;a++)t[r+a>>>2]|=(n[a>>>2]>>>24-a%4*8&255)<<24-(r+a)%4*8;else for(var i=0;i<o;i+=4)t[r+i>>>2]=n[i>>>2];return this.sigBytes+=o,this},clamp:function(){var t=this.words,n=this.sigBytes;t[n>>>2]&=4294967295<<32-n%4*8,t.length=e.ceil(n/4)},clone:function(){var e=s.clone.call(this);return e.words=this.words.slice(0),e},random:function(e){for(var t=[],n=0;n<e;n+=4)t.push(r());return new c.init(t,e)}}),u=a.enc={},p=u.Hex={stringify:function(e){for(var t=e.words,n=e.sigBytes,r=[],o=0;o<n;o++){var a=t[o>>>2]>>>24-o%4*8&255;r.push((a>>>4).toString(16)),r.push((15&a).toString(16))}return r.join("")},parse:function(e){for(var t=e.length,n=[],r=0;r<t;r+=2)n[r>>>3]|=parseInt(e.substr(r,2),16)<<24-r%8*4;return new c.init(n,t/2)}},l=u.Latin1={stringify:function(e){for(var t=e.words,n=e.sigBytes,r=[],o=0;o<n;o++)r.push(String.fromCharCode(t[o>>>2]>>>24-o%4*8&255));return r.join("")},parse:function(e){for(var t=e.length,n=[],r=0;r<t;r++)n[r>>>2]|=(255&e.charCodeAt(r))<<24-r%4*8;return new c.init(n,t)}},d=u.Utf8={stringify:function(e){try{return decodeURIComponent(escape(l.stringify(e)))}catch(e){throw new Error("Malformed UTF-8 data")}},parse:function(e){return l.parse(unescape(encodeURIComponent(e)))}},f=i.BufferedBlockAlgorithm=s.extend({reset:function(){this._data=new c.init,this._nDataBytes=0},_append:function(e){"string"==typeof e&&(e=d.parse(e)),this._data.concat(e),this._nDataBytes+=e.sigBytes},_process:function(t){var n,r=this._data,o=r.words,a=r.sigBytes,i=this.blockSize,s=a/(4*i),u=(s=t?e.ceil(s):e.max((0|s)-this._minBufferSize,0))*i,p=e.min(4*u,a);if(u){for(var l=0;l<u;l+=i)this._doProcessBlock(o,l);n=o.splice(0,u),r.sigBytes-=p}return new c.init(n,p)},clone:function(){var e=s.clone.call(this);return e._data=this._data.clone(),e},_minBufferSize:0}),h=(i.Hasher=f.extend({cfg:s.extend(),init:function(e){this.cfg=this.cfg.extend(e),this.reset()},reset:function(){f.reset.call(this),this._doReset()},update:function(e){return this._append(e),this._process(),this},finalize:function(e){return e&&this._append(e),this._doFinalize()},blockSize:16,_createHelper:function(e){return function(t,n){return new e.init(n).finalize(t)}},_createHmacHelper:function(e){return function(t,n){return new h.HMAC.init(e,n).finalize(t)}}}),a.algo={});return a}(Math)})),io=K((function(e,t){var n;e.exports=(n=ao,function(e){var t=n,r=t.lib,o=r.WordArray,a=r.Hasher,i=t.algo,s=[],c=[];!function(){function t(t){for(var n=e.sqrt(t),r=2;r<=n;r++)if(!(t%r))return!1;return!0}function n(e){return 4294967296*(e-(0|e))|0}for(var r=2,o=0;o<64;)t(r)&&(o<8&&(s[o]=n(e.pow(r,.5))),c[o]=n(e.pow(r,1/3)),o++),r++}();var u=[],p=i.SHA256=a.extend({_doReset:function(){this._hash=new o.init(s.slice(0))},_doProcessBlock:function(e,t){for(var n=this._hash.words,r=n[0],o=n[1],a=n[2],i=n[3],s=n[4],p=n[5],l=n[6],d=n[7],f=0;f<64;f++){if(f<16)u[f]=0|e[t+f];else{var h=u[f-15],y=u[f-2];u[f]=((h<<25|h>>>7)^(h<<14|h>>>18)^h>>>3)+u[f-7]+((y<<15|y>>>17)^(y<<13|y>>>19)^y>>>10)+u[f-16]}var m=r&o^r&a^o&a,g=d+((s<<26|s>>>6)^(s<<21|s>>>11)^(s<<7|s>>>25))+(s&p^~s&l)+c[f]+u[f];d=l,l=p,p=s,s=i+g|0,i=a,a=o,o=r,r=g+(((r<<30|r>>>2)^(r<<19|r>>>13)^(r<<10|r>>>22))+m)|0}n[0]=n[0]+r|0,n[1]=n[1]+o|0,n[2]=n[2]+a|0,n[3]=n[3]+i|0,n[4]=n[4]+s|0,n[5]=n[5]+p|0,n[6]=n[6]+l|0,n[7]=n[7]+d|0},_doFinalize:function(){var t=this._data,n=t.words,r=8*this._nDataBytes,o=8*t.sigBytes;return n[o>>>5]|=128<<24-o%32,n[14+(o+64>>>9<<4)]=e.floor(r/4294967296),n[15+(o+64>>>9<<4)]=r,t.sigBytes=4*n.length,this._process(),this._hash},clone:function(){var e=a.clone.call(this);return e._hash=this._hash.clone(),e}});t.SHA256=a._createHelper(p),t.HmacSHA256=a._createHmacHelper(p)}(Math),n.SHA256)})),so=(K((function(e,t){var n,r;e.exports=(r=(n=ao).enc.Utf8,void(n.algo.HMAC=n.lib.Base.extend({init:function(e,t){e=this._hasher=new e.init,"string"==typeof t&&(t=r.parse(t));var n=e.blockSize,o=4*n;t.sigBytes>o&&(t=e.finalize(t)),t.clamp();for(var a=this._oKey=t.clone(),i=this._iKey=t.clone(),s=a.words,c=i.words,u=0;u<n;u++)s[u]^=1549556828,c[u]^=909522486;a.sigBytes=i.sigBytes=o,this.reset()},reset:function(){var e=this._hasher;e.reset(),e.update(this._iKey)},update:function(e){return this._hasher.update(e),this},finalize:function(e){var t=this._hasher,n=t.finalize(e);return t.reset(),t.finalize(this._oKey.clone().concat(n))}})))})),K((function(e,t){e.exports=ao.HmacSHA256}))),co=K((function(e,t){var n;e.exports=(n=ao,function(e){var t=n,r=t.lib,o=r.WordArray,a=r.Hasher,i=t.algo,s=[];!function(){for(var t=0;t<64;t++)s[t]=4294967296*e.abs(e.sin(t+1))|0}();var c=i.MD5=a.extend({_doReset:function(){this._hash=new o.init([1732584193,4023233417,2562383102,271733878])},_doProcessBlock:function(e,t){for(var n=0;n<16;n++){var r=t+n,o=e[r];e[r]=16711935&(o<<8|o>>>24)|4278255360&(o<<24|o>>>8)}var a=this._hash.words,i=e[t+0],c=e[t+1],f=e[t+2],h=e[t+3],y=e[t+4],m=e[t+5],g=e[t+6],b=e[t+7],v=e[t+8],x=e[t+9],w=e[t+10],S=e[t+11],k=e[t+12],C=e[t+13],T=e[t+14],E=e[t+15],j=a[0],O=a[1],R=a[2],A=a[3];j=u(j,O,R,A,i,7,s[0]),A=u(A,j,O,R,c,12,s[1]),R=u(R,A,j,O,f,17,s[2]),O=u(O,R,A,j,h,22,s[3]),j=u(j,O,R,A,y,7,s[4]),A=u(A,j,O,R,m,12,s[5]),R=u(R,A,j,O,g,17,s[6]),O=u(O,R,A,j,b,22,s[7]),j=u(j,O,R,A,v,7,s[8]),A=u(A,j,O,R,x,12,s[9]),R=u(R,A,j,O,w,17,s[10]),O=u(O,R,A,j,S,22,s[11]),j=u(j,O,R,A,k,7,s[12]),A=u(A,j,O,R,C,12,s[13]),R=u(R,A,j,O,T,17,s[14]),j=p(j,O=u(O,R,A,j,E,22,s[15]),R,A,c,5,s[16]),A=p(A,j,O,R,g,9,s[17]),R=p(R,A,j,O,S,14,s[18]),O=p(O,R,A,j,i,20,s[19]),j=p(j,O,R,A,m,5,s[20]),A=p(A,j,O,R,w,9,s[21]),R=p(R,A,j,O,E,14,s[22]),O=p(O,R,A,j,y,20,s[23]),j=p(j,O,R,A,x,5,s[24]),A=p(A,j,O,R,T,9,s[25]),R=p(R,A,j,O,h,14,s[26]),O=p(O,R,A,j,v,20,s[27]),j=p(j,O,R,A,C,5,s[28]),A=p(A,j,O,R,f,9,s[29]),R=p(R,A,j,O,b,14,s[30]),j=l(j,O=p(O,R,A,j,k,20,s[31]),R,A,m,4,s[32]),A=l(A,j,O,R,v,11,s[33]),R=l(R,A,j,O,S,16,s[34]),O=l(O,R,A,j,T,23,s[35]),j=l(j,O,R,A,c,4,s[36]),A=l(A,j,O,R,y,11,s[37]),R=l(R,A,j,O,b,16,s[38]),O=l(O,R,A,j,w,23,s[39]),j=l(j,O,R,A,C,4,s[40]),A=l(A,j,O,R,i,11,s[41]),R=l(R,A,j,O,h,16,s[42]),O=l(O,R,A,j,g,23,s[43]),j=l(j,O,R,A,x,4,s[44]),A=l(A,j,O,R,k,11,s[45]),R=l(R,A,j,O,E,16,s[46]),j=d(j,O=l(O,R,A,j,f,23,s[47]),R,A,i,6,s[48]),A=d(A,j,O,R,b,10,s[49]),R=d(R,A,j,O,T,15,s[50]),O=d(O,R,A,j,m,21,s[51]),j=d(j,O,R,A,k,6,s[52]),A=d(A,j,O,R,h,10,s[53]),R=d(R,A,j,O,w,15,s[54]),O=d(O,R,A,j,c,21,s[55]),j=d(j,O,R,A,v,6,s[56]),A=d(A,j,O,R,E,10,s[57]),R=d(R,A,j,O,g,15,s[58]),O=d(O,R,A,j,C,21,s[59]),j=d(j,O,R,A,y,6,s[60]),A=d(A,j,O,R,S,10,s[61]),R=d(R,A,j,O,f,15,s[62]),O=d(O,R,A,j,x,21,s[63]),a[0]=a[0]+j|0,a[1]=a[1]+O|0,a[2]=a[2]+R|0,a[3]=a[3]+A|0},_doFinalize:function(){var t=this._data,n=t.words,r=8*this._nDataBytes,o=8*t.sigBytes;n[o>>>5]|=128<<24-o%32;var a=e.floor(r/4294967296),i=r;n[15+(o+64>>>9<<4)]=16711935&(a<<8|a>>>24)|4278255360&(a<<24|a>>>8),n[14+(o+64>>>9<<4)]=16711935&(i<<8|i>>>24)|4278255360&(i<<24|i>>>8),t.sigBytes=4*(n.length+1),this._process();for(var s=this._hash,c=s.words,u=0;u<4;u++){var p=c[u];c[u]=16711935&(p<<8|p>>>24)|4278255360&(p<<24|p>>>8)}return s},clone:function(){var e=a.clone.call(this);return e._hash=this._hash.clone(),e}});function u(e,t,n,r,o,a,i){var s=e+(t&n|~t&r)+o+i;return(s<<a|s>>>32-a)+t}function p(e,t,n,r,o,a,i){var s=e+(t&r|n&~r)+o+i;return(s<<a|s>>>32-a)+t}function l(e,t,n,r,o,a,i){var s=e+(t^n^r)+o+i;return(s<<a|s>>>32-a)+t}function d(e,t,n,r,o,a,i){var s=e+(n^(t|~r))+o+i;return(s<<a|s>>>32-a)+t}t.MD5=a._createHelper(c),t.HmacMD5=a._createHmacHelper(c)}(Math),n.MD5)})),uo=K((function(e,t){var n,r;e.exports=(n=(r=ao).lib.WordArray,r.enc.Base64={stringify:function(e){var t=e.words,n=e.sigBytes,r=this._map;e.clamp();for(var o=[],a=0;a<n;a+=3)for(var i=(t[a>>>2]>>>24-a%4*8&255)<<16|(t[a+1>>>2]>>>24-(a+1)%4*8&255)<<8|t[a+2>>>2]>>>24-(a+2)%4*8&255,s=0;s<4&&a+.75*s<n;s++)o.push(r.charAt(i>>>6*(3-s)&63));var c=r.charAt(64);if(c)for(;o.length%4;)o.push(c);return o.join("")},parse:function(e){var t=e.length,r=this._map,o=this._reverseMap;if(!o){o=this._reverseMap=[];for(var a=0;a<r.length;a++)o[r.charCodeAt(a)]=a}var i=r.charAt(64);if(i){var s=e.indexOf(i);-1!==s&&(t=s)}return function(e,t,r){for(var o=[],a=0,i=0;i<t;i++)if(i%4){var s=r[e.charCodeAt(i-1)]<<i%4*2,c=r[e.charCodeAt(i)]>>>6-i%4*2;o[a>>>2]|=(s|c)<<24-a%4*8,a++}return n.create(o,a)}(e,t,o)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="},r.enc.Base64)})),po=K((function(e,t){e.exports=ao.enc.Hex})),lo=K((function(e,t){e.exports=ao.enc.Utf8}));function fo(e){switch(e){case"utf-8":return lo;case"base64":return uo;case"hex":return po;default:throw new pe("The coding is not supported")}}function ho(e,t){return t?e.toString(fo(t)):e}const yo=function(e,t){if(kr(e))throw new pe("not support buffer in browser environment");return ho(co(e),t)};let mo=null;mo={__proto__:null,hmacSha256:function(e,t,n){return ho(so(t,e),n)},hashSha256:function(e,t){return ho(io(e),t)},hashMd5:yo,parse:function(e,t){return fo(t).parse(e)},stringify:function(e,t){return fo(t).stringify(e)}};const{hmacSha256:go,hashSha256:bo,hashMd5:vo,parse:xo,stringify:wo}=mo;async function So(t){const{uploadId:n,partNumber:r,body:o,enableContentMD5:a=!1}=t,i=vr(t.headers);t.headers=i,Rr(t,["trafficLimit","ssecAlgorithm","ssecKey","ssecKeyMD5"]);const s=Gr(o);s&&null==i["content-length"]&&(i["content-length"]=s.toFixed(0)),a&&null==i["content-md5"]&&console.warn("current not support enableMD5Checksum");const c=Gr(t.body,i),u=null!=c;u||!t.dataTransferStatusChange&&!t.progress||console.warn("Don't get totalSize of uploadPart's body, the `dataTransferStatusChange` callback will not trigger. You can use `uploadPartFromFile` instead");let p=0;const{dataTransferStatusChange:l,progress:d}=t,f=(t,n=0)=>{if(!u||n<0)return;if(!l&&!d)return;p+=n,null==l||l({type:t,rwOnceBytes:n,consumedBytes:p,totalBytes:c});const r=0===c?t===e.DataTransferType.Succeed?1:0:p/c;1===r?t===e.DataTransferType.Succeed&&(null==d||d(r)):null==d||d(r)},h=await Wr({body:t.body,dataTransferCallback:t=>f(e.DataTransferType.Rw,t),beforeRetry:t.beforeRetry,makeRetryStream:t.makeRetryStream,enableCRC:this.opts.enableCRC,rateLimiter:t.rateLimiter});f(e.DataTransferType.Started);const[y,m]=await wr((async()=>{const o=await this._fetchObject(t,"PUT",{partNumber:r,uploadId:n},i,h.body,{handleResponse:e=>({partNumber:r,ETag:e.headers.etag,serverSideEncryption:e.headers["x-tos-server-side-encryption"],serverSideDataEncryption:e.headers["x-tos-server-side-data-encryption"],serverSideEncryptionKeyId:e.headers["x-tos-server-side-encryption-kms-key-id"],ssecAlgorithm:e.headers["x-tos-server-side-encryption-customer-algorithm"],ssecKeyMD5:e.headers["x-tos-server-side-encryption-customer-key-MD5"],hashCrc64ecma:e.headers["x-tos-hash-crc64ecma"]}),axiosOpts:{__retryConfig__:{beforeRetry:()=>{p=0,null==h.beforeRetry||h.beforeRetry()},makeRetryStream:h.makeRetryStream},onUploadProgress:t=>{f(e.DataTransferType.Rw,t.loaded-p)}}});return this.opts.enableCRC&&h.crc&&Br(h.crc,o.headers),o})());if(m&&!m.data.ETag)throw new pe("No ETag in uploadPart's response headers, please see https://www.volcengine.com/docs/6349/127737 to fix CORS problem");if(y||!m)throw f(e.DataTransferType.Failed),y;return f(e.DataTransferType.Succeed),m}async function ko(e){return So.call(this,e)}async function Co(e){throw new pe("uploadPartFromFile doesn't support in browser environment")}async function To(e){var t;e.headers=null!=(t=e.headers)?t:{},Rr(e,["callback","callbackVar","forbidOverwrite"]);const n=t=>{const n=t.headers,r={VersionID:n["x-tos-version-id"],ETag:n.etag,Bucket:e.bucket||this.opts.bucket||"",Location:n.location,HashCrc64ecma:n["x-tos-hash-crc64ecma"],Key:e.key,...t.data};return e.callback&&(r.CallbackResult=""+JSON.stringify(t.data)),r};if(e.completeAll){var r;if((null==(r=e.parts)?void 0:r.length)>0)throw new pe("Should not specify both 'completeAll' and 'parts' params.");return this._fetchObject(e,"POST",{uploadId:e.uploadId},{...e.headers,"x-tos-complete-all":"yes"},void 0,{handleResponse:n})}return this._fetchObject(e,"POST",{uploadId:e.uploadId},{...e.headers},{Parts:e.parts.map(e=>({ETag:e.eTag,PartNumber:e.partNumber}))},{handleResponse:n})}var Eo;(Eo=e.UploadEventType||(e.UploadEventType={}))[Eo.CreateMultipartUploadSucceed=1]="CreateMultipartUploadSucceed",Eo[Eo.CreateMultipartUploadFailed=2]="CreateMultipartUploadFailed",Eo[Eo.UploadPartSucceed=3]="UploadPartSucceed",Eo[Eo.UploadPartFailed=4]="UploadPartFailed",Eo[Eo.UploadPartAborted=5]="UploadPartAborted",Eo[Eo.CompleteMultipartUploadSucceed=6]="CompleteMultipartUploadSucceed",Eo[Eo.CompleteMultipartUploadFailed=7]="CompleteMultipartUploadFailed";const jo=[403,404,405];async function Oo(t){var n,r,o;const{cancelToken:a,enableContentMD5:i=!1}=t,s=vr(t.headers);t.headers=s,Rr(t,["encodingType","cacheControl","contentDisposition","contentEncoding","contentLanguage","contentType","expires","acl","grantFullControl","grantRead","grantReadAcp","grantWriteAcp","ssecAlgorithm","ssecKey","ssecKeyMD5","serverSideEncryption","serverSideDataEncryption","meta","websiteRedirectLocation","storageClass"]);const c=()=>a&&!!a.reason,u=await(async()=>null)(),p=await(async()=>{const{file:e}=t;if(u)return u.size;if(kr(e))return e.length;if(Sr(e))return e.size;throw new pe("`file` must be string, Buffer, File or Blob")})(),l=await(async()=>"object"==typeof t.checkpoint?{record:t.checkpoint}:{})();await(async()=>{var e;if(u&&null!=(e=l.record)&&e.file_info){var t;const{last_modified:e,file_size:n}=null==(t=l.record)?void 0:t.file_info;u.mtimeMs===e&&u.size===n||(console.warn(`The file has been modified since ${new Date(e)}, so the checkpoint file is invalid, and specified file will be uploaded again.`),delete l.record)}})();const d=Ur(p,t.partSize||(null==(n=l.record)?void 0:n.part_size)||20971520,!0);l.record&&l.record.part_size!==d&&(console.warn("The partSize param does not equal the partSize in checkpoint file, so the checkpoint file is invalid, and specified file will be uploaded again."),delete l.record);let f=t.bucket||this.opts.bucket||"";const h=t.key;let y="",m=[];const g=function(e,t){const n=[];for(let r=0;;++r){const o=r*t,a=Math.min(t,e-o);if(n.push({offset:o,partSize:a,partNumber:r+1}),(r+1)*t>=e)break}return n}(p,d),b=((null==(r=l.record)?void 0:r.parts_info)||[]).filter(e=>e.is_completed).reduce((e,t)=>e+t.part_size,0);let v=b;const x=(null==(o=l.record)?void 0:o.parts_info)||[],w=new Map;x.forEach(e=>w.set(e.part_number,e));const S=()=>{const e={bucket:f,key:h,part_size:d,upload_id:y,parts_info:x};return u&&(e.file_info={last_modified:u.mtimeMs,file_size:u.size}),e},k=e=>{if(!t.uploadEventChange)return;const n={bucket:f,uploadId:y,key:h,...e};l.filePath&&(n.checkpointFile=l.filePath),t.uploadEventChange(n)};let C;!function(e){e[e.start=1]="start",e[e.uploadPartSucceed=2]="uploadPartSucceed",e[e.completeMultipartUploadSucceed=3]="completeMultipartUploadSucceed"}(C||(C={}));const T=e=>{t.progress&&(v===p&&e===C.uploadPartSucceed||t.progress(e===C.start&&0===p?0:p?v/p:1,S()))};let E=b;const{dataTransferStatusChange:j}=t,O=(e,t=0)=>{j&&(E+=t,null==j||j({type:e,rwOnceBytes:t,consumedBytes:E,totalBytes:p}))},R=Ir(async()=>{}),A=async(t,n)=>{let r=w.get(t.partNumber);r||(r={part_number:t.partNumber,offset:t.offset,part_size:t.partSize,is_completed:!1,etag:"",hash_crc64ecma:""},x.push(r),w.set(r.part_number,r)),n.err||(r.is_completed=!0,r.etag=n.res.ETag,r.hash_crc64ecma=n.res.hashCrc64ecma),await R();const o={partNumber:r.part_number,partSize:r.part_size,offset:r.offset};if(n.err){const t=n.err;let r=e.UploadEventType.UploadPartFailed;return t instanceof ue&&jo.includes(t.statusCode)&&(r=e.UploadEventType.UploadPartAborted),void k({type:r,err:t,uploadPartInfo:o})}o.etag=n.res.ETag,v+=o.partSize,k({type:e.UploadEventType.UploadPartSucceed,uploadPartInfo:o}),T(C.uploadPartSucceed)};if(l.record){f=l.record.bucket,y=l.record.upload_id;const e=new Set((l.record.parts_info||[]).filter(e=>e.is_completed).map(e=>e.part_number));m=g.filter(t=>!e.has(t.partNumber))}else{try{const{data:n}=await Dr.call(this,t);if(c())throw new dt("cancel uploadFile");var P;f=n.Bucket,y=n.UploadId,l.filePathIsPlaceholder&&(l.filePath=null==(P=l.filePath)?void 0:P.replace("@@checkpoint-file-placeholder@@",function(e,t){return`${t}.${vo(`${e}.${t}`,"hex")}.upload`.replace(/[\\/]/g,"")}(f,h))),k({type:e.UploadEventType.CreateMultipartUploadSucceed})}catch(t){const n=t;throw k({type:e.UploadEventType.CreateMultipartUploadFailed,err:n}),n}m=g}T(C.start),O(e.DataTransferType.Started);const[B,_]=await wr((async()=>{let n=null,r=0;if(await Promise.all(Array.from({length:t.taskNum||1}).map(async()=>{for(;;){const a=r++;if(a>=m.length)return;const u=m[a];let p=0;const l=void 0;try{function o(e,t){const{offset:n,partSize:r}=t,o=n+r;if(l)return l.make();if(Sr(e))return e.slice(n,o);if(kr(e))return e.slice(n,o);throw new pe("`file` must be string, Buffer, File or Blob")}const{data:n}=await So.call(this,{bucket:f,key:h,uploadId:y,body:o(t.file,u),enableContentMD5:i,makeRetryStream:null==l?void 0:l.make,beforeRetry:()=>{E-=p,p=0},partNumber:u.partNumber,headers:{"content-length":""+u.partSize,"x-tos-server-side-encryption-customer-algorithm":s["x-tos-server-side-encryption-customer-algorithm"],"x-tos-server-side-encryption-customer-key":s["x-tos-server-side-encryption-customer-key"],"x-tos-server-side-encryption-customer-key-md5":s["x-tos-server-side-encryption-customer-key-md5"]},dataTransferStatusChange(t){t.type===e.DataTransferType.Rw&&(c()||(p+=t.rwOnceBytes,O(t.type,t.rwOnceBytes)))},trafficLimit:t.trafficLimit,rateLimiter:t.rateLimiter});if(c())throw new dt("cancel uploadFile");await A(u,{res:n})}catch(e){Mr(null==l?void 0:l.getLastStream(),e);const t=e;if(E-=p,p=0,Tr(t))throw t;if(c())throw new dt("cancel uploadFile");n||(n=t),await A(u,{err:t})}}})),n)throw n;const o=(S().parts_info||[]).map(e=>({eTag:e.etag,partNumber:e.part_number})),[a,u]=await wr(To.call(this,{bucket:f,key:h,uploadId:y,parts:o}));if(a||!u)throw k({type:e.UploadEventType.CompleteMultipartUploadFailed}),a;if(k({type:e.UploadEventType.CompleteMultipartUploadSucceed}),T(C.completeMultipartUploadSucceed),await(async()=>{})(),this.opts.enableCRC&&u.data.HashCrc64ecma&&function(e){var t,n,r;const o=(null==(t=e.file_info)?void 0:t.file_size)||0;let a="0";const i=null!=(n=null==(r=e.parts_info)||null==r.sort?void 0:r.sort((e,t)=>e.part_number-t.part_number))?n:[];for(const e of i)a=zr(a,e.hash_crc64ecma,Math.min(e.part_size,o-e.offset));return a}(S())!==u.data.HashCrc64ecma)throw new pe("crc of entire file mismatch.");return u})());if(B||!_)throw O(e.DataTransferType.Failed),B;return O(e.DataTransferType.Succeed),_}var Ro,Ao,Po,Bo,_o,Io,Mo,Do,Uo,Fo,No,Lo,zo,qo,Ko,Ho,$o,Go;async function Wo(e){const t="string"==typeof e?{key:e}:e,n=vr(t.headers);t.headers=n;const r={};return t.versionId&&(r.versionId=t.versionId),Rr(t,["ifMatch","ifModifiedSince","ifNoneMatch","ifUnmodifiedSince","ssecAlgorithm","ssecKey","ssecKeyMD5"]),this._fetchObject(e,"HEAD",r,(null==t?void 0:t.headers)||{},void 0,{handleResponse:e=>{const t={...e.headers,ReplicationStatus:e.headers[Hr.HeaderReplicationStatus]},n=Jr(e.headers);return n&&(t.RestoreInfo=n),t}})}async function Vo(e){const{uploadId:t,partNumber:n}=e,r=vr(e.headers);if(e.headers=r,Rr(e,["copySourceRange","copySourceSSECAlgorithm","copySourceSSECKey","copySourceSSECKeyMD5","ssecAlgorithm","ssecKey","ssecKeyMD5","trafficLimit"]),e.srcBucket&&e.srcKey){var o;let t=Vr(e.srcBucket,e.srcKey);e.srcVersionID&&(t+="?versionId="+e.srcVersionID),r["x-tos-copy-source"]=null!=(o=r["x-tos-copy-source"])?o:t}if(null==e.copySourceRange&&(null!=e.copySourceRangeStart||null!=e.copySourceRangeEnd)){var a;const t=`bytes=${null!=e.copySourceRangeStart?""+e.copySourceRangeStart:""}-${null!=e.copySourceRangeEnd?""+e.copySourceRangeEnd:""}`;r["x-tos-copy-source-range"]=null!=(a=r["x-tos-copy-source-range"])?a:t}const[i,s]=await wr(this._fetchObject(e,"PUT",{partNumber:n,uploadId:t},r,void 0,{handleResponse:e=>({...e.data,SSECAlgorithm:e.headers[jr.ssecAlgorithm],SSECKeyMD5:e.headers[jr.ssecKeyMD5]})}));if(i||!s||!s.data.ETag)throw i;return s}async function Jo(e){const t=vr(e.headers);if(e.headers=t,Rr(e,["cacheControl","contentDisposition","contentEncoding","contentLanguage","contentType","expires","copySourceIfMatch","copySourceIfModifiedSince","copySourceIfNoneMatch","copySourceIfUnmodifiedSince","copySourceSSECAlgorithm","copySourceSSECKey","copySourceSSECKeyMD5","acl","grantFullControl","grantRead","grantReadAcp","grantWriteAcp","ssecAlgorithm","ssecKey","ssecKeyMD5","serverSideEncryption","metadataDirective","meta","websiteRedirectLocation","storageClass","trafficLimit","forbidOverwrite","ifMatch"]),e.srcBucket&&e.srcKey){var n;let r=Vr(e.srcBucket,e.srcKey);e.srcVersionID&&(r+="?versionId="+e.srcVersionID),t["x-tos-copy-source"]=null!=(n=t["x-tos-copy-source"])?n:r}const[r,o]=await wr(this._fetchObject(e,"PUT",{},t));if(r||!o||!o.data.ETag)throw r;return o}function Qo(e){var t=this.__data__=new Ge(e);this.size=t.size}(Ro=e.ACLType||(e.ACLType={})).ACLPrivate="private",Ro.ACLPublicRead="public-read",Ro.ACLPublicReadWrite="public-read-write",Ro.ACLAuthenticatedRead="authenticated-read",Ro.ACLBucketOwnerRead="bucket-owner-read",Ro.ACLBucketOwnerFullControl="bucket-owner-full-control",Ro.ACLBucketOwnerEntrusted="bucket-owner-entrusted",Ro.ACLDefault="default",(Ao=e.StorageClassType||(e.StorageClassType={})).StorageClassStandard="STANDARD",Ao.StorageClassIa="IA",Ao.StorageClassArchiveFr="ARCHIVE_FR",Ao.StorageClassColdArchive="COLD_ARCHIVE",Ao.StorageClassIntelligentTiering="INTELLIGENT_TIERING",Ao.StorageClassArchive="ARCHIVE",(Po=e.MetadataDirectiveType||(e.MetadataDirectiveType={})).MetadataDirectiveCopy="COPY",Po.MetadataDirectiveReplace="REPLACE",(Bo=e.AzRedundancyType||(e.AzRedundancyType={})).AzRedundancySingleAz="single-az",Bo.AzRedundancyMultiAz="multi-az",(_o=e.PermissionType||(e.PermissionType={})).PermissionRead="READ",_o.PermissionWrite="WRITE",_o.PermissionReadAcp="READ_ACP",_o.PermissionWriteAcp="WRITE_ACP",_o.PermissionFullControl="FULL_CONTROL",_o.PermissionReadNONLIST="READ_NON_LIST",(Io=e.GranteeType||(e.GranteeType={})).GranteeGroup="Group",Io.GranteeUser="CanonicalUser",(Mo=e.CannedType||(e.CannedType={})).CannedAllUsers="AllUsers",Mo.CannedAuthenticatedUsers="AuthenticatedUsers",(Do=e.HttpMethodType||(e.HttpMethodType={})).HttpMethodGet="GET",Do.HttpMethodPut="PUT",Do.HttpMethodPost="POST",Do.HttpMethodDelete="DELETE",Do.HttpMethodHead="HEAD",(Uo=e.StorageClassInheritDirectiveType||(e.StorageClassInheritDirectiveType={})).StorageClassInheritDirectiveDestinationBucket="DESTINATION_BUCKET",Uo.StorageClassInheritDirectiveSourceObject="SOURCE_OBJECT",(Fo=e.ReplicationStatusType||(e.ReplicationStatusType={})).Complete="COMPLETE",Fo.Pending="PENDING",Fo.Failed="FAILED",Fo.Replica="REPLICA",(No=e.LifecycleStatusType||(e.LifecycleStatusType={})).Enabled="Enabled",No.Disabled="Disabled",(Lo=e.RedirectType||(e.RedirectType={})).Mirror="Mirror",Lo.Async="Async",(zo=e.StatusType||(e.StatusType={})).Enabled="Enabled",zo.Disabled="Disabled",(qo=e.TierType||(e.TierType={})).TierStandard="Standard",qo.TierExpedited="Expedited",qo.TierBulk="Bulk",(Ko=e.VersioningStatusType||(e.VersioningStatusType={})).Enabled="Enabled",Ko.Suspended="Suspended",Ko.NotSet="",Ko.Enable="Enabled",Ko.Disable="",(Ho=e.AccessPointStatusType||(e.AccessPointStatusType={})).Ready="READY",Ho.Creating="CREATING",Ho.Created="CREATED",Ho.Deleting="DELETING",($o=e.TransferAccelerationStatusType||(e.TransferAccelerationStatusType={})).Activating="AccelerationActivating",$o.Activated="AccelerationActivated",$o.Terminated="AccelerationTerminated",(Go=e.MRAPMirrorBackRedirectPolicyType||(e.MRAPMirrorBackRedirectPolicyType={})).ClosestFirst="Closest-First",Go.LatestFirst="Latest-First",Qo.prototype.clear=function(){this.__data__=new Ge,this.size=0},Qo.prototype.delete=function(e){var t=this.__data__,n=t.delete(e);return this.size=t.size,n},Qo.prototype.get=function(e){return this.__data__.get(e)},Qo.prototype.has=function(e){return this.__data__.has(e)},Qo.prototype.set=function(e,t){var n=this.__data__;if(n instanceof Ge){var r=n.__data__;if(!We||r.length<199)return r.push([e,t]),this.size=++n.size,this;n=this.__data__=new Qe(r)}return n.set(e,t),this.size=n.size,this};var Xo=Qo,Zo=function(e,t,n,r){var o=!n;n||(n={});for(var a=-1,i=t.length;++a<i;){var s=t[a],c=r?r(n[s],e[s],s,n,e):void 0;void 0===c&&(c=e[s]),o?st(n,s,c):ut(n,s,c)}return n},Yo=function(e){return Ce(e)&&"[object Arguments]"==ke(e)},ea=Object.prototype,ta=ea.hasOwnProperty,na=ea.propertyIsEnumerable,ra=Yo(function(){return arguments}())?Yo:function(e){return Ce(e)&&ta.call(e,"callee")&&!na.call(e,"callee")},oa=function(){return!1},aa=K((function(e,t){var n=t&&!t.nodeType&&t,r=n&&e&&!e.nodeType&&e,o=r&&r.exports===n?ye.Buffer:void 0;e.exports=(o?o.isBuffer:void 0)||oa})),ia=function(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=9007199254740991},sa={};sa["[object Float32Array]"]=sa["[object Float64Array]"]=sa["[object Int8Array]"]=sa["[object Int16Array]"]=sa["[object Int32Array]"]=sa["[object Uint8Array]"]=sa["[object Uint8ClampedArray]"]=sa["[object Uint16Array]"]=sa["[object Uint32Array]"]=!0,sa["[object Arguments]"]=sa["[object Array]"]=sa["[object ArrayBuffer]"]=sa["[object Boolean]"]=sa["[object DataView]"]=sa["[object Date]"]=sa["[object Error]"]=sa["[object Function]"]=sa["[object Map]"]=sa["[object Number]"]=sa["[object Object]"]=sa["[object RegExp]"]=sa["[object Set]"]=sa["[object String]"]=sa["[object WeakMap]"]=!1;var ca=function(e){return function(t){return e(t)}},ua=K((function(e,t){var n=t&&!t.nodeType&&t,r=n&&e&&!e.nodeType&&e,o=r&&r.exports===n&&fe.process,a=function(){try{return r&&r.require&&r.require("util").types||o&&o.binding&&o.binding("util")}catch(e){}}();e.exports=a})),pa=ua&&ua.isTypedArray,la=pa?ca(pa):function(e){return Ce(e)&&ia(e.length)&&!!sa[ke(e)]},da=Object.prototype.hasOwnProperty,fa=function(e,t){var n=de(e),r=!n&&ra(e),o=!n&&!r&&aa(e),a=!n&&!r&&!o&&la(e),i=n||r||o||a,s=i?function(e,t){for(var n=-1,r=Array(e);++n<e;)r[n]=t(n);return r}(e.length,String):[],c=s.length;for(var u in e)!t&&!da.call(e,u)||i&&("length"==u||o&&("offset"==u||"parent"==u)||a&&("buffer"==u||"byteLength"==u||"byteOffset"==u)||lt(u,c))||s.push(u);return s},ha=Object.prototype,ya=function(e){var t=e&&e.constructor;return e===("function"==typeof t&&t.prototype||ha)},ma=function(e,t){return function(n){return e(t(n))}},ga=ma(Object.keys,Object),ba=Object.prototype.hasOwnProperty,va=function(e){return null!=e&&ia(e.length)&&!Re(e)},xa=function(e){return va(e)?fa(e):function(e){if(!ya(e))return ga(e);var t=[];for(var n in Object(e))ba.call(e,n)&&"constructor"!=n&&t.push(n);return t}(e)},wa=Object.prototype.hasOwnProperty,Sa=function(e){return va(e)?fa(e,!0):function(e){if(!Oe(e))return function(e){var t=[];if(null!=e)for(var n in Object(e))t.push(n);return t}(e);var t=ya(e),n=[];for(var r in e)("constructor"!=r||!t&&wa.call(e,r))&&n.push(r);return n}(e)},ka=K((function(e,t){var n=t&&!t.nodeType&&t,r=n&&e&&!e.nodeType&&e,o=r&&r.exports===n?ye.Buffer:void 0,a=o?o.allocUnsafe:void 0;e.exports=function(e,t){if(t)return e.slice();var n=e.length,r=a?a(n):new e.constructor(n);return e.copy(r),r}})),Ca=function(){return[]},Ta=Object.prototype.propertyIsEnumerable,Ea=Object.getOwnPropertySymbols,ja=Ea?function(e){return null==e?[]:(e=Object(e),function(t,n){for(var r=-1,o=null==t?0:t.length,a=0,i=[];++r<o;){var s=t[r];Ta.call(e,s)&&(i[a++]=s)}return i}(Ea(e)))}:Ca,Oa=function(e,t){for(var n=-1,r=t.length,o=e.length;++n<r;)e[o+n]=t[n];return e},Ra=ma(Object.getPrototypeOf,Object),Aa=Object.getOwnPropertySymbols?function(e){for(var t=[];e;)Oa(t,ja(e)),e=Ra(e);return t}:Ca,Pa=function(e,t,n){var r=t(e);return de(e)?r:Oa(r,n(e))},Ba=function(e){return Pa(e,xa,ja)},_a=function(e){return Pa(e,Sa,Aa)},Ia=De(ye,"DataView"),Ma=De(ye,"Promise"),Da=De(ye,"Set"),Ua=De(ye,"WeakMap"),Fa=_e(Ia),Na=_e(We),La=_e(Ma),za=_e(Da),qa=_e(Ua),Ka=ke;(Ia&&"[object DataView]"!=Ka(new Ia(new ArrayBuffer(1)))||We&&"[object Map]"!=Ka(new We)||Ma&&"[object Promise]"!=Ka(Ma.resolve())||Da&&"[object Set]"!=Ka(new Da)||Ua&&"[object WeakMap]"!=Ka(new Ua))&&(Ka=function(e){var t=ke(e),n="[object Object]"==t?e.constructor:void 0,r=n?_e(n):"";if(r)switch(r){case Fa:return"[object DataView]";case Na:return"[object Map]";case La:return"[object Promise]";case za:return"[object Set]";case qa:return"[object WeakMap]"}return t});var Ha=Ka,$a=Object.prototype.hasOwnProperty,Ga=ye.Uint8Array,Wa=function(e){var t=new e.constructor(e.byteLength);return new Ga(t).set(new Ga(e)),t},Va=/\w*$/,Ja=me?me.prototype:void 0,Qa=Ja?Ja.valueOf:void 0,Xa=Object.create,Za=function(){function e(){}return function(t){if(!Oe(t))return{};if(Xa)return Xa(t);e.prototype=t;var n=new e;return e.prototype=void 0,n}}(),Ya=ua&&ua.isMap,ei=Ya?ca(Ya):function(e){return Ce(e)&&"[object Map]"==Ha(e)},ti=ua&&ua.isSet,ni=ti?ca(ti):function(e){return Ce(e)&&"[object Set]"==Ha(e)},ri={};ri["[object Arguments]"]=ri["[object Array]"]=ri["[object ArrayBuffer]"]=ri["[object DataView]"]=ri["[object Boolean]"]=ri["[object Date]"]=ri["[object Float32Array]"]=ri["[object Float64Array]"]=ri["[object Int8Array]"]=ri["[object Int16Array]"]=ri["[object Int32Array]"]=ri["[object Map]"]=ri["[object Number]"]=ri["[object Object]"]=ri["[object RegExp]"]=ri["[object Set]"]=ri["[object String]"]=ri["[object Symbol]"]=ri["[object Uint8Array]"]=ri["[object Uint8ClampedArray]"]=ri["[object Uint16Array]"]=ri["[object Uint32Array]"]=!0,ri["[object Error]"]=ri["[object Function]"]=ri["[object WeakMap]"]=!1;var oi,ai=function e(t,n,r,o,a,i){var s,c=1&n,u=2&n,p=4&n;if(r&&(s=a?r(t,o,a,i):r(t)),void 0!==s)return s;if(!Oe(t))return t;var l=de(t);if(l){if(s=function(e){var t=e.length,n=new e.constructor(t);return t&&"string"==typeof e[0]&&$a.call(e,"index")&&(n.index=e.index,n.input=e.input),n}(t),!c)return function(e,t){var n=-1,r=e.length;for(t||(t=Array(r));++n<r;)t[n]=e[n];return t}(t,s)}else{var d=Ha(t),f="[object Function]"==d||"[object GeneratorFunction]"==d;if(aa(t))return ka(t,c);if("[object Object]"==d||"[object Arguments]"==d||f&&!a){if(s=u||f?{}:function(e){return"function"!=typeof e.constructor||ya(e)?{}:Za(Ra(e))}(t),!c)return u?function(e,t){return Zo(e,Aa(e),t)}(t,function(e,t){return e&&Zo(t,Sa(t),e)}(s,t)):function(e,t){return Zo(e,ja(e),t)}(t,function(e,t){return e&&Zo(t,xa(t),e)}(s,t))}else{if(!ri[d])return a?t:{};s=function(e,t,n){var r,o,a=e.constructor;switch(t){case"[object ArrayBuffer]":return Wa(e);case"[object Boolean]":case"[object Date]":return new a(+e);case"[object DataView]":return function(e,t){var n=t?Wa(e.buffer):e.buffer;return new e.constructor(n,e.byteOffset,e.byteLength)}(e,n);case"[object Float32Array]":case"[object Float64Array]":case"[object Int8Array]":case"[object Int16Array]":case"[object Int32Array]":case"[object Uint8Array]":case"[object Uint8ClampedArray]":case"[object Uint16Array]":case"[object Uint32Array]":return function(e,t){var n=t?Wa(e.buffer):e.buffer;return new e.constructor(n,e.byteOffset,e.length)}(e,n);case"[object Map]":return new a;case"[object Number]":case"[object String]":return new a(e);case"[object RegExp]":return(o=new(r=e).constructor(r.source,Va.exec(r))).lastIndex=r.lastIndex,o;case"[object Set]":return new a;case"[object Symbol]":return Qa?Object(Qa.call(e)):{}}}(t,d,c)}}i||(i=new Xo);var h=i.get(t);if(h)return h;i.set(t,s),ni(t)?t.forEach((function(o){s.add(e(o,n,r,o,t,i))})):ei(t)&&t.forEach((function(o,a){s.set(a,e(o,n,r,a,t,i))}));var y=l?void 0:(p?u?_a:Ba:u?Sa:xa)(t);return function(e,t){for(var n=-1,r=null==e?0:e.length;++n<r&&!1!==t(e[n],n););}(y||t,(function(o,a){y&&(o=t[a=o]),ut(s,a,e(o,n,r,a,t,i))})),s};(oi=e.ResumableCopyEventType||(e.ResumableCopyEventType={}))[oi.CreateMultipartUploadSucceed=1]="CreateMultipartUploadSucceed",oi[oi.CreateMultipartUploadFailed=2]="CreateMultipartUploadFailed",oi[oi.UploadPartCopySucceed=3]="UploadPartCopySucceed",oi[oi.UploadPartCopyFailed=4]="UploadPartCopyFailed",oi[oi.UploadPartCopyAborted=5]="UploadPartCopyAborted",oi[oi.CompleteMultipartUploadSucceed=6]="CompleteMultipartUploadSucceed",oi[oi.CompleteMultipartUploadFailed=7]="CompleteMultipartUploadFailed";const ii=[403,404,405];async function si(t){var n,r,o;const{cancelToken:a}=t,i=()=>a&&!!a.reason,{data:s}=await Wo.call(this,{bucket:t.srcBucket,key:t.srcKey,versionId:t.srcVersionId}),c=s.etag,u=+s["content-length"],p=await(async()=>"object"==typeof t.checkpoint?{record:t.checkpoint}:{})();await(async()=>{var e;if(null!=(e=p.record)&&e.copy_source_object_info){var t;const{last_modified:e,object_size:n}=null==(t=p.record)?void 0:t.copy_source_object_info;s["last-modified"]===e&&+s["content-length"]===n||(console.warn(`The file has been modified since ${new Date(e)}, so the checkpoint file is invalid, and specified file will be uploaded again.`),delete p.record)}})();const l=Ur(u,t.partSize||(null==(n=p.record)?void 0:n.part_size)||20971520,!0);p.record&&p.record.part_size!==l&&(console.warn("The partSize param does not equal the partSize in checkpoint file, so the checkpoint file is invalid, and specified file will be uploaded again."),delete p.record);let d=t.bucket||this.opts.bucket||"";const f=t.key;let h="",y=[];const m=function(e,t){const n=[];for(let r=0;;++r){const o=r*t,a=Math.min(t,e-o);if(n.push({offset:o,partSize:a,partNumber:r+1}),(r+1)*t>=e)break}return n}(u,l);let g=((null==(r=p.record)?void 0:r.parts_info)||[]).filter(e=>e.is_completed).reduce((e,t)=>e+t.copy_source_range_end-t.copy_source_range_start+1,0);const b=(null==(o=p.record)?void 0:o.parts_info)||[],v=new Map;b.forEach(e=>v.set(e.part_number,e));const x=()=>({bucket:d,key:f,part_size:l,upload_id:h,parts_info:b,copy_source_object_info:{last_modified:s["last-modified"],etag:s.etag,hash_crc64ecma:s["x-tos-hash-crc64ecma"]||"",object_size:+s["content-length"]}}),w=e=>{if(!t.copyEventListener)return;const n={bucket:d,uploadId:h,key:f,...e};p.filePath&&(n.checkpointFile=p.filePath),t.copyEventListener(n)};let S;!function(e){e[e.start=1]="start",e[e.uploadPartSucceed=2]="uploadPartSucceed",e[e.completeMultipartUploadSucceed=3]="completeMultipartUploadSucceed"}(S||(S={}));const k=e=>{t.progress&&(g===u&&e===S.uploadPartSucceed||t.progress(e===S.start&&0===u?0:u?g/u:1,x()))},C=Ir(async()=>{}),T=async(t,n)=>{let r=v.get(t.partNumber);const o=t.offset,a=Math.min(t.offset+l-1,u-1);r||(r={part_number:t.partNumber,copy_source_range_start:o,copy_source_range_end:a,is_completed:!1,etag:""},b.push(r),v.set(r.part_number,r)),n.err||(r.is_completed=!0,r.etag=n.res.ETag),await C();const i={partNumber:r.part_number,copySourceRangeEnd:r.copy_source_range_end,copySourceRangeStart:r.copy_source_range_start};if(n.err){const t=n.err;let r=e.ResumableCopyEventType.UploadPartCopyFailed;return t instanceof ue&&ii.includes(t.statusCode)&&(r=e.ResumableCopyEventType.UploadPartCopyAborted),void w({type:r,err:t,copyPartInfo:i})}i.etag=n.res.ETag,g+=i.copySourceRangeEnd-i.copySourceRangeStart+1,w({type:e.ResumableCopyEventType.UploadPartCopySucceed,copyPartInfo:i}),k(S.uploadPartSucceed)};if(p.record){d=p.record.bucket,h=p.record.upload_id;const e=new Set((p.record.parts_info||[]).filter(e=>e.is_completed).map(e=>e.part_number));y=m.filter(t=>!e.has(t.partNumber))}else{try{const{data:n}=await Dr.call(this,(O=t,ai(O,5)));if(i())throw new dt("cancel uploadFile");var E;d=n.Bucket,h=n.UploadId,p.filePathIsPlaceholder&&(p.filePath=null==(E=p.filePath)?void 0:E.replace("@@checkpoint-file-placeholder@@",[(j={...t,bucket:d}).srcBucket,j.srcKey,j.srcVersionId,j.bucket,j.key,"copy"].filter(Boolean).join(".").replace(/[\\/]/g,""))),w({type:e.ResumableCopyEventType.CreateMultipartUploadSucceed})}catch(t){const n=t;throw w({type:e.ResumableCopyEventType.CreateMultipartUploadFailed,err:n}),n}y=m}var j,O;return k(S.start),0===u?(async()=>{let n=Vr(t.srcBucket,t.srcKey);t.srcVersionId&&(n+="?versionId="+t.srcVersionId);const r={...t.headers,"x-tos-copy-source":n,"x-tos-copy-source-if-match":c},[o,a]=await wr(Jo.call(this,{bucket:t.bucket,key:t.key,headers:r,trafficLimit:t.trafficLimit}));if(o||!a)throw w({type:e.ResumableCopyEventType.UploadPartCopyFailed}),o;return k(S.completeMultipartUploadSucceed),w({type:e.ResumableCopyEventType.UploadPartCopySucceed,copyPartInfo:{partNumber:0,copySourceRangeStart:0,copySourceRangeEnd:0}}),w({type:e.ResumableCopyEventType.CompleteMultipartUploadSucceed}),{...a,data:{ETag:a.headers.etag||"",Bucket:d,Key:f,Location:`http${this.opts.secure?"s":""}://${d}.${this.opts.endpoint}/${f}`,VersionID:a.headers["x-tos-version-id"],HashCrc64ecma:a.headers["x-tos-hash-crc64ecma"]}}})():(async()=>{let n=null,r=0;if(await Promise.all(Array.from({length:t.taskNum||1}).map(async()=>{for(;;){const e=r++;if(e>=y.length)return;const o=y[e];try{let e=Vr(t.srcBucket,t.srcKey);t.srcVersionId&&(e+="?versionId="+t.srcVersionId);const n=`bytes=${o.offset}-${o.offset+o.partSize-1}`,r={...t.headers,"x-tos-copy-source":e,"x-tos-copy-source-if-match":c,"x-tos-copy-source-range":n};o.partSize||delete r["x-tos-copy-source-range"];const{data:a}=await Vo.call(this,{bucket:d,key:f,uploadId:h,partNumber:o.partNumber,headers:r,trafficLimit:t.trafficLimit});if(i())throw new dt("cancel resumableCopyObject");await T(o,{res:a})}catch(e){const t=e;if(ci(t))throw t;if(i())throw new dt("cancel resumableCopyObject");n||(n=t),await T(o,{err:t})}}})),n)throw n;const o=(x().parts_info||[]).map(e=>({eTag:e.etag,partNumber:e.part_number})),[a,s]=await wr(To.call(this,{bucket:d,key:f,uploadId:h,parts:o}));if(a||!s)throw w({type:e.ResumableCopyEventType.CompleteMultipartUploadFailed}),a;w({type:e.ResumableCopyEventType.CompleteMultipartUploadSucceed}),k(S.completeMultipartUploadSucceed);const u=x().copy_source_object_info.hash_crc64ecma,p=s.data.HashCrc64ecma;if(this.opts.enableCRC&&u&&p&&u!==p)throw new pe(`validate file crc64 failed. Expect crc64 ${u}, actual crc64 ${p}. Please try again.`);return await(async()=>{})(),s})()}function ci(e){return e instanceof dt}async function ui(e){const t="string"==typeof e?{key:e}:e,n={};t.versionId&&(n.versionId=t.versionId);const r=vr(null==t?void 0:t.headers),o=(null==t?void 0:t.response)||{};return Object.keys(o).forEach(e=>{const t=o[e];null!=t&&(n["response-"+e]=t)}),this._fetchObject(e,"GET",n,r,void 0,{axiosOpts:{responseType:"arraybuffer"}})}const pi=["blob"];async function li(t){const n="string"==typeof t?{key:t}:t,r=vr(n.headers);n.headers=r;const o=n.dataType||"stream";n.dataType=o,function(e){let t="node",n=[];if(t="browser",n=pi,!n.includes(e))throw new pe(`The value of \`dataType\` only supports \`${n.join(" | ")}\` in browser environment`)}(o);const a={},i=(null==n?void 0:n.response)||{};if(Object.keys(i).forEach(e=>{const t=i[e];null!=t&&(a["response-"+e]=t)}),function(e,t,n){function r(e,n){null==t[e]&&(t[e]=n)}n.length&&n.forEach(t=>{const n=Or[t];if(!n)throw new pe(`\`${t}\` isn't in keys of \`requestQueryMap\``);const o=e[t];if(null==o)return;if("string"==typeof n)return r(n,""+o);if(Array.isArray(n))return r(n[0],n[1](o));const a=n(o);Object.entries(a).forEach(([e,t])=>{r(e,t)})})}(n,a,["versionId","process","saveBucket","saveObject","responseCacheControl","responseContentDisposition","responseContentEncoding","responseContentLanguage","responseContentType","responseExpires"]),Rr(n,["ifMatch","ifModifiedSince","ifNoneMatch","ifUnmodifiedSince","ssecAlgorithm","ssecKey","ssecKeyMD5","range","trafficLimit"]),null==n.range&&(null!=n.rangeStart||null!=n.rangeEnd)){var s;const e=`bytes=${null!=n.rangeStart?""+n.rangeStart:""}-${null!=n.rangeEnd?""+n.rangeEnd:""}`;r.range=null!=(s=r.range)?s:e}let c=0,u=-1;const{dataTransferStatusChange:p,progress:l}=n,d=(t,n=0)=>{if(n<0)return;if(!p&&!l)return;c+=n,null==p||p({type:t,rwOnceBytes:n,consumedBytes:c,totalBytes:u});const r=u<0?0:0===u?t===e.DataTransferType.Succeed?1:0:c/u;1===r?t===e.DataTransferType.Succeed&&(null==l||l(r)):null==l||l(r)};d(e.DataTransferType.Started);const[f,h]=await wr(this._fetchObject(t,"GET",a,r,void 0,{axiosOpts:{responseType:"arraybuffer",onDownloadProgress:t=>{u=t.total,d(e.DataTransferType.Rw,t.loaded-c)}}}));if(f||!h)throw d(e.DataTransferType.Failed),f;let y=h.headers,m=h.data;u=+(y["content-length"]||0),"blob"===o&&(m=new Blob([h.data],{type:y["content-type"]})),d(e.DataTransferType.Succeed);const g={...h,data:{content:m,etag:y.etag||"",lastModified:y["last-modified"]||"",hashCrc64ecma:y["x-tos-hash-crc64ecma"]||"",ReplicationStatus:y[Hr.HeaderReplicationStatus]}},b=Jr(y);return b&&(g.data.RestoreInfo=b),g}async function di(e){throw new pe("getObjectToFile doesn't support in browser environment")}var fi;async function hi(e){throw new pe("`downloadFile` is not supported in browser environment")}function yi(e){return!e||80===e||443===e}(fi=e.DownloadEventType||(e.DownloadEventType={}))[fi.CreateTempFileSucceed=1]="CreateTempFileSucceed",fi[fi.CreateTempFileFailed=2]="CreateTempFileFailed",fi[fi.DownloadPartSucceed=3]="DownloadPartSucceed",fi[fi.DownloadPartFailed=4]="DownloadPartFailed",fi[fi.DownloadPartAborted=5]="DownloadPartAborted",fi[fi.RenameTempFileSucceed=6]="RenameTempFileSucceed",fi[fi.RenameTempFileFailed=7]="RenameTempFileFailed";const mi="request";class gi{constructor(e,t){this.options=void 0,this.credentials=void 0,this.signature=(e,t,n)=>{n||(n=this.credentials);const r=[],o=this.credentialString(e.datetime);return r.push(this.options.algorithm+" Credential="+n.GetAccessKey()+"/"+o),r.push("SignedHeaders="+this.signedHeaders(e)),r.push("Signature="+this.authorization(e,n,0)),r.join(", ")},this.signatureHeader=(e,t,n)=>{e.datetime=this.getDateTime();const r=new Map;e.headers||(e.headers={}),e.headers.host=""+e.host,yi(e.port)||(e.headers.host+=":"+e.port),e.endpoints&&(e.headers.host=`${this.options.bucket}.${e.endpoints}`),r.set("host",e.headers.host),r.set("x-tos-date",e.datetime),r.set("x-tos-content-sha256",this.hexEncodedBodyHash()),this.options.securityToken&&r.set("x-tos-security-token",this.options.securityToken),r.forEach((t,n)=>{n.startsWith("x-tos")&&(e.headers[n]=t)}),e.path=this.getEncodePath(e.path);const o=this.signature(e,0,n);return r.set("authorization",o),r},this.gnrCopySig=(e,t)=>({key:"",value:""}),this.getSignature=(e,t)=>({key:"",value:""}),this.getSignatureQuery=(e,t)=>{e.datetime=this.getDateTime(),e.headers||(e.headers={}),e.headers.host=""+e.host,yi(e.port)||(e.headers.host+=":"+e.port),e.path=this.getEncodePath(e.path),e.endpoints&&(e.headers.host=`${this.options.bucket}.${e.endpoints}`),e.headers["X-Tos-Date"]=e.datetime;const n=this.credentialString(e.datetime),r={...e.query||{},"X-Tos-Algorithm":this.options.algorithm,"X-Tos-Content-Sha256":this.hexEncodedBodyHash(),"X-Tos-Credential":this.credentials.GetAccessKey()+"/"+n,"X-Tos-Date":e.datetime,"X-Tos-Expires":""+t,"X-Tos-SignedHeaders":this.signedHeaders(e)};return this.options.securityToken&&(r["X-Tos-Security-Token"]=this.options.securityToken),e.query=br(r),r["X-Tos-Signature"]=this.authorization(e,this.credentials,t),r},this.getSignaturePolicyQuery=(e,t)=>{e.datetime=this.getDateTime();const n=this.credentialString(e.datetime),r={"X-Tos-Algorithm":this.options.algorithm,"X-Tos-Credential":this.credentials.GetAccessKey()+"/"+n,"X-Tos-Date":e.datetime,"X-Tos-Expires":""+t,"X-Tos-Policy":wo(xo(JSON.stringify(e.policy),"utf-8"),"base64")};return this.options.securityToken&&(r["X-Tos-Security-Token"]=this.options.securityToken),e.query=br(r),r["X-Tos-Signature"]=this.authorization(e,this.credentials,t),r},this.hexEncodedBodyHash=()=>"UNSIGNED-PAYLOAD",this.authorization=(e,t,n)=>{if(!e.datetime)return"";const r=this.getSigningKey(t,e.datetime.substr(0,8));return go(r,this.stringToSign(e.datetime,e),"hex")},this.getDateTime=()=>new Date((new Date).toUTCString()).toISOString().replace(/\..+/,"").replace(/-/g,"").replace(/:/g,"")+"Z",this.credentialString=e=>this.createScope(e.substr(0,8),this.options.region,this.options.serviceName),this.createScope=(e,t,n)=>[e.substr(0,8),t,n,mi].join("/"),this.getSigningKey=(e,t)=>{const n=go(e.GetSecretKey(),t),r=go(n,this.options.region),o=go(r,this.options.serviceName);return go(o,mi)},this.stringToSign=(e,t)=>{if(!this.options.algorithm)return"";const n=[];n.push(this.options.algorithm),n.push(e),n.push(this.credentialString(e));const r="policy"in t?this.canonicalStringPolicy(t):this.canonicalString(t);return n.push(this.hexEncodedHash(r)),n.join("\n")},this.hexEncodedHash=e=>bo(e,"hex"),this.canonicalString=e=>{const t=[];return t.push(e.method),t.push(e.path),t.push(this.getEncodePath(e.query,!1)),t.push(this.canonicalHeaders(e)+"\n"),t.push(this.signedHeaders(e)),t.push(this.hexEncodedBodyHash()),t.join("\n")},this.canonicalStringPolicy=e=>{const t=[];return t.push(this.getEncodePath(e.query,!1)),t.push(this.hexEncodedBodyHash()),t.join("\n")},this.canonicalHeaders=e=>{const t=[],n=vi(e.headers);for(let r of n){const n=e.headers[r];r=r.toLowerCase(),t.push(r+":"+this.canonicalHeaderValues(n.toString()))}return t.join("\n")},this.canonicalHeaderValues=e=>e.replace(/\s+/g," ").replace(/^\s+|\s+$/g,""),this.signedHeaders=e=>{const t=[],n=vi(e.headers);for(let e of n)e=e.toLowerCase(),t.push(e);return t.sort().join(";")},this.options=e,this.credentials=t}getEncodePath(e,t=!0){if(!e)return"";let n=e;return t&&(n=e.replace(/%2F/g,"/")),n=n.replace(/\(/g,"%28"),n=n.replace(/\)/g,"%29"),n=n.replace(/!/g,"%21"),n=n.replace(/\*/g,"%2A"),n=n.replace(/\'/g,"%27"),n}}class bi{constructor(e,t,n){this.securityToken=void 0,this.secretAccessKey=void 0,this.accessKeyId=void 0,this.accessKeyId=n,this.secretAccessKey=t,this.securityToken=e}GetAccessKey(){return this.accessKeyId}GetSecretKey(){return this.secretAccessKey}}function vi(e){const t=[];return Object.keys(e||{}).forEach(n=>{("host"===n||n.startsWith("x-tos-"))&&null!=e[n]&&t.push(n)}),t.sort()}let xi="wexin";function wi(e,{transformRequestOption:t=(e=>e)}={}){const n=function(){switch(!0){case"object"==typeof wx:return xi="wexin",wx.request.bind(wx);case"object"==typeof swan:return xi="baidu",swan.request.bind(swan);case"object"==typeof dd:return xi="dd",dd.httpRequest.bind(dd);case"object"==typeof my:return xi="alipay",(my.request||my.httpRequest).bind(my);default:return wx.request.bind(wx)}}();return new Promise((r,o)=>{let a,i=e.data,s=e.headers;const c={method:e.method&&e.method.toUpperCase()||"GET",url:l(v(e.baseURL,e.url),e.params,e.paramsSerializer),timeout:e.timeout,success:t=>{const n=function(e,t,n){const r=e.statusCode||e.status;let o="";return 200===r?o="OK":400===r&&(o="Bad Request"),{data:e.data,status:r,statusText:o,headers:e.header||e.headers,config:t,request:n}}(t,e,c);g(r,o,n)},fail:t=>{!function(e,t,n){switch(xi){case"wexin":-1!==e.errMsg.indexOf("request:fail abort")?t(m("Request aborted",n,"ECONNABORTED","")):-1!==e.errMsg.indexOf("timeout")?t(m("timeout of "+n.timeout+"ms exceeded",n,"ECONNABORTED","")):t(m("Network Error",n,null,""));break;case"dd":case"alipay":[14,19].includes(e.error)?t(m("Request aborted",n,"ECONNABORTED","",e)):[13].includes(e.error)?t(m("timeout of "+n.timeout+"ms exceeded",n,"ECONNABORTED","",e)):t(m("Network Error",n,null,"",e));break;case"baidu":t(m("Network Error",n,null,""))}}(t,o,e)},complete(){a=void 0}};if(e.auth){const[t,n]=[e.auth.username||"",e.auth.password||""];s.Authorization="Basic "+function(e){const t=String(e);let n,r,o=0,a="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",i="";for(;t.charAt(0|o)||(a="=",o%1);i+=a.charAt(63&n>>8-o%1*8)){if(r=t.charCodeAt(o+=3/4),r>255)throw new Error("'btoa' failed: The string to be encoded contains characters outside of the Latin1 range.");n=n<<8|r}return i}(t+":"+n)}u.forEach(s,(function(e,t){const n=t.toLowerCase();(void 0===i&&"content-type"===n||"referer"===n)&&delete s[t]})),c.header=s,e.responseType&&(c.responseType=e.responseType),e.cancelToken&&e.cancelToken.promise.then((function(e){a&&(a.abort(),o(e),a=void 0)})),(e=>{try{return"string"==typeof e&&e.length&&(e=JSON.parse(e))&&"[object Object]"===Object.prototype.toString.call(e)}catch(e){return!1}})(i)&&(i=JSON.parse(i)),void 0!==i&&(c.data=i),a=n(t(function(e){var t;return["alipay","dd"].includes(xi)&&(e.headers=e.header,delete e.header,"dd"===xi&&"GET"!==e.method&&"application/json"===(null==(t=e.headers)?void 0:t["Content-Type"])&&"[object Object]"===Object.prototype.toString.call(e.data)&&(e.data=JSON.stringify(e.data))),e}(c)))})}var Si,ki=function(e){if("post"===e.method){if(e.filePath&&e.name)return!0;if(function(e){return Array.isArray(e.files)&&e.files.length>0}(e))return!0}return!1},Ci=Object.prototype.toString,Ti=(Si=Object.create(null),function(e){var t=Ci.call(e);return Si[t]||(Si[t]=t.slice(8,-1).toLowerCase())});function Ei(e){return e=e.toLowerCase(),function(t){return Ti(t)===e}}function ji(e){return Array.isArray(e)}function Oi(e){return void 0===e}var Ri=Ei("ArrayBuffer");function Ai(e){return null!==e&&"object"==typeof e}function Pi(e){if("object"!==Ti(e))return!1;var t=Object.getPrototypeOf(e);return null===t||t===Object.prototype}var Bi=Ei("Date"),_i=Ei("File"),Ii=Ei("Blob"),Mi=Ei("FileList");function Di(e){return"[object Function]"===Ci.call(e)}var Ui=Ei("URLSearchParams");function Fi(e,t){if(null!=e)if("object"!=typeof e&&(e=[e]),ji(e))for(var n=0,r=e.length;n<r;n++)t.call(null,e[n],n,e);else for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.call(null,e[o],o,e)}var Ni=function(e){return function(t){return e&&t instanceof e}}("undefined"!=typeof Uint8Array&&Object.getPrototypeOf(Uint8Array)),Li={isArray:ji,isArrayBuffer:Ri,isBuffer:function(e){return null!==e&&!Oi(e)&&null!==e.constructor&&!Oi(e.constructor)&&"function"==typeof e.constructor.isBuffer&&e.constructor.isBuffer(e)},isFormData:function(e){return e&&("function"==typeof FormData&&e instanceof FormData||"[object FormData]"===Ci.call(e)||Di(e.toString)&&"[object FormData]"===e.toString())},isArrayBufferView:function(e){return"undefined"!=typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(e):e&&e.buffer&&Ri(e.buffer)},isString:function(e){return"string"==typeof e},isNumber:function(e){return"number"==typeof e},isObject:Ai,isPlainObject:Pi,isUndefined:Oi,isDate:Bi,isFile:_i,isBlob:Ii,isFunction:Di,isStream:function(e){return Ai(e)&&Di(e.pipe)},isURLSearchParams:Ui,isStandardBrowserEnv:function(){return("undefined"==typeof navigator||"ReactNative"!==navigator.product&&"NativeScript"!==navigator.product&&"NS"!==navigator.product)&&"undefined"!=typeof window&&"undefined"!=typeof document},forEach:Fi,merge:function e(){var t={};function n(n,r){t[r]=Pi(t[r])&&Pi(n)?e(t[r],n):Pi(n)?e({},n):ji(n)?n.slice():n}for(var r=0,o=arguments.length;r<o;r++)Fi(arguments[r],n);return t},extend:function(e,t,n){return Fi(t,(function(t,r){e[r]=n&&"function"==typeof t?function(e,t){return function(){for(var n=new Array(arguments.length),r=0;r<n.length;r++)n[r]=arguments[r];return e.apply(t,n)}}(t,n):t})),e},trim:function(e){return e.trim?e.trim():e.replace(/^\s+|\s+$/g,"")},stripBOM:function(e){return 65279===e.charCodeAt(0)&&(e=e.slice(1)),e},inherits:function(e,t,n,r){e.prototype=Object.create(t.prototype,r),e.prototype.constructor=e,n&&Object.assign(e.prototype,n)},toFlatObject:function(e,t,n){var r,o,a,i={};t=t||{};do{for(o=(r=Object.getOwnPropertyNames(e)).length;o-- >0;)i[a=r[o]]||(t[a]=e[a],i[a]=!0);e=Object.getPrototypeOf(e)}while(e&&(!n||n(e,t))&&e!==Object.prototype);return t},kindOf:Ti,kindOfTest:Ei,endsWith:function(e,t,n){e=String(e),(void 0===n||n>e.length)&&(n=e.length);var r=e.indexOf(t,n-=t.length);return-1!==r&&r===n},toArray:function(e){if(!e)return null;var t=e.length;if(Oi(t))return null;for(var n=new Array(t);t-- >0;)n[t]=e[t];return n},isTypedArray:Ni,isFileList:Mi};function zi(e,t,n,r,o){Error.call(this),this.message=e,this.name="AxiosError",t&&(this.code=t),n&&(this.config=n),r&&(this.request=r),o&&(this.response=o)}Li.inherits(zi,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:this.config,code:this.code,status:this.response&&this.response.status?this.response.status:null}}});var qi=zi.prototype,Ki={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED"].forEach((function(e){Ki[e]={value:e}})),Object.defineProperties(zi,Ki),Object.defineProperty(qi,"isAxiosError",{value:!0}),zi.from=function(e,t,n,r,o,a){var i=Object.create(qi);return Li.toFlatObject(e,i,(function(e){return e!==Error.prototype})),zi.call(i,e.message,t,n,r,o),i.name=e.name,a&&Object.assign(i,a),i};var Hi=zi;function $i(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}var Gi=function(e,t,n){if(!t)return e;var r;if(n)r=n(t);else if(Li.isURLSearchParams(t))r=t.toString();else{var o=[];Li.forEach(t,(function(e,t){null!=e&&(Li.isArray(e)?t+="[]":e=[e],Li.forEach(e,(function(e){Li.isDate(e)?e=e.toISOString():Li.isObject(e)&&(e=JSON.stringify(e)),o.push($i(t)+"="+$i(e))})))})),r=o.join("&")}if(r){var a=e.indexOf("#");-1!==a&&(e=e.slice(0,a)),e+=(-1===e.indexOf("?")?"?":"&")+r}return e};function Wi(e={}){return new Promise((function(t,n){const r=function(e,t,n){const r=(a=e.url,(o=e.baseURL)&&!/^([a-z][a-z\d+\-.]*:)?\/\//i.test(a)?function(e,t){return t?e.replace(/\/+$/,"")+"/"+t.replace(/^\/+/,""):e}(o,a):a);var o,a;const i=e.headers,s={...e,url:Gi(r,e.params,e.paramsSerializer),header:i};if(ki(e)?(delete i["Content-Type"],s.formData=e.formData?e.formData:"string"==typeof e.data?JSON.parse(e.data):e.data):s.data="get"===e.method?e.data?e.data:e.params:e.data,e.auth){var c=e.auth.username||"",u=unescape(encodeURIComponent(e.auth.password))||"";i.Authorization="Basic "+btoa(c+":"+u)}return s.complete=function(r){!function(e,t,n){var r=n.config.validateStatus;n.status&&r&&!r(n.status)?t(new Hi("Request failed with status code "+n.status,[Hi.ERR_BAD_REQUEST,Hi.ERR_BAD_RESPONSE][Math.floor(n.status/100)-4],n.config,n.request,n)):e(n)}(t,n,{data:r.data,status:r.statusCode,statusText:r.errMsg,header:r.header,config:e})},s}(e,t,n);let o=null;e.cancelToken&&e.cancelToken.promise.then((function(e){o&&(o.abort(),n(e),o=null)})),o=ki(e)?uni.uploadFile(r):uni.request(r)}))}var Vi=Wi;Vi.default=Wi;class Ji{constructor(e){this.opts=void 0,this.axiosInst=void 0,this.userAgent=void 0,this.httpAgent=void 0,this.httpsAgent=void 0,this.getObjectPath=e=>{const t="string"!=typeof e&&e.bucket||this.opts.bucket,n="string"==typeof e?e:e.key;if(!t)throw new pe("Must provide bucket param");return`/${t}/${encodeURIComponent(n)}`},this.setObjectContentTypeHeader=(e,t)=>{if(null!=t["content-type"])return;let n="application/octet-stream";const r=(e=>"string"==typeof e?e:e.key)(e);this.opts.autoRecognizeContentType&&(n=function(e){const t=e.lastIndexOf(".");if(t<=0)return;const n=e.slice(t+1).toLowerCase();return Nr[n]}(r)||n),n&&(t["content-type"]=n)},this.getNormalDataFromError=Pr,this.opts=this.normalizeOpts(e),this.userAgent=this.getUserAgent(),this.axiosInst=(e=>{const t=ce.create();t.defaults.auth=void 0,t.defaults.responseType="json",t.defaults.params=void 0,t.defaults.headers={},t.defaults.withCredentials=!1,t.defaults.maxContentLength=-1,t.defaults.maxBodyLength=-1,t.defaults.maxRedirects=0,t.defaults.validateStatus=function(e){return e>=200&&e<300},t.defaults.decompress=!1,t.defaults.transitional={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},t.interceptors.request.use(e=>e.headers?(Object.keys(e.headers).forEach(t=>{oo.includes(t.toLowerCase())&&delete e.headers[t]}),e):e);const n=e=>{var t;return e.headers=e.headers||e.header||(null==e||null==(t=e.response)?void 0:t.headers)||{},e};function r(e){Object.entries(e).forEach(([t,n])=>{const[r,o]=function(e){try{return[null,decodeURI(n)]}catch(e){return[e,null]}}();if(r||null==o||o===n)return;let a=[];const i=(""+n).match(/./gu),s=o.match(/./gu);for(let e=0,t=0;e<s.length;){const n=s[e];if(n===i[t]){a.push(n),++e,++t;continue}const r=encodeURIComponent(n);n.length>1||n.charCodeAt(0)>=128?a.push(n):a.push(r),++e,t+=r.length}e[t]=a.join("")})}return t.interceptors.response.use(n,e=>(n(e),Promise.reject(e))),t.interceptors.response.use(e=>e.headers?(r(e.headers),e):e,async e=>{var t;if(!ce.isAxiosError(e))return Promise.reject(e);const n=null==(t=e.response)?void 0:t.headers;return n?(r(n),Promise.reject(e)):Promise.reject(e)}),t.interceptors.response.use(void 0,async n=>{var r;const{config:o}=n;if(!o)return Promise.reject(n);o.__retryConfig__||(o.__retryConfig__={});const a=o.__retryConfig__,i=null!=(r=a.retryCount)?r:0;let s=o.data;if(!((function(e){var t;return!e.response&&Boolean(e.code)||e.response&&!(null!=(t=e.response.headers)&&t["x-tos-request-id"])}(n)||function(e){if(!e.response)return!1;const{status:t}=e.response;return 429===t||t>=500}(n))&&i<e))return Promise.reject(n);const c=o.__retrySignature__;if(c){const{signOpt:e,sigInst:t}=c;t.signatureHeader(e).forEach((e,t)=>{o.headers[t]=e})}ro("retryConfig: ",o);const u={...o,data:s,__retryConfig__:{...a,retryCount:i+1}};return null==a.beforeRetry||a.beforeRetry(),t(u)}),t})(this.opts.maxRetryCount)}normalizeOpts(e){var t,n;["accessKeyId","accessKeySecret","stsToken","region","endpoint"].forEach(t=>{const n=e[t];"string"==typeof n&&(e[t]=n.trim())});const r=["accessKeyId","accessKeySecret","region"].filter(t=>!e[t]).join(", ");if(r)throw new pe(`lack params: ${r}.`);const o=e.endpoint||`tos-${e.region}.volces.com`;if(!o)throw new pe("the value of param region is invalid, correct values are cn-beijing, cn-nantong etc.");if(o.includes("s3"))throw new pe("do not support s3 endpoint, please use tos endpoint.");const a=null==e.secure||!!e.secure,i=(e,t)=>null==e?t:e;if(null!=(t=e.enableCRC)&&t)throw new pe("not support crc in browser environment");return{...e,endpoint:o,secure:a,enableVerifySSL:i(e.enableVerifySSL,!0),autoRecognizeContentType:i(e.autoRecognizeContentType,!0),requestTimeout:i(e.requestTimeout,12e4),connectionTimeout:i(e.connectionTimeout,1e4),maxConnections:i(e.maxConnections,1024),idleConnectionTime:i(e.idleConnectionTime,3e4),maxRetryCount:i(e.maxRetryCount,3),enableCRC:null!=(n=e.enableCRC)&&n,requestAdapter:Qi()}}getUserAgent(){return"ve-tos-browserjs-sdk/v2.7.4"}async fetch(e,t,n,r,o,a){const i=(null==a?void 0:a.handleResponse)||(e=>e.data);if(o&&(null==a?void 0:a.needMd5)){const e=vo(JSON.stringify(o),"base64");r["content-md5"]=e}const[s,c]=(()=>null!=a&&a.subdomainBucket&&this.opts.forcePathStyle?[this.opts.endpoint,`/${a.subdomainBucket}${t}`]:null!=a&&a.subdomainBucket&&!this.opts.isCustomDomain?/^(\d|:)/.test(this.opts.endpoint)?[this.opts.endpoint,`/${a.subdomainBucket}${t}`]:[`${null==a?void 0:a.subdomainBucket}.${this.opts.endpoint}`,t]:[this.opts.endpoint,t])();t=c,r=(e=>{const t={};return Object.entries(e).forEach(([e,n])=>{t[e]=(""+n).match(/./gu).map(e=>e.length>1||e.charCodeAt(0)>=128?encodeURIComponent(e):e).join("")}),t})(r);const u={endpoints:void 0,bucket:"",method:e,headers:{...r},path:t,query:br(n),host:s},p=new bi(this.opts.stsToken,this.opts.accessKeySecret,this.opts.accessKeyId),l=new gi({algorithm:"TOS4-HMAC-SHA256",region:this.opts.region,serviceName:"tos",bucket:"",securityToken:this.opts.stsToken},p),d=l.signatureHeader(u),f={...r},h={method:e,baseURL:`http${this.opts.secure?"s":""}://${s}`,url:t,params:n,headers:f,data:o};d.forEach((e,t)=>{h.headers[t]=e});const y=xr(this.opts.proxy);if(null!=y&&y.url&&!this.opts.proxyHost)h.baseURL=y.url,null!=y&&y.needProxyParams&&(h.params["x-proxy-tos-host"]=s,delete f.host);else if(this.opts.proxyHost){if(!this.opts.proxyPort)throw new pe("The `proxyPort` is required if `proxyHost` is truly.");h.proxy={host:this.opts.proxyHost,port:this.opts.proxyPort,protocol:"http"}}f["user-agent"]=this.userAgent,this.opts.requestTimeout>0&&Infinity!==this.opts.requestTimeout&&(h.timeout=this.opts.requestTimeout);try{const e={...h};delete e.httpAgent,delete e.httpsAgent,ro("reqOpts: ",e);const t=await this.axiosInst({maxBodyLength:Infinity,maxContentLength:Infinity,adapter:this.opts.requestAdapter,...h,...(null==a?void 0:a.axiosOpts)||{},__retrySignature__:{signOpt:u,sigInst:l}});return{data:i(t),statusCode:t.status,headers:t.headers,requestId:t.headers["x-tos-request-id"],id2:t.headers["x-tos-id-2"]}}catch(e){var m,g;if(ce.isAxiosError(e)&&null!=(m=e.response)&&null!=(g=m.headers)&&g["x-tos-request-id"]){const t=e.response;throw ro("TosServerError response: ",t),new ue(t)}throw ro("err: ",e),e}}async fetchBucket(e,t,n,r,o,a){const i=e||this.opts.bucket;if(!i)throw new pe("Must provide bucket param");return this.fetch(t,"/",n,r,o,{...a,subdomainBucket:i})}async _fetchObject(e,t,n,r,o,a){const i="string"!=typeof e&&e.bucket||this.opts.bucket,s="string"==typeof e?e:e.key;if(!i)throw new pe("Must provide bucket param");return $r(s),this.fetch(t,"/"+encodeURIComponent(s),n,r,o,{...a,subdomainBucket:i})}getSignatureQuery(e){const t=new bi(this.opts.stsToken,this.opts.accessKeySecret,this.opts.accessKeyId),n=new gi({algorithm:"TOS4-HMAC-SHA256",region:this.opts.endpoint,serviceName:"tos",bucket:e.bucket,securityToken:this.opts.stsToken},t);return"policy"in e?n.getSignaturePolicyQuery({policy:e.policy},e.expires):n.getSignatureQuery({method:e.method,path:e.path,endpoints:e.subdomain?e.endpoint:void 0,host:e.endpoint,query:e.query},e.expires)}normalizeBucketInput(e){return"string"==typeof e?{bucket:e}:e}normalizeObjectInput(e){return"string"==typeof e?{key:e}:e}}function Qi(){if("undefined"==typeof window||void 0===window.location)switch(!0){case"undefined"!=typeof wx:case"undefined"!=typeof swan:case"undefined"!=typeof dd:case"undefined"!=typeof my:return wi;case"undefined"!=typeof uni:return Vi;default:return}}async function Xi(e={}){const{...t}=e,n=await this.fetchBucket(e.bucket,"GET",mr(t),{}),r=hr(n.data);return r("CommonPrefixes"),r("Contents"),r("Versions"),r("DeleteMarkers"),n}async function Zi(e={}){const{...t}=e,n=await this.fetchBucket(e.bucket,"GET",mr({versions:"",...t}),{}),r=hr(n.data);return r("CommonPrefixes"),r("Versions"),r("DeleteMarkers"),n}async function Yi(e={}){const{listOnlyOnce:t=!1}=e;let n;if(e.maxKeys||(e.maxKeys=1e3),t)n=await es.call(this,e);else{const t=e.maxKeys;let r={...e,maxKeys:t};for(;;){const e=await es.call(this,r);if(null==n?n=e:(n={...e,data:n.data},n.data.KeyCount+=e.data.KeyCount,n.data.IsTruncated=e.data.IsTruncated,n.data.NextContinuationToken=e.data.NextContinuationToken,n.data.Contents=n.data.Contents.concat(e.data.Contents),n.data.CommonPrefixes=n.data.CommonPrefixes.concat(e.data.CommonPrefixes)),!e.data.IsTruncated||n.data.KeyCount>=t)break;r.continuationToken=e.data.NextContinuationToken,r.maxKeys=r.maxKeys-e.data.KeyCount}}return n}async function es(e){const{...t}=e,n=await this.fetchBucket(e.bucket,"GET",{"list-type":2,...mr(t)},{}),r=hr(n.data);return r("CommonPrefixes"),r("Contents"),n}class ts extends Ji{modifyAxiosInst(){this.axiosInst.interceptors.request.use(e=>{const t=e.headers||{};return delete t.authorization,t.host=this.parsedPolicyUrlVal.host,e.baseURL=this.parsedPolicyUrlVal.origin,e.paramsSerializer=e=>{const t=Ar(e);return[this.parsedPolicyUrlVal.search,t].filter(e=>e.trim()).join("&")},e})}constructor(e){super({...e,bucket:"fake-bucket",region:"fake-region",accessKeyId:"fake-accessKeyId",accessKeySecret:"fake-accessKeySecret",endpoint:"fake-endpoint.com"}),this.shareLinkClientOpts=void 0,this.parsedPolicyUrlVal=void 0,this.headObject=Wo,this.getObjectV2=li,this.listObjects=Xi,this.listObjectsType2=Yi,this.listObjectVersions=Zi,this.downloadFile=hi,this.shareLinkClientOpts=e,this.parsedPolicyUrlVal=this.initParsedPolicyUrlVal(),this.modifyAxiosInst()}initParsedPolicyUrlVal(){const e=this.shareLinkClientOpts.policyUrl.match(/(https?:\/\/(?:[^@]+@)?([^/?]+))[^?]*\?(.+)/);if(!e)throw new pe("the `policyUrl` param is invalid");return{origin:e[1],host:e[2],search:e[3]}}}async function ns(e={}){const t={};(null==e?void 0:e.projectName)&&Rr({...e,headers:t},["projectName"]);const n=await this.fetch("GET","/",{},t);return hr(n.data)("Buckets"),n}async function rs(e){const t=e.bucket||this.opts.bucket;if(t){if(t.length<3||t.length>63)throw new pe("invalid bucket name, the length must be [3, 63]");if(!/^([a-z]|-|\d)+$/.test(t))throw new pe("invalid bucket name, the character set is illegal");if(/^-/.test(t)||/-$/.test(t))throw new pe("invalid bucket name, the bucket name can be neither starting with '-' nor ending with '-'")}const n=e.headers=vr(e.headers);return Rr(e,["acl","grantFullControl","grantRead","grantReadAcp","grantWrite","grantWriteAcp","storageClass","azRedundancy","bucketType"]),(null==e?void 0:e.projectName)&&Rr(e,["projectName"]),await this.fetchBucket(e.bucket,"PUT",{},n)}async function os(e){return this.fetchBucket(e,"DELETE",{},{})}async function as(e){return this.fetchBucket(e,"HEAD",{},{},void 0,{handleResponse:e=>({...e.headers,ProjectName:e.headers[Hr.HeaderProjectName]})})}async function is(e){const{bucket:t,storageClass:n}=e;return this.fetchBucket(t,"PUT",{storageClass:""},{"x-tos-storage-class":n})}async function ss(e){const t={};return e.acl&&(t["x-tos-acl"]=e.acl),await this.fetchBucket(e.bucket,"PUT",{acl:""},t,e.aclBody,{needMd5:!0})}async function cs(e){const t=await this.fetchBucket(e,"GET",{acl:""},{});return hr(t.data)("Grants"),t}async function us(e){return ps.call(this,e)}async function ps(t){const n=(t=this.normalizeObjectInput(t)).headers=vr(t.headers);Rr(t,["contentLength","contentMD5","contentSHA256","cacheControl","contentDisposition","contentEncoding","contentLanguage","contentType","expires","acl","grantFullControl","grantRead","grantReadAcp","grantWrite","grantWriteAcp","ssecAlgorithm","ssecKey","ssecKeyMD5","serverSideEncryption","serverSideDataEncryption","meta","websiteRedirectLocation","storageClass","trafficLimit","callback","callbackVar","forbidOverwrite","ifMatch"]),this.setObjectContentTypeHeader(t,n);const r=Gr(t.body,n),o=null!=r;o||!t.dataTransferStatusChange&&!t.progress||console.warn("Don't get totalSize of putObject's body, the `dataTransferStatusChange` and `progress` callback will not trigger. You can use `putObjectFromFile` instead");let a=0;const{dataTransferStatusChange:i,progress:s}=t,c=(t,n=0)=>{if(!o||n<0)return;if(!i&&!s)return;a+=n,null==i||i({type:t,rwOnceBytes:n,consumedBytes:a,totalBytes:r});const c=0===r?t===e.DataTransferType.Succeed?1:0:a/r;1===c?t===e.DataTransferType.Succeed&&(null==s||s(c)):null==s||s(c)},u=await Wr({body:t.body,dataTransferCallback:t=>c(e.DataTransferType.Rw,t),makeRetryStream:t.makeRetryStream,enableCRC:this.opts.enableCRC,rateLimiter:t.rateLimiter});c(e.DataTransferType.Started);const[p,l]=await wr((async()=>{const r=await this._fetchObject(t,"PUT",{},n,u.body||"",{handleResponse:e=>{var n;const r={...e.headers};return null!=(n=t)&&n.callback&&e.data&&(r.CallbackResult=""+JSON.stringify(e.data)),r},axiosOpts:{__retryConfig__:{beforeRetry:()=>{a=0,null==u.beforeRetry||u.beforeRetry()},makeRetryStream:u.makeRetryStream},onUploadProgress:t=>{c(e.DataTransferType.Rw,t.loaded-a)}}});return this.opts.enableCRC&&u.crc&&Br(u.crc,r.headers),r})());if(p||!l)throw c(e.DataTransferType.Failed),p;return c(e.DataTransferType.Succeed),l}async function ls(e){throw vr(e.headers),new pe("putObjectFromFile doesn't support in browser environment")}async function ds(e){const t=e.headers=vr(e.headers);return Rr(e,["acl","grantFullControl","grantRead","grantReadAcp","grantWriteAcp","ssecAlgorithm","ssecKey","ssecKeyMD5","meta","storageClass"]),await this._fetchObject(e,"POST",{fetch:""},t,{URL:e.url,IgnoreSameKey:e.ignoreSameKey,ContentMD5:e.contentMD5},{needMd5:!0})}async function fs(e){const t=e.headers=vr(e.headers);return Rr(e,["acl","grantFullControl","grantRead","grantReadAcp","grantWriteAcp","ssecAlgorithm","ssecKey","ssecKeyMD5","meta","storageClass"]),await this._fetchObject(e,"POST",{fetchTask:""},t,{URL:e.url,IgnoreSameKey:e.ignoreSameKey,ContentMD5:e.contentMD5,Object:e.key},{needMd5:!0})}function hs(e){$r(e);const t="string"==typeof e?{key:e}:e,n=t.alternativeEndpoint||this.opts.endpoint,r=!t.alternativeEndpoint&&!t.isCustomDomain,o=t.bucket||this.opts.bucket||"";if(r&&!o)throw new pe("Must provide bucket param");const[a,i,s]=(()=>{const e=encodeURIComponent(t.key),a=t.key.split("/").map(e=>encodeURIComponent(e)).join("/");return r?[`${o}.${n}`,"/"+a,"/"+e]:[n,"/"+a,"/"+e]})(),c=t.query||{},u=(e,t)=>{null==c[e]&&null!=t&&(c[e]=t)},p=t.response||{};Object.keys(p).forEach(e=>{const t=e,n=mr(t);u("response-"+n,p[t])}),t.versionId&&u("versionId",t.versionId);const l=this.getSignatureQuery({bucket:o,method:t.method||"GET",path:s,endpoint:n,subdomain:r,expires:t.expires||1800,query:c}),d=xr(this.opts.proxy);let f=`http${this.opts.secure?"s":""}://${a}`;return null!=d&&d.url&&(f=d.url.replace(/\/+$/g,""),null!=d&&d.needProxyParams&&(l["x-proxy-tos-host"]=a)),`${f}${i}?${Object.keys(l).map(e=>`${encodeURIComponent(e)}=${encodeURIComponent(l[e])}`).join("&")}`}async function ys(e){const t="string"==typeof e?{key:e}:e,n={};return t.versionId&&(n.versionId=t.versionId),t.skipTrash&&(n.skipTrash=t.skipTrash),t.recursive&&(n.recursive=t.recursive),await this._fetchObject(e,"DELETE",n,{},{},{handleResponse:e=>e.headers})}async function ms(e){return e.headers=e.headers||{},Rr(e,["recursiveMkdir","forbidOverwrite"]),this._fetchObject(e,"PUT",{rename:"",name:e.newKey},e.headers,"")}async function gs(e){const t={Quiet:e.quiet,Objects:e.objects.map(e=>({Key:e.key,VersionId:e.versionId}))},n={delete:""};e.skipTrash&&(n.skipTrash=e.skipTrash),e.recursive&&(n.recursive=e.recursive);const r=await this.fetchBucket(e.bucket,"POST",n,{},t),o=hr(r.data);return o("Deleted"),o("Error"),r}async function bs(e){const t="string"==typeof e?{key:e}:e,n={acl:""};t.versionId&&(n.versionId=t.versionId);const r=await this._fetchObject(e,"GET",n,{});return hr(r.data)("Grants"),r}async function vs(e){const t=e.headers=vr(e.headers),n={acl:""};return e.versionId&&(n.versionId=e.versionId),Rr(e,["acl"]),this._fetchObject(e,"PUT",n,t,e.aclBody)}async function xs(e){return this._fetchObject(e,"DELETE",{uploadId:e.uploadId},{})}async function ws(e={}){const{...t}=e,n=await this.fetchBucket(e.bucket,"GET",{uploads:"",...mr(t)},{}),r=hr(n.data);return r("Uploads"),r("CommonPrefixes"),n}async function Ss(t){const n=t=this.normalizeObjectInput(t),r=t.headers=vr(t.headers);Rr(t,["contentLength","cacheControl","contentDisposition","contentEncoding","contentLanguage","contentType","expires","acl","grantFullControl","grantRead","grantReadAcp","grantWriteAcp","meta","websiteRedirectLocation","storageClass","trafficLimit"]),this.setObjectContentTypeHeader(t,r);const o=Gr(t.body,r),a=null!=o;if(!a)throw new pe("appendObject needs to know the content length in advance");if(r["content-length"]=r["content-length"]||""+o,this.opts.enableCRC&&0!==t.offset&&!t.preHashCrc64ecma)throw new pe("must provide preHashCrc64ecma if enableCRC is true and offset is non-zero");let i=0;const{dataTransferStatusChange:s,progress:c}=t,u=(t,n=0)=>{if(!a||n<0)return;if(!s&&!c)return;i+=n,null==s||s({type:t,rwOnceBytes:n,consumedBytes:i,totalBytes:o});const r=0===o?t===e.DataTransferType.Succeed?1:0:i/o;1===r?t===e.DataTransferType.Succeed&&(null==c||c(r)):null==c||c(r)},p=await Wr({body:t.body,dataTransferCallback:t=>u(e.DataTransferType.Rw,t),makeRetryStream:void 0,enableCRC:this.opts.enableCRC,rateLimiter:t.rateLimiter});u(e.DataTransferType.Started);const[l,d]=await wr((async()=>{const a=await this._fetchObject(t,"POST",{append:"",offset:n.offset},r,p.body||"",{handleResponse:e=>({...e.headers,nextAppendOffset:+e.headers["x-tos-next-append-offset"],hashCrc64ecma:e.headers["x-tos-hash-crc64ecma"]}),axiosOpts:{__retryConfig__:{beforeRetry:()=>{i=0,null==p.beforeRetry||p.beforeRetry()},makeRetryStream:p.makeRetryStream},onUploadProgress:t=>{u(e.DataTransferType.Rw,t.loaded-i)}}});return this.opts.enableCRC&&p.crc&&Br(zr(n.preHashCrc64ecma||"0",p.crc.getCrc64(),o),a.headers),a})());if(l||!d)throw u(e.DataTransferType.Failed),l;return u(e.DataTransferType.Succeed),d}async function ks(e){const t="string"==typeof e?{key:e}:e,n=t.headers=vr(t.headers);Rr(t,["cacheControl","contentDisposition","contentEncoding","contentLanguage","contentType","expires","meta"]);const r={metadata:""};return t.versionId&&(r.versionId=t.versionId),this._fetchObject(e,"POST",r,n)}async function Cs(e){$r(e),e=this.normalizeObjectInput(e);const{expiresIn:t=3600,key:n}=e,r=e.bucket||this.opts.bucket,o={...e.fields},a=[...e.conditions||[]];if(!r)throw new pe("Must provide bucket param");const i=this.opts.accessKeySecret,s=new Date,c=Ts({date:new Date(s.valueOf()+1e3*t),type:"ISO"}),u=Ts(),p=u.substring(0,8),l="tos",d="request",f=go(i,p),h=go(f,this.opts.region),y=go(h,l),m=go(y,d),g={key:n,"x-tos-algorithm":"TOS4-HMAC-SHA256","x-tos-date":u,"x-tos-credential":[this.opts.accessKeyId,p,this.opts.region,l,d].join("/")};this.opts.stsToken&&(g["x-tos-security-token"]=this.opts.stsToken),a.push({bucket:r}),Object.entries(g).forEach(([e,t])=>{o[e]=t}),Object.entries(o).forEach(([e,t])=>{a.push({[e]:""+t})});const b=JSON.stringify({expiration:c,conditions:a}),v=wo(xo(b,"utf-8"),"base64"),x=go(m,v,"hex");return o.policy=v,o["x-tos-signature"]=x,o}function Ts(e){const{date:t=new Date,type:n="Z"}=e||{};return"ISO"===n?t.toISOString():t.toISOString().replace(/\..+/,"").replace(/-/g,"").replace(/:/g,"")+"Z"}const Es={getBucketCustomDomain:!0,getBucketIntelligenttiering:!0,getBucketInventory:!0,listBucketInventory:!0,getBucketMirrorBack:!0,getBucketNotification:!0,getBucketPolicy:!0,getBucketRealTimeLog:!0,getBucketReplication:!0,getBucketTagging:!0,getBucketWebsite:!0};function js(e,t){const{enableCatchEmptyServerError:n,methodKey:r,defaultResponse:o}=t;if(e instanceof ue)if(n){if(404===e.statusCode)return Pr(o,e)}else if(void 0===n&&404===e.statusCode&&Es[r])return Pr(o,e);throw e}async function Os(e){return!this.opts.enableOptimizeMethodBehavior&&void 0!==this.opts.enableOptimizeMethodBehavior||e.policy.Statement.length?await this.fetchBucket(e.bucket,"PUT",{policy:""},{},e.policy,{needMd5:!0}):As.call(this,e.bucket)}async function Rs(e){try{const t=await this.fetchBucket(e,"GET",{policy:""},{});return t.data.Statement.forEach(e=>{const t=hr(e);Object.keys(e.Condition||{}).forEach(n=>{Object.keys(e.Condition[n]).forEach(e=>{t(`Condition["${n}"]["${e}"]`)})})}),t}catch(e){return js(e,{enableCatchEmptyServerError:this.opts.enableOptimizeMethodBehavior,methodKey:"getBucketPolicy",defaultResponse:{Statement:[],Version:"2012-10-17"}})}}async function As(e){return this.fetchBucket(e,"DELETE",{policy:""},{})}async function Ps(e){return this.fetchBucket(e,"GET",{versioning:""},{})}async function Bs(e){return this.fetchBucket(e.bucket,"PUT",{versioning:""},{},{Status:e.status})}function _s(e){const t=Is.call(this,e);Ms(e.conditions);const n=`http${this.opts.secure?"s":""}://${e.alternativeEndpoint||(e.isCustomDomain?this.opts.endpoint:`${t.bucket}.${this.opts.endpoint}`)}`,r=Cr(this.getSignatureQuery({bucket:t.bucket,expires:t.expires,policy:{conditions:t.conditions}}));return{getSignedURLForList:e=>{const t=Cr(e),o=[r,t].filter(Boolean).join("&");return`${n}?${o}`},getSignedURLForGetOrHead:(e,t)=>{const o=Cr(t),a=[r,o].filter(Boolean).join("&"),i=e.split("/").map(e=>encodeURIComponent(e)).join("/");return`${n}/${i}?${a}`},signedQuery:r}}function Is(e){const t=e.bucket||this.opts.bucket;if(!t)throw new pe("Must provide bucket param");Ms(e.conditions);const n=e.conditions.map(e=>[e.operator||"eq","$key",e.value]);return n.push(["eq","$bucket",t]),{bucket:t,expires:e.expires||3600,conditions:n}}function Ms(e){if(e.length<1)throw new pe("The `conditions` field of `PreSignedPolicyURLInput` must has one item at least");for(const t of e){if("key"!==t.key)throw new pe("The `key` field of `PolicySignatureCondition` must be `'key'`");if(t.operator&&"eq"!==t.operator&&"starts-with"!==t.operator)throw new pe("The `operator` field of `PolicySignatureCondition` must be `'eq'` or `'starts-with'`")}}async function Ds(e){const{bucket:t}=e;return this.fetchBucket(t,"GET",{location:""},{})}async function Us(e){try{const{bucket:t}=e;return await this.fetchBucket(t,"GET",{cors:""},{})}catch(e){return js(e,{defaultResponse:{CORSRules:[]},enableCatchEmptyServerError:this.opts.enableOptimizeMethodBehavior,methodKey:"getBucketCORS"})}}async function Fs(e){const{bucket:t,CORSRules:n}=e;return this.opts.enableOptimizeMethodBehavior&&!n.length?Ns.call(this,{bucket:t}):this.fetchBucket(t,"PUT",{cors:""},{},{CORSRules:n})}async function Ns(e){const{bucket:t}=e;return this.fetchBucket(t,"DELETE",{cors:""},{})}async function Ls(e){const{bucket:t,rules:n}=e;if(this.opts.enableOptimizeMethodBehavior&&!n.length)return qs.call(this,{bucket:t});const r={};return Rr({...e,headers:r},["allowSameActionOverlap"]),this.fetchBucket(t,"PUT",{lifecycle:""},r,{Rules:n})}async function zs(e){try{const{bucket:t}=e;return await this.fetchBucket(t,"GET",{lifecycle:""},{},{},{handleResponse:e=>({AllowSameActionOverlap:e.headers["x-tos-allow-same-action-overlap"],Rules:e.data.Rules})})}catch(e){return js(e,{enableCatchEmptyServerError:this.opts.enableOptimizeMethodBehavior,methodKey:"getBucketLifecycle",defaultResponse:{Rules:[]}})}}async function qs(e){const{bucket:t}=e;return this.fetchBucket(t,"DELETE",{lifecycle:""},{})}async function Ks(e){const{bucket:t,rule:n}=e;return this.fetchBucket(t,"PUT",{encryption:""},{"Content-MD5":yo(JSON.stringify({Rule:n}),"base64")},{Rule:n})}async function Hs(e){const{bucket:t}=e;return this.fetchBucket(t,"GET",{encryption:""},{})}async function $s(e){const{bucket:t}=e;return this.fetchBucket(t,"DELETE",{encryption:""},{})}async function Gs(e){const{bucket:t,rules:n}=e;return this.opts.enableOptimizeMethodBehavior&&!n.length?Vs.call(this,{bucket:t}):this.fetchBucket(t,"PUT",{mirror:""},{},{Rules:n})}async function Ws(e){const{bucket:t}=e;try{return await this.fetchBucket(t,"GET",{mirror:""},{})}catch(e){return js(e,{enableCatchEmptyServerError:this.opts.enableOptimizeMethodBehavior,methodKey:"getBucketMirrorBack",defaultResponse:{Rules:[]}})}}async function Vs(e){const{bucket:t}=e;return this.fetchBucket(t,"DELETE",{mirror:""},{})}async function Js(e){const{tagSet:t,versionId:n}=e,r=vr({versionId:n});return this._fetchObject(e,"PUT",{tagging:"",...r},{},{TagSet:t})}async function Qs(e){const{versionId:t}=e,n=vr({versionId:t}),r=await this._fetchObject(e,"GET",{tagging:"",...n},{});return hr(r.data.TagSet)("Tags"),r}async function Xs(e){const{versionId:t}=e,n=vr({versionId:t});return this._fetchObject(e,"DELETE",{tagging:"",...n},{})}async function Zs(e){const{bucket:t,rules:n,role:r}=e;return this.opts.enableOptimizeMethodBehavior&&!n.length?ec.call(this,{bucket:t}):this.fetchBucket(t,"PUT",{replication:""},{},{Role:r,Rules:n})}async function Ys(e){const{bucket:t,progress:n,ruleId:r}=e,o={replication:"",progress:n||""};null!=r&&(o["rule-id"]=""+r);try{return await this.fetchBucket(t,"GET",o,{})}catch(e){return js(e,{enableCatchEmptyServerError:this.opts.enableOptimizeMethodBehavior,methodKey:"getBucketReplication",defaultResponse:{Rules:[],Role:""}})}}async function ec(e){const{bucket:t}=e;return this.fetchBucket(t,"DELETE",{replication:""},{})}async function tc(e){const{bucket:t,...n}=e,r=gr(n);return this.fetchBucket(t,"PUT",{website:""},{},{...r})}async function nc(e){const{bucket:t}=e;try{return this.fetchBucket(t,"GET",{website:""},{})}catch(e){return js(e,{enableCatchEmptyServerError:this.opts.enableOptimizeMethodBehavior,methodKey:"getBucketWebsite",defaultResponse:{RoutingRules:[]}})}}async function rc(e){const{bucket:t}=e;return this.fetchBucket(t,"DELETE",{website:""},{})}async function oc(e){const{bucket:t,...n}=e,r=gr(n);return this.fetchBucket(t,"PUT",{notification:""},{},{...r})}async function ac(e){const{bucket:t}=e;try{return await this.fetchBucket(t,"GET",{notification:""},{})}catch(e){return js(e,{enableCatchEmptyServerError:this.opts.enableOptimizeMethodBehavior,methodKey:"getBucketNotification",defaultResponse:{CloudFunctionConfigurations:[],RocketMQConfigurations:[]}})}}async function ic(e){const{bucket:t,...n}=e,r=gr(n);return this.fetchBucket(t,"PUT",{customdomain:""},{},{...r})}async function sc(e){try{const{bucket:t}=e;return await this.fetchBucket(t,"GET",{customdomain:""},{})}catch(e){return js(e,{defaultResponse:{CustomDomainRules:[]},enableCatchEmptyServerError:this.opts.enableOptimizeMethodBehavior,methodKey:"getBucketCustomDomain"})}}async function cc(e){const{bucket:t,customDomain:n}=e;return this.fetchBucket(t,"DELETE",{customdomain:n},{})}async function uc(e){const{bucket:t,...n}=e,r=gr(n);return this.fetchBucket(t,"PUT",{realtimeLog:""},{},{...r})}async function pc(e){const{bucket:t}=e;try{return await this.fetchBucket(t,"GET",{realtimeLog:""},{})}catch(e){return js(e,{enableCatchEmptyServerError:this.opts.enableOptimizeMethodBehavior,methodKey:"getBucketRealTimeLog",defaultResponse:{}})}}async function lc(e){const{bucket:t}=e;return this.fetchBucket(t,"DELETE",{realtimeLog:""},{})}var dc,fc,hc,yc,mc,gc,bc;async function vc(e){try{return await this.fetchBucket(e.bucket,"GET",{inventory:"",id:e.id},{})}catch(e){return js(e,{enableCatchEmptyServerError:this.opts.enableOptimizeMethodBehavior,methodKey:"getBucketInventory",defaultResponse:void 0})}}async function xc(e){const t={inventory:"",...e.continuationToken?{"continuation-token":e.continuationToken}:null};try{return await this.fetchBucket(e.bucket,"GET",t,{})}catch(e){return js(e,{enableCatchEmptyServerError:this.opts.enableOptimizeMethodBehavior,methodKey:"listBucketInventory",defaultResponse:{InventoryConfigurations:[]}})}}async function wc(e){return this.fetchBucket(e.bucket,"DELETE",{inventory:"",id:e.id},{})}function Sc(e){return this.fetchBucket(e.bucket,"PUT",{inventory:"",id:e.inventoryConfiguration.Id},{},e.inventoryConfiguration)}async function kc(e){const{accountId:t,...n}=e,r=gr(n);return await this.fetch("POST","/jobs",{},{"x-tos-account-id":t},{...r})}async function Cc(e){const{accountId:t,maxResults:n=1e3,...r}=e;return await this.fetch("GET","/jobs",{maxResults:n,...r},{"x-tos-account-id":t},{},{axiosOpts:{paramsSerializer:Ar}})}async function Tc(e){const{accountId:t,jobId:n,priority:r}=e;return await this.fetch("POST",`/jobs/${n}/priority`,{priority:r},{"x-tos-account-id":t},{},{needMd5:!0})}async function Ec(e){const{accountId:t,jobId:n,requestedJobStatus:r,statusUpdateReason:o}=e;return await this.fetch("POST",`/jobs/${n}/status`,{requestedJobStatus:r,statusUpdateReason:o},{"x-tos-account-id":t},{},{needMd5:!0})}async function jc(e){const{accountId:t,JobId:n}=e;return await this.fetch("DELETE","/jobs/"+n,{},{"x-tos-account-id":t},{})}async function Oc(e){const{accountId:t,JobId:n}=e;return await this.fetch("GET","/jobs/"+n,{},{"x-tos-account-id":t},{})}async function Rc(e){return await this.fetchBucket(e.bucket,"PUT",{tagging:""},{},e.tagging,{needMd5:!0})}async function Ac({bucket:e}){try{return await this.fetchBucket(e,"GET",{tagging:""},{})}catch(e){return js(e,{enableCatchEmptyServerError:this.opts.enableOptimizeMethodBehavior,methodKey:"getBucketTagging",defaultResponse:{TagSet:{Tags:[]}}})}}async function Pc({bucket:e}){return this.fetchBucket(e,"DELETE",{tagging:""},{})}async function Bc(e){return await this.fetchBucket(e.bucket,"PUT",{payByTraffic:""},{},e.payByTraffic)}async function _c({bucket:e}){return await this.fetchBucket(e,"GET",{payByTraffic:""},{})}async function Ic(e){const{bucket:t}=e;try{return await this.fetchBucket(t,"GET",{imageStyleBriefInfo:""},{})}catch(e){if(e instanceof ue&&404===e.statusCode)return this.getNormalDataFromError({BucketName:t,ImageStyleBriefInfo:[]},e);throw e}}async function Mc(e){try{return await this.fetchBucket(e,"GET",{imageStyle:""},{})}catch(e){if(e instanceof ue&&404===e.statusCode)return this.getNormalDataFromError({ImageStyles:[]},e);throw e}}async function Dc(e){try{const{bucket:t,styleName:n}=e;return await this.fetchBucket(t,"GET",{imageStyleContent:"",styleName:n},{})}catch(e){if(e instanceof ue&&404===e.statusCode)return this.getNormalDataFromError({ImageStyles:[]},e);throw e}}async function Uc(e,t){try{return await this.fetchBucket(e,"GET",{imageStyle:"",styleName:t},{})}catch(e){if(e instanceof ue&&404===e.statusCode)return this.getNormalDataFromError(null,e);throw e}}async function Fc(e){const{bucket:t,styleName:n,content:r,styleObjectPrefix:o}=e;try{return await this.fetchBucket(t,"PUT",{imageStyle:"",styleName:n,styleObjectPrefix:o},{},{Content:r})}catch(e){if(e instanceof ue&&404===e.statusCode)return this.getNormalDataFromError(null,e);throw e}}async function Nc(e){const{styleName:t,styleObjectPrefix:n,bucket:r}=e;try{return await this.fetchBucket(r,"DELETE",{imageStyle:"",styleName:t,styleObjectPrefix:n},{})}catch(e){if(e instanceof ue&&404===e.statusCode)return this.getNormalDataFromError(null,e);throw e}}async function Lc(e,t){try{return await this.fetchBucket(e,"PUT",{originalImageProtect:""},{},t)}catch(e){if(e instanceof ue&&404===e.statusCode)return this.getNormalDataFromError(null,e);throw e}}async function zc(e){try{return await this.fetchBucket(e,"GET",{originalImageProtect:""},{})}catch(e){if(e instanceof ue&&404===e.statusCode)return this.getNormalDataFromError(null,e);throw e}}async function qc(e){const{bucket:t,Separator:n,SeparatorSuffix:r}=e;try{return await this.fetchBucket(t,"PUT",{imageStyleSeparator:""},{},{Separator:n,SeparatorSuffix:r})}catch(e){if(e instanceof ue&&404===e.statusCode)return this.getNormalDataFromError(null,e);throw e}}async function Kc(e){try{return await this.fetchBucket(e,"GET",{imageStyleSeparator:""},{})}catch(e){if(e instanceof ue&&404===e.statusCode)return this.getNormalDataFromError(null,e);throw e}}async function Hc(e){try{return await this.fetchBucket(e,"GET",{intelligenttiering:""},{})}catch(e){return js(e,{enableCatchEmptyServerError:this.opts.enableOptimizeMethodBehavior,methodKey:"getBucketIntelligenttiering",defaultResponse:{}})}}async function $c(e){const{bucket:t,...n}=e,r=gr(n);return this.fetchBucket(t,"PUT",{rename:""},{},{...r})}async function Gc(e){const{bucket:t}=e;return await this.fetchBucket(t,"GET",{rename:""},{})}async function Wc(e){const{bucket:t}=e;return this.fetchBucket(t,"DELETE",{rename:""},{})}async function Vc(e){const{versionId:t,...n}=e,r={restore:""};t&&(r.versionId=t);const o=gr(n);return this._fetchObject(e,"POST",r,{},o)}async function Jc(e){const{accountId:t}=e;return await this.fetch("GET","/storagelens",{},{"x-tos-account-id":t},{},{axiosOpts:{paramsSerializer:Ar}})}async function Qc(e){const{accountId:t,Id:n}=e;return await this.fetch("DELETE","/storagelens",{id:n},{"x-tos-account-id":t},{},{needMd5:!0})}async function Xc(e){const{accountId:t,Id:n}=e;return await this.fetch("GET","/storagelens",{id:n},{"x-tos-account-id":t},{},{needMd5:!0})}async function Zc(e){const{accountId:t,Id:n,...r}=e;return await this.fetch("PUT","/storagelens",{id:n},{"x-tos-account-id":t},{...r,Id:n},{needMd5:!0})}async function Yc(e){const{bucket:t,...n}=e,r=gr(n);return this.fetchBucket(t,"PUT",{notification_v2:""},{},{...r})}async function eu(e){const{bucket:t}=e;try{return await this.fetchBucket(t,"GET",{notification_v2:""},{})}catch(e){return js(e,{enableCatchEmptyServerError:this.opts.enableOptimizeMethodBehavior,methodKey:"getBucketNotificationType2",defaultResponse:{Rules:[]}})}}async function tu(e){return nu.call(this,e)}async function nu(e){const t=e.headers=vr(e.headers);return Rr(e,["symLinkTargetKey","symLinkTargetBucket","forbidOverwrite","acl","storageClass","meta"]),this._fetchObject(e,"PUT",{symlink:""},t,void 0,{handleResponse(e){const{headers:t}=e;return{VersionID:t["x-tos-version-id"]}}})}async function ru(e){return ou.call(this,e)}async function ou(e){const t={symlink:""};return e.versionId&&(t.versionId=e.versionId),this._fetchObject(e,"GET",t,{},void 0,{handleResponse:e=>{const{headers:t}=e;return{VersionID:t["x-tos-version-id"],SymlinkTargetKey:t["x-tos-symlink-target"],SymlinkTargetBucket:t["x-tos-symlink-bucket"],LastModified:t["last-modified"]}}})}async function au(e){const{bucket:t,...n}=e,r=gr(n);return this.fetchBucket(t,"PUT",{transferAcceleration:""},{},{...r})}async function iu(t){try{const{bucket:e}=t,n={};return t.getStatus&&(n["x-tos-get-bucket-acceleration-status"]="true"),await this.fetchBucket(e,"GET",{transferAcceleration:""},n)}catch(t){return js(t,{enableCatchEmptyServerError:this.opts.enableOptimizeMethodBehavior,methodKey:"getBucketTransferAcceleration",defaultResponse:{TransferAccelerationConfiguration:{Enabled:"false",Status:e.TransferAccelerationStatusType.Terminated}}})}}async function su(e){const{bucket:t,status:n}=e;return this.fetchBucket(t,"PUT",{accessmonitor:""},{},{Status:n})}async function cu(e){try{const{bucket:t}=e;return await this.fetchBucket(t,"GET",{accessmonitor:""},{})}catch(e){return js(e,{enableCatchEmptyServerError:this.opts.enableOptimizeMethodBehavior,methodKey:"getBucketAccessMonitor",defaultResponse:{}})}}async function uu(e){const{accountId:t}=e;return await this.fetch("GET","/qospolicy",{},{"x-tos-account-id":t},{},{})}async function pu(e){const{accountId:t,...n}=e;return await this.fetch("PUT","/qospolicy",{},{"x-tos-account-id":t},{...n},{})}async function lu(e){const{accountId:t}=e;return await this.fetch("DELETE","/qospolicy",{},{"x-tos-account-id":t},{},{})}async function du(e){const{accountId:t,name:n,regions:r}=e;return await this.fetch("POST","/mrap",{name:n},{"x-tos-account-id":t},{Name:n,Regions:r},{})}async function fu(e){const{name:t,accountId:n}=e;return await this.fetch("GET","/mrap",{name:t},{"x-tos-account-id":n},{},{})}async function hu(e){const{accountId:t,...n}=e;return await this.fetch("GET","/mrap",{...n},{"x-tos-account-id":t},{},{})}async function yu(e){const{accountId:t,alias:n}=e;return await this.fetch("GET","/mrap/routes",{alias:n},{"x-tos-account-id":t})}async function mu(e){const{name:t,accountId:n}=e;return await this.fetch("DELETE","/mrap",{name:t},{"x-tos-account-id":n})}async function gu(e){const{routes:t,accountId:n,alias:r}=e;return await this.fetch("PATCH","/mrap/routes",{alias:r},{"x-tos-account-id":n},{Routes:t})}!function(e){e.Daily="Daily",e.Weekly="Weekly"}(dc||(dc={})),function(e){e.All="All",e.Current="Current"}(fc||(fc={})),function(e){e.Size="Size",e.LastModifiedDat="LastModifiedDate",e.ETag="ETag",e.StorageClass="StorageClass",e.IsMultipartUploaded="IsMultipartUploaded",e.EncryptionStatus="EncryptionStatus",e.CRC64="CRC64",e.ReplicationStatus="ReplicationStatus"}(hc||(hc={})),function(e){e.StringEquals="StringEquals",e.StringNotEquals="StringNotEquals",e.StringEqualsIgnoreCase="StringEqualsIgnoreCase",e.StringNotEqualsIgnoreCase="StringNotEqualsIgnoreCase",e.StringLike="StringLike",e.StringNotLike="StringNotLike"}(yc||(yc={})),function(e){e.DateEquals="DateEquals",e.DateNotEquals="DateNotEquals",e.DateLessThan="DateLessThan",e.DateLessThanEquals="DateLessThanEquals",e.DateGreaterThan="DateGreaterThan",e.DateGreaterThanEquals="DateGreaterThanEquals"}(mc||(mc={})),function(e){e.IpAddress="IpAddress",e.NotIpAddress="NotIpAddress"}(gc||(gc={})),function(e){e.WritesQps="WritesQps",e.ReadsQps="ReadsQps",e.ListQps="ListQps",e.WritesRate="WritesRate",e.ReadsRate="ReadsRate"}(bc||(bc={}));const bu=async function(e){const{accountId:t,alias:n,rules:r}=e;return this.opts.enableOptimizeMethodBehavior&&!r.length?xu.call(this,{accountId:t,alias:n}):await this.fetch("PUT","/mrap/mirror",{alias:n},{"x-tos-account-id":t},{Rules:r},{handleResponse:()=>({})})},vu=async function(e){const{accountId:t,alias:n}=e;try{const e=await this.fetch("GET","/mrap/mirror",{alias:n},{"x-tos-account-id":t},{},{});return hr(e.data)("Rules"),e}catch(e){return js(e,{enableCatchEmptyServerError:this.opts.enableOptimizeMethodBehavior,methodKey:"getMultiRegionAccessPointMirrorBack",defaultResponse:{Rules:[]}})}},xu=async function(e){const{accountId:t,alias:n}=e;return await this.fetch("DELETE","/mrap/mirror",{alias:n},{"x-tos-account-id":t},{},{handleResponse:()=>({})})};async function wu(e){const{bucket:t,enable:n}=e;return await this.fetchBucket(t,"PUT",{privateM3U8:""},{},{Enable:n})}async function Su(e){const{bucket:t}=e;try{return await this.fetchBucket(t,"GET",{privateM3U8:""},{})}catch(e){return js(e,{enableCatchEmptyServerError:this.opts.enableOptimizeMethodBehavior,methodKey:"getBucketPrivateM3U8",defaultResponse:{Enable:!1}})}}async function ku(e){const{bucket:t,...n}=e,r=gr(n);return this.fetchBucket(t,"PUT",{trash:""},{},{...r})}async function Cu(e){const{bucket:t}=e;return await this.fetchBucket(t,"GET",{trash:""},{})}class Tu extends Ji{constructor(...e){super(...e),this.createBucket=rs,this.headBucket=as,this.deleteBucket=os,this.listBuckets=ns,this.getBucketLocation=Ds,this.putBucketStorageClass=is,this.getBucketAcl=cs,this.putBucketAcl=ss,this.getBucketPolicy=Rs,this.putBucketPolicy=Os,this.deleteBucketPolicy=As,this.getBucketVersioning=Ps,this.putBucketVersioning=Bs,this.getBucketCORS=Us,this.putBucketCORS=Fs,this.deleteBucketCORS=Ns,this.putBucketLifecycle=Ls,this.getBucketLifecycle=zs,this.deleteBucketLifecycle=qs,this.putBucketEncryption=Ks,this.getBucketEncryption=Hs,this.deleteBucketEncryption=$s,this.putBucketMirrorBack=Gs,this.getBucketMirrorBack=Ws,this.deleteBucketMirrorBack=Vs,this.putBucketReplication=Zs,this.getBucketReplication=Ys,this.deleteBucketReplication=ec,this.putBucketWebsite=tc,this.getBucketWebsite=nc,this.deleteBucketWebsite=rc,this.putBucketNotification=oc,this.getBucketNotification=ac,this.putBucketCustomDomain=ic,this.getBucketCustomDomain=sc,this.deleteBucketCustomDomain=cc,this.putBucketRealTimeLog=uc,this.getBucketRealTimeLog=pc,this.deleteBucketRealTimeLog=lc,this.getBucketInventory=vc,this.listBucketInventory=xc,this.putBucketInventory=Sc,this.deleteBucketInventory=wc,this.putBucketTagging=Rc,this.getBucketTagging=Ac,this.deleteBucketTagging=Pc,this.putBucketPayByTraffic=Bc,this.getBucketPayByTraffic=_c,this.getBucketImageStyle=Uc,this.getBucketImageStyleList=Mc,this.getBucketImageStyleListByName=Dc,this.getImageStyleBriefInfo=Ic,this.deleteBucketImageStyle=Nc,this.putBucketImageStyle=Fc,this.putBucketImageStyleSeparator=qc,this.putBucketImageProtect=Lc,this.getBucketImageProtect=zc,this.getBucketImageStyleSeparator=Kc,this.putBucketRename=$c,this.getBucketRename=Gc,this.deleteBucketRename=Wc,this.putBucketTransferAcceleration=au,this.getBucketTransferAcceleration=iu,this.copyObject=Jo,this.resumableCopyObject=si,this.deleteObject=ys,this.deleteMultiObjects=gs,this.getObject=ui,this.getObjectV2=li,this.getObjectToFile=di,this.getObjectAcl=bs,this.headObject=Wo,this.appendObject=Ss,this.listObjects=Xi,this.renameObject=ms,this.fetchObject=ds,this.putFetchTask=fs,this.listObjectsType2=Yi,this.listObjectVersions=Zi,this.putObject=us,this.putObjectFromFile=ls,this.putObjectAcl=vs,this.setObjectMeta=ks,this.createMultipartUpload=Dr,this.uploadPart=ko,this.uploadPartFromFile=Co,this.completeMultipartUpload=To,this.abortMultipartUpload=xs,this.uploadPartCopy=Vo,this.listMultipartUploads=ws,this.listParts=Fr,this.downloadFile=hi,this.putObjectTagging=Js,this.getObjectTagging=Qs,this.deleteObjectTagging=Xs,this.listJobs=Cc,this.createJob=kc,this.deleteJob=jc,this.describeJob=Oc,this.updateJobStatus=Ec,this.updateJobPriority=Tc,this.restoreObject=Vc,this.uploadFile=Oo,this.getPreSignedUrl=hs,this.calculatePostSignature=Cs,this.preSignedPostSignature=Cs,this.preSignedPolicyURL=_s,this.getBucketIntelligenttiering=Hc,this.listStorageLens=Jc,this.deleteStorageLens=Qc,this.getStorageLens=Xc,this.putStorageLens=Zc,this.putBucketNotificationType2=Yc,this.getBucketNotificationType2=eu,this.putSymlink=tu,this.getSymlink=ru,this.putBucketAccessMonitor=su,this.getBucketAccessMonitor=cu,this.putQosPolicy=pu,this.getQosPolicy=uu,this.deleteQosPolicy=lu,this.createMultiRegionAccessPoint=du,this.getMultiRegionAccessPoint=fu,this.listMultiRegionAccessPoints=hu,this.getMultiRegionAccessPointRoutes=yu,this.deleteMultiRegionAccessPoint=mu,this.submitMultiRegionAccessPointRoutes=gu,this.putMultiRegionAccessPointMirrorBack=bu,this.getMultiRegionAccessPointMirrorBack=vu,this.deleteMultiRegionAccessPointMirrorBack=xu,this.putBucketPrivateM3U8=wu,this.getBucketPrivateM3U8=Su,this.putBucketTrash=ku,this.getBucketTrash=Cu}}const Eu=ce.CancelToken;class ju extends Tu{}ju.TosServerError=ue,ju.isCancel=Tr,ju.CancelError=dt,ju.TosServerCode=e.TosServerCode,ju.TosClientError=pe,ju.CancelToken=Eu,ju.ACLType=e.ACLType,ju.StorageClassType=e.StorageClassType,ju.MetadataDirectiveType=e.MetadataDirectiveType,ju.AzRedundancyType=e.AzRedundancyType,ju.PermissionType=e.PermissionType,ju.GranteeType=e.GranteeType,ju.CannedType=e.CannedType,ju.HttpMethodType=e.HttpMethodType,ju.LifecycleStatusType=e.LifecycleStatusType,ju.StatusType=e.StatusType,ju.RedirectType=e.RedirectType,ju.StorageClassInheritDirectiveType=e.StorageClassInheritDirectiveType,ju.TierType=e.TierType,ju.VersioningStatusType=e.VersioningStatusType,ju.createDefaultRateLimiter=Kr,ju.DataTransferType=e.DataTransferType,ju.UploadEventType=e.UploadEventType,ju.DownloadEventType=e.DownloadEventType,ju.ResumableCopyEventType=e.ResumableCopyEventType,ju.ReplicationStatusType=e.ReplicationStatusType,ju.AccessPointStatusType=e.AccessPointStatusType,ju.TransferAccelerationStatusType=e.TransferAccelerationStatusType,ju.MRAPMirrorBackRedirectPolicyType=e.MRAPMirrorBackRedirectPolicyType,ju.ShareLinkClient=ts,"undefined"!=typeof window&&(window.TOS=ju,window.TosClient=ju),"undefined"!=typeof global&&(global.TOS=ju,global.TosClient=ju),"undefined"!=typeof globalThis&&(globalThis.TOS=ju,globalThis.TosClient=ju),e.CancelError=dt,e.CancelToken=Eu,e.ShareLinkClient=ts,e.TOS=ju,e.TosClient=ju,e.TosClientError=pe,e.TosServerError=ue,e.createDefaultRateLimiter=Kr,e.default=ju,e.isCancel=Tr,Object.defineProperty(e,"__esModule",{value:!0})}));
//# sourceMappingURL=tos.umd.production.min.js.map
