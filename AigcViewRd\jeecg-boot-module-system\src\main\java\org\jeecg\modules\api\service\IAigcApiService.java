package org.jeecg.modules.api.service;

import org.jeecg.modules.demo.userprofile.entity.AicgUserProfile;
import org.jeecg.modules.api.dto.PluginVerifyResult;
import org.jeecg.modules.demo.plubshop.entity.AigcPlubShop;
import java.math.BigDecimal;

/**
 * @Description: 智界Aigc API服务接口
 * @Author: jeecg-boot
 * @Date: 2025-06-14
 * @Version: V1.0
 */
public interface IAigcApiService {

    /**
     * 根据API-Key查询用户信息
     * @param apiKey API密钥
     * @return 用户扩展信息
     */
    AicgUserProfile getUserByApiKey(String apiKey);

    /**
     * 检查API调用频率限制
     * @param userId 用户ID
     * @return 是否允许调用
     */
    boolean checkRateLimit(String userId);

    /**
     * 记录API使用记录
     * @param userId 用户ID
     * @param apiType API类型
     * @param details 详细信息
     */
    void recordApiUsage(String userId, String apiType, String details);

    /**
     * 验证API签名
     * @param apiKey API密钥
     * @param content 内容
     * @param timestamp 时间戳
     * @param signature 签名
     * @return 是否验证通过
     */
    boolean verifySignature(String apiKey, String content, String timestamp, String signature);

    /**
     * 验证插件调用权限并扣费
     * @param userId 用户ID
     * @param pluginKey 插件唯一标识
     * @return 验证结果信息
     */
    PluginVerifyResult verifyPluginAndDeduct(String userId, String pluginKey);
    
    /**
     * 检查HTML内容安全性
     * @param htmlContent HTML内容
     * @return 安全检查结果
     */
    String checkHtmlSecurity(String htmlContent);
    
    /**
     * 扣减用户余额
     * @param userId 用户ID
     * @param amount 扣减金额
     * @param description 描述
     * @return 是否成功
     */
    boolean deductBalance(String userId, java.math.BigDecimal amount, String description);
    
    /**
     * 获取用户API调用统计
     * @param userId 用户ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 调用统计
     */
    java.util.Map<String, Object> getApiUsageStats(String userId, java.util.Date startDate, java.util.Date endDate);

    /**
     * 🔥 数据联动更新 - 插件调用成功后的完整数据联动
     * @param userId 用户ID
     * @param plugin 插件信息
     * @param amount 金额
     */
    void performDataLinkage(String userId, AigcPlubShop plugin, BigDecimal amount);
}
