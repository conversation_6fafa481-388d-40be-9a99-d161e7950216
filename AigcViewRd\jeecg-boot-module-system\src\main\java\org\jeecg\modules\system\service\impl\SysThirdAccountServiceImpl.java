package org.jeecg.modules.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.jeecg.common.constant.CommonConstant;
import org.jeecg.common.util.DateUtils;
import org.jeecg.common.util.PasswordUtil;
import org.jeecg.common.util.UUIDGenerator;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.system.entity.SysRole;
import org.jeecg.modules.system.entity.SysThirdAccount;
import org.jeecg.modules.system.entity.SysUser;
import org.jeecg.modules.system.entity.SysUserRole;
import org.jeecg.modules.system.mapper.SysRoleMapper;
import org.jeecg.modules.system.mapper.SysThirdAccountMapper;
import org.jeecg.modules.system.mapper.SysUserMapper;
import org.jeecg.modules.system.mapper.SysUserRoleMapper;
import org.jeecg.modules.system.model.ThirdLoginModel;
import org.jeecg.modules.system.service.ISysThirdAccountService;
import org.jeecg.modules.demo.userprofile.entity.AicgUserProfile;
import org.jeecg.modules.demo.userprofile.service.IAicgUserProfileService;
import org.jeecg.common.system.api.ISysBaseAPI;
import org.jeecg.common.api.dto.message.MessageDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @Description: 第三方登录账号表
 * @Author: jeecg-boot
 * @Date:   2020-11-17
 * @Version: V1.0
 */
@Service
public class SysThirdAccountServiceImpl extends ServiceImpl<SysThirdAccountMapper, SysThirdAccount> implements ISysThirdAccountService {
    
    @Autowired
    private  SysThirdAccountMapper sysThirdAccountMapper;
    
    @Autowired
    private SysUserMapper sysUserMapper;
    @Autowired
    private SysRoleMapper sysRoleMapper;
    @Autowired
    private SysUserRoleMapper sysUserRoleMapper;
    @Autowired
    private IAicgUserProfileService userProfileService;
    @Autowired
    private ISysBaseAPI sysBaseAPI;
    
    @Override
    public void updateThirdUserId(SysUser sysUser,String thirdUserUuid) {
        //修改第三方登录账户表使其进行添加用户id
        LambdaQueryWrapper<SysThirdAccount> query = new LambdaQueryWrapper<>();
        query.eq(SysThirdAccount::getThirdUserUuid,thirdUserUuid);
        SysThirdAccount account = sysThirdAccountMapper.selectOne(query);
        SysThirdAccount sysThirdAccount = new SysThirdAccount();
        sysThirdAccount.setSysUserId(sysUser.getId());
        //根据当前用户id和登录方式查询第三方登录表
        LambdaQueryWrapper<SysThirdAccount> thirdQuery = new LambdaQueryWrapper<>();
        thirdQuery.eq(SysThirdAccount::getSysUserId,sysUser.getId());
        thirdQuery.eq(SysThirdAccount::getThirdType,account.getThirdType());
        SysThirdAccount sysThirdAccounts = sysThirdAccountMapper.selectOne(thirdQuery);
        if(sysThirdAccounts!=null){
            sysThirdAccount.setThirdUserId(sysThirdAccounts.getThirdUserId());
            sysThirdAccountMapper.deleteById(sysThirdAccounts.getId());
        }
        //更新用户账户表sys_user_id
        sysThirdAccountMapper.update(sysThirdAccount,query);
    }
    
    @Override
    public SysUser createUser(String phone, String thirdUserUuid) {
       //先查询第三方，获取登录方式
        LambdaQueryWrapper<SysThirdAccount> query = new LambdaQueryWrapper<>();
        query.eq(SysThirdAccount::getThirdUserUuid,thirdUserUuid);
        SysThirdAccount account = sysThirdAccountMapper.selectOne(query);
        //通过用户名查询数据库是否已存在
        SysUser userByName = sysUserMapper.getUserByName(thirdUserUuid);
        if(null!=userByName){
            //如果账号存在的话，则自动加上一个时间戳
            String format = DateUtils.yyyymmddhhmmss.get().format(new Date());
            thirdUserUuid = thirdUserUuid + format;
        }
        //添加用户
        SysUser user = new SysUser();
        user.setActivitiSync(CommonConstant.ACT_SYNC_0);
        user.setDelFlag(CommonConstant.DEL_FLAG_0);
        user.setStatus(1);
        user.setUsername(thirdUserUuid);
        user.setPhone(phone);
        //设置初始密码
        String salt = oConvertUtils.randomGen(8);
        user.setSalt(salt);
        String passwordEncode = PasswordUtil.encrypt(user.getUsername(), "123456", salt);
        user.setPassword(passwordEncode);
        user.setRealname(account.getRealname());
        user.setAvatar(account.getAvatar());
        String s = this.saveThirdUser(user);

        // 🔑 关键：创建用户扩展信息，并设置密码状态
        try {
            AicgUserProfile userProfile = new AicgUserProfile();
            userProfile.setUserId(s);
            userProfile.setUsername(user.getUsername());
            userProfile.setNickname(user.getRealname());
            userProfile.setPhone(user.getPhone());
            userProfile.setAvatar(user.getAvatar());
            userProfile.setAccountBalance(BigDecimal.ZERO);
            userProfile.setTotalConsumption(BigDecimal.ZERO);
            userProfile.setTotalRecharge(BigDecimal.ZERO);
            userProfile.setInviteCount(0);
            userProfile.setRegisterSource("third_party"); // 第三方注册
            userProfile.setPasswordChanged(0); // 第三方登录用户使用默认密码"123456"，但用户不知道，标记为未修改
            userProfile.setCreateTime(new Date());

            // 生成API Key
            String apiKey = "ak_" + oConvertUtils.randomGen(32);
            userProfile.setApiKey(apiKey);

            // 生成邀请码
            String inviteCode = "INV" + s.substring(s.length() - 8).toUpperCase();
            userProfile.setMyInviteCode(inviteCode);

            userProfileService.save(userProfile);
        } catch (Exception e) {
            // 如果用户扩展信息创建失败，记录日志但不影响主流程
            System.err.println("创建第三方用户扩展信息失败: " + e.getMessage());
        }

        // 发送密码修改提醒通知
        try {
            String title = "安全提醒：建议修改登录密码";
            String content = "为了您的账户安全，建议您尽快修改登录密码。\n\n" +
                           "您通过第三方登录注册，系统已为您设置默认密码\"123456\"。" +
                           "为了方便您使用密码登录，请及时修改为您熟悉的密码。\n\n" +
                           "修改路径：个人中心 → 账户设置 → 安全设置 → 登录密码\n\n" +
                           "密码要求：至少8位，包含字母和数字\n\n" +
                           "修改密码后，您可以使用新密码进行登录，提升使用便利性。";

            // 🛡️ 关键修复：获取用户名而不是直接使用用户ID
            SysUser thirdUser = sysUserMapper.selectById(s);
            if (thirdUser != null) {
                String username = thirdUser.getUsername();
                System.out.println("🔔 准备发送第三方用户密码修改提醒通知，用户ID：" + s + "，用户名：" + username);

                // 使用系统通告发送通知（传入用户名）
                MessageDTO message = new MessageDTO("system", username, title, content);
                sysBaseAPI.sendSysAnnouncement(message);
                System.out.println("✅ 已发送密码修改提醒通知，第三方用户ID：" + s + "，用户名：" + username);
            } else {
                System.err.println("❌ 发送第三方用户密码修改提醒通知失败：用户不存在，用户ID：" + s);
            }
        } catch (Exception e) {
            System.err.println("❌ 发送第三方用户密码修改提醒通知失败: " + e.getMessage());
        }

        //更新用户第三方账户表的userId
        SysThirdAccount sysThirdAccount = new SysThirdAccount();
        sysThirdAccount.setSysUserId(s);
        sysThirdAccountMapper.update(sysThirdAccount,query);
        return user;
    }
    
    public String saveThirdUser(SysUser sysUser) {
        //保存用户
        String userid = UUIDGenerator.generate();
        sysUser.setId(userid);
        sysUserMapper.insert(sysUser);
        //获取第三方角色
        SysRole sysRole = sysRoleMapper.selectOne(new LambdaQueryWrapper<SysRole>().eq(SysRole::getRoleCode, "third_role"));
        //保存用户角色
        SysUserRole userRole = new SysUserRole();
        userRole.setRoleId(sysRole.getId());
        userRole.setUserId(userid);
        sysUserRoleMapper.insert(userRole);
        return userid;
    }

    @Override
    public SysThirdAccount getOneBySysUserId(String sysUserId, String thirdType) {
        LambdaQueryWrapper<SysThirdAccount> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysThirdAccount::getSysUserId, sysUserId);
        queryWrapper.eq(SysThirdAccount::getThirdType, thirdType);
        return super.getOne(queryWrapper);
    }

    @Override
    public SysThirdAccount getOneByThirdUserId(String thirdUserId, String thirdType) {
        LambdaQueryWrapper<SysThirdAccount> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysThirdAccount::getThirdUserId, thirdUserId);
        queryWrapper.eq(SysThirdAccount::getThirdType, thirdType);
        return super.getOne(queryWrapper);
    }

    @Override
    public List<SysThirdAccount> listThirdUserIdByUsername(String[] sysUsernameArr, String thirdType) {
        return sysThirdAccountMapper.selectThirdIdsByUsername(sysUsernameArr, thirdType);
    }

    @Override
    public SysThirdAccount saveThirdUser(ThirdLoginModel tlm) {
        SysThirdAccount user = new SysThirdAccount();
        user.setDelFlag(CommonConstant.DEL_FLAG_0);
        user.setStatus(1);
        user.setThirdType(tlm.getSource());
        user.setAvatar(tlm.getAvatar());
        user.setRealname(tlm.getUsername());
        user.setThirdUserUuid(tlm.getUuid());
        user.setThirdUserId(tlm.getUuid());
        super.save(user);
        return user;
    }

}
