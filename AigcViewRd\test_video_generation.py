#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图生视频插件测试脚本
测试从扣子插件调用到视频生成完成的完整流程
"""

import requests
import json
import time
from typing import Dict, Any

class VideoGenerationTester:
    def __init__(self, base_url: str = "http://localhost:8080/jeecg-boot"):
        self.base_url = base_url
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/json',
            'User-Agent': 'VideoGenerationTester/1.0'
        })

    def test_create_video_task(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """测试创建视频生成任务"""
        url = f"{self.base_url}/api/coze/video/generate-task"
        
        print(f"🚀 测试创建视频任务...")
        print(f"📋 请求参数: {json.dumps(params, indent=2, ensure_ascii=False)}")
        
        try:
            response = self.session.post(url, json=params)
            result = response.json()
            
            print(f"📊 响应状态: {response.status_code}")
            print(f"📄 响应内容: {json.dumps(result, indent=2, ensure_ascii=False)}")
            
            return result
        except Exception as e:
            print(f"❌ 请求失败: {str(e)}")
            return {"success": False, "message": str(e)}

    def test_query_video_task(self, task_id: str) -> Dict[str, Any]:
        """测试查询视频任务状态"""
        url = f"{self.base_url}/api/coze/video/query-task"
        params = {"task_id": task_id}
        
        print(f"🔍 测试查询视频任务状态...")
        print(f"📋 任务ID: {task_id}")
        
        try:
            response = self.session.post(url, json=params)
            result = response.json()
            
            print(f"📊 响应状态: {response.status_code}")
            print(f"📄 响应内容: {json.dumps(result, indent=2, ensure_ascii=False)}")
            
            return result
        except Exception as e:
            print(f"❌ 请求失败: {str(e)}")
            return {"success": False, "message": str(e)}

    def run_basic_tests(self):
        """运行基础测试用例"""
        print("=" * 60)
        print("🧪 开始图生视频插件基础测试")
        print("=" * 60)

        # 测试用例1: 轻量级模型 + 480p + 5秒
        print("\n📝 测试用例1: 轻量级模型 + 480p + 5秒")
        params1 = {
            "apiKey": "ak_test_api_key_here",  # 请替换为真实的API密钥
            "model": "doubao-seedance-1-0-lite-i2v-250428",
            "prompt": "一只可爱的小猫在花园里玩耍，阳光明媚，画面温馨",
            "image_url": "https://example.com/test_image.jpg",
            "resolution": "480p",
            "duration": 5,
            "camerafixed": False
        }
        
        result1 = self.test_create_video_task(params1)

        if result1.get("task_id") and not result1.get("error"):
            task_id = result1["task_id"]
            print(f"✅ 任务创建成功，任务ID: {task_id}")

            # 查询任务状态
            time.sleep(2)
            self.test_query_video_task(task_id)
        else:
            print(f"❌ 任务创建失败: {result1.get('error', '未知错误')}")

        # 测试用例2: 专业级模型 + 1080p + 10秒
        print("\n📝 测试用例2: 专业级模型 + 1080p + 10秒")
        params2 = {
            "apiKey": "ak_test_api_key_here",  # 请替换为真实的API密钥
            "model": "doubao-seedance-1-0-pro-250528",
            "prompt": "城市夜景，霓虹灯闪烁，车流穿梭",
            "image_url": "https://example.com/city_night.jpg",
            "resolution": "1080p",
            "duration": 10
        }
        
        result2 = self.test_create_video_task(params2)

        if result2.get("task_id") and not result2.get("error"):
            task_id = result2["task_id"]
            print(f"✅ 任务创建成功，任务ID: {task_id}")
        else:
            print(f"❌ 任务创建失败: {result2.get('error', '未知错误')}")

    def run_validation_tests(self):
        """运行参数验证测试"""
        print("\n" + "=" * 60)
        print("🔍 开始参数验证测试")
        print("=" * 60)

        # 测试用例3: pro模型 + 720p（应该失败）
        print("\n📝 测试用例3: pro模型 + 720p（应该失败）")
        params3 = {
            "apiKey": "ak_test_api_key_here",
            "model": "doubao-seedance-1-0-pro-250528",
            "prompt": "测试",
            "image_url": "https://example.com/test.jpg",
            "resolution": "720p",
            "duration": 5
        }
        
        result3 = self.test_create_video_task(params3)

        if result3.get("error") and "720p" in result3.get("error", ""):
            print("✅ 参数验证正确，pro模型不支持720p")
        else:
            print("❌ 参数验证失败，应该拒绝pro模型+720p组合")

        # 测试用例4: 无效API密钥
        print("\n📝 测试用例4: 无效API密钥")
        params4 = {
            "apiKey": "invalid_api_key",
            "prompt": "测试",
            "image_url": "https://example.com/test.jpg"
        }
        
        result4 = self.test_create_video_task(params4)

        if result4.get("error") and "API-Key" in result4.get("error", ""):
            print("✅ API密钥验证正确")
        else:
            print("❌ API密钥验证失败")

        # 测试用例5: 缺少必填参数
        print("\n📝 测试用例5: 缺少必填参数")
        params5 = {
            "apiKey": "ak_test_api_key_here",
            # 缺少 prompt 和 image_url
        }
        
        result5 = self.test_create_video_task(params5)

        if result5.get("error"):
            print("✅ 必填参数验证正确")
        else:
            print("❌ 必填参数验证失败")

    def run_pricing_tests(self):
        """运行定价测试"""
        print("\n" + "=" * 60)
        print("💰 开始定价计算测试")
        print("=" * 60)

        # 定价测试用例
        pricing_test_cases = [
            {
                "name": "轻量级模型 + 480p + 5秒",
                "params": {
                    "model": "doubao-seedance-1-0-lite-i2v-250428",
                    "resolution": "480p",
                    "duration": 5
                },
                "expected_prices": {
                    "user": 0.69,
                    "VIP": 0.59,
                    "SVIP": 0.49,
                    "admin": 0.49
                }
            },
            {
                "name": "专业级模型 + 1080p + 10秒",
                "params": {
                    "model": "doubao-seedance-1-0-pro-250528",
                    "resolution": "1080p",
                    "duration": 10
                },
                "expected_prices": {
                    "user": 7.34,
                    "VIP": 7.14,
                    "SVIP": 6.94,
                    "admin": 6.94
                }
            }
        ]

        for test_case in pricing_test_cases:
            print(f"\n📝 定价测试: {test_case['name']}")
            
            # 这里需要根据实际情况测试不同用户等级的定价
            # 由于需要不同的API密钥对应不同用户等级，这里只做示例
            base_params = {
                "apiKey": "ak_test_api_key_here",
                "prompt": "定价测试",
                "image_url": "https://example.com/test.jpg"
            }
            base_params.update(test_case["params"])
            
            result = self.test_create_video_task(base_params)

            if result.get("task_id") and not result.get("error"):
                actual_price = result.get("deductedAmount")
                print(f"💰 实际扣费: {actual_price}元")
                
                # 这里可以根据用户等级验证价格是否正确
                # expected_price = test_case["expected_prices"]["user"]  # 假设是普通用户
                # if abs(actual_price - expected_price) < 0.01:
                #     print("✅ 定价计算正确")
                # else:
                #     print(f"❌ 定价计算错误，期望: {expected_price}元，实际: {actual_price}元")

    def run_all_tests(self):
        """运行所有测试"""
        print("🎯 开始图生视频插件完整测试")
        
        try:
            self.run_basic_tests()
            self.run_validation_tests()
            self.run_pricing_tests()
            
            print("\n" + "=" * 60)
            print("🎉 测试完成！")
            print("=" * 60)
            
        except Exception as e:
            print(f"\n❌ 测试过程中发生异常: {str(e)}")

if __name__ == "__main__":
    # 使用说明
    print("📖 使用说明:")
    print("1. 请确保后端服务已启动 (http://localhost:8080/jeecg-boot)")
    print("2. 请将脚本中的 'ak_test_api_key_here' 替换为真实的API密钥")
    print("3. 请确保测试用户有足够的余额")
    print("4. 请确保豆包API配置正确")
    print()
    
    # 创建测试器并运行测试
    tester = VideoGenerationTester()
    tester.run_all_tests()
