-- ========================================
-- 插件作者功能增强完整脚本
-- 执行顺序：先执行字典数据，再执行表结构修改
-- ========================================

-- 第一部分：创建字典数据
-- ========================================

-- 1. 插件作者职位字典
INSERT INTO sys_dict (id, dict_name, dict_code, description, del_flag, create_by, create_time, update_by, update_time, type) 
VALUES ('1750747534502000001', '插件作者职位', 'author_title', '插件作者的职位头衔选项', 0, 'admin', NOW(), 'admin', NOW(), 0);

INSERT INTO sys_dict_item (id, dict_id, item_text, item_value, description, sort_order, status, create_by, create_time, update_by, update_time) VALUES
('1750747534502001001', '1750747534502000001', '资深AI开发者', 'senior_ai_developer', '资深AI开发者', 1, 1, 'admin', NOW(), 'admin', NOW()),
('1750747534502001002', '1750747534502000001', 'AI算法工程师', 'ai_algorithm_engineer', 'AI算法工程师', 2, 1, 'admin', NOW(), 'admin', NOW()),
('1750747534502001003', '1750747534502000001', '机器学习专家', 'ml_expert', '机器学习专家', 3, 1, 'admin', NOW(), 'admin', NOW()),
('1750747534502001004', '1750747534502000001', '深度学习工程师', 'dl_engineer', '深度学习工程师', 4, 1, 'admin', NOW(), 'admin', NOW()),
('1750747534502001005', '1750747534502000001', '数据科学家', 'data_scientist', '数据科学家', 5, 1, 'admin', NOW(), 'admin', NOW()),
('1750747534502001006', '1750747534502000001', '全栈开发工程师', 'fullstack_developer', '全栈开发工程师', 6, 1, 'admin', NOW(), 'admin', NOW()),
('1750747534502001007', '1750747534502000001', '前端开发工程师', 'frontend_developer', '前端开发工程师', 7, 1, 'admin', NOW(), 'admin', NOW()),
('1750747534502001008', '1750747534502000001', '后端开发工程师', 'backend_developer', '后端开发工程师', 8, 1, 'admin', NOW(), 'admin', NOW()),
('1750747534502001009', '1750747534502000001', '产品经理', 'product_manager', '产品经理', 9, 1, 'admin', NOW(), 'admin', NOW()),
('1750747534502001010', '1750747534502000001', 'UI/UX设计师', 'ui_ux_designer', 'UI/UX设计师', 10, 1, 'admin', NOW(), 'admin', NOW()),
('1750747534502001011', '1750747534502000001', '技术架构师', 'tech_architect', '技术架构师', 11, 1, 'admin', NOW(), 'admin', NOW()),
('1750747534502001012', '1750747534502000001', '独立开发者', 'indie_developer', '独立开发者', 12, 1, 'admin', NOW(), 'admin', NOW());

-- 2. 插件作者专业领域字典
INSERT INTO sys_dict (id, dict_name, dict_code, description, del_flag, create_by, create_time, update_by, update_time, type) 
VALUES ('1750747534502000002', '插件作者专业领域', 'author_expertise', '插件作者的专业技能领域选项', 0, 'admin', NOW(), 'admin', NOW(), 0);

INSERT INTO sys_dict_item (id, dict_id, item_text, item_value, description, sort_order, status, create_by, create_time, update_by, update_time) VALUES
('1750747534502002001', '1750747534502000002', 'AI技术', 'ai_technology', 'AI技术', 1, 1, 'admin', NOW(), 'admin', NOW()),
('1750747534502002002', '1750747534502000002', '机器学习', 'machine_learning', '机器学习', 2, 1, 'admin', NOW(), 'admin', NOW()),
('1750747534502002003', '1750747534502000002', '深度学习', 'deep_learning', '深度学习', 3, 1, 'admin', NOW(), 'admin', NOW()),
('1750747534502002004', '1750747534502000002', '自然语言处理', 'nlp', '自然语言处理', 4, 1, 'admin', NOW(), 'admin', NOW()),
('1750747534502002005', '1750747534502000002', '计算机视觉', 'computer_vision', '计算机视觉', 5, 1, 'admin', NOW(), 'admin', NOW()),
('1750747534502002006', '1750747534502000002', '数据分析', 'data_analysis', '数据分析', 6, 1, 'admin', NOW(), 'admin', NOW()),
('1750747534502002007', '1750747534502000002', '前端开发', 'frontend_dev', '前端开发', 7, 1, 'admin', NOW(), 'admin', NOW()),
('1750747534502002008', '1750747534502000002', '后端开发', 'backend_dev', '后端开发', 8, 1, 'admin', NOW(), 'admin', NOW()),
('1750747534502002009', '1750747534502000002', '移动开发', 'mobile_dev', '移动开发', 9, 1, 'admin', NOW(), 'admin', NOW()),
('1750747534502002010', '1750747534502000002', '产品设计', 'product_design', '产品设计', 10, 1, 'admin', NOW(), 'admin', NOW()),
('1750747534502002011', '1750747534502000002', 'UI设计', 'ui_design', 'UI设计', 11, 1, 'admin', NOW(), 'admin', NOW()),
('1750747534502002012', '1750747534502000002', 'UX设计', 'ux_design', 'UX设计', 12, 1, 'admin', NOW(), 'admin', NOW()),
('1750747534502002013', '1750747534502000002', '系统架构', 'system_architecture', '系统架构', 13, 1, 'admin', NOW(), 'admin', NOW()),
('1750747534502002014', '1750747534502000002', '云计算', 'cloud_computing', '云计算', 14, 1, 'admin', NOW(), 'admin', NOW()),
('1750747534502002015', '1750747534502000002', '大数据', 'big_data', '大数据', 15, 1, 'admin', NOW(), 'admin', NOW()),
('1750747534502002016', '1750747534502000002', '区块链', 'blockchain', '区块链', 16, 1, 'admin', NOW(), 'admin', NOW()),
('1750747534502002017', '1750747534502000002', '物联网', 'iot', '物联网', 17, 1, 'admin', NOW(), 'admin', NOW()),
('1750747534502002018', '1750747534502000002', '网络安全', 'cybersecurity', '网络安全', 18, 1, 'admin', NOW(), 'admin', NOW());

-- 第二部分：修改表结构
-- ========================================

-- 1. 添加作者职位字段（单选，关联字典）
ALTER TABLE aigc_plub_author 
ADD COLUMN title VARCHAR(50) DEFAULT NULL COMMENT '作者职位头衔' AFTER authorname;

-- 2. 添加专业领域字段（多选，存储逗号分隔的字典值）
ALTER TABLE aigc_plub_author 
ADD COLUMN expertise TEXT DEFAULT NULL COMMENT '专业领域（多选，逗号分隔）' AFTER title;

-- 第三部分：为现有数据设置默认值（可选）
-- ========================================

-- 为现有作者设置默认职位
UPDATE aigc_plub_author 
SET title = 'senior_ai_developer' 
WHERE title IS NULL AND authorname IS NOT NULL;

-- 为现有作者设置默认专业领域
UPDATE aigc_plub_author 
SET expertise = 'ai_technology,machine_learning' 
WHERE expertise IS NULL AND authorname IS NOT NULL;

-- 第四部分：验证脚本
-- ========================================

-- 查看字典数据
-- SELECT * FROM sys_dict WHERE dict_code IN ('author_title', 'author_expertise');
-- SELECT * FROM sys_dict_item WHERE dict_id IN ('1750747534502000001', '1750747534502000002');

-- 查看表结构
-- DESC aigc_plub_author;

-- 查看数据
-- SELECT id, authorname, title, expertise FROM aigc_plub_author LIMIT 5;

-- ========================================
-- 执行完成提示
-- ========================================
SELECT '插件作者功能增强脚本执行完成！' AS message;
SELECT '请重启后端服务以加载新的字典数据' AS notice;
SELECT '然后在后台管理系统中测试插件作者的编辑功能' AS next_step;
