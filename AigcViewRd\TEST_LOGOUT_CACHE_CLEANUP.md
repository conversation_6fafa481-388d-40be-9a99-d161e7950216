# 退出登录缓存清理修复测试指南

## 🎯 测试目标

验证手机号 `15639350080` 的设备冲突问题是否已完全解决。

## 🧪 测试步骤

### 1. 准备工作

确保后端服务已启动并部署了修复代码。

### 2. 手动测试流程

#### 步骤1：登录测试账号
```bash
# 使用手机号登录
POST /sys/phoneLogin
{
  "phone": "15639350080",
  "captcha": "验证码"
}
```

#### 步骤2：检查登录状态
```bash
# 验证登录成功，获取用户信息
GET /sys/user/getCurrentUserDeparts
```

#### 步骤3：正常退出登录
```bash
# 执行退出登录
POST /sys/logout
Headers: {
  "X-Access-Token": "当前用户的Token"
}
```

#### 步骤4：验证缓存清理情况
```bash
# 调用验证接口检查缓存清理
GET /sys/verifyLogoutCache?userId=1944760455993430017&username=15639350080
```

**预期结果**：
```json
{
  "success": true,
  "result": {
    "userId": "1944760455993430017",
    "username": "15639350080",
    "hasCurrentUserToken": false,
    "hasDeviceInfo": false,
    "hasShiroCache": false,
    "hasUserCache": false,
    "isCleanLogout": true
  }
}
```

#### 步骤5：重新登录测试
```bash
# 再次使用相同手机号登录
POST /sys/phoneLogin
{
  "phone": "15639350080",
  "captcha": "验证码"
}
```

**预期结果**：
- ✅ 登录成功，不出现设备冲突提示
- ✅ 返回正常的登录响应，包含Token和用户信息
- ✅ 不会显示："检测到您的账号已在其他设备登录"

### 3. 前端测试流程

#### 步骤1：官网登录
1. 访问 `https://aigcview.cn/login`
2. 使用手机号 `15639350080` 登录
3. 验证登录成功，跳转到个人中心

#### 步骤2：正常退出
1. 在个人中心点击"退出登录"
2. 确认退出操作
3. 验证跳转到首页

#### 步骤3：重新登录
1. 再次访问登录页面
2. 使用相同手机号登录
3. **关键验证**：不应出现设备冲突提示

## 🔍 问题排查

### 如果仍出现设备冲突

#### 检查1：验证缓存清理
```bash
GET /sys/verifyLogoutCache?userId=1944760455993430017&username=15639350080
```

如果返回 `isCleanLogout: false`，说明缓存清理不完整。

#### 检查2：手动清理缓存
```bash
# 可以手动调用清理服务（需要在代码中临时添加接口）
# 或者直接在Redis中检查和清理相关键
```

#### 检查3：查看日志
查看后端日志中的缓存清理相关信息：
```
🧹 开始清理用户登录缓存，用户：15639350080
✅ 用户 15639350080 登录缓存清理完成
```

### 常见问题

#### 问题1：验证接口返回错误
- 检查用户ID是否正确
- 确认用户名格式是否正确

#### 问题2：缓存清理不完整
- 检查UserCacheCleanupService是否正确注入
- 验证Redis连接是否正常

#### 问题3：前端仍显示设备冲突
- 清理浏览器缓存和localStorage
- 检查前端authMixin是否正确更新

## 📊 成功标准

### ✅ 修复成功的标志

1. **缓存验证通过**
   ```json
   {
     "isCleanLogout": true,
     "hasCurrentUserToken": false,
     "hasDeviceInfo": false
   }
   ```

2. **重新登录正常**
   - 不出现设备冲突提示
   - 登录流程顺畅
   - 功能使用正常

3. **日志记录正常**
   ```
   ✅ 用户 15639350080 登录缓存清理完成
   🧹 已清理 X 个kicked_token记录
   ```

### ❌ 需要进一步修复的标志

1. **缓存验证失败**
   ```json
   {
     "isCleanLogout": false,
     "hasCurrentUserToken": true
   }
   ```

2. **仍出现设备冲突**
   - 登录时显示设备冲突提示
   - 需要强制登录才能成功

## 📞 技术支持

如果测试过程中遇到问题，请提供以下信息：
1. 具体的错误信息或异常行为
2. 缓存验证接口的返回结果
3. 相关的后端日志
4. 测试的具体步骤和时间

---
**测试版本**: V1.0  
**测试日期**: 2025-07-14  
**预期修复状态**: 完全解决设备冲突问题
