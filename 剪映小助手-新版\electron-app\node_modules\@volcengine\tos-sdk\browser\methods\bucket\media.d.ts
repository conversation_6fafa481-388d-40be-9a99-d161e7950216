import TOSBase from '../base';
export interface PutBucketPrivateM3U8Input {
    bucket: string;
    enable: boolean;
}
export interface PutPrivateM3U8Output {
}
/**
 * @private unstable
 */
export declare function putBucketPrivateM3U8(this: TOSBase, input: PutBucketPrivateM3U8Input): Promise<import("../base").TosResponse<unknown>>;
export interface GetBucketPrivateM3U8Input {
    bucket: string;
}
export interface GetBucketPrivateM3U8Output {
    Enable: boolean;
}
/**
 * @private unstable
 */
export declare function getBucketPrivateM3U8(this: TOSBase, input: GetBucketPrivateM3U8Input): Promise<import("../base").TosResponse<GetBucketPrivateM3U8Output>>;
