<template>
  <WebsitePage>
    <div class="affiliate-container">
      <!-- Loading 覆盖层 -->
      <div v-if="loading" class="affiliate-loading-overlay">
        <div class="loading-content">
          <div class="loading-spinner">
            <a-spin size="large" />
          </div>
          <div class="loading-text">
            <h3>正在加载邀请奖励</h3>
            <p>正在为您准备推荐数据，请稍候...</p>
          </div>
        </div>
      </div>

      <!-- 主要内容区域 -->
      <transition name="content-fade" appear>
        <div v-if="!loading" class="affiliate-main">
          <!-- 简洁页面标题 -->
          <div class="simple-header">
            <h1 class="simple-title">邀请奖励</h1>
            <p class="simple-subtitle">邀请好友注册智界AIGC，获得丰厚奖励</p>
            <div class="commission-badge">
              <span class="badge-text">当前奖励比例：{{ currentCommissionRate }}%</span>
              <span class="badge-level">{{ commissionLevelText }}</span>
            </div>
          </div>

      <!-- 分销内容区域 -->
      <section class="affiliate-section">
        <div class="container">
          <!-- 邀请链接区域 - 最显眼位置 -->
          <div class="promotion-link-section">
            <h2 class="section-title">您的专属邀请链接</h2>
            <div class="link-main-container">
              <div class="link-input-large">
                <a-input
                  :value="affiliateLink || '正在生成邀请链接...'"
                  readonly
                  :loading="loading"
                  size="large"
                  placeholder="邀请链接生成中..."
                />
              </div>
              <div class="link-actions">
                <a-button
                  type="primary"
                  size="large"
                  :disabled="!affiliateLink || loading"
                  @click="copyLink"
                  class="copy-btn"
                >
                  <a-icon type="copy" />
                  复制链接
                </a-button>
                <a-button
                  size="large"
                  :loading="qrLoading"
                  @click="generateQRCode"
                  class="qr-btn"
                >
                  <a-icon type="qrcode" />
                  邀请二维码
                </a-button>
              </div>
            </div>
            <div class="link-tips">
              <a-icon type="info-circle" />
              分享此链接，您将获得好友付费的 <strong>{{ currentCommissionRate }}%</strong> 奖励
            </div>
          </div>

          <!-- 收益展示 -->
          <div class="earnings-dashboard">
            <h2 class="section-title">收益概览</h2>
            <div class="earnings-grid">
              <div class="earning-card primary">
                <div class="card-icon">
                  <a-icon type="dollar" />
                </div>
                <div class="card-content">
                  <a-spin :spinning="loading" size="small">
                    <div class="earning-number">¥{{ formatNumber(totalEarnings) }}</div>
                    <div class="earning-label">累计收益</div>
                  </a-spin>
                </div>
              </div>

              <div class="earning-card success">
                <div class="card-icon">
                  <a-icon type="wallet" />
                </div>
                <div class="card-content">
                  <a-spin :spinning="loading" size="small">
                    <div class="earning-number">¥{{ formatNumber(availableEarnings) }}</div>
                    <div class="earning-label">可提现金额</div>
                  </a-spin>
                  <div class="card-action">
                    <a-button
                      type="primary"
                      size="small"
                      :disabled="availableEarnings <= 0 || loading"
                      @click="openWithdrawModal"
                    >
                      立即提现
                    </a-button>
                  </div>
                </div>
              </div>

              <div class="earning-card info">
                <div class="card-icon">
                  <a-icon type="team" />
                </div>
                <div class="card-content">
                  <a-spin :spinning="loading" size="small">
                    <div class="earning-number">{{ Math.floor(totalReferrals) }}</div>
                    <div class="earning-label">邀请注册人数</div>
                  </a-spin>
                </div>
              </div>

              <div class="earning-card warning">
                <div class="card-icon">
                  <a-icon type="crown" />
                </div>
                <div class="card-content">
                  <a-spin :spinning="loading" size="small">
                    <div class="earning-number">{{ Math.floor(memberReferrals) }}</div>
                    <div class="earning-label">转化人数</div>
                  </a-spin>
                </div>
              </div>
            </div>
          </div>

          <!-- 奖励等级进度 -->
          <div class="commission-progress">
            <h2 class="section-title">奖励等级进度</h2>
            <div class="progress-card">
              <!-- 一行显示所有等级 -->
              <div class="level-timeline-horizontal">
                <div
                  v-for="(level, index) in commissionLevels"
                  :key="index"
                  class="level-step-horizontal"
                  :class="{
                    'current': level.isCurrent,
                    'completed': level.isCompleted,
                    'upcoming': level.isUpcoming
                  }"
                >
                  <div class="step-circle-horizontal">
                    <a-icon v-if="level.isCompleted" type="check" />
                    <span v-else-if="level.isCurrent" class="current-dot"></span>
                    <span v-else class="step-number">{{ index + 1 }}</span>
                  </div>
                  <div class="step-content-horizontal">
                    <div class="step-title">{{ level.name }}</div>
                    <div class="step-rate">{{ level.rate }}%</div>
                    <div class="step-requirement">{{ level.requirement }}人</div>
                    <div v-if="level.remaining > 0" class="step-remaining">
                      还需{{ level.remaining }}个
                    </div>
                    <div v-else-if="level.isCompleted" class="step-completed">
                      已达成
                    </div>
                  </div>
                  <!-- 连接线 -->
                  <div v-if="index < commissionLevels.length - 1" class="step-line-horizontal"></div>
                </div>
              </div>
            </div>
          </div>

          <!-- 奖励规则说明 -->
          <div class="commission-rules">
            <h2 class="section-title">奖励规则</h2>
            <div class="rules-table">
              <div class="rule-row header">
                <div class="rule-cell">用户等级</div>
                <div class="rule-cell">邀请人数要求</div>
                <div class="rule-cell">奖励比例</div>
                <div class="rule-cell">说明</div>
              </div>
              <a-spin :spinning="loading" size="small">
                <div
                  v-for="config in allLevelConfigs"
                  :key="config.id"
                  class="rule-row"
                  :class="{
                    'vip': config.role_code === 'VIP',
                    'svip': config.role_code === 'SVIP'
                  }"
                >
                  <div class="rule-cell">{{ getRoleDisplayName(config.role_code) }}</div>
                  <div class="rule-cell">{{ getRequirementText(config) }}</div>
                  <div class="rule-cell highlight">{{ config.commission_rate }}%</div>
                  <div class="rule-cell">{{ config.level_name }}</div>
                </div>
              </a-spin>
            </div>
          </div>

          <!-- 邀请用户列表 -->
          <div class="referral-users">
            <h2 class="section-title">我的邀请用户</h2>
            <div class="users-table-container">
              <a-table
                :columns="userColumns"
                :data-source="referralUsers"
                :loading="usersLoading"
                :pagination="usersPagination"
                size="middle"
                @change="handleUsersTableChange"
              >
                <template slot="avatar" slot-scope="text, record">
                  <a-avatar :src="getAvatarUrl(record.avatar)" :style="{ backgroundColor: '#87d068' }">
                    {{ record.nickname ? record.nickname.charAt(0) : 'U' }}
                  </a-avatar>
                </template>
                <template slot="reward" slot-scope="text">
                  <span class="reward-amount">¥{{ text || '0.00' }}</span>
                </template>
              </a-table>
            </div>
          </div>

          <!-- 提现记录 -->
          <div class="withdraw-records">
            <h2 class="section-title">提现记录</h2>

            <!-- 筛选区域 -->
            <div class="filter-section" style="margin-bottom: 16px; padding: 16px; background: #fafafa; border-radius: 6px;">
              <a-row :gutter="16">
                <a-col :span="5">
                  <a-form-item label="提现金额">
                    <a-input-group compact>
                      <a-input-number
                        v-model="withdrawFilter.minAmount"
                        placeholder="最小金额"
                        :min="0"
                        :precision="2"
                        style="width: 50%"
                      />
                      <a-input-number
                        v-model="withdrawFilter.maxAmount"
                        placeholder="最大金额"
                        :min="0"
                        :precision="2"
                        style="width: 50%"
                      />
                    </a-input-group>
                  </a-form-item>
                </a-col>

                <a-col :span="5">
                  <a-form-item label="申请时间">
                    <a-range-picker
                      v-model="withdrawFilter.dateRange"
                      format="YYYY-MM-DD"
                      placeholder="['开始日期', '结束日期']"
                      style="width: 100%"
                    />
                  </a-form-item>
                </a-col>

                <a-col :span="4">
                  <a-form-item label="状态">
                    <a-select
                      v-model="withdrawFilter.status"
                      placeholder="选择状态"
                      style="width: 100%"
                    >
                      <a-select-option :value="null">全部</a-select-option>
                      <a-select-option :value="1">待审核</a-select-option>
                      <a-select-option :value="2">已发放</a-select-option>
                      <a-select-option :value="3">审核拒绝</a-select-option>
                      <a-select-option :value="4">已取消</a-select-option>
                    </a-select>
                  </a-form-item>
                </a-col>

                <a-col :span="5">
                  <a-form-item label="完成时间">
                    <a-range-picker
                      v-model="withdrawFilter.completeDateRange"
                      format="YYYY-MM-DD"
                      placeholder="['开始日期', '结束日期']"
                      style="width: 100%"
                    />
                  </a-form-item>
                </a-col>

                <a-col :span="5">
                  <a-form-item label=" ">
                    <a-button type="primary" @click="handleWithdrawFilter" :loading="recordsLoading" style="margin-right: 8px;">
                      搜索
                    </a-button>
                    <a-button @click="handleWithdrawReset">重置</a-button>
                  </a-form-item>
                </a-col>
              </a-row>
            </div>

            <div class="records-table-container">
              <a-table
                :columns="withdrawColumns"
                :data-source="withdrawRecords"
                :loading="recordsLoading"
                :pagination="withdrawPagination"
                size="middle"
                @change="handleWithdrawTableChange"
              >
                <template slot="status" slot-scope="text">
                  <a-tag :color="getStatusColor(text)">
                    {{ text }}
                  </a-tag>
                </template>
                <template slot="amount" slot-scope="text">
                  <span class="withdraw-amount">¥{{ text }}</span>
                </template>
                <template slot="action" slot-scope="text, record">
                  <a-button
                    v-if="record.rawStatus === 1"
                    type="danger"
                    size="small"
                    :loading="cancelLoading"
                    @click="handleCancelWithdraw(record)"
                  >
                    取消提现
                  </a-button>
                  <span v-else>-</span>
                </template>
              </a-table>
            </div>
          </div>
        </div>
      </section>

      <!-- 二维码弹窗 -->
      <a-modal
        v-model="showQRModal"
        title="邀请二维码"
        :footer="null"
        width="400px"
        centered
      >
        <div class="qr-modal-content">
          <div class="qr-code-container" v-if="qrCodeUrl">
            <img :src="qrCodeUrl" alt="邀请二维码" class="qr-code-image" />
          </div>
          <div class="qr-actions">
            <a-button type="primary" block @click="downloadQRCode" v-if="qrCodeUrl">
              <a-icon type="download" />
              下载二维码
            </a-button>
          </div>
        </div>
      </a-modal>

      <!-- 提现弹窗 -->
      <a-modal
        v-model="showWithdrawModal"
        title="申请提现"
        :footer="null"
        width="500px"
        centered
      >
        <div class="withdraw-modal-content">
          <div class="withdraw-info">
            <div class="info-item">
              <span class="info-label">可提现金额：</span>
              <span class="info-value">¥{{ formatNumber(availableEarnings) }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">最低提现金额：</span>
              <span class="info-value">¥50.00</span>
            </div>
          </div>

          <a-form :form="withdrawForm" @submit="handleWithdraw">
            <a-form-item label="提现金额">
              <a-input-number
                v-decorator="['amount', {
                  rules: [
                    { required: true, message: '请输入提现金额' },
                    { type: 'number', min: 50, message: '最低提现金额为50元' },
                    { type: 'number', max: availableEarnings, message: '提现金额不能超过可提现金额' }
                  ]
                }]"
                :min="50"
                :max="availableEarnings"
                :precision="2"
                style="width: 100%"
                placeholder="请输入提现金额"
              >
                <template slot="addonAfter">元</template>
              </a-input-number>
            </a-form-item>

            <a-form-item label="提现方式">
              <a-select
                v-decorator="['method', {
                  rules: [{ required: true, message: '请选择提现方式' }],
                  initialValue: 'alipay'
                }]"
                placeholder="请选择提现方式"
                disabled
              >
                <a-select-option value="alipay">支付宝</a-select-option>
              </a-select>
            </a-form-item>

            <a-form-item label="支付宝手机号">
              <a-input
                v-decorator="['alipayAccount', {
                  rules: [
                    { required: true, message: '请输入支付宝手机号' },
                    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号格式' }
                  ]
                }]"
                placeholder="请输入支付宝手机号"
              />
            </a-form-item>

            <a-form-item label="收款人真实姓名">
              <a-input
                v-decorator="['realName', {
                  rules: [
                    { required: true, message: '请输入收款人真实姓名' },
                    { pattern: /^[\u4e00-\u9fa5]{2,4}$/, message: '请输入正确的中文姓名（2-4个汉字）' }
                  ]
                }]"
                placeholder="请输入收款人真实姓名"
              />
            </a-form-item>
          </a-form>

          <div class="withdraw-actions">
            <a-button @click="showWithdrawModal = false" style="margin-right: 8px">
              取消
            </a-button>
            <a-button
              type="primary"
              :loading="withdrawLoading"
              @click="handleWithdraw"
            >
              申请提现
            </a-button>
          </div>
        </div>
      </a-modal>
        </div>
      </transition>
    </div>
  </WebsitePage>
</template>

<script>
import WebsitePage from '@/components/website/WebsitePage.vue'
import { getReferralStats, generateReferralLink, getUserRole, getLevelConfig } from '@/api/usercenter'
import { ACCESS_TOKEN } from '@/store/mutation-types'
import Vue from 'vue'

export default {
  name: 'Affiliate',
  components: {
    WebsitePage
  },
  data() {
    return {
      loading: true,
      qrLoading: false,

      // 收益数据
      totalEarnings: 0,
      availableEarnings: 0,
      totalReferrals: 0,
      memberReferrals: 0,

      // 邀请链接
      affiliateLink: '',

      // 佣金等级
      userRole: 'user', // user, VIP, SVIP
      currentCommissionRate: 30,
      commissionLevelText: '新手邀请员',
      levelProgress: 0,
      nextLevelRequirement: 10,
      nextLevelText: '高级邀请员',
      nextLevelRate: 40,
      progressColor: '#1890ff',

      // 佣金等级配置
      commissionLevels: [],
      allLevelConfigs: [], // 从数据库获取的完整等级配置

      // 二维码
      showQRModal: false,
      qrCodeUrl: '',
      qrPreGenerated: false, // 是否已预生成二维码

      // 提现
      showWithdrawModal: false,
      withdrawLoading: false,
      withdrawForm: this.$form.createForm(this),

      // 邀请用户列表
      referralUsers: [],
      usersLoading: false,
      usersPagination: {
        current: 1,
        pageSize: 10,
        total: 0,
        showSizeChanger: true,
        showQuickJumper: true,
        showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
      },
      // 邀请用户排序
      usersSort: {
        orderBy: 'total_reward',
        order: 'desc'
      },
      defaultAvatar: '/default-avatar.png', // 本地降级头像

      // 提现记录
      withdrawRecords: [],
      recordsLoading: false,
      cancelLoading: false,
      withdrawPagination: {
        current: 1,
        pageSize: 10,
        total: 0,
        showSizeChanger: true,
        showQuickJumper: true,
        showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
      },
      // 提现记录排序
      withdrawSort: {
        orderBy: 'apply_time',
        order: 'desc'
      },
      // 提现记录筛选
      withdrawFilter: {
        minAmount: null,
        maxAmount: null,
        status: null, // null表示"全部"
        dateRange: [],
        completeDateRange: []
      },

      // 用户信息
      userInfo: null
    }
  },
  computed: {
    // 动态计算表格列配置，确保排序状态响应式更新
    userColumns() {
      return [
        {
          title: '头像',
          dataIndex: 'avatar',
          key: 'avatar',
          scopedSlots: { customRender: 'avatar' },
          width: 80
        },
        {
          title: '用户昵称',
          dataIndex: 'nickname',
          key: 'nickname',
          sorter: true
        },
        {
          title: '注册时间',
          dataIndex: 'registerTime',
          key: 'registerTime',
          sorter: true
        },
        {
          title: '获得奖励',
          dataIndex: 'reward',
          key: 'reward',
          scopedSlots: { customRender: 'reward' },
          sorter: true
        }
      ]
    },

    // 动态计算提现记录表格列配置，确保排序状态响应式更新
    withdrawColumns() {
      return [
        {
          title: '提现金额',
          dataIndex: 'amount',
          key: 'amount',
          scopedSlots: { customRender: 'amount' },
          sorter: true
        },
        {
          title: '提现方式',
          dataIndex: 'method',
          key: 'method'
        },
        {
          title: '申请时间',
          dataIndex: 'applyTime',
          key: 'applyTime',
          sorter: true
        },
        {
          title: '状态',
          dataIndex: 'status',
          key: 'status',
          scopedSlots: { customRender: 'status' },
          sorter: true
        },
        {
          title: '完成时间',
          dataIndex: 'completeTime',
          key: 'completeTime',
          sorter: true
        },
        {
          title: '操作',
          key: 'action',
          width: 100,
          scopedSlots: { customRender: 'action' }
        }
      ]
    }
  },
  async mounted() {
    await this.checkLoginAndLoadData()
  },
  methods: {
    // 获取头像URL（处理CDN路径和默认头像）
    getAvatarUrl(avatar) {
      if (!avatar) {
        return this.defaultAvatar
      }

      // 如果是完整的URL，直接返回
      if (avatar.startsWith('http://') || avatar.startsWith('https://')) {
        return avatar
      }

      // 如果是相对路径，使用getFileAccessHttpUrl转换
      return this.getFileAccessHttpUrl(avatar) || this.defaultAvatar
    },

    // 处理文件访问URL（和其他组件保持一致）
    getFileAccessHttpUrl(avatar) {
      if (!avatar) return this.defaultAvatar

      // 如果已经是完整URL，直接返回
      if (avatar.startsWith('http://') || avatar.startsWith('https://')) {
        return avatar
      }

      // 如果是TOS文件，使用全局方法
      if (avatar.startsWith('uploads/')) {
        return window.getFileAccessHttpUrl ? window.getFileAccessHttpUrl(avatar) : avatar
      }

      // 本地文件，使用静态域名
      const staticDomain = this.$store.state.app.staticDomainURL
      return staticDomain ? `${staticDomain}/${avatar}` : avatar
    },

    // 加载TOS默认头像URL
    async loadDefaultAvatar() {
      try {
        const response = await this.$http.get('/sys/common/default-avatar-url')
        if (response && response.success && response.result) {
          this.defaultAvatar = response.result
          console.log('🎯 Affiliate: 已加载TOS默认头像:', this.defaultAvatar)
        }
      } catch (error) {
        console.warn('⚠️ Affiliate: 获取TOS默认头像失败，使用本地降级:', error)
        // 保持本地默认头像作为降级方案
      }
    },

    // 检查登录状态并加载数据
    async checkLoginAndLoadData() {
      const token = Vue.ls.get(ACCESS_TOKEN)
      if (!token) {
        this.loading = false // 未登录时停止loading
        this.$router.push({ path: '/login', query: { redirect: this.$route.fullPath } })
        return
      }

      try {
        this.loading = true
        console.log('🔄 开始加载邀请奖励数据，loading状态:', this.loading)

        // 记录开始时间
        const startTime = Date.now()

        await Promise.all([
          this.loadReferralData(),
          this.loadReferralLink(),
          this.loadUserRole(),
          this.loadLevelConfig(),
          this.loadReferralUsers(),
          this.loadWithdrawRecords(),
          this.loadDefaultAvatar()
        ])

        // 计算佣金等级
        this.calculateCommissionLevel()

        // 自动预生成邀请二维码
        this.preGenerateQRCode()

        // 确保loading至少显示1秒，让用户能看到
        const elapsedTime = Date.now() - startTime
        const minLoadingTime = 1000 // 最小loading时间1秒

        if (elapsedTime < minLoadingTime) {
          await new Promise(resolve => setTimeout(resolve, minLoadingTime - elapsedTime))
        }

        console.log('✅ 邀请奖励数据加载完成，总耗时:', Date.now() - startTime, 'ms')
      } catch (error) {
        console.error('加载分销数据失败:', error)
        this.$notification.error({
          message: '加载失败',
          description: '获取分销数据失败，请稍后重试',
          placement: 'topRight'
        })
      } finally {
        this.loading = false
        console.log('🔄 关闭loading，loading状态:', this.loading)
      }
    },

    // 加载推荐统计数据
    async loadReferralData() {
      try {
        const response = await getReferralStats()
        if (response.success) {
          const data = response.result
          this.totalEarnings = data.total_reward_amount || 0
          this.availableEarnings = data.available_rewards || 0
          this.totalReferrals = data.total_referrals || 0
          this.memberReferrals = data.member_referrals || 0
        }
      } catch (error) {
        console.error('获取推荐统计失败:', error)
        throw error
      }
    },

    // 加载推荐链接
    async loadReferralLink() {
      try {
        const response = await generateReferralLink({})
        if (response.success) {
          this.affiliateLink = response.result || ''
        }
      } catch (error) {
        console.error('获取推荐链接失败:', error)
        // 如果获取失败，使用默认链接格式
        this.affiliateLink = `${window.location.origin}?ref=loading...`
      }
    },

    // 加载用户角色信息
    async loadUserRole() {
      try {
        const response = await getUserRole()
        if (response.success) {
          this.userRole = response.result.role_code || 'user'
        }
      } catch (error) {
        console.error('获取用户角色失败:', error)
        this.userRole = 'user'
      }
    },

    // 加载等级配置信息
    async loadLevelConfig() {
      try {
        const response = await getLevelConfig()
        if (response.success) {
          this.allLevelConfigs = response.result || []
        }
      } catch (error) {
        console.error('获取等级配置失败:', error)
        this.allLevelConfigs = []
      }
    },

    // 计算佣金等级和进度
    calculateCommissionLevel() {
      const memberCount = this.memberReferrals

      // 从数据库配置中获取当前用户角色的等级配置
      const userLevelConfigs = this.allLevelConfigs.filter(config => config.role_code === this.userRole)

      if (userLevelConfigs.length === 0) {
        console.warn('未找到用户角色的等级配置:', this.userRole)
        return
      }

      // 根据邀请人数确定当前等级
      let currentLevel = null
      for (let i = userLevelConfigs.length - 1; i >= 0; i--) {
        if (memberCount >= userLevelConfigs[i].min_referrals) {
          currentLevel = userLevelConfigs[i]
          break
        }
      }

      if (!currentLevel) {
        currentLevel = userLevelConfigs[0] // 默认最低等级
      }

      // 设置当前等级信息
      this.currentCommissionRate = parseFloat(currentLevel.commission_rate)
      this.commissionLevelText = currentLevel.level_name

      // 查找下一个等级
      const nextLevel = userLevelConfigs.find(config => config.min_referrals > memberCount)

      if (nextLevel) {
        this.nextLevelRequirement = nextLevel.min_referrals
        this.nextLevelText = nextLevel.level_name
        this.nextLevelRate = parseFloat(nextLevel.commission_rate)
        this.levelProgress = (memberCount / nextLevel.min_referrals) * 100
        this.progressColor = '#1890ff'
      } else {
        // 已达最高等级
        this.nextLevelRequirement = 0
        this.nextLevelText = '已达最高等级'
        this.nextLevelRate = this.currentCommissionRate
        this.levelProgress = 100
        this.progressColor = '#722ed1'
      }

      // 生成等级进度显示数据
      this.commissionLevels = userLevelConfigs.map((config, index) => {
        const isCompleted = memberCount >= config.min_referrals

        // 判断当前等级：如果不是已完成，且满足前一个等级的要求，则为当前等级
        let isCurrent = false
        if (!isCompleted) {
          if (index === 0) {
            // 第一个等级，如果没完成就是当前等级
            isCurrent = true
          } else {
            // 其他等级，如果满足前一个等级要求但不满足当前等级要求，则为当前等级
            const prevRequirement = userLevelConfigs[index - 1].min_referrals
            isCurrent = memberCount >= prevRequirement
          }
        }

        const isUpcoming = !isCompleted && !isCurrent

        let remaining = 0
        if (!isCompleted) {
          remaining = config.min_referrals - memberCount
        }

        return {
          name: config.level_name,
          rate: parseFloat(config.commission_rate),
          requirement: config.min_referrals,
          isCompleted,
          isCurrent,
          isUpcoming,
          remaining: remaining > 0 ? remaining : 0
        }
      })
    },

     // 复制邀请链接
     copyLink() {
      if (!this.affiliateLink) {
        this.$notification.warning({
          message: '邀请链接未生成',
          description: '邀请链接正在生成中，请稍后再试',
          placement: 'topRight'
        })
        return
      }

      navigator.clipboard.writeText(this.affiliateLink).then(() => {
        this.$notification.success({
          message: '邀请链接已复制',
          description: '邀请链接已成功复制到剪贴板，快去分享给好友吧！',
          placement: 'topRight',
          duration: 3,
          style: {
            width: '380px',
            marginTop: '101px',
            borderRadius: '8px',
            boxShadow: '0 6px 16px -8px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05), 0 3px 6px -4px rgba(0, 0, 0, 0.12)'
          }
        })
      }).catch(() => {
        this.$notification.error({
          message: '复制失败',
          description: '复制邀请链接失败，请手动复制',
          placement: 'topRight'
        })
      })
    },

    // 预生成邀请二维码（后台静默生成）
    async preGenerateQRCode() {
      if (!this.affiliateLink || this.qrPreGenerated) {
        return
      }

      try {
        console.log('开始预生成邀请二维码...')

        // 调用后端API生成二维码并上传到TOS
        const response = await this.$http.post('/api/usercenter/generateReferralQRCode', null, {
          params: {
            url: this.affiliateLink
          }
        })

        if (response && response.success) {
          // 静默保存二维码URL
          this.qrCodeUrl = response.result
          this.qrPreGenerated = true
          console.log('邀请二维码预生成成功:', this.qrCodeUrl)
        }
      } catch (error) {
        console.error('预生成二维码失败:', error)
        // 预生成失败不显示错误提示，用户点击时再重试
      }
    },

    // 生成邀请二维码（用户主动点击）
    async generateQRCode() {
      if (!this.affiliateLink) {
        this.$notification.warning({
          message: '邀请链接未生成',
          description: '请等待邀请链接生成完成后再生成二维码',
          placement: 'topRight'
        })
        return
      }

      // 如果已经预生成，直接显示
      if (this.qrPreGenerated && this.qrCodeUrl) {
        this.showQRModal = true
        return
      }

      try {
        this.qrLoading = true

        // 调用后端API生成二维码并上传到TOS
        const response = await this.$http.post('/api/usercenter/generateReferralQRCode', null, {
          params: {
            url: this.affiliateLink
          }
        })

        console.log('二维码生成响应:', response)

        if (response && response.success) {
          // 使用CDN地址
          this.qrCodeUrl = response.result
          this.qrPreGenerated = true
          this.showQRModal = true

          this.$notification.success({
            message: '二维码生成成功',
            description: '邀请二维码已生成并存储到CDN，可以下载保存',
            placement: 'topRight'
          })
        } else {
          const errorMsg = (response && response.message) || '生成失败'
          throw new Error(errorMsg)
        }
      } catch (error) {
        console.error('生成二维码失败:', error)
        this.$notification.error({
          message: '生成失败',
          description: error.message || '二维码生成失败，请稍后重试',
          placement: 'topRight'
        })
      } finally {
        this.qrLoading = false
      }
    },

    // 下载二维码
    downloadQRCode() {
      if (!this.qrCodeUrl) return

      try {
        // 从邀请链接中提取邀请码
        const referralCode = this.extractReferralCode(this.affiliateLink)

        // 通过后端代理下载，避免CORS问题
        const backendUrl = process.env.VUE_APP_API_BASE_URL || 'http://localhost:8080/jeecg-boot'
        const downloadUrl = `${backendUrl}/api/usercenter/downloadReferralQRCode?url=${encodeURIComponent(this.qrCodeUrl)}&code=${referralCode}&t=${Date.now()}`

        console.log('下载URL:', downloadUrl)

        // 使用隐藏iframe下载，避免页面跳动
        const iframe = document.createElement('iframe')
        iframe.style.display = 'none'
        iframe.style.position = 'absolute'
        iframe.style.left = '-9999px'
        iframe.src = downloadUrl
        document.body.appendChild(iframe)

        // 3秒后移除iframe
        setTimeout(() => {
          if (iframe.parentNode) {
            document.body.removeChild(iframe)
          }
        }, 3000)

        this.$notification.success({
          message: '下载开始',
          description: `邀请二维码_${referralCode}.png 正在下载`,
          placement: 'topRight'
        })
      } catch (error) {
        console.error('下载二维码失败:', error)
        this.$notification.error({
          message: '下载失败',
          description: '二维码下载失败，请稍后重试',
          placement: 'topRight'
        })
      }
    },

    // 从邀请链接中提取邀请码
    extractReferralCode(url) {
      if (!url) return 'UNKNOWN'

      try {
        const urlObj = new URL(url)
        const refParam = urlObj.searchParams.get('ref')
        return refParam || 'UNKNOWN'
      } catch (error) {
        console.error('提取邀请码失败:', error)
        return 'UNKNOWN'
      }
    },

    // 显示提现弹窗
    openWithdrawModal() {
      if (this.availableEarnings < 50) {
        this.$notification.warning({
          message: '提现金额不足',
          description: '最低提现金额为50元，请继续邀请获得更多收益',
          placement: 'topRight'
        })
        return
      }
      this.showWithdrawModal = true
    },

    // 处理提现申请
    async handleWithdraw() {
      this.withdrawForm.validateFields(async (err, values) => {
        if (err) return

        // 二次确认弹窗
        const h = this.$createElement
        this.$confirm({
          title: '确认提现申请',
          content: h('div', { style: { margin: '16px 0' } }, [
            h('p', { style: { marginBottom: '8px' } }, [
              h('strong', '提现金额：'),
              `¥${values.amount}`
            ]),
            h('p', { style: { marginBottom: '8px' } }, [
              h('strong', '支付宝账号：'),
              values.alipayAccount
            ]),
            h('p', { style: { marginBottom: '8px' } }, [
              h('strong', '收款人姓名：'),
              values.realName
            ]),
            h('p', { style: { color: '#ff4d4f', marginTop: '12px', marginBottom: '0' } }, [
              h('strong', '注意：'),
              '请再次核实一遍提现信息，请确认信息无误！'
            ])
          ]),
          okText: '确认提现',
          cancelText: '取消',
          centered: true,
          width: 400,
          onOk: async () => {
            await this.submitWithdrawRequest(values)
          }
        })
      })
    },

    // 提交提现申请
    async submitWithdrawRequest(values) {
      this.withdrawLoading = true

      try {
        const params = {
          withdrawalAmount: values.amount,
          realName: values.realName,
          alipayAccount: values.alipayAccount
        }

        const response = await this.$http.post('/api/usercenter/applyWithdrawal', params)

        if (response.success) {
          this.withdrawLoading = false
          this.showWithdrawModal = false
          this.withdrawForm.resetFields()

          this.$notification.success({
            message: '提现申请成功',
            description: '您的提现申请已提交，预计1-3个工作日到账',
            placement: 'topRight'
          })

          // 刷新数据
          await Promise.all([
            this.loadReferralData(),
            this.loadWithdrawRecords()
          ])
        } else {
          this.withdrawLoading = false
          this.$notification.error({
            message: '提现申请失败',
            description: response.message || '申请失败，请重试',
            placement: 'topRight'
          })
        }
      } catch (error) {
        this.withdrawLoading = false
        console.error('提现申请失败:', error)

        // 检查是否是HTTP响应错误，如果是则显示后端返回的错误信息
        if (error.response && error.response.data && error.response.data.message) {
          this.$notification.error({
            message: '提现申请失败',
            description: error.response.data.message,
            placement: 'topRight'
          })
        } else if (error.message) {
          this.$notification.error({
            message: '提现申请失败',
            description: error.message,
            placement: 'topRight'
          })
        } else {
          this.$notification.error({
            message: '提现申请失败',
            description: '网络错误，请稍后重试',
            placement: 'topRight'
          })
        }
      }
    },

    // 加载邀请用户列表
    async loadReferralUsers() {
      try {
        this.usersLoading = true

        const params = {
          current: (this.usersPagination && this.usersPagination.current) || 1,
          size: (this.usersPagination && this.usersPagination.pageSize) || 10,
          orderBy: (this.usersSort && this.usersSort.orderBy) || 'total_reward',
          order: (this.usersSort && this.usersSort.order) || 'desc'
        }

        console.log('加载邀请用户参数:', params)

        const response = await this.$http.get('/api/usercenter/referralList', { params })

        if (response && response.success) {
          const result = response.result || {}
          const records = result.records || []

          // 更新分页信息
          if (this.usersPagination) {
            this.usersPagination.total = result.total || 0
          }

          // 转换数据格式
          this.referralUsers = records.map((item, index) => ({
            key: item.id || index,
            nickname: item.referee_nickname || `用户***${index + 1}`,
            avatar: item.referee_avatar || '',
            registerTime: item.register_time || '',
            reward: item.total_reward || '0.00'
          }))
        } else {
          this.referralUsers = []
          if (this.usersPagination) {
            this.usersPagination.total = 0
          }
        }
      } catch (error) {
        console.error('获取邀请用户列表失败:', error)
        this.referralUsers = []
        // 如果是网络错误或其他错误，显示友好提示
        if (error.response && error.response.status === 401) {
          this.$message.warning('登录已过期，请重新登录')
        }
      } finally {
        this.usersLoading = false
      }
    },

    // 加载提现记录
    async loadWithdrawRecords() {
      try {
        this.recordsLoading = true

        const params = {
          current: (this.withdrawPagination && this.withdrawPagination.current) || 1,
          size: (this.withdrawPagination && this.withdrawPagination.pageSize) || 10,
          orderBy: (this.withdrawSort && this.withdrawSort.orderBy) || 'apply_time',
          order: (this.withdrawSort && this.withdrawSort.order) || 'desc'
        }

        // 添加筛选参数
        if (this.withdrawFilter) {
          if (this.withdrawFilter.minAmount !== null && this.withdrawFilter.minAmount !== '') {
            params.minAmount = this.withdrawFilter.minAmount
          }
          if (this.withdrawFilter.maxAmount !== null && this.withdrawFilter.maxAmount !== '') {
            params.maxAmount = this.withdrawFilter.maxAmount
          }
          if (this.withdrawFilter.status !== null) {
            params.status = this.withdrawFilter.status
          }
          if (this.withdrawFilter.dateRange && this.withdrawFilter.dateRange.length === 2) {
            // 处理moment对象，转换为YYYY-MM-DD格式
            params.startDate = this.withdrawFilter.dateRange[0].format
              ? this.withdrawFilter.dateRange[0].format('YYYY-MM-DD')
              : this.withdrawFilter.dateRange[0]
            params.endDate = this.withdrawFilter.dateRange[1].format
              ? this.withdrawFilter.dateRange[1].format('YYYY-MM-DD')
              : this.withdrawFilter.dateRange[1]
          }
          if (this.withdrawFilter.completeDateRange && this.withdrawFilter.completeDateRange.length === 2) {
            // 处理moment对象，转换为YYYY-MM-DD格式
            params.completeStartDate = this.withdrawFilter.completeDateRange[0].format
              ? this.withdrawFilter.completeDateRange[0].format('YYYY-MM-DD')
              : this.withdrawFilter.completeDateRange[0]
            params.completeEndDate = this.withdrawFilter.completeDateRange[1].format
              ? this.withdrawFilter.completeDateRange[1].format('YYYY-MM-DD')
              : this.withdrawFilter.completeDateRange[1]
          }
        }

        console.log('加载提现记录参数:', params)
        console.log('原始筛选条件:', {
          dateRange: this.withdrawFilter.dateRange,
          completeDateRange: this.withdrawFilter.completeDateRange
        })

        const response = await this.$http.get('/api/usercenter/withdrawalHistory', { params })

        if (response && response.success) {
          const result = response.result || {}
          const records = result.records || []

          // 更新分页信息
          if (this.withdrawPagination) {
            this.withdrawPagination.total = result.total || 0
          }

          // 转换数据格式
          this.withdrawRecords = records.map((item, index) => ({
            key: item.id || index,
            id: item.id,
            amount: item.withdrawal_amount || '0.00',
            method: item.withdrawalMethod || '支付宝',
            applyTime: item.apply_time || '',
            status: this.getWithdrawStatusText(item.status, item.review_remark),
            rawStatus: item.status,
            completeTime: item.review_time || '-'
          }))
        } else {
          this.withdrawRecords = []
          if (this.withdrawPagination) {
            this.withdrawPagination.total = 0
          }
        }
      } catch (error) {
        console.error('获取提现记录失败:', error)
        this.withdrawRecords = []
        // 如果是网络错误或其他错误，显示友好提示
        if (error.response && error.response.status === 401) {
          this.$message.warning('登录已过期，请重新登录')
        }
      } finally {
        this.recordsLoading = false
      }
    },

    // 获取提现状态文本
    getWithdrawStatusText(status, reviewRemark) {
      const statusMap = {
        0: '待审核',
        1: '待审核',
        2: '已完成',
        3: '已拒绝',
        4: '已取消'
      }
      let statusText = statusMap[status] || '未知状态'

      // 如果是已拒绝状态且有拒绝原因，则添加原因
      if (status === 3 && reviewRemark) {
        statusText += `（${reviewRemark}）`
      }

      return statusText
    },

    // 获取状态颜色
    getStatusColor(statusText) {
      // 处理带拒绝原因的状态文本
      if (statusText.includes('已拒绝')) {
        return 'red'
      }

      const colorMap = {
        '已完成': 'green',
        '处理中': 'blue',
        '待审核': 'orange',
        '已取消': 'gray'
      }
      return colorMap[statusText] || 'default'
    },

    // 获取排序状态（暂时保留，可能后续需要）
    getSortOrder(field) {
      if (this.usersSort && this.usersSort.orderBy === field) {
        return this.usersSort.order === 'asc' ? 'ascend' : 'descend'
      }
      return null
    },

    // 处理邀请用户表格分页变化
    handleUsersTableChange(pagination, _filters, sorter) {
      console.log('表格变化:', { pagination, sorter })

      if (this.usersPagination && pagination) {
        this.usersPagination.current = pagination.current || 1
        this.usersPagination.pageSize = pagination.pageSize || 10
      }

      // 处理排序
      if (sorter && sorter.field && this.usersSort) {
        const fieldMap = {
          'nickname': 'nickname',
          'registerTime': 'register_time',
          'reward': 'total_reward'
        }

        const newOrderBy = fieldMap[sorter.field] || 'total_reward'

        // 如果点击的是同一个字段，切换排序方向
        if (this.usersSort.orderBy === newOrderBy) {
          this.usersSort.order = this.usersSort.order === 'asc' ? 'desc' : 'asc'
        } else {
          // 如果是新字段，使用默认排序方向
          this.usersSort.orderBy = newOrderBy
          if (newOrderBy === 'total_reward') {
            this.usersSort.order = 'desc' // 奖励金额默认降序
          } else {
            this.usersSort.order = 'asc' // 其他字段默认升序
          }
        }

        console.log('排序更新:', {
          field: sorter.field,
          order: sorter.order,
          orderBy: this.usersSort.orderBy,
          finalOrder: this.usersSort.order,
          clickedSameField: this.usersSort.orderBy === newOrderBy
        })

        // 排序时回到第一页
        if (this.usersPagination) {
          this.usersPagination.current = 1
        }
      }

      this.loadReferralUsers()
    },

    // 处理提现记录表格分页变化
    handleWithdrawTableChange(pagination, _filters, sorter) {
      console.log('提现记录表格变化:', { pagination, sorter })

      if (this.withdrawPagination && pagination) {
        this.withdrawPagination.current = pagination.current || 1
        this.withdrawPagination.pageSize = pagination.pageSize || 10
      }

      // 处理排序
      if (sorter && sorter.field && this.withdrawSort) {
        const fieldMap = {
          'amount': 'withdrawal_amount',
          'applyTime': 'apply_time',
          'status': 'status',
          'completeTime': 'review_time'
        }

        const newOrderBy = fieldMap[sorter.field] || 'apply_time'

        // 如果点击的是同一个字段，切换排序方向
        if (this.withdrawSort.orderBy === newOrderBy) {
          this.withdrawSort.order = this.withdrawSort.order === 'asc' ? 'desc' : 'asc'
        } else {
          // 如果是新字段，使用默认排序方向
          this.withdrawSort.orderBy = newOrderBy
          if (newOrderBy === 'apply_time') {
            this.withdrawSort.order = 'desc' // 申请时间默认降序
          } else if (newOrderBy === 'withdrawal_amount') {
            this.withdrawSort.order = 'desc' // 金额默认降序
          } else {
            this.withdrawSort.order = 'asc' // 其他字段默认升序
          }
        }

        console.log('提现记录排序更新:', {
          field: sorter.field,
          order: sorter.order,
          orderBy: this.withdrawSort.orderBy,
          finalOrder: this.withdrawSort.order
        })

        // 排序时回到第一页
        if (this.withdrawPagination) {
          this.withdrawPagination.current = 1
        }
      }

      this.loadWithdrawRecords()
    },

    // 处理提现记录筛选
    handleWithdrawFilter() {
      // 筛选时回到第一页
      if (this.withdrawPagination) {
        this.withdrawPagination.current = 1
      }
      this.loadWithdrawRecords()
    },

    // 重置提现记录筛选
    handleWithdrawReset() {
      this.withdrawFilter = {
        minAmount: null,
        maxAmount: null,
        status: null,
        dateRange: [],
        completeDateRange: []
      }

      // 重置时回到第一页
      if (this.withdrawPagination) {
        this.withdrawPagination.current = 1
      }

      this.loadWithdrawRecords()
    },

    // 处理取消提现
    handleCancelWithdraw(record) {
      const h = this.$createElement
      this.$confirm({
        title: '确认取消提现',
        content: h('div', { style: { margin: '16px 0' } }, [
          h('p', { style: { marginBottom: '8px' } }, [
            h('strong', '提现金额：'),
            `¥${record.amount}`
          ]),
          h('p', { style: { marginBottom: '8px' } }, [
            h('strong', '申请时间：'),
            record.applyTime
          ]),
          h('p', { style: { color: '#ff4d4f', marginTop: '12px', marginBottom: '0' } }, [
            h('strong', '注意：'),
            '取消后金额将返还到可提现余额，此操作不可撤销！'
          ])
        ]),
        okText: '确认取消',
        okType: 'danger',
        cancelText: '返回',
        centered: true,
        width: 400,
        onOk: async () => {
          await this.confirmCancelWithdraw(record)
        }
      })
    },

    // 确认取消提现
    async confirmCancelWithdraw(record) {
      this.cancelLoading = true

      try {
        const params = {
          withdrawalId: record.id
        }

        const response = await this.$http.post('/api/usercenter/cancelWithdrawal', params)

        if (response.success) {
          this.$notification.success({
            message: '取消成功',
            description: '提现申请已取消，金额已返还到可提现余额',
            placement: 'topRight'
          })

          // 刷新数据
          await Promise.all([
            this.loadReferralData(),
            this.loadWithdrawRecords()
          ])
        } else {
          this.$notification.error({
            message: '取消失败',
            description: response.message || '取消提现失败，请重试',
            placement: 'topRight'
          })
        }
      } catch (error) {
        console.error('取消提现失败:', error)

        if (error.response && error.response.data && error.response.data.message) {
          this.$notification.error({
            message: '取消失败',
            description: error.response.data.message,
            placement: 'topRight'
          })
        } else if (error.message) {
          this.$notification.error({
            message: '取消失败',
            description: error.message,
            placement: 'topRight'
          })
        } else {
          this.$notification.error({
            message: '取消失败',
            description: '网络错误，请稍后重试',
            placement: 'topRight'
          })
        }
      } finally {
        this.cancelLoading = false
      }
    },

     // 格式化数字显示
     formatNumber(num) {
       if (num === null || num === undefined) return '0'
       const number = parseFloat(num)
       if (isNaN(number)) return '0'

       // 如果是金额，保留两位小数
       if (num === this.totalEarnings) {
         return number.toLocaleString('zh-CN', {
           minimumFractionDigits: 2,
           maximumFractionDigits: 2
         })
       }

       // 其他数字不保留小数
       return number.toLocaleString('zh-CN')
     },

     // 获取角色显示名称
     getRoleDisplayName(roleCode) {
       switch (roleCode) {
         case 'VIP':
           return 'VIP用户'
         case 'SVIP':
           return 'SVIP用户'
         case 'user':
         default:
           return '普通用户'
       }
     },

     // 获取邀请人数要求文本
     getRequirementText(config) {
       const minReferrals = config.min_referrals
       const roleCode = config.role_code

       // 查找同角色的下一个等级
       const sameRoleConfigs = this.allLevelConfigs.filter(c => c.role_code === roleCode)
       const currentIndex = sameRoleConfigs.findIndex(c => c.id === config.id)
       const nextConfig = sameRoleConfigs[currentIndex + 1]

       if (roleCode === 'SVIP') {
         return '无要求'
       }

       if (nextConfig) {
         if (minReferrals === 0) {
           return `0-${nextConfig.min_referrals - 1}人`
         } else {
           return `${minReferrals}-${nextConfig.min_referrals - 1}人`
         }
       } else {
         return `${minReferrals}人以上`
       }
     }
   }
 }
</script>

<style scoped>
.affiliate-container {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #cbd5e1 100%);
  min-height: 100vh;
  padding: 2rem 0;
  position: relative;
}

/* Loading 覆盖层 */
.affiliate-loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #cbd5e1 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  backdrop-filter: blur(2px);
}

.loading-content {
  text-align: center;
  background: white;
  padding: 3rem 2rem;
  border-radius: 20px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  max-width: 400px;
  width: 90%;
  animation: loadingFadeIn 0.5s ease-out;
}

.loading-spinner {
  margin-bottom: 1.5rem;
}

.loading-spinner .ant-spin {
  color: #4f46e5;
}

.loading-text h3 {
  color: #1e293b;
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
  background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.loading-text p {
  color: #64748b;
  font-size: 0.95rem;
  margin: 0;
  line-height: 1.5;
}

/* Loading 动画 */
@keyframes loadingFadeIn {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* 内容过渡动画 */
.content-fade-enter-active {
  transition: all 0.6s ease-out;
}

.content-fade-enter {
  opacity: 0;
  transform: translateY(30px);
}

.content-fade-enter-to {
  opacity: 1;
  transform: translateY(0);
}

/* 简洁页面标题 */
.simple-header {
  text-align: center;
  padding: 2rem 0 3rem;
  max-width: 1200px;
  margin: 0 auto;
}

.simple-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0 0 0.5rem 0;
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.simple-subtitle {
  font-size: 1.1rem;
  color: #64748b;
  margin: 0 0 1.5rem 0;
}

.commission-badge {
  display: inline-flex;
  align-items: center;
  gap: 12px;
  background: white;
  padding: 12px 24px;
  border-radius: 50px;
  border: 2px solid #e2e8f0;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.badge-text {
  font-size: 1rem;
  font-weight: 600;
  color: #1e293b;
}

.badge-level {
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  color: white;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 500;
}

/* 分销内容区域 */
.affiliate-section {
  padding: 0 0 4rem 0;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.section-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 1.5rem 0;
  text-align: center;
}

/* 收益仪表板 */
.earnings-dashboard {
  background: white;
  border-radius: 20px;
  padding: 40px;
  margin-bottom: 40px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.earnings-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  gap: 24px;
}

.earning-card {
  display: flex;
  align-items: center;
  padding: 24px;
  border-radius: 16px;
  background: white;
  border: 2px solid #f1f5f9;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.earning-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--card-color);
}

.earning-card.primary {
  --card-color: #3b82f6;
}

.earning-card.success {
  --card-color: #10b981;
}

.earning-card.warning {
  --card-color: #f59e0b;
}

.earning-card.info {
  --card-color: #8b5cf6;
}

.earning-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.1);
  border-color: var(--card-color);
}

.card-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  color: white;
  font-size: 20px;
  background: var(--card-color);
}

.card-content {
  flex: 1;
}

.earning-number {
  font-size: 2rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 4px;
  line-height: 1;
}

.earning-label {
  font-size: 0.9rem;
  color: #6b7280;
  font-weight: 500;
}

/* 佣金等级进度 */
.commission-progress {
  background: white;
  border-radius: 20px;
  padding: 40px;
  margin-bottom: 40px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.progress-card {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 16px;
  padding: 32px;
  border: 2px solid #e2e8f0;
}

.current-level {
  margin-bottom: 24px;
}

.level-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.level-name {
  font-size: 1.2rem;
  font-weight: 600;
  color: #1f2937;
}

.level-rate {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 6px 16px;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 600;
}

.level-progress {
  margin-bottom: 8px;
}

.progress-text {
  text-align: center;
  font-size: 0.9rem;
  color: #6b7280;
  margin-top: 8px;
}

.next-level {
  padding-top: 24px;
  border-top: 1px solid #e5e7eb;
}

.next-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.next-text {
  font-size: 1rem;
  color: #374151;
  font-weight: 500;
}

.next-rate {
  background: #f3f4f6;
  color: #6b7280;
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 0.8rem;
  font-weight: 600;
}

.remaining {
  font-size: 0.9rem;
  color: #9ca3af;
  text-align: center;
}

/* 邀请工具 */
.tools-section {
  background: white;
  border-radius: 20px;
  padding: 40px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.tools-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 32px;
}

.tool-card {
  background: #fafbfc;
  border: 2px solid #f1f5f9;
  border-radius: 16px;
  padding: 32px;
  transition: all 0.3s ease;
}

.tool-card:hover {
  border-color: #667eea;
  transform: translateY(-2px);
  box-shadow: 0 12px 24px rgba(102, 126, 234, 0.15);
}

.tool-header {
  display: flex;
  align-items: flex-start;
  margin-bottom: 24px;
}

.tool-icon {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  color: white;
  font-size: 20px;
  flex-shrink: 0;
}

.tool-info h3 {
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 8px;
  color: #1f2937;
}

.tool-info p {
  color: #6b7280;
  line-height: 1.5;
  margin: 0;
}

.tool-content {
  margin-top: 16px;
}

.link-input {
  width: 100%;
}

/* 二维码弹窗 */
.qr-modal-content {
  text-align: center;
}

.qr-code-container {
  margin-bottom: 24px;
  padding: 20px;
  background: #f8fafc;
  border-radius: 12px;
  border: 2px dashed #d1d5db;
}

.qr-code-image {
  max-width: 100%;
  height: auto;
  border-radius: 8px;
}

.qr-actions {
  margin-top: 16px;
}

/* 提现弹窗 */
.withdraw-modal-content {
  padding: 8px 0;
}

.withdraw-info {
  background: #f8fafc;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 24px;
  border: 1px solid #e2e8f0;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-label {
  color: #64748b;
  font-size: 0.9rem;
}

.info-value {
  color: #1e293b;
  font-weight: 600;
  font-size: 1rem;
}

.withdraw-actions {
  text-align: right;
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid #f1f5f9;
}

.card-action {
  margin-top: 8px;
}

/* 邀请链接区域 */
.promotion-link-section {
  background: white;
  border-radius: 20px;
  padding: 2.5rem;
  margin-bottom: 3rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  border: 2px solid #e2e8f0;
}

.link-main-container {
  margin-bottom: 1.5rem;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.link-input-large {
  flex: 1;
}

.link-input-large .ant-input {
  font-size: 1.1rem;
  padding: 16px 20px;
  border-radius: 12px;
  border: 2px solid #e2e8f0;
  background: #f8fafc;
  transition: all 0.3s ease;
}

.link-input-large .ant-input:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.link-actions {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
  justify-content: center;
}

.copy-btn {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  border: none;
  font-weight: 600;
  border-radius: 12px;
  padding: 12px 32px;
  height: auto;
  font-size: 16px;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
  transition: all 0.3s ease;
  min-width: 140px;
}

.copy-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
  background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
}

.copy-btn:active {
  transform: translateY(0);
}

.qr-btn {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  border: none;
  color: white;
  font-weight: 600;
  border-radius: 12px;
  padding: 12px 32px;
  height: auto;
  font-size: 16px;
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
  transition: all 0.3s ease;
  min-width: 140px;
}

.qr-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(16, 185, 129, 0.4);
  background: linear-gradient(135deg, #059669 0%, #047857 100%);
  color: white;
}

.qr-btn:active {
  transform: translateY(0);
}

/* 按钮禁用状态 */
.copy-btn:disabled,
.qr-btn:disabled {
  background: #e5e7eb !important;
  color: #9ca3af !important;
  box-shadow: none !important;
  transform: none !important;
  cursor: not-allowed !important;
}

.copy-btn:disabled:hover,
.qr-btn:disabled:hover {
  background: #e5e7eb !important;
  transform: none !important;
  box-shadow: none !important;
}

.link-tips {
  background: #f0f9ff;
  border: 1px solid #bae6fd;
  border-radius: 8px;
  padding: 12px 16px;
  color: #0369a1;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  gap: 8px;
}

.link-tips strong {
  color: #1e40af;
  font-weight: 700;
}

/* 分成规则表格 */
.commission-rules {
  background: white;
  border-radius: 20px;
  padding: 2rem;
  margin-bottom: 3rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.rules-table {
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  overflow: hidden;
}

.rule-row {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  border-bottom: 1px solid #e2e8f0;
}

.rule-row:last-child {
  border-bottom: none;
}

.rule-row.header {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  font-weight: 700;
  color: #1e293b;
}

.rule-row.vip {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #cbd5e1 100%);
  color: #475569;
  font-weight: 500;
  border: 1px solid #94a3b8;
  box-shadow: 0 1px 3px rgba(148, 163, 184, 0.2);
}

.rule-row.svip {
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 50%, #f59e0b 100%);
  color: #92400e;
  font-weight: 600;
  border: 1px solid #d97706;
  box-shadow: 0 2px 4px rgba(217, 119, 6, 0.2);
}

.rule-cell {
  padding: 16px;
  text-align: center;
  border-right: 1px solid #e2e8f0;
}

.rule-cell:last-child {
  border-right: none;
}

.rule-cell.highlight {
  font-weight: 700;
  color: #dc2626;
  font-size: 1.1rem;
}

/* 表格容器 */
.users-table-container,
.records-table-container {
  background: white;
  border-radius: 20px;
  padding: 2rem;
  margin-bottom: 3rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.referral-users,
.withdraw-records {
  margin-bottom: 3rem;
}

.reward-amount,
.withdraw-amount {
  font-weight: 600;
  color: #059669;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-title {
    font-size: 2rem;
  }

  .page-subtitle {
    font-size: 1rem;
  }

  .commission-badge {
    flex-direction: column;
    gap: 8px;
  }

  .earnings-grid {
    grid-template-columns: 1fr;
  }

  .tools-grid {
    grid-template-columns: 1fr;
  }

  .earnings-dashboard,
  .commission-progress,
  .tools-section {
    padding: 24px;
  }

  .tool-card {
    padding: 24px;
  }

  .progress-card {
    padding: 24px;
  }

  .level-info,
  .next-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .tool-header {
    flex-direction: column;
    text-align: center;
  }

  .tool-icon {
    margin: 0 auto 16px;
  }
}

@media (max-width: 480px) {
  .affiliate-section {
    padding: 0 16px 60px;
  }

  .page-header {
    padding: 40px 16px 24px;
  }

  .earning-card {
    flex-direction: column;
    text-align: center;
  }

  .card-icon {
    margin: 0 auto 12px;
  }

  .promotion-link-section {
    padding: 1.5rem;
    margin-bottom: 2rem;
  }

  .link-main-container {
    gap: 1rem;
  }

  .link-input-large .ant-input {
    font-size: 1rem;
    padding: 14px 16px;
  }

  .link-actions {
    flex-direction: column;
    gap: 12px;
  }

  .copy-btn,
  .qr-btn {
    width: 100%;
    min-width: auto;
    padding: 14px 24px;
    font-size: 15px;
  }

  .rule-row {
    grid-template-columns: 1fr;
    text-align: left;
  }

  .rule-cell {
    border-right: none;
    border-bottom: 1px solid #e2e8f0;
    text-align: left;
  }

  .rule-cell:last-child {
    border-bottom: none;
  }

  .promotion-link-section,
  .commission-rules,
  .users-table-container,
  .records-table-container {
    padding: 1.5rem;
  }
}

/* 水平等级时间线样式 */
.level-timeline-horizontal {
  position: relative;
  padding: 20px 0;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  margin-bottom: 20px;
}

.level-step-horizontal {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  flex: 1;
  text-align: center;
  min-width: 0;
  padding: 0 10px;
}

.step-circle-horizontal {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 10px;
  z-index: 2;
  border: 2px solid #e8e8e8;
  position: relative;
  flex-shrink: 0;
}

.level-step-horizontal.completed .step-circle-horizontal {
  background-color: #52c41a;
  border-color: #52c41a;
  color: white;
}

.level-step-horizontal.current .step-circle-horizontal {
  background-color: #1890ff;
  border-color: #1890ff;
  color: white;
}

.level-step-horizontal.upcoming .step-circle-horizontal {
  border-color: #d9d9d9;
}

.step-circle-horizontal .step-number {
  font-size: 14px;
  font-weight: 600;
  color: #666;
}

.step-content-horizontal {
  padding: 0 5px;
  max-width: 120px;
}

.step-content-horizontal .step-title {
  font-weight: 600;
  font-size: 14px;
  margin-bottom: 4px;
  white-space: nowrap;
}

.step-content-horizontal .step-rate {
  color: #1890ff;
  font-weight: 500;
  margin-bottom: 4px;
}

.level-step-horizontal.completed .step-content-horizontal .step-rate {
  color: #52c41a;
}

.step-content-horizontal .step-requirement {
  color: #666;
  margin-bottom: 4px;
  font-size: 13px;
}

.step-content-horizontal .step-remaining {
  color: #ff4d4f;
  font-size: 12px;
}

.step-content-horizontal .step-completed {
  color: #52c41a;
  font-size: 12px;
}

.step-line-horizontal {
  position: absolute;
  left: calc(50% + 18px);
  width: calc(100% - 36px);
  top: 18px;
  height: 2px;
  background-color: #e8e8e8;
  z-index: 1;
}

.level-step-horizontal.completed .step-line-horizontal {
  background-color: #52c41a;
}

.level-step-horizontal:last-child .step-line-horizontal {
  display: none;
}

/* 响应式设计 - Loading */
@media (max-width: 768px) {
  .loading-content {
    padding: 2rem 1.5rem;
    margin: 0 1rem;
  }

  .loading-text h3 {
    font-size: 1.25rem;
  }

  .loading-text p {
    font-size: 0.9rem;
  }
}
</style>
