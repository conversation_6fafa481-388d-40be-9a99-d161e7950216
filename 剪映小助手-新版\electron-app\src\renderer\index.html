<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>剪映小助手 - 智界AigcView</title>
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="styles/components.css">
</head>
<body>
    <div id="app">
        <!-- 顶部导航 -->
        <header class="header">
            <div class="header-content">
                <div class="logo">
                    <img src="../../assets/icon.png" alt="剪映小助手" class="logo-icon">
                    <h1 class="logo-text">剪映小助手</h1>
                    <span class="logo-subtitle">智界AigcView出品</span>
                </div>
                <nav class="nav">
                    <button class="nav-btn active" data-page="home">
                        <span class="nav-icon">🏠</span>
                        <span class="nav-text">首页</span>
                    </button>
                    <button class="nav-btn" data-page="history">
                        <span class="nav-icon">📂</span>
                        <span class="nav-text">历史</span>
                    </button>
                    <button class="nav-btn" data-page="settings">
                        <span class="nav-icon">⚙️</span>
                        <span class="nav-text">设置</span>
                    </button>
                </nav>
            </div>
        </header>

        <!-- 主内容区域 -->
        <main class="main">
            <!-- 首页 -->
            <div id="home-page" class="page active">
                <div class="page-content">
                    <div class="import-section">
                        <h2 class="section-title">📥 导入剪映草稿</h2>
                        <p class="section-desc">粘贴从扣子平台获取的草稿链接，一键导入到剪映</p>
                        
                        <div class="input-group">
                            <label for="draft-url" class="input-label">草稿链接（支持多个链接，每行一个）</label>
                            <div class="input-wrapper">
                                <textarea
                                    id="draft-url"
                                    class="url-textarea"
                                    placeholder="粘贴草稿链接，支持多个链接，每行一个：&#10;https://aigcview-plub.tos-s3-cn-guangzhou.volces.com/...&#10;https://aigcview-plub.tos-s3-cn-guangzhou.volces.com/..."
                                    rows="4"
                                    autocomplete="off"
                                ></textarea>
                                <button id="paste-btn" class="paste-btn" title="粘贴剪贴板内容">
                                    📋
                                </button>
                            </div>
                            <div id="url-validation" class="validation-message"></div>
                        </div>

                        <div class="action-group">
                            <button id="import-btn" class="import-btn" disabled>
                                <span class="btn-icon">🚀</span>
                                <span class="btn-text">导入到剪映</span>
                            </button>

                            <!-- 自动打开文件夹开关 -->
                            <div class="setting-toggle">
                                <label class="toggle-label">
                                    <input type="checkbox" id="auto-open-folder" class="toggle-checkbox" checked>
                                    <span class="toggle-slider"></span>
                                    <span class="toggle-text">导入成功后自动打开文件夹</span>
                                </label>
                            </div>

                            <div class="import-hint">
                                <span class="hint-icon">💡</span>
                                <span class="hint-text">支持快捷键 Ctrl+Enter 快速导入</span>
                            </div>
                        </div>
                    </div>

                    <!-- 下载进度显示 -->
                    <div class="download-progress-section" id="download-progress-section" style="display: none;">
                        <h3 class="section-subtitle">📊 下载进度</h3>

                        <!-- 当前文件下载进度 -->
                        <div class="current-download-progress" id="current-download-progress">
                            <div class="file-info">
                                <div class="file-name-row">
                                    <span class="file-icon" id="file-icon">📄</span>
                                    <span class="file-name" id="current-file-name">准备下载...</span>
                                    <span class="file-type" id="current-file-type"></span>
                                </div>
                                <div class="file-size-row">
                                    <span class="downloaded-size" id="downloaded-size">0 B</span>
                                    <span class="size-separator">/</span>
                                    <span class="total-size" id="total-size">未知</span>
                                </div>
                            </div>

                            <div class="progress-bar-container">
                                <div class="progress-bar">
                                    <div class="progress-fill" id="progress-fill" style="width: 0%"></div>
                                    <div class="progress-text" id="progress-text">0%</div>
                                </div>
                            </div>

                            <div class="download-stats">
                                <div class="stat-item">
                                    <span class="stat-label">速度:</span>
                                    <span class="stat-value" id="download-speed">0 B/s</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-label">剩余时间:</span>
                                    <span class="stat-value" id="eta">--</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-label">已用时间:</span>
                                    <span class="stat-value" id="elapsed-time">0:00</span>
                                </div>
                            </div>
                        </div>

                        <!-- 批量下载总进度 -->
                        <div class="batch-progress" id="batch-progress" style="display: none;">
                            <div class="batch-info">
                                <span class="batch-title">总进度:</span>
                                <span class="batch-current" id="batch-current">0</span>
                                <span class="batch-separator">/</span>
                                <span class="batch-total" id="batch-total">0</span>
                                <span class="batch-unit">个文件</span>
                            </div>
                            <div class="progress-bar-container">
                                <div class="progress-bar batch-progress-bar">
                                    <div class="progress-fill" id="batch-progress-fill" style="width: 0%"></div>
                                    <div class="progress-text" id="batch-progress-text">0%</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 下载日志 -->
                    <div class="download-logs">
                        <div class="logs-header">
                            <h3 class="section-subtitle">📥 下载日志</h3>
                            <button id="clear-logs-btn" class="clear-logs-btn">清空日志</button>
                        </div>

                        <!-- 日志内容 -->
                        <div id="logs-content" class="logs-content">
                            <div class="log-item">
                                <span class="log-time">[00:00:29]</span>
                                <span class="log-text">等待用户输入草稿链接...</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 历史页面 -->
            <div id="history-page" class="page">
                <div class="page-content">
                    <div class="page-header">
                        <h2 class="page-title">📂 导入历史</h2>
                        <p class="page-subtitle">只保存最近20条导入记录</p>
                    </div>
                    <div id="history-list" class="history-list">
                        <!-- 动态内容 -->
                    </div>
                </div>
            </div>

            <!-- 设置页面 -->
            <div id="settings-page" class="page">
                <div class="page-content">
                    <h2 class="page-title">⚙️ 应用设置</h2>
                    
                    <div class="settings-section">
                        <h3 class="settings-title">🎬 剪映配置</h3>
                        <div class="setting-item">
                            <label class="setting-label">剪映草稿文件夹</label>
                            <div class="setting-control">
                                <input
                                    type="text"
                                    id="jianying-path"
                                    class="path-input"
                                    placeholder="请选择剪映草稿文件夹..."
                                    readonly
                                >
                                <button id="browse-path-btn" class="browse-btn">浏览</button>
                            </div>
                            <p class="setting-desc">请手动选择剪映草稿文件夹</p>
                        </div>
                        

                    </div>

                    <div class="settings-section">
                        <h3 class="settings-title">🔄 版本更新</h3>
                        <div class="setting-item">
                            <label class="setting-label">自动检查更新</label>
                            <div class="setting-control">
                                <label class="switch">
                                    <input type="checkbox" id="auto-check-updates" checked>
                                    <span class="slider"></span>
                                </label>
                            </div>
                            <p class="setting-desc">控制后台定期检查更新。注意：无论此设置如何，应用启动时都会检查一次更新以确保及时获得重要更新</p>
                        </div>

                        <div class="setting-item">
                            <label class="setting-label">检查频率</label>
                            <div class="setting-control">
                                <select id="check-interval" class="select-input">
                                    <option value="3600000">1小时</option>
                                    <option value="14400000" selected>4小时</option>
                                    <option value="43200000">12小时</option>
                                    <option value="86400000">24小时</option>
                                </select>
                            </div>
                            <p class="setting-desc">设置后台检查更新的时间间隔（仅在启用自动检查更新时生效）</p>
                        </div>

                        <div class="setting-item">
                            <label class="setting-label">显示可选更新</label>
                            <div class="setting-control">
                                <label class="switch">
                                    <input type="checkbox" id="show-optional-updates" checked>
                                    <span class="slider"></span>
                                </label>
                            </div>
                            <p class="setting-desc">启用后将显示非强制性的版本更新提示</p>
                        </div>

                        <div class="setting-item">
                            <label class="setting-label">版本检查</label>
                            <div class="setting-control">
                                <button id="check-updates-btn" class="action-btn">
                                    <span class="btn-icon">🔍</span>
                                    <span class="btn-text">检查更新</span>
                                </button>
                                <span id="last-check-time" class="check-time">从未检查</span>
                            </div>
                            <p class="setting-desc">手动检查是否有新版本可用</p>
                        </div>
                    </div>

                    <div class="settings-section">
                        <h3 class="settings-title">💻 应用信息</h3>
                        <div class="app-info-card">
                            <div class="app-info-header">
                                <div class="app-basic-info">
                                    <h4 class="app-title">剪映小助手</h4>
                                    <div class="app-version-badge" id="app-version">v1.0.0</div>
                                </div>
                            </div>
                            <div class="app-info-details">
                                <div class="detail-row">
                                    <span class="detail-label">🏢 开发者</span>
                                    <span class="detail-value">智界AigcView</span>
                                </div>
                                <div class="detail-row">
                                    <span class="detail-label">🌐 官网</span>
                                    <a href="#" class="detail-link" id="website-link">www.aigcview.com</a>
                                </div>

                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- 模态框 -->
    <div id="modal-overlay" class="modal-overlay hidden">
        <div class="modal">
            <div class="modal-header">
                <h3 id="modal-title" class="modal-title">提示</h3>
                <button id="modal-close" class="modal-close">×</button>
            </div>
            <div class="modal-body">
                <div id="modal-content" class="modal-content">
                    <!-- 动态内容 -->
                </div>
            </div>
            <div class="modal-footer">
                <div id="modal-actions" class="modal-actions">
                    <!-- 动态按钮 -->
                </div>
            </div>
        </div>
    </div>

    <!-- 通知容器 -->
    <div id="notification-container" class="notification-container"></div>

    <!-- 版本更新模态弹窗 -->
    <div id="update-modal" class="update-modal hidden">
        <div class="update-modal-content">
            <div class="update-header">
                <div class="update-icon">🚀</div>
                <h2 id="update-title" class="update-title">发现新版本</h2>
            </div>

            <div class="update-body">
                <div class="version-info">
                    <div class="current-version">
                        <span class="version-label">当前版本：</span>
                        <span id="current-version" class="version-number">v1.0.0</span>
                    </div>
                    <div class="latest-version">
                        <span class="version-label">最新版本：</span>
                        <span id="latest-version" class="version-number">v1.0.1</span>
                    </div>
                </div>

                <div class="update-content">
                    <h4>更新内容：</h4>
                    <div id="update-content" class="content-text">
                        <!-- 动态内容 -->
                    </div>
                </div>

                <!-- 🚀 下载进度区域 -->
                <div id="update-download-progress" class="update-download-progress hidden">
                    <h4>正在下载更新...</h4>
                    <div class="progress-info">
                        <div class="progress-file">
                            <span class="progress-label">文件：</span>
                            <span id="update-progress-filename" class="progress-value">剪映小助手-v1.0.1.exe</span>
                        </div>
                        <div class="progress-status">
                            <span class="progress-label">状态：</span>
                            <span id="update-progress-status" class="progress-value">准备下载...</span>
                        </div>
                    </div>

                    <div class="progress-bar-container">
                        <div class="progress-bar">
                            <div id="update-progress-fill" class="progress-fill" style="width: 0%"></div>
                        </div>
                        <div class="progress-text">
                            <span id="update-progress-percent" class="progress-percent">0%</span>
                        </div>
                    </div>

                    <div class="progress-details">
                        <div class="progress-speed">
                            <span class="progress-label">速度：</span>
                            <span id="update-progress-speed" class="progress-value">0 KB/s</span>
                        </div>
                        <div class="progress-size">
                            <span class="progress-label">大小：</span>
                            <span id="update-progress-size" class="progress-value">0 MB / 0 MB</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="update-footer">
                <div id="update-actions" class="update-actions">
                    <!-- 动态按钮 -->
                </div>
            </div>
        </div>
    </div>

    <!-- 脚本文件 -->
    <script src="scripts/utils.js"></script>
    <script src="scripts/ui.js"></script>
    <script src="scripts/scroll-manager.js"></script>
    <script src="scripts/import.js"></script>
    <script src="scripts/settings.js"></script>
    <script src="scripts/download-progress.js"></script>
    <script src="scripts/main.js"></script>

    <!-- 确保所有脚本加载完成 -->
    <script>
        console.log('🔧 [HTML] 所有脚本加载完成')
        console.log('🔧 [HTML] DownloadProgressManager存在:', !!window.DownloadProgressManager)
        console.log('🔧 [HTML] 当前DOM状态:', document.readyState)
    </script>
</body>
</html>
