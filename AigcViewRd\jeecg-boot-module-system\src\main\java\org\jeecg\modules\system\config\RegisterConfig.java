package org.jeecg.modules.system.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * @Description: 注册功能配置类
 * @Author: jeecg-boot
 * @Date: 2025-06-19
 * @Version: V1.0
 */
@Data
@Component
@ConfigurationProperties(prefix = "aigc.register")
public class RegisterConfig {

    /**
     * 验证码配置
     */
    private VerifyCode verifyCode = new VerifyCode();

    /**
     * 密码配置
     */
    private Password password = new Password();

    /**
     * 邀请码配置
     */
    private InviteCode inviteCode = new InviteCode();

    /**
     * 安全策略配置
     */
    private Security security = new Security();

    @Data
    public static class VerifyCode {
        /**
         * 短信验证码配置
         */
        private Sms sms = new Sms();

        /**
         * 邮箱验证码配置
         */
        private Email email = new Email();

        /**
         * 图形验证码配置
         */
        private Captcha captcha = new Captcha();

        @Data
        public static class Sms {
            /**
             * 有效期（分钟）
             */
            private Integer expireMinutes = 5;

            /**
             * 发送间隔（秒）
             */
            private Integer sendIntervalSeconds = 60;
        }

        @Data
        public static class Email {
            /**
             * 有效期（分钟）
             */
            private Integer expireMinutes = 5;

            /**
             * 发送间隔（秒）
             */
            private Integer sendIntervalSeconds = 60;
        }

        @Data
        public static class Captcha {
            /**
             * 有效期（分钟）
             */
            private Integer expireMinutes = 2;
        }
    }

    @Data
    public static class Password {
        /**
         * 最小长度
         */
        private Integer minLength = 8;

        /**
         * 需要字母
         */
        private Boolean requireLetter = true;

        /**
         * 需要数字
         */
        private Boolean requireNumber = true;

        /**
         * 需要特殊字符
         */
        private Boolean requireSpecial = false;
    }

    @Data
    public static class InviteCode {
        /**
         * 邀请码长度
         */
        private Integer length = 8;

        /**
         * 邀请码类型：numeric-纯数字，alpha-纯字母，alphanumeric-字母数字混合
         */
        private String type = "alphanumeric";

        /**
         * 有效期（天），0表示永久有效
         */
        private Integer expireDays = 0;
    }

    @Data
    public static class Security {
        /**
         * 同一IP每小时注册限制次数
         */
        private Integer ipRegisterLimitHourly = 5;

        /**
         * 同一手机号每天注册尝试次数（防止恶意注册）
         */
        private Integer phoneRegisterLimitDaily = 3;

        /**
         * 同一邮箱每天注册尝试次数（防止恶意注册）
         */
        private Integer emailRegisterLimitDaily = 3;

        /**
         * 短信验证码每天总限制（登录场景）
         */
        private Integer smsCodeLimitDaily = 20;

        /**
         * 邮箱验证码每天总限制（登录场景）
         */
        private Integer emailCodeLimitDaily = 20;

        /**
         * 重置密码验证码每天限制
         */
        private Integer resetCodeLimitDaily = 3;
    }
}
