package org.jeecg.modules.jianying.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

/**
 * 生成视频请求参数
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class GenVideoRequest extends BaseJianyingRequest {
    
    @ApiModelProperty(value = "草稿地址（必填）", required = true,
                     example = "https://example.com/draft/123")
    @NotBlank(message = "draft_url不能为空")
    @JsonProperty("draft_url")
    private String zjDraftUrl;

    @ApiModelProperty(value = "API令牌（必填）", required = true,
                     example = "your_api_token_here")
    @NotBlank(message = "api_token不能为空")
    @JsonProperty("api_token")
    private String zjApiToken;
    
    @Override
    public String getSummary() {
        return "GenVideoRequest{" +
               "accessKey=" + (getAccessKey() != null && getAccessKey().length() > 10 ? 
                              getAccessKey().substring(0, 10) + "***" : "null") +
               ", draftUrl=" + (zjDraftUrl != null && zjDraftUrl.length() > 30 ? 
                               zjDraftUrl.substring(0, 30) + "***" : zjDraftUrl) +
               ", apiToken=" + (zjApiToken != null && zjApiToken.length() > 10 ? 
                               zjApiToken.substring(0, 10) + "***" : "null") +
               "}";
    }
}
