package org.jeecg.modules.system.entity;

import java.io.Serializable;
import java.util.Date;

import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 敏感词管理
 * @Author: jeecg-boot
 * @Date: 2025-06-22
 * @Version: V1.0
 */
@Data
@TableName("sys_sensitive_word")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="sys_sensitive_word对象", description="敏感词管理")
public class SysSensitiveWord implements Serializable {
    private static final long serialVersionUID = 1L;

    /**主键*/
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String id;
    
    /**敏感词*/
    @Excel(name = "敏感词", width = 15)
    @ApiModelProperty(value = "敏感词")
    private String word;
    
    /**分类*/
    @Excel(name = "分类", width = 15, dicCode = "sensitive_word_category")
    @ApiModelProperty(value = "分类")
    private String category;
    
    /**敏感级别*/
    @Excel(name = "敏感级别", width = 15, dicCode = "sensitive_word_level")
    @ApiModelProperty(value = "敏感级别(1-低危 2-中危 3-高危)")
    private Integer level;
    
    /**状态*/
    @Excel(name = "状态", width = 15, dicCode = "valid_status")
    @ApiModelProperty(value = "状态(0-禁用 1-启用)")
    private Integer status;
    
    /**命中次数*/
    @Excel(name = "命中次数", width = 15)
    @ApiModelProperty(value = "命中次数")
    private Integer hitCount;
    
    /**来源*/
    @Excel(name = "来源", width = 15)
    @ApiModelProperty(value = "来源(HOUBB-houbb库/CUSTOM-自定义)")
    private String source;
    
    /**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;
    
    /**创建日期*/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private Date createTime;
    
    /**更新人*/
    @ApiModelProperty(value = "更新人")
    private String updateBy;
    
    /**更新日期*/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private Date updateTime;
    
    /**备注*/
    @Excel(name = "备注", width = 15)
    @ApiModelProperty(value = "备注")
    private String remark;
}
