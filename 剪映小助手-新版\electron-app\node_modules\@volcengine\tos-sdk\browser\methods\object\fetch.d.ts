import { StorageClassType } from '../../TosExportEnum';
import { Acl } from '../../interface';
import TOSBase from '../base';
export interface FetchObjectInput {
    bucket?: string;
    key: string;
    url: string;
    ignoreSameKey?: boolean;
    acl?: Acl;
    grantFullControl?: string;
    grantRead?: string;
    grantReadAcp?: string;
    grantWriteAcp?: string;
    storageClass?: StorageClassType;
    ssecAlgorithm?: string;
    ssecKey?: string;
    ssecKeyMD5?: string;
    meta?: Record<string, string>;
    contentMD5?: string;
    headers?: {
        [key: string]: string | undefined;
    };
}
export interface FetchObjectOutput {
    VersionID?: string;
    Etag: string;
    SSECAlgorithm?: string;
    SSECKeyMD5?: string;
}
export declare function fetchObject(this: TOSBase, input: FetchObjectInput): Promise<import("../base").TosResponse<FetchObjectOutput>>;
export interface PutFetchTaskInput {
    bucket?: string;
    key: string;
    url: string;
    ignoreSameKey?: boolean;
    acl?: Acl;
    grantFullControl?: string;
    grantRead?: string;
    grantReadAcp?: string;
    grantWriteAcp?: string;
    storageClass?: StorageClassType;
    ssecAlgorithm?: string;
    ssecKey?: string;
    ssecKeyMD5?: string;
    meta?: Record<string, string>;
    contentMD5?: string;
    headers?: {
        [key: string]: string | undefined;
    };
}
export interface PutFetchTaskOutput {
    TaskId: string;
}
export declare function putFetchTask(this: TOSBase, input: PutFetchTaskInput): Promise<import("../base").TosResponse<PutFetchTaskOutput>>;
