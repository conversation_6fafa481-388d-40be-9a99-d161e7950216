package org.jeecg.modules.system.util;

import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.util.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Description: 退出登录缓存验证工具
 * @Author: AigcView
 * @Date: 2025-07-14
 * @Version: V1.0
 * 
 * 用于验证用户退出登录后是否完全清理了所有相关缓存，
 * 帮助排查设备冲突问题。
 */
@Slf4j
@Component
public class LogoutCacheVerifier {
    
    @Autowired
    private RedisUtil redisUtil;
    
    /**
     * 验证用户退出登录后的缓存清理情况
     * @param userId 用户ID
     * @param username 用户名
     * @return 验证结果报告
     */
    public CacheVerificationResult verifyUserLogoutCache(String userId, String username) {
        CacheVerificationResult result = new CacheVerificationResult();
        result.setUserId(userId);
        result.setUsername(username);
        
        log.info("🔍 开始验证用户 {} 的退出登录缓存清理情况", username);
        
        try {
            // 1. 检查单设备登录相关缓存
            checkSingleLoginCache(userId, username, result);
            
            // 2. 检查Token相关缓存
            checkTokenCache(username, result);
            
            // 3. 检查权限相关缓存
            checkPermissionCache(userId, username, result);
            
            // 4. 生成验证报告
            generateVerificationReport(result);
            
        } catch (Exception e) {
            log.error("验证用户退出登录缓存失败", e);
            result.setVerificationFailed(true);
            result.setErrorMessage(e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 检查单设备登录相关缓存
     */
    private void checkSingleLoginCache(String userId, String username, CacheVerificationResult result) {
        // 检查current_user_token_
        String currentTokenKey = "current_user_token_" + userId;
        boolean hasCurrentToken = redisUtil.hasKey(currentTokenKey);
        result.setHasCurrentUserToken(hasCurrentToken);
        
        if (hasCurrentToken) {
            Object tokenValue = redisUtil.get(currentTokenKey);
            result.setCurrentUserTokenValue(String.valueOf(tokenValue));
            log.warn("⚠️ 发现残留的current_user_token: {}", currentTokenKey);
        }
        
        // 检查token_device_
        String deviceKey = "token_device_" + userId;
        boolean hasDeviceInfo = redisUtil.hasKey(deviceKey);
        result.setHasDeviceInfo(hasDeviceInfo);
        
        if (hasDeviceInfo) {
            Object deviceValue = redisUtil.get(deviceKey);
            result.setDeviceInfoValue(String.valueOf(deviceValue));
            log.warn("⚠️ 发现残留的token_device: {}", deviceKey);
        }
    }
    
    /**
     * 🆕 检查Token相关缓存（移除危险的keys操作，改为简化检查）
     */
    private void checkTokenCache(String username, CacheVerificationResult result) {
        // 🆕 不再扫描所有kicked_token，避免性能问题和安全风险
        // 只记录为0，表示已优化为安全模式
        result.setKickedTokenCount(0);
        result.setUserTokenCacheCount(0);

        log.debug("🔒 Token缓存检查已优化为安全模式，不再扫描全局Token");
    }
    
    /**
     * 检查权限相关缓存
     */
    private void checkPermissionCache(String userId, String username, CacheVerificationResult result) {
        // 检查Shiro权限缓存
        String shiroKey = "sys:cache:shiro:cache:" + userId;
        boolean hasShiroCache = redisUtil.hasKey(shiroKey);
        result.setHasShiroCache(hasShiroCache);
        
        if (hasShiroCache) {
            log.warn("⚠️ 发现残留的Shiro权限缓存: {}", shiroKey);
        }
        
        // 检查用户信息缓存
        String userCacheKey = "sys:cache:user::" + username;
        boolean hasUserCache = redisUtil.hasKey(userCacheKey);
        result.setHasUserCache(hasUserCache);
        
        if (hasUserCache) {
            log.warn("⚠️ 发现残留的用户信息缓存: {}", userCacheKey);
        }
    }
    
    /**
     * 生成验证报告
     */
    private void generateVerificationReport(CacheVerificationResult result) {
        boolean isCleanLogout = !result.hasCurrentUserToken && 
                               !result.hasDeviceInfo && 
                               !result.hasShiroCache && 
                               !result.hasUserCache;
        
        result.setCleanLogout(isCleanLogout);
        
        if (isCleanLogout) {
            log.info("✅ 用户 {} 退出登录缓存清理完整", result.getUsername());
        } else {
            log.warn("❌ 用户 {} 退出登录缓存清理不完整，存在残留数据", result.getUsername());
            log.warn("   - current_user_token: {}", result.hasCurrentUserToken);
            log.warn("   - token_device: {}", result.hasDeviceInfo);
            log.warn("   - shiro_cache: {}", result.hasShiroCache);
            log.warn("   - user_cache: {}", result.hasUserCache);
        }
    }
    
    /**
     * 验证结果类
     */
    public static class CacheVerificationResult {
        private String userId;
        private String username;
        private boolean hasCurrentUserToken;
        private String currentUserTokenValue;
        private boolean hasDeviceInfo;
        private String deviceInfoValue;
        private boolean hasShiroCache;
        private boolean hasUserCache;
        private int kickedTokenCount;
        private int userTokenCacheCount;
        private boolean isCleanLogout;
        private boolean verificationFailed;
        private String errorMessage;
        
        // Getters and Setters
        public String getUserId() { return userId; }
        public void setUserId(String userId) { this.userId = userId; }
        
        public String getUsername() { return username; }
        public void setUsername(String username) { this.username = username; }
        
        public boolean isHasCurrentUserToken() { return hasCurrentUserToken; }
        public void setHasCurrentUserToken(boolean hasCurrentUserToken) { this.hasCurrentUserToken = hasCurrentUserToken; }
        
        public String getCurrentUserTokenValue() { return currentUserTokenValue; }
        public void setCurrentUserTokenValue(String currentUserTokenValue) { this.currentUserTokenValue = currentUserTokenValue; }
        
        public boolean isHasDeviceInfo() { return hasDeviceInfo; }
        public void setHasDeviceInfo(boolean hasDeviceInfo) { this.hasDeviceInfo = hasDeviceInfo; }
        
        public String getDeviceInfoValue() { return deviceInfoValue; }
        public void setDeviceInfoValue(String deviceInfoValue) { this.deviceInfoValue = deviceInfoValue; }
        
        public boolean isHasShiroCache() { return hasShiroCache; }
        public void setHasShiroCache(boolean hasShiroCache) { this.hasShiroCache = hasShiroCache; }
        
        public boolean isHasUserCache() { return hasUserCache; }
        public void setHasUserCache(boolean hasUserCache) { this.hasUserCache = hasUserCache; }
        
        public int getKickedTokenCount() { return kickedTokenCount; }
        public void setKickedTokenCount(int kickedTokenCount) { this.kickedTokenCount = kickedTokenCount; }
        
        public int getUserTokenCacheCount() { return userTokenCacheCount; }
        public void setUserTokenCacheCount(int userTokenCacheCount) { this.userTokenCacheCount = userTokenCacheCount; }
        
        public boolean isCleanLogout() { return isCleanLogout; }
        public void setCleanLogout(boolean cleanLogout) { isCleanLogout = cleanLogout; }
        
        public boolean isVerificationFailed() { return verificationFailed; }
        public void setVerificationFailed(boolean verificationFailed) { this.verificationFailed = verificationFailed; }
        
        public String getErrorMessage() { return errorMessage; }
        public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }
    }
}
