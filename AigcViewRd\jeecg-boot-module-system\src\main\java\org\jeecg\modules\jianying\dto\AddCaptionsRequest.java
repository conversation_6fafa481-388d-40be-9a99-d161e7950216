package org.jeecg.modules.jianying.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

/**
 * 批量添加字幕请求参数
 *
 * <AUTHOR>
 * @date 2025-01-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AddCaptionsRequest extends BaseJianyingRequest {

    @ApiModelProperty(value = "草稿地址，使用create_draft输出的draft_url即可（必填）", required = true,
                     example = "https://example.com/draft/123")
    @NotBlank(message = "draft_url不能为空")
    @JsonProperty("draft_url")
    private String zjDraftUrl;

    @ApiModelProperty(value = "字幕内容JSON字符串（必填），包含完整的字幕数组信息", required = true,
                     example = "[{\"start\":0,\"end\":3750000,\"text\":\"我需要考虑如何获取最新的差评信息\",\"keyword\":\"考虑\",\"keyword_color\":\"red\",\"keyword_font_size\":15,\"font_size\":15,\"in_animation\":\"飞入\",\"out_animation\":\"消散\",\"loop_animation\":\"晃动\",\"in_animation_duration\":1000000,\"out_animation_duration\":1000000,\"loop_animation_duration\":4000000}]")
    @NotBlank(message = "captions不能为空")
    @JsonProperty("captions")
    private String zjCaptions;

    @ApiModelProperty(value = "字幕透明度（可选，不设置时使用剪映原生默认值1.0）", example = "0.8")
    @JsonProperty("alpha")
    private Double zjAlpha;

    @ApiModelProperty(value = "X轴缩放比例（可选，不设置时使用剪映原生默认值1.0）", example = "1.2")
    @JsonProperty("scale_x")
    private Double zjScaleX;

    @ApiModelProperty(value = "Y轴缩放比例（可选，不设置时使用剪映原生默认值1.0）", example = "1.2")
    @JsonProperty("scale_y")
    private Double zjScaleY;

    @ApiModelProperty(value = "X轴位置变换（可选，不设置时使用剪映原生默认值0.0）", example = "100.0")
    @JsonProperty("transform_x")
    private Double zjTransformX;

    @ApiModelProperty(value = "Y轴位置变换（可选，不设置时使用剪映原生默认值0.0）", example = "-50.0")
    @JsonProperty("transform_y")
    private Double zjTransformY;

    @ApiModelProperty(value = "字体文件URL", example = "https://example.com/font.ttf")
    @JsonProperty("font")
    private String zjFont;

    @ApiModelProperty(value = "字体大小，默认：15", example = "15")
    @JsonProperty("font_size")
    private Integer zjFontSize;

    @ApiModelProperty(value = "文字颜色，格式：#ff1837", example = "#ff1837")
    @JsonProperty("text_color")
    private String zjTextColor;

    @ApiModelProperty(value = "边框颜色，格式：#fe8a80", example = "#fe8a80")
    @JsonProperty("border_color")
    private String zjBorderColor;

    @ApiModelProperty(value = "字幕对齐方式", example = "1")
    @JsonProperty("alignment")
    private Integer zjAlignment;

    @ApiModelProperty(value = "行间距", example = "1.2")
    @JsonProperty("line_spacing")
    private Double zjLineSpacing;

    @ApiModelProperty(value = "字符间距", example = "0.0")
    @JsonProperty("letter_spacing")
    private Double zjLetterSpacing;

    @ApiModelProperty(value = "文本样式，0：默认，1：富文本样式", example = "0")
    @JsonProperty("style_text")
    private Integer zjStyleText;

    @Override
    public String getSummary() {
        return "AddCaptionsRequest{" +
               "accessKey=" + (getAccessKey() != null && getAccessKey().length() > 10 ?
                              getAccessKey().substring(0, 10) + "***" : "null") +
               ", draftUrl=" + (zjDraftUrl != null && zjDraftUrl.length() > 30 ?
                               zjDraftUrl.substring(0, 30) + "***" : zjDraftUrl) +
               ", alpha=" + zjAlpha +
               ", scaleX=" + zjScaleX +
               ", scaleY=" + zjScaleY +
               "}";
    }
}
