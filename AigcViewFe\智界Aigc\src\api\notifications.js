import { axios } from '@/utils/request'

/**
 * 获取系统通知列表
 * 使用系统现有的接口：/sys/sysAnnouncementSend/getMyAnnouncementSend
 * 获取用户的系统消息
 */
export function getSystemNotifications(params = {}) {
  return axios({
    url: '/sys/sysAnnouncementSend/getMyAnnouncementSend',
    method: 'get',
    params: {
      pageNo: 1,
      pageSize: 50,
      ...params
    }
  }).then(response => {
    // 转换数据格式以兼容现有组件
    if (response.success && response.result) {
      const pageData = response.result
      const notifications = pageData.records || []

      // 映射字段以兼容现有组件
      const mappedNotifications = notifications.map(notification => ({
        ...notification,
        // 映射字段：anntId -> id
        id: notification.anntId,
        // 映射字段：titile -> title
        title: notification.titile,
        // 映射字段：msgContent -> content
        content: notification.msgContent,
        // 映射字段：readFlag -> isRead (0=未读, 1=已读)
        isRead: notification.readFlag === '1' ? 1 : 0,
        readFlag: notification.readFlag === '1',
        // 映射字段：sendTime -> createTime
        createTime: notification.sendTime
      }))

      return {
        ...response,
        result: {
          records: mappedNotifications,
          total: pageData.total || mappedNotifications.length
        }
      }
    }
    return response
  }).catch(error => {
    console.error('获取系统通知失败:', error)
    throw error
  })
}

/**
 * 标记通知为已读
 * 使用系统现有的接口：/sys/sysAnnouncementSend/editByAnntIdAndUserId
 */
export function markNotificationAsRead(anntId) {
  return axios({
    url: '/sys/sysAnnouncementSend/editByAnntIdAndUserId',
    method: 'put',
    data: {
      anntId: anntId,
      readFlag: '1'  // 1=已读
    }
  }).then(response => {
    console.log('标记已读API响应:', response)
    return response
  }).catch(error => {
    console.error('标记已读失败:', error)
    throw error
  })
}

/**
 * 标记通知为未读
 * 使用系统现有的接口：/sys/sysAnnouncementSend/editByAnntIdAndUserId
 */
export function markNotificationAsUnread(anntId) {
  return axios({
    url: '/sys/sysAnnouncementSend/editByAnntIdAndUserId',
    method: 'put',
    data: {
      anntId: anntId,
      readFlag: '0'  // 0=未读
    }
  }).then(response => {
    console.log('标记未读API响应:', response)
    return response
  }).catch(error => {
    console.error('标记未读失败:', error)
    throw error
  })
}

/**
 * 批量标记通知为已读
 * 使用系统现有的接口：/sys/sysAnnouncementSend/readAll
 */
export function markAllNotificationsAsRead() {
  return axios({
    url: '/sys/sysAnnouncementSend/readAll',
    method: 'put'
  }).then(response => {
    console.log('批量标记已读API响应:', response)
    return response
  }).catch(error => {
    console.error('批量标记已读失败:', error)
    throw error
  })
}

/**
 * 获取用户未读通知列表
 * 使用系统现有的接口：/sys/sysAnnouncementSend/getMyAnnouncementSend
 * 只获取未读通知
 */
export function getUnreadNotifications() {
  return axios({
    url: '/sys/sysAnnouncementSend/getMyAnnouncementSend',
    method: 'get',
    params: {
      pageNo: 1,
      pageSize: 100,
      readFlag: '0'  // 只获取未读通知
    }
  }).then(response => {
    // 转换数据格式以兼容现有组件
    if (response.success && response.result) {
      const pageData = response.result
      const notifications = pageData.records || []

      // 映射字段以兼容现有组件
      const mappedNotifications = notifications.map(notification => ({
        ...notification,
        // 映射字段：anntId -> id
        id: notification.anntId,
        // 映射字段：titile -> title
        title: notification.titile,
        // 映射字段：msgContent -> content
        content: notification.msgContent,
        // 映射字段：readFlag -> isRead (0=未读, 1=已读)
        isRead: 0,
        readFlag: false,
        // 映射字段：sendTime -> createTime
        createTime: notification.sendTime
      }))

      return {
        ...response,
        result: mappedNotifications
      }
    }
    return response
  }).catch(error => {
    console.error('获取未读通知失败:', error)
    throw error
  })
}

/**
 * 获取通知统计信息
 * 通过获取所有通知然后统计未读数量
 */
export function getNotificationStats() {
  return getSystemNotifications({
    pageNo: 1,
    pageSize: 1000  // 获取足够多的数据来统计全部通知
  }).then(response => {
    if (response.success && response.result && response.result.records) {
      const notifications = response.result.records
      const unreadCount = notifications.filter(n => !n.readFlag).length
      const readCount = notifications.filter(n => n.readFlag).length
      const total = response.result.total || notifications.length

      return {
        ...response,
        result: {
          total: total,
          unreadCount: unreadCount,
          readCount: readCount
        }
      }
    }
    return {
      success: true,
      result: {
        total: 0,
        unreadCount: 0,
        readCount: 0
      }
    }
  }).catch(error => {
    console.error('获取通知统计失败:', error)
    return {
      success: false,
      result: {
        total: 0,
        unreadCount: 0,
        readCount: 0
      }
    }
  })
}

/**
 * 获取未读通知数量
 */
export function getUnreadNotificationCount() {
  return getNotificationStats().then(response => {
    if (response.success && response.result) {
      return {
        ...response,
        result: {
          unreadCount: response.result.unreadCount || 0
        }
      }
    }
    return response
  })
}
