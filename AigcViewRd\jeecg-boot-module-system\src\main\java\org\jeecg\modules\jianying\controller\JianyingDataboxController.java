package org.jeecg.modules.jianying.controller;

import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.jianying.dto.*;
import org.jeecg.modules.jianying.service.JianyingDataboxService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 剪映小助手_智界数据箱 API控制器
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
@Api(tags = "剪映小助手-智界数据箱")
@RestController
@RequestMapping(value = "/api/jianying", produces = "application/json;charset=UTF-8")
@Slf4j
public class JianyingDataboxController {

    @Autowired
    private JianyingDataboxService jianyingDataboxService;
    
    /**
     * 关键帧数据生成器
     */
    @ApiOperation(value = "关键帧数据生成器", notes = "根据时间线制作关键帧数据")
    @PostMapping("/keyframes_infos")
    public Object keyframesInfos(@Valid @RequestBody KeyframesInfosRequest request) {

        try {
            log.info("关键帧数据生成器请求: {}", request.getSummary());

            JSONObject result = jianyingDataboxService.keyframesInfos(request);

            if (result.containsKey("error")) {
                // 返回竞争对手格式的错误
                JSONObject errorResponse = new JSONObject();
                errorResponse.put("error", result.getString("error"));
                return ResponseEntity.status(400).body(errorResponse);
            } else {
                // 直接返回竞争对手格式：{"keyframes_infos": "JSON字符串"}
                return ResponseEntity.ok(result);
            }

        } catch (Exception e) {
            log.error("关键帧数据生成失败", e);
            // 返回竞争对手格式的错误
            JSONObject errorResponse = new JSONObject();
            errorResponse.put("error", e.getMessage());
            return ResponseEntity.status(400).body(errorResponse);
        }
    }
    
    /**
     * 图片数据生成器
     */
    @ApiOperation(value = "图片数据生成器", notes = "根据时间线制作图片数据")
    @PostMapping("/imgs_infos")
    public Object imgsInfos(@Valid @RequestBody ImgsInfosRequest request) {

        try {
            log.info("图片数据生成器请求: {}", request.getSummary());

            JSONObject result = jianyingDataboxService.imgsInfos(request);

            if (result.getBoolean("success")) {
                JSONObject dataObj = result.getJSONObject("data");
                log.info("提取的data对象: {}", dataObj != null ? dataObj.toJSONString() : "null");

                // 直接返回核心数据，完全匹配竞争对手格式
                return ResponseEntity.ok(dataObj);
            } else {
                JSONObject errorResponse = new JSONObject();
                errorResponse.put("error", result.getString("error"));
                return ResponseEntity.status(400).body(errorResponse);
            }

        } catch (Exception e) {
            log.error("图片数据生成失败", e);
            JSONObject errorResponse = new JSONObject();
            errorResponse.put("error", "图片数据生成失败: " + e.getMessage());
            return ResponseEntity.status(400).body(errorResponse);
        }
    }
    
    /**
     * 链接提取器
     */
    @ApiOperation(value = "链接提取器", notes = "从文本中提取链接")
    @PostMapping("/get_url")
    public Object getUrl(@Valid @RequestBody GetUrlRequest request) {

        try {
            log.info("链接提取器请求: {}", request.getSummary());

            JSONObject result = jianyingDataboxService.getUrl(request);

            if (result.getBoolean("success")) {
                JSONObject dataObj = result.getJSONObject("data");
                log.info("提取的data对象: {}", dataObj != null ? dataObj.toJSONString() : "null");

                // 直接返回核心数据，完全匹配竞争对手格式
                return ResponseEntity.ok(dataObj);
            } else {
                return Result.error(result.getString("error"));
            }

        } catch (Exception e) {
            log.error("链接提取失败", e);
            return Result.error("链接提取失败: " + e.getMessage());
        }
    }

    /**
     * 音频时间线生成器
     */
    @ApiOperation(value = "音频时间线生成器", notes = "根据音频生成时间线")
    @PostMapping("/audio_timelines")
    public Object audioTimelines(@Valid @RequestBody AudioTimelinesRequest request) {

        try {
            log.info("音频时间线生成器请求: {}", request.getSummary());

            JSONObject result = jianyingDataboxService.audioTimelines(request);

            if (result.getBoolean("success")) {
                JSONObject dataObj = result.getJSONObject("data");
                log.info("提取的data对象: {}", dataObj != null ? dataObj.toJSONString() : "null");

                // 直接返回数据对象，不包装在Result中
                return ResponseEntity.ok(dataObj);
            } else {
                return Result.error(result.getString("error"));
            }

        } catch (Exception e) {
            log.error("音频时间线生成失败", e);
            return Result.error("音频时间线生成失败: " + e.getMessage());
        }
    }
    
    /**
     * 贴纸搜索器
     */
    @ApiOperation(value = "贴纸搜索器", notes = "查找贴纸")
    @PostMapping("/search_sticker")
    public Object searchSticker(@Valid @RequestBody SearchStickerRequest request) {

        try {
            log.info("贴纸搜索器请求: {}", request.getSummary());

            JSONObject result = jianyingDataboxService.searchSticker(request);

            if (result.getBoolean("success")) {
                JSONObject dataObj = result.getJSONObject("data");
                log.info("提取的data对象: {}", dataObj != null ? dataObj.toJSONString() : "null");

                // 直接返回核心数据，完全匹配竞争对手格式
                return ResponseEntity.ok(dataObj);
            } else {
                return Result.error(result.getString("error"));
            }

        } catch (Exception e) {
            log.error("贴纸搜索失败", e);
            return Result.error("贴纸搜索失败: " + e.getMessage());
        }
    }

    /**
     * 时间线生成器
     */
    @ApiOperation(value = "时间线生成器", notes = "自定义创建时间线列表")
    @PostMapping("/timelines")
    public Object timelines(@Valid @RequestBody TimelinesRequest request) {

        try {
            log.info("时间线生成器请求: {}", request.getSummary());

            JSONObject result = jianyingDataboxService.timelines(request);

            if (result.getBoolean("success")) {
                JSONObject dataObj = result.getJSONObject("data");
                log.info("提取的data对象: {}", dataObj != null ? dataObj.toJSONString() : "null");

                // 直接返回核心数据，完全匹配竞争对手格式
                return ResponseEntity.ok(dataObj);
            } else {
                return Result.error(result.getString("error"));
            }

        } catch (Exception e) {
            log.error("时间线生成失败", e);
            return Result.error("时间线生成失败: " + e.getMessage());
        }
    }

    /**
     * 音频数据生成器
     */
    @ApiOperation(value = "音频数据生成器", notes = "根据时间线制作音频数据")
    @PostMapping("/audio_infos")
    public Object audioInfos(@Valid @RequestBody AudioInfosRequest request) {

        try {
            log.info("音频数据生成器请求: {}", request.getSummary());

            JSONObject result = jianyingDataboxService.audioInfos(request);

            if (result.getBoolean("success")) {
                JSONObject dataObj = result.getJSONObject("data");
                log.info("提取的data对象: {}", dataObj != null ? dataObj.toJSONString() : "null");

                // 直接返回核心数据，完全匹配竞争对手格式
                return ResponseEntity.ok(dataObj);
            } else {
                return Result.error(result.getString("error"));
            }

        } catch (Exception e) {
            log.error("音频数据生成失败", e);
            return Result.error("音频数据生成失败: " + e.getMessage());
        }
    }

    /**
     * 特效数据生成器
     */
    @ApiOperation(value = "特效数据生成器", notes = "根据时间线制作特效数据")
    @PostMapping("/effect_infos")
    public Object effectInfos(@Valid @RequestBody EffectInfosRequest request) {

        try {
            log.info("特效数据生成器请求: {}", request.getSummary());

            JSONObject result = jianyingDataboxService.effectInfos(request);

            if (result.getBoolean("success")) {
                JSONObject dataObj = result.getJSONObject("data");
                log.info("提取的data对象: {}", dataObj != null ? dataObj.toJSONString() : "null");

                // 直接返回核心数据，完全匹配竞争对手格式
                return ResponseEntity.ok(dataObj);
            } else {
                return Result.error(result.getString("error"));
            }

        } catch (Exception e) {
            log.error("特效数据生成失败", e);
            return Result.error("特效数据生成失败: " + e.getMessage());
        }
    }

    /**
     * 字幕数据生成器
     */
    @ApiOperation(value = "字幕数据生成器", notes = "根据时间线制作字幕数据")
    @PostMapping("/caption_infos")
    public Object captionInfos(@Valid @RequestBody CaptionInfosRequest request) {

        try {
            log.info("字幕数据生成器请求: {}", request.getSummary());

            JSONObject result = jianyingDataboxService.captionInfos(request);

            if (result.getBoolean("success")) {
                JSONObject dataObj = result.getJSONObject("data");
                log.info("提取的data对象: {}", dataObj != null ? dataObj.toJSONString() : "null");

                // 直接返回核心数据，完全匹配竞争对手格式
                return ResponseEntity.ok(dataObj);
            } else {
                return Result.error(result.getString("error"));
            }

        } catch (Exception e) {
            log.error("字幕数据生成失败", e);
            return Result.error("字幕数据生成失败: " + e.getMessage());
        }
    }

    /**
     * 视频数据生成器
     */
    @ApiOperation(value = "视频数据生成器", notes = "根据时间线制作视频数据")
    @PostMapping("/video_infos")
    public Object videoInfos(@Valid @RequestBody VideoInfosRequest request) {

        try {
            log.info("视频数据生成器请求: {}", request.getSummary());

            JSONObject result = jianyingDataboxService.videoInfos(request);

            if (result.getBoolean("success")) {
                JSONObject dataObj = result.getJSONObject("data");
                log.info("提取的data对象: {}", dataObj != null ? dataObj.toJSONString() : "null");

                // 直接返回核心数据，完全匹配竞争对手格式
                return ResponseEntity.ok(dataObj);
            } else {
                return Result.error(result.getString("error"));
            }

        } catch (Exception e) {
            log.error("视频数据生成失败", e);
            return Result.error("视频数据生成失败: " + e.getMessage());
        }
    }

    /**
     * 字符串列表转对象
     */
    @ApiOperation(value = "字符串列表转对象", notes = "将字符串列表转换为对象数组")
    @PostMapping("/str_list_to_objs")
    public Object strListToObjs(@Valid @RequestBody StrListToObjsRequest request) {

        try {
            log.info("字符串列表转对象请求: {}", request.getSummary());

            JSONObject result = jianyingDataboxService.strListToObjs(request);

            log.info("Service返回结果: {}", result.toJSONString());

            if (result.getBoolean("success")) {
                JSONObject dataObj = result.getJSONObject("data");
                log.info("提取的data对象: {}", dataObj != null ? dataObj.toJSONString() : "null");

                // 直接返回infos数组，不包装在Result中
                if (dataObj != null && dataObj.containsKey("infos")) {
                    return ResponseEntity.ok(dataObj.getJSONArray("infos"));
                } else {
                    JSONObject errorResponse = new JSONObject();
                    errorResponse.put("error", "数据格式错误");
                    return ResponseEntity.status(400).body(errorResponse);
                }
            } else {
                JSONObject errorResponse = new JSONObject();
                errorResponse.put("error", result.getString("error"));
                return ResponseEntity.status(400).body(errorResponse);
            }

        } catch (Exception e) {
            log.error("字符串列表转对象失败", e);
            JSONObject errorResponse = new JSONObject();
            errorResponse.put("error", "字符串列表转对象失败: " + e.getMessage());
            return ResponseEntity.status(400).body(errorResponse);
        }
    }

    /**
     * 对象转字符串列表
     */
    @ApiOperation(value = "对象转字符串列表", notes = "将对象数组转换为字符串列表")
    @PostMapping("/objs_to_str_list")
    public Object objsToStrList(@Valid @RequestBody ObjsToStrListRequest request) {

        try {
            log.info("对象转字符串列表请求: {}", request.getSummary());

            JSONObject result = jianyingDataboxService.objsToStrList(request);

            if (result.getBoolean("success")) {
                JSONObject dataObj = result.getJSONObject("data");
                log.info("提取的data对象: {}", dataObj != null ? dataObj.toJSONString() : "null");

                // 返回result格式，匹配竞争对手
                if (dataObj != null && dataObj.containsKey("str_list")) {
                    JSONObject resultObj = new JSONObject();
                    resultObj.put("result", dataObj.getJSONArray("str_list"));
                    return ResponseEntity.ok(resultObj);
                } else {
                    JSONObject errorResponse = new JSONObject();
                    errorResponse.put("error", "数据格式错误");
                    return ResponseEntity.status(400).body(errorResponse);
                }
            } else {
                JSONObject errorResponse = new JSONObject();
                errorResponse.put("error", result.getString("error"));
                return ResponseEntity.status(400).body(errorResponse);
            }

        } catch (Exception e) {
            log.error("对象转字符串列表失败", e);
            JSONObject errorResponse = new JSONObject();
            errorResponse.put("error", "对象转字符串列表失败: " + e.getMessage());
            return ResponseEntity.status(400).body(errorResponse);
        }
    }

    /**
     * 字符串转列表
     */
    @ApiOperation(value = "字符串转列表", notes = "将字符串按分隔符转换为列表")
    @PostMapping(value = "/str_to_list", produces = "application/json;charset=UTF-8")
    public Object strToList(@Valid @RequestBody StrToListRequest request) {

        try {
            log.info("=== 收到字符串转列表请求 ===");
            log.info("请求详情: {}", request.getSummary());

            JSONObject result = jianyingDataboxService.strToList(request);

            if (result.getBoolean("success")) {
                JSONObject dataObj = result.getJSONObject("data");
                log.info("提取的data对象: {}", dataObj != null ? dataObj.toJSONString() : "null");

                // 直接返回list数组，不包装在Result中
                if (dataObj != null && dataObj.containsKey("list")) {
                    return ResponseEntity.ok(dataObj.getJSONArray("list"));
                } else {
                    JSONObject errorResponse = new JSONObject();
                    errorResponse.put("error", "数据格式错误");
                    return ResponseEntity.status(400).body(errorResponse);
                }
            } else {
                JSONObject errorResponse = new JSONObject();
                errorResponse.put("error", result.getString("error"));
                return ResponseEntity.status(400).body(errorResponse);
            }

        } catch (Exception e) {
            log.error("字符串转列表失败", e);
            JSONObject errorResponse = new JSONObject();
            errorResponse.put("error", "字符串转列表失败: " + e.getMessage());
            return ResponseEntity.status(400).body(errorResponse);
        }
    }

    /**
     * 语音识别时间线
     */
    @ApiOperation(value = "语音识别时间线", notes = "根据音频生成语音识别时间线")
    @PostMapping("/asr_timelines")
    public Result<?> asrTimelines(@Valid @RequestBody AsrTimelinesRequest request) {

        try {
            log.info("语音识别时间线请求: {}", request.getSummary());

            JSONObject result = jianyingDataboxService.asrTimelines(request);

            if (result.getBoolean("success")) {
                return Result.OK("语音识别时间线生成成功", result.getJSONObject("data"));
            } else {
                return Result.error(result.getString("error"));
            }

        } catch (Exception e) {
            log.error("语音识别时间线生成失败", e);
            return Result.error("语音识别时间线生成失败: " + e.getMessage());
        }
    }
}
