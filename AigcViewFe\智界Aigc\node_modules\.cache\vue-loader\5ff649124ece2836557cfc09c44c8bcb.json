{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\workflow\\components\\CreatorAgentForm.vue?vue&type=template&id=365d0b54&scoped=true&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\workflow\\components\\CreatorAgentForm.vue", "mtime": 1754512791552}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["var render = function() {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  return _c(\n    \"a-modal\",\n    {\n      staticClass: \"creator-agent-modal\",\n      attrs: {\n        title: _vm.modalTitle,\n        visible: _vm.visible,\n        width: 900,\n        \"confirm-loading\": _vm.stepLoading,\n        \"mask-closable\": false,\n        centered: true,\n        \"body-style\": { padding: \"0\" },\n        footer: null\n      },\n      on: { cancel: _vm.handleCancel }\n    },\n    [\n      _c(\n        \"div\",\n        { staticClass: \"step-header\" },\n        [\n          _c(\n            \"a-steps\",\n            {\n              staticClass: \"custom-steps\",\n              attrs: { current: _vm.currentStep }\n            },\n            [\n              _c(\"a-step\", {\n                attrs: { title: \"智能体信息\", description: \"填写基本信息\" }\n              }),\n              _c(\"a-step\", {\n                attrs: { title: \"工作流配置\", description: \"配置工作流\" }\n              }),\n              _c(\"a-step\", {\n                attrs: { title: \"完成创建\", description: \"创建完成\" }\n              })\n            ],\n            1\n          )\n        ],\n        1\n      ),\n      _c(\"div\", { staticClass: \"step-content\" }, [\n        _c(\n          \"div\",\n          {\n            directives: [\n              {\n                name: \"show\",\n                rawName: \"v-show\",\n                value: _vm.currentStep === 0,\n                expression: \"currentStep === 0\"\n              }\n            ],\n            staticClass: \"step-panel\"\n          },\n          [\n            _c(\"div\", { staticClass: \"panel-header\" }, [\n              _c(\n                \"h3\",\n                { staticClass: \"panel-title\" },\n                [\n                  _c(\"a-icon\", { attrs: { type: \"robot\" } }),\n                  _vm._v(\"\\n          智能体基本信息\\n        \")\n                ],\n                1\n              ),\n              _c(\"p\", { staticClass: \"panel-desc\" }, [\n                _vm._v(\"请填写智能体的基本信息，这些信息将展示给用户\")\n              ])\n            ]),\n            _c(\n              \"a-form-model\",\n              {\n                ref: \"form\",\n                staticClass: \"modern-form\",\n                attrs: { model: _vm.formData, rules: _vm.rules }\n              },\n              [\n                _c(\n                  \"div\",\n                  { staticClass: \"form-card\" },\n                  [\n                    _c(\n                      \"a-form-model-item\",\n                      { attrs: { prop: \"agentName\" } },\n                      [\n                        _c(\n                          \"div\",\n                          { staticClass: \"field-label\" },\n                          [\n                            _c(\"a-icon\", { attrs: { type: \"robot\" } }),\n                            _vm._v(\n                              \"\\n              智能体名称\\n              \"\n                            ),\n                            _c(\"span\", { staticClass: \"required-star\" }, [\n                              _vm._v(\"*\")\n                            ])\n                          ],\n                          1\n                        ),\n                        _c(\"a-input\", {\n                          staticClass: \"modern-input\",\n                          attrs: {\n                            placeholder: \"请输入智能体名称\",\n                            size: \"large\",\n                            \"max-length\": 100,\n                            \"show-count\": \"\"\n                          },\n                          model: {\n                            value: _vm.formData.agentName,\n                            callback: function($$v) {\n                              _vm.$set(_vm.formData, \"agentName\", $$v)\n                            },\n                            expression: \"formData.agentName\"\n                          }\n                        }),\n                        _c(\"div\", { staticClass: \"field-tips\" }, [\n                          _vm._v(\n                            \"\\n              为您的智能体起一个吸引人的名称\\n            \"\n                          )\n                        ])\n                      ],\n                      1\n                    )\n                  ],\n                  1\n                ),\n                _c(\n                  \"div\",\n                  { staticClass: \"form-card\" },\n                  [\n                    _c(\n                      \"a-form-model-item\",\n                      { attrs: { prop: \"agentDescription\" } },\n                      [\n                        _c(\n                          \"div\",\n                          { staticClass: \"field-label\" },\n                          [\n                            _c(\"a-icon\", { attrs: { type: \"file-text\" } }),\n                            _vm._v(\n                              \"\\n              智能体描述\\n              \"\n                            ),\n                            _c(\"span\", { staticClass: \"required-star\" }, [\n                              _vm._v(\"*\")\n                            ])\n                          ],\n                          1\n                        ),\n                        _c(\"a-textarea\", {\n                          staticClass: \"modern-textarea\",\n                          attrs: {\n                            placeholder: \"请详细描述您的智能体功能和特点\",\n                            rows: 4,\n                            \"max-length\": 1000,\n                            \"show-count\": \"\"\n                          },\n                          model: {\n                            value: _vm.formData.agentDescription,\n                            callback: function($$v) {\n                              _vm.$set(_vm.formData, \"agentDescription\", $$v)\n                            },\n                            expression: \"formData.agentDescription\"\n                          }\n                        }),\n                        _c(\"div\", { staticClass: \"field-tips\" }, [\n                          _vm._v(\n                            \"\\n              详细描述有助于用户了解您的智能体功能\\n            \"\n                          )\n                        ])\n                      ],\n                      1\n                    )\n                  ],\n                  1\n                ),\n                _c(\n                  \"div\",\n                  { staticClass: \"form-card\" },\n                  [\n                    _c(\n                      \"a-form-model-item\",\n                      { attrs: { prop: \"agentAvatar\" } },\n                      [\n                        _c(\n                          \"div\",\n                          { staticClass: \"field-label\" },\n                          [\n                            _c(\"a-icon\", { attrs: { type: \"picture\" } }),\n                            _vm._v(\n                              \"\\n              智能体头像\\n              \"\n                            ),\n                            _c(\"span\", { staticClass: \"required-star\" }, [\n                              _vm._v(\"*\")\n                            ])\n                          ],\n                          1\n                        ),\n                        _c(\"j-image-upload-deferred\", {\n                          ref: \"avatarUpload\",\n                          attrs: {\n                            isMultiple: false,\n                            bizPath: \"agent-avatar\",\n                            text: \"上传头像\"\n                          },\n                          model: {\n                            value: _vm.formData.agentAvatar,\n                            callback: function($$v) {\n                              _vm.$set(_vm.formData, \"agentAvatar\", $$v)\n                            },\n                            expression: \"formData.agentAvatar\"\n                          }\n                        }),\n                        _c(\"div\", { staticClass: \"field-tips\" }, [\n                          _vm._v(\n                            \"\\n              支持 JPG、PNG 格式，文件大小不超过 5MB\\n            \"\n                          )\n                        ])\n                      ],\n                      1\n                    )\n                  ],\n                  1\n                ),\n                _c(\n                  \"div\",\n                  { staticClass: \"form-card\" },\n                  [\n                    _c(\n                      \"a-form-model-item\",\n                      { attrs: { prop: \"experienceLink\" } },\n                      [\n                        _c(\n                          \"div\",\n                          { staticClass: \"field-label\" },\n                          [\n                            _c(\"a-icon\", { attrs: { type: \"link\" } }),\n                            _vm._v(\"\\n              体验链接\\n              \"),\n                            _c(\"span\", { staticClass: \"optional\" }, [\n                              _vm._v(\"（可选）\")\n                            ])\n                          ],\n                          1\n                        ),\n                        _c(\"a-input\", {\n                          staticClass: \"modern-input\",\n                          attrs: {\n                            placeholder: \"请输入体验链接\",\n                            size: \"large\",\n                            \"max-length\": 500\n                          },\n                          model: {\n                            value: _vm.formData.experienceLink,\n                            callback: function($$v) {\n                              _vm.$set(_vm.formData, \"experienceLink\", $$v)\n                            },\n                            expression: \"formData.experienceLink\"\n                          }\n                        }),\n                        _c(\"div\", { staticClass: \"field-tips\" }, [\n                          _vm._v(\n                            \"\\n              用户可以通过此链接体验您的智能体功能\\n            \"\n                          )\n                        ])\n                      ],\n                      1\n                    )\n                  ],\n                  1\n                ),\n                _c(\n                  \"div\",\n                  { staticClass: \"form-card\" },\n                  [\n                    _c(\n                      \"a-form-model-item\",\n                      { attrs: { prop: \"price\" } },\n                      [\n                        _c(\n                          \"div\",\n                          { staticClass: \"field-label\" },\n                          [\n                            _c(\"a-icon\", { attrs: { type: \"dollar\" } }),\n                            _vm._v(\"\\n              价格设置\\n              \"),\n                            _c(\"span\", { staticClass: \"required-star\" }, [\n                              _vm._v(\"*\")\n                            ])\n                          ],\n                          1\n                        ),\n                        _c(\n                          \"a-input-number\",\n                          {\n                            staticClass: \"modern-input-number\",\n                            attrs: {\n                              placeholder: \"请输入价格\",\n                              min: 0,\n                              max: 99999,\n                              precision: 2,\n                              step: 0.01,\n                              size: \"large\"\n                            },\n                            model: {\n                              value: _vm.formData.price,\n                              callback: function($$v) {\n                                _vm.$set(_vm.formData, \"price\", $$v)\n                              },\n                              expression: \"formData.price\"\n                            }\n                          },\n                          [\n                            _c(\"template\", { slot: \"addonBefore\" }, [\n                              _vm._v(\"¥\")\n                            ])\n                          ],\n                          2\n                        ),\n                        _c(\"div\", { staticClass: \"field-tips\" }, [\n                          _vm._v(\n                            \"\\n              设置智能体的使用价格，用户购买后可以使用您的智能体\\n            \"\n                          )\n                        ])\n                      ],\n                      1\n                    )\n                  ],\n                  1\n                )\n              ]\n            ),\n            _c(\n              \"div\",\n              { staticClass: \"step-footer\" },\n              [\n                _c(\n                  \"a-button\",\n                  {\n                    attrs: { disabled: _vm.stepLoading },\n                    on: { click: _vm.handleCancel }\n                  },\n                  [_vm._v(\"\\n          取消\\n        \")]\n                ),\n                _c(\n                  \"a-button\",\n                  {\n                    staticClass: \"next-btn\",\n                    attrs: { type: \"primary\", loading: _vm.stepLoading },\n                    on: { click: _vm.handleNext }\n                  },\n                  [\n                    _vm._v(\"\\n          下一步：配置工作流\\n          \"),\n                    _c(\"a-icon\", { attrs: { type: \"arrow-right\" } })\n                  ],\n                  1\n                )\n              ],\n              1\n            )\n          ],\n          1\n        ),\n        _c(\n          \"div\",\n          {\n            directives: [\n              {\n                name: \"show\",\n                rawName: \"v-show\",\n                value: _vm.currentStep === 1,\n                expression: \"currentStep === 1\"\n              }\n            ],\n            staticClass: \"step-panel\"\n          },\n          [\n            _c(\"div\", { staticClass: \"panel-header\" }, [\n              _c(\n                \"h3\",\n                { staticClass: \"panel-title\" },\n                [\n                  _c(\"a-icon\", { attrs: { type: \"apartment\" } }),\n                  _vm._v(\"\\n          工作流配置\\n        \")\n                ],\n                1\n              ),\n              _c(\"p\", { staticClass: \"panel-desc\" }, [\n                _vm._v(\"为您的智能体配置工作流，提升智能体的功能和效率\")\n              ])\n            ]),\n            _vm.createdAgent\n              ? _c(\"div\", { staticClass: \"created-agent-info\" }, [\n                  _c(\"div\", { staticClass: \"agent-summary\" }, [\n                    _c(\n                      \"div\",\n                      { staticClass: \"agent-avatar\" },\n                      [\n                        _vm.createdAgent.agentAvatar\n                          ? _c(\"img\", {\n                              attrs: {\n                                src: _vm.getFullAvatarUrl(\n                                  _vm.createdAgent.agentAvatar\n                                ),\n                                alt: _vm.createdAgent.agentName\n                              }\n                            })\n                          : _c(\"a-icon\", { attrs: { type: \"robot\" } })\n                      ],\n                      1\n                    ),\n                    _c(\"div\", { staticClass: \"agent-details\" }, [\n                      _c(\"h4\", [_vm._v(_vm._s(_vm.createdAgent.agentName))]),\n                      _c(\"p\", [\n                        _vm._v(_vm._s(_vm.createdAgent.agentDescription))\n                      ]),\n                      _c(\"span\", { staticClass: \"agent-price\" }, [\n                        _vm._v(\"¥\" + _vm._s(_vm.createdAgent.price))\n                      ])\n                    ])\n                  ])\n                ])\n              : _vm._e(),\n            _c(\"div\", { staticClass: \"workflow-section\" }, [\n              _c(\n                \"div\",\n                { staticClass: \"workflow-form\" },\n                [\n                  _c(\"div\", { staticClass: \"form-header\" }, [\n                    _c(\n                      \"h3\",\n                      [\n                        _c(\"a-icon\", { attrs: { type: \"apartment\" } }),\n                        _vm._v(\"\\n              新增工作流\\n            \")\n                      ],\n                      1\n                    ),\n                    _c(\"p\", [\n                      _vm._v(\"为您的智能体添加工作流，让它更加智能和实用\")\n                    ])\n                  ]),\n                  _c(\n                    \"a-form-model\",\n                    {\n                      ref: \"workflowFormRef\",\n                      attrs: {\n                        model: _vm.workflowFormData,\n                        rules: _vm.workflowRules,\n                        layout: \"vertical\"\n                      }\n                    },\n                    [\n                      _c(\n                        \"a-form-model-item\",\n                        {\n                          attrs: { label: \"工作流名称\", prop: \"workflowName\" }\n                        },\n                        [\n                          _c(\"a-input\", {\n                            attrs: {\n                              placeholder: \"请输入工作流名称，如：文档生成助手\",\n                              maxLength: 30,\n                              \"show-count\": \"\"\n                            },\n                            model: {\n                              value: _vm.workflowFormData.workflowName,\n                              callback: function($$v) {\n                                _vm.$set(\n                                  _vm.workflowFormData,\n                                  \"workflowName\",\n                                  $$v\n                                )\n                              },\n                              expression: \"workflowFormData.workflowName\"\n                            }\n                          })\n                        ],\n                        1\n                      ),\n                      _c(\n                        \"a-form-model-item\",\n                        {\n                          attrs: {\n                            label: \"工作流描述\",\n                            prop: \"workflowDescription\"\n                          }\n                        },\n                        [\n                          _c(\"a-textarea\", {\n                            attrs: {\n                              placeholder:\n                                \"请描述工作流的功能和用途，帮助用户了解其作用\",\n                              rows: 3,\n                              maxLength: 200,\n                              \"show-count\": \"\"\n                            },\n                            model: {\n                              value: _vm.workflowFormData.workflowDescription,\n                              callback: function($$v) {\n                                _vm.$set(\n                                  _vm.workflowFormData,\n                                  \"workflowDescription\",\n                                  $$v\n                                )\n                              },\n                              expression: \"workflowFormData.workflowDescription\"\n                            }\n                          })\n                        ],\n                        1\n                      ),\n                      _c(\n                        \"a-form-model-item\",\n                        {\n                          attrs: {\n                            label: \"输入参数说明\",\n                            prop: \"inputParamsDesc\"\n                          }\n                        },\n                        [\n                          _c(\"a-textarea\", {\n                            attrs: {\n                              placeholder:\n                                \"格式：参数:值 或 参数:\\\"值\\\" 或 参数:'值'（例如：name:\\\"张三\\\",age:25,city:'北京'）\",\n                              rows: 4,\n                              maxLength: 10000\n                            },\n                            on: { blur: _vm.handleInputParamsBlur },\n                            model: {\n                              value: _vm.workflowFormData.inputParamsDesc,\n                              callback: function($$v) {\n                                _vm.$set(\n                                  _vm.workflowFormData,\n                                  \"inputParamsDesc\",\n                                  $$v\n                                )\n                              },\n                              expression: \"workflowFormData.inputParamsDesc\"\n                            }\n                          }),\n                          _c(\n                            \"div\",\n                            {\n                              staticStyle: {\n                                color: \"#666\",\n                                \"font-size\": \"12px\",\n                                \"margin-top\": \"4px\"\n                              }\n                            },\n                            [\n                              _vm._v(\n                                \"\\n                * 必填项，例如：name:\\\"张三\\\",age:25,city:'北京' 支持中英文冒号逗号\\n              \"\n                              )\n                            ]\n                          )\n                        ],\n                        1\n                      ),\n                      _c(\n                        \"a-form-model-item\",\n                        {\n                          attrs: {\n                            label: \"工作流文件\",\n                            prop: \"workflowPackage\"\n                          }\n                        },\n                        [\n                          _c(\"div\", { staticClass: \"workflow-file-upload\" }, [\n                            _vm.workflowFileInfo\n                              ? _c(\n                                  \"div\",\n                                  { staticClass: \"uploaded-file-info\" },\n                                  [\n                                    _c(\n                                      \"div\",\n                                      {\n                                        staticClass: \"file-item\",\n                                        class: {\n                                          \"saved-file\":\n                                            _vm.workflowFileInfo.isSaved\n                                        }\n                                      },\n                                      [\n                                        _c(\"a-icon\", {\n                                          staticClass: \"file-icon\",\n                                          attrs: { type: \"file-zip\" }\n                                        }),\n                                        _c(\n                                          \"span\",\n                                          { staticClass: \"file-name\" },\n                                          [\n                                            _vm._v(\n                                              _vm._s(\n                                                _vm.workflowFileInfo\n                                                  .originalName ||\n                                                  _vm.workflowFileInfo.name\n                                              )\n                                            )\n                                          ]\n                                        ),\n                                        _vm.workflowFileInfo.isSaved\n                                          ? _c(\n                                              \"a-tag\",\n                                              {\n                                                staticStyle: {\n                                                  \"margin-left\": \"8px\"\n                                                },\n                                                attrs: {\n                                                  color: \"green\",\n                                                  size: \"small\"\n                                                }\n                                              },\n                                              [\n                                                _vm._v(\n                                                  \"\\n                      已保存\\n                    \"\n                                                )\n                                              ]\n                                            )\n                                          : _vm._e(),\n                                        _c(\n                                          \"a-button\",\n                                          {\n                                            staticClass: \"remove-btn\",\n                                            attrs: {\n                                              type: \"link\",\n                                              size: \"small\",\n                                              title: _vm.workflowFileInfo\n                                                .isSaved\n                                                ? \"重新选择文件\"\n                                                : \"删除文件\"\n                                            },\n                                            on: {\n                                              click:\n                                                _vm.handleRemoveWorkflowFile\n                                            }\n                                          },\n                                          [\n                                            _c(\"a-icon\", {\n                                              attrs: {\n                                                type: _vm.workflowFileInfo\n                                                  .isSaved\n                                                  ? \"edit\"\n                                                  : \"delete\"\n                                              }\n                                            }),\n                                            _vm._v(\n                                              \"\\n                      \" +\n                                                _vm._s(\n                                                  _vm.workflowFileInfo.isSaved\n                                                    ? \"重选\"\n                                                    : \"删除\"\n                                                ) +\n                                                \"\\n                    \"\n                                            )\n                                          ],\n                                          1\n                                        )\n                                      ],\n                                      1\n                                    )\n                                  ]\n                                )\n                              : _c(\n                                  \"div\",\n                                  { staticClass: \"upload-area\" },\n                                  [\n                                    _c(\n                                      \"a-upload\",\n                                      {\n                                        ref: \"workflowUpload\",\n                                        attrs: {\n                                          name: \"file\",\n                                          multiple: false,\n                                          \"before-upload\":\n                                            _vm.beforeWorkflowUpload,\n                                          \"show-upload-list\": false,\n                                          accept: \".zip\",\n                                          customRequest: function() {}\n                                        },\n                                        on: {\n                                          change: _vm.handleWorkflowFileSelect\n                                        }\n                                      },\n                                      [\n                                        _c(\n                                          \"a-button\",\n                                          {\n                                            attrs: {\n                                              loading: _vm.workflowUploading\n                                            }\n                                          },\n                                          [\n                                            _c(\"a-icon\", {\n                                              attrs: { type: \"upload\" }\n                                            }),\n                                            _vm._v(\n                                              \"\\n                      选择工作流压缩包\\n                    \"\n                                            )\n                                          ],\n                                          1\n                                        )\n                                      ],\n                                      1\n                                    )\n                                  ],\n                                  1\n                                )\n                          ]),\n                          _c(\n                            \"div\",\n                            { staticClass: \"upload-tip\" },\n                            [\n                              _c(\"a-icon\", {\n                                staticStyle: { color: \"#faad14\" },\n                                attrs: { type: \"exclamation-circle\" }\n                              }),\n                              _c(\"strong\", [_vm._v(\"温馨提示：\")]),\n                              _vm._v(\n                                \"只支持 .zip 格式，文件大小不超过 5MB\\n              \"\n                              )\n                            ],\n                            1\n                          )\n                        ]\n                      )\n                    ],\n                    1\n                  ),\n                  _c(\n                    \"div\",\n                    {\n                      directives: [\n                        {\n                          name: \"show\",\n                          rawName: \"v-show\",\n                          value: _vm.currentStep === 1,\n                          expression: \"currentStep === 1\"\n                        }\n                      ],\n                      staticClass: \"add-next-workflow\"\n                    },\n                    [\n                      _c(\n                        \"a-button\",\n                        {\n                          staticClass: \"add-next-btn\",\n                          attrs: { type: \"dashed\", block: \"\" },\n                          on: { click: _vm.addNextWorkflow }\n                        },\n                        [\n                          _c(\"a-icon\", { attrs: { type: \"plus\" } }),\n                          _vm._v(\n                            \"\\n              新增下一个工作流\\n            \"\n                          )\n                        ],\n                        1\n                      )\n                    ],\n                    1\n                  )\n                ],\n                1\n              ),\n              _vm.tempWorkflowList.length > 0\n                ? _c(\n                    \"div\",\n                    { staticClass: \"temp-workflows\" },\n                    [\n                      _c(\"a-divider\", [\n                        _vm._v(\n                          \"工作流列表 (\" +\n                            _vm._s(_vm.tempWorkflowList.length) +\n                            \")\"\n                        )\n                      ]),\n                      _c(\n                        \"div\",\n                        { staticClass: \"workflow-list\" },\n                        _vm._l(_vm.tempWorkflowList, function(workflow, index) {\n                          return _c(\n                            \"div\",\n                            {\n                              key: workflow.id,\n                              staticClass: \"workflow-item\",\n                              class: {\n                                editing: _vm.currentWorkflowIndex === index,\n                                \"has-error\":\n                                  _vm.workflowValidationErrors[workflow.id] &&\n                                  !_vm.workflowValidationErrors[workflow.id]\n                                    .isValid\n                              }\n                            },\n                            [\n                              _c(\"div\", { staticClass: \"workflow-info\" }, [\n                                _c(\n                                  \"h4\",\n                                  { staticClass: \"workflow-name\" },\n                                  [\n                                    _vm._v(\n                                      \"\\n                  \" +\n                                        _vm._s(workflow.workflowName) +\n                                        \"\\n                  \"\n                                    ),\n                                    workflow.status === \"saved\"\n                                      ? _c(\n                                          \"a-tag\",\n                                          {\n                                            attrs: {\n                                              color: \"green\",\n                                              size: \"small\"\n                                            }\n                                          },\n                                          [_vm._v(\"已保存\")]\n                                        )\n                                      : workflow.status === \"draft\"\n                                      ? _c(\n                                          \"a-tag\",\n                                          {\n                                            attrs: {\n                                              color: \"orange\",\n                                              size: \"small\"\n                                            }\n                                          },\n                                          [_vm._v(\"新增\")]\n                                        )\n                                      : _vm._e(),\n                                    _vm.currentWorkflowIndex === index\n                                      ? _c(\n                                          \"a-tag\",\n                                          {\n                                            attrs: {\n                                              color: \"blue\",\n                                              size: \"small\"\n                                            }\n                                          },\n                                          [_vm._v(\"编辑中\")]\n                                        )\n                                      : _vm._e(),\n                                    _vm.workflowValidationErrors[workflow.id] &&\n                                    !_vm.workflowValidationErrors[workflow.id]\n                                      .isValid\n                                      ? _c(\n                                          \"a-tag\",\n                                          {\n                                            attrs: {\n                                              color: \"red\",\n                                              size: \"small\"\n                                            }\n                                          },\n                                          [\n                                            _c(\"a-icon\", {\n                                              attrs: {\n                                                type: \"exclamation-circle\"\n                                              }\n                                            }),\n                                            _vm._v(\n                                              \"\\n                    有错误\\n                  \"\n                                            )\n                                          ],\n                                          1\n                                        )\n                                      : _vm._e()\n                                  ],\n                                  1\n                                ),\n                                _c(\"p\", { staticClass: \"workflow-desc\" }, [\n                                  _vm._v(\n                                    _vm._s(\n                                      workflow.workflowDescription || \"暂无描述\"\n                                    )\n                                  )\n                                ]),\n                                _vm.workflowValidationErrors[workflow.id] &&\n                                !_vm.workflowValidationErrors[workflow.id]\n                                  .isValid\n                                  ? _c(\n                                      \"div\",\n                                      { staticClass: \"workflow-errors\" },\n                                      [\n                                        _c(\"a-alert\", {\n                                          attrs: {\n                                            type: \"error\",\n                                            size: \"small\",\n                                            \"show-icon\": \"\",\n                                            message:\n                                              \"请补充：\" +\n                                              _vm.workflowValidationErrors[\n                                                workflow.id\n                                              ].errors.join(\"、\")\n                                          }\n                                        })\n                                      ],\n                                      1\n                                    )\n                                  : _vm._e()\n                              ]),\n                              _c(\n                                \"div\",\n                                { staticClass: \"workflow-actions\" },\n                                [\n                                  _c(\n                                    \"a-button\",\n                                    {\n                                      attrs: {\n                                        size: \"small\",\n                                        disabled:\n                                          _vm.currentWorkflowIndex === index\n                                      },\n                                      on: {\n                                        click: function($event) {\n                                          return _vm.loadWorkflowFromTemp(index)\n                                        }\n                                      }\n                                    },\n                                    [\n                                      _c(\"a-icon\", { attrs: { type: \"edit\" } }),\n                                      _vm._v(\n                                        \"\\n                  编辑\\n                \"\n                                      )\n                                    ],\n                                    1\n                                  ),\n                                  _c(\n                                    \"a-button\",\n                                    {\n                                      attrs: { size: \"small\", type: \"danger\" },\n                                      on: {\n                                        click: function($event) {\n                                          return _vm.deleteWorkflowFromTemp(\n                                            index\n                                          )\n                                        }\n                                      }\n                                    },\n                                    [\n                                      _c(\"a-icon\", {\n                                        attrs: { type: \"delete\" }\n                                      }),\n                                      _vm._v(\n                                        \"\\n                  删除\\n                \"\n                                      )\n                                    ],\n                                    1\n                                  )\n                                ],\n                                1\n                              )\n                            ]\n                          )\n                        }),\n                        0\n                      )\n                    ],\n                    1\n                  )\n                : _vm._e()\n            ]),\n            _c(\n              \"div\",\n              { staticClass: \"step-footer\" },\n              [\n                _c(\n                  \"a-button\",\n                  {\n                    attrs: { disabled: _vm.stepLoading },\n                    on: { click: _vm.handlePrev }\n                  },\n                  [\n                    _c(\"a-icon\", { attrs: { type: \"arrow-left\" } }),\n                    _vm._v(\"\\n          上一步\\n        \")\n                  ],\n                  1\n                ),\n                _c(\n                  \"a-button\",\n                  {\n                    staticClass: \"complete-btn\",\n                    attrs: { type: \"primary\", loading: _vm.stepLoading },\n                    on: { click: _vm.handleComplete }\n                  },\n                  [\n                    _vm._v(\"\\n          完成创建\\n          \"),\n                    _c(\"a-icon\", { attrs: { type: \"check\" } })\n                  ],\n                  1\n                )\n              ],\n              1\n            )\n          ]\n        ),\n        _vm.currentStep === 2\n          ? _c(\"div\", { staticClass: \"step-content\" }, [\n              _c(\"div\", { staticClass: \"step-header\" }, [\n                _c(\n                  \"h3\",\n                  [\n                    _c(\"a-icon\", {\n                      staticStyle: { color: \"#52c41a\", \"margin-right\": \"8px\" },\n                      attrs: { type: \"check-circle\" }\n                    }),\n                    _vm._v(\"\\n          创建完成\\n        \")\n                  ],\n                  1\n                ),\n                _c(\"p\", [_vm._v(\"智能体创建流程已完成\")])\n              ]),\n              _c(\"div\", { staticClass: \"success-content\" }, [\n                _c(\n                  \"div\",\n                  { staticClass: \"success-icon\" },\n                  [\n                    _c(\"a-icon\", {\n                      staticStyle: { \"font-size\": \"64px\", color: \"#52c41a\" },\n                      attrs: { type: \"check-circle\" }\n                    })\n                  ],\n                  1\n                ),\n                _c(\"div\", { staticClass: \"success-message\" }, [\n                  _c(\"h2\", [\n                    _vm._v(\"恭喜您，已成功提交智能体，请耐心等待审核！\")\n                  ]),\n                  _c(\"p\", [\n                    _vm._v(\n                      \"您的智能体信息已提交至平台，我们将在1-3个工作日内完成审核。\"\n                    )\n                  ]),\n                  _c(\"p\", [\n                    _vm._v(\"审核结果将通过站内消息通知您，请注意查收。\")\n                  ])\n                ])\n              ]),\n              _c(\n                \"div\",\n                { staticClass: \"step-footer\" },\n                [\n                  _c(\n                    \"a-button\",\n                    {\n                      attrs: { type: \"primary\", size: \"large\" },\n                      on: { click: _vm.handleCloseModal }\n                    },\n                    [_vm._v(\"\\n          关闭\\n        \")]\n                  )\n                ],\n                1\n              )\n            ])\n          : _vm._e()\n      ])\n    ]\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"]}