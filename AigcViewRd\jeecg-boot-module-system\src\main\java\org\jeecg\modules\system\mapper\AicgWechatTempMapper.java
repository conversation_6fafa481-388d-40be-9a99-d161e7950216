package org.jeecg.modules.system.mapper;

import org.jeecg.modules.system.entity.AicgWechatTemp;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

/**
 * @Description: 微信登录临时数据表
 * @Author: jeecg-boot
 * @Date: 2025-06-19
 * @Version: V1.0
 */
@Mapper
public interface AicgWechatTempMapper extends BaseMapper<AicgWechatTemp> {

    /**
     * 根据场景ID查询微信临时数据
     * @param sceneId 场景ID
     * @return 微信临时数据
     */
    @Select("SELECT * FROM aicg_wechat_temp WHERE scene_id = #{sceneId} AND expire_time > NOW()")
    AicgWechatTemp getBySceneId(@Param("sceneId") String sceneId);

    /**
     * 更新微信用户信息
     * @param sceneId 场景ID
     * @param openid 微信OpenID
     * @param nickname 微信昵称
     * @param avatar 微信头像
     * @param status 状态
     * @return 更新行数
     */
    @Update("UPDATE aicg_wechat_temp SET openid = #{openid}, nickname = #{nickname}, " +
            "avatar = #{avatar}, status = #{status}, update_time = NOW() " +
            "WHERE scene_id = #{sceneId}")
    int updateWechatInfo(@Param("sceneId") String sceneId, 
                        @Param("openid") String openid, 
                        @Param("nickname") String nickname, 
                        @Param("avatar") String avatar, 
                        @Param("status") Integer status);

    /**
     * 更新状态
     * @param sceneId 场景ID
     * @param status 状态
     * @return 更新行数
     */
    @Update("UPDATE aicg_wechat_temp SET status = #{status}, update_time = NOW() WHERE scene_id = #{sceneId}")
    int updateStatus(@Param("sceneId") String sceneId, @Param("status") Integer status);

    /**
     * 清理过期数据
     * @return 清理的记录数
     */
    @Update("DELETE FROM aicg_wechat_temp WHERE expire_time < NOW()")
    int cleanExpiredData();
}
