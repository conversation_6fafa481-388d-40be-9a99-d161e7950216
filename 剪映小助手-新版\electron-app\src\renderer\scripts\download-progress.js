// 下载进度管理器
class DownloadProgressManager {
    constructor() {
        this.isVisible = false
        this.currentDownload = null
        this.batchInfo = null
        this.elements = {}
        this.lastUpdateTime = 0
        this.updateThrottle = 200 // 限制更新频率为200ms
    }

    // 初始化
    init() {
        console.log('🚀 [INIT] 开始初始化下载进度管理器')
        console.log('🚀 [INIT] DOM状态:', document.readyState)
        console.log('🚀 [INIT] 页面加载完成:', document.readyState === 'complete')

        // 延迟绑定，确保DOM完全加载
        setTimeout(() => {
            console.log('🚀 [INIT] 延迟初始化开始')
            this.bindElements()
            this.hideProgress()
            console.log('✅ [INIT] 下载进度管理器初始化完成')

            // 测试功能已移除，按用户要求

            // 确保全局可访问
            window.DownloadProgressManager = this
            console.log('✅ [INIT] 全局对象设置完成')
        }, 100)
    }



    // 绑定DOM元素
    bindElements() {
        this.elements = {
            // 进度区域
            progressSection: document.getElementById('download-progress-section'),

            // 文件信息
            fileIcon: document.getElementById('file-icon'),
            fileName: document.getElementById('current-file-name'),
            fileType: document.getElementById('current-file-type'),
            downloadedSize: document.getElementById('downloaded-size'),
            totalSize: document.getElementById('total-size'),

            // 进度条
            progressFill: document.getElementById('progress-fill'),
            progressText: document.getElementById('progress-text'),

            // 统计信息
            downloadSpeed: document.getElementById('download-speed'),
            eta: document.getElementById('eta'),
            elapsedTime: document.getElementById('elapsed-time'),

            // 批量进度
            batchProgress: document.getElementById('batch-progress'),
            batchCurrent: document.getElementById('batch-current'),
            batchTotal: document.getElementById('batch-total'),
            batchProgressFill: document.getElementById('batch-progress-fill'),
            batchProgressText: document.getElementById('batch-progress-text')
        }

        // 检查关键元素是否存在
        console.log('🔍 [DOM] DOM元素绑定检查:')
        console.log('🔍 [DOM] 进度区域:', this.elements.progressSection ? '✅ 找到' : '❌ 未找到')
        console.log('🔍 [DOM] 文件名:', this.elements.fileName ? '✅ 找到' : '❌ 未找到')
        console.log('🔍 [DOM] 进度条填充:', this.elements.progressFill ? '✅ 找到' : '❌ 未找到')
        console.log('🔍 [DOM] 进度文本:', this.elements.progressText ? '✅ 找到' : '❌ 未找到')
        console.log('🔍 [DOM] 下载速度:', this.elements.downloadSpeed ? '✅ 找到' : '❌ 未找到')

        // 详细检查每个元素
        Object.keys(this.elements).forEach(key => {
            const element = this.elements[key]
            console.log(`🔍 [DOM] ${key}:`, element ? '✅' : '❌', element)
        })

        if (!this.elements.progressSection) {
            console.error('❌ [DOM] 找不到进度显示区域，请检查HTML结构')
            // 尝试直接查找
            const section = document.querySelector('#download-progress-section')
            console.log('❌ [DOM] 直接查找结果:', section)
        }
    }

    // 显示进度区域
    showProgress() {
        console.log('🎯 [SHOW] 尝试显示进度区域...')
        console.log('🎯 [SHOW] 当前可见状态:', this.isVisible)
        console.log('🎯 [SHOW] 调用堆栈:', new Error().stack)

        // 重新绑定元素，确保能找到
        console.log('🎯 [SHOW] 重新绑定DOM元素')
        this.bindElements()

        if (this.elements.progressSection) {
            console.log('🎯 [SHOW] 找到进度区域元素，设置显示样式')
            console.log('🎯 [SHOW] 元素当前样式:', {
                display: this.elements.progressSection.style.display,
                visibility: this.elements.progressSection.style.visibility,
                opacity: this.elements.progressSection.style.opacity
            })

            this.elements.progressSection.style.display = 'block'
            this.elements.progressSection.style.visibility = 'visible'
            this.elements.progressSection.style.opacity = '1'
            this.isVisible = true

            console.log('✅ [SHOW] 显示下载进度区域成功')
            console.log('✅ [SHOW] 元素更新后样式:', {
                display: this.elements.progressSection.style.display,
                visibility: this.elements.progressSection.style.visibility,
                opacity: this.elements.progressSection.style.opacity
            })

            // 强制重绘
            this.elements.progressSection.offsetHeight
        } else {
            console.error('❌ [SHOW] 找不到进度显示区域元素')

            // 尝试直接查找
            const section = document.querySelector('#download-progress-section')
            console.log('❌ [SHOW] 直接查找结果:', section)
            if (section) {
                console.log('🔧 [SHOW] 通过querySelector找到元素，重新绑定')
                this.elements.progressSection = section
                section.style.display = 'block'
                section.style.visibility = 'visible'
                section.style.opacity = '1'
                this.isVisible = true
                console.log('✅ [SHOW] 备用方案成功')
            } else {
                console.error('❌ [SHOW] 完全找不到进度显示区域')
                console.error('❌ [SHOW] 页面中所有ID元素:', Array.from(document.querySelectorAll('[id]')).map(el => el.id))
            }
        }
    }

    // 隐藏进度区域
    hideProgress() {
        console.log('🔧 [HIDE] 隐藏进度区域请求，当前批量状态:', this.batchInfo)
        console.log('🔧 [HIDE] 调用堆栈:', new Error().stack)

        // 🔧 [FIX] 添加批量处理保护：如果正在进行批量下载且不是最后一个文件，拒绝隐藏
        if (this.batchInfo && this.batchInfo.totalFiles > 1 && !this.batchInfo.isCompleted) {
            const currentIndex = this.batchInfo.currentIndex || 1
            const totalFiles = this.batchInfo.totalFiles
            const isLastFile = currentIndex >= totalFiles

            if (!isLastFile) {
                console.log(`🛡️ [PROTECT] 拒绝隐藏：批量下载进行中 (${currentIndex}/${totalFiles})`)
                return // 拒绝隐藏
            }
        }

        if (this.elements.progressSection) {
            console.log('🔧 [HIDE] 设置进度区域为隐藏')
            this.elements.progressSection.style.display = 'none'
            this.isVisible = false
        }
        this.currentDownload = null

        // 🆕 只有在批量下载完成或不是批量下载时才清理batchInfo
        if (!this.batchInfo || this.batchInfo.isCompleted) {
            console.log('🔧 [HIDE] 清理批量状态')
            this.batchInfo = null
        } else {
            console.log('🔧 [HIDE] 保留批量状态，批量下载进行中')
        }
    }

    // 更新下载进度（带防抖优化）
    updateProgress(progressData) {
        try {
            console.log('📊 [UPDATE] 收到进度更新请求:', progressData)

            if (!progressData || typeof progressData !== 'object') {
                console.warn('❌ [UPDATE] 无效的进度数据:', progressData)
                return
            }

            // 🆕 检查并自动初始化批量状态
            console.log('🔍 [BATCH] 检查批量状态初始化条件:', {
                hasCurrentFileIndex: progressData.currentFileIndex !== undefined,
                hasTotalFiles: progressData.totalFiles !== undefined,
                totalFiles: progressData.totalFiles,
                isMultiFile: progressData.totalFiles > 1,
                hasBatchInfo: !!this.batchInfo
            })

            if (progressData.currentFileIndex !== undefined && progressData.totalFiles !== undefined && progressData.totalFiles > 1) {
                if (!this.batchInfo) {
                    console.log('🚀 [BATCH] 从进度数据中检测到批量下载，自动初始化批量状态')
                    this.batchInfo = {
                        totalFiles: progressData.totalFiles,
                        fileList: Array.from({length: progressData.totalFiles}, (_, i) => `文件${i + 1}`),
                        currentIndex: 0,
                        isCompleted: false
                    }
                    this.showBatchProgress()
                    console.log('🚀 [BATCH] 批量状态自动初始化完成:', this.batchInfo)
                } else {
                    console.log('🔍 [BATCH] 批量状态已存在，跳过初始化')
                }
            } else {
                console.log('🔍 [BATCH] 不满足批量初始化条件，当前为单文件下载')
            }

            // 防抖：限制更新频率，避免过于频繁的DOM操作
            const now = Date.now()
            const shouldUpdate = progressData.status === 'completed' ||
                                progressData.status === 'error' ||
                                (now - this.lastUpdateTime) >= this.updateThrottle

            if (!shouldUpdate) {
                console.log('⏸️ [UPDATE] 防抖跳过更新，缓存数据')
                // 只更新缓存的数据，不更新UI
                this.currentDownload = progressData
                return
            }

            this.lastUpdateTime = now
            console.log('📊 [UPDATE] 执行进度更新:', progressData)
            console.log('📊 [UPDATE] 当前可见状态:', this.isVisible)

            if (!this.isVisible) {
                console.log('📊 [UPDATE] 进度条未显示，调用showProgress')
                this.showProgress()
            }

            this.currentDownload = progressData
            console.log('📊 [UPDATE] 开始更新UI组件')
            this.updateFileInfo(progressData)
            this.updateProgressBar(progressData)
            this.updateStats(progressData)

            // 如果有批量信息，更新批量进度
            if (progressData.currentFileIndex !== undefined && progressData.totalFiles !== undefined) {
                console.log('📊 [BATCH] 检测到批量信息，更新批量进度')
                this.updateBatchProgress(progressData)
            } else {
                console.log('📊 [BATCH] 无批量信息:', {
                    currentFileIndex: progressData.currentFileIndex,
                    totalFiles: progressData.totalFiles
                })
            }

            // 处理不同状态
            if (progressData.status === 'completed') {
                this.showCompletionMessage(progressData.fileName)

                // 🔧 [FIX] 改进的隐藏逻辑：防止批量处理中的闪烁
                setTimeout(() => {
                    // 🆕 防重复隐藏：如果已经隐藏了就不再处理
                    if (!this.isVisible) {
                        console.log('🔍 [HIDE] 进度条已隐藏，跳过处理')
                        return
                    }

                    console.log('🔧 [FIX] 开始检查是否需要隐藏进度条')
                    console.log('🔧 [FIX] 批量信息:', this.batchInfo)
                    console.log('🔧 [FIX] 进度数据:', {
                        currentFileIndex: progressData.currentFileIndex,
                        totalFiles: progressData.totalFiles,
                        fileName: progressData.fileName,
                        status: progressData.status
                    })

                    // 🔧 [FIX] 更保守的批量检测：同时检查多个条件
                    const isBatchDownload = this.batchInfo && this.batchInfo.totalFiles > 1
                    const hasProgressBatchInfo = progressData.totalFiles && progressData.totalFiles > 1
                    const isMultiFile = isBatchDownload || hasProgressBatchInfo

                    if (isMultiFile) {
                        // 🔧 [FIX] 批量下载：使用更可靠的判断逻辑
                        // 优先使用进度数据中的索引，因为它更及时
                        const currentIndexFromProgress = progressData.currentFileIndex
                        const currentIndexFromBatch = this.batchInfo ? this.batchInfo.currentIndex : null
                        const totalFiles = progressData.totalFiles || (this.batchInfo ? this.batchInfo.totalFiles : 1)

                        // 🔧 [FIX] 选择最可靠的当前索引
                        let currentIndex = currentIndexFromProgress || currentIndexFromBatch || 1

                        // 🔧 [FIX] 更严格的最后文件判断：必须确保是真正的最后一个
                        const isLastFile = currentIndex >= totalFiles && totalFiles > 1

                        console.log(`🔧 [FIX] 批量下载状态检查:`)
                        console.log(`  - 进度数据索引: ${currentIndexFromProgress}`)
                        console.log(`  - 批量状态索引: ${currentIndexFromBatch}`)
                        console.log(`  - 选择的索引: ${currentIndex}`)
                        console.log(`  - 总文件数: ${totalFiles}`)
                        console.log(`  - 是否最后文件: ${isLastFile}`)

                        if (isLastFile) {
                            console.log(`🎯 [HIDE] 确认为最后一个文件 (${currentIndex}/${totalFiles})，隐藏进度条`)
                            this.hideProgress()
                        } else {
                            console.log(`🎯 [KEEP] 批量下载进行中 (${currentIndex}/${totalFiles})，保持进度条显示`)
                        }
                    } else {
                        // 单文件下载：直接隐藏
                        console.log('🎯 [HIDE] 单文件下载完成，隐藏进度条')
                        console.log('🔍 [HIDE] 进度数据:', {
                            currentFileIndex: progressData.currentFileIndex,
                            totalFiles: progressData.totalFiles,
                            fileName: progressData.fileName
                        })
                        this.hideProgress()
                    }
                }, 2000)
            } else if (progressData.status === 'error') {
                this.showErrorMessage(progressData.fileName, progressData.error)
            } else if (progressData.status === 'starting') {
                console.log('📊 开始下载:', progressData.fileName)
                // 重置进度显示
                this.resetProgress()
            }
        } catch (error) {
            console.error('更新下载进度时出错:', error)
            // 不影响其他功能，继续执行
        }
    }

    // 显示完成消息
    showCompletionMessage(fileName) {
        if (this.elements.fileName) {
            const originalText = this.elements.fileName.textContent
            this.elements.fileName.textContent = `✅ ${fileName} 下载完成`
            this.elements.fileName.style.color = '#10b981'

            setTimeout(() => {
                this.elements.fileName.textContent = originalText
                this.elements.fileName.style.color = ''
            }, 1500)
        }
    }

    // 显示错误消息
    showErrorMessage(fileName, error) {
        if (this.elements.fileName) {
            this.elements.fileName.textContent = `❌ ${fileName} 下载失败`
            this.elements.fileName.style.color = '#ef4444'

            if (this.elements.progressText) {
                this.elements.progressText.textContent = '失败'
            }
        }
    }

    // 重置进度显示
    resetProgress() {
        if (this.elements.progressFill) {
            this.elements.progressFill.style.width = '0%'
        }
        if (this.elements.progressText) {
            this.elements.progressText.textContent = '0%'
        }
        if (this.elements.fileName) {
            this.elements.fileName.textContent = '准备下载...'
            this.elements.fileName.style.color = ''
        }
        if (this.elements.downloadSpeed) {
            this.elements.downloadSpeed.textContent = '0 B/s'
        }
        if (this.elements.eta) {
            this.elements.eta.textContent = '--'
        }
        if (this.elements.elapsedTime) {
            this.elements.elapsedTime.textContent = '0:00'
        }
    }

    // 更新文件信息
    updateFileInfo(data) {
        if (this.elements.fileName) {
            this.elements.fileName.textContent = data.fileName || '未知文件'
        }

        if (this.elements.fileType && data.fileType) {
            this.elements.fileType.textContent = data.fileType
            this.elements.fileType.style.display = 'inline-block'
        }

        if (this.elements.fileIcon) {
            this.elements.fileIcon.textContent = this.getFileIcon(data.fileType || data.fileName)
        }

        if (this.elements.downloadedSize) {
            this.elements.downloadedSize.textContent = this.formatFileSize(data.downloadedBytes || 0)
        }

        if (this.elements.totalSize) {
            const totalText = data.totalBytes > 0 ? this.formatFileSize(data.totalBytes) : '未知大小'
            this.elements.totalSize.textContent = totalText
        }
    }

    // 更新进度条
    updateProgressBar(data) {
        const progress = data.progress || 0

        if (this.elements.progressFill) {
            this.elements.progressFill.style.width = `${progress}%`
        }

        if (this.elements.progressText) {
            this.elements.progressText.textContent = `${progress}%`
        }
    }

    // 更新统计信息
    updateStats(data) {
        if (this.elements.downloadSpeed) {
            const speed = data.downloadSpeed || data.averageSpeed || 0
            this.elements.downloadSpeed.textContent = this.formatSpeed(speed)
        }

        if (this.elements.eta) {
            const eta = data.estimatedTimeRemaining || 0
            this.elements.eta.textContent = this.formatTime(eta)
        }

        if (this.elements.elapsedTime) {
            const elapsed = data.elapsedTime || 0
            this.elements.elapsedTime.textContent = this.formatTime(elapsed)
        }
    }

    // 开始批量下载
    startBatch(batchData) {
        console.log('🚀 [BATCH] 开始批量下载:', batchData)

        this.batchInfo = {
            totalFiles: batchData.totalFiles,
            fileList: batchData.fileList,
            currentIndex: 0,
            isCompleted: false
        }

        // 显示进度区域
        this.showProgress()
        this.showBatchProgress()
        this.updateBatchInfo()

        console.log(`✅ [BATCH] 批量下载UI已初始化，总文件数: ${batchData.totalFiles}`)
    }

    // 更新批量进度
    updateBatchProgress(data) {
        if (!this.batchInfo) return

        console.log('📊 [BATCH] 更新批量进度:', data)

        // 🔧 [FIX] 更可靠的索引更新：确保索引正确传递
        const newIndex = data.currentFileIndex || this.batchInfo.currentIndex || 1

        // 🔧 [FIX] 防止索引回退（可能导致错误的隐藏判断）
        if (newIndex >= this.batchInfo.currentIndex) {
            this.batchInfo.currentIndex = newIndex
            console.log(`📊 [BATCH] 索引更新: ${this.batchInfo.currentIndex}/${this.batchInfo.totalFiles}`)
        } else {
            console.log(`⚠️ [BATCH] 索引回退被阻止: 新索引${newIndex} < 当前索引${this.batchInfo.currentIndex}`)
        }

        if (this.elements.batchCurrent) {
            this.elements.batchCurrent.textContent = this.batchInfo.currentIndex
        }

        if (this.elements.batchTotal) {
            this.elements.batchTotal.textContent = this.batchInfo.totalFiles
        }

        // 优先使用后端计算的批量进度
        let batchProgress
        if (data.batchProgress !== undefined && data.batchProgress !== null) {
            batchProgress = data.batchProgress
            console.log('📊 [BATCH] 使用后端计算的进度:', batchProgress)
        } else {
            // 备用计算方式
            batchProgress = ((this.batchInfo.currentIndex - 1) * 100 + (data.progress || 0)) / this.batchInfo.totalFiles
            console.log('📊 [BATCH] 使用前端计算的进度:', batchProgress)
        }

        // 确保进度不超过100%
        batchProgress = Math.min(100, Math.max(0, batchProgress))

        if (this.elements.batchProgressFill) {
            this.elements.batchProgressFill.style.width = `${batchProgress}%`
        }

        if (this.elements.batchProgressText) {
            this.elements.batchProgressText.textContent = `${Math.round(batchProgress)}%`
        }

        console.log('📊 [BATCH] 批量进度更新完成:', {
            currentIndex: this.batchInfo.currentIndex,
            totalFiles: this.batchInfo.totalFiles,
            batchProgress: Math.round(batchProgress)
        })
    }

    // 完成批量下载
    completeBatch(batchData) {
        console.log('✅ [BATCH] 批量下载完成:', batchData)

        if (this.batchInfo) {
            this.batchInfo.isCompleted = true

            // 确保批量进度显示100%
            if (this.elements.batchProgressFill) {
                this.elements.batchProgressFill.style.width = '100%'
            }
            if (this.elements.batchProgressText) {
                this.elements.batchProgressText.textContent = '100%'
            }

            console.log('✅ [BATCH] 批量进度已设置为100%')
        }

        // 🆕 改进：不在这里隐藏进度条，让单文件完成逻辑来处理
        // 只需要延迟清理批量进度显示
        setTimeout(() => {
            console.log('🎯 [BATCH] 3秒后清理批量进度显示')
            this.hideBatchProgress()
            this.resetBatch() // 清理批量状态
        }, 3000)
    }

    // 显示批量进度
    showBatchProgress() {
        if (this.elements.batchProgress) {
            this.elements.batchProgress.style.display = 'block'
        }
    }

    // 隐藏批量进度
    hideBatchProgress() {
        if (this.elements.batchProgress) {
            this.elements.batchProgress.style.display = 'none'
        }
    }

    // 🆕 重置批量下载状态
    resetBatch() {
        console.log('🔄 [BATCH] 重置批量下载状态')

        // 清理批量信息
        this.batchInfo = null

        // 重置当前下载信息
        this.currentDownload = null

        // 清理任何可能存在的定时器（虽然这里没有直接的定时器引用，但确保状态干净）
        console.log('✅ [BATCH] 批量状态已重置')
    }

    // 🔧 [FIX] 强制保持进度条显示（用于批量处理中间状态）
    forceKeepVisible() {
        console.log('🛡️ [FORCE] 强制保持进度条显示')
        if (!this.isVisible) {
            this.showProgress()
        }

        // 确保批量状态不被意外清理
        if (this.batchInfo && !this.batchInfo.isCompleted) {
            console.log('🛡️ [FORCE] 保护批量状态不被清理')
        }
    }

    // 更新批量信息
    updateBatchInfo() {
        if (!this.batchInfo) return

        if (this.elements.batchTotal) {
            this.elements.batchTotal.textContent = this.batchInfo.totalFiles
        }

        if (this.elements.batchCurrent) {
            this.elements.batchCurrent.textContent = this.batchInfo.currentIndex || 0
        }
    }

    // 获取文件图标
    getFileIcon(fileName) {
        if (!fileName) return '📄'
        
        const ext = fileName.toLowerCase().split('.').pop()
        
        switch (ext) {
            case 'mp4':
            case 'avi':
            case 'mov':
            case 'mkv':
                return '🎬'
            case 'mp3':
            case 'wav':
            case 'aac':
            case 'flac':
                return '🎵'
            case 'jpg':
            case 'jpeg':
            case 'png':
            case 'gif':
            case 'bmp':
                return '🖼️'
            case 'zip':
            case 'rar':
            case '7z':
                return '📦'
            default:
                return '📄'
        }
    }

    // 格式化文件大小
    formatFileSize(bytes) {
        if (bytes === 0) return '0 B'
        const k = 1024
        const sizes = ['B', 'KB', 'MB', 'GB']
        const i = Math.floor(Math.log(bytes) / Math.log(k))
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    }

    // 格式化速度
    formatSpeed(bytesPerSecond) {
        return this.formatFileSize(bytesPerSecond) + '/s'
    }

    // 格式化时间
    formatTime(seconds) {
        if (!seconds || seconds === Infinity) return '--'
        const mins = Math.floor(seconds / 60)
        const secs = Math.floor(seconds % 60)
        return `${mins}:${secs.toString().padStart(2, '0')}`
    }
}

// 创建全局实例
console.log('🔧 [SCRIPT] 下载进度脚本加载完成，创建全局实例')
window.DownloadProgressManager = new DownloadProgressManager()
console.log('✅ [SCRIPT] DownloadProgressManager全局实例创建完成:', !!window.DownloadProgressManager)

// 🧪 [DEBUG] 添加调试方法
window.debugBatchProgress = function() {
    const manager = window.DownloadProgressManager
    console.log('🧪 [DEBUG] 批量进度状态调试:')
    console.log('  - isVisible:', manager.isVisible)
    console.log('  - batchInfo:', manager.batchInfo)
    console.log('  - currentDownload:', manager.currentDownload)
    console.log('  - 进度区域元素:', manager.elements.progressSection)
}
