<template>
  <div class="simple-carousel" @mouseenter="showControls" @mouseleave="hideControls">
    <div class="carousel-wrapper">
      <!-- 幻灯片 -->
      <div
        v-for="(slide, index) in slides"
        :key="index"
        class="carousel-slide"
        :class="{ 'active': index === currentSlide }"
      >
        <img :src="slide.image" :alt="slide.title" class="slide-image" />
        <div class="slide-content">
          <h2>{{ slide.title }}</h2>
          <p>{{ slide.description }}</p>
        </div>
      </div>
    </div>

    <!-- 控制器 -->
    <div class="carousel-controls" v-show="controlsVisible">
      <button @click="prevSlide" class="control-btn">‹</button>
      
      <div class="indicators">
        <div
          v-for="(slide, index) in slides"
          :key="index"
          class="indicator"
          :class="{ 'active': index === currentSlide }"
          @click="goToSlide(index)"
        ></div>
      </div>
      
      <button @click="nextSlide" class="control-btn">›</button>
    </div>

    <!-- 调试信息 -->
    <div class="debug-info">
      <p>当前: {{ currentSlide + 1 }} / {{ slides.length }}</p>
      <p>自动播放: {{ isPlaying ? '开启' : '关闭' }}</p>
      <button @click="toggleAutoPlay">切换播放</button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'SimpleCarousel',
  props: {
    slides: {
      type: Array,
      required: true
    },
    autoPlay: {
      type: Boolean,
      default: true
    },
    interval: {
      type: Number,
      default: 3000
    }
  },
  data() {
    return {
      currentSlide: 0,
      isPlaying: this.autoPlay,
      autoPlayTimer: null,
      controlsVisible: false
    }
  },
  mounted() {
    console.log('SimpleCarousel mounted with', this.slides.length, 'slides')
    if (this.isPlaying) {
      this.startAutoPlay()
    }
  },
  beforeDestroy() {
    this.stopAutoPlay()
  },
  methods: {
    goToSlide(index) {
      if (index === this.currentSlide) return
      console.log('切换到幻灯片:', index)
      this.currentSlide = index
    },

    nextSlide() {
      const nextIndex = this.currentSlide === this.slides.length - 1 ? 0 : this.currentSlide + 1
      this.goToSlide(nextIndex)
    },

    prevSlide() {
      const prevIndex = this.currentSlide === 0 ? this.slides.length - 1 : this.currentSlide - 1
      this.goToSlide(prevIndex)
    },

    startAutoPlay() {
      this.stopAutoPlay()
      this.autoPlayTimer = setInterval(() => {
        this.nextSlide()
      }, this.interval)
      console.log('自动播放已开启')
    },

    stopAutoPlay() {
      if (this.autoPlayTimer) {
        clearInterval(this.autoPlayTimer)
        this.autoPlayTimer = null
        console.log('自动播放已停止')
      }
    },

    toggleAutoPlay() {
      this.isPlaying = !this.isPlaying
      if (this.isPlaying) {
        this.startAutoPlay()
      } else {
        this.stopAutoPlay()
      }
    },

    showControls() {
      this.controlsVisible = true
    },

    hideControls() {
      this.controlsVisible = false
    }
  }
}
</script>

<style scoped>
.simple-carousel {
  position: relative;
  width: 100%;
  height: 400px;
  overflow: hidden;
  border-radius: 8px;
  background: #f0f0f0;
}

.carousel-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
}

.carousel-slide {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  transition: opacity 0.5s ease;
}

.carousel-slide.active {
  opacity: 1;
}

.slide-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.slide-content {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0,0,0,0.8));
  color: white;
  padding: 20px;
}

.slide-content h2 {
  margin: 0 0 10px 0;
  font-size: 1.5rem;
}

.slide-content p {
  margin: 0;
  opacity: 0.9;
}

.carousel-controls {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  align-items: center;
  gap: 15px;
  background: rgba(0,0,0,0.6);
  padding: 10px 20px;
  border-radius: 25px;
  transition: opacity 0.3s ease;
}

.control-btn {
  width: 30px;
  height: 30px;
  background: rgba(255,255,255,0.2);
  border: 1px solid rgba(255,255,255,0.3);
  border-radius: 50%;
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  transition: all 0.3s ease;
}

.control-btn:hover {
  background: rgba(255,255,255,0.3);
  transform: scale(1.1);
}

.indicators {
  display: flex;
  gap: 8px;
}

.indicator {
  width: 24px;
  height: 4px;
  background: rgba(255,255,255,0.3);
  border-radius: 2px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.indicator.active {
  background: white;
}

.indicator:hover {
  background: rgba(255,255,255,0.6);
}

.debug-info {
  position: absolute;
  top: 10px;
  right: 10px;
  background: rgba(0,0,0,0.8);
  color: white;
  padding: 10px;
  border-radius: 5px;
  font-size: 0.8rem;
}

.debug-info p {
  margin: 0 0 5px 0;
}

.debug-info button {
  background: #3b82f6;
  color: white;
  border: none;
  padding: 5px 10px;
  border-radius: 3px;
  cursor: pointer;
  font-size: 0.7rem;
}
</style>
