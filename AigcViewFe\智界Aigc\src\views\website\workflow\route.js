/**
 * 工作流中心路由配置
 * 包含智能体市场和创作者中心的路由定义
 */

// 工作流中心路由配置
export const workflowCenterRoutes = [
  {
    path: '/workflow-center',
    name: 'WorkflowCenter',
    component: () => import('./WorkflowCenter.vue'),
    meta: {
      title: '工作流中心 - 智界AIGC',
      description: '智能体市场和创作者中心，发现和使用优质AI智能体',
      keepAlive: true,
      componentName: 'WorkflowCenter'
    }
  },
  // 创作者中心直接访问路由
  {
    path: '/creator-center',
    name: 'CreatorCenter',
    redirect: '/workflow-center?tab=creator',
    meta: {
      title: '创作者中心 - 智界AIGC',
      description: '管理您的智能体，查看收益统计，创建和发布AI智能体'
    }
  },
  // 智能体市场直接访问路由
  {
    path: '/agent-market',
    name: 'AgentMarket',
    redirect: '/workflow-center?tab=market',
    meta: {
      title: '智能体市场 - 智界AIGC',
      description: '发现和使用优质AI智能体，提升您的创作效率'
    }
  }
]

// 创作者中心权限守卫
export const creatorCenterGuard = {
  // 路由前置守卫
  beforeEnter: (to, from, next) => {
    // 检查是否访问创作者中心
    if (to.query.tab === 'creator' || to.name === 'CreatorCenter') {
      // 检查用户是否登录
      const token = localStorage.getItem('Access-Token')
      if (!token) {
        // 未登录，跳转到登录页面，并记录回调地址
        const redirectUrl = encodeURIComponent(to.fullPath)
        next(`/user/login?redirect=${redirectUrl}`)
        return
      }
      
      // 可以在这里添加更多权限检查
      // 例如检查用户是否有创作者权限等
    }
    
    next()
  }
}

// 面包屑导航配置
export const workflowBreadcrumbs = {
  '/workflow-center': [
    {
      breadcrumbName: '首页',
      path: '/home'
    },
    {
      breadcrumbName: '工作流中心',
      path: '/workflow-center'
    }
  ],
  '/workflow-center?tab=creator': [
    {
      breadcrumbName: '首页',
      path: '/home'
    },
    {
      breadcrumbName: '工作流中心',
      path: '/workflow-center'
    },
    {
      breadcrumbName: '创作者中心',
      path: '/workflow-center?tab=creator'
    }
  ],
  '/creator-center': [
    {
      breadcrumbName: '首页',
      path: '/home'
    },
    {
      breadcrumbName: '创作者中心',
      path: '/creator-center'
    }
  ]
}

// SEO元数据配置
export const workflowSEO = {
  market: {
    title: '智能体市场 - 智界AIGC',
    description: '发现和使用优质AI智能体，提升您的创作效率。包含文本生成、图像处理、数据分析等多种类型的AI智能体。',
    keywords: 'AI智能体,人工智能,智能体市场,AI工具,创作助手',
    ogTitle: '智能体市场 - 发现优质AI智能体',
    ogDescription: '在智界AIGC智能体市场中发现和使用各种AI智能体，提升您的工作效率和创作质量。',
    ogImage: '/images/agent-market-og.jpg'
  },
  creator: {
    title: '创作者中心 - 智界AIGC',
    description: '管理您的AI智能体，查看收益统计，创建和发布高质量的AI智能体。为创作者提供完整的智能体管理平台。',
    keywords: 'AI智能体创作,智能体管理,AI创作者,收益统计,智能体发布',
    ogTitle: '创作者中心 - 管理您的AI智能体',
    ogDescription: '在创作者中心管理您的AI智能体，查看详细的收益统计，轻松创建和发布新的智能体。',
    ogImage: '/images/creator-center-og.jpg'
  }
}

// 导出配置
export default {
  routes: workflowCenterRoutes,
  guard: creatorCenterGuard,
  breadcrumbs: workflowBreadcrumbs,
  seo: workflowSEO
}

// 使用示例：
// 在 router/index.js 中添加：
/*
import { workflowCenterRoutes, creatorCenterGuard } from '@/views/website/workflow/route'

const routes = [
  // 其他路由...
  ...workflowCenterRoutes.map(route => ({
    ...route,
    beforeEnter: creatorCenterGuard.beforeEnter
  }))
]
*/

// 在组件中使用SEO配置：
/*
import { workflowSEO } from '@/views/website/workflow/route'

export default {
  mounted() {
    // 根据当前tab设置SEO信息
    const seoConfig = this.activeTab === 'creator' ? workflowSEO.creator : workflowSEO.market
    document.title = seoConfig.title
    
    // 设置meta标签
    this.updateMetaTags(seoConfig)
  },
  methods: {
    updateMetaTags(config) {
      // 更新description
      let descMeta = document.querySelector('meta[name="description"]')
      if (descMeta) {
        descMeta.setAttribute('content', config.description)
      }
      
      // 更新keywords
      let keywordsMeta = document.querySelector('meta[name="keywords"]')
      if (keywordsMeta) {
        keywordsMeta.setAttribute('content', config.keywords)
      }
      
      // 更新Open Graph标签
      let ogTitleMeta = document.querySelector('meta[property="og:title"]')
      if (ogTitleMeta) {
        ogTitleMeta.setAttribute('content', config.ogTitle)
      }
    }
  }
}
*/
