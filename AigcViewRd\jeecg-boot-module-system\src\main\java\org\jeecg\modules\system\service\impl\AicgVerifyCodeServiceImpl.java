package org.jeecg.modules.system.service.impl;

import org.jeecg.modules.system.entity.AicgVerifyCode;
import org.jeecg.modules.system.entity.VerifyCodeResult;
import org.jeecg.modules.system.entity.VerifyCodeErrorType;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.system.mapper.AicgVerifyCodeMapper;
import org.jeecg.modules.system.service.IAicgVerifyCodeService;
import org.jeecg.modules.system.config.RegisterConfig;
import org.jeecg.modules.system.config.SmsConfig;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.jeecg.common.util.DySmsHelper;
import org.jeecg.common.util.DySmsEnum;
import lombok.extern.slf4j.Slf4j;
import com.alibaba.fastjson.JSONObject;

import javax.mail.MessagingException;
import javax.mail.internet.MimeMessage;
import java.util.Date;
import java.util.Random;
import java.util.Calendar;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;

/**
 * @Description: 验证码记录表
 * @Author: jeecg-boot
 * @Date: 2025-06-19
 * @Version: V1.0
 */
@Service
@Slf4j
public class AicgVerifyCodeServiceImpl extends ServiceImpl<AicgVerifyCodeMapper, AicgVerifyCode> implements IAicgVerifyCodeService {

    @Autowired
    private AicgVerifyCodeMapper verifyCodeMapper;

    @Autowired
    private RegisterConfig registerConfig;

    @Autowired
    private SmsConfig smsConfig;

    @Autowired
    private JavaMailSender mailSender;

    @Value("${spring.mail.username}")
    private String emailFrom;

    @Override
    public VerifyCodeResult sendSmsCode(String phone, String scene, String ipAddress) {
        try {
            // 🛡️ 兼容修复：不再严格区分注册和登录场景
            // 允许已注册用户发送register场景验证码（用于登录）
            // 允许未注册用户发送login场景验证码（用于注册）
            // 具体的用户状态判断在验证码验证时处理
            log.info("发送短信验证码，手机号：{}，场景：{}", phone, scene);

            // 检查发送频率限制
            if (!canSendCode(phone, "sms", scene)) {
                log.warn("短信验证码发送频率限制，手机号：{}，场景：{}", phone, scene);
                return VerifyCodeResult.error(VerifyCodeErrorType.FREQUENCY_LIMIT,
                    VerifyCodeErrorType.getFriendlyMessage(VerifyCodeErrorType.FREQUENCY_LIMIT));
            }

            // 检查按场景的每日限制
            if (!canSendCodeByScene(phone, "sms", scene)) {
                log.warn("短信验证码场景限制，手机号：{}，场景：{}", phone, scene);
                return VerifyCodeResult.error(VerifyCodeErrorType.DAILY_LIMIT,
                    VerifyCodeErrorType.getFriendlyMessage(VerifyCodeErrorType.DAILY_LIMIT));
            }

            if (!canSendCodeByIp(ipAddress, "sms", scene)) {
                log.warn("短信验证码IP发送频率限制，IP：{}", ipAddress);
                return VerifyCodeResult.error(VerifyCodeErrorType.IP_LIMIT,
                    VerifyCodeErrorType.getFriendlyMessage(VerifyCodeErrorType.IP_LIMIT));
            }

            // 生成验证码
            String code = generateCode(6);

            // 发送短信
            JSONObject params = new JSONObject();
            params.put("code", code);

            // 使用配置的模板发送短信
            try {
                boolean sendResult = sendSmsWithConfig(phone, params);

                if (sendResult) {
                    // 保存验证码记录
                    AicgVerifyCode verifyCode = new AicgVerifyCode();
                    verifyCode.setCodeType("sms");
                    verifyCode.setTarget(phone);
                    verifyCode.setCode(code);
                    verifyCode.setScene(scene);
                    verifyCode.setIpAddress(ipAddress);
                    verifyCode.setUsedStatus(0);
                    verifyCode.setCreateTime(new Date());

                    // 设置过期时间
                    Calendar calendar = Calendar.getInstance();
                    calendar.add(Calendar.MINUTE, registerConfig.getVerifyCode().getSms().getExpireMinutes());
                    verifyCode.setExpireTime(calendar.getTime());

                    this.save(verifyCode);
                    log.info("短信验证码发送成功，手机号：{}", phone);
                    return VerifyCodeResult.success("验证码发送成功");
                } else {
                    log.error("短信验证码发送失败，手机号：{}", phone);
                    return VerifyCodeResult.error(VerifyCodeErrorType.NETWORK_ERROR,
                        VerifyCodeErrorType.getFriendlyMessage(VerifyCodeErrorType.NETWORK_ERROR));
                }
            } catch (RuntimeException e) {
                // 🆕 处理短信发送的具体错误
                return handleSmsError(phone, e);
            }
        } catch (Exception e) {
            log.error("短信验证码发送异常，手机号：{}，错误：{}", phone, e.getMessage(), e);
            return VerifyCodeResult.error(VerifyCodeErrorType.NETWORK_ERROR,
                VerifyCodeErrorType.getFriendlyMessage(VerifyCodeErrorType.NETWORK_ERROR));
        }
    }

    @Override
    public VerifyCodeResult sendEmailCode(String email, String scene, String ipAddress) {
        try {
            // 检查发送频率限制
            if (!canSendCode(email, "email", scene)) {
                log.warn("邮箱验证码发送频率限制，邮箱：{}，场景：{}", email, scene);
                return VerifyCodeResult.error(VerifyCodeErrorType.FREQUENCY_LIMIT,
                    VerifyCodeErrorType.getFriendlyMessage(VerifyCodeErrorType.FREQUENCY_LIMIT));
            }

            // 检查按场景的每日限制
            if (!canSendCodeByScene(email, "email", scene)) {
                log.warn("邮箱验证码场景限制，邮箱：{}，场景：{}", email, scene);
                return VerifyCodeResult.error(VerifyCodeErrorType.DAILY_LIMIT,
                    VerifyCodeErrorType.getFriendlyMessage(VerifyCodeErrorType.DAILY_LIMIT));
            }

            if (!canSendCodeByIp(ipAddress, "email", scene)) {
                log.warn("邮箱验证码IP发送频率限制，IP：{}", ipAddress);
                return VerifyCodeResult.error(VerifyCodeErrorType.IP_LIMIT,
                    VerifyCodeErrorType.getFriendlyMessage(VerifyCodeErrorType.IP_LIMIT));
            }

            // 生成验证码
            String code = generateCode(6);

            // 发送邮件
            boolean sendResult = sendEmailWithTemplate(email, code);

            if (sendResult) {
                // 保存验证码记录
                AicgVerifyCode verifyCode = new AicgVerifyCode();
                verifyCode.setCodeType("email");
                verifyCode.setTarget(email);
                verifyCode.setCode(code);
                verifyCode.setScene(scene);
                verifyCode.setIpAddress(ipAddress);
                verifyCode.setUsedStatus(0);
                verifyCode.setCreateTime(new Date());

                // 设置过期时间
                Calendar calendar = Calendar.getInstance();
                calendar.add(Calendar.MINUTE, registerConfig.getVerifyCode().getEmail().getExpireMinutes());
                verifyCode.setExpireTime(calendar.getTime());

                this.save(verifyCode);
                log.info("邮箱验证码发送成功，邮箱：{}", email);
                return VerifyCodeResult.success("验证码发送成功");
            } else {
                log.error("邮箱验证码发送失败，邮箱：{}", email);
                return VerifyCodeResult.error(VerifyCodeErrorType.NETWORK_ERROR,
                    VerifyCodeErrorType.getFriendlyMessage(VerifyCodeErrorType.NETWORK_ERROR));
            }
        } catch (Exception e) {
            log.error("邮箱验证码发送异常，邮箱：{}，错误：{}", email, e.getMessage(), e);
            return VerifyCodeResult.error(VerifyCodeErrorType.NETWORK_ERROR,
                VerifyCodeErrorType.getFriendlyMessage(VerifyCodeErrorType.NETWORK_ERROR));
        }
    }

    @Override
    public boolean verifyCode(String target, String code, String codeType, String scene) {
        try {
            // 🛡️ 关键修复：兼容登录和注册场景
            // 先尝试查询指定场景的验证码
            AicgVerifyCode verifyCode = verifyCodeMapper.getLatestValidCode(target, codeType, scene);

            // 如果没找到，且当前是login场景，则尝试查找register场景的验证码
            if (verifyCode == null && "login".equals(scene)) {
                log.info("login场景未找到验证码，尝试查找register场景，目标：{}，类型：{}", target, codeType);
                verifyCode = verifyCodeMapper.getLatestValidCode(target, codeType, "register");
            }

            // 如果没找到，且当前是register场景，则尝试查找login场景的验证码
            if (verifyCode == null && "register".equals(scene)) {
                log.info("register场景未找到验证码，尝试查找login场景，目标：{}，类型：{}", target, codeType);
                verifyCode = verifyCodeMapper.getLatestValidCode(target, codeType, "login");
            }

            if (verifyCode == null) {
                log.warn("验证码不存在或已过期，目标：{}，类型：{}，场景：{}", target, codeType, scene);
                return false;
            }

            if (!verifyCode.getCode().equals(code)) {
                log.warn("验证码错误，目标：{}，输入：{}，正确：{}", target, code, verifyCode.getCode());
                return false;
            }

            // 标记为已使用
            verifyCodeMapper.markAsUsed(verifyCode.getId());
            log.info("验证码验证成功，目标：{}，类型：{}，实际场景：{}", target, codeType, verifyCode.getScene());
            return true;
        } catch (Exception e) {
            log.error("验证码验证异常，目标：{}，错误：{}", target, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean canSendCode(String target, String codeType, String scene) {
        int intervalSeconds = "sms".equals(codeType) ? 
            registerConfig.getVerifyCode().getSms().getSendIntervalSeconds() :
            registerConfig.getVerifyCode().getEmail().getSendIntervalSeconds();

        int count = verifyCodeMapper.countRecentCodes(target, codeType, scene, intervalSeconds);
        return count == 0;
    }

    @Override
    public boolean canSendCodeByIp(String ipAddress, String codeType, String scene) {
        // IP限制使用1小时内的发送次数
        int count = verifyCodeMapper.countRecentCodesByIp(ipAddress, codeType, scene, 3600);
        return count < 10; // 每小时最多10次
    }

    /**
     * 检查按场景的每日发送限制
     */
    public boolean canSendCodeByScene(String target, String codeType, String scene) {
        int dailyLimit;

        // 根据场景设置不同的每日限制
        switch (scene) {
            case "register":
                // 注册场景使用注册限制
                dailyLimit = "sms".equals(codeType) ?
                    registerConfig.getSecurity().getPhoneRegisterLimitDaily() :
                    registerConfig.getSecurity().getEmailRegisterLimitDaily();
                break;
            case "reset":
                // 重置密码场景限制
                dailyLimit = registerConfig.getSecurity().getResetCodeLimitDaily();
                break;
            case "login":
            default:
                // 登录场景或其他场景使用总限制
                dailyLimit = "sms".equals(codeType) ?
                    registerConfig.getSecurity().getSmsCodeLimitDaily() :
                    registerConfig.getSecurity().getEmailCodeLimitDaily();
                break;
        }

        // 检查当天发送次数
        int count = verifyCodeMapper.countDailyCodes(target, codeType, scene);
        return count < dailyLimit;
    }

    @Override
    public String generateCode(int length) {
        Random random = new Random();
        StringBuilder code = new StringBuilder();
        for (int i = 0; i < length; i++) {
            code.append(random.nextInt(10));
        }
        return code.toString();
    }

    @Override
    public int cleanExpiredCodes() {
        return verifyCodeMapper.cleanExpiredCodes();
    }

    /**
     * 使用模板发送邮件
     */
    private boolean sendEmailWithTemplate(String email, String code) {
        try {
            MimeMessage message = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message, true, "UTF-8");

            // 设置发件人地址（必须与认证用户一致）
            helper.setFrom(emailFrom);
            helper.setTo(email);
            helper.setSubject("智界AIGC验证码");

            // 读取邮件模板
            String templatePath = "项目文档/邮件验证码模板.html";
            String htmlContent;
            try {
                htmlContent = new String(Files.readAllBytes(Paths.get(templatePath)), "UTF-8");
                // 替换验证码
                htmlContent = htmlContent.replace("${code}", code);
            } catch (IOException e) {
                // 如果模板文件不存在，使用简单的HTML
                htmlContent = getSimpleEmailTemplate(code);
            }

            helper.setText(htmlContent, true);
            mailSender.send(message);
            return true;
        } catch (MessagingException e) {
            log.error("邮件发送失败：{}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 简单的邮件模板
     */
    private String getSimpleEmailTemplate(String code) {
        return "<html><body>" +
                "<h2>智界AIGC验证码</h2>" +
                "<p>您的验证码是：<strong style='font-size:24px;color:#1890ff;'>" + code + "</strong></p>" +
                "<p>验证码有效期为5分钟，请及时使用。</p>" +
                "<p>如果您没有进行此操作，请忽略此邮件。</p>" +
                "</body></html>";
    }

    /**
     * 🆕 处理短信发送错误
     */
    private VerifyCodeResult handleSmsError(String phone, RuntimeException e) {
        String errorMessage = e.getMessage();
        log.error("短信验证码发送失败，手机号：{}，错误：{}", phone, errorMessage);

        if (errorMessage != null) {
            if (errorMessage.startsWith("FLOW_CONTROL_ERROR:")) {
                return VerifyCodeResult.error(VerifyCodeErrorType.FLOW_CONTROL_ERROR,
                    VerifyCodeErrorType.getFriendlyMessage(VerifyCodeErrorType.FLOW_CONTROL_ERROR));
            } else if (errorMessage.startsWith("SMS_PLATFORM_ERROR:")) {
                return VerifyCodeResult.error(VerifyCodeErrorType.SMS_PLATFORM_ERROR,
                    VerifyCodeErrorType.getFriendlyMessage(VerifyCodeErrorType.SMS_PLATFORM_ERROR));
            } else if (errorMessage.startsWith("SMS_SEND_ERROR:")) {
                // 其他短信平台错误
                return VerifyCodeResult.error(VerifyCodeErrorType.SMS_PLATFORM_ERROR,
                    VerifyCodeErrorType.getFriendlyMessage(VerifyCodeErrorType.SMS_PLATFORM_ERROR));
            }
        }

        // 默认返回网络错误
        return VerifyCodeResult.error(VerifyCodeErrorType.NETWORK_ERROR,
            VerifyCodeErrorType.getFriendlyMessage(VerifyCodeErrorType.NETWORK_ERROR));
    }

    /**
     * 使用配置发送短信
     */
    private boolean sendSmsWithConfig(String phone, JSONObject params) {
        try {
            // 设置阿里云短信配置
            DySmsHelper.setAccessKeyId(smsConfig.getAccessKeyId());
            DySmsHelper.setAccessKeySecret(smsConfig.getAccessKeySecret());

            // 创建自定义的DySmsEnum，使用配置的模板
            DySmsEnum customTemplate = createCustomSmsTemplate();

            // 发送短信
            return DySmsHelper.sendSms(phone, params, customTemplate);
        } catch (Exception e) {
            log.error("发送短信失败：{}", e.getMessage(), e);
            // 🆕 重新抛出异常，让上层处理
            throw new RuntimeException(e.getMessage(), e);
        }
    }

    /**
     * 创建自定义短信模板
     */
    private DySmsEnum createCustomSmsTemplate() {
        // 使用配置文件中的模板信息创建自定义模板
        // 由于DySmsEnum是枚举，我们需要找一个现有的并修改其值
        DySmsEnum template = DySmsEnum.REGISTER_TEMPLATE_CODE;
        template.setTemplateCode(smsConfig.getTemplateCode());
        template.setSignName(smsConfig.getSignName());
        return template;
    }

    // 已删除 isPhoneAlreadyRegistered 方法，因为现在不再区分注册和登录场景

    @Override
    public boolean resetUserVerifyCodeLimit(String phone, String email, String resetType, String ipAddress) {
        try {
            int deletedCount = 0;
            int ipDeletedCount = 0;

            switch (resetType) {
                case "sms":
                    // 清除短信验证码记录（包含IP限制）
                    if (oConvertUtils.isNotEmpty(phone)) {
                        deletedCount += verifyCodeMapper.deleteUserTodayRecords(phone, "sms");
                    }
                    // 同时清除IP的短信验证码记录（1小时内）
                    if (oConvertUtils.isNotEmpty(ipAddress)) {
                        ipDeletedCount += verifyCodeMapper.deleteIpRecentRecords(ipAddress, "sms", 1);
                    }
                    break;
                case "email":
                    // 清除邮箱验证码记录（包含IP限制）
                    if (oConvertUtils.isNotEmpty(email)) {
                        deletedCount += verifyCodeMapper.deleteUserTodayRecords(email, "email");
                    }
                    // 同时清除IP的邮箱验证码记录（1小时内）
                    if (oConvertUtils.isNotEmpty(ipAddress)) {
                        ipDeletedCount += verifyCodeMapper.deleteIpRecentRecords(ipAddress, "email", 1);
                    }
                    break;
                case "all":
                default:
                    // 清除所有验证码记录（包含IP限制）
                    if (oConvertUtils.isNotEmpty(phone)) {
                        deletedCount += verifyCodeMapper.deleteUserTodayRecords(phone, "sms");
                    }
                    if (oConvertUtils.isNotEmpty(email)) {
                        deletedCount += verifyCodeMapper.deleteUserTodayRecords(email, "email");
                    }
                    // 同时清除IP的所有验证码记录（1小时内）
                    if (oConvertUtils.isNotEmpty(ipAddress)) {
                        ipDeletedCount += verifyCodeMapper.deleteIpRecentRecords(ipAddress, "sms", 1);
                        ipDeletedCount += verifyCodeMapper.deleteIpRecentRecords(ipAddress, "email", 1);
                    }
                    break;
            }

            log.info("解除用户验证码限制成功，手机号：{}，邮箱：{}，IP：{}，类型：{}，清除用户记录数：{}，清除IP记录数：{}",
                    phone, email, ipAddress, resetType, deletedCount, ipDeletedCount);
            return true;
        } catch (Exception e) {
            log.error("解除用户验证码限制失败，手机号：{}，邮箱：{}，IP：{}，类型：{}，错误：{}",
                    phone, email, ipAddress, resetType, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public int getTodayCodeCount(String target, String codeType) {
        try {
            if (oConvertUtils.isEmpty(target) || oConvertUtils.isEmpty(codeType)) {
                return 0;
            }
            return verifyCodeMapper.countTodayCodes(target, codeType);
        } catch (Exception e) {
            log.error("获取今日验证码发送次数失败，目标：{}，类型：{}，错误：{}",
                    target, codeType, e.getMessage(), e);
            return 0;
        }
    }

    @Override
    public int getIpRecentCodeCount(String ipAddress, String codeType, int hours) {
        try {
            if (oConvertUtils.isEmpty(ipAddress) || oConvertUtils.isEmpty(codeType)) {
                return 0;
            }
            return verifyCodeMapper.countIpRecentCodes(ipAddress, codeType, hours);
        } catch (Exception e) {
            log.error("获取IP近期验证码发送次数失败，IP：{}，类型：{}，小时数：{}，错误：{}",
                    ipAddress, codeType, hours, e.getMessage(), e);
            return 0;
        }
    }

    @Override
    public int getUserRelatedIpRecentCodeCount(String target, String codeType, int hours) {
        try {
            if (oConvertUtils.isEmpty(target) || oConvertUtils.isEmpty(codeType)) {
                return 0;
            }
            return verifyCodeMapper.countUserRelatedIpRecentCodes(target, codeType, hours);
        } catch (Exception e) {
            log.error("获取用户相关IP近期验证码发送次数失败，目标：{}，类型：{}，小时数：{}，错误：{}",
                    target, codeType, hours, e.getMessage(), e);
            return 0;
        }
    }
}
