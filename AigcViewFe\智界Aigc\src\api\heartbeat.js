import { postAction, getAction } from '@/api/manage'

/**
 * 🔄 心跳API接口封装
 * 提供用户活跃状态相关的API调用方法
 *
 * 功能特性：
 * - 统一错误处理和重试机制
 * - 标准响应格式处理
 * - 请求参数验证
 * - 性能监控集成
 * - 缓存机制支持
 *
 * <AUTHOR>
 * @since 2025-01-30
 */

// ==================== 核心心跳API ====================

/**
 * 🔄 发送用户心跳（增强版）
 * @param {Object} heartbeatData 心跳数据
 * @param {Object} options 可选配置
 * @returns {Promise} 标准化API响应
 */
export async function sendHeartbeat(heartbeatData, options = {}) {
  try {
    // 参数验证
    if (!heartbeatData || typeof heartbeatData !== 'object') {
      throw new Error('心跳数据不能为空且必须为对象类型')
    }

    // 添加默认参数
    const requestData = {
      timestamp: Date.now(),
      userAgent: navigator.userAgent,
      pageUrl: window.location.href,
      ...heartbeatData
    }

    // 发送请求
    const response = await postAction('/jeecg-boot/api/heartbeat', requestData)

    // 标准化响应处理
    return handleApiResponse(response, '发送心跳')

  } catch (error) {
    console.error('发送心跳失败:', error)

    // 返回标准错误格式
    return {
      success: false,
      message: error.message || '心跳发送失败',
      code: error.code || 500,
      result: null,
      timestamp: Date.now()
    }
  }
}

/**
 * 🔄 带重试机制的心跳发送
 * @param {Object} heartbeatData 心跳数据
 * @param {Object} retryOptions 重试配置
 * @returns {Promise} API响应
 */
export async function sendHeartbeatWithRetry(heartbeatData, retryOptions = {}) {
  const {
    maxRetries = 3,
    retryDelay = 1000,
    backoffMultiplier = 2
  } = retryOptions

  let lastError = null

  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      const response = await sendHeartbeat(heartbeatData)

      if (response.success) {
        return response
      }

      // 如果是业务错误（非网络错误），不重试
      if (response.code >= 400 && response.code < 500) {
        return response
      }

      lastError = new Error(response.message || '心跳发送失败')

    } catch (error) {
      lastError = error

      // 最后一次尝试，直接抛出错误
      if (attempt === maxRetries) {
        break
      }

      // 等待后重试
      const delay = retryDelay * Math.pow(backoffMultiplier, attempt)
      await new Promise(resolve => setTimeout(resolve, delay))
    }
  }

  // 所有重试都失败
  return {
    success: false,
    message: `心跳发送失败，已重试${maxRetries}次: ${lastError.message}`,
    code: 500,
    result: null,
    timestamp: Date.now()
  }
}

// ==================== 用户状态API ====================

/**
 * 📊 获取用户在线状态（增强版）
 * @param {String} userId 用户ID
 * @param {Object} options 可选配置
 * @returns {Promise} 标准化API响应
 */
export async function getUserOnlineStatus(userId, options = {}) {
  try {
    if (!userId) {
      throw new Error('用户ID不能为空')
    }

    const response = await getAction('/jeecg-boot/api/user/online-status', {
      userId,
      includeDetails: options.includeDetails || false,
      timestamp: Date.now()
    })

    return handleApiResponse(response, '获取用户在线状态')

  } catch (error) {
    console.error('获取用户在线状态失败:', error)
    return createErrorResponse(error, '获取用户在线状态失败')
  }
}

/**
 * 📈 获取在线用户统计（增强版）
 * @param {Object} options 可选配置
 * @returns {Promise} 标准化API响应
 */
export async function getOnlineUserStats(options = {}) {
  try {
    const params = {
      includeHistory: options.includeHistory || false,
      timeRange: options.timeRange || '24h',
      groupBy: options.groupBy || 'hour',
      timestamp: Date.now()
    }

    const response = await getAction('/jeecg-boot/api/online-users/stats', params)
    return handleApiResponse(response, '获取在线用户统计')

  } catch (error) {
    console.error('获取在线用户统计失败:', error)
    return createErrorResponse(error, '获取在线用户统计失败')
  }
}

/**
 * 🎯 获取用户活跃度信息（增强版）
 * @param {String} userId 用户ID
 * @param {Object} options 可选配置
 * @returns {Promise} 标准化API响应
 */
export async function getUserActivityInfo(userId, options = {}) {
  try {
    if (!userId) {
      throw new Error('用户ID不能为空')
    }

    const params = {
      userId,
      includeScore: options.includeScore || true,
      includeHistory: options.includeHistory || false,
      timeRange: options.timeRange || '7d',
      timestamp: Date.now()
    }

    const response = await getAction('/jeecg-boot/api/user/activity-info', params)
    return handleApiResponse(response, '获取用户活跃度信息')

  } catch (error) {
    console.error('获取用户活跃度信息失败:', error)
    return createErrorResponse(error, '获取用户活跃度信息失败')
  }
}

// ==================== 批量操作API ====================

/**
 * 📊 批量获取用户在线状态（增强版）
 * @param {Array} userIds 用户ID列表
 * @param {Object} options 可选配置
 * @returns {Promise} 标准化API响应
 */
export async function getBatchUserOnlineStatus(userIds, options = {}) {
  try {
    if (!Array.isArray(userIds) || userIds.length === 0) {
      throw new Error('用户ID列表不能为空且必须为数组类型')
    }

    if (userIds.length > 100) {
      throw new Error('批量查询用户数量不能超过100个')
    }

    const requestData = {
      userIds,
      includeDetails: options.includeDetails || false,
      timestamp: Date.now()
    }

    const response = await postAction('/jeecg-boot/api/user/batch-online-status', requestData)
    return handleApiResponse(response, '批量获取用户在线状态')

  } catch (error) {
    console.error('批量获取用户在线状态失败:', error)
    return createErrorResponse(error, '批量获取用户在线状态失败')
  }
}

// ==================== 统计数据API ====================

/**
 * 📈 获取实时统计数据（增强版）
 * @param {String} apiKey API密钥
 * @param {Object} options 可选配置
 * @returns {Promise} 标准化API响应
 */
export async function getRealTimeStats(apiKey, options = {}) {
  try {
    if (!apiKey) {
      throw new Error('API密钥不能为空')
    }

    const requestData = {
      apiKey,
      includeHistory: options.includeHistory || false,
      timeRange: options.timeRange || '1h',
      metrics: options.metrics || ['online_users', 'api_calls', 'active_sessions'],
      timestamp: Date.now()
    }

    const response = await postAction('/jeecg-boot/api/dashboard-data', requestData)
    return handleApiResponse(response, '获取实时统计数据')

  } catch (error) {
    console.error('获取实时统计数据失败:', error)
    return createErrorResponse(error, '获取实时统计数据失败')
  }
}

/**
 * 📊 获取用户使用统计（增强版）
 * @param {String} userId 用户ID
 * @param {String} apiKey API密钥
 * @param {Object} options 可选配置
 * @returns {Promise} 标准化API响应
 */
export async function getUserUsageStats(userId, apiKey, options = {}) {
  try {
    if (!userId) {
      throw new Error('用户ID不能为空')
    }
    if (!apiKey) {
      throw new Error('API密钥不能为空')
    }

    const requestData = {
      apiKey,
      timeRange: options.timeRange || '30d',
      includeDetails: options.includeDetails || true,
      groupBy: options.groupBy || 'day',
      timestamp: Date.now()
    }

    const response = await postAction(`/jeecg-boot/api/admin/usage-stats/${userId}`, requestData)
    return handleApiResponse(response, '获取用户使用统计')

  } catch (error) {
    console.error('获取用户使用统计失败:', error)
    return createErrorResponse(error, '获取用户使用统计失败')
  }
}

// ==================== 新增API方法 ====================

/**
 * 🔧 获取心跳配置
 * @param {String} pageType 页面类型
 * @param {Object} options 可选配置
 * @returns {Promise} 标准化API响应
 */
export async function getHeartbeatConfig(pageType, options = {}) {
  try {
    const params = {
      pageType: pageType || 'default',
      userId: options.userId,
      timestamp: Date.now()
    }

    const response = await getAction('/jeecg-boot/api/heartbeat/config', params)
    return handleApiResponse(response, '获取心跳配置')

  } catch (error) {
    console.error('获取心跳配置失败:', error)
    return createErrorResponse(error, '获取心跳配置失败')
  }
}

/**
 * 🔍 检查心跳状态
 * @param {String} sessionId 会话ID
 * @returns {Promise} 标准化API响应
 */
export async function checkHeartbeatStatus(sessionId) {
  try {
    if (!sessionId) {
      throw new Error('会话ID不能为空')
    }

    const response = await getAction('/jeecg-boot/api/heartbeat/status', {
      sessionId,
      timestamp: Date.now()
    })

    return handleApiResponse(response, '检查心跳状态')

  } catch (error) {
    console.error('检查心跳状态失败:', error)
    return createErrorResponse(error, '检查心跳状态失败')
  }
}

/**
 * 📜 获取心跳历史记录
 * @param {Object} params 查询参数
 * @returns {Promise} 标准化API响应
 */
export async function getHeartbeatHistory(params = {}) {
  try {
    const queryParams = {
      userId: params.userId,
      startTime: params.startTime,
      endTime: params.endTime,
      pageSize: params.pageSize || 20,
      pageNo: params.pageNo || 1,
      timestamp: Date.now()
    }

    const response = await getAction('/jeecg-boot/api/heartbeat/history', queryParams)
    return handleApiResponse(response, '获取心跳历史记录')

  } catch (error) {
    console.error('获取心跳历史记录失败:', error)
    return createErrorResponse(error, '获取心跳历史记录失败')
  }
}

/**
 * 📊 获取性能监控数据
 * @param {Object} params 查询参数
 * @returns {Promise} 标准化API响应
 */
export async function getPerformanceMetrics(params = {}) {
  try {
    const queryParams = {
      timeRange: params.timeRange || '1h',
      metrics: params.metrics || ['response_time', 'success_rate', 'error_rate'],
      groupBy: params.groupBy || 'minute',
      timestamp: Date.now()
    }

    const response = await getAction('/jeecg-boot/api/heartbeat/performance', queryParams)
    return handleApiResponse(response, '获取性能监控数据')

  } catch (error) {
    console.error('获取性能监控数据失败:', error)
    return createErrorResponse(error, '获取性能监控数据失败')
  }
}

// ==================== 高级封装API ====================

/**
 * 🚀 智能心跳发送（自动重试+缓存）
 * @param {Object} heartbeatData 心跳数据
 * @param {Object} options 配置选项
 * @returns {Promise} 标准化API响应
 */
export async function smartHeartbeat(heartbeatData, options = {}) {
  const {
    enableCache = true,
    cacheKey = 'heartbeat_cache',
    cacheTTL = 30000, // 30秒缓存
    enableRetry = true,
    retryOptions = {}
  } = options

  try {
    // 检查缓存
    if (enableCache) {
      const cachedResponse = getCachedResponse(cacheKey)
      if (cachedResponse && (Date.now() - cachedResponse.timestamp) < cacheTTL) {
        return cachedResponse
      }
    }

    // 发送心跳（带重试）
    const response = enableRetry
      ? await sendHeartbeatWithRetry(heartbeatData, retryOptions)
      : await sendHeartbeat(heartbeatData)

    // 缓存成功响应
    if (enableCache && response.success) {
      setCachedResponse(cacheKey, response)
    }

    return response

  } catch (error) {
    console.error('智能心跳发送失败:', error)
    return createErrorResponse(error, '智能心跳发送失败')
  }
}

/**
 * 📊 批量数据获取（并发优化）
 * @param {Array} requests 请求列表
 * @param {Object} options 配置选项
 * @returns {Promise} 批量响应结果
 */
export async function batchDataFetch(requests, options = {}) {
  const {
    concurrency = 5, // 并发数限制
    timeout = 30000   // 超时时间
  } = options

  try {
    if (!Array.isArray(requests) || requests.length === 0) {
      throw new Error('请求列表不能为空且必须为数组类型')
    }

    // 分批并发执行
    const results = []
    for (let i = 0; i < requests.length; i += concurrency) {
      const batch = requests.slice(i, i + concurrency)

      const batchPromises = batch.map(async (request) => {
        try {
          const { method, params } = request

          // 根据方法类型调用对应API
          switch (method) {
            case 'getUserOnlineStatus':
              return await getUserOnlineStatus(params.userId, params.options)
            case 'getUserActivityInfo':
              return await getUserActivityInfo(params.userId, params.options)
            case 'getOnlineUserStats':
              return await getOnlineUserStats(params.options)
            default:
              throw new Error(`不支持的方法类型: ${method}`)
          }
        } catch (error) {
          return createErrorResponse(error, `批量请求失败: ${request.method}`)
        }
      })

      // 等待当前批次完成
      const batchResults = await Promise.allSettled(batchPromises)
      results.push(...batchResults.map(result =>
        result.status === 'fulfilled' ? result.value : result.reason
      ))
    }

    return {
      success: true,
      message: '批量数据获取完成',
      code: 200,
      result: {
        total: results.length,
        success: results.filter(r => r.success).length,
        failed: results.filter(r => !r.success).length,
        data: results
      },
      timestamp: Date.now()
    }

  } catch (error) {
    console.error('批量数据获取失败:', error)
    return createErrorResponse(error, '批量数据获取失败')
  }
}

// ==================== 工具函数 ====================

/**
 * 🔧 标准化API响应处理
 * @param {Object} response 原始响应
 * @param {String} operation 操作名称
 * @returns {Object} 标准化响应
 */
function handleApiResponse(response, operation) {
  try {
    // 检查响应是否存在
    if (!response) {
      throw new Error('响应数据为空')
    }

    // 标准化响应格式
    const standardResponse = {
      success: response.success !== false, // 默认为true，除非明确为false
      message: response.message || `${operation}成功`,
      code: response.code || 200,
      result: response.result || response.data || response, // 兼容不同字段名
      timestamp: response.timestamp || Date.now()
    }

    // 如果原响应明确标识失败
    if (response.success === false) {
      standardResponse.success = false
      standardResponse.message = response.message || `${operation}失败`
      standardResponse.code = response.code || 500
    }

    return standardResponse

  } catch (error) {
    console.error(`${operation}响应处理失败:`, error)
    return createErrorResponse(error, `${operation}响应处理失败`)
  }
}

/**
 * 🚨 创建标准错误响应
 * @param {Error} error 错误对象
 * @param {String} operation 操作名称
 * @returns {Object} 标准错误响应
 */
function createErrorResponse(error, operation) {
  return {
    success: false,
    message: error.message || operation || '操作失败',
    code: error.code || error.status || 500,
    result: null,
    timestamp: Date.now(),
    error: {
      type: error.name || 'Error',
      stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
    }
  }
}

/**
 * 💾 获取缓存响应
 * @param {String} key 缓存键
 * @returns {Object|null} 缓存的响应或null
 */
function getCachedResponse(key) {
  try {
    const cached = sessionStorage.getItem(`heartbeat_${key}`)
    return cached ? JSON.parse(cached) : null
  } catch (error) {
    console.warn('获取缓存失败:', error)
    return null
  }
}

/**
 * 💾 设置缓存响应
 * @param {String} key 缓存键
 * @param {Object} response 响应数据
 */
function setCachedResponse(key, response) {
  try {
    sessionStorage.setItem(`heartbeat_${key}`, JSON.stringify(response))
  } catch (error) {
    console.warn('设置缓存失败:', error)
  }
}

/**
 * 🧹 清理缓存
 * @param {String} pattern 缓存键模式（可选）
 */
export function clearHeartbeatCache(pattern = '') {
  try {
    const keys = Object.keys(sessionStorage)
    const targetKeys = keys.filter(key =>
      key.startsWith('heartbeat_') &&
      (pattern === '' || key.includes(pattern))
    )

    targetKeys.forEach(key => sessionStorage.removeItem(key))

    console.log(`清理了 ${targetKeys.length} 个心跳缓存项`)

  } catch (error) {
    console.warn('清理缓存失败:', error)
  }
}

// ==================== 便捷方法导出 ====================

/**
 * 🎯 心跳API便捷方法集合
 * 提供常用的心跳相关操作的快捷访问
 */
export const HeartbeatApi = {
  // 核心方法
  send: sendHeartbeat,
  sendWithRetry: sendHeartbeatWithRetry,
  smart: smartHeartbeat,

  // 状态查询
  getUserStatus: getUserOnlineStatus,
  getStats: getOnlineUserStats,
  getActivity: getUserActivityInfo,

  // 批量操作
  batchStatus: getBatchUserOnlineStatus,
  batchFetch: batchDataFetch,

  // 配置和监控
  getConfig: getHeartbeatConfig,
  checkStatus: checkHeartbeatStatus,
  getHistory: getHeartbeatHistory,
  getMetrics: getPerformanceMetrics,

  // 统计数据
  getRealTime: getRealTimeStats,
  getUsage: getUserUsageStats,

  // 工具方法
  clearCache: clearHeartbeatCache
}

/**
 * 📚 使用示例和最佳实践
 *
 * // 1. 基础心跳发送
 * const response = await HeartbeatApi.send({
 *   pageType: 'home',
 *   apiKey: 'your-api-key'
 * })
 *
 * // 2. 智能心跳（带缓存和重试）
 * const smartResponse = await HeartbeatApi.smart({
 *   pageType: 'market',
 *   apiKey: 'your-api-key'
 * }, {
 *   enableCache: true,
 *   enableRetry: true,
 *   retryOptions: { maxRetries: 3 }
 * })
 *
 * // 3. 批量获取用户状态
 * const batchResponse = await HeartbeatApi.batchStatus(['user1', 'user2', 'user3'])
 *
 * // 4. 获取实时统计
 * const statsResponse = await HeartbeatApi.getRealTime('your-api-key', {
 *   timeRange: '1h',
 *   metrics: ['online_users', 'api_calls']
 * })
 *
 * // 5. 清理缓存
 * HeartbeatApi.clearCache('heartbeat_cache')
 */

// ==================== 默认导出 ====================

export default HeartbeatApi
