# 图生视频插件返回格式说明

## 📋 **返回格式概述**

图生视频插件的所有接口都采用**直接JSON对象**的返回格式，不使用Result包装器。成功时返回业务数据，失败时返回包含error字段的对象。

**重要**: 创建视频任务接口的返回格式会根据 `asyn` 参数的值而不同：
- `asyn: false` (默认) - **异步模式**：立即返回任务ID
- `asyn: true` - **同步模式**：等待视频生成完成再返回结果

---

## 🚀 **创建视频任务接口**

### **接口地址**
```
POST /api/coze/video/generate-task
```

### **异步模式响应格式 (asyn: false)**
```json
{
  "task_id": "task_abc123def456",
  "status": "pending",
  "message": "视频生成任务已创建",
  "deductedAmount": 0.69,
  "balanceAfter": 99.31,
  "pricingDetails": {
    "model": "doubao-seedance-1-0-lite-i2v-250428",
    "resolution": "480p",
    "duration": 5,
    "userLevel": "user",
    "finalPrice": 0.69
  }
}
```

### **同步模式响应格式 (asyn: true)**

#### **生成成功时**
```json
{
  "task_id": "task_abc123def456",
  "status": "completed",
  "message": "视频生成完成",
  "video_url": "https://cdn.aigcview.com/videos/generated_video_123.mp4",
  "deductedAmount": 0.69,
  "balanceAfter": 99.31,
  "pricingDetails": {
    "model": "doubao-seedance-1-0-lite-i2v-250428",
    "resolution": "480p",
    "duration": 5,
    "userLevel": "user",
    "finalPrice": 0.69
  }
}
```

#### **生成失败时**
```json
{
  "error": "视频生成失败: 图片格式不支持"
}
```

#### **等待超时时**
```json
{
  "task_id": "task_abc123def456",
  "status": "running",
  "message": "视频生成超时，请使用task_id查询结果",
  "deductedAmount": 0.69,
  "balanceAfter": 99.31
}
```

### **失败响应格式**
```json
{
  "error": "API-Key无效或已被禁用"
}
```

### **字段说明**
| 字段名 | 类型 | 说明 |
|--------|------|------|
| `task_id` | String | 视频生成任务的唯一标识 |
| `status` | String | 任务状态：pending/running/completed/failed |
| `message` | String | 状态描述信息 |
| `video_url` | String | 生成的视频URL（仅同步模式且完成时存在） |
| `deductedAmount` | Number | 本次扣费金额（元） |
| `balanceAfter` | Number | 扣费后的账户余额（元） |
| `pricingDetails` | Object | 定价详情信息 |
| `error` | String | 错误信息（仅失败时存在） |

---

## 🔄 **asyn参数详细说明**

### **异步模式 (asyn: false, 默认)**
- **行为**: 立即返回任务ID，不等待视频生成完成
- **优点**: 响应快速，不会超时
- **缺点**: 需要额外调用查询接口获取结果
- **适用场景**: 大部分情况，特别是可能耗时较长的任务

### **同步模式 (asyn: true)**
- **行为**: 等待视频生成完成再返回结果（最多等待5分钟）
- **优点**: 一次调用即可获得最终结果
- **缺点**: 可能超时，响应时间长
- **适用场景**: 需要立即获得结果的场景

### **同步模式等待机制**
- **轮询间隔**: 每5秒查询一次任务状态
- **最大等待时间**: 300秒（5分钟）
- **超时处理**: 超时后返回异步格式的响应，包含task_id供后续查询

---

## 🔍 **查询视频任务状态接口**

### **接口地址**
```
POST /api/coze/video/query-task
```

### **请求参数**
```json
{
  "task_id": "task_abc123def456",
  "wait_cnt": 3  // 可选，轮询等待次数，范围1-10，默认1
}
```

### **任务进行中响应**
```json
{
  "task_id": "task_abc123def456",
  "status": "running",
  "message": "视频正在生成中...",
  "created_at": "2025-01-16T10:30:00Z",
  "updated_at": "2025-01-16T10:31:30Z"
}
```

### **任务完成响应**
```json
{
  "task_id": "task_abc123def456",
  "status": "completed",
  "message": "视频生成完成",
  "created_at": "2025-01-16T10:30:00Z",
  "updated_at": "2025-01-16T10:32:15Z",
  "video_url": "https://cdn.aigcview.com/videos/generated_video_123.mp4"
}
```

### **任务失败响应**
```json
{
  "task_id": "task_abc123def456",
  "status": "failed",
  "message": "视频生成失败: 图片格式不支持",
  "created_at": "2025-01-16T10:30:00Z",
  "updated_at": "2025-01-16T10:31:45Z",
  "failure_reason": "图片格式不支持"
}
```

### **查询失败响应**
```json
{
  "error": "任务ID不能为空"
}
```

### **字段说明**
| 字段名 | 类型 | 说明 |
|--------|------|------|
| `task_id` | String | 任务ID |
| `status` | String | 任务状态：pending/running/completed/failed/cancelled |
| `message` | String | 状态描述 |
| `created_at` | String | 任务创建时间（ISO 8601格式） |
| `updated_at` | String | 任务最后更新时间 |
| `video_url` | String | 生成的视频URL（仅completed状态时存在） |
| `failure_reason` | String | 失败原因（仅failed状态时存在） |
| `error` | String | 错误信息（仅查询失败时存在） |

### **wait_cnt参数说明**

`wait_cnt`参数用于控制轮询查询的行为：

- **范围**: 1-10
- **默认值**: 1
- **作用**: 当任务状态为`pending`或`running`时，系统会间隔3秒重复查询，直到：
  - 任务完成（`completed`）
  - 任务失败（`failed`）
  - 任务取消（`cancelled`）
  - 达到指定的查询次数

#### **使用场景**
- `wait_cnt=1`: 立即查询一次，适合快速检查状态
- `wait_cnt=3`: 最多等待9秒（3次×3秒间隔），适合短时任务
- `wait_cnt=5`: 最多等待15秒，适合中等时长任务
- `wait_cnt=10`: 最多等待30秒，适合较长时间任务

---

## ❌ **取消视频任务接口**

### **接口地址**
```
POST /api/coze/video/cancel-task
```

### **取消成功响应**
```json
{
  "task_id": "task_abc123def456",
  "status": "cancelled",
  "message": "任务已取消"
}
```

### **取消失败响应**
```json
{
  "error": "任务ID不能为空"
}
```

---

## 📊 **任务状态说明**

| 状态值 | 状态名称 | 说明 |
|--------|----------|------|
| `pending` | 等待中 | 任务已创建，等待处理 |
| `running` | 生成中 | 视频正在生成中 |
| `completed` | 已完成 | 视频生成完成，可获取视频URL |
| `failed` | 失败 | 视频生成失败，查看失败原因 |
| `cancelled` | 已取消 | 任务被用户取消 |

---

## 🚨 **常见错误信息**

### **参数验证错误**
```json
{
  "error": "提示词不能为空"
}
```

```json
{
  "error": "pro模型不支持720p分辨率，请选择480p或1080p"
}
```

### **认证错误**
```json
{
  "error": "API-Key无效或已被禁用"
}
```

```json
{
  "error": "API密钥格式错误，应以'ak_'开头"
}
```

### **余额错误**
```json
{
  "error": "余额不足，需要0.69元，当前余额0.10元"
}
```

### **系统错误**
```json
{
  "error": "扣费失败，请稍后重试"
}
```

```json
{
  "error": "系统异常，请稍后重试：网络连接超时"
}
```

---

## 💰 **定价详情字段说明**

`pricingDetails` 对象包含以下字段：

| 字段名 | 类型 | 说明 |
|--------|------|------|
| `model` | String | 使用的AI模型 |
| `resolution` | String | 视频分辨率 |
| `duration` | Number | 视频时长（秒） |
| `userLevel` | String | 用户等级：user/VIP/SVIP/admin |
| `finalPrice` | Number | 最终价格（元） |

---

## 🔧 **调用示例**

### **JavaScript调用示例**

#### **异步模式调用**
```javascript
// 异步模式：立即返回任务ID
const createTaskAsync = async () => {
  const response = await fetch('/api/coze/video/generate-task', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      apiKey: 'ak_your_api_key',
      model: 'doubao-seedance-1-0-lite-i2v-250428',
      prompt: '一只可爱的小猫在花园里玩耍',
      image_url: 'https://example.com/cat.jpg',
      resolution: '480p',
      duration: 5,
      asyn: false  // 异步模式（默认）
    })
  });

  const result = await response.json();

  if (result.error) {
    console.error('创建任务失败:', result.error);
  } else {
    console.log('任务创建成功:', result.task_id);
    console.log('扣费金额:', result.deductedAmount);

    // 需要轮询查询任务状态
    pollTaskStatus(result.task_id);
  }
};

#### **同步模式调用**
```javascript
// 同步模式：等待视频生成完成
const createTaskSync = async () => {
  const response = await fetch('/api/coze/video/generate-task', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      apiKey: 'ak_your_api_key',
      model: 'doubao-seedance-1-0-lite-i2v-250428',
      prompt: '一只可爱的小猫在花园里玩耍',
      image_url: 'https://example.com/cat.jpg',
      resolution: '480p',
      duration: 5,
      asyn: true  // 同步模式
    })
  });

  const result = await response.json();

  if (result.error) {
    console.error('视频生成失败:', result.error);
  } else if (result.video_url) {
    console.log('视频生成完成:', result.video_url);
    console.log('扣费金额:', result.deductedAmount);
  } else if (result.task_id) {
    console.log('生成超时，任务ID:', result.task_id);
    // 可以继续查询任务状态
  }
};

// 查询任务状态
const queryTask = async (taskId) => {
  const response = await fetch('/api/coze/video/query-task', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      task_id: taskId
    })
  });
  
  const result = await response.json();
  
  if (result.error) {
    console.error('查询失败:', result.error);
  } else {
    console.log('任务状态:', result.status);
    if (result.video_url) {
      console.log('视频URL:', result.video_url);
    }
  }
};
```

### **Python调用示例**

#### **异步模式调用**
```python
import requests
import time

def create_video_task_async():
    """异步模式：立即返回任务ID"""
    url = 'https://www.aigcview.com/api/coze/video/generate-task'
    data = {
        'apiKey': 'ak_your_api_key',
        'model': 'doubao-seedance-1-0-lite-i2v-250428',
        'prompt': '一只可爱的小猫在花园里玩耍',
        'image_url': 'https://example.com/cat.jpg',
        'resolution': '480p',
        'duration': 5,
        'asyn': False  # 异步模式（默认）
    }

    response = requests.post(url, json=data)
    result = response.json()

    if 'error' in result:
        print(f'创建任务失败: {result["error"]}')
        return None
    else:
        print(f'任务创建成功: {result["task_id"]}')
        print(f'扣费金额: {result["deductedAmount"]}元')
        return result['task_id']

#### **同步模式调用**
```python
def create_video_task_sync():
    """同步模式：等待视频生成完成"""
    url = 'https://www.aigcview.com/api/coze/video/generate-task'
    data = {
        'apiKey': 'ak_your_api_key',
        'model': 'doubao-seedance-1-0-lite-i2v-250428',
        'prompt': '一只可爱的小猫在花园里玩耍',
        'image_url': 'https://example.com/cat.jpg',
        'resolution': '480p',
        'duration': 5,
        'asyn': True  # 同步模式
    }

    response = requests.post(url, json=data)
    result = response.json()

    if 'error' in result:
        print(f'视频生成失败: {result["error"]}')
        return None
    elif 'video_url' in result:
        print(f'视频生成完成: {result["video_url"]}')
        print(f'扣费金额: {result["deductedAmount"]}元')
        return result['video_url']
    elif 'task_id' in result:
        print(f'生成超时，任务ID: {result["task_id"]}')
        print('可以使用查询接口继续获取结果')
        return result['task_id']

# 查询任务状态
def query_video_task(task_id):
    url = 'https://www.aigcview.com/api/coze/video/query-task'
    data = {'task_id': task_id}
    
    response = requests.post(url, json=data)
    result = response.json()
    
    if 'error' in result:
        print(f'查询失败: {result["error"]}')
    else:
        print(f'任务状态: {result["status"]}')
        if 'video_url' in result:
            print(f'视频URL: {result["video_url"]}')
```

---

## 📝 **注意事项**

1. **错误判断**: 通过检查响应中是否存在 `error` 字段来判断请求是否失败
2. **asyn参数选择**:
   - 推荐使用异步模式（asyn: false），响应更快更稳定
   - 同步模式适合需要立即获得结果的场景，但可能超时
3. **状态轮询**: 异步模式下需要定期查询状态，直到任务完成或失败
4. **价格透明**: 创建任务的响应中包含详细的定价信息，便于用户了解扣费详情
5. **时间格式**: 所有时间字段都使用ISO 8601格式（UTC时间）
6. **URL有效期**: 生成的视频URL可能有有效期限制，请及时下载或保存
7. **超时处理**: 同步模式超时后会返回task_id，可继续使用查询接口获取结果

## 💡 **使用建议**

### **什么时候使用异步模式**
- ✅ 大部分场景（推荐）
- ✅ 批量处理多个视频
- ✅ 对响应时间有要求的场景
- ✅ 需要在界面上显示进度的场景

### **什么时候使用同步模式**
- ✅ 需要立即获得视频URL的场景
- ✅ 简单的一次性调用
- ✅ 不想处理轮询逻辑的场景
- ❌ 不推荐用于批量处理

这种返回格式设计既灵活又实用，可以满足不同场景的需求！
