# is-obj [![Build Status](https://travis-ci.org/sindresorhus/is-obj.svg?branch=master)](https://travis-ci.org/sindresorhus/is-obj)

> Check if a value is an object

Keep in mind that array, function, regexp, etc, are objects in JavaScript.<br>
See [`is-plain-obj`](https://github.com/sindresorhus/is-plain-obj) if you want to check for plain objects.


## Install

```
$ npm install is-obj
```


## Usage

```js
const isObject = require('is-obj');

isObject({foo: 'bar'});
//=> true

isObject([1, 2, 3]);
//=> true

isObject('foo');
//=> false
```


## Related

- [is](https://github.com/sindresorhus/is) - Type check values


## License

MIT © [Sindre Sorhus](https://sindresorhus.com)
