<template>
  <a-modal
    :title="title"
    :width="800"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel"
    cancelText="关闭">
    
    <a-spin :spinning="confirmLoading">
      <a-form-model ref="form" :model="model" :rules="validatorRules" v-bind="layout">
        <a-row>
          <a-col :span="12">
            <a-form-model-item label="推荐人ID" prop="referrerId">
              <a-input v-model="model.referrerId" placeholder="请输入推荐人ID"></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="被推荐人ID" prop="refereeId">
              <a-input v-model="model.refereeId" placeholder="请输入被推荐人ID"></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="推荐码" prop="referralCode">
              <a-input v-model="model.referralCode" placeholder="请输入推荐码">
                <a-button slot="suffix" @click="generateCode">生成</a-button>
              </a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="状态" prop="status">
              <a-select v-model="model.status" placeholder="请选择状态">
                <a-select-option :value="1">待确认</a-select-option>
                <a-select-option :value="2">已确认</a-select-option>
                <a-select-option :value="3">已奖励</a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="注册时间" prop="registerTime">
              <a-date-picker 
                v-model="model.registerTime" 
                show-time 
                format="YYYY-MM-DD HH:mm:ss"
                placeholder="请选择注册时间"
                style="width: 100%">
              </a-date-picker>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="首次充值时间" prop="firstRechargeTime">
              <a-date-picker 
                v-model="model.firstRechargeTime" 
                show-time 
                format="YYYY-MM-DD HH:mm:ss"
                placeholder="请选择首次充值时间"
                style="width: 100%">
              </a-date-picker>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="首次充值金额" prop="firstRechargeAmount">
              <a-input-number v-model="model.firstRechargeAmount" :min="0" :precision="2" placeholder="请输入首次充值金额" style="width: 100%"></a-input-number>
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </a-spin>
  </a-modal>
</template>

<script>
  import { httpAction, getAction } from '@/api/manage'
  import moment from 'moment'

  export default {
    name: 'AicgUserReferralModal',
    components: {
    },
    data () {
      return {
        layout: {
          labelCol: {
            xs: { span: 24 },
            sm: { span: 5 },
          },
          wrapperCol: {
            xs: { span: 24 },
            sm: { span: 16 },
          },
        },
        title:"操作",
        visible: false,
        model: {},
        confirmLoading: false,
        form: this.$form.createForm(this),
        validatorRules: {
          referrerId: [
            { required: true, message: '请输入推荐人ID!' },
          ],
          refereeId: [
            { required: true, message: '请输入被推荐人ID!' },
          ],
          referralCode: [
            { required: true, message: '请输入推荐码!' },
          ],
          registerTime: [
            { required: true, message: '请选择注册时间!' },
          ],
        },
        url: {
          add: "/demo/referral/add",
          edit: "/demo/referral/edit",
          queryById: "/demo/referral/queryById"
        }
      }
    },
    methods: {
      add () {
        this.edit({});
      },
      edit (record) {
        this.form.resetFields();
        this.model = Object.assign({}, record);
        this.visible = true;
        this.$nextTick(() => {
          const formData = {
            referrerId: this.model.referrerId,
            refereeId: this.model.refereeId,
            referralCode: this.model.referralCode,
            status: this.model.status,
            firstRechargeAmount: this.model.firstRechargeAmount
          }
          this.form.setFieldsValue(formData)
          if(this.model.registerTime) {
            this.model.registerTime = moment(this.model.registerTime)
          }
          if(this.model.firstRechargeTime) {
            this.model.firstRechargeTime = moment(this.model.firstRechargeTime)
          }
        })
        if(record.id){
          this.title = "编辑";
        }else{
          this.title = "新增";
          this.model.registerTime = moment()
          this.model.status = 1
        }
      },
      close () {
        this.$emit('close');
        this.visible = false;
      },
      handleOk () {
        const that = this;
        // 触发表单验证
        this.$refs.form.validate(valid => {
          if(valid) {
            that.confirmLoading = true;
            let httpurl = '';
            let method = '';
            if(!this.model.id){
              httpurl+=this.url.add;
              method = 'post';
            }else{
              httpurl+=this.url.edit;
              method = 'put';
            }
            let formData = Object.assign(this.model);
            if(formData.registerTime) {
              formData.registerTime = formData.registerTime.format('YYYY-MM-DD HH:mm:ss')
            }
            if(formData.firstRechargeTime) {
              formData.firstRechargeTime = formData.firstRechargeTime.format('YYYY-MM-DD HH:mm:ss')
            }
            httpAction(httpurl,formData,method).then((res)=>{
              if(res.success){
                that.$message.success(res.message);
                that.$emit('ok');
              }else{
                that.$message.warning(res.message);
              }
            }).finally(() => {
              that.confirmLoading = false;
            })
          }
        })
      },
      handleCancel () {
        this.close()
      },
      generateCode() {
        if (!this.model.referrerId) {
          this.$message.warning('请先输入推荐人ID')
          return
        }
        getAction(`/demo/referral/generateCode?userId=${this.model.referrerId}`).then(res => {
          if (res.success) {
            this.model.referralCode = res.result
            this.$message.success('推荐码生成成功')
          } else {
            this.$message.error(res.message)
          }
        })
      }
    }
  }
</script>
