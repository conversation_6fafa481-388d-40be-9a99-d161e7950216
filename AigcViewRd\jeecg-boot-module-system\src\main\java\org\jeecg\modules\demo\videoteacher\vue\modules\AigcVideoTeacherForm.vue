<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form-model ref="form" :model="model" :rules="validatorRules" slot="detail">
        <a-row>
          <a-col :span="24">
            <a-form-model-item label="讲师名" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="teachername">
              <a-input v-model="model.teachername" placeholder="请输入讲师名"  ></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="讲师介绍" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="teacherinfo">
              <a-textarea v-model="model.teacherinfo" rows="4" placeholder="请输入讲师介绍" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="学习人数" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="studyperson">
              <a-input-number v-model="model.studyperson" placeholder="请输入学习人数" style="width: 100%" />
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="课程数量" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="coursenum">
              <a-input-number v-model="model.coursenum" placeholder="请输入课程数量" style="width: 100%" />
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </j-form-container>
  </a-spin>
</template>

<script>

  import { httpAction, getAction } from '@/api/manage'
  import { validateDuplicateValue } from '@/utils/util'

  export default {
    name: 'AigcVideoTeacherForm',
    components: {
    },
    props: {
      //表单禁用
      disabled: {
        type: Boolean,
        default: false,
        required: false
      }
    },
    data () {
      return {
        model:{
         },
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 },
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 },
        },
        confirmLoading: false,
        validatorRules: {
           teachername: [
              { required: true, message: '请输入讲师名!'},
           ],
           teacherinfo: [
              { required: true, message: '请输入讲师介绍!'},
           ],
        },
        url: {
          add: "/videoteacher/aigcVideoTeacher/add",
          edit: "/videoteacher/aigcVideoTeacher/edit",
          queryById: "/videoteacher/aigcVideoTeacher/queryById"
        }
      }
    },
    computed: {
      formDisabled(){
        return this.disabled
      },
    },
    created () {
       //备份model原始值
      this.modelDefault = JSON.parse(JSON.stringify(this.model));
    },
    methods: {
      add () {
        this.edit(this.modelDefault);
      },
      edit (record) {
        this.model = Object.assign({}, record);
        this.visible = true;
      },
      submitForm () {
        const that = this;
        // 触发表单验证
        this.$refs.form.validate(valid => {
          if (valid) {
            that.confirmLoading = true;
            let httpurl = '';
            let method = '';
            if(!this.model.id){
              httpurl+=this.url.add;
              method = 'post';
            }else{
              httpurl+=this.url.edit;
               method = 'put';
            }
            httpAction(httpurl,this.model,method).then((res)=>{
              if(res.success){
                that.$message.success(res.message);
                that.$emit('ok');
              }else{
                that.$message.warning(res.message);
              }
            }).finally(() => {
              that.confirmLoading = false;
            })
          }
         
        })
      },
    }
  }
</script>