# 智界Aigc 实时监控和用户角色映射修复说明

## 🎯 修复目标

解决两个关键问题：
1. **实时监控数据反复横跳** - 剩余配额和QPS数据使用随机数导致的问题
2. **用户角色映射缺失** - 前端获取不到用户角色，无法正确映射会员等级

## 🔍 问题分析

### 1. 实时监控问题

#### **问题现象**
- 剩余配额数值反复横跳，每次刷新都是随机数
- QPS数据也是随机生成，不反映真实使用情况
- 用户体验差，数据不可信

#### **问题根源**
```java
// 原有问题代码
int usedToday = (int)(Math.random() * 100); // 模拟今日已使用
rateLimitInfo.put("remainingQuota", 1000 - usedToday);

int currentQPS = (int)(Math.random() * 30) + 5; // 5-35之间
realTimeStats.put("currentQPS", currentQPS);
```

### 2. 用户角色映射问题

#### **问题现象**
- 前端Login.vue调用`/sys/user/getCurrentUserDeparts`接口获取role
- 后端接口没有返回role字段
- 仪表板显示的会员等级始终是默认值

#### **问题根源**
```java
// 原有getCurrentUserDeparts接口
map.put("list", list);
map.put("orgCode", sysUser.getOrgCode());
// 缺少role字段
```

## 🔧 修复方案

### 1. 实时监控数据真实化

#### **修复剩余配额计算**
```java
// 修复后：基于真实数据计算
LocalDateTime todayStart = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0);
QueryWrapper<AicgUserRecord> todayUsageQuery = new QueryWrapper<>();
todayUsageQuery.eq("user_id", userId).eq("record_type", "消费");
todayUsageQuery.ge("create_time", todayStart);
int usedToday = userRecordService.count(todayUsageQuery);

int dailyLimit = aigcApiConfig.getMaxRequestsPerDay(memberLevel);
rateLimitInfo.put("remainingQuota", Math.max(0, dailyLimit - usedToday));
```

#### **修复QPS计算**
```java
// 修复后：基于最近1分钟真实调用计算
LocalDateTime oneMinuteAgo = LocalDateTime.now().minusMinutes(1);
QueryWrapper<AicgUserRecord> recentQuery = new QueryWrapper<>();
recentQuery.eq("user_id", userId).eq("record_type", "消费");
recentQuery.ge("create_time", oneMinuteAgo);
int recentCalls = userRecordService.count(recentQuery);

// 实际QPS = 最近1分钟调用次数 / 60秒
double currentQPS = recentCalls / 60.0;
realTimeStats.put("currentQPS", Math.round(currentQPS * 100.0) / 100.0);
```

### 2. 用户角色映射实现

#### **修复getCurrentUserDeparts接口**
```java
// 修复后：添加用户角色信息
LoginUser sysUser = (LoginUser)SecurityUtils.getSubject().getPrincipal();
List<SysDepart> list = this.sysDepartService.queryUserDeparts(sysUser.getId());

// 获取用户角色信息
List<String> roles = this.sysUserService.getRole(sysUser.getUsername());
String roleCode = null;
if (roles != null && !roles.isEmpty()) {
    roleCode = roles.get(0); // 取第一个角色作为主要角色
}

Map<String,Object> map = new HashMap<String,Object>();
map.put("list", list);
map.put("orgCode", sysUser.getOrgCode());
map.put("role", roleCode); // 添加角色信息

// 如果有部门信息，添加部门ID
if (list != null && !list.isEmpty()) {
    map.put("departId", list.get(0).getId());
}
```

#### **实现角色到会员等级映射**
```java
/**
 * 根据用户角色映射会员等级
 * @param username 用户名
 * @return 会员等级 (1-普通用户, 2-VIP用户, 3-SVIP用户)
 */
private int getMemberLevelByUserRole(String username) {
    try {
        // 获取用户角色
        List<String> roles = sysUserService.getRole(username);
        if (roles == null || roles.isEmpty()) {
            return 1; // 默认普通用户
        }
        
        String roleCode = roles.get(0); // 取第一个角色
        
        // 角色映射到会员等级
        switch (roleCode.toLowerCase()) {
            case "svip":
            case "super_vip":
            case "admin":
                return 3; // SVIP用户
            case "vip":
            case "premium":
                return 2; // VIP用户
            case "user":
            case "normal":
            default:
                return 1; // 普通用户
        }
    } catch (Exception e) {
        log.error("获取用户角色映射会员等级异常: {}", e.getMessage(), e);
        return 1; // 异常时返回普通用户
    }
}
```

### 3. 前端优化

#### **调整刷新频率**
```javascript
// 修复前：30秒刷新，过于频繁
}, 30000) // 每30秒刷新一次数据

// 修复后：60秒刷新，更合理
}, 60000) // 每60秒刷新一次数据，避免频繁请求
```

## 📊 修复效果

### 1. 实时监控数据稳定

#### **剩余配额**
- ✅ **修复前**: 随机数，反复横跳
- ✅ **修复后**: 基于真实消费记录计算，数据稳定可信

#### **QPS数据**
- ✅ **修复前**: 随机生成，不反映真实情况
- ✅ **修复后**: 基于最近1分钟真实调用计算

### 2. 用户角色正确映射

#### **角色获取**
- ✅ **修复前**: 前端获取不到role字段
- ✅ **修复后**: 后端正确返回用户角色信息

#### **会员等级映射**
- ✅ **修复前**: 始终显示默认等级
- ✅ **修复后**: 根据用户角色正确映射会员等级

```
角色映射规则：
- admin/svip/super_vip → SVIP用户 (等级3)
- vip/premium → VIP用户 (等级2)  
- user/normal/其他 → 普通用户 (等级1)
```

## 🔄 数据流程

### 修复后的完整流程

```
用户登录
    ↓
前端调用 /sys/user/getCurrentUserDeparts
    ↓
后端返回用户部门和角色信息
    ↓
前端存储role到localStorage
    ↓
访问仪表板页面
    ↓
调用 /api/aigc/dashboard-data
    ↓
后端根据用户角色映射会员等级
    ↓
查询真实消费记录计算剩余配额
    ↓
计算最近1分钟调用次数得出QPS
    ↓
返回真实可信的仪表板数据
    ↓
前端显示稳定的监控数据
    ↓
每60秒刷新一次数据
```

## 📁 修改的文件

### 后端文件
```
AigcViewRd/jeecg-boot-module-system/src/main/java/org/jeecg/modules/
├── system/controller/SysUserController.java     # 修复getCurrentUserDeparts接口
└── api/controller/AigcApiController.java        # 修复实时监控数据计算
```

### 前端文件
```
AigcViewFe/智界Aigc/src/views/dashboard/Analysis.vue  # 调整刷新频率
```

## ✅ 验证结果

### 1. 实时监控验证
- ✅ 剩余配额不再反复横跳
- ✅ QPS数据反映真实使用情况
- ✅ 数据更新频率合理（60秒）

### 2. 用户角色验证
- ✅ 前端能正确获取用户角色
- ✅ 会员等级正确映射
- ✅ 不同角色用户看到对应的限制配额

### 3. 性能验证
- ✅ 减少了随机数生成的CPU消耗
- ✅ 基于数据库查询，数据准确可靠
- ✅ 60秒刷新频率减少了服务器压力

## 🎯 核心改进

### 1. 数据真实性
- **剩余配额**: 从随机数改为基于真实消费记录计算
- **QPS统计**: 从随机数改为基于最近1分钟真实调用计算
- **会员等级**: 从固定值改为基于用户角色动态映射

### 2. 用户体验
- **数据稳定**: 监控数据不再反复横跳
- **信息准确**: 显示真实的API使用情况
- **响应合理**: 60秒刷新频率平衡了实时性和性能

### 3. 系统架构
- **角色映射**: 建立了用户角色到会员等级的映射机制
- **数据一致**: 前后端数据完全对接
- **扩展性**: 支持更多角色类型的扩展

## 🚀 后续优化建议

### 1. 缓存优化
- 对用户角色信息进行缓存，减少数据库查询
- 对统计数据进行短期缓存，提高响应速度

### 2. 监控增强
- 添加API调用成功率统计
- 增加错误类型分析
- 实现更精细的QPS监控

### 3. 角色管理
- 支持用户多角色
- 实现角色权重计算
- 添加角色变更日志

---

**修复版本**: V2.1  
**修复时间**: 2025-06-14  
**修复内容**: 实时监控数据真实化 + 用户角色映射  
**开发团队**: 智界Aigc开发组
