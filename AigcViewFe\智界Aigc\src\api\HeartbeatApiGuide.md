# 🔄 心跳API调用封装完整指南

## 📋 概述

本指南详细说明了心跳API调用封装的实现和使用方法，包括增强的错误处理、重试机制、缓存优化和性能监控。

## 🎯 核心特性

### ✅ 已实现功能

1. **🔄 智能心跳发送**
   - 自动重试机制（指数退避）
   - 响应缓存优化
   - 网络状态检测
   - 页面可见性控制

2. **📊 批量操作支持**
   - 并发控制（默认5个并发）
   - 批量用户状态查询
   - 批量数据获取优化

3. **🔧 标准化API封装**
   - 统一错误处理
   - 标准响应格式
   - 参数验证
   - 性能监控

4. **⚙️ 配置管理**
   - 环境特定配置
   - 页面类型配置
   - API密钥管理
   - 错误码映射

5. **🧪 测试工具**
   - 完整测试套件
   - 性能基准测试
   - 单个API测试
   - 测试报告生成

## 🚀 使用方法

### 1. 基础心跳发送

```javascript
import { HeartbeatApi } from '@/api/heartbeat'

// 简单心跳发送
const response = await HeartbeatApi.send({
  pageType: 'home',
  apiKey: 'your-api-key'
})

if (response.success) {
  console.log('心跳发送成功:', response.result)
} else {
  console.error('心跳发送失败:', response.message)
}
```

### 2. 智能心跳（推荐）

```javascript
// 带缓存和重试的智能心跳
const response = await HeartbeatApi.smart({
  pageType: 'market',
  apiKey: 'your-api-key'
}, {
  enableCache: true,        // 启用缓存
  enableRetry: true,        // 启用重试
  retryOptions: {
    maxRetries: 3,          // 最大重试3次
    retryDelay: 1000,       // 重试延迟1秒
    backoffMultiplier: 2    // 指数退避倍数
  }
})
```

### 3. 批量操作

```javascript
// 批量获取用户在线状态
const userIds = ['user1', 'user2', 'user3']
const response = await HeartbeatApi.batchStatus(userIds, {
  includeDetails: true
})

console.log('批量查询结果:', response.result)
```

### 4. 统计数据获取

```javascript
// 获取实时统计数据
const statsResponse = await HeartbeatApi.getRealTime('your-api-key', {
  timeRange: '1h',
  metrics: ['online_users', 'api_calls', 'active_sessions']
})

// 获取用户活跃度信息
const activityResponse = await HeartbeatApi.getActivity('user123', {
  includeScore: true,
  timeRange: '7d'
})
```

### 5. 配置管理

```javascript
import { getApiConfig, getApiKey } from '@/api/heartbeatConfig'

// 获取页面特定配置
const config = getApiConfig('home', {
  enableDebugLog: true,
  customTimeout: 5000
})

// 获取页面API密钥
const apiKey = getApiKey('market')
```

## 🔧 API方法详解

### 核心方法

| 方法名 | 功能 | 参数 | 返回值 |
|--------|------|------|--------|
| `send()` | 基础心跳发送 | heartbeatData, options | Promise\<Response\> |
| `sendWithRetry()` | 带重试的心跳发送 | heartbeatData, retryOptions | Promise\<Response\> |
| `smart()` | 智能心跳（推荐） | heartbeatData, options | Promise\<Response\> |

### 状态查询方法

| 方法名 | 功能 | 参数 | 返回值 |
|--------|------|------|--------|
| `getUserStatus()` | 获取用户在线状态 | userId, options | Promise\<Response\> |
| `getStats()` | 获取在线用户统计 | options | Promise\<Response\> |
| `getActivity()` | 获取用户活跃度 | userId, options | Promise\<Response\> |

### 批量操作方法

| 方法名 | 功能 | 参数 | 返回值 |
|--------|------|------|--------|
| `batchStatus()` | 批量获取用户状态 | userIds, options | Promise\<Response\> |
| `batchFetch()` | 批量数据获取 | requests, options | Promise\<Response\> |

### 配置和监控方法

| 方法名 | 功能 | 参数 | 返回值 |
|--------|------|------|--------|
| `getConfig()` | 获取心跳配置 | pageType, options | Promise\<Response\> |
| `checkStatus()` | 检查心跳状态 | sessionId | Promise\<Response\> |
| `getHistory()` | 获取心跳历史 | params | Promise\<Response\> |
| `getMetrics()` | 获取性能指标 | params | Promise\<Response\> |

## 📊 响应格式

### 标准成功响应

```javascript
{
  "success": true,
  "message": "操作成功",
  "code": 200,
  "result": {
    // 实际数据内容
  },
  "timestamp": 1640995200000
}
```

### 标准错误响应

```javascript
{
  "success": false,
  "message": "错误描述",
  "code": 500,
  "result": null,
  "timestamp": 1640995200000,
  "error": {
    "type": "Error",
    "stack": "..." // 仅开发环境
  }
}
```

## ⚙️ 配置选项

### 页面类型配置

```javascript
const PAGE_TYPE_CONFIG = {
  home: {
    interval: 20000,      // 20秒间隔
    priority: 'high',     // 高优先级
    enableCache: true,
    cacheTTL: 15000      // 15秒缓存
  },
  market: {
    interval: 30000,      // 30秒间隔
    priority: 'medium',   // 中优先级
    enableCache: true,
    cacheTTL: 30000      // 30秒缓存
  },
  admin: {
    interval: 15000,      // 15秒间隔
    priority: 'highest',  // 最高优先级
    enableCache: false,   // 禁用缓存
    cacheTTL: 0
  }
}
```

### 环境配置

```javascript
// 开发环境
{
  enableDebugLog: true,
  enableCache: false,
  retryAttempts: 1,
  timeout: 10000
}

// 生产环境
{
  enableDebugLog: false,
  enableCache: true,
  retryAttempts: 3,
  timeout: 30000
}
```

## 🧪 测试工具使用

### 快速测试

```javascript
import { quickTest } from '@/api/heartbeatTester'

// 运行完整测试套件
const { result, report } = await quickTest({
  enableLog: true,
  enablePerformanceTest: true
})

console.log('测试结果:', result)
console.log('测试报告:', report)
```

### 单个API测试

```javascript
import { testSingleApi } from '@/api/heartbeatTester'

// 测试特定API方法
const result = await testSingleApi('send', {
  pageType: 'home',
  apiKey: 'test-key'
})

console.log('测试结果:', result)
```

### 性能基准测试

```javascript
import { HeartbeatTester } from '@/api/heartbeatTester'

const tester = new HeartbeatTester({
  enablePerformanceTest: true
})

const result = await tester.runFullTestSuite()
const report = tester.getTestReport()

console.log('性能报告:', report.performanceMetrics)
```

## 🔍 错误处理

### 可重试错误

- `NETWORK_ERROR` - 网络连接失败
- `TIMEOUT` - 请求超时
- `SERVER_ERROR_500` - 服务器内部错误
- `BAD_GATEWAY_502` - 网关错误
- `SERVICE_UNAVAILABLE_503` - 服务不可用
- `GATEWAY_TIMEOUT_504` - 网关超时

### 不可重试错误

- `BAD_REQUEST_400` - 请求参数错误
- `UNAUTHORIZED_401` - 未授权访问
- `FORBIDDEN_403` - 访问被禁止
- `NOT_FOUND_404` - 资源不存在

## 🚀 性能优化

### 1. 缓存策略

```javascript
// 启用缓存（推荐用于非实时数据）
const response = await HeartbeatApi.smart(data, {
  enableCache: true,
  cacheTTL: 30000  // 30秒缓存
})
```

### 2. 并发控制

```javascript
// 批量操作并发控制
const response = await HeartbeatApi.batchFetch(requests, {
  concurrency: 5,  // 最多5个并发请求
  timeout: 30000   // 30秒超时
})
```

### 3. 智能重试

```javascript
// 配置重试策略
const response = await HeartbeatApi.sendWithRetry(data, {
  maxRetries: 3,
  retryDelay: 1000,
  backoffMultiplier: 2
})
```

## 📈 监控和调试

### 1. 性能监控

```javascript
// 获取性能指标
const metrics = await HeartbeatApi.getMetrics({
  timeRange: '1h',
  metrics: ['response_time', 'success_rate', 'error_rate']
})
```

### 2. 调试日志

```javascript
// 开发环境启用调试日志
const config = getApiConfig('home', {
  enableDebugLog: true,
  enableVerboseLog: true
})
```

### 3. 缓存管理

```javascript
// 清理特定缓存
HeartbeatApi.clearCache('heartbeat_cache')

// 清理所有心跳缓存
HeartbeatApi.clearCache()
```

## 🔗 相关文件

- **API封装**: `/src/api/heartbeat.js`
- **配置管理**: `/src/api/heartbeatConfig.js`
- **测试工具**: `/src/api/heartbeatTester.js`
- **Mixin组件**: `/src/mixins/HeartbeatMixin.js`
- **配置工具**: `/src/utils/heartbeatConfig.js`

## 📝 最佳实践

1. **使用智能心跳**: 优先使用 `HeartbeatApi.smart()` 方法
2. **合理配置缓存**: 根据数据实时性要求配置缓存策略
3. **错误处理**: 始终检查响应的 `success` 字段
4. **性能监控**: 定期检查性能指标和错误率
5. **测试验证**: 使用测试工具验证API功能
6. **环境适配**: 根据开发/生产环境调整配置
