import TOSBase from '../base';
export interface RenameObjectInput {
    bucket?: string;
    key: string;
    newKey: string;
    recursiveMkdir?: boolean;
    forbidOverwrite?: boolean;
    headers?: {
        [key: string]: string | undefined;
    };
}
export declare function renameObject(this: TOSBase, input: RenameObjectInput): Promise<import("../base").TosResponse<undefined>>;
export default renameObject;
