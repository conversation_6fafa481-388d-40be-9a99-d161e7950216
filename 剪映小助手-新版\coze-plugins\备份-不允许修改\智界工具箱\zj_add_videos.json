{"openapi": "3.0.0", "info": {"title": "剪映小助手_智界工具箱 - 批量添加视频", "description": "批量添加视频", "version": "1.0.0"}, "servers": [{"url": "https://www.aigcview.com"}], "paths": {"/jeecg-boot/api/jianying/add_videos": {"post": {"summary": "批量添加视频", "description": "批量添加视频", "operationId": "addVideos_zj", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"access_key": {"type": "string", "description": "访问密钥（必填）", "example": "JianyingAPI_2025_SecureAccess_AigcView", "default": "JianyingAPI_2025_SecureAccess_AigcView"}, "zj_alpha": {"type": "number", "description": "透明度，范围0-1，默认1", "minimum": 0, "maximum": 1, "example": 1.0}, "zj_draft_url": {"type": "string", "description": "草稿地址，使用createDraft_zj输出的draft_url即可（必填）", "example": "https://example.com/draft/123"}, "zj_scale_x": {"type": "number", "description": "X轴缩放", "example": 1.0}, "zj_scale_y": {"type": "number", "description": "Y轴缩放", "example": 1.0}, "zj_transform_x": {"type": "number", "description": "X轴位置", "example": 0.0}, "zj_transform_y": {"type": "number", "description": "Y轴位置", "example": 0.0}, "zj_video_infos": {"type": "string", "description": "视频信息（必填）", "example": "[{\"video_url\": \"https://example.com/video1.mp4\", \"duration\": 12000000, \"width\": 1920, \"height\": 1080, \"start\": 0, \"end\": 12000000}]"}}, "required": ["access_key", "zj_draft_url", "zj_video_infos"], "additionalProperties": false}}}}, "responses": {"200": {"description": "成功添加视频", "content": {"application/json": {"schema": {"type": "object", "properties": {"video_ids": {"type": "array", "description": "视频材料ID列表", "items": {"type": "string"}}, "draft_url": {"type": "string", "description": "更新后的草稿地址"}, "segment_ids": {"type": "array", "description": "视频段ID列表", "items": {"type": "string"}}, "track_id": {"type": "string", "description": "视频轨道ID"}}, "required": ["video_ids", "draft_url", "segment_ids", "track_id"]}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "description": "错误信息", "example": "批量添加视频失败: 视频信息不能为空"}}, "required": ["error"]}}}}, "500": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "description": "错误信息", "example": "批量添加视频失败: 服务器内部错误"}}, "required": ["error"]}}}}}}}}}