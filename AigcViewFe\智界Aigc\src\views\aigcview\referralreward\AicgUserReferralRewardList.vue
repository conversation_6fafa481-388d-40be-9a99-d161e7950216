<template>
  <a-card :bordered="false">
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline" @keyup.enter.native="searchQuery">
        <a-row :gutter="24">
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="推荐人ID">
              <a-input placeholder="请输入推荐人ID" v-model="queryParam.referrerId"></a-input>
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="奖励类型">
              <a-select placeholder="请选择奖励类型" v-model="queryParam.rewardType" allowClear>
                <a-select-option :value="1">注册奖励</a-select-option>
                <a-select-option :value="2">首充奖励</a-select-option>
                <a-select-option :value="3">升级奖励</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="状态">
              <a-select placeholder="请选择状态" v-model="queryParam.status" allowClear>
                <a-select-option :value="1">待发放</a-select-option>
                <a-select-option :value="2">已发放</a-select-option>
                <a-select-option :value="3">已取消</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <span class="table-page-search-submitButtons">
              <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
              <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    
    <!-- 操作按钮区域 -->
    <div class="table-operator">
      <a-button @click="handleAdd" type="primary" icon="plus">新增</a-button>
      <a-button type="primary" icon="download" @click="handleExportXls('推荐奖励记录')">导出</a-button>
      <a-button type="primary" icon="check" @click="batchPay" v-if="selectedRowKeys.length > 0">批量发放</a-button>
      <a-dropdown v-if="selectedRowKeys.length > 0">
        <a-menu slot="overlay">
          <a-menu-item key="1" @click="batchDel"><a-icon type="delete"/>删除</a-menu-item>
        </a-menu>
        <a-button style="margin-left: 8px"> 批量操作 <a-icon type="down" /></a-button>
      </a-dropdown>
    </div>

    <!-- table区域-begin -->
    <div>
      <div class="ant-alert ant-alert-info" style="margin-bottom: 16px;">
        <i class="anticon anticon-info-circle ant-alert-icon"></i> 已选择 <a style="font-weight: 600">{{ selectedRowKeys.length }}</a>项
        <a style="margin-left: 24px" @click="onClearSelected">清空</a>
      </div>

      <a-table
        ref="table"
        size="middle"
        :scroll="{x:true}"
        bordered
        rowKey="id"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="ipagination"
        :loading="loading"
        :rowSelection="{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}"
        class="j-table-force-nowrap"
        @change="handleTableChange">

        <span slot="action" slot-scope="text, record">
          <a @click="handleEdit(record)">编辑</a>
          <a-divider type="vertical" />
          <a-dropdown>
            <a class="ant-dropdown-link">更多 <a-icon type="down" /></a>
            <a-menu slot="overlay">
              <a-menu-item>
                <a @click="handleDetail(record)">详情</a>
              </a-menu-item>
              <a-menu-item v-if="record.status === 1">
                <a @click="handlePay(record)">发放奖励</a>
              </a-menu-item>
              <a-menu-item v-if="record.status === 1">
                <a @click="handleCancel(record)">取消奖励</a>
              </a-menu-item>
              <a-menu-item>
                <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
                  <a>删除</a>
                </a-popconfirm>
              </a-menu-item>
            </a-menu>
          </a-dropdown>
        </span>

      </a-table>
    </div>

    <aicg-user-referral-reward-modal ref="modalForm" @ok="modalFormOk"></aicg-user-referral-reward-modal>
  </a-card>
</template>

<script>

  import '@/assets/less/TableExpand.less'
  import { mixinDevice } from '@/utils/mixin'
  import { JeecgListMixin } from '@/mixins/JeecgListMixin'
  import AicgUserReferralRewardModal from './modules/AicgUserReferralRewardModal.vue'

  export default {
    name: 'AicgUserReferralRewardList',
    mixins:[JeecgListMixin, mixinDevice],
    components: {
      AicgUserReferralRewardModal
    },
    data () {
      return {
        description: '推荐奖励记录管理页面',
        // 表头
        columns: [
          {
            title: '#',
            dataIndex: '',
            key:'rowIndex',
            width:60,
            align:"center",
            customRender:function (t,r,index) {
              return parseInt(index)+1;
            }
          },
          {
            title:'推荐人ID',
            align:"center",
            dataIndex: 'referrerId'
          },
          {
            title:'被推荐人ID',
            align:"center",
            dataIndex: 'refereeId'
          },
          {
            title:'奖励类型',
            align:"center",
            dataIndex: 'rewardType',
            customRender: function (text) {
              const typeMap = {1: '注册奖励', 2: '首充奖励', 3: '升级奖励'}
              return typeMap[text] || text
            }
          },
          {
            title:'奖励金额',
            align:"center",
            dataIndex: 'rewardAmount',
            customRender: function (text) {
              return text ? `¥${text}` : '-'
            }
          },
          {
            title:'触发事件',
            align:"center",
            dataIndex: 'triggerEvent'
          },
          {
            title:'状态',
            align:"center",
            dataIndex: 'status',
            customRender: function (text) {
              const statusMap = {1: '待发放', 2: '已发放', 3: '已取消'}
              const colorMap = {1: 'orange', 2: 'green', 3: 'red'}
              return `<span style="color: ${colorMap[text]}">${statusMap[text] || text}</span>`
            },
            scopedSlots: { customRender: 'htmlSlot' }
          },
          {
            title:'发放时间',
            align:"center",
            dataIndex: 'rewardTime'
          },
          {
            title: '操作',
            dataIndex: 'action',
            align:"center",
            fixed:"right",
            width:147,
            scopedSlots: { customRender: 'action' }
          }
        ],
        url: {
          list: "/demo/referralreward/list",
          delete: "/demo/referralreward/delete",
          deleteBatch: "/demo/referralreward/deleteBatch",
          exportXlsUrl: "/demo/referralreward/exportXls",
          importExcelUrl: "demo/referralreward/importExcel",
        },
        dictOptions:{},
        superFieldList:[],
      }
    },
    created() {
      this.getSuperFieldList();
    },
    computed: {
      importExcelUrl: function(){
        return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`;
      },
    },
    methods: {
      initDictConfig(){
      },
      getSuperFieldList(){
        let fieldList=[];
        fieldList.push({type:'string',value:'referrerId',text:'推荐人ID'})
        fieldList.push({type:'string',value:'refereeId',text:'被推荐人ID'})
        fieldList.push({type:'int',value:'rewardType',text:'奖励类型'})
        fieldList.push({type:'BigDecimal',value:'rewardAmount',text:'奖励金额'})
        fieldList.push({type:'string',value:'triggerEvent',text:'触发事件'})
        fieldList.push({type:'int',value:'status',text:'状态'})
        fieldList.push({type:'Date',value:'rewardTime',text:'发放时间'})
        this.superFieldList = fieldList
      },
      handlePay(record) {
        this.$confirm({
          title: '发放奖励',
          content: `确定要发放 ¥${record.rewardAmount} 的奖励吗？`,
          onOk: () => {
            this.$http.post(`/demo/referralreward/pay?id=${record.id}`).then((res) => {
              if (res.success) {
                this.$message.success('发放成功')
                this.loadData()
              } else {
                this.$message.error(res.message)
              }
            })
          }
        })
      },
      handleCancel(record) {
        this.$confirm({
          title: '取消奖励',
          content: '确定要取消该奖励吗？',
          onOk: () => {
            this.$http.post(`/demo/referralreward/cancel?id=${record.id}`).then((res) => {
              if (res.success) {
                this.$message.success('取消成功')
                this.loadData()
              } else {
                this.$message.error(res.message)
              }
            })
          }
        })
      },
      batchPay() {
        if (this.selectedRowKeys.length === 0) {
          this.$message.warning('请选择要发放的奖励')
          return
        }
        this.$confirm({
          title: '批量发放奖励',
          content: `确定要发放选中的 ${this.selectedRowKeys.length} 条奖励吗？`,
          onOk: () => {
            this.$http.post(`/demo/referralreward/batchPay?ids=${this.selectedRowKeys.join(',')}`).then((res) => {
              if (res.success) {
                this.$message.success(res.message)
                this.loadData()
                this.onClearSelected()
              } else {
                this.$message.error(res.message)
              }
            })
          }
        })
      }
    }
  }
</script>
<style scoped>
  @import '~@assets/less/common.less';
</style>
