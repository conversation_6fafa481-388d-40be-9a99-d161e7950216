# AigcView TOS迁移测试计划

## 🎯 测试目标
验证文件存储从本地迁移到火山引擎TOS（私有访问）的功能完整性

## 📋 测试环境
- **后端**：http://localhost:8080/jeecg-boot
- **前端**：http://localhost:3000
- **TOS桶**：aigcview-plub（私有访问）
- **存储类型**：tos

## 🧪 测试用例

### **1. 插件商城图片上传测试**
**测试路径**：`/aigcview/plubshop`

**测试步骤**：
1. 访问插件商城管理页面
2. 点击"新增"按钮
3. 上传插件图片（支持多图）
4. 填写其他必填信息
5. 保存插件

**预期结果**：
- ✅ 图片上传成功
- ✅ 返回的文件路径格式：`uploads/temp/2025/01/11/1736582400000_a1b2c3d4.jpg`
- ✅ 图片在列表页正常显示
- ✅ 图片点击可正常预览

**验证方法**：
```bash
# 检查数据库中的文件路径
SELECT plubimg FROM aigc_plub_shop WHERE id = '最新插件ID';

# 检查TOS中的文件
# 登录火山引擎控制台 -> TOS -> aigcview-plub桶 -> uploads/temp/目录
```

### **2. 用户头像上传测试**
**测试路径**：用户中心 -> 个人信息

**测试步骤**：
1. 登录系统
2. 进入用户中心
3. 点击头像上传
4. 选择图片文件
5. 保存用户信息

**预期结果**：
- ✅ 头像上传成功
- ✅ 头像在页面正常显示
- ✅ 文件路径格式：`uploads/avatar/2025/01/11/xxx.jpg`

### **3. 富文本编辑器图片上传测试**
**测试路径**：插件详细内容编辑

**测试步骤**：
1. 编辑插件详细内容
2. 在富文本编辑器中插入图片
3. 保存内容

**预期结果**：
- ✅ 图片上传成功
- ✅ 富文本中图片正常显示
- ✅ 保存后图片链接有效

### **4. 文件访问权限测试**
**测试目标**：验证私有访问和签名URL

**测试步骤**：
1. 获取上传后的文件URL
2. 直接访问TOS原始URL（应该失败）
3. 通过系统代理访问（应该成功）

**预期结果**：
- ❌ 直接访问TOS URL失败（403 Forbidden）
- ✅ 通过 `/sys/common/static/uploads/xxx` 访问成功
- ✅ 签名URL有效期24小时

## 🔧 性能测试

### **1. 上传性能测试**
```bash
# 测试大文件上传（接近10MB限制）
# 测试并发上传（多个用户同时上传）
# 测试不同文件类型（jpg, png, pdf等）
```

### **2. 访问性能测试**
```bash
# 测试图片加载速度
# 测试签名URL生成速度
# 测试缓存效果
```

## 🚨 错误场景测试

### **1. TOS服务异常测试**
- 模拟TOS服务不可用
- 验证错误处理和用户提示

### **2. 文件类型限制测试**
- 上传不支持的文件类型
- 验证文件类型过滤

### **3. 文件大小限制测试**
- 上传超过10MB的文件
- 验证大小限制提示

## 📊 验证检查点

### **1. 数据库检查**
```sql
-- 检查新上传文件的路径格式
SELECT * FROM aigc_plub_shop WHERE plubimg LIKE 'uploads/%' ORDER BY create_time DESC LIMIT 10;

-- 检查用户头像路径
SELECT username, avatar FROM sys_user WHERE avatar LIKE 'uploads/%' ORDER BY update_time DESC LIMIT 10;
```

### **2. TOS控制台检查**
- 登录火山引擎控制台
- 查看 aigcview-plub 桶
- 确认 uploads/ 目录下有新文件
- 检查文件权限设置（私有）

### **3. 日志检查**
```bash
# 检查上传日志
grep "上传通用文件到TOS" logs/jeecgboot-*.log

# 检查签名URL生成日志
grep "生成签名URL" logs/jeecgboot-*.log

# 检查错误日志
grep "TOS" logs/jeecgboot-*.log | grep -i error
```

## 🔄 回滚测试

### **1. 快速回滚验证**
```yaml
# 修改配置文件
jeecg:
  uploadType: local  # 改回 local

# 重启服务
# 验证新上传文件恢复到本地存储
# 验证已有TOS文件仍可正常访问
```

## ✅ 测试通过标准

### **功能完整性**
- [ ] 所有上传功能正常工作
- [ ] 所有文件访问正常
- [ ] 图片预览功能正常
- [ ] 富文本编辑器图片正常

### **性能标准**
- [ ] 上传速度 < 5秒（1MB文件）
- [ ] 图片加载速度 < 2秒
- [ ] 签名URL生成 < 100ms

### **安全性**
- [ ] 直接访问TOS URL被拒绝
- [ ] 签名URL正常工作
- [ ] 文件类型过滤有效

### **稳定性**
- [ ] 并发上传无异常
- [ ] 错误处理正确
- [ ] 日志记录完整

## 📝 测试报告模板

```
测试时间：2025-01-11
测试人员：[姓名]
测试环境：生产环境

功能测试结果：
✅ 插件商城图片上传：通过
✅ 用户头像上传：通过
✅ 富文本图片上传：通过
✅ 文件访问权限：通过

性能测试结果：
- 平均上传时间：[X]秒
- 平均访问时间：[X]秒
- 并发测试：[结果]

发现问题：
1. [问题描述]
2. [问题描述]

建议：
1. [建议内容]
2. [建议内容]
```
