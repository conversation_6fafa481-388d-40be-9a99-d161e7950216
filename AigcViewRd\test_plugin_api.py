#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智界Aigc API插件验证功能测试脚本
测试API-Key验证接口的插件调用功能
"""

import requests
import json

# 配置
BASE_URL = "http://localhost:8080"
API_KEY = "ak_afc0fd5b3fbf4265af92280630b37b91"  # 测试用的API-Key

def test_api_key_only():
    """测试仅验证API-Key（不调用插件）"""
    print("=== 测试1: 仅验证API-Key ===")
    
    url = f"{BASE_URL}/api/aigc/verify-apikey"
    data = {
        "apiKey": API_KEY
    }
    
    try:
        response = requests.post(url, data=data)
        result = response.json()
        
        print(f"状态码: {response.status_code}")
        print(f"响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
        
        if result.get("success"):
            print("✅ API-Key验证成功")
            return True
        else:
            print("❌ API-Key验证失败")
            return False
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

def test_plugin_call_success():
    """测试插件调用成功（余额充足）"""
    print("\n=== 测试2: 插件调用成功 ===")

    # 先查询创作者当前使用总数
    print("查询插件调用前的创作者使用总数...")
    author_usage_before = get_author_usage_count("1933808003163283458")  # AigcView王的ID
    print(f"调用前创作者使用总数: {author_usage_before}")

    url = f"{BASE_URL}/api/aigc/verify-apikey"
    data = {
        "apiKey": API_KEY,
        "pluginKey": "xiaohongshufabu"
    }

    try:
        response = requests.post(url, data=data)
        result = response.json()

        print(f"状态码: {response.status_code}")
        print(f"响应: {json.dumps(result, indent=2, ensure_ascii=False)}")

        if result.get("success"):
            print("✅ 插件调用验证成功")
            plugin_result = result.get("result", {})
            if plugin_result.get("pluginVerified"):
                print(f"   插件名称: {plugin_result.get('pluginName')}")
                print(f"   扣除点数: {plugin_result.get('deductedPoints')}")
                print(f"   余额变化: {plugin_result.get('balance')} -> {plugin_result.get('balanceAfter')}")

                # 验证创作者使用总数是否增加
                print("验证创作者使用总数是否更新...")
                import time
                time.sleep(1)  # 等待数据库更新
                author_usage_after = get_author_usage_count("1933808003163283458")
                print(f"调用后创作者使用总数: {author_usage_after}")

                if author_usage_after == author_usage_before + 1:
                    print("✅ 创作者使用总数正确更新")
                else:
                    print("❌ 创作者使用总数未正确更新")

            return True
        else:
            print("❌ 插件调用验证失败")
            return False

    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

def get_author_usage_count(author_id):
    """获取创作者的使用总数（这里模拟数据库查询）"""
    # 在实际环境中，这里应该是数据库查询
    # 为了测试，我们返回一个模拟值
    # 实际应用中可以通过管理接口或直接数据库查询获取
    try:
        # 这里可以添加实际的数据库查询逻辑
        # 或者调用管理接口获取创作者信息
        return 0  # 模拟返回值
    except:
        return 0

def test_plugin_not_exist():
    """测试插件不存在的情况"""
    print("\n=== 测试3: 插件不存在 ===")
    
    url = f"{BASE_URL}/api/aigc/verify-apikey"
    data = {
        "apiKey": API_KEY,
        "pluginName": "不存在的插件"
    }
    
    try:
        response = requests.post(url, data=data)
        result = response.json()
        
        print(f"状态码: {response.status_code}")
        print(f"响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
        
        if not result.get("success") and "插件不存在" in result.get("message", ""):
            print("✅ 正确处理插件不存在的情况")
            return True
        else:
            print("❌ 未正确处理插件不存在的情况")
            return False
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

def test_invalid_api_key():
    """测试无效API-Key"""
    print("\n=== 测试4: 无效API-Key ===")
    
    url = f"{BASE_URL}/api/aigc/verify-apikey"
    data = {
        "apiKey": "ak_invalid_key_123456789",
        "pluginName": "小红书发布"
    }
    
    try:
        response = requests.post(url, data=data)
        result = response.json()
        
        print(f"状态码: {response.status_code}")
        print(f"响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
        
        if not result.get("success") and "API-Key无效" in result.get("message", ""):
            print("✅ 正确处理无效API-Key")
            return True
        else:
            print("❌ 未正确处理无效API-Key")
            return False
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

def main():
    """主测试函数"""
    print("智界Aigc API插件验证功能测试")
    print("=" * 50)
    
    tests = [
        test_api_key_only,
        test_plugin_call_success,
        test_plugin_not_exist,
        test_invalid_api_key
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        if test_func():
            passed += 1
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！")
    else:
        print("⚠️  部分测试失败，请检查实现")

if __name__ == "__main__":
    main()
