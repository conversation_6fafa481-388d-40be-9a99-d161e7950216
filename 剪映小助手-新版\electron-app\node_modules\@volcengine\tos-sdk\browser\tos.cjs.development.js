'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

function _interopDefault (ex) { return (ex && (typeof ex === 'object') && 'default' in ex) ? ex['default'] : ex; }

var axios = _interopDefault(require('axios'));
var get = _interopDefault(require('lodash/get'));
var set = _interopDefault(require('lodash/set'));
var qs = _interopDefault(require('qs'));
var createDebug = _interopDefault(require('debug'));
var cryptoHmacSha256 = _interopDefault(require('crypto-js/hmac-sha256'));
var cryptoHashSha256 = _interopDefault(require('crypto-js/sha256'));
var cryptoHashMd5 = _interopDefault(require('crypto-js/md5'));
var cryptoEncBase64 = _interopDefault(require('crypto-js/enc-base64'));
var cryptoEncHex = _interopDefault(require('crypto-js/enc-hex'));
var cryptoEncUtf8 = _interopDefault(require('crypto-js/enc-utf8'));
var cloneDeep = _interopDefault(require('lodash/cloneDeep'));
var utils = _interopDefault(require('axios/lib/utils'));
var settle = _interopDefault(require('axios/lib/core/settle'));
var buildURL = _interopDefault(require('axios/lib/helpers/buildURL'));
var buildFullPath = _interopDefault(require('axios/lib/core/buildFullPath'));
var createError = _interopDefault(require('axios/lib/core/createError'));
var uniappAdapter = _interopDefault(require('axios-adapter-uniapp'));

class TosServerError extends Error {
  /**
   * is original from backend, equals `data.Code`
   */

  /**
   * the body when backend errors
   */

  /**
   * status code
   */

  /**
   * response headers
   */

  /**
   * identifies the errored request, equals to headers['x-tos-request-id'].
   * If you has any question about the request, please send the requestId and id2 to TOS worker.
   */

  /**
   * identifies the errored request, equals to headers['x-tos-id-2'].
   * If you has any question about the request, please send the requestId and id2 to TOS worker.
   */
  constructor(response) {
    const {
      data
    } = response;
    super(data.Message); // https://www.dannyguo.com/blog/how-to-fix-instanceof-not-working-for-custom-errors-in-typescript/

    this.code = void 0;
    this.data = void 0;
    this.statusCode = void 0;
    this.headers = void 0;
    this.requestId = void 0;
    this.id2 = void 0;
    Object.setPrototypeOf(this, TosServerError.prototype);
    this.data = data;
    this.code = data.Code;
    this.statusCode = response.status;
    this.headers = response.headers;
    this.requestId = response.headers['x-tos-request-id'];
    this.id2 = response.headers['x-tos-id-2'];
  }

}

(function (TosServerCode) {
  TosServerCode["NoSuchBucket"] = "NoSuchBucket";
  TosServerCode["NoSuchKey"] = "NoSuchKey";
  TosServerCode["AccessDenied"] = "AccessDenied";
  TosServerCode["MalformedAcl"] = "MalformedAclError";
  TosServerCode["UnexpectedContent"] = "UnexpectedContent";
  TosServerCode["InvalidRequest"] = "InvalidRequest";
  TosServerCode["MissingSecurityHeader"] = "MissingSecurityHeader";
  TosServerCode["InvalidArgument"] = "InvalidArgument";
  TosServerCode["EntityTooSmall"] = "EntityTooSmall";
  TosServerCode["InvalidBucketName"] = "InvalidBucketName";
  TosServerCode["BucketNotEmpty"] = "BucketNotEmpty";
  TosServerCode["TooManyBuckets"] = "TooManyBuckets";
  TosServerCode["BucketAlreadyExists"] = "BucketAlreadyExists";
  TosServerCode["MalformedBody"] = "MalformedBody";
  TosServerCode["NoSuchLifecycleConfiguration"] = "NoSuchLifecycleConfiguration";
  TosServerCode["ReplicationConfigurationNotFound"] = "ReplicationConfigurationNotFoundError";
  TosServerCode["InvalidLocationConstraint"] = "InvalidLocationConstraint";
  TosServerCode["AuthorizationQueryParametersError"] = "AuthorizationQueryParametersError";
  TosServerCode["RequestTimeTooSkewed"] = "RequestTimeTooSkewed";
  TosServerCode["SignatureDoesNotMatch"] = "SignatureDoesNotMatch";
  TosServerCode["RequestedRangeNotSatisfiable"] = "Requested Range Not Satisfiable";
  TosServerCode["PreconditionFailed"] = "PreconditionFailed";
  TosServerCode["BadDigest"] = "BadDigest";
  TosServerCode["InvalidDigest"] = "InvalidDigest";
  TosServerCode["EntityTooLarge"] = "EntityTooLarge";
  TosServerCode["UnImplemented"] = "UnImplemented";
  TosServerCode["MethodNotAllowed"] = "MethodNotAllowed";
  TosServerCode["InvalidAccessKeyId"] = "InvalidAccessKeyId";
  TosServerCode["InvalidSecurityToken"] = "InvalidSecurityToken";
  TosServerCode["ContentSHA256Mismatch"] = "ContentSHA256Mismatch";
  TosServerCode["ExceedQPSLimit"] = "ExceedQPSLimit";
  TosServerCode["ExceedRateLimit"] = "ExceedRateLimit";
  TosServerCode["NoSuchCORSConfiguration"] = "NoSuchCORSConfiguration";
  TosServerCode["NoSuchMirrorConfiguration"] = "NoSuchMirrorConfiguration";
  TosServerCode["NoSuchWebsiteConfiguration"] = "NoSuchWebsiteConfiguration";
  TosServerCode["MissingRequestBody"] = "MissingRequestBodyError";
  TosServerCode["BucketAlreadyOwnedByYou"] = "BucketAlreadyOwnedByYou";
  TosServerCode["NoSuchBucketPolicy"] = "NoSuchBucketPolicy";
  TosServerCode["PolicyTooLarge"] = "PolicyTooLarge";
  TosServerCode["MalformedPolicy"] = "MalformedPolicy";
  TosServerCode["InvalidKey"] = "InvalidKey";
  TosServerCode["MirrorFailed"] = "MirrorFailed";
  TosServerCode["Timeout"] = "Timeout";
  TosServerCode["OffsetNotMatched"] = "OffsetNotMatched";
  TosServerCode["NotAppendable"] = "NotAppendable";
  TosServerCode["ContextCanceled"] = "ContextCanceled";
  TosServerCode["InternalError"] = "InternalError";
  TosServerCode["TooManyRequests"] = "TooManyRequests";
  TosServerCode["TimeOut"] = "TimeOut";
  TosServerCode["ConcurrencyUpdateObjectLimit"] = "ConcurrencyUpdateObjectLimit";
  TosServerCode["DuplicateUpload"] = "DuplicateUpload";
  TosServerCode["DuplicateObject"] = "DuplicateObject";
  TosServerCode["InvalidVersionId"] = "InvalidVersionId";
  TosServerCode["StorageClassNotMatch"] = "StorageClassNotMatch";
  TosServerCode["UploadStatusNotUploading"] = "UploadStatusNotUploading";
  TosServerCode["PartSizeNotMatch"] = "PartSizeNotMatch";
  TosServerCode["NoUploadPart"] = "NoUploadPart";
  TosServerCode["PartsLenInvalid"] = "PartsLenInvalid";
  TosServerCode["PartsIdxSmall"] = "PartsIdxSmall";
  TosServerCode["PartSizeSmall"] = "PartSizeSmall";
  TosServerCode["PrefixNotNextKeyPrefix"] = "PrefixNotNextKeyPrefix";
  TosServerCode["InvalidPart"] = "InvalidPart";
  TosServerCode["InvalidPartOffset"] = "InvalidPartOffset";
  TosServerCode["MismatchObject"] = "MismatchObject";
  TosServerCode["UploadStatusMismatch"] = "UploadStatusMismatch";
  TosServerCode["CompletingStatusNoExpiration"] = "CompletingStatusNoExpiration";
  TosServerCode["Found"] = "Found";
  TosServerCode["InvalidRedirectLocation"] = "InvalidRedirectLocation";
})(exports.TosServerCode || (exports.TosServerCode = {}));

class TosClientError extends Error {
  constructor(message) {
    super(message); // https://www.dannyguo.com/blog/how-to-fix-instanceof-not-working-for-custom-errors-in-typescript/

    Object.setPrototypeOf(this, TosClientError.prototype);
  }

}

class CancelError extends Error {
  constructor(message) {
    super(message); // https://www.dannyguo.com/blog/how-to-fix-instanceof-not-working-for-custom-errors-in-typescript/

    Object.setPrototypeOf(this, CancelError.prototype);
  }

}

const makeArrayProp = obj => key => {
  if (obj == null || typeof obj !== 'object') {
    return;
  }

  const value = get(obj, key);

  if (!Array.isArray(value)) {
    set(obj, key, value == null ? [] : [value]);
  }
};

const makeConvertProp = convertMethod => {
  const finalMethod = target => {
    if (Array.isArray(target)) {
      return target.map(it => finalMethod(it));
    }

    if (typeof target === 'string') {
      return convertMethod(target);
    }

    if (typeof target === 'object' && target != null) {
      const ret = Object.keys(target).reduce((acc, key) => {
        const nextKey = finalMethod(key);
        acc[nextKey] = target[key];
        return acc;
      }, {});
      return ret;
    }

    return target;
  };

  return finalMethod;
};

const covertCamelCase2Kebab = /*#__PURE__*/makeConvertProp(camelCase => {
  return camelCase.replace(/[A-Z]/g, '-$&').toLowerCase();
});
const convertNormalCamelCase2Upper = /*#__PURE__*/makeConvertProp(normalCamelCase => {
  return normalCamelCase[0].toUpperCase() + normalCamelCase.slice(1);
});
const getSortedQueryString = query => {
  const searchParts = [];
  Object.keys(query).sort().forEach(key => {
    searchParts.push(`${encodeURIComponent(key)}=${encodeURIComponent(query[key])}`);
  });
  return searchParts.join('&');
};
const normalizeHeadersKey = headers => {
  const headers1 = headers || {};
  const headers2 = {};
  Object.keys(headers1).forEach(key => {
    if (headers1[key] != null) {
      headers2[key] = headers1[key];
    }
  });
  const headers3 = {};
  Object.keys(headers2).forEach(key => {
    const newKey = key.toLowerCase();
    headers3[newKey] = headers2[key];
  });
  return headers3;
};
const encodeHeadersValue = headers => {
  const header2 = {};
  Object.entries(headers).forEach(([key, value]) => {
    header2[key] = `${value}` // reference:
    //  https://stackoverflow.com/questions/38345372/why-is-length-2
    .match(/./gu).map(ch => {
      if (ch.length > 1 || ch.charCodeAt(0) >= 128) {
        return encodeURIComponent(ch);
      }

      return ch;
    }).join('');
  });
  return header2;
}; // TODO: getRegion from endpoint, maybe user passes it is better.
const getEndpoint = region => {
  return `tos-${region}.volces.com`;
};
const normalizeProxy = proxy => {
  var _proxy;

  if (typeof proxy === 'string') {
    proxy = {
      url: proxy
    };
  }

  if (proxy && ((_proxy = proxy) == null ? void 0 : _proxy.needProxyParams) == null && 'browser' === 'browser') {
    proxy.needProxyParams = true;
  }

  return proxy;
};
async function safeAwait(p) {
  try {
    const v = await p;
    return [null, v];
  } catch (err) {
    return [err, null];
  }
}
function safeSync(func) {
  try {
    const ret = func();
    return [null, ret];
  } catch (err) {
    return [err, null];
  }
}
function isBlob(obj) {
  return typeof Blob !== 'undefined' && obj instanceof Blob;
}
function isBuffer(obj) {
  return typeof Buffer !== 'undefined' && obj instanceof Buffer;
}
function obj2QueryStr(v) {
  if (!v) {
    return '';
  }

  return Object.keys(v).map(key => {
    const vStr = `${v[key]}`;
    return `${encodeURIComponent(key)}=${encodeURIComponent(vStr)}`;
  }).join('&');
}
function isCancelError(err) {
  return err instanceof CancelError;
}
const DEFAULT_PART_SIZE = 20 * 1024 * 1024; // 20 MB

const getGMTDateStr = v => {
  return v.toUTCString();
};

const gmtDateOrStr = v => {
  if (typeof v === 'string') {
    return v;
  }

  return v.toUTCString();
};

const requestHeadersMap = {
  projectName: 'x-tos-project-name',
  encodingType: 'encoding-type',
  cacheControl: 'cache-control',
  contentDisposition: 'content-disposition',
  contentLength: 'content-length',
  contentMD5: 'content-md5',
  contentSHA256: 'x-tos-content-sha256',
  contentEncoding: 'content-encoding',
  contentLanguage: 'content-language',
  contentType: 'content-type',
  expires: ['expires', getGMTDateStr],
  range: 'range',
  ifMatch: 'if-match',
  ifModifiedSince: ['if-modified-since', gmtDateOrStr],
  ifNoneMatch: 'if-none-match',
  ifUnmodifiedSince: ['if-unmodified-since', gmtDateOrStr],
  acl: 'x-tos-acl',
  grantFullControl: 'x-tos-grant-full-control',
  grantRead: 'x-tos-grant-read',
  grantReadAcp: 'x-tos-grant-read-acp',
  grantWrite: 'x-tos-grant-write',
  grantWriteAcp: 'x-tos-grant-write-acp',
  serverSideEncryption: 'x-tos-server-side-encryption',
  serverSideDataEncryption: 'x-tos-server-side-data-encryption',
  ssecAlgorithm: 'x-tos-server-side-encryption-customer-algorithm',
  ssecKey: 'x-tos-server-side-encryption-customer-key',
  ssecKeyMD5: 'x-tos-server-side-encryption-customer-key-md5',
  copySourceRange: 'x-tos-copy-source-range',
  copySourceIfMatch: 'x-tos-copy-source-if-match',
  copySourceIfModifiedSince: ['x-tos-copy-source-if-modified-since', gmtDateOrStr],
  copySourceIfNoneMatch: 'x-tos-copy-source-if-none-match',
  copySourceIfUnmodifiedSince: 'x-tos-copy-source-if-unmodified-since',
  copySourceSSECAlgorithm: 'x-tos-copy-source-server-side-encryption-customer-algorithm',
  copySourceSSECKey: 'x-tos-copy-source-server-side-encryption-customer-key',
  copySourceSSECKeyMD5: 'x-tos-copy-source-server-side-encryption-customer-key-MD5',
  metadataDirective: 'x-tos-metadata-directive',
  meta: v => {
    return Object.keys(v).reduce((prev, key) => {
      prev[`x-tos-meta-${key}`] = `${v[key]}`;
      return prev;
    }, {});
  },
  websiteRedirectLocation: 'x-tos-website-redirect-location',
  storageClass: 'x-tos-storage-class',
  azRedundancy: 'x-tos-az-redundancy',
  trafficLimit: 'x-tos-traffic-limit',
  callback: 'x-tos-callback',
  callbackVar: 'x-tos-callback-var',
  allowSameActionOverlap: ['x-tos-allow-same-action-overlap', v => String(v)],
  symLinkTargetKey: 'x-tos-symlink-target',
  symLinkTargetBucket: 'x-tos-symlink-bucket',
  forbidOverwrite: 'x-tos-forbid-overwrite',
  bucketType: 'x-tos-bucket-type',
  recursiveMkdir: 'x-tos-recursive-mkdir'
}; // type RequestHeadersMapKeys = keyof typeof requestHeadersMap;

const requestQueryMap = {
  versionId: 'versionId',
  process: 'x-tos-process',
  saveBucket: 'x-tos-save-bucket',
  saveObject: 'x-tos-save-object',
  responseCacheControl: 'response-cache-control',
  responseContentDisposition: 'response-content-disposition',
  responseContentEncoding: 'response-content-encoding',
  responseContentLanguage: 'response-content-language',
  responseContentType: 'response-content-type',
  responseExpires: ['response-expires', v => v.toUTCString()]
};
function fillRequestHeaders(v, // keys: (keyof T & RequestHeadersMapKeys)[]
keys) {
  if (!keys.length) {
    return;
  }

  const headers = v.headers || {};
  v.headers = headers;

  function setOneHeader(k, v) {
    if (headers[k] == null) {
      headers[k] = v;
    }
  }

  keys.forEach(k => {
    const confV = requestHeadersMap[k];

    if (!confV) {
      // maybe warning
      throw new TosClientError(`\`${k}\` isn't in keys of \`requestHeadersMap\``);
    }

    const oriValue = v[k];

    if (oriValue == null) {
      return;
    }

    const oriValueStr = `${oriValue}`;

    if (typeof confV === 'string') {
      return setOneHeader(confV, oriValueStr);
    }

    if (Array.isArray(confV)) {
      const newKey = confV[0];
      const newValue = confV[1](oriValue);
      return setOneHeader(newKey, newValue);
    }

    const obj = confV(oriValue);
    Object.entries(obj).forEach(([k, v]) => {
      setOneHeader(k, v);
    });
  });
}
function fillRequestQuery(v, query, keys) {
  if (!keys.length) {
    return;
  }

  function setOneKey(k, v) {
    if (query[k] == null) {
      query[k] = v;
    }
  }

  keys.forEach(k => {
    const confV = requestQueryMap[k];

    if (!confV) {
      // maybe warning
      throw new TosClientError(`\`${k}\` isn't in keys of \`requestQueryMap\``);
    }

    const oriValue = v[k];

    if (oriValue == null) {
      return;
    }

    const oriValueStr = `${oriValue}`;

    if (typeof confV === 'string') {
      return setOneKey(confV, oriValueStr);
    }

    if (Array.isArray(confV)) {
      const newKey = confV[0];
      const newValue = confV[1](oriValue);
      return setOneKey(newKey, newValue);
    }

    const obj = confV(oriValue);
    Object.entries(obj).forEach(([k, v]) => {
      setOneKey(k, v);
    });
  });
}
const paramsSerializer = params => {
  return qs.stringify(params);
};
function getNormalDataFromError(data, err) {
  return {
    data,
    statusCode: err.statusCode,
    headers: err.headers,
    requestId: err.requestId,
    id2: err.id2
  };
}
function checkCRC64WithHeaders(crc, headers) {
  const serverCRC64 = headers['x-tos-hash-crc64ecma'];

  if (serverCRC64 == null) {
    {
      console.warn("No x-tos-hash-crc64ecma in response's headers, please see https://www.volcengine.com/docs/6349/127737 to add `x-tos-hash-crc64ecma` to Expose-Headers field.");
    }

    return;
  }

  const crcStr = typeof crc === 'string' ? crc : crc.getCrc64();

  if (crcStr !== serverCRC64) {
    throw new TosClientError(`validate file crc64 failed. Expect crc64 ${serverCRC64}, actual crc64 ${crcStr}. Please try again.`);
  }
}
var HttpHeader;

(function (HttpHeader) {
  HttpHeader["LastModified"] = "last-modified";
  HttpHeader["ContentLength"] = "content-length";
  HttpHeader["AcceptEncoding"] = "accept-encoding";
  HttpHeader["ContentEncoding"] = "content-encoding";
  HttpHeader["ContentMD5"] = "content-md5";
  HttpHeader["TosRawContentLength"] = "x-tos-raw-content-length";
  HttpHeader["TosTrailer"] = "x-tos-trailer";
  HttpHeader["TosHashCrc64ecma"] = "x-tos-hash-crc64ecma";
  HttpHeader["TosContentSha256"] = "x-tos-content-sha256";
  HttpHeader["TosDecodedContentLength"] = "x-tos-decoded-content-length";
  HttpHeader["TosEc"] = "x-tos-ec";
  HttpHeader["TosRequestId"] = "x-tos-request-id";
})(HttpHeader || (HttpHeader = {}));
/**
 * make async tasks serial
 * @param makeTask
 * @returns
 */


const makeSerialAsyncTask = makeTask => {
  let lastTask = Promise.resolve();
  return async () => {
    lastTask = lastTask.then(() => makeTask());
    return lastTask;
  };
};
const tryDestroy = (stream, err) => {
  if (stream && 'destroy' in stream && typeof stream.destroy === 'function') {
    if ('destroyed' in stream && !stream.destroyed) {
      stream.destroy(err);
    }
  }
};

async function createMultipartUpload(input) {
  input = this.normalizeObjectInput(input);
  const headers = normalizeHeadersKey(input.headers);
  input.headers = headers;
  fillRequestHeaders(input, ['encodingType', 'cacheControl', 'contentDisposition', 'contentEncoding', 'contentLanguage', 'contentType', 'expires', 'acl', 'grantFullControl', 'grantRead', 'grantReadAcp', 'grantWriteAcp', 'ssecAlgorithm', 'ssecKey', 'ssecKeyMD5', 'serverSideEncryption', 'serverSideDataEncryption', 'meta', 'websiteRedirectLocation', 'storageClass', 'forbidOverwrite']);
  this.setObjectContentTypeHeader(input, headers);
  return this._fetchObject(input, 'POST', {
    uploads: ''
  }, headers, '');
}

// the last part is no size limit

const MIN_PART_SIZE_EXCEPT_LAST_ONE = 5 * 1024 * 1024;
const MAX_PART_NUMBER = 10000;
const calculateSafePartSize = (totalSize, expectPartSize, showWarning = false) => {
  let partSize = expectPartSize;

  if (expectPartSize < MIN_PART_SIZE_EXCEPT_LAST_ONE) {
    partSize = MIN_PART_SIZE_EXCEPT_LAST_ONE;

    if (showWarning) {
      console.warn(`partSize has been set to ${partSize}, because the partSize you provided is less than the minimal size of multipart`);
    }
  }

  const minSize = Math.ceil(totalSize / MAX_PART_NUMBER);

  if (expectPartSize < minSize) {
    partSize = minSize;

    if (showWarning) {
      console.warn(`partSize has been set to ${partSize}, because the partSize you provided causes the number of part excesses 10,000`);
    }
  }

  return partSize;
};
async function listParts(input) {
  const {
    bucket,
    key,
    uploadId,
    ...nextQuery
  } = input;
  const ret = await this._fetchObject(input, 'GET', {
    uploadId,
    ...covertCamelCase2Kebab(nextQuery)
  }, {});
  const arrayProp = makeArrayProp(ret.data);
  arrayProp('Parts');
  return ret;
}

// alias with GoSDK
// refer https://github.com/volcengine/ve-tos-golang-sdk/blob/main/tos/mime.go
const mimeTypes = {
  '3gp': 'video/3gpp',
  '7z': 'application/x-7z-compressed',
  abw: 'application/x-abiword',
  ai: 'application/postscript',
  aif: 'audio/x-aiff',
  aifc: 'audio/x-aiff',
  aiff: 'audio/x-aiff',
  alc: 'chemical/x-alchemy',
  amr: 'audio/amr',
  anx: 'application/annodex',
  apk: 'application/vnd.android.package-archive',
  appcache: 'text/cache-manifest',
  art: 'image/x-jg',
  asc: 'text/plain',
  asf: 'video/x-ms-asf',
  aso: 'chemical/x-ncbi-asn1-binary',
  asx: 'video/x-ms-asf',
  atom: 'application/atom+xml',
  atomcat: 'application/atomcat+xml',
  atomsrv: 'application/atomserv+xml',
  au: 'audio/basic',
  avi: 'video/x-msvideo',
  awb: 'audio/amr-wb',
  axa: 'audio/annodex',
  axv: 'video/annodex',
  b: 'chemical/x-molconn-Z',
  bak: 'application/x-trash',
  bat: 'application/x-msdos-program',
  bcpio: 'application/x-bcpio',
  bib: 'text/x-bibtex',
  bin: 'application/octet-stream',
  bmp: 'image/x-ms-bmp',
  boo: 'text/x-boo',
  book: 'application/x-maker',
  brf: 'text/plain',
  bsd: 'chemical/x-crossfire',
  c: 'text/x-csrc',
  'c++': 'text/x-c++src',
  c3d: 'chemical/x-chem3d',
  cab: 'application/x-cab',
  cac: 'chemical/x-cache',
  cache: 'chemical/x-cache',
  cap: 'application/vnd.tcpdump.pcap',
  cascii: 'chemical/x-cactvs-binary',
  cat: 'application/vnd.ms-pki.seccat',
  cbin: 'chemical/x-cactvs-binary',
  cbr: 'application/x-cbr',
  cbz: 'application/x-cbz',
  cc: 'text/x-c++src',
  cda: 'application/x-cdf',
  cdf: 'application/x-cdf',
  cdr: 'image/x-coreldraw',
  cdt: 'image/x-coreldrawtemplate',
  cdx: 'chemical/x-cdx',
  cdy: 'application/vnd.cinderella',
  cef: 'chemical/x-cxf',
  cer: 'chemical/x-cerius',
  chm: 'chemical/x-chemdraw',
  chrt: 'application/x-kchart',
  cif: 'chemical/x-cif',
  class: 'application/java-vm',
  cls: 'text/x-tex',
  cmdf: 'chemical/x-cmdf',
  cml: 'chemical/x-cml',
  cod: 'application/vnd.rim.cod',
  com: 'application/x-msdos-program',
  cpa: 'chemical/x-compass',
  cpio: 'application/x-cpio',
  cpp: 'text/x-c++src',
  cpt: 'application/mac-compactpro',
  cr2: 'image/x-canon-cr2',
  crl: 'application/x-pkcs7-crl',
  crt: 'application/x-x509-ca-cert',
  crw: 'image/x-canon-crw',
  csd: 'audio/csound',
  csf: 'chemical/x-cache-csf',
  csh: 'application/x-csh',
  csm: 'chemical/x-csml',
  csml: 'chemical/x-csml',
  css: 'text/css',
  csv: 'text/csv',
  ctab: 'chemical/x-cactvs-binary',
  ctx: 'chemical/x-ctx',
  cu: 'application/cu-seeme',
  cub: 'chemical/x-gaussian-cube',
  cxf: 'chemical/x-cxf',
  cxx: 'text/x-c++src',
  d: 'text/x-dsrc',
  davmount: 'application/davmount+xml',
  dcm: 'application/dicom',
  dcr: 'application/x-director',
  ddeb: 'application/vnd.debian.binary-package',
  dif: 'video/dv',
  diff: 'text/x-diff',
  dir: 'application/x-director',
  djv: 'image/vnd.djvu',
  djvu: 'image/vnd.djvu',
  dl: 'video/dl',
  dll: 'application/x-msdos-program',
  dmg: 'application/x-apple-diskimage',
  dms: 'application/x-dms',
  doc: 'application/msword',
  docm: 'application/vnd.ms-word.document.macroEnabled.12',
  docx: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  dot: 'application/msword',
  dotm: 'application/vnd.ms-word.template.macroEnabled.12',
  dotx: 'application/vnd.openxmlformats-officedocument.wordprocessingml.template',
  dv: 'video/dv',
  dvi: 'application/x-dvi',
  dx: 'chemical/x-jcamp-dx',
  dxr: 'application/x-director',
  emb: 'chemical/x-embl-dl-nucleotide',
  embl: 'chemical/x-embl-dl-nucleotide',
  eml: 'message/rfc822',
  eot: 'application/vnd.ms-fontobject',
  eps: 'application/postscript',
  eps2: 'application/postscript',
  eps3: 'application/postscript',
  epsf: 'application/postscript',
  epsi: 'application/postscript',
  erf: 'image/x-epson-erf',
  es: 'application/ecmascript',
  etx: 'text/x-setext',
  exe: 'application/x-msdos-program',
  ez: 'application/andrew-inset',
  fb: 'application/x-maker',
  fbdoc: 'application/x-maker',
  fch: 'chemical/x-gaussian-checkpoint',
  fchk: 'chemical/x-gaussian-checkpoint',
  fig: 'application/x-xfig',
  flac: 'audio/flac',
  fli: 'video/fli',
  flv: 'video/x-flv',
  fm: 'application/x-maker',
  frame: 'application/x-maker',
  frm: 'application/x-maker',
  gal: 'chemical/x-gaussian-log',
  gam: 'chemical/x-gamess-input',
  gamin: 'chemical/x-gamess-input',
  gan: 'application/x-ganttproject',
  gau: 'chemical/x-gaussian-input',
  gcd: 'text/x-pcs-gcd',
  gcf: 'application/x-graphing-calculator',
  gcg: 'chemical/x-gcg8-sequence',
  gen: 'chemical/x-genbank',
  gf: 'application/x-tex-gf',
  gif: 'image/gif',
  gjc: 'chemical/x-gaussian-input',
  gjf: 'chemical/x-gaussian-input',
  gl: 'video/gl',
  gnumeric: 'application/x-gnumeric',
  gpt: 'chemical/x-mopac-graph',
  gsf: 'application/x-font',
  gsm: 'audio/x-gsm',
  gtar: 'application/x-gtar',
  gz: 'application/gzip',
  h: 'text/x-chdr',
  'h++': 'text/x-c++hdr',
  hdf: 'application/x-hdf',
  hh: 'text/x-c++hdr',
  hin: 'chemical/x-hin',
  hpp: 'text/x-c++hdr',
  hqx: 'application/mac-binhex40',
  hs: 'text/x-haskell',
  hta: 'application/hta',
  htc: 'text/x-component',
  htm: 'text/html',
  html: 'text/html',
  hwp: 'application/x-hwp',
  hxx: 'text/x-c++hdr',
  ica: 'application/x-ica',
  ice: 'x-conference/x-cooltalk',
  ico: 'image/vnd.microsoft.icon',
  ics: 'text/calendar',
  icz: 'text/calendar',
  ief: 'image/ief',
  iges: 'model/iges',
  igs: 'model/iges',
  iii: 'application/x-iphone',
  info: 'application/x-info',
  inp: 'chemical/x-gamess-input',
  ins: 'application/x-internet-signup',
  iso: 'application/x-iso9660-image',
  isp: 'application/x-internet-signup',
  ist: 'chemical/x-isostar',
  istr: 'chemical/x-isostar',
  jad: 'text/vnd.sun.j2me.app-descriptor',
  jam: 'application/x-jam',
  jar: 'application/java-archive',
  java: 'text/x-java',
  jdx: 'chemical/x-jcamp-dx',
  jmz: 'application/x-jmol',
  jng: 'image/x-jng',
  jnlp: 'application/x-java-jnlp-file',
  jp2: 'image/jp2',
  jpe: 'image/jpeg',
  jpeg: 'image/jpeg',
  jpf: 'image/jpx',
  jpg: 'image/jpeg',
  jpg2: 'image/jp2',
  jpm: 'image/jpm',
  jpx: 'image/jpx',
  js: 'application/javascript',
  json: 'application/json',
  kar: 'audio/midi',
  key: 'application/pgp-keys',
  kil: 'application/x-killustrator',
  kin: 'chemical/x-kinemage',
  kml: 'application/vnd.google-earth.kml+xml',
  kmz: 'application/vnd.google-earth.kmz',
  kpr: 'application/x-kpresenter',
  kpt: 'application/x-kpresenter',
  ksp: 'application/x-kspread',
  kwd: 'application/x-kword',
  kwt: 'application/x-kword',
  latex: 'application/x-latex',
  lha: 'application/x-lha',
  lhs: 'text/x-literate-haskell',
  lin: 'application/bbolin',
  lsf: 'video/x-la-asf',
  lsx: 'video/x-la-asf',
  ltx: 'text/x-tex',
  ly: 'text/x-lilypond',
  lyx: 'application/x-lyx',
  lzh: 'application/x-lzh',
  lzx: 'application/x-lzx',
  m3g: 'application/m3g',
  m3u: 'audio/x-mpegurl',
  m3u8: 'application/x-mpegURL',
  m4a: 'audio/mpeg',
  maker: 'application/x-maker',
  man: 'application/x-troff-man',
  mbox: 'application/mbox',
  mcif: 'chemical/x-mmcif',
  mcm: 'chemical/x-macmolecule',
  mdb: 'application/msaccess',
  me: 'application/x-troff-me',
  mesh: 'model/mesh',
  mid: 'audio/midi',
  midi: 'audio/midi',
  mif: 'application/x-mif',
  mkv: 'video/x-matroska',
  mm: 'application/x-freemind',
  mmd: 'chemical/x-macromodel-input',
  mmf: 'application/vnd.smaf',
  mml: 'text/mathml',
  mmod: 'chemical/x-macromodel-input',
  mng: 'video/x-mng',
  moc: 'text/x-moc',
  mol: 'chemical/x-mdl-molfile',
  mol2: 'chemical/x-mol2',
  moo: 'chemical/x-mopac-out',
  mop: 'chemical/x-mopac-input',
  mopcrt: 'chemical/x-mopac-input',
  mov: 'video/quicktime',
  movie: 'video/x-sgi-movie',
  mp2: 'audio/mpeg',
  mp3: 'audio/mpeg',
  mp4: 'video/mp4',
  mpc: 'chemical/x-mopac-input',
  mpe: 'video/mpeg',
  mpeg: 'video/mpeg',
  mpega: 'audio/mpeg',
  mpg: 'video/mpeg',
  mpga: 'audio/mpeg',
  mph: 'application/x-comsol',
  mpv: 'video/x-matroska',
  ms: 'application/x-troff-ms',
  msh: 'model/mesh',
  msi: 'application/x-msi',
  mvb: 'chemical/x-mopac-vib',
  mxf: 'application/mxf',
  mxu: 'video/vnd.mpegurl',
  nb: 'application/mathematica',
  nbp: 'application/mathematica',
  nc: 'application/x-netcdf',
  nef: 'image/x-nikon-nef',
  nwc: 'application/x-nwc',
  o: 'application/x-object',
  oda: 'application/oda',
  odb: 'application/vnd.oasis.opendocument.database',
  odc: 'application/vnd.oasis.opendocument.chart',
  odf: 'application/vnd.oasis.opendocument.formula',
  odg: 'application/vnd.oasis.opendocument.graphics',
  odi: 'application/vnd.oasis.opendocument.image',
  odm: 'application/vnd.oasis.opendocument.text-master',
  odp: 'application/vnd.oasis.opendocument.presentation',
  ods: 'application/vnd.oasis.opendocument.spreadsheet',
  odt: 'application/vnd.oasis.opendocument.text',
  oga: 'audio/ogg',
  ogg: 'audio/ogg',
  ogv: 'video/ogg',
  ogx: 'application/ogg',
  old: 'application/x-trash',
  one: 'application/onenote',
  onepkg: 'application/onenote',
  onetmp: 'application/onenote',
  onetoc2: 'application/onenote',
  opf: 'application/oebps-package+xml',
  opus: 'audio/ogg',
  orc: 'audio/csound',
  orf: 'image/x-olympus-orf',
  otf: 'application/font-sfnt',
  otg: 'application/vnd.oasis.opendocument.graphics-template',
  oth: 'application/vnd.oasis.opendocument.text-web',
  otp: 'application/vnd.oasis.opendocument.presentation-template',
  ots: 'application/vnd.oasis.opendocument.spreadsheet-template',
  ott: 'application/vnd.oasis.opendocument.text-template',
  oza: 'application/x-oz-application',
  p: 'text/x-pascal',
  p7r: 'application/x-pkcs7-certreqresp',
  pac: 'application/x-ns-proxy-autoconfig',
  pas: 'text/x-pascal',
  pat: 'image/x-coreldrawpattern',
  patch: 'text/x-diff',
  pbm: 'image/x-portable-bitmap',
  pcap: 'application/vnd.tcpdump.pcap',
  pcf: 'application/x-font-pcf',
  'pcf.Z': 'application/x-font-pcf',
  pcx: 'image/pcx',
  pdb: 'chemical/x-pdb',
  pdf: 'application/pdf',
  pfa: 'application/x-font',
  pfb: 'application/x-font',
  pfr: 'application/font-tdpfr',
  pgm: 'image/x-portable-graymap',
  pgn: 'application/x-chess-pgn',
  pgp: 'application/pgp-encrypted',
  php: '#application/x-httpd-php',
  php3: '#application/x-httpd-php3',
  php3p: '#application/x-httpd-php3-preprocessed',
  php4: '#application/x-httpd-php4',
  php5: '#application/x-httpd-php5',
  phps: '#application/x-httpd-php-source',
  pht: '#application/x-httpd-php',
  phtml: '#application/x-httpd-php',
  pk: 'application/x-tex-pk',
  pl: 'text/x-perl',
  pls: 'audio/x-scpls',
  pm: 'text/x-perl',
  png: 'image/png',
  pnm: 'image/x-portable-anymap',
  pot: 'text/plain',
  potm: 'application/vnd.ms-powerpoint.template.macroEnabled.12',
  potx: 'application/vnd.openxmlformats-officedocument.presentationml.template',
  ppam: 'application/vnd.ms-powerpoint.addin.macroEnabled.12',
  ppm: 'image/x-portable-pixmap',
  pps: 'application/vnd.ms-powerpoint',
  ppsm: 'application/vnd.ms-powerpoint.slideshow.macroEnabled.12',
  ppsx: 'application/vnd.openxmlformats-officedocument.presentationml.slideshow',
  ppt: 'application/vnd.ms-powerpoint',
  pptm: 'application/vnd.ms-powerpoint.presentation.macroEnabled.12',
  pptx: 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
  prf: 'application/pics-rules',
  prt: 'chemical/x-ncbi-asn1-ascii',
  ps: 'application/postscript',
  psd: 'image/x-photoshop',
  py: 'text/x-python',
  pyc: 'application/x-python-code',
  pyo: 'application/x-python-code',
  qgs: 'application/x-qgis',
  qt: 'video/quicktime',
  qtl: 'application/x-quicktimeplayer',
  ra: 'audio/x-pn-realaudio',
  ram: 'audio/x-pn-realaudio',
  rar: 'application/rar',
  ras: 'image/x-cmu-raster',
  rb: 'application/x-ruby',
  rd: 'chemical/x-mdl-rdfile',
  rdf: 'application/rdf+xml',
  rdp: 'application/x-rdp',
  rgb: 'image/x-rgb',
  rhtml: '#application/x-httpd-eruby',
  rm: 'audio/x-pn-realaudio',
  roff: 'application/x-troff',
  ros: 'chemical/x-rosdal',
  rpm: 'application/x-redhat-package-manager',
  rss: 'application/x-rss+xml',
  rtf: 'application/rtf',
  rtx: 'text/richtext',
  rxn: 'chemical/x-mdl-rxnfile',
  scala: 'text/x-scala',
  sce: 'application/x-scilab',
  sci: 'application/x-scilab',
  sco: 'audio/csound',
  scr: 'application/x-silverlight',
  sct: 'text/scriptlet',
  sd: 'chemical/x-mdl-sdfile',
  sd2: 'audio/x-sd2',
  sda: 'application/vnd.stardivision.draw',
  sdc: 'application/vnd.stardivision.calc',
  sdd: 'application/vnd.stardivision.impress',
  sds: 'application/vnd.stardivision.chart',
  sdw: 'application/vnd.stardivision.writer',
  ser: 'application/java-serialized-object',
  sfd: 'application/vnd.font-fontforge-sfd',
  sfv: 'text/x-sfv',
  sgf: 'application/x-go-sgf',
  sgl: 'application/vnd.stardivision.writer-global',
  sh: 'application/x-sh',
  shar: 'application/x-shar',
  shp: 'application/x-qgis',
  shtml: 'text/html',
  shx: 'application/x-qgis',
  sid: 'audio/prs.sid',
  sig: 'application/pgp-signature',
  sik: 'application/x-trash',
  silo: 'model/mesh',
  sis: 'application/vnd.symbian.install',
  sisx: 'x-epoc/x-sisx-app',
  sit: 'application/x-stuffit',
  sitx: 'application/x-stuffit',
  skd: 'application/x-koan',
  skm: 'application/x-koan',
  skp: 'application/x-koan',
  skt: 'application/x-koan',
  sldm: 'application/vnd.ms-powerpoint.slide.macroEnabled.12',
  sldx: 'application/vnd.openxmlformats-officedocument.presentationml.slide',
  smi: 'application/smil+xml',
  smil: 'application/smil+xml',
  snd: 'audio/basic',
  spc: 'chemical/x-galactic-spc',
  spl: 'application/x-futuresplash',
  spx: 'audio/ogg',
  sql: 'application/x-sql',
  src: 'application/x-wais-source',
  srt: 'text/plain',
  stc: 'application/vnd.sun.xml.calc.template',
  std: 'application/vnd.sun.xml.draw.template',
  sti: 'application/vnd.sun.xml.impress.template',
  stw: 'application/vnd.sun.xml.writer.template',
  sty: 'text/x-tex',
  sv4cpio: 'application/x-sv4cpio',
  sv4crc: 'application/x-sv4crc',
  svg: 'image/svg+xml',
  svgz: 'image/svg+xml',
  sw: 'chemical/x-swissprot',
  swf: 'application/x-shockwave-flash',
  swfl: 'application/x-shockwave-flash',
  sxc: 'application/vnd.sun.xml.calc',
  sxd: 'application/vnd.sun.xml.draw',
  sxg: 'application/vnd.sun.xml.writer.global',
  sxi: 'application/vnd.sun.xml.impress',
  sxm: 'application/vnd.sun.xml.math',
  sxw: 'application/vnd.sun.xml.writer',
  t: 'application/x-troff',
  tar: 'application/x-tar',
  taz: 'application/x-gtar-compressed',
  tcl: 'application/x-tcl',
  tex: 'text/x-tex',
  texi: 'application/x-texinfo',
  texinfo: 'application/x-texinfo',
  text: 'text/plain',
  tgf: 'chemical/x-mdl-tgf',
  tgz: 'application/x-gtar-compressed',
  thmx: 'application/vnd.ms-officetheme',
  tif: 'image/tiff',
  tiff: 'image/tiff',
  tk: 'text/x-tcl',
  tm: 'text/texmacs',
  torrent: 'application/x-bittorrent',
  tr: 'application/x-troff',
  ts: 'video/MP2T',
  tsp: 'application/dsptype',
  tsv: 'text/tab-separated-values',
  ttf: 'application/font-sfnt',
  ttl: 'text/turtle',
  txt: 'text/plain',
  uls: 'text/iuls',
  ustar: 'application/x-ustar',
  val: 'chemical/x-ncbi-asn1-binary',
  vcard: 'text/vcard',
  vcd: 'application/x-cdlink',
  vcf: 'text/vcard',
  vcs: 'text/x-vcalendar',
  vmd: 'chemical/x-vmd',
  vms: 'chemical/x-vamas-iso14976',
  vrm: 'x-world/x-vrml',
  vrml: 'model/vrml',
  vsd: 'application/vnd.visio',
  vss: 'application/vnd.visio',
  vst: 'application/vnd.visio',
  vsw: 'application/vnd.visio',
  wad: 'application/x-doom',
  wasm: 'application/wasm',
  wav: 'audio/wav',
  wax: 'audio/x-ms-wax',
  wbmp: 'image/vnd.wap.wbmp',
  wbxml: 'application/vnd.wap.wbxml',
  webm: 'video/webm',
  wk: 'application/x-123',
  wm: 'video/x-ms-wm',
  wma: 'audio/x-ms-wma',
  wmd: 'application/x-ms-wmd',
  wml: 'text/vnd.wap.wml',
  wmlc: 'application/vnd.wap.wmlc',
  wmls: 'text/vnd.wap.wmlscript',
  wmlsc: 'application/vnd.wap.wmlscriptc',
  wmv: 'video/x-ms-wmv',
  wmx: 'video/x-ms-wmx',
  wmz: 'application/x-ms-wmz',
  woff: 'application/font-woff',
  wp5: 'application/vnd.wordperfect5.1',
  wpd: 'application/vnd.wordperfect',
  wrl: 'model/vrml',
  wsc: 'text/scriptlet',
  wvx: 'video/x-ms-wvx',
  wz: 'application/x-wingz',
  x3d: 'model/x3d+xml',
  x3db: 'model/x3d+binary',
  x3dv: 'model/x3d+vrml',
  xbm: 'image/x-xbitmap',
  xcf: 'application/x-xcf',
  xcos: 'application/x-scilab-xcos',
  xht: 'application/xhtml+xml',
  xhtml: 'application/xhtml+xml',
  xlam: 'application/vnd.ms-excel.addin.macroEnabled.12',
  xlb: 'application/vnd.ms-excel',
  xls: 'application/vnd.ms-excel',
  xlsb: 'application/vnd.ms-excel.sheet.binary.macroEnabled.12',
  xlsm: 'application/vnd.ms-excel.sheet.macroEnabled.12',
  xlsx: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  xlt: 'application/vnd.ms-excel',
  xltm: 'application/vnd.ms-excel.template.macroEnabled.12',
  xltx: 'application/vnd.openxmlformats-officedocument.spreadsheetml.template',
  xml: 'application/xml',
  xpi: 'application/x-xpinstall',
  xpm: 'image/x-xpixmap',
  xsd: 'application/xml',
  xsl: 'application/xslt+xml',
  xslt: 'application/xslt+xml',
  xspf: 'application/xspf+xml',
  xtel: 'chemical/x-xtel',
  xul: 'application/vnd.mozilla.xul+xml',
  xwd: 'image/x-xwindowdump',
  xyz: 'chemical/x-xyz',
  xz: 'application/x-xz',
  zip: 'application/zip'
};

// 1. crcjs maybe make browser long task
// 2. the size of webassembly version's crc is a bit large, it's 1.2MB when uncompressed.

class CRC {
  reset() {}

  async updateBlob() {
    throw new TosClientError('Not implemented.(CRC may cause browser lag.)');
  }

  update(_value) {
    throw new TosClientError('Not implemented.(CRC may cause browser lag.)');
  }

}

var browserCRC = {
  __proto__: null,
  CRC: CRC
};

let crcModule = null;

{
  crcModule = browserCRC;
}

const {
  CRC: CRC$1,
  combineCrc64
} = crcModule;

function createDefaultRateLimiter(_capacity, _rate) {
  throw Error('no implemention in browser environment');
}
function createRateLimiterStream(_rateLimiter) {
  throw Error('no implemention in browser environment');
}

var moduleBrowser = {
  __proto__: null,
  createDefaultRateLimiter: createDefaultRateLimiter,
  createRateLimiterStream: createRateLimiterStream
};

let rateLimiter = null;

{
  rateLimiter = moduleBrowser;
}

const {
  createDefaultRateLimiter: createDefaultRateLimiter$1,
  createRateLimiterStream: createRateLimiterStream$1
} = rateLimiter;

var TosHeader;

(function (TosHeader) {
  TosHeader["HeaderRestore"] = "x-tos-restore";
  TosHeader["HeaderRestoreExpiryDays"] = "x-tos-restore-expiry-days";
  TosHeader["HeaderRestoreRequestDate"] = "x-tos-restore-request-date";
  TosHeader["HeaderRestoreTier"] = "x-tos-restore-tier";
  TosHeader["HeaderProjectName"] = "x-tos-project-name";
  TosHeader["HeaderReplicationStatus"] = "x-tos-replication-status";
})(TosHeader || (TosHeader = {}));

const RestoreOngoingRequestTrueStr = 'ongoing-request="true"';

const getObjectInputKey = input => {
  return typeof input === 'string' ? input : input.key;
};
const DEFAULT_CONTENT_TYPE = 'application/octet-stream';
function lookupMimeType(key) {
  const lastDotIndex = key.lastIndexOf('.');

  if (lastDotIndex <= 0) {
    return undefined;
  }

  const extName = key.slice(lastDotIndex + 1).toLowerCase();
  return mimeTypes[extName];
} // for all object methods

function validateObjectName(input) {
  const key = typeof input === 'string' ? input : input.key;

  if (key.length < 1) {
    throw new TosClientError('invalid object name, the length must be greater than 1');
  }
}
function getSize(body, headers) {
  if (isBuffer(body)) {
    return body.length;
  }

  if (isBlob(body)) {
    return body.size;
  }

  if (headers && headers['content-length']) {
    const v = +headers['content-length'];

    if (v >= 0) {
      return v;
    }
  }

  return null;
}
function getEmitReadBodyConfig({
  body,
  dataTransferCallback,
  makeRetryStream,
  rateLimiter
}) {
  let newBody = body;

  const getDefaultRet = () => ({
    body: newBody,
    makeRetryStream: undefined
  });

  {
    return getDefaultRet();
  }
}
async function getCRCBodyConfig({
  body,
  beforeRetry,
  makeRetryStream,
  enableCRC
}) {
  {
    return {
      body,
      beforeRetry,
      makeRetryStream
    };
  }
}
async function getNewBodyConfig(input) {
  const config1 = getEmitReadBodyConfig(input);
  input = { ...input,
    ...config1
  };
  const config2 = getCRCBodyConfig(input);
  return config2;
}
function getCopySourceHeaderValue(srcBucket, srcKey) {
  return `/${srcBucket}/${encodeURIComponent(srcKey)}`;
}
const getRestoreInfoFromHeaders = headers => {
  if (!headers) return;
  const headerStoreValue = headers == null ? void 0 : headers[TosHeader.HeaderRestore];

  if (headerStoreValue) {
    var _split$1$split$, _split$, _split$$split;

    /**
     * value example:
     * X-Tos-Restore: ongoing-request="false", expiry-date="Fri, 19 Apr 2024 00:00:00 GMT"
     */
    const ExpiryDate = (_split$1$split$ = (_split$ = (headerStoreValue != null ? headerStoreValue : '').split('",')[1]) == null ? void 0 : _split$.split == null ? void 0 : (_split$$split = _split$.split('=')) == null ? void 0 : _split$$split[1]) != null ? _split$1$split$ : '';
    const OngoingRequest = (headerStoreValue == null ? void 0 : headerStoreValue.trim()) === RestoreOngoingRequestTrueStr ? true : false;
    const restoreInfo = {
      RestoreStatus: {
        OngoingRequest,
        ExpiryDate
      }
    };

    if (OngoingRequest) {
      var _headers$TosHeader$He;

      restoreInfo.RestoreParam = {
        ExpiryDays: headers[TosHeader.HeaderRestoreExpiryDays] ? Number(headers[TosHeader.HeaderRestoreExpiryDays]) : 0,
        RequestDate: (_headers$TosHeader$He = headers[TosHeader.HeaderRestoreRequestDate]) != null ? _headers$TosHeader$He : '',
        Tier: headers[TosHeader.HeaderRestoreTier]
      };
    }

    return restoreInfo;
  }

  return;
};

(function (DataTransferType) {
  DataTransferType[DataTransferType["Started"] = 1] = "Started";
  DataTransferType[DataTransferType["Rw"] = 2] = "Rw";
  DataTransferType[DataTransferType["Succeed"] = 3] = "Succeed";
  DataTransferType[DataTransferType["Failed"] = 4] = "Failed";
})(exports.DataTransferType || (exports.DataTransferType = {}));

const TOS = /*#__PURE__*/createDebug('TOS');

const retryNamespace = '__retryConfig__';
const retrySignatureNamespace = '__retrySignature__';

function isNetworkError(error) {
  var _error$response$heade;

  // no response or no requestId, ignore no network(error.code is undefined)
  return !error.response && Boolean(error.code) || error.response && !((_error$response$heade = error.response.headers) != null && _error$response$heade['x-tos-request-id']);
}

function isCanRetryStatusCode(error) {
  if (!error.response) {
    return false;
  }

  const {
    status
  } = error.response;

  if (status === 429 || status >= 500) {
    return true;
  }

  return false;
}

const BROWSER_NEED_DELETE_HEADERS = ['content-length', 'user-agent', 'host'];
const makeAxiosInst = maxRetryCount => {
  const axiosInst = axios.create(); // set `axiosInst` default values to avoid being affected by the global default values of axios

  axiosInst.defaults.auth = undefined;
  axiosInst.defaults.responseType = 'json';
  axiosInst.defaults.params = undefined;
  axiosInst.defaults.headers = {};
  axiosInst.defaults.withCredentials = false;
  axiosInst.defaults.maxContentLength = -1;
  axiosInst.defaults.maxBodyLength = -1;
  axiosInst.defaults.maxRedirects = 0;

  axiosInst.defaults.validateStatus = function (status) {
    return status >= 200 && status < 300; // default
  };

  axiosInst.defaults.decompress = false;
  axiosInst.defaults.transitional = {
    silentJSONParsing: true,
    forcedJSONParsing: true,
    clarifyTimeoutError: false
  }; // delete browser headers

  {
    axiosInst.interceptors.request.use(config => {
      if (!config.headers) {
        return config;
      }

      Object.keys(config.headers).forEach(key => {
        if (BROWSER_NEED_DELETE_HEADERS.includes(key.toLowerCase())) {
          delete config.headers[key];
        }
      });
      return config;
    });
  } // headers


  const ensureHeaders = v => {
    var _v$response;

    v.headers = v.headers || v.header || (v == null ? void 0 : (_v$response = v.response) == null ? void 0 : _v$response.headers) || {};
    return v;
  };

  axiosInst.interceptors.response.use(ensureHeaders, error => {
    ensureHeaders(error);
    return Promise.reject(error);
  }); // decode header. Encode headers' value by encodeHeadersValue method before calling axios

  function handleResponseHeader(headers) {
    Object.entries(headers).forEach(([key, value]) => {
      const [err, decodedValue] = safeSync(() => decodeURI(value));

      if (err || decodedValue == null || decodedValue === value) {
        return;
      }

      let sArr = [];
      const valueArr = `${value}`.match(/./gu);
      const decodedValueArr = decodedValue.match(/./gu);

      for (let i = 0, j = 0; i < decodedValueArr.length;) {
        const ch = decodedValueArr[i];

        if (ch === valueArr[j]) {
          sArr.push(ch);
          ++i;
          ++j;
          continue;
        }

        const encodedCh = encodeURIComponent(ch);

        if (ch.length > 1 || ch.charCodeAt(0) >= 128) {
          sArr.push(ch);
        } else {
          sArr.push(encodedCh);
        }

        ++i;
        j += encodedCh.length;
      }

      headers[key] = sArr.join('');
    });
  }

  axiosInst.interceptors.response.use(res => {
    if (!res.headers) {
      return res;
    }

    handleResponseHeader(res.headers);
    return res;
  }, async error => {
    var _error$response;

    if (!axios.isAxiosError(error)) {
      return Promise.reject(error);
    }

    const headers = (_error$response = error.response) == null ? void 0 : _error$response.headers;

    if (!headers) {
      return Promise.reject(error);
    }

    handleResponseHeader(headers);
    return Promise.reject(error);
  }); // retry

  axiosInst.interceptors.response.use(undefined, async error => {
    var _retryConfig$retryCou;

    const {
      config
    } = error;

    if (!config) {
      return Promise.reject(error);
    }

    if (!config[retryNamespace]) {
      config[retryNamespace] = {};
    }

    const retryConfig = config[retryNamespace];
    const retryCount = (_retryConfig$retryCou = retryConfig.retryCount) != null ? _retryConfig$retryCou : 0;
    let newData = config.data;

    const canRetryData = (() => {

      return true;
    })();

    const canRetry = (isNetworkError(error) || isCanRetryStatusCode(error)) && retryCount < maxRetryCount && canRetryData;

    if (!canRetry) {
      return Promise.reject(error);
    }

    const retrySignature = config[retrySignatureNamespace];

    if (retrySignature) {
      const {
        signOpt,
        sigInst
      } = retrySignature;
      const signatureHeaders = sigInst.signatureHeader(signOpt);
      signatureHeaders.forEach((value, key) => {
        config.headers[key] = value;
      });
    } //console.log('config: ', config)


    TOS('retryConfig: ', config);
    const nextConfig = { ...config,
      data: newData,
      [retryNamespace]: { ...retryConfig,
        retryCount: retryCount + 1
      }
    };
    retryConfig.beforeRetry == null ? void 0 : retryConfig.beforeRetry();
    return axiosInst(nextConfig);
  });
  return axiosInst;
};

function getEnc(coding) {
  switch (coding) {
    case 'utf-8':
      return cryptoEncUtf8;

    case 'base64':
      return cryptoEncBase64;

    case 'hex':
      return cryptoEncHex;

    default:
      throw new TosClientError('The coding is not supported');
  }
}

function decode(v, decoding) {
  if (!decoding) {
    return v;
  }

  return v.toString(getEnc(decoding));
}

const hmacSha256 = function hmacSha256(key, message, decoding) {
  return decode(cryptoHmacSha256(message, key), decoding);
};
const hashSha256 = function hashSha256(message, decoding) {
  return decode(cryptoHashSha256(message), decoding);
};
const hashMd5 = function hashMd5(message, decoding) {
  if (isBuffer(message)) {
    throw new TosClientError('not support buffer in browser environment');
  }

  return decode(cryptoHashMd5(message), decoding);
};
const parse = function parse(str, encoding) {
  return getEnc(encoding).parse(str);
};
const stringify = function stringify(str, decoding) {
  return getEnc(decoding).stringify(str);
};

var cryptoBrowser = {
  __proto__: null,
  hmacSha256: hmacSha256,
  hashSha256: hashSha256,
  hashMd5: hashMd5,
  parse: parse,
  stringify: stringify
};

let crypto = null;

{
  crypto = cryptoBrowser;
}

const {
  hmacSha256: hmacSha256$1,
  hashSha256: hashSha256$1,
  hashMd5: hashMd5$1,
  parse: parse$1,
  stringify: stringify$1
} = crypto;

async function _uploadPart(input) {
  const {
    uploadId,
    partNumber,
    body,
    enableContentMD5 = false
  } = input;
  const headers = normalizeHeadersKey(input.headers);
  input.headers = headers;
  fillRequestHeaders(input, ['trafficLimit', 'ssecAlgorithm', 'ssecKey', 'ssecKeyMD5']);
  const size = getSize(body);

  if (size && headers['content-length'] == null) {
    headers['content-length'] = size.toFixed(0);
  }

  if (enableContentMD5 && headers['content-md5'] == null) {
    // current only support in nodejs
    {
      console.warn(`current not support enableMD5Checksum`);
    }
  }

  const totalSize = getSize(input.body, headers);
  const totalSizeValid = totalSize != null;

  if (!totalSizeValid && (input.dataTransferStatusChange || input.progress)) {
    console.warn(`Don't get totalSize of uploadPart's body, the \`dataTransferStatusChange\` callback will not trigger. You can use \`uploadPartFromFile\` instead`);
  }

  let consumedBytes = 0;
  const {
    dataTransferStatusChange,
    progress
  } = input;

  const triggerDataTransfer = (type, rwOnceBytes = 0) => {
    // request cancel will make rwOnceBytes < 0 in browser
    if (!totalSizeValid || rwOnceBytes < 0) {
      return;
    }

    if (!dataTransferStatusChange && !progress) {
      return;
    }

    consumedBytes += rwOnceBytes;
    dataTransferStatusChange == null ? void 0 : dataTransferStatusChange({
      type,
      rwOnceBytes,
      consumedBytes,
      totalBytes: totalSize
    });

    const progressValue = (() => {
      if (totalSize === 0) {
        if (type === exports.DataTransferType.Succeed) {
          return 1;
        }

        return 0;
      }

      return consumedBytes / totalSize;
    })();

    if (progressValue === 1) {
      if (type === exports.DataTransferType.Succeed) {
        progress == null ? void 0 : progress(progressValue);
      }
    } else {
      progress == null ? void 0 : progress(progressValue);
    }
  };

  const bodyConfig = await getNewBodyConfig({
    body: input.body,
    dataTransferCallback: n => triggerDataTransfer(exports.DataTransferType.Rw, n),
    beforeRetry: input.beforeRetry,
    makeRetryStream: input.makeRetryStream,
    enableCRC: this.opts.enableCRC,
    rateLimiter: input.rateLimiter
  });
  triggerDataTransfer(exports.DataTransferType.Started);

  const task = async () => {
    const res = await this._fetchObject(input, 'PUT', {
      partNumber,
      uploadId
    }, headers, bodyConfig.body, {
      handleResponse: res => ({
        partNumber,
        ETag: res.headers.etag,
        serverSideEncryption: res.headers['x-tos-server-side-encryption'],
        serverSideDataEncryption: res.headers['x-tos-server-side-data-encryption'],
        serverSideEncryptionKeyId: res.headers['x-tos-server-side-encryption-kms-key-id'],
        ssecAlgorithm: res.headers['x-tos-server-side-encryption-customer-algorithm'],
        ssecKeyMD5: res.headers['x-tos-server-side-encryption-customer-key-MD5'],
        hashCrc64ecma: res.headers['x-tos-hash-crc64ecma']
      }),
      axiosOpts: {
        [retryNamespace]: {
          beforeRetry: () => {
            consumedBytes = 0;
            bodyConfig.beforeRetry == null ? void 0 : bodyConfig.beforeRetry();
          },
          makeRetryStream: bodyConfig.makeRetryStream
        },
        onUploadProgress: event => {
          triggerDataTransfer(exports.DataTransferType.Rw, event.loaded - consumedBytes);
        }
      }
    });

    if (this.opts.enableCRC && bodyConfig.crc) {
      checkCRC64WithHeaders(bodyConfig.crc, res.headers);
    }

    return res;
  };

  const [err, res] = await safeAwait(task()); // FAQ: no etag

  {
    if (res && !res.data.ETag) {
      throw new TosClientError("No ETag in uploadPart's response headers, please see https://www.volcengine.com/docs/6349/127737 to fix CORS problem");
    }
  }

  if (err || !res) {
    triggerDataTransfer(exports.DataTransferType.Failed);
    throw err;
  }

  triggerDataTransfer(exports.DataTransferType.Succeed);
  return res;
}
async function uploadPart(input) {
  return _uploadPart.call(this, input);
}
async function uploadPartFromFile(input) {

  {
    throw new TosClientError("uploadPartFromFile doesn't support in browser environment");
  }
}

async function completeMultipartUpload(input) {
  var _input$headers;

  input.headers = (_input$headers = input.headers) != null ? _input$headers : {};
  fillRequestHeaders(input, ['callback', 'callbackVar', 'forbidOverwrite']);

  const handleResponse = response => {
    const bucket = input.bucket || this.opts.bucket || '';
    const headers = response.headers;
    const result = { ...{
        VersionID: headers['x-tos-version-id'],
        ETag: headers['etag'],
        Bucket: bucket,
        Location: headers['location'],
        HashCrc64ecma: headers['x-tos-hash-crc64ecma'],
        Key: input.key
      },
      ...response.data
    };

    if (input.callback) {
      result.CallbackResult = `${JSON.stringify(response.data)}`;
    }

    return result;
  };

  if (input.completeAll) {
    var _input$parts;

    if (((_input$parts = input.parts) == null ? void 0 : _input$parts.length) > 0) {
      throw new TosClientError(`Should not specify both 'completeAll' and 'parts' params.`);
    }

    return this._fetchObject(input, 'POST', {
      uploadId: input.uploadId
    }, { ...input.headers,
      'x-tos-complete-all': 'yes'
    }, undefined, {
      handleResponse
    });
  }

  return this._fetchObject(input, 'POST', {
    uploadId: input.uploadId
  }, { ...input.headers
  }, {
    Parts: input.parts.map(it => ({
      ETag: it.eTag,
      PartNumber: it.partNumber
    }))
  }, {
    handleResponse
  });
}

(function (UploadEventType) {
  UploadEventType[UploadEventType["CreateMultipartUploadSucceed"] = 1] = "CreateMultipartUploadSucceed";
  UploadEventType[UploadEventType["CreateMultipartUploadFailed"] = 2] = "CreateMultipartUploadFailed";
  UploadEventType[UploadEventType["UploadPartSucceed"] = 3] = "UploadPartSucceed";
  UploadEventType[UploadEventType["UploadPartFailed"] = 4] = "UploadPartFailed";
  UploadEventType[UploadEventType["UploadPartAborted"] = 5] = "UploadPartAborted";
  UploadEventType[UploadEventType["CompleteMultipartUploadSucceed"] = 6] = "CompleteMultipartUploadSucceed";
  UploadEventType[UploadEventType["CompleteMultipartUploadFailed"] = 7] = "CompleteMultipartUploadFailed";
})(exports.UploadEventType || (exports.UploadEventType = {}));

const CHECKPOINT_FILE_NAME_PLACEHOLDER = '@@checkpoint-file-placeholder@@';
const FILE_PARAM_CHECK_MSG = '`file` must be string, Buffer, File or Blob';
const ABORT_ERROR_STATUS_CODE = [403, 404, 405];
async function uploadFile(input) {
  var _checkpointRichInfo$r3, _checkpointRichInfo$r4, _checkpointRichInfo$r5;

  const {
    cancelToken,
    enableContentMD5 = false
  } = input;
  const headers = normalizeHeadersKey(input.headers);
  input.headers = headers;
  fillRequestHeaders(input, ['encodingType', 'cacheControl', 'contentDisposition', 'contentEncoding', 'contentLanguage', 'contentType', 'expires', 'acl', 'grantFullControl', 'grantRead', 'grantReadAcp', 'grantWriteAcp', 'ssecAlgorithm', 'ssecKey', 'ssecKeyMD5', 'serverSideEncryption', 'serverSideDataEncryption', 'meta', 'websiteRedirectLocation', 'storageClass']);

  const isCancel = () => cancelToken && !!cancelToken.reason;
  const fileStats = await (async () => {

    return null;
  })();
  const fileSize = await (async () => {
    const {
      file
    } = input;

    if (fileStats) {
      return fileStats.size;
    }

    if (isBuffer(file)) {
      return file.length;
    }

    if (isBlob(file)) {
      return file.size;
    }

    throw new TosClientError(FILE_PARAM_CHECK_MSG);
  })();
  const checkpointRichInfo = await (async () => {

    if (typeof input.checkpoint === 'object') {
      return {
        record: input.checkpoint
      };
    }

    return {};
  })(); // check if file info is matched

  await (async () => {
    var _checkpointRichInfo$r;

    if (fileStats && (_checkpointRichInfo$r = checkpointRichInfo.record) != null && _checkpointRichInfo$r.file_info) {
      var _checkpointRichInfo$r2;

      const {
        last_modified,
        file_size
      } = (_checkpointRichInfo$r2 = checkpointRichInfo.record) == null ? void 0 : _checkpointRichInfo$r2.file_info;

      if (fileStats.mtimeMs !== last_modified || fileStats.size !== file_size) {
        console.warn(`The file has been modified since ${new Date(last_modified)}, so the checkpoint file is invalid, and specified file will be uploaded again.`);
        delete checkpointRichInfo.record;
      }
    }
  })();
  const partSize = calculateSafePartSize(fileSize, input.partSize || ((_checkpointRichInfo$r3 = checkpointRichInfo.record) == null ? void 0 : _checkpointRichInfo$r3.part_size) || DEFAULT_PART_SIZE, true); // check partSize is matched

  if (checkpointRichInfo.record && checkpointRichInfo.record.part_size !== partSize) {
    console.warn('The partSize param does not equal the partSize in checkpoint file, ' + 'so the checkpoint file is invalid, and specified file will be uploaded again.');
    delete checkpointRichInfo.record;
  }

  let bucket = input.bucket || this.opts.bucket || '';
  const key = input.key;
  let uploadId = '';
  let tasks = [];
  const allTasks = getAllTasks(fileSize, partSize);
  const initConsumedBytes = (((_checkpointRichInfo$r4 = checkpointRichInfo.record) == null ? void 0 : _checkpointRichInfo$r4.parts_info) || []).filter(it => it.is_completed).reduce((prev, it) => prev + it.part_size, 0);
  let consumedBytesForProgress = initConsumedBytes; // recorded tasks

  const recordedTasks = ((_checkpointRichInfo$r5 = checkpointRichInfo.record) == null ? void 0 : _checkpointRichInfo$r5.parts_info) || [];
  const recordedTaskMap = new Map();
  recordedTasks.forEach(it => recordedTaskMap.set(it.part_number, it));

  const getCheckpointContent = () => {
    const checkpointContent = {
      bucket,
      key,
      part_size: partSize,
      upload_id: uploadId,
      parts_info: recordedTasks
    };

    if (fileStats) {
      checkpointContent.file_info = {
        last_modified: fileStats.mtimeMs,
        file_size: fileStats.size
      };
    }

    return checkpointContent;
  };

  const triggerUploadEvent = e => {
    if (!input.uploadEventChange) {
      return;
    }

    const event = {
      bucket,
      uploadId,
      key,
      ...e
    };

    if (checkpointRichInfo.filePath) {
      event.checkpointFile = checkpointRichInfo.filePath;
    }

    input.uploadEventChange(event);
  };

  let TriggerProgressEventType;

  (function (TriggerProgressEventType) {
    TriggerProgressEventType[TriggerProgressEventType["start"] = 1] = "start";
    TriggerProgressEventType[TriggerProgressEventType["uploadPartSucceed"] = 2] = "uploadPartSucceed";
    TriggerProgressEventType[TriggerProgressEventType["completeMultipartUploadSucceed"] = 3] = "completeMultipartUploadSucceed";
  })(TriggerProgressEventType || (TriggerProgressEventType = {}));

  const triggerProgressEvent = type => {
    if (!input.progress) {
      return;
    }

    const percent = (() => {
      if (type === TriggerProgressEventType.start && fileSize === 0) {
        return 0;
      }

      return !fileSize ? 1 : consumedBytesForProgress / fileSize;
    })();

    if (consumedBytesForProgress === fileSize && type === TriggerProgressEventType.uploadPartSucceed) ; else {
      input.progress(percent, getCheckpointContent());
    }
  };

  let consumedBytes = initConsumedBytes;
  const {
    dataTransferStatusChange
  } = input;

  const triggerDataTransfer = (type, rwOnceBytes = 0) => {
    if (!dataTransferStatusChange) {
      return;
    }

    consumedBytes += rwOnceBytes;
    dataTransferStatusChange == null ? void 0 : dataTransferStatusChange({
      type,
      rwOnceBytes,
      consumedBytes,
      totalBytes: fileSize
    });
  };

  const writeCheckpointFile = makeSerialAsyncTask(async () => {
  });

  const rmCheckpointFile = async () => {
  };
  /**
   *
   * @param task one part task
   * @param uploadPartRes upload part failed if `uploadPartRes` is Error
   */


  const updateAfterUploadPart = async (task, uploadPartRes) => {
    let existRecordTask = recordedTaskMap.get(task.partNumber);

    if (!existRecordTask) {
      existRecordTask = {
        part_number: task.partNumber,
        offset: task.offset,
        part_size: task.partSize,
        is_completed: false,
        etag: '',
        hash_crc64ecma: ''
      };
      recordedTasks.push(existRecordTask);
      recordedTaskMap.set(existRecordTask.part_number, existRecordTask);
    }

    if (!uploadPartRes.err) {
      existRecordTask.is_completed = true;
      existRecordTask.etag = uploadPartRes.res.ETag;
      existRecordTask.hash_crc64ecma = uploadPartRes.res.hashCrc64ecma;
    }

    await writeCheckpointFile();
    const uploadPartInfo = {
      partNumber: existRecordTask.part_number,
      partSize: existRecordTask.part_size,
      offset: existRecordTask.offset
    };

    if (uploadPartRes.err) {
      const err = uploadPartRes.err;
      let type = exports.UploadEventType.UploadPartFailed;

      if (err instanceof TosServerError) {
        if (ABORT_ERROR_STATUS_CODE.includes(err.statusCode)) {
          type = exports.UploadEventType.UploadPartAborted;
        }
      }

      triggerUploadEvent({
        type,
        err,
        uploadPartInfo
      });
      return;
    }

    uploadPartInfo.etag = uploadPartRes.res.ETag;
    consumedBytesForProgress += uploadPartInfo.partSize;
    triggerUploadEvent({
      type: exports.UploadEventType.UploadPartSucceed,
      uploadPartInfo
    });
    triggerProgressEvent(TriggerProgressEventType.uploadPartSucceed);
  };

  if (checkpointRichInfo.record) {
    bucket = checkpointRichInfo.record.bucket;
    uploadId = checkpointRichInfo.record.upload_id; // checkpoint info exists, so need to calculate remain tasks

    const uploadedPartSet = new Set((checkpointRichInfo.record.parts_info || []).filter(it => it.is_completed).map(it => it.part_number));
    tasks = allTasks.filter(it => !uploadedPartSet.has(it.partNumber));
  } else {
    // createMultipartUpload will check bucket
    try {
      const {
        data: multipartRes
      } = await createMultipartUpload.call(this, input);

      if (isCancel()) {
        throw new CancelError('cancel uploadFile');
      }

      bucket = multipartRes.Bucket;
      uploadId = multipartRes.UploadId;

      if (checkpointRichInfo.filePathIsPlaceholder) {
        var _checkpointRichInfo$f;

        checkpointRichInfo.filePath = (_checkpointRichInfo$f = checkpointRichInfo.filePath) == null ? void 0 : _checkpointRichInfo$f.replace(`${CHECKPOINT_FILE_NAME_PLACEHOLDER}`, getDefaultCheckpointFilePath(bucket, key));
      }

      triggerUploadEvent({
        type: exports.UploadEventType.CreateMultipartUploadSucceed
      });
    } catch (_err) {
      const err = _err;
      triggerUploadEvent({
        type: exports.UploadEventType.CreateMultipartUploadFailed,
        err
      });
      throw err;
    }

    tasks = allTasks;
  }

  triggerProgressEvent(TriggerProgressEventType.start);

  const handleTasks = async () => {
    let firstErr = null;
    let index = 0; // TODO: how to test parallel does work, measure time is not right

    await Promise.all(Array.from({
      length: input.taskNum || 1
    }).map(async () => {
      while (true) {
        const currentIndex = index++;

        if (currentIndex >= tasks.length) {
          return;
        }

        const curTask = tasks[currentIndex];
        let consumedBytesThisTask = 0;
        const makeRetryStream = getMakeRetryStream();

        try {
          function getBody(file, task) {
            const {
              offset: start,
              partSize
            } = task;
            const end = start + partSize;

            if (makeRetryStream) {
              return makeRetryStream.make();
            }

            if (isBlob(file)) {
              return file.slice(start, end);
            }

            if (isBuffer(file)) {
              return file.slice(start, end);
            }

            throw new TosClientError(FILE_PARAM_CHECK_MSG);
          }

          const {
            data: uploadPartRes
          } = await _uploadPart.call(this, {
            bucket,
            key,
            uploadId,
            body: getBody(input.file, curTask),
            enableContentMD5,
            makeRetryStream: makeRetryStream == null ? void 0 : makeRetryStream.make,
            beforeRetry: () => {
              consumedBytes -= consumedBytesThisTask;
              consumedBytesThisTask = 0;
            },
            partNumber: curTask.partNumber,
            headers: {
              ['content-length']: `${curTask.partSize}`,
              ['x-tos-server-side-encryption-customer-algorithm']: headers['x-tos-server-side-encryption-customer-algorithm'],
              ['x-tos-server-side-encryption-customer-key']: headers['x-tos-server-side-encryption-customer-key'],
              ['x-tos-server-side-encryption-customer-key-md5']: headers['x-tos-server-side-encryption-customer-key-md5']
            },

            dataTransferStatusChange(status) {
              if (status.type !== exports.DataTransferType.Rw) {
                return;
              }

              if (isCancel()) {
                return;
              }

              consumedBytesThisTask += status.rwOnceBytes;
              triggerDataTransfer(status.type, status.rwOnceBytes);
            },

            trafficLimit: input.trafficLimit,
            rateLimiter: input.rateLimiter
          });

          if (isCancel()) {
            throw new CancelError('cancel uploadFile');
          }

          await updateAfterUploadPart(curTask, {
            res: uploadPartRes
          });
        } catch (_err) {
          tryDestroy(makeRetryStream == null ? void 0 : makeRetryStream.getLastStream(), _err);
          const err = _err;
          consumedBytes -= consumedBytesThisTask;
          consumedBytesThisTask = 0;

          if (isCancelError(err)) {
            throw err;
          }

          if (isCancel()) {
            throw new CancelError('cancel uploadFile');
          }

          if (!firstErr) {
            firstErr = err;
          }

          await updateAfterUploadPart(curTask, {
            err
          });
        }
      }
    }));

    if (firstErr) {
      throw firstErr;
    }

    const parts = (getCheckpointContent().parts_info || []).map(it => ({
      eTag: it.etag,
      partNumber: it.part_number
    }));
    const [err, res] = await safeAwait(completeMultipartUpload.call(this, {
      bucket,
      key,
      uploadId,
      parts
    }));

    if (err || !res) {
      triggerUploadEvent({
        type: exports.UploadEventType.CompleteMultipartUploadFailed
      });
      throw err;
    }

    triggerUploadEvent({
      type: exports.UploadEventType.CompleteMultipartUploadSucceed
    });
    triggerProgressEvent(TriggerProgressEventType.completeMultipartUploadSucceed);
    await rmCheckpointFile();

    if (this.opts.enableCRC && res.data.HashCrc64ecma && combineCRCInParts(getCheckpointContent()) !== res.data.HashCrc64ecma) {
      throw new TosClientError('crc of entire file mismatch.');
    }

    return res;
  };

  triggerDataTransfer(exports.DataTransferType.Started);
  const [err, res] = await safeAwait(handleTasks());

  if (err || !res) {
    triggerDataTransfer(exports.DataTransferType.Failed);
    throw err;
  }

  triggerDataTransfer(exports.DataTransferType.Succeed);
  return res;
}
/**
 * 即使 totalSize 是 0，也需要一个 Part，否则 Server 端会报错 read request body failed
 */

function getAllTasks(totalSize, partSize) {
  const tasks = [];

  for (let i = 0;; ++i) {
    const offset = i * partSize;
    const currPartSize = Math.min(partSize, totalSize - offset);
    tasks.push({
      offset,
      partSize: currPartSize,
      partNumber: i + 1
    });

    if ((i + 1) * partSize >= totalSize) {
      break;
    }
  }

  return tasks;
}

function getMakeRetryStream(file, task) {

  return undefined;
}

function getDefaultCheckpointFilePath(bucket, key) {
  const originPath = `${key}.${hashMd5$1(`${bucket}.${key}`, 'hex')}.upload`;
  const normalizePath = originPath.replace(/[\\/]/g, '');
  return normalizePath;
}

function combineCRCInParts(cp) {
  var _cp$file_info, _cp$parts_info$sort, _cp$parts_info;

  const size = ((_cp$file_info = cp.file_info) == null ? void 0 : _cp$file_info.file_size) || 0;
  let res = '0';
  const sortedPartsInfo = (_cp$parts_info$sort = (_cp$parts_info = cp.parts_info) == null ? void 0 : _cp$parts_info.sort == null ? void 0 : _cp$parts_info.sort((a, b) => a.part_number - b.part_number)) != null ? _cp$parts_info$sort : [];

  for (const part of sortedPartsInfo) {
    res = combineCrc64(res, part.hash_crc64ecma, Math.min(part.part_size, size - part.offset));
  }

  return res;
}

(function (ACLType) {
  ACLType["ACLPrivate"] = "private";
  ACLType["ACLPublicRead"] = "public-read";
  ACLType["ACLPublicReadWrite"] = "public-read-write";
  ACLType["ACLAuthenticatedRead"] = "authenticated-read";
  ACLType["ACLBucketOwnerRead"] = "bucket-owner-read";
  ACLType["ACLBucketOwnerFullControl"] = "bucket-owner-full-control"; // only works for object ACL

  ACLType["ACLBucketOwnerEntrusted"] = "bucket-owner-entrusted";
  /**
   * @private unstable value for object ACL
   */

  ACLType["ACLDefault"] = "default";
})(exports.ACLType || (exports.ACLType = {}));

(function (StorageClassType) {
  // storage-class will inherit from bucket if uploading object without `x-tos-storage-class` header
  StorageClassType["StorageClassStandard"] = "STANDARD";
  StorageClassType["StorageClassIa"] = "IA";
  StorageClassType["StorageClassArchiveFr"] = "ARCHIVE_FR";
  StorageClassType["StorageClassColdArchive"] = "COLD_ARCHIVE";
  StorageClassType["StorageClassIntelligentTiering"] = "INTELLIGENT_TIERING";
  StorageClassType["StorageClassArchive"] = "ARCHIVE";
})(exports.StorageClassType || (exports.StorageClassType = {}));

(function (MetadataDirectiveType) {
  MetadataDirectiveType["MetadataDirectiveCopy"] = "COPY";
  MetadataDirectiveType["MetadataDirectiveReplace"] = "REPLACE";
})(exports.MetadataDirectiveType || (exports.MetadataDirectiveType = {}));

(function (AzRedundancyType) {
  AzRedundancyType["AzRedundancySingleAz"] = "single-az";
  AzRedundancyType["AzRedundancyMultiAz"] = "multi-az";
})(exports.AzRedundancyType || (exports.AzRedundancyType = {}));

(function (PermissionType) {
  PermissionType["PermissionRead"] = "READ";
  PermissionType["PermissionWrite"] = "WRITE";
  PermissionType["PermissionReadAcp"] = "READ_ACP";
  PermissionType["PermissionWriteAcp"] = "WRITE_ACP";
  PermissionType["PermissionFullControl"] = "FULL_CONTROL";
  /**
   * @private unstable value for ACL
   */

  PermissionType["PermissionReadNONLIST"] = "READ_NON_LIST";
})(exports.PermissionType || (exports.PermissionType = {}));

(function (GranteeType) {
  GranteeType["GranteeGroup"] = "Group";
  GranteeType["GranteeUser"] = "CanonicalUser";
})(exports.GranteeType || (exports.GranteeType = {}));

(function (CannedType) {
  CannedType["CannedAllUsers"] = "AllUsers";
  CannedType["CannedAuthenticatedUsers"] = "AuthenticatedUsers";
})(exports.CannedType || (exports.CannedType = {}));

(function (HttpMethodType) {
  HttpMethodType["HttpMethodGet"] = "GET";
  HttpMethodType["HttpMethodPut"] = "PUT";
  HttpMethodType["HttpMethodPost"] = "POST";
  HttpMethodType["HttpMethodDelete"] = "DELETE";
  HttpMethodType["HttpMethodHead"] = "HEAD";
})(exports.HttpMethodType || (exports.HttpMethodType = {}));

(function (StorageClassInheritDirectiveType) {
  StorageClassInheritDirectiveType["StorageClassInheritDirectiveDestinationBucket"] = "DESTINATION_BUCKET";
  StorageClassInheritDirectiveType["StorageClassInheritDirectiveSourceObject"] = "SOURCE_OBJECT";
})(exports.StorageClassInheritDirectiveType || (exports.StorageClassInheritDirectiveType = {}));

(function (ReplicationStatusType) {
  ReplicationStatusType["Complete"] = "COMPLETE";
  ReplicationStatusType["Pending"] = "PENDING";
  ReplicationStatusType["Failed"] = "FAILED";
  ReplicationStatusType["Replica"] = "REPLICA";
})(exports.ReplicationStatusType || (exports.ReplicationStatusType = {}));

(function (LifecycleStatusType) {
  LifecycleStatusType["Enabled"] = "Enabled";
  LifecycleStatusType["Disabled"] = "Disabled";
})(exports.LifecycleStatusType || (exports.LifecycleStatusType = {}));

(function (RedirectType) {
  RedirectType["Mirror"] = "Mirror";
  RedirectType["Async"] = "Async";
})(exports.RedirectType || (exports.RedirectType = {}));

(function (StatusType) {
  StatusType["Enabled"] = "Enabled";
  StatusType["Disabled"] = "Disabled";
})(exports.StatusType || (exports.StatusType = {}));

(function (TierType) {
  TierType["TierStandard"] = "Standard";
  TierType["TierExpedited"] = "Expedited";
  TierType["TierBulk"] = "Bulk";
})(exports.TierType || (exports.TierType = {}));

(function (VersioningStatusType) {
  VersioningStatusType["Enabled"] = "Enabled";
  VersioningStatusType["Suspended"] = "Suspended";
  VersioningStatusType["NotSet"] = "";
  /**
   * @deprecated use `Enabled` instead
   */

  VersioningStatusType["Enable"] = "Enabled";
  /**
   * @deprecated use `NotSet` instead
   */

  VersioningStatusType["Disable"] = "";
})(exports.VersioningStatusType || (exports.VersioningStatusType = {}));

(function (AccessPointStatusType) {
  AccessPointStatusType["Ready"] = "READY";
  AccessPointStatusType["Creating"] = "CREATING";
  AccessPointStatusType["Created"] = "CREATED";
  AccessPointStatusType["Deleting"] = "DELETING";
})(exports.AccessPointStatusType || (exports.AccessPointStatusType = {}));

(function (TransferAccelerationStatusType) {
  TransferAccelerationStatusType["Activating"] = "AccelerationActivating";
  TransferAccelerationStatusType["Activated"] = "AccelerationActivated";
  TransferAccelerationStatusType["Terminated"] = "AccelerationTerminated";
})(exports.TransferAccelerationStatusType || (exports.TransferAccelerationStatusType = {}));

(function (MRAPMirrorBackRedirectPolicyType) {
  MRAPMirrorBackRedirectPolicyType["ClosestFirst"] = "Closest-First";
  MRAPMirrorBackRedirectPolicyType["LatestFirst"] = "Latest-First";
})(exports.MRAPMirrorBackRedirectPolicyType || (exports.MRAPMirrorBackRedirectPolicyType = {}));

async function headObject(input) {
  const normalizedInput = typeof input === 'string' ? {
    key: input
  } : input;
  const headers = normalizeHeadersKey(normalizedInput.headers);
  normalizedInput.headers = headers;
  const query = {};

  if (normalizedInput.versionId) {
    query.versionId = normalizedInput.versionId;
  }

  fillRequestHeaders(normalizedInput, ['ifMatch', 'ifModifiedSince', 'ifNoneMatch', 'ifUnmodifiedSince', 'ssecAlgorithm', 'ssecKey', 'ssecKeyMD5']);
  return this._fetchObject(input, 'HEAD', query, (normalizedInput == null ? void 0 : normalizedInput.headers) || {}, undefined, {
    handleResponse: res => {
      const result = { ...res.headers,
        ReplicationStatus: res.headers[TosHeader.HeaderReplicationStatus]
      };
      const info = getRestoreInfoFromHeaders(res.headers);

      if (info) {
        result.RestoreInfo = info;
      }

      return result;
    }
  });
}

async function uploadPartCopy(input) {
  const {
    uploadId,
    partNumber
  } = input;
  const headers = normalizeHeadersKey(input.headers);
  input.headers = headers;
  fillRequestHeaders(input, ['copySourceRange', 'copySourceSSECAlgorithm', 'copySourceSSECKey', 'copySourceSSECKeyMD5', 'ssecAlgorithm', 'ssecKey', 'ssecKeyMD5', 'trafficLimit']);

  if (input.srcBucket && input.srcKey) {
    var _headers$xTosCopyS;

    let copySource = getCopySourceHeaderValue(input.srcBucket, input.srcKey);

    if (input.srcVersionID) {
      copySource += `?versionId=${input.srcVersionID}`;
    }

    headers['x-tos-copy-source'] = (_headers$xTosCopyS = headers['x-tos-copy-source']) != null ? _headers$xTosCopyS : copySource;
  }

  if (input.copySourceRange == null && (input.copySourceRangeStart != null || input.copySourceRangeEnd != null)) {
    var _headers$xTosCopyS2;

    const start = input.copySourceRangeStart != null ? `${input.copySourceRangeStart}` : '';
    const end = input.copySourceRangeEnd != null ? `${input.copySourceRangeEnd}` : '';
    const copyRange = `bytes=${start}-${end}`;
    headers['x-tos-copy-source-range'] = (_headers$xTosCopyS2 = headers['x-tos-copy-source-range']) != null ? _headers$xTosCopyS2 : copyRange;
  }

  const [err, res] = await safeAwait(this._fetchObject(input, 'PUT', {
    partNumber,
    uploadId
  }, headers, undefined, {
    handleResponse(response) {
      return { ...response.data,
        SSECAlgorithm: response.headers[requestHeadersMap['ssecAlgorithm']],
        SSECKeyMD5: response.headers[requestHeadersMap['ssecKeyMD5']]
      };
    }

  }));

  if (err || !res || !res.data.ETag) {
    // TODO: throw TosServerErr
    throw err;
  }

  return res;
}

async function copyObject(input) {
  const headers = normalizeHeadersKey(input.headers);
  input.headers = headers;
  fillRequestHeaders(input, ['cacheControl', 'contentDisposition', 'contentEncoding', 'contentLanguage', 'contentType', 'expires', 'copySourceIfMatch', 'copySourceIfModifiedSince', 'copySourceIfNoneMatch', 'copySourceIfUnmodifiedSince', 'copySourceSSECAlgorithm', 'copySourceSSECKey', 'copySourceSSECKeyMD5', 'acl', 'grantFullControl', 'grantRead', 'grantReadAcp', 'grantWriteAcp', 'ssecAlgorithm', 'ssecKey', 'ssecKeyMD5', 'serverSideEncryption', 'metadataDirective', 'meta', 'websiteRedirectLocation', 'storageClass', 'trafficLimit', 'forbidOverwrite', 'ifMatch']);

  if (input.srcBucket && input.srcKey) {
    var _headers$xTosCopyS;

    let copySource = getCopySourceHeaderValue(input.srcBucket, input.srcKey);

    if (input.srcVersionID) {
      copySource += `?versionId=${input.srcVersionID}`;
    }

    headers['x-tos-copy-source'] = (_headers$xTosCopyS = headers['x-tos-copy-source']) != null ? _headers$xTosCopyS : copySource;
  }

  const [err, res] = await safeAwait(this._fetchObject(input, 'PUT', {}, headers));

  if (err || !res || !res.data.ETag) {
    // TODO: throw TosServerErr
    throw err;
  }

  return res;
}

(function (ResumableCopyEventType) {
  ResumableCopyEventType[ResumableCopyEventType["CreateMultipartUploadSucceed"] = 1] = "CreateMultipartUploadSucceed";
  ResumableCopyEventType[ResumableCopyEventType["CreateMultipartUploadFailed"] = 2] = "CreateMultipartUploadFailed";
  ResumableCopyEventType[ResumableCopyEventType["UploadPartCopySucceed"] = 3] = "UploadPartCopySucceed";
  ResumableCopyEventType[ResumableCopyEventType["UploadPartCopyFailed"] = 4] = "UploadPartCopyFailed";
  ResumableCopyEventType[ResumableCopyEventType["UploadPartCopyAborted"] = 5] = "UploadPartCopyAborted";
  ResumableCopyEventType[ResumableCopyEventType["CompleteMultipartUploadSucceed"] = 6] = "CompleteMultipartUploadSucceed";
  ResumableCopyEventType[ResumableCopyEventType["CompleteMultipartUploadFailed"] = 7] = "CompleteMultipartUploadFailed";
})(exports.ResumableCopyEventType || (exports.ResumableCopyEventType = {}));

const CHECKPOINT_FILE_NAME_PLACEHOLDER$1 = '@@checkpoint-file-placeholder@@';
const ABORT_ERROR_STATUS_CODE$1 = [403, 404, 405];
const DEFAULT_PART_SIZE$1 = 20 * 1024 * 1024; // 20 MB

async function resumableCopyObject(input) {
  var _checkpointRichInfo$r3, _checkpointRichInfo$r4, _checkpointRichInfo$r5;

  const {
    cancelToken
  } = input;

  const isCancel = () => cancelToken && !!cancelToken.reason;
  const {
    data: objectStats
  } = await headObject.call(this, {
    bucket: input.srcBucket,
    key: input.srcKey,
    versionId: input.srcVersionId
  });
  const etag = objectStats['etag'];
  const objectSize = +objectStats['content-length'];
  const checkpointRichInfo = await (async () => {

    if (typeof input.checkpoint === 'object') {
      return {
        record: input.checkpoint
      };
    }

    return {};
  })(); // check if file info is matched

  await (async () => {
    var _checkpointRichInfo$r;

    if ((_checkpointRichInfo$r = checkpointRichInfo.record) != null && _checkpointRichInfo$r.copy_source_object_info) {
      var _checkpointRichInfo$r2;

      const {
        last_modified,
        object_size
      } = (_checkpointRichInfo$r2 = checkpointRichInfo.record) == null ? void 0 : _checkpointRichInfo$r2.copy_source_object_info;

      if ( // TODO: `last-modified` aligns to number
      objectStats['last-modified'] !== last_modified || +objectStats['content-length'] !== object_size) {
        console.warn(`The file has been modified since ${new Date(last_modified)}, so the checkpoint file is invalid, and specified file will be uploaded again.`);
        delete checkpointRichInfo.record;
      }
    }
  })();
  const partSize = calculateSafePartSize(objectSize, input.partSize || ((_checkpointRichInfo$r3 = checkpointRichInfo.record) == null ? void 0 : _checkpointRichInfo$r3.part_size) || DEFAULT_PART_SIZE$1, true); // check partSize is matched

  if (checkpointRichInfo.record && checkpointRichInfo.record.part_size !== partSize) {
    console.warn('The partSize param does not equal the partSize in checkpoint file, ' + 'so the checkpoint file is invalid, and specified file will be uploaded again.');
    delete checkpointRichInfo.record;
  }

  let bucket = input.bucket || this.opts.bucket || '';
  const key = input.key;
  let uploadId = '';
  let tasks = [];
  const allTasks = getAllTasks$1(objectSize, partSize);
  const initConsumedBytes = (((_checkpointRichInfo$r4 = checkpointRichInfo.record) == null ? void 0 : _checkpointRichInfo$r4.parts_info) || []).filter(it => it.is_completed).reduce((prev, it) => prev + it.copy_source_range_end - it.copy_source_range_start + 1, 0);
  let consumedBytesForProgress = initConsumedBytes; // recorded tasks

  const recordedTasks = ((_checkpointRichInfo$r5 = checkpointRichInfo.record) == null ? void 0 : _checkpointRichInfo$r5.parts_info) || [];
  const recordedTaskMap = new Map();
  recordedTasks.forEach(it => recordedTaskMap.set(it.part_number, it));

  const getCheckpointContent = () => {
    const checkpointContent = {
      bucket,
      key,
      part_size: partSize,
      upload_id: uploadId,
      parts_info: recordedTasks,
      copy_source_object_info: {
        last_modified: objectStats['last-modified'],
        etag: objectStats.etag,
        hash_crc64ecma: objectStats['x-tos-hash-crc64ecma'] || '',
        object_size: +objectStats['content-length']
      }
    };
    return checkpointContent;
  };

  const triggerUploadEvent = e => {
    if (!input.copyEventListener) {
      return;
    }

    const event = {
      bucket,
      uploadId,
      key,
      ...e
    };

    if (checkpointRichInfo.filePath) {
      event.checkpointFile = checkpointRichInfo.filePath;
    }

    input.copyEventListener(event);
  };

  let TriggerProgressEventType;

  (function (TriggerProgressEventType) {
    TriggerProgressEventType[TriggerProgressEventType["start"] = 1] = "start";
    TriggerProgressEventType[TriggerProgressEventType["uploadPartSucceed"] = 2] = "uploadPartSucceed";
    TriggerProgressEventType[TriggerProgressEventType["completeMultipartUploadSucceed"] = 3] = "completeMultipartUploadSucceed";
  })(TriggerProgressEventType || (TriggerProgressEventType = {}));

  const triggerProgressEvent = type => {
    if (!input.progress) {
      return;
    }

    const percent = (() => {
      if (type === TriggerProgressEventType.start && objectSize === 0) {
        return 0;
      }

      return !objectSize ? 1 : consumedBytesForProgress / objectSize;
    })();

    if (consumedBytesForProgress === objectSize && type === TriggerProgressEventType.uploadPartSucceed) ; else {
      input.progress(percent, getCheckpointContent());
    }
  };

  const writeCheckpointFile = makeSerialAsyncTask(async () => {
  });

  const rmCheckpointFile = async () => {
  };
  /**
   *
   * @param task one part task
   * @param uploadPartRes upload part failed if `uploadPartRes` is Error
   */


  const updateAfterUploadPart = async (task, uploadPartRes) => {
    let existRecordTask = recordedTaskMap.get(task.partNumber);
    const rangeStart = task.offset;
    const rangeEnd = Math.min(task.offset + partSize - 1, objectSize - 1);

    if (!existRecordTask) {
      existRecordTask = {
        part_number: task.partNumber,
        copy_source_range_start: rangeStart,
        copy_source_range_end: rangeEnd,
        is_completed: false,
        etag: ''
      };
      recordedTasks.push(existRecordTask);
      recordedTaskMap.set(existRecordTask.part_number, existRecordTask);
    }

    if (!uploadPartRes.err) {
      existRecordTask.is_completed = true;
      existRecordTask.etag = uploadPartRes.res.ETag;
    }

    await writeCheckpointFile();
    const copyPartInfo = {
      partNumber: existRecordTask.part_number,
      copySourceRangeEnd: existRecordTask.copy_source_range_end,
      copySourceRangeStart: existRecordTask.copy_source_range_start
    };

    if (uploadPartRes.err) {
      const err = uploadPartRes.err;
      let type = exports.ResumableCopyEventType.UploadPartCopyFailed;

      if (err instanceof TosServerError) {
        if (ABORT_ERROR_STATUS_CODE$1.includes(err.statusCode)) {
          type = exports.ResumableCopyEventType.UploadPartCopyAborted;
        }
      }

      triggerUploadEvent({
        type,
        err,
        copyPartInfo
      });
      return;
    }

    copyPartInfo.etag = uploadPartRes.res.ETag;
    consumedBytesForProgress += copyPartInfo.copySourceRangeEnd - copyPartInfo.copySourceRangeStart + 1;
    triggerUploadEvent({
      type: exports.ResumableCopyEventType.UploadPartCopySucceed,
      copyPartInfo
    });
    triggerProgressEvent(TriggerProgressEventType.uploadPartSucceed);
  };

  if (checkpointRichInfo.record) {
    bucket = checkpointRichInfo.record.bucket;
    uploadId = checkpointRichInfo.record.upload_id; // checkpoint info exists, so need to calculate remain tasks

    const uploadedPartSet = new Set((checkpointRichInfo.record.parts_info || []).filter(it => it.is_completed).map(it => it.part_number));
    tasks = allTasks.filter(it => !uploadedPartSet.has(it.partNumber));
  } else {
    // createMultipartUpload will check bucket
    try {
      const {
        data: multipartRes
      } = await createMultipartUpload.call(this, cloneDeep(input));

      if (isCancel()) {
        throw new CancelError('cancel uploadFile');
      }

      bucket = multipartRes.Bucket;
      uploadId = multipartRes.UploadId;

      if (checkpointRichInfo.filePathIsPlaceholder) {
        var _checkpointRichInfo$f;

        checkpointRichInfo.filePath = (_checkpointRichInfo$f = checkpointRichInfo.filePath) == null ? void 0 : _checkpointRichInfo$f.replace(`${CHECKPOINT_FILE_NAME_PLACEHOLDER$1}`, getDefaultCheckpointFilePath$1({ ...input,
          bucket
        }));
      }

      triggerUploadEvent({
        type: exports.ResumableCopyEventType.CreateMultipartUploadSucceed
      });
    } catch (_err) {
      const err = _err;
      triggerUploadEvent({
        type: exports.ResumableCopyEventType.CreateMultipartUploadFailed,
        err
      });
      throw err;
    }

    tasks = allTasks;
  }

  const handleTasks = async () => {
    let firstErr = null;
    let index = 0; // TODO: how to test parallel does work, measure time is not right

    await Promise.all(Array.from({
      length: input.taskNum || 1
    }).map(async () => {
      while (true) {
        const currentIndex = index++;

        if (currentIndex >= tasks.length) {
          return;
        }

        const curTask = tasks[currentIndex];

        try {
          let copySource = getCopySourceHeaderValue(input.srcBucket, input.srcKey);

          if (input.srcVersionId) {
            copySource += `?versionId=${input.srcVersionId}`;
          }

          const copyRange = `bytes=${curTask.offset}-${curTask.offset + curTask.partSize - 1}`;
          const headers = { ...input.headers,
            ['x-tos-copy-source']: copySource,
            ['x-tos-copy-source-if-match']: etag,
            ['x-tos-copy-source-range']: copyRange
          };

          if (!curTask.partSize) {
            delete headers['x-tos-copy-source-range'];
          }

          const {
            data: uploadPartRes
          } = await uploadPartCopy.call(this, {
            bucket,
            key,
            uploadId,
            partNumber: curTask.partNumber,
            headers,
            trafficLimit: input.trafficLimit
          });

          if (isCancel()) {
            throw new CancelError('cancel resumableCopyObject');
          }

          await updateAfterUploadPart(curTask, {
            res: uploadPartRes
          });
        } catch (_err) {
          const err = _err;

          if (isCancelError$1(err)) {
            throw err;
          }

          if (isCancel()) {
            throw new CancelError('cancel resumableCopyObject');
          }

          if (!firstErr) {
            firstErr = err;
          }

          await updateAfterUploadPart(curTask, {
            err
          });
        }
      }
    }));

    if (firstErr) {
      throw firstErr;
    }

    const parts = (getCheckpointContent().parts_info || []).map(it => ({
      eTag: it.etag,
      partNumber: it.part_number
    }));
    const [err, res] = await safeAwait(completeMultipartUpload.call(this, {
      bucket,
      key,
      uploadId,
      parts
    }));

    if (err || !res) {
      triggerUploadEvent({
        type: exports.ResumableCopyEventType.CompleteMultipartUploadFailed
      });
      throw err;
    }

    triggerUploadEvent({
      type: exports.ResumableCopyEventType.CompleteMultipartUploadSucceed
    });
    triggerProgressEvent(TriggerProgressEventType.completeMultipartUploadSucceed);
    const sourceCRC64 = getCheckpointContent().copy_source_object_info.hash_crc64ecma;
    const actualCrc64 = res.data.HashCrc64ecma;

    if (this.opts.enableCRC && sourceCRC64 && actualCrc64 && sourceCRC64 !== actualCrc64) {
      throw new TosClientError(`validate file crc64 failed. Expect crc64 ${sourceCRC64}, actual crc64 ${actualCrc64}. Please try again.`);
    }

    await rmCheckpointFile();
    return res;
  };

  const handleEmptyObj = async () => {
    let copySource = getCopySourceHeaderValue(input.srcBucket, input.srcKey);

    if (input.srcVersionId) {
      copySource += `?versionId=${input.srcVersionId}`;
    }

    const headers = { ...input.headers,
      ['x-tos-copy-source']: copySource,
      ['x-tos-copy-source-if-match']: etag
    };
    const [err, res] = await safeAwait(copyObject.call(this, {
      bucket: input.bucket,
      key: input.key,
      headers,
      trafficLimit: input.trafficLimit
    }));

    if (err || !res) {
      triggerUploadEvent({
        type: exports.ResumableCopyEventType.UploadPartCopyFailed
      });
      throw err;
    }

    triggerProgressEvent(TriggerProgressEventType.completeMultipartUploadSucceed);
    triggerUploadEvent({
      type: exports.ResumableCopyEventType.UploadPartCopySucceed,
      copyPartInfo: {
        partNumber: 0,
        copySourceRangeStart: 0,
        copySourceRangeEnd: 0
      }
    });
    triggerUploadEvent({
      type: exports.ResumableCopyEventType.CompleteMultipartUploadSucceed
    });
    return { ...res,
      data: {
        ETag: res.headers['etag'] || '',
        Bucket: bucket,
        Key: key,
        Location: `http${this.opts.secure ? 's' : ''}://${bucket}.${this.opts.endpoint}/${key}`,
        VersionID: res.headers['x-tos-version-id'],
        HashCrc64ecma: res.headers['x-tos-hash-crc64ecma']
      }
    };
  };

  triggerProgressEvent(TriggerProgressEventType.start);
  return objectSize === 0 ? handleEmptyObj() : handleTasks();
}
function isCancelError$1(err) {
  return err instanceof CancelError;
}
/**
 * 即使 totalSize 是 0，也需要一个 Part，否则 Server 端会报错 read request body failed
 */

function getAllTasks$1(totalSize, partSize) {
  const tasks = [];

  for (let i = 0;; ++i) {
    const offset = i * partSize;
    const currPartSize = Math.min(partSize, totalSize - offset);
    tasks.push({
      offset,
      partSize: currPartSize,
      partNumber: i + 1
    });

    if ((i + 1) * partSize >= totalSize) {
      break;
    }
  }

  return tasks;
}

function getDefaultCheckpointFilePath$1(opts) {
  const originPath = [opts.srcBucket, opts.srcKey, opts.srcVersionId, opts.bucket, opts.key, 'copy'].filter(Boolean).join('.');
  const normalizePath = originPath.replace(/[\\/]/g, '');
  return normalizePath;
}

/**
 * @deprecated use getObjectV2 instead
 * @returns arraybuffer
 */

async function getObject(input) {
  const normalizedInput = typeof input === 'string' ? {
    key: input
  } : input;
  const query = {};

  if (normalizedInput.versionId) {
    query.versionId = normalizedInput.versionId;
  }

  const headers = normalizeHeadersKey(normalizedInput == null ? void 0 : normalizedInput.headers);
  const response = (normalizedInput == null ? void 0 : normalizedInput.response) || {};
  Object.keys(response).forEach(key => {
    const v = response[key];

    if (v != null) {
      query[`response-${key}`] = v;
    }
  }); // TODO: maybe need to return response's headers

  return this._fetchObject(input, 'GET', query, headers, undefined, {
    axiosOpts: {
      responseType: 'arraybuffer'
    }
  });
}
const BROWSER_DATATYPE = ['blob'];

function checkSupportDataType(dataType) {
  let environment = 'node';
  let supportDataTypes = [];

  {
    environment = 'browser';
    supportDataTypes = BROWSER_DATATYPE;
  }

  if (!supportDataTypes.includes(dataType)) {
    throw new TosClientError(`The value of \`dataType\` only supports \`${supportDataTypes.join(' | ')}\` in ${environment} environment`);
  }
}

async function getObjectV2(input) {
  const normalizedInput = typeof input === 'string' ? {
    key: input
  } : input;
  const headers = normalizeHeadersKey(normalizedInput.headers);
  normalizedInput.headers = headers;
  const dataType = normalizedInput.dataType || 'stream';
  normalizedInput.dataType = dataType;
  checkSupportDataType(dataType);
  const query = {};
  const response = (normalizedInput == null ? void 0 : normalizedInput.response) || {};
  Object.keys(response).forEach(key => {
    const v = response[key];

    if (v != null) {
      query[`response-${key}`] = v;
    }
  });
  fillRequestQuery(normalizedInput, query, ['versionId', 'process', 'saveBucket', 'saveObject', 'responseCacheControl', 'responseContentDisposition', 'responseContentEncoding', 'responseContentLanguage', 'responseContentType', 'responseExpires']);
  fillRequestHeaders(normalizedInput, ['ifMatch', 'ifModifiedSince', 'ifNoneMatch', 'ifUnmodifiedSince', 'ssecAlgorithm', 'ssecKey', 'ssecKeyMD5', 'range', 'trafficLimit']);

  if (normalizedInput.range == null && (normalizedInput.rangeStart != null || normalizedInput.rangeEnd != null)) {
    var _headers$range;

    const start = normalizedInput.rangeStart != null ? `${normalizedInput.rangeStart}` : '';
    const end = normalizedInput.rangeEnd != null ? `${normalizedInput.rangeEnd}` : '';
    const range = `bytes=${start}-${end}`;
    headers['range'] = (_headers$range = headers['range']) != null ? _headers$range : range;
  }

  const responseType = (() => {

    return 'arraybuffer';
  })();

  let consumedBytes = 0; // totalSize is unknown when start download

  let totalSize = -1;
  const {
    dataTransferStatusChange,
    progress
  } = normalizedInput;

  const triggerDataTransfer = (type, rwOnceBytes = 0) => {
    // request cancel will make rwOnceBytes < 0 in browser
    if (rwOnceBytes < 0) {
      return;
    }

    if (!dataTransferStatusChange && !progress) {
      return;
    }

    consumedBytes += rwOnceBytes;
    dataTransferStatusChange == null ? void 0 : dataTransferStatusChange({
      type,
      rwOnceBytes,
      consumedBytes,
      totalBytes: totalSize
    });

    const progressValue = (() => {
      // `totalSize` is unknown if it's in start or fail
      if (totalSize < 0) {
        return 0;
      }

      if (totalSize === 0) {
        if (type === exports.DataTransferType.Succeed) {
          return 1;
        }

        return 0;
      }

      return consumedBytes / totalSize;
    })();

    if (progressValue === 1) {
      if (type === exports.DataTransferType.Succeed) {
        progress == null ? void 0 : progress(progressValue);
      }
    } else {
      progress == null ? void 0 : progress(progressValue);
    }
  };

  triggerDataTransfer(exports.DataTransferType.Started);
  const [err, res] = await safeAwait(this._fetchObject(input, 'GET', query, headers, undefined, {
    axiosOpts: {
      responseType,
      onDownloadProgress: event => {
        totalSize = event.total;
        triggerDataTransfer(exports.DataTransferType.Rw, event.loaded - consumedBytes);
      }
    }
  }));

  if (err || !res) {
    triggerDataTransfer(exports.DataTransferType.Failed);
    throw err;
  }

  let resHeaders = res.headers;
  let newData = res.data;
  totalSize = +(resHeaders['content-length'] || 0);

  {
    // 浏览器环境
    if (dataType === 'blob') {
      newData = new Blob([res.data], {
        type: resHeaders['content-type']
      });
    }

    triggerDataTransfer(exports.DataTransferType.Succeed);
  }

  const actualRes = { ...res,
    data: {
      content: newData,
      etag: resHeaders['etag'] || '',
      lastModified: resHeaders['last-modified'] || '',
      hashCrc64ecma: resHeaders['x-tos-hash-crc64ecma'] || '',
      ReplicationStatus: resHeaders[TosHeader.HeaderReplicationStatus]
    }
  };
  const info = getRestoreInfoFromHeaders(resHeaders);

  if (info) {
    actualRes.data.RestoreInfo = info;
  }

  return actualRes;
}

async function getObjectToFile(input) {
  {
    throw new TosClientError("getObjectToFile doesn't support in browser environment");
  }
}

(function (DownloadEventType) {
  DownloadEventType[DownloadEventType["CreateTempFileSucceed"] = 1] = "CreateTempFileSucceed";
  DownloadEventType[DownloadEventType["CreateTempFileFailed"] = 2] = "CreateTempFileFailed";
  DownloadEventType[DownloadEventType["DownloadPartSucceed"] = 3] = "DownloadPartSucceed";
  DownloadEventType[DownloadEventType["DownloadPartFailed"] = 4] = "DownloadPartFailed";
  DownloadEventType[DownloadEventType["DownloadPartAborted"] = 5] = "DownloadPartAborted";
  DownloadEventType[DownloadEventType["RenameTempFileSucceed"] = 6] = "RenameTempFileSucceed";
  DownloadEventType[DownloadEventType["RenameTempFileFailed"] = 7] = "RenameTempFileFailed";
})(exports.DownloadEventType || (exports.DownloadEventType = {}));
async function downloadFile(input) {

  {
    throw new TosClientError('`downloadFile` is not supported in browser environment');
  }
}

// @ts-nocheck
const SIG_QUERY = {
  algorithm: 'tos-algorithm',
  expiration: 'tos-expiration',
  signame: 'tos-signame',
  signature: 'tos-signature',
  v4_algorithm: 'X-Tos-Algorithm',
  v4_credential: 'X-Tos-Credential',
  v4_date: 'X-Tos-Date',
  v4_expires: 'X-Tos-Expires',
  v4_signedHeaders: 'X-Tos-SignedHeaders',
  v4_security_token: 'X-Tos-Security-Token',
  v4_signature: 'X-Tos-Signature',
  v4_content_sha: 'X-Tos-Content-Sha256',
  v4_policy: 'X-Tos-Policy'
};
function isDefaultPort(port) {
  if (port && port !== 80 && port !== 443) {
    return false;
  }

  return true;
}
/**
 * @api private
 */

const v4Identifier = 'request';
/**
 * @api private
 */

class SignersV4 {
  constructor(_opt, _credentials) {
    this.options = void 0;
    this.credentials = void 0;

    this.signature = (opt, expiredAt, credentials) => {
      if (!credentials) {
        credentials = this.credentials;
      }

      const parts = [];
      const datatime = opt.datetime;
      const credString = this.credentialString(datatime);
      parts.push(this.options.algorithm + ' Credential=' + credentials.GetAccessKey() + '/' + credString); // console.log(this.algorithm + ' Credential=' +
      //   credentials.accessKeyId + '/' + credString)

      parts.push('SignedHeaders=' + this.signedHeaders(opt));
      parts.push('Signature=' + this.authorization(opt, credentials, 0));
      return parts.join(', ');
    };

    this.signatureHeader = (opt, expiredAt, credentials) => {
      // const datetime = (new Date(new Date().toUTCString())).Format("yyyyMMddTHHmmssZ")
      opt.datetime = this.getDateTime();
      const header = new Map();
      /* istanbul ignore if */

      if (!opt.headers) {
        const h = {};
        opt.headers = h;
      }

      opt.headers.host = `${opt.host}`;
      /* istanbul ignore if */

      if (!isDefaultPort(opt.port)) {
        opt.headers.host += ':' + opt.port;
      }
      /* istanbul ignore if */


      if (opt.endpoints) {
        opt.headers.host = `${this.options.bucket}.${opt.endpoints}`;
      }

      header.set('host', opt.headers.host); // opt.endpoints as string)

      header.set('x-tos-date', opt.datetime); // opt.datetime)

      /* istanbul ignore if
        if (opt.endpoints) {
            let bucket = this.options.bucket;
            if (opt.bucket) {
                bucket = opt.bucket;
            }
            if (!opt.path || opt.path === '/' || opt.path === `/${bucket}`) {
                opt.path = '/' + bucket;
            } else {
                opt.path = '/' + bucket + opt.path;
            }
        }
        */

      header.set('x-tos-content-sha256', this.hexEncodedBodyHash());

      if (this.options.securityToken) {
        header.set('x-tos-security-token', this.options.securityToken);
      } // x-tos- must to be signatured


      header.forEach((value, key) => {
        if (key.startsWith('x-tos')) {
          opt.headers[key] = value;
        }
      });
      opt.path = this.getEncodePath(opt.path);
      const sign = this.signature(opt, 0, credentials);
      header.set('authorization', sign);
      return header;
    };

    this.gnrCopySig = (opt, credentials) => {
      return {
        key: '',
        value: ''
      };
    };

    this.getSignature = (opt, expiredAt) => {
      return {
        key: '',
        value: ''
      };
    };

    this.getSignatureQuery = (opt, expiredAt) => {
      opt.datetime = this.getDateTime();

      if (!opt.headers) {
        const h = {};
        opt.headers = h;
      }

      opt.headers.host = `${opt.host}`;

      if (!isDefaultPort(opt.port)) {
        opt.headers.host += ':' + opt.port;
      }

      opt.path = this.getEncodePath(opt.path);

      if (opt.endpoints) {
        opt.headers.host = `${this.options.bucket}.${opt.endpoints}`; // opt.path = `${opt.path}`;
      }

      opt.headers[SIG_QUERY.v4_date] = opt.datetime;
      const credString = this.credentialString(opt.datetime);
      const res = { ...(opt.query || {}),
        [SIG_QUERY.v4_algorithm]: this.options.algorithm,
        [SIG_QUERY.v4_content_sha]: this.hexEncodedBodyHash(),
        [SIG_QUERY.v4_credential]: this.credentials.GetAccessKey() + '/' + credString,
        [SIG_QUERY.v4_date]: opt.datetime,
        [SIG_QUERY.v4_expires]: '' + expiredAt,
        [SIG_QUERY.v4_signedHeaders]: this.signedHeaders(opt)
      };

      if (this.options.securityToken) {
        res[SIG_QUERY.v4_security_token] = this.options.securityToken;
      }

      opt.query = getSortedQueryString(res);
      res[SIG_QUERY.v4_signature] = this.authorization(opt, this.credentials, expiredAt);
      return res;
    };

    this.getSignaturePolicyQuery = (opt, expiredAt) => {
      opt.datetime = this.getDateTime();
      const credString = this.credentialString(opt.datetime);
      const res = {
        [SIG_QUERY.v4_algorithm]: this.options.algorithm,
        [SIG_QUERY.v4_credential]: this.credentials.GetAccessKey() + '/' + credString,
        [SIG_QUERY.v4_date]: opt.datetime,
        [SIG_QUERY.v4_expires]: '' + expiredAt,
        [SIG_QUERY.v4_policy]: stringify$1(parse$1(JSON.stringify(opt.policy), 'utf-8'), 'base64')
      };

      if (this.options.securityToken) {
        res[SIG_QUERY.v4_security_token] = this.options.securityToken;
      }

      opt.query = getSortedQueryString(res);
      res[SIG_QUERY.v4_signature] = this.authorization(opt, this.credentials, expiredAt);
      return res;
    };

    this.hexEncodedBodyHash = () => {
      return 'UNSIGNED-PAYLOAD'; // return this.hexEncodedHash('');
    };

    this.authorization = (opt, credentials, expiredAt) => {
      /* istanbul ignore if */
      if (!opt.datetime) {
        return '';
      }

      const signingKey = this.getSigningKey(credentials, opt.datetime.substr(0, 8)); // console.log(
      // 'signingKey:',
      //  signingKey,
      //  'sign:',
      //  this.stringToSign(opt.datetime, opt)
      //  );

      return hmacSha256$1(signingKey, this.stringToSign(opt.datetime, opt), 'hex');
    };

    this.getDateTime = () => {
      const date = new Date(new Date().toUTCString());
      const datetime = date.toISOString().replace(/\..+/, '').replace(/-/g, '').replace(/:/g, '') + 'Z';
      return datetime;
    };

    this.credentialString = datetime => {
      return this.createScope(datetime.substr(0, 8), this.options.region, this.options.serviceName);
    };

    this.createScope = (date, region, serviceName) => {
      return [date.substr(0, 8), region, serviceName, v4Identifier].join('/');
    };

    this.getSigningKey = (credentials, date) => {
      const kDate = hmacSha256$1(credentials.GetSecretKey(), date);
      const kRegion = hmacSha256$1(kDate, this.options.region);
      const kService = hmacSha256$1(kRegion, this.options.serviceName);
      const signingKey = hmacSha256$1(kService, v4Identifier);
      return signingKey;
    };

    this.stringToSign = (datetime, opt) => {
      /* istanbul ignore if */
      if (!this.options.algorithm) {
        return '';
      }

      const parts = [];
      parts.push(this.options.algorithm);
      parts.push(datetime);
      parts.push(this.credentialString(datetime));
      const canonicalString = 'policy' in opt ? this.canonicalStringPolicy(opt) : this.canonicalString(opt); // console.log('canonicalString',this.canonicalString(opt),' code:',this.hexEncodedHash(this.canonicalString(opt)));

      parts.push(this.hexEncodedHash(canonicalString));
      return parts.join('\n');
    };

    this.hexEncodedHash = string => {
      return hashSha256$1(string, 'hex');
    };

    this.canonicalString = opt => {
      const parts = [];
      parts.push(opt.method);
      parts.push(opt.path);
      parts.push(this.getEncodePath(opt.query, false));
      parts.push(this.canonicalHeaders(opt) + '\n');
      parts.push(this.signedHeaders(opt));
      parts.push(this.hexEncodedBodyHash());
      return parts.join('\n');
    };

    this.canonicalStringPolicy = opt => {
      const parts = [];
      parts.push(this.getEncodePath(opt.query, false));
      parts.push(this.hexEncodedBodyHash());
      return parts.join('\n');
    };

    this.canonicalHeaders = opt => {
      const parts = [];
      const needSignHeaders = getNeedSignedHeaders(opt.headers);

      for (let key of needSignHeaders) {
        const value = opt.headers[key];
        key = key.toLowerCase();
        parts.push(key + ':' + this.canonicalHeaderValues(value.toString()));
      }

      return parts.join('\n');
    };

    this.canonicalHeaderValues = values => {
      return values.replace(/\s+/g, ' ').replace(/^\s+|\s+$/g, '');
    };

    this.signedHeaders = opt => {
      const keys = [];
      const needSignHeaders = getNeedSignedHeaders(opt.headers);

      for (let key of needSignHeaders) {
        key = key.toLowerCase();
        keys.push(key);
      }

      return keys.sort().join(';');
    };

    this.options = _opt;
    this.credentials = _credentials;
  }
  /*
   * normal v4 signature
   * */


  /**
   * ! * ' () aren't transformed by encodeUrl, so they need be handled
   */
  getEncodePath(path, encodeAll = true) {
    if (!path) {
      return '';
    }

    let tmpPath = path;

    if (encodeAll) {
      tmpPath = path.replace(/%2F/g, '/');
    }

    tmpPath = tmpPath.replace(/\(/g, '%28');
    tmpPath = tmpPath.replace(/\)/g, '%29');
    tmpPath = tmpPath.replace(/!/g, '%21');
    tmpPath = tmpPath.replace(/\*/g, '%2A');
    tmpPath = tmpPath.replace(/\'/g, '%27');
    return tmpPath;
  }

}
class ISigV4Credentials {
  constructor(securityToken, secretAccessKey, accessKeyId) {
    this.securityToken = void 0;
    this.secretAccessKey = void 0;
    this.accessKeyId = void 0;
    this.accessKeyId = accessKeyId;
    this.secretAccessKey = secretAccessKey;
    this.securityToken = securityToken;
  }

  GetAccessKey() {
    return this.accessKeyId;
  }

  GetSecretKey() {
    return this.secretAccessKey;
  }

}

function getNeedSignedHeaders(headers) {
  const needSignHeaders = [];
  Object.keys(headers || {}).forEach(key => {
    if (key === 'host' || key.startsWith('x-tos-')) {
      if (headers[key] != null) {
        needSignHeaders.push(key);
      }
    }
  });
  return needSignHeaders.sort();
}

// auto generated by scripts/build.js
var version = '2.7.4';

/**
 * forked from  https://github.com/bigmeow/axios-miniprogram-adapter
 * author bigMeow
 */
const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/='; // encoder

function encoder(input) {
  const str = String(input); // initialize result and counter

  let block;
  let charCode;
  let idx = 0;
  let map = chars;
  let output = '';

  for (; // if the next str index does not exist:
  //   change the mapping table to "="
  //   check if d has no fractional digits
  str.charAt(idx | 0) || (map = '=', idx % 1); // "8 - idx % 1 * 8" generates the sequence 2, 4, 6, 8
  output += map.charAt(63 & block >> 8 - idx % 1 * 8)) {
    charCode = str.charCodeAt(idx += 3 / 4);

    if (charCode > 0xff) {
      throw new Error("'btoa' failed: The string to be encoded contains characters outside of the Latin1 range.");
    } //@ts-expect-error


    block = block << 8 | charCode;
  }

  return output;
}

/**
 * forked from  https://github.com/bigmeow/axios-miniprogram-adapter
 * author bigMeow
 */
let platFormName = "wexin"
/* weixin */
;
/**
 * 获取各个平台的请求函数
 */

function getRequest() {
  switch (true) {
    case typeof wx === 'object':
      platFormName = "wexin"
      /* weixin */
      ;
      return wx.request.bind(wx);

    case typeof swan === 'object':
      platFormName = "baidu"
      /* baidu */
      ;
      return swan.request.bind(swan);

    case typeof dd === 'object':
      platFormName = "dd"
      /* dingding */
      ; // https://open.dingtalk.com/document/orgapp-client/send-network-requests

      return dd.httpRequest.bind(dd);

    case typeof my === 'object':
      /**
       * remark:
       * 支付宝客户端已不再维护 my.httpRequest，建议使用 my.request。另外，钉钉客户端尚不支持 my.request。若在钉钉客户端开发小程序，则需要使用 my.httpRequest。
       * my.httpRequest的请求头默认值为{'content-type': 'application/x-www-form-urlencoded'}。
       * my.request的请求头默认值为{'content-type': 'application/json'}。
       * 还有个 dd.httpRequest
       */
      platFormName = "alipay"
      /* zhifubao */
      ;
      return (my.request || my.httpRequest).bind(my);

    default:
      return wx.request.bind(wx);
  }
}
/**
 * 处理各平台返回的响应数据，抹平差异
 * @param mpResponse
 * @param config axios处理过的请求配置对象
 * @param request 小程序的调用发起请求时，传递给小程序api的实际配置
 */

function transformResponse(mpResponse, config, mpRequestOption) {
  const headers = mpResponse.header || mpResponse.headers;
  const status = mpResponse.statusCode || mpResponse.status;
  let statusText = '';

  if (status === 200) {
    statusText = 'OK';
  } else if (status === 400) {
    statusText = 'Bad Request';
  }

  const response = {
    data: mpResponse.data,
    status,
    statusText,
    headers,
    config,
    request: mpRequestOption
  };
  return response;
}
/**
 * 处理各平台返回的错误信息，抹平差异
 * @param error 小程序api返回的错误对象
 * @param reject 上层的promise reject 函数
 * @param config
 */

function transformError(error, reject, config) {
  switch (platFormName) {
    case "wexin"
    /* weixin */
    :
      if (error.errMsg.indexOf('request:fail abort') !== -1) {
        // Handle request cancellation (as opposed to a manual cancellation)
        reject(createError('Request aborted', config, 'ECONNABORTED', ''));
      } else if (error.errMsg.indexOf('timeout') !== -1) {
        // timeout
        reject(createError('timeout of ' + config.timeout + 'ms exceeded', config, 'ECONNABORTED', ''));
      } else {
        // NetWordError
        reject(createError('Network Error', config, null, ''));
      }

      break;

    case "dd"
    /* dingding */
    :
    case "alipay"
    /* zhifubao */
    :
      // https://docs.alipay.com/mini/api/network
      if ([14, 19].includes(error.error)) {
        reject(createError('Request aborted', config, 'ECONNABORTED', '', error));
      } else if ([13].includes(error.error)) {
        // timeout
        reject(createError('timeout of ' + config.timeout + 'ms exceeded', config, 'ECONNABORTED', '', error));
      } else {
        // NetWordError
        reject(createError('Network Error', config, null, '', error));
      }

      break;

    case "baidu"
    /* baidu */
    :
      // TODO error.errCode
      reject(createError('Network Error', config, null, ''));
      break;
  }
}
/**
 * 将axios的请求配置，转换成各个平台都支持的请求config
 * @param config
 */

function transformConfig(config) {
  if (["alipay"
  /* zhifubao */
  , "dd"
  /* dingding */
  ].includes(platFormName)) {
    var _config$headers;

    config.headers = config.header;
    delete config.header;

    if ("dd"
    /* dingding */
    === platFormName && config.method !== 'GET' && ((_config$headers = config.headers) == null ? void 0 : _config$headers['Content-Type']) === 'application/json' && Object.prototype.toString.call(config.data) === '[object Object]') {
      // Content-Type为application/json时，data参数只支持json字符串，需要手动调用JSON.stringify进行序列化
      config.data = JSON.stringify(config.data);
    }
  }

  return config;
}

//@ts-ignore

const isJSONstr = str => {
  try {
    return typeof str === 'string' && str.length && (str = JSON.parse(str)) && Object.prototype.toString.call(str) === '[object Object]';
  } catch (error) {
    return false;
  }
};

function mpAdapter(config, {
  transformRequestOption = requestOption => requestOption
} = {}) {
  const request = getRequest();
  return new Promise((resolve, reject) => {
    let requestTask;
    let requestData = config.data;
    let requestHeaders = config.headers; // baidu miniprogram only support upperCase

    let requestMethod = config.method && config.method.toUpperCase() || 'GET'; // miniprogram network request config

    const mpRequestOption = {
      method: requestMethod,
      url: buildURL(buildFullPath(config.baseURL, config.url), config.params, config.paramsSerializer),
      timeout: config.timeout,
      // Listen for success
      success: mpResponse => {
        const response = transformResponse(mpResponse, config, mpRequestOption);
        settle(resolve, reject, response);
      },
      // Handle request Exception
      fail: error => {
        transformError(error, reject, config);
      },

      complete() {
        requestTask = undefined;
      }

    }; // HTTP basic authentication

    if (config.auth) {
      const [username, password] = [config.auth.username || '', config.auth.password || ''];
      requestHeaders.Authorization = 'Basic ' + encoder(username + ':' + password);
    } // Add headers to the request


    utils.forEach(requestHeaders, function setRequestHeader(_val, key) {
      const _header = key.toLowerCase();

      if (typeof requestData === 'undefined' && _header === 'content-type' || _header === 'referer') {
        // Remove Content-Type if data is undefined
        // And the miniprogram document said that '设置请求的 header，header 中不能设置 Referer'
        delete requestHeaders[key];
      }
    });
    mpRequestOption.header = requestHeaders; // Add responseType to request if needed

    if (config.responseType) {
      mpRequestOption.responseType = config.responseType;
    }

    if (config.cancelToken) {
      // Handle cancellation
      config.cancelToken.promise.then(function onCanceled(cancel) {
        if (!requestTask) {
          return;
        }

        requestTask.abort();
        reject(cancel); // Clean up request

        requestTask = undefined;
      });
    } // Converting JSON strings to objects is handed over to the MiniPrograme


    if (isJSONstr(requestData)) {
      requestData = JSON.parse(requestData);
    }

    if (requestData !== undefined) {
      mpRequestOption.data = requestData;
    }

    requestTask = request(transformRequestOption(transformConfig(mpRequestOption)));
  });
}

class TOSBase {
  constructor(_opts) {
    this.opts = void 0;
    this.axiosInst = void 0;
    this.userAgent = void 0;
    this.httpAgent = void 0;
    this.httpsAgent = void 0;

    this.getObjectPath = opts => {
      const actualBucket = typeof opts !== 'string' && opts.bucket || this.opts.bucket;
      const actualKey = typeof opts === 'string' ? opts : opts.key;

      if (!actualBucket) {
        throw new TosClientError('Must provide bucket param');
      }

      return `/${actualBucket}/${encodeURIComponent(actualKey)}`;
    };

    this.setObjectContentTypeHeader = (input, headers) => {
      if (headers['content-type'] != null) {
        return;
      }

      let mimeType = DEFAULT_CONTENT_TYPE;
      const key = getObjectInputKey(input);

      if (this.opts.autoRecognizeContentType) {
        mimeType = lookupMimeType(key) || mimeType;
      }

      if (mimeType) {
        headers['content-type'] = mimeType;
      }
    };

    this.getNormalDataFromError = getNormalDataFromError;
    this.opts = this.normalizeOpts(_opts);

    this.userAgent = this.getUserAgent();
    this.axiosInst = makeAxiosInst(this.opts.maxRetryCount);
  }

  normalizeOpts(_opts) {
    var _opts$enableCRC, _opts$enableCRC2;

    // 对字符串参数做 trim 操作
    const trimKeys = ['accessKeyId', 'accessKeySecret', 'stsToken', 'region', 'endpoint'];
    trimKeys.forEach(key => {
      const value = _opts[key];

      if (typeof value === 'string') {
        // maybe undefined
        _opts[key] = value.trim();
      }
    });
    const mustKeys = ['accessKeyId', 'accessKeySecret', 'region'];
    const mustKeysErrorStr = mustKeys.filter(key => !_opts[key]).join(', ');

    if (mustKeysErrorStr) {
      throw new TosClientError(`lack params: ${mustKeysErrorStr}.`);
    }

    const endpoint = _opts.endpoint || getEndpoint(_opts.region);

    if (!endpoint) {
      throw new TosClientError(`the value of param region is invalid, correct values are cn-beijing, cn-nantong etc.`);
    }

    if (endpoint.includes('s3')) {
      throw new TosClientError(`do not support s3 endpoint, please use tos endpoint.`);
    }

    const secure = _opts.secure == null ? true : !!_opts.secure;

    const _default = (v, defaultValue) => v == null ? defaultValue : v;

    const enableCRC = (_opts$enableCRC = _opts.enableCRC) != null ? _opts$enableCRC : false;

    if (enableCRC && 'browser' === 'browser') {
      throw new TosClientError('not support crc in browser environment');
    }

    return { ..._opts,
      endpoint,
      secure,
      enableVerifySSL: _default(_opts.enableVerifySSL, true),
      autoRecognizeContentType: _default(_opts.autoRecognizeContentType, true),
      requestTimeout: _default(_opts.requestTimeout, 120000),
      connectionTimeout: _default(_opts.connectionTimeout, 10000),
      maxConnections: _default(_opts.maxConnections, 1024),
      idleConnectionTime: _default(_opts.idleConnectionTime, 30000),
      maxRetryCount: _default(_opts.maxRetryCount, 3),
      enableCRC: (_opts$enableCRC2 = _opts.enableCRC) != null ? _opts$enableCRC2 : false,
      requestAdapter: getAdapter()
    };
  }

  getUserAgent() {
    // ve-tos-go-sdk/v2.0.0 (linux/amd64;go1.17.0)
    const language =  'browserjs' ;
    const sdkVersion = `ve-tos-${language}-sdk/v${version}`;

    {
      return sdkVersion;
    }
  }

  async fetch(method, path, query, headers, body, opts) {
    const handleResponse = (opts == null ? void 0 : opts.handleResponse) || (res => res.data);

    const needMd5 = (opts == null ? void 0 : opts.needMd5) || false;

    if (body && needMd5) {
      const md5String = hashMd5$1(JSON.stringify(body), 'base64');
      headers['content-md5'] = md5String;
    }

    const [endpoint, newPath] = (() => {
      if (opts != null && opts.subdomainBucket && this.opts.forcePathStyle) {
        return [this.opts.endpoint, `/${opts.subdomainBucket}${path}`];
      } // if isCustomDomain true, not add subdomainBucket


      if (opts != null && opts.subdomainBucket && !this.opts.isCustomDomain) {
        // endpoint is ip address
        if (/^(\d|:)/.test(this.opts.endpoint)) {
          return [this.opts.endpoint, `/${opts.subdomainBucket}${path}`];
        }

        return [`${opts == null ? void 0 : opts.subdomainBucket}.${this.opts.endpoint}`, path];
      }

      return [this.opts.endpoint, path];
    })();

    path = newPath;
    headers = encodeHeadersValue(headers);
    const signOpt = {
      // TODO: delete endpoints and buckets
      endpoints: undefined,
      bucket: '',
      method,
      headers: { ...headers
      },
      path,
      query: getSortedQueryString(query),
      host: endpoint
    };
    const signv4 = new ISigV4Credentials(this.opts.stsToken, this.opts.accessKeySecret, this.opts.accessKeyId);
    const sig = new SignersV4({
      algorithm: 'TOS4-HMAC-SHA256',
      region: this.opts.region,
      serviceName: 'tos',
      bucket: '',
      securityToken: this.opts.stsToken
    }, signv4);
    const signatureHeaders = sig.signatureHeader(signOpt);
    const reqHeaders = { ...headers
    };
    const reqOpts = {
      method,
      baseURL: `http${this.opts.secure ? 's' : ''}://${endpoint}`,
      url: path,
      params: query,
      headers: reqHeaders,
      data: body
    };
    signatureHeaders.forEach((value, key) => {
      reqOpts.headers[key] = value;
    });
    const normalizedProxy = normalizeProxy(this.opts.proxy);

    if (normalizedProxy != null && normalizedProxy.url && !this.opts.proxyHost) {
      // proxy for nodejs middleware server
      reqOpts.baseURL = normalizedProxy.url;

      if (normalizedProxy != null && normalizedProxy.needProxyParams) {
        reqOpts.params['x-proxy-tos-host'] = endpoint;
        delete reqHeaders['host'];
      }
    } else if (this.opts.proxyHost) {
      if (!this.opts.proxyPort) {
        throw new TosClientError('The `proxyPort` is required if `proxyHost` is truly.');
      } // proxy for general proxy server


      reqOpts.proxy = {
        host: this.opts.proxyHost,
        port: this.opts.proxyPort,
        protocol: 'http'
      };
    }

    reqHeaders['user-agent'] = this.userAgent;

    if (this.opts.requestTimeout > 0 && this.opts.requestTimeout !== Infinity) {
      reqOpts.timeout = this.opts.requestTimeout;
    }

    try {
      const logReqOpts = { ...reqOpts
      };
      delete logReqOpts.httpAgent;
      delete logReqOpts.httpsAgent;
      TOS('reqOpts: ', logReqOpts);
      const res = await this.axiosInst({ ...{
          maxBodyLength: Infinity,
          maxContentLength: Infinity,
          adapter: this.opts.requestAdapter
        },
        ...reqOpts,
        ...((opts == null ? void 0 : opts.axiosOpts) || {}),
        [retrySignatureNamespace]: {
          signOpt,
          sigInst: sig
        }
      });
      const data = handleResponse(res);
      return {
        data,
        statusCode: res.status,
        headers: res.headers,
        requestId: res.headers['x-tos-request-id'],
        id2: res.headers['x-tos-id-2']
      };
    } catch (err) {
      var _err$response, _err$response$headers;

      if (axios.isAxiosError(err) && (_err$response = err.response) != null && (_err$response$headers = _err$response.headers) != null && _err$response$headers['x-tos-request-id']) {
        // it's ServerError only if `RequestId` exists
        const response = err.response;
        TOS('TosServerError response: ', response);
        const err2 = new TosServerError(response);
        throw err2;
      } // it is neither ServerError nor ClientError, it's other error


      TOS('err: ', err);
      throw err;
    }
  }

  async fetchBucket(bucket, method, query, headers, body, opts) {
    const actualBucket = bucket || this.opts.bucket;

    if (!actualBucket) {
      throw new TosClientError('Must provide bucket param');
    }

    return this.fetch(method, '/', query, headers, body, { ...opts,
      subdomainBucket: actualBucket
    });
  }

  async _fetchObject(input, method, query, headers, body, opts) {
    const actualBucket = typeof input !== 'string' && input.bucket || this.opts.bucket;
    const actualKey = typeof input === 'string' ? input : input.key;

    if (!actualBucket) {
      throw new TosClientError('Must provide bucket param');
    }

    validateObjectName(actualKey);
    return this.fetch(method, `/${encodeURIComponent(actualKey)}`, query, headers, body, { ...opts,
      subdomainBucket: actualBucket
    });
  }

  getSignatureQuery(input) {
    const signv4 = new ISigV4Credentials(this.opts.stsToken, this.opts.accessKeySecret, this.opts.accessKeyId);
    const sig = new SignersV4({
      algorithm: 'TOS4-HMAC-SHA256',
      region: this.opts.endpoint,
      serviceName: 'tos',
      // SignV4 uses this.options.bucket, so set it here
      bucket: input.bucket,
      securityToken: this.opts.stsToken
    }, signv4);

    if ('policy' in input) {
      return sig.getSignaturePolicyQuery({
        policy: input.policy
      }, input.expires);
    } else {
      return sig.getSignatureQuery({
        method: input.method,
        path: input.path,
        endpoints: input.subdomain ? input.endpoint : undefined,
        host: input.endpoint,
        query: input.query
      }, input.expires);
    }
  }

  normalizeBucketInput(input) {
    return typeof input === 'string' ? {
      bucket: input
    } : input;
  }

  normalizeObjectInput(input) {
    return typeof input === 'string' ? {
      key: input
    } : input;
  }

}

function getAdapter() {

  if (typeof window !== 'undefined' && typeof window.location !== 'undefined') {
    // browser env
    return undefined;
  }

  switch (true) {
    case typeof wx !== 'undefined':
    case typeof swan !== 'undefined':
    case typeof dd !== 'undefined':
    case typeof my !== 'undefined':
      return mpAdapter;

    case typeof uni !== 'undefined':
      return uniappAdapter;

    default:
      return undefined;
  }
}

/**
 *
 * @deprecated use listObjectsType2 instead
 * @returns
 */


async function listObjects(input = {}) {
  const {
    bucket,
    ...nextQuery
  } = input;
  const ret = await this.fetchBucket(input.bucket, 'GET', covertCamelCase2Kebab(nextQuery), {});
  const arrayProp = makeArrayProp(ret.data);
  arrayProp('CommonPrefixes');
  arrayProp('Contents');
  arrayProp('Versions');
  arrayProp('DeleteMarkers');
  return ret;
}
async function listObjectVersions(input = {}) {
  const {
    bucket,
    ...nextQuery
  } = input;
  const ret = await this.fetchBucket(input.bucket, 'GET', covertCamelCase2Kebab({
    versions: '',
    ...nextQuery
  }), {});
  const arrayProp = makeArrayProp(ret.data);
  arrayProp('CommonPrefixes');
  arrayProp('Versions');
  arrayProp('DeleteMarkers');
  return ret;
}

const DefaultListMaxKeys = 1000;
async function listObjectsType2(input = {}) {
  const {
    listOnlyOnce = false
  } = input;
  let output;

  if (!input.maxKeys) {
    input.maxKeys = DefaultListMaxKeys;
  }

  if (listOnlyOnce) {
    output = await listObjectsType2Once.call(this, input);
  } else {
    const maxKeys = input.maxKeys;
    let params = { ...input,
      maxKeys
    };

    while (true) {
      const res = await listObjectsType2Once.call(this, params);

      if (output == null) {
        output = res;
      } else {
        output = { ...res,
          data: output.data
        };
        output.data.KeyCount += res.data.KeyCount;
        output.data.IsTruncated = res.data.IsTruncated;
        output.data.NextContinuationToken = res.data.NextContinuationToken;
        output.data.Contents = output.data.Contents.concat(res.data.Contents);
        output.data.CommonPrefixes = output.data.CommonPrefixes.concat(res.data.CommonPrefixes);
      }

      if (!res.data.IsTruncated || output.data.KeyCount >= maxKeys) {
        break;
      }

      params.continuationToken = res.data.NextContinuationToken;
      params.maxKeys = params.maxKeys - res.data.KeyCount;
    }
  }

  return output;
}

async function listObjectsType2Once(input) {
  const {
    bucket,
    ...nextQuery
  } = input;
  const ret = await this.fetchBucket(input.bucket, 'GET', {
    'list-type': 2,
    ...covertCamelCase2Kebab(nextQuery)
  }, {});
  const arrayProp = makeArrayProp(ret.data);
  arrayProp('CommonPrefixes');
  arrayProp('Contents');
  return ret;
}

/** @private unstable */

class ShareLinkClient extends TOSBase {
  modifyAxiosInst() {
    const axiosInst = this.axiosInst;
    axiosInst.interceptors.request.use(config => {
      const headers = config.headers || {};
      delete headers['authorization'];
      headers['host'] = this.parsedPolicyUrlVal.host;
      config.baseURL = this.parsedPolicyUrlVal.origin;

      config.paramsSerializer = params => {
        const addQueryStr = paramsSerializer(params);
        return [this.parsedPolicyUrlVal.search, addQueryStr].filter(it => it.trim()).join('&');
      };

      return config;
    });
  }

  constructor(_opts) {
    super({ ..._opts,
      bucket: 'fake-bucket',
      region: 'fake-region',
      accessKeyId: 'fake-accessKeyId',
      accessKeySecret: 'fake-accessKeySecret',
      endpoint: 'fake-endpoint.com'
    });
    this.shareLinkClientOpts = void 0;
    this.parsedPolicyUrlVal = void 0;
    this.headObject = headObject;
    this.getObjectV2 = getObjectV2;
    this.listObjects = listObjects;
    this.listObjectsType2 = listObjectsType2;
    this.listObjectVersions = listObjectVersions;
    this.downloadFile = downloadFile;
    this.shareLinkClientOpts = _opts;
    this.parsedPolicyUrlVal = this.initParsedPolicyUrlVal();
    this.modifyAxiosInst();
  }

  initParsedPolicyUrlVal() {
    const reg = /(https?:\/\/(?:[^@]+@)?([^/?]+))[^?]*\?(.+)/;
    const matched = this.shareLinkClientOpts.policyUrl.match(reg);

    if (!matched) {
      throw new TosClientError('the `policyUrl` param is invalid');
    }

    return {
      origin: matched[1],
      host: matched[2],
      search: matched[3]
    };
  }

}

async function listBuckets(input = {}) {
  const headers = {};
  /**
   * empty string is invalid value
   */

  (input == null ? void 0 : input.projectName) && fillRequestHeaders({ ...input,
    headers
  }, ['projectName']);
  const res = await this.fetch('GET', '/', {}, headers);
  const arrayProp = makeArrayProp(res.data);
  arrayProp('Buckets');
  return res;
}
async function createBucket(input) {
  const actualBucket = input.bucket || this.opts.bucket; // these errors are only for creating bucket

  if (actualBucket) {
    if (actualBucket.length < 3 || actualBucket.length > 63) {
      throw new TosClientError('invalid bucket name, the length must be [3, 63]');
    }

    if (!/^([a-z]|-|\d)+$/.test(actualBucket)) {
      throw new TosClientError('invalid bucket name, the character set is illegal');
    }

    if (/^-/.test(actualBucket) || /-$/.test(actualBucket)) {
      throw new TosClientError(`invalid bucket name, the bucket name can be neither starting with '-' nor ending with '-'`);
    }
  }

  const headers = input.headers = normalizeHeadersKey(input.headers);
  fillRequestHeaders(input, ['acl', 'grantFullControl', 'grantRead', 'grantReadAcp', 'grantWrite', 'grantWriteAcp', 'storageClass', 'azRedundancy', 'bucketType']);
  /**
   * empty string is invalid value
   */

  (input == null ? void 0 : input.projectName) && fillRequestHeaders(input, ['projectName']);
  const res = await this.fetchBucket(input.bucket, 'PUT', {}, headers);
  return res;
}
async function deleteBucket(bucket) {
  return this.fetchBucket(bucket, 'DELETE', {}, {});
}
async function headBucket(bucket) {
  return this.fetchBucket(bucket, 'HEAD', {}, {}, undefined, {
    handleResponse: res => {
      return { ...res.headers,
        ProjectName: res.headers[TosHeader.HeaderProjectName]
      };
    }
  });
}
async function putBucketStorageClass(input) {
  const {
    bucket,
    storageClass
  } = input;
  return this.fetchBucket(bucket, 'PUT', {
    storageClass: ''
  }, {
    'x-tos-storage-class': storageClass
  });
}

async function putBucketAcl(input) {
  const headers = {};
  if (input.acl) headers['x-tos-acl'] = input.acl;
  const res = await this.fetchBucket(input.bucket, 'PUT', {
    acl: ''
  }, headers, input.aclBody, {
    needMd5: true
  });
  return res;
}
async function getBucketAcl(bucket) {
  const res = await this.fetchBucket(bucket, 'GET', {
    acl: ''
  }, {});
  const arrayProp = makeArrayProp(res.data);
  arrayProp('Grants');
  return res;
}

async function putObject(input) {
  return _putObject.call(this, input);
}
async function _putObject(input) {
  input = this.normalizeObjectInput(input);
  const headers = input.headers = normalizeHeadersKey(input.headers);
  fillRequestHeaders(input, ['contentLength', 'contentMD5', 'contentSHA256', 'cacheControl', 'contentDisposition', 'contentEncoding', 'contentLanguage', 'contentType', 'expires', 'acl', 'grantFullControl', 'grantRead', 'grantReadAcp', 'grantWrite', 'grantWriteAcp', 'ssecAlgorithm', 'ssecKey', 'ssecKeyMD5', 'serverSideEncryption', 'serverSideDataEncryption', 'meta', 'websiteRedirectLocation', 'storageClass', 'trafficLimit', 'callback', 'callbackVar', 'forbidOverwrite', 'ifMatch']);
  this.setObjectContentTypeHeader(input, headers);
  const totalSize = getSize(input.body, headers);
  const totalSizeValid = totalSize != null;

  if (!totalSizeValid && (input.dataTransferStatusChange || input.progress)) {
    console.warn(`Don't get totalSize of putObject's body, the \`dataTransferStatusChange\` and \`progress\` callback will not trigger. You can use \`putObjectFromFile\` instead`);
  }

  let consumedBytes = 0;
  const {
    dataTransferStatusChange,
    progress
  } = input;

  const triggerDataTransfer = (type, rwOnceBytes = 0) => {
    // request cancel will make rwOnceBytes < 0 in browser
    if (!totalSizeValid || rwOnceBytes < 0) {
      return;
    }

    if (!dataTransferStatusChange && !progress) {
      return;
    }

    consumedBytes += rwOnceBytes;
    dataTransferStatusChange == null ? void 0 : dataTransferStatusChange({
      type,
      rwOnceBytes,
      consumedBytes,
      totalBytes: totalSize
    });

    const progressValue = (() => {
      if (totalSize === 0) {
        if (type === exports.DataTransferType.Succeed) {
          return 1;
        }

        return 0;
      }

      return consumedBytes / totalSize;
    })();

    if (progressValue === 1) {
      if (type === exports.DataTransferType.Succeed) {
        progress == null ? void 0 : progress(progressValue);
      }
    } else {
      progress == null ? void 0 : progress(progressValue);
    }
  };

  const bodyConfig = await getNewBodyConfig({
    body: input.body,
    dataTransferCallback: n => triggerDataTransfer(exports.DataTransferType.Rw, n),
    makeRetryStream: input.makeRetryStream,
    enableCRC: this.opts.enableCRC,
    rateLimiter: input.rateLimiter
  });
  triggerDataTransfer(exports.DataTransferType.Started);

  const task = async () => {
    const res = await this._fetchObject(input, 'PUT', {}, headers, bodyConfig.body || '', {
      handleResponse: res => {
        var _input;

        const result = { ...res.headers
        };

        if ((_input = input) != null && _input.callback && res.data) {
          result.CallbackResult = `${JSON.stringify(res.data)}`;
        }

        return result;
      },
      axiosOpts: {
        [retryNamespace]: {
          beforeRetry: () => {
            consumedBytes = 0;
            bodyConfig.beforeRetry == null ? void 0 : bodyConfig.beforeRetry();
          },
          makeRetryStream: bodyConfig.makeRetryStream
        },
        onUploadProgress: event => {
          triggerDataTransfer(exports.DataTransferType.Rw, event.loaded - consumedBytes);
        }
      }
    });

    if (this.opts.enableCRC && bodyConfig.crc) {
      checkCRC64WithHeaders(bodyConfig.crc, res.headers);
    }

    return res;
  };

  const [err, res] = await safeAwait(task());

  if (err || !res) {
    triggerDataTransfer(exports.DataTransferType.Failed);
    throw err;
  }

  triggerDataTransfer(exports.DataTransferType.Succeed);
  return res;
}
async function putObjectFromFile(input) {
  const normalizedHeaders = normalizeHeadersKey(input.headers);

  {
    throw new TosClientError("putObjectFromFile doesn't support in browser environment");
  }
}

async function fetchObject(input) {
  const headers = input.headers = normalizeHeadersKey(input.headers);
  fillRequestHeaders(input, ['acl', 'grantFullControl', 'grantRead', 'grantReadAcp', 'grantWriteAcp', 'ssecAlgorithm', 'ssecKey', 'ssecKeyMD5', 'meta', 'storageClass']);
  const res = await this._fetchObject(input, 'POST', {
    fetch: ''
  }, headers, {
    URL: input.url,
    IgnoreSameKey: input.ignoreSameKey,
    ContentMD5: input.contentMD5
  }, {
    needMd5: true
  });
  return res;
}
async function putFetchTask(input) {
  const headers = input.headers = normalizeHeadersKey(input.headers);
  fillRequestHeaders(input, ['acl', 'grantFullControl', 'grantRead', 'grantReadAcp', 'grantWriteAcp', 'ssecAlgorithm', 'ssecKey', 'ssecKeyMD5', 'meta', 'storageClass']);
  const res = await this._fetchObject(input, 'POST', {
    fetchTask: ''
  }, headers, {
    URL: input.url,
    IgnoreSameKey: input.ignoreSameKey,
    ContentMD5: input.contentMD5,
    Object: input.key
  }, {
    needMd5: true
  });
  return res;
}

function getPreSignedUrl(input) {
  validateObjectName(input);
  const normalizedInput = typeof input === 'string' ? {
    key: input
  } : input;
  const endpoint = normalizedInput.alternativeEndpoint || this.opts.endpoint;
  const subdomain = normalizedInput.alternativeEndpoint || normalizedInput.isCustomDomain ? false : true;
  const bucket = normalizedInput.bucket || this.opts.bucket || '';

  if (subdomain && !bucket) {
    throw new TosClientError('Must provide bucket param');
  }

  const [newHost, newPath, signingPath] = (() => {
    const encodedKey = encodeURIComponent(normalizedInput.key);
    const objectKeyPath = normalizedInput.key.split('/').map(it => encodeURIComponent(it)).join('/');

    if (subdomain) {
      return [`${bucket}.${endpoint}`, `/${objectKeyPath}`, `/${encodedKey}`];
    }

    return [endpoint, `/${objectKeyPath}`, `/${encodedKey}`];
  })();

  const nextQuery = normalizedInput.query || {};

  const setOneQuery = (k, v) => {
    if (nextQuery[k] == null && v != null) {
      nextQuery[k] = v;
    }
  };

  const response = normalizedInput.response || {};
  Object.keys(response).forEach(_key => {
    const key = _key;
    const kebabKey = covertCamelCase2Kebab(key);
    setOneQuery(`response-${kebabKey}`, response[key]);
  });

  if (normalizedInput.versionId) {
    setOneQuery('versionId', normalizedInput.versionId);
  }

  const query = this.getSignatureQuery({
    bucket,
    method: normalizedInput.method || 'GET',
    path: signingPath,
    endpoint,
    subdomain,
    expires: normalizedInput.expires || 1800,
    query: nextQuery
  });
  const normalizedProxy = normalizeProxy(this.opts.proxy);
  let baseURL = `http${this.opts.secure ? 's' : ''}://${newHost}`;

  if (normalizedProxy != null && normalizedProxy.url) {
    // if `baseURL` ends with '/'，we filter it.
    // because `newPath` starts with '/'
    baseURL = normalizedProxy.url.replace(/\/+$/g, '');

    if (normalizedProxy != null && normalizedProxy.needProxyParams) {
      query['x-proxy-tos-host'] = newHost;
    }
  }

  const queryStr = Object.keys(query).map(key => {
    return `${encodeURIComponent(key)}=${encodeURIComponent(query[key])}`;
  }).join('&');
  return `${baseURL}${newPath}?${queryStr}`;
}

async function deleteObject(input) {
  const normalizedInput = typeof input === 'string' ? {
    key: input
  } : input;
  const query = {};

  if (normalizedInput.versionId) {
    query.versionId = normalizedInput.versionId;
  }

  if (normalizedInput.skipTrash) {
    query.skipTrash = normalizedInput.skipTrash;
  }

  if (normalizedInput.recursive) {
    query.recursive = normalizedInput.recursive;
  }

  const res = await this._fetchObject(input, 'DELETE', query, {}, {}, {
    handleResponse: res => res.headers
  });
  return res;
}

async function renameObject(input) {
  input.headers = input.headers || {};
  fillRequestHeaders(input, ['recursiveMkdir', 'forbidOverwrite']);
  return this._fetchObject(input, 'PUT', {
    rename: '',
    name: input.newKey
  }, input.headers, '');
}

async function deleteMultiObjects(input) {
  const body = {
    Quiet: input.quiet,
    Objects: input.objects.map(it => ({
      Key: it.key,
      VersionId: it.versionId
    }))
  };
  const query = {
    delete: ''
  };

  if (input.skipTrash) {
    query.skipTrash = input.skipTrash;
  }

  if (input.recursive) {
    query.recursive = input.recursive;
  }

  const res = await this.fetchBucket(input.bucket, 'POST', query, {}, body);
  const arrayProp = makeArrayProp(res.data);
  arrayProp('Deleted');
  arrayProp('Error');
  return res;
}

async function getObjectAcl(input) {
  const normalizedInput = typeof input === 'string' ? {
    key: input
  } : input;
  const query = {
    acl: ''
  };

  if (normalizedInput.versionId) {
    query.versionId = normalizedInput.versionId;
  }

  const res = await this._fetchObject(input, 'GET', query, {});
  const arrayProp = makeArrayProp(res.data);
  arrayProp('Grants');
  return res;
}
async function putObjectAcl(input) {
  const headers = input.headers = normalizeHeadersKey(input.headers);
  const query = {
    acl: ''
  };

  if (input.versionId) {
    query.versionId = input.versionId;
  }

  fillRequestHeaders(input, ['acl']);
  return this._fetchObject(input, 'PUT', query, headers, input.aclBody);
}

async function abortMultipartUpload(input) {
  return this._fetchObject(input, 'DELETE', {
    uploadId: input.uploadId
  }, {});
}

async function listMultipartUploads(input = {}) {
  const {
    bucket,
    ...nextQuery
  } = input;
  const ret = await this.fetchBucket(input.bucket, 'GET', {
    uploads: '',
    ...covertCamelCase2Kebab(nextQuery)
  }, {});
  const arrayProp = makeArrayProp(ret.data);
  arrayProp('Uploads');
  arrayProp('CommonPrefixes');
  return ret;
}

async function appendObject(input) {
  const normalizedInput = input = this.normalizeObjectInput(input);
  const headers = input.headers = normalizeHeadersKey(input.headers);
  fillRequestHeaders(input, ['contentLength', 'cacheControl', 'contentDisposition', 'contentEncoding', 'contentLanguage', 'contentType', 'expires', 'acl', 'grantFullControl', 'grantRead', 'grantReadAcp', 'grantWriteAcp', 'meta', 'websiteRedirectLocation', 'storageClass', 'trafficLimit']);
  this.setObjectContentTypeHeader(input, headers);
  const totalSize = getSize(input.body, headers);
  const totalSizeValid = totalSize != null;

  if (!totalSizeValid) {
    throw new TosClientError(`appendObject needs to know the content length in advance`);
  }

  headers['content-length'] = headers['content-length'] || `${totalSize}`;

  if (this.opts.enableCRC && input.offset !== 0 && !input.preHashCrc64ecma) {
    throw new TosClientError('must provide preHashCrc64ecma if enableCRC is true and offset is non-zero');
  }

  let consumedBytes = 0;
  const {
    dataTransferStatusChange,
    progress
  } = input;

  const triggerDataTransfer = (type, rwOnceBytes = 0) => {
    // request cancel will make rwOnceBytes < 0 in browser
    if (!totalSizeValid || rwOnceBytes < 0) {
      return;
    }

    if (!dataTransferStatusChange && !progress) {
      return;
    }

    consumedBytes += rwOnceBytes;
    dataTransferStatusChange == null ? void 0 : dataTransferStatusChange({
      type,
      rwOnceBytes,
      consumedBytes,
      totalBytes: totalSize
    });

    const progressValue = (() => {
      if (totalSize === 0) {
        if (type === exports.DataTransferType.Succeed) {
          return 1;
        }

        return 0;
      }

      return consumedBytes / totalSize;
    })();

    if (progressValue === 1) {
      if (type === exports.DataTransferType.Succeed) {
        progress == null ? void 0 : progress(progressValue);
      }
    } else {
      progress == null ? void 0 : progress(progressValue);
    }
  };

  const bodyConfig = await getNewBodyConfig({
    body: input.body,
    dataTransferCallback: n => triggerDataTransfer(exports.DataTransferType.Rw, n),
    makeRetryStream: undefined,
    enableCRC: this.opts.enableCRC,
    rateLimiter: input.rateLimiter
  });
  triggerDataTransfer(exports.DataTransferType.Started);

  const task = async () => {
    const res = await this._fetchObject(input, 'POST', {
      append: '',
      offset: normalizedInput.offset
    }, headers, bodyConfig.body || '', {
      handleResponse: res => ({ ...res.headers,
        nextAppendOffset: +res.headers['x-tos-next-append-offset'],
        hashCrc64ecma: res.headers['x-tos-hash-crc64ecma']
      }),
      axiosOpts: {
        [retryNamespace]: {
          beforeRetry: () => {
            consumedBytes = 0;
            bodyConfig.beforeRetry == null ? void 0 : bodyConfig.beforeRetry();
          },
          makeRetryStream: bodyConfig.makeRetryStream
        },
        onUploadProgress: event => {
          triggerDataTransfer(exports.DataTransferType.Rw, event.loaded - consumedBytes);
        }
      }
    });

    if (this.opts.enableCRC && bodyConfig.crc) {
      const appendObjectCrc = combineCrc64(normalizedInput.preHashCrc64ecma || '0', bodyConfig.crc.getCrc64(), totalSize);
      checkCRC64WithHeaders(appendObjectCrc, res.headers);
    }

    return res;
  };

  const [err, res] = await safeAwait(task());

  if (err || !res) {
    triggerDataTransfer(exports.DataTransferType.Failed);
    throw err;
  }

  triggerDataTransfer(exports.DataTransferType.Succeed);
  return res;
}

async function setObjectMeta(input) {
  const normalizedInput = typeof input === 'string' ? {
    key: input
  } : input;
  const headers = normalizedInput.headers = normalizeHeadersKey(normalizedInput.headers);
  fillRequestHeaders(normalizedInput, ['cacheControl', 'contentDisposition', 'contentEncoding', 'contentLanguage', 'contentType', 'expires', 'meta']);
  const query = {
    metadata: ''
  };

  if (normalizedInput.versionId) {
    query.versionId = normalizedInput.versionId;
  }

  return this._fetchObject(input, 'POST', query, headers);
}

async function calculatePostSignature(input) {
  validateObjectName(input);
  input = this.normalizeObjectInput(input);
  const {
    expiresIn = 3600,
    key
  } = input;
  const bucket = input.bucket || this.opts.bucket;
  const fields = { ...input.fields
  };
  const conditions = [...(input.conditions || [])];

  if (!bucket) {
    throw new TosClientError('Must provide bucket param');
  }

  const accessKeySecret = this.opts.accessKeySecret;
  const date = new Date();
  const expirationDateStr = getDateTimeStr({
    date: new Date(date.valueOf() + expiresIn * 1000),
    type: 'ISO'
  });
  const dateStr = getDateTimeStr();
  const date8Str = dateStr.substring(0, 8);
  const service = 'tos';
  const requestStr = 'request';
  const kDate = hmacSha256$1(accessKeySecret, date8Str);
  const kRegion = hmacSha256$1(kDate, this.opts.region);
  const kService = hmacSha256$1(kRegion, service);
  const signingKey = hmacSha256$1(kService, requestStr);
  const credential = [this.opts.accessKeyId, date8Str, this.opts.region, service, requestStr].join('/');
  const addedInForm = {
    key,
    'x-tos-algorithm': 'TOS4-HMAC-SHA256',
    'x-tos-date': dateStr,
    'x-tos-credential': credential
  };

  if (this.opts.stsToken) {
    addedInForm['x-tos-security-token'] = this.opts.stsToken;
  }

  conditions.push({
    bucket
  });
  Object.entries(addedInForm).forEach(([key, value]) => {
    fields[key] = value;
  });
  Object.entries(fields).forEach(([key, value]) => {
    conditions.push({
      [key]: `${value}`
    });
  });
  const policy = {
    expiration: expirationDateStr,
    conditions
  };
  const policyStr = JSON.stringify(policy);
  const policyBase64 = stringify$1(parse$1(policyStr, 'utf-8'), 'base64');
  const signature = hmacSha256$1(signingKey, policyBase64, 'hex');
  fields.policy = policyBase64;
  fields['x-tos-signature'] = signature;
  return fields;
}
/**
 *
 * Z for 20130728T000000Z
 * ISO for 2007-12-01T12:00:00.000Z
 * @param opt
 * @returns
 */

function getDateTimeStr(opt) {
  const {
    date = new Date(),
    type = 'Z'
  } = opt || {};

  if (type === 'ISO') {
    return date.toISOString();
  }

  const dateTime = date.toISOString().replace(/\..+/, '').replace(/-/g, '').replace(/:/g, '') + 'Z';
  return dateTime;
}

const defaultEmptyMethodMap = {
  getBucketCustomDomain: true,
  getBucketIntelligenttiering: true,
  getBucketInventory: true,
  listBucketInventory: true,
  getBucketMirrorBack: true,
  getBucketNotification: true,
  getBucketPolicy: true,
  getBucketRealTimeLog: true,
  getBucketReplication: true,
  getBucketTagging: true,
  getBucketWebsite: true
};
function handleEmptyServerError(err, opts) {
  const {
    enableCatchEmptyServerError,
    methodKey,
    defaultResponse
  } = opts;

  if (err instanceof TosServerError) {
    if (enableCatchEmptyServerError) {
      if (err.statusCode === 404) {
        return getNormalDataFromError(defaultResponse, err);
      }
    } // 在本次更改前已经有一些接口对404做了catch处理，在不显式声明enableCatchEmptyServerError的情况下，保持原样，不做break change
    else if (enableCatchEmptyServerError === undefined) {
      if (err.statusCode === 404 && defaultEmptyMethodMap[methodKey]) {
        return getNormalDataFromError(defaultResponse, err);
      }
    }
  }

  throw err;
}

async function putBucketPolicy(input) {
  if ((this.opts.enableOptimizeMethodBehavior || this.opts.enableOptimizeMethodBehavior === undefined) && !input.policy.Statement.length) {
    return deleteBucketPolicy.call(this, input.bucket);
  }

  const res = await this.fetchBucket(input.bucket, 'PUT', {
    policy: ''
  }, {}, input.policy, {
    needMd5: true
  });
  return res;
}
async function getBucketPolicy(bucket) {
  try {
    const res = await this.fetchBucket(bucket, 'GET', {
      policy: ''
    }, {});
    res.data.Statement.forEach(it => {
      const arrayProp = makeArrayProp(it);
      Object.keys(it.Condition || {}).forEach(key => {
        Object.keys(it.Condition[key]).forEach(key2 => {
          arrayProp(`Condition["${key}"]["${key2}"]`);
        });
      });
    });
    return res;
  } catch (error) {
    return handleEmptyServerError(error, {
      enableCatchEmptyServerError: this.opts.enableOptimizeMethodBehavior,
      methodKey: 'getBucketPolicy',
      defaultResponse: {
        Statement: [],
        Version: '2012-10-17'
      }
    });
  }
}
async function deleteBucketPolicy(bucket) {
  return this.fetchBucket(bucket, 'DELETE', {
    policy: ''
  }, {});
}

async function getBucketVersioning(bucket) {
  return this.fetchBucket(bucket, 'GET', {
    versioning: ''
  }, {});
}
async function putBucketVersioning(input) {
  return this.fetchBucket(input.bucket, 'PUT', {
    versioning: ''
  }, {}, {
    Status: input.status
  });
}

function preSignedPolicyURL(input) {
  const normalizedInput = normalizeInput.call(this, input);
  validateConditions(input.conditions);
  const endpoint = input.alternativeEndpoint || (input.isCustomDomain ? this.opts.endpoint : `${normalizedInput.bucket}.${this.opts.endpoint}`);
  const baseURL = `http${this.opts.secure ? 's' : ''}://${endpoint}`;
  const query = this.getSignatureQuery({
    bucket: normalizedInput.bucket,
    expires: normalizedInput.expires,
    policy: {
      conditions: normalizedInput.conditions
    }
  });
  const queryStr = obj2QueryStr(query);

  const getSignedURLForList = additionalQuery => {
    const str2 = obj2QueryStr(additionalQuery);
    const q = [queryStr, str2].filter(Boolean).join('&');
    return `${baseURL}?${q}`;
  };

  const getSignedURLForGetOrHead = (key, additionalQuery) => {
    const str2 = obj2QueryStr(additionalQuery);
    const q = [queryStr, str2].filter(Boolean).join('&'); // keep   '/'

    const keyPath = key.split('/').map(it => encodeURIComponent(it)).join('/');
    return `${baseURL}/${keyPath}?${q}`;
  };

  return {
    getSignedURLForList,
    getSignedURLForGetOrHead,
    signedQuery: queryStr
  };
}

function normalizeInput(input) {
  const actualBucket = input.bucket || this.opts.bucket;
  const defaultExpires = 3600;

  if (!actualBucket) {
    throw new TosClientError('Must provide bucket param');
  }

  validateConditions(input.conditions);
  const normalizedConditions = input.conditions.map(it => [it.operator || 'eq', '$key', it.value]);
  normalizedConditions.push(['eq', '$bucket', actualBucket]);
  return {
    bucket: actualBucket,
    expires: input.expires || defaultExpires,
    conditions: normalizedConditions
  };
}

function validateConditions(conditions) {
  if (conditions.length < 1) {
    throw new TosClientError('The `conditions` field of `PreSignedPolicyURLInput` must has one item at least');
  }

  for (const it of conditions) {
    if (it.key !== 'key') {
      throw new TosClientError("The `key` field of `PolicySignatureCondition` must be `'key'`");
    }

    if (it.operator && it.operator !== 'eq' && it.operator !== 'starts-with') {
      throw new TosClientError("The `operator` field of `PolicySignatureCondition` must be `'eq'` or `'starts-with'`");
    }
  }
}

async function getBucketLocation(input) {
  const {
    bucket
  } = input;
  return this.fetchBucket(bucket, 'GET', {
    location: ''
  }, {});
}

async function getBucketCORS(input) {
  try {
    const {
      bucket
    } = input;
    return await this.fetchBucket(bucket, 'GET', {
      cors: ''
    }, {});
  } catch (error) {
    return handleEmptyServerError(error, {
      defaultResponse: {
        CORSRules: []
      },
      enableCatchEmptyServerError: this.opts.enableOptimizeMethodBehavior,
      methodKey: 'getBucketCORS'
    });
  }
}
async function putBucketCORS(input) {
  const {
    bucket,
    CORSRules
  } = input;

  if (this.opts.enableOptimizeMethodBehavior && !CORSRules.length) {
    return deleteBucketCORS.call(this, {
      bucket
    });
  }

  return this.fetchBucket(bucket, 'PUT', {
    cors: ''
  }, {}, {
    CORSRules
  });
}
async function deleteBucketCORS(input) {
  const {
    bucket
  } = input;
  return this.fetchBucket(bucket, 'DELETE', {
    cors: ''
  }, {});
}

async function putBucketLifecycle(input) {
  const {
    bucket,
    rules
  } = input;

  if (this.opts.enableOptimizeMethodBehavior && !rules.length) {
    return deleteBucketLifecycle.call(this, {
      bucket
    });
  }

  const headers = {};
  fillRequestHeaders({ ...input,
    headers
  }, ['allowSameActionOverlap']);
  return this.fetchBucket(bucket, 'PUT', {
    lifecycle: ''
  }, headers, {
    Rules: rules
  });
}
async function getBucketLifecycle(input) {
  try {
    const {
      bucket
    } = input;
    return await this.fetchBucket(bucket, 'GET', {
      lifecycle: ''
    }, {}, {}, {
      handleResponse: res => {
        return {
          AllowSameActionOverlap: res.headers['x-tos-allow-same-action-overlap'],
          Rules: res.data.Rules
        };
      }
    });
  } catch (error) {
    return handleEmptyServerError(error, {
      enableCatchEmptyServerError: this.opts.enableOptimizeMethodBehavior,
      methodKey: 'getBucketLifecycle',
      defaultResponse: {
        Rules: []
      }
    });
  }
}
async function deleteBucketLifecycle(input) {
  const {
    bucket
  } = input;
  return this.fetchBucket(bucket, 'DELETE', {
    lifecycle: ''
  }, {});
}

async function putBucketEncryption(input) {
  const {
    bucket,
    rule
  } = input;
  return this.fetchBucket(bucket, 'PUT', {
    encryption: ''
  }, {
    'Content-MD5': hashMd5(JSON.stringify({
      Rule: rule
    }), 'base64')
  }, {
    Rule: rule
  });
}
async function getBucketEncryption(input) {
  const {
    bucket
  } = input;
  return this.fetchBucket(bucket, 'GET', {
    encryption: ''
  }, {});
}
async function deleteBucketEncryption(input) {
  const {
    bucket
  } = input;
  return this.fetchBucket(bucket, 'DELETE', {
    encryption: ''
  }, {});
}

const CommonQueryKey = 'mirror';
async function putBucketMirrorBack(input) {
  const {
    bucket,
    rules
  } = input;

  if (this.opts.enableOptimizeMethodBehavior && !rules.length) {
    return deleteBucketMirrorBack.call(this, {
      bucket
    });
  }

  return this.fetchBucket(bucket, 'PUT', {
    [CommonQueryKey]: ''
  }, {}, {
    Rules: rules
  });
}
async function getBucketMirrorBack(input) {
  const {
    bucket
  } = input;

  try {
    return await this.fetchBucket(bucket, 'GET', {
      [CommonQueryKey]: ''
    }, {});
  } catch (error) {
    return handleEmptyServerError(error, {
      enableCatchEmptyServerError: this.opts.enableOptimizeMethodBehavior,
      methodKey: 'getBucketMirrorBack',
      defaultResponse: {
        Rules: []
      }
    });
  }
}
async function deleteBucketMirrorBack(input) {
  const {
    bucket
  } = input;
  return this.fetchBucket(bucket, 'DELETE', {
    [CommonQueryKey]: ''
  }, {});
}

const CommonQueryKey$1 = 'tagging';
async function putObjectTagging(input) {
  const {
    tagSet,
    versionId
  } = input;
  const headers = normalizeHeadersKey({
    versionId
  });
  return this._fetchObject(input, 'PUT', {
    [CommonQueryKey$1]: '',
    ...headers
  }, {}, {
    TagSet: tagSet
  });
}
async function getObjectTagging(input) {
  const {
    versionId
  } = input;
  const headers = normalizeHeadersKey({
    versionId
  });
  const res = await this._fetchObject(input, 'GET', {
    [CommonQueryKey$1]: '',
    ...headers
  }, {});
  makeArrayProp(res.data.TagSet)('Tags');
  return res;
}
async function deleteObjectTagging(input) {
  const {
    versionId
  } = input;
  const headers = normalizeHeadersKey({
    versionId
  });
  return this._fetchObject(input, 'DELETE', {
    [CommonQueryKey$1]: '',
    ...headers
  }, {});
}

const CommonQueryKey$2 = 'replication';
async function putBucketReplication(input) {
  const {
    bucket,
    rules,
    role
  } = input;

  if (this.opts.enableOptimizeMethodBehavior && !rules.length) {
    return deleteBucketReplication.call(this, {
      bucket
    });
  }

  return this.fetchBucket(bucket, 'PUT', {
    [CommonQueryKey$2]: ''
  }, {}, {
    Role: role,
    Rules: rules
  });
}
async function getBucketReplication(input) {
  const {
    bucket,
    progress,
    ruleId
  } = input;
  const query = {
    [CommonQueryKey$2]: '',
    progress: progress || ''
  };

  if (ruleId != null) {
    query['rule-id'] = `${ruleId}`;
  }

  try {
    return await this.fetchBucket(bucket, 'GET', query, {});
  } catch (err) {
    return handleEmptyServerError(err, {
      enableCatchEmptyServerError: this.opts.enableOptimizeMethodBehavior,
      methodKey: 'getBucketReplication',
      defaultResponse: {
        Rules: [],
        Role: ''
      }
    });
  }
}
async function deleteBucketReplication(input) {
  const {
    bucket
  } = input;
  return this.fetchBucket(bucket, 'DELETE', {
    [CommonQueryKey$2]: ''
  }, {});
}

const CommonQueryKey$3 = 'website';
async function putBucketWebsite(input) {
  const {
    bucket,
    ...otherProps
  } = input;
  const body = convertNormalCamelCase2Upper(otherProps);
  return this.fetchBucket(bucket, 'PUT', {
    [CommonQueryKey$3]: ''
  }, {}, { ...body
  });
}
async function getBucketWebsite(input) {
  const {
    bucket
  } = input;

  try {
    return this.fetchBucket(bucket, 'GET', {
      [CommonQueryKey$3]: ''
    }, {});
  } catch (error) {
    return handleEmptyServerError(error, {
      enableCatchEmptyServerError: this.opts.enableOptimizeMethodBehavior,
      methodKey: 'getBucketWebsite',
      defaultResponse: {
        RoutingRules: []
      }
    });
  }
}
async function deleteBucketWebsite(input) {
  const {
    bucket
  } = input;
  return this.fetchBucket(bucket, 'DELETE', {
    [CommonQueryKey$3]: ''
  }, {});
}

const CommonQueryKey$4 = 'notification';
/**
 * @deprecated use PutBucketNotificationType2 instead
 */

async function putBucketNotification(input) {
  const {
    bucket,
    ...otherProps
  } = input;
  const body = convertNormalCamelCase2Upper(otherProps);
  return this.fetchBucket(bucket, 'PUT', {
    [CommonQueryKey$4]: ''
  }, {}, { ...body
  });
}
/**
 * @deprecated use GetBucketNotificationType2 instead
 */

async function getBucketNotification(input) {
  const {
    bucket
  } = input;

  try {
    return await this.fetchBucket(bucket, 'GET', {
      [CommonQueryKey$4]: ''
    }, {});
  } catch (error) {
    return handleEmptyServerError(error, {
      enableCatchEmptyServerError: this.opts.enableOptimizeMethodBehavior,
      methodKey: 'getBucketNotification',
      defaultResponse: {
        CloudFunctionConfigurations: [],
        RocketMQConfigurations: []
      }
    });
  }
}

const CommonQueryKey$5 = 'customdomain';
async function putBucketCustomDomain(input) {
  const {
    bucket,
    ...otherProps
  } = input;
  const body = convertNormalCamelCase2Upper(otherProps);
  return this.fetchBucket(bucket, 'PUT', {
    [CommonQueryKey$5]: ''
  }, {}, { ...body
  });
}
async function getBucketCustomDomain(input) {
  try {
    const {
      bucket
    } = input;
    return await this.fetchBucket(bucket, 'GET', {
      [CommonQueryKey$5]: ''
    }, {});
  } catch (error) {
    return handleEmptyServerError(error, {
      defaultResponse: {
        CustomDomainRules: []
      },
      enableCatchEmptyServerError: this.opts.enableOptimizeMethodBehavior,
      methodKey: 'getBucketCustomDomain'
    });
  }
}
async function deleteBucketCustomDomain(input) {
  const {
    bucket,
    customDomain
  } = input;
  return this.fetchBucket(bucket, 'DELETE', {
    customdomain: customDomain
  }, {});
}

const CommonQueryKey$6 = 'realtimeLog';
async function putBucketRealTimeLog(input) {
  const {
    bucket,
    ...otherProps
  } = input;
  const body = convertNormalCamelCase2Upper(otherProps);
  return this.fetchBucket(bucket, 'PUT', {
    [CommonQueryKey$6]: ''
  }, {}, { ...body
  });
}
async function getBucketRealTimeLog(input) {
  const {
    bucket
  } = input;

  try {
    return await this.fetchBucket(bucket, 'GET', {
      [CommonQueryKey$6]: ''
    }, {});
  } catch (error) {
    return handleEmptyServerError(error, {
      enableCatchEmptyServerError: this.opts.enableOptimizeMethodBehavior,
      methodKey: 'getBucketRealTimeLog',
      defaultResponse: {}
    });
  }
}
async function deleteBucketRealTimeLog(input) {
  const {
    bucket
  } = input;
  return this.fetchBucket(bucket, 'DELETE', {
    [CommonQueryKey$6]: ''
  }, {});
}

/**
 * 清单文件导出周期
 */

var ScheduleFrequency;

(function (ScheduleFrequency) {
  /** 按天 */
  ScheduleFrequency["Daily"] = "Daily";
  /** 按周 */

  ScheduleFrequency["Weekly"] = "Weekly";
})(ScheduleFrequency || (ScheduleFrequency = {}));
/**
 * 清单包含Object版本信息值
 */


var IncludedObjectVersions;

(function (IncludedObjectVersions) {
  /** 全部 */
  IncludedObjectVersions["All"] = "All";
  /** 当前版本 */

  IncludedObjectVersions["Current"] = "Current";
})(IncludedObjectVersions || (IncludedObjectVersions = {}));
/**
 * 清单配置项
 */


var InventoryOptionalFields;

(function (InventoryOptionalFields) {
  /** Object的大小 */
  InventoryOptionalFields["Size"] = "Size";
  /** Object的最后修改时间 */

  InventoryOptionalFields["LastModifiedDat"] = "LastModifiedDate";
  /** 标识Object的内容 */

  InventoryOptionalFields["ETag"] = "ETag";
  /** Object的存储类型 */

  InventoryOptionalFields["StorageClass"] = "StorageClass";
  /** 是否为通过分片上传的Object */

  InventoryOptionalFields["IsMultipartUploaded"] = "IsMultipartUploaded";
  /** Object是否加密 */

  InventoryOptionalFields["EncryptionStatus"] = "EncryptionStatus";
  InventoryOptionalFields["CRC64"] = "CRC64";
  /** crr复制状态 */

  InventoryOptionalFields["ReplicationStatus"] = "ReplicationStatus";
})(InventoryOptionalFields || (InventoryOptionalFields = {}));
/**
 * 获取桶清单详情信息
 */


async function getBucketInventory(req) {
  try {
    const res = await this.fetchBucket(req.bucket, 'GET', {
      inventory: '',
      id: req.id
    }, {});
    return res;
  } catch (error) {
    return handleEmptyServerError(error, {
      enableCatchEmptyServerError: this.opts.enableOptimizeMethodBehavior,
      methodKey: 'getBucketInventory',
      defaultResponse: undefined
    });
  }
}
/**
 * 分页获取桶清单信息
 */

async function listBucketInventory(req) {
  const params = {
    inventory: '',
    ...(req.continuationToken ? {
      'continuation-token': req.continuationToken
    } : null)
  };

  try {
    const res = await this.fetchBucket(req.bucket, 'GET', params, {});
    return res;
  } catch (error) {
    return handleEmptyServerError(error, {
      enableCatchEmptyServerError: this.opts.enableOptimizeMethodBehavior,
      methodKey: 'listBucketInventory',
      defaultResponse: {
        InventoryConfigurations: []
      }
    });
  }
}
/**
 * 删除桶清单
 */

async function deleteBucketInventory(req) {
  return this.fetchBucket(req.bucket, 'DELETE', {
    inventory: '',
    id: req.id
  }, {});
}
/**
 * 更新桶清单
 */

function putBucketInventory(req) {
  return this.fetchBucket(req.bucket, 'PUT', {
    inventory: '',
    id: req.inventoryConfiguration.Id
  }, {}, req.inventoryConfiguration);
}

/**
 *
 * @private unstable method
 * @description 创建批量任务
 * @param params
 * @returns
 */

async function createJob(params) {
  const {
    accountId,
    ...reset
  } = params;
  const data = convertNormalCamelCase2Upper(reset);
  const res = await this.fetch('POST', '/jobs', {}, {
    'x-tos-account-id': accountId
  }, { ...data
  });
  return res;
}
/**
 * @private unstable method
 * @description  获取批量任务列表
 * @param params
 * @returns
 */

async function listJobs(params) {
  const {
    accountId,
    maxResults = 1000,
    ...others
  } = params;
  const res = await this.fetch('GET', '/jobs', {
    maxResults,
    ...others
  }, {
    'x-tos-account-id': accountId
  }, {}, {
    axiosOpts: {
      paramsSerializer
    }
  });
  return res;
}
/**
 *
 * @private unstable method
 * @description 更新批量任务优先级
 * @param params
 * @returns
 */

async function updateJobPriority(params) {
  const {
    accountId,
    jobId: JobId,
    priority
  } = params;
  const res = await this.fetch('POST', `/jobs/${JobId}/priority`, {
    priority
  }, {
    'x-tos-account-id': accountId
  }, {}, {
    needMd5: true
  });
  return res;
}
/**
 *
 * @private unstable method
 * @description 更新批量任务优先级
 * @param params
 * @returns
 */

async function updateJobStatus(params) {
  const {
    accountId,
    jobId: JobId,
    requestedJobStatus,
    statusUpdateReason
  } = params;
  const res = await this.fetch('POST', `/jobs/${JobId}/status`, {
    requestedJobStatus,
    statusUpdateReason
  }, {
    'x-tos-account-id': accountId
  }, {}, {
    needMd5: true
  });
  return res;
}
/**
 *
 * @private unstable method
 * @description 删除批量任务
 * @param params
 * @returns
 */

async function deleteJob(params) {
  const {
    accountId,
    JobId
  } = params;
  const res = await this.fetch('DELETE', `/jobs/${JobId}`, {}, {
    'x-tos-account-id': accountId
  }, {});
  return res;
}
/**
 *
 * @private unstable method
 * @description 获取批量任务详情
 * @param params
 * @returns
 */

async function describeJob(params) {
  const {
    accountId,
    JobId
  } = params;
  const res = await this.fetch('GET', `/jobs/${JobId}`, {}, {
    'x-tos-account-id': accountId
  }, {});
  return res;
}

/**
 * @private unstable method
 */

async function putBucketTagging(input) {
  const res = await this.fetchBucket(input.bucket, 'PUT', {
    tagging: ''
  }, {}, input.tagging, {
    needMd5: true
  });
  return res;
}
/**
 * @private unstable method
 */

async function getBucketTagging({
  bucket
}) {
  try {
    const res = await this.fetchBucket(bucket, 'GET', {
      tagging: ''
    }, {});
    return res;
  } catch (error) {
    return handleEmptyServerError(error, {
      enableCatchEmptyServerError: this.opts.enableOptimizeMethodBehavior,
      methodKey: 'getBucketTagging',
      defaultResponse: {
        TagSet: {
          Tags: []
        }
      }
    });
  }
}
/**
 * @private unstable method
 */

async function deleteBucketTagging({
  bucket
}) {
  return this.fetchBucket(bucket, 'DELETE', {
    tagging: ''
  }, {});
}

/**
 * @private unstable method
 */
async function putBucketPayByTraffic(input) {
  const res = await this.fetchBucket(input.bucket, 'PUT', {
    payByTraffic: ''
  }, {}, input.payByTraffic);
  return res;
}
/**
 * @private unstable method
 */

async function getBucketPayByTraffic({
  bucket
}) {
  const res = await this.fetchBucket(bucket, 'GET', {
    payByTraffic: ''
  }, {});
  return res;
}

/**
 * @private unstable method
 */

async function getImageStyleBriefInfo(req) {
  const {
    bucket
  } = req;

  try {
    const res = await this.fetchBucket(bucket, 'GET', {
      imageStyleBriefInfo: ''
    }, {});
    return res;
  } catch (err) {
    if (err instanceof TosServerError) {
      if (err.statusCode === 404) {
        return this.getNormalDataFromError({
          BucketName: bucket,
          ImageStyleBriefInfo: []
        }, err);
      }
    }

    throw err;
  }
}
/**
 * @private unstable method
 */

async function getBucketImageStyleList(bucket) {
  try {
    const res = await this.fetchBucket(bucket, 'GET', {
      imageStyle: ''
    }, {});
    return res;
  } catch (err) {
    if (err instanceof TosServerError) {
      if (err.statusCode === 404) {
        return this.getNormalDataFromError({
          ImageStyles: []
        }, err);
      }
    }

    throw err;
  }
}
/**
 * @private unstable method
 */

async function getBucketImageStyleListByName(req) {
  try {
    const {
      bucket,
      styleName
    } = req;
    const res = await this.fetchBucket(bucket, 'GET', {
      imageStyleContent: '',
      styleName
    }, {});
    return res;
  } catch (err) {
    if (err instanceof TosServerError) {
      if (err.statusCode === 404) {
        return this.getNormalDataFromError({
          ImageStyles: []
        }, err);
      }
    }

    throw err;
  }
}
/**
 * @private unstable method
 */

async function getBucketImageStyle(bucket, styleName) {
  try {
    const res = await this.fetchBucket(bucket, 'GET', {
      imageStyle: '',
      styleName
    }, {});
    return res;
  } catch (err) {
    if (err instanceof TosServerError) {
      if (err.statusCode === 404) {
        return this.getNormalDataFromError(null, err);
      }
    }

    throw err;
  }
}
/**
 * @private unstable method
 */

async function putBucketImageStyle(req) {
  const {
    bucket,
    styleName,
    content,
    styleObjectPrefix
  } = req;

  try {
    const res = await this.fetchBucket(bucket, 'PUT', {
      imageStyle: '',
      styleName,
      styleObjectPrefix
    }, {}, {
      Content: content
    });
    return res;
  } catch (err) {
    if (err instanceof TosServerError) {
      if (err.statusCode === 404) {
        return this.getNormalDataFromError(null, err);
      }
    }

    throw err;
  }
}
/**
 * @private unstable method
 */

async function deleteBucketImageStyle(req) {
  const {
    styleName,
    styleObjectPrefix,
    bucket
  } = req;

  try {
    const res = await this.fetchBucket(bucket, 'DELETE', {
      imageStyle: '',
      styleName,
      styleObjectPrefix
    }, {});
    return res;
  } catch (err) {
    if (err instanceof TosServerError) {
      if (err.statusCode === 404) {
        return this.getNormalDataFromError(null, err);
      }
    }

    throw err;
  }
}
/**
 * @private unstable method
 */

async function putBucketImageProtect(bucket, data) {
  try {
    const res = await this.fetchBucket(bucket, 'PUT', {
      originalImageProtect: ''
    }, {}, data);
    return res;
  } catch (err) {
    if (err instanceof TosServerError) {
      if (err.statusCode === 404) {
        return this.getNormalDataFromError(null, err);
      }
    }

    throw err;
  }
}
/**
 * @private unstable method
 */

async function getBucketImageProtect(bucket) {
  try {
    const res = await this.fetchBucket(bucket, 'GET', {
      originalImageProtect: ''
    }, {});
    return res;
  } catch (err) {
    if (err instanceof TosServerError) {
      if (err.statusCode === 404) {
        return this.getNormalDataFromError(null, err);
      }
    }

    throw err;
  }
}
/**
 * @private unstable method
 */

async function putBucketImageStyleSeparator(req) {
  const {
    bucket,
    Separator,
    SeparatorSuffix
  } = req;

  try {
    const res = await this.fetchBucket(bucket, 'PUT', {
      imageStyleSeparator: ''
    }, {}, {
      Separator,
      SeparatorSuffix
    });
    return res;
  } catch (err) {
    if (err instanceof TosServerError) {
      if (err.statusCode === 404) {
        return this.getNormalDataFromError(null, err);
      }
    }

    throw err;
  }
}
/**
 * @private unstable method
 */

async function getBucketImageStyleSeparator(bucket) {
  try {
    const res = await this.fetchBucket(bucket, 'GET', {
      imageStyleSeparator: ''
    }, {});
    return res;
  } catch (err) {
    if (err instanceof TosServerError) {
      if (err.statusCode === 404) {
        return this.getNormalDataFromError(null, err);
      }
    }

    throw err;
  }
}

async function getBucketIntelligenttiering(bucket) {
  try {
    const res = await this.fetchBucket(bucket, 'GET', {
      intelligenttiering: ''
    }, {});
    return res;
  } catch (error) {
    return handleEmptyServerError(error, {
      enableCatchEmptyServerError: this.opts.enableOptimizeMethodBehavior,
      methodKey: 'getBucketIntelligenttiering',
      defaultResponse: {}
    });
  }
}

const CommonQueryKey$7 = 'rename';
async function putBucketRename(input) {
  const {
    bucket,
    ...otherProps
  } = input;
  const body = convertNormalCamelCase2Upper(otherProps);
  return this.fetchBucket(bucket, 'PUT', {
    [CommonQueryKey$7]: ''
  }, {}, { ...body
  });
}
async function getBucketRename(input) {
  const {
    bucket
  } = input;
  return await this.fetchBucket(bucket, 'GET', {
    [CommonQueryKey$7]: ''
  }, {});
}
async function deleteBucketRename(input) {
  const {
    bucket
  } = input;
  return this.fetchBucket(bucket, 'DELETE', {
    [CommonQueryKey$7]: ''
  }, {});
}

async function restoreObject(input) {
  const {
    bucket,
    key,
    versionId,
    ...otherProps
  } = input;
  const query = {
    restore: ''
  };

  if (versionId) {
    query.versionId = versionId;
  }

  const body = convertNormalCamelCase2Upper(otherProps);
  return this._fetchObject(input, 'POST', query, {}, body);
}

/**
 * @private unstable method
 * @description 获取数据透视列表
 * @param params
 * @returns
 */

async function listStorageLens(params) {
  const {
    accountId
  } = params;
  const res = await this.fetch('GET', '/storagelens', {}, {
    'x-tos-account-id': accountId
  }, {}, {
    axiosOpts: {
      paramsSerializer
    }
  });
  return res;
}
/**
 * @private unstable method
 * @description 删除数据透视记录
 * @param params
 * @returns
 */

async function deleteStorageLens(params) {
  const {
    accountId,
    Id
  } = params;
  const res = await this.fetch('DELETE', `/storagelens`, {
    id: Id
  }, {
    'x-tos-account-id': accountId
  }, {}, {
    needMd5: true
  });
  return res;
}
/**
 * @private unstable method
 * @description 获取数据透视详情
 * @param params
 * @returns
 */

async function getStorageLens(params) {
  const {
    accountId,
    Id
  } = params;
  const res = await this.fetch('GET', `/storagelens`, {
    id: Id
  }, {
    'x-tos-account-id': accountId
  }, {}, {
    needMd5: true
  });
  return res;
}
/**
 * @private unstable method
 * @description 提交数据透视记录
 * @param params
 * @returns
 */

async function putStorageLens(params) {
  const {
    accountId,
    Id,
    ...rest
  } = params;
  const res = await this.fetch('PUT', `/storagelens`, {
    id: Id
  }, {
    'x-tos-account-id': accountId
  }, { ...rest,
    Id
  }, {
    needMd5: true
  });
  return res;
}

const CommonQueryKey$8 = 'notification_v2';
async function putBucketNotificationType2(input) {
  const {
    bucket,
    ...otherProps
  } = input;
  const body = convertNormalCamelCase2Upper(otherProps);
  return this.fetchBucket(bucket, 'PUT', {
    [CommonQueryKey$8]: ''
  }, {}, { ...body
  });
}
async function getBucketNotificationType2(input) {
  const {
    bucket
  } = input;

  try {
    return await this.fetchBucket(bucket, 'GET', {
      [CommonQueryKey$8]: ''
    }, {});
  } catch (error) {
    return handleEmptyServerError(error, {
      enableCatchEmptyServerError: this.opts.enableOptimizeMethodBehavior,
      methodKey: 'getBucketNotificationType2',
      defaultResponse: {
        Rules: []
      }
    });
  }
}

/**
 * @private unstable method
 */

async function putSymlink(input) {
  return _putSymlink.call(this, input);
}
async function _putSymlink(input) {
  const headers = input.headers = normalizeHeadersKey(input.headers);
  fillRequestHeaders(input, ['symLinkTargetKey', 'symLinkTargetBucket', 'forbidOverwrite', 'acl', 'storageClass', 'meta']);
  return this._fetchObject(input, 'PUT', {
    symlink: ''
  }, headers, undefined, {
    handleResponse(response) {
      const {
        headers
      } = response;
      return {
        VersionID: headers['x-tos-version-id']
      };
    }

  });
}

/**
 * @private unstable method
 */
async function getSymlink(input) {
  return _getSymlink.call(this, input);
}
async function _getSymlink(input) {
  const query = {
    symlink: ''
  };

  if (input.versionId) {
    query.versionId = input.versionId;
  }

  return this._fetchObject(input, 'GET', query, {}, undefined, {
    handleResponse: res => {
      const {
        headers
      } = res;
      return {
        VersionID: headers['x-tos-version-id'],
        SymlinkTargetKey: headers['x-tos-symlink-target'],
        SymlinkTargetBucket: headers['x-tos-symlink-bucket'],
        LastModified: headers['last-modified']
      };
    }
  });
}

const CommonQueryKey$9 = 'transferAcceleration';
/**
 * @private unstable
 */

async function putBucketTransferAcceleration(input) {
  const {
    bucket,
    ...otherProps
  } = input;
  const body = convertNormalCamelCase2Upper(otherProps);
  return this.fetchBucket(bucket, 'PUT', {
    [CommonQueryKey$9]: ''
  }, {}, { ...body
  });
}
/**
 * @private unstable
 */

async function getBucketTransferAcceleration(input) {
  try {
    const {
      bucket
    } = input;
    const headers = {};

    if (input.getStatus) {
      headers['x-tos-get-bucket-acceleration-status'] = 'true';
    }

    const res = await this.fetchBucket(bucket, 'GET', {
      [CommonQueryKey$9]: ''
    }, headers);
    return res;
  } catch (error) {
    return handleEmptyServerError(error, {
      enableCatchEmptyServerError: this.opts.enableOptimizeMethodBehavior,
      methodKey: 'getBucketTransferAcceleration',
      defaultResponse: {
        TransferAccelerationConfiguration: {
          Enabled: 'false',
          Status: exports.TransferAccelerationStatusType.Terminated
        }
      }
    });
  }
}

/**
 * @private unstable method
 */

async function putBucketAccessMonitor(input) {
  const {
    bucket,
    status
  } = input;
  return this.fetchBucket(bucket, 'PUT', {
    accessmonitor: ''
  }, {}, {
    Status: status
  });
}
/**
 * @private unstable method
 */

async function getBucketAccessMonitor(input) {
  try {
    const {
      bucket
    } = input;
    return await this.fetchBucket(bucket, 'GET', {
      accessmonitor: ''
    }, {});
  } catch (error) {
    return handleEmptyServerError(error, {
      enableCatchEmptyServerError: this.opts.enableOptimizeMethodBehavior,
      methodKey: 'getBucketAccessMonitor',
      defaultResponse: {}
    });
  }
}

var StringOp;

(function (StringOp) {
  StringOp["StringEquals"] = "StringEquals";
  StringOp["StringNotEquals"] = "StringNotEquals";
  StringOp["StringEqualsIgnoreCase"] = "StringEqualsIgnoreCase";
  StringOp["StringNotEqualsIgnoreCase"] = "StringNotEqualsIgnoreCase";
  StringOp["StringLike"] = "StringLike";
  StringOp["StringNotLike"] = "StringNotLike";
})(StringOp || (StringOp = {}));

var DateOp;

(function (DateOp) {
  DateOp["DateEquals"] = "DateEquals";
  DateOp["DateNotEquals"] = "DateNotEquals";
  DateOp["DateLessThan"] = "DateLessThan";
  DateOp["DateLessThanEquals"] = "DateLessThanEquals";
  DateOp["DateGreaterThan"] = "DateGreaterThan";
  DateOp["DateGreaterThanEquals"] = "DateGreaterThanEquals";
})(DateOp || (DateOp = {}));

var IpOp;

(function (IpOp) {
  IpOp["IpAddress"] = "IpAddress";
  IpOp["NotIpAddress"] = "NotIpAddress";
})(IpOp || (IpOp = {}));
/** 流控类别 */


var QuotaType;

(function (QuotaType) {
  /** 写Qps */
  QuotaType["WritesQps"] = "WritesQps";
  /** 读Qps */

  QuotaType["ReadsQps"] = "ReadsQps";
  /** list类Qps */

  QuotaType["ListQps"] = "ListQps";
  /** 写带宽 */

  QuotaType["WritesRate"] = "WritesRate";
  /** 读带宽 */

  QuotaType["ReadsRate"] = "ReadsRate";
})(QuotaType || (QuotaType = {}));
/**
 * @private unstable method
 * @description 拉取流控策略列表
 * @param {GetQosPolicyInput}
 * @returns {GetQosPolicyOutput}
 */


async function getQosPolicy(params) {
  const {
    accountId
  } = params;
  const res = await this.fetch('GET', '/qospolicy', {}, {
    'x-tos-account-id': accountId
  }, {}, {});
  return res;
}
/**
 * @private unstable method
 * @description 更新流控策略列表 覆盖全部 QosPolicy
 * @param {PutQosPolicyInput}
 */

async function putQosPolicy(params) {
  const {
    accountId,
    ...restParams
  } = params;
  const res = await this.fetch('PUT', '/qospolicy', {}, {
    'x-tos-account-id': accountId
  }, { ...restParams
  }, {});
  return res;
}
/**
 * @private unstable method
 * @description 拉取流控策略列表
 * @param {DeleteQosPolicyInput}
 */

async function deleteQosPolicy(params) {
  const {
    accountId
  } = params;
  const res = await this.fetch('DELETE', '/qospolicy', {}, {
    'x-tos-account-id': accountId
  }, {}, {});
  return res;
}

/**
 * @private unstable method
 */
async function createMultiRegionAccessPoint(input) {
  const {
    accountId,
    name,
    regions
  } = input;
  const res = await this.fetch('POST', '/mrap', {
    name
  }, {
    'x-tos-account-id': accountId
  }, {
    Name: name,
    Regions: regions
  }, {});
  return res;
}
/**
 * @private unstable method
 */

async function getMultiRegionAccessPoint(input) {
  const {
    name,
    accountId
  } = input;
  const res = await this.fetch('GET', '/mrap', {
    name
  }, {
    'x-tos-account-id': accountId
  }, {}, {});
  return res;
}
/**
 * @private unstable method
 */

async function listMultiRegionAccessPoints(input) {
  const {
    accountId,
    ...nextQuery
  } = input;
  const res = await this.fetch('GET', '/mrap', { ...nextQuery
  }, {
    'x-tos-account-id': accountId
  }, {}, {});
  return res;
}
/**
 * @private unstable method
 */

async function getMultiRegionAccessPointRoutes(input) {
  const {
    accountId,
    alias
  } = input;
  const res = await this.fetch('GET', '/mrap/routes', {
    alias
  }, {
    'x-tos-account-id': accountId
  });
  return res;
}
async function deleteMultiRegionAccessPoint(input) {
  const {
    name,
    accountId
  } = input;
  const res = await this.fetch('DELETE', '/mrap', {
    name
  }, {
    'x-tos-account-id': accountId
  });
  return res;
}
async function submitMultiRegionAccessPointRoutes(input) {
  const {
    routes,
    accountId,
    alias
  } = input;
  const res = await this.fetch('PATCH', '/mrap/routes', {
    alias
  }, {
    'x-tos-account-id': accountId
  }, {
    Routes: routes
  });
  return res;
}

/**
 * @private unstable method
 */

const putMultiRegionAccessPointMirrorBack = async function (input) {
  const {
    accountId,
    alias,
    rules
  } = input;

  if (this.opts.enableOptimizeMethodBehavior && !rules.length) {
    return deleteMultiRegionAccessPointMirrorBack.call(this, {
      accountId,
      alias
    });
  }

  const res = await this.fetch('PUT', '/mrap/mirror', {
    alias
  }, {
    'x-tos-account-id': accountId
  }, {
    Rules: rules
  }, {
    handleResponse() {
      return {};
    }

  });
  return res;
};
/**
 * @private unstable method
 */

const getMultiRegionAccessPointMirrorBack = async function (input) {
  const {
    accountId,
    alias
  } = input;

  try {
    const res = await this.fetch('GET', '/mrap/mirror', {
      alias
    }, {
      'x-tos-account-id': accountId
    }, {}, {});
    const arrayProp = makeArrayProp(res.data);
    arrayProp('Rules');
    return res;
  } catch (error) {
    return handleEmptyServerError(error, {
      enableCatchEmptyServerError: this.opts.enableOptimizeMethodBehavior,
      methodKey: 'getMultiRegionAccessPointMirrorBack',
      defaultResponse: {
        Rules: []
      }
    });
  }
};
/**
 * @private unstable method
 */

const deleteMultiRegionAccessPointMirrorBack = async function (input) {
  const {
    accountId,
    alias
  } = input;
  const res = await this.fetch('DELETE', '/mrap/mirror', {
    alias
  }, {
    'x-tos-account-id': accountId
  }, {}, {
    handleResponse() {
      return {};
    }

  });
  return res;
};

/**
 * @private unstable
 */

async function putBucketPrivateM3U8(input) {
  const {
    bucket,
    enable
  } = input;
  return await this.fetchBucket(bucket, 'PUT', {
    privateM3U8: ''
  }, {}, {
    Enable: enable
  });
}
/**
 * @private unstable
 */

async function getBucketPrivateM3U8(input) {
  const {
    bucket
  } = input;

  try {
    return await this.fetchBucket(bucket, 'GET', {
      privateM3U8: ''
    }, {});
  } catch (error) {
    return handleEmptyServerError(error, {
      enableCatchEmptyServerError: this.opts.enableOptimizeMethodBehavior,
      methodKey: 'getBucketPrivateM3U8',
      defaultResponse: {
        Enable: false
      }
    });
  }
}

const CommonQueryKey$a = 'trash';
async function putBucketTrash(input) {
  const {
    bucket,
    ...otherProps
  } = input;
  const body = convertNormalCamelCase2Upper(otherProps);
  return this.fetchBucket(bucket, 'PUT', {
    [CommonQueryKey$a]: ''
  }, {}, { ...body
  });
}
async function getBucketTrash(input) {
  const {
    bucket
  } = input;
  return await this.fetchBucket(bucket, 'GET', {
    [CommonQueryKey$a]: ''
  }, {});
}

class InnerClient extends TOSBase {
  constructor(...args) {
    super(...args);
    this.createBucket = createBucket;
    this.headBucket = headBucket;
    this.deleteBucket = deleteBucket;
    this.listBuckets = listBuckets;
    this.getBucketLocation = getBucketLocation;
    this.putBucketStorageClass = putBucketStorageClass;
    this.getBucketAcl = getBucketAcl;
    this.putBucketAcl = putBucketAcl;
    this.getBucketPolicy = getBucketPolicy;
    this.putBucketPolicy = putBucketPolicy;
    this.deleteBucketPolicy = deleteBucketPolicy;
    this.getBucketVersioning = getBucketVersioning;
    this.putBucketVersioning = putBucketVersioning;
    this.getBucketCORS = getBucketCORS;
    this.putBucketCORS = putBucketCORS;
    this.deleteBucketCORS = deleteBucketCORS;
    this.putBucketLifecycle = putBucketLifecycle;
    this.getBucketLifecycle = getBucketLifecycle;
    this.deleteBucketLifecycle = deleteBucketLifecycle;
    this.putBucketEncryption = putBucketEncryption;
    this.getBucketEncryption = getBucketEncryption;
    this.deleteBucketEncryption = deleteBucketEncryption;
    this.putBucketMirrorBack = putBucketMirrorBack;
    this.getBucketMirrorBack = getBucketMirrorBack;
    this.deleteBucketMirrorBack = deleteBucketMirrorBack;
    this.putBucketReplication = putBucketReplication;
    this.getBucketReplication = getBucketReplication;
    this.deleteBucketReplication = deleteBucketReplication;
    this.putBucketWebsite = putBucketWebsite;
    this.getBucketWebsite = getBucketWebsite;
    this.deleteBucketWebsite = deleteBucketWebsite;
    this.putBucketNotification = putBucketNotification;
    this.getBucketNotification = getBucketNotification;
    this.putBucketCustomDomain = putBucketCustomDomain;
    this.getBucketCustomDomain = getBucketCustomDomain;
    this.deleteBucketCustomDomain = deleteBucketCustomDomain;
    this.putBucketRealTimeLog = putBucketRealTimeLog;
    this.getBucketRealTimeLog = getBucketRealTimeLog;
    this.deleteBucketRealTimeLog = deleteBucketRealTimeLog;
    this.getBucketInventory = getBucketInventory;
    this.listBucketInventory = listBucketInventory;
    this.putBucketInventory = putBucketInventory;
    this.deleteBucketInventory = deleteBucketInventory;
    this.putBucketTagging = putBucketTagging;
    this.getBucketTagging = getBucketTagging;
    this.deleteBucketTagging = deleteBucketTagging;
    this.putBucketPayByTraffic = putBucketPayByTraffic;
    this.getBucketPayByTraffic = getBucketPayByTraffic;
    this.getBucketImageStyle = getBucketImageStyle;
    this.getBucketImageStyleList = getBucketImageStyleList;
    this.getBucketImageStyleListByName = getBucketImageStyleListByName;
    this.getImageStyleBriefInfo = getImageStyleBriefInfo;
    this.deleteBucketImageStyle = deleteBucketImageStyle;
    this.putBucketImageStyle = putBucketImageStyle;
    this.putBucketImageStyleSeparator = putBucketImageStyleSeparator;
    this.putBucketImageProtect = putBucketImageProtect;
    this.getBucketImageProtect = getBucketImageProtect;
    this.getBucketImageStyleSeparator = getBucketImageStyleSeparator;
    this.putBucketRename = putBucketRename;
    this.getBucketRename = getBucketRename;
    this.deleteBucketRename = deleteBucketRename;
    this.putBucketTransferAcceleration = putBucketTransferAcceleration;
    this.getBucketTransferAcceleration = getBucketTransferAcceleration;
    this.copyObject = copyObject;
    this.resumableCopyObject = resumableCopyObject;
    this.deleteObject = deleteObject;
    this.deleteMultiObjects = deleteMultiObjects;
    this.getObject = getObject;
    this.getObjectV2 = getObjectV2;
    this.getObjectToFile = getObjectToFile;
    this.getObjectAcl = getObjectAcl;
    this.headObject = headObject;
    this.appendObject = appendObject;
    this.listObjects = listObjects;
    this.renameObject = renameObject;
    this.fetchObject = fetchObject;
    this.putFetchTask = putFetchTask;
    this.listObjectsType2 = listObjectsType2;
    this.listObjectVersions = listObjectVersions;
    this.putObject = putObject;
    this.putObjectFromFile = putObjectFromFile;
    this.putObjectAcl = putObjectAcl;
    this.setObjectMeta = setObjectMeta;
    this.createMultipartUpload = createMultipartUpload;
    this.uploadPart = uploadPart;
    this.uploadPartFromFile = uploadPartFromFile;
    this.completeMultipartUpload = completeMultipartUpload;
    this.abortMultipartUpload = abortMultipartUpload;
    this.uploadPartCopy = uploadPartCopy;
    this.listMultipartUploads = listMultipartUploads;
    this.listParts = listParts;
    this.downloadFile = downloadFile;
    this.putObjectTagging = putObjectTagging;
    this.getObjectTagging = getObjectTagging;
    this.deleteObjectTagging = deleteObjectTagging;
    this.listJobs = listJobs;
    this.createJob = createJob;
    this.deleteJob = deleteJob;
    this.describeJob = describeJob;
    this.updateJobStatus = updateJobStatus;
    this.updateJobPriority = updateJobPriority;
    this.restoreObject = restoreObject;
    this.uploadFile = uploadFile;
    this.getPreSignedUrl = getPreSignedUrl;
    this.calculatePostSignature = calculatePostSignature;
    this.preSignedPostSignature = calculatePostSignature;
    this.preSignedPolicyURL = preSignedPolicyURL;
    this.getBucketIntelligenttiering = getBucketIntelligenttiering;
    this.listStorageLens = listStorageLens;
    this.deleteStorageLens = deleteStorageLens;
    this.getStorageLens = getStorageLens;
    this.putStorageLens = putStorageLens;
    this.putBucketNotificationType2 = putBucketNotificationType2;
    this.getBucketNotificationType2 = getBucketNotificationType2;
    this.putSymlink = putSymlink;
    this.getSymlink = getSymlink;
    this.putBucketAccessMonitor = putBucketAccessMonitor;
    this.getBucketAccessMonitor = getBucketAccessMonitor;
    this.putQosPolicy = putQosPolicy;
    this.getQosPolicy = getQosPolicy;
    this.deleteQosPolicy = deleteQosPolicy;
    this.createMultiRegionAccessPoint = createMultiRegionAccessPoint;
    this.getMultiRegionAccessPoint = getMultiRegionAccessPoint;
    this.listMultiRegionAccessPoints = listMultiRegionAccessPoints;
    this.getMultiRegionAccessPointRoutes = getMultiRegionAccessPointRoutes;
    this.deleteMultiRegionAccessPoint = deleteMultiRegionAccessPoint;
    this.submitMultiRegionAccessPointRoutes = submitMultiRegionAccessPointRoutes;
    this.putMultiRegionAccessPointMirrorBack = putMultiRegionAccessPointMirrorBack;
    this.getMultiRegionAccessPointMirrorBack = getMultiRegionAccessPointMirrorBack;
    this.deleteMultiRegionAccessPointMirrorBack = deleteMultiRegionAccessPointMirrorBack;
    this.putBucketPrivateM3U8 = putBucketPrivateM3U8;
    this.getBucketPrivateM3U8 = getBucketPrivateM3U8;
    this.putBucketTrash = putBucketTrash;
    this.getBucketTrash = getBucketTrash;
  }

}

const CancelToken = axios.CancelToken; // for export

class TosClient extends InnerClient {}

TosClient.TosServerError = TosServerError;
TosClient.isCancel = isCancelError;
TosClient.CancelError = CancelError;
TosClient.TosServerCode = exports.TosServerCode;
TosClient.TosClientError = TosClientError;
TosClient.CancelToken = CancelToken;
TosClient.ACLType = exports.ACLType;
TosClient.StorageClassType = exports.StorageClassType;
TosClient.MetadataDirectiveType = exports.MetadataDirectiveType;
TosClient.AzRedundancyType = exports.AzRedundancyType;
TosClient.PermissionType = exports.PermissionType;
TosClient.GranteeType = exports.GranteeType;
TosClient.CannedType = exports.CannedType;
TosClient.HttpMethodType = exports.HttpMethodType;
TosClient.LifecycleStatusType = exports.LifecycleStatusType;
TosClient.StatusType = exports.StatusType;
TosClient.RedirectType = exports.RedirectType;
TosClient.StorageClassInheritDirectiveType = exports.StorageClassInheritDirectiveType;
TosClient.TierType = exports.TierType;
TosClient.VersioningStatusType = exports.VersioningStatusType;
TosClient.createDefaultRateLimiter = createDefaultRateLimiter$1;
TosClient.DataTransferType = exports.DataTransferType;
TosClient.UploadEventType = exports.UploadEventType;
TosClient.DownloadEventType = exports.DownloadEventType;
TosClient.ResumableCopyEventType = exports.ResumableCopyEventType;
TosClient.ReplicationStatusType = exports.ReplicationStatusType;
TosClient.AccessPointStatusType = exports.AccessPointStatusType;
TosClient.TransferAccelerationStatusType = exports.TransferAccelerationStatusType;
TosClient.MRAPMirrorBackRedirectPolicyType = exports.MRAPMirrorBackRedirectPolicyType;
TosClient.ShareLinkClient = ShareLinkClient;

exports.CancelError = CancelError;
exports.CancelToken = CancelToken;
exports.ShareLinkClient = ShareLinkClient;
exports.TOS = TosClient;
exports.TosClient = TosClient;
exports.TosClientError = TosClientError;
exports.TosServerError = TosServerError;
exports.createDefaultRateLimiter = createDefaultRateLimiter$1;
exports.default = TosClient;
exports.isCancel = isCancelError;
//# sourceMappingURL=tos.cjs.development.js.map
