package org.jeecg.modules.system.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.web.servlet.ModelAndView;


import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.system.entity.SysSensitiveWord;
import org.jeecg.modules.system.service.ISysSensitiveWordService;
import org.jeecg.modules.system.service.ISysSensitiveWordHitLogService;
import org.jeecg.modules.system.service.ISysSensitiveWordService.ImportResult;
import org.jeecg.modules.system.service.ISysSensitiveWordService.SensitiveWordCheckResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.system.vo.LoginUser;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import lombok.extern.slf4j.Slf4j;

/**
 * @Description: 敏感词管理
 * @Author: jeecg-boot
 * @Date: 2025-06-22
 * @Version: V1.0
 */
@Api(tags = "敏感词管理")
@RestController
@RequestMapping("/sys/sensitiveWord")
@Slf4j
public class SysSensitiveWordController extends JeecgController<SysSensitiveWord, ISysSensitiveWordService> {

    @Autowired
    private ISysSensitiveWordService sensitiveWordService;

    @Autowired
    private ISysSensitiveWordHitLogService hitLogService;

    /**
     * 分页列表查询
     */
    @AutoLog(value = "敏感词管理-分页列表查询")
    @ApiOperation(value = "敏感词管理-分页列表查询", notes = "敏感词管理-分页列表查询")
    @GetMapping(value = "/list")
    public Result<IPage<SysSensitiveWord>> queryPageList(SysSensitiveWord sensitiveWord,
                                                         @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                         @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                         HttpServletRequest req) {
        QueryWrapper<SysSensitiveWord> queryWrapper = QueryGenerator.initQueryWrapper(sensitiveWord, req.getParameterMap());
        
        // 默认按命中次数降序，创建时间降序
        queryWrapper.orderByDesc("hit_count").orderByDesc("create_time");
        
        Page<SysSensitiveWord> page = new Page<SysSensitiveWord>(pageNo, pageSize);
        IPage<SysSensitiveWord> pageList = sensitiveWordService.page(page, queryWrapper);
        
        return Result.OK(pageList);
    }

    /**
     * 添加
     */
    @AutoLog(value = "敏感词管理-添加")
    @ApiOperation(value = "敏感词管理-添加", notes = "敏感词管理-添加")
    @PostMapping(value = "/add")
    public Result<String> add(@RequestBody SysSensitiveWord sensitiveWord) {
        try {
            // 检查敏感词是否已存在
            QueryWrapper<SysSensitiveWord> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("word", sensitiveWord.getWord());
            SysSensitiveWord existingWord = sensitiveWordService.getOne(queryWrapper);
            
            if (existingWord != null) {
                return Result.error("敏感词已存在", (String)null);
            }
            
            // 设置默认值
            if (sensitiveWord.getLevel() == null) {
                sensitiveWord.setLevel(1); // 默认低危
            }
            if (sensitiveWord.getStatus() == null) {
                sensitiveWord.setStatus(1); // 默认启用
            }
            if (sensitiveWord.getHitCount() == null) {
                sensitiveWord.setHitCount(0);
            }
            if (sensitiveWord.getSource() == null) {
                sensitiveWord.setSource("CUSTOM");
            }
            
            sensitiveWordService.save(sensitiveWord);
            
            // 刷新缓存
            sensitiveWordService.refreshCache();
            
            return Result.OK("添加成功！");
        } catch (Exception e) {
            log.error("添加敏感词失败", e);
            return Result.error("添加失败：" + e.getMessage(), (String)null);
        }
    }

    /**
     * 编辑
     */
    @AutoLog(value = "敏感词管理-编辑")
    @ApiOperation(value = "敏感词管理-编辑", notes = "敏感词管理-编辑")
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<String> edit(@RequestBody SysSensitiveWord sensitiveWord) {
        try {
            // 检查敏感词是否已存在（排除自己）
            QueryWrapper<SysSensitiveWord> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("word", sensitiveWord.getWord());
            queryWrapper.ne("id", sensitiveWord.getId());
            SysSensitiveWord existingWord = sensitiveWordService.getOne(queryWrapper);
            
            if (existingWord != null) {
                return Result.error("敏感词已存在", (String)null);
            }
            
            sensitiveWordService.updateById(sensitiveWord);
            
            // 刷新缓存
            sensitiveWordService.refreshCache();
            
            return Result.OK("编辑成功!");
        } catch (Exception e) {
            log.error("编辑敏感词失败", e);
            return Result.error("编辑失败：" + e.getMessage(), (String)null);
        }
    }

    /**
     * 通过id删除
     */
    @AutoLog(value = "敏感词管理-通过id删除")
    @ApiOperation(value = "敏感词管理-通过id删除", notes = "敏感词管理-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<String> delete(@RequestParam(name = "id", required = true) String id) {
        try {
            sensitiveWordService.removeById(id);
            
            // 刷新缓存
            sensitiveWordService.refreshCache();
            
            return Result.OK("删除成功!");
        } catch (Exception e) {
            log.error("删除敏感词失败", e);
            return Result.error("删除失败：" + e.getMessage(), (String)null);
        }
    }

    /**
     * 批量删除
     */
    @AutoLog(value = "敏感词管理-批量删除")
    @ApiOperation(value = "敏感词管理-批量删除", notes = "敏感词管理-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<String> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        try {
            this.sensitiveWordService.removeByIds(Arrays.asList(ids.split(",")));
            
            // 刷新缓存
            sensitiveWordService.refreshCache();
            
            return Result.OK("批量删除成功!");
        } catch (Exception e) {
            log.error("批量删除敏感词失败", e);
            return Result.error("批量删除失败：" + e.getMessage(), (String)null);
        }
    }

    /**
     * 通过id查询
     */
    @AutoLog(value = "敏感词管理-通过id查询")
    @ApiOperation(value = "敏感词管理-通过id查询", notes = "敏感词管理-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<SysSensitiveWord> queryById(@RequestParam(name = "id", required = true) String id) {
        SysSensitiveWord sensitiveWord = sensitiveWordService.getById(id);
        if (sensitiveWord == null) {
            return Result.error("未找到对应数据", (SysSensitiveWord)null);
        }
        return Result.OK(sensitiveWord);
    }

    /**
     * 导出excel
     */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, SysSensitiveWord sensitiveWord) {
        return super.exportXls(request, sensitiveWord, SysSensitiveWord.class, "敏感词管理");
    }

    /**
     * 通过excel导入数据
     */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<String> importExcel(HttpServletRequest request, HttpServletResponse response) {
        MultipartFile file = null;
        try {
            MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
            file = multipartRequest.getFile("file");
            LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            String createBy = loginUser.getUsername();
            
            ImportResult result = sensitiveWordService.importSensitiveWords(file, createBy);
            
            if (result.isSuccess()) {
                return Result.OK(result.getMessage());
            } else {
                return Result.error(result.getMessage(), (String)null);
            }
        } catch (Exception e) {
            log.error("导入敏感词失败", e);
            return Result.error("导入失败：" + e.getMessage(), (String)null);
        }
    }

    /**
     * 从houbb库导入敏感词
     */
    @AutoLog(value = "敏感词管理-从houbb库导入")
    @ApiOperation(value = "从houbb库导入敏感词", notes = "从houbb库导入敏感词")
    @PostMapping(value = "/importFromHoubb")
    public Result<String> importFromHoubb() {
        try {
            LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            String createBy = loginUser.getUsername();
            ImportResult result = sensitiveWordService.importFromHoubb(createBy);
            
            if (result.isSuccess()) {
                return Result.OK(result.getMessage());
            } else {
                return Result.error(result.getMessage(), (String)null);
            }
        } catch (Exception e) {
            log.error("从houbb库导入敏感词失败", e);
            return Result.error("导入失败：" + e.getMessage(), (String)null);
        }
    }

    /**
     * 敏感词检测
     */
    @AutoLog(value = "敏感词管理-敏感词检测")
    @ApiOperation(value = "敏感词检测", notes = "检测文本中是否包含敏感词")
    @PostMapping(value = "/check")
    public Result<SensitiveWordCheckResult> checkSensitiveWords(@RequestParam("text") String text) {
        try {
            SensitiveWordCheckResult result = sensitiveWordService.checkSensitiveWords(text);
            return Result.OK(result);
        } catch (Exception e) {
            log.error("敏感词检测失败", e);
            return Result.error("检测失败：" + e.getMessage(), (SensitiveWordCheckResult)null);
        }
    }

    /**
     * 敏感词替换
     */
    @AutoLog(value = "敏感词管理-敏感词替换")
    @ApiOperation(value = "敏感词替换", notes = "替换文本中的敏感词")
    @PostMapping(value = "/replace")
    public Result<String> replaceSensitiveWords(@RequestParam("text") String text,
                                                @RequestParam(value = "replacement", defaultValue = "*") String replacement) {
        try {
            String result = sensitiveWordService.replaceSensitiveWords(text, replacement);
            return Result.OK(result);
        } catch (Exception e) {
            log.error("敏感词替换失败", e);
            return Result.error("替换失败：" + e.getMessage(), (String)null);
        }
    }

    /**
     * 刷新敏感词缓存
     */
    @AutoLog(value = "敏感词管理-刷新缓存")
    @ApiOperation(value = "刷新敏感词缓存", notes = "刷新敏感词缓存")
    @PostMapping(value = "/refreshCache")
    public Result<String> refreshCache() {
        try {
            sensitiveWordService.refreshCache();
            return Result.OK("缓存刷新成功！");
        } catch (Exception e) {
            log.error("刷新敏感词缓存失败", e);
            return Result.error("缓存刷新失败：" + e.getMessage(), (String)null);
        }
    }

    /**
     * 获取敏感词统计信息
     */
    @AutoLog(value = "敏感词管理-获取统计信息")
    @ApiOperation(value = "获取敏感词统计信息", notes = "获取敏感词统计信息")
    @GetMapping(value = "/statistics")
    public Result<Map<String, Object>> getStatistics() {
        try {
            Map<String, Object> statistics = sensitiveWordService.getStatistics();
            return Result.OK(statistics);
        } catch (Exception e) {
            log.error("获取敏感词统计信息失败", e);
            return Result.error("获取统计信息失败：" + e.getMessage(), (Map<String, Object>)null);
        }
    }

    /**
     * 获取热门敏感词
     */
    @AutoLog(value = "敏感词管理-获取热门敏感词")
    @ApiOperation(value = "获取热门敏感词", notes = "获取热门敏感词")
    @GetMapping(value = "/topHitWords")
    public Result<List<Map<String, Object>>> getTopHitWords(@RequestParam(value = "limit", defaultValue = "10") Integer limit) {
        try {
            List<Map<String, Object>> topWords = sensitiveWordService.getTopHitWords(limit);
            return Result.OK(topWords);
        } catch (Exception e) {
            log.error("获取热门敏感词失败", e);
            return Result.error("获取热门敏感词失败：" + e.getMessage(), (List<Map<String, Object>>)null);
        }
    }

    /**
     * 获取分类统计
     */
    @AutoLog(value = "敏感词管理-获取分类统计")
    @ApiOperation(value = "获取分类统计", notes = "获取分类统计")
    @GetMapping(value = "/categoryStatistics")
    public Result<List<Map<String, Object>>> getCategoryStatistics() {
        try {
            List<Map<String, Object>> statistics = sensitiveWordService.getCategoryStatistics();
            return Result.OK(statistics);
        } catch (Exception e) {
            log.error("获取分类统计失败", e);
            return Result.error("获取分类统计失败：" + e.getMessage(), (List<Map<String, Object>>)null);
        }
    }

    /**
     * 获取级别统计
     */
    @AutoLog(value = "敏感词管理-获取级别统计")
    @ApiOperation(value = "获取级别统计", notes = "获取级别统计")
    @GetMapping(value = "/levelStatistics")
    public Result<List<Map<String, Object>>> getLevelStatistics() {
        try {
            List<Map<String, Object>> statistics = sensitiveWordService.getLevelStatistics();
            return Result.OK(statistics);
        } catch (Exception e) {
            log.error("获取级别统计失败", e);
            return Result.error("获取级别统计失败：" + e.getMessage(), (List<Map<String, Object>>)null);
        }
    }

    /**
     * 获取命中趋势
     */
    @AutoLog(value = "敏感词管理-获取命中趋势")
    @ApiOperation(value = "获取命中趋势", notes = "获取敏感词命中趋势")
    @GetMapping(value = "/hitTrend")
    public Result<List<Map<String, Object>>> getHitTrend(@RequestParam(value = "days", defaultValue = "7") Integer days) {
        try {
            List<Map<String, Object>> trend = hitLogService.getHitTrend(days);
            return Result.OK(trend);
        } catch (Exception e) {
            log.error("获取命中趋势失败", e);
            return Result.error("获取命中趋势失败：" + e.getMessage(), (List<Map<String, Object>>)null);
        }
    }

    /**
     * 获取综合统计报告
     */
    @AutoLog(value = "敏感词管理-获取综合统计报告")
    @ApiOperation(value = "获取综合统计报告", notes = "获取敏感词综合统计报告")
    @GetMapping(value = "/comprehensiveReport")
    public Result<Map<String, Object>> getComprehensiveReport(@RequestParam(value = "days", defaultValue = "7") Integer days) {
        try {
            Map<String, Object> report = hitLogService.getComprehensiveReport(days);
            return Result.OK(report);
        } catch (Exception e) {
            log.error("获取综合统计报告失败", e);
            return Result.error("获取综合统计报告失败：" + e.getMessage(), (Map<String, Object>)null);
        }
    }
}
