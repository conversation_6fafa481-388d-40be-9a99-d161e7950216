# add_masks接口坐标转换技术文档

## 📋 文档概述

本文档详细说明了add_masks接口中坐标转换的具体实现逻辑，包括转换公式、基准选择、计算示例、竞争对手对比分析以及边界情况处理机制。

**文档版本**：v1.0  
**创建时间**：2025-07-09  
**适用接口**：JianyingAssistantService.addMasks()  
**核心文件**：`AigcViewRd\jeecg-boot-module-system\src\main\java\org\jeecg\modules\jianying\service\JianyingAssistantService.java`

---

## 🎯 1. 转换公式说明

### 1.1 核心转换方法

```java
/**
 * 坐标转换：像素值转归一化坐标
 * 基于竞争对手分析：使用视频尺寸的一半作为基准
 */
private double convertPixelToNormalizedCoordinate(Integer pixelValue, int videoSize) {
    if (pixelValue == null) return 0.0;
    // 竞争对手的转换公式：pixelValue / (videoSize / 2)
    return pixelValue.doubleValue() / (videoSize / 2.0);
}
```

### 1.2 公式原理

- **归一化坐标** = **像素值** ÷ (**视频尺寸** ÷ 2)
- **核心思想**：将像素坐标转换为剪映内部使用的归一化坐标系统
- **除以2的原因**：剪映的坐标系统以视频中心为原点，需要用半径作为基准

### 1.3 调用方式

```java
// X坐标转换
config.put("centerX", convertPixelToNormalizedCoordinate(request.getZjX(), videoWidth));

// Y坐标转换  
config.put("centerY", convertPixelToNormalizedCoordinate(request.getZjY(), videoHeight));
```

---

## 🎯 2. 基准选择逻辑

### 2.1 为什么使用视频素材尺寸？

#### 技术原因
1. **剪映内部坐标系统**：
   - 蒙版定位相对于**视频内容**而不是画布
   - 蒙版需要跟随视频内容进行缩放和变换
   - 使用视频尺寸确保蒙版在不同画布尺寸下都能正确定位

2. **竞争对手分析结果**：
   - 通过对比草稿文件发现，竞争对手使用视频素材尺寸作为基准
   - 确保与竞争对手的完全兼容性

3. **灵活性考虑**：
   - 画布尺寸可能与视频尺寸不同
   - 使用视频尺寸能适应各种画布配置

### 2.2 修复前后对比

| 项目 | 修复前逻辑 | 修复后逻辑 | 竞争对手逻辑 | 一致性 |
|------|------------|------------|--------------|--------|
| 基准选择 | 画布尺寸 | 视频尺寸 | 视频尺寸 | ✅ |
| X坐标公式 | `(X * 2) / canvasWidth` | `X / (videoWidth / 2)` | `X / (videoWidth / 2)` | ✅ |
| Y坐标公式 | `(Y * 2) / canvasHeight` | `Y / (videoHeight / 2)` | `Y / (videoHeight / 2)` | ✅ |

---

## 🎯 3. 实际计算示例

### 3.1 输入参数示例
```json
{
  "zj_X": 100,
  "zj_Y": -100,
  "zj_width": 500,
  "zj_height": 500
}
```

### 3.2 完整计算过程

#### 步骤1：获取视频尺寸
```java
int[] videoSize = getVideoSize(draft);
// 假设获取到：videoWidth = 1920, videoHeight = 1080
int videoWidth = videoSize[0];   // 1920
int videoHeight = videoSize[1];  // 1080
```

#### 步骤2：X坐标转换
```java
centerX = convertPixelToNormalizedCoordinate(100, 1920);
// 计算过程：100 / (1920 / 2) = 100 / 960 = 0.10416666666666667
```

#### 步骤3：Y坐标转换
```java
centerY = convertPixelToNormalizedCoordinate(-100, 1080);
// 计算过程：-100 / (1080 / 2) = -100 / 540 = -0.18518518518518517
```

#### 步骤4：最终结果
```json
{
  "centerX": 0.10416666666666667,
  "centerY": -0.18518518518518517
}
```

### 3.3 数值验证对比

**输入**：X=100, Y=-100, 视频尺寸1920x1080

| 方案 | centerX计算 | centerY计算 | 结果状态 |
|------|-------------|-------------|----------|
| 修复前 | `(100*2)/1024=0.1953125` | `(-100*2)/1024=-0.1953125` | ❌ 不一致 |
| 修复后 | `100/(1920/2)=0.10416666666666667` | `-100/(1080/2)=-0.18518518518518517` | ✅ 完全一致 |
| 竞争对手 | `0.10416666666666667` | `-0.18518518518518517` | 🎯 基准 |

---

## 🎯 4. getVideoSize方法详解

### 4.1 方法实现
```java
/**
 * 获取视频尺寸（用于坐标转换基准）
 * 竞争对手使用视频素材尺寸而非画布尺寸作为坐标转换基准
 */
private int[] getVideoSize(JSONObject draft) {
    try {
        JSONObject materials = draft.getJSONObject("materials");
        com.alibaba.fastjson.JSONArray videos = materials.getJSONArray("videos");
        if (videos != null && videos.size() > 0) {
            JSONObject firstVideo = videos.getJSONObject(0);
            int width = firstVideo.getIntValue("width");
            int height = firstVideo.getIntValue("height");
            if (width > 0 && height > 0) {
                return new int[]{width, height};
            }
        }
    } catch (Exception e) {
        log.warn("获取视频尺寸失败，使用默认值: {}", e.getMessage());
    }
    // 默认使用1920x1080（标准高清尺寸）
    return new int[]{1920, 1080};
}
```

### 4.2 核心功能
1. **动态获取**：从草稿文件的`materials.videos`数组中获取第一个视频的尺寸
2. **数据验证**：确保获取的宽高值大于0
3. **异常处理**：捕获所有可能的异常，避免程序崩溃
4. **默认值机制**：当无法获取有效尺寸时，使用1920x1080作为标准高清默认值

### 4.3 数据获取路径
```
草稿文件结构：
├── materials
│   └── videos[]
│       └── [0]
│           ├── width: 1920
│           └── height: 1080
```

---

## 🎯 5. 边界情况处理

### 5.1 异常情况处理机制

#### 情况1：草稿文件中没有materials字段
```java
// 捕获异常，返回默认值[1920, 1080]
catch (Exception e) {
    log.warn("获取视频尺寸失败，使用默认值: {}", e.getMessage());
}
```

#### 情况2：materials中没有videos数组
```java
if (videos != null && videos.size() > 0)
// 条件不满足时，返回默认值
```

#### 情况3：videos数组为空
```java
videos.size() > 0  // 检查数组大小
// 为空时返回默认值
```

#### 情况4：视频对象缺少width/height字段
```java
int width = firstVideo.getIntValue("width");  // 缺少时返回0
if (width > 0 && height > 0)  // 验证有效性
```

#### 情况5：width/height为0或负数
```java
if (width > 0 && height > 0)  // 确保为正数
// 无效时返回默认值
```

#### 情况6：输入参数为null
```java
if (pixelValue == null) return 0.0;  // 直接返回0
```

### 5.2 默认值选择理由

- **1920x1080**：标准高清分辨率，最常用的视频尺寸
- **向下兼容**：确保在任何情况下都能正常工作
- **合理基准**：提供合理的坐标转换基准，避免极端数值

---

## 🎯 6. 尺寸转换说明

### 6.1 尺寸转换方法
```java
/**
 * 尺寸转归一化值
 * 基于竞争对手分析：使用固定1000作为基准
 */
private double convertSizeToNormalized(Integer size) {
    if (size == null) return 0.5; // 默认50%
    // 竞争对手使用固定1000作为基准：500/1000=0.5 ✅
    return size.doubleValue() / 1000.0;
}
```

### 6.2 尺寸转换示例
```java
// 输入：width=500, height=500
width = convertSizeToNormalized(500);   // 500/1000 = 0.5
height = convertSizeToNormalized(500);  // 500/1000 = 0.5
```

---

## 📊 7. 完整配置示例

### 7.1 输入参数
```json
{
  "zj_X": 100,
  "zj_Y": -100,
  "zj_width": 500,
  "zj_height": 500,
  "zj_feather": 10,
  "zj_rotation": 180,
  "zj_roundCorner": 10,
  "zj_invert": true
}
```

### 7.2 最终蒙版配置
```json
{
  "config": {
    "aspectRatio": 1,
    "centerX": 0.10416666666666667,
    "centerY": -0.18518518518518517,
    "feather": 0.1,
    "height": 0.5,
    "invert": true,
    "rotation": 180,
    "roundCorner": 0.1,
    "width": 0.5
  }
}
```

---

## 🔧 8. 技术要点总结

### 8.1 关键成功因素
1. **精确转换**：使用与竞争对手完全一致的转换公式
2. **动态适应**：根据实际视频尺寸进行转换，适应不同项目
3. **稳定可靠**：完善的异常处理和默认值机制
4. **完全兼容**：与竞争对手的输出结果完全一致

### 8.2 维护注意事项
1. **不要修改转换公式**：当前公式经过竞争对手分析验证，确保兼容性
2. **保持默认值**：1920x1080是经过验证的合理默认值
3. **异常处理完整性**：确保所有边界情况都有适当处理
4. **日志记录**：保持适当的日志记录，便于问题排查

### 8.3 性能考虑
- **轻量级计算**：转换公式简单，性能开销极小
- **缓存机制**：视频尺寸在单次请求中不变，可考虑缓存
- **异常处理开销**：异常情况下的处理开销可接受

---

## 📝 9. 更新日志

| 版本 | 日期 | 更新内容 | 负责人 |
|------|------|----------|--------|
| v1.0 | 2025-07-09 | 初始版本，完整的坐标转换技术文档 | 开发团队 |

---

## 📞 10. 技术支持

如有技术问题或需要进一步说明，请联系开发团队。

**文档状态**：✅ 已完成  
**最后更新**：2025-07-09  
**下次审查**：根据需要
