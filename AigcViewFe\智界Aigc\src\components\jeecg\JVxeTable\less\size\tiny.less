.j-vxe-table-box {

  @height: 24px;
  @lineHeight: 1.5;
  @spacing: 4px;
  @fontSize: 14px;
  @borderRadius: 2px;

  &.size--tiny {

    .vxe-table--header .vxe-cell--checkbox {
      position: relative;
      top: 2px;
      right: 1px;
    }

    .vxe-table--body .vxe-cell--checkbox {
      line-height: 2;
    }

    .vxe-cell {
      padding: 0 5px;
      font-size: @fontSize;
      line-height: @lineHeight;
    }

    .vxe-table .vxe-header--column .vxe-cell {
      font-size: 12px;
    }

    .vxe-body--column.col--actived {
      padding: 0;

      .vxe-cell {
        padding: 0;
      }

    }

    // ant输入框
    .ant-input,
      //  ant下拉框
    .ant-select-selection {
      padding: 2px @spacing;
      height: @height;
      font-size: @fontSize;
      border-radius: @borderRadius;
      line-height: @lineHeight;
    }

    // 输入框图标对齐
    .ant-input-affix-wrapper {
      & .ant-input-prefix {
        left: 4px;
      }

      & .ant-input:not(:first-child) {
        padding-left: 20px;
      }
    }

    // 按钮 addon
    .ant-input-group-addon {
      border-color: transparent;
      border-radius: @borderRadius;
    }


    //  ant下拉多选框
    .ant-select-selection--multiple {
      min-height: @height;

      & .ant-select-selection__rendered > ul > li {
        height: calc(@height - 6px);
        font-size: calc(@fontSize - 2px);
        margin-top: 0;
        line-height: @lineHeight;
        padding: 0 18px 0 4px;

      }

      & .ant-select-selection__clear,
      & .ant-select-arrow {
        top: 12px;
      }
    }


    // ant按钮
    .ant-upload {
      width: 100%;

      .ant-btn {
        width: 100%;
        height: @height;
        padding: 0 8px;
        font-size: @fontSize;
        border-color: transparent;
        background-color: transparent;
        border-radius: @borderRadius;

        &:hover {
          background-color: rgba(255, 255, 255, 0.3);
        }
      }
    }

    .ant-select-selection__rendered {
      line-height: @lineHeight;
      margin-left: 0;
    }


    // 工具栏
    .j-vxe-toolbar {
      margin-bottom: 4px;

      .ant-form-item-label,
      .ant-form-item-control {
        line-height: 22px;
      }

      .ant-form-inline .ant-form-item {
        margin-right: 4px;
      }

    }
  }

  /** 内置属性 */

  .vxe-table.size--tiny {
    & .vxe-table--expanded {
      padding-right: 0;
    }

    & .vxe-body--expanded-cell {
      padding: 8px;
    }
  }

  .size--tiny .vxe-loading .vxe-loading--spinner {
    width: 38px;
    height: 38px
  }

  .vxe-table.size--tiny .vxe-body--column.col--ellipsis,
  .vxe-table.size--tiny .vxe-footer--column.col--ellipsis,
  .vxe-table.size--tiny .vxe-header--column.col--ellipsis,
  .vxe-table.vxe-editable.size--tiny .vxe-body--column {
    height: @height;
  }

  .vxe-table.size--tiny {
    font-size: 12px
  }

  .vxe-table.size--tiny .vxe-table--empty-block,
  .vxe-table.size--tiny .vxe-table--empty-placeholder {
    min-height: @height;
  }

  .vxe-table.size--tiny .vxe-body--column:not(.col--ellipsis),
  .vxe-table.size--tiny .vxe-footer--column:not(.col--ellipsis),
  .vxe-table.size--tiny .vxe-header--column:not(.col--ellipsis) {
    padding: 4px 0
  }

  .vxe-table.size--tiny .vxe-cell .vxe-default-input,
  .vxe-table.size--tiny .vxe-cell .vxe-default-select,
  .vxe-table.size--tiny .vxe-cell .vxe-default-textarea {
    height: @height;
  }

  .vxe-table.size--tiny .vxe-cell .vxe-default-input[type=date]::-webkit-inner-spin-button {
    margin-top: 1px
  }

  .vxe-table.size--tiny.virtual--x .col--ellipsis .vxe-cell,
  .vxe-table.size--tiny.virtual--y .col--ellipsis .vxe-cell,
  .vxe-table.size--tiny .vxe-body--column.col--ellipsis .vxe-cell,
  .vxe-table.size--tiny .vxe-footer--column.col--ellipsis .vxe-cell,
  .vxe-table.size--tiny .vxe-header--column.col--ellipsis .vxe-cell {
    max-height: @height;
  }

  .vxe-table.size--tiny .vxe-cell--checkbox .vxe-checkbox--icon,
  .vxe-table.size--tiny .vxe-cell--radio .vxe-radio--icon {
    font-size: 14px
  }


  .vxe-table.size--tiny .vxe-table--filter-option > .vxe-checkbox--icon,
  .vxe-table.size--small .vxe-table--filter-option > .vxe-checkbox--icon {
    font-size: 14px
  }

  .vxe-modal--wrapper.size--tiny .vxe-export--panel-column-option > .vxe-checkbox--icon,
  .vxe-modal--wrapper.size--small .vxe-export--panel-column-option > .vxe-checkbox--icon {
    font-size: 14px
  }

  .vxe-grid.size--tiny {
    font-size: 12px
  }

  .vxe-toolbar.size--tiny {
    font-size: 12px;
    height: 46px
  }

  .vxe-toolbar.size--tiny .vxe-custom--option > .vxe-checkbox--icon {
    font-size: 14px
  }

  .vxe-pager.size--tiny {
    font-size: 12px;
    height: @height;
  }

  .vxe-checkbox.size--tiny {
    font-size: 12px
  }

  .vxe-checkbox.size--tiny .vxe-checkbox--icon {
    font-size: 14px
  }

  .vxe-radio-button.size--tiny .vxe-radio--label {
    line-height: 26px
  }

  .vxe-radio.size--tiny {
    font-size: 12px
  }

  .vxe-radio.size--tiny .vxe-radio--icon {
    font-size: 14px
  }

  .vxe-input.size--tiny {
    font-size: 12px;
    height: @height;
  }

  .vxe-input.size--tiny .vxe-input--inner[type=date]::-webkit-inner-spin-button,
  .vxe-input.size--tiny .vxe-input--inner[type=month]::-webkit-inner-spin-button,
  .vxe-input.size--tiny .vxe-input--inner[type=week]::-webkit-inner-spin-button {
    margin-top: 0
  }

  .vxe-dropdown--panel.size--tiny {
    font-size: 12px
  }

  .vxe-textarea--autosize.size--tiny,
  .vxe-textarea.size--tiny {
    font-size: 12px
  }

  .vxe-textarea.size--tiny:not(.is--autosize) {
    min-height: @height;
  }

  .vxe-button.size--tiny {
    font-size: 12px
  }

  .vxe-button.size--tiny.type--button {
    height: @height;
  }

  .vxe-button.size--tiny.type--button.is--circle {
    min-width: @height;
  }

  .vxe-button.size--tiny.type--button.is--round {
    border-radius: 14px
  }

  .vxe-button.size--tiny .vxe-button--icon,
  .vxe-button.size--tiny .vxe-button--loading-icon {
    min-width: 12px
  }

  .vxe-modal--wrapper.size--tiny {
    font-size: 12px
  }

  .vxe-form.size--tiny {
    font-size: 12px
  }

  .vxe-form.size--tiny .vxe-form--item-inner {
    min-height: 30px
  }

  .vxe-form.size--tiny .vxe-default-input[type=reset],
  .vxe-form.size--tiny .vxe-default-input[type=submit] {
    line-height: 26px
  }

  .vxe-form.size--tiny .vxe-default-input,
  .vxe-form.size--tiny .vxe-default-select {
    height: @height;
  }

  .vxe-select--panel.size--tiny,
  .vxe-select.size--tiny {
    font-size: 12px
  }

  .vxe-select--panel.size--tiny .vxe-optgroup--title,
  .vxe-select--panel.size--tiny .vxe-select-option {
    height: 24px;
    line-height: 24px
  }

  .vxe-switch.size--tiny {
    font-size: 12px
  }


  .vxe-pulldown--panel.size--tiny,
  .vxe-pulldown.size--tiny {
    font-size: 12px
  }


}


