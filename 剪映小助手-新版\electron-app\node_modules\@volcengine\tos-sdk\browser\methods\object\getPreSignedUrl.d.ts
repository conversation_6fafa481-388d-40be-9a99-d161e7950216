import TOSBase from '../base';
export interface GetPreSignedUrlInput {
    bucket?: string;
    key: string;
    /**
     * default: 'GET'
     */
    method?: 'GET' | 'PUT';
    /**
     * unit: second, default: 1800
     */
    expires?: number;
    alternativeEndpoint?: string;
    response?: {
        contentType?: string;
        contentDisposition?: string;
    };
    versionId?: string;
    query?: Record<string, string>;
    /**
     * default: false
     * if set true. generate domain will direct use `endpoint` or `alternativeEndpoint`.
     */
    isCustomDomain?: boolean;
}
export declare function getPreSignedUrl(this: TOSBase, input: GetPreSignedUrlInput | string): string;
export default getPreSignedUrl;
