package org.jeecg.modules.api.service;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @Description: 用户活跃状态统计服务接口
 * @Author: AigcView
 * @Date: 2025-01-30
 * @Version: V1.0
 */
public interface IUserActivityStatsService {

    // ==================== 基础统计方法 ====================

    /**
     * 获取多维度在线用户统计
     * 一次查询获取5分钟、15分钟、1小时、今日活跃用户数
     * @return 多维度统计数据
     */
    Map<String, Object> getMultiDimensionStats();

    /**
     * 获取当前在线用户数（15分钟内活跃）
     * @return 当前在线用户数
     */
    int getCurrentOnlineUsersCount();

    /**
     * 获取今日活跃用户数
     * @return 今日活跃用户数
     */
    int getTodayActiveUsersCount();

    /**
     * 获取指定时间范围内的活跃用户数
     * @param minutes 时间范围（分钟）
     * @return 活跃用户数
     */
    int getActiveUsersCount(int minutes);

    // ==================== 高级统计方法 ====================

    /**
     * 获取用户活跃度分布统计
     * 按时间段统计活跃用户分布
     * @return 活跃度分布数据
     */
    List<Map<String, Object>> getUserActivityDistribution();

    /**
     * 获取用户留存率统计
     * 计算不同时间段的用户留存情况
     * @return 留存率统计数据
     */
    List<Map<String, Object>> getUserRetentionStats();

    /**
     * 获取实时在线用户详情（分页）
     * 支持按活跃时间排序的在线用户列表
     * @param page 页码（从1开始）
     * @param size 每页大小
     * @return 在线用户详情列表
     */
    List<Map<String, Object>> getOnlineUsersDetail(int page, int size);

    /**
     * 获取用户活跃趋势（24小时）
     * 按小时统计活跃用户数量趋势
     * @return 24小时活跃趋势数据
     */
    List<Map<String, Object>> getUserActivityTrend24Hours();

    /**
     * 获取用户会话统计
     * 统计用户的会话时长和活跃度
     * @param days 统计天数
     * @param limit 返回记录数限制
     * @return 用户会话统计数据
     */
    List<Map<String, Object>> getUserSessionStats(int days, int limit);

    /**
     * 获取IP地址统计
     * 统计不同IP地址的用户活跃情况
     * @param hours 统计小时数
     * @param minUsers 最小用户数阈值
     * @param limit 返回记录数限制
     * @return IP地址统计数据
     */
    List<Map<String, Object>> getIpAddressStats(int hours, int minUsers, int limit);

    // ==================== 用户状态查询方法 ====================

    /**
     * 检查用户是否在线
     * @param userId 用户ID
     * @param minutes 在线判断时间阈值（分钟）
     * @return 是否在线
     */
    boolean isUserOnline(String userId, int minutes);

    /**
     * 获取用户最后活跃时间
     * @param userId 用户ID
     * @return 最后活跃时间
     */
    Date getUserLastActiveTime(String userId);

    /**
     * 获取用户在线时长（当前会话）
     * @param userId 用户ID
     * @return 在线时长（分钟）
     */
    long getUserOnlineDuration(String userId);

    // ==================== 性能监控方法 ====================

    /**
     * 获取数据库表性能统计
     * 用于监控表的大小和性能
     * @return 表性能统计数据
     */
    Map<String, Object> getTablePerformanceStats();

    /**
     * 获取统计查询性能指标
     * @return 性能指标数据
     */
    Map<String, Object> getStatsPerformanceMetrics();

    /**
     * 获取缓存命中率统计
     * @return 缓存统计数据
     */
    Map<String, Object> getCacheStats();

    // ==================== 数据清理方法 ====================

    /**
     * 清理过期数据（软删除）
     * 将长时间未活跃的记录标记为离线
     * @param days 过期天数
     * @return 清理的记录数
     */
    int cleanExpiredRecords(int days);

    /**
     * 物理删除过期数据
     * 删除超过指定天数的离线记录（谨慎使用）
     * @param days 过期天数
     * @return 删除的记录数
     */
    int physicalDeleteExpiredRecords(int days);

    // ==================== 实时统计方法 ====================

    /**
     * 获取实时统计概览
     * 包含当前在线用户、今日活跃用户、系统负载等关键指标
     * @return 实时统计概览
     */
    Map<String, Object> getRealTimeStatsOverview();

    /**
     * 获取统计数据刷新时间
     * @return 最后刷新时间
     */
    Date getStatsLastRefreshTime();

    /**
     * 强制刷新统计缓存
     * @return 是否刷新成功
     */
    boolean refreshStatsCache();

    // ==================== 导出和报告方法 ====================

    /**
     * 导出用户活跃统计报告
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 统计报告数据
     */
    Map<String, Object> exportActivityReport(Date startDate, Date endDate);

    /**
     * 获取周统计报告
     * @return 周统计数据
     */
    Map<String, Object> getWeeklyReport();

    /**
     * 获取月统计报告
     * @return 月统计数据
     */
    Map<String, Object> getMonthlyReport();
}
