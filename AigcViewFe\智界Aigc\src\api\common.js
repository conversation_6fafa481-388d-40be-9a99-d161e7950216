import { axios } from '@/utils/request'

/**
 * 通用 API 接口
 */

/**
 * 上传文件（通用）
 * @param {FormData} formData - 包含文件的FormData对象
 * @returns {Promise}
 */
export function uploadFile(formData) {
  return axios({
    url: '/sys/common/upload',
    method: 'POST',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    timeout: 60000 // 1分钟超时
  })
}

/**
 * 上传图片文件
 * @param {File} file - 图片文件
 * @returns {Promise}
 */
export function uploadImage(file) {
  const formData = new FormData()
  formData.append('file', file)
  
  return uploadFile(formData)
}

/**
 * 获取字典数据
 * @param {string} dictCode - 字典编码
 * @returns {Promise}
 */
export function getDictItems(dictCode) {
  return axios({
    url: '/sys/dict/getDictItems',
    method: 'GET',
    params: { dictCode }
  })
}

/**
 * 获取当前用户信息
 * @returns {Promise}
 */
export function getCurrentUser() {
  return axios({
    url: '/sys/user/getCurrentUser',
    method: 'GET'
  })
}

/**
 * 错误处理工具函数
 */

/**
 * 处理API错误
 * @param {Error} error - 错误对象
 * @param {string} defaultMessage - 默认错误消息
 * @returns {string} 错误消息
 */
export function handleApiError(error, defaultMessage = '操作失败') {
  if (error.response) {
    // 服务器返回错误状态码
    const { status, data } = error.response
    
    switch (status) {
      case 401:
        return '登录已过期，请重新登录'
      case 403:
        return '没有权限执行此操作'
      case 404:
        return '请求的资源不存在'
      case 422:
        return data.message || '请求参数错误'
      case 500:
        return '服务器内部错误'
      default:
        return data.message || defaultMessage
    }
  } else if (error.request) {
    // 网络错误
    return '网络连接失败，请检查网络设置'
  } else {
    // 其他错误
    return error.message || defaultMessage
  }
}

/**
 * 格式化文件大小
 * @param {number} bytes - 字节数
 * @returns {string} 格式化后的文件大小
 */
export function formatFileSize(bytes) {
  if (bytes === 0) return '0 B'
  
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

/**
 * 验证文件类型
 * @param {File} file - 文件对象
 * @param {Array} allowedTypes - 允许的文件类型数组
 * @returns {boolean} 是否为允许的文件类型
 */
export function validateFileType(file, allowedTypes) {
  if (!file || !allowedTypes || allowedTypes.length === 0) {
    return false
  }
  
  const fileName = file.name.toLowerCase()
  return allowedTypes.some(type => fileName.endsWith(type.toLowerCase()))
}

/**
 * 验证图片文件
 * @param {File} file - 文件对象
 * @param {number} maxSize - 最大文件大小（字节）
 * @returns {Object} 验证结果
 */
export function validateImageFile(file, maxSize = 2 * 1024 * 1024) {
  const result = {
    valid: true,
    message: ''
  }
  
  // 检查文件类型
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp']
  if (!allowedTypes.includes(file.type)) {
    result.valid = false
    result.message = '只支持 JPG、PNG、GIF、WebP 格式的图片文件'
    return result
  }
  
  // 检查文件大小
  if (file.size > maxSize) {
    result.valid = false
    result.message = `图片大小不能超过 ${formatFileSize(maxSize)}`
    return result
  }
  
  return result
}

/**
 * 验证压缩包文件
 * @param {File} file - 文件对象
 * @param {number} maxSize - 最大文件大小（字节）
 * @returns {Object} 验证结果
 */
export function validateArchiveFile(file, maxSize = 50 * 1024 * 1024) {
  const result = {
    valid: true,
    message: ''
  }
  
  // 检查文件扩展名
  const allowedExtensions = ['.zip', '.rar', '.7z']
  const fileName = file.name.toLowerCase()
  const isValidType = allowedExtensions.some(ext => fileName.endsWith(ext))
  
  if (!isValidType) {
    result.valid = false
    result.message = '只支持 ZIP、RAR、7Z 格式的压缩包文件'
    return result
  }
  
  // 检查文件大小
  if (file.size > maxSize) {
    result.valid = false
    result.message = `文件大小不能超过 ${formatFileSize(maxSize)}`
    return result
  }
  
  return result
}

/**
 * 防抖函数
 * @param {Function} func - 要防抖的函数
 * @param {number} wait - 等待时间（毫秒）
 * @returns {Function} 防抖后的函数
 */
export function debounce(func, wait) {
  let timeout
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout)
      func(...args)
    }
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
  }
}

/**
 * 节流函数
 * @param {Function} func - 要节流的函数
 * @param {number} limit - 限制时间（毫秒）
 * @returns {Function} 节流后的函数
 */
export function throttle(func, limit) {
  let inThrottle
  return function executedFunction(...args) {
    if (!inThrottle) {
      func.apply(this, args)
      inThrottle = true
      setTimeout(() => inThrottle = false, limit)
    }
  }
}

// 导出所有通用函数
export default {
  uploadFile,
  uploadImage,
  getDictItems,
  getCurrentUser,
  handleApiError,
  formatFileSize,
  validateFileType,
  validateImageFile,
  validateArchiveFile,
  debounce,
  throttle
}
