{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\workflow\\components\\CreatorCenter.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\workflow\\components\\CreatorCenter.vue", "mtime": 1754512989758}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["import _regeneratorRuntime from \"D:/AigcView_zj/AigcViewFe/\\u667A\\u754CAigc/node_modules/@babel/runtime/regenerator\";\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(Object(source), true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\nfunction asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }\n\nfunction _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"next\", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"throw\", err); } _next(undefined); }); }; }\n\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nimport RevenueStatsCards from './RevenueStatsCards.vue';\nimport RevenueRanking from './RevenueRanking.vue';\nimport AgentManagement from './AgentManagement.vue';\nimport CreatorAgentForm from './CreatorAgentForm.vue'; // 🔥 导入提现相关组件\n\nimport WithdrawModal from '@/components/withdraw/WithdrawModal.vue';\nimport WithdrawRecordsModal from '@/components/withdraw/WithdrawRecordsModal.vue';\nimport { getCreatorAgents, createAgent, updateAgent, deleteAgent, getRevenueStats } from '@/api/creator-agent';\nexport default {\n  name: 'CreatorCenter',\n  components: {\n    RevenueStatsCards: RevenueStatsCards,\n    RevenueRanking: RevenueRanking,\n    AgentManagement: AgentManagement,\n    CreatorAgentForm: CreatorAgentForm,\n    // 🔥 注册提现相关组件\n    WithdrawModal: WithdrawModal,\n    WithdrawRecordsModal: WithdrawRecordsModal\n  },\n  data: function data() {\n    return {\n      // 加载状态\n      creating: false,\n      statsLoading: false,\n      agentsLoading: false,\n      formLoading: false,\n      // 收益统计数据\n      revenueStats: {},\n      // 智能体列表数据\n      agentList: [],\n      pagination: {\n        current: 1,\n        pageSize: 12,\n        total: 0\n      },\n      // 筛选和搜索\n      searchQuery: '',\n      auditStatusFilter: '',\n      // 排序相关\n      sortField: 'totalRevenue',\n      sortOrder: 'desc',\n      // 表单相关\n      formVisible: false,\n      formMode: 'create',\n      // 'create' | 'edit'\n      currentAgent: null,\n      // 🔥 提现相关\n      showWithdrawModal: false,\n      showWithdrawRecordsModal: false,\n      withdrawLoading: false,\n      withdrawAvailableAmount: 0,\n      withdrawRevenueType: 'agent'\n    };\n  },\n  created: function () {\n    var _created = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee() {\n      return _regeneratorRuntime.wrap(function _callee$(_context) {\n        while (1) {\n          switch (_context.prev = _context.next) {\n            case 0:\n              _context.next = 2;\n              return this.initData();\n\n            case 2:\n            case \"end\":\n              return _context.stop();\n          }\n        }\n      }, _callee, this);\n    }));\n\n    function created() {\n      return _created.apply(this, arguments);\n    }\n\n    return created;\n  }(),\n  methods: {\n    // 初始化数据\n    initData: function () {\n      var _initData = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee2() {\n        return _regeneratorRuntime.wrap(function _callee2$(_context2) {\n          while (1) {\n            switch (_context2.prev = _context2.next) {\n              case 0:\n                _context2.next = 2;\n                return Promise.all([this.loadRevenueStats(), this.loadAgentList()]);\n\n              case 2:\n              case \"end\":\n                return _context2.stop();\n            }\n          }\n        }, _callee2, this);\n      }));\n\n      function initData() {\n        return _initData.apply(this, arguments);\n      }\n\n      return initData;\n    }(),\n    // 加载收益统计\n    loadRevenueStats: function () {\n      var _loadRevenueStats = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee3() {\n        var response;\n        return _regeneratorRuntime.wrap(function _callee3$(_context3) {\n          while (1) {\n            switch (_context3.prev = _context3.next) {\n              case 0:\n                this.statsLoading = true;\n                _context3.prev = 1;\n                _context3.next = 4;\n                return getRevenueStats();\n\n              case 4:\n                response = _context3.sent;\n\n                if (response.success) {\n                  this.revenueStats = response.result;\n                } else {\n                  this.$message.error(response.message || '获取收益统计失败');\n                }\n\n                _context3.next = 12;\n                break;\n\n              case 8:\n                _context3.prev = 8;\n                _context3.t0 = _context3[\"catch\"](1);\n                console.error('加载收益统计失败:', _context3.t0);\n                this.$message.error('获取收益统计失败');\n\n              case 12:\n                _context3.prev = 12;\n                this.statsLoading = false;\n                return _context3.finish(12);\n\n              case 15:\n              case \"end\":\n                return _context3.stop();\n            }\n          }\n        }, _callee3, this, [[1, 8, 12, 15]]);\n      }));\n\n      function loadRevenueStats() {\n        return _loadRevenueStats.apply(this, arguments);\n      }\n\n      return loadRevenueStats;\n    }(),\n    // 加载智能体列表\n    loadAgentList: function () {\n      var _loadAgentList = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee4() {\n        var resetPage,\n            params,\n            response,\n            _args4 = arguments;\n        return _regeneratorRuntime.wrap(function _callee4$(_context4) {\n          while (1) {\n            switch (_context4.prev = _context4.next) {\n              case 0:\n                resetPage = _args4.length > 0 && _args4[0] !== undefined ? _args4[0] : false;\n\n                if (resetPage) {\n                  this.pagination.current = 1;\n                }\n\n                this.agentsLoading = true;\n                _context4.prev = 3;\n                params = {\n                  pageNo: this.pagination.current,\n                  pageSize: this.pagination.pageSize,\n                  agentName: this.searchQuery || undefined,\n                  auditStatus: this.auditStatusFilter || undefined,\n                  sortField: this.sortField,\n                  sortOrder: this.sortOrder\n                };\n                _context4.next = 7;\n                return getCreatorAgents(params);\n\n              case 7:\n                response = _context4.sent;\n\n                if (response.success) {\n                  this.agentList = response.result.records || [];\n                  this.pagination.total = response.result.total || 0;\n                } else {\n                  this.$message.error(response.message || '获取智能体列表失败');\n                }\n\n                _context4.next = 15;\n                break;\n\n              case 11:\n                _context4.prev = 11;\n                _context4.t0 = _context4[\"catch\"](3);\n                console.error('加载智能体列表失败:', _context4.t0);\n                this.$message.error('获取智能体列表失败');\n\n              case 15:\n                _context4.prev = 15;\n                this.agentsLoading = false;\n                return _context4.finish(15);\n\n              case 18:\n              case \"end\":\n                return _context4.stop();\n            }\n          }\n        }, _callee4, this, [[3, 11, 15, 18]]);\n      }));\n\n      function loadAgentList() {\n        return _loadAgentList.apply(this, arguments);\n      }\n\n      return loadAgentList;\n    }(),\n    // 新增智能体\n    handleCreateAgent: function handleCreateAgent() {\n      this.formMode = 'create';\n      this.currentAgent = null;\n      this.formVisible = true;\n    },\n    // 编辑智能体\n    handleEditAgent: function handleEditAgent(agent) {\n      this.formMode = 'edit';\n      this.currentAgent = _objectSpread({}, agent);\n      this.formVisible = true;\n    },\n    // 删除智能体\n    handleDeleteAgent: function () {\n      var _handleDeleteAgent = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee6(agent) {\n        var self;\n        return _regeneratorRuntime.wrap(function _callee6$(_context6) {\n          while (1) {\n            switch (_context6.prev = _context6.next) {\n              case 0:\n                self = this;\n                this.$confirm({\n                  title: '确认删除',\n                  content: \"\\u786E\\u5B9A\\u8981\\u5220\\u9664\\u667A\\u80FD\\u4F53\\\"\".concat(agent.agentName, \"\\\"\\u5417\\uFF1F\\u6B64\\u64CD\\u4F5C\\u4E0D\\u53EF\\u6062\\u590D\\u3002\"),\n                  okText: '确定',\n                  cancelText: '取消',\n                  onOk: function () {\n                    var _onOk = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee5() {\n                      var response;\n                      return _regeneratorRuntime.wrap(function _callee5$(_context5) {\n                        while (1) {\n                          switch (_context5.prev = _context5.next) {\n                            case 0:\n                              _context5.prev = 0;\n                              _context5.next = 3;\n                              return deleteAgent(agent.id);\n\n                            case 3:\n                              response = _context5.sent;\n\n                              if (!response.success) {\n                                _context5.next = 12;\n                                break;\n                              }\n\n                              self.$message.success('删除成功');\n                              _context5.next = 8;\n                              return self.loadAgentList();\n\n                            case 8:\n                              _context5.next = 10;\n                              return self.loadRevenueStats();\n\n                            case 10:\n                              _context5.next = 13;\n                              break;\n\n                            case 12:\n                              self.$message.error(response.message || '删除失败');\n\n                            case 13:\n                              _context5.next = 19;\n                              break;\n\n                            case 15:\n                              _context5.prev = 15;\n                              _context5.t0 = _context5[\"catch\"](0);\n                              console.error('删除智能体失败:', _context5.t0);\n                              self.$message.error('删除失败');\n\n                            case 19:\n                            case \"end\":\n                              return _context5.stop();\n                          }\n                        }\n                      }, _callee5, null, [[0, 15]]);\n                    }));\n\n                    function onOk() {\n                      return _onOk.apply(this, arguments);\n                    }\n\n                    return onOk;\n                  }()\n                });\n\n              case 2:\n              case \"end\":\n                return _context6.stop();\n            }\n          }\n        }, _callee6, this);\n      }));\n\n      function handleDeleteAgent(_x) {\n        return _handleDeleteAgent.apply(this, arguments);\n      }\n\n      return handleDeleteAgent;\n    }(),\n    // 智能体搜索\n    handleAgentSearch: function handleAgentSearch(searchQuery) {\n      this.searchQuery = searchQuery;\n      this.loadAgentList(true);\n    },\n    // 智能体筛选\n    handleAgentFilter: function handleAgentFilter(filters) {\n      this.auditStatusFilter = filters.auditStatus;\n      this.loadAgentList(true);\n    },\n    // 排序变化\n    handleSortChange: function handleSortChange(sortParams) {\n      this.sortField = sortParams.sortField;\n      this.sortOrder = sortParams.sortOrder;\n      this.loadAgentList(true);\n    },\n    // 分页变化\n    handlePageChange: function handlePageChange(page, pageSize) {\n      this.pagination.current = page;\n      this.pagination.pageSize = pageSize;\n      this.loadAgentList();\n    },\n    // 关闭表单\n    handleCloseForm: function handleCloseForm() {\n      this.formVisible = false;\n      this.currentAgent = null;\n    },\n    // 提交表单\n    handleSubmitForm: function () {\n      var _handleSubmitForm = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee7(formData) {\n        var response;\n        return _regeneratorRuntime.wrap(function _callee7$(_context7) {\n          while (1) {\n            switch (_context7.prev = _context7.next) {\n              case 0:\n                this.formLoading = true;\n                _context7.prev = 1;\n\n                if (!(this.formMode === 'create')) {\n                  _context7.next = 8;\n                  break;\n                }\n\n                _context7.next = 5;\n                return createAgent(formData);\n\n              case 5:\n                response = _context7.sent;\n                _context7.next = 11;\n                break;\n\n              case 8:\n                _context7.next = 10;\n                return updateAgent(this.currentAgent.id, formData);\n\n              case 10:\n                response = _context7.sent;\n\n              case 11:\n                if (!response.success) {\n                  _context7.next = 21;\n                  break;\n                }\n\n                // 🔥 成功后确认删除被替换的原始头像文件（与后台管理系统逻辑一致）\n                if (this.$refs && this.$refs.agentForm && this.$refs.agentForm.confirmDeleteOriginalFiles) {\n                  this.$refs.agentForm.confirmDeleteOriginalFiles();\n                }\n\n                this.$message.success(this.formMode === 'create' ? '创建成功' : '更新成功');\n                this.handleCloseForm();\n                _context7.next = 17;\n                return this.loadAgentList();\n\n              case 17:\n                _context7.next = 19;\n                return this.loadRevenueStats();\n\n              case 19:\n                _context7.next = 23;\n                break;\n\n              case 21:\n                console.error('🎯 CreatorCenter: 智能体操作失败:', response.message);\n                this.$message.error(response.message || '操作失败');\n\n              case 23:\n                _context7.next = 29;\n                break;\n\n              case 25:\n                _context7.prev = 25;\n                _context7.t0 = _context7[\"catch\"](1);\n                console.error('🎯 CreatorCenter: 提交表单失败:', _context7.t0);\n                this.$message.error('操作失败: ' + (_context7.t0.message || '未知错误'));\n\n              case 29:\n                _context7.prev = 29;\n                this.formLoading = false;\n                return _context7.finish(29);\n\n              case 32:\n              case \"end\":\n                return _context7.stop();\n            }\n          }\n        }, _callee7, this, [[1, 25, 29, 32]]);\n      }));\n\n      function handleSubmitForm(_x2) {\n        return _handleSubmitForm.apply(this, arguments);\n      }\n\n      return handleSubmitForm;\n    }(),\n    // 刷新所有数据（供父组件调用）\n    refreshAllData: function () {\n      var _refreshAllData = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee8() {\n        return _regeneratorRuntime.wrap(function _callee8$(_context8) {\n          while (1) {\n            switch (_context8.prev = _context8.next) {\n              case 0:\n                _context8.prev = 0;\n                _context8.next = 3;\n                return Promise.all([this.loadRevenueStats(), this.loadAgentList()]);\n\n              case 3:\n                _context8.next = 8;\n                break;\n\n              case 5:\n                _context8.prev = 5;\n                _context8.t0 = _context8[\"catch\"](0);\n                console.error('❌ 创作者中心数据刷新失败:', _context8.t0);\n\n              case 8:\n              case \"end\":\n                return _context8.stop();\n            }\n          }\n        }, _callee8, this, [[0, 5]]);\n      }));\n\n      function refreshAllData() {\n        return _refreshAllData.apply(this, arguments);\n      }\n\n      return refreshAllData;\n    }(),\n    // 🔥 智能体创建完成\n    handleAgentComplete: function handleAgentComplete(agent) {\n      this.handleCloseForm();\n      this.loadAgentList();\n      this.loadRevenueStats();\n    },\n    // 🔥 从表单中删除工作流\n    handleDeleteWorkflowFromForm: function () {\n      var _handleDeleteWorkflowFromForm = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee9(workflow) {\n        return _regeneratorRuntime.wrap(function _callee9$(_context9) {\n          while (1) {\n            switch (_context9.prev = _context9.next) {\n              case 0:\n                console.log('🎯 CreatorCenter: 从表单中删除工作流', workflow);\n\n                try {\n                  // 这里调用删除工作流的API\n                  // await deleteWorkflow(workflow.id)\n                  this.$message.success('工作流删除成功'); // 刷新表单中的工作流列表\n\n                  if (this.$refs.agentForm && this.$refs.agentForm.loadWorkflowList) {\n                    this.$refs.agentForm.loadWorkflowList(workflow.agentId);\n                  }\n                } catch (error) {\n                  console.error('🎯 CreatorCenter: 删除工作流失败:', error);\n                  this.$message.error('删除工作流失败');\n                }\n\n              case 2:\n              case \"end\":\n                return _context9.stop();\n            }\n          }\n        }, _callee9, this);\n      }));\n\n      function handleDeleteWorkflowFromForm(_x3) {\n        return _handleDeleteWorkflowFromForm.apply(this, arguments);\n      }\n\n      return handleDeleteWorkflowFromForm;\n    }(),\n    // 🔥 ==================== 提现相关方法 ====================\n    // 处理提现\n    handleWithdraw: function handleWithdraw(params) {\n      this.withdrawAvailableAmount = params.availableAmount;\n      this.withdrawRevenueType = params.revenueType;\n      this.showWithdrawModal = true;\n    },\n    // 打开提现记录弹窗\n    openWithdrawRecordsModal: function openWithdrawRecordsModal() {\n      this.showWithdrawRecordsModal = true;\n    },\n    // 提现申请提交\n    handleWithdrawSubmit: function () {\n      var _handleWithdrawSubmit = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee10(params) {\n        var response;\n        return _regeneratorRuntime.wrap(function _callee10$(_context10) {\n          while (1) {\n            switch (_context10.prev = _context10.next) {\n              case 0:\n                this.withdrawLoading = true;\n                _context10.prev = 1;\n                _context10.next = 4;\n                return this.$http.post('/api/usercenter/applyWithdrawal', params);\n\n              case 4:\n                response = _context10.sent;\n\n                if (!response.success) {\n                  _context10.next = 12;\n                  break;\n                }\n\n                this.$notification.success({\n                  message: '提现申请成功',\n                  description: '您的提现申请已提交，预计1-3个工作日到账'\n                });\n                this.showWithdrawModal = false; // 刷新收益数据\n\n                _context10.next = 10;\n                return this.loadRevenueStats();\n\n              case 10:\n                _context10.next = 13;\n                break;\n\n              case 12:\n                this.$notification.error({\n                  message: '提现申请失败',\n                  description: response.message || '申请失败，请重试'\n                });\n\n              case 13:\n                _context10.next = 19;\n                break;\n\n              case 15:\n                _context10.prev = 15;\n                _context10.t0 = _context10[\"catch\"](1);\n                console.error('🔥 CreatorCenter: 提现申请失败:', _context10.t0);\n                this.$notification.error({\n                  message: '提现申请失败',\n                  description: _context10.t0.message || '申请失败，请重试'\n                });\n\n              case 19:\n                _context10.prev = 19;\n                this.withdrawLoading = false;\n                return _context10.finish(19);\n\n              case 22:\n              case \"end\":\n                return _context10.stop();\n            }\n          }\n        }, _callee10, this, [[1, 15, 19, 22]]);\n      }));\n\n      function handleWithdrawSubmit(_x4) {\n        return _handleWithdrawSubmit.apply(this, arguments);\n      }\n\n      return handleWithdrawSubmit;\n    }(),\n    // 取消提现\n    handleWithdrawCancel: function handleWithdrawCancel() {\n      this.showWithdrawModal = false;\n    },\n    // 关闭提现记录弹窗\n    handleWithdrawRecordsCancel: function handleWithdrawRecordsCancel() {\n      this.showWithdrawRecordsModal = false;\n    }\n  }\n};", {"version": 3, "sources": ["CreatorCenter.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiHA,OAAA,iBAAA,MAAA,yBAAA;AACA,OAAA,cAAA,MAAA,sBAAA;AACA,OAAA,eAAA,MAAA,uBAAA;AACA,OAAA,gBAAA,MAAA,wBAAA,C,CACA;;AACA,OAAA,aAAA,MAAA,yCAAA;AACA,OAAA,oBAAA,MAAA,gDAAA;AACA,SAAA,gBAAA,EAAA,WAAA,EAAA,WAAA,EAAA,WAAA,EAAA,eAAA,QAAA,qBAAA;AAEA,eAAA;AACA,EAAA,IAAA,EAAA,eADA;AAEA,EAAA,UAAA,EAAA;AACA,IAAA,iBAAA,EAAA,iBADA;AAEA,IAAA,cAAA,EAAA,cAFA;AAGA,IAAA,eAAA,EAAA,eAHA;AAIA,IAAA,gBAAA,EAAA,gBAJA;AAKA;AACA,IAAA,aAAA,EAAA,aANA;AAOA,IAAA,oBAAA,EAAA;AAPA,GAFA;AAWA,EAAA,IAXA,kBAWA;AACA,WAAA;AACA;AACA,MAAA,QAAA,EAAA,KAFA;AAGA,MAAA,YAAA,EAAA,KAHA;AAIA,MAAA,aAAA,EAAA,KAJA;AAKA,MAAA,WAAA,EAAA,KALA;AAOA;AACA,MAAA,YAAA,EAAA,EARA;AAUA;AACA,MAAA,SAAA,EAAA,EAXA;AAYA,MAAA,UAAA,EAAA;AACA,QAAA,OAAA,EAAA,CADA;AAEA,QAAA,QAAA,EAAA,EAFA;AAGA,QAAA,KAAA,EAAA;AAHA,OAZA;AAkBA;AACA,MAAA,WAAA,EAAA,EAnBA;AAoBA,MAAA,iBAAA,EAAA,EApBA;AAsBA;AACA,MAAA,SAAA,EAAA,cAvBA;AAwBA,MAAA,SAAA,EAAA,MAxBA;AA0BA;AACA,MAAA,WAAA,EAAA,KA3BA;AA4BA,MAAA,QAAA,EAAA,QA5BA;AA4BA;AACA,MAAA,YAAA,EAAA,IA7BA;AAiCA;AACA,MAAA,iBAAA,EAAA,KAlCA;AAmCA,MAAA,wBAAA,EAAA,KAnCA;AAoCA,MAAA,eAAA,EAAA,KApCA;AAqCA,MAAA,uBAAA,EAAA,CArCA;AAsCA,MAAA,mBAAA,EAAA;AAtCA,KAAA;AAwCA,GApDA;AAsDA,EAAA,OAtDA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,qBAuDA,KAAA,QAAA,EAvDA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AA0DA,EAAA,OAAA,EAAA;AACA;AACA,IAAA,QAFA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uBAGA,OAAA,CAAA,GAAA,CAAA,CACA,KAAA,gBAAA,EADA,EAEA,KAAA,aAAA,EAFA,CAAA,CAHA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AASA;AACA,IAAA,gBAVA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA,qBAAA,YAAA,GAAA,IAAA;AAXA;AAAA;AAAA,uBAaA,eAAA,EAbA;;AAAA;AAaA,gBAAA,QAbA;;AAcA,oBAAA,QAAA,CAAA,OAAA,EAAA;AACA,uBAAA,YAAA,GAAA,QAAA,CAAA,MAAA;AACA,iBAFA,MAEA;AACA,uBAAA,QAAA,CAAA,KAAA,CAAA,QAAA,CAAA,OAAA,IAAA,UAAA;AACA;;AAlBA;AAAA;;AAAA;AAAA;AAAA;AAoBA,gBAAA,OAAA,CAAA,KAAA,CAAA,WAAA;AACA,qBAAA,QAAA,CAAA,KAAA,CAAA,UAAA;;AArBA;AAAA;AAuBA,qBAAA,YAAA,GAAA,KAAA;AAvBA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AA2BA;AACA,IAAA,aA5BA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA4BA,gBAAA,SA5BA,8DA4BA,KA5BA;;AA6BA,oBAAA,SAAA,EAAA;AACA,uBAAA,UAAA,CAAA,OAAA,GAAA,CAAA;AACA;;AAEA,qBAAA,aAAA,GAAA,IAAA;AAjCA;AAmCA,gBAAA,MAnCA,GAmCA;AACA,kBAAA,MAAA,EAAA,KAAA,UAAA,CAAA,OADA;AAEA,kBAAA,QAAA,EAAA,KAAA,UAAA,CAAA,QAFA;AAGA,kBAAA,SAAA,EAAA,KAAA,WAAA,IAAA,SAHA;AAIA,kBAAA,WAAA,EAAA,KAAA,iBAAA,IAAA,SAJA;AAKA,kBAAA,SAAA,EAAA,KAAA,SALA;AAMA,kBAAA,SAAA,EAAA,KAAA;AANA,iBAnCA;AAAA;AAAA,uBA4CA,gBAAA,CAAA,MAAA,CA5CA;;AAAA;AA4CA,gBAAA,QA5CA;;AA6CA,oBAAA,QAAA,CAAA,OAAA,EAAA;AACA,uBAAA,SAAA,GAAA,QAAA,CAAA,MAAA,CAAA,OAAA,IAAA,EAAA;AACA,uBAAA,UAAA,CAAA,KAAA,GAAA,QAAA,CAAA,MAAA,CAAA,KAAA,IAAA,CAAA;AACA,iBAHA,MAGA;AACA,uBAAA,QAAA,CAAA,KAAA,CAAA,QAAA,CAAA,OAAA,IAAA,WAAA;AACA;;AAlDA;AAAA;;AAAA;AAAA;AAAA;AAoDA,gBAAA,OAAA,CAAA,KAAA,CAAA,YAAA;AACA,qBAAA,QAAA,CAAA,KAAA,CAAA,WAAA;;AArDA;AAAA;AAuDA,qBAAA,aAAA,GAAA,KAAA;AAvDA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AA2DA;AACA,IAAA,iBA5DA,+BA4DA;AACA,WAAA,QAAA,GAAA,QAAA;AACA,WAAA,YAAA,GAAA,IAAA;AACA,WAAA,WAAA,GAAA,IAAA;AACA,KAhEA;AAkEA;AACA,IAAA,eAnEA,2BAmEA,KAnEA,EAmEA;AACA,WAAA,QAAA,GAAA,MAAA;AACA,WAAA,YAAA,qBAAA,KAAA;AACA,WAAA,WAAA,GAAA,IAAA;AACA,KAvEA;AAyEA;AACA,IAAA,iBA1EA;AAAA,0GA0EA,KA1EA;AAAA;AAAA;AAAA;AAAA;AAAA;AA2EA,gBAAA,IA3EA,GA2EA,IA3EA;AA4EA,qBAAA,QAAA,CAAA;AACA,kBAAA,KAAA,EAAA,MADA;AAEA,kBAAA,OAAA,8DAAA,KAAA,CAAA,SAAA,mEAFA;AAGA,kBAAA,MAAA,EAAA,IAHA;AAIA,kBAAA,UAAA,EAAA,IAJA;AAKA,kBAAA,IAAA;AAAA,yFAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,qCAEA,WAAA,CAAA,KAAA,CAAA,EAAA,CAFA;;AAAA;AAEA,8BAAA,QAFA;;AAAA,mCAGA,QAAA,CAAA,OAHA;AAAA;AAAA;AAAA;;AAIA,8BAAA,IAAA,CAAA,QAAA,CAAA,OAAA,CAAA,MAAA;AAJA;AAAA,qCAKA,IAAA,CAAA,aAAA,EALA;;AAAA;AAAA;AAAA,qCAMA,IAAA,CAAA,gBAAA,EANA;;AAAA;AAAA;AAAA;;AAAA;AAQA,8BAAA,IAAA,CAAA,QAAA,CAAA,KAAA,CAAA,QAAA,CAAA,OAAA,IAAA,MAAA;;AARA;AAAA;AAAA;;AAAA;AAAA;AAAA;AAWA,8BAAA,OAAA,CAAA,KAAA,CAAA,UAAA;AACA,8BAAA,IAAA,CAAA,QAAA,CAAA,KAAA,CAAA,MAAA;;AAZA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,qBAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AALA,iBAAA;;AA5EA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAqGA;AACA,IAAA,iBAtGA,6BAsGA,WAtGA,EAsGA;AACA,WAAA,WAAA,GAAA,WAAA;AACA,WAAA,aAAA,CAAA,IAAA;AACA,KAzGA;AA2GA;AACA,IAAA,iBA5GA,6BA4GA,OA5GA,EA4GA;AACA,WAAA,iBAAA,GAAA,OAAA,CAAA,WAAA;AACA,WAAA,aAAA,CAAA,IAAA;AACA,KA/GA;AAiHA;AACA,IAAA,gBAlHA,4BAkHA,UAlHA,EAkHA;AACA,WAAA,SAAA,GAAA,UAAA,CAAA,SAAA;AACA,WAAA,SAAA,GAAA,UAAA,CAAA,SAAA;AACA,WAAA,aAAA,CAAA,IAAA;AACA,KAtHA;AAwHA;AACA,IAAA,gBAzHA,4BAyHA,IAzHA,EAyHA,QAzHA,EAyHA;AACA,WAAA,UAAA,CAAA,OAAA,GAAA,IAAA;AACA,WAAA,UAAA,CAAA,QAAA,GAAA,QAAA;AACA,WAAA,aAAA;AACA,KA7HA;AA+HA;AACA,IAAA,eAhIA,6BAgIA;AACA,WAAA,WAAA,GAAA,KAAA;AACA,WAAA,YAAA,GAAA,IAAA;AACA,KAnIA;AAqIA;AACA,IAAA,gBAtIA;AAAA,yGAsIA,QAtIA;AAAA;AAAA;AAAA;AAAA;AAAA;AAuIA,qBAAA,WAAA,GAAA,IAAA;AAvIA;;AAAA,sBA2IA,KAAA,QAAA,KAAA,QA3IA;AAAA;AAAA;AAAA;;AAAA;AAAA,uBA4IA,WAAA,CAAA,QAAA,CA5IA;;AAAA;AA4IA,gBAAA,QA5IA;AAAA;AAAA;;AAAA;AAAA;AAAA,uBA8IA,WAAA,CAAA,KAAA,YAAA,CAAA,EAAA,EAAA,QAAA,CA9IA;;AAAA;AA8IA,gBAAA,QA9IA;;AAAA;AAAA,qBAiJA,QAAA,CAAA,OAjJA;AAAA;AAAA;AAAA;;AAmJA;AACA,oBAAA,KAAA,KAAA,IAAA,KAAA,KAAA,CAAA,SAAA,IAAA,KAAA,KAAA,CAAA,SAAA,CAAA,0BAAA,EAAA;AACA,uBAAA,KAAA,CAAA,SAAA,CAAA,0BAAA;AACA;;AAEA,qBAAA,QAAA,CAAA,OAAA,CAAA,KAAA,QAAA,KAAA,QAAA,GAAA,MAAA,GAAA,MAAA;AACA,qBAAA,eAAA;AAzJA;AAAA,uBA0JA,KAAA,aAAA,EA1JA;;AAAA;AAAA;AAAA,uBA2JA,KAAA,gBAAA,EA3JA;;AAAA;AAAA;AAAA;;AAAA;AA6JA,gBAAA,OAAA,CAAA,KAAA,CAAA,4BAAA,EAAA,QAAA,CAAA,OAAA;AACA,qBAAA,QAAA,CAAA,KAAA,CAAA,QAAA,CAAA,OAAA,IAAA,MAAA;;AA9JA;AAAA;AAAA;;AAAA;AAAA;AAAA;AAiKA,gBAAA,OAAA,CAAA,KAAA,CAAA,2BAAA;AACA,qBAAA,QAAA,CAAA,KAAA,CAAA,YAAA,aAAA,OAAA,IAAA,MAAA,CAAA;;AAlKA;AAAA;AAoKA,qBAAA,WAAA,GAAA,KAAA;AApKA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AA0KA;AACA,IAAA,cA3KA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uBA6KA,OAAA,CAAA,GAAA,CAAA,CACA,KAAA,gBAAA,EADA,EAEA,KAAA,aAAA,EAFA,CAAA,CA7KA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;AAkLA,gBAAA,OAAA,CAAA,KAAA,CAAA,gBAAA;;AAlLA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAsLA;AACA,IAAA,mBAvLA,+BAuLA,KAvLA,EAuLA;AACA,WAAA,eAAA;AACA,WAAA,aAAA;AACA,WAAA,gBAAA;AACA,KA3LA;AA+LA;AACA,IAAA,4BAhMA;AAAA,qHAgMA,QAhMA;AAAA;AAAA;AAAA;AAAA;AAiMA,gBAAA,OAAA,CAAA,GAAA,CAAA,6BAAA,EAAA,QAAA;;AACA,oBAAA;AACA;AACA;AACA,uBAAA,QAAA,CAAA,OAAA,CAAA,SAAA,EAHA,CAKA;;AACA,sBAAA,KAAA,KAAA,CAAA,SAAA,IAAA,KAAA,KAAA,CAAA,SAAA,CAAA,gBAAA,EAAA;AACA,yBAAA,KAAA,CAAA,SAAA,CAAA,gBAAA,CAAA,QAAA,CAAA,OAAA;AACA;AACA,iBATA,CASA,OAAA,KAAA,EAAA;AACA,kBAAA,OAAA,CAAA,KAAA,CAAA,4BAAA,EAAA,KAAA;AACA,uBAAA,QAAA,CAAA,KAAA,CAAA,SAAA;AACA;;AA9MA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAiNA;AAEA;AACA,IAAA,cApNA,0BAoNA,MApNA,EAoNA;AACA,WAAA,uBAAA,GAAA,MAAA,CAAA,eAAA;AACA,WAAA,mBAAA,GAAA,MAAA,CAAA,WAAA;AACA,WAAA,iBAAA,GAAA,IAAA;AACA,KAxNA;AA0NA;AACA,IAAA,wBA3NA,sCA2NA;AACA,WAAA,wBAAA,GAAA,IAAA;AACA,KA7NA;AA+NA;AACA,IAAA,oBAhOA;AAAA,8GAgOA,MAhOA;AAAA;AAAA;AAAA;AAAA;AAAA;AAkOA,qBAAA,eAAA,GAAA,IAAA;AAlOA;AAAA;AAAA,uBAqOA,KAAA,KAAA,CAAA,IAAA,CAAA,iCAAA,EAAA,MAAA,CArOA;;AAAA;AAqOA,gBAAA,QArOA;;AAAA,qBAuOA,QAAA,CAAA,OAvOA;AAAA;AAAA;AAAA;;AAwOA,qBAAA,aAAA,CAAA,OAAA,CAAA;AACA,kBAAA,OAAA,EAAA,QADA;AAEA,kBAAA,WAAA,EAAA;AAFA,iBAAA;AAKA,qBAAA,iBAAA,GAAA,KAAA,CA7OA,CA+OA;;AA/OA;AAAA,uBAgPA,KAAA,gBAAA,EAhPA;;AAAA;AAAA;AAAA;;AAAA;AAkPA,qBAAA,aAAA,CAAA,KAAA,CAAA;AACA,kBAAA,OAAA,EAAA,QADA;AAEA,kBAAA,WAAA,EAAA,QAAA,CAAA,OAAA,IAAA;AAFA,iBAAA;;AAlPA;AAAA;AAAA;;AAAA;AAAA;AAAA;AAwPA,gBAAA,OAAA,CAAA,KAAA,CAAA,2BAAA;AACA,qBAAA,aAAA,CAAA,KAAA,CAAA;AACA,kBAAA,OAAA,EAAA,QADA;AAEA,kBAAA,WAAA,EAAA,cAAA,OAAA,IAAA;AAFA,iBAAA;;AAzPA;AAAA;AA8PA,qBAAA,eAAA,GAAA,KAAA;AA9PA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;AAkQA;AACA,IAAA,oBAnQA,kCAmQA;AACA,WAAA,iBAAA,GAAA,KAAA;AACA,KArQA;AAuQA;AACA,IAAA,2BAxQA,yCAwQA;AACA,WAAA,wBAAA,GAAA,KAAA;AACA;AA1QA;AA1DA,CAAA", "sourcesContent": ["<template>\n  <div class=\"creator-center\">\n    <!-- 页面头部 -->\n    <div class=\"creator-header\">\n      <div class=\"header-content\">\n        <div class=\"header-left\">\n          <h1 class=\"page-title\">\n            <a-icon type=\"user\" />\n            创作者中心\n          </h1>\n          <p class=\"page-subtitle\">管理您的智能体，查看收益统计（全部收益归创作者，但VIP和SVIP用户分别是价格的7折和5折，如您选择发布默认同意此方案）</p>\n        </div>\n        <div class=\"header-right\">\n          <a-button type=\"primary\" size=\"large\" @click=\"handleCreateAgent\" :loading=\"creating\">\n            <a-icon type=\"plus\" />\n            新增智能体\n          </a-button>\n        </div>\n      </div>\n    </div>\n\n    <!-- 收益统计卡片 -->\n    <div class=\"stats-cards-section\">\n      <div class=\"section-header\">\n        <h2 class=\"section-title\">收益统计</h2>\n        <div class=\"section-actions\">\n          <a-button\n            type=\"default\"\n            @click=\"openWithdrawRecordsModal\"\n            class=\"records-btn\"\n          >\n            <a-icon type=\"history\" />\n            查看提现记录\n          </a-button>\n        </div>\n      </div>\n\n      <RevenueStatsCards\n        :loading=\"statsLoading\"\n        :data=\"revenueStats\"\n        @refresh=\"loadRevenueStats\"\n        @withdraw=\"handleWithdraw\"\n      />\n    </div>\n\n    <!-- 智能体管理区域 -->\n    <div class=\"agents-section\">\n      <div class=\"section-header\">\n        <h2 class=\"section-title\">我的智能体</h2>\n\n      </div>\n\n      <!-- 智能体列表 -->\n      <AgentManagement\n        :loading=\"agentsLoading\"\n        :agents=\"agentList\"\n        :pagination=\"pagination\"\n        @refresh=\"loadAgentList\"\n        @edit=\"handleEditAgent\"\n        @delete=\"handleDeleteAgent\"\n\n        @page-change=\"handlePageChange\"\n        @search=\"handleAgentSearch\"\n        @filter-change=\"handleAgentFilter\"\n        @sort-change=\"handleSortChange\"\n      />\n    </div>\n\n    <!-- 智能体收益排行 -->\n    <div class=\"ranking-section\">\n      <RevenueRanking\n        :loading=\"statsLoading\"\n        :data=\"revenueStats\"\n      />\n    </div>\n\n    <!-- 智能体表单弹窗 -->\n    <CreatorAgentForm\n      ref=\"agentForm\"\n      :visible=\"formVisible\"\n      :loading=\"formLoading\"\n      :agent=\"currentAgent\"\n      :mode=\"formMode\"\n      @close=\"handleCloseForm\"\n      @submit=\"handleSubmitForm\"\n      @complete=\"handleAgentComplete\"\n\n      @delete-workflow=\"handleDeleteWorkflowFromForm\"\n    />\n\n\n\n    <!-- 🔥 提现弹窗 -->\n    <WithdrawModal\n      :visible=\"showWithdrawModal\"\n      :available-amount=\"withdrawAvailableAmount\"\n      :revenue-type=\"withdrawRevenueType\"\n      :loading=\"withdrawLoading\"\n      @submit=\"handleWithdrawSubmit\"\n      @cancel=\"handleWithdrawCancel\"\n    />\n\n    <!-- 🔥 提现记录弹窗 -->\n    <WithdrawRecordsModal\n      :visible=\"showWithdrawRecordsModal\"\n      :revenue-type=\"'agent'\"\n      @cancel=\"handleWithdrawRecordsCancel\"\n      @refresh=\"loadRevenueStats\"\n    />\n  </div>\n</template>\n\n<script>\nimport RevenueStatsCards from './RevenueStatsCards.vue'\nimport RevenueRanking from './RevenueRanking.vue'\nimport AgentManagement from './AgentManagement.vue'\nimport CreatorAgentForm from './CreatorAgentForm.vue'\n// 🔥 导入提现相关组件\nimport WithdrawModal from '@/components/withdraw/WithdrawModal.vue'\nimport WithdrawRecordsModal from '@/components/withdraw/WithdrawRecordsModal.vue'\nimport { getCreatorAgents, createAgent, updateAgent, deleteAgent, getRevenueStats } from '@/api/creator-agent'\n\nexport default {\n  name: 'CreatorCenter',\n  components: {\n    RevenueStatsCards,\n    RevenueRanking,\n    AgentManagement,\n    CreatorAgentForm,\n    // 🔥 注册提现相关组件\n    WithdrawModal,\n    WithdrawRecordsModal\n  },\n  data() {\n    return {\n      // 加载状态\n      creating: false,\n      statsLoading: false,\n      agentsLoading: false,\n      formLoading: false,\n      \n      // 收益统计数据\n      revenueStats: {},\n      \n      // 智能体列表数据\n      agentList: [],\n      pagination: {\n        current: 1,\n        pageSize: 12,\n        total: 0\n      },\n      \n      // 筛选和搜索\n      searchQuery: '',\n      auditStatusFilter: '',\n\n      // 排序相关\n      sortField: 'totalRevenue',\n      sortOrder: 'desc',\n      \n      // 表单相关\n      formVisible: false,\n      formMode: 'create', // 'create' | 'edit'\n      currentAgent: null,\n      \n\n\n      // 🔥 提现相关\n      showWithdrawModal: false,\n      showWithdrawRecordsModal: false,\n      withdrawLoading: false,\n      withdrawAvailableAmount: 0,\n      withdrawRevenueType: 'agent'\n    }\n  },\n  \n  async created() {\n    await this.initData()\n  },\n  \n  methods: {\n    // 初始化数据\n    async initData() {\n      await Promise.all([\n        this.loadRevenueStats(),\n        this.loadAgentList()\n      ])\n    },\n    \n    // 加载收益统计\n    async loadRevenueStats() {\n      this.statsLoading = true\n      try {\n        const response = await getRevenueStats()\n        if (response.success) {\n          this.revenueStats = response.result\n        } else {\n          this.$message.error(response.message || '获取收益统计失败')\n        }\n      } catch (error) {\n        console.error('加载收益统计失败:', error)\n        this.$message.error('获取收益统计失败')\n      } finally {\n        this.statsLoading = false\n      }\n    },\n    \n    // 加载智能体列表\n    async loadAgentList(resetPage = false) {\n      if (resetPage) {\n        this.pagination.current = 1\n      }\n      \n      this.agentsLoading = true\n      try {\n        const params = {\n          pageNo: this.pagination.current,\n          pageSize: this.pagination.pageSize,\n          agentName: this.searchQuery || undefined,\n          auditStatus: this.auditStatusFilter || undefined,\n          sortField: this.sortField,\n          sortOrder: this.sortOrder\n        }\n        \n        const response = await getCreatorAgents(params)\n        if (response.success) {\n          this.agentList = response.result.records || []\n          this.pagination.total = response.result.total || 0\n        } else {\n          this.$message.error(response.message || '获取智能体列表失败')\n        }\n      } catch (error) {\n        console.error('加载智能体列表失败:', error)\n        this.$message.error('获取智能体列表失败')\n      } finally {\n        this.agentsLoading = false\n      }\n    },\n    \n    // 新增智能体\n    handleCreateAgent() {\n      this.formMode = 'create'\n      this.currentAgent = null\n      this.formVisible = true\n    },\n    \n    // 编辑智能体\n    handleEditAgent(agent) {\n      this.formMode = 'edit'\n      this.currentAgent = { ...agent }\n      this.formVisible = true\n    },\n    \n    // 删除智能体\n    async handleDeleteAgent(agent) {\n      const self = this\n      this.$confirm({\n        title: '确认删除',\n        content: `确定要删除智能体\"${agent.agentName}\"吗？此操作不可恢复。`,\n        okText: '确定',\n        cancelText: '取消',\n        onOk: async () => {\n          try {\n            const response = await deleteAgent(agent.id)\n            if (response.success) {\n              self.$message.success('删除成功')\n              await self.loadAgentList()\n              await self.loadRevenueStats() // 刷新统计数据\n            } else {\n              self.$message.error(response.message || '删除失败')\n            }\n          } catch (error) {\n            console.error('删除智能体失败:', error)\n            self.$message.error('删除失败')\n          }\n        }\n      })\n    },\n    \n\n    \n    // 智能体搜索\n    handleAgentSearch(searchQuery) {\n      this.searchQuery = searchQuery\n      this.loadAgentList(true)\n    },\n\n    // 智能体筛选\n    handleAgentFilter(filters) {\n      this.auditStatusFilter = filters.auditStatus\n      this.loadAgentList(true)\n    },\n\n    // 排序变化\n    handleSortChange(sortParams) {\n      this.sortField = sortParams.sortField\n      this.sortOrder = sortParams.sortOrder\n      this.loadAgentList(true)\n    },\n\n    // 分页变化\n    handlePageChange(page, pageSize) {\n      this.pagination.current = page\n      this.pagination.pageSize = pageSize\n      this.loadAgentList()\n    },\n    \n    // 关闭表单\n    handleCloseForm() {\n      this.formVisible = false\n      this.currentAgent = null\n    },\n    \n    // 提交表单\n    async handleSubmitForm(formData) {\n      this.formLoading = true\n\n      try {\n        let response\n        if (this.formMode === 'create') {\n          response = await createAgent(formData)\n        } else {\n          response = await updateAgent(this.currentAgent.id, formData)\n        }\n\n        if (response.success) {\n\n          // 🔥 成功后确认删除被替换的原始头像文件（与后台管理系统逻辑一致）\n          if (this.$refs && this.$refs.agentForm && this.$refs.agentForm.confirmDeleteOriginalFiles) {\n            this.$refs.agentForm.confirmDeleteOriginalFiles()\n          }\n\n          this.$message.success(this.formMode === 'create' ? '创建成功' : '更新成功')\n          this.handleCloseForm()\n          await this.loadAgentList()\n          await this.loadRevenueStats() // 刷新统计数据\n        } else {\n          console.error('🎯 CreatorCenter: 智能体操作失败:', response.message)\n          this.$message.error(response.message || '操作失败')\n        }\n      } catch (error) {\n        console.error('🎯 CreatorCenter: 提交表单失败:', error)\n        this.$message.error('操作失败: ' + (error.message || '未知错误'))\n      } finally {\n        this.formLoading = false\n      }\n    },\n    \n\n\n    // 刷新所有数据（供父组件调用）\n    async refreshAllData() {\n      try {\n        await Promise.all([\n          this.loadRevenueStats(),\n          this.loadAgentList()\n        ])\n      } catch (error) {\n        console.error('❌ 创作者中心数据刷新失败:', error)\n      }\n    },\n\n    // 🔥 智能体创建完成\n    handleAgentComplete(agent) {\n      this.handleCloseForm()\n      this.loadAgentList()\n      this.loadRevenueStats()\n    },\n\n\n\n    // 🔥 从表单中删除工作流\n    async handleDeleteWorkflowFromForm(workflow) {\n      console.log('🎯 CreatorCenter: 从表单中删除工作流', workflow)\n      try {\n        // 这里调用删除工作流的API\n        // await deleteWorkflow(workflow.id)\n        this.$message.success('工作流删除成功')\n\n        // 刷新表单中的工作流列表\n        if (this.$refs.agentForm && this.$refs.agentForm.loadWorkflowList) {\n          this.$refs.agentForm.loadWorkflowList(workflow.agentId)\n        }\n      } catch (error) {\n        console.error('🎯 CreatorCenter: 删除工作流失败:', error)\n        this.$message.error('删除工作流失败')\n      }\n    },\n\n    // 🔥 ==================== 提现相关方法 ====================\n\n    // 处理提现\n    handleWithdraw(params) {\n      this.withdrawAvailableAmount = params.availableAmount\n      this.withdrawRevenueType = params.revenueType\n      this.showWithdrawModal = true\n    },\n\n    // 打开提现记录弹窗\n    openWithdrawRecordsModal() {\n      this.showWithdrawRecordsModal = true\n    },\n\n    // 提现申请提交\n    async handleWithdrawSubmit(params) {\n\n      this.withdrawLoading = true\n\n      try {\n        const response = await this.$http.post('/api/usercenter/applyWithdrawal', params)\n\n        if (response.success) {\n          this.$notification.success({\n            message: '提现申请成功',\n            description: '您的提现申请已提交，预计1-3个工作日到账'\n          })\n\n          this.showWithdrawModal = false\n\n          // 刷新收益数据\n          await this.loadRevenueStats()\n        } else {\n          this.$notification.error({\n            message: '提现申请失败',\n            description: response.message || '申请失败，请重试'\n          })\n        }\n      } catch (error) {\n        console.error('🔥 CreatorCenter: 提现申请失败:', error)\n        this.$notification.error({\n          message: '提现申请失败',\n          description: error.message || '申请失败，请重试'\n        })\n      } finally {\n        this.withdrawLoading = false\n      }\n    },\n\n    // 取消提现\n    handleWithdrawCancel() {\n      this.showWithdrawModal = false\n    },\n\n    // 关闭提现记录弹窗\n    handleWithdrawRecordsCancel() {\n      this.showWithdrawRecordsModal = false\n    }\n  }\n}\n</script>\n\n<style lang=\"less\" scoped>\n.creator-center {\n  min-height: 100vh;\n  background: #f5f7fa;\n\n  // 页面头部\n  .creator-header {\n    background: #fff;\n    border-bottom: 1px solid #e8eaec;\n    padding: 24px 0;\n    margin-bottom: 24px;\n\n    .header-content {\n      max-width: 1600px;\n      margin: 0 auto;\n      padding: 0 32px;\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n\n      .header-left {\n        .page-title {\n          margin: 0;\n          font-size: 28px;\n          font-weight: 600;\n          color: #1f2937;\n          display: flex;\n          align-items: center;\n          gap: 12px;\n\n          .anticon {\n            color: #1890ff;\n          }\n        }\n\n        .page-subtitle {\n          margin: 8px 0 0 0;\n          color: #6b7280;\n          font-size: 14px;\n        }\n      }\n\n      .header-right {\n        .ant-btn {\n          height: 40px;\n          padding: 0 24px;\n          font-size: 14px;\n          border-radius: 6px;\n          box-shadow: 0 2px 4px rgba(24, 144, 255, 0.2);\n\n          &:hover {\n            transform: translateY(-1px);\n            box-shadow: 0 4px 8px rgba(24, 144, 255, 0.3);\n          }\n        }\n      }\n    }\n  }\n\n  // 统计卡片面板\n  .stats-cards-section {\n    max-width: 1600px;\n    margin: 0 auto 24px;\n    padding: 0 32px;\n  }\n\n  // 收益排行面板\n  .ranking-section {\n    max-width: 1600px;\n    margin: 0 auto 24px;\n    padding: 32px;\n  }\n\n  // 🔥 收益统计区域\n  .stats-cards-section {\n    max-width: 1600px;\n    margin: 0 auto;\n    padding: 0 32px;\n\n    .section-header {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      margin-bottom: 20px;\n\n      .section-title {\n        margin: 0;\n        font-size: 20px;\n        font-weight: 600;\n        color: #1f2937;\n      }\n\n      .section-actions {\n        display: flex;\n        gap: 12px;\n\n        .records-btn {\n          height: 32px;\n          padding: 0 16px;\n          font-size: 13px;\n          border-radius: 6px;\n          border: 1px solid #d9d9d9;\n          background: #fff;\n\n          &:hover {\n            border-color: #1890ff;\n            color: #1890ff;\n          }\n\n          .anticon {\n            font-size: 12px;\n            margin-right: 6px;\n          }\n        }\n      }\n    }\n  }\n\n  // 智能体管理区域\n  .agents-section {\n    max-width: 1600px;\n    margin: 0 auto;\n    padding: 0 32px;\n\n    .section-header {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      margin-bottom: 20px;\n\n      .section-title {\n        margin: 0;\n        font-size: 20px;\n        font-weight: 600;\n        color: #1f2937;\n      }\n\n      .section-actions {\n        display: flex;\n        align-items: center;\n        gap: 12px;\n\n        .ant-select {\n          .ant-select-selection {\n            border-radius: 6px;\n          }\n        }\n\n        .ant-input-search {\n          .ant-input {\n            border-radius: 6px;\n          }\n\n          .ant-input-search-button {\n            border-radius: 0 6px 6px 0;\n          }\n        }\n      }\n    }\n  }\n}\n\n// 响应式设计\n@media (max-width: 768px) {\n  .creator-center {\n    .creator-header {\n      .header-content {\n        flex-direction: column;\n        gap: 16px;\n        text-align: center;\n\n        .header-left {\n          .page-title {\n            font-size: 24px;\n          }\n        }\n      }\n    }\n\n    .stats-cards-section,\n    .ranking-section,\n    .agents-section {\n      padding: 0 20px;\n    }\n\n    .agents-section {\n      .section-header {\n        flex-direction: column;\n        gap: 16px;\n        align-items: stretch;\n\n        .section-actions {\n          justify-content: center;\n          flex-wrap: wrap;\n        }\n      }\n    }\n  }\n}\n</style>\n"], "sourceRoot": "src/views/website/workflow/components"}]}