{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\agent\\AgentDetailPage.vue?vue&type=template&id=2f9872c6&scoped=true&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\agent\\AgentDetailPage.vue", "mtime": 1754511159792}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\n<div class=\"agent-detail-page\">\n  <!-- 时间轴式导航 -->\n  <div class=\"timeline-nav\">\n    <div class=\"timeline-line\"></div>\n    <div\n      v-for=\"(item, index) in navItems\"\n      :key=\"item.id\"\n      class=\"timeline-node\"\n      :class=\"{ 'active': activeNavItem === item.id }\"\n      :style=\"{ top: (index * 120 + 100) + 'px' }\"\n      @click=\"scrollToSection(item.id)\"\n    >\n      <div class=\"node-dot\"></div>\n      <div class=\"node-label\">{{ item.title }}</div>\n    </div>\n  </div>\n\n  <!-- 权限检查加载状态 -->\n  <div v-if=\"isCheckingPermission\" class=\"permission-loading\">\n    <a-spin size=\"large\">\n      <div class=\"loading-text\">正在验证访问权限...</div>\n    </a-spin>\n  </div>\n\n  <!-- 未登录提示 -->\n  <div v-else-if=\"!isLoggedIn\" class=\"permission-denied\">\n    <div class=\"permission-content\">\n      <a-icon type=\"lock\" class=\"permission-icon\" />\n      <h2>需要登录访问</h2>\n      <p>请先登录您的账户以查看智能体详细信息</p>\n      <a-button type=\"primary\" size=\"large\" @click=\"goToLogin\">\n        立即登录\n      </a-button>\n    </div>\n  </div>\n\n  <!-- 未购买提示 -->\n  <div v-else-if=\"!isPurchased\" class=\"permission-denied\">\n    <div class=\"permission-content\">\n      <a-icon type=\"shopping-cart\" class=\"permission-icon\" />\n      <h2>需要购买访问</h2>\n      <p>您还未购买此智能体，请先购买后再查看详细信息</p>\n      <div class=\"action-buttons\">\n        <a-button size=\"large\" @click=\"goBack\">\n          返回列表\n        </a-button>\n        <a-button type=\"primary\" size=\"large\" @click=\"goToPurchase\">\n          立即购买\n        </a-button>\n      </div>\n    </div>\n  </div>\n\n  <!-- 主要内容区域 -->\n  <div v-else class=\"main-content\">\n    <!-- 页面头部 -->\n    <div class=\"page-header\">\n      <a-button @click=\"goBack\" class=\"back-button\">\n        <a-icon type=\"arrow-left\" />\n        返回\n      </a-button>\n      <div class=\"breadcrumb\">\n        <a-breadcrumb>\n          <a-breadcrumb-item>\n            <router-link to=\"/workflow-center\">工作流中心</router-link>\n          </a-breadcrumb-item>\n          <a-breadcrumb-item>智能体详情</a-breadcrumb-item>\n        </a-breadcrumb>\n      </div>\n    </div>\n\n    <!-- 智能体基本信息 -->\n    <div id=\"agent-info\" class=\"agent-info-section\">\n      <div class=\"agent-basic-info\">\n        <div class=\"agent-avatar\">\n          <img \n            :src=\"agentDetail.agentAvatar || defaultAvatar\" \n            :alt=\"agentDetail.agentName\"\n            @error=\"handleAvatarError\"\n          />\n        </div>\n        <div class=\"agent-details\">\n          <h1 class=\"agent-name\">{{ agentDetail.agentName || '智能体名称' }}</h1>\n          <div class=\"agent-description\">\n            <p>{{ agentDetail.agentDescription || '暂无描述' }}</p>\n          </div>\n          <div class=\"creator-info\">\n            <img\n              :src=\"(agentDetail.creatorInfo && agentDetail.creatorInfo.avatar) || agentDetail.creatorAvatar || defaultCreatorAvatar\"\n              :alt=\"(agentDetail.creatorInfo && agentDetail.creatorInfo.nickname) || agentDetail.creatorNickname || '创作者'\"\n              class=\"creator-avatar\"\n              @error=\"handleCreatorAvatarError\"\n            />\n            <span class=\"creator-name\">{{ (agentDetail.creatorInfo && agentDetail.creatorInfo.nickname) || agentDetail.creatorNickname || '创作者' }}</span>\n            <span class=\"author-type-badge\" :class=\"authorTypeClass\">\n              {{ authorTypeText }}\n            </span>\n          </div>\n        </div>\n      </div>\n\n      <!-- 阅读提示 -->\n      <div class=\"reading-tip\" @click=\"scrollToUsageGuide\">\n        <a-icon type=\"info-circle\" class=\"tip-icon\" />\n        <span class=\"tip-text\">\n          请往下滑或点此快速下滑，仔细阅读工作流导入使用说明\n        </span>\n        <a-icon type=\"arrow-down\" class=\"arrow-icon\" />\n      </div>\n    </div>\n\n    <!-- 演示视频区域 -->\n    <div id=\"demo-video\" v-if=\"agentDetail.demoVideo\" class=\"video-section\">\n      <div class=\"video-header\" @click=\"toggleVideoCollapse\">\n        <h3>演示视频</h3>\n        <a-icon\n          :type=\"videoCollapsed ? 'down' : 'up'\"\n          class=\"collapse-icon\"\n        />\n      </div>\n      <div v-show=\"!videoCollapsed\" class=\"video-container\">\n        <video\n          ref=\"videoPlayer\"\n          :src=\"agentDetail.demoVideo\"\n          controls\n          autoplay\n          muted\n          preload=\"metadata\"\n          @loadstart=\"onVideoLoadStart\"\n          @loadeddata=\"onVideoLoaded\"\n          @error=\"onVideoError\"\n          @play=\"onVideoPlay\"\n          @pause=\"onVideoPause\"\n        >\n          您的浏览器不支持视频播放\n        </video>\n        <div v-if=\"videoError\" class=\"video-error\">\n          <a-icon type=\"exclamation-circle\" />\n          <span>视频加载失败，请稍后重试</span>\n        </div>\n      </div>\n    </div>\n\n    <!-- 工作流导入使用说明 -->\n    <div id=\"usage-guide\" class=\"usage-guide-section\">\n      <div class=\"guide-header\" @click=\"toggleGuideCollapse\">\n        <h3>工作流导入使用说明</h3>\n        <a-icon\n          :type=\"guideCollapsed ? 'down' : 'up'\"\n          class=\"collapse-icon\"\n        />\n      </div>\n      <div v-show=\"!guideCollapsed\" class=\"guide-content\">\n        <div class=\"guide-step\">\n          <div class=\"step-number\">1</div>\n          <div class=\"step-content\">\n            <p class=\"step-text\">点击下载工作流，下载完成是一个压缩包。</p>\n            <div class=\"step-image\" @click=\"previewImage('https://cdn.aigcview.com/defaults/export.png')\">\n              <img\n                src=\"https://cdn.aigcview.com/defaults/export.png\"\n                alt=\"下载工作流示例图\"\n                @error=\"handleGuideImageError\"\n              />\n              <div class=\"image-overlay\">\n                <a-icon type=\"zoom-in\" />\n                <span>点击查看大图</span>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div class=\"guide-step\">\n          <div class=\"step-number\">2</div>\n          <div class=\"step-content\">\n            <p class=\"step-text\">\n              <a\n                href=\"https://www.coze.cn/space\"\n                target=\"_blank\"\n                class=\"coze-link\"\n              >\n                点此快速跳转Coze工作空间\n                <a-icon type=\"external-link\" />\n              </a>\n              ，选择需要放置的工作空间，点击右上角导入按钮，选择下载好的工作流压缩包即可完成。<span class=\"highlight-text\">（压缩包无需解压）</span>\n            </p>\n            <div class=\"step-image\" @click=\"previewImage('https://cdn.aigcview.com/defaults/import.png')\">\n              <img\n                src=\"https://cdn.aigcview.com/defaults/import.png\"\n                alt=\"导入工作流示例图\"\n                @error=\"handleGuideImageError\"\n              />\n              <div class=\"image-overlay\">\n                <a-icon type=\"zoom-in\" />\n                <span>点击查看大图</span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 工作流列表区域 -->\n    <div id=\"workflow-list\" class=\"workflow-section\">\n      <h3>关联工作流 ({{ workflowList.length }}个)</h3>\n      <div v-if=\"workflowLoading\" class=\"workflow-loading\">\n        <a-spin>加载工作流列表...</a-spin>\n      </div>\n      <div v-else-if=\"workflowList.length === 0\" class=\"workflow-empty\">\n        <a-empty description=\"暂无关联工作流\" />\n      </div>\n      <div v-else class=\"workflow-list\">\n        <div\n          v-for=\"workflow in workflowList\"\n          :key=\"workflow.id\"\n          class=\"workflow-card\"\n        >\n          <!-- 工作流头像（使用智能体头像） -->\n          <div class=\"workflow-avatar\">\n            <img\n              :src=\"agentDetail.agentAvatar || defaultAgentAvatar\"\n              :alt=\"workflow.workflowName\"\n              class=\"workflow-image\"\n              @error=\"handleWorkflowImageError\"\n            />\n          </div>\n\n          <div class=\"workflow-info\">\n            <h4 class=\"workflow-name\">{{ workflow.workflowName }}</h4>\n            <p class=\"workflow-description\">{{ workflow.workflowDescription || '暂无描述' }}</p>\n\n            <!-- 🔥 新增：输入参数说明 -->\n            <div class=\"workflow-params\">\n              <div class=\"params-label\">\n                <a-icon type=\"setting\" />\n                <span>输入参数说明</span>\n              </div>\n              <div class=\"params-content\">\n                {{ workflow.inputParamsDesc || '暂无输入参数说明' }}\n              </div>\n            </div>\n          </div>\n\n          <div class=\"workflow-actions\">\n            <a-button\n              type=\"primary\"\n              size=\"default\"\n              class=\"download-btn\"\n              @click=\"handleWorkflowDownload(workflow)\"\n              :loading=\"workflow.downloading\"\n              :disabled=\"!workflow.workflowPackage\"\n            >\n              <a-icon type=\"cloud-download\" />\n              <span>下载工作流</span>\n            </a-button>\n          </div>\n        </div>\n      </div>\n\n      <!-- 使用提示 -->\n      <div v-if=\"workflowList.length > 0\" class=\"workflow-usage-tip\">\n        <a-icon type=\"info-circle\" class=\"tip-icon\" />\n        <span class=\"tip-text\">\n          点击下载工作流是一个压缩包，将下载好的压缩包导入进Coze工作空间即可使用。<span class=\"highlight-text\">（压缩包无需解压）</span>\n        </span>\n      </div>\n    </div>\n  </div>\n</div>\n", null]}