/// <reference types="node" />
import { Axios<PERSON>dapter, AxiosInstance, AxiosRequestConfig, AxiosResponse, Method } from 'axios';
import { Headers } from '../interface';
import { getNormalDataFromError } from '../utils';
export interface TOSConstructorOptions {
    accessKeyId: string;
    accessKeySecret: string;
    stsToken?: string;
    bucket?: string;
    endpoint?: string;
    /**
     * default value: true
     * when using proxyHost&proxyPort, it needs to be set to false
     */
    secure?: boolean;
    region: string;
    /**
     * proxy for web, use it with the middleware of `./proxy`
     */
    proxy?: string | {
        url: string;
        needProxyParams?: boolean;
    };
    /**
     * proxy to general http proxy server, this feature doesn't work in browser environment.
     * only support http proxy server.
     * proxyHost and proxyPort are required if the proxy function works.
     * HINT: need set `secure` field false
     */
    proxyHost?: string;
    proxyPort?: number;
    /**
     * default value: true
     */
    enableVerifySSL?: boolean;
    /**
     * default value: true
     */
    autoRecognizeContentType?: boolean;
    /**
     * unit: ms
     * default value: 120s
     * disable if value <= 0
     */
    requestTimeout?: number;
    /**
     * unit: ms
     * default value: 10s
     * disable if value <= 0
     */
    connectionTimeout?: number;
    /**
     * default value: 1024
     */
    maxConnections?: number;
    /**
     * unit: ms
     * default value: 30s
     */
    idleConnectionTime?: number;
    /**
     * default value: 3
     *
     * disable if value <= 0
     */
    maxRetryCount?: number;
    /**
     * default value: false
     *
     * CRC executed by js is slow currently, it's default value will be true if it is fast enough.
     */
    enableCRC?: boolean;
    /**
     * set request adapter to send request.
     */
    requestAdapter?: AxiosAdapter;
    /**
     * default value: false  ${bucket}.${endpoint}
     * if true request will not combine `${bucket}.${endpoint}`
     */
    isCustomDomain?: boolean;
    /**
     * @private unstable option: false | true | undefined
     * default value: undefined
     * true:
     * Allow SDK to internally catch server errors for 404 and return default values
     * Allow SDK to internally change some put methods to delete methods when pass empty value
     */
    enableOptimizeMethodBehavior?: boolean;
    /**
     * @private unstable option
     */
    forcePathStyle?: boolean;
    userAgentProductName?: string;
    userAgentSoftName?: string;
    userAgentSoftVersion?: string;
    userAgentCustomizedKeyValues?: Record<string, string>;
}
interface NormalizedTOSConstructorOptions extends TOSConstructorOptions {
    secure: boolean;
    endpoint: string;
    enableVerifySSL: boolean;
    autoRecognizeContentType: boolean;
    requestTimeout: number;
    connectionTimeout: number;
    maxConnections: number;
    idleConnectionTime: number;
    maxRetryCount: number;
    enableCRC: boolean;
}
interface GetSignatureQueryUrlInput {
    bucket: string;
    method: Method;
    path: string;
    subdomain: boolean;
    endpoint: string;
    expires: number;
    query?: Record<string, any>;
}
interface GetSignaturePolicyQueryInput {
    bucket: string;
    expires: number;
    policy: {
        conditions: (string[] | {
            bucket: string;
        } | {
            key: string;
        })[];
    };
}
declare type GetSignatureQueryInput = GetSignatureQueryUrlInput | GetSignaturePolicyQueryInput;
interface FetchOpts<T> {
    needMd5?: boolean;
    handleResponse?: (response: AxiosResponse<T>) => T;
    subdomainBucket?: string;
    axiosOpts?: AxiosRequestConfig;
}
export interface TosResponse<T> {
    data: T;
    statusCode: number;
    headers: Headers;
    /**
     * identifies the errored request, equals to headers['x-tos-request-id'].
     * If you has any question about the request, please send the requestId and id2 to TOS worker.
     */
    requestId: string;
    /**
     * identifies the errored request, equals to headers['x-tos-id-2'].
     * If you has any question about the request, please send the requestId and id2 to TOS worker.
     */
    id2: string;
}
export declare class TOSBase {
    opts: NormalizedTOSConstructorOptions;
    axiosInst: AxiosInstance;
    userAgent: string;
    private httpAgent;
    private httpsAgent;
    constructor(_opts: TOSConstructorOptions);
    private normalizeOpts;
    private getUserAgent;
    protected fetch<Data>(method: Method, path: string, query: Record<string, any>, headers: Headers, body?: Object | File | Blob | NodeJS.ReadableStream, opts?: FetchOpts<Data>): Promise<TosResponse<Data>>;
    protected fetchBucket<Data>(bucket: string | undefined, method: Method, query: any, headers: Headers, body?: Object | File | Blob | NodeJS.ReadableStream, opts?: FetchOpts<Data>): Promise<TosResponse<Data>>;
    protected _fetchObject<Data>(input: {
        bucket?: string;
        key: string;
    } | string, method: Method, query: any, headers: Headers, body?: Object | File | Blob | NodeJS.ReadableStream, opts?: FetchOpts<Data>): Promise<TosResponse<Data>>;
    protected getSignatureQuery(input: GetSignatureQueryInput): Record<string, string>;
    protected getObjectPath: (opts: {
        bucket?: string;
        key: string;
    } | string) => string;
    protected normalizeBucketInput<T extends {
        bucket: string;
    }>(input: T | string): T;
    protected normalizeObjectInput<T extends {
        key: string;
    }>(input: T | string): T;
    protected setObjectContentTypeHeader: (input: string | {
        key: string;
    }, headers: Headers) => void;
    protected getNormalDataFromError: typeof getNormalDataFromError;
}
export default TOSBase;
