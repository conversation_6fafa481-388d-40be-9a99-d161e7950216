package org.jeecg.modules.api.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.modules.demo.userprofile.entity.AicgUserProfile;
import org.jeecg.modules.demo.userprofile.service.IAicgUserProfileService;
import org.jeecg.modules.api.service.IAigcApiService;
import org.jeecg.modules.api.service.impl.AigcApiServiceImpl;
import org.jeecg.modules.api.service.ImageGenerationService;
import org.jeecg.modules.api.exception.VideoGenerationException;
import org.jeecg.modules.demo.plubshop.entity.AigcPlubShop;
import org.jeecg.modules.demo.plubshop.service.IAigcPlubShopService;
import org.jeecg.modules.system.entity.SysRole;
import org.jeecg.modules.system.entity.SysUserRole;
import org.jeecg.modules.system.service.ISysRoleService;
import org.jeecg.modules.system.service.ISysUserRoleService;
import org.jeecg.modules.demo.apiusage.entity.AicgUserApiUsage;
import org.jeecg.modules.demo.apiusage.service.IAicgUserApiUsageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import org.springframework.jdbc.core.JdbcTemplate;

/**
 * 扣子图片生成插件Controller
 * 
 * <AUTHOR>
 * @since 2025-01-16
 */
@Api(tags = "扣子图片生成插件")
@RestController
@RequestMapping("/api/coze/image")
@Slf4j
public class CozeImageController {

    @Autowired
    private IAigcApiService aigcApiService;

    @Autowired
    private IAicgUserProfileService userProfileService;

    @Autowired
    private ISysUserRoleService sysUserRoleService;

    @Autowired
    private ISysRoleService sysRoleService;

    @Autowired
    private IAicgUserApiUsageService apiUsageService;

    @Autowired
    private IAigcPlubShopService plubShopService;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private ImageGenerationService imageGenerationService;

    /**
     * 生成图片任务
     */
    @ApiOperation(value = "生成图片任务", notes = "扣子插件专用：根据参数动态定价并生成图片")
    @PostMapping("/generate-task")
    public Map<String, Object> generateImageTask(@RequestBody Map<String, Object> params, HttpServletRequest request) {
        long requestStartTime = System.currentTimeMillis(); // 记录请求开始时间
        log.info("图片生成任务创建请求开始，参数: {}", params);

        try {
            // 1. 🔥 参数验证和标准化
            log.info("开始参数验证...");
            validateImageParameters(params);
            log.info("参数验证通过，开始标准化...");
            normalizeImageParameters(params);
            log.info("参数标准化完成，标准化后参数: {}", params);
        } catch (Exception e) {
            log.error("参数验证失败: {}", e.getMessage(), e);
            throw new VideoGenerationException("参数验证失败: " + e.getMessage());
        }

        String apiKey = (String) params.get("apiKey");

        // 2. API-Key验证（不扣费）
        AicgUserProfile userProfile = aigcApiService.getUserByApiKey(apiKey);
        if (userProfile == null || userProfile.getStatus() != 1) {
            throw new VideoGenerationException("API-Key无效或已被禁用");
        }

        String userId = userProfile.getUserId();
        log.info("API-Key验证通过，用户ID: {}", userId);

        // 3. 🔥 动态定价计算
        PriceCalculationResult priceResult = calculateImagePrice(userId);
        BigDecimal finalPrice = priceResult.getFinalPrice();
        String userLevel = priceResult.getUserLevel();

        log.info("图片生成价格计算完成 - 用户等级: {}, 价格: {}", userLevel, finalPrice);

        // 4. 🔥 余额检查和预扣费（冻结余额）
        if (finalPrice.compareTo(BigDecimal.ZERO) > 0) {
            boolean preDeductSuccess = userProfileService.preDeductBalance(userId, finalPrice);
            if (!preDeductSuccess) {
                throw new VideoGenerationException("余额不足，当前余额不足以支付 " + finalPrice + " 元");
            }
            log.info("预扣费成功，用户ID: {}, 冻结金额: {}", userId, finalPrice);
        } else {
            log.info("免费服务，跳过预扣费，用户ID: {}", userId);
        }

        try {
            // 5. 调用豆包API生成图片（业务逻辑执行阶段）
            Map<String, Object> imageResult = imageGenerationService.generateImage(params, userProfile);

            log.info("豆包API调用成功，图片URL: {}", imageResult.get("image_url"));

            // 6. 🔥 豆包API调用成功，立即确认扣费（从冻结余额转为实际扣费）
            if (finalPrice.compareTo(BigDecimal.ZERO) > 0) {
                boolean confirmSuccess = userProfileService.confirmDeductBalance(userId, finalPrice);
                if (!confirmSuccess) {
                    throw new RuntimeException("确认扣费失败，冻结余额不足");
                }
                log.info("确认扣费成功，用户ID: {}, 扣费金额: {}", userId, finalPrice);
            } else {
                log.info("免费服务，跳过扣费，用户ID: {}", userId);
            }

            // 7. 🔥 数据联动更新（完全参考图生视频接口）
            try {
                log.info("=== 开始更新插件统计数据 ===");

                // 查询插件信息
                AigcPlubShop plugin = plubShopService.getByPluginKey("coze_image_generate");
                if (plugin != null) {
                    // 🔥 使用与图生视频接口完全相同的调用方式
                    ((AigcApiServiceImpl) aigcApiService).performDataLinkage(userId, plugin, finalPrice);
                    log.info("✅ 插件统计数据更新成功 - 插件: {}, 用户: {}, 金额: {}",
                            plugin.getPlubname(), userId, finalPrice);
                } else {
                    log.warn("⚠️ 未找到插件信息，跳过统计更新: coze_image_generate");
                }

                log.info("=== 插件统计数据更新完成 ===");
            } catch (Exception e) {
                log.error("更新插件统计数据失败，但不影响主功能", e);
            }

            // 8. 🔥 生成交易记录（参考图生视频接口）
            try {
                generateTransactionRecord(userId, "coze_image_generate", "文生图", finalPrice);
                log.info("交易记录已保存 - 用户: {}, 接口: 文生图, 费用: {}", userId, finalPrice);
            } catch (Exception e) {
                log.error("保存交易记录失败", e);
                // 不影响主流程，继续执行
            }

            // 9. 查询扣费后的余额
            AicgUserProfile updatedProfile = userProfileService.getById(userId);
            BigDecimal balanceAfter = updatedProfile != null ? updatedProfile.getAccountBalance() : BigDecimal.ZERO;

            // 10. 🔥 记录API使用情况（参考图生视频接口）
            try {
                recordApiUsage(request, userId, apiKey,
                             "coze_image_generate", "文生图",
                             200, requestStartTime, 0, finalPrice, null);
                log.info("API使用记录已保存 - 用户: {}, 接口: 文生图, 费用: {}", userId, finalPrice);
            } catch (Exception e) {
                log.error("保存API使用记录失败", e);
                // 不影响主流程，继续执行
            }

            // 9. 构建成功响应
            Map<String, Object> response = new HashMap<>();
            response.put("image_url", imageResult.get("image_url"));
            response.put("status", "completed");
            response.put("message", "图片生成成功");
            response.put("deductedAmount", finalPrice);
            response.put("balanceAfter", balanceAfter);

            // 定价详情
            Map<String, Object> pricingDetails = new HashMap<>();
            pricingDetails.put("userLevel", userLevel);
            pricingDetails.put("finalPrice", finalPrice);
            pricingDetails.put("model", "doubao-seedream-3-0-t2i-250415");
            response.put("pricingDetails", pricingDetails);

            log.info("图片生成任务完成，响应: {}", response);
            return response;

        } catch (Exception e) {
            // 🔥 API调用失败，退还预扣费用
            if (finalPrice.compareTo(BigDecimal.ZERO) > 0) {
                try {
                    boolean refundSuccess = userProfileService.refundFrozenBalance(userId, finalPrice);
                    if (refundSuccess) {
                        log.info("API调用失败，预扣费用已退还，用户ID: {}, 退还金额: {}", userId, finalPrice);
                    } else {
                        log.error("API调用失败，预扣费用退还失败，用户ID: {}, 金额: {}", userId, finalPrice);
                    }
                } catch (Exception refundException) {
                    log.error("退还预扣费用时发生异常", refundException);
                }
            }

            // 记录失败的API使用情况
            try {
                recordApiUsage(request, userId, apiKey,
                             "coze_image_generate", "文生图",
                             500, requestStartTime, 0, BigDecimal.ZERO, e.getMessage());
            } catch (Exception recordException) {
                log.error("记录失败API使用情况时发生异常", recordException);
            }

            log.error("图片生成失败", e);
            throw new VideoGenerationException("图片生成失败: " + e.getMessage());
        }
    }

    /**
     * 计算图片生成价格
     */
    private PriceCalculationResult calculateImagePrice(String userId) {
        try {
            log.info("开始计算图片生成价格，用户ID: {}", userId);

            // 1. 获取用户角色
            String userLevel = getUserLevel(userId);

            // 2. 🔥 根据定价表计算价格
            BigDecimal price = getImagePrice(userLevel);

            log.info("图片生成价格计算完成 - 用户等级: {}, 价格: {}", userLevel, price);

            return PriceCalculationResult.success(price, userLevel, price);

        } catch (Exception e) {
            log.error("计算图片生成价格失败，用户ID: {}", userId, e);
            throw new VideoGenerationException("价格计算失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户等级
     */
    private String getUserLevel(String userId) {
        try {
            // 查询用户角色
            QueryWrapper<org.jeecg.modules.system.entity.SysUserRole> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("user_id", userId);
            List<org.jeecg.modules.system.entity.SysUserRole> userRoles = sysUserRoleService.list(queryWrapper);
            
            if (userRoles.isEmpty()) {
                return "user"; // 默认普通用户
            }

            // 获取角色代码列表
            List<String> roleCodes = new ArrayList<>();
            for (SysUserRole userRole : userRoles) {
                SysRole role = sysRoleService.getById(userRole.getRoleId());
                if (role != null) {
                    roleCodes.add(role.getRoleCode());
                }
            }

            // 按优先级返回角色（管理员 > SVIP > VIP > 普通用户）
            if (roleCodes.contains("admin")) {
                return "admin";
            } else if (roleCodes.contains("svip")) {
                return "svip";
            } else if (roleCodes.contains("vip")) {
                return "vip";
            } else {
                return "user";
            }

        } catch (Exception e) {
            log.error("获取用户等级失败，用户ID: {}", userId, e);
            return "user"; // 默认普通用户
        }
    }

    /**
     * 根据用户等级获取图片生成价格
     */
    private BigDecimal getImagePrice(String userLevel) {
        switch (userLevel.toLowerCase()) {
            case "admin":
                return new BigDecimal("0.48"); // 与SVIP相同价格
            case "svip":
                return new BigDecimal("0.48");
            case "vip":
                return new BigDecimal("0.58");
            case "user":
            default:
                return new BigDecimal("0.68");
        }
    }

    /**
     * 验证图片生成参数
     */
    private void validateImageParameters(Map<String, Object> params) {
        try {
            log.info("开始验证图片生成参数: {}", params);

            // 验证API Key
            String apiKey = (String) params.get("apiKey");
            if (apiKey == null || apiKey.trim().isEmpty()) {
                throw new IllegalArgumentException("API Key不能为空");
            }

            // 验证prompt
            String prompt = (String) params.get("prompt");
            if (prompt == null || prompt.trim().isEmpty()) {
                throw new IllegalArgumentException("prompt不能为空");
            }
            if (prompt.length() > 1000) {
                throw new IllegalArgumentException("prompt长度不能超过1000字符");
            }

            // 图片尺寸验证已在DouBaoImageApiService中处理，这里不需要额外验证

            if (params.containsKey("seed")) {
                Object seedObj = params.get("seed");
                if (seedObj != null) {
                    Integer seed = null;
                    if (seedObj instanceof String) {
                        try {
                            seed = Integer.parseInt((String) seedObj);
                        } catch (NumberFormatException e) {
                            throw new IllegalArgumentException("seed必须是整数");
                        }
                    } else if (seedObj instanceof Integer) {
                        seed = (Integer) seedObj;
                    }
                    if (seed != null && (seed < -1 || seed > 2147483647)) {
                        throw new IllegalArgumentException("seed取值范围为[-1, 2147483647]");
                    }
                }
            }

            if (params.containsKey("guidance_scale")) {
                Object guidanceObj = params.get("guidance_scale");
                if (guidanceObj != null) {
                    Float guidanceScale = null;
                    if (guidanceObj instanceof String) {
                        try {
                            guidanceScale = Float.parseFloat((String) guidanceObj);
                        } catch (NumberFormatException e) {
                            throw new IllegalArgumentException("guidance_scale必须是数字");
                        }
                    } else if (guidanceObj instanceof Number) {
                        guidanceScale = ((Number) guidanceObj).floatValue();
                    }
                    if (guidanceScale != null && (guidanceScale < 1.0f || guidanceScale > 10.0f)) {
                        throw new IllegalArgumentException("guidance_scale取值范围为[1.0, 10.0]");
                    }
                }
            }

            log.info("图片生成参数验证通过");

        } catch (Exception e) {
            log.error("图片生成参数验证失败: {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 标准化图片生成参数
     */
    private void normalizeImageParameters(Map<String, Object> params) {
        try {
            log.info("开始标准化图片生成参数: {}", params);

            // 标准化prompt
            String prompt = (String) params.get("prompt");
            if (prompt != null) {
                params.put("prompt", prompt.trim());
            }

            // 标准化size
            if (!params.containsKey("size") || params.get("size") == null) {
                params.put("size", "1024x1024");
            }

            // 标准化seed
            if (!params.containsKey("seed") || params.get("seed") == null) {
                params.put("seed", -1);
            } else {
                Object seedObj = params.get("seed");
                if (seedObj instanceof String) {
                    try {
                        params.put("seed", Integer.parseInt((String) seedObj));
                    } catch (NumberFormatException e) {
                        params.put("seed", -1);
                    }
                }
            }

            // 标准化guidance_scale
            if (!params.containsKey("guidance_scale") || params.get("guidance_scale") == null) {
                params.put("guidance_scale", 2.5f);
            } else {
                Object guidanceObj = params.get("guidance_scale");
                if (guidanceObj instanceof String) {
                    try {
                        params.put("guidance_scale", Float.parseFloat((String) guidanceObj));
                    } catch (NumberFormatException e) {
                        params.put("guidance_scale", 2.5f);
                    }
                } else if (guidanceObj instanceof Number) {
                    params.put("guidance_scale", ((Number) guidanceObj).floatValue());
                }
            }

            // 标准化watermark
            if (!params.containsKey("watermark") || params.get("watermark") == null) {
                params.put("watermark", false);
            } else {
                Object watermarkObj = params.get("watermark");
                if (watermarkObj instanceof String) {
                    params.put("watermark", Boolean.parseBoolean((String) watermarkObj));
                } else if (!(watermarkObj instanceof Boolean)) {
                    params.put("watermark", false);
                }
            }

            log.info("图片生成参数标准化完成: {}", params);

        } catch (Exception e) {
            log.error("图片生成参数标准化失败: {}", e.getMessage(), e);
            throw new RuntimeException("参数标准化失败: " + e.getMessage());
        }
    }



    /**
     * 获取客户端IP地址
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty() && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            return xForwardedFor.split(",")[0];
        }
        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty() && !"unknown".equalsIgnoreCase(xRealIp)) {
            return xRealIp;
        }
        return request.getRemoteAddr();
    }

    /**
     * 价格计算结果类
     */
    private static class PriceCalculationResult {
        private final BigDecimal finalPrice;
        private final String userLevel;
        private final BigDecimal originalPrice;

        private PriceCalculationResult(BigDecimal finalPrice, String userLevel, BigDecimal originalPrice) {
            this.finalPrice = finalPrice;
            this.userLevel = userLevel;
            this.originalPrice = originalPrice;
        }

        public static PriceCalculationResult success(BigDecimal finalPrice, String userLevel, BigDecimal originalPrice) {
            return new PriceCalculationResult(finalPrice, userLevel, originalPrice);
        }

        public BigDecimal getFinalPrice() {
            return finalPrice;
        }

        public String getUserLevel() {
            return userLevel;
        }

        public BigDecimal getOriginalPrice() {
            return originalPrice;
        }
    }

    // ==================== 数据记录辅助方法（参考图生视频接口） ====================

    /**
     * 🔥 生成交易记录（参考图生视频接口的generateTransactionRecord方法）
     */
    private void generateTransactionRecord(String userId, String pluginKey, String pluginName, BigDecimal amount) {
        try {
            log.info("🔥 开始生成交易记录 - 用户ID: {}, 插件: {}, 金额: {}", userId, pluginKey, amount);

            // 获取用户当前余额信息
            AicgUserProfile userProfile = userProfileService.getByUserId(userId);
            if (userProfile == null) {
                log.error("❌ 生成交易记录失败：用户不存在 - 用户ID: {}", userId);
                return;
            }
            log.info("✅ 用户信息获取成功 - 当前余额: {}", userProfile.getAccountBalance());

            // 🔥 完全按照图生视频接口的逻辑插入交易记录
            String transactionId = UUID.randomUUID().toString().replace("-", "");
            String orderType = "plugin"; // 插件订单类型
            int orderStatus = 3; // 已完成

            // 生成订单号（按照图生视频接口的格式）
            String orderPrefix = "IMG";
            String dateStr = new SimpleDateFormat("yyyyMMdd").format(new Date());
            String orderSuffix = transactionId.substring(transactionId.length() - 8).toUpperCase();
            String relatedOrderId = orderPrefix + dateStr + "_" + orderSuffix;

            String insertSql = "INSERT INTO aicg_user_transaction (" +
                "id, user_id, transaction_type, amount, balance_before, balance_after, " +
                "description, related_order_id, transaction_time, create_by, create_time, " +
                "order_status, order_type, plugin_id, plugin_key, plugin_name" +
                ") VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

            jdbcTemplate.update(insertSql,
                transactionId,
                userId,
                1, // 消费类型
                amount,
                userProfile.getAccountBalance().add(amount), // 扣费前余额
                userProfile.getAccountBalance(), // 扣费后余额
                "调用插件: " + pluginName,
                relatedOrderId,
                new Date(),
                "api_system",
                new Date(),
                orderStatus,
                orderType,
                null, // pluginId (文生图不是插件商店的插件)
                pluginKey,
                pluginName
            );

            log.info("✅ 交易记录生成成功 - 交易ID: {}, 订单号: {}", transactionId, relatedOrderId);

        } catch (Exception e) {
            log.error("❌ 生成交易记录失败", e);
            throw e; // 重新抛出异常以便上层捕获
        }
    }

    /**
     * 🔥 记录API使用情况（参考图生视频接口）
     */
    private void recordApiUsage(HttpServletRequest request, String userId, String apiKey,
                               String pluginKey, String pluginName, int responseStatus,
                               long requestStartTime, Integer tokensUsed, BigDecimal costAmount,
                               String errorMessage) {
        try {
            log.info("🔥 开始记录API使用情况 - 用户: {}, 插件: {} ({})", userId, pluginName, pluginKey);

            // 计算响应时间
            long responseTime = System.currentTimeMillis() - requestStartTime;

            // 获取请求信息
            String ipAddress = getClientIpAddress(request);
            String userAgent = request.getHeader("User-Agent");
            String requestParams = String.format("{\"pluginKey\":\"%s\",\"pluginName\":\"%s\"}", pluginKey, pluginName);

            // 调用标准的API使用记录服务
            Object result = apiUsageService.recordUsage(
                userId,
                apiKey,
                "/api/coze/image/generate-task",
                "POST",
                requestParams,
                responseStatus,
                (int) responseTime,
                tokensUsed != null ? tokensUsed : 0,
                costAmount,
                ipAddress,
                userAgent,
                errorMessage
            );

            log.info("apiUsageService.recordUsage 调用成功，返回结果: {}", result != null ? result.toString() : "null");
            log.info("API使用记录保存成功 - 用户: {}, 插件: {} ({}), 费用: {}",
                    userId, pluginName, pluginKey, costAmount);
        } catch (Exception e) {
            log.error("记录API使用情况失败", e);
            throw e; // 重新抛出异常以便上层捕获
        }
    }
}
