import { axios } from '@/utils/request'
import { ajaxGetDictItems } from '@/api/api'
import { getCachedCategoriesData, cacheCategoriesData } from '@/views/website/market/utils/marketUtils'

/**
 * 插件中心相关API接口
 */
const marketApi = {

  /**
   * 获取插件分类字典数据（带缓存）
   * @param {Boolean} useCache 是否使用缓存，默认true
   * @returns {Promise} 分类列表
   */
  async getPluginCategories(useCache = true) {
    // 尝试从缓存获取
    if (useCache) {
      const cachedData = getCachedCategoriesData()
      if (cachedData) {
        console.log('使用缓存的分类数据:', cachedData)
        return Promise.resolve({
          success: true,
          result: cachedData
        })
      }
    }

    // 从服务器获取
    try {
      const response = await ajaxGetDictItems('plugin_category')

      // 缓存数据
      if (response.success && response.result) {
        cacheCategoriesData(response.result)
      }

      return response
    } catch (error) {
      console.error('获取分类数据失败:', error)
      throw error
    }
  },
  
  /**
   * 获取插件列表
   * @param {Object} params 查询参数
   * @returns {Promise} 插件列表数据
   */
  getPluginList(params) {
    return axios({
      url: '/plubshop/aigcPlubShop/list',
      method: 'get',
      params: {
        status: 1, // 只获取已上架的插件
        ...params
      }
    })
  },
  
  /**
   * 获取插件详情
   * @param {String} id 插件ID
   * @returns {Promise} 插件详情数据
   */
  getPluginDetail(id) {
    return axios({
      url: '/plubshop/aigcPlubShop/getPluginDetail',
      method: 'get',
      params: { id }
    })
  },
  
  /**
   * 使用插件（API调用验证）
   * @param {Object} params 调用参数
   * @returns {Promise} 调用结果
   */
  usePlugin(params) {
    return axios({
      url: '/api/aigc/verify-apikey',
      method: 'post',
      data: params
    })
  },
  
  /**
   * 获取分类统计数据（可选功能）
   * @returns {Promise} 各分类插件数量统计
   */
  getCategoryStats() {
    return axios({
      url: '/plubshop/aigcPlubShop/categoryStats',
      method: 'get'
    })
  }
}

export default marketApi
