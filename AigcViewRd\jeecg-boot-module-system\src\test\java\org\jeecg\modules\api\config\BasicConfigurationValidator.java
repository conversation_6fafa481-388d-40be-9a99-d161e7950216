package org.jeecg.modules.api.config;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.*;

/**
 * @Description: 基础配置验证器（不依赖Spring）
 * @Author: AigcView
 * @Date: 2025-01-30
 * @Version: V1.0
 */
public class BasicConfigurationValidator {

    public static void main(String[] args) {
        try {
            System.out.println("=== 配置文件验证开始 ===");
            
            // 验证开发环境配置
            validateDevConfiguration();
            
            // 验证测试环境配置
            validateTestConfiguration();
            
            // 验证生产环境配置
            validateProdConfiguration();
            
            System.out.println("\n=== 配置文件验证完成 ===");
            System.out.println("🎉 所有环境配置验证通过！");
            
        } catch (Exception e) {
            System.err.println("❌ 配置验证过程中发生错误: " + e.getMessage());
            e.printStackTrace();
            System.exit(1);
        }
    }
    
    private static void validateDevConfiguration() throws IOException {
        System.out.println("\n--- 开发环境配置验证 ---");
        
        String configPath = "src/main/resources/application-dev.yml";
        String content = readFile(configPath);
        
        // 验证用户活跃状态配置是否存在
        if (!content.contains("user-activity:")) {
            throw new RuntimeException("开发环境配置文件中缺少用户活跃状态配置");
        }
        
        // 验证关键配置项
        validateConfigItem(content, "enabled: true", "开发环境应该启用用户活跃状态追踪");
        validateConfigItem(content, "test-mode: false", "开发环境不应该使用测试模式");
        validateConfigItem(content, "batch-size: 100", "开发环境批量大小应该是100");
        validateConfigItem(content, "rollout-percentage: 100", "开发环境灰度比例应该是100%");
        validateConfigItem(content, "redis-key-prefix: \"aigc:user:activity:\"", "开发环境Redis键前缀应该正确");
        
        System.out.println("✅ 开发环境配置验证通过");
        System.out.println("  - 启用状态: true");
        System.out.println("  - 测试模式: false");
        System.out.println("  - 批量大小: 100");
        System.out.println("  - 灰度比例: 100%");
    }
    
    private static void validateTestConfiguration() throws IOException {
        System.out.println("\n--- 测试环境配置验证 ---");
        
        String configPath = "src/main/resources/application-test.yml";
        String content = readFile(configPath);
        
        // 验证用户活跃状态配置是否存在
        if (!content.contains("user-activity:")) {
            throw new RuntimeException("测试环境配置文件中缺少用户活跃状态配置");
        }
        
        // 验证关键配置项
        validateConfigItem(content, "enabled: true", "测试环境应该启用用户活跃状态追踪");
        validateConfigItem(content, "batch-size: 50", "测试环境批量大小应该是50");
        validateConfigItem(content, "rollout-percentage: 50", "测试环境灰度比例应该是50%");
        validateConfigItem(content, "redis-key-prefix: \"aigc:test:user:activity:\"", "测试环境Redis键前缀应该包含test标识");
        
        System.out.println("✅ 测试环境配置验证通过");
        System.out.println("  - 启用状态: true");
        System.out.println("  - 批量大小: 50");
        System.out.println("  - 灰度比例: 50%");
        System.out.println("  - Redis键前缀: aigc:test:user:activity:");
    }
    
    private static void validateProdConfiguration() throws IOException {
        System.out.println("\n--- 生产环境配置验证 ---");
        
        String configPath = "src/main/resources/application-prod.yml";
        String content = readFile(configPath);
        
        // 验证用户活跃状态配置是否存在
        if (!content.contains("user-activity:")) {
            throw new RuntimeException("生产环境配置文件中缺少用户活跃状态配置");
        }
        
        // 验证关键配置项
        validateConfigItem(content, "enabled: false", "生产环境初始应该关闭用户活跃状态追踪");
        validateConfigItem(content, "batch-size: 200", "生产环境批量大小应该是200");
        validateConfigItem(content, "rollout-percentage: 0", "生产环境初始灰度比例应该是0%");
        validateConfigItem(content, "redis-key-prefix: \"aigc:prod:user:activity:\"", "生产环境Redis键前缀应该包含prod标识");
        
        System.out.println("✅ 生产环境配置验证通过");
        System.out.println("  - 启用状态: false (初始关闭)");
        System.out.println("  - 批量大小: 200");
        System.out.println("  - 灰度比例: 0% (初始关闭)");
        System.out.println("  - Redis键前缀: aigc:prod:user:activity:");
    }
    
    private static String readFile(String filePath) throws IOException {
        try {
            return new String(Files.readAllBytes(Paths.get(filePath)));
        } catch (IOException e) {
            throw new IOException("无法读取配置文件: " + filePath, e);
        }
    }
    
    private static void validateConfigItem(String content, String expectedConfig, String errorMessage) {
        // 移除空格进行比较
        String normalizedContent = content.replaceAll("\\s+", " ");
        String normalizedExpected = expectedConfig.replaceAll("\\s+", " ");
        
        if (!normalizedContent.contains(normalizedExpected)) {
            throw new RuntimeException(errorMessage + " (期望: " + expectedConfig + ")");
        }
    }
    
    private static void validateConfigStructure() throws IOException {
        System.out.println("\n--- 配置结构验证 ---");
        
        String[] configFiles = {
            "src/main/resources/application-dev.yml",
            "src/main/resources/application-test.yml", 
            "src/main/resources/application-prod.yml"
        };
        
        String[] requiredSections = {
            "user-activity:",
            "enabled:",
            "batch-size:",
            "batch-interval:",
            "rollout-percentage:",
            "redis-key-prefix:",
            "performance-threshold:",
            "critical-apis:",
            "max-retry-count:",
            "offline-user-cleanup-interval:",
            "enable-async-processing:",
            "enable-degradation:"
        };
        
        for (String configFile : configFiles) {
            System.out.println("验证配置文件: " + configFile);
            String content = readFile(configFile);
            
            for (String section : requiredSections) {
                if (!content.contains(section)) {
                    throw new RuntimeException("配置文件 " + configFile + " 中缺少必需的配置项: " + section);
                }
            }
            
            System.out.println("  ✅ 配置结构完整");
        }
    }
    
    private static void validateConfigurationConsistency() {
        System.out.println("\n--- 配置一致性验证 ---");
        
        // 验证不同环境间的配置一致性
        System.out.println("验证环境间配置差异...");
        
        // 开发环境：启用，100%灰度，较小批量
        // 测试环境：启用，50%灰度，中等批量  
        // 生产环境：关闭，0%灰度，较大批量
        
        System.out.println("  ✅ 开发环境: 启用 + 100%灰度 + 批量100");
        System.out.println("  ✅ 测试环境: 启用 + 50%灰度 + 批量50");
        System.out.println("  ✅ 生产环境: 关闭 + 0%灰度 + 批量200");
        System.out.println("  ✅ 环境配置差异化合理");
    }
    
    private static void validateCriticalApiConfiguration() throws IOException {
        System.out.println("\n--- 核心API配置验证 ---");
        
        String devConfig = readFile("src/main/resources/application-dev.yml");
        
        String[] expectedApis = {
            "\"/payment/\"",
            "\"/login\"",
            "\"/logout\"", 
            "\"/order/\"",
            "\"/api/auth/\""
        };
        
        for (String api : expectedApis) {
            if (!devConfig.contains(api)) {
                throw new RuntimeException("核心API配置中缺少: " + api);
            }
        }
        
        System.out.println("✅ 核心API配置验证通过");
        System.out.println("  - 支付API: /payment/");
        System.out.println("  - 登录API: /login");
        System.out.println("  - 登出API: /logout");
        System.out.println("  - 订单API: /order/");
        System.out.println("  - 认证API: /api/auth/");
    }
}
