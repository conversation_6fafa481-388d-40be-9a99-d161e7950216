# 邀请奖励触发机制测试用例

## 📋 测试目标

验证会员订阅成功后，邀请奖励能够正确触发并计算佣金。

## 🧪 测试场景

### 场景1：普通用户邀请奖励

**前置条件：**
1. 用户A（普通用户，邀请了5人）
2. 用户B通过用户A的邀请码注册
3. 用户B订阅VIP会员（99元/月）

**预期结果：**
- 用户A获得佣金：99 × 30% = 29.7元
- 创建奖励记录在 `aicg_user_referral_reward` 表
- 用户A的 `available_commission` 增加29.7元

### 场景2：VIP用户邀请奖励

**前置条件：**
1. 用户C（VIP用户，邀请了15人）
2. 用户D通过用户C的邀请码注册
3. 用户D订阅SVIP会员（299元/月）

**预期结果：**
- 用户C获得佣金：299 × 45% = 134.55元
- 创建奖励记录在 `aicg_user_referral_reward` 表
- 用户C的 `available_commission` 增加134.55元

### 场景3：SVIP用户邀请奖励

**前置条件：**
1. 用户E（SVIP用户，邀请了3人）
2. 用户F通过用户E的邀请码注册
3. 用户F订阅VIP会员（99元/月）

**预期结果：**
- 用户E获得佣金：99 × 50% = 49.5元
- 创建奖励记录在 `aicg_user_referral_reward` 表
- 用户E的 `available_commission` 增加49.5元

## 🔍 测试步骤

### 步骤1：准备测试数据

```sql
-- 1. 创建测试用户A（普通用户）
INSERT INTO aicg_user_profile (user_id, username, account_balance, commission_level, valid_invite_count, total_commission, available_commission) 
VALUES ('test_user_a', 'testUserA', 100.00, 1, 5, 0.00, 0.00);

-- 2. 创建测试用户B（被邀请人）
INSERT INTO aicg_user_profile (user_id, username, account_balance) 
VALUES ('test_user_b', 'testUserB', 50.00);

-- 3. 创建邀请关系
INSERT INTO aicg_user_referral (id, referrer_id, referee_id, referral_code, register_time, status, create_time) 
VALUES ('test_referral_1', 'test_user_a', 'test_user_b', 'INVITE123', NOW(), 1, NOW());

-- 4. 创建会员订阅订单
INSERT INTO aicg_user_transaction (
    id, user_id, transaction_type, amount, balance_before, balance_after, 
    description, order_status, order_type, product_info, related_order_id, create_time
) VALUES (
    'test_transaction_1', 'test_user_b', 5, 99.00, 50.00, 50.00,
    'VIP会员月卡 - 1个月', 1, 'membership', 
    '{"membershipLevel": 2, "duration": 1, "planName": "VIP会员月卡"}',
    'ORDER_TEST_123456', NOW()
);
```

### 步骤2：模拟支付成功

```java
// 调用支付成功处理方法
boolean success = processPaymentSuccess("ORDER_TEST_123456", "ALIPAY_TRADE_123", "99.00");
```

### 步骤3：验证结果

```sql
-- 1. 检查奖励记录是否创建
SELECT * FROM aicg_user_referral_reward 
WHERE referrer_id = 'test_user_a' AND referee_id = 'test_user_b';

-- 预期结果：
-- reward_amount = 29.70
-- trigger_event = '会员订阅成功 - 等级2 - 订单:ORDER_TEST_123456'
-- status = 1 (待发放)

-- 2. 检查邀请人佣金余额是否更新
SELECT available_commission, total_commission 
FROM aicg_user_profile 
WHERE user_id = 'test_user_a';

-- 预期结果：
-- available_commission = 29.70
-- total_commission = 29.70

-- 3. 检查邀请关系状态是否更新
SELECT status, first_recharge_time, first_recharge_amount 
FROM aicg_user_referral 
WHERE id = 'test_referral_1';

-- 预期结果：
-- status = 2 (已确认)
-- first_recharge_time = 当前时间
-- first_recharge_amount = 99.00
```

## 🧪 完整测试脚本

```sql
-- =====================================================
-- 邀请奖励触发机制测试脚本
-- =====================================================

-- 清理测试数据
DELETE FROM aicg_user_referral_reward WHERE referrer_id LIKE 'test_user_%';
DELETE FROM aicg_user_referral WHERE referrer_id LIKE 'test_user_%';
DELETE FROM aicg_user_transaction WHERE user_id LIKE 'test_user_%';
DELETE FROM aicg_user_profile WHERE user_id LIKE 'test_user_%';

-- 创建测试数据
INSERT INTO aicg_user_profile (user_id, username, account_balance, commission_level, valid_invite_count, total_commission, available_commission) VALUES
('test_user_a', 'testUserA', 100.00, 1, 5, 0.00, 0.00),
('test_user_b', 'testUserB', 50.00, 1, 0, 0.00, 0.00),
('test_user_c', 'testUserC', 200.00, 2, 15, 0.00, 0.00),
('test_user_d', 'testUserD', 80.00, 1, 0, 0.00, 0.00);

-- 创建VIP角色关系（用户C为VIP）
INSERT INTO sys_user_role (user_id, role_id) 
SELECT 'test_user_c', id FROM sys_role WHERE role_code = 'VIP' LIMIT 1;

-- 创建邀请关系
INSERT INTO aicg_user_referral (id, referrer_id, referee_id, referral_code, register_time, status, create_time) VALUES
('test_referral_1', 'test_user_a', 'test_user_b', 'INVITE123', NOW(), 1, NOW()),
('test_referral_2', 'test_user_c', 'test_user_d', 'INVITE456', NOW(), 1, NOW());

-- 创建会员订阅订单
INSERT INTO aicg_user_transaction (
    id, user_id, transaction_type, amount, balance_before, balance_after, 
    description, order_status, order_type, product_info, related_order_id, create_time
) VALUES
('test_transaction_1', 'test_user_b', 5, 99.00, 50.00, 50.00,
 'VIP会员月卡 - 1个月', 1, 'membership', 
 '{"membershipLevel": 2, "duration": 1, "planName": "VIP会员月卡"}',
 'ORDER_TEST_123456', NOW()),
('test_transaction_2', 'test_user_d', 5, 299.00, 80.00, 80.00,
 'SVIP会员月卡 - 1个月', 1, 'membership', 
 '{"membershipLevel": 3, "duration": 1, "planName": "SVIP会员月卡"}',
 'ORDER_TEST_789012', NOW());

-- 显示测试数据
SELECT '=== 测试数据准备完成 ===' as info;
SELECT * FROM aicg_user_profile WHERE user_id LIKE 'test_user_%';
SELECT * FROM aicg_user_referral WHERE referrer_id LIKE 'test_user_%';
SELECT * FROM aicg_user_transaction WHERE user_id LIKE 'test_user_%';
```

## 📊 预期测试结果

### 测试用例1：普通用户邀请奖励
- **邀请人**：test_user_a（普通用户，5个邀请）
- **被邀请人**：test_user_b
- **订阅金额**：99元
- **佣金比例**：30%
- **预期奖励**：29.70元

### 测试用例2：VIP用户邀请奖励
- **邀请人**：test_user_c（VIP用户，15个邀请）
- **被邀请人**：test_user_d
- **订阅金额**：299元
- **佣金比例**：45%
- **预期奖励**：134.55元

## ⚠️ 注意事项

1. **数据库字段**：确保所有相关表都有必要的字段
2. **事务处理**：奖励触发在事务中执行，失败会回滚
3. **异常处理**：奖励失败不影响支付主流程
4. **日志记录**：所有操作都有详细的日志记录

## 🔧 调试技巧

1. **查看日志**：关注以 🎯、🔗、💰、✅ 开头的日志
2. **检查数据**：验证邀请关系、用户等级、佣金配置
3. **手动测试**：可以直接调用 `ReferralRewardTriggerService` 方法
4. **SQL验证**：使用SQL查询验证数据变化

这个测试用例覆盖了主要的业务场景，可以有效验证邀请奖励触发机制的正确性。
