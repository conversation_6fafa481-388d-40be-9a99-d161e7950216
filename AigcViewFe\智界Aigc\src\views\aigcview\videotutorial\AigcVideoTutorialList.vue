<template>
  <a-card :bordered="false">
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline" @keyup.enter.native="searchQuery">
        <a-row :gutter="24">
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="是否系列视频">
              <j-dict-select-tag placeholder="请选择是否系列视频" v-model="queryParam.isseries" dictCode="isTrue"/>
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="系列名称">
              <a-input placeholder="请输入系列名称" v-model="queryParam.seriesname"></a-input>
            </a-form-item>
          </a-col>
          <template v-if="toggleSearchStatus">
            <a-col :xl="6" :lg="7" :md="8" :sm="24">
              <a-form-item label="讲师">
                <j-dict-select-tag placeholder="请选择讲师" v-model="queryParam.teacher" dictCode="aigc_video_teacher,teachername,id"/>
              </a-form-item>
            </a-col>
            <a-col :xl="10" :lg="11" :md="12" :sm="24">
              <a-form-item label="点击量">
                <a-input placeholder="请输入最小值" class="query-group-cust" v-model="queryParam.clicknum_begin"></a-input>
                <span class="query-group-split-cust"></span>
                <a-input placeholder="请输入最大值" class="query-group-cust" v-model="queryParam.clicknum_end"></a-input>
              </a-form-item>
            </a-col>
            <a-col :xl="6" :lg="7" :md="8" :sm="24">
              <a-form-item label="标题">
                <a-input placeholder="请输入标题" v-model="queryParam.titile"></a-input>
              </a-form-item>
            </a-col>
            <a-col :xl="6" :lg="7" :md="8" :sm="24">
              <a-form-item label="设置等级">
                <j-dict-select-tag placeholder="请选择设置等级" v-model="queryParam.setlevel" dictCode="setLevel"/>
              </a-form-item>
            </a-col>
            <a-col :xl="10" :lg="11" :md="12" :sm="24">
              <a-form-item label="课程标签">
                <a-input placeholder="请输入最小值" class="query-group-cust" v-model="queryParam.tag_begin"></a-input>
                <span class="query-group-split-cust"></span>
                <a-input placeholder="请输入最大值" class="query-group-cust" v-model="queryParam.tag_end"></a-input>
              </a-form-item>
            </a-col>
            <a-col :xl="10" :lg="11" :md="12" :sm="24">
              <a-form-item label="上传日期">
                <j-date placeholder="请选择开始日期" class="query-group-cust" v-model="queryParam.uptime_begin"></j-date>
                <span class="query-group-split-cust"></span>
                <j-date placeholder="请选择结束日期" class="query-group-cust" v-model="queryParam.uptime_end"></j-date>
              </a-form-item>
            </a-col>
          </template>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <span style="float: left;overflow: hidden;" class="table-page-search-submitButtons">
              <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
              <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
              <a @click="handleToggleSearch" style="margin-left: 8px">
                {{ toggleSearchStatus ? '收起' : '展开' }}
                <a-icon :type="toggleSearchStatus ? 'up' : 'down'"/>
              </a>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <!-- 查询区域-END -->

    <!-- 操作按钮区域 -->
    <div class="table-operator">
      <a-button @click="handleAdd" type="primary" icon="plus">新增</a-button>
      <a-button type="primary" icon="download" @click="handleExportXls('视频教程')">导出</a-button>
      <a-upload name="file" :showUploadList="false" :multiple="false" :headers="tokenHeader" :action="importExcelUrl" @change="handleImportExcel">
        <a-button type="primary" icon="import">导入</a-button>
      </a-upload>
      <!-- 高级查询区域 -->
      <j-super-query :fieldList="superFieldList" ref="superQueryModal" @handleSuperQuery="handleSuperQuery"></j-super-query>
      <a-dropdown v-if="selectedRowKeys.length > 0">
        <a-menu slot="overlay">
          <a-menu-item key="1" @click="batchDel"><a-icon type="delete"/>删除</a-menu-item>
        </a-menu>
        <a-button style="margin-left: 8px"> 批量操作 <a-icon type="down" /></a-button>
      </a-dropdown>
    </div>

    <!-- table区域-begin -->
    <div>
      <div class="ant-alert ant-alert-info" style="margin-bottom: 16px;">
        <i class="anticon anticon-info-circle ant-alert-icon"></i> 已选择 <a style="font-weight: 600">{{ selectedRowKeys.length }}</a>项
        <a style="margin-left: 24px" @click="onClearSelected">清空</a>
      </div>

      <a-table
        ref="table"
        size="middle"
        :scroll="{x:true}"
        bordered
        rowKey="id"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="ipagination"
        :loading="loading"
        :rowSelection="{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}"
        class="j-table-force-nowrap"
        @change="handleTableChange">

        <template slot="htmlSlot" slot-scope="text">
          <div v-html="text"></div>
        </template>
        <template slot="imgSlot" slot-scope="text">
          <span v-if="!text" style="font-size: 12px;font-style: italic;">无图片</span>
          <img v-else :src="getImgView(text)" height="25px" alt="" style="max-width:80px;font-size: 12px;font-style: italic;"/>
        </template>
        <template slot="fileSlot" slot-scope="text">
          <span v-if="!text" style="font-size: 12px;font-style: italic;">无文件</span>
          <a-button
            v-else
            :ghost="true"
            type="primary"
            icon="download"
            size="small"
            @click="downloadFile(text)">
            下载
          </a-button>
        </template>

        <span slot="action" slot-scope="text, record">
          <a @click="handleEdit(record)">编辑</a>

          <a-divider type="vertical" />
          <a-dropdown>
            <a class="ant-dropdown-link">更多 <a-icon type="down" /></a>
            <a-menu slot="overlay">
              <a-menu-item>
                <a @click="handleDetail(record)">详情</a>
              </a-menu-item>
              <a-menu-item>
                <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
                  <a>删除</a>
                </a-popconfirm>
              </a-menu-item>
            </a-menu>
          </a-dropdown>
        </span>

      </a-table>
    </div>

    <aigc-video-tutorial-modal ref="modalForm" @ok="modalFormOk"></aigc-video-tutorial-modal>
  </a-card>
</template>

<script>

  import '@/assets/less/TableExpand.less'
  import { mixinDevice } from '@/utils/mixin'
  import { JeecgListMixin } from '@/mixins/JeecgListMixin'
  import AigcVideoTutorialModal from './modules/AigcVideoTutorialModal'
  import {filterMultiDictText} from '@/components/dict/JDictSelectUtil'

  export default {
    name: 'AigcVideoTutorialList',
    mixins:[JeecgListMixin, mixinDevice],
    components: {
      AigcVideoTutorialModal
    },
    data () {
      return {
        description: '视频教程管理页面',
        // 表头
        columns: [
          {
            title: '#',
            dataIndex: '',
            key:'rowIndex',
            width:60,
            align:"center",
            customRender:function (t,r,index) {
              return parseInt(index)+1;
            }
          },
          {
            title:'是否系列视频',
            align:"center",
            dataIndex: 'isseries_dictText'
          },
          {
            title:'系列名称',
            align:"center",
            dataIndex: 'seriesname'
          },
          {
            title:'视频文件',
            align:"center",
            dataIndex: 'videofile',
            scopedSlots: {customRender: 'fileSlot'}
          },
          {
            title:'讲师',
            align:"center",
            dataIndex: 'teacher_dictText'
          },
          {
            title:'点击量',
            align:"center",
            dataIndex: 'clicknum'
          },
          {
            title:'标题',
            align:"center",
            dataIndex: 'titile'
          },
          {
            title:'设置等级',
            align:"center",
            dataIndex: 'setlevel_dictText'
          },
          {
            title:'课程介绍',
            align:"center",
            dataIndex: 'intro'
          },
          {
            title:'课程标签',
            align:"center",
            dataIndex: 'tag_dictText'
          },
          {
            title:'上传日期',
            align:"center",
            dataIndex: 'uptime',
            customRender:function (text) {
              return !text?"":(text.length>10?text.substr(0,10):text)
            }
          },
          {
            title: '操作',
            dataIndex: 'action',
            align:"center",
            fixed:"right",
            width:147,
            scopedSlots: { customRender: 'action' }
          }
        ],
        url: {
          list: "/videotutorial/aigcVideoTutorial/list",
          delete: "/videotutorial/aigcVideoTutorial/delete",
          deleteBatch: "/videotutorial/aigcVideoTutorial/deleteBatch",
          exportXlsUrl: "/videotutorial/aigcVideoTutorial/exportXls",
          importExcelUrl: "videotutorial/aigcVideoTutorial/importExcel",
          
        },
        dictOptions:{},
        superFieldList:[],
      }
    },
    created() {
    this.getSuperFieldList();
    },
    computed: {
      importExcelUrl: function(){
        return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`;
      },
    },
    methods: {
      initDictConfig(){
      },
      getSuperFieldList(){
        let fieldList=[];
        fieldList.push({type:'string',value:'isseries',text:'是否系列视频',dictCode:'isTrue'})
        fieldList.push({type:'string',value:'seriesname',text:'系列名称',dictCode:''})
        fieldList.push({type:'string',value:'videofile',text:'视频文件',dictCode:''})
        fieldList.push({type:'string',value:'teacher',text:'讲师',dictCode:'aigc_video_teacher,teachername,id'})
        fieldList.push({type:'int',value:'clicknum',text:'点击量',dictCode:''})
        fieldList.push({type:'string',value:'titile',text:'标题',dictCode:''})
        fieldList.push({type:'string',value:'setlevel',text:'设置等级',dictCode:'setLevel'})
        fieldList.push({type:'string',value:'intro',text:'课程介绍',dictCode:''})
        fieldList.push({type:'string',value:'tag',text:'课程标签',dictCode:'tag'})
        fieldList.push({type:'date',value:'uptime',text:'上传日期'})
        this.superFieldList = fieldList
      }
    }
  }
</script>
<style scoped>
  @import '~@assets/less/common.less';
</style>