<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.system.mapper.SysSensitiveWordMapper">

    <!-- 获取所有启用的敏感词 -->
    <select id="getEnabledWords" resultType="java.lang.String">
        SELECT word FROM sys_sensitive_word 
        WHERE status = 1 
        ORDER BY level DESC, hit_count DESC
    </select>

    <!-- 批量插入敏感词 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO sys_sensitive_word (id, word, category, level, status, hit_count, source, create_by, create_time)
        VALUES
        <foreach collection="words" item="item" separator=",">
            (#{item.id}, #{item.word}, #{item.category}, #{item.level}, #{item.status},
             #{item.hitCount}, #{item.source}, #{item.createBy}, #{item.createTime})
        </foreach>
        ON DUPLICATE KEY UPDATE
        update_time = VALUES(create_time),
        update_by = VALUES(create_by)
    </insert>

    <!-- 增加敏感词命中次数 -->
    <update id="incrementHitCount">
        UPDATE sys_sensitive_word 
        SET hit_count = hit_count + 1, update_time = NOW()
        WHERE word = #{word} AND status = 1
    </update>

    <!-- 获取敏感词统计信息 -->
    <select id="getStatistics" resultType="java.util.Map">
        SELECT 
            COUNT(*) as total_words,
            COUNT(CASE WHEN status = 1 THEN 1 END) as enabled_words,
            COUNT(CASE WHEN status = 0 THEN 1 END) as disabled_words,
            SUM(hit_count) as total_hits,
            COUNT(CASE WHEN level = 1 THEN 1 END) as low_risk_words,
            COUNT(CASE WHEN level = 2 THEN 1 END) as medium_risk_words,
            COUNT(CASE WHEN level = 3 THEN 1 END) as high_risk_words,
            COUNT(CASE WHEN source = 'HOUBB' THEN 1 END) as houbb_words,
            COUNT(CASE WHEN source = 'CUSTOM' THEN 1 END) as custom_words
        FROM sys_sensitive_word
    </select>

    <!-- 获取热门敏感词TOP10 -->
    <select id="getTopHitWords" resultType="java.util.Map">
        SELECT 
            word,
            category,
            level,
            hit_count,
            CASE level 
                WHEN 1 THEN '低危'
                WHEN 2 THEN '中危'
                WHEN 3 THEN '高危'
                ELSE '未知'
            END as level_text
        FROM sys_sensitive_word 
        WHERE status = 1 AND hit_count > 0
        ORDER BY hit_count DESC 
        LIMIT #{limit}
    </select>

    <!-- 按分类统计敏感词数量 -->
    <select id="getCategoryStatistics" resultType="java.util.Map">
        SELECT 
            category,
            COUNT(*) as word_count,
            SUM(hit_count) as total_hits,
            COUNT(CASE WHEN status = 1 THEN 1 END) as enabled_count,
            COUNT(CASE WHEN status = 0 THEN 1 END) as disabled_count
        FROM sys_sensitive_word 
        GROUP BY category
        ORDER BY word_count DESC
    </select>

    <!-- 按级别统计敏感词数量 -->
    <select id="getLevelStatistics" resultType="java.util.Map">
        SELECT 
            level,
            CASE level 
                WHEN 1 THEN '低危'
                WHEN 2 THEN '中危'
                WHEN 3 THEN '高危'
                ELSE '未知'
            END as level_text,
            COUNT(*) as word_count,
            SUM(hit_count) as total_hits,
            COUNT(CASE WHEN status = 1 THEN 1 END) as enabled_count
        FROM sys_sensitive_word 
        GROUP BY level
        ORDER BY level DESC
    </select>

    <!-- 检查敏感词是否存在 -->
    <select id="checkWordExists" resultType="org.jeecg.modules.system.entity.SysSensitiveWord">
        SELECT * FROM sys_sensitive_word WHERE word = #{word} LIMIT 1
    </select>

    <!-- 获取敏感词详细信息 -->
    <select id="getWordDetail" resultType="java.util.Map">
        SELECT 
            w.*,
            CASE w.level 
                WHEN 1 THEN '低危'
                WHEN 2 THEN '中危'
                WHEN 3 THEN '高危'
                ELSE '未知'
            END as level_text,
            CASE w.status 
                WHEN 1 THEN '启用'
                WHEN 0 THEN '禁用'
                ELSE '未知'
            END as status_text,
            (SELECT COUNT(*) FROM sys_sensitive_word_hit_log WHERE word_id = w.id) as log_count,
            (SELECT hit_time FROM sys_sensitive_word_hit_log WHERE word_id = w.id ORDER BY hit_time DESC LIMIT 1) as last_hit_time
        FROM sys_sensitive_word w 
        WHERE w.word = #{word}
    </select>

</mapper>
