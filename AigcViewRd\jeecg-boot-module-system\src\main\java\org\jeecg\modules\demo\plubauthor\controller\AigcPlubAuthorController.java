package org.jeecg.modules.demo.plubauthor.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.demo.plubauthor.entity.AigcPlubAuthor;
import org.jeecg.modules.demo.plubauthor.service.IAigcPlubAuthorService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.vo.LoginUser;
import org.apache.shiro.SecurityUtils;
import org.jeecg.modules.system.service.ISysUserService;

 /**
 * @Description: 插件创作者
 * @Author: jeecg-boot
 * @Date:   2025-06-14
 * @Version: V1.0
 */
@Api(tags="插件创作者")
@RestController
@RequestMapping("/plubauthor/aigcPlubAuthor")
@Slf4j
public class AigcPlubAuthorController extends JeecgController<AigcPlubAuthor, IAigcPlubAuthorService> {
	@Autowired
	private IAigcPlubAuthorService aigcPlubAuthorService;

	@Autowired
	private ISysUserService sysUserService;
	
	/**
	 * 分页列表查询
	 *
	 * @param aigcPlubAuthor
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	@AutoLog(value = "插件创作者-分页列表查询")
	@ApiOperation(value="插件创作者-分页列表查询", notes="插件创作者-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(AigcPlubAuthor aigcPlubAuthor,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<AigcPlubAuthor> queryWrapper = QueryGenerator.initQueryWrapper(aigcPlubAuthor, req.getParameterMap());

		// 获取当前登录用户
		LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();

		// 检查用户角色，非admin用户只能查看自己创建的数据
		if (sysUser != null) {
			List<String> userRoles = sysUserService.getRole(sysUser.getUsername());
			boolean isAdmin = userRoles != null && userRoles.contains("admin");

			if (!isAdmin) {
				// 非admin用户只能看到自己创建的数据
				queryWrapper.eq("create_by", sysUser.getUsername());
				log.info("🔒 非admin用户 {} 只能查看自己的作者数据", sysUser.getUsername());
			} else {
				log.info("🔓 admin用户 {} 可以查看所有作者数据", sysUser.getUsername());
			}
		}

		Page<AigcPlubAuthor> page = new Page<AigcPlubAuthor>(pageNo, pageSize);
		IPage<AigcPlubAuthor> pageList = aigcPlubAuthorService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param aigcPlubAuthor
	 * @return
	 */
	@AutoLog(value = "插件创作者-添加")
	@ApiOperation(value="插件创作者-添加", notes="插件创作者-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody AigcPlubAuthor aigcPlubAuthor) {
		aigcPlubAuthorService.save(aigcPlubAuthor);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param aigcPlubAuthor
	 * @return
	 */
	@AutoLog(value = "插件创作者-编辑")
	@ApiOperation(value="插件创作者-编辑", notes="插件创作者-编辑")
	@PutMapping(value = "/edit")
	public Result<?> edit(@RequestBody AigcPlubAuthor aigcPlubAuthor) {
		aigcPlubAuthorService.updateById(aigcPlubAuthor);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "插件创作者-通过id删除")
	@ApiOperation(value="插件创作者-通过id删除", notes="插件创作者-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		aigcPlubAuthorService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "插件创作者-批量删除")
	@ApiOperation(value="插件创作者-批量删除", notes="插件创作者-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.aigcPlubAuthorService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "插件创作者-通过id查询")
	@ApiOperation(value="插件创作者-通过id查询", notes="插件创作者-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		AigcPlubAuthor aigcPlubAuthor = aigcPlubAuthorService.getById(id);
		if(aigcPlubAuthor==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(aigcPlubAuthor);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param aigcPlubAuthor
    */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, AigcPlubAuthor aigcPlubAuthor) {
        return super.exportXls(request, aigcPlubAuthor, AigcPlubAuthor.class, "插件创作者");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, AigcPlubAuthor.class);
    }

    /**
     * 更新指定作者的插件数
     *
     * @param id 作者ID
     * @return
     */
    @AutoLog(value = "插件创作者-更新插件数")
    @ApiOperation(value="插件创作者-更新插件数", notes="根据插件商城数据更新指定作者的插件数")
    @PostMapping(value = "/updatePluginCount")
    public Result<?> updatePluginCount(@RequestParam(name="id",required=true) String id) {
        try {
            boolean success = aigcPlubAuthorService.updateAuthorPluginCount(id);
            if (success) {
                return Result.OK("更新插件数成功！");
            } else {
                return Result.error("更新插件数失败，请检查作者ID是否存在");
            }
        } catch (Exception e) {
            log.error("更新插件数异常: {}", e.getMessage(), e);
            return Result.error("更新插件数异常: " + e.getMessage());
        }
    }

    /**
     * 批量更新所有作者的插件数
     *
     * @return
     */
    @AutoLog(value = "插件创作者-批量更新插件数")
    @ApiOperation(value="插件创作者-批量更新插件数", notes="批量更新所有作者的插件数")
    @PostMapping(value = "/updateAllPluginCounts")
    public Result<?> updateAllPluginCounts() {
        try {
            int updateCount = aigcPlubAuthorService.updateAllAuthorPluginCounts();
            return Result.OK("批量更新成功，共更新 " + updateCount + " 个作者的插件数");
        } catch (Exception e) {
            log.error("批量更新插件数异常: {}", e.getMessage(), e);
            return Result.error("批量更新插件数异常: " + e.getMessage());
        }
    }

}
