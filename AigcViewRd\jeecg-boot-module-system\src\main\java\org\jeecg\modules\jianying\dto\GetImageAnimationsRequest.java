package org.jeecg.modules.jianying.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 获取图片动画请求参数
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class GetImageAnimationsRequest extends BaseJianyingRequest {
    
    @ApiModelProperty(value = "默认0 全部 1 vip 2 免费", example = "0")
    @JsonProperty("mode")
    private Integer zjMode;

    @ApiModelProperty(value = "三个值，默认in：in 入场 out 出场 group 组合", example = "in")
    @JsonProperty("type")
    private String zjType;
    
    @Override
    public String getSummary() {
        return "GetImageAnimationsRequest{" +
               "accessKey=" + (getAccessKey() != null && getAccessKey().length() > 10 ? 
                              getAccessKey().substring(0, 10) + "***" : "null") +
               ", mode=" + zjMode +
               ", type=" + zjType +
               "}";
    }
}
