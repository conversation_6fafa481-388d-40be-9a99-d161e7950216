#!/usr/bin/env node

/**
 * 版本号一致性检查工具
 * 检查项目中所有版本号是否与package.json保持一致
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 检查版本号一致性...\n');

try {
    // 读取 package.json 中的版本号
    const packageJsonPath = path.join(__dirname, '../package.json');
    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
    const packageVersion = packageJson.version;
    
    console.log(`📦 package.json 版本号: ${packageVersion}`);
    
    let hasInconsistency = false;
    const issues = [];
    
    // 检查函数
    function checkFile(filePath, description, checkFunction) {
        if (fs.existsSync(filePath)) {
            try {
                const content = fs.readFileSync(filePath, 'utf8');
                const result = checkFunction(content, packageVersion);
                
                if (result.consistent) {
                    console.log(`✅ ${description}: 版本号一致`);
                } else {
                    console.log(`❌ ${description}: 发现不一致`);
                    result.issues.forEach(issue => {
                        console.log(`   - ${issue}`);
                        issues.push(`${description}: ${issue}`);
                    });
                    hasInconsistency = true;
                }
            } catch (error) {
                console.log(`⚠️  ${description}: 检查时出错 - ${error.message}`);
            }
        } else {
            console.log(`⚠️  ${description}: 文件不存在`);
        }
    }
    
    // 1. 检查主进程文件
    checkFile(
        path.join(__dirname, '../src/main/main.js'),
        '主进程文件',
        (content, version) => {
            // 检查是否正确使用 app.getVersion()
            const hasAppGetVersion = content.includes('app.getVersion()');
            const hasHardcodedVersion = /['"`]\d+\.\d+\.\d+['"`]/.test(content);
            
            const issues = [];
            if (!hasAppGetVersion) {
                issues.push('未找到 app.getVersion() 调用');
            }
            if (hasHardcodedVersion) {
                const matches = content.match(/['"`](\d+\.\d+\.\d+)['"`]/g);
                if (matches) {
                    matches.forEach(match => {
                        issues.push(`发现硬编码版本号: ${match}`);
                    });
                }
            }
            
            return {
                consistent: issues.length === 0,
                issues
            };
        }
    );
    
    // 2. 检查渲染进程文件
    checkFile(
        path.join(__dirname, '../src/renderer/scripts/main.js'),
        '渲染进程主文件',
        (content, version) => {
            // 检查是否有硬编码的版本号
            const hardcodedVersions = content.match(/版本 v\d+\.\d+\.\d+/g);
            const issues = [];
            
            if (hardcodedVersions) {
                hardcodedVersions.forEach(match => {
                    if (!match.includes('${currentVersion}')) {
                        issues.push(`发现硬编码版本号: ${match}`);
                    }
                });
            }
            
            return {
                consistent: issues.length === 0,
                issues
            };
        }
    );
    
    // 3. 检查HTML文件
    checkFile(
        path.join(__dirname, '../src/renderer/index.html'),
        'HTML文件',
        (content, version) => {
            // 检查是否有硬编码的版本号
            const hardcodedVersions = content.match(/v\d+\.\d+\.\d+/g);
            const issues = [];
            
            if (hardcodedVersions) {
                hardcodedVersions.forEach(match => {
                    // 检查是否在动态更新的元素中
                    const isInDynamicElement = content.includes(`id="current-version"`) || 
                                             content.includes(`id="app-version"`);
                    if (!isInDynamicElement || match !== `v${version}`) {
                        // 允许默认值为当前版本
                        if (match !== `v${version}`) {
                            issues.push(`发现可能的硬编码版本号: ${match}`);
                        }
                    }
                });
            }
            
            return {
                consistent: issues.length === 0,
                issues
            };
        }
    );
    
    // 4. 检查设置文件
    checkFile(
        path.join(__dirname, '../src/renderer/scripts/settings.js'),
        '设置页面脚本',
        (content, version) => {
            // 检查是否正确使用动态版本获取
            const hasVersionUpdate = content.includes('data.currentVersion');
            const issues = [];
            
            if (!hasVersionUpdate) {
                issues.push('未找到动态版本号更新逻辑');
            }
            
            return {
                consistent: issues.length === 0,
                issues
            };
        }
    );
    
    console.log('\n' + '='.repeat(50));
    
    if (hasInconsistency) {
        console.log('❌ 发现版本号不一致问题：');
        issues.forEach(issue => {
            console.log(`   - ${issue}`);
        });
        console.log('\n💡 建议：');
        console.log('   1. 使用 app.getVersion() 获取版本号');
        console.log('   2. 在渲染进程中通过 IPC 获取版本号');
        console.log('   3. 避免硬编码版本号');
        console.log('   4. 运行 node scripts/update-version.js <版本号> 来统一更新');
        
        process.exit(1);
    } else {
        console.log('✅ 所有版本号检查通过！');
        console.log('\n📝 当前配置：');
        console.log('   - package.json 版本号作为唯一数据源');
        console.log('   - 主进程使用 app.getVersion() 获取版本号');
        console.log('   - 渲染进程通过 IPC 动态获取版本号');
        console.log('   - 所有显示位置都使用动态版本号');
    }
    
} catch (error) {
    console.error('❌ 检查过程中发生错误:', error.message);
    process.exit(1);
}
