{"version": 3, "file": "nsisUtil.js", "sourceRoot": "", "sources": ["../../../src/targets/nsis/nsisUtil.ts"], "names": [], "mappings": ";;;AAAA,+CAAwC;AAExC,mDAAsE;AACtE,4CAAuD;AACvD,6BAA4B;AAC5B,wDAAwD;AAExD,kCAAiC;AACjC,6BAA4B;AAGf,QAAA,gBAAgB,GAAG,IAAA,6BAAe,EAAC,MAAM,CAAC,CAAA;AAE1C,QAAA,iBAAiB,GAAG,CAAC,GAAG,EAAE;IACrC,IAAI,QAAuC,CAAA;IAC3C,MAAM,OAAO,GAAG,IAAI,OAAO,CAAc,OAAO,CAAC,EAAE,CAAC,CAAC,QAAQ,GAAG,OAAO,CAAC,CAAC,CAAA;IACzE,OAAO;QACL,IAAI,EAAE,CAAC,QAAuC,EAAmB,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC;QAC1F,OAAO,EAAE,CAAC,OAAoB,EAAO,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC;KAC1D,CAAA;AACH,CAAC,CAAC,EAAE,CAAA;AAEG,MAAM,SAAS,GAAG,GAAG,EAAE;IAC5B,MAAM,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC,yBAAyB,CAAA;IACpD,IAAI,MAAM,IAAI,IAAI,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACxC,OAAO,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,CAAA;IACvC,CAAC;IACD,OAAO,yBAAiB,CAAC,IAAI,CAAC,CAAC,OAAoB,EAAE,EAAE;QACrD,IAAI,OAAO,CAAC,gBAAgB,EAAE,CAAC;YAC7B,MAAM,EAAE,QAAQ,EAAE,GAAG,EAAE,OAAO,EAAE,GAAG,OAAO,CAAC,gBAAgB,CAAA;YAC3D,IAAI,QAAQ,IAAI,GAAG,EAAE,CAAC;gBACpB,MAAM,aAAa,GAAG,OAAO,IAAI,QAAQ,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;gBACtD,OAAO,IAAA,iCAAmB,EAAC,MAAM,EAAE,aAAa,EAAE,GAAG,EAAE,QAAQ,CAAC,CAAA;YAClE,CAAC;QACH,CAAC;QACD,kGAAkG;QAClG,uCAAuC;QACvC,OAAO,IAAA,2BAAa,EAAC,MAAM,EAAE,SAAS,EAAE,0FAA0F,CAAC,CAAA;IACrI,CAAC,CAAC,CAAA;AACJ,CAAC,CAAA;AAjBY,QAAA,SAAS,aAiBrB;AAOD,MAAa,gBAAgB;IAO3B,YAA6B,aAAgC;QAAhC,kBAAa,GAAb,aAAa,CAAmB;QAN5C,iBAAY,GAAG,IAAI,GAAG,EAAiC,CAAA;QACvD,mBAAc,GAAG,IAAI,GAAG,EAA4B,CAAA;QAErE,eAAe;QACf,aAAQ,GAAG,CAAC,CAAA;IAEoD,CAAC;IAEjE,KAAK,CAAC,QAAQ,CAAC,IAAU,EAAE,MAAkB;QAC3C,IAAI,aAAa,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,CAAA;QAC/C,IAAI,aAAa,IAAI,IAAI,EAAE,CAAC;YAC1B,MAAM,SAAS,GAAG,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAE,CAAA;YACzC,aAAa,GAAG,IAAI,CAAC,aAAa;iBAC/B,IAAI,CAAC,SAAS,EAAE,MAAM,CAAC;iBACvB,IAAI,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,eAAe,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;iBACnD,IAAI,CAAC,KAAK,EAAC,QAAQ,EAAC,EAAE,CAAC,CAAC;gBACvB,QAAQ;gBACR,YAAY,EAAE,MAAM,IAAA,YAAO,EAAC,SAAS,CAAC;aACvC,CAAC,CAAC,CAAA;YACL,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,EAAE,aAAa,CAAC,CAAA;QAC5C,CAAC;QAED,MAAM,MAAM,GAAG,MAAM,aAAa,CAAA;QAClC,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,GAAG,MAAM,CAAA;QACjC,IAAI,MAAM,CAAC,cAAc,EAAE,CAAC;YAC1B,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC,CAAA;QACtC,CAAC;aAAM,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;YAC1C,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;QACrC,CAAC;QACD,OAAO,MAAM,CAAA;IACf,CAAC;IAED,KAAK,CAAC,WAAW;QACf,IAAI,EAAE,IAAI,CAAC,QAAQ,GAAG,CAAC,EAAE,CAAC;YACxB,OAAM;QACR,CAAC;QAED,MAAM,aAAa,GAAkB,EAAE,CAAA;QACvC,KAAK,MAAM,CAAC,IAAI,EAAE,QAAQ,CAAC,IAAI,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,EAAE,CAAC;YAC7D,IAAI,QAAQ,EAAE,CAAC;gBACb,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YAC/B,CAAC;QACH,CAAC;QAED,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,CAAA;IAC3D,CAAC;CACF;AA/CD,4CA+CC;AAED,MAAa,iBAAiB;IAA9B;QACmB,WAAM,GAAG,IAAI,GAAG,EAAwB,CAAA;IAiC3D,CAAC;IA/BC,IAAI,CAAC,SAAiB,EAAE,MAAkB;QACxC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,mBAAmB,EAAE,CAAC;YACxD,OAAO,OAAO,CAAC,OAAO,EAAE,CAAA;QAC1B,CAAC;QAED,IAAI,mBAAmB,GAAG,MAAM,CAAC,OAAO,CAAC,iBAAiB,CAAA;QAC1D,IAAI,mBAAmB,KAAK,KAAK,IAAI,MAAM,CAAC,OAAO,CAAC,UAAU,KAAK,IAAI,EAAE,CAAC;YACxE,mBAAmB,GAAG,IAAI,CAAA;YAC1B,kBAAG,CAAC,IAAI,CAAC,+EAA+E,CAAC,CAAA;QAC3F,CAAC;QAED,IAAI,mBAAmB,KAAK,KAAK,EAAE,CAAC;YAClC,OAAO,OAAO,CAAC,OAAO,EAAE,CAAA;QAC1B,CAAC;QAED,IAAI,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,SAAS,CAAC,CAAA;QACxC,IAAI,OAAO,IAAI,IAAI,EAAE,CAAC;YACpB,OAAO,OAAO,CAAA;QAChB,CAAC;QAED,OAAO,GAAG,IAAA,iBAAS,GAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE;YAC9B,MAAM,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,WAAW,EAAE,aAAa,CAAC,CAAA;YAChE,MAAM,OAAO,GAAG,IAAA,aAAQ,EAAC,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,aAAa,CAAC,EAAE,OAAO,EAAE,KAAK,CAAC,CAAA;YACtE,IAAI,MAAM,CAAC,QAAQ,CAAC,4BAA4B,CAAC,qBAAqB,KAAK,KAAK,EAAE,CAAC;gBACjF,OAAO,OAAO,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAA;YAC1D,CAAC;YACD,OAAO,OAAO,CAAA;QAChB,CAAC,CAAC,CAAA;QACF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,SAAS,EAAE,OAAO,CAAC,CAAA;QACnC,OAAO,OAAO,CAAA;IAChB,CAAC;CACF;AAlCD,8CAkCC;AAED,MAAM,YAAY;IAIhB,YAAY,MAAc;QACxB,IAAI,CAAC,OAAO,GAAG,MAAM,CAAA;QACrB,IAAI,CAAC,SAAS,GAAG,CAAC,CAAA;IACpB,CAAC;IAED,IAAI,MAAM;QACR,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,CAAA;IAC5B,CAAC;IAED,IAAI,QAAQ;QACV,OAAO,IAAI,CAAC,SAAS,CAAA;IACvB,CAAC;IAED,KAAK,CAAC,SAAwB;QAC5B,IAAI,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;YACtE,IAAI,CAAC,SAAS,IAAI,SAAS,CAAC,MAAM,CAAA;YAClC,OAAO,IAAI,CAAA;QACb,CAAC;QACD,OAAO,KAAK,CAAA;IACd,CAAC;IAED,IAAI,CAAC,MAAc;QACjB,IAAI,CAAC,SAAS,IAAI,MAAM,CAAA;IAC1B,CAAC;IAED,KAAK,CAAC,IAAY;QAChB,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,CAAA;QAC1E,IAAI,CAAC,SAAS,IAAI,IAAI,CAAA;QACtB,OAAO,KAAK,CAAA;IACd,CAAC;IAED,MAAM;QACJ,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAA;QACpF,IAAI,CAAC,SAAS,IAAI,CAAC,CAAA;QACnB,OAAO,KAAK,CAAA;IACd,CAAC;IAED,MAAM;QACJ,OAAO,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,EAAE,CAAC,CAAA;IAC9C,CAAC;IAED,MAAM,CAAC,MAAc;QACnB,IAAI,KAAK,GAAG,EAAE,CAAA;QACd,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAChC,MAAM,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC,CAAA;YAC1C,IAAI,CAAC,KAAK,IAAI,EAAE,CAAC;gBACf,MAAK;YACP,CAAC;YACD,KAAK,IAAI,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,CAAA;QACjC,CAAC;QACD,IAAI,CAAC,SAAS,IAAI,MAAM,CAAA;QACxB,OAAO,KAAK,CAAA;IACd,CAAC;CACF;AAED,MAAa,iBAAiB;IAC5B,uCAAuC;IACvC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,aAAqB,EAAE,eAAuB;QAC9D,MAAM,MAAM,GAAG,MAAM,EAAE,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAA;QAC/C,MAAM,MAAM,GAAG,IAAI,YAAY,CAAC,MAAM,CAAC,CAAA;QACvC,mBAAmB;QACnB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,EAAE,CAAC;YAChC,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAA;QAC5C,CAAC;QACD,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;QACf,WAAW;QACX,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAA;QAC9C,oBAAoB;QACpB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,EAAE,CAAC;YAC5C,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAA;QAC5C,CAAC;QACD,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;QACd,MAAM,gBAAgB,GAAG,MAAM,CAAC,MAAM,EAAE,CAAA;QACxC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;QACf,MAAM,oBAAoB,GAAG,MAAM,CAAC,MAAM,EAAE,CAAA;QAC5C,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;QACd,MAAM,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAA;QACjC,uBAAuB;QACvB,IAAI,UAAU,GAAG,CAAC,CAAA;QAClB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,gBAAgB,EAAE,CAAC,EAAE,EAAE,CAAC;YAC1C,MAAM,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAA;YAC7B,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;YACd,MAAM,OAAO,GAAG,MAAM,CAAC,MAAM,EAAE,CAAA;YAC/B,MAAM,UAAU,GAAG,MAAM,CAAC,MAAM,EAAE,CAAA;YAClC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;YACf,QAAQ,IAAI,EAAE,CAAC;gBACb,KAAK,OAAO,CAAC;gBACb,KAAK,QAAQ,CAAC;gBACd,KAAK,OAAO,CAAC;gBACb,KAAK,OAAO,CAAC,CAAC,CAAC;oBACb,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,UAAU,GAAG,OAAO,EAAE,UAAU,CAAC,CAAA;oBACvD,MAAK;gBACP,CAAC;gBACD,OAAO,CAAC,CAAC,CAAC;oBACR,IAAI,UAAU,KAAK,CAAC,IAAI,OAAO,KAAK,CAAC,EAAE,CAAC;wBACtC,MAAM,IAAI,KAAK,CAAC,uBAAuB,GAAG,IAAI,GAAG,IAAI,CAAC,CAAA;oBACxD,CAAC;oBACD,MAAK;gBACP,CAAC;YACH,CAAC;QACH,CAAC;QACD,MAAM,UAAU,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC,EAAE,UAAU,CAAC,CAAA;QACjD,MAAM,QAAQ,GAAG,MAAM,CAAC,MAAM,GAAG,UAAU,CAAA;QAC3C,MAAM,UAAU,GAAG,IAAI,YAAY,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,EAAE,UAAU,GAAG,QAAQ,CAAC,CAAC,CAAA;QACvF,MAAM,aAAa,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAA;QACtH,UAAU,CAAC,MAAM,EAAE,CAAA,CAAC,IAAI;QACxB,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,aAAa,CAAC,EAAE,CAAC;YACrC,MAAM,IAAI,KAAK,CAAC,oBAAoB,CAAC,CAAA;QACvC,CAAC;QACD,UAAU,CAAC,MAAM,EAAE,CAAA,CAAC,IAAI;QACxB,IAAI,QAAQ,KAAK,UAAU,CAAC,MAAM,EAAE,EAAE,CAAC;YACrC,MAAM,IAAI,KAAK,CAAC,gBAAgB,CAAC,CAAA;QACnC,CAAC;QAED,IAAI,WAAW,GAAG,IAAI,CAAA;QACtB,OAAO,IAAI,EAAE,CAAC;YACZ,IAAI,IAAI,GAAG,UAAU,CAAC,MAAM,EAAE,CAAA;YAC9B,MAAM,UAAU,GAAG,CAAC,IAAI,GAAG,UAAU,CAAC,KAAK,CAAC,CAAA;YAC5C,IAAI,GAAG,IAAI,GAAG,UAAU,CAAA;YACxB,IAAI,IAAI,KAAK,CAAC,IAAI,UAAU,CAAC,QAAQ,GAAG,IAAI,GAAG,UAAU,CAAC,MAAM,IAAI,UAAU,CAAC,QAAQ,IAAI,UAAU,CAAC,MAAM,EAAE,CAAC;gBAC7G,MAAK;YACP,CAAC;YACD,IAAI,MAAM,GAAG,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;YACnC,IAAI,UAAU,EAAE,CAAC;gBACf,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAA;YACtC,CAAC;YACD,MAAM,WAAW,GAAG,IAAI,YAAY,CAAC,MAAM,CAAC,CAAA;YAC5C,WAAW,CAAC,MAAM,EAAE,CAAA,CAAC,IAAI;YACzB,IAAI,WAAW,CAAC,KAAK,CAAC,aAAa,CAAC,EAAE,CAAC;gBACrC,IAAI,WAAW,EAAE,CAAC;oBAChB,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAA;gBAC3C,CAAC;gBACD,WAAW,GAAG,MAAM,CAAA;YACtB,CAAC;QACH,CAAC;QACD,IAAI,CAAC,WAAW,EAAE,CAAC;YACjB,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAA;QAC3C,CAAC;QACD,MAAM,EAAE,CAAC,SAAS,CAAC,eAAe,EAAE,UAAU,CAAC,CAAA;QAC/C,MAAM,EAAE,CAAC,UAAU,CAAC,eAAe,EAAE,WAAW,CAAC,CAAA;IACnD,CAAC;CACF;AAtFD,8CAsFC", "sourcesContent": ["import { Arch, log } from \"builder-util\"\nimport { PackageFileInfo } from \"builder-util-runtime\"\nimport { getBinFromUrl, getBinFromCustomLoc } from \"../../binDownload\"\nimport { copyFile, dirSize } from \"builder-util/out/fs\"\nimport * as path from \"path\"\nimport { getTemplatePath } from \"../../util/pathManager\"\nimport { NsisTarget } from \"./NsisTarget\"\nimport * as fs from \"fs/promises\"\nimport * as zlib from \"zlib\"\nimport { NsisOptions } from \"./nsisOptions\"\n\nexport const nsisTemplatesDir = getTemplatePath(\"nsis\")\n\nexport const NsisTargetOptions = (() => {\n  let _resolve: (options: NsisOptions) => any\n  const promise = new Promise<NsisOptions>(resolve => (_resolve = resolve))\n  return {\n    then: (callback: (options: NsisOptions) => any): Promise<string> => promise.then(callback),\n    resolve: (options: NsisOptions): any => _resolve(options),\n  }\n})()\n\nexport const NSIS_PATH = () => {\n  const custom = process.env.ELECTRON_BUILDER_NSIS_DIR\n  if (custom != null && custom.length > 0) {\n    return Promise.resolve(custom.trim())\n  }\n  return NsisTargetOptions.then((options: NsisOptions) => {\n    if (options.customNsisBinary) {\n      const { checksum, url, version } = options.customNsisBinary\n      if (checksum && url) {\n        const binaryVersion = version || checksum.substr(0, 8)\n        return getBinFromCustomLoc(\"nsis\", binaryVersion, url, checksum)\n      }\n    }\n    // Warning: Don't use v3.0.4.2 - https://github.com/electron-userland/electron-builder/issues/6334\n    // noinspection SpellCheckingInspection\n    return getBinFromUrl(\"nsis\", \"3.0.4.1\", \"VKMiizYdmNdJOWpRGz4trl4lD++BvYP2irAXpMilheUP0pc93iKlWAoP843Vlraj8YG19CVn0j+dCo/hURz9+Q==\")\n  })\n}\n\nexport interface PackArchResult {\n  fileInfo: PackageFileInfo\n  unpackedSize: number\n}\n\nexport class AppPackageHelper {\n  private readonly archToResult = new Map<Arch, Promise<PackArchResult>>()\n  private readonly infoToIsDelete = new Map<PackageFileInfo, boolean>()\n\n  /** @private */\n  refCount = 0\n\n  constructor(private readonly elevateHelper: CopyElevateHelper) {}\n\n  async packArch(arch: Arch, target: NsisTarget): Promise<PackArchResult> {\n    let resultPromise = this.archToResult.get(arch)\n    if (resultPromise == null) {\n      const appOutDir = target.archs.get(arch)!\n      resultPromise = this.elevateHelper\n        .copy(appOutDir, target)\n        .then(() => target.buildAppPackage(appOutDir, arch))\n        .then(async fileInfo => ({\n          fileInfo,\n          unpackedSize: await dirSize(appOutDir),\n        }))\n      this.archToResult.set(arch, resultPromise)\n    }\n\n    const result = await resultPromise\n    const { fileInfo: info } = result\n    if (target.isWebInstaller) {\n      this.infoToIsDelete.set(info, false)\n    } else if (!this.infoToIsDelete.has(info)) {\n      this.infoToIsDelete.set(info, true)\n    }\n    return result\n  }\n\n  async finishBuild(): Promise<any> {\n    if (--this.refCount > 0) {\n      return\n    }\n\n    const filesToDelete: Array<string> = []\n    for (const [info, isDelete] of this.infoToIsDelete.entries()) {\n      if (isDelete) {\n        filesToDelete.push(info.path)\n      }\n    }\n\n    await Promise.all(filesToDelete.map(it => fs.unlink(it)))\n  }\n}\n\nexport class CopyElevateHelper {\n  private readonly copied = new Map<string, Promise<any>>()\n\n  copy(appOutDir: string, target: NsisTarget): Promise<any> {\n    if (!target.packager.info.framework.isCopyElevateHelper) {\n      return Promise.resolve()\n    }\n\n    let isPackElevateHelper = target.options.packElevateHelper\n    if (isPackElevateHelper === false && target.options.perMachine === true) {\n      isPackElevateHelper = true\n      log.warn(\"`packElevateHelper = false` is ignored, because `perMachine` is set to `true`\")\n    }\n\n    if (isPackElevateHelper === false) {\n      return Promise.resolve()\n    }\n\n    let promise = this.copied.get(appOutDir)\n    if (promise != null) {\n      return promise\n    }\n\n    promise = NSIS_PATH().then(it => {\n      const outFile = path.join(appOutDir, \"resources\", \"elevate.exe\")\n      const promise = copyFile(path.join(it, \"elevate.exe\"), outFile, false)\n      if (target.packager.platformSpecificBuildOptions.signAndEditExecutable !== false) {\n        return promise.then(() => target.packager.sign(outFile))\n      }\n      return promise\n    })\n    this.copied.set(appOutDir, promise)\n    return promise\n  }\n}\n\nclass BinaryReader {\n  private readonly _buffer: Buffer\n  private _position: number\n\n  constructor(buffer: Buffer) {\n    this._buffer = buffer\n    this._position = 0\n  }\n\n  get length(): number {\n    return this._buffer.length\n  }\n\n  get position(): number {\n    return this._position\n  }\n\n  match(signature: Array<number>): boolean {\n    if (signature.every((v, i) => this._buffer[this._position + i] === v)) {\n      this._position += signature.length\n      return true\n    }\n    return false\n  }\n\n  skip(offset: number) {\n    this._position += offset\n  }\n\n  bytes(size: number): Buffer {\n    const value = this._buffer.subarray(this._position, this._position + size)\n    this._position += size\n    return value\n  }\n\n  uint16(): number {\n    const value = this._buffer[this._position] | (this._buffer[this._position + 1] << 8)\n    this._position += 2\n    return value\n  }\n\n  uint32(): number {\n    return this.uint16() | (this.uint16() << 16)\n  }\n\n  string(length: number): string {\n    let value = \"\"\n    for (let i = 0; i < length; i++) {\n      const c = this._buffer[this._position + i]\n      if (c === 0x00) {\n        break\n      }\n      value += String.fromCharCode(c)\n    }\n    this._position += length\n    return value\n  }\n}\n\nexport class UninstallerReader {\n  // noinspection SpellCheckingInspection\n  static async exec(installerPath: string, uninstallerPath: string) {\n    const buffer = await fs.readFile(installerPath)\n    const reader = new BinaryReader(buffer)\n    // IMAGE_DOS_HEADER\n    if (!reader.match([0x4d, 0x5a])) {\n      throw new Error(\"Invalid 'MZ' signature.\")\n    }\n    reader.skip(58)\n    // e_lfanew\n    reader.skip(reader.uint32() - reader.position)\n    // IMAGE_FILE_HEADER\n    if (!reader.match([0x50, 0x45, 0x00, 0x00])) {\n      throw new Error(\"Invalid 'PE' signature.\")\n    }\n    reader.skip(2)\n    const numberOfSections = reader.uint16()\n    reader.skip(12)\n    const sizeOfOptionalHeader = reader.uint16()\n    reader.skip(2)\n    reader.skip(sizeOfOptionalHeader)\n    // IMAGE_SECTION_HEADER\n    let nsisOffset = 0\n    for (let i = 0; i < numberOfSections; i++) {\n      const name = reader.string(8)\n      reader.skip(8)\n      const rawSize = reader.uint32()\n      const rawPointer = reader.uint32()\n      reader.skip(16)\n      switch (name) {\n        case \".text\":\n        case \".rdata\":\n        case \".data\":\n        case \".rsrc\": {\n          nsisOffset = Math.max(rawPointer + rawSize, nsisOffset)\n          break\n        }\n        default: {\n          if (rawPointer !== 0 && rawSize !== 0) {\n            throw new Error(\"Unsupported section '\" + name + \"'.\")\n          }\n          break\n        }\n      }\n    }\n    const executable = buffer.subarray(0, nsisOffset)\n    const nsisSize = buffer.length - nsisOffset\n    const nsisReader = new BinaryReader(buffer.subarray(nsisOffset, nsisOffset + nsisSize))\n    const nsisSignature = [0xef, 0xbe, 0xad, 0xde, 0x4e, 0x75, 0x6c, 0x6c, 0x73, 0x6f, 0x66, 0x74, 0x49, 0x6e, 0x73, 0x74]\n    nsisReader.uint32() // ?\n    if (!nsisReader.match(nsisSignature)) {\n      throw new Error(\"Invalid signature.\")\n    }\n    nsisReader.uint32() // ?\n    if (nsisSize !== nsisReader.uint32()) {\n      throw new Error(\"Size mismatch.\")\n    }\n\n    let innerBuffer = null\n    while (true) {\n      let size = nsisReader.uint32()\n      const compressed = (size & 0x80000000) !== 0\n      size = size & 0x7fffffff\n      if (size === 0 || nsisReader.position + size > nsisReader.length || nsisReader.position >= nsisReader.length) {\n        break\n      }\n      let buffer = nsisReader.bytes(size)\n      if (compressed) {\n        buffer = zlib.inflateRawSync(buffer)\n      }\n      const innerReader = new BinaryReader(buffer)\n      innerReader.uint32() // ?\n      if (innerReader.match(nsisSignature)) {\n        if (innerBuffer) {\n          throw new Error(\"Multiple inner blocks.\")\n        }\n        innerBuffer = buffer\n      }\n    }\n    if (!innerBuffer) {\n      throw new Error(\"Inner block not found.\")\n    }\n    await fs.writeFile(uninstallerPath, executable)\n    await fs.appendFile(uninstallerPath, innerBuffer)\n  }\n}\n"]}