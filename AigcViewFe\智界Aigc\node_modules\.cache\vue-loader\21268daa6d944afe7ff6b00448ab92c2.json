{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\workflow\\components\\AgentManagement.vue?vue&type=template&id=66227f41&scoped=true&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\workflow\\components\\AgentManagement.vue", "mtime": 1754512044865}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\n<div class=\"agent-management\">\n  <!-- 筛选和搜索 -->\n  <div class=\"market-filters\">\n    <div class=\"filter-row\">\n      <!-- 搜索框 -->\n      <div class=\"search-box\">\n        <div class=\"search-wrapper\">\n          <a-icon type=\"search\" class=\"search-icon\" />\n          <a-input\n            v-model=\"searchQuery\"\n            placeholder=\"搜索智能体名称、描述...\"\n            size=\"large\"\n            @pressEnter=\"handleSearch\"\n            @input=\"handleSearch\"\n            class=\"search-input\"\n          />\n          <a-icon\n            v-if=\"searchQuery\"\n            type=\"close-circle\"\n            class=\"clear-icon\"\n            @click=\"clearSearch\"\n          />\n        </div>\n      </div>\n\n      <!-- 筛选器 -->\n      <div class=\"filter-controls\">\n        <!-- 审核状态筛选 -->\n        <div class=\"filter-group\">\n          <div class=\"filter-buttons\">\n            <button\n              class=\"filter-btn\"\n              :class=\"{ 'active': auditStatusFilter === '' }\"\n              @click=\"setAuditStatusFilter('')\"\n            >\n              <a-icon type=\"appstore\" />\n              全部\n            </button>\n            <button\n              class=\"filter-btn pending\"\n              :class=\"{ 'active': auditStatusFilter === '1' }\"\n              @click=\"setAuditStatusFilter('1')\"\n            >\n              <a-icon type=\"clock-circle\" />\n              待审核\n            </button>\n            <button\n              class=\"filter-btn approved\"\n              :class=\"{ 'active': auditStatusFilter === '2' }\"\n              @click=\"setAuditStatusFilter('2')\"\n            >\n              <a-icon type=\"check-circle\" />\n              已通过\n            </button>\n            <button\n              class=\"filter-btn rejected\"\n              :class=\"{ 'active': auditStatusFilter === '3' }\"\n              @click=\"setAuditStatusFilter('3')\"\n            >\n              <a-icon type=\"close-circle\" />\n              已拒绝\n            </button>\n          </div>\n        </div>\n\n        <!-- 排序控件 -->\n        <div class=\"filter-group\">\n          <div class=\"sort-controls\">\n            <span class=\"sort-label\">排序：</span>\n            <a-select\n              v-model=\"sortField\"\n              size=\"default\"\n              style=\"width: 120px; margin-right: 8px;\"\n              @change=\"handleSortChange\"\n            >\n              <a-select-option value=\"totalRevenue\">总收益</a-select-option>\n              <a-select-option value=\"salesCount\">销售次数</a-select-option>\n              <a-select-option value=\"createTime\">创建时间</a-select-option>\n            </a-select>\n            <a-button\n              :icon=\"sortOrder === 'desc' ? 'sort-descending' : 'sort-ascending'\"\n              @click=\"toggleSortOrder\"\n              :title=\"sortOrder === 'desc' ? '降序' : '升序'\"\n              class=\"sort-order-btn\"\n            >\n              {{ sortOrder === 'desc' ? '降序' : '升序' }}\n            </a-button>\n          </div>\n        </div>\n\n      </div>\n    </div>\n  </div>\n\n  <!-- 智能体卡片列表 -->\n  <div class=\"agent-grid\" v-if=\"!loading && agents.length > 0\">\n    <div \n      v-for=\"agent in agents\" \n      :key=\"agent.id\" \n      class=\"agent-card\"\n      :class=\"{ 'pending': agent.auditStatus === '1', 'approved': agent.auditStatus === '2', 'rejected': agent.auditStatus === '3' }\"\n    >\n      <!-- 卡片头部 -->\n      <div class=\"card-header\">\n        <div class=\"agent-avatar\">\n          <img \n            v-if=\"agent.agentAvatar\" \n            :src=\"agent.agentAvatar\" \n            :alt=\"agent.agentName\"\n            @error=\"handleImageError\"\n          />\n          <div v-else class=\"avatar-placeholder\">\n            <a-icon type=\"robot\" />\n          </div>\n        </div>\n        \n        <div class=\"agent-info\">\n          <h3 class=\"agent-name\" :title=\"agent.agentName\">{{ agent.agentName }}</h3>\n          <div class=\"agent-meta\">\n            <a-tag\n              :color=\"getStatusColor(agent.auditStatus)\"\n              class=\"status-tag\"\n            >\n              {{ agent.auditStatusText }}\n            </a-tag>\n          </div>\n        </div>\n        \n        <!-- 操作按钮 -->\n        <div class=\"card-actions\">\n          <a-dropdown :trigger=\"['click']\">\n            <a-button type=\"text\" size=\"small\">\n              <a-icon type=\"more\" />\n            </a-button>\n            <a-menu slot=\"overlay\">\n              <a-menu-item key=\"edit\" @click=\"handleEdit(agent)\" :disabled=\"!agent.editable\">\n                <a-icon type=\"edit\" />\n                编辑\n              </a-menu-item>\n\n              <a-menu-item key=\"delete\" @click=\"handleDelete(agent)\" :disabled=\"!agent.deletable\">\n                <a-icon type=\"delete\" />\n                删除\n              </a-menu-item>\n            </a-menu>\n          </a-dropdown>\n        </div>\n      </div>\n      \n      <!-- 卡片内容 -->\n      <div class=\"card-content\">\n        <div class=\"agent-description\" :title=\"agent.agentDescription\">\n          <p>{{ agent.agentDescription || '暂无描述' }}</p>\n        </div>\n        \n        <!-- 统计信息 -->\n        <div class=\"agent-stats\">\n          <div class=\"stat-item\">\n            <span class=\"stat-label\">价格</span>\n            <span class=\"stat-value price\">¥{{ agent.price || 0 }}</span>\n          </div>\n          <div class=\"stat-item\">\n            <span class=\"stat-label\">工作流</span>\n            <span class=\"stat-value\">{{ agent.workflowCount || 0 }}</span>\n          </div>\n          <div class=\"stat-item\" v-if=\"agent.auditStatus === '2'\">\n            <span class=\"stat-label\">销售</span>\n            <span class=\"stat-value\">{{ agent.salesCount || 0 }}</span>\n          </div>\n        </div>\n        \n        <!-- 收益信息（仅已通过状态显示） -->\n        <div class=\"stat-item revenue\" v-if=\"agent.auditStatus === '2'\">\n          <div class=\"stat-content\">\n            <span class=\"stat-label\">总收益</span>\n            <span class=\"stat-value revenue\">¥{{ formatMoney(agent.totalRevenue) }}</span>\n          </div>\n        </div>\n        \n        <!-- 审核备注（拒绝状态显示） -->\n        <div class=\"audit-remark\" v-if=\"agent.auditStatus === '3' && agent.auditRemark\">\n          <div class=\"remark-label\">拒绝原因：</div>\n          <div class=\"remark-content\">{{ agent.auditRemark }}</div>\n        </div>\n      </div>\n      \n      <!-- 卡片底部 -->\n      <div class=\"card-footer\">\n        <div class=\"create-time\">\n          创建时间：{{ formatDate(agent.createTime) }}\n        </div>\n        <!-- 体验链接 -->\n        <div class=\"experience-link\" v-if=\"agent.experienceLink\">\n          <a :href=\"agent.experienceLink\" target=\"_blank\" class=\"link-button\">\n            <a-icon type=\"link\" />\n            体验链接\n          </a>\n        </div>\n      </div>\n    </div>\n  </div>\n  \n  <!-- 加载状态 -->\n  <div class=\"loading-container\" v-if=\"loading\">\n    <a-spin size=\"large\">\n      <div class=\"loading-text\">加载中...</div>\n    </a-spin>\n  </div>\n  \n  <!-- 空状态 -->\n  <div class=\"empty-container\" v-if=\"!loading && agents.length === 0\">\n    <a-empty \n      description=\"暂无智能体\"\n      :image=\"emptyImage\"\n    >\n      <a-button type=\"primary\" @click=\"handleCreate\">\n        <a-icon type=\"plus\" />\n        创建第一个智能体\n      </a-button>\n    </a-empty>\n  </div>\n  \n  <!-- 分页 -->\n  <div class=\"pagination-container\" v-if=\"!loading && agents.length > 0\">\n    <div class=\"pagination-wrapper\">\n      <a-pagination\n        :current=\"pagination.current\"\n        :total=\"pagination.total\"\n        :page-size=\"pagination.pageSize\"\n        :page-size-options=\"['12', '24', '36', '48']\"\n        :show-size-changer=\"true\"\n        :show-quick-jumper=\"true\"\n        :show-total=\"(total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`\"\n        @change=\"handlePageChange\"\n        @showSizeChange=\"handlePageSizeChange\"\n        class=\"custom-pagination\"\n      >\n        <template slot=\"buildOptionText\" slot-scope=\"props\">\n          <span>{{ props.value }}条/页</span>\n        </template>\n      </a-pagination>\n    </div>\n  </div>\n</div>\n", null]}