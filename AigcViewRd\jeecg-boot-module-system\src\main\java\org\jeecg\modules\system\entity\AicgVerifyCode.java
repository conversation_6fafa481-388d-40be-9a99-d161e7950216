package org.jeecg.modules.system.entity;

import java.io.Serializable;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 验证码记录表
 * @Author: jeecg-boot
 * @Date: 2025-06-19
 * @Version: V1.0
 */
@Data
@TableName("aicg_verify_code")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="aicg_verify_code对象", description="验证码记录表")
public class AicgVerifyCode implements Serializable {
    private static final long serialVersionUID = 1L;

    /**主键*/
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String id;
    
    /**验证码类型*/
    @Excel(name = "验证码类型", width = 15)
    @ApiModelProperty(value = "验证码类型：sms-短信,email-邮箱,captcha-图形")
    private String codeType;
    
    /**目标（手机号/邮箱）*/
    @Excel(name = "目标", width = 20)
    @ApiModelProperty(value = "目标（手机号/邮箱）")
    private String target;
    
    /**验证码*/
    @Excel(name = "验证码", width = 15)
    @ApiModelProperty(value = "验证码")
    private String code;
    
    /**使用场景*/
    @Excel(name = "使用场景", width = 15)
    @ApiModelProperty(value = "使用场景：register-注册,login-登录,reset-重置密码")
    private String scene;
    
    /**请求IP*/
    @Excel(name = "请求IP", width = 15)
    @ApiModelProperty(value = "请求IP")
    private String ipAddress;
    
    /**使用状态*/
    @Excel(name = "使用状态", width = 15)
    @ApiModelProperty(value = "使用状态：0-未使用,1-已使用")
    private Integer usedStatus;
    
    /**过期时间*/
    @Excel(name = "过期时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "过期时间")
    private Date expireTime;
    
    /**创建时间*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    
    /**使用时间*/
    @Excel(name = "使用时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "使用时间")
    private Date useTime;
}
