import TOSBase from '../base';
export interface CustomDomainRule {
    Domain: string;
    Cname: string;
    Forbidden?: boolean;
    ForbiddenReason?: string;
    CertId?: string;
    CertStatus?: string;
    /**@private unstable
     * value tos|s3
     * */
    Protocol?: string;
}
export interface PutBucketCustomDomainInput {
    bucket: string;
    customDomainRule: {
        Domain: string;
        CertId?: string;
        /**@private unstable
         * value tos|s3
         * */
        Protocol?: string;
    };
}
export interface PutBucketCustomDomainOutput {
}
export declare function putBucketCustomDomain(this: TOSBase, input: PutBucketCustomDomainInput): Promise<import("../base").TosResponse<PutBucketCustomDomainOutput>>;
export interface GetBucketCustomDomainInput {
    bucket: string;
}
export interface GetBucketCustomDomainOutput {
    CustomDomainRules: CustomDomainRule[];
}
export declare function getBucketCustomDomain(this: TOSBase, input: GetBucketCustomDomainInput): Promise<import("../base").TosResponse<GetBucketCustomDomainOutput>>;
export interface DeleteBucketCustomDomainInput {
    bucket: string;
    customDomain: string;
}
export interface DeleteBucketCustomDomainOutput {
}
export declare function deleteBucketCustomDomain(this: TOSBase, input: DeleteBucketCustomDomainInput): Promise<import("../base").TosResponse<DeleteBucketCustomDomainOutput>>;
