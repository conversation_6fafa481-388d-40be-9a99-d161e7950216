package org.jeecg.modules.api.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description: 创作者智能体视图对象
 * @Author: 智界AIGC
 * @Date: 2025-08-04
 * @Version: V1.0
 */
@Data
@ApiModel(value = "CreatorAgentVO", description = "创作者智能体视图对象")
public class CreatorAgentVO implements Serializable {
    
    private static final long serialVersionUID = 1L;

    /**主键ID*/
    @ApiModelProperty(value = "主键ID")
    private String id;

    /**智能体ID*/
    @ApiModelProperty(value = "智能体ID")
    private String agentId;

    /**智能体名称*/
    @ApiModelProperty(value = "智能体名称")
    private String agentName;

    /**智能体描述*/
    @ApiModelProperty(value = "智能体描述")
    private String agentDescription;

    /**智能体头像*/
    @ApiModelProperty(value = "智能体头像URL")
    private String agentAvatar;

    /**体验链接*/
    @ApiModelProperty(value = "体验链接")
    private String experienceLink;

    /**价格（元）*/
    @ApiModelProperty(value = "价格（元）")
    private BigDecimal price;

    /**作者类型*/
    @ApiModelProperty(value = "作者类型：1-官方，2-创作者")
    private String authorType;

    /**作者类型文本*/
    @ApiModelProperty(value = "作者类型文本")
    private String authorTypeText;

    /**审核状态*/
    @ApiModelProperty(value = "审核状态：1-待审核，2-已通过，3-已拒绝")
    private String auditStatus;

    /**审核状态文本*/
    @ApiModelProperty(value = "审核状态文本")
    private String auditStatusText;

    /**审核备注*/
    @ApiModelProperty(value = "审核备注")
    private String auditRemark;

    /**销售次数*/
    @ApiModelProperty(value = "销售次数")
    private Integer salesCount;

    /**总收益*/
    @ApiModelProperty(value = "总收益")
    private BigDecimal totalRevenue;

    /**本月收益*/
    @ApiModelProperty(value = "本月收益")
    private BigDecimal monthRevenue;

    /**工作流数量*/
    @ApiModelProperty(value = "工作流数量")
    private Integer workflowCount;

    /**创建时间*/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**更新时间*/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    /**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;

    /**是否可编辑*/
    @ApiModelProperty(value = "是否可编辑（只有待审核状态可编辑）")
    private Boolean editable;

    /**是否可删除*/
    @ApiModelProperty(value = "是否可删除（只有待审核状态可删除）")
    private Boolean deletable;
}
