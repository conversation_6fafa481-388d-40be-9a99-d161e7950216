import TOSBase, { TosResponse } from '../base';
export interface ListObjectsType2Input {
    bucket?: string;
    prefix?: string;
    delimiter?: string;
    encodingType?: string;
    /**
     * if not specify `maxKeys` field, default maxKeys value is 1000.
     */
    maxKeys?: number;
    continuationToken?: string;
    startAfter?: string;
    /**
     * default value: false
     * if set false, the method will keep fetch objects until get `maxKeys` objects.
     * if set true,  the method will fetch objects once
     */
    listOnlyOnce?: boolean;
}
export interface ListObjectsType2ContentItem {
    ETag: string;
    Key: string;
    LastModified: string;
    Owner?: {
        ID: string;
        DisplayName: string;
    };
    Size: number;
    StorageClass: string;
    HashCrc64ecma?: string;
}
export interface ListObjectsType2VersionItem {
    ETag: string;
    IsLatest: boolean;
    Key: string;
    LastModified: string;
    Owner: {
        ID: string;
        DisplayName: string;
    };
    Size: number;
    StorageClass: string;
    VersionId: string;
}
export interface ListObjectDeleteMarkerItem {
    ETag: string;
    IsLatest: boolean;
    Key: string;
    LastModified: string;
    Owner: {
        ID: string;
        DisplayName: string;
    };
    Size: number;
    StorageClass: string;
    VersionId: string;
}
export interface ListedCommonPrefix {
    Prefix: string;
}
export interface ListObjectsType2Output {
    Name: string;
    Prefix: string;
    MaxKeys: number;
    Delimiter?: string;
    EncodingType?: string;
    IsTruncated: boolean;
    KeyCount: number;
    StartAfter?: string;
    ContinuationToken?: string;
    NextContinuationToken?: string;
    CommonPrefixes: ListedCommonPrefix[];
    Contents: ListObjectsType2ContentItem[];
}
declare class TOSListObjectsType2 extends TOSBase {
    listObjectsType2: typeof listObjectsType2;
}
export declare function listObjectsType2(this: TOSListObjectsType2, input?: ListObjectsType2Input): Promise<TosResponse<ListObjectsType2Output>>;
export {};
