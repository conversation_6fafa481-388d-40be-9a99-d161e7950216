<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>轮播图功能测试</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            padding: 20px;
        }

        .test-container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .test-info {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .controls {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }

        .btn {
            padding: 10px 20px;
            background: #3b82f6;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 500;
        }

        .btn:hover {
            background: #2563eb;
        }

        /* 轮播图样式 */
        .carousel {
            position: relative;
            width: 100%;
            height: 500px;
            overflow: hidden;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .carousel-slide {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            opacity: 0;
            transition: opacity 0.6s ease-in-out;
        }

        .carousel-slide.active {
            opacity: 1;
        }

        .slide-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .slide-content {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: linear-gradient(transparent, rgba(0,0,0,0.7));
            color: white;
            padding: 40px;
        }

        .slide-title {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .slide-description {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        /* 控制器 */
        .carousel-controls {
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 15px;
            align-items: center;
            background: rgba(0,0,0,0.6);
            padding: 10px 20px;
            border-radius: 25px;
            backdrop-filter: blur(10px);
        }

        .control-btn {
            width: 36px;
            height: 36px;
            background: rgba(255,255,255,0.2);
            border: 1px solid rgba(255,255,255,0.3);
            border-radius: 50%;
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .control-btn:hover {
            background: rgba(255,255,255,0.3);
            transform: scale(1.1);
        }

        .indicators {
            display: flex;
            gap: 8px;
        }

        .indicator {
            width: 32px;
            height: 6px;
            background: rgba(255,255,255,0.3);
            border-radius: 3px;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .indicator.active {
            background: rgba(255,255,255,0.6);
        }

        .indicator-progress {
            position: absolute;
            top: 0;
            left: 0;
            height: 100%;
            width: 0%;
            background: white;
            border-radius: 3px;
            transition: width 0.1s ease;
        }

        .debug-info {
            background: #1f2937;
            color: white;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
        }

        .status {
            color: #10b981;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>轮播图功能测试</h1>
        
        <div class="test-info">
            <p><strong>当前幻灯片:</strong> <span id="currentSlide">1</span> / <span id="totalSlides">4</span></p>
            <p><strong>自动播放:</strong> <span id="autoPlayStatus" class="status">开启</span></p>
            <p><strong>测试状态:</strong> <span id="testStatus" class="status">准备就绪</span></p>
        </div>

        <div class="controls">
            <button class="btn" onclick="prevSlide()">上一张</button>
            <button class="btn" onclick="nextSlide()">下一张</button>
            <button class="btn" onclick="toggleAutoPlay()">切换自动播放</button>
            <button class="btn" onclick="goToSlide(0)">回到第一张</button>
            <button class="btn" onclick="runTest()">运行测试</button>
        </div>

        <div class="carousel" id="carousel">
            <div class="carousel-slide active">
                <img src="./public/plugImg.jpg" alt="测试图片1" class="slide-image">
                <div class="slide-content">
                    <h2 class="slide-title">第一张测试图片</h2>
                    <p class="slide-description">这是第一张测试图片，用于验证轮播图基础功能</p>
                </div>
            </div>
            <div class="carousel-slide">
                <img src="https://images.unsplash.com/photo-1485827404703-89b55fcc595e?w=1200&h=500&fit=crop&crop=center" alt="测试图片2" class="slide-image">
                <div class="slide-content">
                    <h2 class="slide-title">第二张测试图片</h2>
                    <p class="slide-description">这是第二张测试图片，用于验证切换功能</p>
                </div>
            </div>
            <div class="carousel-slide">
                <img src="https://images.unsplash.com/photo-1611224923853-80b023f02d71?w=1200&h=500&fit=crop&crop=center" alt="测试图片3" class="slide-image">
                <div class="slide-content">
                    <h2 class="slide-title">第三张测试图片</h2>
                    <p class="slide-description">这是第三张测试图片，用于验证循环播放</p>
                </div>
            </div>
            <div class="carousel-slide">
                <img src="https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=1200&h=500&fit=crop&crop=center" alt="测试图片4" class="slide-image">
                <div class="slide-content">
                    <h2 class="slide-title">第四张测试图片</h2>
                    <p class="slide-description">这是第四张测试图片，用于验证完整循环</p>
                </div>
            </div>

            <!-- 控制器 -->
            <div class="carousel-controls">
                <button class="control-btn" onclick="prevSlide()">‹</button>
                <div class="indicators">
                    <div class="indicator active" onclick="goToSlide(0)">
                        <div class="indicator-progress"></div>
                    </div>
                    <div class="indicator" onclick="goToSlide(1)"></div>
                    <div class="indicator" onclick="goToSlide(2)"></div>
                    <div class="indicator" onclick="goToSlide(3)"></div>
                </div>
                <button class="control-btn" onclick="nextSlide()">›</button>
            </div>
        </div>

        <div class="debug-info">
            <h3>调试信息</h3>
            <div id="debugInfo">
                <p>轮播图已初始化</p>
                <p>当前幻灯片索引: 0</p>
                <p>自动播放: 开启</p>
                <p>时间戳: <span id="timestamp"></span></p>
            </div>
        </div>
    </div>

    <script>
        let currentSlideIndex = 0;
        let totalSlides = 4;
        let isAutoPlaying = true;
        let autoPlayTimer = null;
        let progressTimer = null;

        // 初始化
        function init() {
            updateDisplay();
            if (isAutoPlaying) {
                startAutoPlay();
            }
            updateTimestamp();
            console.log('轮播图初始化完成');
        }

        // 切换到指定幻灯片
        function goToSlide(index) {
            if (index === currentSlideIndex) return;
            
            console.log(`切换到幻灯片 ${index}`);
            
            // 移除当前活动状态
            document.querySelectorAll('.carousel-slide').forEach(slide => {
                slide.classList.remove('active');
            });
            document.querySelectorAll('.indicator').forEach(indicator => {
                indicator.classList.remove('active');
            });
            
            // 设置新的活动状态
            currentSlideIndex = index;
            document.querySelectorAll('.carousel-slide')[index].classList.add('active');
            document.querySelectorAll('.indicator')[index].classList.add('active');
            
            updateDisplay();
            resetProgress();
        }

        // 下一张
        function nextSlide() {
            const nextIndex = currentSlideIndex === totalSlides - 1 ? 0 : currentSlideIndex + 1;
            goToSlide(nextIndex);
        }

        // 上一张
        function prevSlide() {
            const prevIndex = currentSlideIndex === 0 ? totalSlides - 1 : currentSlideIndex - 1;
            goToSlide(prevIndex);
        }

        // 切换自动播放
        function toggleAutoPlay() {
            isAutoPlaying = !isAutoPlaying;
            if (isAutoPlaying) {
                startAutoPlay();
            } else {
                stopAutoPlay();
            }
            updateDisplay();
        }

        // 开始自动播放
        function startAutoPlay() {
            stopAutoPlay(); // 先停止之前的定时器
            autoPlayTimer = setInterval(nextSlide, 3000);
            startProgress();
        }

        // 停止自动播放
        function stopAutoPlay() {
            if (autoPlayTimer) {
                clearInterval(autoPlayTimer);
                autoPlayTimer = null;
            }
            stopProgress();
        }

        // 开始进度条
        function startProgress() {
            stopProgress();
            const progressBar = document.querySelector('.indicator.active .indicator-progress');
            if (progressBar) {
                progressBar.style.width = '0%';
                progressBar.style.transition = 'width 3s linear';
                setTimeout(() => {
                    progressBar.style.width = '100%';
                }, 10);
            }
        }

        // 停止进度条
        function stopProgress() {
            const progressBar = document.querySelector('.indicator.active .indicator-progress');
            if (progressBar) {
                progressBar.style.transition = 'none';
                progressBar.style.width = '0%';
            }
        }

        // 重置进度条
        function resetProgress() {
            if (isAutoPlaying) {
                startProgress();
            }
        }

        // 更新显示
        function updateDisplay() {
            document.getElementById('currentSlide').textContent = currentSlideIndex + 1;
            document.getElementById('totalSlides').textContent = totalSlides;
            document.getElementById('autoPlayStatus').textContent = isAutoPlaying ? '开启' : '关闭';
            updateTimestamp();
        }

        // 更新时间戳
        function updateTimestamp() {
            document.getElementById('timestamp').textContent = new Date().toLocaleTimeString();
        }

        // 运行测试
        function runTest() {
            document.getElementById('testStatus').textContent = '测试中...';
            console.log('开始轮播图功能测试');
            
            let testStep = 0;
            const testSteps = [
                () => { goToSlide(1); console.log('测试步骤1: 切换到第2张'); },
                () => { goToSlide(2); console.log('测试步骤2: 切换到第3张'); },
                () => { goToSlide(3); console.log('测试步骤3: 切换到第4张'); },
                () => { goToSlide(0); console.log('测试步骤4: 回到第1张'); },
                () => { 
                    document.getElementById('testStatus').textContent = '测试完成';
                    console.log('轮播图功能测试完成');
                }
            ];
            
            function runNextStep() {
                if (testStep < testSteps.length) {
                    testSteps[testStep]();
                    testStep++;
                    setTimeout(runNextStep, 1000);
                }
            }
            
            runNextStep();
        }

        // 页面加载完成后初始化
        window.addEventListener('load', init);
    </script>
</body>
</html>
