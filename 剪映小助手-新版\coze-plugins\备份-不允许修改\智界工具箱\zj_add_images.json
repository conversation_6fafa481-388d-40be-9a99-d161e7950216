{"openapi": "3.0.0", "info": {"title": "剪映小助手_智界工具箱 - 批量添加图片", "description": "批量添加图片", "version": "1.0.0"}, "servers": [{"url": "https://www.aigcview.com"}], "paths": {"/jeecg-boot/api/jianying/add_images": {"post": {"summary": "批量添加图片", "description": "批量添加图片", "operationId": "addImages_zj", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"access_key": {"type": "string", "description": "访问密钥（必填）", "example": "JianyingAPI_2025_SecureAccess_AigcView", "default": "JianyingAPI_2025_SecureAccess_AigcView"}, "draft_url": {"type": "string", "description": "草稿地址，使用createDraft_zj输出的draft_url即可（必填）", "example": "https://aigcview-plub.tos-s-cn-guangzhou.volces.com/jianying-assistant/drafts/2025/01/06/draft_1736152800000_1024/draft_content.json"}, "alpha": {"type": "number", "description": "图片透明度，范围0-1（可选）", "minimum": 0, "maximum": 1, "example": 0.5}, "image_infos": {"type": "string", "description": "图片信息JSON字符串（必填）", "example": "[{\"image_url\":\"https://example.com/img1.jpg\",\"duration\":5000000,\"start\":0,\"end\":5000000,\"in_animation\":\"淡入\",\"out_animation\":\"淡出\",\"group_animation\":\"无\"},{\"image_url\":\"https://example.com/img2.jpg\",\"duration\":5000000,\"start\":5000000,\"end\":10000000,\"in_animation\":\"展开\",\"out_animation\":\"收缩\",\"group_animation\":\"无\"},{\"image_url\":\"https://example.com/img3.jpg\",\"duration\":5000000,\"start\":10000000,\"end\":15000000,\"in_animation\":\"闪现\",\"out_animation\":\"消失\",\"group_animation\":\"无\"}]"}, "scale_x": {"type": "number", "description": "X轴缩放比例（可选）", "example": 0.9}, "scale_y": {"type": "number", "description": "Y轴缩放比例（可选）", "example": 0.9}, "transform_x": {"type": "number", "description": "X轴移动位置（可选）", "example": 100}, "transform_y": {"type": "number", "description": "Y轴移动位置（可选）", "example": -100}}, "required": ["access_key", "draft_url", "image_infos"], "additionalProperties": false}}}}, "responses": {"200": {"description": "成功添加图片", "content": {"application/json": {"schema": {"type": "object", "properties": {"track_id": {"type": "string", "description": "新创建的轨道ID", "example": "b36c6b40-f2cb-48b6-99e9-2b3ab3f0f506"}, "draft_url": {"type": "string", "description": "更新后的草稿地址", "example": "https://aigcview-plub.tos-s-cn-guangzhou.volces.com/jianying-assistant/drafts/2025/01/06/draft_1736152800000_1024/draft_content.json"}, "image_ids": {"type": "array", "description": "添加的图片材料ID列表", "items": {"type": "string"}, "example": ["uuid1", "uuid2", "uuid3"]}, "segment_ids": {"type": "array", "description": "创建的片段ID列表", "items": {"type": "string"}, "example": ["segment1", "segment2", "segment3"]}, "segment_infos": {"type": "array", "description": "片段详细信息", "items": {"type": "object", "properties": {"segment_id": {"type": "string", "description": "片段ID"}, "start": {"type": "integer", "description": "开始时间（微秒）"}, "end": {"type": "integer", "description": "结束时间（微秒）"}, "duration": {"type": "integer", "description": "持续时间（微秒）"}}}, "example": [{"segment_id": "segment1", "start": 0, "end": 5000000, "duration": 5000000}, {"segment_id": "segment2", "start": 5000000, "end": 10000000, "duration": 5000000}, {"segment_id": "segment3", "start": 10000000, "end": 15000000, "duration": 5000000}]}}, "required": ["track_id", "draft_url", "image_ids", "segment_ids", "segment_infos"]}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "description": "错误信息", "example": "批量添加图片失败: 图片信息不能为空"}}, "required": ["error"]}}}}, "500": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "description": "错误信息", "example": "批量添加图片失败: 服务器内部错误"}}, "required": ["error"]}}}}}}}}}