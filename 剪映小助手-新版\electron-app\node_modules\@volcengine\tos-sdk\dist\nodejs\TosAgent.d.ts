/// <reference types="node" />
import http from 'http';
import { Socket } from 'net';
declare module 'http' {
    interface AgentOptions {
        rejectUnauthorized?: boolean;
    }
    interface Agent {
        createConnection(...opts: unknown[]): Socket;
    }
}
interface TosAgentOptions extends http.AgentOptions {
    tosOpts: {
        enableVerifySSL: boolean;
        connectionTimeout: number;
        maxConnections: number;
        idleConnectionTime: number;
        isHttps: boolean;
    };
}
export declare function TosAgent(opts: TosAgentOptions): http.Agent;
export {};
