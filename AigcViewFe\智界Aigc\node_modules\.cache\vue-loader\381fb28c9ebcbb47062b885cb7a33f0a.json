{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\workflow\\components\\AgentManagement.vue?vue&type=style&index=0&id=66227f41&lang=less&scoped=true&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\workflow\\components\\AgentManagement.vue", "mtime": 1754512044865}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\css-loader\\index.js", "mtime": 1749979994548}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\less-loader\\dist\\cjs.js", "mtime": 1749980456032}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n.agent-management {\n  // 筛选器样式\n  .market-filters {\n    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);\n    padding: 2rem;\n    border-radius: 20px;\n    box-shadow: 0 8px 32px rgba(59, 130, 246, 0.12), 0 2px 8px rgba(0, 0, 0, 0.04);\n    margin-bottom: 2rem;\n    border: 1px solid rgba(59, 130, 246, 0.1);\n  }\n\n  .filter-row {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    gap: 2rem;\n  }\n\n  .search-box {\n    flex: 1;\n    max-width: 600px;\n  }\n\n  .search-wrapper {\n    position: relative;\n    display: flex;\n    align-items: center;\n    background: white;\n    border-radius: 12px;\n    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);\n    border: 2px solid #cbd5e1;\n    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n\n    &:hover {\n      border-color: #3b82f6;\n      box-shadow: 0 6px 20px rgba(59, 130, 246, 0.2);\n      transform: translateY(-1px);\n    }\n\n    &:focus-within {\n      border-color: #3b82f6;\n      box-shadow: 0 8px 24px rgba(59, 130, 246, 0.3);\n      transform: translateY(-1px);\n    }\n  }\n\n  .search-icon {\n    position: absolute;\n    left: 16px;\n    z-index: 2;\n    color: #64748b;\n    font-size: 18px;\n    transition: color 0.3s ease;\n  }\n\n  .search-wrapper:focus-within .search-icon {\n    color: #3b82f6;\n  }\n\n  .clear-icon {\n    position: absolute;\n    right: 16px;\n    z-index: 2;\n    color: #94a3b8;\n    font-size: 16px;\n    cursor: pointer;\n    transition: color 0.3s ease;\n\n    &:hover {\n      color: #64748b;\n    }\n  }\n\n  .search-input {\n    flex: 1;\n    border: none !important;\n    box-shadow: none !important;\n    padding-left: 48px !important;\n    padding-right: 48px !important;\n    font-size: 16px;\n    height: 48px;\n    background: transparent;\n\n    &:focus {\n      border: none !important;\n      box-shadow: none !important;\n    }\n  }\n\n  .filter-controls {\n    display: flex;\n    align-items: flex-start;\n    gap: 2rem;\n    flex-wrap: wrap;\n  }\n\n  .filter-group {\n    display: flex;\n    align-items: center;\n  }\n\n  // 🔥 排序控件样式\n  .sort-controls {\n    display: flex;\n    align-items: center;\n    gap: 8px;\n\n    .sort-label {\n      font-size: 14px;\n      color: #666;\n      font-weight: 500;\n    }\n\n    .sort-order-btn {\n      height: 32px;\n      border: 1px solid #d9d9d9;\n      border-radius: 6px;\n      background: #fff;\n      color: #666;\n      font-size: 12px;\n      transition: all 0.2s ease;\n\n      &:hover {\n        border-color: #40a9ff;\n        color: #40a9ff;\n      }\n    }\n  }\n\n  .filter-buttons {\n    display: flex;\n    gap: 8px;\n    flex-wrap: wrap;\n  }\n\n  .filter-btn {\n    display: flex;\n    align-items: center;\n    gap: 6px;\n    padding: 8px 16px;\n    border: 2px solid #e2e8f0;\n    border-radius: 12px;\n    background: white;\n    color: #64748b;\n    font-size: 14px;\n    font-weight: 500;\n    cursor: pointer;\n    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n    position: relative;\n    overflow: hidden;\n\n    &::before {\n      content: '';\n      position: absolute;\n      top: 0;\n      left: -100%;\n      width: 100%;\n      height: 100%;\n      background: linear-gradient(90deg, transparent, rgba(79, 70, 229, 0.1), transparent);\n      transition: left 0.5s ease;\n    }\n\n    &:hover::before {\n      left: 100%;\n    }\n\n    &:hover {\n      border-color: #4f46e5;\n      color: #4f46e5;\n      transform: translateY(-1px);\n      box-shadow: 0 4px 12px rgba(79, 70, 229, 0.15);\n    }\n\n    &.active {\n      background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);\n      border-color: #4f46e5;\n      color: white;\n      transform: translateY(-1px);\n      box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3);\n\n      &:hover {\n        background: linear-gradient(135deg, #4338ca 0%, #6d28d9 100%);\n      }\n    }\n  }\n\n  // 智能体网格布局\n  .agent-grid {\n    display: grid;\n    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));\n    gap: 24px;\n    margin-bottom: 24px;\n  }\n\n  // 智能体卡片\n  .agent-card {\n    background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);\n    border-radius: 20px;\n    border: 2px solid #f1f5f9;\n    overflow: hidden;\n    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);\n    position: relative;\n    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.06), 0 2px 8px rgba(0, 0, 0, 0.04);\n\n    &::before {\n      content: '';\n      position: absolute;\n      top: 0;\n      left: 0;\n      right: 0;\n      height: 4px;\n      background: linear-gradient(90deg, #e2e8f0, #e2e8f0);\n      transition: all 0.3s ease;\n      z-index: 1;\n    }\n\n    &:hover {\n      transform: translateY(-6px);\n      box-shadow: 0 20px 48px rgba(0, 0, 0, 0.12), 0 8px 24px rgba(0, 0, 0, 0.08);\n      border-color: #cbd5e1;\n\n      &::before {\n        background: linear-gradient(90deg, #3b82f6, #8b5cf6);\n      }\n    }\n\n    // 状态样式\n    &.pending {\n      &::before {\n        background: linear-gradient(90deg, #f59e0b, #d97706);\n      }\n\n      &:hover::before {\n        background: linear-gradient(90deg, #d97706, #b45309);\n      }\n    }\n\n    &.approved {\n      &::before {\n        background: linear-gradient(90deg, #10b981, #059669);\n      }\n\n      &:hover::before {\n        background: linear-gradient(90deg, #059669, #047857);\n      }\n    }\n\n    &.rejected {\n      &::before {\n        background: linear-gradient(90deg, #ef4444, #dc2626);\n      }\n\n      &:hover::before {\n        background: linear-gradient(90deg, #dc2626, #b91c1c);\n      }\n    }\n\n    // 卡片头部\n    .card-header {\n      padding: 24px 24px 20px;\n      background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);\n      border-bottom: 1px solid #e2e8f0;\n      display: flex;\n      align-items: flex-start;\n      gap: 16px;\n      position: relative;\n\n      .agent-avatar {\n        width: 64px;\n        height: 64px;\n        border-radius: 16px;\n        overflow: hidden;\n        flex-shrink: 0;\n        position: relative;\n        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);\n        border: 3px solid white;\n\n        img {\n          width: 100%;\n          height: 100%;\n          object-fit: cover;\n          transition: transform 0.3s ease;\n        }\n\n        &:hover img {\n          transform: scale(1.05);\n        }\n\n        .avatar-placeholder {\n          width: 100%;\n          height: 100%;\n          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          color: #fff;\n          font-size: 24px;\n        }\n      }\n\n      .agent-info {\n        flex: 1;\n        min-width: 0;\n\n        .agent-name {\n          margin: 0 0 8px 0;\n          font-size: 20px;\n          font-weight: 700;\n          color: #1e293b;\n          white-space: nowrap;\n          overflow: hidden;\n          text-overflow: ellipsis;\n          line-height: 1.3;\n          background: linear-gradient(135deg, #1e293b 0%, #334155 100%);\n          -webkit-background-clip: text;\n          -webkit-text-fill-color: transparent;\n          background-clip: text;\n        }\n\n        .agent-meta {\n          display: flex;\n          align-items: center;\n          gap: 8px;\n\n          .status-tag {\n            font-size: 13px;\n            font-weight: 600;\n            border-radius: 8px;\n            padding: 4px 12px;\n            border: none;\n            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n          }\n        }\n      }\n\n      .card-actions {\n        .ant-btn {\n          border: none;\n          box-shadow: none;\n\n          &:hover {\n            background: #f5f5f5;\n          }\n        }\n      }\n    }\n\n    // 卡片内容\n    .card-content {\n      padding: 20px 24px;\n\n      .agent-description {\n        margin: 0 0 20px 0;\n        height: 62px;\n        background: #f8fafc;\n        border-radius: 10px;\n        border-left: 3px solid #e2e8f0;\n        display: flex;\n        align-items: center;\n        padding: 0 16px;\n\n        p {\n          color: #64748b;\n          font-size: 14px;\n          line-height: 1.5;\n          display: -webkit-box;\n          -webkit-line-clamp: 2;\n          -webkit-box-orient: vertical;\n          overflow: hidden;\n          word-break: break-word;\n          margin: 0;\n          width: 100%;\n        }\n      }\n\n      .agent-stats {\n        display: grid;\n        grid-template-columns: repeat(3, 1fr);\n        gap: 12px;\n        margin-bottom: 16px;\n      }\n\n      .stat-item {\n        background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);\n        border: 2px solid #e2e8f0;\n        border-radius: 10px;\n        padding: 12px;\n        text-align: center;\n        transition: all 0.3s ease;\n        position: relative;\n        overflow: hidden;\n\n        &::before {\n          content: '';\n          position: absolute;\n          top: 0;\n          left: 0;\n          right: 0;\n          height: 2px;\n          background: linear-gradient(90deg, #e2e8f0, #e2e8f0);\n          transition: all 0.3s ease;\n        }\n\n        &:hover {\n          transform: translateY(-1px);\n          box-shadow: 0 6px 16px rgba(0, 0, 0, 0.08);\n          border-color: #cbd5e1;\n        }\n\n        .stat-content {\n          display: flex;\n          flex-direction: column;\n          align-items: center;\n        }\n\n        .stat-label {\n          display: block;\n          font-size: 11px;\n          color: #64748b;\n          margin-bottom: 4px;\n          font-weight: 500;\n        }\n\n        .stat-value {\n          display: block;\n          font-size: 14px;\n          font-weight: 600;\n          color: #1e293b;\n\n          &.price {\n            color: #059669;\n          }\n\n          &.revenue {\n            color: #dc2626;\n          }\n        }\n\n        &.revenue {\n          &::before {\n            background: linear-gradient(90deg, #dc2626, #b91c1c);\n          }\n        }\n      }\n\n      .audit-remark {\n        background: #fef2f2;\n        border: 1px solid #fecaca;\n        border-radius: 6px;\n        padding: 12px;\n        margin-bottom: 16px;\n\n        .remark-label {\n          font-size: 12px;\n          color: #dc2626;\n          font-weight: 600;\n          margin-bottom: 4px;\n        }\n\n        .remark-content {\n          font-size: 14px;\n          color: #7f1d1d;\n          line-height: 1.4;\n        }\n      }\n    }\n\n    // 卡片底部\n    .card-footer {\n      padding: 12px 16px;\n      background: #fafbfc;\n      border-top: 1px solid #f0f0f0;\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n\n      .create-time,\n      .update-time {\n        font-size: 12px;\n        color: #9ca3af;\n        line-height: 1.4;\n      }\n\n      .experience-link {\n        .link-button {\n          display: inline-flex;\n          align-items: center;\n          gap: 4px;\n          padding: 6px 10px;\n          background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);\n          color: white;\n          text-decoration: none;\n          font-size: 11px;\n          font-weight: 500;\n          border-radius: 6px;\n          transition: all 0.3s ease;\n          box-shadow: 0 2px 6px rgba(59, 130, 246, 0.2);\n\n          &:hover {\n            background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);\n            transform: translateY(-1px);\n            box-shadow: 0 3px 8px rgba(59, 130, 246, 0.3);\n            color: white;\n            text-decoration: none;\n          }\n\n          .anticon {\n            font-size: 11px;\n          }\n        }\n      }\n    }\n  }\n\n  // 加载状态\n  .loading-container {\n    display: flex;\n    justify-content: center;\n    align-items: center;\n    min-height: 200px;\n\n    .loading-text {\n      margin-top: 16px;\n      color: #6b7280;\n    }\n  }\n\n  // 空状态\n  .empty-container {\n    display: flex;\n    justify-content: center;\n    align-items: center;\n    min-height: 300px;\n    background: #fff;\n    border-radius: 12px;\n    border: 1px solid #e8eaec;\n  }\n\n  // 分页\n  .pagination-container {\n    display: flex;\n    justify-content: center;\n    margin-top: 32px;\n    padding: 32px 0;\n  }\n\n  .pagination-wrapper {\n    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);\n    padding: 24px 32px;\n    border-radius: 16px;\n    box-shadow: 0 8px 32px rgba(59, 130, 246, 0.12), 0 2px 8px rgba(0, 0, 0, 0.04);\n    border: 1px solid rgba(59, 130, 246, 0.1);\n    backdrop-filter: blur(10px);\n  }\n\n  .custom-pagination {\n    :global(.ant-pagination-item) {\n      border: 2px solid #e2e8f0;\n      border-radius: 12px;\n      background: white;\n      margin: 0 4px;\n      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\n\n      &:hover {\n        border-color: #3b82f6;\n        transform: translateY(-1px);\n        box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);\n      }\n\n      a {\n        color: #64748b;\n        font-weight: 500;\n        transition: color 0.3s ease;\n      }\n\n      &:hover a {\n        color: #3b82f6;\n      }\n    }\n\n    :global(.ant-pagination-item-active) {\n      background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);\n      border-color: #3b82f6;\n      transform: translateY(-1px);\n      box-shadow: 0 6px 16px rgba(59, 130, 246, 0.3);\n\n      a {\n        color: white;\n        font-weight: 600;\n      }\n\n      &:hover {\n        background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);\n        border-color: #2563eb;\n      }\n    }\n\n    :global(.ant-pagination-prev),\n    :global(.ant-pagination-next) {\n      border: 2px solid #e2e8f0;\n      border-radius: 12px;\n      background: white;\n      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\n\n      &:hover {\n        border-color: #3b82f6;\n        transform: translateY(-1px);\n        box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);\n      }\n\n      a {\n        color: #64748b;\n        transition: color 0.3s ease;\n      }\n\n      &:hover a {\n        color: #3b82f6;\n      }\n    }\n\n    :global(.ant-pagination-options) {\n      margin-left: 16px;\n\n      .ant-select .ant-select-selector {\n        border: 2px solid #e2e8f0;\n        border-radius: 12px;\n        background: white;\n        transition: all 0.3s ease;\n        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\n\n        &:hover {\n          border-color: #3b82f6;\n          box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);\n        }\n      }\n    }\n\n    :global(.ant-pagination-options-quick-jumper) input {\n      border: 2px solid #e2e8f0;\n      border-radius: 12px;\n      background: white;\n      transition: all 0.3s ease;\n      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\n\n      &:hover {\n        border-color: #3b82f6;\n        box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);\n      }\n\n      &:focus {\n        border-color: #3b82f6;\n        box-shadow: 0 6px 16px rgba(59, 130, 246, 0.3);\n      }\n    }\n\n    :global(.ant-pagination-total-text) {\n      color: #64748b;\n      font-weight: 500;\n      margin-right: 16px;\n    }\n  }\n}\n\n// 响应式设计\n@media (max-width: 768px) {\n  .agent-management {\n    .agent-grid {\n      grid-template-columns: 1fr;\n      gap: 16px;\n    }\n\n    .agent-card {\n      .card-header {\n        .agent-info {\n          .agent-meta {\n            flex-direction: column;\n            align-items: flex-start;\n            gap: 4px;\n          }\n        }\n      }\n\n      .card-content {\n        .agent-stats {\n          grid-template-columns: repeat(2, 1fr);\n          gap: 8px;\n        }\n\n        .stat-item {\n          padding: 10px;\n\n          .stat-label {\n            font-size: 10px;\n          }\n\n          .stat-value {\n            font-size: 12px;\n          }\n        }\n      }\n    }\n  }\n\n  // 筛选器响应式设计\n  @media (max-width: 1200px) {\n    .market-filters {\n      padding: 1.5rem;\n\n      .filter-row {\n        flex-direction: column;\n        gap: 1.5rem;\n        align-items: stretch;\n      }\n\n      .search-box {\n        max-width: none;\n      }\n\n      .filter-controls {\n        gap: 1rem;\n      }\n    }\n  }\n\n  @media (max-width: 768px) {\n    .market-filters {\n      padding: 1rem;\n      border-radius: 12px;\n\n      .filter-row {\n        gap: 1rem;\n      }\n\n      .filter-controls {\n        flex-direction: column;\n        gap: 1rem;\n      }\n\n      .filter-buttons {\n        justify-content: center;\n      }\n\n      .filter-btn {\n        padding: 6px 12px;\n        font-size: 13px;\n      }\n    }\n  }\n\n  // 分页响应式设计\n  @media (max-width: 768px) {\n    .pagination-wrapper {\n      padding: 16px 20px;\n      border-radius: 12px;\n    }\n\n    .custom-pagination {\n      :global(.ant-pagination-item),\n      :global(.ant-pagination-prev),\n      :global(.ant-pagination-next) {\n        margin: 0 2px;\n        border-radius: 8px;\n      }\n\n      :global(.ant-pagination-options) {\n        margin-left: 8px;\n        margin-top: 8px;\n      }\n\n      :global(.ant-pagination-total-text) {\n        margin-right: 8px;\n        font-size: 12px;\n      }\n    }\n  }\n\n  // 🔥 工作流已发布提示弹窗样式 - 全新现代化设计\n  :global(.workflow-published-modal) {\n    .ant-modal-content {\n      border-radius: 24px !important;\n      overflow: hidden;\n      box-shadow: 0 32px 64px rgba(0, 0, 0, 0.12), 0 0 0 1px rgba(255, 255, 255, 0.05) !important;\n      border: none;\n      background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);\n      backdrop-filter: blur(20px);\n    }\n\n    .ant-modal-header {\n      display: none !important; // 完全隐藏默认头部\n    }\n\n    // 🔥 隐藏左上角的默认图标\n    .ant-modal-confirm-body-wrapper {\n      .ant-modal-confirm-body {\n        .ant-modal-confirm-content {\n          margin-left: 0 !important; // 去掉图标占位的左边距\n        }\n      }\n    }\n\n    // 🔥 隐藏默认的标题和图标区域\n    .ant-modal-confirm-title {\n      display: none !important;\n    }\n\n    .ant-modal-confirm-btns {\n      margin-top: 0 !important;\n    }\n\n    // 🔥 只在这个弹窗中隐藏anticon\n    .ant-modal-confirm-body > .anticon {\n      display: none !important;\n    }\n\n    .ant-modal-body {\n      padding: 40px 32px 32px !important;\n      background: transparent;\n\n      .custom-modal-content {\n        position: relative;\n      }\n    }\n\n    .ant-modal-footer {\n      border-top: none !important;\n      padding: 0 32px 40px !important;\n      text-align: center;\n      background: transparent;\n\n      .ant-btn {\n        min-width: 160px;\n        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n        position: relative;\n        overflow: hidden;\n\n        &::before {\n          content: '';\n          position: absolute;\n          top: 0;\n          left: -100%;\n          width: 100%;\n          height: 100%;\n          background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);\n          transition: left 0.5s;\n        }\n\n        &:hover {\n          transform: translateY(-2px) scale(1.02);\n          box-shadow: 0 12px 40px rgba(255, 167, 38, 0.5) !important;\n\n          &::before {\n            left: 100%;\n          }\n        }\n\n        &:active {\n          transform: translateY(-1px) scale(1.01);\n        }\n      }\n    }\n\n    // 关闭按钮样式\n    .ant-modal-close {\n      top: 20px;\n      right: 20px;\n      z-index: 10;\n\n      .ant-modal-close-x {\n        width: 36px;\n        height: 36px;\n        line-height: 36px;\n        border-radius: 50%;\n        background: rgba(0, 0, 0, 0.04);\n        transition: all 0.3s ease;\n\n        &:hover {\n          background: rgba(0, 0, 0, 0.08);\n          transform: rotate(90deg);\n        }\n      }\n    }\n\n    // 弹窗动画效果 - 更流畅的动画\n    &.ant-modal {\n      .ant-modal-content {\n        animation: modalSlideIn 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);\n      }\n    }\n\n    @keyframes modalSlideIn {\n      from {\n        opacity: 0;\n        transform: scale(0.8) translateY(-20px) rotateX(10deg);\n      }\n      to {\n        opacity: 1;\n        transform: scale(1) translateY(0) rotateX(0deg);\n      }\n    }\n\n    // 遮罩层样式\n    &.ant-modal .ant-modal-mask {\n      background: rgba(0, 0, 0, 0.6);\n      backdrop-filter: blur(8px);\n    }\n  }\n}\n", {"version": 3, "sources": ["AgentManagement.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0hBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "AgentManagement.vue", "sourceRoot": "src/views/website/workflow/components", "sourcesContent": ["<template>\n  <div class=\"agent-management\">\n    <!-- 筛选和搜索 -->\n    <div class=\"market-filters\">\n      <div class=\"filter-row\">\n        <!-- 搜索框 -->\n        <div class=\"search-box\">\n          <div class=\"search-wrapper\">\n            <a-icon type=\"search\" class=\"search-icon\" />\n            <a-input\n              v-model=\"searchQuery\"\n              placeholder=\"搜索智能体名称、描述...\"\n              size=\"large\"\n              @pressEnter=\"handleSearch\"\n              @input=\"handleSearch\"\n              class=\"search-input\"\n            />\n            <a-icon\n              v-if=\"searchQuery\"\n              type=\"close-circle\"\n              class=\"clear-icon\"\n              @click=\"clearSearch\"\n            />\n          </div>\n        </div>\n\n        <!-- 筛选器 -->\n        <div class=\"filter-controls\">\n          <!-- 审核状态筛选 -->\n          <div class=\"filter-group\">\n            <div class=\"filter-buttons\">\n              <button\n                class=\"filter-btn\"\n                :class=\"{ 'active': auditStatusFilter === '' }\"\n                @click=\"setAuditStatusFilter('')\"\n              >\n                <a-icon type=\"appstore\" />\n                全部\n              </button>\n              <button\n                class=\"filter-btn pending\"\n                :class=\"{ 'active': auditStatusFilter === '1' }\"\n                @click=\"setAuditStatusFilter('1')\"\n              >\n                <a-icon type=\"clock-circle\" />\n                待审核\n              </button>\n              <button\n                class=\"filter-btn approved\"\n                :class=\"{ 'active': auditStatusFilter === '2' }\"\n                @click=\"setAuditStatusFilter('2')\"\n              >\n                <a-icon type=\"check-circle\" />\n                已通过\n              </button>\n              <button\n                class=\"filter-btn rejected\"\n                :class=\"{ 'active': auditStatusFilter === '3' }\"\n                @click=\"setAuditStatusFilter('3')\"\n              >\n                <a-icon type=\"close-circle\" />\n                已拒绝\n              </button>\n            </div>\n          </div>\n\n          <!-- 排序控件 -->\n          <div class=\"filter-group\">\n            <div class=\"sort-controls\">\n              <span class=\"sort-label\">排序：</span>\n              <a-select\n                v-model=\"sortField\"\n                size=\"default\"\n                style=\"width: 120px; margin-right: 8px;\"\n                @change=\"handleSortChange\"\n              >\n                <a-select-option value=\"totalRevenue\">总收益</a-select-option>\n                <a-select-option value=\"salesCount\">销售次数</a-select-option>\n                <a-select-option value=\"createTime\">创建时间</a-select-option>\n              </a-select>\n              <a-button\n                :icon=\"sortOrder === 'desc' ? 'sort-descending' : 'sort-ascending'\"\n                @click=\"toggleSortOrder\"\n                :title=\"sortOrder === 'desc' ? '降序' : '升序'\"\n                class=\"sort-order-btn\"\n              >\n                {{ sortOrder === 'desc' ? '降序' : '升序' }}\n              </a-button>\n            </div>\n          </div>\n\n        </div>\n      </div>\n    </div>\n\n    <!-- 智能体卡片列表 -->\n    <div class=\"agent-grid\" v-if=\"!loading && agents.length > 0\">\n      <div \n        v-for=\"agent in agents\" \n        :key=\"agent.id\" \n        class=\"agent-card\"\n        :class=\"{ 'pending': agent.auditStatus === '1', 'approved': agent.auditStatus === '2', 'rejected': agent.auditStatus === '3' }\"\n      >\n        <!-- 卡片头部 -->\n        <div class=\"card-header\">\n          <div class=\"agent-avatar\">\n            <img \n              v-if=\"agent.agentAvatar\" \n              :src=\"agent.agentAvatar\" \n              :alt=\"agent.agentName\"\n              @error=\"handleImageError\"\n            />\n            <div v-else class=\"avatar-placeholder\">\n              <a-icon type=\"robot\" />\n            </div>\n          </div>\n          \n          <div class=\"agent-info\">\n            <h3 class=\"agent-name\" :title=\"agent.agentName\">{{ agent.agentName }}</h3>\n            <div class=\"agent-meta\">\n              <a-tag\n                :color=\"getStatusColor(agent.auditStatus)\"\n                class=\"status-tag\"\n              >\n                {{ agent.auditStatusText }}\n              </a-tag>\n            </div>\n          </div>\n          \n          <!-- 操作按钮 -->\n          <div class=\"card-actions\">\n            <a-dropdown :trigger=\"['click']\">\n              <a-button type=\"text\" size=\"small\">\n                <a-icon type=\"more\" />\n              </a-button>\n              <a-menu slot=\"overlay\">\n                <a-menu-item key=\"edit\" @click=\"handleEdit(agent)\" :disabled=\"!agent.editable\">\n                  <a-icon type=\"edit\" />\n                  编辑\n                </a-menu-item>\n\n                <a-menu-item key=\"delete\" @click=\"handleDelete(agent)\" :disabled=\"!agent.deletable\">\n                  <a-icon type=\"delete\" />\n                  删除\n                </a-menu-item>\n              </a-menu>\n            </a-dropdown>\n          </div>\n        </div>\n        \n        <!-- 卡片内容 -->\n        <div class=\"card-content\">\n          <div class=\"agent-description\" :title=\"agent.agentDescription\">\n            <p>{{ agent.agentDescription || '暂无描述' }}</p>\n          </div>\n          \n          <!-- 统计信息 -->\n          <div class=\"agent-stats\">\n            <div class=\"stat-item\">\n              <span class=\"stat-label\">价格</span>\n              <span class=\"stat-value price\">¥{{ agent.price || 0 }}</span>\n            </div>\n            <div class=\"stat-item\">\n              <span class=\"stat-label\">工作流</span>\n              <span class=\"stat-value\">{{ agent.workflowCount || 0 }}</span>\n            </div>\n            <div class=\"stat-item\" v-if=\"agent.auditStatus === '2'\">\n              <span class=\"stat-label\">销售</span>\n              <span class=\"stat-value\">{{ agent.salesCount || 0 }}</span>\n            </div>\n          </div>\n          \n          <!-- 收益信息（仅已通过状态显示） -->\n          <div class=\"stat-item revenue\" v-if=\"agent.auditStatus === '2'\">\n            <div class=\"stat-content\">\n              <span class=\"stat-label\">总收益</span>\n              <span class=\"stat-value revenue\">¥{{ formatMoney(agent.totalRevenue) }}</span>\n            </div>\n          </div>\n          \n          <!-- 审核备注（拒绝状态显示） -->\n          <div class=\"audit-remark\" v-if=\"agent.auditStatus === '3' && agent.auditRemark\">\n            <div class=\"remark-label\">拒绝原因：</div>\n            <div class=\"remark-content\">{{ agent.auditRemark }}</div>\n          </div>\n        </div>\n        \n        <!-- 卡片底部 -->\n        <div class=\"card-footer\">\n          <div class=\"create-time\">\n            创建时间：{{ formatDate(agent.createTime) }}\n          </div>\n          <!-- 体验链接 -->\n          <div class=\"experience-link\" v-if=\"agent.experienceLink\">\n            <a :href=\"agent.experienceLink\" target=\"_blank\" class=\"link-button\">\n              <a-icon type=\"link\" />\n              体验链接\n            </a>\n          </div>\n        </div>\n      </div>\n    </div>\n    \n    <!-- 加载状态 -->\n    <div class=\"loading-container\" v-if=\"loading\">\n      <a-spin size=\"large\">\n        <div class=\"loading-text\">加载中...</div>\n      </a-spin>\n    </div>\n    \n    <!-- 空状态 -->\n    <div class=\"empty-container\" v-if=\"!loading && agents.length === 0\">\n      <a-empty \n        description=\"暂无智能体\"\n        :image=\"emptyImage\"\n      >\n        <a-button type=\"primary\" @click=\"handleCreate\">\n          <a-icon type=\"plus\" />\n          创建第一个智能体\n        </a-button>\n      </a-empty>\n    </div>\n    \n    <!-- 分页 -->\n    <div class=\"pagination-container\" v-if=\"!loading && agents.length > 0\">\n      <div class=\"pagination-wrapper\">\n        <a-pagination\n          :current=\"pagination.current\"\n          :total=\"pagination.total\"\n          :page-size=\"pagination.pageSize\"\n          :page-size-options=\"['12', '24', '36', '48']\"\n          :show-size-changer=\"true\"\n          :show-quick-jumper=\"true\"\n          :show-total=\"(total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`\"\n          @change=\"handlePageChange\"\n          @showSizeChange=\"handlePageSizeChange\"\n          class=\"custom-pagination\"\n        >\n          <template slot=\"buildOptionText\" slot-scope=\"props\">\n            <span>{{ props.value }}条/页</span>\n          </template>\n        </a-pagination>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { Empty } from 'ant-design-vue'\n\nexport default {\n  name: 'AgentManagement',\n  props: {\n    loading: {\n      type: Boolean,\n      default: false\n    },\n    agents: {\n      type: Array,\n      default: () => []\n    },\n    pagination: {\n      type: Object,\n      default: () => ({\n        current: 1,\n        pageSize: 12,\n        total: 0\n      })\n    }\n  },\n  \n  data() {\n    return {\n      emptyImage: Empty.PRESENTED_IMAGE_SIMPLE,\n      // 筛选相关\n      searchQuery: '',\n      auditStatusFilter: '',\n      // 排序相关\n      sortField: 'totalRevenue',  // 默认按总收益排序\n      sortOrder: 'desc'           // 默认降序\n    }\n  },\n  \n  methods: {\n    // 获取状态颜色\n    getStatusColor(status) {\n      const colorMap = {\n        '1': 'orange',   // 待审核\n        '2': 'green',    // 已通过\n        '3': 'red'       // 已拒绝\n      }\n      return colorMap[status] || 'default'\n    },\n\n    // 获取状态图标\n    getStatusIcon(status) {\n      const iconMap = {\n        '1': 'clock-circle',    // 待审核\n        '2': 'check-circle',    // 已通过\n        '3': 'close-circle'     // 已拒绝\n      }\n      return iconMap[status] || 'question-circle'\n    },\n    \n    // 格式化金额\n    formatMoney(amount) {\n      if (!amount) return '0.00'\n      return Number(amount).toFixed(2)\n    },\n    \n    // 格式化日期\n    formatDate(dateStr) {\n      if (!dateStr) return ''\n      const date = new Date(dateStr)\n      return date.toLocaleString('zh-CN', {\n        year: 'numeric',\n        month: '2-digit',\n        day: '2-digit',\n        hour: '2-digit',\n        minute: '2-digit'\n      })\n    },\n\n    // 搜索处理\n    handleSearch() {\n      this.$emit('search', this.searchQuery)\n    },\n\n    // 清除搜索\n    clearSearch() {\n      this.searchQuery = ''\n      this.handleSearch()\n    },\n\n    // 排序字段变化\n    handleSortChange() {\n      this.emitSortChange()\n    },\n\n    // 切换排序方向\n    toggleSortOrder() {\n      this.sortOrder = this.sortOrder === 'desc' ? 'asc' : 'desc'\n      this.emitSortChange()\n    },\n\n    // 发送排序变化事件\n    emitSortChange() {\n      this.$emit('sort-change', {\n        sortField: this.sortField,\n        sortOrder: this.sortOrder\n      })\n    },\n\n    // 设置审核状态筛选\n    setAuditStatusFilter(status) {\n      this.auditStatusFilter = status\n      this.$emit('filter-change', {\n        auditStatus: this.auditStatusFilter\n      })\n    },\n\n    // 处理图片加载错误\n    handleImageError(event) {\n      event.target.style.display = 'none'\n      event.target.nextElementSibling.style.display = 'flex'\n    },\n    \n    // 编辑智能体\n    handleEdit(agent) {\n      this.$emit('edit', agent)\n    },\n    \n    // 删除智能体\n    handleDelete(agent) {\n      this.$emit('delete', agent)\n    },\n    \n    // 🔥 显示工作流已发布提示弹窗\n    showWorkflowPublishedModal() {\n      // 创建完全自定义的美观弹窗\n      this.$confirm({\n        title: '', // 空标题\n        icon: () => this.$createElement('span'), // 返回空span来完全去掉图标\n\n        content: () => this.$createElement('div', {\n          class: 'custom-modal-content'\n        }, [\n          // 顶部装饰条\n          this.$createElement('div', {\n            style: {\n              height: '4px',\n              background: 'linear-gradient(90deg, #ffa726 0%, #ff9800 100%)',\n              borderRadius: '2px 2px 0 0',\n              marginBottom: '32px'\n            }\n          }),\n\n          // 图标区域\n          this.$createElement('div', {\n            style: {\n              textAlign: 'center',\n              marginBottom: '24px'\n            }\n          }, [\n            this.$createElement('div', {\n              style: {\n                width: '64px',\n                height: '64px',\n                background: 'linear-gradient(135deg, #ffa726 0%, #ff9800 100%)',\n                borderRadius: '50%',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                margin: '0 auto',\n                boxShadow: '0 8px 32px rgba(255, 167, 38, 0.3)'\n              }\n            }, [\n              this.$createElement('a-icon', {\n                props: {\n                  type: 'lock',\n                  style: {\n                    fontSize: '28px',\n                    color: '#ffffff'\n                  }\n                }\n              })\n            ])\n          ]),\n\n          // 标题\n          this.$createElement('div', {\n            style: {\n              textAlign: 'center',\n              fontSize: '24px',\n              fontWeight: '700',\n              color: '#1a202c',\n              marginBottom: '16px',\n              letterSpacing: '-0.5px'\n            }\n          }, '工作流已锁定'),\n\n          // 内容\n          this.$createElement('div', {\n            style: {\n              textAlign: 'center',\n              fontSize: '16px',\n              color: '#4a5568',\n              lineHeight: '1.6',\n              marginBottom: '8px'\n            }\n          }, '您的工作流已成功发布并锁定'),\n\n          this.$createElement('div', {\n            style: {\n              textAlign: 'center',\n              fontSize: '14px',\n              color: '#718096',\n              marginBottom: '32px'\n            }\n          }, '如需修改请联系管理员解锁')\n        ]),\n\n        centered: true,\n        width: 480,\n        maskClosable: true,\n        cancelButtonProps: { style: { display: 'none' } },\n        okText: '我知道了',\n        okButtonProps: {\n          style: {\n            background: 'linear-gradient(135deg, #ffa726 0%, #ff9800 100%)',\n            border: 'none',\n            borderRadius: '12px',\n            height: '48px',\n            fontSize: '16px',\n            fontWeight: '600',\n            padding: '0 40px',\n            boxShadow: '0 4px 20px rgba(255, 167, 38, 0.4)',\n            transition: 'all 0.3s ease',\n            color: '#ffffff'\n          }\n        },\n\n        onOk: () => {\n          console.log('🎯 AgentManagement: 用户确认工作流已发布提示')\n        },\n\n        class: 'workflow-published-modal'\n      })\n    },\n    \n    // 创建智能体\n    handleCreate() {\n      this.$emit('create')\n    },\n    \n    // 分页变化\n    handlePageChange(page, pageSize) {\n      this.$emit('page-change', page, pageSize)\n      // 滚动到创作者中心顶部\n      this.scrollToCreatorCenter()\n    },\n\n    // 页面大小变化\n    handlePageSizeChange(current, size) {\n      this.$emit('page-change', current, size)\n      // 滚动到创作者中心顶部\n      this.scrollToCreatorCenter()\n    },\n\n    // 滚动到创作者中心顶部\n    scrollToCreatorCenter() {\n      this.$nextTick(() => {\n        // 查找创作者中心容器\n        const creatorCenter = document.querySelector('.creator-center')\n        if (creatorCenter) {\n          creatorCenter.scrollIntoView({\n            behavior: 'smooth',\n            block: 'start'\n          })\n        } else {\n          // 如果找不到创作者中心容器，滚动到页面顶部\n          window.scrollTo({\n            top: 0,\n            behavior: 'smooth'\n          })\n        }\n      })\n    },\n    \n    // 刷新列表\n    handleRefresh() {\n      this.$emit('refresh')\n    }\n  }\n}\n</script>\n\n<style lang=\"less\" scoped>\n.agent-management {\n  // 筛选器样式\n  .market-filters {\n    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);\n    padding: 2rem;\n    border-radius: 20px;\n    box-shadow: 0 8px 32px rgba(59, 130, 246, 0.12), 0 2px 8px rgba(0, 0, 0, 0.04);\n    margin-bottom: 2rem;\n    border: 1px solid rgba(59, 130, 246, 0.1);\n  }\n\n  .filter-row {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    gap: 2rem;\n  }\n\n  .search-box {\n    flex: 1;\n    max-width: 600px;\n  }\n\n  .search-wrapper {\n    position: relative;\n    display: flex;\n    align-items: center;\n    background: white;\n    border-radius: 12px;\n    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);\n    border: 2px solid #cbd5e1;\n    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n\n    &:hover {\n      border-color: #3b82f6;\n      box-shadow: 0 6px 20px rgba(59, 130, 246, 0.2);\n      transform: translateY(-1px);\n    }\n\n    &:focus-within {\n      border-color: #3b82f6;\n      box-shadow: 0 8px 24px rgba(59, 130, 246, 0.3);\n      transform: translateY(-1px);\n    }\n  }\n\n  .search-icon {\n    position: absolute;\n    left: 16px;\n    z-index: 2;\n    color: #64748b;\n    font-size: 18px;\n    transition: color 0.3s ease;\n  }\n\n  .search-wrapper:focus-within .search-icon {\n    color: #3b82f6;\n  }\n\n  .clear-icon {\n    position: absolute;\n    right: 16px;\n    z-index: 2;\n    color: #94a3b8;\n    font-size: 16px;\n    cursor: pointer;\n    transition: color 0.3s ease;\n\n    &:hover {\n      color: #64748b;\n    }\n  }\n\n  .search-input {\n    flex: 1;\n    border: none !important;\n    box-shadow: none !important;\n    padding-left: 48px !important;\n    padding-right: 48px !important;\n    font-size: 16px;\n    height: 48px;\n    background: transparent;\n\n    &:focus {\n      border: none !important;\n      box-shadow: none !important;\n    }\n  }\n\n  .filter-controls {\n    display: flex;\n    align-items: flex-start;\n    gap: 2rem;\n    flex-wrap: wrap;\n  }\n\n  .filter-group {\n    display: flex;\n    align-items: center;\n  }\n\n  // 🔥 排序控件样式\n  .sort-controls {\n    display: flex;\n    align-items: center;\n    gap: 8px;\n\n    .sort-label {\n      font-size: 14px;\n      color: #666;\n      font-weight: 500;\n    }\n\n    .sort-order-btn {\n      height: 32px;\n      border: 1px solid #d9d9d9;\n      border-radius: 6px;\n      background: #fff;\n      color: #666;\n      font-size: 12px;\n      transition: all 0.2s ease;\n\n      &:hover {\n        border-color: #40a9ff;\n        color: #40a9ff;\n      }\n    }\n  }\n\n  .filter-buttons {\n    display: flex;\n    gap: 8px;\n    flex-wrap: wrap;\n  }\n\n  .filter-btn {\n    display: flex;\n    align-items: center;\n    gap: 6px;\n    padding: 8px 16px;\n    border: 2px solid #e2e8f0;\n    border-radius: 12px;\n    background: white;\n    color: #64748b;\n    font-size: 14px;\n    font-weight: 500;\n    cursor: pointer;\n    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n    position: relative;\n    overflow: hidden;\n\n    &::before {\n      content: '';\n      position: absolute;\n      top: 0;\n      left: -100%;\n      width: 100%;\n      height: 100%;\n      background: linear-gradient(90deg, transparent, rgba(79, 70, 229, 0.1), transparent);\n      transition: left 0.5s ease;\n    }\n\n    &:hover::before {\n      left: 100%;\n    }\n\n    &:hover {\n      border-color: #4f46e5;\n      color: #4f46e5;\n      transform: translateY(-1px);\n      box-shadow: 0 4px 12px rgba(79, 70, 229, 0.15);\n    }\n\n    &.active {\n      background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);\n      border-color: #4f46e5;\n      color: white;\n      transform: translateY(-1px);\n      box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3);\n\n      &:hover {\n        background: linear-gradient(135deg, #4338ca 0%, #6d28d9 100%);\n      }\n    }\n  }\n\n  // 智能体网格布局\n  .agent-grid {\n    display: grid;\n    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));\n    gap: 24px;\n    margin-bottom: 24px;\n  }\n\n  // 智能体卡片\n  .agent-card {\n    background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);\n    border-radius: 20px;\n    border: 2px solid #f1f5f9;\n    overflow: hidden;\n    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);\n    position: relative;\n    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.06), 0 2px 8px rgba(0, 0, 0, 0.04);\n\n    &::before {\n      content: '';\n      position: absolute;\n      top: 0;\n      left: 0;\n      right: 0;\n      height: 4px;\n      background: linear-gradient(90deg, #e2e8f0, #e2e8f0);\n      transition: all 0.3s ease;\n      z-index: 1;\n    }\n\n    &:hover {\n      transform: translateY(-6px);\n      box-shadow: 0 20px 48px rgba(0, 0, 0, 0.12), 0 8px 24px rgba(0, 0, 0, 0.08);\n      border-color: #cbd5e1;\n\n      &::before {\n        background: linear-gradient(90deg, #3b82f6, #8b5cf6);\n      }\n    }\n\n    // 状态样式\n    &.pending {\n      &::before {\n        background: linear-gradient(90deg, #f59e0b, #d97706);\n      }\n\n      &:hover::before {\n        background: linear-gradient(90deg, #d97706, #b45309);\n      }\n    }\n\n    &.approved {\n      &::before {\n        background: linear-gradient(90deg, #10b981, #059669);\n      }\n\n      &:hover::before {\n        background: linear-gradient(90deg, #059669, #047857);\n      }\n    }\n\n    &.rejected {\n      &::before {\n        background: linear-gradient(90deg, #ef4444, #dc2626);\n      }\n\n      &:hover::before {\n        background: linear-gradient(90deg, #dc2626, #b91c1c);\n      }\n    }\n\n    // 卡片头部\n    .card-header {\n      padding: 24px 24px 20px;\n      background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);\n      border-bottom: 1px solid #e2e8f0;\n      display: flex;\n      align-items: flex-start;\n      gap: 16px;\n      position: relative;\n\n      .agent-avatar {\n        width: 64px;\n        height: 64px;\n        border-radius: 16px;\n        overflow: hidden;\n        flex-shrink: 0;\n        position: relative;\n        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);\n        border: 3px solid white;\n\n        img {\n          width: 100%;\n          height: 100%;\n          object-fit: cover;\n          transition: transform 0.3s ease;\n        }\n\n        &:hover img {\n          transform: scale(1.05);\n        }\n\n        .avatar-placeholder {\n          width: 100%;\n          height: 100%;\n          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          color: #fff;\n          font-size: 24px;\n        }\n      }\n\n      .agent-info {\n        flex: 1;\n        min-width: 0;\n\n        .agent-name {\n          margin: 0 0 8px 0;\n          font-size: 20px;\n          font-weight: 700;\n          color: #1e293b;\n          white-space: nowrap;\n          overflow: hidden;\n          text-overflow: ellipsis;\n          line-height: 1.3;\n          background: linear-gradient(135deg, #1e293b 0%, #334155 100%);\n          -webkit-background-clip: text;\n          -webkit-text-fill-color: transparent;\n          background-clip: text;\n        }\n\n        .agent-meta {\n          display: flex;\n          align-items: center;\n          gap: 8px;\n\n          .status-tag {\n            font-size: 13px;\n            font-weight: 600;\n            border-radius: 8px;\n            padding: 4px 12px;\n            border: none;\n            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n          }\n        }\n      }\n\n      .card-actions {\n        .ant-btn {\n          border: none;\n          box-shadow: none;\n\n          &:hover {\n            background: #f5f5f5;\n          }\n        }\n      }\n    }\n\n    // 卡片内容\n    .card-content {\n      padding: 20px 24px;\n\n      .agent-description {\n        margin: 0 0 20px 0;\n        height: 62px;\n        background: #f8fafc;\n        border-radius: 10px;\n        border-left: 3px solid #e2e8f0;\n        display: flex;\n        align-items: center;\n        padding: 0 16px;\n\n        p {\n          color: #64748b;\n          font-size: 14px;\n          line-height: 1.5;\n          display: -webkit-box;\n          -webkit-line-clamp: 2;\n          -webkit-box-orient: vertical;\n          overflow: hidden;\n          word-break: break-word;\n          margin: 0;\n          width: 100%;\n        }\n      }\n\n      .agent-stats {\n        display: grid;\n        grid-template-columns: repeat(3, 1fr);\n        gap: 12px;\n        margin-bottom: 16px;\n      }\n\n      .stat-item {\n        background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);\n        border: 2px solid #e2e8f0;\n        border-radius: 10px;\n        padding: 12px;\n        text-align: center;\n        transition: all 0.3s ease;\n        position: relative;\n        overflow: hidden;\n\n        &::before {\n          content: '';\n          position: absolute;\n          top: 0;\n          left: 0;\n          right: 0;\n          height: 2px;\n          background: linear-gradient(90deg, #e2e8f0, #e2e8f0);\n          transition: all 0.3s ease;\n        }\n\n        &:hover {\n          transform: translateY(-1px);\n          box-shadow: 0 6px 16px rgba(0, 0, 0, 0.08);\n          border-color: #cbd5e1;\n        }\n\n        .stat-content {\n          display: flex;\n          flex-direction: column;\n          align-items: center;\n        }\n\n        .stat-label {\n          display: block;\n          font-size: 11px;\n          color: #64748b;\n          margin-bottom: 4px;\n          font-weight: 500;\n        }\n\n        .stat-value {\n          display: block;\n          font-size: 14px;\n          font-weight: 600;\n          color: #1e293b;\n\n          &.price {\n            color: #059669;\n          }\n\n          &.revenue {\n            color: #dc2626;\n          }\n        }\n\n        &.revenue {\n          &::before {\n            background: linear-gradient(90deg, #dc2626, #b91c1c);\n          }\n        }\n      }\n\n      .audit-remark {\n        background: #fef2f2;\n        border: 1px solid #fecaca;\n        border-radius: 6px;\n        padding: 12px;\n        margin-bottom: 16px;\n\n        .remark-label {\n          font-size: 12px;\n          color: #dc2626;\n          font-weight: 600;\n          margin-bottom: 4px;\n        }\n\n        .remark-content {\n          font-size: 14px;\n          color: #7f1d1d;\n          line-height: 1.4;\n        }\n      }\n    }\n\n    // 卡片底部\n    .card-footer {\n      padding: 12px 16px;\n      background: #fafbfc;\n      border-top: 1px solid #f0f0f0;\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n\n      .create-time,\n      .update-time {\n        font-size: 12px;\n        color: #9ca3af;\n        line-height: 1.4;\n      }\n\n      .experience-link {\n        .link-button {\n          display: inline-flex;\n          align-items: center;\n          gap: 4px;\n          padding: 6px 10px;\n          background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);\n          color: white;\n          text-decoration: none;\n          font-size: 11px;\n          font-weight: 500;\n          border-radius: 6px;\n          transition: all 0.3s ease;\n          box-shadow: 0 2px 6px rgba(59, 130, 246, 0.2);\n\n          &:hover {\n            background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);\n            transform: translateY(-1px);\n            box-shadow: 0 3px 8px rgba(59, 130, 246, 0.3);\n            color: white;\n            text-decoration: none;\n          }\n\n          .anticon {\n            font-size: 11px;\n          }\n        }\n      }\n    }\n  }\n\n  // 加载状态\n  .loading-container {\n    display: flex;\n    justify-content: center;\n    align-items: center;\n    min-height: 200px;\n\n    .loading-text {\n      margin-top: 16px;\n      color: #6b7280;\n    }\n  }\n\n  // 空状态\n  .empty-container {\n    display: flex;\n    justify-content: center;\n    align-items: center;\n    min-height: 300px;\n    background: #fff;\n    border-radius: 12px;\n    border: 1px solid #e8eaec;\n  }\n\n  // 分页\n  .pagination-container {\n    display: flex;\n    justify-content: center;\n    margin-top: 32px;\n    padding: 32px 0;\n  }\n\n  .pagination-wrapper {\n    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);\n    padding: 24px 32px;\n    border-radius: 16px;\n    box-shadow: 0 8px 32px rgba(59, 130, 246, 0.12), 0 2px 8px rgba(0, 0, 0, 0.04);\n    border: 1px solid rgba(59, 130, 246, 0.1);\n    backdrop-filter: blur(10px);\n  }\n\n  .custom-pagination {\n    :global(.ant-pagination-item) {\n      border: 2px solid #e2e8f0;\n      border-radius: 12px;\n      background: white;\n      margin: 0 4px;\n      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\n\n      &:hover {\n        border-color: #3b82f6;\n        transform: translateY(-1px);\n        box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);\n      }\n\n      a {\n        color: #64748b;\n        font-weight: 500;\n        transition: color 0.3s ease;\n      }\n\n      &:hover a {\n        color: #3b82f6;\n      }\n    }\n\n    :global(.ant-pagination-item-active) {\n      background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);\n      border-color: #3b82f6;\n      transform: translateY(-1px);\n      box-shadow: 0 6px 16px rgba(59, 130, 246, 0.3);\n\n      a {\n        color: white;\n        font-weight: 600;\n      }\n\n      &:hover {\n        background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);\n        border-color: #2563eb;\n      }\n    }\n\n    :global(.ant-pagination-prev),\n    :global(.ant-pagination-next) {\n      border: 2px solid #e2e8f0;\n      border-radius: 12px;\n      background: white;\n      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\n\n      &:hover {\n        border-color: #3b82f6;\n        transform: translateY(-1px);\n        box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);\n      }\n\n      a {\n        color: #64748b;\n        transition: color 0.3s ease;\n      }\n\n      &:hover a {\n        color: #3b82f6;\n      }\n    }\n\n    :global(.ant-pagination-options) {\n      margin-left: 16px;\n\n      .ant-select .ant-select-selector {\n        border: 2px solid #e2e8f0;\n        border-radius: 12px;\n        background: white;\n        transition: all 0.3s ease;\n        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\n\n        &:hover {\n          border-color: #3b82f6;\n          box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);\n        }\n      }\n    }\n\n    :global(.ant-pagination-options-quick-jumper) input {\n      border: 2px solid #e2e8f0;\n      border-radius: 12px;\n      background: white;\n      transition: all 0.3s ease;\n      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\n\n      &:hover {\n        border-color: #3b82f6;\n        box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);\n      }\n\n      &:focus {\n        border-color: #3b82f6;\n        box-shadow: 0 6px 16px rgba(59, 130, 246, 0.3);\n      }\n    }\n\n    :global(.ant-pagination-total-text) {\n      color: #64748b;\n      font-weight: 500;\n      margin-right: 16px;\n    }\n  }\n}\n\n// 响应式设计\n@media (max-width: 768px) {\n  .agent-management {\n    .agent-grid {\n      grid-template-columns: 1fr;\n      gap: 16px;\n    }\n\n    .agent-card {\n      .card-header {\n        .agent-info {\n          .agent-meta {\n            flex-direction: column;\n            align-items: flex-start;\n            gap: 4px;\n          }\n        }\n      }\n\n      .card-content {\n        .agent-stats {\n          grid-template-columns: repeat(2, 1fr);\n          gap: 8px;\n        }\n\n        .stat-item {\n          padding: 10px;\n\n          .stat-label {\n            font-size: 10px;\n          }\n\n          .stat-value {\n            font-size: 12px;\n          }\n        }\n      }\n    }\n  }\n\n  // 筛选器响应式设计\n  @media (max-width: 1200px) {\n    .market-filters {\n      padding: 1.5rem;\n\n      .filter-row {\n        flex-direction: column;\n        gap: 1.5rem;\n        align-items: stretch;\n      }\n\n      .search-box {\n        max-width: none;\n      }\n\n      .filter-controls {\n        gap: 1rem;\n      }\n    }\n  }\n\n  @media (max-width: 768px) {\n    .market-filters {\n      padding: 1rem;\n      border-radius: 12px;\n\n      .filter-row {\n        gap: 1rem;\n      }\n\n      .filter-controls {\n        flex-direction: column;\n        gap: 1rem;\n      }\n\n      .filter-buttons {\n        justify-content: center;\n      }\n\n      .filter-btn {\n        padding: 6px 12px;\n        font-size: 13px;\n      }\n    }\n  }\n\n  // 分页响应式设计\n  @media (max-width: 768px) {\n    .pagination-wrapper {\n      padding: 16px 20px;\n      border-radius: 12px;\n    }\n\n    .custom-pagination {\n      :global(.ant-pagination-item),\n      :global(.ant-pagination-prev),\n      :global(.ant-pagination-next) {\n        margin: 0 2px;\n        border-radius: 8px;\n      }\n\n      :global(.ant-pagination-options) {\n        margin-left: 8px;\n        margin-top: 8px;\n      }\n\n      :global(.ant-pagination-total-text) {\n        margin-right: 8px;\n        font-size: 12px;\n      }\n    }\n  }\n\n  // 🔥 工作流已发布提示弹窗样式 - 全新现代化设计\n  :global(.workflow-published-modal) {\n    .ant-modal-content {\n      border-radius: 24px !important;\n      overflow: hidden;\n      box-shadow: 0 32px 64px rgba(0, 0, 0, 0.12), 0 0 0 1px rgba(255, 255, 255, 0.05) !important;\n      border: none;\n      background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);\n      backdrop-filter: blur(20px);\n    }\n\n    .ant-modal-header {\n      display: none !important; // 完全隐藏默认头部\n    }\n\n    // 🔥 隐藏左上角的默认图标\n    .ant-modal-confirm-body-wrapper {\n      .ant-modal-confirm-body {\n        .ant-modal-confirm-content {\n          margin-left: 0 !important; // 去掉图标占位的左边距\n        }\n      }\n    }\n\n    // 🔥 隐藏默认的标题和图标区域\n    .ant-modal-confirm-title {\n      display: none !important;\n    }\n\n    .ant-modal-confirm-btns {\n      margin-top: 0 !important;\n    }\n\n    // 🔥 只在这个弹窗中隐藏anticon\n    .ant-modal-confirm-body > .anticon {\n      display: none !important;\n    }\n\n    .ant-modal-body {\n      padding: 40px 32px 32px !important;\n      background: transparent;\n\n      .custom-modal-content {\n        position: relative;\n      }\n    }\n\n    .ant-modal-footer {\n      border-top: none !important;\n      padding: 0 32px 40px !important;\n      text-align: center;\n      background: transparent;\n\n      .ant-btn {\n        min-width: 160px;\n        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n        position: relative;\n        overflow: hidden;\n\n        &::before {\n          content: '';\n          position: absolute;\n          top: 0;\n          left: -100%;\n          width: 100%;\n          height: 100%;\n          background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);\n          transition: left 0.5s;\n        }\n\n        &:hover {\n          transform: translateY(-2px) scale(1.02);\n          box-shadow: 0 12px 40px rgba(255, 167, 38, 0.5) !important;\n\n          &::before {\n            left: 100%;\n          }\n        }\n\n        &:active {\n          transform: translateY(-1px) scale(1.01);\n        }\n      }\n    }\n\n    // 关闭按钮样式\n    .ant-modal-close {\n      top: 20px;\n      right: 20px;\n      z-index: 10;\n\n      .ant-modal-close-x {\n        width: 36px;\n        height: 36px;\n        line-height: 36px;\n        border-radius: 50%;\n        background: rgba(0, 0, 0, 0.04);\n        transition: all 0.3s ease;\n\n        &:hover {\n          background: rgba(0, 0, 0, 0.08);\n          transform: rotate(90deg);\n        }\n      }\n    }\n\n    // 弹窗动画效果 - 更流畅的动画\n    &.ant-modal {\n      .ant-modal-content {\n        animation: modalSlideIn 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);\n      }\n    }\n\n    @keyframes modalSlideIn {\n      from {\n        opacity: 0;\n        transform: scale(0.8) translateY(-20px) rotateX(10deg);\n      }\n      to {\n        opacity: 1;\n        transform: scale(1) translateY(0) rotateX(0deg);\n      }\n    }\n\n    // 遮罩层样式\n    &.ant-modal .ant-modal-mask {\n      background: rgba(0, 0, 0, 0.6);\n      backdrop-filter: blur(8px);\n    }\n  }\n}\n</style>\n"]}]}