package org.jeecg.modules.api.service.impl;

import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.api.config.UserActivityConfig;
import org.jeecg.modules.api.service.IUserActivityPerformanceMonitor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.lang.management.ManagementFactory;
import java.lang.management.MemoryMXBean;
import java.lang.management.ThreadMXBean;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

/**
 * @Description: 用户活跃状态性能监控服务实现
 * @Author: AigcView
 * @Date: 2025-01-30
 * @Version: V1.0
 */
@Slf4j
@Service
public class UserActivityPerformanceMonitorImpl implements IUserActivityPerformanceMonitor {

    @Autowired
    private UserActivityConfig userActivityConfig;

    // ==================== 性能监控数据结构 ====================
    
    /**
     * API性能数据存储
     * Key: API路径, Value: 性能统计数据
     */
    private final Map<String, ApiPerformanceData> apiPerformanceMap = new ConcurrentHashMap<>();
    
    /**
     * 系统整体性能数据
     */
    private final SystemPerformanceData systemPerformanceData = new SystemPerformanceData();
    
    /**
     * 降级状态管理
     */
    private final AtomicBoolean isDegraded = new AtomicBoolean(false);
    private volatile long degradationStartTime = 0;
    private volatile long degradationEndTime = 0;
    private volatile String degradationReason = "";
    
    /**
     * 性能告警列表
     */
    private final List<Map<String, Object>> performanceAlerts = Collections.synchronizedList(new ArrayList<>());
    
    /**
     * JVM监控Bean
     */
    private MemoryMXBean memoryMXBean;
    private ThreadMXBean threadMXBean;

    @PostConstruct
    public void init() {
        memoryMXBean = ManagementFactory.getMemoryMXBean();
        threadMXBean = ManagementFactory.getThreadMXBean();
        log.info("用户活跃状态性能监控服务初始化完成");
    }

    // ==================== 性能监控实现 ====================

    @Override
    public void recordApiPerformance(String apiPath, long responseTime, boolean isCriticalApi, boolean success) {
        try {
            // 1. 更新API级别的性能数据
            ApiPerformanceData apiData = apiPerformanceMap.computeIfAbsent(apiPath, k -> new ApiPerformanceData());
            apiData.recordCall(responseTime, success, isCriticalApi);
            
            // 2. 更新系统级别的性能数据
            systemPerformanceData.recordCall(responseTime, success, isCriticalApi);
            
            // 3. 检查性能阈值
            if (isPerformanceThresholdExceeded(apiPath, responseTime)) {
                addPerformanceAlert(apiPath, responseTime, isCriticalApi);
            }
            
            // 4. 检查是否需要自动降级
            checkAutoDegrade();
            
            log.debug("记录API性能数据 - API: {}, 响应时间: {}ms, 关键API: {}, 成功: {}", 
                apiPath, responseTime, isCriticalApi, success);
                
        } catch (Exception e) {
            log.error("记录API性能数据失败 - API: {}, 错误: {}", apiPath, e.getMessage(), e);
        }
    }

    @Override
    public Map<String, Object> getApiPerformanceStats(String apiPath) {
        Map<String, Object> stats = new HashMap<>();
        
        try {
            if (apiPath == null) {
                // 返回全局统计
                stats = systemPerformanceData.getStats();
            } else {
                // 返回特定API统计
                ApiPerformanceData apiData = apiPerformanceMap.get(apiPath);
                if (apiData != null) {
                    stats = apiData.getStats();
                    stats.put("apiPath", apiPath);
                } else {
                    stats.put("apiPath", apiPath);
                    stats.put("message", "API性能数据不存在");
                }
            }
            
            stats.put("timestamp", System.currentTimeMillis());
            
        } catch (Exception e) {
            log.error("获取API性能统计失败 - API: {}, 错误: {}", apiPath, e.getMessage(), e);
            stats.put("error", e.getMessage());
        }
        
        return stats;
    }

    @Override
    public Map<String, Object> getSystemPerformanceStats() {
        Map<String, Object> stats = new HashMap<>();
        
        try {
            // 系统性能统计
            stats.putAll(systemPerformanceData.getStats());
            
            // API数量统计
            stats.put("totalApis", apiPerformanceMap.size());
            stats.put("degraded", isDegraded.get());
            stats.put("degradationReason", degradationReason);
            
            if (isDegraded.get()) {
                stats.put("degradationStartTime", degradationStartTime);
                stats.put("degradationDuration", System.currentTimeMillis() - degradationStartTime);
            }
            
            // 告警数量
            stats.put("alertCount", performanceAlerts.size());
            
            stats.put("timestamp", System.currentTimeMillis());
            
        } catch (Exception e) {
            log.error("获取系统性能统计失败 - 错误: {}", e.getMessage(), e);
            stats.put("error", e.getMessage());
        }
        
        return stats;
    }

    @Override
    public Map<String, Object> getRecentPerformanceStats(int minutes) {
        Map<String, Object> stats = new HashMap<>();
        
        try {
            long timeThreshold = System.currentTimeMillis() - (minutes * 60 * 1000L);
            
            // 过滤最近N分钟的数据
            Map<String, Object> recentStats = new HashMap<>();
            
            // 系统级别的最近统计
            recentStats.putAll(systemPerformanceData.getRecentStats(timeThreshold));
            
            // API级别的最近统计
            Map<String, Map<String, Object>> apiRecentStats = new HashMap<>();
            for (Map.Entry<String, ApiPerformanceData> entry : apiPerformanceMap.entrySet()) {
                Map<String, Object> apiStats = entry.getValue().getRecentStats(timeThreshold);
                if (!apiStats.isEmpty()) {
                    apiRecentStats.put(entry.getKey(), apiStats);
                }
            }
            
            recentStats.put("apiStats", apiRecentStats);
            recentStats.put("timeWindow", minutes + "分钟");
            recentStats.put("timestamp", System.currentTimeMillis());
            
            stats = recentStats;
            
        } catch (Exception e) {
            log.error("获取最近性能统计失败 - 时间窗口: {}分钟, 错误: {}", minutes, e.getMessage(), e);
            stats.put("error", e.getMessage());
        }
        
        return stats;
    }

    @Override
    public boolean isPerformanceThresholdExceeded(String apiPath, long responseTime) {
        try {
            // 检查全局阈值
            if (responseTime > userActivityConfig.getPerformanceThreshold()) {
                return true;
            }
            
            // 检查关键API的特殊阈值
            if (userActivityConfig.isCriticalApi(apiPath)) {
                long criticalThreshold = userActivityConfig.getPerformanceThreshold() / 2; // 关键API阈值更严格
                return responseTime > criticalThreshold;
            }
            
            return false;
            
        } catch (Exception e) {
            log.error("检查性能阈值失败 - API: {}, 响应时间: {}ms, 错误: {}", apiPath, responseTime, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public List<Map<String, Object>> getPerformanceAlerts() {
        try {
            // 返回最近的告警信息（最多100条）
            synchronized (performanceAlerts) {
                return new ArrayList<>(performanceAlerts.stream()
                    .sorted((a, b) -> Long.compare((Long) b.get("timestamp"), (Long) a.get("timestamp")))
                    .limit(100)
                    .collect(Collectors.toList()));
            }
        } catch (Exception e) {
            log.error("获取性能告警失败 - 错误: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    // ==================== 降级机制实现 ====================

    @Override
    public boolean shouldDegrade() {
        try {
            // 如果已经手动降级，直接返回
            if (isDegraded.get()) {
                return true;
            }
            
            // 检查自动降级条件
            return checkAutoDegradeConditions();
            
        } catch (Exception e) {
            log.error("检查系统降级状态失败 - 错误: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean shouldDegradeApi(String apiPath, boolean isCriticalApi) {
        try {
            // 关键API不降级
            if (isCriticalApi) {
                return false;
            }
            
            // 检查系统是否需要降级
            if (shouldDegrade()) {
                return true;
            }
            
            // 检查特定API的性能
            ApiPerformanceData apiData = apiPerformanceMap.get(apiPath);
            if (apiData != null) {
                return apiData.shouldDegrade(userActivityConfig.getPerformanceThreshold());
            }
            
            return false;
            
        } catch (Exception e) {
            log.error("检查API降级状态失败 - API: {}, 错误: {}", apiPath, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean triggerDegradation(String reason, int durationMinutes) {
        try {
            isDegraded.set(true);
            degradationStartTime = System.currentTimeMillis();
            degradationEndTime = degradationStartTime + (durationMinutes * 60 * 1000L);
            degradationReason = reason;
            
            log.warn("手动触发系统降级 - 原因: {}, 持续时间: {}分钟", reason, durationMinutes);
            
            // 添加降级告警
            addDegradationAlert("手动降级", reason, durationMinutes);
            
            return true;
            
        } catch (Exception e) {
            log.error("触发系统降级失败 - 原因: {}, 错误: {}", reason, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean recoverFromDegradation(String reason) {
        try {
            if (isDegraded.get()) {
                isDegraded.set(false);
                long degradationDuration = System.currentTimeMillis() - degradationStartTime;
                
                log.info("手动恢复系统降级 - 原因: {}, 降级持续时间: {}ms", reason, degradationDuration);
                
                // 添加恢复告警
                addRecoveryAlert("手动恢复", reason, degradationDuration);
                
                // 重置降级相关数据
                degradationStartTime = 0;
                degradationEndTime = 0;
                degradationReason = "";
                
                return true;
            }
            
            return false;
            
        } catch (Exception e) {
            log.error("恢复系统降级失败 - 原因: {}, 错误: {}", reason, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public Map<String, Object> getDegradationStatus() {
        Map<String, Object> status = new HashMap<>();

        try {
            status.put("degraded", isDegraded.get());
            status.put("degradationReason", degradationReason);

            if (isDegraded.get()) {
                status.put("degradationStartTime", degradationStartTime);
                status.put("degradationEndTime", degradationEndTime);
                status.put("degradationDuration", System.currentTimeMillis() - degradationStartTime);

                // 检查是否应该自动恢复
                if (degradationEndTime > 0 && System.currentTimeMillis() > degradationEndTime) {
                    status.put("shouldAutoRecover", true);
                }
            }

            status.put("timestamp", System.currentTimeMillis());

        } catch (Exception e) {
            log.error("获取降级状态失败 - 错误: {}", e.getMessage(), e);
            status.put("error", e.getMessage());
        }

        return status;
    }

    @Override
    public boolean isDegraded() {
        try {
            // 检查手动降级状态
            if (isDegraded.get()) {
                // 检查是否到了自动恢复时间
                if (degradationEndTime > 0 && System.currentTimeMillis() > degradationEndTime) {
                    recoverFromDegradation("自动恢复");
                    return false;
                }
                return true;
            }

            return false;

        } catch (Exception e) {
            log.error("检查降级状态失败 - 错误: {}", e.getMessage(), e);
            return false;
        }
    }

    // ==================== 系统负载监控实现 ====================

    @Override
    public Map<String, Object> getSystemLoad() {
        Map<String, Object> load = new HashMap<>();

        try {
            // CPU使用率
            double cpuUsage = ((com.sun.management.OperatingSystemMXBean)
                ManagementFactory.getOperatingSystemMXBean()).getProcessCpuLoad() * 100;
            load.put("cpuUsage", Math.round(cpuUsage * 100.0) / 100.0);

            // 系统负载平均值
            double systemLoadAverage = ManagementFactory.getOperatingSystemMXBean().getSystemLoadAverage();
            load.put("systemLoadAverage", systemLoadAverage);

            // 可用处理器数量
            int availableProcessors = Runtime.getRuntime().availableProcessors();
            load.put("availableProcessors", availableProcessors);

            // 负载评估
            if (systemLoadAverage > 0) {
                double loadPercentage = (systemLoadAverage / availableProcessors) * 100;
                load.put("loadPercentage", Math.round(loadPercentage * 100.0) / 100.0);

                if (loadPercentage > 80) {
                    load.put("loadLevel", "HIGH");
                } else if (loadPercentage > 60) {
                    load.put("loadLevel", "MEDIUM");
                } else {
                    load.put("loadLevel", "LOW");
                }
            }

            load.put("timestamp", System.currentTimeMillis());

        } catch (Exception e) {
            log.error("获取系统负载失败 - 错误: {}", e.getMessage(), e);
            load.put("error", e.getMessage());
        }

        return load;
    }

    @Override
    public Map<String, Object> getJvmMemoryInfo() {
        Map<String, Object> memory = new HashMap<>();

        try {
            // 堆内存信息
            long heapUsed = memoryMXBean.getHeapMemoryUsage().getUsed();
            long heapMax = memoryMXBean.getHeapMemoryUsage().getMax();
            long heapCommitted = memoryMXBean.getHeapMemoryUsage().getCommitted();

            memory.put("heapUsed", heapUsed);
            memory.put("heapMax", heapMax);
            memory.put("heapCommitted", heapCommitted);
            memory.put("heapUsagePercentage", Math.round((double) heapUsed / heapMax * 100 * 100.0) / 100.0);

            // 非堆内存信息
            long nonHeapUsed = memoryMXBean.getNonHeapMemoryUsage().getUsed();
            long nonHeapMax = memoryMXBean.getNonHeapMemoryUsage().getMax();
            long nonHeapCommitted = memoryMXBean.getNonHeapMemoryUsage().getCommitted();

            memory.put("nonHeapUsed", nonHeapUsed);
            memory.put("nonHeapMax", nonHeapMax);
            memory.put("nonHeapCommitted", nonHeapCommitted);

            // 内存使用评估
            double heapUsagePercentage = (double) heapUsed / heapMax * 100;
            if (heapUsagePercentage > 85) {
                memory.put("memoryLevel", "HIGH");
            } else if (heapUsagePercentage > 70) {
                memory.put("memoryLevel", "MEDIUM");
            } else {
                memory.put("memoryLevel", "LOW");
            }

            memory.put("timestamp", System.currentTimeMillis());

        } catch (Exception e) {
            log.error("获取JVM内存信息失败 - 错误: {}", e.getMessage(), e);
            memory.put("error", e.getMessage());
        }

        return memory;
    }

    @Override
    public Map<String, Object> getThreadPoolStatus() {
        Map<String, Object> threadPool = new HashMap<>();

        try {
            // 线程信息
            int threadCount = threadMXBean.getThreadCount();
            int peakThreadCount = threadMXBean.getPeakThreadCount();
            int daemonThreadCount = threadMXBean.getDaemonThreadCount();

            threadPool.put("threadCount", threadCount);
            threadPool.put("peakThreadCount", peakThreadCount);
            threadPool.put("daemonThreadCount", daemonThreadCount);
            threadPool.put("nonDaemonThreadCount", threadCount - daemonThreadCount);

            // 线程状态评估
            if (threadCount > 200) {
                threadPool.put("threadLevel", "HIGH");
            } else if (threadCount > 100) {
                threadPool.put("threadLevel", "MEDIUM");
            } else {
                threadPool.put("threadLevel", "LOW");
            }

            threadPool.put("timestamp", System.currentTimeMillis());

        } catch (Exception e) {
            log.error("获取线程池状态失败 - 错误: {}", e.getMessage(), e);
            threadPool.put("error", e.getMessage());
        }

        return threadPool;
    }

    @Override
    public Map<String, Object> getDatabaseConnectionPoolStatus() {
        Map<String, Object> dbPool = new HashMap<>();

        try {
            // 这里可以集成具体的数据库连接池监控
            // 由于需要依赖具体的连接池实现，这里提供框架
            dbPool.put("status", "MONITORING_NOT_IMPLEMENTED");
            dbPool.put("message", "数据库连接池监控需要集成具体的连接池实现");
            dbPool.put("timestamp", System.currentTimeMillis());

        } catch (Exception e) {
            log.error("获取数据库连接池状态失败 - 错误: {}", e.getMessage(), e);
            dbPool.put("error", e.getMessage());
        }

        return dbPool;
    }

    @Override
    public Map<String, Object> getRedisConnectionStatus() {
        Map<String, Object> redis = new HashMap<>();

        try {
            // 这里可以集成Redis连接监控
            // 由于需要依赖Redis连接，这里提供框架
            redis.put("status", "MONITORING_NOT_IMPLEMENTED");
            redis.put("message", "Redis连接监控需要集成Redis连接池");
            redis.put("timestamp", System.currentTimeMillis());

        } catch (Exception e) {
            log.error("获取Redis连接状态失败 - 错误: {}", e.getMessage(), e);
            redis.put("error", e.getMessage());
        }

        return redis;
    }

    // ==================== 辅助方法 ====================

    /**
     * 检查自动降级条件
     */
    private boolean checkAutoDegradeConditions() {
        try {
            // 检查系统负载
            Map<String, Object> systemLoad = getSystemLoad();
            String loadLevel = (String) systemLoad.get("loadLevel");
            if ("HIGH".equals(loadLevel)) {
                return true;
            }

            // 检查内存使用
            Map<String, Object> memoryInfo = getJvmMemoryInfo();
            String memoryLevel = (String) memoryInfo.get("memoryLevel");
            if ("HIGH".equals(memoryLevel)) {
                return true;
            }

            // 检查整体性能
            if (systemPerformanceData.shouldDegrade(userActivityConfig.getPerformanceThreshold())) {
                return true;
            }

            return false;

        } catch (Exception e) {
            log.error("检查自动降级条件失败 - 错误: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 检查自动降级
     */
    private void checkAutoDegrade() {
        try {
            if (!isDegraded.get() && checkAutoDegradeConditions()) {
                triggerDegradation("自动降级", 10); // 自动降级10分钟
            }
        } catch (Exception e) {
            log.error("自动降级检查失败 - 错误: {}", e.getMessage(), e);
        }
    }

    /**
     * 添加性能告警
     */
    private void addPerformanceAlert(String apiPath, long responseTime, boolean isCriticalApi) {
        try {
            Map<String, Object> alert = new HashMap<>();
            alert.put("type", "PERFORMANCE_THRESHOLD_EXCEEDED");
            alert.put("apiPath", apiPath);
            alert.put("responseTime", responseTime);
            alert.put("threshold", userActivityConfig.getPerformanceThreshold());
            alert.put("isCriticalApi", isCriticalApi);
            alert.put("timestamp", System.currentTimeMillis());

            synchronized (performanceAlerts) {
                performanceAlerts.add(alert);
                // 保持告警列表大小不超过1000条
                if (performanceAlerts.size() > 1000) {
                    performanceAlerts.remove(0);
                }
            }

            log.warn("性能告警 - API: {}, 响应时间: {}ms, 阈值: {}ms",
                apiPath, responseTime, userActivityConfig.getPerformanceThreshold());

        } catch (Exception e) {
            log.error("添加性能告警失败 - 错误: {}", e.getMessage(), e);
        }
    }

    /**
     * 添加降级告警
     */
    private void addDegradationAlert(String type, String reason, int durationMinutes) {
        try {
            Map<String, Object> alert = new HashMap<>();
            alert.put("type", "SYSTEM_DEGRADATION");
            alert.put("degradationType", type);
            alert.put("reason", reason);
            alert.put("durationMinutes", durationMinutes);
            alert.put("timestamp", System.currentTimeMillis());

            synchronized (performanceAlerts) {
                performanceAlerts.add(alert);
            }

        } catch (Exception e) {
            log.error("添加降级告警失败 - 错误: {}", e.getMessage(), e);
        }
    }

    /**
     * 添加恢复告警
     */
    private void addRecoveryAlert(String type, String reason, long degradationDuration) {
        try {
            Map<String, Object> alert = new HashMap<>();
            alert.put("type", "SYSTEM_RECOVERY");
            alert.put("recoveryType", type);
            alert.put("reason", reason);
            alert.put("degradationDuration", degradationDuration);
            alert.put("timestamp", System.currentTimeMillis());

            synchronized (performanceAlerts) {
                performanceAlerts.add(alert);
            }

        } catch (Exception e) {
            log.error("添加恢复告警失败 - 错误: {}", e.getMessage(), e);
        }
    }

    // ==================== 简化实现的方法 ====================

    @Override
    public Map<String, Object> analyzePerformanceBottlenecks() {
        Map<String, Object> analysis = new HashMap<>();
        analysis.put("status", "ANALYSIS_NOT_IMPLEMENTED");
        analysis.put("message", "性能瓶颈分析功能待实现");
        return analysis;
    }

    @Override
    public List<Map<String, Object>> getPerformanceOptimizationSuggestions() {
        return new ArrayList<>();
    }

    @Override
    public Map<String, Object> generatePerformanceReport(long startTime, long endTime) {
        Map<String, Object> report = new HashMap<>();
        report.put("status", "REPORT_NOT_IMPLEMENTED");
        report.put("message", "性能报告生成功能待实现");
        return report;
    }

    @Override
    public boolean updatePerformanceThresholds(Map<String, Object> thresholds) {
        return false;
    }

    @Override
    public boolean updateDegradationConfig(Map<String, Object> degradationConfig) {
        return false;
    }

    @Override
    public Map<String, Object> getCurrentConfig() {
        Map<String, Object> config = new HashMap<>();
        config.put("performanceThreshold", userActivityConfig.getPerformanceThreshold());
        config.put("enablePerformanceMonitoring", userActivityConfig.getEnablePerformanceMonitoring());
        return config;
    }

    @Override
    public int cleanExpiredPerformanceData(int retentionHours) {
        return 0;
    }

    @Override
    public boolean resetPerformanceStats() {
        try {
            apiPerformanceMap.clear();
            systemPerformanceData.reset();
            performanceAlerts.clear();
            return true;
        } catch (Exception e) {
            log.error("重置性能统计失败 - 错误: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public String exportPerformanceData(String format) {
        return "{}";
    }

    @Override
    public Map<String, Object> getRealTimeMetrics() {
        return getSystemPerformanceStats();
    }

    @Override
    public Map<String, Object> getApiCallFrequencyStats(int timeWindowMinutes) {
        return getRecentPerformanceStats(timeWindowMinutes);
    }

    @Override
    public Map<String, Object> getErrorRateStats(int timeWindowMinutes) {
        return getRecentPerformanceStats(timeWindowMinutes);
    }

    @Override
    public Map<String, Object> checkSystemHealth() {
        Map<String, Object> health = new HashMap<>();
        health.put("systemLoad", getSystemLoad());
        health.put("jvmMemory", getJvmMemoryInfo());
        health.put("threadPool", getThreadPoolStatus());
        health.put("degradationStatus", getDegradationStatus());
        return health;
    }

    // ==================== 内部数据结构类 ====================

    /**
     * API性能数据
     */
    private static class ApiPerformanceData {
        private final AtomicLong totalCalls = new AtomicLong(0);
        private final AtomicLong successCalls = new AtomicLong(0);
        private final AtomicLong totalResponseTime = new AtomicLong(0);
        private final AtomicLong maxResponseTime = new AtomicLong(0);
        private final AtomicLong minResponseTime = new AtomicLong(Long.MAX_VALUE);
        private volatile boolean isCriticalApi = false;
        private volatile long lastCallTime = 0;

        public void recordCall(long responseTime, boolean success, boolean critical) {
            totalCalls.incrementAndGet();
            if (success) {
                successCalls.incrementAndGet();
            }

            totalResponseTime.addAndGet(responseTime);

            // 更新最大响应时间
            long currentMax = maxResponseTime.get();
            while (responseTime > currentMax && !maxResponseTime.compareAndSet(currentMax, responseTime)) {
                currentMax = maxResponseTime.get();
            }

            // 更新最小响应时间
            long currentMin = minResponseTime.get();
            while (responseTime < currentMin && !minResponseTime.compareAndSet(currentMin, responseTime)) {
                currentMin = minResponseTime.get();
            }

            isCriticalApi = critical;
            lastCallTime = System.currentTimeMillis();
        }

        public Map<String, Object> getStats() {
            Map<String, Object> stats = new HashMap<>();
            long calls = totalCalls.get();

            stats.put("totalCalls", calls);
            stats.put("successCalls", successCalls.get());
            stats.put("errorCalls", calls - successCalls.get());

            if (calls > 0) {
                stats.put("successRate", Math.round((double) successCalls.get() / calls * 100 * 100.0) / 100.0);
                stats.put("errorRate", Math.round((double) (calls - successCalls.get()) / calls * 100 * 100.0) / 100.0);
                stats.put("avgResponseTime", Math.round((double) totalResponseTime.get() / calls * 100.0) / 100.0);
            } else {
                stats.put("successRate", 0.0);
                stats.put("errorRate", 0.0);
                stats.put("avgResponseTime", 0.0);
            }

            stats.put("maxResponseTime", maxResponseTime.get() == 0 ? 0 : maxResponseTime.get());
            stats.put("minResponseTime", minResponseTime.get() == Long.MAX_VALUE ? 0 : minResponseTime.get());
            stats.put("isCriticalApi", isCriticalApi);
            stats.put("lastCallTime", lastCallTime);

            return stats;
        }

        public Map<String, Object> getRecentStats(long timeThreshold) {
            if (lastCallTime < timeThreshold) {
                return new HashMap<>();
            }
            return getStats();
        }

        public boolean shouldDegrade(long threshold) {
            long calls = totalCalls.get();
            if (calls > 0) {
                double avgResponseTime = (double) totalResponseTime.get() / calls;
                return avgResponseTime > threshold;
            }
            return false;
        }
    }

    /**
     * 系统性能数据
     */
    private static class SystemPerformanceData {
        private final AtomicLong totalCalls = new AtomicLong(0);
        private final AtomicLong successCalls = new AtomicLong(0);
        private final AtomicLong criticalCalls = new AtomicLong(0);
        private final AtomicLong totalResponseTime = new AtomicLong(0);
        private final AtomicLong maxResponseTime = new AtomicLong(0);
        private final AtomicLong minResponseTime = new AtomicLong(Long.MAX_VALUE);
        private volatile long lastCallTime = 0;

        public void recordCall(long responseTime, boolean success, boolean critical) {
            totalCalls.incrementAndGet();
            if (success) {
                successCalls.incrementAndGet();
            }
            if (critical) {
                criticalCalls.incrementAndGet();
            }

            totalResponseTime.addAndGet(responseTime);

            // 更新最大响应时间
            long currentMax = maxResponseTime.get();
            while (responseTime > currentMax && !maxResponseTime.compareAndSet(currentMax, responseTime)) {
                currentMax = maxResponseTime.get();
            }

            // 更新最小响应时间
            long currentMin = minResponseTime.get();
            while (responseTime < currentMin && !minResponseTime.compareAndSet(currentMin, responseTime)) {
                currentMin = minResponseTime.get();
            }

            lastCallTime = System.currentTimeMillis();
        }

        public Map<String, Object> getStats() {
            Map<String, Object> stats = new HashMap<>();
            long calls = totalCalls.get();

            stats.put("totalCalls", calls);
            stats.put("successCalls", successCalls.get());
            stats.put("errorCalls", calls - successCalls.get());
            stats.put("criticalCalls", criticalCalls.get());

            if (calls > 0) {
                stats.put("successRate", Math.round((double) successCalls.get() / calls * 100 * 100.0) / 100.0);
                stats.put("errorRate", Math.round((double) (calls - successCalls.get()) / calls * 100 * 100.0) / 100.0);
                stats.put("criticalRate", Math.round((double) criticalCalls.get() / calls * 100 * 100.0) / 100.0);
                stats.put("avgResponseTime", Math.round((double) totalResponseTime.get() / calls * 100.0) / 100.0);
            } else {
                stats.put("successRate", 0.0);
                stats.put("errorRate", 0.0);
                stats.put("criticalRate", 0.0);
                stats.put("avgResponseTime", 0.0);
            }

            stats.put("maxResponseTime", maxResponseTime.get() == 0 ? 0 : maxResponseTime.get());
            stats.put("minResponseTime", minResponseTime.get() == Long.MAX_VALUE ? 0 : minResponseTime.get());
            stats.put("lastCallTime", lastCallTime);

            return stats;
        }

        public Map<String, Object> getRecentStats(long timeThreshold) {
            if (lastCallTime < timeThreshold) {
                return new HashMap<>();
            }
            return getStats();
        }

        public boolean shouldDegrade(long threshold) {
            long calls = totalCalls.get();
            if (calls > 0) {
                double avgResponseTime = (double) totalResponseTime.get() / calls;
                return avgResponseTime > threshold;
            }
            return false;
        }

        public void reset() {
            totalCalls.set(0);
            successCalls.set(0);
            criticalCalls.set(0);
            totalResponseTime.set(0);
            maxResponseTime.set(0);
            minResponseTime.set(Long.MAX_VALUE);
            lastCallTime = 0;
        }
    }
}
