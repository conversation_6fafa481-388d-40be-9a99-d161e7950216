<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>轮播图API测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .btn {
            padding: 10px 20px;
            background: #3b82f6;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            margin: 10px 5px;
        }
        
        .btn:hover {
            background: #2563eb;
        }
        
        .result {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 6px;
            border-left: 4px solid #3b82f6;
        }
        
        .error {
            border-left-color: #ef4444;
            background: #fef2f2;
            color: #dc2626;
        }
        
        .success {
            border-left-color: #10b981;
            background: #f0fdf4;
            color: #059669;
        }
        
        .carousel-item {
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            padding: 15px;
            margin: 10px 0;
            background: white;
        }
        
        .carousel-item h3 {
            margin: 0 0 10px 0;
            color: #1f2937;
        }
        
        .carousel-item .badge {
            display: inline-block;
            padding: 4px 8px;
            background: #3b82f6;
            color: white;
            border-radius: 4px;
            font-size: 0.8rem;
            margin-bottom: 8px;
        }
        
        .carousel-item .description {
            color: #6b7280;
            margin: 8px 0;
        }
        
        .carousel-item .action {
            margin-top: 10px;
        }
        
        .carousel-item .action a {
            color: #3b82f6;
            text-decoration: none;
            font-weight: 500;
        }
        
        pre {
            background: #1f2937;
            color: #f9fafb;
            padding: 15px;
            border-radius: 6px;
            overflow-x: auto;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>首页API接口测试</h1>
        <p>测试首页所有数据API接口调用（轮播图、功能特性、统计数据）</p>
        
        <div>
            <button class="btn" onclick="testCarouselAPI()">测试轮播图API</button>
            <button class="btn" onclick="testFeaturesAPI()">测试功能特性API</button>
            <button class="btn" onclick="testStatsAPI()">测试统计数据API</button>
            <button class="btn" onclick="testAllAPIs()">测试所有API</button>
            <button class="btn" onclick="clearResults()">清空结果</button>
        </div>
        
        <div id="results"></div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:8080/jeecg-boot';
        
        async function testCarouselAPI() {
            const resultsDiv = document.getElementById('results');
            
            try {
                showResult('正在调用轮播图API...', 'info');
                
                const response = await fetch(`${API_BASE_URL}/aigc/aigcHomeCarousel/list?status=1&pageNo=1&pageSize=10`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        // 如果需要认证，在这里添加token
                        // 'X-Access-Token': 'your-token-here'
                    }
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                
                showResult('API调用成功！', 'success');
                showResult(`<pre>${JSON.stringify(data, null, 2)}</pre>`, 'info');
                
                // 如果有数据，显示格式化的轮播图信息
                if (data.success && data.result && data.result.records) {
                    showCarouselData(data.result.records);
                }
                
            } catch (error) {
                console.error('API调用失败:', error);
                showResult(`API调用失败: ${error.message}`, 'error');
                
                // 显示可能的解决方案
                showResult(`
                    <strong>可能的解决方案:</strong><br>
                    1. 确保后端服务已启动 (http://localhost:8080)<br>
                    2. 检查数据库连接是否正常<br>
                    3. 确认API路径是否正确<br>
                    4. 检查CORS配置是否允许跨域请求
                `, 'error');
            }
        }

        async function testFeaturesAPI() {
            try {
                showResult('正在调用功能特性API...', 'info');

                const response = await fetch(`${API_BASE_URL}/aigc/websiteFeatures/list?status=1&pageNo=1&pageSize=10`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();

                showResult('功能特性API调用成功！', 'success');
                showResult(`<pre>${JSON.stringify(data, null, 2)}</pre>`, 'info');

                if (data.success && data.result && data.result.records) {
                    showFeaturesData(data.result.records);
                }

            } catch (error) {
                console.error('功能特性API调用失败:', error);
                showResult(`功能特性API调用失败: ${error.message}`, 'error');
            }
        }

        async function testStatsAPI() {
            try {
                showResult('正在调用统计数据API...', 'info');

                const response = await fetch(`${API_BASE_URL}/aigc/websiteStats/list?status=1&pageNo=1&pageSize=10`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();

                showResult('统计数据API调用成功！', 'success');
                showResult(`<pre>${JSON.stringify(data, null, 2)}</pre>`, 'info');

                if (data.success && data.result && data.result.records) {
                    showStatsData(data.result.records);
                }

            } catch (error) {
                console.error('统计数据API调用失败:', error);
                showResult(`统计数据API调用失败: ${error.message}`, 'error');
            }
        }

        async function testAllAPIs() {
            showResult('开始测试所有首页API接口...', 'info');
            await testCarouselAPI();
            await new Promise(resolve => setTimeout(resolve, 500));
            await testFeaturesAPI();
            await new Promise(resolve => setTimeout(resolve, 500));
            await testStatsAPI();
            showResult('所有API测试完成！', 'success');
        }

        function showCarouselData(records) {
            let html = '<h3>轮播图数据预览:</h3>';
            
            records.sort((a, b) => (a.sort_order || 0) - (b.sort_order || 0));
            
            records.forEach((item, index) => {
                html += `
                    <div class="carousel-item">
                        <div class="badge">${item.badge || '无标签'}</div>
                        <h3>${item.title || '无标题'}</h3>
                        <div class="description">${item.description || '无描述'}</div>
                        <div class="action">
                            <a href="${item.button_link || '#'}">${item.button_text || '了解更多'}</a>
                        </div>
                        <small style="color: #9ca3af;">
                            排序: ${item.sort_order || 0} | 状态: ${item.status || '未知'} | 图片: ${item.image_url || '无'}
                        </small>
                    </div>
                `;
            });
            
            showResult(html, 'info');
        }

        function showFeaturesData(records) {
            let html = '<h3>功能特性数据预览:</h3>';

            records.sort((a, b) => (a.sort_order || 0) - (b.sort_order || 0));

            records.forEach((item, index) => {
                html += `
                    <div class="carousel-item">
                        <h3><i class="icon-${item.icon || 'star'}"></i> ${item.title || '无标题'}</h3>
                        <div class="description">${item.description || '无描述'}</div>
                        <div class="action">
                            <a href="${item.link_url || '#'}">${item.link_url || '无链接'}</a>
                        </div>
                        <small style="color: #9ca3af;">
                            排序: ${item.sort_order || 0} | 状态: ${item.status || '未知'} | 图标: ${item.icon || '无'}
                        </small>
                    </div>
                `;
            });

            showResult(html, 'info');
        }

        function showStatsData(records) {
            let html = '<h3>统计数据预览:</h3>';

            records.sort((a, b) => (a.sort_order || 0) - (b.sort_order || 0));

            records.forEach((item, index) => {
                html += `
                    <div class="carousel-item">
                        <h3>${item.stat_value || '0'} - ${item.stat_label || '无标签'}</h3>
                        <div class="description">
                            <strong>统计键:</strong> ${item.stat_key || '无'}<br>
                            <strong>目标数值:</strong> ${item.target_number || 0}<br>
                            <strong>单位:</strong> ${item.unit || '无'}
                        </div>
                        <small style="color: #9ca3af;">
                            排序: ${item.sort_order || 0} | 状态: ${item.status || '未知'}
                        </small>
                    </div>
                `;
            });

            showResult(html, 'info');
        }

        function showResult(message, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `result ${type}`;
            resultDiv.innerHTML = message;
            resultsDiv.appendChild(resultDiv);
        }
        
        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }
        
        // 页面加载时显示说明
        window.addEventListener('load', function() {
            showResult(`
                <strong>首页API测试说明:</strong><br>
                • 轮播图接口: GET /aigc/aigcHomeCarousel/list<br>
                • 功能特性接口: GET /aigc/websiteFeatures/list<br>
                • 统计数据接口: GET /aigc/websiteStats/list<br>
                • 参数: status=1&pageNo=1&pageSize=10<br>
                • 预期返回: 启用状态的数据列表<br>
                • 请确保后端服务已启动在 http://localhost:8080/jeecg-boot
            `, 'info');
        });
    </script>
</body>
</html>
