package org.jeecg.modules.jianying.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.jianying.dto.GetImageAnimationsRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 剪映ID解析服务 - 统一获取动画和转场的真实ID
 * 
 * <AUTHOR>
 * @date 2025-07-07
 */
@Slf4j
@Service
public class JianyingIdResolverService {
    
    @Autowired
    private JianyingAssistantService jianyingAssistantService;
    
    @Autowired
    private CacheManager cacheManager;
    
    @Autowired
    private RestTemplate restTemplate;
    
    // 转场API配置
    private static final String TRANSITION_API_URL = "https://lv-api-sinfonlinec.ulikecam.com/artist/v1/effect/search";
    
    // 转场本地缓存（24小时过期）
    private final Map<String, TransitionCacheEntry> transitionCache = new ConcurrentHashMap<>();
    private static final long TRANSITION_CACHE_EXPIRE_TIME = 24 * 60 * 60 * 1000L;
    
    /**
     * 动画信息类
     */
    public static class AnimationInfo {
        private String resourceId;
        private String id;
        private String name;
        private String categoryId;
        private String type;
        
        public AnimationInfo(String resourceId, String id, String name, String categoryId, String type) {
            this.resourceId = resourceId;
            this.id = id;
            this.name = name;
            this.categoryId = categoryId;
            this.type = type;
        }
        
        // Getters
        public String getResourceId() { return resourceId; }
        public String getId() { return id; }
        public String getName() { return name; }
        public String getCategoryId() { return categoryId; }
        public String getType() { return type; }
    }
    
    /**
     * 转场信息类
     */
    public static class TransitionInfo {
        private String resourceId;
        private String effectId;
        private String name;
        private boolean isOverlap;
        
        public TransitionInfo(String resourceId, String effectId, String name, boolean isOverlap) {
            this.resourceId = resourceId;
            this.effectId = effectId;
            this.name = name;
            this.isOverlap = isOverlap;
        }
        
        // Getters
        public String getResourceId() { return resourceId; }
        public String getEffectId() { return effectId; }
        public String getName() { return name; }
        public boolean isOverlap() { return isOverlap; }
    }
    
    /**
     * 转场缓存条目
     */
    private static class TransitionCacheEntry {
        private final TransitionInfo transitionInfo;
        private final long timestamp;
        
        public TransitionCacheEntry(TransitionInfo transitionInfo) {
            this.transitionInfo = transitionInfo;
            this.timestamp = System.currentTimeMillis();
        }
        
        public boolean isExpired() {
            return System.currentTimeMillis() - timestamp > TRANSITION_CACHE_EXPIRE_TIME;
        }
        
        public TransitionInfo getTransitionInfo() {
            return transitionInfo;
        }
    }
    
    /**
     * 根据动画名称和类型获取动画信息（图片动画）
     *
     * @param animationName 动画名称（如"闪现"、"烟雾弹"）
     * @param animationType 动画类型（"in"、"out"、"group"）
     * @return 动画信息，如果未找到返回null
     */
    public AnimationInfo findAnimationByName(String animationName, String animationType) {
        return findAnimationByName(animationName, animationType, "image");
    }

    /**
     * 根据动画名称、类型和面板获取动画信息
     *
     * @param animationName 动画名称（如"闪现"、"烟雾弹"）
     * @param animationType 动画类型（图片："in"、"out"、"group"；文字："in"、"out"、"loop"）
     * @param panel 面板类型（"image"：图片动画，"text"：文字动画）
     * @return 动画信息，如果未找到返回null
     */
    public AnimationInfo findAnimationByName(String animationName, String animationType, String panel) {
        if (animationName == null || animationName.trim().isEmpty()) {
            log.warn("动画名称为空，无法查找");
            return null;
        }

        String cleanName = animationName.trim();
        log.info("查找动画: name={}, type={}, panel={}", cleanName, animationType, panel);

        // 1. 先从Redis缓存查找对应类型
        AnimationInfo animation = findInSpecificCache(cleanName, animationType, panel);
        if (animation != null) {
            log.info("从缓存找到动画: {}", cleanName);
            return animation;
        }

        // 2. 如果没找到，触发对应类型的API调用更新缓存
        log.info("缓存中未找到动画，触发API更新缓存: type={}, panel={}", animationType, panel);
        refreshSpecificAnimationCache(animationType, panel);

        // 3. 再次查找
        animation = findInSpecificCache(cleanName, animationType, panel);
        if (animation != null) {
            log.info("API更新后找到动画: {}", cleanName);
            return animation;
        }

        // 4. 如果还是没找到，可能是类型判断错误，加载所有类型
        log.warn("指定类型未找到动画，尝试加载所有类型: {}", cleanName);
        refreshAllAnimationCache(panel);

        // 5. 最后一次查找（遍历所有类型）
        animation = findInAllCache(cleanName, panel);
        if (animation != null) {
            log.info("全量更新后找到动画: {}", cleanName);
            return animation;
        }

        log.error("未找到动画: {}", cleanName);
        return null;
    }
    
    /**
     * 根据转场名称获取转场信息
     * 
     * @param transitionName 转场名称（如"相片切换"）
     * @return 转场信息，如果未找到返回null
     */
    public TransitionInfo findTransitionByName(String transitionName) {
        if (transitionName == null || transitionName.trim().isEmpty()) {
            log.warn("转场名称为空，无法查找");
            return null;
        }
        
        String cleanName = transitionName.trim();
        log.info("查找转场: name={}", cleanName);
        
        // 1. 先检查本地缓存
        TransitionCacheEntry cacheEntry = transitionCache.get(cleanName);
        if (cacheEntry != null && !cacheEntry.isExpired()) {
            log.info("从缓存找到转场: {}", cleanName);
            return cacheEntry.getTransitionInfo();
        } else if (cacheEntry != null && cacheEntry.isExpired()) {
            // 缓存过期，移除
            transitionCache.remove(cleanName);
            log.info("转场缓存已过期，移除: {}", cleanName);
        }
        
        // 2. 调用转场API
        try {
            TransitionInfo transitionInfo = callTransitionAPI(cleanName);
            if (transitionInfo != null) {
                // 缓存结果
                transitionCache.put(cleanName, new TransitionCacheEntry(transitionInfo));
                log.info("转场API调用成功: {} -> resource_id: {}, effect_id: {}",
                        cleanName, transitionInfo.getResourceId(), transitionInfo.getEffectId());
                return transitionInfo;
            } else {
                log.warn("转场API未找到: {}", cleanName);
                return null;
            }
        } catch (Exception e) {
            log.error("转场API调用失败: {}", cleanName, e);
            return null;
        }
    }
    
    /**
     * 从指定类型的Redis缓存中查找动画（图片动画）
     */
    private AnimationInfo findInSpecificCache(String animationName, String animationType) {
        return findInSpecificCache(animationName, animationType, "image");
    }

    /**
     * 从指定类型的Redis缓存中查找动画（支持图片和文字动画）
     */
    private AnimationInfo findInSpecificCache(String animationName, String animationType, String panel) {
        String[] modes = {"0", "1", "2"}; // 全部、VIP、免费

        for (String mode : modes) {
            String cacheKey;
            if ("text".equals(panel)) {
                // 文字动画使用text_前缀
                cacheKey = "text_" + animationType + ":" + mode;
            } else {
                // 图片动画使用原始格式
                cacheKey = animationType + ":" + mode;
            }

            AnimationInfo animation = findInCacheKey(animationName, cacheKey);
            if (animation != null) {
                return animation;
            }
        }
        return null;
    }
    
    /**
     * 从所有类型的Redis缓存中查找动画（图片动画）
     */
    private AnimationInfo findInAllCache(String animationName) {
        return findInAllCache(animationName, "image");
    }

    /**
     * 从所有类型的Redis缓存中查找动画（支持图片和文字动画）
     */
    private AnimationInfo findInAllCache(String animationName, String panel) {
        String[] types;
        String[] modes = {"0", "1", "2"};

        if ("text".equals(panel)) {
            // 文字动画类型
            types = new String[]{"in", "out", "loop"};
        } else {
            // 图片动画类型
            types = new String[]{"in", "out", "group"};
        }

        for (String type : types) {
            for (String mode : modes) {
                String cacheKey;
                if ("text".equals(panel)) {
                    // 文字动画使用text_前缀
                    cacheKey = "text_" + type + ":" + mode;
                } else {
                    // 图片动画使用原始格式
                    cacheKey = type + ":" + mode;
                }

                AnimationInfo animation = findInCacheKey(animationName, cacheKey);
                if (animation != null) {
                    return animation;
                }
            }
        }
        return null;
    }
    
    /**
     * 从指定缓存key中查找动画
     */
    private AnimationInfo findInCacheKey(String animationName, String cacheKey) {
        try {
            // 使用CacheManager获取缓存，确保序列化一致性
            Cache cache = cacheManager.getCache("jianying:animations");
            if (cache == null) {
                log.warn("缓存不存在: jianying:animations");
                return null;
            }

            Cache.ValueWrapper valueWrapper = cache.get(cacheKey);
            if (valueWrapper != null) {
                Object cacheValue = valueWrapper.get();
                if (cacheValue != null) {
                    JSONObject data;
                    if (cacheValue instanceof JSONObject) {
                        data = (JSONObject) cacheValue;
                    } else if (cacheValue instanceof String) {
                        data = JSONObject.parseObject((String) cacheValue);
                    } else {
                        log.warn("缓存数据格式不支持: cacheKey={}, type={}", cacheKey, cacheValue.getClass().getName());
                        return null;
                    }

                    JSONArray effects = data.getJSONArray("effects");
                    if (effects != null) {
                        for (int i = 0; i < effects.size(); i++) {
                            JSONObject effect = effects.getJSONObject(i);
                            String name = effect.getString("name");

                            if (animationName.equals(name)) {
                                return new AnimationInfo(
                                    effect.getString("resource_id"),
                                    effect.getString("id"),
                                    name,
                                    effect.getString("category_id"),
                                    effect.getString("type")
                                );
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            // 检查是否是序列化问题
            if (e.getMessage() != null && (e.getMessage().contains("Unexpected character") ||
                e.getMessage().contains("SerializationException") ||
                e.getCause() instanceof org.springframework.data.redis.serializer.SerializationException)) {
                log.warn("检测到缓存序列化格式不兼容，自动清空缓存: cacheKey={}", cacheKey);
                clearAllAnimationCache();
                return null; // 返回null，让上层重新获取
            }

            log.error("从缓存查找动画失败，缓存访问问题: cacheKey={}, animationName={}", cacheKey, animationName, e);
            // 不返回null，直接抛出异常停止处理
            throw new RuntimeException("缓存访问失败，请检查缓存配置: " + e.getMessage(), e);
        }
        return null;
    }
    
    /**
     * 刷新指定类型的动画缓存（图片动画）
     */
    private void refreshSpecificAnimationCache(String animationType) {
        refreshSpecificAnimationCache(animationType, "image");
    }

    /**
     * 刷新指定类型的动画缓存（支持图片和文字动画）
     */
    private void refreshSpecificAnimationCache(String animationType, String panel) {
        try {
            // 先清空对应类型的缓存
            clearSpecificAnimationCache(animationType, panel);

            if ("text".equals(panel)) {
                // 文字动画
                org.jeecg.modules.jianying.dto.GetTextAnimationsRequest request =
                    new org.jeecg.modules.jianying.dto.GetTextAnimationsRequest();
                request.setZjType(animationType);
                request.setZjMode(0); // 获取全部
                jianyingAssistantService.getTextAnimations(request);
                log.info("刷新文字动画缓存成功: type={}", animationType);
            } else {
                // 图片动画
                GetImageAnimationsRequest request = new GetImageAnimationsRequest();
                request.setZjType(animationType);
                request.setZjMode(0); // 获取全部
                jianyingAssistantService.getImageAnimations(request);
                log.info("刷新图片动画缓存成功: type={}", animationType);
            }
        } catch (Exception e) {
            log.error("刷新动画缓存失败: type={}, panel={}", animationType, panel, e);
        }
    }
    
    /**
     * 刷新所有类型的动画缓存（图片动画）
     */
    private void refreshAllAnimationCache() {
        refreshAllAnimationCache("image");
    }

    /**
     * 刷新所有类型的动画缓存（支持图片和文字动画）
     */
    private void refreshAllAnimationCache(String panel) {
        // 先清空所有缓存
        clearAllAnimationCache();

        String[] types;
        if ("text".equals(panel)) {
            // 文字动画类型
            types = new String[]{"in", "out", "loop"};
        } else {
            // 图片动画类型
            types = new String[]{"in", "out", "group"};
        }

        for (String type : types) {
            refreshSpecificAnimationCache(type, panel);
        }
    }

    /**
     * 清空所有动画缓存（解决序列化格式不兼容问题）
     */
    public void clearAllAnimationCache() {
        try {
            Cache cache = cacheManager.getCache("jianying:animations");
            if (cache != null) {
                cache.clear();
                log.info("清空动画缓存成功");
            }
        } catch (Exception e) {
            log.error("清空动画缓存失败", e);
        }
    }

    /**
     * 清空指定类型的动画缓存（图片动画）
     */
    private void clearSpecificAnimationCache(String animationType) {
        clearSpecificAnimationCache(animationType, "image");
    }

    /**
     * 清空指定类型的动画缓存（支持图片和文字动画）
     */
    private void clearSpecificAnimationCache(String animationType, String panel) {
        try {
            Cache cache = cacheManager.getCache("jianying:animations");
            if (cache != null) {
                String[] modes = {"0", "1", "2"};
                for (String mode : modes) {
                    String cacheKey;
                    if ("text".equals(panel)) {
                        // 文字动画使用text_前缀
                        cacheKey = "text_" + animationType + ":" + mode;
                    } else {
                        // 图片动画使用原始格式
                        cacheKey = animationType + ":" + mode;
                    }
                    cache.evict(cacheKey);
                    log.debug("清空缓存key: {}", cacheKey);
                }
                log.info("清空指定类型动画缓存成功: type={}, panel={}", animationType, panel);
            }
        } catch (Exception e) {
            log.error("清空指定类型动画缓存失败: type={}, panel={}", animationType, panel, e);
        }
    }
    
    /**
     * 调用转场API获取转场信息
     */
    private TransitionInfo callTransitionAPI(String transitionName) {
        try {
            // 构建请求URL
            String url = TRANSITION_API_URL + "?aid=3704&version_name=5.9.0&device_platform=windows";
            
            // 构建请求体
            JSONObject requestBody = new JSONObject();
            requestBody.put("count", 1);
            requestBody.put("effect_type", 19);
            requestBody.put("query", transitionName);
            
            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            
            HttpEntity<String> entity = new HttpEntity<>(requestBody.toJSONString(), headers);
            
            // 发送请求
            ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.POST, entity, String.class);
            
            if (response.getStatusCode() == HttpStatus.OK) {
                JSONObject responseData = JSONObject.parseObject(response.getBody());
                
                // 检查返回状态
                String ret = responseData.getString("ret");
                if (!"0".equals(ret)) {
                    log.error("转场API返回错误: ret={}, errmsg={}", ret, responseData.getString("errmsg"));
                    return null;
                }
                
                // 解析数据
                JSONObject data = responseData.getJSONObject("data");
                JSONArray effectItemList = data.getJSONArray("effect_item_list");
                
                if (effectItemList != null && effectItemList.size() > 0) {
                    JSONObject firstEffect = effectItemList.getJSONObject(0);
                    JSONObject commonAttr = firstEffect.getJSONObject("common_attr");
                    
                    if (commonAttr != null) {
                        String resourceId = commonAttr.getString("id");
                        String effectId = commonAttr.getString("effect_id");
                        String title = commonAttr.getString("title");
                        
                        // 解析is_overlap
                        boolean isOverlap = true; // 默认值
                        String sdkExtra = commonAttr.getString("sdk_extra");
                        if (sdkExtra != null && !sdkExtra.isEmpty()) {
                            try {
                                JSONObject sdkExtraObj = JSONObject.parseObject(sdkExtra);
                                JSONObject transition = sdkExtraObj.getJSONObject("transition");
                                if (transition != null && transition.containsKey("isOverlap")) {
                                    isOverlap = transition.getBoolean("isOverlap");
                                }
                            } catch (Exception e) {
                                log.warn("解析sdk_extra失败，使用默认is_overlap=true: {}", sdkExtra, e);
                            }
                        }
                        
                        return new TransitionInfo(resourceId, effectId, title, isOverlap);
                    }
                }
                
                log.warn("转场API未返回有效数据: {}", transitionName);
                return null;
                
            } else {
                log.error("转场API请求失败，状态码: {}", response.getStatusCode());
                return null;
            }
            
        } catch (Exception e) {
            log.error("调用转场API异常: {}", transitionName, e);
            return null;
        }
    }
}
