{"openapi": "3.0.0", "info": {"title": "剪映小助手_智界数据箱 - 字符串列表转对象", "description": "字符串列表转化成对象列表", "version": "1.0.0"}, "servers": [{"url": "https://www.aigcview.com"}], "paths": {"/jeecg-boot/api/jianying/str_list_to_objs": {"post": {"summary": "字符串列表转对象", "description": "字符串列表转化成对象列表", "operationId": "strListToObjs_zj", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"access_key": {"type": "string", "description": "访问密钥（必填）", "example": "JianyingAPI_2025_SecureAccess_AigcView", "default": "JianyingAPI_2025_SecureAccess_AigcView"}, "zj_infos": {"type": "array", "description": "列表内容（必填）", "items": {"type": "string"}, "example": ["item1", "item2", "item3"]}}, "required": ["access_key", "zj_infos"], "additionalProperties": false}}}}, "responses": {"200": {"description": "成功转换为对象列表", "content": {"application/json": {"schema": {"type": "array", "description": "转换后的对象列表", "items": {"type": "object", "properties": {"output": {"type": "string", "description": "输出内容"}}}}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "description": "操作是否成功", "example": false}, "error": {"type": "string", "description": "错误信息"}, "code": {"type": "string", "description": "错误代码"}}, "required": ["success", "error"]}}}}, "500": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "description": "操作是否成功", "example": false}, "error": {"type": "string", "description": "错误信息"}, "code": {"type": "string", "description": "错误代码"}}, "required": ["success", "error"]}}}}}}}}}