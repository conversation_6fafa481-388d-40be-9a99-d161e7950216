package org.jeecg.modules.api.exception;

/**
 * 视频生成异常类
 * 
 * <AUTHOR>
 * @since 2025-01-16
 */
public class VideoGenerationException extends RuntimeException {

    private String errorCode;
    private String errorMessage;

    public VideoGenerationException(String message) {
        super(message);
        this.errorMessage = message;
    }

    public VideoGenerationException(String errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
        this.errorMessage = message;
    }

    public VideoGenerationException(String message, Throwable cause) {
        super(message, cause);
        this.errorMessage = message;
    }

    public VideoGenerationException(String errorCode, String message, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
        this.errorMessage = message;
    }

    public String getErrorCode() {
        return errorCode;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    // 常见错误代码定义
    public static class ErrorCodes {
        public static final String INVALID_PARAMETER = "INVALID_PARAMETER";
        public static final String INSUFFICIENT_BALANCE = "INSUFFICIENT_BALANCE";
        public static final String API_KEY_INVALID = "API_KEY_INVALID";
        public static final String MODEL_NOT_SUPPORTED = "MODEL_NOT_SUPPORTED";
        public static final String RESOLUTION_NOT_SUPPORTED = "RESOLUTION_NOT_SUPPORTED";
        public static final String DURATION_NOT_SUPPORTED = "DURATION_NOT_SUPPORTED";
        public static final String DOUBAO_API_ERROR = "DOUBAO_API_ERROR";
        public static final String DEDUCT_BALANCE_FAILED = "DEDUCT_BALANCE_FAILED";
        public static final String TASK_NOT_FOUND = "TASK_NOT_FOUND";
        public static final String NETWORK_ERROR = "NETWORK_ERROR";
        public static final String SYSTEM_ERROR = "SYSTEM_ERROR";
    }

    // 便捷的静态方法
    public static VideoGenerationException invalidParameter(String message) {
        return new VideoGenerationException(ErrorCodes.INVALID_PARAMETER, message);
    }

    public static VideoGenerationException insufficientBalance(String message) {
        return new VideoGenerationException(ErrorCodes.INSUFFICIENT_BALANCE, message);
    }

    public static VideoGenerationException apiKeyInvalid(String message) {
        return new VideoGenerationException(ErrorCodes.API_KEY_INVALID, message);
    }

    public static VideoGenerationException modelNotSupported(String message) {
        return new VideoGenerationException(ErrorCodes.MODEL_NOT_SUPPORTED, message);
    }

    public static VideoGenerationException resolutionNotSupported(String message) {
        return new VideoGenerationException(ErrorCodes.RESOLUTION_NOT_SUPPORTED, message);
    }

    public static VideoGenerationException durationNotSupported(String message) {
        return new VideoGenerationException(ErrorCodes.DURATION_NOT_SUPPORTED, message);
    }

    public static VideoGenerationException douBaoApiError(String message) {
        return new VideoGenerationException(ErrorCodes.DOUBAO_API_ERROR, message);
    }

    public static VideoGenerationException douBaoApiError(String message, Throwable cause) {
        return new VideoGenerationException(ErrorCodes.DOUBAO_API_ERROR, message, cause);
    }

    public static VideoGenerationException deductBalanceFailed(String message) {
        return new VideoGenerationException(ErrorCodes.DEDUCT_BALANCE_FAILED, message);
    }

    public static VideoGenerationException taskNotFound(String message) {
        return new VideoGenerationException(ErrorCodes.TASK_NOT_FOUND, message);
    }

    public static VideoGenerationException networkError(String message, Throwable cause) {
        return new VideoGenerationException(ErrorCodes.NETWORK_ERROR, message, cause);
    }

    public static VideoGenerationException systemError(String message, Throwable cause) {
        return new VideoGenerationException(ErrorCodes.SYSTEM_ERROR, message, cause);
    }
}
