-- =============================================
-- 智界Aigc 程序版本控制功能 数据库脚本
-- 创建时间：2025-01-09
-- 描述：新增程序版本控制表和相关权限配置
-- =============================================

-- 1. 创建程序版本控制表
DROP TABLE IF EXISTS `aigc_version_control`;
CREATE TABLE `aigc_version_control` (
  `id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键ID',
  `program_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '程序类型：frontend-前端,backend-后端,desktop-桌面应用,plugin-扣子插件',
  `version_number` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '版本号，如1.0.0、2.1.3等',
  `update_content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '更新内容详细说明',
  `release_date` datetime NOT NULL COMMENT '发布日期',
  `is_latest` tinyint(1) DEFAULT 0 COMMENT '是否最新版本：1-是，0-否',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
  `create_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `sys_org_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '所属部门',
  PRIMARY KEY (`id`),
  KEY `idx_program_type` (`program_type`),
  KEY `idx_version_number` (`version_number`),
  KEY `idx_is_latest` (`is_latest`),
  KEY `idx_status` (`status`),
  KEY `idx_release_date` (`release_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='程序版本控制表';

-- 2. 插入初始数据
INSERT INTO `aigc_version_control` (`id`, `program_type`, `version_number`, `update_content`, `release_date`, `is_latest`, `status`, `create_by`, `create_time`) VALUES
('1', 'frontend', '2.4.6', '基于JeecgBoot框架的前端管理系统，包含官网和后台管理双模式', '2025-01-01 00:00:00', 1, 1, 'admin', NOW()),
('2', 'backend', '2.3.5', 'Spring Boot后端服务，包含用户管理、插件生态、教育内容等核心模块', '2025-01-01 00:00:00', 1, 1, 'admin', NOW()),
('3', 'desktop', '1.0.0', 'Electron桌面应用，支持剪映草稿导入功能', '2025-01-01 00:00:00', 1, 1, 'admin', NOW()),
('4', 'plugin', '1.0.0', '扣子平台插件，包含智界工具箱17个工具和智界数据箱14个工具', '2025-01-01 00:00:00', 1, 1, 'admin', NOW());

-- 3. 新增菜单权限记录
-- 3.1 查询系统管理菜单的ID（假设为系统管理的父级菜单）
SET @parent_id = (SELECT id FROM sys_permission WHERE menu_name = '系统管理' AND menu_type = 0 LIMIT 1);

-- 3.2 插入程序版本控制菜单
INSERT INTO `sys_permission` (
  `id`, 
  `parent_id`, 
  `name`, 
  `url`, 
  `component`, 
  `component_name`, 
  `redirect`, 
  `menu_type`, 
  `perms`, 
  `perms_type`, 
  `sort_no`, 
  `always_show`, 
  `icon`, 
  `is_route`, 
  `is_leaf`, 
  `keep_alive`, 
  `hidden`, 
  `hide_tab`, 
  `description`, 
  `status`, 
  `del_flag`, 
  `rule_flag`, 
  `create_by`, 
  `create_time`, 
  `update_by`, 
  `update_time`
) VALUES (
  REPLACE(UUID(), '-', ''),  -- 生成32位UUID作为主键
  @parent_id,                -- 父级菜单ID（系统管理）
  'aigc-version-control',    -- 权限标识
  '/aigcview/versioncontrol', -- 菜单路径
  'aigcview/versioncontrol/AigcVersionControlList', -- 组件路径
  'AigcVersionControlList',  -- 组件名称
  NULL,                      -- 重定向
  1,                         -- 菜单类型：1-菜单
  NULL,                      -- 权限字符串
  '1',                       -- 权限策略：1-显示
  5.0,                       -- 排序号
  0,                         -- 是否总是显示
  'setting',                 -- 菜单图标
  1,                         -- 是否路由菜单
  1,                         -- 是否叶子节点
  0,                         -- 是否缓存
  0,                         -- 是否隐藏
  0,                         -- 是否隐藏Tab
  '程序版本控制管理',         -- 描述
  '1',                       -- 状态：1-正常
  0,                         -- 删除标志：0-正常
  0,                         -- 规则标志
  'admin',                   -- 创建人
  NOW(),                     -- 创建时间
  'admin',                   -- 更新人
  NOW()                      -- 更新时间
);

-- 3.3 获取刚插入的菜单ID
SET @menu_id = (SELECT id FROM sys_permission WHERE name = 'aigc-version-control' LIMIT 1);

-- 3.4 插入菜单的按钮权限
INSERT INTO `sys_permission` (
  `id`, 
  `parent_id`, 
  `name`, 
  `url`, 
  `component`, 
  `component_name`, 
  `redirect`, 
  `menu_type`, 
  `perms`, 
  `perms_type`, 
  `sort_no`, 
  `always_show`, 
  `icon`, 
  `is_route`, 
  `is_leaf`, 
  `keep_alive`, 
  `hidden`, 
  `hide_tab`, 
  `description`, 
  `status`, 
  `del_flag`, 
  `rule_flag`, 
  `create_by`, 
  `create_time`
) VALUES 
-- 查询权限
(REPLACE(UUID(), '-', ''), @menu_id, 'aigc-version-control:list', NULL, NULL, NULL, NULL, 2, 'aigcview:versioncontrol:list', '1', 1.0, 0, NULL, 0, 1, 0, 0, 0, '查询', '1', 0, 0, 'admin', NOW()),
-- 新增权限
(REPLACE(UUID(), '-', ''), @menu_id, 'aigc-version-control:add', NULL, NULL, NULL, NULL, 2, 'aigcview:versioncontrol:add', '1', 2.0, 0, NULL, 0, 1, 0, 0, 0, '新增', '1', 0, 0, 'admin', NOW()),
-- 编辑权限
(REPLACE(UUID(), '-', ''), @menu_id, 'aigc-version-control:edit', NULL, NULL, NULL, NULL, 2, 'aigcview:versioncontrol:edit', '1', 3.0, 0, NULL, 0, 1, 0, 0, 0, '编辑', '1', 0, 0, 'admin', NOW()),
-- 删除权限
(REPLACE(UUID(), '-', ''), @menu_id, 'aigc-version-control:delete', NULL, NULL, NULL, NULL, 2, 'aigcview:versioncontrol:delete', '1', 4.0, 0, NULL, 0, 1, 0, 0, 0, '删除', '1', 0, 0, 'admin', NOW()),
-- 批量删除权限
(REPLACE(UUID(), '-', ''), @menu_id, 'aigc-version-control:deleteBatch', NULL, NULL, NULL, NULL, 2, 'aigcview:versioncontrol:deleteBatch', '1', 5.0, 0, NULL, 0, 1, 0, 0, 0, '批量删除', '1', 0, 0, 'admin', NOW()),
-- 导出权限
(REPLACE(UUID(), '-', ''), @menu_id, 'aigc-version-control:exportXls', NULL, NULL, NULL, NULL, 2, 'aigcview:versioncontrol:exportXls', '1', 6.0, 0, NULL, 0, 1, 0, 0, 0, '导出', '1', 0, 0, 'admin', NOW()),
-- 导入权限
(REPLACE(UUID(), '-', ''), @menu_id, 'aigc-version-control:importExcel', NULL, NULL, NULL, NULL, 2, 'aigcview:versioncontrol:importExcel', '1', 7.0, 0, NULL, 0, 1, 0, 0, 0, '导入', '1', 0, 0, 'admin', NOW());

-- 4. 为管理员角色分配权限
-- 4.1 获取管理员角色ID
SET @admin_role_id = (SELECT id FROM sys_role WHERE role_code = 'admin' LIMIT 1);

-- 4.2 为管理员角色分配菜单权限
INSERT INTO `sys_role_permission` (`role_id`, `permission_id`) 
SELECT @admin_role_id, id FROM sys_permission 
WHERE name LIKE 'aigc-version-control%' AND del_flag = 0;

-- 5. 创建字典配置（程序类型）
INSERT INTO `sys_dict` (`id`, `dict_name`, `dict_code`, `description`, `del_flag`, `create_by`, `create_time`, `type`) VALUES 
(REPLACE(UUID(), '-', ''), '程序类型', 'program_type', '程序版本控制中的程序类型选项', 0, 'admin', NOW(), 0);

-- 获取字典ID
SET @dict_id = (SELECT id FROM sys_dict WHERE dict_code = 'program_type' LIMIT 1);

-- 插入字典项
INSERT INTO `sys_dict_item` (`id`, `dict_id`, `item_text`, `item_value`, `description`, `sort_order`, `status`, `create_by`, `create_time`) VALUES 
(REPLACE(UUID(), '-', ''), @dict_id, '前端', 'frontend', '网页前端系统', 1, 1, 'admin', NOW()),
(REPLACE(UUID(), '-', ''), @dict_id, '后端', 'backend', '后端服务系统', 2, 1, 'admin', NOW()),
(REPLACE(UUID(), '-', ''), @dict_id, '桌面应用', 'desktop', 'Electron桌面应用', 3, 1, 'admin', NOW()),
(REPLACE(UUID(), '-', ''), @dict_id, '扣子插件', 'plugin', '扣子平台插件', 4, 1, 'admin', NOW());

-- 执行完成提示
SELECT '程序版本控制功能数据库脚本执行完成！' AS message;
