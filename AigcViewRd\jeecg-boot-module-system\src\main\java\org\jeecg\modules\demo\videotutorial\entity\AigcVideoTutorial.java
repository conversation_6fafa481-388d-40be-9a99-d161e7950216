package org.jeecg.modules.demo.videotutorial.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 视频教程
 * @Author: jeecg-boot
 * @Date:   2025-06-14
 * @Version: V1.0
 */
@Data
@TableName("aigc_video_tutorial")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="aigc_video_tutorial对象", description="视频教程")
public class AigcVideoTutorial implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private java.lang.String id;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private java.util.Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private java.util.Date updateTime;
	/**所属部门*/
    @ApiModelProperty(value = "所属部门")
    private java.lang.String sysOrgCode;
	/**是否系列视频*/
	@Excel(name = "是否系列视频", width = 15, dicCode = "isTrue")
	@Dict(dicCode = "isTrue")
    @ApiModelProperty(value = "是否系列视频")
    private java.lang.String isseries;
	/**系列名称*/
	@Excel(name = "系列名称", width = 15)
    @ApiModelProperty(value = "系列名称")
    private java.lang.String seriesname;
	/**视频文件*/
	@Excel(name = "视频文件", width = 15)
    @ApiModelProperty(value = "视频文件")
    private java.lang.String videofile;
	/**视频链接*/
	@Excel(name = "视频链接", width = 20)
    @ApiModelProperty(value = "视频链接")
    private java.lang.String videoUrl;
	/**讲师*/
	@Excel(name = "讲师", width = 15, dictTable = "aigc_video_teacher", dicText = "teachername", dicCode = "id")
	@Dict(dictTable = "aigc_video_teacher", dicText = "teachername", dicCode = "id")
    @ApiModelProperty(value = "讲师")
    private java.lang.String teacher;
	/**点击量*/
	@Excel(name = "点击量", width = 15)
    @ApiModelProperty(value = "点击量")
    private java.lang.Integer clicknum;
	/**标题*/
	@Excel(name = "标题", width = 15)
    @ApiModelProperty(value = "标题")
    private java.lang.String titile;
	/**设置等级*/
	@Excel(name = "设置等级", width = 15, dicCode = "setLevel")
	@Dict(dicCode = "setLevel")
    @ApiModelProperty(value = "设置等级")
    private java.lang.String setlevel;
	/**课程介绍*/
	@Excel(name = "课程介绍", width = 15)
    @ApiModelProperty(value = "课程介绍")
    private java.lang.String intro;
	/**课程标签*/
	@Excel(name = "课程标签", width = 15, dicCode = "tag")
	@Dict(dicCode = "tag")
    @ApiModelProperty(value = "课程标签")
    private java.lang.String tag;
	/**上传日期*/
	@Excel(name = "上传日期", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "上传日期")
    private java.util.Date uptime;
}
