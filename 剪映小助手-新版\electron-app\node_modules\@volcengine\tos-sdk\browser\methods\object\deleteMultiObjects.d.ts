import TOSBase from '../base';
export interface DeleteMultiObjectsInput {
    bucket?: string;
    /**
     * default: false
     */
    quiet?: boolean;
    objects: {
        key: string;
        versionId?: string;
    }[];
    /**@private unstable */
    skipTrash?: string;
    /**@private unstable */
    recursive?: string;
}
export interface DeleteMultiObjectsOutput {
    Deleted: {
        Key: string;
        VersionId: string;
        DeleteMarker?: boolean;
        DeleteMarkerVersionId?: string;
    }[];
    Error: {
        Code: string;
        Message: string;
        Key: string;
        VersionId: string;
    }[];
}
export declare function deleteMultiObjects(this: TOSBase, input: DeleteMultiObjectsInput): Promise<import("../base").TosResponse<DeleteMultiObjectsOutput>>;
export default deleteMultiObjects;
