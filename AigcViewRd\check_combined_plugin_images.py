#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔍 检查组合插件图片数据脚本
用于诊断组合插件图片显示问题
"""

import mysql.connector
from mysql.connector import Error
import json
from datetime import datetime

# 数据库配置
DB_CONFIG = {
    'host': 'localhost',
    'database': 'aigcview',
    'user': 'root',
    'password': 'root',
    'charset': 'utf8mb4'
}

def get_db_connection():
    """获取数据库连接"""
    try:
        connection = mysql.connector.connect(**DB_CONFIG)
        if connection.is_connected():
            return connection
    except Error as e:
        print(f"❌ 数据库连接失败: {e}")
        return None

def check_combined_plugins():
    """检查组合插件数据"""
    conn = get_db_connection()
    if not conn:
        return
    
    try:
        cursor = conn.cursor(dictionary=True)
        
        print("🔍 正在检查组合插件图片数据...")
        print("=" * 80)
        
        # 1. 查询所有组合插件
        query = """
        SELECT 
            id, plubname, plubimg, isCombined, combinedName, 
            combinedImage, combinedDescription, sortOrder,
            create_time, update_time
        FROM aigc_plub_shop 
        WHERE isCombined = 1
        ORDER BY combinedName, plubname
        """
        
        cursor.execute(query)
        combined_plugins = cursor.fetchall()
        
        if not combined_plugins:
            print("❌ 没有找到组合插件数据")
            return
        
        print(f"📊 找到 {len(combined_plugins)} 个组合插件")
        print()
        
        # 2. 统计分析
        stats = {
            'total': len(combined_plugins),
            'with_own_image': 0,
            'with_combined_image': 0,
            'without_any_image': 0,
            'combined_names': set()
        }
        
        # 3. 详细分析每个插件
        print("📋 组合插件详细信息:")
        print("-" * 80)
        
        for plugin in combined_plugins:
            stats['combined_names'].add(plugin['combinedName'] or '未命名')
            
            has_own_image = plugin['plubimg'] and plugin['plubimg'].strip()
            has_combined_image = plugin['combinedImage'] and plugin['combinedImage'].strip()
            
            if has_own_image:
                stats['with_own_image'] += 1
            if has_combined_image:
                stats['with_combined_image'] += 1
            if not has_own_image and not has_combined_image:
                stats['without_any_image'] += 1
            
            # 确定图片来源
            if has_own_image:
                image_source = "✅ 自有图片"
                final_image = plugin['plubimg']
            elif has_combined_image:
                image_source = "🔗 组合图片"
                final_image = plugin['combinedImage']
            else:
                image_source = "❌ 默认图片"
                final_image = "defaults/plugin-default.jpg"
            
            print(f"插件名称: {plugin['plubname']}")
            print(f"组合名称: {plugin['combinedName'] or '未设置'}")
            print(f"自有图片: {plugin['plubimg'] or '(空)'}")
            print(f"组合图片: {plugin['combinedImage'] or '(空)'}")
            print(f"图片来源: {image_source}")
            print(f"最终图片: {final_image}")
            print(f"创建时间: {plugin['create_time']}")
            print(f"更新时间: {plugin['update_time']}")
            print("-" * 40)
        
        # 4. 显示统计结果
        print("\n📈 统计结果:")
        print("=" * 50)
        print(f"总组合插件数: {stats['total']}")
        print(f"有自有图片的: {stats['with_own_image']}")
        print(f"有组合图片的: {stats['with_combined_image']}")
        print(f"无任何图片的: {stats['without_any_image']}")
        print(f"组合插件名数: {len(stats['combined_names'])}")
        print()
        
        # 5. 组合插件名列表
        print("🔗 组合插件名列表:")
        for name in sorted(stats['combined_names']):
            print(f"  - {name}")
        print()
        
        # 6. 问题诊断
        print("🔧 问题诊断:")
        print("=" * 50)
        
        if stats['with_combined_image'] == 0:
            print("❌ 问题确认：所有组合插件的 combinedImage 字段都是空的！")
            print()
            print("💡 解决方案：")
            print("1. 在后台管理系统中编辑组合插件，上传组合插件图片")
            print("2. 或者直接在数据库中更新 combinedImage 字段")
            print()
            print("🛠️ 数据库更新示例：")
            print("UPDATE aigc_plub_shop SET combinedImage = 'combined/default-combined.jpg' WHERE isCombined = 1 AND combinedName = '某个组合名';")
        else:
            print(f"✅ 发现 {stats['with_combined_image']} 个插件有组合图片")
            print("如果前端仍显示默认图片，请检查：")
            print("1. 图片文件是否存在于TOS存储中")
            print("2. 前端图片获取逻辑是否正确")
            print("3. 浏览器缓存是否需要清理")
        
        # 7. 生成修复SQL（如果需要）
        if stats['with_combined_image'] == 0:
            print("\n🔧 自动生成修复SQL:")
            print("=" * 50)
            
            # 按组合名分组
            combined_groups = {}
            for plugin in combined_plugins:
                name = plugin['combinedName'] or '未命名'
                if name not in combined_groups:
                    combined_groups[name] = []
                combined_groups[name].append(plugin)
            
            for combined_name, plugins in combined_groups.items():
                if combined_name != '未命名':
                    # 生成一个默认的组合插件图片路径
                    safe_name = combined_name.replace(' ', '-').replace('/', '-')
                    default_image = f"combined/{safe_name}.jpg"
                    
                    print(f"-- 更新组合插件: {combined_name}")
                    print(f"UPDATE aigc_plub_shop SET combinedImage = '{default_image}' WHERE combinedName = '{combined_name}' AND isCombined = 1;")
                    print()
        
        cursor.close()
        conn.close()
        
        print("🎉 检查完成！")
        
    except Error as e:
        print(f"❌ 查询异常: {e}")
        if conn:
            conn.close()

def update_combined_images():
    """更新组合插件图片（示例）"""
    conn = get_db_connection()
    if not conn:
        return
    
    try:
        cursor = conn.cursor()
        
        # 示例：为所有组合插件设置默认图片
        default_updates = [
            ("智界工具箱", "combined/zj-toolbox.jpg"),
            ("智界数据箱", "combined/zj-databox.jpg"),
            ("AI视频生成套装", "combined/ai-video-suite.jpg"),
            ("智能创作助手", "combined/ai-creator.jpg")
        ]
        
        print("🔧 正在更新组合插件图片...")
        
        for combined_name, image_path in default_updates:
            update_sql = """
            UPDATE aigc_plub_shop 
            SET combinedImage = %s, update_time = NOW() 
            WHERE combinedName = %s AND isCombined = 1
            """
            
            cursor.execute(update_sql, (image_path, combined_name))
            affected_rows = cursor.rowcount
            
            if affected_rows > 0:
                print(f"✅ 更新了 {affected_rows} 个 '{combined_name}' 插件的图片")
            else:
                print(f"⚠️ 没有找到名为 '{combined_name}' 的组合插件")
        
        conn.commit()
        cursor.close()
        conn.close()
        
        print("🎉 更新完成！")
        
    except Error as e:
        print(f"❌ 更新异常: {e}")
        if conn:
            conn.rollback()
            conn.close()

if __name__ == "__main__":
    print("🔍 组合插件图片数据检查工具")
    print("=" * 50)
    print("1. 检查数据")
    print("2. 更新示例数据")
    print("=" * 50)
    
    choice = input("请选择操作 (1/2): ").strip()
    
    if choice == "1":
        check_combined_plugins()
    elif choice == "2":
        print("⚠️ 注意：这将更新数据库中的组合插件图片字段")
        confirm = input("确认执行吗？(y/N): ").strip().lower()
        if confirm == 'y':
            update_combined_images()
        else:
            print("❌ 操作已取消")
    else:
        print("❌ 无效选择")
