<template>
  <!-- Portal方式实现，不在模板中渲染任何内容 -->
  <div></div>
</template>

<script>
export default {
  name: 'LogoutConfirmDialog',
  props: {
    // 控制显示隐藏
    visible: {
      type: Boolean,
      default: false
    },
    // 对话框标题
    title: {
      type: String,
      default: '确认退出登录'
    },
    // 取消按钮文本
    cancelText: {
      type: String,
      default: '取消'
    },
    // 确认按钮文本
    confirmText: {
      type: String,
      default: '确认退出'
    },
    // 加载状态
    loading: {
      type: Boolean,
      default: false
    },
    // 加载时的文本
    loadingText: {
      type: String,
      default: '退出中...'
    },
    // 新增：对话框类型
    dialogType: {
      type: String,
      default: 'logout' // 'logout' | 'changePassword'
    },
    // 新增：是否首次修改密码
    isFirstTimePassword: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      portalElement: null,
      savedScrollY: null
    }
  },
  watch: {
    visible: {
      handler(newVal) {
        if (newVal) {
          this.createPortal()
        } else {
          this.removePortal()
        }
      },
      immediate: true
    }
  },
  beforeDestroy() {
    // 组件销毁时清理Portal
    this.removePortal()
  },
  methods: {
    createPortal() {
      // 先清理已存在的Portal
      this.removePortal()

      if (this.portalElement) {
        return
      }

      // 保存当前滚动位置
      this.savedScrollY = window.pageYOffset || document.documentElement.scrollTop

      // 禁止页面滚动
      document.body.style.overflow = 'hidden'
      document.documentElement.style.overflow = 'hidden'

      // 创建Portal元素
      this.portalElement = document.createElement('div')
      this.portalElement.style.cssText = `
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        right: 0 !important;
        bottom: 0 !important;
        width: 100vw !important;
        height: 100vh !important;
        background: rgba(0, 0, 0, 0.85) !important;
        backdrop-filter: blur(20px) !important;
        -webkit-backdrop-filter: blur(20px) !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        z-index: 1000 !important;
        margin: 0 !important;
        padding: 0 !important;
        overflow: hidden !important;
        pointer-events: all !important;
      `

      // 创建对话框内容
      this.portalElement.innerHTML = this.generateDialogHTML()

      // 添加事件监听
      this.portalElement.addEventListener('click', this.handleOverlayClick)

      const cancelBtn = this.portalElement.querySelector('.cancel-btn')
      const confirmBtn = this.portalElement.querySelector('.confirm-btn')
      const dialog = this.portalElement.querySelector('.confirm-dialog')

      if (cancelBtn) {
        cancelBtn.addEventListener('click', this.handleCancel)
      }
      if (confirmBtn) {
        confirmBtn.addEventListener('click', this.handleConfirm)
      }
      if (dialog) {
        dialog.addEventListener('click', (e) => e.stopPropagation())
      }

      // 🔑 新增：如果是密码对话框，添加实时验证
      if (this.dialogType === 'changePassword') {
        this.setupPasswordValidation()
      }

      // 直接挂载到body
      document.body.appendChild(this.portalElement)
    },

    removePortal() {
      if (this.portalElement) {
        // 使用保存的滚动位置
        const scrollY = this.savedScrollY || 0
        console.log('🔍 LogoutConfirmDialog关闭，恢复滚动位置到:', scrollY)

        // 恢复页面滚动
        document.body.style.overflow = ''
        document.documentElement.style.overflow = ''

        // 立即恢复滚动位置
        window.scrollTo(0, scrollY)

        // 使用多重保险确保滚动位置正确
        setTimeout(() => {
          window.scrollTo(0, scrollY)
          console.log('🔍 LogoutConfirmDialog延迟恢复滚动位置到:', scrollY)
        }, 10)

        requestAnimationFrame(() => {
          window.scrollTo(0, scrollY)
        })

        // 移除事件监听
        this.portalElement.removeEventListener('click', this.handleOverlayClick)

        // 从DOM中移除
        if (this.portalElement.parentNode) {
          this.portalElement.parentNode.removeChild(this.portalElement)
        }

        this.portalElement = null
        this.savedScrollY = null
      }
    },

    generateDialogHTML() {
      if (this.dialogType === 'changePassword') {
        return this.generatePasswordDialogHTML()
      }
      return this.generateLogoutDialogHTML()
    },

    generateLogoutDialogHTML() {
      return `
        <div class="confirm-dialog-portal" style="
          background: rgba(255, 255, 255, 0.98) !important;
          border-radius: 24px !important;
          padding: 4rem !important;
          margin: 1rem !important;
          max-width: 700px !important;
          width: 98% !important;
          max-height: 90vh !important;
          overflow-y: auto !important;
          box-shadow: 0 25px 80px rgba(0, 0, 0, 0.4), 0 0 0 1px rgba(255, 255, 255, 0.2) !important;
          backdrop-filter: blur(20px) !important;
          -webkit-backdrop-filter: blur(20px) !important;
          border: 1px solid rgba(124, 138, 237, 0.15) !important;
          z-index: 1000 !important;
          position: relative !important;
        " onclick="event.stopPropagation()">
          <div style="display: flex; align-items: center; gap: 1rem; margin-bottom: 1.5rem;">
            <div style="
              width: 48px; height: 48px; border-radius: 50%; display: flex;
              align-items: center; justify-content: center; color: white; font-size: 24px;
              background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            ">
              <i class="anticon anticon-logout"></i>
            </div>
            <h3 style="font-size: 1.5rem; font-weight: 600; color: #334155; margin: 0;">
              ${this.title}
            </h3>
          </div>

          <div style="margin-bottom: 2rem;">
            <div style="
              display: flex; gap: 1rem; padding: 1.5rem; border-radius: 16px; border: 1px solid;
              background: linear-gradient(135deg, rgba(239, 68, 68, 0.1) 0%, rgba(220, 38, 38, 0.05) 100%);
              border-color: rgba(239, 68, 68, 0.2);
            ">
              <div style="flex-shrink: 0; width: 24px; height: 24px; font-size: 20px; color: #ef4444;">
                <i class="anticon anticon-warning"></i>
              </div>
              <div style="flex: 1; color: #64748b; line-height: 1.6;">
                <p style="margin: 0 0 0.75rem 0;"><strong style="color: #334155; font-weight: 600;">退出登录将清除您的登录状态和本地缓存数据。</strong></p>
                <p style="margin: 0 0 0.75rem 0;">退出后您需要重新登录才能访问个人中心和其他需要登录的功能。</p>
                <p style="color: #ef4444; font-weight: 600; margin: 1rem 0 0 0;">确定要退出登录吗？</p>
              </div>
            </div>
          </div>

          <div style="display: flex; gap: 1rem; justify-content: flex-end;">
            <button class="cancel-btn" style="
              display: flex; align-items: center; gap: 0.5rem; padding: 0.875rem 1.5rem;
              border-radius: 12px; font-weight: 500; cursor: pointer; transition: all 0.3s ease;
              border: 1px solid rgba(148, 163, 184, 0.2); font-size: 14px; min-width: 120px;
              justify-content: center; background: rgba(148, 163, 184, 0.1); color: #64748b;
            ">
              <i class="anticon anticon-close"></i>
              <span>${this.cancelText}</span>
            </button>
            <button class="confirm-btn" style="
              display: flex; align-items: center; gap: 0.5rem; padding: 0.875rem 1.5rem;
              border-radius: 12px; font-weight: 500; cursor: pointer; transition: all 0.3s ease;
              border: none; font-size: 14px; min-width: 120px; justify-content: center;
              background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%); color: white;
              box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
            " ${this.loading ? 'disabled' : ''}>
              <i class="anticon ${this.loading ? 'anticon-loading spinning' : 'anticon-logout'}"></i>
              <span>${this.loading ? this.loadingText : this.confirmText}</span>
            </button>
          </div>
        </div>
      `
    },

    generatePasswordDialogHTML() {
      const isFirstTime = this.isFirstTimePassword

      return `
        <div class="confirm-dialog-portal" style="
          background: rgba(255, 255, 255, 0.98) !important;
          border-radius: 24px !important;
          padding: 4rem !important;
          margin: 1rem !important;
          max-width: 750px !important;
          width: 98% !important;
          max-height: 90vh !important;
          overflow-y: auto !important;
          box-shadow: 0 25px 80px rgba(0, 0, 0, 0.4), 0 0 0 1px rgba(255, 255, 255, 0.2) !important;
          backdrop-filter: blur(20px) !important;
          -webkit-backdrop-filter: blur(20px) !important;
          border: 1px solid rgba(124, 138, 237, 0.15) !important;
          z-index: 1000 !important;
          position: relative !important;
        " onclick="event.stopPropagation()">
          <!-- 标题区域 -->
          <div style="display: flex; align-items: center; gap: 1rem; margin-bottom: 1.5rem;">
            <div style="
              width: 48px; height: 48px; border-radius: 50%; display: flex;
              align-items: center; justify-content: center; color: white; font-size: 24px;
              background: linear-gradient(135deg, #7c8aed 0%, #8b5fbf 100%);
            ">
              <i class="anticon anticon-lock"></i>
            </div>
            <h3 style="font-size: 1.5rem; font-weight: 600; color: #334155; margin: 0;">
              ${this.title}
            </h3>
          </div>

          <!-- 提示信息 -->
          <div style="margin-bottom: 2rem;">
            <div style="
              display: flex; gap: 1rem; padding: 1.5rem; border-radius: 16px; border: 1px solid;
              background: linear-gradient(135deg, rgba(124, 138, 237, 0.1) 0%, rgba(139, 95, 191, 0.05) 100%);
              border-color: rgba(124, 138, 237, 0.2);
            ">
              <div style="flex-shrink: 0; width: 24px; height: 24px; font-size: 20px; color: #7c8aed;">
                <i class="anticon anticon-info-circle"></i>
              </div>
              <div style="flex: 1; color: #64748b; line-height: 1.6;">
                ${isFirstTime ?
                  '<p style="margin: 0;"><strong style="color: #334155;">首次设置密码</strong></p><p style="margin: 0.5rem 0 0 0;">为了您的账户安全，请设置一个强密码。</p>' :
                  '<p style="margin: 0;"><strong style="color: #334155;">修改登录密码</strong></p><p style="margin: 0.5rem 0 0 0;">定期更换密码可以提高账户安全性。</p>'
                }
              </div>
            </div>
          </div>

          <!-- 密码输入表单 -->
          <div style="margin-bottom: 2rem;">
            ${!isFirstTime ? `
              <div style="margin-bottom: 1rem;">
                <label style="display: block; margin-bottom: 0.5rem; font-weight: 500; color: #334155;">当前密码</label>
                <input type="password" id="oldPassword" placeholder="请输入当前密码" style="
                  width: 100%; padding: 0.75rem; border: 1px solid #d1d5db; border-radius: 8px;
                  font-size: 14px; transition: border-color 0.3s ease; box-sizing: border-box;
                " onclick="event.stopPropagation()" onmousedown="event.stopPropagation()" onfocus="event.stopPropagation()" />
              </div>
            ` : ''}

            <div style="margin-bottom: 1rem;">
              <label style="display: block; margin-bottom: 0.5rem; font-weight: 500; color: #334155;">新密码</label>
              <input type="password" id="newPassword" placeholder="请输入新密码" style="
                width: 100%; padding: 0.75rem; border: 1px solid #d1d5db; border-radius: 8px;
                font-size: 14px; transition: border-color 0.3s ease; box-sizing: border-box;
              " onclick="event.stopPropagation()" onmousedown="event.stopPropagation()" onfocus="event.stopPropagation()" />

              <!-- 密码强度指示器 -->
              <div id="passwordStrengthContainer" style="margin-top: 0.5rem; display: none;">
                <div style="display: flex; align-items: center; gap: 0.5rem; margin-bottom: 0.5rem;">
                  <span style="font-size: 12px; color: #64748b;">密码强度：</span>
                  <span id="strengthText" style="font-size: 12px; font-weight: 600;"></span>
                  <div style="flex: 1; height: 4px; background: #e5e7eb; border-radius: 2px; overflow: hidden;">
                    <div id="strengthBar" style="height: 100%; transition: all 0.3s ease; border-radius: 2px; width: 0%;"></div>
                  </div>
                </div>
                <div id="passwordErrors" style="font-size: 12px; color: #ef4444; display: none;"></div>
              </div>
            </div>

            <div style="margin-bottom: 1rem;">
              <label style="display: block; margin-bottom: 0.5rem; font-weight: 500; color: #334155;">确认密码</label>
              <input type="password" id="confirmPassword" placeholder="请再次输入新密码" style="
                width: 100%; padding: 0.75rem; border: 1px solid #d1d5db; border-radius: 8px;
                font-size: 14px; transition: border-color 0.3s ease; box-sizing: border-box;
              " onclick="event.stopPropagation()" onmousedown="event.stopPropagation()" onfocus="event.stopPropagation()" />
              <div id="confirmPasswordError" style="margin-top: 0.5rem; font-size: 12px; color: #ef4444; display: none;"></div>
            </div>

            <!-- 密码要求提示 -->
            <div style="
              padding: 1rem; background: rgba(124, 138, 237, 0.05); border: 1px solid rgba(124, 138, 237, 0.1);
              border-radius: 8px; font-size: 12px; color: #64748b; line-height: 1.5;
            ">
              <div style="font-weight: 600; color: #334155; margin-bottom: 0.5rem;">密码要求：</div>
              <div>• 长度8-32位</div>
              <div>• 至少包含3种字符类型（大写字母、小写字母、数字、特殊字符）</div>
              ${!isFirstTime ? '<div>• 新密码不能与当前密码相同</div>' : ''}
              <div>• 不能过于简单（如连续相同字符等）</div>
            </div>
          </div>

          <!-- 按钮区域 -->
          <div style="display: flex; gap: 1rem; justify-content: flex-end;">
            <button class="cancel-btn" style="
              display: flex; align-items: center; gap: 0.5rem; padding: 0.875rem 1.5rem;
              border-radius: 12px; font-weight: 500; cursor: pointer; transition: all 0.3s ease;
              border: 1px solid rgba(148, 163, 184, 0.2); font-size: 14px; min-width: 120px;
              justify-content: center; background: rgba(148, 163, 184, 0.1); color: #64748b;
            ">
              <i class="anticon anticon-close"></i>
              <span>${this.cancelText}</span>
            </button>
            <button class="confirm-btn" id="confirmBtn" style="
              display: flex; align-items: center; gap: 0.5rem; padding: 0.875rem 1.5rem;
              border-radius: 12px; font-weight: 500; cursor: pointer; transition: all 0.3s ease;
              border: none; font-size: 14px; min-width: 120px; justify-content: center;
              background: linear-gradient(135deg, #7c8aed 0%, #8b5fbf 100%); color: white;
              box-shadow: 0 4px 12px rgba(124, 138, 237, 0.3);
            " ${this.loading ? 'disabled' : ''}>
              <i class="anticon ${this.loading ? 'anticon-loading spinning' : 'anticon-check'}"></i>
              <span>${this.loading ? this.loadingText : this.confirmText}</span>
            </button>
          </div>
        </div>
      `
    },

    handleOverlayClick(event) {
      // 获取对话框元素
      const dialogElement = this.portalElement.querySelector('.confirm-dialog-portal')

      if (!dialogElement) {
        this.handleCancel()
        return
      }

      // 获取对话框的边界
      const dialogRect = dialogElement.getBoundingClientRect()

      // 定义安全区域（对话框周围50px的缓冲区）
      const safeZone = 50
      const safeRect = {
        left: dialogRect.left - safeZone,
        right: dialogRect.right + safeZone,
        top: dialogRect.top - safeZone,
        bottom: dialogRect.bottom + safeZone
      }

      // 检查点击位置是否在安全区域外
      const clickX = event.clientX
      const clickY = event.clientY

      const isOutsideSafeZone = (
        clickX < safeRect.left ||
        clickX > safeRect.right ||
        clickY < safeRect.top ||
        clickY > safeRect.bottom
      )

      // 只有点击在安全区域外才关闭弹窗
      if (isOutsideSafeZone) {
        this.handleCancel()
      }
    },
    handleCancel() {
      this.$emit('cancel')
      this.$emit('update:visible', false)
    },
    handleConfirm() {
      this.$emit('confirm')
    },

    // 🔑 新增：设置密码实时验证
    setupPasswordValidation() {
      const newPasswordInput = this.portalElement.querySelector('#newPassword')
      const confirmPasswordInput = this.portalElement.querySelector('#confirmPassword')
      const oldPasswordInput = this.portalElement.querySelector('#oldPassword')

      if (newPasswordInput) {
        newPasswordInput.addEventListener('input', () => {
          this.validatePasswordRealtime()
        })
      }

      if (confirmPasswordInput) {
        confirmPasswordInput.addEventListener('input', () => {
          this.validatePasswordRealtime()
        })
      }

      if (oldPasswordInput) {
        oldPasswordInput.addEventListener('input', () => {
          this.validatePasswordRealtime()
        })
      }
    },

    // 🔑 新增：实时密码验证
    validatePasswordRealtime() {
      const newPasswordInput = this.portalElement.querySelector('#newPassword')
      const confirmPasswordInput = this.portalElement.querySelector('#confirmPassword')
      const oldPasswordInput = this.portalElement.querySelector('#oldPassword')

      const newPassword = newPasswordInput ? newPasswordInput.value : ''
      const confirmPassword = confirmPasswordInput ? confirmPasswordInput.value : ''
      const oldPassword = oldPasswordInput ? oldPasswordInput.value : ''

      // 密码强度验证
      const validation = this.validatePasswordStrength(newPassword)

      // 更新密码强度显示
      this.updatePasswordStrengthDisplay(newPassword, validation)

      // 更新确认密码验证
      this.updateConfirmPasswordDisplay(newPassword, confirmPassword)

      // 更新确认按钮状态
      this.updateConfirmButtonState(validation, newPassword, confirmPassword, oldPassword)
    },

    // 🔑 新增：密码强度验证逻辑
    validatePasswordStrength(password) {
      const errors = []

      // 长度检查
      if (password.length > 0 && password.length < 8) {
        errors.push('密码长度至少8位')
      }

      if (password.length > 32) {
        errors.push('密码长度不能超过32位')
      }

      // 字符类型检查 - 至少包含3种
      if (password.length > 0) {
        let charTypeCount = 0
        if (/[A-Z]/.test(password)) charTypeCount++      // 大写字母
        if (/[a-z]/.test(password)) charTypeCount++      // 小写字母
        if (/\d/.test(password)) charTypeCount++         // 数字
        if (/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) charTypeCount++ // 特殊字符

        if (charTypeCount < 3) {
          errors.push('至少包含3种字符类型（大写字母、小写字母、数字、特殊字符）')
        }
      }

      // 弱密码检查
      if (password.length > 0) {
        if (/(.)\1{3,}/.test(password)) {
          errors.push('不能包含连续4个相同字符')
        }

        if (/^123456/.test(password)) {
          errors.push('不能以123456开头')
        }

        if (/^password$/i.test(password)) {
          errors.push('不能是password')
        }
      }

      // 计算强度
      let strength = this.calculatePasswordStrength(password)

      return {
        isValid: errors.length === 0 && password.length >= 8,
        errors: errors,
        strength: strength
      }
    },

    // 🔑 新增：计算密码强度
    calculatePasswordStrength(password) {
      if (password.length === 0) {
        return { level: 'none', text: '', color: '#d1d5db' }
      }

      let score = 0

      // 长度加分
      if (password.length >= 8) score += 1
      if (password.length >= 12) score += 1
      if (password.length >= 16) score += 1

      // 字符类型加分
      if (/[a-z]/.test(password)) score += 1
      if (/[A-Z]/.test(password)) score += 1
      if (/\d/.test(password)) score += 1
      if (/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) score += 1

      // 组合加分
      if (/(?=.*[a-zA-Z])(?=.*\d)/.test(password)) score += 1

      if (score <= 2) return { level: 'weak', text: '弱', color: '#ef4444' }
      if (score <= 4) return { level: 'medium', text: '中', color: '#f59e0b' }
      if (score <= 6) return { level: 'strong', text: '强', color: '#10b981' }
      return { level: 'very-strong', text: '很强', color: '#059669' }
    },

    // 🔑 新增：更新密码强度显示
    updatePasswordStrengthDisplay(password, validation) {
      const strengthContainer = this.portalElement.querySelector('#passwordStrengthContainer')

      if (!strengthContainer) return

      if (password.length === 0) {
        strengthContainer.style.display = 'none'
        return
      }

      strengthContainer.style.display = 'block'

      // 更新强度文本和颜色
      const strengthText = strengthContainer.querySelector('#strengthText')
      const strengthBar = strengthContainer.querySelector('#strengthBar')
      const errorsContainer = strengthContainer.querySelector('#passwordErrors')

      if (strengthText && strengthBar) {
        strengthText.textContent = validation.strength.text
        strengthText.style.color = validation.strength.color

        // 强度条宽度
        const widthPercent = {
          'none': '0%',
          'weak': '25%',
          'medium': '50%',
          'strong': '75%',
          'very-strong': '100%'
        }[validation.strength.level] || '0%'

        strengthBar.style.width = widthPercent
        strengthBar.style.background = validation.strength.color
      }

      // 更新错误提示
      if (errorsContainer) {
        if (validation.errors.length > 0) {
          errorsContainer.textContent = validation.errors.join('，')
          errorsContainer.style.display = 'block'
        } else {
          errorsContainer.style.display = 'none'
        }
      }
    },

    // 🔑 新增：更新确认密码显示
    updateConfirmPasswordDisplay(newPassword, confirmPassword) {
      const confirmErrorContainer = this.portalElement.querySelector('#confirmPasswordError')

      if (!confirmErrorContainer) return

      if (confirmPassword.length === 0) {
        confirmErrorContainer.style.display = 'none'
        return
      }

      if (confirmPassword !== newPassword) {
        confirmErrorContainer.textContent = '两次输入的密码不一致'
        confirmErrorContainer.style.display = 'block'
      } else {
        confirmErrorContainer.style.display = 'none'
      }
    },

    // 🔑 新增：更新确认按钮状态
    updateConfirmButtonState(validation, newPassword, confirmPassword, oldPassword) {
      const confirmBtn = this.portalElement.querySelector('#confirmBtn')

      if (!confirmBtn) return

      // 检查所有条件
      const isPasswordValid = validation.isValid && newPassword.length >= 8
      const isConfirmValid = confirmPassword === newPassword && confirmPassword.length > 0
      const isOldPasswordValid = !this.portalElement.querySelector('#oldPassword') || oldPassword.length > 0

      const isValid = isPasswordValid && isConfirmValid && isOldPasswordValid

      confirmBtn.disabled = !isValid
      confirmBtn.style.opacity = isValid ? '1' : '0.5'
      confirmBtn.style.cursor = isValid ? 'pointer' : 'not-allowed'
    }
  }
}
</script>

<style scoped>
.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}
</style>
