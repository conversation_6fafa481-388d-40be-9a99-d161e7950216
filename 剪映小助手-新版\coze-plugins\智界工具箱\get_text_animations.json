{"openapi": "3.0.0", "info": {"title": "剪映小助手_智界工具箱 - 获取文字出入场动画", "description": "获取文字出入场动画", "version": "1.0.0"}, "servers": [{"url": "https://www.aigcview.com"}], "paths": {"/jeecg-boot/api/jianying/get_text_animations": {"post": {"summary": "获取文字出入场动画", "description": "获取文字出入场动画", "operationId": "get_text_animations", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"access_key": {"type": "string", "description": "访问密钥（必填）", "example": "JianyingAPI_2025_SecureAccess_AigcView", "default": "JianyingAPI_2025_SecureAccess_AigcView"}, "mode": {"type": "integer", "description": "动画模式，0：默认所有，1：VIP，2：免费", "enum": [0, 1, 2], "example": 0}, "type": {"type": "string", "description": "动画类型，in：入场，out：出场，loop：循环", "enum": ["in", "out", "loop"], "example": "in"}}, "required": ["access_key"], "additionalProperties": false}}}}, "responses": {"200": {"description": "成功获取文字动画", "content": {"application/json": {"schema": {"type": "object", "properties": {"effects": {"type": "array", "description": "可用的文字动画列表", "items": {"type": "object", "properties": {"resource_id": {"type": "string", "description": "动画资源ID"}, "type": {"type": "string", "description": "动画类型（in/out/loop）"}, "category_id": {"type": "string", "description": "分类ID"}, "duration": {"type": "integer", "description": "动画时长（微秒）"}, "name": {"type": "string", "description": "动画名称"}, "panel": {"type": "string", "description": "面板类型（text）"}, "platform": {"type": "string", "description": "平台类型"}, "category_name": {"type": "string", "description": "分类名称"}, "id": {"type": "string", "description": "动画ID"}, "material_type": {"type": "string", "description": "素材类型（text）"}}}}}, "required": ["effects"]}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "description": "操作是否成功", "example": false}, "error": {"type": "string", "description": "错误信息"}, "code": {"type": "string", "description": "错误代码"}}, "required": ["success", "error"]}}}}, "500": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "description": "操作是否成功", "example": false}, "error": {"type": "string", "description": "错误信息"}, "code": {"type": "string", "description": "错误代码"}}, "required": ["success", "error"]}}}}}}}}}