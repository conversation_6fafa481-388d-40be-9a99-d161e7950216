{"openapi": "3.0.0", "info": {"title": "剪映小助手_智界音频库 - 背景音乐搜索", "description": "搜索剪映背景音乐库", "version": "1.0.0"}, "servers": [{"url": "https://www.aigcview.com"}], "paths": {"/jeecg-boot/api/jianying/bgm_search": {"post": {"summary": "搜索背景音乐", "description": "搜索剪映背景音乐库", "operationId": "bgm_search", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"access_key": {"type": "string", "description": "访问密钥（必填）", "example": "JianyingAPI_2025_SecureAccess_AigcView", "default": "JianyingAPI_2025_SecureAccess_AigcView"}, "keyword": {"type": "string", "description": "搜索关键词（必填）", "example": "我的天空"}, "type": {"type": "string", "description": "音乐类型筛选：0-默认所有，1-VIP，2-免费", "enum": ["0", "1", "2"], "example": "0", "default": "0"}}, "required": ["access_key", "keyword"], "additionalProperties": false}}}}, "responses": {"200": {"description": "成功搜索背景音乐", "content": {"application/json": {"schema": {"type": "object", "properties": {"data": {"type": "array", "description": "背景音乐列表", "items": {"type": "object", "properties": {"timelines": {"type": "array", "description": "时间轴信息", "items": {"type": "object", "properties": {"end": {"type": "integer", "description": "结束时间（微秒）"}, "start": {"type": "integer", "description": "开始时间（微秒）"}}}}, "title": {"type": "string", "description": "音乐标题"}, "bgm_url": {"type": "string", "description": "音乐URL"}, "bgm_urls": {"type": "array", "description": "音乐URL列表", "items": {"type": "string"}}, "duration": {"type": "integer", "description": "音乐时长（微秒）"}}}}, "message": {"type": "string", "description": "响应消息"}, "success": {"type": "boolean", "description": "是否成功"}}}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"type": "object", "properties": {"data": {"type": "array", "description": "空数据", "items": {"type": "object"}}, "message": {"type": "string", "description": "错误消息"}, "success": {"type": "boolean", "description": "是否成功"}}}}}}}}}}}