{"version": 3, "file": "7za.js", "sourceRoot": "", "sources": ["../src/7za.ts"], "names": [], "mappings": ";;;AAAA,wCAA0C;AAC1C,uCAAgC;AAEzB,KAAK,UAAU,UAAU;IAC9B,MAAM,IAAA,gBAAK,EAAC,mBAAO,EAAE,KAAK,CAAC,CAAA;IAC3B,OAAO,mBAAO,CAAA;AAChB,CAAC;AAHD,gCAGC;AAEM,KAAK,UAAU,SAAS;IAC7B,MAAM,IAAA,gBAAK,EAAC,kBAAM,EAAE,KAAK,CAAC,CAAA;IAC1B,OAAO,kBAAM,CAAA;AACf,CAAC;AAHD,8BAGC", "sourcesContent": ["import { path7x, path7za } from \"7zip-bin\"\nimport { chmod } from \"fs-extra\"\n\nexport async function getPath7za(): Promise<string> {\n  await chmod(path7za, 0o755)\n  return path7za\n}\n\nexport async function getPath7x(): Promise<string> {\n  await chmod(path7x, 0o755)\n  return path7x\n}\n"]}