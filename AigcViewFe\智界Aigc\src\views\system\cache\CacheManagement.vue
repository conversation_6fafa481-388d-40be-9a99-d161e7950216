<template>
  <div class="cache-management">
    <a-card title="🗄️ 系统缓存管理" :bordered="false">
      <div class="cache-stats" style="margin-bottom: 24px;">
        <a-row :gutter="16">
          <a-col :span="8">
            <a-statistic title="字典缓存数量" :value="cacheStats.dictCacheCount" />
          </a-col>
          <a-col :span="8">
            <a-statistic title="启用字典缓存数量" :value="cacheStats.enableDictCacheCount" />
          </a-col>
          <a-col :span="8">
            <a-statistic title="总缓存数量" :value="cacheStats.totalCacheCount" />
          </a-col>
        </a-row>
      </div>

      <a-divider />

      <div class="cache-operations">
        <a-space size="large">
          <a-button 
            type="danger" 
            size="large"
            @click="clearAllCache" 
            :loading="clearingAll"
            :disabled="clearingSpecific">
            <a-icon type="delete" />
            清除所有字典缓存
          </a-button>
          
          <a-input-group compact style="width: 400px;">
            <a-select 
              v-model="selectedDictCode" 
              placeholder="选择要清除的字典"
              style="width: 70%"
              :disabled="clearingAll">
              <a-select-option value="plugin_category">插件分类 (plugin_category)</a-select-option>
              <a-select-option value="isTrue">是否 (isTrue)</a-select-option>
              <a-select-option value="plugin_status">插件状态 (plugin_status)</a-select-option>
              <a-select-option value="sex">性别 (sex)</a-select-option>
              <a-select-option value="priority">优先级 (priority)</a-select-option>
            </a-select>
            <a-button 
              type="primary" 
              @click="clearSpecificCache"
              :loading="clearingSpecific"
              :disabled="!selectedDictCode || clearingAll"
              style="width: 30%">
              <a-icon type="reload" />
              清除指定缓存
            </a-button>
          </a-input-group>

          <a-button 
            @click="refreshStats"
            :loading="loadingStats"
            :disabled="clearingAll || clearingSpecific">
            <a-icon type="sync" />
            刷新统计
          </a-button>
        </a-space>
      </div>

      <a-divider />

      <div class="cache-help">
        <a-alert
          message="缓存清除说明"
          type="info"
          show-icon>
          <div slot="description">
            <ul>
              <li><strong>清除所有字典缓存</strong>：清除Redis中所有字典相关缓存，并通知所有在线用户刷新前端缓存</li>
              <li><strong>清除指定缓存</strong>：只清除选定字典的缓存，适用于单个字典更新后的缓存刷新</li>
              <li><strong>自动通知</strong>：缓存清除后会自动通知所有在线用户，用户无需手动刷新浏览器</li>
              <li><strong>权限要求</strong>：需要 system:cache:clear 权限才能执行缓存清除操作</li>
            </ul>
          </div>
        </a-alert>
      </div>

      <a-divider />

      <div class="operation-log">
        <h3>最近操作记录</h3>
        <a-list
          :data-source="operationLogs"
          :loading="loadingLogs">
          <a-list-item slot="renderItem" slot-scope="item">
            <a-list-item-meta>
              <div slot="title">
                <a-tag :color="item.type === 'CLEAR_ALL' ? 'red' : 'blue'">
                  {{ item.type === 'CLEAR_ALL' ? '清除所有缓存' : '清除指定缓存' }}
                </a-tag>
                <span v-if="item.dictCode">{{ item.dictCode }}</span>
              </div>
              <div slot="description">
                操作人：{{ item.operator }} | 时间：{{ item.time }} | 结果：{{ item.result }}
              </div>
            </a-list-item-meta>
          </a-list-item>
        </a-list>
      </div>
    </a-card>
  </div>
</template>

<script>
import { getAction, postAction } from '@/api/manage'

export default {
  name: 'CacheManagement',
  data() {
    return {
      cacheStats: {
        dictCacheCount: 0,
        enableDictCacheCount: 0,
        totalCacheCount: 0
      },
      selectedDictCode: '',
      clearingAll: false,
      clearingSpecific: false,
      loadingStats: false,
      loadingLogs: false,
      operationLogs: []
    }
  },
  
  mounted() {
    this.loadCacheStats()
    this.loadOperationLogs()
  },
  
  methods: {
    // 加载缓存统计信息
    async loadCacheStats() {
      this.loadingStats = true
      try {
        const response = await getAction('/sys/cache/getCacheStats')
        if (response.success) {
          this.cacheStats = response.result
        } else {
          this.$message.error('获取缓存统计失败：' + response.message)
        }
      } catch (error) {
        this.$message.error('获取缓存统计异常：' + error.message)
      } finally {
        this.loadingStats = false
      }
    },

    // 清除所有缓存
    async clearAllCache() {
      const that = this
      this.$confirm({
        title: '确认清除所有字典缓存？',
        content: '此操作将清除Redis中所有字典缓存，并通知所有在线用户刷新。操作不可撤销，请确认！',
        okText: '确认清除',
        okType: 'danger',
        cancelText: '取消',
        onOk() {
          that.executeClearAllCache()
        }
      })
    },

    async executeClearAllCache() {
      this.clearingAll = true
      try {
        const response = await postAction('/sys/cache/clearAllDictCache')
        if (response.success) {
          this.$message.success(response.message)
          this.addOperationLog('CLEAR_ALL', '', '成功')
          await this.loadCacheStats()
        } else {
          this.$message.error('清除缓存失败：' + response.message)
          this.addOperationLog('CLEAR_ALL', '', '失败：' + response.message)
        }
      } catch (error) {
        this.$message.error('清除缓存异常：' + error.message)
        this.addOperationLog('CLEAR_ALL', '', '异常：' + error.message)
      } finally {
        this.clearingAll = false
      }
    },

    // 清除指定缓存
    async clearSpecificCache() {
      if (!this.selectedDictCode) {
        this.$message.warning('请选择要清除的字典')
        return
      }

      this.clearingSpecific = true
      try {
        const response = await postAction('/sys/cache/clearSpecificDictCache', {
          dictCode: this.selectedDictCode
        })
        if (response.success) {
          this.$message.success(response.message)
          this.addOperationLog('CLEAR_SPECIFIC', this.selectedDictCode, '成功')
          await this.loadCacheStats()
        } else {
          this.$message.error('清除缓存失败：' + response.message)
          this.addOperationLog('CLEAR_SPECIFIC', this.selectedDictCode, '失败：' + response.message)
        }
      } catch (error) {
        this.$message.error('清除缓存异常：' + error.message)
        this.addOperationLog('CLEAR_SPECIFIC', this.selectedDictCode, '异常：' + error.message)
      } finally {
        this.clearingSpecific = false
      }
    },

    // 刷新统计
    async refreshStats() {
      await this.loadCacheStats()
      this.$message.success('统计信息已刷新')
    },

    // 加载操作日志
    loadOperationLogs() {
      // 从localStorage获取操作日志
      const logs = JSON.parse(localStorage.getItem('cache_operation_logs') || '[]')
      this.operationLogs = logs.slice(0, 10) // 只显示最近10条
    },

    // 添加操作日志
    addOperationLog(type, dictCode, result) {
      const log = {
        type,
        dictCode,
        result,
        operator: this.$store.getters.userInfo.username || '未知',
        time: new Date().toLocaleString()
      }
      
      let logs = JSON.parse(localStorage.getItem('cache_operation_logs') || '[]')
      logs.unshift(log)
      logs = logs.slice(0, 50) // 只保留最近50条
      
      localStorage.setItem('cache_operation_logs', JSON.stringify(logs))
      this.loadOperationLogs()
    }
  }
}
</script>

<style scoped>
.cache-management {
  padding: 24px;
}

.cache-operations {
  text-align: center;
}

.cache-help {
  margin-top: 24px;
}

.operation-log {
  margin-top: 24px;
}
</style>
