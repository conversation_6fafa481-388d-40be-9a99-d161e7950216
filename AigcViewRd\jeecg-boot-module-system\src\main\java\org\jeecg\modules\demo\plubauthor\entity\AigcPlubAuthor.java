package org.jeecg.modules.demo.plubauthor.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 插件创作者
 * @Author: jeecg-boot
 * @Date:   2025-06-14
 * @Version: V1.0
 */
@Data
@TableName("aigc_plub_author")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="aigc_plub_author对象", description="插件创作者")
public class AigcPlubAuthor implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private java.lang.String id;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private java.util.Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private java.util.Date updateTime;
	/**所属部门*/
    @ApiModelProperty(value = "所属部门")
    private java.lang.String sysOrgCode;
	/**创作者名称*/
	@Excel(name = "创作者名称", width = 15)
    @ApiModelProperty(value = "创作者名称")
    private java.lang.String authorname;
	/**作者职位头衔*/
	@Excel(name = "作者职位", width = 15, dicCode = "author_title")
	@Dict(dicCode = "author_title")
    @ApiModelProperty(value = "作者职位头衔")
    private java.lang.String title;
	/**专业领域*/
	@Excel(name = "专业领域", width = 20, dicCode = "author_expertise")
	@Dict(dicCode = "author_expertise")
    @ApiModelProperty(value = "专业领域（多选，逗号分隔）")
    private java.lang.String expertise;
	/**插件数*/
	@Excel(name = "插件数", width = 15)
    @ApiModelProperty(value = "插件数")
    private java.lang.Integer plubnum;
	/**插件使用总数*/
	@Excel(name = "插件使用总数", width = 15)
    @ApiModelProperty(value = "插件使用总数")
    private java.lang.Integer plubusenum;
	/**累计收益*/
	@Excel(name = "累计收益", width = 15)
    @ApiModelProperty(value = "累计收益")
    private java.math.BigDecimal totalIncome;
	/**创作者简介*/
	@Excel(name = "创作者简介", width = 15)
    @ApiModelProperty(value = "创作者简介")
    private java.lang.String createinfo;
}
