import TOSBase from '../../base';
interface ListPartInput {
    bucket?: string;
    key: string;
    uploadId: string;
    maxParts?: number;
    partNumberMarker?: number;
    encodingType?: string;
}
interface ListPartOutput {
    Bucket: string;
    Key: string;
    UploadId: string;
    PartNumberMarker: number;
    NextPartNumberMarker: number;
    MaxParts: number;
    IsTruncated: boolean;
    StorageClass: string;
    Owner: {
        ID: string;
        DisplayName: string;
    };
    Parts: {
        PartNumber: number;
        LastModified: string;
        ETag: string;
        Size: number;
    }[];
}
export declare const MIN_PART_SIZE_EXCEPT_LAST_ONE: number;
export declare const MAX_PART_NUMBER = 10000;
export declare const calculateSafePartSize: (totalSize: number, expectPartSize: number, showWarning?: boolean) => number;
export declare function listParts(this: TOSBase, input: ListPartInput): Promise<import("../../base").TosResponse<ListPartOutput>>;
export {};
