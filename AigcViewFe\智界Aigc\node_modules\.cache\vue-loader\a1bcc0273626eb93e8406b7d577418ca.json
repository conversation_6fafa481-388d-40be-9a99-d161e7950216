{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\workflow\\components\\CreatorAgentForm.vue?vue&type=template&id=365d0b54&scoped=true&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\workflow\\components\\CreatorAgentForm.vue", "mtime": 1754512791552}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\n<a-modal\n  :title=\"modalTitle\"\n  :visible=\"visible\"\n  :width=\"900\"\n  :confirm-loading=\"stepLoading\"\n  :mask-closable=\"false\"\n  :centered=\"true\"\n  :body-style=\"{ padding: '0' }\"\n  :footer=\"null\"\n  class=\"creator-agent-modal\"\n  @cancel=\"handleCancel\"\n>\n  <!-- 步骤条 -->\n  <div class=\"step-header\">\n    <a-steps :current=\"currentStep\" class=\"custom-steps\">\n      <a-step title=\"智能体信息\" description=\"填写基本信息\" />\n      <a-step title=\"工作流配置\" description=\"配置工作流\" />\n      <a-step title=\"完成创建\" description=\"创建完成\" />\n    </a-steps>\n  </div>\n\n  <!-- 步骤内容 -->\n  <div class=\"step-content\">\n    <!-- 第一步：智能体基本信息 -->\n    <div v-show=\"currentStep === 0\" class=\"step-panel\">\n      <div class=\"panel-header\">\n        <h3 class=\"panel-title\">\n          <a-icon type=\"robot\" />\n          智能体基本信息\n        </h3>\n        <p class=\"panel-desc\">请填写智能体的基本信息，这些信息将展示给用户</p>\n      </div>\n\n      <a-form-model\n        ref=\"form\"\n        :model=\"formData\"\n        :rules=\"rules\"\n        class=\"modern-form\"\n      >\n        <!-- 智能体名称 -->\n        <div class=\"form-card\">\n          <a-form-model-item prop=\"agentName\">\n            <div class=\"field-label\">\n              <a-icon type=\"robot\" />\n              智能体名称\n              <span class=\"required-star\">*</span>\n            </div>\n            <a-input\n              v-model=\"formData.agentName\"\n              placeholder=\"请输入智能体名称\"\n              size=\"large\"\n              class=\"modern-input\"\n              :max-length=\"100\"\n              show-count\n            />\n            <div class=\"field-tips\">\n              为您的智能体起一个吸引人的名称\n            </div>\n          </a-form-model-item>\n        </div>\n\n        <!-- 智能体描述 -->\n        <div class=\"form-card\">\n          <a-form-model-item prop=\"agentDescription\">\n            <div class=\"field-label\">\n              <a-icon type=\"file-text\" />\n              智能体描述\n              <span class=\"required-star\">*</span>\n            </div>\n            <a-textarea\n              v-model=\"formData.agentDescription\"\n              placeholder=\"请详细描述您的智能体功能和特点\"\n              :rows=\"4\"\n              :max-length=\"1000\"\n              show-count\n              class=\"modern-textarea\"\n            />\n            <div class=\"field-tips\">\n              详细描述有助于用户了解您的智能体功能\n            </div>\n          </a-form-model-item>\n        </div>\n\n        <!-- 智能体头像 -->\n        <div class=\"form-card\">\n          <a-form-model-item prop=\"agentAvatar\">\n            <div class=\"field-label\">\n              <a-icon type=\"picture\" />\n              智能体头像\n              <span class=\"required-star\">*</span>\n            </div>\n            <j-image-upload-deferred\n              ref=\"avatarUpload\"\n              v-model=\"formData.agentAvatar\"\n              :isMultiple=\"false\"\n              bizPath=\"agent-avatar\"\n              text=\"上传头像\">\n            </j-image-upload-deferred>\n            <div class=\"field-tips\">\n              支持 JPG、PNG 格式，文件大小不超过 5MB\n            </div>\n          </a-form-model-item>\n        </div>\n\n        <!-- 体验链接 -->\n        <div class=\"form-card\">\n          <a-form-model-item prop=\"experienceLink\">\n            <div class=\"field-label\">\n              <a-icon type=\"link\" />\n              体验链接\n              <span class=\"optional\">（可选）</span>\n            </div>\n            <a-input\n              v-model=\"formData.experienceLink\"\n              placeholder=\"请输入体验链接\"\n              size=\"large\"\n              class=\"modern-input\"\n              :max-length=\"500\"\n            />\n            <div class=\"field-tips\">\n              用户可以通过此链接体验您的智能体功能\n            </div>\n          </a-form-model-item>\n        </div>\n\n        <!-- 价格 -->\n        <div class=\"form-card\">\n          <a-form-model-item prop=\"price\">\n            <div class=\"field-label\">\n              <a-icon type=\"dollar\" />\n              价格设置\n              <span class=\"required-star\">*</span>\n            </div>\n            <a-input-number\n              v-model=\"formData.price\"\n              placeholder=\"请输入价格\"\n              :min=\"0\"\n              :max=\"99999\"\n              :precision=\"2\"\n              :step=\"0.01\"\n              size=\"large\"\n              class=\"modern-input-number\"\n            >\n              <template slot=\"addonBefore\">¥</template>\n            </a-input-number>\n            <div class=\"field-tips\">\n              设置智能体的使用价格，用户购买后可以使用您的智能体\n            </div>\n          </a-form-model-item>\n        </div>\n      </a-form-model>\n\n      <!-- 第一步底部按钮 -->\n      <div class=\"step-footer\">\n        <a-button @click=\"handleCancel\" :disabled=\"stepLoading\">\n          取消\n        </a-button>\n        <a-button type=\"primary\" @click=\"handleNext\" :loading=\"stepLoading\" class=\"next-btn\">\n          下一步：配置工作流\n          <a-icon type=\"arrow-right\" />\n        </a-button>\n      </div>\n    </div>\n\n    <!-- 第二步：工作流配置 -->\n    <div v-show=\"currentStep === 1\" class=\"step-panel\">\n      <div class=\"panel-header\">\n        <h3 class=\"panel-title\">\n          <a-icon type=\"apartment\" />\n          工作流配置\n        </h3>\n        <p class=\"panel-desc\">为您的智能体配置工作流，提升智能体的功能和效率</p>\n      </div>\n\n      <!-- 已创建的智能体信息 -->\n      <div class=\"created-agent-info\" v-if=\"createdAgent\">\n        <div class=\"agent-summary\">\n          <div class=\"agent-avatar\">\n            <img :src=\"getFullAvatarUrl(createdAgent.agentAvatar)\" :alt=\"createdAgent.agentName\" v-if=\"createdAgent.agentAvatar\" />\n            <a-icon type=\"robot\" v-else />\n          </div>\n          <div class=\"agent-details\">\n            <h4>{{ createdAgent.agentName }}</h4>\n            <p>{{ createdAgent.agentDescription }}</p>\n            <span class=\"agent-price\">¥{{ createdAgent.price }}</span>\n          </div>\n        </div>\n      </div>\n\n      <!-- 工作流配置区域 -->\n      <div class=\"workflow-section\">\n        <!-- 工作流表单 -->\n        <div class=\"workflow-form\">\n          <div class=\"form-header\">\n            <h3>\n              <a-icon type=\"apartment\" />\n              新增工作流\n            </h3>\n            <p>为您的智能体添加工作流，让它更加智能和实用</p>\n          </div>\n\n          <a-form-model ref=\"workflowFormRef\" :model=\"workflowFormData\" :rules=\"workflowRules\" layout=\"vertical\">\n            <a-form-model-item label=\"工作流名称\" prop=\"workflowName\">\n              <a-input\n                v-model=\"workflowFormData.workflowName\"\n                placeholder=\"请输入工作流名称，如：文档生成助手\"\n                :maxLength=\"30\"\n                show-count\n              />\n            </a-form-model-item>\n\n            <a-form-model-item label=\"工作流描述\" prop=\"workflowDescription\">\n              <a-textarea\n                v-model=\"workflowFormData.workflowDescription\"\n                placeholder=\"请描述工作流的功能和用途，帮助用户了解其作用\"\n                :rows=\"3\"\n                :maxLength=\"200\"\n                show-count\n              />\n            </a-form-model-item>\n\n            <a-form-model-item label=\"输入参数说明\" prop=\"inputParamsDesc\">\n              <a-textarea\n                v-model=\"workflowFormData.inputParamsDesc\"\n                placeholder=\"格式：参数:值 或 参数:&quot;值&quot; 或 参数:'值'（例如：name:&quot;张三&quot;,age:25,city:'北京'）\"\n                :rows=\"4\"\n                :maxLength=\"10000\"\n                @blur=\"handleInputParamsBlur\"\n              />\n              <div style=\"color: #666; font-size: 12px; margin-top: 4px;\">\n                * 必填项，例如：name:&quot;张三&quot;,age:25,city:'北京' 支持中英文冒号逗号\n              </div>\n            </a-form-model-item>\n\n            <a-form-model-item label=\"工作流文件\" prop=\"workflowPackage\">\n              <!-- 文件上传区域 -->\n              <div class=\"workflow-file-upload\">\n                <!-- 已上传文件显示 -->\n                <div v-if=\"workflowFileInfo\" class=\"uploaded-file-info\">\n                  <div class=\"file-item\" :class=\"{ 'saved-file': workflowFileInfo.isSaved }\">\n                    <a-icon type=\"file-zip\" class=\"file-icon\" />\n                    <span class=\"file-name\">{{ workflowFileInfo.originalName || workflowFileInfo.name }}</span>\n                    <!-- 🔥 已保存的文件显示状态标签 -->\n                    <a-tag v-if=\"workflowFileInfo.isSaved\" color=\"green\" size=\"small\" style=\"margin-left: 8px;\">\n                      已保存\n                    </a-tag>\n                    <a-button\n                      type=\"link\"\n                      size=\"small\"\n                      @click=\"handleRemoveWorkflowFile\"\n                      class=\"remove-btn\"\n                      :title=\"workflowFileInfo.isSaved ? '重新选择文件' : '删除文件'\"\n                    >\n                      <a-icon :type=\"workflowFileInfo.isSaved ? 'edit' : 'delete'\" />\n                      {{ workflowFileInfo.isSaved ? '重选' : '删除' }}\n                    </a-button>\n                  </div>\n                </div>\n\n                <!-- 上传按钮 -->\n                <div v-else class=\"upload-area\">\n                  <a-upload\n                    ref=\"workflowUpload\"\n                    name=\"file\"\n                    :multiple=\"false\"\n                    :before-upload=\"beforeWorkflowUpload\"\n                    :show-upload-list=\"false\"\n                    @change=\"handleWorkflowFileSelect\"\n                    accept=\".zip\"\n                    :customRequest=\"() => {}\"\n                  >\n                    <a-button :loading=\"workflowUploading\">\n                      <a-icon type=\"upload\" />\n                      选择工作流压缩包\n                    </a-button>\n                  </a-upload>\n                </div>\n              </div>\n\n              <div class=\"upload-tip\">\n                <a-icon type=\"exclamation-circle\" style=\"color: #faad14;\" />\n                <strong>温馨提示：</strong>只支持 .zip 格式，文件大小不超过 5MB\n              </div>\n            </a-form-model-item>\n          </a-form-model>\n\n          <!-- 新增下一个工作流按钮 -->\n          <div class=\"add-next-workflow\" v-show=\"currentStep === 1\">\n            <a-button type=\"dashed\" block @click=\"addNextWorkflow\" class=\"add-next-btn\">\n              <a-icon type=\"plus\" />\n              新增下一个工作流\n            </a-button>\n          </div>\n        </div>\n\n        <!-- 工作流列表 -->\n        <div class=\"temp-workflows\" v-if=\"tempWorkflowList.length > 0\">\n          <a-divider>工作流列表 ({{ tempWorkflowList.length }})</a-divider>\n          <div class=\"workflow-list\">\n            <div\n              v-for=\"(workflow, index) in tempWorkflowList\"\n              :key=\"workflow.id\"\n              class=\"workflow-item\"\n              :class=\"{\n                'editing': currentWorkflowIndex === index,\n                'has-error': workflowValidationErrors[workflow.id] && !workflowValidationErrors[workflow.id].isValid\n              }\"\n            >\n              <div class=\"workflow-info\">\n                <h4 class=\"workflow-name\">\n                  {{ workflow.workflowName }}\n                  <!-- 状态标签 -->\n                  <a-tag v-if=\"workflow.status === 'saved'\" color=\"green\" size=\"small\">已保存</a-tag>\n                  <a-tag v-else-if=\"workflow.status === 'draft'\" color=\"orange\" size=\"small\">新增</a-tag>\n                  <a-tag v-if=\"currentWorkflowIndex === index\" color=\"blue\" size=\"small\">编辑中</a-tag>\n                  <a-tag v-if=\"workflowValidationErrors[workflow.id] && !workflowValidationErrors[workflow.id].isValid\" color=\"red\" size=\"small\">\n                    <a-icon type=\"exclamation-circle\" />\n                    有错误\n                  </a-tag>\n                </h4>\n                <p class=\"workflow-desc\">{{ workflow.workflowDescription || '暂无描述' }}</p>\n\n                <!-- 🔥 验证错误提示 -->\n                <div v-if=\"workflowValidationErrors[workflow.id] && !workflowValidationErrors[workflow.id].isValid\" class=\"workflow-errors\">\n                  <a-alert\n                    type=\"error\"\n                    size=\"small\"\n                    show-icon\n                    :message=\"`请补充：${workflowValidationErrors[workflow.id].errors.join('、')}`\"\n                  />\n                </div>\n\n              </div>\n              <div class=\"workflow-actions\">\n                <a-button size=\"small\" @click=\"loadWorkflowFromTemp(index)\" :disabled=\"currentWorkflowIndex === index\">\n                  <a-icon type=\"edit\" />\n                  编辑\n                </a-button>\n                <a-button size=\"small\" type=\"danger\" @click=\"deleteWorkflowFromTemp(index)\">\n                  <a-icon type=\"delete\" />\n                  删除\n                </a-button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- 第二步底部按钮 -->\n      <div class=\"step-footer\">\n        <a-button @click=\"handlePrev\" :disabled=\"stepLoading\">\n          <a-icon type=\"arrow-left\" />\n          上一步\n        </a-button>\n        <a-button type=\"primary\" @click=\"handleComplete\" :loading=\"stepLoading\" class=\"complete-btn\">\n          完成创建\n          <a-icon type=\"check\" />\n        </a-button>\n      </div>\n    </div>\n\n    <!-- 第三步：创建完成 -->\n    <div v-if=\"currentStep === 2\" class=\"step-content\">\n      <div class=\"step-header\">\n        <h3>\n          <a-icon type=\"check-circle\" style=\"color: #52c41a; margin-right: 8px;\" />\n          创建完成\n        </h3>\n        <p>智能体创建流程已完成</p>\n      </div>\n\n      <div class=\"success-content\">\n        <div class=\"success-icon\">\n          <a-icon type=\"check-circle\" style=\"font-size: 64px; color: #52c41a;\" />\n        </div>\n        <div class=\"success-message\">\n          <h2>恭喜您，已成功提交智能体，请耐心等待审核！</h2>\n          <p>您的智能体信息已提交至平台，我们将在1-3个工作日内完成审核。</p>\n          <p>审核结果将通过站内消息通知您，请注意查收。</p>\n        </div>\n      </div>\n\n      <!-- 第三步底部按钮 -->\n      <div class=\"step-footer\">\n        <a-button type=\"primary\" @click=\"handleCloseModal\" size=\"large\">\n          关闭\n        </a-button>\n      </div>\n    </div>\n  </div>\n</a-modal>\n", null]}