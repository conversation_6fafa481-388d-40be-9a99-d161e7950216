{"openapi": "3.0.0", "info": {"title": "剪映小助手_智界工具箱 - 保存草稿", "description": "保存草稿", "version": "1.0.0"}, "servers": [{"url": "https://www.aigcview.com"}], "paths": {"/jeecg-boot/api/jianying/save_draft": {"post": {"summary": "保存草稿", "description": "保存草稿", "operationId": "save_draft", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"access_key": {"type": "string", "description": "访问密钥（必填）", "example": "JianyingAPI_2025_SecureAccess_AigcView", "default": "JianyingAPI_2025_SecureAccess_AigcView"}, "user_id": {"type": "integer", "description": "用户ID，如果填写了这个ID，新用户产生的月费就会按照比例归属到这个账号下", "example": 12345}, "draft_url": {"type": "string", "description": "草稿地址，使用create_draft输出的draft_url即可（必填）", "example": "https://example.com/draft/123"}}, "required": ["access_key", "draft_url"], "additionalProperties": false}}}}, "responses": {"200": {"description": "成功保存草稿", "content": {"application/json": {"schema": {"type": "object", "properties": {"draft_url": {"type": "string", "description": "草稿地址（与输入的draft_url相同）", "example": "https://aigcview-plub.tos-cn-guangzhou.volces.com/jianying-assistant/drafts/2025/07/10/zj_draft_20250710_032651_869701b4.json"}, "message": {"type": "string", "description": "导入指导信息", "example": "不知道导入的请看：https://www.aigcview.com/JianYingDraft?draft=https://aigcview-plub.tos-cn-guangzhou.volces.com/jianying-assistant/drafts/2025/07/10/zj_draft_20250710_032651_869701b4.json"}}, "required": ["draft_url", "message"]}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "description": "错误信息", "example": "草稿地址不能为空"}}, "required": ["error"]}}}}, "500": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "description": "错误信息", "example": "保存草稿失败: 网络连接超时"}}, "required": ["error"]}}}}}}}}}