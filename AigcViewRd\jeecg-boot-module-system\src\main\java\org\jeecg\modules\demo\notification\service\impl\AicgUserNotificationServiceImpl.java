package org.jeecg.modules.demo.notification.service.impl;

import java.util.Date;
import java.util.List;
import java.util.Map;

import org.jeecg.modules.demo.notification.entity.AicgUserNotification;
import org.jeecg.modules.demo.notification.mapper.AicgUserNotificationMapper;
import org.jeecg.modules.demo.notification.service.IAicgUserNotificationService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: 用户通知消息表
 * @Author: jeecg-boot
 * @Date:   2025-06-18
 * @Version: V1.0
 */
@Service
public class AicgUserNotificationServiceImpl extends ServiceImpl<AicgUserNotificationMapper, AicgUserNotification> implements IAicgUserNotificationService {

    @Override
    public List<AicgUserNotification> getByUserId(String userId) {
        return baseMapper.getByUserId(userId);
    }

    @Override
    public List<AicgUserNotification> getUnreadByUserId(String userId) {
        return baseMapper.getUnreadByUserId(userId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AicgUserNotification createNotification(String userId, String title, String content, Integer type,
                                                  Integer priority, String relatedId, String relatedType,
                                                  String actionUrl, Date expireTime, String operatorId) {
        AicgUserNotification notification = new AicgUserNotification();
        notification.setUserId(userId);
        notification.setTitle(title);
        notification.setContent(content);
        notification.setType(type);
        notification.setPriority(priority != null ? priority : 1);
        notification.setIsRead(0); // 未读
        notification.setRelatedId(relatedId);
        notification.setRelatedType(relatedType);
        notification.setActionUrl(actionUrl);
        notification.setExpireTime(expireTime);
        notification.setCreateBy(operatorId);
        notification.setCreateTime(new Date());
        
        this.save(notification);
        return notification;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean markAsRead(String id, String userId, String operatorId) {
        AicgUserNotification notification = this.getById(id);
        if (notification != null && notification.getUserId().equals(userId) && notification.getIsRead() == 0) {
            notification.setIsRead(1);
            notification.setReadTime(new Date());
            notification.setUpdateBy(operatorId);
            notification.setUpdateTime(new Date());
            return this.updateById(notification);
        }
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean markAsUnread(String id, String userId, String operatorId) {
        AicgUserNotification notification = this.getById(id);
        if (notification != null && notification.getUserId().equals(userId) && notification.getIsRead() == 1) {
            notification.setIsRead(0);
            notification.setReadTime(null);
            notification.setUpdateBy(operatorId);
            notification.setUpdateTime(new Date());
            return this.updateById(notification);
        }
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchMarkAsRead(String userId, List<String> ids, String operatorId) {
        return baseMapper.batchMarkAsRead(userId, ids);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int markAllAsRead(String userId, String operatorId) {
        return baseMapper.markAllAsRead(userId);
    }

    @Override
    public Map<String, Object> getNotificationStats(String userId) {
        return baseMapper.getNotificationStats(userId);
    }

    @Override
    public List<AicgUserNotification> getByUserIdAndType(String userId, Integer type) {
        return baseMapper.getByUserIdAndType(userId, type);
    }

    @Override
    public List<AicgUserNotification> getExpiringNotifications() {
        return baseMapper.getExpiringNotifications();
    }

    @Override
    public List<AicgUserNotification> getExpiredNotifications() {
        return baseMapper.getExpiredNotifications();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteExpiredNotifications() {
        return baseMapper.deleteExpiredNotifications();
    }

    @Override
    public AicgUserNotification sendSystemNotification(String userId, String title, String content, String operatorId) {
        return createNotification(userId, title, content, 1, 1, null, null, null, null, operatorId);
    }

    @Override
    public AicgUserNotification sendTransactionNotification(String userId, String title, String content, 
                                                           String transactionId, String operatorId) {
        return createNotification(userId, title, content, 2, 2, transactionId, "transaction", null, null, operatorId);
    }

    @Override
    public AicgUserNotification sendSecurityAlert(String userId, String title, String content, String operatorId) {
        return createNotification(userId, title, content, 3, 3, null, null, null, null, operatorId);
    }

    @Override
    public AicgUserNotification sendMarketingNotification(String userId, String title, String content, 
                                                         String actionUrl, Date expireTime, String operatorId) {
        return createNotification(userId, title, content, 4, 1, null, null, actionUrl, expireTime, operatorId);
    }
}
