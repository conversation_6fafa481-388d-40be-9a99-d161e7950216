/** init domain config */
import './config'

import Vue from 'vue'
import App from './App.vue'
import Storage from 'vue-ls'
import router from './router'
import store from './store/'
import { VueAxios } from "@/utils/request"

require('@jeecg/antd-online-mini')
require('@jeecg/antd-online-mini/dist/OnlineForm.css')

import Antd, { version } from 'ant-design-vue'
console.log('ant-design-vue version:', version)

import Viser from 'viser-vue'
import 'ant-design-vue/dist/antd.less';  // or 'ant-design-vue/dist/antd.less'

import '@/permission' // permission control
import '@/utils/filter' // base filter
import Print from 'vue-print-nb-jeecg'
import preview from 'vue-photo-preview'
import 'vue-photo-preview/dist/skin.css'
import SSO from '@/cas/sso.js'
import { install as categoryServiceInstall } from '@/utils/categoryService'
import {
  ACCESS_TOKEN,
  DEFAULT_COLOR,
  DEFAULT_THEME,
  DEFAULT_LAYOUT_MODE,
  DEFAULT_COLOR_WEAK,
  SIDEBAR_TYPE,
  DEFAULT_FIXED_HEADER,
  DEFAULT_FIXED_HEADER_HIDDEN,
  DEFAULT_FIXED_SIDEMENU,
  DEFAULT_CONTENT_WIDTH_TYPE,
  DEFAULT_MULTI_PAGE
} from "@/store/mutation-types"
import config from '@/defaultSettings'

import JDictSelectTag from './components/dict/index.js'
import hasPermission from '@/utils/hasPermission'
import vueBus from '@/utils/vueBus';
import JeecgComponents from '@/components/jeecg/index'
import { clearRoleInfo } from '@/utils/roleUtils'
import '@/assets/less/JAreaLinkage.less'
import '@/assets/less/common.less'
import VueAreaLinkage from 'vue-area-linkage'
import '@/components/jeecg/JVxeTable/install'
import '@/components/JVxeCells/install'
//表单验证
import { rules } from '@/utils/rules'
// 全局错误处理
import ErrorHandler from '@/plugins/errorHandler'
Vue.prototype.rules = rules
Vue.config.productionTip = false
Vue.use(Storage, config.storageOptions)
Vue.use(Antd)
Vue.use(VueAxios, router)
Vue.use(Viser)
Vue.use(hasPermission)
Vue.use(JDictSelectTag)
Vue.use(Print)
Vue.use(preview)
Vue.use(vueBus);
Vue.use(JeecgComponents);
Vue.use(VueAreaLinkage);
Vue.use(ErrorHandler);
Vue.use(categoryServiceInstall);

// 🆕 创建全局文件访问方法（优先CDN）
window.getFileAccessHttpUrl = function(filePath) {
  if (!filePath) return '';

  // 如果已经是完整URL，直接返回
  if (filePath.startsWith('http://') || filePath.startsWith('https://')) {
    return filePath;
  }

  // 🆕 TOS文件访问策略（双桶策略）
  // 🌐 通用桶文件（uploads/、electron/、defaults/）：强制CDN访问
  if (filePath.startsWith('uploads/') || filePath.startsWith('electron/') || filePath.startsWith('defaults/')) {
    if (window._CONFIG && window._CONFIG['enableCDN'] && window._CONFIG['cdnDomainURL']) {
      const cdnUrl = window._CONFIG['cdnDomainURL'] + '/' + filePath;
      console.debug('通用桶文件使用CDN访问:', filePath, '->', cdnUrl);
      return cdnUrl;
    }
  }

  // 🔒 剪映草稿文件：通过后端内网TOS处理（免费流量）
  if (filePath.startsWith('jianying-assistant/')) {
    console.debug('剪映文件通过后端内网TOS处理:', filePath);
    // 剪映文件不使用CDN，让后端使用内网TOS
    return (window._CONFIG && window._CONFIG['staticDomainURL']) ?
           window._CONFIG['staticDomainURL'] + '/' + filePath :
           '/jeecg-boot/sys/common/static/' + filePath;
  }

    // 🔄 降级方案：通过后端API获取
    const cacheKey = 'tos_url_' + filePath;
    const cached = sessionStorage.getItem(cacheKey);

    if (cached) {
      const cacheData = JSON.parse(cached);
      if (Date.now() - cacheData.timestamp < 10 * 60 * 1000) {
        return cacheData.url;
      }
    }

    try {
      const xhr = new XMLHttpRequest();
      xhr.open('GET', Vue.prototype.API_BASE_URL + '/sys/common/tos-file-url?filePath=' + encodeURIComponent(filePath), false);
      xhr.setRequestHeader('X-Access-Token', Vue.ls.get('Access-Token'));
      xhr.send();

      if (xhr.status === 200) {
        const response = JSON.parse(xhr.responseText);
        if (response.success && response.result) {
          sessionStorage.setItem(cacheKey, JSON.stringify({
            url: response.result,
            timestamp: Date.now()
          }));
          return response.result;
        }
      }
    } catch (error) {
      console.warn('获取文件URL失败:', error);
    }

    // 🔄 最终降级：使用静态域名
    return (window._CONFIG && window._CONFIG['staticDomainURL']) ?
           window._CONFIG['staticDomainURL'] + '/' + filePath :
           Vue.prototype.API_BASE_URL.replace('/jeecg-boot', '') + '/' + filePath;
};

SSO.init(() => {
  main()
})
function main() {
  // 临时注释掉缓存清除，用于调试
  // clearRoleInfo()

  new Vue({
    router,
    store,
    mounted () {
      store.commit('SET_SIDEBAR_TYPE', Vue.ls.get(SIDEBAR_TYPE, true))
      store.commit('TOGGLE_THEME', Vue.ls.get(DEFAULT_THEME, config.navTheme))
      store.commit('TOGGLE_LAYOUT_MODE', Vue.ls.get(DEFAULT_LAYOUT_MODE, config.layout))
      store.commit('TOGGLE_FIXED_HEADER', Vue.ls.get(DEFAULT_FIXED_HEADER, config.fixedHeader))
      store.commit('TOGGLE_FIXED_SIDERBAR', Vue.ls.get(DEFAULT_FIXED_SIDEMENU, config.fixSiderbar))
      store.commit('TOGGLE_CONTENT_WIDTH', Vue.ls.get(DEFAULT_CONTENT_WIDTH_TYPE, config.contentWidth))
      store.commit('TOGGLE_FIXED_HEADER_HIDDEN', Vue.ls.get(DEFAULT_FIXED_HEADER_HIDDEN, config.autoHideHeader))
      store.commit('TOGGLE_WEAK', Vue.ls.get(DEFAULT_COLOR_WEAK, config.colorWeak))
      store.commit('TOGGLE_COLOR', Vue.ls.get(DEFAULT_COLOR, config.primaryColor))
      store.commit('SET_TOKEN', Vue.ls.get(ACCESS_TOKEN))
      store.commit('SET_MULTI_PAGE',Vue.ls.get(DEFAULT_MULTI_PAGE,config.multipage))
    },
    render: h => h(App)
  }).$mount('#app')
}