{"openapi": "3.0.0", "info": {"title": "剪映小助手_超级剪映小助手 - 批量添加音频", "description": "批量添加音频", "version": "1.0.0"}, "servers": [{"url": "https://www.aigcview.com"}], "paths": {"/jeecg-boot/api/jianyingpro/add_audios": {"post": {"summary": "批量添加音频", "description": "批量添加音频", "operationId": "add_audios_pro", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"access_key": {"type": "string", "description": "访问密钥（必填）", "example": "JianyingAPI_2025_SecureAccess_AigcView", "default": "JianyingAPI_2025_SecureAccess_AigcView"}, "draft_url": {"type": "string", "description": "草稿地址，使用create_draft输出的draft_url即可（必填）", "example": "https://example.com/draft/123"}, "mp3_urls": {"type": "array", "items": {"type": "string"}, "description": "音频列表（必填）", "example": ["https://example.com/audio1.mp3", "https://example.com/audio2.mp3"]}, "timelines": {"type": "array", "items": {"type": "object", "properties": {"start": {"type": "integer"}, "end": {"type": "integer"}}}, "description": "时间线（必填）", "example": [{"start": 0, "end": 4612000}]}, "audio_effect": {"type": "string", "description": "特效音，eg：教堂，默认无", "example": "教堂"}, "volume": {"type": "number", "description": "音量大小，0-10，默认1", "minimum": 0, "maximum": 10, "example": 1.0}}, "required": ["access_key", "draft_url", "mp3_urls", "timelines"], "additionalProperties": false}}}}, "responses": {"200": {"description": "成功添加音频", "content": {"application/json": {"schema": {"type": "object", "properties": {"track_id": {"type": "string", "description": "音频轨道ID"}, "draft_url": {"type": "string", "description": "更新后的草稿地址"}, "audio_ids": {"type": "array", "description": "音频材料ID列表", "items": {"type": "string"}}, "segment_ids": {"type": "array", "description": "音频段ID列表", "items": {"type": "string"}}, "message": {"type": "string", "description": "导入指南信息", "example": "不知道导入的请看：https://www.aigcview.com/JianYingDraft?draft=https://example.com/draft.json"}}, "required": ["track_id", "draft_url", "audio_ids", "segment_ids", "message"]}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "description": "错误信息", "example": "参数不完整: 草稿地址不能为空"}, "error_code": {"type": "string", "description": "错误码", "example": "PARAM_INCOMPLETE_003"}, "error_message": {"type": "string", "description": "详细错误消息", "example": "参数不完整"}, "error_details": {"type": "string", "description": "错误解决方案", "example": "请提供有效的draft_url参数"}}, "required": ["error", "error_code"]}}}}, "500": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "description": "错误信息", "example": "批量添加音频失败: 服务器内部错误"}}, "required": ["error"]}}}}}}}}}