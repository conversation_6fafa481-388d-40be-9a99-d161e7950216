// 测试多链接解析功能
const testUrls = `https://aigcview-plub.tos-cn-guangzhou.volces.com/jianying-assistant/drafts/2025/07/10/zj_draft_20250710_150327_3f7d6196.json
https://aigcview-plub.tos-cn-guangzhou.volces.com/jianying-assistant/drafts/2025/07/10/zj_draft_20250710_150722_6c0c5c9a.json`;

console.log('测试输入:');
console.log(testUrls);
console.log('\n按行分割:');

const lines = testUrls.split('\n');
console.log('行数:', lines.length);

lines.forEach((line, index) => {
    const trimmed = line.trim();
    console.log(`第 ${index + 1} 行: "${trimmed}"`);
    console.log(`长度: ${trimmed.length}`);
    console.log(`包含volces.com: ${trimmed.includes('volces.com')}`);
    console.log(`包含jianying-assistant: ${trimmed.includes('jianying-assistant')}`);
    console.log(`以.json结尾: ${trimmed.endsWith('.json')}`);
    console.log('---');
});

// 测试正则表达式
const tosPattern = /^https:\/\/.*\.volces\.com\/+jianying-assistant\/drafts\/.+\.(json|zip)$/;
console.log('\n正则表达式测试:');
lines.forEach((line, index) => {
    const trimmed = line.trim();
    if (trimmed) {
        const result = tosPattern.test(trimmed);
        console.log(`第 ${index + 1} 行验证结果: ${result}`);
    }
});
