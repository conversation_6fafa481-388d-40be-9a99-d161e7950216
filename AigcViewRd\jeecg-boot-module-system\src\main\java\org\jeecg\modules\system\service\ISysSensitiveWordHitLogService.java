package org.jeecg.modules.system.service;

import java.util.List;
import java.util.Map;

import org.jeecg.modules.system.entity.SysSensitiveWordHitLog;

import com.baomidou.mybatisplus.extension.service.IService;

/**
 * @Description: 敏感词命中记录
 * @Author: jeecg-boot
 * @Date: 2025-06-22
 * @Version: V1.0
 */
public interface ISysSensitiveWordHitLogService extends IService<SysSensitiveWordHitLog> {

    /**
     * 获取敏感词命中趋势（按天统计）
     * @param days 统计天数
     * @return 趋势数据
     */
    List<Map<String, Object>> getHitTrend(int days);

    /**
     * 获取用户敏感词触发统计
     * @param userId 用户ID（可为空，查询所有用户）
     * @param days 统计天数
     * @return 用户统计
     */
    List<Map<String, Object>> getUserHitStatistics(String userId, int days);

    /**
     * 获取模块敏感词触发统计
     * @param days 统计天数
     * @return 模块统计
     */
    List<Map<String, Object>> getModuleHitStatistics(int days);

    /**
     * 获取IP敏感词触发统计
     * @param days 统计天数
     * @return IP统计
     */
    List<Map<String, Object>> getIpHitStatistics(int days);

    /**
     * 清理过期的命中记录
     * @param days 保留天数
     * @return 清理数量
     */
    int cleanExpiredLogs(int days);

    /**
     * 获取综合统计报告
     * @param days 统计天数
     * @return 统计报告
     */
    Map<String, Object> getComprehensiveReport(int days);
}
