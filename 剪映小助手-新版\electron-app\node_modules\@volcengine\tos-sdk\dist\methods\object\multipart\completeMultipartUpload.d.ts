import TOSBase from '../../base';
export interface CompleteMultipartUploadInput {
    bucket?: string;
    key: string;
    uploadId: string;
    parts: {
        eTag: string;
        partNumber: number;
    }[];
    /**
     * when true `parts` param need to be empty array
     */
    completeAll?: boolean;
    callback?: string;
    callbackVar?: string;
    forbidOverwrite?: boolean;
    headers?: {
        ['x-tos-forbid-overwrite']?: string;
    };
}
export declare type UploadedPart = {
    PartNumber: number;
    ETag: string;
};
export interface CompleteMultipartUploadOutput {
    Bucket: string;
    Key: string;
    ETag: string;
    Location: string;
    VersionID?: string;
    HashCrc64ecma?: string;
    /** the field has a value when completeAll is true
     * when specify callback, the field will not has a value
     */
    CompletedParts?: UploadedPart[];
    CallbackResult?: string;
}
export declare function completeMultipartUpload(this: TOSBase, input: CompleteMultipartUploadInput): Promise<import("../../base").TosResponse<CompleteMultipartUploadOutput>>;
