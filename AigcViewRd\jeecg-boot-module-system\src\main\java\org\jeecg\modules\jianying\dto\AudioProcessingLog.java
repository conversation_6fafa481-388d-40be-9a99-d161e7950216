package org.jeecg.modules.jianying.dto;

import lombok.Data;

/**
 * 音频处理日志类
 * 用于记录音频处理过程中的详细日志信息
 * 
 * <AUTHOR>
 * @date 2025-07-08
 */
@Data
public class AudioProcessingLog {
    
    /** 时间戳 */
    private String timestamp;
    
    /** 日志级别：INFO, WARN, ERROR */
    private String level;
    
    /** 日志消息 */
    private String message;
    
    /** 音频文件URL（可选） */
    private String audioUrl;
    
    /** 音频文件索引（可选） */
    private Integer index;
    
    /**
     * 创建完整的处理日志
     */
    public AudioProcessingLog(String level, String message, String audioUrl, Integer index) {
        this.timestamp = java.time.LocalDateTime.now().format(
            java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS"));
        this.level = level;
        this.message = message;
        this.audioUrl = audioUrl;
        this.index = index;
    }
    
    /**
     * 创建简单的处理日志（无音频文件信息）
     */
    public AudioProcessingLog(String level, String message) {
        this(level, message, null, null);
    }
    
    /**
     * 创建INFO级别日志
     */
    public static AudioProcessingLog info(String message) {
        return new AudioProcessingLog("INFO", message);
    }
    
    /**
     * 创建INFO级别日志（带音频文件信息）
     */
    public static AudioProcessingLog info(String message, String audioUrl, Integer index) {
        return new AudioProcessingLog("INFO", message, audioUrl, index);
    }
    
    /**
     * 创建WARN级别日志
     */
    public static AudioProcessingLog warn(String message) {
        return new AudioProcessingLog("WARN", message);
    }
    
    /**
     * 创建WARN级别日志（带音频文件信息）
     */
    public static AudioProcessingLog warn(String message, String audioUrl, Integer index) {
        return new AudioProcessingLog("WARN", message, audioUrl, index);
    }
    
    /**
     * 创建ERROR级别日志
     */
    public static AudioProcessingLog error(String message) {
        return new AudioProcessingLog("ERROR", message);
    }
    
    /**
     * 创建ERROR级别日志（带音频文件信息）
     */
    public static AudioProcessingLog error(String message, String audioUrl, Integer index) {
        return new AudioProcessingLog("ERROR", message, audioUrl, index);
    }
}
