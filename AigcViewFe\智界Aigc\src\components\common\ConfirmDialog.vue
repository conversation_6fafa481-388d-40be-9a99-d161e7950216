<template>
  <!-- Portal方式实现，不在模板中渲染任何内容 -->
  <div></div>
</template>

<script>
export default {
  name: 'ConfirmDialog',
  props: {
    // 控制显示隐藏
    visible: {
      type: Boolean,
      default: false
    },
    // 对话框标题
    title: {
      type: String,
      default: '确认操作'
    },
    // 内容文本（支持HTML）
    content: {
      type: String,
      default: ''
    },
    // 对话框类型：warning, danger, info, success
    type: {
      type: String,
      default: 'warning',
      validator: value => ['warning', 'danger', 'info', 'success'].includes(value)
    },
    // 是否显示取消按钮
    showCancel: {
      type: Boolean,
      default: true
    },
    // 取消按钮文本
    cancelText: {
      type: String,
      default: '取消'
    },
    // 确认按钮文本
    confirmText: {
      type: String,
      default: '确定'
    },
    // 加载状态
    loading: {
      type: Boolean,
      default: false
    },
    // 加载时的文本
    loadingText: {
      type: String,
      default: '处理中...'
    },
    // 点击遮罩层是否关闭
    closeOnClickOverlay: {
      type: Boolean,
      default: true
    }
  },
  computed: {
    iconClass() {
      return {
        'icon-warning': this.type === 'warning',
        'icon-danger': this.type === 'danger',
        'icon-info': this.type === 'info',
        'icon-success': this.type === 'success'
      }
    },
    iconType() {
      const iconMap = {
        warning: 'anticon anticon-exclamation-circle',
        danger: 'anticon anticon-close-circle',
        info: 'anticon anticon-info-circle',
        success: 'anticon anticon-check-circle'
      }
      return iconMap[this.type]
    },
    warningIconType() {
      const iconMap = {
        warning: 'anticon anticon-warning',
        danger: 'anticon anticon-close-circle',
        info: 'anticon anticon-info-circle',
        success: 'anticon anticon-check-circle'
      }
      return iconMap[this.type]
    },
    contentClass() {
      return {
        'content-warning': this.type === 'warning',
        'content-danger': this.type === 'danger',
        'content-info': this.type === 'info',
        'content-success': this.type === 'success'
      }
    },
    confirmButtonClass() {
      return {
        'btn-warning': this.type === 'warning',
        'btn-danger': this.type === 'danger',
        'btn-info': this.type === 'info',
        'btn-success': this.type === 'success'
      }
    },
    confirmIconType() {
      const iconMap = {
        warning: 'anticon anticon-check',
        danger: 'anticon anticon-check',
        info: 'anticon anticon-check',
        success: 'anticon anticon-check'
      }
      return iconMap[this.type]
    }
  },
  data() {
    return {
      portalElement: null
    }
  },
  watch: {
    visible: {
      handler(newVal) {
        if (newVal) {
          this.createPortal()
        } else {
          this.removePortal()
        }
      },
      immediate: true
    }
  },
  beforeDestroy() {
    // 组件销毁时清理Portal
    this.removePortal()
  },
  computed: {
    iconClass() {
      const classMap = {
        warning: 'icon-warning',
        danger: 'icon-danger',
        info: 'icon-info',
        success: 'icon-success'
      }
      return classMap[this.type] || 'icon-warning'
    },
    contentClass() {
      const classMap = {
        warning: 'content-warning',
        danger: 'content-danger',
        info: 'content-info',
        success: 'content-success'
      }
      return classMap[this.type] || 'content-warning'
    },
    confirmButtonClass() {
      const classMap = {
        warning: 'btn-warning',
        danger: 'btn-danger',
        info: 'btn-info',
        success: 'btn-success'
      }
      return classMap[this.type] || 'btn-warning'
    }
  },
  methods: {
    createPortal() {
      // 先清理已存在的Portal
      this.removePortal()

      if (this.portalElement) {
        return
      }

      // 保存当前滚动位置
      this.savedScrollY = window.pageYOffset || document.documentElement.scrollTop

      // 禁止页面滚动
      document.body.style.overflow = 'hidden'
      document.documentElement.style.overflow = 'hidden'

      // 创建Portal元素
      this.portalElement = document.createElement('div')
      this.portalElement.style.cssText = `
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        right: 0 !important;
        bottom: 0 !important;
        width: 100vw !important;
        height: 100vh !important;
        background: rgba(0, 0, 0, 0.85) !important;
        backdrop-filter: blur(20px) !important;
        -webkit-backdrop-filter: blur(20px) !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        z-index: 1000 !important;
        margin: 0 !important;
        padding: 0 !important;
        overflow: hidden !important;
        pointer-events: all !important;
      `

      // 创建对话框内容
      this.portalElement.innerHTML = this.generateDialogHTML()

      // 添加事件监听
      this.portalElement.addEventListener('click', this.handleOverlayClick)

      const cancelBtn = this.portalElement.querySelector('.cancel-btn')
      const confirmBtn = this.portalElement.querySelector('.confirm-btn')
      const dialog = this.portalElement.querySelector('.confirm-dialog')

      if (cancelBtn) {
        cancelBtn.addEventListener('click', this.handleCancel)
      }
      if (confirmBtn) {
        confirmBtn.addEventListener('click', this.handleConfirm)
      }
      if (dialog) {
        dialog.addEventListener('click', (e) => e.stopPropagation())
      }

      // 直接挂载到body
      document.body.appendChild(this.portalElement)
    },

    removePortal() {
      if (this.portalElement) {
        // 使用保存的滚动位置
        const scrollY = this.savedScrollY || 0
        console.log('🔍 ConfirmDialog关闭，恢复滚动位置到:', scrollY)

        // 恢复页面滚动
        document.body.style.overflow = ''
        document.documentElement.style.overflow = ''

        // 立即恢复滚动位置
        window.scrollTo(0, scrollY)

        // 使用多重保险确保滚动位置正确
        setTimeout(() => {
          window.scrollTo(0, scrollY)
          console.log('🔍 ConfirmDialog延迟恢复滚动位置到:', scrollY)
        }, 10)

        requestAnimationFrame(() => {
          window.scrollTo(0, scrollY)
        })

        // 移除事件监听
        this.portalElement.removeEventListener('click', this.handleOverlayClick)

        // 从DOM中移除
        if (this.portalElement.parentNode) {
          this.portalElement.parentNode.removeChild(this.portalElement)
        }

        this.portalElement = null
        this.savedScrollY = null
      }
    },

    generateDialogHTML() {
      return `
        <div class="confirm-dialog-portal" style="
          background: rgba(255, 255, 255, 0.98) !important;
          border-radius: 24px !important;
          padding: 4rem !important;
          margin: 1rem !important;
          max-width: 700px !important;
          width: 98% !important;
          max-height: 90vh !important;
          overflow-y: auto !important;
          box-shadow: 0 25px 80px rgba(0, 0, 0, 0.4), 0 0 0 1px rgba(255, 255, 255, 0.2) !important;
          backdrop-filter: blur(20px) !important;
          -webkit-backdrop-filter: blur(20px) !important;
          border: 1px solid rgba(124, 138, 237, 0.15) !important;
          z-index: 1000 !important;
          position: relative !important;
        " onclick="event.stopPropagation()">
          <div style="display: flex; align-items: center; gap: 1rem; margin-bottom: 1.5rem;">
            <div style="
              width: 48px; height: 48px; border-radius: 50%; display: flex;
              align-items: center; justify-content: center; color: white; font-size: 24px;
              background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
            ">
              <i class="${this.iconType}"></i>
            </div>
            <h3 style="font-size: 1.5rem; font-weight: 600; color: #334155; margin: 0;">
              ${this.title}
            </h3>
          </div>

          <div style="margin-bottom: 2rem;">
            <div style="
              display: flex; gap: 1rem; padding: 1.5rem; border-radius: 16px; border: 1px solid;
              background: linear-gradient(135deg, rgba(245, 158, 11, 0.1) 0%, rgba(217, 119, 6, 0.05) 100%);
              border-color: rgba(245, 158, 11, 0.2);
            ">
              <div style="flex-shrink: 0; width: 24px; height: 24px; font-size: 20px; color: #f59e0b;">
                <i class="${this.warningIconType}"></i>
              </div>
              <div style="flex: 1; color: #64748b; line-height: 1.6;">
                <p style="margin: 0 0 0.75rem 0;"><strong style="color: #334155; font-weight: 600;">API Key是您访问系统的唯一凭证，具有唯一性，请妥善保管。</strong></p>
                <p style="margin: 0 0 0.75rem 0;">重置后原有的API Key将立即失效，所有使用旧API Key的应用将无法正常工作。</p>
                <p style="color: #f59e0b; font-weight: 600; margin: 1rem 0 0 0;">确定要重置吗？</p>
              </div>
            </div>
          </div>

          <div style="display: flex; gap: 1rem; justify-content: flex-end;">
            ${this.showCancel ? `
              <button class="cancel-btn" style="
                display: flex; align-items: center; gap: 0.5rem; padding: 0.875rem 1.5rem;
                border-radius: 12px; font-weight: 500; cursor: pointer; transition: all 0.3s ease;
                border: 1px solid rgba(148, 163, 184, 0.2); font-size: 14px; min-width: 120px;
                justify-content: center; background: rgba(148, 163, 184, 0.1); color: #64748b;
              ">
                <i class="anticon anticon-close"></i>
                <span>${this.cancelText}</span>
              </button>
            ` : ''}
            <button class="confirm-btn" style="
              display: flex; align-items: center; gap: 0.5rem; padding: 0.875rem 1.5rem;
              border-radius: 12px; font-weight: 500; cursor: pointer; transition: all 0.3s ease;
              border: none; font-size: 14px; min-width: 120px; justify-content: center;
              background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%); color: white;
              box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
            " ${this.loading ? 'disabled' : ''}>
              <i class="anticon ${this.loading ? 'anticon-loading spinning' : this.confirmIconType}"></i>
              <span>${this.loading ? this.loadingText : this.confirmText}</span>
            </button>
          </div>
        </div>
      `
    },

    handleOverlayClick(event) {
      if (!this.closeOnClickOverlay) {
        return
      }

      // 获取对话框元素
      const dialogElement = this.portalElement.querySelector('.confirm-dialog-portal')

      if (!dialogElement) {
        this.handleCancel()
        return
      }

      // 获取对话框的边界
      const dialogRect = dialogElement.getBoundingClientRect()

      // 定义安全区域（对话框周围50px的缓冲区）
      const safeZone = 50
      const safeRect = {
        left: dialogRect.left - safeZone,
        right: dialogRect.right + safeZone,
        top: dialogRect.top - safeZone,
        bottom: dialogRect.bottom + safeZone
      }

      // 检查点击位置是否在安全区域外
      const clickX = event.clientX
      const clickY = event.clientY

      const isOutsideSafeZone = (
        clickX < safeRect.left ||
        clickX > safeRect.right ||
        clickY < safeRect.top ||
        clickY > safeRect.bottom
      )

      // 只有点击在安全区域外才关闭弹窗
      if (isOutsideSafeZone) {
        this.handleCancel()
      }
    },
    handleCancel() {
      this.$emit('cancel')
      this.$emit('update:visible', false)
    },
    handleConfirm() {
      this.$emit('confirm')
    }
  }
}
</script>

<style scoped>
/* 确认对话框样式 - 真正的全页面遮罩 */
.confirm-overlay-portal {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  z-index: 9999999 !important;
  pointer-events: none !important;
}

.confirm-overlay {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  background: rgba(0, 0, 0, 0.85) !important;
  backdrop-filter: blur(20px) !important;
  -webkit-backdrop-filter: blur(20px) !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  z-index: 9999999 !important;
  animation: fadeIn 0.4s ease !important;
  overflow: hidden !important;
  pointer-events: all !important;
  margin: 0 !important;
  padding: 0 !important;
}

.confirm-dialog {
  background: rgba(255, 255, 255, 0.98) !important;
  border-radius: 24px !important;
  padding: 2.5rem !important;
  max-width: 520px !important;
  width: 90% !important;
  max-height: 80vh !important;
  overflow-y: auto !important;
  box-shadow:
    0 25px 80px rgba(0, 0, 0, 0.4),
    0 0 0 1px rgba(255, 255, 255, 0.2) !important;
  backdrop-filter: blur(20px) !important;
  -webkit-backdrop-filter: blur(20px) !important;
  border: 1px solid rgba(124, 138, 237, 0.15) !important;
  animation: slideUp 0.4s cubic-bezier(0.34, 1.56, 0.64, 1) !important;
  transform-origin: center center !important;
  z-index: 9999999 !important;
  position: relative !important;
}

.confirm-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.confirm-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 24px;
}

.confirm-icon.icon-warning {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

.confirm-icon.icon-danger {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
}

.confirm-icon.icon-info {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
}

.confirm-icon.icon-success {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.confirm-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #334155;
  margin: 0;
}

.confirm-content {
  margin-bottom: 2rem;
}

.warning-box {
  display: flex;
  gap: 1rem;
  padding: 1.5rem;
  border-radius: 16px;
  border: 1px solid;
}

.warning-box.content-warning {
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.1) 0%, rgba(217, 119, 6, 0.05) 100%);
  border-color: rgba(245, 158, 11, 0.2);
}

.warning-box.content-danger {
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.1) 0%, rgba(220, 38, 38, 0.05) 100%);
  border-color: rgba(239, 68, 68, 0.2);
}

.warning-box.content-info {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(29, 78, 216, 0.05) 100%);
  border-color: rgba(59, 130, 246, 0.2);
}

.warning-box.content-success {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.1) 0%, rgba(5, 150, 105, 0.05) 100%);
  border-color: rgba(16, 185, 129, 0.2);
}

.warning-icon {
  flex-shrink: 0;
  width: 24px;
  height: 24px;
  font-size: 20px;
}

.content-warning .warning-icon {
  color: #f59e0b;
}

.content-danger .warning-icon {
  color: #ef4444;
}

.content-info .warning-icon {
  color: #3b82f6;
}

.content-success .warning-icon {
  color: #10b981;
}

.warning-text {
  flex: 1;
  color: #64748b;
  line-height: 1.6;
}

.warning-text p {
  margin: 0 0 0.75rem 0;
}

.warning-text p:last-child {
  margin-bottom: 0;
}

.warning-text strong {
  color: #334155;
  font-weight: 600;
}

.confirm-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
}

.cancel-btn, .confirm-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.875rem 1.5rem;
  border-radius: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
  font-size: 14px;
  min-width: 120px;
  justify-content: center;
}

.cancel-btn {
  background: rgba(148, 163, 184, 0.1);
  color: #64748b;
  border: 1px solid rgba(148, 163, 184, 0.2);
}

.cancel-btn:hover {
  background: rgba(148, 163, 184, 0.15);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(148, 163, 184, 0.2);
}

.confirm-btn {
  color: white;
}

.confirm-btn.btn-warning {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
}

.confirm-btn.btn-danger {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
}

.confirm-btn.btn-info {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.confirm-btn.btn-success {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.confirm-btn:hover:not(:disabled) {
  transform: translateY(-2px);
}

.confirm-btn.btn-warning:hover:not(:disabled) {
  box-shadow: 0 6px 20px rgba(245, 158, 11, 0.4);
}

.confirm-btn.btn-danger:hover:not(:disabled) {
  box-shadow: 0 6px 20px rgba(239, 68, 68, 0.4);
}

.confirm-btn.btn-info:hover:not(:disabled) {
  box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
}

.confirm-btn.btn-success:hover:not(:disabled) {
  box-shadow: 0 6px 20px rgba(16, 185, 129, 0.4);
}

.confirm-btn:disabled {
  background: linear-gradient(135deg, #94a3b8 0%, #64748b 100%);
  cursor: not-allowed;
  transform: none;
  box-shadow: 0 2px 8px rgba(148, 163, 184, 0.2);
}

.spinning {
  animation: spin 1s linear infinite;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    backdrop-filter: blur(0px);
    -webkit-backdrop-filter: blur(0px);
  }
  to {
    opacity: 1;
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.9);
    filter: blur(4px);
  }
  50% {
    opacity: 0.8;
    transform: translateY(-5px) scale(1.02);
    filter: blur(0px);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
    filter: blur(0px);
  }
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .confirm-dialog {
    padding: 2rem;
    margin: 1rem;
  }

  .confirm-actions {
    flex-direction: column;
  }

  .cancel-btn, .confirm-btn {
    width: 100%;
  }
}
</style>
