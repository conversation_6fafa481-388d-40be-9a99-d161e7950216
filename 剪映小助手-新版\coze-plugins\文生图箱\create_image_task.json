{"openapi": "3.0.0", "info": {"title": "剪映小助手_文生图箱 - 创建图片生成任务", "description": "调用豆包文生图插件创建图片生成任务", "version": "1.0.0"}, "servers": [{"url": "https://www.aigcview.com"}], "paths": {"/jeecg-boot/api/coze/image/generate-task": {"post": {"summary": "创建图片生成任务", "description": "调用豆包文生图模型，同步生成高质量图片。", "operationId": "create_image_task", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"apiKey": {"type": "string", "description": "API令牌（必填），获取地址：https://www.aigcview.com/usercenter", "example": "your_api_token_here"}, "prompt": {"type": "string", "description": "图片描述提示词（必填），详细描述想要生成的图片内容、风格、场景等", "example": "一只可爱的橘猫坐在樱花树下，春天的阳光透过花瓣洒在猫咪身上，画面温馨唯美，高清摄影风格"}, "size": {"type": "string", "description": "图片尺寸，格式为'宽x高'，默认值：1024x1024，要求介于 [512 x 512, 2048 x 2048] 之间。推荐尺寸：1024x1024(正方形)、864x1152(竖屏3:4)、1152x864(横屏4:3)、1280x720(宽屏16:9)、720x1280(手机竖屏9:16)、832x1248(海报2:3)、1248x832(风景3:2)、1512x648(超宽屏21:9)，也可自定义其他尺寸", "example": "1024x1024", "default": "1024x1024"}, "seed": {"type": "integer", "description": "随机种子，控制图片生成的随机性。-1表示随机，固定值可复现相同效果，取值范围：[-1, 2147483647]", "example": -1, "default": -1, "minimum": -1, "maximum": 2147483647}, "guidance_scale": {"type": "number", "format": "float", "description": "引导强度，控制AI对提示词的遵循程度。值越高越严格按照提示词生成，取值范围：[1.0, 10.0]", "example": 2.5, "default": 2.5, "minimum": 1.0, "maximum": 10.0}, "watermark": {"type": "boolean", "description": "是否添加水印，默认false", "example": false, "default": false}}, "required": ["<PERSON><PERSON><PERSON><PERSON>", "prompt"], "additionalProperties": false}}}}, "responses": {"200": {"description": "成功生成图片", "content": {"application/json": {"schema": {"type": "object", "properties": {"image_url": {"type": "string", "description": "生成的图片下载链接", "example": "https://ark-content-generation-cn-beijing.tos-cn-beijing.volces.com/image.jpg"}, "status": {"type": "string", "description": "任务状态：completed(已完成)", "example": "completed", "enum": ["completed"]}, "message": {"type": "string", "description": "状态描述信息", "example": "图片生成成功"}, "deductedAmount": {"type": "number", "description": "实际扣费金额（元）", "example": 0.68}, "balanceAfter": {"type": "number", "description": "扣费后账户余额（元）", "example": 99.32}, "pricingDetails": {"type": "object", "description": "定价详情", "properties": {"model": {"type": "string", "description": "使用的模型", "example": "doubao-seedream-3-0-t2i-250415"}, "userLevel": {"type": "string", "description": "用户等级", "example": "普通用户"}, "finalPrice": {"type": "number", "description": "最终价格（元）", "example": 0.68}}}}, "required": ["image_url", "status", "message", "deductedAmount", "balanceAfter"]}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "description": "错误信息", "example": "创建图片生成任务失败: 提示词不能为空"}}, "required": ["error"]}}}}, "401": {"description": "API令牌无效", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "description": "错误信息", "example": "创建图片生成任务失败: API令牌无效或已过期"}}, "required": ["error"]}}}}, "402": {"description": "余额不足", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "description": "错误信息", "example": "创建图片生成任务失败: 余额不足，当前余额不足以支付 0.68 元"}}, "required": ["error"]}}}}, "500": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "description": "错误信息", "example": "图片生成失败: 豆包API调用失败"}}, "required": ["error"]}}}}}}}}, "components": {"schemas": {"ImageSizeInfo": {"type": "object", "description": "图片尺寸说明", "properties": {"1024x1024": {"type": "string", "description": "正方形 (1:1) - 适合头像、图标、社交媒体"}, "864x1152": {"type": "string", "description": "竖屏 (3:4) - 适合手机壁纸、海报"}, "1152x864": {"type": "string", "description": "横屏 (4:3) - 适合电脑壁纸、展示图"}, "1280x720": {"type": "string", "description": "宽屏 (16:9) - 适合视频封面、横幅"}, "720x1280": {"type": "string", "description": "手机竖屏 (9:16) - 适合短视频、手机壁纸"}, "832x1248": {"type": "string", "description": "海报比例 (2:3) - 适合海报、宣传图"}, "1248x832": {"type": "string", "description": "风景比例 (3:2) - 适合风景照、横版海报"}, "1512x648": {"type": "string", "description": "超宽屏 (21:9) - 适合横幅广告、全景图"}}}, "PricingInfo": {"type": "object", "description": "定价信息", "properties": {"普通用户": {"type": "string", "description": "0.68元/张"}, "VIP用户": {"type": "string", "description": "0.58元/张"}, "SVIP用户": {"type": "string", "description": "0.48元/张"}, "管理员": {"type": "string", "description": "0.48元/张（与SVIP相同）"}}}}}}