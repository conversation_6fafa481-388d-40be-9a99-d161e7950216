package org.jeecg.modules.demo.notification.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.demo.notification.entity.AicgUserNotification;
import org.jeecg.modules.demo.notification.service.IAicgUserNotificationService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;

/**
 * @Description: 用户通知消息表
 * @Author: jeecg-boot
 * @Date:   2025-06-18
 * @Version: V1.0
 */
@Api(tags="用户通知消息表")
@RestController
@RequestMapping("/demo/notification")
@Slf4j
public class AicgUserNotificationController extends JeecgController<AicgUserNotification, IAicgUserNotificationService> {
	@Autowired
	private IAicgUserNotificationService aicgUserNotificationService;
	
	/**
	 * 分页列表查询
	 */
	@AutoLog(value = "用户通知消息表-分页列表查询")
	@ApiOperation(value="用户通知消息表-分页列表查询", notes="用户通知消息表-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(AicgUserNotification aicgUserNotification,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<AicgUserNotification> queryWrapper = QueryGenerator.initQueryWrapper(aicgUserNotification, req.getParameterMap());
		Page<AicgUserNotification> page = new Page<AicgUserNotification>(pageNo, pageSize);
		IPage<AicgUserNotification> pageList = aicgUserNotificationService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 * 添加
	 */
	@AutoLog(value = "用户通知消息表-添加")
	@ApiOperation(value="用户通知消息表-添加", notes="用户通知消息表-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody AicgUserNotification aicgUserNotification) {
		aicgUserNotificationService.save(aicgUserNotification);
		return Result.OK("添加成功！");
	}
	
	/**
	 * 编辑
	 */
	@AutoLog(value = "用户通知消息表-编辑")
	@ApiOperation(value="用户通知消息表-编辑", notes="用户通知消息表-编辑")
	@PutMapping(value = "/edit")
	public Result<?> edit(@RequestBody AicgUserNotification aicgUserNotification) {
		aicgUserNotificationService.updateById(aicgUserNotification);
		return Result.OK("编辑成功!");
	}
	
	/**
	 * 通过id删除
	 */
	@AutoLog(value = "用户通知消息表-通过id删除")
	@ApiOperation(value="用户通知消息表-通过id删除", notes="用户通知消息表-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		aicgUserNotificationService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 * 批量删除
	 */
	@AutoLog(value = "用户通知消息表-批量删除")
	@ApiOperation(value="用户通知消息表-批量删除", notes="用户通知消息表-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.aicgUserNotificationService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 */
	@AutoLog(value = "用户通知消息表-通过id查询")
	@ApiOperation(value="用户通知消息表-通过id查询", notes="用户通知消息表-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		AicgUserNotification aicgUserNotification = aicgUserNotificationService.getById(id);
		if(aicgUserNotification==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(aicgUserNotification);
	}

    /**
	 * 导出excel
	 */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, AicgUserNotification aicgUserNotification) {
        return super.exportXls(request, aicgUserNotification, AicgUserNotification.class, "用户通知消息表");
    }

    /**
     * 通过excel导入数据
     */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, AicgUserNotification.class);
    }
    
    /**
     * 根据用户ID查询通知消息
     */
    @AutoLog(value = "根据用户ID查询通知消息")
    @ApiOperation(value="根据用户ID查询通知消息", notes="根据用户ID查询通知消息")
    @GetMapping(value = "/getByUserId")
    public Result<?> getByUserId(@RequestParam(name="userId",required=true) String userId) {
        List<AicgUserNotification> list = aicgUserNotificationService.getByUserId(userId);
        return Result.OK(list);
    }
    
    /**
     * 根据用户ID查询未读通知
     */
    @AutoLog(value = "根据用户ID查询未读通知")
    @ApiOperation(value="根据用户ID查询未读通知", notes="根据用户ID查询未读通知")
    @GetMapping(value = "/getUnread")
    public Result<?> getUnreadByUserId(@RequestParam(name="userId",required=true) String userId) {
        List<AicgUserNotification> list = aicgUserNotificationService.getUnreadByUserId(userId);
        return Result.OK(list);
    }
    
    /**
     * 获取通知统计
     */
    @AutoLog(value = "获取通知统计")
    @ApiOperation(value="获取通知统计", notes="获取通知统计")
    @GetMapping(value = "/getStats")
    public Result<?> getNotificationStats(@RequestParam(name="userId",required=true) String userId) {
        Map<String, Object> stats = aicgUserNotificationService.getNotificationStats(userId);
        return Result.OK(stats);
    }
    
    /**
     * 标记为已读
     */
    @AutoLog(value = "标记为已读")
    @ApiOperation(value="标记为已读", notes="标记为已读")
    @PostMapping(value = "/markRead")
    public Result<?> markAsRead(@RequestParam(name="id",required=true) String id,
                               @RequestParam(name="userId",required=true) String userId) {
        boolean success = aicgUserNotificationService.markAsRead(id, userId, "system");
        if (success) {
            return Result.OK("标记成功!");
        } else {
            return Result.error("标记失败!");
        }
    }

    /**
     * 标记为未读
     */
    @AutoLog(value = "标记为未读")
    @ApiOperation(value="标记为未读", notes="标记为未读")
    @PostMapping(value = "/markUnread")
    public Result<?> markAsUnread(@RequestParam(name="id",required=true) String id,
                                 @RequestParam(name="userId",required=true) String userId) {
        boolean success = aicgUserNotificationService.markAsUnread(id, userId, "system");
        if (success) {
            return Result.OK("标记未读成功!");
        } else {
            return Result.error("标记未读失败!");
        }
    }


    
    /**
     * 批量标记为已读
     */
    @AutoLog(value = "批量标记为已读")
    @ApiOperation(value="批量标记为已读", notes="批量标记为已读")
    @PostMapping(value = "/batchMarkRead")
    public Result<?> batchMarkAsRead(@RequestParam(name="userId",required=true) String userId,
                                    @RequestParam(name="ids",required=true) String ids) {
        List<String> idList = Arrays.asList(ids.split(","));
        int count = aicgUserNotificationService.batchMarkAsRead(userId, idList, "system");
        return Result.OK("成功标记 " + count + " 条通知为已读");
    }
    
    /**
     * 标记全部为已读
     */
    @AutoLog(value = "标记全部为已读")
    @ApiOperation(value="标记全部为已读", notes="标记全部为已读")
    @PostMapping(value = "/markAllRead")
    public Result<?> markAllAsRead(@RequestParam(name="userId",required=true) String userId) {
        int count = aicgUserNotificationService.markAllAsRead(userId, "system");
        return Result.OK("成功标记 " + count + " 条通知为已读");
    }
}
