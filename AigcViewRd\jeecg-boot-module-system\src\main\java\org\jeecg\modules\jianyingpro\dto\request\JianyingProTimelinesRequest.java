package org.jeecg.modules.jianyingpro.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 智能时间线生成请求
 * 合并 timelines + audio_timelines 的参数，支持智能模式识别
 * 
 * <AUTHOR>
 * @since 2025-01-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class JianyingProTimelinesRequest extends BaseJianyingProRequest {
    
    // ========== 音频时间线模式参数 ==========
    @ApiModelProperty(value = "音频URL列表（音频时间线模式使用）",
                     notes = "提供此参数时，系统将获取每个音频的实际时长生成连续时间线",
                     example = "[\"https://example.com/audio1.mp3\", \"https://example.com/audio2.mp3\"]")
    @JsonProperty("audio_urls")
    private List<String> audioUrls;
    
    // ========== 自定义时间线模式参数 ==========
    @ApiModelProperty(value = "总时长，单位微秒（自定义时间线模式使用）",
                     notes = "提供此参数时，系统将均匀分割总时长生成时间线",
                     example = "30000000")
    @JsonProperty("duration")
    private Long duration;
    
    @ApiModelProperty(value = "分段数量（自定义时间线模式使用）",
                     example = "5")
    @JsonProperty("num")
    private Integer num;
    
    @ApiModelProperty(value = "开始时间，单位微秒（可选，默认0）",
                     example = "0")
    @JsonProperty("start")
    private Long start;
    
    @ApiModelProperty(value = "时间线类型（可选，默认0）",
                     example = "0")
    @JsonProperty("type")
    private Integer type;
    
    @Override
    public String getSummary() {
        if (audioUrls != null && !audioUrls.isEmpty()) {
            return "JianyingProTimelinesRequest{mode=audio, audioCount=" + audioUrls.size() + "}";
        } else if (duration != null && num != null) {
            return "JianyingProTimelinesRequest{mode=custom, duration=" + duration + "μs, segments=" + num + "}";
        } else {
            return "JianyingProTimelinesRequest{mode=unknown, incomplete_params}";
        }
    }
    
    @Override
    public void validate() {
        super.validate();
        
        boolean hasAudioParams = audioUrls != null && !audioUrls.isEmpty();
        boolean hasCustomParams = duration != null && num != null;
        
        // 情况1：两种参数都提供了 - 冲突错误
        if (hasAudioParams && hasCustomParams) {
            throw new IllegalArgumentException(
                "参数冲突：不能同时提供 audio_urls 和 duration+num 参数。" +
                "请选择以下参数组合之一：\n" +
                "1. 仅提供 audio_urls（音频时间线模式）\n" +
                "2. 仅提供 duration + num（自定义时间线模式）"
            );
        }
        
        // 情况2：参数不完整
        if (!hasAudioParams && !hasCustomParams) {
            throw new IllegalArgumentException(
                "参数不完整：请提供以下参数组合之一：\n" +
                "1. audio_urls（音频时间线模式）\n" +
                "2. duration + num（自定义时间线模式）"
            );
        }
        
        // 情况3：验证音频参数
        if (hasAudioParams) {
            for (String url : audioUrls) {
                if (url == null || url.trim().isEmpty()) {
                    throw new IllegalArgumentException(
                        "audio_urls 中不能包含空的URL"
                    );
                }
            }
        }
        
        // 情况4：验证自定义参数
        if (hasCustomParams) {
            if (duration <= 0) {
                throw new IllegalArgumentException(
                    "duration 参数必须大于0，当前值: " + duration
                );
            }
            if (num <= 0) {
                throw new IllegalArgumentException(
                    "num 参数必须大于0，当前值: " + num
                );
            }
        }
    }
}
