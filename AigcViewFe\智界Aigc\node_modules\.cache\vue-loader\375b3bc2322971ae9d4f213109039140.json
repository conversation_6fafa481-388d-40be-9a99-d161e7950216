{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\agent\\AgentDetailPage.vue?vue&type=template&id=2f9872c6&scoped=true&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\agent\\AgentDetailPage.vue", "mtime": 1754511159792}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["var render = function() {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  return _c(\"div\", { staticClass: \"agent-detail-page\" }, [\n    _c(\n      \"div\",\n      { staticClass: \"timeline-nav\" },\n      [\n        _c(\"div\", { staticClass: \"timeline-line\" }),\n        _vm._l(_vm.navItems, function(item, index) {\n          return _c(\n            \"div\",\n            {\n              key: item.id,\n              staticClass: \"timeline-node\",\n              class: { active: _vm.activeNavItem === item.id },\n              style: { top: index * 120 + 100 + \"px\" },\n              on: {\n                click: function($event) {\n                  return _vm.scrollToSection(item.id)\n                }\n              }\n            },\n            [\n              _c(\"div\", { staticClass: \"node-dot\" }),\n              _c(\"div\", { staticClass: \"node-label\" }, [\n                _vm._v(_vm._s(item.title))\n              ])\n            ]\n          )\n        })\n      ],\n      2\n    ),\n    _vm.isCheckingPermission\n      ? _c(\n          \"div\",\n          { staticClass: \"permission-loading\" },\n          [\n            _c(\"a-spin\", { attrs: { size: \"large\" } }, [\n              _c(\"div\", { staticClass: \"loading-text\" }, [\n                _vm._v(\"正在验证访问权限...\")\n              ])\n            ])\n          ],\n          1\n        )\n      : !_vm.isLoggedIn\n      ? _c(\"div\", { staticClass: \"permission-denied\" }, [\n          _c(\n            \"div\",\n            { staticClass: \"permission-content\" },\n            [\n              _c(\"a-icon\", {\n                staticClass: \"permission-icon\",\n                attrs: { type: \"lock\" }\n              }),\n              _c(\"h2\", [_vm._v(\"需要登录访问\")]),\n              _c(\"p\", [_vm._v(\"请先登录您的账户以查看智能体详细信息\")]),\n              _c(\n                \"a-button\",\n                {\n                  attrs: { type: \"primary\", size: \"large\" },\n                  on: { click: _vm.goToLogin }\n                },\n                [_vm._v(\"\\n        立即登录\\n      \")]\n              )\n            ],\n            1\n          )\n        ])\n      : !_vm.isPurchased\n      ? _c(\"div\", { staticClass: \"permission-denied\" }, [\n          _c(\n            \"div\",\n            { staticClass: \"permission-content\" },\n            [\n              _c(\"a-icon\", {\n                staticClass: \"permission-icon\",\n                attrs: { type: \"shopping-cart\" }\n              }),\n              _c(\"h2\", [_vm._v(\"需要购买访问\")]),\n              _c(\"p\", [_vm._v(\"您还未购买此智能体，请先购买后再查看详细信息\")]),\n              _c(\n                \"div\",\n                { staticClass: \"action-buttons\" },\n                [\n                  _c(\n                    \"a-button\",\n                    { attrs: { size: \"large\" }, on: { click: _vm.goBack } },\n                    [_vm._v(\"\\n          返回列表\\n        \")]\n                  ),\n                  _c(\n                    \"a-button\",\n                    {\n                      attrs: { type: \"primary\", size: \"large\" },\n                      on: { click: _vm.goToPurchase }\n                    },\n                    [_vm._v(\"\\n          立即购买\\n        \")]\n                  )\n                ],\n                1\n              )\n            ],\n            1\n          )\n        ])\n      : _c(\"div\", { staticClass: \"main-content\" }, [\n          _c(\n            \"div\",\n            { staticClass: \"page-header\" },\n            [\n              _c(\n                \"a-button\",\n                { staticClass: \"back-button\", on: { click: _vm.goBack } },\n                [\n                  _c(\"a-icon\", { attrs: { type: \"arrow-left\" } }),\n                  _vm._v(\"\\n        返回\\n      \")\n                ],\n                1\n              ),\n              _c(\n                \"div\",\n                { staticClass: \"breadcrumb\" },\n                [\n                  _c(\n                    \"a-breadcrumb\",\n                    [\n                      _c(\n                        \"a-breadcrumb-item\",\n                        [\n                          _c(\n                            \"router-link\",\n                            { attrs: { to: \"/workflow-center\" } },\n                            [_vm._v(\"工作流中心\")]\n                          )\n                        ],\n                        1\n                      ),\n                      _c(\"a-breadcrumb-item\", [_vm._v(\"智能体详情\")])\n                    ],\n                    1\n                  )\n                ],\n                1\n              )\n            ],\n            1\n          ),\n          _c(\n            \"div\",\n            { staticClass: \"agent-info-section\", attrs: { id: \"agent-info\" } },\n            [\n              _c(\"div\", { staticClass: \"agent-basic-info\" }, [\n                _c(\"div\", { staticClass: \"agent-avatar\" }, [\n                  _c(\"img\", {\n                    attrs: {\n                      src: _vm.agentDetail.agentAvatar || _vm.defaultAvatar,\n                      alt: _vm.agentDetail.agentName\n                    },\n                    on: { error: _vm.handleAvatarError }\n                  })\n                ]),\n                _c(\"div\", { staticClass: \"agent-details\" }, [\n                  _c(\"h1\", { staticClass: \"agent-name\" }, [\n                    _vm._v(_vm._s(_vm.agentDetail.agentName || \"智能体名称\"))\n                  ]),\n                  _c(\"div\", { staticClass: \"agent-description\" }, [\n                    _c(\"p\", [\n                      _vm._v(\n                        _vm._s(_vm.agentDetail.agentDescription || \"暂无描述\")\n                      )\n                    ])\n                  ]),\n                  _c(\"div\", { staticClass: \"creator-info\" }, [\n                    _c(\"img\", {\n                      staticClass: \"creator-avatar\",\n                      attrs: {\n                        src:\n                          (_vm.agentDetail.creatorInfo &&\n                            _vm.agentDetail.creatorInfo.avatar) ||\n                          _vm.agentDetail.creatorAvatar ||\n                          _vm.defaultCreatorAvatar,\n                        alt:\n                          (_vm.agentDetail.creatorInfo &&\n                            _vm.agentDetail.creatorInfo.nickname) ||\n                          _vm.agentDetail.creatorNickname ||\n                          \"创作者\"\n                      },\n                      on: { error: _vm.handleCreatorAvatarError }\n                    }),\n                    _c(\"span\", { staticClass: \"creator-name\" }, [\n                      _vm._v(\n                        _vm._s(\n                          (_vm.agentDetail.creatorInfo &&\n                            _vm.agentDetail.creatorInfo.nickname) ||\n                            _vm.agentDetail.creatorNickname ||\n                            \"创作者\"\n                        )\n                      )\n                    ]),\n                    _c(\n                      \"span\",\n                      {\n                        staticClass: \"author-type-badge\",\n                        class: _vm.authorTypeClass\n                      },\n                      [\n                        _vm._v(\n                          \"\\n              \" +\n                            _vm._s(_vm.authorTypeText) +\n                            \"\\n            \"\n                        )\n                      ]\n                    )\n                  ])\n                ])\n              ]),\n              _c(\n                \"div\",\n                {\n                  staticClass: \"reading-tip\",\n                  on: { click: _vm.scrollToUsageGuide }\n                },\n                [\n                  _c(\"a-icon\", {\n                    staticClass: \"tip-icon\",\n                    attrs: { type: \"info-circle\" }\n                  }),\n                  _c(\"span\", { staticClass: \"tip-text\" }, [\n                    _vm._v(\n                      \"\\n          请往下滑或点此快速下滑，仔细阅读工作流导入使用说明\\n        \"\n                    )\n                  ]),\n                  _c(\"a-icon\", {\n                    staticClass: \"arrow-icon\",\n                    attrs: { type: \"arrow-down\" }\n                  })\n                ],\n                1\n              )\n            ]\n          ),\n          _vm.agentDetail.demoVideo\n            ? _c(\n                \"div\",\n                { staticClass: \"video-section\", attrs: { id: \"demo-video\" } },\n                [\n                  _c(\n                    \"div\",\n                    {\n                      staticClass: \"video-header\",\n                      on: { click: _vm.toggleVideoCollapse }\n                    },\n                    [\n                      _c(\"h3\", [_vm._v(\"演示视频\")]),\n                      _c(\"a-icon\", {\n                        staticClass: \"collapse-icon\",\n                        attrs: { type: _vm.videoCollapsed ? \"down\" : \"up\" }\n                      })\n                    ],\n                    1\n                  ),\n                  _c(\n                    \"div\",\n                    {\n                      directives: [\n                        {\n                          name: \"show\",\n                          rawName: \"v-show\",\n                          value: !_vm.videoCollapsed,\n                          expression: \"!videoCollapsed\"\n                        }\n                      ],\n                      staticClass: \"video-container\"\n                    },\n                    [\n                      _c(\n                        \"video\",\n                        {\n                          ref: \"videoPlayer\",\n                          attrs: {\n                            src: _vm.agentDetail.demoVideo,\n                            controls: \"\",\n                            autoplay: \"\",\n                            muted: \"\",\n                            preload: \"metadata\"\n                          },\n                          domProps: { muted: true },\n                          on: {\n                            loadstart: _vm.onVideoLoadStart,\n                            loadeddata: _vm.onVideoLoaded,\n                            error: _vm.onVideoError,\n                            play: _vm.onVideoPlay,\n                            pause: _vm.onVideoPause\n                          }\n                        },\n                        [\n                          _vm._v(\n                            \"\\n          您的浏览器不支持视频播放\\n        \"\n                          )\n                        ]\n                      ),\n                      _vm.videoError\n                        ? _c(\n                            \"div\",\n                            { staticClass: \"video-error\" },\n                            [\n                              _c(\"a-icon\", {\n                                attrs: { type: \"exclamation-circle\" }\n                              }),\n                              _c(\"span\", [_vm._v(\"视频加载失败，请稍后重试\")])\n                            ],\n                            1\n                          )\n                        : _vm._e()\n                    ]\n                  )\n                ]\n              )\n            : _vm._e(),\n          _c(\n            \"div\",\n            {\n              staticClass: \"usage-guide-section\",\n              attrs: { id: \"usage-guide\" }\n            },\n            [\n              _c(\n                \"div\",\n                {\n                  staticClass: \"guide-header\",\n                  on: { click: _vm.toggleGuideCollapse }\n                },\n                [\n                  _c(\"h3\", [_vm._v(\"工作流导入使用说明\")]),\n                  _c(\"a-icon\", {\n                    staticClass: \"collapse-icon\",\n                    attrs: { type: _vm.guideCollapsed ? \"down\" : \"up\" }\n                  })\n                ],\n                1\n              ),\n              _c(\n                \"div\",\n                {\n                  directives: [\n                    {\n                      name: \"show\",\n                      rawName: \"v-show\",\n                      value: !_vm.guideCollapsed,\n                      expression: \"!guideCollapsed\"\n                    }\n                  ],\n                  staticClass: \"guide-content\"\n                },\n                [\n                  _c(\"div\", { staticClass: \"guide-step\" }, [\n                    _c(\"div\", { staticClass: \"step-number\" }, [_vm._v(\"1\")]),\n                    _c(\"div\", { staticClass: \"step-content\" }, [\n                      _c(\"p\", { staticClass: \"step-text\" }, [\n                        _vm._v(\"点击下载工作流，下载完成是一个压缩包。\")\n                      ]),\n                      _c(\n                        \"div\",\n                        {\n                          staticClass: \"step-image\",\n                          on: {\n                            click: function($event) {\n                              return _vm.previewImage(\n                                \"https://cdn.aigcview.com/defaults/export.png\"\n                              )\n                            }\n                          }\n                        },\n                        [\n                          _c(\"img\", {\n                            attrs: {\n                              src:\n                                \"https://cdn.aigcview.com/defaults/export.png\",\n                              alt: \"下载工作流示例图\"\n                            },\n                            on: { error: _vm.handleGuideImageError }\n                          }),\n                          _c(\n                            \"div\",\n                            { staticClass: \"image-overlay\" },\n                            [\n                              _c(\"a-icon\", { attrs: { type: \"zoom-in\" } }),\n                              _c(\"span\", [_vm._v(\"点击查看大图\")])\n                            ],\n                            1\n                          )\n                        ]\n                      )\n                    ])\n                  ]),\n                  _c(\"div\", { staticClass: \"guide-step\" }, [\n                    _c(\"div\", { staticClass: \"step-number\" }, [_vm._v(\"2\")]),\n                    _c(\"div\", { staticClass: \"step-content\" }, [\n                      _c(\"p\", { staticClass: \"step-text\" }, [\n                        _c(\n                          \"a\",\n                          {\n                            staticClass: \"coze-link\",\n                            attrs: {\n                              href: \"https://www.coze.cn/space\",\n                              target: \"_blank\"\n                            }\n                          },\n                          [\n                            _vm._v(\n                              \"\\n                点此快速跳转Coze工作空间\\n                \"\n                            ),\n                            _c(\"a-icon\", { attrs: { type: \"external-link\" } })\n                          ],\n                          1\n                        ),\n                        _vm._v(\n                          \"\\n              ，选择需要放置的工作空间，点击右上角导入按钮，选择下载好的工作流压缩包即可完成。\"\n                        ),\n                        _c(\"span\", { staticClass: \"highlight-text\" }, [\n                          _vm._v(\"（压缩包无需解压）\")\n                        ])\n                      ]),\n                      _c(\n                        \"div\",\n                        {\n                          staticClass: \"step-image\",\n                          on: {\n                            click: function($event) {\n                              return _vm.previewImage(\n                                \"https://cdn.aigcview.com/defaults/import.png\"\n                              )\n                            }\n                          }\n                        },\n                        [\n                          _c(\"img\", {\n                            attrs: {\n                              src:\n                                \"https://cdn.aigcview.com/defaults/import.png\",\n                              alt: \"导入工作流示例图\"\n                            },\n                            on: { error: _vm.handleGuideImageError }\n                          }),\n                          _c(\n                            \"div\",\n                            { staticClass: \"image-overlay\" },\n                            [\n                              _c(\"a-icon\", { attrs: { type: \"zoom-in\" } }),\n                              _c(\"span\", [_vm._v(\"点击查看大图\")])\n                            ],\n                            1\n                          )\n                        ]\n                      )\n                    ])\n                  ])\n                ]\n              )\n            ]\n          ),\n          _c(\n            \"div\",\n            { staticClass: \"workflow-section\", attrs: { id: \"workflow-list\" } },\n            [\n              _c(\"h3\", [\n                _vm._v(\"关联工作流 (\" + _vm._s(_vm.workflowList.length) + \"个)\")\n              ]),\n              _vm.workflowLoading\n                ? _c(\n                    \"div\",\n                    { staticClass: \"workflow-loading\" },\n                    [_c(\"a-spin\", [_vm._v(\"加载工作流列表...\")])],\n                    1\n                  )\n                : _vm.workflowList.length === 0\n                ? _c(\n                    \"div\",\n                    { staticClass: \"workflow-empty\" },\n                    [\n                      _c(\"a-empty\", {\n                        attrs: { description: \"暂无关联工作流\" }\n                      })\n                    ],\n                    1\n                  )\n                : _c(\n                    \"div\",\n                    { staticClass: \"workflow-list\" },\n                    _vm._l(_vm.workflowList, function(workflow) {\n                      return _c(\n                        \"div\",\n                        { key: workflow.id, staticClass: \"workflow-card\" },\n                        [\n                          _c(\"div\", { staticClass: \"workflow-avatar\" }, [\n                            _c(\"img\", {\n                              staticClass: \"workflow-image\",\n                              attrs: {\n                                src:\n                                  _vm.agentDetail.agentAvatar ||\n                                  _vm.defaultAgentAvatar,\n                                alt: workflow.workflowName\n                              },\n                              on: { error: _vm.handleWorkflowImageError }\n                            })\n                          ]),\n                          _c(\"div\", { staticClass: \"workflow-info\" }, [\n                            _c(\"h4\", { staticClass: \"workflow-name\" }, [\n                              _vm._v(_vm._s(workflow.workflowName))\n                            ]),\n                            _c(\"p\", { staticClass: \"workflow-description\" }, [\n                              _vm._v(\n                                _vm._s(\n                                  workflow.workflowDescription || \"暂无描述\"\n                                )\n                              )\n                            ]),\n                            _c(\"div\", { staticClass: \"workflow-params\" }, [\n                              _c(\n                                \"div\",\n                                { staticClass: \"params-label\" },\n                                [\n                                  _c(\"a-icon\", { attrs: { type: \"setting\" } }),\n                                  _c(\"span\", [_vm._v(\"输入参数说明\")])\n                                ],\n                                1\n                              ),\n                              _c(\"div\", { staticClass: \"params-content\" }, [\n                                _vm._v(\n                                  \"\\n                \" +\n                                    _vm._s(\n                                      workflow.inputParamsDesc ||\n                                        \"暂无输入参数说明\"\n                                    ) +\n                                    \"\\n              \"\n                                )\n                              ])\n                            ])\n                          ]),\n                          _c(\n                            \"div\",\n                            { staticClass: \"workflow-actions\" },\n                            [\n                              _c(\n                                \"a-button\",\n                                {\n                                  staticClass: \"download-btn\",\n                                  attrs: {\n                                    type: \"primary\",\n                                    size: \"default\",\n                                    loading: workflow.downloading,\n                                    disabled: !workflow.workflowPackage\n                                  },\n                                  on: {\n                                    click: function($event) {\n                                      return _vm.handleWorkflowDownload(\n                                        workflow\n                                      )\n                                    }\n                                  }\n                                },\n                                [\n                                  _c(\"a-icon\", {\n                                    attrs: { type: \"cloud-download\" }\n                                  }),\n                                  _c(\"span\", [_vm._v(\"下载工作流\")])\n                                ],\n                                1\n                              )\n                            ],\n                            1\n                          )\n                        ]\n                      )\n                    }),\n                    0\n                  ),\n              _vm.workflowList.length > 0\n                ? _c(\n                    \"div\",\n                    { staticClass: \"workflow-usage-tip\" },\n                    [\n                      _c(\"a-icon\", {\n                        staticClass: \"tip-icon\",\n                        attrs: { type: \"info-circle\" }\n                      }),\n                      _vm._m(0)\n                    ],\n                    1\n                  )\n                : _vm._e()\n            ]\n          )\n        ])\n  ])\n}\nvar staticRenderFns = [\n  function() {\n    var _vm = this\n    var _h = _vm.$createElement\n    var _c = _vm._self._c || _h\n    return _c(\"span\", { staticClass: \"tip-text\" }, [\n      _vm._v(\n        \"\\n          点击下载工作流是一个压缩包，将下载好的压缩包导入进Coze工作空间即可使用。\"\n      ),\n      _c(\"span\", { staticClass: \"highlight-text\" }, [\n        _vm._v(\"（压缩包无需解压）\")\n      ])\n    ])\n  }\n]\nrender._withStripped = true\n\nexport { render, staticRenderFns }"]}