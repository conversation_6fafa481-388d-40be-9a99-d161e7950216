package org.jeecg.modules.api.service;

import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.api.config.UserActivityConfig;
import org.jeecg.modules.api.dto.UserActivityUpdateDTO;
import org.jeecg.modules.api.mapper.AicgOnlineUsersMapper;
import org.jeecg.modules.api.entity.AicgOnlineUsers;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.util.*;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

/**
 * @Description: 用户活跃状态批量更新服务
 * @Author: AigcView
 * @Date: 2025-01-30
 * @Version: V1.0
 */
@Slf4j
@Service
public class UserActivityBatchUpdateService implements IUserActivityBatchUpdateService {

    @Autowired
    private UserActivityConfig config;

    @Autowired
    private AicgOnlineUsersMapper onlineUsersMapper;

    /**
     * 待更新数据队列（线程安全）
     */
    private final ConcurrentLinkedQueue<UserActivityUpdateDTO> updateQueue = new ConcurrentLinkedQueue<>();

    /**
     * 性能监控指标
     */
    private final AtomicLong totalProcessed = new AtomicLong(0);
    private final AtomicLong totalSuccess = new AtomicLong(0);
    private final AtomicLong totalFailed = new AtomicLong(0);
    private final AtomicInteger currentQueueSize = new AtomicInteger(0);
    private volatile boolean isProcessing = false;
    private volatile long lastProcessTime = System.currentTimeMillis();

    /**
     * 降级状态
     */
    private volatile boolean isDegraded = false;
    private volatile long degradationStartTime = 0;

    @PostConstruct
    public void init() {
        log.info("用户活跃状态批量更新服务初始化完成");
        log.info("配置信息 - 批量大小: {}, 批量间隔: {}秒, 队列容量: {}", 
                config.getBatchSize(), config.getBatchInterval(), config.getQueueCapacity());
    }

    @PreDestroy
    public void destroy() {
        log.info("用户活跃状态批量更新服务正在关闭...");
        // 处理剩余队列中的数据
        if (!updateQueue.isEmpty()) {
            log.info("处理剩余队列数据，队列大小: {}", updateQueue.size());
            processBatchSync();
        }
        log.info("用户活跃状态批量更新服务已关闭");
    }

    /**
     * 添加用户活跃状态更新到队列
     * @param updateDTO 更新数据
     * @return 是否添加成功
     */
    public boolean addToUpdateQueue(UserActivityUpdateDTO updateDTO) {
        if (!config.getEnabled()) {
            log.debug("用户活跃状态追踪已禁用，跳过添加到队列");
            return false;
        }

        if (updateDTO == null || !updateDTO.isValid()) {
            log.warn("无效的更新数据，跳过添加到队列: {}", updateDTO);
            return false;
        }

        // 检查队列容量
        if (currentQueueSize.get() >= config.getQueueCapacity()) {
            log.warn("队列已满，当前大小: {}, 最大容量: {}", currentQueueSize.get(), config.getQueueCapacity());
            
            // 如果启用降级机制，直接处理当前数据
            if (config.getEnableDegradation()) {
                return processImmediately(updateDTO);
            }
            return false;
        }

        // 设置是否为核心API
        if (updateDTO.getApiPath() != null) {
            updateDTO.setIsCriticalApi(config.isCriticalApi(updateDTO.getApiPath()));
        }

        // 添加到队列
        boolean added = updateQueue.offer(updateDTO);
        if (added) {
            currentQueueSize.incrementAndGet();
            log.debug("添加用户活跃状态更新到队列: {}, 当前队列大小: {}", updateDTO, currentQueueSize.get());
            
            // 如果达到批量大小，触发立即处理
            if (currentQueueSize.get() >= config.getBatchSize()) {
                triggerBatchProcess();
            }
        } else {
            log.error("添加到队列失败: {}", updateDTO);
        }

        return added;
    }

    /**
     * 立即处理单个更新（降级模式）
     * @param updateDTO 更新数据
     * @return 是否处理成功
     */
    private boolean processImmediately(UserActivityUpdateDTO updateDTO) {
        if (config.getTestMode()) {
            log.info("测试模式 - 立即处理用户活跃状态更新: {}", updateDTO);
            return true;
        }

        try {
            long startTime = System.currentTimeMillis();
            
            // 执行数据库更新
            boolean success = updateUserActivity(updateDTO);
            
            long processingTime = System.currentTimeMillis() - startTime;
            
            if (success) {
                totalSuccess.incrementAndGet();
                log.debug("立即处理用户活跃状态更新成功: {}, 耗时: {}ms", updateDTO, processingTime);
            } else {
                totalFailed.incrementAndGet();
                log.warn("立即处理用户活跃状态更新失败: {}", updateDTO);
            }
            
            totalProcessed.incrementAndGet();
            return success;
            
        } catch (Exception e) {
            log.error("立即处理用户活跃状态更新异常: {}, 错误: {}", updateDTO, e.getMessage(), e);
            totalFailed.incrementAndGet();
            totalProcessed.incrementAndGet();
            return false;
        }
    }

    /**
     * 触发批量处理
     */
    private void triggerBatchProcess() {
        if (config.getEnableAsyncProcessing()) {
            processBatchAsync();
        } else {
            processBatchSync();
        }
    }

    /**
     * 异步批量处理
     */
    @Async
    public void processBatchAsync() {
        processBatch();
    }

    /**
     * 同步批量处理
     */
    public void processBatchSync() {
        processBatch();
    }

    /**
     * 定时批量处理任务
     */
    @Scheduled(fixedDelayString = "#{${aigc.user-activity.batch-interval:10} * 1000}")
    public void scheduledBatchProcess() {
        if (!config.getEnabled() || updateQueue.isEmpty()) {
            return;
        }

        // 检查是否需要从降级状态恢复
        checkDegradationRecovery();

        processBatch();
    }

    /**
     * 核心批量处理逻辑
     */
    private void processBatch() {
        if (isProcessing) {
            log.debug("批量处理正在进行中，跳过本次处理");
            return;
        }

        if (updateQueue.isEmpty()) {
            return;
        }

        isProcessing = true;
        long startTime = System.currentTimeMillis();

        try {
            List<UserActivityUpdateDTO> batchData = collectBatchData();
            if (batchData.isEmpty()) {
                return;
            }

            log.info("开始批量处理用户活跃状态更新，批量大小: {}", batchData.size());

            if (config.getTestMode()) {
                log.info("测试模式 - 模拟批量处理: {}", batchData.size());
                totalProcessed.addAndGet(batchData.size());
                totalSuccess.addAndGet(batchData.size());
                return;
            }

            // 执行批量更新
            int successCount = executeBatchUpdate(batchData);
            int failedCount = batchData.size() - successCount;

            // 更新统计信息
            totalProcessed.addAndGet(batchData.size());
            totalSuccess.addAndGet(successCount);
            totalFailed.addAndGet(failedCount);

            long processingTime = System.currentTimeMillis() - startTime;
            lastProcessTime = System.currentTimeMillis();

            log.info("批量处理完成 - 总数: {}, 成功: {}, 失败: {}, 耗时: {}ms", 
                    batchData.size(), successCount, failedCount, processingTime);

            // 检查是否需要启动降级机制
            if (processingTime > config.getPerformanceThreshold() && config.getEnableDegradation()) {
                activateDegradation();
            }

        } catch (Exception e) {
            log.error("批量处理异常: {}", e.getMessage(), e);
        } finally {
            isProcessing = false;
        }
    }

    /**
     * 收集批量数据
     * @return 批量数据列表
     */
    private List<UserActivityUpdateDTO> collectBatchData() {
        List<UserActivityUpdateDTO> batchData = new ArrayList<>();
        int batchSize = config.getBatchSize();

        // 从队列中取出数据
        for (int i = 0; i < batchSize && !updateQueue.isEmpty(); i++) {
            UserActivityUpdateDTO dto = updateQueue.poll();
            if (dto != null) {
                currentQueueSize.decrementAndGet();
                
                // 检查数据是否过期（超过5分钟的数据丢弃）
                if (dto.isExpired(5 * 60 * 1000)) {
                    log.warn("丢弃过期数据: {}", dto);
                    continue;
                }
                
                batchData.add(dto);
            }
        }

        // 去重处理（基于用户ID和会话ID）
        Map<String, UserActivityUpdateDTO> uniqueData = new LinkedHashMap<>();
        for (UserActivityUpdateDTO dto : batchData) {
            String key = dto.getUniqueKey();
            // 保留最新的数据
            if (!uniqueData.containsKey(key) || 
                dto.getTimestamp() > uniqueData.get(key).getTimestamp()) {
                uniqueData.put(key, dto);
            }
        }

        List<UserActivityUpdateDTO> result = new ArrayList<>(uniqueData.values());
        
        if (result.size() != batchData.size()) {
            log.debug("去重后数据量从 {} 减少到 {}", batchData.size(), result.size());
        }

        return result;
    }

    /**
     * 执行批量更新
     * @param batchData 批量数据
     * @return 成功更新的数量
     */
    @Transactional(rollbackFor = Exception.class)
    public int executeBatchUpdate(List<UserActivityUpdateDTO> batchData) {
        if (batchData == null || batchData.isEmpty()) {
            return 0;
        }

        try {
            // 按更新类型分组处理
            Map<String, List<UserActivityUpdateDTO>> groupedData = batchData.stream()
                .collect(Collectors.groupingBy(UserActivityUpdateDTO::getUpdateType));

            int totalSuccessCount = 0;

            // 处理LOGIN类型 - 使用批量UPSERT
            if (groupedData.containsKey("LOGIN")) {
                List<UserActivityUpdateDTO> loginData = groupedData.get("LOGIN");
                int loginSuccess = executeBatchUpsert(loginData);
                totalSuccessCount += loginSuccess;
                log.info("批量处理LOGIN更新完成，成功: {}/{}", loginSuccess, loginData.size());
            }

            // 处理ACTIVITY类型 - 使用批量更新
            if (groupedData.containsKey("ACTIVITY")) {
                List<UserActivityUpdateDTO> activityData = groupedData.get("ACTIVITY");
                int activitySuccess = executeBatchActivityUpdate(activityData);
                totalSuccessCount += activitySuccess;
                log.info("批量处理ACTIVITY更新完成，成功: {}/{}", activitySuccess, activityData.size());
            }

            // 处理LOGOUT类型 - 使用批量离线设置
            if (groupedData.containsKey("LOGOUT")) {
                List<UserActivityUpdateDTO> logoutData = groupedData.get("LOGOUT");
                int logoutSuccess = executeBatchLogout(logoutData);
                totalSuccessCount += logoutSuccess;
                log.info("批量处理LOGOUT更新完成，成功: {}/{}", logoutSuccess, logoutData.size());
            }

            return totalSuccessCount;

        } catch (Exception e) {
            log.error("批量更新用户活跃状态异常: {}", e.getMessage(), e);
            // 降级到单个处理
            return executeBatchUpdateFallback(batchData);
        }
    }

    /**
     * 执行批量UPSERT操作（LOGIN类型）
     */
    private int executeBatchUpsert(List<UserActivityUpdateDTO> loginData) {
        try {
            int result = onlineUsersMapper.batchUpsertUserActivity(loginData);
            log.debug("批量UPSERT操作完成，影响行数: {}", result);
            return result;
        } catch (Exception e) {
            log.error("批量UPSERT操作失败: {}", e.getMessage(), e);
            return executeSingleUpdates(loginData);
        }
    }

    /**
     * 执行批量活跃状态更新（ACTIVITY类型）
     */
    private int executeBatchActivityUpdate(List<UserActivityUpdateDTO> activityData) {
        try {
            int result = onlineUsersMapper.batchUpdateUserActivity(activityData);
            log.debug("批量活跃状态更新完成，影响行数: {}", result);
            return result;
        } catch (Exception e) {
            log.error("批量活跃状态更新失败: {}", e.getMessage(), e);
            return executeSingleUpdates(activityData);
        }
    }

    /**
     * 执行批量登出操作（LOGOUT类型）
     */
    private int executeBatchLogout(List<UserActivityUpdateDTO> logoutData) {
        try {
            List<String> userIds = logoutData.stream()
                .map(UserActivityUpdateDTO::getUserId)
                .distinct()
                .collect(Collectors.toList());

            int result = onlineUsersMapper.batchSetUsersOffline(userIds);
            log.debug("批量登出操作完成，影响行数: {}", result);
            return result;
        } catch (Exception e) {
            log.error("批量登出操作失败: {}", e.getMessage(), e);
            return executeSingleUpdates(logoutData);
        }
    }

    /**
     * 降级处理：单个更新（当批量操作失败时）
     */
    private int executeBatchUpdateFallback(List<UserActivityUpdateDTO> batchData) {
        log.warn("批量操作失败，降级到单个处理模式");
        return executeSingleUpdates(batchData);
    }

    /**
     * 执行单个更新（降级模式）
     */
    private int executeSingleUpdates(List<UserActivityUpdateDTO> batchData) {
        int successCount = 0;

        for (UserActivityUpdateDTO dto : batchData) {
            try {
                boolean success = updateUserActivity(dto);
                if (success) {
                    successCount++;
                } else {
                    // 重试逻辑
                    if (!dto.isExceedMaxRetry(config.getMaxRetryCount())) {
                        dto.incrementRetryCount();
                        addToUpdateQueue(dto); // 重新加入队列
                        log.debug("更新失败，重新加入队列重试: {}", dto);
                    } else {
                        log.error("更新失败且超过最大重试次数: {}", dto);
                    }
                }
            } catch (Exception e) {
                log.error("更新用户活跃状态异常: {}, 错误: {}", dto, e.getMessage(), e);
            }
        }

        return successCount;
    }

    /**
     * 更新单个用户活跃状态
     * @param dto 更新数据
     * @return 是否成功
     */
    private boolean updateUserActivity(UserActivityUpdateDTO dto) {
        try {
            switch (dto.getUpdateType()) {
                case "LOGIN":
                    return handleLoginUpdate(dto);
                case "LOGOUT":
                    return handleLogoutUpdate(dto);
                case "ACTIVITY":
                default:
                    return handleActivityUpdate(dto);
            }
        } catch (Exception e) {
            log.error("更新用户活跃状态异常: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 处理登录更新
     */
    private boolean handleLoginUpdate(UserActivityUpdateDTO dto) {
        // 先设置该用户的其他会话为离线
        onlineUsersMapper.setUserOffline(dto.getUserId());
        
        // 创建新的在线记录
        AicgOnlineUsers onlineUser = new AicgOnlineUsers();
        onlineUser.setUserId(dto.getUserId());
        onlineUser.setSessionId(dto.getSessionId());
        onlineUser.setLoginTime(dto.getLastActiveTime());
        onlineUser.setLastActiveTime(dto.getLastActiveTime());
        onlineUser.setIpAddress(dto.getIpAddress());
        onlineUser.setUserAgent(dto.getUserAgent());
        onlineUser.setStatus(true);
        onlineUser.setCreateTime(new Date());
        onlineUser.setUpdateTime(new Date());
        
        int result = onlineUsersMapper.insert(onlineUser);
        return result > 0;
    }

    /**
     * 处理登出更新
     */
    private boolean handleLogoutUpdate(UserActivityUpdateDTO dto) {
        int result = onlineUsersMapper.setUserOffline(dto.getUserId());
        return result > 0;
    }

    /**
     * 处理活跃状态更新
     */
    private boolean handleActivityUpdate(UserActivityUpdateDTO dto) {
        int result = onlineUsersMapper.updateLastActiveTime(dto.getUserId());
        return result > 0;
    }

    /**
     * 激活降级机制
     */
    private void activateDegradation() {
        if (!isDegraded) {
            isDegraded = true;
            degradationStartTime = System.currentTimeMillis();
            log.warn("激活降级机制 - 性能阈值超标");
        }
    }

    /**
     * 检查降级恢复
     */
    private void checkDegradationRecovery() {
        if (isDegraded) {
            long degradationDuration = System.currentTimeMillis() - degradationStartTime;
            if (degradationDuration > config.getDegradationRecoveryTime() * 1000) {
                isDegraded = false;
                log.info("从降级状态恢复正常");
            }
        }
    }

    /**
     * 获取队列大小
     * @return 当前队列大小
     */
    public int getQueueSize() {
        return currentQueueSize.get();
    }

    /**
     * 获取性能统计信息
     * @return 性能统计
     */
    public Map<String, Object> getPerformanceStats() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("totalProcessed", totalProcessed.get());
        stats.put("totalSuccess", totalSuccess.get());
        stats.put("totalFailed", totalFailed.get());
        stats.put("currentQueueSize", currentQueueSize.get());
        stats.put("isProcessing", isProcessing);
        stats.put("isDegraded", isDegraded);
        stats.put("lastProcessTime", new Date(lastProcessTime));
        
        long total = totalProcessed.get();
        if (total > 0) {
            stats.put("successRate", (double) totalSuccess.get() / total * 100);
        } else {
            stats.put("successRate", 0.0);
        }
        
        return stats;
    }

    /**
     * 强制执行批量更新
     * @return 处理的数据量
     */
    public int forceBatchUpdate() {
        if (updateQueue.isEmpty()) {
            return 0;
        }
        
        int originalSize = currentQueueSize.get();
        processBatchSync();
        return originalSize - currentQueueSize.get();
    }

    /**
     * 清空队列
     * @return 清空的数据量
     */
    public int clearQueue() {
        int size = currentQueueSize.get();
        updateQueue.clear();
        currentQueueSize.set(0);
        log.info("清空更新队列，清空数据量: {}", size);
        return size;
    }

    // ==================== 新增批量操作方法 ====================

    /**
     * 批量清理离线用户
     * @param minutes 离线时间阈值（分钟）
     * @return 清理的用户数量
     */
    public int batchCleanOfflineUsers(int minutes) {
        try {
            int result = onlineUsersMapper.batchCleanOfflineUsers(minutes);
            log.info("批量清理离线用户完成，清理数量: {}, 离线时间阈值: {}分钟", result, minutes);
            return result;
        } catch (Exception e) {
            log.error("批量清理离线用户失败: {}", e.getMessage(), e);
            return 0;
        }
    }

    /**
     * 批量设置指定用户离线
     * @param userIds 用户ID列表
     * @return 设置离线的用户数量
     */
    public int batchSetUsersOffline(List<String> userIds) {
        if (userIds == null || userIds.isEmpty()) {
            return 0;
        }

        try {
            int result = onlineUsersMapper.batchSetUsersOffline(userIds);
            log.info("批量设置用户离线完成，设置数量: {}", result);
            return result;
        } catch (Exception e) {
            log.error("批量设置用户离线失败: {}", e.getMessage(), e);
            return 0;
        }
    }

    /**
     * 强制批量UPSERT用户活跃状态
     * @param userActivities 用户活跃状态列表
     * @return 成功处理的数量
     */
    public int forceBatchUpsert(List<UserActivityUpdateDTO> userActivities) {
        if (userActivities == null || userActivities.isEmpty()) {
            return 0;
        }

        try {
            int result = onlineUsersMapper.batchUpsertUserActivity(userActivities);
            log.info("强制批量UPSERT完成，处理数量: {}", result);
            return result;
        } catch (Exception e) {
            log.error("强制批量UPSERT失败: {}", e.getMessage(), e);
            return 0;
        }
    }

    /**
     * 定时清理离线用户任务
     * 每30分钟执行一次，清理超过15分钟未活跃的用户
     */
    @Scheduled(fixedDelayString = "#{${aigc.user-activity.cleanup-interval:1800} * 1000}")
    public void scheduledCleanupOfflineUsers() {
        if (!config.getEnabled()) {
            return;
        }

        try {
            int offlineThreshold = config.getOfflineThresholdMinutes();
            int cleanedCount = batchCleanOfflineUsers(offlineThreshold);

            if (cleanedCount > 0) {
                log.info("定时清理离线用户完成，清理数量: {}", cleanedCount);
            }
        } catch (Exception e) {
            log.error("定时清理离线用户任务异常: {}", e.getMessage(), e);
        }
    }

    /**
     * 获取批量操作性能统计
     * @return 批量操作统计信息
     */
    public Map<String, Object> getBatchOperationStats() {
        Map<String, Object> stats = new HashMap<>();

        try {
            // 获取表性能统计
            Map<String, Object> tableStats = onlineUsersMapper.getTablePerformanceStats();
            stats.putAll(tableStats);

            // 添加队列统计
            stats.put("current_queue_size", currentQueueSize.get());
            stats.put("total_processed", totalProcessed.get());
            stats.put("total_success", totalSuccess.get());
            stats.put("total_failed", totalFailed.get());
            stats.put("success_rate", calculateSuccessRate());

            // 添加批量操作配置
            stats.put("batch_size", config.getBatchSize());
            stats.put("queue_capacity", config.getQueueCapacity());
            stats.put("offline_threshold_minutes", config.getOfflineThresholdMinutes());

        } catch (Exception e) {
            log.error("获取批量操作统计失败: {}", e.getMessage(), e);
            stats.put("error", e.getMessage());
        }

        return stats;
    }

    /**
     * 计算成功率
     */
    private double calculateSuccessRate() {
        long total = totalProcessed.get();
        if (total == 0) {
            return 0.0;
        }
        return (double) totalSuccess.get() / total * 100.0;
    }
}
