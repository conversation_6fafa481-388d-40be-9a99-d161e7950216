package org.jeecg.modules.demo.referral.entity;

import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 用户推荐关系表
 * @Author: jeecg-boot
 * @Date:   2025-06-18
 * @Version: V1.0
 */
@Data
@TableName("aicg_user_referral")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="aicg_user_referral对象", description="用户推荐关系表")
public class AicgUserReferral implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String id;
    
	/**推荐人ID*/
	@Excel(name = "推荐人ID", width = 15)
    @ApiModelProperty(value = "推荐人ID，关联sys_user.id")
    private String referrerId;
    
	/**被推荐人ID*/
	@Excel(name = "被推荐人ID", width = 15)
    @ApiModelProperty(value = "被推荐人ID，关联sys_user.id")
    private String refereeId;
    
	/**推荐码*/
	@Excel(name = "推荐码", width = 15)
    @ApiModelProperty(value = "推荐码")
    private String referralCode;
    
	/**被推荐人注册时间*/
	@Excel(name = "被推荐人注册时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "被推荐人注册时间")
    private Date registerTime;
    
	/**首次充值时间*/
	@Excel(name = "首次充值时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "首次充值时间")
    private Date firstRechargeTime;
    
	/**首次充值金额*/
	@Excel(name = "首次充值金额", width = 15)
    @ApiModelProperty(value = "首次充值金额")
    private BigDecimal firstRechargeAmount;
    
	/**状态*/
	@Excel(name = "状态", width = 15)
    @ApiModelProperty(value = "状态：1=待确认,2=已确认,3=已奖励")
    private Integer status;
    
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;
    
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private Date createTime;
    
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private String updateBy;
    
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private Date updateTime;
    
	/**所属部门*/
    @ApiModelProperty(value = "所属部门")
    private String sysOrgCode;
}
