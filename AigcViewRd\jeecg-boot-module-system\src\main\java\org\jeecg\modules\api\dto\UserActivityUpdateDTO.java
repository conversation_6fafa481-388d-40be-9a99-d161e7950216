package org.jeecg.modules.api.dto;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.Date;

/**
 * @Description: 用户活跃状态更新数据传输对象
 * @Author: AigcView
 * @Date: 2025-01-30
 * @Version: V1.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserActivityUpdateDTO {
    
    /**
     * 用户ID
     */
    private String userId;
    
    /**
     * 会话ID
     */
    private String sessionId;
    
    /**
     * 最后活跃时间
     */
    private Date lastActiveTime;
    
    /**
     * IP地址
     */
    private String ipAddress;
    
    /**
     * 用户代理字符串
     */
    private String userAgent;
    
    /**
     * API路径（用于判断是否为核心API）
     */
    private String apiPath;
    
    /**
     * 请求时间戳
     */
    private Long timestamp;
    
    /**
     * 用户状态（1-在线，0-离线）
     */
    private Integer status;
    
    /**
     * 更新类型（LOGIN-登录，ACTIVITY-活跃，LOGOUT-登出）
     */
    private String updateType;
    
    /**
     * 是否为核心API调用
     */
    private Boolean isCriticalApi;
    
    /**
     * 重试次数
     */
    private Integer retryCount;
    
    /**
     * 创建时间（DTO创建时间，用于性能监控）
     */
    private Date createTime;

    /**
     * 主键ID（用于UPSERT操作）
     */
    private String id;

    /**
     * 登录时间（用于LOGIN类型更新）
     */
    private Date loginTime;
    
    /**
     * 构造方法 - 用于快速创建活跃状态更新DTO
     * @param userId 用户ID
     * @param sessionId 会话ID
     * @param apiPath API路径
     * @param ipAddress IP地址
     * @param userAgent 用户代理
     * @return UserActivityUpdateDTO
     */
    public static UserActivityUpdateDTO createActivityUpdate(String userId, String sessionId, 
                                                           String apiPath, String ipAddress, String userAgent) {
        return UserActivityUpdateDTO.builder()
                .userId(userId)
                .sessionId(sessionId)
                .lastActiveTime(new Date())
                .ipAddress(ipAddress)
                .userAgent(userAgent)
                .apiPath(apiPath)
                .timestamp(System.currentTimeMillis())
                .status(1) // 在线状态
                .updateType("ACTIVITY")
                .retryCount(0)
                .createTime(new Date())
                .build();
    }
    
    /**
     * 构造方法 - 用于创建登录状态更新DTO
     * @param userId 用户ID
     * @param sessionId 会话ID
     * @param ipAddress IP地址
     * @param userAgent 用户代理
     * @return UserActivityUpdateDTO
     */
    public static UserActivityUpdateDTO createLoginUpdate(String userId, String sessionId,
                                                        String ipAddress, String userAgent) {
        Date now = new Date();
        return UserActivityUpdateDTO.builder()
                .id(java.util.UUID.randomUUID().toString().replace("-", ""))
                .userId(userId)
                .sessionId(sessionId)
                .loginTime(now)
                .lastActiveTime(now)
                .ipAddress(ipAddress)
                .userAgent(userAgent)
                .timestamp(System.currentTimeMillis())
                .status(1) // 在线状态
                .updateType("LOGIN")
                .retryCount(0)
                .createTime(now)
                .build();
    }
    
    /**
     * 构造方法 - 用于创建登出状态更新DTO
     * @param userId 用户ID
     * @param sessionId 会话ID
     * @return UserActivityUpdateDTO
     */
    public static UserActivityUpdateDTO createLogoutUpdate(String userId, String sessionId) {
        return UserActivityUpdateDTO.builder()
                .userId(userId)
                .sessionId(sessionId)
                .lastActiveTime(new Date())
                .timestamp(System.currentTimeMillis())
                .status(0) // 离线状态
                .updateType("LOGOUT")
                .retryCount(0)
                .createTime(new Date())
                .build();
    }
    
    /**
     * 增加重试次数
     */
    public void incrementRetryCount() {
        if (this.retryCount == null) {
            this.retryCount = 0;
        }
        this.retryCount++;
    }
    
    /**
     * 检查是否超过最大重试次数
     * @param maxRetryCount 最大重试次数
     * @return 是否超过最大重试次数
     */
    public boolean isExceedMaxRetry(int maxRetryCount) {
        return this.retryCount != null && this.retryCount >= maxRetryCount;
    }
    
    /**
     * 获取数据年龄（从创建到现在的毫秒数）
     * @return 数据年龄（毫秒）
     */
    public long getDataAge() {
        if (createTime == null) {
            return 0;
        }
        return System.currentTimeMillis() - createTime.getTime();
    }
    
    /**
     * 检查数据是否过期
     * @param maxAgeMillis 最大年龄（毫秒）
     * @return 是否过期
     */
    public boolean isExpired(long maxAgeMillis) {
        return getDataAge() > maxAgeMillis;
    }
    
    /**
     * 生成唯一键（用于去重）
     * @return 唯一键
     */
    public String getUniqueKey() {
        return userId + ":" + sessionId + ":" + updateType;
    }
    
    /**
     * 检查DTO是否有效
     * @return 是否有效
     */
    public boolean isValid() {
        return userId != null && !userId.trim().isEmpty() &&
               sessionId != null && !sessionId.trim().isEmpty() &&
               lastActiveTime != null &&
               status != null &&
               updateType != null;
    }
    
    @Override
    public String toString() {
        return "UserActivityUpdateDTO{" +
                "userId='" + userId + '\'' +
                ", sessionId='" + sessionId + '\'' +
                ", updateType='" + updateType + '\'' +
                ", status=" + status +
                ", retryCount=" + retryCount +
                ", dataAge=" + getDataAge() + "ms" +
                '}';
    }
}
