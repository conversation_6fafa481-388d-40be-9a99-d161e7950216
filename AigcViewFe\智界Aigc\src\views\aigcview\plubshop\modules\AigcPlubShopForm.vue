<template>
  <a-spin :spinning="confirmLoading">
    <j-form-container :disabled="formDisabled">
      <a-form-model ref="form" :model="model" :rules="validatorRules" slot="detail">
        <!-- 🔥 第一行：是否组合插件 + 组合插件名 -->
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item label="是否组合插件" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-dict-select-tag
                v-model="model.isCombined"
                dictCode="isTrue"
                placeholder="请选择是否为组合插件" />
            </a-form-model-item>
          </a-col>
          <a-col :span="12" v-if="isCombinedPlugin">
            <a-form-model-item label="组合插件名" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-input
                v-model="model.combinedName"
                placeholder="请输入组合插件名称" />
              <!-- 🔥 权限验证错误提示 -->
              <div v-if="combinedNameError" style="color: #f5222d; font-size: 12px; margin-top: 4px;">
                <a-icon type="exclamation-circle" /> {{ combinedNameError }}
              </div>
              <!-- 🔥 成功提示 -->
              <div v-if="foundExistingCombined && !combinedNameError" style="color: #52c41a; font-size: 12px; margin-top: 4px;">
                <a-icon type="check-circle" /> 已找到现有组合插件，相关信息已自动回填
              </div>
            </a-form-model-item>
          </a-col>
        </a-row>

        <!-- 🔥 第二行：组合插件介绍（独占一行） -->
        <a-row :gutter="24" v-if="isCombinedPlugin">
          <a-col :span="24">
            <a-form-model-item label="组合插件介绍" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <a-textarea
                v-model="model.combinedDescription"
                rows="3"
                placeholder="请输入组合插件介绍" />
            </a-form-model-item>
          </a-col>
        </a-row>

        <!-- 🔥 第三行：组合插件图片（独占一行） -->
        <a-row :gutter="24" v-if="isCombinedPlugin">
          <a-col :span="24">
            <a-form-model-item label="组合插件图片" :labelCol="labelCol" :wrapperCol="wrapperCol">
              <j-image-upload-deferred ref="combinedImageUpload" v-model="model.combinedImage" />
            </a-form-model-item>
          </a-col>
        </a-row>

        <!-- 第四行：插件名称、插件图片 -->
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item label="插件名称" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="plubname">
              <a-input v-model="model.plubname" placeholder="请输入插件名称"></a-input>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="插件图片" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="plubimg">
              <j-image-upload-deferred ref="pluginImageUpload" isMultiple v-model="model.plubimg"></j-image-upload-deferred>
            </a-form-model-item>
          </a-col>
        </a-row>

        <!-- 第二行：插件创作者、插件分类 -->
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item label="插件创作者" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="plubwrite">
              <j-dict-select-tag type="list" v-model="model.plubwrite" dictCode="aigc_plub_author,authorname,id" placeholder="请选择插件创作者" />
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="插件分类" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="plubCategory">
              <j-dict-select-tag v-model="model.plubCategory" dictCode="plugin_category" placeholder="请选择插件分类" />
            </a-form-model-item>
          </a-col>
        </a-row>

        <!-- 第二点五行：适用场景（独占一行） -->
        <a-row :gutter="24">
          <a-col :span="24">
            <a-form-model-item label="适用场景" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="scenarios">
              <j-multi-select-tag
                v-model="model.scenarios"
                dictCode="plugin_scenarios"
                placeholder="请选择适用场景（可多选）">
              </j-multi-select-tag>
            </a-form-model-item>
          </a-col>
        </a-row>

        <!-- 第三行：插件介绍（独占一行） -->
        <a-row :gutter="24">
          <a-col :span="24">
            <a-form-model-item label="插件介绍" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="plubinfo">
              <a-textarea v-model="model.plubinfo" rows="4" placeholder="请输入插件介绍" />
            </a-form-model-item>
          </a-col>
        </a-row>

        <!-- 第四行：插件详细内容（独占一行） -->
        <a-row :gutter="24">
          <a-col :span="24">
            <a-form-model-item
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
              label="插件详细内容"
              prop="plubContent"
              class="j-field-content">
              <j-editor v-model="model.plubContent" placeholder="请输入插件详细内容"></j-editor>
            </a-form-model-item>
          </a-col>
        </a-row>

        <!-- 第五行：插件状态、排序权重 -->
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item label="插件状态" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="status">
              <j-dict-select-tag
                v-model="model.status"
                dictCode="plugin_status"
                placeholder="请选择插件状态"
                :disabled="!isAdmin" />
              <div v-if="!isAdmin" style="color: #999; font-size: 12px; margin-top: 4px;">
                非管理员用户默认为"审核中"状态
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="排序权重" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="sortOrder">
              <a-input-number
                v-model="model.sortOrder"
                placeholder="请输入排序权重(数字越大越靠前)"
                style="width: 100%"
                :disabled="!isAdmin"
                @change="handleSortOrderChange" />
              <div v-if="!isAdmin" style="color: #999; font-size: 12px; margin-top: 4px;">
                非管理员用户自动分配权重
              </div>
            </a-form-model-item>
          </a-col>
        </a-row>

        <!-- 第六行：插件唯一标识（独占一行，因为有按钮组） -->
        <a-row :gutter="24">
          <a-col :span="24">
            <a-form-model-item label="插件唯一标识" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="pluginKey">
              <a-input-group compact>
                <a-input
                  v-model="model.pluginKey"
                  placeholder="插件唯一标识（可手动编辑）"
                  style="width: 70%"
                  :disabled="false"
                  :suffix="checkingUnique ? '检查中...' : ''"
                  :class="{ 'error-input': isDuplicate }"
                  @blur="onPluginKeyBlur"
                  @input="onPluginKeyInput" />
                <a-button
                  @click="regeneratePluginKey"
                  type="primary"
                  style="width: 15%"
                  :loading="generating">
                  重新生成
                </a-button>
                <a-button
                  @click="checkUnique"
                  type="default"
                  style="width: 15%"
                  :loading="checkingUnique">
                  检查重复
                </a-button>
              </a-input-group>

              <!-- 状态提示 -->
              <div style="margin-top: 4px; font-size: 12px;">
                <span v-if="isDuplicate" style="color: #ff4d4f;">
                  <a-icon type="close-circle" /> 标识已存在，请重新生成或手动修改
                </span>
                <span v-else-if="isUnique" style="color: #52c41a;">
                  <a-icon type="check-circle" /> 标识可用
                </span>
                <span v-else-if="model.pluginKey && model.pluginKey.trim()" style="color: #1890ff;">
                  <a-icon type="info-circle" /> 请点击"检查重复"验证标识可用性
                </span>
                <span v-else style="color: #999;">
                  基于插件名称自动生成，也可手动编辑
                </span>
              </div>
            </a-form-model-item>
          </a-col>
        </a-row>

        <!-- 第七行：插件教程视频（禁用）、需要金额 -->
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item label="插件教程视频" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="plubvideo">
              <a-button @click="showVideoUploadTip" disabled style="width: 100%">
                <a-icon type="upload" />
                暂不支持上传文件
              </a-button>
              <div style="margin-top: 8px; color: #999; font-size: 12px;">
                视频上传功能暂时禁用，请使用下方的教程链接字段
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="需要金额" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="neednum">
              <a-input-number v-model="model.neednum" placeholder="请输入需要金额" style="width: 100%" :precision="2" />
            </a-form-model-item>
          </a-col>
        </a-row>

        <!-- 第八行：插件教程链接 -->
        <a-row :gutter="24">
          <a-col :span="24">
            <a-form-model-item label="插件教程链接" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="tutorialLink">
              <a-input
                v-model="model.tutorialLink"
                placeholder="请输入插件教程链接，如：https://www.example.com/tutorial"
                style="width: 100%"
              >
                <a-icon slot="prefix" type="link" />
              </a-input>
              <div style="margin-top: 8px; color: #666; font-size: 12px;">
                <a-icon type="info-circle" style="margin-right: 4px;" />
                支持外部视频链接，如B站、YouTube、腾讯视频等平台链接
              </div>
            </a-form-model-item>
          </a-col>
        </a-row>



        <!-- 第八行：收益金额、调用次数 -->
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-model-item label="收益金额" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="income">
              <a-input-number
                v-model="model.income"
                placeholder="系统自动统计"
                style="width: 100%"
                :precision="2"
                :disabled="true" />
              <div style="color: #999; font-size: 12px; margin-top: 4px;">
                系统根据实际使用情况自动计算
              </div>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="调用次数" :labelCol="labelCol" :wrapperCol="wrapperCol" prop="usernum">
              <a-input-number
                v-model="model.usernum"
                placeholder="系统自动统计"
                style="width: 100%"
                :disabled="true" />
              <div style="color: #999; font-size: 12px; margin-top: 4px;">
                系统根据实际调用自动统计
              </div>
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </j-form-container>
  </a-spin>
</template>

<script>

  import { httpAction, getAction } from '@/api/manage'
  import JImageUploadDeferred from '@/components/jeecg/JImageUploadDeferred'

  export default {
    name: 'AigcPlubShopForm',
    components: {
      JImageUploadDeferred
    },
    props: {
      //表单禁用
      disabled: {
        type: Boolean,
        default: false,
        required: false
      }
    },
    data () {
      return {
        model:{
          usernum: 0,           // 调用次数默认为0
          income: 0,            // 收益金额默认为0
          isCombined: 2,        // 是否组合插件默认为"否"
          combinedName: '',     // 组合插件名默认为空
          combinedDescription: '', // 组合插件介绍默认为空
          combinedImage: ''     // 组合插件图片默认为空
         },
        generating: false,
        checkingUnique: false,
        isDuplicate: false,
        isUnique: false,
        keyCounter: 1,
        checkTimer: null,
        labelCol: {
          xs: { span: 24 },
          sm: { span: 5 },
        },
        wrapperCol: {
          xs: { span: 24 },
          sm: { span: 16 },
        },
        confirmLoading: false,
        // 🔥 智能回填相关变量
        queryTimer: null,              // 防抖定时器
        isAutoFilledDescription: false, // 标记介绍是否为自动回填
        isAutoFilledImage: false,       // 标记图片是否为自动回填
        isAutoFilledAuthor: false,      // 标记创作者是否为自动回填
        isAutoFilledSortOrder: false,   // 标记排序权重是否为自动回填
        foundExistingCombined: false,   // 是否找到现有组合插件
        combinedNameError: '',          // 权限验证错误信息
        validatorRules: {
           plubname: [
              { required: true, message: '请输入插件名称!'},
           ],
           plubwrite: [
              { required: true, message: '请输入插件创作者!'},
           ],
           plubinfo: [
              { required: true, message: '请输入插件介绍!'},
           ],
           plubCategory: [
              { required: true, message: '请选择插件分类!'},
           ],
           status: [
              { required: true, message: '请选择插件状态!'},
           ],
           pluginKey: [
              { required: true, message: '请输入插件唯一标识!'},
              { pattern: /^[a-z0-9_]+$/, message: '插件标识只能包含小写字母、数字和下划线!'},
           ],
           tutorialLink: [
              { pattern: /^https?:\/\/.+/, message: '请输入有效的链接地址，必须以http://或https://开头!'},
           ],
        },
        url: {
          add: "/plubshop/aigcPlubShop/add",
          edit: "/plubshop/aigcPlubShop/edit",
          queryById: "/plubshop/aigcPlubShop/queryById"
        }
      }
    },
    computed: {
      formDisabled(){
        return this.disabled
      },
      isAdmin() {
        // 判断当前用户是否为admin角色（基于role_code）
        const userRole = localStorage.getItem('userRole');
        return userRole && userRole.toLowerCase().includes('admin');
      },
      // 🔥 判断是否为组合插件
      isCombinedPlugin() {
        return this.model.isCombined === '1' || this.model.isCombined === 1;
      }
    },
    watch: {
      // 监听插件名称变化
      'model.plubname': {
        handler(newVal, oldVal) {
          if (newVal && newVal !== oldVal) {
            this.keyCounter = 1; // 重置计数器

            // 🔥 只有在插件标识为空或者是自动生成的情况下才自动生成
            if (!this.model.pluginKey || this.model.pluginKey.trim() === '' || this.isAutoGeneratedKey()) {
              this.generatePluginKey();
            }

            // 非管理员用户设置默认值
            if (!this.isAdmin) {
              this.model.status = 2; // 审核中
              this.setDefaultSortOrder(); // 自动计算排序权重
            }
          }
        },
        immediate: true
      },

      // 监听标识变化，自动检查重复
      'model.pluginKey': {
        handler(newVal) {
          if (newVal) {
            this.debounceCheckUnique();
          }
        }
      },

      // 🔥 监听是否组合插件变化
      'model.isCombined': {
        handler(newVal) {
          // 如果选择"否"（值为2），清空组合插件相关字段
          if (newVal === '2' || newVal === 2) {
            this.model.combinedName = '';
            this.model.combinedDescription = '';
          }
        },
        immediate: true
      },

      // 🔥 监听组合插件名变化，实现智能回填
      'model.combinedName': {
        handler(newVal, oldVal) {
          // 只有在组合插件模式下才进行查询
          if (this.isCombinedPlugin && newVal && newVal.trim() && newVal !== oldVal) {
            // 防抖查询
            this.debounceQueryCombinedPlugin();
          } else if (!newVal || !newVal.trim()) {
            // 清空时也清空自动回填的字段
            this.clearAutoFilledFields();
            this.combinedNameError = '';
            this.foundExistingCombined = false;
          }
        }
      }
    },
    created () {
       //备份model原始值
      this.modelDefault = JSON.parse(JSON.stringify(this.model));
    },
    methods: {
      add () {
        this.edit(this.modelDefault);
      },
      edit (record) {
        this.model = Object.assign({}, record);
        this.visible = true;
      },

      // 生成插件标识
      async generatePluginKey() {
        if (!this.model.plubname) return;

        this.generating = true;
        this.isDuplicate = false;
        this.isUnique = false;

        try {
          const baseKey = this.createBaseKey(this.model.plubname);
          let finalKey = baseKey;

          // 如果是第一次生成，直接使用基础key
          if (this.keyCounter > 1) {
            finalKey = `${baseKey}_${this.keyCounter}`;
          }

          this.model.pluginKey = finalKey;

          // 自动检查唯一性
          await this.checkUnique();

        } finally {
          this.generating = false;
        }
      },

      // 重新生成（递增版本）
      async regeneratePluginKey() {
        if (!this.model.plubname) {
          this.$message.warning('请先输入插件名称');
          return;
        }

        this.keyCounter++;
        await this.generatePluginKey();
      },

      // 创建基础key
      createBaseKey(pluginName) {
        return pluginName
          .toLowerCase()
          .replace(/[\u4e00-\u9fa5]/g, (char) => {
            // 扩展的中文转拼音映射表
            const pinyinMap = {
              // 扣子智能体平台相关
              '扣': 'kou', '子': 'zi', '智': 'zhi', '能': 'neng', '体': 'ti',
              '插': 'cha', '件': 'jian', '机': 'ji', '器': 'qi', '人': 'ren',
              '助': 'zhu', '手': 'shou', '客': 'ke', '服': 'fu', '聊': 'liao',
              '天': 'tian', '对': 'dui', '话': 'hua', '问': 'wen', '答': 'da',

              // 主流平台
              '抖': 'dou', '音': 'yin', '快': 'kuai', '手': 'shou', '小': 'xiao',
              '红': 'hong', '书': 'shu', '微': 'wei', '信': 'xin', '博': 'bo',
              '微': 'wei', '博': 'bo', '知': 'zhi', '乎': 'hu', '豆': 'dou',
              '瓣': 'ban', '贴': 'tie', '吧': 'ba', '论': 'lun', '坛': 'tan',
              '社': 'she', '区': 'qu', '群': 'qun', '组': 'zu', '圈': 'quan',

              // 办公平台
              '飞': 'fei', '书': 'shu', '钉': 'ding', '钉': 'ding', '企': 'qi',
              '微': 'wei', '腾': 'teng', '讯': 'xun', '会': 'hui', '议': 'yi',
              '文': 'wen', '档': 'dang', '表': 'biao', '格': 'ge', '演': 'yan',
              '示': 'shi', '邮': 'you', '件': 'jian', '日': 'ri', '历': 'li',
              '任': 'ren', '务': 'wu', '项': 'xiang', '目': 'mu', '团': 'tuan',
              '队': 'dui', '协': 'xie', '作': 'zuo', '办': 'ban', '公': 'gong',

              // AI相关
              'A': 'a', 'I': 'i', '人': 'ren', '工': 'gong', '智': 'zhi',
              '能': 'neng', '学': 'xue', '习': 'xi', '深': 'shen', '度': 'du',
              '神': 'shen', '经': 'jing', '网': 'wang', '络': 'luo', '算': 'suan',
              '法': 'fa', '模': 'mo', '型': 'xing', '训': 'xun', '练': 'lian',
              '预': 'yu', '测': 'ce', '识': 'shi', '别': 'bie', '检': 'jian',
              '测': 'ce', '分': 'fen', '析': 'xi', '处': 'chu', '理': 'li',
              '自': 'zi', '动': 'dong', '化': 'hua', '优': 'you', '化': 'hua',

              // 内容创作
              '创': 'chuang', '作': 'zuo', '编': 'bian', '辑': 'ji', '写': 'xie',
              '作': 'zuo', '文': 'wen', '章': 'zhang', '标': 'biao', '题': 'ti',
              '摘': 'zhai', '要': 'yao', '总': 'zong', '结': 'jie', '翻': 'fan',
              '译': 'yi', '润': 'run', '色': 'se', '校': 'jiao', '对': 'dui',
              '审': 'shen', '核': 'he', '发': 'fa', '布': 'bu', '推': 'tui',
              '广': 'guang', '营': 'ying', '销': 'xiao', '宣': 'xuan', '传': 'chuan',

              // 媒体处理
              '图': 'tu', '片': 'pian', '照': 'zhao', '片': 'pian', '画': 'hua',
              '像': 'xiang', '视': 'shi', '频': 'pin', '音': 'yin', '频': 'pin',
              '声': 'sheng', '音': 'yin', '录': 'lu', '制': 'zhi', '剪': 'jian',
              '辑': 'ji', '合': 'he', '成': 'cheng', '特': 'te', '效': 'xiao',
              '滤': 'lv', '镜': 'jing', '美': 'mei', '颜': 'yan', '修': 'xiu',
              '图': 'tu', '水': 'shui', '印': 'yin', '压': 'ya', '缩': 'suo',

              // 电商相关
              '淘': 'tao', '宝': 'bao', '天': 'tian', '猫': 'mao', '京': 'jing',
              '东': 'dong', '拼': 'pin', '多': 'duo', '多': 'duo', '苏': 'su',
              '宁': 'ning', '唯': 'wei', '品': 'pin', '会': 'hui', '商': 'shang',
              '品': 'pin', '店': 'dian', '铺': 'pu', '货': 'huo', '物': 'wu',
              '订': 'ding', '单': 'dan', '支': 'zhi', '付': 'fu', '配': 'pei',
              '送': 'song', '物': 'wu', '流': 'liu', '退': 'tui', '换': 'huan',

              // 原有基础字
              '大': 'da', '师': 'shi', '打': 'da', '洞': 'dong', '可': 'ke',
              '口': 'kou', '乐': 'le', '生': 'sheng', '成': 'cheng',

              // 工具和功能相关
              '工': 'gong', '具': 'ju', '功': 'gong', '能': 'neng', '应': 'ying',
              '用': 'yong', '软': 'ruan', '件': 'jian', '程': 'cheng', '序': 'xu',
              '系': 'xi', '统': 'tong', '平': 'ping', '台': 'tai', '框': 'kuang',
              '架': 'jia', '库': 'ku', '包': 'bao', '模': 'mo', '块': 'kuai',
              '组': 'zu', '件': 'jian', '接': 'jie', '口': 'kou', '服': 'fu',
              '务': 'wu', '端': 'duan', '前': 'qian', '后': 'hou', '全': 'quan',
              '栈': 'zhan', '开': 'kai', '发': 'fa', '设': 'she', '计': 'ji',
              '实': 'shi', '现': 'xian', '部': 'bu', '署': 'shu', '运': 'yun',
              '维': 'wei', '测': 'ce', '试': 'shi', '调': 'tiao', '试': 'shi',

              // 数据和存储
              '数': 'shu', '据': 'ju', '库': 'ku', '表': 'biao', '字': 'zi',
              '段': 'duan', '索': 'suo', '引': 'yin', '查': 'cha', '询': 'xun',
              '增': 'zeng', '删': 'shan', '改': 'gai', '查': 'cha', '存': 'cun',
              '储': 'chu', '备': 'bei', '份': 'fen', '恢': 'hui', '复': 'fu',
              '同': 'tong', '步': 'bu', '异': 'yi', '步': 'bu', '缓': 'huan',
              '存': 'cun', '内': 'nei', '存': 'cun', '磁': 'ci', '盘': 'pan',

              // 网络和通信
              '网': 'wang', '络': 'luo', '互': 'hu', '联': 'lian', '网': 'wang',
              '协': 'xie', '议': 'yi', '传': 'chuan', '输': 'shu', '通': 'tong',
              '信': 'xin', '连': 'lian', '接': 'jie', '请': 'qing', '求': 'qiu',
              '响': 'xiang', '应': 'ying', '状': 'zhuang', '态': 'tai', '码': 'ma',
              '头': 'tou', '部': 'bu', '体': 'ti', '参': 'can', '数': 'shu',

              // 安全和权限
              '安': 'an', '全': 'quan', '权': 'quan', '限': 'xian', '角': 'jiao',
              '色': 'se', '用': 'yong', '户': 'hu', '认': 'ren', '证': 'zheng',
              '授': 'shou', '权': 'quan', '登': 'deng', '录': 'lu', '注': 'zhu',
              '册': 'ce', '退': 'tui', '出': 'chu', '密': 'mi', '码': 'ma',
              '加': 'jia', '密': 'mi', '解': 'jie', '密': 'mi', '签': 'qian',
              '名': 'ming', '验': 'yan', '证': 'zheng', '令': 'ling', '牌': 'pai',

              // 界面和交互
              '界': 'jie', '面': 'mian', '页': 'ye', '面': 'mian', '窗': 'chuang',
              '口': 'kou', '对': 'dui', '话': 'hua', '框': 'kuang', '按': 'an',
              '钮': 'niu', '链': 'lian', '接': 'jie', '菜': 'cai', '单': 'dan',
              '导': 'dao', '航': 'hang', '标': 'biao', '签': 'qian', '选': 'xuan',
              '项': 'xiang', '卡': 'ka', '片': 'pian', '列': 'lie', '表': 'biao',
              '表': 'biao', '格': 'ge', '表': 'biao', '单': 'dan', '输': 'shu',
              '入': 'ru', '框': 'kuang', '下': 'xia', '拉': 'la', '框': 'kuang',
              '复': 'fu', '选': 'xuan', '框': 'kuang', '单': 'dan', '选': 'xuan',
              '按': 'an', '钮': 'niu', '滑': 'hua', '块': 'kuai', '进': 'jin',
              '度': 'du', '条': 'tiao', '加': 'jia', '载': 'zai', '刷': 'shua',
              '新': 'xin', '更': 'geng', '新': 'xin', '提': 'ti', '示': 'shi',
              '消': 'xiao', '息': 'xi', '通': 'tong', '知': 'zhi', '警': 'jing',
              '告': 'gao', '确': 'que', '认': 'ren', '取': 'qu', '消': 'xiao'
            };
            return pinyinMap[char] || char.charCodeAt(0).toString(36);
          })
          .replace(/[^a-z0-9]/g, '_')
          .replace(/_+/g, '_')
          .replace(/^_|_$/g, '');
      },
      // 检查唯一性
      async checkUnique() {
        if (!this.model.pluginKey) return;

        this.checkingUnique = true;
        this.isDuplicate = false;
        this.isUnique = false;

        try {
          const response = await getAction('/plubshop/aigcPlubShop/checkPluginKey', {
            pluginKey: this.model.pluginKey,
            excludeId: this.model.id // 编辑时排除自己
          });

          if (response.success) {
            this.isDuplicate = response.result.exists;
            this.isUnique = !response.result.exists;

            // 如果重复，自动递增重新生成
            if (this.isDuplicate) {
              this.$message.warning('标识已存在，正在自动生成新的标识...');
              setTimeout(() => {
                this.regeneratePluginKey();
              }, 1000);
            }
          }
        } catch (error) {
          this.$message.error('检查标识唯一性失败');
        } finally {
          this.checkingUnique = false;
        }
      },

      // 防抖检查
      debounceCheckUnique() {
        clearTimeout(this.checkTimer);
        this.checkTimer = setTimeout(() => {
          this.checkUnique();
        }, 500);
      },

      // 🔥 处理用户手动输入插件标识
      onPluginKeyInput(value) {
        // 实时格式化：只允许小写字母、数字和下划线
        const formattedValue = value
          .toLowerCase()
          .replace(/[^a-z0-9_]/g, '');

        if (formattedValue !== value) {
          this.model.pluginKey = formattedValue;
        }

        // 重置状态
        this.isDuplicate = false;
        this.isUnique = false;

        // 防抖检查重复
        if (formattedValue && formattedValue.trim()) {
          this.debounceCheckUnique();
        }
      },

      // 🔥 处理插件标识失去焦点
      onPluginKeyBlur() {
        if (this.model.pluginKey && this.model.pluginKey.trim()) {
          // 失去焦点时立即检查重复
          this.checkUnique();
        }
      },

      // 🔥 判断当前插件标识是否为自动生成的
      isAutoGeneratedKey() {
        if (!this.model.pluginKey || !this.model.plubname) {
          return true;
        }

        // 生成基础key
        const baseKey = this.createBaseKey(this.model.plubname);

        // 检查是否匹配自动生成的模式
        return this.model.pluginKey === baseKey ||
               this.model.pluginKey.startsWith(baseKey + '_');
      },

      // 设置默认排序权重（非管理员用户）
      async setDefaultSortOrder() {
        try {
          const response = await getAction('/plubshop/aigcPlubShop/getMaxSortOrder');
          this.model.sortOrder = (response.result || 0) + 1;
        } catch (error) {
          console.error('获取最大排序权重失败', error);
          this.model.sortOrder = 1;
        }
      },

      // 处理排序权重变化（管理员用户）
      async handleSortOrderChange(newSortOrder) {
        if (this.isAdmin && newSortOrder && newSortOrder > 0) {
          try {
            console.log('🎯 handleSortOrderChange - 检查权重:', newSortOrder, '类型:', typeof newSortOrder);

            // 检查权重是否冲突
            const response = await getAction('/plubshop/aigcPlubShop/checkSortOrderConflict', {
              sortOrder: parseInt(newSortOrder), // 确保传递整数
              excludeId: this.model.id || ''
            });

            if (response.success && response.result.hasConflict) {
              // 提示用户将调整其他插件权重
              this.$confirm({
                title: '权重冲突',
                content: `权重 ${newSortOrder} 已存在，是否将现有插件权重后移？`,
                onOk: () => {
                  this.adjustSortOrder(newSortOrder);
                },
                onCancel: () => {
                  // 取消时恢复原值
                  this.setDefaultSortOrder();
                }
              });
            }
          } catch (error) {
            console.error('检查排序权重冲突失败', error);
          }
        }
      },

      // 调整排序权重冲突
      async adjustSortOrder(targetOrder) {
        try {
          console.log('🎯 adjustSortOrder - 调整权重:', targetOrder, '类型:', typeof targetOrder);

          await httpAction('/plubshop/aigcPlubShop/adjustSortOrder', {
            targetOrder: parseInt(targetOrder), // 确保传递整数
            excludeId: this.model.id || ''
          }, 'post');

          this.$message.success('权重调整成功');
        } catch (error) {
          this.$message.error('权重调整失败');
          console.error('调整排序权重失败', error);
        }
      },

      // 🔥 防抖查询组合插件
      debounceQueryCombinedPlugin() {
        clearTimeout(this.queryTimer);
        this.queryTimer = setTimeout(() => {
          this.queryCombinedPluginInfo();
        }, 500); // 500ms防抖
      },

      // 🔥 查询组合插件信息（包含权限验证）
      async queryCombinedPluginInfo() {
        try {
          const response = await getAction('/plubshop/aigcPlubShop/validateAndQueryCombinedPlugin', {
            combinedName: this.model.combinedName,
            excludeId: this.model.id || ''
          });

          if (response.success && response.result) {
            const result = response.result;

            // 🔥 权限验证
            if (!result.hasPermission) {
              this.combinedNameError = result.message;
              this.foundExistingCombined = false;
              this.clearAutoFilledFields();
              return;
            }

            // 清除错误提示
            this.combinedNameError = '';

            if (result.foundExisting) {
              let autoFilledFields = [];

              // 🔥 回填组合插件介绍
              if (!this.model.combinedDescription ||
                  this.model.combinedDescription.trim() === '' ||
                  this.isAutoFilledDescription) {

                this.model.combinedDescription = result.combinedDescription;
                this.isAutoFilledDescription = true;
                autoFilledFields.push('组合插件介绍');
              }

              // 🔥 回填组合插件图片
              if (!this.model.combinedImage ||
                  this.model.combinedImage.trim() === '' ||
                  this.isAutoFilledImage) {

                this.model.combinedImage = result.combinedImage;
                this.isAutoFilledImage = true;
                autoFilledFields.push('组合插件图片');
              }

              // 🔥 回填插件创作者
              if (!this.model.plubwrite ||
                  this.model.plubwrite.trim() === '' ||
                  this.isAutoFilledAuthor) {

                this.model.plubwrite = result.plubwrite;
                this.isAutoFilledAuthor = true;
                autoFilledFields.push('插件创作者');
              }

              // 🔥 回填排序权重
              if (!this.model.sortOrder ||
                  this.model.sortOrder === 0 ||
                  this.isAutoFilledSortOrder) {

                this.model.sortOrder = result.sortOrder;
                this.isAutoFilledSortOrder = true;
                autoFilledFields.push('排序权重');
              }

              this.foundExistingCombined = true;

              if (autoFilledFields.length > 0) {
                this.$message.info(`已自动回填"${this.model.combinedName}"的${autoFilledFields.join('、')}`);
              }
            } else {
              this.foundExistingCombined = false;
              this.clearAutoFilledFlags();
            }
          }
        } catch (error) {
          console.log('查询组合插件信息失败:', error);
          this.combinedNameError = '查询失败，请重试';
          this.foundExistingCombined = false;
        }
      },

      // 🔥 清除自动回填的字段
      clearAutoFilledFields() {
        if (this.isAutoFilledDescription) {
          this.model.combinedDescription = '';
        }
        if (this.isAutoFilledImage) {
          this.model.combinedImage = '';
        }
        if (this.isAutoFilledAuthor) {
          this.model.plubwrite = '';
        }
        if (this.isAutoFilledSortOrder) {
          this.model.sortOrder = null;
        }
        this.clearAutoFilledFlags();
      },

      // 🔥 清除自动回填标记
      clearAutoFilledFlags() {
        this.isAutoFilledDescription = false;
        this.isAutoFilledImage = false;
        this.isAutoFilledAuthor = false;
        this.isAutoFilledSortOrder = false;
      },

      // 🔥 检查相同组合插件数量
      async checkCombinedPluginCount() {
        try {
          if (!this.model.combinedName || !this.isCombinedPlugin) {
            return 0;
          }

          const response = await getAction('/plubshop/aigcPlubShop/list', {
            combinedName: this.model.combinedName,
            isCombined: 1,
            pageNo: 1,
            pageSize: 1000 // 获取所有匹配的记录
          });

          if (response.success && response.result && response.result.records) {
            // 排除当前编辑的记录
            const excludeId = this.model.id;
            const matchingPlugins = response.result.records.filter(plugin =>
              plugin.id !== excludeId && plugin.combinedName === this.model.combinedName
            );
            return matchingPlugins.length;
          }
          return 0;
        } catch (error) {
          console.log('检查组合插件数量失败:', error);
          return 0;
        }
      },

      async submitForm () {
        const that = this;

        // 🔥 保存前检查是否会影响其他插件
        if (this.model.isCombined === 1 && this.model.combinedName && this.model.combinedDescription) {
          try {
            const count = await this.checkCombinedPluginCount();
            if (count > 0) {
              // 显示确认对话框
              const confirmed = await new Promise((resolve) => {
                this.$confirm({
                  title: '确认保存',
                  content: `检测到还有 ${count} 个插件使用相同的组合插件名"${this.model.combinedName}"，保存后将同步更新它们的介绍。是否继续？`,
                  okText: '确认保存',
                  cancelText: '取消',
                  onOk: () => resolve(true),
                  onCancel: () => resolve(false)
                });
              });

              if (!confirmed) {
                return; // 用户取消，不继续保存
              }
            }
          } catch (error) {
            console.log('检查组合插件数量失败:', error);
          }
        }

        // 触发表单验证
        this.$refs.form.validate(async (valid) => {
          if (valid) {
            that.confirmLoading = true;

            try {
              // 🔥 先上传待上传的图片
              await this.uploadPendingImages();

              let httpurl = '';
              let method = '';
              if(!this.model.id){
                httpurl+=this.url.add;
                method = 'post';
              }else{
                httpurl+=this.url.edit;
                 method = 'put';
              }

              const res = await httpAction(httpurl, this.model, method);
              if(res.success){
                // 🔥 保存成功后，确认删除被替换的原始文件
                await this.confirmDeleteOriginalFiles();

                that.$message.success(res.message);
                that.$emit('ok');
              }else{
                that.$message.warning(res.message);
              }

            } catch (error) {
              console.error('保存失败:', error);
              that.$message.error('保存失败: ' + (error.message || '未知错误'));
            } finally {
              that.confirmLoading = false;
            }
          }
        })
      },

      // 🔥 上传待上传的图片
      async uploadPendingImages() {
        console.log('🎯 AigcPlubShopForm: 开始检查待上传图片...');

        // 处理插件图片
        if (this.$refs.pluginImageUpload) {
          if (this.$refs.pluginImageUpload.hasPendingFiles()) {
            console.log('🎯 AigcPlubShopForm: 发现待上传的插件图片，开始上传...');
            try {
              const uploadedFiles = await this.$refs.pluginImageUpload.performUpload();
              console.log('🎯 AigcPlubShopForm: 插件图片上传结果:', uploadedFiles);

              // 上传完成后，组件内部会自动更新fileList和调用emitChange
              // 我们需要从组件获取最终的值
              const finalValue = this.$refs.pluginImageUpload.getCurrentValue();
              this.model.plubimg = finalValue;
              console.log('🎯 AigcPlubShopForm: 插件图片最终值:', this.model.plubimg);
            } catch (error) {
              console.error('🎯 AigcPlubShopForm: 插件图片上传失败:', error);
              throw error;
            }
          } else {
            // 没有待上传文件，检查是否有删除操作
            const currentValue = this.$refs.pluginImageUpload.getCurrentValue();
            this.model.plubimg = currentValue;
            console.log('🎯 AigcPlubShopForm: 插件图片无新上传，当前值:', this.model.plubimg);
          }
        }

        // 处理组合插件图片
        if (this.$refs.combinedImageUpload) {
          if (this.$refs.combinedImageUpload.hasPendingFiles()) {
            console.log('🎯 AigcPlubShopForm: 发现待上传的组合插件图片，开始上传...');
            try {
              const uploadedFiles = await this.$refs.combinedImageUpload.performUpload();
              console.log('🎯 AigcPlubShopForm: 组合插件图片上传结果:', uploadedFiles);

              const finalValue = this.$refs.combinedImageUpload.getCurrentValue();
              this.model.combinedImage = finalValue;
              console.log('🎯 AigcPlubShopForm: 组合插件图片最终值:', this.model.combinedImage);
            } catch (error) {
              console.error('🎯 AigcPlubShopForm: 组合插件图片上传失败:', error);
              throw error;
            }
          } else {
            // 没有待上传文件，检查是否有删除操作
            const currentValue = this.$refs.combinedImageUpload.getCurrentValue();
            this.model.combinedImage = currentValue;
            console.log('🎯 AigcPlubShopForm: 组合插件图片无新上传，当前值:', this.model.combinedImage);
          }
        }

        console.log('🎯 AigcPlubShopForm: 图片处理完成');
      },

      // 🔥 确认删除被替换的原始文件
      async confirmDeleteOriginalFiles() {
        const deleteTasks = [];

        // 确认删除插件图片的原始文件
        if (this.$refs.pluginImageUpload) {
          deleteTasks.push(this.$refs.pluginImageUpload.confirmDeleteOriginalFiles());
        }

        // 确认删除组合插件图片的原始文件
        if (this.$refs.combinedImageUpload) {
          deleteTasks.push(this.$refs.combinedImageUpload.confirmDeleteOriginalFiles());
        }

        // 等待所有删除任务完成
        if (deleteTasks.length > 0) {
          try {
            await Promise.all(deleteTasks);
            console.log('🎯 AigcPlubShopForm: 原始文件清理完成');
          } catch (error) {
            console.warn('🎯 AigcPlubShopForm: 原始文件清理失败:', error);
            // 删除失败不影响主流程
          }
        }
      },

      // 🔥 关闭表单时回滚图片变更
      handleClose() {
        console.log('🎯 AigcPlubShopForm: 表单关闭，回滚图片变更')

        // 回滚插件图片变更
        if (this.$refs.pluginImageUpload) {
          this.$refs.pluginImageUpload.rollbackChanges()
        }

        // 回滚组合插件图片变更
        if (this.$refs.combinedImageUpload) {
          this.$refs.combinedImageUpload.rollbackChanges()
        }

        // 发出关闭事件
        this.$emit('close')
      },

      // 显示视频上传提示
      showVideoUploadTip() {
        this.$message.info('暂不支持上传文件，请使用教程链接字段输入外部视频链接');
      },
    }
  }
</script>

<style scoped>
.error-input {
  border-color: #ff4d4f !important;
  box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.2) !important;
}
</style>