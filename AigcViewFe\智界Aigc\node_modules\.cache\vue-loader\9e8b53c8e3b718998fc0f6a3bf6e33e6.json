{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\workflow\\components\\CreatorAgentForm.vue?vue&type=style&index=0&id=365d0b54&lang=less&scoped=true&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\workflow\\components\\CreatorAgentForm.vue", "mtime": 1754512791552}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\css-loader\\index.js", "mtime": 1749979994548}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\less-loader\\dist\\cjs.js", "mtime": 1749980456032}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// 上传提示\n.upload-tips {\n  margin-top: 8px;\n\n  p {\n    margin: 0;\n    font-size: 12px;\n    color: #666;\n    line-height: 1.4;\n  }\n\n  .tip-note {\n    color: #1890ff;\n    font-weight: 500;\n    margin-top: 4px;\n  }\n}\n\n// 字段提示\n.field-tips {\n  margin-top: 4px;\n  font-size: 12px;\n  color: #999;\n  line-height: 1.4;\n}\n\n// 表单通知\n.form-notice {\n  margin-top: 24px;\n\n  .ant-alert {\n    border-radius: 6px;\n\n    .ant-alert-message {\n      font-weight: 600;\n    }\n\n    .ant-alert-description {\n      margin-top: 4px;\n      line-height: 1.5;\n    }\n  }\n}\n\n// 表单样式优化\n.ant-form-model {\n  .ant-form-model-item {\n    margin-bottom: 24px;\n\n    .ant-form-model-item-label {\n      label {\n        font-weight: 600;\n        color: #1f2937;\n\n        &::after {\n          content: '';\n        }\n      }\n    }\n\n    .ant-input,\n    .ant-input-number,\n    .ant-select-selection,\n    .ant-input-number-input {\n      border-radius: 6px;\n      border-color: #d1d5db;\n\n      &:focus,\n      &:hover {\n        border-color: #1890ff;\n      }\n    }\n\n    .ant-textarea {\n      border-radius: 6px;\n      border-color: #d1d5db;\n\n      &:focus,\n      &:hover {\n        border-color: #1890ff;\n      }\n    }\n\n    .ant-input-number {\n      width: 100%;\n\n      .ant-input-number-input {\n        border: none;\n      }\n    }\n  }\n}\n\n// 模态框样式\n.ant-modal {\n  .ant-modal-header {\n    border-radius: 8px 8px 0 0;\n\n    .ant-modal-title {\n      font-weight: 600;\n      color: #1f2937;\n    }\n  }\n\n  .ant-modal-body {\n    padding: 24px;\n  }\n\n  .ant-modal-footer {\n    border-radius: 0 0 8px 8px;\n\n    .ant-btn {\n      border-radius: 6px;\n\n      &.ant-btn-primary {\n        background: #1890ff;\n        border-color: #1890ff;\n\n        &:hover {\n          background: #40a9ff;\n          border-color: #40a9ff;\n        }\n      }\n    }\n  }\n}\n\n// 响应式设计\n@media (max-width: 768px) {\n  .ant-modal {\n    margin: 0;\n    max-width: 100vw;\n\n    .ant-modal-content {\n      border-radius: 0;\n    }\n  }\n\n  .ant-form-model {\n    .ant-form-model-item {\n      .ant-form-model-item-label {\n        text-align: left;\n      }\n    }\n  }\n}\n\n// 弹窗样式\n:global(.creator-agent-modal) {\n  .ant-modal-header {\n    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n    border-bottom: none;\n    border-radius: 12px 12px 0 0;\n\n    .ant-modal-title {\n      color: white;\n      font-weight: 600;\n      font-size: 20px;\n    }\n  }\n\n  .ant-modal-close {\n    color: white;\n\n    &:hover {\n      color: rgba(255, 255, 255, 0.8);\n    }\n  }\n\n  .ant-modal-body {\n    padding: 0;\n    max-height: 80vh;\n    overflow: hidden;\n  }\n\n  .ant-modal-content {\n    border-radius: 12px;\n    overflow: hidden;\n    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);\n  }\n}\n\n// 步骤条样式\n.step-header {\n  padding: 24px 32px;\n  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);\n  border-bottom: 1px solid #e2e8f0;\n\n  .custom-steps {\n    :global(.ant-steps-item-title) {\n      font-weight: 600;\n      font-size: 16px;\n    }\n\n    :global(.ant-steps-item-description) {\n      color: #64748b;\n    }\n\n    :global(.ant-steps-item-process .ant-steps-item-icon) {\n      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n      border-color: #667eea;\n    }\n\n    :global(.ant-steps-item-finish .ant-steps-item-icon) {\n      background: #10b981;\n      border-color: #10b981;\n    }\n  }\n}\n\n// 步骤内容\n.step-content {\n  min-height: 500px;\n  max-height: 60vh;\n  overflow-y: auto;\n}\n\n.step-panel {\n  padding: 32px;\n\n  .panel-header {\n    text-align: center;\n    margin-bottom: 32px;\n\n    .panel-title {\n      font-size: 24px;\n      font-weight: 700;\n      color: #1e293b;\n      margin: 0 0 8px 0;\n\n      .anticon {\n        margin-right: 12px;\n        color: #667eea;\n      }\n    }\n\n    .panel-desc {\n      font-size: 16px;\n      color: #64748b;\n      margin: 0;\n    }\n  }\n}\n\n// 现代化表单样式\n.modern-form {\n  .form-card {\n    background: white;\n    border-radius: 12px;\n    padding: 24px;\n    margin-bottom: 20px;\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\n    border: 1px solid #e2e8f0;\n    transition: all 0.3s ease;\n\n    &:hover {\n      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);\n      border-color: #cbd5e1;\n    }\n\n    .field-label {\n      font-size: 16px;\n      font-weight: 600;\n      color: #1e293b;\n      margin-bottom: 12px;\n      display: flex;\n      align-items: center;\n\n      .anticon {\n        margin-right: 8px;\n        color: #667eea;\n        font-size: 18px;\n      }\n\n      .optional {\n        font-size: 14px;\n        font-weight: 400;\n        color: #94a3b8;\n        margin-left: 8px;\n      }\n\n      // 🔥 必填星号样式\n      .required-star {\n        color: #ff4d4f;\n        font-size: 16px;\n        font-weight: 600;\n        margin-left: 4px;\n      }\n    }\n\n    .modern-input,\n    .modern-textarea,\n    .modern-input-number {\n      border-radius: 8px;\n      border: 2px solid #e2e8f0;\n      transition: all 0.3s ease;\n\n      &:hover {\n        border-color: #cbd5e1;\n      }\n\n      &:focus,\n      &.ant-input-focused {\n        border-color: #667eea;\n        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);\n      }\n    }\n\n    .modern-input-number {\n      width: 100%;\n\n      :global(.ant-input-number-input) {\n        border: none;\n        border-radius: 6px;\n      }\n    }\n\n    .field-tips {\n      margin-top: 8px;\n      font-size: 14px;\n      color: #64748b;\n      line-height: 1.5;\n    }\n  }\n}\n\n// 步骤底部按钮\n.step-footer {\n  padding: 24px 32px;\n  border-top: 1px solid #e2e8f0;\n  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n\n  .ant-btn {\n    height: 44px;\n    padding: 0 24px;\n    font-size: 16px;\n    font-weight: 500;\n    border-radius: 8px;\n    transition: all 0.3s ease;\n\n    &:not(.ant-btn-primary) {\n      border: 2px solid #e2e8f0;\n      color: #64748b;\n\n      &:hover {\n        border-color: #cbd5e1;\n        color: #475569;\n      }\n    }\n  }\n\n  .step-actions {\n    display: flex;\n    gap: 12px;\n    align-items: center;\n  }\n\n  .next-btn,\n  .complete-btn {\n    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n    border: none;\n    color: white;\n\n    &:hover {\n      background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);\n      transform: translateY(-1px);\n      box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);\n    }\n\n    .anticon {\n      margin-left: 8px;\n    }\n  }\n}\n\n// 已创建智能体信息\n.created-agent-info {\n  margin-bottom: 24px;\n\n  .agent-summary {\n    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);\n    border: 2px solid #0ea5e9;\n    border-radius: 12px;\n    padding: 20px;\n    display: flex;\n    align-items: center;\n    gap: 16px;\n\n    .agent-avatar {\n      width: 60px;\n      height: 60px;\n      border-radius: 12px;\n      overflow: hidden;\n      background: white;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      border: 2px solid #0ea5e9;\n\n      img {\n        width: 100%;\n        height: 100%;\n        object-fit: cover;\n      }\n\n      .anticon {\n        font-size: 24px;\n        color: #0ea5e9;\n      }\n    }\n\n    .agent-details {\n      flex: 1;\n\n      h4 {\n        margin: 0 0 4px 0;\n        font-size: 18px;\n        font-weight: 700;\n        color: #0c4a6e;\n      }\n\n      p {\n        margin: 0 0 8px 0;\n        font-size: 14px;\n        color: #0369a1;\n        line-height: 1.4;\n      }\n\n      .agent-price {\n        font-size: 16px;\n        font-weight: 600;\n        color: #059669;\n        background: rgba(5, 150, 105, 0.1);\n        padding: 4px 8px;\n        border-radius: 6px;\n      }\n    }\n  }\n}\n\n// 工作流管理区域\n.workflow-section {\n  border: 1px solid #e8eaec;\n  border-radius: 8px;\n  padding: 16px;\n  background: #fafbfc;\n\n  .workflow-notice {\n    margin-bottom: 16px;\n  }\n\n  .workflow-form {\n    .form-header {\n      margin-bottom: 24px;\n      text-align: center;\n\n      h3 {\n        margin: 0 0 8px 0;\n        font-size: 18px;\n        font-weight: 600;\n        color: #333;\n\n        .anticon {\n          margin-right: 8px;\n          color: #1890ff;\n        }\n      }\n\n      p {\n        margin: 0;\n        font-size: 14px;\n        color: #666;\n      }\n    }\n\n    .ant-form-item {\n      margin-bottom: 20px;\n    }\n\n    .upload-tip {\n      margin-top: 8px;\n      padding: 8px 12px;\n      font-size: 12px;\n      color: #666;\n      background: #fff7e6;\n      border: 1px solid #ffd591;\n      border-radius: 4px;\n      line-height: 1.4;\n\n      .anticon {\n        margin-right: 4px;\n      }\n\n      strong {\n        color: #fa8c16;\n      }\n    }\n  }\n\n  .add-next-workflow {\n    margin-top: 20px;\n    padding-top: 16px;\n    border-top: 1px dashed #e8eaec;\n\n    .add-next-btn {\n      height: 48px;\n      border: 2px dashed #52c41a;\n      color: #52c41a;\n      font-size: 14px;\n      font-weight: 500;\n      transition: all 0.3s;\n\n      &:hover {\n        border-color: #389e0d;\n        color: #389e0d;\n        background: #f6ffed;\n      }\n\n      .anticon {\n        margin-right: 8px;\n      }\n    }\n  }\n\n  .workflow-header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    margin-bottom: 16px;\n\n    .workflow-title {\n      font-size: 14px;\n      font-weight: 600;\n      color: #333;\n\n      .anticon {\n        margin-right: 6px;\n        color: #1890ff;\n      }\n    }\n  }\n\n  .workflow-list {\n    .workflow-item {\n      background: white;\n      border: 1px solid #e8eaec;\n      border-radius: 6px;\n      padding: 12px;\n      margin-bottom: 8px;\n      display: flex;\n      justify-content: space-between;\n      align-items: flex-start;\n      transition: all 0.3s ease;\n\n      &:hover {\n        border-color: #1890ff;\n        box-shadow: 0 2px 8px rgba(24, 144, 255, 0.1);\n      }\n\n      &:last-child {\n        margin-bottom: 0;\n      }\n\n      &.pending {\n        border-color: #faad14;\n        background: #fffbe6;\n\n        &:hover {\n          border-color: #faad14;\n          box-shadow: 0 2px 8px rgba(250, 173, 20, 0.2);\n        }\n      }\n\n      .workflow-info {\n        flex: 1;\n\n        .workflow-name {\n          margin: 0 0 4px 0;\n          font-size: 14px;\n          font-weight: 600;\n          color: #333;\n        }\n\n        .workflow-desc {\n          margin: 0 0 4px 0;\n          font-size: 12px;\n          color: #666;\n          line-height: 1.4;\n        }\n\n        .workflow-time {\n          font-size: 11px;\n          color: #999;\n        }\n\n        .workflow-status {\n          font-size: 11px;\n          color: #faad14;\n          background: #fff7e6;\n          padding: 2px 6px;\n          border-radius: 4px;\n          border: 1px solid #ffd591;\n        }\n      }\n\n      .workflow-actions {\n        display: flex;\n        gap: 8px;\n\n        .ant-btn {\n          padding: 4px 8px;\n          height: auto;\n          font-size: 12px;\n        }\n      }\n    }\n  }\n\n  // 🔥 暂存工作流列表特殊样式\n  .temp-workflows {\n    .workflow-item {\n      &.editing {\n        border-color: #1890ff;\n        background: #f0f8ff;\n        box-shadow: 0 2px 8px rgba(24, 144, 255, 0.15);\n      }\n\n      // 🔥 验证错误状态样式\n      &.has-error {\n        border-color: #ff4d4f;\n        background: #fff2f0;\n        box-shadow: 0 2px 8px rgba(255, 77, 79, 0.15);\n\n        .workflow-name {\n          color: #ff4d4f;\n        }\n      }\n\n      .workflow-meta {\n        display: flex;\n        gap: 16px;\n        font-size: 12px;\n        color: #999;\n        margin-top: 8px;\n\n        .workflow-file, .workflow-size {\n          display: flex;\n          align-items: center;\n        }\n      }\n    }\n  }\n\n  // 🔥 工作流文件上传样式\n  .workflow-file-upload {\n    .uploaded-file-info {\n      .file-item {\n        display: flex;\n        align-items: center;\n        padding: 8px 12px;\n        background: #f5f5f5;\n        border: 1px solid #d9d9d9;\n        border-radius: 6px;\n        margin-bottom: 8px;\n\n        .file-icon {\n          color: #1890ff;\n          margin-right: 8px;\n          font-size: 16px;\n        }\n\n        .file-name {\n          flex: 1;\n          font-size: 14px;\n          color: #333;\n          margin-right: 8px;\n        }\n\n        .remove-btn {\n          padding: 4px 8px;\n          font-size: 12px;\n        }\n\n        // 🔥 已保存文件的特殊样式\n        &.saved-file {\n          background: #f6ffed;\n          border-color: #b7eb8f;\n\n          .file-icon {\n            color: #52c41a;\n          }\n\n          .file-name {\n            color: #389e0d;\n            font-weight: 500;\n          }\n        }\n      }\n\n      // 🔥 验证错误提示样式\n      .workflow-errors {\n        margin-top: 12px;\n        margin-bottom: 8px;\n\n        .ant-alert {\n          border-radius: 4px;\n\n          .ant-alert-message {\n            font-size: 12px;\n            line-height: 1.4;\n          }\n        }\n      }\n    }\n  }\n\n  .workflow-empty {\n    text-align: center;\n    padding: 20px;\n\n    .ant-empty {\n      margin: 0;\n    }\n  }\n\n  .workflow-add-item {\n    margin-top: 16px;\n\n    .add-workflow-btn {\n      height: 48px;\n      border: 2px dashed #d9d9d9;\n      color: #666;\n      font-size: 14px;\n      transition: all 0.3s;\n\n      &:hover {\n        border-color: #1890ff;\n        color: #1890ff;\n      }\n\n      .anticon {\n        margin-right: 8px;\n      }\n    }\n  }\n}\n\n// 响应式设计\n@media (max-width: 768px) {\n  :global(.creator-agent-modal) {\n    .ant-modal {\n      width: 95% !important;\n      margin: 10px auto;\n    }\n\n    .ant-modal-body {\n      padding: 16px;\n    }\n  }\n\n  .modal-footer {\n    padding: 12px 16px;\n\n    .ant-btn {\n      width: 48%;\n      margin-left: 4%;\n\n      &:first-child {\n        margin-left: 0;\n      }\n    }\n  }\n\n  .workflow-section {\n    .workflow-item {\n      flex-direction: column;\n      gap: 12px;\n\n      .workflow-actions {\n        align-self: stretch;\n        justify-content: flex-end;\n      }\n    }\n  }\n}\n\n// 第三步成功页面样式\n.success-content {\n  text-align: center;\n  padding: 40px 20px;\n\n  .success-icon {\n    margin-bottom: 24px;\n  }\n\n  .success-message {\n    h2 {\n      color: #52c41a;\n      font-size: 24px;\n      font-weight: 600;\n      margin-bottom: 16px;\n    }\n\n    p {\n      color: #666;\n      font-size: 14px;\n      line-height: 1.6;\n      margin-bottom: 8px;\n\n      &:last-child {\n        margin-bottom: 0;\n      }\n    }\n  }\n}\n", {"version": 3, "sources": ["CreatorAgentForm.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6oEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "CreatorAgentForm.vue", "sourceRoot": "src/views/website/workflow/components", "sourcesContent": ["<template>\n  <a-modal\n    :title=\"modalTitle\"\n    :visible=\"visible\"\n    :width=\"900\"\n    :confirm-loading=\"stepLoading\"\n    :mask-closable=\"false\"\n    :centered=\"true\"\n    :body-style=\"{ padding: '0' }\"\n    :footer=\"null\"\n    class=\"creator-agent-modal\"\n    @cancel=\"handleCancel\"\n  >\n    <!-- 步骤条 -->\n    <div class=\"step-header\">\n      <a-steps :current=\"currentStep\" class=\"custom-steps\">\n        <a-step title=\"智能体信息\" description=\"填写基本信息\" />\n        <a-step title=\"工作流配置\" description=\"配置工作流\" />\n        <a-step title=\"完成创建\" description=\"创建完成\" />\n      </a-steps>\n    </div>\n\n    <!-- 步骤内容 -->\n    <div class=\"step-content\">\n      <!-- 第一步：智能体基本信息 -->\n      <div v-show=\"currentStep === 0\" class=\"step-panel\">\n        <div class=\"panel-header\">\n          <h3 class=\"panel-title\">\n            <a-icon type=\"robot\" />\n            智能体基本信息\n          </h3>\n          <p class=\"panel-desc\">请填写智能体的基本信息，这些信息将展示给用户</p>\n        </div>\n\n        <a-form-model\n          ref=\"form\"\n          :model=\"formData\"\n          :rules=\"rules\"\n          class=\"modern-form\"\n        >\n          <!-- 智能体名称 -->\n          <div class=\"form-card\">\n            <a-form-model-item prop=\"agentName\">\n              <div class=\"field-label\">\n                <a-icon type=\"robot\" />\n                智能体名称\n                <span class=\"required-star\">*</span>\n              </div>\n              <a-input\n                v-model=\"formData.agentName\"\n                placeholder=\"请输入智能体名称\"\n                size=\"large\"\n                class=\"modern-input\"\n                :max-length=\"100\"\n                show-count\n              />\n              <div class=\"field-tips\">\n                为您的智能体起一个吸引人的名称\n              </div>\n            </a-form-model-item>\n          </div>\n\n          <!-- 智能体描述 -->\n          <div class=\"form-card\">\n            <a-form-model-item prop=\"agentDescription\">\n              <div class=\"field-label\">\n                <a-icon type=\"file-text\" />\n                智能体描述\n                <span class=\"required-star\">*</span>\n              </div>\n              <a-textarea\n                v-model=\"formData.agentDescription\"\n                placeholder=\"请详细描述您的智能体功能和特点\"\n                :rows=\"4\"\n                :max-length=\"1000\"\n                show-count\n                class=\"modern-textarea\"\n              />\n              <div class=\"field-tips\">\n                详细描述有助于用户了解您的智能体功能\n              </div>\n            </a-form-model-item>\n          </div>\n\n          <!-- 智能体头像 -->\n          <div class=\"form-card\">\n            <a-form-model-item prop=\"agentAvatar\">\n              <div class=\"field-label\">\n                <a-icon type=\"picture\" />\n                智能体头像\n                <span class=\"required-star\">*</span>\n              </div>\n              <j-image-upload-deferred\n                ref=\"avatarUpload\"\n                v-model=\"formData.agentAvatar\"\n                :isMultiple=\"false\"\n                bizPath=\"agent-avatar\"\n                text=\"上传头像\">\n              </j-image-upload-deferred>\n              <div class=\"field-tips\">\n                支持 JPG、PNG 格式，文件大小不超过 5MB\n              </div>\n            </a-form-model-item>\n          </div>\n\n          <!-- 体验链接 -->\n          <div class=\"form-card\">\n            <a-form-model-item prop=\"experienceLink\">\n              <div class=\"field-label\">\n                <a-icon type=\"link\" />\n                体验链接\n                <span class=\"optional\">（可选）</span>\n              </div>\n              <a-input\n                v-model=\"formData.experienceLink\"\n                placeholder=\"请输入体验链接\"\n                size=\"large\"\n                class=\"modern-input\"\n                :max-length=\"500\"\n              />\n              <div class=\"field-tips\">\n                用户可以通过此链接体验您的智能体功能\n              </div>\n            </a-form-model-item>\n          </div>\n\n          <!-- 价格 -->\n          <div class=\"form-card\">\n            <a-form-model-item prop=\"price\">\n              <div class=\"field-label\">\n                <a-icon type=\"dollar\" />\n                价格设置\n                <span class=\"required-star\">*</span>\n              </div>\n              <a-input-number\n                v-model=\"formData.price\"\n                placeholder=\"请输入价格\"\n                :min=\"0\"\n                :max=\"99999\"\n                :precision=\"2\"\n                :step=\"0.01\"\n                size=\"large\"\n                class=\"modern-input-number\"\n              >\n                <template slot=\"addonBefore\">¥</template>\n              </a-input-number>\n              <div class=\"field-tips\">\n                设置智能体的使用价格，用户购买后可以使用您的智能体\n              </div>\n            </a-form-model-item>\n          </div>\n        </a-form-model>\n\n        <!-- 第一步底部按钮 -->\n        <div class=\"step-footer\">\n          <a-button @click=\"handleCancel\" :disabled=\"stepLoading\">\n            取消\n          </a-button>\n          <a-button type=\"primary\" @click=\"handleNext\" :loading=\"stepLoading\" class=\"next-btn\">\n            下一步：配置工作流\n            <a-icon type=\"arrow-right\" />\n          </a-button>\n        </div>\n      </div>\n\n      <!-- 第二步：工作流配置 -->\n      <div v-show=\"currentStep === 1\" class=\"step-panel\">\n        <div class=\"panel-header\">\n          <h3 class=\"panel-title\">\n            <a-icon type=\"apartment\" />\n            工作流配置\n          </h3>\n          <p class=\"panel-desc\">为您的智能体配置工作流，提升智能体的功能和效率</p>\n        </div>\n\n        <!-- 已创建的智能体信息 -->\n        <div class=\"created-agent-info\" v-if=\"createdAgent\">\n          <div class=\"agent-summary\">\n            <div class=\"agent-avatar\">\n              <img :src=\"getFullAvatarUrl(createdAgent.agentAvatar)\" :alt=\"createdAgent.agentName\" v-if=\"createdAgent.agentAvatar\" />\n              <a-icon type=\"robot\" v-else />\n            </div>\n            <div class=\"agent-details\">\n              <h4>{{ createdAgent.agentName }}</h4>\n              <p>{{ createdAgent.agentDescription }}</p>\n              <span class=\"agent-price\">¥{{ createdAgent.price }}</span>\n            </div>\n          </div>\n        </div>\n\n        <!-- 工作流配置区域 -->\n        <div class=\"workflow-section\">\n          <!-- 工作流表单 -->\n          <div class=\"workflow-form\">\n            <div class=\"form-header\">\n              <h3>\n                <a-icon type=\"apartment\" />\n                新增工作流\n              </h3>\n              <p>为您的智能体添加工作流，让它更加智能和实用</p>\n            </div>\n\n            <a-form-model ref=\"workflowFormRef\" :model=\"workflowFormData\" :rules=\"workflowRules\" layout=\"vertical\">\n              <a-form-model-item label=\"工作流名称\" prop=\"workflowName\">\n                <a-input\n                  v-model=\"workflowFormData.workflowName\"\n                  placeholder=\"请输入工作流名称，如：文档生成助手\"\n                  :maxLength=\"30\"\n                  show-count\n                />\n              </a-form-model-item>\n\n              <a-form-model-item label=\"工作流描述\" prop=\"workflowDescription\">\n                <a-textarea\n                  v-model=\"workflowFormData.workflowDescription\"\n                  placeholder=\"请描述工作流的功能和用途，帮助用户了解其作用\"\n                  :rows=\"3\"\n                  :maxLength=\"200\"\n                  show-count\n                />\n              </a-form-model-item>\n\n              <a-form-model-item label=\"输入参数说明\" prop=\"inputParamsDesc\">\n                <a-textarea\n                  v-model=\"workflowFormData.inputParamsDesc\"\n                  placeholder=\"格式：参数:值 或 参数:&quot;值&quot; 或 参数:'值'（例如：name:&quot;张三&quot;,age:25,city:'北京'）\"\n                  :rows=\"4\"\n                  :maxLength=\"10000\"\n                  @blur=\"handleInputParamsBlur\"\n                />\n                <div style=\"color: #666; font-size: 12px; margin-top: 4px;\">\n                  * 必填项，例如：name:&quot;张三&quot;,age:25,city:'北京' 支持中英文冒号逗号\n                </div>\n              </a-form-model-item>\n\n              <a-form-model-item label=\"工作流文件\" prop=\"workflowPackage\">\n                <!-- 文件上传区域 -->\n                <div class=\"workflow-file-upload\">\n                  <!-- 已上传文件显示 -->\n                  <div v-if=\"workflowFileInfo\" class=\"uploaded-file-info\">\n                    <div class=\"file-item\" :class=\"{ 'saved-file': workflowFileInfo.isSaved }\">\n                      <a-icon type=\"file-zip\" class=\"file-icon\" />\n                      <span class=\"file-name\">{{ workflowFileInfo.originalName || workflowFileInfo.name }}</span>\n                      <!-- 🔥 已保存的文件显示状态标签 -->\n                      <a-tag v-if=\"workflowFileInfo.isSaved\" color=\"green\" size=\"small\" style=\"margin-left: 8px;\">\n                        已保存\n                      </a-tag>\n                      <a-button\n                        type=\"link\"\n                        size=\"small\"\n                        @click=\"handleRemoveWorkflowFile\"\n                        class=\"remove-btn\"\n                        :title=\"workflowFileInfo.isSaved ? '重新选择文件' : '删除文件'\"\n                      >\n                        <a-icon :type=\"workflowFileInfo.isSaved ? 'edit' : 'delete'\" />\n                        {{ workflowFileInfo.isSaved ? '重选' : '删除' }}\n                      </a-button>\n                    </div>\n                  </div>\n\n                  <!-- 上传按钮 -->\n                  <div v-else class=\"upload-area\">\n                    <a-upload\n                      ref=\"workflowUpload\"\n                      name=\"file\"\n                      :multiple=\"false\"\n                      :before-upload=\"beforeWorkflowUpload\"\n                      :show-upload-list=\"false\"\n                      @change=\"handleWorkflowFileSelect\"\n                      accept=\".zip\"\n                      :customRequest=\"() => {}\"\n                    >\n                      <a-button :loading=\"workflowUploading\">\n                        <a-icon type=\"upload\" />\n                        选择工作流压缩包\n                      </a-button>\n                    </a-upload>\n                  </div>\n                </div>\n\n                <div class=\"upload-tip\">\n                  <a-icon type=\"exclamation-circle\" style=\"color: #faad14;\" />\n                  <strong>温馨提示：</strong>只支持 .zip 格式，文件大小不超过 5MB\n                </div>\n              </a-form-model-item>\n            </a-form-model>\n\n            <!-- 新增下一个工作流按钮 -->\n            <div class=\"add-next-workflow\" v-show=\"currentStep === 1\">\n              <a-button type=\"dashed\" block @click=\"addNextWorkflow\" class=\"add-next-btn\">\n                <a-icon type=\"plus\" />\n                新增下一个工作流\n              </a-button>\n            </div>\n          </div>\n\n          <!-- 工作流列表 -->\n          <div class=\"temp-workflows\" v-if=\"tempWorkflowList.length > 0\">\n            <a-divider>工作流列表 ({{ tempWorkflowList.length }})</a-divider>\n            <div class=\"workflow-list\">\n              <div\n                v-for=\"(workflow, index) in tempWorkflowList\"\n                :key=\"workflow.id\"\n                class=\"workflow-item\"\n                :class=\"{\n                  'editing': currentWorkflowIndex === index,\n                  'has-error': workflowValidationErrors[workflow.id] && !workflowValidationErrors[workflow.id].isValid\n                }\"\n              >\n                <div class=\"workflow-info\">\n                  <h4 class=\"workflow-name\">\n                    {{ workflow.workflowName }}\n                    <!-- 状态标签 -->\n                    <a-tag v-if=\"workflow.status === 'saved'\" color=\"green\" size=\"small\">已保存</a-tag>\n                    <a-tag v-else-if=\"workflow.status === 'draft'\" color=\"orange\" size=\"small\">新增</a-tag>\n                    <a-tag v-if=\"currentWorkflowIndex === index\" color=\"blue\" size=\"small\">编辑中</a-tag>\n                    <a-tag v-if=\"workflowValidationErrors[workflow.id] && !workflowValidationErrors[workflow.id].isValid\" color=\"red\" size=\"small\">\n                      <a-icon type=\"exclamation-circle\" />\n                      有错误\n                    </a-tag>\n                  </h4>\n                  <p class=\"workflow-desc\">{{ workflow.workflowDescription || '暂无描述' }}</p>\n\n                  <!-- 🔥 验证错误提示 -->\n                  <div v-if=\"workflowValidationErrors[workflow.id] && !workflowValidationErrors[workflow.id].isValid\" class=\"workflow-errors\">\n                    <a-alert\n                      type=\"error\"\n                      size=\"small\"\n                      show-icon\n                      :message=\"`请补充：${workflowValidationErrors[workflow.id].errors.join('、')}`\"\n                    />\n                  </div>\n\n                </div>\n                <div class=\"workflow-actions\">\n                  <a-button size=\"small\" @click=\"loadWorkflowFromTemp(index)\" :disabled=\"currentWorkflowIndex === index\">\n                    <a-icon type=\"edit\" />\n                    编辑\n                  </a-button>\n                  <a-button size=\"small\" type=\"danger\" @click=\"deleteWorkflowFromTemp(index)\">\n                    <a-icon type=\"delete\" />\n                    删除\n                  </a-button>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- 第二步底部按钮 -->\n        <div class=\"step-footer\">\n          <a-button @click=\"handlePrev\" :disabled=\"stepLoading\">\n            <a-icon type=\"arrow-left\" />\n            上一步\n          </a-button>\n          <a-button type=\"primary\" @click=\"handleComplete\" :loading=\"stepLoading\" class=\"complete-btn\">\n            完成创建\n            <a-icon type=\"check\" />\n          </a-button>\n        </div>\n      </div>\n\n      <!-- 第三步：创建完成 -->\n      <div v-if=\"currentStep === 2\" class=\"step-content\">\n        <div class=\"step-header\">\n          <h3>\n            <a-icon type=\"check-circle\" style=\"color: #52c41a; margin-right: 8px;\" />\n            创建完成\n          </h3>\n          <p>智能体创建流程已完成</p>\n        </div>\n\n        <div class=\"success-content\">\n          <div class=\"success-icon\">\n            <a-icon type=\"check-circle\" style=\"font-size: 64px; color: #52c41a;\" />\n          </div>\n          <div class=\"success-message\">\n            <h2>恭喜您，已成功提交智能体，请耐心等待审核！</h2>\n            <p>您的智能体信息已提交至平台，我们将在1-3个工作日内完成审核。</p>\n            <p>审核结果将通过站内消息通知您，请注意查收。</p>\n          </div>\n        </div>\n\n        <!-- 第三步底部按钮 -->\n        <div class=\"step-footer\">\n          <a-button type=\"primary\" @click=\"handleCloseModal\" size=\"large\">\n            关闭\n          </a-button>\n        </div>\n      </div>\n    </div>\n  </a-modal>\n</template>\n\n<script>\nimport JImageUploadDeferred from '@/components/jeecg/JImageUploadDeferred'\nimport { createAgent, updateAgent } from '@/api/creator-agent'\nimport { createWorkflow, updateWorkflow, getWorkflowList } from '@/api/creator-workflow'\n\nexport default {\n  name: 'CreatorAgentForm',\n  components: {\n    JImageUploadDeferred\n  },\n  props: {\n    visible: {\n      type: Boolean,\n      default: false\n    },\n    loading: {\n      type: Boolean,\n      default: false\n    },\n    agent: {\n      type: Object,\n      default: null\n    },\n    mode: {\n      type: String,\n      default: 'create' // 'create' | 'edit'\n    }\n  },\n\n  data() {\n    return {\n      currentStep: 0, // 当前步骤 0=第一步，1=第二步\n      stepLoading: false, // 步骤操作加载状态\n      createdAgent: null, // 第一步创建的智能体信息\n\n      formData: {\n        agentId: '',\n        agentName: '',\n        agentDescription: '',\n        agentAvatar: '',\n        experienceLink: '',\n        price: 0\n      },\n\n      // 🔥 工作流表单数据\n      workflowFormData: {\n        workflowName: '',\n        workflowDescription: '',\n        inputParamsDesc: '',\n        workflowPackage: ''\n      },\n\n      // 🔥 工作流前端暂存管理（新的数据管理机制）\n      tempWorkflowList: [], // 前端暂存的工作流列表\n      currentWorkflowIndex: -1, // 当前正在编辑的工作流索引 (-1表示新建)\n\n      // 🔥 工作流验证错误状态管理\n      workflowValidationErrors: {}, // 格式：{ workflowId: { errors: ['缺少工作流描述', '缺少压缩包文件'], isValid: false } }\n      workflowFileList: [], // 当前工作流的文件列表（File对象，未上传）\n\n      // 🔥 工作流文件上传相关（延迟上传机制）\n      workflowFileInfo: null, // 当前选择的文件信息（用于显示）\n      workflowUploading: false, // 上传状态\n      workflowList: [], // 已保存的工作流列表（从后端加载）\n      workflowLoading: false,\n\n      rules: {\n        agentName: [\n          { required: true, message: '请输入智能体名称', trigger: 'blur' },\n          { min: 2, max: 100, message: '智能体名称长度在 2 到 100 个字符', trigger: 'blur' }\n        ],\n        agentDescription: [\n          { required: true, message: '请输入智能体描述', trigger: 'blur' },\n          { min: 2, max: 1000, message: '智能体描述长度在 2 到 1000 个字符', trigger: 'blur' }\n        ],\n        agentAvatar: [\n          { required: true, message: '请上传智能体头像', trigger: 'change' }\n        ],\n        experienceLink: [\n          { type: 'url', message: '请输入正确的URL格式', trigger: 'blur' }\n        ],\n        price: [\n          { required: true, message: '请输入价格', trigger: 'blur' },\n          { type: 'number', min: 0, max: 99999, message: '价格范围在 0 到 99999 元', trigger: 'blur' }\n        ]\n      },\n\n      // 🔥 工作流表单验证规则（所有字段必填）\n      workflowRules: {\n        workflowName: [\n          { required: true, message: '工作流名称为必填项', trigger: 'blur' },\n          { min: 2, max: 30, message: '工作流名称长度在 2 到 30 个字符', trigger: 'blur' },\n          { pattern: /^[a-zA-Z0-9\\u4e00-\\u9fa5\\s\\-_]+$/, message: '工作流名称只能包含中英文、数字、空格、横线和下划线', trigger: 'blur' }\n        ],\n        workflowDescription: [\n          { required: true, message: '工作流描述为必填项', trigger: 'blur' },\n          { min: 2, max: 200, message: '工作流描述长度在 2 到 200 个字符', trigger: 'blur' }\n        ],\n        inputParamsDesc: [\n          { required: true, message: '请输入参数说明', trigger: 'blur' },\n          { min: 2, message: '参数说明至少需要2个字符', trigger: 'blur' },\n          { max: 10000, message: '参数说明长度不能超过10000个字符', trigger: 'blur' },\n          {\n            pattern: /^[^:：]+[:：](?:\"[^\"]*\"|'[^']*'|[^:：,，]+)(?:[,，][^:：]+[:：](?:\"[^\"]*\"|'[^']*'|[^:：,，]+))*$/,\n            message: '请按照格式填写：参数:值 或 参数:\"值\" 或 参数:\\'值\\'',\n            trigger: 'blur'\n          }\n        ],\n        workflowPackage: [\n          // 🔥 移除自动验证，改为手动验证，避免文件选择后仍提示必填\n        ]\n      }\n    }\n  },\n\n  computed: {\n    modalTitle() {\n      return this.mode === 'create' ? '新增智能体' : '编辑智能体'\n    },\n\n    // 🔥 文件上传配置（与后台管理系统一致）\n    uploadAction() {\n      return `${window._CONFIG['domianURL']}/sys/common/upload`\n    },\n\n    uploadHeaders() {\n      return {\n        'X-Access-Token': this.$ls.get('Access-Token')\n      }\n    },\n\n    uploadData() {\n      return {\n        'isup': 1,\n        'biz': '' // 工作流文件使用空的biz，后台会自动设置为workflow路径\n      }\n    }\n  },\n\n  watch: {\n    visible(val) {\n      if (val) {\n        this.initForm()\n        // 🔥 新增和编辑模式都从第一步开始，让用户可以修改基本信息\n        this.currentStep = 0\n\n        // 🔥 弹窗打开时立即滚动到顶部\n        this.scrollToTop()\n\n        if (this.mode === 'edit' && this.agent) {\n          this.createdAgent = { ...this.agent }\n          // 🔥 编辑模式下立即加载工作流数据，确保数据回填\n          this.$nextTick(() => {\n            this.loadWorkflowList(this.agent.id)\n          })\n        }\n      } else {\n        // 🔥 弹窗关闭时清空所有数据，避免数据污染\n        this.resetForm()\n        this.clearAllWorkflowData()\n      }\n    },\n\n    agent: {\n      handler(val) {\n        if (val && this.visible) {\n          this.initForm()\n          if (this.mode === 'edit') {\n            this.createdAgent = { ...val }\n            // 🔥 编辑模式下立即加载工作流数据，确保数据回填\n            this.$nextTick(() => {\n              this.loadWorkflowList(val.id)\n            })\n          }\n        }\n      },\n      deep: true\n    }\n  },\n\n  methods: {\n    // 初始化表单\n    initForm() {\n      if (this.mode === 'edit' && this.agent) {\n        this.formData = {\n          agentName: this.agent.agentName || '',\n          agentDescription: this.agent.agentDescription || '',\n          agentAvatar: this.agent.agentAvatar || '',\n          experienceLink: this.agent.experienceLink || '',\n          price: this.agent.price || 0\n        }\n      } else {\n        this.formData = {\n          agentName: '',\n          agentDescription: '',\n          agentAvatar: '',\n          experienceLink: '',\n          price: 0\n        }\n\n        // 🔥 新增智能体时清空所有暂存工作流数据，确保每个智能体的创建流程是独立的\n        this.clearAllWorkflowData()\n      }\n\n      // 清除验证状态\n      this.$nextTick(() => {\n        if (this.$refs.form) {\n          this.$refs.form.clearValidate()\n        }\n      })\n    },\n\n    // 重置表单\n    resetForm() {\n      this.currentStep = 0\n      this.stepLoading = false\n      this.createdAgent = null\n\n      this.formData = {\n        agentId: '',\n        agentName: '',\n        agentDescription: '',\n        agentAvatar: '',\n        experienceLink: '',\n        price: 0\n      }\n      this.workflowList = []\n\n      if (this.$refs.form) {\n        this.$refs.form.clearValidate()\n      }\n    },\n\n    // 🔥 第一步：下一步按钮\n    async handleNext() {\n      // 🔥 先进行表单验证\n      const originalAvatar = this.formData.agentAvatar\n      const hasPendingAvatar = this.$refs.avatarUpload && this.$refs.avatarUpload.hasPendingFiles()\n\n      if (!this.formData.agentAvatar && hasPendingAvatar) {\n        this.formData.agentAvatar = 'pending_upload'\n      }\n\n      this.$refs.form.validate(async (valid) => {\n        this.formData.agentAvatar = originalAvatar\n\n        if (valid) {\n          this.stepLoading = true\n          try {\n            // 🔥 上传头像\n            await this.uploadPendingImages()\n\n            // 🔥 提交智能体信息\n            const agentData = {\n              agentName: this.formData.agentName.trim(),\n              agentDescription: this.formData.agentDescription.trim(),\n              agentAvatar: this.processAvatarValue(this.formData.agentAvatar),\n              experienceLink: this.formData.experienceLink.trim(),\n              price: this.formData.price\n            }\n\n\n\n            // 🔥 根据模式调用不同的API\n            let resultAgent\n            if (this.mode === 'create') {\n              // 新增模式：创建新智能体\n              resultAgent = await this.createAgentStep(agentData)\n              this.$message.success('智能体创建成功，请配置工作流')\n            } else {\n              // 编辑模式：更新现有智能体\n              resultAgent = await this.updateAgentStep(agentData)\n              this.$message.success('智能体更新成功，请配置工作流')\n            }\n\n            this.createdAgent = resultAgent\n\n            // 进入第二步\n            this.currentStep = 1\n            this.loadWorkflowList(resultAgent.id) // 🔥 使用主键ID\n\n            // 🔥 滚动到顶部，确保用户看到完整的第二步内容\n            this.scrollToTop()\n\n          } catch (error) {\n            console.error('🎯 CreatorAgentForm: 第一步提交失败:', error)\n            this.$message.error('智能体创建失败: ' + (error.message || '未知错误'))\n          } finally {\n            this.stepLoading = false\n          }\n        } else {\n          this.$message.error('请检查表单信息')\n        }\n      })\n    },\n\n    // 🔥 第二步：上一步按钮\n    handlePrev() {\n      console.log('🎯 CreatorAgentForm: 返回第一步')\n      this.currentStep = 0\n\n      // 🔥 滚动到顶部，确保用户看到完整的第一步内容\n      this.scrollToTop()\n    },\n\n    // 🔥 第二步：完成按钮（批量保存所有工作流）\n    async handleComplete() {\n      try {\n        console.log('🎯 CreatorAgentForm: 完成创建，开始批量处理工作流')\n        this.stepLoading = true\n\n        // 🔥 自动暂存当前表单数据（统一处理，避免重复保存）\n        console.log('🎯 CreatorAgentForm: 第一步 - 自动暂存当前表单数据')\n        console.log('🎯 CreatorAgentForm: 当前暂存列表长度:', this.tempWorkflowList.length)\n        console.log('🎯 CreatorAgentForm: 当前编辑索引:', this.currentWorkflowIndex)\n        const saveResult = this.autoSaveCurrentWorkflow()\n        console.log('🎯 CreatorAgentForm: 自动暂存结果:', saveResult)\n        console.log('🎯 CreatorAgentForm: 暂存后列表长度:', this.tempWorkflowList.length)\n\n        // 检查是否有暂存的工作流需要保存\n        if (this.tempWorkflowList.length === 0) {\n          console.log('🎯 CreatorAgentForm: 无工作流数据，直接完成')\n          this.$message.success('智能体创建完成！')\n          this.$emit('complete', this.createdAgent)\n          this.handleCancel()\n          return\n        }\n\n        console.log(`🎯 CreatorAgentForm: 第二步 - 开始验证所有 ${this.tempWorkflowList.length} 个工作流`)\n\n        // 🔥 第二步：验证所有工作流的完整性（包括刚暂存的）\n        const validationResult = this.validateAllWorkflows()\n        if (!validationResult.isValid) {\n          console.error('🎯 CreatorAgentForm: 工作流验证失败:', validationResult.errors)\n\n          // 🔥 第三步：智能错误处理 - 回填第一个有错误的工作流到表单\n          this.handleValidationErrors(validationResult)\n          this.stepLoading = false\n          return\n        }\n\n        console.log(`🎯 CreatorAgentForm: 第三步 - 验证通过，开始批量保存 ${this.tempWorkflowList.length} 个工作流`)\n\n        // 批量上传文件和保存工作流\n        const savedWorkflows = await this.batchSaveWorkflows()\n\n        this.$message.success(`智能体和 ${savedWorkflows.length} 个工作流创建完成！`)\n\n        // 🔥 进入第三步成功页面，而不是直接关闭弹窗\n        this.currentStep = 2\n        this.scrollToTop()\n\n        console.log('🎯 CreatorAgentForm: 进入第三步成功页面')\n\n      } catch (error) {\n        console.error('🎯 CreatorAgentForm: 完成创建失败:', error)\n        this.$message.error('工作流保存失败: ' + (error.message || '未知错误'))\n      } finally {\n        this.stepLoading = false\n      }\n    },\n\n    // 🔥 关闭弹窗（第三步完成后）\n    handleCloseModal() {\n      console.log('🎯 CreatorAgentForm: 用户点击关闭按钮')\n      this.$emit('complete', this.createdAgent)\n      this.handleCancel()\n    },\n\n    // 🔥 批量保存所有暂存的工作流\n    async batchSaveWorkflows() {\n      const savedWorkflows = []\n      const totalCount = this.tempWorkflowList.length\n\n      for (let i = 0; i < this.tempWorkflowList.length; i++) {\n        const workflow = this.tempWorkflowList[i]\n        console.log(`🎯 CreatorAgentForm: 保存工作流 ${i + 1}/${totalCount}: ${workflow.workflowName}`)\n\n        try {\n          // 🔥 区分已保存和新增工作流的处理逻辑\n          if (workflow.status === 'saved') {\n            // 已保存的工作流：检查是否有实际修改（包括文件修改）\n            const originalWorkflow = workflow.originalWorkflow\n            const hasTextChanges = originalWorkflow && (\n              originalWorkflow.workflowName !== workflow.workflowName ||\n              originalWorkflow.workflowDescription !== workflow.workflowDescription ||\n              originalWorkflow.inputParamsDesc !== workflow.inputParamsDesc\n            )\n\n            // 🔥 检查是否重新选择了文件\n            const hasFileChanges = workflow.workflowFile !== null\n            const hasChanges = hasTextChanges || hasFileChanges\n\n            if (hasChanges) {\n              console.log(`🎯 CreatorAgentForm: 已保存工作流有修改，开始更新: ${workflow.workflowName}`)\n              console.log(`🎯 CreatorAgentForm: 文本修改: ${hasTextChanges}, 文件修改: ${hasFileChanges}`)\n\n              let fileUrl = originalWorkflow.workflowPackage // 默认使用原有文件路径\n\n              // 🔥 如果用户重新选择了文件，先上传新文件\n              if (hasFileChanges) {\n                console.log(`🎯 CreatorAgentForm: 检测到文件变更，上传新文件: ${workflow.workflowFile.name}`)\n                fileUrl = await this.uploadWorkflowFile(workflow.workflowFile, workflow.workflowName)\n                console.log(`🎯 CreatorAgentForm: 新文件上传成功: ${fileUrl}`)\n              }\n\n              // 🔥 调用更新工作流API\n              const updateData = {\n                agentId: this.createdAgent.id, // 🔥 后端必填字段\n                workflowName: workflow.workflowName,\n                workflowDescription: workflow.workflowDescription,\n                inputParamsDesc: workflow.inputParamsDesc,\n                workflowPackage: fileUrl // 🔥 使用新文件路径或原有路径\n              }\n\n              console.log('🎯 CreatorAgentForm: 调用工作流更新API:', updateData)\n              console.log('🎯 CreatorAgentForm: 更新前的原始数据:', originalWorkflow)\n              const response = await updateWorkflow(originalWorkflow.id, updateData)\n              console.log('🎯 CreatorAgentForm: 更新API响应:', response)\n\n              if (response.success) {\n                const updatedWorkflow = response.result || { ...originalWorkflow, ...updateData }\n                savedWorkflows.push(updatedWorkflow)\n\n                // 🔥 同步更新暂存列表中的数据，确保界面显示最新状态\n                this.tempWorkflowList[i] = {\n                  ...workflow,\n                  // 🔥 确保所有字段都同步到最新状态\n                  workflowName: updatedWorkflow.workflowName || workflow.workflowName,\n                  workflowDescription: updatedWorkflow.workflowDescription || workflow.workflowDescription,\n                  inputParamsDesc: updatedWorkflow.inputParamsDesc || workflow.inputParamsDesc,\n                  originalWorkflow: updatedWorkflow // 更新原始数据引用\n                }\n\n                console.log('🎯 CreatorAgentForm: 暂存列表已同步更新:', this.tempWorkflowList[i])\n\n                console.log(`🎯 CreatorAgentForm: 工作流 ${workflow.workflowName} 更新成功`)\n              } else {\n                throw new Error(response.message || '工作流更新API调用失败')\n              }\n            } else {\n              console.log(`🎯 CreatorAgentForm: 已保存工作流无修改，跳过保存: ${workflow.workflowName}`)\n              savedWorkflows.push(originalWorkflow) // 使用原始数据\n            }\n          } else {\n            // 新增的工作流：需要上传文件并创建\n            console.log(`🎯 CreatorAgentForm: 新增工作流，开始上传文件: ${workflow.workflowFile ? workflow.workflowFile.name : 'null'}`)\n            const fileUrl = await this.uploadWorkflowFile(workflow.workflowFile, workflow.workflowName)\n\n            // 保存工作流数据\n            const workflowData = {\n              agentId: this.createdAgent.id,\n              workflowName: workflow.workflowName,\n              workflowDescription: workflow.workflowDescription,\n              inputParamsDesc: workflow.inputParamsDesc, // 🔥 前端验证确保不为空\n              workflowPackage: fileUrl\n            }\n\n            // 🔥 调用工作流创建API\n            console.log('🎯 CreatorAgentForm: 调用工作流创建API:', workflowData)\n            const response = await createWorkflow(workflowData)\n\n            if (response.success) {\n              savedWorkflows.push(response.result)\n              console.log(`🎯 CreatorAgentForm: 工作流 ${workflow.workflowName} API调用成功`)\n            } else {\n              throw new Error(response.message || '工作流创建API调用失败')\n            }\n          }\n\n          console.log(`🎯 CreatorAgentForm: 工作流 ${workflow.workflowName} 保存成功`)\n\n        } catch (error) {\n          console.error(`🎯 CreatorAgentForm: 工作流 ${workflow.workflowName} 保存失败:`, error)\n          throw new Error(`工作流\"${workflow.workflowName}\"保存失败: ${error.message}`)\n        }\n      }\n\n      return savedWorkflows\n    },\n\n    // 🔥 上传单个工作流文件\n    async uploadWorkflowFile(file, workflowName) {\n      return new Promise((resolve, reject) => {\n        const formData = new FormData()\n        formData.append('file', file)\n        formData.append('isup', '1')\n        formData.append('biz', '') // 工作流文件使用空的biz\n\n        // 生成重命名后的文件名\n        const renamedFileName = this.generateWorkflowFileName(file.name, workflowName)\n\n        // 使用fetch进行文件上传\n        fetch(this.uploadAction, {\n          method: 'POST',\n          headers: this.uploadHeaders,\n          body: formData\n        })\n        .then(response => response.json())\n        .then(result => {\n          if (result.success) {\n            console.log(`🎯 CreatorAgentForm: 文件上传成功: ${file.name} -> ${result.message}`)\n            resolve(result.message)\n          } else {\n            reject(new Error(result.message || '文件上传失败'))\n          }\n        })\n        .catch(error => {\n          console.error(`🎯 CreatorAgentForm: 文件上传失败:`, error)\n          reject(error)\n        })\n      })\n    },\n\n    // 🔥 创建智能体（第一步）\n    async createAgentStep(agentData) {\n      try {\n        console.log('🎯 CreatorAgentForm: 调用创建智能体API...', agentData)\n        const response = await createAgent(agentData)\n\n        if (response.success) {\n          console.log('🎯 CreatorAgentForm: 智能体创建成功', response.result)\n\n          // 🔥 确认删除被替换的原始头像文件\n          this.confirmDeleteOriginalFiles()\n\n          return response.result\n        } else {\n          throw new Error(response.message || '创建智能体失败')\n        }\n      } catch (error) {\n        console.error('🎯 CreatorAgentForm: 创建智能体失败:', error)\n        throw error\n      }\n    },\n\n    // 🔥 更新智能体（编辑模式第一步）\n    async updateAgentStep(agentData) {\n      try {\n        console.log('🎯 CreatorAgentForm: 调用更新智能体API...', agentData)\n\n        // 获取智能体ID\n        const agentId = this.agent.id || this.agent.agentId\n        if (!agentId) {\n          throw new Error('智能体ID不存在')\n        }\n\n        const response = await updateAgent(agentId, agentData)\n\n        if (response.success) {\n          console.log('🎯 CreatorAgentForm: 智能体更新成功', response.result)\n\n          // 🔥 确认删除被替换的原始头像文件\n          this.confirmDeleteOriginalFiles()\n\n          return response.result\n        } else {\n          throw new Error(response.message || '更新智能体失败')\n        }\n      } catch (error) {\n        console.error('🎯 CreatorAgentForm: 更新智能体失败:', error)\n        throw error\n      }\n    },\n\n    // 🔥 处理头像值，确保返回字符串格式\n    processAvatarValue(avatarValue) {\n      console.log('🎯 CreatorAgentForm: 处理头像值 - 原始值:', avatarValue, typeof avatarValue)\n\n      // 如果是空值，返回空字符串\n      if (!avatarValue) {\n        return ''\n      }\n\n      // 如果是数组，取第一个元素\n      if (Array.isArray(avatarValue)) {\n        console.log('🎯 CreatorAgentForm: 头像值是数组，取第一个元素:', avatarValue[0])\n        return this.processAvatarValue(avatarValue[0]) // 递归处理\n      }\n\n      // 如果是对象，尝试获取url字段\n      if (typeof avatarValue === 'object' && avatarValue.url) {\n        console.log('🎯 CreatorAgentForm: 头像值是对象，取url字段:', avatarValue.url)\n        return this.processAvatarValue(avatarValue.url) // 递归处理\n      }\n\n      // 如果是字符串，处理URL\n      if (typeof avatarValue === 'string') {\n        return this.extractRelativePath(avatarValue)\n      }\n\n      // 其他情况，转换为字符串\n      console.log('🎯 CreatorAgentForm: 头像值转换为字符串:', String(avatarValue))\n      return String(avatarValue)\n    },\n\n    // 🔥 提取相对路径，避免TOS URL双重包装\n    extractRelativePath(url) {\n      console.log('🎯 CreatorAgentForm: 提取相对路径 - 输入URL:', url)\n\n      // 如果是完整的TOS URL，提取相对路径\n      if (url.includes('aigcview-tos.tos-cn-shanghai.volces.com/')) {\n        // 提取 uploads/ 开头的路径\n        const match = url.match(/uploads\\/[^?]+/)\n        if (match) {\n          const relativePath = match[0]\n          console.log('🎯 CreatorAgentForm: 从TOS URL提取相对路径:', relativePath)\n          return relativePath\n        }\n      }\n\n      // 如果是CDN URL，提取相对路径\n      if (url.includes('cdn.aigcview.com/')) {\n        const match = url.match(/uploads\\/[^?]+/)\n        if (match) {\n          const relativePath = match[0]\n          console.log('🎯 CreatorAgentForm: 从CDN URL提取相对路径:', relativePath)\n          return relativePath\n        }\n      }\n\n      // 如果已经是相对路径（以uploads/开头），直接返回\n      if (url.startsWith('uploads/')) {\n        console.log('🎯 CreatorAgentForm: 已经是相对路径:', url)\n        return url\n      }\n\n      // 其他情况，直接返回原值\n      console.log('🎯 CreatorAgentForm: 无法识别的URL格式，返回原值:', url)\n      return url\n    },\n\n    // 🔥 获取完整的头像URL（用于显示）\n    getFullAvatarUrl(avatarPath) {\n      if (!avatarPath) {\n        return ''\n      }\n\n      // 如果已经是完整URL，直接返回\n      if (avatarPath.startsWith('http')) {\n        return avatarPath\n      }\n\n      // 如果是相对路径，转换为CDN URL\n      if (avatarPath.startsWith('uploads/')) {\n        const cdnUrl = `https://cdn.aigcview.com/${avatarPath}`\n        console.log('🎯 CreatorAgentForm: 转换头像URL:', avatarPath, '->', cdnUrl)\n        return cdnUrl\n      }\n\n      // 其他情况，尝试拼接CDN前缀\n      const cdnUrl = `https://cdn.aigcview.com/${avatarPath}`\n      console.log('🎯 CreatorAgentForm: 拼接头像URL:', avatarPath, '->', cdnUrl)\n      return cdnUrl\n    },\n\n    // 提交表单（保留原方法，但在分步模式下不使用）\n    async handleSubmit() {\n      console.log('🎯 CreatorAgentForm: 开始提交智能体表单...')\n\n      // 🔥 先进行表单验证，验证通过后再上传头像\n      // 🔥 验证前检查头像：如果当前值为空但有待上传文件，则临时设置一个值通过验证\n      const originalAvatar = this.formData.agentAvatar\n      const hasPendingAvatar = this.$refs.avatarUpload && this.$refs.avatarUpload.hasPendingFiles()\n\n      if (!this.formData.agentAvatar && hasPendingAvatar) {\n        console.log('🎯 CreatorAgentForm: 检测到待上传头像，临时设置头像值以通过验证')\n        this.formData.agentAvatar = 'pending_upload' // 临时值\n      }\n\n      // 表单验证\n      this.$refs.form.validate(async (valid) => {\n        // 恢复原始值\n        this.formData.agentAvatar = originalAvatar\n\n        if (valid) {\n          console.log('🎯 CreatorAgentForm: 表单验证通过，开始上传头像...')\n\n          try {\n            // 🔥 表单验证通过后才上传头像\n            await this.uploadPendingImages()\n\n            // 创建提交数据，只包含需要的字段\n            const submitData = {\n              agentName: this.formData.agentName.trim(),\n              agentDescription: this.formData.agentDescription.trim(),\n              agentAvatar: this.processAvatarValue(this.formData.agentAvatar),\n              experienceLink: this.formData.experienceLink.trim(),\n              price: this.formData.price\n            }\n\n            console.log('🎯 CreatorAgentForm: 头像上传成功，提交数据:', submitData)\n            this.$emit('submit', submitData)\n\n          } catch (error) {\n            console.error('🎯 CreatorAgentForm: 头像上传失败:', error)\n            this.$message.error('头像上传失败: ' + (error.message || '未知错误'))\n          }\n        } else {\n          console.log('🎯 CreatorAgentForm: 表单验证失败，不上传头像')\n          this.$message.error('请检查表单信息')\n        }\n      })\n    },\n\n    // 取消\n    handleCancel() {\n      console.log('🎯 CreatorAgentForm: 用户取消，执行清理操作...')\n\n      // 🔥 回滚头像变更\n      this.rollbackChanges()\n\n      // 🔥 清空所有工作流数据，避免数据污染\n      this.clearAllWorkflowData()\n\n      // 🔥 重置表单数据\n      this.resetForm()\n\n      console.log('🎯 CreatorAgentForm: 所有数据已清理完成')\n      this.$emit('close')\n    },\n\n    // 🔥 上传待处理的图片（与后台管理系统逻辑一致）\n    async uploadPendingImages() {\n      console.log('🎯 CreatorAgentForm: 开始上传待处理的头像...')\n\n      if (this.$refs.avatarUpload && this.$refs.avatarUpload.hasPendingFiles()) {\n        console.log('🎯 CreatorAgentForm: 检测到待上传头像，开始上传...')\n\n        try {\n          // 执行上传并获取最终的URL\n          const finalUrl = await this.$refs.avatarUpload.performUpload()\n          console.log('🎯 CreatorAgentForm: 头像上传成功，URL:', finalUrl)\n\n          // 更新formData中的头像URL\n          this.formData.agentAvatar = finalUrl\n\n          return finalUrl\n        } catch (error) {\n          console.error('🎯 CreatorAgentForm: 头像上传失败:', error)\n          throw new Error('头像上传失败: ' + (error.message || '未知错误'))\n        }\n      } else {\n        console.log('🎯 CreatorAgentForm: 没有待上传的头像')\n        return this.formData.agentAvatar\n      }\n    },\n\n    // 🔥 确认删除原始文件（与后台管理系统逻辑一致）\n    confirmDeleteOriginalFiles() {\n      console.log('🎯 CreatorAgentForm: 确认删除被替换的原始头像文件...')\n\n      if (this.$refs.avatarUpload && this.$refs.avatarUpload.confirmDeleteOriginalFiles) {\n        this.$refs.avatarUpload.confirmDeleteOriginalFiles()\n        console.log('🎯 CreatorAgentForm: 原始头像文件删除确认完成')\n      }\n    },\n\n    // 🔥 回滚变更（与后台管理系统逻辑一致）\n    rollbackChanges() {\n      console.log('🎯 CreatorAgentForm: 回滚头像变更...')\n\n      if (this.$refs.avatarUpload && this.$refs.avatarUpload.rollbackChanges) {\n        this.$refs.avatarUpload.rollbackChanges()\n        console.log('🎯 CreatorAgentForm: 头像变更回滚完成')\n      }\n    },\n\n    // 🔥 加载工作流列表\n    async loadWorkflowList(agentId) {\n      if (!agentId) return\n\n      this.workflowLoading = true\n      try {\n        console.log('🎯 CreatorAgentForm: 加载工作流列表...', agentId)\n\n        // 🔥 调用获取工作流列表的API\n        const response = await getWorkflowList(agentId)\n\n        if (response.success) {\n          this.workflowList = response.result || []\n          console.log('🎯 CreatorAgentForm: API返回工作流数据:', this.workflowList)\n\n          // 🔥 编辑模式下：将现有工作流数据回填到暂存区域\n          if (this.mode === 'edit' && this.workflowList.length > 0) {\n            this.convertWorkflowsToTempList(this.workflowList)\n            console.log('🎯 CreatorAgentForm: 编辑模式 - 工作流数据已回填到暂存区域')\n          } else if (this.mode === 'edit') {\n            console.log('🎯 CreatorAgentForm: 编辑模式 - 该智能体暂无工作流数据')\n          }\n        } else {\n          console.error('🎯 CreatorAgentForm: API返回失败:', response.message)\n          this.$message.error(response.message || '获取工作流列表失败')\n          this.workflowList = []\n        }\n\n        console.log('🎯 CreatorAgentForm: 工作流列表加载完成，共', this.workflowList.length, '个工作流')\n      } catch (error) {\n        console.error('🎯 CreatorAgentForm: 加载工作流列表失败:', error)\n        this.$message.error('加载工作流列表失败: ' + (error.message || '网络错误'))\n        this.workflowList = []\n      } finally {\n        this.workflowLoading = false\n      }\n    },\n\n    // 🔥 将现有工作流数据转换为暂存格式（编辑模式回填）\n    convertWorkflowsToTempList(workflows) {\n      console.log('🎯 CreatorAgentForm: 开始转换工作流数据到暂存区域')\n      console.log('🎯 CreatorAgentForm: 原始工作流数据:', workflows)\n\n      // 清空现有暂存数据\n      this.tempWorkflowList = []\n\n      // 转换每个工作流为暂存格式\n      workflows.forEach((workflow, index) => {\n        console.log(`🎯 CreatorAgentForm: 处理工作流 ${index + 1}:`, {\n          id: workflow.id,\n          workflowName: workflow.workflowName,\n          workflowDescription: workflow.workflowDescription,\n          inputParamsDesc: workflow.inputParamsDesc,\n          workflowPackage: workflow.workflowPackage,\n          createTime: workflow.createTime\n        })\n\n        // 🔥 处理已保存工作流的文件信息\n        const packageFileName = workflow.workflowPackage || ''\n        const displayFileName = packageFileName ?\n          (packageFileName.includes('/') ? packageFileName.split('/').pop() : packageFileName) :\n          '已上传文件'\n\n        const tempWorkflow = {\n          id: workflow.id || Date.now() + index,\n          workflowName: workflow.workflowName || '',\n          workflowDescription: workflow.workflowDescription || '',\n          inputParamsDesc: workflow.inputParamsDesc || '', // 🔥 确保包含输入参数说明\n          workflowFile: null, // 已保存的工作流没有文件对象\n          fileName: displayFileName, // 🔥 显示文件名而不是完整路径\n          fileSize: 0, // 已保存的工作流无法获取文件大小\n          workflowPackage: workflow.workflowPackage || '', // 🔥 保存完整的包路径\n          status: 'saved', // 标记为已保存状态\n          createTime: new Date(workflow.createTime || Date.now()),\n          originalWorkflow: workflow // 保存原始工作流数据引用\n        }\n\n        this.tempWorkflowList.push(tempWorkflow)\n        console.log(`🎯 CreatorAgentForm: 工作流 \"${workflow.workflowName}\" 已转换为暂存格式:`, tempWorkflow)\n      })\n\n      console.log(`🎯 CreatorAgentForm: 共转换 ${workflows.length} 个工作流到暂存区域`)\n      console.log('🎯 CreatorAgentForm: 最终暂存列表:', this.tempWorkflowList)\n    },\n\n    // 🔥 工作流文件上传前安全检查\n    beforeWorkflowUpload(file) {\n      console.log('🎯 CreatorAgentForm: 工作流文件安全检查开始:', file.name)\n\n      // 🛡️ 1. 文件名安全检查\n      if (!this.isSecureFileName(file.name)) {\n        this.$message.error('文件名包含不安全字符，请重命名后重试!')\n        return false\n      }\n\n      // 🛡️ 2. 严格检查文件扩展名（只允许.zip）\n      const fileName = file.name.toLowerCase()\n      if (!fileName.endsWith('.zip')) {\n        this.$message.error('只能上传 .zip 格式的文件!')\n        return false\n      }\n\n      // 🛡️ 3. 严格检查MIME类型\n      const allowedMimeTypes = [\n        'application/zip',\n        'application/x-zip-compressed',\n        'application/x-zip'\n      ]\n      if (!allowedMimeTypes.includes(file.type)) {\n        this.$message.error('文件类型不正确，只允许上传ZIP压缩包!')\n        return false\n      }\n\n      // 🛡️ 4. 检查文件大小 (5MB)\n      const maxSize = 5 * 1024 * 1024 // 5MB\n      if (file.size > maxSize) {\n        this.$message.error('文件大小不能超过 5MB!')\n        return false\n      }\n\n      // 🛡️ 5. 检查文件大小不能为0\n      if (file.size === 0) {\n        this.$message.error('文件不能为空!')\n        return false\n      }\n\n      console.log('🎯 CreatorAgentForm: 工作流文件安全检查通过')\n      return false // 阻止默认上传，使用延迟上传机制\n    },\n\n    // 🛡️ 文件名安全检查\n    isSecureFileName(fileName) {\n      // 检查危险字符\n      const dangerousChars = /[<>:\"|?*\\x00-\\x1f]/\n      if (dangerousChars.test(fileName)) {\n        return false\n      }\n\n      // 检查路径遍历\n      if (fileName.includes('..') || fileName.includes('./') || fileName.includes('.\\\\')) {\n        return false\n      }\n\n      // 检查保留名称（Windows）\n      const reservedNames = /^(CON|PRN|AUX|NUL|COM[1-9]|LPT[1-9])(\\.|$)/i\n      if (reservedNames.test(fileName)) {\n        return false\n      }\n\n      // 检查文件名长度\n      if (fileName.length > 255) {\n        return false\n      }\n\n      return true\n    },\n\n    // 🛡️ 验证工作流数据安全性\n    validateWorkflowData() {\n      const { workflowName, workflowDescription, inputParamsDesc } = this.workflowFormData\n\n      // 检查工作流名称\n      if (!workflowName || workflowName.trim().length === 0) {\n        this.$message.error('工作流名称不能为空')\n        return false\n      }\n\n      // 检查名称中的危险字符\n      const namePattern = /^[a-zA-Z0-9\\u4e00-\\u9fa5\\s\\-_]+$/\n      if (!namePattern.test(workflowName)) {\n        this.$message.error('工作流名称包含不允许的字符')\n        return false\n      }\n\n      // 检查工作流描述\n      if (!workflowDescription || workflowDescription.trim().length === 0) {\n        this.$message.error('工作流描述不能为空')\n        return false\n      }\n\n      // 检查描述长度\n      if (workflowDescription.length < 10 || workflowDescription.length > 200) {\n        this.$message.error('工作流描述长度必须在10-200字符之间')\n        return false\n      }\n\n      // 检查是否包含潜在的脚本注入\n      const scriptPattern = /<script|javascript:|on\\w+\\s*=/i\n      if (scriptPattern.test(workflowName) || scriptPattern.test(workflowDescription) || scriptPattern.test(inputParamsDesc)) {\n        this.$message.error('输入内容包含不安全的脚本代码')\n        return false\n      }\n\n      return true\n    },\n\n    // 🔥 工作流文件选择处理（延迟上传机制）\n    handleWorkflowFileSelect(info) {\n      console.log('🎯 CreatorAgentForm: 工作流文件选择:', info.file ? info.file.name : 'no file', 'status:', info.file ? info.file.status : 'no status')\n\n      // 处理文件选择（不进行实际上传）\n      if (info.fileList && info.fileList.length > 0) {\n        const file = info.fileList[0].originFileObj || info.fileList[0]\n\n        // 🛡️ 文件大小验证（5MB限制）\n        const maxSize = 5 * 1024 * 1024 // 5MB\n        if (file.size > maxSize) {\n          console.error('🎯 CreatorAgentForm: 文件大小超过限制:', file.size, '字节')\n          this.$message.error('文件大小不能超过 5MB!')\n\n          // 🔥 立即清理文件状态，不保存任何信息\n          this.workflowFileList = []\n          this.workflowFileInfo = null\n\n          // 强制清空上传组件的文件列表\n          this.$nextTick(() => {\n            // 通过ref清空上传组件（如果有ref的话）\n            const uploadComponent = this.$refs.workflowUpload\n            if (uploadComponent) {\n              uploadComponent.fileList = []\n            }\n          })\n\n          console.log('🎯 CreatorAgentForm: 文件已清理，不保存超大文件')\n          return // 直接返回，不执行后续逻辑\n        }\n\n        // 🛡️ 文件大小不能为0\n        if (file.size === 0) {\n          console.error('🎯 CreatorAgentForm: 文件大小为0')\n          this.$message.error('文件不能为空!')\n\n          // 清理文件状态\n          this.workflowFileList = []\n          this.workflowFileInfo = null\n          return\n        }\n\n        // 🛡️ 文件名安全检查\n        if (!this.isSecureFileName(file.name)) {\n          console.error('🎯 CreatorAgentForm: 文件名不安全:', file.name)\n          this.$message.error('文件名包含不安全字符，请重命名后重试!')\n\n          // 清理文件状态\n          this.workflowFileList = []\n          this.workflowFileInfo = null\n          return\n        }\n\n        // 🛡️ 文件类型检查（扩展名）\n        const fileName = file.name.toLowerCase()\n        if (!fileName.endsWith('.zip')) {\n          console.error('🎯 CreatorAgentForm: 文件扩展名不正确:', file.name)\n          this.$message.error('只能上传 .zip 格式的文件!')\n\n          // 清理文件状态\n          this.workflowFileList = []\n          this.workflowFileInfo = null\n          return\n        }\n\n        // 🛡️ 文件类型检查（MIME类型）\n        const allowedMimeTypes = [\n          'application/zip',\n          'application/x-zip-compressed',\n          'application/x-zip'\n        ]\n        if (!allowedMimeTypes.includes(file.type)) {\n          console.error('🎯 CreatorAgentForm: 文件MIME类型不正确:', file.type)\n          this.$message.error('文件类型不正确，只允许上传ZIP压缩包!')\n\n          // 清理文件状态\n          this.workflowFileList = []\n          this.workflowFileInfo = null\n          return\n        }\n\n        // ✅ 所有验证通过，保存文件信息\n        this.workflowFileList = [file]\n        this.workflowFileInfo = {\n          name: file.name,\n          originalName: file.name,\n          size: file.size,\n          file: file, // 保存原始File对象\n          isSaved: false // 🔥 新选择的文件标记为未保存状态\n        }\n\n        console.log('🎯 CreatorAgentForm: 工作流文件已选择，等待最终上传:', file.name)\n        this.$message.success('工作流文件已选择，将在完成创建时上传')\n\n      } else if (info.fileList && info.fileList.length === 0) {\n        // 文件被移除\n        this.workflowFileList = []\n        this.workflowFileInfo = null\n        console.log('🎯 CreatorAgentForm: 工作流文件已移除')\n      }\n    },\n\n    // 🔥 生成工作流文件名（与后台管理系统一致的重命名逻辑）\n    generateWorkflowFileName(originalName, customWorkflowName = null) {\n      // 获取文件扩展名\n      const lastDotIndex = originalName.lastIndexOf('.')\n      const extension = lastDotIndex > -1 ? originalName.substring(lastDotIndex) : ''\n\n      // 生成时间戳\n      const timestamp = new Date().getTime()\n\n      // 生成随机数\n      const random = Math.floor(Math.random() * 10000).toString().padStart(4, '0')\n\n      // 智能体名称（清理特殊字符）\n      const agentName = this.formData.agentName\n        ? this.formData.agentName.replace(/[^a-zA-Z0-9\\u4e00-\\u9fa5]/g, '_').substring(0, 20)\n        : 'workflow'\n\n      // 工作流名称（清理特殊字符）\n      const workflowName = (customWorkflowName || this.workflowFormData.workflowName)\n        ? (customWorkflowName || this.workflowFormData.workflowName).replace(/[^a-zA-Z0-9\\u4e00-\\u9fa5]/g, '_').substring(0, 20)\n        : 'default'\n\n      // 生成新文件名：智能体名称_工作流名称_时间戳_随机数.扩展名\n      const newFileName = `${agentName}_${workflowName}_${timestamp}_${random}${extension}`\n\n      console.log('🎯 CreatorAgentForm: 文件重命名:', originalName, '->', newFileName)\n      return newFileName\n    },\n\n    // 🔥 移除工作流文件\n    handleWorkflowRemove(file) {\n      console.log('🎯 CreatorAgentForm: 移除工作流文件:', file.name)\n      this.workflowFileList = []\n      this.workflowFormData.workflowPackage = ''\n      this.workflowFileInfo = null\n    },\n\n    // 🔥 移除工作流文件（用于文件信息显示区域）\n    handleRemoveWorkflowFile() {\n      console.log('🎯 CreatorAgentForm: 移除工作流文件')\n      this.workflowFileList = []\n      this.workflowFormData.workflowPackage = ''\n      this.workflowFileInfo = null\n      this.$message.success('工作流文件已移除')\n    },\n\n    // 🔥 格式化文件大小\n    formatFileSize(bytes) {\n      if (!bytes) return '0 B'\n      const k = 1024\n      const sizes = ['B', 'KB', 'MB', 'GB']\n      const i = Math.floor(Math.log(bytes) / Math.log(k))\n      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]\n    },\n\n    // 🔥 提交工作流表单\n    handleWorkflowSubmit() {\n      return new Promise((resolve, reject) => {\n        // 🛡️ 1. 验证表单（Vue 2 + Ant Design Vue 方式）\n        this.$refs.workflowFormRef.validate((valid) => {\n          if (!valid) {\n            this.$message.error('请完善表单信息')\n            reject(new Error('表单验证失败'))\n            return\n          }\n\n          this.performWorkflowSubmit().then(resolve).catch(reject)\n        })\n      })\n    },\n\n    // 🔥 执行工作流提交\n    async performWorkflowSubmit() {\n      try {\n        // 🛡️ 2. 二次验证文件\n        if (this.workflowFileList.length === 0) {\n          this.$message.error('请选择工作流文件')\n          throw new Error('未选择工作流文件')\n        }\n\n        const file = this.workflowFileList[0]\n\n        // 🛡️ 3. 提交前再次安全检查\n        if (!this.isSecureFileName(file.name)) {\n          this.$message.error('文件名不安全，请重新选择文件')\n          throw new Error('文件名不安全')\n        }\n\n        if (!file.name.toLowerCase().endsWith('.zip')) {\n          this.$message.error('只能提交ZIP格式的文件')\n          throw new Error('文件格式不正确')\n        }\n\n        if (file.size > 5 * 1024 * 1024) {\n          this.$message.error('文件大小超过5MB限制')\n          throw new Error('文件大小超限')\n        }\n\n        // 🛡️ 4. 验证表单数据安全性\n        if (!this.validateWorkflowData()) {\n          throw new Error('表单数据验证失败')\n        }\n\n        this.stepLoading = true\n        console.log('🎯 CreatorAgentForm: 提交工作流表单:', this.workflowFormData)\n\n        // TODO: 调用工作流创建API\n        // const workflowData = {\n        //   agentId: this.createdAgent.id,\n        //   workflowName: this.workflowFormData.workflowName,\n        //   workflowDescription: this.workflowFormData.workflowDescription,\n        //   workflowFile: this.workflowFileList[0]\n        // }\n        // const response = await createWorkflow(workflowData)\n\n        // 模拟成功\n        await new Promise(resolve => setTimeout(resolve, 1000))\n\n        this.$message.success('工作流创建成功!')\n\n        // 重新加载工作流列表\n        this.loadWorkflowList(this.createdAgent.id)\n\n        // 重置表单\n        this.resetWorkflowForm()\n\n        return true\n      } catch (error) {\n        console.error('🎯 CreatorAgentForm: 工作流创建失败:', error)\n        this.$message.error('工作流创建失败: ' + (error.message || '未知错误'))\n        throw error\n      } finally {\n        this.stepLoading = false\n      }\n    },\n\n    // 🔥 重置工作流表单\n    resetWorkflowForm() {\n      this.workflowFormData = {\n        workflowName: '',\n        workflowDescription: '',\n        inputParamsDesc: '',\n        workflowPackage: ''\n      }\n      this.workflowFileList = []\n      this.workflowFileInfo = null\n      this.currentWorkflowIndex = -1\n\n      // Vue 2 + Ant Design Vue 方式重置表单\n      this.$nextTick(() => {\n        if (this.$refs.workflowFormRef) {\n          this.$refs.workflowFormRef.resetFields()\n        }\n      })\n\n      console.log('🎯 CreatorAgentForm: 工作流表单已重置')\n    },\n\n    // 🔥 清空所有工作流数据（解决数据污染问题）\n    clearAllWorkflowData() {\n      // 清空暂存工作流列表\n      this.tempWorkflowList = []\n\n      // 重置工作流表单\n      this.resetWorkflowForm()\n\n      // 重置工作流相关状态\n      this.workflowFileList = []\n      this.workflowFileInfo = null\n      this.currentWorkflowIndex = -1\n\n      // 🔥 清空工作流验证错误状态\n      this.workflowValidationErrors = {}\n\n      // 🔥 重置工作流上传状态\n      this.workflowUploading = false\n\n      // 🔥 清空已保存的工作流列表\n      this.workflowList = []\n      this.workflowLoading = false\n\n      console.log('🎯 CreatorAgentForm: 所有工作流数据已清空')\n    },\n\n    // 🔥 处理输入参数说明失焦事件，手动触发验证\n    handleInputParamsBlur() {\n      console.log('🎯 CreatorAgentForm: 输入参数说明失焦，触发验证')\n      this.$nextTick(() => {\n        if (this.$refs.workflowFormRef) {\n          this.$refs.workflowFormRef.validateField('inputParamsDesc', (errorMessage) => {\n            if (errorMessage) {\n              console.log('🎯 CreatorAgentForm: 输入参数说明验证失败:', errorMessage)\n            } else {\n              console.log('🎯 CreatorAgentForm: 输入参数说明验证通过')\n            }\n          })\n        }\n      })\n    },\n\n    // 🔥 滚动弹窗到顶部（提升用户体验）\n    scrollToTop() {\n      this.$nextTick(() => {\n        console.log('🎯 CreatorAgentForm: 开始查找滚动容器...')\n\n        // 🔥 优先查找步骤内容容器（真正的滚动容器）\n        const stepContent = document.querySelector('.step-content')\n\n        if (stepContent) {\n          console.log('🎯 CreatorAgentForm: 找到步骤内容容器:', {\n            scrollTop: stepContent.scrollTop,\n            scrollHeight: stepContent.scrollHeight,\n            clientHeight: stepContent.clientHeight\n          })\n\n          const beforeScrollTop = stepContent.scrollTop\n          stepContent.scrollTop = 0\n          const afterScrollTop = stepContent.scrollTop\n\n          console.log(`🎯 CreatorAgentForm: 步骤内容滚动 - 滚动前: ${beforeScrollTop}, 滚动后: ${afterScrollTop}`)\n\n          if (beforeScrollTop !== afterScrollTop || beforeScrollTop === 0) {\n            console.log('🎯 CreatorAgentForm: 步骤内容滚动成功')\n            return\n          }\n        }\n\n        // 🔥 备用方案：尝试其他可能的滚动容器\n        const selectors = [\n          '.creator-agent-modal .ant-modal-body',\n          '.ant-modal-body',\n          '.creator-agent-modal .ant-modal-content',\n          '.ant-modal-content',\n          '.creator-agent-modal',\n          '.ant-modal-wrap'\n        ]\n\n        let scrollContainer = null\n        let usedSelector = ''\n\n        for (const selector of selectors) {\n          const element = document.querySelector(selector)\n          if (element) {\n            console.log(`🎯 CreatorAgentForm: 找到元素 ${selector}:`, element)\n            console.log(`🎯 CreatorAgentForm: 元素当前 scrollTop: ${element.scrollTop}, scrollHeight: ${element.scrollHeight}, clientHeight: ${element.clientHeight}`)\n\n            // 检查元素是否可滚动\n            if (element.scrollHeight > element.clientHeight || element.scrollTop > 0) {\n              scrollContainer = element\n              usedSelector = selector\n              break\n            }\n          }\n        }\n\n        if (scrollContainer) {\n          const beforeScrollTop = scrollContainer.scrollTop\n          scrollContainer.scrollTop = 0\n          const afterScrollTop = scrollContainer.scrollTop\n\n          console.log(`🎯 CreatorAgentForm: 使用选择器 ${usedSelector} 滚动到顶部`)\n          console.log(`🎯 CreatorAgentForm: 滚动前: ${beforeScrollTop}, 滚动后: ${afterScrollTop}`)\n\n          if (beforeScrollTop === afterScrollTop && beforeScrollTop > 0) {\n            console.warn('🎯 CreatorAgentForm: 滚动可能没有生效，尝试其他方法')\n            // 尝试平滑滚动\n            scrollContainer.scrollTo({ top: 0, behavior: 'smooth' })\n          }\n        } else {\n          console.warn('🎯 CreatorAgentForm: 未找到可滚动的容器，延迟重试')\n          // 🔥 延迟重试，并尝试更多选择器\n          setTimeout(() => {\n            const allModalElements = document.querySelectorAll('[class*=\"modal\"]')\n            console.log('🎯 CreatorAgentForm: 所有modal相关元素:', allModalElements)\n\n            for (const element of allModalElements) {\n              if (element.scrollHeight > element.clientHeight || element.scrollTop > 0) {\n                element.scrollTop = 0\n                console.log('🎯 CreatorAgentForm: 延迟重试成功，使用元素:', element)\n                return\n              }\n            }\n\n            console.error('🎯 CreatorAgentForm: 延迟重试仍未找到可滚动容器')\n          }, 200)\n        }\n      })\n    },\n\n    // 🔥 新增下一个工作流（前端暂存机制）\n    addNextWorkflow() {\n      console.log('🎯 CreatorAgentForm: 新增下一个工作流')\n\n      // 🔥 自动暂存当前数据（智能保护机制）\n      this.autoSaveCurrentWorkflow()\n\n      // 重置表单，准备添加新工作流\n      this.resetWorkflowForm()\n      this.currentWorkflowIndex = -1 // 设置为新建状态\n\n      // 🔥 滚动到顶部，确保用户看到新工作流表单的开始\n      this.scrollToTop()\n\n      this.$message.success('可以继续添加新工作流')\n    },\n\n    // 🔥 验证当前工作流数据\n    validateCurrentWorkflowData() {\n      // 检查工作流名称\n      if (!this.workflowFormData.workflowName.trim()) {\n        this.$message.error('请输入工作流名称')\n        return false\n      }\n\n      // 🔥 严格检查工作流名称长度（防止数据库字段溢出）\n      if (this.workflowFormData.workflowName.trim().length > 30) {\n        this.$message.error('工作流名称不能超过30个字符')\n        return false\n      }\n\n      // 检查工作流描述\n      if (!this.workflowFormData.workflowDescription.trim()) {\n        this.$message.error('请输入工作流描述')\n        return false\n      }\n\n      // 🔥 严格检查工作流描述长度\n      if (this.workflowFormData.workflowDescription.trim().length > 200) {\n        this.$message.error('工作流描述不能超过200个字符')\n        return false\n      }\n\n      // 🔥 检查输入参数说明（后端必填字段）\n      if (!this.workflowFormData.inputParamsDesc.trim()) {\n        this.$message.error('请输入参数说明')\n        return false\n      }\n\n      // 🔥 检查输入参数说明格式（与后台管理一致的正则验证）\n      const inputParamsPattern = /^[^:：]+[:：](?:\"[^\"]*\"|'[^']*'|[^:：,，]+)(?:[,，][^:：]+[:：](?:\"[^\"]*\"|'[^']*'|[^:：,，]+))*$/\n      if (!inputParamsPattern.test(this.workflowFormData.inputParamsDesc.trim())) {\n        this.$message.error('请按照格式填写：参数:值 或 参数:\"值\" 或 参数:\\'值\\'')\n        return false\n      }\n\n      // 🔥 检查工作流文件（区分新增和已保存工作流）\n      console.log('🎯 CreatorAgentForm: 文件验证 - workflowFileList.length:', this.workflowFileList.length)\n      console.log('🎯 CreatorAgentForm: 文件验证 - workflowFileInfo:', this.workflowFileInfo)\n      console.log('🎯 CreatorAgentForm: 文件验证 - workflowFileInfo.isSaved:', this.workflowFileInfo && this.workflowFileInfo.isSaved)\n\n      // 🔥 文件验证逻辑：\n      // 1. 如果有新选择的文件（workflowFileList.length > 0），验证通过\n      // 2. 如果没有新文件，但有已保存的文件信息（workflowFileInfo.isSaved），验证通过\n      // 3. 其他情况验证失败\n      const hasNewFile = this.workflowFileList.length > 0\n      const hasSavedFile = this.workflowFileInfo && this.workflowFileInfo.isSaved\n\n      if (!hasNewFile && !hasSavedFile) {\n        this.$message.error('工作流文件为必填项，请上传ZIP压缩包')\n        return false\n      }\n\n      return true\n    },\n\n    // 🔥 保存当前工作流到暂存列表（支持任意字段的暂存）\n    saveCurrentWorkflowToTemp() {\n      // 🔥 生成默认工作流名称（如果用户没有输入）\n      let workflowName = this.workflowFormData.workflowName.trim()\n      if (!workflowName) {\n        // 如果没有名称，生成一个默认名称\n        const timestamp = new Date().toLocaleString('zh-CN')\n        workflowName = `工作流_${timestamp}`\n      }\n\n      // 🔥 智能处理工作流状态和文件信息\n      const isEditingExisting = this.currentWorkflowIndex >= 0\n      const existingWorkflow = isEditingExisting ? this.tempWorkflowList[this.currentWorkflowIndex] : null\n\n      console.log('🎯 CreatorAgentForm: saveCurrentWorkflowToTemp - isEditingExisting:', isEditingExisting)\n      console.log('🎯 CreatorAgentForm: saveCurrentWorkflowToTemp - existingWorkflow:', existingWorkflow)\n      console.log('🎯 CreatorAgentForm: saveCurrentWorkflowToTemp - existingWorkflow.status:', existingWorkflow ? existingWorkflow.status : 'null')\n\n      // 🔥 保持已保存工作流的状态和文件信息\n      const workflowData = {\n        id: isEditingExisting ? existingWorkflow.id : Date.now(),\n        workflowName: workflowName,\n        workflowDescription: this.workflowFormData.workflowDescription.trim(),\n        inputParamsDesc: this.workflowFormData.inputParamsDesc.trim(),\n        workflowFile: this.workflowFileList.length > 0 ? this.workflowFileList[0] : (existingWorkflow ? existingWorkflow.workflowFile : null),\n        fileName: this.workflowFileInfo ? this.workflowFileInfo.name : (existingWorkflow ? existingWorkflow.fileName : ''),\n        fileSize: this.workflowFileInfo ? this.workflowFileInfo.size : (existingWorkflow ? existingWorkflow.fileSize : 0),\n        // 🔥 保持已保存工作流的状态，新工作流设为draft\n        status: (existingWorkflow && existingWorkflow.status === 'saved') ? 'saved' : 'draft',\n        createTime: isEditingExisting ? existingWorkflow.createTime : new Date(),\n        // 🔥 保持已保存工作流的包路径信息\n        workflowPackage: existingWorkflow ? existingWorkflow.workflowPackage : '',\n        originalWorkflow: existingWorkflow ? existingWorkflow.originalWorkflow : null\n      }\n\n      console.log('🎯 CreatorAgentForm: saveCurrentWorkflowToTemp - 创建的workflowData:', workflowData)\n      console.log('🎯 CreatorAgentForm: saveCurrentWorkflowToTemp - workflowData.status:', workflowData.status)\n      console.log('🎯 CreatorAgentForm: saveCurrentWorkflowToTemp - workflowData.workflowPackage:', workflowData.workflowPackage)\n\n      if (this.currentWorkflowIndex >= 0) {\n        // 更新现有工作流\n        console.log('🎯 CreatorAgentForm: 更新现有工作流，索引:', this.currentWorkflowIndex)\n        this.tempWorkflowList.splice(this.currentWorkflowIndex, 1, workflowData)\n        console.log('🎯 CreatorAgentForm: 更新暂存工作流:', workflowData)\n      } else {\n        // 添加新工作流\n        console.log('🎯 CreatorAgentForm: 添加新工作流，当前列表长度:', this.tempWorkflowList.length)\n        this.tempWorkflowList.push(workflowData)\n        console.log('🎯 CreatorAgentForm: 添加暂存工作流:', workflowData)\n        console.log('🎯 CreatorAgentForm: 添加后列表长度:', this.tempWorkflowList.length)\n      }\n\n      // 🔥 实时更新验证状态\n      this.updateWorkflowValidation(workflowData)\n    },\n\n    // 🔥 从暂存列表加载工作流数据到表单（带自动暂存保护）\n    loadWorkflowFromTemp(index, skipAutoSave = false) {\n      if (index < 0 || index >= this.tempWorkflowList.length) {\n        console.error('🎯 CreatorAgentForm: 无效的工作流索引:', index)\n        return\n      }\n\n      // 🔥 自动暂存当前正在编辑的数据（数据保护机制）\n      // 在验证错误处理时跳过自动暂存，避免循环\n      if (!skipAutoSave) {\n        this.autoSaveCurrentWorkflow()\n      }\n\n      const workflow = this.tempWorkflowList[index]\n      console.log('🎯 CreatorAgentForm: 加载暂存工作流:', workflow)\n\n      // 加载表单数据\n      this.workflowFormData = {\n        workflowName: workflow.workflowName,\n        workflowDescription: workflow.workflowDescription,\n        inputParamsDesc: workflow.inputParamsDesc || '',\n        workflowPackage: ''\n      }\n\n      // 🔥 加载文件数据（区分新增和已保存工作流）\n      if (workflow.status === 'saved' && workflow.fileName) {\n        // 已保存的工作流：显示文件信息但没有File对象\n        this.workflowFileList = [] // 已保存的工作流没有File对象\n        this.workflowFileInfo = {\n          name: workflow.fileName,\n          originalName: workflow.fileName,\n          size: workflow.fileSize || 0,\n          file: null, // 已保存的工作流没有File对象\n          isSaved: true, // 标记为已保存状态\n          packagePath: workflow.workflowPackage || '' // 保存包路径\n        }\n        console.log('🎯 CreatorAgentForm: 加载已保存工作流的文件信息:', this.workflowFileInfo)\n      } else if (workflow.workflowFile && workflow.fileName) {\n        // 新增的工作流：有完整的File对象\n        this.workflowFileList = [workflow.workflowFile]\n        this.workflowFileInfo = {\n          name: workflow.fileName,\n          originalName: workflow.fileName,\n          size: workflow.fileSize || 0,\n          file: workflow.workflowFile,\n          isSaved: false\n        }\n        console.log('🎯 CreatorAgentForm: 加载新增工作流的文件信息:', this.workflowFileInfo)\n      } else {\n        // 没有文件信息\n        this.workflowFileList = []\n        this.workflowFileInfo = null\n        console.log('🎯 CreatorAgentForm: 工作流没有文件信息')\n      }\n\n      // 设置当前编辑索引\n      this.currentWorkflowIndex = index\n\n      // 在验证错误处理时不显示加载提示\n      if (!skipAutoSave) {\n        this.$message.info(`已加载工作流: ${workflow.workflowName}`)\n\n        // 🔥 滚动到第二步顶部，确保用户看到完整的工作流表单\n        this.scrollToTop()\n      }\n    },\n\n    // 🔥 自动暂存当前工作流数据（智能数据保护）\n    autoSaveCurrentWorkflow() {\n      // 🔥 检查当前是否有任意工作流数据需要保存（包括已保存的工作流）\n      const hasCurrentData = this.workflowFormData.workflowName.trim() ||\n                             this.workflowFormData.workflowDescription.trim() ||\n                             this.workflowFileList.length > 0 ||\n                             (this.workflowFileInfo && this.workflowFileInfo.isSaved)\n\n      if (!hasCurrentData) {\n        console.log('🎯 CreatorAgentForm: 当前无数据，跳过自动暂存')\n        return false // 返回false表示没有暂存任何数据\n      }\n\n      // 如果当前正在编辑已存在的工作流，直接更新\n      if (this.currentWorkflowIndex >= 0) {\n        console.log('🎯 CreatorAgentForm: 自动更新当前编辑的工作流')\n        this.saveCurrentWorkflowToTemp()\n        return true // 返回true表示已暂存数据\n      }\n\n      // 🔥 如果是新工作流，只要有任意数据就自动暂存\n      console.log('🎯 CreatorAgentForm: 自动暂存新工作流数据（有任意输入）')\n      this.saveCurrentWorkflowToTemp()\n\n      // 在完成创建流程中不显示暂存提示，避免干扰用户\n      if (!this.stepLoading) {\n        this.$message.info('当前工作流数据已自动暂存')\n      }\n\n      return true // 返回true表示已暂存数据\n    },\n\n    // 🔥 验证所有工作流的完整性\n    validateAllWorkflows() {\n      console.log('🎯 CreatorAgentForm: 开始验证所有工作流的完整性')\n\n      // 清空之前的验证错误\n      this.workflowValidationErrors = {}\n\n      let isAllValid = true\n      let invalidCount = 0\n      const allErrors = []\n\n      // 遍历所有暂存的工作流\n      for (let i = 0; i < this.tempWorkflowList.length; i++) {\n        const workflow = this.tempWorkflowList[i]\n        const errors = []\n\n        // 验证工作流名称\n        if (!workflow.workflowName || !workflow.workflowName.trim()) {\n          errors.push('缺少工作流名称')\n        }\n\n        // 验证工作流描述\n        if (!workflow.workflowDescription || !workflow.workflowDescription.trim()) {\n          errors.push('缺少工作流描述')\n        }\n\n        // 🔥 验证输入参数说明（后端必填字段）\n        if (!workflow.inputParamsDesc || !workflow.inputParamsDesc.trim()) {\n          errors.push('缺少参数说明')\n        } else {\n          // 🔥 验证输入参数说明格式（与后台管理一致的正则验证）\n          const inputParamsPattern = /^[^:：]+[:：](?:\"[^\"]*\"|'[^']*'|[^:：,，]+)(?:[,，][^:：]+[:：](?:\"[^\"]*\"|'[^']*'|[^:：,，]+))*$/\n          if (!inputParamsPattern.test(workflow.inputParamsDesc.trim())) {\n            errors.push('参数说明格式不正确')\n          }\n        }\n\n        // 🔥 验证工作流文件（区分新增和已保存工作流）\n        if (workflow.status === 'saved') {\n          // 已保存的工作流：只需要有文件名或包路径\n          if (!workflow.fileName && !workflow.workflowPackage) {\n            errors.push('缺少压缩包文件')\n          }\n        } else {\n          // 新增的工作流：需要有File对象和文件名\n          if (!workflow.workflowFile || !workflow.fileName) {\n            errors.push('缺少压缩包文件')\n          }\n        }\n\n        // 记录验证结果\n        const isValid = errors.length === 0\n        this.workflowValidationErrors[workflow.id] = {\n          errors: errors,\n          isValid: isValid,\n          workflowName: workflow.workflowName || `工作流${i + 1}`\n        }\n\n        if (!isValid) {\n          isAllValid = false\n          invalidCount++\n          allErrors.push(`${workflow.workflowName || `工作流${i + 1}`}: ${errors.join('、')}`)\n        }\n      }\n\n      const result = {\n        isValid: isAllValid,\n        invalidCount: invalidCount,\n        errors: allErrors,\n        validationDetails: this.workflowValidationErrors\n      }\n\n      console.log('🎯 CreatorAgentForm: 工作流验证结果:', result)\n      return result\n    },\n\n    // 🔥 实时更新单个工作流的验证状态\n    updateWorkflowValidation(workflow) {\n      const errors = []\n\n      // 验证工作流名称\n      if (!workflow.workflowName || !workflow.workflowName.trim()) {\n        errors.push('缺少工作流名称')\n      }\n\n      // 验证工作流描述\n      if (!workflow.workflowDescription || !workflow.workflowDescription.trim()) {\n        errors.push('缺少工作流描述')\n      }\n\n      // 🔥 验证输入参数说明（与批量验证保持一致）\n      if (!workflow.inputParamsDesc || !workflow.inputParamsDesc.trim()) {\n        errors.push('缺少参数说明')\n      } else {\n        // 🔥 验证输入参数说明格式（与后台管理一致的正则验证）\n        const inputParamsPattern = /^[^:：]+[:：](?:\"[^\"]*\"|'[^']*'|[^:：,，]+)(?:[,，][^:：]+[:：](?:\"[^\"]*\"|'[^']*'|[^:：,，]+))*$/\n        if (!inputParamsPattern.test(workflow.inputParamsDesc.trim())) {\n          errors.push('参数说明格式不正确')\n        }\n      }\n\n      // 🔥 验证工作流文件（区分新增和已保存工作流）\n      if (workflow.status === 'saved') {\n        // 已保存的工作流：只需要有文件名或包路径\n        if (!workflow.fileName && !workflow.workflowPackage) {\n          errors.push('缺少压缩包文件')\n        }\n      } else {\n        // 新增的工作流：需要有File对象和文件名\n        if (!workflow.workflowFile || !workflow.fileName) {\n          errors.push('缺少压缩包文件')\n        }\n      }\n\n      // 更新验证状态\n      const isValid = errors.length === 0\n      this.$set(this.workflowValidationErrors, workflow.id, {\n        errors: errors,\n        isValid: isValid,\n        workflowName: workflow.workflowName || '未命名工作流'\n      })\n\n      console.log(`🎯 CreatorAgentForm: 更新工作流验证状态 - ${workflow.workflowName}: ${isValid ? '有效' : '无效'}`, errors)\n    },\n\n    // 🔥 智能错误处理 - 回填第一个有错误的工作流到表单\n    handleValidationErrors(validationResult) {\n      console.log('🎯 CreatorAgentForm: 开始智能错误处理')\n\n      // 找到第一个有错误的工作流\n      let firstErrorWorkflowIndex = -1\n      let firstErrorWorkflow = null\n\n      for (let i = 0; i < this.tempWorkflowList.length; i++) {\n        const workflow = this.tempWorkflowList[i]\n        const validationInfo = validationResult.validationDetails[workflow.id]\n\n        if (validationInfo && !validationInfo.isValid) {\n          firstErrorWorkflowIndex = i\n          firstErrorWorkflow = workflow\n          break\n        }\n      }\n\n      if (firstErrorWorkflowIndex >= 0 && firstErrorWorkflow) {\n        console.log(`🎯 CreatorAgentForm: 找到第一个错误工作流: ${firstErrorWorkflow.workflowName} (索引: ${firstErrorWorkflowIndex})`)\n\n        // 回填错误工作流到表单中（跳过自动暂存避免循环）\n        this.loadWorkflowFromTemp(firstErrorWorkflowIndex, true)\n\n        // 显示详细的错误信息\n        const errorInfo = validationResult.validationDetails[firstErrorWorkflow.id]\n        const errorMessage = `工作流\"${firstErrorWorkflow.workflowName}\"缺少必填信息：${errorInfo.errors.join('、')}`\n\n        this.$message.error(errorMessage)\n\n        // 显示总体错误统计\n        setTimeout(() => {\n          this.$message.warning(`共有 ${validationResult.invalidCount} 个工作流存在问题，请逐一完善后再提交`)\n        }, 1000)\n\n        console.log('🎯 CreatorAgentForm: 错误工作流已回填到表单，用户可以直接修改')\n      } else {\n        // 如果没找到错误工作流（理论上不应该发生）\n        this.$message.error(`请完善工作流信息后再提交，共有 ${validationResult.invalidCount} 个工作流存在问题`)\n      }\n    },\n\n    // 🔥 从暂存列表删除工作流\n    deleteWorkflowFromTemp(index) {\n      if (index < 0 || index >= this.tempWorkflowList.length) {\n        console.error('🎯 CreatorAgentForm: 无效的工作流索引:', index)\n        return\n      }\n\n      const workflow = this.tempWorkflowList[index]\n      this.$confirm({\n        title: '删除工作流',\n        content: `确定要删除工作流\"${workflow.workflowName}\"吗？`,\n        onOk: () => {\n          this.tempWorkflowList.splice(index, 1)\n          console.log('🎯 CreatorAgentForm: 删除暂存工作流:', workflow)\n\n          // 如果删除的是当前正在编辑的工作流，重置表单\n          if (this.currentWorkflowIndex === index) {\n            this.resetWorkflowForm()\n            this.currentWorkflowIndex = -1\n            // 🔥 删除当前编辑的工作流后滚动到顶部\n            this.scrollToTop()\n          } else if (this.currentWorkflowIndex > index) {\n            // 调整当前编辑索引\n            this.currentWorkflowIndex--\n          }\n\n          this.$message.success('工作流已删除')\n        }\n      })\n    },\n\n    // 格式化日期\n    formatDate(date) {\n      if (!date) return ''\n      return new Date(date).toLocaleString('zh-CN')\n    }\n  }\n}\n</script>\n\n<style lang=\"less\" scoped>\n// 上传提示\n.upload-tips {\n  margin-top: 8px;\n\n  p {\n    margin: 0;\n    font-size: 12px;\n    color: #666;\n    line-height: 1.4;\n  }\n\n  .tip-note {\n    color: #1890ff;\n    font-weight: 500;\n    margin-top: 4px;\n  }\n}\n\n// 字段提示\n.field-tips {\n  margin-top: 4px;\n  font-size: 12px;\n  color: #999;\n  line-height: 1.4;\n}\n\n// 表单通知\n.form-notice {\n  margin-top: 24px;\n\n  .ant-alert {\n    border-radius: 6px;\n\n    .ant-alert-message {\n      font-weight: 600;\n    }\n\n    .ant-alert-description {\n      margin-top: 4px;\n      line-height: 1.5;\n    }\n  }\n}\n\n// 表单样式优化\n.ant-form-model {\n  .ant-form-model-item {\n    margin-bottom: 24px;\n\n    .ant-form-model-item-label {\n      label {\n        font-weight: 600;\n        color: #1f2937;\n\n        &::after {\n          content: '';\n        }\n      }\n    }\n\n    .ant-input,\n    .ant-input-number,\n    .ant-select-selection,\n    .ant-input-number-input {\n      border-radius: 6px;\n      border-color: #d1d5db;\n\n      &:focus,\n      &:hover {\n        border-color: #1890ff;\n      }\n    }\n\n    .ant-textarea {\n      border-radius: 6px;\n      border-color: #d1d5db;\n\n      &:focus,\n      &:hover {\n        border-color: #1890ff;\n      }\n    }\n\n    .ant-input-number {\n      width: 100%;\n\n      .ant-input-number-input {\n        border: none;\n      }\n    }\n  }\n}\n\n// 模态框样式\n.ant-modal {\n  .ant-modal-header {\n    border-radius: 8px 8px 0 0;\n\n    .ant-modal-title {\n      font-weight: 600;\n      color: #1f2937;\n    }\n  }\n\n  .ant-modal-body {\n    padding: 24px;\n  }\n\n  .ant-modal-footer {\n    border-radius: 0 0 8px 8px;\n\n    .ant-btn {\n      border-radius: 6px;\n\n      &.ant-btn-primary {\n        background: #1890ff;\n        border-color: #1890ff;\n\n        &:hover {\n          background: #40a9ff;\n          border-color: #40a9ff;\n        }\n      }\n    }\n  }\n}\n\n// 响应式设计\n@media (max-width: 768px) {\n  .ant-modal {\n    margin: 0;\n    max-width: 100vw;\n\n    .ant-modal-content {\n      border-radius: 0;\n    }\n  }\n\n  .ant-form-model {\n    .ant-form-model-item {\n      .ant-form-model-item-label {\n        text-align: left;\n      }\n    }\n  }\n}\n\n// 弹窗样式\n:global(.creator-agent-modal) {\n  .ant-modal-header {\n    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n    border-bottom: none;\n    border-radius: 12px 12px 0 0;\n\n    .ant-modal-title {\n      color: white;\n      font-weight: 600;\n      font-size: 20px;\n    }\n  }\n\n  .ant-modal-close {\n    color: white;\n\n    &:hover {\n      color: rgba(255, 255, 255, 0.8);\n    }\n  }\n\n  .ant-modal-body {\n    padding: 0;\n    max-height: 80vh;\n    overflow: hidden;\n  }\n\n  .ant-modal-content {\n    border-radius: 12px;\n    overflow: hidden;\n    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);\n  }\n}\n\n// 步骤条样式\n.step-header {\n  padding: 24px 32px;\n  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);\n  border-bottom: 1px solid #e2e8f0;\n\n  .custom-steps {\n    :global(.ant-steps-item-title) {\n      font-weight: 600;\n      font-size: 16px;\n    }\n\n    :global(.ant-steps-item-description) {\n      color: #64748b;\n    }\n\n    :global(.ant-steps-item-process .ant-steps-item-icon) {\n      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n      border-color: #667eea;\n    }\n\n    :global(.ant-steps-item-finish .ant-steps-item-icon) {\n      background: #10b981;\n      border-color: #10b981;\n    }\n  }\n}\n\n// 步骤内容\n.step-content {\n  min-height: 500px;\n  max-height: 60vh;\n  overflow-y: auto;\n}\n\n.step-panel {\n  padding: 32px;\n\n  .panel-header {\n    text-align: center;\n    margin-bottom: 32px;\n\n    .panel-title {\n      font-size: 24px;\n      font-weight: 700;\n      color: #1e293b;\n      margin: 0 0 8px 0;\n\n      .anticon {\n        margin-right: 12px;\n        color: #667eea;\n      }\n    }\n\n    .panel-desc {\n      font-size: 16px;\n      color: #64748b;\n      margin: 0;\n    }\n  }\n}\n\n// 现代化表单样式\n.modern-form {\n  .form-card {\n    background: white;\n    border-radius: 12px;\n    padding: 24px;\n    margin-bottom: 20px;\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\n    border: 1px solid #e2e8f0;\n    transition: all 0.3s ease;\n\n    &:hover {\n      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);\n      border-color: #cbd5e1;\n    }\n\n    .field-label {\n      font-size: 16px;\n      font-weight: 600;\n      color: #1e293b;\n      margin-bottom: 12px;\n      display: flex;\n      align-items: center;\n\n      .anticon {\n        margin-right: 8px;\n        color: #667eea;\n        font-size: 18px;\n      }\n\n      .optional {\n        font-size: 14px;\n        font-weight: 400;\n        color: #94a3b8;\n        margin-left: 8px;\n      }\n\n      // 🔥 必填星号样式\n      .required-star {\n        color: #ff4d4f;\n        font-size: 16px;\n        font-weight: 600;\n        margin-left: 4px;\n      }\n    }\n\n    .modern-input,\n    .modern-textarea,\n    .modern-input-number {\n      border-radius: 8px;\n      border: 2px solid #e2e8f0;\n      transition: all 0.3s ease;\n\n      &:hover {\n        border-color: #cbd5e1;\n      }\n\n      &:focus,\n      &.ant-input-focused {\n        border-color: #667eea;\n        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);\n      }\n    }\n\n    .modern-input-number {\n      width: 100%;\n\n      :global(.ant-input-number-input) {\n        border: none;\n        border-radius: 6px;\n      }\n    }\n\n    .field-tips {\n      margin-top: 8px;\n      font-size: 14px;\n      color: #64748b;\n      line-height: 1.5;\n    }\n  }\n}\n\n// 步骤底部按钮\n.step-footer {\n  padding: 24px 32px;\n  border-top: 1px solid #e2e8f0;\n  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n\n  .ant-btn {\n    height: 44px;\n    padding: 0 24px;\n    font-size: 16px;\n    font-weight: 500;\n    border-radius: 8px;\n    transition: all 0.3s ease;\n\n    &:not(.ant-btn-primary) {\n      border: 2px solid #e2e8f0;\n      color: #64748b;\n\n      &:hover {\n        border-color: #cbd5e1;\n        color: #475569;\n      }\n    }\n  }\n\n  .step-actions {\n    display: flex;\n    gap: 12px;\n    align-items: center;\n  }\n\n  .next-btn,\n  .complete-btn {\n    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n    border: none;\n    color: white;\n\n    &:hover {\n      background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);\n      transform: translateY(-1px);\n      box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);\n    }\n\n    .anticon {\n      margin-left: 8px;\n    }\n  }\n}\n\n// 已创建智能体信息\n.created-agent-info {\n  margin-bottom: 24px;\n\n  .agent-summary {\n    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);\n    border: 2px solid #0ea5e9;\n    border-radius: 12px;\n    padding: 20px;\n    display: flex;\n    align-items: center;\n    gap: 16px;\n\n    .agent-avatar {\n      width: 60px;\n      height: 60px;\n      border-radius: 12px;\n      overflow: hidden;\n      background: white;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      border: 2px solid #0ea5e9;\n\n      img {\n        width: 100%;\n        height: 100%;\n        object-fit: cover;\n      }\n\n      .anticon {\n        font-size: 24px;\n        color: #0ea5e9;\n      }\n    }\n\n    .agent-details {\n      flex: 1;\n\n      h4 {\n        margin: 0 0 4px 0;\n        font-size: 18px;\n        font-weight: 700;\n        color: #0c4a6e;\n      }\n\n      p {\n        margin: 0 0 8px 0;\n        font-size: 14px;\n        color: #0369a1;\n        line-height: 1.4;\n      }\n\n      .agent-price {\n        font-size: 16px;\n        font-weight: 600;\n        color: #059669;\n        background: rgba(5, 150, 105, 0.1);\n        padding: 4px 8px;\n        border-radius: 6px;\n      }\n    }\n  }\n}\n\n// 工作流管理区域\n.workflow-section {\n  border: 1px solid #e8eaec;\n  border-radius: 8px;\n  padding: 16px;\n  background: #fafbfc;\n\n  .workflow-notice {\n    margin-bottom: 16px;\n  }\n\n  .workflow-form {\n    .form-header {\n      margin-bottom: 24px;\n      text-align: center;\n\n      h3 {\n        margin: 0 0 8px 0;\n        font-size: 18px;\n        font-weight: 600;\n        color: #333;\n\n        .anticon {\n          margin-right: 8px;\n          color: #1890ff;\n        }\n      }\n\n      p {\n        margin: 0;\n        font-size: 14px;\n        color: #666;\n      }\n    }\n\n    .ant-form-item {\n      margin-bottom: 20px;\n    }\n\n    .upload-tip {\n      margin-top: 8px;\n      padding: 8px 12px;\n      font-size: 12px;\n      color: #666;\n      background: #fff7e6;\n      border: 1px solid #ffd591;\n      border-radius: 4px;\n      line-height: 1.4;\n\n      .anticon {\n        margin-right: 4px;\n      }\n\n      strong {\n        color: #fa8c16;\n      }\n    }\n  }\n\n  .add-next-workflow {\n    margin-top: 20px;\n    padding-top: 16px;\n    border-top: 1px dashed #e8eaec;\n\n    .add-next-btn {\n      height: 48px;\n      border: 2px dashed #52c41a;\n      color: #52c41a;\n      font-size: 14px;\n      font-weight: 500;\n      transition: all 0.3s;\n\n      &:hover {\n        border-color: #389e0d;\n        color: #389e0d;\n        background: #f6ffed;\n      }\n\n      .anticon {\n        margin-right: 8px;\n      }\n    }\n  }\n\n  .workflow-header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    margin-bottom: 16px;\n\n    .workflow-title {\n      font-size: 14px;\n      font-weight: 600;\n      color: #333;\n\n      .anticon {\n        margin-right: 6px;\n        color: #1890ff;\n      }\n    }\n  }\n\n  .workflow-list {\n    .workflow-item {\n      background: white;\n      border: 1px solid #e8eaec;\n      border-radius: 6px;\n      padding: 12px;\n      margin-bottom: 8px;\n      display: flex;\n      justify-content: space-between;\n      align-items: flex-start;\n      transition: all 0.3s ease;\n\n      &:hover {\n        border-color: #1890ff;\n        box-shadow: 0 2px 8px rgba(24, 144, 255, 0.1);\n      }\n\n      &:last-child {\n        margin-bottom: 0;\n      }\n\n      &.pending {\n        border-color: #faad14;\n        background: #fffbe6;\n\n        &:hover {\n          border-color: #faad14;\n          box-shadow: 0 2px 8px rgba(250, 173, 20, 0.2);\n        }\n      }\n\n      .workflow-info {\n        flex: 1;\n\n        .workflow-name {\n          margin: 0 0 4px 0;\n          font-size: 14px;\n          font-weight: 600;\n          color: #333;\n        }\n\n        .workflow-desc {\n          margin: 0 0 4px 0;\n          font-size: 12px;\n          color: #666;\n          line-height: 1.4;\n        }\n\n        .workflow-time {\n          font-size: 11px;\n          color: #999;\n        }\n\n        .workflow-status {\n          font-size: 11px;\n          color: #faad14;\n          background: #fff7e6;\n          padding: 2px 6px;\n          border-radius: 4px;\n          border: 1px solid #ffd591;\n        }\n      }\n\n      .workflow-actions {\n        display: flex;\n        gap: 8px;\n\n        .ant-btn {\n          padding: 4px 8px;\n          height: auto;\n          font-size: 12px;\n        }\n      }\n    }\n  }\n\n  // 🔥 暂存工作流列表特殊样式\n  .temp-workflows {\n    .workflow-item {\n      &.editing {\n        border-color: #1890ff;\n        background: #f0f8ff;\n        box-shadow: 0 2px 8px rgba(24, 144, 255, 0.15);\n      }\n\n      // 🔥 验证错误状态样式\n      &.has-error {\n        border-color: #ff4d4f;\n        background: #fff2f0;\n        box-shadow: 0 2px 8px rgba(255, 77, 79, 0.15);\n\n        .workflow-name {\n          color: #ff4d4f;\n        }\n      }\n\n      .workflow-meta {\n        display: flex;\n        gap: 16px;\n        font-size: 12px;\n        color: #999;\n        margin-top: 8px;\n\n        .workflow-file, .workflow-size {\n          display: flex;\n          align-items: center;\n        }\n      }\n    }\n  }\n\n  // 🔥 工作流文件上传样式\n  .workflow-file-upload {\n    .uploaded-file-info {\n      .file-item {\n        display: flex;\n        align-items: center;\n        padding: 8px 12px;\n        background: #f5f5f5;\n        border: 1px solid #d9d9d9;\n        border-radius: 6px;\n        margin-bottom: 8px;\n\n        .file-icon {\n          color: #1890ff;\n          margin-right: 8px;\n          font-size: 16px;\n        }\n\n        .file-name {\n          flex: 1;\n          font-size: 14px;\n          color: #333;\n          margin-right: 8px;\n        }\n\n        .remove-btn {\n          padding: 4px 8px;\n          font-size: 12px;\n        }\n\n        // 🔥 已保存文件的特殊样式\n        &.saved-file {\n          background: #f6ffed;\n          border-color: #b7eb8f;\n\n          .file-icon {\n            color: #52c41a;\n          }\n\n          .file-name {\n            color: #389e0d;\n            font-weight: 500;\n          }\n        }\n      }\n\n      // 🔥 验证错误提示样式\n      .workflow-errors {\n        margin-top: 12px;\n        margin-bottom: 8px;\n\n        .ant-alert {\n          border-radius: 4px;\n\n          .ant-alert-message {\n            font-size: 12px;\n            line-height: 1.4;\n          }\n        }\n      }\n    }\n  }\n\n  .workflow-empty {\n    text-align: center;\n    padding: 20px;\n\n    .ant-empty {\n      margin: 0;\n    }\n  }\n\n  .workflow-add-item {\n    margin-top: 16px;\n\n    .add-workflow-btn {\n      height: 48px;\n      border: 2px dashed #d9d9d9;\n      color: #666;\n      font-size: 14px;\n      transition: all 0.3s;\n\n      &:hover {\n        border-color: #1890ff;\n        color: #1890ff;\n      }\n\n      .anticon {\n        margin-right: 8px;\n      }\n    }\n  }\n}\n\n// 响应式设计\n@media (max-width: 768px) {\n  :global(.creator-agent-modal) {\n    .ant-modal {\n      width: 95% !important;\n      margin: 10px auto;\n    }\n\n    .ant-modal-body {\n      padding: 16px;\n    }\n  }\n\n  .modal-footer {\n    padding: 12px 16px;\n\n    .ant-btn {\n      width: 48%;\n      margin-left: 4%;\n\n      &:first-child {\n        margin-left: 0;\n      }\n    }\n  }\n\n  .workflow-section {\n    .workflow-item {\n      flex-direction: column;\n      gap: 12px;\n\n      .workflow-actions {\n        align-self: stretch;\n        justify-content: flex-end;\n      }\n    }\n  }\n}\n\n// 第三步成功页面样式\n.success-content {\n  text-align: center;\n  padding: 40px 20px;\n\n  .success-icon {\n    margin-bottom: 24px;\n  }\n\n  .success-message {\n    h2 {\n      color: #52c41a;\n      font-size: 24px;\n      font-weight: 600;\n      margin-bottom: 16px;\n    }\n\n    p {\n      color: #666;\n      font-size: 14px;\n      line-height: 1.6;\n      margin-bottom: 8px;\n\n      &:last-child {\n        margin-bottom: 0;\n      }\n    }\n  }\n}\n</style>\n"]}]}