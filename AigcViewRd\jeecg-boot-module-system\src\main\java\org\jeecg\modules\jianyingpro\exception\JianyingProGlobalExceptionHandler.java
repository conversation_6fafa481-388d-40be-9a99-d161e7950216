package org.jeecg.modules.jianyingpro.exception;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.jianyingpro.enums.JianyingProErrorCode;
import org.jeecg.modules.jianyingpro.util.JianyingProResponseUtil;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import java.util.stream.Collectors;

/**
 * 超级剪映小助手全局异常处理器
 * 统一处理所有异常并返回标准格式的错误响应
 * 
 * <AUTHOR>
 * @since 2025-01-01
 */
@Slf4j
@RestControllerAdvice(basePackages = "org.jeecg.modules.jianyingpro")
public class JianyingProGlobalExceptionHandler {
    
    /**
     * 处理参数验证异常 - @Valid注解触发
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity<JSONObject> handleValidationException(MethodArgumentNotValidException e) {
        log.warn("参数验证失败: {}", e.getMessage());
        
        // 收集所有验证错误信息
        String errorMessage = e.getBindingResult().getFieldErrors().stream()
            .map(FieldError::getDefaultMessage)
            .collect(Collectors.joining("; "));
        
        JSONObject response = JianyingProResponseUtil.error(
            JianyingProErrorCode.PARAM_INCOMPLETE_003,
            "参数验证失败: " + errorMessage,
            "请检查请求参数格式和内容"
        );
        
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
    }
    
    /**
     * 处理绑定异常
     */
    @ExceptionHandler(BindException.class)
    public ResponseEntity<JSONObject> handleBindException(BindException e) {
        log.warn("参数绑定失败: {}", e.getMessage());
        
        String errorMessage = e.getFieldErrors().stream()
            .map(FieldError::getDefaultMessage)
            .collect(Collectors.joining("; "));
        
        JSONObject response = JianyingProResponseUtil.error(
            JianyingProErrorCode.PARAM_FORMAT_004,
            "参数绑定失败: " + errorMessage,
            "请检查请求参数格式"
        );
        
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
    }
    
    /**
     * 处理约束违反异常
     */
    @ExceptionHandler(ConstraintViolationException.class)
    public ResponseEntity<JSONObject> handleConstraintViolationException(ConstraintViolationException e) {
        log.warn("约束验证失败: {}", e.getMessage());
        
        String errorMessage = e.getConstraintViolations().stream()
            .map(ConstraintViolation::getMessage)
            .collect(Collectors.joining("; "));
        
        JSONObject response = JianyingProResponseUtil.error(
            JianyingProErrorCode.PARAM_INVALID_002,
            "约束验证失败: " + errorMessage,
            "请检查参数值是否符合要求"
        );
        
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
    }
    
    /**
     * 处理非法参数异常
     */
    @ExceptionHandler(IllegalArgumentException.class)
    public ResponseEntity<JSONObject> handleIllegalArgumentException(IllegalArgumentException e) {
        log.warn("非法参数: {}", e.getMessage());
        
        JSONObject response = JianyingProResponseUtil.error(
            JianyingProErrorCode.PARAM_INVALID_002,
            "参数错误: " + e.getMessage(),
            "请检查参数值是否正确"
        );
        
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
    }
    
    /**
     * 处理空指针异常
     */
    @ExceptionHandler(NullPointerException.class)
    public ResponseEntity<JSONObject> handleNullPointerException(NullPointerException e) {
        log.error("空指针异常", e);
        
        JSONObject response = JianyingProResponseUtil.error(
            JianyingProErrorCode.SYSTEM_ERROR_500,
            "系统内部错误: 空指针异常",
            "系统内部错误，请联系管理员"
        );
        
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
    }
    
    /**
     * 处理运行时异常
     */
    @ExceptionHandler(RuntimeException.class)
    public ResponseEntity<JSONObject> handleRuntimeException(RuntimeException e) {
        log.error("运行时异常: {}", e.getMessage(), e);
        
        // 判断是否为业务异常
        String message = e.getMessage();
        if (message != null && (message.contains("下载失败") || message.contains("上传失败") || 
                               message.contains("处理失败") || message.contains("生成失败"))) {
            // 业务异常
            JSONObject response = JianyingProResponseUtil.error(
                JianyingProErrorCode.BUSINESS_AUDIO_PROCESS_101,
                "业务处理失败: " + message,
                "请检查输入参数和网络连接后重试"
            );
            return ResponseEntity.status(HttpStatus.UNPROCESSABLE_ENTITY).body(response);
        } else {
            // 系统异常
            JSONObject response = JianyingProResponseUtil.error(
                JianyingProErrorCode.SYSTEM_ERROR_500,
                "系统运行时错误: " + message,
                "系统内部错误，请联系管理员"
            );
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }
    
    /**
     * 处理所有其他异常
     */
    @ExceptionHandler(Exception.class)
    public ResponseEntity<JSONObject> handleGenericException(Exception e) {
        log.error("未处理的异常", e);
        
        JSONObject response = JianyingProResponseUtil.error(
            JianyingProErrorCode.SYSTEM_UNKNOWN_504,
            "未知系统错误: " + e.getMessage(),
            "系统发生未知错误，请联系管理员"
        );
        
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
    }
    
    /**
     * 处理自定义业务异常（如果需要的话）
     */
    @ExceptionHandler(JianyingProBusinessException.class)
    public ResponseEntity<JSONObject> handleBusinessException(JianyingProBusinessException e) {
        log.warn("业务异常: {}", e.getMessage());
        
        JSONObject response = JianyingProResponseUtil.error(
            e.getErrorCode(),
            e.getMessage(),
            e.getSolution()
        );
        
        int httpStatus = JianyingProResponseUtil.getHttpStatus(response);
        return ResponseEntity.status(httpStatus).body(response);
    }
}
