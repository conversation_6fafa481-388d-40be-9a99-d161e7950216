# 剪映小助手API文档

## 📋 项目概述

剪映小助手是智界Aigc平台的核心功能模块，提供了完整的剪映视频编辑API接口。通过这些API，开发者可以实现自动化的视频创建、编辑和渲染功能。

### 🎯 主要功能

- **智界工具箱**：17个核心API，包括草稿创建、素材添加、视频渲染等
- **智界数据箱**：14个数据处理API，包括时间线生成、数据转换等
- **统一认证**：基于access_key的安全验证机制
- **参数验证**：完整的请求参数验证和错误处理
- **异常处理**：统一的异常处理和错误响应格式

## 🚀 快速开始

### 环境要求

- **Java**: JDK 8+ (推荐JDK 11)
- **数据库**: MySQL 5.7+
- **缓存**: Redis 3.0+
- **构建工具**: Maven 3.6+

### 安装部署

1. **克隆项目**
```bash
git clone https://github.com/your-repo/aigcview.git
cd aigcview
```

2. **配置数据库**
```sql
-- 创建数据库
CREATE DATABASE aigcview CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 导入数据表结构
mysql -u root -p aigcview < db/aigcview.sql
```

3. **配置应用**
```yaml
# application-prod.yml
spring:
  datasource:
    url: ************************************
    username: root
    password: your_password
  redis:
    host: localhost
    port: 6379
```

4. **启动应用**
```bash
mvn clean package
java -jar target/jeecg-boot-module-system-2.4.6.jar
```

### API访问地址

- **开发环境**: `http://localhost:8080/jeecg-boot/api/jianying/`
- **生产环境**: `https://www.aigcview.cn/jeecg-boot/api/jianying/`
- **API文档**: `http://localhost:8080/jeecg-boot/doc.html`

## 🔐 认证机制

### Access Key验证

所有剪映API都需要在请求体中包含`access_key`参数：

```json
{
  "access_key": "JianyingAPI_2025_SecureAccess_AigcView",
  "zj_height": 1080,
  "zj_width": 1920
}
```

### 安全特性

- **统一验证**: 通过AOP切面自动验证所有API请求
- **绕过Shiro**: 剪映API路径已配置绕过JWT Token验证
- **错误处理**: 无效access_key返回401状态码
- **日志记录**: 完整的访问日志和安全审计

## 📚 API接口文档

### 智界工具箱 (17个API)

#### 1. 创建草稿
```http
POST /api/jianying/create_draft
Content-Type: application/json

{
  "access_key": "JianyingAPI_2025_SecureAccess_AigcView",
  "zj_height": 1080,
  "zj_width": 1920,
  "zj_user_id": 12345
}
```

#### 2. 快速创建素材轨道
```http
POST /api/jianying/easy_create_material
Content-Type: application/json

{
  "access_key": "JianyingAPI_2025_SecureAccess_AigcView",
  "zj_video_url": "https://example.com/video.mp4",
  "zj_text": "示例文本",
  "zj_image_url": "https://example.com/image.jpg",
  "zj_audio_url": "https://example.com/audio.mp3",
  "zj_draft_url": "https://example.com/draft/123"
}
```

#### 3. 添加贴纸
```http
POST /api/jianying/add_sticker
Content-Type: application/json

{
  "access_key": "JianyingAPI_2025_SecureAccess_AigcView",
  "zj_sticker_id": "sticker_123",
  "zj_draft_url": "https://example.com/draft/123",
  "zj_start_time": 0,
  "zj_end_time": 5000000
}
```

#### 4. 云渲染视频
```http
POST /api/jianying/gen_video
Content-Type: application/json

{
  "access_key": "JianyingAPI_2025_SecureAccess_AigcView",
  "zj_api_token": "your_api_token",
  "zj_draft_url": "https://example.com/draft/123"
}
```

### 智界数据箱 (14个API)

#### 1. 时间线生成器
```http
POST /api/jianying/timelines
Content-Type: application/json

{
  "access_key": "JianyingAPI_2025_SecureAccess_AigcView",
  "zj_duration": 10000000,
  "zj_num": 5
}
```

#### 2. 贴纸搜索器
```http
POST /api/jianying/search_sticker
Content-Type: application/json

{
  "access_key": "JianyingAPI_2025_SecureAccess_AigcView",
  "zj_keyword": "爱心"
}
```

## 🛠️ 开发指南

### 项目结构

```
org.jeecg.modules.jianying/
├── aspect/                 # AOP切面
│   └── JianyingAccessKeyAspect.java
├── controller/             # 控制器
│   ├── JianyingToolboxController.java
│   └── JianyingDataboxController.java
├── dto/                    # 请求参数类
│   ├── BaseJianyingRequest.java
│   ├── CreateDraftRequest.java
│   └── ...
├── exception/              # 异常处理
│   └── JianyingExceptionHandler.java
└── service/               # 业务服务
    ├── JianyingAssistantService.java
    └── JianyingDataboxService.java
```

### 添加新API

1. **创建Request类**
```java
@Data
@EqualsAndHashCode(callSuper = true)
public class NewApiRequest extends BaseJianyingRequest {
    @ApiModelProperty(value = "参数说明", required = true)
    @NotBlank(message = "参数不能为空")
    private String zjParameter;
    
    @Override
    public String getSummary() {
        return "NewApiRequest{" +
               "accessKey=" + (getAccessKey() != null && getAccessKey().length() > 10 ? 
                              getAccessKey().substring(0, 10) + "***" : "null") +
               ", parameter=" + zjParameter +
               "}";
    }
}
```

2. **添加Controller方法**
```java
@ApiOperation(value = "新API", notes = "新API功能说明")
@PostMapping("/new_api")
public Result<?> newApi(@Valid @RequestBody NewApiRequest request) {
    try {
        log.info("新API请求: {}", request.getSummary());
        
        JSONObject result = jianyingAssistantService.newApi(request.getZjParameter());
        
        if (result.getBoolean("success")) {
            return Result.OK("操作成功", result.getJSONObject("data"));
        } else {
            return Result.error(result.getString("error"));
        }
        
    } catch (Exception e) {
        log.error("新API执行失败", e);
        return Result.error("操作失败: " + e.getMessage());
    }
}
```

### 测试开发

1. **创建测试用例**
```java
@Test
public void testNewApi() throws Exception {
    NewApiRequest request = new NewApiRequest();
    request.setAccessKey(JianyingAccessKeyAspect.getExpectedAccessKey());
    request.setZjParameter("test_value");
    
    String requestJson = JSONObject.toJSONString(request);
    
    mockMvc.perform(MockMvcRequestBuilders.post("/api/jianying/new_api")
            .contentType(MediaType.APPLICATION_JSON)
            .content(requestJson))
            .andDo(print())
            .andExpect(MockMvcResultMatchers.status().isOk())
            .andExpect(MockMvcResultMatchers.jsonPath("$.success").value(true));
}
```

2. **运行测试**
```bash
# 运行所有测试
mvn test

# 运行特定测试
mvn test -Dtest="JianyingApiTest"
```

## 🔧 配置说明

### Shiro安全配置

```java
// ShiroConfig.java
filterChainDefinitionMap.put("/jeecg-boot/api/jianying/**", "anon");
```

```yaml
# application.yml
jeecg:
  shiro:
    excludeUrls: /api/jianying/**
```

### 日志配置

```yaml
# logback-spring.xml
logging:
  level:
    org.jeecg.modules.jianying: DEBUG
```

### 自定义配置

```yaml
# application.yml
jianying:
  access-key: JianyingAPI_2025_SecureAccess_AigcView
  enable-validation: true
  log-requests: true
```

## 📊 监控和运维

### 健康检查

```http
GET /jeecg-boot/actuator/health
```

### 日志监控

- **访问日志**: 记录所有API访问
- **错误日志**: 记录异常和错误
- **安全日志**: 记录access_key验证

### 性能监控

- **响应时间**: 监控API响应时间
- **并发量**: 监控并发请求数量
- **错误率**: 监控API错误率

## 🚨 故障排查

### 常见问题

1. **access_key无效**
   - 检查access_key是否正确
   - 确认请求格式是否为JSON
   - 验证字段名是否为`access_key`

2. **参数验证失败**
   - 检查必填参数是否提供
   - 确认参数类型是否正确
   - 查看详细错误信息

3. **404错误**
   - 确认API路径是否正确
   - 检查应用是否正常启动
   - 验证上下文路径配置

### 调试方法

1. **启用DEBUG日志**
```yaml
logging:
  level:
    org.jeecg.modules.jianying: DEBUG
```

2. **查看详细错误**
```bash
tail -f logs/jeecg-boot.log | grep jianying
```

3. **使用API测试工具**
```bash
curl -X POST "http://localhost:8080/jeecg-boot/api/jianying/create_draft" \
     -H "Content-Type: application/json" \
     -d '{"access_key":"JianyingAPI_2025_SecureAccess_AigcView","zj_height":1080}'
```

## 🎯 完整API列表

### 智界工具箱API (17个)

| API名称 | 路径 | 功能说明 |
|---------|------|----------|
| 创建草稿 | `/create_draft` | 创建新的剪映草稿 |
| 快速创建素材轨道 | `/easy_create_material` | 快速创建包含多种素材的轨道 |
| 获取图片动画 | `/get_image_animations` | 获取图片出入场动画列表 |
| 添加贴纸 | `/add_sticker` | 向草稿中添加贴纸 |
| 获取音频时长 | `/get_audio_duration` | 获取音频文件时长信息 |
| 云渲染视频 | `/gen_video` | 云端渲染视频 |
| 批量添加音频 | `/add_audios` | 批量添加音频文件 |
| 添加特效 | `/add_effects` | 添加视频特效 |
| 批量添加字幕 | `/add_captions` | 批量添加字幕 |
| 批量添加图片 | `/add_images` | 批量添加图片 |
| 批量添加视频 | `/add_videos` | 批量添加视频 |
| 创建文本样式 | `/add_text_style` | 创建富文本样式 |
| 增加蒙版 | `/add_masks` | 添加视频蒙版 |
| 保存草稿 | `/save_draft` | 保存草稿到存储 |
| 添加关键帧 | `/add_keyframes` | 添加动画关键帧 |
| 获取文字动画 | `/get_text_animations` | 获取文字出入场动画 |
| 查询视频状态 | `/gen_video_status` | 查询渲染状态 |

### 智界数据箱API (14个)

| API名称 | 路径 | 功能说明 |
|---------|------|----------|
| 关键帧数据生成器 | `/keyframes_infos` | 生成关键帧数据 |
| 图片数据生成器 | `/imgs_infos` | 生成图片轨道数据 |
| 链接提取器 | `/get_url` | 从文本提取链接 |
| 音频时间线生成器 | `/audio_timelines` | 生成音频时间线 |
| 贴纸搜索器 | `/search_sticker` | 搜索贴纸资源 |
| 时间线生成器 | `/timelines` | 自定义时间线 |
| 音频数据生成器 | `/audio_infos` | 生成音频轨道数据 |
| 特效数据生成器 | `/effect_infos` | 生成特效数据 |
| 字幕数据生成器 | `/caption_infos` | 生成字幕数据 |
| 视频数据生成器 | `/video_infos` | 生成视频轨道数据 |
| 字符串列表转对象 | `/str_list_to_objs` | 数据格式转换 |
| 对象转字符串列表 | `/objs_to_str_list` | 数据格式转换 |
| 字符串转列表 | `/str_to_list` | 字符串分割转换 |
| 语音识别时间线 | `/asr_timelines` | 生成语音识别时间线 |

## 📞 技术支持

- **官方网站**: https://www.aigcview.cn
- **API文档**: https://www.aigcview.cn/jeecg-boot/doc.html
- **技术支持**: 智界Aigc开发团队

---

**版本**: v1.0.0
**更新时间**: 2025-01-01
**维护团队**: AigcView开发团队
