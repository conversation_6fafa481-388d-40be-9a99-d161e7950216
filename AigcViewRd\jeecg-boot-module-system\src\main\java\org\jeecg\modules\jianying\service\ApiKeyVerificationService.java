package org.jeecg.modules.jianying.service;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.api.dto.PluginVerifyResult;
import org.jeecg.modules.api.service.IAigcApiService;
import org.jeecg.modules.demo.plubshop.entity.AigcPlubShop;
import org.jeecg.modules.demo.plubshop.service.IAigcPlubShopService;
import org.jeecg.modules.demo.userprofile.entity.AicgUserProfile;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 剪映小助手API Key验证服务
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
@Slf4j
@Service
public class ApiKeyVerificationService {
    
    @Autowired
    private IAigcApiService aigcApiService;
    
    @Autowired
    private IAigcPlubShopService plubShopService;
    
    /**
     * 验证API Key（不扣费）
     * 
     * @param apiKey API密钥
     * @return 验证结果
     */
    public JSONObject verifyApiKey(String apiKey) {
        try {
            log.info("验证API Key: {}", apiKey);
            
            // 基础验证
            if (apiKey == null || apiKey.isEmpty()) {
                return createErrorResult("API-Key不能为空");
            }
            
            if (!apiKey.startsWith("ak_") || apiKey.length() != 35) {
                return createErrorResult("API-Key格式错误");
            }
            
            // 查询用户信息
            AicgUserProfile userProfile = aigcApiService.getUserByApiKey(apiKey);
            if (userProfile == null) {
                return createErrorResult("API-Key无效");
            }
            
            // 检查用户状态
            if (userProfile.getStatus() != 1) {
                return createErrorResult("用户状态异常，API-Key已被禁用");
            }
            
            // 返回成功结果
            JSONObject result = new JSONObject();
            result.put("success", true);
            result.put("message", "验证成功");
            result.put("data", new JSONObject() {{
                put("valid", true);
                put("userId", userProfile.getUserId());
                put("nickname", userProfile.getNickname());
                put("balance", userProfile.getAccountBalance());
                put("frozenBalance", userProfile.getFrozenBalance());
                put("availableBalance", userProfile.getAccountBalance().subtract(userProfile.getFrozenBalance()));
            }});
            
            return result;
            
        } catch (Exception e) {
            log.error("API Key验证失败", e);
            return createErrorResult("API Key验证失败: " + e.getMessage());
        }
    }
    
    /**
     * 验证API Key并检查插件权限（会扣费）
     * 
     * @param apiKey API密钥
     * @param pluginKey 插件标识
     * @return 验证结果
     */
    public JSONObject verifyApiKeyWithPlugin(String apiKey, String pluginKey) {
        try {
            log.info("验证API Key并检查插件权限: apiKey={}, pluginKey={}", apiKey, pluginKey);
            
            // 先进行基础验证
            JSONObject baseResult = verifyApiKey(apiKey);
            if (!baseResult.getBoolean("success")) {
                return baseResult;
            }
            
            // 获取用户信息
            AicgUserProfile userProfile = aigcApiService.getUserByApiKey(apiKey);
            String userId = userProfile.getUserId();
            
            // 验证插件并扣费
            PluginVerifyResult pluginVerifyResult = aigcApiService.verifyPluginAndDeduct(userId, pluginKey);
            
            if (pluginVerifyResult.isSuccess()) {
                // 插件验证成功，返回完整信息
                JSONObject result = new JSONObject();
                result.put("success", true);
                result.put("message", "验证成功，已扣费");
                result.put("data", new JSONObject() {{
                    put("valid", true);
                    put("userId", userId);
                    put("nickname", userProfile.getNickname());
                    put("pluginVerified", true);
                    put("pluginName", pluginVerifyResult.getPluginName());
                    put("deductedAmount", pluginVerifyResult.getNeedAmount());
                    put("balanceAfter", pluginVerifyResult.getBalanceAfter());
                }});
                
                return result;
            } else {
                return createErrorResult(pluginVerifyResult.getErrorMessage());
            }
            
        } catch (Exception e) {
            log.error("API Key和插件验证失败", e);
            return createErrorResult("验证失败: " + e.getMessage());
        }
    }
    
    /**
     * 检查插件是否存在且可用（不扣费）
     * 
     * @param pluginKey 插件标识
     * @return 检查结果
     */
    public JSONObject checkPluginAvailability(String pluginKey) {
        try {
            log.info("检查插件可用性: {}", pluginKey);
            
            AigcPlubShop plugin = plubShopService.getByPluginKey(pluginKey);
            if (plugin == null) {
                return createErrorResult("插件不存在: " + pluginKey);
            }
            
            if (plugin.getStatus() != 1) {
                return createErrorResult("插件已禁用: " + pluginKey);
            }
            
            JSONObject result = new JSONObject();
            result.put("success", true);
            result.put("message", "插件可用");
            result.put("data", new JSONObject() {{
                put("pluginKey", pluginKey);
                put("pluginName", plugin.getPlubname());
                put("price", plugin.getNeednum());
                put("available", true);
            }});
            
            return result;
            
        } catch (Exception e) {
            log.error("检查插件可用性失败", e);
            return createErrorResult("检查插件失败: " + e.getMessage());
        }
    }
    
    /**
     * 创建错误结果
     */
    private JSONObject createErrorResult(String message) {
        JSONObject result = new JSONObject();
        result.put("success", false);
        result.put("error", message);
        result.put("code", "VERIFICATION_ERROR");
        return result;
    }
}
