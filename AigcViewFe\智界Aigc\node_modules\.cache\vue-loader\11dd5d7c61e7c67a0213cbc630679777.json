{"remainingRequest": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\workflow\\components\\CreatorCenter.vue?vue&type=template&id=bfaacd0c&scoped=true&", "dependencies": [{"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\src\\views\\website\\workflow\\components\\CreatorCenter.vue", "mtime": 1754512989758}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\AigcView_zj\\AigcViewFe\\智界Aigc\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": ["\n<div class=\"creator-center\">\n  <!-- 页面头部 -->\n  <div class=\"creator-header\">\n    <div class=\"header-content\">\n      <div class=\"header-left\">\n        <h1 class=\"page-title\">\n          <a-icon type=\"user\" />\n          创作者中心\n        </h1>\n        <p class=\"page-subtitle\">管理您的智能体，查看收益统计（全部收益归创作者，但VIP和SVIP用户分别是价格的7折和5折，如您选择发布默认同意此方案）</p>\n      </div>\n      <div class=\"header-right\">\n        <a-button type=\"primary\" size=\"large\" @click=\"handleCreateAgent\" :loading=\"creating\">\n          <a-icon type=\"plus\" />\n          新增智能体\n        </a-button>\n      </div>\n    </div>\n  </div>\n\n  <!-- 收益统计卡片 -->\n  <div class=\"stats-cards-section\">\n    <div class=\"section-header\">\n      <h2 class=\"section-title\">收益统计</h2>\n      <div class=\"section-actions\">\n        <a-button\n          type=\"default\"\n          @click=\"openWithdrawRecordsModal\"\n          class=\"records-btn\"\n        >\n          <a-icon type=\"history\" />\n          查看提现记录\n        </a-button>\n      </div>\n    </div>\n\n    <RevenueStatsCards\n      :loading=\"statsLoading\"\n      :data=\"revenueStats\"\n      @refresh=\"loadRevenueStats\"\n      @withdraw=\"handleWithdraw\"\n    />\n  </div>\n\n  <!-- 智能体管理区域 -->\n  <div class=\"agents-section\">\n    <div class=\"section-header\">\n      <h2 class=\"section-title\">我的智能体</h2>\n\n    </div>\n\n    <!-- 智能体列表 -->\n    <AgentManagement\n      :loading=\"agentsLoading\"\n      :agents=\"agentList\"\n      :pagination=\"pagination\"\n      @refresh=\"loadAgentList\"\n      @edit=\"handleEditAgent\"\n      @delete=\"handleDeleteAgent\"\n\n      @page-change=\"handlePageChange\"\n      @search=\"handleAgentSearch\"\n      @filter-change=\"handleAgentFilter\"\n      @sort-change=\"handleSortChange\"\n    />\n  </div>\n\n  <!-- 智能体收益排行 -->\n  <div class=\"ranking-section\">\n    <RevenueRanking\n      :loading=\"statsLoading\"\n      :data=\"revenueStats\"\n    />\n  </div>\n\n  <!-- 智能体表单弹窗 -->\n  <CreatorAgentForm\n    ref=\"agentForm\"\n    :visible=\"formVisible\"\n    :loading=\"formLoading\"\n    :agent=\"currentAgent\"\n    :mode=\"formMode\"\n    @close=\"handleCloseForm\"\n    @submit=\"handleSubmitForm\"\n    @complete=\"handleAgentComplete\"\n\n    @delete-workflow=\"handleDeleteWorkflowFromForm\"\n  />\n\n\n\n  <!-- 🔥 提现弹窗 -->\n  <WithdrawModal\n    :visible=\"showWithdrawModal\"\n    :available-amount=\"withdrawAvailableAmount\"\n    :revenue-type=\"withdrawRevenueType\"\n    :loading=\"withdrawLoading\"\n    @submit=\"handleWithdrawSubmit\"\n    @cancel=\"handleWithdrawCancel\"\n  />\n\n  <!-- 🔥 提现记录弹窗 -->\n  <WithdrawRecordsModal\n    :visible=\"showWithdrawRecordsModal\"\n    :revenue-type=\"'agent'\"\n    @cancel=\"handleWithdrawRecordsCancel\"\n    @refresh=\"loadRevenueStats\"\n  />\n</div>\n", null]}