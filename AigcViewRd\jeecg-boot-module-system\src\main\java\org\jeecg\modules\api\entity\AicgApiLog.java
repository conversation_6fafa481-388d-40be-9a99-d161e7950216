package org.jeecg.modules.api.entity;

import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: API调用日志
 * @Author: jeecg-boot
 * @Date: 2025-06-14
 * @Version: V1.0
 */
@Data
@TableName("aicg_api_log")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="aicg_api_log对象", description="API调用日志")
public class AicgApiLog implements Serializable {
    private static final long serialVersionUID = 1L;

    /**主键*/
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String id;

    /**用户ID*/
    @Excel(name = "用户ID", width = 15)
    @ApiModelProperty(value = "用户ID")
    private String userId;

    /**API类型*/
    @Excel(name = "API类型", width = 15)
    @ApiModelProperty(value = "API类型")
    private String apiType;

    /**API名称*/
    @Excel(name = "API名称", width = 15)
    @ApiModelProperty(value = "API名称")
    private String apiName;

    /**请求URL*/
    @Excel(name = "请求URL", width = 30)
    @ApiModelProperty(value = "请求URL")
    private String requestUrl;

    /**请求方法*/
    @Excel(name = "请求方法", width = 10)
    @ApiModelProperty(value = "请求方法")
    private String requestMethod;

    /**请求参数*/
    @ApiModelProperty(value = "请求参数")
    private String requestParams;

    /**响应数据*/
    @ApiModelProperty(value = "响应数据")
    private String responseData;

    /**HTTP状态码*/
    @Excel(name = "状态码", width = 10)
    @ApiModelProperty(value = "HTTP状态码")
    private Integer statusCode;

    /**是否成功*/
    @Excel(name = "是否成功", width = 10, replace = {"成功_1", "失败_0"})
    @ApiModelProperty(value = "是否成功：0-失败，1-成功")
    private Boolean success;

    /**错误信息*/
    @Excel(name = "错误信息", width = 30)
    @ApiModelProperty(value = "错误信息")
    private String errorMessage;

    /**消耗积分*/
    @Excel(name = "消耗积分", width = 15)
    @ApiModelProperty(value = "消耗积分")
    private BigDecimal costPoints;

    /**请求时间*/
    @Excel(name = "请求时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "请求时间")
    private Date requestTime;

    /**响应时间*/
    @Excel(name = "响应时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "响应时间")
    private Date responseTime;

    /**耗时(毫秒)*/
    @Excel(name = "耗时(毫秒)", width = 15)
    @ApiModelProperty(value = "耗时(毫秒)")
    private Long durationMs;

    /**IP地址*/
    @Excel(name = "IP地址", width = 15)
    @ApiModelProperty(value = "IP地址")
    private String ipAddress;

    /**用户代理*/
    @ApiModelProperty(value = "用户代理")
    private String userAgent;

    /**创建时间*/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**更新时间*/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
}
