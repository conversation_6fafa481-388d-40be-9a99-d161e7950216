import { StorageClass, ServerSideEncryption, Acl } from '../../interface';
import TOSBase, { TosResponse } from '../base';
import { StorageClassType } from '../../TosExportEnum';
export interface CopyObjectInput {
    bucket?: string;
    key: string;
    srcBucket?: string;
    srcKey?: string;
    srcVersionID?: string;
    cacheControl?: string;
    contentDisposition?: string;
    contentEncoding?: string;
    contentLanguage?: string;
    contentType?: string;
    expires?: Date;
    copySourceIfMatch?: string;
    copySourceIfModifiedSince?: string | Date;
    copySourceIfNoneMatch?: string;
    copySourceIfUnmodifiedSince?: string;
    copySourceSSECAlgorithm?: string;
    copySourceSSECKey?: string;
    copySourceSSECKeyMD5?: string;
    ssecAlgorithm?: string;
    ssecKey?: string;
    ssecKeyMD5?: string;
    serverSideEncryption?: string;
    /**
     * unit: bit/s
     * server side traffic limit
     **/
    trafficLimit?: number;
    acl?: Acl;
    grantFullControl?: string;
    grantRead?: string;
    grantReadAcp?: string;
    grantWriteAcp?: string;
    metadataDirective?: string;
    meta?: Record<string, string>;
    websiteRedirectLocation?: string;
    storageClass?: StorageClassType;
    ifMatch?: string;
    forbidOverwrite?: boolean;
    headers?: {
        [key: string]: string | undefined;
        ['x-tos-copy-source']?: string;
        ['x-tos-acl']?: string;
        ['x-tos-copy-source-if-match']?: string;
        ['x-tos-copy-source-if-modified-since']?: string;
        ['x-tos-copy-source-if-none-match']?: string;
        ['x-tos-copy-source-if-unmodified-since']?: string;
        ['x-tos-copy-source-server-side-encryption-customer-algorithm']?: string;
        ['x-tos-copy-source-server-side-encryption-customer-key']?: string;
        ['x-tos-copy-source-server-side-encryption-customer-key-MD5']?: string;
        ['x-tos-grant-full-control']?: string;
        ['x-tos-grant-read']?: string;
        ['x-tos-grant-read-acp']?: string;
        ['x-tos-metadata-directive']?: string;
        ['x-tos-website-redirect-location']?: string;
        ['x-tos-storage-class']?: StorageClass;
        ['x-tos-server-side-encryption']?: ServerSideEncryption;
        ['x-tos-forbid-overwrite']?: string;
        'If-Match'?: string;
    };
}
interface CopyObjectBody {
    ETag: string;
}
export interface CopyObjectOutput extends CopyObjectBody {
}
export declare function copyObject(this: TOSBase, input: CopyObjectInput): Promise<TosResponse<CopyObjectOutput>>;
export default copyObject;
