<template>
  <a-card :bordered="false">
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline" @keyup.enter.native="searchQuery">
        <a-row :gutter="24">
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="程序类型">
              <j-dict-select-tag placeholder="请选择程序类型" v-model="queryParam.programType" dictCode="program_type"/>
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="版本号">
              <a-input placeholder="请输入版本号" v-model="queryParam.versionNumber"></a-input>
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="是否最新">
              <j-dict-select-tag placeholder="请选择" v-model="queryParam.isLatest" dictCode="isTrue"/>
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <span class="table-page-search-submitButtons" style="display: block;white-space: nowrap;">
              <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
              <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
              <a @click="handleToggleSearch" style="margin-left: 8px">
                {{ toggleSearchStatus ? '收起' : '展开' }}
                <a-icon :type="toggleSearchStatus ? 'up' : 'down'"/>
              </a>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <!-- 查询区域-END -->

    <!-- 操作按钮区域 -->
    <div class="table-operator">
      <a-button @click="handleAdd" type="primary" icon="plus">新增</a-button>
      <a-button type="primary" icon="download" @click="handleExportXls('程序版本控制')">导出</a-button>
      <a-upload name="file" :showUploadList="false" :multiple="false" :headers="tokenHeader" :action="importExcelUrl" @change="handleImportExcel">
        <a-button type="primary" icon="import">导入</a-button>
      </a-upload>
      <!-- 高级查询区域 -->
      <j-super-query :fieldList="superFieldList" ref="superQueryModal" @handleSuperQuery="handleSuperQuery"></j-super-query>
      <a-dropdown v-if="selectedRowKeys.length > 0">
        <a-menu slot="overlay">
          <a-menu-item key="1" @click="batchDel"><a-icon type="delete"/>删除</a-menu-item>
        </a-menu>
        <a-button style="margin-left: 8px"> 批量操作 <a-icon type="down" /></a-button>
      </a-dropdown>
    </div>

    <!-- table区域-begin -->
    <div>
      <div class="ant-alert ant-alert-info" style="margin-bottom: 16px;">
        <i class="anticon anticon-info-circle ant-alert-icon"></i> 已选择 <a style="font-weight: 600">{{ selectedRowKeys.length }}</a>项
        <a style="margin-left: 24px" @click="onClearSelected">清空</a>
      </div>

      <a-table
        ref="table"
        size="middle"
        :scroll="{x:true}"
        bordered
        rowKey="id"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="ipagination"
        :loading="loading"
        :rowSelection="{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}"
        class="j-table-force-nowrap"
        @change="handleTableChange">

        <template slot="htmlSlot" slot-scope="text">
          <div v-html="text"></div>
        </template>
        <template slot="imgSlot" slot-scope="text">
          <span v-if="!text" style="font-size: 12px;font-style: italic;">无图片</span>
          <img v-else :src="getImgView(text)" height="25px" alt="" style="max-width:80px;font-size: 12px;font-style: italic;"/>
        </template>
        <template slot="fileSlot" slot-scope="text">
          <span v-if="!text" style="font-size: 12px;font-style: italic;">无文件</span>
          <a-button
            v-else
            :ghost="true"
            type="primary"
            icon="download"
            size="small"
            @click="downloadFile(text)">
            下载
          </a-button>
        </template>

        <span slot="action" slot-scope="text, record">
          <a @click="handleEdit(record)">编辑</a>

          <a-divider type="vertical" />
          <a-dropdown>
            <a class="ant-dropdown-link">更多 <a-icon type="down" /></a>
            <a-menu slot="overlay">
              <a-menu-item>
                <a @click="handleDetail(record)">详情</a>
              </a-menu-item>
              <a-menu-item v-if="record.isLatest !== 1">
                <a @click="handleSetLatest(record)">设为最新</a>
              </a-menu-item>
              <a-menu-item>
                <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
                  <a>删除</a>
                </a-popconfirm>
              </a-menu-item>
            </a-menu>
          </a-dropdown>
        </span>

      </a-table>
    </div>

    <aigc-version-control-modal ref="modalForm" @ok="modalFormOk"></aigc-version-control-modal>
  </a-card>
</template>

<script>

  import '@/assets/less/TableExpand.less'
  import { mixinDevice } from '@/utils/mixin'
  import { JeecgListMixin } from '@/mixins/JeecgListMixin'
  import AigcVersionControlModal from './modules/AigcVersionControlModal'

  export default {
    name: 'AigcVersionControlList',
    mixins:[JeecgListMixin, mixinDevice],
    components: {
      AigcVersionControlModal
    },
    data () {
      return {
        description: '程序版本控制管理页面',
        // 表头
        columns: [
          {
            title: '#',
            dataIndex: '',
            key:'rowIndex',
            width:60,
            align:"center",
            customRender:function (t,r,index) {
              return parseInt(index)+1;
            }
          },
          {
            title:'程序类型',
            align:"center",
            dataIndex: 'programType',
            customRender: (text) => {
              const typeMap = {
                'frontend': '前端',
                'backend': '后端', 
                'desktop': '桌面应用',
                'plugin': '扣子插件'
              }
              return typeMap[text] || text
            }
          },
          {
            title:'版本号',
            align:"center",
            dataIndex: 'versionNumber'
          },
          {
            title:'更新内容',
            align:"center",
            dataIndex: 'updateContent',
            width: 200,
            customRender: (text) => {
              if (!text) return '-'
              return text.length > 50 ? text.substring(0, 50) + '...' : text
            }
          },
          {
            title:'下载链接',
            align:"center",
            dataIndex: 'downloadUrl',
            width: 150,
            customRender: (text) => {
              if (!text) return '-'
              return `<a href="${text}" target="_blank" style="color: #1890ff;">下载</a>`
            }
          },
          {
            title:'发布日期',
            align:"center",
            dataIndex: 'releaseDate',
            customRender: (text) => {
              return !text ? "" : (text.length > 10 ? text.substr(0,10) : text)
            }
          },
          {
            title:'是否最新',
            align:"center",
            dataIndex: 'isLatest',
            customRender: (text) => {
              return text === 1 ? '是' : '否'
            }
          },
          {
            title:'状态',
            align:"center",
            dataIndex: 'status',
            customRender: (text) => {
              return text === 1 ? '启用' : '禁用'
            }
          },
          {
            title: '操作',
            dataIndex: 'action',
            align:"center",
            fixed:"right",
            width:147,
            scopedSlots: { customRender: 'action' }
          }
        ],
        url: {
          list: "/aigcview/versioncontrol/list",
          delete: "/aigcview/versioncontrol/delete",
          deleteBatch: "/aigcview/versioncontrol/deleteBatch",
          exportXlsUrl: "/aigcview/versioncontrol/exportXls",
          importExcelUrl: "aigcview/versioncontrol/importExcel",
          setLatest: "/aigcview/versioncontrol/setAsLatest"
        },
        dictOptions:{},
        superFieldList:[],
      }
    },
    created() {
      this.getSuperFieldList();
      // 不设置isLatest默认值，让用户可以查看全部数据
    },
    computed: {
      importExcelUrl: function(){
        return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`;
      },
    },
    methods: {
      initDictConfig(){
      },
      getSuperFieldList(){
        let fieldList=[];
        fieldList.push({type:'string',value:'programType',text:'程序类型',dictCode:'program_type'})
        fieldList.push({type:'string',value:'versionNumber',text:'版本号'})
        fieldList.push({type:'string',value:'updateContent',text:'更新内容'})
        fieldList.push({type:'date',value:'releaseDate',text:'发布日期'})
        fieldList.push({type:'int',value:'isLatest',text:'是否最新版本',dictCode:'isTrue'})
        fieldList.push({type:'int',value:'status',text:'状态',dictCode:'valid_status'})
        this.superFieldList = fieldList
      },
      handleSetLatest(record) {
        let that = this;
        this.$confirm({
          title: "确认操作",
          content: "确定要将此版本设为最新版本吗？",
          onOk: function () {
            that.doSetLatest(record.id);
          }
        });
      },
      doSetLatest(id) {
        let that = this;
        this.$http.put(this.url.setLatest, {}, {
          params: { id: id }
        }).then((res) => {
          if (res.success) {
            that.$message.success(res.message);
            that.loadData();
          } else {
            that.$message.warning(res.message);
          }
        }).catch((error) => {
          console.error('设置最新版本失败:', error);
          that.$message.error('设置最新版本失败，请稍后重试');
        });
      }
    }
  }
</script>
<style scoped>
  @import '~@assets/less/common.less';
</style>
