# add_masks接口坐标转换技术文档

## 📋 文档信息

- **接口名称**：add_masks
- **功能描述**：为剪映草稿添加蒙版效果
- **坐标系统**：中心偏移坐标系统
- **更新时间**：2025-07-09
- **版本**：v2.0

## 🎯 坐标系统概述

### 坐标系统类型
add_masks接口使用**中心偏移坐标系统**，与addSticker接口的绝对坐标系统不同。

### 坐标原点
- **原点位置**：视频中心 (0, 0)
- **X轴正方向**：向右
- **Y轴正方向**：向下
- **坐标范围**：理论上无限制，支持负值

## 🔧 转换公式详解

### 核心转换方法
```java
private double convertPixelToNormalizedCoordinate(Integer pixelValue, int videoSize) {
    if (pixelValue == null) return 0.0;
    // 竞争对手的转换公式：pixelValue / (videoSize / 2)
    return pixelValue.doubleValue() / (videoSize / 2.0);
}
```

### 转换逻辑说明
1. **基准尺寸**：使用视频尺寸的一半作为基准
2. **转换公式**：`归一化坐标 = 像素偏移量 ÷ (视频尺寸 ÷ 2)`
3. **坐标含义**：结果表示相对于视频中心的归一化偏移量

### 实际应用
```java
// 在addMasks方法中的使用
config.put("centerX", convertPixelToNormalizedCoordinate(request.getZjX(), videoWidth));
config.put("centerY", convertPixelToNormalizedCoordinate(request.getZjY(), videoHeight));
```

## 📊 转换示例

### 示例1：标准1920×1080视频
**视频尺寸**：1920×1080
**基准值**：X基准=960, Y基准=540

| 用户输入(像素) | 转换计算 | 归一化坐标 | 含义 |
|---------------|----------|------------|------|
| X: 100 | 100 ÷ 960 | 0.104 | 向右偏移960像素的10.4% |
| X: -100 | -100 ÷ 960 | -0.104 | 向左偏移960像素的10.4% |
| Y: 50 | 50 ÷ 540 | 0.093 | 向下偏移540像素的9.3% |
| Y: -50 | -50 ÷ 540 | -0.093 | 向上偏移540像素的9.3% |

### 示例2：正方形1024×1024视频
**视频尺寸**：1024×1024
**基准值**：X基准=512, Y基准=512

| 用户输入(像素) | 转换计算 | 归一化坐标 | 含义 |
|---------------|----------|------------|------|
| X: 256 | 256 ÷ 512 | 0.5 | 向右偏移半个视频宽度 |
| Y: -256 | -256 ÷ 512 | -0.5 | 向上偏移半个视频高度 |

## 🎭 与其他接口的区别

### addSticker接口对比
| 对比项目 | add_masks | addSticker |
|----------|-----------|------------|
| **坐标系统** | 中心偏移坐标系统 | 绝对坐标系统 |
| **坐标原点** | 视频中心(0,0) | 画布左上角(0,0) |
| **转换公式** | `像素值 ÷ (视频尺寸 ÷ 2)` | `像素值 ÷ 画布尺寸` |
| **基准尺寸** | 视频尺寸 | 画布尺寸 |
| **存储字段** | `config.centerX/Y` | `transform.x/y` |
| **支持负值** | ✅ 是 | ❌ 否（通常） |

## 🔍 技术实现细节

### 视频尺寸获取
```java
private int[] getVideoSize(JSONObject draft) {
    try {
        // 尝试从tracks中获取第一个视频轨道的尺寸
        JSONArray tracks = draft.getJSONArray("tracks");
        if (tracks != null) {
            for (int i = 0; i < tracks.size(); i++) {
                JSONObject track = tracks.getJSONObject(i);
                if ("video".equals(track.getString("type"))) {
                    JSONArray segments = track.getJSONArray("segments");
                    if (segments != null && segments.size() > 0) {
                        // 获取第一个视频段的材料信息
                        JSONObject firstSegment = segments.getJSONObject(0);
                        String materialId = firstSegment.getString("material_id");
                        
                        // 从materials中查找对应的视频材料
                        JSONObject materials = draft.getJSONObject("materials");
                        if (materials != null) {
                            JSONArray videos = materials.getJSONArray("videos");
                            if (videos != null) {
                                for (int j = 0; j < videos.size(); j++) {
                                    JSONObject video = videos.getJSONObject(j);
                                    if (materialId.equals(video.getString("id"))) {
                                        Integer width = video.getInteger("width");
                                        Integer height = video.getInteger("height");
                                        if (width != null && height != null && width > 0 && height > 0) {
                                            return new int[]{width, height};
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    } catch (Exception e) {
        log.warn("获取视频尺寸失败，使用默认值: {}", e.getMessage());
    }
    // 默认使用1920x1080（标准高清尺寸）
    return new int[]{1920, 1080};
}
```

### 错误处理机制
1. **null值处理**：输入为null时返回0.0
2. **默认尺寸**：获取视频尺寸失败时使用1920×1080
3. **异常捕获**：完善的异常处理和日志记录

## 📝 API参数说明

### 输入参数
- **zj_x**：蒙版中心X坐标（像素值，相对于视频中心的偏移）
- **zj_y**：蒙版中心Y坐标（像素值，相对于视频中心的偏移）

### 坐标含义
- **正值**：X正值向右偏移，Y正值向下偏移
- **负值**：X负值向左偏移，Y负值向上偏移
- **零值**：表示蒙版中心与视频中心重合

## 🎯 最佳实践

### 坐标设置建议
1. **居中蒙版**：使用 (0, 0) 将蒙版置于视频中心
2. **右上角蒙版**：使用正X值和负Y值
3. **左下角蒙版**：使用负X值和正Y值
4. **精确定位**：根据视频尺寸计算合适的像素偏移量

### 常用坐标模式
```javascript
// 视频中心
{ "zj_x": 0, "zj_y": 0 }

// 右上角（1920×1080视频）
{ "zj_x": 480, "zj_y": -270 }  // 向右1/2，向上1/2

// 左下角（1920×1080视频）
{ "zj_x": -480, "zj_y": 270 }  // 向左1/2，向下1/2
```

## 🔧 调试和验证

### 日志输出
转换过程会输出详细日志，便于调试：
```
蒙版坐标转换: 用户输入(100, -50)像素 -> 归一化坐标(0.104, -0.093), 视频尺寸: 1920x1080
```

### 验证方法
1. **计算验证**：手动计算转换结果是否正确
2. **视觉验证**：在剪映中检查蒙版位置是否符合预期
3. **边界测试**：测试极值和边界情况

## 📚 相关文档

- [addSticker接口坐标转换技术文档](./addSticker接口坐标转换技术文档.md)
- [剪映坐标系统对比分析](./剪映坐标系统对比分析.md)
- [API接口使用指南](./API接口使用指南.md)

---

**维护者**：智界Aigc开发团队  
**联系方式**：技术支持群  
**最后更新**：2025-07-09
