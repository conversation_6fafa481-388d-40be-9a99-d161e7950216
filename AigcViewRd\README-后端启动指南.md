# 智界Aigc 后端启动指南

## 项目概述
- **项目名称**: 智界Aigc
- **后端框架**: Jeecg-Boot 2.4.6
- **推荐Java版本**: JDK 8 或 JDK 11 ⭐
- **当前Java版本**: JDK 17
- **Spring Boot版本**: 2.3.5.RELEASE

## 重要说明 ⚠️

### JDK版本建议
**强烈推荐使用JDK 8或JDK 11**，原因：
- ✅ **完美兼容**：无需任何特殊配置
- ✅ **稳定性好**：MyBatis-Plus运行更稳定
- ✅ **启动简单**：不需要复杂的JVM参数
- ✅ **部署方便**：减少生产环境问题

### Java 17 兼容性问题
如果必须使用Java 17，MyBatis-Plus在处理Lambda表达式时会遇到模块系统限制，导致以下错误：
```
java.lang.reflect.InaccessibleObjectException: Unable to make field private final java.lang.Class java.lang.invoke.SerializedLambda.capturingClass accessible
```

**必须添加JVM参数来解决此问题！**

## JDK切换指南（推荐）

### 下载JDK 8或11
- **JDK 8**: [OpenJDK 8](https://adoptium.net/temurin/releases/?version=8) 或 [Oracle JDK 8](https://www.oracle.com/java/technologies/javase/javase8-archive-downloads.html)
- **JDK 11**: [OpenJDK 11](https://adoptium.net/temurin/releases/?version=11) 或 [Oracle JDK 11](https://www.oracle.com/java/technologies/javase/jdk11-archive-downloads.html)

### 配置环境变量
```bash
# 设置JAVA_HOME
JAVA_HOME=C:\Program Files\Java\jdk1.8.0_xxx
# 或
JAVA_HOME=C:\Program Files\Java\jdk-11.x.x

# 更新PATH
PATH=%JAVA_HOME%\bin;%PATH%
```

### 验证JDK版本
```bash
java -version
javac -version
```

## 启动方法

### 使用JDK 8/11启动（推荐）
直接启动，无需任何特殊参数：
```bash
cd jeecg-boot-module-system
mvn spring-boot:run
```

### 方法1: IDE启动
1. 在IDE中打开项目
2. 找到主启动类：`org.jeecg.JeecgSystemApplication`
3. 在运行配置中添加以下JVM参数：
   ```
   --add-opens java.base/java.lang.invoke=ALL-UNNAMED
   --add-opens java.base/java.lang.reflect=ALL-UNNAMED
   --add-opens java.base/java.io=ALL-UNNAMED
   ```
4. 运行主类

### 方法2: 使用提供的启动脚本
在项目根目录下运行：
```bash
# Windows批处理
./start-backend-java17.bat

# PowerShell脚本
./start-backend-java17.ps1
```

### 方法3: Maven命令启动
```bash
cd jeecg-boot-module-system
mvn spring-boot:run -Dspring-boot.run.jvmArguments="--add-opens java.base/java.lang.invoke=ALL-UNNAMED --add-opens java.base/java.lang.reflect=ALL-UNNAMED --add-opens java.base/java.io=ALL-UNNAMED"
```

### 方法4: 直接Java命令
```bash
cd jeecg-boot-module-system
java --add-opens java.base/java.lang.invoke=ALL-UNNAMED \
     --add-opens java.base/java.lang.reflect=ALL-UNNAMED \
     --add-opens java.base/java.io=ALL-UNNAMED \
     -jar target/jeecg-boot-module-system-2.4.6.jar
```

## 启动成功标志
当看到以下信息时，表示启动成功：
```
----------------------------------------------------------
        Application Jeecg-Boot is running! Access URLs:
        Local:          http://localhost:8080/jeecg-boot/
        External:       http://*************:8080/jeecg-boot/
        Swagger文档:    http://*************:8080/jeecg-boot/doc.html
----------------------------------------------------------
```

## 访问地址
- **后端API**: http://localhost:8080/jeecg-boot/
- **Swagger文档**: http://localhost:8080/jeecg-boot/doc.html

## 数据库配置
- **数据库**: MySQL
- **数据库名**: AigcView
- **连接地址**: 127.0.0.1:3306
- **用户名**: root
- **密码**: root

## Redis配置
- **地址**: 127.0.0.1:6379
- **密码**: 123456

## 常见问题

### 1. 登录失败 - OGNL表达式错误
**错误信息**: `Error evaluating expression 'ew.sqlSegment != null and ew.sqlSegment != '' and ew.nonEmptyOfNormal'`

**解决方案**: 确保添加了上述JVM参数，这是Java 17模块系统限制导致的问题。

### 2. Quartz表不存在错误
**错误信息**: `Table 'aigcview.qrtz_locks' doesn't exist`

**解决方案**: 这是定时任务相关的表，不影响基本功能。如需使用定时任务功能，请执行相关SQL脚本。

### 3. Maven插件找不到
**错误信息**: `No plugin found for prefix 'spring-boot'`

**解决方案**: 使用完整插件名称：
```bash
mvn org.springframework.boot:spring-boot-maven-plugin:run
```

## 配置文件说明
- **开发环境**: `application-dev.yml`
- **生产环境**: `application-prod.yml`
- **测试环境**: `application-test.yml`

## 项目结构
```
jeecg-boot-module-system/
├── src/main/java/org/jeecg/
│   ├── JeecgSystemApplication.java  # 主启动类
│   ├── modules/                     # 业务模块
│   └── config/                      # 配置类
├── src/main/resources/
│   ├── application-dev.yml          # 开发环境配置
│   └── static/                      # 静态资源
└── target/                          # 编译输出目录
```

## 开发注意事项
1. 确保Java版本为JDK 17
2. 启动时必须添加JVM参数解决反射访问问题
3. 数据库和Redis服务需要先启动
4. 首次启动可能需要下载依赖，请耐心等待

## 更新日志
- 2025-06-14: 解决Java 17兼容性问题，添加必要的JVM参数
- 2025-06-14: 修复MyBatis-Plus OGNL表达式解析错误
- 2025-06-14: 更新项目名称为"智界Aigc"
