package org.jeecg.modules.api.aspect;

import org.apache.shiro.SecurityUtils;
import org.apache.shiro.mgt.SecurityManager;
import org.apache.shiro.subject.Subject;
import org.apache.shiro.util.ThreadContext;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.modules.api.config.UserActivityConfig;
import org.jeecg.modules.api.dto.UserActivityUpdateDTO;
import org.jeecg.modules.api.service.IUserActivityBatchUpdateService;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpSession;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * @Description: 用户活跃状态追踪AOP切面测试类
 * @Author: AigcView
 * @Date: 2025-01-30
 * @Version: V1.0
 */
@ExtendWith(MockitoExtension.class)
class UserActivityAspectTest {

    @Mock
    private UserActivityConfig userActivityConfig;

    @Mock
    private IUserActivityBatchUpdateService batchUpdateService;

    @Mock
    private ProceedingJoinPoint joinPoint;

    @Mock
    private Signature signature;

    @Mock
    private SecurityManager securityManager;

    @Mock
    private Subject subject;

    @InjectMocks
    private UserActivityAspect userActivityAspect;

    private MockHttpServletRequest request;
    private LoginUser testUser;

    @BeforeEach
    void setUp() {
        // 设置测试用户
        testUser = new LoginUser();
        testUser.setId("test-user-id");
        testUser.setUsername("testuser");
        testUser.setRealname("测试用户");

        // 设置Mock请求
        request = new MockHttpServletRequest();
        request.setRequestURI("/api/test");
        request.setRemoteAddr("192.168.1.100");
        request.addHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36");
        request.setSession(new MockHttpSession());

        // 设置RequestContextHolder
        ServletRequestAttributes attributes = new ServletRequestAttributes(request);
        RequestContextHolder.setRequestAttributes(attributes);

        // 设置Shiro SecurityUtils
        ThreadContext.bind(securityManager);
        when(SecurityUtils.getSubject()).thenReturn(subject);
    }

    @AfterEach
    void tearDown() {
        RequestContextHolder.resetRequestAttributes();
        ThreadContext.unbindSubject();
        ThreadContext.unbindSecurityManager();
    }

    @Test
    void testTrackUserActivity_ConfigDisabled() throws Throwable {
        // Given: 配置未启用
        when(userActivityConfig.getEnabled()).thenReturn(false);
        when(joinPoint.proceed()).thenReturn("success");

        // When: 执行切面方法
        Object result = userActivityAspect.trackUserActivity(joinPoint);

        // Then: 直接执行原方法，不进行用户活跃状态追踪
        assertEquals("success", result);
        verify(joinPoint, times(1)).proceed();
        verify(batchUpdateService, never()).addToUpdateQueue(any());
    }

    @Test
    void testTrackUserActivity_UserNotLoggedIn() throws Throwable {
        // Given: 配置启用但用户未登录
        when(userActivityConfig.getEnabled()).thenReturn(true);
        when(subject.getPrincipal()).thenReturn(null);
        when(joinPoint.proceed()).thenReturn("success");

        // When: 执行切面方法
        Object result = userActivityAspect.trackUserActivity(joinPoint);

        // Then: 直接执行原方法，不进行用户活跃状态追踪
        assertEquals("success", result);
        verify(joinPoint, times(1)).proceed();
        verify(batchUpdateService, never()).addToUpdateQueue(any());
    }

    @Test
    void testTrackUserActivity_ExcludedPath() throws Throwable {
        // Given: 请求路径在排除列表中
        request.setRequestURI("/sys/login");
        ServletRequestAttributes attributes = new ServletRequestAttributes(request);
        RequestContextHolder.setRequestAttributes(attributes);

        when(userActivityConfig.getEnabled()).thenReturn(true);
        when(joinPoint.proceed()).thenReturn("success");

        // When: 执行切面方法
        Object result = userActivityAspect.trackUserActivity(joinPoint);

        // Then: 直接执行原方法，不进行用户活跃状态追踪
        assertEquals("success", result);
        verify(joinPoint, times(1)).proceed();
        verify(batchUpdateService, never()).addToUpdateQueue(any());
    }

    @Test
    void testTrackUserActivity_NotInRollout() throws Throwable {
        // Given: 用户不在灰度发布范围内
        when(userActivityConfig.getEnabled()).thenReturn(true);
        when(subject.getPrincipal()).thenReturn(testUser);
        when(userActivityConfig.isInRollout("testuser")).thenReturn(false);
        when(joinPoint.proceed()).thenReturn("success");

        // When: 执行切面方法
        Object result = userActivityAspect.trackUserActivity(joinPoint);

        // Then: 直接执行原方法，不进行用户活跃状态追踪
        assertEquals("success", result);
        verify(joinPoint, times(1)).proceed();
        verify(batchUpdateService, never()).addToUpdateQueue(any());
    }

    @Test
    void testTrackUserActivity_SuccessfulTracking() throws Throwable {
        // Given: 正常的用户活跃状态追踪场景
        when(userActivityConfig.getEnabled()).thenReturn(true);
        when(subject.getPrincipal()).thenReturn(testUser);
        when(userActivityConfig.isInRollout("testuser")).thenReturn(true);
        when(userActivityConfig.isCriticalApi("/api/test")).thenReturn(false);
        when(userActivityConfig.getTestMode()).thenReturn(true);
        when(joinPoint.proceed()).thenReturn("success");

        // When: 执行切面方法
        Object result = userActivityAspect.trackUserActivity(joinPoint);

        // Then: 执行原方法并进行用户活跃状态追踪
        assertEquals("success", result);
        verify(joinPoint, times(1)).proceed();
        verify(batchUpdateService, times(1)).addToUpdateQueue(any(UserActivityUpdateDTO.class));
    }

    @Test
    void testTrackUserActivity_CriticalApi() throws Throwable {
        // Given: 核心API请求
        request.setRequestURI("/payment/create");
        ServletRequestAttributes attributes = new ServletRequestAttributes(request);
        RequestContextHolder.setRequestAttributes(attributes);

        when(userActivityConfig.getEnabled()).thenReturn(true);
        when(subject.getPrincipal()).thenReturn(testUser);
        when(userActivityConfig.isInRollout("testuser")).thenReturn(true);
        when(userActivityConfig.isCriticalApi("/payment/create")).thenReturn(true);
        when(userActivityConfig.getTestMode()).thenReturn(false);
        when(joinPoint.proceed()).thenReturn("success");

        // When: 执行切面方法
        Object result = userActivityAspect.trackUserActivity(joinPoint);

        // Then: 执行原方法并进行用户活跃状态追踪
        assertEquals("success", result);
        verify(joinPoint, times(1)).proceed();
        verify(batchUpdateService, times(1)).addToUpdateQueue(any(UserActivityUpdateDTO.class));
        verify(userActivityConfig, times(1)).isCriticalApi("/payment/create");
    }

    @Test
    void testTrackUserActivity_ExceptionHandling() throws Throwable {
        // Given: 批量更新服务抛出异常
        when(userActivityConfig.getEnabled()).thenReturn(true);
        when(subject.getPrincipal()).thenReturn(testUser);
        when(userActivityConfig.isInRollout("testuser")).thenReturn(true);
        when(userActivityConfig.isCriticalApi(anyString())).thenReturn(false);
        when(joinPoint.proceed()).thenReturn("success");
        doThrow(new RuntimeException("批量更新失败")).when(batchUpdateService).addToUpdateQueue(any());

        // When: 执行切面方法
        Object result = userActivityAspect.trackUserActivity(joinPoint);

        // Then: 异常不应影响主业务流程
        assertEquals("success", result);
        verify(joinPoint, times(1)).proceed();
    }

    @Test
    void testTrackUserActivity_StaticResourceExclusion() throws Throwable {
        // Given: 静态资源请求
        request.setRequestURI("/static/css/app.css");
        ServletRequestAttributes attributes = new ServletRequestAttributes(request);
        RequestContextHolder.setRequestAttributes(attributes);

        when(userActivityConfig.getEnabled()).thenReturn(true);
        when(joinPoint.proceed()).thenReturn("success");

        // When: 执行切面方法
        Object result = userActivityAspect.trackUserActivity(joinPoint);

        // Then: 直接执行原方法，不进行用户活跃状态追踪
        assertEquals("success", result);
        verify(joinPoint, times(1)).proceed();
        verify(batchUpdateService, never()).addToUpdateQueue(any());
    }

    @Test
    void testTrackUserActivity_NoRequestContext() throws Throwable {
        // Given: 没有请求上下文
        RequestContextHolder.resetRequestAttributes();

        when(userActivityConfig.getEnabled()).thenReturn(true);
        when(joinPoint.proceed()).thenReturn("success");

        // When: 执行切面方法
        Object result = userActivityAspect.trackUserActivity(joinPoint);

        // Then: 直接执行原方法，不进行用户活跃状态追踪
        assertEquals("success", result);
        verify(joinPoint, times(1)).proceed();
        verify(batchUpdateService, never()).addToUpdateQueue(any());
    }

    @Test
    void testTrackUserActivity_SecurityException() throws Throwable {
        // Given: SecurityUtils抛出异常
        when(userActivityConfig.getEnabled()).thenReturn(true);
        when(SecurityUtils.getSubject()).thenThrow(new RuntimeException("Security异常"));
        when(joinPoint.proceed()).thenReturn("success");

        // When: 执行切面方法
        Object result = userActivityAspect.trackUserActivity(joinPoint);

        // Then: 异常不应影响主业务流程
        assertEquals("success", result);
        verify(joinPoint, times(1)).proceed();
        verify(batchUpdateService, never()).addToUpdateQueue(any());
    }
}
