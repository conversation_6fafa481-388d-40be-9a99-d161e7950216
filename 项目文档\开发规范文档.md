# 🔧 智界Aigc开发规范文档

## 🎯 核心开发原则

### 0. 系统设计优先级原则 ⭐⭐⭐
**最高优先级：客户体验不能受影响**

#### **设计理念**
- **客户体验第一**：官网用户的访问速度和体验是最重要的，任何优化都不能影响客户
- **运维可接受慢一点**：后台管理系统的性能可以适当牺牲，运维人员能理解和接受
- **官网体验优先**：一切技术决策都要以官网体验为先，后台管理为辅

#### **实施原则**
```javascript
// ✅ 正确的优先级考虑
if (isCustomerFacing) {
  // 客户相关功能：追求极致性能和体验
  return optimizeForSpeed()
} else if (isAdminFunction) {
  // 管理功能：功能完整性优先，性能其次
  return optimizeForFeatures()
}
```

#### **具体应用场景**
- **路由设计**：官网路由简洁快速，后台路由可以复杂一些
- **资源加载**：官网资源优先加载，后台资源可以懒加载
- **错误处理**：官网错误要优雅降级，后台错误可以详细展示
- **性能优化**：官网要极致优化，后台可以功能优先

### 1. 前端双系统架构规范 ⭐
**智界AIGC采用"一个前端项目，两套系统"的架构设计**

#### **架构概述**
```
智界AIGC前端项目
├── 官网系统 (Customer-Facing)
│   ├── 静态路由 - 快速响应
│   ├── 公开访问 - 无需权限
│   ├── 性能优先 - 极致优化
│   └── 用户体验 - 最高优先级
└── 后台管理系统 (Admin-Facing)
    ├── 动态路由 - 权限控制
    ├── 登录访问 - 角色验证
    ├── 功能优先 - 完整性重要
    └── 运维工具 - 可接受慢一点
```

#### **路由架构设计**
```javascript
// 官网路由 - 静态配置，快速匹配
const websiteRoutes = [
  { path: '/home', component: Home },
  { path: '/market', component: Market },
  { path: '/usercenter', component: UserCenter }
]

// 后台路由 - 动态生成，权限控制
const adminRoutes = generateIndexRouter(menuData) // 从后端获取
```

#### **🚨 动态路由红线约束 - 绝对不能违反！**
**重要警告：动态路由系统是项目的核心架构，任何修改都会产生连锁反应！**

##### **禁止修改的内容**
```javascript
// ❌ 绝对禁止修改这些内容
1. 后端菜单数据库表结构
2. 菜单URL字段的路径格式 (如: /views/aigcview/usercenter/UserCenter)
3. generateIndexRouter函数的路径生成逻辑
4. 动态路由的根路径结构 (/)
5. 权限系统的路由匹配机制
```

##### **修改动态路由的巨大影响**
- **数据库层面**：需要修改所有菜单记录的URL字段
- **权限系统**：整个RBAC权限控制需要重新适配
- **菜单管理**：后台菜单管理功能需要重构
- **路由生成**：动态路由生成逻辑需要重写
- **用户权限**：所有用户的权限配置需要迁移
- **系统稳定性**：可能影响整个后台管理系统

##### **正确的解决思路**
```javascript
// ✅ 正确做法：适配现有动态路由
if (isDynamicRouteConflict) {
  // 在前端层面解决冲突，不修改后端路由结构
  return adaptToExistingRoutes()
}

// ❌ 错误做法：修改动态路由结构
if (isDynamicRouteConflict) {
  // 这会牵一发而动全身！
  return modifyBackendRoutes() // 绝对禁止！
}
```

##### **设计原则**
- **后端路由不可变**：动态路由是既定架构，必须适配
- **前端灵活适配**：通过前端技术手段解决路由冲突
- **最小化改动**：优先选择影响最小的解决方案
- **向后兼容**：任何修改都不能破坏现有功能

#### **智能路由冲突解决方案**
**问题**：刷新后台页面时，动态路由未加载，静态路由通配符先生效导致跳转404

**解决方案**：智能通配符组件
```javascript
// ❌ 原有方案 - 直接重定向
{
  path: '*',
  redirect: '/not-found'
}

// ✅ 智能方案 - 条件处理
{
  path: '*',
  component: () => import('@/components/SmartNotFound.vue')
}
```

**SmartNotFound组件逻辑**：
1. **检查用户角色**：是否为admin用户
2. **判断访问路径**：是否为后台页面路径
3. **智能处理**：
   - admin访问后台 → 加载动态路由 → 跳转原页面
   - 其他情况 → 显示404页面

#### **实现效果**
- **客户访问错误路径**：直接显示404，零延迟
- **admin刷新后台页面**：短暂加载后回到原页面
- **性能影响**：仅对admin用户，客户完全不受影响

### 2. 组件化设计原则
所有公共部分必须组件化，方便复用和维护。

#### 必须组件化的部分：
- ✅ **页头组件（Header.vue）** - 导航栏，所有页面复用
- ✅ **页脚组件（Footer.vue）** - 页脚信息，所有页面复用
- ✅ **布局组件（Layout.vue）** - 整体页面布局
- ✅ **插件卡片组件** - 插件展示复用
- ✅ **视频卡片组件** - 视频展示复用

#### 组件设计规范：
```vue
<!-- 正确的组件设计 -->
<template>
  <div class="component-name">
    <!-- 组件内容 -->
  </div>
</template>

<script>
export default {
  name: 'ComponentName',
  props: {
    // 定义组件属性
  },
  data() {
    return {
      // 组件数据从API获取
    }
  },
  async mounted() {
    // 组件挂载时获取数据
    await this.loadData()
  },
  methods: {
    async loadData() {
      // 从后端API获取数据
    }
  }
}
</script>
```

### 3. 零硬编码原则
前端不允许任何硬编码，所有数据必须从后端API获取。

#### ❌ 禁止的硬编码示例：
```javascript
// 错误 - 硬编码菜单
const menuItems = [
  { name: '首页', path: '/' },
  { name: '商城', path: '/market' }
]

// 错误 - 硬编码文案
const welcomeText = '欢迎使用智界Aigc'

// 错误 - 硬编码配置
const apiBaseUrl = 'https://www.aigcview.com'
```

#### ✅ 正确的API调用示例：
```javascript
// 正确 - 从API获取菜单
async loadMenu() {
  this.menuItems = await this.$http.get('/api/website/menu')
}

// 正确 - 从API获取文案
async loadContent() {
  this.pageContent = await this.$http.get('/api/website/content')
}

// 正确 - 从配置文件获取
const apiBaseUrl = process.env.VUE_APP_API_BASE_URL
```

### 4. 数据驱动原则
所有页面内容都应该是数据驱动的，支持后台配置。

#### 需要API提供的数据：
```javascript
// 页头数据
GET /api/website/header
{
  "logo": "logo地址",
  "menuItems": [菜单项],
  "userInfo": "用户信息"
}

// 页脚数据
GET /api/website/footer
{
  "companyInfo": "公司信息",
  "links": "友情链接",
  "beianInfo": "备案信息"
}

// 首页数据
GET /api/website/home
{
  "banners": "轮播图",
  "features": "功能介绍",
  "statistics": "统计数据"
}
```

### 5. 🎨 智界AIGC官网UI设计风格规范 ⭐

#### **核心设计理念**
- **明亮现代**：采用浅色系为主的现代化设计风格
- **清新简约**：避免深沉压抑的色调，营造轻松愉悦的用户体验
- **科技感与亲和力并重**：既体现AI科技的专业性，又保持用户友好的亲和力
- **品牌一致性**：所有页面保持统一的视觉语言和交互体验

#### **🎨 色彩规范**

##### **主色调系统**
```css
/* 背景色系 - 明亮浅色调 */
--bg-primary: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
--bg-secondary: rgba(255, 255, 255, 0.9);
--bg-card: rgba(255, 255, 255, 0.95);
--bg-overlay: rgba(255, 255, 255, 0.8);

/* 文字色系 - 深色系确保可读性 */
--text-primary: #374151;      /* 主要文字 */
--text-secondary: #6b7280;    /* 次要文字 */
--text-muted: #9ca3af;        /* 辅助文字 */
--text-light: #d1d5db;        /* 浅色文字 */

/* 品牌色系 - 蓝紫渐变 */
--brand-primary: #3b82f6;     /* 主品牌色 */
--brand-secondary: #8b5cf6;   /* 次品牌色 */
--brand-accent: #06b6d4;      /* 强调色 */
--brand-success: #10b981;     /* 成功色 */

/* 渐变色系 */
--gradient-brand: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
--gradient-text: linear-gradient(135deg, #1e293b 0%, #3b82f6 100%);
--gradient-button: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
```

##### **边框与阴影系统**
```css
/* 边框色系 */
--border-light: rgba(59, 130, 246, 0.1);
--border-medium: rgba(59, 130, 246, 0.2);
--border-strong: rgba(59, 130, 246, 0.3);

/* 阴影系统 */
--shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
--shadow-md: 0 4px 16px rgba(0, 0, 0, 0.1);
--shadow-lg: 0 8px 32px rgba(0, 0, 0, 0.1);
--shadow-brand: 0 4px 12px rgba(59, 130, 246, 0.2);
```

#### **🖼️ 背景装饰规范**

##### **动态网格背景**
```css
.grid-background {
  background-image:
    linear-gradient(rgba(59, 130, 246, 0.05) 1px, transparent 1px),
    linear-gradient(90deg, rgba(59, 130, 246, 0.05) 1px, transparent 1px);
  background-size: 60px 60px;
  animation: grid-move 30s linear infinite;
}

@keyframes grid-move {
  0% { transform: translate(0, 0); }
  100% { transform: translate(60px, 60px); }
}
```

##### **浮动装饰元素**
```css
.floating-elements .element {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.08) 0%, rgba(139, 92, 246, 0.08) 100%);
  animation: float-element 20s ease-in-out infinite;
}

@keyframes float-element {
  0%, 100% {
    transform: translateY(0px) scale(1) rotate(0deg);
    opacity: 0.05;
  }
  50% {
    transform: translateY(-20px) scale(1.05) rotate(180deg);
    opacity: 0.1;
  }
}
```

#### **🧭 导航栏设计规范**

##### **导航栏结构**
```vue
<nav class="navbar">
  <div class="nav-content">
    <div class="nav-brand">
      <router-link to="/" class="brand-link">
        <span class="brand-text">智界AIGC</span>
      </router-link>
    </div>
    <div class="nav-actions">
      <!-- 导航按钮 -->
    </div>
  </div>
</nav>
```

##### **导航栏样式标准**
```css
.navbar {
  position: fixed;
  top: 0;
  width: 100%;
  z-index: 1000;
  padding: 1.5rem 2rem;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(59, 130, 246, 0.1);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.brand-text {
  font-size: 1.5rem;
  font-weight: 700;
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.nav-btn {
  padding: 0.75rem 1.5rem;
  background: rgba(59, 130, 246, 0.1);
  border: 1px solid rgba(59, 130, 246, 0.2);
  border-radius: 12px;
  color: #374151;
  transition: all 0.3s ease;
}

.nav-btn:hover {
  background: rgba(59, 130, 246, 0.15);
  border-color: rgba(59, 130, 246, 0.3);
  color: #1e40af;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
}
```

#### **🃏 卡片组件设计规范**

##### **主要卡片样式**
```css
.card-primary {
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid rgba(59, 130, 246, 0.1);
  border-radius: 24px;
  padding: 3rem;
  backdrop-filter: blur(20px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.card-secondary {
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(59, 130, 246, 0.1);
  border-radius: 16px;
  padding: 1.5rem;
  backdrop-filter: blur(10px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.card-hover:hover {
  background: rgba(255, 255, 255, 0.95);
  border-color: rgba(59, 130, 246, 0.2);
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}
```

#### **🔘 按钮设计规范**

##### **主要按钮**
```css
.btn-primary {
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  border: none;
  border-radius: 12px;
  color: white;
  font-weight: 600;
  padding: 1rem 2rem;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.btn-primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.btn-primary:hover::before {
  left: 100%;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 32px rgba(59, 130, 246, 0.4);
}
```

##### **次要按钮**
```css
.btn-secondary {
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(59, 130, 246, 0.2);
  border-radius: 12px;
  color: #6b7280;
  font-weight: 500;
  padding: 0.75rem 1.5rem;
  transition: all 0.3s ease;
}

.btn-secondary:hover {
  background: rgba(255, 255, 255, 0.95);
  border-color: rgba(59, 130, 246, 0.3);
  color: #374151;
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}
```

#### **📝 表单设计规范**

##### **输入框样式**
```css
.form-input {
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  background: #ffffff;
  color: #374151;
  padding: 1rem;
  transition: all 0.3s ease;
}

.form-input:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  outline: none;
}

.form-input:hover {
  border-color: #9ca3af;
}
```

##### **表单布局**
```css
.form-group {
  margin-bottom: 1.5rem;
}

.form-row {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.form-section {
  background: rgba(255, 255, 255, 0.6);
  border-radius: 16px;
  padding: 2rem;
  margin-bottom: 2rem;
}
```

#### **📱 响应式设计规范**

##### **断点系统**
```css
/* 移动端优先的响应式断点 */
@media (min-width: 640px) { /* sm */ }
@media (min-width: 768px) { /* md */ }
@media (min-width: 1024px) { /* lg */ }
@media (min-width: 1280px) { /* xl */ }
@media (min-width: 1536px) { /* 2xl */ }
```

##### **响应式组件调整**
```css
/* 导航栏响应式 */
@media (max-width: 768px) {
  .navbar {
    padding: 1rem;
  }

  .nav-btn span {
    display: none; /* 移动端隐藏按钮文字 */
  }
}

/* 卡片响应式 */
@media (max-width: 768px) {
  .card-primary {
    padding: 2rem 1.5rem;
    border-radius: 16px;
  }
}
```

### 6. GSAP动效开发规范 ⭐
**强制要求：所有官网动画必须使用GSAP动画库实现，禁止使用CSS动画或其他动画库**

#### GSAP技术栈
```javascript
// 核心库导入
import { gsap } from "gsap"
import { ScrollTrigger } from "gsap/ScrollTrigger"
import { TextPlugin } from "gsap/TextPlugin"
import { MotionPathPlugin } from "gsap/MotionPathPlugin"
import { MorphSVGPlugin } from "gsap/MorphSVGPlugin"

// 注册插件
gsap.registerPlugin(ScrollTrigger, TextPlugin, MotionPathPlugin, MorphSVGPlugin)
```

#### 动效设计原则
- **性能优先** - 确保60fps流畅体验，使用transform和opacity
- **渐进增强** - 低端设备降级处理，移动端简化动效
- **用户体验** - 动效服务于功能，不喧宾夺主
- **品牌一致** - 统一的动效风格和时长
- **强制使用GSAP** - 所有动画必须用GSAP实现，保证一致性

#### 🎯 官网页面动画规范

##### **1. 首页动画规范**
```javascript
// 首页Hero区域动画
const initHomeAnimations = () => {
  const tl = gsap.timeline()

  // 轮播图入场动画
  tl.from(".carousel-container", {
    duration: 0.8,
    scale: 0.9,
    opacity: 0,
    ease: "power2.out"
  })

  // 标题依次出现
  .from(".title-line", {
    duration: 0.6,
    y: 50,
    opacity: 0,
    stagger: 0.1,
    ease: "power2.out"
  }, "-=0.3")

  // 副标题出现
  .from(".title-subtitle", {
    duration: 0.6,
    y: 30,
    opacity: 0,
    ease: "power2.out"
  }, "-=0.4")

  return tl
}

// 功能特性区域动画
const initFeaturesAnimation = () => {
  return gsap.from(".feature-card", {
    duration: 0.8,
    y: 100,
    opacity: 0,
    rotation: 5,
    stagger: 0.15,
    ease: "back.out(1.7)",
    scrollTrigger: {
      trigger: ".features-section",
      start: "top 75%",
      toggleActions: "play none none reverse"
    }
  })
}

// 统计数据动画
const initStatsAnimation = () => {
  const counters = document.querySelectorAll('.counter-number')

  counters.forEach(counter => {
    const target = parseInt(counter.dataset.target)

    gsap.to(counter, {
      duration: 2,
      innerHTML: target,
      ease: "power2.out",
      snap: { innerHTML: 1 },
      scrollTrigger: {
        trigger: counter,
        start: "top 80%",
        toggleActions: "play none none none"
      }
    })
  })
}
```

##### **2. 导航栏动画规范**
```javascript
// 导航栏入场动画
const initNavbarAnimations = () => {
  return gsap.from([".nav-brand", ".nav-menu", ".nav-actions"], {
    duration: 0.4,
    y: -20,
    opacity: 0,
    ease: "power2.out",
    stagger: 0.05
  })
}

// 滚动时导航栏变化
const initNavbarScrollEffect = () => {
  ScrollTrigger.create({
    start: "top -80",
    end: 99999,
    toggleClass: {
      className: "navbar-scrolled",
      targets: ".navbar"
    }
  })
}

// 移动端菜单动画
const initMobileMenuAnimation = () => {
  const tl = gsap.timeline({ paused: true })

  tl.to(".mobile-menu", {
    duration: 0.3,
    x: 0,
    ease: "power2.out"
  })
  .from(".mobile-menu-item", {
    duration: 0.4,
    x: 50,
    opacity: 0,
    stagger: 0.1,
    ease: "power2.out"
  }, "-=0.2")

  return tl
}
```

##### **3. 登录页面动画规范**
```javascript
// 登录页面入场动画
const initLoginAnimations = () => {
  // 左侧信息区域动画
  gsap.from(".login-info", {
    duration: 1,
    x: -100,
    opacity: 0,
    ease: "power2.out"
  })

  // 右侧登录表单动画
  gsap.from(".login-container", {
    duration: 1,
    x: 100,
    opacity: 0,
    ease: "power2.out",
    delay: 0.2
  })

  // 特性列表依次出现
  gsap.from(".feature-item", {
    duration: 0.6,
    y: 30,
    opacity: 0,
    stagger: 0.1,
    ease: "power2.out",
    delay: 0.5
  })
}

// Logo浮动动画
const initLogoFloatAnimation = () => {
  return gsap.to(".logo-icon-large", {
    duration: 3,
    y: -10,
    ease: "power2.inOut",
    repeat: -1,
    yoyo: true
  })
}
```

##### **4. 商城页面动画规范**
```javascript
// 插件卡片瀑布流动画
const initPluginCardsAnimation = () => {
  return gsap.from(".plugin-card", {
    duration: 0.6,
    y: 50,
    opacity: 0,
    stagger: {
      amount: 1,
      grid: "auto",
      from: "start"
    },
    ease: "power2.out"
  })
}

// 筛选切换动画
const initFilterAnimation = () => {
  const filterCards = (category) => {
    const tl = gsap.timeline()

    // 先隐藏所有卡片
    tl.to(".plugin-card", {
      duration: 0.3,
      scale: 0.8,
      opacity: 0,
      stagger: 0.05
    })

    // 重新显示匹配的卡片
    .to(`.plugin-card[data-category="${category}"]`, {
      duration: 0.4,
      scale: 1,
      opacity: 1,
      stagger: 0.08,
      ease: "back.out(1.7)"
    })

    return tl
  }

  return { filterCards }
}

// 卡片悬停效果
const initCardHoverEffects = () => {
  document.querySelectorAll('.plugin-card').forEach(card => {
    card.addEventListener('mouseenter', () => {
      gsap.to(card, {
        duration: 0.3,
        scale: 1.05,
        y: -10,
        boxShadow: "0 20px 40px rgba(0,0,0,0.1)",
        ease: "power2.out"
      })
    })

    card.addEventListener('mouseleave', () => {
      gsap.to(card, {
        duration: 0.3,
        scale: 1,
        y: 0,
        boxShadow: "0 5px 15px rgba(0,0,0,0.05)",
        ease: "power2.out"
      })
    })
  })
}
```

#### 🎨 MorphSVG形状变换动画规范

##### **MorphSVGPlugin核心技术**
```javascript
// MorphSVG插件导入和注册
import { MorphSVGPlugin } from "gsap/MorphSVGPlugin"
gsap.registerPlugin(MorphSVGPlugin)

// 基础形状变换
const morphAnimation = gsap.to(svgPath, {
  duration: 0.5,
  morphSVG: {
    type: "rotational",      // 变形类型
    map: "complexity",       // 映射方式
    shape: targetPath        // 目标路径
  }
})
```

##### **变形类型配置**
```javascript
// 变形类型选择指南
const morphTypes = {
  rotational: "旋转式变形 - 适合播放/暂停、展开/收起等对称变换",
  linear: "线性变形 - 适合相似形状间的变换",
  auto: "自动选择 - 让GSAP自动选择最佳变形方式"
}

// 映射方式选择
const mapTypes = {
  complexity: "复杂度映射 - 性能最佳，适合大多数场景",
  position: "位置映射 - 更精确的点对点映射",
  size: "大小映射 - 适合缩放类变换"
}
```

##### **智界AIGC应用场景**
```javascript
// 播放/暂停按钮变形
const playPauseButton = {
  playPath: "M3.5 5L3.50049 3.9468C3.50049 3.177 4.33382 2.69588 5.00049 3.08078L20.0005 11.741C20.6672 12.1259 20.6672 13.0882 20.0005 13.4731L17.2388 15.1412L17.0055 15.2759M3.50049 8L3.50049 21.2673C3.50049 22.0371 4.33382 22.5182 5.00049 22.1333L14.1192 16.9423L14.4074 16.7759",
  pausePath: "M15.5004 4.05859V5.0638V5.58691V8.58691V15.5869V19.5869V21.2549M8.5 3.96094V10.3721V17V19L8.5 21",

  toggle: (iconPath, isPlaying, animManager) => {
    const morphAnim = gsap.to(iconPath, {
      duration: 0.5,
      morphSVG: {
        type: "rotational",
        map: "complexity",
        shape: isPlaying ? playPauseButton.pausePath : playPauseButton.playPath
      }
    })

    animManager.add(morphAnim)
    return morphAnim
  }
}

// AI生成开始/停止按钮
const aiControlButton = {
  startPath: "M8 5v14l11-7z",  // 播放三角形
  stopPath: "M6 6h12v12H6z",   // 停止方块

  toggle: (iconPath, isGenerating, animManager) => {
    const morphAnim = gsap.to(iconPath, {
      duration: 0.4,
      morphSVG: {
        type: "rotational",
        map: "complexity",
        shape: isGenerating ? aiControlButton.stopPath : aiControlButton.startPath
      }
    })

    animManager.add(morphAnim)
    return morphAnim
  }
}

// 展开/收起箭头
const expandCollapseArrow = {
  expandPath: "M19 9l-7 7-7-7",    // 向下箭头
  collapsePath: "M5 15l7-7 7 7",  // 向上箭头

  toggle: (iconPath, isExpanded, animManager) => {
    const morphAnim = gsap.to(iconPath, {
      duration: 0.3,
      morphSVG: {
        type: "rotational",
        map: "complexity",
        shape: isExpanded ? expandCollapseArrow.collapsePath : expandCollapseArrow.expandPath
      }
    })

    animManager.add(morphAnim)
    return morphAnim
  }
}

// 音量控制按钮
const volumeButton = {
  mutePath: "M16 9a5 5 0 0 1 0 6m-2.5-8.5L9 10.5H5v3h4l4.5 3.5V6.5z",     // 有声音
  unmutePath: "M11 5L6 9H2v6h4l5 4V5zM15.54 8.46a5 5 0 0 1 0 7.07",        // 静音

  toggle: (iconPath, isMuted, animManager) => {
    const morphAnim = gsap.to(iconPath, {
      duration: 0.4,
      morphSVG: {
        type: "rotational",
        map: "complexity",
        shape: isMuted ? volumeButton.unmutePath : volumeButton.mutePath
      }
    })

    animManager.add(morphAnim)
    return morphAnim
  }
}
```

##### **MorphSVG性能优化**
```javascript
// 性能优化最佳实践
const morphOptimization = {
  // 1. 预处理SVG路径
  preprocessPaths: (paths) => {
    // 确保路径点数相近，减少计算复杂度
    return paths.map(path => {
      return MorphSVGPlugin.pathDataToBezier(path, {
        offsetX: 0,
        offsetY: 0
      })
    })
  },

  // 2. 缓存变形动画
  createCachedMorph: (element, pathA, pathB) => {
    const timeline = gsap.timeline({ paused: true })

    timeline.to(element, {
      duration: 0.5,
      morphSVG: {
        type: "rotational",
        map: "complexity",
        shape: pathB
      }
    })

    return {
      forward: () => timeline.play(),
      reverse: () => timeline.reverse()
    }
  },

  // 3. 批量变形优化
  batchMorph: (elements, targetPaths, animManager) => {
    const tl = gsap.timeline()

    elements.forEach((element, index) => {
      tl.to(element, {
        duration: 0.5,
        morphSVG: {
          type: "rotational",
          map: "complexity",
          shape: targetPaths[index]
        }
      }, index * 0.1) // 错开0.1秒
    })

    animManager.addTimeline(tl)
    return tl
  }
}
```

##### **Vue组件集成示例**
```vue
<!-- MorphingButton.vue -->
<template>
  <button @click="toggle" class="morphing-button" :class="{ active: isActive }">
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="morph-icon">
      <path
        ref="iconPath"
        :d="currentPath"
        stroke="currentColor"
        stroke-width="2"
        fill="none"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>
  </button>
</template>

<script>
import { gsap } from 'gsap'
import { MorphSVGPlugin } from 'gsap/MorphSVGPlugin'
import { AnimationManager } from '@/animations/gsap/managers/AnimationManager.js'

gsap.registerPlugin(MorphSVGPlugin)

export default {
  name: 'MorphingButton',
  props: {
    type: {
      type: String,
      default: 'playPause', // playPause, aiControl, expandCollapse, volume
      validator: value => ['playPause', 'aiControl', 'expandCollapse', 'volume'].includes(value)
    },
    initialState: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      isActive: this.initialState,
      animManager: new AnimationManager(),
      pathConfigs: {
        playPause: {
          inactive: "M3.5 5L3.50049 3.9468C3.50049 3.177 4.33382 2.69588 5.00049 3.08078L20.0005 11.741C20.6672 12.1259 20.6672 13.0882 20.0005 13.4731L17.2388 15.1412L17.0055 15.2759M3.50049 8L3.50049 21.2673C3.50049 22.0371 4.33382 22.5182 5.00049 22.1333L14.1192 16.9423L14.4074 16.7759",
          active: "M15.5004 4.05859V5.0638V5.58691V8.58691V15.5869V19.5869V21.2549M8.5 3.96094V10.3721V17V19L8.5 21"
        },
        aiControl: {
          inactive: "M8 5v14l11-7z",
          active: "M6 6h12v12H6z"
        },
        expandCollapse: {
          inactive: "M19 9l-7 7-7-7",
          active: "M5 15l7-7 7 7"
        },
        volume: {
          inactive: "M16 9a5 5 0 0 1 0 6m-2.5-8.5L9 10.5H5v3h4l4.5 3.5V6.5z",
          active: "M11 5L6 9H2v6h4l5 4V5zM15.54 8.46a5 5 0 0 1 0 7.07"
        }
      }
    }
  },
  computed: {
    currentPath() {
      const config = this.pathConfigs[this.type]
      return this.isActive ? config.active : config.inactive
    }
  },
  methods: {
    toggle() {
      this.isActive = !this.isActive
      this.morphToState()
      this.$emit('toggle', this.isActive)
    },

    morphToState() {
      const targetPath = this.currentPath

      const morphAnim = gsap.to(this.$refs.iconPath, {
        duration: 0.5,
        morphSVG: {
          type: "rotational",
          map: "complexity",
          shape: targetPath
        },
        ease: "power2.out"
      })

      this.animManager.add(morphAnim)
    }
  },
  beforeDestroy() {
    this.animManager.killAll()
  }
}
</script>

<style scoped>
.morphing-button {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 0.5rem;
  transition: background-color 0.3s ease;
}

.morphing-button:hover {
  background-color: rgba(0, 0, 0, 0.1);
}

.morph-icon {
  width: 24px;
  height: 24px;
  color: currentColor;
}
</style>
```

#### 🔧 标准动效库
```javascript
// 页面入场动画（通用）
const pageEnterAnimation = (elements) => {
  return gsap.from(elements, {
    duration: 0.8,
    y: 50,
    opacity: 0,
    stagger: 0.1,
    ease: "power2.out"
  })
}

// 滚动触发动画（通用）
const scrollTriggerAnimation = (trigger, elements) => {
  return gsap.from(elements, {
    duration: 0.8,
    y: 100,
    opacity: 0,
    stagger: 0.2,
    ease: "power2.out",
    scrollTrigger: {
      trigger: trigger,
      start: "top 80%",
      end: "bottom 20%",
      toggleActions: "play none none reverse"
    }
  })
}

// 视差滚动效果（通用）
const parallaxEffect = (element, speed = -50) => {
  return gsap.to(element, {
    yPercent: speed,
    ease: "none",
    scrollTrigger: {
      trigger: element,
      start: "top bottom",
      end: "bottom top",
      scrub: true
    }
  })
}

// 按钮悬停效果（通用）
const buttonHoverEffect = (button) => {
  button.addEventListener('mouseenter', () => {
    gsap.to(button, {
      duration: 0.3,
      scale: 1.05,
      ease: "power2.out"
    })
  })

  button.addEventListener('mouseleave', () => {
    gsap.to(button, {
      duration: 0.3,
      scale: 1,
      ease: "power2.out"
    })
  })
}
```

#### ⚡ 动效性能规范
- **避免动画属性** - 不要动画width、height、left、top、margin、padding
- **优先使用** - transform（translateX/Y/Z、scale、rotate）、opacity属性
- **合理使用will-change** - 提前告知浏览器优化
- **及时清理** - 组件销毁时清理动画实例
- **硬件加速** - 使用transform3d或translateZ(0)启用GPU加速

#### 📱 移动端适配规范
```javascript
// 移动端检测
const isMobile = () => window.innerWidth <= 768
const isLowEndDevice = () => navigator.hardwareConcurrency <= 2

// 移动端动效简化
const initResponsiveAnimations = () => {
  if (isMobile() || isLowEndDevice()) {
    // 移动端使用简化动效
    gsap.set("*", { force3D: false }) // 禁用3D变换
    gsap.globalTimeline.timeScale(1.5) // 加快动画速度

    // 简化动画
    return gsap.from(".element", {
      duration: 0.3,
      opacity: 0,
      ease: "power1.out"
    })
  } else {
    // 桌面端使用完整动效
    return initComplexAnimations()
  }
}
```

## 📁 项目结构规范

### 1. 前端官网文件组织规范

#### **官网页面统一存放规则**
所有官网相关的页面文件必须统一放在：
```
AigcViewFe/智界Aigc/src/views/website/
```

**重要说明**：此规则适用于所有官网功能，包括现有的8个模块和未来可能新增的任何官网模块。

#### **具体文件结构规范**
```
AigcViewFe/智界Aigc/src/views/
├── dashboard/              # 现有：仪表板页面
├── aigcview/              # 现有：后台管理业务页面
├── system/                # 现有：系统管理页面
├── user/                  # 现有：用户相关页面
└── website/               # 新增：官网页面统一目录 ⭐
    ├── home/              # 首页模块
    │   ├── Home.vue
    │   └── components/    # 首页专用组件
    ├── market/            # 商城模块
    │   ├── Market.vue
    │   └── components/    # 商城专用组件
    ├── cases/             # 客户案例模块
    │   ├── Cases.vue
    │   └── components/
    ├── tutorials/         # 教程中心模块
    │   ├── Tutorials.vue
    │   └── components/
    ├── signin/            # 签到奖励模块
    │   ├── SignIn.vue
    │   └── components/
    ├── membership/        # 订阅会员模块
    │   ├── Membership.vue
    │   └── components/
    ├── affiliate/         # 分销推广模块
    │   ├── Affiliate.vue
    │   └── components/
    └── usercenter/        # 个人中心模块
        ├── UserCenter.vue
        └── components/
```

#### **开发规则（适用于所有官网模块）**
- ✅ **必须遵守**：所有官网页面都放在 `views/website/` 目录下
- ✅ **按模块分文件夹**：每个功能模块有独立的文件夹
- ✅ **组件就近原则**：模块专用组件放在对应模块的 `components/` 下
- ✅ **命名规范**：文件夹用小写，Vue文件用大驼峰
- ✅ **新增模块规范**：未来新增的任何官网功能模块都必须遵循相同规则
- ❌ **禁止**：在其他目录下创建官网相关页面

#### **新增模块示例**
如果未来需要新增官网模块（如：活动页面、帮助中心等），文件结构应为：
```
views/website/
├── 现有模块.../
├── activity/              # 新增：活动页面模块
│   ├── Activity.vue
│   └── components/
└── help/                  # 新增：帮助中心模块
    ├── Help.vue
    └── components/
```

## 🎨 GSAP动效实施计划与管理规范

### 1. 动画管理器规范 ⭐

#### **AnimationManager类（必须使用）**
```javascript
// 动画实例管理器 - 所有页面必须使用
class AnimationManager {
  constructor() {
    this.animations = []
    this.scrollTriggers = []
    this.timelines = []
  }

  // 添加动画实例
  add(animation) {
    this.animations.push(animation)
    return animation
  }

  // 添加ScrollTrigger实例
  addScrollTrigger(trigger) {
    this.scrollTriggers.push(trigger)
    return trigger
  }

  // 添加Timeline实例
  addTimeline(timeline) {
    this.timelines.push(timeline)
    return timeline
  }

  // 清理所有动画
  killAll() {
    this.animations.forEach(anim => anim.kill())
    this.scrollTriggers.forEach(trigger => trigger.kill())
    this.timelines.forEach(tl => tl.kill())
    this.animations = []
    this.scrollTriggers = []
    this.timelines = []
  }

  // 暂停所有动画
  pauseAll() {
    this.animations.forEach(anim => anim.pause())
    this.timelines.forEach(tl => tl.pause())
  }

  // 恢复所有动画
  resumeAll() {
    this.animations.forEach(anim => anim.resume())
    this.timelines.forEach(tl => tl.resume())
  }

  // 获取动画状态
  getStatus() {
    return {
      animations: this.animations.length,
      scrollTriggers: this.scrollTriggers.length,
      timelines: this.timelines.length
    }
  }
}
```

#### **Vue组件中的标准使用方式**
```javascript
// 所有使用GSAP的Vue组件必须遵循此模式
export default {
  name: 'ComponentName',
  data() {
    return {
      animManager: new AnimationManager() // 必须创建动画管理器
    }
  },

  mounted() {
    this.$nextTick(() => {
      this.initAnimations()
    })
  },

  methods: {
    initAnimations() {
      // 页面入场动画
      const enterAnim = gsap.from(".element", {
        duration: 0.8,
        y: 50,
        opacity: 0,
        ease: "power2.out"
      })
      this.animManager.add(enterAnim) // 必须添加到管理器

      // 滚动触发动画
      const scrollTrigger = ScrollTrigger.create({
        trigger: ".trigger-element",
        start: "top 80%",
        animation: gsap.from(".target", {
          y: 100,
          opacity: 0,
          duration: 1
        })
      })
      this.animManager.addScrollTrigger(scrollTrigger) // 必须添加到管理器

      // Timeline动画
      const tl = gsap.timeline()
      tl.from(".title", { y: 50, opacity: 0, duration: 0.6 })
        .from(".subtitle", { y: 30, opacity: 0, duration: 0.4 }, "-=0.3")
      this.animManager.addTimeline(tl) // 必须添加到管理器
    }
  },

  beforeDestroy() {
    // 必须清理所有动画资源
    this.animManager.killAll()
    console.log('动画资源已清理')
  }
}
```

### 2. 性能监控与优化规范

#### **性能监控器（必须集成）**
```javascript
// 动画性能监控器
class AnimationPerformanceMonitor {
  constructor() {
    this.frameCount = 0
    this.lastTime = performance.now()
    this.fpsHistory = []
    this.isMonitoring = false
  }

  start() {
    if (this.isMonitoring) return
    this.isMonitoring = true
    this.checkFPS()
  }

  stop() {
    this.isMonitoring = false
  }

  checkFPS() {
    if (!this.isMonitoring) return

    this.frameCount++
    const currentTime = performance.now()

    if (currentTime - this.lastTime >= 1000) {
      const fps = Math.round((this.frameCount * 1000) / (currentTime - this.lastTime))
      this.fpsHistory.push(fps)

      // 保持最近10秒的FPS记录
      if (this.fpsHistory.length > 10) {
        this.fpsHistory.shift()
      }

      // 性能警告和降级处理
      if (fps < 30) {
        console.warn(`动画性能警告: FPS=${fps}，低于30fps`)
        this.handleLowPerformance()
      }

      // 重置计数器
      this.frameCount = 0
      this.lastTime = currentTime
    }

    requestAnimationFrame(() => this.checkFPS())
  }

  handleLowPerformance() {
    // 降级处理策略
    gsap.globalTimeline.timeScale(1.5) // 加快动画速度

    // 禁用复杂动画
    document.body.classList.add('low-performance-mode')

    // 简化ScrollTrigger
    ScrollTrigger.getAll().forEach(trigger => {
      if (trigger.animation) {
        trigger.animation.duration(trigger.animation.duration() * 0.5)
      }
    })
  }

  getAverageFPS() {
    if (this.fpsHistory.length === 0) return 0
    return Math.round(this.fpsHistory.reduce((a, b) => a + b) / this.fpsHistory.length)
  }
}

// 全局性能监控实例
const perfMonitor = new AnimationPerformanceMonitor()

// 在应用启动时开始监控
perfMonitor.start()
```

#### **设备性能检测**
```javascript
// 设备性能检测器
class DeviceCapabilityDetector {
  constructor() {
    this.capabilities = this.detectCapabilities()
  }

  detectCapabilities() {
    const capabilities = {
      isMobile: this.isMobile(),
      isLowEndDevice: this.isLowEndDevice(),
      supportsWebGL: this.supportsWebGL(),
      hardwareConcurrency: navigator.hardwareConcurrency || 1,
      deviceMemory: navigator.deviceMemory || 1,
      connectionType: this.getConnectionType()
    }

    console.log('设备性能检测结果:', capabilities)
    return capabilities
  }

  isMobile() {
    return window.innerWidth <= 768 || /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)
  }

  isLowEndDevice() {
    return (
      (navigator.hardwareConcurrency && navigator.hardwareConcurrency <= 2) ||
      (navigator.deviceMemory && navigator.deviceMemory <= 2) ||
      this.isMobile()
    )
  }

  supportsWebGL() {
    try {
      const canvas = document.createElement('canvas')
      return !!(canvas.getContext('webgl') || canvas.getContext('experimental-webgl'))
    } catch (e) {
      return false
    }
  }

  getConnectionType() {
    const connection = navigator.connection || navigator.mozConnection || navigator.webkitConnection
    return connection ? connection.effectiveType : 'unknown'
  }

  // 根据设备能力调整动画配置
  getAnimationConfig() {
    if (this.capabilities.isLowEndDevice) {
      return {
        duration: 0.3,
        ease: "power1.out",
        stagger: 0.05,
        enableComplexAnimations: false,
        enableParallax: false,
        enableScrollTrigger: false
      }
    } else if (this.capabilities.isMobile) {
      return {
        duration: 0.5,
        ease: "power2.out",
        stagger: 0.08,
        enableComplexAnimations: false,
        enableParallax: true,
        enableScrollTrigger: true
      }
    } else {
      return {
        duration: 0.8,
        ease: "power2.out",
        stagger: 0.1,
        enableComplexAnimations: true,
        enableParallax: true,
        enableScrollTrigger: true
      }
    }
  }
}

// 全局设备检测实例
const deviceDetector = new DeviceCapabilityDetector()
```

### 3. 响应式动画配置

#### **自适应动画系统**
```javascript
// 响应式动画配置管理器
class ResponsiveAnimationConfig {
  constructor() {
    this.config = deviceDetector.getAnimationConfig()
    this.breakpoints = {
      mobile: 768,
      tablet: 1024,
      desktop: 1200
    }
  }

  // 获取当前设备类型
  getCurrentDeviceType() {
    const width = window.innerWidth
    if (width <= this.breakpoints.mobile) return 'mobile'
    if (width <= this.breakpoints.tablet) return 'tablet'
    return 'desktop'
  }

  // 获取适配的动画参数
  getAnimationParams(baseParams) {
    const deviceType = this.getCurrentDeviceType()

    switch (deviceType) {
      case 'mobile':
        return {
          ...baseParams,
          duration: baseParams.duration * 0.6,
          stagger: baseParams.stagger * 0.5,
          ease: "power1.out"
        }
      case 'tablet':
        return {
          ...baseParams,
          duration: baseParams.duration * 0.8,
          stagger: baseParams.stagger * 0.8,
          ease: "power2.out"
        }
      default:
        return baseParams
    }
  }

  // 条件执行动画
  executeIfSupported(animationFn, fallbackFn = null) {
    if (this.config.enableComplexAnimations) {
      return animationFn()
    } else if (fallbackFn) {
      return fallbackFn()
    } else {
      // 简化版动画
      return gsap.set(".element", { opacity: 1 })
    }
  }
}

// 全局响应式配置实例
const responsiveConfig = new ResponsiveAnimationConfig()

// 使用示例
const initResponsiveAnimation = () => {
  const baseParams = {
    duration: 0.8,
    y: 50,
    opacity: 0,
    stagger: 0.1,
    ease: "power2.out"
  }

  const adaptedParams = responsiveConfig.getAnimationParams(baseParams)

  return responsiveConfig.executeIfSupported(
    // 完整动画
    () => gsap.from(".element", adaptedParams),
    // 降级动画
    () => gsap.from(".element", { opacity: 0, duration: 0.3 })
  )
}
```

### 4. 动画预设库

#### **标准动画预设**
```javascript
// 动画预设库 - 确保一致性
const AnimationPresets = {
  // 页面入场动画
  pageEnter: (elements, options = {}) => {
    const params = responsiveConfig.getAnimationParams({
      duration: 0.8,
      y: 50,
      opacity: 0,
      stagger: 0.1,
      ease: "power2.out",
      ...options
    })

    return gsap.from(elements, params)
  },

  // 卡片悬停效果
  cardHover: (element) => {
    const hoverTl = gsap.timeline({ paused: true })

    hoverTl.to(element, {
      duration: 0.3,
      scale: 1.05,
      y: -10,
      boxShadow: "0 20px 40px rgba(0,0,0,0.1)",
      ease: "power2.out"
    })

    element.addEventListener('mouseenter', () => hoverTl.play())
    element.addEventListener('mouseleave', () => hoverTl.reverse())

    return hoverTl
  },

  // 数字递增动画
  counterUp: (element, target, options = {}) => {
    const params = {
      duration: 2,
      ease: "power2.out",
      ...options
    }

    return gsap.to(element, {
      ...params,
      innerHTML: target,
      snap: { innerHTML: 1 }
    })
  },

  // 文字打字机效果
  typewriter: (element, text, options = {}) => {
    const params = {
      duration: text.length * 0.05,
      ease: "none",
      ...options
    }

    return gsap.to(element, {
      ...params,
      text: text
    })
  },

  // SVG形状变换效果
  morphShape: (element, targetPath, options = {}) => {
    const params = {
      duration: 0.5,
      ease: "power2.out",
      ...options
    }

    return gsap.to(element, {
      ...params,
      morphSVG: {
        type: "rotational",
        map: "complexity",
        shape: targetPath
      }
    })
  },

  // 滚动视差效果
  parallax: (element, speed = -50) => {
    if (!responsiveConfig.config.enableParallax) {
      return null
    }

    return gsap.to(element, {
      yPercent: speed,
      ease: "none",
      scrollTrigger: {
        trigger: element,
        start: "top bottom",
        end: "bottom top",
        scrub: true
      }
    })
  },

  // 滚动触发动画
  scrollReveal: (trigger, elements, options = {}) => {
    if (!responsiveConfig.config.enableScrollTrigger) {
      return gsap.set(elements, { opacity: 1 })
    }

    const params = responsiveConfig.getAnimationParams({
      duration: 0.8,
      y: 100,
      opacity: 0,
      stagger: 0.2,
      ease: "power2.out",
      ...options
    })

    return gsap.from(elements, {
      ...params,
      scrollTrigger: {
        trigger: trigger,
        start: "top 80%",
        end: "bottom 20%",
        toggleActions: "play none none reverse"
      }
    })
  },

  // MorphSVG形状变换预设
  morphButton: {
    // 播放/暂停按钮
    playPause: (element, isPlaying, animManager) => {
      const paths = {
        play: "M3.5 5L3.50049 3.9468C3.50049 3.177 4.33382 2.69588 5.00049 3.08078L20.0005 11.741C20.6672 12.1259 20.6672 13.0882 20.0005 13.4731L17.2388 15.1412L17.0055 15.2759M3.50049 8L3.50049 21.2673C3.50049 22.0371 4.33382 22.5182 5.00049 22.1333L14.1192 16.9423L14.4074 16.7759",
        pause: "M15.5004 4.05859V5.0638V5.58691V8.58691V15.5869V19.5869V21.2549M8.5 3.96094V10.3721V17V19L8.5 21"
      }

      const morphAnim = gsap.to(element, {
        duration: 0.5,
        morphSVG: {
          type: "rotational",
          map: "complexity",
          shape: isPlaying ? paths.pause : paths.play
        },
        ease: "power2.out"
      })

      if (animManager) animManager.add(morphAnim)
      return morphAnim
    },

    // AI生成控制按钮
    aiControl: (element, isGenerating, animManager) => {
      const paths = {
        start: "M8 5v14l11-7z",
        stop: "M6 6h12v12H6z"
      }

      const morphAnim = gsap.to(element, {
        duration: 0.4,
        morphSVG: {
          type: "rotational",
          map: "complexity",
          shape: isGenerating ? paths.stop : paths.start
        },
        ease: "power2.out"
      })

      if (animManager) animManager.add(morphAnim)
      return morphAnim
    },

    // 展开/收起箭头
    expandCollapse: (element, isExpanded, animManager) => {
      const paths = {
        expand: "M19 9l-7 7-7-7",
        collapse: "M5 15l7-7 7 7"
      }

      const morphAnim = gsap.to(element, {
        duration: 0.3,
        morphSVG: {
          type: "rotational",
          map: "complexity",
          shape: isExpanded ? paths.collapse : paths.expand
        },
        ease: "power2.out"
      })

      if (animManager) animManager.add(morphAnim)
      return morphAnim
    },

    // 音量控制按钮
    volume: (element, isMuted, animManager) => {
      const paths = {
        mute: "M16 9a5 5 0 0 1 0 6m-2.5-8.5L9 10.5H5v3h4l4.5 3.5V6.5z",
        unmute: "M11 5L6 9H2v6h4l5 4V5zM15.54 8.46a5 5 0 0 1 0 7.07"
      }

      const morphAnim = gsap.to(element, {
        duration: 0.4,
        morphSVG: {
          type: "rotational",
          map: "complexity",
          shape: isMuted ? paths.unmute : paths.mute
        },
        ease: "power2.out"
      })

      if (animManager) animManager.add(morphAnim)
      return morphAnim
    }
  }
}
```

## ⚠️ 常见问题与解决方案

### 1. 编译兼容性问题

#### **ES2020语法兼容性问题**

**问题描述：**
在使用现代JavaScript语法（如可选链操作符 `?.`）时，可能遇到编译错误：
```
Module parse failed: Unexpected token (784:15)
You may need an additional loader to handle the result of these loaders.
```

**问题原因：**
- 项目的Babel配置不支持ES2020语法
- 可选链操作符（`?.`）需要特定的Babel插件支持
- 当前构建环境可能使用较旧的JavaScript标准

**解决方案：**

##### **方案1：语法降级（推荐）**
将现代语法改为兼容性更好的传统语法：

```javascript
// ❌ 问题代码（ES2020语法）
const startTime = performance.getEntriesByType('paint')
  .find(entry => entry.name === 'first-paint')?.startTime || 0

// ✅ 修复代码（ES5兼容语法）
const paintEntries = performance.getEntriesByType('paint')
const firstPaint = paintEntries.find(function(entry) {
  return entry.name === 'first-paint'
})
const startTime = firstPaint ? firstPaint.startTime : 0
```

##### **方案2：Babel配置升级**
如果需要使用现代语法，可以升级Babel配置：

```json
// babel.config.js 或 .babelrc
{
  "presets": [
    ["@babel/preset-env", {
      "targets": {
        "browsers": ["> 1%", "last 2 versions"]
      }
    }]
  ],
  "plugins": [
    "@babel/plugin-proposal-optional-chaining",
    "@babel/plugin-proposal-nullish-coalescing-operator"
  ]
}
```

#### **性能监控API兼容性问题**

**问题描述：**
使用过时的Performance API时出现警告：
```
'timing' is deprecated.
'domContentLoadedEventEnd' is deprecated.
```

**解决方案：**
使用现代Performance API替代过时的API：

```javascript
// ❌ 过时的API
if (performance && performance.timing) {
  const timing = performance.timing
  const loadTime = timing.loadEventEnd - timing.navigationStart
}

// ✅ 现代API
if (typeof performance !== 'undefined' && performance.getEntriesByType) {
  try {
    const navigationEntries = performance.getEntriesByType('navigation')
    if (navigationEntries.length > 0) {
      const loadTime = navigationEntries[0].loadEventEnd
    }
  } catch (error) {
    console.warn('获取性能指标失败:', error)
  }
}
```

#### **语法兼容性最佳实践**

##### **1. 避免使用的现代语法**
```javascript
// ❌ 避免使用
obj?.property?.method?.()           // 可选链
arr?.[index]                        // 可选索引
value ?? defaultValue               // 空值合并
const { a, ...rest } = obj          // 对象展开（在某些环境）
```

##### **2. 推荐的兼容语法**
```javascript
// ✅ 推荐使用
obj && obj.property && obj.property.method && obj.property.method()
arr && arr[index]
value !== null && value !== undefined ? value : defaultValue
const rest = Object.assign({}, obj); delete rest.a
```

##### **3. 安全的现代语法**
```javascript
// ✅ 广泛支持的ES6+语法
const arrow = () => {}              // 箭头函数
const { a, b } = obj               // 解构赋值
const arr = [...oldArr]            // 数组展开
const obj = { ...oldObj }          // 对象展开（简单情况）
const template = `Hello ${name}`   // 模板字符串
```

#### **兼容性检查清单**

##### **开发前检查**
- [ ] 确认项目的Babel配置和目标浏览器
- [ ] 了解项目支持的JavaScript版本
- [ ] 查看现有代码的语法标准

##### **开发中检查**
- [ ] 避免使用过新的JavaScript语法
- [ ] 使用传统语法替代现代语法
- [ ] 添加适当的错误处理和兼容性检查

##### **编译前检查**
- [ ] 本地编译测试
- [ ] 检查编译警告和错误
- [ ] 验证在目标浏览器中的兼容性

##### **部署前检查**
- [ ] 生产环境编译测试
- [ ] 跨浏览器兼容性测试
- [ ] 性能影响评估

#### **常见兼容性问题汇总**

| 语法特性 | 问题版本 | 兼容替代 | 支持版本 |
|---------|---------|---------|---------|
| 可选链 `?.` | ES2020 | `obj && obj.prop` | ES5+ |
| 空值合并 `??` | ES2020 | `value !== null && value !== undefined ? value : default` | ES5+ |
| 私有字段 `#field` | ES2022 | `this._field` | ES5+ |
| 顶层await | ES2022 | `async function` | ES2017+ |
| 逻辑赋值 `||=` | ES2021 | `a = a || b` | ES5+ |

## 📋 代码规范

### 1. 文件命名规范
```
components/
├── layout/
│   ├── Header.vue          # 大驼峰命名
│   ├── Footer.vue
│   └── Layout.vue
├── common/
│   ├── PluginCard.vue      # 功能描述性命名
│   ├── VideoCard.vue
│   └── UserAvatar.vue
└── business/
    ├── SignInCalendar.vue  # 业务相关组件
    └── MembershipCard.vue
```

### 2. GSAP动效文件组织规范
```javascript
// 动效文件组织结构（必须遵循）
src/
├── animations/
│   ├── gsap/
│   │   ├── managers/         # 动画管理器
│   │   │   ├── AnimationManager.js
│   │   │   ├── PerformanceMonitor.js
│   │   │   ├── DeviceDetector.js
│   │   │   └── ResponsiveConfig.js
│   │   ├── presets/          # 动画预设库
│   │   │   ├── AnimationPresets.js
│   │   │   ├── TransitionPresets.js
│   │   │   ├── EffectPresets.js
│   │   │   └── MorphSVGPresets.js
│   │   ├── pages/            # 页面专用动效
│   │   │   ├── homeAnimations.js
│   │   │   ├── loginAnimations.js
│   │   │   ├── marketAnimations.js
│   │   │   ├── casesAnimations.js
│   │   │   ├── tutorialsAnimations.js
│   │   │   ├── signinAnimations.js
│   │   │   ├── membershipAnimations.js
│   │   │   ├── affiliateAnimations.js
│   │   │   └── usercenterAnimations.js
│   │   ├── components/       # 组件动效
│   │   │   ├── headerAnimations.js
│   │   │   ├── footerAnimations.js
│   │   │   ├── carouselAnimations.js
│   │   │   ├── cardAnimations.js
│   │   │   ├── buttonAnimations.js
│   │   │   └── morphButtonAnimations.js
│   │   └── common/           # 通用动效
│   │       ├── pageTransitions.js
│   │       ├── scrollEffects.js
│   │       ├── hoverEffects.js
│   │       └── loadingEffects.js
│   └── index.js              # 动画库统一导出
```

### 3. API接口规范（含安全分类和数据字段映射）

#### **🔧 统一前后端数据字段映射规范**

##### **核心原则**
- **统一字段名**：确保所有API都使用 `response.result` 字段返回数据
- **兼容处理**：前端使用 `response.result || response.data` 提供向后兼容
- **响应式更新**：使用 `Object.assign` 确保Vue响应式数据正确更新
- **错误处理**：提供完善的错误处理和用户提示

##### **标准响应格式**
```javascript
// ✅ 后端标准响应格式
{
  "success": true,           // 请求是否成功
  "message": "操作成功",      // 响应消息
  "code": 200,              // 状态码
  "result": {               // ✅ 实际数据内容（统一使用此字段）
    // 具体业务数据
  },
  "timestamp": 1640995200000 // 时间戳
}

// ❌ 避免使用的格式
{
  "success": true,
  "data": {                 // ❌ 不推荐：字段名不统一
    // 数据内容
  }
}
```

##### **前端数据获取规范**
```javascript
// ✅ 推荐写法：兼容性处理
const response = await apiCall()
if (response.success) {
  this.data = response.result || response.data || {}
}

// ✅ Vue组件中的标准处理
async loadData() {
  try {
    this.loading = true
    const response = await getApiData()

    if (response.success) {
      // 使用Object.assign确保响应式更新
      Object.assign(this.data, response.result || response.data || {})
      this.$forceUpdate() // 备用方案
    }
  } catch (error) {
    this.$message.error('加载数据失败')
  } finally {
    this.loading = false
  }
}
```

##### **后端Controller规范**
```java
// ✅ 标准Controller返回格式
@RestController
public class DataController {

    @GetMapping("/data")
    public Result<?> getData() {
        try {
            Map<String, Object> data = service.getData();
            return Result.OK(data);  // 数据自动放入result字段
        } catch (Exception e) {
            return Result.error("获取数据失败");
        }
    }
}
```

```javascript
// API文件组织
api/
├── website.js              # 官网相关接口（多为公共访问）
├── user.js                 # 用户相关接口（需要认证）
├── plugin.js               # 插件相关接口（混合类型）
├── admin.js                # 管理相关接口（需要认证）
└── common.js               # 通用接口

// 接口命名规范（按安全级别分类）
export const websiteApi = {
  // 公共访问接口（无需token）
  getHeader: () => get('/api/website/header'),
  getFooter: () => get('/api/website/footer'),
  getHomeData: () => get('/api/website/home'),
  getCarouselList: () => get('/aigc/aigcHomeCarousel/list'),
  getFeaturesList: () => get('/aigc/websiteFeatures/list'),

  // 管理接口（需要token和权限）
  addCarousel: (data) => post('/aigc/aigcHomeCarousel/add', data),
  editCarousel: (data) => put('/aigc/aigcHomeCarousel/edit', data),
  deleteCarousel: (id) => del('/aigc/aigcHomeCarousel/delete', { id })
}

// 接口调用示例（含错误处理和正确的数据字段映射）
export const carouselApi = {
  // 公共查询接口
  async getList() {
    try {
      const response = await get('/aigc/aigcHomeCarousel/list')
      // ✅ 正确：使用 result 字段，提供 data 字段兼容
      return response.result || response.data || []
    } catch (error) {
      console.error('获取轮播图列表失败:', error)
      return [] // 返回空数组作为降级
    }
  },

  // 管理接口（需要认证）
  async add(data) {
    try {
      const response = await post('/aigc/aigcHomeCarousel/add', data)
      // ✅ 正确：统一使用 result 字段
      return response.result || response.data
    } catch (error) {
      if (error.status === 401) {
        // 未认证，跳转登录
        router.push('/login')
      }
      throw error
    }
  }
}

// 🔧 统一数据字段映射规范
export const apiDataMapping = {
  // 标准响应格式
  standardResponse: {
    success: true,      // 请求是否成功
    message: "操作成功", // 响应消息
    code: 200,          // 状态码
    result: {},         // ✅ 实际数据内容（标准字段）
    timestamp: 1640995200000
  },

  // 前端数据获取标准模板
  async getData(apiCall) {
    try {
      const response = await apiCall()
      if (response.success) {
        // ✅ 统一使用 result 字段，提供 data 字段兼容
        return response.result || response.data || {}
      } else {
        throw new Error(response.message || '请求失败')
      }
    } catch (error) {
      console.error('API调用失败:', error)
      throw error
    }
  },

  // Vue组件中的标准数据处理
  async loadComponentData(component, apiCall, dataKey = 'data') {
    try {
      component.loading = true
      const response = await apiCall()

      if (response.success) {
        // ✅ 使用Object.assign确保响应式更新
        const newData = response.result || response.data || {}
        Object.assign(component[dataKey], newData)
        component.$forceUpdate() // 备用方案，确保界面更新
      } else {
        component.$message.error(response.message || '加载数据失败')
      }
    } catch (error) {
      console.error('数据加载失败:', error)
      component.$message.error('加载数据失败，请刷新重试')
    } finally {
      component.loading = false
    }
  }
}
```

### 4. 页面动画实施规范（必须遵循）

#### **首页动画实施（Home.vue）**
```javascript
// src/animations/gsap/pages/homeAnimations.js
import { gsap } from 'gsap'
import { ScrollTrigger } from 'gsap/ScrollTrigger'
import { AnimationPresets } from '../presets/AnimationPresets.js'
import { responsiveConfig } from '../managers/ResponsiveConfig.js'

gsap.registerPlugin(ScrollTrigger)

export const homeAnimations = {
  // 初始化首页所有动画
  init(animManager) {
    const animations = []

    // 1. 导航栏动画
    const navAnim = this.initNavbarAnimation()
    if (navAnim) animations.push(navAnim)

    // 2. Hero区域动画
    const heroAnim = this.initHeroAnimation()
    if (heroAnim) animations.push(heroAnim)

    // 3. 功能特性动画
    const featuresAnim = this.initFeaturesAnimation()
    if (featuresAnim) animations.push(featuresAnim)

    // 4. 统计数据动画
    const statsAnim = this.initStatsAnimation()
    if (statsAnim) animations.push(statsAnim)

    // 5. 视差滚动效果
    const parallaxAnim = this.initParallaxEffects()
    if (parallaxAnim) animations.push(parallaxAnim)

    // 将所有动画添加到管理器
    animations.forEach(anim => {
      if (anim.scrollTrigger) {
        animManager.addScrollTrigger(anim.scrollTrigger)
      } else {
        animManager.add(anim)
      }
    })

    return animations
  },

  // 导航栏动画
  initNavbarAnimation() {
    return AnimationPresets.pageEnter([".nav-brand", ".nav-menu", ".nav-actions"], {
      duration: 0.4,
      y: -20,
      stagger: 0.05
    })
  },

  // Hero区域动画
  initHeroAnimation() {
    const tl = gsap.timeline()

    // 轮播图入场
    tl.from(".carousel-container", {
      duration: 0.8,
      scale: 0.9,
      opacity: 0,
      ease: "power2.out"
    })

    // 标题依次出现（使用GSAP，不用自定义JavaScript）
    .from(".title-line", {
      duration: 0.6,
      y: 50,
      opacity: 0,
      stagger: 0.1,
      ease: "power2.out"
    }, "-=0.3")

    // 副标题出现
    .from(".title-subtitle", {
      duration: 0.6,
      y: 30,
      opacity: 0,
      ease: "power2.out"
    }, "-=0.4")

    return tl
  },

  // 功能特性动画
  initFeaturesAnimation() {
    return AnimationPresets.scrollReveal(".features-section", ".feature-card", {
      duration: 0.8,
      y: 100,
      rotation: 5,
      stagger: 0.15,
      ease: "back.out(1.7)"
    })
  },

  // 统计数据动画
  initStatsAnimation() {
    const counters = document.querySelectorAll('.counter-number')
    const animations = []

    counters.forEach(counter => {
      const target = parseInt(counter.dataset.target)
      const anim = AnimationPresets.counterUp(counter, target, {
        scrollTrigger: {
          trigger: counter,
          start: "top 80%",
          toggleActions: "play none none none"
        }
      })
      animations.push(anim)
    })

    return animations
  },

  // 视差滚动效果
  initParallaxEffects() {
    const parallaxElements = document.querySelectorAll('.parallax-element')
    const animations = []

    parallaxElements.forEach(element => {
      const speed = element.dataset.speed || -50
      const anim = AnimationPresets.parallax(element, speed)
      if (anim) animations.push(anim)
    })

    return animations
  }
}
```

#### **登录页面动画实施（Login.vue）**
```javascript
// src/animations/gsap/pages/loginAnimations.js
import { gsap } from 'gsap'
import { AnimationPresets } from '../presets/AnimationPresets.js'

export const loginAnimations = {
  // 初始化登录页面动画
  init(animManager) {
    const animations = []

    // 1. 页面入场动画
    const enterAnim = this.initPageEnterAnimation()
    animations.push(enterAnim)

    // 2. Logo浮动动画
    const logoAnim = this.initLogoFloatAnimation()
    animations.push(logoAnim)

    // 3. 特性列表动画
    const featuresAnim = this.initFeaturesListAnimation()
    animations.push(featuresAnim)

    // 将动画添加到管理器
    animations.forEach(anim => animManager.add(anim))

    return animations
  },

  // 页面入场动画
  initPageEnterAnimation() {
    const tl = gsap.timeline()

    // 左侧信息区域从左滑入
    tl.from(".login-info", {
      duration: 1,
      x: -100,
      opacity: 0,
      ease: "power2.out"
    })

    // 右侧登录表单从右滑入
    .from(".login-container", {
      duration: 1,
      x: 100,
      opacity: 0,
      ease: "power2.out"
    }, "-=0.8")

    return tl
  },

  // Logo浮动动画
  initLogoFloatAnimation() {
    return gsap.to(".logo-icon-large", {
      duration: 3,
      y: -10,
      ease: "power2.inOut",
      repeat: -1,
      yoyo: true
    })
  },

  // 特性列表动画
  initFeaturesListAnimation() {
    return AnimationPresets.pageEnter(".feature-item", {
      duration: 0.6,
      y: 30,
      stagger: 0.1,
      delay: 0.5
    })
  }
}
```

#### **组件使用规范（含GSAP动效）**
```vue
<!-- 官网页面标准模板：views/website/market/Market.vue -->
<template>
  <Layout>
    <div class="page-content" ref="pageContent">
      <!-- 页面内容 -->
      <PluginCard
        v-for="plugin in plugins"
        :key="plugin.id"
        :plugin="plugin"
        class="plugin-card"
        @click="handlePluginClick"
      />
    </div>
  </Layout>
</template>

<script>
import Layout from '@/components/layout/Layout.vue'
import PluginCard from '@/components/common/PluginCard.vue'
import { marketAnimations } from '@/animations/gsap/pages/marketAnimations.js'
import { AnimationManager } from '@/animations/gsap/managers/AnimationManager.js'
import { perfMonitor } from '@/animations/gsap/managers/PerformanceMonitor.js'

export default {
  name: 'Market',
  components: {
    Layout,
    PluginCard
  },
  data() {
    return {
      plugins: [],
      animManager: new AnimationManager() // 必须创建动画管理器
    }
  },
  async mounted() {
    // 数据从API获取
    this.plugins = await this.$api.plugin.getList()

    // 等待DOM更新后初始化动画
    this.$nextTick(() => {
      this.initAnimations()
    })
  },
  methods: {
    initAnimations() {
      console.log('初始化商城页面动画')

      // 开始性能监控
      perfMonitor.start()

      // 初始化页面动效
      const animations = marketAnimations.init(this.animManager)

      // 记录动画状态
      console.log('动画管理器状态:', this.animManager.getStatus())
    }
  },
  beforeDestroy() {
    // 必须清理动画资源
    console.log('清理商城页面动画资源')
    this.animManager.killAll()

    // 停止性能监控
    perfMonitor.stop()
  }
}
</script>

<style scoped>
/* 动画优化样式 */
.plugin-card {
  will-change: transform, opacity;
  transform: translateZ(0); /* 启用硬件加速 */
}

/* 低性能模式样式 */
.low-performance-mode .plugin-card {
  will-change: auto;
  transform: none;
}

/* 视差元素样式 */
.parallax-element {
  will-change: transform;
}
</style>
```

#### **动画文件导出规范**
```javascript
// src/animations/index.js - 统一导出所有动画
export { AnimationManager } from './gsap/managers/AnimationManager.js'
export { perfMonitor } from './gsap/managers/PerformanceMonitor.js'
export { deviceDetector } from './gsap/managers/DeviceDetector.js'
export { responsiveConfig } from './gsap/managers/ResponsiveConfig.js'
export { AnimationPresets } from './gsap/presets/AnimationPresets.js'

// 页面动画
export { homeAnimations } from './gsap/pages/homeAnimations.js'
export { loginAnimations } from './gsap/pages/loginAnimations.js'
export { marketAnimations } from './gsap/pages/marketAnimations.js'
export { casesAnimations } from './gsap/pages/casesAnimations.js'
export { tutorialsAnimations } from './gsap/pages/tutorialsAnimations.js'
export { signinAnimations } from './gsap/pages/signinAnimations.js'
export { membershipAnimations } from './gsap/pages/membershipAnimations.js'
export { affiliateAnimations } from './gsap/pages/affiliateAnimations.js'
export { usercenterAnimations } from './gsap/pages/usercenterAnimations.js'

// 组件动画
export { headerAnimations } from './gsap/components/headerAnimations.js'
export { footerAnimations } from './gsap/components/footerAnimations.js'
export { carouselAnimations } from './gsap/components/carouselAnimations.js'
export { cardAnimations } from './gsap/components/cardAnimations.js'
export { buttonAnimations } from './gsap/components/buttonAnimations.js'
export { morphButtonAnimations } from './gsap/components/morphButtonAnimations.js'
```

## 🎬 GSAP动画开发检查清单

### 1. 开发前检查 ✅

#### **技术准备**
- [ ] 确认GSAP库已正确安装和配置
- [ ] 导入必要的GSAP插件（ScrollTrigger、TextPlugin等）
- [ ] 创建AnimationManager实例
- [ ] 设置性能监控器
- [ ] 确认设备检测器工作正常

#### **设计规划**
- [ ] 确定页面需要的动画效果
- [ ] 选择合适的动画预设
- [ ] 规划动画时序和层次
- [ ] 考虑移动端适配方案
- [ ] 评估性能影响

### 2. 开发中检查 ⚙️

#### **代码规范**
- [ ] 所有动画使用GSAP实现（禁用CSS动画）
- [ ] 动画实例添加到AnimationManager
- [ ] 使用AnimationPresets标准预设
- [ ] 遵循响应式配置规范
- [ ] 添加适当的性能优化

#### **动画质量**
- [ ] 动画流畅度达到60fps
- [ ] 缓动函数选择合适
- [ ] 时长和延迟设置合理
- [ ] 移动端动画简化处理
- [ ] 低端设备降级方案

### 3. 测试验证 🧪

#### **功能测试**
- [ ] 动画在不同设备上正常播放
- [ ] 滚动触发动画工作正常
- [ ] 悬停效果响应及时
- [ ] 页面切换动画流畅
- [ ] 动画资源正确清理

#### **性能测试**
- [ ] FPS保持在30以上
- [ ] 内存使用量合理
- [ ] 低端设备性能可接受
- [ ] 网络慢速时降级正常
- [ ] 动画不阻塞用户交互

### 4. 部署前检查 🚀

#### **生产准备**
- [ ] 移除调试代码和console.log
- [ ] 确认动画文件正确打包
- [ ] 验证CDN资源加载
- [ ] 测试生产环境性能
- [ ] 更新动画文档

#### **兼容性检查**
- [ ] 主流浏览器兼容性
- [ ] 移动端浏览器测试
- [ ] 不同屏幕尺寸适配
- [ ] 触摸设备交互正常
- [ ] 无障碍访问支持

## 🔄 开发流程规范

### 1. 新功能开发流程（含动画）
1. **需求分析** - 明确功能需求和动画需求
2. **动画设计** - 设计动画效果和交互方案
3. **API设计** - 设计后端接口和数据格式
4. **组件设计** - 确定需要的组件和复用策略
5. **动画实现** - 使用GSAP实现动画效果
6. **编码实现** - 按照规范进行开发
7. **动画测试** - 测试动画性能和兼容性
8. **功能测试** - 功能测试和数据验证
9. **文档更新** - 更新相关文档和动画说明

### 2. 组件开发流程（含动画）
1. **组件设计** - 确定组件功能和动画属性
2. **动画规划** - 规划组件内的动画效果
3. **数据接口** - 确定需要的API接口
4. **动画实现** - 实现组件动画逻辑
5. **组件实现** - 编写组件代码
6. **动画集成** - 集成动画到组件生命周期
7. **组件测试** - 测试组件功能和动画
8. **性能优化** - 优化动画性能
9. **组件文档** - 编写使用说明和动画说明

### 3. API开发流程（含安全规范）
1. **接口设计** - 确定接口路径和数据格式
   - 明确接口类型（查询/管理）
   - 确定安全级别（公共/认证）
   - 设计权限控制策略
2. **安全配置** - 配置接口访问权限
   - 查询接口添加到公共访问列表
   - 管理接口配置认证和权限
   - 更新Shiro配置文件
3. **后端实现** - 实现接口逻辑
   - 实现业务逻辑
   - 添加数据过滤
   - 添加权限注解
4. **安全测试** - 验证安全配置
   - 测试公共接口无需token访问
   - 测试管理接口需要认证
   - 验证权限控制效果
5. **前端调用** - 前端集成接口
6. **联调测试** - 前后端联调
7. **文档更新** - 更新API文档和安全说明

## 🛡️ 安全规范

### 1. 后端接口安全规范

#### **接口公共访问控制原则**
**核心原则：除非明确指定为管理接口开发，否则对查询接口开放公共访问**

#### **公共访问接口规范**
```yaml
# Shiro配置 - 公共访问接口列表
jeecg:
  shiro:
    excludeUrls:
      # 网站展示相关接口（只读查询）
      /aigc/aigcHomeCarousel/list,          # 轮播图列表
      /aigc/websiteFeatures/list,           # 功能特性列表
      /aigc/websiteStats/list,              # 统计数据
      /aigc/customerCases/list,             # 客户案例列表
      /aigc/tutorials/list,                 # 教程中心列表
      /aigc/websiteContent/list,            # 网站内容列表
      # 其他公开API
      /api/public/**                        # 专门的公开API路径
```

#### **接口安全分类**

##### ✅ **公共访问接口（无需认证）**
- **查询接口** - 用于网站展示的只读数据
- **列表接口** - 展示用的数据列表
- **详情接口** - 公开内容的详细信息
- **统计接口** - 公开的统计数据

**示例：**
```java
// ✅ 公共访问 - 轮播图查询
@GetMapping("/list")
public Result<List<CarouselVO>> getCarouselList() {
    // 只返回启用状态的数据
    return Result.ok(carouselService.getPublicList());
}

// ✅ 公共访问 - 功能特性列表
@GetMapping("/features/list")
public Result<List<FeatureVO>> getFeaturesList() {
    return Result.ok(featuresService.getPublicList());
}
```

##### 🔒 **需要认证的接口**
- **管理接口** - 增删改操作
- **用户接口** - 个人信息相关
- **系统接口** - 系统配置相关
- **敏感接口** - 涉及敏感数据

**示例：**
```java
// 🔒 需要认证 - 轮播图管理
@PostMapping("/add")
@RequiresPermissions("aigc:carousel:add")
public Result<String> addCarousel(@RequestBody CarouselDTO dto) {
    return Result.ok(carouselService.add(dto));
}

// 🔒 需要认证 - 轮播图编辑
@PutMapping("/edit")
@RequiresPermissions("aigc:carousel:edit")
public Result<String> editCarousel(@RequestBody CarouselDTO dto) {
    return Result.ok(carouselService.edit(dto));
}

// 🔒 需要认证 - 轮播图删除
@DeleteMapping("/delete")
@RequiresPermissions("aigc:carousel:delete")
public Result<String> deleteCarousel(@RequestParam String id) {
    return Result.ok(carouselService.delete(id));
}
```

#### **接口开发规范**

##### **1. 查询接口默认公开**
```java
// 正确示例 - 查询接口
@RestController
@RequestMapping("/aigc/websiteContent")
public class WebsiteContentController {

    // ✅ 查询接口 - 默认公开访问
    @GetMapping("/list")
    public Result<List<ContentVO>> getContentList(@RequestParam Map<String, Object> params) {
        // 只返回已发布的内容
        params.put("status", "published");
        return Result.ok(contentService.getPublicList(params));
    }

    // ✅ 详情接口 - 公开访问
    @GetMapping("/detail/{id}")
    public Result<ContentVO> getContentDetail(@PathVariable String id) {
        return Result.ok(contentService.getPublicDetail(id));
    }
}
```

##### **2. 管理接口需要认证**
```java
// 管理接口 - 需要认证和权限
@RestController
@RequestMapping("/aigc/websiteContent")
public class WebsiteContentController {

    // 🔒 管理接口 - 需要认证
    @PostMapping("/add")
    @RequiresPermissions("website:content:add")
    public Result<String> addContent(@RequestBody ContentDTO dto) {
        return Result.ok(contentService.add(dto));
    }

    // 🔒 管理接口 - 需要认证
    @PutMapping("/edit")
    @RequiresPermissions("website:content:edit")
    public Result<String> editContent(@RequestBody ContentDTO dto) {
        return Result.ok(contentService.edit(dto));
    }

    // 🔒 管理接口 - 需要认证
    @DeleteMapping("/delete")
    @RequiresPermissions("website:content:delete")
    public Result<String> deleteContent(@RequestParam String id) {
        return Result.ok(contentService.delete(id));
    }
}
```

#### **安全配置管理**

##### **1. 环境配置文件**
```yaml
# application-dev.yml (开发环境)
jeecg:
  shiro:
    excludeUrls: /test/**,/aigc/*/list,/aigc/*/detail/**

# application-prod.yml (生产环境)
jeecg:
  shiro:
    excludeUrls: /aigc/*/list,/aigc/*/detail/**,/api/public/**

# application-test.yml (测试环境)
jeecg:
  shiro:
    excludeUrls: /test/**,/aigc/*/list,/aigc/*/detail/**
```

##### **2. 动态权限控制**
```java
// 在Service层进行数据过滤
@Service
public class CarouselServiceImpl implements CarouselService {

    // 公共查询 - 只返回启用状态的数据
    public List<CarouselVO> getPublicList() {
        QueryWrapper<Carousel> wrapper = new QueryWrapper<>();
        wrapper.eq("status", "1"); // 只查询启用状态
        wrapper.eq("is_published", "1"); // 只查询已发布
        wrapper.orderByAsc("sort_order");
        return baseMapper.selectList(wrapper);
    }

    // 管理查询 - 返回所有数据（需要认证）
    public List<CarouselVO> getAdminList(Map<String, Object> params) {
        // 管理员可以查看所有状态的数据
        return baseMapper.selectByMap(params);
    }
}
```

#### **接口安全检查清单**

##### **开发前检查**
- [ ] 确认接口类型（查询 vs 管理）
- [ ] 确认是否需要公共访问
- [ ] 确认权限控制策略
- [ ] 确认数据过滤规则

##### **开发后检查**
- [ ] 验证公共接口无需token即可访问
- [ ] 验证管理接口需要认证才能访问
- [ ] 验证数据过滤是否正确
- [ ] 验证权限控制是否生效

##### **部署前检查**
- [ ] 检查生产环境配置
- [ ] 验证安全策略
- [ ] 进行安全测试
- [ ] 更新API文档

### 2. 数据安全
- 所有用户输入必须验证
- 敏感数据必须加密传输
- API调用必须有权限验证

### 3. 代码安全
- 不在前端代码中暴露敏感信息
- 使用环境变量管理配置
- 定期更新依赖包版本

## 🎯 GSAP动画最佳实践

### 1. 性能优化最佳实践

#### **动画属性选择**
```javascript
// ✅ 推荐：使用transform和opacity
gsap.to(".element", {
  x: 100,           // 使用transform: translateX
  y: 50,            // 使用transform: translateY
  scale: 1.2,       // 使用transform: scale
  rotation: 45,     // 使用transform: rotate
  opacity: 0.5,     // 使用opacity
  duration: 1
})

// ❌ 避免：直接修改布局属性
gsap.to(".element", {
  left: "100px",    // 避免：触发重排
  top: "50px",      // 避免：触发重排
  width: "200px",   // 避免：触发重排
  height: "100px",  // 避免：触发重排
  margin: "10px",   // 避免：触发重排
  duration: 1
})
```

#### **硬件加速优化**
```css
/* 为动画元素启用硬件加速 */
.animated-element {
  will-change: transform, opacity;
  transform: translateZ(0); /* 强制启用GPU加速 */
  backface-visibility: hidden; /* 避免闪烁 */
}

/* 动画完成后清理will-change */
.animation-complete {
  will-change: auto;
}
```

#### **内存管理**
```javascript
// 正确的动画清理方式
export default {
  data() {
    return {
      animManager: new AnimationManager(),
      scrollTriggers: []
    }
  },

  beforeDestroy() {
    // 1. 清理GSAP动画
    this.animManager.killAll()

    // 2. 清理ScrollTrigger
    this.scrollTriggers.forEach(trigger => trigger.kill())

    // 3. 清理事件监听器
    this.removeEventListeners()

    // 4. 清理will-change属性
    document.querySelectorAll('[style*="will-change"]').forEach(el => {
      el.style.willChange = 'auto'
    })

    console.log('动画资源清理完成')
  }
}
```

### 2. 动画设计原则

#### **时长和缓动**
```javascript
// 标准动画时长
const ANIMATION_DURATIONS = {
  micro: 0.15,      // 微交互（按钮悬停）
  short: 0.3,       // 短动画（状态切换）
  medium: 0.6,      // 中等动画（页面元素入场）
  long: 1.0,        // 长动画（页面切换）
  extra: 1.5        // 特殊动画（品牌展示）
}

// 标准缓动函数
const EASING_PRESETS = {
  smooth: "power2.out",      // 平滑出场
  bounce: "back.out(1.7)",   // 弹性效果
  sharp: "power3.inOut",     // 锐利变化
  gentle: "power1.out",      // 温和效果
  elastic: "elastic.out(1, 0.3)" // 弹性效果
}

// 使用示例
gsap.to(".element", {
  duration: ANIMATION_DURATIONS.medium,
  ease: EASING_PRESETS.smooth,
  x: 100
})
```

#### **动画层次和时序**
```javascript
// 创建有层次的动画序列
const createStaggeredAnimation = () => {
  const tl = gsap.timeline()

  // 主要元素先出现
  tl.from(".main-title", {
    duration: 0.8,
    y: 50,
    opacity: 0,
    ease: "power2.out"
  })

  // 次要元素跟进
  .from(".subtitle", {
    duration: 0.6,
    y: 30,
    opacity: 0,
    ease: "power2.out"
  }, "-=0.4") // 重叠0.4秒

  // 列表项依次出现
  .from(".list-item", {
    duration: 0.5,
    y: 20,
    opacity: 0,
    stagger: 0.1, // 每个间隔0.1秒
    ease: "power2.out"
  }, "-=0.3")

  return tl
}
```

### 3. 常见问题解决方案

#### **问题1：动画卡顿**
```javascript
// 原因：动画属性选择不当
// ❌ 问题代码
gsap.to(".element", {
  left: "100px",
  top: "50px",
  width: "200px"
})

// ✅ 解决方案
gsap.to(".element", {
  x: 100,
  y: 50,
  scaleX: 2
})

// 额外优化
gsap.set(".element", {
  force3D: true,  // 强制3D变换
  transformOrigin: "center center"
})
```

#### **问题2：内存泄漏**
```javascript
// 原因：动画实例未正确清理
// ✅ 解决方案
class ComponentWithAnimations {
  constructor() {
    this.animations = []
    this.scrollTriggers = []
  }

  addAnimation(animation) {
    this.animations.push(animation)
    return animation
  }

  destroy() {
    // 清理所有动画
    this.animations.forEach(anim => anim.kill())
    this.scrollTriggers.forEach(trigger => trigger.kill())

    // 清空数组
    this.animations = []
    this.scrollTriggers = []
  }
}
```

#### **问题3：移动端性能差**
```javascript
// 解决方案：设备检测和降级
const initMobileOptimizedAnimation = () => {
  const isMobile = window.innerWidth <= 768
  const isLowEnd = navigator.hardwareConcurrency <= 2

  if (isMobile || isLowEnd) {
    // 简化动画
    return gsap.to(".element", {
      duration: 0.3,
      opacity: 1,
      ease: "power1.out"
    })
  } else {
    // 完整动画
    return gsap.to(".element", {
      duration: 0.8,
      x: 100,
      y: 50,
      scale: 1.2,
      rotation: 360,
      ease: "power2.out"
    })
  }
}
```

#### **问题4：ScrollTrigger不工作**
```javascript
// 原因：元素未正确渲染或ScrollTrigger未刷新
// ✅ 解决方案
const initScrollAnimation = () => {
  // 等待DOM完全渲染
  gsap.registerPlugin(ScrollTrigger)

  // 延迟初始化
  setTimeout(() => {
    ScrollTrigger.create({
      trigger: ".trigger-element",
      start: "top 80%",
      end: "bottom 20%",
      animation: gsap.from(".target", {
        y: 100,
        opacity: 0,
        duration: 1
      }),
      toggleActions: "play none none reverse",
      onRefresh: () => console.log("ScrollTrigger已刷新")
    })

    // 手动刷新
    ScrollTrigger.refresh()
  }, 100)
}
```

### 4. 调试和监控

#### **动画调试工具**
```javascript
// 开发环境动画调试
if (process.env.NODE_ENV === 'development') {
  // 全局动画速度控制
  gsap.globalTimeline.timeScale(0.5) // 减慢到50%

  // 动画事件监听
  gsap.globalTimeline.eventCallback("onUpdate", () => {
    console.log("动画进度:", gsap.globalTimeline.progress())
  })

  // ScrollTrigger调试
  ScrollTrigger.addEventListener("refresh", () => {
    console.log("ScrollTrigger已刷新")
  })
}
```

#### **性能监控**
```javascript
// 实时FPS监控
class FPSMonitor {
  constructor() {
    this.fps = 0
    this.frames = 0
    this.lastTime = performance.now()
  }

  update() {
    this.frames++
    const currentTime = performance.now()

    if (currentTime - this.lastTime >= 1000) {
      this.fps = Math.round((this.frames * 1000) / (currentTime - this.lastTime))

      // 性能警告
      if (this.fps < 30) {
        console.warn(`FPS过低: ${this.fps}`)
        this.handleLowFPS()
      }

      this.frames = 0
      this.lastTime = currentTime
    }

    requestAnimationFrame(() => this.update())
  }

  handleLowFPS() {
    // 降级处理
    gsap.globalTimeline.timeScale(1.5) // 加快动画
    document.body.classList.add('low-performance')
  }
}

const fpsMonitor = new FPSMonitor()
fpsMonitor.update()
```

## 📊 性能规范

### 1. 前端性能（含动画优化）
- 使用懒加载减少初始加载时间
- 图片压缩和格式优化
- 合理使用缓存机制
- **动画性能优化**：使用transform和opacity属性
- **硬件加速**：合理使用will-change和transform3d
- **内存管理**：及时清理动画实例和事件监听器

### 2. 接口性能
- 避免不必要的API调用
- 使用分页减少数据量
- 合理使用防抖和节流
- **动画数据**：预加载动画所需的数据
- **异步加载**：大型动画资源异步加载

## 🧪 测试规范

### 1. 功能测试
- 每个功能都要有对应的测试用例
- 测试正常流程和异常流程
- 测试不同设备和浏览器兼容性

### 2. 数据测试
- 验证API返回数据的正确性
- 测试数据的完整性和一致性
- 测试边界条件和异常情况

## 📝 文档规范

### 1. 代码注释
```javascript
/**
 * 获取插件列表
 * @param {Object} params - 查询参数
 * @param {Number} params.page - 页码
 * @param {Number} params.size - 每页数量
 * @returns {Promise} 插件列表数据
 */
async getPluginList(params) {
  return await this.$http.get('/api/plugins', { params })
}
```

### 2. 组件文档
每个组件都要有使用说明，包括：
- 组件功能描述
- 属性说明
- 事件说明
- 使用示例

### 3. API文档
每个接口都要有详细说明，包括：
- 接口功能描述
- 请求参数说明
- 响应数据格式
- 错误码说明

## 🎨 智界AIGC设计风格应用指导

### 📋 设计风格检查清单

#### **页面开发前检查**
- [ ] 确认页面类型（官网页面 vs 管理后台）
- [ ] 选择正确的设计风格（明亮现代 vs 深色科技）
- [ ] 确认色彩方案和组件规范
- [ ] 规划动画效果和交互方式

#### **开发过程中检查**
- [ ] 背景使用浅色渐变 `linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)`
- [ ] 文字颜色使用深色系 `#374151`, `#6b7280`, `#9ca3af`
- [ ] 卡片使用白色半透明背景 `rgba(255, 255, 255, 0.95)`
- [ ] 按钮使用品牌渐变色 `linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%)`
- [ ] 边框使用蓝色系 `rgba(59, 130, 246, 0.1-0.3)`
- [ ] 阴影使用浅色系 `rgba(0, 0, 0, 0.1)`

#### **组件应用检查**
- [ ] 导航栏使用固定定位和毛玻璃效果
- [ ] 表单输入框使用圆角和聚焦效果
- [ ] 按钮有悬浮动画和光波扫过效果
- [ ] 卡片有悬浮阴影和边框变化
- [ ] 所有动画使用GSAP实现

#### **响应式检查**
- [ ] 移动端导航栏适配（隐藏文字，显示图标）
- [ ] 卡片在小屏幕上的布局调整
- [ ] 表单在移动端的堆叠布局
- [ ] 动画在低端设备上的降级处理

### 🎯 页面类型设计指导

#### **官网页面（明亮风格）**
```css
/* 适用页面：首页、商城、登录、注册、404等 */
.website-page {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  color: #374151;
  min-height: 100vh;
}

.website-card {
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid rgba(59, 130, 246, 0.1);
  border-radius: 24px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.website-button {
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  color: white;
  border-radius: 12px;
}
```

#### **管理后台（深色科技风格）**
```css
/* 适用页面：管理员后台、数据统计、系统设置等 */
.admin-page {
  background: #0a0a0a;
  color: #ffffff;
  min-height: 100vh;
}

.admin-card {
  background: rgba(15, 15, 15, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.admin-button {
  background: rgba(59, 130, 246, 0.2);
  color: #3b82f6;
  border: 1px solid rgba(59, 130, 246, 0.3);
}
```

### 🚀 快速开始模板

#### **新页面开发模板**
```vue
<template>
  <div class="website-page" ref="pageContainer">
    <!-- 导航栏 -->
    <nav class="navbar">
      <div class="nav-content">
        <div class="nav-brand">
          <router-link to="/" class="brand-link">
            <span class="brand-text">智界AIGC</span>
          </router-link>
        </div>
        <div class="nav-actions">
          <!-- 导航按钮 -->
        </div>
      </div>
    </nav>

    <!-- 背景装饰 -->
    <div class="animated-background">
      <div class="grid-lines"></div>
      <div class="floating-elements">
        <div class="element element-1"></div>
        <div class="element element-2"></div>
        <div class="element element-3"></div>
        <div class="element element-4"></div>
      </div>
    </div>

    <!-- 主要内容 -->
    <main class="main-content">
      <div class="content-container">
        <!-- 页面内容 -->
      </div>
    </main>
  </div>
</template>

<script>
import { gsap } from 'gsap'
import { AnimationManager } from '@/animations/gsap/managers/AnimationManager.js'

export default {
  name: 'NewPage',
  data() {
    return {
      animManager: new AnimationManager()
    }
  },
  async mounted() {
    try {
      // 加载页面数据（如果有）
      await this.loadPageData()

      // 初始化动画
      this.initAnimations()

      // 页面加载完成后，添加loaded类名，恢复正常亮度
      this.$nextTick(() => {
        setTimeout(() => {
          const container = this.$refs.pageContainer || this.$el
          if (container) {
            container.classList.add('loaded')
          }
        }, 200) // 稍微延迟，让初始动画开始
      })

    } catch (error) {
      console.error('页面加载失败:', error)

      // 即使出错也要恢复亮度
      this.$nextTick(() => {
        const container = this.$refs.pageContainer || this.$el
        if (container) {
          container.classList.add('loaded')
        }
      })
    }
  },
  beforeDestroy() {
    this.animManager.killAll()
  },
  methods: {
    async loadPageData() {
      // 加载页面所需数据
      // 如果页面不需要加载数据，可以省略此方法
    },

    initAnimations() {
      // 页面入场动画
      const tl = gsap.timeline()

      tl.from('.main-content', {
        duration: 0.8,
        y: 50,
        opacity: 0,
        ease: 'power2.out'
      })

      this.animManager.addTimeline(tl)
    }
  }
}
</script>

<style scoped>
/* 使用设计规范中的样式 */
.website-page {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  color: #374151;
  min-height: 100vh;
  position: relative;
  overflow: hidden;
  /* 柔和加载：初始状态稍微暗一点，避免刺眼 */
  opacity: 0.85;
  transition: opacity 0.5s ease-out;
  will-change: opacity;
}

/* 页面加载完成后恢复正常亮度 */
.website-page.loaded {
  opacity: 1;
  will-change: auto;
}

/* 背景装饰渐进显示 */
.animated-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  /* 初始状态稍微暗一点 */
  opacity: 0.7;
  transition: opacity 0.6s ease-out;
}

.website-page.loaded .animated-background {
  opacity: 1;
}

/* 其他样式按照设计规范编写 */
</style>
```

### 📚 设计规范总结

#### **核心原则**
1. **明亮现代**：官网使用浅色系，营造轻松愉悦的体验
2. **一致性**：所有页面保持统一的视觉语言
3. **响应式**：适配各种设备和屏幕尺寸
4. **性能优先**：所有动画使用GSAP，确保流畅体验
5. **用户友好**：界面简洁，交互直观

#### **🌟 页面初始加载优化规范**

##### **高科技AI风格全局加载器**
```css
/* 主背景 - 深色科技感 */
#loader-wrapper {
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
  /* 科技感网格背景 */
}

#loader-wrapper::before {
  background-image:
    linear-gradient(rgba(59, 130, 246, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(59, 130, 246, 0.1) 1px, transparent 1px);
  background-size: 50px 50px;
  animation: gridMove 20s linear infinite;
}

/* AI神经网络风格加载器 */
#loader::before {
  border-top: 3px solid #3b82f6;
  border-right: 3px solid #8b5cf6;
  animation: aiSpin 2s linear infinite;
}

#loader::after {
  border-bottom: 2px solid #10b981;
  border-left: 2px solid #06b6d4;
  animation: aiSpinReverse 1.5s linear infinite;
}

/* 中心脉冲核心 */
.loader-core {
  background: linear-gradient(45deg, #3b82f6, #8b5cf6);
  box-shadow:
    0 0 20px rgba(59, 130, 246, 0.5),
    0 0 40px rgba(139, 92, 246, 0.3);
  animation: corePulse 2s ease-in-out infinite;
}
```

##### **柔和页面过渡标准**
```css
/* 主容器柔和初始状态 - 避免刺眼 */
.page-container {
  /* 初始状态稍微暗一点，避免刺眼 */
  opacity: 0.85;
  transition: opacity 0.5s ease-out;
}

/* 页面加载完成后恢复正常亮度 */
.page-container.loaded {
  opacity: 1;
}

/* 背景装饰渐进显示 */
.animated-background {
  /* 初始状态稍微暗一点 */
  opacity: 0.7;
  transition: opacity 0.6s ease-out;
}

.page-container.loaded .animated-background {
  opacity: 1;
}
```

##### **JavaScript加载状态控制**
```javascript
// 页面加载完成后的标准处理
async mounted() {
  try {
    // 加载页面数据
    await this.loadPageData()

    // 初始化动画
    this.initAnimations()

    // 页面加载完成后，添加loaded类名，恢复正常亮度
    this.$nextTick(() => {
      setTimeout(() => {
        const container = this.$refs.pageContainer || this.$el
        if (container) {
          container.classList.add('loaded')
        }
      }, 200) // 稍微延迟，让初始动画开始
    })

  } catch (error) {
    // 即使出错也要恢复亮度
    this.$nextTick(() => {
      const container = this.$refs.pageContainer || this.$el
      if (container) {
        container.classList.add('loaded')
      }
    })
  }
}
```

##### **模板结构标准**
```vue
<template>
  <div class="page-container" ref="pageContainer">
    <!-- 背景装饰 -->
    <div class="animated-background">
      <!-- 背景元素 -->
    </div>

    <!-- 页面内容 -->
    <main class="main-content">
      <!-- 具体内容 -->
    </main>
  </div>
</template>
```

##### **设计理念**
- 🤖 **AI科技感**：体现人工智能和高科技项目的专业性
- 🌌 **神经网络风格**：多层旋转环模拟AI神经网络运作
- ⚡ **能量脉冲**：中心脉冲点象征AI算法的核心运算
- 🔮 **未来感**：深色背景配合蓝紫渐变，营造未来科技氛围
- 💎 **精致细节**：光效、阴影、渐变等细节提升品质感

##### **技术特点**
- 🎯 **纯CSS实现**：无需额外JavaScript，性能优异
- 🔄 **多层动画**：外圈、内圈、核心三层独立动画
- 🌈 **渐变配色**：使用品牌色系的渐变组合
- 📱 **响应式适配**：在各种设备上都有良好表现
- ⚡ **硬件加速**：使用transform和opacity优化性能

##### **应用场景**
- ✅ **全局加载器**：项目启动时的主要加载界面
- ✅ **官网首页**：必须使用柔和页面过渡
- ✅ **登录页面**：必须使用柔和页面过渡
- ✅ **404页面**：必须使用柔和页面过渡
- ✅ **所有官网页面**：建议使用柔和页面过渡
- ❌ **管理后台**：可选使用（深色主题下效果不明显）

##### **性能考虑**
```css
/* 使用CSS transition而非JavaScript动画，性能更好 */
.page-container {
  transition: opacity 0.5s ease-out;
  /* 避免使用transform，减少重排重绘 */
}

/* 使用will-change提示浏览器优化 */
.page-container {
  will-change: opacity;
}

/* 动画完成后移除will-change */
.page-container.loaded {
  will-change: auto;
}
```

#### **禁止事项**
- ❌ 在官网页面使用深色背景
- ❌ 使用CSS动画替代GSAP动画
- ❌ 硬编码任何数据或配置
- ❌ 忽略响应式设计
- ❌ 不清理动画资源
- ❌ 页面初始加载过于刺眼

#### **必须遵循**
- ✅ 使用设计规范中的色彩系统
- ✅ 所有动画使用GSAP实现
- ✅ 数据从后端API获取
- ✅ 组件化可复用部分
- ✅ 性能优化和内存管理
- ✅ 页面初始加载使用柔和过渡

---

## 📋 重要问题记录与解决方案

### 🔧 已解决问题

#### **问题1：admin角色刷新后台页面跳转404问题**
**发现时间**：2025-06-18
**问题描述**：admin用户在后台管理系统任意页面刷新时，会自动跳转到 `/not-found` 页面，无法停留在原页面。

**根本原因**：
- 前端双系统架构中的路由时序冲突
- 页面刷新时，Vue Router先加载静态路由，动态路由后加载
- 静态路由中的通配符 `{ path: '*', redirect: '/not-found' }` 在动态路由加载前就生效
- 导致后台页面路径被通配符捕获，强制重定向到404页面

**🚨 重要约束**：
- **动态路由绝对不能修改** - 这是项目红线！
- 修改动态路由会影响：数据库、权限系统、菜单管理、用户权限等
- 必须在前端层面适配现有的动态路由结构

**解决方案**：智能通配符组件
```javascript
// ❌ 原有配置
{
  path: '*',
  redirect: '/not-found'
}

// ✅ 智能配置
{
  path: '*',
  component: () => import('@/components/SmartNotFound.vue')
}
```

**SmartNotFound组件逻辑**：
1. 检查用户TOKEN和角色
2. 判断访问路径是否为后台页面
3. 如果是admin访问后台页面：加载动态路由 → 跳转原页面
4. 如果不是：显示404页面

**效果验证**：
- ✅ admin用户刷新后台页面：短暂加载后回到原页面
- ✅ 客户访问错误路径：直接显示404，零延迟
- ✅ 性能影响：仅对admin用户，客户完全不受影响

**设计原则体现**：
- **客户体验第一**：客户访问不受任何影响
- **运维可接受慢一点**：admin用户可以接受短暂的加载时间
- **官网体验优先**：所有优化都以官网体验为先

**相关文件**：
- `src/components/SmartNotFound.vue` - 智能404组件
- `src/config/router.config.js` - 路由配置修改
- `src/permission.js` - 路由守卫（未修改）

**经验总结**：
- **🚨 动态路由是红线** - 绝对不能修改，必须适配现有结构
- 前端双系统架构需要特别注意路由时序问题
- 通配符路由的处理要考虑不同用户角色的需求
- 性能优化要区分客户端和管理端的优先级
- 智能组件可以很好地解决条件性路由处理问题
- **最小化改动原则** - 优先选择影响最小的前端解决方案

#### **问题2：前后端数据字段映射不一致问题**
**发现时间**：2025-06-18
**问题描述**：个人中心所有子组件中使用了错误的响应字段，导致数据无法正确显示，概览页面统计数据显示为0。

**根本原因**：
- 前端代码中使用了 `response.data` 字段
- 后端API实际返回的是 `response.result` 字段
- 字段映射不一致导致数据解析失败
- 缺少统一的前后端数据字段规范

**影响范围**：
- `Profile.vue` - 头像上传接口
- `Credits.vue` - 交易统计、交易记录、消费图表接口
- `Orders.vue` - 订单统计、订单列表、订单详情接口
- `Usage.vue` - API统计、图表数据、分布数据、插件使用、最近活动接口
- `Membership.vue` - 会员信息、会员历史接口
- `Referral.vue` - 推荐统计、推荐记录、提现记录、推荐链接接口

**解决方案**：统一前后端数据字段映射规范

#### **🔧 统一前后端数据字段映射规范**

##### **1. 标准响应格式**
```javascript
// ✅ 标准后端响应格式
{
  "success": true,
  "message": "操作成功",
  "code": 200,
  "result": {
    // 实际数据内容
  },
  "timestamp": 1640995200000
}
```

##### **2. 前端数据获取规范**
```javascript
// ❌ 错误写法
const response = await getApiData()
if (response.success) {
  this.data = response.data  // 错误：使用了不存在的data字段
}

// ✅ 正确写法
const response = await getApiData()
if (response.success) {
  this.data = response.result  // 正确：使用result字段
}

// ✅ 兼容性写法（推荐）
const response = await getApiData()
if (response.success) {
  this.data = response.result || response.data  // 提供向后兼容
}
```

##### **3. API接口开发规范**
```javascript
// 后端Controller返回格式
@RestController
public class UserCenterDataController {

    @GetMapping("/overview")
    public Result<?> getOverviewData() {
        try {
            Map<String, Object> data = userService.getOverviewData();
            return Result.OK(data);  // 数据会自动放入result字段
        } catch (Exception e) {
            return Result.error("获取数据失败");
        }
    }
}
```

##### **4. 前端API调用标准模板**
```javascript
// 标准API调用模板
export const apiTemplate = {
  async getData(params) {
    try {
      const response = await request({
        url: '/api/data',
        method: 'GET',
        params
      })

      if (response.success) {
        // 统一使用result字段，提供data字段兼容
        return response.result || response.data || {}
      } else {
        throw new Error(response.message || '请求失败')
      }
    } catch (error) {
      console.error('API调用失败:', error)
      throw error
    }
  }
}
```

##### **5. Vue组件数据处理规范**
```javascript
// Vue组件中的标准数据处理
export default {
  data() {
    return {
      loading: false,
      data: {}
    }
  },

  async mounted() {
    await this.loadData()
  },

  methods: {
    async loadData() {
      try {
        this.loading = true
        const response = await getApiData()

        if (response.success) {
          // 使用Object.assign确保响应式更新
          Object.assign(this.data, response.result || response.data || {})
          this.$forceUpdate() // 备用方案，确保界面更新
        } else {
          this.$message.error(response.message || '加载数据失败')
        }
      } catch (error) {
        console.error('数据加载失败:', error)
        this.$message.error('加载数据失败，请刷新重试')
      } finally {
        this.loading = false
      }
    }
  }
}
```

##### **6. 数据字段映射检查清单**
- [ ] **后端接口**：确保所有接口都使用 `Result.OK(data)` 返回数据
- [ ] **前端调用**：确保所有API调用都使用 `response.result` 获取数据
- [ ] **兼容处理**：添加 `response.result || response.data` 兼容性处理
- [ ] **响应式更新**：使用 `Object.assign` 确保Vue响应式更新
- [ ] **错误处理**：添加完善的错误处理和用户提示
- [ ] **测试验证**：验证数据能够正确显示和更新

**修复状态**：✅ **已完成**

**相关文件**：
- 所有个人中心Vue组件（Profile.vue、Credits.vue等）
- 后端UserCenterDataController.java
- 前端API接口文件usercenter.js

**经验总结**：
- **统一规范是关键** - 前后端必须约定统一的数据字段格式
- **兼容性处理很重要** - 使用 `||` 操作符提供向后兼容
- **响应式更新要注意** - Vue对象更新需要使用正确的方法
- **测试验证不可少** - 数据字段修改后必须充分测试
- **文档同步很关键** - API文档必须与实际实现保持一致

---

**本文档为智界AIGC项目的核心开发规范，所有开发人员必须严格遵循。如有疑问或建议，请及时沟通更新。**
