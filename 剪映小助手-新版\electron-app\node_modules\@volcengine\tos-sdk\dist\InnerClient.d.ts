import TOSBase from './methods/base';
import { listBuckets, createBucket, deleteBucket, headBucket, putBucketStorageClass } from './methods/bucket/base';
import { getBucketAcl, putBucketAcl } from './methods/bucket/acl';
import { getObject, getObjectV2, getObjectToFile } from './methods/object/getObject';
import putObject, { putObjectFromFile } from './methods/object/putObject';
import { fetchObject, putFetchTask } from './methods/object/fetch';
import { listObjectVersions, listObjects } from './methods/object/listObjects';
import getPreSignedUrl from './methods/object/getPreSignedUrl';
import headObject from './methods/object/headObject';
import deleteObject from './methods/object/deleteObject';
import renameObject from './methods/object/renameObject';
import deleteMultiObjects from './methods/object/deleteMultiObjects';
import copyObject from './methods/object/copyObject';
import { getObjectAcl, putObjectAcl } from './methods/object/acl';
import { abortMultipartUpload, completeMultipartUpload, createMultipartUpload, listParts, uploadPart, listMultipartUploads, uploadPartFromFile } from './methods/object/multipart';
import appendObject from './methods/object/appendObject';
import setObjectMeta from './methods/object/setObjectMeta';
import { uploadPartCopy } from './methods/object/multipart/uploadPartCopy';
import uploadFile from './methods/object/multipart/uploadFile';
import { calculatePostSignature } from './methods/object/calculatePostSignature';
import { resumableCopyObject } from './methods/object/multipart/resumableCopyObject';
import { deleteBucketPolicy, getBucketPolicy, putBucketPolicy } from './methods/bucket/policy';
import { getBucketVersioning, putBucketVersioning } from './methods/bucket/versioning';
import { preSignedPolicyURL } from './methods/object/preSignedPolicyURL';
import downloadFile from './methods/object/downloadFile';
import { getBucketLocation } from './methods/bucket/getBucketLocation';
import { deleteBucketCORS, getBucketCORS, putBucketCORS } from './methods/bucket/cors';
import { listObjectsType2 } from './methods/object/listObjectsType2';
import { deleteBucketLifecycle, getBucketLifecycle, putBucketLifecycle } from './methods/bucket/lifecycle';
import { putBucketEncryption, getBucketEncryption, deleteBucketEncryption } from './methods/bucket/encryption';
import { deleteBucketMirrorBack, getBucketMirrorBack, putBucketMirrorBack } from './methods/bucket/mirrorback';
import { deleteObjectTagging, getObjectTagging, putObjectTagging } from './methods/object/tagging';
import { deleteBucketReplication, getBucketReplication, putBucketReplication } from './methods/bucket/replication';
import { deleteBucketWebsite, getBucketWebsite, putBucketWebsite } from './methods/bucket/website';
import { getBucketNotification, putBucketNotification } from './methods/bucket/notification';
import { deleteBucketCustomDomain, getBucketCustomDomain, putBucketCustomDomain } from './methods/bucket/customDomain';
import { deleteBucketRealTimeLog, getBucketRealTimeLog, putBucketRealTimeLog } from './methods/bucket/realTimeLog';
import { deleteBucketInventory, getBucketInventory, listBucketInventory, putBucketInventory } from './methods/bucket/inventory';
import { createJob, deleteJob, describeJob, updateJobStatus, updateJobPriority, listJobs } from './methods/batch';
import { deleteBucketTagging, getBucketTagging, putBucketTagging } from './methods/bucket/tag';
import { getBucketPayByTraffic, putBucketPayByTraffic } from './methods/bucket/payByTraffic';
import { getBucketImageStyle, getBucketImageStyleList, getBucketImageStyleListByName, getImageStyleBriefInfo, deleteBucketImageStyle, putBucketImageStyle, putBucketImageStyleSeparator, putBucketImageProtect, getBucketImageProtect, getBucketImageStyleSeparator } from './methods/bucket/img';
import { getBucketIntelligenttiering } from './methods/bucket/intelligenttiering';
import { putBucketRename, getBucketRename, deleteBucketRename } from './methods/bucket/rename';
import restoreObject from './methods/object/restoreObject';
import { deleteStorageLens, getStorageLens, listStorageLens, putStorageLens } from './methods/storageLens';
import { putBucketNotificationType2, getBucketNotificationType2 } from './methods/bucket/notificationType2';
import putSymlink from './methods/object/putSymlink';
import getSymlink from './methods/object/getSymlink';
import { getBucketTransferAcceleration, putBucketTransferAcceleration } from './methods/bucket/acceleration';
import { getBucketAccessMonitor, putBucketAccessMonitor } from './methods/bucket/accessMonitor';
import { getQosPolicy, putQosPolicy, deleteQosPolicy } from './methods/qosPolicy';
import { createMultiRegionAccessPoint, getMultiRegionAccessPoint, listMultiRegionAccessPoints, getMultiRegionAccessPointRoutes, deleteMultiRegionAccessPoint, submitMultiRegionAccessPointRoutes } from './methods/mrap';
import { putBucketPrivateM3U8, getBucketPrivateM3U8 } from './methods/bucket/media';
import { getBucketTrash, putBucketTrash } from './methods/bucket/trash';
export declare class InnerClient extends TOSBase {
    createBucket: typeof createBucket;
    headBucket: typeof headBucket;
    deleteBucket: typeof deleteBucket;
    listBuckets: typeof listBuckets;
    getBucketLocation: typeof getBucketLocation;
    putBucketStorageClass: typeof putBucketStorageClass;
    getBucketAcl: typeof getBucketAcl;
    putBucketAcl: typeof putBucketAcl;
    getBucketPolicy: typeof getBucketPolicy;
    putBucketPolicy: typeof putBucketPolicy;
    deleteBucketPolicy: typeof deleteBucketPolicy;
    getBucketVersioning: typeof getBucketVersioning;
    putBucketVersioning: typeof putBucketVersioning;
    getBucketCORS: typeof getBucketCORS;
    putBucketCORS: typeof putBucketCORS;
    deleteBucketCORS: typeof deleteBucketCORS;
    putBucketLifecycle: typeof putBucketLifecycle;
    getBucketLifecycle: typeof getBucketLifecycle;
    deleteBucketLifecycle: typeof deleteBucketLifecycle;
    putBucketEncryption: typeof putBucketEncryption;
    getBucketEncryption: typeof getBucketEncryption;
    deleteBucketEncryption: typeof deleteBucketEncryption;
    putBucketMirrorBack: typeof putBucketMirrorBack;
    getBucketMirrorBack: typeof getBucketMirrorBack;
    deleteBucketMirrorBack: typeof deleteBucketMirrorBack;
    putBucketReplication: typeof putBucketReplication;
    getBucketReplication: typeof getBucketReplication;
    deleteBucketReplication: typeof deleteBucketReplication;
    putBucketWebsite: typeof putBucketWebsite;
    getBucketWebsite: typeof getBucketWebsite;
    deleteBucketWebsite: typeof deleteBucketWebsite;
    putBucketNotification: typeof putBucketNotification;
    getBucketNotification: typeof getBucketNotification;
    putBucketCustomDomain: typeof putBucketCustomDomain;
    getBucketCustomDomain: typeof getBucketCustomDomain;
    deleteBucketCustomDomain: typeof deleteBucketCustomDomain;
    putBucketRealTimeLog: typeof putBucketRealTimeLog;
    getBucketRealTimeLog: typeof getBucketRealTimeLog;
    deleteBucketRealTimeLog: typeof deleteBucketRealTimeLog;
    getBucketInventory: typeof getBucketInventory;
    listBucketInventory: typeof listBucketInventory;
    putBucketInventory: typeof putBucketInventory;
    deleteBucketInventory: typeof deleteBucketInventory;
    putBucketTagging: typeof putBucketTagging;
    getBucketTagging: typeof getBucketTagging;
    deleteBucketTagging: typeof deleteBucketTagging;
    putBucketPayByTraffic: typeof putBucketPayByTraffic;
    getBucketPayByTraffic: typeof getBucketPayByTraffic;
    getBucketImageStyle: typeof getBucketImageStyle;
    getBucketImageStyleList: typeof getBucketImageStyleList;
    getBucketImageStyleListByName: typeof getBucketImageStyleListByName;
    getImageStyleBriefInfo: typeof getImageStyleBriefInfo;
    deleteBucketImageStyle: typeof deleteBucketImageStyle;
    putBucketImageStyle: typeof putBucketImageStyle;
    putBucketImageStyleSeparator: typeof putBucketImageStyleSeparator;
    putBucketImageProtect: typeof putBucketImageProtect;
    getBucketImageProtect: typeof getBucketImageProtect;
    getBucketImageStyleSeparator: typeof getBucketImageStyleSeparator;
    putBucketRename: typeof putBucketRename;
    getBucketRename: typeof getBucketRename;
    deleteBucketRename: typeof deleteBucketRename;
    putBucketTransferAcceleration: typeof putBucketTransferAcceleration;
    getBucketTransferAcceleration: typeof getBucketTransferAcceleration;
    copyObject: typeof copyObject;
    resumableCopyObject: typeof resumableCopyObject;
    deleteObject: typeof deleteObject;
    deleteMultiObjects: typeof deleteMultiObjects;
    getObject: typeof getObject;
    getObjectV2: typeof getObjectV2;
    getObjectToFile: typeof getObjectToFile;
    getObjectAcl: typeof getObjectAcl;
    headObject: typeof headObject;
    appendObject: typeof appendObject;
    listObjects: typeof listObjects;
    renameObject: typeof renameObject;
    fetchObject: typeof fetchObject;
    putFetchTask: typeof putFetchTask;
    listObjectsType2: typeof listObjectsType2;
    listObjectVersions: typeof listObjectVersions;
    putObject: typeof putObject;
    putObjectFromFile: typeof putObjectFromFile;
    putObjectAcl: typeof putObjectAcl;
    setObjectMeta: typeof setObjectMeta;
    createMultipartUpload: typeof createMultipartUpload;
    uploadPart: typeof uploadPart;
    uploadPartFromFile: typeof uploadPartFromFile;
    completeMultipartUpload: typeof completeMultipartUpload;
    abortMultipartUpload: typeof abortMultipartUpload;
    uploadPartCopy: typeof uploadPartCopy;
    listMultipartUploads: typeof listMultipartUploads;
    listParts: typeof listParts;
    downloadFile: typeof downloadFile;
    putObjectTagging: typeof putObjectTagging;
    getObjectTagging: typeof getObjectTagging;
    deleteObjectTagging: typeof deleteObjectTagging;
    listJobs: typeof listJobs;
    createJob: typeof createJob;
    deleteJob: typeof deleteJob;
    describeJob: typeof describeJob;
    updateJobStatus: typeof updateJobStatus;
    updateJobPriority: typeof updateJobPriority;
    restoreObject: typeof restoreObject;
    uploadFile: typeof uploadFile;
    getPreSignedUrl: typeof getPreSignedUrl;
    /**
     * alias to preSignedPostSignature
     */
    calculatePostSignature: typeof calculatePostSignature;
    preSignedPostSignature: typeof calculatePostSignature;
    preSignedPolicyURL: typeof preSignedPolicyURL;
    getBucketIntelligenttiering: typeof getBucketIntelligenttiering;
    listStorageLens: typeof listStorageLens;
    deleteStorageLens: typeof deleteStorageLens;
    getStorageLens: typeof getStorageLens;
    putStorageLens: typeof putStorageLens;
    putBucketNotificationType2: typeof putBucketNotificationType2;
    getBucketNotificationType2: typeof getBucketNotificationType2;
    putSymlink: typeof putSymlink;
    getSymlink: typeof getSymlink;
    putBucketAccessMonitor: typeof putBucketAccessMonitor;
    getBucketAccessMonitor: typeof getBucketAccessMonitor;
    putQosPolicy: typeof putQosPolicy;
    getQosPolicy: typeof getQosPolicy;
    deleteQosPolicy: typeof deleteQosPolicy;
    createMultiRegionAccessPoint: typeof createMultiRegionAccessPoint;
    getMultiRegionAccessPoint: typeof getMultiRegionAccessPoint;
    listMultiRegionAccessPoints: typeof listMultiRegionAccessPoints;
    getMultiRegionAccessPointRoutes: typeof getMultiRegionAccessPointRoutes;
    deleteMultiRegionAccessPoint: typeof deleteMultiRegionAccessPoint;
    submitMultiRegionAccessPointRoutes: typeof submitMultiRegionAccessPointRoutes;
    putMultiRegionAccessPointMirrorBack: (this: TOSBase, input: import("./methods/mrap/mirror").PutMultiRegionAccessPointMirrorBackInput) => Promise<import("./methods/base").TosResponse<import("./methods/mrap/mirror").DeleteMultiRegionAccessPointMirrorBackOutput>>;
    getMultiRegionAccessPointMirrorBack: (this: TOSBase, input: import("./methods/mrap/mirror").GetMultiRegionAccessPointMirrorBackInput) => Promise<import("./methods/base").TosResponse<import("./methods/mrap/mirror").GetMultiRegionAccessPointMirrorBackOutput>>;
    deleteMultiRegionAccessPointMirrorBack: (this: TOSBase, input: import("./methods/mrap/mirror").DeleteMultiRegionAccessPointMirrorBackInput) => Promise<import("./methods/base").TosResponse<import("./methods/mrap/mirror").DeleteMultiRegionAccessPointMirrorBackOutput>>;
    putBucketPrivateM3U8: typeof putBucketPrivateM3U8;
    getBucketPrivateM3U8: typeof getBucketPrivateM3U8;
    putBucketTrash: typeof putBucketTrash;
    getBucketTrash: typeof getBucketTrash;
}
