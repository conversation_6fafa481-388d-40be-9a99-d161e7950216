package org.jeecg.modules.jianyingpro.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 超级剪映小助手基础请求类
 * 所有请求DTO的基类，包含通用字段
 * 
 * <AUTHOR>
 * @since 2025-01-01
 */
@Data
public abstract class BaseJianyingProRequest {
    
    @ApiModelProperty(value = "访问密钥（必填）", required = true, 
                     example = "JianyingAPI_2025_SecureAccess_AigcView")
    @NotBlank(message = "access_key不能为空")
    @JsonProperty("access_key")
    private String accessKey;
    
    /**
     * 获取请求摘要信息，用于日志记录
     * 子类应该重写此方法提供具体的摘要信息
     * 
     * @return 请求摘要
     */
    public abstract String getSummary();
    
    /**
     * 参数验证方法
     * 子类可以重写此方法实现自定义验证逻辑
     */
    public void validate() {
        // 默认实现为空，子类可以重写
    }
}
