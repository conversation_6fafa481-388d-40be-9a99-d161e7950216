/**
 * 版本检查功能测试脚本
 * 用于测试版本检查API和逻辑
 */

const VersionChecker = require('./src/utils/VersionChecker');

async function testVersionCheck() {
  console.log('=== 版本检查功能测试 ===');
  
  // 创建版本检查器实例
  const versionChecker = new VersionChecker({
    apiBaseUrl: 'https://aigcview.com/jeecg-boot',
    programType: 'desktop'
  });
  
  try {
    console.log('当前版本:', versionChecker.currentVersion);
    
    // 测试版本比较算法
    console.log('\n=== 版本比较测试 ===');
    const testCases = [
      ['1.0.0', '1.0.1', -1],
      ['1.0.1', '1.0.0', 1],
      ['1.0.0', '1.0.0', 0],
      ['1.0.0', '1.1.0', -1],
      ['2.0.0', '1.9.9', 1],
      ['1.0', '1.0.0', 0]
    ];
    
    testCases.forEach(([v1, v2, expected]) => {
      const result = versionChecker.compareVersions(v1, v2);
      const status = result === expected ? '✅' : '❌';
      console.log(`${status} ${v1} vs ${v2} = ${result} (期望: ${expected})`);
    });
    
    // 测试API调用
    console.log('\n=== API调用测试 ===');
    const versionInfo = await versionChecker.checkForUpdates();
    
    console.log('版本检查结果:', {
      hasUpdate: versionInfo.hasUpdate,
      currentVersion: versionInfo.currentVersion,
      latestVersion: versionInfo.latestVersion,
      forceUpdate: versionInfo.forceUpdate,
      networkError: versionInfo.networkError
    });
    
    if (versionInfo.hasUpdate) {
      console.log('更新内容:', versionInfo.updateContent);
      console.log('下载链接:', versionInfo.downloadUrl);
    }
    
    // 测试缓存机制
    console.log('\n=== 缓存机制测试 ===');
    const cachedResult = await versionChecker.checkForUpdates();
    console.log('缓存结果:', cachedResult.hasUpdate ? '有更新' : '无更新');

    // 测试用户偏好设置
    console.log('\n=== 用户偏好设置测试 ===');
    const preferences = versionChecker.getUserPreferences();
    console.log('当前偏好设置:', preferences);

    // 更新偏好设置
    versionChecker.updateUserPreferences({
      autoCheck: false,
      reminderDelay: 7200000 // 2小时
    });

    const updatedPreferences = versionChecker.getUserPreferences();
    console.log('更新后偏好设置:', updatedPreferences);

    // 测试日志功能
    console.log('\n=== 日志功能测试 ===');
    versionChecker.log('测试日志消息', { test: true });
    versionChecker.log('测试警告消息', { warning: true }, 'warn');
    versionChecker.log('测试错误消息', { error: true }, 'error');

    const logs = versionChecker.getLogs(10);
    console.log(`获取到 ${logs.length} 条日志`);
    logs.slice(0, 3).forEach(log => {
      console.log(`[${log.level.toUpperCase()}] ${log.timestamp}: ${log.message}`);
    });

    // 测试错误处理
    console.log('\n=== 错误处理测试 ===');
    const originalApiUrl = versionChecker.apiBaseUrl;
    versionChecker.apiBaseUrl = 'http://invalid-url-for-testing.com';

    const errorResult = await versionChecker.checkForUpdates(true);
    console.log('错误处理结果:', {
      networkError: errorResult.networkError,
      errorType: errorResult.errorType,
      fallbackUsed: errorResult.fallbackUsed
    });

    // 恢复正确的API地址
    versionChecker.apiBaseUrl = originalApiUrl;

    console.log('\n=== 测试完成 ===');
    
  } catch (error) {
    console.error('测试失败:', error);
  }
}

// 运行测试
if (require.main === module) {
  testVersionCheck();
}

module.exports = testVersionCheck;
