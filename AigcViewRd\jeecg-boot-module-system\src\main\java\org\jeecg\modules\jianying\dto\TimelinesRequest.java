package org.jeecg.modules.jianying.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 * 时间线生成器请求参数
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class TimelinesRequest extends BaseJianyingRequest {
    
    @ApiModelProperty(value = "总长度，单位微秒（必填）", required = true, example = "10000000")
    @NotNull(message = "duration不能为空")
    @JsonProperty("duration")
    private Long zjDuration;

    @ApiModelProperty(value = "时间线上的个数（必填）", required = true, example = "5")
    @NotNull(message = "num不能为空")
    @JsonProperty("num")
    private Integer zjNum;

    @ApiModelProperty(value = "默认开始0，如果设置了，就从start开始", example = "0")
    @JsonProperty("start")
    private Integer zjStart;

    @ApiModelProperty(value = "方式，0平均分，1随机，默认0", example = "0")
    @JsonProperty("type")
    private Integer zjType;

    @Override
    public String getSummary() {
        return "TimelinesRequest{" +
               "accessKey=" + (getAccessKey() != null && getAccessKey().length() > 10 ?
                              getAccessKey().substring(0, 10) + "***" : "null") +
               ", duration=" + zjDuration +
               ", num=" + zjNum +
               ", start=" + zjStart +
               ", type=" + zjType +
               "}";
    }
}
