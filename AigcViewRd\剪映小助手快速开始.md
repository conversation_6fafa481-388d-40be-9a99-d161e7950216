# 剪映小助手API - 快速开始指南

## 🚀 5分钟快速上手

### 第一步：获取Access Key

所有API请求都需要包含access_key参数：
```
JianyingAPI_2025_SecureAccess_AigcView
```

### 第二步：发送第一个请求

创建一个简单的草稿：

```bash
curl -X POST "https://www.aigcview.cn/jeecg-boot/api/jianying/create_draft" \
     -H "Content-Type: application/json" \
     -d '{
       "access_key": "JianyingAPI_2025_SecureAccess_AigcView",
       "zj_height": 1080,
       "zj_width": 1920,
       "zj_user_id": 12345
     }'
```

### 第三步：处理响应

成功响应示例：
```json
{
  "success": true,
  "code": 200,
  "message": "草稿创建成功",
  "data": {
    "draft_url": "https://example.com/draft/abc123",
    "draft_id": "abc123"
  }
}
```

## 📋 常用API示例

### 1. 搜索贴纸

```bash
curl -X POST "https://www.aigcview.cn/jeecg-boot/api/jianying/search_sticker" \
     -H "Content-Type: application/json" \
     -d '{
       "access_key": "JianyingAPI_2025_SecureAccess_AigcView",
       "zj_keyword": "爱心"
     }'
```

### 2. 生成时间线

```bash
curl -X POST "https://www.aigcview.cn/jeecg-boot/api/jianying/timelines" \
     -H "Content-Type: application/json" \
     -d '{
       "access_key": "JianyingAPI_2025_SecureAccess_AigcView",
       "zj_duration": 10000000,
       "zj_num": 5
     }'
```

### 3. 添加贴纸

```bash
curl -X POST "https://www.aigcview.cn/jeecg-boot/api/jianying/add_sticker" \
     -H "Content-Type: application/json" \
     -d '{
       "access_key": "JianyingAPI_2025_SecureAccess_AigcView",
       "zj_sticker_id": "sticker_123",
       "zj_draft_url": "https://example.com/draft/abc123",
       "zj_start_time": 0,
       "zj_end_time": 5000000
     }'
```

## 🔧 JavaScript示例

```javascript
// 使用fetch API
async function createDraft() {
  const response = await fetch('https://www.aigcview.cn/jeecg-boot/api/jianying/create_draft', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      access_key: 'JianyingAPI_2025_SecureAccess_AigcView',
      zj_height: 1080,
      zj_width: 1920,
      zj_user_id: 12345
    })
  });
  
  const result = await response.json();
  console.log(result);
}

// 使用axios
import axios from 'axios';

const api = axios.create({
  baseURL: 'https://www.aigcview.cn/jeecg-boot/api/jianying',
  headers: {
    'Content-Type': 'application/json'
  }
});

async function searchSticker(keyword) {
  try {
    const response = await api.post('/search_sticker', {
      access_key: 'JianyingAPI_2025_SecureAccess_AigcView',
      zj_keyword: keyword
    });
    return response.data;
  } catch (error) {
    console.error('API调用失败:', error);
  }
}
```

## 🐍 Python示例

```python
import requests
import json

# 配置
BASE_URL = 'https://www.aigcview.cn/jeecg-boot/api/jianying'
ACCESS_KEY = 'JianyingAPI_2025_SecureAccess_AigcView'

def create_draft(height=1080, width=1920, user_id=12345):
    """创建草稿"""
    url = f'{BASE_URL}/create_draft'
    data = {
        'access_key': ACCESS_KEY,
        'zj_height': height,
        'zj_width': width,
        'zj_user_id': user_id
    }
    
    response = requests.post(url, json=data)
    return response.json()

def search_sticker(keyword):
    """搜索贴纸"""
    url = f'{BASE_URL}/search_sticker'
    data = {
        'access_key': ACCESS_KEY,
        'zj_keyword': keyword
    }
    
    response = requests.post(url, json=data)
    return response.json()

def generate_timelines(duration, num):
    """生成时间线"""
    url = f'{BASE_URL}/timelines'
    data = {
        'access_key': ACCESS_KEY,
        'zj_duration': duration,
        'zj_num': num
    }
    
    response = requests.post(url, json=data)
    return response.json()

# 使用示例
if __name__ == '__main__':
    # 创建草稿
    draft_result = create_draft()
    print('草稿创建结果:', draft_result)
    
    # 搜索贴纸
    sticker_result = search_sticker('爱心')
    print('贴纸搜索结果:', sticker_result)
    
    # 生成时间线
    timeline_result = generate_timelines(10000000, 5)
    print('时间线生成结果:', timeline_result)
```

## ☕ Java示例

```java
import com.alibaba.fastjson.JSONObject;
import org.springframework.http.*;
import org.springframework.web.client.RestTemplate;

public class JianyingApiClient {
    
    private static final String BASE_URL = "https://www.aigcview.cn/jeecg-boot/api/jianying";
    private static final String ACCESS_KEY = "JianyingAPI_2025_SecureAccess_AigcView";
    
    private RestTemplate restTemplate = new RestTemplate();
    
    public JSONObject createDraft(Integer height, Integer width, Integer userId) {
        String url = BASE_URL + "/create_draft";
        
        JSONObject request = new JSONObject();
        request.put("access_key", ACCESS_KEY);
        request.put("zj_height", height);
        request.put("zj_width", width);
        request.put("zj_user_id", userId);
        
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        
        HttpEntity<String> entity = new HttpEntity<>(request.toJSONString(), headers);
        
        ResponseEntity<String> response = restTemplate.postForEntity(url, entity, String.class);
        return JSONObject.parseObject(response.getBody());
    }
    
    public JSONObject searchSticker(String keyword) {
        String url = BASE_URL + "/search_sticker";
        
        JSONObject request = new JSONObject();
        request.put("access_key", ACCESS_KEY);
        request.put("zj_keyword", keyword);
        
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        
        HttpEntity<String> entity = new HttpEntity<>(request.toJSONString(), headers);
        
        ResponseEntity<String> response = restTemplate.postForEntity(url, entity, String.class);
        return JSONObject.parseObject(response.getBody());
    }
}
```

## ❌ 错误处理

### 常见错误码

| 错误码 | 说明 | 解决方案 |
|--------|------|----------|
| 400 | 参数验证失败 | 检查必填参数和参数格式 |
| 401 | access_key无效 | 确认access_key是否正确 |
| 404 | API路径不存在 | 检查API路径是否正确 |
| 500 | 服务器内部错误 | 联系技术支持 |

### 错误响应示例

```json
{
  "success": false,
  "code": 401,
  "message": "访问被拒绝：access_key无效"
}
```

```json
{
  "success": false,
  "code": 400,
  "message": "参数验证失败：zj_keyword不能为空"
}
```

## 📚 更多资源

- **完整API文档**: [剪映小助手API文档.md](./剪映小助手API文档.md)
- **在线API文档**: https://www.aigcview.cn/jeecg-boot/doc.html
- **官方网站**: https://www.aigcview.cn

---

**快速开始指南** | **版本**: v1.0.0 | **更新时间**: 2025-01-01
