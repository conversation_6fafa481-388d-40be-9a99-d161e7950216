# 🔗 对外使用插件数据联动标准流程

## 📋 **文档概述**

本文档定义了智界Aigc项目中所有对外暴露的API-Key接口的标准开发流程和数据联动规范。确保每个新增的API接口都能自动触发完整的数据追踪、权限验证和统计更新。

## 🎯 **核心原理**

所有对外暴露的API-Key接口都必须遵循统一的数据联动模式：
- **统一验证入口** - 通过 `aigcApiService.verifyPluginAndDeduct()` 验证权限和扣费
- **自动数据联动** - 验证成功后自动触发6个维度的数据更新
- **完整记录追踪** - 自动记录API使用情况和插件信息
- **事务一致性** - 所有操作在同一事务中完成，确保数据完整性

## 🔄 **标准联动流程**

### **第一步：接口入口统一验证**

```java
@PostMapping("/api/your-service/endpoint")
@ApiOperation("您的API服务描述")
public Result<YourResponse> yourApiEndpoint(
        @RequestBody @Valid YourRequest request, 
        HttpServletRequest httpRequest) {
    
    long startTime = System.currentTimeMillis();
    
    try {
        // 🔥 关键步骤：调用统一的验证和扣费方法
        // 这一步会自动触发完整的数据联动
        PluginVerifyResult verifyResult = aigcApiService.verifyPluginAndDeduct(
            request.getApiKey(),    // 用户的API密钥
            request.getPluginKey()  // 插件唯一标识，如 "xiaohongshufabu"
        );
        
        if (!verifyResult.isSuccess()) {
            // 验证失败，记录失败调用但不扣费
            recordApiUsage(request, httpRequest, 400, startTime, 
                          null, BigDecimal.ZERO, verifyResult.getMessage());
            return Result.error(verifyResult.getMessage());
        }
        
        // 🎉 验证成功，此时数据联动已经完成：
        // ✅ 用户余额已扣减
        // ✅ 插件统计已更新（调用次数+1，收益累加）
        // ✅ 创作者统计已更新（使用次数+1，收益累加）
        // ✅ 用户统计已更新（消费累加，调用次数+1）
        // ✅ 交易记录已生成
        
        // 🔥 第二步：执行具体业务逻辑
        YourResponse response = executeYourBusinessLogic(request);
        
        // 🔥 第三步：记录成功调用
        recordApiUsage(request, httpRequest, 200, startTime, 
                      response.getTokensUsed(), verifyResult.getCostAmount(), null);
        
        return Result.OK(response);
        
    } catch (Exception e) {
        log.error("业务执行失败", e);
        recordApiUsage(request, httpRequest, 500, startTime, 
                      null, BigDecimal.ZERO, e.getMessage());
        return Result.error("服务异常，请稍后重试");
    }
}
```

### **第二步：verifyPluginAndDeduct() 内部联动机制**

```java
// 在 AigcApiServiceImpl.verifyPluginAndDeduct() 中的完整流程
@Override
@Transactional(rollbackFor = Exception.class)
public PluginVerifyResult verifyPluginAndDeduct(String apiKey, String pluginKey) {
    
    // 1. 验证API Key有效性
    String userId = validateApiKey(apiKey);
    if (userId == null) {
        return PluginVerifyResult.failure("API Key无效");
    }
    
    // 2. 通过pluginKey查询插件信息
    AigcPlubShop plugin = plubShopService.getByPluginKey(pluginKey);
    if (plugin == null) {
        return PluginVerifyResult.failure("插件不存在");
    }
    
    // 3. 检查插件状态
    if (plugin.getStatus() != 1) {
        return PluginVerifyResult.failure("插件已禁用");
    }
    
    // 4. 检查用户余额
    BigDecimal needAmount = plugin.getNeednum();
    boolean hasBalance = userProfileService.checkBalance(userId, needAmount);
    if (!hasBalance) {
        return PluginVerifyResult.failure("余额不足");
    }
    
    // 5. 执行扣费（包含双表交易记录）
    boolean deductSuccess = userProfileService.consume(
        userId,
        needAmount,
        "调用插件: " + plugin.getPlubname(),
        "api_system"
    );
    // 🔥 注意：consume() 方法现在会同时写入两个表：
    // - aicg_user_transaction（订单记录表）- 用于前端订单页面显示
    // - aicg_user_record（详细记录表）- 用于详细的交易记录
    
    if (deductSuccess) {
        // 🔥 6. 触发数据联动更新
        performDataLinkage(userId, plugin, needAmount);
        
        return PluginVerifyResult.success(
            plugin.getPlubname(), 
            needAmount, 
            getCurrentBalance(userId), 
            getCurrentBalance(userId).subtract(needAmount),
            plugin.getId()
        );
    }
    
    return PluginVerifyResult.failure("扣费失败");
}
```

### **第三步：performDataLinkage() 完整联动**

```java
/**
 * 🔥 数据联动更新 - 插件调用成功后的完整数据联动
 */
@Transactional(rollbackFor = Exception.class)
public void performDataLinkage(String userId, AigcPlubShop plugin, BigDecimal amount) {
    try {
        // 🔥 联动1：更新插件商城统计
        updatePluginShopStats(plugin.getId(), amount);
        
        // 🔥 联动2：更新插件创作者统计
        updatePluginAuthorStats(plugin.getPlubwrite(), amount);
        
        // 🔥 联动3：更新用户扩展统计
        updateUserProfileStats(userId, amount);
        
        log.info("数据联动更新成功 - 插件: {} ({}), 用户: {}, 金额: {}", 
                plugin.getPlubname(), plugin.getPluginKey(), userId, amount);
                
    } catch (Exception e) {
        log.error("数据联动更新失败 - 插件: {}, 用户: {}, 错误: {}", 
                 plugin.getPluginKey(), userId, e.getMessage(), e);
        // 数据联动失败不影响主流程，只记录日志
    }
}

/**
 * 更新插件商城统计
 */
private void updatePluginShopStats(String pluginId, BigDecimal amount) {
    String sql = "UPDATE aigc_plub_shop SET " +
                "usernum = usernum + 1, " +                    // 总调用次数+1
                "income = income + ?, " +                      // 总收益累加
                "monthly_calls = monthly_calls + 1, " +        // 本月调用次数+1
                "monthly_income = monthly_income + ?, " +      // 本月收益累加
                "last_used_time = NOW(), " +                   // 更新最后使用时间
                "update_time = NOW() " +                       // 更新修改时间
                "WHERE id = ?";
    
    int result = jdbcTemplate.update(sql, amount, amount, pluginId);
    
    if (result > 0) {
        log.info("插件商城统计更新成功 - 插件ID: {}, 金额: {}", pluginId, amount);
    } else {
        log.error("插件商城统计更新失败 - 插件ID: {}, 可能插件不存在", pluginId);
    }
}

/**
 * 更新插件创作者统计
 */
private void updatePluginAuthorStats(String authorId, BigDecimal amount) {
    if (authorId == null || authorId.trim().isEmpty()) {
        log.warn("创作者ID为空，跳过创作者统计更新");
        return;
    }
    
    String sql = "UPDATE aigc_plub_author SET " +
                "plubusenum = plubusenum + 1, " +              // 总使用次数+1
                "total_income = total_income + ?, " +          // 总收益累加
                "monthly_calls = monthly_calls + 1, " +        // 本月调用次数+1
                "monthly_income = monthly_income + ?, " +      // 本月收益累加
                "last_active_time = NOW(), " +                 // 更新最后活跃时间
                "update_time = NOW() " +                       // 更新修改时间
                "WHERE id = ?";
    
    int result = jdbcTemplate.update(sql, amount, amount, authorId);
    
    if (result > 0) {
        log.info("创作者统计更新成功 - 创作者ID: {}, 金额: {}", authorId, amount);
    } else {
        log.error("创作者统计更新失败 - 创作者ID: {}, 可能创作者不存在", authorId);
    }
}

/**
 * 更新用户扩展统计
 * 注意：account_balance 和 total_consumption 已经在 deductBalance() 中更新了
 */
private void updateUserProfileStats(String userId, BigDecimal amount) {
    String sql = "UPDATE aicg_user_profile SET " +
                "plugin_call_count = plugin_call_count + 1, " +    // 插件调用次数+1
                "monthly_plugin_cost = monthly_plugin_cost + ?, " + // 本月插件消费累加
                "last_plugin_call_time = NOW(), " +                // 更新最后调用时间
                "update_time = NOW() " +                           // 更新修改时间
                "WHERE user_id = ?";
    
    int result = jdbcTemplate.update(sql, amount, userId);
    
    if (result > 0) {
        log.info("用户插件统计更新成功 - 用户ID: {}, 金额: {}", userId, amount);
    } else {
        log.error("用户插件统计更新失败 - 用户ID: {}, 可能用户不存在", userId);
    }
}
```

### **第四步：API使用记录自动补充插件信息**

```java
/**
 * 🔥 记录API使用情况 - 标准化方法
 */
private void recordApiUsage(YourRequest request, HttpServletRequest httpRequest,
                           int responseStatus, long startTime, Integer tokensUsed, 
                           BigDecimal costAmount, String errorMessage) {
    try {
        String userId = getUserIdFromApiKey(request.getApiKey());
        String requestParams = buildRequestParams(request);
        long responseTime = System.currentTimeMillis() - startTime;
        
        // 调用API使用记录服务，会自动提取和补充插件信息
        apiUsageService.recordUsage(
            userId,
            request.getApiKey(),
            "/api/your-service/endpoint",
            "POST",
            requestParams,                              // 包含 pluginKey 的请求参数
            responseStatus,
            (int) responseTime,
            tokensUsed,
            costAmount,
            getClientIpAddress(httpRequest),
            httpRequest.getHeader("User-Agent"),
            errorMessage
        );
    } catch (Exception e) {
        log.error("记录API使用情况失败", e);
    }
}

/**
 * 构建请求参数字符串（包含pluginKey）
 */
private String buildRequestParams(YourRequest request) {
    try {
        ObjectMapper mapper = new ObjectMapper();
        return mapper.writeValueAsString(request);
    } catch (Exception e) {
        return String.format("pluginKey: %s, apiKey: %s, ...", 
                           request.getPluginKey(), 
                           request.getApiKey().substring(0, 8) + "***");
    }
}
```

### **第五步：recordUsage() 自动提取插件信息**

```java
// 在 AicgUserApiUsageServiceImpl.recordUsage() 中自动处理
@Override
@Transactional(rollbackFor = Exception.class)
public AicgUserApiUsage recordUsage(String userId, String apiKey, String apiEndpoint, 
                                   String apiMethod, String requestParams, 
                                   Integer responseStatus, Integer responseTime,
                                   Integer tokensUsed, BigDecimal costAmount, 
                                   String ipAddress, String userAgent, String errorMessage) {
    
    // 🔥 自动提取插件信息
    String pluginKey = extractPluginKey(requestParams);
    String pluginId = null;
    String pluginName = null;
    
    if (pluginKey != null && !pluginKey.isEmpty()) {
        try {
            AigcPlubShop plugin = plubShopService.getByPluginKey(pluginKey);
            if (plugin != null) {
                pluginId = plugin.getId();
                pluginName = plugin.getPlubname();
            }
        } catch (Exception e) {
            log.warn("查询插件信息失败 - pluginKey: {}, 错误: {}", pluginKey, e.getMessage());
        }
    }
    
    // 创建API使用记录
    AicgUserApiUsage usage = new AicgUserApiUsage();
    usage.setUserId(userId);
    usage.setApiKey(apiKey);
    usage.setApiEndpoint(apiEndpoint);
    usage.setApiMethod(apiMethod);
    usage.setRequestParams(requestParams);
    usage.setResponseStatus(responseStatus);
    usage.setResponseTime(responseTime);
    usage.setTokensUsed(tokensUsed);
    usage.setCostAmount(costAmount);
    usage.setIpAddress(ipAddress);
    usage.setUserAgent(userAgent);
    usage.setErrorMessage(errorMessage);
    usage.setCallTime(new Date());
    usage.setCreateTime(new Date());
    
    // 🔥 自动补充插件信息
    usage.setPluginId(pluginId);      // 插件ID
    usage.setPluginKey(pluginKey);    // 插件标识
    usage.setPluginName(pluginName);  // 插件名称
    
    this.save(usage);
    
    log.info("API使用记录保存成功 - 用户: {}, 接口: {}, 插件: {} ({})", 
            userId, apiEndpoint, pluginName, pluginKey);
    
    return usage;
}

/**
 * 从请求参数中提取插件Key
 */
private String extractPluginKey(String requestParams) {
    if (requestParams == null || requestParams.isEmpty()) {
        return null;
    }
    
    // 查找 "pluginKey: " 模式
    String pattern = "pluginKey: ";
    int index = requestParams.indexOf(pattern);
    if (index == -1) {
        return null;
    }
    
    // 提取插件Key
    String remaining = requestParams.substring(index + pattern.length());
    int commaIndex = remaining.indexOf(",");
    String pluginKey;
    if (commaIndex != -1) {
        pluginKey = remaining.substring(0, commaIndex).trim();
    } else {
        pluginKey = remaining.trim();
    }
    
    // 验证插件Key格式
    if (pluginKey.matches("^[a-zA-Z0-9_-]+$")) {
        return pluginKey;
    }
    
    return null;
}
```

## 📊 **完整的数据联动效果**

### **一次API调用触发的数据更新**

```
用户调用: POST /api/ai/copywriting/generate
请求参数: { 
    "apiKey": "ak_1234567890abcdef", 
    "pluginKey": "aicopywriting",
    "keywords": "智能手机,科技",
    "style": "营销型"
}

自动触发的数据联动:
├── 1. 用户余额扣减 ✅
│   └── aicg_user_profile.account_balance -= 5.00
│
├── 2. 用户消费统计 ✅  
│   ├── aicg_user_profile.total_consumption += 5.00
│   ├── aicg_user_profile.plugin_call_count += 1
│   ├── aicg_user_profile.monthly_plugin_cost += 5.00
│   └── aicg_user_profile.last_plugin_call_time = NOW()
│
├── 3. 插件商城统计 ✅
│   ├── aigc_plub_shop.usernum += 1
│   ├── aigc_plub_shop.income += 5.00
│   ├── aigc_plub_shop.monthly_calls += 1
│   ├── aigc_plub_shop.monthly_income += 5.00
│   └── aigc_plub_shop.last_used_time = NOW()
│
├── 4. 创作者收益统计 ✅
│   ├── aigc_plub_author.plubusenum += 1
│   ├── aigc_plub_author.total_income += 5.00
│   ├── aigc_plub_author.monthly_calls += 1
│   ├── aigc_plub_author.monthly_income += 5.00
│   └── aigc_plub_author.last_active_time = NOW()
│
├── 5. 交易记录生成（双表写入）✅
│   ├── aicg_user_transaction 插入记录 (订单记录表)
│   │   ├── plugin_id: "1933809340810715137"
│   │   ├── plugin_key: "aicopywriting"
│   │   ├── plugin_name: "AI文案生成"
│   │   ├── order_status: 3 (已完成)
│   │   ├── order_type: "plugin"
│   │   ├── amount: 5.00
│   │   ├── balance_before: 100.00
│   │   └── balance_after: 95.00
│   └── aicg_user_record 插入记录 (详细记录表)
│       ├── plugin_id: "1933809340810715137"
│       ├── plugin_key: "aicopywriting"
│       ├── plugin_name: "AI文案生成"
│       ├── transaction_type: 1 (消费)
│       ├── amount: 5.00
│       ├── balance_before: 100.00
│       └── balance_after: 95.00
│
└── 6. API使用记录 ✅
    └── aicg_user_api_usage 插入记录 (含完整插件信息)
        ├── plugin_id: "1933809340810715137"
        ├── plugin_key: "aicopywriting"
        ├── plugin_name: "AI文案生成"
        ├── api_endpoint: "/api/ai/copywriting/generate"
        ├── cost_amount: 5.00
        ├── response_status: 200
        └── call_time: NOW()
```

## 🚀 **新接口开发标准模板**

### **Controller标准模板**

```java
@RestController
@RequestMapping("/api/your-service")
@Api(tags = "您的服务描述")
@Slf4j
public class YourServiceController {

    @Autowired
    private IAigcApiService aigcApiService;
    
    @Autowired
    private IAicgUserApiUsageService apiUsageService;

    @PostMapping("/your-endpoint")
    @ApiOperation("您的API功能描述")
    public Result<YourResponse> yourApiMethod(
            @RequestBody @Valid YourRequest request,
            HttpServletRequest httpRequest) {
        
        long startTime = System.currentTimeMillis();
        
        try {
            // 🔥 步骤1：统一验证和扣费（自动触发数据联动）
            PluginVerifyResult verifyResult = aigcApiService.verifyPluginAndDeduct(
                request.getApiKey(),     // 必须：用户API密钥
                request.getPluginKey()   // 必须：插件唯一标识
            );
            
            if (!verifyResult.isSuccess()) {
                // 验证失败，记录失败调用
                recordApiUsage(request, httpRequest, 400, startTime, 
                              null, BigDecimal.ZERO, verifyResult.getMessage());
                return Result.error(verifyResult.getMessage());
            }
            
            // 🔥 步骤2：执行具体业务逻辑
            YourResponse response = executeYourBusinessLogic(request);
            
            // 🔥 步骤3：记录成功调用
            recordApiUsage(request, httpRequest, 200, startTime, 
                          response.getTokensUsed(), verifyResult.getCostAmount(), null);
            
            return Result.OK(response);
            
        } catch (Exception e) {
            log.error("业务执行失败", e);
            recordApiUsage(request, httpRequest, 500, startTime, 
                          null, BigDecimal.ZERO, e.getMessage());
            return Result.error("服务异常，请稍后重试");
        }
    }
    
    /**
     * 执行具体业务逻辑
     */
    private YourResponse executeYourBusinessLogic(YourRequest request) {
        // 在这里实现您的具体业务逻辑
        // 例如：调用AI服务、处理数据、生成结果等
        
        YourResponse response = new YourResponse();
        // ... 设置响应数据
        return response;
    }
    
    /**
     * 记录API使用情况
     */
    private void recordApiUsage(YourRequest request, HttpServletRequest httpRequest,
                               int responseStatus, long startTime, Integer tokensUsed, 
                               BigDecimal costAmount, String errorMessage) {
        try {
            String userId = getUserIdFromApiKey(request.getApiKey());
            String requestParams = buildRequestParams(request);
            long responseTime = System.currentTimeMillis() - startTime;
            
            apiUsageService.recordUsage(
                userId,
                request.getApiKey(),
                "/api/your-service/your-endpoint",
                "POST",
                requestParams,
                responseStatus,
                (int) responseTime,
                tokensUsed,
                costAmount,
                getClientIpAddress(httpRequest),
                httpRequest.getHeader("User-Agent"),
                errorMessage
            );
        } catch (Exception e) {
            log.error("记录API使用情况失败", e);
        }
    }
    
    // ... 其他辅助方法
}
```

### **请求参数标准结构**

```java
@ApiModel("您的API请求参数")
public class YourRequest {
    
    @NotBlank(message = "API Key不能为空")
    @ApiModelProperty(value = "API密钥", required = true, example = "ak_1234567890abcdef")
    private String apiKey;
    
    @NotBlank(message = "插件标识不能为空")
    @ApiModelProperty(value = "插件唯一标识", required = true, example = "yourpluginkey")
    private String pluginKey;
    
    // 您的业务参数
    @ApiModelProperty(value = "业务参数描述", example = "示例值")
    private String yourBusinessParam;
    
    // ... 其他业务参数
}
```

### **响应结果标准结构**

```java
@ApiModel("您的API响应结果")
public class YourResponse {
    
    @ApiModelProperty("业务结果数据")
    private String resultData;
    
    @ApiModelProperty("消耗的Token数量")
    private Integer tokensUsed;
    
    @ApiModelProperty("处理时间")
    private Date processTime;
    
    // ... 其他响应字段
}
```

## ✅ **开发规范检查清单**

### **必须遵循的规范**

#### **1. 接口设计规范** ✅
- [ ] 请求参数包含 `apiKey` 和 `pluginKey` 字段
- [ ] 使用 `@Valid` 注解进行参数验证
- [ ] 添加完整的 Swagger 文档注解
- [ ] 统一的响应结果格式

#### **2. 数据联动规范** ✅
- [ ] 调用 `aigcApiService.verifyPluginAndDeduct()` 进行验证和扣费
- [ ] 验证失败时记录失败调用
- [ ] 业务成功时记录成功调用
- [ ] 异常时记录异常调用

#### **3. 日志记录规范** ✅
- [ ] 使用 `@Slf4j` 注解
- [ ] 关键操作添加日志记录
- [ ] 异常时记录详细错误信息
- [ ] 避免使用 `System.out.println`

#### **4. 异常处理规范** ✅
- [ ] 使用 try-catch 包装业务逻辑
- [ ] 异常时返回统一的错误格式
- [ ] 不向用户暴露内部错误详情
- [ ] 记录完整的异常堆栈

### **自动获得的功能** ✅

#### **1. 完整数据联动**
- ✅ 用户余额自动扣减
- ✅ 插件统计自动更新（调用次数、收益）
- ✅ 创作者统计自动更新（使用数、收益）
- ✅ 用户统计自动更新（消费、调用次数）
- ✅ 交易记录自动生成（含插件信息）
- ✅ API使用记录自动生成（含插件信息）

#### **2. 权限验证**
- ✅ API Key有效性验证
- ✅ 插件权限验证
- ✅ 插件状态检查
- ✅ 用户余额检查

#### **3. 统计分析**
- ✅ 插件热度排行
- ✅ 创作者收益排行
- ✅ 用户消费统计
- ✅ API调用分析
- ✅ 收入趋势分析

#### **4. 运营支持**
- ✅ 精确的分成结算
- ✅ 完整的调用记录
- ✅ 用户行为分析
- ✅ 插件性能监控

## 🎯 **总结**

遵循本标准流程开发的API接口将自动获得：

### **技术保障**
- 🔒 **安全性** - 完整的权限验证和API Key管理
- 🔄 **一致性** - 事务保证的数据一致性
- 📊 **可追溯** - 完整的调用记录和数据链路
- 🚀 **高性能** - 优化的数据库操作和索引

### **业务价值**
- 💰 **精确计费** - 准确的费用扣减和收益统计
- 📈 **数据驱动** - 完整的运营数据支持决策
- 👥 **用户体验** - 透明的消费记录和余额管理
- 🎯 **商业洞察** - 深入的用户行为和插件性能分析

---

**文档版本**: V1.1
**创建时间**: 2025-06-21
**更新时间**: 2025-06-21
**适用范围**: 所有对外API-Key接口
**维护团队**: 开发团队

### **V1.1 更新内容**
- ✅ **双表写入机制** - consume() 方法现在同时写入订单表和记录表
- ✅ **订单状态管理** - 插件调用自动设置为已完成状态和正确类型
- ✅ **数据一致性保证** - 确保前端订单页面和后台记录的数据一致性
- ✅ **完善插件信息** - 自动提取和补充完整的插件信息到所有相关表
