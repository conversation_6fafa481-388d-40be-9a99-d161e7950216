package org.jeecg.modules.api.config;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import static org.junit.jupiter.api.Assertions.*;

/**
 * @Description: 用户活跃状态配置类测试
 * @Author: AigcView
 * @Date: 2025-01-30
 * @Version: V1.0
 */
@SpringBootTest
@ActiveProfiles("dev")
public class UserActivityConfigTest {

    @Autowired
    private UserActivityConfig userActivityConfig;

    @Test
    public void testConfigLoading() {
        // 验证配置是否正确加载
        assertNotNull(userActivityConfig, "配置类应该被正确注入");
        
        // 验证基础配置
        assertTrue(userActivityConfig.getEnabled(), "开发环境应该启用用户活跃状态追踪");
        assertFalse(userActivityConfig.getTestMode(), "开发环境不应该使用测试模式");
        
        // 验证缓存配置
        assertEquals(300, userActivityConfig.getCacheDuration(), "缓存持续时间应该是300秒");
        assertEquals(600, userActivityConfig.getCacheExpireTime(), "缓存过期时间应该是600秒");
        assertEquals("aigc:user:activity:", userActivityConfig.getRedisKeyPrefix(), "Redis键前缀应该正确");
        
        // 验证批量处理配置
        assertEquals(100, userActivityConfig.getBatchSize(), "批量大小应该是100");
        assertEquals(10, userActivityConfig.getBatchInterval(), "批量间隔应该是10秒");
        
        // 验证性能配置
        assertEquals(1000L, userActivityConfig.getPerformanceThreshold(), "性能阈值应该是1000ms");
        assertTrue(userActivityConfig.getEnablePerformanceMonitoring(), "应该启用性能监控");
        
        // 验证灰度发布配置
        assertEquals(100, userActivityConfig.getRolloutPercentage(), "开发环境应该100%开启");
        
        // 验证核心API列表
        assertNotNull(userActivityConfig.getCriticalApis(), "核心API列表不应该为空");
        assertTrue(userActivityConfig.getCriticalApis().contains("/payment/"), "应该包含支付API");
        assertTrue(userActivityConfig.getCriticalApis().contains("/login"), "应该包含登录API");
        
        // 验证异步处理配置
        assertTrue(userActivityConfig.getEnableAsyncProcessing(), "应该启用异步处理");
        assertEquals(5, userActivityConfig.getAsyncThreadPoolSize(), "线程池大小应该是5");
        assertEquals(1000, userActivityConfig.getQueueCapacity(), "队列容量应该是1000");
        
        // 验证降级机制配置
        assertTrue(userActivityConfig.getEnableDegradation(), "应该启用降级机制");
        assertEquals(300, userActivityConfig.getDegradationRecoveryTime(), "降级恢复时间应该是300秒");
    }

    @Test
    public void testConfigValidation() {
        // 验证配置有效性检查
        assertTrue(userActivityConfig.isValid(), "配置应该是有效的");
    }

    @Test
    public void testRedisKeyGeneration() {
        // 测试Redis键生成
        String key = userActivityConfig.getRedisKey("test:user:123");
        assertEquals("aigc:user:activity:test:user:123", key, "Redis键应该正确生成");
    }

    @Test
    public void testCriticalApiCheck() {
        // 测试核心API检查
        assertTrue(userActivityConfig.isCriticalApi("/payment/create"), "支付API应该被识别为核心API");
        assertTrue(userActivityConfig.isCriticalApi("/login"), "登录API应该被识别为核心API");
        assertTrue(userActivityConfig.isCriticalApi("/api/auth/token"), "认证API应该被识别为核心API");
        
        assertFalse(userActivityConfig.isCriticalApi("/api/user/profile"), "用户资料API不应该被识别为核心API");
        assertFalse(userActivityConfig.isCriticalApi("/api/aigc/generate"), "生成API不应该被识别为核心API");
    }

    @Test
    public void testRolloutCheck() {
        // 测试灰度发布检查
        // 开发环境rolloutPercentage是100，所以所有用户都应该在灰度范围内
        assertTrue(userActivityConfig.isInRollout("user123"), "用户应该在灰度范围内");
        assertTrue(userActivityConfig.isInRollout("user456"), "用户应该在灰度范围内");
        assertTrue(userActivityConfig.isInRollout("user789"), "用户应该在灰度范围内");
    }

    @Test
    public void testConfigConstraints() {
        // 验证配置约束
        assertTrue(userActivityConfig.getCacheDuration() >= 60, "缓存持续时间应该至少60秒");
        assertTrue(userActivityConfig.getCacheDuration() <= 3600, "缓存持续时间应该不超过3600秒");
        
        assertTrue(userActivityConfig.getBatchSize() >= 10, "批量大小应该至少10");
        assertTrue(userActivityConfig.getBatchSize() <= 1000, "批量大小应该不超过1000");
        
        assertTrue(userActivityConfig.getBatchInterval() >= 5, "批量间隔应该至少5秒");
        assertTrue(userActivityConfig.getBatchInterval() <= 300, "批量间隔应该不超过300秒");
        
        assertTrue(userActivityConfig.getPerformanceThreshold() >= 100, "性能阈值应该至少100ms");
        assertTrue(userActivityConfig.getPerformanceThreshold() <= 10000, "性能阈值应该不超过10000ms");
        
        assertTrue(userActivityConfig.getRolloutPercentage() >= 0, "灰度比例应该至少0%");
        assertTrue(userActivityConfig.getRolloutPercentage() <= 100, "灰度比例应该不超过100%");
    }

    @Test
    public void testNullSafety() {
        // 测试空值安全性
        assertFalse(userActivityConfig.isCriticalApi(null), "空API路径应该返回false");
        
        // 创建一个临时配置对象测试空值情况
        UserActivityConfig tempConfig = new UserActivityConfig();
        tempConfig.setCriticalApis(null);
        assertFalse(tempConfig.isCriticalApi("/test"), "空核心API列表应该返回false");
    }
}
