package org.jeecg.modules.jianying.validator;

import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.springframework.stereotype.Component;

/**
 * 剪映小助手Access Key验证器
 * 统一验证access_key参数
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
@Slf4j
@Component
public class JianyingAccessValidator {
    
    /**
     * 固定的访问密钥
     */
    private static final String VALID_ACCESS_KEY = "JianyingAPI_2025_SecureAccess_AigcView";
    
    /**
     * 验证访问密钥
     * 
     * @param accessKey 待验证的访问密钥
     * @return true表示验证通过，false表示验证失败
     */
    public boolean validateAccessKey(String accessKey) {
        if (accessKey == null || accessKey.trim().isEmpty()) {
            log.warn("剪映API访问被拒绝：access_key为空");
            return false;
        }
        
        boolean isValid = VALID_ACCESS_KEY.equals(accessKey.trim());
        
        if (isValid) {
            log.debug("剪映API访问密钥验证通过");
        } else {
            log.warn("剪映API访问被拒绝：无效的access_key: {}", accessKey);
        }
        
        return isValid;
    }
    
    /**
     * 获取访问被拒绝时的错误响应
     * 
     * @return 标准的错误响应对象
     */
    public Result<?> getUnauthorizedResult() {
        return Result.error(401, "访问密钥无效，请检查access_key参数");
    }
    
    /**
     * 获取访问密钥为空时的错误响应
     * 
     * @return 标准的错误响应对象
     */
    public Result<?> getMissingKeyResult() {
        return Result.error(400, "缺少必填参数：access_key");
    }
    
    /**
     * 验证访问密钥并返回结果
     * 
     * @param accessKey 待验证的访问密钥
     * @return 验证结果，包含成功状态和错误信息
     */
    public Result<?> validateAndGetResult(String accessKey) {
        if (accessKey == null || accessKey.trim().isEmpty()) {
            return getMissingKeyResult();
        }
        
        if (!validateAccessKey(accessKey)) {
            return getUnauthorizedResult();
        }
        
        return Result.OK("访问密钥验证通过");
    }
    
    /**
     * 获取当前有效的访问密钥
     *
     * @return 当前系统使用的访问密钥
     */
    public String getValidAccessKey() {
        return VALID_ACCESS_KEY;
    }
    
    /**
     * 检查访问密钥是否为有效格式
     * 
     * @param accessKey 待检查的访问密钥
     * @return true表示格式有效，false表示格式无效
     */
    public boolean isValidFormat(String accessKey) {
        if (accessKey == null || accessKey.trim().isEmpty()) {
            return false;
        }
        
        // 检查基本格式：应该包含特定的前缀和后缀
        String trimmed = accessKey.trim();
        return trimmed.startsWith("JianyingAPI_") && 
               trimmed.contains("_SecureAccess_") && 
               trimmed.endsWith("AigcView");
    }
    
    /**
     * 记录访问日志
     * 
     * @param accessKey 访问密钥
     * @param apiPath API路径
     * @param clientIp 客户端IP
     * @param success 是否成功
     */
    public void logAccess(String accessKey, String apiPath, String clientIp, boolean success) {
        if (success) {
            log.info("剪映API访问成功 - Path: {}, IP: {}, AccessKey: {}***", 
                    apiPath, clientIp, 
                    accessKey != null && accessKey.length() > 10 ? 
                    accessKey.substring(0, 10) : "null");
        } else {
            log.warn("剪映API访问失败 - Path: {}, IP: {}, AccessKey: {}", 
                    apiPath, clientIp, 
                    accessKey != null && accessKey.length() > 10 ? 
                    accessKey.substring(0, 10) + "***" : "null");
        }
    }
}
