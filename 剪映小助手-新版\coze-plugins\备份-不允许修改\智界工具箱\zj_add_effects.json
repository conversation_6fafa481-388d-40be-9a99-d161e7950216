{"openapi": "3.0.0", "info": {"title": "剪映小助手_智界工具箱 - 添加特效", "description": "添加特效", "version": "1.0.0"}, "servers": [{"url": "https://www.aigcview.com"}], "paths": {"/jeecg-boot/api/jianying/add_effects": {"post": {"summary": "添加特效", "description": "添加特效", "operationId": "addEffects_zj", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"access_key": {"type": "string", "description": "访问密钥（必填）", "example": "JianyingAPI_2025_SecureAccess_AigcView", "default": "JianyingAPI_2025_SecureAccess_AigcView"}, "zj_draft_url": {"type": "string", "description": "草稿地址，使用createDraft_zj输出的draft_url即可（必填）", "example": "https://example.com/draft/123"}, "zj_effect_infos": {"type": "string", "description": "特效数组内容（必填）", "example": "[{\"effect_title\":\"金粉闪闪\",\"end\":5000000,\"start\":0}]"}}, "required": ["access_key", "zj_draft_url", "zj_effect_infos"], "additionalProperties": false}}}}, "responses": {"200": {"description": "成功添加特效", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "description": "响应消息", "example": "不知道导入的请看：https://www.aigcview.com/JianYingDraft?draft=https://aigcview-plub.tos-s3-cn-guangzhou.volces.com/jianying-assistant/drafts/2025/01/05/draft_123456.json"}, "draft_url": {"type": "string", "description": "更新后的草稿地址", "example": "https://aigcview-plub.tos-s3-cn-guangzhou.volces.com/jianying-assistant/drafts/2025/01/05/draft_123456.json"}}, "required": ["message", "draft_url"]}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "description": "错误信息"}}, "required": ["error"]}}}}, "500": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "description": "错误信息"}}, "required": ["error"]}}}}}}}}}