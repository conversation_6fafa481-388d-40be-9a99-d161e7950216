package org.jeecg.modules.jianyingpro.dto.request;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 一体化关键帧添加请求
 * 合并 keyframes_infos + add_keyframes 的参数
 *
 * <AUTHOR>
 * @since 2025-01-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class JianyingProAddKeyframesRequest extends BaseJianyingProRequest {

    // ========== 核心参数 ==========
    @ApiModelProperty(value = "草稿地址，使用create_draft输出的draft_url即可（必填）", required = true,
                     example = "https://example.com/draft/123")
    @NotBlank(message = "draft_url不能为空")
    @JsonProperty("draft_url")
    private String draftUrl;

    @ApiModelProperty(value = "关键帧类型（必填）", required = true,
                     example = "KFTypeAlpha")
    @NotBlank(message = "ctype不能为空")
    @JsonProperty("ctype")
    private String ctype;

    @ApiModelProperty(value = "需要放置关键帧的位置比例（必填）", required = true,
                     example = "0|100")
    @NotBlank(message = "offsets不能为空")
    @JsonProperty("offsets")
    private String offsets;

    @ApiModelProperty(value = "轨道数据，add_images节点输出（必填）", required = true,
                     example = "[{\"id\": \"segment1\", \"type\": \"video\"}]")
    @NotEmpty(message = "segment_infos不能为空")
    @JsonProperty("segment_infos")
    private List<JSONObject> segmentInfos;

    @ApiModelProperty(value = "对应offsets的值，长度要一致（必填）", required = true,
                     example = "1|2")
    @NotBlank(message = "values不能为空")
    @JsonProperty("values")
    private String values;

    // ========== 来自稳定版keyframes_infos的参数 ==========
    @ApiModelProperty(value = "视频宽度", example = "1920")
    @JsonProperty("width")
    private Integer width;

    @ApiModelProperty(value = "视频高度", example = "1080")
    @JsonProperty("height")
    private Integer height;

    @Override
    public String getSummary() {
        return "JianyingProAddKeyframesRequest{" +
               "draftUrl=" + (draftUrl != null && draftUrl.length() > 50 ?
                            draftUrl.substring(0, 50) + "***" : draftUrl) +
               ", ctype=" + ctype +
               ", offsets=" + offsets +
               ", segmentInfosCount=" + (segmentInfos != null ? segmentInfos.size() : 0) +
               ", values=" + values +
               ", width=" + width +
               ", height=" + height +
               "}";
    }

    @Override
    public void validate() {
        super.validate();

        // ctype、offsets、segment_infos、values都是必填的（复制自稳定版keyframes_infos要求）
        if (ctype == null || ctype.trim().isEmpty()) {
            throw new IllegalArgumentException(
                "参数不完整：ctype不能为空"
            );
        }
        if (offsets == null || offsets.trim().isEmpty()) {
            throw new IllegalArgumentException(
                "参数不完整：offsets不能为空"
            );
        }
        if (segmentInfos == null || segmentInfos.isEmpty()) {
            throw new IllegalArgumentException(
                "参数不完整：segment_infos不能为空"
            );
        }
        if (values == null || values.trim().isEmpty()) {
            throw new IllegalArgumentException(
                "参数不完整：values不能为空"
            );
        }
    }
}
