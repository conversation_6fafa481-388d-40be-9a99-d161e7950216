# 智界Aigc 对外API接口详细对接文档

## 📋 文档概述

本文档详细描述智界Aigc系统对外提供的两个核心API接口，包括接口规范、安全机制、参数说明、响应格式、错误处理、代码示例等完整信息。

### 🎯 接口列表
1. **API-Key验证接口** - 验证API密钥有效性，支持插件调用扣费
2. **HTML文件生成接口** - 将HTML代码生成文件并返回访问地址和二维码

### 🔐 安全机制
- **API-Key身份验证** - 确保调用者身份合法
- **请求签名验证** - 防止请求被篡改和重放攻击
- **HTML内容安全检查** - 防止恶意代码注入
- **时间戳验证** - 防止重放攻击（5分钟有效期）
- **内容长度限制** - 防止资源滥用

---

## 🔑 接口一：API-Key验证接口

### 基本信息
- **接口名称**: API-Key验证接口
- **接口地址**: `POST /api/aigc/verify-apikey`
- **Content-Type**: `application/x-www-form-urlencoded`
- **功能描述**: 验证API密钥的有效性，支持插件调用验证和自动扣费

### 功能模式

#### 1. 基础验证模式
仅验证API-Key的有效性，不进行任何扣费操作，适用于：
- 检查API-Key是否有效
- 获取用户基本信息
- 验证用户状态

#### 2. 插件调用模式
验证API-Key后，检查用户余额并扣除插件所需点数，适用于：
- 插件功能调用前的验证
- 自动扣费和统计更新
- 插件使用权限控制

### 请求参数

| 参数名 | 类型 | 必填 | 长度限制 | 说明 |
|--------|------|------|----------|------|
| apiKey | String | 是 | 35位 | API密钥，格式：ak_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx |
| pluginKey | String | 否 | 1-100字符 | 插件唯一标识，提供时进行插件调用验证和扣费 |

#### API-Key格式规范
- **前缀**: 必须以 `ak_` 开头
- **长度**: 总长度35位（前缀3位 + 密钥32位）
- **字符集**: 密钥部分仅包含大小写字母和数字 `[a-zA-Z0-9]`
- **示例**: `ak_afc0fd5b3fbf4265af92280630b37b91`

### 请求示例

#### 1. 基础验证请求
```bash
curl -X POST "https://www.aigcview.com/api/aigc/verify-apikey" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "apiKey=ak_afc0fd5b3fbf4265af92280630b37b91"
```

#### 2. 插件调用验证请求
```bash
curl -X POST "https://www.aigcview.com/api/aigc/verify-apikey" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "apiKey=ak_afc0fd5b3fbf4265af92280630b37b91&pluginKey=xiaohongshufabu"
```

### 响应格式

#### 成功响应结构
```json
{
  "success": true,
  "message": "验证成功",
  "code": 200,
  "result": {
    "valid": true,
    "userId": "1400726588175749122",
    "nickname": "智界用户",
    "memberLevel": 1,
    "balance": 100.00,
    "pluginVerified": false,
    // 插件调用时额外返回字段
    "pluginName": "小红书发布",
    "deductedAmount": 0.50,
    "balanceAfter": 99.50,
    "pluginId": "plugin_001"
  },
  "timestamp": 1718352000000
}
```

#### 响应字段说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| success | Boolean | 请求是否成功 |
| message | String | 响应消息 |
| code | Integer | 响应状态码 |
| result | Object | 响应数据 |
| timestamp | Long | 响应时间戳 |

**result对象字段**:

| 字段名 | 类型 | 必返回 | 说明 |
|--------|------|--------|------|
| valid | Boolean | 是 | API-Key是否有效 |
| userId | String | 是 | 用户ID |
| nickname | String | 是 | 用户昵称 |
| memberLevel | Integer | 是 | 会员等级：1-普通用户，2-VIP用户，3-SVIP用户 |
| balance | BigDecimal | 是 | 账户余额（元） |
| pluginVerified | Boolean | 是 | 是否进行了插件验证 |
| pluginName | String | 插件调用时 | 插件名称 |
| deductedAmount | BigDecimal | 插件调用时 | 扣除金额（元） |
| balanceAfter | BigDecimal | 插件调用时 | 扣费后余额（元） |
| pluginId | String | 插件调用时 | 插件ID |

### 错误响应

#### 1. API-Key格式错误
```json
{
  "success": false,
  "message": "API-Key格式错误",
  "code": 500,
  "result": null,
  "timestamp": 1718352000000
}
```

#### 2. API-Key无效
```json
{
  "success": false,
  "message": "API-Key无效",
  "code": 500,
  "result": null,
  "timestamp": 1718352000000
}
```

#### 3. 用户状态异常
```json
{
  "success": false,
  "message": "用户状态异常，API-Key已被禁用",
  "code": 500,
  "result": null,
  "timestamp": 1718352000000
}
```

#### 4. 插件不存在
```json
{
  "success": false,
  "message": "插件不存在: 小红书发布",
  "code": 500,
  "result": null,
  "timestamp": 1718352000000
}
```

#### 5. 余额不足
```json
{
  "success": false,
  "message": "余额不足，插件 小红书发布 需要 0.50 元，当前余额 0.20 元",
  "code": 500,
  "result": null,
  "timestamp": 1718352000000
}
```

### 插件调用机制详解

#### 扣费流程
1. **验证API-Key有效性** - 检查密钥格式和用户状态
2. **查询插件信息** - 根据插件名称获取插件详情和价格
3. **检查用户余额** - 验证余额是否足够支付插件费用
4. **执行扣费操作** - 从用户账户扣除相应金额
5. **更新统计信息** - 更新插件收益、调用次数、创作者收益
6. **记录交易日志** - 生成用户消费记录

#### 统计信息更新
- **插件统计**: 收益金额增加、调用次数+1
- **创作者统计**: 插件使用总数+1、累计收益增加
- **用户记录**: 生成消费交易记录

#### 事务保证
- 所有操作在数据库事务中执行
- 扣费失败时自动回滚
- 确保数据一致性

---

## 📄 接口二：HTML文件生成接口

### 基本信息
- **接口名称**: HTML文件生成接口
- **接口地址**: `POST /api/aigc/generate-html`
- **Content-Type**: `application/json`
- **功能描述**: 将HTML代码生成文件并返回访问地址和二维码

### 安全机制

#### 签名算法
```
signature = SHA256(apiKey + htmlContent + timestamp)
```

#### 签名生成步骤
1. 将API密钥、HTML内容、时间戳按顺序拼接
2. 使用SHA-256算法计算哈希值
3. 将哈希值转换为十六进制字符串

#### 时间戳验证
- 请求时间戳与服务器时间差不能超过5分钟
- 防止重放攻击
- 时间戳格式：Unix毫秒时间戳

### 请求参数

| 参数名 | 类型 | 必填 | 长度限制 | 说明 |
|--------|------|------|----------|------|
| apiKey | String | 是 | 35位 | API密钥 |
| htmlContent | String | 是 | 最大2MB | HTML内容 |
| timestamp | String | 是 | 13位 | Unix毫秒时间戳 |
| signature | String | 是 | 64位 | 请求签名（SHA-256十六进制） |
| filename | String | 否 | 最大100字符 | 自定义文件名（不含扩展名） |

### HTML内容安全检查

#### 禁止的HTML标签
```html
<script>、<iframe>、<object>、<embed>、<form>、<input>、
<meta>、<link>、<style>、<base>、<applet>、<audio>、
<video>、<source>、<track>
```

#### 禁止的HTML属性
```html
on* 事件处理器（onclick、onload等）
javascript: 协议
vbscript: 协议
data:text/html 协议
expression() CSS表达式
```

#### 禁止的协议
```html
javascript:、vbscript:、data:、file:、ftp:、
about:、chrome:、resource:
```

#### 其他安全限制
- 不允许引用外部资源（http://、https://、//）
- 不允许@import样式导入
- 不允许IE条件注释
- 不允许包含SQL关键字

### 请求示例

```bash
curl -X POST "https://www.aigcview.com/api/aigc/generate-html" \
  -H "Content-Type: application/json" \
  -d '{
    "apiKey": "ak_afc0fd5b3fbf4265af92280630b37b91",
    "htmlContent": "<!DOCTYPE html><html><head><title>测试页面</title></head><body><h1>Hello World</h1><p>这是一个测试页面</p></body></html>",
    "timestamp": "1718352000000",
    "signature": "a1b2c3d4e5f6789012345678901234567890abcdef1234567890abcdef123456",
    "filename": "test-page"
  }'
```

### 响应格式

#### 成功响应
```json
{
  "success": true,
  "message": "HTML文件生成成功",
  "code": 200,
  "result": {
    "htmlUrl": "https://www.aigcview.com/api/aigc/html/aigc_1718352000000_abc12345.html",
    "qrcodeUrl": "https://www.aigcview.com/api/aigc/qrcode/qr_1718352000000_def67890.png",
    "filename": "aigc_1718352000000_abc12345.html",
    "generateTime": "2025-06-14T10:00:00.000+00:00"
  },
  "timestamp": 1718352000000
}
```

#### 响应字段说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| htmlUrl | String | HTML文件访问地址 |
| qrcodeUrl | String | 二维码图片地址 |
| filename | String | 生成的文件名 |
| generateTime | String | 生成时间（ISO 8601格式） |

### 错误响应

#### 1. 参数缺失
```json
{
  "success": false,
  "message": "必要参数不能为空",
  "code": 500,
  "result": null,
  "timestamp": 1718352000000
}
```

#### 2. 时间戳过期
```json
{
  "success": false,
  "message": "请求已过期，请重新发送",
  "code": 500,
  "result": null,
  "timestamp": 1718352000000
}
```

#### 3. 签名验证失败
```json
{
  "success": false,
  "message": "签名验证失败",
  "code": 500,
  "result": null,
  "timestamp": 1718352000000
}
```

#### 4. HTML内容不安全
```json
{
  "success": false,
  "message": "HTML内容包含不安全元素: 包含危险的HTML标签（script、iframe、object等）",
  "code": 500,
  "result": null,
  "timestamp": 1718352000000
}
```

### 文件访问机制

#### HTML文件访问
- **访问地址**: `GET /api/aigc/html/{filename}`
- **Content-Type**: `text/html;charset=UTF-8`
- **缓存策略**: `Cache-Control: public, max-age=3600`（1小时）
- **文件存储**: 服务器本地存储，支持直接访问

#### 二维码文件访问
- **访问地址**: `GET /api/aigc/qrcode/{filename}`
- **Content-Type**: `image/png`
- **缓存策略**: `Cache-Control: public, max-age=86400`（24小时）
- **图片规格**: 300x300像素，PNG格式

---

## 💻 代码示例

### Java示例

```java
import java.security.MessageDigest;
import java.util.HashMap;
import java.util.Map;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import com.fasterxml.jackson.databind.ObjectMapper;

public class AigcApiClient {
    private String baseUrl = "https://www.aigcview.com";
    private String apiKey = "ak_your_api_key_here";
    
    /**
     * 生成签名
     */
    private String generateSignature(String apiKey, String content, String timestamp) {
        try {
            String signString = apiKey + content + timestamp;
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] hash = digest.digest(signString.getBytes("UTF-8"));
            
            StringBuilder hexString = new StringBuilder();
            for (byte b : hash) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            return hexString.toString();
        } catch (Exception e) {
            throw new RuntimeException("签名生成失败", e);
        }
    }
    
    /**
     * 验证API-Key
     */
    public Map<String, Object> verifyApiKey(String pluginName) {
        try {
            String url = baseUrl + "/api/aigc/verify-apikey";
            
            // 构建请求参数
            StringBuilder params = new StringBuilder();
            params.append("apiKey=").append(apiKey);
            if (pluginName != null && !pluginName.isEmpty()) {
                params.append("&pluginName=").append(pluginName);
            }
            
            // 发送请求
            CloseableHttpClient client = HttpClients.createDefault();
            HttpPost post = new HttpPost(url);
            post.setHeader("Content-Type", "application/x-www-form-urlencoded");
            post.setEntity(new StringEntity(params.toString()));
            
            // 处理响应
            // ... 响应处理代码
            
            return new HashMap<>();
        } catch (Exception e) {
            throw new RuntimeException("API调用失败", e);
        }
    }
    
    /**
     * 生成HTML文件
     */
    public Map<String, Object> generateHtml(String htmlContent, String filename) {
        try {
            String timestamp = String.valueOf(System.currentTimeMillis());
            String signature = generateSignature(apiKey, htmlContent, timestamp);
            
            // 构建请求数据
            Map<String, Object> requestData = new HashMap<>();
            requestData.put("apiKey", apiKey);
            requestData.put("htmlContent", htmlContent);
            requestData.put("timestamp", timestamp);
            requestData.put("signature", signature);
            if (filename != null && !filename.isEmpty()) {
                requestData.put("filename", filename);
            }
            
            // 发送请求
            String url = baseUrl + "/api/aigc/generate-html";
            CloseableHttpClient client = HttpClients.createDefault();
            HttpPost post = new HttpPost(url);
            post.setHeader("Content-Type", "application/json");
            
            ObjectMapper mapper = new ObjectMapper();
            String jsonData = mapper.writeValueAsString(requestData);
            post.setEntity(new StringEntity(jsonData, "UTF-8"));
            
            // 处理响应
            // ... 响应处理代码
            
            return new HashMap<>();
        } catch (Exception e) {
            throw new RuntimeException("HTML生成失败", e);
        }
    }
}
```

### Python示例

```python
import hashlib
import time
import requests
import json

class AigcApiClient:
    def __init__(self, base_url, api_key):
        """
        初始化API客户端
        :param base_url: API基础地址
        :param api_key: API密钥
        """
        self.base_url = base_url
        self.api_key = api_key

    def generate_signature(self, html_content, timestamp):
        """
        生成请求签名
        :param html_content: HTML内容
        :param timestamp: 时间戳
        :return: 签名字符串
        """
        sign_string = self.api_key + html_content + timestamp
        return hashlib.sha256(sign_string.encode('utf-8')).hexdigest()

    def verify_api_key(self, plugin_name=None):
        """
        验证API-Key
        :param plugin_name: 插件名称（可选）
        :return: 验证结果
        """
        url = f"{self.base_url}/api/aigc/verify-apikey"

        data = {"apiKey": self.api_key}
        if plugin_name:
            data["pluginName"] = plugin_name

        try:
            response = requests.post(url, data=data, timeout=30)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            return {
                "success": False,
                "message": f"请求失败: {str(e)}",
                "code": 500
            }

    def generate_html(self, html_content, filename=None):
        """
        生成HTML文件
        :param html_content: HTML内容
        :param filename: 自定义文件名（可选）
        :return: 生成结果
        """
        timestamp = str(int(time.time() * 1000))
        signature = self.generate_signature(html_content, timestamp)

        url = f"{self.base_url}/api/aigc/generate-html"
        data = {
            "apiKey": self.api_key,
            "htmlContent": html_content,
            "timestamp": timestamp,
            "signature": signature
        }

        if filename:
            data["filename"] = filename

        try:
            response = requests.post(
                url,
                json=data,
                headers={"Content-Type": "application/json"},
                timeout=30
            )
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            return {
                "success": False,
                "message": f"请求失败: {str(e)}",
                "code": 500
            }

# 使用示例
if __name__ == "__main__":
    # 初始化客户端
    client = AigcApiClient(
        base_url="https://www.aigcview.com",
        api_key="ak_your_api_key_here"
    )

    # 1. 验证API-Key
    print("=== 验证API-Key ===")
    result = client.verify_api_key()
    print(json.dumps(result, indent=2, ensure_ascii=False))

    # 2. 验证API-Key并调用插件
    print("\n=== 验证API-Key并调用插件 ===")
    result = client.verify_api_key("小红书发布")
    print(json.dumps(result, indent=2, ensure_ascii=False))

    # 3. 生成HTML文件
    print("\n=== 生成HTML文件 ===")
    html_content = """
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>智界Aigc测试页面</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 40px; }
            .container { max-width: 800px; margin: 0 auto; }
            .header { text-align: center; color: #333; }
            .content { margin-top: 30px; line-height: 1.6; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>智界Aigc HTML生成测试</h1>
                <p>这是通过API生成的HTML页面</p>
            </div>
            <div class="content">
                <h2>功能特点</h2>
                <ul>
                    <li>安全的HTML内容检查</li>
                    <li>自动生成二维码</li>
                    <li>支持自定义文件名</li>
                    <li>完整的错误处理</li>
                </ul>
                <h2>生成时间</h2>
                <p>页面生成时间：{}</p>
            </div>
        </div>
    </body>
    </html>
    """.format(time.strftime("%Y-%m-%d %H:%M:%S"))

    result = client.generate_html(html_content, "test-page")
    print(json.dumps(result, indent=2, ensure_ascii=False))
```

### JavaScript/Node.js示例

```javascript
const crypto = require('crypto');
const axios = require('axios');

class AigcApiClient {
    constructor(baseUrl, apiKey) {
        this.baseUrl = baseUrl;
        this.apiKey = apiKey;
    }

    /**
     * 生成签名
     */
    generateSignature(htmlContent, timestamp) {
        const signString = this.apiKey + htmlContent + timestamp;
        return crypto.createHash('sha256').update(signString, 'utf8').digest('hex');
    }

    /**
     * 验证API-Key
     */
    async verifyApiKey(pluginName = null) {
        const url = `${this.baseUrl}/api/aigc/verify-apikey`;

        const data = new URLSearchParams();
        data.append('apiKey', this.apiKey);
        if (pluginName) {
            data.append('pluginName', pluginName);
        }

        try {
            const response = await axios.post(url, data, {
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded'
                },
                timeout: 30000
            });
            return response.data;
        } catch (error) {
            return {
                success: false,
                message: `请求失败: ${error.message}`,
                code: 500
            };
        }
    }

    /**
     * 生成HTML文件
     */
    async generateHtml(htmlContent, filename = null) {
        const timestamp = Date.now().toString();
        const signature = this.generateSignature(htmlContent, timestamp);

        const url = `${this.baseUrl}/api/aigc/generate-html`;
        const data = {
            apiKey: this.apiKey,
            htmlContent: htmlContent,
            timestamp: timestamp,
            signature: signature
        };

        if (filename) {
            data.filename = filename;
        }

        try {
            const response = await axios.post(url, data, {
                headers: {
                    'Content-Type': 'application/json'
                },
                timeout: 30000
            });
            return response.data;
        } catch (error) {
            return {
                success: false,
                message: `请求失败: ${error.message}`,
                code: 500
            };
        }
    }
}

// 使用示例
async function main() {
    const client = new AigcApiClient(
        'https://www.aigcview.com',
        'ak_your_api_key_here'
    );

    try {
        // 1. 验证API-Key
        console.log('=== 验证API-Key ===');
        const verifyResult = await client.verifyApiKey();
        console.log(JSON.stringify(verifyResult, null, 2));

        // 2. 验证API-Key并调用插件
        console.log('\n=== 验证API-Key并调用插件 ===');
        const pluginResult = await client.verifyApiKey('小红书发布');
        console.log(JSON.stringify(pluginResult, null, 2));

        // 3. 生成HTML文件
        console.log('\n=== 生成HTML文件 ===');
        const htmlContent = `
        <!DOCTYPE html>
        <html lang="zh-CN">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>智界Aigc Node.js测试</title>
            <style>
                body {
                    font-family: 'Microsoft YaHei', Arial, sans-serif;
                    margin: 0;
                    padding: 40px;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                }
                .container {
                    max-width: 800px;
                    margin: 0 auto;
                    background: rgba(255,255,255,0.1);
                    padding: 30px;
                    border-radius: 10px;
                    backdrop-filter: blur(10px);
                }
                .header { text-align: center; margin-bottom: 30px; }
                .content { line-height: 1.8; }
                .highlight {
                    background: rgba(255,255,255,0.2);
                    padding: 15px;
                    border-radius: 5px;
                    margin: 15px 0;
                }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>🚀 智界Aigc API测试</h1>
                    <p>Node.js客户端生成的HTML页面</p>
                </div>
                <div class="content">
                    <h2>✨ 技术特性</h2>
                    <div class="highlight">
                        <ul>
                            <li>🔐 SHA-256签名验证</li>
                            <li>🛡️ 多层安全防护</li>
                            <li>📱 二维码自动生成</li>
                            <li>⚡ 高性能处理</li>
                            <li>🎨 响应式设计</li>
                        </ul>
                    </div>
                    <h2>📊 生成信息</h2>
                    <div class="highlight">
                        <p><strong>生成时间：</strong>${new Date().toLocaleString('zh-CN')}</p>
                        <p><strong>客户端：</strong>Node.js</p>
                        <p><strong>API版本：</strong>v1.0</p>
                    </div>
                </div>
            </div>
        </body>
        </html>
        `;

        const htmlResult = await client.generateHtml(htmlContent, 'nodejs-test');
        console.log(JSON.stringify(htmlResult, null, 2));

    } catch (error) {
        console.error('执行失败:', error);
    }
}

// 运行示例
main();
```

### PHP示例

```php
<?php
class AigcApiClient {
    private $baseUrl;
    private $apiKey;

    public function __construct($baseUrl, $apiKey) {
        $this->baseUrl = $baseUrl;
        $this->apiKey = $apiKey;
    }

    /**
     * 生成签名
     */
    private function generateSignature($htmlContent, $timestamp) {
        $signString = $this->apiKey . $htmlContent . $timestamp;
        return hash('sha256', $signString);
    }

    /**
     * 验证API-Key
     */
    public function verifyApiKey($pluginName = null) {
        $url = $this->baseUrl . '/api/aigc/verify-apikey';

        $data = array('apiKey' => $this->apiKey);
        if ($pluginName) {
            $data['pluginName'] = $pluginName;
        }

        $options = array(
            'http' => array(
                'header' => "Content-Type: application/x-www-form-urlencoded\r\n",
                'method' => 'POST',
                'content' => http_build_query($data),
                'timeout' => 30
            )
        );

        $context = stream_context_create($options);
        $result = file_get_contents($url, false, $context);

        if ($result === FALSE) {
            return array(
                'success' => false,
                'message' => '请求失败',
                'code' => 500
            );
        }

        return json_decode($result, true);
    }

    /**
     * 生成HTML文件
     */
    public function generateHtml($htmlContent, $filename = null) {
        $timestamp = (string)(time() * 1000);
        $signature = $this->generateSignature($htmlContent, $timestamp);

        $url = $this->baseUrl . '/api/aigc/generate-html';
        $data = array(
            'apiKey' => $this->apiKey,
            'htmlContent' => $htmlContent,
            'timestamp' => $timestamp,
            'signature' => $signature
        );

        if ($filename) {
            $data['filename'] = $filename;
        }

        $options = array(
            'http' => array(
                'header' => "Content-Type: application/json\r\n",
                'method' => 'POST',
                'content' => json_encode($data),
                'timeout' => 30
            )
        );

        $context = stream_context_create($options);
        $result = file_get_contents($url, false, $context);

        if ($result === FALSE) {
            return array(
                'success' => false,
                'message' => '请求失败',
                'code' => 500
            );
        }

        return json_decode($result, true);
    }
}

// 使用示例
$client = new AigcApiClient(
    'https://www.aigcview.com',
    'ak_your_api_key_here'
);

// 1. 验证API-Key
echo "=== 验证API-Key ===\n";
$result = $client->verifyApiKey();
echo json_encode($result, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n";

// 2. 验证API-Key并调用插件
echo "\n=== 验证API-Key并调用插件 ===\n";
$result = $client->verifyApiKey('小红书发布');
echo json_encode($result, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n";

// 3. 生成HTML文件
echo "\n=== 生成HTML文件 ===\n";
$htmlContent = '
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智界Aigc PHP测试</title>
    <style>
        body {
            font-family: "Microsoft YaHei", Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .content { line-height: 1.6; color: #555; }
        .info-box {
            background: #e7f3ff;
            padding: 15px;
            border-left: 4px solid #007bff;
            margin: 20px 0;
        }
        .success { color: #28a745; font-weight: bold; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🐘 智界Aigc PHP客户端</h1>
            <p>PHP语言生成的HTML页面示例</p>
        </div>
        <div class="content">
            <h2>🔧 技术栈</h2>
            <div class="info-box">
                <ul>
                    <li><strong>后端语言：</strong>PHP</li>
                    <li><strong>HTTP客户端：</strong>file_get_contents + stream_context</li>
                    <li><strong>签名算法：</strong>SHA-256</li>
                    <li><strong>数据格式：</strong>JSON</li>
                </ul>
            </div>

            <h2>📈 API功能</h2>
            <div class="info-box">
                <p class="success">✅ API-Key验证</p>
                <p class="success">✅ 插件调用扣费</p>
                <p class="success">✅ HTML文件生成</p>
                <p class="success">✅ 二维码生成</p>
                <p class="success">✅ 安全内容检查</p>
            </div>

            <h2>⏰ 生成信息</h2>
            <div class="info-box">
                <p><strong>生成时间：</strong>' . date('Y-m-d H:i:s') . '</p>
                <p><strong>PHP版本：</strong>' . PHP_VERSION . '</p>
                <p><strong>服务器时间：</strong>' . date('c') . '</p>
            </div>
        </div>
    </div>
</body>
</html>
';

$result = $client->generateHtml($htmlContent, 'php-test');
echo json_encode($result, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n";
?>
```

---

## 🛠️ 错误处理与最佳实践

### 错误处理策略

#### 1. 网络错误处理
```python
import requests
from requests.adapters import HTTPAdapter
from requests.packages.urllib3.util.retry import Retry

def create_session_with_retries():
    session = requests.Session()
    retry_strategy = Retry(
        total=3,
        status_forcelist=[429, 500, 502, 503, 504],
        method_whitelist=["HEAD", "GET", "OPTIONS", "POST"]
    )
    adapter = HTTPAdapter(max_retries=retry_strategy)
    session.mount("http://", adapter)
    session.mount("https://", adapter)
    return session

# 使用示例
session = create_session_with_retries()
response = session.post(url, json=data, timeout=30)
```

#### 2. 响应验证
```python
def validate_response(response_data):
    """验证API响应数据"""
    if not isinstance(response_data, dict):
        raise ValueError("响应数据格式错误")

    if 'success' not in response_data:
        raise ValueError("响应缺少success字段")

    if not response_data['success']:
        error_msg = response_data.get('message', '未知错误')
        raise APIError(f"API调用失败: {error_msg}")

    return response_data.get('result')
```

#### 3. 签名验证失败处理
```java
public class SignatureValidator {
    public static boolean validateSignature(String apiKey, String content,
                                          String timestamp, String signature) {
        try {
            // 检查时间戳是否过期
            long requestTime = Long.parseLong(timestamp);
            long currentTime = System.currentTimeMillis();
            if (Math.abs(currentTime - requestTime) > 300000) { // 5分钟
                throw new SignatureException("时间戳已过期");
            }

            // 生成期望的签名
            String expectedSignature = SecurityUtil.generateSignature(
                apiKey, content, timestamp);

            return signature.equals(expectedSignature);
        } catch (Exception e) {
            log.error("签名验证失败: {}", e.getMessage());
            return false;
        }
    }
}
```

### 最佳实践

#### 1. API-Key安全管理
```bash
# 环境变量方式存储API-Key
export AIGC_API_KEY="ak_your_api_key_here"
export AIGC_BASE_URL="https://www.aigcview.com"

# 配置文件方式（注意加密存储）
# config.json
{
  "aigc": {
    "apiKey": "ak_your_api_key_here",
    "baseUrl": "https://www.aigcview.com",
    "timeout": 30000
  }
}
```

#### 2. 请求频率控制
```python
import time
from threading import Lock

class RateLimiter:
    def __init__(self, max_requests_per_minute=60):
        self.max_requests = max_requests_per_minute
        self.requests = []
        self.lock = Lock()

    def wait_if_needed(self):
        with self.lock:
            now = time.time()
            # 清理1分钟前的请求记录
            self.requests = [req_time for req_time in self.requests
                           if now - req_time < 60]

            if len(self.requests) >= self.max_requests:
                sleep_time = 60 - (now - self.requests[0])
                if sleep_time > 0:
                    time.sleep(sleep_time)

            self.requests.append(now)

# 使用示例
rate_limiter = RateLimiter(max_requests_per_minute=50)

def call_api_with_rate_limit():
    rate_limiter.wait_if_needed()
    # 执行API调用
    return client.verify_api_key()
```

#### 3. 缓存策略
```python
import hashlib
import json
from functools import wraps

def cache_api_response(expire_seconds=300):
    """API响应缓存装饰器"""
    cache = {}

    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # 生成缓存键
            cache_key = hashlib.md5(
                json.dumps([args, kwargs], sort_keys=True).encode()
            ).hexdigest()

            now = time.time()

            # 检查缓存
            if cache_key in cache:
                cached_data, cached_time = cache[cache_key]
                if now - cached_time < expire_seconds:
                    return cached_data

            # 执行函数并缓存结果
            result = func(*args, **kwargs)
            cache[cache_key] = (result, now)

            return result
        return wrapper
    return decorator

# 使用示例
@cache_api_response(expire_seconds=600)  # 缓存10分钟
def verify_api_key_cached(api_key):
    return client.verify_api_key()
```

#### 4. 日志记录
```python
import logging
import json
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('aigc_api.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger('AigcApiClient')

class AigcApiClientWithLogging:
    def __init__(self, base_url, api_key):
        self.base_url = base_url
        self.api_key = api_key[:10] + "***"  # 日志中隐藏完整API-Key

    def verify_api_key(self, plugin_name=None):
        start_time = datetime.now()

        try:
            logger.info(f"开始验证API-Key，插件: {plugin_name}")

            # 执行API调用
            result = self._do_verify_api_key(plugin_name)

            duration = (datetime.now() - start_time).total_seconds()
            logger.info(f"API-Key验证成功，耗时: {duration:.2f}秒")

            return result

        except Exception as e:
            duration = (datetime.now() - start_time).total_seconds()
            logger.error(f"API-Key验证失败，耗时: {duration:.2f}秒，错误: {str(e)}")
            raise
```

---

## ❓ 常见问题 (FAQ)

### Q1: 如何获取API-Key？
**A**: 请登录智界Aigc系统，在用户中心的API管理页面生成您的专属API-Key。每个用户可以生成多个API-Key用于不同的应用场景。

### Q2: API-Key有使用期限吗？
**A**: API-Key默认永久有效，但建议定期更换以确保安全。您可以在用户中心随时禁用或重新生成API-Key。

### Q3: 签名验证总是失败怎么办？
**A**: 请检查以下几点：
- 确保签名算法正确：`SHA256(apiKey + htmlContent + timestamp)`
- 检查时间戳格式：必须是13位Unix毫秒时间戳
- 确保参数拼接顺序正确
- 检查字符编码：统一使用UTF-8

### Q4: HTML内容安全检查太严格怎么办？
**A**: 我们的安全检查是为了保护系统和用户安全。如果您的HTML内容被误判，请：
- 移除所有JavaScript代码
- 避免使用外部资源引用
- 不要使用危险的HTML标签
- 如有特殊需求，请联系技术支持

### Q5: 生成的HTML文件可以永久访问吗？
**A**: 生成的HTML文件会在服务器保存一定时间（通常为30天），建议您及时下载保存。如需长期保存，请联系我们升级存储服务。

### Q6: 插件调用扣费是如何计算的？
**A**: 插件调用费用由插件创作者设定，在插件商城中可以查看具体价格。扣费采用预扣费模式，调用成功后才会真正扣除费用。

### Q7: 如何处理并发请求？
**A**: 建议：
- 实现请求队列机制
- 使用连接池复用HTTP连接
- 设置合理的超时时间
- 实现重试机制

### Q8: API调用失败如何排查？
**A**: 排查步骤：
1. 检查网络连接
2. 验证API-Key有效性
3. 检查请求参数格式
4. 查看错误响应消息
5. 检查服务器日志
6. 联系技术支持

---

## 📞 技术支持

### 联系方式
- **技术支持邮箱**: <EMAIL>
- **开发文档**: https://www.aigcview.com/docs
- **API测试工具**: https://www.aigcview.com/api-test
- **Swagger文档**: https://www.aigcview.com/jeecg-boot/doc.html

### 支持时间
- **工作日**: 9:00 - 18:00
- **紧急问题**: 24小时内响应
- **一般问题**: 1个工作日内响应

### 版本更新
- **当前版本**: v1.0
- **更新频率**: 每月定期更新
- **更新通知**: 邮件 + 系统通知

---

**文档版本**: v1.0
**最后更新**: 2025-06-14
**维护团队**: 智界Aigc开发组
**文档状态**: ✅ 生产就绪
