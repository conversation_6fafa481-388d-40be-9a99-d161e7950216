package org.jeecg.modules.jianying.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

/**
 * 添加文本样式请求参数
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AddTextStyleRequest extends BaseJianyingRequest {
    
    @ApiModelProperty(value = "关键词（必填）", required = true, example = "爱心")
    @NotBlank(message = "keyword不能为空")
    @JsonProperty("keyword")
    private String zjKeyword;

    @ApiModelProperty(value = "文本内容（必填）", required = true, example = "这是文本内容")
    @NotBlank(message = "text不能为空")
    @JsonProperty("text")
    private String zjText;

    @ApiModelProperty(value = "字体大小，默认15", example = "15")
    @JsonProperty("font_size")
    private Integer zjFontSize;

    @ApiModelProperty(value = "关键词颜色，默认：#ff7100", example = "#ff7100")
    @JsonProperty("keyword_color")
    private String zjKeywordColor;

    @ApiModelProperty(value = "关键词字体大小，默认15", example = "15")
    @JsonProperty("keyword_font_size")
    private Integer zjKeywordFontSize;
    
    @Override
    public String getSummary() {
        return "AddTextStyleRequest{" +
               "accessKey=" + (getAccessKey() != null && getAccessKey().length() > 10 ? 
                              getAccessKey().substring(0, 10) + "***" : "null") +
               ", keyword=" + zjKeyword +
               ", text=" + (zjText != null && zjText.length() > 20 ? 
                           zjText.substring(0, 20) + "***" : zjText) +
               "}";
    }
}
