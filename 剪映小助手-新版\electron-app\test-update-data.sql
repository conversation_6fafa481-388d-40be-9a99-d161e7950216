-- 测试版本更新数据
-- 用于测试Electron应用的版本检查功能

-- 清除现有数据
DELETE FROM aigc_version_control WHERE program_type = 'desktop';

-- 插入测试数据

-- 1. 当前版本（22.3.27）
INSERT INTO `aigc_version_control` VALUES
('test-1', 'desktop', '22.3.27', '当前版本\n- 基础功能实现\n- 剪映草稿导入\n- 文件管理功能', 'https://aigcview.com/download/desktop/v22.3.27', NOW(), 2, 1, 'system', NOW(), NULL, NULL, NULL);

-- 2. 可选更新版本（22.4.0）
INSERT INTO `aigc_version_control` VALUES
('test-2', 'desktop', '22.4.0', '功能优化更新\n- 优化用户界面\n- 修复已知问题\n- 提升下载性能\n- 增加错误提示', 'https://aigcview.com/download/desktop/v22.4.0', NOW(), 1, 1, 'system', NOW(), NULL, NULL, NULL);

-- 3. 强制更新版本（23.0.0）- 注释掉，需要时取消注释
-- INSERT INTO `aigc_version_control` VALUES
-- ('test-3', 'desktop', '23.0.0', '安全更新：修复重要安全漏洞\n- 修复文件上传安全问题\n- 更新依赖库版本\n- 加强数据验证\n- 建议立即更新', 'https://aigcview.com/download/desktop/v23.0.0', NOW(), 1, 1, 'system', NOW(), NULL, NULL, NULL);

-- 查看插入的数据
SELECT * FROM aigc_version_control WHERE program_type = 'desktop' ORDER BY version_number DESC;
