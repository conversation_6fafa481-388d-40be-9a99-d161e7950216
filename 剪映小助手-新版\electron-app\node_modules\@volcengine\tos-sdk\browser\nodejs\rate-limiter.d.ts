/// <reference types="node" />
import { Readable, Transform } from 'stream';
import { IRateLimiter } from '../interface';
export interface DefaultRateLimiter {
    rate: number;
    capacity: number;
    currentAmount: number;
    lastConsumeTime: number;
}
/**
 *
 * @param capacity  minValue 10KB. unit byte
 * @param rate   minValue 1KB. unit byte/s
 * @returns
 */
export declare function createDefaultRateLimiter(capacity: number, rate: number): IRateLimiter;
export declare function createRateLimiterStream(stream: NodeJS.ReadableStream | Readable, rateLimiter: IRateLimiter): Transform;
export declare function wait(milliseconds: number): Promise<unknown>;
