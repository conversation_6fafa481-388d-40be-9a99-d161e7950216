/// <reference types="node" />
declare const hmacSha256: (key: string, message: string, decoding?: "base64" | "hex" | undefined) => string, hashSha256: (message: string, decoding?: "base64" | "hex" | undefined) => string, hashMd5: (message: string | Buffer, decoding?: "base64" | "hex" | undefined) => string, parse: (str: string, encoding: 'utf-8' | 'base64' | 'hex') => string, stringify: (str: string, decoding: 'utf-8' | 'base64' | 'hex') => string;
export { hmacSha256, hashSha256, hashMd5, parse, stringify };
