<template>
  <a-card :bordered="false">
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline" @keyup.enter.native="searchQuery">
        <a-row :gutter="24">
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="推荐人ID">
              <a-input placeholder="请输入推荐人ID" v-model="queryParam.referrerId"></a-input>
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="被推荐人ID">
              <a-input placeholder="请输入被推荐人ID" v-model="queryParam.refereeId"></a-input>
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="推荐码">
              <a-input placeholder="请输入推荐码" v-model="queryParam.referralCode"></a-input>
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-item label="状态">
              <a-select placeholder="请选择状态" v-model="queryParam.status" allowClear>
                <a-select-option :value="1">待确认</a-select-option>
                <a-select-option :value="2">已确认</a-select-option>
                <a-select-option :value="3">已奖励</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <span class="table-page-search-submitButtons">
              <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
              <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    
    <!-- 操作按钮区域 -->
    <div class="table-operator">
      <a-button @click="handleAdd" type="primary" icon="plus">新增</a-button>
      <a-button type="primary" icon="download" @click="handleExportXls('推荐关系')">导出</a-button>
      <a-dropdown v-if="selectedRowKeys.length > 0">
        <a-menu slot="overlay">
          <a-menu-item key="1" @click="batchDel"><a-icon type="delete"/>删除</a-menu-item>
        </a-menu>
        <a-button style="margin-left: 8px"> 批量操作 <a-icon type="down" /></a-button>
      </a-dropdown>
    </div>

    <!-- table区域-begin -->
    <div>
      <div class="ant-alert ant-alert-info" style="margin-bottom: 16px;">
        <i class="anticon anticon-info-circle ant-alert-icon"></i> 已选择 <a style="font-weight: 600">{{ selectedRowKeys.length }}</a>项
        <a style="margin-left: 24px" @click="onClearSelected">清空</a>
      </div>

      <a-table
        ref="table"
        size="middle"
        :scroll="{x:true}"
        bordered
        rowKey="id"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="ipagination"
        :loading="loading"
        :rowSelection="{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}"
        class="j-table-force-nowrap"
        @change="handleTableChange">

        <span slot="action" slot-scope="text, record">
          <a @click="handleEdit(record)">编辑</a>
          <a-divider type="vertical" />
          <a-dropdown>
            <a class="ant-dropdown-link">更多 <a-icon type="down" /></a>
            <a-menu slot="overlay">
              <a-menu-item>
                <a @click="handleDetail(record)">详情</a>
              </a-menu-item>
              <a-menu-item v-if="record.status === 1">
                <a @click="handleConfirm(record)">确认推荐</a>
              </a-menu-item>
              <a-menu-item v-if="record.status === 2">
                <a @click="handleMarkRewarded(record)">标记已奖励</a>
              </a-menu-item>
              <a-menu-item>
                <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
                  <a>删除</a>
                </a-popconfirm>
              </a-menu-item>
            </a-menu>
          </a-dropdown>
        </span>

      </a-table>
    </div>

    <aicg-user-referral-modal ref="modalForm" @ok="modalFormOk"></aicg-user-referral-modal>
  </a-card>
</template>

<script>

  import '@/assets/less/TableExpand.less'
  import { mixinDevice } from '@/utils/mixin'
  import { JeecgListMixin } from '@/mixins/JeecgListMixin'
  import AicgUserReferralModal from './modules/AicgUserReferralModal.vue'

  export default {
    name: 'AicgUserReferralList',
    mixins:[JeecgListMixin, mixinDevice],
    components: {
      AicgUserReferralModal
    },
    data () {
      return {
        description: '用户推荐关系管理页面',
        // 表头
        columns: [
          {
            title: '#',
            dataIndex: '',
            key:'rowIndex',
            width:60,
            align:"center",
            customRender:function (t,r,index) {
              return parseInt(index)+1;
            }
          },
          {
            title:'推荐人ID',
            align:"center",
            dataIndex: 'referrerId'
          },
          {
            title:'被推荐人ID',
            align:"center",
            dataIndex: 'refereeId'
          },
          {
            title:'推荐码',
            align:"center",
            dataIndex: 'referralCode'
          },
          {
            title:'注册时间',
            align:"center",
            dataIndex: 'registerTime'
          },
          {
            title:'首次充值时间',
            align:"center",
            dataIndex: 'firstRechargeTime'
          },
          {
            title:'首次充值金额',
            align:"center",
            dataIndex: 'firstRechargeAmount',
            customRender: function (text) {
              return text ? `¥${text}` : '-'
            }
          },
          {
            title:'状态',
            align:"center",
            dataIndex: 'status',
            customRender: function (text) {
              const statusMap = {1: '待确认', 2: '已确认', 3: '已奖励'}
              const colorMap = {1: 'orange', 2: 'blue', 3: 'green'}
              return `<span style="color: ${colorMap[text]}">${statusMap[text] || text}</span>`
            },
            scopedSlots: { customRender: 'htmlSlot' }
          },
          {
            title: '操作',
            dataIndex: 'action',
            align:"center",
            fixed:"right",
            width:147,
            scopedSlots: { customRender: 'action' }
          }
        ],
        url: {
          list: "/demo/referral/list",
          delete: "/demo/referral/delete",
          deleteBatch: "/demo/referral/deleteBatch",
          exportXlsUrl: "/demo/referral/exportXls",
          importExcelUrl: "demo/referral/importExcel",
        },
        dictOptions:{},
        superFieldList:[],
      }
    },
    created() {
      this.getSuperFieldList();
    },
    computed: {
      importExcelUrl: function(){
        return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`;
      },
    },
    methods: {
      initDictConfig(){
      },
      getSuperFieldList(){
        let fieldList=[];
        fieldList.push({type:'string',value:'referrerId',text:'推荐人ID'})
        fieldList.push({type:'string',value:'refereeId',text:'被推荐人ID'})
        fieldList.push({type:'string',value:'referralCode',text:'推荐码'})
        fieldList.push({type:'Date',value:'registerTime',text:'注册时间'})
        fieldList.push({type:'Date',value:'firstRechargeTime',text:'首次充值时间'})
        fieldList.push({type:'BigDecimal',value:'firstRechargeAmount',text:'首次充值金额'})
        fieldList.push({type:'int',value:'status',text:'状态'})
        this.superFieldList = fieldList
      },
      handleConfirm(record) {
        this.$confirm({
          title: '确认推荐',
          content: '确定要确认该推荐关系吗？',
          onOk: () => {
            this.$http.post(`/demo/referral/confirm?refereeId=${record.refereeId}&rechargeAmount=${record.firstRechargeAmount || 0}`).then((res) => {
              if (res.success) {
                this.$message.success('确认成功')
                this.loadData()
              } else {
                this.$message.error(res.message)
              }
            })
          }
        })
      },
      handleMarkRewarded(record) {
        this.$confirm({
          title: '标记已奖励',
          content: '确定要标记该推荐关系为已奖励吗？',
          onOk: () => {
            this.$http.post(`/demo/referral/markRewarded?id=${record.id}`).then((res) => {
              if (res.success) {
                this.$message.success('标记成功')
                this.loadData()
              } else {
                this.$message.error(res.message)
              }
            })
          }
        })
      }
    }
  }
</script>
<style scoped>
  @import '~@assets/less/common.less';
</style>
