"use strict";function e(e){return e&&"object"==typeof e&&"default"in e?e.default:e}Object.defineProperty(exports,"__esModule",{value:!0});var t,a=e(require("axios")),o=require("stream"),n=e(require("lodash/get")),i=e(require("lodash/set")),r=e(require("qs")),s=e(require("fs")),c=e(require("fs/promises")),l=require("util"),p=require("tos-crc64-js"),u=e(require("debug")),d=e(require("crypto-js/md5")),h=e(require("crypto-js/enc-base64")),m=e(require("crypto-js/enc-hex")),f=e(require("crypto-js/enc-utf8")),y=e(require("crypto")),g=e(require("path")),x=e(require("lodash/cloneDeep")),b=e(require("http")),k=e(require("https")),v=e(require("os"));class T extends Error{constructor(e){const{data:t}=e;super(t.Message),this.code=void 0,this.data=void 0,this.statusCode=void 0,this.headers=void 0,this.requestId=void 0,this.id2=void 0,Object.setPrototypeOf(this,T.prototype),this.data=t,this.code=t.Code,this.statusCode=e.status,this.headers=e.headers,this.requestId=e.headers["x-tos-request-id"],this.id2=e.headers["x-tos-id-2"]}}(t=exports.TosServerCode||(exports.TosServerCode={})).NoSuchBucket="NoSuchBucket",t.NoSuchKey="NoSuchKey",t.AccessDenied="AccessDenied",t.MalformedAcl="MalformedAclError",t.UnexpectedContent="UnexpectedContent",t.InvalidRequest="InvalidRequest",t.MissingSecurityHeader="MissingSecurityHeader",t.InvalidArgument="InvalidArgument",t.EntityTooSmall="EntityTooSmall",t.InvalidBucketName="InvalidBucketName",t.BucketNotEmpty="BucketNotEmpty",t.TooManyBuckets="TooManyBuckets",t.BucketAlreadyExists="BucketAlreadyExists",t.MalformedBody="MalformedBody",t.NoSuchLifecycleConfiguration="NoSuchLifecycleConfiguration",t.ReplicationConfigurationNotFound="ReplicationConfigurationNotFoundError",t.InvalidLocationConstraint="InvalidLocationConstraint",t.AuthorizationQueryParametersError="AuthorizationQueryParametersError",t.RequestTimeTooSkewed="RequestTimeTooSkewed",t.SignatureDoesNotMatch="SignatureDoesNotMatch",t.RequestedRangeNotSatisfiable="Requested Range Not Satisfiable",t.PreconditionFailed="PreconditionFailed",t.BadDigest="BadDigest",t.InvalidDigest="InvalidDigest",t.EntityTooLarge="EntityTooLarge",t.UnImplemented="UnImplemented",t.MethodNotAllowed="MethodNotAllowed",t.InvalidAccessKeyId="InvalidAccessKeyId",t.InvalidSecurityToken="InvalidSecurityToken",t.ContentSHA256Mismatch="ContentSHA256Mismatch",t.ExceedQPSLimit="ExceedQPSLimit",t.ExceedRateLimit="ExceedRateLimit",t.NoSuchCORSConfiguration="NoSuchCORSConfiguration",t.NoSuchMirrorConfiguration="NoSuchMirrorConfiguration",t.NoSuchWebsiteConfiguration="NoSuchWebsiteConfiguration",t.MissingRequestBody="MissingRequestBodyError",t.BucketAlreadyOwnedByYou="BucketAlreadyOwnedByYou",t.NoSuchBucketPolicy="NoSuchBucketPolicy",t.PolicyTooLarge="PolicyTooLarge",t.MalformedPolicy="MalformedPolicy",t.InvalidKey="InvalidKey",t.MirrorFailed="MirrorFailed",t.Timeout="Timeout",t.OffsetNotMatched="OffsetNotMatched",t.NotAppendable="NotAppendable",t.ContextCanceled="ContextCanceled",t.InternalError="InternalError",t.TooManyRequests="TooManyRequests",t.TimeOut="TimeOut",t.ConcurrencyUpdateObjectLimit="ConcurrencyUpdateObjectLimit",t.DuplicateUpload="DuplicateUpload",t.DuplicateObject="DuplicateObject",t.InvalidVersionId="InvalidVersionId",t.StorageClassNotMatch="StorageClassNotMatch",t.UploadStatusNotUploading="UploadStatusNotUploading",t.PartSizeNotMatch="PartSizeNotMatch",t.NoUploadPart="NoUploadPart",t.PartsLenInvalid="PartsLenInvalid",t.PartsIdxSmall="PartsIdxSmall",t.PartSizeSmall="PartSizeSmall",t.PrefixNotNextKeyPrefix="PrefixNotNextKeyPrefix",t.InvalidPart="InvalidPart",t.InvalidPartOffset="InvalidPartOffset",t.MismatchObject="MismatchObject",t.UploadStatusMismatch="UploadStatusMismatch",t.CompletingStatusNoExpiration="CompletingStatusNoExpiration",t.Found="Found",t.InvalidRedirectLocation="InvalidRedirectLocation";class S extends Error{constructor(e){super(e),Object.setPrototypeOf(this,S.prototype)}}class w extends Error{constructor(e){super(e),Object.setPrototypeOf(this,w.prototype)}}const C=s.createWriteStream,E=s.createReadStream,R=l.promisify(s.rename),P=l.promisify(s.stat),B=l.promisify(s.writeFile),I=l.promisify(s.unlink),D=l.promisify(s.readFile),M=async e=>{try{await c.access(e)}catch(t){await c.mkdir(e,{recursive:!0})}},O=e=>t=>{if(null==e||"object"!=typeof e)return;const a=n(e,t);Array.isArray(a)||i(e,t,null==a?[]:[a])},A=e=>{const t=a=>Array.isArray(a)?a.map(e=>t(e)):"string"==typeof a?e(a):"object"==typeof a&&null!=a?Object.keys(a).reduce((e,o)=>(e[t(o)]=a[o],e),{}):a;return t},j=A(e=>e.replace(/[A-Z]/g,"-$&").toLowerCase()),_=A(e=>e[0].toUpperCase()+e.slice(1)),L=e=>{const t=[];return Object.keys(e).sort().forEach(a=>{t.push(`${encodeURIComponent(a)}=${encodeURIComponent(e[a])}`)}),t.join("&")},U=e=>{const t=e||{},a={};Object.keys(t).forEach(e=>{null!=t[e]&&(a[e]=t[e])});const o={};return Object.keys(a).forEach(e=>{const t=e.toLowerCase();o[t]=a[e]}),o},N=e=>("string"==typeof e&&(e={url:e}),e);async function z(e){try{return[null,await e]}catch(e){return[e,null]}}function F(e){return"undefined"!=typeof Blob&&e instanceof Blob}function q(e){return"undefined"!=typeof Buffer&&e instanceof Buffer}function K(e){return e instanceof o.Readable}function $(e){return e?Object.keys(e).map(t=>{const a=""+e[t];return`${encodeURIComponent(t)}=${encodeURIComponent(a)}`}).join("&"):""}function H(e){return e instanceof w}const G=e=>"string"==typeof e?e:e.toUTCString(),V={projectName:"x-tos-project-name",encodingType:"encoding-type",cacheControl:"cache-control",contentDisposition:"content-disposition",contentLength:"content-length",contentMD5:"content-md5",contentSHA256:"x-tos-content-sha256",contentEncoding:"content-encoding",contentLanguage:"content-language",contentType:"content-type",expires:["expires",e=>e.toUTCString()],range:"range",ifMatch:"if-match",ifModifiedSince:["if-modified-since",G],ifNoneMatch:"if-none-match",ifUnmodifiedSince:["if-unmodified-since",G],acl:"x-tos-acl",grantFullControl:"x-tos-grant-full-control",grantRead:"x-tos-grant-read",grantReadAcp:"x-tos-grant-read-acp",grantWrite:"x-tos-grant-write",grantWriteAcp:"x-tos-grant-write-acp",serverSideEncryption:"x-tos-server-side-encryption",serverSideDataEncryption:"x-tos-server-side-data-encryption",ssecAlgorithm:"x-tos-server-side-encryption-customer-algorithm",ssecKey:"x-tos-server-side-encryption-customer-key",ssecKeyMD5:"x-tos-server-side-encryption-customer-key-md5",copySourceRange:"x-tos-copy-source-range",copySourceIfMatch:"x-tos-copy-source-if-match",copySourceIfModifiedSince:["x-tos-copy-source-if-modified-since",G],copySourceIfNoneMatch:"x-tos-copy-source-if-none-match",copySourceIfUnmodifiedSince:"x-tos-copy-source-if-unmodified-since",copySourceSSECAlgorithm:"x-tos-copy-source-server-side-encryption-customer-algorithm",copySourceSSECKey:"x-tos-copy-source-server-side-encryption-customer-key",copySourceSSECKeyMD5:"x-tos-copy-source-server-side-encryption-customer-key-MD5",metadataDirective:"x-tos-metadata-directive",meta:e=>Object.keys(e).reduce((t,a)=>(t["x-tos-meta-"+a]=""+e[a],t),{}),websiteRedirectLocation:"x-tos-website-redirect-location",storageClass:"x-tos-storage-class",azRedundancy:"x-tos-az-redundancy",trafficLimit:"x-tos-traffic-limit",callback:"x-tos-callback",callbackVar:"x-tos-callback-var",allowSameActionOverlap:["x-tos-allow-same-action-overlap",e=>String(e)],symLinkTargetKey:"x-tos-symlink-target",symLinkTargetBucket:"x-tos-symlink-bucket",forbidOverwrite:"x-tos-forbid-overwrite",bucketType:"x-tos-bucket-type",recursiveMkdir:"x-tos-recursive-mkdir"},W={versionId:"versionId",process:"x-tos-process",saveBucket:"x-tos-save-bucket",saveObject:"x-tos-save-object",responseCacheControl:"response-cache-control",responseContentDisposition:"response-content-disposition",responseContentEncoding:"response-content-encoding",responseContentLanguage:"response-content-language",responseContentType:"response-content-type",responseExpires:["response-expires",e=>e.toUTCString()]};function J(e,t){if(!t.length)return;const a=e.headers||{};function o(e,t){null==a[e]&&(a[e]=t)}e.headers=a,t.forEach(t=>{const a=V[t];if(!a)throw new S(`\`${t}\` isn't in keys of \`requestHeadersMap\``);const n=e[t];if(null==n)return;if("string"==typeof a)return o(a,""+n);if(Array.isArray(a))return o(a[0],a[1](n));const i=a(n);Object.entries(i).forEach(([e,t])=>{o(e,t)})})}const Q=e=>r.stringify(e);function X(e,t){return{data:e,statusCode:t.statusCode,headers:t.headers,requestId:t.requestId,id2:t.id2}}function Y(e,t){const a=t["x-tos-hash-crc64ecma"];if(null==a)return;const o="string"==typeof e?e:e.getCrc64();if(o!==a)throw new S(`validate file crc64 failed. Expect crc64 ${a}, actual crc64 ${o}. Please try again.`)}var Z;!function(e){e.LastModified="last-modified",e.ContentLength="content-length",e.AcceptEncoding="accept-encoding",e.ContentEncoding="content-encoding",e.ContentMD5="content-md5",e.TosRawContentLength="x-tos-raw-content-length",e.TosTrailer="x-tos-trailer",e.TosHashCrc64ecma="x-tos-hash-crc64ecma",e.TosContentSha256="x-tos-content-sha256",e.TosDecodedContentLength="x-tos-decoded-content-length",e.TosEc="x-tos-ec",e.TosRequestId="x-tos-request-id"}(Z||(Z={}));const ee=e=>{let t=Promise.resolve();return async()=>(t=t.then(()=>e()),t)},te=async e=>{try{return JSON.parse(await D(e,"utf-8"))}catch(e){return void console.warn("checkpoint's content is not a valid JSON")}},ae=e=>{let t=null;return{getLastStream:()=>t,make:()=>(t&&oe(t,new Error("retry new stream by makeRetryStreamAutoClose")),t=e(),t)}},oe=(e,t)=>{e&&"destroy"in e&&"function"==typeof e.destroy&&"destroyed"in e&&!e.destroyed&&e.destroy(t)},ne=(e,t,a)=>{var o;return t.on("error",(o=a,e=>{console.log((o||"")+" stream error:",e)})),e.on("error",e=>oe(t,e)),t.on("error",t=>oe(e,t)),e.pipe(t)};async function ie(e){e=this.normalizeObjectInput(e);const t=U(e.headers);return e.headers=t,J(e,["encodingType","cacheControl","contentDisposition","contentEncoding","contentLanguage","contentType","expires","acl","grantFullControl","grantRead","grantReadAcp","grantWriteAcp","ssecAlgorithm","ssecKey","ssecKeyMD5","serverSideEncryption","serverSideDataEncryption","meta","websiteRedirectLocation","storageClass","forbidOverwrite"]),this.setObjectContentTypeHeader(e,t),this._fetchObject(e,"POST",{uploads:""},t,"")}const re=(e,t,a=!1)=>{let o=t;t<5242880&&(o=5242880,a&&console.warn(`partSize has been set to ${o}, because the partSize you provided is less than the minimal size of multipart`));const n=Math.ceil(e/1e4);return t<n&&(o=n,a&&console.warn(`partSize has been set to ${o}, because the partSize you provided causes the number of part excesses 10,000`)),o};async function se(e){const{uploadId:t,...a}=e,o=await this._fetchObject(e,"GET",{uploadId:t,...j(a)},{});return O(o.data)("Parts"),o}const ce={"3gp":"video/3gpp","7z":"application/x-7z-compressed",abw:"application/x-abiword",ai:"application/postscript",aif:"audio/x-aiff",aifc:"audio/x-aiff",aiff:"audio/x-aiff",alc:"chemical/x-alchemy",amr:"audio/amr",anx:"application/annodex",apk:"application/vnd.android.package-archive",appcache:"text/cache-manifest",art:"image/x-jg",asc:"text/plain",asf:"video/x-ms-asf",aso:"chemical/x-ncbi-asn1-binary",asx:"video/x-ms-asf",atom:"application/atom+xml",atomcat:"application/atomcat+xml",atomsrv:"application/atomserv+xml",au:"audio/basic",avi:"video/x-msvideo",awb:"audio/amr-wb",axa:"audio/annodex",axv:"video/annodex",b:"chemical/x-molconn-Z",bak:"application/x-trash",bat:"application/x-msdos-program",bcpio:"application/x-bcpio",bib:"text/x-bibtex",bin:"application/octet-stream",bmp:"image/x-ms-bmp",boo:"text/x-boo",book:"application/x-maker",brf:"text/plain",bsd:"chemical/x-crossfire",c:"text/x-csrc","c++":"text/x-c++src",c3d:"chemical/x-chem3d",cab:"application/x-cab",cac:"chemical/x-cache",cache:"chemical/x-cache",cap:"application/vnd.tcpdump.pcap",cascii:"chemical/x-cactvs-binary",cat:"application/vnd.ms-pki.seccat",cbin:"chemical/x-cactvs-binary",cbr:"application/x-cbr",cbz:"application/x-cbz",cc:"text/x-c++src",cda:"application/x-cdf",cdf:"application/x-cdf",cdr:"image/x-coreldraw",cdt:"image/x-coreldrawtemplate",cdx:"chemical/x-cdx",cdy:"application/vnd.cinderella",cef:"chemical/x-cxf",cer:"chemical/x-cerius",chm:"chemical/x-chemdraw",chrt:"application/x-kchart",cif:"chemical/x-cif",class:"application/java-vm",cls:"text/x-tex",cmdf:"chemical/x-cmdf",cml:"chemical/x-cml",cod:"application/vnd.rim.cod",com:"application/x-msdos-program",cpa:"chemical/x-compass",cpio:"application/x-cpio",cpp:"text/x-c++src",cpt:"application/mac-compactpro",cr2:"image/x-canon-cr2",crl:"application/x-pkcs7-crl",crt:"application/x-x509-ca-cert",crw:"image/x-canon-crw",csd:"audio/csound",csf:"chemical/x-cache-csf",csh:"application/x-csh",csm:"chemical/x-csml",csml:"chemical/x-csml",css:"text/css",csv:"text/csv",ctab:"chemical/x-cactvs-binary",ctx:"chemical/x-ctx",cu:"application/cu-seeme",cub:"chemical/x-gaussian-cube",cxf:"chemical/x-cxf",cxx:"text/x-c++src",d:"text/x-dsrc",davmount:"application/davmount+xml",dcm:"application/dicom",dcr:"application/x-director",ddeb:"application/vnd.debian.binary-package",dif:"video/dv",diff:"text/x-diff",dir:"application/x-director",djv:"image/vnd.djvu",djvu:"image/vnd.djvu",dl:"video/dl",dll:"application/x-msdos-program",dmg:"application/x-apple-diskimage",dms:"application/x-dms",doc:"application/msword",docm:"application/vnd.ms-word.document.macroEnabled.12",docx:"application/vnd.openxmlformats-officedocument.wordprocessingml.document",dot:"application/msword",dotm:"application/vnd.ms-word.template.macroEnabled.12",dotx:"application/vnd.openxmlformats-officedocument.wordprocessingml.template",dv:"video/dv",dvi:"application/x-dvi",dx:"chemical/x-jcamp-dx",dxr:"application/x-director",emb:"chemical/x-embl-dl-nucleotide",embl:"chemical/x-embl-dl-nucleotide",eml:"message/rfc822",eot:"application/vnd.ms-fontobject",eps:"application/postscript",eps2:"application/postscript",eps3:"application/postscript",epsf:"application/postscript",epsi:"application/postscript",erf:"image/x-epson-erf",es:"application/ecmascript",etx:"text/x-setext",exe:"application/x-msdos-program",ez:"application/andrew-inset",fb:"application/x-maker",fbdoc:"application/x-maker",fch:"chemical/x-gaussian-checkpoint",fchk:"chemical/x-gaussian-checkpoint",fig:"application/x-xfig",flac:"audio/flac",fli:"video/fli",flv:"video/x-flv",fm:"application/x-maker",frame:"application/x-maker",frm:"application/x-maker",gal:"chemical/x-gaussian-log",gam:"chemical/x-gamess-input",gamin:"chemical/x-gamess-input",gan:"application/x-ganttproject",gau:"chemical/x-gaussian-input",gcd:"text/x-pcs-gcd",gcf:"application/x-graphing-calculator",gcg:"chemical/x-gcg8-sequence",gen:"chemical/x-genbank",gf:"application/x-tex-gf",gif:"image/gif",gjc:"chemical/x-gaussian-input",gjf:"chemical/x-gaussian-input",gl:"video/gl",gnumeric:"application/x-gnumeric",gpt:"chemical/x-mopac-graph",gsf:"application/x-font",gsm:"audio/x-gsm",gtar:"application/x-gtar",gz:"application/gzip",h:"text/x-chdr","h++":"text/x-c++hdr",hdf:"application/x-hdf",hh:"text/x-c++hdr",hin:"chemical/x-hin",hpp:"text/x-c++hdr",hqx:"application/mac-binhex40",hs:"text/x-haskell",hta:"application/hta",htc:"text/x-component",htm:"text/html",html:"text/html",hwp:"application/x-hwp",hxx:"text/x-c++hdr",ica:"application/x-ica",ice:"x-conference/x-cooltalk",ico:"image/vnd.microsoft.icon",ics:"text/calendar",icz:"text/calendar",ief:"image/ief",iges:"model/iges",igs:"model/iges",iii:"application/x-iphone",info:"application/x-info",inp:"chemical/x-gamess-input",ins:"application/x-internet-signup",iso:"application/x-iso9660-image",isp:"application/x-internet-signup",ist:"chemical/x-isostar",istr:"chemical/x-isostar",jad:"text/vnd.sun.j2me.app-descriptor",jam:"application/x-jam",jar:"application/java-archive",java:"text/x-java",jdx:"chemical/x-jcamp-dx",jmz:"application/x-jmol",jng:"image/x-jng",jnlp:"application/x-java-jnlp-file",jp2:"image/jp2",jpe:"image/jpeg",jpeg:"image/jpeg",jpf:"image/jpx",jpg:"image/jpeg",jpg2:"image/jp2",jpm:"image/jpm",jpx:"image/jpx",js:"application/javascript",json:"application/json",kar:"audio/midi",key:"application/pgp-keys",kil:"application/x-killustrator",kin:"chemical/x-kinemage",kml:"application/vnd.google-earth.kml+xml",kmz:"application/vnd.google-earth.kmz",kpr:"application/x-kpresenter",kpt:"application/x-kpresenter",ksp:"application/x-kspread",kwd:"application/x-kword",kwt:"application/x-kword",latex:"application/x-latex",lha:"application/x-lha",lhs:"text/x-literate-haskell",lin:"application/bbolin",lsf:"video/x-la-asf",lsx:"video/x-la-asf",ltx:"text/x-tex",ly:"text/x-lilypond",lyx:"application/x-lyx",lzh:"application/x-lzh",lzx:"application/x-lzx",m3g:"application/m3g",m3u:"audio/x-mpegurl",m3u8:"application/x-mpegURL",m4a:"audio/mpeg",maker:"application/x-maker",man:"application/x-troff-man",mbox:"application/mbox",mcif:"chemical/x-mmcif",mcm:"chemical/x-macmolecule",mdb:"application/msaccess",me:"application/x-troff-me",mesh:"model/mesh",mid:"audio/midi",midi:"audio/midi",mif:"application/x-mif",mkv:"video/x-matroska",mm:"application/x-freemind",mmd:"chemical/x-macromodel-input",mmf:"application/vnd.smaf",mml:"text/mathml",mmod:"chemical/x-macromodel-input",mng:"video/x-mng",moc:"text/x-moc",mol:"chemical/x-mdl-molfile",mol2:"chemical/x-mol2",moo:"chemical/x-mopac-out",mop:"chemical/x-mopac-input",mopcrt:"chemical/x-mopac-input",mov:"video/quicktime",movie:"video/x-sgi-movie",mp2:"audio/mpeg",mp3:"audio/mpeg",mp4:"video/mp4",mpc:"chemical/x-mopac-input",mpe:"video/mpeg",mpeg:"video/mpeg",mpega:"audio/mpeg",mpg:"video/mpeg",mpga:"audio/mpeg",mph:"application/x-comsol",mpv:"video/x-matroska",ms:"application/x-troff-ms",msh:"model/mesh",msi:"application/x-msi",mvb:"chemical/x-mopac-vib",mxf:"application/mxf",mxu:"video/vnd.mpegurl",nb:"application/mathematica",nbp:"application/mathematica",nc:"application/x-netcdf",nef:"image/x-nikon-nef",nwc:"application/x-nwc",o:"application/x-object",oda:"application/oda",odb:"application/vnd.oasis.opendocument.database",odc:"application/vnd.oasis.opendocument.chart",odf:"application/vnd.oasis.opendocument.formula",odg:"application/vnd.oasis.opendocument.graphics",odi:"application/vnd.oasis.opendocument.image",odm:"application/vnd.oasis.opendocument.text-master",odp:"application/vnd.oasis.opendocument.presentation",ods:"application/vnd.oasis.opendocument.spreadsheet",odt:"application/vnd.oasis.opendocument.text",oga:"audio/ogg",ogg:"audio/ogg",ogv:"video/ogg",ogx:"application/ogg",old:"application/x-trash",one:"application/onenote",onepkg:"application/onenote",onetmp:"application/onenote",onetoc2:"application/onenote",opf:"application/oebps-package+xml",opus:"audio/ogg",orc:"audio/csound",orf:"image/x-olympus-orf",otf:"application/font-sfnt",otg:"application/vnd.oasis.opendocument.graphics-template",oth:"application/vnd.oasis.opendocument.text-web",otp:"application/vnd.oasis.opendocument.presentation-template",ots:"application/vnd.oasis.opendocument.spreadsheet-template",ott:"application/vnd.oasis.opendocument.text-template",oza:"application/x-oz-application",p:"text/x-pascal",p7r:"application/x-pkcs7-certreqresp",pac:"application/x-ns-proxy-autoconfig",pas:"text/x-pascal",pat:"image/x-coreldrawpattern",patch:"text/x-diff",pbm:"image/x-portable-bitmap",pcap:"application/vnd.tcpdump.pcap",pcf:"application/x-font-pcf","pcf.Z":"application/x-font-pcf",pcx:"image/pcx",pdb:"chemical/x-pdb",pdf:"application/pdf",pfa:"application/x-font",pfb:"application/x-font",pfr:"application/font-tdpfr",pgm:"image/x-portable-graymap",pgn:"application/x-chess-pgn",pgp:"application/pgp-encrypted",php:"#application/x-httpd-php",php3:"#application/x-httpd-php3",php3p:"#application/x-httpd-php3-preprocessed",php4:"#application/x-httpd-php4",php5:"#application/x-httpd-php5",phps:"#application/x-httpd-php-source",pht:"#application/x-httpd-php",phtml:"#application/x-httpd-php",pk:"application/x-tex-pk",pl:"text/x-perl",pls:"audio/x-scpls",pm:"text/x-perl",png:"image/png",pnm:"image/x-portable-anymap",pot:"text/plain",potm:"application/vnd.ms-powerpoint.template.macroEnabled.12",potx:"application/vnd.openxmlformats-officedocument.presentationml.template",ppam:"application/vnd.ms-powerpoint.addin.macroEnabled.12",ppm:"image/x-portable-pixmap",pps:"application/vnd.ms-powerpoint",ppsm:"application/vnd.ms-powerpoint.slideshow.macroEnabled.12",ppsx:"application/vnd.openxmlformats-officedocument.presentationml.slideshow",ppt:"application/vnd.ms-powerpoint",pptm:"application/vnd.ms-powerpoint.presentation.macroEnabled.12",pptx:"application/vnd.openxmlformats-officedocument.presentationml.presentation",prf:"application/pics-rules",prt:"chemical/x-ncbi-asn1-ascii",ps:"application/postscript",psd:"image/x-photoshop",py:"text/x-python",pyc:"application/x-python-code",pyo:"application/x-python-code",qgs:"application/x-qgis",qt:"video/quicktime",qtl:"application/x-quicktimeplayer",ra:"audio/x-pn-realaudio",ram:"audio/x-pn-realaudio",rar:"application/rar",ras:"image/x-cmu-raster",rb:"application/x-ruby",rd:"chemical/x-mdl-rdfile",rdf:"application/rdf+xml",rdp:"application/x-rdp",rgb:"image/x-rgb",rhtml:"#application/x-httpd-eruby",rm:"audio/x-pn-realaudio",roff:"application/x-troff",ros:"chemical/x-rosdal",rpm:"application/x-redhat-package-manager",rss:"application/x-rss+xml",rtf:"application/rtf",rtx:"text/richtext",rxn:"chemical/x-mdl-rxnfile",scala:"text/x-scala",sce:"application/x-scilab",sci:"application/x-scilab",sco:"audio/csound",scr:"application/x-silverlight",sct:"text/scriptlet",sd:"chemical/x-mdl-sdfile",sd2:"audio/x-sd2",sda:"application/vnd.stardivision.draw",sdc:"application/vnd.stardivision.calc",sdd:"application/vnd.stardivision.impress",sds:"application/vnd.stardivision.chart",sdw:"application/vnd.stardivision.writer",ser:"application/java-serialized-object",sfd:"application/vnd.font-fontforge-sfd",sfv:"text/x-sfv",sgf:"application/x-go-sgf",sgl:"application/vnd.stardivision.writer-global",sh:"application/x-sh",shar:"application/x-shar",shp:"application/x-qgis",shtml:"text/html",shx:"application/x-qgis",sid:"audio/prs.sid",sig:"application/pgp-signature",sik:"application/x-trash",silo:"model/mesh",sis:"application/vnd.symbian.install",sisx:"x-epoc/x-sisx-app",sit:"application/x-stuffit",sitx:"application/x-stuffit",skd:"application/x-koan",skm:"application/x-koan",skp:"application/x-koan",skt:"application/x-koan",sldm:"application/vnd.ms-powerpoint.slide.macroEnabled.12",sldx:"application/vnd.openxmlformats-officedocument.presentationml.slide",smi:"application/smil+xml",smil:"application/smil+xml",snd:"audio/basic",spc:"chemical/x-galactic-spc",spl:"application/x-futuresplash",spx:"audio/ogg",sql:"application/x-sql",src:"application/x-wais-source",srt:"text/plain",stc:"application/vnd.sun.xml.calc.template",std:"application/vnd.sun.xml.draw.template",sti:"application/vnd.sun.xml.impress.template",stw:"application/vnd.sun.xml.writer.template",sty:"text/x-tex",sv4cpio:"application/x-sv4cpio",sv4crc:"application/x-sv4crc",svg:"image/svg+xml",svgz:"image/svg+xml",sw:"chemical/x-swissprot",swf:"application/x-shockwave-flash",swfl:"application/x-shockwave-flash",sxc:"application/vnd.sun.xml.calc",sxd:"application/vnd.sun.xml.draw",sxg:"application/vnd.sun.xml.writer.global",sxi:"application/vnd.sun.xml.impress",sxm:"application/vnd.sun.xml.math",sxw:"application/vnd.sun.xml.writer",t:"application/x-troff",tar:"application/x-tar",taz:"application/x-gtar-compressed",tcl:"application/x-tcl",tex:"text/x-tex",texi:"application/x-texinfo",texinfo:"application/x-texinfo",text:"text/plain",tgf:"chemical/x-mdl-tgf",tgz:"application/x-gtar-compressed",thmx:"application/vnd.ms-officetheme",tif:"image/tiff",tiff:"image/tiff",tk:"text/x-tcl",tm:"text/texmacs",torrent:"application/x-bittorrent",tr:"application/x-troff",ts:"video/MP2T",tsp:"application/dsptype",tsv:"text/tab-separated-values",ttf:"application/font-sfnt",ttl:"text/turtle",txt:"text/plain",uls:"text/iuls",ustar:"application/x-ustar",val:"chemical/x-ncbi-asn1-binary",vcard:"text/vcard",vcd:"application/x-cdlink",vcf:"text/vcard",vcs:"text/x-vcalendar",vmd:"chemical/x-vmd",vms:"chemical/x-vamas-iso14976",vrm:"x-world/x-vrml",vrml:"model/vrml",vsd:"application/vnd.visio",vss:"application/vnd.visio",vst:"application/vnd.visio",vsw:"application/vnd.visio",wad:"application/x-doom",wasm:"application/wasm",wav:"audio/wav",wax:"audio/x-ms-wax",wbmp:"image/vnd.wap.wbmp",wbxml:"application/vnd.wap.wbxml",webm:"video/webm",wk:"application/x-123",wm:"video/x-ms-wm",wma:"audio/x-ms-wma",wmd:"application/x-ms-wmd",wml:"text/vnd.wap.wml",wmlc:"application/vnd.wap.wmlc",wmls:"text/vnd.wap.wmlscript",wmlsc:"application/vnd.wap.wmlscriptc",wmv:"video/x-ms-wmv",wmx:"video/x-ms-wmx",wmz:"application/x-ms-wmz",woff:"application/font-woff",wp5:"application/vnd.wordperfect5.1",wpd:"application/vnd.wordperfect",wrl:"model/vrml",wsc:"text/scriptlet",wvx:"video/x-ms-wvx",wz:"application/x-wingz",x3d:"model/x3d+xml",x3db:"model/x3d+binary",x3dv:"model/x3d+vrml",xbm:"image/x-xbitmap",xcf:"application/x-xcf",xcos:"application/x-scilab-xcos",xht:"application/xhtml+xml",xhtml:"application/xhtml+xml",xlam:"application/vnd.ms-excel.addin.macroEnabled.12",xlb:"application/vnd.ms-excel",xls:"application/vnd.ms-excel",xlsb:"application/vnd.ms-excel.sheet.binary.macroEnabled.12",xlsm:"application/vnd.ms-excel.sheet.macroEnabled.12",xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",xlt:"application/vnd.ms-excel",xltm:"application/vnd.ms-excel.template.macroEnabled.12",xltx:"application/vnd.openxmlformats-officedocument.spreadsheetml.template",xml:"application/xml",xpi:"application/x-xpinstall",xpm:"image/x-xpixmap",xsd:"application/xml",xsl:"application/xslt+xml",xslt:"application/xslt+xml",xspf:"application/xspf+xml",xtel:"chemical/x-xtel",xul:"application/vnd.mozilla.xul+xml",xwd:"image/x-xwindowdump",xyz:"chemical/x-xyz",xz:"application/x-xz",zip:"application/zip"};function le(e){return new o.Transform({async transform(t,a,o){e(t.length),this.push(t),o()}})}function pe(e,t){const a=le(t);return ne(e,a,"createReadNReadStream")}let ue=null;ue={__proto__:null,CRC:class{constructor(){this.value="0"}reset(){this.value="0"}async updateBlob(){throw new S("Not implemented in node.js environment.")}update(e){return this.value=p.crc64(e,this.value),this.value}getCrc64(){return this.value}},combineCrc64:p.combineCrc64};const{CRC:de,combineCrc64:he}=ue;function me(e){return new o.Transform({async transform(t,a,o){try{const a=t.length;let n=!1;for(;!n;){const{ok:t,timeToWait:o}=await e.Acquire(a);t||await fe(o),n=t}this.push(t),o()}catch(e){o(e)}}})}function fe(e){return new Promise(t=>{setTimeout(()=>t(""),e)})}var ye={__proto__:null,createDefaultRateLimiter:function(e,t){const a=Math.max(10240,e),o={rate:Math.max(1024,t),capacity:a,currentAmount:a,lastConsumeTime:Date.now()};return{Acquire:async e=>{e>o.capacity&&(e=o.capacity);const t=Date.now(),a=Math.floor((t-o.lastConsumeTime)/1e3*o.rate);return a+o.currentAmount>o.capacity?o.currentAmount=o.capacity:o.currentAmount+=a,e>o.currentAmount?{ok:!1,timeToWait:Math.ceil((e-o.currentAmount)/o.rate*1e3)}:(o.lastConsumeTime=t,o.currentAmount=o.currentAmount-e,{ok:!0,timeToWait:0})}}},createRateLimiterStream:function(e,t){const a=me(t);return ne(e,a,"createRateLimiterStream")},wait:fe};let ge=null;ge=ye;const{createDefaultRateLimiter:xe,createRateLimiterStream:be}=ge;class ke extends o.Readable{constructor(e){super(),this.buf=void 0,this.lastPos=0,this.buf=e}_read(e){const t=this.buf.length;let a=Math.min(e,t-this.lastPos);this.lastPos>=t?this.push(null):(this.push(this.buf.slice(this.lastPos,this.lastPos+a)),this.lastPos+=a)}}function ve(e){return new o.Transform({async transform(t,a,o){e(t),this.push(t),o()}})}function Te(e,t){const a=ve(e=>t.update(e));return ne(e,a,"createCrcReadStream")}var Se;function we(e){if(("string"==typeof e?e:e.key).length<1)throw new S("invalid object name, the length must be greater than 1")}function Ce(e,t){if(q(e))return e.length;if(F(e))return e.size;if(t&&t["content-length"]){const e=+t["content-length"];if(e>=0)return e}return null}async function Ee(e){const t=function({body:e,dataTransferCallback:t,makeRetryStream:a,rateLimiter:o}){let n=e;if(q(n)){const e=n;a=()=>new ke(e),n=new ke(e)}if(K(n)&&(o&&Pe(o)&&(n=be(n,o)),n=pe(n,t),a)){const e=a;return{body:n,makeRetryStream:()=>{let a=e();return a?(o&&Pe(o)&&(a=be(a,o)),a=pe(a,t),a):a}}}return{body:n,makeRetryStream:void 0}}(e);return async function({body:e,beforeRetry:t,makeRetryStream:a,enableCRC:o}){if(!o)return{body:e,beforeRetry:t,makeRetryStream:a};let n=e;const i=new de;if(K(e)&&(n=Te(e,i),a)){const e=a;a=()=>{const t=e();return t?Te(t,i):t}}return{body:n,beforeRetry:()=>{i.reset(),null==t||t()},makeRetryStream:a,crc:i}}(e={...e,...t})}function Re(e,t){return`/${e}/${encodeURIComponent(t)}`}function Pe(e){if(!(null!=e&&e.Acquire&&(null==e?void 0:e.Acquire)instanceof Function))throw new S("The rateLimiter is not valid function");return!0}function Be(e){"object"==typeof e&&console.warn("The `checkpoint` parameter should be passed as a string in node.js environment, representing a file or directory.Passing a checkpoint object to it will be removed in the future.")}!function(e){e.HeaderRestore="x-tos-restore",e.HeaderRestoreExpiryDays="x-tos-restore-expiry-days",e.HeaderRestoreRequestDate="x-tos-restore-request-date",e.HeaderRestoreTier="x-tos-restore-tier",e.HeaderProjectName="x-tos-project-name",e.HeaderReplicationStatus="x-tos-replication-status"}(Se||(Se={}));const Ie=e=>{if(!e)return;const t=null==e?void 0:e[Se.HeaderRestore];if(t){var a,o,n;const r=null!=(a=null==(o=(null!=t?t:"").split('",')[1])||null==o.split||null==(n=o.split("="))?void 0:n[1])?a:"",s='ongoing-request="true"'===(null==t?void 0:t.trim()),c={RestoreStatus:{OngoingRequest:s,ExpiryDate:r}};var i;return s&&(c.RestoreParam={ExpiryDays:e[Se.HeaderRestoreExpiryDays]?Number(e[Se.HeaderRestoreExpiryDays]):0,RequestDate:null!=(i=e[Se.HeaderRestoreRequestDate])?i:"",Tier:e[Se.HeaderRestoreTier]}),c}};var De;(De=exports.DataTransferType||(exports.DataTransferType={}))[De.Started=1]="Started",De[De.Rw=2]="Rw",De[De.Succeed=3]="Succeed",De[De.Failed=4]="Failed";const Me=u("TOS"),Oe=function(e,t){if(q(e))throw new S("not support buffer in browser environment");return function(e,t){return t?e.toString(function(e){switch(e){case"utf-8":return f;case"base64":return h;case"hex":return m;default:throw new S("The coding is not supported")}}(t)):e}(d(e),t)};function Ae(e,t){return t?e.digest(t):e.digest()}let je=null;je={__proto__:null,hmacSha256:function(e,t,a){return Ae(y.createHmac("sha256",e).update(t),a)},hashSha256:function(e,t){return Ae(y.createHash("sha256").update(e),t)},hashMd5:function(e,t){return Ae(y.createHash("md5").update(e),t)},parse:function(e,t){return Buffer.from(e,t)},stringify:function(e,t){return e.toString(t)}};const{hmacSha256:_e,hashSha256:Le,hashMd5:Ue,parse:Ne,stringify:ze}=je;async function Fe(e){const{uploadId:t,partNumber:a,body:o,enableContentMD5:n=!1}=e,i=U(e.headers);e.headers=i,J(e,["trafficLimit","ssecAlgorithm","ssecKey","ssecKeyMD5"]);const r=Ce(o);if(r&&null==i["content-length"]&&(i["content-length"]=r.toFixed(0)),n&&null==i["content-md5"])if(K(o)&&e.makeRetryStream){const t=e.makeRetryStream();if(t){let e=Buffer.from([]);for await(const a of t)e=Buffer.concat([e,"string"==typeof a?Buffer.from(a):a]);const a=Ue(e,"base64");i["content-md5"]=a}}else console.warn("current not support enableMD5Checksum");const s=Ce(e.body,i),c=null!=s;c||!e.dataTransferStatusChange&&!e.progress||console.warn("Don't get totalSize of uploadPart's body, the `dataTransferStatusChange` callback will not trigger. You can use `uploadPartFromFile` instead");let l=0;const{dataTransferStatusChange:p,progress:u}=e,d=(e,t=0)=>{if(!c||t<0)return;if(!p&&!u)return;l+=t,null==p||p({type:e,rwOnceBytes:t,consumedBytes:l,totalBytes:s});const a=0===s?e===exports.DataTransferType.Succeed?1:0:l/s;1===a?e===exports.DataTransferType.Succeed&&(null==u||u(a)):null==u||u(a)},h=await Ee({body:e.body,dataTransferCallback:e=>d(exports.DataTransferType.Rw,e),beforeRetry:e.beforeRetry,makeRetryStream:e.makeRetryStream,enableCRC:this.opts.enableCRC,rateLimiter:e.rateLimiter});d(exports.DataTransferType.Started);const[m,f]=await z((async()=>{const o=await this._fetchObject(e,"PUT",{partNumber:a,uploadId:t},i,h.body,{handleResponse:e=>({partNumber:a,ETag:e.headers.etag,serverSideEncryption:e.headers["x-tos-server-side-encryption"],serverSideDataEncryption:e.headers["x-tos-server-side-data-encryption"],serverSideEncryptionKeyId:e.headers["x-tos-server-side-encryption-kms-key-id"],ssecAlgorithm:e.headers["x-tos-server-side-encryption-customer-algorithm"],ssecKeyMD5:e.headers["x-tos-server-side-encryption-customer-key-MD5"],hashCrc64ecma:e.headers["x-tos-hash-crc64ecma"]}),axiosOpts:{__retryConfig__:{beforeRetry:()=>{l=0,null==h.beforeRetry||h.beforeRetry()},makeRetryStream:h.makeRetryStream},onUploadProgress:e=>{d(exports.DataTransferType.Rw,e.loaded-l)}}});return this.opts.enableCRC&&h.crc&&Y(h.crc,o.headers),o})());if(m||!f)throw d(exports.DataTransferType.Failed),m;return d(exports.DataTransferType.Succeed),f}async function qe(e){return Fe.call(this,e)}async function Ke(e){var t,a;const o=await P(e.filePath),n=null!=(t=e.offset)?t:0,i=Math.min(o.size,n+(null!=(a=e.partSize)?a:o.size)),r=ae(()=>E(e.filePath,{start:n,end:i-1}));try{return await Fe.call(this,{...e,body:r.make(),headers:{...e.headers||{},"content-length":""+(i-n)},makeRetryStream:r.make})}catch(e){throw oe(r.getLastStream(),e),e}}async function $e(e){var t;e.headers=null!=(t=e.headers)?t:{},J(e,["callback","callbackVar","forbidOverwrite"]);const a=t=>{const a=t.headers,o={VersionID:a["x-tos-version-id"],ETag:a.etag,Bucket:e.bucket||this.opts.bucket||"",Location:a.location,HashCrc64ecma:a["x-tos-hash-crc64ecma"],Key:e.key,...t.data};return e.callback&&(o.CallbackResult=""+JSON.stringify(t.data)),o};if(e.completeAll){var o;if((null==(o=e.parts)?void 0:o.length)>0)throw new S("Should not specify both 'completeAll' and 'parts' params.");return this._fetchObject(e,"POST",{uploadId:e.uploadId},{...e.headers,"x-tos-complete-all":"yes"},void 0,{handleResponse:a})}return this._fetchObject(e,"POST",{uploadId:e.uploadId},{...e.headers},{Parts:e.parts.map(e=>({ETag:e.eTag,PartNumber:e.partNumber}))},{handleResponse:a})}class He extends o.Readable{_read(){this.push(null)}}var Ge;(Ge=exports.UploadEventType||(exports.UploadEventType={}))[Ge.CreateMultipartUploadSucceed=1]="CreateMultipartUploadSucceed",Ge[Ge.CreateMultipartUploadFailed=2]="CreateMultipartUploadFailed",Ge[Ge.UploadPartSucceed=3]="UploadPartSucceed",Ge[Ge.UploadPartFailed=4]="UploadPartFailed",Ge[Ge.UploadPartAborted=5]="UploadPartAborted",Ge[Ge.CompleteMultipartUploadSucceed=6]="CompleteMultipartUploadSucceed",Ge[Ge.CompleteMultipartUploadFailed=7]="CompleteMultipartUploadFailed";const Ve=[403,404,405];async function We(e){var t,a,o;const{cancelToken:n,enableContentMD5:i=!1}=e,r=U(e.headers);e.headers=r,J(e,["encodingType","cacheControl","contentDisposition","contentEncoding","contentLanguage","contentType","expires","acl","grantFullControl","grantRead","grantReadAcp","grantWriteAcp","ssecAlgorithm","ssecKey","ssecKeyMD5","serverSideEncryption","serverSideDataEncryption","meta","websiteRedirectLocation","storageClass"]);const s=()=>n&&!!n.reason;Be(e.checkpoint);const c=await(async()=>"string"==typeof e.file?P(e.file):null)(),l=await(async()=>{const{file:t}=e;if(c)return c.size;if(q(t))return t.length;if(F(t))return t.size;throw new S("`file` must be string, Buffer, File or Blob")})(),p=await(async()=>{if("string"==typeof e.checkpoint){const{checkpoint:t}=e;let a=null;try{a=await P(t)}catch(e){const t=e;if("ENOENT"!==t.code)throw t}const o=a?a.isDirectory():t.endsWith("/"),n=o?g.resolve(t,"@@checkpoint-file-placeholder@@"):g.resolve(t),i=g.dirname(n);if(await M(i),o)return{filePath:n,filePathIsPlaceholder:!0};try{return{filePath:n,filePathIsPlaceholder:!1,record:a?await te(n):void 0}}catch(e){throw console.warn("the checkpoint file is invalid JSON format. please check checkpoint file"),e}}return"object"==typeof e.checkpoint?{record:e.checkpoint}:{}})();await(async()=>{var e;if(c&&null!=(e=p.record)&&e.file_info){var t;const{last_modified:e,file_size:a}=null==(t=p.record)?void 0:t.file_info;c.mtimeMs===e&&c.size===a||(console.warn(`The file has been modified since ${new Date(e)}, so the checkpoint file is invalid, and specified file will be uploaded again.`),delete p.record)}})();const u=re(l,e.partSize||(null==(t=p.record)?void 0:t.part_size)||20971520,!0);p.record&&p.record.part_size!==u&&(console.warn("The partSize param does not equal the partSize in checkpoint file, so the checkpoint file is invalid, and specified file will be uploaded again."),delete p.record);let d=e.bucket||this.opts.bucket||"";const h=e.key;let m="",f=[];const y=function(e,t){const a=[];for(let o=0;;++o){const n=o*t,i=Math.min(t,e-n);if(a.push({offset:n,partSize:i,partNumber:o+1}),(o+1)*t>=e)break}return a}(l,u),x=((null==(a=p.record)?void 0:a.parts_info)||[]).filter(e=>e.is_completed).reduce((e,t)=>e+t.part_size,0);let b=x;const k=(null==(o=p.record)?void 0:o.parts_info)||[],v=new Map;k.forEach(e=>v.set(e.part_number,e));const C=()=>{const e={bucket:d,key:h,part_size:u,upload_id:m,parts_info:k};return c&&(e.file_info={last_modified:c.mtimeMs,file_size:c.size}),e},E=t=>{if(!e.uploadEventChange)return;const a={bucket:d,uploadId:m,key:h,...t};p.filePath&&(a.checkpointFile=p.filePath),e.uploadEventChange(a)};let R;!function(e){e[e.start=1]="start",e[e.uploadPartSucceed=2]="uploadPartSucceed",e[e.completeMultipartUploadSucceed=3]="completeMultipartUploadSucceed"}(R||(R={}));const D=t=>{e.progress&&(b===l&&t===R.uploadPartSucceed||e.progress(t===R.start&&0===l?0:l?b/l:1,C()))};let O=x;const{dataTransferStatusChange:A}=e,j=(e,t=0)=>{A&&(O+=t,null==A||A({type:e,rwOnceBytes:t,consumedBytes:O,totalBytes:l}))},_=ee(async()=>{if(p.filePath){const e=JSON.stringify(C(),null,2),t=g.dirname(p.filePath);await M(t),await B(p.filePath,e,"utf-8")}}),L=async(e,t)=>{let a=v.get(e.partNumber);a||(a={part_number:e.partNumber,offset:e.offset,part_size:e.partSize,is_completed:!1,etag:"",hash_crc64ecma:""},k.push(a),v.set(a.part_number,a)),t.err||(a.is_completed=!0,a.etag=t.res.ETag,a.hash_crc64ecma=t.res.hashCrc64ecma),await _();const o={partNumber:a.part_number,partSize:a.part_size,offset:a.offset};if(t.err){const e=t.err;let a=exports.UploadEventType.UploadPartFailed;return e instanceof T&&Ve.includes(e.statusCode)&&(a=exports.UploadEventType.UploadPartAborted),void E({type:a,err:e,uploadPartInfo:o})}o.etag=t.res.ETag,b+=o.partSize,E({type:exports.UploadEventType.UploadPartSucceed,uploadPartInfo:o}),D(R.uploadPartSucceed)};if(p.record){d=p.record.bucket,m=p.record.upload_id;const e=new Set((p.record.parts_info||[]).filter(e=>e.is_completed).map(e=>e.part_number));f=y.filter(t=>!e.has(t.partNumber))}else{try{const{data:t}=await ie.call(this,e);if(s())throw new w("cancel uploadFile");var N;d=t.Bucket,m=t.UploadId,p.filePathIsPlaceholder&&(p.filePath=null==(N=p.filePath)?void 0:N.replace("@@checkpoint-file-placeholder@@",function(e,t){return`${t}.${Ue(`${e}.${t}`,"hex")}.upload`.replace(/[\\/]/g,"")}(d,h))),E({type:exports.UploadEventType.CreateMultipartUploadSucceed})}catch(e){const t=e;throw E({type:exports.UploadEventType.CreateMultipartUploadFailed,err:t}),t}f=y}D(R.start),j(exports.DataTransferType.Started);const[K,$]=await z((async()=>{let t=null,a=0;if(await Promise.all(Array.from({length:e.taskNum||1}).map(async()=>{for(;;){const n=a++;if(n>=f.length)return;const c=f[n];let l=0;const p=Je(e.file,c);try{function o(e,t){const{offset:a,partSize:o}=t,n=a+o;if(p)return p.make();if(F(e))return e.slice(a,n);if(q(e))return e.slice(a,n);throw new S("`file` must be string, Buffer, File or Blob")}const{data:t}=await Fe.call(this,{bucket:d,key:h,uploadId:m,body:o(e.file,c),enableContentMD5:i,makeRetryStream:null==p?void 0:p.make,beforeRetry:()=>{O-=l,l=0},partNumber:c.partNumber,headers:{"content-length":""+c.partSize,"x-tos-server-side-encryption-customer-algorithm":r["x-tos-server-side-encryption-customer-algorithm"],"x-tos-server-side-encryption-customer-key":r["x-tos-server-side-encryption-customer-key"],"x-tos-server-side-encryption-customer-key-md5":r["x-tos-server-side-encryption-customer-key-md5"]},dataTransferStatusChange(e){e.type===exports.DataTransferType.Rw&&(s()||(l+=e.rwOnceBytes,j(e.type,e.rwOnceBytes)))},trafficLimit:e.trafficLimit,rateLimiter:e.rateLimiter});if(s())throw new w("cancel uploadFile");await L(c,{res:t})}catch(e){oe(null==p?void 0:p.getLastStream(),e);const a=e;if(O-=l,l=0,H(a))throw a;if(s())throw new w("cancel uploadFile");t||(t=a),await L(c,{err:a})}}})),t)throw t;const o=(C().parts_info||[]).map(e=>({eTag:e.etag,partNumber:e.part_number})),[n,c]=await z($e.call(this,{bucket:d,key:h,uploadId:m,parts:o}));if(n||!c)throw E({type:exports.UploadEventType.CompleteMultipartUploadFailed}),n;if(E({type:exports.UploadEventType.CompleteMultipartUploadSucceed}),D(R.completeMultipartUploadSucceed),await(async()=>{p.filePath&&await I(p.filePath).catch(e=>{console.warn("remove checkpoint file failure, you can remove it by hand.\n",`checkpoint file path: ${p.filePath}\n`,e.message)})})(),this.opts.enableCRC&&c.data.HashCrc64ecma&&function(e){var t,a,o;const n=(null==(t=e.file_info)?void 0:t.file_size)||0;let i="0";const r=null!=(a=null==(o=e.parts_info)||null==o.sort?void 0:o.sort((e,t)=>e.part_number-t.part_number))?a:[];for(const e of r)i=he(i,e.hash_crc64ecma,Math.min(e.part_size,n-e.offset));return i}(C())!==c.data.HashCrc64ecma)throw new S("crc of entire file mismatch.");return c})());if(K||!$)throw j(exports.DataTransferType.Failed),K;return j(exports.DataTransferType.Succeed),$}function Je(e,t){const{offset:a,partSize:o}=t,n=a+o;if("string"==typeof e)return ae(()=>o?E(e,{start:a,end:n-1}):new He)}var Qe,Xe,Ye,Ze,et,tt,at,ot,nt,it,rt,st,ct,lt,pt,ut,dt,ht,mt;async function ft(e){const t="string"==typeof e?{key:e}:e,a=U(t.headers);t.headers=a;const o={};return t.versionId&&(o.versionId=t.versionId),J(t,["ifMatch","ifModifiedSince","ifNoneMatch","ifUnmodifiedSince","ssecAlgorithm","ssecKey","ssecKeyMD5"]),this._fetchObject(e,"HEAD",o,(null==t?void 0:t.headers)||{},void 0,{handleResponse:e=>{const t={...e.headers,ReplicationStatus:e.headers[Se.HeaderReplicationStatus]},a=Ie(e.headers);return a&&(t.RestoreInfo=a),t}})}async function yt(e){const{uploadId:t,partNumber:a}=e,o=U(e.headers);if(e.headers=o,J(e,["copySourceRange","copySourceSSECAlgorithm","copySourceSSECKey","copySourceSSECKeyMD5","ssecAlgorithm","ssecKey","ssecKeyMD5","trafficLimit"]),e.srcBucket&&e.srcKey){var n;let t=Re(e.srcBucket,e.srcKey);e.srcVersionID&&(t+="?versionId="+e.srcVersionID),o["x-tos-copy-source"]=null!=(n=o["x-tos-copy-source"])?n:t}if(null==e.copySourceRange&&(null!=e.copySourceRangeStart||null!=e.copySourceRangeEnd)){var i;const t=`bytes=${null!=e.copySourceRangeStart?""+e.copySourceRangeStart:""}-${null!=e.copySourceRangeEnd?""+e.copySourceRangeEnd:""}`;o["x-tos-copy-source-range"]=null!=(i=o["x-tos-copy-source-range"])?i:t}const[r,s]=await z(this._fetchObject(e,"PUT",{partNumber:a,uploadId:t},o,void 0,{handleResponse:e=>({...e.data,SSECAlgorithm:e.headers[V.ssecAlgorithm],SSECKeyMD5:e.headers[V.ssecKeyMD5]})}));if(r||!s||!s.data.ETag)throw r;return s}async function gt(e){const t=U(e.headers);if(e.headers=t,J(e,["cacheControl","contentDisposition","contentEncoding","contentLanguage","contentType","expires","copySourceIfMatch","copySourceIfModifiedSince","copySourceIfNoneMatch","copySourceIfUnmodifiedSince","copySourceSSECAlgorithm","copySourceSSECKey","copySourceSSECKeyMD5","acl","grantFullControl","grantRead","grantReadAcp","grantWriteAcp","ssecAlgorithm","ssecKey","ssecKeyMD5","serverSideEncryption","metadataDirective","meta","websiteRedirectLocation","storageClass","trafficLimit","forbidOverwrite","ifMatch"]),e.srcBucket&&e.srcKey){var a;let o=Re(e.srcBucket,e.srcKey);e.srcVersionID&&(o+="?versionId="+e.srcVersionID),t["x-tos-copy-source"]=null!=(a=t["x-tos-copy-source"])?a:o}const[o,n]=await z(this._fetchObject(e,"PUT",{},t));if(o||!n||!n.data.ETag)throw o;return n}(Qe=exports.ACLType||(exports.ACLType={})).ACLPrivate="private",Qe.ACLPublicRead="public-read",Qe.ACLPublicReadWrite="public-read-write",Qe.ACLAuthenticatedRead="authenticated-read",Qe.ACLBucketOwnerRead="bucket-owner-read",Qe.ACLBucketOwnerFullControl="bucket-owner-full-control",Qe.ACLBucketOwnerEntrusted="bucket-owner-entrusted",Qe.ACLDefault="default",(Xe=exports.StorageClassType||(exports.StorageClassType={})).StorageClassStandard="STANDARD",Xe.StorageClassIa="IA",Xe.StorageClassArchiveFr="ARCHIVE_FR",Xe.StorageClassColdArchive="COLD_ARCHIVE",Xe.StorageClassIntelligentTiering="INTELLIGENT_TIERING",Xe.StorageClassArchive="ARCHIVE",(Ye=exports.MetadataDirectiveType||(exports.MetadataDirectiveType={})).MetadataDirectiveCopy="COPY",Ye.MetadataDirectiveReplace="REPLACE",(Ze=exports.AzRedundancyType||(exports.AzRedundancyType={})).AzRedundancySingleAz="single-az",Ze.AzRedundancyMultiAz="multi-az",(et=exports.PermissionType||(exports.PermissionType={})).PermissionRead="READ",et.PermissionWrite="WRITE",et.PermissionReadAcp="READ_ACP",et.PermissionWriteAcp="WRITE_ACP",et.PermissionFullControl="FULL_CONTROL",et.PermissionReadNONLIST="READ_NON_LIST",(tt=exports.GranteeType||(exports.GranteeType={})).GranteeGroup="Group",tt.GranteeUser="CanonicalUser",(at=exports.CannedType||(exports.CannedType={})).CannedAllUsers="AllUsers",at.CannedAuthenticatedUsers="AuthenticatedUsers",(ot=exports.HttpMethodType||(exports.HttpMethodType={})).HttpMethodGet="GET",ot.HttpMethodPut="PUT",ot.HttpMethodPost="POST",ot.HttpMethodDelete="DELETE",ot.HttpMethodHead="HEAD",(nt=exports.StorageClassInheritDirectiveType||(exports.StorageClassInheritDirectiveType={})).StorageClassInheritDirectiveDestinationBucket="DESTINATION_BUCKET",nt.StorageClassInheritDirectiveSourceObject="SOURCE_OBJECT",(it=exports.ReplicationStatusType||(exports.ReplicationStatusType={})).Complete="COMPLETE",it.Pending="PENDING",it.Failed="FAILED",it.Replica="REPLICA",(rt=exports.LifecycleStatusType||(exports.LifecycleStatusType={})).Enabled="Enabled",rt.Disabled="Disabled",(st=exports.RedirectType||(exports.RedirectType={})).Mirror="Mirror",st.Async="Async",(ct=exports.StatusType||(exports.StatusType={})).Enabled="Enabled",ct.Disabled="Disabled",(lt=exports.TierType||(exports.TierType={})).TierStandard="Standard",lt.TierExpedited="Expedited",lt.TierBulk="Bulk",(pt=exports.VersioningStatusType||(exports.VersioningStatusType={})).Enabled="Enabled",pt.Suspended="Suspended",pt.NotSet="",pt.Enable="Enabled",pt.Disable="",(ut=exports.AccessPointStatusType||(exports.AccessPointStatusType={})).Ready="READY",ut.Creating="CREATING",ut.Created="CREATED",ut.Deleting="DELETING",(dt=exports.TransferAccelerationStatusType||(exports.TransferAccelerationStatusType={})).Activating="AccelerationActivating",dt.Activated="AccelerationActivated",dt.Terminated="AccelerationTerminated",(ht=exports.MRAPMirrorBackRedirectPolicyType||(exports.MRAPMirrorBackRedirectPolicyType={})).ClosestFirst="Closest-First",ht.LatestFirst="Latest-First",(mt=exports.ResumableCopyEventType||(exports.ResumableCopyEventType={}))[mt.CreateMultipartUploadSucceed=1]="CreateMultipartUploadSucceed",mt[mt.CreateMultipartUploadFailed=2]="CreateMultipartUploadFailed",mt[mt.UploadPartCopySucceed=3]="UploadPartCopySucceed",mt[mt.UploadPartCopyFailed=4]="UploadPartCopyFailed",mt[mt.UploadPartCopyAborted=5]="UploadPartCopyAborted",mt[mt.CompleteMultipartUploadSucceed=6]="CompleteMultipartUploadSucceed",mt[mt.CompleteMultipartUploadFailed=7]="CompleteMultipartUploadFailed";const xt=[403,404,405];async function bt(e){var t,a,o;const{cancelToken:n}=e,i=()=>n&&!!n.reason;Be(e.checkpoint);const{data:r}=await ft.call(this,{bucket:e.srcBucket,key:e.srcKey,versionId:e.srcVersionId}),s=r.etag,c=+r["content-length"],l=await(async()=>{if("string"==typeof e.checkpoint){const{checkpoint:t}=e;let a=null;try{a=await P(t)}catch(e){const t=e;if("ENOENT"!==t.code)throw t}const o=a?a.isDirectory():t.endsWith("/"),n=o?g.resolve(t,"@@checkpoint-file-placeholder@@"):g.resolve(t),i=g.dirname(n);return await M(i),o?{filePath:n,filePathIsPlaceholder:!0}:{filePath:n,filePathIsPlaceholder:!1,record:a?await te(n):void 0}}return"object"==typeof e.checkpoint?{record:e.checkpoint}:{}})();await(async()=>{var e;if(null!=(e=l.record)&&e.copy_source_object_info){var t;const{last_modified:e,object_size:a}=null==(t=l.record)?void 0:t.copy_source_object_info;r["last-modified"]===e&&+r["content-length"]===a||(console.warn(`The file has been modified since ${new Date(e)}, so the checkpoint file is invalid, and specified file will be uploaded again.`),delete l.record)}})();const p=re(c,e.partSize||(null==(t=l.record)?void 0:t.part_size)||20971520,!0);l.record&&l.record.part_size!==p&&(console.warn("The partSize param does not equal the partSize in checkpoint file, so the checkpoint file is invalid, and specified file will be uploaded again."),delete l.record);let u=e.bucket||this.opts.bucket||"";const d=e.key;let h="",m=[];const f=function(e,t){const a=[];for(let o=0;;++o){const n=o*t,i=Math.min(t,e-n);if(a.push({offset:n,partSize:i,partNumber:o+1}),(o+1)*t>=e)break}return a}(c,p);let y=((null==(a=l.record)?void 0:a.parts_info)||[]).filter(e=>e.is_completed).reduce((e,t)=>e+t.copy_source_range_end-t.copy_source_range_start+1,0);const b=(null==(o=l.record)?void 0:o.parts_info)||[],k=new Map;b.forEach(e=>k.set(e.part_number,e));const v=()=>({bucket:u,key:d,part_size:p,upload_id:h,parts_info:b,copy_source_object_info:{last_modified:r["last-modified"],etag:r.etag,hash_crc64ecma:r["x-tos-hash-crc64ecma"]||"",object_size:+r["content-length"]}}),C=t=>{if(!e.copyEventListener)return;const a={bucket:u,uploadId:h,key:d,...t};l.filePath&&(a.checkpointFile=l.filePath),e.copyEventListener(a)};let E;!function(e){e[e.start=1]="start",e[e.uploadPartSucceed=2]="uploadPartSucceed",e[e.completeMultipartUploadSucceed=3]="completeMultipartUploadSucceed"}(E||(E={}));const R=t=>{e.progress&&(y===c&&t===E.uploadPartSucceed||e.progress(t===E.start&&0===c?0:c?y/c:1,v()))},D=ee(async()=>{if(l.filePath){const e=JSON.stringify(v(),null,2),t=g.dirname(l.filePath);await M(t),await B(l.filePath,e,"utf-8")}}),O=async(e,t)=>{let a=k.get(e.partNumber);const o=e.offset,n=Math.min(e.offset+p-1,c-1);a||(a={part_number:e.partNumber,copy_source_range_start:o,copy_source_range_end:n,is_completed:!1,etag:""},b.push(a),k.set(a.part_number,a)),t.err||(a.is_completed=!0,a.etag=t.res.ETag),await D();const i={partNumber:a.part_number,copySourceRangeEnd:a.copy_source_range_end,copySourceRangeStart:a.copy_source_range_start};if(t.err){const e=t.err;let a=exports.ResumableCopyEventType.UploadPartCopyFailed;return e instanceof T&&xt.includes(e.statusCode)&&(a=exports.ResumableCopyEventType.UploadPartCopyAborted),void C({type:a,err:e,copyPartInfo:i})}i.etag=t.res.ETag,y+=i.copySourceRangeEnd-i.copySourceRangeStart+1,C({type:exports.ResumableCopyEventType.UploadPartCopySucceed,copyPartInfo:i}),R(E.uploadPartSucceed)};if(l.record){u=l.record.bucket,h=l.record.upload_id;const e=new Set((l.record.parts_info||[]).filter(e=>e.is_completed).map(e=>e.part_number));m=f.filter(t=>!e.has(t.partNumber))}else{try{const{data:t}=await ie.call(this,x(e));if(i())throw new w("cancel uploadFile");var A;u=t.Bucket,h=t.UploadId,l.filePathIsPlaceholder&&(l.filePath=null==(A=l.filePath)?void 0:A.replace("@@checkpoint-file-placeholder@@",[(j={...e,bucket:u}).srcBucket,j.srcKey,j.srcVersionId,j.bucket,j.key,"copy"].filter(Boolean).join(".").replace(/[\\/]/g,""))),C({type:exports.ResumableCopyEventType.CreateMultipartUploadSucceed})}catch(e){const t=e;throw C({type:exports.ResumableCopyEventType.CreateMultipartUploadFailed,err:t}),t}m=f}var j;return R(E.start),0===c?(async()=>{let t=Re(e.srcBucket,e.srcKey);e.srcVersionId&&(t+="?versionId="+e.srcVersionId);const a={...e.headers,"x-tos-copy-source":t,"x-tos-copy-source-if-match":s},[o,n]=await z(gt.call(this,{bucket:e.bucket,key:e.key,headers:a,trafficLimit:e.trafficLimit}));if(o||!n)throw C({type:exports.ResumableCopyEventType.UploadPartCopyFailed}),o;return R(E.completeMultipartUploadSucceed),C({type:exports.ResumableCopyEventType.UploadPartCopySucceed,copyPartInfo:{partNumber:0,copySourceRangeStart:0,copySourceRangeEnd:0}}),C({type:exports.ResumableCopyEventType.CompleteMultipartUploadSucceed}),{...n,data:{ETag:n.headers.etag||"",Bucket:u,Key:d,Location:`http${this.opts.secure?"s":""}://${u}.${this.opts.endpoint}/${d}`,VersionID:n.headers["x-tos-version-id"],HashCrc64ecma:n.headers["x-tos-hash-crc64ecma"]}}})():(async()=>{let t=null,a=0;if(await Promise.all(Array.from({length:e.taskNum||1}).map(async()=>{for(;;){const o=a++;if(o>=m.length)return;const n=m[o];try{let t=Re(e.srcBucket,e.srcKey);e.srcVersionId&&(t+="?versionId="+e.srcVersionId);const a=`bytes=${n.offset}-${n.offset+n.partSize-1}`,o={...e.headers,"x-tos-copy-source":t,"x-tos-copy-source-if-match":s,"x-tos-copy-source-range":a};n.partSize||delete o["x-tos-copy-source-range"];const{data:r}=await yt.call(this,{bucket:u,key:d,uploadId:h,partNumber:n.partNumber,headers:o,trafficLimit:e.trafficLimit});if(i())throw new w("cancel resumableCopyObject");await O(n,{res:r})}catch(e){const a=e;if(kt(a))throw a;if(i())throw new w("cancel resumableCopyObject");t||(t=a),await O(n,{err:a})}}})),t)throw t;const o=(v().parts_info||[]).map(e=>({eTag:e.etag,partNumber:e.part_number})),[n,r]=await z($e.call(this,{bucket:u,key:d,uploadId:h,parts:o}));if(n||!r)throw C({type:exports.ResumableCopyEventType.CompleteMultipartUploadFailed}),n;C({type:exports.ResumableCopyEventType.CompleteMultipartUploadSucceed}),R(E.completeMultipartUploadSucceed);const c=v().copy_source_object_info.hash_crc64ecma,p=r.data.HashCrc64ecma;if(this.opts.enableCRC&&c&&p&&c!==p)throw new S(`validate file crc64 failed. Expect crc64 ${c}, actual crc64 ${p}. Please try again.`);return await(async()=>{l.filePath&&await I(l.filePath).catch(e=>{console.warn("remove checkpoint file failure, you can remove it by hand.\n",`checkpoint file path: ${l.filePath}\n`,e.message)})})(),r})()}function kt(e){return e instanceof w}async function vt(e){const t="string"==typeof e?{key:e}:e,a={};t.versionId&&(a.versionId=t.versionId);const o=U(null==t?void 0:t.headers),n=(null==t?void 0:t.response)||{};return Object.keys(n).forEach(e=>{const t=n[e];null!=t&&(a["response-"+e]=t)}),this._fetchObject(e,"GET",a,o,void 0,{axiosOpts:{responseType:"arraybuffer"}})}const Tt=["stream","buffer"];async function St(e){const t="string"==typeof e?{key:e}:e,a=U(t.headers);t.headers=a;const o=t.dataType||"stream";t.dataType=o,function(e){let t="node",a=[];if(t="node",a=Tt,!a.includes(e))throw new S(`The value of \`dataType\` only supports \`${a.join(" | ")}\` in node environment`)}(o);const n={},i=(null==t?void 0:t.response)||{};if(Object.keys(i).forEach(e=>{const t=i[e];null!=t&&(n["response-"+e]=t)}),function(e,t,a){function o(e,a){null==t[e]&&(t[e]=a)}a.length&&a.forEach(t=>{const a=W[t];if(!a)throw new S(`\`${t}\` isn't in keys of \`requestQueryMap\``);const n=e[t];if(null==n)return;if("string"==typeof a)return o(a,""+n);if(Array.isArray(a))return o(a[0],a[1](n));const i=a(n);Object.entries(i).forEach(([e,t])=>{o(e,t)})})}(t,n,["versionId","process","saveBucket","saveObject","responseCacheControl","responseContentDisposition","responseContentEncoding","responseContentLanguage","responseContentType","responseExpires"]),J(t,["ifMatch","ifModifiedSince","ifNoneMatch","ifUnmodifiedSince","ssecAlgorithm","ssecKey","ssecKeyMD5","range","trafficLimit"]),null==t.range&&(null!=t.rangeStart||null!=t.rangeEnd)){var r;const e=`bytes=${null!=t.rangeStart?""+t.rangeStart:""}-${null!=t.rangeEnd?""+t.rangeEnd:""}`;a.range=null!=(r=a.range)?r:e}let s=0,c=-1;const{dataTransferStatusChange:l,progress:p}=t,u=(e,t=0)=>{if(t<0)return;if(!l&&!p)return;s+=t,null==l||l({type:e,rwOnceBytes:t,consumedBytes:s,totalBytes:c});const a=c<0?0:0===c?e===exports.DataTransferType.Succeed?1:0:s/c;1===a?e===exports.DataTransferType.Succeed&&(null==p||p(a)):null==p||p(a)};u(exports.DataTransferType.Started);const[d,h]=await z(this._fetchObject(e,"GET",n,a,void 0,{axiosOpts:{responseType:"stream",onDownloadProgress:e=>{c=e.total,u(exports.DataTransferType.Rw,e.loaded-s)}}}));if(d||!h)throw u(exports.DataTransferType.Failed),d;let m=h.headers,f=h.data;c=+(m["content-length"]||0),K(f)&&(t.rateLimiter&&Pe(t.rateLimiter)&&(f=be(f,t.rateLimiter)),f=pe(f,e=>u(exports.DataTransferType.Rw,e)),f.on("end",()=>u(exports.DataTransferType.Succeed)),"buffer"===o&&(f=await(async e=>{let t=Buffer.from([]);return new Promise((a,o)=>{e.on("data",e=>{t=Buffer.concat([t,e])}),e.on("end",()=>{a(t)}),e.on("error",e=>{o(e)})})})(f)));const y={...h,data:{content:f,etag:m.etag||"",lastModified:m["last-modified"]||"",hashCrc64ecma:m["x-tos-hash-crc64ecma"]||"",ReplicationStatus:m[Se.HeaderReplicationStatus]}},g=Ie(m);return g&&(y.data.RestoreInfo=g),y}async function wt(e){return new Promise(async(t,a)=>{const o=await St.call(this,e),n=o.data.content,i=C(e.filePath);n.pipe(i),n.on("error",e=>i.destroy(e)),i.on("error",e=>a(e)),i.on("finish",()=>{const e={...o.data};delete e.content,t({...o,data:{...e}})})})}var Ct;(Ct=exports.DownloadEventType||(exports.DownloadEventType={}))[Ct.CreateTempFileSucceed=1]="CreateTempFileSucceed",Ct[Ct.CreateTempFileFailed=2]="CreateTempFileFailed",Ct[Ct.DownloadPartSucceed=3]="DownloadPartSucceed",Ct[Ct.DownloadPartFailed=4]="DownloadPartFailed",Ct[Ct.DownloadPartAborted=5]="DownloadPartAborted",Ct[Ct.RenameTempFileSucceed=6]="RenameTempFileSucceed",Ct[Ct.RenameTempFileFailed=7]="RenameTempFileFailed";const Et=[403,404,405];async function Rt(e){var t,a,o,n;const{cancelToken:i,versionId:r}=e,s=()=>i&&!!i.reason;Be(e.checkpoint);const c=await ft.call(this,{bucket:e.bucket,key:e.key,versionId:r}),{data:l}=c,p=l.etag,u=null!=(t=l["x-tos-symlink-target-size"])?t:0,d="Symlink"===l["x-tos-object-type"]?+u:+l["content-length"],h=await(async()=>{if("string"==typeof e.checkpoint){const{checkpoint:t}=e;let a=null;try{a=await P(t)}catch(e){const t=e;if("ENOENT"!==t.code)throw t}const o=a?a.isDirectory():t.endsWith("/"),n=o?g.resolve(t,"@@checkpoint-file-placeholder@@"):t,i=g.dirname(n);return await M(i),o?{filePath:n,filePathIsPlaceholder:!0}:{filePath:n,filePathIsPlaceholder:!1,record:a?await te(n):void 0}}return"object"==typeof e.checkpoint?{record:e.checkpoint}:{}})();await(async()=>{var e;if(null!=(e=h.record)&&e.object_info){var t;const{last_modified:e,object_size:a}=null==(t=h.record)?void 0:t.object_info;l["last-modified"]===e&&d===a||(console.warn(`The file has been modified since ${new Date(e)}, so the checkpoint file is invalid, and specified object will be downloaded again.`),delete h.record)}})();const m=e.partSize||(null==(a=h.record)?void 0:a.part_size)||20971520;h.record&&h.record.part_size!==m&&(console.warn("The partSize param does not equal the partSize in checkpoint file, so the checkpoint file is invalid, and specified object will be downloaded again."),delete h.record);let f=e.bucket||this.opts.bucket||"";const y=e.key,x=await(async()=>{let t=null;try{t=await P(e.filePath)}catch(e){const t=e;if("ENOENT"!==t.code)throw t}const a=(t?t.isDirectory():e.filePath.endsWith("/"))?g.resolve(e.filePath,y):e.filePath,o=g.dirname(a);return await M(o),a})(),[b,k]=await(async()=>{const t=e.tempFilePath?e.tempFilePath:x+".temp";let a=!0;try{await P(t)}catch(e){const t=e;if("ENOENT"!==t.code)throw t;a=!1}return[t,a]})();h.record&&(k||(console.warn("The temp file doesn't not exist so the checkpoint file is invalid, and specified object will be downloaded again."),delete h.record));let v=[];const E=function(e,t){const a=[];for(let o=0;;++o){const n=o*t,i=Math.min(t,e-n);if(a.push({offset:n,partSize:i,partNumber:o+1}),(o+1)*t>=e)break}return a}(d,m),D=((null==(o=h.record)?void 0:o.parts_info)||[]).filter(e=>e.is_completed).reduce((e,t)=>e+(t.range_end-t.range_start+1),0),O=(null==(n=h.record)?void 0:n.parts_info)||[],A=new Map;O.forEach(e=>A.set(e.part_number,e));const j=async()=>{const t=()=>({bucket:f,key:y,version_id:r,part_size:m,parts_info:O,file_info:{file_path:x,temp_file_path:b},object_info:{last_modified:l["last-modified"],etag:p,hash_crc64ecma:l["x-tos-hash-crc64ecma"]||"",object_size:d}}),a=t=>{if(!e.downloadEventChange)return;const a={bucket:f,versionId:r,key:y,filePath:x,...t};h.filePath&&(a.checkpointFile=h.filePath),e.downloadEventChange(a)};let o,n=D;!function(e){e[e.start=0]="start",e[e.downloadPartSucceed=1]="downloadPartSucceed",e[e.renameTempFileSucceed=2]="renameTempFileSucceed"}(o||(o={}));const i=a=>{e.progress&&(n===d&&a===o.downloadPartSucceed||e.progress(a===o.start&&0===d?0:d?n/d:1,t()))};let u=D;const{dataTransferStatusChange:k}=e,P=(e,t=0)=>{k&&(u+=t,null==k||k({type:e,rwOnceBytes:t,consumedBytes:u,totalBytes:d}))},j=ee(async()=>{if(h.filePath){const e=JSON.stringify(t(),null,2),a=g.dirname(h.filePath);await M(a),await B(h.filePath,e,"utf-8")}}),_=async(e,t)=>{let r=A.get(e.partNumber);const s=e.offset,c=Math.min(e.offset+m-1,d-1);r||(r={part_number:e.partNumber,range_start:s,range_end:c,hash_crc64ecma:"",is_completed:!1},O.push(r),A.set(r.part_number,r)),t.err||(r.is_completed=!0,r.hash_crc64ecma=t.res.rangeHashCrc64ecma),await j();const l={partNumber:r.part_number,rangeStart:s,rangeEnd:c};if(t.err){const e=t.err;let o=exports.DownloadEventType.DownloadPartFailed;return e instanceof T&&Et.includes(e.statusCode)&&(o=exports.DownloadEventType.DownloadPartAborted),void a({type:o,err:e,downloadPartInfo:l})}n+=l.rangeEnd-l.rangeStart+1,a({type:exports.DownloadEventType.DownloadPartSucceed,downloadPartInfo:l}),i(o.downloadPartSucceed)};if(h.record){f=h.record.bucket;const e=new Set((h.record.parts_info||[]).filter(e=>e.is_completed).map(e=>e.part_number));v=E.filter(t=>!e.has(t.partNumber))}else{try{await B(b,"",{flag:"w+"})}catch(e){const t=e;throw a({type:exports.DownloadEventType.CreateTempFileFailed,err:t}),t}var L;h.filePathIsPlaceholder&&(h.filePath=null==(L=h.filePath)?void 0:L.replace("@@checkpoint-file-placeholder@@",function(e,t,a){return`${e}_${t}.${a}.json`.replace(/[\\/]/g,"")}(f,y,r))),a({type:exports.DownloadEventType.CreateTempFileSucceed}),P(exports.DataTransferType.Started),v=E}i(o.start),0===d?await(async()=>{})():await(async()=>{let a=null,o=0;if(await Promise.all(Array.from({length:e.taskNum||1}).map(async()=>{for(;;){const t=o++;if(t>=v.length)return;const n=v[t];let i=0;try{const t=await St.call(this,{bucket:f,key:y,versionId:r,headers:{"if-match":p,range:`bytes=${n.offset}-${Math.min(n.offset+n.partSize-1,d-1)}`},trafficLimit:e.trafficLimit,rateLimiter:e.rateLimiter,dataTransferStatusChange(e){e.type===exports.DataTransferType.Rw&&(s()||(i+=e.rwOnceBytes,P(exports.DataTransferType.Rw,e.rwOnceBytes)))}});let a=t.data.content;const o=new de;if(this.opts.enableCRC&&(a=Te(a,o)),await new Promise((e,t)=>{const o=C(b,{start:n.offset,flags:"r+"});let i=!1,r=null;o.on("close",()=>{i?t(r):e(void 0)}),o.on("error",e=>{i=!0,r=e}),a.pipe(o),a.on("error",e=>o.destroy(e)),a.on("data",(function e(){s()&&(t(new w("cancel downloadFile")),o.end(),a.unpipe(o),a.off("data",e))}))}),s())throw new w("cancel downloadFile");await _(n,{res:{...t.data,rangeHashCrc64ecma:o.getCrc64()}})}catch(e){const t=e;if(u-=i,i=0,H(t))throw t;if(s())throw new w("cancel downloadFile");a||(a=t),await _(n,{err:t})}}})),a)throw a;const n=c.data["x-tos-hash-crc64ecma"];if(this.opts.enableCRC&&n){const e=function(e){var t,a;let o="0";const n=null!=(t=null==(a=e.parts_info)||null==a.sort?void 0:a.sort((e,t)=>e.part_number-t.part_number))?t:[];for(const e of n)o=he(o,e.hash_crc64ecma,e.range_end-e.range_start+1);return o}(t());if(e!==n)throw new S(`validate file crc64 failed. Expect crc64 ${n}, actual crc64 ${e}. Please try again.`)}})();try{"function"==typeof e.customRenameFileAfterDownloadCompleted?await e.customRenameFileAfterDownloadCompleted(b,x):await R(b,x)}catch(e){const t=e;throw a({type:exports.DownloadEventType.RenameTempFileFailed,err:t}),P(exports.DataTransferType.Failed),t}return a({type:exports.DownloadEventType.RenameTempFileSucceed}),i(o.renameTempFileSucceed),P(exports.DataTransferType.Succeed),await(async()=>{h.filePath&&await I(h.filePath).catch(e=>{console.warn("remove checkpoint file failure, you can remove it by hand.\n",`checkpoint file path: ${h.filePath}\n`,e.message)})})(),c};try{return await j()}finally{}}function Pt(e){return!e||80===e||443===e}const Bt="request";class It{constructor(e,t){this.options=void 0,this.credentials=void 0,this.signature=(e,t,a)=>{a||(a=this.credentials);const o=[],n=this.credentialString(e.datetime);return o.push(this.options.algorithm+" Credential="+a.GetAccessKey()+"/"+n),o.push("SignedHeaders="+this.signedHeaders(e)),o.push("Signature="+this.authorization(e,a,0)),o.join(", ")},this.signatureHeader=(e,t,a)=>{e.datetime=this.getDateTime();const o=new Map;e.headers||(e.headers={}),e.headers.host=""+e.host,Pt(e.port)||(e.headers.host+=":"+e.port),e.endpoints&&(e.headers.host=`${this.options.bucket}.${e.endpoints}`),o.set("host",e.headers.host),o.set("x-tos-date",e.datetime),o.set("x-tos-content-sha256",this.hexEncodedBodyHash()),this.options.securityToken&&o.set("x-tos-security-token",this.options.securityToken),o.forEach((t,a)=>{a.startsWith("x-tos")&&(e.headers[a]=t)}),e.path=this.getEncodePath(e.path);const n=this.signature(e,0,a);return o.set("authorization",n),o},this.gnrCopySig=(e,t)=>({key:"",value:""}),this.getSignature=(e,t)=>({key:"",value:""}),this.getSignatureQuery=(e,t)=>{e.datetime=this.getDateTime(),e.headers||(e.headers={}),e.headers.host=""+e.host,Pt(e.port)||(e.headers.host+=":"+e.port),e.path=this.getEncodePath(e.path),e.endpoints&&(e.headers.host=`${this.options.bucket}.${e.endpoints}`),e.headers["X-Tos-Date"]=e.datetime;const a=this.credentialString(e.datetime),o={...e.query||{},"X-Tos-Algorithm":this.options.algorithm,"X-Tos-Content-Sha256":this.hexEncodedBodyHash(),"X-Tos-Credential":this.credentials.GetAccessKey()+"/"+a,"X-Tos-Date":e.datetime,"X-Tos-Expires":""+t,"X-Tos-SignedHeaders":this.signedHeaders(e)};return this.options.securityToken&&(o["X-Tos-Security-Token"]=this.options.securityToken),e.query=L(o),o["X-Tos-Signature"]=this.authorization(e,this.credentials,t),o},this.getSignaturePolicyQuery=(e,t)=>{e.datetime=this.getDateTime();const a=this.credentialString(e.datetime),o={"X-Tos-Algorithm":this.options.algorithm,"X-Tos-Credential":this.credentials.GetAccessKey()+"/"+a,"X-Tos-Date":e.datetime,"X-Tos-Expires":""+t,"X-Tos-Policy":ze(Ne(JSON.stringify(e.policy),"utf-8"),"base64")};return this.options.securityToken&&(o["X-Tos-Security-Token"]=this.options.securityToken),e.query=L(o),o["X-Tos-Signature"]=this.authorization(e,this.credentials,t),o},this.hexEncodedBodyHash=()=>"UNSIGNED-PAYLOAD",this.authorization=(e,t,a)=>{if(!e.datetime)return"";const o=this.getSigningKey(t,e.datetime.substr(0,8));return _e(o,this.stringToSign(e.datetime,e),"hex")},this.getDateTime=()=>new Date((new Date).toUTCString()).toISOString().replace(/\..+/,"").replace(/-/g,"").replace(/:/g,"")+"Z",this.credentialString=e=>this.createScope(e.substr(0,8),this.options.region,this.options.serviceName),this.createScope=(e,t,a)=>[e.substr(0,8),t,a,Bt].join("/"),this.getSigningKey=(e,t)=>{const a=_e(e.GetSecretKey(),t),o=_e(a,this.options.region),n=_e(o,this.options.serviceName);return _e(n,Bt)},this.stringToSign=(e,t)=>{if(!this.options.algorithm)return"";const a=[];a.push(this.options.algorithm),a.push(e),a.push(this.credentialString(e));const o="policy"in t?this.canonicalStringPolicy(t):this.canonicalString(t);return a.push(this.hexEncodedHash(o)),a.join("\n")},this.hexEncodedHash=e=>Le(e,"hex"),this.canonicalString=e=>{const t=[];return t.push(e.method),t.push(e.path),t.push(this.getEncodePath(e.query,!1)),t.push(this.canonicalHeaders(e)+"\n"),t.push(this.signedHeaders(e)),t.push(this.hexEncodedBodyHash()),t.join("\n")},this.canonicalStringPolicy=e=>{const t=[];return t.push(this.getEncodePath(e.query,!1)),t.push(this.hexEncodedBodyHash()),t.join("\n")},this.canonicalHeaders=e=>{const t=[],a=Mt(e.headers);for(let o of a){const a=e.headers[o];o=o.toLowerCase(),t.push(o+":"+this.canonicalHeaderValues(a.toString()))}return t.join("\n")},this.canonicalHeaderValues=e=>e.replace(/\s+/g," ").replace(/^\s+|\s+$/g,""),this.signedHeaders=e=>{const t=[],a=Mt(e.headers);for(let e of a)e=e.toLowerCase(),t.push(e);return t.sort().join(";")},this.options=e,this.credentials=t}getEncodePath(e,t=!0){if(!e)return"";let a=e;return t&&(a=e.replace(/%2F/g,"/")),a=a.replace(/\(/g,"%28"),a=a.replace(/\)/g,"%29"),a=a.replace(/!/g,"%21"),a=a.replace(/\*/g,"%2A"),a=a.replace(/\'/g,"%27"),a}}class Dt{constructor(e,t,a){this.securityToken=void 0,this.secretAccessKey=void 0,this.accessKeyId=void 0,this.accessKeyId=a,this.secretAccessKey=t,this.securityToken=e}GetAccessKey(){return this.accessKeyId}GetSecretKey(){return this.secretAccessKey}}function Mt(e){const t=[];return Object.keys(e||{}).forEach(a=>{("host"===a||a.startsWith("x-tos-"))&&null!=e[a]&&t.push(a)}),t.sort()}function Ot(e){const{tosOpts:t,...a}=e,o=new(t.isHttps?k.Agent:b.Agent)({...a,keepAlive:!0,rejectUnauthorized:t.enableVerifySSL,timeout:t.idleConnectionTime});o.maxFreeSockets=Infinity,o.maxTotalSockets=t.maxConnections;const n=o.createConnection;return o.createConnection=function(...e){const a=n.call(this,...e);let o=!1,i=!1,r=null;return process.nextTick(()=>{i||(r=setTimeout(()=>{o=!0},t.connectionTimeout))}),a.on("connect",()=>{i=!0,r&&clearTimeout(r),o&&a.destroy(new Error("Connect timeout"))}),a},o}class At{constructor(e){this.opts=void 0,this.axiosInst=void 0,this.userAgent=void 0,this.httpAgent=void 0,this.httpsAgent=void 0,this.getObjectPath=e=>{const t="string"!=typeof e&&e.bucket||this.opts.bucket,a="string"==typeof e?e:e.key;if(!t)throw new S("Must provide bucket param");return`/${t}/${encodeURIComponent(a)}`},this.setObjectContentTypeHeader=(e,t)=>{if(null!=t["content-type"])return;let a="application/octet-stream";const o=(e=>"string"==typeof e?e:e.key)(e);this.opts.autoRecognizeContentType&&(a=function(e){const t=e.lastIndexOf(".");if(t<=0)return;const a=e.slice(t+1).toLowerCase();return ce[a]}(o)||a),a&&(t["content-type"]=a)},this.getNormalDataFromError=X,this.opts=this.normalizeOpts(e),this.httpAgent=Ot({tosOpts:{...this.opts,isHttps:!1}}),this.httpsAgent=Ot({tosOpts:{...this.opts,isHttps:!this.opts.proxyHost}}),this.userAgent=this.getUserAgent(),this.axiosInst=(e=>{const t=a.create();t.defaults.auth=void 0,t.defaults.responseType="json",t.defaults.params=void 0,t.defaults.headers={},t.defaults.withCredentials=!1,t.defaults.maxContentLength=-1,t.defaults.maxBodyLength=-1,t.defaults.maxRedirects=0,t.defaults.validateStatus=function(e){return e>=200&&e<300},t.defaults.decompress=!1,t.defaults.transitional={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1};const n=e=>{var t;return e.headers=e.headers||e.header||(null==e||null==(t=e.response)?void 0:t.headers)||{},e};function i(e){Object.entries(e).forEach(([t,a])=>{const[o,n]=function(e){try{return[null,decodeURI(a)]}catch(e){return[e,null]}}();if(o||null==n||n===a)return;let i=[];const r=(""+a).match(/./gu),s=n.match(/./gu);for(let e=0,t=0;e<s.length;){const a=s[e];if(a===r[t]){i.push(a),++e,++t;continue}const o=encodeURIComponent(a);a.length>1||a.charCodeAt(0)>=128?i.push(a):i.push(o),++e,t+=o.length}e[t]=i.join("")})}return t.interceptors.response.use(n,e=>(n(e),Promise.reject(e))),t.interceptors.response.use(e=>e.headers?(i(e.headers),e):e,async e=>{var t;if(!a.isAxiosError(e))return Promise.reject(e);const o=null==(t=e.response)?void 0:t.headers;return o?(i(o),Promise.reject(e)):Promise.reject(e)}),t.interceptors.response.use(void 0,async a=>{var n;const{config:i}=a;if(!i)return Promise.reject(a);i.__retryConfig__||(i.__retryConfig__={});const r=i.__retryConfig__,s=null!=(n=r.retryCount)?n:0;let c=i.data;const l=(()=>{if(i.data&&i.data instanceof o.Readable){const e=null==r.makeRetryStream?void 0:r.makeRetryStream();if(!e)return!1;c=e}return!0})();if(!((function(e){var t;return!e.response&&Boolean(e.code)||e.response&&!(null!=(t=e.response.headers)&&t["x-tos-request-id"])}(a)||function(e){if(!e.response)return!1;const{status:t}=e.response;return 429===t||t>=500}(a))&&s<e&&l))return Promise.reject(a);const p=i.__retrySignature__;if(p){const{signOpt:e,sigInst:t}=p;t.signatureHeader(e).forEach((e,t)=>{i.headers[t]=e})}Me("retryConfig: ",i);const u={...i,data:c,__retryConfig__:{...r,retryCount:s+1}};return null==r.beforeRetry||r.beforeRetry(),t(u)}),t})(this.opts.maxRetryCount)}normalizeOpts(e){var t;["accessKeyId","accessKeySecret","stsToken","region","endpoint"].forEach(t=>{const a=e[t];"string"==typeof a&&(e[t]=a.trim())});const a=["accessKeyId","accessKeySecret","region"].filter(t=>!e[t]).join(", ");if(a)throw new S(`lack params: ${a}.`);const o=e.endpoint||`tos-${e.region}.volces.com`;if(!o)throw new S("the value of param region is invalid, correct values are cn-beijing, cn-nantong etc.");if(o.includes("s3"))throw new S("do not support s3 endpoint, please use tos endpoint.");const n=null==e.secure||!!e.secure,i=(e,t)=>null==e?t:e;return{...e,endpoint:o,secure:n,enableVerifySSL:i(e.enableVerifySSL,!0),autoRecognizeContentType:i(e.autoRecognizeContentType,!0),requestTimeout:i(e.requestTimeout,12e4),connectionTimeout:i(e.connectionTimeout,1e4),maxConnections:i(e.maxConnections,1024),idleConnectionTime:i(e.idleConnectionTime,3e4),maxRetryCount:i(e.maxRetryCount,3),enableCRC:null!=(t=e.enableCRC)&&t,requestAdapter:void 0}}getUserAgent(){const e=(()=>{const e=v.type();return{Linux:"linux",Darwin:"darwin",Windows_NT:"windows"}[e]||e})(),t=process.version.replaceAll("v","");return[`ve-tos-nodejs-sdk/v2.7.4 (${e}/${process.arch};nodejs${t})`,(()=>{const{userAgentProductName:e,userAgentSoftName:t,userAgentSoftVersion:a}=this.opts;let o=Object.entries(this.opts.userAgentCustomizedKeyValues||{}).map(([e,t])=>`${e}/${t}`).join(";");return o=o?`(${o})`:"",e||t||a||o?[[e,t,a].map(e=>e||"undefined").join("/"),o].filter(Boolean).join(" "):""})()].filter(Boolean).join(" -- ")}async fetch(e,t,o,n,i,r){const s=(null==r?void 0:r.handleResponse)||(e=>e.data);if(i&&(null==r?void 0:r.needMd5)){const e=Ue(JSON.stringify(i),"base64");n["content-md5"]=e}const[c,l]=(()=>null!=r&&r.subdomainBucket&&this.opts.forcePathStyle?[this.opts.endpoint,`/${r.subdomainBucket}${t}`]:null!=r&&r.subdomainBucket&&!this.opts.isCustomDomain?/^(\d|:)/.test(this.opts.endpoint)?[this.opts.endpoint,`/${r.subdomainBucket}${t}`]:[`${null==r?void 0:r.subdomainBucket}.${this.opts.endpoint}`,t]:[this.opts.endpoint,t])();t=l,n=(e=>{const t={};return Object.entries(e).forEach(([e,a])=>{t[e]=(""+a).match(/./gu).map(e=>e.length>1||e.charCodeAt(0)>=128?encodeURIComponent(e):e).join("")}),t})(n);const p={endpoints:void 0,bucket:"",method:e,headers:{...n},path:t,query:L(o),host:c},u=new Dt(this.opts.stsToken,this.opts.accessKeySecret,this.opts.accessKeyId),d=new It({algorithm:"TOS4-HMAC-SHA256",region:this.opts.region,serviceName:"tos",bucket:"",securityToken:this.opts.stsToken},u),h=d.signatureHeader(p),m={...n},f={method:e,baseURL:`http${this.opts.secure?"s":""}://${c}`,url:t,params:o,headers:m,data:i};h.forEach((e,t)=>{f.headers[t]=e});const y=N(this.opts.proxy);if(null!=y&&y.url&&!this.opts.proxyHost)f.baseURL=y.url,null!=y&&y.needProxyParams&&(f.params["x-proxy-tos-host"]=c,delete m.host);else if(this.opts.proxyHost){if(!this.opts.proxyPort)throw new S("The `proxyPort` is required if `proxyHost` is truly.");f.proxy={host:this.opts.proxyHost,port:this.opts.proxyPort,protocol:"http"}}m["user-agent"]=this.userAgent,this.opts.requestTimeout>0&&Infinity!==this.opts.requestTimeout&&(f.timeout=this.opts.requestTimeout),f.httpAgent=this.httpAgent,f.httpsAgent=this.httpsAgent;try{const e={...f};delete e.httpAgent,delete e.httpsAgent,Me("reqOpts: ",e);const t=await this.axiosInst({maxBodyLength:Infinity,maxContentLength:Infinity,adapter:this.opts.requestAdapter,...f,...(null==r?void 0:r.axiosOpts)||{},__retrySignature__:{signOpt:p,sigInst:d}});return{data:s(t),statusCode:t.status,headers:t.headers,requestId:t.headers["x-tos-request-id"],id2:t.headers["x-tos-id-2"]}}catch(e){var g,x;if(a.isAxiosError(e)&&null!=(g=e.response)&&null!=(x=g.headers)&&x["x-tos-request-id"]){const t=e.response;throw Me("TosServerError response: ",t),new T(t)}throw Me("err: ",e),e}}async fetchBucket(e,t,a,o,n,i){const r=e||this.opts.bucket;if(!r)throw new S("Must provide bucket param");return this.fetch(t,"/",a,o,n,{...i,subdomainBucket:r})}async _fetchObject(e,t,a,o,n,i){const r="string"!=typeof e&&e.bucket||this.opts.bucket,s="string"==typeof e?e:e.key;if(!r)throw new S("Must provide bucket param");return we(s),this.fetch(t,"/"+encodeURIComponent(s),a,o,n,{...i,subdomainBucket:r})}getSignatureQuery(e){const t=new Dt(this.opts.stsToken,this.opts.accessKeySecret,this.opts.accessKeyId),a=new It({algorithm:"TOS4-HMAC-SHA256",region:this.opts.endpoint,serviceName:"tos",bucket:e.bucket,securityToken:this.opts.stsToken},t);return"policy"in e?a.getSignaturePolicyQuery({policy:e.policy},e.expires):a.getSignatureQuery({method:e.method,path:e.path,endpoints:e.subdomain?e.endpoint:void 0,host:e.endpoint,query:e.query},e.expires)}normalizeBucketInput(e){return"string"==typeof e?{bucket:e}:e}normalizeObjectInput(e){return"string"==typeof e?{key:e}:e}}async function jt(e={}){const{...t}=e,a=await this.fetchBucket(e.bucket,"GET",j(t),{}),o=O(a.data);return o("CommonPrefixes"),o("Contents"),o("Versions"),o("DeleteMarkers"),a}async function _t(e={}){const{...t}=e,a=await this.fetchBucket(e.bucket,"GET",j({versions:"",...t}),{}),o=O(a.data);return o("CommonPrefixes"),o("Versions"),o("DeleteMarkers"),a}async function Lt(e={}){const{listOnlyOnce:t=!1}=e;let a;if(e.maxKeys||(e.maxKeys=1e3),t)a=await Ut.call(this,e);else{const t=e.maxKeys;let o={...e,maxKeys:t};for(;;){const e=await Ut.call(this,o);if(null==a?a=e:(a={...e,data:a.data},a.data.KeyCount+=e.data.KeyCount,a.data.IsTruncated=e.data.IsTruncated,a.data.NextContinuationToken=e.data.NextContinuationToken,a.data.Contents=a.data.Contents.concat(e.data.Contents),a.data.CommonPrefixes=a.data.CommonPrefixes.concat(e.data.CommonPrefixes)),!e.data.IsTruncated||a.data.KeyCount>=t)break;o.continuationToken=e.data.NextContinuationToken,o.maxKeys=o.maxKeys-e.data.KeyCount}}return a}async function Ut(e){const{...t}=e,a=await this.fetchBucket(e.bucket,"GET",{"list-type":2,...j(t)},{}),o=O(a.data);return o("CommonPrefixes"),o("Contents"),a}class Nt extends At{modifyAxiosInst(){this.axiosInst.interceptors.request.use(e=>{const t=e.headers||{};return delete t.authorization,t.host=this.parsedPolicyUrlVal.host,e.baseURL=this.parsedPolicyUrlVal.origin,e.paramsSerializer=e=>{const t=Q(e);return[this.parsedPolicyUrlVal.search,t].filter(e=>e.trim()).join("&")},e})}constructor(e){super({...e,bucket:"fake-bucket",region:"fake-region",accessKeyId:"fake-accessKeyId",accessKeySecret:"fake-accessKeySecret",endpoint:"fake-endpoint.com"}),this.shareLinkClientOpts=void 0,this.parsedPolicyUrlVal=void 0,this.headObject=ft,this.getObjectV2=St,this.listObjects=jt,this.listObjectsType2=Lt,this.listObjectVersions=_t,this.downloadFile=Rt,this.shareLinkClientOpts=e,this.parsedPolicyUrlVal=this.initParsedPolicyUrlVal(),this.modifyAxiosInst()}initParsedPolicyUrlVal(){const e=this.shareLinkClientOpts.policyUrl.match(/(https?:\/\/(?:[^@]+@)?([^/?]+))[^?]*\?(.+)/);if(!e)throw new S("the `policyUrl` param is invalid");return{origin:e[1],host:e[2],search:e[3]}}}async function zt(e={}){const t={};(null==e?void 0:e.projectName)&&J({...e,headers:t},["projectName"]);const a=await this.fetch("GET","/",{},t);return O(a.data)("Buckets"),a}async function Ft(e){const t=e.bucket||this.opts.bucket;if(t){if(t.length<3||t.length>63)throw new S("invalid bucket name, the length must be [3, 63]");if(!/^([a-z]|-|\d)+$/.test(t))throw new S("invalid bucket name, the character set is illegal");if(/^-/.test(t)||/-$/.test(t))throw new S("invalid bucket name, the bucket name can be neither starting with '-' nor ending with '-'")}const a=e.headers=U(e.headers);return J(e,["acl","grantFullControl","grantRead","grantReadAcp","grantWrite","grantWriteAcp","storageClass","azRedundancy","bucketType"]),(null==e?void 0:e.projectName)&&J(e,["projectName"]),await this.fetchBucket(e.bucket,"PUT",{},a)}async function qt(e){return this.fetchBucket(e,"DELETE",{},{})}async function Kt(e){return this.fetchBucket(e,"HEAD",{},{},void 0,{handleResponse:e=>({...e.headers,ProjectName:e.headers[Se.HeaderProjectName]})})}async function $t(e){const{bucket:t,storageClass:a}=e;return this.fetchBucket(t,"PUT",{storageClass:""},{"x-tos-storage-class":a})}async function Ht(e){const t={};return e.acl&&(t["x-tos-acl"]=e.acl),await this.fetchBucket(e.bucket,"PUT",{acl:""},t,e.aclBody,{needMd5:!0})}async function Gt(e){const t=await this.fetchBucket(e,"GET",{acl:""},{});return O(t.data)("Grants"),t}async function Vt(e){return Wt.call(this,e)}async function Wt(e){const t=(e=this.normalizeObjectInput(e)).headers=U(e.headers);J(e,["contentLength","contentMD5","contentSHA256","cacheControl","contentDisposition","contentEncoding","contentLanguage","contentType","expires","acl","grantFullControl","grantRead","grantReadAcp","grantWrite","grantWriteAcp","ssecAlgorithm","ssecKey","ssecKeyMD5","serverSideEncryption","serverSideDataEncryption","meta","websiteRedirectLocation","storageClass","trafficLimit","callback","callbackVar","forbidOverwrite","ifMatch"]),this.setObjectContentTypeHeader(e,t);const a=Ce(e.body,t),o=null!=a;o||!e.dataTransferStatusChange&&!e.progress||console.warn("Don't get totalSize of putObject's body, the `dataTransferStatusChange` and `progress` callback will not trigger. You can use `putObjectFromFile` instead");let n=0;const{dataTransferStatusChange:i,progress:r}=e,s=(e,t=0)=>{if(!o||t<0)return;if(!i&&!r)return;n+=t,null==i||i({type:e,rwOnceBytes:t,consumedBytes:n,totalBytes:a});const s=0===a?e===exports.DataTransferType.Succeed?1:0:n/a;1===s?e===exports.DataTransferType.Succeed&&(null==r||r(s)):null==r||r(s)},c=await Ee({body:e.body,dataTransferCallback:e=>s(exports.DataTransferType.Rw,e),makeRetryStream:e.makeRetryStream,enableCRC:this.opts.enableCRC,rateLimiter:e.rateLimiter});s(exports.DataTransferType.Started);const[l,p]=await z((async()=>{const a=await this._fetchObject(e,"PUT",{},t,c.body||"",{handleResponse:t=>{var a;const o={...t.headers};return null!=(a=e)&&a.callback&&t.data&&(o.CallbackResult=""+JSON.stringify(t.data)),o},axiosOpts:{__retryConfig__:{beforeRetry:()=>{n=0,null==c.beforeRetry||c.beforeRetry()},makeRetryStream:c.makeRetryStream},onUploadProgress:e=>{s(exports.DataTransferType.Rw,e.loaded-n)}}});return this.opts.enableCRC&&c.crc&&Y(c.crc,a.headers),a})());if(l||!p)throw s(exports.DataTransferType.Failed),l;return s(exports.DataTransferType.Succeed),p}async function Jt(e){const t=U(e.headers);if(!t["content-length"]){const a=await P(e.filePath);t["content-length"]=""+a.size}const a=ae(()=>E(e.filePath));try{return await Wt.call(this,{...e,body:a.make(),headers:t,makeRetryStream:a.make})}catch(e){throw oe(a.getLastStream(),e),e}}async function Qt(e){const t=e.headers=U(e.headers);return J(e,["acl","grantFullControl","grantRead","grantReadAcp","grantWriteAcp","ssecAlgorithm","ssecKey","ssecKeyMD5","meta","storageClass"]),await this._fetchObject(e,"POST",{fetch:""},t,{URL:e.url,IgnoreSameKey:e.ignoreSameKey,ContentMD5:e.contentMD5},{needMd5:!0})}async function Xt(e){const t=e.headers=U(e.headers);return J(e,["acl","grantFullControl","grantRead","grantReadAcp","grantWriteAcp","ssecAlgorithm","ssecKey","ssecKeyMD5","meta","storageClass"]),await this._fetchObject(e,"POST",{fetchTask:""},t,{URL:e.url,IgnoreSameKey:e.ignoreSameKey,ContentMD5:e.contentMD5,Object:e.key},{needMd5:!0})}function Yt(e){we(e);const t="string"==typeof e?{key:e}:e,a=t.alternativeEndpoint||this.opts.endpoint,o=!t.alternativeEndpoint&&!t.isCustomDomain,n=t.bucket||this.opts.bucket||"";if(o&&!n)throw new S("Must provide bucket param");const[i,r,s]=(()=>{const e=encodeURIComponent(t.key),i=t.key.split("/").map(e=>encodeURIComponent(e)).join("/");return o?[`${n}.${a}`,"/"+i,"/"+e]:[a,"/"+i,"/"+e]})(),c=t.query||{},l=(e,t)=>{null==c[e]&&null!=t&&(c[e]=t)},p=t.response||{};Object.keys(p).forEach(e=>{const t=e,a=j(t);l("response-"+a,p[t])}),t.versionId&&l("versionId",t.versionId);const u=this.getSignatureQuery({bucket:n,method:t.method||"GET",path:s,endpoint:a,subdomain:o,expires:t.expires||1800,query:c}),d=N(this.opts.proxy);let h=`http${this.opts.secure?"s":""}://${i}`;return null!=d&&d.url&&(h=d.url.replace(/\/+$/g,""),null!=d&&d.needProxyParams&&(u["x-proxy-tos-host"]=i)),`${h}${r}?${Object.keys(u).map(e=>`${encodeURIComponent(e)}=${encodeURIComponent(u[e])}`).join("&")}`}async function Zt(e){const t="string"==typeof e?{key:e}:e,a={};return t.versionId&&(a.versionId=t.versionId),t.skipTrash&&(a.skipTrash=t.skipTrash),t.recursive&&(a.recursive=t.recursive),await this._fetchObject(e,"DELETE",a,{},{},{handleResponse:e=>e.headers})}async function ea(e){return e.headers=e.headers||{},J(e,["recursiveMkdir","forbidOverwrite"]),this._fetchObject(e,"PUT",{rename:"",name:e.newKey},e.headers,"")}async function ta(e){const t={Quiet:e.quiet,Objects:e.objects.map(e=>({Key:e.key,VersionId:e.versionId}))},a={delete:""};e.skipTrash&&(a.skipTrash=e.skipTrash),e.recursive&&(a.recursive=e.recursive);const o=await this.fetchBucket(e.bucket,"POST",a,{},t),n=O(o.data);return n("Deleted"),n("Error"),o}async function aa(e){const t="string"==typeof e?{key:e}:e,a={acl:""};t.versionId&&(a.versionId=t.versionId);const o=await this._fetchObject(e,"GET",a,{});return O(o.data)("Grants"),o}async function oa(e){const t=e.headers=U(e.headers),a={acl:""};return e.versionId&&(a.versionId=e.versionId),J(e,["acl"]),this._fetchObject(e,"PUT",a,t,e.aclBody)}async function na(e){return this._fetchObject(e,"DELETE",{uploadId:e.uploadId},{})}async function ia(e={}){const{...t}=e,a=await this.fetchBucket(e.bucket,"GET",{uploads:"",...j(t)},{}),o=O(a.data);return o("Uploads"),o("CommonPrefixes"),a}async function ra(e){const t=e=this.normalizeObjectInput(e),a=e.headers=U(e.headers);J(e,["contentLength","cacheControl","contentDisposition","contentEncoding","contentLanguage","contentType","expires","acl","grantFullControl","grantRead","grantReadAcp","grantWriteAcp","meta","websiteRedirectLocation","storageClass","trafficLimit"]),this.setObjectContentTypeHeader(e,a);const o=Ce(e.body,a),n=null!=o;if(!n)throw new S("appendObject needs to know the content length in advance");if(a["content-length"]=a["content-length"]||""+o,this.opts.enableCRC&&0!==e.offset&&!e.preHashCrc64ecma)throw new S("must provide preHashCrc64ecma if enableCRC is true and offset is non-zero");let i=0;const{dataTransferStatusChange:r,progress:s}=e,c=(e,t=0)=>{if(!n||t<0)return;if(!r&&!s)return;i+=t,null==r||r({type:e,rwOnceBytes:t,consumedBytes:i,totalBytes:o});const a=0===o?e===exports.DataTransferType.Succeed?1:0:i/o;1===a?e===exports.DataTransferType.Succeed&&(null==s||s(a)):null==s||s(a)},l=await Ee({body:e.body,dataTransferCallback:e=>c(exports.DataTransferType.Rw,e),makeRetryStream:void 0,enableCRC:this.opts.enableCRC,rateLimiter:e.rateLimiter});c(exports.DataTransferType.Started);const[p,u]=await z((async()=>{const n=await this._fetchObject(e,"POST",{append:"",offset:t.offset},a,l.body||"",{handleResponse:e=>({...e.headers,nextAppendOffset:+e.headers["x-tos-next-append-offset"],hashCrc64ecma:e.headers["x-tos-hash-crc64ecma"]}),axiosOpts:{__retryConfig__:{beforeRetry:()=>{i=0,null==l.beforeRetry||l.beforeRetry()},makeRetryStream:l.makeRetryStream},onUploadProgress:e=>{c(exports.DataTransferType.Rw,e.loaded-i)}}});return this.opts.enableCRC&&l.crc&&Y(he(t.preHashCrc64ecma||"0",l.crc.getCrc64(),o),n.headers),n})());if(p||!u)throw c(exports.DataTransferType.Failed),p;return c(exports.DataTransferType.Succeed),u}async function sa(e){const t="string"==typeof e?{key:e}:e,a=t.headers=U(t.headers);J(t,["cacheControl","contentDisposition","contentEncoding","contentLanguage","contentType","expires","meta"]);const o={metadata:""};return t.versionId&&(o.versionId=t.versionId),this._fetchObject(e,"POST",o,a)}async function ca(e){we(e),e=this.normalizeObjectInput(e);const{expiresIn:t=3600,key:a}=e,o=e.bucket||this.opts.bucket,n={...e.fields},i=[...e.conditions||[]];if(!o)throw new S("Must provide bucket param");const r=this.opts.accessKeySecret,s=new Date,c=la({date:new Date(s.valueOf()+1e3*t),type:"ISO"}),l=la(),p=l.substring(0,8),u="tos",d="request",h=_e(r,p),m=_e(h,this.opts.region),f=_e(m,u),y=_e(f,d),g={key:a,"x-tos-algorithm":"TOS4-HMAC-SHA256","x-tos-date":l,"x-tos-credential":[this.opts.accessKeyId,p,this.opts.region,u,d].join("/")};this.opts.stsToken&&(g["x-tos-security-token"]=this.opts.stsToken),i.push({bucket:o}),Object.entries(g).forEach(([e,t])=>{n[e]=t}),Object.entries(n).forEach(([e,t])=>{i.push({[e]:""+t})});const x=JSON.stringify({expiration:c,conditions:i}),b=ze(Ne(x,"utf-8"),"base64"),k=_e(y,b,"hex");return n.policy=b,n["x-tos-signature"]=k,n}function la(e){const{date:t=new Date,type:a="Z"}=e||{};return"ISO"===a?t.toISOString():t.toISOString().replace(/\..+/,"").replace(/-/g,"").replace(/:/g,"")+"Z"}const pa={getBucketCustomDomain:!0,getBucketIntelligenttiering:!0,getBucketInventory:!0,listBucketInventory:!0,getBucketMirrorBack:!0,getBucketNotification:!0,getBucketPolicy:!0,getBucketRealTimeLog:!0,getBucketReplication:!0,getBucketTagging:!0,getBucketWebsite:!0};function ua(e,t){const{enableCatchEmptyServerError:a,methodKey:o,defaultResponse:n}=t;if(e instanceof T)if(a){if(404===e.statusCode)return X(n,e)}else if(void 0===a&&404===e.statusCode&&pa[o])return X(n,e);throw e}async function da(e){return!this.opts.enableOptimizeMethodBehavior&&void 0!==this.opts.enableOptimizeMethodBehavior||e.policy.Statement.length?await this.fetchBucket(e.bucket,"PUT",{policy:""},{},e.policy,{needMd5:!0}):ma.call(this,e.bucket)}async function ha(e){try{const t=await this.fetchBucket(e,"GET",{policy:""},{});return t.data.Statement.forEach(e=>{const t=O(e);Object.keys(e.Condition||{}).forEach(a=>{Object.keys(e.Condition[a]).forEach(e=>{t(`Condition["${a}"]["${e}"]`)})})}),t}catch(e){return ua(e,{enableCatchEmptyServerError:this.opts.enableOptimizeMethodBehavior,methodKey:"getBucketPolicy",defaultResponse:{Statement:[],Version:"2012-10-17"}})}}async function ma(e){return this.fetchBucket(e,"DELETE",{policy:""},{})}async function fa(e){return this.fetchBucket(e,"GET",{versioning:""},{})}async function ya(e){return this.fetchBucket(e.bucket,"PUT",{versioning:""},{},{Status:e.status})}function ga(e){const t=xa.call(this,e);ba(e.conditions);const a=`http${this.opts.secure?"s":""}://${e.alternativeEndpoint||(e.isCustomDomain?this.opts.endpoint:`${t.bucket}.${this.opts.endpoint}`)}`,o=$(this.getSignatureQuery({bucket:t.bucket,expires:t.expires,policy:{conditions:t.conditions}}));return{getSignedURLForList:e=>{const t=$(e),n=[o,t].filter(Boolean).join("&");return`${a}?${n}`},getSignedURLForGetOrHead:(e,t)=>{const n=$(t),i=[o,n].filter(Boolean).join("&"),r=e.split("/").map(e=>encodeURIComponent(e)).join("/");return`${a}/${r}?${i}`},signedQuery:o}}function xa(e){const t=e.bucket||this.opts.bucket;if(!t)throw new S("Must provide bucket param");ba(e.conditions);const a=e.conditions.map(e=>[e.operator||"eq","$key",e.value]);return a.push(["eq","$bucket",t]),{bucket:t,expires:e.expires||3600,conditions:a}}function ba(e){if(e.length<1)throw new S("The `conditions` field of `PreSignedPolicyURLInput` must has one item at least");for(const t of e){if("key"!==t.key)throw new S("The `key` field of `PolicySignatureCondition` must be `'key'`");if(t.operator&&"eq"!==t.operator&&"starts-with"!==t.operator)throw new S("The `operator` field of `PolicySignatureCondition` must be `'eq'` or `'starts-with'`")}}async function ka(e){const{bucket:t}=e;return this.fetchBucket(t,"GET",{location:""},{})}async function va(e){try{const{bucket:t}=e;return await this.fetchBucket(t,"GET",{cors:""},{})}catch(e){return ua(e,{defaultResponse:{CORSRules:[]},enableCatchEmptyServerError:this.opts.enableOptimizeMethodBehavior,methodKey:"getBucketCORS"})}}async function Ta(e){const{bucket:t,CORSRules:a}=e;return this.opts.enableOptimizeMethodBehavior&&!a.length?Sa.call(this,{bucket:t}):this.fetchBucket(t,"PUT",{cors:""},{},{CORSRules:a})}async function Sa(e){const{bucket:t}=e;return this.fetchBucket(t,"DELETE",{cors:""},{})}async function wa(e){const{bucket:t,rules:a}=e;if(this.opts.enableOptimizeMethodBehavior&&!a.length)return Ea.call(this,{bucket:t});const o={};return J({...e,headers:o},["allowSameActionOverlap"]),this.fetchBucket(t,"PUT",{lifecycle:""},o,{Rules:a})}async function Ca(e){try{const{bucket:t}=e;return await this.fetchBucket(t,"GET",{lifecycle:""},{},{},{handleResponse:e=>({AllowSameActionOverlap:e.headers["x-tos-allow-same-action-overlap"],Rules:e.data.Rules})})}catch(e){return ua(e,{enableCatchEmptyServerError:this.opts.enableOptimizeMethodBehavior,methodKey:"getBucketLifecycle",defaultResponse:{Rules:[]}})}}async function Ea(e){const{bucket:t}=e;return this.fetchBucket(t,"DELETE",{lifecycle:""},{})}async function Ra(e){const{bucket:t,rule:a}=e;return this.fetchBucket(t,"PUT",{encryption:""},{"Content-MD5":Oe(JSON.stringify({Rule:a}),"base64")},{Rule:a})}async function Pa(e){const{bucket:t}=e;return this.fetchBucket(t,"GET",{encryption:""},{})}async function Ba(e){const{bucket:t}=e;return this.fetchBucket(t,"DELETE",{encryption:""},{})}async function Ia(e){const{bucket:t,rules:a}=e;return this.opts.enableOptimizeMethodBehavior&&!a.length?Ma.call(this,{bucket:t}):this.fetchBucket(t,"PUT",{mirror:""},{},{Rules:a})}async function Da(e){const{bucket:t}=e;try{return await this.fetchBucket(t,"GET",{mirror:""},{})}catch(e){return ua(e,{enableCatchEmptyServerError:this.opts.enableOptimizeMethodBehavior,methodKey:"getBucketMirrorBack",defaultResponse:{Rules:[]}})}}async function Ma(e){const{bucket:t}=e;return this.fetchBucket(t,"DELETE",{mirror:""},{})}async function Oa(e){const{tagSet:t,versionId:a}=e,o=U({versionId:a});return this._fetchObject(e,"PUT",{tagging:"",...o},{},{TagSet:t})}async function Aa(e){const{versionId:t}=e,a=U({versionId:t}),o=await this._fetchObject(e,"GET",{tagging:"",...a},{});return O(o.data.TagSet)("Tags"),o}async function ja(e){const{versionId:t}=e,a=U({versionId:t});return this._fetchObject(e,"DELETE",{tagging:"",...a},{})}async function _a(e){const{bucket:t,rules:a,role:o}=e;return this.opts.enableOptimizeMethodBehavior&&!a.length?Ua.call(this,{bucket:t}):this.fetchBucket(t,"PUT",{replication:""},{},{Role:o,Rules:a})}async function La(e){const{bucket:t,progress:a,ruleId:o}=e,n={replication:"",progress:a||""};null!=o&&(n["rule-id"]=""+o);try{return await this.fetchBucket(t,"GET",n,{})}catch(e){return ua(e,{enableCatchEmptyServerError:this.opts.enableOptimizeMethodBehavior,methodKey:"getBucketReplication",defaultResponse:{Rules:[],Role:""}})}}async function Ua(e){const{bucket:t}=e;return this.fetchBucket(t,"DELETE",{replication:""},{})}async function Na(e){const{bucket:t,...a}=e,o=_(a);return this.fetchBucket(t,"PUT",{website:""},{},{...o})}async function za(e){const{bucket:t}=e;try{return this.fetchBucket(t,"GET",{website:""},{})}catch(e){return ua(e,{enableCatchEmptyServerError:this.opts.enableOptimizeMethodBehavior,methodKey:"getBucketWebsite",defaultResponse:{RoutingRules:[]}})}}async function Fa(e){const{bucket:t}=e;return this.fetchBucket(t,"DELETE",{website:""},{})}async function qa(e){const{bucket:t,...a}=e,o=_(a);return this.fetchBucket(t,"PUT",{notification:""},{},{...o})}async function Ka(e){const{bucket:t}=e;try{return await this.fetchBucket(t,"GET",{notification:""},{})}catch(e){return ua(e,{enableCatchEmptyServerError:this.opts.enableOptimizeMethodBehavior,methodKey:"getBucketNotification",defaultResponse:{CloudFunctionConfigurations:[],RocketMQConfigurations:[]}})}}async function $a(e){const{bucket:t,...a}=e,o=_(a);return this.fetchBucket(t,"PUT",{customdomain:""},{},{...o})}async function Ha(e){try{const{bucket:t}=e;return await this.fetchBucket(t,"GET",{customdomain:""},{})}catch(e){return ua(e,{defaultResponse:{CustomDomainRules:[]},enableCatchEmptyServerError:this.opts.enableOptimizeMethodBehavior,methodKey:"getBucketCustomDomain"})}}async function Ga(e){const{bucket:t,customDomain:a}=e;return this.fetchBucket(t,"DELETE",{customdomain:a},{})}async function Va(e){const{bucket:t,...a}=e,o=_(a);return this.fetchBucket(t,"PUT",{realtimeLog:""},{},{...o})}async function Wa(e){const{bucket:t}=e;try{return await this.fetchBucket(t,"GET",{realtimeLog:""},{})}catch(e){return ua(e,{enableCatchEmptyServerError:this.opts.enableOptimizeMethodBehavior,methodKey:"getBucketRealTimeLog",defaultResponse:{}})}}async function Ja(e){const{bucket:t}=e;return this.fetchBucket(t,"DELETE",{realtimeLog:""},{})}var Qa,Xa,Ya,Za,eo,to,ao;async function oo(e){try{return await this.fetchBucket(e.bucket,"GET",{inventory:"",id:e.id},{})}catch(e){return ua(e,{enableCatchEmptyServerError:this.opts.enableOptimizeMethodBehavior,methodKey:"getBucketInventory",defaultResponse:void 0})}}async function no(e){const t={inventory:"",...e.continuationToken?{"continuation-token":e.continuationToken}:null};try{return await this.fetchBucket(e.bucket,"GET",t,{})}catch(e){return ua(e,{enableCatchEmptyServerError:this.opts.enableOptimizeMethodBehavior,methodKey:"listBucketInventory",defaultResponse:{InventoryConfigurations:[]}})}}async function io(e){return this.fetchBucket(e.bucket,"DELETE",{inventory:"",id:e.id},{})}function ro(e){return this.fetchBucket(e.bucket,"PUT",{inventory:"",id:e.inventoryConfiguration.Id},{},e.inventoryConfiguration)}async function so(e){const{accountId:t,...a}=e,o=_(a);return await this.fetch("POST","/jobs",{},{"x-tos-account-id":t},{...o})}async function co(e){const{accountId:t,maxResults:a=1e3,...o}=e;return await this.fetch("GET","/jobs",{maxResults:a,...o},{"x-tos-account-id":t},{},{axiosOpts:{paramsSerializer:Q}})}async function lo(e){const{accountId:t,jobId:a,priority:o}=e;return await this.fetch("POST",`/jobs/${a}/priority`,{priority:o},{"x-tos-account-id":t},{},{needMd5:!0})}async function po(e){const{accountId:t,jobId:a,requestedJobStatus:o,statusUpdateReason:n}=e;return await this.fetch("POST",`/jobs/${a}/status`,{requestedJobStatus:o,statusUpdateReason:n},{"x-tos-account-id":t},{},{needMd5:!0})}async function uo(e){const{accountId:t,JobId:a}=e;return await this.fetch("DELETE","/jobs/"+a,{},{"x-tos-account-id":t},{})}async function ho(e){const{accountId:t,JobId:a}=e;return await this.fetch("GET","/jobs/"+a,{},{"x-tos-account-id":t},{})}async function mo(e){return await this.fetchBucket(e.bucket,"PUT",{tagging:""},{},e.tagging,{needMd5:!0})}async function fo({bucket:e}){try{return await this.fetchBucket(e,"GET",{tagging:""},{})}catch(e){return ua(e,{enableCatchEmptyServerError:this.opts.enableOptimizeMethodBehavior,methodKey:"getBucketTagging",defaultResponse:{TagSet:{Tags:[]}}})}}async function yo({bucket:e}){return this.fetchBucket(e,"DELETE",{tagging:""},{})}async function go(e){return await this.fetchBucket(e.bucket,"PUT",{payByTraffic:""},{},e.payByTraffic)}async function xo({bucket:e}){return await this.fetchBucket(e,"GET",{payByTraffic:""},{})}async function bo(e){const{bucket:t}=e;try{return await this.fetchBucket(t,"GET",{imageStyleBriefInfo:""},{})}catch(e){if(e instanceof T&&404===e.statusCode)return this.getNormalDataFromError({BucketName:t,ImageStyleBriefInfo:[]},e);throw e}}async function ko(e){try{return await this.fetchBucket(e,"GET",{imageStyle:""},{})}catch(e){if(e instanceof T&&404===e.statusCode)return this.getNormalDataFromError({ImageStyles:[]},e);throw e}}async function vo(e){try{const{bucket:t,styleName:a}=e;return await this.fetchBucket(t,"GET",{imageStyleContent:"",styleName:a},{})}catch(e){if(e instanceof T&&404===e.statusCode)return this.getNormalDataFromError({ImageStyles:[]},e);throw e}}async function To(e,t){try{return await this.fetchBucket(e,"GET",{imageStyle:"",styleName:t},{})}catch(e){if(e instanceof T&&404===e.statusCode)return this.getNormalDataFromError(null,e);throw e}}async function So(e){const{bucket:t,styleName:a,content:o,styleObjectPrefix:n}=e;try{return await this.fetchBucket(t,"PUT",{imageStyle:"",styleName:a,styleObjectPrefix:n},{},{Content:o})}catch(e){if(e instanceof T&&404===e.statusCode)return this.getNormalDataFromError(null,e);throw e}}async function wo(e){const{styleName:t,styleObjectPrefix:a,bucket:o}=e;try{return await this.fetchBucket(o,"DELETE",{imageStyle:"",styleName:t,styleObjectPrefix:a},{})}catch(e){if(e instanceof T&&404===e.statusCode)return this.getNormalDataFromError(null,e);throw e}}async function Co(e,t){try{return await this.fetchBucket(e,"PUT",{originalImageProtect:""},{},t)}catch(e){if(e instanceof T&&404===e.statusCode)return this.getNormalDataFromError(null,e);throw e}}async function Eo(e){try{return await this.fetchBucket(e,"GET",{originalImageProtect:""},{})}catch(e){if(e instanceof T&&404===e.statusCode)return this.getNormalDataFromError(null,e);throw e}}async function Ro(e){const{bucket:t,Separator:a,SeparatorSuffix:o}=e;try{return await this.fetchBucket(t,"PUT",{imageStyleSeparator:""},{},{Separator:a,SeparatorSuffix:o})}catch(e){if(e instanceof T&&404===e.statusCode)return this.getNormalDataFromError(null,e);throw e}}async function Po(e){try{return await this.fetchBucket(e,"GET",{imageStyleSeparator:""},{})}catch(e){if(e instanceof T&&404===e.statusCode)return this.getNormalDataFromError(null,e);throw e}}async function Bo(e){try{return await this.fetchBucket(e,"GET",{intelligenttiering:""},{})}catch(e){return ua(e,{enableCatchEmptyServerError:this.opts.enableOptimizeMethodBehavior,methodKey:"getBucketIntelligenttiering",defaultResponse:{}})}}async function Io(e){const{bucket:t,...a}=e,o=_(a);return this.fetchBucket(t,"PUT",{rename:""},{},{...o})}async function Do(e){const{bucket:t}=e;return await this.fetchBucket(t,"GET",{rename:""},{})}async function Mo(e){const{bucket:t}=e;return this.fetchBucket(t,"DELETE",{rename:""},{})}async function Oo(e){const{versionId:t,...a}=e,o={restore:""};t&&(o.versionId=t);const n=_(a);return this._fetchObject(e,"POST",o,{},n)}async function Ao(e){const{accountId:t}=e;return await this.fetch("GET","/storagelens",{},{"x-tos-account-id":t},{},{axiosOpts:{paramsSerializer:Q}})}async function jo(e){const{accountId:t,Id:a}=e;return await this.fetch("DELETE","/storagelens",{id:a},{"x-tos-account-id":t},{},{needMd5:!0})}async function _o(e){const{accountId:t,Id:a}=e;return await this.fetch("GET","/storagelens",{id:a},{"x-tos-account-id":t},{},{needMd5:!0})}async function Lo(e){const{accountId:t,Id:a,...o}=e;return await this.fetch("PUT","/storagelens",{id:a},{"x-tos-account-id":t},{...o,Id:a},{needMd5:!0})}async function Uo(e){const{bucket:t,...a}=e,o=_(a);return this.fetchBucket(t,"PUT",{notification_v2:""},{},{...o})}async function No(e){const{bucket:t}=e;try{return await this.fetchBucket(t,"GET",{notification_v2:""},{})}catch(e){return ua(e,{enableCatchEmptyServerError:this.opts.enableOptimizeMethodBehavior,methodKey:"getBucketNotificationType2",defaultResponse:{Rules:[]}})}}async function zo(e){return Fo.call(this,e)}async function Fo(e){const t=e.headers=U(e.headers);return J(e,["symLinkTargetKey","symLinkTargetBucket","forbidOverwrite","acl","storageClass","meta"]),this._fetchObject(e,"PUT",{symlink:""},t,void 0,{handleResponse(e){const{headers:t}=e;return{VersionID:t["x-tos-version-id"]}}})}async function qo(e){return Ko.call(this,e)}async function Ko(e){const t={symlink:""};return e.versionId&&(t.versionId=e.versionId),this._fetchObject(e,"GET",t,{},void 0,{handleResponse:e=>{const{headers:t}=e;return{VersionID:t["x-tos-version-id"],SymlinkTargetKey:t["x-tos-symlink-target"],SymlinkTargetBucket:t["x-tos-symlink-bucket"],LastModified:t["last-modified"]}}})}async function $o(e){const{bucket:t,...a}=e,o=_(a);return this.fetchBucket(t,"PUT",{transferAcceleration:""},{},{...o})}async function Ho(e){try{const{bucket:t}=e,a={};return e.getStatus&&(a["x-tos-get-bucket-acceleration-status"]="true"),await this.fetchBucket(t,"GET",{transferAcceleration:""},a)}catch(e){return ua(e,{enableCatchEmptyServerError:this.opts.enableOptimizeMethodBehavior,methodKey:"getBucketTransferAcceleration",defaultResponse:{TransferAccelerationConfiguration:{Enabled:"false",Status:exports.TransferAccelerationStatusType.Terminated}}})}}async function Go(e){const{bucket:t,status:a}=e;return this.fetchBucket(t,"PUT",{accessmonitor:""},{},{Status:a})}async function Vo(e){try{const{bucket:t}=e;return await this.fetchBucket(t,"GET",{accessmonitor:""},{})}catch(e){return ua(e,{enableCatchEmptyServerError:this.opts.enableOptimizeMethodBehavior,methodKey:"getBucketAccessMonitor",defaultResponse:{}})}}async function Wo(e){const{accountId:t}=e;return await this.fetch("GET","/qospolicy",{},{"x-tos-account-id":t},{},{})}async function Jo(e){const{accountId:t,...a}=e;return await this.fetch("PUT","/qospolicy",{},{"x-tos-account-id":t},{...a},{})}async function Qo(e){const{accountId:t}=e;return await this.fetch("DELETE","/qospolicy",{},{"x-tos-account-id":t},{},{})}async function Xo(e){const{accountId:t,name:a,regions:o}=e;return await this.fetch("POST","/mrap",{name:a},{"x-tos-account-id":t},{Name:a,Regions:o},{})}async function Yo(e){const{name:t,accountId:a}=e;return await this.fetch("GET","/mrap",{name:t},{"x-tos-account-id":a},{},{})}async function Zo(e){const{accountId:t,...a}=e;return await this.fetch("GET","/mrap",{...a},{"x-tos-account-id":t},{},{})}async function en(e){const{accountId:t,alias:a}=e;return await this.fetch("GET","/mrap/routes",{alias:a},{"x-tos-account-id":t})}async function tn(e){const{name:t,accountId:a}=e;return await this.fetch("DELETE","/mrap",{name:t},{"x-tos-account-id":a})}async function an(e){const{routes:t,accountId:a,alias:o}=e;return await this.fetch("PATCH","/mrap/routes",{alias:o},{"x-tos-account-id":a},{Routes:t})}!function(e){e.Daily="Daily",e.Weekly="Weekly"}(Qa||(Qa={})),function(e){e.All="All",e.Current="Current"}(Xa||(Xa={})),function(e){e.Size="Size",e.LastModifiedDat="LastModifiedDate",e.ETag="ETag",e.StorageClass="StorageClass",e.IsMultipartUploaded="IsMultipartUploaded",e.EncryptionStatus="EncryptionStatus",e.CRC64="CRC64",e.ReplicationStatus="ReplicationStatus"}(Ya||(Ya={})),function(e){e.StringEquals="StringEquals",e.StringNotEquals="StringNotEquals",e.StringEqualsIgnoreCase="StringEqualsIgnoreCase",e.StringNotEqualsIgnoreCase="StringNotEqualsIgnoreCase",e.StringLike="StringLike",e.StringNotLike="StringNotLike"}(Za||(Za={})),function(e){e.DateEquals="DateEquals",e.DateNotEquals="DateNotEquals",e.DateLessThan="DateLessThan",e.DateLessThanEquals="DateLessThanEquals",e.DateGreaterThan="DateGreaterThan",e.DateGreaterThanEquals="DateGreaterThanEquals"}(eo||(eo={})),function(e){e.IpAddress="IpAddress",e.NotIpAddress="NotIpAddress"}(to||(to={})),function(e){e.WritesQps="WritesQps",e.ReadsQps="ReadsQps",e.ListQps="ListQps",e.WritesRate="WritesRate",e.ReadsRate="ReadsRate"}(ao||(ao={}));const on=async function(e){const{accountId:t,alias:a,rules:o}=e;return this.opts.enableOptimizeMethodBehavior&&!o.length?rn.call(this,{accountId:t,alias:a}):await this.fetch("PUT","/mrap/mirror",{alias:a},{"x-tos-account-id":t},{Rules:o},{handleResponse:()=>({})})},nn=async function(e){const{accountId:t,alias:a}=e;try{const e=await this.fetch("GET","/mrap/mirror",{alias:a},{"x-tos-account-id":t},{},{});return O(e.data)("Rules"),e}catch(e){return ua(e,{enableCatchEmptyServerError:this.opts.enableOptimizeMethodBehavior,methodKey:"getMultiRegionAccessPointMirrorBack",defaultResponse:{Rules:[]}})}},rn=async function(e){const{accountId:t,alias:a}=e;return await this.fetch("DELETE","/mrap/mirror",{alias:a},{"x-tos-account-id":t},{},{handleResponse:()=>({})})};async function sn(e){const{bucket:t,enable:a}=e;return await this.fetchBucket(t,"PUT",{privateM3U8:""},{},{Enable:a})}async function cn(e){const{bucket:t}=e;try{return await this.fetchBucket(t,"GET",{privateM3U8:""},{})}catch(e){return ua(e,{enableCatchEmptyServerError:this.opts.enableOptimizeMethodBehavior,methodKey:"getBucketPrivateM3U8",defaultResponse:{Enable:!1}})}}async function ln(e){const{bucket:t,...a}=e,o=_(a);return this.fetchBucket(t,"PUT",{trash:""},{},{...o})}async function pn(e){const{bucket:t}=e;return await this.fetchBucket(t,"GET",{trash:""},{})}class un extends At{constructor(...e){super(...e),this.createBucket=Ft,this.headBucket=Kt,this.deleteBucket=qt,this.listBuckets=zt,this.getBucketLocation=ka,this.putBucketStorageClass=$t,this.getBucketAcl=Gt,this.putBucketAcl=Ht,this.getBucketPolicy=ha,this.putBucketPolicy=da,this.deleteBucketPolicy=ma,this.getBucketVersioning=fa,this.putBucketVersioning=ya,this.getBucketCORS=va,this.putBucketCORS=Ta,this.deleteBucketCORS=Sa,this.putBucketLifecycle=wa,this.getBucketLifecycle=Ca,this.deleteBucketLifecycle=Ea,this.putBucketEncryption=Ra,this.getBucketEncryption=Pa,this.deleteBucketEncryption=Ba,this.putBucketMirrorBack=Ia,this.getBucketMirrorBack=Da,this.deleteBucketMirrorBack=Ma,this.putBucketReplication=_a,this.getBucketReplication=La,this.deleteBucketReplication=Ua,this.putBucketWebsite=Na,this.getBucketWebsite=za,this.deleteBucketWebsite=Fa,this.putBucketNotification=qa,this.getBucketNotification=Ka,this.putBucketCustomDomain=$a,this.getBucketCustomDomain=Ha,this.deleteBucketCustomDomain=Ga,this.putBucketRealTimeLog=Va,this.getBucketRealTimeLog=Wa,this.deleteBucketRealTimeLog=Ja,this.getBucketInventory=oo,this.listBucketInventory=no,this.putBucketInventory=ro,this.deleteBucketInventory=io,this.putBucketTagging=mo,this.getBucketTagging=fo,this.deleteBucketTagging=yo,this.putBucketPayByTraffic=go,this.getBucketPayByTraffic=xo,this.getBucketImageStyle=To,this.getBucketImageStyleList=ko,this.getBucketImageStyleListByName=vo,this.getImageStyleBriefInfo=bo,this.deleteBucketImageStyle=wo,this.putBucketImageStyle=So,this.putBucketImageStyleSeparator=Ro,this.putBucketImageProtect=Co,this.getBucketImageProtect=Eo,this.getBucketImageStyleSeparator=Po,this.putBucketRename=Io,this.getBucketRename=Do,this.deleteBucketRename=Mo,this.putBucketTransferAcceleration=$o,this.getBucketTransferAcceleration=Ho,this.copyObject=gt,this.resumableCopyObject=bt,this.deleteObject=Zt,this.deleteMultiObjects=ta,this.getObject=vt,this.getObjectV2=St,this.getObjectToFile=wt,this.getObjectAcl=aa,this.headObject=ft,this.appendObject=ra,this.listObjects=jt,this.renameObject=ea,this.fetchObject=Qt,this.putFetchTask=Xt,this.listObjectsType2=Lt,this.listObjectVersions=_t,this.putObject=Vt,this.putObjectFromFile=Jt,this.putObjectAcl=oa,this.setObjectMeta=sa,this.createMultipartUpload=ie,this.uploadPart=qe,this.uploadPartFromFile=Ke,this.completeMultipartUpload=$e,this.abortMultipartUpload=na,this.uploadPartCopy=yt,this.listMultipartUploads=ia,this.listParts=se,this.downloadFile=Rt,this.putObjectTagging=Oa,this.getObjectTagging=Aa,this.deleteObjectTagging=ja,this.listJobs=co,this.createJob=so,this.deleteJob=uo,this.describeJob=ho,this.updateJobStatus=po,this.updateJobPriority=lo,this.restoreObject=Oo,this.uploadFile=We,this.getPreSignedUrl=Yt,this.calculatePostSignature=ca,this.preSignedPostSignature=ca,this.preSignedPolicyURL=ga,this.getBucketIntelligenttiering=Bo,this.listStorageLens=Ao,this.deleteStorageLens=jo,this.getStorageLens=_o,this.putStorageLens=Lo,this.putBucketNotificationType2=Uo,this.getBucketNotificationType2=No,this.putSymlink=zo,this.getSymlink=qo,this.putBucketAccessMonitor=Go,this.getBucketAccessMonitor=Vo,this.putQosPolicy=Jo,this.getQosPolicy=Wo,this.deleteQosPolicy=Qo,this.createMultiRegionAccessPoint=Xo,this.getMultiRegionAccessPoint=Yo,this.listMultiRegionAccessPoints=Zo,this.getMultiRegionAccessPointRoutes=en,this.deleteMultiRegionAccessPoint=tn,this.submitMultiRegionAccessPointRoutes=an,this.putMultiRegionAccessPointMirrorBack=on,this.getMultiRegionAccessPointMirrorBack=nn,this.deleteMultiRegionAccessPointMirrorBack=rn,this.putBucketPrivateM3U8=sn,this.getBucketPrivateM3U8=cn,this.putBucketTrash=ln,this.getBucketTrash=pn}}const dn=a.CancelToken;class hn extends un{}hn.TosServerError=T,hn.isCancel=H,hn.CancelError=w,hn.TosServerCode=exports.TosServerCode,hn.TosClientError=S,hn.CancelToken=dn,hn.ACLType=exports.ACLType,hn.StorageClassType=exports.StorageClassType,hn.MetadataDirectiveType=exports.MetadataDirectiveType,hn.AzRedundancyType=exports.AzRedundancyType,hn.PermissionType=exports.PermissionType,hn.GranteeType=exports.GranteeType,hn.CannedType=exports.CannedType,hn.HttpMethodType=exports.HttpMethodType,hn.LifecycleStatusType=exports.LifecycleStatusType,hn.StatusType=exports.StatusType,hn.RedirectType=exports.RedirectType,hn.StorageClassInheritDirectiveType=exports.StorageClassInheritDirectiveType,hn.TierType=exports.TierType,hn.VersioningStatusType=exports.VersioningStatusType,hn.createDefaultRateLimiter=xe,hn.DataTransferType=exports.DataTransferType,hn.UploadEventType=exports.UploadEventType,hn.DownloadEventType=exports.DownloadEventType,hn.ResumableCopyEventType=exports.ResumableCopyEventType,hn.ReplicationStatusType=exports.ReplicationStatusType,hn.AccessPointStatusType=exports.AccessPointStatusType,hn.TransferAccelerationStatusType=exports.TransferAccelerationStatusType,hn.MRAPMirrorBackRedirectPolicyType=exports.MRAPMirrorBackRedirectPolicyType,hn.ShareLinkClient=Nt,exports.CancelError=w,exports.CancelToken=dn,exports.ShareLinkClient=Nt,exports.TOS=hn,exports.TosClient=hn,exports.TosClientError=S,exports.TosServerError=T,exports.createDefaultRateLimiter=xe,exports.default=hn,exports.isCancel=H;
//# sourceMappingURL=tos.cjs.production.min.js.map
