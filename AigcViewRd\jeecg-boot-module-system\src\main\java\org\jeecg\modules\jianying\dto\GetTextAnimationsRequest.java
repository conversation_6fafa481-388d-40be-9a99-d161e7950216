package org.jeecg.modules.jianying.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 获取文本动画请求参数
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class GetTextAnimationsRequest extends BaseJianyingRequest {
    
    @ApiModelProperty(value = "动画模式，0：默认所有，1：VIP，2：免费", example = "0")
    @JsonProperty("mode")
    private Integer zjMode;

    @ApiModelProperty(value = "动画类型，in：入场，out：出场，loop：循环", example = "in")
    @JsonProperty("type")
    private String zjType;
    
    @Override
    public String getSummary() {
        return "GetTextAnimationsRequest{" +
               "accessKey=" + (getAccessKey() != null && getAccessKey().length() > 10 ? 
                              getAccessKey().substring(0, 10) + "***" : "null") +
               ", mode=" + zjMode +
               ", type=" + zjType +
               "}";
    }
}
