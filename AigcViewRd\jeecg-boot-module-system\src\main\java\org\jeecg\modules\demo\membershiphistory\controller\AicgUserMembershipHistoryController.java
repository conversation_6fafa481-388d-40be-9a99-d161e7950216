package org.jeecg.modules.demo.membershiphistory.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.demo.membershiphistory.entity.AicgUserMembershipHistory;
import org.jeecg.modules.demo.membershiphistory.service.IAicgUserMembershipHistoryService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;

/**
 * @Description: 用户会员订阅历史表
 * @Author: jeecg-boot
 * @Date:   2025-06-18
 * @Version: V1.0
 */
@Api(tags="用户会员订阅历史表")
@RestController
@RequestMapping("/demo/membershiphistory")
@Slf4j
public class AicgUserMembershipHistoryController extends JeecgController<AicgUserMembershipHistory, IAicgUserMembershipHistoryService> {
	@Autowired
	private IAicgUserMembershipHistoryService aicgUserMembershipHistoryService;
	
	/**
	 * 分页列表查询
	 *
	 * @param aicgUserMembershipHistory
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	@AutoLog(value = "用户会员订阅历史表-分页列表查询")
	@ApiOperation(value="用户会员订阅历史表-分页列表查询", notes="用户会员订阅历史表-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(AicgUserMembershipHistory aicgUserMembershipHistory,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		QueryWrapper<AicgUserMembershipHistory> queryWrapper = QueryGenerator.initQueryWrapper(aicgUserMembershipHistory, req.getParameterMap());
		Page<AicgUserMembershipHistory> page = new Page<AicgUserMembershipHistory>(pageNo, pageSize);
		IPage<AicgUserMembershipHistory> pageList = aicgUserMembershipHistoryService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 * 添加
	 *
	 * @param aicgUserMembershipHistory
	 * @return
	 */
	@AutoLog(value = "用户会员订阅历史表-添加")
	@ApiOperation(value="用户会员订阅历史表-添加", notes="用户会员订阅历史表-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody AicgUserMembershipHistory aicgUserMembershipHistory) {
		aicgUserMembershipHistoryService.save(aicgUserMembershipHistory);
		return Result.OK("添加成功！");
	}
	
	/**
	 * 编辑
	 *
	 * @param aicgUserMembershipHistory
	 * @return
	 */
	@AutoLog(value = "用户会员订阅历史表-编辑")
	@ApiOperation(value="用户会员订阅历史表-编辑", notes="用户会员订阅历史表-编辑")
	@PutMapping(value = "/edit")
	public Result<?> edit(@RequestBody AicgUserMembershipHistory aicgUserMembershipHistory) {
		aicgUserMembershipHistoryService.updateById(aicgUserMembershipHistory);
		return Result.OK("编辑成功!");
	}
	
	/**
	 * 通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "用户会员订阅历史表-通过id删除")
	@ApiOperation(value="用户会员订阅历史表-通过id删除", notes="用户会员订阅历史表-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		aicgUserMembershipHistoryService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 * 批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "用户会员订阅历史表-批量删除")
	@ApiOperation(value="用户会员订阅历史表-批量删除", notes="用户会员订阅历史表-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.aicgUserMembershipHistoryService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "用户会员订阅历史表-通过id查询")
	@ApiOperation(value="用户会员订阅历史表-通过id查询", notes="用户会员订阅历史表-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		AicgUserMembershipHistory aicgUserMembershipHistory = aicgUserMembershipHistoryService.getById(id);
		if(aicgUserMembershipHistory==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(aicgUserMembershipHistory);
	}

    /**
	 * 导出excel
	 *
	 * @param request
	 * @param aicgUserMembershipHistory
	 */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, AicgUserMembershipHistory aicgUserMembershipHistory) {
        return super.exportXls(request, aicgUserMembershipHistory, AicgUserMembershipHistory.class, "用户会员订阅历史表");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, AicgUserMembershipHistory.class);
    }
    
    /**
     * 根据用户ID查询会员订阅历史
     *
     * @param userId
     * @return
     */
    @AutoLog(value = "根据用户ID查询会员订阅历史")
    @ApiOperation(value="根据用户ID查询会员订阅历史", notes="根据用户ID查询会员订阅历史")
    @GetMapping(value = "/getByUserId")
    public Result<?> getByUserId(@RequestParam(name="userId",required=true) String userId) {
        List<AicgUserMembershipHistory> list = aicgUserMembershipHistoryService.getByUserId(userId);
        return Result.OK(list);
    }
    
    /**
     * 根据用户ID查询当前生效的会员订阅
     *
     * @param userId
     * @return
     */
    @AutoLog(value = "根据用户ID查询当前生效的会员订阅")
    @ApiOperation(value="根据用户ID查询当前生效的会员订阅", notes="根据用户ID查询当前生效的会员订阅")
    @GetMapping(value = "/getCurrentMembership")
    public Result<?> getCurrentMembership(@RequestParam(name="userId",required=true) String userId) {
        AicgUserMembershipHistory membership = aicgUserMembershipHistoryService.getCurrentMembership(userId);
        return Result.OK(membership);
    }
    
    /**
     * 取消会员订阅
     *
     * @param id
     * @return
     */
    @AutoLog(value = "取消会员订阅")
    @ApiOperation(value="取消会员订阅", notes="取消会员订阅")
    @PostMapping(value = "/cancel")
    public Result<?> cancelMembership(@RequestParam(name="id",required=true) String id) {
        boolean success = aicgUserMembershipHistoryService.cancelMembership(id, "system");
        if (success) {
            return Result.OK("取消成功!");
        } else {
            return Result.error("取消失败!");
        }
    }
    
    /**
     * 更新过期会员订阅状态
     *
     * @return
     */
    @AutoLog(value = "更新过期会员订阅状态")
    @ApiOperation(value="更新过期会员订阅状态", notes="更新过期会员订阅状态")
    @PostMapping(value = "/updateExpired")
    public Result<?> updateExpiredMemberships() {
        int count = aicgUserMembershipHistoryService.updateExpiredMemberships();
        return Result.OK("更新了 " + count + " 条过期记录");
    }
}
