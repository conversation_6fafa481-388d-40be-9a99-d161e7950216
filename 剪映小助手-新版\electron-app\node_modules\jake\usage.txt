Jake <PERSON> build tool
********************************************************************************
If no flags are given, <PERSON> looks for a Jake<PERSON>le or Jakefile.js in the current directory.
********************************************************************************
{Usage}: jake [options ...] [env variables ...] target

{Options}:
  -f,      --jakefile FILE          Use FILE as the Jakefile.
  -C,      --directory DIRECTORY    Change to DIRECTORY before running tasks.
  -q,      --quiet                  Do not log messages to standard output.
  -B,      --always-make            Unconditionally make all targets.
  -T/-ls,  --tasks                  Display the tasks (matching optional PATTERN) with descriptions, then exit.
  -J,      --jake<PERSON>bdir JAKELIBDIR  Auto-import any .jake files in JAKELIBDIR. (default is \'jakelib\')
  -h,      --help                   Display this help message.
  -V/-v,   --version                Display the Jake version.
  -ar,     --allow-rejection        Keep running even after unhandled promise rejection.

