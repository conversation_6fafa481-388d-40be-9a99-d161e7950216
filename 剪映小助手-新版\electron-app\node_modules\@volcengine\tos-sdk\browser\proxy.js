"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
exports.__esModule = true;
exports.createTosProxyMiddleware = void 0;
var url_1 = __importDefault(require("url"));
var http_proxy_middleware_1 = require("http-proxy-middleware");
var cloneDeep_1 = __importDefault(require("lodash/cloneDeep"));
/**
 *
 * @param context is context of http-proxy-middleware. refer: https://github.com/chimurai/http-proxy-middleware
 * @param {CreateTosProxyMiddlewareOpts} opts
 */
function createTosProxyMiddleware(context, opts) {
    var proxyParamKey = 'x-proxy-tos-host';
    return (0, http_proxy_middleware_1.createProxyMiddleware)(context, {
        secure: false,
        /**
         * 这里不开可能会导致 https 转发建联失败
         */
        changeOrigin: true,
        pathRewrite: function (_path, req) {
            var path = req.path;
            var query = (0, cloneDeep_1["default"])(req.query);
            delete query[proxyParamKey];
            var newPath = url_1["default"].format({
                pathname: path.replace(context, ''),
                query: query
            });
            return newPath;
        },
        router: function (req) {
            var _a;
            var originHost = req.query[proxyParamKey];
            var realProtocol = (_a = opts === null || opts === void 0 ? void 0 : opts.protocol) !== null && _a !== void 0 ? _a : req.protocol;
            if (!originHost) {
                throw Error("\u4EE3\u7406\u7F3A\u5C11 ".concat(proxyParamKey, " \u53C2\u6570"));
            }
            if (opts === null || opts === void 0 ? void 0 : opts.destHost) {
                if (typeof opts.destHost === 'function') {
                    originHost = opts.destHost(originHost);
                }
                else {
                    originHost = opts.destHost;
                }
            }
            return "".concat(realProtocol, "://").concat(originHost);
        }
    });
}
exports.createTosProxyMiddleware = createTosProxyMiddleware;
