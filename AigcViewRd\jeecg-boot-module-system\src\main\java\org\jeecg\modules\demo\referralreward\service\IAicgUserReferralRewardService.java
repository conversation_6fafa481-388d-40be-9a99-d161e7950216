package org.jeecg.modules.demo.referralreward.service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import org.jeecg.modules.demo.referralreward.entity.AicgUserReferralReward;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * @Description: 推荐奖励记录表
 * @Author: jeecg-boot
 * @Date:   2025-06-18
 * @Version: V1.0
 */
public interface IAicgUserReferralRewardService extends IService<AicgUserReferralReward> {

    /**
     * 根据推荐人ID查询奖励记录
     * @param referrerId 推荐人ID
     * @return 奖励记录列表
     */
    List<AicgUserReferralReward> getByReferrerId(String referrerId);
    
    /**
     * 根据推荐关系ID查询奖励记录
     * @param referralId 推荐关系ID
     * @return 奖励记录列表
     */
    List<AicgUserReferralReward> getByReferralId(String referralId);
    
    /**
     * 创建推荐奖励记录
     * @param referralId 推荐关系ID
     * @param referrerId 推荐人ID
     * @param refereeId 被推荐人ID
     * @param rewardType 奖励类型
     * @param rewardAmount 奖励金额
     * @param triggerEvent 触发事件描述
     * @param operatorId 操作人ID
     * @return 创建的奖励记录
     */
    AicgUserReferralReward createReward(String referralId, String referrerId, String refereeId, 
                                       Integer rewardType, BigDecimal rewardAmount, String triggerEvent, String operatorId);
    
    /**
     * 发放奖励
     * @param id 奖励记录ID
     * @param transactionId 关联交易记录ID
     * @param operatorId 操作人ID
     * @return 是否成功
     */
    boolean payReward(String id, String transactionId, String operatorId);
    
    /**
     * 取消奖励
     * @param id 奖励记录ID
     * @param operatorId 操作人ID
     * @return 是否成功
     */
    boolean cancelReward(String id, String operatorId);
    
    /**
     * 统计推荐人的奖励数据
     * @param referrerId 推荐人ID
     * @return 奖励统计数据
     */
    Map<String, Object> getRewardStats(String referrerId);
    
    /**
     * 查询待发放的奖励记录
     * @return 待发放的奖励记录列表
     */
    List<AicgUserReferralReward> getPendingRewards();
    
    /**
     * 查询已发放的奖励记录
     * @param referrerId 推荐人ID
     * @return 已发放的奖励记录列表
     */
    List<AicgUserReferralReward> getPaidRewards(String referrerId);
    
    /**
     * 计算推荐人的可提现金额
     * @param referrerId 推荐人ID
     * @return 可提现金额
     */
    BigDecimal getAvailableRewardAmount(String referrerId);
    
    /**
     * 批量发放奖励
     * @param rewardIds 奖励记录ID列表
     * @param operatorId 操作人ID
     * @return 成功发放的数量
     */
    int batchPayRewards(List<String> rewardIds, String operatorId);
}
