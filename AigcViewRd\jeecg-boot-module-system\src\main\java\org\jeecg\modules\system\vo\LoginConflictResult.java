package org.jeecg.modules.system.vo;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;

/**
 * @Description: 登录冲突检查结果
 * @Author: jeecg-boot
 * @Date: 2025-06-21
 * @Version: V1.0
 */
@Data
public class LoginConflictResult {
    
    /**
     * 是否有冲突
     */
    private boolean hasConflict;
    
    /**
     * 是否强制登录
     */
    private boolean forceLogin;
    
    /**
     * 已存在的Token
     */
    private String existingToken;
    
    /**
     * 冲突详情信息
     */
    private JSONObject conflictInfo;
    
    /**
     * 构造函数
     */
    public LoginConflictResult(boolean hasConflict, boolean forceLogin, String existingToken, JSONObject conflictInfo) {
        this.hasConflict = hasConflict;
        this.forceLogin = forceLogin;
        this.existingToken = existingToken;
        this.conflictInfo = conflictInfo;
    }
    
    /**
     * 无冲突
     */
    public static LoginConflictResult noConflict() {
        return new LoginConflictResult(false, false, null, null);
    }
    
    /**
     * 有冲突，需要用户确认
     */
    public static LoginConflictResult hasConflict(JSONObject conflictInfo) {
        return new LoginConflictResult(true, false, null, conflictInfo);
    }
    
    /**
     * 强制登录，执行踢下线
     */
    public static LoginConflictResult forceLogin(String existingToken) {
        return new LoginConflictResult(true, true, existingToken, null);
    }
}
