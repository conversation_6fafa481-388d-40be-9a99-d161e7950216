package org.jeecg.modules.demo.userrecord.service.impl;

import org.jeecg.modules.demo.userrecord.entity.AicgUserRecord;
import org.jeecg.modules.demo.userrecord.mapper.AicgUserRecordMapper;
import org.jeecg.modules.demo.userrecord.service.IAicgUserRecordService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: 消费记录表
 * @Author: jeecg-boot
 * @Date:   2025-06-14
 * @Version: V1.0
 */
@Service
public class AicgUserRecordServiceImpl extends ServiceImpl<AicgUserRecordMapper, AicgUserRecord> implements IAicgUserRecordService {

}
