{"version": 3, "file": "sign.js", "sourceRoot": "", "sources": ["../../src/sign.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,6CAA+B;AAC/B,uCAAyB;AACzB,2CAA6B;AAC7B,6CAA+B;AAC/B,sEAA6C;AAE7C,iCAQgB;AAChB,uDAA6D;AAC7D,6EAAmG;AACnG,2DAA0D;AAG1D,MAAM,UAAU,GAAW,OAAO,CAAC,oBAAoB,CAAC,CAAC,OAAO,CAAC;AAEjE,MAAM,SAAS,GAAG,EAAE,CAAC,OAAO,EAAE,CAAC;AAE/B;;GAEG;AACH,KAAK,UAAU,oBAAoB,CAAE,IAAiB;IACpD,IAAI,IAAI,CAAC,QAAQ,EAAE;QACjB,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE;YACjC,MAAM,IAAI,KAAK,CAAC,yCAAyC,CAAC,CAAC;SAC5D;QACD,sEAAsE;KACvE;AACH,CAAC;AAED,SAAS,kBAAkB,CAAE,MAA6B;IACxD,IAAI,MAAM,IAAI,CAAC,CAAC,MAAM,YAAY,KAAK,CAAC,EAAE;QACxC,OAAO,CAAC,MAAM,CAAC,CAAC;KACjB;AACH,CAAC;AAED;;GAEG;AACH,KAAK,UAAU,gBAAgB,CAAE,IAAiB;IAChD,MAAM,oBAAoB,CAAC,IAAI,CAAC,CAAC;IACjC,MAAM,IAAA,sBAAe,EAAC,IAAI,CAAC,CAAC;IAE5B,IAAI,IAAI,CAAC,mBAAmB,IAAI,OAAO,IAAI,CAAC,mBAAmB,KAAK,QAAQ,EAAE;QAC5E,MAAM,IAAI,KAAK,CAAC,kDAAkD,CAAC,CAAC;KACrE;IAED,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,KAAK,aAAa,IAAI,IAAI,CAAC,IAAI,KAAK,cAAc,EAAE;QAC5E,MAAM,IAAI,KAAK,CAAC,sDAAsD,CAAC,CAAC;KACzE;IAED,MAAM,QAAQ,GAAG,MAAM,IAAA,2BAAoB,EAAC,IAAI,CAAC,CAAC;IAClD,MAAM,MAAM,mCACP,IAAI,KACP,MAAM,EAAE,kBAAkB,CAAC,IAAI,CAAC,MAAM,CAAC,EACvC,IAAI,EAAE,IAAI,CAAC,IAAI,IAAI,cAAc,EACjC,QAAQ,GACT,CAAC;IACF,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;GAEG;AACH,KAAK,UAAU,qBAAqB,CAAE,IAA0B;IAC9D,uBAAuB;IACvB,IAAA,eAAQ,EAAC,+CAA+C,CAAC,CAAC;IAE1D,MAAM,IAAA,oBAAa,EACjB,UAAU,EACV,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC,MAAM,CAC3B,IAAI,CAAC,YAAY,KAAK,KAAK,IAAI,IAAA,yBAAc,EAAC,SAAS,EAAE,QAAQ,CAAC,IAAI,CAAC,CAAC,8DAA8D;QACpI,CAAC,CAAC;YACE,UAAU;gBACR,CAAC,IAAI,CAAC,YAAY;oBAChB,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC,YAAY,CAAC,wDAAwD;oBAClF,CAAC,CAAC,EAAE,CAAC;SACV;QACH,CAAC,CAAC,EAAE,EACN,CAAC,aAAa,EAAE,IAAI,CAAC,GAAG,CAAC,CAC1B,CACF,CAAC;AACJ,CAAC;AAED,SAAS,qBAAqB,CAAE,QAAgB,EAAE,QAA6B;IAC7E,MAAM,kBAAkB,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,cAAc,CAAC,CAAC;IAE/E,IAAI,gBAAwB,CAAC;IAC7B,IAAI,QAAQ,KAAK,QAAQ,EAAE;QACzB,uBAAuB;QACvB,kGAAkG;QAClG,uCAAuC;QACvC,gBAAgB,GAAG,IAAI,CAAC,OAAO,CAAC,kBAAkB,EAAE,sBAAsB,CAAC,CAAC;QAC5E,gBAAgB;QAChB,4GAA4G;QAC5G,IAAI,QAAQ,CAAC,QAAQ,CAAC,cAAc,CAAC,EAAE;YACrC,gBAAgB,GAAG,IAAI,CAAC,OAAO,CAAC,kBAAkB,EAAE,6BAA6B,CAAC,CAAC;YACrF,aAAa;YACb,yGAAyG;SACxG;aAAM,IAAI,QAAQ,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE;YACzC,gBAAgB,GAAG,IAAI,CAAC,OAAO,CAAC,kBAAkB,EAAE,0BAA0B,CAAC,CAAC;YAClF,kBAAkB;YAClB,8GAA8G;SAC7G;aAAM,IAAI,QAAQ,CAAC,QAAQ,CAAC,gBAAgB,CAAC,EAAE;YAC9C,gBAAgB,GAAG,IAAI,CAAC,OAAO,CAAC,kBAAkB,EAAE,+BAA+B,CAAC,CAAC;SACtF;KACF;SAAM;QACL,uBAAuB;QACvB,2DAA2D;QAC3D,gBAAgB,GAAG,IAAI,CAAC,OAAO,CAAC,kBAAkB,EAAE,mBAAmB,CAAC,CAAC;QAEzE,8DAA8D;QAC9D,IAAI,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE;YAC9B,gBAAgB,GAAG,IAAI,CAAC,OAAO,CAAC,kBAAkB,EAAE,yBAAyB,CAAC,CAAC;SAChF;KACF;IAED,OAAO;QACL,YAAY,EAAE,gBAAgB;QAC9B,eAAe,EAAE,IAAI;QACrB,YAAY,EAAE,SAA+B;QAC7C,cAAc,EAAE,SAA0C;QAC1D,SAAS,EAAE,SAA+B;KAC3C,CAAC;AACJ,CAAC;AAED,KAAK,UAAU,mBAAmB,CAChC,IAA+B,EAC/B,QAAkD;IAElD,MAAM,oBAAoB,qBAAQ,QAAQ,CAAE,CAAC;IAC7C,IAAI,IAAI,EAAE;QACR,IAAI,IAAI,CAAC,YAAY,KAAK,SAAS,EAAE;YACnC,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE;gBACpC,MAAM,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,CAAsB,CAAC,IAAI,EAAE,cAAc,EAAE,EAAE,CAAC,iCACxF,IAAI,KACP,CAAC,cAAc,CAAC,EAAE,IAAI,IACtB,EAAE,EAAE,CAAC,CAAC;gBACR,MAAM,GAAG,GAAG,MAAM,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,MAAM,EAAE,EAAE,mBAAmB,CAAC,CAAC,CAAC;gBAC7E,MAAM,gBAAgB,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,oBAAoB,CAAC,CAAC;gBAC9D,MAAM,EAAE,CAAC,SAAS,CAAC,gBAAgB,EAAE,KAAK,CAAC,KAAK,CAAC,YAAY,CAAC,EAAE,MAAM,CAAC,CAAC;gBACxE,IAAI,CAAC,YAAY,GAAG,gBAAgB,CAAC;aACtC;YACD,oBAAoB,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC;SACvD;QACD,IAAI,IAAI,CAAC,eAAe,KAAK,SAAS,EAAE;YACtC,oBAAoB,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC;SAC7D;QACD,IAAI,IAAI,CAAC,YAAY,KAAK,SAAS;YAAE,oBAAoB,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC;QAC3F,IAAI,IAAI,CAAC,cAAc,KAAK,SAAS,EAAE;YACrC,oBAAoB,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC;SAC3D;QACD,IAAI,IAAI,CAAC,SAAS,KAAK,SAAS;YAAE,oBAAoB,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;KACnF;IACD,OAAO,oBAAoB,CAAC;AAC9B,CAAC;AAED;;GAEG;AACH,KAAK,UAAU,eAAe,CAAE,IAA0B,EAAE,QAAkB;IAC5E,SAAS,oBAAoB,CAAE,QAAgB;QAC7C,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,MAAM;gBACtC,IAAI,OAAO,MAAM,KAAK,UAAU,EAAE;oBAChC,OAAO,MAAM,CAAC,QAAQ,CAAC,CAAC;iBACzB;gBACD,OAAO,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;YAChC,CAAC,CAAC,CAAC;SACJ;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAED,MAAM,QAAQ,GAAG,MAAM,IAAA,gBAAS,EAAC,IAAA,yBAAkB,EAAC,IAAI,CAAC,CAAC,CAAC;IAE3D,IAAI,IAAI,CAAC,QAAQ;QAAE,QAAQ,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC;IAEnD,MAAM,IAAI,GAAG,CAAC,QAAQ,EAAE,QAAQ,CAAC,IAAI,IAAI,QAAQ,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;IACnE,IAAI,IAAI,CAAC,QAAQ,EAAE;QACjB,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;KACxC;IAED;;;;OAIG;IACH,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;QACrB,MAAM,MAAM,GAAG,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC;QACxC,MAAM,MAAM,GAAG,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC;QACxC,OAAO,MAAM,GAAG,MAAM,CAAC;IACzB,CAAC,CAAC,CAAC;IAEH,KAAK,MAAM,QAAQ,IAAI,CAAC,GAAG,QAAQ,EAAE,IAAI,CAAC,GAAG,CAAC,EAAE;QAC9C,IAAI,oBAAoB,CAAC,QAAQ,CAAC,EAAE;YAClC,IAAA,eAAQ,EAAC,aAAa,GAAG,QAAQ,CAAC,CAAC;YACnC,SAAS;SACV;QAED,MAAM,cAAc,GAAG,MAAM,mBAAmB,CAC9C,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,EAC1D,qBAAqB,CAAC,QAAQ,EAAE,IAAI,CAAC,QAAQ,CAAC,CAC/C,CAAC;QAEF,0EAA0E;QAC1E,gFAAgF;QAChF,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE;YAC/B,IAAI,IAAI,CAAC,mBAAmB,KAAK,KAAK,EAAE;gBACtC,IAAA,gBAAS,EAAC,0DAA0D,CAAC,CAAC;aACvE;iBAAM;gBACL,IAAA,eAAQ,EACN,kFAAkF,EAClF,IAAI,EACJ,0DAA0D,CAC3D,CAAC;gBACF,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,IAAA,yBAAc,EAAC,IAAI,CAAC,OAAO,EAAE,OAAO,CAAC,IAAI,CAAC,EAAE;oBAC/D,6HAA6H;oBAC7H,MAAM,eAAe,GAAG,MAAM,IAAA,uCAAmB,EAAC,IAAI,EAAE,cAAc,EAAE;wBACtE,QAAQ;wBACR,mBAAmB,EAAE,IAAI,CAAC,mBAAmB;4BAC3C,CAAC,CAAC,MAAM,IAAA,mDAAsB,EAAC,IAAI,CAAC,mBAAmB,EAAE,IAAI,CAAC,QAAQ,CAAC;4BACvE,CAAC,CAAC,SAAS;qBACd,CAAC,CAAC;oBAEH,mFAAmF;oBACnF,uEAAuE;oBACvE,IAAI,eAAe,EAAE;wBACnB,cAAc,CAAC,YAAY,GAAG,eAAe,CAAC;qBAC/C;iBACF;aACF;SACF;QAED,IAAA,eAAQ,EAAC,aAAa,GAAG,QAAQ,CAAC,CAAC;QAEnC,MAAM,WAAW,GAAG,CAAC,GAAG,IAAI,CAAC,CAAC;QAE9B,IAAI,cAAc,CAAC,YAAY,EAAE;YAC/B,WAAW,CAAC,IAAI,CAAC,gBAAgB,EAAE,cAAc,CAAC,YAAY,CAAC,CAAC;SACjE;QACD,IAAI,cAAc,CAAC,SAAS,EAAE;YAC5B,WAAW,CAAC,IAAI,CAAC,cAAc,GAAG,cAAc,CAAC,SAAS,CAAC,CAAC;SAC7D;aAAM;YACL,WAAW,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;SACjC;QAED,IAAI,gBAAgB,GAAa,EAAE,CAAC;QAEpC,IAAI,cAAc,CAAC,cAAc,EAAE;YACjC,IAAI,KAAK,CAAC,OAAO,CAAC,cAAc,CAAC,cAAc,CAAC,EAAE;gBAChD,gBAAgB,CAAC,IAAI,CAAC,GAAG,cAAc,CAAC,cAAc,CAAC,CAAC;aACzD;iBAAM;gBACL,MAAM,KAAK,GAAG,cAAc,CAAC,cAAc,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,UAAU,IAAI;oBACvE,OAAO,IAAI,CAAC,IAAI,EAAE,CAAC;gBACrB,CAAC,CAAC,CAAC;gBACH,gBAAgB,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,CAAC;aACjC;SACF;QAED,IAAI,cAAc,CAAC,eAAe,IAAI,gBAAgB,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE;YAC1E,yDAAyD;YACzD,IAAI,IAAA,yBAAc,EAAC,SAAS,EAAE,QAAQ,CAAC,IAAI,CAAC,EAAE;gBAC5C,gBAAgB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;aAClC;iBAAM;gBACL,qDAAqD;gBACrD,IAAA,eAAQ,EACN,2FAA2F,CAC5F,CAAC;gBACF,gBAAgB,GAAG,gBAAgB,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,EAAE;oBACjD,OAAO,GAAG,KAAK,SAAS,CAAC;gBAC3B,CAAC,CAAC,CAAC;aACJ;SACF;QAED,IAAI,gBAAgB,CAAC,MAAM,EAAE;YAC3B,WAAW,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,GAAG,IAAI,GAAG,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;SACzE;QAED,MAAM,IAAA,oBAAa,EACjB,UAAU,EACV,WAAW,CAAC,MAAM,CAAC,gBAAgB,EAAE,cAAc,CAAC,YAAY,EAAE,QAAQ,CAAC,CAC5E,CAAC;KACH;IAED,mBAAmB;IACnB,IAAA,eAAQ,EAAC,cAAc,CAAC,CAAC;IACzB,MAAM,qBAAqB,CAAC,IAAI,CAAC,CAAC;IAClC,IAAA,eAAQ,EAAC,WAAW,CAAC,CAAC;IAEtB,mCAAmC;IACnC,IAAA,eAAQ,EAAC,4BAA4B,CAAC,CAAC;IACvC,MAAM,MAAM,GAAG,MAAM,IAAA,oBAAa,EAAC,UAAU,EAAE;QAC7C,WAAW;QACX,gBAAgB;QAChB,IAAI;QACJ,IAAI,CAAC,GAAG;KACT,CAAC,CAAC;IAEH,IAAA,eAAQ,EAAC,eAAe,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC;AAC1C,CAAC;AAED;;GAEG;AACI,KAAK,UAAU,OAAO,CAAE,KAAkB;IAC/C,IAAA,eAAQ,EAAC,sBAAsB,EAAE,UAAU,CAAC,CAAC;IAC7C,MAAM,aAAa,GAAG,MAAM,gBAAgB,CAAC,KAAK,CAAC,CAAC;IACpD,IAAI,UAAU,GAAe,EAAE,CAAC;IAChC,IAAI,aAAa,GAAoB,IAAI,CAAC;IAE1C,iCAAiC;IACjC,IAAI,aAAa,CAAC,QAAQ,EAAE;QAC1B,IAAA,eAAQ,EAAC,iCAAiC,CAAC,CAAC;QAC5C,IAAI,aAAa,CAAC,kBAAkB,KAAK,KAAK,EAAE;YAC9C,aAAa,GAAG,IAAI,0BAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;SACtD;aAAM;YACL,UAAU,GAAG,MAAM,IAAA,gCAAc,EAAC,aAAa,CAAC,QAAQ,IAAI,IAAI,EAAE,aAAa,CAAC,QAAQ,CAAC,CAAC;SAC3F;KACF;SAAM;QACL,IAAA,gBAAS,EAAC,sCAAsC,CAAC,CAAC;QAClD,IAAI,aAAa,CAAC,QAAQ,KAAK,KAAK,EAAE;YACpC,IAAI,aAAa,CAAC,IAAI,KAAK,cAAc,EAAE;gBACzC,IAAA,eAAQ,EACN,gHAAgH,CACjH,CAAC;gBACF,UAAU,GAAG,MAAM,IAAA,gCAAc,EAC/B,aAAa,CAAC,QAAQ,IAAI,IAAI,EAC9B,sCAAsC,CACvC,CAAC;aACH;iBAAM;gBACL,IAAA,eAAQ,EACN,qGAAqG,CACtG,CAAC;gBACF,UAAU,GAAG,MAAM,IAAA,gCAAc,EAAC,aAAa,CAAC,QAAQ,IAAI,IAAI,EAAE,gBAAgB,CAAC,CAAC;aACrF;SACF;aAAM;YACL,IAAA,eAAQ,EACN,8FAA8F,CAC/F,CAAC;YACF,UAAU,GAAG,MAAM,IAAA,gCAAc,EAC/B,aAAa,CAAC,QAAQ,IAAI,IAAI,EAC9B,2BAA2B,CAC5B,CAAC;SACH;KACF;IAED,IAAI,CAAC,aAAa,EAAE;QAClB,IAAI,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;YACzB,uBAAuB;YACvB,IAAI,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;gBACzB,IAAA,gBAAS,EAAC,2DAA2D,CAAC,CAAC;aACxE;iBAAM;gBACL,IAAA,eAAQ,EAAC,mBAAmB,CAAC,CAAC;aAC/B;YACD,aAAa,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;SAC/B;aAAM;YACL,oBAAoB;YACpB,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC;SACnD;KACF;IAED,sBAAsB;IACtB,IAAI,aAAa,CAAC,2BAA2B,KAAK,KAAK,EAAE;QACvD,IAAA,gBAAS,EACP,iEAAiE,EACjE,IAAI,EACJ,iEAAiE,CAClE,CAAC;KACH;SAAM;QACL,IAAA,eAAQ,EACN,sDAAsD,EACtD,IAAI,EACJ,mEAAmE,CACpE,CAAC;QACF,MAAM,IAAA,wDAA2B,EAC/B,aAAa,EACb,aAAa,CAAC,mBAAmB;YAC/B,CAAC,CAAC,MAAM,IAAA,mDAAsB,EAAC,aAAa,CAAC,mBAAmB,EAAE,aAAa,CAAC,QAAQ,CAAC;YACzF,CAAC,CAAC,IAAI,CACT,CAAC;KACH;IAED,IAAA,eAAQ,EACN,wBAAwB,EACxB,IAAI,EACJ,gBAAgB,EAChB,aAAa,CAAC,GAAG,EACjB,IAAI,EACJ,aAAa,EACb,aAAa,CAAC,QAAQ,EACtB,IAAI,EACJ,wBAAwB,EACxB,aAAa,CAAC,QAAQ,EACtB,IAAI,EACJ,aAAa,EACb,aAAa,CAAC,QAAQ,CACvB,CAAC;IACF,MAAM,eAAe,CAAC,aAAa,EAAE,aAAa,CAAC,CAAC;IAEpD,uBAAuB;IACvB,IAAA,eAAQ,EAAC,qBAAqB,CAAC,CAAC;AAClC,CAAC;AAjGD,0BAiGC;AAED;;;;GAIG;AACI,MAAM,IAAI,GAAG,CAAC,IAAiB,EAAE,EAA4B,EAAE,EAAE;IACtE,OAAO,CAAC,IAAI,CAAC;SACV,IAAI,CAAC,GAAG,EAAE;QACT,IAAA,eAAQ,EAAC,sBAAsB,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC;QAC5C,IAAI,EAAE;YAAE,EAAE,EAAE,CAAC;IACf,CAAC,CAAC;SACD,KAAK,CAAC,CAAC,GAAG,EAAE,EAAE;QACb,IAAI,GAAG,CAAC,OAAO;YAAE,IAAA,eAAQ,EAAC,GAAG,CAAC,OAAO,CAAC,CAAC;aAClC,IAAI,GAAG,CAAC,KAAK;YAAE,IAAA,eAAQ,EAAC,GAAG,CAAC,KAAK,CAAC,CAAC;;YACnC,IAAA,eAAQ,EAAC,GAAG,CAAC,CAAC;QACnB,IAAI,EAAE;YAAE,EAAE,CAAC,GAAG,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC;AACP,CAAC,CAAC;AAZW,QAAA,IAAI,QAYf"}