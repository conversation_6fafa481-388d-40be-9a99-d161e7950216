/**
 * 更新管理器
 * 统一管理版本检查、更新窗口显示和用户交互处理
 */

const VersionChecker = require('./VersionChecker');
const UpdateWindow = require('../windows/UpdateWindow');
const { app, ipcMain } = require('electron');

class UpdateManager {
  constructor(options = {}) {
    this.versionChecker = new VersionChecker(options);
    this.updateWindow = new UpdateWindow();
    this.mainWindow = null;
    this.isCheckingUpdate = false;
    this.backgroundCheckTimer = null;
    this.pendingForceUpdate = null;
    this.pendingOptionalUpdate = null;
    
    // 配置选项
    this.options = {
      enableBackgroundCheck: options.enableBackgroundCheck !== false, // 默认启用后台检查
      backgroundCheckInterval: options.backgroundCheckInterval || 4 * 60 * 60 * 1000, // 4小时检查一次
      showOptionalUpdates: options.showOptionalUpdates !== false, // 默认显示可选更新
      ...options
    };
    
    // 绑定事件处理器
    this.setupEventHandlers();
    
    console.log('UpdateManager 初始化完成', this.options);
  }

  /**
   * 设置事件处理器
   * 注意：IPC处理器统一在main.js中注册，这里只处理内部事件
   */
  setupEventHandlers() {
    // 这里不再注册IPC处理器，避免与main.js中的注册冲突
    // 所有IPC处理器都在main.js中统一管理
    console.log('UpdateManager 事件处理器设置完成');
  }

  /**
   * 设置主窗口引用
   * @param {BrowserWindow} mainWindow 主窗口实例
   */
  setMainWindow(mainWindow) {
    this.mainWindow = mainWindow;
  }

  /**
   * 应用启动时的版本检查
   * 注意：启动时总是检查更新，不受用户"自动检查"设置影响
   * @returns {Promise<boolean>} 是否允许应用继续启动
   */
  async checkOnStartup() {
    try {
      console.log('应用启动时检查版本更新...');

      // 启动时强制检查，忽略用户的autoCheck设置和缓存
      // 这确保用户每次打开程序都能获得最新的版本信息
      const result = await this.versionChecker.checkForUpdates(true); // 强制检查

      if (result.networkError) {
        console.warn('网络异常，允许应用启动:', result.errorMessage);
        this.startBackgroundCheck();
        return true; // 网络异常时允许启动
      }

      if (!result.hasUpdate) {
        console.log('当前已是最新版本');
        this.startBackgroundCheck();
        return true;
      }
      
      // 所有更新都视为强制更新，不允许跳过
      console.log('发现版本更新，需要先创建主窗口再显示强制更新提示');
      this.pendingForceUpdate = result;
      return true; // 允许创建主窗口
      
      this.startBackgroundCheck();
      return true;
      
    } catch (error) {
      console.error('启动时版本检查失败:', error);
      this.startBackgroundCheck();
      return true; // 检查失败时允许启动
    }
  }

  /**
   * 手动检查更新
   * @param {boolean} showNoUpdateMessage 是否显示"已是最新版本"消息
   */
  async checkForUpdates(showNoUpdateMessage = false) {
    if (this.isCheckingUpdate) {
      console.log('正在检查更新中，跳过重复检查');
      return;
    }
    
    this.isCheckingUpdate = true;
    
    try {
      console.log('手动检查版本更新...');
      
      const result = await this.versionChecker.checkForUpdates(true); // 强制检查
      
      if (result.networkError) {
        this.showErrorMessage('网络连接异常，请检查网络后重试');
        return;
      }
      
      if (!result.hasUpdate) {
        if (showNoUpdateMessage) {
          this.showInfoMessage('当前已是最新版本');
        }
        return;
      }
      
      // 所有更新都是强制的，不允许跳过
      this.showUpdateWindow(result, true); // 强制更新
      
    } catch (error) {
      console.error('手动版本检查失败:', error);
      this.showErrorMessage('检查更新失败: ' + error.message);
    } finally {
      this.isCheckingUpdate = false;
    }
  }

  /**
   * 显示更新窗口（在主窗口内显示模态弹窗）
   * @param {Object} versionInfo 版本信息
   * @param {boolean} forceUpdate 是否强制更新
   */
  showUpdateWindow(versionInfo, forceUpdate = false) {
    try {
      if (this.mainWindow && !this.mainWindow.isDestroyed()) {
        console.log('使用主窗口内部模态弹窗显示更新提示');

        // 发送消息给主窗口，显示内部模态弹窗（所有更新都是强制的）
        this.mainWindow.webContents.send('show-update-modal', {
          versionInfo: versionInfo,
          forceUpdate: true // 所有更新都是强制的
        });

        // 禁用主窗口的关闭按钮
        this.mainWindow.setClosable(false);
        console.log('强制更新：已禁用主窗口关闭按钮');
      } else {
        console.warn('主窗口不可用，使用独立更新窗口作为备选方案');
        this.updateWindow.show(versionInfo, forceUpdate);
      }
    } catch (error) {
      console.error('显示更新窗口失败:', error);
      // 出错时使用独立窗口作为备选
      try {
        this.updateWindow.show(versionInfo, forceUpdate);
      } catch (fallbackError) {
        console.error('备选更新窗口也失败:', fallbackError);
      }
    }
  }

  /**
   * 处理用户选择
   * @param {string} choice 用户选择
   * @param {Object} versionInfo 版本信息
   */
  handleUserChoice(choice, versionInfo) {
    console.log('处理用户选择:', choice);

    // 委托给VersionChecker处理
    this.versionChecker.handleUserChoice(choice, versionInfo);

    // 如果用户选择退出且是强制更新，则退出应用
    if (choice === 'exit' && versionInfo.forceUpdate) {
      console.log('用户选择退出，应用即将关闭');
      app.quit();
    }

    // 如果用户选择更新，发送事件通知主进程
    if (choice === 'update') {
      // 通知主进程用户已选择更新
      if (this.mainWindow && !this.mainWindow.isDestroyed()) {
        this.mainWindow.webContents.send('user-chose-update', versionInfo);
      }
    }
  }

  /**
   * 启动后台检查
   */
  startBackgroundCheck() {
    if (!this.options.enableBackgroundCheck) {
      console.log('后台检查已禁用');
      return;
    }
    
    this.updateBackgroundCheckTimer();
  }

  /**
   * 更新后台检查定时器
   */
  updateBackgroundCheckTimer() {
    // 清除现有定时器
    if (this.backgroundCheckTimer) {
      clearInterval(this.backgroundCheckTimer);
      this.backgroundCheckTimer = null;
    }

    if (!this.options.enableBackgroundCheck) {
      return;
    }

    // 检查用户的autoCheck设置
    const userPreferences = this.versionChecker.getUserPreferences();
    if (!userPreferences.autoCheck) {
      console.log('用户已关闭自动检查更新，跳过后台检查');
      return;
    }

    const interval = this.options.backgroundCheckInterval;
    console.log(`设置后台检查间隔: ${interval / 1000 / 60} 分钟`);

    this.backgroundCheckTimer = setInterval(() => {
      // 再次检查用户设置，因为用户可能在运行时更改了设置
      const currentPreferences = this.versionChecker.getUserPreferences();
      if (!currentPreferences.autoCheck) {
        console.log('用户已关闭自动检查更新，停止后台检查');
        this.stopBackgroundCheck();
        return;
      }

      console.log('后台检查版本更新...');
      this.checkForUpdates(false);
    }, interval);
  }

  /**
   * 停止后台检查
   */
  stopBackgroundCheck() {
    if (this.backgroundCheckTimer) {
      clearInterval(this.backgroundCheckTimer);
      this.backgroundCheckTimer = null;
      console.log('后台检查已停止');
    }
  }

  /**
   * 显示错误消息
   * @param {string} message 错误消息
   * @param {Object} error 错误对象
   */
  showErrorMessage(message, error = null) {
    // 根据错误类型提供更具体的提示
    let detailedMessage = message;

    if (error) {
      if (error.code === 'ENOTFOUND' || error.code === 'ECONNREFUSED') {
        detailedMessage = '无法连接到更新服务器，请检查网络连接后重试';
      } else if (error.code === 'ETIMEDOUT') {
        detailedMessage = '连接超时，请稍后重试或检查网络状况';
      } else if (error.response?.status === 404) {
        detailedMessage = '更新服务暂时不可用，请稍后重试';
      } else if (error.response?.status >= 500) {
        detailedMessage = '服务器暂时出现问题，请稍后重试';
      }
    }

    if (this.mainWindow && !this.mainWindow.isDestroyed()) {
      this.mainWindow.webContents.send('show-message', {
        type: 'error',
        title: '更新检查失败',
        message: detailedMessage,
        details: error ? {
          code: error.code,
          message: error.message,
          url: error.config?.url
        } : null
      });
    } else {
      console.error('错误消息:', detailedMessage, error);
    }
  }

  /**
   * 显示信息消息
   * @param {string} message 信息消息
   */
  showInfoMessage(message) {
    if (this.mainWindow && !this.mainWindow.isDestroyed()) {
      this.mainWindow.webContents.send('show-message', {
        type: 'info',
        title: '版本检查',
        message: message
      });
    } else {
      console.log('信息消息:', message);
    }
  }

  /**
   * 清理资源
   */
  cleanup() {
    this.stopBackgroundCheck();
    
    if (this.updateWindow) {
      this.updateWindow.close();
    }
    
    // IPC监听器在main.js中统一管理，这里不需要移除
    
    console.log('UpdateManager 资源已清理');
  }

  /**
   * 获取更新状态
   * @returns {Object} 更新状态信息
   */
  getUpdateStatus() {
    return {
      isCheckingUpdate: this.isCheckingUpdate,
      backgroundCheckEnabled: this.options.enableBackgroundCheck,
      lastCheckTime: this.versionChecker.store.get('lastCheckTime', 0),
      currentVersion: this.versionChecker.currentVersion,
      userPreferences: this.versionChecker.getUserPreferences()
    };
  }

  /**
   * 设置主窗口引用
   * @param {BrowserWindow} window 主窗口实例
   */
  setMainWindow(window) {
    this.mainWindow = window;
    console.log('UpdateManager 主窗口引用已设置');

    // 检查是否有待处理的更新
    if (this.pendingForceUpdate) {
      console.log('处理待处理的强制更新');
      setTimeout(() => {
        this.showUpdateWindow(this.pendingForceUpdate, true);
        this.pendingForceUpdate = null;
      }, 1000); // 延迟1秒确保窗口完全加载
    }
  }
}

module.exports = UpdateManager;
