#!/usr/bin/env node

/**
 * 版本号更新工具
 * 用于统一更新package.json中的版本号，并确保所有相关文件同步更新
 */

const fs = require('fs');
const path = require('path');

// 获取命令行参数
const args = process.argv.slice(2);
const newVersion = args[0];

if (!newVersion) {
    console.error('❌ 请提供新的版本号');
    console.log('用法: node scripts/update-version.js <新版本号>');
    console.log('示例: node scripts/update-version.js 1.2.0');
    process.exit(1);
}

// 验证版本号格式
const versionRegex = /^\d+\.\d+\.\d+$/;
if (!versionRegex.test(newVersion)) {
    console.error('❌ 版本号格式不正确，应为 x.y.z 格式');
    console.log('示例: 1.2.0, 2.1.3, 1.0.0');
    process.exit(1);
}

console.log(`🚀 开始更新版本号到 ${newVersion}...`);

try {
    // 1. 更新 package.json
    const packageJsonPath = path.join(__dirname, '../package.json');
    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
    const oldVersion = packageJson.version;
    
    packageJson.version = newVersion;
    fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2) + '\n');
    console.log(`✅ 已更新 package.json: ${oldVersion} → ${newVersion}`);

    // 2. 更新测试数据文件中的版本号
    const testDataPath = path.join(__dirname, '../test-update-data.sql');
    if (fs.existsSync(testDataPath)) {
        let testData = fs.readFileSync(testDataPath, 'utf8');
        
        // 替换当前版本的测试数据
        testData = testData.replace(
            /INSERT INTO `aigc_version_control` VALUES\s*\(\s*'test-1',\s*'desktop',\s*'[^']+'/g,
            `INSERT INTO \`aigc_version_control\` VALUES\n('test-1', 'desktop', '${newVersion}'`
        );
        
        fs.writeFileSync(testDataPath, testData);
        console.log(`✅ 已更新测试数据文件中的版本号`);
    }

    // 3. 检查是否有其他需要更新的文件
    console.log('\n📋 版本号更新完成！');
    console.log('\n🔍 请检查以下位置是否需要手动更新：');
    console.log('   - README.md 中的版本说明');
    console.log('   - 发布说明文档');
    console.log('   - 构建脚本中的版本引用');
    
    console.log('\n📝 下一步操作：');
    console.log('   1. 运行应用测试版本号显示是否正确');
    console.log('   2. 提交代码变更');
    console.log('   3. 创建新的版本标签');
    console.log('   4. 构建和发布新版本');
    
    console.log(`\n🎉 版本号已成功更新到 ${newVersion}！`);

} catch (error) {
    console.error('❌ 更新版本号时发生错误:', error.message);
    process.exit(1);
}
