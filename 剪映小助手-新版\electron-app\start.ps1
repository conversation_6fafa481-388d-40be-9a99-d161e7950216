# 剪映小助手启动脚本 (PowerShell)
# 设置控制台编码为UTF-8
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "    🎬 剪映小助手 - 智界AigcView出品" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# 检查Node.js是否安装
try {
    $nodeVersion = node --version 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Node.js已安装: $nodeVersion" -ForegroundColor Green
    } else {
        throw "Node.js未找到"
    }
} catch {
    Write-Host "❌ 错误: 未检测到Node.js" -ForegroundColor Red
    Write-Host "请先安装Node.js: https://nodejs.org/" -ForegroundColor Yellow
    Write-Host ""
    Read-Host "按任意键退出"
    exit 1
}

# 检查npm是否可用
try {
    $npmVersion = npm --version 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ npm已安装: $npmVersion" -ForegroundColor Green
    } else {
        throw "npm未找到"
    }
} catch {
    Write-Host "❌ 错误: npm不可用" -ForegroundColor Red
    Write-Host ""
    Read-Host "按任意键退出"
    exit 1
}

# 检查是否存在node_modules
if (-not (Test-Path "node_modules")) {
    Write-Host ""
    Write-Host "📦 首次运行，正在安装依赖..." -ForegroundColor Yellow
    Write-Host ""
    
    try {
        npm install
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ 依赖安装完成" -ForegroundColor Green
        } else {
            throw "依赖安装失败"
        }
    } catch {
        Write-Host "❌ 依赖安装失败" -ForegroundColor Red
        Write-Host ""
        Read-Host "按任意键退出"
        exit 1
    }
}

# 启动应用
Write-Host ""
Write-Host "🚀 启动剪映小助手..." -ForegroundColor Cyan
Write-Host ""

# 检查是否传入开发模式参数
if ($args[0] -eq "dev") {
    Write-Host "🔧 开发模式启动" -ForegroundColor Magenta
    npm run dev
} else {
    Write-Host "📱 生产模式启动" -ForegroundColor Blue
    npm start
}

if ($LASTEXITCODE -ne 0) {
    Write-Host ""
    Write-Host "❌ 应用启动失败" -ForegroundColor Red
    Write-Host ""
    Read-Host "按任意键退出"
    exit 1
}

Write-Host ""
Write-Host "✅ 应用已关闭" -ForegroundColor Green
Read-Host "按任意键退出"
