# 前端兼容性测试

## 📋 测试目的
验证Request类型修改（List<Object> → List<JSONObject>）对前端传参的影响

## 🧪 测试用例

### 1. 标准JSON对象数组（应该正常工作）
```javascript
// 测试timelines参数
const request1 = {
  access_key: "JianyingAPI_2025_SecureAccess_AigcView",
  zj_timelines: [
    {"start": 0, "end": 5000000},
    {"start": 5000000, "end": 10000000}
  ]
};

// 测试segment_infos参数
const request2 = {
  access_key: "JianyingAPI_2025_SecureAccess_AigcView",
  zj_ctype: "KFTypePositionX",
  zj_segment_infos: [
    {"id": "segment1", "type": "video"},
    {"id": "segment2", "type": "audio"}
  ]
};
```

### 2. 空数组（应该正常工作）
```javascript
const request3 = {
  access_key: "JianyingAPI_2025_SecureAccess_AigcView",
  zj_timelines: []
};
```

### 3. 嵌套对象（应该正常工作）
```javascript
const request4 = {
  access_key: "JianyingAPI_2025_SecureAccess_AigcView",
  zj_timelines: [
    {
      "start": 0,
      "end": 5000000,
      "metadata": {
        "type": "video",
        "duration": 5000000
      }
    }
  ]
};
```

## 🔍 测试方法

### 方法1：使用curl测试
```bash
# 测试timelines API
curl -X POST "http://localhost:8080/jeecg-boot/api/jianying/timelines" \
     -H "Content-Type: application/json" \
     -d '{
       "access_key": "JianyingAPI_2025_SecureAccess_AigcView",
       "zj_duration": 10000000,
       "zj_num": 2
     }'

# 测试caption_infos API
curl -X POST "http://localhost:8080/jeecg-boot/api/jianying/caption_infos" \
     -H "Content-Type: application/json" \
     -d '{
       "access_key": "JianyingAPI_2025_SecureAccess_AigcView",
       "zj_texts": ["第一句", "第二句"],
       "zj_timelines": [
         {"start": 0, "end": 5000000},
         {"start": 5000000, "end": 10000000}
       ]
     }'
```

### 方法2：使用Postman测试
1. 创建POST请求到各个API端点
2. 设置Content-Type为application/json
3. 在Body中发送JSON数据
4. 验证响应是否正常

### 方法3：使用JavaScript测试
```javascript
// 使用fetch API测试
async function testAPI() {
  const response = await fetch('http://localhost:8080/jeecg-boot/api/jianying/caption_infos', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      access_key: 'JianyingAPI_2025_SecureAccess_AigcView',
      zj_texts: ['测试字幕1', '测试字幕2'],
      zj_timelines: [
        {start: 0, end: 5000000},
        {start: 5000000, end: 10000000}
      ]
    })
  });
  
  const result = await response.json();
  console.log('API响应:', result);
}
```

## ✅ 预期结果

### 成功情况
- HTTP状态码：200
- 响应格式：
```json
{
  "success": true,
  "code": 200,
  "message": "操作成功",
  "data": { ... }
}
```

### 失败情况（如果有兼容性问题）
- HTTP状态码：400
- 响应格式：
```json
{
  "success": false,
  "code": 400,
  "message": "参数验证失败：类型转换错误"
}
```

## 🚨 风险评估

### 低风险场景
- ✅ 标准JSON对象数组
- ✅ 空数组
- ✅ 嵌套JSON对象

### 中风险场景
- ⚠️ 非常复杂的嵌套结构
- ⚠️ 包含特殊字符的JSON

### 高风险场景
- ❌ 传递非对象类型到对象数组字段
- ❌ 格式错误的JSON

## 📝 测试结论

**预期结论**：由于修改的字段本来就应该接收JSON对象数组，所以对前端应该没有影响。

**如果发现问题**：可以考虑以下解决方案：
1. 回滚类型修改，在Controller中进行类型转换
2. 添加自定义的JSON反序列化器
3. 使用@JsonDeserialize注解处理特殊情况

## 🔄 回滚方案

如果发现兼容性问题，可以快速回滚：
```java
// 回滚到原来的类型
private List<Object> zjTimelines;

// 在Controller中进行转换
@SuppressWarnings("unchecked")
List<JSONObject> timelines = request.getZjTimelines().stream()
    .map(obj -> obj instanceof JSONObject ? (JSONObject) obj : new JSONObject((Map<String, Object>) obj))
    .collect(Collectors.toList());
```
