package org.jeecg.modules.system.util;

import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.system.entity.SysAnnouncement;
import org.jeecg.modules.system.entity.SysAnnouncementSend;
import org.jeecg.modules.system.entity.SysUser;
import org.jeecg.modules.system.service.ISysAnnouncementSendService;
import org.jeecg.modules.system.service.ISysAnnouncementService;
import org.jeecg.common.util.IPUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * @Description: 系统通告管理类
 * @Author: jeecg-boot
 * @Date: 2025-06-21
 * @Version: V1.0
 */
@Slf4j
@Component
public class AnnouncementManager {
    
    @Autowired
    private ISysAnnouncementService sysAnnouncementService;
    
    @Autowired
    private ISysAnnouncementSendService sysAnnouncementSendService;
    
    /**
     * 创建被踢下线通告
     * @param user 用户信息
     * @param request 请求对象
     * @param kickedSessionCount 被踢会话数量
     */
    public void createKickOffAnnouncement(SysUser user, HttpServletRequest request, int kickedSessionCount) {
        try {
            SysAnnouncement announcement = new SysAnnouncement();
            
            // 基本信息
            announcement.setTitile("账号安全提醒");
            announcement.setMsgContent(buildKickOffMessage(request, kickedSessionCount));
            announcement.setMsgType("2"); // 系统消息
            announcement.setPriority("H"); // 高优先级
            announcement.setMsgCategory("2"); // 指定用户
            announcement.setSendStatus("1"); // 已发布
            announcement.setSendTime(new Date());
            announcement.setDelFlag("0");
            announcement.setCreateBy("system");
            announcement.setCreateTime(new Date());
            
            // 指定接收用户
            announcement.setUserIds(user.getId());
            
            // 特殊标记
            announcement.setBusType("KICK_OFF");
            announcement.setBusId(user.getId());
            
            // 保存通告
            sysAnnouncementService.save(announcement);
            
            // 创建用户通告关联
            SysAnnouncementSend send = new SysAnnouncementSend();
            send.setAnntId(announcement.getId());
            send.setUserId(user.getId());
            send.setReadFlag("0"); // 未读
            sysAnnouncementSendService.save(send);
            
            log.info("创建踢下线通告成功，用户：{}，被踢会话数：{}", user.getUsername(), kickedSessionCount);
            
        } catch (Exception e) {
            log.error("创建踢下线通告失败，用户：{}，错误：{}", user.getUsername(), e.getMessage(), e);
        }
    }
    
    /**
     * 构建踢下线消息内容（无emoji）
     * @param request 请求对象
     * @param kickedSessionCount 被踢会话数量
     * @return 消息内容
     */
    private String buildKickOffMessage(HttpServletRequest request, int kickedSessionCount) {
        String deviceInfo = parseDeviceInfo(request);
        String currentTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());

        return String.format(
            "账号安全提醒\n\n" +
            "检测到您的账号在新设备/浏览器登录，已自动下线其他 %d 个会话。\n\n" +
            "新登录信息：\n" +
            "设备：%s\n" +
            "时间：%s\n\n" +
            "安全提示：\n" +
            "如非本人操作，请立即修改密码\n" +
            "本系统采用严格单点登录策略\n" +
            "任何新登录都会下线其他会话\n\n" +
            "如有疑问，请联系客服。",
            kickedSessionCount, deviceInfo, currentTime
        );
    }

    /**
     * 解析设备信息
     * @param request 请求对象
     * @return 设备信息字符串
     */
    private String parseDeviceInfo(HttpServletRequest request) {
        if (request == null) {
            return "未知设备";
        }

        String userAgent = request.getHeader("User-Agent");
        String ip = IPUtils.getIpAddr(request);

        // 简单解析浏览器和操作系统
        String browser = "未知浏览器";
        String os = "未知系统";

        if (userAgent != null) {
            if (userAgent.contains("Chrome")) browser = "Chrome";
            else if (userAgent.contains("Firefox")) browser = "Firefox";
            else if (userAgent.contains("Safari")) browser = "Safari";
            else if (userAgent.contains("Edge")) browser = "Edge";

            if (userAgent.contains("Windows")) os = "Windows";
            else if (userAgent.contains("Mac")) os = "Mac";
            else if (userAgent.contains("Linux")) os = "Linux";
            else if (userAgent.contains("Android")) os = "Android";
            else if (userAgent.contains("iPhone")) os = "iPhone";
        }

        return String.format("%s浏览器 (%s) - %s", browser, os, ip);
    }
}
