// 导入功能模块
const ImportManager = {
    isImporting: false,
    currentUrl: '',

    // 初始化导入功能
    init() {
        this.bindEvents()
        this.setupIpcListeners()
        this.loadSettings()
    },

    // 加载设置
    async loadSettings() {
        try {
            const { ipcRenderer } = require('electron')
            const autoOpenFolder = await ipcRenderer.invoke('get-auto-open-folder')

            // 设置开关状态
            const autoOpenCheckbox = document.getElementById('auto-open-folder')
            if (autoOpenCheckbox) {
                autoOpenCheckbox.checked = autoOpenFolder
            }

            console.log('加载设置：自动打开文件夹 =', autoOpenFolder)
        } catch (error) {
            console.error('加载设置失败:', error)
        }
    },

    // 绑定事件
    bindEvents() {
        const urlInput = document.getElementById('draft-url')
        const importBtn = document.getElementById('import-btn')
        const pasteBtn = document.getElementById('paste-btn')
        const autoOpenCheckbox = document.getElementById('auto-open-folder')

        // URL输入框事件
        urlInput.addEventListener('input', (e) => {
            this.validateUrl(e.target.value)
        })

        urlInput.addEventListener('paste', (e) => {
            setTimeout(() => {
                this.validateUrl(e.target.value)
            }, 10)
        })

        // 导入按钮事件
        importBtn.addEventListener('click', () => {
            this.startImport()
        })

        // 粘贴按钮事件
        pasteBtn.addEventListener('click', () => {
            this.pasteFromClipboard()
        })

        // 自动打开文件夹开关事件
        if (autoOpenCheckbox) {
            autoOpenCheckbox.addEventListener('change', (e) => {
                this.saveAutoOpenFolderSetting(e.target.checked)
            })
        }

        // 快捷键支持
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey && e.key === 'Enter') {
                e.preventDefault()
                this.startImport()
            }
        })
    },

    // 保存自动打开文件夹设置
    async saveAutoOpenFolderSetting(enabled) {
        try {
            const { ipcRenderer } = require('electron')
            const result = await ipcRenderer.invoke('set-auto-open-folder', enabled)

            if (result.success) {
                console.log('设置已保存：自动打开文件夹 =', enabled)

                // 显示简短的提示
                if (window.LogManager) {
                    const message = enabled ? '✅ 已开启自动打开文件夹' : '❌ 已关闭自动打开文件夹'
                    window.LogManager.addLog(message, 'info')
                }
            }
        } catch (error) {
            console.error('保存设置失败:', error)
        }
    },

    // 设置IPC监听器
    setupIpcListeners() {
        const { ipcRenderer } = require('electron')

        // 监听导入进度消息
        ipcRenderer.on('import-progress', (event, message) => {
            this.updateStatus(message)
        })

        // 监听剪贴板检测
        ipcRenderer.on('clipboard-draft-detected', (event, url) => {
            this.handleClipboardDetection(url)
        })

        // 监听快速导入
        ipcRenderer.on('quick-import', () => {
            this.startImport()
        })

        // 监听下载进度
        ipcRenderer.on('download-progress', (event, progress) => {
            // 只在即将完成时添加日志
            if (window.LogManager && progress >= 95 && !this.logged95) {
                window.LogManager.addLog('📊 下载即将完成...', 'progress')
                this.logged95 = true
            }
        })

        // 监听导入进度（主进程发送的详细信息）
        ipcRenderer.on('import-progress', (event, message) => {
            console.log('收到导入进度:', message)

            // 使用我们的详细日志解析功能
            if (window.LogManager) {
                this.parseLogMessage(message)
            }
        })
    },

    // 处理剪贴板检测
    handleClipboardDetection(url) {
        const urlInput = document.getElementById('draft-url')
        
        if (!urlInput.value.trim()) {
            urlInput.value = url
            this.validateUrl(url)
            UI.showNotification('✨ 检测到剪贴板中的草稿链接', 'success')
        }
    },

    // 从剪贴板粘贴
    async pasteFromClipboard() {
        try {
            const text = await navigator.clipboard.readText()
            const urlInput = document.getElementById('draft-url')
            urlInput.value = text
            this.validateUrl(text)
        } catch (error) {
            console.error('读取剪贴板失败:', error)
            UI.showNotification('❌ 无法读取剪贴板内容', 'error')
        }
    },

    // 验证URL（仅验证，不更新UI）
    async validateUrlOnly(url) {
        if (!url || !url.trim()) {
            return false
        }

        try {
            const { ipcRenderer } = require('electron')
            return await ipcRenderer.invoke('validate-draft-url', url)
        } catch (error) {
            console.error('验证URL失败:', error)
            return false
        }
    },

    // 验证URL（验证并更新UI）
    async validateUrl(url) {
        const urlInput = document.getElementById('draft-url')
        const validation = document.getElementById('url-validation')
        const importBtn = document.getElementById('import-btn')

        if (!url.trim()) {
            urlInput.className = 'url-textarea'
            validation.textContent = ''
            validation.className = 'validation-message'
            importBtn.disabled = true
            return
        }

        try {
            // 检查是否为多行输入
            const lines = url.split('\n').filter(line => line.trim())

            if (lines.length > 1) {
                // 多行输入，验证每一行
                console.log('检测到多行输入，开始验证...')
                const urls = await this.parseMultipleUrls(url)

                if (urls.length > 0) {
                    urlInput.className = 'url-textarea valid'
                    validation.textContent = `✅ 找到 ${urls.length} 个有效的草稿链接`
                    validation.className = 'validation-message valid'
                    importBtn.disabled = false
                    this.currentUrl = urls[0] // 设置第一个作为当前URL
                } else {
                    urlInput.className = 'url-textarea invalid'
                    validation.textContent = '❌ 没有找到有效的草稿链接'
                    validation.className = 'validation-message invalid'
                    importBtn.disabled = true
                    this.currentUrl = ''
                }
            } else {
                // 单行输入，直接验证
                const isValid = await this.validateUrlOnly(url)

                if (isValid) {
                    urlInput.className = 'url-textarea valid'
                    validation.textContent = '✅ 有效的草稿链接'
                    validation.className = 'validation-message valid'
                    importBtn.disabled = false
                    this.currentUrl = url
                } else {
                    urlInput.className = 'url-textarea invalid'
                    validation.textContent = '❌ 无效的草稿链接格式'
                    validation.className = 'validation-message invalid'
                    importBtn.disabled = true
                    this.currentUrl = ''
                }
            }
        } catch (error) {
            console.error('验证URL失败:', error)
            urlInput.className = 'url-textarea invalid'
            validation.textContent = '❌ 验证失败'
            validation.className = 'validation-message invalid'
            importBtn.disabled = true
        }
    },

    // 开始导入（支持多链接批量处理）
    async startImport() {
        if (this.isImporting) {
            return
        }

        // 🎯 立即滚动到日志区域，让用户看到进度信息
        if (window.ScrollManager) {
            window.ScrollManager.scrollToLogs()
            console.log('📜 自动滚动到日志区域')
        }

        // 获取输入框中的所有链接
        const urlInput = document.getElementById('draft-url')
        const inputText = urlInput.value.trim()

        if (!inputText) {
            return
        }

        // 解析多行链接
        const urls = await this.parseMultipleUrls(inputText)

        if (urls.length === 0) {
            if (window.LogManager) {
                window.LogManager.addLog('❌ 没有找到有效的草稿链接', 'error')
            }
            return
        }

        try {
            console.log('=== 前端：开始批量导入流程 ===')
            console.log('找到链接数量:', urls.length)
            console.log('链接列表:', urls)

            // 1. 检查剪映路径
            console.log('检查剪映路径...')
            const pathCheckResult = await this.checkJianyingPath()
            if (!pathCheckResult.success) {
                console.log('剪映路径检查失败，显示路径设置弹窗')
                this.showJianyingPathModal()
                return
            }
            console.log('剪映路径检查通过')

            this.isImporting = true
            this.totalFiles = urls.length  // 🆕 设置总文件数

            // 🆕 初始化批量下载状态（如果是多链接）
            if (urls.length > 1 && window.DownloadProgressManager) {
                console.log('🚀 [BATCH] 初始化批量下载状态，链接数量:', urls.length)
                console.log('🚀 [BATCH] DownloadProgressManager存在:', !!window.DownloadProgressManager)
                window.DownloadProgressManager.startBatch({
                    totalFiles: urls.length,
                    fileList: urls.map((url, index) => `文件${index + 1}`)
                })
                console.log('🚀 [BATCH] 批量状态初始化完成，当前batchInfo:', window.DownloadProgressManager.batchInfo)
            } else {
                console.log('🔍 [BATCH] 不满足批量下载条件:', {
                    urlsLength: urls.length,
                    hasDownloadManager: !!window.DownloadProgressManager
                })
            }

            // 2. 批量处理所有链接
            let successCount = 0
            let failCount = 0

            for (let i = 0; i < urls.length; i++) {
                const url = urls[i]
                console.log(`开始处理第 ${i + 1}/${urls.length} 个链接:`, url)

                try {
                    this.currentUrl = url
                    this.currentFileIndex = i + 1  // 🆕 设置当前文件索引

                    // 🔧 [FIX] 在开始新文件前，强制保持进度条显示
                    if (urls.length > 1 && window.DownloadProgressManager) {
                        window.DownloadProgressManager.forceKeepVisible()
                        console.log(`🛡️ [BATCH] 强制保持进度条显示 - 处理第 ${i + 1}/${urls.length} 个文件`)
                    }

                    // 下载草稿
                    console.log('开始下载草稿...')
                    await this.downloadDraft()

                    // 导入到剪映
                    await this.importToJianying()

                    successCount++
                    console.log(`第 ${i + 1} 个链接导入成功`)

                    // 🔧 [FIX] 文件完成后，再次确保进度条状态正确
                    if (urls.length > 1 && window.DownloadProgressManager && i < urls.length - 1) {
                        // 不是最后一个文件，确保进度条保持显示
                        window.DownloadProgressManager.forceKeepVisible()
                        console.log(`🛡️ [BATCH] 文件 ${i + 1} 完成，保持进度条显示继续处理`)
                    }

                } catch (error) {
                    failCount++
                    console.error(`第 ${i + 1} 个链接导入失败:`, error)

                    // 添加错误日志
                    if (window.LogManager) {
                        window.LogManager.addLog(`❌ 第 ${i + 1} 个链接导入失败: ${error.message}`, 'error')
                    }

                    // 🔧 [FIX] 即使出错也要保持进度条显示（如果不是最后一个文件）
                    if (urls.length > 1 && window.DownloadProgressManager && i < urls.length - 1) {
                        window.DownloadProgressManager.forceKeepVisible()
                        console.log(`🛡️ [BATCH] 文件 ${i + 1} 出错，但保持进度条显示继续处理`)
                    }

                    // 继续处理下一个链接，不中断整个流程
                }
            }

            // 🆕 完成批量下载（如果是多链接）
            if (urls.length > 1 && window.DownloadProgressManager) {
                console.log('✅ [BATCH] 批量下载完成')
                window.DownloadProgressManager.completeBatch({
                    successCount,
                    failedCount: failCount,
                    totalFiles: urls.length
                })
            }

            // 3. 显示批量处理结果
            if (window.LogManager) {
                if (failCount === 0) {
                    window.LogManager.addLog(`🎉 全部导入成功！共 ${successCount} 个草稿`, 'success')
                } else if (successCount === 0) {
                    window.LogManager.addLog(`❌ 全部导入失败！共 ${failCount} 个草稿`, 'error')
                } else {
                    window.LogManager.addLog(`⚠️ 部分导入成功：成功 ${successCount} 个，失败 ${failCount} 个`, 'info')
                }
            }

        } catch (error) {
            console.error('批量导入失败:', error)

            // 添加错误日志
            if (window.LogManager) {
                window.LogManager.addLog(`❌ 批量导入失败: ${error.message}`, 'error')
                window.LogManager.setImporting(false)
            }

            // 错误已在上面的日志中记录
        } finally {
            this.isImporting = false
        }
    },

    // 解析多行链接
    async parseMultipleUrls(inputText) {
        const urls = []

        console.log('=== 开始解析多行链接 ===')
        console.log('输入文本:', inputText)

        // 按行分割
        const lines = inputText.split('\n')
        console.log('分割后的行数:', lines.length)

        for (let i = 0; i < lines.length; i++) {
            const line = lines[i]
            const trimmedLine = line.trim()
            console.log(`处理第 ${i + 1} 行:`, trimmedLine)

            if (!trimmedLine) {
                console.log(`第 ${i + 1} 行为空，跳过`)
                continue
            }

            // 提取真实的TOS链接
            const extractedUrl = this.extractRealUrl(trimmedLine)
            console.log(`第 ${i + 1} 行提取的URL:`, extractedUrl)

            if (extractedUrl) {
                const isValid = await this.validateUrlOnly(extractedUrl)
                console.log(`第 ${i + 1} 行验证结果:`, isValid)

                if (isValid) {
                    urls.push(extractedUrl)
                    console.log(`第 ${i + 1} 行添加到有效URL列表`)
                } else {
                    console.log(`第 ${i + 1} 行验证失败，跳过`)
                }
            } else {
                console.log(`第 ${i + 1} 行无法提取URL，跳过`)
            }
        }

        console.log('=== 解析完成 ===')
        console.log('有效URL数量:', urls.length)
        console.log('有效URL列表:', urls)
        return urls
    },

    // 提取真实的TOS链接
    extractRealUrl(url) {
        console.log('原始链接:', url)

        // 格式1：https://www.aigcview.com/JianYingDraft?draft=真实链接
        if (url.includes('JianYingDraft?draft=')) {
            const match = url.match(/draft=(.+)/)
            if (match) {
                const extracted = decodeURIComponent(match[1])
                console.log('提取的TOS链接 (格式1):', extracted)
                return extracted
            }
        }

        // 格式2：https://aigcview.com/JianYingDraft?draft=真实链接
        if (url.includes('aigcview.com') && url.includes('?draft=')) {
            const match = url.match(/draft=(.+)/)
            if (match) {
                const extracted = decodeURIComponent(match[1])
                console.log('提取的TOS链接 (格式2):', extracted)
                return extracted
            }
        }

        // 格式3：直接的TOS链接（火山引擎TOS）
        if (url.includes('volces.com') && url.includes('jianying-assistant')) {
            console.log('直接的TOS链接 (格式3):', url)
            return url
        }

        // 格式4：包含.json或.zip的链接
        if (url.includes('.json') || url.includes('.zip')) {
            console.log('包含文件扩展名的链接 (格式4):', url)
            return url
        }

        // 其他格式，直接返回（可能是其他有效链接）
        console.log('未识别格式，直接返回:', url)
        return url
    },



    // 检查剪映路径
    async checkJianyingPath() {
        const { ipcRenderer } = require('electron')
        const jianyingPath = await ipcRenderer.invoke('get-jianying-path')
        
        if (!jianyingPath) {
            return { success: false, error: '未设置剪映路径' }
        }

        return { success: true, path: jianyingPath }
    },

    // 显示剪映路径设置模态框
    showJianyingPathModal() {
        const modal = {
            title: '🎬 需要设置剪映路径',
            content: `
                <div class="modal-message">
                    <div class="message-icon">⚠️</div>
                    <div class="message-text">
                        <p>在导入草稿之前，需要先设置剪映的安装路径。</p>
                        <p>请点击下方按钮前往设置页面进行配置。</p>
                    </div>
                </div>
            `,
            actions: [
                {
                    text: '取消',
                    type: 'secondary',
                    action: () => UI.hideModal()
                },
                {
                    text: '去设置',
                    type: 'primary',
                    action: () => {
                        UI.hideModal()
                        UI.switchPage('settings')
                    }
                }
            ]
        }
        
        UI.showModal(modal)
    },



    // 更新状态（简化版，只记录关键步骤）
    updateStatus(status) {
        // 只记录关键步骤到日志
        if (window.LogManager) {
            // 过滤掉不需要的详细信息
            if (status.includes('🔍 验证链接') ||
                status.includes('📥 下载') ||
                status.includes('📦 解压') ||
                status.includes('🎬 导入') ||
                status.includes('✅ 导入完成')) {

                let logType = 'info'
                if (status.includes('✅') || status.includes('完成')) {
                    logType = 'success'
                } else if (status.includes('❌') || status.includes('失败')) {
                    logType = 'error'
                } else if (status.includes('📥') || status.includes('📦') || status.includes('🎬')) {
                    logType = 'progress'
                }

                window.LogManager.addLog(status, logType)
            }
        }
    },

    // 解析并显示详细的处理日志
    parseAndDisplayDetailedLogs(response) {
        if (!window.LogManager) return

        // 如果响应中包含处理日志，显示它们
        if (response.processing_logs && Array.isArray(response.processing_logs)) {
            response.processing_logs.forEach(logMsg => {
                this.parseLogMessage(logMsg)
            })
        }

        // 如果响应中包含错误信息，解析并显示
        if (response.error) {
            window.LogManager.addLog(`❌ 处理失败: ${response.error}`, 'error')
        }
    },

    // 解析单条日志消息，提取关键信息显示给用户
    parseLogMessage(logMsg) {
        if (!window.LogManager) return

        let logType = 'info'
        let displayMsg = logMsg

        // 直接显示主进程发送的格式化消息
        if (logMsg.includes('🎵') || logMsg.includes('📹') || logMsg.includes('🖼️') ||
            logMsg.includes('✅') || logMsg.includes('❌') || logMsg.includes('⚠️') ||
            logMsg.includes('📥') || logMsg.includes('🔄')) {

            // 确定日志类型
            if (logMsg.includes('✅')) {
                logType = 'success'
            } else if (logMsg.includes('❌')) {
                logType = 'error'
            } else if (logMsg.includes('⚠️')) {
                logType = 'error'
            } else if (logMsg.includes('正在') || logMsg.includes('发现') || logMsg.includes('🔄')) {
                logType = 'progress'
            } else {
                logType = 'info'
            }

            displayMsg = logMsg
        } else {
            // 对于其他技术日志，过滤掉不显示
            return
        }

        window.LogManager.addLog(displayMsg, logType)
    },

    // 下载草稿
    async downloadDraft() {
        console.log('=== 前端：downloadDraft方法被调用 ===')
        console.log('当前URL:', this.currentUrl)
        console.log('当前文件索引:', this.currentFileIndex)

        const isZip = this.currentUrl.endsWith('.zip')
        const fileType = isZip ? '草稿包' : '草稿文件'

        console.log('文件类型:', fileType)

        this.updateStatus(`📥 下载${fileType}...`)

        // 🆕 如果有文件索引信息，传递给主进程
        const downloadParams = {
            url: this.currentUrl,
            currentFileIndex: this.currentFileIndex || 1,
            totalFiles: this.totalFiles || 1
        }

        // 🔧 [FIX] 确保批量信息正确传递，防止进度条闪烁
        console.log('🔧 [BATCH] 传递给主进程的批量信息:', downloadParams)

        console.log('开始调用IPC事件 download-draft，参数:', downloadParams)
        const { ipcRenderer } = require('electron')
        const result = await ipcRenderer.invoke('download-draft', downloadParams)
        console.log('IPC调用结果:', result)

        if (!result.success) {
            throw new Error(result.error || '下载失败')
        }

        this.draftPath = result.path
        this.isZipFile = result.isZip || false
        console.log('草稿文件路径:', this.draftPath)
        console.log('是否为ZIP文件:', this.isZipFile)
        return result
    },

    // 导入到剪映
    async importToJianying() {
        console.log('=== 前端：开始导入到剪映 ===')
        console.log('草稿路径:', this.draftPath)
        console.log('是否为ZIP:', this.isZipFile)

        if (this.isZipFile) {
            this.updateStatus('📦 解压草稿包...')
        }

        this.updateStatus('🎬 导入到剪映...')

        console.log('调用import-to-jianying IPC事件...')
        const { ipcRenderer } = require('electron')
        const result = await ipcRenderer.invoke('import-to-jianying', this.draftPath, this.isZipFile)
        console.log('导入结果:', result)

        if (!result.success) {
            throw new Error(result.error || '导入失败')
        }

        this.updateStatus('✅ 导入完成')

        // 添加详细的完成日志
        if (window.LogManager) {
            window.LogManager.addLog('🎉 草稿导入成功！', 'success')

            // 显示完整的路径信息
            if (result.projectPath) {
                window.LogManager.addLog(`📁 草稿已保存到: ${result.projectPath}`, 'info')
            } else {
                window.LogManager.addLog('📁 草稿已保存到剪映草稿文件夹', 'info')
            }

            window.LogManager.setImporting(false)
        }

        // 根据用户设置决定是否自动打开文件夹
        if (result.projectPath) {
            await this.conditionalOpenProjectFolder(result.projectPath)
        }

        // 导入完成后重新显示等待提示
        setTimeout(() => {
            if (window.LogManager) {
                window.LogManager.addLog('等待用户输入草稿链接...', 'info')
            }
        }, 2000) // 2秒后显示等待提示

        return result
    },



    // 重置表单（保持输入框内容不变）
    resetForm() {
        const urlInput = document.getElementById('draft-url')
        const validation = document.getElementById('url-validation')
        const importBtn = document.getElementById('import-btn')

        // 不清空输入框内容，保持用户输入
        // urlInput.value = ''
        urlInput.className = 'url-textarea'
        validation.textContent = ''
        validation.className = 'validation-message'

        // 如果输入框有内容且有效，启用导入按钮
        if (urlInput.value.trim() && this.validateUrl(urlInput.value.trim())) {
            importBtn.disabled = false
        } else {
            importBtn.disabled = true
        }

        // 保持currentUrl，以便支持重复导入
        // this.currentUrl = ''
        this.draftPath = ''
        this.isImporting = false

        // 重置进度标志
        this.logged95 = false
    },

    // 根据设置条件打开项目文件夹
    async conditionalOpenProjectFolder(projectPath) {
        try {
            const { ipcRenderer } = require('electron')
            const autoOpenFolder = await ipcRenderer.invoke('get-auto-open-folder')

            if (autoOpenFolder) {
                console.log('用户设置：自动打开文件夹已开启，正在打开...')
                await this.openProjectFolder(projectPath)
            } else {
                console.log('用户设置：自动打开文件夹已关闭，跳过打开')
                if (window.LogManager) {
                    window.LogManager.addLog('📁 草稿已保存（自动打开已关闭）', 'info')
                }
            }
        } catch (error) {
            console.error('检查自动打开设置失败:', error)
            // 如果检查设置失败，默认不打开
        }
    },

    // 打开项目文件夹（静默模式，不显示通知）
    async openProjectFolder(projectPath) {
        try {
            const { ipcRenderer } = require('electron')
            const result = await ipcRenderer.invoke('open-project-folder', projectPath)

            if (result.success) {
                console.log('文件夹打开成功:', projectPath)
                if (window.LogManager) {
                    window.LogManager.addLog('📂 已打开草稿文件夹', 'info')
                }
            } else {
                console.error('文件夹打开失败:', result.error)
                // 导入完成后的自动打开失败不显示错误日志，避免干扰用户
                console.log('静默模式：文件夹打开失败，不显示错误提示')
            }
        } catch (error) {
            console.error('打开文件夹异常:', error)
            // 导入完成后的自动打开异常不显示错误日志
            console.log('静默模式：文件夹打开异常，不显示错误提示')
        }
    }
}

// 日志管理器
const LogManager = {
    logs: [],
    startTime: null,

    // 初始化日志
    init() {
        this.startTime = new Date()
        this.bindEvents()
        this.addLog('等待用户输入草稿链接...', 'info')
    },

    // 绑定事件
    bindEvents() {
        const clearBtn = document.getElementById('clear-logs-btn')
        if (clearBtn) {
            clearBtn.addEventListener('click', () => {
                this.clearLogs()
            })
        }
    },

    // 添加日志
    addLog(message, type = 'info') {
        const now = new Date()
        const timeStr = this.formatTime(now)

        const log = {
            time: timeStr,
            message: message,
            type: type,
            timestamp: now
        }

        this.logs.push(log)
        this.renderLogs()
    },

    // 格式化时间（显示具体时间）
    formatTime(date) {
        const hours = date.getHours().toString().padStart(2, '0')
        const minutes = date.getMinutes().toString().padStart(2, '0')
        const seconds = date.getSeconds().toString().padStart(2, '0')
        return `[${hours}:${minutes}:${seconds}]`
    },

    // 渲染日志
    renderLogs() {
        const logsContent = document.getElementById('logs-content')
        if (!logsContent) return

        logsContent.innerHTML = this.logs.map(log => `
            <div class="log-item ${log.type}">
                <span class="log-time">${log.time}</span>
                <span class="log-text">${log.message}</span>
            </div>
        `).join('')

        // 滚动到底部
        logsContent.scrollTop = logsContent.scrollHeight
    },



    // 清空日志
    clearLogs() {
        this.logs = []
        this.startTime = new Date()
        this.addLog('日志已清空，等待用户操作...', 'info')
    },

    // 处理音频导入（简化版 - 显示基本日志）
    async processAudioImport(audioData) {
        try {
            this.addLog('开始处理音频文件...', 'info')
            this.setImporting(true)

            const response = await fetch('https://www.aigcview.com/jeecg-boot/api/jianying/add_audios', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(audioData)
            })

            const result = await response.json()

            if (result.error) {
                this.addLog(`音频处理失败: ${result.error}`, 'error')
                // 如果有处理日志，显示它们
                if (result.processing_logs && Array.isArray(result.processing_logs)) {
                    result.processing_logs.forEach(logMsg => {
                        this.addLog(logMsg, 'info')
                    })
                }
                return
            }

            // 显示处理日志
            if (result.processing_logs && Array.isArray(result.processing_logs)) {
                result.processing_logs.forEach(logMsg => {
                    this.addLog(logMsg, 'info')
                })
            }

            // 显示成功消息
            let message = `音频处理完成！`
            if (result.processing_time) {
                message += ` 耗时: ${result.processing_time}`
            }

            this.addLog(message, 'success')

        } catch (error) {
            console.error('音频处理请求失败:', error)
            this.addLog(`音频处理请求失败: ${error.message}`, 'error')
        } finally {
            this.setImporting(false)
        }
    },

    // 设置导入状态
    setImporting(isImporting) {
        this.isImporting = isImporting
    }
}

// 扩展ImportManager添加日志功能
const originalInit = ImportManager.init
ImportManager.init = function() {
    originalInit.call(this)
    LogManager.init()
}

// 将LogManager设为全局可用
window.LogManager = LogManager

// 重写一些方法来添加日志
const originalValidateUrl = ImportManager.validateUrl
ImportManager.validateUrl = function(url) {
    const result = originalValidateUrl.call(this, url)
    // 删除重复的检测日志，避免与handleClipboardDetection中的通知重复
    return result
}

const originalStartImport = ImportManager.startImport
ImportManager.startImport = function() {
    // 🎯 确保滚动功能在重写的方法中也能正常工作
    if (window.ScrollManager) {
        window.ScrollManager.scrollToLogs()
        console.log('📜 [重写方法] 自动滚动到日志区域')
    }

    LogManager.addLog('开始下载草稿文件...', 'info')
    LogManager.setImporting(true)
    return originalStartImport.call(this)
}

// 导出模块
window.ImportManager = ImportManager
window.LogManager = LogManager
