(function (global, factory) {
  typeof exports === 'object' && typeof module !== 'undefined' ? factory(exports) :
  typeof define === 'function' && define.amd ? define(['exports'], factory) :
  (global = global || self, factory(global.TOS = {}));
}(this, (function (exports) { 'use strict';

  var bind = function bind(fn, thisArg) {
    return function wrap() {
      var args = new Array(arguments.length);
      for (var i = 0; i < args.length; i++) {
        args[i] = arguments[i];
      }
      return fn.apply(thisArg, args);
    };
  };

  // utils is a library of generic helper functions non-specific to axios

  var toString = Object.prototype.toString;

  /**
   * Determine if a value is an Array
   *
   * @param {Object} val The value to test
   * @returns {boolean} True if value is an Array, otherwise false
   */
  function isArray(val) {
    return toString.call(val) === '[object Array]';
  }

  /**
   * Determine if a value is undefined
   *
   * @param {Object} val The value to test
   * @returns {boolean} True if the value is undefined, otherwise false
   */
  function isUndefined(val) {
    return typeof val === 'undefined';
  }

  /**
   * Determine if a value is a Buffer
   *
   * @param {Object} val The value to test
   * @returns {boolean} True if value is a Buffer, otherwise false
   */
  function isBuffer(val) {
    return val !== null && !isUndefined(val) && val.constructor !== null && !isUndefined(val.constructor)
      && typeof val.constructor.isBuffer === 'function' && val.constructor.isBuffer(val);
  }

  /**
   * Determine if a value is an ArrayBuffer
   *
   * @param {Object} val The value to test
   * @returns {boolean} True if value is an ArrayBuffer, otherwise false
   */
  function isArrayBuffer(val) {
    return toString.call(val) === '[object ArrayBuffer]';
  }

  /**
   * Determine if a value is a FormData
   *
   * @param {Object} val The value to test
   * @returns {boolean} True if value is an FormData, otherwise false
   */
  function isFormData(val) {
    return (typeof FormData !== 'undefined') && (val instanceof FormData);
  }

  /**
   * Determine if a value is a view on an ArrayBuffer
   *
   * @param {Object} val The value to test
   * @returns {boolean} True if value is a view on an ArrayBuffer, otherwise false
   */
  function isArrayBufferView(val) {
    var result;
    if ((typeof ArrayBuffer !== 'undefined') && (ArrayBuffer.isView)) {
      result = ArrayBuffer.isView(val);
    } else {
      result = (val) && (val.buffer) && (val.buffer instanceof ArrayBuffer);
    }
    return result;
  }

  /**
   * Determine if a value is a String
   *
   * @param {Object} val The value to test
   * @returns {boolean} True if value is a String, otherwise false
   */
  function isString(val) {
    return typeof val === 'string';
  }

  /**
   * Determine if a value is a Number
   *
   * @param {Object} val The value to test
   * @returns {boolean} True if value is a Number, otherwise false
   */
  function isNumber(val) {
    return typeof val === 'number';
  }

  /**
   * Determine if a value is an Object
   *
   * @param {Object} val The value to test
   * @returns {boolean} True if value is an Object, otherwise false
   */
  function isObject(val) {
    return val !== null && typeof val === 'object';
  }

  /**
   * Determine if a value is a plain Object
   *
   * @param {Object} val The value to test
   * @return {boolean} True if value is a plain Object, otherwise false
   */
  function isPlainObject(val) {
    if (toString.call(val) !== '[object Object]') {
      return false;
    }

    var prototype = Object.getPrototypeOf(val);
    return prototype === null || prototype === Object.prototype;
  }

  /**
   * Determine if a value is a Date
   *
   * @param {Object} val The value to test
   * @returns {boolean} True if value is a Date, otherwise false
   */
  function isDate(val) {
    return toString.call(val) === '[object Date]';
  }

  /**
   * Determine if a value is a File
   *
   * @param {Object} val The value to test
   * @returns {boolean} True if value is a File, otherwise false
   */
  function isFile(val) {
    return toString.call(val) === '[object File]';
  }

  /**
   * Determine if a value is a Blob
   *
   * @param {Object} val The value to test
   * @returns {boolean} True if value is a Blob, otherwise false
   */
  function isBlob(val) {
    return toString.call(val) === '[object Blob]';
  }

  /**
   * Determine if a value is a Function
   *
   * @param {Object} val The value to test
   * @returns {boolean} True if value is a Function, otherwise false
   */
  function isFunction(val) {
    return toString.call(val) === '[object Function]';
  }

  /**
   * Determine if a value is a Stream
   *
   * @param {Object} val The value to test
   * @returns {boolean} True if value is a Stream, otherwise false
   */
  function isStream(val) {
    return isObject(val) && isFunction(val.pipe);
  }

  /**
   * Determine if a value is a URLSearchParams object
   *
   * @param {Object} val The value to test
   * @returns {boolean} True if value is a URLSearchParams object, otherwise false
   */
  function isURLSearchParams(val) {
    return typeof URLSearchParams !== 'undefined' && val instanceof URLSearchParams;
  }

  /**
   * Trim excess whitespace off the beginning and end of a string
   *
   * @param {String} str The String to trim
   * @returns {String} The String freed of excess whitespace
   */
  function trim(str) {
    return str.trim ? str.trim() : str.replace(/^\s+|\s+$/g, '');
  }

  /**
   * Determine if we're running in a standard browser environment
   *
   * This allows axios to run in a web worker, and react-native.
   * Both environments support XMLHttpRequest, but not fully standard globals.
   *
   * web workers:
   *  typeof window -> undefined
   *  typeof document -> undefined
   *
   * react-native:
   *  navigator.product -> 'ReactNative'
   * nativescript
   *  navigator.product -> 'NativeScript' or 'NS'
   */
  function isStandardBrowserEnv() {
    if (typeof navigator !== 'undefined' && (navigator.product === 'ReactNative' ||
                                             navigator.product === 'NativeScript' ||
                                             navigator.product === 'NS')) {
      return false;
    }
    return (
      typeof window !== 'undefined' &&
      typeof document !== 'undefined'
    );
  }

  /**
   * Iterate over an Array or an Object invoking a function for each item.
   *
   * If `obj` is an Array callback will be called passing
   * the value, index, and complete array for each item.
   *
   * If 'obj' is an Object callback will be called passing
   * the value, key, and complete object for each property.
   *
   * @param {Object|Array} obj The object to iterate
   * @param {Function} fn The callback to invoke for each item
   */
  function forEach(obj, fn) {
    // Don't bother if no value provided
    if (obj === null || typeof obj === 'undefined') {
      return;
    }

    // Force an array if not already something iterable
    if (typeof obj !== 'object') {
      /*eslint no-param-reassign:0*/
      obj = [obj];
    }

    if (isArray(obj)) {
      // Iterate over array values
      for (var i = 0, l = obj.length; i < l; i++) {
        fn.call(null, obj[i], i, obj);
      }
    } else {
      // Iterate over object keys
      for (var key in obj) {
        if (Object.prototype.hasOwnProperty.call(obj, key)) {
          fn.call(null, obj[key], key, obj);
        }
      }
    }
  }

  /**
   * Accepts varargs expecting each argument to be an object, then
   * immutably merges the properties of each object and returns result.
   *
   * When multiple objects contain the same key the later object in
   * the arguments list will take precedence.
   *
   * Example:
   *
   * ```js
   * var result = merge({foo: 123}, {foo: 456});
   * console.log(result.foo); // outputs 456
   * ```
   *
   * @param {Object} obj1 Object to merge
   * @returns {Object} Result of all merge properties
   */
  function merge(/* obj1, obj2, obj3, ... */) {
    var result = {};
    function assignValue(val, key) {
      if (isPlainObject(result[key]) && isPlainObject(val)) {
        result[key] = merge(result[key], val);
      } else if (isPlainObject(val)) {
        result[key] = merge({}, val);
      } else if (isArray(val)) {
        result[key] = val.slice();
      } else {
        result[key] = val;
      }
    }

    for (var i = 0, l = arguments.length; i < l; i++) {
      forEach(arguments[i], assignValue);
    }
    return result;
  }

  /**
   * Extends object a by mutably adding to it the properties of object b.
   *
   * @param {Object} a The object to be extended
   * @param {Object} b The object to copy properties from
   * @param {Object} thisArg The object to bind function to
   * @return {Object} The resulting value of object a
   */
  function extend(a, b, thisArg) {
    forEach(b, function assignValue(val, key) {
      if (thisArg && typeof val === 'function') {
        a[key] = bind(val, thisArg);
      } else {
        a[key] = val;
      }
    });
    return a;
  }

  /**
   * Remove byte order marker. This catches EF BB BF (the UTF-8 BOM)
   *
   * @param {string} content with BOM
   * @return {string} content value without BOM
   */
  function stripBOM(content) {
    if (content.charCodeAt(0) === 0xFEFF) {
      content = content.slice(1);
    }
    return content;
  }

  var utils = {
    isArray: isArray,
    isArrayBuffer: isArrayBuffer,
    isBuffer: isBuffer,
    isFormData: isFormData,
    isArrayBufferView: isArrayBufferView,
    isString: isString,
    isNumber: isNumber,
    isObject: isObject,
    isPlainObject: isPlainObject,
    isUndefined: isUndefined,
    isDate: isDate,
    isFile: isFile,
    isBlob: isBlob,
    isFunction: isFunction,
    isStream: isStream,
    isURLSearchParams: isURLSearchParams,
    isStandardBrowserEnv: isStandardBrowserEnv,
    forEach: forEach,
    merge: merge,
    extend: extend,
    trim: trim,
    stripBOM: stripBOM
  };

  function encode(val) {
    return encodeURIComponent(val).
      replace(/%3A/gi, ':').
      replace(/%24/g, '$').
      replace(/%2C/gi, ',').
      replace(/%20/g, '+').
      replace(/%5B/gi, '[').
      replace(/%5D/gi, ']');
  }

  /**
   * Build a URL by appending params to the end
   *
   * @param {string} url The base of the url (e.g., http://www.google.com)
   * @param {object} [params] The params to be appended
   * @returns {string} The formatted url
   */
  var buildURL = function buildURL(url, params, paramsSerializer) {
    /*eslint no-param-reassign:0*/
    if (!params) {
      return url;
    }

    var serializedParams;
    if (paramsSerializer) {
      serializedParams = paramsSerializer(params);
    } else if (utils.isURLSearchParams(params)) {
      serializedParams = params.toString();
    } else {
      var parts = [];

      utils.forEach(params, function serialize(val, key) {
        if (val === null || typeof val === 'undefined') {
          return;
        }

        if (utils.isArray(val)) {
          key = key + '[]';
        } else {
          val = [val];
        }

        utils.forEach(val, function parseValue(v) {
          if (utils.isDate(v)) {
            v = v.toISOString();
          } else if (utils.isObject(v)) {
            v = JSON.stringify(v);
          }
          parts.push(encode(key) + '=' + encode(v));
        });
      });

      serializedParams = parts.join('&');
    }

    if (serializedParams) {
      var hashmarkIndex = url.indexOf('#');
      if (hashmarkIndex !== -1) {
        url = url.slice(0, hashmarkIndex);
      }

      url += (url.indexOf('?') === -1 ? '?' : '&') + serializedParams;
    }

    return url;
  };

  function InterceptorManager() {
    this.handlers = [];
  }

  /**
   * Add a new interceptor to the stack
   *
   * @param {Function} fulfilled The function to handle `then` for a `Promise`
   * @param {Function} rejected The function to handle `reject` for a `Promise`
   *
   * @return {Number} An ID used to remove interceptor later
   */
  InterceptorManager.prototype.use = function use(fulfilled, rejected, options) {
    this.handlers.push({
      fulfilled: fulfilled,
      rejected: rejected,
      synchronous: options ? options.synchronous : false,
      runWhen: options ? options.runWhen : null
    });
    return this.handlers.length - 1;
  };

  /**
   * Remove an interceptor from the stack
   *
   * @param {Number} id The ID that was returned by `use`
   */
  InterceptorManager.prototype.eject = function eject(id) {
    if (this.handlers[id]) {
      this.handlers[id] = null;
    }
  };

  /**
   * Iterate over all the registered interceptors
   *
   * This method is particularly useful for skipping over any
   * interceptors that may have become `null` calling `eject`.
   *
   * @param {Function} fn The function to call for each interceptor
   */
  InterceptorManager.prototype.forEach = function forEach(fn) {
    utils.forEach(this.handlers, function forEachHandler(h) {
      if (h !== null) {
        fn(h);
      }
    });
  };

  var InterceptorManager_1 = InterceptorManager;

  var normalizeHeaderName = function normalizeHeaderName(headers, normalizedName) {
    utils.forEach(headers, function processHeader(value, name) {
      if (name !== normalizedName && name.toUpperCase() === normalizedName.toUpperCase()) {
        headers[normalizedName] = value;
        delete headers[name];
      }
    });
  };

  /**
   * Update an Error with the specified config, error code, and response.
   *
   * @param {Error} error The error to update.
   * @param {Object} config The config.
   * @param {string} [code] The error code (for example, 'ECONNABORTED').
   * @param {Object} [request] The request.
   * @param {Object} [response] The response.
   * @returns {Error} The error.
   */
  var enhanceError = function enhanceError(error, config, code, request, response) {
    error.config = config;
    if (code) {
      error.code = code;
    }

    error.request = request;
    error.response = response;
    error.isAxiosError = true;

    error.toJSON = function toJSON() {
      return {
        // Standard
        message: this.message,
        name: this.name,
        // Microsoft
        description: this.description,
        number: this.number,
        // Mozilla
        fileName: this.fileName,
        lineNumber: this.lineNumber,
        columnNumber: this.columnNumber,
        stack: this.stack,
        // Axios
        config: this.config,
        code: this.code
      };
    };
    return error;
  };

  /**
   * Create an Error with the specified message, config, error code, request and response.
   *
   * @param {string} message The error message.
   * @param {Object} config The config.
   * @param {string} [code] The error code (for example, 'ECONNABORTED').
   * @param {Object} [request] The request.
   * @param {Object} [response] The response.
   * @returns {Error} The created error.
   */
  var createError = function createError(message, config, code, request, response) {
    var error = new Error(message);
    return enhanceError(error, config, code, request, response);
  };

  /**
   * Resolve or reject a Promise based on response status.
   *
   * @param {Function} resolve A function that resolves the promise.
   * @param {Function} reject A function that rejects the promise.
   * @param {object} response The response.
   */
  var settle = function settle(resolve, reject, response) {
    var validateStatus = response.config.validateStatus;
    if (!response.status || !validateStatus || validateStatus(response.status)) {
      resolve(response);
    } else {
      reject(createError(
        'Request failed with status code ' + response.status,
        response.config,
        null,
        response.request,
        response
      ));
    }
  };

  var cookies = (
    utils.isStandardBrowserEnv() ?

    // Standard browser envs support document.cookie
      (function standardBrowserEnv() {
        return {
          write: function write(name, value, expires, path, domain, secure) {
            var cookie = [];
            cookie.push(name + '=' + encodeURIComponent(value));

            if (utils.isNumber(expires)) {
              cookie.push('expires=' + new Date(expires).toGMTString());
            }

            if (utils.isString(path)) {
              cookie.push('path=' + path);
            }

            if (utils.isString(domain)) {
              cookie.push('domain=' + domain);
            }

            if (secure === true) {
              cookie.push('secure');
            }

            document.cookie = cookie.join('; ');
          },

          read: function read(name) {
            var match = document.cookie.match(new RegExp('(^|;\\s*)(' + name + ')=([^;]*)'));
            return (match ? decodeURIComponent(match[3]) : null);
          },

          remove: function remove(name) {
            this.write(name, '', Date.now() - 86400000);
          }
        };
      })() :

    // Non standard browser env (web workers, react-native) lack needed support.
      (function nonStandardBrowserEnv() {
        return {
          write: function write() {},
          read: function read() { return null; },
          remove: function remove() {}
        };
      })()
  );

  /**
   * Determines whether the specified URL is absolute
   *
   * @param {string} url The URL to test
   * @returns {boolean} True if the specified URL is absolute, otherwise false
   */
  var isAbsoluteURL = function isAbsoluteURL(url) {
    // A URL is considered absolute if it begins with "<scheme>://" or "//" (protocol-relative URL).
    // RFC 3986 defines scheme name as a sequence of characters beginning with a letter and followed
    // by any combination of letters, digits, plus, period, or hyphen.
    return /^([a-z][a-z\d\+\-\.]*:)?\/\//i.test(url);
  };

  /**
   * Creates a new URL by combining the specified URLs
   *
   * @param {string} baseURL The base URL
   * @param {string} relativeURL The relative URL
   * @returns {string} The combined URL
   */
  var combineURLs = function combineURLs(baseURL, relativeURL) {
    return relativeURL
      ? baseURL.replace(/\/+$/, '') + '/' + relativeURL.replace(/^\/+/, '')
      : baseURL;
  };

  /**
   * Creates a new URL by combining the baseURL with the requestedURL,
   * only when the requestedURL is not already an absolute URL.
   * If the requestURL is absolute, this function returns the requestedURL untouched.
   *
   * @param {string} baseURL The base URL
   * @param {string} requestedURL Absolute or relative URL to combine
   * @returns {string} The combined full path
   */
  var buildFullPath = function buildFullPath(baseURL, requestedURL) {
    if (baseURL && !isAbsoluteURL(requestedURL)) {
      return combineURLs(baseURL, requestedURL);
    }
    return requestedURL;
  };

  // Headers whose duplicates are ignored by node
  // c.f. https://nodejs.org/api/http.html#http_message_headers
  var ignoreDuplicateOf = [
    'age', 'authorization', 'content-length', 'content-type', 'etag',
    'expires', 'from', 'host', 'if-modified-since', 'if-unmodified-since',
    'last-modified', 'location', 'max-forwards', 'proxy-authorization',
    'referer', 'retry-after', 'user-agent'
  ];

  /**
   * Parse headers into an object
   *
   * ```
   * Date: Wed, 27 Aug 2014 08:58:49 GMT
   * Content-Type: application/json
   * Connection: keep-alive
   * Transfer-Encoding: chunked
   * ```
   *
   * @param {String} headers Headers needing to be parsed
   * @returns {Object} Headers parsed into an object
   */
  var parseHeaders = function parseHeaders(headers) {
    var parsed = {};
    var key;
    var val;
    var i;

    if (!headers) { return parsed; }

    utils.forEach(headers.split('\n'), function parser(line) {
      i = line.indexOf(':');
      key = utils.trim(line.substr(0, i)).toLowerCase();
      val = utils.trim(line.substr(i + 1));

      if (key) {
        if (parsed[key] && ignoreDuplicateOf.indexOf(key) >= 0) {
          return;
        }
        if (key === 'set-cookie') {
          parsed[key] = (parsed[key] ? parsed[key] : []).concat([val]);
        } else {
          parsed[key] = parsed[key] ? parsed[key] + ', ' + val : val;
        }
      }
    });

    return parsed;
  };

  var isURLSameOrigin = (
    utils.isStandardBrowserEnv() ?

    // Standard browser envs have full support of the APIs needed to test
    // whether the request URL is of the same origin as current location.
      (function standardBrowserEnv() {
        var msie = /(msie|trident)/i.test(navigator.userAgent);
        var urlParsingNode = document.createElement('a');
        var originURL;

        /**
      * Parse a URL to discover it's components
      *
      * @param {String} url The URL to be parsed
      * @returns {Object}
      */
        function resolveURL(url) {
          var href = url;

          if (msie) {
          // IE needs attribute set twice to normalize properties
            urlParsingNode.setAttribute('href', href);
            href = urlParsingNode.href;
          }

          urlParsingNode.setAttribute('href', href);

          // urlParsingNode provides the UrlUtils interface - http://url.spec.whatwg.org/#urlutils
          return {
            href: urlParsingNode.href,
            protocol: urlParsingNode.protocol ? urlParsingNode.protocol.replace(/:$/, '') : '',
            host: urlParsingNode.host,
            search: urlParsingNode.search ? urlParsingNode.search.replace(/^\?/, '') : '',
            hash: urlParsingNode.hash ? urlParsingNode.hash.replace(/^#/, '') : '',
            hostname: urlParsingNode.hostname,
            port: urlParsingNode.port,
            pathname: (urlParsingNode.pathname.charAt(0) === '/') ?
              urlParsingNode.pathname :
              '/' + urlParsingNode.pathname
          };
        }

        originURL = resolveURL(window.location.href);

        /**
      * Determine if a URL shares the same origin as the current location
      *
      * @param {String} requestURL The URL to test
      * @returns {boolean} True if URL shares the same origin, otherwise false
      */
        return function isURLSameOrigin(requestURL) {
          var parsed = (utils.isString(requestURL)) ? resolveURL(requestURL) : requestURL;
          return (parsed.protocol === originURL.protocol &&
              parsed.host === originURL.host);
        };
      })() :

    // Non standard browser envs (web workers, react-native) lack needed support.
      (function nonStandardBrowserEnv() {
        return function isURLSameOrigin() {
          return true;
        };
      })()
  );

  var xhr = function xhrAdapter(config) {
    return new Promise(function dispatchXhrRequest(resolve, reject) {
      var requestData = config.data;
      var requestHeaders = config.headers;
      var responseType = config.responseType;

      if (utils.isFormData(requestData)) {
        delete requestHeaders['Content-Type']; // Let the browser set it
      }

      var request = new XMLHttpRequest();

      // HTTP basic authentication
      if (config.auth) {
        var username = config.auth.username || '';
        var password = config.auth.password ? unescape(encodeURIComponent(config.auth.password)) : '';
        requestHeaders.Authorization = 'Basic ' + btoa(username + ':' + password);
      }

      var fullPath = buildFullPath(config.baseURL, config.url);
      request.open(config.method.toUpperCase(), buildURL(fullPath, config.params, config.paramsSerializer), true);

      // Set the request timeout in MS
      request.timeout = config.timeout;

      function onloadend() {
        if (!request) {
          return;
        }
        // Prepare the response
        var responseHeaders = 'getAllResponseHeaders' in request ? parseHeaders(request.getAllResponseHeaders()) : null;
        var responseData = !responseType || responseType === 'text' ||  responseType === 'json' ?
          request.responseText : request.response;
        var response = {
          data: responseData,
          status: request.status,
          statusText: request.statusText,
          headers: responseHeaders,
          config: config,
          request: request
        };

        settle(resolve, reject, response);

        // Clean up request
        request = null;
      }

      if ('onloadend' in request) {
        // Use onloadend if available
        request.onloadend = onloadend;
      } else {
        // Listen for ready state to emulate onloadend
        request.onreadystatechange = function handleLoad() {
          if (!request || request.readyState !== 4) {
            return;
          }

          // The request errored out and we didn't get a response, this will be
          // handled by onerror instead
          // With one exception: request that using file: protocol, most browsers
          // will return status as 0 even though it's a successful request
          if (request.status === 0 && !(request.responseURL && request.responseURL.indexOf('file:') === 0)) {
            return;
          }
          // readystate handler is calling before onerror or ontimeout handlers,
          // so we should call onloadend on the next 'tick'
          setTimeout(onloadend);
        };
      }

      // Handle browser request cancellation (as opposed to a manual cancellation)
      request.onabort = function handleAbort() {
        if (!request) {
          return;
        }

        reject(createError('Request aborted', config, 'ECONNABORTED', request));

        // Clean up request
        request = null;
      };

      // Handle low level network errors
      request.onerror = function handleError() {
        // Real errors are hidden from us by the browser
        // onerror should only fire if it's a network error
        reject(createError('Network Error', config, null, request));

        // Clean up request
        request = null;
      };

      // Handle timeout
      request.ontimeout = function handleTimeout() {
        var timeoutErrorMessage = 'timeout of ' + config.timeout + 'ms exceeded';
        if (config.timeoutErrorMessage) {
          timeoutErrorMessage = config.timeoutErrorMessage;
        }
        reject(createError(
          timeoutErrorMessage,
          config,
          config.transitional && config.transitional.clarifyTimeoutError ? 'ETIMEDOUT' : 'ECONNABORTED',
          request));

        // Clean up request
        request = null;
      };

      // Add xsrf header
      // This is only done if running in a standard browser environment.
      // Specifically not if we're in a web worker, or react-native.
      if (utils.isStandardBrowserEnv()) {
        // Add xsrf header
        var xsrfValue = (config.withCredentials || isURLSameOrigin(fullPath)) && config.xsrfCookieName ?
          cookies.read(config.xsrfCookieName) :
          undefined;

        if (xsrfValue) {
          requestHeaders[config.xsrfHeaderName] = xsrfValue;
        }
      }

      // Add headers to the request
      if ('setRequestHeader' in request) {
        utils.forEach(requestHeaders, function setRequestHeader(val, key) {
          if (typeof requestData === 'undefined' && key.toLowerCase() === 'content-type') {
            // Remove Content-Type if data is undefined
            delete requestHeaders[key];
          } else {
            // Otherwise add header to the request
            request.setRequestHeader(key, val);
          }
        });
      }

      // Add withCredentials to request if needed
      if (!utils.isUndefined(config.withCredentials)) {
        request.withCredentials = !!config.withCredentials;
      }

      // Add responseType to request if needed
      if (responseType && responseType !== 'json') {
        request.responseType = config.responseType;
      }

      // Handle progress if needed
      if (typeof config.onDownloadProgress === 'function') {
        request.addEventListener('progress', config.onDownloadProgress);
      }

      // Not all browsers support upload events
      if (typeof config.onUploadProgress === 'function' && request.upload) {
        request.upload.addEventListener('progress', config.onUploadProgress);
      }

      if (config.cancelToken) {
        // Handle cancellation
        config.cancelToken.promise.then(function onCanceled(cancel) {
          if (!request) {
            return;
          }

          request.abort();
          reject(cancel);
          // Clean up request
          request = null;
        });
      }

      if (!requestData) {
        requestData = null;
      }

      // Send the request
      request.send(requestData);
    });
  };

  var DEFAULT_CONTENT_TYPE = {
    'Content-Type': 'application/x-www-form-urlencoded'
  };

  function setContentTypeIfUnset(headers, value) {
    if (!utils.isUndefined(headers) && utils.isUndefined(headers['Content-Type'])) {
      headers['Content-Type'] = value;
    }
  }

  function getDefaultAdapter() {
    var adapter;
    if (typeof XMLHttpRequest !== 'undefined') {
      // For browsers use XHR adapter
      adapter = xhr;
    } else if (typeof process !== 'undefined' && Object.prototype.toString.call(process) === '[object process]') {
      // For node use HTTP adapter
      adapter = xhr;
    }
    return adapter;
  }

  function stringifySafely(rawValue, parser, encoder) {
    if (utils.isString(rawValue)) {
      try {
        (parser || JSON.parse)(rawValue);
        return utils.trim(rawValue);
      } catch (e) {
        if (e.name !== 'SyntaxError') {
          throw e;
        }
      }
    }

    return (encoder || JSON.stringify)(rawValue);
  }

  var defaults = {

    transitional: {
      silentJSONParsing: true,
      forcedJSONParsing: true,
      clarifyTimeoutError: false
    },

    adapter: getDefaultAdapter(),

    transformRequest: [function transformRequest(data, headers) {
      normalizeHeaderName(headers, 'Accept');
      normalizeHeaderName(headers, 'Content-Type');

      if (utils.isFormData(data) ||
        utils.isArrayBuffer(data) ||
        utils.isBuffer(data) ||
        utils.isStream(data) ||
        utils.isFile(data) ||
        utils.isBlob(data)
      ) {
        return data;
      }
      if (utils.isArrayBufferView(data)) {
        return data.buffer;
      }
      if (utils.isURLSearchParams(data)) {
        setContentTypeIfUnset(headers, 'application/x-www-form-urlencoded;charset=utf-8');
        return data.toString();
      }
      if (utils.isObject(data) || (headers && headers['Content-Type'] === 'application/json')) {
        setContentTypeIfUnset(headers, 'application/json');
        return stringifySafely(data);
      }
      return data;
    }],

    transformResponse: [function transformResponse(data) {
      var transitional = this.transitional;
      var silentJSONParsing = transitional && transitional.silentJSONParsing;
      var forcedJSONParsing = transitional && transitional.forcedJSONParsing;
      var strictJSONParsing = !silentJSONParsing && this.responseType === 'json';

      if (strictJSONParsing || (forcedJSONParsing && utils.isString(data) && data.length)) {
        try {
          return JSON.parse(data);
        } catch (e) {
          if (strictJSONParsing) {
            if (e.name === 'SyntaxError') {
              throw enhanceError(e, this, 'E_JSON_PARSE');
            }
            throw e;
          }
        }
      }

      return data;
    }],

    /**
     * A timeout in milliseconds to abort a request. If set to 0 (default) a
     * timeout is not created.
     */
    timeout: 0,

    xsrfCookieName: 'XSRF-TOKEN',
    xsrfHeaderName: 'X-XSRF-TOKEN',

    maxContentLength: -1,
    maxBodyLength: -1,

    validateStatus: function validateStatus(status) {
      return status >= 200 && status < 300;
    }
  };

  defaults.headers = {
    common: {
      'Accept': 'application/json, text/plain, */*'
    }
  };

  utils.forEach(['delete', 'get', 'head'], function forEachMethodNoData(method) {
    defaults.headers[method] = {};
  });

  utils.forEach(['post', 'put', 'patch'], function forEachMethodWithData(method) {
    defaults.headers[method] = utils.merge(DEFAULT_CONTENT_TYPE);
  });

  var defaults_1 = defaults;

  /**
   * Transform the data for a request or a response
   *
   * @param {Object|String} data The data to be transformed
   * @param {Array} headers The headers for the request or response
   * @param {Array|Function} fns A single function or Array of functions
   * @returns {*} The resulting transformed data
   */
  var transformData = function transformData(data, headers, fns) {
    var context = this || defaults_1;
    /*eslint no-param-reassign:0*/
    utils.forEach(fns, function transform(fn) {
      data = fn.call(context, data, headers);
    });

    return data;
  };

  var isCancel = function isCancel(value) {
    return !!(value && value.__CANCEL__);
  };

  /**
   * Throws a `Cancel` if cancellation has been requested.
   */
  function throwIfCancellationRequested(config) {
    if (config.cancelToken) {
      config.cancelToken.throwIfRequested();
    }
  }

  /**
   * Dispatch a request to the server using the configured adapter.
   *
   * @param {object} config The config that is to be used for the request
   * @returns {Promise} The Promise to be fulfilled
   */
  var dispatchRequest = function dispatchRequest(config) {
    throwIfCancellationRequested(config);

    // Ensure headers exist
    config.headers = config.headers || {};

    // Transform request data
    config.data = transformData.call(
      config,
      config.data,
      config.headers,
      config.transformRequest
    );

    // Flatten headers
    config.headers = utils.merge(
      config.headers.common || {},
      config.headers[config.method] || {},
      config.headers
    );

    utils.forEach(
      ['delete', 'get', 'head', 'post', 'put', 'patch', 'common'],
      function cleanHeaderConfig(method) {
        delete config.headers[method];
      }
    );

    var adapter = config.adapter || defaults_1.adapter;

    return adapter(config).then(function onAdapterResolution(response) {
      throwIfCancellationRequested(config);

      // Transform response data
      response.data = transformData.call(
        config,
        response.data,
        response.headers,
        config.transformResponse
      );

      return response;
    }, function onAdapterRejection(reason) {
      if (!isCancel(reason)) {
        throwIfCancellationRequested(config);

        // Transform response data
        if (reason && reason.response) {
          reason.response.data = transformData.call(
            config,
            reason.response.data,
            reason.response.headers,
            config.transformResponse
          );
        }
      }

      return Promise.reject(reason);
    });
  };

  /**
   * Config-specific merge-function which creates a new config-object
   * by merging two configuration objects together.
   *
   * @param {Object} config1
   * @param {Object} config2
   * @returns {Object} New object resulting from merging config2 to config1
   */
  var mergeConfig = function mergeConfig(config1, config2) {
    // eslint-disable-next-line no-param-reassign
    config2 = config2 || {};
    var config = {};

    var valueFromConfig2Keys = ['url', 'method', 'data'];
    var mergeDeepPropertiesKeys = ['headers', 'auth', 'proxy', 'params'];
    var defaultToConfig2Keys = [
      'baseURL', 'transformRequest', 'transformResponse', 'paramsSerializer',
      'timeout', 'timeoutMessage', 'withCredentials', 'adapter', 'responseType', 'xsrfCookieName',
      'xsrfHeaderName', 'onUploadProgress', 'onDownloadProgress', 'decompress',
      'maxContentLength', 'maxBodyLength', 'maxRedirects', 'transport', 'httpAgent',
      'httpsAgent', 'cancelToken', 'socketPath', 'responseEncoding'
    ];
    var directMergeKeys = ['validateStatus'];

    function getMergedValue(target, source) {
      if (utils.isPlainObject(target) && utils.isPlainObject(source)) {
        return utils.merge(target, source);
      } else if (utils.isPlainObject(source)) {
        return utils.merge({}, source);
      } else if (utils.isArray(source)) {
        return source.slice();
      }
      return source;
    }

    function mergeDeepProperties(prop) {
      if (!utils.isUndefined(config2[prop])) {
        config[prop] = getMergedValue(config1[prop], config2[prop]);
      } else if (!utils.isUndefined(config1[prop])) {
        config[prop] = getMergedValue(undefined, config1[prop]);
      }
    }

    utils.forEach(valueFromConfig2Keys, function valueFromConfig2(prop) {
      if (!utils.isUndefined(config2[prop])) {
        config[prop] = getMergedValue(undefined, config2[prop]);
      }
    });

    utils.forEach(mergeDeepPropertiesKeys, mergeDeepProperties);

    utils.forEach(defaultToConfig2Keys, function defaultToConfig2(prop) {
      if (!utils.isUndefined(config2[prop])) {
        config[prop] = getMergedValue(undefined, config2[prop]);
      } else if (!utils.isUndefined(config1[prop])) {
        config[prop] = getMergedValue(undefined, config1[prop]);
      }
    });

    utils.forEach(directMergeKeys, function merge(prop) {
      if (prop in config2) {
        config[prop] = getMergedValue(config1[prop], config2[prop]);
      } else if (prop in config1) {
        config[prop] = getMergedValue(undefined, config1[prop]);
      }
    });

    var axiosKeys = valueFromConfig2Keys
      .concat(mergeDeepPropertiesKeys)
      .concat(defaultToConfig2Keys)
      .concat(directMergeKeys);

    var otherKeys = Object
      .keys(config1)
      .concat(Object.keys(config2))
      .filter(function filterAxiosKeys(key) {
        return axiosKeys.indexOf(key) === -1;
      });

    utils.forEach(otherKeys, mergeDeepProperties);

    return config;
  };

  var name = "axios";
  var version = "0.21.4";
  var description = "Promise based HTTP client for the browser and node.js";
  var main = "index.js";
  var scripts = {
  	test: "grunt test",
  	start: "node ./sandbox/server.js",
  	build: "NODE_ENV=production grunt build",
  	preversion: "npm test",
  	version: "npm run build && grunt version && git add -A dist && git add CHANGELOG.md bower.json package.json",
  	postversion: "git push && git push --tags",
  	examples: "node ./examples/server.js",
  	coveralls: "cat coverage/lcov.info | ./node_modules/coveralls/bin/coveralls.js",
  	fix: "eslint --fix lib/**/*.js"
  };
  var repository = {
  	type: "git",
  	url: "https://github.com/axios/axios.git"
  };
  var keywords = [
  	"xhr",
  	"http",
  	"ajax",
  	"promise",
  	"node"
  ];
  var author = "Matt Zabriskie";
  var license = "MIT";
  var bugs = {
  	url: "https://github.com/axios/axios/issues"
  };
  var homepage = "https://axios-http.com";
  var devDependencies = {
  	coveralls: "^3.0.0",
  	"es6-promise": "^4.2.4",
  	grunt: "^1.3.0",
  	"grunt-banner": "^0.6.0",
  	"grunt-cli": "^1.2.0",
  	"grunt-contrib-clean": "^1.1.0",
  	"grunt-contrib-watch": "^1.0.0",
  	"grunt-eslint": "^23.0.0",
  	"grunt-karma": "^4.0.0",
  	"grunt-mocha-test": "^0.13.3",
  	"grunt-ts": "^6.0.0-beta.19",
  	"grunt-webpack": "^4.0.2",
  	"istanbul-instrumenter-loader": "^1.0.0",
  	"jasmine-core": "^2.4.1",
  	karma: "^6.3.2",
  	"karma-chrome-launcher": "^3.1.0",
  	"karma-firefox-launcher": "^2.1.0",
  	"karma-jasmine": "^1.1.1",
  	"karma-jasmine-ajax": "^0.1.13",
  	"karma-safari-launcher": "^1.0.0",
  	"karma-sauce-launcher": "^4.3.6",
  	"karma-sinon": "^1.0.5",
  	"karma-sourcemap-loader": "^0.3.8",
  	"karma-webpack": "^4.0.2",
  	"load-grunt-tasks": "^3.5.2",
  	minimist: "^1.2.0",
  	mocha: "^8.2.1",
  	sinon: "^4.5.0",
  	"terser-webpack-plugin": "^4.2.3",
  	typescript: "^4.0.5",
  	"url-search-params": "^0.10.0",
  	webpack: "^4.44.2",
  	"webpack-dev-server": "^3.11.0"
  };
  var browser = {
  	"./lib/adapters/http.js": "./lib/adapters/xhr.js"
  };
  var jsdelivr = "dist/axios.min.js";
  var unpkg = "dist/axios.min.js";
  var typings = "./index.d.ts";
  var dependencies = {
  	"follow-redirects": "^1.14.0"
  };
  var bundlesize = [
  	{
  		path: "./dist/axios.min.js",
  		threshold: "5kB"
  	}
  ];
  var _package = {
  	name: name,
  	version: version,
  	description: description,
  	main: main,
  	scripts: scripts,
  	repository: repository,
  	keywords: keywords,
  	author: author,
  	license: license,
  	bugs: bugs,
  	homepage: homepage,
  	devDependencies: devDependencies,
  	browser: browser,
  	jsdelivr: jsdelivr,
  	unpkg: unpkg,
  	typings: typings,
  	dependencies: dependencies,
  	bundlesize: bundlesize
  };

  var _package$1 = {
    __proto__: null,
    name: name,
    version: version,
    description: description,
    main: main,
    scripts: scripts,
    repository: repository,
    keywords: keywords,
    author: author,
    license: license,
    bugs: bugs,
    homepage: homepage,
    devDependencies: devDependencies,
    browser: browser,
    jsdelivr: jsdelivr,
    unpkg: unpkg,
    typings: typings,
    dependencies: dependencies,
    bundlesize: bundlesize,
    'default': _package
  };

  var commonjsGlobal = typeof globalThis !== 'undefined' ? globalThis : typeof window !== 'undefined' ? window : typeof global !== 'undefined' ? global : typeof self !== 'undefined' ? self : {};

  function createCommonjsModule(fn, module) {
  	return module = { exports: {} }, fn(module, module.exports), module.exports;
  }

  function getCjsExportFromNamespace (n) {
  	return n && n['default'] || n;
  }

  function commonjsRequire () {
  	throw new Error('Dynamic requires are not currently supported by @rollup/plugin-commonjs');
  }

  var pkg = getCjsExportFromNamespace(_package$1);

  var validators = {};

  // eslint-disable-next-line func-names
  ['object', 'boolean', 'number', 'function', 'string', 'symbol'].forEach(function(type, i) {
    validators[type] = function validator(thing) {
      return typeof thing === type || 'a' + (i < 1 ? 'n ' : ' ') + type;
    };
  });

  var deprecatedWarnings = {};
  var currentVerArr = pkg.version.split('.');

  /**
   * Compare package versions
   * @param {string} version
   * @param {string?} thanVersion
   * @returns {boolean}
   */
  function isOlderVersion(version, thanVersion) {
    var pkgVersionArr = thanVersion ? thanVersion.split('.') : currentVerArr;
    var destVer = version.split('.');
    for (var i = 0; i < 3; i++) {
      if (pkgVersionArr[i] > destVer[i]) {
        return true;
      } else if (pkgVersionArr[i] < destVer[i]) {
        return false;
      }
    }
    return false;
  }

  /**
   * Transitional option validator
   * @param {function|boolean?} validator
   * @param {string?} version
   * @param {string} message
   * @returns {function}
   */
  validators.transitional = function transitional(validator, version, message) {
    var isDeprecated = version && isOlderVersion(version);

    function formatMessage(opt, desc) {
      return '[Axios v' + pkg.version + '] Transitional option \'' + opt + '\'' + desc + (message ? '. ' + message : '');
    }

    // eslint-disable-next-line func-names
    return function(value, opt, opts) {
      if (validator === false) {
        throw new Error(formatMessage(opt, ' has been removed in ' + version));
      }

      if (isDeprecated && !deprecatedWarnings[opt]) {
        deprecatedWarnings[opt] = true;
        // eslint-disable-next-line no-console
        console.warn(
          formatMessage(
            opt,
            ' has been deprecated since v' + version + ' and will be removed in the near future'
          )
        );
      }

      return validator ? validator(value, opt, opts) : true;
    };
  };

  /**
   * Assert object's properties type
   * @param {object} options
   * @param {object} schema
   * @param {boolean?} allowUnknown
   */

  function assertOptions(options, schema, allowUnknown) {
    if (typeof options !== 'object') {
      throw new TypeError('options must be an object');
    }
    var keys = Object.keys(options);
    var i = keys.length;
    while (i-- > 0) {
      var opt = keys[i];
      var validator = schema[opt];
      if (validator) {
        var value = options[opt];
        var result = value === undefined || validator(value, opt, options);
        if (result !== true) {
          throw new TypeError('option ' + opt + ' must be ' + result);
        }
        continue;
      }
      if (allowUnknown !== true) {
        throw Error('Unknown option ' + opt);
      }
    }
  }

  var validator = {
    isOlderVersion: isOlderVersion,
    assertOptions: assertOptions,
    validators: validators
  };

  var validators$1 = validator.validators;
  /**
   * Create a new instance of Axios
   *
   * @param {Object} instanceConfig The default config for the instance
   */
  function Axios(instanceConfig) {
    this.defaults = instanceConfig;
    this.interceptors = {
      request: new InterceptorManager_1(),
      response: new InterceptorManager_1()
    };
  }

  /**
   * Dispatch a request
   *
   * @param {Object} config The config specific for this request (merged with this.defaults)
   */
  Axios.prototype.request = function request(config) {
    /*eslint no-param-reassign:0*/
    // Allow for axios('example/url'[, config]) a la fetch API
    if (typeof config === 'string') {
      config = arguments[1] || {};
      config.url = arguments[0];
    } else {
      config = config || {};
    }

    config = mergeConfig(this.defaults, config);

    // Set config.method
    if (config.method) {
      config.method = config.method.toLowerCase();
    } else if (this.defaults.method) {
      config.method = this.defaults.method.toLowerCase();
    } else {
      config.method = 'get';
    }

    var transitional = config.transitional;

    if (transitional !== undefined) {
      validator.assertOptions(transitional, {
        silentJSONParsing: validators$1.transitional(validators$1.boolean, '1.0.0'),
        forcedJSONParsing: validators$1.transitional(validators$1.boolean, '1.0.0'),
        clarifyTimeoutError: validators$1.transitional(validators$1.boolean, '1.0.0')
      }, false);
    }

    // filter out skipped interceptors
    var requestInterceptorChain = [];
    var synchronousRequestInterceptors = true;
    this.interceptors.request.forEach(function unshiftRequestInterceptors(interceptor) {
      if (typeof interceptor.runWhen === 'function' && interceptor.runWhen(config) === false) {
        return;
      }

      synchronousRequestInterceptors = synchronousRequestInterceptors && interceptor.synchronous;

      requestInterceptorChain.unshift(interceptor.fulfilled, interceptor.rejected);
    });

    var responseInterceptorChain = [];
    this.interceptors.response.forEach(function pushResponseInterceptors(interceptor) {
      responseInterceptorChain.push(interceptor.fulfilled, interceptor.rejected);
    });

    var promise;

    if (!synchronousRequestInterceptors) {
      var chain = [dispatchRequest, undefined];

      Array.prototype.unshift.apply(chain, requestInterceptorChain);
      chain = chain.concat(responseInterceptorChain);

      promise = Promise.resolve(config);
      while (chain.length) {
        promise = promise.then(chain.shift(), chain.shift());
      }

      return promise;
    }


    var newConfig = config;
    while (requestInterceptorChain.length) {
      var onFulfilled = requestInterceptorChain.shift();
      var onRejected = requestInterceptorChain.shift();
      try {
        newConfig = onFulfilled(newConfig);
      } catch (error) {
        onRejected(error);
        break;
      }
    }

    try {
      promise = dispatchRequest(newConfig);
    } catch (error) {
      return Promise.reject(error);
    }

    while (responseInterceptorChain.length) {
      promise = promise.then(responseInterceptorChain.shift(), responseInterceptorChain.shift());
    }

    return promise;
  };

  Axios.prototype.getUri = function getUri(config) {
    config = mergeConfig(this.defaults, config);
    return buildURL(config.url, config.params, config.paramsSerializer).replace(/^\?/, '');
  };

  // Provide aliases for supported request methods
  utils.forEach(['delete', 'get', 'head', 'options'], function forEachMethodNoData(method) {
    /*eslint func-names:0*/
    Axios.prototype[method] = function(url, config) {
      return this.request(mergeConfig(config || {}, {
        method: method,
        url: url,
        data: (config || {}).data
      }));
    };
  });

  utils.forEach(['post', 'put', 'patch'], function forEachMethodWithData(method) {
    /*eslint func-names:0*/
    Axios.prototype[method] = function(url, data, config) {
      return this.request(mergeConfig(config || {}, {
        method: method,
        url: url,
        data: data
      }));
    };
  });

  var Axios_1 = Axios;

  /**
   * A `Cancel` is an object that is thrown when an operation is canceled.
   *
   * @class
   * @param {string=} message The message.
   */
  function Cancel(message) {
    this.message = message;
  }

  Cancel.prototype.toString = function toString() {
    return 'Cancel' + (this.message ? ': ' + this.message : '');
  };

  Cancel.prototype.__CANCEL__ = true;

  var Cancel_1 = Cancel;

  /**
   * A `CancelToken` is an object that can be used to request cancellation of an operation.
   *
   * @class
   * @param {Function} executor The executor function.
   */
  function CancelToken(executor) {
    if (typeof executor !== 'function') {
      throw new TypeError('executor must be a function.');
    }

    var resolvePromise;
    this.promise = new Promise(function promiseExecutor(resolve) {
      resolvePromise = resolve;
    });

    var token = this;
    executor(function cancel(message) {
      if (token.reason) {
        // Cancellation has already been requested
        return;
      }

      token.reason = new Cancel_1(message);
      resolvePromise(token.reason);
    });
  }

  /**
   * Throws a `Cancel` if cancellation has been requested.
   */
  CancelToken.prototype.throwIfRequested = function throwIfRequested() {
    if (this.reason) {
      throw this.reason;
    }
  };

  /**
   * Returns an object that contains a new `CancelToken` and a function that, when called,
   * cancels the `CancelToken`.
   */
  CancelToken.source = function source() {
    var cancel;
    var token = new CancelToken(function executor(c) {
      cancel = c;
    });
    return {
      token: token,
      cancel: cancel
    };
  };

  var CancelToken_1 = CancelToken;

  /**
   * Syntactic sugar for invoking a function and expanding an array for arguments.
   *
   * Common use case would be to use `Function.prototype.apply`.
   *
   *  ```js
   *  function f(x, y, z) {}
   *  var args = [1, 2, 3];
   *  f.apply(null, args);
   *  ```
   *
   * With `spread` this example can be re-written.
   *
   *  ```js
   *  spread(function(x, y, z) {})([1, 2, 3]);
   *  ```
   *
   * @param {Function} callback
   * @returns {Function}
   */
  var spread = function spread(callback) {
    return function wrap(arr) {
      return callback.apply(null, arr);
    };
  };

  /**
   * Determines whether the payload is an error thrown by Axios
   *
   * @param {*} payload The value to test
   * @returns {boolean} True if the payload is an error thrown by Axios, otherwise false
   */
  var isAxiosError = function isAxiosError(payload) {
    return (typeof payload === 'object') && (payload.isAxiosError === true);
  };

  /**
   * Create an instance of Axios
   *
   * @param {Object} defaultConfig The default config for the instance
   * @return {Axios} A new instance of Axios
   */
  function createInstance(defaultConfig) {
    var context = new Axios_1(defaultConfig);
    var instance = bind(Axios_1.prototype.request, context);

    // Copy axios.prototype to instance
    utils.extend(instance, Axios_1.prototype, context);

    // Copy context to instance
    utils.extend(instance, context);

    return instance;
  }

  // Create the default instance to be exported
  var axios = createInstance(defaults_1);

  // Expose Axios class to allow class inheritance
  axios.Axios = Axios_1;

  // Factory for creating new instances
  axios.create = function create(instanceConfig) {
    return createInstance(mergeConfig(axios.defaults, instanceConfig));
  };

  // Expose Cancel & CancelToken
  axios.Cancel = Cancel_1;
  axios.CancelToken = CancelToken_1;
  axios.isCancel = isCancel;

  // Expose all/spread
  axios.all = function all(promises) {
    return Promise.all(promises);
  };
  axios.spread = spread;

  // Expose isAxiosError
  axios.isAxiosError = isAxiosError;

  var axios_1 = axios;

  // Allow use of default import syntax in TypeScript
  var _default = axios;
  axios_1.default = _default;

  var axios$1 = axios_1;

  class TosServerError extends Error {
    /**
     * is original from backend, equals `data.Code`
     */

    /**
     * the body when backend errors
     */

    /**
     * status code
     */

    /**
     * response headers
     */

    /**
     * identifies the errored request, equals to headers['x-tos-request-id'].
     * If you has any question about the request, please send the requestId and id2 to TOS worker.
     */

    /**
     * identifies the errored request, equals to headers['x-tos-id-2'].
     * If you has any question about the request, please send the requestId and id2 to TOS worker.
     */
    constructor(response) {
      const {
        data
      } = response;
      super(data.Message); // https://www.dannyguo.com/blog/how-to-fix-instanceof-not-working-for-custom-errors-in-typescript/

      this.code = void 0;
      this.data = void 0;
      this.statusCode = void 0;
      this.headers = void 0;
      this.requestId = void 0;
      this.id2 = void 0;
      Object.setPrototypeOf(this, TosServerError.prototype);
      this.data = data;
      this.code = data.Code;
      this.statusCode = response.status;
      this.headers = response.headers;
      this.requestId = response.headers['x-tos-request-id'];
      this.id2 = response.headers['x-tos-id-2'];
    }

  }

  (function (TosServerCode) {
    TosServerCode["NoSuchBucket"] = "NoSuchBucket";
    TosServerCode["NoSuchKey"] = "NoSuchKey";
    TosServerCode["AccessDenied"] = "AccessDenied";
    TosServerCode["MalformedAcl"] = "MalformedAclError";
    TosServerCode["UnexpectedContent"] = "UnexpectedContent";
    TosServerCode["InvalidRequest"] = "InvalidRequest";
    TosServerCode["MissingSecurityHeader"] = "MissingSecurityHeader";
    TosServerCode["InvalidArgument"] = "InvalidArgument";
    TosServerCode["EntityTooSmall"] = "EntityTooSmall";
    TosServerCode["InvalidBucketName"] = "InvalidBucketName";
    TosServerCode["BucketNotEmpty"] = "BucketNotEmpty";
    TosServerCode["TooManyBuckets"] = "TooManyBuckets";
    TosServerCode["BucketAlreadyExists"] = "BucketAlreadyExists";
    TosServerCode["MalformedBody"] = "MalformedBody";
    TosServerCode["NoSuchLifecycleConfiguration"] = "NoSuchLifecycleConfiguration";
    TosServerCode["ReplicationConfigurationNotFound"] = "ReplicationConfigurationNotFoundError";
    TosServerCode["InvalidLocationConstraint"] = "InvalidLocationConstraint";
    TosServerCode["AuthorizationQueryParametersError"] = "AuthorizationQueryParametersError";
    TosServerCode["RequestTimeTooSkewed"] = "RequestTimeTooSkewed";
    TosServerCode["SignatureDoesNotMatch"] = "SignatureDoesNotMatch";
    TosServerCode["RequestedRangeNotSatisfiable"] = "Requested Range Not Satisfiable";
    TosServerCode["PreconditionFailed"] = "PreconditionFailed";
    TosServerCode["BadDigest"] = "BadDigest";
    TosServerCode["InvalidDigest"] = "InvalidDigest";
    TosServerCode["EntityTooLarge"] = "EntityTooLarge";
    TosServerCode["UnImplemented"] = "UnImplemented";
    TosServerCode["MethodNotAllowed"] = "MethodNotAllowed";
    TosServerCode["InvalidAccessKeyId"] = "InvalidAccessKeyId";
    TosServerCode["InvalidSecurityToken"] = "InvalidSecurityToken";
    TosServerCode["ContentSHA256Mismatch"] = "ContentSHA256Mismatch";
    TosServerCode["ExceedQPSLimit"] = "ExceedQPSLimit";
    TosServerCode["ExceedRateLimit"] = "ExceedRateLimit";
    TosServerCode["NoSuchCORSConfiguration"] = "NoSuchCORSConfiguration";
    TosServerCode["NoSuchMirrorConfiguration"] = "NoSuchMirrorConfiguration";
    TosServerCode["NoSuchWebsiteConfiguration"] = "NoSuchWebsiteConfiguration";
    TosServerCode["MissingRequestBody"] = "MissingRequestBodyError";
    TosServerCode["BucketAlreadyOwnedByYou"] = "BucketAlreadyOwnedByYou";
    TosServerCode["NoSuchBucketPolicy"] = "NoSuchBucketPolicy";
    TosServerCode["PolicyTooLarge"] = "PolicyTooLarge";
    TosServerCode["MalformedPolicy"] = "MalformedPolicy";
    TosServerCode["InvalidKey"] = "InvalidKey";
    TosServerCode["MirrorFailed"] = "MirrorFailed";
    TosServerCode["Timeout"] = "Timeout";
    TosServerCode["OffsetNotMatched"] = "OffsetNotMatched";
    TosServerCode["NotAppendable"] = "NotAppendable";
    TosServerCode["ContextCanceled"] = "ContextCanceled";
    TosServerCode["InternalError"] = "InternalError";
    TosServerCode["TooManyRequests"] = "TooManyRequests";
    TosServerCode["TimeOut"] = "TimeOut";
    TosServerCode["ConcurrencyUpdateObjectLimit"] = "ConcurrencyUpdateObjectLimit";
    TosServerCode["DuplicateUpload"] = "DuplicateUpload";
    TosServerCode["DuplicateObject"] = "DuplicateObject";
    TosServerCode["InvalidVersionId"] = "InvalidVersionId";
    TosServerCode["StorageClassNotMatch"] = "StorageClassNotMatch";
    TosServerCode["UploadStatusNotUploading"] = "UploadStatusNotUploading";
    TosServerCode["PartSizeNotMatch"] = "PartSizeNotMatch";
    TosServerCode["NoUploadPart"] = "NoUploadPart";
    TosServerCode["PartsLenInvalid"] = "PartsLenInvalid";
    TosServerCode["PartsIdxSmall"] = "PartsIdxSmall";
    TosServerCode["PartSizeSmall"] = "PartSizeSmall";
    TosServerCode["PrefixNotNextKeyPrefix"] = "PrefixNotNextKeyPrefix";
    TosServerCode["InvalidPart"] = "InvalidPart";
    TosServerCode["InvalidPartOffset"] = "InvalidPartOffset";
    TosServerCode["MismatchObject"] = "MismatchObject";
    TosServerCode["UploadStatusMismatch"] = "UploadStatusMismatch";
    TosServerCode["CompletingStatusNoExpiration"] = "CompletingStatusNoExpiration";
    TosServerCode["Found"] = "Found";
    TosServerCode["InvalidRedirectLocation"] = "InvalidRedirectLocation";
  })(exports.TosServerCode || (exports.TosServerCode = {}));

  class TosClientError extends Error {
    constructor(message) {
      super(message); // https://www.dannyguo.com/blog/how-to-fix-instanceof-not-working-for-custom-errors-in-typescript/

      Object.setPrototypeOf(this, TosClientError.prototype);
    }

  }

  /**
   * Checks if `value` is classified as an `Array` object.
   *
   * @static
   * @memberOf _
   * @since 0.1.0
   * @category Lang
   * @param {*} value The value to check.
   * @returns {boolean} Returns `true` if `value` is an array, else `false`.
   * @example
   *
   * _.isArray([1, 2, 3]);
   * // => true
   *
   * _.isArray(document.body.children);
   * // => false
   *
   * _.isArray('abc');
   * // => false
   *
   * _.isArray(_.noop);
   * // => false
   */
  var isArray$1 = Array.isArray;

  var isArray_1 = isArray$1;

  /** Detect free variable `global` from Node.js. */
  var freeGlobal = typeof commonjsGlobal == 'object' && commonjsGlobal && commonjsGlobal.Object === Object && commonjsGlobal;

  var _freeGlobal = freeGlobal;

  /** Detect free variable `self`. */
  var freeSelf = typeof self == 'object' && self && self.Object === Object && self;

  /** Used as a reference to the global object. */
  var root = _freeGlobal || freeSelf || Function('return this')();

  var _root = root;

  /** Built-in value references. */
  var Symbol$1 = _root.Symbol;

  var _Symbol = Symbol$1;

  /** Used for built-in method references. */
  var objectProto = Object.prototype;

  /** Used to check objects for own properties. */
  var hasOwnProperty = objectProto.hasOwnProperty;

  /**
   * Used to resolve the
   * [`toStringTag`](http://ecma-international.org/ecma-262/7.0/#sec-object.prototype.tostring)
   * of values.
   */
  var nativeObjectToString = objectProto.toString;

  /** Built-in value references. */
  var symToStringTag = _Symbol ? _Symbol.toStringTag : undefined;

  /**
   * A specialized version of `baseGetTag` which ignores `Symbol.toStringTag` values.
   *
   * @private
   * @param {*} value The value to query.
   * @returns {string} Returns the raw `toStringTag`.
   */
  function getRawTag(value) {
    var isOwn = hasOwnProperty.call(value, symToStringTag),
        tag = value[symToStringTag];

    try {
      value[symToStringTag] = undefined;
      var unmasked = true;
    } catch (e) {}

    var result = nativeObjectToString.call(value);
    if (unmasked) {
      if (isOwn) {
        value[symToStringTag] = tag;
      } else {
        delete value[symToStringTag];
      }
    }
    return result;
  }

  var _getRawTag = getRawTag;

  /** Used for built-in method references. */
  var objectProto$1 = Object.prototype;

  /**
   * Used to resolve the
   * [`toStringTag`](http://ecma-international.org/ecma-262/7.0/#sec-object.prototype.tostring)
   * of values.
   */
  var nativeObjectToString$1 = objectProto$1.toString;

  /**
   * Converts `value` to a string using `Object.prototype.toString`.
   *
   * @private
   * @param {*} value The value to convert.
   * @returns {string} Returns the converted string.
   */
  function objectToString(value) {
    return nativeObjectToString$1.call(value);
  }

  var _objectToString = objectToString;

  /** `Object#toString` result references. */
  var nullTag = '[object Null]',
      undefinedTag = '[object Undefined]';

  /** Built-in value references. */
  var symToStringTag$1 = _Symbol ? _Symbol.toStringTag : undefined;

  /**
   * The base implementation of `getTag` without fallbacks for buggy environments.
   *
   * @private
   * @param {*} value The value to query.
   * @returns {string} Returns the `toStringTag`.
   */
  function baseGetTag(value) {
    if (value == null) {
      return value === undefined ? undefinedTag : nullTag;
    }
    return (symToStringTag$1 && symToStringTag$1 in Object(value))
      ? _getRawTag(value)
      : _objectToString(value);
  }

  var _baseGetTag = baseGetTag;

  /**
   * Checks if `value` is object-like. A value is object-like if it's not `null`
   * and has a `typeof` result of "object".
   *
   * @static
   * @memberOf _
   * @since 4.0.0
   * @category Lang
   * @param {*} value The value to check.
   * @returns {boolean} Returns `true` if `value` is object-like, else `false`.
   * @example
   *
   * _.isObjectLike({});
   * // => true
   *
   * _.isObjectLike([1, 2, 3]);
   * // => true
   *
   * _.isObjectLike(_.noop);
   * // => false
   *
   * _.isObjectLike(null);
   * // => false
   */
  function isObjectLike(value) {
    return value != null && typeof value == 'object';
  }

  var isObjectLike_1 = isObjectLike;

  /** `Object#toString` result references. */
  var symbolTag = '[object Symbol]';

  /**
   * Checks if `value` is classified as a `Symbol` primitive or object.
   *
   * @static
   * @memberOf _
   * @since 4.0.0
   * @category Lang
   * @param {*} value The value to check.
   * @returns {boolean} Returns `true` if `value` is a symbol, else `false`.
   * @example
   *
   * _.isSymbol(Symbol.iterator);
   * // => true
   *
   * _.isSymbol('abc');
   * // => false
   */
  function isSymbol(value) {
    return typeof value == 'symbol' ||
      (isObjectLike_1(value) && _baseGetTag(value) == symbolTag);
  }

  var isSymbol_1 = isSymbol;

  /** Used to match property names within property paths. */
  var reIsDeepProp = /\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,
      reIsPlainProp = /^\w*$/;

  /**
   * Checks if `value` is a property name and not a property path.
   *
   * @private
   * @param {*} value The value to check.
   * @param {Object} [object] The object to query keys on.
   * @returns {boolean} Returns `true` if `value` is a property name, else `false`.
   */
  function isKey(value, object) {
    if (isArray_1(value)) {
      return false;
    }
    var type = typeof value;
    if (type == 'number' || type == 'symbol' || type == 'boolean' ||
        value == null || isSymbol_1(value)) {
      return true;
    }
    return reIsPlainProp.test(value) || !reIsDeepProp.test(value) ||
      (object != null && value in Object(object));
  }

  var _isKey = isKey;

  /**
   * Checks if `value` is the
   * [language type](http://www.ecma-international.org/ecma-262/7.0/#sec-ecmascript-language-types)
   * of `Object`. (e.g. arrays, functions, objects, regexes, `new Number(0)`, and `new String('')`)
   *
   * @static
   * @memberOf _
   * @since 0.1.0
   * @category Lang
   * @param {*} value The value to check.
   * @returns {boolean} Returns `true` if `value` is an object, else `false`.
   * @example
   *
   * _.isObject({});
   * // => true
   *
   * _.isObject([1, 2, 3]);
   * // => true
   *
   * _.isObject(_.noop);
   * // => true
   *
   * _.isObject(null);
   * // => false
   */
  function isObject$1(value) {
    var type = typeof value;
    return value != null && (type == 'object' || type == 'function');
  }

  var isObject_1 = isObject$1;

  /** `Object#toString` result references. */
  var asyncTag = '[object AsyncFunction]',
      funcTag = '[object Function]',
      genTag = '[object GeneratorFunction]',
      proxyTag = '[object Proxy]';

  /**
   * Checks if `value` is classified as a `Function` object.
   *
   * @static
   * @memberOf _
   * @since 0.1.0
   * @category Lang
   * @param {*} value The value to check.
   * @returns {boolean} Returns `true` if `value` is a function, else `false`.
   * @example
   *
   * _.isFunction(_);
   * // => true
   *
   * _.isFunction(/abc/);
   * // => false
   */
  function isFunction$1(value) {
    if (!isObject_1(value)) {
      return false;
    }
    // The use of `Object#toString` avoids issues with the `typeof` operator
    // in Safari 9 which returns 'object' for typed arrays and other constructors.
    var tag = _baseGetTag(value);
    return tag == funcTag || tag == genTag || tag == asyncTag || tag == proxyTag;
  }

  var isFunction_1 = isFunction$1;

  /** Used to detect overreaching core-js shims. */
  var coreJsData = _root['__core-js_shared__'];

  var _coreJsData = coreJsData;

  /** Used to detect methods masquerading as native. */
  var maskSrcKey = (function() {
    var uid = /[^.]+$/.exec(_coreJsData && _coreJsData.keys && _coreJsData.keys.IE_PROTO || '');
    return uid ? ('Symbol(src)_1.' + uid) : '';
  }());

  /**
   * Checks if `func` has its source masked.
   *
   * @private
   * @param {Function} func The function to check.
   * @returns {boolean} Returns `true` if `func` is masked, else `false`.
   */
  function isMasked(func) {
    return !!maskSrcKey && (maskSrcKey in func);
  }

  var _isMasked = isMasked;

  /** Used for built-in method references. */
  var funcProto = Function.prototype;

  /** Used to resolve the decompiled source of functions. */
  var funcToString = funcProto.toString;

  /**
   * Converts `func` to its source code.
   *
   * @private
   * @param {Function} func The function to convert.
   * @returns {string} Returns the source code.
   */
  function toSource(func) {
    if (func != null) {
      try {
        return funcToString.call(func);
      } catch (e) {}
      try {
        return (func + '');
      } catch (e) {}
    }
    return '';
  }

  var _toSource = toSource;

  /**
   * Used to match `RegExp`
   * [syntax characters](http://ecma-international.org/ecma-262/7.0/#sec-patterns).
   */
  var reRegExpChar = /[\\^$.*+?()[\]{}|]/g;

  /** Used to detect host constructors (Safari). */
  var reIsHostCtor = /^\[object .+?Constructor\]$/;

  /** Used for built-in method references. */
  var funcProto$1 = Function.prototype,
      objectProto$2 = Object.prototype;

  /** Used to resolve the decompiled source of functions. */
  var funcToString$1 = funcProto$1.toString;

  /** Used to check objects for own properties. */
  var hasOwnProperty$1 = objectProto$2.hasOwnProperty;

  /** Used to detect if a method is native. */
  var reIsNative = RegExp('^' +
    funcToString$1.call(hasOwnProperty$1).replace(reRegExpChar, '\\$&')
    .replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g, '$1.*?') + '$'
  );

  /**
   * The base implementation of `_.isNative` without bad shim checks.
   *
   * @private
   * @param {*} value The value to check.
   * @returns {boolean} Returns `true` if `value` is a native function,
   *  else `false`.
   */
  function baseIsNative(value) {
    if (!isObject_1(value) || _isMasked(value)) {
      return false;
    }
    var pattern = isFunction_1(value) ? reIsNative : reIsHostCtor;
    return pattern.test(_toSource(value));
  }

  var _baseIsNative = baseIsNative;

  /**
   * Gets the value at `key` of `object`.
   *
   * @private
   * @param {Object} [object] The object to query.
   * @param {string} key The key of the property to get.
   * @returns {*} Returns the property value.
   */
  function getValue(object, key) {
    return object == null ? undefined : object[key];
  }

  var _getValue = getValue;

  /**
   * Gets the native function at `key` of `object`.
   *
   * @private
   * @param {Object} object The object to query.
   * @param {string} key The key of the method to get.
   * @returns {*} Returns the function if it's native, else `undefined`.
   */
  function getNative(object, key) {
    var value = _getValue(object, key);
    return _baseIsNative(value) ? value : undefined;
  }

  var _getNative = getNative;

  /* Built-in method references that are verified to be native. */
  var nativeCreate = _getNative(Object, 'create');

  var _nativeCreate = nativeCreate;

  /**
   * Removes all key-value entries from the hash.
   *
   * @private
   * @name clear
   * @memberOf Hash
   */
  function hashClear() {
    this.__data__ = _nativeCreate ? _nativeCreate(null) : {};
    this.size = 0;
  }

  var _hashClear = hashClear;

  /**
   * Removes `key` and its value from the hash.
   *
   * @private
   * @name delete
   * @memberOf Hash
   * @param {Object} hash The hash to modify.
   * @param {string} key The key of the value to remove.
   * @returns {boolean} Returns `true` if the entry was removed, else `false`.
   */
  function hashDelete(key) {
    var result = this.has(key) && delete this.__data__[key];
    this.size -= result ? 1 : 0;
    return result;
  }

  var _hashDelete = hashDelete;

  /** Used to stand-in for `undefined` hash values. */
  var HASH_UNDEFINED = '__lodash_hash_undefined__';

  /** Used for built-in method references. */
  var objectProto$3 = Object.prototype;

  /** Used to check objects for own properties. */
  var hasOwnProperty$2 = objectProto$3.hasOwnProperty;

  /**
   * Gets the hash value for `key`.
   *
   * @private
   * @name get
   * @memberOf Hash
   * @param {string} key The key of the value to get.
   * @returns {*} Returns the entry value.
   */
  function hashGet(key) {
    var data = this.__data__;
    if (_nativeCreate) {
      var result = data[key];
      return result === HASH_UNDEFINED ? undefined : result;
    }
    return hasOwnProperty$2.call(data, key) ? data[key] : undefined;
  }

  var _hashGet = hashGet;

  /** Used for built-in method references. */
  var objectProto$4 = Object.prototype;

  /** Used to check objects for own properties. */
  var hasOwnProperty$3 = objectProto$4.hasOwnProperty;

  /**
   * Checks if a hash value for `key` exists.
   *
   * @private
   * @name has
   * @memberOf Hash
   * @param {string} key The key of the entry to check.
   * @returns {boolean} Returns `true` if an entry for `key` exists, else `false`.
   */
  function hashHas(key) {
    var data = this.__data__;
    return _nativeCreate ? (data[key] !== undefined) : hasOwnProperty$3.call(data, key);
  }

  var _hashHas = hashHas;

  /** Used to stand-in for `undefined` hash values. */
  var HASH_UNDEFINED$1 = '__lodash_hash_undefined__';

  /**
   * Sets the hash `key` to `value`.
   *
   * @private
   * @name set
   * @memberOf Hash
   * @param {string} key The key of the value to set.
   * @param {*} value The value to set.
   * @returns {Object} Returns the hash instance.
   */
  function hashSet(key, value) {
    var data = this.__data__;
    this.size += this.has(key) ? 0 : 1;
    data[key] = (_nativeCreate && value === undefined) ? HASH_UNDEFINED$1 : value;
    return this;
  }

  var _hashSet = hashSet;

  /**
   * Creates a hash object.
   *
   * @private
   * @constructor
   * @param {Array} [entries] The key-value pairs to cache.
   */
  function Hash(entries) {
    var index = -1,
        length = entries == null ? 0 : entries.length;

    this.clear();
    while (++index < length) {
      var entry = entries[index];
      this.set(entry[0], entry[1]);
    }
  }

  // Add methods to `Hash`.
  Hash.prototype.clear = _hashClear;
  Hash.prototype['delete'] = _hashDelete;
  Hash.prototype.get = _hashGet;
  Hash.prototype.has = _hashHas;
  Hash.prototype.set = _hashSet;

  var _Hash = Hash;

  /**
   * Removes all key-value entries from the list cache.
   *
   * @private
   * @name clear
   * @memberOf ListCache
   */
  function listCacheClear() {
    this.__data__ = [];
    this.size = 0;
  }

  var _listCacheClear = listCacheClear;

  /**
   * Performs a
   * [`SameValueZero`](http://ecma-international.org/ecma-262/7.0/#sec-samevaluezero)
   * comparison between two values to determine if they are equivalent.
   *
   * @static
   * @memberOf _
   * @since 4.0.0
   * @category Lang
   * @param {*} value The value to compare.
   * @param {*} other The other value to compare.
   * @returns {boolean} Returns `true` if the values are equivalent, else `false`.
   * @example
   *
   * var object = { 'a': 1 };
   * var other = { 'a': 1 };
   *
   * _.eq(object, object);
   * // => true
   *
   * _.eq(object, other);
   * // => false
   *
   * _.eq('a', 'a');
   * // => true
   *
   * _.eq('a', Object('a'));
   * // => false
   *
   * _.eq(NaN, NaN);
   * // => true
   */
  function eq(value, other) {
    return value === other || (value !== value && other !== other);
  }

  var eq_1 = eq;

  /**
   * Gets the index at which the `key` is found in `array` of key-value pairs.
   *
   * @private
   * @param {Array} array The array to inspect.
   * @param {*} key The key to search for.
   * @returns {number} Returns the index of the matched value, else `-1`.
   */
  function assocIndexOf(array, key) {
    var length = array.length;
    while (length--) {
      if (eq_1(array[length][0], key)) {
        return length;
      }
    }
    return -1;
  }

  var _assocIndexOf = assocIndexOf;

  /** Used for built-in method references. */
  var arrayProto = Array.prototype;

  /** Built-in value references. */
  var splice = arrayProto.splice;

  /**
   * Removes `key` and its value from the list cache.
   *
   * @private
   * @name delete
   * @memberOf ListCache
   * @param {string} key The key of the value to remove.
   * @returns {boolean} Returns `true` if the entry was removed, else `false`.
   */
  function listCacheDelete(key) {
    var data = this.__data__,
        index = _assocIndexOf(data, key);

    if (index < 0) {
      return false;
    }
    var lastIndex = data.length - 1;
    if (index == lastIndex) {
      data.pop();
    } else {
      splice.call(data, index, 1);
    }
    --this.size;
    return true;
  }

  var _listCacheDelete = listCacheDelete;

  /**
   * Gets the list cache value for `key`.
   *
   * @private
   * @name get
   * @memberOf ListCache
   * @param {string} key The key of the value to get.
   * @returns {*} Returns the entry value.
   */
  function listCacheGet(key) {
    var data = this.__data__,
        index = _assocIndexOf(data, key);

    return index < 0 ? undefined : data[index][1];
  }

  var _listCacheGet = listCacheGet;

  /**
   * Checks if a list cache value for `key` exists.
   *
   * @private
   * @name has
   * @memberOf ListCache
   * @param {string} key The key of the entry to check.
   * @returns {boolean} Returns `true` if an entry for `key` exists, else `false`.
   */
  function listCacheHas(key) {
    return _assocIndexOf(this.__data__, key) > -1;
  }

  var _listCacheHas = listCacheHas;

  /**
   * Sets the list cache `key` to `value`.
   *
   * @private
   * @name set
   * @memberOf ListCache
   * @param {string} key The key of the value to set.
   * @param {*} value The value to set.
   * @returns {Object} Returns the list cache instance.
   */
  function listCacheSet(key, value) {
    var data = this.__data__,
        index = _assocIndexOf(data, key);

    if (index < 0) {
      ++this.size;
      data.push([key, value]);
    } else {
      data[index][1] = value;
    }
    return this;
  }

  var _listCacheSet = listCacheSet;

  /**
   * Creates an list cache object.
   *
   * @private
   * @constructor
   * @param {Array} [entries] The key-value pairs to cache.
   */
  function ListCache(entries) {
    var index = -1,
        length = entries == null ? 0 : entries.length;

    this.clear();
    while (++index < length) {
      var entry = entries[index];
      this.set(entry[0], entry[1]);
    }
  }

  // Add methods to `ListCache`.
  ListCache.prototype.clear = _listCacheClear;
  ListCache.prototype['delete'] = _listCacheDelete;
  ListCache.prototype.get = _listCacheGet;
  ListCache.prototype.has = _listCacheHas;
  ListCache.prototype.set = _listCacheSet;

  var _ListCache = ListCache;

  /* Built-in method references that are verified to be native. */
  var Map$1 = _getNative(_root, 'Map');

  var _Map = Map$1;

  /**
   * Removes all key-value entries from the map.
   *
   * @private
   * @name clear
   * @memberOf MapCache
   */
  function mapCacheClear() {
    this.size = 0;
    this.__data__ = {
      'hash': new _Hash,
      'map': new (_Map || _ListCache),
      'string': new _Hash
    };
  }

  var _mapCacheClear = mapCacheClear;

  /**
   * Checks if `value` is suitable for use as unique object key.
   *
   * @private
   * @param {*} value The value to check.
   * @returns {boolean} Returns `true` if `value` is suitable, else `false`.
   */
  function isKeyable(value) {
    var type = typeof value;
    return (type == 'string' || type == 'number' || type == 'symbol' || type == 'boolean')
      ? (value !== '__proto__')
      : (value === null);
  }

  var _isKeyable = isKeyable;

  /**
   * Gets the data for `map`.
   *
   * @private
   * @param {Object} map The map to query.
   * @param {string} key The reference key.
   * @returns {*} Returns the map data.
   */
  function getMapData(map, key) {
    var data = map.__data__;
    return _isKeyable(key)
      ? data[typeof key == 'string' ? 'string' : 'hash']
      : data.map;
  }

  var _getMapData = getMapData;

  /**
   * Removes `key` and its value from the map.
   *
   * @private
   * @name delete
   * @memberOf MapCache
   * @param {string} key The key of the value to remove.
   * @returns {boolean} Returns `true` if the entry was removed, else `false`.
   */
  function mapCacheDelete(key) {
    var result = _getMapData(this, key)['delete'](key);
    this.size -= result ? 1 : 0;
    return result;
  }

  var _mapCacheDelete = mapCacheDelete;

  /**
   * Gets the map value for `key`.
   *
   * @private
   * @name get
   * @memberOf MapCache
   * @param {string} key The key of the value to get.
   * @returns {*} Returns the entry value.
   */
  function mapCacheGet(key) {
    return _getMapData(this, key).get(key);
  }

  var _mapCacheGet = mapCacheGet;

  /**
   * Checks if a map value for `key` exists.
   *
   * @private
   * @name has
   * @memberOf MapCache
   * @param {string} key The key of the entry to check.
   * @returns {boolean} Returns `true` if an entry for `key` exists, else `false`.
   */
  function mapCacheHas(key) {
    return _getMapData(this, key).has(key);
  }

  var _mapCacheHas = mapCacheHas;

  /**
   * Sets the map `key` to `value`.
   *
   * @private
   * @name set
   * @memberOf MapCache
   * @param {string} key The key of the value to set.
   * @param {*} value The value to set.
   * @returns {Object} Returns the map cache instance.
   */
  function mapCacheSet(key, value) {
    var data = _getMapData(this, key),
        size = data.size;

    data.set(key, value);
    this.size += data.size == size ? 0 : 1;
    return this;
  }

  var _mapCacheSet = mapCacheSet;

  /**
   * Creates a map cache object to store key-value pairs.
   *
   * @private
   * @constructor
   * @param {Array} [entries] The key-value pairs to cache.
   */
  function MapCache(entries) {
    var index = -1,
        length = entries == null ? 0 : entries.length;

    this.clear();
    while (++index < length) {
      var entry = entries[index];
      this.set(entry[0], entry[1]);
    }
  }

  // Add methods to `MapCache`.
  MapCache.prototype.clear = _mapCacheClear;
  MapCache.prototype['delete'] = _mapCacheDelete;
  MapCache.prototype.get = _mapCacheGet;
  MapCache.prototype.has = _mapCacheHas;
  MapCache.prototype.set = _mapCacheSet;

  var _MapCache = MapCache;

  /** Error message constants. */
  var FUNC_ERROR_TEXT = 'Expected a function';

  /**
   * Creates a function that memoizes the result of `func`. If `resolver` is
   * provided, it determines the cache key for storing the result based on the
   * arguments provided to the memoized function. By default, the first argument
   * provided to the memoized function is used as the map cache key. The `func`
   * is invoked with the `this` binding of the memoized function.
   *
   * **Note:** The cache is exposed as the `cache` property on the memoized
   * function. Its creation may be customized by replacing the `_.memoize.Cache`
   * constructor with one whose instances implement the
   * [`Map`](http://ecma-international.org/ecma-262/7.0/#sec-properties-of-the-map-prototype-object)
   * method interface of `clear`, `delete`, `get`, `has`, and `set`.
   *
   * @static
   * @memberOf _
   * @since 0.1.0
   * @category Function
   * @param {Function} func The function to have its output memoized.
   * @param {Function} [resolver] The function to resolve the cache key.
   * @returns {Function} Returns the new memoized function.
   * @example
   *
   * var object = { 'a': 1, 'b': 2 };
   * var other = { 'c': 3, 'd': 4 };
   *
   * var values = _.memoize(_.values);
   * values(object);
   * // => [1, 2]
   *
   * values(other);
   * // => [3, 4]
   *
   * object.a = 2;
   * values(object);
   * // => [1, 2]
   *
   * // Modify the result cache.
   * values.cache.set(object, ['a', 'b']);
   * values(object);
   * // => ['a', 'b']
   *
   * // Replace `_.memoize.Cache`.
   * _.memoize.Cache = WeakMap;
   */
  function memoize(func, resolver) {
    if (typeof func != 'function' || (resolver != null && typeof resolver != 'function')) {
      throw new TypeError(FUNC_ERROR_TEXT);
    }
    var memoized = function() {
      var args = arguments,
          key = resolver ? resolver.apply(this, args) : args[0],
          cache = memoized.cache;

      if (cache.has(key)) {
        return cache.get(key);
      }
      var result = func.apply(this, args);
      memoized.cache = cache.set(key, result) || cache;
      return result;
    };
    memoized.cache = new (memoize.Cache || _MapCache);
    return memoized;
  }

  // Expose `MapCache`.
  memoize.Cache = _MapCache;

  var memoize_1 = memoize;

  /** Used as the maximum memoize cache size. */
  var MAX_MEMOIZE_SIZE = 500;

  /**
   * A specialized version of `_.memoize` which clears the memoized function's
   * cache when it exceeds `MAX_MEMOIZE_SIZE`.
   *
   * @private
   * @param {Function} func The function to have its output memoized.
   * @returns {Function} Returns the new memoized function.
   */
  function memoizeCapped(func) {
    var result = memoize_1(func, function(key) {
      if (cache.size === MAX_MEMOIZE_SIZE) {
        cache.clear();
      }
      return key;
    });

    var cache = result.cache;
    return result;
  }

  var _memoizeCapped = memoizeCapped;

  /** Used to match property names within property paths. */
  var rePropName = /[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g;

  /** Used to match backslashes in property paths. */
  var reEscapeChar = /\\(\\)?/g;

  /**
   * Converts `string` to a property path array.
   *
   * @private
   * @param {string} string The string to convert.
   * @returns {Array} Returns the property path array.
   */
  var stringToPath = _memoizeCapped(function(string) {
    var result = [];
    if (string.charCodeAt(0) === 46 /* . */) {
      result.push('');
    }
    string.replace(rePropName, function(match, number, quote, subString) {
      result.push(quote ? subString.replace(reEscapeChar, '$1') : (number || match));
    });
    return result;
  });

  var _stringToPath = stringToPath;

  /**
   * A specialized version of `_.map` for arrays without support for iteratee
   * shorthands.
   *
   * @private
   * @param {Array} [array] The array to iterate over.
   * @param {Function} iteratee The function invoked per iteration.
   * @returns {Array} Returns the new mapped array.
   */
  function arrayMap(array, iteratee) {
    var index = -1,
        length = array == null ? 0 : array.length,
        result = Array(length);

    while (++index < length) {
      result[index] = iteratee(array[index], index, array);
    }
    return result;
  }

  var _arrayMap = arrayMap;

  /** Used as references for various `Number` constants. */
  var INFINITY = 1 / 0;

  /** Used to convert symbols to primitives and strings. */
  var symbolProto = _Symbol ? _Symbol.prototype : undefined,
      symbolToString = symbolProto ? symbolProto.toString : undefined;

  /**
   * The base implementation of `_.toString` which doesn't convert nullish
   * values to empty strings.
   *
   * @private
   * @param {*} value The value to process.
   * @returns {string} Returns the string.
   */
  function baseToString(value) {
    // Exit early for strings to avoid a performance hit in some environments.
    if (typeof value == 'string') {
      return value;
    }
    if (isArray_1(value)) {
      // Recursively convert values (susceptible to call stack limits).
      return _arrayMap(value, baseToString) + '';
    }
    if (isSymbol_1(value)) {
      return symbolToString ? symbolToString.call(value) : '';
    }
    var result = (value + '');
    return (result == '0' && (1 / value) == -INFINITY) ? '-0' : result;
  }

  var _baseToString = baseToString;

  /**
   * Converts `value` to a string. An empty string is returned for `null`
   * and `undefined` values. The sign of `-0` is preserved.
   *
   * @static
   * @memberOf _
   * @since 4.0.0
   * @category Lang
   * @param {*} value The value to convert.
   * @returns {string} Returns the converted string.
   * @example
   *
   * _.toString(null);
   * // => ''
   *
   * _.toString(-0);
   * // => '-0'
   *
   * _.toString([1, 2, 3]);
   * // => '1,2,3'
   */
  function toString$1(value) {
    return value == null ? '' : _baseToString(value);
  }

  var toString_1 = toString$1;

  /**
   * Casts `value` to a path array if it's not one.
   *
   * @private
   * @param {*} value The value to inspect.
   * @param {Object} [object] The object to query keys on.
   * @returns {Array} Returns the cast property path array.
   */
  function castPath(value, object) {
    if (isArray_1(value)) {
      return value;
    }
    return _isKey(value, object) ? [value] : _stringToPath(toString_1(value));
  }

  var _castPath = castPath;

  /** Used as references for various `Number` constants. */
  var INFINITY$1 = 1 / 0;

  /**
   * Converts `value` to a string key if it's not a string or symbol.
   *
   * @private
   * @param {*} value The value to inspect.
   * @returns {string|symbol} Returns the key.
   */
  function toKey(value) {
    if (typeof value == 'string' || isSymbol_1(value)) {
      return value;
    }
    var result = (value + '');
    return (result == '0' && (1 / value) == -INFINITY$1) ? '-0' : result;
  }

  var _toKey = toKey;

  /**
   * The base implementation of `_.get` without support for default values.
   *
   * @private
   * @param {Object} object The object to query.
   * @param {Array|string} path The path of the property to get.
   * @returns {*} Returns the resolved value.
   */
  function baseGet(object, path) {
    path = _castPath(path, object);

    var index = 0,
        length = path.length;

    while (object != null && index < length) {
      object = object[_toKey(path[index++])];
    }
    return (index && index == length) ? object : undefined;
  }

  var _baseGet = baseGet;

  /**
   * Gets the value at `path` of `object`. If the resolved value is
   * `undefined`, the `defaultValue` is returned in its place.
   *
   * @static
   * @memberOf _
   * @since 3.7.0
   * @category Object
   * @param {Object} object The object to query.
   * @param {Array|string} path The path of the property to get.
   * @param {*} [defaultValue] The value returned for `undefined` resolved values.
   * @returns {*} Returns the resolved value.
   * @example
   *
   * var object = { 'a': [{ 'b': { 'c': 3 } }] };
   *
   * _.get(object, 'a[0].b.c');
   * // => 3
   *
   * _.get(object, ['a', '0', 'b', 'c']);
   * // => 3
   *
   * _.get(object, 'a.b.c', 'default');
   * // => 'default'
   */
  function get(object, path, defaultValue) {
    var result = object == null ? undefined : _baseGet(object, path);
    return result === undefined ? defaultValue : result;
  }

  var get_1 = get;

  var defineProperty = (function() {
    try {
      var func = _getNative(Object, 'defineProperty');
      func({}, '', {});
      return func;
    } catch (e) {}
  }());

  var _defineProperty = defineProperty;

  /**
   * The base implementation of `assignValue` and `assignMergeValue` without
   * value checks.
   *
   * @private
   * @param {Object} object The object to modify.
   * @param {string} key The key of the property to assign.
   * @param {*} value The value to assign.
   */
  function baseAssignValue(object, key, value) {
    if (key == '__proto__' && _defineProperty) {
      _defineProperty(object, key, {
        'configurable': true,
        'enumerable': true,
        'value': value,
        'writable': true
      });
    } else {
      object[key] = value;
    }
  }

  var _baseAssignValue = baseAssignValue;

  /** Used for built-in method references. */
  var objectProto$5 = Object.prototype;

  /** Used to check objects for own properties. */
  var hasOwnProperty$4 = objectProto$5.hasOwnProperty;

  /**
   * Assigns `value` to `key` of `object` if the existing value is not equivalent
   * using [`SameValueZero`](http://ecma-international.org/ecma-262/7.0/#sec-samevaluezero)
   * for equality comparisons.
   *
   * @private
   * @param {Object} object The object to modify.
   * @param {string} key The key of the property to assign.
   * @param {*} value The value to assign.
   */
  function assignValue(object, key, value) {
    var objValue = object[key];
    if (!(hasOwnProperty$4.call(object, key) && eq_1(objValue, value)) ||
        (value === undefined && !(key in object))) {
      _baseAssignValue(object, key, value);
    }
  }

  var _assignValue = assignValue;

  /** Used as references for various `Number` constants. */
  var MAX_SAFE_INTEGER = 9007199254740991;

  /** Used to detect unsigned integer values. */
  var reIsUint = /^(?:0|[1-9]\d*)$/;

  /**
   * Checks if `value` is a valid array-like index.
   *
   * @private
   * @param {*} value The value to check.
   * @param {number} [length=MAX_SAFE_INTEGER] The upper bounds of a valid index.
   * @returns {boolean} Returns `true` if `value` is a valid index, else `false`.
   */
  function isIndex(value, length) {
    var type = typeof value;
    length = length == null ? MAX_SAFE_INTEGER : length;

    return !!length &&
      (type == 'number' ||
        (type != 'symbol' && reIsUint.test(value))) &&
          (value > -1 && value % 1 == 0 && value < length);
  }

  var _isIndex = isIndex;

  /**
   * The base implementation of `_.set`.
   *
   * @private
   * @param {Object} object The object to modify.
   * @param {Array|string} path The path of the property to set.
   * @param {*} value The value to set.
   * @param {Function} [customizer] The function to customize path creation.
   * @returns {Object} Returns `object`.
   */
  function baseSet(object, path, value, customizer) {
    if (!isObject_1(object)) {
      return object;
    }
    path = _castPath(path, object);

    var index = -1,
        length = path.length,
        lastIndex = length - 1,
        nested = object;

    while (nested != null && ++index < length) {
      var key = _toKey(path[index]),
          newValue = value;

      if (key === '__proto__' || key === 'constructor' || key === 'prototype') {
        return object;
      }

      if (index != lastIndex) {
        var objValue = nested[key];
        newValue = customizer ? customizer(objValue, key, nested) : undefined;
        if (newValue === undefined) {
          newValue = isObject_1(objValue)
            ? objValue
            : (_isIndex(path[index + 1]) ? [] : {});
        }
      }
      _assignValue(nested, key, newValue);
      nested = nested[key];
    }
    return object;
  }

  var _baseSet = baseSet;

  /**
   * Sets the value at `path` of `object`. If a portion of `path` doesn't exist,
   * it's created. Arrays are created for missing index properties while objects
   * are created for all other missing properties. Use `_.setWith` to customize
   * `path` creation.
   *
   * **Note:** This method mutates `object`.
   *
   * @static
   * @memberOf _
   * @since 3.7.0
   * @category Object
   * @param {Object} object The object to modify.
   * @param {Array|string} path The path of the property to set.
   * @param {*} value The value to set.
   * @returns {Object} Returns `object`.
   * @example
   *
   * var object = { 'a': [{ 'b': { 'c': 3 } }] };
   *
   * _.set(object, 'a[0].b.c', 4);
   * console.log(object.a[0].b.c);
   * // => 4
   *
   * _.set(object, ['x', '0', 'y', 'z'], 5);
   * console.log(object.x[0].y.z);
   * // => 5
   */
  function set(object, path, value) {
    return object == null ? object : _baseSet(object, path, value);
  }

  var set_1 = set;

  class CancelError extends Error {
    constructor(message) {
      super(message); // https://www.dannyguo.com/blog/how-to-fix-instanceof-not-working-for-custom-errors-in-typescript/

      Object.setPrototypeOf(this, CancelError.prototype);
    }

  }

  /* eslint complexity: [2, 18], max-statements: [2, 33] */
  var shams = function hasSymbols() {
  	if (typeof Symbol !== 'function' || typeof Object.getOwnPropertySymbols !== 'function') { return false; }
  	if (typeof Symbol.iterator === 'symbol') { return true; }

  	var obj = {};
  	var sym = Symbol('test');
  	var symObj = Object(sym);
  	if (typeof sym === 'string') { return false; }

  	if (Object.prototype.toString.call(sym) !== '[object Symbol]') { return false; }
  	if (Object.prototype.toString.call(symObj) !== '[object Symbol]') { return false; }

  	// temp disabled per https://github.com/ljharb/object.assign/issues/17
  	// if (sym instanceof Symbol) { return false; }
  	// temp disabled per https://github.com/WebReflection/get-own-property-symbols/issues/4
  	// if (!(symObj instanceof Symbol)) { return false; }

  	// if (typeof Symbol.prototype.toString !== 'function') { return false; }
  	// if (String(sym) !== Symbol.prototype.toString.call(sym)) { return false; }

  	var symVal = 42;
  	obj[sym] = symVal;
  	for (sym in obj) { return false; } // eslint-disable-line no-restricted-syntax, no-unreachable-loop
  	if (typeof Object.keys === 'function' && Object.keys(obj).length !== 0) { return false; }

  	if (typeof Object.getOwnPropertyNames === 'function' && Object.getOwnPropertyNames(obj).length !== 0) { return false; }

  	var syms = Object.getOwnPropertySymbols(obj);
  	if (syms.length !== 1 || syms[0] !== sym) { return false; }

  	if (!Object.prototype.propertyIsEnumerable.call(obj, sym)) { return false; }

  	if (typeof Object.getOwnPropertyDescriptor === 'function') {
  		var descriptor = Object.getOwnPropertyDescriptor(obj, sym);
  		if (descriptor.value !== symVal || descriptor.enumerable !== true) { return false; }
  	}

  	return true;
  };

  var origSymbol = typeof Symbol !== 'undefined' && Symbol;


  var hasSymbols = function hasNativeSymbols() {
  	if (typeof origSymbol !== 'function') { return false; }
  	if (typeof Symbol !== 'function') { return false; }
  	if (typeof origSymbol('foo') !== 'symbol') { return false; }
  	if (typeof Symbol('bar') !== 'symbol') { return false; }

  	return shams();
  };

  /* eslint no-invalid-this: 1 */

  var ERROR_MESSAGE = 'Function.prototype.bind called on incompatible ';
  var slice = Array.prototype.slice;
  var toStr = Object.prototype.toString;
  var funcType = '[object Function]';

  var implementation = function bind(that) {
      var target = this;
      if (typeof target !== 'function' || toStr.call(target) !== funcType) {
          throw new TypeError(ERROR_MESSAGE + target);
      }
      var args = slice.call(arguments, 1);

      var bound;
      var binder = function () {
          if (this instanceof bound) {
              var result = target.apply(
                  this,
                  args.concat(slice.call(arguments))
              );
              if (Object(result) === result) {
                  return result;
              }
              return this;
          } else {
              return target.apply(
                  that,
                  args.concat(slice.call(arguments))
              );
          }
      };

      var boundLength = Math.max(0, target.length - args.length);
      var boundArgs = [];
      for (var i = 0; i < boundLength; i++) {
          boundArgs.push('$' + i);
      }

      bound = Function('binder', 'return function (' + boundArgs.join(',') + '){ return binder.apply(this,arguments); }')(binder);

      if (target.prototype) {
          var Empty = function Empty() {};
          Empty.prototype = target.prototype;
          bound.prototype = new Empty();
          Empty.prototype = null;
      }

      return bound;
  };

  var functionBind = Function.prototype.bind || implementation;

  var src = functionBind.call(Function.call, Object.prototype.hasOwnProperty);

  var undefined$1;

  var $SyntaxError = SyntaxError;
  var $Function = Function;
  var $TypeError = TypeError;

  // eslint-disable-next-line consistent-return
  var getEvalledConstructor = function (expressionSyntax) {
  	try {
  		return $Function('"use strict"; return (' + expressionSyntax + ').constructor;')();
  	} catch (e) {}
  };

  var $gOPD = Object.getOwnPropertyDescriptor;
  if ($gOPD) {
  	try {
  		$gOPD({}, '');
  	} catch (e) {
  		$gOPD = null; // this is IE 8, which has a broken gOPD
  	}
  }

  var throwTypeError = function () {
  	throw new $TypeError();
  };
  var ThrowTypeError = $gOPD
  	? (function () {
  		try {
  			// eslint-disable-next-line no-unused-expressions, no-caller, no-restricted-properties
  			arguments.callee; // IE 8 does not throw here
  			return throwTypeError;
  		} catch (calleeThrows) {
  			try {
  				// IE 8 throws on Object.getOwnPropertyDescriptor(arguments, '')
  				return $gOPD(arguments, 'callee').get;
  			} catch (gOPDthrows) {
  				return throwTypeError;
  			}
  		}
  	}())
  	: throwTypeError;

  var hasSymbols$1 = hasSymbols();

  var getProto = Object.getPrototypeOf || function (x) { return x.__proto__; }; // eslint-disable-line no-proto

  var needsEval = {};

  var TypedArray = typeof Uint8Array === 'undefined' ? undefined$1 : getProto(Uint8Array);

  var INTRINSICS = {
  	'%AggregateError%': typeof AggregateError === 'undefined' ? undefined$1 : AggregateError,
  	'%Array%': Array,
  	'%ArrayBuffer%': typeof ArrayBuffer === 'undefined' ? undefined$1 : ArrayBuffer,
  	'%ArrayIteratorPrototype%': hasSymbols$1 ? getProto([][Symbol.iterator]()) : undefined$1,
  	'%AsyncFromSyncIteratorPrototype%': undefined$1,
  	'%AsyncFunction%': needsEval,
  	'%AsyncGenerator%': needsEval,
  	'%AsyncGeneratorFunction%': needsEval,
  	'%AsyncIteratorPrototype%': needsEval,
  	'%Atomics%': typeof Atomics === 'undefined' ? undefined$1 : Atomics,
  	'%BigInt%': typeof BigInt === 'undefined' ? undefined$1 : BigInt,
  	'%Boolean%': Boolean,
  	'%DataView%': typeof DataView === 'undefined' ? undefined$1 : DataView,
  	'%Date%': Date,
  	'%decodeURI%': decodeURI,
  	'%decodeURIComponent%': decodeURIComponent,
  	'%encodeURI%': encodeURI,
  	'%encodeURIComponent%': encodeURIComponent,
  	'%Error%': Error,
  	'%eval%': eval, // eslint-disable-line no-eval
  	'%EvalError%': EvalError,
  	'%Float32Array%': typeof Float32Array === 'undefined' ? undefined$1 : Float32Array,
  	'%Float64Array%': typeof Float64Array === 'undefined' ? undefined$1 : Float64Array,
  	'%FinalizationRegistry%': typeof FinalizationRegistry === 'undefined' ? undefined$1 : FinalizationRegistry,
  	'%Function%': $Function,
  	'%GeneratorFunction%': needsEval,
  	'%Int8Array%': typeof Int8Array === 'undefined' ? undefined$1 : Int8Array,
  	'%Int16Array%': typeof Int16Array === 'undefined' ? undefined$1 : Int16Array,
  	'%Int32Array%': typeof Int32Array === 'undefined' ? undefined$1 : Int32Array,
  	'%isFinite%': isFinite,
  	'%isNaN%': isNaN,
  	'%IteratorPrototype%': hasSymbols$1 ? getProto(getProto([][Symbol.iterator]())) : undefined$1,
  	'%JSON%': typeof JSON === 'object' ? JSON : undefined$1,
  	'%Map%': typeof Map === 'undefined' ? undefined$1 : Map,
  	'%MapIteratorPrototype%': typeof Map === 'undefined' || !hasSymbols$1 ? undefined$1 : getProto(new Map()[Symbol.iterator]()),
  	'%Math%': Math,
  	'%Number%': Number,
  	'%Object%': Object,
  	'%parseFloat%': parseFloat,
  	'%parseInt%': parseInt,
  	'%Promise%': typeof Promise === 'undefined' ? undefined$1 : Promise,
  	'%Proxy%': typeof Proxy === 'undefined' ? undefined$1 : Proxy,
  	'%RangeError%': RangeError,
  	'%ReferenceError%': ReferenceError,
  	'%Reflect%': typeof Reflect === 'undefined' ? undefined$1 : Reflect,
  	'%RegExp%': RegExp,
  	'%Set%': typeof Set === 'undefined' ? undefined$1 : Set,
  	'%SetIteratorPrototype%': typeof Set === 'undefined' || !hasSymbols$1 ? undefined$1 : getProto(new Set()[Symbol.iterator]()),
  	'%SharedArrayBuffer%': typeof SharedArrayBuffer === 'undefined' ? undefined$1 : SharedArrayBuffer,
  	'%String%': String,
  	'%StringIteratorPrototype%': hasSymbols$1 ? getProto(''[Symbol.iterator]()) : undefined$1,
  	'%Symbol%': hasSymbols$1 ? Symbol : undefined$1,
  	'%SyntaxError%': $SyntaxError,
  	'%ThrowTypeError%': ThrowTypeError,
  	'%TypedArray%': TypedArray,
  	'%TypeError%': $TypeError,
  	'%Uint8Array%': typeof Uint8Array === 'undefined' ? undefined$1 : Uint8Array,
  	'%Uint8ClampedArray%': typeof Uint8ClampedArray === 'undefined' ? undefined$1 : Uint8ClampedArray,
  	'%Uint16Array%': typeof Uint16Array === 'undefined' ? undefined$1 : Uint16Array,
  	'%Uint32Array%': typeof Uint32Array === 'undefined' ? undefined$1 : Uint32Array,
  	'%URIError%': URIError,
  	'%WeakMap%': typeof WeakMap === 'undefined' ? undefined$1 : WeakMap,
  	'%WeakRef%': typeof WeakRef === 'undefined' ? undefined$1 : WeakRef,
  	'%WeakSet%': typeof WeakSet === 'undefined' ? undefined$1 : WeakSet
  };

  var doEval = function doEval(name) {
  	var value;
  	if (name === '%AsyncFunction%') {
  		value = getEvalledConstructor('async function () {}');
  	} else if (name === '%GeneratorFunction%') {
  		value = getEvalledConstructor('function* () {}');
  	} else if (name === '%AsyncGeneratorFunction%') {
  		value = getEvalledConstructor('async function* () {}');
  	} else if (name === '%AsyncGenerator%') {
  		var fn = doEval('%AsyncGeneratorFunction%');
  		if (fn) {
  			value = fn.prototype;
  		}
  	} else if (name === '%AsyncIteratorPrototype%') {
  		var gen = doEval('%AsyncGenerator%');
  		if (gen) {
  			value = getProto(gen.prototype);
  		}
  	}

  	INTRINSICS[name] = value;

  	return value;
  };

  var LEGACY_ALIASES = {
  	'%ArrayBufferPrototype%': ['ArrayBuffer', 'prototype'],
  	'%ArrayPrototype%': ['Array', 'prototype'],
  	'%ArrayProto_entries%': ['Array', 'prototype', 'entries'],
  	'%ArrayProto_forEach%': ['Array', 'prototype', 'forEach'],
  	'%ArrayProto_keys%': ['Array', 'prototype', 'keys'],
  	'%ArrayProto_values%': ['Array', 'prototype', 'values'],
  	'%AsyncFunctionPrototype%': ['AsyncFunction', 'prototype'],
  	'%AsyncGenerator%': ['AsyncGeneratorFunction', 'prototype'],
  	'%AsyncGeneratorPrototype%': ['AsyncGeneratorFunction', 'prototype', 'prototype'],
  	'%BooleanPrototype%': ['Boolean', 'prototype'],
  	'%DataViewPrototype%': ['DataView', 'prototype'],
  	'%DatePrototype%': ['Date', 'prototype'],
  	'%ErrorPrototype%': ['Error', 'prototype'],
  	'%EvalErrorPrototype%': ['EvalError', 'prototype'],
  	'%Float32ArrayPrototype%': ['Float32Array', 'prototype'],
  	'%Float64ArrayPrototype%': ['Float64Array', 'prototype'],
  	'%FunctionPrototype%': ['Function', 'prototype'],
  	'%Generator%': ['GeneratorFunction', 'prototype'],
  	'%GeneratorPrototype%': ['GeneratorFunction', 'prototype', 'prototype'],
  	'%Int8ArrayPrototype%': ['Int8Array', 'prototype'],
  	'%Int16ArrayPrototype%': ['Int16Array', 'prototype'],
  	'%Int32ArrayPrototype%': ['Int32Array', 'prototype'],
  	'%JSONParse%': ['JSON', 'parse'],
  	'%JSONStringify%': ['JSON', 'stringify'],
  	'%MapPrototype%': ['Map', 'prototype'],
  	'%NumberPrototype%': ['Number', 'prototype'],
  	'%ObjectPrototype%': ['Object', 'prototype'],
  	'%ObjProto_toString%': ['Object', 'prototype', 'toString'],
  	'%ObjProto_valueOf%': ['Object', 'prototype', 'valueOf'],
  	'%PromisePrototype%': ['Promise', 'prototype'],
  	'%PromiseProto_then%': ['Promise', 'prototype', 'then'],
  	'%Promise_all%': ['Promise', 'all'],
  	'%Promise_reject%': ['Promise', 'reject'],
  	'%Promise_resolve%': ['Promise', 'resolve'],
  	'%RangeErrorPrototype%': ['RangeError', 'prototype'],
  	'%ReferenceErrorPrototype%': ['ReferenceError', 'prototype'],
  	'%RegExpPrototype%': ['RegExp', 'prototype'],
  	'%SetPrototype%': ['Set', 'prototype'],
  	'%SharedArrayBufferPrototype%': ['SharedArrayBuffer', 'prototype'],
  	'%StringPrototype%': ['String', 'prototype'],
  	'%SymbolPrototype%': ['Symbol', 'prototype'],
  	'%SyntaxErrorPrototype%': ['SyntaxError', 'prototype'],
  	'%TypedArrayPrototype%': ['TypedArray', 'prototype'],
  	'%TypeErrorPrototype%': ['TypeError', 'prototype'],
  	'%Uint8ArrayPrototype%': ['Uint8Array', 'prototype'],
  	'%Uint8ClampedArrayPrototype%': ['Uint8ClampedArray', 'prototype'],
  	'%Uint16ArrayPrototype%': ['Uint16Array', 'prototype'],
  	'%Uint32ArrayPrototype%': ['Uint32Array', 'prototype'],
  	'%URIErrorPrototype%': ['URIError', 'prototype'],
  	'%WeakMapPrototype%': ['WeakMap', 'prototype'],
  	'%WeakSetPrototype%': ['WeakSet', 'prototype']
  };



  var $concat = functionBind.call(Function.call, Array.prototype.concat);
  var $spliceApply = functionBind.call(Function.apply, Array.prototype.splice);
  var $replace = functionBind.call(Function.call, String.prototype.replace);
  var $strSlice = functionBind.call(Function.call, String.prototype.slice);

  /* adapted from https://github.com/lodash/lodash/blob/4.17.15/dist/lodash.js#L6735-L6744 */
  var rePropName$1 = /[^%.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|%$))/g;
  var reEscapeChar$1 = /\\(\\)?/g; /** Used to match backslashes in property paths. */
  var stringToPath$1 = function stringToPath(string) {
  	var first = $strSlice(string, 0, 1);
  	var last = $strSlice(string, -1);
  	if (first === '%' && last !== '%') {
  		throw new $SyntaxError('invalid intrinsic syntax, expected closing `%`');
  	} else if (last === '%' && first !== '%') {
  		throw new $SyntaxError('invalid intrinsic syntax, expected opening `%`');
  	}
  	var result = [];
  	$replace(string, rePropName$1, function (match, number, quote, subString) {
  		result[result.length] = quote ? $replace(subString, reEscapeChar$1, '$1') : number || match;
  	});
  	return result;
  };
  /* end adaptation */

  var getBaseIntrinsic = function getBaseIntrinsic(name, allowMissing) {
  	var intrinsicName = name;
  	var alias;
  	if (src(LEGACY_ALIASES, intrinsicName)) {
  		alias = LEGACY_ALIASES[intrinsicName];
  		intrinsicName = '%' + alias[0] + '%';
  	}

  	if (src(INTRINSICS, intrinsicName)) {
  		var value = INTRINSICS[intrinsicName];
  		if (value === needsEval) {
  			value = doEval(intrinsicName);
  		}
  		if (typeof value === 'undefined' && !allowMissing) {
  			throw new $TypeError('intrinsic ' + name + ' exists, but is not available. Please file an issue!');
  		}

  		return {
  			alias: alias,
  			name: intrinsicName,
  			value: value
  		};
  	}

  	throw new $SyntaxError('intrinsic ' + name + ' does not exist!');
  };

  var getIntrinsic = function GetIntrinsic(name, allowMissing) {
  	if (typeof name !== 'string' || name.length === 0) {
  		throw new $TypeError('intrinsic name must be a non-empty string');
  	}
  	if (arguments.length > 1 && typeof allowMissing !== 'boolean') {
  		throw new $TypeError('"allowMissing" argument must be a boolean');
  	}

  	var parts = stringToPath$1(name);
  	var intrinsicBaseName = parts.length > 0 ? parts[0] : '';

  	var intrinsic = getBaseIntrinsic('%' + intrinsicBaseName + '%', allowMissing);
  	var intrinsicRealName = intrinsic.name;
  	var value = intrinsic.value;
  	var skipFurtherCaching = false;

  	var alias = intrinsic.alias;
  	if (alias) {
  		intrinsicBaseName = alias[0];
  		$spliceApply(parts, $concat([0, 1], alias));
  	}

  	for (var i = 1, isOwn = true; i < parts.length; i += 1) {
  		var part = parts[i];
  		var first = $strSlice(part, 0, 1);
  		var last = $strSlice(part, -1);
  		if (
  			(
  				(first === '"' || first === "'" || first === '`')
  				|| (last === '"' || last === "'" || last === '`')
  			)
  			&& first !== last
  		) {
  			throw new $SyntaxError('property names with quotes must have matching quotes');
  		}
  		if (part === 'constructor' || !isOwn) {
  			skipFurtherCaching = true;
  		}

  		intrinsicBaseName += '.' + part;
  		intrinsicRealName = '%' + intrinsicBaseName + '%';

  		if (src(INTRINSICS, intrinsicRealName)) {
  			value = INTRINSICS[intrinsicRealName];
  		} else if (value != null) {
  			if (!(part in value)) {
  				if (!allowMissing) {
  					throw new $TypeError('base intrinsic for ' + name + ' exists, but the property is not available.');
  				}
  				return void undefined$1;
  			}
  			if ($gOPD && (i + 1) >= parts.length) {
  				var desc = $gOPD(value, part);
  				isOwn = !!desc;

  				// By convention, when a data property is converted to an accessor
  				// property to emulate a data property that does not suffer from
  				// the override mistake, that accessor's getter is marked with
  				// an `originalValue` property. Here, when we detect this, we
  				// uphold the illusion by pretending to see that original data
  				// property, i.e., returning the value rather than the getter
  				// itself.
  				if (isOwn && 'get' in desc && !('originalValue' in desc.get)) {
  					value = desc.get;
  				} else {
  					value = value[part];
  				}
  			} else {
  				isOwn = src(value, part);
  				value = value[part];
  			}

  			if (isOwn && !skipFurtherCaching) {
  				INTRINSICS[intrinsicRealName] = value;
  			}
  		}
  	}
  	return value;
  };

  var callBind = createCommonjsModule(function (module) {




  var $apply = getIntrinsic('%Function.prototype.apply%');
  var $call = getIntrinsic('%Function.prototype.call%');
  var $reflectApply = getIntrinsic('%Reflect.apply%', true) || functionBind.call($call, $apply);

  var $gOPD = getIntrinsic('%Object.getOwnPropertyDescriptor%', true);
  var $defineProperty = getIntrinsic('%Object.defineProperty%', true);
  var $max = getIntrinsic('%Math.max%');

  if ($defineProperty) {
  	try {
  		$defineProperty({}, 'a', { value: 1 });
  	} catch (e) {
  		// IE 8 has a broken defineProperty
  		$defineProperty = null;
  	}
  }

  module.exports = function callBind(originalFunction) {
  	var func = $reflectApply(functionBind, $call, arguments);
  	if ($gOPD && $defineProperty) {
  		var desc = $gOPD(func, 'length');
  		if (desc.configurable) {
  			// original length, plus the receiver, minus any additional arguments (after the receiver)
  			$defineProperty(
  				func,
  				'length',
  				{ value: 1 + $max(0, originalFunction.length - (arguments.length - 1)) }
  			);
  		}
  	}
  	return func;
  };

  var applyBind = function applyBind() {
  	return $reflectApply(functionBind, $apply, arguments);
  };

  if ($defineProperty) {
  	$defineProperty(module.exports, 'apply', { value: applyBind });
  } else {
  	module.exports.apply = applyBind;
  }
  });

  var $indexOf = callBind(getIntrinsic('String.prototype.indexOf'));

  var callBound = function callBoundIntrinsic(name, allowMissing) {
  	var intrinsic = getIntrinsic(name, !!allowMissing);
  	if (typeof intrinsic === 'function' && $indexOf(name, '.prototype.') > -1) {
  		return callBind(intrinsic);
  	}
  	return intrinsic;
  };

  var _nodeResolve_empty = {};

  var _nodeResolve_empty$1 = {
    __proto__: null,
    'default': _nodeResolve_empty
  };

  var require$$0 = getCjsExportFromNamespace(_nodeResolve_empty$1);

  var hasMap = typeof Map === 'function' && Map.prototype;
  var mapSizeDescriptor = Object.getOwnPropertyDescriptor && hasMap ? Object.getOwnPropertyDescriptor(Map.prototype, 'size') : null;
  var mapSize = hasMap && mapSizeDescriptor && typeof mapSizeDescriptor.get === 'function' ? mapSizeDescriptor.get : null;
  var mapForEach = hasMap && Map.prototype.forEach;
  var hasSet = typeof Set === 'function' && Set.prototype;
  var setSizeDescriptor = Object.getOwnPropertyDescriptor && hasSet ? Object.getOwnPropertyDescriptor(Set.prototype, 'size') : null;
  var setSize = hasSet && setSizeDescriptor && typeof setSizeDescriptor.get === 'function' ? setSizeDescriptor.get : null;
  var setForEach = hasSet && Set.prototype.forEach;
  var hasWeakMap = typeof WeakMap === 'function' && WeakMap.prototype;
  var weakMapHas = hasWeakMap ? WeakMap.prototype.has : null;
  var hasWeakSet = typeof WeakSet === 'function' && WeakSet.prototype;
  var weakSetHas = hasWeakSet ? WeakSet.prototype.has : null;
  var hasWeakRef = typeof WeakRef === 'function' && WeakRef.prototype;
  var weakRefDeref = hasWeakRef ? WeakRef.prototype.deref : null;
  var booleanValueOf = Boolean.prototype.valueOf;
  var objectToString$1 = Object.prototype.toString;
  var functionToString = Function.prototype.toString;
  var $match = String.prototype.match;
  var $slice = String.prototype.slice;
  var $replace$1 = String.prototype.replace;
  var $toUpperCase = String.prototype.toUpperCase;
  var $toLowerCase = String.prototype.toLowerCase;
  var $test = RegExp.prototype.test;
  var $concat$1 = Array.prototype.concat;
  var $join = Array.prototype.join;
  var $arrSlice = Array.prototype.slice;
  var $floor = Math.floor;
  var bigIntValueOf = typeof BigInt === 'function' ? BigInt.prototype.valueOf : null;
  var gOPS = Object.getOwnPropertySymbols;
  var symToString = typeof Symbol === 'function' && typeof Symbol.iterator === 'symbol' ? Symbol.prototype.toString : null;
  var hasShammedSymbols = typeof Symbol === 'function' && typeof Symbol.iterator === 'object';
  // ie, `has-tostringtag/shams
  var toStringTag = typeof Symbol === 'function' && Symbol.toStringTag && (typeof Symbol.toStringTag === hasShammedSymbols ? 'object' : 'symbol')
      ? Symbol.toStringTag
      : null;
  var isEnumerable = Object.prototype.propertyIsEnumerable;

  var gPO = (typeof Reflect === 'function' ? Reflect.getPrototypeOf : Object.getPrototypeOf) || (
      [].__proto__ === Array.prototype // eslint-disable-line no-proto
          ? function (O) {
              return O.__proto__; // eslint-disable-line no-proto
          }
          : null
  );

  function addNumericSeparator(num, str) {
      if (
          num === Infinity
          || num === -Infinity
          || num !== num
          || (num && num > -1000 && num < 1000)
          || $test.call(/e/, str)
      ) {
          return str;
      }
      var sepRegex = /[0-9](?=(?:[0-9]{3})+(?![0-9]))/g;
      if (typeof num === 'number') {
          var int = num < 0 ? -$floor(-num) : $floor(num); // trunc(num)
          if (int !== num) {
              var intStr = String(int);
              var dec = $slice.call(str, intStr.length + 1);
              return $replace$1.call(intStr, sepRegex, '$&_') + '.' + $replace$1.call($replace$1.call(dec, /([0-9]{3})/g, '$&_'), /_$/, '');
          }
      }
      return $replace$1.call(str, sepRegex, '$&_');
  }

  var inspectCustom = require$$0.custom;
  var inspectSymbol = inspectCustom && isSymbol$1(inspectCustom) ? inspectCustom : null;

  var objectInspect = function inspect_(obj, options, depth, seen) {
      var opts = options || {};

      if (has(opts, 'quoteStyle') && (opts.quoteStyle !== 'single' && opts.quoteStyle !== 'double')) {
          throw new TypeError('option "quoteStyle" must be "single" or "double"');
      }
      if (
          has(opts, 'maxStringLength') && (typeof opts.maxStringLength === 'number'
              ? opts.maxStringLength < 0 && opts.maxStringLength !== Infinity
              : opts.maxStringLength !== null
          )
      ) {
          throw new TypeError('option "maxStringLength", if provided, must be a positive integer, Infinity, or `null`');
      }
      var customInspect = has(opts, 'customInspect') ? opts.customInspect : true;
      if (typeof customInspect !== 'boolean' && customInspect !== 'symbol') {
          throw new TypeError('option "customInspect", if provided, must be `true`, `false`, or `\'symbol\'`');
      }

      if (
          has(opts, 'indent')
          && opts.indent !== null
          && opts.indent !== '\t'
          && !(parseInt(opts.indent, 10) === opts.indent && opts.indent > 0)
      ) {
          throw new TypeError('option "indent" must be "\\t", an integer > 0, or `null`');
      }
      if (has(opts, 'numericSeparator') && typeof opts.numericSeparator !== 'boolean') {
          throw new TypeError('option "numericSeparator", if provided, must be `true` or `false`');
      }
      var numericSeparator = opts.numericSeparator;

      if (typeof obj === 'undefined') {
          return 'undefined';
      }
      if (obj === null) {
          return 'null';
      }
      if (typeof obj === 'boolean') {
          return obj ? 'true' : 'false';
      }

      if (typeof obj === 'string') {
          return inspectString(obj, opts);
      }
      if (typeof obj === 'number') {
          if (obj === 0) {
              return Infinity / obj > 0 ? '0' : '-0';
          }
          var str = String(obj);
          return numericSeparator ? addNumericSeparator(obj, str) : str;
      }
      if (typeof obj === 'bigint') {
          var bigIntStr = String(obj) + 'n';
          return numericSeparator ? addNumericSeparator(obj, bigIntStr) : bigIntStr;
      }

      var maxDepth = typeof opts.depth === 'undefined' ? 5 : opts.depth;
      if (typeof depth === 'undefined') { depth = 0; }
      if (depth >= maxDepth && maxDepth > 0 && typeof obj === 'object') {
          return isArray$2(obj) ? '[Array]' : '[Object]';
      }

      var indent = getIndent(opts, depth);

      if (typeof seen === 'undefined') {
          seen = [];
      } else if (indexOf(seen, obj) >= 0) {
          return '[Circular]';
      }

      function inspect(value, from, noIndent) {
          if (from) {
              seen = $arrSlice.call(seen);
              seen.push(from);
          }
          if (noIndent) {
              var newOpts = {
                  depth: opts.depth
              };
              if (has(opts, 'quoteStyle')) {
                  newOpts.quoteStyle = opts.quoteStyle;
              }
              return inspect_(value, newOpts, depth + 1, seen);
          }
          return inspect_(value, opts, depth + 1, seen);
      }

      if (typeof obj === 'function') {
          var name = nameOf(obj);
          var keys = arrObjKeys(obj, inspect);
          return '[Function' + (name ? ': ' + name : ' (anonymous)') + ']' + (keys.length > 0 ? ' { ' + $join.call(keys, ', ') + ' }' : '');
      }
      if (isSymbol$1(obj)) {
          var symString = hasShammedSymbols ? $replace$1.call(String(obj), /^(Symbol\(.*\))_[^)]*$/, '$1') : symToString.call(obj);
          return typeof obj === 'object' && !hasShammedSymbols ? markBoxed(symString) : symString;
      }
      if (isElement(obj)) {
          var s = '<' + $toLowerCase.call(String(obj.nodeName));
          var attrs = obj.attributes || [];
          for (var i = 0; i < attrs.length; i++) {
              s += ' ' + attrs[i].name + '=' + wrapQuotes(quote(attrs[i].value), 'double', opts);
          }
          s += '>';
          if (obj.childNodes && obj.childNodes.length) { s += '...'; }
          s += '</' + $toLowerCase.call(String(obj.nodeName)) + '>';
          return s;
      }
      if (isArray$2(obj)) {
          if (obj.length === 0) { return '[]'; }
          var xs = arrObjKeys(obj, inspect);
          if (indent && !singleLineValues(xs)) {
              return '[' + indentedJoin(xs, indent) + ']';
          }
          return '[ ' + $join.call(xs, ', ') + ' ]';
      }
      if (isError(obj)) {
          var parts = arrObjKeys(obj, inspect);
          if ('cause' in obj && !isEnumerable.call(obj, 'cause')) {
              return '{ [' + String(obj) + '] ' + $join.call($concat$1.call('[cause]: ' + inspect(obj.cause), parts), ', ') + ' }';
          }
          if (parts.length === 0) { return '[' + String(obj) + ']'; }
          return '{ [' + String(obj) + '] ' + $join.call(parts, ', ') + ' }';
      }
      if (typeof obj === 'object' && customInspect) {
          if (inspectSymbol && typeof obj[inspectSymbol] === 'function') {
              return obj[inspectSymbol]();
          } else if (customInspect !== 'symbol' && typeof obj.inspect === 'function') {
              return obj.inspect();
          }
      }
      if (isMap(obj)) {
          var mapParts = [];
          mapForEach.call(obj, function (value, key) {
              mapParts.push(inspect(key, obj, true) + ' => ' + inspect(value, obj));
          });
          return collectionOf('Map', mapSize.call(obj), mapParts, indent);
      }
      if (isSet(obj)) {
          var setParts = [];
          setForEach.call(obj, function (value) {
              setParts.push(inspect(value, obj));
          });
          return collectionOf('Set', setSize.call(obj), setParts, indent);
      }
      if (isWeakMap(obj)) {
          return weakCollectionOf('WeakMap');
      }
      if (isWeakSet(obj)) {
          return weakCollectionOf('WeakSet');
      }
      if (isWeakRef(obj)) {
          return weakCollectionOf('WeakRef');
      }
      if (isNumber$1(obj)) {
          return markBoxed(inspect(Number(obj)));
      }
      if (isBigInt(obj)) {
          return markBoxed(inspect(bigIntValueOf.call(obj)));
      }
      if (isBoolean(obj)) {
          return markBoxed(booleanValueOf.call(obj));
      }
      if (isString$1(obj)) {
          return markBoxed(inspect(String(obj)));
      }
      if (!isDate$1(obj) && !isRegExp(obj)) {
          var ys = arrObjKeys(obj, inspect);
          var isPlainObject = gPO ? gPO(obj) === Object.prototype : obj instanceof Object || obj.constructor === Object;
          var protoTag = obj instanceof Object ? '' : 'null prototype';
          var stringTag = !isPlainObject && toStringTag && Object(obj) === obj && toStringTag in obj ? $slice.call(toStr$1(obj), 8, -1) : protoTag ? 'Object' : '';
          var constructorTag = isPlainObject || typeof obj.constructor !== 'function' ? '' : obj.constructor.name ? obj.constructor.name + ' ' : '';
          var tag = constructorTag + (stringTag || protoTag ? '[' + $join.call($concat$1.call([], stringTag || [], protoTag || []), ': ') + '] ' : '');
          if (ys.length === 0) { return tag + '{}'; }
          if (indent) {
              return tag + '{' + indentedJoin(ys, indent) + '}';
          }
          return tag + '{ ' + $join.call(ys, ', ') + ' }';
      }
      return String(obj);
  };

  function wrapQuotes(s, defaultStyle, opts) {
      var quoteChar = (opts.quoteStyle || defaultStyle) === 'double' ? '"' : "'";
      return quoteChar + s + quoteChar;
  }

  function quote(s) {
      return $replace$1.call(String(s), /"/g, '&quot;');
  }

  function isArray$2(obj) { return toStr$1(obj) === '[object Array]' && (!toStringTag || !(typeof obj === 'object' && toStringTag in obj)); }
  function isDate$1(obj) { return toStr$1(obj) === '[object Date]' && (!toStringTag || !(typeof obj === 'object' && toStringTag in obj)); }
  function isRegExp(obj) { return toStr$1(obj) === '[object RegExp]' && (!toStringTag || !(typeof obj === 'object' && toStringTag in obj)); }
  function isError(obj) { return toStr$1(obj) === '[object Error]' && (!toStringTag || !(typeof obj === 'object' && toStringTag in obj)); }
  function isString$1(obj) { return toStr$1(obj) === '[object String]' && (!toStringTag || !(typeof obj === 'object' && toStringTag in obj)); }
  function isNumber$1(obj) { return toStr$1(obj) === '[object Number]' && (!toStringTag || !(typeof obj === 'object' && toStringTag in obj)); }
  function isBoolean(obj) { return toStr$1(obj) === '[object Boolean]' && (!toStringTag || !(typeof obj === 'object' && toStringTag in obj)); }

  // Symbol and BigInt do have Symbol.toStringTag by spec, so that can't be used to eliminate false positives
  function isSymbol$1(obj) {
      if (hasShammedSymbols) {
          return obj && typeof obj === 'object' && obj instanceof Symbol;
      }
      if (typeof obj === 'symbol') {
          return true;
      }
      if (!obj || typeof obj !== 'object' || !symToString) {
          return false;
      }
      try {
          symToString.call(obj);
          return true;
      } catch (e) {}
      return false;
  }

  function isBigInt(obj) {
      if (!obj || typeof obj !== 'object' || !bigIntValueOf) {
          return false;
      }
      try {
          bigIntValueOf.call(obj);
          return true;
      } catch (e) {}
      return false;
  }

  var hasOwn = Object.prototype.hasOwnProperty || function (key) { return key in this; };
  function has(obj, key) {
      return hasOwn.call(obj, key);
  }

  function toStr$1(obj) {
      return objectToString$1.call(obj);
  }

  function nameOf(f) {
      if (f.name) { return f.name; }
      var m = $match.call(functionToString.call(f), /^function\s*([\w$]+)/);
      if (m) { return m[1]; }
      return null;
  }

  function indexOf(xs, x) {
      if (xs.indexOf) { return xs.indexOf(x); }
      for (var i = 0, l = xs.length; i < l; i++) {
          if (xs[i] === x) { return i; }
      }
      return -1;
  }

  function isMap(x) {
      if (!mapSize || !x || typeof x !== 'object') {
          return false;
      }
      try {
          mapSize.call(x);
          try {
              setSize.call(x);
          } catch (s) {
              return true;
          }
          return x instanceof Map; // core-js workaround, pre-v2.5.0
      } catch (e) {}
      return false;
  }

  function isWeakMap(x) {
      if (!weakMapHas || !x || typeof x !== 'object') {
          return false;
      }
      try {
          weakMapHas.call(x, weakMapHas);
          try {
              weakSetHas.call(x, weakSetHas);
          } catch (s) {
              return true;
          }
          return x instanceof WeakMap; // core-js workaround, pre-v2.5.0
      } catch (e) {}
      return false;
  }

  function isWeakRef(x) {
      if (!weakRefDeref || !x || typeof x !== 'object') {
          return false;
      }
      try {
          weakRefDeref.call(x);
          return true;
      } catch (e) {}
      return false;
  }

  function isSet(x) {
      if (!setSize || !x || typeof x !== 'object') {
          return false;
      }
      try {
          setSize.call(x);
          try {
              mapSize.call(x);
          } catch (m) {
              return true;
          }
          return x instanceof Set; // core-js workaround, pre-v2.5.0
      } catch (e) {}
      return false;
  }

  function isWeakSet(x) {
      if (!weakSetHas || !x || typeof x !== 'object') {
          return false;
      }
      try {
          weakSetHas.call(x, weakSetHas);
          try {
              weakMapHas.call(x, weakMapHas);
          } catch (s) {
              return true;
          }
          return x instanceof WeakSet; // core-js workaround, pre-v2.5.0
      } catch (e) {}
      return false;
  }

  function isElement(x) {
      if (!x || typeof x !== 'object') { return false; }
      if (typeof HTMLElement !== 'undefined' && x instanceof HTMLElement) {
          return true;
      }
      return typeof x.nodeName === 'string' && typeof x.getAttribute === 'function';
  }

  function inspectString(str, opts) {
      if (str.length > opts.maxStringLength) {
          var remaining = str.length - opts.maxStringLength;
          var trailer = '... ' + remaining + ' more character' + (remaining > 1 ? 's' : '');
          return inspectString($slice.call(str, 0, opts.maxStringLength), opts) + trailer;
      }
      // eslint-disable-next-line no-control-regex
      var s = $replace$1.call($replace$1.call(str, /(['\\])/g, '\\$1'), /[\x00-\x1f]/g, lowbyte);
      return wrapQuotes(s, 'single', opts);
  }

  function lowbyte(c) {
      var n = c.charCodeAt(0);
      var x = {
          8: 'b',
          9: 't',
          10: 'n',
          12: 'f',
          13: 'r'
      }[n];
      if (x) { return '\\' + x; }
      return '\\x' + (n < 0x10 ? '0' : '') + $toUpperCase.call(n.toString(16));
  }

  function markBoxed(str) {
      return 'Object(' + str + ')';
  }

  function weakCollectionOf(type) {
      return type + ' { ? }';
  }

  function collectionOf(type, size, entries, indent) {
      var joinedEntries = indent ? indentedJoin(entries, indent) : $join.call(entries, ', ');
      return type + ' (' + size + ') {' + joinedEntries + '}';
  }

  function singleLineValues(xs) {
      for (var i = 0; i < xs.length; i++) {
          if (indexOf(xs[i], '\n') >= 0) {
              return false;
          }
      }
      return true;
  }

  function getIndent(opts, depth) {
      var baseIndent;
      if (opts.indent === '\t') {
          baseIndent = '\t';
      } else if (typeof opts.indent === 'number' && opts.indent > 0) {
          baseIndent = $join.call(Array(opts.indent + 1), ' ');
      } else {
          return null;
      }
      return {
          base: baseIndent,
          prev: $join.call(Array(depth + 1), baseIndent)
      };
  }

  function indentedJoin(xs, indent) {
      if (xs.length === 0) { return ''; }
      var lineJoiner = '\n' + indent.prev + indent.base;
      return lineJoiner + $join.call(xs, ',' + lineJoiner) + '\n' + indent.prev;
  }

  function arrObjKeys(obj, inspect) {
      var isArr = isArray$2(obj);
      var xs = [];
      if (isArr) {
          xs.length = obj.length;
          for (var i = 0; i < obj.length; i++) {
              xs[i] = has(obj, i) ? inspect(obj[i], obj) : '';
          }
      }
      var syms = typeof gOPS === 'function' ? gOPS(obj) : [];
      var symMap;
      if (hasShammedSymbols) {
          symMap = {};
          for (var k = 0; k < syms.length; k++) {
              symMap['$' + syms[k]] = syms[k];
          }
      }

      for (var key in obj) { // eslint-disable-line no-restricted-syntax
          if (!has(obj, key)) { continue; } // eslint-disable-line no-restricted-syntax, no-continue
          if (isArr && String(Number(key)) === key && key < obj.length) { continue; } // eslint-disable-line no-restricted-syntax, no-continue
          if (hasShammedSymbols && symMap['$' + key] instanceof Symbol) {
              // this is to prevent shammed Symbols, which are stored as strings, from being included in the string key section
              continue; // eslint-disable-line no-restricted-syntax, no-continue
          } else if ($test.call(/[^\w$]/, key)) {
              xs.push(inspect(key, obj) + ': ' + inspect(obj[key], obj));
          } else {
              xs.push(key + ': ' + inspect(obj[key], obj));
          }
      }
      if (typeof gOPS === 'function') {
          for (var j = 0; j < syms.length; j++) {
              if (isEnumerable.call(obj, syms[j])) {
                  xs.push('[' + inspect(syms[j]) + ']: ' + inspect(obj[syms[j]], obj));
              }
          }
      }
      return xs;
  }

  var $TypeError$1 = getIntrinsic('%TypeError%');
  var $WeakMap = getIntrinsic('%WeakMap%', true);
  var $Map = getIntrinsic('%Map%', true);

  var $weakMapGet = callBound('WeakMap.prototype.get', true);
  var $weakMapSet = callBound('WeakMap.prototype.set', true);
  var $weakMapHas = callBound('WeakMap.prototype.has', true);
  var $mapGet = callBound('Map.prototype.get', true);
  var $mapSet = callBound('Map.prototype.set', true);
  var $mapHas = callBound('Map.prototype.has', true);

  /*
   * This function traverses the list returning the node corresponding to the
   * given key.
   *
   * That node is also moved to the head of the list, so that if it's accessed
   * again we don't need to traverse the whole list. By doing so, all the recently
   * used nodes can be accessed relatively quickly.
   */
  var listGetNode = function (list, key) { // eslint-disable-line consistent-return
  	for (var prev = list, curr; (curr = prev.next) !== null; prev = curr) {
  		if (curr.key === key) {
  			prev.next = curr.next;
  			curr.next = list.next;
  			list.next = curr; // eslint-disable-line no-param-reassign
  			return curr;
  		}
  	}
  };

  var listGet = function (objects, key) {
  	var node = listGetNode(objects, key);
  	return node && node.value;
  };
  var listSet = function (objects, key, value) {
  	var node = listGetNode(objects, key);
  	if (node) {
  		node.value = value;
  	} else {
  		// Prepend the new node to the beginning of the list
  		objects.next = { // eslint-disable-line no-param-reassign
  			key: key,
  			next: objects.next,
  			value: value
  		};
  	}
  };
  var listHas = function (objects, key) {
  	return !!listGetNode(objects, key);
  };

  var sideChannel = function getSideChannel() {
  	var $wm;
  	var $m;
  	var $o;
  	var channel = {
  		assert: function (key) {
  			if (!channel.has(key)) {
  				throw new $TypeError$1('Side channel does not contain ' + objectInspect(key));
  			}
  		},
  		get: function (key) { // eslint-disable-line consistent-return
  			if ($WeakMap && key && (typeof key === 'object' || typeof key === 'function')) {
  				if ($wm) {
  					return $weakMapGet($wm, key);
  				}
  			} else if ($Map) {
  				if ($m) {
  					return $mapGet($m, key);
  				}
  			} else {
  				if ($o) { // eslint-disable-line no-lonely-if
  					return listGet($o, key);
  				}
  			}
  		},
  		has: function (key) {
  			if ($WeakMap && key && (typeof key === 'object' || typeof key === 'function')) {
  				if ($wm) {
  					return $weakMapHas($wm, key);
  				}
  			} else if ($Map) {
  				if ($m) {
  					return $mapHas($m, key);
  				}
  			} else {
  				if ($o) { // eslint-disable-line no-lonely-if
  					return listHas($o, key);
  				}
  			}
  			return false;
  		},
  		set: function (key, value) {
  			if ($WeakMap && key && (typeof key === 'object' || typeof key === 'function')) {
  				if (!$wm) {
  					$wm = new $WeakMap();
  				}
  				$weakMapSet($wm, key, value);
  			} else if ($Map) {
  				if (!$m) {
  					$m = new $Map();
  				}
  				$mapSet($m, key, value);
  			} else {
  				if (!$o) {
  					/*
  					 * Initialize the linked list as an empty node, so that we don't have
  					 * to special-case handling of the first node: we can always refer to
  					 * it as (previous node).next, instead of something like (list).head
  					 */
  					$o = { key: {}, next: null };
  				}
  				listSet($o, key, value);
  			}
  		}
  	};
  	return channel;
  };

  var replace = String.prototype.replace;
  var percentTwenties = /%20/g;

  var Format = {
      RFC1738: 'RFC1738',
      RFC3986: 'RFC3986'
  };

  var formats = {
      'default': Format.RFC3986,
      formatters: {
          RFC1738: function (value) {
              return replace.call(value, percentTwenties, '+');
          },
          RFC3986: function (value) {
              return String(value);
          }
      },
      RFC1738: Format.RFC1738,
      RFC3986: Format.RFC3986
  };

  var has$1 = Object.prototype.hasOwnProperty;
  var isArray$3 = Array.isArray;

  var hexTable = (function () {
      var array = [];
      for (var i = 0; i < 256; ++i) {
          array.push('%' + ((i < 16 ? '0' : '') + i.toString(16)).toUpperCase());
      }

      return array;
  }());

  var compactQueue = function compactQueue(queue) {
      while (queue.length > 1) {
          var item = queue.pop();
          var obj = item.obj[item.prop];

          if (isArray$3(obj)) {
              var compacted = [];

              for (var j = 0; j < obj.length; ++j) {
                  if (typeof obj[j] !== 'undefined') {
                      compacted.push(obj[j]);
                  }
              }

              item.obj[item.prop] = compacted;
          }
      }
  };

  var arrayToObject = function arrayToObject(source, options) {
      var obj = options && options.plainObjects ? Object.create(null) : {};
      for (var i = 0; i < source.length; ++i) {
          if (typeof source[i] !== 'undefined') {
              obj[i] = source[i];
          }
      }

      return obj;
  };

  var merge$1 = function merge(target, source, options) {
      /* eslint no-param-reassign: 0 */
      if (!source) {
          return target;
      }

      if (typeof source !== 'object') {
          if (isArray$3(target)) {
              target.push(source);
          } else if (target && typeof target === 'object') {
              if ((options && (options.plainObjects || options.allowPrototypes)) || !has$1.call(Object.prototype, source)) {
                  target[source] = true;
              }
          } else {
              return [target, source];
          }

          return target;
      }

      if (!target || typeof target !== 'object') {
          return [target].concat(source);
      }

      var mergeTarget = target;
      if (isArray$3(target) && !isArray$3(source)) {
          mergeTarget = arrayToObject(target, options);
      }

      if (isArray$3(target) && isArray$3(source)) {
          source.forEach(function (item, i) {
              if (has$1.call(target, i)) {
                  var targetItem = target[i];
                  if (targetItem && typeof targetItem === 'object' && item && typeof item === 'object') {
                      target[i] = merge(targetItem, item, options);
                  } else {
                      target.push(item);
                  }
              } else {
                  target[i] = item;
              }
          });
          return target;
      }

      return Object.keys(source).reduce(function (acc, key) {
          var value = source[key];

          if (has$1.call(acc, key)) {
              acc[key] = merge(acc[key], value, options);
          } else {
              acc[key] = value;
          }
          return acc;
      }, mergeTarget);
  };

  var assign = function assignSingleSource(target, source) {
      return Object.keys(source).reduce(function (acc, key) {
          acc[key] = source[key];
          return acc;
      }, target);
  };

  var decode = function (str, decoder, charset) {
      var strWithoutPlus = str.replace(/\+/g, ' ');
      if (charset === 'iso-8859-1') {
          // unescape never throws, no try...catch needed:
          return strWithoutPlus.replace(/%[0-9a-f]{2}/gi, unescape);
      }
      // utf-8
      try {
          return decodeURIComponent(strWithoutPlus);
      } catch (e) {
          return strWithoutPlus;
      }
  };

  var encode$1 = function encode(str, defaultEncoder, charset, kind, format) {
      // This code was originally written by Brian White (mscdex) for the io.js core querystring library.
      // It has been adapted here for stricter adherence to RFC 3986
      if (str.length === 0) {
          return str;
      }

      var string = str;
      if (typeof str === 'symbol') {
          string = Symbol.prototype.toString.call(str);
      } else if (typeof str !== 'string') {
          string = String(str);
      }

      if (charset === 'iso-8859-1') {
          return escape(string).replace(/%u[0-9a-f]{4}/gi, function ($0) {
              return '%26%23' + parseInt($0.slice(2), 16) + '%3B';
          });
      }

      var out = '';
      for (var i = 0; i < string.length; ++i) {
          var c = string.charCodeAt(i);

          if (
              c === 0x2D // -
              || c === 0x2E // .
              || c === 0x5F // _
              || c === 0x7E // ~
              || (c >= 0x30 && c <= 0x39) // 0-9
              || (c >= 0x41 && c <= 0x5A) // a-z
              || (c >= 0x61 && c <= 0x7A) // A-Z
              || (format === formats.RFC1738 && (c === 0x28 || c === 0x29)) // ( )
          ) {
              out += string.charAt(i);
              continue;
          }

          if (c < 0x80) {
              out = out + hexTable[c];
              continue;
          }

          if (c < 0x800) {
              out = out + (hexTable[0xC0 | (c >> 6)] + hexTable[0x80 | (c & 0x3F)]);
              continue;
          }

          if (c < 0xD800 || c >= 0xE000) {
              out = out + (hexTable[0xE0 | (c >> 12)] + hexTable[0x80 | ((c >> 6) & 0x3F)] + hexTable[0x80 | (c & 0x3F)]);
              continue;
          }

          i += 1;
          c = 0x10000 + (((c & 0x3FF) << 10) | (string.charCodeAt(i) & 0x3FF));
          /* eslint operator-linebreak: [2, "before"] */
          out += hexTable[0xF0 | (c >> 18)]
              + hexTable[0x80 | ((c >> 12) & 0x3F)]
              + hexTable[0x80 | ((c >> 6) & 0x3F)]
              + hexTable[0x80 | (c & 0x3F)];
      }

      return out;
  };

  var compact = function compact(value) {
      var queue = [{ obj: { o: value }, prop: 'o' }];
      var refs = [];

      for (var i = 0; i < queue.length; ++i) {
          var item = queue[i];
          var obj = item.obj[item.prop];

          var keys = Object.keys(obj);
          for (var j = 0; j < keys.length; ++j) {
              var key = keys[j];
              var val = obj[key];
              if (typeof val === 'object' && val !== null && refs.indexOf(val) === -1) {
                  queue.push({ obj: obj, prop: key });
                  refs.push(val);
              }
          }
      }

      compactQueue(queue);

      return value;
  };

  var isRegExp$1 = function isRegExp(obj) {
      return Object.prototype.toString.call(obj) === '[object RegExp]';
  };

  var isBuffer$1 = function isBuffer(obj) {
      if (!obj || typeof obj !== 'object') {
          return false;
      }

      return !!(obj.constructor && obj.constructor.isBuffer && obj.constructor.isBuffer(obj));
  };

  var combine = function combine(a, b) {
      return [].concat(a, b);
  };

  var maybeMap = function maybeMap(val, fn) {
      if (isArray$3(val)) {
          var mapped = [];
          for (var i = 0; i < val.length; i += 1) {
              mapped.push(fn(val[i]));
          }
          return mapped;
      }
      return fn(val);
  };

  var utils$1 = {
      arrayToObject: arrayToObject,
      assign: assign,
      combine: combine,
      compact: compact,
      decode: decode,
      encode: encode$1,
      isBuffer: isBuffer$1,
      isRegExp: isRegExp$1,
      maybeMap: maybeMap,
      merge: merge$1
  };

  var has$2 = Object.prototype.hasOwnProperty;

  var arrayPrefixGenerators = {
      brackets: function brackets(prefix) {
          return prefix + '[]';
      },
      comma: 'comma',
      indices: function indices(prefix, key) {
          return prefix + '[' + key + ']';
      },
      repeat: function repeat(prefix) {
          return prefix;
      }
  };

  var isArray$4 = Array.isArray;
  var push = Array.prototype.push;
  var pushToArray = function (arr, valueOrArray) {
      push.apply(arr, isArray$4(valueOrArray) ? valueOrArray : [valueOrArray]);
  };

  var toISO = Date.prototype.toISOString;

  var defaultFormat = formats['default'];
  var defaults$1 = {
      addQueryPrefix: false,
      allowDots: false,
      charset: 'utf-8',
      charsetSentinel: false,
      delimiter: '&',
      encode: true,
      encoder: utils$1.encode,
      encodeValuesOnly: false,
      format: defaultFormat,
      formatter: formats.formatters[defaultFormat],
      // deprecated
      indices: false,
      serializeDate: function serializeDate(date) {
          return toISO.call(date);
      },
      skipNulls: false,
      strictNullHandling: false
  };

  var isNonNullishPrimitive = function isNonNullishPrimitive(v) {
      return typeof v === 'string'
          || typeof v === 'number'
          || typeof v === 'boolean'
          || typeof v === 'symbol'
          || typeof v === 'bigint';
  };

  var sentinel = {};

  var stringify = function stringify(
      object,
      prefix,
      generateArrayPrefix,
      commaRoundTrip,
      strictNullHandling,
      skipNulls,
      encoder,
      filter,
      sort,
      allowDots,
      serializeDate,
      format,
      formatter,
      encodeValuesOnly,
      charset,
      sideChannel$1
  ) {
      var obj = object;

      var tmpSc = sideChannel$1;
      var step = 0;
      var findFlag = false;
      while ((tmpSc = tmpSc.get(sentinel)) !== void undefined && !findFlag) {
          // Where object last appeared in the ref tree
          var pos = tmpSc.get(object);
          step += 1;
          if (typeof pos !== 'undefined') {
              if (pos === step) {
                  throw new RangeError('Cyclic object value');
              } else {
                  findFlag = true; // Break while
              }
          }
          if (typeof tmpSc.get(sentinel) === 'undefined') {
              step = 0;
          }
      }

      if (typeof filter === 'function') {
          obj = filter(prefix, obj);
      } else if (obj instanceof Date) {
          obj = serializeDate(obj);
      } else if (generateArrayPrefix === 'comma' && isArray$4(obj)) {
          obj = utils$1.maybeMap(obj, function (value) {
              if (value instanceof Date) {
                  return serializeDate(value);
              }
              return value;
          });
      }

      if (obj === null) {
          if (strictNullHandling) {
              return encoder && !encodeValuesOnly ? encoder(prefix, defaults$1.encoder, charset, 'key', format) : prefix;
          }

          obj = '';
      }

      if (isNonNullishPrimitive(obj) || utils$1.isBuffer(obj)) {
          if (encoder) {
              var keyValue = encodeValuesOnly ? prefix : encoder(prefix, defaults$1.encoder, charset, 'key', format);
              return [formatter(keyValue) + '=' + formatter(encoder(obj, defaults$1.encoder, charset, 'value', format))];
          }
          return [formatter(prefix) + '=' + formatter(String(obj))];
      }

      var values = [];

      if (typeof obj === 'undefined') {
          return values;
      }

      var objKeys;
      if (generateArrayPrefix === 'comma' && isArray$4(obj)) {
          // we need to join elements in
          if (encodeValuesOnly && encoder) {
              obj = utils$1.maybeMap(obj, encoder);
          }
          objKeys = [{ value: obj.length > 0 ? obj.join(',') || null : void undefined }];
      } else if (isArray$4(filter)) {
          objKeys = filter;
      } else {
          var keys = Object.keys(obj);
          objKeys = sort ? keys.sort(sort) : keys;
      }

      var adjustedPrefix = commaRoundTrip && isArray$4(obj) && obj.length === 1 ? prefix + '[]' : prefix;

      for (var j = 0; j < objKeys.length; ++j) {
          var key = objKeys[j];
          var value = typeof key === 'object' && typeof key.value !== 'undefined' ? key.value : obj[key];

          if (skipNulls && value === null) {
              continue;
          }

          var keyPrefix = isArray$4(obj)
              ? typeof generateArrayPrefix === 'function' ? generateArrayPrefix(adjustedPrefix, key) : adjustedPrefix
              : adjustedPrefix + (allowDots ? '.' + key : '[' + key + ']');

          sideChannel$1.set(object, step);
          var valueSideChannel = sideChannel();
          valueSideChannel.set(sentinel, sideChannel$1);
          pushToArray(values, stringify(
              value,
              keyPrefix,
              generateArrayPrefix,
              commaRoundTrip,
              strictNullHandling,
              skipNulls,
              generateArrayPrefix === 'comma' && encodeValuesOnly && isArray$4(obj) ? null : encoder,
              filter,
              sort,
              allowDots,
              serializeDate,
              format,
              formatter,
              encodeValuesOnly,
              charset,
              valueSideChannel
          ));
      }

      return values;
  };

  var normalizeStringifyOptions = function normalizeStringifyOptions(opts) {
      if (!opts) {
          return defaults$1;
      }

      if (opts.encoder !== null && typeof opts.encoder !== 'undefined' && typeof opts.encoder !== 'function') {
          throw new TypeError('Encoder has to be a function.');
      }

      var charset = opts.charset || defaults$1.charset;
      if (typeof opts.charset !== 'undefined' && opts.charset !== 'utf-8' && opts.charset !== 'iso-8859-1') {
          throw new TypeError('The charset option must be either utf-8, iso-8859-1, or undefined');
      }

      var format = formats['default'];
      if (typeof opts.format !== 'undefined') {
          if (!has$2.call(formats.formatters, opts.format)) {
              throw new TypeError('Unknown format option provided.');
          }
          format = opts.format;
      }
      var formatter = formats.formatters[format];

      var filter = defaults$1.filter;
      if (typeof opts.filter === 'function' || isArray$4(opts.filter)) {
          filter = opts.filter;
      }

      return {
          addQueryPrefix: typeof opts.addQueryPrefix === 'boolean' ? opts.addQueryPrefix : defaults$1.addQueryPrefix,
          allowDots: typeof opts.allowDots === 'undefined' ? defaults$1.allowDots : !!opts.allowDots,
          charset: charset,
          charsetSentinel: typeof opts.charsetSentinel === 'boolean' ? opts.charsetSentinel : defaults$1.charsetSentinel,
          delimiter: typeof opts.delimiter === 'undefined' ? defaults$1.delimiter : opts.delimiter,
          encode: typeof opts.encode === 'boolean' ? opts.encode : defaults$1.encode,
          encoder: typeof opts.encoder === 'function' ? opts.encoder : defaults$1.encoder,
          encodeValuesOnly: typeof opts.encodeValuesOnly === 'boolean' ? opts.encodeValuesOnly : defaults$1.encodeValuesOnly,
          filter: filter,
          format: format,
          formatter: formatter,
          serializeDate: typeof opts.serializeDate === 'function' ? opts.serializeDate : defaults$1.serializeDate,
          skipNulls: typeof opts.skipNulls === 'boolean' ? opts.skipNulls : defaults$1.skipNulls,
          sort: typeof opts.sort === 'function' ? opts.sort : null,
          strictNullHandling: typeof opts.strictNullHandling === 'boolean' ? opts.strictNullHandling : defaults$1.strictNullHandling
      };
  };

  var stringify_1 = function (object, opts) {
      var obj = object;
      var options = normalizeStringifyOptions(opts);

      var objKeys;
      var filter;

      if (typeof options.filter === 'function') {
          filter = options.filter;
          obj = filter('', obj);
      } else if (isArray$4(options.filter)) {
          filter = options.filter;
          objKeys = filter;
      }

      var keys = [];

      if (typeof obj !== 'object' || obj === null) {
          return '';
      }

      var arrayFormat;
      if (opts && opts.arrayFormat in arrayPrefixGenerators) {
          arrayFormat = opts.arrayFormat;
      } else if (opts && 'indices' in opts) {
          arrayFormat = opts.indices ? 'indices' : 'repeat';
      } else {
          arrayFormat = 'indices';
      }

      var generateArrayPrefix = arrayPrefixGenerators[arrayFormat];
      if (opts && 'commaRoundTrip' in opts && typeof opts.commaRoundTrip !== 'boolean') {
          throw new TypeError('`commaRoundTrip` must be a boolean, or absent');
      }
      var commaRoundTrip = generateArrayPrefix === 'comma' && opts && opts.commaRoundTrip;

      if (!objKeys) {
          objKeys = Object.keys(obj);
      }

      if (options.sort) {
          objKeys.sort(options.sort);
      }

      var sideChannel$1 = sideChannel();
      for (var i = 0; i < objKeys.length; ++i) {
          var key = objKeys[i];

          if (options.skipNulls && obj[key] === null) {
              continue;
          }
          pushToArray(keys, stringify(
              obj[key],
              key,
              generateArrayPrefix,
              commaRoundTrip,
              options.strictNullHandling,
              options.skipNulls,
              options.encode ? options.encoder : null,
              options.filter,
              options.sort,
              options.allowDots,
              options.serializeDate,
              options.format,
              options.formatter,
              options.encodeValuesOnly,
              options.charset,
              sideChannel$1
          ));
      }

      var joined = keys.join(options.delimiter);
      var prefix = options.addQueryPrefix === true ? '?' : '';

      if (options.charsetSentinel) {
          if (options.charset === 'iso-8859-1') {
              // encodeURIComponent('&#10003;'), the "numeric entity" representation of a checkmark
              prefix += 'utf8=%26%2310003%3B&';
          } else {
              // encodeURIComponent('✓')
              prefix += 'utf8=%E2%9C%93&';
          }
      }

      return joined.length > 0 ? prefix + joined : '';
  };

  var has$3 = Object.prototype.hasOwnProperty;
  var isArray$5 = Array.isArray;

  var defaults$2 = {
      allowDots: false,
      allowPrototypes: false,
      allowSparse: false,
      arrayLimit: 20,
      charset: 'utf-8',
      charsetSentinel: false,
      comma: false,
      decoder: utils$1.decode,
      delimiter: '&',
      depth: 5,
      ignoreQueryPrefix: false,
      interpretNumericEntities: false,
      parameterLimit: 1000,
      parseArrays: true,
      plainObjects: false,
      strictNullHandling: false
  };

  var interpretNumericEntities = function (str) {
      return str.replace(/&#(\d+);/g, function ($0, numberStr) {
          return String.fromCharCode(parseInt(numberStr, 10));
      });
  };

  var parseArrayValue = function (val, options) {
      if (val && typeof val === 'string' && options.comma && val.indexOf(',') > -1) {
          return val.split(',');
      }

      return val;
  };

  // This is what browsers will submit when the ✓ character occurs in an
  // application/x-www-form-urlencoded body and the encoding of the page containing
  // the form is iso-8859-1, or when the submitted form has an accept-charset
  // attribute of iso-8859-1. Presumably also with other charsets that do not contain
  // the ✓ character, such as us-ascii.
  var isoSentinel = 'utf8=%26%2310003%3B'; // encodeURIComponent('&#10003;')

  // These are the percent-encoded utf-8 octets representing a checkmark, indicating that the request actually is utf-8 encoded.
  var charsetSentinel = 'utf8=%E2%9C%93'; // encodeURIComponent('✓')

  var parseValues = function parseQueryStringValues(str, options) {
      var obj = { __proto__: null };

      var cleanStr = options.ignoreQueryPrefix ? str.replace(/^\?/, '') : str;
      var limit = options.parameterLimit === Infinity ? undefined : options.parameterLimit;
      var parts = cleanStr.split(options.delimiter, limit);
      var skipIndex = -1; // Keep track of where the utf8 sentinel was found
      var i;

      var charset = options.charset;
      if (options.charsetSentinel) {
          for (i = 0; i < parts.length; ++i) {
              if (parts[i].indexOf('utf8=') === 0) {
                  if (parts[i] === charsetSentinel) {
                      charset = 'utf-8';
                  } else if (parts[i] === isoSentinel) {
                      charset = 'iso-8859-1';
                  }
                  skipIndex = i;
                  i = parts.length; // The eslint settings do not allow break;
              }
          }
      }

      for (i = 0; i < parts.length; ++i) {
          if (i === skipIndex) {
              continue;
          }
          var part = parts[i];

          var bracketEqualsPos = part.indexOf(']=');
          var pos = bracketEqualsPos === -1 ? part.indexOf('=') : bracketEqualsPos + 1;

          var key, val;
          if (pos === -1) {
              key = options.decoder(part, defaults$2.decoder, charset, 'key');
              val = options.strictNullHandling ? null : '';
          } else {
              key = options.decoder(part.slice(0, pos), defaults$2.decoder, charset, 'key');
              val = utils$1.maybeMap(
                  parseArrayValue(part.slice(pos + 1), options),
                  function (encodedVal) {
                      return options.decoder(encodedVal, defaults$2.decoder, charset, 'value');
                  }
              );
          }

          if (val && options.interpretNumericEntities && charset === 'iso-8859-1') {
              val = interpretNumericEntities(val);
          }

          if (part.indexOf('[]=') > -1) {
              val = isArray$5(val) ? [val] : val;
          }

          if (has$3.call(obj, key)) {
              obj[key] = utils$1.combine(obj[key], val);
          } else {
              obj[key] = val;
          }
      }

      return obj;
  };

  var parseObject = function (chain, val, options, valuesParsed) {
      var leaf = valuesParsed ? val : parseArrayValue(val, options);

      for (var i = chain.length - 1; i >= 0; --i) {
          var obj;
          var root = chain[i];

          if (root === '[]' && options.parseArrays) {
              obj = [].concat(leaf);
          } else {
              obj = options.plainObjects ? Object.create(null) : {};
              var cleanRoot = root.charAt(0) === '[' && root.charAt(root.length - 1) === ']' ? root.slice(1, -1) : root;
              var index = parseInt(cleanRoot, 10);
              if (!options.parseArrays && cleanRoot === '') {
                  obj = { 0: leaf };
              } else if (
                  !isNaN(index)
                  && root !== cleanRoot
                  && String(index) === cleanRoot
                  && index >= 0
                  && (options.parseArrays && index <= options.arrayLimit)
              ) {
                  obj = [];
                  obj[index] = leaf;
              } else if (cleanRoot !== '__proto__') {
                  obj[cleanRoot] = leaf;
              }
          }

          leaf = obj;
      }

      return leaf;
  };

  var parseKeys = function parseQueryStringKeys(givenKey, val, options, valuesParsed) {
      if (!givenKey) {
          return;
      }

      // Transform dot notation to bracket notation
      var key = options.allowDots ? givenKey.replace(/\.([^.[]+)/g, '[$1]') : givenKey;

      // The regex chunks

      var brackets = /(\[[^[\]]*])/;
      var child = /(\[[^[\]]*])/g;

      // Get the parent

      var segment = options.depth > 0 && brackets.exec(key);
      var parent = segment ? key.slice(0, segment.index) : key;

      // Stash the parent if it exists

      var keys = [];
      if (parent) {
          // If we aren't using plain objects, optionally prefix keys that would overwrite object prototype properties
          if (!options.plainObjects && has$3.call(Object.prototype, parent)) {
              if (!options.allowPrototypes) {
                  return;
              }
          }

          keys.push(parent);
      }

      // Loop through children appending to the array until we hit depth

      var i = 0;
      while (options.depth > 0 && (segment = child.exec(key)) !== null && i < options.depth) {
          i += 1;
          if (!options.plainObjects && has$3.call(Object.prototype, segment[1].slice(1, -1))) {
              if (!options.allowPrototypes) {
                  return;
              }
          }
          keys.push(segment[1]);
      }

      // If there's a remainder, just add whatever is left

      if (segment) {
          keys.push('[' + key.slice(segment.index) + ']');
      }

      return parseObject(keys, val, options, valuesParsed);
  };

  var normalizeParseOptions = function normalizeParseOptions(opts) {
      if (!opts) {
          return defaults$2;
      }

      if (opts.decoder !== null && opts.decoder !== undefined && typeof opts.decoder !== 'function') {
          throw new TypeError('Decoder has to be a function.');
      }

      if (typeof opts.charset !== 'undefined' && opts.charset !== 'utf-8' && opts.charset !== 'iso-8859-1') {
          throw new TypeError('The charset option must be either utf-8, iso-8859-1, or undefined');
      }
      var charset = typeof opts.charset === 'undefined' ? defaults$2.charset : opts.charset;

      return {
          allowDots: typeof opts.allowDots === 'undefined' ? defaults$2.allowDots : !!opts.allowDots,
          allowPrototypes: typeof opts.allowPrototypes === 'boolean' ? opts.allowPrototypes : defaults$2.allowPrototypes,
          allowSparse: typeof opts.allowSparse === 'boolean' ? opts.allowSparse : defaults$2.allowSparse,
          arrayLimit: typeof opts.arrayLimit === 'number' ? opts.arrayLimit : defaults$2.arrayLimit,
          charset: charset,
          charsetSentinel: typeof opts.charsetSentinel === 'boolean' ? opts.charsetSentinel : defaults$2.charsetSentinel,
          comma: typeof opts.comma === 'boolean' ? opts.comma : defaults$2.comma,
          decoder: typeof opts.decoder === 'function' ? opts.decoder : defaults$2.decoder,
          delimiter: typeof opts.delimiter === 'string' || utils$1.isRegExp(opts.delimiter) ? opts.delimiter : defaults$2.delimiter,
          // eslint-disable-next-line no-implicit-coercion, no-extra-parens
          depth: (typeof opts.depth === 'number' || opts.depth === false) ? +opts.depth : defaults$2.depth,
          ignoreQueryPrefix: opts.ignoreQueryPrefix === true,
          interpretNumericEntities: typeof opts.interpretNumericEntities === 'boolean' ? opts.interpretNumericEntities : defaults$2.interpretNumericEntities,
          parameterLimit: typeof opts.parameterLimit === 'number' ? opts.parameterLimit : defaults$2.parameterLimit,
          parseArrays: opts.parseArrays !== false,
          plainObjects: typeof opts.plainObjects === 'boolean' ? opts.plainObjects : defaults$2.plainObjects,
          strictNullHandling: typeof opts.strictNullHandling === 'boolean' ? opts.strictNullHandling : defaults$2.strictNullHandling
      };
  };

  var parse = function (str, opts) {
      var options = normalizeParseOptions(opts);

      if (str === '' || str === null || typeof str === 'undefined') {
          return options.plainObjects ? Object.create(null) : {};
      }

      var tempObj = typeof str === 'string' ? parseValues(str, options) : str;
      var obj = options.plainObjects ? Object.create(null) : {};

      // Iterate over the keys and setup the new object

      var keys = Object.keys(tempObj);
      for (var i = 0; i < keys.length; ++i) {
          var key = keys[i];
          var newObj = parseKeys(key, tempObj[key], options, typeof str === 'string');
          obj = utils$1.merge(obj, newObj, options);
      }

      if (options.allowSparse === true) {
          return obj;
      }

      return utils$1.compact(obj);
  };

  var lib = {
      formats: formats,
      parse: parse,
      stringify: stringify_1
  };

  const makeArrayProp = obj => key => {
    if (obj == null || typeof obj !== 'object') {
      return;
    }

    const value = get_1(obj, key);

    if (!Array.isArray(value)) {
      set_1(obj, key, value == null ? [] : [value]);
    }
  };

  const makeConvertProp = convertMethod => {
    const finalMethod = target => {
      if (Array.isArray(target)) {
        return target.map(it => finalMethod(it));
      }

      if (typeof target === 'string') {
        return convertMethod(target);
      }

      if (typeof target === 'object' && target != null) {
        const ret = Object.keys(target).reduce((acc, key) => {
          const nextKey = finalMethod(key);
          acc[nextKey] = target[key];
          return acc;
        }, {});
        return ret;
      }

      return target;
    };

    return finalMethod;
  };

  const covertCamelCase2Kebab = /*#__PURE__*/makeConvertProp(camelCase => {
    return camelCase.replace(/[A-Z]/g, '-$&').toLowerCase();
  });
  const convertNormalCamelCase2Upper = /*#__PURE__*/makeConvertProp(normalCamelCase => {
    return normalCamelCase[0].toUpperCase() + normalCamelCase.slice(1);
  });
  const getSortedQueryString = query => {
    const searchParts = [];
    Object.keys(query).sort().forEach(key => {
      searchParts.push(`${encodeURIComponent(key)}=${encodeURIComponent(query[key])}`);
    });
    return searchParts.join('&');
  };
  const normalizeHeadersKey = headers => {
    const headers1 = headers || {};
    const headers2 = {};
    Object.keys(headers1).forEach(key => {
      if (headers1[key] != null) {
        headers2[key] = headers1[key];
      }
    });
    const headers3 = {};
    Object.keys(headers2).forEach(key => {
      const newKey = key.toLowerCase();
      headers3[newKey] = headers2[key];
    });
    return headers3;
  };
  const encodeHeadersValue = headers => {
    const header2 = {};
    Object.entries(headers).forEach(([key, value]) => {
      header2[key] = `${value}` // reference:
      //  https://stackoverflow.com/questions/38345372/why-is-length-2
      .match(/./gu).map(ch => {
        if (ch.length > 1 || ch.charCodeAt(0) >= 128) {
          return encodeURIComponent(ch);
        }

        return ch;
      }).join('');
    });
    return header2;
  }; // TODO: getRegion from endpoint, maybe user passes it is better.
  const getEndpoint = region => {
    return `tos-${region}.volces.com`;
  };
  const normalizeProxy = proxy => {
    var _proxy;

    if (typeof proxy === 'string') {
      proxy = {
        url: proxy
      };
    }

    if (proxy && ((_proxy = proxy) == null ? void 0 : _proxy.needProxyParams) == null && 'browser' === 'browser') {
      proxy.needProxyParams = true;
    }

    return proxy;
  };
  async function safeAwait(p) {
    try {
      const v = await p;
      return [null, v];
    } catch (err) {
      return [err, null];
    }
  }
  function safeSync(func) {
    try {
      const ret = func();
      return [null, ret];
    } catch (err) {
      return [err, null];
    }
  }
  function isBlob$1(obj) {
    return typeof Blob !== 'undefined' && obj instanceof Blob;
  }
  function isBuffer$2(obj) {
    return typeof Buffer !== 'undefined' && obj instanceof Buffer;
  }
  function obj2QueryStr(v) {
    if (!v) {
      return '';
    }

    return Object.keys(v).map(key => {
      const vStr = `${v[key]}`;
      return `${encodeURIComponent(key)}=${encodeURIComponent(vStr)}`;
    }).join('&');
  }
  function isCancelError(err) {
    return err instanceof CancelError;
  }
  const DEFAULT_PART_SIZE = 20 * 1024 * 1024; // 20 MB

  const getGMTDateStr = v => {
    return v.toUTCString();
  };

  const gmtDateOrStr = v => {
    if (typeof v === 'string') {
      return v;
    }

    return v.toUTCString();
  };

  const requestHeadersMap = {
    projectName: 'x-tos-project-name',
    encodingType: 'encoding-type',
    cacheControl: 'cache-control',
    contentDisposition: 'content-disposition',
    contentLength: 'content-length',
    contentMD5: 'content-md5',
    contentSHA256: 'x-tos-content-sha256',
    contentEncoding: 'content-encoding',
    contentLanguage: 'content-language',
    contentType: 'content-type',
    expires: ['expires', getGMTDateStr],
    range: 'range',
    ifMatch: 'if-match',
    ifModifiedSince: ['if-modified-since', gmtDateOrStr],
    ifNoneMatch: 'if-none-match',
    ifUnmodifiedSince: ['if-unmodified-since', gmtDateOrStr],
    acl: 'x-tos-acl',
    grantFullControl: 'x-tos-grant-full-control',
    grantRead: 'x-tos-grant-read',
    grantReadAcp: 'x-tos-grant-read-acp',
    grantWrite: 'x-tos-grant-write',
    grantWriteAcp: 'x-tos-grant-write-acp',
    serverSideEncryption: 'x-tos-server-side-encryption',
    serverSideDataEncryption: 'x-tos-server-side-data-encryption',
    ssecAlgorithm: 'x-tos-server-side-encryption-customer-algorithm',
    ssecKey: 'x-tos-server-side-encryption-customer-key',
    ssecKeyMD5: 'x-tos-server-side-encryption-customer-key-md5',
    copySourceRange: 'x-tos-copy-source-range',
    copySourceIfMatch: 'x-tos-copy-source-if-match',
    copySourceIfModifiedSince: ['x-tos-copy-source-if-modified-since', gmtDateOrStr],
    copySourceIfNoneMatch: 'x-tos-copy-source-if-none-match',
    copySourceIfUnmodifiedSince: 'x-tos-copy-source-if-unmodified-since',
    copySourceSSECAlgorithm: 'x-tos-copy-source-server-side-encryption-customer-algorithm',
    copySourceSSECKey: 'x-tos-copy-source-server-side-encryption-customer-key',
    copySourceSSECKeyMD5: 'x-tos-copy-source-server-side-encryption-customer-key-MD5',
    metadataDirective: 'x-tos-metadata-directive',
    meta: v => {
      return Object.keys(v).reduce((prev, key) => {
        prev[`x-tos-meta-${key}`] = `${v[key]}`;
        return prev;
      }, {});
    },
    websiteRedirectLocation: 'x-tos-website-redirect-location',
    storageClass: 'x-tos-storage-class',
    azRedundancy: 'x-tos-az-redundancy',
    trafficLimit: 'x-tos-traffic-limit',
    callback: 'x-tos-callback',
    callbackVar: 'x-tos-callback-var',
    allowSameActionOverlap: ['x-tos-allow-same-action-overlap', v => String(v)],
    symLinkTargetKey: 'x-tos-symlink-target',
    symLinkTargetBucket: 'x-tos-symlink-bucket',
    forbidOverwrite: 'x-tos-forbid-overwrite',
    bucketType: 'x-tos-bucket-type',
    recursiveMkdir: 'x-tos-recursive-mkdir'
  }; // type RequestHeadersMapKeys = keyof typeof requestHeadersMap;

  const requestQueryMap = {
    versionId: 'versionId',
    process: 'x-tos-process',
    saveBucket: 'x-tos-save-bucket',
    saveObject: 'x-tos-save-object',
    responseCacheControl: 'response-cache-control',
    responseContentDisposition: 'response-content-disposition',
    responseContentEncoding: 'response-content-encoding',
    responseContentLanguage: 'response-content-language',
    responseContentType: 'response-content-type',
    responseExpires: ['response-expires', v => v.toUTCString()]
  };
  function fillRequestHeaders(v, // keys: (keyof T & RequestHeadersMapKeys)[]
  keys) {
    if (!keys.length) {
      return;
    }

    const headers = v.headers || {};
    v.headers = headers;

    function setOneHeader(k, v) {
      if (headers[k] == null) {
        headers[k] = v;
      }
    }

    keys.forEach(k => {
      const confV = requestHeadersMap[k];

      if (!confV) {
        // maybe warning
        throw new TosClientError(`\`${k}\` isn't in keys of \`requestHeadersMap\``);
      }

      const oriValue = v[k];

      if (oriValue == null) {
        return;
      }

      const oriValueStr = `${oriValue}`;

      if (typeof confV === 'string') {
        return setOneHeader(confV, oriValueStr);
      }

      if (Array.isArray(confV)) {
        const newKey = confV[0];
        const newValue = confV[1](oriValue);
        return setOneHeader(newKey, newValue);
      }

      const obj = confV(oriValue);
      Object.entries(obj).forEach(([k, v]) => {
        setOneHeader(k, v);
      });
    });
  }
  function fillRequestQuery(v, query, keys) {
    if (!keys.length) {
      return;
    }

    function setOneKey(k, v) {
      if (query[k] == null) {
        query[k] = v;
      }
    }

    keys.forEach(k => {
      const confV = requestQueryMap[k];

      if (!confV) {
        // maybe warning
        throw new TosClientError(`\`${k}\` isn't in keys of \`requestQueryMap\``);
      }

      const oriValue = v[k];

      if (oriValue == null) {
        return;
      }

      const oriValueStr = `${oriValue}`;

      if (typeof confV === 'string') {
        return setOneKey(confV, oriValueStr);
      }

      if (Array.isArray(confV)) {
        const newKey = confV[0];
        const newValue = confV[1](oriValue);
        return setOneKey(newKey, newValue);
      }

      const obj = confV(oriValue);
      Object.entries(obj).forEach(([k, v]) => {
        setOneKey(k, v);
      });
    });
  }
  const paramsSerializer = params => {
    return lib.stringify(params);
  };
  function getNormalDataFromError(data, err) {
    return {
      data,
      statusCode: err.statusCode,
      headers: err.headers,
      requestId: err.requestId,
      id2: err.id2
    };
  }
  function checkCRC64WithHeaders(crc, headers) {
    const serverCRC64 = headers['x-tos-hash-crc64ecma'];

    if (serverCRC64 == null) {
      {
        console.warn("No x-tos-hash-crc64ecma in response's headers, please see https://www.volcengine.com/docs/6349/127737 to add `x-tos-hash-crc64ecma` to Expose-Headers field.");
      }

      return;
    }

    const crcStr = typeof crc === 'string' ? crc : crc.getCrc64();

    if (crcStr !== serverCRC64) {
      throw new TosClientError(`validate file crc64 failed. Expect crc64 ${serverCRC64}, actual crc64 ${crcStr}. Please try again.`);
    }
  }
  var HttpHeader;

  (function (HttpHeader) {
    HttpHeader["LastModified"] = "last-modified";
    HttpHeader["ContentLength"] = "content-length";
    HttpHeader["AcceptEncoding"] = "accept-encoding";
    HttpHeader["ContentEncoding"] = "content-encoding";
    HttpHeader["ContentMD5"] = "content-md5";
    HttpHeader["TosRawContentLength"] = "x-tos-raw-content-length";
    HttpHeader["TosTrailer"] = "x-tos-trailer";
    HttpHeader["TosHashCrc64ecma"] = "x-tos-hash-crc64ecma";
    HttpHeader["TosContentSha256"] = "x-tos-content-sha256";
    HttpHeader["TosDecodedContentLength"] = "x-tos-decoded-content-length";
    HttpHeader["TosEc"] = "x-tos-ec";
    HttpHeader["TosRequestId"] = "x-tos-request-id";
  })(HttpHeader || (HttpHeader = {}));
  /**
   * make async tasks serial
   * @param makeTask
   * @returns
   */


  const makeSerialAsyncTask = makeTask => {
    let lastTask = Promise.resolve();
    return async () => {
      lastTask = lastTask.then(() => makeTask());
      return lastTask;
    };
  };
  const tryDestroy = (stream, err) => {
    if (stream && 'destroy' in stream && typeof stream.destroy === 'function') {
      if ('destroyed' in stream && !stream.destroyed) {
        stream.destroy(err);
      }
    }
  };

  async function createMultipartUpload(input) {
    input = this.normalizeObjectInput(input);
    const headers = normalizeHeadersKey(input.headers);
    input.headers = headers;
    fillRequestHeaders(input, ['encodingType', 'cacheControl', 'contentDisposition', 'contentEncoding', 'contentLanguage', 'contentType', 'expires', 'acl', 'grantFullControl', 'grantRead', 'grantReadAcp', 'grantWriteAcp', 'ssecAlgorithm', 'ssecKey', 'ssecKeyMD5', 'serverSideEncryption', 'serverSideDataEncryption', 'meta', 'websiteRedirectLocation', 'storageClass', 'forbidOverwrite']);
    this.setObjectContentTypeHeader(input, headers);
    return this._fetchObject(input, 'POST', {
      uploads: ''
    }, headers, '');
  }

  // the last part is no size limit

  const MIN_PART_SIZE_EXCEPT_LAST_ONE = 5 * 1024 * 1024;
  const MAX_PART_NUMBER = 10000;
  const calculateSafePartSize = (totalSize, expectPartSize, showWarning = false) => {
    let partSize = expectPartSize;

    if (expectPartSize < MIN_PART_SIZE_EXCEPT_LAST_ONE) {
      partSize = MIN_PART_SIZE_EXCEPT_LAST_ONE;

      if (showWarning) {
        console.warn(`partSize has been set to ${partSize}, because the partSize you provided is less than the minimal size of multipart`);
      }
    }

    const minSize = Math.ceil(totalSize / MAX_PART_NUMBER);

    if (expectPartSize < minSize) {
      partSize = minSize;

      if (showWarning) {
        console.warn(`partSize has been set to ${partSize}, because the partSize you provided causes the number of part excesses 10,000`);
      }
    }

    return partSize;
  };
  async function listParts(input) {
    const {
      bucket,
      key,
      uploadId,
      ...nextQuery
    } = input;
    const ret = await this._fetchObject(input, 'GET', {
      uploadId,
      ...covertCamelCase2Kebab(nextQuery)
    }, {});
    const arrayProp = makeArrayProp(ret.data);
    arrayProp('Parts');
    return ret;
  }

  // alias with GoSDK
  // refer https://github.com/volcengine/ve-tos-golang-sdk/blob/main/tos/mime.go
  const mimeTypes = {
    '3gp': 'video/3gpp',
    '7z': 'application/x-7z-compressed',
    abw: 'application/x-abiword',
    ai: 'application/postscript',
    aif: 'audio/x-aiff',
    aifc: 'audio/x-aiff',
    aiff: 'audio/x-aiff',
    alc: 'chemical/x-alchemy',
    amr: 'audio/amr',
    anx: 'application/annodex',
    apk: 'application/vnd.android.package-archive',
    appcache: 'text/cache-manifest',
    art: 'image/x-jg',
    asc: 'text/plain',
    asf: 'video/x-ms-asf',
    aso: 'chemical/x-ncbi-asn1-binary',
    asx: 'video/x-ms-asf',
    atom: 'application/atom+xml',
    atomcat: 'application/atomcat+xml',
    atomsrv: 'application/atomserv+xml',
    au: 'audio/basic',
    avi: 'video/x-msvideo',
    awb: 'audio/amr-wb',
    axa: 'audio/annodex',
    axv: 'video/annodex',
    b: 'chemical/x-molconn-Z',
    bak: 'application/x-trash',
    bat: 'application/x-msdos-program',
    bcpio: 'application/x-bcpio',
    bib: 'text/x-bibtex',
    bin: 'application/octet-stream',
    bmp: 'image/x-ms-bmp',
    boo: 'text/x-boo',
    book: 'application/x-maker',
    brf: 'text/plain',
    bsd: 'chemical/x-crossfire',
    c: 'text/x-csrc',
    'c++': 'text/x-c++src',
    c3d: 'chemical/x-chem3d',
    cab: 'application/x-cab',
    cac: 'chemical/x-cache',
    cache: 'chemical/x-cache',
    cap: 'application/vnd.tcpdump.pcap',
    cascii: 'chemical/x-cactvs-binary',
    cat: 'application/vnd.ms-pki.seccat',
    cbin: 'chemical/x-cactvs-binary',
    cbr: 'application/x-cbr',
    cbz: 'application/x-cbz',
    cc: 'text/x-c++src',
    cda: 'application/x-cdf',
    cdf: 'application/x-cdf',
    cdr: 'image/x-coreldraw',
    cdt: 'image/x-coreldrawtemplate',
    cdx: 'chemical/x-cdx',
    cdy: 'application/vnd.cinderella',
    cef: 'chemical/x-cxf',
    cer: 'chemical/x-cerius',
    chm: 'chemical/x-chemdraw',
    chrt: 'application/x-kchart',
    cif: 'chemical/x-cif',
    class: 'application/java-vm',
    cls: 'text/x-tex',
    cmdf: 'chemical/x-cmdf',
    cml: 'chemical/x-cml',
    cod: 'application/vnd.rim.cod',
    com: 'application/x-msdos-program',
    cpa: 'chemical/x-compass',
    cpio: 'application/x-cpio',
    cpp: 'text/x-c++src',
    cpt: 'application/mac-compactpro',
    cr2: 'image/x-canon-cr2',
    crl: 'application/x-pkcs7-crl',
    crt: 'application/x-x509-ca-cert',
    crw: 'image/x-canon-crw',
    csd: 'audio/csound',
    csf: 'chemical/x-cache-csf',
    csh: 'application/x-csh',
    csm: 'chemical/x-csml',
    csml: 'chemical/x-csml',
    css: 'text/css',
    csv: 'text/csv',
    ctab: 'chemical/x-cactvs-binary',
    ctx: 'chemical/x-ctx',
    cu: 'application/cu-seeme',
    cub: 'chemical/x-gaussian-cube',
    cxf: 'chemical/x-cxf',
    cxx: 'text/x-c++src',
    d: 'text/x-dsrc',
    davmount: 'application/davmount+xml',
    dcm: 'application/dicom',
    dcr: 'application/x-director',
    ddeb: 'application/vnd.debian.binary-package',
    dif: 'video/dv',
    diff: 'text/x-diff',
    dir: 'application/x-director',
    djv: 'image/vnd.djvu',
    djvu: 'image/vnd.djvu',
    dl: 'video/dl',
    dll: 'application/x-msdos-program',
    dmg: 'application/x-apple-diskimage',
    dms: 'application/x-dms',
    doc: 'application/msword',
    docm: 'application/vnd.ms-word.document.macroEnabled.12',
    docx: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    dot: 'application/msword',
    dotm: 'application/vnd.ms-word.template.macroEnabled.12',
    dotx: 'application/vnd.openxmlformats-officedocument.wordprocessingml.template',
    dv: 'video/dv',
    dvi: 'application/x-dvi',
    dx: 'chemical/x-jcamp-dx',
    dxr: 'application/x-director',
    emb: 'chemical/x-embl-dl-nucleotide',
    embl: 'chemical/x-embl-dl-nucleotide',
    eml: 'message/rfc822',
    eot: 'application/vnd.ms-fontobject',
    eps: 'application/postscript',
    eps2: 'application/postscript',
    eps3: 'application/postscript',
    epsf: 'application/postscript',
    epsi: 'application/postscript',
    erf: 'image/x-epson-erf',
    es: 'application/ecmascript',
    etx: 'text/x-setext',
    exe: 'application/x-msdos-program',
    ez: 'application/andrew-inset',
    fb: 'application/x-maker',
    fbdoc: 'application/x-maker',
    fch: 'chemical/x-gaussian-checkpoint',
    fchk: 'chemical/x-gaussian-checkpoint',
    fig: 'application/x-xfig',
    flac: 'audio/flac',
    fli: 'video/fli',
    flv: 'video/x-flv',
    fm: 'application/x-maker',
    frame: 'application/x-maker',
    frm: 'application/x-maker',
    gal: 'chemical/x-gaussian-log',
    gam: 'chemical/x-gamess-input',
    gamin: 'chemical/x-gamess-input',
    gan: 'application/x-ganttproject',
    gau: 'chemical/x-gaussian-input',
    gcd: 'text/x-pcs-gcd',
    gcf: 'application/x-graphing-calculator',
    gcg: 'chemical/x-gcg8-sequence',
    gen: 'chemical/x-genbank',
    gf: 'application/x-tex-gf',
    gif: 'image/gif',
    gjc: 'chemical/x-gaussian-input',
    gjf: 'chemical/x-gaussian-input',
    gl: 'video/gl',
    gnumeric: 'application/x-gnumeric',
    gpt: 'chemical/x-mopac-graph',
    gsf: 'application/x-font',
    gsm: 'audio/x-gsm',
    gtar: 'application/x-gtar',
    gz: 'application/gzip',
    h: 'text/x-chdr',
    'h++': 'text/x-c++hdr',
    hdf: 'application/x-hdf',
    hh: 'text/x-c++hdr',
    hin: 'chemical/x-hin',
    hpp: 'text/x-c++hdr',
    hqx: 'application/mac-binhex40',
    hs: 'text/x-haskell',
    hta: 'application/hta',
    htc: 'text/x-component',
    htm: 'text/html',
    html: 'text/html',
    hwp: 'application/x-hwp',
    hxx: 'text/x-c++hdr',
    ica: 'application/x-ica',
    ice: 'x-conference/x-cooltalk',
    ico: 'image/vnd.microsoft.icon',
    ics: 'text/calendar',
    icz: 'text/calendar',
    ief: 'image/ief',
    iges: 'model/iges',
    igs: 'model/iges',
    iii: 'application/x-iphone',
    info: 'application/x-info',
    inp: 'chemical/x-gamess-input',
    ins: 'application/x-internet-signup',
    iso: 'application/x-iso9660-image',
    isp: 'application/x-internet-signup',
    ist: 'chemical/x-isostar',
    istr: 'chemical/x-isostar',
    jad: 'text/vnd.sun.j2me.app-descriptor',
    jam: 'application/x-jam',
    jar: 'application/java-archive',
    java: 'text/x-java',
    jdx: 'chemical/x-jcamp-dx',
    jmz: 'application/x-jmol',
    jng: 'image/x-jng',
    jnlp: 'application/x-java-jnlp-file',
    jp2: 'image/jp2',
    jpe: 'image/jpeg',
    jpeg: 'image/jpeg',
    jpf: 'image/jpx',
    jpg: 'image/jpeg',
    jpg2: 'image/jp2',
    jpm: 'image/jpm',
    jpx: 'image/jpx',
    js: 'application/javascript',
    json: 'application/json',
    kar: 'audio/midi',
    key: 'application/pgp-keys',
    kil: 'application/x-killustrator',
    kin: 'chemical/x-kinemage',
    kml: 'application/vnd.google-earth.kml+xml',
    kmz: 'application/vnd.google-earth.kmz',
    kpr: 'application/x-kpresenter',
    kpt: 'application/x-kpresenter',
    ksp: 'application/x-kspread',
    kwd: 'application/x-kword',
    kwt: 'application/x-kword',
    latex: 'application/x-latex',
    lha: 'application/x-lha',
    lhs: 'text/x-literate-haskell',
    lin: 'application/bbolin',
    lsf: 'video/x-la-asf',
    lsx: 'video/x-la-asf',
    ltx: 'text/x-tex',
    ly: 'text/x-lilypond',
    lyx: 'application/x-lyx',
    lzh: 'application/x-lzh',
    lzx: 'application/x-lzx',
    m3g: 'application/m3g',
    m3u: 'audio/x-mpegurl',
    m3u8: 'application/x-mpegURL',
    m4a: 'audio/mpeg',
    maker: 'application/x-maker',
    man: 'application/x-troff-man',
    mbox: 'application/mbox',
    mcif: 'chemical/x-mmcif',
    mcm: 'chemical/x-macmolecule',
    mdb: 'application/msaccess',
    me: 'application/x-troff-me',
    mesh: 'model/mesh',
    mid: 'audio/midi',
    midi: 'audio/midi',
    mif: 'application/x-mif',
    mkv: 'video/x-matroska',
    mm: 'application/x-freemind',
    mmd: 'chemical/x-macromodel-input',
    mmf: 'application/vnd.smaf',
    mml: 'text/mathml',
    mmod: 'chemical/x-macromodel-input',
    mng: 'video/x-mng',
    moc: 'text/x-moc',
    mol: 'chemical/x-mdl-molfile',
    mol2: 'chemical/x-mol2',
    moo: 'chemical/x-mopac-out',
    mop: 'chemical/x-mopac-input',
    mopcrt: 'chemical/x-mopac-input',
    mov: 'video/quicktime',
    movie: 'video/x-sgi-movie',
    mp2: 'audio/mpeg',
    mp3: 'audio/mpeg',
    mp4: 'video/mp4',
    mpc: 'chemical/x-mopac-input',
    mpe: 'video/mpeg',
    mpeg: 'video/mpeg',
    mpega: 'audio/mpeg',
    mpg: 'video/mpeg',
    mpga: 'audio/mpeg',
    mph: 'application/x-comsol',
    mpv: 'video/x-matroska',
    ms: 'application/x-troff-ms',
    msh: 'model/mesh',
    msi: 'application/x-msi',
    mvb: 'chemical/x-mopac-vib',
    mxf: 'application/mxf',
    mxu: 'video/vnd.mpegurl',
    nb: 'application/mathematica',
    nbp: 'application/mathematica',
    nc: 'application/x-netcdf',
    nef: 'image/x-nikon-nef',
    nwc: 'application/x-nwc',
    o: 'application/x-object',
    oda: 'application/oda',
    odb: 'application/vnd.oasis.opendocument.database',
    odc: 'application/vnd.oasis.opendocument.chart',
    odf: 'application/vnd.oasis.opendocument.formula',
    odg: 'application/vnd.oasis.opendocument.graphics',
    odi: 'application/vnd.oasis.opendocument.image',
    odm: 'application/vnd.oasis.opendocument.text-master',
    odp: 'application/vnd.oasis.opendocument.presentation',
    ods: 'application/vnd.oasis.opendocument.spreadsheet',
    odt: 'application/vnd.oasis.opendocument.text',
    oga: 'audio/ogg',
    ogg: 'audio/ogg',
    ogv: 'video/ogg',
    ogx: 'application/ogg',
    old: 'application/x-trash',
    one: 'application/onenote',
    onepkg: 'application/onenote',
    onetmp: 'application/onenote',
    onetoc2: 'application/onenote',
    opf: 'application/oebps-package+xml',
    opus: 'audio/ogg',
    orc: 'audio/csound',
    orf: 'image/x-olympus-orf',
    otf: 'application/font-sfnt',
    otg: 'application/vnd.oasis.opendocument.graphics-template',
    oth: 'application/vnd.oasis.opendocument.text-web',
    otp: 'application/vnd.oasis.opendocument.presentation-template',
    ots: 'application/vnd.oasis.opendocument.spreadsheet-template',
    ott: 'application/vnd.oasis.opendocument.text-template',
    oza: 'application/x-oz-application',
    p: 'text/x-pascal',
    p7r: 'application/x-pkcs7-certreqresp',
    pac: 'application/x-ns-proxy-autoconfig',
    pas: 'text/x-pascal',
    pat: 'image/x-coreldrawpattern',
    patch: 'text/x-diff',
    pbm: 'image/x-portable-bitmap',
    pcap: 'application/vnd.tcpdump.pcap',
    pcf: 'application/x-font-pcf',
    'pcf.Z': 'application/x-font-pcf',
    pcx: 'image/pcx',
    pdb: 'chemical/x-pdb',
    pdf: 'application/pdf',
    pfa: 'application/x-font',
    pfb: 'application/x-font',
    pfr: 'application/font-tdpfr',
    pgm: 'image/x-portable-graymap',
    pgn: 'application/x-chess-pgn',
    pgp: 'application/pgp-encrypted',
    php: '#application/x-httpd-php',
    php3: '#application/x-httpd-php3',
    php3p: '#application/x-httpd-php3-preprocessed',
    php4: '#application/x-httpd-php4',
    php5: '#application/x-httpd-php5',
    phps: '#application/x-httpd-php-source',
    pht: '#application/x-httpd-php',
    phtml: '#application/x-httpd-php',
    pk: 'application/x-tex-pk',
    pl: 'text/x-perl',
    pls: 'audio/x-scpls',
    pm: 'text/x-perl',
    png: 'image/png',
    pnm: 'image/x-portable-anymap',
    pot: 'text/plain',
    potm: 'application/vnd.ms-powerpoint.template.macroEnabled.12',
    potx: 'application/vnd.openxmlformats-officedocument.presentationml.template',
    ppam: 'application/vnd.ms-powerpoint.addin.macroEnabled.12',
    ppm: 'image/x-portable-pixmap',
    pps: 'application/vnd.ms-powerpoint',
    ppsm: 'application/vnd.ms-powerpoint.slideshow.macroEnabled.12',
    ppsx: 'application/vnd.openxmlformats-officedocument.presentationml.slideshow',
    ppt: 'application/vnd.ms-powerpoint',
    pptm: 'application/vnd.ms-powerpoint.presentation.macroEnabled.12',
    pptx: 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    prf: 'application/pics-rules',
    prt: 'chemical/x-ncbi-asn1-ascii',
    ps: 'application/postscript',
    psd: 'image/x-photoshop',
    py: 'text/x-python',
    pyc: 'application/x-python-code',
    pyo: 'application/x-python-code',
    qgs: 'application/x-qgis',
    qt: 'video/quicktime',
    qtl: 'application/x-quicktimeplayer',
    ra: 'audio/x-pn-realaudio',
    ram: 'audio/x-pn-realaudio',
    rar: 'application/rar',
    ras: 'image/x-cmu-raster',
    rb: 'application/x-ruby',
    rd: 'chemical/x-mdl-rdfile',
    rdf: 'application/rdf+xml',
    rdp: 'application/x-rdp',
    rgb: 'image/x-rgb',
    rhtml: '#application/x-httpd-eruby',
    rm: 'audio/x-pn-realaudio',
    roff: 'application/x-troff',
    ros: 'chemical/x-rosdal',
    rpm: 'application/x-redhat-package-manager',
    rss: 'application/x-rss+xml',
    rtf: 'application/rtf',
    rtx: 'text/richtext',
    rxn: 'chemical/x-mdl-rxnfile',
    scala: 'text/x-scala',
    sce: 'application/x-scilab',
    sci: 'application/x-scilab',
    sco: 'audio/csound',
    scr: 'application/x-silverlight',
    sct: 'text/scriptlet',
    sd: 'chemical/x-mdl-sdfile',
    sd2: 'audio/x-sd2',
    sda: 'application/vnd.stardivision.draw',
    sdc: 'application/vnd.stardivision.calc',
    sdd: 'application/vnd.stardivision.impress',
    sds: 'application/vnd.stardivision.chart',
    sdw: 'application/vnd.stardivision.writer',
    ser: 'application/java-serialized-object',
    sfd: 'application/vnd.font-fontforge-sfd',
    sfv: 'text/x-sfv',
    sgf: 'application/x-go-sgf',
    sgl: 'application/vnd.stardivision.writer-global',
    sh: 'application/x-sh',
    shar: 'application/x-shar',
    shp: 'application/x-qgis',
    shtml: 'text/html',
    shx: 'application/x-qgis',
    sid: 'audio/prs.sid',
    sig: 'application/pgp-signature',
    sik: 'application/x-trash',
    silo: 'model/mesh',
    sis: 'application/vnd.symbian.install',
    sisx: 'x-epoc/x-sisx-app',
    sit: 'application/x-stuffit',
    sitx: 'application/x-stuffit',
    skd: 'application/x-koan',
    skm: 'application/x-koan',
    skp: 'application/x-koan',
    skt: 'application/x-koan',
    sldm: 'application/vnd.ms-powerpoint.slide.macroEnabled.12',
    sldx: 'application/vnd.openxmlformats-officedocument.presentationml.slide',
    smi: 'application/smil+xml',
    smil: 'application/smil+xml',
    snd: 'audio/basic',
    spc: 'chemical/x-galactic-spc',
    spl: 'application/x-futuresplash',
    spx: 'audio/ogg',
    sql: 'application/x-sql',
    src: 'application/x-wais-source',
    srt: 'text/plain',
    stc: 'application/vnd.sun.xml.calc.template',
    std: 'application/vnd.sun.xml.draw.template',
    sti: 'application/vnd.sun.xml.impress.template',
    stw: 'application/vnd.sun.xml.writer.template',
    sty: 'text/x-tex',
    sv4cpio: 'application/x-sv4cpio',
    sv4crc: 'application/x-sv4crc',
    svg: 'image/svg+xml',
    svgz: 'image/svg+xml',
    sw: 'chemical/x-swissprot',
    swf: 'application/x-shockwave-flash',
    swfl: 'application/x-shockwave-flash',
    sxc: 'application/vnd.sun.xml.calc',
    sxd: 'application/vnd.sun.xml.draw',
    sxg: 'application/vnd.sun.xml.writer.global',
    sxi: 'application/vnd.sun.xml.impress',
    sxm: 'application/vnd.sun.xml.math',
    sxw: 'application/vnd.sun.xml.writer',
    t: 'application/x-troff',
    tar: 'application/x-tar',
    taz: 'application/x-gtar-compressed',
    tcl: 'application/x-tcl',
    tex: 'text/x-tex',
    texi: 'application/x-texinfo',
    texinfo: 'application/x-texinfo',
    text: 'text/plain',
    tgf: 'chemical/x-mdl-tgf',
    tgz: 'application/x-gtar-compressed',
    thmx: 'application/vnd.ms-officetheme',
    tif: 'image/tiff',
    tiff: 'image/tiff',
    tk: 'text/x-tcl',
    tm: 'text/texmacs',
    torrent: 'application/x-bittorrent',
    tr: 'application/x-troff',
    ts: 'video/MP2T',
    tsp: 'application/dsptype',
    tsv: 'text/tab-separated-values',
    ttf: 'application/font-sfnt',
    ttl: 'text/turtle',
    txt: 'text/plain',
    uls: 'text/iuls',
    ustar: 'application/x-ustar',
    val: 'chemical/x-ncbi-asn1-binary',
    vcard: 'text/vcard',
    vcd: 'application/x-cdlink',
    vcf: 'text/vcard',
    vcs: 'text/x-vcalendar',
    vmd: 'chemical/x-vmd',
    vms: 'chemical/x-vamas-iso14976',
    vrm: 'x-world/x-vrml',
    vrml: 'model/vrml',
    vsd: 'application/vnd.visio',
    vss: 'application/vnd.visio',
    vst: 'application/vnd.visio',
    vsw: 'application/vnd.visio',
    wad: 'application/x-doom',
    wasm: 'application/wasm',
    wav: 'audio/wav',
    wax: 'audio/x-ms-wax',
    wbmp: 'image/vnd.wap.wbmp',
    wbxml: 'application/vnd.wap.wbxml',
    webm: 'video/webm',
    wk: 'application/x-123',
    wm: 'video/x-ms-wm',
    wma: 'audio/x-ms-wma',
    wmd: 'application/x-ms-wmd',
    wml: 'text/vnd.wap.wml',
    wmlc: 'application/vnd.wap.wmlc',
    wmls: 'text/vnd.wap.wmlscript',
    wmlsc: 'application/vnd.wap.wmlscriptc',
    wmv: 'video/x-ms-wmv',
    wmx: 'video/x-ms-wmx',
    wmz: 'application/x-ms-wmz',
    woff: 'application/font-woff',
    wp5: 'application/vnd.wordperfect5.1',
    wpd: 'application/vnd.wordperfect',
    wrl: 'model/vrml',
    wsc: 'text/scriptlet',
    wvx: 'video/x-ms-wvx',
    wz: 'application/x-wingz',
    x3d: 'model/x3d+xml',
    x3db: 'model/x3d+binary',
    x3dv: 'model/x3d+vrml',
    xbm: 'image/x-xbitmap',
    xcf: 'application/x-xcf',
    xcos: 'application/x-scilab-xcos',
    xht: 'application/xhtml+xml',
    xhtml: 'application/xhtml+xml',
    xlam: 'application/vnd.ms-excel.addin.macroEnabled.12',
    xlb: 'application/vnd.ms-excel',
    xls: 'application/vnd.ms-excel',
    xlsb: 'application/vnd.ms-excel.sheet.binary.macroEnabled.12',
    xlsm: 'application/vnd.ms-excel.sheet.macroEnabled.12',
    xlsx: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    xlt: 'application/vnd.ms-excel',
    xltm: 'application/vnd.ms-excel.template.macroEnabled.12',
    xltx: 'application/vnd.openxmlformats-officedocument.spreadsheetml.template',
    xml: 'application/xml',
    xpi: 'application/x-xpinstall',
    xpm: 'image/x-xpixmap',
    xsd: 'application/xml',
    xsl: 'application/xslt+xml',
    xslt: 'application/xslt+xml',
    xspf: 'application/xspf+xml',
    xtel: 'chemical/x-xtel',
    xul: 'application/vnd.mozilla.xul+xml',
    xwd: 'image/x-xwindowdump',
    xyz: 'chemical/x-xyz',
    xz: 'application/x-xz',
    zip: 'application/zip'
  };

  // 1. crcjs maybe make browser long task
  // 2. the size of webassembly version's crc is a bit large, it's 1.2MB when uncompressed.

  class CRC {
    reset() {}

    async updateBlob() {
      throw new TosClientError('Not implemented.(CRC may cause browser lag.)');
    }

    update(_value) {
      throw new TosClientError('Not implemented.(CRC may cause browser lag.)');
    }

  }

  var browserCRC = {
    __proto__: null,
    CRC: CRC
  };

  let crcModule = null;

  {
    crcModule = browserCRC;
  }

  const {
    CRC: CRC$1,
    combineCrc64
  } = crcModule;

  function createDefaultRateLimiter(_capacity, _rate) {
    throw Error('no implemention in browser environment');
  }
  function createRateLimiterStream(_rateLimiter) {
    throw Error('no implemention in browser environment');
  }

  var moduleBrowser = {
    __proto__: null,
    createDefaultRateLimiter: createDefaultRateLimiter,
    createRateLimiterStream: createRateLimiterStream
  };

  let rateLimiter = null;

  {
    rateLimiter = moduleBrowser;
  }

  const {
    createDefaultRateLimiter: createDefaultRateLimiter$1,
    createRateLimiterStream: createRateLimiterStream$1
  } = rateLimiter;

  var TosHeader;

  (function (TosHeader) {
    TosHeader["HeaderRestore"] = "x-tos-restore";
    TosHeader["HeaderRestoreExpiryDays"] = "x-tos-restore-expiry-days";
    TosHeader["HeaderRestoreRequestDate"] = "x-tos-restore-request-date";
    TosHeader["HeaderRestoreTier"] = "x-tos-restore-tier";
    TosHeader["HeaderProjectName"] = "x-tos-project-name";
    TosHeader["HeaderReplicationStatus"] = "x-tos-replication-status";
  })(TosHeader || (TosHeader = {}));

  const RestoreOngoingRequestTrueStr = 'ongoing-request="true"';

  const getObjectInputKey = input => {
    return typeof input === 'string' ? input : input.key;
  };
  const DEFAULT_CONTENT_TYPE$1 = 'application/octet-stream';
  function lookupMimeType(key) {
    const lastDotIndex = key.lastIndexOf('.');

    if (lastDotIndex <= 0) {
      return undefined;
    }

    const extName = key.slice(lastDotIndex + 1).toLowerCase();
    return mimeTypes[extName];
  } // for all object methods

  function validateObjectName(input) {
    const key = typeof input === 'string' ? input : input.key;

    if (key.length < 1) {
      throw new TosClientError('invalid object name, the length must be greater than 1');
    }
  }
  function getSize(body, headers) {
    if (isBuffer$2(body)) {
      return body.length;
    }

    if (isBlob$1(body)) {
      return body.size;
    }

    if (headers && headers['content-length']) {
      const v = +headers['content-length'];

      if (v >= 0) {
        return v;
      }
    }

    return null;
  }
  function getEmitReadBodyConfig({
    body,
    dataTransferCallback,
    makeRetryStream,
    rateLimiter
  }) {
    let newBody = body;

    const getDefaultRet = () => ({
      body: newBody,
      makeRetryStream: undefined
    });

    {
      return getDefaultRet();
    }
  }
  async function getCRCBodyConfig({
    body,
    beforeRetry,
    makeRetryStream,
    enableCRC
  }) {
    {
      return {
        body,
        beforeRetry,
        makeRetryStream
      };
    }
  }
  async function getNewBodyConfig(input) {
    const config1 = getEmitReadBodyConfig(input);
    input = { ...input,
      ...config1
    };
    const config2 = getCRCBodyConfig(input);
    return config2;
  }
  function getCopySourceHeaderValue(srcBucket, srcKey) {
    return `/${srcBucket}/${encodeURIComponent(srcKey)}`;
  }
  const getRestoreInfoFromHeaders = headers => {
    if (!headers) return;
    const headerStoreValue = headers == null ? void 0 : headers[TosHeader.HeaderRestore];

    if (headerStoreValue) {
      var _split$1$split$, _split$, _split$$split;

      /**
       * value example:
       * X-Tos-Restore: ongoing-request="false", expiry-date="Fri, 19 Apr 2024 00:00:00 GMT"
       */
      const ExpiryDate = (_split$1$split$ = (_split$ = (headerStoreValue != null ? headerStoreValue : '').split('",')[1]) == null ? void 0 : _split$.split == null ? void 0 : (_split$$split = _split$.split('=')) == null ? void 0 : _split$$split[1]) != null ? _split$1$split$ : '';
      const OngoingRequest = (headerStoreValue == null ? void 0 : headerStoreValue.trim()) === RestoreOngoingRequestTrueStr ? true : false;
      const restoreInfo = {
        RestoreStatus: {
          OngoingRequest,
          ExpiryDate
        }
      };

      if (OngoingRequest) {
        var _headers$TosHeader$He;

        restoreInfo.RestoreParam = {
          ExpiryDays: headers[TosHeader.HeaderRestoreExpiryDays] ? Number(headers[TosHeader.HeaderRestoreExpiryDays]) : 0,
          RequestDate: (_headers$TosHeader$He = headers[TosHeader.HeaderRestoreRequestDate]) != null ? _headers$TosHeader$He : '',
          Tier: headers[TosHeader.HeaderRestoreTier]
        };
      }

      return restoreInfo;
    }

    return;
  };

  (function (DataTransferType) {
    DataTransferType[DataTransferType["Started"] = 1] = "Started";
    DataTransferType[DataTransferType["Rw"] = 2] = "Rw";
    DataTransferType[DataTransferType["Succeed"] = 3] = "Succeed";
    DataTransferType[DataTransferType["Failed"] = 4] = "Failed";
  })(exports.DataTransferType || (exports.DataTransferType = {}));

  /**
   * Helpers.
   */

  var s = 1000;
  var m = s * 60;
  var h = m * 60;
  var d = h * 24;
  var w = d * 7;
  var y = d * 365.25;

  /**
   * Parse or format the given `val`.
   *
   * Options:
   *
   *  - `long` verbose formatting [false]
   *
   * @param {String|Number} val
   * @param {Object} [options]
   * @throws {Error} throw an error if val is not a non-empty string or a number
   * @return {String|Number}
   * @api public
   */

  var ms = function(val, options) {
    options = options || {};
    var type = typeof val;
    if (type === 'string' && val.length > 0) {
      return parse$1(val);
    } else if (type === 'number' && isFinite(val)) {
      return options.long ? fmtLong(val) : fmtShort(val);
    }
    throw new Error(
      'val is not a non-empty string or a valid number. val=' +
        JSON.stringify(val)
    );
  };

  /**
   * Parse the given `str` and return milliseconds.
   *
   * @param {String} str
   * @return {Number}
   * @api private
   */

  function parse$1(str) {
    str = String(str);
    if (str.length > 100) {
      return;
    }
    var match = /^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(
      str
    );
    if (!match) {
      return;
    }
    var n = parseFloat(match[1]);
    var type = (match[2] || 'ms').toLowerCase();
    switch (type) {
      case 'years':
      case 'year':
      case 'yrs':
      case 'yr':
      case 'y':
        return n * y;
      case 'weeks':
      case 'week':
      case 'w':
        return n * w;
      case 'days':
      case 'day':
      case 'd':
        return n * d;
      case 'hours':
      case 'hour':
      case 'hrs':
      case 'hr':
      case 'h':
        return n * h;
      case 'minutes':
      case 'minute':
      case 'mins':
      case 'min':
      case 'm':
        return n * m;
      case 'seconds':
      case 'second':
      case 'secs':
      case 'sec':
      case 's':
        return n * s;
      case 'milliseconds':
      case 'millisecond':
      case 'msecs':
      case 'msec':
      case 'ms':
        return n;
      default:
        return undefined;
    }
  }

  /**
   * Short format for `ms`.
   *
   * @param {Number} ms
   * @return {String}
   * @api private
   */

  function fmtShort(ms) {
    var msAbs = Math.abs(ms);
    if (msAbs >= d) {
      return Math.round(ms / d) + 'd';
    }
    if (msAbs >= h) {
      return Math.round(ms / h) + 'h';
    }
    if (msAbs >= m) {
      return Math.round(ms / m) + 'm';
    }
    if (msAbs >= s) {
      return Math.round(ms / s) + 's';
    }
    return ms + 'ms';
  }

  /**
   * Long format for `ms`.
   *
   * @param {Number} ms
   * @return {String}
   * @api private
   */

  function fmtLong(ms) {
    var msAbs = Math.abs(ms);
    if (msAbs >= d) {
      return plural(ms, msAbs, d, 'day');
    }
    if (msAbs >= h) {
      return plural(ms, msAbs, h, 'hour');
    }
    if (msAbs >= m) {
      return plural(ms, msAbs, m, 'minute');
    }
    if (msAbs >= s) {
      return plural(ms, msAbs, s, 'second');
    }
    return ms + ' ms';
  }

  /**
   * Pluralization helper.
   */

  function plural(ms, msAbs, n, name) {
    var isPlural = msAbs >= n * 1.5;
    return Math.round(ms / n) + ' ' + name + (isPlural ? 's' : '');
  }

  /**
   * This is the common logic for both the Node.js and web browser
   * implementations of `debug()`.
   */

  function setup(env) {
  	createDebug.debug = createDebug;
  	createDebug.default = createDebug;
  	createDebug.coerce = coerce;
  	createDebug.disable = disable;
  	createDebug.enable = enable;
  	createDebug.enabled = enabled;
  	createDebug.humanize = ms;
  	createDebug.destroy = destroy;

  	Object.keys(env).forEach(key => {
  		createDebug[key] = env[key];
  	});

  	/**
  	* The currently active debug mode names, and names to skip.
  	*/

  	createDebug.names = [];
  	createDebug.skips = [];

  	/**
  	* Map of special "%n" handling functions, for the debug "format" argument.
  	*
  	* Valid key names are a single, lower or upper-case letter, i.e. "n" and "N".
  	*/
  	createDebug.formatters = {};

  	/**
  	* Selects a color for a debug namespace
  	* @param {String} namespace The namespace string for the debug instance to be colored
  	* @return {Number|String} An ANSI color code for the given namespace
  	* @api private
  	*/
  	function selectColor(namespace) {
  		let hash = 0;

  		for (let i = 0; i < namespace.length; i++) {
  			hash = ((hash << 5) - hash) + namespace.charCodeAt(i);
  			hash |= 0; // Convert to 32bit integer
  		}

  		return createDebug.colors[Math.abs(hash) % createDebug.colors.length];
  	}
  	createDebug.selectColor = selectColor;

  	/**
  	* Create a debugger with the given `namespace`.
  	*
  	* @param {String} namespace
  	* @return {Function}
  	* @api public
  	*/
  	function createDebug(namespace) {
  		let prevTime;
  		let enableOverride = null;
  		let namespacesCache;
  		let enabledCache;

  		function debug(...args) {
  			// Disabled?
  			if (!debug.enabled) {
  				return;
  			}

  			const self = debug;

  			// Set `diff` timestamp
  			const curr = Number(new Date());
  			const ms = curr - (prevTime || curr);
  			self.diff = ms;
  			self.prev = prevTime;
  			self.curr = curr;
  			prevTime = curr;

  			args[0] = createDebug.coerce(args[0]);

  			if (typeof args[0] !== 'string') {
  				// Anything else let's inspect with %O
  				args.unshift('%O');
  			}

  			// Apply any `formatters` transformations
  			let index = 0;
  			args[0] = args[0].replace(/%([a-zA-Z%])/g, (match, format) => {
  				// If we encounter an escaped % then don't increase the array index
  				if (match === '%%') {
  					return '%';
  				}
  				index++;
  				const formatter = createDebug.formatters[format];
  				if (typeof formatter === 'function') {
  					const val = args[index];
  					match = formatter.call(self, val);

  					// Now we need to remove `args[index]` since it's inlined in the `format`
  					args.splice(index, 1);
  					index--;
  				}
  				return match;
  			});

  			// Apply env-specific formatting (colors, etc.)
  			createDebug.formatArgs.call(self, args);

  			const logFn = self.log || createDebug.log;
  			logFn.apply(self, args);
  		}

  		debug.namespace = namespace;
  		debug.useColors = createDebug.useColors();
  		debug.color = createDebug.selectColor(namespace);
  		debug.extend = extend;
  		debug.destroy = createDebug.destroy; // XXX Temporary. Will be removed in the next major release.

  		Object.defineProperty(debug, 'enabled', {
  			enumerable: true,
  			configurable: false,
  			get: () => {
  				if (enableOverride !== null) {
  					return enableOverride;
  				}
  				if (namespacesCache !== createDebug.namespaces) {
  					namespacesCache = createDebug.namespaces;
  					enabledCache = createDebug.enabled(namespace);
  				}

  				return enabledCache;
  			},
  			set: v => {
  				enableOverride = v;
  			}
  		});

  		// Env-specific initialization logic for debug instances
  		if (typeof createDebug.init === 'function') {
  			createDebug.init(debug);
  		}

  		return debug;
  	}

  	function extend(namespace, delimiter) {
  		const newDebug = createDebug(this.namespace + (typeof delimiter === 'undefined' ? ':' : delimiter) + namespace);
  		newDebug.log = this.log;
  		return newDebug;
  	}

  	/**
  	* Enables a debug mode by namespaces. This can include modes
  	* separated by a colon and wildcards.
  	*
  	* @param {String} namespaces
  	* @api public
  	*/
  	function enable(namespaces) {
  		createDebug.save(namespaces);
  		createDebug.namespaces = namespaces;

  		createDebug.names = [];
  		createDebug.skips = [];

  		let i;
  		const split = (typeof namespaces === 'string' ? namespaces : '').split(/[\s,]+/);
  		const len = split.length;

  		for (i = 0; i < len; i++) {
  			if (!split[i]) {
  				// ignore empty strings
  				continue;
  			}

  			namespaces = split[i].replace(/\*/g, '.*?');

  			if (namespaces[0] === '-') {
  				createDebug.skips.push(new RegExp('^' + namespaces.slice(1) + '$'));
  			} else {
  				createDebug.names.push(new RegExp('^' + namespaces + '$'));
  			}
  		}
  	}

  	/**
  	* Disable debug output.
  	*
  	* @return {String} namespaces
  	* @api public
  	*/
  	function disable() {
  		const namespaces = [
  			...createDebug.names.map(toNamespace),
  			...createDebug.skips.map(toNamespace).map(namespace => '-' + namespace)
  		].join(',');
  		createDebug.enable('');
  		return namespaces;
  	}

  	/**
  	* Returns true if the given mode name is enabled, false otherwise.
  	*
  	* @param {String} name
  	* @return {Boolean}
  	* @api public
  	*/
  	function enabled(name) {
  		if (name[name.length - 1] === '*') {
  			return true;
  		}

  		let i;
  		let len;

  		for (i = 0, len = createDebug.skips.length; i < len; i++) {
  			if (createDebug.skips[i].test(name)) {
  				return false;
  			}
  		}

  		for (i = 0, len = createDebug.names.length; i < len; i++) {
  			if (createDebug.names[i].test(name)) {
  				return true;
  			}
  		}

  		return false;
  	}

  	/**
  	* Convert regexp to namespace
  	*
  	* @param {RegExp} regxep
  	* @return {String} namespace
  	* @api private
  	*/
  	function toNamespace(regexp) {
  		return regexp.toString()
  			.substring(2, regexp.toString().length - 2)
  			.replace(/\.\*\?$/, '*');
  	}

  	/**
  	* Coerce `val`.
  	*
  	* @param {Mixed} val
  	* @return {Mixed}
  	* @api private
  	*/
  	function coerce(val) {
  		if (val instanceof Error) {
  			return val.stack || val.message;
  		}
  		return val;
  	}

  	/**
  	* XXX DO NOT USE. This is a temporary stub function.
  	* XXX It WILL be removed in the next major release.
  	*/
  	function destroy() {
  		console.warn('Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.');
  	}

  	createDebug.enable(createDebug.load());

  	return createDebug;
  }

  var common = setup;

  var browser$1 = createCommonjsModule(function (module, exports) {
  /* eslint-env browser */

  /**
   * This is the web browser implementation of `debug()`.
   */

  exports.formatArgs = formatArgs;
  exports.save = save;
  exports.load = load;
  exports.useColors = useColors;
  exports.storage = localstorage();
  exports.destroy = (() => {
  	let warned = false;

  	return () => {
  		if (!warned) {
  			warned = true;
  			console.warn('Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.');
  		}
  	};
  })();

  /**
   * Colors.
   */

  exports.colors = [
  	'#0000CC',
  	'#0000FF',
  	'#0033CC',
  	'#0033FF',
  	'#0066CC',
  	'#0066FF',
  	'#0099CC',
  	'#0099FF',
  	'#00CC00',
  	'#00CC33',
  	'#00CC66',
  	'#00CC99',
  	'#00CCCC',
  	'#00CCFF',
  	'#3300CC',
  	'#3300FF',
  	'#3333CC',
  	'#3333FF',
  	'#3366CC',
  	'#3366FF',
  	'#3399CC',
  	'#3399FF',
  	'#33CC00',
  	'#33CC33',
  	'#33CC66',
  	'#33CC99',
  	'#33CCCC',
  	'#33CCFF',
  	'#6600CC',
  	'#6600FF',
  	'#6633CC',
  	'#6633FF',
  	'#66CC00',
  	'#66CC33',
  	'#9900CC',
  	'#9900FF',
  	'#9933CC',
  	'#9933FF',
  	'#99CC00',
  	'#99CC33',
  	'#CC0000',
  	'#CC0033',
  	'#CC0066',
  	'#CC0099',
  	'#CC00CC',
  	'#CC00FF',
  	'#CC3300',
  	'#CC3333',
  	'#CC3366',
  	'#CC3399',
  	'#CC33CC',
  	'#CC33FF',
  	'#CC6600',
  	'#CC6633',
  	'#CC9900',
  	'#CC9933',
  	'#CCCC00',
  	'#CCCC33',
  	'#FF0000',
  	'#FF0033',
  	'#FF0066',
  	'#FF0099',
  	'#FF00CC',
  	'#FF00FF',
  	'#FF3300',
  	'#FF3333',
  	'#FF3366',
  	'#FF3399',
  	'#FF33CC',
  	'#FF33FF',
  	'#FF6600',
  	'#FF6633',
  	'#FF9900',
  	'#FF9933',
  	'#FFCC00',
  	'#FFCC33'
  ];

  /**
   * Currently only WebKit-based Web Inspectors, Firefox >= v31,
   * and the Firebug extension (any Firefox version) are known
   * to support "%c" CSS customizations.
   *
   * TODO: add a `localStorage` variable to explicitly enable/disable colors
   */

  // eslint-disable-next-line complexity
  function useColors() {
  	// NB: In an Electron preload script, document will be defined but not fully
  	// initialized. Since we know we're in Chrome, we'll just detect this case
  	// explicitly
  	if (typeof window !== 'undefined' && window.process && (window.process.type === 'renderer' || window.process.__nwjs)) {
  		return true;
  	}

  	// Internet Explorer and Edge do not support colors.
  	if (typeof navigator !== 'undefined' && navigator.userAgent && navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/)) {
  		return false;
  	}

  	// Is webkit? http://stackoverflow.com/a/16459606/376773
  	// document is undefined in react-native: https://github.com/facebook/react-native/pull/1632
  	return (typeof document !== 'undefined' && document.documentElement && document.documentElement.style && document.documentElement.style.WebkitAppearance) ||
  		// Is firebug? http://stackoverflow.com/a/398120/376773
  		(typeof window !== 'undefined' && window.console && (window.console.firebug || (window.console.exception && window.console.table))) ||
  		// Is firefox >= v31?
  		// https://developer.mozilla.org/en-US/docs/Tools/Web_Console#Styling_messages
  		(typeof navigator !== 'undefined' && navigator.userAgent && navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/) && parseInt(RegExp.$1, 10) >= 31) ||
  		// Double check webkit in userAgent just in case we are in a worker
  		(typeof navigator !== 'undefined' && navigator.userAgent && navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/));
  }

  /**
   * Colorize log arguments if enabled.
   *
   * @api public
   */

  function formatArgs(args) {
  	args[0] = (this.useColors ? '%c' : '') +
  		this.namespace +
  		(this.useColors ? ' %c' : ' ') +
  		args[0] +
  		(this.useColors ? '%c ' : ' ') +
  		'+' + module.exports.humanize(this.diff);

  	if (!this.useColors) {
  		return;
  	}

  	const c = 'color: ' + this.color;
  	args.splice(1, 0, c, 'color: inherit');

  	// The final "%c" is somewhat tricky, because there could be other
  	// arguments passed either before or after the %c, so we need to
  	// figure out the correct index to insert the CSS into
  	let index = 0;
  	let lastC = 0;
  	args[0].replace(/%[a-zA-Z%]/g, match => {
  		if (match === '%%') {
  			return;
  		}
  		index++;
  		if (match === '%c') {
  			// We only are interested in the *last* %c
  			// (the user may have provided their own)
  			lastC = index;
  		}
  	});

  	args.splice(lastC, 0, c);
  }

  /**
   * Invokes `console.debug()` when available.
   * No-op when `console.debug` is not a "function".
   * If `console.debug` is not available, falls back
   * to `console.log`.
   *
   * @api public
   */
  exports.log = console.debug || console.log || (() => {});

  /**
   * Save `namespaces`.
   *
   * @param {String} namespaces
   * @api private
   */
  function save(namespaces) {
  	try {
  		if (namespaces) {
  			exports.storage.setItem('debug', namespaces);
  		} else {
  			exports.storage.removeItem('debug');
  		}
  	} catch (error) {
  		// Swallow
  		// XXX (@Qix-) should we be logging these?
  	}
  }

  /**
   * Load `namespaces`.
   *
   * @return {String} returns the previously persisted debug modes
   * @api private
   */
  function load() {
  	let r;
  	try {
  		r = exports.storage.getItem('debug');
  	} catch (error) {
  		// Swallow
  		// XXX (@Qix-) should we be logging these?
  	}

  	// If debug isn't set in LS, and we're in Electron, try to load $DEBUG
  	if (!r && typeof process !== 'undefined' && 'env' in process) {
  		r = process.env.DEBUG;
  	}

  	return r;
  }

  /**
   * Localstorage attempts to return the localstorage.
   *
   * This is necessary because safari throws
   * when a user disables cookies/localstorage
   * and you attempt to access it.
   *
   * @return {LocalStorage}
   * @api private
   */

  function localstorage() {
  	try {
  		// TVMLKit (Apple TV JS Runtime) does not have a window object, just localStorage in the global context
  		// The Browser also has localStorage in the global context.
  		return localStorage;
  	} catch (error) {
  		// Swallow
  		// XXX (@Qix-) should we be logging these?
  	}
  }

  module.exports = common(exports);

  const {formatters} = module.exports;

  /**
   * Map %j to `JSON.stringify()`, since no Web Inspectors do that by default.
   */

  formatters.j = function (v) {
  	try {
  		return JSON.stringify(v);
  	} catch (error) {
  		return '[UnexpectedJSONParseError]: ' + error.message;
  	}
  };
  });

  const TOS = /*#__PURE__*/browser$1('TOS');

  const retryNamespace = '__retryConfig__';
  const retrySignatureNamespace = '__retrySignature__';

  function isNetworkError(error) {
    var _error$response$heade;

    // no response or no requestId, ignore no network(error.code is undefined)
    return !error.response && Boolean(error.code) || error.response && !((_error$response$heade = error.response.headers) != null && _error$response$heade['x-tos-request-id']);
  }

  function isCanRetryStatusCode(error) {
    if (!error.response) {
      return false;
    }

    const {
      status
    } = error.response;

    if (status === 429 || status >= 500) {
      return true;
    }

    return false;
  }

  const BROWSER_NEED_DELETE_HEADERS = ['content-length', 'user-agent', 'host'];
  const makeAxiosInst = maxRetryCount => {
    const axiosInst = axios$1.create(); // set `axiosInst` default values to avoid being affected by the global default values of axios

    axiosInst.defaults.auth = undefined;
    axiosInst.defaults.responseType = 'json';
    axiosInst.defaults.params = undefined;
    axiosInst.defaults.headers = {};
    axiosInst.defaults.withCredentials = false;
    axiosInst.defaults.maxContentLength = -1;
    axiosInst.defaults.maxBodyLength = -1;
    axiosInst.defaults.maxRedirects = 0;

    axiosInst.defaults.validateStatus = function (status) {
      return status >= 200 && status < 300; // default
    };

    axiosInst.defaults.decompress = false;
    axiosInst.defaults.transitional = {
      silentJSONParsing: true,
      forcedJSONParsing: true,
      clarifyTimeoutError: false
    }; // delete browser headers

    {
      axiosInst.interceptors.request.use(config => {
        if (!config.headers) {
          return config;
        }

        Object.keys(config.headers).forEach(key => {
          if (BROWSER_NEED_DELETE_HEADERS.includes(key.toLowerCase())) {
            delete config.headers[key];
          }
        });
        return config;
      });
    } // headers


    const ensureHeaders = v => {
      var _v$response;

      v.headers = v.headers || v.header || (v == null ? void 0 : (_v$response = v.response) == null ? void 0 : _v$response.headers) || {};
      return v;
    };

    axiosInst.interceptors.response.use(ensureHeaders, error => {
      ensureHeaders(error);
      return Promise.reject(error);
    }); // decode header. Encode headers' value by encodeHeadersValue method before calling axios

    function handleResponseHeader(headers) {
      Object.entries(headers).forEach(([key, value]) => {
        const [err, decodedValue] = safeSync(() => decodeURI(value));

        if (err || decodedValue == null || decodedValue === value) {
          return;
        }

        let sArr = [];
        const valueArr = `${value}`.match(/./gu);
        const decodedValueArr = decodedValue.match(/./gu);

        for (let i = 0, j = 0; i < decodedValueArr.length;) {
          const ch = decodedValueArr[i];

          if (ch === valueArr[j]) {
            sArr.push(ch);
            ++i;
            ++j;
            continue;
          }

          const encodedCh = encodeURIComponent(ch);

          if (ch.length > 1 || ch.charCodeAt(0) >= 128) {
            sArr.push(ch);
          } else {
            sArr.push(encodedCh);
          }

          ++i;
          j += encodedCh.length;
        }

        headers[key] = sArr.join('');
      });
    }

    axiosInst.interceptors.response.use(res => {
      if (!res.headers) {
        return res;
      }

      handleResponseHeader(res.headers);
      return res;
    }, async error => {
      var _error$response;

      if (!axios$1.isAxiosError(error)) {
        return Promise.reject(error);
      }

      const headers = (_error$response = error.response) == null ? void 0 : _error$response.headers;

      if (!headers) {
        return Promise.reject(error);
      }

      handleResponseHeader(headers);
      return Promise.reject(error);
    }); // retry

    axiosInst.interceptors.response.use(undefined, async error => {
      var _retryConfig$retryCou;

      const {
        config
      } = error;

      if (!config) {
        return Promise.reject(error);
      }

      if (!config[retryNamespace]) {
        config[retryNamespace] = {};
      }

      const retryConfig = config[retryNamespace];
      const retryCount = (_retryConfig$retryCou = retryConfig.retryCount) != null ? _retryConfig$retryCou : 0;
      let newData = config.data;

      const canRetryData = (() => {

        return true;
      })();

      const canRetry = (isNetworkError(error) || isCanRetryStatusCode(error)) && retryCount < maxRetryCount && canRetryData;

      if (!canRetry) {
        return Promise.reject(error);
      }

      const retrySignature = config[retrySignatureNamespace];

      if (retrySignature) {
        const {
          signOpt,
          sigInst
        } = retrySignature;
        const signatureHeaders = sigInst.signatureHeader(signOpt);
        signatureHeaders.forEach((value, key) => {
          config.headers[key] = value;
        });
      } //console.log('config: ', config)


      TOS('retryConfig: ', config);
      const nextConfig = { ...config,
        data: newData,
        [retryNamespace]: { ...retryConfig,
          retryCount: retryCount + 1
        }
      };
      retryConfig.beforeRetry == null ? void 0 : retryConfig.beforeRetry();
      return axiosInst(nextConfig);
    });
    return axiosInst;
  };

  var core = createCommonjsModule(function (module, exports) {
  (function (root, factory) {
  	{
  		// CommonJS
  		module.exports = exports = factory();
  	}
  }(commonjsGlobal, function () {

  	/*globals window, global, require*/

  	/**
  	 * CryptoJS core components.
  	 */
  	var CryptoJS = CryptoJS || (function (Math, undefined$1) {

  	    var crypto;

  	    // Native crypto from window (Browser)
  	    if (typeof window !== 'undefined' && window.crypto) {
  	        crypto = window.crypto;
  	    }

  	    // Native crypto in web worker (Browser)
  	    if (typeof self !== 'undefined' && self.crypto) {
  	        crypto = self.crypto;
  	    }

  	    // Native crypto from worker
  	    if (typeof globalThis !== 'undefined' && globalThis.crypto) {
  	        crypto = globalThis.crypto;
  	    }

  	    // Native (experimental IE 11) crypto from window (Browser)
  	    if (!crypto && typeof window !== 'undefined' && window.msCrypto) {
  	        crypto = window.msCrypto;
  	    }

  	    // Native crypto from global (NodeJS)
  	    if (!crypto && typeof commonjsGlobal !== 'undefined' && commonjsGlobal.crypto) {
  	        crypto = commonjsGlobal.crypto;
  	    }

  	    // Native crypto import via require (NodeJS)
  	    if (!crypto && typeof commonjsRequire === 'function') {
  	        try {
  	            crypto = require$$0;
  	        } catch (err) {}
  	    }

  	    /*
  	     * Cryptographically secure pseudorandom number generator
  	     *
  	     * As Math.random() is cryptographically not safe to use
  	     */
  	    var cryptoSecureRandomInt = function () {
  	        if (crypto) {
  	            // Use getRandomValues method (Browser)
  	            if (typeof crypto.getRandomValues === 'function') {
  	                try {
  	                    return crypto.getRandomValues(new Uint32Array(1))[0];
  	                } catch (err) {}
  	            }

  	            // Use randomBytes method (NodeJS)
  	            if (typeof crypto.randomBytes === 'function') {
  	                try {
  	                    return crypto.randomBytes(4).readInt32LE();
  	                } catch (err) {}
  	            }
  	        }

  	        throw new Error('Native crypto module could not be used to get secure random number.');
  	    };

  	    /*
  	     * Local polyfill of Object.create

  	     */
  	    var create = Object.create || (function () {
  	        function F() {}

  	        return function (obj) {
  	            var subtype;

  	            F.prototype = obj;

  	            subtype = new F();

  	            F.prototype = null;

  	            return subtype;
  	        };
  	    }());

  	    /**
  	     * CryptoJS namespace.
  	     */
  	    var C = {};

  	    /**
  	     * Library namespace.
  	     */
  	    var C_lib = C.lib = {};

  	    /**
  	     * Base object for prototypal inheritance.
  	     */
  	    var Base = C_lib.Base = (function () {


  	        return {
  	            /**
  	             * Creates a new object that inherits from this object.
  	             *
  	             * @param {Object} overrides Properties to copy into the new object.
  	             *
  	             * @return {Object} The new object.
  	             *
  	             * @static
  	             *
  	             * @example
  	             *
  	             *     var MyType = CryptoJS.lib.Base.extend({
  	             *         field: 'value',
  	             *
  	             *         method: function () {
  	             *         }
  	             *     });
  	             */
  	            extend: function (overrides) {
  	                // Spawn
  	                var subtype = create(this);

  	                // Augment
  	                if (overrides) {
  	                    subtype.mixIn(overrides);
  	                }

  	                // Create default initializer
  	                if (!subtype.hasOwnProperty('init') || this.init === subtype.init) {
  	                    subtype.init = function () {
  	                        subtype.$super.init.apply(this, arguments);
  	                    };
  	                }

  	                // Initializer's prototype is the subtype object
  	                subtype.init.prototype = subtype;

  	                // Reference supertype
  	                subtype.$super = this;

  	                return subtype;
  	            },

  	            /**
  	             * Extends this object and runs the init method.
  	             * Arguments to create() will be passed to init().
  	             *
  	             * @return {Object} The new object.
  	             *
  	             * @static
  	             *
  	             * @example
  	             *
  	             *     var instance = MyType.create();
  	             */
  	            create: function () {
  	                var instance = this.extend();
  	                instance.init.apply(instance, arguments);

  	                return instance;
  	            },

  	            /**
  	             * Initializes a newly created object.
  	             * Override this method to add some logic when your objects are created.
  	             *
  	             * @example
  	             *
  	             *     var MyType = CryptoJS.lib.Base.extend({
  	             *         init: function () {
  	             *             // ...
  	             *         }
  	             *     });
  	             */
  	            init: function () {
  	            },

  	            /**
  	             * Copies properties into this object.
  	             *
  	             * @param {Object} properties The properties to mix in.
  	             *
  	             * @example
  	             *
  	             *     MyType.mixIn({
  	             *         field: 'value'
  	             *     });
  	             */
  	            mixIn: function (properties) {
  	                for (var propertyName in properties) {
  	                    if (properties.hasOwnProperty(propertyName)) {
  	                        this[propertyName] = properties[propertyName];
  	                    }
  	                }

  	                // IE won't copy toString using the loop above
  	                if (properties.hasOwnProperty('toString')) {
  	                    this.toString = properties.toString;
  	                }
  	            },

  	            /**
  	             * Creates a copy of this object.
  	             *
  	             * @return {Object} The clone.
  	             *
  	             * @example
  	             *
  	             *     var clone = instance.clone();
  	             */
  	            clone: function () {
  	                return this.init.prototype.extend(this);
  	            }
  	        };
  	    }());

  	    /**
  	     * An array of 32-bit words.
  	     *
  	     * @property {Array} words The array of 32-bit words.
  	     * @property {number} sigBytes The number of significant bytes in this word array.
  	     */
  	    var WordArray = C_lib.WordArray = Base.extend({
  	        /**
  	         * Initializes a newly created word array.
  	         *
  	         * @param {Array} words (Optional) An array of 32-bit words.
  	         * @param {number} sigBytes (Optional) The number of significant bytes in the words.
  	         *
  	         * @example
  	         *
  	         *     var wordArray = CryptoJS.lib.WordArray.create();
  	         *     var wordArray = CryptoJS.lib.WordArray.create([0x00010203, 0x04050607]);
  	         *     var wordArray = CryptoJS.lib.WordArray.create([0x00010203, 0x04050607], 6);
  	         */
  	        init: function (words, sigBytes) {
  	            words = this.words = words || [];

  	            if (sigBytes != undefined$1) {
  	                this.sigBytes = sigBytes;
  	            } else {
  	                this.sigBytes = words.length * 4;
  	            }
  	        },

  	        /**
  	         * Converts this word array to a string.
  	         *
  	         * @param {Encoder} encoder (Optional) The encoding strategy to use. Default: CryptoJS.enc.Hex
  	         *
  	         * @return {string} The stringified word array.
  	         *
  	         * @example
  	         *
  	         *     var string = wordArray + '';
  	         *     var string = wordArray.toString();
  	         *     var string = wordArray.toString(CryptoJS.enc.Utf8);
  	         */
  	        toString: function (encoder) {
  	            return (encoder || Hex).stringify(this);
  	        },

  	        /**
  	         * Concatenates a word array to this word array.
  	         *
  	         * @param {WordArray} wordArray The word array to append.
  	         *
  	         * @return {WordArray} This word array.
  	         *
  	         * @example
  	         *
  	         *     wordArray1.concat(wordArray2);
  	         */
  	        concat: function (wordArray) {
  	            // Shortcuts
  	            var thisWords = this.words;
  	            var thatWords = wordArray.words;
  	            var thisSigBytes = this.sigBytes;
  	            var thatSigBytes = wordArray.sigBytes;

  	            // Clamp excess bits
  	            this.clamp();

  	            // Concat
  	            if (thisSigBytes % 4) {
  	                // Copy one byte at a time
  	                for (var i = 0; i < thatSigBytes; i++) {
  	                    var thatByte = (thatWords[i >>> 2] >>> (24 - (i % 4) * 8)) & 0xff;
  	                    thisWords[(thisSigBytes + i) >>> 2] |= thatByte << (24 - ((thisSigBytes + i) % 4) * 8);
  	                }
  	            } else {
  	                // Copy one word at a time
  	                for (var j = 0; j < thatSigBytes; j += 4) {
  	                    thisWords[(thisSigBytes + j) >>> 2] = thatWords[j >>> 2];
  	                }
  	            }
  	            this.sigBytes += thatSigBytes;

  	            // Chainable
  	            return this;
  	        },

  	        /**
  	         * Removes insignificant bits.
  	         *
  	         * @example
  	         *
  	         *     wordArray.clamp();
  	         */
  	        clamp: function () {
  	            // Shortcuts
  	            var words = this.words;
  	            var sigBytes = this.sigBytes;

  	            // Clamp
  	            words[sigBytes >>> 2] &= 0xffffffff << (32 - (sigBytes % 4) * 8);
  	            words.length = Math.ceil(sigBytes / 4);
  	        },

  	        /**
  	         * Creates a copy of this word array.
  	         *
  	         * @return {WordArray} The clone.
  	         *
  	         * @example
  	         *
  	         *     var clone = wordArray.clone();
  	         */
  	        clone: function () {
  	            var clone = Base.clone.call(this);
  	            clone.words = this.words.slice(0);

  	            return clone;
  	        },

  	        /**
  	         * Creates a word array filled with random bytes.
  	         *
  	         * @param {number} nBytes The number of random bytes to generate.
  	         *
  	         * @return {WordArray} The random word array.
  	         *
  	         * @static
  	         *
  	         * @example
  	         *
  	         *     var wordArray = CryptoJS.lib.WordArray.random(16);
  	         */
  	        random: function (nBytes) {
  	            var words = [];

  	            for (var i = 0; i < nBytes; i += 4) {
  	                words.push(cryptoSecureRandomInt());
  	            }

  	            return new WordArray.init(words, nBytes);
  	        }
  	    });

  	    /**
  	     * Encoder namespace.
  	     */
  	    var C_enc = C.enc = {};

  	    /**
  	     * Hex encoding strategy.
  	     */
  	    var Hex = C_enc.Hex = {
  	        /**
  	         * Converts a word array to a hex string.
  	         *
  	         * @param {WordArray} wordArray The word array.
  	         *
  	         * @return {string} The hex string.
  	         *
  	         * @static
  	         *
  	         * @example
  	         *
  	         *     var hexString = CryptoJS.enc.Hex.stringify(wordArray);
  	         */
  	        stringify: function (wordArray) {
  	            // Shortcuts
  	            var words = wordArray.words;
  	            var sigBytes = wordArray.sigBytes;

  	            // Convert
  	            var hexChars = [];
  	            for (var i = 0; i < sigBytes; i++) {
  	                var bite = (words[i >>> 2] >>> (24 - (i % 4) * 8)) & 0xff;
  	                hexChars.push((bite >>> 4).toString(16));
  	                hexChars.push((bite & 0x0f).toString(16));
  	            }

  	            return hexChars.join('');
  	        },

  	        /**
  	         * Converts a hex string to a word array.
  	         *
  	         * @param {string} hexStr The hex string.
  	         *
  	         * @return {WordArray} The word array.
  	         *
  	         * @static
  	         *
  	         * @example
  	         *
  	         *     var wordArray = CryptoJS.enc.Hex.parse(hexString);
  	         */
  	        parse: function (hexStr) {
  	            // Shortcut
  	            var hexStrLength = hexStr.length;

  	            // Convert
  	            var words = [];
  	            for (var i = 0; i < hexStrLength; i += 2) {
  	                words[i >>> 3] |= parseInt(hexStr.substr(i, 2), 16) << (24 - (i % 8) * 4);
  	            }

  	            return new WordArray.init(words, hexStrLength / 2);
  	        }
  	    };

  	    /**
  	     * Latin1 encoding strategy.
  	     */
  	    var Latin1 = C_enc.Latin1 = {
  	        /**
  	         * Converts a word array to a Latin1 string.
  	         *
  	         * @param {WordArray} wordArray The word array.
  	         *
  	         * @return {string} The Latin1 string.
  	         *
  	         * @static
  	         *
  	         * @example
  	         *
  	         *     var latin1String = CryptoJS.enc.Latin1.stringify(wordArray);
  	         */
  	        stringify: function (wordArray) {
  	            // Shortcuts
  	            var words = wordArray.words;
  	            var sigBytes = wordArray.sigBytes;

  	            // Convert
  	            var latin1Chars = [];
  	            for (var i = 0; i < sigBytes; i++) {
  	                var bite = (words[i >>> 2] >>> (24 - (i % 4) * 8)) & 0xff;
  	                latin1Chars.push(String.fromCharCode(bite));
  	            }

  	            return latin1Chars.join('');
  	        },

  	        /**
  	         * Converts a Latin1 string to a word array.
  	         *
  	         * @param {string} latin1Str The Latin1 string.
  	         *
  	         * @return {WordArray} The word array.
  	         *
  	         * @static
  	         *
  	         * @example
  	         *
  	         *     var wordArray = CryptoJS.enc.Latin1.parse(latin1String);
  	         */
  	        parse: function (latin1Str) {
  	            // Shortcut
  	            var latin1StrLength = latin1Str.length;

  	            // Convert
  	            var words = [];
  	            for (var i = 0; i < latin1StrLength; i++) {
  	                words[i >>> 2] |= (latin1Str.charCodeAt(i) & 0xff) << (24 - (i % 4) * 8);
  	            }

  	            return new WordArray.init(words, latin1StrLength);
  	        }
  	    };

  	    /**
  	     * UTF-8 encoding strategy.
  	     */
  	    var Utf8 = C_enc.Utf8 = {
  	        /**
  	         * Converts a word array to a UTF-8 string.
  	         *
  	         * @param {WordArray} wordArray The word array.
  	         *
  	         * @return {string} The UTF-8 string.
  	         *
  	         * @static
  	         *
  	         * @example
  	         *
  	         *     var utf8String = CryptoJS.enc.Utf8.stringify(wordArray);
  	         */
  	        stringify: function (wordArray) {
  	            try {
  	                return decodeURIComponent(escape(Latin1.stringify(wordArray)));
  	            } catch (e) {
  	                throw new Error('Malformed UTF-8 data');
  	            }
  	        },

  	        /**
  	         * Converts a UTF-8 string to a word array.
  	         *
  	         * @param {string} utf8Str The UTF-8 string.
  	         *
  	         * @return {WordArray} The word array.
  	         *
  	         * @static
  	         *
  	         * @example
  	         *
  	         *     var wordArray = CryptoJS.enc.Utf8.parse(utf8String);
  	         */
  	        parse: function (utf8Str) {
  	            return Latin1.parse(unescape(encodeURIComponent(utf8Str)));
  	        }
  	    };

  	    /**
  	     * Abstract buffered block algorithm template.
  	     *
  	     * The property blockSize must be implemented in a concrete subtype.
  	     *
  	     * @property {number} _minBufferSize The number of blocks that should be kept unprocessed in the buffer. Default: 0
  	     */
  	    var BufferedBlockAlgorithm = C_lib.BufferedBlockAlgorithm = Base.extend({
  	        /**
  	         * Resets this block algorithm's data buffer to its initial state.
  	         *
  	         * @example
  	         *
  	         *     bufferedBlockAlgorithm.reset();
  	         */
  	        reset: function () {
  	            // Initial values
  	            this._data = new WordArray.init();
  	            this._nDataBytes = 0;
  	        },

  	        /**
  	         * Adds new data to this block algorithm's buffer.
  	         *
  	         * @param {WordArray|string} data The data to append. Strings are converted to a WordArray using UTF-8.
  	         *
  	         * @example
  	         *
  	         *     bufferedBlockAlgorithm._append('data');
  	         *     bufferedBlockAlgorithm._append(wordArray);
  	         */
  	        _append: function (data) {
  	            // Convert string to WordArray, else assume WordArray already
  	            if (typeof data == 'string') {
  	                data = Utf8.parse(data);
  	            }

  	            // Append
  	            this._data.concat(data);
  	            this._nDataBytes += data.sigBytes;
  	        },

  	        /**
  	         * Processes available data blocks.
  	         *
  	         * This method invokes _doProcessBlock(offset), which must be implemented by a concrete subtype.
  	         *
  	         * @param {boolean} doFlush Whether all blocks and partial blocks should be processed.
  	         *
  	         * @return {WordArray} The processed data.
  	         *
  	         * @example
  	         *
  	         *     var processedData = bufferedBlockAlgorithm._process();
  	         *     var processedData = bufferedBlockAlgorithm._process(!!'flush');
  	         */
  	        _process: function (doFlush) {
  	            var processedWords;

  	            // Shortcuts
  	            var data = this._data;
  	            var dataWords = data.words;
  	            var dataSigBytes = data.sigBytes;
  	            var blockSize = this.blockSize;
  	            var blockSizeBytes = blockSize * 4;

  	            // Count blocks ready
  	            var nBlocksReady = dataSigBytes / blockSizeBytes;
  	            if (doFlush) {
  	                // Round up to include partial blocks
  	                nBlocksReady = Math.ceil(nBlocksReady);
  	            } else {
  	                // Round down to include only full blocks,
  	                // less the number of blocks that must remain in the buffer
  	                nBlocksReady = Math.max((nBlocksReady | 0) - this._minBufferSize, 0);
  	            }

  	            // Count words ready
  	            var nWordsReady = nBlocksReady * blockSize;

  	            // Count bytes ready
  	            var nBytesReady = Math.min(nWordsReady * 4, dataSigBytes);

  	            // Process blocks
  	            if (nWordsReady) {
  	                for (var offset = 0; offset < nWordsReady; offset += blockSize) {
  	                    // Perform concrete-algorithm logic
  	                    this._doProcessBlock(dataWords, offset);
  	                }

  	                // Remove processed words
  	                processedWords = dataWords.splice(0, nWordsReady);
  	                data.sigBytes -= nBytesReady;
  	            }

  	            // Return processed words
  	            return new WordArray.init(processedWords, nBytesReady);
  	        },

  	        /**
  	         * Creates a copy of this object.
  	         *
  	         * @return {Object} The clone.
  	         *
  	         * @example
  	         *
  	         *     var clone = bufferedBlockAlgorithm.clone();
  	         */
  	        clone: function () {
  	            var clone = Base.clone.call(this);
  	            clone._data = this._data.clone();

  	            return clone;
  	        },

  	        _minBufferSize: 0
  	    });

  	    /**
  	     * Abstract hasher template.
  	     *
  	     * @property {number} blockSize The number of 32-bit words this hasher operates on. Default: 16 (512 bits)
  	     */
  	    var Hasher = C_lib.Hasher = BufferedBlockAlgorithm.extend({
  	        /**
  	         * Configuration options.
  	         */
  	        cfg: Base.extend(),

  	        /**
  	         * Initializes a newly created hasher.
  	         *
  	         * @param {Object} cfg (Optional) The configuration options to use for this hash computation.
  	         *
  	         * @example
  	         *
  	         *     var hasher = CryptoJS.algo.SHA256.create();
  	         */
  	        init: function (cfg) {
  	            // Apply config defaults
  	            this.cfg = this.cfg.extend(cfg);

  	            // Set initial values
  	            this.reset();
  	        },

  	        /**
  	         * Resets this hasher to its initial state.
  	         *
  	         * @example
  	         *
  	         *     hasher.reset();
  	         */
  	        reset: function () {
  	            // Reset data buffer
  	            BufferedBlockAlgorithm.reset.call(this);

  	            // Perform concrete-hasher logic
  	            this._doReset();
  	        },

  	        /**
  	         * Updates this hasher with a message.
  	         *
  	         * @param {WordArray|string} messageUpdate The message to append.
  	         *
  	         * @return {Hasher} This hasher.
  	         *
  	         * @example
  	         *
  	         *     hasher.update('message');
  	         *     hasher.update(wordArray);
  	         */
  	        update: function (messageUpdate) {
  	            // Append
  	            this._append(messageUpdate);

  	            // Update the hash
  	            this._process();

  	            // Chainable
  	            return this;
  	        },

  	        /**
  	         * Finalizes the hash computation.
  	         * Note that the finalize operation is effectively a destructive, read-once operation.
  	         *
  	         * @param {WordArray|string} messageUpdate (Optional) A final message update.
  	         *
  	         * @return {WordArray} The hash.
  	         *
  	         * @example
  	         *
  	         *     var hash = hasher.finalize();
  	         *     var hash = hasher.finalize('message');
  	         *     var hash = hasher.finalize(wordArray);
  	         */
  	        finalize: function (messageUpdate) {
  	            // Final message update
  	            if (messageUpdate) {
  	                this._append(messageUpdate);
  	            }

  	            // Perform concrete-hasher logic
  	            var hash = this._doFinalize();

  	            return hash;
  	        },

  	        blockSize: 512/32,

  	        /**
  	         * Creates a shortcut function to a hasher's object interface.
  	         *
  	         * @param {Hasher} hasher The hasher to create a helper for.
  	         *
  	         * @return {Function} The shortcut function.
  	         *
  	         * @static
  	         *
  	         * @example
  	         *
  	         *     var SHA256 = CryptoJS.lib.Hasher._createHelper(CryptoJS.algo.SHA256);
  	         */
  	        _createHelper: function (hasher) {
  	            return function (message, cfg) {
  	                return new hasher.init(cfg).finalize(message);
  	            };
  	        },

  	        /**
  	         * Creates a shortcut function to the HMAC's object interface.
  	         *
  	         * @param {Hasher} hasher The hasher to use in this HMAC helper.
  	         *
  	         * @return {Function} The shortcut function.
  	         *
  	         * @static
  	         *
  	         * @example
  	         *
  	         *     var HmacSHA256 = CryptoJS.lib.Hasher._createHmacHelper(CryptoJS.algo.SHA256);
  	         */
  	        _createHmacHelper: function (hasher) {
  	            return function (message, key) {
  	                return new C_algo.HMAC.init(hasher, key).finalize(message);
  	            };
  	        }
  	    });

  	    /**
  	     * Algorithm namespace.
  	     */
  	    var C_algo = C.algo = {};

  	    return C;
  	}(Math));


  	return CryptoJS;

  }));
  });

  var sha256 = createCommonjsModule(function (module, exports) {
  (function (root, factory) {
  	{
  		// CommonJS
  		module.exports = exports = factory(core);
  	}
  }(commonjsGlobal, function (CryptoJS) {

  	(function (Math) {
  	    // Shortcuts
  	    var C = CryptoJS;
  	    var C_lib = C.lib;
  	    var WordArray = C_lib.WordArray;
  	    var Hasher = C_lib.Hasher;
  	    var C_algo = C.algo;

  	    // Initialization and round constants tables
  	    var H = [];
  	    var K = [];

  	    // Compute constants
  	    (function () {
  	        function isPrime(n) {
  	            var sqrtN = Math.sqrt(n);
  	            for (var factor = 2; factor <= sqrtN; factor++) {
  	                if (!(n % factor)) {
  	                    return false;
  	                }
  	            }

  	            return true;
  	        }

  	        function getFractionalBits(n) {
  	            return ((n - (n | 0)) * 0x100000000) | 0;
  	        }

  	        var n = 2;
  	        var nPrime = 0;
  	        while (nPrime < 64) {
  	            if (isPrime(n)) {
  	                if (nPrime < 8) {
  	                    H[nPrime] = getFractionalBits(Math.pow(n, 1 / 2));
  	                }
  	                K[nPrime] = getFractionalBits(Math.pow(n, 1 / 3));

  	                nPrime++;
  	            }

  	            n++;
  	        }
  	    }());

  	    // Reusable object
  	    var W = [];

  	    /**
  	     * SHA-256 hash algorithm.
  	     */
  	    var SHA256 = C_algo.SHA256 = Hasher.extend({
  	        _doReset: function () {
  	            this._hash = new WordArray.init(H.slice(0));
  	        },

  	        _doProcessBlock: function (M, offset) {
  	            // Shortcut
  	            var H = this._hash.words;

  	            // Working variables
  	            var a = H[0];
  	            var b = H[1];
  	            var c = H[2];
  	            var d = H[3];
  	            var e = H[4];
  	            var f = H[5];
  	            var g = H[6];
  	            var h = H[7];

  	            // Computation
  	            for (var i = 0; i < 64; i++) {
  	                if (i < 16) {
  	                    W[i] = M[offset + i] | 0;
  	                } else {
  	                    var gamma0x = W[i - 15];
  	                    var gamma0  = ((gamma0x << 25) | (gamma0x >>> 7))  ^
  	                                  ((gamma0x << 14) | (gamma0x >>> 18)) ^
  	                                   (gamma0x >>> 3);

  	                    var gamma1x = W[i - 2];
  	                    var gamma1  = ((gamma1x << 15) | (gamma1x >>> 17)) ^
  	                                  ((gamma1x << 13) | (gamma1x >>> 19)) ^
  	                                   (gamma1x >>> 10);

  	                    W[i] = gamma0 + W[i - 7] + gamma1 + W[i - 16];
  	                }

  	                var ch  = (e & f) ^ (~e & g);
  	                var maj = (a & b) ^ (a & c) ^ (b & c);

  	                var sigma0 = ((a << 30) | (a >>> 2)) ^ ((a << 19) | (a >>> 13)) ^ ((a << 10) | (a >>> 22));
  	                var sigma1 = ((e << 26) | (e >>> 6)) ^ ((e << 21) | (e >>> 11)) ^ ((e << 7)  | (e >>> 25));

  	                var t1 = h + sigma1 + ch + K[i] + W[i];
  	                var t2 = sigma0 + maj;

  	                h = g;
  	                g = f;
  	                f = e;
  	                e = (d + t1) | 0;
  	                d = c;
  	                c = b;
  	                b = a;
  	                a = (t1 + t2) | 0;
  	            }

  	            // Intermediate hash value
  	            H[0] = (H[0] + a) | 0;
  	            H[1] = (H[1] + b) | 0;
  	            H[2] = (H[2] + c) | 0;
  	            H[3] = (H[3] + d) | 0;
  	            H[4] = (H[4] + e) | 0;
  	            H[5] = (H[5] + f) | 0;
  	            H[6] = (H[6] + g) | 0;
  	            H[7] = (H[7] + h) | 0;
  	        },

  	        _doFinalize: function () {
  	            // Shortcuts
  	            var data = this._data;
  	            var dataWords = data.words;

  	            var nBitsTotal = this._nDataBytes * 8;
  	            var nBitsLeft = data.sigBytes * 8;

  	            // Add padding
  	            dataWords[nBitsLeft >>> 5] |= 0x80 << (24 - nBitsLeft % 32);
  	            dataWords[(((nBitsLeft + 64) >>> 9) << 4) + 14] = Math.floor(nBitsTotal / 0x100000000);
  	            dataWords[(((nBitsLeft + 64) >>> 9) << 4) + 15] = nBitsTotal;
  	            data.sigBytes = dataWords.length * 4;

  	            // Hash final blocks
  	            this._process();

  	            // Return final computed hash
  	            return this._hash;
  	        },

  	        clone: function () {
  	            var clone = Hasher.clone.call(this);
  	            clone._hash = this._hash.clone();

  	            return clone;
  	        }
  	    });

  	    /**
  	     * Shortcut function to the hasher's object interface.
  	     *
  	     * @param {WordArray|string} message The message to hash.
  	     *
  	     * @return {WordArray} The hash.
  	     *
  	     * @static
  	     *
  	     * @example
  	     *
  	     *     var hash = CryptoJS.SHA256('message');
  	     *     var hash = CryptoJS.SHA256(wordArray);
  	     */
  	    C.SHA256 = Hasher._createHelper(SHA256);

  	    /**
  	     * Shortcut function to the HMAC's object interface.
  	     *
  	     * @param {WordArray|string} message The message to hash.
  	     * @param {WordArray|string} key The secret key.
  	     *
  	     * @return {WordArray} The HMAC.
  	     *
  	     * @static
  	     *
  	     * @example
  	     *
  	     *     var hmac = CryptoJS.HmacSHA256(message, key);
  	     */
  	    C.HmacSHA256 = Hasher._createHmacHelper(SHA256);
  	}(Math));


  	return CryptoJS.SHA256;

  }));
  });

  var hmac = createCommonjsModule(function (module, exports) {
  (function (root, factory) {
  	{
  		// CommonJS
  		module.exports = exports = factory(core);
  	}
  }(commonjsGlobal, function (CryptoJS) {

  	(function () {
  	    // Shortcuts
  	    var C = CryptoJS;
  	    var C_lib = C.lib;
  	    var Base = C_lib.Base;
  	    var C_enc = C.enc;
  	    var Utf8 = C_enc.Utf8;
  	    var C_algo = C.algo;

  	    /**
  	     * HMAC algorithm.
  	     */
  	    var HMAC = C_algo.HMAC = Base.extend({
  	        /**
  	         * Initializes a newly created HMAC.
  	         *
  	         * @param {Hasher} hasher The hash algorithm to use.
  	         * @param {WordArray|string} key The secret key.
  	         *
  	         * @example
  	         *
  	         *     var hmacHasher = CryptoJS.algo.HMAC.create(CryptoJS.algo.SHA256, key);
  	         */
  	        init: function (hasher, key) {
  	            // Init hasher
  	            hasher = this._hasher = new hasher.init();

  	            // Convert string to WordArray, else assume WordArray already
  	            if (typeof key == 'string') {
  	                key = Utf8.parse(key);
  	            }

  	            // Shortcuts
  	            var hasherBlockSize = hasher.blockSize;
  	            var hasherBlockSizeBytes = hasherBlockSize * 4;

  	            // Allow arbitrary length keys
  	            if (key.sigBytes > hasherBlockSizeBytes) {
  	                key = hasher.finalize(key);
  	            }

  	            // Clamp excess bits
  	            key.clamp();

  	            // Clone key for inner and outer pads
  	            var oKey = this._oKey = key.clone();
  	            var iKey = this._iKey = key.clone();

  	            // Shortcuts
  	            var oKeyWords = oKey.words;
  	            var iKeyWords = iKey.words;

  	            // XOR keys with pad constants
  	            for (var i = 0; i < hasherBlockSize; i++) {
  	                oKeyWords[i] ^= 0x5c5c5c5c;
  	                iKeyWords[i] ^= 0x36363636;
  	            }
  	            oKey.sigBytes = iKey.sigBytes = hasherBlockSizeBytes;

  	            // Set initial values
  	            this.reset();
  	        },

  	        /**
  	         * Resets this HMAC to its initial state.
  	         *
  	         * @example
  	         *
  	         *     hmacHasher.reset();
  	         */
  	        reset: function () {
  	            // Shortcut
  	            var hasher = this._hasher;

  	            // Reset
  	            hasher.reset();
  	            hasher.update(this._iKey);
  	        },

  	        /**
  	         * Updates this HMAC with a message.
  	         *
  	         * @param {WordArray|string} messageUpdate The message to append.
  	         *
  	         * @return {HMAC} This HMAC instance.
  	         *
  	         * @example
  	         *
  	         *     hmacHasher.update('message');
  	         *     hmacHasher.update(wordArray);
  	         */
  	        update: function (messageUpdate) {
  	            this._hasher.update(messageUpdate);

  	            // Chainable
  	            return this;
  	        },

  	        /**
  	         * Finalizes the HMAC computation.
  	         * Note that the finalize operation is effectively a destructive, read-once operation.
  	         *
  	         * @param {WordArray|string} messageUpdate (Optional) A final message update.
  	         *
  	         * @return {WordArray} The HMAC.
  	         *
  	         * @example
  	         *
  	         *     var hmac = hmacHasher.finalize();
  	         *     var hmac = hmacHasher.finalize('message');
  	         *     var hmac = hmacHasher.finalize(wordArray);
  	         */
  	        finalize: function (messageUpdate) {
  	            // Shortcut
  	            var hasher = this._hasher;

  	            // Compute HMAC
  	            var innerHash = hasher.finalize(messageUpdate);
  	            hasher.reset();
  	            var hmac = hasher.finalize(this._oKey.clone().concat(innerHash));

  	            return hmac;
  	        }
  	    });
  	}());


  }));
  });

  var hmacSha256 = createCommonjsModule(function (module, exports) {
  (function (root, factory, undef) {
  	{
  		// CommonJS
  		module.exports = exports = factory(core, sha256, hmac);
  	}
  }(commonjsGlobal, function (CryptoJS) {

  	return CryptoJS.HmacSHA256;

  }));
  });

  var md5 = createCommonjsModule(function (module, exports) {
  (function (root, factory) {
  	{
  		// CommonJS
  		module.exports = exports = factory(core);
  	}
  }(commonjsGlobal, function (CryptoJS) {

  	(function (Math) {
  	    // Shortcuts
  	    var C = CryptoJS;
  	    var C_lib = C.lib;
  	    var WordArray = C_lib.WordArray;
  	    var Hasher = C_lib.Hasher;
  	    var C_algo = C.algo;

  	    // Constants table
  	    var T = [];

  	    // Compute constants
  	    (function () {
  	        for (var i = 0; i < 64; i++) {
  	            T[i] = (Math.abs(Math.sin(i + 1)) * 0x100000000) | 0;
  	        }
  	    }());

  	    /**
  	     * MD5 hash algorithm.
  	     */
  	    var MD5 = C_algo.MD5 = Hasher.extend({
  	        _doReset: function () {
  	            this._hash = new WordArray.init([
  	                0x67452301, 0xefcdab89,
  	                0x98badcfe, 0x10325476
  	            ]);
  	        },

  	        _doProcessBlock: function (M, offset) {
  	            // Swap endian
  	            for (var i = 0; i < 16; i++) {
  	                // Shortcuts
  	                var offset_i = offset + i;
  	                var M_offset_i = M[offset_i];

  	                M[offset_i] = (
  	                    (((M_offset_i << 8)  | (M_offset_i >>> 24)) & 0x00ff00ff) |
  	                    (((M_offset_i << 24) | (M_offset_i >>> 8))  & 0xff00ff00)
  	                );
  	            }

  	            // Shortcuts
  	            var H = this._hash.words;

  	            var M_offset_0  = M[offset + 0];
  	            var M_offset_1  = M[offset + 1];
  	            var M_offset_2  = M[offset + 2];
  	            var M_offset_3  = M[offset + 3];
  	            var M_offset_4  = M[offset + 4];
  	            var M_offset_5  = M[offset + 5];
  	            var M_offset_6  = M[offset + 6];
  	            var M_offset_7  = M[offset + 7];
  	            var M_offset_8  = M[offset + 8];
  	            var M_offset_9  = M[offset + 9];
  	            var M_offset_10 = M[offset + 10];
  	            var M_offset_11 = M[offset + 11];
  	            var M_offset_12 = M[offset + 12];
  	            var M_offset_13 = M[offset + 13];
  	            var M_offset_14 = M[offset + 14];
  	            var M_offset_15 = M[offset + 15];

  	            // Working variables
  	            var a = H[0];
  	            var b = H[1];
  	            var c = H[2];
  	            var d = H[3];

  	            // Computation
  	            a = FF(a, b, c, d, M_offset_0,  7,  T[0]);
  	            d = FF(d, a, b, c, M_offset_1,  12, T[1]);
  	            c = FF(c, d, a, b, M_offset_2,  17, T[2]);
  	            b = FF(b, c, d, a, M_offset_3,  22, T[3]);
  	            a = FF(a, b, c, d, M_offset_4,  7,  T[4]);
  	            d = FF(d, a, b, c, M_offset_5,  12, T[5]);
  	            c = FF(c, d, a, b, M_offset_6,  17, T[6]);
  	            b = FF(b, c, d, a, M_offset_7,  22, T[7]);
  	            a = FF(a, b, c, d, M_offset_8,  7,  T[8]);
  	            d = FF(d, a, b, c, M_offset_9,  12, T[9]);
  	            c = FF(c, d, a, b, M_offset_10, 17, T[10]);
  	            b = FF(b, c, d, a, M_offset_11, 22, T[11]);
  	            a = FF(a, b, c, d, M_offset_12, 7,  T[12]);
  	            d = FF(d, a, b, c, M_offset_13, 12, T[13]);
  	            c = FF(c, d, a, b, M_offset_14, 17, T[14]);
  	            b = FF(b, c, d, a, M_offset_15, 22, T[15]);

  	            a = GG(a, b, c, d, M_offset_1,  5,  T[16]);
  	            d = GG(d, a, b, c, M_offset_6,  9,  T[17]);
  	            c = GG(c, d, a, b, M_offset_11, 14, T[18]);
  	            b = GG(b, c, d, a, M_offset_0,  20, T[19]);
  	            a = GG(a, b, c, d, M_offset_5,  5,  T[20]);
  	            d = GG(d, a, b, c, M_offset_10, 9,  T[21]);
  	            c = GG(c, d, a, b, M_offset_15, 14, T[22]);
  	            b = GG(b, c, d, a, M_offset_4,  20, T[23]);
  	            a = GG(a, b, c, d, M_offset_9,  5,  T[24]);
  	            d = GG(d, a, b, c, M_offset_14, 9,  T[25]);
  	            c = GG(c, d, a, b, M_offset_3,  14, T[26]);
  	            b = GG(b, c, d, a, M_offset_8,  20, T[27]);
  	            a = GG(a, b, c, d, M_offset_13, 5,  T[28]);
  	            d = GG(d, a, b, c, M_offset_2,  9,  T[29]);
  	            c = GG(c, d, a, b, M_offset_7,  14, T[30]);
  	            b = GG(b, c, d, a, M_offset_12, 20, T[31]);

  	            a = HH(a, b, c, d, M_offset_5,  4,  T[32]);
  	            d = HH(d, a, b, c, M_offset_8,  11, T[33]);
  	            c = HH(c, d, a, b, M_offset_11, 16, T[34]);
  	            b = HH(b, c, d, a, M_offset_14, 23, T[35]);
  	            a = HH(a, b, c, d, M_offset_1,  4,  T[36]);
  	            d = HH(d, a, b, c, M_offset_4,  11, T[37]);
  	            c = HH(c, d, a, b, M_offset_7,  16, T[38]);
  	            b = HH(b, c, d, a, M_offset_10, 23, T[39]);
  	            a = HH(a, b, c, d, M_offset_13, 4,  T[40]);
  	            d = HH(d, a, b, c, M_offset_0,  11, T[41]);
  	            c = HH(c, d, a, b, M_offset_3,  16, T[42]);
  	            b = HH(b, c, d, a, M_offset_6,  23, T[43]);
  	            a = HH(a, b, c, d, M_offset_9,  4,  T[44]);
  	            d = HH(d, a, b, c, M_offset_12, 11, T[45]);
  	            c = HH(c, d, a, b, M_offset_15, 16, T[46]);
  	            b = HH(b, c, d, a, M_offset_2,  23, T[47]);

  	            a = II(a, b, c, d, M_offset_0,  6,  T[48]);
  	            d = II(d, a, b, c, M_offset_7,  10, T[49]);
  	            c = II(c, d, a, b, M_offset_14, 15, T[50]);
  	            b = II(b, c, d, a, M_offset_5,  21, T[51]);
  	            a = II(a, b, c, d, M_offset_12, 6,  T[52]);
  	            d = II(d, a, b, c, M_offset_3,  10, T[53]);
  	            c = II(c, d, a, b, M_offset_10, 15, T[54]);
  	            b = II(b, c, d, a, M_offset_1,  21, T[55]);
  	            a = II(a, b, c, d, M_offset_8,  6,  T[56]);
  	            d = II(d, a, b, c, M_offset_15, 10, T[57]);
  	            c = II(c, d, a, b, M_offset_6,  15, T[58]);
  	            b = II(b, c, d, a, M_offset_13, 21, T[59]);
  	            a = II(a, b, c, d, M_offset_4,  6,  T[60]);
  	            d = II(d, a, b, c, M_offset_11, 10, T[61]);
  	            c = II(c, d, a, b, M_offset_2,  15, T[62]);
  	            b = II(b, c, d, a, M_offset_9,  21, T[63]);

  	            // Intermediate hash value
  	            H[0] = (H[0] + a) | 0;
  	            H[1] = (H[1] + b) | 0;
  	            H[2] = (H[2] + c) | 0;
  	            H[3] = (H[3] + d) | 0;
  	        },

  	        _doFinalize: function () {
  	            // Shortcuts
  	            var data = this._data;
  	            var dataWords = data.words;

  	            var nBitsTotal = this._nDataBytes * 8;
  	            var nBitsLeft = data.sigBytes * 8;

  	            // Add padding
  	            dataWords[nBitsLeft >>> 5] |= 0x80 << (24 - nBitsLeft % 32);

  	            var nBitsTotalH = Math.floor(nBitsTotal / 0x100000000);
  	            var nBitsTotalL = nBitsTotal;
  	            dataWords[(((nBitsLeft + 64) >>> 9) << 4) + 15] = (
  	                (((nBitsTotalH << 8)  | (nBitsTotalH >>> 24)) & 0x00ff00ff) |
  	                (((nBitsTotalH << 24) | (nBitsTotalH >>> 8))  & 0xff00ff00)
  	            );
  	            dataWords[(((nBitsLeft + 64) >>> 9) << 4) + 14] = (
  	                (((nBitsTotalL << 8)  | (nBitsTotalL >>> 24)) & 0x00ff00ff) |
  	                (((nBitsTotalL << 24) | (nBitsTotalL >>> 8))  & 0xff00ff00)
  	            );

  	            data.sigBytes = (dataWords.length + 1) * 4;

  	            // Hash final blocks
  	            this._process();

  	            // Shortcuts
  	            var hash = this._hash;
  	            var H = hash.words;

  	            // Swap endian
  	            for (var i = 0; i < 4; i++) {
  	                // Shortcut
  	                var H_i = H[i];

  	                H[i] = (((H_i << 8)  | (H_i >>> 24)) & 0x00ff00ff) |
  	                       (((H_i << 24) | (H_i >>> 8))  & 0xff00ff00);
  	            }

  	            // Return final computed hash
  	            return hash;
  	        },

  	        clone: function () {
  	            var clone = Hasher.clone.call(this);
  	            clone._hash = this._hash.clone();

  	            return clone;
  	        }
  	    });

  	    function FF(a, b, c, d, x, s, t) {
  	        var n = a + ((b & c) | (~b & d)) + x + t;
  	        return ((n << s) | (n >>> (32 - s))) + b;
  	    }

  	    function GG(a, b, c, d, x, s, t) {
  	        var n = a + ((b & d) | (c & ~d)) + x + t;
  	        return ((n << s) | (n >>> (32 - s))) + b;
  	    }

  	    function HH(a, b, c, d, x, s, t) {
  	        var n = a + (b ^ c ^ d) + x + t;
  	        return ((n << s) | (n >>> (32 - s))) + b;
  	    }

  	    function II(a, b, c, d, x, s, t) {
  	        var n = a + (c ^ (b | ~d)) + x + t;
  	        return ((n << s) | (n >>> (32 - s))) + b;
  	    }

  	    /**
  	     * Shortcut function to the hasher's object interface.
  	     *
  	     * @param {WordArray|string} message The message to hash.
  	     *
  	     * @return {WordArray} The hash.
  	     *
  	     * @static
  	     *
  	     * @example
  	     *
  	     *     var hash = CryptoJS.MD5('message');
  	     *     var hash = CryptoJS.MD5(wordArray);
  	     */
  	    C.MD5 = Hasher._createHelper(MD5);

  	    /**
  	     * Shortcut function to the HMAC's object interface.
  	     *
  	     * @param {WordArray|string} message The message to hash.
  	     * @param {WordArray|string} key The secret key.
  	     *
  	     * @return {WordArray} The HMAC.
  	     *
  	     * @static
  	     *
  	     * @example
  	     *
  	     *     var hmac = CryptoJS.HmacMD5(message, key);
  	     */
  	    C.HmacMD5 = Hasher._createHmacHelper(MD5);
  	}(Math));


  	return CryptoJS.MD5;

  }));
  });

  var encBase64 = createCommonjsModule(function (module, exports) {
  (function (root, factory) {
  	{
  		// CommonJS
  		module.exports = exports = factory(core);
  	}
  }(commonjsGlobal, function (CryptoJS) {

  	(function () {
  	    // Shortcuts
  	    var C = CryptoJS;
  	    var C_lib = C.lib;
  	    var WordArray = C_lib.WordArray;
  	    var C_enc = C.enc;

  	    /**
  	     * Base64 encoding strategy.
  	     */
  	    var Base64 = C_enc.Base64 = {
  	        /**
  	         * Converts a word array to a Base64 string.
  	         *
  	         * @param {WordArray} wordArray The word array.
  	         *
  	         * @return {string} The Base64 string.
  	         *
  	         * @static
  	         *
  	         * @example
  	         *
  	         *     var base64String = CryptoJS.enc.Base64.stringify(wordArray);
  	         */
  	        stringify: function (wordArray) {
  	            // Shortcuts
  	            var words = wordArray.words;
  	            var sigBytes = wordArray.sigBytes;
  	            var map = this._map;

  	            // Clamp excess bits
  	            wordArray.clamp();

  	            // Convert
  	            var base64Chars = [];
  	            for (var i = 0; i < sigBytes; i += 3) {
  	                var byte1 = (words[i >>> 2]       >>> (24 - (i % 4) * 8))       & 0xff;
  	                var byte2 = (words[(i + 1) >>> 2] >>> (24 - ((i + 1) % 4) * 8)) & 0xff;
  	                var byte3 = (words[(i + 2) >>> 2] >>> (24 - ((i + 2) % 4) * 8)) & 0xff;

  	                var triplet = (byte1 << 16) | (byte2 << 8) | byte3;

  	                for (var j = 0; (j < 4) && (i + j * 0.75 < sigBytes); j++) {
  	                    base64Chars.push(map.charAt((triplet >>> (6 * (3 - j))) & 0x3f));
  	                }
  	            }

  	            // Add padding
  	            var paddingChar = map.charAt(64);
  	            if (paddingChar) {
  	                while (base64Chars.length % 4) {
  	                    base64Chars.push(paddingChar);
  	                }
  	            }

  	            return base64Chars.join('');
  	        },

  	        /**
  	         * Converts a Base64 string to a word array.
  	         *
  	         * @param {string} base64Str The Base64 string.
  	         *
  	         * @return {WordArray} The word array.
  	         *
  	         * @static
  	         *
  	         * @example
  	         *
  	         *     var wordArray = CryptoJS.enc.Base64.parse(base64String);
  	         */
  	        parse: function (base64Str) {
  	            // Shortcuts
  	            var base64StrLength = base64Str.length;
  	            var map = this._map;
  	            var reverseMap = this._reverseMap;

  	            if (!reverseMap) {
  	                    reverseMap = this._reverseMap = [];
  	                    for (var j = 0; j < map.length; j++) {
  	                        reverseMap[map.charCodeAt(j)] = j;
  	                    }
  	            }

  	            // Ignore padding
  	            var paddingChar = map.charAt(64);
  	            if (paddingChar) {
  	                var paddingIndex = base64Str.indexOf(paddingChar);
  	                if (paddingIndex !== -1) {
  	                    base64StrLength = paddingIndex;
  	                }
  	            }

  	            // Convert
  	            return parseLoop(base64Str, base64StrLength, reverseMap);

  	        },

  	        _map: 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/='
  	    };

  	    function parseLoop(base64Str, base64StrLength, reverseMap) {
  	      var words = [];
  	      var nBytes = 0;
  	      for (var i = 0; i < base64StrLength; i++) {
  	          if (i % 4) {
  	              var bits1 = reverseMap[base64Str.charCodeAt(i - 1)] << ((i % 4) * 2);
  	              var bits2 = reverseMap[base64Str.charCodeAt(i)] >>> (6 - (i % 4) * 2);
  	              var bitsCombined = bits1 | bits2;
  	              words[nBytes >>> 2] |= bitsCombined << (24 - (nBytes % 4) * 8);
  	              nBytes++;
  	          }
  	      }
  	      return WordArray.create(words, nBytes);
  	    }
  	}());


  	return CryptoJS.enc.Base64;

  }));
  });

  var encHex = createCommonjsModule(function (module, exports) {
  (function (root, factory) {
  	{
  		// CommonJS
  		module.exports = exports = factory(core);
  	}
  }(commonjsGlobal, function (CryptoJS) {

  	return CryptoJS.enc.Hex;

  }));
  });

  var encUtf8 = createCommonjsModule(function (module, exports) {
  (function (root, factory) {
  	{
  		// CommonJS
  		module.exports = exports = factory(core);
  	}
  }(commonjsGlobal, function (CryptoJS) {

  	return CryptoJS.enc.Utf8;

  }));
  });

  function getEnc(coding) {
    switch (coding) {
      case 'utf-8':
        return encUtf8;

      case 'base64':
        return encBase64;

      case 'hex':
        return encHex;

      default:
        throw new TosClientError('The coding is not supported');
    }
  }

  function decode$1(v, decoding) {
    if (!decoding) {
      return v;
    }

    return v.toString(getEnc(decoding));
  }

  const hmacSha256$1 = function hmacSha256$1(key, message, decoding) {
    return decode$1(hmacSha256(message, key), decoding);
  };
  const hashSha256 = function hashSha256(message, decoding) {
    return decode$1(sha256(message), decoding);
  };
  const hashMd5 = function hashMd5(message, decoding) {
    if (isBuffer$2(message)) {
      throw new TosClientError('not support buffer in browser environment');
    }

    return decode$1(md5(message), decoding);
  };
  const parse$2 = function parse(str, encoding) {
    return getEnc(encoding).parse(str);
  };
  const stringify$1 = function stringify(str, decoding) {
    return getEnc(decoding).stringify(str);
  };

  var cryptoBrowser = {
    __proto__: null,
    hmacSha256: hmacSha256$1,
    hashSha256: hashSha256,
    hashMd5: hashMd5,
    parse: parse$2,
    stringify: stringify$1
  };

  let crypto = null;

  {
    crypto = cryptoBrowser;
  }

  const {
    hmacSha256: hmacSha256$2,
    hashSha256: hashSha256$1,
    hashMd5: hashMd5$1,
    parse: parse$3,
    stringify: stringify$2
  } = crypto;

  async function _uploadPart(input) {
    const {
      uploadId,
      partNumber,
      body,
      enableContentMD5 = false
    } = input;
    const headers = normalizeHeadersKey(input.headers);
    input.headers = headers;
    fillRequestHeaders(input, ['trafficLimit', 'ssecAlgorithm', 'ssecKey', 'ssecKeyMD5']);
    const size = getSize(body);

    if (size && headers['content-length'] == null) {
      headers['content-length'] = size.toFixed(0);
    }

    if (enableContentMD5 && headers['content-md5'] == null) {
      // current only support in nodejs
      {
        console.warn(`current not support enableMD5Checksum`);
      }
    }

    const totalSize = getSize(input.body, headers);
    const totalSizeValid = totalSize != null;

    if (!totalSizeValid && (input.dataTransferStatusChange || input.progress)) {
      console.warn(`Don't get totalSize of uploadPart's body, the \`dataTransferStatusChange\` callback will not trigger. You can use \`uploadPartFromFile\` instead`);
    }

    let consumedBytes = 0;
    const {
      dataTransferStatusChange,
      progress
    } = input;

    const triggerDataTransfer = (type, rwOnceBytes = 0) => {
      // request cancel will make rwOnceBytes < 0 in browser
      if (!totalSizeValid || rwOnceBytes < 0) {
        return;
      }

      if (!dataTransferStatusChange && !progress) {
        return;
      }

      consumedBytes += rwOnceBytes;
      dataTransferStatusChange == null ? void 0 : dataTransferStatusChange({
        type,
        rwOnceBytes,
        consumedBytes,
        totalBytes: totalSize
      });

      const progressValue = (() => {
        if (totalSize === 0) {
          if (type === exports.DataTransferType.Succeed) {
            return 1;
          }

          return 0;
        }

        return consumedBytes / totalSize;
      })();

      if (progressValue === 1) {
        if (type === exports.DataTransferType.Succeed) {
          progress == null ? void 0 : progress(progressValue);
        }
      } else {
        progress == null ? void 0 : progress(progressValue);
      }
    };

    const bodyConfig = await getNewBodyConfig({
      body: input.body,
      dataTransferCallback: n => triggerDataTransfer(exports.DataTransferType.Rw, n),
      beforeRetry: input.beforeRetry,
      makeRetryStream: input.makeRetryStream,
      enableCRC: this.opts.enableCRC,
      rateLimiter: input.rateLimiter
    });
    triggerDataTransfer(exports.DataTransferType.Started);

    const task = async () => {
      const res = await this._fetchObject(input, 'PUT', {
        partNumber,
        uploadId
      }, headers, bodyConfig.body, {
        handleResponse: res => ({
          partNumber,
          ETag: res.headers.etag,
          serverSideEncryption: res.headers['x-tos-server-side-encryption'],
          serverSideDataEncryption: res.headers['x-tos-server-side-data-encryption'],
          serverSideEncryptionKeyId: res.headers['x-tos-server-side-encryption-kms-key-id'],
          ssecAlgorithm: res.headers['x-tos-server-side-encryption-customer-algorithm'],
          ssecKeyMD5: res.headers['x-tos-server-side-encryption-customer-key-MD5'],
          hashCrc64ecma: res.headers['x-tos-hash-crc64ecma']
        }),
        axiosOpts: {
          [retryNamespace]: {
            beforeRetry: () => {
              consumedBytes = 0;
              bodyConfig.beforeRetry == null ? void 0 : bodyConfig.beforeRetry();
            },
            makeRetryStream: bodyConfig.makeRetryStream
          },
          onUploadProgress: event => {
            triggerDataTransfer(exports.DataTransferType.Rw, event.loaded - consumedBytes);
          }
        }
      });

      if (this.opts.enableCRC && bodyConfig.crc) {
        checkCRC64WithHeaders(bodyConfig.crc, res.headers);
      }

      return res;
    };

    const [err, res] = await safeAwait(task()); // FAQ: no etag

    {
      if (res && !res.data.ETag) {
        throw new TosClientError("No ETag in uploadPart's response headers, please see https://www.volcengine.com/docs/6349/127737 to fix CORS problem");
      }
    }

    if (err || !res) {
      triggerDataTransfer(exports.DataTransferType.Failed);
      throw err;
    }

    triggerDataTransfer(exports.DataTransferType.Succeed);
    return res;
  }
  async function uploadPart(input) {
    return _uploadPart.call(this, input);
  }
  async function uploadPartFromFile(input) {

    {
      throw new TosClientError("uploadPartFromFile doesn't support in browser environment");
    }
  }

  async function completeMultipartUpload(input) {
    var _input$headers;

    input.headers = (_input$headers = input.headers) != null ? _input$headers : {};
    fillRequestHeaders(input, ['callback', 'callbackVar', 'forbidOverwrite']);

    const handleResponse = response => {
      const bucket = input.bucket || this.opts.bucket || '';
      const headers = response.headers;
      const result = { ...{
          VersionID: headers['x-tos-version-id'],
          ETag: headers['etag'],
          Bucket: bucket,
          Location: headers['location'],
          HashCrc64ecma: headers['x-tos-hash-crc64ecma'],
          Key: input.key
        },
        ...response.data
      };

      if (input.callback) {
        result.CallbackResult = `${JSON.stringify(response.data)}`;
      }

      return result;
    };

    if (input.completeAll) {
      var _input$parts;

      if (((_input$parts = input.parts) == null ? void 0 : _input$parts.length) > 0) {
        throw new TosClientError(`Should not specify both 'completeAll' and 'parts' params.`);
      }

      return this._fetchObject(input, 'POST', {
        uploadId: input.uploadId
      }, { ...input.headers,
        'x-tos-complete-all': 'yes'
      }, undefined, {
        handleResponse
      });
    }

    return this._fetchObject(input, 'POST', {
      uploadId: input.uploadId
    }, { ...input.headers
    }, {
      Parts: input.parts.map(it => ({
        ETag: it.eTag,
        PartNumber: it.partNumber
      }))
    }, {
      handleResponse
    });
  }

  (function (UploadEventType) {
    UploadEventType[UploadEventType["CreateMultipartUploadSucceed"] = 1] = "CreateMultipartUploadSucceed";
    UploadEventType[UploadEventType["CreateMultipartUploadFailed"] = 2] = "CreateMultipartUploadFailed";
    UploadEventType[UploadEventType["UploadPartSucceed"] = 3] = "UploadPartSucceed";
    UploadEventType[UploadEventType["UploadPartFailed"] = 4] = "UploadPartFailed";
    UploadEventType[UploadEventType["UploadPartAborted"] = 5] = "UploadPartAborted";
    UploadEventType[UploadEventType["CompleteMultipartUploadSucceed"] = 6] = "CompleteMultipartUploadSucceed";
    UploadEventType[UploadEventType["CompleteMultipartUploadFailed"] = 7] = "CompleteMultipartUploadFailed";
  })(exports.UploadEventType || (exports.UploadEventType = {}));

  const CHECKPOINT_FILE_NAME_PLACEHOLDER = '@@checkpoint-file-placeholder@@';
  const FILE_PARAM_CHECK_MSG = '`file` must be string, Buffer, File or Blob';
  const ABORT_ERROR_STATUS_CODE = [403, 404, 405];
  async function uploadFile(input) {
    var _checkpointRichInfo$r3, _checkpointRichInfo$r4, _checkpointRichInfo$r5;

    const {
      cancelToken,
      enableContentMD5 = false
    } = input;
    const headers = normalizeHeadersKey(input.headers);
    input.headers = headers;
    fillRequestHeaders(input, ['encodingType', 'cacheControl', 'contentDisposition', 'contentEncoding', 'contentLanguage', 'contentType', 'expires', 'acl', 'grantFullControl', 'grantRead', 'grantReadAcp', 'grantWriteAcp', 'ssecAlgorithm', 'ssecKey', 'ssecKeyMD5', 'serverSideEncryption', 'serverSideDataEncryption', 'meta', 'websiteRedirectLocation', 'storageClass']);

    const isCancel = () => cancelToken && !!cancelToken.reason;
    const fileStats = await (async () => {

      return null;
    })();
    const fileSize = await (async () => {
      const {
        file
      } = input;

      if (fileStats) {
        return fileStats.size;
      }

      if (isBuffer$2(file)) {
        return file.length;
      }

      if (isBlob$1(file)) {
        return file.size;
      }

      throw new TosClientError(FILE_PARAM_CHECK_MSG);
    })();
    const checkpointRichInfo = await (async () => {

      if (typeof input.checkpoint === 'object') {
        return {
          record: input.checkpoint
        };
      }

      return {};
    })(); // check if file info is matched

    await (async () => {
      var _checkpointRichInfo$r;

      if (fileStats && (_checkpointRichInfo$r = checkpointRichInfo.record) != null && _checkpointRichInfo$r.file_info) {
        var _checkpointRichInfo$r2;

        const {
          last_modified,
          file_size
        } = (_checkpointRichInfo$r2 = checkpointRichInfo.record) == null ? void 0 : _checkpointRichInfo$r2.file_info;

        if (fileStats.mtimeMs !== last_modified || fileStats.size !== file_size) {
          console.warn(`The file has been modified since ${new Date(last_modified)}, so the checkpoint file is invalid, and specified file will be uploaded again.`);
          delete checkpointRichInfo.record;
        }
      }
    })();
    const partSize = calculateSafePartSize(fileSize, input.partSize || ((_checkpointRichInfo$r3 = checkpointRichInfo.record) == null ? void 0 : _checkpointRichInfo$r3.part_size) || DEFAULT_PART_SIZE, true); // check partSize is matched

    if (checkpointRichInfo.record && checkpointRichInfo.record.part_size !== partSize) {
      console.warn('The partSize param does not equal the partSize in checkpoint file, ' + 'so the checkpoint file is invalid, and specified file will be uploaded again.');
      delete checkpointRichInfo.record;
    }

    let bucket = input.bucket || this.opts.bucket || '';
    const key = input.key;
    let uploadId = '';
    let tasks = [];
    const allTasks = getAllTasks(fileSize, partSize);
    const initConsumedBytes = (((_checkpointRichInfo$r4 = checkpointRichInfo.record) == null ? void 0 : _checkpointRichInfo$r4.parts_info) || []).filter(it => it.is_completed).reduce((prev, it) => prev + it.part_size, 0);
    let consumedBytesForProgress = initConsumedBytes; // recorded tasks

    const recordedTasks = ((_checkpointRichInfo$r5 = checkpointRichInfo.record) == null ? void 0 : _checkpointRichInfo$r5.parts_info) || [];
    const recordedTaskMap = new Map();
    recordedTasks.forEach(it => recordedTaskMap.set(it.part_number, it));

    const getCheckpointContent = () => {
      const checkpointContent = {
        bucket,
        key,
        part_size: partSize,
        upload_id: uploadId,
        parts_info: recordedTasks
      };

      if (fileStats) {
        checkpointContent.file_info = {
          last_modified: fileStats.mtimeMs,
          file_size: fileStats.size
        };
      }

      return checkpointContent;
    };

    const triggerUploadEvent = e => {
      if (!input.uploadEventChange) {
        return;
      }

      const event = {
        bucket,
        uploadId,
        key,
        ...e
      };

      if (checkpointRichInfo.filePath) {
        event.checkpointFile = checkpointRichInfo.filePath;
      }

      input.uploadEventChange(event);
    };

    let TriggerProgressEventType;

    (function (TriggerProgressEventType) {
      TriggerProgressEventType[TriggerProgressEventType["start"] = 1] = "start";
      TriggerProgressEventType[TriggerProgressEventType["uploadPartSucceed"] = 2] = "uploadPartSucceed";
      TriggerProgressEventType[TriggerProgressEventType["completeMultipartUploadSucceed"] = 3] = "completeMultipartUploadSucceed";
    })(TriggerProgressEventType || (TriggerProgressEventType = {}));

    const triggerProgressEvent = type => {
      if (!input.progress) {
        return;
      }

      const percent = (() => {
        if (type === TriggerProgressEventType.start && fileSize === 0) {
          return 0;
        }

        return !fileSize ? 1 : consumedBytesForProgress / fileSize;
      })();

      if (consumedBytesForProgress === fileSize && type === TriggerProgressEventType.uploadPartSucceed) ; else {
        input.progress(percent, getCheckpointContent());
      }
    };

    let consumedBytes = initConsumedBytes;
    const {
      dataTransferStatusChange
    } = input;

    const triggerDataTransfer = (type, rwOnceBytes = 0) => {
      if (!dataTransferStatusChange) {
        return;
      }

      consumedBytes += rwOnceBytes;
      dataTransferStatusChange == null ? void 0 : dataTransferStatusChange({
        type,
        rwOnceBytes,
        consumedBytes,
        totalBytes: fileSize
      });
    };

    const writeCheckpointFile = makeSerialAsyncTask(async () => {
    });

    const rmCheckpointFile = async () => {
    };
    /**
     *
     * @param task one part task
     * @param uploadPartRes upload part failed if `uploadPartRes` is Error
     */


    const updateAfterUploadPart = async (task, uploadPartRes) => {
      let existRecordTask = recordedTaskMap.get(task.partNumber);

      if (!existRecordTask) {
        existRecordTask = {
          part_number: task.partNumber,
          offset: task.offset,
          part_size: task.partSize,
          is_completed: false,
          etag: '',
          hash_crc64ecma: ''
        };
        recordedTasks.push(existRecordTask);
        recordedTaskMap.set(existRecordTask.part_number, existRecordTask);
      }

      if (!uploadPartRes.err) {
        existRecordTask.is_completed = true;
        existRecordTask.etag = uploadPartRes.res.ETag;
        existRecordTask.hash_crc64ecma = uploadPartRes.res.hashCrc64ecma;
      }

      await writeCheckpointFile();
      const uploadPartInfo = {
        partNumber: existRecordTask.part_number,
        partSize: existRecordTask.part_size,
        offset: existRecordTask.offset
      };

      if (uploadPartRes.err) {
        const err = uploadPartRes.err;
        let type = exports.UploadEventType.UploadPartFailed;

        if (err instanceof TosServerError) {
          if (ABORT_ERROR_STATUS_CODE.includes(err.statusCode)) {
            type = exports.UploadEventType.UploadPartAborted;
          }
        }

        triggerUploadEvent({
          type,
          err,
          uploadPartInfo
        });
        return;
      }

      uploadPartInfo.etag = uploadPartRes.res.ETag;
      consumedBytesForProgress += uploadPartInfo.partSize;
      triggerUploadEvent({
        type: exports.UploadEventType.UploadPartSucceed,
        uploadPartInfo
      });
      triggerProgressEvent(TriggerProgressEventType.uploadPartSucceed);
    };

    if (checkpointRichInfo.record) {
      bucket = checkpointRichInfo.record.bucket;
      uploadId = checkpointRichInfo.record.upload_id; // checkpoint info exists, so need to calculate remain tasks

      const uploadedPartSet = new Set((checkpointRichInfo.record.parts_info || []).filter(it => it.is_completed).map(it => it.part_number));
      tasks = allTasks.filter(it => !uploadedPartSet.has(it.partNumber));
    } else {
      // createMultipartUpload will check bucket
      try {
        const {
          data: multipartRes
        } = await createMultipartUpload.call(this, input);

        if (isCancel()) {
          throw new CancelError('cancel uploadFile');
        }

        bucket = multipartRes.Bucket;
        uploadId = multipartRes.UploadId;

        if (checkpointRichInfo.filePathIsPlaceholder) {
          var _checkpointRichInfo$f;

          checkpointRichInfo.filePath = (_checkpointRichInfo$f = checkpointRichInfo.filePath) == null ? void 0 : _checkpointRichInfo$f.replace(`${CHECKPOINT_FILE_NAME_PLACEHOLDER}`, getDefaultCheckpointFilePath(bucket, key));
        }

        triggerUploadEvent({
          type: exports.UploadEventType.CreateMultipartUploadSucceed
        });
      } catch (_err) {
        const err = _err;
        triggerUploadEvent({
          type: exports.UploadEventType.CreateMultipartUploadFailed,
          err
        });
        throw err;
      }

      tasks = allTasks;
    }

    triggerProgressEvent(TriggerProgressEventType.start);

    const handleTasks = async () => {
      let firstErr = null;
      let index = 0; // TODO: how to test parallel does work, measure time is not right

      await Promise.all(Array.from({
        length: input.taskNum || 1
      }).map(async () => {
        while (true) {
          const currentIndex = index++;

          if (currentIndex >= tasks.length) {
            return;
          }

          const curTask = tasks[currentIndex];
          let consumedBytesThisTask = 0;
          const makeRetryStream = getMakeRetryStream();

          try {
            function getBody(file, task) {
              const {
                offset: start,
                partSize
              } = task;
              const end = start + partSize;

              if (makeRetryStream) {
                return makeRetryStream.make();
              }

              if (isBlob$1(file)) {
                return file.slice(start, end);
              }

              if (isBuffer$2(file)) {
                return file.slice(start, end);
              }

              throw new TosClientError(FILE_PARAM_CHECK_MSG);
            }

            const {
              data: uploadPartRes
            } = await _uploadPart.call(this, {
              bucket,
              key,
              uploadId,
              body: getBody(input.file, curTask),
              enableContentMD5,
              makeRetryStream: makeRetryStream == null ? void 0 : makeRetryStream.make,
              beforeRetry: () => {
                consumedBytes -= consumedBytesThisTask;
                consumedBytesThisTask = 0;
              },
              partNumber: curTask.partNumber,
              headers: {
                ['content-length']: `${curTask.partSize}`,
                ['x-tos-server-side-encryption-customer-algorithm']: headers['x-tos-server-side-encryption-customer-algorithm'],
                ['x-tos-server-side-encryption-customer-key']: headers['x-tos-server-side-encryption-customer-key'],
                ['x-tos-server-side-encryption-customer-key-md5']: headers['x-tos-server-side-encryption-customer-key-md5']
              },

              dataTransferStatusChange(status) {
                if (status.type !== exports.DataTransferType.Rw) {
                  return;
                }

                if (isCancel()) {
                  return;
                }

                consumedBytesThisTask += status.rwOnceBytes;
                triggerDataTransfer(status.type, status.rwOnceBytes);
              },

              trafficLimit: input.trafficLimit,
              rateLimiter: input.rateLimiter
            });

            if (isCancel()) {
              throw new CancelError('cancel uploadFile');
            }

            await updateAfterUploadPart(curTask, {
              res: uploadPartRes
            });
          } catch (_err) {
            tryDestroy(makeRetryStream == null ? void 0 : makeRetryStream.getLastStream(), _err);
            const err = _err;
            consumedBytes -= consumedBytesThisTask;
            consumedBytesThisTask = 0;

            if (isCancelError(err)) {
              throw err;
            }

            if (isCancel()) {
              throw new CancelError('cancel uploadFile');
            }

            if (!firstErr) {
              firstErr = err;
            }

            await updateAfterUploadPart(curTask, {
              err
            });
          }
        }
      }));

      if (firstErr) {
        throw firstErr;
      }

      const parts = (getCheckpointContent().parts_info || []).map(it => ({
        eTag: it.etag,
        partNumber: it.part_number
      }));
      const [err, res] = await safeAwait(completeMultipartUpload.call(this, {
        bucket,
        key,
        uploadId,
        parts
      }));

      if (err || !res) {
        triggerUploadEvent({
          type: exports.UploadEventType.CompleteMultipartUploadFailed
        });
        throw err;
      }

      triggerUploadEvent({
        type: exports.UploadEventType.CompleteMultipartUploadSucceed
      });
      triggerProgressEvent(TriggerProgressEventType.completeMultipartUploadSucceed);
      await rmCheckpointFile();

      if (this.opts.enableCRC && res.data.HashCrc64ecma && combineCRCInParts(getCheckpointContent()) !== res.data.HashCrc64ecma) {
        throw new TosClientError('crc of entire file mismatch.');
      }

      return res;
    };

    triggerDataTransfer(exports.DataTransferType.Started);
    const [err, res] = await safeAwait(handleTasks());

    if (err || !res) {
      triggerDataTransfer(exports.DataTransferType.Failed);
      throw err;
    }

    triggerDataTransfer(exports.DataTransferType.Succeed);
    return res;
  }
  /**
   * 即使 totalSize 是 0，也需要一个 Part，否则 Server 端会报错 read request body failed
   */

  function getAllTasks(totalSize, partSize) {
    const tasks = [];

    for (let i = 0;; ++i) {
      const offset = i * partSize;
      const currPartSize = Math.min(partSize, totalSize - offset);
      tasks.push({
        offset,
        partSize: currPartSize,
        partNumber: i + 1
      });

      if ((i + 1) * partSize >= totalSize) {
        break;
      }
    }

    return tasks;
  }

  function getMakeRetryStream(file, task) {

    return undefined;
  }

  function getDefaultCheckpointFilePath(bucket, key) {
    const originPath = `${key}.${hashMd5$1(`${bucket}.${key}`, 'hex')}.upload`;
    const normalizePath = originPath.replace(/[\\/]/g, '');
    return normalizePath;
  }

  function combineCRCInParts(cp) {
    var _cp$file_info, _cp$parts_info$sort, _cp$parts_info;

    const size = ((_cp$file_info = cp.file_info) == null ? void 0 : _cp$file_info.file_size) || 0;
    let res = '0';
    const sortedPartsInfo = (_cp$parts_info$sort = (_cp$parts_info = cp.parts_info) == null ? void 0 : _cp$parts_info.sort == null ? void 0 : _cp$parts_info.sort((a, b) => a.part_number - b.part_number)) != null ? _cp$parts_info$sort : [];

    for (const part of sortedPartsInfo) {
      res = combineCrc64(res, part.hash_crc64ecma, Math.min(part.part_size, size - part.offset));
    }

    return res;
  }

  (function (ACLType) {
    ACLType["ACLPrivate"] = "private";
    ACLType["ACLPublicRead"] = "public-read";
    ACLType["ACLPublicReadWrite"] = "public-read-write";
    ACLType["ACLAuthenticatedRead"] = "authenticated-read";
    ACLType["ACLBucketOwnerRead"] = "bucket-owner-read";
    ACLType["ACLBucketOwnerFullControl"] = "bucket-owner-full-control"; // only works for object ACL

    ACLType["ACLBucketOwnerEntrusted"] = "bucket-owner-entrusted";
    /**
     * @private unstable value for object ACL
     */

    ACLType["ACLDefault"] = "default";
  })(exports.ACLType || (exports.ACLType = {}));

  (function (StorageClassType) {
    // storage-class will inherit from bucket if uploading object without `x-tos-storage-class` header
    StorageClassType["StorageClassStandard"] = "STANDARD";
    StorageClassType["StorageClassIa"] = "IA";
    StorageClassType["StorageClassArchiveFr"] = "ARCHIVE_FR";
    StorageClassType["StorageClassColdArchive"] = "COLD_ARCHIVE";
    StorageClassType["StorageClassIntelligentTiering"] = "INTELLIGENT_TIERING";
    StorageClassType["StorageClassArchive"] = "ARCHIVE";
  })(exports.StorageClassType || (exports.StorageClassType = {}));

  (function (MetadataDirectiveType) {
    MetadataDirectiveType["MetadataDirectiveCopy"] = "COPY";
    MetadataDirectiveType["MetadataDirectiveReplace"] = "REPLACE";
  })(exports.MetadataDirectiveType || (exports.MetadataDirectiveType = {}));

  (function (AzRedundancyType) {
    AzRedundancyType["AzRedundancySingleAz"] = "single-az";
    AzRedundancyType["AzRedundancyMultiAz"] = "multi-az";
  })(exports.AzRedundancyType || (exports.AzRedundancyType = {}));

  (function (PermissionType) {
    PermissionType["PermissionRead"] = "READ";
    PermissionType["PermissionWrite"] = "WRITE";
    PermissionType["PermissionReadAcp"] = "READ_ACP";
    PermissionType["PermissionWriteAcp"] = "WRITE_ACP";
    PermissionType["PermissionFullControl"] = "FULL_CONTROL";
    /**
     * @private unstable value for ACL
     */

    PermissionType["PermissionReadNONLIST"] = "READ_NON_LIST";
  })(exports.PermissionType || (exports.PermissionType = {}));

  (function (GranteeType) {
    GranteeType["GranteeGroup"] = "Group";
    GranteeType["GranteeUser"] = "CanonicalUser";
  })(exports.GranteeType || (exports.GranteeType = {}));

  (function (CannedType) {
    CannedType["CannedAllUsers"] = "AllUsers";
    CannedType["CannedAuthenticatedUsers"] = "AuthenticatedUsers";
  })(exports.CannedType || (exports.CannedType = {}));

  (function (HttpMethodType) {
    HttpMethodType["HttpMethodGet"] = "GET";
    HttpMethodType["HttpMethodPut"] = "PUT";
    HttpMethodType["HttpMethodPost"] = "POST";
    HttpMethodType["HttpMethodDelete"] = "DELETE";
    HttpMethodType["HttpMethodHead"] = "HEAD";
  })(exports.HttpMethodType || (exports.HttpMethodType = {}));

  (function (StorageClassInheritDirectiveType) {
    StorageClassInheritDirectiveType["StorageClassInheritDirectiveDestinationBucket"] = "DESTINATION_BUCKET";
    StorageClassInheritDirectiveType["StorageClassInheritDirectiveSourceObject"] = "SOURCE_OBJECT";
  })(exports.StorageClassInheritDirectiveType || (exports.StorageClassInheritDirectiveType = {}));

  (function (ReplicationStatusType) {
    ReplicationStatusType["Complete"] = "COMPLETE";
    ReplicationStatusType["Pending"] = "PENDING";
    ReplicationStatusType["Failed"] = "FAILED";
    ReplicationStatusType["Replica"] = "REPLICA";
  })(exports.ReplicationStatusType || (exports.ReplicationStatusType = {}));

  (function (LifecycleStatusType) {
    LifecycleStatusType["Enabled"] = "Enabled";
    LifecycleStatusType["Disabled"] = "Disabled";
  })(exports.LifecycleStatusType || (exports.LifecycleStatusType = {}));

  (function (RedirectType) {
    RedirectType["Mirror"] = "Mirror";
    RedirectType["Async"] = "Async";
  })(exports.RedirectType || (exports.RedirectType = {}));

  (function (StatusType) {
    StatusType["Enabled"] = "Enabled";
    StatusType["Disabled"] = "Disabled";
  })(exports.StatusType || (exports.StatusType = {}));

  (function (TierType) {
    TierType["TierStandard"] = "Standard";
    TierType["TierExpedited"] = "Expedited";
    TierType["TierBulk"] = "Bulk";
  })(exports.TierType || (exports.TierType = {}));

  (function (VersioningStatusType) {
    VersioningStatusType["Enabled"] = "Enabled";
    VersioningStatusType["Suspended"] = "Suspended";
    VersioningStatusType["NotSet"] = "";
    /**
     * @deprecated use `Enabled` instead
     */

    VersioningStatusType["Enable"] = "Enabled";
    /**
     * @deprecated use `NotSet` instead
     */

    VersioningStatusType["Disable"] = "";
  })(exports.VersioningStatusType || (exports.VersioningStatusType = {}));

  (function (AccessPointStatusType) {
    AccessPointStatusType["Ready"] = "READY";
    AccessPointStatusType["Creating"] = "CREATING";
    AccessPointStatusType["Created"] = "CREATED";
    AccessPointStatusType["Deleting"] = "DELETING";
  })(exports.AccessPointStatusType || (exports.AccessPointStatusType = {}));

  (function (TransferAccelerationStatusType) {
    TransferAccelerationStatusType["Activating"] = "AccelerationActivating";
    TransferAccelerationStatusType["Activated"] = "AccelerationActivated";
    TransferAccelerationStatusType["Terminated"] = "AccelerationTerminated";
  })(exports.TransferAccelerationStatusType || (exports.TransferAccelerationStatusType = {}));

  (function (MRAPMirrorBackRedirectPolicyType) {
    MRAPMirrorBackRedirectPolicyType["ClosestFirst"] = "Closest-First";
    MRAPMirrorBackRedirectPolicyType["LatestFirst"] = "Latest-First";
  })(exports.MRAPMirrorBackRedirectPolicyType || (exports.MRAPMirrorBackRedirectPolicyType = {}));

  async function headObject(input) {
    const normalizedInput = typeof input === 'string' ? {
      key: input
    } : input;
    const headers = normalizeHeadersKey(normalizedInput.headers);
    normalizedInput.headers = headers;
    const query = {};

    if (normalizedInput.versionId) {
      query.versionId = normalizedInput.versionId;
    }

    fillRequestHeaders(normalizedInput, ['ifMatch', 'ifModifiedSince', 'ifNoneMatch', 'ifUnmodifiedSince', 'ssecAlgorithm', 'ssecKey', 'ssecKeyMD5']);
    return this._fetchObject(input, 'HEAD', query, (normalizedInput == null ? void 0 : normalizedInput.headers) || {}, undefined, {
      handleResponse: res => {
        const result = { ...res.headers,
          ReplicationStatus: res.headers[TosHeader.HeaderReplicationStatus]
        };
        const info = getRestoreInfoFromHeaders(res.headers);

        if (info) {
          result.RestoreInfo = info;
        }

        return result;
      }
    });
  }

  async function uploadPartCopy(input) {
    const {
      uploadId,
      partNumber
    } = input;
    const headers = normalizeHeadersKey(input.headers);
    input.headers = headers;
    fillRequestHeaders(input, ['copySourceRange', 'copySourceSSECAlgorithm', 'copySourceSSECKey', 'copySourceSSECKeyMD5', 'ssecAlgorithm', 'ssecKey', 'ssecKeyMD5', 'trafficLimit']);

    if (input.srcBucket && input.srcKey) {
      var _headers$xTosCopyS;

      let copySource = getCopySourceHeaderValue(input.srcBucket, input.srcKey);

      if (input.srcVersionID) {
        copySource += `?versionId=${input.srcVersionID}`;
      }

      headers['x-tos-copy-source'] = (_headers$xTosCopyS = headers['x-tos-copy-source']) != null ? _headers$xTosCopyS : copySource;
    }

    if (input.copySourceRange == null && (input.copySourceRangeStart != null || input.copySourceRangeEnd != null)) {
      var _headers$xTosCopyS2;

      const start = input.copySourceRangeStart != null ? `${input.copySourceRangeStart}` : '';
      const end = input.copySourceRangeEnd != null ? `${input.copySourceRangeEnd}` : '';
      const copyRange = `bytes=${start}-${end}`;
      headers['x-tos-copy-source-range'] = (_headers$xTosCopyS2 = headers['x-tos-copy-source-range']) != null ? _headers$xTosCopyS2 : copyRange;
    }

    const [err, res] = await safeAwait(this._fetchObject(input, 'PUT', {
      partNumber,
      uploadId
    }, headers, undefined, {
      handleResponse(response) {
        return { ...response.data,
          SSECAlgorithm: response.headers[requestHeadersMap['ssecAlgorithm']],
          SSECKeyMD5: response.headers[requestHeadersMap['ssecKeyMD5']]
        };
      }

    }));

    if (err || !res || !res.data.ETag) {
      // TODO: throw TosServerErr
      throw err;
    }

    return res;
  }

  async function copyObject(input) {
    const headers = normalizeHeadersKey(input.headers);
    input.headers = headers;
    fillRequestHeaders(input, ['cacheControl', 'contentDisposition', 'contentEncoding', 'contentLanguage', 'contentType', 'expires', 'copySourceIfMatch', 'copySourceIfModifiedSince', 'copySourceIfNoneMatch', 'copySourceIfUnmodifiedSince', 'copySourceSSECAlgorithm', 'copySourceSSECKey', 'copySourceSSECKeyMD5', 'acl', 'grantFullControl', 'grantRead', 'grantReadAcp', 'grantWriteAcp', 'ssecAlgorithm', 'ssecKey', 'ssecKeyMD5', 'serverSideEncryption', 'metadataDirective', 'meta', 'websiteRedirectLocation', 'storageClass', 'trafficLimit', 'forbidOverwrite', 'ifMatch']);

    if (input.srcBucket && input.srcKey) {
      var _headers$xTosCopyS;

      let copySource = getCopySourceHeaderValue(input.srcBucket, input.srcKey);

      if (input.srcVersionID) {
        copySource += `?versionId=${input.srcVersionID}`;
      }

      headers['x-tos-copy-source'] = (_headers$xTosCopyS = headers['x-tos-copy-source']) != null ? _headers$xTosCopyS : copySource;
    }

    const [err, res] = await safeAwait(this._fetchObject(input, 'PUT', {}, headers));

    if (err || !res || !res.data.ETag) {
      // TODO: throw TosServerErr
      throw err;
    }

    return res;
  }

  /**
   * Removes all key-value entries from the stack.
   *
   * @private
   * @name clear
   * @memberOf Stack
   */
  function stackClear() {
    this.__data__ = new _ListCache;
    this.size = 0;
  }

  var _stackClear = stackClear;

  /**
   * Removes `key` and its value from the stack.
   *
   * @private
   * @name delete
   * @memberOf Stack
   * @param {string} key The key of the value to remove.
   * @returns {boolean} Returns `true` if the entry was removed, else `false`.
   */
  function stackDelete(key) {
    var data = this.__data__,
        result = data['delete'](key);

    this.size = data.size;
    return result;
  }

  var _stackDelete = stackDelete;

  /**
   * Gets the stack value for `key`.
   *
   * @private
   * @name get
   * @memberOf Stack
   * @param {string} key The key of the value to get.
   * @returns {*} Returns the entry value.
   */
  function stackGet(key) {
    return this.__data__.get(key);
  }

  var _stackGet = stackGet;

  /**
   * Checks if a stack value for `key` exists.
   *
   * @private
   * @name has
   * @memberOf Stack
   * @param {string} key The key of the entry to check.
   * @returns {boolean} Returns `true` if an entry for `key` exists, else `false`.
   */
  function stackHas(key) {
    return this.__data__.has(key);
  }

  var _stackHas = stackHas;

  /** Used as the size to enable large array optimizations. */
  var LARGE_ARRAY_SIZE = 200;

  /**
   * Sets the stack `key` to `value`.
   *
   * @private
   * @name set
   * @memberOf Stack
   * @param {string} key The key of the value to set.
   * @param {*} value The value to set.
   * @returns {Object} Returns the stack cache instance.
   */
  function stackSet(key, value) {
    var data = this.__data__;
    if (data instanceof _ListCache) {
      var pairs = data.__data__;
      if (!_Map || (pairs.length < LARGE_ARRAY_SIZE - 1)) {
        pairs.push([key, value]);
        this.size = ++data.size;
        return this;
      }
      data = this.__data__ = new _MapCache(pairs);
    }
    data.set(key, value);
    this.size = data.size;
    return this;
  }

  var _stackSet = stackSet;

  /**
   * Creates a stack cache object to store key-value pairs.
   *
   * @private
   * @constructor
   * @param {Array} [entries] The key-value pairs to cache.
   */
  function Stack(entries) {
    var data = this.__data__ = new _ListCache(entries);
    this.size = data.size;
  }

  // Add methods to `Stack`.
  Stack.prototype.clear = _stackClear;
  Stack.prototype['delete'] = _stackDelete;
  Stack.prototype.get = _stackGet;
  Stack.prototype.has = _stackHas;
  Stack.prototype.set = _stackSet;

  var _Stack = Stack;

  /**
   * A specialized version of `_.forEach` for arrays without support for
   * iteratee shorthands.
   *
   * @private
   * @param {Array} [array] The array to iterate over.
   * @param {Function} iteratee The function invoked per iteration.
   * @returns {Array} Returns `array`.
   */
  function arrayEach(array, iteratee) {
    var index = -1,
        length = array == null ? 0 : array.length;

    while (++index < length) {
      if (iteratee(array[index], index, array) === false) {
        break;
      }
    }
    return array;
  }

  var _arrayEach = arrayEach;

  /**
   * Copies properties of `source` to `object`.
   *
   * @private
   * @param {Object} source The object to copy properties from.
   * @param {Array} props The property identifiers to copy.
   * @param {Object} [object={}] The object to copy properties to.
   * @param {Function} [customizer] The function to customize copied values.
   * @returns {Object} Returns `object`.
   */
  function copyObject$1(source, props, object, customizer) {
    var isNew = !object;
    object || (object = {});

    var index = -1,
        length = props.length;

    while (++index < length) {
      var key = props[index];

      var newValue = customizer
        ? customizer(object[key], source[key], key, object, source)
        : undefined;

      if (newValue === undefined) {
        newValue = source[key];
      }
      if (isNew) {
        _baseAssignValue(object, key, newValue);
      } else {
        _assignValue(object, key, newValue);
      }
    }
    return object;
  }

  var _copyObject = copyObject$1;

  /**
   * The base implementation of `_.times` without support for iteratee shorthands
   * or max array length checks.
   *
   * @private
   * @param {number} n The number of times to invoke `iteratee`.
   * @param {Function} iteratee The function invoked per iteration.
   * @returns {Array} Returns the array of results.
   */
  function baseTimes(n, iteratee) {
    var index = -1,
        result = Array(n);

    while (++index < n) {
      result[index] = iteratee(index);
    }
    return result;
  }

  var _baseTimes = baseTimes;

  /** `Object#toString` result references. */
  var argsTag = '[object Arguments]';

  /**
   * The base implementation of `_.isArguments`.
   *
   * @private
   * @param {*} value The value to check.
   * @returns {boolean} Returns `true` if `value` is an `arguments` object,
   */
  function baseIsArguments(value) {
    return isObjectLike_1(value) && _baseGetTag(value) == argsTag;
  }

  var _baseIsArguments = baseIsArguments;

  /** Used for built-in method references. */
  var objectProto$6 = Object.prototype;

  /** Used to check objects for own properties. */
  var hasOwnProperty$5 = objectProto$6.hasOwnProperty;

  /** Built-in value references. */
  var propertyIsEnumerable = objectProto$6.propertyIsEnumerable;

  /**
   * Checks if `value` is likely an `arguments` object.
   *
   * @static
   * @memberOf _
   * @since 0.1.0
   * @category Lang
   * @param {*} value The value to check.
   * @returns {boolean} Returns `true` if `value` is an `arguments` object,
   *  else `false`.
   * @example
   *
   * _.isArguments(function() { return arguments; }());
   * // => true
   *
   * _.isArguments([1, 2, 3]);
   * // => false
   */
  var isArguments = _baseIsArguments(function() { return arguments; }()) ? _baseIsArguments : function(value) {
    return isObjectLike_1(value) && hasOwnProperty$5.call(value, 'callee') &&
      !propertyIsEnumerable.call(value, 'callee');
  };

  var isArguments_1 = isArguments;

  /**
   * This method returns `false`.
   *
   * @static
   * @memberOf _
   * @since 4.13.0
   * @category Util
   * @returns {boolean} Returns `false`.
   * @example
   *
   * _.times(2, _.stubFalse);
   * // => [false, false]
   */
  function stubFalse() {
    return false;
  }

  var stubFalse_1 = stubFalse;

  var isBuffer_1 = createCommonjsModule(function (module, exports) {
  /** Detect free variable `exports`. */
  var freeExports =  exports && !exports.nodeType && exports;

  /** Detect free variable `module`. */
  var freeModule = freeExports && 'object' == 'object' && module && !module.nodeType && module;

  /** Detect the popular CommonJS extension `module.exports`. */
  var moduleExports = freeModule && freeModule.exports === freeExports;

  /** Built-in value references. */
  var Buffer = moduleExports ? _root.Buffer : undefined;

  /* Built-in method references for those with the same name as other `lodash` methods. */
  var nativeIsBuffer = Buffer ? Buffer.isBuffer : undefined;

  /**
   * Checks if `value` is a buffer.
   *
   * @static
   * @memberOf _
   * @since 4.3.0
   * @category Lang
   * @param {*} value The value to check.
   * @returns {boolean} Returns `true` if `value` is a buffer, else `false`.
   * @example
   *
   * _.isBuffer(new Buffer(2));
   * // => true
   *
   * _.isBuffer(new Uint8Array(2));
   * // => false
   */
  var isBuffer = nativeIsBuffer || stubFalse_1;

  module.exports = isBuffer;
  });

  /** Used as references for various `Number` constants. */
  var MAX_SAFE_INTEGER$1 = 9007199254740991;

  /**
   * Checks if `value` is a valid array-like length.
   *
   * **Note:** This method is loosely based on
   * [`ToLength`](http://ecma-international.org/ecma-262/7.0/#sec-tolength).
   *
   * @static
   * @memberOf _
   * @since 4.0.0
   * @category Lang
   * @param {*} value The value to check.
   * @returns {boolean} Returns `true` if `value` is a valid length, else `false`.
   * @example
   *
   * _.isLength(3);
   * // => true
   *
   * _.isLength(Number.MIN_VALUE);
   * // => false
   *
   * _.isLength(Infinity);
   * // => false
   *
   * _.isLength('3');
   * // => false
   */
  function isLength(value) {
    return typeof value == 'number' &&
      value > -1 && value % 1 == 0 && value <= MAX_SAFE_INTEGER$1;
  }

  var isLength_1 = isLength;

  /** `Object#toString` result references. */
  var argsTag$1 = '[object Arguments]',
      arrayTag = '[object Array]',
      boolTag = '[object Boolean]',
      dateTag = '[object Date]',
      errorTag = '[object Error]',
      funcTag$1 = '[object Function]',
      mapTag = '[object Map]',
      numberTag = '[object Number]',
      objectTag = '[object Object]',
      regexpTag = '[object RegExp]',
      setTag = '[object Set]',
      stringTag = '[object String]',
      weakMapTag = '[object WeakMap]';

  var arrayBufferTag = '[object ArrayBuffer]',
      dataViewTag = '[object DataView]',
      float32Tag = '[object Float32Array]',
      float64Tag = '[object Float64Array]',
      int8Tag = '[object Int8Array]',
      int16Tag = '[object Int16Array]',
      int32Tag = '[object Int32Array]',
      uint8Tag = '[object Uint8Array]',
      uint8ClampedTag = '[object Uint8ClampedArray]',
      uint16Tag = '[object Uint16Array]',
      uint32Tag = '[object Uint32Array]';

  /** Used to identify `toStringTag` values of typed arrays. */
  var typedArrayTags = {};
  typedArrayTags[float32Tag] = typedArrayTags[float64Tag] =
  typedArrayTags[int8Tag] = typedArrayTags[int16Tag] =
  typedArrayTags[int32Tag] = typedArrayTags[uint8Tag] =
  typedArrayTags[uint8ClampedTag] = typedArrayTags[uint16Tag] =
  typedArrayTags[uint32Tag] = true;
  typedArrayTags[argsTag$1] = typedArrayTags[arrayTag] =
  typedArrayTags[arrayBufferTag] = typedArrayTags[boolTag] =
  typedArrayTags[dataViewTag] = typedArrayTags[dateTag] =
  typedArrayTags[errorTag] = typedArrayTags[funcTag$1] =
  typedArrayTags[mapTag] = typedArrayTags[numberTag] =
  typedArrayTags[objectTag] = typedArrayTags[regexpTag] =
  typedArrayTags[setTag] = typedArrayTags[stringTag] =
  typedArrayTags[weakMapTag] = false;

  /**
   * The base implementation of `_.isTypedArray` without Node.js optimizations.
   *
   * @private
   * @param {*} value The value to check.
   * @returns {boolean} Returns `true` if `value` is a typed array, else `false`.
   */
  function baseIsTypedArray(value) {
    return isObjectLike_1(value) &&
      isLength_1(value.length) && !!typedArrayTags[_baseGetTag(value)];
  }

  var _baseIsTypedArray = baseIsTypedArray;

  /**
   * The base implementation of `_.unary` without support for storing metadata.
   *
   * @private
   * @param {Function} func The function to cap arguments for.
   * @returns {Function} Returns the new capped function.
   */
  function baseUnary(func) {
    return function(value) {
      return func(value);
    };
  }

  var _baseUnary = baseUnary;

  var _nodeUtil = createCommonjsModule(function (module, exports) {
  /** Detect free variable `exports`. */
  var freeExports =  exports && !exports.nodeType && exports;

  /** Detect free variable `module`. */
  var freeModule = freeExports && 'object' == 'object' && module && !module.nodeType && module;

  /** Detect the popular CommonJS extension `module.exports`. */
  var moduleExports = freeModule && freeModule.exports === freeExports;

  /** Detect free variable `process` from Node.js. */
  var freeProcess = moduleExports && _freeGlobal.process;

  /** Used to access faster Node.js helpers. */
  var nodeUtil = (function() {
    try {
      // Use `util.types` for Node.js 10+.
      var types = freeModule && freeModule.require && freeModule.require('util').types;

      if (types) {
        return types;
      }

      // Legacy `process.binding('util')` for Node.js < 10.
      return freeProcess && freeProcess.binding && freeProcess.binding('util');
    } catch (e) {}
  }());

  module.exports = nodeUtil;
  });

  /* Node.js helper references. */
  var nodeIsTypedArray = _nodeUtil && _nodeUtil.isTypedArray;

  /**
   * Checks if `value` is classified as a typed array.
   *
   * @static
   * @memberOf _
   * @since 3.0.0
   * @category Lang
   * @param {*} value The value to check.
   * @returns {boolean} Returns `true` if `value` is a typed array, else `false`.
   * @example
   *
   * _.isTypedArray(new Uint8Array);
   * // => true
   *
   * _.isTypedArray([]);
   * // => false
   */
  var isTypedArray = nodeIsTypedArray ? _baseUnary(nodeIsTypedArray) : _baseIsTypedArray;

  var isTypedArray_1 = isTypedArray;

  /** Used for built-in method references. */
  var objectProto$7 = Object.prototype;

  /** Used to check objects for own properties. */
  var hasOwnProperty$6 = objectProto$7.hasOwnProperty;

  /**
   * Creates an array of the enumerable property names of the array-like `value`.
   *
   * @private
   * @param {*} value The value to query.
   * @param {boolean} inherited Specify returning inherited property names.
   * @returns {Array} Returns the array of property names.
   */
  function arrayLikeKeys(value, inherited) {
    var isArr = isArray_1(value),
        isArg = !isArr && isArguments_1(value),
        isBuff = !isArr && !isArg && isBuffer_1(value),
        isType = !isArr && !isArg && !isBuff && isTypedArray_1(value),
        skipIndexes = isArr || isArg || isBuff || isType,
        result = skipIndexes ? _baseTimes(value.length, String) : [],
        length = result.length;

    for (var key in value) {
      if ((inherited || hasOwnProperty$6.call(value, key)) &&
          !(skipIndexes && (
             // Safari 9 has enumerable `arguments.length` in strict mode.
             key == 'length' ||
             // Node.js 0.10 has enumerable non-index properties on buffers.
             (isBuff && (key == 'offset' || key == 'parent')) ||
             // PhantomJS 2 has enumerable non-index properties on typed arrays.
             (isType && (key == 'buffer' || key == 'byteLength' || key == 'byteOffset')) ||
             // Skip index properties.
             _isIndex(key, length)
          ))) {
        result.push(key);
      }
    }
    return result;
  }

  var _arrayLikeKeys = arrayLikeKeys;

  /** Used for built-in method references. */
  var objectProto$8 = Object.prototype;

  /**
   * Checks if `value` is likely a prototype object.
   *
   * @private
   * @param {*} value The value to check.
   * @returns {boolean} Returns `true` if `value` is a prototype, else `false`.
   */
  function isPrototype(value) {
    var Ctor = value && value.constructor,
        proto = (typeof Ctor == 'function' && Ctor.prototype) || objectProto$8;

    return value === proto;
  }

  var _isPrototype = isPrototype;

  /**
   * Creates a unary function that invokes `func` with its argument transformed.
   *
   * @private
   * @param {Function} func The function to wrap.
   * @param {Function} transform The argument transform.
   * @returns {Function} Returns the new function.
   */
  function overArg(func, transform) {
    return function(arg) {
      return func(transform(arg));
    };
  }

  var _overArg = overArg;

  /* Built-in method references for those with the same name as other `lodash` methods. */
  var nativeKeys = _overArg(Object.keys, Object);

  var _nativeKeys = nativeKeys;

  /** Used for built-in method references. */
  var objectProto$9 = Object.prototype;

  /** Used to check objects for own properties. */
  var hasOwnProperty$7 = objectProto$9.hasOwnProperty;

  /**
   * The base implementation of `_.keys` which doesn't treat sparse arrays as dense.
   *
   * @private
   * @param {Object} object The object to query.
   * @returns {Array} Returns the array of property names.
   */
  function baseKeys(object) {
    if (!_isPrototype(object)) {
      return _nativeKeys(object);
    }
    var result = [];
    for (var key in Object(object)) {
      if (hasOwnProperty$7.call(object, key) && key != 'constructor') {
        result.push(key);
      }
    }
    return result;
  }

  var _baseKeys = baseKeys;

  /**
   * Checks if `value` is array-like. A value is considered array-like if it's
   * not a function and has a `value.length` that's an integer greater than or
   * equal to `0` and less than or equal to `Number.MAX_SAFE_INTEGER`.
   *
   * @static
   * @memberOf _
   * @since 4.0.0
   * @category Lang
   * @param {*} value The value to check.
   * @returns {boolean} Returns `true` if `value` is array-like, else `false`.
   * @example
   *
   * _.isArrayLike([1, 2, 3]);
   * // => true
   *
   * _.isArrayLike(document.body.children);
   * // => true
   *
   * _.isArrayLike('abc');
   * // => true
   *
   * _.isArrayLike(_.noop);
   * // => false
   */
  function isArrayLike(value) {
    return value != null && isLength_1(value.length) && !isFunction_1(value);
  }

  var isArrayLike_1 = isArrayLike;

  /**
   * Creates an array of the own enumerable property names of `object`.
   *
   * **Note:** Non-object values are coerced to objects. See the
   * [ES spec](http://ecma-international.org/ecma-262/7.0/#sec-object.keys)
   * for more details.
   *
   * @static
   * @since 0.1.0
   * @memberOf _
   * @category Object
   * @param {Object} object The object to query.
   * @returns {Array} Returns the array of property names.
   * @example
   *
   * function Foo() {
   *   this.a = 1;
   *   this.b = 2;
   * }
   *
   * Foo.prototype.c = 3;
   *
   * _.keys(new Foo);
   * // => ['a', 'b'] (iteration order is not guaranteed)
   *
   * _.keys('hi');
   * // => ['0', '1']
   */
  function keys(object) {
    return isArrayLike_1(object) ? _arrayLikeKeys(object) : _baseKeys(object);
  }

  var keys_1 = keys;

  /**
   * The base implementation of `_.assign` without support for multiple sources
   * or `customizer` functions.
   *
   * @private
   * @param {Object} object The destination object.
   * @param {Object} source The source object.
   * @returns {Object} Returns `object`.
   */
  function baseAssign(object, source) {
    return object && _copyObject(source, keys_1(source), object);
  }

  var _baseAssign = baseAssign;

  /**
   * This function is like
   * [`Object.keys`](http://ecma-international.org/ecma-262/7.0/#sec-object.keys)
   * except that it includes inherited enumerable properties.
   *
   * @private
   * @param {Object} object The object to query.
   * @returns {Array} Returns the array of property names.
   */
  function nativeKeysIn(object) {
    var result = [];
    if (object != null) {
      for (var key in Object(object)) {
        result.push(key);
      }
    }
    return result;
  }

  var _nativeKeysIn = nativeKeysIn;

  /** Used for built-in method references. */
  var objectProto$a = Object.prototype;

  /** Used to check objects for own properties. */
  var hasOwnProperty$8 = objectProto$a.hasOwnProperty;

  /**
   * The base implementation of `_.keysIn` which doesn't treat sparse arrays as dense.
   *
   * @private
   * @param {Object} object The object to query.
   * @returns {Array} Returns the array of property names.
   */
  function baseKeysIn(object) {
    if (!isObject_1(object)) {
      return _nativeKeysIn(object);
    }
    var isProto = _isPrototype(object),
        result = [];

    for (var key in object) {
      if (!(key == 'constructor' && (isProto || !hasOwnProperty$8.call(object, key)))) {
        result.push(key);
      }
    }
    return result;
  }

  var _baseKeysIn = baseKeysIn;

  /**
   * Creates an array of the own and inherited enumerable property names of `object`.
   *
   * **Note:** Non-object values are coerced to objects.
   *
   * @static
   * @memberOf _
   * @since 3.0.0
   * @category Object
   * @param {Object} object The object to query.
   * @returns {Array} Returns the array of property names.
   * @example
   *
   * function Foo() {
   *   this.a = 1;
   *   this.b = 2;
   * }
   *
   * Foo.prototype.c = 3;
   *
   * _.keysIn(new Foo);
   * // => ['a', 'b', 'c'] (iteration order is not guaranteed)
   */
  function keysIn(object) {
    return isArrayLike_1(object) ? _arrayLikeKeys(object, true) : _baseKeysIn(object);
  }

  var keysIn_1 = keysIn;

  /**
   * The base implementation of `_.assignIn` without support for multiple sources
   * or `customizer` functions.
   *
   * @private
   * @param {Object} object The destination object.
   * @param {Object} source The source object.
   * @returns {Object} Returns `object`.
   */
  function baseAssignIn(object, source) {
    return object && _copyObject(source, keysIn_1(source), object);
  }

  var _baseAssignIn = baseAssignIn;

  var _cloneBuffer = createCommonjsModule(function (module, exports) {
  /** Detect free variable `exports`. */
  var freeExports =  exports && !exports.nodeType && exports;

  /** Detect free variable `module`. */
  var freeModule = freeExports && 'object' == 'object' && module && !module.nodeType && module;

  /** Detect the popular CommonJS extension `module.exports`. */
  var moduleExports = freeModule && freeModule.exports === freeExports;

  /** Built-in value references. */
  var Buffer = moduleExports ? _root.Buffer : undefined,
      allocUnsafe = Buffer ? Buffer.allocUnsafe : undefined;

  /**
   * Creates a clone of  `buffer`.
   *
   * @private
   * @param {Buffer} buffer The buffer to clone.
   * @param {boolean} [isDeep] Specify a deep clone.
   * @returns {Buffer} Returns the cloned buffer.
   */
  function cloneBuffer(buffer, isDeep) {
    if (isDeep) {
      return buffer.slice();
    }
    var length = buffer.length,
        result = allocUnsafe ? allocUnsafe(length) : new buffer.constructor(length);

    buffer.copy(result);
    return result;
  }

  module.exports = cloneBuffer;
  });

  /**
   * Copies the values of `source` to `array`.
   *
   * @private
   * @param {Array} source The array to copy values from.
   * @param {Array} [array=[]] The array to copy values to.
   * @returns {Array} Returns `array`.
   */
  function copyArray(source, array) {
    var index = -1,
        length = source.length;

    array || (array = Array(length));
    while (++index < length) {
      array[index] = source[index];
    }
    return array;
  }

  var _copyArray = copyArray;

  /**
   * A specialized version of `_.filter` for arrays without support for
   * iteratee shorthands.
   *
   * @private
   * @param {Array} [array] The array to iterate over.
   * @param {Function} predicate The function invoked per iteration.
   * @returns {Array} Returns the new filtered array.
   */
  function arrayFilter(array, predicate) {
    var index = -1,
        length = array == null ? 0 : array.length,
        resIndex = 0,
        result = [];

    while (++index < length) {
      var value = array[index];
      if (predicate(value, index, array)) {
        result[resIndex++] = value;
      }
    }
    return result;
  }

  var _arrayFilter = arrayFilter;

  /**
   * This method returns a new empty array.
   *
   * @static
   * @memberOf _
   * @since 4.13.0
   * @category Util
   * @returns {Array} Returns the new empty array.
   * @example
   *
   * var arrays = _.times(2, _.stubArray);
   *
   * console.log(arrays);
   * // => [[], []]
   *
   * console.log(arrays[0] === arrays[1]);
   * // => false
   */
  function stubArray() {
    return [];
  }

  var stubArray_1 = stubArray;

  /** Used for built-in method references. */
  var objectProto$b = Object.prototype;

  /** Built-in value references. */
  var propertyIsEnumerable$1 = objectProto$b.propertyIsEnumerable;

  /* Built-in method references for those with the same name as other `lodash` methods. */
  var nativeGetSymbols = Object.getOwnPropertySymbols;

  /**
   * Creates an array of the own enumerable symbols of `object`.
   *
   * @private
   * @param {Object} object The object to query.
   * @returns {Array} Returns the array of symbols.
   */
  var getSymbols = !nativeGetSymbols ? stubArray_1 : function(object) {
    if (object == null) {
      return [];
    }
    object = Object(object);
    return _arrayFilter(nativeGetSymbols(object), function(symbol) {
      return propertyIsEnumerable$1.call(object, symbol);
    });
  };

  var _getSymbols = getSymbols;

  /**
   * Copies own symbols of `source` to `object`.
   *
   * @private
   * @param {Object} source The object to copy symbols from.
   * @param {Object} [object={}] The object to copy symbols to.
   * @returns {Object} Returns `object`.
   */
  function copySymbols(source, object) {
    return _copyObject(source, _getSymbols(source), object);
  }

  var _copySymbols = copySymbols;

  /**
   * Appends the elements of `values` to `array`.
   *
   * @private
   * @param {Array} array The array to modify.
   * @param {Array} values The values to append.
   * @returns {Array} Returns `array`.
   */
  function arrayPush(array, values) {
    var index = -1,
        length = values.length,
        offset = array.length;

    while (++index < length) {
      array[offset + index] = values[index];
    }
    return array;
  }

  var _arrayPush = arrayPush;

  /** Built-in value references. */
  var getPrototype = _overArg(Object.getPrototypeOf, Object);

  var _getPrototype = getPrototype;

  /* Built-in method references for those with the same name as other `lodash` methods. */
  var nativeGetSymbols$1 = Object.getOwnPropertySymbols;

  /**
   * Creates an array of the own and inherited enumerable symbols of `object`.
   *
   * @private
   * @param {Object} object The object to query.
   * @returns {Array} Returns the array of symbols.
   */
  var getSymbolsIn = !nativeGetSymbols$1 ? stubArray_1 : function(object) {
    var result = [];
    while (object) {
      _arrayPush(result, _getSymbols(object));
      object = _getPrototype(object);
    }
    return result;
  };

  var _getSymbolsIn = getSymbolsIn;

  /**
   * Copies own and inherited symbols of `source` to `object`.
   *
   * @private
   * @param {Object} source The object to copy symbols from.
   * @param {Object} [object={}] The object to copy symbols to.
   * @returns {Object} Returns `object`.
   */
  function copySymbolsIn(source, object) {
    return _copyObject(source, _getSymbolsIn(source), object);
  }

  var _copySymbolsIn = copySymbolsIn;

  /**
   * The base implementation of `getAllKeys` and `getAllKeysIn` which uses
   * `keysFunc` and `symbolsFunc` to get the enumerable property names and
   * symbols of `object`.
   *
   * @private
   * @param {Object} object The object to query.
   * @param {Function} keysFunc The function to get the keys of `object`.
   * @param {Function} symbolsFunc The function to get the symbols of `object`.
   * @returns {Array} Returns the array of property names and symbols.
   */
  function baseGetAllKeys(object, keysFunc, symbolsFunc) {
    var result = keysFunc(object);
    return isArray_1(object) ? result : _arrayPush(result, symbolsFunc(object));
  }

  var _baseGetAllKeys = baseGetAllKeys;

  /**
   * Creates an array of own enumerable property names and symbols of `object`.
   *
   * @private
   * @param {Object} object The object to query.
   * @returns {Array} Returns the array of property names and symbols.
   */
  function getAllKeys(object) {
    return _baseGetAllKeys(object, keys_1, _getSymbols);
  }

  var _getAllKeys = getAllKeys;

  /**
   * Creates an array of own and inherited enumerable property names and
   * symbols of `object`.
   *
   * @private
   * @param {Object} object The object to query.
   * @returns {Array} Returns the array of property names and symbols.
   */
  function getAllKeysIn(object) {
    return _baseGetAllKeys(object, keysIn_1, _getSymbolsIn);
  }

  var _getAllKeysIn = getAllKeysIn;

  /* Built-in method references that are verified to be native. */
  var DataView$1 = _getNative(_root, 'DataView');

  var _DataView = DataView$1;

  /* Built-in method references that are verified to be native. */
  var Promise$1 = _getNative(_root, 'Promise');

  var _Promise = Promise$1;

  /* Built-in method references that are verified to be native. */
  var Set$1 = _getNative(_root, 'Set');

  var _Set = Set$1;

  /* Built-in method references that are verified to be native. */
  var WeakMap$1 = _getNative(_root, 'WeakMap');

  var _WeakMap = WeakMap$1;

  /** `Object#toString` result references. */
  var mapTag$1 = '[object Map]',
      objectTag$1 = '[object Object]',
      promiseTag = '[object Promise]',
      setTag$1 = '[object Set]',
      weakMapTag$1 = '[object WeakMap]';

  var dataViewTag$1 = '[object DataView]';

  /** Used to detect maps, sets, and weakmaps. */
  var dataViewCtorString = _toSource(_DataView),
      mapCtorString = _toSource(_Map),
      promiseCtorString = _toSource(_Promise),
      setCtorString = _toSource(_Set),
      weakMapCtorString = _toSource(_WeakMap);

  /**
   * Gets the `toStringTag` of `value`.
   *
   * @private
   * @param {*} value The value to query.
   * @returns {string} Returns the `toStringTag`.
   */
  var getTag = _baseGetTag;

  // Fallback for data views, maps, sets, and weak maps in IE 11 and promises in Node.js < 6.
  if ((_DataView && getTag(new _DataView(new ArrayBuffer(1))) != dataViewTag$1) ||
      (_Map && getTag(new _Map) != mapTag$1) ||
      (_Promise && getTag(_Promise.resolve()) != promiseTag) ||
      (_Set && getTag(new _Set) != setTag$1) ||
      (_WeakMap && getTag(new _WeakMap) != weakMapTag$1)) {
    getTag = function(value) {
      var result = _baseGetTag(value),
          Ctor = result == objectTag$1 ? value.constructor : undefined,
          ctorString = Ctor ? _toSource(Ctor) : '';

      if (ctorString) {
        switch (ctorString) {
          case dataViewCtorString: return dataViewTag$1;
          case mapCtorString: return mapTag$1;
          case promiseCtorString: return promiseTag;
          case setCtorString: return setTag$1;
          case weakMapCtorString: return weakMapTag$1;
        }
      }
      return result;
    };
  }

  var _getTag = getTag;

  /** Used for built-in method references. */
  var objectProto$c = Object.prototype;

  /** Used to check objects for own properties. */
  var hasOwnProperty$9 = objectProto$c.hasOwnProperty;

  /**
   * Initializes an array clone.
   *
   * @private
   * @param {Array} array The array to clone.
   * @returns {Array} Returns the initialized clone.
   */
  function initCloneArray(array) {
    var length = array.length,
        result = new array.constructor(length);

    // Add properties assigned by `RegExp#exec`.
    if (length && typeof array[0] == 'string' && hasOwnProperty$9.call(array, 'index')) {
      result.index = array.index;
      result.input = array.input;
    }
    return result;
  }

  var _initCloneArray = initCloneArray;

  /** Built-in value references. */
  var Uint8Array$1 = _root.Uint8Array;

  var _Uint8Array = Uint8Array$1;

  /**
   * Creates a clone of `arrayBuffer`.
   *
   * @private
   * @param {ArrayBuffer} arrayBuffer The array buffer to clone.
   * @returns {ArrayBuffer} Returns the cloned array buffer.
   */
  function cloneArrayBuffer(arrayBuffer) {
    var result = new arrayBuffer.constructor(arrayBuffer.byteLength);
    new _Uint8Array(result).set(new _Uint8Array(arrayBuffer));
    return result;
  }

  var _cloneArrayBuffer = cloneArrayBuffer;

  /**
   * Creates a clone of `dataView`.
   *
   * @private
   * @param {Object} dataView The data view to clone.
   * @param {boolean} [isDeep] Specify a deep clone.
   * @returns {Object} Returns the cloned data view.
   */
  function cloneDataView(dataView, isDeep) {
    var buffer = isDeep ? _cloneArrayBuffer(dataView.buffer) : dataView.buffer;
    return new dataView.constructor(buffer, dataView.byteOffset, dataView.byteLength);
  }

  var _cloneDataView = cloneDataView;

  /** Used to match `RegExp` flags from their coerced string values. */
  var reFlags = /\w*$/;

  /**
   * Creates a clone of `regexp`.
   *
   * @private
   * @param {Object} regexp The regexp to clone.
   * @returns {Object} Returns the cloned regexp.
   */
  function cloneRegExp(regexp) {
    var result = new regexp.constructor(regexp.source, reFlags.exec(regexp));
    result.lastIndex = regexp.lastIndex;
    return result;
  }

  var _cloneRegExp = cloneRegExp;

  /** Used to convert symbols to primitives and strings. */
  var symbolProto$1 = _Symbol ? _Symbol.prototype : undefined,
      symbolValueOf = symbolProto$1 ? symbolProto$1.valueOf : undefined;

  /**
   * Creates a clone of the `symbol` object.
   *
   * @private
   * @param {Object} symbol The symbol object to clone.
   * @returns {Object} Returns the cloned symbol object.
   */
  function cloneSymbol(symbol) {
    return symbolValueOf ? Object(symbolValueOf.call(symbol)) : {};
  }

  var _cloneSymbol = cloneSymbol;

  /**
   * Creates a clone of `typedArray`.
   *
   * @private
   * @param {Object} typedArray The typed array to clone.
   * @param {boolean} [isDeep] Specify a deep clone.
   * @returns {Object} Returns the cloned typed array.
   */
  function cloneTypedArray(typedArray, isDeep) {
    var buffer = isDeep ? _cloneArrayBuffer(typedArray.buffer) : typedArray.buffer;
    return new typedArray.constructor(buffer, typedArray.byteOffset, typedArray.length);
  }

  var _cloneTypedArray = cloneTypedArray;

  /** `Object#toString` result references. */
  var boolTag$1 = '[object Boolean]',
      dateTag$1 = '[object Date]',
      mapTag$2 = '[object Map]',
      numberTag$1 = '[object Number]',
      regexpTag$1 = '[object RegExp]',
      setTag$2 = '[object Set]',
      stringTag$1 = '[object String]',
      symbolTag$1 = '[object Symbol]';

  var arrayBufferTag$1 = '[object ArrayBuffer]',
      dataViewTag$2 = '[object DataView]',
      float32Tag$1 = '[object Float32Array]',
      float64Tag$1 = '[object Float64Array]',
      int8Tag$1 = '[object Int8Array]',
      int16Tag$1 = '[object Int16Array]',
      int32Tag$1 = '[object Int32Array]',
      uint8Tag$1 = '[object Uint8Array]',
      uint8ClampedTag$1 = '[object Uint8ClampedArray]',
      uint16Tag$1 = '[object Uint16Array]',
      uint32Tag$1 = '[object Uint32Array]';

  /**
   * Initializes an object clone based on its `toStringTag`.
   *
   * **Note:** This function only supports cloning values with tags of
   * `Boolean`, `Date`, `Error`, `Map`, `Number`, `RegExp`, `Set`, or `String`.
   *
   * @private
   * @param {Object} object The object to clone.
   * @param {string} tag The `toStringTag` of the object to clone.
   * @param {boolean} [isDeep] Specify a deep clone.
   * @returns {Object} Returns the initialized clone.
   */
  function initCloneByTag(object, tag, isDeep) {
    var Ctor = object.constructor;
    switch (tag) {
      case arrayBufferTag$1:
        return _cloneArrayBuffer(object);

      case boolTag$1:
      case dateTag$1:
        return new Ctor(+object);

      case dataViewTag$2:
        return _cloneDataView(object, isDeep);

      case float32Tag$1: case float64Tag$1:
      case int8Tag$1: case int16Tag$1: case int32Tag$1:
      case uint8Tag$1: case uint8ClampedTag$1: case uint16Tag$1: case uint32Tag$1:
        return _cloneTypedArray(object, isDeep);

      case mapTag$2:
        return new Ctor;

      case numberTag$1:
      case stringTag$1:
        return new Ctor(object);

      case regexpTag$1:
        return _cloneRegExp(object);

      case setTag$2:
        return new Ctor;

      case symbolTag$1:
        return _cloneSymbol(object);
    }
  }

  var _initCloneByTag = initCloneByTag;

  /** Built-in value references. */
  var objectCreate = Object.create;

  /**
   * The base implementation of `_.create` without support for assigning
   * properties to the created object.
   *
   * @private
   * @param {Object} proto The object to inherit from.
   * @returns {Object} Returns the new object.
   */
  var baseCreate = (function() {
    function object() {}
    return function(proto) {
      if (!isObject_1(proto)) {
        return {};
      }
      if (objectCreate) {
        return objectCreate(proto);
      }
      object.prototype = proto;
      var result = new object;
      object.prototype = undefined;
      return result;
    };
  }());

  var _baseCreate = baseCreate;

  /**
   * Initializes an object clone.
   *
   * @private
   * @param {Object} object The object to clone.
   * @returns {Object} Returns the initialized clone.
   */
  function initCloneObject(object) {
    return (typeof object.constructor == 'function' && !_isPrototype(object))
      ? _baseCreate(_getPrototype(object))
      : {};
  }

  var _initCloneObject = initCloneObject;

  /** `Object#toString` result references. */
  var mapTag$3 = '[object Map]';

  /**
   * The base implementation of `_.isMap` without Node.js optimizations.
   *
   * @private
   * @param {*} value The value to check.
   * @returns {boolean} Returns `true` if `value` is a map, else `false`.
   */
  function baseIsMap(value) {
    return isObjectLike_1(value) && _getTag(value) == mapTag$3;
  }

  var _baseIsMap = baseIsMap;

  /* Node.js helper references. */
  var nodeIsMap = _nodeUtil && _nodeUtil.isMap;

  /**
   * Checks if `value` is classified as a `Map` object.
   *
   * @static
   * @memberOf _
   * @since 4.3.0
   * @category Lang
   * @param {*} value The value to check.
   * @returns {boolean} Returns `true` if `value` is a map, else `false`.
   * @example
   *
   * _.isMap(new Map);
   * // => true
   *
   * _.isMap(new WeakMap);
   * // => false
   */
  var isMap$1 = nodeIsMap ? _baseUnary(nodeIsMap) : _baseIsMap;

  var isMap_1 = isMap$1;

  /** `Object#toString` result references. */
  var setTag$3 = '[object Set]';

  /**
   * The base implementation of `_.isSet` without Node.js optimizations.
   *
   * @private
   * @param {*} value The value to check.
   * @returns {boolean} Returns `true` if `value` is a set, else `false`.
   */
  function baseIsSet(value) {
    return isObjectLike_1(value) && _getTag(value) == setTag$3;
  }

  var _baseIsSet = baseIsSet;

  /* Node.js helper references. */
  var nodeIsSet = _nodeUtil && _nodeUtil.isSet;

  /**
   * Checks if `value` is classified as a `Set` object.
   *
   * @static
   * @memberOf _
   * @since 4.3.0
   * @category Lang
   * @param {*} value The value to check.
   * @returns {boolean} Returns `true` if `value` is a set, else `false`.
   * @example
   *
   * _.isSet(new Set);
   * // => true
   *
   * _.isSet(new WeakSet);
   * // => false
   */
  var isSet$1 = nodeIsSet ? _baseUnary(nodeIsSet) : _baseIsSet;

  var isSet_1 = isSet$1;

  /** Used to compose bitmasks for cloning. */
  var CLONE_DEEP_FLAG = 1,
      CLONE_FLAT_FLAG = 2,
      CLONE_SYMBOLS_FLAG = 4;

  /** `Object#toString` result references. */
  var argsTag$2 = '[object Arguments]',
      arrayTag$1 = '[object Array]',
      boolTag$2 = '[object Boolean]',
      dateTag$2 = '[object Date]',
      errorTag$1 = '[object Error]',
      funcTag$2 = '[object Function]',
      genTag$1 = '[object GeneratorFunction]',
      mapTag$4 = '[object Map]',
      numberTag$2 = '[object Number]',
      objectTag$2 = '[object Object]',
      regexpTag$2 = '[object RegExp]',
      setTag$4 = '[object Set]',
      stringTag$2 = '[object String]',
      symbolTag$2 = '[object Symbol]',
      weakMapTag$2 = '[object WeakMap]';

  var arrayBufferTag$2 = '[object ArrayBuffer]',
      dataViewTag$3 = '[object DataView]',
      float32Tag$2 = '[object Float32Array]',
      float64Tag$2 = '[object Float64Array]',
      int8Tag$2 = '[object Int8Array]',
      int16Tag$2 = '[object Int16Array]',
      int32Tag$2 = '[object Int32Array]',
      uint8Tag$2 = '[object Uint8Array]',
      uint8ClampedTag$2 = '[object Uint8ClampedArray]',
      uint16Tag$2 = '[object Uint16Array]',
      uint32Tag$2 = '[object Uint32Array]';

  /** Used to identify `toStringTag` values supported by `_.clone`. */
  var cloneableTags = {};
  cloneableTags[argsTag$2] = cloneableTags[arrayTag$1] =
  cloneableTags[arrayBufferTag$2] = cloneableTags[dataViewTag$3] =
  cloneableTags[boolTag$2] = cloneableTags[dateTag$2] =
  cloneableTags[float32Tag$2] = cloneableTags[float64Tag$2] =
  cloneableTags[int8Tag$2] = cloneableTags[int16Tag$2] =
  cloneableTags[int32Tag$2] = cloneableTags[mapTag$4] =
  cloneableTags[numberTag$2] = cloneableTags[objectTag$2] =
  cloneableTags[regexpTag$2] = cloneableTags[setTag$4] =
  cloneableTags[stringTag$2] = cloneableTags[symbolTag$2] =
  cloneableTags[uint8Tag$2] = cloneableTags[uint8ClampedTag$2] =
  cloneableTags[uint16Tag$2] = cloneableTags[uint32Tag$2] = true;
  cloneableTags[errorTag$1] = cloneableTags[funcTag$2] =
  cloneableTags[weakMapTag$2] = false;

  /**
   * The base implementation of `_.clone` and `_.cloneDeep` which tracks
   * traversed objects.
   *
   * @private
   * @param {*} value The value to clone.
   * @param {boolean} bitmask The bitmask flags.
   *  1 - Deep clone
   *  2 - Flatten inherited properties
   *  4 - Clone symbols
   * @param {Function} [customizer] The function to customize cloning.
   * @param {string} [key] The key of `value`.
   * @param {Object} [object] The parent object of `value`.
   * @param {Object} [stack] Tracks traversed objects and their clone counterparts.
   * @returns {*} Returns the cloned value.
   */
  function baseClone(value, bitmask, customizer, key, object, stack) {
    var result,
        isDeep = bitmask & CLONE_DEEP_FLAG,
        isFlat = bitmask & CLONE_FLAT_FLAG,
        isFull = bitmask & CLONE_SYMBOLS_FLAG;

    if (customizer) {
      result = object ? customizer(value, key, object, stack) : customizer(value);
    }
    if (result !== undefined) {
      return result;
    }
    if (!isObject_1(value)) {
      return value;
    }
    var isArr = isArray_1(value);
    if (isArr) {
      result = _initCloneArray(value);
      if (!isDeep) {
        return _copyArray(value, result);
      }
    } else {
      var tag = _getTag(value),
          isFunc = tag == funcTag$2 || tag == genTag$1;

      if (isBuffer_1(value)) {
        return _cloneBuffer(value, isDeep);
      }
      if (tag == objectTag$2 || tag == argsTag$2 || (isFunc && !object)) {
        result = (isFlat || isFunc) ? {} : _initCloneObject(value);
        if (!isDeep) {
          return isFlat
            ? _copySymbolsIn(value, _baseAssignIn(result, value))
            : _copySymbols(value, _baseAssign(result, value));
        }
      } else {
        if (!cloneableTags[tag]) {
          return object ? value : {};
        }
        result = _initCloneByTag(value, tag, isDeep);
      }
    }
    // Check for circular references and return its corresponding clone.
    stack || (stack = new _Stack);
    var stacked = stack.get(value);
    if (stacked) {
      return stacked;
    }
    stack.set(value, result);

    if (isSet_1(value)) {
      value.forEach(function(subValue) {
        result.add(baseClone(subValue, bitmask, customizer, subValue, value, stack));
      });
    } else if (isMap_1(value)) {
      value.forEach(function(subValue, key) {
        result.set(key, baseClone(subValue, bitmask, customizer, key, value, stack));
      });
    }

    var keysFunc = isFull
      ? (isFlat ? _getAllKeysIn : _getAllKeys)
      : (isFlat ? keysIn_1 : keys_1);

    var props = isArr ? undefined : keysFunc(value);
    _arrayEach(props || value, function(subValue, key) {
      if (props) {
        key = subValue;
        subValue = value[key];
      }
      // Recursively populate clone (susceptible to call stack limits).
      _assignValue(result, key, baseClone(subValue, bitmask, customizer, key, value, stack));
    });
    return result;
  }

  var _baseClone = baseClone;

  /** Used to compose bitmasks for cloning. */
  var CLONE_DEEP_FLAG$1 = 1,
      CLONE_SYMBOLS_FLAG$1 = 4;

  /**
   * This method is like `_.clone` except that it recursively clones `value`.
   *
   * @static
   * @memberOf _
   * @since 1.0.0
   * @category Lang
   * @param {*} value The value to recursively clone.
   * @returns {*} Returns the deep cloned value.
   * @see _.clone
   * @example
   *
   * var objects = [{ 'a': 1 }, { 'b': 2 }];
   *
   * var deep = _.cloneDeep(objects);
   * console.log(deep[0] === objects[0]);
   * // => false
   */
  function cloneDeep(value) {
    return _baseClone(value, CLONE_DEEP_FLAG$1 | CLONE_SYMBOLS_FLAG$1);
  }

  var cloneDeep_1 = cloneDeep;

  (function (ResumableCopyEventType) {
    ResumableCopyEventType[ResumableCopyEventType["CreateMultipartUploadSucceed"] = 1] = "CreateMultipartUploadSucceed";
    ResumableCopyEventType[ResumableCopyEventType["CreateMultipartUploadFailed"] = 2] = "CreateMultipartUploadFailed";
    ResumableCopyEventType[ResumableCopyEventType["UploadPartCopySucceed"] = 3] = "UploadPartCopySucceed";
    ResumableCopyEventType[ResumableCopyEventType["UploadPartCopyFailed"] = 4] = "UploadPartCopyFailed";
    ResumableCopyEventType[ResumableCopyEventType["UploadPartCopyAborted"] = 5] = "UploadPartCopyAborted";
    ResumableCopyEventType[ResumableCopyEventType["CompleteMultipartUploadSucceed"] = 6] = "CompleteMultipartUploadSucceed";
    ResumableCopyEventType[ResumableCopyEventType["CompleteMultipartUploadFailed"] = 7] = "CompleteMultipartUploadFailed";
  })(exports.ResumableCopyEventType || (exports.ResumableCopyEventType = {}));

  const CHECKPOINT_FILE_NAME_PLACEHOLDER$1 = '@@checkpoint-file-placeholder@@';
  const ABORT_ERROR_STATUS_CODE$1 = [403, 404, 405];
  const DEFAULT_PART_SIZE$1 = 20 * 1024 * 1024; // 20 MB

  async function resumableCopyObject(input) {
    var _checkpointRichInfo$r3, _checkpointRichInfo$r4, _checkpointRichInfo$r5;

    const {
      cancelToken
    } = input;

    const isCancel = () => cancelToken && !!cancelToken.reason;
    const {
      data: objectStats
    } = await headObject.call(this, {
      bucket: input.srcBucket,
      key: input.srcKey,
      versionId: input.srcVersionId
    });
    const etag = objectStats['etag'];
    const objectSize = +objectStats['content-length'];
    const checkpointRichInfo = await (async () => {

      if (typeof input.checkpoint === 'object') {
        return {
          record: input.checkpoint
        };
      }

      return {};
    })(); // check if file info is matched

    await (async () => {
      var _checkpointRichInfo$r;

      if ((_checkpointRichInfo$r = checkpointRichInfo.record) != null && _checkpointRichInfo$r.copy_source_object_info) {
        var _checkpointRichInfo$r2;

        const {
          last_modified,
          object_size
        } = (_checkpointRichInfo$r2 = checkpointRichInfo.record) == null ? void 0 : _checkpointRichInfo$r2.copy_source_object_info;

        if ( // TODO: `last-modified` aligns to number
        objectStats['last-modified'] !== last_modified || +objectStats['content-length'] !== object_size) {
          console.warn(`The file has been modified since ${new Date(last_modified)}, so the checkpoint file is invalid, and specified file will be uploaded again.`);
          delete checkpointRichInfo.record;
        }
      }
    })();
    const partSize = calculateSafePartSize(objectSize, input.partSize || ((_checkpointRichInfo$r3 = checkpointRichInfo.record) == null ? void 0 : _checkpointRichInfo$r3.part_size) || DEFAULT_PART_SIZE$1, true); // check partSize is matched

    if (checkpointRichInfo.record && checkpointRichInfo.record.part_size !== partSize) {
      console.warn('The partSize param does not equal the partSize in checkpoint file, ' + 'so the checkpoint file is invalid, and specified file will be uploaded again.');
      delete checkpointRichInfo.record;
    }

    let bucket = input.bucket || this.opts.bucket || '';
    const key = input.key;
    let uploadId = '';
    let tasks = [];
    const allTasks = getAllTasks$1(objectSize, partSize);
    const initConsumedBytes = (((_checkpointRichInfo$r4 = checkpointRichInfo.record) == null ? void 0 : _checkpointRichInfo$r4.parts_info) || []).filter(it => it.is_completed).reduce((prev, it) => prev + it.copy_source_range_end - it.copy_source_range_start + 1, 0);
    let consumedBytesForProgress = initConsumedBytes; // recorded tasks

    const recordedTasks = ((_checkpointRichInfo$r5 = checkpointRichInfo.record) == null ? void 0 : _checkpointRichInfo$r5.parts_info) || [];
    const recordedTaskMap = new Map();
    recordedTasks.forEach(it => recordedTaskMap.set(it.part_number, it));

    const getCheckpointContent = () => {
      const checkpointContent = {
        bucket,
        key,
        part_size: partSize,
        upload_id: uploadId,
        parts_info: recordedTasks,
        copy_source_object_info: {
          last_modified: objectStats['last-modified'],
          etag: objectStats.etag,
          hash_crc64ecma: objectStats['x-tos-hash-crc64ecma'] || '',
          object_size: +objectStats['content-length']
        }
      };
      return checkpointContent;
    };

    const triggerUploadEvent = e => {
      if (!input.copyEventListener) {
        return;
      }

      const event = {
        bucket,
        uploadId,
        key,
        ...e
      };

      if (checkpointRichInfo.filePath) {
        event.checkpointFile = checkpointRichInfo.filePath;
      }

      input.copyEventListener(event);
    };

    let TriggerProgressEventType;

    (function (TriggerProgressEventType) {
      TriggerProgressEventType[TriggerProgressEventType["start"] = 1] = "start";
      TriggerProgressEventType[TriggerProgressEventType["uploadPartSucceed"] = 2] = "uploadPartSucceed";
      TriggerProgressEventType[TriggerProgressEventType["completeMultipartUploadSucceed"] = 3] = "completeMultipartUploadSucceed";
    })(TriggerProgressEventType || (TriggerProgressEventType = {}));

    const triggerProgressEvent = type => {
      if (!input.progress) {
        return;
      }

      const percent = (() => {
        if (type === TriggerProgressEventType.start && objectSize === 0) {
          return 0;
        }

        return !objectSize ? 1 : consumedBytesForProgress / objectSize;
      })();

      if (consumedBytesForProgress === objectSize && type === TriggerProgressEventType.uploadPartSucceed) ; else {
        input.progress(percent, getCheckpointContent());
      }
    };

    const writeCheckpointFile = makeSerialAsyncTask(async () => {
    });

    const rmCheckpointFile = async () => {
    };
    /**
     *
     * @param task one part task
     * @param uploadPartRes upload part failed if `uploadPartRes` is Error
     */


    const updateAfterUploadPart = async (task, uploadPartRes) => {
      let existRecordTask = recordedTaskMap.get(task.partNumber);
      const rangeStart = task.offset;
      const rangeEnd = Math.min(task.offset + partSize - 1, objectSize - 1);

      if (!existRecordTask) {
        existRecordTask = {
          part_number: task.partNumber,
          copy_source_range_start: rangeStart,
          copy_source_range_end: rangeEnd,
          is_completed: false,
          etag: ''
        };
        recordedTasks.push(existRecordTask);
        recordedTaskMap.set(existRecordTask.part_number, existRecordTask);
      }

      if (!uploadPartRes.err) {
        existRecordTask.is_completed = true;
        existRecordTask.etag = uploadPartRes.res.ETag;
      }

      await writeCheckpointFile();
      const copyPartInfo = {
        partNumber: existRecordTask.part_number,
        copySourceRangeEnd: existRecordTask.copy_source_range_end,
        copySourceRangeStart: existRecordTask.copy_source_range_start
      };

      if (uploadPartRes.err) {
        const err = uploadPartRes.err;
        let type = exports.ResumableCopyEventType.UploadPartCopyFailed;

        if (err instanceof TosServerError) {
          if (ABORT_ERROR_STATUS_CODE$1.includes(err.statusCode)) {
            type = exports.ResumableCopyEventType.UploadPartCopyAborted;
          }
        }

        triggerUploadEvent({
          type,
          err,
          copyPartInfo
        });
        return;
      }

      copyPartInfo.etag = uploadPartRes.res.ETag;
      consumedBytesForProgress += copyPartInfo.copySourceRangeEnd - copyPartInfo.copySourceRangeStart + 1;
      triggerUploadEvent({
        type: exports.ResumableCopyEventType.UploadPartCopySucceed,
        copyPartInfo
      });
      triggerProgressEvent(TriggerProgressEventType.uploadPartSucceed);
    };

    if (checkpointRichInfo.record) {
      bucket = checkpointRichInfo.record.bucket;
      uploadId = checkpointRichInfo.record.upload_id; // checkpoint info exists, so need to calculate remain tasks

      const uploadedPartSet = new Set((checkpointRichInfo.record.parts_info || []).filter(it => it.is_completed).map(it => it.part_number));
      tasks = allTasks.filter(it => !uploadedPartSet.has(it.partNumber));
    } else {
      // createMultipartUpload will check bucket
      try {
        const {
          data: multipartRes
        } = await createMultipartUpload.call(this, cloneDeep_1(input));

        if (isCancel()) {
          throw new CancelError('cancel uploadFile');
        }

        bucket = multipartRes.Bucket;
        uploadId = multipartRes.UploadId;

        if (checkpointRichInfo.filePathIsPlaceholder) {
          var _checkpointRichInfo$f;

          checkpointRichInfo.filePath = (_checkpointRichInfo$f = checkpointRichInfo.filePath) == null ? void 0 : _checkpointRichInfo$f.replace(`${CHECKPOINT_FILE_NAME_PLACEHOLDER$1}`, getDefaultCheckpointFilePath$1({ ...input,
            bucket
          }));
        }

        triggerUploadEvent({
          type: exports.ResumableCopyEventType.CreateMultipartUploadSucceed
        });
      } catch (_err) {
        const err = _err;
        triggerUploadEvent({
          type: exports.ResumableCopyEventType.CreateMultipartUploadFailed,
          err
        });
        throw err;
      }

      tasks = allTasks;
    }

    const handleTasks = async () => {
      let firstErr = null;
      let index = 0; // TODO: how to test parallel does work, measure time is not right

      await Promise.all(Array.from({
        length: input.taskNum || 1
      }).map(async () => {
        while (true) {
          const currentIndex = index++;

          if (currentIndex >= tasks.length) {
            return;
          }

          const curTask = tasks[currentIndex];

          try {
            let copySource = getCopySourceHeaderValue(input.srcBucket, input.srcKey);

            if (input.srcVersionId) {
              copySource += `?versionId=${input.srcVersionId}`;
            }

            const copyRange = `bytes=${curTask.offset}-${curTask.offset + curTask.partSize - 1}`;
            const headers = { ...input.headers,
              ['x-tos-copy-source']: copySource,
              ['x-tos-copy-source-if-match']: etag,
              ['x-tos-copy-source-range']: copyRange
            };

            if (!curTask.partSize) {
              delete headers['x-tos-copy-source-range'];
            }

            const {
              data: uploadPartRes
            } = await uploadPartCopy.call(this, {
              bucket,
              key,
              uploadId,
              partNumber: curTask.partNumber,
              headers,
              trafficLimit: input.trafficLimit
            });

            if (isCancel()) {
              throw new CancelError('cancel resumableCopyObject');
            }

            await updateAfterUploadPart(curTask, {
              res: uploadPartRes
            });
          } catch (_err) {
            const err = _err;

            if (isCancelError$1(err)) {
              throw err;
            }

            if (isCancel()) {
              throw new CancelError('cancel resumableCopyObject');
            }

            if (!firstErr) {
              firstErr = err;
            }

            await updateAfterUploadPart(curTask, {
              err
            });
          }
        }
      }));

      if (firstErr) {
        throw firstErr;
      }

      const parts = (getCheckpointContent().parts_info || []).map(it => ({
        eTag: it.etag,
        partNumber: it.part_number
      }));
      const [err, res] = await safeAwait(completeMultipartUpload.call(this, {
        bucket,
        key,
        uploadId,
        parts
      }));

      if (err || !res) {
        triggerUploadEvent({
          type: exports.ResumableCopyEventType.CompleteMultipartUploadFailed
        });
        throw err;
      }

      triggerUploadEvent({
        type: exports.ResumableCopyEventType.CompleteMultipartUploadSucceed
      });
      triggerProgressEvent(TriggerProgressEventType.completeMultipartUploadSucceed);
      const sourceCRC64 = getCheckpointContent().copy_source_object_info.hash_crc64ecma;
      const actualCrc64 = res.data.HashCrc64ecma;

      if (this.opts.enableCRC && sourceCRC64 && actualCrc64 && sourceCRC64 !== actualCrc64) {
        throw new TosClientError(`validate file crc64 failed. Expect crc64 ${sourceCRC64}, actual crc64 ${actualCrc64}. Please try again.`);
      }

      await rmCheckpointFile();
      return res;
    };

    const handleEmptyObj = async () => {
      let copySource = getCopySourceHeaderValue(input.srcBucket, input.srcKey);

      if (input.srcVersionId) {
        copySource += `?versionId=${input.srcVersionId}`;
      }

      const headers = { ...input.headers,
        ['x-tos-copy-source']: copySource,
        ['x-tos-copy-source-if-match']: etag
      };
      const [err, res] = await safeAwait(copyObject.call(this, {
        bucket: input.bucket,
        key: input.key,
        headers,
        trafficLimit: input.trafficLimit
      }));

      if (err || !res) {
        triggerUploadEvent({
          type: exports.ResumableCopyEventType.UploadPartCopyFailed
        });
        throw err;
      }

      triggerProgressEvent(TriggerProgressEventType.completeMultipartUploadSucceed);
      triggerUploadEvent({
        type: exports.ResumableCopyEventType.UploadPartCopySucceed,
        copyPartInfo: {
          partNumber: 0,
          copySourceRangeStart: 0,
          copySourceRangeEnd: 0
        }
      });
      triggerUploadEvent({
        type: exports.ResumableCopyEventType.CompleteMultipartUploadSucceed
      });
      return { ...res,
        data: {
          ETag: res.headers['etag'] || '',
          Bucket: bucket,
          Key: key,
          Location: `http${this.opts.secure ? 's' : ''}://${bucket}.${this.opts.endpoint}/${key}`,
          VersionID: res.headers['x-tos-version-id'],
          HashCrc64ecma: res.headers['x-tos-hash-crc64ecma']
        }
      };
    };

    triggerProgressEvent(TriggerProgressEventType.start);
    return objectSize === 0 ? handleEmptyObj() : handleTasks();
  }
  function isCancelError$1(err) {
    return err instanceof CancelError;
  }
  /**
   * 即使 totalSize 是 0，也需要一个 Part，否则 Server 端会报错 read request body failed
   */

  function getAllTasks$1(totalSize, partSize) {
    const tasks = [];

    for (let i = 0;; ++i) {
      const offset = i * partSize;
      const currPartSize = Math.min(partSize, totalSize - offset);
      tasks.push({
        offset,
        partSize: currPartSize,
        partNumber: i + 1
      });

      if ((i + 1) * partSize >= totalSize) {
        break;
      }
    }

    return tasks;
  }

  function getDefaultCheckpointFilePath$1(opts) {
    const originPath = [opts.srcBucket, opts.srcKey, opts.srcVersionId, opts.bucket, opts.key, 'copy'].filter(Boolean).join('.');
    const normalizePath = originPath.replace(/[\\/]/g, '');
    return normalizePath;
  }

  /**
   * @deprecated use getObjectV2 instead
   * @returns arraybuffer
   */

  async function getObject(input) {
    const normalizedInput = typeof input === 'string' ? {
      key: input
    } : input;
    const query = {};

    if (normalizedInput.versionId) {
      query.versionId = normalizedInput.versionId;
    }

    const headers = normalizeHeadersKey(normalizedInput == null ? void 0 : normalizedInput.headers);
    const response = (normalizedInput == null ? void 0 : normalizedInput.response) || {};
    Object.keys(response).forEach(key => {
      const v = response[key];

      if (v != null) {
        query[`response-${key}`] = v;
      }
    }); // TODO: maybe need to return response's headers

    return this._fetchObject(input, 'GET', query, headers, undefined, {
      axiosOpts: {
        responseType: 'arraybuffer'
      }
    });
  }
  const BROWSER_DATATYPE = ['blob'];

  function checkSupportDataType(dataType) {
    let environment = 'node';
    let supportDataTypes = [];

    {
      environment = 'browser';
      supportDataTypes = BROWSER_DATATYPE;
    }

    if (!supportDataTypes.includes(dataType)) {
      throw new TosClientError(`The value of \`dataType\` only supports \`${supportDataTypes.join(' | ')}\` in ${environment} environment`);
    }
  }

  async function getObjectV2(input) {
    const normalizedInput = typeof input === 'string' ? {
      key: input
    } : input;
    const headers = normalizeHeadersKey(normalizedInput.headers);
    normalizedInput.headers = headers;
    const dataType = normalizedInput.dataType || 'stream';
    normalizedInput.dataType = dataType;
    checkSupportDataType(dataType);
    const query = {};
    const response = (normalizedInput == null ? void 0 : normalizedInput.response) || {};
    Object.keys(response).forEach(key => {
      const v = response[key];

      if (v != null) {
        query[`response-${key}`] = v;
      }
    });
    fillRequestQuery(normalizedInput, query, ['versionId', 'process', 'saveBucket', 'saveObject', 'responseCacheControl', 'responseContentDisposition', 'responseContentEncoding', 'responseContentLanguage', 'responseContentType', 'responseExpires']);
    fillRequestHeaders(normalizedInput, ['ifMatch', 'ifModifiedSince', 'ifNoneMatch', 'ifUnmodifiedSince', 'ssecAlgorithm', 'ssecKey', 'ssecKeyMD5', 'range', 'trafficLimit']);

    if (normalizedInput.range == null && (normalizedInput.rangeStart != null || normalizedInput.rangeEnd != null)) {
      var _headers$range;

      const start = normalizedInput.rangeStart != null ? `${normalizedInput.rangeStart}` : '';
      const end = normalizedInput.rangeEnd != null ? `${normalizedInput.rangeEnd}` : '';
      const range = `bytes=${start}-${end}`;
      headers['range'] = (_headers$range = headers['range']) != null ? _headers$range : range;
    }

    const responseType = (() => {

      return 'arraybuffer';
    })();

    let consumedBytes = 0; // totalSize is unknown when start download

    let totalSize = -1;
    const {
      dataTransferStatusChange,
      progress
    } = normalizedInput;

    const triggerDataTransfer = (type, rwOnceBytes = 0) => {
      // request cancel will make rwOnceBytes < 0 in browser
      if (rwOnceBytes < 0) {
        return;
      }

      if (!dataTransferStatusChange && !progress) {
        return;
      }

      consumedBytes += rwOnceBytes;
      dataTransferStatusChange == null ? void 0 : dataTransferStatusChange({
        type,
        rwOnceBytes,
        consumedBytes,
        totalBytes: totalSize
      });

      const progressValue = (() => {
        // `totalSize` is unknown if it's in start or fail
        if (totalSize < 0) {
          return 0;
        }

        if (totalSize === 0) {
          if (type === exports.DataTransferType.Succeed) {
            return 1;
          }

          return 0;
        }

        return consumedBytes / totalSize;
      })();

      if (progressValue === 1) {
        if (type === exports.DataTransferType.Succeed) {
          progress == null ? void 0 : progress(progressValue);
        }
      } else {
        progress == null ? void 0 : progress(progressValue);
      }
    };

    triggerDataTransfer(exports.DataTransferType.Started);
    const [err, res] = await safeAwait(this._fetchObject(input, 'GET', query, headers, undefined, {
      axiosOpts: {
        responseType,
        onDownloadProgress: event => {
          totalSize = event.total;
          triggerDataTransfer(exports.DataTransferType.Rw, event.loaded - consumedBytes);
        }
      }
    }));

    if (err || !res) {
      triggerDataTransfer(exports.DataTransferType.Failed);
      throw err;
    }

    let resHeaders = res.headers;
    let newData = res.data;
    totalSize = +(resHeaders['content-length'] || 0);

    {
      // 浏览器环境
      if (dataType === 'blob') {
        newData = new Blob([res.data], {
          type: resHeaders['content-type']
        });
      }

      triggerDataTransfer(exports.DataTransferType.Succeed);
    }

    const actualRes = { ...res,
      data: {
        content: newData,
        etag: resHeaders['etag'] || '',
        lastModified: resHeaders['last-modified'] || '',
        hashCrc64ecma: resHeaders['x-tos-hash-crc64ecma'] || '',
        ReplicationStatus: resHeaders[TosHeader.HeaderReplicationStatus]
      }
    };
    const info = getRestoreInfoFromHeaders(resHeaders);

    if (info) {
      actualRes.data.RestoreInfo = info;
    }

    return actualRes;
  }

  async function getObjectToFile(input) {
    {
      throw new TosClientError("getObjectToFile doesn't support in browser environment");
    }
  }

  (function (DownloadEventType) {
    DownloadEventType[DownloadEventType["CreateTempFileSucceed"] = 1] = "CreateTempFileSucceed";
    DownloadEventType[DownloadEventType["CreateTempFileFailed"] = 2] = "CreateTempFileFailed";
    DownloadEventType[DownloadEventType["DownloadPartSucceed"] = 3] = "DownloadPartSucceed";
    DownloadEventType[DownloadEventType["DownloadPartFailed"] = 4] = "DownloadPartFailed";
    DownloadEventType[DownloadEventType["DownloadPartAborted"] = 5] = "DownloadPartAborted";
    DownloadEventType[DownloadEventType["RenameTempFileSucceed"] = 6] = "RenameTempFileSucceed";
    DownloadEventType[DownloadEventType["RenameTempFileFailed"] = 7] = "RenameTempFileFailed";
  })(exports.DownloadEventType || (exports.DownloadEventType = {}));
  async function downloadFile(input) {

    {
      throw new TosClientError('`downloadFile` is not supported in browser environment');
    }
  }

  // @ts-nocheck
  const SIG_QUERY = {
    algorithm: 'tos-algorithm',
    expiration: 'tos-expiration',
    signame: 'tos-signame',
    signature: 'tos-signature',
    v4_algorithm: 'X-Tos-Algorithm',
    v4_credential: 'X-Tos-Credential',
    v4_date: 'X-Tos-Date',
    v4_expires: 'X-Tos-Expires',
    v4_signedHeaders: 'X-Tos-SignedHeaders',
    v4_security_token: 'X-Tos-Security-Token',
    v4_signature: 'X-Tos-Signature',
    v4_content_sha: 'X-Tos-Content-Sha256',
    v4_policy: 'X-Tos-Policy'
  };
  function isDefaultPort(port) {
    if (port && port !== 80 && port !== 443) {
      return false;
    }

    return true;
  }
  /**
   * @api private
   */

  const v4Identifier = 'request';
  /**
   * @api private
   */

  class SignersV4 {
    constructor(_opt, _credentials) {
      this.options = void 0;
      this.credentials = void 0;

      this.signature = (opt, expiredAt, credentials) => {
        if (!credentials) {
          credentials = this.credentials;
        }

        const parts = [];
        const datatime = opt.datetime;
        const credString = this.credentialString(datatime);
        parts.push(this.options.algorithm + ' Credential=' + credentials.GetAccessKey() + '/' + credString); // console.log(this.algorithm + ' Credential=' +
        //   credentials.accessKeyId + '/' + credString)

        parts.push('SignedHeaders=' + this.signedHeaders(opt));
        parts.push('Signature=' + this.authorization(opt, credentials, 0));
        return parts.join(', ');
      };

      this.signatureHeader = (opt, expiredAt, credentials) => {
        // const datetime = (new Date(new Date().toUTCString())).Format("yyyyMMddTHHmmssZ")
        opt.datetime = this.getDateTime();
        const header = new Map();
        /* istanbul ignore if */

        if (!opt.headers) {
          const h = {};
          opt.headers = h;
        }

        opt.headers.host = `${opt.host}`;
        /* istanbul ignore if */

        if (!isDefaultPort(opt.port)) {
          opt.headers.host += ':' + opt.port;
        }
        /* istanbul ignore if */


        if (opt.endpoints) {
          opt.headers.host = `${this.options.bucket}.${opt.endpoints}`;
        }

        header.set('host', opt.headers.host); // opt.endpoints as string)

        header.set('x-tos-date', opt.datetime); // opt.datetime)

        /* istanbul ignore if
          if (opt.endpoints) {
              let bucket = this.options.bucket;
              if (opt.bucket) {
                  bucket = opt.bucket;
              }
              if (!opt.path || opt.path === '/' || opt.path === `/${bucket}`) {
                  opt.path = '/' + bucket;
              } else {
                  opt.path = '/' + bucket + opt.path;
              }
          }
          */

        header.set('x-tos-content-sha256', this.hexEncodedBodyHash());

        if (this.options.securityToken) {
          header.set('x-tos-security-token', this.options.securityToken);
        } // x-tos- must to be signatured


        header.forEach((value, key) => {
          if (key.startsWith('x-tos')) {
            opt.headers[key] = value;
          }
        });
        opt.path = this.getEncodePath(opt.path);
        const sign = this.signature(opt, 0, credentials);
        header.set('authorization', sign);
        return header;
      };

      this.gnrCopySig = (opt, credentials) => {
        return {
          key: '',
          value: ''
        };
      };

      this.getSignature = (opt, expiredAt) => {
        return {
          key: '',
          value: ''
        };
      };

      this.getSignatureQuery = (opt, expiredAt) => {
        opt.datetime = this.getDateTime();

        if (!opt.headers) {
          const h = {};
          opt.headers = h;
        }

        opt.headers.host = `${opt.host}`;

        if (!isDefaultPort(opt.port)) {
          opt.headers.host += ':' + opt.port;
        }

        opt.path = this.getEncodePath(opt.path);

        if (opt.endpoints) {
          opt.headers.host = `${this.options.bucket}.${opt.endpoints}`; // opt.path = `${opt.path}`;
        }

        opt.headers[SIG_QUERY.v4_date] = opt.datetime;
        const credString = this.credentialString(opt.datetime);
        const res = { ...(opt.query || {}),
          [SIG_QUERY.v4_algorithm]: this.options.algorithm,
          [SIG_QUERY.v4_content_sha]: this.hexEncodedBodyHash(),
          [SIG_QUERY.v4_credential]: this.credentials.GetAccessKey() + '/' + credString,
          [SIG_QUERY.v4_date]: opt.datetime,
          [SIG_QUERY.v4_expires]: '' + expiredAt,
          [SIG_QUERY.v4_signedHeaders]: this.signedHeaders(opt)
        };

        if (this.options.securityToken) {
          res[SIG_QUERY.v4_security_token] = this.options.securityToken;
        }

        opt.query = getSortedQueryString(res);
        res[SIG_QUERY.v4_signature] = this.authorization(opt, this.credentials, expiredAt);
        return res;
      };

      this.getSignaturePolicyQuery = (opt, expiredAt) => {
        opt.datetime = this.getDateTime();
        const credString = this.credentialString(opt.datetime);
        const res = {
          [SIG_QUERY.v4_algorithm]: this.options.algorithm,
          [SIG_QUERY.v4_credential]: this.credentials.GetAccessKey() + '/' + credString,
          [SIG_QUERY.v4_date]: opt.datetime,
          [SIG_QUERY.v4_expires]: '' + expiredAt,
          [SIG_QUERY.v4_policy]: stringify$2(parse$3(JSON.stringify(opt.policy), 'utf-8'), 'base64')
        };

        if (this.options.securityToken) {
          res[SIG_QUERY.v4_security_token] = this.options.securityToken;
        }

        opt.query = getSortedQueryString(res);
        res[SIG_QUERY.v4_signature] = this.authorization(opt, this.credentials, expiredAt);
        return res;
      };

      this.hexEncodedBodyHash = () => {
        return 'UNSIGNED-PAYLOAD'; // return this.hexEncodedHash('');
      };

      this.authorization = (opt, credentials, expiredAt) => {
        /* istanbul ignore if */
        if (!opt.datetime) {
          return '';
        }

        const signingKey = this.getSigningKey(credentials, opt.datetime.substr(0, 8)); // console.log(
        // 'signingKey:',
        //  signingKey,
        //  'sign:',
        //  this.stringToSign(opt.datetime, opt)
        //  );

        return hmacSha256$2(signingKey, this.stringToSign(opt.datetime, opt), 'hex');
      };

      this.getDateTime = () => {
        const date = new Date(new Date().toUTCString());
        const datetime = date.toISOString().replace(/\..+/, '').replace(/-/g, '').replace(/:/g, '') + 'Z';
        return datetime;
      };

      this.credentialString = datetime => {
        return this.createScope(datetime.substr(0, 8), this.options.region, this.options.serviceName);
      };

      this.createScope = (date, region, serviceName) => {
        return [date.substr(0, 8), region, serviceName, v4Identifier].join('/');
      };

      this.getSigningKey = (credentials, date) => {
        const kDate = hmacSha256$2(credentials.GetSecretKey(), date);
        const kRegion = hmacSha256$2(kDate, this.options.region);
        const kService = hmacSha256$2(kRegion, this.options.serviceName);
        const signingKey = hmacSha256$2(kService, v4Identifier);
        return signingKey;
      };

      this.stringToSign = (datetime, opt) => {
        /* istanbul ignore if */
        if (!this.options.algorithm) {
          return '';
        }

        const parts = [];
        parts.push(this.options.algorithm);
        parts.push(datetime);
        parts.push(this.credentialString(datetime));
        const canonicalString = 'policy' in opt ? this.canonicalStringPolicy(opt) : this.canonicalString(opt); // console.log('canonicalString',this.canonicalString(opt),' code:',this.hexEncodedHash(this.canonicalString(opt)));

        parts.push(this.hexEncodedHash(canonicalString));
        return parts.join('\n');
      };

      this.hexEncodedHash = string => {
        return hashSha256$1(string, 'hex');
      };

      this.canonicalString = opt => {
        const parts = [];
        parts.push(opt.method);
        parts.push(opt.path);
        parts.push(this.getEncodePath(opt.query, false));
        parts.push(this.canonicalHeaders(opt) + '\n');
        parts.push(this.signedHeaders(opt));
        parts.push(this.hexEncodedBodyHash());
        return parts.join('\n');
      };

      this.canonicalStringPolicy = opt => {
        const parts = [];
        parts.push(this.getEncodePath(opt.query, false));
        parts.push(this.hexEncodedBodyHash());
        return parts.join('\n');
      };

      this.canonicalHeaders = opt => {
        const parts = [];
        const needSignHeaders = getNeedSignedHeaders(opt.headers);

        for (let key of needSignHeaders) {
          const value = opt.headers[key];
          key = key.toLowerCase();
          parts.push(key + ':' + this.canonicalHeaderValues(value.toString()));
        }

        return parts.join('\n');
      };

      this.canonicalHeaderValues = values => {
        return values.replace(/\s+/g, ' ').replace(/^\s+|\s+$/g, '');
      };

      this.signedHeaders = opt => {
        const keys = [];
        const needSignHeaders = getNeedSignedHeaders(opt.headers);

        for (let key of needSignHeaders) {
          key = key.toLowerCase();
          keys.push(key);
        }

        return keys.sort().join(';');
      };

      this.options = _opt;
      this.credentials = _credentials;
    }
    /*
     * normal v4 signature
     * */


    /**
     * ! * ' () aren't transformed by encodeUrl, so they need be handled
     */
    getEncodePath(path, encodeAll = true) {
      if (!path) {
        return '';
      }

      let tmpPath = path;

      if (encodeAll) {
        tmpPath = path.replace(/%2F/g, '/');
      }

      tmpPath = tmpPath.replace(/\(/g, '%28');
      tmpPath = tmpPath.replace(/\)/g, '%29');
      tmpPath = tmpPath.replace(/!/g, '%21');
      tmpPath = tmpPath.replace(/\*/g, '%2A');
      tmpPath = tmpPath.replace(/\'/g, '%27');
      return tmpPath;
    }

  }
  class ISigV4Credentials {
    constructor(securityToken, secretAccessKey, accessKeyId) {
      this.securityToken = void 0;
      this.secretAccessKey = void 0;
      this.accessKeyId = void 0;
      this.accessKeyId = accessKeyId;
      this.secretAccessKey = secretAccessKey;
      this.securityToken = securityToken;
    }

    GetAccessKey() {
      return this.accessKeyId;
    }

    GetSecretKey() {
      return this.secretAccessKey;
    }

  }

  function getNeedSignedHeaders(headers) {
    const needSignHeaders = [];
    Object.keys(headers || {}).forEach(key => {
      if (key === 'host' || key.startsWith('x-tos-')) {
        if (headers[key] != null) {
          needSignHeaders.push(key);
        }
      }
    });
    return needSignHeaders.sort();
  }

  // auto generated by scripts/build.js
  var version$1 = '2.7.4';

  /**
   * forked from  https://github.com/bigmeow/axios-miniprogram-adapter
   * author bigMeow
   */
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/='; // encoder

  function encoder(input) {
    const str = String(input); // initialize result and counter

    let block;
    let charCode;
    let idx = 0;
    let map = chars;
    let output = '';

    for (; // if the next str index does not exist:
    //   change the mapping table to "="
    //   check if d has no fractional digits
    str.charAt(idx | 0) || (map = '=', idx % 1); // "8 - idx % 1 * 8" generates the sequence 2, 4, 6, 8
    output += map.charAt(63 & block >> 8 - idx % 1 * 8)) {
      charCode = str.charCodeAt(idx += 3 / 4);

      if (charCode > 0xff) {
        throw new Error("'btoa' failed: The string to be encoded contains characters outside of the Latin1 range.");
      } //@ts-expect-error


      block = block << 8 | charCode;
    }

    return output;
  }

  /**
   * forked from  https://github.com/bigmeow/axios-miniprogram-adapter
   * author bigMeow
   */
  let platFormName = "wexin"
  /* weixin */
  ;
  /**
   * 获取各个平台的请求函数
   */

  function getRequest() {
    switch (true) {
      case typeof wx === 'object':
        platFormName = "wexin"
        /* weixin */
        ;
        return wx.request.bind(wx);

      case typeof swan === 'object':
        platFormName = "baidu"
        /* baidu */
        ;
        return swan.request.bind(swan);

      case typeof dd === 'object':
        platFormName = "dd"
        /* dingding */
        ; // https://open.dingtalk.com/document/orgapp-client/send-network-requests

        return dd.httpRequest.bind(dd);

      case typeof my === 'object':
        /**
         * remark:
         * 支付宝客户端已不再维护 my.httpRequest，建议使用 my.request。另外，钉钉客户端尚不支持 my.request。若在钉钉客户端开发小程序，则需要使用 my.httpRequest。
         * my.httpRequest的请求头默认值为{'content-type': 'application/x-www-form-urlencoded'}。
         * my.request的请求头默认值为{'content-type': 'application/json'}。
         * 还有个 dd.httpRequest
         */
        platFormName = "alipay"
        /* zhifubao */
        ;
        return (my.request || my.httpRequest).bind(my);

      default:
        return wx.request.bind(wx);
    }
  }
  /**
   * 处理各平台返回的响应数据，抹平差异
   * @param mpResponse
   * @param config axios处理过的请求配置对象
   * @param request 小程序的调用发起请求时，传递给小程序api的实际配置
   */

  function transformResponse(mpResponse, config, mpRequestOption) {
    const headers = mpResponse.header || mpResponse.headers;
    const status = mpResponse.statusCode || mpResponse.status;
    let statusText = '';

    if (status === 200) {
      statusText = 'OK';
    } else if (status === 400) {
      statusText = 'Bad Request';
    }

    const response = {
      data: mpResponse.data,
      status,
      statusText,
      headers,
      config,
      request: mpRequestOption
    };
    return response;
  }
  /**
   * 处理各平台返回的错误信息，抹平差异
   * @param error 小程序api返回的错误对象
   * @param reject 上层的promise reject 函数
   * @param config
   */

  function transformError(error, reject, config) {
    switch (platFormName) {
      case "wexin"
      /* weixin */
      :
        if (error.errMsg.indexOf('request:fail abort') !== -1) {
          // Handle request cancellation (as opposed to a manual cancellation)
          reject(createError('Request aborted', config, 'ECONNABORTED', ''));
        } else if (error.errMsg.indexOf('timeout') !== -1) {
          // timeout
          reject(createError('timeout of ' + config.timeout + 'ms exceeded', config, 'ECONNABORTED', ''));
        } else {
          // NetWordError
          reject(createError('Network Error', config, null, ''));
        }

        break;

      case "dd"
      /* dingding */
      :
      case "alipay"
      /* zhifubao */
      :
        // https://docs.alipay.com/mini/api/network
        if ([14, 19].includes(error.error)) {
          reject(createError('Request aborted', config, 'ECONNABORTED', '', error));
        } else if ([13].includes(error.error)) {
          // timeout
          reject(createError('timeout of ' + config.timeout + 'ms exceeded', config, 'ECONNABORTED', '', error));
        } else {
          // NetWordError
          reject(createError('Network Error', config, null, '', error));
        }

        break;

      case "baidu"
      /* baidu */
      :
        // TODO error.errCode
        reject(createError('Network Error', config, null, ''));
        break;
    }
  }
  /**
   * 将axios的请求配置，转换成各个平台都支持的请求config
   * @param config
   */

  function transformConfig(config) {
    if (["alipay"
    /* zhifubao */
    , "dd"
    /* dingding */
    ].includes(platFormName)) {
      var _config$headers;

      config.headers = config.header;
      delete config.header;

      if ("dd"
      /* dingding */
      === platFormName && config.method !== 'GET' && ((_config$headers = config.headers) == null ? void 0 : _config$headers['Content-Type']) === 'application/json' && Object.prototype.toString.call(config.data) === '[object Object]') {
        // Content-Type为application/json时，data参数只支持json字符串，需要手动调用JSON.stringify进行序列化
        config.data = JSON.stringify(config.data);
      }
    }

    return config;
  }

  //@ts-ignore

  const isJSONstr = str => {
    try {
      return typeof str === 'string' && str.length && (str = JSON.parse(str)) && Object.prototype.toString.call(str) === '[object Object]';
    } catch (error) {
      return false;
    }
  };

  function mpAdapter(config, {
    transformRequestOption = requestOption => requestOption
  } = {}) {
    const request = getRequest();
    return new Promise((resolve, reject) => {
      let requestTask;
      let requestData = config.data;
      let requestHeaders = config.headers; // baidu miniprogram only support upperCase

      let requestMethod = config.method && config.method.toUpperCase() || 'GET'; // miniprogram network request config

      const mpRequestOption = {
        method: requestMethod,
        url: buildURL(buildFullPath(config.baseURL, config.url), config.params, config.paramsSerializer),
        timeout: config.timeout,
        // Listen for success
        success: mpResponse => {
          const response = transformResponse(mpResponse, config, mpRequestOption);
          settle(resolve, reject, response);
        },
        // Handle request Exception
        fail: error => {
          transformError(error, reject, config);
        },

        complete() {
          requestTask = undefined;
        }

      }; // HTTP basic authentication

      if (config.auth) {
        const [username, password] = [config.auth.username || '', config.auth.password || ''];
        requestHeaders.Authorization = 'Basic ' + encoder(username + ':' + password);
      } // Add headers to the request


      utils.forEach(requestHeaders, function setRequestHeader(_val, key) {
        const _header = key.toLowerCase();

        if (typeof requestData === 'undefined' && _header === 'content-type' || _header === 'referer') {
          // Remove Content-Type if data is undefined
          // And the miniprogram document said that '设置请求的 header，header 中不能设置 Referer'
          delete requestHeaders[key];
        }
      });
      mpRequestOption.header = requestHeaders; // Add responseType to request if needed

      if (config.responseType) {
        mpRequestOption.responseType = config.responseType;
      }

      if (config.cancelToken) {
        // Handle cancellation
        config.cancelToken.promise.then(function onCanceled(cancel) {
          if (!requestTask) {
            return;
          }

          requestTask.abort();
          reject(cancel); // Clean up request

          requestTask = undefined;
        });
      } // Converting JSON strings to objects is handed over to the MiniPrograme


      if (isJSONstr(requestData)) {
        requestData = JSON.parse(requestData);
      }

      if (requestData !== undefined) {
        mpRequestOption.data = requestData;
      }

      requestTask = request(transformRequestOption(transformConfig(mpRequestOption)));
    });
  }

  var isMultiUpload = function isMultiUpload(config) {
    return Array.isArray(config.files) && config.files.length > 0
  };

  var isUploadFile = function isUploadFile(config) {
    if (config.method === 'post') {
      if (config.filePath && config.name) return true

      if (isMultiUpload(config)) return true
    }

    return false
  };

  var bind$1 = function bind(fn, thisArg) {
    return function wrap() {
      var args = new Array(arguments.length);
      for (var i = 0; i < args.length; i++) {
        args[i] = arguments[i];
      }
      return fn.apply(thisArg, args);
    };
  };

  // utils is a library of generic helper functions non-specific to axios

  var toString$2 = Object.prototype.toString;

  // eslint-disable-next-line func-names
  var kindOf = (function(cache) {
    // eslint-disable-next-line func-names
    return function(thing) {
      var str = toString$2.call(thing);
      return cache[str] || (cache[str] = str.slice(8, -1).toLowerCase());
    };
  })(Object.create(null));

  function kindOfTest(type) {
    type = type.toLowerCase();
    return function isKindOf(thing) {
      return kindOf(thing) === type;
    };
  }

  /**
   * Determine if a value is an Array
   *
   * @param {Object} val The value to test
   * @returns {boolean} True if value is an Array, otherwise false
   */
  function isArray$6(val) {
    return Array.isArray(val);
  }

  /**
   * Determine if a value is undefined
   *
   * @param {Object} val The value to test
   * @returns {boolean} True if the value is undefined, otherwise false
   */
  function isUndefined$1(val) {
    return typeof val === 'undefined';
  }

  /**
   * Determine if a value is a Buffer
   *
   * @param {Object} val The value to test
   * @returns {boolean} True if value is a Buffer, otherwise false
   */
  function isBuffer$3(val) {
    return val !== null && !isUndefined$1(val) && val.constructor !== null && !isUndefined$1(val.constructor)
      && typeof val.constructor.isBuffer === 'function' && val.constructor.isBuffer(val);
  }

  /**
   * Determine if a value is an ArrayBuffer
   *
   * @function
   * @param {Object} val The value to test
   * @returns {boolean} True if value is an ArrayBuffer, otherwise false
   */
  var isArrayBuffer$1 = kindOfTest('ArrayBuffer');


  /**
   * Determine if a value is a view on an ArrayBuffer
   *
   * @param {Object} val The value to test
   * @returns {boolean} True if value is a view on an ArrayBuffer, otherwise false
   */
  function isArrayBufferView$1(val) {
    var result;
    if ((typeof ArrayBuffer !== 'undefined') && (ArrayBuffer.isView)) {
      result = ArrayBuffer.isView(val);
    } else {
      result = (val) && (val.buffer) && (isArrayBuffer$1(val.buffer));
    }
    return result;
  }

  /**
   * Determine if a value is a String
   *
   * @param {Object} val The value to test
   * @returns {boolean} True if value is a String, otherwise false
   */
  function isString$2(val) {
    return typeof val === 'string';
  }

  /**
   * Determine if a value is a Number
   *
   * @param {Object} val The value to test
   * @returns {boolean} True if value is a Number, otherwise false
   */
  function isNumber$2(val) {
    return typeof val === 'number';
  }

  /**
   * Determine if a value is an Object
   *
   * @param {Object} val The value to test
   * @returns {boolean} True if value is an Object, otherwise false
   */
  function isObject$2(val) {
    return val !== null && typeof val === 'object';
  }

  /**
   * Determine if a value is a plain Object
   *
   * @param {Object} val The value to test
   * @return {boolean} True if value is a plain Object, otherwise false
   */
  function isPlainObject$1(val) {
    if (kindOf(val) !== 'object') {
      return false;
    }

    var prototype = Object.getPrototypeOf(val);
    return prototype === null || prototype === Object.prototype;
  }

  /**
   * Determine if a value is a Date
   *
   * @function
   * @param {Object} val The value to test
   * @returns {boolean} True if value is a Date, otherwise false
   */
  var isDate$2 = kindOfTest('Date');

  /**
   * Determine if a value is a File
   *
   * @function
   * @param {Object} val The value to test
   * @returns {boolean} True if value is a File, otherwise false
   */
  var isFile$1 = kindOfTest('File');

  /**
   * Determine if a value is a Blob
   *
   * @function
   * @param {Object} val The value to test
   * @returns {boolean} True if value is a Blob, otherwise false
   */
  var isBlob$2 = kindOfTest('Blob');

  /**
   * Determine if a value is a FileList
   *
   * @function
   * @param {Object} val The value to test
   * @returns {boolean} True if value is a File, otherwise false
   */
  var isFileList = kindOfTest('FileList');

  /**
   * Determine if a value is a Function
   *
   * @param {Object} val The value to test
   * @returns {boolean} True if value is a Function, otherwise false
   */
  function isFunction$2(val) {
    return toString$2.call(val) === '[object Function]';
  }

  /**
   * Determine if a value is a Stream
   *
   * @param {Object} val The value to test
   * @returns {boolean} True if value is a Stream, otherwise false
   */
  function isStream$1(val) {
    return isObject$2(val) && isFunction$2(val.pipe);
  }

  /**
   * Determine if a value is a FormData
   *
   * @param {Object} thing The value to test
   * @returns {boolean} True if value is an FormData, otherwise false
   */
  function isFormData$1(thing) {
    var pattern = '[object FormData]';
    return thing && (
      (typeof FormData === 'function' && thing instanceof FormData) ||
      toString$2.call(thing) === pattern ||
      (isFunction$2(thing.toString) && thing.toString() === pattern)
    );
  }

  /**
   * Determine if a value is a URLSearchParams object
   * @function
   * @param {Object} val The value to test
   * @returns {boolean} True if value is a URLSearchParams object, otherwise false
   */
  var isURLSearchParams$1 = kindOfTest('URLSearchParams');

  /**
   * Trim excess whitespace off the beginning and end of a string
   *
   * @param {String} str The String to trim
   * @returns {String} The String freed of excess whitespace
   */
  function trim$1(str) {
    return str.trim ? str.trim() : str.replace(/^\s+|\s+$/g, '');
  }

  /**
   * Determine if we're running in a standard browser environment
   *
   * This allows axios to run in a web worker, and react-native.
   * Both environments support XMLHttpRequest, but not fully standard globals.
   *
   * web workers:
   *  typeof window -> undefined
   *  typeof document -> undefined
   *
   * react-native:
   *  navigator.product -> 'ReactNative'
   * nativescript
   *  navigator.product -> 'NativeScript' or 'NS'
   */
  function isStandardBrowserEnv$1() {
    if (typeof navigator !== 'undefined' && (navigator.product === 'ReactNative' ||
                                             navigator.product === 'NativeScript' ||
                                             navigator.product === 'NS')) {
      return false;
    }
    return (
      typeof window !== 'undefined' &&
      typeof document !== 'undefined'
    );
  }

  /**
   * Iterate over an Array or an Object invoking a function for each item.
   *
   * If `obj` is an Array callback will be called passing
   * the value, index, and complete array for each item.
   *
   * If 'obj' is an Object callback will be called passing
   * the value, key, and complete object for each property.
   *
   * @param {Object|Array} obj The object to iterate
   * @param {Function} fn The callback to invoke for each item
   */
  function forEach$1(obj, fn) {
    // Don't bother if no value provided
    if (obj === null || typeof obj === 'undefined') {
      return;
    }

    // Force an array if not already something iterable
    if (typeof obj !== 'object') {
      /*eslint no-param-reassign:0*/
      obj = [obj];
    }

    if (isArray$6(obj)) {
      // Iterate over array values
      for (var i = 0, l = obj.length; i < l; i++) {
        fn.call(null, obj[i], i, obj);
      }
    } else {
      // Iterate over object keys
      for (var key in obj) {
        if (Object.prototype.hasOwnProperty.call(obj, key)) {
          fn.call(null, obj[key], key, obj);
        }
      }
    }
  }

  /**
   * Accepts varargs expecting each argument to be an object, then
   * immutably merges the properties of each object and returns result.
   *
   * When multiple objects contain the same key the later object in
   * the arguments list will take precedence.
   *
   * Example:
   *
   * ```js
   * var result = merge({foo: 123}, {foo: 456});
   * console.log(result.foo); // outputs 456
   * ```
   *
   * @param {Object} obj1 Object to merge
   * @returns {Object} Result of all merge properties
   */
  function merge$2(/* obj1, obj2, obj3, ... */) {
    var result = {};
    function assignValue(val, key) {
      if (isPlainObject$1(result[key]) && isPlainObject$1(val)) {
        result[key] = merge$2(result[key], val);
      } else if (isPlainObject$1(val)) {
        result[key] = merge$2({}, val);
      } else if (isArray$6(val)) {
        result[key] = val.slice();
      } else {
        result[key] = val;
      }
    }

    for (var i = 0, l = arguments.length; i < l; i++) {
      forEach$1(arguments[i], assignValue);
    }
    return result;
  }

  /**
   * Extends object a by mutably adding to it the properties of object b.
   *
   * @param {Object} a The object to be extended
   * @param {Object} b The object to copy properties from
   * @param {Object} thisArg The object to bind function to
   * @return {Object} The resulting value of object a
   */
  function extend$1(a, b, thisArg) {
    forEach$1(b, function assignValue(val, key) {
      if (thisArg && typeof val === 'function') {
        a[key] = bind$1(val, thisArg);
      } else {
        a[key] = val;
      }
    });
    return a;
  }

  /**
   * Remove byte order marker. This catches EF BB BF (the UTF-8 BOM)
   *
   * @param {string} content with BOM
   * @return {string} content value without BOM
   */
  function stripBOM$1(content) {
    if (content.charCodeAt(0) === 0xFEFF) {
      content = content.slice(1);
    }
    return content;
  }

  /**
   * Inherit the prototype methods from one constructor into another
   * @param {function} constructor
   * @param {function} superConstructor
   * @param {object} [props]
   * @param {object} [descriptors]
   */

  function inherits(constructor, superConstructor, props, descriptors) {
    constructor.prototype = Object.create(superConstructor.prototype, descriptors);
    constructor.prototype.constructor = constructor;
    props && Object.assign(constructor.prototype, props);
  }

  /**
   * Resolve object with deep prototype chain to a flat object
   * @param {Object} sourceObj source object
   * @param {Object} [destObj]
   * @param {Function} [filter]
   * @returns {Object}
   */

  function toFlatObject(sourceObj, destObj, filter) {
    var props;
    var i;
    var prop;
    var merged = {};

    destObj = destObj || {};

    do {
      props = Object.getOwnPropertyNames(sourceObj);
      i = props.length;
      while (i-- > 0) {
        prop = props[i];
        if (!merged[prop]) {
          destObj[prop] = sourceObj[prop];
          merged[prop] = true;
        }
      }
      sourceObj = Object.getPrototypeOf(sourceObj);
    } while (sourceObj && (!filter || filter(sourceObj, destObj)) && sourceObj !== Object.prototype);

    return destObj;
  }

  /*
   * determines whether a string ends with the characters of a specified string
   * @param {String} str
   * @param {String} searchString
   * @param {Number} [position= 0]
   * @returns {boolean}
   */
  function endsWith(str, searchString, position) {
    str = String(str);
    if (position === undefined || position > str.length) {
      position = str.length;
    }
    position -= searchString.length;
    var lastIndex = str.indexOf(searchString, position);
    return lastIndex !== -1 && lastIndex === position;
  }


  /**
   * Returns new array from array like object
   * @param {*} [thing]
   * @returns {Array}
   */
  function toArray(thing) {
    if (!thing) return null;
    var i = thing.length;
    if (isUndefined$1(i)) return null;
    var arr = new Array(i);
    while (i-- > 0) {
      arr[i] = thing[i];
    }
    return arr;
  }

  // eslint-disable-next-line func-names
  var isTypedArray$1 = (function(TypedArray) {
    // eslint-disable-next-line func-names
    return function(thing) {
      return TypedArray && thing instanceof TypedArray;
    };
  })(typeof Uint8Array !== 'undefined' && Object.getPrototypeOf(Uint8Array));

  var utils$2 = {
    isArray: isArray$6,
    isArrayBuffer: isArrayBuffer$1,
    isBuffer: isBuffer$3,
    isFormData: isFormData$1,
    isArrayBufferView: isArrayBufferView$1,
    isString: isString$2,
    isNumber: isNumber$2,
    isObject: isObject$2,
    isPlainObject: isPlainObject$1,
    isUndefined: isUndefined$1,
    isDate: isDate$2,
    isFile: isFile$1,
    isBlob: isBlob$2,
    isFunction: isFunction$2,
    isStream: isStream$1,
    isURLSearchParams: isURLSearchParams$1,
    isStandardBrowserEnv: isStandardBrowserEnv$1,
    forEach: forEach$1,
    merge: merge$2,
    extend: extend$1,
    trim: trim$1,
    stripBOM: stripBOM$1,
    inherits: inherits,
    toFlatObject: toFlatObject,
    kindOf: kindOf,
    kindOfTest: kindOfTest,
    endsWith: endsWith,
    toArray: toArray,
    isTypedArray: isTypedArray$1,
    isFileList: isFileList
  };

  /**
   * Create an Error with the specified message, config, error code, request and response.
   *
   * @param {string} message The error message.
   * @param {string} [code] The error code (for example, 'ECONNABORTED').
   * @param {Object} [config] The config.
   * @param {Object} [request] The request.
   * @param {Object} [response] The response.
   * @returns {Error} The created error.
   */
  function AxiosError(message, code, config, request, response) {
    Error.call(this);
    this.message = message;
    this.name = 'AxiosError';
    code && (this.code = code);
    config && (this.config = config);
    request && (this.request = request);
    response && (this.response = response);
  }

  utils$2.inherits(AxiosError, Error, {
    toJSON: function toJSON() {
      return {
        // Standard
        message: this.message,
        name: this.name,
        // Microsoft
        description: this.description,
        number: this.number,
        // Mozilla
        fileName: this.fileName,
        lineNumber: this.lineNumber,
        columnNumber: this.columnNumber,
        stack: this.stack,
        // Axios
        config: this.config,
        code: this.code,
        status: this.response && this.response.status ? this.response.status : null
      };
    }
  });

  var prototype = AxiosError.prototype;
  var descriptors = {};

  [
    'ERR_BAD_OPTION_VALUE',
    'ERR_BAD_OPTION',
    'ECONNABORTED',
    'ETIMEDOUT',
    'ERR_NETWORK',
    'ERR_FR_TOO_MANY_REDIRECTS',
    'ERR_DEPRECATED',
    'ERR_BAD_RESPONSE',
    'ERR_BAD_REQUEST',
    'ERR_CANCELED'
  // eslint-disable-next-line func-names
  ].forEach(function(code) {
    descriptors[code] = {value: code};
  });

  Object.defineProperties(AxiosError, descriptors);
  Object.defineProperty(prototype, 'isAxiosError', {value: true});

  // eslint-disable-next-line func-names
  AxiosError.from = function(error, code, config, request, response, customProps) {
    var axiosError = Object.create(prototype);

    utils$2.toFlatObject(error, axiosError, function filter(obj) {
      return obj !== Error.prototype;
    });

    AxiosError.call(axiosError, error.message, code, config, request, response);

    axiosError.name = error.name;

    customProps && Object.assign(axiosError, customProps);

    return axiosError;
  };

  var AxiosError_1 = AxiosError;

  /**
   * Resolve or reject a Promise based on response status.
   *
   * @param {Function} resolve A function that resolves the promise.
   * @param {Function} reject A function that rejects the promise.
   * @param {object} response The response.
   */
  var settle$1 = function settle(resolve, reject, response) {
    var validateStatus = response.config.validateStatus;
    if (!response.status || !validateStatus || validateStatus(response.status)) {
      resolve(response);
    } else {
      reject(new AxiosError_1(
        'Request failed with status code ' + response.status,
        [AxiosError_1.ERR_BAD_REQUEST, AxiosError_1.ERR_BAD_RESPONSE][Math.floor(response.status / 100) - 4],
        response.config,
        response.request,
        response
      ));
    }
  };

  function encode$2(val) {
    return encodeURIComponent(val).
      replace(/%3A/gi, ':').
      replace(/%24/g, '$').
      replace(/%2C/gi, ',').
      replace(/%20/g, '+').
      replace(/%5B/gi, '[').
      replace(/%5D/gi, ']');
  }

  /**
   * Build a URL by appending params to the end
   *
   * @param {string} url The base of the url (e.g., http://www.google.com)
   * @param {object} [params] The params to be appended
   * @returns {string} The formatted url
   */
  var buildURL$1 = function buildURL(url, params, paramsSerializer) {
    /*eslint no-param-reassign:0*/
    if (!params) {
      return url;
    }

    var serializedParams;
    if (paramsSerializer) {
      serializedParams = paramsSerializer(params);
    } else if (utils$2.isURLSearchParams(params)) {
      serializedParams = params.toString();
    } else {
      var parts = [];

      utils$2.forEach(params, function serialize(val, key) {
        if (val === null || typeof val === 'undefined') {
          return;
        }

        if (utils$2.isArray(val)) {
          key = key + '[]';
        } else {
          val = [val];
        }

        utils$2.forEach(val, function parseValue(v) {
          if (utils$2.isDate(v)) {
            v = v.toISOString();
          } else if (utils$2.isObject(v)) {
            v = JSON.stringify(v);
          }
          parts.push(encode$2(key) + '=' + encode$2(v));
        });
      });

      serializedParams = parts.join('&');
    }

    if (serializedParams) {
      var hashmarkIndex = url.indexOf('#');
      if (hashmarkIndex !== -1) {
        url = url.slice(0, hashmarkIndex);
      }

      url += (url.indexOf('?') === -1 ? '?' : '&') + serializedParams;
    }

    return url;
  };

  /**
   * Determines whether the specified URL is absolute
   *
   * @param {string} url The URL to test
   * @returns {boolean} True if the specified URL is absolute, otherwise false
   */
  var isAbsoluteURL$1 = function isAbsoluteURL(url) {
    // A URL is considered absolute if it begins with "<scheme>://" or "//" (protocol-relative URL).
    // RFC 3986 defines scheme name as a sequence of characters beginning with a letter and followed
    // by any combination of letters, digits, plus, period, or hyphen.
    return /^([a-z][a-z\d+\-.]*:)?\/\//i.test(url);
  };

  /**
   * Creates a new URL by combining the specified URLs
   *
   * @param {string} baseURL The base URL
   * @param {string} relativeURL The relative URL
   * @returns {string} The combined URL
   */
  var combineURLs$1 = function combineURLs(baseURL, relativeURL) {
    return relativeURL
      ? baseURL.replace(/\/+$/, '') + '/' + relativeURL.replace(/^\/+/, '')
      : baseURL;
  };

  /**
   * Creates a new URL by combining the baseURL with the requestedURL,
   * only when the requestedURL is not already an absolute URL.
   * If the requestURL is absolute, this function returns the requestedURL untouched.
   *
   * @param {string} baseURL The base URL
   * @param {string} requestedURL Absolute or relative URL to combine
   * @returns {string} The combined full path
   */
  var buildFullPath$1 = function buildFullPath(baseURL, requestedURL) {
    if (baseURL && !isAbsoluteURL$1(requestedURL)) {
      return combineURLs$1(baseURL, requestedURL);
    }
    return requestedURL;
  };

  // var utils = require("axios/lib/utils");

  // var cookies = require("axios/lib/helpers/cookies");


  // var parseHeaders = require("axios/lib/helpers/parseHeaders");
  // var isURLSameOrigin = require("axios/lib/helpers/isURLSameOrigin");
  // var createError = require("axios/lib/core/createError");


  var format = function format(config, resolve, reject) {
    const fullPath = buildFullPath$1(config.baseURL, config.url);
    const requestHeaders = config.headers;

    const uniConfig = {
      ...config,
      url: buildURL$1(fullPath, config.params, config.paramsSerializer),

      // uniapp 用的是 header
      header: requestHeaders,
    };

    if (isUploadFile(config)) {
      delete requestHeaders['Content-Type']; // Let the browser set it
      if (config.formData) {
        uniConfig.formData = config.formData;
      } else {
        // application/json 且 data isObject 时， 发送前会对 config.data 进行 JSON.stringify 处理
        // uniapp 内部会处理，即需要的就是 object， 所以需要提前 parse
        if (typeof config.data === 'string') {
          // 如果，config.data 数据格式不合适，还是选择报错
          uniConfig.formData = JSON.parse(config.data);
        } else {
          uniConfig.formData = config.data;
        }
      }
    } else if (config.method === 'get') {
      // 兼容 get 时的 params 字段
      uniConfig.data = config.data ? config.data : config.params;
    } else {
      uniConfig.data = config.data;
    }

    // HTTP basic authentication
    if (config.auth) {
      var username = config.auth.username || '';
      var password = unescape(encodeURIComponent(config.auth.password)) || '';
      requestHeaders.Authorization = 'Basic ' + btoa(username + ':' + password);
    }

    uniConfig.complete = function (response) {
      // 暂时不明白为什么要判断 responseType === 'text'，也许返回结果是有多种格式的，但是目前没碰到。
      // var responseData = !config.responseType || config.responseType === 'text' ? request.responseText : request.response;
      var result = {
        data: response.data,
        status: response.statusCode,
        statusText: response.errMsg,
        header: response.header,
        config: config,
        // request: request
      };

      settle$1(resolve, reject, result);
    };

    return uniConfig
  };

  /**
   * 参数配置参考：
   *   > axios          https://www.npmjs.com/package/axios#request-config
   *   > uniapp request https://uniapp.dcloud.io/api/request/request
   *   > uniapp upload  https://uniapp.dcloud.io/api/request/network-file
   * @param {object} config
   */
  function uniappAdapter(config = {}) {
    return new Promise(function dispatchUniApp(resolve, reject) {
      const uniConfig = format(config, resolve, reject);

      let requestTask = null;
      if (config.cancelToken) {
        // Handle cancellation
        config.cancelToken.promise.then(function onCanceled(cancel) {
          if (!requestTask) {
            return
          }

          requestTask.abort();
          reject(cancel);
          // Clean up request
          requestTask = null;
        });
      }

      // Send the request
      if (isUploadFile(config)) {
        requestTask = uni.uploadFile(uniConfig);
      } else {
        requestTask = uni.request(uniConfig);
      }
    })
  }
  var axiosAdapterUniapp = uniappAdapter;

  // Allow use of default import syntax in TypeScript
  var _default$1 = uniappAdapter;
  axiosAdapterUniapp.default = _default$1;

  class TOSBase {
    constructor(_opts) {
      this.opts = void 0;
      this.axiosInst = void 0;
      this.userAgent = void 0;
      this.httpAgent = void 0;
      this.httpsAgent = void 0;

      this.getObjectPath = opts => {
        const actualBucket = typeof opts !== 'string' && opts.bucket || this.opts.bucket;
        const actualKey = typeof opts === 'string' ? opts : opts.key;

        if (!actualBucket) {
          throw new TosClientError('Must provide bucket param');
        }

        return `/${actualBucket}/${encodeURIComponent(actualKey)}`;
      };

      this.setObjectContentTypeHeader = (input, headers) => {
        if (headers['content-type'] != null) {
          return;
        }

        let mimeType = DEFAULT_CONTENT_TYPE$1;
        const key = getObjectInputKey(input);

        if (this.opts.autoRecognizeContentType) {
          mimeType = lookupMimeType(key) || mimeType;
        }

        if (mimeType) {
          headers['content-type'] = mimeType;
        }
      };

      this.getNormalDataFromError = getNormalDataFromError;
      this.opts = this.normalizeOpts(_opts);

      this.userAgent = this.getUserAgent();
      this.axiosInst = makeAxiosInst(this.opts.maxRetryCount);
    }

    normalizeOpts(_opts) {
      var _opts$enableCRC, _opts$enableCRC2;

      // 对字符串参数做 trim 操作
      const trimKeys = ['accessKeyId', 'accessKeySecret', 'stsToken', 'region', 'endpoint'];
      trimKeys.forEach(key => {
        const value = _opts[key];

        if (typeof value === 'string') {
          // maybe undefined
          _opts[key] = value.trim();
        }
      });
      const mustKeys = ['accessKeyId', 'accessKeySecret', 'region'];
      const mustKeysErrorStr = mustKeys.filter(key => !_opts[key]).join(', ');

      if (mustKeysErrorStr) {
        throw new TosClientError(`lack params: ${mustKeysErrorStr}.`);
      }

      const endpoint = _opts.endpoint || getEndpoint(_opts.region);

      if (!endpoint) {
        throw new TosClientError(`the value of param region is invalid, correct values are cn-beijing, cn-nantong etc.`);
      }

      if (endpoint.includes('s3')) {
        throw new TosClientError(`do not support s3 endpoint, please use tos endpoint.`);
      }

      const secure = _opts.secure == null ? true : !!_opts.secure;

      const _default = (v, defaultValue) => v == null ? defaultValue : v;

      const enableCRC = (_opts$enableCRC = _opts.enableCRC) != null ? _opts$enableCRC : false;

      if (enableCRC && 'browser' === 'browser') {
        throw new TosClientError('not support crc in browser environment');
      }

      return { ..._opts,
        endpoint,
        secure,
        enableVerifySSL: _default(_opts.enableVerifySSL, true),
        autoRecognizeContentType: _default(_opts.autoRecognizeContentType, true),
        requestTimeout: _default(_opts.requestTimeout, 120000),
        connectionTimeout: _default(_opts.connectionTimeout, 10000),
        maxConnections: _default(_opts.maxConnections, 1024),
        idleConnectionTime: _default(_opts.idleConnectionTime, 30000),
        maxRetryCount: _default(_opts.maxRetryCount, 3),
        enableCRC: (_opts$enableCRC2 = _opts.enableCRC) != null ? _opts$enableCRC2 : false,
        requestAdapter: getAdapter()
      };
    }

    getUserAgent() {
      // ve-tos-go-sdk/v2.0.0 (linux/amd64;go1.17.0)
      const language =  'browserjs' ;
      const sdkVersion = `ve-tos-${language}-sdk/v${version$1}`;

      {
        return sdkVersion;
      }
    }

    async fetch(method, path, query, headers, body, opts) {
      const handleResponse = (opts == null ? void 0 : opts.handleResponse) || (res => res.data);

      const needMd5 = (opts == null ? void 0 : opts.needMd5) || false;

      if (body && needMd5) {
        const md5String = hashMd5$1(JSON.stringify(body), 'base64');
        headers['content-md5'] = md5String;
      }

      const [endpoint, newPath] = (() => {
        if (opts != null && opts.subdomainBucket && this.opts.forcePathStyle) {
          return [this.opts.endpoint, `/${opts.subdomainBucket}${path}`];
        } // if isCustomDomain true, not add subdomainBucket


        if (opts != null && opts.subdomainBucket && !this.opts.isCustomDomain) {
          // endpoint is ip address
          if (/^(\d|:)/.test(this.opts.endpoint)) {
            return [this.opts.endpoint, `/${opts.subdomainBucket}${path}`];
          }

          return [`${opts == null ? void 0 : opts.subdomainBucket}.${this.opts.endpoint}`, path];
        }

        return [this.opts.endpoint, path];
      })();

      path = newPath;
      headers = encodeHeadersValue(headers);
      const signOpt = {
        // TODO: delete endpoints and buckets
        endpoints: undefined,
        bucket: '',
        method,
        headers: { ...headers
        },
        path,
        query: getSortedQueryString(query),
        host: endpoint
      };
      const signv4 = new ISigV4Credentials(this.opts.stsToken, this.opts.accessKeySecret, this.opts.accessKeyId);
      const sig = new SignersV4({
        algorithm: 'TOS4-HMAC-SHA256',
        region: this.opts.region,
        serviceName: 'tos',
        bucket: '',
        securityToken: this.opts.stsToken
      }, signv4);
      const signatureHeaders = sig.signatureHeader(signOpt);
      const reqHeaders = { ...headers
      };
      const reqOpts = {
        method,
        baseURL: `http${this.opts.secure ? 's' : ''}://${endpoint}`,
        url: path,
        params: query,
        headers: reqHeaders,
        data: body
      };
      signatureHeaders.forEach((value, key) => {
        reqOpts.headers[key] = value;
      });
      const normalizedProxy = normalizeProxy(this.opts.proxy);

      if (normalizedProxy != null && normalizedProxy.url && !this.opts.proxyHost) {
        // proxy for nodejs middleware server
        reqOpts.baseURL = normalizedProxy.url;

        if (normalizedProxy != null && normalizedProxy.needProxyParams) {
          reqOpts.params['x-proxy-tos-host'] = endpoint;
          delete reqHeaders['host'];
        }
      } else if (this.opts.proxyHost) {
        if (!this.opts.proxyPort) {
          throw new TosClientError('The `proxyPort` is required if `proxyHost` is truly.');
        } // proxy for general proxy server


        reqOpts.proxy = {
          host: this.opts.proxyHost,
          port: this.opts.proxyPort,
          protocol: 'http'
        };
      }

      reqHeaders['user-agent'] = this.userAgent;

      if (this.opts.requestTimeout > 0 && this.opts.requestTimeout !== Infinity) {
        reqOpts.timeout = this.opts.requestTimeout;
      }

      try {
        const logReqOpts = { ...reqOpts
        };
        delete logReqOpts.httpAgent;
        delete logReqOpts.httpsAgent;
        TOS('reqOpts: ', logReqOpts);
        const res = await this.axiosInst({ ...{
            maxBodyLength: Infinity,
            maxContentLength: Infinity,
            adapter: this.opts.requestAdapter
          },
          ...reqOpts,
          ...((opts == null ? void 0 : opts.axiosOpts) || {}),
          [retrySignatureNamespace]: {
            signOpt,
            sigInst: sig
          }
        });
        const data = handleResponse(res);
        return {
          data,
          statusCode: res.status,
          headers: res.headers,
          requestId: res.headers['x-tos-request-id'],
          id2: res.headers['x-tos-id-2']
        };
      } catch (err) {
        var _err$response, _err$response$headers;

        if (axios$1.isAxiosError(err) && (_err$response = err.response) != null && (_err$response$headers = _err$response.headers) != null && _err$response$headers['x-tos-request-id']) {
          // it's ServerError only if `RequestId` exists
          const response = err.response;
          TOS('TosServerError response: ', response);
          const err2 = new TosServerError(response);
          throw err2;
        } // it is neither ServerError nor ClientError, it's other error


        TOS('err: ', err);
        throw err;
      }
    }

    async fetchBucket(bucket, method, query, headers, body, opts) {
      const actualBucket = bucket || this.opts.bucket;

      if (!actualBucket) {
        throw new TosClientError('Must provide bucket param');
      }

      return this.fetch(method, '/', query, headers, body, { ...opts,
        subdomainBucket: actualBucket
      });
    }

    async _fetchObject(input, method, query, headers, body, opts) {
      const actualBucket = typeof input !== 'string' && input.bucket || this.opts.bucket;
      const actualKey = typeof input === 'string' ? input : input.key;

      if (!actualBucket) {
        throw new TosClientError('Must provide bucket param');
      }

      validateObjectName(actualKey);
      return this.fetch(method, `/${encodeURIComponent(actualKey)}`, query, headers, body, { ...opts,
        subdomainBucket: actualBucket
      });
    }

    getSignatureQuery(input) {
      const signv4 = new ISigV4Credentials(this.opts.stsToken, this.opts.accessKeySecret, this.opts.accessKeyId);
      const sig = new SignersV4({
        algorithm: 'TOS4-HMAC-SHA256',
        region: this.opts.endpoint,
        serviceName: 'tos',
        // SignV4 uses this.options.bucket, so set it here
        bucket: input.bucket,
        securityToken: this.opts.stsToken
      }, signv4);

      if ('policy' in input) {
        return sig.getSignaturePolicyQuery({
          policy: input.policy
        }, input.expires);
      } else {
        return sig.getSignatureQuery({
          method: input.method,
          path: input.path,
          endpoints: input.subdomain ? input.endpoint : undefined,
          host: input.endpoint,
          query: input.query
        }, input.expires);
      }
    }

    normalizeBucketInput(input) {
      return typeof input === 'string' ? {
        bucket: input
      } : input;
    }

    normalizeObjectInput(input) {
      return typeof input === 'string' ? {
        key: input
      } : input;
    }

  }

  function getAdapter() {

    if (typeof window !== 'undefined' && typeof window.location !== 'undefined') {
      // browser env
      return undefined;
    }

    switch (true) {
      case typeof wx !== 'undefined':
      case typeof swan !== 'undefined':
      case typeof dd !== 'undefined':
      case typeof my !== 'undefined':
        return mpAdapter;

      case typeof uni !== 'undefined':
        return axiosAdapterUniapp;

      default:
        return undefined;
    }
  }

  /**
   *
   * @deprecated use listObjectsType2 instead
   * @returns
   */


  async function listObjects(input = {}) {
    const {
      bucket,
      ...nextQuery
    } = input;
    const ret = await this.fetchBucket(input.bucket, 'GET', covertCamelCase2Kebab(nextQuery), {});
    const arrayProp = makeArrayProp(ret.data);
    arrayProp('CommonPrefixes');
    arrayProp('Contents');
    arrayProp('Versions');
    arrayProp('DeleteMarkers');
    return ret;
  }
  async function listObjectVersions(input = {}) {
    const {
      bucket,
      ...nextQuery
    } = input;
    const ret = await this.fetchBucket(input.bucket, 'GET', covertCamelCase2Kebab({
      versions: '',
      ...nextQuery
    }), {});
    const arrayProp = makeArrayProp(ret.data);
    arrayProp('CommonPrefixes');
    arrayProp('Versions');
    arrayProp('DeleteMarkers');
    return ret;
  }

  const DefaultListMaxKeys = 1000;
  async function listObjectsType2(input = {}) {
    const {
      listOnlyOnce = false
    } = input;
    let output;

    if (!input.maxKeys) {
      input.maxKeys = DefaultListMaxKeys;
    }

    if (listOnlyOnce) {
      output = await listObjectsType2Once.call(this, input);
    } else {
      const maxKeys = input.maxKeys;
      let params = { ...input,
        maxKeys
      };

      while (true) {
        const res = await listObjectsType2Once.call(this, params);

        if (output == null) {
          output = res;
        } else {
          output = { ...res,
            data: output.data
          };
          output.data.KeyCount += res.data.KeyCount;
          output.data.IsTruncated = res.data.IsTruncated;
          output.data.NextContinuationToken = res.data.NextContinuationToken;
          output.data.Contents = output.data.Contents.concat(res.data.Contents);
          output.data.CommonPrefixes = output.data.CommonPrefixes.concat(res.data.CommonPrefixes);
        }

        if (!res.data.IsTruncated || output.data.KeyCount >= maxKeys) {
          break;
        }

        params.continuationToken = res.data.NextContinuationToken;
        params.maxKeys = params.maxKeys - res.data.KeyCount;
      }
    }

    return output;
  }

  async function listObjectsType2Once(input) {
    const {
      bucket,
      ...nextQuery
    } = input;
    const ret = await this.fetchBucket(input.bucket, 'GET', {
      'list-type': 2,
      ...covertCamelCase2Kebab(nextQuery)
    }, {});
    const arrayProp = makeArrayProp(ret.data);
    arrayProp('CommonPrefixes');
    arrayProp('Contents');
    return ret;
  }

  /** @private unstable */

  class ShareLinkClient extends TOSBase {
    modifyAxiosInst() {
      const axiosInst = this.axiosInst;
      axiosInst.interceptors.request.use(config => {
        const headers = config.headers || {};
        delete headers['authorization'];
        headers['host'] = this.parsedPolicyUrlVal.host;
        config.baseURL = this.parsedPolicyUrlVal.origin;

        config.paramsSerializer = params => {
          const addQueryStr = paramsSerializer(params);
          return [this.parsedPolicyUrlVal.search, addQueryStr].filter(it => it.trim()).join('&');
        };

        return config;
      });
    }

    constructor(_opts) {
      super({ ..._opts,
        bucket: 'fake-bucket',
        region: 'fake-region',
        accessKeyId: 'fake-accessKeyId',
        accessKeySecret: 'fake-accessKeySecret',
        endpoint: 'fake-endpoint.com'
      });
      this.shareLinkClientOpts = void 0;
      this.parsedPolicyUrlVal = void 0;
      this.headObject = headObject;
      this.getObjectV2 = getObjectV2;
      this.listObjects = listObjects;
      this.listObjectsType2 = listObjectsType2;
      this.listObjectVersions = listObjectVersions;
      this.downloadFile = downloadFile;
      this.shareLinkClientOpts = _opts;
      this.parsedPolicyUrlVal = this.initParsedPolicyUrlVal();
      this.modifyAxiosInst();
    }

    initParsedPolicyUrlVal() {
      const reg = /(https?:\/\/(?:[^@]+@)?([^/?]+))[^?]*\?(.+)/;
      const matched = this.shareLinkClientOpts.policyUrl.match(reg);

      if (!matched) {
        throw new TosClientError('the `policyUrl` param is invalid');
      }

      return {
        origin: matched[1],
        host: matched[2],
        search: matched[3]
      };
    }

  }

  async function listBuckets(input = {}) {
    const headers = {};
    /**
     * empty string is invalid value
     */

    (input == null ? void 0 : input.projectName) && fillRequestHeaders({ ...input,
      headers
    }, ['projectName']);
    const res = await this.fetch('GET', '/', {}, headers);
    const arrayProp = makeArrayProp(res.data);
    arrayProp('Buckets');
    return res;
  }
  async function createBucket(input) {
    const actualBucket = input.bucket || this.opts.bucket; // these errors are only for creating bucket

    if (actualBucket) {
      if (actualBucket.length < 3 || actualBucket.length > 63) {
        throw new TosClientError('invalid bucket name, the length must be [3, 63]');
      }

      if (!/^([a-z]|-|\d)+$/.test(actualBucket)) {
        throw new TosClientError('invalid bucket name, the character set is illegal');
      }

      if (/^-/.test(actualBucket) || /-$/.test(actualBucket)) {
        throw new TosClientError(`invalid bucket name, the bucket name can be neither starting with '-' nor ending with '-'`);
      }
    }

    const headers = input.headers = normalizeHeadersKey(input.headers);
    fillRequestHeaders(input, ['acl', 'grantFullControl', 'grantRead', 'grantReadAcp', 'grantWrite', 'grantWriteAcp', 'storageClass', 'azRedundancy', 'bucketType']);
    /**
     * empty string is invalid value
     */

    (input == null ? void 0 : input.projectName) && fillRequestHeaders(input, ['projectName']);
    const res = await this.fetchBucket(input.bucket, 'PUT', {}, headers);
    return res;
  }
  async function deleteBucket(bucket) {
    return this.fetchBucket(bucket, 'DELETE', {}, {});
  }
  async function headBucket(bucket) {
    return this.fetchBucket(bucket, 'HEAD', {}, {}, undefined, {
      handleResponse: res => {
        return { ...res.headers,
          ProjectName: res.headers[TosHeader.HeaderProjectName]
        };
      }
    });
  }
  async function putBucketStorageClass(input) {
    const {
      bucket,
      storageClass
    } = input;
    return this.fetchBucket(bucket, 'PUT', {
      storageClass: ''
    }, {
      'x-tos-storage-class': storageClass
    });
  }

  async function putBucketAcl(input) {
    const headers = {};
    if (input.acl) headers['x-tos-acl'] = input.acl;
    const res = await this.fetchBucket(input.bucket, 'PUT', {
      acl: ''
    }, headers, input.aclBody, {
      needMd5: true
    });
    return res;
  }
  async function getBucketAcl(bucket) {
    const res = await this.fetchBucket(bucket, 'GET', {
      acl: ''
    }, {});
    const arrayProp = makeArrayProp(res.data);
    arrayProp('Grants');
    return res;
  }

  async function putObject(input) {
    return _putObject.call(this, input);
  }
  async function _putObject(input) {
    input = this.normalizeObjectInput(input);
    const headers = input.headers = normalizeHeadersKey(input.headers);
    fillRequestHeaders(input, ['contentLength', 'contentMD5', 'contentSHA256', 'cacheControl', 'contentDisposition', 'contentEncoding', 'contentLanguage', 'contentType', 'expires', 'acl', 'grantFullControl', 'grantRead', 'grantReadAcp', 'grantWrite', 'grantWriteAcp', 'ssecAlgorithm', 'ssecKey', 'ssecKeyMD5', 'serverSideEncryption', 'serverSideDataEncryption', 'meta', 'websiteRedirectLocation', 'storageClass', 'trafficLimit', 'callback', 'callbackVar', 'forbidOverwrite', 'ifMatch']);
    this.setObjectContentTypeHeader(input, headers);
    const totalSize = getSize(input.body, headers);
    const totalSizeValid = totalSize != null;

    if (!totalSizeValid && (input.dataTransferStatusChange || input.progress)) {
      console.warn(`Don't get totalSize of putObject's body, the \`dataTransferStatusChange\` and \`progress\` callback will not trigger. You can use \`putObjectFromFile\` instead`);
    }

    let consumedBytes = 0;
    const {
      dataTransferStatusChange,
      progress
    } = input;

    const triggerDataTransfer = (type, rwOnceBytes = 0) => {
      // request cancel will make rwOnceBytes < 0 in browser
      if (!totalSizeValid || rwOnceBytes < 0) {
        return;
      }

      if (!dataTransferStatusChange && !progress) {
        return;
      }

      consumedBytes += rwOnceBytes;
      dataTransferStatusChange == null ? void 0 : dataTransferStatusChange({
        type,
        rwOnceBytes,
        consumedBytes,
        totalBytes: totalSize
      });

      const progressValue = (() => {
        if (totalSize === 0) {
          if (type === exports.DataTransferType.Succeed) {
            return 1;
          }

          return 0;
        }

        return consumedBytes / totalSize;
      })();

      if (progressValue === 1) {
        if (type === exports.DataTransferType.Succeed) {
          progress == null ? void 0 : progress(progressValue);
        }
      } else {
        progress == null ? void 0 : progress(progressValue);
      }
    };

    const bodyConfig = await getNewBodyConfig({
      body: input.body,
      dataTransferCallback: n => triggerDataTransfer(exports.DataTransferType.Rw, n),
      makeRetryStream: input.makeRetryStream,
      enableCRC: this.opts.enableCRC,
      rateLimiter: input.rateLimiter
    });
    triggerDataTransfer(exports.DataTransferType.Started);

    const task = async () => {
      const res = await this._fetchObject(input, 'PUT', {}, headers, bodyConfig.body || '', {
        handleResponse: res => {
          var _input;

          const result = { ...res.headers
          };

          if ((_input = input) != null && _input.callback && res.data) {
            result.CallbackResult = `${JSON.stringify(res.data)}`;
          }

          return result;
        },
        axiosOpts: {
          [retryNamespace]: {
            beforeRetry: () => {
              consumedBytes = 0;
              bodyConfig.beforeRetry == null ? void 0 : bodyConfig.beforeRetry();
            },
            makeRetryStream: bodyConfig.makeRetryStream
          },
          onUploadProgress: event => {
            triggerDataTransfer(exports.DataTransferType.Rw, event.loaded - consumedBytes);
          }
        }
      });

      if (this.opts.enableCRC && bodyConfig.crc) {
        checkCRC64WithHeaders(bodyConfig.crc, res.headers);
      }

      return res;
    };

    const [err, res] = await safeAwait(task());

    if (err || !res) {
      triggerDataTransfer(exports.DataTransferType.Failed);
      throw err;
    }

    triggerDataTransfer(exports.DataTransferType.Succeed);
    return res;
  }
  async function putObjectFromFile(input) {
    const normalizedHeaders = normalizeHeadersKey(input.headers);

    {
      throw new TosClientError("putObjectFromFile doesn't support in browser environment");
    }
  }

  async function fetchObject(input) {
    const headers = input.headers = normalizeHeadersKey(input.headers);
    fillRequestHeaders(input, ['acl', 'grantFullControl', 'grantRead', 'grantReadAcp', 'grantWriteAcp', 'ssecAlgorithm', 'ssecKey', 'ssecKeyMD5', 'meta', 'storageClass']);
    const res = await this._fetchObject(input, 'POST', {
      fetch: ''
    }, headers, {
      URL: input.url,
      IgnoreSameKey: input.ignoreSameKey,
      ContentMD5: input.contentMD5
    }, {
      needMd5: true
    });
    return res;
  }
  async function putFetchTask(input) {
    const headers = input.headers = normalizeHeadersKey(input.headers);
    fillRequestHeaders(input, ['acl', 'grantFullControl', 'grantRead', 'grantReadAcp', 'grantWriteAcp', 'ssecAlgorithm', 'ssecKey', 'ssecKeyMD5', 'meta', 'storageClass']);
    const res = await this._fetchObject(input, 'POST', {
      fetchTask: ''
    }, headers, {
      URL: input.url,
      IgnoreSameKey: input.ignoreSameKey,
      ContentMD5: input.contentMD5,
      Object: input.key
    }, {
      needMd5: true
    });
    return res;
  }

  function getPreSignedUrl(input) {
    validateObjectName(input);
    const normalizedInput = typeof input === 'string' ? {
      key: input
    } : input;
    const endpoint = normalizedInput.alternativeEndpoint || this.opts.endpoint;
    const subdomain = normalizedInput.alternativeEndpoint || normalizedInput.isCustomDomain ? false : true;
    const bucket = normalizedInput.bucket || this.opts.bucket || '';

    if (subdomain && !bucket) {
      throw new TosClientError('Must provide bucket param');
    }

    const [newHost, newPath, signingPath] = (() => {
      const encodedKey = encodeURIComponent(normalizedInput.key);
      const objectKeyPath = normalizedInput.key.split('/').map(it => encodeURIComponent(it)).join('/');

      if (subdomain) {
        return [`${bucket}.${endpoint}`, `/${objectKeyPath}`, `/${encodedKey}`];
      }

      return [endpoint, `/${objectKeyPath}`, `/${encodedKey}`];
    })();

    const nextQuery = normalizedInput.query || {};

    const setOneQuery = (k, v) => {
      if (nextQuery[k] == null && v != null) {
        nextQuery[k] = v;
      }
    };

    const response = normalizedInput.response || {};
    Object.keys(response).forEach(_key => {
      const key = _key;
      const kebabKey = covertCamelCase2Kebab(key);
      setOneQuery(`response-${kebabKey}`, response[key]);
    });

    if (normalizedInput.versionId) {
      setOneQuery('versionId', normalizedInput.versionId);
    }

    const query = this.getSignatureQuery({
      bucket,
      method: normalizedInput.method || 'GET',
      path: signingPath,
      endpoint,
      subdomain,
      expires: normalizedInput.expires || 1800,
      query: nextQuery
    });
    const normalizedProxy = normalizeProxy(this.opts.proxy);
    let baseURL = `http${this.opts.secure ? 's' : ''}://${newHost}`;

    if (normalizedProxy != null && normalizedProxy.url) {
      // if `baseURL` ends with '/'，we filter it.
      // because `newPath` starts with '/'
      baseURL = normalizedProxy.url.replace(/\/+$/g, '');

      if (normalizedProxy != null && normalizedProxy.needProxyParams) {
        query['x-proxy-tos-host'] = newHost;
      }
    }

    const queryStr = Object.keys(query).map(key => {
      return `${encodeURIComponent(key)}=${encodeURIComponent(query[key])}`;
    }).join('&');
    return `${baseURL}${newPath}?${queryStr}`;
  }

  async function deleteObject(input) {
    const normalizedInput = typeof input === 'string' ? {
      key: input
    } : input;
    const query = {};

    if (normalizedInput.versionId) {
      query.versionId = normalizedInput.versionId;
    }

    if (normalizedInput.skipTrash) {
      query.skipTrash = normalizedInput.skipTrash;
    }

    if (normalizedInput.recursive) {
      query.recursive = normalizedInput.recursive;
    }

    const res = await this._fetchObject(input, 'DELETE', query, {}, {}, {
      handleResponse: res => res.headers
    });
    return res;
  }

  async function renameObject(input) {
    input.headers = input.headers || {};
    fillRequestHeaders(input, ['recursiveMkdir', 'forbidOverwrite']);
    return this._fetchObject(input, 'PUT', {
      rename: '',
      name: input.newKey
    }, input.headers, '');
  }

  async function deleteMultiObjects(input) {
    const body = {
      Quiet: input.quiet,
      Objects: input.objects.map(it => ({
        Key: it.key,
        VersionId: it.versionId
      }))
    };
    const query = {
      delete: ''
    };

    if (input.skipTrash) {
      query.skipTrash = input.skipTrash;
    }

    if (input.recursive) {
      query.recursive = input.recursive;
    }

    const res = await this.fetchBucket(input.bucket, 'POST', query, {}, body);
    const arrayProp = makeArrayProp(res.data);
    arrayProp('Deleted');
    arrayProp('Error');
    return res;
  }

  async function getObjectAcl(input) {
    const normalizedInput = typeof input === 'string' ? {
      key: input
    } : input;
    const query = {
      acl: ''
    };

    if (normalizedInput.versionId) {
      query.versionId = normalizedInput.versionId;
    }

    const res = await this._fetchObject(input, 'GET', query, {});
    const arrayProp = makeArrayProp(res.data);
    arrayProp('Grants');
    return res;
  }
  async function putObjectAcl(input) {
    const headers = input.headers = normalizeHeadersKey(input.headers);
    const query = {
      acl: ''
    };

    if (input.versionId) {
      query.versionId = input.versionId;
    }

    fillRequestHeaders(input, ['acl']);
    return this._fetchObject(input, 'PUT', query, headers, input.aclBody);
  }

  async function abortMultipartUpload(input) {
    return this._fetchObject(input, 'DELETE', {
      uploadId: input.uploadId
    }, {});
  }

  async function listMultipartUploads(input = {}) {
    const {
      bucket,
      ...nextQuery
    } = input;
    const ret = await this.fetchBucket(input.bucket, 'GET', {
      uploads: '',
      ...covertCamelCase2Kebab(nextQuery)
    }, {});
    const arrayProp = makeArrayProp(ret.data);
    arrayProp('Uploads');
    arrayProp('CommonPrefixes');
    return ret;
  }

  async function appendObject(input) {
    const normalizedInput = input = this.normalizeObjectInput(input);
    const headers = input.headers = normalizeHeadersKey(input.headers);
    fillRequestHeaders(input, ['contentLength', 'cacheControl', 'contentDisposition', 'contentEncoding', 'contentLanguage', 'contentType', 'expires', 'acl', 'grantFullControl', 'grantRead', 'grantReadAcp', 'grantWriteAcp', 'meta', 'websiteRedirectLocation', 'storageClass', 'trafficLimit']);
    this.setObjectContentTypeHeader(input, headers);
    const totalSize = getSize(input.body, headers);
    const totalSizeValid = totalSize != null;

    if (!totalSizeValid) {
      throw new TosClientError(`appendObject needs to know the content length in advance`);
    }

    headers['content-length'] = headers['content-length'] || `${totalSize}`;

    if (this.opts.enableCRC && input.offset !== 0 && !input.preHashCrc64ecma) {
      throw new TosClientError('must provide preHashCrc64ecma if enableCRC is true and offset is non-zero');
    }

    let consumedBytes = 0;
    const {
      dataTransferStatusChange,
      progress
    } = input;

    const triggerDataTransfer = (type, rwOnceBytes = 0) => {
      // request cancel will make rwOnceBytes < 0 in browser
      if (!totalSizeValid || rwOnceBytes < 0) {
        return;
      }

      if (!dataTransferStatusChange && !progress) {
        return;
      }

      consumedBytes += rwOnceBytes;
      dataTransferStatusChange == null ? void 0 : dataTransferStatusChange({
        type,
        rwOnceBytes,
        consumedBytes,
        totalBytes: totalSize
      });

      const progressValue = (() => {
        if (totalSize === 0) {
          if (type === exports.DataTransferType.Succeed) {
            return 1;
          }

          return 0;
        }

        return consumedBytes / totalSize;
      })();

      if (progressValue === 1) {
        if (type === exports.DataTransferType.Succeed) {
          progress == null ? void 0 : progress(progressValue);
        }
      } else {
        progress == null ? void 0 : progress(progressValue);
      }
    };

    const bodyConfig = await getNewBodyConfig({
      body: input.body,
      dataTransferCallback: n => triggerDataTransfer(exports.DataTransferType.Rw, n),
      makeRetryStream: undefined,
      enableCRC: this.opts.enableCRC,
      rateLimiter: input.rateLimiter
    });
    triggerDataTransfer(exports.DataTransferType.Started);

    const task = async () => {
      const res = await this._fetchObject(input, 'POST', {
        append: '',
        offset: normalizedInput.offset
      }, headers, bodyConfig.body || '', {
        handleResponse: res => ({ ...res.headers,
          nextAppendOffset: +res.headers['x-tos-next-append-offset'],
          hashCrc64ecma: res.headers['x-tos-hash-crc64ecma']
        }),
        axiosOpts: {
          [retryNamespace]: {
            beforeRetry: () => {
              consumedBytes = 0;
              bodyConfig.beforeRetry == null ? void 0 : bodyConfig.beforeRetry();
            },
            makeRetryStream: bodyConfig.makeRetryStream
          },
          onUploadProgress: event => {
            triggerDataTransfer(exports.DataTransferType.Rw, event.loaded - consumedBytes);
          }
        }
      });

      if (this.opts.enableCRC && bodyConfig.crc) {
        const appendObjectCrc = combineCrc64(normalizedInput.preHashCrc64ecma || '0', bodyConfig.crc.getCrc64(), totalSize);
        checkCRC64WithHeaders(appendObjectCrc, res.headers);
      }

      return res;
    };

    const [err, res] = await safeAwait(task());

    if (err || !res) {
      triggerDataTransfer(exports.DataTransferType.Failed);
      throw err;
    }

    triggerDataTransfer(exports.DataTransferType.Succeed);
    return res;
  }

  async function setObjectMeta(input) {
    const normalizedInput = typeof input === 'string' ? {
      key: input
    } : input;
    const headers = normalizedInput.headers = normalizeHeadersKey(normalizedInput.headers);
    fillRequestHeaders(normalizedInput, ['cacheControl', 'contentDisposition', 'contentEncoding', 'contentLanguage', 'contentType', 'expires', 'meta']);
    const query = {
      metadata: ''
    };

    if (normalizedInput.versionId) {
      query.versionId = normalizedInput.versionId;
    }

    return this._fetchObject(input, 'POST', query, headers);
  }

  async function calculatePostSignature(input) {
    validateObjectName(input);
    input = this.normalizeObjectInput(input);
    const {
      expiresIn = 3600,
      key
    } = input;
    const bucket = input.bucket || this.opts.bucket;
    const fields = { ...input.fields
    };
    const conditions = [...(input.conditions || [])];

    if (!bucket) {
      throw new TosClientError('Must provide bucket param');
    }

    const accessKeySecret = this.opts.accessKeySecret;
    const date = new Date();
    const expirationDateStr = getDateTimeStr({
      date: new Date(date.valueOf() + expiresIn * 1000),
      type: 'ISO'
    });
    const dateStr = getDateTimeStr();
    const date8Str = dateStr.substring(0, 8);
    const service = 'tos';
    const requestStr = 'request';
    const kDate = hmacSha256$2(accessKeySecret, date8Str);
    const kRegion = hmacSha256$2(kDate, this.opts.region);
    const kService = hmacSha256$2(kRegion, service);
    const signingKey = hmacSha256$2(kService, requestStr);
    const credential = [this.opts.accessKeyId, date8Str, this.opts.region, service, requestStr].join('/');
    const addedInForm = {
      key,
      'x-tos-algorithm': 'TOS4-HMAC-SHA256',
      'x-tos-date': dateStr,
      'x-tos-credential': credential
    };

    if (this.opts.stsToken) {
      addedInForm['x-tos-security-token'] = this.opts.stsToken;
    }

    conditions.push({
      bucket
    });
    Object.entries(addedInForm).forEach(([key, value]) => {
      fields[key] = value;
    });
    Object.entries(fields).forEach(([key, value]) => {
      conditions.push({
        [key]: `${value}`
      });
    });
    const policy = {
      expiration: expirationDateStr,
      conditions
    };
    const policyStr = JSON.stringify(policy);
    const policyBase64 = stringify$2(parse$3(policyStr, 'utf-8'), 'base64');
    const signature = hmacSha256$2(signingKey, policyBase64, 'hex');
    fields.policy = policyBase64;
    fields['x-tos-signature'] = signature;
    return fields;
  }
  /**
   *
   * Z for 20130728T000000Z
   * ISO for 2007-12-01T12:00:00.000Z
   * @param opt
   * @returns
   */

  function getDateTimeStr(opt) {
    const {
      date = new Date(),
      type = 'Z'
    } = opt || {};

    if (type === 'ISO') {
      return date.toISOString();
    }

    const dateTime = date.toISOString().replace(/\..+/, '').replace(/-/g, '').replace(/:/g, '') + 'Z';
    return dateTime;
  }

  const defaultEmptyMethodMap = {
    getBucketCustomDomain: true,
    getBucketIntelligenttiering: true,
    getBucketInventory: true,
    listBucketInventory: true,
    getBucketMirrorBack: true,
    getBucketNotification: true,
    getBucketPolicy: true,
    getBucketRealTimeLog: true,
    getBucketReplication: true,
    getBucketTagging: true,
    getBucketWebsite: true
  };
  function handleEmptyServerError(err, opts) {
    const {
      enableCatchEmptyServerError,
      methodKey,
      defaultResponse
    } = opts;

    if (err instanceof TosServerError) {
      if (enableCatchEmptyServerError) {
        if (err.statusCode === 404) {
          return getNormalDataFromError(defaultResponse, err);
        }
      } // 在本次更改前已经有一些接口对404做了catch处理，在不显式声明enableCatchEmptyServerError的情况下，保持原样，不做break change
      else if (enableCatchEmptyServerError === undefined) {
        if (err.statusCode === 404 && defaultEmptyMethodMap[methodKey]) {
          return getNormalDataFromError(defaultResponse, err);
        }
      }
    }

    throw err;
  }

  async function putBucketPolicy(input) {
    if ((this.opts.enableOptimizeMethodBehavior || this.opts.enableOptimizeMethodBehavior === undefined) && !input.policy.Statement.length) {
      return deleteBucketPolicy.call(this, input.bucket);
    }

    const res = await this.fetchBucket(input.bucket, 'PUT', {
      policy: ''
    }, {}, input.policy, {
      needMd5: true
    });
    return res;
  }
  async function getBucketPolicy(bucket) {
    try {
      const res = await this.fetchBucket(bucket, 'GET', {
        policy: ''
      }, {});
      res.data.Statement.forEach(it => {
        const arrayProp = makeArrayProp(it);
        Object.keys(it.Condition || {}).forEach(key => {
          Object.keys(it.Condition[key]).forEach(key2 => {
            arrayProp(`Condition["${key}"]["${key2}"]`);
          });
        });
      });
      return res;
    } catch (error) {
      return handleEmptyServerError(error, {
        enableCatchEmptyServerError: this.opts.enableOptimizeMethodBehavior,
        methodKey: 'getBucketPolicy',
        defaultResponse: {
          Statement: [],
          Version: '2012-10-17'
        }
      });
    }
  }
  async function deleteBucketPolicy(bucket) {
    return this.fetchBucket(bucket, 'DELETE', {
      policy: ''
    }, {});
  }

  async function getBucketVersioning(bucket) {
    return this.fetchBucket(bucket, 'GET', {
      versioning: ''
    }, {});
  }
  async function putBucketVersioning(input) {
    return this.fetchBucket(input.bucket, 'PUT', {
      versioning: ''
    }, {}, {
      Status: input.status
    });
  }

  function preSignedPolicyURL(input) {
    const normalizedInput = normalizeInput.call(this, input);
    validateConditions(input.conditions);
    const endpoint = input.alternativeEndpoint || (input.isCustomDomain ? this.opts.endpoint : `${normalizedInput.bucket}.${this.opts.endpoint}`);
    const baseURL = `http${this.opts.secure ? 's' : ''}://${endpoint}`;
    const query = this.getSignatureQuery({
      bucket: normalizedInput.bucket,
      expires: normalizedInput.expires,
      policy: {
        conditions: normalizedInput.conditions
      }
    });
    const queryStr = obj2QueryStr(query);

    const getSignedURLForList = additionalQuery => {
      const str2 = obj2QueryStr(additionalQuery);
      const q = [queryStr, str2].filter(Boolean).join('&');
      return `${baseURL}?${q}`;
    };

    const getSignedURLForGetOrHead = (key, additionalQuery) => {
      const str2 = obj2QueryStr(additionalQuery);
      const q = [queryStr, str2].filter(Boolean).join('&'); // keep   '/'

      const keyPath = key.split('/').map(it => encodeURIComponent(it)).join('/');
      return `${baseURL}/${keyPath}?${q}`;
    };

    return {
      getSignedURLForList,
      getSignedURLForGetOrHead,
      signedQuery: queryStr
    };
  }

  function normalizeInput(input) {
    const actualBucket = input.bucket || this.opts.bucket;
    const defaultExpires = 3600;

    if (!actualBucket) {
      throw new TosClientError('Must provide bucket param');
    }

    validateConditions(input.conditions);
    const normalizedConditions = input.conditions.map(it => [it.operator || 'eq', '$key', it.value]);
    normalizedConditions.push(['eq', '$bucket', actualBucket]);
    return {
      bucket: actualBucket,
      expires: input.expires || defaultExpires,
      conditions: normalizedConditions
    };
  }

  function validateConditions(conditions) {
    if (conditions.length < 1) {
      throw new TosClientError('The `conditions` field of `PreSignedPolicyURLInput` must has one item at least');
    }

    for (const it of conditions) {
      if (it.key !== 'key') {
        throw new TosClientError("The `key` field of `PolicySignatureCondition` must be `'key'`");
      }

      if (it.operator && it.operator !== 'eq' && it.operator !== 'starts-with') {
        throw new TosClientError("The `operator` field of `PolicySignatureCondition` must be `'eq'` or `'starts-with'`");
      }
    }
  }

  async function getBucketLocation(input) {
    const {
      bucket
    } = input;
    return this.fetchBucket(bucket, 'GET', {
      location: ''
    }, {});
  }

  async function getBucketCORS(input) {
    try {
      const {
        bucket
      } = input;
      return await this.fetchBucket(bucket, 'GET', {
        cors: ''
      }, {});
    } catch (error) {
      return handleEmptyServerError(error, {
        defaultResponse: {
          CORSRules: []
        },
        enableCatchEmptyServerError: this.opts.enableOptimizeMethodBehavior,
        methodKey: 'getBucketCORS'
      });
    }
  }
  async function putBucketCORS(input) {
    const {
      bucket,
      CORSRules
    } = input;

    if (this.opts.enableOptimizeMethodBehavior && !CORSRules.length) {
      return deleteBucketCORS.call(this, {
        bucket
      });
    }

    return this.fetchBucket(bucket, 'PUT', {
      cors: ''
    }, {}, {
      CORSRules
    });
  }
  async function deleteBucketCORS(input) {
    const {
      bucket
    } = input;
    return this.fetchBucket(bucket, 'DELETE', {
      cors: ''
    }, {});
  }

  async function putBucketLifecycle(input) {
    const {
      bucket,
      rules
    } = input;

    if (this.opts.enableOptimizeMethodBehavior && !rules.length) {
      return deleteBucketLifecycle.call(this, {
        bucket
      });
    }

    const headers = {};
    fillRequestHeaders({ ...input,
      headers
    }, ['allowSameActionOverlap']);
    return this.fetchBucket(bucket, 'PUT', {
      lifecycle: ''
    }, headers, {
      Rules: rules
    });
  }
  async function getBucketLifecycle(input) {
    try {
      const {
        bucket
      } = input;
      return await this.fetchBucket(bucket, 'GET', {
        lifecycle: ''
      }, {}, {}, {
        handleResponse: res => {
          return {
            AllowSameActionOverlap: res.headers['x-tos-allow-same-action-overlap'],
            Rules: res.data.Rules
          };
        }
      });
    } catch (error) {
      return handleEmptyServerError(error, {
        enableCatchEmptyServerError: this.opts.enableOptimizeMethodBehavior,
        methodKey: 'getBucketLifecycle',
        defaultResponse: {
          Rules: []
        }
      });
    }
  }
  async function deleteBucketLifecycle(input) {
    const {
      bucket
    } = input;
    return this.fetchBucket(bucket, 'DELETE', {
      lifecycle: ''
    }, {});
  }

  async function putBucketEncryption(input) {
    const {
      bucket,
      rule
    } = input;
    return this.fetchBucket(bucket, 'PUT', {
      encryption: ''
    }, {
      'Content-MD5': hashMd5(JSON.stringify({
        Rule: rule
      }), 'base64')
    }, {
      Rule: rule
    });
  }
  async function getBucketEncryption(input) {
    const {
      bucket
    } = input;
    return this.fetchBucket(bucket, 'GET', {
      encryption: ''
    }, {});
  }
  async function deleteBucketEncryption(input) {
    const {
      bucket
    } = input;
    return this.fetchBucket(bucket, 'DELETE', {
      encryption: ''
    }, {});
  }

  const CommonQueryKey = 'mirror';
  async function putBucketMirrorBack(input) {
    const {
      bucket,
      rules
    } = input;

    if (this.opts.enableOptimizeMethodBehavior && !rules.length) {
      return deleteBucketMirrorBack.call(this, {
        bucket
      });
    }

    return this.fetchBucket(bucket, 'PUT', {
      [CommonQueryKey]: ''
    }, {}, {
      Rules: rules
    });
  }
  async function getBucketMirrorBack(input) {
    const {
      bucket
    } = input;

    try {
      return await this.fetchBucket(bucket, 'GET', {
        [CommonQueryKey]: ''
      }, {});
    } catch (error) {
      return handleEmptyServerError(error, {
        enableCatchEmptyServerError: this.opts.enableOptimizeMethodBehavior,
        methodKey: 'getBucketMirrorBack',
        defaultResponse: {
          Rules: []
        }
      });
    }
  }
  async function deleteBucketMirrorBack(input) {
    const {
      bucket
    } = input;
    return this.fetchBucket(bucket, 'DELETE', {
      [CommonQueryKey]: ''
    }, {});
  }

  const CommonQueryKey$1 = 'tagging';
  async function putObjectTagging(input) {
    const {
      tagSet,
      versionId
    } = input;
    const headers = normalizeHeadersKey({
      versionId
    });
    return this._fetchObject(input, 'PUT', {
      [CommonQueryKey$1]: '',
      ...headers
    }, {}, {
      TagSet: tagSet
    });
  }
  async function getObjectTagging(input) {
    const {
      versionId
    } = input;
    const headers = normalizeHeadersKey({
      versionId
    });
    const res = await this._fetchObject(input, 'GET', {
      [CommonQueryKey$1]: '',
      ...headers
    }, {});
    makeArrayProp(res.data.TagSet)('Tags');
    return res;
  }
  async function deleteObjectTagging(input) {
    const {
      versionId
    } = input;
    const headers = normalizeHeadersKey({
      versionId
    });
    return this._fetchObject(input, 'DELETE', {
      [CommonQueryKey$1]: '',
      ...headers
    }, {});
  }

  const CommonQueryKey$2 = 'replication';
  async function putBucketReplication(input) {
    const {
      bucket,
      rules,
      role
    } = input;

    if (this.opts.enableOptimizeMethodBehavior && !rules.length) {
      return deleteBucketReplication.call(this, {
        bucket
      });
    }

    return this.fetchBucket(bucket, 'PUT', {
      [CommonQueryKey$2]: ''
    }, {}, {
      Role: role,
      Rules: rules
    });
  }
  async function getBucketReplication(input) {
    const {
      bucket,
      progress,
      ruleId
    } = input;
    const query = {
      [CommonQueryKey$2]: '',
      progress: progress || ''
    };

    if (ruleId != null) {
      query['rule-id'] = `${ruleId}`;
    }

    try {
      return await this.fetchBucket(bucket, 'GET', query, {});
    } catch (err) {
      return handleEmptyServerError(err, {
        enableCatchEmptyServerError: this.opts.enableOptimizeMethodBehavior,
        methodKey: 'getBucketReplication',
        defaultResponse: {
          Rules: [],
          Role: ''
        }
      });
    }
  }
  async function deleteBucketReplication(input) {
    const {
      bucket
    } = input;
    return this.fetchBucket(bucket, 'DELETE', {
      [CommonQueryKey$2]: ''
    }, {});
  }

  const CommonQueryKey$3 = 'website';
  async function putBucketWebsite(input) {
    const {
      bucket,
      ...otherProps
    } = input;
    const body = convertNormalCamelCase2Upper(otherProps);
    return this.fetchBucket(bucket, 'PUT', {
      [CommonQueryKey$3]: ''
    }, {}, { ...body
    });
  }
  async function getBucketWebsite(input) {
    const {
      bucket
    } = input;

    try {
      return this.fetchBucket(bucket, 'GET', {
        [CommonQueryKey$3]: ''
      }, {});
    } catch (error) {
      return handleEmptyServerError(error, {
        enableCatchEmptyServerError: this.opts.enableOptimizeMethodBehavior,
        methodKey: 'getBucketWebsite',
        defaultResponse: {
          RoutingRules: []
        }
      });
    }
  }
  async function deleteBucketWebsite(input) {
    const {
      bucket
    } = input;
    return this.fetchBucket(bucket, 'DELETE', {
      [CommonQueryKey$3]: ''
    }, {});
  }

  const CommonQueryKey$4 = 'notification';
  /**
   * @deprecated use PutBucketNotificationType2 instead
   */

  async function putBucketNotification(input) {
    const {
      bucket,
      ...otherProps
    } = input;
    const body = convertNormalCamelCase2Upper(otherProps);
    return this.fetchBucket(bucket, 'PUT', {
      [CommonQueryKey$4]: ''
    }, {}, { ...body
    });
  }
  /**
   * @deprecated use GetBucketNotificationType2 instead
   */

  async function getBucketNotification(input) {
    const {
      bucket
    } = input;

    try {
      return await this.fetchBucket(bucket, 'GET', {
        [CommonQueryKey$4]: ''
      }, {});
    } catch (error) {
      return handleEmptyServerError(error, {
        enableCatchEmptyServerError: this.opts.enableOptimizeMethodBehavior,
        methodKey: 'getBucketNotification',
        defaultResponse: {
          CloudFunctionConfigurations: [],
          RocketMQConfigurations: []
        }
      });
    }
  }

  const CommonQueryKey$5 = 'customdomain';
  async function putBucketCustomDomain(input) {
    const {
      bucket,
      ...otherProps
    } = input;
    const body = convertNormalCamelCase2Upper(otherProps);
    return this.fetchBucket(bucket, 'PUT', {
      [CommonQueryKey$5]: ''
    }, {}, { ...body
    });
  }
  async function getBucketCustomDomain(input) {
    try {
      const {
        bucket
      } = input;
      return await this.fetchBucket(bucket, 'GET', {
        [CommonQueryKey$5]: ''
      }, {});
    } catch (error) {
      return handleEmptyServerError(error, {
        defaultResponse: {
          CustomDomainRules: []
        },
        enableCatchEmptyServerError: this.opts.enableOptimizeMethodBehavior,
        methodKey: 'getBucketCustomDomain'
      });
    }
  }
  async function deleteBucketCustomDomain(input) {
    const {
      bucket,
      customDomain
    } = input;
    return this.fetchBucket(bucket, 'DELETE', {
      customdomain: customDomain
    }, {});
  }

  const CommonQueryKey$6 = 'realtimeLog';
  async function putBucketRealTimeLog(input) {
    const {
      bucket,
      ...otherProps
    } = input;
    const body = convertNormalCamelCase2Upper(otherProps);
    return this.fetchBucket(bucket, 'PUT', {
      [CommonQueryKey$6]: ''
    }, {}, { ...body
    });
  }
  async function getBucketRealTimeLog(input) {
    const {
      bucket
    } = input;

    try {
      return await this.fetchBucket(bucket, 'GET', {
        [CommonQueryKey$6]: ''
      }, {});
    } catch (error) {
      return handleEmptyServerError(error, {
        enableCatchEmptyServerError: this.opts.enableOptimizeMethodBehavior,
        methodKey: 'getBucketRealTimeLog',
        defaultResponse: {}
      });
    }
  }
  async function deleteBucketRealTimeLog(input) {
    const {
      bucket
    } = input;
    return this.fetchBucket(bucket, 'DELETE', {
      [CommonQueryKey$6]: ''
    }, {});
  }

  /**
   * 清单文件导出周期
   */

  var ScheduleFrequency;

  (function (ScheduleFrequency) {
    /** 按天 */
    ScheduleFrequency["Daily"] = "Daily";
    /** 按周 */

    ScheduleFrequency["Weekly"] = "Weekly";
  })(ScheduleFrequency || (ScheduleFrequency = {}));
  /**
   * 清单包含Object版本信息值
   */


  var IncludedObjectVersions;

  (function (IncludedObjectVersions) {
    /** 全部 */
    IncludedObjectVersions["All"] = "All";
    /** 当前版本 */

    IncludedObjectVersions["Current"] = "Current";
  })(IncludedObjectVersions || (IncludedObjectVersions = {}));
  /**
   * 清单配置项
   */


  var InventoryOptionalFields;

  (function (InventoryOptionalFields) {
    /** Object的大小 */
    InventoryOptionalFields["Size"] = "Size";
    /** Object的最后修改时间 */

    InventoryOptionalFields["LastModifiedDat"] = "LastModifiedDate";
    /** 标识Object的内容 */

    InventoryOptionalFields["ETag"] = "ETag";
    /** Object的存储类型 */

    InventoryOptionalFields["StorageClass"] = "StorageClass";
    /** 是否为通过分片上传的Object */

    InventoryOptionalFields["IsMultipartUploaded"] = "IsMultipartUploaded";
    /** Object是否加密 */

    InventoryOptionalFields["EncryptionStatus"] = "EncryptionStatus";
    InventoryOptionalFields["CRC64"] = "CRC64";
    /** crr复制状态 */

    InventoryOptionalFields["ReplicationStatus"] = "ReplicationStatus";
  })(InventoryOptionalFields || (InventoryOptionalFields = {}));
  /**
   * 获取桶清单详情信息
   */


  async function getBucketInventory(req) {
    try {
      const res = await this.fetchBucket(req.bucket, 'GET', {
        inventory: '',
        id: req.id
      }, {});
      return res;
    } catch (error) {
      return handleEmptyServerError(error, {
        enableCatchEmptyServerError: this.opts.enableOptimizeMethodBehavior,
        methodKey: 'getBucketInventory',
        defaultResponse: undefined
      });
    }
  }
  /**
   * 分页获取桶清单信息
   */

  async function listBucketInventory(req) {
    const params = {
      inventory: '',
      ...(req.continuationToken ? {
        'continuation-token': req.continuationToken
      } : null)
    };

    try {
      const res = await this.fetchBucket(req.bucket, 'GET', params, {});
      return res;
    } catch (error) {
      return handleEmptyServerError(error, {
        enableCatchEmptyServerError: this.opts.enableOptimizeMethodBehavior,
        methodKey: 'listBucketInventory',
        defaultResponse: {
          InventoryConfigurations: []
        }
      });
    }
  }
  /**
   * 删除桶清单
   */

  async function deleteBucketInventory(req) {
    return this.fetchBucket(req.bucket, 'DELETE', {
      inventory: '',
      id: req.id
    }, {});
  }
  /**
   * 更新桶清单
   */

  function putBucketInventory(req) {
    return this.fetchBucket(req.bucket, 'PUT', {
      inventory: '',
      id: req.inventoryConfiguration.Id
    }, {}, req.inventoryConfiguration);
  }

  /**
   *
   * @private unstable method
   * @description 创建批量任务
   * @param params
   * @returns
   */

  async function createJob(params) {
    const {
      accountId,
      ...reset
    } = params;
    const data = convertNormalCamelCase2Upper(reset);
    const res = await this.fetch('POST', '/jobs', {}, {
      'x-tos-account-id': accountId
    }, { ...data
    });
    return res;
  }
  /**
   * @private unstable method
   * @description  获取批量任务列表
   * @param params
   * @returns
   */

  async function listJobs(params) {
    const {
      accountId,
      maxResults = 1000,
      ...others
    } = params;
    const res = await this.fetch('GET', '/jobs', {
      maxResults,
      ...others
    }, {
      'x-tos-account-id': accountId
    }, {}, {
      axiosOpts: {
        paramsSerializer
      }
    });
    return res;
  }
  /**
   *
   * @private unstable method
   * @description 更新批量任务优先级
   * @param params
   * @returns
   */

  async function updateJobPriority(params) {
    const {
      accountId,
      jobId: JobId,
      priority
    } = params;
    const res = await this.fetch('POST', `/jobs/${JobId}/priority`, {
      priority
    }, {
      'x-tos-account-id': accountId
    }, {}, {
      needMd5: true
    });
    return res;
  }
  /**
   *
   * @private unstable method
   * @description 更新批量任务优先级
   * @param params
   * @returns
   */

  async function updateJobStatus(params) {
    const {
      accountId,
      jobId: JobId,
      requestedJobStatus,
      statusUpdateReason
    } = params;
    const res = await this.fetch('POST', `/jobs/${JobId}/status`, {
      requestedJobStatus,
      statusUpdateReason
    }, {
      'x-tos-account-id': accountId
    }, {}, {
      needMd5: true
    });
    return res;
  }
  /**
   *
   * @private unstable method
   * @description 删除批量任务
   * @param params
   * @returns
   */

  async function deleteJob(params) {
    const {
      accountId,
      JobId
    } = params;
    const res = await this.fetch('DELETE', `/jobs/${JobId}`, {}, {
      'x-tos-account-id': accountId
    }, {});
    return res;
  }
  /**
   *
   * @private unstable method
   * @description 获取批量任务详情
   * @param params
   * @returns
   */

  async function describeJob(params) {
    const {
      accountId,
      JobId
    } = params;
    const res = await this.fetch('GET', `/jobs/${JobId}`, {}, {
      'x-tos-account-id': accountId
    }, {});
    return res;
  }

  /**
   * @private unstable method
   */

  async function putBucketTagging(input) {
    const res = await this.fetchBucket(input.bucket, 'PUT', {
      tagging: ''
    }, {}, input.tagging, {
      needMd5: true
    });
    return res;
  }
  /**
   * @private unstable method
   */

  async function getBucketTagging({
    bucket
  }) {
    try {
      const res = await this.fetchBucket(bucket, 'GET', {
        tagging: ''
      }, {});
      return res;
    } catch (error) {
      return handleEmptyServerError(error, {
        enableCatchEmptyServerError: this.opts.enableOptimizeMethodBehavior,
        methodKey: 'getBucketTagging',
        defaultResponse: {
          TagSet: {
            Tags: []
          }
        }
      });
    }
  }
  /**
   * @private unstable method
   */

  async function deleteBucketTagging({
    bucket
  }) {
    return this.fetchBucket(bucket, 'DELETE', {
      tagging: ''
    }, {});
  }

  /**
   * @private unstable method
   */
  async function putBucketPayByTraffic(input) {
    const res = await this.fetchBucket(input.bucket, 'PUT', {
      payByTraffic: ''
    }, {}, input.payByTraffic);
    return res;
  }
  /**
   * @private unstable method
   */

  async function getBucketPayByTraffic({
    bucket
  }) {
    const res = await this.fetchBucket(bucket, 'GET', {
      payByTraffic: ''
    }, {});
    return res;
  }

  /**
   * @private unstable method
   */

  async function getImageStyleBriefInfo(req) {
    const {
      bucket
    } = req;

    try {
      const res = await this.fetchBucket(bucket, 'GET', {
        imageStyleBriefInfo: ''
      }, {});
      return res;
    } catch (err) {
      if (err instanceof TosServerError) {
        if (err.statusCode === 404) {
          return this.getNormalDataFromError({
            BucketName: bucket,
            ImageStyleBriefInfo: []
          }, err);
        }
      }

      throw err;
    }
  }
  /**
   * @private unstable method
   */

  async function getBucketImageStyleList(bucket) {
    try {
      const res = await this.fetchBucket(bucket, 'GET', {
        imageStyle: ''
      }, {});
      return res;
    } catch (err) {
      if (err instanceof TosServerError) {
        if (err.statusCode === 404) {
          return this.getNormalDataFromError({
            ImageStyles: []
          }, err);
        }
      }

      throw err;
    }
  }
  /**
   * @private unstable method
   */

  async function getBucketImageStyleListByName(req) {
    try {
      const {
        bucket,
        styleName
      } = req;
      const res = await this.fetchBucket(bucket, 'GET', {
        imageStyleContent: '',
        styleName
      }, {});
      return res;
    } catch (err) {
      if (err instanceof TosServerError) {
        if (err.statusCode === 404) {
          return this.getNormalDataFromError({
            ImageStyles: []
          }, err);
        }
      }

      throw err;
    }
  }
  /**
   * @private unstable method
   */

  async function getBucketImageStyle(bucket, styleName) {
    try {
      const res = await this.fetchBucket(bucket, 'GET', {
        imageStyle: '',
        styleName
      }, {});
      return res;
    } catch (err) {
      if (err instanceof TosServerError) {
        if (err.statusCode === 404) {
          return this.getNormalDataFromError(null, err);
        }
      }

      throw err;
    }
  }
  /**
   * @private unstable method
   */

  async function putBucketImageStyle(req) {
    const {
      bucket,
      styleName,
      content,
      styleObjectPrefix
    } = req;

    try {
      const res = await this.fetchBucket(bucket, 'PUT', {
        imageStyle: '',
        styleName,
        styleObjectPrefix
      }, {}, {
        Content: content
      });
      return res;
    } catch (err) {
      if (err instanceof TosServerError) {
        if (err.statusCode === 404) {
          return this.getNormalDataFromError(null, err);
        }
      }

      throw err;
    }
  }
  /**
   * @private unstable method
   */

  async function deleteBucketImageStyle(req) {
    const {
      styleName,
      styleObjectPrefix,
      bucket
    } = req;

    try {
      const res = await this.fetchBucket(bucket, 'DELETE', {
        imageStyle: '',
        styleName,
        styleObjectPrefix
      }, {});
      return res;
    } catch (err) {
      if (err instanceof TosServerError) {
        if (err.statusCode === 404) {
          return this.getNormalDataFromError(null, err);
        }
      }

      throw err;
    }
  }
  /**
   * @private unstable method
   */

  async function putBucketImageProtect(bucket, data) {
    try {
      const res = await this.fetchBucket(bucket, 'PUT', {
        originalImageProtect: ''
      }, {}, data);
      return res;
    } catch (err) {
      if (err instanceof TosServerError) {
        if (err.statusCode === 404) {
          return this.getNormalDataFromError(null, err);
        }
      }

      throw err;
    }
  }
  /**
   * @private unstable method
   */

  async function getBucketImageProtect(bucket) {
    try {
      const res = await this.fetchBucket(bucket, 'GET', {
        originalImageProtect: ''
      }, {});
      return res;
    } catch (err) {
      if (err instanceof TosServerError) {
        if (err.statusCode === 404) {
          return this.getNormalDataFromError(null, err);
        }
      }

      throw err;
    }
  }
  /**
   * @private unstable method
   */

  async function putBucketImageStyleSeparator(req) {
    const {
      bucket,
      Separator,
      SeparatorSuffix
    } = req;

    try {
      const res = await this.fetchBucket(bucket, 'PUT', {
        imageStyleSeparator: ''
      }, {}, {
        Separator,
        SeparatorSuffix
      });
      return res;
    } catch (err) {
      if (err instanceof TosServerError) {
        if (err.statusCode === 404) {
          return this.getNormalDataFromError(null, err);
        }
      }

      throw err;
    }
  }
  /**
   * @private unstable method
   */

  async function getBucketImageStyleSeparator(bucket) {
    try {
      const res = await this.fetchBucket(bucket, 'GET', {
        imageStyleSeparator: ''
      }, {});
      return res;
    } catch (err) {
      if (err instanceof TosServerError) {
        if (err.statusCode === 404) {
          return this.getNormalDataFromError(null, err);
        }
      }

      throw err;
    }
  }

  async function getBucketIntelligenttiering(bucket) {
    try {
      const res = await this.fetchBucket(bucket, 'GET', {
        intelligenttiering: ''
      }, {});
      return res;
    } catch (error) {
      return handleEmptyServerError(error, {
        enableCatchEmptyServerError: this.opts.enableOptimizeMethodBehavior,
        methodKey: 'getBucketIntelligenttiering',
        defaultResponse: {}
      });
    }
  }

  const CommonQueryKey$7 = 'rename';
  async function putBucketRename(input) {
    const {
      bucket,
      ...otherProps
    } = input;
    const body = convertNormalCamelCase2Upper(otherProps);
    return this.fetchBucket(bucket, 'PUT', {
      [CommonQueryKey$7]: ''
    }, {}, { ...body
    });
  }
  async function getBucketRename(input) {
    const {
      bucket
    } = input;
    return await this.fetchBucket(bucket, 'GET', {
      [CommonQueryKey$7]: ''
    }, {});
  }
  async function deleteBucketRename(input) {
    const {
      bucket
    } = input;
    return this.fetchBucket(bucket, 'DELETE', {
      [CommonQueryKey$7]: ''
    }, {});
  }

  async function restoreObject(input) {
    const {
      bucket,
      key,
      versionId,
      ...otherProps
    } = input;
    const query = {
      restore: ''
    };

    if (versionId) {
      query.versionId = versionId;
    }

    const body = convertNormalCamelCase2Upper(otherProps);
    return this._fetchObject(input, 'POST', query, {}, body);
  }

  /**
   * @private unstable method
   * @description 获取数据透视列表
   * @param params
   * @returns
   */

  async function listStorageLens(params) {
    const {
      accountId
    } = params;
    const res = await this.fetch('GET', '/storagelens', {}, {
      'x-tos-account-id': accountId
    }, {}, {
      axiosOpts: {
        paramsSerializer
      }
    });
    return res;
  }
  /**
   * @private unstable method
   * @description 删除数据透视记录
   * @param params
   * @returns
   */

  async function deleteStorageLens(params) {
    const {
      accountId,
      Id
    } = params;
    const res = await this.fetch('DELETE', `/storagelens`, {
      id: Id
    }, {
      'x-tos-account-id': accountId
    }, {}, {
      needMd5: true
    });
    return res;
  }
  /**
   * @private unstable method
   * @description 获取数据透视详情
   * @param params
   * @returns
   */

  async function getStorageLens(params) {
    const {
      accountId,
      Id
    } = params;
    const res = await this.fetch('GET', `/storagelens`, {
      id: Id
    }, {
      'x-tos-account-id': accountId
    }, {}, {
      needMd5: true
    });
    return res;
  }
  /**
   * @private unstable method
   * @description 提交数据透视记录
   * @param params
   * @returns
   */

  async function putStorageLens(params) {
    const {
      accountId,
      Id,
      ...rest
    } = params;
    const res = await this.fetch('PUT', `/storagelens`, {
      id: Id
    }, {
      'x-tos-account-id': accountId
    }, { ...rest,
      Id
    }, {
      needMd5: true
    });
    return res;
  }

  const CommonQueryKey$8 = 'notification_v2';
  async function putBucketNotificationType2(input) {
    const {
      bucket,
      ...otherProps
    } = input;
    const body = convertNormalCamelCase2Upper(otherProps);
    return this.fetchBucket(bucket, 'PUT', {
      [CommonQueryKey$8]: ''
    }, {}, { ...body
    });
  }
  async function getBucketNotificationType2(input) {
    const {
      bucket
    } = input;

    try {
      return await this.fetchBucket(bucket, 'GET', {
        [CommonQueryKey$8]: ''
      }, {});
    } catch (error) {
      return handleEmptyServerError(error, {
        enableCatchEmptyServerError: this.opts.enableOptimizeMethodBehavior,
        methodKey: 'getBucketNotificationType2',
        defaultResponse: {
          Rules: []
        }
      });
    }
  }

  /**
   * @private unstable method
   */

  async function putSymlink(input) {
    return _putSymlink.call(this, input);
  }
  async function _putSymlink(input) {
    const headers = input.headers = normalizeHeadersKey(input.headers);
    fillRequestHeaders(input, ['symLinkTargetKey', 'symLinkTargetBucket', 'forbidOverwrite', 'acl', 'storageClass', 'meta']);
    return this._fetchObject(input, 'PUT', {
      symlink: ''
    }, headers, undefined, {
      handleResponse(response) {
        const {
          headers
        } = response;
        return {
          VersionID: headers['x-tos-version-id']
        };
      }

    });
  }

  /**
   * @private unstable method
   */
  async function getSymlink(input) {
    return _getSymlink.call(this, input);
  }
  async function _getSymlink(input) {
    const query = {
      symlink: ''
    };

    if (input.versionId) {
      query.versionId = input.versionId;
    }

    return this._fetchObject(input, 'GET', query, {}, undefined, {
      handleResponse: res => {
        const {
          headers
        } = res;
        return {
          VersionID: headers['x-tos-version-id'],
          SymlinkTargetKey: headers['x-tos-symlink-target'],
          SymlinkTargetBucket: headers['x-tos-symlink-bucket'],
          LastModified: headers['last-modified']
        };
      }
    });
  }

  const CommonQueryKey$9 = 'transferAcceleration';
  /**
   * @private unstable
   */

  async function putBucketTransferAcceleration(input) {
    const {
      bucket,
      ...otherProps
    } = input;
    const body = convertNormalCamelCase2Upper(otherProps);
    return this.fetchBucket(bucket, 'PUT', {
      [CommonQueryKey$9]: ''
    }, {}, { ...body
    });
  }
  /**
   * @private unstable
   */

  async function getBucketTransferAcceleration(input) {
    try {
      const {
        bucket
      } = input;
      const headers = {};

      if (input.getStatus) {
        headers['x-tos-get-bucket-acceleration-status'] = 'true';
      }

      const res = await this.fetchBucket(bucket, 'GET', {
        [CommonQueryKey$9]: ''
      }, headers);
      return res;
    } catch (error) {
      return handleEmptyServerError(error, {
        enableCatchEmptyServerError: this.opts.enableOptimizeMethodBehavior,
        methodKey: 'getBucketTransferAcceleration',
        defaultResponse: {
          TransferAccelerationConfiguration: {
            Enabled: 'false',
            Status: exports.TransferAccelerationStatusType.Terminated
          }
        }
      });
    }
  }

  /**
   * @private unstable method
   */

  async function putBucketAccessMonitor(input) {
    const {
      bucket,
      status
    } = input;
    return this.fetchBucket(bucket, 'PUT', {
      accessmonitor: ''
    }, {}, {
      Status: status
    });
  }
  /**
   * @private unstable method
   */

  async function getBucketAccessMonitor(input) {
    try {
      const {
        bucket
      } = input;
      return await this.fetchBucket(bucket, 'GET', {
        accessmonitor: ''
      }, {});
    } catch (error) {
      return handleEmptyServerError(error, {
        enableCatchEmptyServerError: this.opts.enableOptimizeMethodBehavior,
        methodKey: 'getBucketAccessMonitor',
        defaultResponse: {}
      });
    }
  }

  var StringOp;

  (function (StringOp) {
    StringOp["StringEquals"] = "StringEquals";
    StringOp["StringNotEquals"] = "StringNotEquals";
    StringOp["StringEqualsIgnoreCase"] = "StringEqualsIgnoreCase";
    StringOp["StringNotEqualsIgnoreCase"] = "StringNotEqualsIgnoreCase";
    StringOp["StringLike"] = "StringLike";
    StringOp["StringNotLike"] = "StringNotLike";
  })(StringOp || (StringOp = {}));

  var DateOp;

  (function (DateOp) {
    DateOp["DateEquals"] = "DateEquals";
    DateOp["DateNotEquals"] = "DateNotEquals";
    DateOp["DateLessThan"] = "DateLessThan";
    DateOp["DateLessThanEquals"] = "DateLessThanEquals";
    DateOp["DateGreaterThan"] = "DateGreaterThan";
    DateOp["DateGreaterThanEquals"] = "DateGreaterThanEquals";
  })(DateOp || (DateOp = {}));

  var IpOp;

  (function (IpOp) {
    IpOp["IpAddress"] = "IpAddress";
    IpOp["NotIpAddress"] = "NotIpAddress";
  })(IpOp || (IpOp = {}));
  /** 流控类别 */


  var QuotaType;

  (function (QuotaType) {
    /** 写Qps */
    QuotaType["WritesQps"] = "WritesQps";
    /** 读Qps */

    QuotaType["ReadsQps"] = "ReadsQps";
    /** list类Qps */

    QuotaType["ListQps"] = "ListQps";
    /** 写带宽 */

    QuotaType["WritesRate"] = "WritesRate";
    /** 读带宽 */

    QuotaType["ReadsRate"] = "ReadsRate";
  })(QuotaType || (QuotaType = {}));
  /**
   * @private unstable method
   * @description 拉取流控策略列表
   * @param {GetQosPolicyInput}
   * @returns {GetQosPolicyOutput}
   */


  async function getQosPolicy(params) {
    const {
      accountId
    } = params;
    const res = await this.fetch('GET', '/qospolicy', {}, {
      'x-tos-account-id': accountId
    }, {}, {});
    return res;
  }
  /**
   * @private unstable method
   * @description 更新流控策略列表 覆盖全部 QosPolicy
   * @param {PutQosPolicyInput}
   */

  async function putQosPolicy(params) {
    const {
      accountId,
      ...restParams
    } = params;
    const res = await this.fetch('PUT', '/qospolicy', {}, {
      'x-tos-account-id': accountId
    }, { ...restParams
    }, {});
    return res;
  }
  /**
   * @private unstable method
   * @description 拉取流控策略列表
   * @param {DeleteQosPolicyInput}
   */

  async function deleteQosPolicy(params) {
    const {
      accountId
    } = params;
    const res = await this.fetch('DELETE', '/qospolicy', {}, {
      'x-tos-account-id': accountId
    }, {}, {});
    return res;
  }

  /**
   * @private unstable method
   */
  async function createMultiRegionAccessPoint(input) {
    const {
      accountId,
      name,
      regions
    } = input;
    const res = await this.fetch('POST', '/mrap', {
      name
    }, {
      'x-tos-account-id': accountId
    }, {
      Name: name,
      Regions: regions
    }, {});
    return res;
  }
  /**
   * @private unstable method
   */

  async function getMultiRegionAccessPoint(input) {
    const {
      name,
      accountId
    } = input;
    const res = await this.fetch('GET', '/mrap', {
      name
    }, {
      'x-tos-account-id': accountId
    }, {}, {});
    return res;
  }
  /**
   * @private unstable method
   */

  async function listMultiRegionAccessPoints(input) {
    const {
      accountId,
      ...nextQuery
    } = input;
    const res = await this.fetch('GET', '/mrap', { ...nextQuery
    }, {
      'x-tos-account-id': accountId
    }, {}, {});
    return res;
  }
  /**
   * @private unstable method
   */

  async function getMultiRegionAccessPointRoutes(input) {
    const {
      accountId,
      alias
    } = input;
    const res = await this.fetch('GET', '/mrap/routes', {
      alias
    }, {
      'x-tos-account-id': accountId
    });
    return res;
  }
  async function deleteMultiRegionAccessPoint(input) {
    const {
      name,
      accountId
    } = input;
    const res = await this.fetch('DELETE', '/mrap', {
      name
    }, {
      'x-tos-account-id': accountId
    });
    return res;
  }
  async function submitMultiRegionAccessPointRoutes(input) {
    const {
      routes,
      accountId,
      alias
    } = input;
    const res = await this.fetch('PATCH', '/mrap/routes', {
      alias
    }, {
      'x-tos-account-id': accountId
    }, {
      Routes: routes
    });
    return res;
  }

  /**
   * @private unstable method
   */

  const putMultiRegionAccessPointMirrorBack = async function (input) {
    const {
      accountId,
      alias,
      rules
    } = input;

    if (this.opts.enableOptimizeMethodBehavior && !rules.length) {
      return deleteMultiRegionAccessPointMirrorBack.call(this, {
        accountId,
        alias
      });
    }

    const res = await this.fetch('PUT', '/mrap/mirror', {
      alias
    }, {
      'x-tos-account-id': accountId
    }, {
      Rules: rules
    }, {
      handleResponse() {
        return {};
      }

    });
    return res;
  };
  /**
   * @private unstable method
   */

  const getMultiRegionAccessPointMirrorBack = async function (input) {
    const {
      accountId,
      alias
    } = input;

    try {
      const res = await this.fetch('GET', '/mrap/mirror', {
        alias
      }, {
        'x-tos-account-id': accountId
      }, {}, {});
      const arrayProp = makeArrayProp(res.data);
      arrayProp('Rules');
      return res;
    } catch (error) {
      return handleEmptyServerError(error, {
        enableCatchEmptyServerError: this.opts.enableOptimizeMethodBehavior,
        methodKey: 'getMultiRegionAccessPointMirrorBack',
        defaultResponse: {
          Rules: []
        }
      });
    }
  };
  /**
   * @private unstable method
   */

  const deleteMultiRegionAccessPointMirrorBack = async function (input) {
    const {
      accountId,
      alias
    } = input;
    const res = await this.fetch('DELETE', '/mrap/mirror', {
      alias
    }, {
      'x-tos-account-id': accountId
    }, {}, {
      handleResponse() {
        return {};
      }

    });
    return res;
  };

  /**
   * @private unstable
   */

  async function putBucketPrivateM3U8(input) {
    const {
      bucket,
      enable
    } = input;
    return await this.fetchBucket(bucket, 'PUT', {
      privateM3U8: ''
    }, {}, {
      Enable: enable
    });
  }
  /**
   * @private unstable
   */

  async function getBucketPrivateM3U8(input) {
    const {
      bucket
    } = input;

    try {
      return await this.fetchBucket(bucket, 'GET', {
        privateM3U8: ''
      }, {});
    } catch (error) {
      return handleEmptyServerError(error, {
        enableCatchEmptyServerError: this.opts.enableOptimizeMethodBehavior,
        methodKey: 'getBucketPrivateM3U8',
        defaultResponse: {
          Enable: false
        }
      });
    }
  }

  const CommonQueryKey$a = 'trash';
  async function putBucketTrash(input) {
    const {
      bucket,
      ...otherProps
    } = input;
    const body = convertNormalCamelCase2Upper(otherProps);
    return this.fetchBucket(bucket, 'PUT', {
      [CommonQueryKey$a]: ''
    }, {}, { ...body
    });
  }
  async function getBucketTrash(input) {
    const {
      bucket
    } = input;
    return await this.fetchBucket(bucket, 'GET', {
      [CommonQueryKey$a]: ''
    }, {});
  }

  class InnerClient extends TOSBase {
    constructor(...args) {
      super(...args);
      this.createBucket = createBucket;
      this.headBucket = headBucket;
      this.deleteBucket = deleteBucket;
      this.listBuckets = listBuckets;
      this.getBucketLocation = getBucketLocation;
      this.putBucketStorageClass = putBucketStorageClass;
      this.getBucketAcl = getBucketAcl;
      this.putBucketAcl = putBucketAcl;
      this.getBucketPolicy = getBucketPolicy;
      this.putBucketPolicy = putBucketPolicy;
      this.deleteBucketPolicy = deleteBucketPolicy;
      this.getBucketVersioning = getBucketVersioning;
      this.putBucketVersioning = putBucketVersioning;
      this.getBucketCORS = getBucketCORS;
      this.putBucketCORS = putBucketCORS;
      this.deleteBucketCORS = deleteBucketCORS;
      this.putBucketLifecycle = putBucketLifecycle;
      this.getBucketLifecycle = getBucketLifecycle;
      this.deleteBucketLifecycle = deleteBucketLifecycle;
      this.putBucketEncryption = putBucketEncryption;
      this.getBucketEncryption = getBucketEncryption;
      this.deleteBucketEncryption = deleteBucketEncryption;
      this.putBucketMirrorBack = putBucketMirrorBack;
      this.getBucketMirrorBack = getBucketMirrorBack;
      this.deleteBucketMirrorBack = deleteBucketMirrorBack;
      this.putBucketReplication = putBucketReplication;
      this.getBucketReplication = getBucketReplication;
      this.deleteBucketReplication = deleteBucketReplication;
      this.putBucketWebsite = putBucketWebsite;
      this.getBucketWebsite = getBucketWebsite;
      this.deleteBucketWebsite = deleteBucketWebsite;
      this.putBucketNotification = putBucketNotification;
      this.getBucketNotification = getBucketNotification;
      this.putBucketCustomDomain = putBucketCustomDomain;
      this.getBucketCustomDomain = getBucketCustomDomain;
      this.deleteBucketCustomDomain = deleteBucketCustomDomain;
      this.putBucketRealTimeLog = putBucketRealTimeLog;
      this.getBucketRealTimeLog = getBucketRealTimeLog;
      this.deleteBucketRealTimeLog = deleteBucketRealTimeLog;
      this.getBucketInventory = getBucketInventory;
      this.listBucketInventory = listBucketInventory;
      this.putBucketInventory = putBucketInventory;
      this.deleteBucketInventory = deleteBucketInventory;
      this.putBucketTagging = putBucketTagging;
      this.getBucketTagging = getBucketTagging;
      this.deleteBucketTagging = deleteBucketTagging;
      this.putBucketPayByTraffic = putBucketPayByTraffic;
      this.getBucketPayByTraffic = getBucketPayByTraffic;
      this.getBucketImageStyle = getBucketImageStyle;
      this.getBucketImageStyleList = getBucketImageStyleList;
      this.getBucketImageStyleListByName = getBucketImageStyleListByName;
      this.getImageStyleBriefInfo = getImageStyleBriefInfo;
      this.deleteBucketImageStyle = deleteBucketImageStyle;
      this.putBucketImageStyle = putBucketImageStyle;
      this.putBucketImageStyleSeparator = putBucketImageStyleSeparator;
      this.putBucketImageProtect = putBucketImageProtect;
      this.getBucketImageProtect = getBucketImageProtect;
      this.getBucketImageStyleSeparator = getBucketImageStyleSeparator;
      this.putBucketRename = putBucketRename;
      this.getBucketRename = getBucketRename;
      this.deleteBucketRename = deleteBucketRename;
      this.putBucketTransferAcceleration = putBucketTransferAcceleration;
      this.getBucketTransferAcceleration = getBucketTransferAcceleration;
      this.copyObject = copyObject;
      this.resumableCopyObject = resumableCopyObject;
      this.deleteObject = deleteObject;
      this.deleteMultiObjects = deleteMultiObjects;
      this.getObject = getObject;
      this.getObjectV2 = getObjectV2;
      this.getObjectToFile = getObjectToFile;
      this.getObjectAcl = getObjectAcl;
      this.headObject = headObject;
      this.appendObject = appendObject;
      this.listObjects = listObjects;
      this.renameObject = renameObject;
      this.fetchObject = fetchObject;
      this.putFetchTask = putFetchTask;
      this.listObjectsType2 = listObjectsType2;
      this.listObjectVersions = listObjectVersions;
      this.putObject = putObject;
      this.putObjectFromFile = putObjectFromFile;
      this.putObjectAcl = putObjectAcl;
      this.setObjectMeta = setObjectMeta;
      this.createMultipartUpload = createMultipartUpload;
      this.uploadPart = uploadPart;
      this.uploadPartFromFile = uploadPartFromFile;
      this.completeMultipartUpload = completeMultipartUpload;
      this.abortMultipartUpload = abortMultipartUpload;
      this.uploadPartCopy = uploadPartCopy;
      this.listMultipartUploads = listMultipartUploads;
      this.listParts = listParts;
      this.downloadFile = downloadFile;
      this.putObjectTagging = putObjectTagging;
      this.getObjectTagging = getObjectTagging;
      this.deleteObjectTagging = deleteObjectTagging;
      this.listJobs = listJobs;
      this.createJob = createJob;
      this.deleteJob = deleteJob;
      this.describeJob = describeJob;
      this.updateJobStatus = updateJobStatus;
      this.updateJobPriority = updateJobPriority;
      this.restoreObject = restoreObject;
      this.uploadFile = uploadFile;
      this.getPreSignedUrl = getPreSignedUrl;
      this.calculatePostSignature = calculatePostSignature;
      this.preSignedPostSignature = calculatePostSignature;
      this.preSignedPolicyURL = preSignedPolicyURL;
      this.getBucketIntelligenttiering = getBucketIntelligenttiering;
      this.listStorageLens = listStorageLens;
      this.deleteStorageLens = deleteStorageLens;
      this.getStorageLens = getStorageLens;
      this.putStorageLens = putStorageLens;
      this.putBucketNotificationType2 = putBucketNotificationType2;
      this.getBucketNotificationType2 = getBucketNotificationType2;
      this.putSymlink = putSymlink;
      this.getSymlink = getSymlink;
      this.putBucketAccessMonitor = putBucketAccessMonitor;
      this.getBucketAccessMonitor = getBucketAccessMonitor;
      this.putQosPolicy = putQosPolicy;
      this.getQosPolicy = getQosPolicy;
      this.deleteQosPolicy = deleteQosPolicy;
      this.createMultiRegionAccessPoint = createMultiRegionAccessPoint;
      this.getMultiRegionAccessPoint = getMultiRegionAccessPoint;
      this.listMultiRegionAccessPoints = listMultiRegionAccessPoints;
      this.getMultiRegionAccessPointRoutes = getMultiRegionAccessPointRoutes;
      this.deleteMultiRegionAccessPoint = deleteMultiRegionAccessPoint;
      this.submitMultiRegionAccessPointRoutes = submitMultiRegionAccessPointRoutes;
      this.putMultiRegionAccessPointMirrorBack = putMultiRegionAccessPointMirrorBack;
      this.getMultiRegionAccessPointMirrorBack = getMultiRegionAccessPointMirrorBack;
      this.deleteMultiRegionAccessPointMirrorBack = deleteMultiRegionAccessPointMirrorBack;
      this.putBucketPrivateM3U8 = putBucketPrivateM3U8;
      this.getBucketPrivateM3U8 = getBucketPrivateM3U8;
      this.putBucketTrash = putBucketTrash;
      this.getBucketTrash = getBucketTrash;
    }

  }

  const CancelToken$1 = axios$1.CancelToken; // for export

  class TosClient extends InnerClient {}

  TosClient.TosServerError = TosServerError;
  TosClient.isCancel = isCancelError;
  TosClient.CancelError = CancelError;
  TosClient.TosServerCode = exports.TosServerCode;
  TosClient.TosClientError = TosClientError;
  TosClient.CancelToken = CancelToken$1;
  TosClient.ACLType = exports.ACLType;
  TosClient.StorageClassType = exports.StorageClassType;
  TosClient.MetadataDirectiveType = exports.MetadataDirectiveType;
  TosClient.AzRedundancyType = exports.AzRedundancyType;
  TosClient.PermissionType = exports.PermissionType;
  TosClient.GranteeType = exports.GranteeType;
  TosClient.CannedType = exports.CannedType;
  TosClient.HttpMethodType = exports.HttpMethodType;
  TosClient.LifecycleStatusType = exports.LifecycleStatusType;
  TosClient.StatusType = exports.StatusType;
  TosClient.RedirectType = exports.RedirectType;
  TosClient.StorageClassInheritDirectiveType = exports.StorageClassInheritDirectiveType;
  TosClient.TierType = exports.TierType;
  TosClient.VersioningStatusType = exports.VersioningStatusType;
  TosClient.createDefaultRateLimiter = createDefaultRateLimiter$1;
  TosClient.DataTransferType = exports.DataTransferType;
  TosClient.UploadEventType = exports.UploadEventType;
  TosClient.DownloadEventType = exports.DownloadEventType;
  TosClient.ResumableCopyEventType = exports.ResumableCopyEventType;
  TosClient.ReplicationStatusType = exports.ReplicationStatusType;
  TosClient.AccessPointStatusType = exports.AccessPointStatusType;
  TosClient.TransferAccelerationStatusType = exports.TransferAccelerationStatusType;
  TosClient.MRAPMirrorBackRedirectPolicyType = exports.MRAPMirrorBackRedirectPolicyType;
  TosClient.ShareLinkClient = ShareLinkClient;

  {
    // @ts-ignore
    if (typeof window !== 'undefined') {
      // @ts-ignore
      window.TOS = TosClient; // @ts-ignore

      window.TosClient = TosClient;
    }

    if (typeof global !== 'undefined') {
      // @ts-ignore
      global.TOS = TosClient; // @ts-ignore

      global.TosClient = TosClient;
    }

    if (typeof globalThis !== 'undefined') {
      // @ts-ignore
      globalThis.TOS = TosClient; // @ts-ignore

      globalThis.TosClient = TosClient;
    }
  }

  exports.CancelError = CancelError;
  exports.CancelToken = CancelToken$1;
  exports.ShareLinkClient = ShareLinkClient;
  exports.TOS = TosClient;
  exports.TosClient = TosClient;
  exports.TosClientError = TosClientError;
  exports.TosServerError = TosServerError;
  exports.createDefaultRateLimiter = createDefaultRateLimiter$1;
  exports.default = TosClient;
  exports.isCancel = isCancelError;

  Object.defineProperty(exports, '__esModule', { value: true });

})));
//# sourceMappingURL=tos.umd.development.js.map
