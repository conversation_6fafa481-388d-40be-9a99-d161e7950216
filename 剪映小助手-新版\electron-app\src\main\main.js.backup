const { app, BrowserWindow, ipcMain, dialog, shell, clipboard, globalShortcut, Menu } = require('electron')
const path = require('path')
const fs = require('fs')
const os = require('os')
const https = require('https')
const { spawn, exec } = require('child_process')
const Store = require('electron-store')
const yauzl = require('yauzl')

// 初始化配置存储
const store = new Store()

// 文件夹属性分析函数
function analyzeFolderProperties(folderPath, label = '') {
  try {
    const stats = fs.statSync(folderPath)
    console.log(`=== 文件夹属性分析 ${label} ===`)
    console.log('路径:', folderPath)
    console.log('是否为目录:', stats.isDirectory())
    console.log('权限模式:', stats.mode.toString(8))
    console.log('用户ID:', stats.uid)
    console.log('组ID:', stats.gid)
    console.log('大小:', stats.size)
    console.log('访问时间:', stats.atime.toISOString())
    console.log('修改时间:', stats.mtime.toISOString())
    console.log('状态改变时间:', stats.ctime.toISOString())
    console.log('创建时间:', stats.birthtime.toISOString())

    // Windows特定属性检查
    if (process.platform === 'win32') {
      exec(`dir "${folderPath}" /Q /T:C`, (error, stdout, stderr) => {
        if (!error) {
          console.log('Windows目录信息:', stdout)
        }
      })

      exec(`attrib "${folderPath}"`, (error, stdout, stderr) => {
        if (!error) {
          console.log('Windows文件属性:', stdout.trim())
        }
      })
    }

    return {
      isDirectory: stats.isDirectory(),
      mode: stats.mode,
      uid: stats.uid,
      gid: stats.gid,
      size: stats.size,
      atime: stats.atime,
      mtime: stats.mtime,
      ctime: stats.ctime,
      birthtime: stats.birthtime
    }
  } catch (error) {
    console.error(`分析文件夹属性失败 ${label}:`, error)
    return null
  }
}

// 改进的文件夹创建函数
function createDraftFolderWithCorrectProperties(folderPath) {
  try {
    console.log('使用改进方法创建文件夹:', folderPath)

    // 1. 创建文件夹（使用默认权限）
    fs.mkdirSync(folderPath, { recursive: true })

    // 2. 设置时间戳（模仿自然创建的文件夹）
    const now = new Date()
    fs.utimesSync(folderPath, now, now)

    // 3. Windows特定优化
    if (process.platform === 'win32') {
      // 确保文件夹没有特殊属性
      exec(`attrib -h -s -r "${folderPath}"`, (error, stdout, stderr) => {
        if (error) {
          console.log('设置文件夹属性时出现警告:', error.message)
        } else {
          console.log('已清除文件夹特殊属性')
        }
      })

      // 设置标准的文件夹权限
      exec(`icacls "${folderPath}" /grant Everyone:(OI)(CI)F`, (error, stdout, stderr) => {
        if (error) {
          console.log('设置文件夹权限时出现警告:', error.message)
        } else {
          console.log('已设置标准文件夹权限')
        }
      })
    }

    console.log('文件夹创建完成:', folderPath)
    return true
  } catch (error) {
    console.error('创建文件夹失败:', error)
    return false
  }
}

let mainWindow

// 创建主窗口
function createWindow() {
  console.log('=== 剪映小助手启动 ===')
  console.log('开始创建主窗口...')

  mainWindow = new BrowserWindow({
    width: 1400,
    height: 1000,
    minWidth: 1000,
    minHeight: 800,
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false,
      enableRemoteModule: true
    },
    icon: path.join(__dirname, '../../assets/icon.png'),
    title: '剪映小助手 - 智界AigcView',
    show: false,
    titleBarStyle: 'default'
  })

  // 加载主页面
  mainWindow.loadFile(path.join(__dirname, '../renderer/index.html'))

  // 窗口准备好后显示
  mainWindow.once('ready-to-show', () => {
    mainWindow.show()

    // 延迟打开开发者工具，确保窗口完全加载
    setTimeout(() => {
      mainWindow.webContents.openDevTools({ mode: 'detach' })
      console.log('开发者工具已打开')
    }, 1000)

    // 清理缓存并强制刷新
    mainWindow.webContents.session.clearCache()
    console.log('已清理Electron缓存')
    console.log('主窗口创建完成')
  })

  // 窗口关闭事件
  mainWindow.on('closed', () => {
    mainWindow = null
  })
}

// 创建中文菜单
function createMenu() {
  const template = [
    {
      label: '文件',
      submenu: [
        {
          label: '导入草稿',
          accelerator: 'CmdOrCtrl+O',
          click: () => {
            mainWindow.webContents.send('menu-import-draft')
          }
        },
        { type: 'separator' },
        {
          label: '退出',
          accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
          click: () => {
            app.quit()
          }
        }
      ]
    },
    {
      label: '编辑',
      submenu: [
        {
          label: '复制',
          accelerator: 'CmdOrCtrl+C',
          role: 'copy'
        },
        {
          label: '粘贴',
          accelerator: 'CmdOrCtrl+V',
          role: 'paste'
        },
        {
          label: '全选',
          accelerator: 'CmdOrCtrl+A',
          role: 'selectall'
        }
      ]
    },
    {
      label: '视图',
      submenu: [
        {
          label: '重新加载',
          accelerator: 'CmdOrCtrl+R',
          click: () => {
            mainWindow.reload()
          }
        },
        {
          label: '开发者工具',
          accelerator: process.platform === 'darwin' ? 'Alt+Cmd+I' : 'Ctrl+Shift+I',
          click: () => {
            mainWindow.webContents.toggleDevTools()
          }
        },
        { type: 'separator' },
        {
          label: '实际大小',
          accelerator: 'CmdOrCtrl+0',
          role: 'resetzoom'
        },
        {
          label: '放大',
          accelerator: 'CmdOrCtrl+Plus',
          role: 'zoomin'
        },
        {
          label: '缩小',
          accelerator: 'CmdOrCtrl+-',
          role: 'zoomout'
        }
      ]
    },
    {
      label: '帮助',
      submenu: [
        {
          label: '关于剪映小助手',
          click: () => {
            dialog.showMessageBox(mainWindow, {
              type: 'info',
              title: '关于',
              message: '剪映小助手',
              detail: '版本: 1.0.0\n开发者: 智界AigcView\n官网: www.aigcview.com'
            })
          }
        },
        {
          label: '访问官网',
          click: () => {
            shell.openExternal('https://www.aigcview.com')
          }
        }
      ]
    }
  ]

  const menu = Menu.buildFromTemplate(template)
  Menu.setApplicationMenu(menu)
}

// 应用准备就绪
app.whenReady().then(() => {
  console.log('=== Electron应用启动 ===')
  console.log('开始创建窗口和注册事件...')

  createWindow()
  createMenu()

  // macOS 特殊处理
  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      createWindow()
    }
  })

  // 注册全局快捷键
  registerGlobalShortcuts()

  // 不自动检查剪贴板，让用户手动操作
  // setTimeout(checkClipboardForDraftUrl, 1000)

  console.log('应用启动完成，所有事件已注册')
})

// 所有窗口关闭时退出应用 (macOS除外)
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit()
  }
})

// 应用退出前清理
app.on('before-quit', () => {
  globalShortcut.unregisterAll()
})

// 注册全局快捷键
function registerGlobalShortcuts() {
  // 不自动检查剪贴板，让用户手动粘贴
  // globalShortcut.register('CommandOrControl+V', () => {
  //   if (mainWindow && mainWindow.isFocused()) {
  //     checkClipboardForDraftUrl()
  //   }
  // })

  // Ctrl+Enter 快速导入
  globalShortcut.register('CommandOrControl+Enter', () => {
    if (mainWindow && mainWindow.isFocused()) {
      mainWindow.webContents.send('quick-import')
    }
  })
}

// 检查剪贴板中的草稿链接
function checkClipboardForDraftUrl() {
  try {
    const clipboardText = clipboard.readText()
    if (validateDraftUrl(clipboardText)) {
      mainWindow.webContents.send('clipboard-draft-detected', clipboardText)
    }
  } catch (error) {
    console.error('检查剪贴板失败:', error)
  }
}

// 验证草稿URL
function validateDraftUrl(url) {
  if (!url || typeof url !== 'string') return false

  // 支持火山引擎TOS的双斜杠格式（这是正常的）
  // 匹配: https://任意子域名.volces.com/一个或多个斜杠/jianying-assistant/drafts/路径/文件名.json|zip
  const tosPattern = /^https:\/\/.*\.volces\.com\/+jianying-assistant\/drafts\/.+\.(json|zip)$/

  // 调试信息
  console.log('验证URL:', url)
  console.log('正则表达式:', tosPattern.toString())
  const result = tosPattern.test(url)
  console.log('验证结果:', result)

  return result
}

// 检查URL是否为ZIP文件
function isZipUrl(url) {
  return url && url.endsWith('.zip')
}

// IPC 事件处理

// 获取剪映草稿文件夹路径
ipcMain.handle('get-jianying-path', async () => {
  return store.get('jianyingDraftsPath', '')
})

// 设置剪映草稿文件夹路径
ipcMain.handle('set-jianying-path', async (event, jianyingPath) => {
  if (jianyingPath && fs.existsSync(jianyingPath)) {
    store.set('jianyingDraftsPath', jianyingPath)
    return { success: true }
  }
  return { success: false, error: '路径不存在' }
})

// 选择剪映草稿文件夹
ipcMain.handle('select-jianying-path', async () => {
  const result = await dialog.showOpenDialog(mainWindow, {
    title: '请选择剪映草稿文件夹',
    properties: ['openDirectory']
  })

  if (!result.canceled && result.filePaths.length > 0) {
    const selectedPath = result.filePaths[0]
    store.set('jianyingDraftsPath', selectedPath)
    return { success: true, path: selectedPath }
  }

  return { success: false }
})

// 验证草稿URL
ipcMain.handle('validate-draft-url', async (event, url) => {
  return validateDraftUrl(url)
})

// 下载草稿文件（支持JSON和ZIP）
ipcMain.handle('download-draft', async (event, url) => {
  console.log('=== IPC download-draft 被调用 ===')
  console.log('接收到的URL:', url)

  // 发送日志到渲染进程
  mainWindow.webContents.send('import-progress', '🔍 主进程：download-draft被调用')
  mainWindow.webContents.send('import-progress', `📥 接收URL: ${url}`)

  return new Promise((resolve, reject) => {
    try {
      const isZip = isZipUrl(url)
      const fileExtension = isZip ? 'zip' : 'json'
      const fileName = `draft_${Date.now()}.${fileExtension}`
      const tempDir = app.getPath('temp')
      const savePath = path.join(tempDir, fileName)

      const file = fs.createWriteStream(savePath)

      https.get(url, (response) => {
        if (response.statusCode !== 200) {
          reject(new Error(`下载失败: HTTP ${response.statusCode}`))
          return
        }

        const totalSize = parseInt(response.headers['content-length'], 10)
        let downloadedSize = 0

        response.on('data', (chunk) => {
          downloadedSize += chunk.length
          const progress = totalSize ? (downloadedSize / totalSize) * 100 : 0
          mainWindow.webContents.send('download-progress', progress)
        })

        response.pipe(file)

        file.on('finish', () => {
          file.close()
          resolve({ success: true, path: savePath, isZip: isZip })
        })

        file.on('error', (error) => {
          fs.unlink(savePath, () => {}) // 删除不完整的文件
          reject(error)
        })

      }).on('error', (error) => {
        reject(error)
      })

    } catch (error) {
      reject(error)
    }
  })
})

// 解压ZIP文件
async function extractZipFile(zipPath, extractDir) {
  return new Promise((resolve, reject) => {
    yauzl.open(zipPath, { lazyEntries: true }, (err, zipfile) => {
      if (err) {
        reject(err)
        return
      }

      zipfile.readEntry()

      zipfile.on('entry', (entry) => {
        if (/\/$/.test(entry.fileName)) {
          // 目录条目
          const dirPath = path.join(extractDir, entry.fileName)
          fs.mkdir(dirPath, { recursive: true }, (err) => {
            if (err) {
              reject(err)
              return
            }
            zipfile.readEntry()
          })
        } else {
          // 文件条目
          zipfile.openReadStream(entry, (err, readStream) => {
            if (err) {
              reject(err)
              return
            }

            const filePath = path.join(extractDir, entry.fileName)
            const fileDir = path.dirname(filePath)

            fs.mkdir(fileDir, { recursive: true }, (err) => {
              if (err) {
                reject(err)
                return
              }

              const writeStream = fs.createWriteStream(filePath)
              readStream.pipe(writeStream)

              writeStream.on('close', () => {
                zipfile.readEntry()
              })

              writeStream.on('error', (err) => {
                reject(err)
              })
            })
          })
        }
      })

      zipfile.on('end', () => {
        resolve()
      })

      zipfile.on('error', (err) => {
        reject(err)
      })
    })
  })
}

// 导入草稿到剪映（支持JSON和ZIP）
ipcMain.handle('import-to-jianying', async (event, draftPath, isZip = false) => {
  try {
    const jianyingDraftsPath = store.get('jianyingDraftsPath')
    if (!jianyingDraftsPath || !fs.existsSync(jianyingDraftsPath)) {
      throw new Error('剪映草稿文件夹未设置或不存在')
    }

    if (isZip) {
      // 处理ZIP文件
      mainWindow.webContents.send('import-progress', '正在解压草稿包...')

      // 创建临时解压目录
      const tempExtractDir = path.join(app.getPath('temp'), `extract_${Date.now()}`)
      await fs.promises.mkdir(tempExtractDir, { recursive: true })

      // 解压ZIP文件
      await extractZipFile(draftPath, tempExtractDir)

      // 查找解压后的草稿文件夹（第一个子目录）
      const extractedItems = await fs.promises.readdir(tempExtractDir)
      const draftFolderName = extractedItems.find(item => {
        const itemPath = path.join(tempExtractDir, item)
        return fs.statSync(itemPath).isDirectory()
      })

      if (!draftFolderName) {
        throw new Error('ZIP文件中未找到草稿文件夹')
      }

      const extractedDraftPath = path.join(tempExtractDir, draftFolderName)

      // 移动到剪映草稿目录
      const targetProjectDir = path.join(jianyingDraftsPath, draftFolderName)

      // 如果目标目录已存在，添加时间戳后缀
      let finalTargetDir = targetProjectDir
      if (fs.existsSync(targetProjectDir)) {
        finalTargetDir = path.join(jianyingDraftsPath, `${draftFolderName}_${Date.now()}`)
      }

      mainWindow.webContents.send('import-progress', '正在导入到剪映...')

      // 移动整个文件夹
      await fs.promises.rename(extractedDraftPath, finalTargetDir)

      // 清理临时目录
      await fs.promises.rmdir(tempExtractDir, { recursive: true })

      // 添加到历史记录
      addToImportHistory(draftPath, finalTargetDir)

      return { success: true, projectPath: finalTargetDir }

    } else {
      // 处理JSON文件（支持URL和本地文件）
      console.log('=== 开始处理JSON文件 ===')
      console.log('输入路径:', draftPath)
      mainWindow.webContents.send('import-progress', '正在处理草稿JSON...')

      let draftJson
      let isUrl = draftPath.startsWith('http://') || draftPath.startsWith('https://')
      console.log('是否为URL:', isUrl)

      if (isUrl) {
        // 下载JSON文件
        console.log('开始下载JSON文件...')
        mainWindow.webContents.send('import-progress', '正在下载草稿JSON...')
        draftJson = await downloadJsonFile(draftPath)
        console.log('JSON下载完成')
      } else {
        // 读取本地JSON文件
        console.log('读取本地JSON文件...')
        const jsonContent = await fs.promises.readFile(draftPath, 'utf8')
        draftJson = JSON.parse(jsonContent)
        console.log('本地JSON读取完成')
      }

      console.log('开始创建草稿文件夹结构...')
      // 根据JSON内容创建完整的草稿文件夹结构
      const projectPath = await createDraftFromJson(draftJson, jianyingDraftsPath)
      console.log('草稿文件夹创建完成:', projectPath)

      // 添加到历史记录
      addToImportHistory(draftPath, projectPath)

      return { success: true, projectPath: projectPath }
    }

  } catch (error) {
    return { success: false, error: error.message }
  }
})

// 添加到导入历史
function addToImportHistory(draftPath, projectPath) {
  console.log('添加历史记录:')
  console.log('  draftPath:', draftPath)
  console.log('  projectPath:', projectPath)

  const history = store.get('importHistory', [])
  const newItem = {
    draftPath,
    projectPath,
    timestamp: Date.now(),
    name: `zj_draft_${new Date().toLocaleString()}`
  }

  console.log('  新记录:', JSON.stringify(newItem, null, 2))

  history.unshift(newItem)

  // 只保留最近20条记录
  store.set('importHistory', history.slice(0, 20))
}

// 获取导入历史
ipcMain.handle('get-import-history', async () => {
  const history = store.get('importHistory', [])
  console.log('获取历史记录:', JSON.stringify(history, null, 2))
  return history
})

// 打开外部链接
ipcMain.handle('open-external', async (event, url) => {
  shell.openExternal(url)
})

// 显示消息框
ipcMain.handle('show-message', async (event, options) => {
  return dialog.showMessageBox(mainWindow, options)
})

// 打开项目文件夹
ipcMain.handle('open-project-folder', async (event, projectPath) => {
  console.log('主进程：尝试打开项目文件夹:', projectPath)

  try {
    // 检查路径是否存在
    if (!fs.existsSync(projectPath)) {
      console.error('主进程：路径不存在:', projectPath)
      return { success: false, error: '路径不存在' }
    }

    console.log('主进程：路径存在，尝试打开...')

    // 尝试打开路径
    const result = await shell.openPath(projectPath)

    if (result) {
      // 如果有错误信息，说明打开失败
      console.error('主进程：打开路径失败:', result)

      // 尝试在文件管理器中显示
      console.log('主进程：尝试在文件管理器中显示...')
      shell.showItemInFolder(projectPath)
      return { success: true, message: '已在文件管理器中显示' }
    } else {
      console.log('主进程：路径打开成功')
      return { success: true, message: '已打开项目文件夹' }
    }

  } catch (error) {
    console.error('主进程：打开项目失败:', error)
    return { success: false, error: error.message }
  }
})

// 下载JSON文件
async function downloadJsonFile(url) {
  return new Promise((resolve, reject) => {
    console.log('开始下载JSON文件:', url)

    // 处理火山引擎TOS的双斜杠URL（这是正常的格式）
    console.log('原始URL:', url)

    const protocol = url.startsWith('https:') ? https : require('http')

    protocol.get(url, (response) => {
      console.log('HTTP响应状态码:', response.statusCode)
      console.log('HTTP响应头:', response.headers)

      if (response.statusCode !== 200) {
        reject(new Error(`下载失败，状态码: ${response.statusCode}`))
        return
      }

      let data = ''
      response.on('data', (chunk) => {
        data += chunk
      })

      response.on('end', () => {
        try {
          console.log('下载的原始数据长度:', data.length)
          console.log('下载的原始数据前100字符:', data.substring(0, 100))

          const jsonData = JSON.parse(data)
          console.log('JSON文件下载成功，大小:', data.length, '字符')
          resolve(jsonData)
        } catch (error) {
          console.error('JSON解析失败，原始数据:', data)
          reject(new Error('JSON解析失败: ' + error.message))
        }
      })
    }).on('error', (error) => {
      console.error('HTTP请求失败:', error)
      reject(new Error('下载失败: ' + error.message))
    })
  })
}

// 根据JSON内容创建完整的草稿文件夹结构
async function createDraftFromJson(draftJson, jianyingDraftsPath) {
  try {
    console.log('开始根据JSON创建草稿文件夹结构')

    // 生成项目文件夹名称
    const projectId = draftJson.id || `zj_${Date.now()}`
    const projectDir = path.join(jianyingDraftsPath, projectId)

    // 创建项目目录（使用改进的方法）
    createDraftFolderWithCorrectProperties(projectDir)
    console.log('创建项目目录:', projectDir)

    // 1. 先创建基础草稿文件（保留原始字段用于下载）
    await createBaseDraftFiles(projectDir, projectId, draftJson)

    // 2. 再处理素材文件（音频、视频、图片等）
    await processMaterialFiles(projectDir, draftJson)

    // 3. 最后更新草稿文件（删除多余字段，生成最终版本）
    await updateFinalDraftFiles(projectDir, projectId, draftJson)

    console.log('草稿文件夹结构创建完成:', projectDir)
    return projectDir

  } catch (error) {
    console.error('创建草稿文件夹结构失败:', error)
    throw error
  }
}

// 完全重写的草稿文件更新函数
async function updateFinalDraftFiles(projectDir, projectId, draftJson) {
  try {
    console.log('=== 开始完整修复草稿文件 ===')

    // 分析文件夹属性
    analyzeFolderProperties(projectDir, '我们的草稿文件夹')

    // 深度复制并清理数据
    const sourceData = JSON.parse(JSON.stringify(draftJson))

    // 音频素材去重（基于download_url，与processMaterialFiles保持一致）
    let finalAudios = []
    if (sourceData.materials?.audios) {
      console.log(`JSON中原始音频素材数量: ${sourceData.materials.audios.length}`)

      // 使用相同的去重逻辑：基于download_url
      const urlMap = new Map()
      sourceData.materials.audios.forEach(audio => {
        // 使用原始的download_url作为key（如果存在的话）
        const key = audio.download_url || audio.path || audio.id
        urlMap.set(key, audio)
      })

      finalAudios = Array.from(urlMap.values()).map(audio => {
        const { download_url, file_name, ...cleanAudio } = audio
        return cleanAudio
      })

      console.log(`JSON去重后音频素材数量: ${finalAudios.length}`)
    }

    // 获取有效音频ID
    const audioIds = new Set(finalAudios.map(audio => audio.id))
    console.log('有效音频ID:', Array.from(audioIds))

    // 第三步：处理轨道，过滤无效引用
    const cleanTracks = (sourceData.tracks || []).map(track => {
      if (track.type === 'audio' && track.segments) {
        // 过滤掉引用无效音频素材的段
        const validSegments = track.segments.filter(segment => {
          if (segment.material_id && !audioIds.has(segment.material_id)) {
            console.log(`移除引用无效音频素材的段: ${segment.material_id}`)
            return false
          }
          return true
        })

        return { ...track, segments: validSegments }
      }
      return track
    })

    // 第四步：构建最终JSON（完全按照竞争对手格式）
    const finalJson = {
      canvas_config: {
        height: sourceData.canvas_config?.height || 1024,
        ratio: sourceData.canvas_config?.ratio || "original",
        width: sourceData.canvas_config?.width || 1024
      },
      color_space: sourceData.color_space || -1,
      config: {
        adjust_max_index: 1,
        attachment_info: [],
        combination_max_index: 1,
        export_range: null,
        extract_audio_last_index: 1,
        lyrics_recognition_id: "",
        lyrics_sync: true,
        lyrics_taskinfo: [],
        maintrack_adsorb: true,
        material_save_mode: 0,
        multi_language_current: "none",
        multi_language_list: [],
        multi_language_main: "none",
        multi_language_mode: "none",
        original_sound_last_index: 1,
        record_audio_last_index: 1,
        sticker_max_index: 1,
        subtitle_keywords_config: null,
        subtitle_recognition_id: "",
        subtitle_sync: true,
        subtitle_taskinfo: [],
        system_font_list: [],
        video_mute: false,
        zoom_info_params: null
      },
      cover: sourceData.cover || null,
      create_time: sourceData.create_time || 0,
      duration: sourceData.duration || 10000000,
      extra_info: sourceData.extra_info || null,
      fps: sourceData.fps || 30,
      free_render_index_mode_on: sourceData.free_render_index_mode_on !== false,
      group_container: sourceData.group_container || null,
      id: sourceData.id || projectId,
      is_drop_frame_timecode: sourceData.is_drop_frame_timecode || false,
      keyframe_graph_list: sourceData.keyframe_graph_list || [],
      keyframes: {
        adjusts: [],
        audios: [],
        effects: [],
        filters: [],
        handwrites: [],
        stickers: [],
        texts: [],
        videos: []
      },
      lyrics_effects: sourceData.lyrics_effects || [],
      materials: {
        ai_translates: [],
        audio_balances: [],
        audio_effects: [],
        audio_fades: [],
        audio_track_indexes: [],
        audios: finalAudios,
        beats: [],
        canvases: [],
        chromas: [],
        color_curves: [],
        masks: [],
        digital_humans: [],
        drafts: [],
        effects: [],
        flowers: [],
        green_screens: [],
        handwrites: [],
        hsl: [],
        images: [],
        log_color_wheels: [],
        loudnesses: [],
        manual_deformations: [],
        material_animations: [],
        material_colors: [],
        multi_language_refs: [],
        placeholder_infos: [],
        placeholders: [],
        plugin_effects: [],
        primary_color_wheels: [],
        realtime_denoises: [],
        shapes: [],
        smart_crops: [],
        smart_relights: [],
        sound_channel_mappings: [],
        speeds: [],
        stickers: [],
        tail_leaders: [],
        text_templates: [],
        texts: [],
        time_marks: [],
        transitions: [],
        video_effects: [],
        video_trackings: [],
        videos: [],
        vocal_beautifys: [],
        vocal_separations: []
      },
      mutable_config: sourceData.mutable_config || null,
      name: sourceData.name || "",
      new_version: sourceData.new_version || "110.0.0",
      path: sourceData.path || "",
      relationships: sourceData.relationships || [],
      render_index_track_mode_on: sourceData.render_index_track_mode_on !== false,
      retouch_cover: sourceData.retouch_cover || null,
      source: sourceData.source || "default",
      static_cover_image_path: sourceData.static_cover_image_path || "",
      time_marks: sourceData.time_marks || null,
      tracks: cleanTracks,
      update_time: sourceData.update_time || 0,
      version: sourceData.version || 360000,
      platform: {
        app_id: sourceData.platform?.app_id || 3704,
        app_source: sourceData.platform?.app_source || "lv",
        app_version: sourceData.platform?.app_version || "5.9.0",
        device_id: sourceData.platform?.device_id || "9d624d7e23dc4e43a0ed4020b8e3e90e",
        hard_disk_id: sourceData.platform?.hard_disk_id || "f242a67014ec01ecdc2a9280da37adb1",
        mac_address: sourceData.platform?.mac_address || "0c233adaa327abd93e3d6b5dae6d75e2",
        os: sourceData.platform?.os || "mac",
        os_version: sourceData.platform?.os_version || "14.5"
      },
      last_modified_platform: {
        app_id: sourceData.last_modified_platform?.app_id || 3704,
        app_source: "cc",
        app_version: sourceData.last_modified_platform?.app_version || "5.9.0",
        device_id: sourceData.last_modified_platform?.device_id || "9d624d7e23dc4e43a0ed4020b8e3e90e",
        hard_disk_id: sourceData.last_modified_platform?.hard_disk_id || "f242a67014ec01ecdc2a9280da37adb1",
        mac_address: sourceData.last_modified_platform?.mac_address || "0c233adaa327abd93e3d6b5dae6d75e2",
        os: sourceData.last_modified_platform?.os || "mac",
        os_version: sourceData.last_modified_platform?.os_version || "14.5"
      }
    }

    // 第五步：生成所有JSON文件
    const jsonContent = JSON.stringify(finalJson)

    await fs.promises.writeFile(path.join(projectDir, 'draft_content.json'), jsonContent, 'utf8')
    await fs.promises.writeFile(path.join(projectDir, 'draft_info.json'), jsonContent, 'utf8')

    // 查找素材文件夹并生成对应的JSON文件
    const items = await fs.promises.readdir(projectDir)
    const materialFolders = items.filter(item => {
      const itemPath = path.join(projectDir, item)
      try {
        return fs.statSync(itemPath).isDirectory() &&
               item.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i)
      } catch (e) {
        return false
      }
    })

    if (materialFolders.length > 0) {
      const materialFolderName = materialFolders[0]
      await fs.promises.writeFile(path.join(projectDir, `${materialFolderName}.json`), jsonContent, 'utf8')
      console.log(`生成素材JSON文件: ${materialFolderName}.json`)
    }

    // 第六步：生成 draft_meta_info.json
    const currentTime = Date.now() * 1000
    const metaInfo = {
      cloud_package_completed_time: "",
      draft_cloud_capcut_purchase_info: "",
      draft_cloud_last_action_download: false,
      draft_cloud_materials: [],
      draft_cloud_purchase_info: "",
      draft_cloud_template_id: "",
      draft_cloud_tutorial_info: "",
      draft_cloud_videocut_purchase_info: "",
      draft_cover: "draft_cover.jpg",
      draft_deeplink_url: "",
      draft_enterprise_info: {
        draft_enterprise_extra: "",
        draft_enterprise_id: "",
        draft_enterprise_name: "",
        enterprise_material: []
      },
      draft_fold_path: `C:/Users/<USER>/AppData/Local/JianyingPro/User Data/Projects/com.lveditor.draft/${projectId}`,
      draft_id: sourceData.id || projectId,
      draft_is_ai_packaging_used: false,
      draft_is_ai_shorts: false,
      draft_is_ai_translate: false,
      draft_is_article_video_draft: false,
      draft_is_from_deeplink: "false",
      draft_is_invisible: false,
      draft_materials: [
        {type: 0, value: []},
        {type: 1, value: []},
        {type: 2, value: []},
        {type: 3, value: []},
        {type: 6, value: []},
        {type: 7, value: []},
        {type: 8, value: []}
      ],
      draft_materials_copied_info: [],
      draft_name: projectId,
      draft_new_version: "",
      draft_removable_storage_device: "",
      draft_root_path: "C:\\Users\\<USER>\\AppData\\Local\\JianyingPro\\User Data\\Projects\\com.lveditor.draft",
      draft_segment_extra_info: [],
      draft_timeline_materials_size_: 2075,
      draft_type: "",
      tm_draft_cloud_completed: "",
      tm_draft_cloud_modified: 0,
      tm_draft_create: currentTime,
      tm_draft_modified: currentTime,
      tm_draft_removed: 0,
      tm_duration: 0
    }

    await fs.promises.writeFile(path.join(projectDir, 'draft_meta_info.json'), JSON.stringify(metaInfo), 'utf8')

    // 第七步：直接使用竞争对手的完整JSON，只替换必要的ID和路径
    if (finalAudios.length > 0) {
      const audio = finalAudios[0]
      const audioTrack = cleanTracks.find(track => track.type === 'audio')
      const audioSegment = audioTrack?.segments?.[0]

      // 直接复制竞争对手的JSON结构，确保ID唯一
      const competitorJson = {
        "canvas_config": {"height": 1024, "ratio": "original", "width": 1024},
        "color_space": -1,
        "config": {
          "adjust_max_index": 1,
          "attachment_info": [],
          "combination_max_index": 1,
          "export_range": null,
          "extract_audio_last_index": 1,
          "lyrics_recognition_id": "",
          "lyrics_sync": true,
          "lyrics_taskinfo": [],
          "maintrack_adsorb": true,
          "material_save_mode": 0,
          "multi_language_current": "none",
          "multi_language_list": [],
          "multi_language_main": "none",
          "multi_language_mode": "none",
          "original_sound_last_index": 1,
          "record_audio_last_index": 1,
          "sticker_max_index": 1,
          "subtitle_keywords_config": null,
          "subtitle_recognition_id": "",
          "subtitle_sync": true,
          "subtitle_taskinfo": [],
          "system_font_list": [],
          "video_mute": false,
          "zoom_info_params": null
        },
        "cover": null,
        "create_time": 0,
        "duration": 10000000,
        "extra_info": null,
        "fps": 30,
        "free_render_index_mode_on": false,
        "group_container": null,
        "id": sourceData.id || projectId,
        "is_drop_frame_timecode": false,
        "keyframe_graph_list": [],
        "keyframes": {
          "adjusts": [],
          "audios": [],
          "effects": [],
          "filters": [],
          "handwrites": [],
          "stickers": [],
          "texts": [],
          "videos": []
        },
        "lyrics_effects": [],
        "materials": {
          "ai_translates": [],
          "audio_balances": [],
          "audio_effects": [],
          "audio_fades": [],
          "audio_track_indexes": [],
          "audios": [{
            "app_id": 0,
            "category_id": "",
            "category_name": "local",
            "check_flag": 1,
            "copyright_limit_type": "none",
            "duration": 14088000,
            "effect_id": "",
            "formula_id": "",
            "id": audio.id,
            "intensifies_path": "",
            "is_ai_clone_tone": false,
            "is_text_edit_overdub": false,
            "is_ugc": false,
            "local_material_id": "",
            "music_id": "",
            "name": audio.name,
            "path": audio.path,
            "query": "",
            "request_id": "",
            "resource_id": "",
            "search_id": "",
            "source_from": "",
            "source_platform": 0,
            "team_id": "",
            "text_id": "",
            "tone_category_id": "",
            "tone_category_name": "",
            "tone_effect_id": "",
            "tone_effect_name": "",
            "tone_platform": "",
            "tone_second_category_id": "",
            "tone_second_category_name": "",
            "tone_speaker": "",
            "tone_type": "",
            "type": "extract_music",
            "video_id": "",
            "wave_points": []
          }],
          "beats": [],
          "canvases": [],
          "chromas": [],
          "color_curves": [],
          "masks": [],
          "digital_humans": [],
          "drafts": [],
          "effects": [],
          "flowers": [],
          "green_screens": [],
          "handwrites": [],
          "hsl": [],
          "images": [],
          "log_color_wheels": [],
          "loudnesses": [],
          "manual_deformations": [],
          "material_animations": [],
          "material_colors": [],
          "multi_language_refs": [],
          "placeholder_infos": [],
          "placeholders": [],
          "plugin_effects": [],
          "primary_color_wheels": [],
          "realtime_denoises": [],
          "shapes": [],
          "smart_crops": [],
          "smart_relights": [],
          "sound_channel_mappings": [],
          "speeds": [],
          "stickers": [],
          "tail_leaders": [],
          "text_templates": [],
          "texts": [],
          "time_marks": [],
          "transitions": [],
          "video_effects": [],
          "video_trackings": [],
          "videos": [],
          "vocal_beautifys": [],
          "vocal_separations": []
        },
        "mutable_config": null,
        "name": "",
        "new_version": "110.0.0",
        "path": "",
        "relationships": [],
        "render_index_track_mode_on": true,
        "retouch_cover": null,
        "source": "default",
        "static_cover_image_path": "",
        "time_marks": null,
        "tracks": [
          {
            "attribute": 0,
            "flag": 0,
            "id": "2D0629D7-BC55-4A90-867A-C078CFEEE0D2",
            "segments": [],
            "type": "video"
          },
          {
            "attribute": 0,
            "flag": 3,
            "id": "8a92a22c-4fac-4f4e-b748-8883d235ca77",
            "segments": [{
              "caption_info": null,
              "cartoon": false,
              "clip": {
                "alpha": 1,
                "flip": {"horizontal": false, "vertical": false},
                "rotation": 0,
                "scale": {"x": 1, "y": 1},
                "transform": {"x": 0, "y": 0}
              },
              "common_keyframes": [],
              "enable_adjust": true,
              "enable_color_correct_adjust": false,
              "enable_color_curves": true,
              "enable_color_match_adjust": false,
              "enable_color_wheels": true,
              "enable_lut": true,
              "enable_smart_color_adjust": false,
              "extra_material_refs": [],
              "group_id": "",
              "hdr_settings": {"intensity": 1, "mode": 1, "nits": 1000},
              "id": "8a32a7d9-e8e2-4adb-ae50-f44a62e86262",
              "intensifies_audio": false,
              "is_placeholder": false,
              "is_tone_modify": false,
              "keyframe_refs": [],
              "last_nonzero_volume": 1,
              "material_id": audio.id,
              "render_index": 1,
              "responsive_layout": {
                "enable": false,
                "horizontal_pos_layout": 0,
                "size_layout": 0,
                "target_follow": "",
                "vertical_pos_layout": 0
              },
              "reverse": false,
              "source_timerange": {"duration": 14088000, "start": 0},
              "speed": 1,
              "target_timerange": {"duration": 14088000, "start": 0},
              "template_id": "",
              "template_scene": "default",
              "track_attribute": 0,
              "track_render_index": 3,
              "uniform_scale": {"on": true, "value": 1},
              "visible": true,
              "volume": 1
            }],
            "type": "audio"
          },
          {
            "attribute": 0,
            "flag": 3,
            "id": "a6721a71-b310-45ca-b011-1e3ee9db0af3",
            "segments": [],
            "type": "video"
          },
          {
            "attribute": 0,
            "flag": 3,
            "id": "cd0327f5-cf9e-4204-bb82-825069eb6638",
            "segments": [],
            "type": "text"
          }
        ],
        "update_time": 0,
        "version": 360000,
        "platform": {
          "app_id": 3704,
          "app_source": "lv",
          "app_version": "5.9.0",
          "device_id": "9d624d7e23dc4e43a0ed4020b8e3e90e",
          "hard_disk_id": "f242a67014ec01ecdc2a9280da37adb1",
          "mac_address": "0c233adaa327abd93e3d6b5dae6d75e2",
          "os": "mac",
          "os_version": "14.5"
        },
        "last_modified_platform": {
          "app_id": 3704,
          "app_source": "cc",
          "app_version": "5.9.0",
          "device_id": "9d624d7e23dc4e43a0ed4020b8e3e90e",
          "hard_disk_id": "f242a67014ec01ecdc2a9280da37adb1",
          "mac_address": "0c233adaa327abd93e3d6b5dae6d75e2",
          "os": "mac",
          "os_version": "14.5"
        }
      }

      // 生成竞争对手格式的测试文件
      await fs.promises.writeFile(path.join(projectDir, 'draft_content_competitor_format.json'), JSON.stringify(competitorJson), 'utf8')
      console.log('已生成竞争对手格式的测试文件（修复ID重复问题）')
    }

    console.log('=== 草稿文件修复完成 ===')

  } catch (error) {
    console.error('更新最终草稿文件失败:', error)
    throw error
  }
}

// 创建基础草稿文件

// 创建基础草稿文件
async function createBaseDraftFiles(projectDir, projectId, draftJson) {
  try {
    console.log('创建基础草稿文件')

    // 直接使用原始JSON创建基础文件
    const draftContent = JSON.stringify(draftJson)

    // 创建基础的JSON文件
    await fs.promises.writeFile(path.join(projectDir, 'draft_content.json'), draftContent, 'utf8')
    await fs.promises.writeFile(path.join(projectDir, 'draft_info.json'), draftContent, 'utf8')

    // 创建其他必要的配置文件（匹配竞争对手格式）
    const currentTime = Date.now() * 1000 // 转换为微秒
    const metaInfo = {
      cloud_package_completed_time: "",
      draft_cloud_capcut_purchase_info: "",
      draft_cloud_last_action_download: false,
      draft_cloud_materials: [],
      draft_cloud_purchase_info: "",
      draft_cloud_template_id: "",
      draft_cloud_tutorial_info: "",
      draft_cloud_videocut_purchase_info: "",
      draft_cover: "draft_cover.jpg",
      draft_deeplink_url: "",
      draft_enterprise_info: {
        draft_enterprise_extra: "",
        draft_enterprise_id: "",
        draft_enterprise_name: "",
        enterprise_material: []
      },
      draft_fold_path: `C:/Users/<USER>/AppData/Local/JianyingPro/User Data/Projects/com.lveditor.draft/${projectId}`,
      draft_id: projectId,
      draft_is_ai_packaging_used: false,
      draft_is_ai_shorts: false,
      draft_is_ai_translate: false,
      draft_is_article_video_draft: false,
      draft_is_from_deeplink: "false",
      draft_is_invisible: false,
      draft_materials: [
        {type: 0, value: []},
        {type: 1, value: []},
        {type: 2, value: []},
        {type: 3, value: []},
        {type: 6, value: []},
        {type: 7, value: []},
        {type: 8, value: []}
      ],
      draft_materials_copied_info: [],
      draft_name: projectId,
      draft_new_version: "",
      draft_removable_storage_device: "",
      draft_root_path: "C:\\Users\\<USER>\\AppData\\Local\\JianyingPro\\User Data\\Projects\\com.lveditor.draft",
      draft_segment_extra_info: [],
      draft_timeline_materials_size_: 2075,
      draft_type: "",
      tm_draft_cloud_completed: "",
      tm_draft_cloud_modified: 0,
      tm_draft_create: currentTime,
      tm_draft_modified: currentTime,
      tm_draft_removed: 0,
      tm_duration: 0
    }
    await fs.promises.writeFile(path.join(projectDir, 'draft_meta_info.json'), JSON.stringify(metaInfo), 'utf8')

    const agencyConfig = {
      marterials: null,
      use_converter: false,
      video_resolution: 720
    }
    await fs.promises.writeFile(path.join(projectDir, 'draft_agency_config.json'), JSON.stringify(agencyConfig), 'utf8')

    const attachmentConfig = {
      pc_feature_flag: 0,
      template_item_infos: [],
      unlock_template_ids: []
    }
    await fs.promises.writeFile(path.join(projectDir, 'attachment_pc_common.json'), JSON.stringify(attachmentConfig), 'utf8')

    const template = {
      canvas_config: { height: 0, ratio: "original", width: 0 },
      color_space: -1,
      config: {
        adjust_max_index: 1,
        attachment_info: [],
        combination_max_index: 1,
        export_range: null,
        extract_audio_last_index: 1,
        lyrics_recognition_id: "",
        lyrics_sync: true,
        lyrics_taskinfo: [],
        maintrack_adsorb: true,
        material_save_mode: 0,
        original_sound_last_index: 1,
        record_audio_last_index: 1,
        sticker_max_index: 1,
        subtitle_recognition_id: "",
        subtitle_sync: true,
        subtitle_taskinfo: [],
        video_mute: false,
        zoom_info_params: null
      },
      cover: null,
      create_time: 0,
      duration: 0,
      extra_info: null,
      fps: 30,
      free_render_index_mode_on: false,
      group_container: null,
      id: projectId,
      keyframes: {
        adjusts: [],
        audios: [],
        effects: [],
        filters: [],
        handwrites: [],
        stickers: [],
        texts: [],
        videos: []
      },
      last_modified_platform: {
        app_id: 3704,
        app_source: "lv",
        app_version: "3.9.0",
        device_id: "ece93fefa6d73d0879f6cf6251e212fd",
        hard_disk_id: "da04071d52c1f66c291bc2f5b1f0e87f",
        mac_address: "864b94efa06c9456b10992695eba19c4",
        os: "mac",
        os_version: "12.5.1"
      },
      materials: {
        audio_balances: [],
        audio_effects: [],
        audio_fades: [],
        audios: [],
        beats: [],
        canvases: [],
        chromas: [],
        color_curves: [],
        drafts: [],
        effects: [],
        handwrites: [],
        hsl: [],
        images: [],
        log_color_wheels: [],
        manual_deformations: [],
        masks: [],
        material_animations: [],
        placeholders: [],
        plugin_effects: [],
        primary_color_wheels: [],
        realtime_denoises: [],
        sound_channel_mappings: [],
        speeds: [],
        stickers: [],
        tail_leaders: [],
        text_templates: [],
        texts: [],
        transitions: [],
        video_effects: [],
        video_trackings: [],
        videos: []
      },
      mutable_config: null,
      name: "",
      new_version: "69.0.0",
      platform: {
        app_id: 3704,
        app_source: "lv",
        app_version: "3.9.0",
        device_id: "ece93fefa6d73d0879f6cf6251e212fd",
        hard_disk_id: "da04071d52c1f66c291bc2f5b1f0e87f",
        mac_address: "864b94efa06c9456b10992695eba19c4",
        os: "mac",
        os_version: "12.5.1"
      },
      relationships: [],
      render_index_track_mode_on: false,
      retouch_cover: null,
      source: "default",
      static_cover_image_path: "",
      tracks: [],
      update_time: 0,
      version: 360000
    }
    await fs.promises.writeFile(path.join(projectDir, 'template.tmp'), JSON.stringify(template), 'utf8')

    // 创建使用说明文件
    await fs.promises.writeFile(path.join(projectDir, '把当前文件夹放到剪映草稿目录下'), '', 'utf8')

    console.log('基础草稿文件创建完成')

  } catch (error) {
    console.error('创建基础草稿文件失败:', error)
    throw error
  }
}

// 处理素材文件（下载音频、视频、图片等）
async function processMaterialFiles(projectDir, draftJson) {
  try {
    console.log('开始处理素材文件')
    console.log('草稿JSON结构:', JSON.stringify(draftJson, null, 2))

    // 检查是否有素材需要下载
    const materials = draftJson.materials
    console.log('素材信息:', materials)

    if (!materials) {
      console.log('没有素材需要处理')
      mainWindow.webContents.send('import-progress', '没有素材需要处理')
      return
    }

    // 处理音频素材（根据download_url去重）
    if (materials.audios && materials.audios.length > 0) {
      console.log(`原始音频素材数量: ${materials.audios.length}`)

      // 去重：根据download_url字段去重，保留最后一个
      const urlMap = new Map()
      materials.audios.forEach(audio => {
        if (audio.download_url) {
          urlMap.set(audio.download_url, audio) // 后面的会覆盖前面的
        }
      })

      const uniqueAudios = Array.from(urlMap.values())
      console.log(`去重后音频素材数量: ${uniqueAudios.length}`)

      for (const audio of uniqueAudios) {
        await processSingleMaterial(projectDir, audio, 'audio')
      }
    }

    // 处理视频素材
    if (materials.videos && materials.videos.length > 0) {
      for (const video of materials.videos) {
        await processSingleMaterial(projectDir, video, 'video')
      }
    }

    // 处理图片素材
    if (materials.images && materials.images.length > 0) {
      for (const image of materials.images) {
        await processSingleMaterial(projectDir, image, 'image')
      }
    }

    console.log('素材文件处理完成')

  } catch (error) {
    console.error('处理素材文件失败:', error)
    throw error
  }
}

// 处理单个素材文件
async function processSingleMaterial(projectDir, material, type) {
  try {
    console.log(`处理${type}素材:`, material.id)

    // 从素材路径中提取文件夹ID和文件名
    const materialPath = material.path
    if (!materialPath || !materialPath.includes('\\')) {
      console.log('素材路径格式不正确，跳过:', materialPath)
      return
    }

    // 解析路径：##_draftpath_placeholder_{UUID}_##\materialId\filename
    const pathParts = materialPath.split('\\')
    if (pathParts.length < 3) {
      console.log('素材路径格式不正确，跳过:', materialPath)
      return
    }

    const materialId = pathParts[1]  // JSON中的素材ID
    const fileName = pathParts[2]    // JSON中的文件名

    console.log(`使用JSON中的素材ID: ${materialId}`)
    console.log(`使用JSON中的文件名: ${fileName}`)

    // 创建素材文件夹（使用改进的方法）
    const materialDir = path.join(projectDir, materialId)
    createDraftFolderWithCorrectProperties(materialDir)
    console.log(`创建素材文件夹: ${materialDir}`)

    // 注意：素材ID命名的JSON文件将在 updateFinalDraftFiles 中生成，这里不重复生成

    // 下载实际的音频文件
    console.log(`检查${type}素材:`, material)
    console.log(`${type}素材download_url:`, material.download_url)

    if (material.download_url) {
      console.log(`开始下载${type}文件:`, material.download_url)
      mainWindow.webContents.send('import-progress', `正在下载${type}文件...`)

      // 使用从path中提取的文件名，确保路径一致
      const audioFilePath = path.join(materialDir, fileName)

      console.log(`${type}文件保存路径:`, audioFilePath)
      console.log(`使用从path提取的文件名: ${fileName}`)

      try {
        await downloadFile(material.download_url, audioFilePath)
        console.log(`${type}文件下载成功:`, audioFilePath)
        mainWindow.webContents.send('import-progress', `${type}文件下载完成`)
      } catch (downloadError) {
        console.error(`${type}文件下载失败:`, downloadError)
        mainWindow.webContents.send('import-progress', `${type}文件下载失败: ${downloadError.message}`)
        // 下载失败不影响整体流程，继续执行
      }
    } else {
      console.log(`${type}素材没有下载URL，跳过文件下载`)
      mainWindow.webContents.send('import-progress', `${type}素材没有下载URL，跳过文件下载`)
    }

    console.log(`${type}素材处理完成:`, materialId)

  } catch (error) {
    console.error(`处理${type}素材失败:`, error)
    throw error
  }
}

// 下载文件
async function downloadFile(url, filePath) {
  return new Promise((resolve, reject) => {
    console.log('开始下载文件:', url, '到', filePath)

    const protocol = url.startsWith('https:') ? https : require('http')
    const file = fs.createWriteStream(filePath)

    protocol.get(url, (response) => {
      if (response.statusCode !== 200) {
        file.close()
        fs.unlink(filePath, () => {}) // 删除不完整的文件
        reject(new Error(`下载失败，状态码: ${response.statusCode}`))
        return
      }

      response.pipe(file)

      file.on('finish', () => {
        file.close()
        console.log('文件下载完成:', filePath)
        resolve()
      })

      file.on('error', (error) => {
        file.close()
        fs.unlink(filePath, () => {}) // 删除不完整的文件
        reject(error)
      })

    }).on('error', (error) => {
      file.close()
      fs.unlink(filePath, () => {}) // 删除不完整的文件
      reject(error)
    })
  })
}
