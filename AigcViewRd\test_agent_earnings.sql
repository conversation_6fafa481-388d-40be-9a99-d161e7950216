-- 🎯 智能体收益分配功能测试脚本
-- 头儿，这个脚本用于验证收益分配功能是否正常工作

-- 1. 查看测试前的数据状态
SELECT '=== 测试前数据状态 ===' as info;

-- 查看智能体信息
SELECT 
    a.id,
    a.agent_name,
    a.sales_count,
    a.create_by,
    a.price,
    a.audit_status
FROM aigc_agent a 
WHERE a.id = '*****************03'  -- 编程导师
LIMIT 1;

-- 查看创作者收益
SELECT 
    u.username,
    p.agent_earnings,
    p.account_balance
FROM sys_user u 
LEFT JOIN aicg_user_profile p ON u.id = p.user_id 
WHERE u.username = 'admin';

-- 2. 模拟购买记录（手动测试用）
-- 注意：这个INSERT语句仅用于测试，实际购买应该通过API接口

/*
INSERT INTO aicg_user_agent_purchase (
    id, user_id, agent_id, transaction_id, purchase_price, original_price, 
    discount_rate, user_role_at_purchase, purchase_time, agent_name, 
    agent_description, author_type, author_name, status, create_by, create_time
) VALUES (
    'test_purchase_001',
    'e9ca23d68d884d4ebb19d07889727dae',  -- admin用户ID
    '*****************03',  -- 编程导师ID
    'TXN_TEST_001',
    49.00,  -- 购买价格
    49.00,  -- 原价
    100,    -- 折扣率
    '普通用户',
    NOW(),
    '编程导师',
    '专业的编程指导',
    '1',    -- 官方智能体
    'admin',
    1,
    'test_user',
    NOW()
);
*/

-- 3. 手动执行收益分配逻辑（模拟API调用）
-- 更新智能体销售次数
UPDATE aigc_agent 
SET sales_count = IFNULL(sales_count, 0) + 1 
WHERE id = '*****************03';

-- 更新创作者收益（假设购买价格为49元）
UPDATE aicg_user_profile 
SET agent_earnings = IFNULL(agent_earnings, 0) + 49.00 
WHERE user_id = 'e9ca23d68d884d4ebb19d07889727dae';

-- 4. 查看测试后的数据状态
SELECT '=== 测试后数据状态 ===' as info;

-- 查看智能体销售次数是否增加
SELECT 
    a.id,
    a.agent_name,
    a.sales_count,
    a.create_by,
    a.price
FROM aigc_agent a 
WHERE a.id = '*****************03';

-- 查看创作者收益是否增加
SELECT 
    u.username,
    p.agent_earnings,
    p.account_balance
FROM sys_user u 
LEFT JOIN aicg_user_profile p ON u.id = p.user_id 
WHERE u.username = 'admin';

-- 5. 查看购买记录
SELECT 
    p.id,
    p.agent_name,
    p.purchase_price,
    p.original_price,
    p.author_name,
    p.purchase_time
FROM aicg_user_agent_purchase p 
WHERE p.agent_id = '*****************03'
ORDER BY p.purchase_time DESC
LIMIT 5;

-- 6. 收益分配验证查询
SELECT 
    '收益分配验证' as check_type,
    a.agent_name,
    a.sales_count as '销售次数',
    a.create_by as '创作者',
    p.agent_earnings as '创作者收益',
    (SELECT COUNT(*) FROM aicg_user_agent_purchase WHERE agent_id = a.id) as '购买记录数'
FROM aigc_agent a
LEFT JOIN sys_user u ON a.create_by = u.username
LEFT JOIN aicg_user_profile p ON u.id = p.user_id
WHERE a.id = '*****************03';

-- 7. 恢复测试数据（可选）
/*
-- 如果需要重置测试数据，可以执行以下语句：
UPDATE aigc_agent SET sales_count = 0 WHERE id = '*****************03';
UPDATE aicg_user_profile SET agent_earnings = 0.00 WHERE user_id = 'e9ca23d68d884d4ebb19d07889727dae';
DELETE FROM aicg_user_agent_purchase WHERE id = 'test_purchase_001';
*/
