{"version": 3, "file": "install-app-deps.js", "sourceRoot": "", "sources": ["../../src/cli/install-app-deps.ts"], "names": [], "mappings": ";;;;AAEA,kFAAiF;AACjF,4DAAuF;AACvF,wDAAgE;AAChE,yDAA6D;AAC7D,sFAAuF;AACvF,+CAAwD;AACxD,sDAA4D;AAC5D,uCAAmC;AACnC,uCAA+B;AAC/B,6BAA4B;AAC5B,uDAAuD;AACvD,+BAA8B;AAE9B,gBAAgB;AAChB,SAAgB,8BAA8B,CAAC,KAAiB;IAC9D,4CAA4C;IAC5C,qCAAqC;IACrC,OAAO,KAAK;SACT,mBAAmB,CAAC;QACnB,sBAAsB,EAAE,KAAK;KAC9B,CAAC;SACD,MAAM,CAAC,UAAU,EAAE;QAClB,OAAO,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,OAAO,CAAC;QACrC,OAAO,EAAE,OAAO,CAAC,QAAQ;QACzB,WAAW,EAAE,qBAAqB;KACnC,CAAC;SACD,MAAM,CAAC,MAAM,EAAE;QACd,OAAO,EAAE,IAAA,8BAAe,GAAE,CAAC,MAAM,CAAC,KAAK,CAAC;QACxC,OAAO,EAAE,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI;QACzD,WAAW,EAAE,iBAAiB;KAC/B,CAAC,CAAA;AACN,CAAC;AAjBD,wEAiBC;AAED,gBAAgB;AACT,KAAK,UAAU,cAAc,CAAC,IAAS;IAC5C,IAAI,CAAC;QACH,kBAAG,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,yBAAe,EAAE,EAAE,kBAAkB,CAAC,CAAA;IAC5D,CAAC;IAAC,OAAO,CAAM,EAAE,CAAC;QAChB,kCAAkC;QAClC,IAAI,CAAC,CAAC,CAAC,YAAY,cAAc,CAAC,EAAE,CAAC;YACnC,MAAM,CAAC,CAAA;QACT,CAAC;IACH,CAAC;IAED,MAAM,UAAU,GAAG,OAAO,CAAC,GAAG,EAAE,CAAA;IAChC,MAAM,eAAe,GAAG,IAAI,eAAI,CAAC,GAAG,EAAE,CAAC,IAAA,uCAAoB,EAAC,IAAA,mBAAQ,EAAC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,cAAc,CAAC,CAAC,CAAC,CAAC,CAAA;IAC7G,MAAM,MAAM,GAAG,MAAM,IAAA,kBAAS,EAAC,UAAU,EAAE,IAAI,EAAE,IAAI,EAAE,eAAe,CAAC,CAAA;IACvE,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAS;QAClD,IAAA,mCAA0B,EACxB,UAAU,EACV,IAAA,kBAAG,EAAC,MAAM,CAAC,WAAW,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,CACtC;QACD,IAAA,oCAAkB,EAAC,UAAU,EAAE,MAAM,CAAC;KACvC,CAAC,CAAA;IAEF,sGAAsG;IACtG,MAAM,IAAA,uBAAgB,EACpB,MAAM,EACN,MAAM,EACN;QACE,aAAa,EAAE,EAAE,OAAO,EAAE,aAAa,EAAE,IAAI,EAAE;QAC/C,QAAQ,EAAE,IAAI,CAAC,QAAQ;QACvB,IAAI,EAAE,IAAI,CAAC,IAAI;QACf,cAAc,EAAE,IAAA,8CAAwB,EAAC,MAAM,EAAE,IAAI,CAAC;KACvD,EACD,MAAM,KAAK,UAAU,CACtB,CAAA;AACH,CAAC;AAjCD,wCAiCC;AAED,SAAS,IAAI;IACX,OAAO,cAAc,CAAC,8BAA8B,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAA;AACnE,CAAC;AAED,IAAI,OAAO,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;IAC5B,kBAAG,CAAC,IAAI,CAAC,6DAA6D,CAAC,CAAA;IACvE,IAAI,EAAE,CAAC,KAAK,CAAC,2BAAiB,CAAC,CAAA;AACjC,CAAC", "sourcesContent": ["#! /usr/bin/env node\n\nimport { getElectronVersion } from \"app-builder-lib/out/electron/electronVersion\"\nimport { computeDefaultAppDirectory, getConfig } from \"app-builder-lib/out/util/config\"\nimport { installOrRebuild } from \"app-builder-lib/out/util/yarn\"\nimport { PACKAGE_VERSION } from \"app-builder-lib/out/version\"\nimport { createLazyProductionDeps } from \"app-builder-lib/out/util/packageDependencies\"\nimport { getArchCliNames, log, use } from \"builder-util\"\nimport { printErrorAndExit } from \"builder-util/out/promise\"\nimport { readJson } from \"fs-extra\"\nimport { Lazy } from \"lazy-val\"\nimport * as path from \"path\"\nimport { orNullIfFileNotExist } from \"read-config-file\"\nimport * as yargs from \"yargs\"\n\n/** @internal */\nexport function configureInstallAppDepsCommand(yargs: yargs.Argv): yargs.Argv {\n  // https://github.com/yargs/yargs/issues/760\n  // demandOption is required to be set\n  return yargs\n    .parserConfiguration({\n      \"camel-case-expansion\": false,\n    })\n    .option(\"platform\", {\n      choices: [\"linux\", \"darwin\", \"win32\"],\n      default: process.platform,\n      description: \"The target platform\",\n    })\n    .option(\"arch\", {\n      choices: getArchCliNames().concat(\"all\"),\n      default: process.arch === \"arm\" ? \"armv7l\" : process.arch,\n      description: \"The target arch\",\n    })\n}\n\n/** @internal */\nexport async function installAppDeps(args: any) {\n  try {\n    log.info({ version: PACKAGE_VERSION }, \"electron-builder\")\n  } catch (e: any) {\n    // error in dev mode without babel\n    if (!(e instanceof ReferenceError)) {\n      throw e\n    }\n  }\n\n  const projectDir = process.cwd()\n  const packageMetadata = new Lazy(() => orNullIfFileNotExist(readJson(path.join(projectDir, \"package.json\"))))\n  const config = await getConfig(projectDir, null, null, packageMetadata)\n  const [appDir, version] = await Promise.all<string>([\n    computeDefaultAppDirectory(\n      projectDir,\n      use(config.directories, it => it.app)\n    ),\n    getElectronVersion(projectDir, config),\n  ])\n\n  // if two package.json — force full install (user wants to install/update app deps in addition to dev)\n  await installOrRebuild(\n    config,\n    appDir,\n    {\n      frameworkInfo: { version, useCustomDist: true },\n      platform: args.platform,\n      arch: args.arch,\n      productionDeps: createLazyProductionDeps(appDir, null),\n    },\n    appDir !== projectDir\n  )\n}\n\nfunction main() {\n  return installAppDeps(configureInstallAppDepsCommand(yargs).argv)\n}\n\nif (require.main === module) {\n  log.warn(\"please use as subcommand: electron-builder install-app-deps\")\n  main().catch(printErrorAndExit)\n}\n"]}