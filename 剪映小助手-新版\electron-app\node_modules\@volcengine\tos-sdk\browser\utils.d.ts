/// <reference types="node" />
import { CamelCasedPropertiesDeep, KebabCasedPropertiesDeep, PascalCasedPropertiesDeep } from 'type-fest';
import { Headers } from './interface';
import { TOSConstructorOptions, TosResponse } from './methods/base';
import TosServerError from './TosServerError';
import { CRCCls } from './universal/crc';
import { ReadStream, WriteStream } from 'fs';
export declare const makeArrayProp: (obj: unknown) => (key: string) => void;
export declare const covertCamelCase2Kebab: <T = unknown>(target: T) => import("type-fest").DelimiterCasedPropertiesDeep<T, "-">;
export declare const convertUpperCamelCase2Normal: <T = unknown>(target: T) => CamelCasedPropertiesDeep<T>;
export declare const convertNormalCamelCase2Upper: <T = unknown>(target: T) => PascalCasedPropertiesDeep<T>;
export declare const getSortedQueryString: (query: Record<string, any>) => string;
export declare const normalizeHeadersKey: <T extends Headers>(headers: T | undefined) => T;
export declare const encodeHeadersValue: (headers: Headers) => Headers;
export declare const getRegion: (endpoint: string) => string;
export declare const getEndpoint: (region: string) => string;
export declare const normalizeProxy: (proxy: TOSConstructorOptions['proxy']) => {
    url: string;
    needProxyParams?: boolean | undefined;
} | undefined;
export declare function safeAwait<T>(p: T): Promise<[null, Awaited<T>] | [any, null]>;
export declare function safeSync<T>(func: () => T): [any, null] | [null, T];
export declare function isBlob(obj: unknown): obj is Blob;
export declare function isBuffer(obj: unknown): obj is Buffer;
export declare function isReadable(obj: unknown): obj is NodeJS.ReadableStream;
export declare function isValidNumber(v: number): v is number;
export declare function obj2QueryStr(v?: Record<string, unknown>): string;
export declare function isCancelError(err: any): boolean;
export declare const DEFAULT_PART_SIZE: number;
export declare const getGMTDateStr: (v: Date) => string;
export declare const requestHeadersMap: Record<string, string | [string, (v: any) => string] | ((v: any) => Record<string, string>)>;
export declare const requestQueryMap: Record<string, string | [string, (v: any) => string] | ((v: any) => Record<string, string>)>;
export declare function fillRequestHeaders<T extends {
    headers?: Headers;
}>(v: T, keys: (keyof T & string)[]): void;
export declare function fillRequestQuery<T>(v: T, query: Record<string, unknown>, keys: (keyof T & string)[]): void;
export declare const paramsSerializer: (params: Record<string, string>) => string;
export declare function getNormalDataFromError<T>(data: T, err: TosServerError): TosResponse<T>;
export declare const streamToBuf: (stream: NodeJS.ReadableStream) => Promise<Buffer>;
export declare function checkCRC64WithHeaders(crc: CRCCls | string, headers: Headers): void;
export declare const makeStreamErrorHandler: (prefix?: string | undefined) => (err: any) => void;
export declare enum HttpHeader {
    LastModified = "last-modified",
    ContentLength = "content-length",
    AcceptEncoding = "accept-encoding",
    ContentEncoding = "content-encoding",
    ContentMD5 = "content-md5",
    TosRawContentLength = "x-tos-raw-content-length",
    TosTrailer = "x-tos-trailer",
    TosHashCrc64ecma = "x-tos-hash-crc64ecma",
    TosContentSha256 = "x-tos-content-sha256",
    TosDecodedContentLength = "x-tos-decoded-content-length",
    TosEc = "x-tos-ec",
    TosRequestId = "x-tos-request-id"
}
/**
 * make async tasks serial
 * @param makeTask
 * @returns
 */
export declare const makeSerialAsyncTask: (makeTask: () => Promise<void>) => () => Promise<void>;
export declare const safeParseCheckpointFile: (filePath: string) => Promise<any>;
export declare const makeRetryStreamAutoClose: (makeStream: () => NodeJS.ReadableStream | ReadStream) => {
    getLastStream: () => NodeJS.ReadableStream | ReadStream | null;
    make: () => NodeJS.ReadableStream | ReadStream;
};
export declare const tryDestroy: (stream: NodeJS.ReadableStream | ReadStream | NodeJS.WritableStream | WriteStream | null | undefined, err: any) => void;
export declare const pipeStreamWithErrorHandle: <Src extends NodeJS.ReadableStream | ReadStream, Dest extends NodeJS.WritableStream | WriteStream>(src: Src, dest: Dest, label: string) => Dest;
