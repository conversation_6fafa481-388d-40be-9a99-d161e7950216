{"version": 3, "file": "normalizePackageData.js", "sourceRoot": "", "sources": ["../../src/util/normalizePackageData.ts"], "names": [], "mappings": ";;;AAAA,iCAAgC;AAChC,qDAAyC;AACzC,2BAA0B;AAE1B,SAAgB,oBAAoB,CAAC,IAAS;IAC5C,KAAK,MAAM,EAAE,IAAI,KAAK,EAAE,CAAC;QACvB,EAAE,CAAC,IAAI,CAAC,CAAA;IACV,CAAC;AACH,CAAC;AAJD,oDAIC;AAED,MAAM,QAAQ,GAAG,CAAC,cAAc,EAAE,iBAAiB,EAAE,sBAAsB,CAAC,CAAA;AAC5E,MAAM,KAAK,GAAG;IACZ,UAAU,IAAS;QACjB,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACtB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAA;QACxC,CAAC;QACD,IAAI,OAAO,IAAI,CAAC,UAAU,KAAK,QAAQ,EAAE,CAAC;YACxC,IAAI,CAAC,UAAU,GAAG;gBAChB,IAAI,EAAE,KAAK;gBACX,GAAG,EAAE,IAAI,CAAC,UAAU;aACrB,CAAA;QACH,CAAC;QACD,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,EAAE,CAAC;YACnD,MAAM,MAAM,GAAG,IAAA,yBAAO,EAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAA;YAC3C,IAAI,MAAM,EAAE,CAAC;gBACX,IAAI,CAAC,UAAU,CAAC,GAAG,GAAG,MAAM,CAAC,wBAAwB,EAAE,IAAI,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAA;YAC5G,CAAC;QACH,CAAC;IACH,CAAC;IACD,UAAU,IAAS;QACjB,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAA;QACxB,IAAI,KAAK,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;YACnC,OAAO,IAAI,CAAC,KAAK,CAAA;QACnB,CAAC;aAAM,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;YACtB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,UAAU,IAAS;gBAChD,OAAO,CAAC,CAAC,CAAC,IAAI,IAAI,OAAO,IAAI,KAAK,QAAQ,CAAC,CAAA;YAC7C,CAAC,CAAC,CAAA;QACJ,CAAC;IACH,CAAC;IACD,UAAU,IAAS;QACjB,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC;YACd,OAAM;QACR,CAAC;QACD,IAAI,OAAO,IAAI,CAAC,GAAG,KAAK,QAAQ,EAAE,CAAC;YACjC,MAAM,CAAC,GAAQ,EAAE,CAAA;YACjB,MAAM,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAA;YAChD,IAAI,KAAK,EAAE,CAAC;gBACV,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAA;YACxB,CAAC;iBAAM,CAAC;gBACN,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,CAAA;YACzB,CAAC;YACD,IAAI,CAAC,GAAG,GAAG,CAAC,CAAA;QACd,CAAC;IACH,CAAC;IACD,UAAU,IAAS;QACjB,IAAI,IAAI,CAAC,WAAW,IAAI,OAAO,IAAI,CAAC,WAAW,KAAK,QAAQ,EAAE,CAAC;YAC7D,OAAO,IAAI,CAAC,WAAW,CAAA;QACzB,CAAC;QACD,IAAI,IAAI,CAAC,WAAW,KAAK,SAAS,EAAE,CAAC;YACnC,OAAO,IAAI,CAAC,WAAW,CAAA;QACzB,CAAC;IACH,CAAC;IACD,0BAA0B;IAC1B,SAAS,eAAe,CAAC,IAAS;QAChC,aAAa,CAAC,IAAI,CAAC,CAAA;QACnB,0BAA0B,CAAC,IAAI,CAAC,CAAA;QAEhC,KAAK,MAAM,IAAI,IAAI,CAAC,cAAc,EAAE,iBAAiB,EAAE,sBAAsB,CAAC,EAAE,CAAC;YAC/E,IAAI,CAAC,CAAC,IAAI,IAAI,IAAI,CAAC,EAAE,CAAC;gBACpB,SAAQ;YACV,CAAC;YACD,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC,KAAK,QAAQ,EAAE,CAAC;gBAClD,OAAO,IAAI,CAAC,IAAI,CAAC,CAAA;gBACjB,SAAQ;YACV,CAAC;YACD,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC;gBACzC,MAAM,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAA;gBACvB,IAAI,OAAO,CAAC,KAAK,QAAQ,EAAE,CAAC;oBAC1B,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAA;gBACtB,CAAC;gBACD,MAAM,MAAM,GAAG,IAAA,yBAAO,EAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;gBACrC,IAAI,MAAM,EAAE,CAAC;oBACX,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAA;gBACnC,CAAC;YACH,CAAC,CAAC,CAAA;QACJ,CAAC;IACH,CAAC;IACD,SAAS,YAAY,CAAC,IAAS;QAC7B,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,EAAE,CAAC;YACzD,MAAM,MAAM,GAAG,IAAA,yBAAO,EAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAA;YAC3C,IAAI,MAAM,IAAI,MAAM,CAAC,IAAI,EAAE,EAAE,CAAC;gBAC5B,IAAI,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,MAAM,CAAC,IAAI,EAAE,EAAE,CAAA;YACpC,CAAC;QACH,CAAC;aAAM,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;YACrB,MAAM,OAAO,GAAG,aAAa,CAAA;YAC7B,IAAI,OAAO,IAAI,CAAC,IAAI,IAAI,QAAQ,EAAE,CAAC;gBACjC,IAAI,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;oBAC5B,IAAI,CAAC,IAAI,GAAG,EAAE,KAAK,EAAE,IAAI,CAAC,IAAI,EAAE,CAAA;gBAClC,CAAC;qBAAM,IAAI,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,QAAQ,EAAE,CAAC;oBACzC,IAAI,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,IAAI,EAAE,CAAA;gBAChC,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;gBACpB,MAAM,OAAO,GAAG,IAAI,CAAC,IAAI,CAAA;gBACzB,IAAI,CAAC,IAAI,GAAG,EAAE,CAAA;gBACd,IAAI,OAAO,CAAC,GAAG,EAAE,CAAC;oBAChB,IAAI,OAAO,OAAO,CAAC,GAAG,IAAI,QAAQ,IAAI,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,CAAC;wBACtE,IAAI,CAAC,IAAI,CAAC,GAAG,GAAG,OAAO,CAAC,GAAG,CAAA;oBAC7B,CAAC;gBACH,CAAC;gBACD,IAAI,OAAO,CAAC,KAAK,EAAE,CAAC;oBAClB,IAAI,OAAO,OAAO,CAAC,KAAK,IAAI,QAAQ,IAAI,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;wBACpE,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAA;oBACjC,CAAC;gBACH,CAAC;YACH,CAAC;YACD,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC;gBACvC,OAAO,IAAI,CAAC,IAAI,CAAA;YAClB,CAAC;QACH,CAAC;IACH,CAAC;IACD,SAAS,eAAe,CAAC,IAAS;QAChC,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACjB,OAAO,IAAI,CAAC,OAAO,CAAA;QACrB,CAAC;IACH,CAAC;IACD,SAAS,gBAAgB,CAAC,IAAS;QACjC,IAAI,OAAO,IAAI,CAAC,QAAQ,KAAK,QAAQ,EAAE,CAAC;YACtC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,CAAA;QAC7C,CAAC;QACD,IAAI,IAAI,CAAC,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;YACnD,OAAO,IAAI,CAAC,QAAQ,CAAA;QACtB,CAAC;aAAM,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YACzB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,UAAU,EAAO;gBACpD,OAAO,CAAC,CAAC,OAAO,EAAE,KAAK,QAAQ,IAAI,CAAC,EAAE,CAAC,CAAA;YACzC,CAAC,CAAC,CAAA;QACJ,CAAC;IACH,CAAC;IACD,SAAS,eAAe,CAAC,IAAS;QAChC,MAAM,KAAK,GAAG,IAAI,CAAA;QAClB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;YAClB,IAAI,CAAC,OAAO,GAAG,EAAE,CAAA;YACjB,OAAO,IAAI,CAAA;QACb,CAAC;QACD,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,EAAE,CAAC;YACvC,MAAM,IAAI,KAAK,CAAC,qBAAqB,IAAI,CAAC,OAAO,GAAG,CAAC,CAAA;QACvD,CAAC;QACD,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,CAAA;QAChD,OAAO,IAAI,CAAA;IACb,CAAC;IACD,SAAS,SAAS,CAAC,IAAS;QAC1B,YAAY,CAAC,IAAI,EAAE,aAAa,CAAC,CAAA;QACjC,YAAY,CAAC,IAAI,EAAE,WAAW,CAAC,CAAA;IACjC,CAAC;IACD,SAAS,YAAY,CAAC,IAAS;QAC7B,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;YACf,IAAI,CAAC,IAAI,GAAG,EAAE,CAAA;YACd,OAAM;QACR,CAAC;QACD,IAAI,OAAO,IAAI,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;YAClC,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAA;QACjD,CAAC;QACD,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAA;QAC5B,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;IAC5B,CAAC;IACD,SAAS,gBAAgB,CAAC,IAAS;QACjC,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,EAAE,CAAC;YAC7D,MAAM,MAAM,GAAG,IAAA,yBAAO,EAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAA;YAC3C,IAAI,MAAM,IAAI,MAAM,CAAC,IAAI,EAAE,EAAE,CAAC;gBAC5B,IAAI,CAAC,QAAQ,GAAG,MAAM,CAAC,IAAI,EAAE,CAAA;YAC/B,CAAC;QACH,CAAC;QACD,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACnB,OAAM;QACR,CAAC;QAED,IAAI,OAAO,IAAI,CAAC,QAAQ,KAAK,QAAQ,EAAE,CAAC;YACtC,OAAO,IAAI,CAAC,QAAQ,CAAA;QACtB,CAAC;QACD,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,QAAQ,EAAE,CAAC;YACvC,IAAI,CAAC,QAAQ,GAAG,WAAW,IAAI,CAAC,QAAQ,EAAE,CAAA;QAC5C,CAAC;QACD,OAAM;IACR,CAAC;CACF,CAAA;AAED,SAAS,0BAA0B,CAAC,IAAS;IAC3C,MAAM,GAAG,GAAG,qBAAqB,CAAA;IACjC,MAAM,EAAE,GAAG,oBAAoB,CAAA;IAC/B,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC;QAC3B,IAAI,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAA;QACpB,OAAO,IAAI,CAAC,GAAG,CAAC,CAAA;IAClB,CAAC;IACD,IAAI,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;QACzC,OAAO,IAAI,CAAC,EAAE,CAAC,CAAA;IACjB,CAAC;SAAM,IAAI,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC;QACpB,IAAI,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,UAAU,EAAO;YAC1C,IAAI,CAAC,EAAE,IAAI,OAAO,EAAE,KAAK,QAAQ,EAAE,CAAC;gBAClC,OAAO,KAAK,CAAA;YACd,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC;oBACvB,IAAI,CAAC,YAAY,GAAG,EAAE,CAAA;gBACxB,CAAC;gBACD,IAAI,CAAC,CAAC,IAAI,IAAI,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC;oBACjC,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,GAAG,GAAG,CAAA;gBAC7B,CAAC;gBACD,OAAO,IAAI,CAAA;YACb,CAAC;QACH,CAAC,CAAC,CAAA;IACJ,CAAC;AACH,CAAC;AAED,SAAS,wBAAwB,CAAC,IAAY;IAC5C,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE,CAAC;QAC3B,OAAO,KAAK,CAAA;IACd,CAAC;IAED,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;IACrC,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QACtB,OAAO,KAAK,CAAA;IACd,CAAC;IAED,OAAO,IAAI,CAAC,CAAC,CAAC,KAAK,EAAE,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,EAAE,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,IAAI,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,IAAI,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,kBAAkB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,kBAAkB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAA;AACrK,CAAC;AAED,SAAS,sBAAsB,CAAC,IAAY;IAC1C,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,KAAK,kBAAkB,CAAC,IAAI,CAAC,CAAA;AACrE,CAAC;AAED,SAAS,eAAe,CAAC,IAAY;IACnC,IACE,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG;QACtB,CAAC,CAAC,wBAAwB,CAAC,IAAI,CAAC,IAAI,sBAAsB,CAAC,IAAI,CAAC,CAAC;QACjE,IAAI,CAAC,WAAW,EAAE,KAAK,cAAc;QACrC,IAAI,CAAC,WAAW,EAAE,KAAK,aAAa,EACpC,CAAC;QACD,MAAM,IAAI,KAAK,CAAC,gBAAgB,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAA;IAC1D,CAAC;AACH,CAAC;AAED,SAAS,YAAY,CAAC,IAAS,EAAE,EAAO;IACtC,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;QAChB,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;IAC/B,CAAC;IACD,KAAK,MAAM,GAAG,IAAI,CAAC,aAAa,EAAE,cAAc,CAAC,EAAE,CAAC;QAClD,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC;YAC9B,SAAQ;QACV,CAAC;QACD,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAA;IAC/B,CAAC;IACD,OAAO,IAAI,CAAA;AACb,CAAC;AAED,SAAS,aAAa,CAAC,MAAW;IAChC,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE,CAAC;QAC/B,OAAO,MAAM,CAAA;IACf,CAAC;IACD,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,IAAI,EAAE,CAAA;IAC9B,MAAM,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI,MAAM,CAAC,GAAG,CAAA;IAClC,MAAM,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAA;IAC9B,MAAM,CAAC,GAAG,MAAM,CAAC,KAAK,IAAI,MAAM,CAAC,IAAI,CAAA;IACrC,MAAM,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAA;IAChC,OAAO,GAAG,IAAI,GAAG,KAAK,GAAG,GAAG,EAAE,CAAA;AAChC,CAAC;AAED,SAAS,WAAW,CAAC,MAAW;IAC9B,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE,CAAC;QAC/B,OAAO,MAAM,CAAA;IACf,CAAC;IACD,MAAM,IAAI,GAAG,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;IACrC,MAAM,GAAG,GAAG,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;IACtC,MAAM,KAAK,GAAG,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;IACtC,MAAM,GAAG,GAAQ,EAAE,CAAA;IACnB,IAAI,IAAI,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC;QAC3B,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAA;IAC3B,CAAC;IACD,IAAI,KAAK,EAAE,CAAC;QACV,GAAG,CAAC,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,CAAA;IACtB,CAAC;IACD,IAAI,GAAG,EAAE,CAAC;QACR,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC,CAAA;IAClB,CAAC;IACD,OAAO,GAAG,CAAA;AACZ,CAAC;AAED,SAAS,YAAY,CAAC,IAAS;IAC7B,IAAI,CAAC,IAAI,EAAE,CAAC;QACV,OAAO,EAAE,CAAA;IACX,CAAC;IACD,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE,CAAC;QAC7B,IAAI,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,eAAe,CAAC,CAAA;IAC3C,CAAC;IACD,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;QACzB,OAAO,IAAI,CAAA;IACb,CAAC;IACD,MAAM,CAAC,GAAQ,EAAE,CAAA;IACjB,IAAI;SACD,MAAM,CAAC,UAAU,CAAC;QACjB,OAAO,OAAO,CAAC,KAAK,QAAQ,CAAA;IAC9B,CAAC,CAAC;SACD,OAAO,CAAC,UAAU,CAAC;QAClB,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,cAAc,CAAC,CAAA;QAClC,MAAM,EAAE,GAAG,CAAC,CAAC,KAAK,EAAE,CAAA;QACpB,IAAI,EAAE,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;QACnB,EAAE,GAAG,EAAE,CAAC,IAAI,EAAE,CAAA;QACd,EAAE,GAAG,EAAE,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAAA;QACzB,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,CAAA;IACZ,CAAC,CAAC,CAAA;IACJ,OAAO,CAAC,CAAA;AACV,CAAC;AAED,SAAS,aAAa,CAAC,IAAS;IAC9B,QAAQ,CAAC,OAAO,CAAC,UAAU,IAAI;QAC7B,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;YAChB,OAAM;QACR,CAAC;QACD,IAAI,CAAC,IAAI,CAAC,GAAG,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAA;IACvC,CAAC,CAAC,CAAA;AACJ,CAAC;AAED,MAAM,QAAQ,GAAQ,EAAE,GAAG,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,CAAA;AAEjD,SAAS,SAAS,CAAC,IAAS;IAC1B,IAAI,CAAC,IAAI,EAAE,CAAC;QACV,OAAM;IACR,CAAC;IACD,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC;QACnC,IAAI,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC;YAChB,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAA;YAC3B,OAAO,IAAI,CAAC,CAAC,CAAC,CAAA;QAChB,CAAC;IACH,CAAC,CAAC,CAAA;AACJ,CAAC", "sourcesContent": ["import * as semver from \"semver\"\nimport { fromUrl } from \"hosted-git-info\"\nimport * as url from \"url\"\n\nexport function normalizePackageData(data: any) {\n  for (const it of check) {\n    it(data)\n  }\n}\n\nconst depTypes = [\"dependencies\", \"devDependencies\", \"optionalDependencies\"]\nconst check = [\n  function (data: any) {\n    if (data.repositories) {\n      data.repository = data.repositories[0]\n    }\n    if (typeof data.repository === \"string\") {\n      data.repository = {\n        type: \"git\",\n        url: data.repository,\n      }\n    }\n    if (data.repository != null && data.repository.url) {\n      const hosted = fromUrl(data.repository.url)\n      if (hosted) {\n        data.repository.url = hosted.getDefaultRepresentation() == \"shortcut\" ? hosted.https() : hosted.toString()\n      }\n    }\n  },\n  function (data: any) {\n    const files = data.files\n    if (files && !Array.isArray(files)) {\n      delete data.files\n    } else if (data.files) {\n      data.files = data.files.filter(function (file: any) {\n        return !(!file || typeof file !== \"string\")\n      })\n    }\n  },\n  function (data: any) {\n    if (!data.bin) {\n      return\n    }\n    if (typeof data.bin === \"string\") {\n      const b: any = {}\n      const match = data.name.match(/^@[^/]+[/](.*)$/)\n      if (match) {\n        b[match[1]] = data.bin\n      } else {\n        b[data.name] = data.bin\n      }\n      data.bin = b\n    }\n  },\n  function (data: any) {\n    if (data.description && typeof data.description !== \"string\") {\n      delete data.description\n    }\n    if (data.description === undefined) {\n      delete data.description\n    }\n  },\n  fixBundleDependenciesField,\n  function fixDependencies(data: any) {\n    objectifyDeps(data)\n    fixBundleDependenciesField(data)\n\n    for (const deps of [\"dependencies\", \"devDependencies\", \"optionalDependencies\"]) {\n      if (!(deps in data)) {\n        continue\n      }\n      if (!data[deps] || typeof data[deps] !== \"object\") {\n        delete data[deps]\n        continue\n      }\n      Object.keys(data[deps]).forEach(function (d) {\n        const r = data[deps][d]\n        if (typeof r !== \"string\") {\n          delete data[deps][d]\n        }\n        const hosted = fromUrl(data[deps][d])\n        if (hosted) {\n          data[deps][d] = hosted.toString()\n        }\n      })\n    }\n  },\n  function fixBugsField(data: any) {\n    if (!data.bugs && data.repository && data.repository.url) {\n      const hosted = fromUrl(data.repository.url)\n      if (hosted && hosted.bugs()) {\n        data.bugs = { url: hosted.bugs() }\n      }\n    } else if (data.bugs) {\n      const emailRe = /^.+@.*\\..+$/\n      if (typeof data.bugs == \"string\") {\n        if (emailRe.test(data.bugs)) {\n          data.bugs = { email: data.bugs }\n        } else if (url.parse(data.bugs).protocol) {\n          data.bugs = { url: data.bugs }\n        }\n      } else {\n        bugsTypos(data.bugs)\n        const oldBugs = data.bugs\n        data.bugs = {}\n        if (oldBugs.url) {\n          if (typeof oldBugs.url == \"string\" && url.parse(oldBugs.url).protocol) {\n            data.bugs.url = oldBugs.url\n          }\n        }\n        if (oldBugs.email) {\n          if (typeof oldBugs.email == \"string\" && emailRe.test(oldBugs.email)) {\n            data.bugs.email = oldBugs.email\n          }\n        }\n      }\n      if (!data.bugs.email && !data.bugs.url) {\n        delete data.bugs\n      }\n    }\n  },\n  function fixModulesField(data: any) {\n    if (data.modules) {\n      delete data.modules\n    }\n  },\n  function fixKeywordsField(data: any) {\n    if (typeof data.keywords === \"string\") {\n      data.keywords = data.keywords.split(/,\\s+/)\n    }\n    if (data.keywords && !Array.isArray(data.keywords)) {\n      delete data.keywords\n    } else if (data.keywords) {\n      data.keywords = data.keywords.filter(function (kw: any) {\n        return !(typeof kw !== \"string\" || !kw)\n      })\n    }\n  },\n  function fixVersionField(data: any) {\n    const loose = true\n    if (!data.version) {\n      data.version = \"\"\n      return true\n    }\n    if (!semver.valid(data.version, loose)) {\n      throw new Error(`Invalid version: \"${data.version}\"`)\n    }\n    data.version = semver.clean(data.version, loose)\n    return true\n  },\n  function fixPeople(data: any) {\n    modifyPeople(data, unParsePerson)\n    modifyPeople(data, parsePerson)\n  },\n  function fixNameField(data: any) {\n    if (!data.name) {\n      data.name = \"\"\n      return\n    }\n    if (typeof data.name !== \"string\") {\n      throw new Error(\"name field must be a string.\")\n    }\n    data.name = data.name.trim()\n    ensureValidName(data.name)\n  },\n  function fixHomepageField(data: any) {\n    if (!data.homepage && data.repository && data.repository.url) {\n      const hosted = fromUrl(data.repository.url)\n      if (hosted && hosted.docs()) {\n        data.homepage = hosted.docs()\n      }\n    }\n    if (!data.homepage) {\n      return\n    }\n\n    if (typeof data.homepage !== \"string\") {\n      delete data.homepage\n    }\n    if (!url.parse(data.homepage).protocol) {\n      data.homepage = `https://${data.homepage}`\n    }\n    return\n  },\n]\n\nfunction fixBundleDependenciesField(data: any) {\n  const bdd = \"bundledDependencies\"\n  const bd = \"bundleDependencies\"\n  if (data[bdd] && !data[bd]) {\n    data[bd] = data[bdd]\n    delete data[bdd]\n  }\n  if (data[bd] && !Array.isArray(data[bd])) {\n    delete data[bd]\n  } else if (data[bd]) {\n    data[bd] = data[bd].filter(function (bd: any) {\n      if (!bd || typeof bd !== \"string\") {\n        return false\n      } else {\n        if (!data.dependencies) {\n          data.dependencies = {}\n        }\n        if (!(\"bd\" in data.dependencies)) {\n          data.dependencies[bd] = \"*\"\n        }\n        return true\n      }\n    })\n  }\n}\n\nfunction isValidScopedPackageName(spec: string): boolean {\n  if (spec.charAt(0) !== \"@\") {\n    return false\n  }\n\n  const rest = spec.slice(1).split(\"/\")\n  if (rest.length !== 2) {\n    return false\n  }\n\n  return rest[0] !== \"\" && rest[1] !== \"\" && rest[0] != null && rest[1] != null && rest[0] === encodeURIComponent(rest[0]) && rest[1] === encodeURIComponent(rest[1])\n}\n\nfunction isCorrectlyEncodedName(spec: string): boolean {\n  return !/[/@\\s+%:]/.test(spec) && spec === encodeURIComponent(spec)\n}\n\nfunction ensureValidName(name: string): void {\n  if (\n    name.charAt(0) === \".\" ||\n    !(isValidScopedPackageName(name) || isCorrectlyEncodedName(name)) ||\n    name.toLowerCase() === \"node_modules\" ||\n    name.toLowerCase() === \"favicon.ico\"\n  ) {\n    throw new Error(\"Invalid name: \" + JSON.stringify(name))\n  }\n}\n\nfunction modifyPeople(data: any, fn: any): any {\n  if (data.author) {\n    data.author = fn(data.author)\n  }\n  for (const set of [\"maintainers\", \"contributors\"]) {\n    if (!Array.isArray(data[set])) {\n      continue\n    }\n    data[set] = data[set].map(fn)\n  }\n  return data\n}\n\nfunction unParsePerson(person: any): string {\n  if (typeof person === \"string\") {\n    return person\n  }\n  const name = person.name || \"\"\n  const u = person.url || person.web\n  const url = u ? ` (${u})` : \"\"\n  const e = person.email || person.mail\n  const email = e ? ` <${e}>` : \"\"\n  return `${name}${email}${url}`\n}\n\nfunction parsePerson(person: any) {\n  if (typeof person !== \"string\") {\n    return person\n  }\n  const name = /^([^(<]+)/.exec(person)\n  const url = /\\(([^)]+)\\)/.exec(person)\n  const email = /<([^>]+)>/.exec(person)\n  const obj: any = {}\n  if (name && name[0].trim()) {\n    obj.name = name[0].trim()\n  }\n  if (email) {\n    obj.email = email[1]\n  }\n  if (url) {\n    obj.url = url[1]\n  }\n  return obj\n}\n\nfunction depObjectify(deps: any): any {\n  if (!deps) {\n    return {}\n  }\n  if (typeof deps === \"string\") {\n    deps = deps.trim().split(/[\\n\\r\\s\\t ,]+/)\n  }\n  if (!Array.isArray(deps)) {\n    return deps\n  }\n  const o: any = {}\n  deps\n    .filter(function (d) {\n      return typeof d === \"string\"\n    })\n    .forEach(function (d) {\n      d = d.trim().split(/(:?[@\\s><=])/)\n      const dn = d.shift()\n      let dv = d.join(\"\")\n      dv = dv.trim()\n      dv = dv.replace(/^@/, \"\")\n      o[dn] = dv\n    })\n  return o\n}\n\nfunction objectifyDeps(data: any) {\n  depTypes.forEach(function (type) {\n    if (!data[type]) {\n      return\n    }\n    data[type] = depObjectify(data[type])\n  })\n}\n\nconst typoBugs: any = { web: \"url\", name: \"url\" }\n\nfunction bugsTypos(bugs: any) {\n  if (!bugs) {\n    return\n  }\n  Object.keys(bugs).forEach(function (k) {\n    if (typoBugs[k]) {\n      bugs[typoBugs[k]] = bugs[k]\n      delete bugs[k]\n    }\n  })\n}\n"]}