# 防刷机制具体实现方案

## 🛡️ 防刷机制总体架构

### 📊 三层防护体系
```
第一层：前端采集 → 设备指纹、IP地址、行为数据
第二层：实时检测 → 规则引擎、风险评分、异常识别
第三层：人工审核 → 高风险订单、异常账户、申诉处理
```

---

## 🔍 技术层面防护实现

### 1. **设备指纹识别系统**

#### 前端设备指纹采集
```javascript
// 设备指纹生成器
class DeviceFingerprintGenerator {
    constructor() {
        this.fingerprint = null;
    }
    
    async generateFingerprint() {
        const components = {
            // 基础信息
            userAgent: navigator.userAgent,
            language: navigator.language,
            platform: navigator.platform,
            cookieEnabled: navigator.cookieEnabled,
            
            // 屏幕信息
            screenResolution: `${screen.width}x${screen.height}`,
            colorDepth: screen.colorDepth,
            pixelRatio: window.devicePixelRatio,
            
            // 时区和地理
            timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
            timezoneOffset: new Date().getTimezoneOffset(),
            
            // Canvas指纹
            canvasFingerprint: this.generateCanvasFingerprint(),
            
            // WebGL指纹
            webglFingerprint: this.generateWebGLFingerprint(),
            
            // 音频指纹
            audioFingerprint: await this.generateAudioFingerprint(),
            
            // 字体检测
            fonts: this.detectFonts(),
            
            // 插件信息
            plugins: this.getPluginInfo(),
            
            // 硬件信息
            hardwareConcurrency: navigator.hardwareConcurrency,
            deviceMemory: navigator.deviceMemory || 0,
            
            // 网络信息
            connection: this.getConnectionInfo()
        };
        
        // 生成指纹哈希
        this.fingerprint = await this.hashComponents(components);
        return this.fingerprint;
    }
    
    generateCanvasFingerprint() {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        
        // 绘制复杂图形
        ctx.textBaseline = 'top';
        ctx.font = '14px Arial';
        ctx.fillStyle = '#f60';
        ctx.fillRect(125, 1, 62, 20);
        ctx.fillStyle = '#069';
        ctx.fillText('AigcView Device Check 🔒', 2, 15);
        ctx.fillStyle = 'rgba(102, 204, 0, 0.7)';
        ctx.fillText('AigcView Device Check 🔒', 4, 17);
        
        return canvas.toDataURL();
    }
    
    generateWebGLFingerprint() {
        const canvas = document.createElement('canvas');
        const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
        
        if (!gl) return null;
        
        const debugInfo = gl.getExtension('WEBGL_debug_renderer_info');
        return {
            vendor: gl.getParameter(debugInfo.UNMASKED_VENDOR_WEBGL),
            renderer: gl.getParameter(debugInfo.UNMASKED_RENDERER_WEBGL),
            version: gl.getParameter(gl.VERSION),
            shadingLanguageVersion: gl.getParameter(gl.SHADING_LANGUAGE_VERSION)
        };
    }
    
    async generateAudioFingerprint() {
        return new Promise((resolve) => {
            try {
                const audioContext = new (window.AudioContext || window.webkitAudioContext)();
                const oscillator = audioContext.createOscillator();
                const analyser = audioContext.createAnalyser();
                const gainNode = audioContext.createGain();
                
                oscillator.type = 'triangle';
                oscillator.frequency.setValueAtTime(10000, audioContext.currentTime);
                
                gainNode.gain.setValueAtTime(0, audioContext.currentTime);
                oscillator.connect(analyser);
                analyser.connect(gainNode);
                gainNode.connect(audioContext.destination);
                
                oscillator.start(0);
                
                setTimeout(() => {
                    const frequencyData = new Uint8Array(analyser.frequencyBinCount);
                    analyser.getByteFrequencyData(frequencyData);
                    
                    oscillator.stop();
                    audioContext.close();
                    
                    const fingerprint = Array.from(frequencyData).slice(0, 30).join(',');
                    resolve(fingerprint);
                }, 100);
            } catch (e) {
                resolve(null);
            }
        });
    }
    
    async hashComponents(components) {
        const jsonString = JSON.stringify(components, Object.keys(components).sort());
        const encoder = new TextEncoder();
        const data = encoder.encode(jsonString);
        const hashBuffer = await crypto.subtle.digest('SHA-256', data);
        const hashArray = Array.from(new Uint8Array(hashBuffer));
        return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
    }
}

// 使用示例
const fingerprintGenerator = new DeviceFingerprintGenerator();
const deviceFingerprint = await fingerprintGenerator.generateFingerprint();
```

#### 后端设备指纹分析
```java
@Service
public class DeviceFingerprintService {
    
    @Autowired
    private AicgDeviceFingerprintMapper deviceFingerprintMapper;
    
    /**
     * 分析设备指纹风险
     */
    public DeviceRiskResult analyzeDeviceRisk(String fingerprint, String userId) {
        DeviceRiskResult result = new DeviceRiskResult();
        
        // 1. 查询设备指纹记录
        AicgDeviceFingerprint deviceRecord = deviceFingerprintMapper.selectByFingerprint(fingerprint);
        
        if (deviceRecord == null) {
            // 新设备，创建记录
            deviceRecord = createNewDeviceRecord(fingerprint, userId);
            result.setRiskLevel(RiskLevel.LOW);
            result.setRiskScore(10);
        } else {
            // 已存在设备，检查关联用户数
            int userCount = deviceRecord.getUserCount();
            
            if (userCount == 1) {
                result.setRiskLevel(RiskLevel.LOW);
                result.setRiskScore(5);
            } else if (userCount <= 3) {
                result.setRiskLevel(RiskLevel.MEDIUM);
                result.setRiskScore(50);
                result.addRiskReason("设备关联多个用户账号");
            } else {
                result.setRiskLevel(RiskLevel.HIGH);
                result.setRiskScore(90);
                result.addRiskReason("设备关联用户数过多，疑似批量注册");
            }
            
            // 更新设备记录
            updateDeviceRecord(deviceRecord, userId);
        }
        
        return result;
    }
    
    /**
     * 检查设备指纹相似度
     */
    public List<SimilarDevice> findSimilarDevices(String fingerprint, double threshold) {
        // 使用编辑距离算法检查相似设备指纹
        List<AicgDeviceFingerprint> allDevices = deviceFingerprintMapper.selectAll();
        List<SimilarDevice> similarDevices = new ArrayList<>();
        
        for (AicgDeviceFingerprint device : allDevices) {
            double similarity = calculateSimilarity(fingerprint, device.getFingerprintHash());
            if (similarity >= threshold) {
                similarDevices.add(new SimilarDevice(device, similarity));
            }
        }
        
        return similarDevices;
    }
    
    private double calculateSimilarity(String fp1, String fp2) {
        // 使用Jaccard相似度算法
        Set<String> set1 = new HashSet<>(Arrays.asList(fp1.split("")));
        Set<String> set2 = new HashSet<>(Arrays.asList(fp2.split("")));
        
        Set<String> intersection = new HashSet<>(set1);
        intersection.retainAll(set2);
        
        Set<String> union = new HashSet<>(set1);
        union.addAll(set2);
        
        return (double) intersection.size() / union.size();
    }
}
```

### 2. **IP地址风险检测**

#### IP地址分析服务
```java
@Service
public class IPRiskAnalysisService {
    
    @Autowired
    private AicgIpAddressLogMapper ipAddressLogMapper;
    
    @Autowired
    private GeoLocationService geoLocationService;
    
    /**
     * 分析IP地址风险
     */
    public IPRiskResult analyzeIPRisk(String ipAddress, String userId) {
        IPRiskResult result = new IPRiskResult();
        
        // 1. 查询IP记录
        AicgIpAddressLog ipRecord = ipAddressLogMapper.selectByIpAddress(ipAddress);
        
        if (ipRecord == null) {
            // 新IP地址
            ipRecord = createNewIPRecord(ipAddress, userId);
            result.setRiskLevel(RiskLevel.LOW);
            result.setRiskScore(5);
        } else {
            // 检查今日注册数
            int todayRegisterCount = ipRecord.getRegisterCountToday();
            
            if (todayRegisterCount >= 5) {
                result.setRiskLevel(RiskLevel.HIGH);
                result.setRiskScore(80);
                result.addRiskReason("同一IP今日注册数过多");
            } else if (todayRegisterCount >= 3) {
                result.setRiskLevel(RiskLevel.MEDIUM);
                result.setRiskScore(40);
                result.addRiskReason("同一IP今日注册数较多");
            }
            
            // 检查用户总数
            int userCount = ipRecord.getUserCount();
            if (userCount >= 10) {
                result.setRiskLevel(RiskLevel.HIGH);
                result.setRiskScore(Math.max(result.getRiskScore(), 70));
                result.addRiskReason("同一IP关联用户数过多");
            }
            
            // 更新IP记录
            updateIPRecord(ipRecord, userId);
        }
        
        // 2. 地理位置检查
        GeoLocation geoInfo = geoLocationService.getGeoLocation(ipAddress);
        if (geoInfo != null) {
            result.setGeoLocation(geoInfo);
            
            // 检查是否为代理IP
            if (geoInfo.isProxy() || geoInfo.isVpn()) {
                result.setRiskLevel(RiskLevel.HIGH);
                result.setRiskScore(Math.max(result.getRiskScore(), 60));
                result.addRiskReason("使用代理或VPN");
            }
        }
        
        return result;
    }
    
    /**
     * 检查IP地址段风险
     */
    public boolean checkIPSegmentRisk(String ipAddress) {
        // 检查同一IP段的注册情况
        String ipSegment = getIPSegment(ipAddress); // 获取前三段IP
        
        QueryWrapper<AicgIpAddressLog> queryWrapper = new QueryWrapper<>();
        queryWrapper.likeRight("ip_address", ipSegment);
        queryWrapper.ge("create_time", LocalDateTime.now().minusDays(1));
        
        List<AicgIpAddressLog> segmentIPs = ipAddressLogMapper.selectList(queryWrapper);
        
        // 如果同一IP段24小时内注册用户超过20个，标记为高风险
        int totalUsers = segmentIPs.stream().mapToInt(AicgIpAddressLog::getUserCount).sum();
        return totalUsers > 20;
    }
}
```

### 3. **实名认证关联检测**

#### 实名认证风险检查
```java
@Service
public class RealNameVerificationService {
    
    /**
     * 检查身份证号关联风险
     */
    public RealNameRiskResult checkIdCardRisk(String idCardNumber, String userId) {
        RealNameRiskResult result = new RealNameRiskResult();
        
        // 1. 检查身份证号是否已被其他用户使用
        QueryWrapper<AicgUserProfile> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("id_card_number", idCardNumber);
        queryWrapper.ne("user_id", userId);
        
        List<AicgUserProfile> existingUsers = userProfileMapper.selectList(queryWrapper);
        
        if (!existingUsers.isEmpty()) {
            result.setRiskLevel(RiskLevel.HIGH);
            result.setRiskScore(95);
            result.addRiskReason("身份证号已被其他用户使用");
            result.setConflictUserIds(existingUsers.stream()
                .map(AicgUserProfile::getUserId)
                .collect(Collectors.toList()));
            return result;
        }
        
        // 2. 身份证号格式和有效性检查
        if (!isValidIdCard(idCardNumber)) {
            result.setRiskLevel(RiskLevel.HIGH);
            result.setRiskScore(90);
            result.addRiskReason("身份证号格式无效");
            return result;
        }
        
        // 3. 年龄检查
        int age = calculateAge(idCardNumber);
        if (age < 18) {
            result.setRiskLevel(RiskLevel.MEDIUM);
            result.setRiskScore(30);
            result.addRiskReason("用户年龄未满18岁");
        } else if (age > 80) {
            result.setRiskLevel(RiskLevel.MEDIUM);
            result.setRiskScore(25);
            result.addRiskReason("用户年龄异常");
        }
        
        result.setAge(age);
        return result;
    }
    
    /**
     * 人脸识别验证
     */
    public FaceVerificationResult verifyFace(String userId, String faceImageBase64) {
        // 调用第三方人脸识别API
        // 这里使用腾讯云、阿里云或百度云的人脸识别服务
        
        FaceVerificationResult result = new FaceVerificationResult();
        
        try {
            // 1. 获取用户身份证照片（如果有）
            String idCardPhoto = getIdCardPhoto(userId);
            
            // 2. 调用人脸比对API
            FaceComparisonResponse response = faceRecognitionService.compareFaces(
                idCardPhoto, faceImageBase64);
            
            result.setSimilarity(response.getSimilarity());
            result.setConfidence(response.getConfidence());
            
            if (response.getSimilarity() >= 0.8) {
                result.setVerificationPassed(true);
                result.setRiskLevel(RiskLevel.LOW);
            } else if (response.getSimilarity() >= 0.6) {
                result.setVerificationPassed(false);
                result.setRiskLevel(RiskLevel.MEDIUM);
                result.addRiskReason("人脸相似度较低");
            } else {
                result.setVerificationPassed(false);
                result.setRiskLevel(RiskLevel.HIGH);
                result.addRiskReason("人脸验证失败");
            }
            
        } catch (Exception e) {
            result.setVerificationPassed(false);
            result.setRiskLevel(RiskLevel.HIGH);
            result.addRiskReason("人脸识别服务异常");
        }
        
        return result;
    }
}
```

---

## 🕵️ 业务层面防护实现

### 1. **异常行为检测引擎**

#### 行为模式分析
```java
@Service
public class BehaviorAnalysisService {
    
    /**
     * 分析用户注册行为模式
     */
    public BehaviorRiskResult analyzeRegistrationBehavior(RegistrationBehaviorDTO behavior) {
        BehaviorRiskResult result = new BehaviorRiskResult();
        int riskScore = 0;
        
        // 1. 注册时间模式检查
        LocalTime registerTime = behavior.getRegisterTime().toLocalTime();
        if (registerTime.isAfter(LocalTime.of(2, 0)) && registerTime.isBefore(LocalTime.of(6, 0))) {
            riskScore += 20;
            result.addRiskReason("深夜注册时间异常");
        }
        
        // 2. 页面停留时间检查
        long pageStayTime = behavior.getPageStayTime(); // 秒
        if (pageStayTime < 30) {
            riskScore += 30;
            result.addRiskReason("页面停留时间过短");
        } else if (pageStayTime > 3600) {
            riskScore += 15;
            result.addRiskReason("页面停留时间异常长");
        }
        
        // 3. 表单填写速度检查
        long formFillTime = behavior.getFormFillTime(); // 秒
        if (formFillTime < 10) {
            riskScore += 40;
            result.addRiskReason("表单填写速度异常快");
        }
        
        // 4. 鼠标轨迹检查
        if (behavior.getMouseMovements() < 5) {
            riskScore += 25;
            result.addRiskReason("鼠标活动异常少");
        }
        
        // 5. 键盘输入模式检查
        if (behavior.getKeyboardEvents() < 10) {
            riskScore += 20;
            result.addRiskReason("键盘输入异常少");
        }
        
        // 6. 浏览器特征检查
        if (behavior.isHeadlessBrowser()) {
            riskScore += 80;
            result.addRiskReason("检测到无头浏览器");
        }
        
        if (behavior.isAutomationTool()) {
            riskScore += 90;
            result.addRiskReason("检测到自动化工具");
        }
        
        result.setRiskScore(Math.min(riskScore, 100));
        result.setRiskLevel(getRiskLevel(riskScore));
        
        return result;
    }
    
    /**
     * 分析充值行为模式
     */
    public BehaviorRiskResult analyzeRechargeBehavior(String userId, RechargeInfo rechargeInfo) {
        BehaviorRiskResult result = new BehaviorRiskResult();
        int riskScore = 0;
        
        // 1. 注册到充值时间间隔检查
        LocalDateTime registerTime = getUserRegisterTime(userId);
        long minutesToRecharge = ChronoUnit.MINUTES.between(registerTime, rechargeInfo.getRechargeTime());
        
        if (minutesToRecharge < 5) {
            riskScore += 60;
            result.addRiskReason("注册后立即充值，时间间隔异常短");
        } else if (minutesToRecharge < 30) {
            riskScore += 30;
            result.addRiskReason("注册后快速充值");
        }
        
        // 2. 充值金额模式检查
        BigDecimal amount = rechargeInfo.getAmount();
        if (amount.compareTo(new BigDecimal("50")) == 0) {
            // 正好50元（最低充值要求），可能是为了触发推荐奖励
            riskScore += 25;
            result.addRiskReason("充值金额为最低要求金额");
        }
        
        // 3. 支付方式检查
        String paymentMethod = rechargeInfo.getPaymentMethod();
        if ("VIRTUAL_CARD".equals(paymentMethod)) {
            riskScore += 40;
            result.addRiskReason("使用虚拟卡支付");
        }
        
        // 4. 历史充值模式检查
        List<RechargeInfo> historyRecharges = getHistoryRecharges(userId);
        if (historyRecharges.size() == 1 && amount.compareTo(new BigDecimal("50")) == 0) {
            riskScore += 20;
            result.addRiskReason("仅有一次最低金额充值");
        }
        
        result.setRiskScore(Math.min(riskScore, 100));
        result.setRiskLevel(getRiskLevel(riskScore));
        
        return result;
    }
}
```

### 2. **规则引擎实现**

#### 风控规则配置
```java
@Component
public class RiskRuleEngine {
    
    private final List<RiskRule> rules = Arrays.asList(
        // 设备相关规则
        new RiskRule("DEVICE_MULTI_USER", "同设备多用户", 
            (context) -> context.getDeviceUserCount() > 3, 
            RiskLevel.HIGH, 70, "FREEZE_ACCOUNT"),
            
        // IP相关规则
        new RiskRule("IP_DAILY_REGISTER", "IP日注册数过多", 
            (context) -> context.getIpDailyRegisterCount() > 5, 
            RiskLevel.HIGH, 80, "BLOCK_IP"),
            
        // 行为相关规则
        new RiskRule("FAST_REGISTER_RECHARGE", "快速注册充值", 
            (context) -> context.getRegisterToRechargeMinutes() < 5, 
            RiskLevel.MEDIUM, 60, "MANUAL_REVIEW"),
            
        // 邀请相关规则
        new RiskRule("SAME_IP_INVITE", "同IP互相邀请", 
            (context) -> context.getReferrerIp().equals(context.getRefereeIp()), 
            RiskLevel.HIGH, 90, "REJECT_REFERRAL"),
            
        // 实名认证相关规则
        new RiskRule("DUPLICATE_ID_CARD", "身份证重复", 
            (context) -> context.getIdCardUserCount() > 1, 
            RiskLevel.HIGH, 95, "FREEZE_ACCOUNT")
    );
    
    /**
     * 执行风控检查
     */
    public RiskCheckResult executeRiskCheck(RiskCheckContext context) {
        RiskCheckResult result = new RiskCheckResult();
        List<RiskRuleResult> triggeredRules = new ArrayList<>();
        int totalRiskScore = 0;
        
        for (RiskRule rule : rules) {
            if (rule.evaluate(context)) {
                RiskRuleResult ruleResult = new RiskRuleResult();
                ruleResult.setRuleName(rule.getName());
                ruleResult.setRuleDescription(rule.getDescription());
                ruleResult.setRiskLevel(rule.getRiskLevel());
                ruleResult.setRiskScore(rule.getRiskScore());
                ruleResult.setAction(rule.getAction());
                ruleResult.setTriggerTime(LocalDateTime.now());
                
                triggeredRules.add(ruleResult);
                totalRiskScore += rule.getRiskScore();
                
                // 记录风控日志
                logRiskEvent(context.getUserId(), rule, context);
            }
        }
        
        result.setTriggeredRules(triggeredRules);
        result.setTotalRiskScore(Math.min(totalRiskScore, 100));
        result.setRiskLevel(getRiskLevel(totalRiskScore));
        result.setPassed(totalRiskScore < 50); // 风险评分低于50分通过
        
        return result;
    }
}
```

这个防刷机制方案提供了全方位的技术防护，您觉得还需要补充哪些方面？
