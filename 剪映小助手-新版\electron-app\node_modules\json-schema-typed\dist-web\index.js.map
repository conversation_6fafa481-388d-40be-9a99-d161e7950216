{"version": 3, "file": "index.js", "sources": ["../dist-src/index.js"], "sourcesContent": ["\"use strict\";\n\nexport let JSONSchemaFormat;\n\n(function (JSONSchemaFormat) {\n  JSONSchemaFormat[\"Date\"] = \"date\";\n  JSONSchemaFormat[\"DateTime\"] = \"date-time\";\n  JSONSchemaFormat[\"Email\"] = \"email\";\n  JSONSchemaFormat[\"Hostname\"] = \"hostname\";\n  JSONSchemaFormat[\"IDNEmail\"] = \"idn-email\";\n  JSONSchemaFormat[\"IDNHostname\"] = \"idn-hostname\";\n  JSONSchemaFormat[\"IPv4\"] = \"ipv4\";\n  JSONSchemaFormat[\"IPv6\"] = \"ipv6\";\n  JSONSchemaFormat[\"IRI\"] = \"iri\";\n  JSONSchemaFormat[\"IRIReference\"] = \"iri-reference\";\n  JSONSchemaFormat[\"JSONPointer\"] = \"json-pointer\";\n  JSONSchemaFormat[\"JSONPointerURIFragment\"] = \"json-pointer-uri-fragment\";\n  JSONSchemaFormat[\"RegEx\"] = \"regex\";\n  JSONSchemaFormat[\"RelativeJSONPointer\"] = \"relative-json-pointer\";\n  JSONSchemaFormat[\"Time\"] = \"time\";\n  JSONSchemaFormat[\"URI\"] = \"uri\";\n  JSONSchemaFormat[\"URIReference\"] = \"uri-reference\";\n  JSONSchemaFormat[\"URITemplate\"] = \"uri-template\";\n  JSONSchemaFormat[\"UUID\"] = \"uuid\";\n})(JSONSchemaFormat || (JSONSchemaFormat = {}));\n\nexport let JSONSchemaType;\n\n(function (JSONSchemaType) {\n  JSONSchemaType[\"Array\"] = \"array\";\n  JSONSchemaType[\"Boolean\"] = \"boolean\";\n  JSONSchemaType[\"Integer\"] = \"integer\";\n  JSONSchemaType[\"Null\"] = \"null\";\n  JSONSchemaType[\"Number\"] = \"number\";\n  JSONSchemaType[\"Object\"] = \"object\";\n  JSONSchemaType[\"String\"] = \"string\";\n})(JSONSchemaType || (JSONSchemaType = {}));\n\nexport let JSONSchemaContentEncoding;\n\n(function (JSONSchemaContentEncoding) {\n  JSONSchemaContentEncoding[\"7bit\"] = \"7bit\";\n  JSONSchemaContentEncoding[\"8bit\"] = \"8bit\";\n  JSONSchemaContentEncoding[\"Binary\"] = \"binary\";\n  JSONSchemaContentEncoding[\"QuotedPrintable\"] = \"quoted-printable\";\n  JSONSchemaContentEncoding[\"Base64\"] = \"base64\";\n  JSONSchemaContentEncoding[\"IETFToken\"] = \"ietf-token\";\n  JSONSchemaContentEncoding[\"XToken\"] = \"x-token\";\n})(JSONSchemaContentEncoding || (JSONSchemaContentEncoding = {}));\n\nexport const JSONSchemaKeys = ['$comment', '$id', '$ref', '$schema', 'additionalItems', 'additionalProperties', 'allOf', 'anyOf', 'const', 'contains', 'contentEncoding', 'contentMediaType', 'default', 'definitions', 'dependencies', 'description', 'else', 'enum', 'examples', 'exclusiveMaximum', 'exclusiveMinimum', 'format', 'if', 'items', 'maximum', 'maxItems', 'maxLength', 'maxProperties', 'minimum', 'minItems', 'minLength', 'minProperties', 'multipleOf', 'not', 'oneOf', 'pattern', 'patternProperties', 'properties', 'propertyNames', 'readOnly', 'required', 'then', 'title', 'type', 'uniqueItems', 'writeOnly'];"], "names": [], "mappings": "AAEU,IAAC,gBAAgB,CAAC;;AAE5B,CAAC,UAAU,gBAAgB,EAAE;EAC3B,gBAAgB,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC;EAClC,gBAAgB,CAAC,UAAU,CAAC,GAAG,WAAW,CAAC;EAC3C,gBAAgB,CAAC,OAAO,CAAC,GAAG,OAAO,CAAC;EACpC,gBAAgB,CAAC,UAAU,CAAC,GAAG,UAAU,CAAC;EAC1C,gBAAgB,CAAC,UAAU,CAAC,GAAG,WAAW,CAAC;EAC3C,gBAAgB,CAAC,aAAa,CAAC,GAAG,cAAc,CAAC;EACjD,gBAAgB,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC;EAClC,gBAAgB,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC;EAClC,gBAAgB,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC;EAChC,gBAAgB,CAAC,cAAc,CAAC,GAAG,eAAe,CAAC;EACnD,gBAAgB,CAAC,aAAa,CAAC,GAAG,cAAc,CAAC;EACjD,gBAAgB,CAAC,wBAAwB,CAAC,GAAG,2BAA2B,CAAC;EACzE,gBAAgB,CAAC,OAAO,CAAC,GAAG,OAAO,CAAC;EACpC,gBAAgB,CAAC,qBAAqB,CAAC,GAAG,uBAAuB,CAAC;EAClE,gBAAgB,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC;EAClC,gBAAgB,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC;EAChC,gBAAgB,CAAC,cAAc,CAAC,GAAG,eAAe,CAAC;EACnD,gBAAgB,CAAC,aAAa,CAAC,GAAG,cAAc,CAAC;EACjD,gBAAgB,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC;CACnC,EAAE,gBAAgB,KAAK,gBAAgB,GAAG,EAAE,CAAC,CAAC,CAAC;;AAEhD,AAAU,IAAC,cAAc,CAAC;;AAE1B,CAAC,UAAU,cAAc,EAAE;EACzB,cAAc,CAAC,OAAO,CAAC,GAAG,OAAO,CAAC;EAClC,cAAc,CAAC,SAAS,CAAC,GAAG,SAAS,CAAC;EACtC,cAAc,CAAC,SAAS,CAAC,GAAG,SAAS,CAAC;EACtC,cAAc,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC;EAChC,cAAc,CAAC,QAAQ,CAAC,GAAG,QAAQ,CAAC;EACpC,cAAc,CAAC,QAAQ,CAAC,GAAG,QAAQ,CAAC;EACpC,cAAc,CAAC,QAAQ,CAAC,GAAG,QAAQ,CAAC;CACrC,EAAE,cAAc,KAAK,cAAc,GAAG,EAAE,CAAC,CAAC,CAAC;;AAE5C,AAAU,IAAC,yBAAyB,CAAC;;AAErC,CAAC,UAAU,yBAAyB,EAAE;EACpC,yBAAyB,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC;EAC3C,yBAAyB,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC;EAC3C,yBAAyB,CAAC,QAAQ,CAAC,GAAG,QAAQ,CAAC;EAC/C,yBAAyB,CAAC,iBAAiB,CAAC,GAAG,kBAAkB,CAAC;EAClE,yBAAyB,CAAC,QAAQ,CAAC,GAAG,QAAQ,CAAC;EAC/C,yBAAyB,CAAC,WAAW,CAAC,GAAG,YAAY,CAAC;EACtD,yBAAyB,CAAC,QAAQ,CAAC,GAAG,SAAS,CAAC;CACjD,EAAE,yBAAyB,KAAK,yBAAyB,GAAG,EAAE,CAAC,CAAC,CAAC;;AAElE,AAAY,MAAC,cAAc,GAAG,CAAC,UAAU,EAAE,KAAK,EAAE,MAAM,EAAE,SAAS,EAAE,iBAAiB,EAAE,sBAAsB,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,UAAU,EAAE,iBAAiB,EAAE,kBAAkB,EAAE,SAAS,EAAE,aAAa,EAAE,cAAc,EAAE,aAAa,EAAE,MAAM,EAAE,MAAM,EAAE,UAAU,EAAE,kBAAkB,EAAE,kBAAkB,EAAE,QAAQ,EAAE,IAAI,EAAE,OAAO,EAAE,SAAS,EAAE,UAAU,EAAE,WAAW,EAAE,eAAe,EAAE,SAAS,EAAE,UAAU,EAAE,WAAW,EAAE,eAAe,EAAE,YAAY,EAAE,KAAK,EAAE,OAAO,EAAE,SAAS,EAAE,mBAAmB,EAAE,YAAY,EAAE,eAAe,EAAE,UAAU,EAAE,UAAU,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,aAAa,EAAE,WAAW,CAAC;;;;"}