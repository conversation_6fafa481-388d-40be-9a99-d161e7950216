package org.jeecg.modules.jianyingpro.service.internal;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.jeecg.modules.jianying.dto.CreateDraftRequest;
import org.springframework.stereotype.Service;

import java.util.UUID;

/**
 * 超级剪映小助手 - 草稿内容生成器
 * 复制自DraftContentGenerator，保持所有原有业务逻辑不变
 * 
 * <AUTHOR>
 * @since 2025-01-01
 */
@Service
public class JianyingProDraftContentGenerator {

    /**
     * 生成完整的草稿内容JSON
     * 
     * @param request 创建草稿请求
     * @param draftId 草稿ID (UUID格式)
     * @return 完整的草稿JSON对象
     */
    public JSONObject generateDraftContent(CreateDraftRequest request, String draftId) {
        // 使用LinkedHashMap保证字段顺序
        JSONObject draft = new JSONObject(new java.util.LinkedHashMap<>());
        
        // 基础配置
        draft.put("canvas_config", generateCanvasConfig(request));
        draft.put("color_space", -1);
        draft.put("config", generateConfig());
        draft.put("cover", null);
        draft.put("create_time", 0);
        draft.put("duration", 10000000); // 10秒默认时长
        draft.put("extra_info", null);
        draft.put("fps", 30);
        draft.put("free_render_index_mode_on", false);
        draft.put("group_container", null);
        draft.put("id", draftId);
        draft.put("is_drop_frame_timecode", false);
        draft.put("keyframe_graph_list", new JSONArray());
        
        // 关键帧系统
        draft.put("keyframes", generateKeyframes());
        
        // 歌词效果
        draft.put("lyrics_effects", new JSONArray());
        
        // 素材系统
        draft.put("materials", generateMaterials());
        
        // 可变配置
        draft.put("mutable_config", null);
        draft.put("name", "");
        draft.put("new_version", "110.0.0");
        draft.put("path", "");
        draft.put("relationships", new JSONArray());
        draft.put("render_index_track_mode_on", true);
        draft.put("retouch_cover", null);
        draft.put("source", "default");
        draft.put("static_cover_image_path", "");
        draft.put("time_marks", null);
        
        // 轨道系统
        draft.put("tracks", generateTracks());
        
        draft.put("update_time", 0);
        draft.put("version", 360000);
        
        // 平台信息
        draft.put("platform", generatePlatformInfo());
        draft.put("last_modified_platform", generateLastModifiedPlatformInfo());
        
        return draft;
    }

    /**
     * 生成画布配置
     */
    private JSONObject generateCanvasConfig(CreateDraftRequest request) {
        JSONObject canvasConfig = new JSONObject();
        
        // 使用请求中的尺寸，如果没有则使用默认值
        int width = request.getZjWidth() != null ? request.getZjWidth() : 1080;
        int height = request.getZjHeight() != null ? request.getZjHeight() : 1920;
        
        canvasConfig.put("height", height);
        canvasConfig.put("width", width);
        canvasConfig.put("ratio", "9:16"); // 默认竖屏比例
        
        return canvasConfig;
    }

    /**
     * 生成基础配置
     */
    private JSONObject generateConfig() {
        JSONObject config = new JSONObject();
        config.put("attribute", 0);
        config.put("is_landscape", false);
        config.put("is_square", false);
        config.put("motion_blur_config", null);
        config.put("preview_hdr", false);
        config.put("system_font_list", new JSONArray());
        return config;
    }

    /**
     * 生成关键帧配置
     */
    private JSONObject generateKeyframes() {
        JSONObject keyframes = new JSONObject();
        keyframes.put("canvas_config", new JSONArray());
        keyframes.put("color_space", new JSONArray());
        keyframes.put("config", new JSONArray());
        keyframes.put("cover", new JSONArray());
        keyframes.put("extra_info", new JSONArray());
        keyframes.put("fps", new JSONArray());
        keyframes.put("free_render_index_mode_on", new JSONArray());
        keyframes.put("group_container", new JSONArray());
        keyframes.put("keyframe_graph_list", new JSONArray());
        keyframes.put("lyrics_effects", new JSONArray());
        keyframes.put("materials", new JSONArray());
        keyframes.put("mutable_config", new JSONArray());
        keyframes.put("name", new JSONArray());
        keyframes.put("new_version", new JSONArray());
        keyframes.put("path", new JSONArray());
        keyframes.put("relationships", new JSONArray());
        keyframes.put("render_index_track_mode_on", new JSONArray());
        keyframes.put("retouch_cover", new JSONArray());
        keyframes.put("source", new JSONArray());
        keyframes.put("static_cover_image_path", new JSONArray());
        keyframes.put("time_marks", new JSONArray());
        keyframes.put("tracks", new JSONArray());
        keyframes.put("update_time", new JSONArray());
        keyframes.put("version", new JSONArray());
        return keyframes;
    }

    /**
     * 生成素材配置
     */
    private JSONObject generateMaterials() {
        JSONObject materials = new JSONObject();
        materials.put("audios", new JSONArray());
        materials.put("beats", new JSONArray());
        materials.put("canvases", new JSONArray());
        materials.put("chromas", new JSONArray());
        materials.put("color_curves", new JSONArray());
        materials.put("draft_localization", new JSONArray());
        materials.put("effects", new JSONArray());
        materials.put("flowers", new JSONArray());
        materials.put("handwrites", new JSONArray());
        materials.put("hsl", new JSONArray());
        materials.put("images", new JSONArray());
        materials.put("material_animations", new JSONArray());
        materials.put("placeholders", new JSONArray());
        materials.put("plugin_effects", new JSONArray());
        materials.put("shapes", new JSONArray());
        materials.put("sound_channel_mappings", new JSONArray());
        materials.put("speeds", new JSONArray());
        materials.put("stickers", new JSONArray());
        materials.put("texts", new JSONArray());
        materials.put("transitions", new JSONArray());
        materials.put("video_effects", new JSONArray());
        materials.put("video_trackings", new JSONArray());
        materials.put("videos", new JSONArray());
        return materials;
    }

    /**
     * 生成轨道配置
     */
    private JSONArray generateTracks() {
        JSONArray tracks = new JSONArray();
        
        // 主视频轨道
        JSONObject mainVideoTrack = new JSONObject();
        mainVideoTrack.put("attribute", 0);
        mainVideoTrack.put("flag", 0);
        mainVideoTrack.put("id", UUID.randomUUID().toString());
        mainVideoTrack.put("is_default_name", true);
        mainVideoTrack.put("name", "");
        mainVideoTrack.put("segments", new JSONArray());
        mainVideoTrack.put("type", "video");
        tracks.add(mainVideoTrack);
        
        // 主音频轨道
        JSONObject mainAudioTrack = new JSONObject();
        mainAudioTrack.put("attribute", 0);
        mainAudioTrack.put("flag", 0);
        mainAudioTrack.put("id", UUID.randomUUID().toString());
        mainAudioTrack.put("is_default_name", true);
        mainAudioTrack.put("name", "");
        mainAudioTrack.put("segments", new JSONArray());
        mainAudioTrack.put("type", "audio");
        tracks.add(mainAudioTrack);
        
        return tracks;
    }

    /**
     * 生成平台信息
     */
    private JSONObject generatePlatformInfo() {
        JSONObject platform = new JSONObject();
        platform.put("device_id", "");
        platform.put("hard_disk_id", "");
        platform.put("mac_address", "");
        platform.put("os", "windows");
        platform.put("platform", "pc");
        platform.put("soft_version", "5.9.0");
        return platform;
    }

    /**
     * 生成最后修改平台信息
     */
    private JSONObject generateLastModifiedPlatformInfo() {
        JSONObject lastModifiedPlatform = new JSONObject();
        lastModifiedPlatform.put("device_id", "");
        lastModifiedPlatform.put("hard_disk_id", "");
        lastModifiedPlatform.put("mac_address", "");
        lastModifiedPlatform.put("os", "windows");
        lastModifiedPlatform.put("platform", "pc");
        lastModifiedPlatform.put("soft_version", "5.9.0");
        return lastModifiedPlatform;
    }
}
