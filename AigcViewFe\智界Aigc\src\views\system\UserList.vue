<template>
  <a-card :bordered="false">

    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline" @keyup.enter.native="searchQuery">
        <a-row :gutter="24">

          <a-col :md="6" :sm="12">
            <a-form-item label="账号">
              <!--<a-input placeholder="请输入账号查询" v-model="queryParam.username"></a-input>-->
              <j-input placeholder="输入账号模糊查询" v-model="queryParam.username"></j-input>
            </a-form-item>
          </a-col>

          <a-col :md="6" :sm="8">
            <a-form-item label="性别">
              <a-select v-model="queryParam.sex" placeholder="请选择性别">
                <a-select-option value="">请选择</a-select-option>
                <a-select-option value="1">男</a-select-option>
                <a-select-option value="2">女</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>


          <template v-if="toggleSearchStatus">
            <a-col :md="6" :sm="8">
              <a-form-item label="真实名字">
                <a-input placeholder="请输入真实名字" v-model="queryParam.realname"></a-input>
              </a-form-item>
            </a-col>

            <a-col :md="6" :sm="8">
              <a-form-item label="手机号码">
                <a-input placeholder="请输入手机号码查询" v-model="queryParam.phone"></a-input>
              </a-form-item>
            </a-col>

            <a-col :md="6" :sm="8">
              <a-form-item label="用户状态">
                <a-select v-model="queryParam.status" placeholder="请选择">
                  <a-select-option value="">请选择</a-select-option>
                  <a-select-option value="1">正常</a-select-option>
                  <a-select-option value="2">冻结</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
          </template>

          <a-col :md="6" :sm="8">
            <span style="float: left;overflow: hidden;" class="table-page-search-submitButtons">
              <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
              <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
              <a @click="handleToggleSearch" style="margin-left: 8px">
                {{ toggleSearchStatus ? '收起' : '展开' }}
                <a-icon :type="toggleSearchStatus ? 'up' : 'down'"/>
              </a>
            </span>
          </a-col>

        </a-row>
      </a-form>
    </div>

    <!-- 操作按钮区域 -->
    <div class="table-operator" style="border-top: 5px">
      <a-button @click="handleAdd" type="primary" icon="plus" >添加用户</a-button>
      <a-button type="primary" icon="download" @click="handleExportXls('用户信息')">导出</a-button>
      <a-upload name="file" :showUploadList="false" :multiple="false" :headers="tokenHeader" :action="importExcelUrl" @change="handleImportExcel">
        <a-button type="primary" icon="import">导入</a-button>
      </a-upload>
      <j-third-app-button biz-type="user" :selected-row-keys="selectedRowKeys" syncToApp syncToLocal @sync-finally="onSyncFinally"/>
      <a-button type="primary" icon="hdd" @click="recycleBinVisible=true">回收站</a-button>
      <!-- 解除验证码限制按钮 - 只有admin可见 -->
      <a-button
        v-if="isAdminRole && selectedRowKeys.length > 0"
        type="primary"
        icon="unlock"
        @click="showResetVerifyCodeModal"
        style="margin-left: 8px">
        解除验证码限制
      </a-button>
      <a-dropdown v-if="selectedRowKeys.length > 0">
        <a-menu slot="overlay" @click="handleMenuClick">
          <a-menu-item key="1">
            <a-icon type="delete" @click="batchDel"/>
            删除
          </a-menu-item>
          <a-menu-item key="2">
            <a-icon type="lock" @click="batchFrozen('2')"/>
            冻结
          </a-menu-item>
          <a-menu-item key="3">
            <a-icon type="unlock" @click="batchFrozen('1')"/>
            解冻
          </a-menu-item>
        </a-menu>
        <a-button style="margin-left: 8px">
          批量操作
          <a-icon type="down"/>
        </a-button>
      </a-dropdown>
      <j-super-query :fieldList="superQueryFieldList" @handleSuperQuery="handleSuperQuery"/>
    </div>

    <!-- table区域-begin -->
    <div>
      <div class="ant-alert ant-alert-info" style="margin-bottom: 16px;">
        <i class="anticon anticon-info-circle ant-alert-icon"></i>已选择&nbsp;<a style="font-weight: 600">{{ selectedRowKeys.length }}</a>项&nbsp;&nbsp;
        <a style="margin-left: 24px" @click="onClearSelected">清空</a>
      </div>

      <a-table
        ref="table"
        bordered
        size="middle"
        rowKey="id"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="ipagination"
        :loading="loading"
        :rowSelection="{selectedRowKeys: selectedRowKeys, onChange: onSelectChange}"
        @change="handleTableChange">

        <template slot="avatarslot" slot-scope="text, record, index">
          <div class="anty-img-wrap">
            <a-avatar shape="square" :src="getAvatarView(record.avatar)" icon="user"/>
          </div>
        </template>

        <template slot="verifyCodeStatusSlot" slot-scope="text, record">
          <div v-if="isAdminRole" style="font-size: 11px; line-height: 1.3; text-align: center;">
            <!-- 用户验证码状态 -->
            <div style="display: flex; align-items: center; justify-content: center; margin-bottom: 1px;">
              <span :style="getStatusIconStyle(record.smsCount, 20)">●</span>
              <span style="margin-left: 3px;">短信: {{ record.smsCount || 0 }}/20</span>
            </div>
            <div style="display: flex; align-items: center; justify-content: center; margin-bottom: 2px;">
              <span :style="getStatusIconStyle(record.emailCount, 20)">●</span>
              <span style="margin-left: 3px;">邮箱: {{ record.emailCount || 0 }}/20</span>
            </div>
            <!-- 用户相关IP验证码状态 -->
            <div style="border-top: 1px solid #f0f0f0; padding-top: 2px; margin-top: 2px;">
              <div style="display: flex; align-items: center; justify-content: center; margin-bottom: 1px;">
                <span :style="getStatusIconStyle(record.ipSmsCount, 10)">▲</span>
                <span style="margin-left: 3px;">相关IP短信: {{ record.ipSmsCount || 0 }}/10</span>
              </div>
              <div style="display: flex; align-items: center; justify-content: center;">
                <span :style="getStatusIconStyle(record.ipEmailCount, 10)">▲</span>
                <span style="margin-left: 3px;">相关IP邮箱: {{ record.ipEmailCount || 0 }}/10</span>
              </div>
            </div>
          </div>
          <div v-else style="text-align: center;">-</div>
        </template>

        <span slot="action" slot-scope="text, record">
          <a @click="handleEdit(record)" >编辑</a>

          <a-divider type="vertical" />

          <a-dropdown>
            <a class="ant-dropdown-link">
              更多 <a-icon type="down"/>
            </a>
            <a-menu slot="overlay">
              <a-menu-item>
                <a href="javascript:;" @click="handleDetail(record)">详情</a>
              </a-menu-item>

              <a-menu-item>
                <a href="javascript:;" @click="handleChangePassword(record.username)">密码</a>
              </a-menu-item>

              <a-menu-item>
                <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
                  <a>删除</a>
                </a-popconfirm>
              </a-menu-item>

              <a-menu-item v-if="record.status==1">
                <a-popconfirm title="确定冻结吗?" @confirm="() => handleFrozen(record.id,2,record.username)">
                  <a>冻结</a>
                </a-popconfirm>
              </a-menu-item>

              <a-menu-item v-if="record.status==2">
                <a-popconfirm title="确定解冻吗?" @confirm="() => handleFrozen(record.id,1,record.username)">
                  <a>解冻</a>
                </a-popconfirm>
              </a-menu-item>

              <a-menu-item>
                <a href="javascript:;" @click="handleAgentSettings(record.username)">代理人</a>
              </a-menu-item>

            </a-menu>
          </a-dropdown>
        </span>


      </a-table>
    </div>
    <!-- table区域-end -->

    <user-modal ref="modalForm" @ok="modalFormOk"></user-modal>

    <password-modal ref="passwordmodal" @ok="passwordModalOk"></password-modal>

    <sys-user-agent-modal ref="sysUserAgentModal"></sys-user-agent-modal>

    <!-- 用户回收站 -->
    <user-recycle-bin-modal :visible.sync="recycleBinVisible" @ok="modalFormOk"/>

    <!-- 解除验证码限制对话框 -->
    <a-modal
      title="解除验证码限制"
      :visible="resetVerifyCodeVisible"
      @ok="handleResetVerifyCode"
      @cancel="resetVerifyCodeVisible = false"
      :confirmLoading="resetVerifyCodeLoading">
      <div>
        <p>已选择 <strong>{{ selectedRowKeys.length }}</strong> 个用户，请选择要解除的验证码限制类型：</p>
        <a-radio-group v-model="resetType" style="margin-top: 16px;">
          <a-radio value="sms">解除短信验证码限制</a-radio>
          <a-radio value="email">解除邮箱验证码限制</a-radio>
          <a-radio value="all">解除全部验证码限制</a-radio>
        </a-radio-group>
        <a-alert
          message="注意"
          :description="getResetDescription()"
          type="warning"
          style="margin-top: 16px;"
          show-icon />
      </div>
    </a-modal>

  </a-card>
</template>

<script>
  import UserModal from './modules/UserModal'
  import PasswordModal from './modules/PasswordModal'
  import {putAction,getFileAccessHttpUrl,postAction,getAction} from '@/api/manage';
  import {frozenBatch} from '@/api/api'
  import {isAdmin} from '@/utils/roleUtils'
  import {JeecgListMixin} from '@/mixins/JeecgListMixin'
  import SysUserAgentModal from "./modules/SysUserAgentModal";
  import JInput from '@/components/jeecg/JInput'
  import UserRecycleBinModal from './modules/UserRecycleBinModal'
  import JSuperQuery from '@/components/jeecg/JSuperQuery'
  import JThirdAppButton from '@/components/jeecgbiz/thirdApp/JThirdAppButton'

  export default {
    name: "UserList",
    mixins: [JeecgListMixin],
    components: {
      JThirdAppButton,
      SysUserAgentModal,
      UserModal,
      PasswordModal,
      JInput,
      UserRecycleBinModal,
      JSuperQuery
    },
    data() {
      return {
        description: '这是用户管理页面',
        queryParam: {},
        recycleBinVisible: false,
        // 解除验证码限制相关
        resetVerifyCodeVisible: false,
        resetVerifyCodeLoading: false,
        resetType: 'all',
        isAdminRole: false, // admin权限状态
        verifyCodeStatusMap: {}, // 存储用户验证码状态
        columns: [
          /*{
            title: '#',
            dataIndex: '',
            key:'rowIndex',
            width:60,
            align:"center",
            customRender:function (t,r,index) {
              return parseInt(index)+1;
            }
          },*/
          {
            title: '用户账号',
            align: "center",
            dataIndex: 'username',
            width: 120,
            sorter: true
          },
          {
            title: '用户姓名',
            align: "center",
            width: 100,
            dataIndex: 'realname',
          },
          {
            title: '头像',
            align: "center",
            width: 120,
            dataIndex: 'avatar',
            scopedSlots: {customRender: "avatarslot"}
          },

          {
            title: '性别',
            align: "center",
            width: 80,
            dataIndex: 'sex_dictText',
            sorter: true
          },
          {
            title: '生日',
            align: "center",
            width: 100,
            dataIndex: 'birthday'
          },
          {
            title: '手机号码',
            align: "center",
            width: 100,
            dataIndex: 'phone'
          },
          {
            title: '部门',
            align: "center",
            width: 180,
            dataIndex: 'orgCodeTxt'
          },
          {
            title: '负责部门',
            align: "center",
            width: 180,
            dataIndex: 'departIds_dictText'
          },
          {
            title: '状态',
            align: "center",
            width: 80,
            dataIndex: 'status_dictText'
          },
          {
            title: '是否是作者',
            align: "center",
            width: 100,
            dataIndex: 'isAuthor_dictText'
          },
          {
            title: '验证码状态',
            align: "center",
            width: 160,
            dataIndex: 'verifyCodeStatus',
            scopedSlots: {customRender: 'verifyCodeStatusSlot'}
          },
          {
            title: '操作',
            dataIndex: 'action',
            scopedSlots: {customRender: 'action'},
            align: "center",
            width: 170
          }

        ],
        superQueryFieldList: [
          { type: 'input', value: 'username', text: '用户账号', },
          { type: 'input', value: 'realname', text: '用户姓名', },
          { type: 'select', value: 'sex', dbType: 'int', text: '性别', dictCode: 'sex' },
        ],
        url: {
          syncUser: "/act/process/extActProcess/doSyncUser",
          list: "/sys/user/list",
          delete: "/sys/user/delete",
          deleteBatch: "/sys/user/deleteBatch",
          exportXlsUrl: "/sys/user/exportXls",
          importExcelUrl: "sys/user/importExcel",
        },
      }
    },
    computed: {
      importExcelUrl: function(){
        return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`;
      }
    },
    async created() {
      // 异步获取admin权限状态
      try {
        this.isAdminRole = await isAdmin();
        console.log('用户admin权限状态:', this.isAdminRole);
      } catch (error) {
        console.error('获取admin权限失败:', error);
        this.isAdminRole = false;
      }
    },
    watch: {
      // 监听dataSource变化，当数据加载完成后加载验证码状态
      dataSource: {
        handler(newVal, oldVal) {
          // 确保是真正的数据变化，而不是验证码状态的更新
          if (newVal && newVal.length > 0 && this.isAdminRole) {
            // 检查是否是新的数据加载
            // 1. 没有旧数据（首次加载）
            // 2. 数组长度变化（分页、筛选）
            // 3. 第一个元素ID变化（排序、筛选）
            // 4. 检查是否有验证码状态字段，如果没有说明是新加载的数据
            const isNewData = !oldVal ||
                             oldVal.length !== newVal.length ||
                             (oldVal.length > 0 && newVal.length > 0 && oldVal[0].id !== newVal[0].id) ||
                             (newVal.length > 0 && (newVal[0].smsCount === undefined || newVal[0].emailCount === undefined || newVal[0].ipSmsCount === undefined));

            if (isNewData) {
              console.log('检测到新的用户数据，加载验证码状态');
              // 延迟加载验证码状态，确保DOM更新完成
              setTimeout(() => {
                this.loadVerifyCodeStatus();
              }, 200);
            }
          }
        },
        immediate: false
      }
    },
    methods: {
      getAvatarView: function (avatar) {
        return getFileAccessHttpUrl(avatar)
      },

      batchFrozen: function (status) {
        if (this.selectedRowKeys.length <= 0) {
          this.$message.warning('请选择一条记录！');
          return false;
        } else {
          let ids = "";
          let that = this;
          let isAdmin = false;
          that.selectionRows.forEach(function (row) {
            if (row.username == 'admin') {
              isAdmin = true;
            }
          });
          if (isAdmin) {
            that.$message.warning('管理员账号不允许此操作,请重新选择！');
            return;
          }
          that.selectedRowKeys.forEach(function (val) {
            ids += val + ",";
          });
          that.$confirm({
            title: "确认操作",
            content: "是否" + (status == 1 ? "解冻" : "冻结") + "选中账号?",
            onOk: function () {
              frozenBatch({ids: ids, status: status}).then((res) => {
                if (res.success) {
                  that.$message.success(res.message);
                  that.loadData();
                  that.onClearSelected();
                } else {
                  that.$message.warning(res.message);
                }
              });
            }
          });
        }
      },
      handleMenuClick(e) {
        if (e.key == 1) {
          this.batchDel();
        } else if (e.key == 2) {
          this.batchFrozen(2);
        } else if (e.key == 3) {
          this.batchFrozen(1);
        }
      },
      handleFrozen: function (id, status, username) {
        let that = this;
        //TODO 后台校验管理员角色
        if ('admin' == username) {
          that.$message.warning('管理员账号不允许此操作！');
          return;
        }
        frozenBatch({ids: id, status: status}).then((res) => {
          if (res.success) {
            that.$message.success(res.message);
            that.loadData();
          } else {
            that.$message.warning(res.message);
          }
        });
      },
      handleChangePassword(username) {
        this.$refs.passwordmodal.show(username);
      },
      handleAgentSettings(username){
        this.$refs.sysUserAgentModal.agentSettings(username);
        this.$refs.sysUserAgentModal.title = "用户代理人设置";
      },
      passwordModalOk() {
        //TODO 密码修改完成 不需要刷新页面，可以把datasource中的数据更新一下
      },
      onSyncFinally({isToLocal}) {
        // 同步到本地时刷新下数据
        if (isToLocal) {
          this.loadData()
        }
      },
      // 显示解除验证码限制对话框
      showResetVerifyCodeModal() {
        if (this.selectedRowKeys.length <= 0) {
          this.$message.warning('请选择要操作的用户！');
          return;
        }
        this.resetType = 'all';
        this.resetVerifyCodeVisible = true;
      },
      // 执行解除验证码限制
      handleResetVerifyCode() {
        this.resetVerifyCodeLoading = true;
        const userIds = this.selectedRowKeys.join(',');
        const params = {
          userIds: userIds,
          resetType: this.resetType
        };

        postAction('/sys/user/resetVerifyCodeLimit', params).then((res) => {
          if (res.success) {
            this.$message.success(`成功解除 ${this.selectedRowKeys.length} 个用户的验证码限制`);
            this.resetVerifyCodeVisible = false;
            this.onClearSelected(); // 清空选择
            // 延迟刷新验证码状态
            setTimeout(() => {
              this.loadVerifyCodeStatus();
            }, 500);
          } else {
            this.$message.error(res.message || '操作失败');
          }
        }).catch((error) => {
          console.error('解除验证码限制失败:', error);
          this.$message.error('操作失败，请稍后重试');
        }).finally(() => {
          this.resetVerifyCodeLoading = false;
        });
      },
      // 获取状态指示器样式
      getStatusIconStyle(count, limit) {
        const percentage = (count / limit) * 100;
        let color = '#52c41a'; // 绿色 - 正常

        if (percentage >= 80) {
          color = '#ff4d4f'; // 红色 - 危险
        } else if (percentage >= 50) {
          color = '#faad14'; // 黄色 - 警告
        }

        return {
          color: color,
          fontSize: '12px'
        };
      },
      // 加载验证码状态数据
      async loadVerifyCodeStatus() {
        if (!this.isAdminRole) return;

        try {
          // 获取当前页面所有用户的ID
          const userIds = this.dataSource.map(user => user.id).join(',');
          if (!userIds) return;

          console.log('开始加载验证码状态，用户IDs:', userIds);
          const response = await getAction('/sys/user/getVerifyCodeStatus', { userIds });

          if (response.success && response.result) {
            this.verifyCodeStatusMap = response.result;
            console.log('验证码状态数据:', this.verifyCodeStatusMap);

            // 使用Vue.set确保响应式更新
            this.dataSource.forEach((user, index) => {
              const status = this.verifyCodeStatusMap[user.id];
              if (status) {
                this.$set(this.dataSource[index], 'smsCount', status.smsCount || 0);
                this.$set(this.dataSource[index], 'emailCount', status.emailCount || 0);
                this.$set(this.dataSource[index], 'ipSmsCount', status.ipSmsCount || 0);
                this.$set(this.dataSource[index], 'ipEmailCount', status.ipEmailCount || 0);
                this.$set(this.dataSource[index], 'ipAddress', status.ipAddress || '');
              } else {
                this.$set(this.dataSource[index], 'smsCount', 0);
                this.$set(this.dataSource[index], 'emailCount', 0);
                this.$set(this.dataSource[index], 'ipSmsCount', 0);
                this.$set(this.dataSource[index], 'ipEmailCount', 0);
                this.$set(this.dataSource[index], 'ipAddress', '');
              }
            });

            console.log('验证码状态已合并到用户数据');
            // 强制更新视图
            this.$forceUpdate();
          }
        } catch (error) {
          console.error('加载验证码状态失败:', error);
        }
      },
      // 获取重置类型的描述信息
      getResetDescription() {
        switch (this.resetType) {
          case 'sms':
            return '此操作将清除选中用户今日的短信验证码发送记录，同时解除当前IP地址的短信验证码限制，用户可以立即重新发送短信验证码。';
          case 'email':
            return '此操作将清除选中用户今日的邮箱验证码发送记录，同时解除当前IP地址的邮箱验证码限制，用户可以立即重新发送邮箱验证码。';
          case 'all':
            return '此操作将清除选中用户今日的所有验证码发送记录，同时解除当前IP地址的所有验证码限制，用户可以立即重新发送验证码。';
          default:
            return '此操作将清除选中用户今日的验证码发送记录，同时解除IP地址限制，用户可以立即重新发送验证码。';
        }
      },

    }

  }
</script>
<style scoped>
  @import '~@assets/less/common.less'
</style>