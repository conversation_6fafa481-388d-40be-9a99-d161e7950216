package org.jeecg.modules.jianying.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 字符串列表转对象请求参数
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class StrListToObjsRequest extends BaseJianyingRequest {
    
    @ApiModelProperty(value = "列表内容（必填）", required = true,
                     example = "[\"item1\", \"item2\", \"item3\"]")
    @NotEmpty(message = "infos不能为空")
    @JsonProperty("infos")
    private List<String> zjInfos;
    
    @Override
    public String getSummary() {
        return "StrListToObjsRequest{" +
               "accessKey=" + (getAccessKey() != null && getAccessKey().length() > 10 ? 
                              getAccessKey().substring(0, 10) + "***" : "null") +
               ", infosCount=" + (zjInfos != null ? zjInfos.size() : 0) +
               "}";
    }
}
