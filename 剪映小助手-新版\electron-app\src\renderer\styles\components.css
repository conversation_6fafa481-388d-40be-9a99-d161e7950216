/* 组件样式 */

/* 下载进度显示样式 */
.download-progress-section {
    margin-top: 24px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 12px;
    padding: 24px;
    color: #2d3748;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
}

.download-progress-section .section-subtitle {
    color: #2d3748;
    margin-bottom: 20px;
    font-size: 16px;
    font-weight: 600;
}

/* 当前文件下载进度 */
.current-download-progress {
    background: #f8fafc;
    border-radius: 10px;
    padding: 20px;
    border: 1px solid #e2e8f0;
}

.file-info {
    margin-bottom: 16px;
}

.file-name-row {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
}

.file-icon {
    font-size: 18px;
}

.file-name {
    font-weight: 600;
    font-size: 14px;
    flex: 1;
    word-break: break-all;
}

.file-type {
    background: rgba(255, 255, 255, 0.2);
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 11px;
    text-transform: uppercase;
}

.file-size-row {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 13px;
    opacity: 0.9;
}

.size-separator {
    opacity: 0.7;
}

/* 进度条样式 */
.progress-bar-container {
    margin: 16px 0;
}

.progress-bar {
    position: relative;
    background: #e2e8f0;
    border-radius: 20px;
    height: 24px;
    overflow: hidden;
    border: 1px solid #cbd5e0;
}

.progress-fill {
    background: linear-gradient(90deg, #10b981, #34d399);
    height: 100%;
    border-radius: 20px;
    transition: width 0.3s ease;
    position: relative;
    overflow: hidden;
}

.progress-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.3),
        transparent
    );
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.progress-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 12px;
    font-weight: 600;
    color: #1f2937;
    text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
    z-index: 1;
}

/* 下载统计信息 */
.download-stats {
    display: flex;
    justify-content: space-between;
    gap: 16px;
    flex-wrap: wrap;
}

.stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    flex: 1;
    min-width: 80px;
}

.stat-label {
    font-size: 11px;
    opacity: 0.8;
    margin-bottom: 4px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.stat-value {
    font-size: 13px;
    font-weight: 600;
    color: #059669;
}

/* 批量下载进度 */
.batch-progress {
    margin-top: 20px;
    background: #f1f5f9;
    border-radius: 10px;
    padding: 16px;
    border: 1px solid #cbd5e0;
}

.batch-info {
    display: flex;
    align-items: center;
    gap: 4px;
    margin-bottom: 12px;
    font-size: 14px;
}

.batch-title {
    font-weight: 600;
}

.batch-current {
    color: #fbbf24;
    font-weight: 700;
}

.batch-separator {
    opacity: 0.7;
}

.batch-total {
    font-weight: 600;
}

.batch-unit {
    opacity: 0.8;
}

.batch-progress-bar {
    height: 20px;
}

.batch-progress-bar .progress-fill {
    background: linear-gradient(90deg, #f59e0b, #fbbf24);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .download-stats {
        flex-direction: column;
        gap: 12px;
    }

    .stat-item {
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
    }

    .file-name-row {
        flex-wrap: wrap;
    }

    .file-type {
        order: -1;
        align-self: flex-start;
    }
}

/* 下载日志样式 */
.download-logs {
    margin-top: 24px;
    background: #f8fafc;
    border-radius: 12px;
    padding: 24px;
    border: 1px solid #e2e8f0;
    width: 100%;
    max-width: none;
}

.logs-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.clear-logs-btn {
    background: #ef4444;
    color: white;
    border: none;
    border-radius: 6px;
    padding: 6px 12px;
    font-size: 12px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.clear-logs-btn:hover {
    background: #dc2626;
}



.logs-content {
    background: white;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    padding: 16px;
    height: 300px;
    overflow-y: auto;
    font-family: 'Consolas', 'Monaco', monospace;
    font-size: 13px;
    line-height: 1.6;
    width: 100%;
}

.log-item {
    margin-bottom: 4px;
    display: flex;
    align-items: flex-start;
}

.log-time {
    color: #6b7280;
    margin-right: 8px;
    flex-shrink: 0;
}

.log-text {
    color: #374151;
}

.log-item.success .log-text {
    color: #059669;
}

.log-item.error .log-text {
    color: #dc2626;
}

.log-item.warning .log-text {
    color: #d97706;
}

.log-item.info .log-text {
    color: #2563eb;
}

.log-item.progress .log-text {
    color: #7c3aed;
    font-weight: 500;
}

/* 增强日志显示样式 */
.log-level {
    font-weight: bold;
    margin-right: 8px;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 11px;
    text-transform: uppercase;
}

.log-item.info .log-level {
    background-color: #dbeafe;
    color: #1e40af;
}

.log-item.warn .log-level {
    background-color: #fef3c7;
    color: #92400e;
}

.log-item.error .log-level {
    background-color: #fee2e2;
    color: #991b1b;
}

.log-url {
    color: #6b7280;
    font-style: italic;
    font-size: 11px;
    margin-left: 8px;
}

/* 处理摘要样式 */
.processing-summary {
    background-color: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 16px;
    margin: 16px 0;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.summary-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.summary-header h4 {
    margin: 0;
    color: #1f2937;
    font-size: 16px;
    font-weight: 600;
}

.status-badge {
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-badge.success {
    background-color: #dcfce7;
    color: #166534;
    border: 1px solid #bbf7d0;
}

.status-badge.warning {
    background-color: #fef3c7;
    color: #92400e;
    border: 1px solid #fde68a;
}

.status-badge.error {
    background-color: #fee2e2;
    color: #991b1b;
    border: 1px solid #fecaca;
}

.summary-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 12px;
}

.summary-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background-color: white;
    border-radius: 6px;
    border: 1px solid #e5e7eb;
}

.summary-item .label {
    color: #6b7280;
    font-size: 13px;
    font-weight: 500;
}

.summary-item .value {
    font-weight: 600;
    font-size: 14px;
    color: #1f2937;
}

.summary-item .value.success {
    color: #059669;
}

.summary-item .value.error {
    color: #dc2626;
}

/* 日志项增强样式 */
.log-item {
    padding: 6px 12px;
    margin: 2px 0;
    border-left: 3px solid transparent;
    border-radius: 4px;
    transition: background-color 0.2s ease;
}

.log-item:hover {
    background-color: #f9fafb;
}

.log-item.info {
    border-left-color: #3b82f6;
    background-color: #f0f9ff;
}

.log-item.warn {
    border-left-color: #f59e0b;
    background-color: #fffbeb;
}

.log-item.error {
    border-left-color: #ef4444;
    background-color: #fef2f2;
}

.log-item.success {
    border-left-color: #10b981;
    background-color: #f0fdf4;
}

/* 时间戳样式优化 */
.log-time {
    color: #9ca3af;
    font-family: 'Courier New', monospace;
    font-size: 11px;
    margin-right: 12px;
    flex-shrink: 0;
    min-width: 80px;
}

/* 日志文本样式优化 */
.log-text {
    color: #374151;
    line-height: 1.5;
    word-break: break-word;
}

/* 模态框样式 */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 1;
    transition: opacity 0.3s ease;
}

.modal-overlay.hidden {
    opacity: 0;
    pointer-events: none;
}

.modal {
    background: white;
    border-radius: 16px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow: hidden;
    transform: scale(1);
    transition: transform 0.3s ease;
}

.modal-overlay.hidden .modal {
    transform: scale(0.9);
}

.modal-header {
    padding: 20px 24px 16px;
    border-bottom: 1px solid #e2e8f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-title {
    font-size: 18px;
    font-weight: 600;
    color: #2d3748;
    margin: 0;
}

.modal-close {
    width: 32px;
    height: 32px;
    border: none;
    background: #f7fafc;
    border-radius: 8px;
    cursor: pointer;
    font-size: 18px;
    color: #718096;
    transition: all 0.2s ease;
}

.modal-close:hover {
    background: #edf2f7;
    color: #4a5568;
}

.modal-body {
    padding: 20px 24px;
    max-height: 60vh;
    overflow-y: auto;
}

.modal-footer {
    padding: 16px 24px 20px;
    border-top: 1px solid #e2e8f0;
}

.modal-actions {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
}

.modal-btn {
    padding: 10px 20px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.modal-btn.primary {
    background: #667eea;
    color: white;
}

.modal-btn.primary:hover {
    background: #5a67d8;
}

.modal-btn.secondary {
    background: #f7fafc;
    color: #4a5568;
    border: 1px solid #e2e8f0;
}

.modal-btn.secondary:hover {
    background: #edf2f7;
}

/* 通知样式 */
.notification-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 2000;
    pointer-events: none;
}

.notification {
    background: white;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    margin-bottom: 12px;
    padding: 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    min-width: 300px;
    max-width: 400px;
    transform: translateX(100%);
    opacity: 0;
    transition: all 0.3s ease;
    pointer-events: auto;
    border-left: 4px solid #e2e8f0;
}

.notification.show {
    transform: translateX(0);
    opacity: 1;
}

.notification.hide {
    transform: translateX(100%);
    opacity: 0;
}

.notification-success {
    border-left-color: #48bb78;
}

.notification-error {
    border-left-color: #f56565;
}

.notification-warning {
    border-left-color: #ed8936;
}

.notification-info {
    border-left-color: #4299e1;
}

.notification-content {
    display: flex;
    align-items: center;
    gap: 12px;
    flex: 1;
}

.notification-icon {
    font-size: 16px;
}

.notification-message {
    font-size: 14px;
    color: #2d3748;
    line-height: 1.4;
}

.notification-close {
    width: 24px;
    height: 24px;
    border: none;
    background: transparent;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
    color: #a0aec0;
    transition: all 0.2s ease;
    margin-left: 12px;
}

.notification-close:hover {
    background: #f7fafc;
    color: #718096;
}

/* 设置页面样式 */
.settings-section {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    padding: 30px;
    margin-bottom: 30px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.settings-title {
    font-size: 18px;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.setting-item {
    margin-bottom: 24px;
}

.setting-label {
    display: block;
    font-weight: 500;
    color: #4a5568;
    margin-bottom: 8px;
}

.setting-control {
    display: flex;
    gap: 12px;
    align-items: center;
}

.path-input {
    flex: 1;
    padding: 12px 16px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 14px;
    background: #f7fafc;
    color: #4a5568;
}

.browse-btn {
    padding: 12px 20px;
    background: #667eea;
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.browse-btn:hover {
    background: #5a67d8;
}

.setting-desc {
    font-size: 12px;
    color: #718096;
    margin-top: 8px;
    line-height: 1.4;
}

.setting-actions {
    display: flex;
    gap: 12px;
    margin-top: 20px;
}

.action-btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 10px 16px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.action-btn {
    background: #667eea;
    color: white;
}

.action-btn:hover {
    background: #5a67d8;
}

.action-btn.secondary {
    background: #f7fafc;
    color: #4a5568;
    border: 1px solid #e2e8f0;
}

.action-btn.secondary:hover {
    background: #edf2f7;
}

.info-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #e2e8f0;
}

.info-label {
    font-weight: 500;
    color: #4a5568;
}

.info-value {
    color: #2d3748;
}

.info-link {
    color: #667eea;
    text-decoration: none;
    transition: color 0.2s ease;
}

.info-link:hover {
    color: #5a67d8;
    text-decoration: underline;
}

/* 历史页面样式 */
.history-list {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    padding: 30px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #718096;
}

.empty-icon {
    font-size: 48px;
    margin-bottom: 16px;
}

.empty-state h3 {
    font-size: 18px;
    font-weight: 600;
    color: #4a5568;
    margin-bottom: 8px;
}

.empty-state p {
    font-size: 14px;
    line-height: 1.5;
}

.history-item {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 16px;
    border-radius: 12px;
    border: 1px solid #e2e8f0;
    margin-bottom: 12px;
    transition: all 0.2s ease;
}

.history-item:hover {
    background: #f7fafc;
    border-color: #cbd5e0;
}

.history-icon {
    font-size: 24px;
    width: 48px;
    height: 48px;
    background: #f7fafc;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.history-content {
    flex: 1;
}

.history-title {
    font-size: 16px;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 4px;
}

.history-time {
    font-size: 12px;
    color: #718096;
    margin-bottom: 4px;
}

.history-path {
    font-size: 12px;
    color: #a0aec0;
    font-family: monospace;
}

.history-actions {
    display: flex;
    gap: 8px;
}

.history-btn {
    padding: 8px 16px;
    background: #667eea;
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.history-btn:hover {
    background: #5a67d8;
}

/* 特殊内容样式 */
.modal-message {
    display: flex;
    align-items: flex-start;
    gap: 16px;
    padding: 20px 0;
}

.message-icon {
    font-size: 32px;
    flex-shrink: 0;
}

.message-text {
    flex: 1;
}

.message-text p {
    margin-bottom: 12px;
    line-height: 1.5;
    color: #4a5568;
}

.message-text p:last-child {
    margin-bottom: 0;
}

.setup-guide {
    padding: 20px 0;
}

.guide-step {
    display: flex;
    align-items: flex-start;
    gap: 16px;
    margin-bottom: 24px;
}

.guide-step:last-child {
    margin-bottom: 0;
}

.step-number {
    width: 32px;
    height: 32px;
    background: #667eea;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 14px;
    flex-shrink: 0;
}

.step-content h4 {
    font-size: 16px;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 4px;
}

.step-content p {
    font-size: 14px;
    color: #718096;
    line-height: 1.5;
}

/* 加载动画 */
.loading-content {
    text-align: center;
    padding: 40px 20px;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #e2e8f0;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-message {
    font-size: 16px;
    color: #4a5568;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .action-cards {
        grid-template-columns: 1fr;
    }
    
    .info-grid {
        grid-template-columns: 1fr;
    }
    
    .modal {
        width: 95%;
        margin: 20px;
    }
    
    .notification {
        min-width: 280px;
        max-width: 320px;
    }
}

/* 版本检查相关样式 */
.switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: 0.3s;
    border-radius: 24px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: 0.3s;
    border-radius: 50%;
}

input:checked + .slider {
    background-color: #4f46e5;
}

input:checked + .slider:before {
    transform: translateX(26px);
}

.select-input {
    padding: 8px 12px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    background: white;
    font-size: 14px;
    color: #374151;
    cursor: pointer;
    transition: border-color 0.2s;
}

.select-input:focus {
    outline: none;
    border-color: #4f46e5;
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.action-btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    background: #4f46e5;
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s;
    margin-right: 12px;
}

.action-btn:hover {
    background: #4338ca;
    transform: translateY(-1px);
}

.action-btn:active {
    transform: translateY(0);
}

.action-btn:disabled {
    background: #9ca3af;
    cursor: not-allowed;
    transform: none;
}

.btn-icon {
    font-size: 16px;
}

.btn-text {
    font-weight: 500;
}

.check-time {
    font-size: 12px;
    color: #6b7280;
    font-style: italic;
}

.setting-control {
    display: flex;
    align-items: center;
    gap: 12px;
}

/* 错误详情样式 */
.error-details {
    text-align: left;
}

.error-message {
    margin-bottom: 16px;
    padding: 12px;
    background: #fef2f2;
    border: 1px solid #fecaca;
    border-radius: 6px;
    color: #dc2626;
}

.error-suggestions {
    margin-bottom: 16px;
    padding: 12px;
    background: #f0f9ff;
    border: 1px solid #bae6fd;
    border-radius: 6px;
    color: #0369a1;
}

.error-suggestions ul {
    margin: 8px 0 0 0;
    padding-left: 20px;
}

.error-suggestions li {
    margin-bottom: 4px;
}

.error-technical {
    margin-top: 16px;
}

.error-technical details {
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    padding: 8px;
}

.error-technical summary {
    cursor: pointer;
    font-weight: 500;
    color: #6b7280;
    padding: 4px;
}

.error-technical summary:hover {
    color: #374151;
}

.error-technical pre {
    margin: 8px 0 0 0;
    padding: 8px;
    background: #f9fafb;
    border-radius: 4px;
    font-size: 12px;
    color: #6b7280;
    white-space: pre-wrap;
    word-break: break-word;
}

/* 版本更新模态弹窗样式 */
.update-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    backdrop-filter: blur(5px);
}

.update-modal.hidden {
    display: none;
}

.update-modal-content {
    background: white;
    border-radius: 12px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow: hidden;
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.update-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 24px;
    text-align: center;
}

.update-icon {
    font-size: 48px;
    margin-bottom: 12px;
}

.update-title {
    margin: 0;
    font-size: 24px;
    font-weight: 600;
}

.update-body {
    padding: 24px;
}

.version-info {
    display: flex;
    justify-content: space-between;
    margin-bottom: 24px;
    padding: 16px;
    background: #f8fafc;
    border-radius: 8px;
}

.current-version,
.latest-version {
    text-align: center;
}

.version-label {
    display: block;
    font-size: 14px;
    color: #64748b;
    margin-bottom: 4px;
}

.version-number {
    display: block;
    font-size: 18px;
    font-weight: 600;
    color: #1e293b;
}

.latest-version .version-number {
    color: #059669;
}

.update-content h4 {
    margin: 0 0 12px 0;
    color: #374151;
    font-size: 16px;
}

.content-text {
    background: #f9fafb;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    padding: 16px;
    max-height: 200px;
    overflow-y: auto;
    line-height: 1.6;
    color: #374151;
    white-space: pre-wrap;
}

.update-footer {
    padding: 24px;
    border-top: 1px solid #e5e7eb;
    background: #f8fafc;
}

.update-actions {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
}

.update-btn {
    padding: 12px 24px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    min-width: 100px;
}

.update-btn.primary {
    background: #059669;
    color: white;
}

.update-btn.primary:hover {
    background: #047857;
    transform: translateY(-1px);
}

.update-btn.secondary {
    background: #6b7280;
    color: white;
}

.update-btn.secondary:hover {
    background: #4b5563;
}

.update-btn.danger {
    background: #dc2626;
    color: white;
}

.update-btn.danger:hover {
    background: #b91c1c;
}

.update-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none !important;
}

/* 强制更新样式 */
.update-modal.force-update .update-header {
    background: linear-gradient(135deg, #dc2626 0%, #991b1b 100%);
}

.update-modal.force-update .update-icon {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
}

/* 🚀 更新弹窗内的下载进度样式 */
.update-download-progress {
    margin-top: 20px;
    padding: 20px;
    background: #f8fafc;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
}

.update-download-progress.hidden {
    display: none;
}

.update-download-progress h4 {
    margin: 0 0 15px 0;
    color: #1e40af;
    font-size: 16px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

.update-download-progress h4::before {
    content: "⬇️";
    font-size: 18px;
}

.progress-info {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-bottom: 15px;
}

.progress-info > div {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.progress-label {
    font-weight: 500;
    color: #64748b;
    font-size: 13px;
}

.progress-value {
    color: #1e293b;
    font-size: 13px;
    font-weight: 500;
}

.progress-bar-container {
    margin: 15px 0;
    display: flex;
    align-items: center;
    gap: 12px;
}

.progress-bar {
    flex: 1;
    height: 8px;
    background: #e2e8f0;
    border-radius: 4px;
    overflow: hidden;
    position: relative;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #3b82f6 0%, #1d4ed8 100%);
    border-radius: 4px;
    transition: width 0.3s ease;
    position: relative;
}

.progress-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent 0%, rgba(255,255,255,0.3) 50%, transparent 100%);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.progress-percent {
    font-size: 13px;
    font-weight: 600;
    color: #1e40af;
    min-width: 40px;
    text-align: right;
}

.progress-details {
    display: flex;
    justify-content: space-between;
    gap: 20px;
    margin-top: 10px;
}

.progress-details > div {
    display: flex;
    align-items: center;
    gap: 6px;
}

.progress-speed .progress-value {
    color: #059669;
    font-weight: 600;
}

.progress-size .progress-value {
    color: #7c3aed;
    font-weight: 600;
}

/* 设置开关样式 */
.setting-toggle {
    margin: 12px 0;
    display: flex;
    align-items: center;
    justify-content: center;
}

.toggle-label {
    display: flex;
    align-items: center;
    gap: 12px;
    cursor: pointer;
    user-select: none;
    font-size: 14px;
    color: #555;
    font-weight: 500;
}

.toggle-checkbox {
    display: none;
}

.toggle-slider {
    position: relative;
    width: 50px;
    height: 26px;
    background: #ccc;
    border-radius: 26px;
    transition: all 0.3s ease;
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

.toggle-slider::before {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    width: 22px;
    height: 22px;
    background: white;
    border-radius: 50%;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.toggle-checkbox:checked + .toggle-slider {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.toggle-checkbox:checked + .toggle-slider::before {
    transform: translateX(24px);
}

.toggle-label:hover .toggle-slider {
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1), 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.toggle-text {
    font-size: 14px;
    color: #333;
    font-weight: 500;
}

/* 应用信息卡片样式 */
.app-info-card {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    padding: 20px;
    margin-top: 16px;
}

.app-info-header {
    display: flex;
    align-items: center;
    gap: 16px;
    margin-bottom: 20px;
    padding-bottom: 16px;
    border-bottom: 1px solid #e2e8f0;
}

.app-icon-wrapper {
    flex-shrink: 0;
}

.app-icon {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.app-basic-info {
    flex: 1;
}

.app-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: #1f2937;
    margin: 0 0 8px 0;
}

.app-version-badge {
    background: #3b82f6;
    color: white;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
    display: inline-block;
}

.app-info-details {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.detail-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
}

.detail-label {
    font-size: 0.9rem;
    color: #6b7280;
    font-weight: 500;
}

.detail-value {
    font-size: 0.9rem;
    color: #374151;
    font-weight: 500;
}

.detail-link {
    color: #3b82f6;
    text-decoration: none;
    font-size: 0.9rem;
    font-weight: 500;
    transition: color 0.2s;
}

.detail-link:hover {
    color: #2563eb;
    text-decoration: underline;
}

/* 深色主题下的应用信息卡片 */
@media (prefers-color-scheme: dark) {
    .app-info-card {
        background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
        border-color: #374151;
    }

    .app-info-header {
        border-bottom-color: #374151;
    }

    .app-title {
        color: #f9fafb;
    }

    .detail-label {
        color: #9ca3af;
    }

    .detail-value {
        color: #f3f4f6;
    }

    .detail-link {
        color: #60a5fa;
    }

    .detail-link:hover {
        color: #93c5fd;
    }
}

/* 页面标题样式 */
.page-header {
    margin-bottom: 24px;
}

.page-subtitle {
    color: #ffffff;
    font-size: 0.9rem;
    margin: 8px 0 0 0;
    font-style: italic;
}

@media (prefers-color-scheme: dark) {
    .page-subtitle {
        color: #ffffff;
    }
}

/* 关于弹窗样式 */
.about-modal-content {
    max-width: 450px;
    width: 90%;
}

.about-info-section {
    text-align: center;
    padding: 16px 0;
}

.app-logo {
    margin-bottom: 16px;
}

.app-logo img {
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.app-name {
    font-size: 1.4rem;
    font-weight: 600;
    color: #1f2937;
    margin: 0 0 12px 0;
}

.about-info-section .version-info {
    margin-bottom: 16px;
}

.about-info-section .version-info {
    display: flex;
    justify-content: center;
    margin-bottom: 16px;
}

.about-info-section .current-version {
    background: #f3f4f6;
    padding: 6px 12px;
    border-radius: 6px;
    display: inline-flex;
    align-items: center;
    gap: 6px;
    font-size: 0.9rem;
}

.app-description {
    margin-bottom: 16px;
    color: #6b7280;
    line-height: 1.5;
    font-size: 0.9rem;
}

.developer-info {
    color: #6b7280;
    font-size: 0.85rem;
    line-height: 1.6;
}

.developer-info p {
    margin: 2px 0;
}

.website-link {
    color: #3b82f6;
    text-decoration: none;
    transition: color 0.2s;
}

.website-link:hover {
    color: #2563eb;
    text-decoration: underline;
}

/* 深色主题下的关于弹窗 */
@media (prefers-color-scheme: dark) {
    .app-name {
        color: #f9fafb;
    }

    .about-info-section .current-version {
        background: #374151;
        color: #f9fafb;
    }

    .app-description,
    .developer-info {
        color: #d1d5db;
    }

    .website-link {
        color: #60a5fa;
    }

    .website-link:hover {
        color: #93c5fd;
    }
}
